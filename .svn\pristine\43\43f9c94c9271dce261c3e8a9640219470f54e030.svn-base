﻿using AutoFixture;
using ContinuityPatrol.Application.Features.UserGroup.Commands.Create;
using ContinuityPatrol.Application.Features.UserGroup.Commands.Update;
using ContinuityPatrol.Application.Features.UserGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.UserGroupModel;
using ContinuityPatrol.Domain.ViewModels.UserModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class UserGroupControllerShould
    {
        private readonly Mock<ILogger<UserGroupController>> _mockLogger =new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private UserGroupController _controller;

        public UserGroupControllerShould()
        {
            
            _controller = new UserGroupController(
                _mockMapper.Object,
                _mockLogger.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public void List_ReturnsViewResult()
        {
            // Act
            var result = _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public async Task CreateOrUpdate_CreateUserGroup_ReturnsRedirectToActionResult()
        {
            // Arrange
            var userGroupModel = new AutoFixture.Fixture().Create<UserGrouplistModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty id for create scenario
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateUserGroupCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            // Setup UserGroupService mock
            var mockUserGroupService = new Mock<IUserGroupService>();
            mockUserGroupService.Setup(s => s.CreateAsync(It.IsAny<CreateUserGroupCommand>()))
                .ReturnsAsync(response);
            _mockDataProvider.Setup(dp => dp.UserGroup).Returns(mockUserGroupService.Object);

            _mockMapper.Setup(m => m.Map<CreateUserGroupCommand>(It.IsAny<UserGrouplistModel>()))
                .Returns(command);

            // Act
            var result = await _controller.CreateOrUpdate(userGroupModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            mockUserGroupService.Verify(s => s.CreateAsync(It.IsAny<CreateUserGroupCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdateUserGroup_ReturnsRedirectToActionResult()
        {
            // Arrange
            var userGroupModel = new AutoFixture.Fixture().Create<UserGrouplistModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22"); // Non-empty id for update scenario
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateUserGroupCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            // Setup UserGroupService mock
            var mockUserGroupService = new Mock<IUserGroupService>();
            mockUserGroupService.Setup(s => s.UpdateAsync(It.IsAny<UpdateUserGroupCommand>()))
                .ReturnsAsync(response);
            _mockDataProvider.Setup(dp => dp.UserGroup).Returns(mockUserGroupService.Object);

            _mockMapper.Setup(m => m.Map<UpdateUserGroupCommand>(It.IsAny<UserGrouplistModel>()))
                .Returns(command);

            // Act
            var result = await _controller.CreateOrUpdate(userGroupModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            mockUserGroupService.Verify(s => s.UpdateAsync(It.IsAny<UpdateUserGroupCommand>()), Times.Once);
        }

        [Fact]
        public async Task GetUserNames_ReturnsJsonResult()
        {
            // Arrange
            var userDetails = new List<UserNameVm>();
            var mockUserService = new Mock<IUserService>();
            mockUserService.Setup(s => s.GetUserNames())
                .ReturnsAsync(userDetails);
            _mockDataProvider.Setup(dp => dp.User).Returns(mockUserService.Object);

            // Act
            var result = await _controller.GetUserNames() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            mockUserService.Verify(s => s.GetUserNames(), Times.Once);
        }

        [Fact]
        public async Task IsGroupNameExist_ReturnsBoolean()
        {
            // Arrange
            var groupName = "Group1";
            var id = "123";
            var mockUserGroupService = new Mock<IUserGroupService>();
            mockUserGroupService.Setup(s => s.IsGroupNameExist(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(true);
            _mockDataProvider.Setup(dp => dp.UserGroup).Returns(mockUserGroupService.Object);

            // Act
            var result = await _controller.IsGroupNameExist(groupName, id);

            // Assert
            Assert.True(result);
            mockUserGroupService.Verify(s => s.IsGroupNameExist(groupName, id), Times.Once);
        }

        [Fact]
        public async Task Delete_ReturnsRedirectToActionResult()
        {
            // Arrange
            var id = "123";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            var mockUserGroupService = new Mock<IUserGroupService>();
            mockUserGroupService.Setup(s => s.DeleteAsync(It.IsAny<string>()))
                .ReturnsAsync(response);
            _mockDataProvider.Setup(dp => dp.UserGroup).Returns(mockUserGroupService.Object);

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            mockUserGroupService.Verify(s => s.DeleteAsync(id), Times.Once);
        }

        [Fact]
        public async Task GetPaginationList_ReturnsJsonResult()
        {
            // Arrange
            var query = new GetUserGroupPaginatedListQuery();
            var paginatedList = new PaginatedResult<UserGroupListVm>();
            var mockUserGroupService = new Mock<IUserGroupService>();
            mockUserGroupService.Setup(s => s.GetPaginatedUserGroups(It.IsAny<GetUserGroupPaginatedListQuery>()))
                .ReturnsAsync(paginatedList);
            _mockDataProvider.Setup(dp => dp.UserGroup).Returns(mockUserGroupService.Object);

            // Act
            var result = await _controller.GetPaginationList(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(paginatedList, jsonResult.Value);
            mockUserGroupService.Verify(s => s.GetPaginatedUserGroups(query), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidationException_ReturnsRedirectToList()
        {
            // Arrange
            var userGroupModel = new AutoFixture.Fixture().Create<UserGrouplistModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateUserGroupCommand();

            var mockUserGroupService = new Mock<IUserGroupService>();
            mockUserGroupService.Setup(s => s.CreateAsync(It.IsAny<CreateUserGroupCommand>()))
                .ThrowsAsync(new ValidationException(new FluentValidation.Results.ValidationResult()));
            _mockDataProvider.Setup(dp => dp.UserGroup).Returns(mockUserGroupService.Object);

            _mockMapper.Setup(m => m.Map<CreateUserGroupCommand>(It.IsAny<UserGrouplistModel>()))
                .Returns(command);

            // Act
            var result = await _controller.CreateOrUpdate(userGroupModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_WithGeneralException_ReturnsRedirectToList()
        {
            // Arrange
            var userGroupModel = new AutoFixture.Fixture().Create<UserGrouplistModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateUserGroupCommand();

            var mockUserGroupService = new Mock<IUserGroupService>();
            mockUserGroupService.Setup(s => s.UpdateAsync(It.IsAny<UpdateUserGroupCommand>()))
                .ThrowsAsync(new Exception("Database error"));
            _mockDataProvider.Setup(dp => dp.UserGroup).Returns(mockUserGroupService.Object);

            _mockMapper.Setup(m => m.Map<UpdateUserGroupCommand>(It.IsAny<UserGrouplistModel>()))
                .Returns(command);

            // Act
            var result = await _controller.CreateOrUpdate(userGroupModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task GetUserNames_WithException_ReturnsJsonError()
        {
            // Arrange
            var mockUserService = new Mock<IUserService>();
            mockUserService.Setup(s => s.GetUserNames())
                .ThrowsAsync(new Exception("Database connection failed"));
            _mockDataProvider.Setup(dp => dp.User).Returns(mockUserService.Object);

            // Act
            var result = await _controller.GetUserNames() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            var response = jsonResult.Value as BaseResponse;
            Assert.NotNull(response);
            Assert.False(response.Success);
        }

        [Fact]
        public async Task IsGroupNameExist_WithException_ReturnsFalse()
        {
            // Arrange
            var groupName = "Group1";
            var id = "123";
            var mockUserGroupService = new Mock<IUserGroupService>();
            mockUserGroupService.Setup(s => s.IsGroupNameExist(It.IsAny<string>(), It.IsAny<string>()))
                .ThrowsAsync(new Exception("Service unavailable"));
            _mockDataProvider.Setup(dp => dp.UserGroup).Returns(mockUserGroupService.Object);

            // Act
            var result = await _controller.IsGroupNameExist(groupName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task Delete_WithException_ReturnsRedirectToList()
        {
            // Arrange
            var id = "123";
            var mockUserGroupService = new Mock<IUserGroupService>();
            mockUserGroupService.Setup(s => s.DeleteAsync(It.IsAny<string>()))
                .ThrowsAsync(new Exception("Delete operation failed"));
            _mockDataProvider.Setup(dp => dp.UserGroup).Returns(mockUserGroupService.Object);

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task GetPaginationList_WithException_ReturnsJsonError()
        {
            // Arrange
            var query = new GetUserGroupPaginatedListQuery();
            var mockUserGroupService = new Mock<IUserGroupService>();
            mockUserGroupService.Setup(s => s.GetPaginatedUserGroups(It.IsAny<GetUserGroupPaginatedListQuery>()))
                .ThrowsAsync(new Exception("Query execution failed"));
            _mockDataProvider.Setup(dp => dp.UserGroup).Returns(mockUserGroupService.Object);

            // Act
            var result = await _controller.GetPaginationList(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            var response = jsonResult.Value as BaseResponse;
            Assert.NotNull(response);
            Assert.False(response.Success);
        }

        [Fact]
        public async Task CreateOrUpdate_WithNullId_CallsCreatePath()
        {
            // Arrange
            var userGroupModel = new AutoFixture.Fixture().Create<UserGrouplistModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", (string)null); // Null id
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateUserGroupCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            var mockUserGroupService = new Mock<IUserGroupService>();
            mockUserGroupService.Setup(s => s.CreateAsync(It.IsAny<CreateUserGroupCommand>()))
                .ReturnsAsync(response);
            _mockDataProvider.Setup(dp => dp.UserGroup).Returns(mockUserGroupService.Object);

            _mockMapper.Setup(m => m.Map<CreateUserGroupCommand>(It.IsAny<UserGrouplistModel>()))
                .Returns(command);

            // Act
            var result = await _controller.CreateOrUpdate(userGroupModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            mockUserGroupService.Verify(s => s.CreateAsync(It.IsAny<CreateUserGroupCommand>()), Times.Once);
            mockUserGroupService.Verify(s => s.UpdateAsync(It.IsAny<UpdateUserGroupCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithWhitespaceId_CallsCreatePath()
        {
            // Arrange
            var userGroupModel = new AutoFixture.Fixture().Create<UserGrouplistModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "   "); // Whitespace id
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateUserGroupCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            var mockUserGroupService = new Mock<IUserGroupService>();
            mockUserGroupService.Setup(s => s.CreateAsync(It.IsAny<CreateUserGroupCommand>()))
                .ReturnsAsync(response);
            _mockDataProvider.Setup(dp => dp.UserGroup).Returns(mockUserGroupService.Object);

            _mockMapper.Setup(m => m.Map<CreateUserGroupCommand>(It.IsAny<UserGrouplistModel>()))
                .Returns(command);

            // Act
            var result = await _controller.CreateOrUpdate(userGroupModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            mockUserGroupService.Verify(s => s.CreateAsync(It.IsAny<CreateUserGroupCommand>()), Times.Once);
            mockUserGroupService.Verify(s => s.UpdateAsync(It.IsAny<UpdateUserGroupCommand>()), Times.Never);
        }
    }
}
