﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class WorkflowOperationGroupFilterSpecification : Specification<WorkflowOperationGroup>
{
    public WorkflowOperationGroupFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                {
                    if (stringItem.Contains("infraobjectname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.InfraObjectName.Contains(stringItem.Replace("infraobjectname=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    if (stringItem.Contains("workflowname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.WorkflowName.Contains(stringItem.Replace("workflowname=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    if (stringItem.Contains("currentactionname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.CurrentActionName.Contains(stringItem.Replace("currentactionname=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    if (stringItem.Contains("status=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Status.Contains(stringItem.Replace("status=", "",
                            StringComparison.OrdinalIgnoreCase)));
                }
            }
            else
            {
                Criteria = p =>
                    p.CurrentActionName.Contains(searchString) || p.InfraObjectName.Contains(searchString) ||
                    p.WorkflowName.Contains(searchString) || p.Status.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.CurrentActionName != null;
        }
    }
}