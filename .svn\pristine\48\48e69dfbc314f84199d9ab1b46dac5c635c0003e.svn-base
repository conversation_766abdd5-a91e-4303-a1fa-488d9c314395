﻿namespace ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetActionId;

public class
    GetWorkflowPredictionListByActionIdQueryHandler : IRequestHandler<GetWorkflowPredictionListByActionIdQuery,
        WorkflowPredictionResult>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowPredictionRepository _workflowPredictionRepository;

    public GetWorkflowPredictionListByActionIdQueryHandler(IMapper mapper,
        IWorkflowPredictionRepository workflowPredictionRepository)
    {
        _mapper = mapper;
        _workflowPredictionRepository = workflowPredictionRepository;
    }

    public async Task<WorkflowPredictionResult> Handle(GetWorkflowPredictionListByActionIdQuery request,
        CancellationToken cancellationToken)
    {
        var workflowPrediction = new List<Domain.Entities.WorkflowPrediction>();

        var workflowPredictions = await _workflowPredictionRepository.GetWorkflowPredictionByActionId(
            request.PreviousActionId.IsNullOrWhiteSpace() ? request.ActionId : request.PreviousActionId);

        var matches = !request.PreviousActionId.IsNullOrWhiteSpace() &&
                      workflowPredictions.Any(x =>
                          x.NextPossibleId.IsNotNullOrWhiteSpace() && x.NextPossibleId.Equals(request.ActionId));

        var workflowPredictionsList = workflowPredictions
            .OrderByDescending(x => x.Count)
            .ThenByDescending(x => x.LastModifiedDate)
            .Take(5)
            .ToList();

        //if(misMatched) == false


        if (request.PreviousActionId.IsNotNullOrWhiteSpace())
        {
            if (!matches)
            {
                var workflowPredictionList =
                    await _workflowPredictionRepository.GetWorkflowPredictionByActionId(request.PreviousActionId);

                workflowPrediction = workflowPredictionList
                    .OrderByDescending(x => x.Count)
                    .ThenByDescending(x => x.LastModifiedDate)
                    .Take(5)
                    .ToList();

                var workflowPredictionDto1 = _mapper.Map<List<WorkflowPredictionListByActionIdVm>>(workflowPrediction);


                return new WorkflowPredictionResult
                {
                    Status = false,
                    MatchedActions = workflowPredictionDto1
                };
            }
            else
            {
                var workflowPredictions1 =
                    await _workflowPredictionRepository.GetWorkflowPredictionByActionId(request.ActionId);


                var workflowPredictionsList1 = workflowPredictions1
                    .OrderByDescending(x => x.Count)
                    .ThenByDescending(x => x.LastModifiedDate)
                    .Take(5)
                    .ToList();

                var workflowPredictionDto1 =
                    _mapper.Map<List<WorkflowPredictionListByActionIdVm>>(workflowPredictionsList1);


                return new WorkflowPredictionResult
                {
                    Status = true,
                    WorkflowPredictionListByActionIdVms = workflowPredictionDto1
                };
            }
        }

        var workflowPredictionDto = _mapper.Map<List<WorkflowPredictionListByActionIdVm>>(workflowPredictionsList);

        return new WorkflowPredictionResult
        {
            Status = true,
            WorkflowPredictionListByActionIdVms = workflowPredictionDto
        };
    }
}