using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class CyberAirGapStatusProfile : Profile
{
    public CyberAirGapStatusProfile()
    {
        CreateMap<CyberAirGapStatus, CyberAirGapStatusListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<CyberAirGapStatus, CyberAirGapStatusDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<CyberAirGapStatus, CreateCyberAirGapStatusCommand>().ReverseMap();
        CreateMap<CyberAirGapStatus, CyberAirGapStatusViewModel>().ReverseMap();

        CreateMap<CreateCyberAirGapStatusCommand, CyberAirGapStatusViewModel>().ReverseMap();
        CreateMap<UpdateCyberAirGapStatusCommand, CyberAirGapStatusViewModel>().ReverseMap();

        CreateMap<UpdateCyberAirGapStatusCommand, CyberAirGapStatus>().ForMember(x => x.Id, y => y.Ignore()); 

        CreateMap<PaginatedResult<CyberAirGapStatus>, PaginatedResult<CyberAirGapStatusListVm>>().ForMember(x => x.Data, y => y.MapFrom(src=>src.Data));
    }
}