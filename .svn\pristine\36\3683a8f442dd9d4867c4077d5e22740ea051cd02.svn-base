namespace ContinuityPatrol.Domain.Entities;

public class DrReady : AuditableEntity
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public int TotalBusinessFunction { get; set; }
    public int BFAvailable { get; set; }
    public int BFImpact { get; set; }
    public int BFDRReady { get; set; }
    public int BFDRNotReady { get; set; }
    public int TotalInfraObject { get; set; }
    public int InfraAvailable { get; set; }
    public int InfraImpact { get; set; }
    public int InfraDRReady { get; set; }
    public int InfraDRNotReady { get; set; }
}