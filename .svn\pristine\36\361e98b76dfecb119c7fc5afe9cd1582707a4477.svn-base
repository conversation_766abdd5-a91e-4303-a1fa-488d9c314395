﻿using ContinuityPatrol.Application.Features.BulkImport.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Next;
using ContinuityPatrol.Application.Features.BulkImport.Commands.RollBack;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Services.Provider;

namespace ContinuityPatrol.Web.API;

[Route("api/[controller]")]
[ApiController]
public class BulkImportController : ControllerBase
{
    private readonly IDataProvider _dataProvider;
    
    public BulkImportController(IDataProvider dataProvider)
    {
        _dataProvider = dataProvider;
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<IActionResult> CreateBulkImport([FromBody] string bulkImportGroupId)
    {
        var bulkImport = new CreateBulkImportCommand
        {
            Id = bulkImportGroupId
        };

        return CreatedAtAction(nameof(CreateBulkImport), await _dataProvider.BulkImport.CreateBulkImport(bulkImport));
    }

    [HttpPut,Route("Next")]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<IActionResult> BulkImportNext([FromBody] string bulkImportGroupId)
    {
        var bulkImport = new NextBulkImportCommand
        {
            GroupId = bulkImportGroupId
        };

        return Ok(await _dataProvider.BulkImport.NextBulkImportAction(bulkImport));
    }

    [HttpPut,Route("roleBack")]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<IActionResult> BulkImportRolBack([FromBody] string bulkImportGroupId)
    {
        var bulkImport = new RollBackBulkImportCommand
        {
            GroupId = bulkImportGroupId
        };

        return Ok(await _dataProvider.BulkImport.RollBackBulkImportAction(bulkImport));
    }
}