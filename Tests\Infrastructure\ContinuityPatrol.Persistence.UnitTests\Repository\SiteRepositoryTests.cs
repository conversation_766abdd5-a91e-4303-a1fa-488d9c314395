using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SiteRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly SiteRepository _repository;
    private readonly SiteFixture _fixture;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public SiteRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        
        _repository = new SiteRepository(_dbContext, _mockLoggedInUserService.Object);
        _fixture = new SiteFixture();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnSitesForCompany()
    {
        // Arrange
        await ClearDatabase();
        await SetupRelatedEntities();

        var site1 = _fixture.CreateSite(name: "Site1", companyId: "COMPANY_123", typeId: "TYPE_001", locationId: "LOC_001");
        var site2 = _fixture.CreateSite(name: "Site2", companyId: "COMPANY_123", typeId: "TYPE_002", locationId: "LOC_002");
        var site3 = _fixture.CreateSite(name: "Site3", companyId: "COMPANY_456", typeId: "TYPE_001", locationId: "LOC_001");

        await _repository.AddAsync(site1);
        await _repository.AddAsync(site2);
        await _repository.AddAsync(site3);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.Name == "Site1");
        Assert.Contains(result, s => s.Name == "Site2");
        Assert.DoesNotContain(result, s => s.Name == "Site3");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnMappedProperties()
    {
        // Arrange
        await ClearDatabase();
        await SetupRelatedEntities();

        var site = _fixture.CreateSite(
            name: "TestSite",
            companyId: "COMPANY_123",
            typeId: "TYPE_001",
            locationId: "LOC_001",
            platformType: "Windows",
            dataTemperature: "Hot"
        );

        await _repository.AddAsync(site);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var siteResult = result.First();
        Assert.Equal("TestSite", siteResult.Name);
        Assert.Equal("COMPANY_123", siteResult.CompanyId);
        Assert.Equal("Test Company", siteResult.CompanyName);
        Assert.Equal("TYPE_001", siteResult.TypeId);
        Assert.Equal("Primary", siteResult.Type);
        Assert.Equal("New York", siteResult.Location);
        Assert.Equal("Windows", siteResult.PlatformType);
        Assert.Equal("Hot", siteResult.DataTemperature);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnSite_WhenIdExistsForCompany()
    {
        // Arrange
        await ClearDatabase();
        await SetupRelatedEntities();

        var site = _fixture.CreateSite(name: "TestSite", companyId: "COMPANY_123", typeId: "TYPE_001", locationId: "LOC_001");
        await _repository.AddAsync(site);

        // Act
        var result = await _repository.GetByReferenceIdAsync(site.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("TestSite", result.Name);
        Assert.Equal("COMPANY_123", result.CompanyId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIdExistsButDifferentCompany()
    {
        // Arrange
        await ClearDatabase();
        await SetupRelatedEntities();

        var site = _fixture.CreateSite(name: "TestSite", companyId: "COMPANY_456", typeId: "TYPE_001", locationId: "LOC_001");
        await _repository.AddAsync(site);

        // Act
        var result = await _repository.GetByReferenceIdAsync(site.ReferenceId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetSiteByCompanyId Tests

    [Fact]
    public async Task GetSiteByCompanyId_ShouldReturnSitesForSpecificCompany_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupRelatedEntities();

        var site1 = _fixture.CreateSite(name: "Site1", companyId: "COMPANY_123", typeId: "TYPE_001", locationId: "LOC_001");
        var site2 = _fixture.CreateSite(name: "Site2", companyId: "COMPANY_456", typeId: "TYPE_001", locationId: "LOC_001");

        await _repository.AddAsync(site1);
        await _repository.AddAsync(site2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetSiteByCompanyId("COMPANY_456");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Site2", result.First().Name);
    }

    [Fact]
    public async Task GetSiteByCompanyId_ShouldReturnSitesForLoggedInCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupRelatedEntities();

        var site1 = _fixture.CreateSite(name: "Site1", companyId: "COMPANY_123", typeId: "TYPE_001", locationId: "LOC_001");
        var site2 = _fixture.CreateSite(name: "Site2", companyId: "COMPANY_456", typeId: "TYPE_001", locationId: "LOC_001");

        await _repository.AddAsync(site1);
        await _repository.AddAsync(site2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetSiteByCompanyId("COMPANY_456");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should return empty because user's company is COMPANY_123
    }

    #endregion

    #region GetSiteBySiteType Tests

    [Fact]
    public async Task GetSiteBySiteType_ShouldReturnSitesWithMatchingTypeAndCompany_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupRelatedEntities();

        var site1 = _fixture.CreateSite(name: "Site1", companyId: "COMPANY_123", typeId: "TYPE_001", locationId: "LOC_001");
        var site2 = _fixture.CreateSite(name: "Site2", companyId: "COMPANY_123", typeId: "TYPE_002", locationId: "LOC_001");
        var site3 = _fixture.CreateSite(name: "Site3", companyId: "COMPANY_456", typeId: "TYPE_001", locationId: "LOC_001");

        await _repository.AddAsync(site1);
        await _repository.AddAsync(site2);
        await _repository.AddAsync(site3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetSiteBySiteType("COMPANY_123", "TYPE_001");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Site1", result.First().Name);
    }

    [Fact]
    public async Task GetSiteBySiteType_ShouldReturnSitesForLoggedInCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupRelatedEntities();

        var site1 = _fixture.CreateSite(name: "Site1", companyId: "COMPANY_123", typeId: "TYPE_001", locationId: "LOC_001");
        var site2 = _fixture.CreateSite(name: "Site2", companyId: "COMPANY_456", typeId: "TYPE_001", locationId: "LOC_001");

        await _repository.AddAsync(site1);
        await _repository.AddAsync(site2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetSiteBySiteType("COMPANY_456", "TYPE_001");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should return empty because user's company is COMPANY_123
    }

    #endregion

    #region IsSiteNameExist Tests

    [Fact]
    public async Task IsSiteNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsNotValidGuid()
    {
        // Arrange
        await ClearDatabase();

        var site = _fixture.CreateSite(name: "Existing Site");
        await _repository.AddAsync(site);

        // Act
        var result = await _repository.IsSiteNameExist("Existing Site", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsSiteNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsNotValidGuid()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsSiteNameExist("Non-existing Site", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSiteNameExist_ShouldReturnFalse_WhenNameExistsButIdMatches()
    {
        // Arrange
        await ClearDatabase();

        var site = _fixture.CreateSite(name: "Test Site");
        await _repository.AddAsync(site);

        // Act
        var result = await _repository.IsSiteNameExist("Test Site", site.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSiteNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsDifferent()
    {
        // Arrange
        await ClearDatabase();

        var site = _fixture.CreateSite(name: "Test Site");
        await _repository.AddAsync(site);

        // Act
        var result = await _repository.IsSiteNameExist("Test Site", Guid.NewGuid().ToString());

        // Assert
        Assert.True(result);
    }

    #endregion

    #region IsSiteNameUnique Tests

    [Fact]
    public async Task IsSiteNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();

        var site = _fixture.CreateSite(name: "Existing Site");
        await _repository.AddAsync(site);

        // Act
        var result = await _repository.IsSiteNameUnique("Existing Site");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsSiteNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsSiteNameUnique("Non-existing Site");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSiteNameUnique_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();

        var site = _fixture.CreateSite(name: "Test Site");
        await _repository.AddAsync(site);

        // Act
        var result = await _repository.IsSiteNameUnique("test site");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetSitesById Tests

    [Fact]
    public async Task GetSitesById_ShouldReturnSiteWithTypeId_WhenIdExists()
    {
        // Arrange
        await ClearDatabase();

        var site = _fixture.CreateSite(name: "Test Site", typeId: "TYPE_001");
        await _repository.AddAsync(site);

        // Act
        var result = await _repository.GetSitesById(site.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("TYPE_001", result.TypeId);
    }

    [Fact]
    public async Task GetSitesById_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetSitesById(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetSitesByIds Tests

    [Fact]
    public async Task GetSitesByIds_ShouldReturnMatchingSites_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var site1 = _fixture.CreateSite(name: "Site1", location: "Location1");
        var site2 = _fixture.CreateSite(name: "Site2", location: "Location2");
        var site3 = _fixture.CreateSite(name: "Site3", location: "Location3");

        await _repository.AddAsync(site1);
        await _repository.AddAsync(site2);
        await _repository.AddAsync(site3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var ids = new List<string> { site1.ReferenceId, site2.ReferenceId };

        // Act
        var result = await _repository.GetSitesByIds(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.Name == "Site1");
        Assert.Contains(result, s => s.Name == "Site2");
        Assert.DoesNotContain(result, s => s.Name == "Site3");
    }

    [Fact]
    public async Task GetSitesByIds_ShouldReturnSitesForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var site1 = _fixture.CreateSite(name: "Site1", companyId: "COMPANY_123", location: "Location1");
        var site2 = _fixture.CreateSite(name: "Site2", companyId: "COMPANY_456", location: "Location2");

        await _repository.AddAsync(site1);
        await _repository.AddAsync(site2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var ids = new List<string> { site1.ReferenceId, site2.ReferenceId };

        // Act
        var result = await _repository.GetSitesByIds(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Site1", result.First().Name);
    }

    #endregion

    #region GetSiteBySiteTypeId Tests

    [Fact]
    public async Task GetSiteBySiteTypeId_ShouldReturnSitesWithMatchingTypeId()
    {
        // Arrange
        await ClearDatabase();

        var site1 = _fixture.CreateSite(name: "Site1", typeId: "TYPE_001");
        var site2 = _fixture.CreateSite(name: "Site2", typeId: "TYPE_001");
        var site3 = _fixture.CreateSite(name: "Site3", typeId: "TYPE_002");

        await _repository.AddAsync(site1);
        await _repository.AddAsync(site2);
        await _repository.AddAsync(site3);

        // Act
        var result = await _repository.GetSiteBySiteTypeId("TYPE_001");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.Name == "Site1");
        Assert.Contains(result, s => s.Name == "Site2");
        Assert.DoesNotContain(result, s => s.Name == "Site3");
    }

    #endregion

    #region GetSiteBySiteLocation Tests

    [Fact]
    public async Task GetSiteBySiteLocation_ShouldReturnSitesWithMatchingLocation()
    {
        // Arrange
        await ClearDatabase();

        var site1 = _fixture.CreateSite(name: "Site1", location: "New York");
        var site2 = _fixture.CreateSite(name: "Site2", location: "New York");
        var site3 = _fixture.CreateSite(name: "Site3", location: "London");

        await _repository.AddAsync(site1);
        await _repository.AddAsync(site2);
        await _repository.AddAsync(site3);

        // Act
        var result = await _repository.GetSiteBySiteLocation("New York");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.Name == "Site1");
        Assert.Contains(result, s => s.Name == "Site2");
        Assert.DoesNotContain(result, s => s.Name == "Site3");
    }

    #endregion

    #region GetSiteNames Tests

    [Fact]
    public async Task GetSiteNames_ShouldReturnActiveSitesForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var site1 = _fixture.CreateSite(name: "Site_B", companyId: "COMPANY_123", typeId: "TYPE_001", isActive: true);
        var site2 = _fixture.CreateSite(name: "Site_A", companyId: "COMPANY_123", typeId: "TYPE_002", isActive: true);
        var site3 = _fixture.CreateSite(name: "Site_C", companyId: "COMPANY_456", typeId: "TYPE_001", isActive: true);
        var site4 = _fixture.CreateSite(name: "Site_D", companyId: "COMPANY_123", typeId: "TYPE_001", isActive: false);

        await _dbContext.Sites.AddRangeAsync(site1, site2, site3, site4);
        _dbContext.SaveChanges();

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetSiteNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Equal("Site_A", result[0].Name); // Should be ordered by name
        Assert.Equal("Site_B", result[1].Name);

    }

    [Fact]
    public async Task GetSiteNames_ShouldReturnAllActiveSites_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var site1 = _fixture.CreateSite(name: "Site_B", companyId: "COMPANY_123", isActive: true);
        var site2 = _fixture.CreateSite(name: "Site_A", companyId: "COMPANY_456", isActive: true);
        var site3 = _fixture.CreateSite(name: "Site_C", companyId: "COMPANY_123", isActive: false);

        await _dbContext.Sites.AddRangeAsync(site1, site2, site3);
        _dbContext.SaveChanges();

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
      var result = await _repository.GetSiteNames();

        // Assert
        Assert.NotNull(result);

    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnSite_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var site = _fixture.CreateSite(name: "Test Site");
        await _repository.AddAsync(site);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetByIdAsync(site.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Site", result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnSite_WhenUserIsNotParentAndSameCompany()
    {
        // Arrange
        await ClearDatabase();

        var site = _fixture.CreateSite(name: "Test Site", companyId: "COMPANY_123");
        await _repository.AddAsync(site);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetByIdAsync(site.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Site", result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenUserIsNotParentAndDifferentCompany()
    {
        // Arrange
        await ClearDatabase();

        var site = _fixture.CreateSite(name: "Test Site", companyId: "COMPANY_456");
        await _repository.AddAsync(site);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetByIdAsync(site.Id);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetSitesBySiteTypeCategory Tests

    [Fact]
    public async Task GetSitesBySiteTypeCategory_ShouldReturnSitesWithMatchingCategory()
    {
        // Arrange
        await ClearDatabase();
        await SetupRelatedEntities();

        var site1 = _fixture.CreateSite(name: "Site1", companyId: "COMPANY_123", typeId: "TYPE_001"); // Primary category
        var site2 = _fixture.CreateSite(name: "Site2", companyId: "COMPANY_123", typeId: "TYPE_002"); // DR category
        var site3 = _fixture.CreateSite(name: "Site3", companyId: "COMPANY_456", typeId: "TYPE_001"); // Different company

        await _repository.AddAsync(site1);
        await _repository.AddAsync(site2);
        await _repository.AddAsync(site3);

        // Act
        var result = await _repository.GetSitesBySiteTypeCategory("primary");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Site1", result.First().Name);
    }

    [Fact]
    public async Task GetSitesBySiteTypeCategory_ShouldReturnEmpty_WhenNoCategoryMatches()
    {
        // Arrange
        await ClearDatabase();
        await SetupRelatedEntities();

        var site = _fixture.CreateSite(name: "Site1", companyId: "COMPANY_123", typeId: "TYPE_001");
        await _repository.AddAsync(site);

        // Act
        var result = await _repository.GetSitesBySiteTypeCategory("nonexistent");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetSiteGroupBySiteTypeId Tests

    [Fact]
    public async Task GetSiteGroupBySiteTypeId_ShouldReturnGroupedSites()
    {
        // Arrange
        await ClearDatabase();

        var site1 = _fixture.CreateSite(name: "Site1", typeId: "TYPE_001", isActive: true);
        var site2 = _fixture.CreateSite(name: "Site2", typeId: "TYPE_001", isActive: true);
        var site3 = _fixture.CreateSite(name: "Site3", typeId: "TYPE_002", isActive: true);
        var site4 = _fixture.CreateSite(name: "Site4", typeId: "TYPE_001", isActive: false);
        await _dbContext.Sites.AddRangeAsync(site1, site2, site3, site4);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetSiteGroupBySiteTypeId();

        // Assert
        Assert.NotNull(result);

    }

    [Fact]
    public async Task GetSiteGroupBySiteTypeId_ShouldReturnEmpty_WhenNoActiveSites()
    {
        // Arrange
        await ClearDatabase();

        var site = _fixture.CreateSite(name: "Site1", typeId: "TYPE_001", isActive: false);

        await _dbContext.Sites.AddAsync(site);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetSiteGroupBySiteTypeId();

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupRelatedEntities();

        var site1 = _fixture.CreateSite(name: "Site1", typeId: "TYPE_001", locationId: "LOC_001");
        var site2 = _fixture.CreateSite(name: "Site2", typeId: "TYPE_002", locationId: "LOC_002");
        var site3 = _fixture.CreateSite(name: "Site3", typeId: "TYPE_001", locationId: "LOC_001");

        await _repository.AddAsync(site1);
        await _repository.AddAsync(site2);
        await _repository.AddAsync(site3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        string? searchString = null;

        var specification = new SiteFilterSpecification(searchString);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 2, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Data.Count);
        Assert.Equal(3, result.TotalCount);
        Assert.Equal(2, result.PageSize);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResultsForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupRelatedEntities();

        var site1 = _fixture.CreateSite(name: "Site1", companyId: "COMPANY_123", typeId: "TYPE_001", locationId: "LOC_001");
        var site2 = _fixture.CreateSite(name: "Site2", companyId: "COMPANY_456", typeId: "TYPE_002", locationId: "LOC_002");

        await _repository.AddAsync(site1);
        await _repository.AddAsync(site2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        string? searchString = null;

        var specification = new SiteFilterSpecification(searchString);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Data);
        Assert.Equal("Site1", result.Data.First().Name);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public async Task GetPaginatedQuery_ShouldReturnQueryableForCompany()
    {
        // Arrange
        await ClearDatabase();
        await SetupRelatedEntities();

        var site1 = _fixture.CreateSite(name: "Site1", companyId: "COMPANY_123", typeId: "TYPE_001", locationId: "LOC_001");
        var site2 = _fixture.CreateSite(name: "Site2", companyId: "COMPANY_123", typeId: "TYPE_002", locationId: "LOC_002");
        var site3 = _fixture.CreateSite(name: "Site3", companyId: "COMPANY_456", typeId: "TYPE_001", locationId: "LOC_001");

        await _repository.AddAsync(site1);
        await _repository.AddAsync(site2);
        await _repository.AddAsync(site3);

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = await result.ToListAsync();
        Assert.Equal(2, resultList.Count);
        Assert.Contains(resultList, s => s.Name == "Site1");
        Assert.Contains(resultList, s => s.Name == "Site2");
        Assert.DoesNotContain(resultList, s => s.Name == "Site3");
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.Sites.RemoveRange(_dbContext.Sites);
        _dbContext.Companies.RemoveRange(_dbContext.Companies);
        _dbContext.SiteTypes.RemoveRange(_dbContext.SiteTypes);
        _dbContext.SiteLocations.RemoveRange(_dbContext.SiteLocations);
        await _dbContext.SaveChangesAsync();
    }

    private async Task SetupRelatedEntities()
    {
        // Setup Companies
        var company1 = new Company
        {
            ReferenceId = "COMPANY_123",
            Name = "Test Company",
            DisplayName = "Test Company",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };

        var company2 = new Company
        {
            ReferenceId = "COMPANY_456",
            Name = "Other Company",
            DisplayName = "Other Company",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };

        // Setup SiteTypes
        var siteType1 = new SiteType
        {
            ReferenceId = "TYPE_001",
            Type = "Primary",
            Category = "Primary",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };

        var siteType2 = new SiteType
        {
            ReferenceId = "TYPE_002",
            Type = "DR",
            Category = "DR",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };

        // Setup SiteLocations
        var location1 = new SiteLocation
        {
            ReferenceId = "LOC_001",
            City = "New York",
            Country = "USA",
            Lat = "40.7128",
            Lng = "-74.0060",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };

        var location2 = new SiteLocation
        {
            ReferenceId = "LOC_002",
            City = "London",
            Country = "UK",
            Lat = "51.5074",
            Lng = "-0.1278",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };

        _dbContext.Companies.AddRange(company1, company2);
        _dbContext.SiteTypes.AddRange(siteType1, siteType2);
        _dbContext.SiteLocations.AddRange(location1, location2);
        await _dbContext.SaveChangesAsync();
    }
}
