using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator.Helper;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class BulkImportOperationFixture
{
    public List<BulkImportOperationListVm> BulkImportOperationListVm { get; }
    public List<BulkImportOperationRunningListVm> BulkImportOperationRunningListVm { get; }
    public BulkImportOperationDetailVm BulkImportOperationDetailVm { get; }
    public CreateBulkImportOperationCommand CreateBulkImportOperationCommand { get; }
    public CreateBulkImportValidatorCommand CreateBulkImportValidatorCommand { get; }
    public UpdateBulkImportOperationCommand UpdateBulkImportOperationCommand { get; }

    public BulkImportOperationFixture()
    {
        var fixture = new Fixture();

        // Create sample BulkImportOperation list data
        BulkImportOperationListVm = new List<BulkImportOperationListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                UserName = "admin.user",
                Description = "Infrastructure Components Bulk Import Operation",
                Status = "Completed",
                StartTime = DateTime.Now.AddHours(-6),
                EndTime = DateTime.Now.AddHours(-4)
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                UserName = "import.specialist",
                Description = "Database Configuration Bulk Import Operation",
                Status = "Failed",
                StartTime = DateTime.Now.AddHours(-8),
                EndTime = DateTime.Now.AddHours(-7)
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                UserName = "system.operator",
                Description = "Network Security Components Import Operation",
                Status = "In Progress",
                StartTime = DateTime.Now.AddHours(-2),
                EndTime = DateTime.Now.AddHours(1)
            }
        };

        // Create sample BulkImportOperation running list data
        BulkImportOperationRunningListVm = new List<BulkImportOperationRunningListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                UserName = "active.importer",
                Description = "Active Server Import Operation",
                Status = "Running",
                StartTime = DateTime.Now.AddMinutes(-45),
                EndTime = DateTime.Now.AddMinutes(15),
                BulkImportOperationGroup = new List<BulkImportOperationGroupList>
                {
                    new()
                    {
                        Id = Guid.NewGuid().ToString(),
                        BulkImportOperationId = Guid.NewGuid().ToString(),
                        InfraObjectName = "Production Server Group",
                        CompanyId = Guid.NewGuid().ToString(),
                        ProgressStatus = "75%",
                        Status = "Processing",
                        ErrorMessage = null,
                        ConditionalOperation = 1,
                        NodeId = Guid.NewGuid().ToString()
                    }
                }
            }
        };

        // Create detailed BulkImportOperation data
        BulkImportOperationDetailVm = new BulkImportOperationDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            UserName = "enterprise.admin",
            Description = "Enterprise-wide Infrastructure Import Operation",
            Status = "Completed",
            StartTime = DateTime.Now.AddHours(-12),
            EndTime = DateTime.Now.AddHours(-10)
        };

        // Create command for creating BulkImportOperation
        CreateBulkImportOperationCommand = new CreateBulkImportOperationCommand
        {
            CompanyId = Guid.NewGuid().ToString(),
            UserName = "new.importer",
            Description = "New Bulk Import Operation for Application Servers",
            Status = "Initiated",
            StartTime = DateTime.Now,
            EndTime = DateTime.Now.AddHours(2),
            BulkImportOperationList = new List<CreateBulkImportOperationListCommand>()
        };

        // Create command for BulkImport validator
        CreateBulkImportValidatorCommand = new CreateBulkImportValidatorCommand
        {
            BulkImportOperationList = new List<CreateBulkImportValidatorCommandList>
            {
                new()
                {
                    ServerList = new List<CreateBulkImportComponentServerCommand>(),
                    DatabaseList = new List<CreateBulkImportComponentDataBaseCommand>(),
                    ReplicationList = new List<CreateBulkImportComponentReplicationCommand>(),
                    InfraObject = new CreateBulkImportComponentInfraObjectCommand(),
                    IsSwitchOver = true,
                    SwitchOverTemplate = "Test SwitchOver Template",
                    IsFailOver = false,
                    FailOverTemplate = null,
                    IsSwitchBack = false,
                    SwitchBackTemplate = null,
                    IsFailBack = false,
                    FailBackTemplate = null
                }
            }
        };

        // Create command for updating BulkImportOperation
        UpdateBulkImportOperationCommand = new UpdateBulkImportOperationCommand
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            UserName = "updated.importer",
            Description = "Updated Bulk Import Operation for Security Components",
            Status = "Updated",
            StartTime = DateTime.Now.AddHours(-1),
            EndTime = DateTime.Now.AddHours(1)
        };
    }
}
