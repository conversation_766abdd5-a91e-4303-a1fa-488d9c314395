using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DriftParameter.Commands.Create;
using ContinuityPatrol.Application.Features.DriftParameter.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftParameter.Commands.Update;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetList;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftParameterModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DriftParameterControllerTests : IClassFixture<DriftParameterFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DriftParametersController _controller;
    private readonly DriftParameterFixture _driftParameterFixture;

    public DriftParameterControllerTests(DriftParameterFixture driftParameterFixture)
    {
        _driftParameterFixture = driftParameterFixture;

        var testBuilder = new ControllerTestBuilder<DriftParametersController>();
        _controller = testBuilder.CreateController(
            _ => new DriftParametersController(),
            out _mediatorMock);
    }

    #region GetDriftParameters Tests

    [Fact]
    public async Task GetDriftParameters_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _driftParameterFixture.DriftParameterListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftParameterListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftParameters();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftParameterListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.NotNull(item.Name));
        Assert.All(returnedList, item => Assert.True(item.Severity >= 1));
    }

    [Fact]
    public async Task GetDriftParameters_WithEmptyResult_ReturnsOkWithEmptyList()
    {
        // Arrange
        var emptyList = new List<DriftParameterListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftParameterListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDriftParameters();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftParameterListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDriftParameters_WithException_ThrowsException()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftParameterListQuery>(), default))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.GetDriftParameters());
    }

    #endregion

    #region CreateDriftParameter Tests

    [Fact]
    public async Task CreateDriftParameter_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftParameterFixture.CreateDriftParameterCommand;
        var expectedResponse = _driftParameterFixture.CreateDriftParameterResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftParameter(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftParameterResponse>(createdResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        Assert.True(command.Severity >= 1);
        Assert.NotNull(command.Name);
    }

    [Fact]
    public async Task CreateDriftParameter_WithNullCommand_HandlesGracefully()
    {
        // Arrange
        CreateDriftParameterCommand nullCommand = null;

        var successResponse = new CreateDriftParameterResponse
        {
            Success = true,
            Message = "DriftParameter created successfully",
            Id = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(nullCommand, default))
            .ReturnsAsync(successResponse);

        // Act
        var result = await _controller.CreateDriftParameter(nullCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftParameterResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreateDriftParameter_WithInvalidDriftCategoryId_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftParameterFixture.CreateDriftParameterCommand;
        command.DriftCategoryId = "invalid-guid"; // Invalid GUID

        var failureResponse = new CreateDriftParameterResponse
        {
            Success = false,
            Message = "Invalid DriftCategoryId format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.CreateDriftParameter(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftParameterResponse>(createdResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region UpdateDriftParameter Tests

    [Fact]
    public async Task UpdateDriftParameter_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _driftParameterFixture.UpdateDriftParameterCommand;
        var expectedResponse = _driftParameterFixture.UpdateDriftParameterResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftParameter(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftParameterResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        // Check if ID is a valid GUID (handle AutoFixture generated IDs that may have prefixes)
        var idToCheck = returnedResponse.Id.StartsWith("Id") ? returnedResponse.Id.Substring(2) : returnedResponse.Id;
        Assert.True(Guid.TryParse(idToCheck, out _), $"Expected valid GUID but got: {returnedResponse.Id}");
        Assert.True(command.Severity >= 1);
        Assert.NotNull(command.Name);
    }

    [Fact]
    public async Task UpdateDriftParameter_WithNullCommand_HandlesGracefully()
    {
        // Arrange
        UpdateDriftParameterCommand nullCommand = null;

        var successResponse = new UpdateDriftParameterResponse
        {
            Success = true,
            Message = "DriftParameter updated successfully",
            Id = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(nullCommand, default))
            .ReturnsAsync(successResponse);

        // Act
        var result = await _controller.UpdateDriftParameter(nullCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftParameterResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDriftParameter_WithInvalidId_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftParameterFixture.UpdateDriftParameterCommand;
        command.Id = "invalid-guid";

        var failureResponse = new UpdateDriftParameterResponse
        {
            Success = false,
            Message = "Invalid ID format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.UpdateDriftParameter(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftParameterResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region GetDriftParameterById Tests

    [Fact]
    public async Task GetDriftParameterById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftParameterFixture.DriftParameterDetailVm;
        expectedDetail.Id = id;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftParameterDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftParameterById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftParameterDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.NotNull(returnedDetail.Name);
        Assert.True(returnedDetail.Severity >= 1);
        Assert.True(returnedDetail.IsActive);
        Assert.NotNull(returnedDetail.CreatedBy);
        Assert.NotNull(returnedDetail.LastModifiedBy);
    }

    [Fact]
    public async Task GetDriftParameterById_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDriftParameterById(invalidId));
    }

    [Fact]
    public async Task GetDriftParameterById_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetDriftParameterById(nullId));
    }

    #endregion

    #region DeleteDriftParameter Tests

    [Fact]
    public async Task DeleteDriftParameter_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftParameterFixture.DeleteDriftParameterResponse;
        expectedResponse.IsActive=false;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDriftParameterCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftParameter(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftParameterResponse>(okResult.Value);
        Assert.False(returnedResponse.IsActive);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
    }

    [Fact]
    public async Task DeleteDriftParameter_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDriftParameter(invalidId));
    }

    [Fact]
    public async Task DeleteDriftParameter_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteDriftParameter(nullId));
    }

    #endregion

    #region GetPaginatedDriftParameters Tests

    [Fact]
    public async Task GetPaginatedDriftParameters_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _driftParameterFixture.GetDriftParameterPaginatedListQuery;
        var expectedResult = _driftParameterFixture.DriftParameterPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftParameters(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftParameterListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.True(returnedResult.Succeeded);
        Assert.NotEmpty(returnedResult.Data);
        Assert.All(returnedResult.Data, item => Assert.NotNull(item.Name));
    }

    [Fact]
    public async Task GetPaginatedDriftParameters_WithNullQuery_HandlesGracefully()
    {
        // Arrange
        GetDriftParameterPaginatedListQuery nullQuery = null;

        var emptyResult = new PaginatedResult<DriftParameterListVm>
        {
            Data = new List<DriftParameterListVm>(),
            TotalCount = 0,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(nullQuery, default))
            .ReturnsAsync(emptyResult);

        // Act
        var result = await _controller.GetPaginatedDriftParameters(nullQuery);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftParameterListVm>>(okResult.Value);
        Assert.True(returnedResult.Succeeded);
        Assert.Empty(returnedResult.Data);
    }

    [Fact]
    public async Task GetPaginatedDriftParameters_WithInvalidPageSize_ReturnsEmptyResult()
    {
        // Arrange
        var query = _driftParameterFixture.GetDriftParameterPaginatedListQuery;
        query.PageSize = 0; // Invalid page size

        var emptyResult = new PaginatedResult<DriftParameterListVm>
        {
            Data = new List<DriftParameterListVm>(),
            TotalCount = 0,
            Succeeded = false
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(emptyResult);

        // Act
        var result = await _controller.GetPaginatedDriftParameters(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftParameterListVm>>(okResult.Value);
        Assert.False(returnedResult.Succeeded);
        Assert.Empty(returnedResult.Data);
    }

    #endregion

    #region IsDriftParameterNameUnique Tests

    [Fact]
    public async Task IsDriftParameterNameUnique_WithUniqueName_ReturnsTrue()
    {
        // Arrange
        var parameterName = "Unique Enterprise Parameter";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftParameterNameUniqueQuery>(q => q.Name == parameterName && q.Id == id), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDriftParameterNameExist(parameterName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var isUnique = Assert.IsType<bool>(okResult.Value);
        Assert.True(isUnique);
    }

    [Fact]
    public async Task IsDriftParameterNameUnique_WithDuplicateName_ReturnsFalse()
    {
        // Arrange
        var parameterName = "Duplicate Enterprise Parameter";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftParameterNameUniqueQuery>(q => q.Name == parameterName && q.Id == id), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftParameterNameExist(parameterName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var isUnique = Assert.IsType<bool>(okResult.Value);
        Assert.False(isUnique);
    }

    [Fact]
    public async Task IsDriftParameterNameUnique_WithNullName_ThrowsArgumentNullException()
    {
        // Arrange
        string nullName = null;
        var id = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsDriftParameterNameExist(nullName, id));
    }

    [Fact]
    public async Task IsDriftParameterNameUnique_WithNullId_ReturnsValidResult()
    {
        // Arrange
        var parameterName = "Enterprise Parameter";
        string nullId = null;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftParameterNameUniqueQuery>(q => q.Name == parameterName && q.Id == nullId), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDriftParameterNameExist(parameterName, nullId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var isUnique = Assert.IsType<bool>(okResult.Value);
        Assert.True(isUnique);
    }

    #endregion

    #region Additional Comprehensive Test Cases

    [Fact]
    public async Task CreateDriftParameter_WithHighSeverityConfiguration_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftParameterFixture.CreateDriftParameterCommand;
        command.Name = "Critical Security Parameter";
        command.Severity = 5;
        command.Properties = "{\"security_level\": \"critical\", \"encryption\": true, \"audit_required\": true}";
        var expectedResponse = _driftParameterFixture.CreateDriftParameterResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftParameter(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftParameterResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Critical Security Parameter", command.Name);
        Assert.Equal(5, command.Severity);
        Assert.Contains("security_level", command.Properties);
    }

    [Fact]
    public async Task UpdateDriftParameter_WithComplexProperties_ReturnsOkResult()
    {
        // Arrange
        var command = _driftParameterFixture.UpdateDriftParameterCommand;
        command.Properties = "{\"thresholds\": {\"cpu\": 85, \"memory\": 80, \"disk\": 90}, \"alerts\": [\"email\", \"sms\"]}";
        command.Severity = 4;
        var expectedResponse = _driftParameterFixture.UpdateDriftParameterResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftParameter(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftParameterResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("thresholds", command.Properties);
        Assert.Contains("alerts", command.Properties);
        Assert.Equal(4, command.Severity);
    }

    [Fact]
    public async Task GetDriftParameterById_WithComplexConfiguration_ReturnsDetailedParameter()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftParameterFixture.DriftParameterDetailVm;
        expectedDetail.Id = id; // Set the ID to match the test parameter
        expectedDetail.Properties = "{\"monitoring\": {\"interval\": 300, \"retries\": 3}, \"validation\": {\"script\": \"validate.ps1\"}}";
        expectedDetail.Severity = 3;
        expectedDetail.DriftCategoryName = "Performance Monitoring";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftParameterDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftParameterById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftParameterDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.Contains("monitoring", returnedDetail.Properties);
        Assert.Contains("validation", returnedDetail.Properties);
        Assert.Equal(3, returnedDetail.Severity);
        Assert.Equal("Performance Monitoring", returnedDetail.DriftCategoryName);
    }

    [Fact]
    public async Task GetPaginatedDriftParameters_WithSeverityFiltering_ReturnsFilteredResults()
    {
        // Arrange
        var query = _driftParameterFixture.GetDriftParameterPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 20;
        query.SearchString = "severity:high";
        var expectedResult = _driftParameterFixture.DriftParameterPaginatedResult;
        expectedResult.Data.ForEach(x => x.Severity = 4);

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftParameters(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftParameterListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.All(returnedResult.Data, item => Assert.True(item.Severity >= 4));
        Assert.Equal("severity:high", query.SearchString);
    }

    [Fact]
    public async Task GetDriftParameters_WithCategoryGrouping_ReturnsGroupedParameters()
    {
        // Arrange
        var expectedList = _driftParameterFixture.DriftParameterListVm;
        expectedList.ForEach(x =>
        {
            x.DriftCategoryName = "Security Compliance";
            x.Severity = 5;
            x.Properties = "{\"compliance_standard\": \"SOX\", \"audit_frequency\": \"daily\"}";
        });

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftParameterListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftParameters();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftParameterListVm>>(okResult.Value);
        Assert.All(returnedList, item => Assert.Equal("Security Compliance", item.DriftCategoryName));
        Assert.All(returnedList, item => Assert.Equal(5, item.Severity));
        Assert.All(returnedList, item => Assert.Contains("compliance_standard", item.Properties));
    }

    [Fact]
    public async Task IsDriftParameterNameExist_WithSimilarNamesButDifferentCase_ReturnsCorrectResult()
    {
        // Arrange
        var parameterName = "ENTERPRISE DRIFT PARAMETER";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftParameterNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftParameterNameExist(parameterName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    [Fact]
    public async Task DeleteDriftParameter_WithDependentConfigurations_ReturnsSuccessResponse()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftParameterFixture.DeleteDriftParameterResponse;
        expectedResponse.Message = "DriftParameter and all dependent configurations deleted successfully";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDriftParameterCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftParameter(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftParameterResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("dependent configurations", returnedResponse.Message);
    }

    [Fact]
    public async Task CreateDriftParameter_WithValidationScript_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftParameterFixture.CreateDriftParameterCommand;
        command.Properties = "{\"validation_script\": \"if ($cpu -gt 90) { return $false } else { return $true }\", \"script_type\": \"powershell\"}";
        command.Name = "CPU Threshold Validation Parameter";
        var expectedResponse = _driftParameterFixture.CreateDriftParameterResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftParameter(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftParameterResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("validation_script", command.Properties);
        Assert.Contains("powershell", command.Properties);
        Assert.Equal("CPU Threshold Validation Parameter", command.Name);
    }

    [Fact]
    public async Task GetDriftParameters_WithActiveStatusFilter_ReturnsOnlyActiveParameters()
    {
        // Arrange
        var expectedList = _driftParameterFixture.DriftParameterListVm;
        expectedList.ForEach(x => x.Name = "Active Parameter");

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftParameterListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftParameters();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftParameterListVm>>(okResult.Value);
        Assert.All(returnedList, item => Assert.Contains("Active", item.Name));
        Assert.True(returnedList.Count > 0);
    }

    [Fact]
    public async Task UpdateDriftParameter_WithSeverityEscalation_ReturnsOkResult()
    {
        // Arrange
        var command = _driftParameterFixture.UpdateDriftParameterCommand;
        command.Severity = 5; // Escalated to critical
        command.Properties = "{\"escalation\": {\"reason\": \"security_breach\", \"approved_by\": \"admin\", \"timestamp\": \"2024-01-01T10:00:00Z\"}}";
        var expectedResponse = _driftParameterFixture.UpdateDriftParameterResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftParameter(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftParameterResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal(5, command.Severity);
        Assert.Contains("escalation", command.Properties);
        Assert.Contains("security_breach", command.Properties);
    }

    #endregion
}
