﻿using ContinuityPatrol.Application.Features.BusinessService.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessService.Queries;

public class GetBusinessServicePaginatedListQueryHandlerTests : IClassFixture<BusinessServiceFixture>
{
    private readonly BusinessServiceFixture _businessServiceFixture;
    private readonly GetBusinessServicePaginatedListQueryHandler _handler;
    private readonly Mock<IBusinessServiceRepository> _mockBusinessServiceRepository;

    public GetBusinessServicePaginatedListQueryHandlerTests(BusinessServiceFixture businessServiceFixture)
    {
        _businessServiceFixture = businessServiceFixture;

        _businessServiceFixture.BusinessServices[0].Name = "Test_BusinessService";
        _businessServiceFixture.BusinessServices[0].CompanyName = "PTS_1";
        _businessServiceFixture.BusinessServices[0].Description = "Testing";


        _businessServiceFixture.BusinessServices[1].Name = "Test_BusinessService2";
        _businessServiceFixture.BusinessServices[1].CompanyName = "PTS_2";
        _businessServiceFixture.BusinessServices[1].Description = "Test_Purpose";

        _mockBusinessServiceRepository = BusinessServiceRepositoryMocks.GetPaginatedBusinessServiceRepository(_businessServiceFixture.BusinessServices);

        _handler = new GetBusinessServicePaginatedListQueryHandler(_businessServiceFixture.Mapper, _mockBusinessServiceRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetBusinessServicePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessServiceListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_BusinessServices_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetBusinessServicePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=Test_BusinessService;companyname=PTS_1;description=Testing" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessServiceListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Name.ShouldBe("Test_BusinessService");

        result.Data[0].CompanyName.ShouldBe("PTS_1");

        result.Data[1].Description.ShouldBe("Test_Purpose");

        result.Data[0].CompanyId.ShouldBe(_businessServiceFixture.BusinessServices[0].CompanyId);



        result.Data[0].Priority.ShouldBe(_businessServiceFixture.BusinessServices[0].Priority);
    }

    [Fact]
    public async Task Handle_Return_PaginatedBusinessServices_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetBusinessServicePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessServiceListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<BusinessServiceListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].CompanyName.ShouldBe("PTS_1");

        result.Data[0].Name.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetBusinessServicePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABCD" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessServiceListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetBusinessServicePaginatedListQuery(), CancellationToken.None);

        _mockBusinessServiceRepository.Verify(x => x.PaginatedListAllAsync(), Times.Once);
    }
}