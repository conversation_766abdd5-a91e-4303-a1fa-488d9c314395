﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.EscalationMatrix.Events.Create;

public class EscalationMatrixCreatedEventHandler : INotificationHandler<EscalationMatrixCreatedEvent>
{
    private readonly ILogger<EscalationMatrixCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public EscalationMatrixCreatedEventHandler(ILoggedInUserService userService,
        IUserActivityRepository userActivityRepository, ILogger<EscalationMatrixCreatedEventHandler> logger)
    {
        _userService = userService;
        _userActivityRepository = userActivityRepository;
        _logger = logger;
    }

    public async Task Handle(EscalationMatrixCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.EscalationMatrix.ToString(),
            Action = $"{ActivityType.Create} {Modules.EscalationMatrix}",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $" Escalation Matrix '{createdEvent.MatrixName}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Escalation Matrix '{createdEvent.MatrixName}' created successfully.");
    }
}