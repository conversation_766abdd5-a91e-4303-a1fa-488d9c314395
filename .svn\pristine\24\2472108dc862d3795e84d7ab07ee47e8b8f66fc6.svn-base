﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class SmtpConfigurationFilterSpecification : Specification<SmtpConfiguration>
{
    public SmtpConfigurationFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.UserName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("username=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.UserName.Contains(stringItem.Replace("username=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("smtp=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.SmtpHost.Contains(stringItem.Replace("smtp=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("port=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Port.Contains(stringItem.Replace("port=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.UserName.Contains(searchString) || p.SmtpHost.Contains(searchString) ||
                    p.Port.Contains(searchString);
            }
        }
    }
}