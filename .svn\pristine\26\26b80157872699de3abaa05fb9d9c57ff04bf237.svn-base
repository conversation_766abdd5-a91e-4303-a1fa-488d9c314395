﻿@model ContinuityPatrol.Application.Features.User.Commands.UserLock.UserLockCommand
@using ContinuityPatrol.Shared.Services.Helper
<div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
    <div class="modal-content">
        <form id="CreateForm" asp-area="Admin" asp-controller="LicenseManager" asp-action="DecommissionAuthentication" method="post" enctype="multipart/form-data">
        <div class="modal-header">
            <h6 class="page_title" title="Authenticate"><i class="cp-workflow-execution"></i><span>Authenticate</span></h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
        </div>
        <div class="modal-body">
        
            <div class="mb-3">
               <div class="form-group">
                <div class="form-label">Password</div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-lock"></i></span>
                        <input type="hidden" id="loginName" asp-for="UserName" data-loginnames="@WebHelper.UserSession.LoginName">
                            <input type="password" id="txtPassword" asp-for="Password" maxlength="30" class="form-control validate" autocomplete="off" placeholder="Enter Password" required />
                            <span role="button" class="input-group-text toggle-password"><i class="cp-password-visible fs-6"></i></span>
                </div>
                    <span asp-validation-for="Password" id="Password-error"></span>
               </div>   
            </div> 
            
        </div>
        <div class="modal-footer d-flex justify-content-between align-items-center">
            <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" data-bs-target="#serverattachedModal" data-bs-toggle="modal">Back</button>
                    <button type="button" class="btn btn-primary btn-sm" id="decommissionFunction">Save</button>
            </div>
        </div>
        </form>
    </div>
</div>
@*alertNotification*@
<div class='Notification'>
    <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
        <div class='d-flex'>
            <div class='toast-body'>
                <span id="alertClass" class='{ClassName}-toast'>
                    <i id="icon_Detail" class=''></i>
                </span>
                <span id="message">

                </span>
            </div>
            <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
        </div>
    </div>
</div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Admin/License/License Manager/LicenseManager.js"></script>