﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.User.Events.Create;

public class UserCreatedEventHandler : INotificationHandler<UserCreatedEvent>
{
    private readonly ILogger<UserCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public UserCreatedEventHandler(ILoggedInUserService userService, ILogger<UserCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(UserCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        if (!_userService.LoginName.Equals("Anonymous"))
        {
            var userActivity = new Domain.Entities.UserActivity
            {
                UserId = _userService.UserId,
                LoginName = _userService.LoginName,
                RequestUrl = _userService.RequestedUrl,
                CompanyId = _userService.CompanyId,
                HostAddress = _userService.IpAddress,
                Entity = Modules.User.ToString(),
                Action = $"{ActivityType.Create} {Modules.User}",
                ActivityType = ActivityType.Create.ToString(),
                ActivityDetails = $"User '{createdEvent.UserName}' created successfully.",
                CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
                LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
            };

            await _userActivityRepository.AddAsync(userActivity);
        }

        _logger.LogInformation($"User '{createdEvent.UserName}' created successfully.");
    }
}