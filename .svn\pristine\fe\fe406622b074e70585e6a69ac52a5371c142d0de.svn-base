﻿using ContinuityPatrol.Application.Features.TableAccess.Commands.Update;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetTableNameListBySchema;
using ContinuityPatrol.Domain.ViewModels.DataSetModel;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class TableAccessControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<ILogger<TableAccessController>> _mockLogger = new();
        private  TableAccessController _controller;

        public TableAccessControllerShould()
        {
            Initialize();
        }
        internal void Initialize()
        {
            _controller = new TableAccessController(
                _mockPublisher.Object,
                _mockDataProvider.Object,
                _mockMapper.Object,
                _mockLogger.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public async Task List_ReturnsViewResult_WithTableAccessModel()
        {
            // Arrange
            var tableAccesses = new List<TableAccessListVm>();
            _mockDataProvider.Setup(dp => dp.TableAccess.GetAllTableAccesses())
                             .ReturnsAsync(tableAccesses);

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            var model = result.Model as TableAccessModel;
            Assert.NotNull(model);
            Assert.Equal(tableAccesses, model.PaginatedTableAccess);
        }

        [Fact]
        public async Task GetTableNamesBySchemaName_ReturnsJsonResult_WithSuccess()
        {
            // Arrange
            var schemaName = "schema1";
            var tableNames = new List<GetTableNameListBySchemaVm>();
            _mockDataProvider.Setup(dp => dp.TableAccess.GetTableNamesBySchemaName(schemaName))
                             .ReturnsAsync(tableNames);

            // Act
            var result = await _controller.GetTableNamesBySchemaName(schemaName) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetTableNamesBySchemaName_ReturnsJsonResult_WithNullSchemaName()
        {
            // Act
            var result = await _controller.GetTableNamesBySchemaName(null) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("Table Name Id is not valid format", json);
        }

        [Fact]
        public async Task GetTableNamesBySchemaName_ReturnsJsonResult_WithEmptySchemaName()
        {
            // Act
            var result = await _controller.GetTableNamesBySchemaName("") as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("Table Name Id is not valid format", json);
        }

        [Fact]
        public async Task GetTableNamesBySchemaName_ReturnsJsonException_WhenExceptionThrown()
        {
            // Arrange
            var schemaName = "test";
            _mockDataProvider.Setup(p => p.TableAccess.GetTableNamesBySchemaName(schemaName))
                            .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetTableNamesBySchemaName(schemaName);

            // Assert
            Assert.NotNull(result);
            // The result should be from ex.GetJsonException()
        }

        [Fact]
        public async Task GetTableAccessList_ReturnsJsonResult_WithTableAccesses()
        {
            // Arrange
            var tableAccesses = new List<TableAccessListVm>();
            _mockDataProvider.Setup(dp => dp.TableAccess.GetAllTableAccesses())
                             .ReturnsAsync(tableAccesses);

            // Act
            var result = await _controller.GetTableAccessList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            dynamic jsonResponse = result.Value;
            Assert.Equal(tableAccesses, jsonResponse);
        }

        [Fact]
        public async Task GetTableAccessList_ReturnsEmptyJson_WhenExceptionThrown()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.TableAccess.GetAllTableAccesses())
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetTableAccessList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("", result.Value);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesTableAccess_WithValidModel()
        {
            // Arrange
            var tableAccessModel = new TableAccessModel
            {
                PaginatedTableAccess = new List<TableAccessListVm>
                {
                    new TableAccessListVm { Id = "1", TableName = "Table1", SchemaName = "Schema1", IsChecked = true }
                }
            };
            var updateTableAccessList = new List<UpdateTableAccess>
            {
                new UpdateTableAccess { Id = "1", TableName = "Table1", SchemaName = "Schema1", IsChecked = true }
            };
            var response = new UpdateTableAccessResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<List<UpdateTableAccess>>(tableAccessModel.PaginatedTableAccess))
                       .Returns(updateTableAccessList);
            _mockDataProvider.Setup(dp => dp.TableAccess.UpdateAsync(It.IsAny<UpdateTableAccessCommand>()))
                             .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(tableAccessModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task CreateOrUpdate_ReturnsSuccessMessage_WhenTableAccessCommandIsEmpty()
        {
            // Arrange
            var tableAccessModel = new TableAccessModel
            {
                PaginatedTableAccess = new List<TableAccessListVm>()
            };
            var emptyUpdateTableAccessList = new List<UpdateTableAccess>();

            _mockMapper.Setup(m => m.Map<List<UpdateTableAccess>>(tableAccessModel.PaginatedTableAccess))
                       .Returns(emptyUpdateTableAccessList);

            // Act
            var result = await _controller.CreateOrUpdate(tableAccessModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("The Table is currently in use", json);
        }

        [Fact]
        public async Task CreateOrUpdate_ReturnsJsonException_WhenValidationExceptionThrown()
        {
            // Arrange
            var tableAccessModel = new TableAccessModel
            {
                PaginatedTableAccess = new List<TableAccessListVm>
                {
                    new TableAccessListVm { Id = "1", TableName = "Table1", SchemaName = "Schema1", IsChecked = true }
                }
            };
            var updateTableAccessList = new List<UpdateTableAccess>
            {
                new UpdateTableAccess { Id = "1", TableName = "Table1", SchemaName = "Schema1", IsChecked = true }
            };
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Property", "Validation error"));
            var validationException = new ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<List<UpdateTableAccess>>(tableAccessModel.PaginatedTableAccess))
                       .Returns(updateTableAccessList);
            _mockDataProvider.Setup(dp => dp.TableAccess.UpdateAsync(It.IsAny<UpdateTableAccessCommand>()))
                             .ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(tableAccessModel);

            // Assert
            Assert.NotNull(result);
            // The result should be from ex.GetJsonException()
        }

        [Fact]
        public async Task CreateOrUpdate_ReturnsJsonException_WhenGeneralExceptionThrown()
        {
            // Arrange
            var tableAccessModel = new TableAccessModel
            {
                PaginatedTableAccess = new List<TableAccessListVm>
                {
                    new TableAccessListVm { Id = "1", TableName = "Table1", SchemaName = "Schema1", IsChecked = true }
                }
            };
            var updateTableAccessList = new List<UpdateTableAccess>
            {
                new UpdateTableAccess { Id = "1", TableName = "Table1", SchemaName = "Schema1", IsChecked = true }
            };

            _mockMapper.Setup(m => m.Map<List<UpdateTableAccess>>(tableAccessModel.PaginatedTableAccess))
                       .Returns(updateTableAccessList);
            _mockDataProvider.Setup(dp => dp.TableAccess.UpdateAsync(It.IsAny<UpdateTableAccessCommand>()))
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(tableAccessModel);

            // Assert
            Assert.NotNull(result);
            // The result should be from ex.GetJsonException()
        }

        [Fact]
        public async Task TableList_ReturnsJsonResult_WithTableAccessModel()
        {
            // Arrange
            var tableAccesses = new List<TableAccessListVm>
            {
                new TableAccessListVm { Id = "1", TableName = "Table1", SchemaName = "Schema1", IsChecked = true }
            };
            _mockDataProvider.Setup(dp => dp.TableAccess.GetAllTableAccesses())
                             .ReturnsAsync(tableAccesses);

            // Act
            var result = await _controller.TableList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var model = result.Value as TableAccessModel;
            Assert.NotNull(model);
            Assert.Equal(tableAccesses, model.PaginatedTableAccess);
        }

        [Fact]
        public async Task DataSet_ReturnsJsonResult_WithDataSetList()
        {
            // Arrange
            var dataSetList = new List<DataSetListVm>
            {
                new DataSetListVm { Id = "1", DataSetName = "DataSet1", Description = "Test DataSet" }
            };
            _mockDataProvider.Setup(dp => dp.DataSet.GetDataSetList())
                             .ReturnsAsync(dataSetList);

            // Act
            var result = await _controller.DataSet() as JsonResult;

            // Assert
            Assert.NotNull(result);
            dynamic jsonResponse = result.Value;
            Assert.Equal(dataSetList, jsonResponse);
        }

        [Fact]
        public async Task DataSet_ReturnsEmptyJson_WhenExceptionThrown()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.DataSet.GetDataSetList())
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DataSet() as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("", result.Value);
        }

    }
}
