﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetPaginatedList;

public class GetWorkflowOperationPaginatedListQueryHandler : IRequestHandler<GetWorkflowOperationPaginatedListQuery,
    PaginatedResult<WorkflowOperationListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowOperationRepository _workflowOperationRepository;

    public GetWorkflowOperationPaginatedListQueryHandler(IMapper mapper,
        IWorkflowOperationRepository workflowOperationRepository)
    {
        _mapper = mapper;
        _workflowOperationRepository = workflowOperationRepository;
    }

    public async Task<PaginatedResult<WorkflowOperationListVm>> Handle(GetWorkflowOperationPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _workflowOperationRepository.GetPaginatedQuery();

        var productFilterSpec = new WorkflowOperationFilterSpecification(request.SearchString);

        var workflowOperationList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<WorkflowOperationListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return workflowOperationList;
    }
}