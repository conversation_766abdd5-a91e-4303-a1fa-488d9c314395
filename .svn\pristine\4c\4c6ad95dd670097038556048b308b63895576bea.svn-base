﻿namespace ContinuityPatrol.Application.Features.Site.Commands.Update;

public class UpdateSiteCommandValidator : AbstractValidator<UpdateSiteCommand>
{
    private readonly List<string> _allowedPlatformType = new() { "physical", "virtual", "hcl", "cloud" };
    private readonly ISiteRepository _siteRepository;

    public UpdateSiteCommandValidator(ISiteRepository siteRepository)
    {
        _siteRepository = siteRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters");

        RuleFor(p => p.Location)
            .Matches(@"^[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]$").WithMessage("Please Enter Valid {PropertyName}")
            .NotEmpty().WithMessage("{PropertyName} is required")
            .NotNull();

        RuleFor(p => p.Type)
            .NotEmpty().WithMessage("Select Site {PropertyName}")
            .Matches(@"^[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]$").WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();

        RuleFor(p => p.PlatformType)
            .NotEmpty().WithMessage("Select Site {PropertyName}")
            .Matches(@"^[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Must(value => value != null && _allowedPlatformType.Contains(value.ToLower()))
            .WithMessage("{PropertyName} is invalid.");
        ;

        RuleFor(p => p.CompanyName)
            .Matches(@"^([1-9][a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]|[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d])$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotEmpty().WithMessage("Select Company Name.")
            .NotNull();

        RuleFor(e => e)
            .MustAsync(SiteNameUnique)
            .WithMessage("A same name already exists");

        RuleFor(y => y)
            .NotNull()
            .MustAsync(VerifyGuid)
            .WithMessage("Id is invalid");
    }

    private async Task<bool> SiteNameUnique(UpdateSiteCommand e, CancellationToken token)
    {
        return !await _siteRepository.IsSiteNameExist(e.Name, e.Id);
    }

    private Task<bool> VerifyGuid(UpdateSiteCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.Id, "Site Id");
        Guard.Against.InvalidGuidOrEmpty(p.LocationId, "Location Id");
        Guard.Against.InvalidGuidOrEmpty(p.TypeId, "Type Id");
        Guard.Against.InvalidGuidOrEmpty(p.CompanyId, "Company Id");

        return Task.FromResult(true);
    }
}