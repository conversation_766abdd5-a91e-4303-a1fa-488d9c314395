﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class FormHistoryRepository : BaseRepository<FormHistory>, IFormHistoryRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public FormHistoryRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<FormHistory>> GetFormHistoryByFormId(string formId)
    {
        Guard.Against.InvalidGuidOrEmpty(formId, "FormId", "FormId cannot be invalid");

       return await _dbContext.FormHistories
           .Where(formHistory => formHistory.FormId.Equals(formId))
           .ToListAsync();
    }

    public override Task<IReadOnlyList<FormHistory>> ListAllAsync()
    {
        return _loggedInUserService.IsParent
            ? base.ListAllAsync()
            : FindByFilterAsync(formHistory => formHistory.CompanyId.Equals(_loggedInUserService.CompanyId));
    }

    public async Task<List<FormHistory>> GetFormHistoryByFormIdAndVersion(string formId, string version)
    {
        return await _dbContext.FormHistories
            .Where(formHistory => formHistory.FormId.Equals(formId) && formHistory.Version.Equals(version))
            .ToListAsync();
    }

}