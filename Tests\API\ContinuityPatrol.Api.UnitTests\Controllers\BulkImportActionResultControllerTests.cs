using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Delete;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetByOperationIdAndOperationGroupId;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class BulkImportActionResultControllerTests : IClassFixture<BulkImportActionResultFixture>
{
    private readonly BulkImportActionResultFixture _bulkImportActionResultFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly BulkImportActionResultsController _controller;

    public BulkImportActionResultControllerTests(BulkImportActionResultFixture bulkImportActionResultFixture)
    {
        _bulkImportActionResultFixture = bulkImportActionResultFixture;

        var testBuilder = new ControllerTestBuilder<BulkImportActionResultsController>();
        _controller = testBuilder.CreateController(
            _ => new BulkImportActionResultsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetBulkImportActionResults_ReturnsExpectedList()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
       // _controller.Cache.Remove(ApplicationConstants.Cache.AllBulkImportActionResultCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBulkImportActionResultListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_bulkImportActionResultFixture.BulkImportActionResultListVm);

        // Act
        var result = await _controller.GetBulkImportActionResults();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var actionResults = Assert.IsAssignableFrom<List<BulkImportActionResultListVm>>(okResult.Value);
        Assert.Equal(3, actionResults.Count);
    }

    [Fact]
    public async Task GetBulkImportActionResults_ReturnsEmptyList_WhenNoResultsExist()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        //_controller.Cache.Remove(ApplicationConstants.Cache.AllBulkImportActionResultCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBulkImportActionResultListQuery>(), default))
            .ReturnsAsync(new List<BulkImportActionResultListVm>());

        // Act
        var result = await _controller.GetBulkImportActionResults();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var actionResults = Assert.IsAssignableFrom<List<BulkImportActionResultListVm>>(okResult.Value);
        Assert.Empty(actionResults);
    }

    [Fact]
    public async Task GetBulkImportActionResultById_ReturnsActionResult_WhenIdIsValid()
    {
        // Arrange
        var actionResultId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBulkImportActionResultDetailQuery>(q => q.Id == actionResultId), default))
            .ReturnsAsync(_bulkImportActionResultFixture.BulkImportActionResultDetailVm);

        // Act
        var result = await _controller.GetBulkImportActionResultById(actionResultId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var actionResult = Assert.IsType<BulkImportActionResultDetailVm>(okResult.Value);
        Assert.NotNull(actionResult);
    }

    [Fact]
    public async Task GetBulkImportActionResultById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetBulkImportActionResultById("invalid-guid"));
    }

    [Fact]
    public async Task CreateBulkImportActionResult_Returns201Created()
    {
        // Arrange
        var command = _bulkImportActionResultFixture.CreateBulkImportActionResultCommand;
        var expectedMessage = $"BulkImportActionResult '{command.EntityName}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBulkImportActionResultResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBulkImportActionResult(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBulkImportActionResultResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBulkImportActionResult_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"BulkImportActionResult '{_bulkImportActionResultFixture.UpdateBulkImportActionResultCommand.EntityName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateBulkImportActionResultCommand>(), default))
            .ReturnsAsync(new UpdateBulkImportActionResultResponse
            {
                Message = expectedMessage,
                Id = _bulkImportActionResultFixture.UpdateBulkImportActionResultCommand.Id
            });

        // Act
        var result = await _controller.UpdateBulkImportActionResult(_bulkImportActionResultFixture.UpdateBulkImportActionResultCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBulkImportActionResultResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBulkImportActionResult_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "BulkImportActionResult 'Test Action Result' has been deleted successfully!.";
        var actionResultId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBulkImportActionResultCommand>(c => c.Id == actionResultId), default))
            .ReturnsAsync(new DeleteBulkImportActionResultResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteBulkImportActionResult(actionResultId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBulkImportActionResultResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetByOperationIdAndOperationGroupId_ReturnsExpectedResults()
    {
        // Arrange
        var operationId = Guid.NewGuid().ToString();
        var operationGroupId = Guid.NewGuid().ToString();
        var expectedResults = _bulkImportActionResultFixture.BulkImportActionResultListVm.Take(2).ToList();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByOperationIdAndOperationGroupIdQuery>(q =>
                q.BulkImportOperationId == operationId && q.BulkImportOperationGroupId == operationGroupId), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetByOperationIdAndOperationGroupId(operationId, operationGroupId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var actionResults = Assert.IsAssignableFrom<List<BulkImportActionResultListVm>>(okResult.Value);
        Assert.Equal(2, actionResults.Count);
    }

    [Fact]
    public async Task CreateBulkImportActionResult_ValidatesEntityType()
    {
        // Arrange
        var command = new CreateBulkImportActionResultCommand
        {
            EntityName = "Test Entity",
            EntityType = "", // Empty entity type should cause validation error
            Status = "Initiated"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("EntityType is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBulkImportActionResult(command));
    }

    [Fact]
    public async Task UpdateBulkImportActionResult_ValidatesActionResultExists()
    {
        // Arrange
        var command = new UpdateBulkImportActionResultCommand
        {
            Id = Guid.NewGuid().ToString(),
            EntityName = "Updated Entity",
            Status = "Updated"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("BulkImportActionResult not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateBulkImportActionResult(command));
    }

    [Fact]
    public async Task GetByOperationIdAndOperationGroupId_HandlesEmptyResults()
    {
        // Arrange
        var operationId = Guid.NewGuid().ToString();
        var operationGroupId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByOperationIdAndOperationGroupIdQuery>(q =>
                q.BulkImportOperationId == operationId && q.BulkImportOperationGroupId == operationGroupId), default))
            .ReturnsAsync(new List<BulkImportActionResultListVm>());

        // Act
        var result = await _controller.GetByOperationIdAndOperationGroupId(operationId, operationGroupId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var actionResults = Assert.IsAssignableFrom<List<BulkImportActionResultListVm>>(okResult.Value);
        Assert.Empty(actionResults);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }
}
