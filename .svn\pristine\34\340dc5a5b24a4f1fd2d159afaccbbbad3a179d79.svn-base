﻿using ContinuityPatrol.Application.Features.CredentialProfile.Events.Create;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.CredentialProfile.Commands.Create;

public class
    CreateCredentialProfileCommandHandler : IRequestHandler<CreateCredentialProfileCommand,
        CreateCredentialProfileResponse>
{
    private readonly ICredentialProfileRepository _credentialProfileRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateCredentialProfileCommandHandler(IMapper mapper,
        ICredentialProfileRepository credentialProfileRepository, IPublisher publisher,
        ILoggedInUserService loggedInUserService)
    {
        _mapper = mapper;
        _publisher = publisher;
        _credentialProfileRepository = credentialProfileRepository;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<CreateCredentialProfileResponse> Handle(CreateCredentialProfileCommand request,
        CancellationToken cancellationToken)
    {
        request.CompanyId = _loggedInUserService.CompanyId;

        request.Properties = GetJsonProperties.PasswordEncryption(request.Properties);

        var credentialProfile = _mapper.Map<Domain.Entities.CredentialProfile>(request);

        credentialProfile = await _credentialProfileRepository.AddAsync(credentialProfile);

        var response = new CreateCredentialProfileResponse
        {
            Message = Message.Create(nameof(Domain.Entities.CredentialProfile), credentialProfile.Name),

            CredentialProfileId = credentialProfile.ReferenceId
        };

        await _publisher.Publish(new CredentialProfileCreatedEvent { CredentialProfileName = credentialProfile.Name },
            cancellationToken);

        return response;
    }
}