using ContinuityPatrol.Application.Features.DriftProfile.Commands.Create;
using ContinuityPatrol.Application.Features.DriftProfile.Commands.Update;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftProfileModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IDriftProfileService
{
    Task<List<DriftProfileListVm>> GetDriftProfileList();
    Task<BaseResponse> CreateAsync(CreateDriftProfileCommand createDriftProfileCommand);
    Task<BaseResponse> UpdateAsync(UpdateDriftProfileCommand updateDriftProfileCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<DriftProfileDetailVm> GetByReferenceId(string id);
    #region NameExist
 Task<bool> IsDriftProfileNameExist(string name, string? id);
   #endregion
    #region Paginated
 Task<PaginatedResult<DriftProfileListVm>> GetPaginatedDriftProfiles(GetDriftProfilePaginatedListQuery query);
    #endregion
}
