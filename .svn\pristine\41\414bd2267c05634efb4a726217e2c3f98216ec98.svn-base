﻿using ContinuityPatrol.Application.Features.WorkflowActionType.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionType.Events;

public class WorkflowActionTypeDeletedEventHandlerTests : IClassFixture<WorkflowActionTypeFixture>
{
    private readonly WorkflowActionTypeFixture _workflowActionTypeFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly WorkflowActionTypeDeletedEventHandler _handler;

    public WorkflowActionTypeDeletedEventHandlerTests(WorkflowActionTypeFixture workflowActionTypeFixture)
    {
        _workflowActionTypeFixture = workflowActionTypeFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockWorkflowActionTypeEventLogger = new Mock<ILogger<WorkflowActionTypeDeletedEventHandler>>();

        _mockUserActivityRepository = WorkflowActionTypeRepositoryMocks.CreateWorkflowActionTypeEventRepository(_workflowActionTypeFixture.UserActivities);

        _handler = new WorkflowActionTypeDeletedEventHandler(mockLoggedInUserService.Object, mockWorkflowActionTypeEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteWorkflowActionTypeEventDeleted()
    {
        _workflowActionTypeFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowActionTypeFixture.WorkflowActionTypeDeletedEvent, CancellationToken.None);

        result.Equals(_workflowActionTypeFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowActionTypeFixture.WorkflowActionTypeDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_DeleteWorkflowActionTypeEventDeleted()
    {
        _workflowActionTypeFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowActionTypeFixture.WorkflowActionTypeDeletedEvent, CancellationToken.None);

        result.Equals(_workflowActionTypeFixture.UserActivities[0].Id);

        result.Equals(_workflowActionTypeFixture.WorkflowActionTypeDeletedEvent.ActionType);

        await Task.CompletedTask;
    }
}