﻿namespace ContinuityPatrol.Application.Features.ComponentType.Commands.Update;

public class UpdateComponentTypeCommand : IRequest<UpdateComponentTypeResponse>
{
    public string Id { get; set; }
    public string ComponentName { get; set; }
    public string FormTypeId { get; set; }
    public string FormTypeName { get; set; }
    public string Properties { get; set; }
    public string ComponentProperties { get; set; }
    public string Logo { get; set; }
    public string Version { get; set; }
    public bool IsDatabase { get; set; }
    public bool IsReplication { get; set; }
    public bool IsServer { get; set; }
    public bool IsCustom { get; set; }

    public override string ToString()
    {
        return $"Name: {FormTypeName}; Id:{Id};";
    }
}