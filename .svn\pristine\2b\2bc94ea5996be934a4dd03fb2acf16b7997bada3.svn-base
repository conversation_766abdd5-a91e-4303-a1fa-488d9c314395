let isEdit = false
const nameExistUrl = "Configuration/Node/IsNodeNameExist";
var props = "";
let createPermission = $("#configurationCreate")?.data("create-permission")?.toLowerCase();
let deletePermission = $("#configurationDelete")?.data("delete-permission")?.toLowerCase();
let this1;
let btnDisable = false;

if (createPermission == "false") {
    $("#createBtn").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
}

$(function () {
    document.getElementById('nodeName').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            e.preventDefault();
        }
    });

    document.getElementById('search-inp').addEventListener('keypress', function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
    });

    let selectedValues = [];
    let dataTable = $('#datatablelist').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous" ></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "Sortable": true,
            "order": [],
            fixedColumns: {
                left: 1,
                right: 1
            },
            "ajax": {
                "type": "GET",
                "url": "/Configuration/Node/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        json.recordsTotal = json?.data?.totalPages;
                        json.recordsFiltered = json?.data?.totalCount;
                        if (json?.data?.data?.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        return json.data.data;
                    } else {
                        errorNotification(json)
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [1],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1
                        }
                        return data;
                    },
                    "orderable": false
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            let editedData = data ? data : "NA";
                            return `<span title='${editedData}'>${editedData} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "serverName", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            let editedData = data ? data : "NA";
                            return `<span title='${editedData}'>${editedData} </span>`;
                        }
                        return data;
                    }
                },
                {
                    data: 'properties', name: 'oracle SID', autoWidth: true,
                    render: function (data, type, row) {
                        let oracleSID = JSON.parse(data)?.OracleSID;
                        let editedOracleSID = oracleSID ? oracleSID : "NA";
                        if (type === 'display') {
                            return `<span title='${editedOracleSID}'>${editedOracleSID} </span>`;
                        }
                        return editedOracleSID;
                    }
                },
                {
                    "orderable": false, "width": '100px',
                    "render": function (data, type, row) {
                        if (createPermission == 'true' && deletePermission == "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="edit-button" data-node='${row.id}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="node-delete-button" data-node-id="${row.id}" data-node-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else if (createPermission == 'true' && deletePermission == "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class=" edit-button" data-node='${row.id}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="icon-disabled">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else if (createPermission == 'false' && deletePermission == "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete" class="node-delete-button" data-node-id="${row.id}" data-node-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else if (createPermission == 'false' && deletePermission == "false") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled">
                                    <i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }

                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart; // Get the start index of displayed data
                let counter = startIndex + index + 1; // Calculate the serial number based on start index and index
                $('td:eq(0)', row).html(counter); // Update the cell in the first column with the serial number
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('.form-select-sm').select2({
        "language": {
            "noResults": function () {
                return "No Results Found";
            }
        },
    });

    $('#search-inp').on('input', function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        } else {
            const nameCheckbox = $("#Name");
            const companyNameCheckbox = $("#CompanyName");
            const inputValue = $('#search-inp').val();
            if (nameCheckbox.is(':checked')) {
                selectedValues.push(nameCheckbox.val() + inputValue);
            }
            if (companyNameCheckbox.is(':checked')) {
                selectedValues.push(companyNameCheckbox.val() + inputValue);
            }
            dataTable.ajax.reload(function (json) {
                if (json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            })
        }
    });

    $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Configuration/Server/DatabaseServerNameList",
        dataType: "json",
        success: function (response) {
            if (response.success) {
                let nodeServer = $("#nodeServer");
                nodeServer.empty().append($('<option>').val('').text('Select server'));
                response.data.forEach(nodeServerList => {
                    $('<option>')
                        .val(nodeServerList.name)
                        .text(nodeServerList.name)
                        .attr('title', nodeServerList.serverType)
                        .attr('id', nodeServerList.id)
                        .appendTo(nodeServer);
                });
            } else {
                errorNotification(response);
            }
        }
    });

    $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Admin/FormMapping/NodeList",
        dataType: "json",
        success: function (result) {
            if (result.success) {
                let nodeType = $('#nodeType');
                nodeType.empty().append($('<option>').val('').text('Select node'));
                result.data.forEach((item) => {
                    nodeType.append($('<option>').val(item.formTypeId).text(item.formTypeName));
                });
            } else {
                errorNotification(result)
            }
        }
    });
})

$('#datatablelist').on('click', '.node-delete-button', function () {
    $("#deleteData").attr("title", $(this).data('node-name'));
    $('#deleteData').text($(this).data('node-name'));
    $('#textDeleteId').val($(this).data('node-id'));
});

$('#datatablelist').on('click', '.edit-button', function () {
    const errorElements = ['#Name-error', '#ServerName-error', '#NodeType-error'];
    clearInputFields('node-form', errorElements);
    isEdit = true;
    let nodeID = $(this).data("node");
    $.ajax({
        url: RootUrl + "Configuration/Node/GetByReferenceId",
        method: 'GET',
        dataType: 'json',
        data: { id: nodeID },
        success: function (result) {
            if (result.success) {
                populateModalFields(result?.data);
            } else {
                errorNotification(result)
            }
        }
    })
    $('#saveButton').text("Update");
    $('#saveButton').attr("Update");
    $('#CreateModal').modal('show');
});

async function validateName(value, id = null) {
    const errorElement = $('#Name-error');
    if (!value) {
        errorElement.text('Enter node name')
            .addClass('field-validation-error');
        return false;
    }
    let url = RootUrl + nameExistUrl;
    let data = {};
    data.nodeName = value;
    data.id = id;
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsNameExist(url, data, OnError)
    ];
    return await CommonValidation(errorElement, validationResults);
}
async function IsNameExist(url, data, errorFunc) {
    return !data.nodeName?.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}

function validationServerName() {
    $("#ServerId").val($('#nodeServer option:selected').attr('id'));
    serverNameNodeTypeValidation($(".infraComponentsServerName").val(), " Select ServerName", "ServerName-error");
};

function serverNameNodeTypeValidation(value, errorMessage, errorElement) {
    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}

function validationType() {
    let nodeType = $(".infraComponentsNodeType option:selected").text();
    $("#nodeTypeName").val(nodeType);
    serverNameNodeTypeValidation(nodeType, " Select NodeType", "NodeType-error");
};

const populateModalFields = (data) => {
    $('#nodeName').val(data.name);
    $("#nodeServer").val(data.serverName).trigger("change");
    $("#ServerId").val(data.serverId);
    $('#nodeId').val(data.id);
    $("#nodeTypeName").val(data.Type);
    $('#nodeType').val(data.typeId)
    $('#nodeFormRenderingArea').empty();
    setTimeout(() => {
        validateName(data.name, data.id, null);
        $('#nodeType').trigger("change");
    }, 150)
    props = data.properties
};

function populateDynamicFields(data) {
    let formData = JSON.parse(data);
    $('#nodeFormRenderingArea .formeo-render .f-field-group').each(function (index, element) {
        let elem = (element);
        let fieldName = $(element).find('input, select, textarea').attr('name');
        let fieldVal = $(element).find('input, select, textarea').attr('value');
        if (fieldName && formData.hasOwnProperty(fieldName) || fieldVal) {
            let value = formData[fieldName];
            if (typeof value === "boolean") {
                let checkbox = $(element).find('input[type="checkbox"]').attr("name")
                let chkValue = formData[checkbox]
                $(element).find('input[type="checkbox"]').prop('checked', chkValue);
                $(element).find('input[type="checkbox"]').trigger("change");
            }
            else if (fieldVal) {
                $(element).find('input[type="checkbox"]').prop('checked', chkValue).trigger("change");
            }
            else if (typeof value === "object") {
                if (value) {
                    const valuesArray = Object.values(value);
                    const containsObject = Array.isArray(valuesArray) && valuesArray.some(item => typeof item === 'object');
                    let labelsArray;
                    if (containsObject) {
                        labelsArray = valuesArray.map(item => item.value);
                    } else {
                        labelsArray = valuesArray.map(item => item);
                    }
                    const selectElement = $(element).find('select');
                    setTimeout(function () {
                        selectElement?.find('option').each(function () {
                            const optionValue = $(this).val();
                            if (labelsArray.includes(optionValue)) {
                                $(this).prop('selected', true);
                            }
                        });
                        selectElement.trigger('change');
                    }, 350)
                }
            }
            else {
                $(element).find('input, select, textarea').val(value).trigger("change");
                $(element).find('input, select, textarea').trigger("change");
                if (fieldName === "@@singlesignon_name") {
                    $("#f-new-select-id8rhdgry0").val(formData["@@signonprofile"]);
                    $("#f-new-select-id8rhdgry0").trigger("change");
                }
            }
        }
    });

    let encryption = $("input[type='password'][required]");
    encryption?.each(function () {
        let $this = $(this);
        let id = $this.attr("id");
        document.getElementById(id).addEventListener("focus", async function () {
            if ($this.is(':visible')) {
                let $thisName = $(this).attr('name');
                let $thisval = $(this).val();
                if ($thisval) {
                    let pwd = await nodeDecryptPassword($thisval);
                    $(`input[name=${$thisName}]`).val(pwd);
                }
            }
        });
        document.getElementById(id).addEventListener('blur', async function () {
            if ($this.is(':visible')) {
                let $thisName = $(this).attr('name');
                let $thisval = $(this).val();
                if ($thisval) {
                    let pwd = await nodeEncryptPassword($thisval);
                    $(`input[name=${$thisName}]`).val(pwd);
                }
            }
        });
    });
}

function commonSelectTagBinding(selectField, _options) {
    _options.forEach(option => {
        const optionElement = document.createElement("option");
        optionElement.value = option.value;
        optionElement.text = option.text;
        selectField.appendChild(optionElement);
    });
}

$("#createBtn").on("click", function () {
    isEdit = false;
    $('#nodeId').val('');
    $('#nodeName').val("")
    $('#nodeServer').val("");
    $('#nodeType').val("");
    $('#nodeFormRenderingArea').empty();
    props = "";
    $('#saveButton').text("Save");
    $('#saveButton').attr("Save");
    const errorElements = ['#Name-error', '#ServerName-error', '#NodeType-error'];
    clearInputFields('node-form', errorElements);
})

$("#nodeType").on("change", async function (e) {
    var nodeid = $(this).val();
    $.ajax({
        url: RootUrl + "Admin/FormMapping/GetFormMappingByFormId",
        method: 'GET',
        data: { "formTypeId": nodeid, "version": '' },
        dataType: 'json',
        success: async function (result) {
            if (result.success) {
                let form = result.data
                $('#nodeFormRenderingArea').empty();
                let parsedJsonData = JSON.parse(form.properties);
                var renderedForm = new FormeoRenderer({
                    renderContainer: document.querySelector("#nodeFormRenderingArea")
                });
                await renderedForm.render(parsedJsonData);
                await populateFormbuilderDynamicFields(parsedJsonData?.fields);
                setTimeout(() => {
                    //const inputElements = [
                    //    document.querySelector('input[name="SourceArchiveLogPath"]'),
                    //    document.querySelector('input[name="TargetArchiveLogPath"]')
                    //];

                    //inputElements?.forEach(inputElement => {
                    //    const fieldGroup = inputElement?.closest('.f-field-group');
                    //    if (fieldGroup) {
                    //        fieldGroup.classList.add('fieldGroupSourcePath'); style.css - 945.                           
                    //    }
                    //});                                   

                    let selectElements = document.querySelectorAll('.form-select-modal-dynamic');
                    selectElements.forEach(async function (selectElement) {
                        let $this = $(selectElement);
                        $this.select2({
                            dropdownParent: this1.find('.modal-content'),
                            placeholder: $this.attr('placeholder')
                        });
                    });
                    $('.form-select-modal-dynamic').next('.select2-container').css('width', '100%');
                    setTimeout(() => {
                        let disableSelectTagTitle = document.querySelectorAll('.select2-selection__rendered');
                        disableSelectTagTitle?.forEach(async function (selectElement) {
                            let $this = $(selectElement);
                            $this.attr('title', '');
                        });
                    }, 200);
                    onChangeFormBuilderValidation('node');//onChangeNodeReplicationFormBuilderValidation();
                }, 200);

                ///onsetconditionals
                for (const key in parsedJsonData.fields) {
                    if (parsedJsonData.fields.hasOwnProperty(key)) {
                        const field = parsedJsonData.fields[key];
                        setFieldAttrValues(field);
                    }
                }

                $('#nodeFormRenderingArea').on('change input', '.formeo-render .f-field-group input , .formeo-render .f-field-group select', function (event) {
                    var selectedValue = event.target.value;
                    var selectedid = event.target.id;
                    var typ = event.target.type;

                    if (typ === "radio" || typ === "checkbox") {

                        // Loop through all fields and their conditions id radio
                        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                            var field = parsedJsonData.fields[fieldId];
                            if (field.conditions && field.conditions.length > 0) {
                                var isVisible = false;
                                field.conditions.forEach(function (condition) {
                                    var isMatchingCondition = condition.if.some(function (ifClause) {
                                        sourceField = parsedJsonData.fields[ifClause.source.substring(7)];
                                        return ifClause.target === selectedValue;
                                    });
                                    if (isMatchingCondition) {
                                        isVisible = true;
                                    }
                                });
                                field.conditions.forEach(function (condition) {
                                    condition.then.forEach(function (thenClause) {
                                        condition.if.forEach(function (ifClause) {
                                            var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                            var targetElementChk = document.getElementById(`f-${thenClause.target.substring(7)}-0`);
                                            var srcElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                            if (targetElement) {
                                                if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                                                    if (isVisible) {
                                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                    }
                                                    if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                        if (targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) return false;
                                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.add("d-none");
                                                        let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                                        let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                                        if (textField?.length > 0) {
                                                            removeValidationWhenUncheck(textField);
                                                        }
                                                        if (selectField?.length > 0) {
                                                            removeValidationWhenUncheck(selectField);
                                                        }
                                                    }
                                                }
                                                else if (ifClause.comparison === "notEquals") {

                                                    if (targetElement && event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                        let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                                        let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                                        if (textField?.length > 0) {
                                                            removeValidationWhenUncheck(textField);
                                                        }
                                                        if (selectField?.length > 0) {
                                                            removeValidationWhenUncheck(selectField);
                                                        }
                                                    }
                                                    else if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                                                    }
                                                }
                                            }
                                            else if (targetElementChk) {
                                                if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                                                    if (isVisible) {
                                                        targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                    }
                                                    if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                        if (targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) return false;
                                                        targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.add("d-none");
                                                        let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                                        let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                                        if (textField?.length > 0) {
                                                            removeValidationWhenUncheck(textField);
                                                        }
                                                        if (selectField?.length > 0) {
                                                            removeValidationWhenUncheck(selectField);
                                                        }
                                                    }
                                                }
                                                else if (ifClause.comparison === "notEquals") {

                                                    if (targetElementChk && event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                        targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                        let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                                        let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                                        if (textField?.length > 0) {
                                                            removeValidationWhenUncheck(textField);
                                                        }
                                                        if (selectField?.length > 0) {
                                                            removeValidationWhenUncheck(selectField);
                                                        }
                                                    }
                                                    else if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                        targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                                                    }
                                                }
                                            }
                                        });
                                    });
                                });
                            }
                        });
                    };
                    if (typ === "select-one") {
                        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                            var field = parsedJsonData.fields[fieldId];
                            if (field.conditions && field.conditions.length > 0) {
                                var isMatchingCondition = field.conditions.some(function (condition) {
                                    return condition.if.some(function (ifClause) {
                                        if (ifClause.source === `fields.${fieldId}` && ifClause.target === selectedValue) {
                                            return true;
                                        }
                                    });
                                });
                                if (isMatchingCondition) {
                                    field.conditions.forEach(function (condition) {
                                        condition.then.forEach(function (thenClause) {
                                            condition.if.forEach(function (ifClause) {
                                                var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                if (targetElement && thenClause.targetProperty === 'isVisible') {
                                                    if (ifClause.target === selectedValue && thenClause.assignment === 'equals' && targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none")) {
                                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                        correctElementId = targetElement.id
                                                    } else if (ifClause.target !== selectedValue) {
                                                        targetElement.value = ""
                                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                        let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                                        let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                                        if (textField?.length > 0) {
                                                            removeValidationWhenUncheck(textField);
                                                        }
                                                        if (selectField?.length > 0) {
                                                            removeValidationWhenUncheck(selectField);
                                                        }
                                                    }
                                                }
                                            });
                                        });
                                    });
                                } else {
                                    Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                        var field = parsedJsonData.fields[fieldId];
                                        if (field.conditions && field.conditions.length > 0) {
                                            field.conditions.forEach(function (condition) {
                                                condition.then.forEach(function (thenClause) {
                                                    condition.if.forEach(function (ifClause) {
                                                        var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                        var sourceElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                                        var sourceName = document.getElementsByName(`f-${ifClause.source.substring(7)}`);
                                                        if (targetElement && selectedValue !== ifClause.target && ifClause.comparison !== 'notEquals' && (selectedid === sourceElement || selectedid == sourceName)) {
                                                            targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                            let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                                            let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                                            if (textField?.length > 0) {
                                                                removeValidationWhenUncheck(textField);
                                                            }
                                                            if (selectField?.length > 0) {
                                                                removeValidationWhenUncheck(selectField);
                                                            }
                                                        }
                                                    });
                                                });
                                            });
                                        }
                                    });
                                }
                            }
                        });
                    }
                });

                //  initalstate;
                Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                    var field = parsedJsonData.fields[fieldId];
                    if (field.conditions && field.conditions.length > 0) {
                        field.conditions.forEach(function (condition) {
                            condition.if.forEach(function (ifClause) {
                                condition.then.forEach(function (thenClause) {
                                    if ((thenClause.targetProperty === 'isVisible') && thenClause.assignment === 'equals' && ifClause.comparison !== "notEquals") {
                                        var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);

                                        if (targetElement && !targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none") && !targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) {
                                            targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                            let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                            let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                            if (textField?.length > 0) {
                                                removeValidationWhenUncheck(textField);
                                            }
                                            if (selectField?.length > 0) {
                                                removeValidationWhenUncheck(selectField);
                                            }
                                        }
                                    }
                                });
                            });
                        });
                    }
                });
                if (isEdit && props) {
                    setTimeout(() => {
                        populateDynamicFields(props);
                    }, 250)
                }
            }
            else {
                errorNotification(result);
            }
        }
    });

    const nodeFormRenderingArea = document.getElementById('nodeFormRenderingArea');
    let pp;
    if (props) {
        pp = JSON.parse(props)
    }
    const inputFields = nodeFormRenderingArea.querySelectorAll('input, select, textarea');
    inputFields.forEach((inputField) => {
        const fieldName = inputField.name; // Assuming 'name' attribute is set for form fields
        if (pp && pp.hasOwnProperty(fieldName)) {
            inputField.value = pp[fieldName];
        }
    });
});

function removeValidationWhenUncheck(targets) {
    targets.forEach(function (target) {
        target.parentNode.removeChild(target);
    });
}

async function saveFormFields() {
    let formData = {};
    let promises = [];
    $('#nodeFormRenderingArea .formeo-render .f-field-group').each(async function (index, element) {
        let $this = $(this);
        if ($this.is(':visible')) {
            let fieldName = $(element).find('input, select, textarea').attr('name');
            let fieldVal = $(element).find('input').attr('value');
            let fieldType = $(element).find('input, select, textarea').attr('type');
            let value;

            if (fieldType === "date") {
                value = $(element).find('input[type="date"]').val();
                formData[fieldName] = value;
            }

            if (fieldType === 'checkbox') {
                value = $(element).find('input[type="checkbox"]').prop('checked');
            } else {
                value = $(element).find('input, select, textarea').val();
            }

            // Save field name as key and field value as value in formData object
            if (fieldType === "checkbox") {
                formData[fieldVal] = value;
            }
            if (fieldType === "password" && (value && value !== "") && value.length < 64) {
                promises.push(nodeEncryptPassword(value).then(encryptedPassword => {
                    formData[fieldName] = encryptedPassword;
                }));
            }
            else {
                formData[fieldName] = value;
            }
        }
    });
    await Promise.all(promises);
    let formDataJson = JSON.stringify(formData);
    let hiddenInput = document.getElementById('Props');
    if (hiddenInput) {
        hiddenInput.value = formDataJson;
    }
    return formData;
}

const nodeEncryptPassword = async (text) => {

    const generateKey = async () => {
        const key = await window.crypto.subtle.generateKey({
            name: 'AES-GCM',
            length: 256
        },
            true, [
            'encrypt',
            'decrypt'
        ]);
        const exportedKey = await window.crypto.subtle.exportKey(
            'raw',
            key,
        );
        return bufferToBase64(exportedKey);
    }

    // arrayBuffer to base64
    const bufferToBase64 = (arrayBuffer) => {
        return window.btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
    }

    // load a base64 encoded key
    const loadKey = async (base64Key) => {
        return await window.crypto.subtle.importKey(
            'raw',
            base64ToBuffer(base64Key),
            "AES-GCM",
            true, [
            "encrypt",
            "decrypt"
        ]
        );
    }

    // base64 to arrayBuffer
    const base64ToBuffer = (base64) => {
        const binary_string = window.atob(base64);
        const len = binary_string.length;
        let bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binary_string.charCodeAt(i);
        }
        return bytes.buffer;
    }

    const cryptGcm = async (base64Key, bytes) => {
        const key = await loadKey(base64Key);
        const iv = window.crypto.getRandomValues(new Uint8Array(12));
        const algorithm = {
            iv,
            name: 'AES-GCM'
        };
        const cipherData = await window.crypto.subtle.encrypt(
            algorithm,
            key,
            bytes
        );

        // prepend the random IV bytes to raw cipherdata
        const cipherText = concatArrayBuffers(iv.buffer, cipherData);
        return bufferToBase64(cipherText);
    }

    // concatenate two array buffers
    const concatArrayBuffers = (buffer1, buffer2) => {
        let tmp = new Uint8Array(buffer1.byteLength + buffer2.byteLength);
        tmp.set(new Uint8Array(buffer1), 0);
        tmp.set(new Uint8Array(buffer2), buffer1.byteLength);
        return tmp.buffer;
    }

    const plaintext = text;
    const plaintextBytes = (new TextEncoder()).encode(plaintext, 'utf-8');
    const encryptionKey = await generateKey();
    const ciphertext = await cryptGcm(encryptionKey, plaintextBytes);
    // console.log("plaintext: ", plaintext);
    // console.log("encryptionKey (base64):", encryptionKey);
    // console.log("ciphertext (base64):", ciphertext);

    if (encryptionKey && ciphertext) {
        return encryptionKey + "$" + ciphertext;
    }
}

const nodeDecryptPassword = async (encryptedText) => {
    const [encryptionKeyBase64, ciphertextBase64] = encryptedText.split("$");

    // Load the encryption key
    const loadKey = async (base64Key) => {
        return await window.crypto.subtle.importKey(
            'raw',
            base64ToBuffer(base64Key),
            "AES-GCM",
            true, [
            "encrypt",
            "decrypt"
        ]
        );
    }

    // base64 to arrayBuffer
    const base64ToBuffer = (base64) => {
        const binary_string = window.atob(base64);
        const len = binary_string.length;
        let bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binary_string.charCodeAt(i);
        }
        return bytes.buffer;
    }

    const key = await loadKey(encryptionKeyBase64);
    const ciphertextBuffer = base64ToBuffer(ciphertextBase64);
    const iv = ciphertextBuffer.slice(0, 12);
    const encryptedData = ciphertextBuffer.slice(12);

    const algorithm = {
        iv,
        name: 'AES-GCM'
    };

    const decryptedData = await window.crypto.subtle.decrypt(algorithm, key, encryptedData);
    const decryptedText = new TextDecoder().decode(decryptedData);
    return decryptedText;
}


$("#saveButton").on("click", async function () {
    let NodeName = await validateName($("#nodeName").val(), $('#nodeId').val(), IsNameExist);
    let validateServerName = serverNameNodeTypeValidation($(".infraComponentsServerName").val(), " Select server", "ServerName-error");
    let validateType = serverNameNodeTypeValidation($(".infraComponentsNodeType option:selected").val(), " Select node type", "NodeType-error");
    let res = await inputFormValidation('node');
    if (NodeName && validateServerName && validateType && res) {
        let fd = await saveFormFields();
        let formDataJson = JSON.stringify(fd);
        const keys = Object.keys(fd);
        keys.forEach(key => {
            if (key.startsWith('f-')) {
                delete fd[key];
            }
        });
        if (!btnDisable) {
            btnDisable = true;
            let hiddenInput = document.getElementById('Props');
            hiddenInput.value = formDataJson;
            $("#node-form").trigger('submit');
        }
    }
});

$('.modal').on('shown.bs.modal', function async() {
    this1 = $(this);
});

$('.modal').on('hidden.bs.modal', function () {
    isEdit = false;
});

$('#nodeName').on('keydown, input', function () {
    const value = $(this).val();
    let sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    validateName(value, $('#nodeId').val(), IsNameExist);
});
