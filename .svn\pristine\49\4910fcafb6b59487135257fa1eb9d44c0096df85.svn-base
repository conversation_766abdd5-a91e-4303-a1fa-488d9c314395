﻿using ContinuityPatrol.Application.Features.SiteType.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.SiteType.Validators;

public class CreateSiteTypeValidatorTests
{
    private readonly Mock<ISiteTypeRepository> _mockSiteTypeRepository;
    private readonly Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public CreateSiteTypeValidatorTests()
    {
        var siteTypes = new Fixture().Create<List<Domain.Entities.SiteType>>();
        var licenseManagers = new Fixture().Create<List<Domain.Entities.LicenseManager>>();

        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockSiteTypeRepository = SiteTypeRepositoryMocks.CreateSiteTypeRepository(siteTypes);
        _mockLicenseManagerRepository = LicenseManagerRepositoryMocks.CreateLicenseManagerRepository(licenseManagers);
    }

    //Type

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_Type_InSiteType_WithEmpty(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = "";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_Type_InSiteType_IsNull(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = null;

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeNotEmpty, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_Type_InSiteType_MinimumRange(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = "CV";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeRangeRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_Type_InSiteType_MaximumRange(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOP";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_Type_InSiteType_Valid(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = "  PQRS  ";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeValidNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_ValidType_InSiteType_SingleSpace_InFront(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = " PQRS";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeValidNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_ValidType_InSiteType_SingleSpace_InBack(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = "PQRS ";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeValidNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_ValidType_InSiteType_DoubleSpace_InBetween(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = "PTS  Site";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeValidNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_ValidType_InSiteType_SpecialCharacters_InFront(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = "#@@$PTS Site";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeValidNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_ValidType_InSiteType_SpecialCharacters_InBack(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = "PTS Site#%$#";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeValidNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_ValidType_InSiteType_SpecialCharacters_Only(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = "&*^Y%$#*%";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeValidNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_ValidType_InSiteType__With_UnderScore_InFront(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = "_PTSSite";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeValidNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_ValidType_InSiteType__With_UnderScore_InBack(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = "PTSSite_";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeValidNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_ValidType_InSiteType__With_Numbers_Only(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Type = "0498487484435";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeValidNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]
    public async Task Verify_Create_ValidType_InSiteType__With_Numbers_InFront(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);
        createSiteTypeCommand.Type = "1324PTSSite";
        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeValidNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    //Icon

    [Theory]
    [AutoSiteTypeData]

    public async Task Verify_Create_Icon_InSiteType_WithEmpty(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Icon = "";

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.SiteType.SiteTypeValidRequired, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoSiteTypeData]

    public async Task Create_Icon_InSiteType_IsNull(CreateSiteTypeCommand createSiteTypeCommand)
    {
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var validator = new CreateSiteTypeCommandValidator(_mockSiteTypeRepository.Object, _mockLicenseManagerRepository.Object, mockLoggedInUserService.Object);

        createSiteTypeCommand.Icon = null;

        var validateResult = await validator.ValidateAsync(createSiteTypeCommand, CancellationToken.None);

        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Icon is required.");
    }
}