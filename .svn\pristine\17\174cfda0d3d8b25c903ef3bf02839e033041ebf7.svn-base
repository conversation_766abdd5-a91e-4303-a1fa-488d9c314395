using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ApprovalMatrixFixture : IDisposable
{
    public List<ApprovalMatrix> ApprovalMatrixPaginationList { get; set; }
    public List<ApprovalMatrix> ApprovalMatrixList { get; set; }
    public ApprovalMatrix ApprovalMatrixDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ApprovalMatrixFixture()
    {
        var fixture = new Fixture();

        ApprovalMatrixList = fixture.Create<List<ApprovalMatrix>>();

        ApprovalMatrixPaginationList = fixture.CreateMany<ApprovalMatrix>(20).ToList();

       
        ApprovalMatrixPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        ApprovalMatrixPaginationList.ForEach(x => x.IsActive = true);

      
        ApprovalMatrixList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        ApprovalMatrixList.ForEach(x => x.IsActive = true);

        ApprovalMatrixDto = fixture.Create<ApprovalMatrix>();

        ApprovalMatrixDto.ReferenceId = Guid.NewGuid().ToString();
        ApprovalMatrixDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
