﻿using ContinuityPatrol.Application.Features.ServerSubType.Queries.GetByServerTypeId;

namespace ContinuityPatrol.Application.UnitTests.Features.ServerSubType.Queries
{
    public class GetServerSubTypeByServerTypeIdQueryHandlerTests
    {
        private readonly Mock<IServerSubTypeRepository> _mockServerSubTypeRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetServerSubTypeByServerTypeIdQueryHandler _handler;

        public GetServerSubTypeByServerTypeIdQueryHandlerTests()
        {
            _mockServerSubTypeRepository = new Mock<IServerSubTypeRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetServerSubTypeByServerTypeIdQueryHandler(_mockMapper.Object, _mockServerSubTypeRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedResult_WhenServerSubTypesExist()
        {
            var request = new GetServerSubTypeByServerTypeIdQuery
            {
                ServerSubTypeId = Guid.NewGuid().ToString(),
            };

            var serverSubTypes = new List<Domain.Entities.ServerSubType>
            {
                new Domain.Entities.ServerSubType { Id = 1, Name = "SubType1" },
                new Domain.Entities.ServerSubType { Id = 2, Name = "SubType2" }
            };

            var serverSubTypeVms = new List<GetServerSubTypeByServerTypeIdVm>
            {
                new GetServerSubTypeByServerTypeIdVm { Id = Guid.NewGuid().ToString(), Name = "SubType1" },
                new GetServerSubTypeByServerTypeIdVm { Id = Guid.NewGuid().ToString(), Name = "SubType2" }
            };

            _mockServerSubTypeRepository
                .Setup(repo => repo.GetServerSubTypeByServerType(request.ServerSubTypeId))
                .ReturnsAsync(serverSubTypes);

            _mockMapper
                .Setup(mapper => mapper.Map<List<GetServerSubTypeByServerTypeIdVm>>(serverSubTypes))
                .Returns(serverSubTypeVms);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(serverSubTypeVms.Count, result.Count);
            _mockServerSubTypeRepository.Verify(repo => repo.GetServerSubTypeByServerType(request.ServerSubTypeId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<GetServerSubTypeByServerTypeIdVm>>(serverSubTypes), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoServerSubTypesExist()
        {
            var request = new GetServerSubTypeByServerTypeIdQuery
            {
                ServerSubTypeId = Guid.NewGuid().ToString()
            };

            _mockServerSubTypeRepository
                .Setup(repo => repo.GetServerSubTypeByServerType(request.ServerSubTypeId))
                .ReturnsAsync(new List<Domain.Entities.ServerSubType>());

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);
            _mockServerSubTypeRepository.Verify(repo => repo.GetServerSubTypeByServerType(request.ServerSubTypeId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<GetServerSubTypeByServerTypeIdVm>>(It.IsAny<List<Domain.Entities.ServerSubType>>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowException_WhenRepositoryThrows()
        {
            var request = new GetServerSubTypeByServerTypeIdQuery
            {
                ServerSubTypeId = Guid.NewGuid().ToString(),
            };

            _mockServerSubTypeRepository
                .Setup(repo => repo.GetServerSubTypeByServerType(request.ServerSubTypeId))
                .ThrowsAsync(new Exception("Database error"));

            var exception = await Assert.ThrowsAsync<Exception>(() =>
                _handler.Handle(request, CancellationToken.None));

            Assert.Equal("Database error", exception.Message);
            _mockServerSubTypeRepository.Verify(repo => repo.GetServerSubTypeByServerType(request.ServerSubTypeId), Times.Once);
        }
    }
}
