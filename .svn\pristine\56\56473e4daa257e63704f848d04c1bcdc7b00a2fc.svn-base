﻿using AutoMapper;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetResilienceHealthStatus;
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateState;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectDetailById;
using ContinuityPatrol.Application.Features.Job.Commands.RescheduleJob;
using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;
using ContinuityPatrol.Domain.ViewModels.InfraSummaryModel;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Dashboard.Controllers;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Dashboard.Controllers
{
    public class ITResiliencyViewControllerTests
    {
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IPublisher> _Mockpublisher = new();
        private readonly Mock<ILogger<ITResiliencyViewController>> _mockLogger = new();
        private ITResiliencyViewController _controller;

        public ITResiliencyViewControllerTests()
        {
            Initialiaze();
        }
        public void Initialiaze()
        {
            _controller = new ITResiliencyViewController
                (_mockDataProvider.Object,
                _mockMapper.Object,
                _mockLogger.Object,
                _Mockpublisher.Object );
        }

        [Fact]
        public async Task GetITViewByInfraObjectId_ReturnsJsonResult_WithData_WhenInfraObjectIdIsProvided()
        {
            var infraObjectId = "testInfraObjectId";
            var mockInfra = new ItViewByInfraObjectIdVm();

            _mockDataProvider.Setup(p => p.DashboardView.GetITViewByInfraObjectId(infraObjectId))
                             .ReturnsAsync(mockInfra);

            var result = await _controller.GetITViewByInfraObjectId(infraObjectId) as JsonResult;
            var data = result?.Value as dynamic;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetITViewByInfraObjectId_ReturnsError_WhenInfraObjectIdIsEmpty()
        {
            var infraObjectId = "";

            var result = await _controller.GetITViewByInfraObjectId(infraObjectId) as JsonResult;

            Assert.NotNull(result);
            Assert.Equal("InfraObject is Not valid format", result?.Value);
        }

        [Fact]
        public async Task GetItViewByBusinessServiceId_ReturnsJsonResult_WithData_WhenBusinessServiceIdIsProvided()
        {
            var businessServiceId = "testBusinessServiceId";
            var mockBusinessService = new List<ItViewByBusinessServiceIdVm>();

            _mockDataProvider.Setup(p => p.DashboardView.GetItViewByBusinessServiceId(businessServiceId))
                             .ReturnsAsync(mockBusinessService);

            var result = await _controller.GetItViewByBusinessServiceId(businessServiceId) as JsonResult;
            var data = result?.Value as dynamic;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetITViewLogByInfraObjectId_ReturnsJsonResult_WithData_WhenInfraObjectIdIsProvided()
        {
            var infraObjectId = "testInfraObjectId";
            var mockInfraLog = new ResilienceHealthStatusDetailVm();

            _mockDataProvider.Setup(p => p.DashboardView.GetResilienceHealthStatusByInfraObjectId(infraObjectId))
                             .ReturnsAsync(mockInfraLog);

            var result = await _controller.GetITViewLogByInfraObjectId(infraObjectId) as JsonResult;
            var data = result?.Value as dynamic;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetBusinessServiceList_ReturnsJsonResult_WithData()
        {
            var mockItViewList = new List<GetItViewListVm>();

            _mockDataProvider.Setup(p => p.DashboardView.GetItViewList())
                             .ReturnsAsync(mockItViewList);

            var result = await _controller.GetBusinessServiceList() as JsonResult;
            var data = result?.Value as dynamic;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetAllInfraSummaries_ReturnsJsonResult_WithData()
        {
            var mockSummaries = new List<InfraSummaryListVm>();

            _mockDataProvider.Setup(p => p.InfraSummary.GetInfraSummaries())
                             .ReturnsAsync(mockSummaries);

            var result = await _controller.GetAllInfraSummaries() as JsonResult;
            var data = result?.Value as dynamic;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task UpdateInfraObjectState_ReturnsJsonResult_WithSuccess_WhenStateIsUpdated()
        {
            var mockUpdateCommand = new UpdateInfraObjectStateCommand();
            var mockUpdateResponse = new UpdateInfraObjectStateResponse { Success = true };

            _mockDataProvider.Setup(p => p.InfraObject.UpdateInfraObjectState(mockUpdateCommand))
                             .ReturnsAsync(mockUpdateResponse);

            var result = await _controller.UpdateInfraObjectState(mockUpdateCommand) as JsonResult;
            var data = result?.Value as dynamic;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task RescheduleJob_ReturnsJsonResult_WithSuccess_WhenJobIsRescheduled()
        {
            var mockRescheduleCommand = new RescheduleJobCommand();
            var mockRescheduleResponse = new RescheduleJobResponse { Success = true };

            _mockDataProvider.Setup(p => p.JobService.RescheduleJob(mockRescheduleCommand))
                             .ReturnsAsync(mockRescheduleResponse);

            var result = await _controller.RescheduleJob(mockRescheduleCommand) as JsonResult;
            var data = result?.Value as dynamic;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetInfraObjectDetailsById_ReturnsJsonResult_WithData_WhenInfraObjectIdIsProvided()
        {
            var infraObjectId = "testInfraObjectId";
            var mockInfraObjectDetails = new GetInfraObjectDetailByIdVm();

            _mockDataProvider.Setup(p => p.InfraObject.GetInfraObjectDetailsById(infraObjectId))
                             .ReturnsAsync(mockInfraObjectDetails);

            var result = await _controller.GetInfraObjectDetailsById(infraObjectId) as JsonResult;
            var data = result?.Value as dynamic;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetInfraObjectDetailsById_ReturnsError_WhenInfraObjectIdIsEmpty()
        {
            var infraObjectId = "";

            var result = await _controller.GetInfraObjectDetailsById(infraObjectId) as JsonResult;

            Assert.NotNull(result);
            Assert.Equal("InfraObject is Not valid format", result?.Value);
        }
    }
}

