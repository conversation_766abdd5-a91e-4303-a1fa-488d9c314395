﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories
{
    public class DataSyncJobRepository:BaseRepository<DataSyncJob>, IDataSyncJobRepository
    {
        private readonly ILoggedInUserService _loggedInUserService;
        private readonly ApplicationDbContext _dbContext;

        public DataSyncJobRepository(ILoggedInUserService loggedInUserService,ApplicationDbContext dbContext):base(dbContext)
        {
            _dbContext = dbContext;
            _loggedInUserService = loggedInUserService;
        }
     
    }
}
