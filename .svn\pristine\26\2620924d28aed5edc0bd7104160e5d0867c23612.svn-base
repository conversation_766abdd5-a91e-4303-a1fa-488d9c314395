﻿using ContinuityPatrol.Application.Features.DataSet.Event.Update;

namespace ContinuityPatrol.Application.Features.DataSet.Commands.Update;

public class UpdateDataSetCommandHandler : IRequestHandler<UpdateDataSetCommand, UpdateDataSetResponse>
{
    private readonly IDataSetRepository _dataSetRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateDataSetCommandHandler(IMapper mapper, IDataSetRepository dataSetRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _dataSetRepository = dataSetRepository;
        _publisher = publisher;
    }

    public async Task<UpdateDataSetResponse> Handle(UpdateDataSetCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _dataSetRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.DataSet), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateDataSetCommand), typeof(Domain.Entities.DataSet));

        await _dataSetRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateDataSetResponse
        {
            Message = Message.Update("Dataset", eventToUpdate.DataSetName),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new DataSetUpdatedEvent { DatasetName = eventToUpdate.DataSetName },
            cancellationToken);

        return response;
    }
}