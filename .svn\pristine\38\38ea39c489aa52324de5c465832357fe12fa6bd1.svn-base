﻿namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class AlertReceiverController : BaseController
{
    private readonly ILogger<AlertReceiverController> _logger;

    public AlertReceiverController(ILogger<AlertReceiverController> logger)
    {
        _logger = logger;
    }
    public IActionResult List()
    {
        _logger.LogInformation("Notification Manager Viewed");

        return View();
    }
}