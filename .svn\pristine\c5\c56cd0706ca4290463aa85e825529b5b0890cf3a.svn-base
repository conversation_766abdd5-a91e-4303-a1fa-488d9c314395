﻿using ContinuityPatrol.Domain.ViewModels.TableAccessModel;

namespace ContinuityPatrol.Application.Features.TableAccess.Queries.GetList;

public class GetTableAccessListQueryHandler : IRequestHandler<GetTableAccessListQuery, List<TableAccessListVm>>
{
    private readonly IConfiguration _config;
    private readonly IMapper _mapper;
    private readonly ITableAccessRepository _tableAccessRepository;

    public GetTableAccessListQueryHandler(IMapper mapper, IConfiguration config,
        ITableAccessRepository tableAccessRepository)
    {
        _mapper = mapper;
        _config = config;
        _tableAccessRepository = tableAccessRepository;
    }

    public async Task<List<TableAccessListVm>> Handle(GetTableAccessListQuery request,
        CancellationToken cancellationToken)
    {
        var config = _config.GetConnectionString("Default");

        var dbProvider = _config.GetConnectionString("DBProvider");

        var decryptString = CryptographyHelper.Decrypt(config);

        var dbProviderString = CryptographyHelper.Decrypt(dbProvider);

        var eventToTableAccess = await _tableAccessRepository.GetTableAccessListAsync();

        var schema = GetDatabaseNameFromConnectionString(decryptString, dbProviderString);

        var tableAccessSchema = await _tableAccessRepository.GetSchemas(schema, dbProviderString);

        var tableNames = tableAccessSchema.Select(x => x.TableName).ToList();

        var existingTableAccesses = await _tableAccessRepository.GetTableAccessByTableNames(tableNames);

        var notUsedTableAccesses = await _tableAccessRepository.GetUnUsedTableAccessByTableNames(tableNames);

        notUsedTableAccesses.ForEach(x => x.IsActive = false);

        await _tableAccessRepository.UpdateRangeAsync(notUsedTableAccesses);

        var existingTableAccessDict = existingTableAccesses.ToDictionary(x => x.TableName);

        var newTableAccesses = tableAccessSchema
            .Where(ta => !existingTableAccessDict.ContainsKey(ta.TableName))
            .Select(ta => new Domain.Entities.TableAccess
            {
                SchemaName = schema,
                TableName = ta.TableName,
                IsChecked = false              
            })
            .ToList();

      
        var updatedTableAccesses = existingTableAccesses
            .Select(ta =>
            {
                ta.SchemaName = schema;
                return ta;
            })
            .ToList();

       
        if (newTableAccesses.Any())
            await _tableAccessRepository.AddRangeAsync(newTableAccesses);

        if (updatedTableAccesses.Any())
            await _tableAccessRepository.UpdateRangeAsync(updatedTableAccesses);
       
        return eventToTableAccess;            

    }

    private string GetDatabaseNameFromConnectionString(string connectionString, string provider)
    {
        DbConnectionStringBuilder builder = provider.ToLower() switch
        {
            "mysql" => new MySqlConnectionStringBuilder(connectionString),
            "oracle" => new OracleConnectionStringBuilder(connectionString),
            "mssql" => new SqlConnectionStringBuilder(connectionString),
            "npgsql" => new NpgsqlConnectionStringBuilder(connectionString),
            _ => throw new ArgumentException("Unsupported provider name.")
        };

        if (builder.TryGetValue("Database", out var databaseName))
        {
            return databaseName.ToString();
        }

        builder.TryGetValue("User Id", out var dbName);
        return dbName.ToString().ToUpper();

        throw new ArgumentException("Unable to extract database name from connection string.");
    }
}

