using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public static class ApprovalMatrixRequestRepositoryMocks
{
    public static Mock<IApprovalMatrixRequestRepository> CreateApprovalMatrixRequestRepository(List<ApprovalMatrixRequest> approvalMatrixRequests)
    {
        var mockApprovalMatrixRequestRepository = new Mock<IApprovalMatrixRequestRepository>();

        mockApprovalMatrixRequestRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(approvalMatrixRequests);

        mockApprovalMatrixRequestRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => approvalMatrixRequests.FirstOrDefault(x => x.ReferenceId == id));

        mockApprovalMatrixRequestRepository.Setup(repo => repo.GetByRequestId(It.IsAny<string>()))
            .ReturnsAsync((string requestId) => approvalMatrixRequests.FirstOrDefault(x => x.RequestId == requestId));

        mockApprovalMatrixRequestRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string name, string id) => 
                approvalMatrixRequests.Any(x => x.ProcessName == name && x.ReferenceId != id && x.IsActive));

        mockApprovalMatrixRequestRepository.Setup(repo => repo.AddAsync(It.IsAny<ApprovalMatrixRequest>()))
            .ReturnsAsync((ApprovalMatrixRequest approvalMatrixRequest) =>
            {
                approvalMatrixRequest.ReferenceId = Guid.NewGuid().ToString();
                approvalMatrixRequest.Id = approvalMatrixRequests.Count + 1;
                approvalMatrixRequests.Add(approvalMatrixRequest);
                return approvalMatrixRequest;
            });

        mockApprovalMatrixRequestRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ApprovalMatrixRequest>()))
            .Returns((ApprovalMatrixRequest approvalMatrixRequest) =>
            {
                var existingRequest = approvalMatrixRequests.FirstOrDefault(x => x.ReferenceId == approvalMatrixRequest.ReferenceId);
                if (existingRequest != null)
                {
                    existingRequest.ProcessName = approvalMatrixRequest.ProcessName;
                    existingRequest.Description = approvalMatrixRequest.Description;
                    existingRequest.UserName = approvalMatrixRequest.UserName;
                    existingRequest.Status = approvalMatrixRequest.Status;
                    existingRequest.Approvers = approvalMatrixRequest.Approvers;
                    existingRequest.Message = approvalMatrixRequest.Message;
                    existingRequest.StartDateTime = approvalMatrixRequest.StartDateTime;
                    existingRequest.EndDateTime = approvalMatrixRequest.EndDateTime;
                    existingRequest.IsRequest = approvalMatrixRequest.IsRequest;
                    existingRequest.IsActive = approvalMatrixRequest.IsActive;
                }
                return Task.CompletedTask;
            });

        mockApprovalMatrixRequestRepository.Setup(repo => repo.DeleteAsync(It.IsAny<ApprovalMatrixRequest>()))
            .Returns((ApprovalMatrixRequest approvalMatrixRequest) =>
            {
                approvalMatrixRequests.Remove(approvalMatrixRequest);
                return Task.CompletedTask;
            });

        //mockApprovalMatrixRequestRepository.Setup(repo => repo.PaginatedListAllAsync(
        //    It.IsAny<int>(), It.IsAny<int>(), It.IsAny<ISpecification<ApprovalMatrixRequest>>(), 
        //    It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((int pageNumber, int pageSize, ISpecification<ApprovalMatrixRequest> spec, string sortColumn, string sortOrder) =>
        //    {
        //        var filteredRequests = approvalMatrixRequests.Where(x => x.IsActive).ToList();
        //        var totalCount = filteredRequests.Count;
        //        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        //        var skip = (pageNumber - 1) * pageSize;
        //        var pagedRequests = filteredRequests.Skip(skip).Take(pageSize).ToList();

        //        return new PaginatedResult<ApprovalMatrixRequest>
        //        {
        //            Data = pagedRequests,
        //            PageSize = pageSize,
        //            TotalCount = totalCount,
        //            TotalPages = totalPages
        //        };
        //    });

        return mockApprovalMatrixRequestRepository;
    }

    public static Mock<IApprovalMatrixApprovalRepository> CreateApprovalMatrixApprovalRepository(List<ApprovalMatrixApproval> approvalMatrixApprovals)
    {
        var mockApprovalMatrixApprovalRepository = new Mock<IApprovalMatrixApprovalRepository>();

        mockApprovalMatrixApprovalRepository.Setup(repo => repo.GetApprovalMatrixApprovalByRequestId(It.IsAny<string>()))
            .ReturnsAsync((string requestId) => approvalMatrixApprovals.Where(x => x.RequestId == requestId).ToList());

        mockApprovalMatrixApprovalRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ApprovalMatrixApproval>()))
            .Returns((ApprovalMatrixApproval approval) =>
            {
                var existingApproval = approvalMatrixApprovals.FirstOrDefault(x => x.ReferenceId == approval.ReferenceId);
                if (existingApproval != null)
                {
                    existingApproval.Status = approval.Status;
                    existingApproval.ApproverName = approval.ApproverName;
                    existingApproval.IsActive = approval.IsActive;
                }
                return Task.CompletedTask;
            });

        return mockApprovalMatrixApprovalRepository;
    }

    public static Mock<IWorkflowTempRepository> CreateWorkflowTempRepository(List<WorkflowTemp> workflowTemps)
    {
        var mockWorkflowTempRepository = new Mock<IWorkflowTempRepository>();


        mockWorkflowTempRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowTemp>()))
            .Returns((WorkflowTemp workflowTemp) =>
            {
                var existingWorkflow = workflowTemps.FirstOrDefault(x => x.ReferenceId == workflowTemp.ReferenceId);
                if (existingWorkflow != null)
                {
                    existingWorkflow.IsActive = workflowTemp.IsActive;
                }
                return Task.CompletedTask;
            });

        return mockWorkflowTempRepository;
    }

    public static Mock<IWorkflowRepository> CreateWorkflowRepository(List<Workflow> workflows)
    {
        var mockWorkflowRepository = new Mock<IWorkflowRepository>();


        mockWorkflowRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Workflow>()))
            .Returns((Workflow workflow) =>
            {
                var existingWorkflow = workflows.FirstOrDefault(x => x.ReferenceId == workflow.ReferenceId);
                if (existingWorkflow != null)
                {
                    existingWorkflow.IsActive = workflow.IsActive;
                }
                return Task.CompletedTask;
            });

        return mockWorkflowRepository;
    }

    public static Mock<IUserActivityRepository> CreateUserActivityRepository(List<UserActivity> userActivities)
    {
        var mockUserActivityRepository = new Mock<IUserActivityRepository>();

        mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
            .ReturnsAsync((UserActivity userActivity) =>
            {
                userActivity.Id = userActivities.Count + 1;
                userActivity.ReferenceId = Guid.NewGuid().ToString();
                userActivities.Add(userActivity);
                return userActivity;
            });

        mockUserActivityRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(userActivities);

        return mockUserActivityRepository;
    }
}
