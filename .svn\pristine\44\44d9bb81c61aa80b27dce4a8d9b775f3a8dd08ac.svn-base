﻿using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetDetailByName;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Queries
{
    public class GetWorkflowActionDetailByNameQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IWorkflowActionRepository> _mockWorkflowActionRepository;
        private readonly GetWorkflowActionDetailByNameQueryHandler _handler;

        public GetWorkflowActionDetailByNameQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockWorkflowActionRepository = new Mock<IWorkflowActionRepository>();
            _handler = new GetWorkflowActionDetailByNameQueryHandler(
                _mockMapper.Object,
                _mockWorkflowActionRepository.Object
            );
        }

        [Fact]
        public async Task Handle_Should_Return_WorkflowActionDetailVm_When_WorkflowActionExists()
        {
            var request = new GetWorkflowActionDetailByNameQuery
            {
                Name = "TestAction"
            };

            var workflowAction = new Domain.Entities.WorkflowAction
            {
                Id = 1,
                ActionName = "TestEmail",
                Version = "6.0"
            };

            var workflowActionDetailVm = new WorkflowActionDetailVm
            {
                Id = Guid.NewGuid().ToString(),
                ActionName = "TestEmail",
                Type = "Custom"
            };

            _mockWorkflowActionRepository
                .Setup(repo => repo.GetWorkflowActionDetailsByName(request.Name))
                .ReturnsAsync(workflowAction);

            _mockMapper
                .Setup(mapper => mapper.Map<WorkflowActionDetailVm>(workflowAction))
                .Returns(workflowActionDetailVm);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(workflowAction.ReferenceId, result.Id);
            Assert.Equal(workflowAction.ActionName, result.ActionName);
            Assert.Equal(workflowAction.Version, result.Type);

            _mockWorkflowActionRepository.Verify(repo => repo.GetWorkflowActionDetailsByName(request.Name), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<WorkflowActionDetailVm>(workflowAction), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Throw_NotFoundException_When_WorkflowActionDoesNotExist()
        {
            var request = new GetWorkflowActionDetailByNameQuery
            {
                Name = "NonExistentAction"
            };

            _mockWorkflowActionRepository
                .Setup(repo => repo.GetWorkflowActionDetailsByName(request.Name))
                .ReturnsAsync((Domain.Entities.WorkflowAction)null);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(request, CancellationToken.None));

            Assert.Equal(nameof(Domain.Entities.WorkflowAction), exception.Source);
            Assert.Equal(request.Name, exception.Source);

            _mockWorkflowActionRepository.Verify(repo => repo.GetWorkflowActionDetailsByName(request.Name), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Throw_NotFoundException_When_WorkflowActionDetailVmIsNull()
        {
            var request = new GetWorkflowActionDetailByNameQuery
            {
                Name = "TestAction"
            };

            var workflowAction = new Domain.Entities.WorkflowAction
            {
                Id = 1,
                ActionName = "TestEmail",
                Type = "Custom"
            };

            _mockWorkflowActionRepository
                .Setup(repo => repo.GetWorkflowActionDetailsByName(request.Name))
                .ReturnsAsync(workflowAction);

            _mockMapper
                .Setup(mapper => mapper.Map<WorkflowActionDetailVm>(workflowAction))
                .Returns((WorkflowActionDetailVm)null);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(request, CancellationToken.None));

            Assert.Equal(nameof(Domain.Entities.WorkflowAction), exception.Source);
            Assert.Equal(request.Name, exception.Source);

            _mockWorkflowActionRepository.Verify(repo => repo.GetWorkflowActionDetailsByName(request.Name), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<WorkflowActionDetailVm>(workflowAction), Times.Once);
        }
    }
}
