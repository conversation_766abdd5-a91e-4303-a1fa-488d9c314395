﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class MSSQLMonitorStatusRepository : BaseRepository<MSSQLMonitorStatus>, IMssqlMonitorStatusRepository
{
    private readonly ApplicationDbContext _dbContext;

    public MSSQLMonitorStatusRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<List<MSSQLMonitorStatus>> GetDetailByType(string type)
    {
        return await _dbContext.MssqlMonitorStatuses.Where(x => x.IsActive && x.Type.Equals(type)).ToListAsync();
    }

    public async Task<MSSQLMonitorStatus> GetMssqlMonitorStatusByInfraObjectIdAsync(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObjectId", "InfraObjectId cannot be invalid");

        return await _dbContext.MssqlMonitorStatuses
            .Where(x => x.InfraObjectId.Equals(infraObjectId))
            //.Select(x => new MSSQLMonitorStatus { ReferenceId = x.ReferenceId })
            .FirstOrDefaultAsync();
    }
}