﻿using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Create;
using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;


public class AutoLicenseInfoDataAttribute : AutoDataAttribute
{
    public AutoLicenseInfoDataAttribute()
        : base(() =>
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateLicenseInfoCommand>(p => p.LicenseId, 10));
            fixture.Customize<CreateLicenseInfoCommand>(c => c.With(b => b.LicenseId, 10.ToString()));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateLicenseInfoCommand>(p => p.LicenseId, 10));
            fixture.Customize<UpdateLicenseInfoCommand>(c => c.With(b => b.LicenseId, 0.ToString()));

            return fixture;
        })
    {

    }
}