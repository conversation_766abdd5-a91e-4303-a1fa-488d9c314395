﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowOperation.Events.Update;

public class WorkflowOperationUpdatedEventHandler : INotificationHandler<WorkflowOperationUpdatedEvent>
{
    private readonly ILogger<WorkflowOperationUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowOperationUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowOperationUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowOperationUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Workflow '{updatedEvent.Description}' updated successfully.");

        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.WorkflowOperation}",
            Entity = Modules.WorkflowOperation.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"WorkflowOperation '{updatedEvent.Description}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);
    }
}