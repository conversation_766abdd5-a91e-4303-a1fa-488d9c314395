﻿namespace ContinuityPatrol.Web.Areas.ITAutomation.Controllers;

[Area("ITAutomation")]
public class WorkflowOperationController : BaseController
{
    private readonly ILogger<WorkflowOperationController> _logger;

    public WorkflowOperationController(ILogger<WorkflowOperationController> logger)
    {
        _logger = logger;
    }

    public IActionResult List()
    {
        _logger.LogDebug("Entering List method in Workflow Operation");

        return View();
    }
}