﻿let msServiceStatus = "";
let msStatus = "";
let msId = "";
let datas = [];
let msInfra = [];
let selectedValues = [];
let actionData = {};

$(function () {
    let Permission = {
        'Create': $("#ManageCreate").data("create-permission").toLowerCase(),
        'Delete': $("#ManageDelete").data("delete-permission").toLowerCase()
    }
    let monitorURL = {
        nameExistUrl: "Manage/MonitoringServices/IsServiceNameExist",
        getPagination: "/Manage/MonitoringServices/GetPagination",
        CreateOrUpdate: "Manage/MonitoringServices/CreateOrUpdate",
        Delete: "Manage/MonitoringServices/Delete",
        GetInfraObjectsByBusinessServiceId: "Configuration/InfraObject/GetInfraObjectByBusinessServiceId",
        GetServersByInfraObjectId: "Configuration/InfraObject/GetServersByInfraObjectId",
        GetServerDataByServerId: "Manage/MonitoringServices/GetServerById",
        getWorkflowByType: "ITAutomation/WorkflowInfraObject/GetWorkflowByInfraObjectIdAndActionType",
        UpdateMonitorStatus: "Manage/MonitoringServices/UpdateMonitorStatus"
    }

    if (Permission.Create == 'false') {
        $("#btnMonitoringServiceCreate").addClass("btn-disabled").css("cursor", "not-allowed").removeAttr("data-bs-toggle data-bs-target id");
    }

    let dataTable = $('#tblMoniterService').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        "processing": true,
        "serverSide": false,
        "filter": true,
        "order": [],
        "ajax": {
            "type": "GET",
            "url": monitorURL.getPagination,
            "dataType": "json",
            "data": function (d) {
                d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                d.pageSize = d?.length;
                d.searchString = selectedValues?.length === 0 ? $('#msSearch').val() : selectedValues.join(';');
                selectedValues.length = 0;
                d.businessServiceId = $('#msListOperationService').find("option:selected").val() === "All" ? '' : $('#msListOperationService').find("option:selected").val();
                d.infraObjectId = $('#msListInfra').find("option:selected").val() === "All" ? '' : $('#msListInfra').find("option:selected").val()
            },
            "dataSrc": function (json) {    
                if (json?.success && json?.data?.data && json?.data?.data.length) {
                    json?.data?.data?.forEach((item) => {
                        if (item.lastExecutionTime) {
                            let date = new Date(item.lastExecutionTime);
                            item.lastExecutionTime = `${('0' + date.getDate()).slice(-2)}-${('0' + (date.getMonth() + 1)).slice(-2)}-${date.getFullYear()} ${('0' + date.getHours()).slice(-2)}:${('0' + date.getMinutes()).slice(-2)}:${('0' + date.getSeconds()).slice(-2)}`;
                        }
                        datas.push(item);
                        if (item.businessServiceName) {
                            $('#msListOperationService').append(`<option value="${item.businessServiceId}">${item.businessServiceName}</option>`);
                        }
                    });                   
                } else {
                    notificationAlert("warning", json?.message);
                    return false;
                }
             
                $('#msListOperationService').find('option').each(function () {
                    $(this).siblings(`[value="${this.value}"]`).remove();
                });
                json.recordsTotal = json?.totalPages;
                json.recordsFiltered = json?.totalCount;
                $(".pagination-column").toggleClass("disabled", json?.data?.data?.length === 0);
                return json?.data?.data;
            }
        },
        "columnDefs": [
            { "targets": [1, 2, 3, 4], "className": "truncate" }
        ],
        "columns": [
            {
                "data": null,
                "name": "Sr. No.",
                "orderable": false,
                "render": (data, type, row, meta) => type === 'display' ? meta.row + 1 : data
            },
            { "data": "businessServiceName", "name": "businessServiceName" },
            { "data": "infraObjectName", "name": "infraObjectName" },
            { "data": "serverName", "name": "serverName" },
            {
                "data": "servicePath", "name": "servicePath",
                "render": (data, type, row) => row.workflowName ?? data
            },
            {
                "data": "lastExecutionTime", "name": "lastExecutionTime",
                "render": (data) => data || 'NA'
            },
            {
                "data": "isServiceUpdate",
                "name": "isServiceUpdate",
                "render": (data, type, row) => {
                    let icon = '', display = true;

                    switch (data) {
                        case 'Running':
                            if (row?.status?.toLowerCase() !== 'stopped') {
                                icon = 'text-primary cp-reload cp-animate me-1';
                            } else {
                                data = 'NA'; display = false;
                            }
                            break;
                        case 'Stopped':
                        case 'Error':
                            icon = 'text-danger cp-error';
                            break;
                        case 'NA':
                            data = '-'; display = false;
                            break;
                    }

                    return display ? `<td><i class="${icon}"></i></td><td><span>${data}</span></td>` :
                        `<td></td><td><span>${data}</span></td>`;
                }
            },
            {
                "data": "status", "name": "Status",
                "render": (data) => {
                    if (!data) return `<td></td>`;

                    let status = data.toLowerCase();
                    let iconClass = {
                        started: "text-success cp-success me-1",
                        stopped: "text-danger cp-error me-1",
                        pending: "cp-pending text-warning me-1"
                    }[status];
                    let title = data.charAt(0).toUpperCase() + data.slice(1);
                    return `<td><i class="${iconClass}" title="${title}"></i></td><td><span>${title}</span></td>`;
                }
            },
            {
                "orderable": false,
                "render": (data, type, row) => {
                    let isEditAllowed = Permission.Create === "true";
                    let isDeleteAllowed = Permission.Delete === "true";
                    let isStarted = row?.status?.toLowerCase() === 'started';
                    let isPending = row?.status?.toLowerCase() === 'pending';

                    let statusIcon = (!isPending && row?.status) ? `<i id='MonitorStatus' class="${isStarted ? 'text-danger cp-Stopped' : 'text-success cp-circle-play'}"  data-moniter-name="${row.infraObjectName}"  title="${isStarted ? 'Stop' : 'Start'}"   data-title="${isStarted ? 'Stop' : 'Start'}" data-status="${row.status}" data-moniter-state="${isStarted ? 'Stopped' : 'Started'}" data-moniter-id="${row.id}"></i>`
                        : '';
                    let editBtn = isEditAllowed
                        ? `<span role="button" title="Edit" class="btnMSEdit ${isStarted ? 'form-delete-disable' : ''}" data-moniter-service='${JSON.stringify(row)}'><i class="cp-edit"></i></span>`
                        : `<span role="button" title="Edit" class="btn-disabled"><i class="cp-edit"></i></span>`;
                    let deleteBtn = isDeleteAllowed
                        ? `<span role="button" title="Delete" class="btnMSDelete ${isStarted ? 'form-delete-disable' : ''}" data-moniter-id="${row.id}" data-moniter-name="${row.infraObjectName}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i></span>`
                        : `<span role="button" title="Delete" class="btn-disabled"><i class="cp-Delete"></i></span>`;

                    return `<div class="d-flex align-items-center gap-2">${editBtn}${deleteBtn}${statusIcon}</div>`;
                }
            }
        ], "rowCallback": function (row, data, index) {
            let api = this.api();
            let startIndex = api.context[0]._iDisplayStart;
            let counter = startIndex + index + 1;
            $('td:eq(0)', row).html(counter);
        },
        initComplete: function () {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
            $('th.Sr_No').removeClass('sorting_asc');
        }
    });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
        $('th.Sr_No').removeClass('sorting_asc');
    });

    document.getElementById('msSearch')?.addEventListener('keypress', preventEnterKey);

    $('#msSearch').on('input', commonDebounce(function (e) {
        let inputValue = $('#msSearch').val();
        let checkboxes = [
            $('#filterBusinessServiceName'),
            $('#filterInfraObjectName'),
            $('#filterServerName')
        ];
        selectedValues = checkboxes
            .filter(checkbox => checkbox.is(':checked'))
            .map(checkbox => checkbox.val() + inputValue);
        let currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {
                if (e?.target?.value && json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false);
        }
    }, 500));
    function populateMSFields(monitorData) {
        let infraDropdown = $('#msInfraObject').empty();
        let serverDropdown = $('#msServer').empty();
        msId = monitorData.id;
        $('#msBusinessService').val(monitorData.businessServiceName);
        $('.msradio[value="' + monitorData.threadType + '"]').prop("checked", true);
        if ($('#msInfraObject option[value="' + monitorData.infraObjectName + '"]').length === 0) {
            infraDropdown.append(`<option data-infraid="${monitorData.infraObjectId}" value="${monitorData.infraObjectName}">${monitorData.infraObjectName}</option>`);
        }
        $('#msInfraObject').val(monitorData.infraObjectName);
        serverDropdown.append(`<option data-serverid="${monitorData.serverId}" value="${monitorData.serverName}">${monitorData.serverName}</option>`);
        $('#msServer').val(monitorData.serverName);
        $('#msWorkflowType').val(monitorData.workflowType);
        msServiceStatus = monitorData.isServiceUpdate ?? 'NA';
        msStatus = monitorData.status ?? 'NA';
        getServerData(monitorData.serverId, monitorData.type);
        let isWorkflow = monitorData.type === "Use Workflow";
        $('#workflowType, #workflow').toggle(isWorkflow);
        $('#command').toggle(!isWorkflow);
        if (isWorkflow) {
            getWorkflowsByType(monitorData.infraObjectId, monitorData.workflowType, monitorData.workflowId, monitorData.workflowName);
        } else {
            let isService = $('.msradio:checked').val() === "Service";
            $('#msServiceDiv').toggle(isService);
            $('#msProcessDiv').toggle(!isService);
            $(isService ? '#msServiceName' : '#msProcessName').val(monitorData.servicePath);
        }
    }

    async function getInfraObjectsByBusinessServiceId(businessServiceId) {
        let infraDropdown = $('#msInfraObject').empty();
        let url = `${RootUrl}${monitorURL.GetInfraObjectsByBusinessServiceId}`;
        let data = { businessServiceId };
        let result = await getAysncWithHandler(url, data, OnError);
        infraDropdown.append('<option value="">Select InfraObject</option>');
        result?.forEach(({ id, name }) => {
            if (id && name) {
                infraDropdown.append(`<option data-infraid="${id}" value="${name}">${name}</option>`);
            }
        });
    };

    async function getServersByInfraObjectId(infraObjectId) {
        let serverDropdown = $('#msServer').empty();
        $('#msAuthenticationType').empty();
        let url = `${RootUrl}${monitorURL.GetServersByInfraObjectId}`;
        let data = { infraObjectId };
        let result = await getAysncWithHandler(url, data, OnError);
        serverDropdown.append('<option value="">Select Server</option>');
        let servers = JSON?.parse(result?.serverProperties || '{}');
        if (servers && typeof servers === 'object') {
            Object.values(servers).forEach(({ id, name }) => {
                let ids = id?.split(',') || [];
                let names = name?.split(',') || [];
                ids.forEach((serverId, i) => {
                    let serverName = names[i] || names[names.length - 1];
                    serverDropdown.append(`<option data-serverid="${serverId}" value="${serverName}">${serverName}</option>`);
                });
            });
        }
    }

    async function msCreateOrUpdate(commonData) {
        await $.ajax({
            url: RootUrl + monitorURL.CreateOrUpdate,
            type: "POST",
            dataType: "json",
            data: commonData,
            success: function (result) {
                if (result?.success) {
                    notificationAlert("success", result.data);
                    $('#CreateModal').modal('hide');
                    setTimeout(() => dataTable.ajax.reload(), 200);
                } else {
                    errorNotification(result);
                }
            }
        });
    };

    let getServerData = async (id, populateValue = '') => {
        let authDropdown = $('#msAuthenticationType');
        authDropdown.empty();
        let serverId = id?.includes(',') ? id.split(',')[0] : id;
        let data = { serverId };
        let url = RootUrl + monitorURL.GetServerDataByServerId;
        await $.ajax({
            type: "GET",
            url: url,
            data: data,
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result && typeof result === 'object') {
                    let authOptions = {
                        Linux: ["Use ps -ef", "Use Workflow"],
                        Windows: ["Use WMI", "Use Workflow", "Use SSH", "Use PowerShell"],
                        AS400: ["Use WMI", "Use Workflow", "Use SSH", "Use PowerShell", "Use Telnet"],
                        default: ["Use ps -ef", "Use Workflow"]
                    };
                    let options = authOptions[result?.osType] || authOptions.default;
                    authDropdown.append(new Option("Select Server Authentication Type", ""));
                    options.forEach(option => authDropdown.append(new Option(option, option)));
                    if (populateValue) {
                        authDropdown.val(populateValue);
                    }
                } else {
                    authDropdown.append(new Option("Authentication type Not Found", ""));
                }
            }
        })
    };

    async function getWorkflowsByType(infraId, actionType, selectedId = '', selectedName = '') {
        let url = RootUrl + monitorURL.getWorkflowByType;
        let data = { infraId, actionType };
        let result = await getAysncWithHandler(url, data, OnError);
        let $Workflow = $('#msWorkflow').empty().append('<option value="">Select Workflow Name</option>');
        result?.forEach(({ workflowId, workflowName }) => {
            if (workflowId && workflowName) {
                $Workflow.append(`<option value="${workflowId}">${workflowName}</option>`);
            }
        });
        if (selectedId?.length > 1) {
            $Workflow.val(selectedId);
        }
    }

    //Validations-functions
    async function isNameExist(url, inputValue, fieldName) {
        if (!inputValue || !inputValue[fieldName] || !inputValue[fieldName].trim()) {
            return true;
        }
        return (await getAysncWithHandler(url, inputValue, OnError)) ? " Name already exists" : true;
    }

    async function pathNameValidate(value, id = null, infraObjectId, type, serverId, threadType, urlPath, errorMsg, errorElement) {
        if (!value) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        }
        let url = RootUrl + urlPath;
        let data = { servicePath: value, id, infraObjectId, type, serverId, threadType };
        let validationResults = [
            await SpecialCharValidateCustom(value),
            await ShouldNotBeginWithUnderScore(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxCompanylength(value),
            await secondChar(value),
            await isNameExist(url, data, "servicePath", OnError)
        ];
        return CommonValidation(errorElement, validationResults);
    }

    async function monitoringServiceValidate(value, errorMessage, errorElement) {
        let isValid = !!value;
        errorElement.text(isValid ? '' : errorMessage).toggleClass('field-validation-error', !isValid);
        return isValid;
    }

    async function workflowNameValidate(value, id = null, infraId, type, serverId, workflowType, workflowId, urlPath, errorMessage, errorElement) {
        let url = RootUrl + urlPath;
        let data = { workflowId: value, id, infraObjectId: infraId, type, serverId, workflowType, workflowName: workflowId };
        if (!value) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        }
        let validationResult = await isNameExist(url, data, "workflowId", OnError);
        return CommonValidation(errorElement, [validationResult]);
    }

    async function monitorServiceValidateFields() {
        let errorElements = ['#BusinessServiceId-error', '#InfraObjectId-error', '#ServerId-error', '#Type-error', '#WorkflowType-error', '#WorkflowId-error', '#ServicePath-error', '#ThreadType-error', '#ProcessPath-error'];
        clearInputFields('CreateForm', errorElements);
    }

    $('#btnMonitoringServiceCreate').on('click', function () {
        monitorServiceValidateFields();
        msId = "";
        $('#msServiceName,#msProcessName').val('');
        ['#msBusinessService', '#msInfraObject', '#msServer', '#msAuthenticationType', '#msWorkflowType', '#msWorkflow'].forEach(selector => $(selector).prop('selectedIndex', 0));
        $('#btnMSSave').text('Save');
        $('#msInfraObject,#msServer,#msWorkflow,#msAuthenticationType').empty();
        $('.msradio').prop("checked", false);
        $('#msServiceDiv,#msProcessDiv,#workflowType,#workflow,#command').hide();
    });

    $('.msradio').on('click', function () {
        let selectedValue = document.querySelector('.msradio:checked').value;
        let targetBox = $("." + selectedValue);
        $(".box").not(targetBox).hide();
        $(targetBox).show();
        if (selectedValue === "Service") {
            $('#msProcessName').val('');
            $("#ProcessPath-error").text("").removeClass('field-validation-error');
        }
        else if (selectedValue === "Process") {
            $('#msServiceName').val('');
            $("#ServicePath-error").text("").removeClass('field-validation-error');
        }
    });

    $('#tblMoniterService').on('click', '#MonitorStatus', function () {
        let $this = $(this);
        let monitorId = $this.data('moniter-id');
        let status = $this.data('status');
        let textStatus = $this.data('title').toLowerCase();
        let monitorName = $this.data('moniter-name');
        let newStatus = status === 'Stopped' ? 'Started' : 'Stopped';
        actionData = { id: monitorId, status: newStatus, isServiceUpdate: newStatus === 'Stopped' ? 'NA' : 'NA' };
        $('#statusData').text(textStatus);
        $('#monitorData').text(monitorName);
        $('#msControlModal').modal('show');
    });

    $("#btnMSControl").on('click', function () {
        $('#btnMSControl').prop('disabled', true);
        $('#MSLoader').removeClass('d-none').show();
        $.ajax({
            type: "POST",
            url: RootUrl + monitorURL.UpdateMonitorStatus,
            data: actionData,
            traditional: true,
            headers: { 'RequestVerificationToken': gettoken() },
            dataType: 'json',
            success: function (result) {
                let dt = $('#tblMoniterService').DataTable();

                if (result?.data?.success) {
                    let page = dt.page();
                    dt.ajax.reload(() => dt.page(page).draw(false), false);
                    notificationAlert("success", result.data.message);
                } else {
                    $('.btnMSEdit, .btnMSDelete, .text-danger.cp-Stopped, .text-success.cp-circle-play')
                        .addClass('icon-disabled').prop('disabled', true)
                        .removeAttr('data-bs-toggle data-bs-target');
                    errorNotification(result);
                }
                $('#MSLoader').removeClass('d-none').hide();
                $('#msControlModal').modal('hide');
                $("#btnMSControl").prop("disabled", false);
            }
        });
    });

    $('#msWorkflowType').on('change', function () {
        let type = $(this).val();
        let infraId = $('#msInfraObject option:selected').data('infraid');
        let errorElement = $('#WorkflowType-error');
        monitoringServiceValidate(type, 'Select workflow type', errorElement);
        if (infraId && type) {
            getWorkflowsByType(infraId, type);
        }
    });

    $('#msListOperationService').on('change', function () {
        $('#msListInfra').empty()
        msInfra = [];
        $('#msListInfra').append('<option value="All">All</option>');
        datas.forEach(function (value) {
            if ($('#msListOperationService').find(':selected').val() === value.businessServiceId && !msInfra.some(item => item.infraObjectId === value.infraObjectId)) {
                let bsValue = value.businessServiceName
                let infraValue = value.infraObjectName
                let infraId = value.infraObjectId
                $('#msListInfra').append('<option value="' + value.infraObjectId + '">' + value.infraObjectName + '</option>');
                msInfra.push({ infraObjectId: infraId, infraObjectName: infraValue })
                selectedValues.push(`businessServiceName=${bsValue}`)

            }
        });
        dataTable.ajax.reload()
    })

    $("#msListInfra").on("change", function () {
        let selectedInfraId = $(this).val();
        if (selectedInfraId !== 'All') {
            let selectedInfra = msInfra.find(item => item.infraObjectId === selectedInfraId);
            if (selectedInfra) {
                selectedValues.push(`infraObjectName=${selectedInfra.infraObjectName}`);
            }
        } else {
            msInfra.forEach(({ infraObjectName }) => {
                selectedValues.push(`infraObjectName=${infraObjectName}`);
            });
        }
        dataTable.ajax.reload();
    });

    $('#msInfraObject').on('change', function () {
        let value = $(this).val();
        let id = $(this).find('option:selected').data('infraid');
        let errorElement = $('#InfraObjectId-error');
        $('#workflowType,#workflow,#command').hide();
        $('#msServiceName,#msWorkflowType, #msWorkflow').val('');
        getServersByInfraObjectId(id);
        monitoringServiceValidate(value, 'Select infraobject', errorElement);
    });

    $('#msServer').on('change', function () {
        let value = $(this).val();
        let id = $(this).find('option:selected').data('serverid');
        let errorElement = $('#ServerId-error');
        $('#workflowType,#workflow,#command').hide();
        $('#msServiceName,#msWorkflowType, #msWorkflow').val('');
        getServerData(id)
        monitoringServiceValidate(value, 'Select server', errorElement);
    });

    $('#msAuthenticationType').on('change', function () {
        let value = $(this).val();
        let isWorkflow = value === 'Use Workflow';
        let isCommandBased = ['Use ps -ef', 'Use WMI', 'Use SSH', 'Use PowerShell', 'Use Telnet'].includes(value);
        $('#workflowType, #workflow').toggle(isWorkflow);
        $('#command').toggle(isCommandBased);
        $('#msServiceName,#msWorkflowType, #msWorkflow').val('').trigger('change');
        let errorIds = [
            '#WorkflowType-error',
            '#WorkflowId-error',
            '#ThreadType-error',
            '#ServicePath-error'
        ];
        errorIds.forEach(id => $(id).text('').removeClass('field-validation-error'));
        $('#msServiceDiv, #msProcessDiv').hide();
        $('.msradio').prop("checked", false);
        $('#ProcessPath-error').val('').trigger('change');

        if (!$('#msWorkflowType').val()) {
            $('#msWorkflowType').append('<option value="" disabled selected>Select Workflow Type</option>');
        }
        if (!$('#msWorkflow').val()) {
            $('#msWorkflow').append('<option value="" disabled selected>Select Workflow Name</option>');
        }
        let errorElement = $('#Type-error');
        monitoringServiceValidate(value, 'Select server authentication type', errorElement);
    });

    $('#msWorkflow').on('change', async function () {
        let workflowId = $("#msWorkflow").val();
        let workflowName = $("#msWorkflow option:selected").text();
        let workflowType = $('#msWorkflowType').val();
        let monitorId = msId;
        let infraId = $('#msInfraObject option:selected').data('infraid');
        let serverId = $('#msServer option:selected').data('serverid');
        let type = $('#msAuthenticationType option:selected').text();
        let errorElement = $('#WorkflowId-error');
        await workflowNameValidate(workflowId, monitorId, infraId, type, serverId, workflowType, workflowName, monitorURL.nameExistUrl, 'Select workflow name', errorElement);
    });

    $("#btnMSSave").on('click', async function () {
        let businessServiceId = $("#msBusinessService option:selected").attr("id");
        let businessServiceName = $("#msBusinessService").val();
        let infraId = $("#msInfraObject option:selected").data("infraid");
        let infraName = $("#msInfraObject").val();
        let serverId = $("#msServer option:selected").data("serverid");
        let serverName = $("#msServer").val();
        let threadType = $('.msradio:checked').val();
        let type = $("#msAuthenticationType").val();
        let id = msId;
        let workflowType = $("#msWorkflowType").val();
        let workflow = $("#msWorkflow").val();
        let workflowName = workflow ? $("#msWorkflow option:selected").text() : null;
        let servicePath = threadType === 'Service' ? $('#msServiceName').val() : threadType === 'Process' ? $('#msProcessName').val() : '';
        let errors = {
            Bsservice: $('#BusinessServiceId-error'),
            Infra: $('#InfraObjectId-error'),
            Server: $('#ServerId-error'),
            Type: $('#Type-error'),
            Workflowtype: $('#WorkflowType-error'),
            Workflow: $('#WorkflowId-error'),
            Servicepath: $('#ServicePath-error'),
            Processpath: $('#ProcessPath-error'),
            Threadtype: $('#ThreadType-error')
        };
        let isBsService = monitoringServiceValidate(businessServiceName, 'Select operational service', errors.Bsservice);
        let isInfra = monitoringServiceValidate(infraName, 'Select infraobject', errors.Infra);
        let isServer = monitoringServiceValidate(serverName, 'Select server', errors.Server);
        let isType = monitoringServiceValidate(type, 'Select server authentication type', errors.Type);
        let isWorkflowType = monitoringServiceValidate(workflowType, 'Select workflow type', errors.Workflowtype);
        let isThreadType = monitoringServiceValidate(threadType, 'Select any one', errors.Threadtype);
        let isWorkflow = await workflowNameValidate(workflow, id, infraId, type, serverId, workflowType, workflowName, monitorURL.nameExistUrl, 'Select workflow name', errors.Workflow);
        let isServicePath = false;
        if (threadType === 'Service') {
            isServicePath = await pathNameValidate(servicePath, id, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter service name', errors.Servicepath);
        } else if (threadType === 'Process') {
            isServicePath = await pathNameValidate(servicePath, id, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter process name', errors.Processpath);
        }

        let TypeValue = type === "Use Workflow";
        let isCommandType = ["Use ps -ef", "Use SSH", "Use WMI", "Use PowerShell", "Use Telnet"].includes(type);
        let commonData = {
            BusinessServiceId: businessServiceId,
            BusinessServiceName: businessServiceName,
            InfraObjectId: infraId,
            InfraObjectName: infraName,
            ServerId: serverId,
            ServerName: serverName,
            ServicePath: servicePath,
            ThreadType: threadType,
            Type: type,
            Id: id,
            IsServiceUpdate: msServiceStatus,
            Status: msStatus,
            WorkflowId: workflow,
            WorkflowName: workflowName,
            WorkflowType: workflowType,
            __RequestVerificationToken: gettoken()
        };

        if (TypeValue) {
            if (isBsService && isInfra && isServer && isType && isWorkflowType && isWorkflow && isThreadType) {
                msCreateOrUpdate(commonData);
            }
        } else if (isCommandType) {
            if (isBsService && isInfra && isServer && isType && isServicePath && isThreadType) {
                msCreateOrUpdate(commonData);
            }
        }
    });

    $('#msBusinessService').on('change', function () {
        let value = $(this).val();
        let id = $(this).find('option:selected').attr('id');
        let errorElement = $('#BusinessServiceId-error');
        monitoringServiceValidate(value, 'Select operational service', errorElement);
        $('#msServer,#msAuthenticationType').empty();
        $('#workflowType, #workflow,#command').hide();
        $('#msServiceName,#msWorkflowType, #msWorkflow').val('');
        getInfraObjectsByBusinessServiceId(id);
    });

    $('#msServiceName').on('input', commonDebounce(async function () {
        let value = $(this).val();
        if (value !== undefined) {
            $(this).val(value.replace(/ {2,}/g, " "));
        }
        let infraId = $('#msInfraObject option:selected').data('infraid');
        let serverId = $('#msServer option:selected').data('serverid');
        let type = $('#msAuthenticationType option:selected').text()
        let threadType = $('.msradio:checked').val();
        let monitorId = msId;
        let errorElementPath = $('#ServicePath-error');
        await pathNameValidate(value, monitorId, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter service name', errorElementPath);
    }, 400));

    $('#msProcessName').on('input', commonDebounce(async function () {
        let value = $(this).val();
        if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }
        let infraId = $('#msInfraObject option:selected').data('infraid');
        let serverId = $('#msServer option:selected').data('serverid');
        let type = $('#msAuthenticationType option:selected').text()
        let threadType = $('.msradio:checked').val();
        let monitorId = msId;
        let errorElementPath = $('#ProcessPath-error');
        await pathNameValidate(value, monitorId, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter process name', errorElementPath);
    }, 400));

    $('#tblMoniterService').on('click', '.btnMSEdit', function () {
        let monitorData = $(this).data('moniterService');
        monitorServiceValidateFields();
        populateMSFields(monitorData);
        $('#btnMSSave').text('Update');
        $('#CreateModal').modal('show');
    });

    $('#tblMoniterService').on('click', '.btnMSDelete', function () {
        let monitorId = $(this).data('moniter-id');
        let monitorName = $(this).data('moniter-name');
        $('#deleteData').text(monitorName);
        msId = monitorId;
    });

    $('#btnMSConfirmDelete').on('click', async function () {
        let monitorId = msId;
        await $.ajax({
            url: RootUrl + monitorURL.Delete,
            type: "POST",
            dataType: "json",
            data: { id: monitorId },
            success: function (result) {
                if (result?.success) {
                    notificationAlert("success", result.data);
                    $('#DeleteModal').modal('hide');
                    setTimeout(() => dataTable.ajax.reload(), 200);
                } else {
                    errorNotification(result);
                }
            }
        });
    });
});