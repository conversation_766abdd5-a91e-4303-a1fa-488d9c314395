using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Create;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Delete;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Update;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetByRole;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetList;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AccessManagerModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class AccessManagersControllerTests : IClassFixture<AccessManagerFixture>
{
    private readonly AccessManagerFixture _accessManagerFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly AccessManagersController _controller;

    public AccessManagersControllerTests(AccessManagerFixture accessManagerFixture)
    {
        _accessManagerFixture = accessManagerFixture;

        var testBuilder = new ControllerTestBuilder<AccessManagersController>();
        _controller = testBuilder.CreateController(
            _ => new AccessManagersController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAccessManagers_ReturnsExpectedList()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllBusinessFunctionsCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAccessManagerListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_accessManagerFixture.AccessManagerListVm);

        // Act
        var result = await _controller.GetAccessManagers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var accessManagers = Assert.IsAssignableFrom<List<AccessManagerListVm>>(okResult.Value);
        Assert.Equal(3, accessManagers.Count);
    }

    [Fact]
    public async Task GetAccessManagers_ReturnsEmptyList_WhenNoAccessManagersExist()
    {
        // Arrange
        var companyId = "Test_Company123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllBusinessFunctionsCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAccessManagerListQuery>(), default))
            .ReturnsAsync(new List<AccessManagerListVm>());

        // Act
        var result = await _controller.GetAccessManagers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var accessManagers = Assert.IsAssignableFrom<List<AccessManagerListVm>>(okResult.Value);
        Assert.Empty(accessManagers);
    }

    [Fact]
    public async Task GetAccessManagerById_ReturnsAccessManager_WhenIdIsValid()
    {
        // Arrange
        var accessManagerId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAccessManagerDetailQuery>(q => q.Id == accessManagerId), default))
            .ReturnsAsync(_accessManagerFixture.AccessManagerDetailVm);

        // Act
        var result = await _controller.GetAccessManagerById(accessManagerId);

        // Assert
        Assert.IsType<OkObjectResult>(result.Result);
    }

    [Fact]
    public async Task GetAccessManagerById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetAccessManagerById("invalid-guid"));
    }

    [Fact]
    public async Task CreateAccessManager_Returns201Created()
    {
        // Arrange
        var command = _accessManagerFixture.CreateAccessManagerCommand;
        var expectedMessage = $"AccessManager '{command.RoleName}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAccessManagerResponse
            {
                Message = expectedMessage,
                AccessManagerId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateAccessManager(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateAccessManagerResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task CreateAccessManager_Throws_WhenRoleExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateAccessManagerCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("Role exists"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.CreateAccessManager(_accessManagerFixture.CreateAccessManagerCommand));
    }

    [Fact]
    public async Task UpdateAccessManager_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"AccessManager '{_accessManagerFixture.UpdateAccessManagerCommand.RoleName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateAccessManagerCommand>(), default))
            .ReturnsAsync(new UpdateAccessManagerResponse
            {
                Message = expectedMessage,
                AccessManagerId = _accessManagerFixture.UpdateAccessManagerCommand.Id
            });

        // Act
        var result = await _controller.UpdateAccessManager(_accessManagerFixture.UpdateAccessManagerCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateAccessManagerResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAccessManager_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "AccessManager 'TestRole' has been deleted successfully!.";
        var accessManagerId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAccessManagerCommand>(c => c.Id == accessManagerId), default))
            .ReturnsAsync(new DeleteAccessManagerResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAccessManager(accessManagerId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAccessManagerResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAccessManager_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteAccessManager("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedAccessManagers_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetAccessManagerPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _accessManagerFixture.AccessManagerListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAccessManagerPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<AccessManagerListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        var result = await _controller.GetPaginatedAccessManagers(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<AccessManagerListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<AccessManagerListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(3, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetByRole_ReturnsAccessManagerByRole()
    {
        // Arrange
        var roleId = "ADMIN_ROLE";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByRoleQuery>(q => q.Role == roleId), default))
            .ReturnsAsync(_accessManagerFixture.GetByRoleIdVm);

        // Act
        var result = await _controller.GetByRole(roleId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<GetByRoleIdVm>(okResult.Value);
        Assert.Equal(roleId, response.RoleId);
        Assert.Equal("Administrator", response.RoleName);
    }

    [Fact]
    public async Task GetAccessManagers_CallsCorrectQuery()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllBusinessFunctionsCacheKey + companyId);

        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAccessManagerListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<AccessManagerListVm>());

        // Act
        await _controller.GetAccessManagers();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public async Task CreateAccessManager_ClearsCacheAfterCreation()
    {
        // Arrange
        var command = _accessManagerFixture.CreateAccessManagerCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAccessManagerResponse
            {
                Message = "Created successfully",
                AccessManagerId = Guid.NewGuid().ToString()
            });

        // Act
        await _controller.CreateAccessManager(command);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public async Task UpdateAccessManager_ClearsCacheAfterUpdate()
    {
        // Arrange
        var command = _accessManagerFixture.UpdateAccessManagerCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateAccessManagerResponse
            {
                Message = "Updated successfully",
                AccessManagerId = command.Id
            });

        // Act
        await _controller.UpdateAccessManager(command);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public async Task DeleteAccessManager_ClearsCacheAfterDeletion()
    {
        // Arrange
        var accessManagerId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAccessManagerCommand>(c => c.Id == accessManagerId), default))
            .ReturnsAsync(new DeleteAccessManagerResponse
            {
                IsActive = false,
                Message = "Deleted successfully"
            });

        // Act
        await _controller.DeleteAccessManager(accessManagerId);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }


   
    [Fact]
    public async Task GetPaginatedAccessManagers_HandlesLargePageSize()
    {
        // Arrange
        var query = new GetAccessManagerPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 1000 // Large page size
        };

        var expectedData = _accessManagerFixture.AccessManagerListVm;
        var expectedPaginatedResult = PaginatedResult<AccessManagerListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAccessManagerPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedAccessManagers(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<AccessManagerListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<AccessManagerListVm>>(okResult.Value);

        Assert.Equal(1000, paginatedResult.PageSize);
        Assert.Equal(3, paginatedResult.Data.Count);
    }

  
    [Fact]
    public async Task DeleteAccessManager_VerifiesAccessManagerIsDeactivated()
    {
        // Arrange
        var accessManagerId = Guid.NewGuid().ToString();
        var expectedMessage = "AccessManager 'Test Manager' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAccessManagerCommand>(c => c.Id == accessManagerId), default))
            .ReturnsAsync(new DeleteAccessManagerResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAccessManager(accessManagerId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAccessManagerResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }
}
