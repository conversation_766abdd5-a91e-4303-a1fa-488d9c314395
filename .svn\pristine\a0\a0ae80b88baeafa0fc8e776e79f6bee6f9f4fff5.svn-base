﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using System.Threading.Tasks;

namespace ContinuityPatrol.Persistence.Repositories
{
    public class SolutionTypeTablesRepository:BaseRepository<SolutionTypeTables>, ISolutionTypeTablesRepository
    {
        private readonly ILoggedInUserService _loggedInUserService;
        private readonly ApplicationDbContext _dbContext;
        public SolutionTypeTablesRepository(ApplicationDbContext applicationDbContext,ILoggedInUserService loggedInUserService):base(applicationDbContext, loggedInUserService)
        {
            _loggedInUserService = loggedInUserService;
            _dbContext = applicationDbContext;
        }


        public async Task<List<SolutionTypeTables>> GetBySolutionType(string solutionType)
        {
            var result = await _dbContext.SolutionTypeTables.Active().Where(x => x.SolutionType.ToLower().Contains(solutionType.ToLower())).ToListAsync();
            return result;
            
        }
    }
}
