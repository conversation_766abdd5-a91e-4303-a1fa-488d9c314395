﻿
namespace ContinuityPatrol.Application.Features.WorkflowExecutionTemp.Commands.DeleteByWorkflowId;

public class DeleteWorkflowByWorkflowIdCommandHandler : IRequestHandler<DeleteWorkflowByWorkflowIdCommand, DeleteWorkflowByWorkflowIdResponse>
{
    private readonly IWorkflowExecutionTempRepository _workflowExecutionTempRepository;
    public DeleteWorkflowByWorkflowIdCommandHandler(IWorkflowExecutionTempRepository workflowExecutionTempRepository)
    {
        _workflowExecutionTempRepository = workflowExecutionTempRepository;
    }

    public async Task<DeleteWorkflowByWorkflowIdResponse> Handle(DeleteWorkflowByWorkflowIdCommand request, CancellationToken cancellationToken)
    {
        var eventToDelete = await _workflowExecutionTempRepository.FilterByWorkflowId(request.WorkflowId);

        if (eventToDelete.Any())
        {
            eventToDelete.ForEach(x=>x.IsActive = false);

            await _workflowExecutionTempRepository.UpdateRange(eventToDelete);

            var response = new DeleteWorkflowByWorkflowIdResponse
            {
                Message = "Custom workflow deleted successfully.",

                IsActive = false
            };

            return response;

        }

        var existingWorkflow = eventToDelete.FirstOrDefault();

        Guard.Against.Null(existingWorkflow, nameof(Domain.Entities.WorkflowExecutionTemp),
            new NotFoundException(nameof(Domain.Entities.WorkflowExecutionTemp), request.WorkflowId));

        return new DeleteWorkflowByWorkflowIdResponse
        {
            Message = "Custom workflow not found."
        };

    }
}