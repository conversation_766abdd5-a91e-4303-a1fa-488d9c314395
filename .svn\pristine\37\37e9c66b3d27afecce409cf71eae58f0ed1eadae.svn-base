﻿using ContinuityPatrol.Application.Features.PageWidget.Commands.Update;
using ContinuityPatrol.Application.Features.PageWidget.Events.Update;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Application.UnitTests.Features.PageWidget.Commands
{
    public class UpdatePageWidgetTests
    {
        private readonly Mock<IPageWidgetRepository> _mockPageWidgetRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<IMapper> _mockMapper;
        private readonly UpdatePageWidgetCommandHandler _handler;

        public UpdatePageWidgetTests()
        {
            _mockPageWidgetRepository = new Mock<IPageWidgetRepository>();
            _mockPublisher = new Mock<IPublisher>();
            _mockMapper = new Mock<IMapper>();
            _handler = new UpdatePageWidgetCommandHandler(_mockMapper.Object, _mockPageWidgetRepository.Object, _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnResponse_WhenPageWidgetUpdatedSuccessfully()
        {
            var command = new UpdatePageWidgetCommand { Id = "123", Name = "Updated Widget" };

            var existingPageWidget = new Domain.Entities.PageWidget
            {
                ReferenceId = "123",
                Name = "Old Widget"
            };
            _mockPageWidgetRepository.Setup(r => r.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(existingPageWidget);

            _mockPageWidgetRepository.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.PageWidget>()))
                .Returns(ToString);

            _mockMapper.Setup(m => m.Map(command, existingPageWidget, typeof(UpdatePageWidgetCommand), typeof(Domain.Entities.PageWidget)))
                .Callback(() => existingPageWidget.Name = command.Name);

            var expectedResponse = new UpdatePageWidgetResponse
            {
                Message = Message.Update(nameof(PageWidget), command.Name),
                Id = existingPageWidget.ReferenceId
            };

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedResponse.Message, result.Message);
            Assert.Equal(expectedResponse.Id, result.Id);

            _mockPageWidgetRepository.Verify(r => r.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockPageWidgetRepository.Verify(r => r.UpdateAsync(existingPageWidget), Times.Once);
            _mockMapper.Verify(m => m.Map(command, existingPageWidget, typeof(UpdatePageWidgetCommand), typeof(Domain.Entities.PageWidget)), Times.Once);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<PageWidgetUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenPageWidgetDoesNotExist()
        {
            var command = new UpdatePageWidgetCommand { Id = "123", Name = "Updated Widget" };

            _mockPageWidgetRepository.Setup(r => r.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync((Domain.Entities.PageWidget)null);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Equal(nameof(PageWidget), exception.Source);

            _mockPageWidgetRepository.Verify(r => r.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockPageWidgetRepository.Verify(r => r.UpdateAsync(It.IsAny<Domain.Entities.PageWidget>()), Times.Never);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<PageWidgetUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldPublishEvent_WhenPageWidgetUpdatedSuccessfully()
        {
            var command = new UpdatePageWidgetCommand { Id = "123", Name = "Updated Widget" };

            var existingPageWidget = new Domain.Entities.PageWidget
            {
                ReferenceId = "123",
                Name = "Old Widget"
            };

            _mockPageWidgetRepository.Setup(r => r.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(existingPageWidget);

            _mockPageWidgetRepository.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.PageWidget>()))
                .Returns(ToString);

            _mockMapper.Setup(m => m.Map(command, existingPageWidget, typeof(UpdatePageWidgetCommand), typeof(Domain.Entities.PageWidget)))
                .Callback(() => existingPageWidget.Name = command.Name);

            await _handler.Handle(command, CancellationToken.None);

            _mockPublisher.Verify(p => p.Publish(It.Is<PageWidgetUpdatedEvent>(e => e.Name == command.Name), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}
