using AutoFixture;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class LoadBalancerRepositoryTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly LoadBalancerRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly LoadBalancerFixture _fixture;
    private readonly Fixture _autoFixture;

    public LoadBalancerRepositoryTests()
    {
        _context = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _fixture = new LoadBalancerFixture();
        _autoFixture = new Fixture();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(LoadBalancerFixture.CompanyId);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        _repository = new LoadBalancerRepository(_context, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _context?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _context.NodeConfigurations.RemoveRange(_context.NodeConfigurations);
        _context.MonitorServices.RemoveRange(_context.MonitorServices);
        _context.Jobs.RemoveRange(_context.Jobs);
        _context.ReplicationJobs.RemoveRange(_context.ReplicationJobs);
        _context.InfraObjectSchedulers.RemoveRange(_context.InfraObjectSchedulers);
        _context.WorkflowOperationGroups.RemoveRange(_context.WorkflowOperationGroups);
        await _context.SaveChangesAsync();
    }

    #region Basic Repository Methods

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveLoadBalancers()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancers = new List<LoadBalancer>
        {
            _fixture.LoadBalancerDto,
            _autoFixture.Create<LoadBalancer>(),
            _autoFixture.Create<LoadBalancer>()
        };

        await _context.NodeConfigurations.AddRangeAsync(loadBalancers);
        await _context.SaveChangesAsync();
        loadBalancers[2].IsActive = false; // Make one inactive
        _context.NodeConfigurations.Update(loadBalancers[2]);
        _context.SaveChanges();
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only active entities
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnLoadBalancer_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.ReferenceId = "41e6c8ca-8c72-4e31-a572-63c0051d4f06";
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();
        string refernceid= "41e6c8ca-8c72-4e31-a572-63c0051d4f06";
        // Act
        var result = await _repository.GetByReferenceIdAsync(refernceid);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(loadBalancer.ReferenceId, result.ReferenceId);
        Assert.Equal(loadBalancer.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        await ClearDatabase();
        string refernceid = "41e6c8ca-8c72-4e31-a572-63c0051d4f06";

        // Act
        var result = await _repository.GetByReferenceIdAsync(refernceid);

        // Assert
        Assert.Null(result);
    }

    

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldWorkWithParentUser()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.ReferenceId= "41e6c8ca-8c72-4e31-a572-63c0051d4f06";
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();
        string refernceid = "41e6c8ca-8c72-4e31-a572-63c0051d4f06";

        // Act
        var result = await _repository.GetByReferenceIdAsync(refernceid);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(loadBalancer.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldWorkWithNonParentUser()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.ReferenceId = "41e6c8ca-8c72-4e31-a572-63c0051d4f06";
        string refernceid = "41e6c8ca-8c72-4e31-a572-63c0051d4f06";

        loadBalancer.CompanyId = LoadBalancerFixture.CompanyId; // Same company
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(refernceid);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(loadBalancer.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenDifferentCompanyForNonParent()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("DIFFERENT_COMPANY");
        string refernceid = "41e6c8ca-8c72-4e31-a572-63c0051d4f06";

        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.CompanyId = LoadBalancerFixture.CompanyId; // Different company
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(refernceid);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnOrderedQueryable()
    {
        // Arrange
        var loadBalancers = new List<LoadBalancer>
        {
            _fixture.LoadBalancerDto,
            _autoFixture.Create<LoadBalancer>()
        };

        _context.NodeConfigurations.AddRange(loadBalancers);
        _context.SaveChanges();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IQueryable<LoadBalancer>>(result);
        
        var materializedResult = result.ToList();
        Assert.Equal(2, materializedResult.Count);
    }

    #endregion

    #region IsNodeConfigurationNameExist Tests

    [Fact]
    public async Task IsNodeConfigurationNameExist_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNodeConfigurationNameExist(loadBalancer.Name, "NEW_ID");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNodeConfigurationNameExist_ShouldReturnFalse_WhenNameNotExists()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsNodeConfigurationNameExist("NON_EXISTENT_NAME", "NEW_ID");

        // Assert
        Assert.False(result);
    }

  

    [Fact]
    public async Task IsNodeConfigurationNameExist_ShouldReturnTrue_WhenInvalidGuid()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNodeConfigurationNameExist(loadBalancer.Name, "INVALID_GUID");

        // Assert
        Assert.True(result);
    }

    #endregion

    #region IsNodeConfigurationNameUnique Tests

   

    [Fact]
    public async Task IsNodeConfigurationNameUnique_ShouldReturnFalse_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNodeConfigurationNameUnique(loadBalancer.Name);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region GetNodeConfigurationListById Tests

    [Fact]
    public async Task GetNodeConfigurationListById_ShouldReturnMatchingLoadBalancers()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer1 = _fixture.LoadBalancerDto;
        var loadBalancer2 = _autoFixture.Create<LoadBalancer>();
        var loadBalancer3 = _autoFixture.Create<LoadBalancer>();

        await _context.NodeConfigurations.AddRangeAsync(loadBalancer1, loadBalancer2, loadBalancer3);
        await _context.SaveChangesAsync();

        var ids = new List<string> { loadBalancer1.ReferenceId, loadBalancer2.ReferenceId };

        // Act
        var result = await _repository.GetNodeConfigurationListById(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.ReferenceId == loadBalancer1.ReferenceId);
        Assert.Contains(result, x => x.ReferenceId == loadBalancer2.ReferenceId);
    }

    [Fact]
    public async Task GetNodeConfigurationListById_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var ids = new List<string> { "NON_EXISTENT_ID1", "NON_EXISTENT_ID2" };

        // Act
        var result = await _repository.GetNodeConfigurationListById(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetNodeConfigurationListById_ShouldReturnEmpty_WhenEmptyList()
    {
        // Arrange
        await ClearDatabase();
        var ids = new List<string>();

        // Act
        var result = await _repository.GetNodeConfigurationListById(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetNodeConfigurationByTypeAndTypeCategory Tests

    [Fact]
    public async Task GetNodeConfigurationByTypeAndTypeCategory_ShouldReturnMatchingLoadBalancer()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.Type = "TestType";
        loadBalancer.TypeCategory = "TestCategory";
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetNodeConfigurationByTypeAndTypeCategory("TestType", "TestCategory");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(loadBalancer.ReferenceId, result.ReferenceId);
        Assert.Equal("TestType", result.Type);
        Assert.Equal("TestCategory", result.TypeCategory);
    }

    [Fact]
    public async Task GetNodeConfigurationByTypeAndTypeCategory_ShouldReturnNull_WhenNoMatch()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetNodeConfigurationByTypeAndTypeCategory("NonExistentType", "NonExistentCategory");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetLoadBalancerType Tests

    [Fact]
    public async Task GetLoadBalancerType_ShouldReturnMatchingTypes_WhenTypeProvided()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer1 = _fixture.LoadBalancerDto;
        loadBalancer1.Type = "Web Server";
        var loadBalancer2 = _autoFixture.Create<LoadBalancer>();
        loadBalancer2.Type = "Web Server";
        var loadBalancer3 = _autoFixture.Create<LoadBalancer>();
        loadBalancer3.Type = "Database Server";

        await _context.NodeConfigurations.AddRangeAsync(loadBalancer1, loadBalancer2, loadBalancer3);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLoadBalancerType("WebServer");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal("Web Server", x.Type));
    }

    [Fact]
    public async Task GetLoadBalancerType_ShouldReturnDistinctTypes_WhenTypeIsNull()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer1 = _fixture.LoadBalancerDto;
        loadBalancer1.Type = "Web Server";
        var loadBalancer2 = _autoFixture.Create<LoadBalancer>();
        loadBalancer2.Type = "Database Server";

        await _context.NodeConfigurations.AddRangeAsync(loadBalancer1, loadBalancer2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLoadBalancerType(null);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count >= 2);
    }

    [Fact]
    public async Task GetLoadBalancerType_ShouldReturnEmpty_WhenNoMatchingType()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.Type = "Web Server";
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLoadBalancerType("NonExistentType");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetLoadBalancerByType Tests

    [Fact]
    public async Task GetLoadBalancerByType_ShouldReturnMatchingLoadBalancers_ExcludingSpecifiedId()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer1 = _fixture.LoadBalancerDto;
        loadBalancer1.Type = "Web Server";
        var loadBalancer2 = _autoFixture.Create<LoadBalancer>();
        loadBalancer2.Type = "Web Server";
        var loadBalancer3 = _autoFixture.Create<LoadBalancer>();
        loadBalancer3.Type = "Database Server";

        await _context.NodeConfigurations.AddRangeAsync(loadBalancer1, loadBalancer2, loadBalancer3);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLoadBalancerByType(loadBalancer1.ReferenceId, "Web Server");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(loadBalancer2.ReferenceId, result.First().ReferenceId);
        Assert.DoesNotContain(result, x => x.ReferenceId == loadBalancer1.ReferenceId);
    }

    [Fact]
    public async Task GetLoadBalancerByType_ShouldReturnEmpty_WhenNoMatchingType()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.Type = "Web Server";
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLoadBalancerByType(loadBalancer.ReferenceId, "Database Server");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetNodeNameByIdAsync Tests

    [Fact]
    public async Task GetNodeNameByIdAsync_ShouldReturnMatchingNodes()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer1 = _fixture.LoadBalancerDto;
        var loadBalancer2 = _autoFixture.Create<LoadBalancer>();
        var loadBalancer3 = _autoFixture.Create<LoadBalancer>();

        await _context.NodeConfigurations.AddRangeAsync(loadBalancer1, loadBalancer2, loadBalancer3);
        await _context.SaveChangesAsync();

        var ids = new List<string> { loadBalancer1.ReferenceId, loadBalancer2.ReferenceId };

        // Act
        var result = await _repository.GetNodeNameByIdAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.NotNull(x.Name));
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
        Assert.Contains(result, x => x.ReferenceId == loadBalancer1.ReferenceId);
        Assert.Contains(result, x => x.ReferenceId == loadBalancer2.ReferenceId);
    }

    [Fact]
    public async Task GetNodeNameByIdAsync_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var ids = new List<string> { "NON_EXISTENT_ID1", "NON_EXISTENT_ID2" };

        // Act
        var result = await _repository.GetNodeNameByIdAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetNodeNameByIdAsync_ShouldReturnEmpty_WhenEmptyList()
    {
        // Arrange
        await ClearDatabase();
        var ids = new List<string>();

        // Act
        var result = await _repository.GetNodeNameByIdAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetNamesByType Tests

    [Fact]
    public async Task GetNamesByType_ShouldReturnMatchingLoadBalancers()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer1 = _fixture.LoadBalancerDto;
        loadBalancer1.Type = "Web Server";
        var loadBalancer2 = _autoFixture.Create<LoadBalancer>();
        loadBalancer2.Type = "Web Server";
        var loadBalancer3 = _autoFixture.Create<LoadBalancer>();
        loadBalancer3.Type = "Database Server";

        await _context.NodeConfigurations.AddRangeAsync(loadBalancer1, loadBalancer2, loadBalancer3);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetNamesByType("Web Server");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal("Web Server", x.Type));
        Assert.All(result, x => Assert.NotNull(x.Name));
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
    }

    [Fact]
    public async Task GetNamesByType_ShouldReturnEmpty_WhenNoMatchingType()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.Type = "Web Server";
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetNamesByType("Database Server");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNodeConfigurationIpAddressAndPortExist Tests

    [Fact]
    public async Task IsNodeConfigurationIpAddressAndPortExist_ShouldReturnTrue_WhenIpAndPortExist()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.IPAddress = "*************";
        loadBalancer.Port = 8080;
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNodeConfigurationIpAddressAndPortExist("*************", 8080, "NEW_ID");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNodeConfigurationIpAddressAndPortExist_ShouldReturnFalse_WhenIpAndPortNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsNodeConfigurationIpAddressAndPortExist("*************", 9090, "NEW_ID");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNodeConfigurationIpAddressAndPortExist_ShouldReturnFalse_WhenSameIdIpAndPort()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.IPAddress = "*************";
        loadBalancer.Port = 8080;
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();
        string refernceid = "41e6c8ca-8c72-4e31-a572-63c0051d4f06";

        // Act
        var result = await _repository.IsNodeConfigurationIpAddressAndPortExist("*************", 8080, refernceid);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region GetActiveNodeAndInActiveNodeByType Tests

    [Fact]
    public async Task GetActiveNodeAndInActiveNodeByType_ShouldReturnCorrectActiveAndInactiveNodes()
    {
        // Arrange
        await ClearDatabase();
        var activeLoadBalancer = _fixture.LoadBalancerDto;
        activeLoadBalancer.Type = "Web Server";
        activeLoadBalancer.TypeCategory = "CP Node"; // Must be CP Node
        activeLoadBalancer.HealthStatus = "Active"; // Active health status
        activeLoadBalancer.Name = "ActiveNode1";

        var inactiveLoadBalancer = _autoFixture.Create<LoadBalancer>();
        inactiveLoadBalancer.Type = "Web Server";
        inactiveLoadBalancer.TypeCategory = "CP Node"; // Must be CP Node
        inactiveLoadBalancer.HealthStatus = "Inactive"; // Inactive health status
        inactiveLoadBalancer.Name = "InactiveNode1";

        await _context.NodeConfigurations.AddRangeAsync(activeLoadBalancer, inactiveLoadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetActiveNodeAndInActiveNodeByType("WebServer");

        // Assert
        Assert.Single(result.ActiveNodes);
        Assert.Single(result.InActiveNodes);
        Assert.Contains("ActiveNode1", result.ActiveNodes); // Returns Name, not ReferenceId
        Assert.Contains("InactiveNode1", result.InActiveNodes); // Returns Name, not ReferenceId
    }

    [Fact]
    public async Task GetActiveNodeAndInActiveNodeByType_ShouldReturnEmpty_WhenNoMatchingType()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.Type = "Web Server";
        loadBalancer.TypeCategory = "CP Node";
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetActiveNodeAndInActiveNodeByType("DatabaseServer");

        // Assert
        Assert.Empty(result.ActiveNodes);
        Assert.Empty(result.InActiveNodes);
    }

    [Fact]
    public async Task GetActiveNodeAndInActiveNodeByType_ShouldFilterByTypeCategoryOnly()
    {
        // Arrange
        await ClearDatabase();
        var cpNodeLoadBalancer = _fixture.LoadBalancerDto;
        cpNodeLoadBalancer.Type = "Web Server";
        cpNodeLoadBalancer.TypeCategory = "CP Node"; // Should be included
        cpNodeLoadBalancer.HealthStatus = "Active";
        cpNodeLoadBalancer.Name = "CPNode1";

        var otherLoadBalancer = _autoFixture.Create<LoadBalancer>();
        otherLoadBalancer.Type = "Web Server";
        otherLoadBalancer.TypeCategory = "Other Category"; // Should be excluded
        otherLoadBalancer.HealthStatus = "Active";
        otherLoadBalancer.Name = "OtherNode1";

        await _context.NodeConfigurations.AddRangeAsync(cpNodeLoadBalancer, otherLoadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetActiveNodeAndInActiveNodeByType("WebServer");

        // Assert
        Assert.Single(result.ActiveNodes);
        Assert.Empty(result.InActiveNodes);
        Assert.Contains("CPNode1", result.ActiveNodes);
        Assert.DoesNotContain("OtherNode1", result.ActiveNodes);
    }

    [Fact]
    public async Task GetActiveNodeAndInActiveNodeByType_ShouldHandleTypeWithSpaces()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.Type = "Web Server"; // Has space
        loadBalancer.TypeCategory = "CP Node";
        loadBalancer.HealthStatus = "Active";
        loadBalancer.Name = "WebServerNode";

        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act - Search with spaces removed
        var result = await _repository.GetActiveNodeAndInActiveNodeByType("WebServer");

        // Assert
        Assert.Single(result.ActiveNodes);
        Assert.Contains("WebServerNode", result.ActiveNodes);
    }

    [Fact]
    public async Task GetActiveNodeAndInActiveNodeByType_ShouldBeCaseInsensitive()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.Type = "Web Server";
        loadBalancer.TypeCategory = "cp node"; // lowercase
        loadBalancer.HealthStatus = "ACTIVE"; // uppercase
        loadBalancer.Name = "TestNode";

        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetActiveNodeAndInActiveNodeByType("webserver");

        // Assert
        Assert.Single(result.ActiveNodes);
        Assert.Contains("TestNode", result.ActiveNodes);
    }

    [Fact]
    public async Task GetActiveNodeAndInActiveNodeByType_ShouldHandleVariousHealthStatuses()
    {
        // Arrange
        await ClearDatabase();
        var activeNode1 = _fixture.LoadBalancerDto;
        activeNode1.Type = "Web Server";
        activeNode1.TypeCategory = "CP Node";
        activeNode1.HealthStatus = "active"; // lowercase active
        activeNode1.Name = "ActiveNode1";

        var activeNode2 = _autoFixture.Create<LoadBalancer>();
        activeNode2.Type = "Web Server";
        activeNode2.TypeCategory = "CP Node";
        activeNode2.HealthStatus = "ACTIVE"; // uppercase active
        activeNode2.Name = "ActiveNode2";

        var inactiveNode1 = _autoFixture.Create<LoadBalancer>();
        inactiveNode1.Type = "Web Server";
        inactiveNode1.TypeCategory = "CP Node";
        inactiveNode1.HealthStatus = "inactive"; // inactive
        inactiveNode1.Name = "InactiveNode1";

        var inactiveNode2 = _autoFixture.Create<LoadBalancer>();
        inactiveNode2.Type = "Web Server";
        inactiveNode2.TypeCategory = "CP Node";
        inactiveNode2.HealthStatus = "down"; // other status
        inactiveNode2.Name = "InactiveNode2";

        await _context.NodeConfigurations.AddRangeAsync(activeNode1, activeNode2, inactiveNode1, inactiveNode2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetActiveNodeAndInActiveNodeByType("WebServer");

        // Assert
        Assert.Equal(2, result.ActiveNodes.Count);
        Assert.Equal(2, result.InActiveNodes.Count);
        Assert.Contains("ActiveNode1", result.ActiveNodes);
        Assert.Contains("ActiveNode2", result.ActiveNodes);
        Assert.Contains("InactiveNode1", result.InActiveNodes);
        Assert.Contains("InactiveNode2", result.InActiveNodes);
    }

    #endregion

    #region IsNodeInUse Tests

    [Fact]
    public async Task IsNodeInUse_ShouldReturnTrue_WhenNodeIsUsedInMonitorServices()
    {
        // Arrange
        await ClearDatabase();
        var nodeId = Guid.NewGuid().ToString();

        var monitorService = _autoFixture.Create<MonitorService>();
        monitorService.NodeId = nodeId;
        monitorService.Status = "running";
        monitorService.IsActive = true;

        await _context.MonitorServices.AddAsync(monitorService);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNodeInUse(nodeId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNodeInUse_ShouldReturnTrue_WhenNodeIsUsedInJobs()
    {
        // Arrange
        await ClearDatabase();
        var nodeId = Guid.NewGuid().ToString();

        var job = _autoFixture.Create<ContinuityPatrol.Domain.Entities.Job>();
        job.NodeId = nodeId;
        job.Status = "running";
        job.IsActive = true;

        await _context.Jobs.AddAsync(job);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNodeInUse(nodeId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNodeInUse_ShouldReturnFalse_WhenNodeIsNotInUse()
    {
        // Arrange
        await ClearDatabase();
        var nodeId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsNodeInUse(nodeId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNodeInUse_ShouldReturnFalse_WhenNodeHasInactiveServices()
    {
        // Arrange
        await ClearDatabase();
        var nodeId = Guid.NewGuid().ToString();

        var monitorService = _autoFixture.Create<MonitorService>();
        monitorService.NodeId = nodeId;
        monitorService.Status = "stopped";
        monitorService.IsActive = true;

        await _context.MonitorServices.AddAsync(monitorService);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNodeInUse(nodeId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancers = new List<LoadBalancer>
        {
            _fixture.LoadBalancerDto,
            _autoFixture.Create<LoadBalancer>(),
            _autoFixture.Create<LoadBalancer>()
        };

        await _context.NodeConfigurations.AddRangeAsync(loadBalancers);
        await _context.SaveChangesAsync();
        string? searchString = null;

        var specification = new LoadBalancerFilterSpecification(searchString);
        // Act
        var result = await _repository.PaginatedListAllAsync(1, 2, specification, "Id", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Data.Count);
        Assert.Equal(3, result.TotalCount);
        Assert.Equal(2, result.PageSize);
    }

    #endregion

    #region Error Handling and Edge Cases

 

    [Fact]
    public async Task GetNodeConfigurationListById_ShouldHandleNullList()
    {
        // Act
        var result = await _repository.GetNodeConfigurationListById(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetNodeNameByIdAsync_ShouldHandleNullList()
    {
        // Act
        var result = await _repository.GetNodeNameByIdAsync(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetLoadBalancerType_ShouldHandleEmptyString()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetLoadBalancerType("");

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetLoadBalancerType_ShouldHandleWhitespace()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetLoadBalancerType("   ");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }



    [Fact]
    public async Task IsNodeConfigurationIpAddressAndPortExist_ShouldHandleInvalidPort()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsNodeConfigurationIpAddressAndPortExist("*************", -1, "SOME_ID");

        // Assert
        Assert.False(result);
    }

  

    [Fact]
    public async Task IsNodeInUse_ShouldHandleEmptyNodeId()
    {
        // Act
        var result = await _repository.IsNodeInUse("");

        // Assert
        Assert.False(result);
    }

    

    #endregion

    #region Integration Tests

    [Fact]
    public async Task IsNodeInUse_ShouldCheckAllRelatedTables()
    {
        // Arrange
        await ClearDatabase();
        var nodeId = Guid.NewGuid().ToString();

        // Add entities in different tables that use the node
        var replicationJob = _autoFixture.Create<ReplicationJob>();
        replicationJob.NodeId = nodeId;
        replicationJob.Status = "running";
        replicationJob.IsActive = true;

        var infraObjectScheduler = _autoFixture.Create<InfraObjectScheduler>();
        infraObjectScheduler.NodeId = nodeId;
        infraObjectScheduler.Status = "running";
        infraObjectScheduler.IsActive = true;

        var workflowOperationGroup = _autoFixture.Create<WorkflowOperationGroup>();
        workflowOperationGroup.NodeId = nodeId;
        workflowOperationGroup.Status = "running";
        workflowOperationGroup.IsActive = true;

        await _context.ReplicationJobs.AddAsync(replicationJob);
        await _context.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _context.WorkflowOperationGroups.AddAsync(workflowOperationGroup);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNodeInUse(nodeId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task CompleteWorkflow_ShouldWorkEndToEnd()
    {
        // Arrange
        await ClearDatabase();
        var loadBalancer = _fixture.LoadBalancerDto;
        loadBalancer.ReferenceId = "41e6c8ca-8c72-4e31-a572-63c0051d4f06";
        loadBalancer.Type = "Web Server";
        loadBalancer.Name = "TestLoadBalancer";
        loadBalancer.IPAddress = "*************";
        loadBalancer.Port = 8080;
        string refernceid = "41e6c8ca-8c72-4e31-a572-63c0051d4f06";
        await _context.NodeConfigurations.AddAsync(loadBalancer);
        await _context.SaveChangesAsync();

        // Act & Assert - Test complete workflow

        // 1. Check if name exists
        var nameExists = await _repository.IsNodeConfigurationNameExist(loadBalancer.Name, "NEW_ID");
        Assert.True(nameExists);

        // 2. Check if IP and port exist
        var ipPortExists = await _repository.IsNodeConfigurationIpAddressAndPortExist(loadBalancer.IPAddress, loadBalancer.Port, "NEW_ID");
        Assert.True(ipPortExists);

        // 3. Get by reference ID
        var retrieved = await _repository.GetByReferenceIdAsync(refernceid);
        Assert.NotNull(retrieved);
        Assert.Equal(loadBalancer.Name, retrieved.Name);

        // 4. Get by type
        var byType = await _repository.GetLoadBalancerByType("OTHER_ID", "Web Server");
        Assert.Single(byType);

        // 5. Check if node is in use
        var inUse = await _repository.IsNodeInUse(refernceid);
        Assert.False(inUse); // Should be false as no services are using it

        // 6. Get names by type
        var namesByType = await _repository.GetNamesByType("Web Server");
        Assert.Single(namesByType);
        Assert.Equal(loadBalancer.Name, namesByType.First().Name);
    }

    #endregion
}
