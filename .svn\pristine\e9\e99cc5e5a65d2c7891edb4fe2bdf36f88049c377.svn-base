using ContinuityPatrol.Domain.ViewModels.MenuBuilderModel;

namespace ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetList;

public class GetMenuBuilderListQueryHandler : IRequestHandler<GetMenuBuilderListQuery, List<MenuBuilderListVm>>
{
    private readonly IMenuBuilderRepository _menuBuilderRepository;
    private readonly IMapper _mapper;

    public GetMenuBuilderListQueryHandler(IMapper mapper, IMenuBuilderRepository menuBuilderRepository)
    {
        _mapper = mapper;
        _menuBuilderRepository = menuBuilderRepository;
    }

    public async Task<List<MenuBuilderListVm>> Handle(GetMenuBuilderListQuery request, CancellationToken cancellationToken)
    {
        var menuBuilders = await _menuBuilderRepository.ListAllAsync();

        if (menuBuilders.Count <= 0) return new List<MenuBuilderListVm>();

        return _mapper.Map<List<MenuBuilderListVm>>(menuBuilders);
    }
}
