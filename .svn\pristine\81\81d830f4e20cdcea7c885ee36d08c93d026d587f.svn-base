<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>WorkFlowList QUnit Test</title>
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.20.0.css">
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://code.jquery.com/qunit/qunit-2.20.0.js"></script>

    <!-- Include your application script -->
    <script>
        let selectedValues = [];
        let globalUserName = "adminuser";
        let createPermission = true;
        let deletePermission = true;

        const workflowList = {
            getPagination: "/ITAutomation/WorkflowList/GetPagination",
            workflowConfiguration: "/ITAutomation/WorkflowConfiguration/List",
            getReport: "ITAutomation/WorkflowConfiguration/GetRunBookReport"
        };

        function renderSpan(value) {
            value = value || 'NA';
            return `<span title="${value}">${value}</span>`;
        }
    </script>
</head>
<body>
    <h1 id="qunit-header">WorkFlowList QUnit Tests</h1>
    <div id="qunit"></div>
    <div id="qunit-fixture">
        <input id="wfListSearch" type="text" />
        <table id="WorkFlowList"><tbody></tbody></table>
    </div>

    <!-- Load test file -->
    <script src="../../js/WorkflowList/WorkFlowList.qunit.test.js"></script>
</body>
</html>
