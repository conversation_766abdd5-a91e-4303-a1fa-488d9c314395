using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberComponentGroupModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class CyberComponentGroupsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<List<CyberComponentGroupListVm>>> GetCyberComponentGroups()
    {
        Logger.LogDebug("Get All CyberComponentGroups");

        return Ok(await Mediator.Send(new GetCyberComponentGroupListQuery()));
    }

    [HttpGet("{id}", Name = "GetCyberComponentGroup")]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<CyberComponentGroupDetailVm>> GetCyberComponentGroupById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberComponentGroup Id");

        Logger.LogDebug($"Get CyberComponentGroup Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetCyberComponentGroupDetailQuery { Id = id }));
    }
    #region Paginated
    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<PaginatedResult<CyberComponentGroupListVm>>> GetPaginatedCyberComponentGroups([FromQuery] GetCyberComponentGroupPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in CyberComponentGroup Paginated List");

        return Ok(await Mediator.Send(query));
    }
    #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Cyber.Create)]
    public async Task<ActionResult<CreateCyberComponentGroupResponse>> CreateCyberComponentGroup([FromBody] CreateCyberComponentGroupCommand createCyberComponentGroupCommand)
    {
        Logger.LogDebug($"Create CyberComponentGroup '{createCyberComponentGroupCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateCyberComponentGroup), await Mediator.Send(createCyberComponentGroupCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Cyber.Edit)]
    public async Task<ActionResult<UpdateCyberComponentGroupResponse>> UpdateCyberComponentGroup([FromBody] UpdateCyberComponentGroupCommand updateCyberComponentGroupCommand)
    {
        Logger.LogDebug($"Update CyberComponentGroup '{updateCyberComponentGroupCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateCyberComponentGroupCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Cyber.Delete)]
    public async Task<ActionResult<DeleteCyberComponentGroupResponse>> DeleteCyberComponentGroup(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberComponentGroup Id");

        Logger.LogDebug($"Delete CyberComponentGroup Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteCyberComponentGroupCommand { Id = id }));
    }

     #region NameExist

    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsCyberComponentGroupNameExist(string cyberComponentGroupName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(cyberComponentGroupName, "CyberComponentGroup Name");
   
        Logger.LogDebug($"Check Name Exists Detail by CyberComponentGroup Name '{cyberComponentGroupName}' and Id '{id}'");
   
        return Ok(await Mediator.Send(new GetCyberComponentGroupNameUniqueQuery { Name = cyberComponentGroupName, Id = id }));
    }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


