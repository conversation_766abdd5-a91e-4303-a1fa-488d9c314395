﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.BusinessService.Queries.GetBusinessServiceDiagramDetail;

public record GetBusinessServiceDiagramDetailVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }

    public List<BusinessFunctionDataLag> BusinessFunctionDataLag { get; set; } = new();
}

public record BusinessFunctionDataLag
{
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string BusinessServiceId { get; set; }

    public List<InfraObjectDataLag> InfraObjectDataLag { get; set; } = new();
}

public record InfraObjectDataLag
{
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string ReplicationTypeName { get; set; }
    public string ReplicationCategoryType { get; set; }
    [JsonIgnore] public string ServerProperties { get; set; }
    [JsonIgnore] public string DatabaseProperties { get; set; }
    [JsonIgnore] public string ReplicationProperties { get; set; }
    [JsonIgnore] public int DROperationStatus { get; set; }
    [JsonIgnore] public int ReplicationStatus { get; set; }
    [JsonIgnore] public string BusinessFunctionId { get; set; }
    [JsonIgnore] public string ReplicationTypeId { get; set; }
    [JsonIgnore] public string SubType { get; set; }

    public List<ServerDtoVm> ServerDtoVm { get; set; } = new();
    public List<DatabaseDtoVm> DatabaseDtoVm { get; set; } = new();
    public List<ReplicationDtoVm> ReplicationDtoVm { get; set; } = new();
}

public record ServerDtoVm
{
    public string ServerId { get; set; }
    public string ServerName { get; set; }
    public string OSType { get; set; }
    public string Status { get; set; }
    public string IPAddress { get; set; }
    public string HostName { get; set; }
    public string ServerType { get; set; }
    public string Type { get; set; }
    public string NodeName { get; set; }
    public string ConnectViaHostName { get; set; }
    public string ExceptionMessage { get; set; }
    
}

public record DatabaseDtoVm
{
    public string DatabaseId { get; set; }
    public string DatabaseName { get; set; }
    public string OracleSID { get; set; }
    public string ModeType { get; set; }
    public string DatabaseType { get; set; }
    public string Type { get; set; }
    public string NodeName { get; set; }
    public string ExceptionMessage { get; set; }
    
}

public record ReplicationDtoVm
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Type { get; set; }

}