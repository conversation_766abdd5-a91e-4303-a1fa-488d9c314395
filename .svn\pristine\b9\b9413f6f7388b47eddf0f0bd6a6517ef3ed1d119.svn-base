<!DOCTYPE html>
<html lang="en">
<style>   
    .Top_Menu .dropdown-menu, .dropdown-menu, .nav-link.Menu_Icon span {
        --bs-nav-menu-font-size: 0.600rem;
    }
    body {
        margin: 0;
        font-family: Arial, sans-serif;
    }

    .navbar {
        background-color: #f8f9fa;
    }

    .nav-link {
        cursor: pointer;
    }

    iframe {
        width: 100%;
        height: 85vh;
        border: none;
        display: block;
    }
</style>
<head>
    <meta charset="UTF-8">
    <title>ContinuityPatrol UnitTest</title>
    <link href="../../css/Style.css" rel="stylesheet" />
    <link href="../../lib/bootstrap/css/bootstrap.min.css" rel="stylesheet" />
    <link href="../../lib/dataTables/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
    <link href="../../lib/bootstrap-select/css/select2.min.css" rel="stylesheet" />
    <link href="../../css/theme.css" rel="stylesheet" />
    <link href="../../css/Table.css" rel="stylesheet" />
    <link href="../../css/form.css" rel="stylesheet" />
    <link href="../../css/highlight.css" rel="stylesheet" />
    <link href="../../css/Menu.css" rel="stylesheet" />
    <link href="../../fonts/Cp-icons/cp-icon.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light px-3">
        <div class="container-fluid justify-content-between Top_Menu">
            <a class="navbar-brand d-flex align-items-center me-3 p-0" href="#">
                <img class="logo" src="../../img/logo/cplogo.svg" alt="CP Logo" style="height: 34px; width: 140px;" />
            </a>
            <div class="collapse navbar-collapse justify-content-center" id="navbarSupportedContent">
                <ul class="navbar-nav align-items-center gap-1">
                    <li class="nav-item dropdown" id="DashboardNavbar">
                        <a class="nav-link Menu_Icon " aria-current="page" id="dashboard-link" href="#">
                            <span class="icon cp-dashboard me-1"></span><span>Dashboard</span>
                        </a>
                        <ul class="dropdown-menu btnDropDown" id="dashboard-dropdownList">
                            <li class="nav-item">
                                <a href="#" class="nav-link dropdown-item ">Service Availability</a>
                            </li>
                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">IT Resiliency View</a></li>
                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">Operational Analytics</a></li>
                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">One View</a></li>
                            <li class="nav-item dropend customDashboard d-none">
                                <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Custom Dashboard</a>
                                <ul class="dropdown-menu customDashboardDropdown"></ul>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link Menu_Icon drift" aria-current="page" id="dashboard-link" href="#">
                            <span class="icon cp-drift_dashboard me-1"></span><span>Drift</span>
                        </a>
                        <ul class="dropdown-menu btnDropDown" id="dashboard-dropdown">
                            <li class="nav-item">
                                <a href="#" class="nav-link dropdown-item ">Drift Dashboard</a>
                            </li>
                            <li class="nav-item dropend disabled">
                                <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Drift Configuration</a>
                                <ul class="dropdown-menu btnDropDown">
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Drift Parameter</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Drift Profile</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Drift Job Management</a></li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link dropdown-item driftreport">Drift Report</a>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link Menu_Icon dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <span class="icon cp-resiliency-readiness me-1"></span><span>Resiliency Readiness</span>
                        </a>
                        <ul class="dropdown-menu btnDropDown" id="dashboard-dropdown">
                            <li class="nav-item">
                                <a href="#" class="nav-link dropdown-item">Resiliency Dashboard</a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link dropdown-item">Manage Resiliency Readiness</a>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link Menu_Icon dropdown-toggle " href="#" id="">
                            <span class="icon cp-cyber-recovery me-1"></span><span>Cyber Resiliency</span>
                        </a>
                        <ul class="dropdown-menu btnDropDown" id="orchestration-dropdown">
                            <li class="nav-item dropend ">
                                <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Configuration</a>
                                <ul class="dropdown-menu btnDropDown">
                                    <li><a class="nav-link dropdown-item ">Airgap</a></li>
                                    <li><a class="nav-link dropdown-item ">Manage</a></li>
                                    <li><a class="nav-link dropdown-item ">Job Management</a></li>
                                    <li><a class="nav-link dropdown-item ">Snap</a></li>
                                </ul>
                            </li>
                            <li><a class="nav-link dropdown-item d">CG Execution Report</a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link Menu_Icon dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <span class="icon cp-IT-automation me-1"></span><span>IT Automation</span>
                        </a>
                        <ul class="dropdown-menu btnDropDown" id="orchestration-dropdown">
                            <li class="nav-item"><a href="#" class="nav-link dropdown-item">Workflow Configuration</a></li>
                            <li class="nav-item"><a href="#" class="nav-link dropdown-item">Workflow Profile Management</a></li>
                            <li class="nav-item"><a href="#" class="nav-link dropdown-item">Workflow Execution</a></li>
                            <!--<li class="nav-item"><a href="/View/WorkflowList/WorkFlowList.qunit.test.html" class="nav-link dropdown-item">Workflow List</a></li>-->
                            <li class="nav-item"><a class="nav-link" onclick="loadTest('ITAutomation/Views/WorkflowList/WorkFlowList.qunit.test.html')">Workflow List</a></li>
                            <li class="nav-item"><a href="#" class="nav-link dropdown-item">Workflow Template</a></li>
                            <li class="nav-item"><a href="#" class="nav-link dropdown-item">Workflow Schedule Execution History</a></li>
                            <li class="nav-item"><a href="#" class="nav-link dropdown-item">User Privileges</a></li>
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a href="#" class="nav-link Menu_Icon">
                            <span class="icon cp-cloud-connect me-1"></span><span>CloudConnect</span>
                        </a>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link Menu_Icon" aria-current="page" id="dashboard-link" href="#">
                            <span class="icon cp-site-admin me-1"></span><span>SiteAdmin</span>
                        </a>
                        <ul class="dropdown-menu btnDropDown" id="dashboard-dropdown">
                            <li class="nav-item dropend">
                                <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Form</a>
                                <ul class="dropdown-menu btnDropDown">
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Component Type</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Server Mapping</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Operation Type</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Form Mapping</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Form Type</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Form Builder</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Infra-Replication Mapping</a></li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link dropdown-item ">ActionBuilder</a>
                            </li>
                            <li class="nav-item dropend">
                                <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Page Builder</a>
                                <ul class="dropdown-menu btnDropDown">
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Configure Widget</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Configure Page</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Solution Mapping</a></li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link dropdown-item Company">Company</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link dropdown-item Users">Users</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link dropdown-item LicenseManager">License Manager</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link dropdown-item settings">Settings</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link dropdown-item GlobalSettings">Global Settings</a>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a href="#" class="nav-link Menu_Icon">
                            <span class="icon cp-company me-1"></span><span>BasicCompany</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link Menu_Icon">
                            <span class="icon cp-remote-login me-1"></span><span>Login</span>
                        </a>
                    </li>
                    <!-- Repeat similar for other menu items, replacing Razor logic with static HTML or comments -->
                    <!-- ... -->
                </ul>
            </div>
            <ul class="navbar-nav align-items-center Active-none">
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link Menu_Icon" id="btnSetting" role="button" data-bs-toggle="dropdown" aria-expanded="false" aria-controls="menu">
                        <span class="icon cp-settings fw-semibold"></span>
                    </a>
                    <div class="dropdown-menu btnDropDown1 Mega-Menu end-0" style="width:45rem">
                        <ul class="list-group list-group-horizontal p-0">
                            <li class="list-group-item flex-fill p-2">
                                <ul class="ps-0 " style="list-style:none">
                                    <li class="Mega-Menu-header"><i class="cp-configure me-1"></i><span class="align-middle">Configure</span></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item ">Company</a></li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Sites</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a href="#" class="nav-link dropdown-item ">Site Location</a></li>
                                            <li><a href="#" class="nav-link dropdown-item ">Site Type</a></li>
                                            <li><a href="#" class="nav-link dropdown-item ">Site</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item ">Operational Service</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item ">Operational Function</a></li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Infra Components</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">Server</a></li>
                                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">Database</a></li>
                                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">Replication</a></li>
                                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">Single Sign-On</a></li>
                                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">Veritas Cluster</a></li>
                                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">HACMP Cluster</a></li>
                                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">DataSync Properties</a></li>
                                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">RoboCopy Options</a></li>
                                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">Rsync Options</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item ">InfraObject</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item  ">Bulk Import</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item ">Bulk Server Credential</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item ">Bulk Database Credential</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item ">DR Calendar</a></li>
                                    <li class="nav-item  dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">FIA/BIA</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">FIA Templates</a></li>
                                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">BIA Rules</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item  dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Incident</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">What if Analysis</a></li>
                                            <li class="nav-item"><a href="#" class="nav-link dropdown-item ">Incident Details</a></li>
                                        </ul>
                                    </li>
                                </ul>
                            </li>
                            <li class="list-group-item flex-fill p-2">
                                <ul class="ps-0" style="list-style:none">
                                    <li class="Mega-Menu-header">
                                        <i class="cp-manage me-1"></i><span class="align-middle">Manage</span>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Job Management</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a href="#" class="nav-link dropdown-item">Monitoring Job</a></li>
                                            <li><a href="#" class="nav-link dropdown-item">Replication Job</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Dashboard Builder</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a href="#" class="nav-link dropdown-item">Tile Configuration</a></li>
                                            <li><a href="#" class="nav-link dropdown-item">Custom Dashboard</a></li>
                                            <li><a href="#" class="nav-link dropdown-item">User Mapping</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#" class="nav-link dropdown-item">Notification Manager</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#" class="nav-link dropdown-item">AD Password Expire</a>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Approval Matrix</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a href="#" class="nav-link dropdown-item">Approvers</a></li>
                                            <li><a href="#" class="nav-link dropdown-item">Template</a></li>
                                            <li><a href="#" class="nav-link dropdown-item">Request</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#" class="nav-link dropdown-item">Escalation Matrix</a>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">PreRequisites</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a href="#" class="nav-link dropdown-item">Parameter</a></li>
                                            <li><a href="#" class="nav-link dropdown-item">Profile</a></li>
                                            <li><a href="#" class="nav-link dropdown-item">Scanner</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#" class="nav-link dropdown-item">Manage Operational Service</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#" class="nav-link dropdown-item">Monitoring Services</a>
                                    </li>
                                </ul>
                            </li>
                            <li class="list-group-item flex-fill p-2">
                                <ul class="ps-0" style="list-style:none">
                                    <li class="Mega-Menu-header">
                                        <i class="cp-user me-1"></i><span class="align-middle">Admin</span>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Manager</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a class="nav-link" onclick="loadTest('Admin/Views/AccessManager/AccessManager.IfElseCheck.test.html')">Access Manager</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">License</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a href="#" class="nav-link dropdown-item">License Manager</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">User Management</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a href="#" class="nav-link dropdown-item">Users</a></li>
                                            <li><a href="#" class="nav-link dropdown-item">User Role</a></li>
                                            <li><a href="#" class="nav-link dropdown-item">User Group</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Dataset</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a class="nav-link" onclick="loadTest('Admin/Views/TableAccess/TableAccess.qunit.test.html')">Table Access</a></li>
                                            <li><a href="#" class="nav-link dropdown-item">Configure Dataset</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Archive</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Backup Data</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Settings</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Global Settings</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item border-0">Load Balancer</a></li>
                                    <li class="nav-item"><a href="#" class="nav-link dropdown-item">Group Node Policy</a></li>
                                </ul>
                            </li>

                            <ul class="list-group">
                                <!-- Report Section -->
                                <li class="list-group-item p-0">
                                    <ul class="ps-0" style="list-style: none;">
                                        <li class="Mega-Menu-header">
                                            <i class="cp-report me-1"></i>
                                            <span class="align-middle">Report</span>
                                        </li>
                                        <li class="nav-item"><a href="prebuild-reports.html" class="nav-link dropdown-item">Prebuild Reports</a></li>
                                        <li class="nav-item"><a href="custom-reports.html" class="nav-link dropdown-item">Custom Reports</a></li>
                                        <li class="nav-item"><a href="report-scheduler.html" class="nav-link dropdown-item border-0">Report Scheduler</a></li>
                                        <li class="nav-item d-none"><a href="#" class="nav-link dropdown-item border-0"></a></li>
                                    </ul>
                                </li>

                                <!-- Alert Section -->
                                <li class="list-group-item px-0" style="margin-top: -1px;" id="alert-dropdown">
                                    <ul class="ps-0" style="list-style: none;">
                                        <li class="Mega-Menu-header">
                                            <i class="cp-alerts-head me-1"></i>
                                            <span class="align-middle">Alert</span>
                                        </li>
                                        <li class="nav-item"><a href="alert-dashboard.html" class="nav-link dropdown-item">Alert Dashboard</a></li>
                                        <li class="nav-item"><a href="manage-alert.html" class="nav-link dropdown-item">Manage Alert</a></li>
                                    </ul>
                                </li>

                                <!-- Server Logs Section -->
                                <li class="list-group-item px-0" style="margin-top: -1px;" id="serverlog-dropdown">
                                    <ul class="ps-0" style="list-style: none;">
                                        <li class="Mega-Menu-header">
                                            <i class="cp-server me-1"></i>
                                            <span class="align-middle">Server Logs</span>
                                        </li>
                                        <li class="nav-item"><a href="server-log.html" class="nav-link dropdown-item">Server Log</a></li>
                                        <li class="nav-item"><a href="server-log-history.html" class="nav-link dropdown-item">Server Log History</a></li>
                                    </ul>
                                </li>
                            </ul>

                        </ul>
                    </div>

                </li>


                <li class="list-group-item flex-fill p-2">
                    <!-- ... existing Admin, Report, Alert, Server Logs (unchanged) ... -->
                </li>

                <!-- Alert Icon -->
                <li class="nav-item dropdown">
                    <a class="nav-link Menu_Icon d-none" href="#" role="button" title="Alert Icon">
                        <span class="translate-middle badge blink alertcount"></span>
                        <i class="cp-fail-back alerticon"></i>
                    </a>
                </li>

                <!-- User Profile -->
                <li class="nav-item dropdown">
                    <a class="nav-link Menu_Icon d-flex align-items-center py-0" href="#">
                        <img src="../../img/input_Icons/user-profile.svg" class="me-2 rounded-circle" id="userProfileImage" width="20px" height="20px" />
                        <div class="d-grid" style="width:50px; color:var(--bs-nav-link-color)">
                            <span id="userLoginName" class="align-middle text-truncate d-inline-block" userid="User123" style="max-width:125px">Admin</span>
                            <small id="time" class="align-middle" style="font-size:11px;"></small>
                        </div>
                    </a>
                    <ul class="dropdown-menu btnDropDown end-0">
                        <li class="nav-item"><a href="#" class="nav-link dropdown-item">Profile</a></li>
                        <li class="nav-item"><a href="#" class="nav-link dropdown-item">Patch List</a></li>
                        <li class="nav-item"><a href="#" class="nav-link dropdown-item">Change Password</a></li>
                        <li class="nav-item"><a href="#" class="nav-link dropdown-item">About</a></li>
                        <li class="nav-item"><a href="#" class="nav-link dropdown-item">Lock</a></li>
                        <li class="nav-item"><a href="/pdf/Help.pdf" target="_blank" class="nav-link dropdown-item help" title="User manual">Help</a></li>
                        <li class="nav-item"><a href="#" class="nav-link dropdown-item">SiteAdmin</a></li>
                        <li class="nav-item"><a href="#" class="nav-link dropdown-item">LogIn</a></li>
                        <li class="nav-item"><a href="#" class="nav-link dropdown-item">BasicCompany</a></li>
                        <li class="nav-item"><a href="#" class="nav-link dropdown-item">Logout</a></li>
                    </ul>
                </li>

                <!-- Customer Logo -->
                <li class="nav-item dropdown">
                    <img src="../../img/logo/pts_logo.png" height="24" width="125" alt="Customer Logo" title="Customer Logo" />
                </li>
            </ul>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                    data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false"
                    aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
        </div>
        <div>
            <span id="LoginId" style="display:none;">USERID_PLACEHOLDER</span>
            <span class="align-middle" id="userRole" style="display:none;">ROLEID_PLACEHOLDER</span>
            <span id="sessionExpirationTime" style="display:none;">SESSION_EXPIRATION_PLACEHOLDER</span>
        </div>
    </nav>
    <main>
        <iframe id="testFrame" title="Test Results"></iframe>
    </main>
    <script src="../../lib/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sinon.js/15.2.0/sinon.min.js"></script>
    <script src="../../js/NavBar/navbarPartial.js"></script>
    <script>
        var baseUrl = '@Configuration["AppSettings:BaseUrl"]';
        function loadTest(relativePath) {
            const frame = document.getElementById('testFrame');
            if (frame) {
                frame.src = '/Areas/' + relativePath;
                window.location.hash = relativePath;
                window.scrollTo(0, 0);
            }
        }
        window.onload = function () {
            const hash = window.location.hash;
            if (hash) {
                const testPath = hash.substring(1);
                loadTest(testPath);
            }
        };
    </script>
</body>
</html>

