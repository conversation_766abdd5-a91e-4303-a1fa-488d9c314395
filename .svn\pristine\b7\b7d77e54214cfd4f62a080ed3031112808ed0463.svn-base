﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<style>
    .pagination {
        padding-right: 20px
    }
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 27px;
    }
</style>
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-alert-dashboard"></i><span>Alert Dashboard</span></h6>
            <form class="d-flex gap-2">
                <div class="input-group">
                    <input type="search" id="alertSearchInp" class="form-control" placeholder="Search" autocomplete="off" />                  
                    <span class="input-group-text pe-0"><i class="cp-search"></i></span>
                </div>
                <button id="BtnAlertDownload" type="button" title="Download" class="btn btn-primary btn-sm"><i class="cp-download"></i></button>
                <span id="alertCollapsebtn" class="btn btn-sm alertCollapsebtn text-primary" data-bs-toggle="collapse"
                      href="#alertCollapseExample" data-clicked="false" role="button" aria-expanded="false" title="Graphical View"
                      aria-controls="alertCollapseExample">
                    <i class="cp-circle-downarrow" style="font-size:20px"></i>
                </span>
            </form>
        </div>
        <div class="card-body pt-0">
            <div class="d-flex gap-2">
                <div class="form-group w-100" id="infraoption">
                    <div class="input-group">  
                        <span class="input-group-text form-label mb-0" for="basic-url"><i class="cp-infra-object me-1"></i>InfraObject</span>
                        <select aria-label="Default select example" class="form-select" id="alertInfraValue" data-placeholder="Select Infraobject">
                            <option value="All">All</option>
                        </select>
                    </div>
                </div>
                <div class="form-group w-100" id="severityoption">
                    <div class="input-group">
                        <span class="input-group-text form-label mb-0" for="basic-url"><i class="cp-priority me-1"></i>Priority</span>
                        <select aria-label="Default select example" class="form-select" id="alertServerityValue" data-placeholder="Select Alert Priority ">
                            <option value="All">All</option>
                            <option value="High">High</option>
                            <option value="Low">Low</option>
                            <option value="Critical">Critical</option>
                            <option value="Information">Information</option>
                        </select>
                    </div>
                </div>
                <div class="form-group w-100" id="typeoption">
                    <div class="input-group">
                        <span class="input-group-text form-label mb-0" for="basic-url"> <i class="cp-activity-type me-1"></i>Type</span>
                        <select aria-label="Default select example" class="form-select" id="alertTypeValue" data-placeholder="Select Type">
                            <option value="All">All</option>
                        </select>
                    </div>
                </div>
                <div class="form-group w-100">
                    <div class="input-group" id="alertStartDateInp">
                        <span class="input-group-text form-label mb-0" for="startDate" cursorshover="true"><i class="cp-calendar me-1"></i>Start&nbsp;Date</span>
                        <input placeholder="Select start date" type="date" id="alertStartDate"
                               class="form-control custom-cursor-default-hover" value="" />
                    </div>
                    <span id="alertStartDateError"></span>
                </div>
                <div class="form-group w-100">
                    <div class="input-group" id="alertEndDateInp">
                        <span class="input-group-text form-label mb-0" for="endDate"><i class="cp-calendar me-1"></i>End&nbsp;Date</span>
                        <input placeholder="Select end date" type="date" id="alertEndDate"
                               class="form-control custom-cursor-default-hover" value="" />
                    </div>
                </div>
                <button type="button" id="alertReset" title="Reset" class="btn btn-primary btn-sm"><i class="cp-reload"></i></button>
            </div>
            <!-- chart section start -->
            <div class="collapse row mt-2" id="alertCollapseExample">
                <div class="col-xl-5">
                    <div id="alertChartDiv"></div>
                </div>
                <div class="col-xl-7">
                    <div id="severityLineChart"></div>
                </div>
            </div>
            <!-- chart section end -->
            <div id="alertCollapeTable" class="header_filter">
                <table class="table  table-hover dataTable tabledata no-footer" id="alertTableData" style="width:100%">
                    <thead>
                        <tr class="">
                            <th class="SrNo_th">Sr.No</th>
                            <th class="">Alert&nbsp;Name</th>
                            <th class="">Alert&nbsp;Priority</th>
                            <th class="col-md-3">Description</th>
                            <th class="">Job&nbsp;Name</th>
                            <th class="">InfraObject</th>
                            <th class="">Created&nbsp;Date</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

  
<div class="modal fade" id="AlertReport" data-bs-backdrop="static" tabindex="-1" aria-labelledby="configureModalLabel" aria-hidden="true">
    <partial name="AlertReport" />
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Alert/Alert Dashboard/Alert.js"></script>
<script>
            $(".alertCollapsebtn").click(function () {
        var icondata = $(this).find("i").attr("class");
        if (icondata == "cp-circle-downarrow" || icondata == "cp-circle-downarrow") {
            $(".cp-circle-downarrow").addClass("cp-circle-uparrow")
            $(".cp-circle-downarrow").removeClass("cp-circle-downarrow")
        }
        else {
            $(".cp-circle-uparrow").addClass("cp-circle-downarrow")
            $(".cp-circle-uparrow").removeClass("cp-circle-uparrow")
        }
    })
</script>
<script type="text/javascript">
    $(function () {
        var dtToday = new Date();
        var month = dtToday.getMonth() + 1;
        var day = dtToday.getDate();
        var year = dtToday.getFullYear();
        if (month < 10)
            month = '0' + month.toString();
        if (day < 10)
            day = '0' + day.toString();
        var maxDate = year + '-' + month + '-' + day;
            $('#alertStartDate').attr('max', maxDate);
            $('#alertEndDate').attr('max', maxDate);
    });
</script>