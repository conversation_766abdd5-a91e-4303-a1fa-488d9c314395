﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.LoadBalancer.Events.Update;

public class LoadBalancerUpdatedEventHandler : INotificationHandler<LoadBalancerUpdatedEvent>
{
    private readonly ILogger<LoadBalancerUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public LoadBalancerUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<LoadBalancerUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(LoadBalancerUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.LoadBalancer}",
            Entity = Modules.LoadBalancer.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"{updatedEvent.TypeCategory}  '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"{updatedEvent.TypeCategory} '{updatedEvent.Name}' updated successfully.");
    }
}