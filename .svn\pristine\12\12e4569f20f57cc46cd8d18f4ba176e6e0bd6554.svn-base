﻿using ContinuityPatrol.Application.Features.DashboardView.Commands.Create;
using ContinuityPatrol.Application.Features.DashboardView.Commands.Delete;
using ContinuityPatrol.Application.Features.DashboardView.Commands.Update;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetBusinessViewPaginatedList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByLast7Days;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessFunctionId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetDCMappingBySites;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDcMappingList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetNames;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetResilienceHealthStatus;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetServiceTopologyList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetSitePropertiesByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetSiteList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetBusinessImpactAnalysis;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetComponentFailureAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetDrillAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalAvailabilityAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalHealthSummary;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetSlaBreach;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetWorkflowAnalytics;
using ContinuityPatrol.Application.Features.DataLag.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.DrReady.Queries.GetDrReadyByBusinessServiceId;
using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetImpactByBusinessServiceId;
using ContinuityPatrol.Application.Features.Rto.Queries.GetRTOByBusinessServiceId;
using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;
using ContinuityPatrol.Domain.ViewModels.DataLagModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetVerifyWorkflowDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetBreachDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetLastDrillDetails;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.OneViewEntitiesEvent;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.OneViewRiskMitigationFailedDrill;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.OneViewRiskMitigationCyberSecurity;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetTotalSiteDetailForOneView;

namespace ContinuityPatrol.Services.Db.Impl.Dashboard;

public class DashboardViewService : BaseService, IDashboardViewService
{
    public DashboardViewService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<ItViewByInfraObjectIdVm> GetITViewByInfraObjectId(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "ITView InfraObjectId");

        Logger.LogDebug($"Get ITView Detail by InfraObjectId '{infraObjectId}'");

        return await Mediator.Send(new GetItViewByInfraObjectIdQuery { InfraObjectId = infraObjectId });
    }

    public async Task<DashboardViewDetailVm> GetDashboardViewById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DashboardView Id");

        Logger.LogDebug($"Get DashboardView Detail by Id '{id}'");

        return await Mediator.Send(new GetDashboardViewDetailQuery { Id = id });
    }

    public async Task<List<BusinessViewPaginatedList>> GetBusinessViews()
    {
        Logger.LogDebug("Get Searching Details in OperationalView Paginated List");

        return await Mediator.Send(new GetBusinessViewPaginatedListQuery());
    }

    public async Task<List<DashboardViewByBusinessServiceIdVm>> GetDashboardViewListByBusinessServiceId(
        string businessServiceId)
    {
        Logger.LogDebug($"Get DashboardViewList By OperationalServiceId '{businessServiceId}'");

        return await Mediator.Send(new GetDashboardViewByBusinessServiceIdQuery
        { BusinessServiceId = businessServiceId });
    }

    public async Task<List<DashboardViewByBusinessFunctionIdVm>> GetDashboardViewListByBusinessFunctionId(
        string businessFunction)
    {
        Logger.LogDebug($"Get DashboardViewList By OperationalFunctionId '{businessFunction}'");

        return await Mediator.Send(new GetDashboardViewByBusinessFunctionIdQuery
        { BusinessFunctionId = businessFunction });
    }

    public async Task<GetDashboardViewByInfraObjectIdVm> GetDashboardViewListByInfraObjectId(string infraObjectId)
    {
        Logger.LogDebug($"Get DashboardViewList Detail By InfraObjectId '{infraObjectId}'");

        return await Mediator.Send(new GetDashboardViewByInfraObjectIdQuery { InfraObjectId = infraObjectId });
    }

    public async Task<List<DashboardViewListVm>> GetDashboardViews()
    {
        Logger.LogDebug("Get All DashboardViews");

        return await Mediator.Send(new GetDashboardViewListQuery());
    }

    public async Task<List<GetDcMappingListVm>> GetDcMappingDetails(string? siteId)
    {
        Logger.LogDebug($"Get DcMappings by siteId '{siteId}'");

        return await Mediator.Send(new GetDcMappingListQuery { SiteId = siteId });
    }

    public async Task<List<DataLagStatusbyLast7DaysVm>> GetDatalagStatusByLast7DaysList()
    {
        Logger.LogDebug("Get All Datalag Status");

        return await Mediator.Send(new GetDataLagStatusbyLast7DaysQuery());
    }

    public async Task<List<GetItViewListVm>> GetItViewList()
    {
        Logger.LogDebug("Get It-View List");

        return await Mediator.Send(new GetItViewListQuery());
    }

    public async Task<List<ItViewByBusinessServiceIdVm>> GetItViewByBusinessServiceId(string businessServiceId)
    {
        Logger.LogDebug($"Get It-View detail by OperationalServiceId {businessServiceId}");

        return await Mediator.Send(new GetItViewByBusinessServiceIdQuery { BusinessServiceId = businessServiceId });
    }

    public async Task<BaseResponse> CreateAsync(CreateDashboardViewCommand createDashboardViewCommand)
    {
        Logger.LogDebug($"Create Dashboard View '{createDashboardViewCommand}'");

        return await Mediator.Send(createDashboardViewCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDashboardViewCommand updateDashboardViewCommand)
    {
        Logger.LogDebug($"Update Dashboard View'{updateDashboardViewCommand}'");

        return await Mediator.Send(updateDashboardViewCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DashboardView Id");

        Logger.LogDebug($"Delete DashboardView Details by Id '{id}'");

        return await Mediator.Send(new DeleteDashboardViewCommand { BusinessViewId = id });
    }

    public async Task<GetByEntityIdVm> GetMonitorServiceStatusByIdAndType(string monitorId, string type)
    {
        Guard.Against.InvalidGuidOrEmpty(monitorId, "MonitorServiceStatus MonitorId");

        Logger.LogDebug($"Get MonitorServiceStatus Detail by EntityId '{monitorId}' and '{type}");

        return await Mediator.Send(new GetByEntityIdQuery { EntityId = monitorId, Type = type });
    }

    public async Task<List<DashboardViewNameVm>> GetDashboardNames()
    {
        Logger.LogDebug("Get All Dashboard Names");

        return await Mediator.Send(new GetDashboardNameQuery());
    }

    public async Task<List<GetServiceTopologyListVm>> GetBusinessServiceTopologyByBusinessServiceId(
        string? businessServiceId)
    {
        Logger.LogDebug($"Get BuOperationalService Topology By OperationalServiceId '{businessServiceId}'");

        return await Mediator.Send(new GetServiceTopologyListQuery { BusinessServiceId = businessServiceId });
    }

    public async Task<RTOByBusinessServiceIdVm> GetRTOByBusinessServiceId(string? businessServiceId)
    {
        Logger.LogDebug($"Get RTO Details By OperationalServiceId '{businessServiceId}'");

        return await Mediator.Send(new GetRTOByBusinessServiceIdQuery { BusinessServiceId = businessServiceId });
    }

    public async Task<DrReadyByBusinessServiceIdVm> GetDrReadyByBusinessServiceId(string? businessServiceId)
    {
        //Guard.Against.InvalidGuidOrEmpty(businessServiceId, "DRReadyStatus By businessServiceId");

        Logger.LogDebug($"Get DRReady Detail by OperationalServiceId '{businessServiceId}'");

        return await Mediator.Send(new GetDrReadyByBusinessServiceIdQuery { BusinessServiceId = businessServiceId });
    }

    public async Task<ImpactAvailabilityDetailVm> GetImpactAvailabilityByBusinessServiceId(string businessServiceId)
    {
        Logger.LogDebug($"Get ImpactAvailability Detail by OperationalServiceId '{businessServiceId}'");

        return await Mediator.Send(new GetImpactByBusinessServiceIdQuery { BusinessServiceId = businessServiceId });
    }

    public async Task<DataLagListVm> GetDataLagByBusinessServiceId(string businessServiceId)
    {
        Logger.LogDebug($"Get Data lag Detail by OperationalServiceId '{businessServiceId}'");

        return await Mediator.Send(new GetDataLagDetailByBusinessServiceIdQuery
        { BusinessServiceId = businessServiceId });
    }

    public async Task<SitePropertiesByBusinessServiceIdVm> GetSitePropertiesByBusinessServiceId(
        string businessServiceId)
    {
        Logger.LogDebug($"Get Data Center Detail by OperationalServiceId '{businessServiceId}'");

        return await Mediator.Send(new GetSitePropertiesByBusinessServiceIdQuery
        { BusinessServiceId = businessServiceId });
    }
    public async Task<ResilienceHealthStatusDetailVm> GetResilienceHealthStatusByInfraObjectId(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObject Id");

        Logger.LogDebug($"Get Resilience Health Status By InfraObject Id '{infraObjectId}'");

        return await Mediator.Send(new ResilienceHealthStatusDetailQuery { InfraObjectId = infraObjectId });
    }

    #region OneView
    public async Task<List<SiteCountListVm>> GetSiteCountList()
    {
        Logger.LogDebug($"Get Site Details for one-view");

        return await Mediator.Send(new GetSiteCountListQuery());
    }

    public async Task<VerifyWorkflowDetailVm> GetVerifiedWorkflowList()
    {
        Logger.LogDebug("Get All Verified Workflow");

        return await Mediator.Send(new GetVerifyWorkflowDetailQuery());
    }

    public async Task<BreachDetailVm> GetBreachDetails()
    {
        Logger.LogDebug("Get RPO and RTO Breach Detail.");

        return await Mediator.Send(new GetBreachDetailQuery());
    }

    public async Task<GetDcMappingSitesVm> GetDcMappingSiteDetails()
    {
        Logger.LogDebug("Get Sites details for DCMapping");

        return await Mediator.Send(new GetDcMappingSitesQuery());
    }
    public async Task<TotalSiteDetailForOneViewListVm> GetTotalSiteDetailsForOneView(List<string> siteId, string categoryType)
    {
        Logger.LogDebug($" Get Total Site Details For OneView");

        return await Mediator.Send(new GetTotalSiteDetailForOneViewQuery { SiteIds = siteId, CategoryType = categoryType });
    }

    public async Task<LastDrillDetailVm> GetLastDrillDetails()
    {
        Logger.LogDebug("Get LastDrill Details for OneView");

        return await Mediator.Send(new LastDrillDetailQuery());
    }

    public async Task<List<OneViewEntitiesEventView>> GetOneViewEntitiesEventViewList()
    {

        Logger.LogDebug("Get Entities event Details for OneView");

        return await Mediator.Send(new GetOneViewEntitiesEventQuery());
    }

    public async Task<List<OneViewRiskMitigationCyberSecurityView>> GetOneViewRiskmitigationCyberSecurityList()
    {
        Logger.LogDebug("Get OneView Riskmitigation CyberSecurity List");

        return await Mediator.Send(new GetOneViewRiskMitigationCyberSecurityQuery());
    }

    public async Task<List<OneViewRiskMitigationFailedDrillView>> GetOneViewRiskmitigationFailedDrillList()
    {
        Logger.LogDebug("Get OneView Riskmitigation Failed Drill List");

        return await Mediator.Send(new GetOneViewRiskMitigationFailedDrillQuery());
    }
    #endregion

    #region Analytics

    // Operational Analytics
    public async Task<DrillAnalyticsDetailVm> GetDrillAnalytics()
    {
        Logger.LogDebug("Get Operational Readiness for Operational Analytics");

        return await Mediator.Send(new GetOperationalReadinessQuery());
    }

    public async Task<ComponentFailureAnalyticsDetailVm> GetComponentFailureAnalytics()
    {
        Logger.LogDebug("Get Analytics Details for Component Failure");

        return await Mediator.Send(new ComponentFailureAnalyticsDetailQuery());
    }
    public async Task<GetSlaBreachListVm> GetSlaBreach()
    {
        Logger.LogDebug("Get Analytics Details for Sla Breach");

        return await Mediator.Send(new GetSlaBreachListQuery());
    }

    public async Task<GetOperationalAvailabilityAnalyticsDetailVm> GetOperationalAvailabilityAnalytics()
    {
        Logger.LogDebug("Get Analytics Details for Operational Availability");

        return await Mediator.Send(new GetOperationalAvailabilityAnalyticsQuery());
    }

    public async Task<GetWorkflowAnalyticsDetailVm> GetWorkflowAnalytics()
    {
        Logger.LogDebug("Get Analytics Details for Operational Readiness");

        return await Mediator.Send(new GetWorkflowAnalyticsQuery());
    }

    public async Task<List<OperationalHealthSummaryDetailVm>> GetOperationalServiceHealthSummary()
    {
        Logger.LogDebug("Get Analytics Dashboard Operational Service Health Summary");

        return await Mediator.Send(new OperationalHealthSummaryQuery());
    }
    public async Task<List<BusinessImpactAnalysisVm>> GetBusinessImpactAnalysisAsync()
    {
        Logger.LogDebug("Get Business Impact Analysis details");
        return await Mediator.Send(new GetBusinessImpactAnalysisQuery());
    }

    #endregion
}