﻿
let isUnAuthorize = false;
let configurationURL = '';


$('#search-inp').attr('autocomplete', 'off');

if (!window?.location?.href.includes('ITAutomation/WorkflowConfiguration')) {
    window.alert = function () { return false }
}

function commonDebounce(func, delay = 300) {
    let timer;
    return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
            func.apply(context, args);
        }, delay);
    };
}

//const startSignalRNotifictionConnection = async (connection) => {
//    try {
//        await connection.start();
//        connection.on("notification", (message) => {
//            showCustomToast(message);
//        });
//    } catch (err) {
//        setTimeout(startSignalRNotifictionConnection, 10000); //Retry after 10 seconds.
//    }
//};

//let wasClicked = false;

//document.querySelector('.detect-click').addEventListener('mousedown', function (e) {
//    // Check for right-click (button 2) or middle-click (button 1)
//    if (e.button === 2 || e.button === 1) {
//        wasClicked = true;
//        console.log('Right-click or middle-click detected');
//    }
//});

//window.addEventListener('blur', function () {
//    if (wasClicked) {
//        console.log('Likely opened in a new tab or context menu option');
//        wasClicked = false;
//    }
//});

function showCustomToast(title) {
    if (title?.group?.toLowerCase() == "cyber") {
        let typeofName = typeof (title?.message)
        var airGabToastDiv = document.getElementById('airGabToast');
        var airGapIcon = document.querySelector('#airGabToast .cp-air-gap');
        var airGapMessage = document.querySelector('#airGabToast h6');
        let getMessage = title?.message
        if (typeofName == "string") {
            getMessage = JSON.parse(title?.message)
        }
        if (title?.status?.toLowerCase() === "open" || title?.status?.toLowerCase() == "enable" || title?.status?.toLowerCase() == "unlock") {
            airGapIcon.classList.replace('text-danger', 'text-success');
            airGapMessage.textContent = getMessage?.message;
            airGabToastDiv.classList.replace('border-danger', 'border-success');

        } else if (title?.status?.toLowerCase() === "close" || title?.status?.toLowerCase() == "disable" || title?.status?.toLowerCase() == "lock") {
            airGapIcon.classList.replace('text-success', 'text-danger');
            airGapMessage.textContent = getMessage?.message;
            airGabToastDiv.classList.replace('border-success', 'border-danger');
        }
        var airGapToastBootstrap = bootstrap.Toast.getOrCreateInstance(airGabToastDiv);
        // Show the toast
        airGapToastBootstrap.show();
        //toastLiveExample.classList.remove('bg-primary');

        //if (title.status.toLowerCase() === "open") {
        //    toastLiveExample.classList.add('bg-success');
        //} else if (title.status.toLowerCase() === "close") {
        //    toastLiveExample.classList.add('bg-warning');
        //} else {
        //    toastLiveExample.classList.add('bg-primary');
        //}
        if (window?.location?.href.includes('CyberResiliency/CyberResiliency')) {
            if (getMessage && getMessage?.id) {
                GetAirGapsStatus();
                const targetImg = $(`img[airgapid="${getMessage?.id}"]`);
                if (targetImg && targetImg?.length) {
                    const status = title?.status?.toLowerCase();
                    const newSrc = (status === "open" || status === "enable" || status === "unlock") ?
                        '/../img/Component_icons/airgap-on-vertical.svg' :
                        '/../img/Component_icons/airgap-off-vertical.svg';
                    targetImg[0].onload = function () {
                        convertToImage();
                    };
                    targetImg[0].src = newSrc;
                    if (targetImg[0]?.complete) {
                        targetImg[0]?.onload();
                    }
                }
            }
        }

    } else if (title?.group?.toLowerCase() == "workflow" && window?.location?.pathname?.toLowerCase() != '/itautomation/workflowexecution/list') {

        let typeofName = typeof (title?.message)
        if (typeofName == "string") {
            let jsonParse = JSON.parse(title?.message)
            notificationAlert(jsonParse?.success ? "success" : "warning", jsonParse?.message)
        }
        else {
            notificationAlert(title?.success ? "success" : "warning", title?.message)
        }

    } else {
        var loginName = document.getElementById('btntoaster').getAttribute('data-userName');
        var toastLiveExample = document.getElementById('liveToast')
        var toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        var titleElement = toastLiveExample.querySelector('h6');

        if (title.userName === loginName) {
            toastBootstrap.hide()
        }
        else {
            titleElement.innerText = title.message;
            toastBootstrap.show();
        }
    }
}


//const getSignalRConnection = async () => {
//    //await $.ajax({
//    //    type: "GET",
//    //    url: RootUrl + 'Account/GetConfigurationUrlExe',
//    //    datatype: "json",
//    //    traditional: true,
//    //    success: function (result) {
//    // if (result.success) {
//    configurationURL = $('#Chat_Bot').attr('signalRurl');

//    sessionStorage.setItem('configurationURL', configurationURL);
//    let url = configurationURL + "notificationhub"
//    let connection = new signalR.HubConnectionBuilder()
//        .withUrl(url, {
//            skipNegotiation: true,
//            rejectUnauthorized: false,
//            transport: signalR.HttpTransportType.WebSockets
//        })
//        .configureLogging(signalR.LogLevel.Information)
//        .build();

//    connection.onclose(async () => {
//        await new Promise(resolve => setTimeout(resolve, 10000));
//        await startSignalRNotifictionConnection();
//    });
//    startSignalRNotifictionConnection(connection);   //Start the connection.
//    //   } 

//    //  }
//    // })
//}
//$(function () {
//    if (!window.location.href?.toLowerCase().includes('itautomation/workflowexecution')) getSignalRConnection();
//})

//Select2 configuration

//$('select').on('select2:opening', () => {
//    $('.select2-selection__choice_more').remove();
//});

//$('select').on('select2:closing', () => {

//    $('.select2-selection').width('auto');
//});

//$('.modal').on('shown.bs.modal', function (e) {
//    $(this).find('.form-select-modal').select2({
//        dropdownParent: $(this).find('.modal-content')
//    });
//})

//$(function () {
//    $('.form-select-sm').select2({
//        minimumResultsForSearch: Infinity
//    });
//})

//select2 repeated multiple time
//$(".form-select-modal").select2({
//    tags: true,
//});


//$('.select2-icon').select2({
//    templateSelection: formatText,
//    templateResult: formatText
//});

//$('.form-select').select2({
//    "language": {
//        "noResults": function () {
//            return "No results found";
//        }
//    },
//});

//$(document).on('mouseenter', '.select2-selection__rendered', function () {
//    $('.select2-selection__rendered').removeAttr('title');
//});

function gettoken() {
    let token = $('input[name="__RequestVerificationToken"]').val();
    return token;
}


function CommonValidation(errorElement, validationResults) {

    const failedValidations = validationResults.filter(result => result !== true);

    if (failedValidations.length > 0) {
        errorElement.text(failedValidations[0])
            .addClass('field-validation-error')

        return false;
    } else {
        errorElement.text('')
            .removeClass('field-validation-error')
        return true;
    }
}

//Clear data
const clearInputFields = (id, errorElements) => {
    document.getElementById(id)?.reset();
    $('#' + id + ' input[type=hidden]').not('[name=__RequestVerificationToken]').val('');

    errorElements?.forEach(element => {
        $(element).text('').removeClass('field-validation-error');
    });
}
const WordbetweenSpecialChar = (value) => {
    return (!RegExp(/^[a-zA-Z0-9&.,_@$#'"\-]+$/).test(value)) ? "Invalid format" : true;
}

const negativevalue = (value) => {
    return value < 0 ? "Negative value not allowed" : true
}

const zeroValue = (value) => { return value <= 0 ? "Enter value above 0" : true }
//const InvalidPathRegex = (value) => {
//    return (!RegExp(/^(?:[a-zA-Z]:|(\\\\|\/\/)[\w\]+(\\|\/)[\w$]+)((\\|\/)|(\\\\|\/\/))(?:[\w ]+(\\|\/))*\w([\w ])+$/i).test(value)) ? "Invalid path" : true;
//}
const Backupfilepath = (value) => {
    return (!RegExp(/^(?:(?:[a-zA-Z]:|\\\\|\/(?:[a-zA-Z0-9]+\/)?)((?:[\w .:\\-]+(?:\\|\/)?)+|\/(?:[a-zA-Z0-9]+\/)?(?:[\w .:\\-]+(?:\\|\/)?)+|\/\/[a-zA-Z0-9]+\/(?:[\w .:\\-]+(?:\\|\/)?)+))$/).test(value)) ? "Invalid backup path" : true;
}
const BackupSourcefilepath = (value) => {
    return (!RegExp(/^(?:(?:[a-zA-Z]:|\\\\|\/(?:[a-zA-Z0-9]+\/)?)((?:[\w .:\\-]+(?:\\|\/)?)+|\/(?:[a-zA-Z0-9]+\/)?(?:[\w .:\\-]+(?:\\|\/)?)+|\/\/[a-zA-Z0-9]+\/(?:[\w .:\\-]+(?:\\|\/)?)+))$/).test(value)) ? "Invalid source path" : true;
}
const BackupTargetfilepath = (value) => {
    return (!RegExp(/^(?:(?:[a-zA-Z]:|\\\\|\/(?:[a-zA-Z0-9]+\/)?)((?:[\w .:\\-]+(?:\\|\/)?)+|\/(?:[a-zA-Z0-9]+\/)?(?:[\w .:\\-]+(?:\\|\/)?)+|\/\/[a-zA-Z0-9]+\/(?:[\w .:\\-]+(?:\\|\/)?)+))$/).test(value)) ? "Invalid target path" : true;
}
//const Backupfilepath = (value) => {

//    return (!RegExp(/^(?!.*[\\\/]\s+)(?!(?:.*\s|.*\.|\W+)$)(?:[a-zA-Z]:)?(?:(?:[^<>:"\|\?\*\n])+(?:\/\/|\/|\\\\|\\)?)+$/).test(value)) ? "Invalid path" : true;
//}
/*const InvalidSMSUrl = (value) => {
    return (!RegExp(/^(https?:\/\/)?(www\.)?([\da-z.-]+)\.([a-z.]{2,})([/\w .-]*)*\/?$/).test(value)) ? "Invalid URL" : true;
}*/
const InvalidSMSUrl = (value) => {
    return (!RegExp(/^(https?:\/\/)?(www\.)?([\da-z]+)\.([a-z.]{2,})([\/\w .]*)*\/?$/).test(value)) ? "Invalid URL" : true;
}

const InvalidPathRegex = (value) => {
    return (!RegExp(/^(?:[a-zA-Z]:)?[\\/][\w\s!@#$%^&*()\-+=,.{}\[\]~`'";]+(?:[\\/][\w\s!@#$%^&*()\-+=,.{}\[\]~`'";]+)*[\\/]?$/i).test(value)) ? "Invalid path" : true;
}
const SmtpHost = (value) => { return (!RegExp(/^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\-]*[A-Za-z0-9])$/gm).test(value)) ? "Invalid host" : true; }

const SpecialCharValidate = (value) => {
    const regex = /^[a-zA-Z0-9_\s]*$/;
    return !regex.test(value) ? "Special characters not allowed" : !(/^[^<]*$/).test(value) ? "Special characters not allowed" : true;
}

const SpecialCharValidateCustom = (value) => {
    const regex = /^[a-zA-Z0-9_\s\-.]*$/;
    return !regex.test(value) ? "Special characters not allowed" : !(/^[^<]*$/).test(value) ? "Special characters not allowed" : true;
}

const OnlyNumericsValidate = (value) => {
    const regex = /^[0-9]+$/;
    return regex.test(value) ? "Only numerics not allowed" : true;
}
const ShouldNotBeginWithUnderScore = (value) => {
    return (RegExp(/(^_+)/).test(value)) ? "Should not begin with underscore" : true;
}

const ShouldNotBeginWithDotAndHyphen = (value) => {
    if (value.startsWith('.')) {
        return "Should not begin with dot";
    }
    if (value.startsWith('-')) {
        return "Should not begin with hyphen";
    }
    return true;
};

const ShouldNotConsecutiveDotAndHyphen = (value) => {
    if (value.includes('..')) {
        return "Should not contain consecutive dots";
    }
    if (value.includes('--')) {
        return "Should not contain consecutive hyphens";
    }
    return true;
};

const ShouldNotBeginWithSpace = (value) => { return (!RegExp(/^(?!\s).*/).test(value)) ? "Should not begin with space" : true; }
const ShouldNotAllowMultipleSpace = (value) => {
    return (!RegExp(/^\S+(?: \S+)*$/).test(value)) ? "Should not allow multiple space" : true;
}
const SpaceWithUnderScore = (value) => {
    return (RegExp(/(([a-zA-z0-9]+)(\s+)(_)([a-zA-Z0-9]+)*)/).test(value)) ? "Invalid format" : true;
}
const LocationMaxlength = (value) => {
    return value.length > 2 && value.length < 51 ? true : "Between 3 to 50 characters"
}
const minMaxlength = (value, maxlength = 100) => {
    return value.length > 2 && value.length < maxlength + 1 ? true : `Between 3 to ${maxlength} characters`
}
const manage_alert_name_minMaxlength = (value) => {
    return value.length >= 3 && value.length <= 100 ? true : "Between 3 to 100 characters"
}
const manage_alert_message_minMaxlength = (value) => {
    return value.length >= 3 && value.length <= 500 ? true : "Between 3 to 500 characters"
}
const minMaxCompanylength = (value) => {
    return value.length > 2 && value.length < 101 ? true : "Between 3 to 100 characters"
}
const secondChar = (value) => {
    return value.charAt(1) === " " ? 'Invalid format' : true
}
const ShouldNotEndWithUnderScore = (value) => {
    return (RegExp(/([a-zA-Z0-9]+)(_+$)/).test(value)) ? "Should not end with underscore" : true;
}
const ShouldNotEndWithSpace = (value) => {
    return (!RegExp(/^[^\s]+(\s+[^\s]+)*$/).test(value)) ? "Should not end with space" : true;
}

const MultiUnderScoreRegex = (value) => {
    return (RegExp(/(([a-zA-z0-9]+)(_)(_+)([a-zA-Z0-9]+)*)/).test(value)) ? "Invalid format" : true;
}
const SpaceAndUnderScoreRegex = (value) => {
    return (RegExp(/(([a-zA-z0-9]+)(_)(\s+)([a-zA-Z0-9]+)*)/).test(value)) ? "Invalid format" : true;
}
const DisplayLength = (value) => {
    return value.length > 2 && value.length < 51 ? true : "Between 3 to 50 characters"
}
const WebValidate = (value) => {
    return (!RegExp(/(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/gi).test(value)) ? "Enter valid web address" : true;
}


const ShouldNotEndWithHyphen = (value) => {
    return (RegExp(/([a-zA-Z0-9]+)(-+$)/).test(value)) ? "Should not end with hyphen" : true;
}
const ShouldNotBeginWithHyphen = (value) => {
    return (RegExp(/(^-+)/).test(value)) ? "Should not begin with hyphen" : true;
}

const WebAddressValidate = (value) => {
    return (!RegExp(/^((ftp|http|https|smtp):\/\/)?(www\.)?([a-z0-9][a-z0-9_-]*[a-z0-9]\.){1,}[a-z]{2,}(:\d+)?(\/[^\s?#]*)?(\?[^\s#]*)?(#[a-zA-Z0-9\-._]*)?$/i).test(value) || /^(https?:\/\/www\.(?![^\/]+\.[a-z]{2,})[a-z0-9_-]*\/?)|(^|:\/\/)www(?!\.)|^www\.[a-z0-9][a-z0-9_-]*[a-z0-9]?\.?$/i.test(value) || /([^:]\/)\/+|\/[^a-zA-Z0-9\-._%\/?&=#]/i.test(value)) ? "Enter valid company web address" : true;
}
const webAddValidation = (value) => {
    return (!RegExp(/^((ftp|http|https|smtp):\/\/)??(WWW.)?(?!.*(ftp|http|https|WWW.))[a-zA-Z0-9_-]+(\.[a-zA-Z]+)+((\/)[\W]+)*(\/\W+\?[a-zA-Z0-9_]+=\W+(&[a-zA-Z0-9_]+=\W+)*)?\/?$/).test(value)) ? "Enter valid web address" : true;
}

const selectDropDownListValidation = (value, dropDownValue) => {
    return (!value) ? "Select " + dropDownValue : true
}
const descriptionMinMaxlength = (value) => {

    return (!value) ? true : value.length > 2 && value.length < 251 ? true : "Between 3 to 250 characters"
}
//const FileSizeValidate = (value) => {
//    const numericPart = parseFloat(value);
//    return !isNaN(numericPart) && numericPart < 2 * 1024 * 1024
//        ? true
//        : "File size should be less than 2MB";
//}
const FileSizeValidate = (fileSize) => {
    // Ensure the file size is numeric
    const numericSize = parseFloat(fileSize);

    // Check if the file size is smaller than 1KB or larger than 2MB
    if (numericSize < 1024) {
        return "File size must be at least 1KB";
    } else if (numericSize > 2 * 1024 * 1024) { // 2MB
        return "File size should be less than 2MB";
    }

    return true; // Valid file size
};
const FileFormatValidate = (value) => {
    const regex = /\.(jpeg|jpg|png)$/i;
    return regex.test(value.toLowerCase()) ? true : "Only JPG, JPEG, or PNG files are allowed.";
}
const passwordRegex = (value) => { return (!RegExp(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[#$@!%&*?=])[A-Za-z\d#$@!%&*?=]{8,15}$/).test(value)) ? "Invalid password" : true; }

//const emailRegex = (value) => { return (!RegExp(/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/).test(value)) ? "Invalid email" : true; }
//const emailRegex = (value) => { return (!RegExp(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.(com|org|net|gov)$/).test(value)) ? "Invalid email" : true; }
//const emailRegex = (value) => { return (!RegExp(/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/).test(value))? "Invalid email": true;}
const emailRegex = (value) => { return (!RegExp(/^[^\s@]+@[^\s@]+\.[^\s@]+$/).test(value)) ? "Invalid email" : true; }
const mobileRegex = (value) => { return (!RegExp(/^[+\-()0-9]{7,15}$/).test(value)) ? "Invalid recipient number" : true; }
//const mobileRegex = (value) => {
//    return (RegExp(/^(?:(?:\+91[- ]?)?[6-9]\d{9}|(?:(?!\+91[- ]?)[6-9]\d{9,14}))$/).test(value)) ? true : "Invalid recipient number";
//};


const OnlyNum = (value) => { return (!RegExp(/^[0-9]+$/).test(value)) ? "Enter numbers" : true; }

const ShouldNotBeginWithNumber = (value) => {  return (/^[0-9]/.test(value)) ? "Should not begin with number" : true; }

//const OnenumOnecharOneSpecialChar = (value) => { return (!RegExp(/^(?=.*[!@#$%^&*()_+,\-./:;<=>?@[\\]^_`{|}~])(?=.*\d).+$/).test(value)) ? "Invalid format" : true, }

const PortReg = (value) => { return (!RegExp(/^((6553[0-5])|(655[0-2][0-9])|(65[0-4][0-9]{2})|(6[0-4][0-9]{3})|([1-5][0-9]{4})|([0-5]{0,5})|([0-9]{1,5}))$/gi).test(value)) ? "Invalid port" : true; }

const IpaddressReg = (value) => { return (!RegExp(/^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/).test(value)) ? "Invalid ip address" : true; }
const mobilePreReg = (value) => { return (!RegExp(/^\+\d{1,3}$/).test(value)) ? "Invalid country code" : true }
const HostNameReg = (value) => {
    const regex = /^(?!.*[_-]{2})[a-zA-Z0-9_.\s-]+$/;
    if (value.startsWith('.') || value.endsWith('.') || value.startsWith('-') || value.endsWith('-')) {
        return "Invalid host name";
    }
    return regex.test(value) ? true : "Invalid host name";
}

const preventEnterKey = (e) => {

    if (e.key === 'Enter' || e.key === '=') {
        e.preventDefault();
    }
};

$(document).on('paste', '.form-control', function (e) {
    let value = e.originalEvent.clipboardData.getData('text');
    let newValue = value.replace(/[^\p{L}\p{N}\p{P}\p{Z}^$\n]/gu, '')

    if (newValue === '') {
        return false
    }
    if (!window?.location?.href.includes('Admin/LicenseManager')) {
        if (!window?.location?.href.includes('Admin/ActionBuilder/List')) {

            if ($(this).hasClass('form-control')) {
                let text = e.originalEvent.clipboardData.getData('text')
                if ($(this)[0].type === 'search') {
                    return text.includes('=') ? e.preventDefault() : ''
                } else {
                    if ($(this)[0].type !== 'textarea') {
                        if (text.length > 100) {
                            $(this).parents('.form-group').find('.errorName-container').addClass('field-validation-error').text('Between 3 to 100 characters')
                            return e.preventDefault()
                        } else {
                            $(this).parents('.form-group').find('.errorName-container').removeClass('field-validation-error').text('')
                        }
                    }
                }
            }
        }
    }

})


async function GetAsync(url, data) {
    return await $.get(url, data).fail(function (jqXHR) {
        notificationAlert("warning", 'Error Occured')
    });
}

async function getAysncWithHandler(url, data) {

    let dataArray = [];
    await $.get(url, data).done((response) => {
        if (response.success) {
            dataArray = response.data
        } else {
            errorNotification(response)
        }
    })
    return dataArray;
}

const IsNameAlreadyExist = async (url, data, text = 'Name already exists') => {
    return await GetAsync(url, data) ? text : true;

}

async function PostAsync(url, data) {
    return await $.post(url, data)
        .fail(function (jqXHR) {
            notificationAlert("warning", 'Error Occured')
        });
}

function OnError(msg) {
    console.log(msg.d);
}



const errorNotification = (data) => {
    if (data.hasOwnProperty('ErrorCode')) {
        if (data.ErrorCode === 1001) {
            notificationAlert("unauthorised", 'Session expired')
            setTimeout(() => {
                window.location.assign(RootUrl + 'Account/Logout');
            }, 2000)
        }
    } else {
        notificationAlert("warning", data.message)
    }
}

const notificationAlert = (toastClass, data, mode = '') => {
    $('#mytoastrdata').toast('hide');
    $('#alertClass, #icon_Detail').removeClass();
    let alertClass = toastClass === "success" ? "success-toast" : toastClass === "warning" ? "warning-toast" : toastClass === "info" ? "info-toast"
        : toastClass === "error" || "unauthorised" ? "unauthorised-toast" : '';
    let icon = toastClass === "success" ? "cp-check" : toastClass === "warning" ? "cp-warning" : toastClass === "info" ? "cp-note"
        : toastClass === "error" ? "cp-close" : toastClass === "unauthorised" ? "cp-user" : '';


    if (mode?.toLowerCase() == 'execution') {
        $('#notificationAlertmessage').append(data)
    } else {
        $('#alertClass').addClass(alertClass)
        $("#icon_Detail").addClass(`${icon} toast_icon`)
        $('#notificationAlertmessage').text(data)
    }

    if (data) $('#mytoastrdata').toast('show');
    let hideTimeout = setTimeout(function () {
        $('#mytoastrdata').toast('hide');
    }, 2000);


    $('#mytoastrdata')
        .on('mouseenter', function () {
            clearTimeout(hideTimeout);
        })
        .on('mouseleave', function () {
            hideTimeout = setTimeout(function () {
                $('#mytoastrdata').toast('hide');
            }, 2000);
        });
}

const AjaxErrorHandler = (jqxhr) => {
    if (jqxhr.status === 401) {
        if ($('#mytoastrdata').is(':visible')) {
            return false;
        }
        isUnAuthorize = true
        notificationAlert("unauthorised", 'Session expired')
        setTimeout(() => {
            window.location.assign(RootUrl + 'Account/Logout');
        }, 2500)
    } else if (jqxhr.status === 500) {
        console.log('Network error')
        // notificationAlert("error", 'Network error occured')       
    } else if (jqxhr.status === 400) {
        notificationAlert("error", 'Bad request detected')
    }
}

//if ($.fn.dataTable) {
//    $.fn.dataTable.ext.errMode = function (settings, helpPage, message) {
//        AjaxErrorHandler(settings.jqXHR);
//    };
//}


$(document).ajaxError(function (event, jqxhr, settings, thrownError) {
    AjaxErrorHandler(jqxhr)
})

const btnCrudDiasable = (btnId) => {
    $(`#${btnId}`).css('pointer-events', 'none')

}

const btnCrudEnable = (btnId) => {
    $(`#${btnId}`).css('pointer-events', '')
}


const btnCrudDisableWithClassName = (btnId) => {
    $(`.${btnId}`).css('pointer-events', 'none')
}

const btnCrudEnableWithClassName = (btnId) => {
    $(`.${btnId}`).css('pointer-events', '')
}


//const getVersion = async () => {

//    await $.ajax({
//        type: 'GET',
//        url: RootUrl + 'ITAutomation/WorkflowTemplate/GetConfigurationUrl',
//        datatype: "json",
//        traditional: true,
//        success: function (result) {         
//            $('.cpVersionData').val(result.cpVersion)
//            $('.cpVersionData').text(result.cpVersion)
//        }
//    })

//}
//getVersion()

//(function () {
//window.history.pushState(null, null, window.location.href);
//window.onpopstate = function () {
//    window.history.pushState(null, null, window.location.href);
//};
//})()

//(function () {
//    if (window.location.href !== 'https://localhost:7079/Account/Logout' || window.location.href !== 'https://localhost:7079/') {
//        window.history.forward();
//    }
//})()

//window.onunload = function () {
//    return false
//};

//$(window).on('unload', function (e) {
//    console.log(e)
//    if (window.location.href !== 'https://localhost:7079/Account/Logout' || window.location.href !== 'https://localhost:7079/') {
//        window.history.forward();
//    }
//})

$(document).on('ready', function () {
    setTimeout(() => {
        if (window.location.href !== 'https://localhost:7079/Account/Logout' || window.location.href !== 'https://localhost:7079/') {
            function disableBack() { window.history.forward() }
            window.onload = disableBack();
            window.onpageshow = function (evt) { if (evt.persisted) disableBack() }
        }
    })
});

//function sanitizeInput(string) {
//    const map = {
//        '&': '&amp;',
//        '<': '&lt;',
//        '>': '&gt;',
//        '"': '&quot;',
//        "'": '&#x27;',
//        "/": '&#x2F;',
//    };
//    const reg = /[&<>"'/]/ig;
//    return string.replace(reg, (match) => (map[match]));
//}

function sanitizeInput(str) {
    //return DOMPurify.sanitize(str);
    return str.replace(/\s{2,}/g, ' ');
}

const stringPurifier = (str) => {
    return DOMPurify.sanitize(str);
}

const sanitizeContainer = (arr) => {
    arr.forEach((item) => {
        $(`#${item}`).val(stringPurifier($(`#${item}`).val()))
    })
}

//let DOMPurify = {
//    sanitize: function (str) {
//        return str.replace(/\s{2,}/g, ' ');
//    }
//}

//String Encryption,
const EncryptPassword = async (text) => {

    const generateKey = async () => {
        const key = await window.crypto.subtle.generateKey({
            name: 'AES-GCM',
            length: 256
        },
            true, [
            'encrypt',
            'decrypt'
        ]);
        const exportedKey = await window.crypto.subtle.exportKey(
            'raw',
            key,
        );
        return bufferToBase64(exportedKey);
    }

    // arrayBuffer to base64
    const bufferToBase64 = (arrayBuffer) => {
        return window.btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
    }

    // load a base64 encoded key
    const loadKey = async (base64Key) => {
        return await window.crypto.subtle.importKey(
            'raw',
            base64ToBuffer(base64Key),
            "AES-GCM",
            true, [
            "encrypt",
            "decrypt"
        ]
        );
    }

    // base64 to arrayBuffer
    const base64ToBuffer = (base64) => {
        const binary_string = window.atob(base64);
        const len = binary_string.length;
        let bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binary_string.charCodeAt(i);
        }
        return bytes.buffer;
    }

    const cryptGcm = async (base64Key, bytes) => {
        const key = await loadKey(base64Key);
        const iv = window.crypto.getRandomValues(new Uint8Array(12));
        const algorithm = {
            iv,
            name: 'AES-GCM'
        };
        const cipherData = await window.crypto.subtle.encrypt(
            algorithm,
            key,
            bytes
        );

        // prepend the random IV bytes to raw cipherdata
        const cipherText = concatArrayBuffers(iv.buffer, cipherData);
        return bufferToBase64(cipherText);
    }

    // concatenate two array buffers
    const concatArrayBuffers = (buffer1, buffer2) => {
        let tmp = new Uint8Array(buffer1.byteLength + buffer2.byteLength);
        tmp.set(new Uint8Array(buffer1), 0);
        tmp.set(new Uint8Array(buffer2), buffer1.byteLength);
        return tmp.buffer;
    }

    const plaintext = text;
    const plaintextBytes = (new TextEncoder()).encode(plaintext, 'utf-8');
    const encryptionKey = await generateKey();
    const ciphertext = await cryptGcm(encryptionKey, plaintextBytes);
    // console.log("plaintext: ", plaintext);
    // console.log("encryptionKey (base64):", encryptionKey);
    // console.log("ciphertext (base64):", ciphertext);

    if (encryptionKey && ciphertext) {
        return encryptionKey + "$" + ciphertext;
    }


}


const DecryptPassword = async (encryptedText) => {
    const [encryptionKeyBase64, ciphertextBase64] = encryptedText.split("$");

    // Load the encryption key
    const loadKey = async (base64Key) => {
        return await window.crypto.subtle.importKey(
            'raw',
            base64ToBuffer(base64Key),
            "AES-GCM",
            true, [
            "encrypt",
            "decrypt"
        ]
        );
    }

    // base64 to arrayBuffer
    const base64ToBuffer = (base64) => {
        const binary_string = window.atob(base64);
        const len = binary_string.length;
        let bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binary_string.charCodeAt(i);
        }
        return bytes.buffer;
    }

    const key = await loadKey(encryptionKeyBase64);
    const ciphertextBuffer = base64ToBuffer(ciphertextBase64);
    const iv = ciphertextBuffer.slice(0, 12);
    const encryptedData = ciphertextBuffer.slice(12);

    const algorithm = {
        iv,
        name: 'AES-GCM'
    };

    const decryptedData = await window.crypto.subtle.decrypt(algorithm, key, encryptedData);
    const decryptedText = new TextDecoder().decode(decryptedData);
    return decryptedText;

}

const getEncryptionPassword = async (text) => {
    let dataPassword = await EncryptPassword(text).then((data) => {
        return data
    })
    return dataPassword
}


const propertyEncryption = async (prop) => {
    if (typeof prop !== 'string') {
        prop = JSON.stringify(prop)
    }

    let getEncryptionProperty = await EncryptPassword(prop)
    return getEncryptionProperty;
}

//Functions to reload the DataTable during CRUD operations.
//const dataTableCreateAndUpdate = (buttonid, dataTable, type = null, value = null) => {
//    dataTable.ajax.reload(() => {
//        if (buttonid.text() === "Update") {
//            const currentPage = dataTable.page();
//            dataTable.page(currentPage).draw(false);
//        } else {            
//            if (type && value) {
//                type.val(value).trigger("change");
//            }      
//            dataTable.page(0).draw(false);
//        }
//    }, false);
//}

//const dataTableDelete = (dataTable) => {
//    dataTable.ajax.reload(() => {
//        const currentPage = dataTable.page();
//        const pageInfo = dataTable.page.info();
//        const recordsOnPage = pageInfo.recordsDisplay - pageInfo.start;
//        if (recordsOnPage > 0) {
//            dataTable.page(currentPage).draw(false);
//        } else {
//            dataTable.page(currentPage - 1).draw(false);
//        }
//    }, false);
//}

//var check = document.querySelector('#check');
//var div = document.querySelector('#text');
//var reset = document.querySelector('#reset');

//var dictionary = 'https://rawcdn.githack.com/maheshmurag/bjspell/master/dictionary.js/en_US.js';
//var lang = BJSpell(dictionary, function () {
//    check.disabled = false;
//});

//check.addEventListener('click', function () {
//    var text = div.innerText;
//    var words = text.split(/\s/);

//    div.innerHTML = words.map(function (word) {
//        var correct = lang.check(word);
//        var className = correct ? 'correct' : 'misspelled';
//        var title = correct
//            ? 'Correct spelling'
//            : `Did you mean ${lang.suggest(word, 5).join(', ')}?`;
//        return `<span title="${title}" class="${className}">${word}</span>`;
//    }).join(' ');
//});

//reset.addEventListener('click', function () {
//    div.innerText = div.innerText;
//});

//For error - blocked aria-hidden on an element because its descendant retained focus.
$(function () {
    function blurActiveElementOnClick(selector) {
        document.querySelectorAll(selector).forEach(btn => {
            btn.addEventListener('click', () => {
                if (document.activeElement) {
                    document.activeElement.blur();
                }
            });
        });
    }

    blurActiveElementOnClick('[data-bs-dismiss="modal"], .finish_btn, .btn-primary');
})
