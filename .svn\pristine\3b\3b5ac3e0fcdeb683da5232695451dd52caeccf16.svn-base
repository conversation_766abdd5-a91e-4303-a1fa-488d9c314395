﻿using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.GlobalSetting;
using ContinuityPatrol.Shared.Core.Wrapper;
using GlobalSettingNameVm = ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetNames.GlobalSettingNameVm;

namespace ContinuityPatrol.Application.Mappings;

public class GlobalSettingProfile : Profile
{
    public GlobalSettingProfile()
    {
        CreateMap<CreateGlobalSettingCommand, GlobalSettingModel>().ReverseMap();
        CreateMap<UpdateGlobalSettingCommand, GlobalSettingModel>().ReverseMap();

        CreateMap<GlobalSetting, CreateGlobalSettingCommand>().ReverseMap();
        CreateMap<UpdateGlobalSettingCommand, GlobalSetting>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<GlobalSetting, GlobalSettingListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        //.ForMember(dest => dest.SValue, opt => opt.MapFrom(src => SecurityHelper.Decrypt(src.SValue)));

        CreateMap<GlobalSetting, GlobalSettingDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<GlobalSetting, GlobalSettingNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<PaginatedResult<GlobalSetting>, PaginatedResult<GlobalSettingListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}