﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.SVCGMMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.SVCGMMonitoringLogs.Queries.GetPaginatedList;

public class GetSVCGMMonitorLogPaginatedListQueryHandler : IRequestHandler<GetSVCGMMonitorLogPaginatedListQuery,
    PaginatedResult<SVCGMMonitorLogsListVm>>
{
    private readonly IMapper _mapper;
    private readonly ISVCGMMonitorLogRepository _svcGMMonitorLogRepository;

    public GetSVCGMMonitorLogPaginatedListQueryHandler(ISVCGMMonitorLogRepository svcGMMonitorLogRepository,
        IMapper mapper)
    {
        _svcGMMonitorLogRepository = svcGMMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<SVCGMMonitorLogsListVm>> Handle(GetSVCGMMonitorLogPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _svcGMMonitorLogRepository.GetPaginatedQuery();

        var productFilterSpec = new SvcgmMonitorLogFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<SVCGMMonitorLogsListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}