using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowActionTypeFixture : IDisposable
{
    public List<WorkflowActionType> WorkflowActionTypePaginationList { get; set; }
    public List<WorkflowActionType> WorkflowActionTypeList { get; set; }
    public WorkflowActionType WorkflowActionTypeDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowActionTypeFixture()
    {
        var fixture = new Fixture();

        WorkflowActionTypeList = fixture.Create<List<WorkflowActionType>>();

        WorkflowActionTypePaginationList = fixture.CreateMany<WorkflowActionType>(20).ToList();

        WorkflowActionTypeDto = fixture.Create<WorkflowActionType>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
