﻿$(function () {
    const sanitize = val => val?.replace(/\s+/g, '');
    const showError = (id, msg) => $(`#${id}-error`).text(msg).addClass('field-validation-error');
    const clearError = id => $(`#${id}-error`).text('').removeClass('field-validation-error');
    const CurrentPassword = async val => {
        if (!val) return showError('OldPassword', 'Enter current password'), false;
        clearError('OldPassword'); return true;
    };

    const NewPassword = async val => {
        if (!val) return showError('NewPassword', 'Enter new password'), false;
        clearError('NewPassword'); return true;
    };

    const ConfirmPassword = async val => {
        if (!val) return showError('ConfirmPassword', 'Enter confirm password'), false;
        clearError('ConfirmPassword'); return true;
    };
    $('#SaveFunction').on('click', async function () {
        const currentPassword = $('#CurrentPassword').val();
        const newPassword = $('#Password').val();
        const confirmPassword = $('#ConfirmPassword').val();
        const profileId = $('#selectWorkflowprofileName').val();

        $('#loginId').val(profileId);
        const isCurrentValid = await CurrentPassword(currentPassword);
        const isNewValid = await NewPassword(newPassword);
        const isConfirmValid = await ConfirmPassword(confirmPassword);

        if (profileId && isCurrentValid && isNewValid && isConfirmValid) {
            try {
                if (currentPassword.length < 30)
                    $('#CurrentPassword').val(await EncryptPassword(currentPassword));

                if (newPassword.length < 30)
                    $('#Password').val(await EncryptPassword(newPassword));

                if (confirmPassword.length < 30)
                    $('#ConfirmPassword').val(await EncryptPassword(confirmPassword));

                $(this).prop('disabled', true);
                $('#CreateForm').trigger('submit');
                $('#ProfileChangepasswordModal').modal('show');
            } catch (e) {
            }
        }
    });

    $(document).on('input keyup', '#CurrentPassword, #Password, #ConfirmPassword', function () {
        const id = this.id, val = sanitize(this.value || '');
        if (id === 'CurrentPassword') {
            inputpassword(id, val);
            val ? clearError('OldPassword') : showError('OldPassword', 'Enter current password');
        } else if (id === 'Password') {
            inputpassword(id, val);
            $('#ConfirmPassword').val('');            
            clearError('ConfirmPassword');
        } else if (id === 'ConfirmPassword') {
            inputConfirmpassword(id, val);
        }
    });

    $(document).on('blur', '#Password, #ConfirmPassword,#CurrentPassword', function () {
        if (this.value) blurpassword(this.id, sanitize(this.value));
    });

    $(document).on('focus', '#Password, #CurrentPassword, #ConfirmPassword', function () {
        this.id === 'ConfirmPassword' ? focusconfirmpassword(this.id) : focuspassword(this.id);
    });

    $('.toggle-password').on('click', async function () {
        const input = $(this).prev();
        const icon = $(this).find('i');
        if (input.attr('type') === 'password') {
            showPassword(input, icon);
            const val = input.val();
            if (val && val.length > 30)
                input.val(await onfocusPassword(val));
        } else {
            hidePassword(input, icon);
        }
    });
});

//$(".toggle-password").on('click',async function () {
//    let input = $(this).prev();
//    let icon = $(this).find("i");

//    if (input.attr("type") === "password") {

//        showPassword(input, icon);
//        let encryptedPassword = input.val();
//        if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
//            let afterLoginName = await onfocusPassword(encryptedPassword);
//            input.val(afterLoginName);

//        }

//    } else {

//        hidePassword(input, icon);
//    }
//});

//$(".cp-password-hide").on('mouseover',function () {
//    $(this).attr("title", "Hide Password");
//});

//$(".cp-password-visible").on('mouseover',function () {
//    $(this).attr("title", "Show Password");
//});




//function showPassword(input, icon) {
//    input.attr("type", "text");
//    icon.removeClass("cp-password-visible").addClass("cp-password-hide")
//    var icon = $(".cp-password-hide");
//    icon.attr("title", "Hide Password");
//    $(".cp-password-hide").on('mouseover',function () {
//        $(this).attr("title", "Hide Password");
//    });


//}

//function hidePassword(input, icon) {
//    input.attr("type", "password");
//    icon.removeClass("cp-password-hide").addClass("cp-password-visible");
//    var icon = $(".cp-password-visible");
//    icon.attr("title", "Show Password");
//    $(".cp-password-visible").on('mouseover', function () {
//        $(this).attr("title", "Show Password");
//    });


//}
//$("#reset_value").on('click',function () {
//    const errorElements = ['#OldPassword-error', '#NewPassword-error', '#ConfirmPassword-error'];
//    errorElements.forEach(element => {
//        $(element).text('').removeClass('field-validation-error');
//    });

//    $("#CurrentPassword").val("");
//    $("#ConfirmPassword").val("");
//    $("#Password").val("");
//    $('.toggle-password i').addClass('cp-password-visible fs-6');
//    $('#CurrentPassword').attr('type', 'password');
//    $('#Password').attr('type', 'password');
//});


//$('#Password').on('keydown keyup', async function () {
//    let passwordId = $('#Password').val();
//    const value = $(this).val();
//    const passwordValue = value.replace(/\s+/g, '');
//    $(this).val(passwordValue);
//    await validateNewPassword(passwordValue, passwordId);
//    $('#ConfirmPassword').val('');
//});
//$('#ConfirmPassword').on('keydown keyup', async function () {

//    let passwordconfirmId = $('#ConfirmPassword').val();
//    const value = $(this).val();
//    const confirmValue = value.replace(/\s+/g, '');
//    $(this).val(confirmValue);
//    await validateConfirmPassword(confirmValue, passwordconfirmId);
//});
//async function IsSamePasswordExist(workflowprofileId, value) {
//    try {
//        const result = await $.ajax({
//            url: "/ITAutomation/WorkflowProfile/IsWorkflowProfilePasswordExist",
//            method: 'GET',
//            data: {
//                workflowProfileId: workflowprofileId,
//                password: value
//            },
//            dataType: 'json'
//        });

//        return result.success;
//    } catch (error) {
//        console.error("Error occurred during AJAX call:", error);
//        return false; // Return false in case of error
//    }
//}

//$("#CurrentPassword").on('blur',async function () {

//    let loginName = $("#loginName").data("loginnames");
//    let password = $(this).val();

//    if (password != "" && password.length > 0 && password.length < 64) {
//        try {
//            let blurPassword = await EncryptPassword(password, "#" + this.id);

//            $(this).val(blurPassword);
//        } catch (error) {

//        }
//    }

//    $(this).attr('type', 'password');
//    $('.toggle-password i').removeClass('fs-6');
//    $(this).siblings('.toggle-password').find('i').addClass('cp-password-visible fs-6');

//});

//async function onfocusPassword(encryptedPassword) {
//    if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) { 
//            return await DecryptPassword(encryptedPassword);     
//    }
//}

//var profileId=$('#selectWorkflowprofileName').find(':selected').val()

//$("#SaveFunction").prop('disabled', true);

//$("#SaveFunction").on('click',async function () {
//    let currentPassword = $("#CurrentPassword").val();
//    let newPassword = $("#Password").val();
//    let confirmPassword = $("#ConfirmPassword").val();
//    let profileId = $('#selectWorkflowprofileName').find(':selected').val()
//    $("#loginId").val(profileId); 
//    let isCurrentPassword = await CurrentPassword (currentPassword);  
//    let isNewPassword = await NewPassword(newPassword);
//    let isConfirmPassword = await ConfirmPassword(confirmPassword);

//    if (profileId && isCurrentPassword && isNewPassword && isConfirmPassword) {
//        try {
//            if (currentPassword.length < 30) {
//                const encryptedCurrentPassword = await EncryptPassword(currentPassword);
//                $("#CurrentPassword").val(encryptedCurrentPassword);
//            }
//            if (newPassword.length < 30) {
//                const encryptedNewPassword = await EncryptPassword(newPassword);
//                $("#Password").val(encryptedNewPassword);
//            }
//            if (confirmPassword.length < 30) {
//                const encryptedConfirmPassword = await EncryptPassword(confirmPassword);
//                $("#ConfirmPassword").val(encryptedConfirmPassword);
//            }      
//            $(this).prop('disabled', true);
//            $("#CreateForm").trigger('submit');
//            $('#ProfileChangepasswordModal').modal('show')
//        } catch (error) {
           
//        }

//    }
//});


//async function CurrentPassword(value) {
//    const errorElement = $('#OldPassword-error');

//    if (!value) {
//        errorElement.text('Enter current password').addClass('field-validation-error');
//        return false;
//    } else {
//        errorElement.text('').removeClass('field-validation-error');
//    }

//    return true;
//}
//async function NewPassword(value) { 
//    const errorElement = $('#NewPassword-error');  
//    if (!value) {
//        errorElement.text('Enter new password').addClass('field-validation-error');
//        return false;
//    } 
//    else {
//        errorElement.text('').removeClass('field-validation-error');
//    }
//    return true;
//}
//async function ConfirmPassword(value) {
//    const errorElement = $('#ConfirmPassword-error');

//    if (!value) {
//        errorElement.text('Enter confirm password').addClass('field-validation-error');
//        return false;
//    } else {
//        errorElement.text('').removeClass('field-validation-error');
//    }

//    return true;
//}

//$('#CurrentPassword').on('keydown keyup', async function () {
   
//    const workflowprofileId = $('#selectWorkflowprofileName').val();
//    const value = $(this).val().replace(/\s+/g, '');
//    $(this).val(value);

//    const isValidPassword = await validateCurrentPassword(workflowprofileId, value);
//    if (!value) {
//        $('#OldPassword-error').text('Enter current password').addClass('field-validation-error');
//        return false;
//    }

//    if (!isValidPassword) {
//        $('#OldPassword-error').text('Incorrect current password').addClass('field-validation-error');
//    } else {
//        $('#OldPassword-error').text('').removeClass('field-validation-error');
//    }
//});

//async function validateCurrentPassword(profileId, currentPassword) {
    
//    try {
//        if (!currentPassword) {
//            $('#OldPassword-error').text('Enter current password').addClass('field-validation-error');
//            return false;
//        }

//        const result = await $.ajax({
//            url: "/ITAutomation/WorkflowProfileManagement/IsWorkflowProfilePasswordExist",
//            method: 'GET',
//            data: {
//                workflowProfileId: profileId,
//                password: currentPassword
//            },
//            dataType: 'json'
//        });

//        if (!result) {
//            $('#OldPassword-error').text('Incorrect current password').addClass('field-validation-error');
//            return false;
//        }

//        $('#OldPassword-error').text('').removeClass('field-validation-error');
//        return true;
//    } catch (error) {
       
//        return false;
//    }
//}

//async function validateNewPassword(value) {
  
//    const errorElement = $('#NewPassword-error');
//    let currentPassword = $("#CurrentPassword").val();
//    if (!value) {
//        errorElement.text('Enter new password').addClass('field-validation-error');
//        $("#SaveFunction").prop('disabled', true);
//        return false;
//    }
//    else if (currentPassword === value) {

//        errorElement.text('Same password already exists').addClass('field-validation-error');
//        return false;
//    }
//    try {

//        const validationResult = await PasswordPolicy(value);

//        if (validationResult === true) {

//            errorElement.text('').removeClass('field-validation-error');
//            $("#SaveFunction").prop('disabled', false);
//            return true;
//        }

//        else {
//            $("#SaveFunction").prop('disabled', true);
//            return false;
//        }

//    } catch (error) {
       
//        $("#SaveFunction").prop('disabled', true);
//        return false;
//    }
//}
//async function PasswordPolicy(value) {
//    const settingList = "Admin/Settings/GetList";
//    let defaultSkey = "Password Policy";

//    try {
//        const response = await $.ajax({
//            type: "GET",
//            url: RootUrl + settingList,
//            async: true
//        });

//        if (response && response.data && response.data.length > 0) {
//            const passwordPolicy = response.data.find(pwdplcy => pwdplcy.sKey === defaultSkey);
//            if (passwordPolicy) {
//                let passwordRules = JSON.parse(passwordPolicy.sValue);
//                let minSValue = passwordRules.minSValue;
//                let maxSValue = passwordRules.maxSValue;
//                let minUpSValue = passwordRules.minUpSValue;
//                let minNumSValue = passwordRules.minNumSValue;
//                let minLowSValue = passwordRules.minLowSValue;
//                let minSpclSValue = passwordRules.minSpclSValue;

//                return validatePassword(value, minSValue, minUpSValue, minNumSValue, minLowSValue, minSpclSValue, maxSValue);
//            }
//            else {
//                return settingPassword(value)

//            }

//        }
//        else {
//            return settingPassword(value)
//        }

//    } catch (error) {
       
//        return "Error fetching password policy";
//    }
//}
//function validatePassword(value, minSValue, minUpSValue, minNumSValue, minLowSValue, minSpclSValue, maxSValue) {

//    const uppercaseCount = (value.match(/[A-Z]/g) || []).length;
//    const numericCount = (value.match(/[0-9]/g) || []).length;
//    const lowercaseCount = (value.match(/[a-z]/g) || []).length;
//    const specialCount = (value.match(/[^a-zA-Z0-9]/g) || []).length;
//    const errorElement = $('#NewPassword-error');

//    if (value.length < parseInt(minSValue)) {
//        errorElement.text("Password is too short").addClass('field-validation-error');
//        return false;
//    }

//    if (uppercaseCount < parseInt(minUpSValue)) {
//        errorElement.text("Password should contain at least " + minUpSValue + " uppercase character(s)").addClass('field-validation-error');
//        return false;
//    }

//    if (numericCount < parseInt(minNumSValue)) {
//        errorElement.text("Password should contain at least " + minNumSValue + " numeric character(s).").addClass('field-validation-error');
//        return false;
//    }

//    if (value.length > parseInt(maxSValue)) {
//        errorElement.text("Password is too long").addClass('field-validation-error');
//        return false;
//    }

//    if (lowercaseCount < parseInt(minLowSValue)) {
//        errorElement.text("Password should contain at least " + minLowSValue + " lowercase character(s)").addClass('field-validation-error');
//        return false;
//    }

//    if (specialCount < parseInt(minSpclSValue)) {
//        errorElement.text("Password should contain at least " + minSpclSValue + " special character(s)").addClass('field-validation-error');
//        return false;
//    }

//    errorElement.text('').removeClass('field-validation-error');

//    return true;

//}
//function settingPassword(value) {

//    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[#$@!%&*?])[A-Za-z\d#$@!%&*?]{8,15}$/;
//    const errorElement = $('#NewPassword-error');
//    const errorMessages = {
//        invalid: "Invalid password",
//        length: "Password must be at least 8 characters",
//        number: "Password must contain at least one number",
//        symbol: "Password must contain at least one symbol",
//        uppercase: "Password must contain at least one uppercase letter",
//        lowercase: "Password must contain at least one lowercase letter",
//    };

//    if (value.length < 8) {
//        errorElement.text(errorMessages.length).addClass('field-validation-error');
//        return false;
//    } else if (!/\d/.test(value)) {
//        errorElement.text(errorMessages.number).addClass('field-validation-error');
//        return false;
//    } else if (!/[!@#$%^&*]/.test(value)) {
//        errorElement.text(errorMessages.symbol).addClass('field-validation-error');
//        return false;
//    } else if (!/[A-Z]/.test(value)) {
//        errorElement.text(errorMessages.uppercase).addClass('field-validation-error');
//        return false;
//    } else if (!/[a-z]/.test(value)) {
//        errorElement.text(errorMessages.lowercase).addClass('field-validation-error');
//        return false;
//    }
//    else if (!passwordRegex.test(value)) {
//        errorElement.text(errorMessages.invalid).addClass('field-validation-error');
//        return false;
//    }
//    errorElement.text('').removeClass('field-validation-error');

//    return true;

//}
//async function validateConfirmPassword(value) {
//    const errorElement = $('#ConfirmPassword-error');
    
//    if (!value) {
//        errorElement.text('Enter confirm password').addClass('field-validation-error');
//        $("#SaveFunction").prop('disabled', true);
//        return false;
//    }

//    const encryptPassword = $('#Password').val();
//    let newPassword = await onfocusPassword(encryptPassword);
//    let newdecryptPassword = encryptPassword;

//    if (newPassword !== value && newdecryptPassword !== value) {
//        errorElement.text('Password does not match').addClass('field-validation-error');
//    } else {
//        errorElement.text('').removeClass('field-validation-error');
//    }

//    if ($('#NewPassword-error').text()) {

//        $("#SaveFunction").prop('disabled', true);
//    } else if (errorElement.text() === '') {
//        $("#SaveFunction").prop('disabled', false);
//        return true;
//    } else {
//        $("#SaveFunction").prop('disabled', true);
//    }

//    return false;
//}


//$('#ConfirmPassword').on('input', function () {
//    const confirmPassword = $(this).val();
//    validateConfirmPassword(confirmPassword);
//});

//async function decryptPassword(password, inputSelector) {

//    let data = {

//        password: password
//    };

//    try {
//        const response = await $.ajax({
//            type: "GET",
//            url: RootUrl + "Admin/User/DecryptPassword",
//            data: data,
//            dataType: "json"
//        });
//        if (response) {
//            $(inputSelector).val(response);
//            return response;
//        }

//    } catch (error) {
//        console.error("Error hashing password: " + error);
//    }
//}
//async function hashPassword(loginName, password, inputSelector) {


//    let name = loginName.toLowerCase();
//    let data = {
//        loginName: name,
//        password: password
//    };

//    try {
//        const response = await $.ajax({
//            type: "GET",
//            url: RootUrl + "Account/HashPassword",
//            data: data,
//            dataType: "json"
//        });
//        if (response && response.encrypt) {

//            $(inputSelector).val(response.encrypt);
//        }

//    } catch (error) {
//        console.error("Error hashing password: " + error);
//    }
//}