﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.SVCMssqlMonitorLog.Queries.GetPaginatedList;

public class GetSVCMssqlMonitorLogPaginatedListQueryHandler : IRequestHandler<GetSVCMssqlMonitorLogPaginatedListQuery,
    PaginatedResult<SVCMssqlMonitorLogPaginatedListVm>>
{
    private readonly IMapper _mapper;
    private readonly ISVCMssqlMonitorLogRepository _svcMssqlMonitorLogRepository;

    public GetSVCMssqlMonitorLogPaginatedListQueryHandler(ISVCMssqlMonitorLogRepository svcMssqlMonitorLogRepository,
        IMapper mapper)
    {
        _svcMssqlMonitorLogRepository = svcMssqlMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<SVCMssqlMonitorLogPaginatedListVm>> Handle(
        GetSVCMssqlMonitorLogPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _svcMssqlMonitorLogRepository.GetPaginatedQuery();

        var productFilterSpec = new SvcMssqlMonitorLogFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<SVCMssqlMonitorLogPaginatedListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}