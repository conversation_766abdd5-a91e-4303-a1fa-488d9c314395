namespace ContinuityPatrol.Application.Features.Archive.Queries.GetDetail;

public class ArchiveDetailVm
{
    public string Id { get; set; }
    public string CompanyId { get; set; }
    public string TableNameProperties { get; set; }
    public string ArchiveProfileName { get; set; }
    public int Count { get; set; }
    public string CronExpression { get; set; }
    public string ScheduleTime { get; set; }
    public int ScheduleType { get; set; }
    public string BackUpType { get; set; }
    public string Type { get; set; }
    public string ClearBackup { get; set; }
    public string NodeId { get; set; }
    public string NodeName { get; set; }
}