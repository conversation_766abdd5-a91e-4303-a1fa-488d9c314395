﻿namespace ContinuityPatrol.Application.Features.FastCopyMonitor.Queries.GetByDataSyncId
{
    public class GetFastCopyMonitorByDataSyncIdsQueryHandler : IRequestHandler<GetFastCopyMonitorByDataSyncIdsQuery,List<FatCopyMonitorListVm>>
    {
        private readonly IFastCopyMonitorRepository _fastCopyMonitorRepository;
        private readonly IMapper _mapper;
        public GetFastCopyMonitorByDataSyncIdsQueryHandler(IFastCopyMonitorRepository fastCopyMonitorRepository,IMapper mapper)
        {
            _fastCopyMonitorRepository = fastCopyMonitorRepository;  
            _mapper = mapper;
        }
        public async Task<List<FatCopyMonitorListVm>> Handle(GetFastCopyMonitorByDataSyncIdsQuery request, CancellationToken cancellationToken)
        {
            var fastCopyMonitors = await _fastCopyMonitorRepository.GetByDataSyncJobIds(request.DataSyncJobIds);

            var fastCopyMonitorList = _mapper.Map<List<FatCopyMonitorListVm>>(fastCopyMonitors);

            return fastCopyMonitorList;
        }
    }
}
