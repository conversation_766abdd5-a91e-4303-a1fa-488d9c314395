using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MenuBuilderFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";

    public List<MenuBuilder> MenuBuilderPaginationList { get; set; }
    public List<MenuBuilder> MenuBuilderList { get; set; }
    public MenuBuilder MenuBuilderDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MenuBuilderFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<MenuBuilder>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Name, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.State, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .With(x => x.LastModifiedBy, UserId)
            .With(x => x.LastModifiedDate, DateTime.UtcNow)
        );

        MenuBuilderList = _fixture.Create<List<MenuBuilder>>();
        MenuBuilderPaginationList = _fixture.CreateMany<MenuBuilder>(20).ToList();
        MenuBuilderDto = _fixture.Create<MenuBuilder>();
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MenuBuilder CreateMenuBuilder(string name = null, string referenceId = null)
    {
        var menuBuilder = _fixture.Create<MenuBuilder>();

        if (!string.IsNullOrEmpty(name))
            menuBuilder.Name = name;

        if (!string.IsNullOrEmpty(referenceId))
            menuBuilder.ReferenceId = referenceId;

        return menuBuilder;
    }

    public MenuBuilder CreateMenuBuilderWithProperties(
        string name = null,
        string referenceId = null,
        string properties = null,
        string state = null,
        bool isActive = true)
    {
        var menuBuilder = CreateMenuBuilder(name, referenceId);

        if (!string.IsNullOrEmpty(properties))
            menuBuilder.Properties = properties;

        if (!string.IsNullOrEmpty(state))
            menuBuilder.State = state;

        menuBuilder.IsActive = isActive;

        return menuBuilder;
    }

    public List<MenuBuilder> CreateMultipleMenuBuilders(int count, string baseName = "MenuBuilder")
    {
        var menuBuilders = new List<MenuBuilder>();

        for (int i = 1; i <= count; i++)
        {
            var menuBuilder = CreateMenuBuilder($"{baseName}{i}");
            menuBuilders.Add(menuBuilder);
        }

        return menuBuilders;
    }

    public MenuBuilder CreateMenuBuilderWithSpecificName(string name)
    {
        return CreateMenuBuilder(name);
    }

    public MenuBuilder CreateInactiveMenuBuilder(string name = null)
    {
        return CreateMenuBuilderWithProperties(name: name, isActive: false);
    }

    public MenuBuilder CreateMenuBuilderWithEmptyName()
    {
        return CreateMenuBuilder("");
    }

    public MenuBuilder CreateMenuBuilderWithNullName()
    {
        var menuBuilder = CreateMenuBuilder();
        menuBuilder.Name = null;
        return menuBuilder;
    }

    public MenuBuilder CreateMenuBuilderWithLongName(int length = 1000)
    {
        var longName = new string('A', length);
        return CreateMenuBuilder(longName);
    }

    public MenuBuilder CreateMenuBuilderWithSpecialCharacters()
    {
        var specialName = "Test@Menu#Builder$123!";
        return CreateMenuBuilder(specialName);
    }

    public MenuBuilder CreateMenuBuilderWithUnicodeCharacters()
    {
        var unicodeName = "测试菜单构建器"; // Chinese characters
        return CreateMenuBuilder(unicodeName);
    }

    public MenuBuilder CreateMenuBuilderWithWhitespace()
    {
        var nameWithSpaces = "  Test Menu Builder  ";
        return CreateMenuBuilder(nameWithSpaces);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string ValidGuid = Guid.NewGuid().ToString();
        public static readonly string InvalidGuid = "INVALID_GUID";
        public static readonly string EmptyGuid = Guid.Empty.ToString();
        public static readonly string NullString = null;
        public static readonly string EmptyString = "";
        public static readonly string WhitespaceString = "   ";

        public static readonly string[] CommonMenuBuilderNames =
        {
            "Main Menu",
            "Navigation Menu",
            "Admin Menu",
            "User Menu",
            "Settings Menu"
        };

        public static readonly string[] SpecialCharacterNames =
        {
            "Test@Menu",
            "Menu#Builder",
            "Menu$Builder",
            "Menu!Builder",
            "Menu%Builder"
        };

        public static readonly string[] UnicodeNames =
        {
            "测试菜单", // Chinese
            "メニュー", // Japanese
            "메뉴", // Korean
            "Меню", // Russian
            "قائمة" // Arabic
        };
    }
}
