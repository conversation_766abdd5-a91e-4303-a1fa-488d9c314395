﻿using ContinuityPatrol.Application.Features.Form.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.FormModel;

namespace ContinuityPatrol.Application.UnitTests.Features.Form.Queries;

public class GetFormListQueryHandlerTests : IClassFixture<FormFixture>
{
    private readonly FormFixture _formFixture;

    private Mock<IFormRepository> _mockFormRepository;

    private readonly GetFormListQueryHandler _handler;

    public GetFormListQueryHandlerTests(FormFixture formFixture)
    {
        _formFixture = formFixture;

        _mockFormRepository = FormRepositoryMocks.GetFormRepository(_formFixture.Forms);

        _handler = new GetFormListQueryHandler(_formFixture.Mapper, _mockFormRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Valid_FormsList()
    {
        var result = await _handler.Handle(new GetFormListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<FormListVm>>();

        result[0].Id.ShouldBe(_formFixture.Forms[0].ReferenceId);
        result[0].Name.ShouldBe(_formFixture.Forms[0].Name);
        result[0].Properties.ShouldBe(_formFixture.Forms[0].Properties);
        result[0].Type.ShouldBe(_formFixture.Forms[0].Type);
        result[0].Version.ShouldBe(_formFixture.Forms[0].Version);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockFormRepository = FormRepositoryMocks.GetFormEmptyRepository();

        var handler = new GetFormListQueryHandler(_formFixture.Mapper, _mockFormRepository.Object);

        var result = await handler.Handle(new GetFormListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetFormListQuery(), CancellationToken.None);

        _mockFormRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}