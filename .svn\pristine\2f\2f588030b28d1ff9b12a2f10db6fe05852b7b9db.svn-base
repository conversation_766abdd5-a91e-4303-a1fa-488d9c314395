using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionFieldMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Queries.GetPaginatedList;

public class GetWorkflowActionFieldMasterPaginatedListQueryHandler : IRequestHandler<
    GetWorkflowActionFieldMasterPaginatedListQuery, PaginatedResult<WorkflowActionFieldMasterListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowActionFieldMasterRepository _workflowActionFieldMasterRepository;

    public GetWorkflowActionFieldMasterPaginatedListQueryHandler(IMapper mapper,
        IWorkflowActionFieldMasterRepository workflowActionFieldMasterRepository)
    {
        _mapper = mapper;
        _workflowActionFieldMasterRepository = workflowActionFieldMasterRepository;
    }

    public async Task<PaginatedResult<WorkflowActionFieldMasterListVm>> Handle(
        GetWorkflowActionFieldMasterPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _workflowActionFieldMasterRepository.GetPaginatedQuery();

        var productFilterSpec = new WorkflowActionFieldMasterFilterSpecification(request.SearchString);

        var workflowActionFieldMasterList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<WorkflowActionFieldMasterListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return workflowActionFieldMasterList;
    }
}