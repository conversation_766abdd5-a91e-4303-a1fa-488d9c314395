﻿using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseCount;

public class GetLicenseCountQueryHandler : IRequestHandler<GetLicenseCountQuery, LicenseCountVm>
{
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;

    public GetLicenseCountQueryHandler(ILicenseManagerRepository licenseManagerRepository,
        ILicenseInfoRepository licenseInfoRepository)
    {
        _licenseManagerRepository = licenseManagerRepository;
        _licenseInfoRepository = licenseInfoRepository;
    }

    public async Task<LicenseCountVm> Handle(GetLicenseCountQuery request, CancellationToken cancellationToken)
    {
        var baseLicenses = await _licenseManagerRepository.ListAllBaseLicense();

        var baseDatabaseCount =
            baseLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarydatabaseCount"));
        var baseReplicationCount =
            baseLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primaryreplicationCount"));
        //var baseServerDbCount =
        //    baseLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "serverDbCount"));
        var baseStorageCount =
            baseLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarystorageCount"));
        var baseVirtualizationCount =
            baseLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primaryvirtualizationCount"));
        var baseApplicationCount =
            baseLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primaryapplicationCount"));
        var baseDnsCount =
            baseLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarydnsCount"));
        var baseNetworkCount =
            baseLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarynetworkCount"));
        var baseThirdPartyCount =
            baseLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarythirdPartyCount"));

        var baseUsedDatabaseCount = 0;
        //var baseUsedDbServerCount = 0;
        var baseUsedReplicationCount = 0;
        var baseUsedStorageCount = 0;
        var baseUsedVirtualizationCount = 0;
        var baseUsedApplicationCount = 0;
        var baseUsedDnsCount = 0;
        var baseUsedNetworkCount = 0;
        var baseUsedThirdPartyCount = 0;


        foreach (var license in baseLicenses)
        {
            baseUsedDatabaseCount += await _licenseInfoRepository
                .GetAvailableCountByLicenseId(license.ReferenceId, Modules.Database.ToString());
            baseUsedReplicationCount += await _licenseInfoRepository
                .GetAvailableCountByLicenseId(license.ReferenceId, Modules.Replication.ToString());
            baseUsedStorageCount += await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId, Modules.Server.ToString(), "storage");
            baseUsedVirtualizationCount += await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId, Modules.Server.ToString(),
                    "virtualization");
            baseUsedApplicationCount += await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId, Modules.Server.ToString(),
                    "application");
            baseUsedDnsCount += await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId, Modules.Server.ToString(), "dns");
            baseUsedNetworkCount += await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId, Modules.Server.ToString(), "network");
            baseUsedThirdPartyCount += await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId, Modules.Server.ToString(),
                    "thirdparty");

            var derivedLicenses = await _licenseManagerRepository
                .GetDerivedLicenseDetailByBaseLicenseDetailAsync(license.CompanyId, license.PoNumber);

            baseDatabaseCount +=
                derivedLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarydatabaseCount"));
            baseReplicationCount +=
                derivedLicenses.Sum(x =>
                    GetJsonProperties.GetLicenseJsonValue(x.Properties, "primaryreplicationCount"));
            //baseServerDbCount +=
            //    derivedLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "serverDbCount"));
            baseStorageCount +=
                derivedLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarystorageCount"));
            baseVirtualizationCount += derivedLicenses.Sum(x =>
                GetJsonProperties.GetLicenseJsonValue(x.Properties, "primaryvirtualizationCount"));
            baseApplicationCount +=
                derivedLicenses.Sum(x =>
                    GetJsonProperties.GetLicenseJsonValue(x.Properties, "primaryapplicationCount"));
            baseDnsCount +=
                derivedLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarydnsCount"));
            baseNetworkCount +=
                derivedLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarynetworkCount"));
            baseThirdPartyCount +=
                derivedLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarythirdPartyCount"));

            foreach (var deLicense in derivedLicenses)
            {
                baseUsedDatabaseCount += await _licenseInfoRepository
                    .GetAvailableCountByLicenseId(deLicense.ReferenceId, Modules.Database.ToString());
                baseUsedReplicationCount += await _licenseInfoRepository
                    .GetAvailableCountByLicenseId(deLicense.ReferenceId, Modules.Replication.ToString());
                baseUsedStorageCount += await _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(deLicense.ReferenceId, Modules.Server.ToString(),
                        "storage");
                baseUsedVirtualizationCount += await _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(deLicense.ReferenceId, Modules.Server.ToString(),
                        "virtualization");
                baseUsedApplicationCount += await _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(deLicense.ReferenceId, Modules.Server.ToString(),
                        "application");
                baseUsedDnsCount += await _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(deLicense.ReferenceId, Modules.Server.ToString(),
                        "dns");
                baseUsedNetworkCount += await _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(deLicense.ReferenceId, Modules.Server.ToString(),
                        "network");
                baseUsedThirdPartyCount += await _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(deLicense.ReferenceId, Modules.Server.ToString(),
                        "thirdparty");
            }
        }

        var totalCount = baseDatabaseCount + baseReplicationCount
                                           + baseStorageCount + baseVirtualizationCount + baseApplicationCount
                                           + baseDnsCount + baseNetworkCount + baseThirdPartyCount;

        return new LicenseCountVm
        {
            TotalLicenseCount = totalCount,
            //ServerDbAvailableCount = baseServerDbCount,
            DatabaseAvailableCount = baseDatabaseCount,
            ReplicationAvailableCount = baseReplicationCount,
            // ServerDbUsedCount = baseUsedDbServerCount,
            DatabaseUsedCount = baseUsedDatabaseCount,
            ReplicationUsedCount = baseUsedReplicationCount,
            StorageAvailableCount = baseStorageCount,
            StorageUsedCount = baseUsedStorageCount,
            VirtualizationAvailableCount = baseVirtualizationCount,
            VirtualizationUsedCount = baseUsedVirtualizationCount,
            ApplicationAvailableCount = baseApplicationCount,
            ApplicationUsedCount = baseUsedApplicationCount,
            DnsAvailableCount = baseDnsCount,
            DnsUsedCount = baseUsedDnsCount,
            NetworkUsedCount = baseUsedNetworkCount,
            NetworkAvailableCount = baseNetworkCount,
            ThirdPartyUsedCount = baseUsedThirdPartyCount,
            ThirdPartyAvailableCount = baseThirdPartyCount
        };
    }
}