﻿using ContinuityPatrol.Application.Features.UserRole.Commands.Create;
using ContinuityPatrol.Application.Features.UserRole.Commands.Update;
using ContinuityPatrol.Application.Features.UserRole.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.UserRoleModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class UserRoleControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IUserRoleService> _mockUserRoleService = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<ILogger<UserRoleController>> _mockLogger = new();
        private  UserRoleController _controller;

        public UserRoleControllerShould()
        {
            Initialize();
        }
        internal void Initialize()
        {
            _controller = new UserRoleController(
                _mockPublisher.Object,
                _mockUserRoleService.Object,
                _mockMapper.Object,
                _mockLogger.Object);
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public async Task List_ShouldPublishUserRolePaginatedEventAndReturnView()
        {
            
            var paginatedList = new AutoFixture.Fixture().Create<PaginatedResult<UserRoleListVm>>();
            var userRole= new AutoFixture.Fixture().Create<UserRoleListVm>();
            var query = new GetUserRolePaginatedListQuery();
            _mockMapper.Setup(m => m.Map<GetUserRolePaginatedListQuery>(userRole)).Returns(query);
            _mockUserRoleService.Setup(s => s.GetUserRolePaginatedList(query))
                .ReturnsAsync(paginatedList);

            
            var result = await _controller.List();

            
            var viewResult = Assert.IsType<ViewResult>(result);
            var model = Assert.IsType<UserRoleViewModel>(viewResult.Model);
            Assert.NotNull(model);
            Assert.Equal(paginatedList, model.PaginatedUserRole);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidModel_ShouldCreateUserRole()
        {

            var userRoleModel = new AutoFixture.Fixture().Create<UserRoleViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", ""); // Empty id for create scenario
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateUserRoleCommand ();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };
            _mockMapper.Setup(m => m.Map<CreateUserRoleCommand>(It.IsAny<UserRoleViewModel>())).Returns(createCommand);
            _mockUserRoleService.Setup(s => s.CreateAsync(createCommand)).ReturnsAsync(response);


            var result = await _controller.CreateOrUpdate(userRoleModel);


            Assert.IsType<JsonResult>(result);
            var jsonResult = (JsonResult)result;
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidModel_ShouldUpdateUserRole()
        {

            var userRoleModel = new AutoFixture.Fixture().Create<UserRoleViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "22"); // Non-empty id for update scenario
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateUserRoleCommand ();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };
            _mockMapper.Setup(m => m.Map<UpdateUserRoleCommand>(It.IsAny<UserRoleViewModel>())).Returns(updateCommand);
            _mockUserRoleService.Setup(s => s.UpdateAsync(updateCommand)).ReturnsAsync(response);


            var result = await _controller.CreateOrUpdate(userRoleModel);


            Assert.IsType<JsonResult>(result);
            var jsonResult = (JsonResult)result;
            Assert.NotNull(jsonResult.Value);

        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleValidationException()
        {

            var userRoleModel = new AutoFixture.Fixture().Create<UserRoleViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateUserRoleCommand();
            var response = new BaseResponse { Success = false, Message = "Validation error" };
            _mockMapper.Setup(m => m.Map<UpdateUserRoleCommand>(It.IsAny<UserRoleViewModel>())).Returns(updateCommand);
            _mockUserRoleService.Setup(s => s.UpdateAsync(updateCommand)).ReturnsAsync(response);

            var result = await _controller.CreateOrUpdate(userRoleModel);


            Assert.IsType<JsonResult>(result);
            var jsonResult = (JsonResult)result;
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleGeneralException()
        {

            var userRoleModel = new AutoFixture.Fixture().Create<UserRoleViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateUserRoleCommand();
            _mockMapper.Setup(m => m.Map<UpdateUserRoleCommand>(It.IsAny<UserRoleViewModel>())).Returns(updateCommand);
            _mockUserRoleService.Setup(s => s.UpdateAsync(updateCommand)).ThrowsAsync(new Exception("General error"));


            var result = await _controller.CreateOrUpdate(userRoleModel);


            Assert.IsType<JsonResult>(result);
            var jsonResult = (JsonResult)result;
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task IsRoleNameExist_ShouldReturnTrue()
        {
            
            var roleName = "Admin";
            var id = "123";
            _mockUserRoleService.Setup(s => s.IsUserRoleNameExist(roleName, id)).ReturnsAsync(true);
            
            var result = await _controller.IsRoleNameExist(roleName, id);

            Assert.True(result);
        }

        [Fact]
        public async Task IsRoleNameExist_ShouldReturnFalse()
        {
            
            var roleName = "User";
            var id = "123";
            _mockUserRoleService.Setup(s => s.IsUserRoleNameExist(roleName, id)).ReturnsAsync(false);

            
            var result = await _controller.IsRoleNameExist(roleName, id);

            
            Assert.False(result);
        }

        [Fact]
        public async Task Delete_ShouldDeleteUserRole()
        {

            var id = "123";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockUserRoleService.Setup(s => s.DeleteAsync(id)).ReturnsAsync(response);


            var result = await _controller.Delete(id);


            Assert.IsType<RedirectToActionResult>(result);
            var redirectResult = (RedirectToActionResult)result;
            Assert.Equal("List", redirectResult.ActionName);
            Assert.Equal("UserRole", redirectResult.ControllerName);
        }

        [Fact]
        public async Task Delete_ShouldHandleException()
        {

            var id = "123";
            _mockUserRoleService.Setup(s => s.DeleteAsync(id)).Throws(new Exception("Error"));


            var result = await _controller.Delete(id);


            Assert.IsType<RedirectToActionResult>(result);
            var redirectResult = (RedirectToActionResult)result;
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_WithEmptyId_ShouldCreateUserRole()
        {

            var userRoleModel = new AutoFixture.Fixture().Create<UserRoleViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", ""); // Empty id triggers create path
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateUserRoleCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };
            _mockMapper.Setup(m => m.Map<CreateUserRoleCommand>(It.IsAny<UserRoleViewModel>())).Returns(createCommand);
            _mockUserRoleService.Setup(s => s.CreateAsync(createCommand)).ReturnsAsync(response);


            var result = await _controller.CreateOrUpdate(userRoleModel);


            Assert.IsType<JsonResult>(result);
            var jsonResult = (JsonResult)result;
            Assert.NotNull(jsonResult.Value);
            _mockUserRoleService.Verify(s => s.CreateAsync(createCommand), Times.Once);
        }
    }
}
