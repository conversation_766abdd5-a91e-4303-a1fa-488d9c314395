using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class CyberComponentFilterSpecification : Specification<CyberComponent>
{
    public CyberComponentFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.Name != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');
                
                foreach (var stringItem in stringArray)
                    
                    if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("site=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.SiteName.Contains(stringItem.Replace("site=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("serverType=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ServerType.Contains(stringItem.Replace("serverType=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("type=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Type.Contains(stringItem.Replace("type=", "", StringComparison.OrdinalIgnoreCase)));
                   
            }
            else
            {
                Criteria = p =>
                    p.Name.Contains(searchString) || p.Type.Contains(searchString) ||
                    p.ServerType.Contains(searchString) || p.SiteName.Contains(searchString);
            }
        }
    }
}