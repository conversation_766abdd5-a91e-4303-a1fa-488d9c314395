using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class EmployeeFilterSpecification : Specification<Employee>
{
    public EmployeeFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.Name != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("age=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Age.Contains(stringItem.Replace("age=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("phonenumber=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Phonenumber.Contains(stringItem.Replace("phonenumber=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("address=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Address.Contains(stringItem.Replace("address=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("companyname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.CompanyName.Contains(stringItem.Replace("companyname=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.Name.Contains(searchString) || p.Age.Contains(searchString) ||
                    p.Phonenumber.Contains(searchString) || p.Address.Contains(searchString) ||
                    p.CompanyName.Contains(searchString);
            }
        }
    }
}