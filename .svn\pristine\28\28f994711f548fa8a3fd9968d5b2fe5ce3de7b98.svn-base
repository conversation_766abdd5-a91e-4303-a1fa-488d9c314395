﻿@model ContinuityPatrol.Domain.ViewModels.InfraObjectModel.InfraObjectViewModel

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}


<link href="~/lib/jquery.steps/jquery.steps.css" rel="stylesheet" />
<link href="~/css/wizard.css" rel="stylesheet" />
<style>
    .dataTables_length label {
        display: flex !important;
        align-items: center !important;
    }
    .truncate{
        max-width: 9em;
    }
    .manage-btn .btn{
        font-size:0.713rem !important;
        padding-left: .45rem !important;
        padding-right: .45rem !important;
    }
</style>
@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-settings"></i><span>Manage Operational Service</span></h6>
            <div class="d-flex gap-2">
                <div class="input-group" style="width:300px">
                    <span class="form-label mb-0 input-group-text me-2">Operational Service : </span>
                    <span class="input-group-text"><i class="cp-business-service"></i></span>
                    <select id="businessService" class="form-select" data-placeholder="All">
                        <option>All</option> 
                    </select>
                </div>
                <div class="input-group" style="width:300px" id="BF_change">
                    <span class="form-label mb-0 input-group-text me-2">Operational Function : </span>
                    <span class="input-group-text"><i class="cp-business-function"></i></span>
                    <select id="businessFunction" class="form-select" data-placeholder="Select Operational Function">
                    </select>
                </div>
            </div>
        </div>
        <div class="card-body pt-0">
            <div class="row g-2">
                <div class="col-xl-3 col-md-5 col-sm-12">
                    <div id="CreateForm">
                        <div class="mb-3">
                            <section>
                                <div>
                                    <div class="form-label">Assigned InfraObject(s)</div>
                                    <div class="Workflow-Tree" style="height: calc(100vh - 210px); overflow-y:auto;">
                                        <details id="selectAllOpen" class="ms-0">
                                            <summary>
                                                <input class="form-check-input selectAll" type="checkbox" id="selectAll">All <!-- Add "Select All" checkbox -->
                                                <ul class="tree ps-0" id="treeview">
                                                </ul>
                                            </summary>
                                        </details>
                                        <input type="hidden" id="textProperties" />
                                        <div id="error-message" class="alert alert-danger" style="display: none;">
                                            Select atleast one infraObject.
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                        <div class="d-flex pe-3 manage-btn">
                            <button type="button" value="Active" style="background-color:#fafff8" class="btn btn-transparent border border-success-subtle me-1 d-flex align-items-center  text-primary-emphasis" data-bs-toggle="modal" id="active"> <i class="cp-active-inactive text-success me-1 " title="Active"></i> &nbsp;Active</button>
                            <button type="button" style="background-color:#fff0f2"
                                    class="btn btn-transparent border border-primary-subtle me-1 d-flex align-items-center"
                                    value="Maintenance" id="maintenance" data-bs-toggle="modal">
                                <i class="cp-maintenance text-primary me-1" title="Maintenance" style="color: #5b70f5;"></i>
                                &nbsp;Maintenance
                            </button>

                            <button type="button" style="background-color:#ffe8a3" class="btn btn-transparent border border-warning me-1 d-flex align-items-center" value="Unlock" id="Unlock" data-bs-toggle="modal"> <i class="cp-lock text-warning me-1" title="Lock"></i> &nbsp;Unlock</button>
                            <button type="button" class="btn btn-transparent border border-secondary-subtle text-primary-emphasis d-flex align-items-center" style="background-color:#ededed" id="btnReset">Reset</button>
                            <input  type="hidden" id="updateStatus" class="form-control" />
                            <input  type="hidden" id="reasonFor" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="col-xl-9 col-md-7 col-sm-12">
                    <div id="collapetable">
                        <table id="manageBusinessService" class="datatable table table-hover dataTable no-footer" style="width:100%">
                            <thead>
                                <tr>
                                    <th class="SrNo_th">Sr.No</th>
                                    <th>InfraObject Name </th>
                                    <th>Operational&nbsp;Function</th>
                                    <th>Operational&nbsp;Service</th>
                                    <th>Type</th>
                                    <th>Replication</th>
                                    <th style="text-overflow:ellipsis">Reason</th>
                                    <th class="Action-th">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="ConfimationModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content" style="border-radius:70px 70px 10px 10px">
            <form>
                <div class="modal-header p-0">
                    <img class="delete-img" src="~/img/isomatric/maintanence.svg" alt="Delete Img" />
                </div>
                <div class="modal-body text-center pt-0">
                    <h5 class="fw-bold">Confirmation</h5>
                    <div class="mb-3 form-group" id="boxContainer">
                        <div class="form-label"><i class="cp-question-mark me-1"></i> Reason</div>
                        <div>
                            <textarea class="form-control border" id="textArea" placeholder="Enter The Reason" rows="2" cols="50" maxlength="200" style="resize: none;"></textarea>
                        </div>
                    </div>

                    <p>Do you want to switch the state to <span class="font-weight-bolder text-primary" id="stateText"></span> ?</p>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary btn-sm" id="btnSave">Yes</button>
                </div>
            </form>
        </div>
    </div>
</div>

@* Confirmation Modal *@
<div class="modal" id="stateConfirmationModal" tabindex="-1" area-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <img class="w-100" src="~/img/isomatric/confirmation.svg" alt="Duplicate Actions" loading="lazy" />
            </div>
            <div class="modal-body text-center pt-5">
                <p>
                    Some of infraObject(s) are in a different state rather than
                    <span class="font-weight-bolder text-primary" id="statests"></span>.
                    Do you want to change the state from
                    <span class="font-weight-bolder text-primary" id="activestatein"></span>?
                </p>                
            </div>

            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-target="#CreateModal" data-bs-toggle="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="stateConfirmation">Yes</button>
            </div>

        </div>
    </div>
</div>


<div id="ManageCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.CreateAndEdit" aria-hidden="true"></div>
<script src="~/js/Manage/Manage Operational Service/ManageBusinessServices.js"></script>
<script>

    $(document).ready(function () {
        //$(".box").hide();
        //$('input[type="radio"]').click(function () {
        //    var inputValue = $(this).attr("value");
        //    var targetBox = $("." + inputValue);
        //    $(".box").not(targetBox).hide();
        //    $(targetBox).show();
        //});
    });

</script>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
