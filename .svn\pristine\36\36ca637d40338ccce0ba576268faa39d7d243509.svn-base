﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.ImpactAvailabilityModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetPaginatedList;

public class GetImpactAvailabilityPaginatedListQueryHandler : IRequestHandler<GetImpactAvailabilityPaginatedListQuery,
    PaginatedResult<ImpactAvailabilityViewModel>>
{
    private readonly IImpactAvailabilityRepository _impactAvailabilityRepository;
    private readonly IMapper _mapper;

    public GetImpactAvailabilityPaginatedListQueryHandler(IImpactAvailabilityRepository impactAvailabilityRepository,
        IMapper mapper)
    {
        _impactAvailabilityRepository = impactAvailabilityRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<ImpactAvailabilityViewModel>> Handle(
        GetImpactAvailabilityPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var productFilterSpec = new ImpactAvailabilityFilterSpecification(request.SearchString);

        var queryable = await _impactAvailabilityRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var sitesList = _mapper.Map<PaginatedResult<ImpactAvailabilityViewModel>>(queryable);

        return sitesList;
    }
}