using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class JobFixture : IDisposable
{
    public List<Domain.Entities.Job> JobPaginationList { get; set; }
    public List<Domain.Entities.Job> JobList { get; set; }
    public Domain.Entities.Job JobDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public JobFixture()
    {
        var fixture = new Fixture();

        JobList = fixture.Create<List<Domain.Entities.Job>>();

        JobPaginationList = fixture.CreateMany<Domain.Entities.Job>(20).ToList();

        JobPaginationList.ForEach(x => x.CompanyId = CompanyId);

        JobList.ForEach(x => x.CompanyId = CompanyId);

        JobDto = fixture.Create<Domain.Entities.Job>();

        JobDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
