using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DrReadyRepositoryTests : IClassFixture<DrReadyFixture>
{
    private readonly DrReadyFixture _drReadyFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DrReadyRepository _repository;

    public DrReadyRepositoryTests(DrReadyFixture drReadyFixture)
    {
        _drReadyFixture = drReadyFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DrReadyRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var drReady = _drReadyFixture.DrReadyDto;

        // Act
        var result = await _repository.AddAsync(drReady);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drReady.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(drReady.BusinessServiceName, result.BusinessServiceName);
        Assert.Single(_dbContext.DrReadys);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var drReady = _drReadyFixture.DrReadyDto;
        await _repository.AddAsync(drReady);

        drReady.BusinessServiceName = "Updated Service Name";

        // Act
        var result = await _repository.UpdateAsync(drReady);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Service Name", result.BusinessServiceName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var drReady = _drReadyFixture.DrReadyDto;
        await _repository.AddAsync(drReady);

        // Act
        var result = await _repository.DeleteAsync(drReady);

        // Assert
        Assert.Equal(drReady.BusinessServiceId, result.BusinessServiceId);
        Assert.Empty(_dbContext.DrReadys);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var drReady = _drReadyFixture.DrReadyDto;
        var addedEntity = await _repository.AddAsync(drReady);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var drReady = _drReadyFixture.DrReadyDto;
        await _repository.AddAsync(drReady);

        // Act
        var result = await _repository.GetByReferenceIdAsync(drReady.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drReady.ReferenceId, result.ReferenceId);
        Assert.Equal(drReady.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var drReadies = _drReadyFixture.DrReadyList;
        await _repository.AddRangeAsync(drReadies);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drReadies.Count, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldNotReturnInactiveEntities()
    {
        // Arrange
        var drReadies = _drReadyFixture.DrReadyList;
        drReadies.First().IsActive = false; // Make one inactive
        await _repository.AddRangeAsync(drReadies);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drReadies.Count - 1, result.Count); // Should exclude the inactive one
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region GetDrReadyByBusinessServiceId Tests

    [Fact]
    public async Task GetDrReadyByBusinessServiceId_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var drReady = _drReadyFixture.DrReadyDto;
        await _repository.AddAsync(drReady);

        // Act
        var result = await _repository.GetDrReadyByBusinessServiceId(DrReadyFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(DrReadyFixture.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetDrReadyByBusinessServiceId_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var drReadies = _drReadyFixture.DrReadyList;
        await _repository.AddRangeAsync(drReadies);

        // Act
        var result = await _repository.GetDrReadyByBusinessServiceId("non-existent-service-id");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetDrReadyByBusinessServiceId_ShouldReturnOnlyActiveEntity()
    {
        // Arrange
        var drReady1 = _drReadyFixture.DrReadyDto;
        drReady1.IsActive = true;
        
        var drReady2 = _drReadyFixture.DrReadyDto;
        drReady2.ReferenceId = Guid.NewGuid().ToString();
        drReady2.IsActive = false; // Inactive
        drReady2.BusinessServiceId = DrReadyFixture.BusinessServiceId;
        
        await _repository.AddAsync(drReady1);
        await _repository.AddAsync(drReady2);

        // Act
        var result = await _repository.GetDrReadyByBusinessServiceId(DrReadyFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.IsActive);
        Assert.Equal(drReady1.ReferenceId, result.ReferenceId);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var drReadies = _drReadyFixture.DrReadyList;

        // Act
        var result = await _repository.AddRangeAsync(drReadies);

        // Assert
        Assert.Equal(drReadies.Count, result.Count());
        Assert.Equal(drReadies.Count, _dbContext.DrReadys.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var drReadies = _drReadyFixture.DrReadyList;
        await _repository.AddRangeAsync(drReadies);

        // Act
        var result = await _repository.RemoveRangeAsync(drReadies);

        // Assert
        Assert.Equal(drReadies.Count, result.Count());
        Assert.Empty(_dbContext.DrReadys);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var drReadies = _drReadyFixture.DrReadyList.Take(5).ToList();
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(drReadies);
        var initialCount = drReadies.Count;
        
        var toUpdate = drReadies.Take(2).ToList();
        toUpdate.ForEach(x => x.BusinessServiceName = "UpdatedServiceName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = drReadies.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.BusinessServiceName == "UpdatedServiceName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
