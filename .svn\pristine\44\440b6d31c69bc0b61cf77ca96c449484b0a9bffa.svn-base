using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DynamicDashboardWidget.Events.Create;

public class DynamicDashboardWidgetCreatedEventHandler : INotificationHandler<DynamicDashboardWidgetCreatedEvent>
{
    private readonly ILogger<DynamicDashboardWidgetCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DynamicDashboardWidgetCreatedEventHandler(ILoggedInUserService userService,
        ILogger<DynamicDashboardWidgetCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(DynamicDashboardWidgetCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} DynamicDashboardWidget",
            Entity = "DynamicDashboardWidget",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"DynamicDashboardWidget '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"DynamicDashboardWidget '{createdEvent.Name}' created successfully.");
    }
}