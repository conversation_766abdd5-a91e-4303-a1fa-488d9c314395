﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}


<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title"><i class="cp-monitoring"></i><span>Infra Object Application Monitor</span></h6>
        <span><i class="cp-time me-2"></i>Last Monitored Time : 12/9/2022 1:39:31 PM</span>
    </div>
    <div class="monitor_pages">
        <div class="row g-2 mt-0">
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        Protection Policy Status
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                              <thead>
                            <tr>
                                    <th>Policy Name</th>
                                    <th >Asset Count</th>
                                    <th>last Run Status</th>
                                    <th>Status</th>
                            </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-incident-summery me-1"></i>vclusterprot-pp1</td>
                                    <td class="text-truncate">2 </td>
                                    <td class="text-truncate text-success">Success</td>
                                    <td class="text-truncate"><i class="text-success cp-circle-switch me-1"></i>ENABLED</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-incident-summery me-1"></i>critical_prot-policy</td>
                                    <td class="text-truncate">2 </td>
                                    <td class="text-truncate text-success">Success</td>
                                    <td class="text-truncate"><i class="text-success cp-circle-switch me-1"></i>ENABLED</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center pt-0">
                        @* <img src="~/img/isomatric/solutiondiagram.svg" height="159px;" /> *@
                        <div id="Solution_Diagram" class="w-50"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-2">
            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        All Protection Policy - Job Status (Last 24 Hours)
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>
                                        Successful
                                    </th>
                                    <th>Failed</th>
                                    <th>Partial Successful</th>
                                    <th>In Progress</th>
                                    <th>Cancelled</th>
                                    <th>Total Jobs</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate ">41</td>
                                    <td class="text-truncate">6</td>
                                    <td class="text-truncate">0</td>
                                    <td class="text-truncate">0</td>
                                    <td class="text-truncate">0</td>
                                    <td class="text-truncate">46</td>
                              
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title">
                        Protection Policy - Job Status (Last 24 Hours)
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>
                                        Job ID
                                    </th>
                                    <th>Status</th>
                                    <th>Description</th>
                                    <th>Policy Name</th>
                                    <th>Assets</th>
                                    <th>job Type</th>
                                    <th>Start Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-user-configuration me-1"></i>28F890AC</td>
                                    <td class="text-truncate"><i class="text-success cp-success me-1"></i>Success</td>
                                    <td class="text-truncate">Instant Restore VM to New: Restoring copy to new VM..</td>
                                    <td class="text-truncate">vclusterprot-pp1</td>
                                    <td class="text-truncate">1</td>
                                    <td class="text-truncate">Restore</td>
                                    <td class="text-truncate">Dec 8, 2022, 11:29:40 PM</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-user-configuration me-1"></i>A2E76C2E</td>
                                    <td class="text-truncate"><i class="text-success cp-success me-1"></i>Success</td>
                                    <td class="text-truncate">Instant Restore VM to New: Restoring copy to new VM..</td>
                                    <td class="text-truncate">vclusterprot-pp1</td>
                                    <td class="text-truncate">1</td>
                                    <td class="text-truncate">Restore</td>
                                    <td class="text-truncate">Dec 8, 2022, 11:29:40 PM</td>
                                </tr>
                                 <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-user-configuration me-1"></i>498ABFAC</td>
                                    <td class="text-truncate"><i class="text-success cp-success me-1"></i>Success</td>
                                    <td class="text-truncate">Instant Restore VM to New: Restoring copy to new VM..</td>
                                    <td class="text-truncate">vclusterprot-pp1</td>
                                    <td class="text-truncate">1</td>
                                    <td class="text-truncate">Restore</td>
                                    <td class="text-truncate">Dec 8, 2022, 11:29:40 PM</td>
                                </tr>
                                 <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-user-configuration me-1"></i>10C0EE03</td>
                                    <td class="text-truncate"><i class="text-success cp-success me-1"></i>Success</td>
                                    <td class="text-truncate">Instant Restore VM to New: Restoring copy to new VM..</td>
                                    <td class="text-truncate">vclusterprot-pp1</td>
                                    <td class="text-truncate">1</td>
                                    <td class="text-truncate">Restore</td>
                                    <td class="text-truncate">Dec 8, 2022, 11:29:40 PM</td>
                                </tr>
                                 <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-user-configuration me-1"></i>E8768C61</td>
                                    <td class="text-truncate"><i class="text-danger cp-error me-1"></i>Failed (1)</td>
                                    <td class="text-truncate">Instant Restore VM to New: Restoring copy to new VM..</td>
                                    <td class="text-truncate">vclusterprot-pp1</td>
                                    <td class="text-truncate">1</td>
                                    <td class="text-truncate">Restore</td>
                                    <td class="text-truncate">Dec 8, 2022, 11:29:40 PM</td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
           
            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        Recovery Activities Monitoring
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>
                                        Service Name
                                    </th>
                                    <th class="text-primary">Server IP/HostName</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-database-warning me-1"></i>MSSQL Server</td>
                                    <td class="text-truncate"><i class="text-success cp-fal-server me-1"></i>**************</td>
                                    <td class="text-truncate"><i class="text-primary cp-reload me-1 cp-animate"></i>Running</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-log-file-name me-1"></i>MSSQL Server</td>
                                    <td class="text-truncate"><i class="text-success cp-fal-server me-1"></i>**************</td>
                                    <td class="text-truncate"><i class="text-primary cp-reload me-1 cp-animate"></i>Running</td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>