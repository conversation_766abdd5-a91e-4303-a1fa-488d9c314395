﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Queries;

public class GetWorkflowOperationGroupDetailQueryHandlerTests : IClassFixture<WorkflowOperationGroupFixture>
{
    private readonly WorkflowOperationGroupFixture _workflowOperationGroupFixture;

    private readonly Mock<IWorkflowOperationGroupRepository> _mockWorkflowOperationGroupRepository;

    private readonly GetWorkflowOperationGroupDetailQueryHandler _handler;

    public GetWorkflowOperationGroupDetailQueryHandlerTests(WorkflowOperationGroupFixture workflowOperationGroupFixture)
    {
        _workflowOperationGroupFixture = workflowOperationGroupFixture;

        _mockWorkflowOperationGroupRepository = WorkflowOperationGroupRepositoryMocks.GetWorkflowOperationGroupRepository(_workflowOperationGroupFixture.WorkflowOperationGroups);

        _handler = new GetWorkflowOperationGroupDetailQueryHandler(_workflowOperationGroupFixture.Mapper, _mockWorkflowOperationGroupRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_WorkflowOperationGroup_Details_When_Valid()
    {
        var result = await _handler.Handle(new GetWorkflowOperationGroupDetailQuery { Id = _workflowOperationGroupFixture.WorkflowOperationGroups[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<WorkflowOperationGroupDetailVm>();
        result.Id.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].ReferenceId);
        result.InfraObjectId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].InfraObjectId);
        result.InfraObjectName.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].InfraObjectName);
        result.WorkflowId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowId);
        result.WorkflowName.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowName);
        result.CurrentActionId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].CurrentActionId);
        result.CurrentActionName.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].CurrentActionName);
        result.Status.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].Status);
        result.Message.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].Message);
        result.WorkflowOperationId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowOperationId);
        result.ProgressStatus.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].ProgressStatus);
        result.JobName.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].JobName);
        result.WorkflowVersion.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowVersion);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_Invalid_WorkflowOperationGroupId()
    {
        var handler = new GetWorkflowOperationGroupDetailQueryHandler(_workflowOperationGroupFixture.Mapper, _mockWorkflowOperationGroupRepository.Object);

        await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(new GetWorkflowOperationGroupDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowByReferenceId_OneTime()
    {
        await _handler.Handle(new GetWorkflowOperationGroupDetailQuery() { Id = _workflowOperationGroupFixture.WorkflowOperationGroups[0].ReferenceId }, CancellationToken.None);

        _mockWorkflowOperationGroupRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}