using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class PageBuilderFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string PageBuilderName => "TestPageBuilder";
    public static string PageBuilderType => "Dashboard";

    public List<PageBuilder> PageBuilderPaginationList { get; set; }
    public List<PageBuilder> PageBuilderList { get; set; }
    public PageBuilder PageBuilderDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public PageBuilderFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<PageBuilder>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Name, () => PageBuilderName + "_" + _fixture.Create<string>())
            .With(x => x.Type, () => PageBuilderType)
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.IsLock, false)
            .With(x => x.IsPublish, false)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow));

        PageBuilderPaginationList = _fixture.CreateMany<PageBuilder>(20).ToList();
        PageBuilderList = _fixture.CreateMany<PageBuilder>(5).ToList();
        PageBuilderDto = _fixture.Create<PageBuilder>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public PageBuilder CreatePageBuilderWithProperties(
        string name = null,
        string type = null,
        bool isLock = false,
        bool isPublish = false,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<PageBuilder>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Name, name ?? PageBuilderName + "_" + _fixture.Create<string>())
            .With(x => x.Type, type ?? PageBuilderType)
            .With(x => x.IsLock, isLock)
            .With(x => x.IsPublish, isPublish)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public PageBuilder CreatePageBuilderWithSpecificName(string name)
    {
        return CreatePageBuilderWithProperties(name: name);
    }

    public PageBuilder CreateLockedPageBuilder()
    {
        return CreatePageBuilderWithProperties(isLock: true);
    }

    public PageBuilder CreatePublishedPageBuilder()
    {
        return CreatePageBuilderWithProperties(isPublish: true);
    }

    public List<PageBuilder> CreateMultiplePageBuildersWithSameName(string name, int count)
    {
        var pageBuilders = new List<PageBuilder>();
        for (int i = 0; i < count; i++)
        {
            pageBuilders.Add(CreatePageBuilderWithProperties(name: name));
        }
        return pageBuilders;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
