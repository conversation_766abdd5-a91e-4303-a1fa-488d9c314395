﻿using ContinuityPatrol.Application.Constants;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Persistence.Services;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace ContinuityPatrol.Persistence.UnitTests.Services
{
    public class PasswordAuthenticationServiceTests
    {
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly Mock<IUserLoginRepository> _mockUserLoginRepository;
        private readonly Mock<ICompanyRepository> _mockCompanyRepository;
        private readonly Mock<ILogger<BaseAuthenticationService>> _mockLogger;
        private readonly Mock<IAccessManagerRepository> _mockAccessManagerRepository;
        private readonly Mock<IUserCredentialRepository> _mockUserCredentialRepository;
        private readonly Mock<IUserInfraObjectRepository> _mockUserInfraObjectRepository;
        private readonly Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;
        private readonly Mock<IWebHostEnvironment> _mockWebHostEnvironment;
        private readonly Mock<IEmailService> _mockEmailService;
        private readonly Mock<ISmtpConfigurationRepository> _mockSmtpConfigurationRepository;
        private readonly Mock<IUserInfoRepository> _mockUserInfoRepository;
        private readonly Mock<IAlertRepository> _mockAlertRepository;
        private readonly Mock<IDynamicDashboardMapRepository> _mockDynamicDashboardMapRepository;
        private readonly Mock<IGlobalSettingRepository> _mockGlobalSettingRepository;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly PasswordAuthenticationService _service;

        public PasswordAuthenticationServiceTests()
        {
            _mockUserRepository = new Mock<IUserRepository>();
            _mockUserLoginRepository = new Mock<IUserLoginRepository>();
            _mockCompanyRepository = new Mock<ICompanyRepository>();
            _mockLogger = new Mock<ILogger<BaseAuthenticationService>>();
            _mockAccessManagerRepository = new Mock<IAccessManagerRepository>();
            _mockUserCredentialRepository = new Mock<IUserCredentialRepository>();
            _mockUserInfraObjectRepository = new Mock<IUserInfraObjectRepository>();
            _mockLicenseManagerRepository = new Mock<ILicenseManagerRepository>();
            _mockWebHostEnvironment = new Mock<IWebHostEnvironment>();
            _mockEmailService = new Mock<IEmailService>();
            _mockSmtpConfigurationRepository = new Mock<ISmtpConfigurationRepository>();
            _mockUserInfoRepository = new Mock<IUserInfoRepository>();
            _mockAlertRepository = new Mock<IAlertRepository>();
            _mockDynamicDashboardMapRepository = new Mock<IDynamicDashboardMapRepository>();
            _mockGlobalSettingRepository = new Mock<IGlobalSettingRepository>();
            _mockConfiguration = new Mock<IConfiguration>();

            _service = new PasswordAuthenticationService(
                _mockConfiguration.Object,
                _mockUserRepository.Object,
                _mockUserLoginRepository.Object,
                _mockCompanyRepository.Object,
                _mockLogger.Object,
                _mockAccessManagerRepository.Object,
                _mockUserCredentialRepository.Object,
                _mockUserInfraObjectRepository.Object,
                _mockLicenseManagerRepository.Object,
                _mockWebHostEnvironment.Object,
                _mockEmailService.Object,
                _mockSmtpConfigurationRepository.Object,
                _mockUserInfoRepository.Object,
                _mockAlertRepository.Object,
                _mockDynamicDashboardMapRepository.Object,
                _mockGlobalSettingRepository.Object
            );
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldReturnSuccessResponse_WhenValidCredentialsProvided()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                CompanyName = "Test Company",
                SessionTimeout = 30,
                InfraObjectAllFlag = true,
                TwoFactorAuthentication = "",
                IsReset = false,
                LoginType = "local",
                IsLock = false,
                IsDefaultDashboard = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0,
                LastPasswordChanged = DateTime.UtcNow.AddDays(-10)
            };

            var company = new Company
            {
                ReferenceId = "company1",
                IsParent = true
            };

            var accessManager = new AccessManager
            {
                RoleId = "Administrator",
                Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}"
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>(){new UserCredential() { UserId = user.ReferenceId }});

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(accessManager);

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(company);

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>
                {
                    new LicenseManager
                    {
                        ExpiryDate = DateTime.UtcNow.AddDays(30).ToString("dd MMMM yyyy"),
                        MacAddress = "00:00:00:00:00:00"
                    }
                });

            _mockLicenseManagerRepository.Setup(repo => repo.GetMacAddress())
                .ReturnsAsync(new List<string> { "00:00:00:00:00:00" });

            _mockWebHostEnvironment.Setup(env => env.EnvironmentName).Returns("Production");

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);
            Assert.Equal(user.LoginName, result.LoginName);
            Assert.Equal(user.CompanyId, result.CompanyId);
            Assert.Equal(user.CompanyName, result.CompanyName);
            Assert.Equal(user.Role, result.Role);
            Assert.Equal(user.RoleName, result.RoleName);
            Assert.Equal(user.ReferenceId, result.UserId);
            Assert.Equal(user.SessionTimeout, result.SessionTimeout);
            Assert.Equal(user.InfraObjectAllFlag, result.IsAllInfra);
            Assert.Equal(user.TwoFactorAuthentication, result.TwoFactorAuthentication);
            Assert.Equal(user.IsReset, result.IsReset);
            Assert.Equal(user.LoginType, result.AuthenticationType);
            Assert.True(result.IsLicenseValidity);
            Assert.True(result.LicenseEmpty);
            Assert.True(result.IsParent);
            Assert.Equal(company.ReferenceId, result.ParentCompanyId);
            Assert.Equal(user.IsDefaultDashboard, result.IsDefaultDashboard);
            Assert.Equal(string.Empty, result.Url);
            Assert.Equal(userLogin.LastPasswordChanged, result.LastPasswordChanged);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldThrowAuthenticationException_WhenUserNotFound()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "nonexistentuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1"
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync((User)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));

            Assert.Equal(Authentication.InvalidLogin, exception.Message);
            Assert.Equal((int)ErrorCode.InvalidAuthentication, exception.ErrorCode);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldThrowAuthenticationException_WhenInvalidPassword()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("wrongpassword"),
                CompanyId = "company1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            // Act & Assert
            await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldThrowAuthenticationException_WhenUserIsLocked()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "lockeduser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1"
            };

            var user = new User
            {
                LoginName = "lockeduser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                IsLock = true
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));

            Assert.Equal(string.Format(Authentication.UserAccountLocked, user.LoginName), exception.Message);
            Assert.Equal((int)ErrorCode.AccountLocked, exception.ErrorCode);
        }

        //[Fact]
        //public async Task AuthenticateAsync_ShouldVerifyCompany_WhenCompanyIdMismatch()
        //{
        //    // Arrange
        //    var request = new AuthenticationRequest
        //    {
        //        LoginName = "testuser",
        //        Password = SecurityHelper.Encrypt("password123"),
        //        CompanyId = "company2"
        //    };

        //    var user = new User
        //    {
        //        LoginName = "testuser",
        //        LoginPassword = SecurityHelper.Encrypt("password123"),
        //        CompanyId = "company1",
        //        ReferenceId = "user1",
        //        IsLock = false
        //    };

        //    _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
        //        .ReturnsAsync(user);

        //    _mockUserRepository.Setup(repo => repo.GetUserCompanyByUserIdAsync(user.ReferenceId))
        //        .ReturnsAsync(new List<UserCompany>
        //        {
        //            new UserCompany { CompanyId = "company2" }
        //        });

        //    var userLogin = new UserLogin
        //    {
        //        UserId = "user1",
        //        InvalidLoginAttempt = 0
        //    };

        //    _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
        //        .ReturnsAsync(userLogin);

        //    _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
        //        .ReturnsAsync(userLogin);

        //    _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
        //        .ReturnsAsync(new UserCredential { UserId = user.ReferenceId });

        //    _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
        //        .ReturnsAsync(new UserCredential());

        //    var accessManager = new AccessManager
        //    {
        //        RoleId = "Administrator",
        //        Properties = "[]"
        //    };

        //    _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
        //        .ReturnsAsync(accessManager);

        //    var company = new Company
        //    {
        //        ReferenceId = "company2",
        //        IsParent = false
        //    };

        //    _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(request.CompanyId))
        //        .ReturnsAsync(company);

        //    _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
        //        .ReturnsAsync(new UserInfraObject { Properties = "" });

        //    _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(request.CompanyId))
        //        .ReturnsAsync(new List<LicenseManager>());

        //    // Act
        //    var result = await _service.AuthenticateAsync(request);

        //    // Assert
        //    Assert.NotNull(result);
        //    Assert.True(result.IsAuthorized);
        //    Assert.Equal(user.LoginName, result.LoginName);
        //    Assert.Equal(request.CompanyId, result.CompanyId); // Should use the requested company ID
        //}

        [Fact]
        public async Task AuthenticateAsync_ShouldThrowAuthenticationException_WhenInvalidRole()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "InvalidRole",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync((AccessManager)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));

            Assert.Equal(Authentication.InvalidAccess, exception.Message);
            Assert.Equal((int)ErrorCode.InvalidAuthentication, exception.ErrorCode);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldSetDefaultTimeout_WhenUserTimeoutIsZero()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                CompanyName = "Test Company",
                SessionTimeout = 0, // Zero timeout
                InfraObjectAllFlag = true,
                TwoFactorAuthentication = "",
                IsReset = false,
                LoginType = "local",
                IsLock = false,
                IsDefaultDashboard = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            var company = new Company
            {
                ReferenceId = "company1",
                IsParent = true
            };

            var accessManager = new AccessManager
            {
                RoleId = "Administrator",
                Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}"
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(accessManager);

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(company);

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(PasswordAuthenticationService.DefaultTimeout, result.SessionTimeout);
        }
    }
}
