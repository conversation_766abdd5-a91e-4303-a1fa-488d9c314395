﻿using ContinuityPatrol.Application.Features.TableAccess.Event.Delete;

namespace ContinuityPatrol.Application.Features.TableAccess.Commands.Delete;

public class DeleteTableAccessCommandHandler : IRequestHandler<DeleteTableAccessCommand, DeleteTableAccessResponse>
{
    private readonly IPublisher _publisher;
    private readonly ITableAccessRepository _tableAccessRepository;

    public DeleteTableAccessCommandHandler(ITableAccessRepository tableAccessRepository, IPublisher publisher)
    {
        _tableAccessRepository = tableAccessRepository;
        _publisher = publisher;
    }

    public async Task<DeleteTableAccessResponse> Handle(DeleteTableAccessCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _tableAccessRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.TableAccess),
            new NotFoundException(nameof(TableAccess), request.Id));

        eventToDelete.IsActive = false;

        await _tableAccessRepository.UpdateAsync(eventToDelete);

        var response = new DeleteTableAccessResponse
        {
            Message = Message.Delete("Table Access", eventToDelete.TableName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new TableAccessDeletedEvent { TableName = eventToDelete.TableName },
            cancellationToken);

        return response;
    }
}