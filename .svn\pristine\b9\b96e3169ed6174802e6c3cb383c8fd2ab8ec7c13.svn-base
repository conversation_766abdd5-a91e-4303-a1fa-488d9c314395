﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.LoadBalancerModel.LoadBalancerViewModel
@Html.AntiForgeryToken()

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title">
                <i class="cp-load-balancer" id="changeIcon"></i><span id="loadTitle">
                    Load Balancer
                </span>
            </h6>
            <form class="d-flex align-items-center">

               @*  <button class="btn btn-primary btn-sm me-2" type="button" id="hide" title="Load Balancer">
                    <i class="cp-table"></i>
                </button> *@
                <button class="btn btn-primary btn-sm me-2" type="button" id="show" state="true" title="State Monitoring">
                    <i class="cp-circle-workflow"></i>
                </button>
                <div class="input-group me-2 w-auto" id="search-container">
                    <input type="search" id="nodeSearch" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="cp-filter" title="Filter"></i>
                            </span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li>
                                    <h6 class="dropdown-header">Filter Search</h6>
                                </li>
                                <li class="dropdown-item">

                                    <div>
                                        <input class="form-check-input" type="checkbox" value="configurationtype="
                                               id="configuration_type">
                                        <label class="form-check-label" for="configuration_type">
                                            Configuration Type
                                        </label>
                                    </div>
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name="
                                               id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="ipaddress="
                                               id="ip_address">
                                        <label class="form-check-label" for="ip_address">
                                            IP Address
                                        </label>
                                    </div>
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="connectiontype="
                                               id="connection_type">
                                        <label class="form-check-label" for="connection_type">
                                            Connection Type
                                        </label>
                                    </div>
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="servicetype="
                                               id="service_type">
                                        <label class="form-check-label" for="service_type">
                                            Service Type
                                        </label>
                                    </div>
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="hostname="
                                               id="host_name">
                                        <label class="form-check-label" for="host_name">
                                            Host Name
                                        </label>
                                    </div>
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="port="
                                               id="ports">
                                        <label class="form-check-label" for="ports">
                                            Port
                                        </label>
                                    </div>

                                </li>

                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" id="create" class="btn btn-primary btn-sm createBtn">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>

        </div>
        <div class="card-body pt-0">
            <div class="desc" id="table">
                <table id="loadBalancerTable" class="table table-hover dataTable" style="width:100%">
                    <thead>
                        <tr>
                            <th class="SrNo_th">Sr. No.</th>
                            <th>Name</th>
                            <th>Configuration Type</th>                        
                            <th>IP Address</th>
                            <th>Connection Type</th>
                            <th>Service Type</th>
                            <th>Host Name</th>
                            <th>Port</th>
                            <th>Health State</th>
                            <th>Node State</th>
                            <th class="Action-th">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>

            </div>
            <div class="desc chart" id="chart">

             @*    <div class="card-body p-0 d-none" align="center" id="parentLoader">
                    <div id="LBLoader" class="spinner-border text-primary position-absolute" style="width: 3rem; height: 3rem; top:45%;left:47%;" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>

                </div> *@

                @* <div id="LoadBalanceChart" style="width:100%; height: calc(100vh - 157px);max-width: 100%;"> </div> *@



                <div id="container" class="position-relative overflow-hidden" style="height:500px">
                    <div id="svg-content" class="position-absolute w-100">
                        <div id="my-circle">
                            <div class="card-body p-0 d-none" align="center" id="parentLoader">
                                <div id="LBLoader" class="spinner-border text-primary position-absolute" style="width: 3rem; height: 3rem; top:45%;left:47%;" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>

                            </div>

                            <div id="LoadBalanceChart" style="width:100%; height: calc(100vh - 157px);max-width: 100%;"> </div>
                        </div>
                    </div>
                </div>
               @*  <button onclick="zoomOut()"> - </button>
                <button onclick="reset()"> Reset </button>
                <button onclick="zoomIn()"> + </button> *@
            </div>
        </div>
    </div>
    <div id="adminCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.CreateAndEdit" aria-hidden="true"></div>
    <div id="adminDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.Delete" aria-hidden="true"></div>
    <!--Modal Create-->
    <div class="modal fade" id="nodeCreateModal" data-bs-backdrop="static" aria-labelledby="configureModalLabel" aria-hidden="true">
        <partial name="Configuration" />
    </div>
    <!--Modal Delete-->
    <div class="modal fade" id="nodeDeleteModal" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <partial name="Delete" />
    </div>
    @* Duplicate Actions *@
    <div id="nodeDuplicateActionsModal" class="modal fade" data-bs-backdrop="static" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <img class="w-100" src="~/img/isomatric/confirmation.svg" alt="Duplicate Actions" />
                </div>
                <div class="modal-body text-center pt-5">
                    <h5 class="fw-bold">Confirmation</h5>
                    <h6 class="fw-bold">Are you sure?</h6>Do you want <span class="font-weight-bolder text-primary" id="statusPreviousData"></span> into <span class="font-weight-bolder text-primary" id="statusData"></span> of your Load balancer Node status?
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="duplicateConfirmation">Yes</button>
                </div>

            </div>
        </div>
    </div>
    <!--Modal Error-->

    <div class="modal fade" id="nodeErrorModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-md modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-error-message"></i><span>Error Message</span></h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="list-group list-group-flush Profile-Select">
                        <div class="d-grid">

                            <span id="loadException">

                            </span>
                            <div class="text-center d-none" id="loadExceptionNA">
                                <span>
                                    <img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img" style="padding:10px">
                                </span>
                            </div>

                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>

</div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/js/Admin/LoadBalancer/LoadBalancer.js"></script>
@* <script src="~/js/nodeConfiguration.js"></script> *@
<script src="~/js/Admin/LoadBalancer/nodeConfigurationFunctions.js"></script>
<script src="~/js/Admin/LoadBalancer/nodeConfiguration.js"></script>
<script src="~/lib/zoom/zoom.js"></script>