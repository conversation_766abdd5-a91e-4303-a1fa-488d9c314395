﻿$('#btnExecutionRefresh').on('click', WFExeEventDebounce(async function () {
    currentActiveContainerId = $('.runningWorkflowContainer.Active-Card').attr('operationgroupid')
    if ($('#LoadRunningProfile').children().length) {
        $(this).css('opacity', '0.7')
       // loadExistingProfiles();
      //  await loadUsersData().then((res) => {
            let userId = $('#UserList').val();
            if (userId == 'all' || !userId) {
                await loadExistingProfiles();
                await loadUsersData()
            } else {
                let data = {
                    userId: userId
                }
                await loadExistingProfiles(data, 'user')
                await loadUsersData().then((res) => {
                    //setTimeout(() => {
                        $('#UserList').val(userId).trigger('change')
                   // }, 200)
                })  
            }  
      //  })
    }
    setTimeout(() => {
        $(this).css('opacity', '1')
    }, 800)
}, 1000))

$('#btnRunningProfiles').on('click', function () {
    if (!$('#executionDropDownMenu').is(':visible')) {
        if (ProfileStatus.running.length) {
            let html = '';
            ProfileStatus.running.forEach((d, i) => {
                let profileName = $(`#${d} .profileNameClass`).text()
                html += `<li role='button' class='fs-8 list-group-item runningProfileList' profileId='${d}'><div class='d-flex flex-row gap-2'><span class='me-1'>${i + 1}.</span><div style="word-wrap: break-word;width: 90%;" id='${getRandomId('list')}' >${profileName}</div></div></li>`
            })
            $('#executionDropDownMenu').empty().append(html)
        }
    }
    $(this).dropdown('toggle')
})

$(document).on('click', '.runningProfileList', function () {
    let getProfileId = $(this).attr('profileId');
    $(`#${getProfileId}`)[0].scrollIntoView();
    $('#btnRunningProfiles').dropdown('toggle')
})

const getActionId = () => {
    let actionIds = '';
    if ($("#timeline_view .cp-Parallel")?.parent()?.find('.cp-error') && $("#timeline_view .cp-Parallel")?.parent()?.find('.cp-error').length) {
        $("#timeline_view .cp-Parallel")?.parent()?.find('.cp-error').each((idx, obj) => {
            let getActionId = $(obj).attr('data-actionid')
            actionIds += getActionId + ',';
        })
    } 
    //else if (!actionIds && $("#timeline_view")?.find('.cp-error').length) {
    //    let getRunningId = $("#timeline_view")?.find('.cp-error').last().attr('data-actionid')
    //    actionIds += getRunningId + ','
    //} else if (!actionIds && $("#timeline_view")?.find('.cp-success').length) {
    //    let getRunningId = $("#timeline_view")?.find('.cp-success').last().attr('data-actionid')
    //    actionIds += getRunningId + ','
    //}
    //else if (!actionIds && $("#timeline_view")?.find('.cp-reload').length) {
    //    let getRunningId = $("#timeline_view")?.find('.cp-reload').last().attr('data-actionid')
    //    actionIds += getRunningId + ','
    //} else if (!actionIds && $("#timeline_view")?.find('.cp-skipped').length) {
    //    let getRunningId = $("#timeline_view")?.find('.cp-skipped').last().attr('data-actionid')
    //    actionIds += getRunningId + ','
    //}
    if (actionIds) actionIds = actionIds.slice(0, actionIds.length - 1)

    return actionIds;
}

$(document).on('click', '.btnUpdateOperations', async function () {
    let actionId = getActionId();
    let workflowId = $(this).parents('.runningWorkflowContainer').attr('id')
    let operationGroupId = $(`#${workflowId}`).attr('operationGroupId')
    let conditionalOperation = $(this).hasClass('btnNext') ? 1 : $(this).hasClass('btnRetry') ? 2 : $(this).hasClass('btnReload') ? 3 :
        $(this).hasClass('aborted') ? 4 : $(this).hasClass('btnPauseResume') ? 5 : 0

    let getPauseOrResume = $(this).hasClass('btnPauseResume') ? $(this).find('span').text().toLowerCase() === 'pause' ? 'pause' : 'resume' : '';

    let data = {
        groupId: operationGroupId,
        conditionalOperation: conditionalOperation,
        actionIds: actionId,
        pauseOrResume: getPauseOrResume,
        __RequestVerificationToken: gettoken()
    }  
    if ($(`#${workflowId}`).attr('isCustom') == 'true' && conditionalOperation == 3) {
        await getWorkflowById(workflowId)
    } else {
        await updateConditionOperation(data)
       
    }
    $(`#${workflowId} .btnUpdateOperations`).prop('disabled', false).css("color", "var(--bs-secondary)");
   
})

$(document).on('click', '.btnAbortOperation', function () {
    let getWorkflowId = $(this).parents('.runningWorkflowContainer').attr('id')
    let WorkflowName = $(`#${getWorkflowId} .workflowTextContainer`).text();
    let getOperationGroupId = $(`#${getWorkflowId}`).attr('operationgroupid')
    $('#confirmAbortButton').attr('opertationGroupId', getOperationGroupId)
    $('#abortingWorkflowName').text(WorkflowName)
    $('#AbortModal').modal('show')
})

$('#confirmAbortButton').on('click', async function () {
    let getOperationGroupId = $(this).attr('opertationgroupid')
    let actionIds = '';

    if ($("#timeline_view .cp-Parallel")?.parent()?.find('.cp-error') && $("#timeline_view .cp-Parallel")?.parent()?.find('.cp-error').length) {
        $("#timeline_view .cp-Parallel")?.parent()?.find('.cp-error').each((idx, obj) => {
            let getActionId = $(obj).attr('data-actionid')
            actionIds += getActionId + ','
        })
        $("#timeline_view .cp-Parallel")?.parent()?.find('.cp-reload').each((idx, obj) => {
            let getActionId = $(obj).attr('data-actionid')
            actionIds += getActionId + ','
        })
    }
    //else if (!actionIds && $("#timeline_view")?.find('.cp-success').length) {
    //    let getRunningId = $("#timeline_view")?.find('.cp-success').last().attr('data-actionid')
    //    actionIds += getRunningId + ','
    //}
    //else if (!actionIds && $("#timeline_view")?.find('.cp-reload').length) {
    //    let getRunningId = $("#timeline_view")?.find('.cp-reload').last().attr('data-actionid')
    //    actionIds += getRunningId + ','
    //} else if (!actionIds && $("#timeline_view")?.find('.cp-skipped').length) {
    //    let getRunningId = $("#timeline_view")?.find('.cp-skipped').last().attr('data-actionid')
    //    actionIds += getRunningId + ','
    //} else if (!actionIds) {
    //    let getRunningId = $("#timeline_view")?.find('.cp-pending').last().attr('data-actionid')
    //    actionIds += getRunningId + ','

    //}

    if (actionIds) actionIds = actionIds.slice(0, actionIds.length - 1)

    let data = {
        groupId: getOperationGroupId,
        conditionalOperation: 4,
        actionIds: actionIds,
        pauseOrResume: '',
        __RequestVerificationToken: gettoken()
    }

    await updateConditionOperation(data)
    setTimeout(() => {
        let workflowId = $(`.runningWorkflowContainer[operationGroupId=${data.groupId}]`).attr('id')
        $(`#${workflowId} .waitActionModal, #${workflowId} .parallelErrorModal, #${workflowId} .snapActionModal`).addClass('d-none');      
        $('#AbortModal').modal('hide');
        loadExistingProfiles();
    }, 200)
})

$(document).on('click', '.parallelErrorModal', async function (e) {
    e.stopPropagation();
    $('#failedParallelLoader').removeClass('d-none');
    $('#failedModal').modal('show');

    let workflowId = $(this).parents('.runningWorkflowContainer').attr('id')
    let WorkflowName = $(`#${workflowId} .workflowTextContainer`).text();
    let operationGroupId = $(`#${workflowId}`).attr('operationGroupId')

    await updateConditionForParallelError(operationGroupId, WorkflowName, workflowId)
})

const updateConditionForParallelError = async (operationGroupId, workflowName, workflowId) => {

    await $.ajax({
        type: "get",
        url: RootUrl + executionMethods.getActionResultByOperationGroupId,
        data: { groupId: operationGroupId },
        dataType: 'text',
        success: function (response) {
            if (response) {
                let responseData = JSON.parse(response)
                let errorData = responseData.filter(data => data?.status?.toLowerCase() === 'error')
                
                if (errorData.length) {
                    let tableData = ''
                    for (let i = 0; i < errorData.length; i++) {
                        if (errorData[i]?.status?.toLowerCase() === 'error') {
                            tableData += `<tr class='parallelErrorContainer' id="${errorData[i]?.id}" operationGroupId='${errorData[i]?.workflowOperationGroupId}' workflowId='${workflowId}'>
                                          <td>${i + 1}</td>
                                          <td><span title="${errorData[i]?.workflowActionName}" class='text-truncate' style='max-width: 130px; display: inline-block'>${errorData[i]?.workflowActionName}</span></td>
                                          <td><span title="${workflowName}" class='text-truncate' style='max-width:130px;display:inline-block'>${workflowName}</span></td>
                                          <td><span title='${errorData[i]?.message || 'NA'}' class='text-truncate' style='max-width:130px;display:inline-block'>${errorData[i]?.message || 'NA'}</span></td>
                                          <td><button type='button' class='btn btn btn-outline-primary border-0 btnNext btnParellelErrorUpdateOperations'><i class='cp-circle-playnext fs-6 me-2'></i><span class='align-middle'>Next</span></button>
                                          <button type='button' class='btn btn btn-outline-primary border-0 btnRetry btnParellelErrorUpdateOperations' ><i class='cp-retry me-2'></i><span class='align-middle'>Retry</span></button>
                                          <button type='button' class='btn btn btn-outline-primary border-0 btnReload btnParellelErrorUpdateOperations d-none' ><i class='cp-reload fs-6 me-2'></i><span class='align-middle'>Reload</span></button>
                                          </td></tr>`
                        }
                    }
                    $('#failedActionResult').empty().append(tableData);
                    $('#btnReloadParellelErrorUpdateOperations').attr('operationGroupId', operationGroupId)
                } else {
                    $('#failedModal').modal('hide');
                }
            }
            $('#failedParallelLoader').addClass('d-none');
        }
    });
}

$('#failedActionResult').on('click', '.btnParellelErrorUpdateOperations', async function (e) {
    e.stopPropagation();
    let actionId = $(this).parents('.parallelErrorContainer').attr('id')
    let operationGroupId = $(`#${actionId}`).attr('operationGroupId')

    let operationCondition = $(this).hasClass('btnNext') ? 1 : $(this).hasClass('btnRetry') ? 2 : $(this).hasClass('btnReload') ? 3 : 0;

    let data = {
        groupId: operationGroupId,
        conditionalOperation: operationCondition,
        actionIds: actionId,
        pauseOrResume: '',
        __RequestVerificationToken: gettoken()
    }
    await updateConditionOperation(data)

    $(`#${actionId}`).remove();

    if (!$(`#failedActionResult`).children().length) {
        $('#failedModal').modal('hide');
        $(`.runningWorkflowContainer[operationGroupId=${operationGroupId}]`).find('parallelErrorModal').addClass('d-none')
    }
})

$('#btnReloadParellelErrorUpdateOperations').on('click', async function () {
    let actionIds = '';
    let operationGroupId = $(this).attr('operationGroupId');
    if ($("#timeline_view .cp-Parallel")?.find('.cp-error') && $("#timeline_view .cp-Parallel")?.find('.cp-error').length) {
        $("#timeline_view .cp-Parallel")?.find('.cp-error').each((idx, obj) => {
            let getActionId = $(obj).attr('data-actionid')
            actionIds += getActionId + ','
        })
       
    }
    if (actionIds) actionIds = actionIds.slice(0, actionIds.length - 1)

    let data = {
        groupId: operationGroupId,
        conditionalOperation: 3,
        actionIds: actionIds,
        pauseOrResume: '',
        __RequestVerificationToken: gettoken()
    }
    await updateConditionOperation(data)

    $(this).prop('disabled', true)
    
})

//wait action modal
$(document).on('click', '.waitActionModal', async function (e) {
    let workflowId = $(this).parents('.runningWorkflowContainer').attr('id')
    let currentActionName = $(this).attr('currentActionName')
    let currentActionId = $(this).attr('currentActionId')
    let WorkflowName = $(`#${workflowId} .workflowTextContainer`).text();
    let operationGroupId = $(`#${workflowId}`).attr('operationGroupId')
    let errorMessage = atob($(this).attr('errorMessage'))
    await updateConditionForWaitAction(workflowId, currentActionId, currentActionName, operationGroupId, errorMessage)
})

const updateConditionForWaitAction = async (workflowId, currentActionId, currentActionName, operationGroupId, errorMessage) => {
    let data = { WorkflowId: workflowId, ActionName: currentActionName, __RequestVerificationToken: gettoken() }

    await $.ajax({
        type: "get",
        url: RootUrl + executionMethods.getActionDetails,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (response) {
            if (response?.success) {
                if (response?.data) {
                    let getProperties = response?.data?.properties
                    getProperties = getProperties && JSON.parse(getProperties)
                    if (getProperties) {
                        html = `<tr class="waitActiontableData" id='${currentActionId}' operationGroupId='${operationGroupId}' workflowId='${workflowId}'>
                            <td class="text-truncate"><span title='${response?.data?.actionName || 'NA'}' >${response?.data?.actionName || 'NA'}</span></td>
                            <td style="word-wrap: break-word">
                             <span title="${getProperties['@@Reason_for_wait'] || 'NA'}" >${getProperties['@@Reason_for_wait'] || 'NA'}</span>
                            </td>
                            <td class="text-truncate"><span title='${response?.data?.workflowName || 'NA'}'>${response?.data?.workflowName || 'NA'}</span></td>                            
                             ${getProperties?.hasOwnProperty('@@Required_Authentication') && getProperties['@@Required_Authentication'] ?
                                `<td><input class="form-control border border-secondary-subtle waitActionInput" type="password" id='waitActionPassword' />
                             <span id='waitActionPassword-error' style='width: 12.5% !important'></span>
                           </td>` : ''}

                            ${getProperties?.hasOwnProperty('@@Accept_Note') && getProperties['@@Accept_Note'] ?
                                `<td>
                            <input class="form-control border border-secondary-subtle waitActionInput" id='waitActionActionNote' type="text" placeholder="Enter Action Note" />
                            <span id='waitActionActionNote-error' style='width: 12.5% !important'></span>
                            </td>` : ''}

                            <td><button class="btn btn-sm btn-primary" id="btnWaitActionSubmit" acceptMessage='${getProperties['@@AcceptNote']}'>OK</button></td>
                        </tr>`
                        $('#waitActionBody').empty().append(html);
                        if (getProperties?.hasOwnProperty('@@Required_Authentication') && !getProperties['@@Required_Authentication']) {
                            $('#waitPasswordHead').hide()
                        } else {
                            $('#waitPasswordHead').show()
                        }

                        if (getProperties?.hasOwnProperty('@@Accept_Note') && !getProperties['@@Accept_Note']) {
                            $('#waitAcceptHead').hide()
                        } else {
                            $('#waitAcceptHead').show()
                        }
                    }
                }
                $('#waitModal').modal('show');
            } else {
                errorNotification(response)
            }
        }
    });
}

$(document).on('click', '.waitActionInput', function (e) {
    let value = e?.target?.value
    let id = e?.target?.getAttribute('id')

    if (value) {
        $(`#${id}-error`).text('').removeClass('field-validation-error')
    }
})

$(document).on('click', '#btnWaitActionSubmit', async function (e) {
    e.stopPropagation();
    let operationGroupId = $(this).parents('.waitActiontableData').attr('operationgroupid')
    let workflowId = $(this).parents('.waitActiontableData').attr('workflowid')
    let waitActionAcceptMessage = $(this).attr('acceptMessage')
    let checkFormValid = await checkWaitValidation(waitActionAcceptMessage)

    if (checkFormValid) {
        let data = {
            groupId: operationGroupId,
            conditionalOperation: 6,
            actionIds: '',
            pauseOrResume: '',
            __RequestVerificationToken: gettoken()
        }
        await updateConditionOperation(data)

        setTimeout(() => {
            $(`#${workflowId}`).find('.waitActionModal').addClass('d-none')
            $('#waitModal').modal('hide');
        }, 500)

    }
})

let checkWaitValidation = async (acceptMessage) => {

    let waitPassword = $(`#waitActionPassword`).val();
    let actionNote = $(`#waitActionActionNote`).val();
    let formIsValid = true

    if (actionNote == '' && $(`#waitActionActionNote`).parent().is(':visible')) {
        $(`#waitActionActionNote-error`).text('Enter action note').addClass('field-validation-error')
        formIsValid = false
    } else if (acceptMessage && $(`#waitActionActionNote`).parent().is(':visible')) {
        if (actionNote?.replace(/\s+/g, '')?.toLowerCase() !== acceptMessage?.replace(/\s+/g, '')?.toLowerCase()) {
            $(`#waitActionActionNote-error`).text('Action note mismatched').addClass('field-validation-error')
            formIsValid = false
        } else {
            $(`#waitActionActionNote-error`).text('').removeClass('field-validation-error')
            formIsValid = true
        }
    }

    if (waitPassword == '' && $(`#waitActionPassword`).parent().is(':visible')) {
        $(`#waitActionPassword-error`).text('Enter password').addClass('field-validation-error')
        formIsValid = false
    } else if ($(`#waitActionPassword`).parent().is(':visible')) {
        let encryptedPassword = await EncryptPassword(waitPassword);
        let profileId = $('.runningWorkflowContainer.Active-Card')?.parents('.profileContainer').attr('id')
        let data = {
            'profileId': profileId,
            'password': encryptedPassword,
            'description': '',
            __RequestVerificationToken: gettoken()
        }
        let result = await GetAsync(RootUrl + executionMethods.verifyProfile, data, OnError);
        if (!result) {
            $(`#waitActionPassword`).val('')
            $(`#waitActionPassword-error`).text('Please enter valid password').addClass('field-validation-error')
            formIsValid = false;
        }
    }
    return formIsValid;
}

const updateConditionOperation = async (data) => {
   
    await $.ajax({
        type: "POST",
        url: RootUrl + executionMethods.updateConditions,
        data: data,
        dataType: 'text',
        success: function (response) {
            let result = JSON.parse(response)
            return result;
            if (!result.success) {
                setTimeout(() => {
                    let workflowId = $(`.runningWorkflowContainer[operationGroupId=${data.groupId}]`).attr('id')
                    $(`#${workflowId} .btnUpdateOperations`).prop('disabled', false).css("color", "var(--bs-primary)");
                },3000)          
                errorNotification(result)
            }
        }
    });
}


/*snap action modal*/

$(document).on('click', '.snapActionModal', function (e) {
    e.stopPropagation();
    let workflowId = $(this).parents('.runningWorkflowContainer').attr('id')
    let currentActionName = $(this).attr('currentactionname')
    let profileId = $(`#${workflowId}`).parents('.profileContainer').attr('id')
    let operationId = $(`#${workflowId}`).attr('operationId')

    $('#btnSnapSubmit').attr('workflowId', workflowId).attr('profileId', profileId).attr('operationId', operationId).attr('workflowId', workflowId)
    updateConditionForSnapAction(workflowId, currentActionName)
})

const updateConditionForSnapAction = async (workflowId, currentActionName) => {
    await $.ajax({
        type: "get",
        url: RootUrl + 'ITAutomation/WorkflowExecution/GetSnapList',
        data: { storageGroupName: '', linkedStatus: '' },
        dataType: "json",
        traditional: true,
        success: async function (response) {
            if (response?.success) {
                if (response?.data && response?.data?.length && Array.isArray(response?.data)) {
                    await getSnapActionDetails(workflowId, currentActionName, response?.data)
                };
            } else {
                errorNotification(response)
            }
        }
    });
}

const getSnapActionDetails = async (workflowId, actionName, snapList) => {

    await $.ajax({
        type: "get",
        url: RootUrl + executionMethods.getActionDetails,
        data: { WorkflowId: workflowId, ActionName: actionName },
        dataType: "json",
        traditional: true,
        success: function (response) {
            let properties = JSON.parse(response.data.properties)
            let splitData = [];
            let filteredSnap = [];
            let html = '';
            if (properties['@@StorageGroupName'].includes(',')) {
                splitData = properties['@@StorageGroupName'].split(',')
                let filteredSnapGroup = snapList.filter((d) => d.storageGroupName === splitData[0])
                if (splitData[1] && splitData[1].length) {
                    filteredSnap = filteredSnapGroup.filter((d) => d.linkedStatus.toLowerCase() === splitData[1]?.toLowerCase())
                } else {
                    filteredSnap = filteredSnapGroup
                }
            } else {
                filteredSnap = snapList.filter((d) => d.storageGroupName === properties['@@StorageGroupName'])
            }

            let snapData = filteredSnap.reverse()

            for (let i = 0; i < snapData?.length; i++) {
                const timeStamp = snapData[i]?.timeStamp && snapData[i]?.timeStamp?.split('.')
                const iconClass = snapData[i]?.linkedStatus?.toLowerCase() === "linked" ? "text-bg-success badge" : snapData[i]?.linkedStatus?.toLowerCase() === "unlinked" ? "text-bg-danger badge" : "";

                let linkedStatus = snapData[i]?.linkedStatus || '-';
                linkedStatus = linkedStatus == 'NA' ? '-' : linkedStatus

                html += `<tr><td class="text-truncate"><div>
                            <input class="form-check-input snapCheckedData" type="radio" value=${snapData[i]?.id} name='snap' gen=${snapData[i]?.gen} timestamp='${snapData[i]?.timeStamp}' snapName=${snapData[i]?.name}>
                                <label class="form-check-label">${snapData[i]?.name || 'NA'}</label></div></td>  
                            <td class="text-truncate linked_status">${snapData[i]?.gen || 'NA'} </td> 
                            <td class="text-truncate">${snapData[i]?.storageGroupName || 'NA'}</td>                               
                            <td class="text-truncate">${timeStamp && timeStamp?.length ? timeStamp[0] : '-'}</td> 
                            <td class="text-truncate"><span class="align-middle ${iconClass} ms-1" > ${linkedStatus}</span></td></tr>`
            }
            $('#snapActionBody').empty().append(html)
            $('#snapModal').modal('show');
        }
    })
}

$('#filter_TimeStamp').on("change", function () {
    var filter = $(this).val();
    var altered = new Date(filter).toString().split(' ')
    let categoryFlagStatus = true;
    $('#snapActionBody' + " tr").each(function () {
        let $columns = $(this).find("td");
        if ($columns.length > 0) {
            let timeStampText = $columns.eq(dateColumnIndex).text().trim();

            if (timeStampText.includes(altered[0]) && timeStampText.includes(altered[1]) && timeStampText.includes(altered[2]) && timeStampText.includes(altered[3])) {
                $(this).show();
                categoryFlagStatus = false;
            } else {
                $(this).hide();
            }
        }
    });

    if (categoryFlagStatus) {
        $(tableSelector).append('<tr class="no-records"><td colspan="' + numColumns + '" style="text-align: center;">No matching records found</td></tr>');
    } else {
        $(".no-records").remove();
    }
});

$(`#snapPassword`).on('change', function (e) {
    let value = e.target.value;
    if (value) {
        $(`#snapPasswordError`).text('').removeClass('field-validation-error')
    } else {
        $(`#snapPasswordError`).text('Enter password').addClass('field-validation-error')
    }
})

$('#btnSnapSubmit').on('click', WFExeEventDebounce(async function () {
    let password = $(`#snapPassword`)?.val();
    let profileId = $(this).attr('profileId');
    let operationId = $(this).attr('operationId')
    let workflowId = $(this).attr('workflowId')

    if (password) {
        let snapData = $('#snapActionBody .snapCheckedData:checked');
        let obj = {};
        for (let i = 0; i < snapData?.length; i++) {
            if (snapData[i]?.checked) {
                obj.id = snapData[i]?.value
                obj.name = snapData[i]?.getAttribute('snapname')
                obj.gen = snapData[i].getAttribute('gen')
                obj.timestamp = snapData[i].getAttribute('timestamp')
            }
        }
        if (obj && Object.keys(obj).length) {
            let encryptedEassword = await EncryptPassword(pwd);
            let data = {
                'profileId': profileId,
                'password': encryptedEassword,
                'description': ''
            }

            let result = await GetAsync(RootUrl + executionMethods.verifyProfile, data);

            if (result) {
                let operationData = {
                    'groupId': operationId,
                    'conditionalOperation': 8,
                    'isParallel': false,
                    'operationId': '',
                    'pauseOrResume': '',
                    'snapProperties': JSON.stringify(obj),
                    '__RequestVerificationToken': gettoken()
                }

                await updateConditionOperation(operationData)
 
                setTimeout(() => {
                    $(`#${workflowId}`).find('.snapActionModal').addClass('d-none')
                    $('#snapModal').modal('hide');
                }, 500)
            } else {
                $(`#snapPassword`).val('')
                $(`#snapPasswordError`).text('Enter valid password').addClass('field-validation-error')
            }
        } else {
            notificationAlert("warning", 'Atleast one snap reqired')
        }
    } else {
        $(`#snapPasswordError`).text('Enter password').addClass('field-validation-error')
    }
}, 700))

//message modal

const loadMessageDetails = async (workflowId, workflowActionName, message) => {
    let operationId = $(`#${workflowId}`).attr('operationId')
    $('#btnMessageSubmit').attr('operationId', operationId)

    await $.ajax({
        type: "get",
        url: RootUrl + executionMethods.getActionDetails,
        data: { WorkflowId: workflowId, ActionName: workflowActionName },
        dataType: "json",
        traditional: true,
        success: function (response) {
            if (response?.success) {
                if (response?.data) {
                    let getProperties = response?.data?.properties
                    getProperties = getProperties && JSON.parse(getProperties)
                    if (getProperties && getProperties['@@Header']) {
                        $('#messageHeader').text(getProperties['@@Header'])
                        $('#messageBody').text(message);
                        $('#messageModal').modal('show');
                    }
                }
            } else {
                errorNotification(response)
            }
        }
    });
}

$('#btnMessageSubmit').on('click', WFExeEventDebounce(async function () {
    let operationId = $(this).attr('operationId')

    let data = {
        groupId: operationId,
        conditionalOperation: 9,
        isParallel: false,
        operationId: '',
        pauseOrResume: '',
        __RequestVerificationToken: gettoken()
    }

    await updateConditionOperation(data);

    setTimeout(() => {
        $('#messageModal').modal('hide');
    },500) 
}, 700))

// Global Variable

let getWorkflowDetails = async (event) => {

    let actionName = '';
    let workflowId = $('.timelineAndLog.Active-Card')?.attr('workflowid')
    let waitData = event.target.tagName == 'I' ? event?.target?.parentElement?.getAttribute('timeline-data') : event?.target?.getAttribute('timeline-data')

    if (waitData) {
        waitData = JSON.parse(waitData)
        actionName = waitData?.workflowActionName
    } else {
        actionName = event?.srcElement?.parentElement?.getAttribute('actionname')
    }
    let operationGroupId = $('.timelineAndLog.Active-Card')?.attr('id')
    await $.ajax({
        type: "get",
        url: RootUrl + 'ITAutomation/WorkflowExecution/GetWaitActionDetails',
        data: { WorkflowId: workflowId, ActionName: actionName },
        dataType: "json",
        traditional: true,

        success: function (response) {
            if (response?.success) {
                if (response?.data) {
                    let getProperties = response?.data?.properties
                    getProperties = getProperties && JSON.parse(getProperties)
                    getGlobalVariable(getProperties['@@VariableName'], operationGroupId)
                    globalvariable = getProperties['@@ResultVariable']

                }
            }
        }
    })
}

const getGlobalVariable = async (variableName, operationGroupId, type = '') => {

    let data = {
        variableName: variableName
    }

    return new Promise((resolve, reject) => {
        $.ajax({
            type: "GET",
            url: RootUrl + executionMethods?.getvariableByName,
            data: data,
            dataType: 'json',
            success: function (response) {
                if (response?.success) {
                    if (response?.data && response?.data?.length) {
                        if (type !== 'check') {
                            let property = JSON.parse(response?.data[0]?.variableValue)
                            globalVariableType = response?.data[0]?.type
                            let tableData = ''
                            for (let i = 0; i < property?.length; i++) {
                                tableData += `<tr>
                                            <td><input type='checkbox' class="globalVariableCheckbox form-check" details='${JSON.stringify(property[i])}' /></td>
                                            <td class="align-center">${i + 1}</td>
                                            <td>${property[i]?.Name}</td>
                                            <td>${property[i]?.VMXPath}</td>
                                        </tr>`
                            }
                            $('#globalVariableTableBody').empty().append(tableData)
                            $('#btnVariableSubmit').attr('oprationGroupId', operationGroupId)
                            $('#currentGlobalVariableName').text(`- ${variableName}`)
                            $('#globalVariableModal').modal('show')
                        }
                        resolve(response.data)
                    } else {
                        resolve([])
                    }

                }
            }
        });
    })

}

$(document).on('click', '.globalVariableGroupCheckbox', function (e) {
    e.stopPropagation();
    $('#globalVariableTableBody .globalVariableCheckbox').prop('checked', e?.target?.checked)
})

$('#variableSearch').on('input', function (e) {
    let value = $(this)?.val()
    let tableContainer = $('#globalVariableTableBody')?.children()
    for (let i = 0; i < tableContainer?.length; i++) {
        if (tableContainer[i]?.innerText?.toLowerCase()?.includes(value)) {
            tableContainer[i].style.display = ''
        } else {
            tableContainer[i].style.display = 'none'
        }
    }
    if (!$('#globalVariableTableBody')?.children()?.is(':visible')) {
        $('#noDataImageContainer').removeClass('d-none')
    } else {
        $('#noDataImageContainer').addClass('d-none')
    }
})

$('#btnVariableSubmit').on('click', async function () {
    let getCheckedValues = $('.globalVariableCheckbox:checked')
    let operationGroupId = $(this)?.attr('oprationGroupId')
    let checkedArray = []
    if (getCheckedValues?.length) {
        $.each(getCheckedValues, function (idx, obj) {
            let details = JSON.parse($(this)?.attr('details'))
            checkedArray.push(details)
        })

        await getGlobalVariable(globalvariable, operationGroupId, 'check').then((res) => {
            let variableObj = res[0]

            if (variableObj && variableObj?.hasOwnProperty('variableValue')) {
                submitGlobalVariable(checkedArray, operationGroupId, variableObj)
            } else {
                submitGlobalVariable(checkedArray, operationGroupId, variableObj)
            }
        })
    } else {
        notificationAlert('warning', 'Select variable for submission')
    }
})

const submitGlobalVariable = async (checkedArray, operationGroupId, response) => {
    let data = {
        VariableName: globalvariable,
        VariableValue: JSON.stringify(checkedArray),
        Type: globalVariableType
    }
    let Url = createVariableResult;
    if (response && response?.hasOwnProperty('id')) {
        data['id'] = response?.id
        Url = updateVariableResult;
    }

    await $.ajax({
        type: "POST",
        data: data,
        url: RootUrl + Url,
        dataType: 'json',
        success: function (response) {
            if (response?.success) {
                notificationAlert('success', response?.message)
                updateConditionVariable(operationGroupId)
            } else {
                errorNotification(response)
            }
        }
    });
}

