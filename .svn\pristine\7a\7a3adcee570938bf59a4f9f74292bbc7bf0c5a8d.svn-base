﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class PluginManagerRepository : BaseRepository<PluginManager>, IPluginManagerRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public PluginManagerRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override Task<IReadOnlyList<PluginManager>> ListAllAsync()
    {
        return _loggedInUserService.IsParent
            ? base.ListAllAsync()
            : FindByFilter(pluginManager => pluginManager.CompanyId.Equals(_loggedInUserService.CompanyId));
    }

    public override Task<PluginManager> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilter(pluginManager =>
                    pluginManager.ReferenceId.Equals(id) &&
                    pluginManager.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Result.SingleOrDefault());
    }

    public Task<List<PluginManager>> GetPluginNames()
    {
        if (!_loggedInUserService.IsParent)
            return _dbContext.PluginManagers.Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new PluginManager { ReferenceId = x.ReferenceId, Name = x.Name })
                .OrderBy(x => x.Name)
                .ToListAsync();

        return _dbContext.PluginManagers
            .Active()
            .Select(x => new PluginManager { ReferenceId = x.ReferenceId, Name = x.Name })
            .OrderBy(x => x.Name)
            .ToListAsync();
    }

    public Task<bool> IsPluginNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(x => x.Name.Equals(name))
            : Entities.Where(y => y.Name.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsPluginNameUnique(string name)
    {
        var match = _dbContext.PluginManagers.Any(x => x.Name.Equals(name));

        return Task.FromResult(match);
    }
    public override async Task<PaginatedResult<PluginManager>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<PluginManager> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await (_loggedInUserService.IsParent
            ? Entities.Specify(productFilterSpec).DescOrderById()
            : Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<PluginManager> GetPaginatedQuery()
    {
        if (_loggedInUserService.IsParent)
            return Entities.Where(x => x.IsActive)
                .AsNoTracking()
                .OrderByDescending(x => x.Id);

        return Entities.Where(x => x.IsActive && x.CompanyId.Equals(_loggedInUserService.CompanyId))
            .AsNoTracking()
            .OrderByDescending(x => x.Id);
    }
}