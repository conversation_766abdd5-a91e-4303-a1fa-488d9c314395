﻿using ContinuityPatrol.Application.Features.SvcMsSqlMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SvcMsSqlMonitorStatusModel;

namespace ContinuityPatrol.Application.UnitTests.Features.SvcMsSqlMonitorStatus.Queries
{
    public class SvcMsSqlMonitorStatusPaginatedListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISvcMsSqlMonitorStatusRepository> _mockSvcMsSqlMonitorStatusRepository;
        private readonly SvcMsSqlMonitorStatusPaginatedListQueryHandler _handler;

        public SvcMsSqlMonitorStatusPaginatedListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSvcMsSqlMonitorStatusRepository = new Mock<ISvcMsSqlMonitorStatusRepository>();
            _handler = new SvcMsSqlMonitorStatusPaginatedListQueryHandler(_mockMapper.Object, _mockSvcMsSqlMonitorStatusRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedResult_WhenValidDataExists()
        {
            var query = new SvcMsSqlMonitorStatusPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = "Critical"
            };

            var svcMsSqlMonitorStatusEntities = new List<Domain.Entities.SvcMsSqlMonitorStatus>
            {
                new Domain.Entities.SvcMsSqlMonitorStatus { ReferenceId = Guid.NewGuid().ToString(), WorkflowName = "Critical", Type = "Active" },
                new Domain.Entities.SvcMsSqlMonitorStatus { ReferenceId = Guid.NewGuid().ToString(), WorkflowName = "Critical", Type = "Inactive" }
            };

            var svcMsSqlMonitorStatusViewModels = svcMsSqlMonitorStatusEntities.Select(e =>
                new SvcMsSqlMonitorStatusListVm
                {
                    Id = e.ReferenceId,
                    WorkflowName = "StatusType",
                    Type = "Status"
                }).ToList();

            //var paginatedResult = new PaginatedResult<SvcMsSqlMonitorStatusListVm>(
            //    svcMsSqlMonitorStatusViewModels, svcMsSqlMonitorStatusEntities.Equals, query.PageNumber, query.PageSize);

            var mockQueryable = svcMsSqlMonitorStatusEntities.AsQueryable().BuildMock();

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.GetPaginatedQuery())
                .Returns(mockQueryable);

            _mockMapper.Setup(m => m.Map<SvcMsSqlMonitorStatusListVm>(It.IsAny<Domain.Entities.SvcMsSqlMonitorStatus>()))
                .Returns<Domain.Entities.SvcMsSqlMonitorStatus>(entity =>
                    new SvcMsSqlMonitorStatusListVm
                    {
                        Id = entity.ReferenceId,
                        WorkflowName = "StatusType",
                        Type = "Status"
                    });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.TotalCount);
            Assert.Equal(query.PageNumber, result.CurrentPage);
            Assert.Equal(query.PageSize, result.PageSize);
            Assert.Equal("Critical", result.Data[0].WorkflowName);

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(m => m.Map<SvcMsSqlMonitorStatusListVm>(It.IsAny<Domain.Entities.SvcMsSqlMonitorStatus>()), Times.Exactly(2));
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyPaginatedResult_WhenNoDataMatchesFilter()
        {
            var query = new SvcMsSqlMonitorStatusPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = "NonExistent"
            };

            var svcMsSqlMonitorStatusEntities = new List<Domain.Entities.SvcMsSqlMonitorStatus>();

            var mockQueryable = svcMsSqlMonitorStatusEntities.AsQueryable().BuildMock();

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.GetPaginatedQuery())
                .Returns(mockQueryable);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);
            Assert.Equal(0, result.TotalCount);

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(m => m.Map<SvcMsSqlMonitorStatusListVm>(It.IsAny<Domain.Entities.SvcMsSqlMonitorStatus>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryAndMapperOnce_ForEachEntity()
        {
            var query = new SvcMsSqlMonitorStatusPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 1,
                SearchString = "Warning"
            };

            var svcMsSqlMonitorStatusEntities = new List<Domain.Entities.SvcMsSqlMonitorStatus>
            {
                new Domain.Entities.SvcMsSqlMonitorStatus { ReferenceId = Guid.NewGuid().ToString(), WorkflowName = "Warning", Type = "Active" }
            };

            var mockQueryable = svcMsSqlMonitorStatusEntities.AsQueryable().BuildMock();

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.GetPaginatedQuery())
                .Returns(mockQueryable);

            _mockMapper.Setup(m => m.Map<SvcMsSqlMonitorStatusListVm>(It.IsAny<Domain.Entities.SvcMsSqlMonitorStatus>()))
                .Returns<Domain.Entities.SvcMsSqlMonitorStatus>(entity =>
                    new SvcMsSqlMonitorStatusListVm
                    {
                        Id = entity.ReferenceId,
                        WorkflowName = "StatusType",
                        Type = "Status"
                    });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result.Data);
            Assert.Equal("Warning", result.Data[0].WorkflowName);

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(m => m.Map<SvcMsSqlMonitorStatusListVm>(It.IsAny<Domain.Entities.SvcMsSqlMonitorStatus>()), Times.Once);
        }
    }
}
