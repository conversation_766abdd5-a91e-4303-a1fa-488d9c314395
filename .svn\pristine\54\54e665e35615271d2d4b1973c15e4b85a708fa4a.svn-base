﻿using ContinuityPatrol.Application.Features.Setting.Commands.Create;
using ContinuityPatrol.Application.Features.Setting.Commands.Update;
using ContinuityPatrol.Application.Features.Setting.Events.Create;
using ContinuityPatrol.Application.Features.Setting.Events.Delete;
using ContinuityPatrol.Application.Features.Setting.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Setting.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class SettingFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<Setting> Settings { get; set; }

    public CreateSettingCommand CreateSettingCommand { get; set; }

    public UpdateSettingCommand UpdateSettingCommand { get; set; }

    public SettingCreatedEvent SettingCreatedEvent { get; set; }
    public SettingDeletedEvent SettingDeletedEvent { get; set; }
    public SettingUpdatedEvent SettingUpdatedEvent { get; set; }
    public SettingPaginatedEvent SettingPaginatedEvent { get; set; }

    public SettingFixture()
    {
        Settings = AutoSettingFixture.Create<List<Setting>>();

        CreateSettingCommand = AutoSettingFixture.Create<CreateSettingCommand>();

        UpdateSettingCommand = AutoSettingFixture.Create<UpdateSettingCommand>();

        SettingCreatedEvent = AutoSettingFixture.Create<SettingCreatedEvent>();

        SettingDeletedEvent = AutoSettingFixture.Create<SettingDeletedEvent>();

        SettingUpdatedEvent = AutoSettingFixture.Create<SettingUpdatedEvent>();

        SettingPaginatedEvent = AutoSettingFixture.Create<SettingPaginatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<SettingProfile>();
        });

        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoSettingFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateSettingCommand>(p => p.SKey, 10));
            fixture.Customize<CreateSettingCommand>(c => c.With(b => b.LoginUserId, 0.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateSettingCommand>(p => p.SKey, 10));
            fixture.Customize<UpdateSettingCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<Setting>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<SettingCreatedEvent>(p => p.SKey, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<SettingDeletedEvent>(p => p.SKey, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<SettingUpdatedEvent>(p => p.SKey, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<SettingPaginatedEvent>(p => p.SKey, 10));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}