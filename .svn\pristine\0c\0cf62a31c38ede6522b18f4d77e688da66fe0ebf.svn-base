using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class GlobalVariableFixture : IDisposable
{
    public List<GlobalVariable> GlobalVariablePaginationList { get; set; }
    public List<GlobalVariable> GlobalVariableList { get; set; }
    public GlobalVariable GlobalVariableDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public GlobalVariableFixture()
    {
        var fixture = new Fixture();

        GlobalVariableList = fixture.Create<List<GlobalVariable>>();

        GlobalVariablePaginationList = fixture.CreateMany<GlobalVariable>(20).ToList();


        GlobalVariableDto = fixture.Create<GlobalVariable>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
