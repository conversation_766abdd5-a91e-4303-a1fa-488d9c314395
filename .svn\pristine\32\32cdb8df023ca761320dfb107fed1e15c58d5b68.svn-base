using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Delete;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetailByName;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetList;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.GlobalVariableModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class GlobalVariablesController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<GlobalVariableListVm>>> GetGlobalVariables()
    {
        Logger.LogDebug("Get All GlobalVariables");

        return Ok(await Mediator.Send(new GetGlobalVariableListQuery()));
    }

    [HttpGet("{id}", Name = "GetGlobalVariable")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<GlobalVariableDetailVm>> GetGlobalVariableById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "GlobalVariable Id");

        Logger.LogDebug($"Get GlobalVariable Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetGlobalVariableDetailQuery { Id = id }));
    }



    [HttpGet("{variableName}")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<GlobalVariableDetailVm>>> GetGlobalVariableByVariableName(string variableName)
    {
        Guard.Against.NullOrWhiteSpace(variableName, "GlobalVariable variableName");

        Logger.LogDebug($"Get GlobalVariable Detail by variableName '{variableName}'");

        return Ok(await Mediator.Send(new GetGlobalVariableDetailByNameQuery { Name = variableName }));
    }
    #region Paginated
    [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Configuration.View)]
 public async Task<ActionResult<PaginatedResult<GlobalVariableListVm>>> GetPaginatedGlobalVariables([FromQuery] GetGlobalVariablePaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in GlobalVariable Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateGlobalVariableResponse>> CreateGlobalVariable([FromBody] CreateGlobalVariableCommand createGlobalVariableCommand)
    {
        Logger.LogDebug($"Create GlobalVariable '{createGlobalVariableCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateGlobalVariable), await Mediator.Send(createGlobalVariableCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateGlobalVariableResponse>> UpdateGlobalVariable([FromBody] UpdateGlobalVariableCommand updateGlobalVariableCommand)
    {
        Logger.LogDebug($"Update GlobalVariable '{updateGlobalVariableCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateGlobalVariableCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteGlobalVariableResponse>> DeleteGlobalVariable(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "GlobalVariable Id");

        Logger.LogDebug($"Delete GlobalVariable Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteGlobalVariableCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsGlobalVariableNameExist(string globalVariableName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(globalVariableName, "GlobalVariable Name");

     Logger.LogDebug($"Check Name Exists Detail by GlobalVariable Name '{globalVariableName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetGlobalVariableNameUniqueQuery { Name = globalVariableName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


