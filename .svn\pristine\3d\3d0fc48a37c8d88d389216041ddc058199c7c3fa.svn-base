using ContinuityPatrol.Domain.ViewModels.WorkflowApprovalMappingModel;

namespace ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Queries.GetList;

public class GetWorkflowApprovalMappingListQueryHandler : IRequestHandler<GetWorkflowApprovalMappingListQuery, List<WorkflowApprovalMappingListVm>>
{
    private readonly IWorkflowApprovalMappingRepository _workflowApprovalMappingRepository;
    private readonly IMapper _mapper;

    public GetWorkflowApprovalMappingListQueryHandler(IMapper mapper, IWorkflowApprovalMappingRepository workflowApprovalMappingRepository)
    {
        _mapper = mapper;
        _workflowApprovalMappingRepository = workflowApprovalMappingRepository;
    }

    public async Task<List<WorkflowApprovalMappingListVm>> Handle(GetWorkflowApprovalMappingListQuery request, CancellationToken cancellationToken)
    {
        var workflowApprovalMappings = await _workflowApprovalMappingRepository.ListAllAsync();

        if (workflowApprovalMappings.Count <= 0) return new List<WorkflowApprovalMappingListVm>();

        return _mapper.Map<List<WorkflowApprovalMappingListVm>>(workflowApprovalMappings);
    }
}
