﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface ICompanyRepository : IRepository<Company>
{
    Task<List<Company>> GetAllCompanyNames();

    Task<Company> GetCompanyByLoginCompanyId(string id);

    Task<bool> IsNameExist(string name, string id);

    Task<bool> IsDisplayNameExist(string displayName, string id);

    Task<bool> IsCompanyAndDisplayNameUnique(string name, string displayName);
    Task<Company> GetParentCompanyByLoginCompanyId(string id);
}