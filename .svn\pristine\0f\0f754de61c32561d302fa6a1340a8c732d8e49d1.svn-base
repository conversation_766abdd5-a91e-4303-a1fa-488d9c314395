using ContinuityPatrol.Application.Contracts.Job;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using Org.BouncyCastle.Security;

namespace ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Update;

public class UpdateBulkImportOperationGroupCommandHandler : IRequestHandler<UpdateBulkImportOperationGroupCommand,
    UpdateBulkImportOperationGroupResponse>
{
    private readonly IBulkImportOperationGroupRepository _bulkImportOperationGroupRepository;
    private readonly IMapper _mapper;
    private readonly IQuartzJobScheduler _client;
    private readonly IPublisher _publisher;
    private readonly ILoggedInUserService _loggedInUserService;

    public UpdateBulkImportOperationGroupCommandHandler(IMapper mapper, IQuartzJobScheduler client,
    IBulkImportOperationGroupRepository bulkImportOperationGroupRepository, IPublisher publisher,ILoggedInUserService loggedInUserService)
    {
        _mapper = mapper;
        _bulkImportOperationGroupRepository = bulkImportOperationGroupRepository;
        _publisher = publisher;
        _client=client;
        _loggedInUserService= loggedInUserService;
    }

    public async Task<UpdateBulkImportOperationGroupResponse> Handle(UpdateBulkImportOperationGroupCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _bulkImportOperationGroupRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.BulkImportOperationGroup), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateBulkImportOperationGroupCommand),
            typeof(Domain.Entities.BulkImportOperationGroup));

        await _bulkImportOperationGroupRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateBulkImportOperationGroupResponse
        {
            Message = Message.Update(nameof(Domain.Entities.BulkImportOperationGroup), eventToUpdate.InfraObjectName),

            Id = eventToUpdate.ReferenceId
        };
        if(eventToUpdate.Status.IsNotNullOrEmpty()&& eventToUpdate.Status.ToLower().Contains("next"))
        {
            await _client.ScheduleJob<BulkImportJob>(request.BulkImportOperationId,
                new Dictionary<string, string> { ["bulkImportOperationId"] = request.BulkImportOperationId, ["CompanyId"] = _loggedInUserService.CompanyId, ["UserId"] = _loggedInUserService.UserId, ["operationtype"]= "Next", ["bulkImportOperationGroupId"] = request.Id });

        }

        // await _publisher.Publish(new BulkImportOperationGroupUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}