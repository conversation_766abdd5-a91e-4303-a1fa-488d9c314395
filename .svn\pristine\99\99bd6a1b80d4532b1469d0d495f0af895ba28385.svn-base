using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SolutionHistoryFixture : IDisposable
{
    public List<SolutionHistory> SolutionHistoryPaginationList { get; set; }
    public List<SolutionHistory> SolutionHistoryList { get; set; }
    public SolutionHistory SolutionHistoryDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SolutionHistoryFixture()
    {
        var fixture = new Fixture();

        SolutionHistoryList = fixture.Create<List<SolutionHistory>>();

        SolutionHistoryPaginationList = fixture.CreateMany<SolutionHistory>(20).ToList();

        SolutionHistoryPaginationList.ForEach(x => x.CompanyId = CompanyId);

        SolutionHistoryList.ForEach(x => x.CompanyId = CompanyId);

        SolutionHistoryDto = fixture.Create<SolutionHistory>();

        SolutionHistoryDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
