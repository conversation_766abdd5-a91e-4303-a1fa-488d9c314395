﻿namespace ContinuityPatrol.Application.Features.AlertInformation.Commands.Delete;

public class
    DeleteAlertInformationCommandHandler : IRequestHandler<DeleteAlertInformationCommand,
        DeleteAlertInformationResponse>
{
    private readonly IAlertInformationRepository _alertInformationRepository;

    public DeleteAlertInformationCommandHandler(IAlertInformationRepository alertInformationRepository)
    {
        _alertInformationRepository = alertInformationRepository;
    }

    public async Task<DeleteAlertInformationResponse> Handle(DeleteAlertInformationCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _alertInformationRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.AlertInformation),
            new NotFoundException(nameof(Domain.Entities.AlertInformation), request.Id));

        eventToDelete.IsActive = false;

        await _alertInformationRepository.UpdateAsync(eventToDelete);

        var response = new DeleteAlertInformationResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.Alert), eventToDelete.Type),

            IsActive = eventToDelete.IsActive
        };

        return response;
    }
}