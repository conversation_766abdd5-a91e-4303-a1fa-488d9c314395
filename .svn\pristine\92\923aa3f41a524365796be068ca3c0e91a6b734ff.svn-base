﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;

public class CreateBulkDataServerListCommand
{
    public string Name { get; set; }

    [JsonIgnore] public string CompanyId { get; set; }

    public string SiteId { get; set; }
    public string SiteName { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string RoleTypeId { get; set; }
    public string RoleType { get; set; }
    public string ServerTypeId { get; set; }
    public string ServerType { get; set; }
    public string Logo { get; set; }
    public string OSType { get; set; }
    public string OSTypeId { get; set; }

    [JsonIgnore] public string Status { get; set; }

    public string Properties { get; set; }
    public string LicenseId { get; set; }
    public string LicenseKey { get; set; }
    public string Version { get; set; }

    public string FormVersion { get; set; }

    [JsonIgnore] public bool IsAttached { get; set; }
    //[JsonIgnore]
    //public string BulkImportOperationId { get; set; }
    //[JsonIgnore]
    //public string BulkImportOperationGroupId { get; set; }

    public override string ToString()
    {
        return $"Name: {Name};";
    }

    //public void Sanitize()
    //{
    //    var props = GetType().GetProperties()
    //        .Where(p => p.PropertyType == typeof(string) && p.CanRead && p.CanWrite);

    //    foreach (var prop in props)
    //    {
    //        if (prop.GetValue(this) is string value)
    //        {
    //            prop.SetValue(this, value.Trim());
    //        }
    //    }
    //}
}