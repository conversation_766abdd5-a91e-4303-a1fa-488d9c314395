﻿using ContinuityPatrol.Application.Features.FourEyeApprovers.Commands.Create;
using ContinuityPatrol.Application.Features.FourEyeApprovers.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.FourEyeApprovers.Queries.GetPaginatedListProfile;
using ContinuityPatrol.Application.Features.FourEyeApprovers.Queries.GetPaginatedListWorkflow;
using ContinuityPatrol.Domain.ViewModels.FourEyeApproversModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class FourEyeApproverService : BaseService, IFourEyeApproverService
{
    public FourEyeApproverService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateFourEyeApproversCommand createFourEyeApproversCommand)
    {
        Logger.LogDebug($"Creating Four-Eye_Approvers '{createFourEyeApproversCommand.workflow_profileName}'");

        return await Mediator.Send(createFourEyeApproversCommand);
    }

    public async Task<PaginatedResult<FourEyeApproversListVM>> GetPaginatedFourEyeApprovers(
        GetFourEyeApproversPaginatedListQuery query)
    {
        Logger.LogDebug("Getting Four Eye Approvers for ALL");

        return await Mediator.Send(query);
    }

    public async Task<PaginatedResult<FourEyeApproversListVM>> GetPaginatedFourEyeApprovers_Profile(
        GetFourEyeApproversPaginatedListProfileQuery query)
    {
        Logger.LogDebug("Getting Four Eye Approvers for Profile");

        return await Mediator.Send(query);
    }

    public async Task<PaginatedResult<FourEyeApproversListVM>> GetPaginatedFourEyeApprovers_Workflow(
        GetFourEyeApproversPaginatedListWorkflowQuery query)
    {
        Logger.LogDebug("Getting Four Eye Approvers for Workflow");

        return await Mediator.Send(query);
    }
}