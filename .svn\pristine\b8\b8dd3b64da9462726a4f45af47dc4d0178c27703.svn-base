﻿let checkCurrentMonth = ''
let checkCurrentYear = ''
let currentDate = new Date();
let currentMonth = (currentDate?.getMonth() + 1)?.toString()?.padStart(2, '0');
let currentYear = currentDate?.getFullYear();
let currentDay = currentDate?.getDate();

let displayValidDates = {
    '02': ['29', '30', '31'],
    '04': ['31'], '06': ['31'],
    '09': ['31'], '11': ['31'],
}

const displayFutureMonths = () => {
    let monthInput = document.getElementById("lblMonth");
    if (!monthInput) return;

    let today = new Date();
    let currentYear = today?.getFullYear();
    let currentMonth = today?.getMonth() + 1;
    let minMonth = currentYear + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
    let maxMonth = (currentYear + 77) + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
    monthInput.setAttribute("min", minMonth);
    monthInput.setAttribute("max", maxMonth);
}

displayFutureMonths()

const updateCheckboxes = () => {

    $('input[name=Monthyday]').each(function () {
        let checkboxValue = parseInt($(this).val());

        if (checkCurrentYear > currentYear) {
            $(this).prop('disabled', false);
        } else if (checkCurrentMonth === currentMonth && checkboxValue >= currentDay) {
            $(this).prop('disabled', false);
        } else if (checkCurrentMonth > currentMonth || (checkCurrentMonth === currentMonth && checkboxValue > currentDay)) {
            $(this).prop('disabled', false);
        } else {
            $(this).prop('disabled', true);
        }
    });

    if (checkCurrentMonth || currentMonth) {
        let selectedMonth = (checkCurrentMonth ?? currentMonth).toString().padStart(2, '0');
        let selectedYear = checkCurrentYear ?? currentYear;

        let isLeapYear = (selectedYear % 4 === 0 && selectedYear % 100 !== 0) || (selectedYear % 400 === 0);
        let monthsWith30 = ['04', '06', '09', '11'];
        $('input[name="Monthyday"]').parent().show();
        if (selectedMonth === '02') {
            const hideDays = isLeapYear ? ['30', '31'] : ['29', '30', '31'];
            hideDays.forEach(day => {
                $(`input[name="Monthyday"][value="${day}"]`).parent().hide();
            });
        } else if (monthsWith30.includes(selectedMonth)) {
            $(`input[name="Monthyday"][value="31"]`).parent().hide();
        }
    }
}

updateCheckboxes();

function CronValidation() {

    let checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    let weeklyDays = Array.from(checkedCheckboxes).map(checkbox => checkbox?.value);
    let checkedDaysCheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    let monthlyDays = Array.from(checkedDaysCheckboxes).map(checkbox => checkbox?.value);
    
    let textHours = $('#txtHours').val();
    let texMint = $("#txtMinutes").val()
    let ddlHours = $('#ddlHours').val();
    let everyHours = $('#everyHours').val();
    let monthlyHours = $('#monthlyHours').val();
    let month = $('#lblMonth').val();

    let isScheduler = true;
    let Scheduler_types = $('.nav-tabs .active')?.text()?.trim();

    switch (Scheduler_types) {

        case "Hourly":
            $('#everyHours, #ddlHours, #lblMonth, #monthlyHours').val('');
            $("#defaultCheck-everyday, #defaultCheck-MON-FRI").prop("checked", false)
            $('input[name=weekDays], input[name=Monthyday]').prop("checked", false)
           
            isScheduler = validateHourNumber(textHours, "Enter hours", $('#CronHourlyError')) && isScheduler;
            isScheduler = validateMinNumber(texMint, " Enter minutes", $('#CronHourMinError')) && isScheduler;
            break;

        case "Daily":
            $('#txtHours,#txtMinutes, #ddlHours, #lblMonth, #monthlyHours').val('');
            $('input[name=weekDays], input[name=Monthyday]').prop("checked", false)

            isScheduler = ValidateCronRadioButton($('#CrondayseveryError')) && isScheduler;
            isScheduler = validateHourandDayNumber(everyHours, "Select hours", $('#CroneveryHourError')) && isScheduler;
            break;

        case "Weekly":
            $('#txtHours,#txtMinutes, #everyHours, #lblMonth, #monthlyHours').val('');
            $("#defaultCheck-everyday, #defaultCheck-MON-FRI").prop("checked", false)
            $('input[name=Monthyday]').prop("checked", false)

            isScheduler = validateHourandDayNumber(weeklyDays, "Select day(s)", $('#CronDayError')) && isScheduler;
            isScheduler = validateHourandDayNumber(ddlHours, "Select hours", $('#CronddlHourError')) && isScheduler;
            break;

        case "Monthly":

            $('#txtHours,#txtMinutes, #everyHours, #ddlHours').val('');
            $("#defaultCheck-everyday, #defaultCheck-MON-FRI").prop("checked", false)
            $('input[name=weekDays]').prop("checked", false)
            checkCurrentMonth = ''
            //updateCheckboxes();   

            isScheduler = validateHourandDayNumber(month, "Select month and year", $('#CronMonthError')) && isScheduler;
            $('input[name="Monthyday"]').each(function () {
                const monthlyDay = $(this);
                if (!monthlyDay.prop('disabled')) {
                    $('#CronMonthlyDay-error').show();
                    const isValid = validateHourandDayNumber(monthlyDays, "Select date(s)", $('#CronMonthlyDayError')) && isScheduler;
                    isScheduler = isValid && isScheduler;
                }
            });
            isScheduler = validateHourandDayNumber(monthlyHours, "Select hours", $('#CronMonthHrsError')) && isScheduler;
            break;
    }

    return isScheduler;
}
function validateMinNumber(value, errorMsg, errorElement) {
    
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    }
    else if ((Number(value) < 0) || (Number(value) > 59)) {
        errorElement.text("Enter value between 1 to 59").addClass('field-validation-error');
        return false;
    } else if (Number(value) == "0") {
        errorElement.text("Enter the value more than 0").addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}

function validateHourNumber(value, errorMsg, errorElement) {
    
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    }
    else if ((Number(value) == 0)) {
        errorElement.text("Enter value greater than zero").addClass('field-validation-error');
        return false;
    }
    else if ((Number(value) < 1) || (Number(value) >= 24)) {
        errorElement.text("Enter value between 1 to 23").addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}

// schedule nav tab click

$('.nav-link').on('click', function (event) {

    let navName = event?.target?.name;
    if (navName === 'hourly') {

        $('#everyHours, #ddlHours, #lblMonth, #monthlyHours').val('');
        $("#defaultCheck-everyday, #defaultCheck-MON-FRI").prop("checked", false)
        $('input[name=weekDays], input[name=Monthyday]').prop("checked", false)
        $('input[name=Monthyday]').prop("disabled", true)

        $('#CrondayseveryError, #CroneveryHourError, #CronDayError, #CronddlHourError, #CronMonthError, #CronMonthHrsError, #CronMonthlyDayError').text('').removeClass('field-validation-error');

    } else if (navName === 'daily') {

        $('#txtHours,#txtMinutes, #ddlHours, #lblMonth, #monthlyHours').val('');
        $('input[name=weekDays], input[name=Monthyday]').prop("checked", false)
        $('input[name=Monthyday]').prop("disabled", true)

        $('#CronHourlyError,#CronHourMinError, #CronDayError, #CronddlHourError, #CronMonthError, #CronMonthHrsError, #CronMonthlyDayError').text('').removeClass('field-validation-error');

    } else if (navName === 'weekly') {

        $('#txtHours,#txtMinutes, #everyHours, #lblMonth, #monthlyHours').val('');
        $("#defaultCheck-everyday, #defaultCheck-MON-FRI").prop("checked", false)
        $('input[name=Monthyday]').prop("checked", false).prop("disabled", true)

        $('#CronHourlyError,#CronHourMinError, #CrondayseveryError, #CroneveryHourError, #CronMonthError, #CronMonthHrsError, #CronMonthlyDayError').text('').removeClass('field-validation-error');

    } else if (navName === 'monthly') {

        $('#txtHours,#txtMinutes, #everyHours, #ddlHours').val('');
        $("#defaultCheck-everyday, #defaultCheck-MON-FRI").prop("checked", false)
        $('input[name=weekDays]').prop("checked", false)

        $('#CronHourlyError,#CronHourMinError, #CrondayseveryError, #CroneveryHourError, #CronDayError, #CronddlHourError').text('').removeClass('field-validation-error');
    }

})

$('#lblMonth').on('change', function () {
    let selectedValue = $(this).val();
    if (!selectedValue) {
        checkCurrentMonth = null;
        checkCurrentYear = null;
        $('input[name="Days"]').prop('disabled', true).removeClass('is-invalid');
        $('input[name="Monthyday"]').prop('checked', false).prop('disabled', true).removeClass('is-invalid');
        $('#CronMonthlyDayError').hide();
    } else {
        let selectedMonth = $(this).val().split('-')[1];
        let selectedYear = $(this).val().split('-')[0];
        checkCurrentMonth = selectedMonth?.padStart(2, '0');
        checkCurrentYear = selectedYear;
        $('input[name = Monthyday]').prop("checked", false)
        updateCheckboxes();
    }
});

// radio button validation
function ValidateCronRadioButton(errorElement) {
    if ($('input[name=daysevery]:checked')?.length) {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }else {
        errorElement.text("Select day type").addClass('field-validation-error');
        return false;
    }
}

// validate hours and Days

const validateHourandDayNumber = (value, errorMsg, errorElement) => {

    if (!value || (Array.isArray(value) && !value.length)) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}

// Generate Cron Expression
function GetCronExpression() {

    let checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    let weeklyDays = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);

    let checkedDaysCheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    let montlyDays = Array.from(checkedDaysCheckboxes).map(checkbox => checkbox.value);

    let Scheduler_types = $('.nav-tabs .active').text().trim();

    let textHours = $('#txtHours').val();
    let texMint = $("#txtMinutes").val()
   /* let hourly = $('#hourly').val();*/
    let weeklyHours = $('#ddlHours').val();
    let everyHours = $('#everyHours').val();
    let weekDay = $('#defaultCheck-MON-FRI').val();
    let month = $('#lblMonth').val();
    let monthYear = month.split("-");
    let monthlyHours = $('#monthlyHours').val();

    let schedule_model = document.querySelector('input[name="daysevery"]:checked');
    let cronListView = "";
    let cronExpression = ""

    if (weeklyDays.length) {
        let cronTime = getCronTimeExpression(weeklyHours);

        cronExpression = "0 " + cronTime[1] + " " + cronTime[0] + " ? * " + weeklyDays + " *";
        cronListView = "Every " + cronTime[0] + " Hrs " + cronTime[1] + " Mins for " + weeklyDays;
    }
    else if (montlyDays.length) {
        let cronTime = getCronTimeExpression(monthlyHours);

        cronExpression = "0 " + cronTime[1] + " " + cronTime[0] + " " + montlyDays + " " + monthYear[1] + " ? " + monthYear[0];
        cronListView = "At " + cronTime[0] + " Hrs " + cronTime[1] + " Mins on the " + montlyDays + "day(s), in " + monthYear[1] + "-" + monthYear[0];

    } else if (Scheduler_types?.toLowerCase() == "hourly") {
        let cronTime = getCronTimeExpression(textHours + ":" + texMint);  

        cronExpression = `0 ${cronTime[1]} 0/${cronTime[0]} * * ?`;
        cronListView = "Every" + cronTime[1] + " hours " + cronTime[0] + "minutes";
        
    }
    else if (schedule_model?.value == "everyday") {
        let cronTime = getCronTimeExpression(everyHours);

        cronExpression = `0 ${cronTime[1]} ${cronTime[0]} * * ?`;
        cronListView = `${cronTime[1]} Hrs ${cronTime[0]} Mins on Every Day`;

    }
    else if (schedule_model?.value == "MON-FRI") {
        let cronTime = getCronTimeExpression(everyHours);

        cronExpression = "0 " + cronTime[1] + " " + cronTime[0] + " ? * " + weekDay
        cronListView = cronTime[0] + " Hrs " + cronTime[1] + " Mins on Every " + weekDay;
    }

    return { cronExpression, cronListView };
}

const getCronTimeExpression = (time) => {
    const cronTime = time?.split(":")?.map(num => parseInt(num));
    return cronTime;
}

const convertCronToOriginalValue = (cronExpression, isEdit = false, row = {}) => {
    if (cronExpression) {
        
        let cronListView = "";
        const cronParts = cronExpression?.split(' ');

        let hours = cronParts[2]?.split('/')[1];
        const minutes = cronParts[1];
        let daysOfWeek = cronParts[5]?.split(',');

        const date = cronParts[4];
        const year = cronParts[6];
        const monthAbbreviations = {
        "01": "JAN",
        "02": "FEB",
        "03": "MAR",
        "04": "APR",
        "05": "MAY",
        "06": "JUN",
        "07": "JUL",
        "08": "AUG",
        "09": "SEP",
        "10": "OCT",
        "11": "NOV",
        "12": "DEC"
    };
       
        if (!hours) hours = cronParts[2]
        if (daysOfWeek?.includes('?')) daysOfWeek = cronParts[3]?.split(',');

        let getHrsAndMins = `${hours?.length === 1 ? `0${hours}` : hours}:${minutes?.length === 1 ? `0${minutes}` : minutes}`

        if ((date && year) && (!date?.includes('*'))) {
            const monthAbbr = monthAbbreviations[date] || '';
            cronListView = `${hours} hours ${minutes} minutes for ${daysOfWeek?.join(',')} day(s) on ${monthAbbr} in the year ${year}`;

            if (isEdit) {
                $('input[name="Monthyday"]').prop("checked", false)
                daysOfWeek.forEach((day) => {
                    $(`input[name="Monthyday"][value="${day}"]`).prop('checked', true);
                });

                $('#lblMonth').val(`${year}-${date}`)
                $('#monthlyHours').val(getHrsAndMins)

                checkCurrentMonth = date
                checkCurrentYear = year
                updateCheckboxes();
                $('#nav-Monthly-tab').addClass('active')
                $('#nav-Monthly').addClass('active show')

                $('#everyHours, #ddlHours').val('');
                $("#defaultCheck-everyday, #defaultCheck-MON-FRI").prop("checked", false)
                $('input[name=weekDays]').prop("checked", false)
                $('#nav-Daily-tab,#nav-Hourly-tab,#nav-Hourly, #nav-Daily, #nav-Weekly-tab, #nav-Weekly').removeClass('active')
            }

        } else if (daysOfWeek?.length && !daysOfWeek?.includes('*') && !daysOfWeek?.includes('MON-FRI')) {
            cronListView = `${daysOfWeek?.join(',')} at ${hours} hours ${minutes} minutes`;
            
            if (isEdit) {
                daysOfWeek?.forEach((day) => {
                    let isUpper = day?.toUpperCase() === day

                    if (isUpper) {
                        day = day?.charAt(0)?.toUpperCase() + day?.slice(1)?.toLowerCase();
                    }

                    $(`input[name="weekDays"][value="${day}"]`).prop('checked', true);
                });
                $('#ddlHours').val(getHrsAndMins)

                $('#nav-Weekly-tab').addClass('active')
                $('#nav-Weekly').addClass('active show')

                $('#everyHours, #lblMonth, #monthlyHours').val('');
                $("#defaultCheck-everyday, #defaultCheck-MON-FRI").prop("checked", false)
                $('input[name=Monthyday]').prop("checked", false)

                $('#nav-Hourly-tab,#nav-Hourly,#nav-Daily-tab, #nav-Daily, #nav-Monthly-tab, #nav-Monthly').removeClass('active')
            }

        } else {
            
            if (cronParts.includes('MON-FRI')) {
                cronListView = `MON-FRI at ${hours} hours ${minutes} minutes`;
                $('#defaultCheck-MON-FRI').prop('checked', true)

            } else {
                if (row?.type?.toLowerCase() == 'hourly') {
                    cronListView = `Every ${hours} hours ${minutes} minutes`;
                } else {
                    cronListView = `Every day at ${hours} hours ${minutes} minutes`;
                    $('#defaultCheck-everyday').prop('checked', true);
                }
            }
            if (isEdit) {
                if (row?.type?.toLowerCase() == 'hourly') {
                    
                    /*  $('#hourly').val(getHrsAndMins)*/
                    $('#txtHours').val(hours);
                    $("#txtMinutes").val(minutes)
                    $('#nav-Daily-tab').removeClass('active')
                    $('#nav-Daily').removeClass('active show')
                    $('#nav-Hourly-tab').addClass('active')
                    $('#nav-Hourly').addClass('active show')
                    $('#everyHours').val('')
                    $("#defaultCheck-everyday, #defaultCheck-MON-FRI").prop("checked", false)
                } else {
                    $('#everyHours').val(getHrsAndMins)
                    $('#nav-Hourly-tab').removeClass('active')
                    $('#nav-Hourly').removeClass('active show')
                    $('#nav-Daily-tab').addClass('active')
                    $('#nav-Daily').addClass('active show')
                    $('#hourly').val('')
                }

                $('#ddlHours, #lblMonth, #monthlyHours').val('');
                $('input[name=weekDays], input[name=Monthyday]').prop("checked", false)
                $('#nav-Weekly-tab, #nav-Weekly, #nav-Monthly-tab, #nav-Monthly').removeClass('active')
            }
        }

        return cronListView
    }
}

