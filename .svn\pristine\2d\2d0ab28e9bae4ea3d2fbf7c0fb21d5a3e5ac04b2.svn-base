﻿using ContinuityPatrol.Application.Features.InfraObjectInfo.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObjectInfo.Commands;

public class UpdateInfraObjectInfoTests : IClassFixture<InfraObjectInfoFixture>
{
    private readonly InfraObjectInfoFixture _infraObjectInfoFixture;
    private readonly Mock<IInfraObjectInfoRepository> _mockInfraObjectInfoRepository;
    private readonly UpdateInfraObjectInfoCommandHandler _handler;

    public UpdateInfraObjectInfoTests(InfraObjectInfoFixture infraObjectInfoFixture)
    {
        _infraObjectInfoFixture = infraObjectInfoFixture;

        _mockInfraObjectInfoRepository = InfraObjectInfoRepositoryMocks.UpdateInfraObjectInfoRepository(_infraObjectInfoFixture.InfraObjectInfos);

        _handler = new UpdateInfraObjectInfoCommandHandler(_infraObjectInfoFixture.Mapper, _mockInfraObjectInfoRepository.Object);
    }

    [Fact]
    public async Task Handle_ValidInfraObjectInfo_UpdateToInfraObjectInfosRepo()
    {
        _infraObjectInfoFixture.UpdateInfraObjectInfoCommand.Id = _infraObjectInfoFixture.InfraObjectInfos[0].ReferenceId;

        var result = await _handler.Handle(_infraObjectInfoFixture.UpdateInfraObjectInfoCommand, CancellationToken.None);

        var infraObjectInfo = await _mockInfraObjectInfoRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_infraObjectInfoFixture.UpdateInfraObjectInfoCommand.InfraObjectId, infraObjectInfo.InfraObjectId);
    }

    [Fact]
    public async Task Handle_Return_UpdateInfraObjectInfoResponse_When_InfraObjectInfoUpdated()
    {
        _infraObjectInfoFixture.UpdateInfraObjectInfoCommand.Id = _infraObjectInfoFixture.InfraObjectInfos[0].ReferenceId;

        var result = await _handler.Handle(_infraObjectInfoFixture.UpdateInfraObjectInfoCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateInfraObjectInfoResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_infraObjectInfoFixture.UpdateInfraObjectInfoCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidInfraObjectInfoId()
    {
        _infraObjectInfoFixture.UpdateInfraObjectInfoCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_infraObjectInfoFixture.UpdateInfraObjectInfoCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _infraObjectInfoFixture.UpdateInfraObjectInfoCommand.Id = _infraObjectInfoFixture.InfraObjectInfos[0].ReferenceId;

        await _handler.Handle(_infraObjectInfoFixture.UpdateInfraObjectInfoCommand, CancellationToken.None);

        _mockInfraObjectInfoRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockInfraObjectInfoRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.InfraObjectInfo>()), Times.Once);
    }
}