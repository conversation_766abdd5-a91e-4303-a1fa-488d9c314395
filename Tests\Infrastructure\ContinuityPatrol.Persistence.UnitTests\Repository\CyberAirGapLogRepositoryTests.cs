using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Serilog;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class CyberAirGapLogRepositoryTests : IClassFixture<CyberAirGapLogFixture>
{
    private readonly CyberAirGapLogFixture _cyberAirGapLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberAirGapLogRepository _repository;

    public CyberAirGapLogRepositoryTests(CyberAirGapLogFixture cyberAirGapLogFixture)
    {
        _cyberAirGapLogFixture = cyberAirGapLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberAirGapLogRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var cyberAirGapLog = _cyberAirGapLogFixture.CyberAirGapLogDto;
        cyberAirGapLog.AirGapName = "ExistingName";
        await _dbContext.CyberAirGapLogs.AddAsync(cyberAirGapLog);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.IsNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var cyberAirGapLogs = _cyberAirGapLogFixture.CyberAirGapLogList;
        await _repository.AddRangeAsync(cyberAirGapLogs);

        // Act
        var result = await _repository.IsNameExist("NonExistentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var cyberAirGapLog = _cyberAirGapLogFixture.CyberAirGapLogDto;
        cyberAirGapLog.AirGapName = "SameName";
        await _dbContext.CyberAirGapLogs.AddAsync(cyberAirGapLog);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.IsNameExist("SameName", cyberAirGapLog.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetCyberAirGapLogByAirGabId Tests

    [Fact]
    public async Task GetCyberAirGapLogByAirGabId_ShouldReturnLogsForSpecificAirGap()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = baseDate.ToString("yyyy-MM-dd");
        var airGapId = "SPECIFIC_AIRGAP_001";

        var logs = new List<CyberAirGapLog>
        {
            new CyberAirGapLog 
            { 
                AirGapId = airGapId,
                AirGapName = "SpecificAirGap1",
                Status = "Success",
                CreatedDate = baseDate.AddDays(-3),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGapLog 
            { 
                AirGapId = airGapId,
                AirGapName = "SpecificAirGap2",
                Status = "Error",
                CreatedDate = baseDate.AddDays(-1),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGapLog 
            { 
                AirGapId = "DIFFERENT_AIRGAP_001",
                AirGapName = "DifferentAirGap",
                Status = "Success",
                CreatedDate = baseDate.AddDays(-2),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetCyberAirGapLogByAirGabId(startDate, endDate, airGapId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(airGapId, x.AirGapId));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= DateTime.Parse(startDate)));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date <= DateTime.Parse(endDate)));
    }

    [Fact]
    public async Task GetCyberAirGapLogByAirGabId_ShouldReturnAllLogsWhenAirGapIdIsAll()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = baseDate.ToString("yyyy-MM-dd");

        var logs = new List<CyberAirGapLog>
        {
            new CyberAirGapLog 
            { 
                AirGapId = "AIRGAP_001",
                AirGapName = "AirGap1",
                Status = "Success",
                CreatedDate = baseDate.AddDays(-3),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGapLog 
            { 
                AirGapId = "AIRGAP_002",
                AirGapName = "AirGap2",
                Status = "Error",
                CreatedDate = baseDate.AddDays(-1),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGapLog 
            { 
                AirGapId = "AIRGAP_003",
                AirGapName = "AirGap3",
                Status = "Warning",
                CreatedDate = baseDate.AddDays(-15),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        _dbContext.CyberAirGapLogs.AddRange(logs);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetCyberAirGapLogByAirGabId(startDate, endDate, "all");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only logs within date range
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= DateTime.Parse(startDate)));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date <= DateTime.Parse(endDate)));
    }

    [Fact]
    public async Task GetCyberAirGapLogByAirGabId_ShouldReturnEmpty_WhenNoLogsInDateRange()
    {
        // Arrange
        var logs = _cyberAirGapLogFixture.CyberAirGapLogList;
        await _repository.AddRangeAsync(logs);

        var futureStartDate = DateTime.Now.AddDays(10).ToString("yyyy-MM-dd");
        var futureEndDate = DateTime.Now.AddDays(15).ToString("yyyy-MM-dd");

        // Act
        var result = await _repository.GetCyberAirGapLogByAirGabId(futureStartDate, futureEndDate, "AIRGAP_001");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetAirGaplist Tests

    [Fact]
    public async Task GetAirGaplist_ShouldReturnLogsInDateRange()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = baseDate.ToString("yyyy-MM-dd");

        var logs = new List<CyberAirGapLog>
        {
            new CyberAirGapLog 
            { 
                AirGapId = "AIRGAP_001",
                AirGapName = "AirGap1",
                Status = "Success",
                CreatedDate = baseDate.AddDays(-3),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGapLog 
            { 
                AirGapId = "AIRGAP_002",
                AirGapName = "AirGap2",
                Status = "Error",  
                CreatedDate = baseDate.AddDays(-1),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGapLog 
            { 
                AirGapId = "AIRGAP_003",
                AirGapName = "AirGap3",
                Status = "Warning",
                CreatedDate = baseDate.AddDays(-15), // Outside date range
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

         _dbContext.CyberAirGapLogs.AddRange(logs);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetAirGaplist(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= DateTime.Parse(startDate)));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date <= DateTime.Parse(endDate)));
    }

    [Fact]
    public async Task GetAirGaplist_ShouldReturnEmpty_WhenNoLogsInDateRange()
    {
        // Arrange
        var logs = _cyberAirGapLogFixture.CyberAirGapLogList;
        await _repository.AddRangeAsync(logs);

        var futureStartDate = DateTime.Now.AddDays(10).ToString("yyyy-MM-dd");
        var futureEndDate = DateTime.Now.AddDays(15).ToString("yyyy-MM-dd");

        // Act
        var result = await _repository.GetAirGaplist(futureStartDate, futureEndDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Infrastructure Assignment Tests

    [Fact]
    public async Task Repository_ShouldHandleAirGapAssignments()
    {
        // Arrange
        var logs = new List<CyberAirGapLog>
        {
            new CyberAirGapLog
            {
                AirGapId = "DB_AIRGAP_001",
                AirGapName = "DatabaseAirGap",
                Status = "Success",
                CreatedDate = DateTime.Now.AddDays(-1),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGapLog
            {
                AirGapId = "WEB_AIRGAP_001",
                AirGapName = "WebAirGap",
                Status = "Error",
                CreatedDate = DateTime.Now.AddDays(-1),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGapLog
            {
                AirGapId = "APP_AIRGAP_001",
                AirGapName = "ApplicationAirGap",
                Status = "Warning",
                CreatedDate = DateTime.Now.AddDays(-1),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(logs);

        // Act
        var dbLogs = await _repository.FindByFilterAsync(x => x.AirGapId.Contains("DB"));
        var webLogs = await _repository.FindByFilterAsync(x => x.AirGapId.Contains("WEB"));
        var appLogs = await _repository.FindByFilterAsync(x => x.AirGapId.Contains("APP"));

        // Assert
        Assert.Single(dbLogs);
        Assert.Single(webLogs);
        Assert.Single(appLogs);
        Assert.Contains("DB", dbLogs.First().AirGapId);
        Assert.Contains("WEB", webLogs.First().AirGapId);
        Assert.Contains("APP", appLogs.First().AirGapId);
        Assert.Equal("Success", dbLogs.First().Status);
        Assert.Equal("Error", webLogs.First().Status);
        Assert.Equal("Warning", appLogs.First().Status);
    }

    #endregion

    #region Log Level and Status Tests

    [Fact]
    public async Task Repository_ShouldFilterByLogTargetId()
    {
        // Arrange
        var logs = new List<CyberAirGapLog>
        {
            new CyberAirGapLog
            {
                AirGapId = "AIRGAP_001",
                AirGapName = "AirGap1",
                Status = "Success",
                TargetSiteName = "PR",
                CreatedDate = DateTime.Now,
                ReferenceId = Guid.NewGuid().ToString(),
 
                IsActive = true
            },
            new CyberAirGapLog
            {
                AirGapId = "AIRGAP_002",
                AirGapName = "AirGap2",
                Status = "Error",
                TargetSiteName = "DR",
                CreatedDate = DateTime.Now,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGapLog
            {
                AirGapId = "AIRGAP_003",
                AirGapName = "AirGap3",
                Status = "Warning",
                CreatedDate = DateTime.Now,
                TargetSiteName= "NearDR",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(logs);

        // Act
        var infoLogs = await _repository.FindByFilterAsync(x => x.TargetSiteName == "PR");
        var errorLogs = await _repository.FindByFilterAsync(x => x.TargetSiteName == "DR");
        var warningLogs = await _repository.FindByFilterAsync(x => x.TargetSiteName == "NearDR");

        // Assert
        Assert.Single(infoLogs);
        Assert.Single(errorLogs);
        Assert.Single(warningLogs);
        Assert.Equal("PR", infoLogs.First().TargetSiteName);
        Assert.Equal("DR", errorLogs.First().TargetSiteName);
        Assert.Equal("NearDR", warningLogs.First().TargetSiteName);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var log = _cyberAirGapLogFixture.CyberAirGapLogList;
        var log1 = log[0];
        var log2 = log[1];
        
        log2.AirGapName = "DifferentAirGap";

        // Act
        await _dbContext.CyberAirGapLogs.AddAsync(log1);
        await _dbContext.CyberAirGapLogs.AddAsync(log2);
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync();


        // Assert
        Assert.Equal(2, results.Count);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.CyberAirGapLogs.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var logs = _cyberAirGapLogFixture.CyberAirGapLogList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(logs);
        var initialCount = logs.Count;

        var toUpdate = logs.Take(2).ToList();
        toUpdate.ForEach(x => x.Status = "Updated");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = logs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Status == "Updated").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
