﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class AlertMasterFilterSpecification : Specification<AlertMaster>
{
    public AlertMasterFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.AlertName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("alertname=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.AlertName.Contains(stringItem.Replace("alertname=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    else if (stringItem.Contains("alertid=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.AlertId.Contains(stringItem.Replace("alertid=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("alertmessage=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.AlertMessage.Contains(stringItem.Replace("alertmessage=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("alertpriority=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.AlertPriority.Contains(stringItem.Replace("alertpriority=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.AlertName.Contains(searchString) || p.AlertMessage.Contains(searchString) ||
                    p.AlertId.Contains(searchString) ||
                    p.AlertPriority.Contains(searchString);
            }
        }
    }
}