﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowActionTypeRepository : BaseRepository<WorkflowActionType>, IWorkflowActionTypeRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;
    public WorkflowActionTypeRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<WorkflowActionType>> ListAllAsync()
    {
        return await SelectToActionTypes(base.QueryAll(x => x.IsActive))
            .ToListAsync();
    }

    public async Task<WorkflowActionType> GetWorkflowActionTypeById(string id)
    {
        var matches = await _dbContext.WorkflowActionTypes
            .AsNoTracking().Active()
            .Where(e => e.ReferenceId.Equals(id) && e.IsDelete)
            .FirstOrDefaultAsync();

        return matches;
    }

    public Task<bool> IsWorkflowActionTypeExist(string actionType, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.WorkflowActionTypes.Any(e => e.ActionType.Equals(actionType)))
            : Task.FromResult(_dbContext.WorkflowActionTypes.Where(e => e.ActionType.Equals(actionType)).ToList()
                .Unique(id));
    }

    public override async Task<PaginatedResult<WorkflowActionType>>PaginatedListAllAsync(int pageNumber, int pageSize, Specification<WorkflowActionType> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await SelectToActionTypes(Entities.Specify(productFilterSpec).DescOrderById())
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public Task<bool> IsWorkflowActionTypeUnique(string actionType)
    {
        var matches = _dbContext.WorkflowActionTypes.Any(e => e.ActionType.Equals(actionType));

        return Task.FromResult(matches);
    }

    private IQueryable<WorkflowActionType> SelectToActionTypes(IQueryable<WorkflowActionType> query)
    {
        return query.Select(x => new WorkflowActionType
        {
            ReferenceId = x.ReferenceId,
            ActionType = x.ActionType,
            IsDelete = x.IsDelete
        });
    }

}