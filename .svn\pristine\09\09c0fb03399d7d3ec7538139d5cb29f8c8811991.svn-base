﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Database.Events.Delete;

public class DatabaseDeletedEventHandler : INotificationHandler<DatabaseDeletedEvent>
{
    private readonly IHeatMapStatusRepository _heatMapStatusRepository;
    private readonly ILogger<DatabaseDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DatabaseDeletedEventHandler(ILoggedInUserService userService, ILogger<DatabaseDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository, IHeatMapStatusRepository heatMapStatusRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _heatMapStatusRepository = heatMapStatusRepository;
    }

    public async Task Handle(DatabaseDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.Database}",
            Entity = Modules.Database.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Database '{deletedEvent.DatabaseName}' deleted successfully."
        };

        var heatMapStatusListByEntity =
            await _heatMapStatusRepository.GetHeatMapStatusByEntityId(deletedEvent.DatabaseId);
      
        heatMapStatusListByEntity.ForEach(heatMap => heatMap.IsActive = false);

        await _heatMapStatusRepository.UpdateRange(heatMapStatusListByEntity);
       
        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Database '{deletedEvent.DatabaseName}' deleted successfully.");
    }
}