﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ReportScheduleRepository : BaseRepository<ReportSchedule>, IReportScheduleRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public ReportScheduleRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<ReportSchedule>> ListAllAsync()
    {
        var reportSchedules = base.ListAllAsync(report =>
           report.CompanyId.Equals(_loggedInUserService.CompanyId));

        return await reportSchedules.ToListAsync();
    }
    public override async Task<PaginatedResult<ReportSchedule>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<ReportSchedule> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await (_loggedInUserService.IsParent
            ? Entities.Specify(productFilterSpec).DescOrderById()
            : Entities.Specify(productFilterSpec).Where(x=>x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<ReportSchedule> PaginatedListAllAsync()
    {
        return base.ListAllAsync(report => report.CompanyId.Equals(_loggedInUserService.CompanyId))
                   .AsNoTracking().OrderByDescending(x => x.Id);
    }
    public override async Task<ReportSchedule> GetByReferenceIdAsync(string id)
    {
        var reportSchedules = base.GetByReferenceIdAsync(id,
                report => report.CompanyId.Equals(_loggedInUserService.CompanyId) && report.ReferenceId.Equals(id));

        return await reportSchedules.FirstOrDefaultAsync();
    }
    public Task<bool> IsReportScheduleNameExist(string reportName, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.ReportSchedules.Any(e => e.ReportName.Equals(reportName)))
            : Task.FromResult(
                _dbContext.ReportSchedules.Where(e => e.ReportName.Equals(reportName)).ToList().Unique(id));
    }

    public Task<bool> IsReportScheduleNameUnique(string reportName)
    {
        var matches = _dbContext.ReportSchedules.Any(e => e.ReportName.Equals(reportName));

        return Task.FromResult(matches);
    }

    public Task<List<ReportSchedule>> GetReportScheduleNames()
    {
        var reportSchedules = base
          .ListAllAsync(data => data.CompanyId.Equals(_loggedInUserService.CompanyId))
          .Select(x => new ReportSchedule { ReferenceId = x.ReferenceId, ReportName = x.ReportName })
          .OrderBy(x => x.ReportName);

        return reportSchedules.ToListAsync();
    }

    public async Task<List<ReportSchedule>>GetReportSchedulerByUserGroupId(string userGroupId)
    {
        return await _dbContext.ReportSchedules.Active().Where(e => e.UserProperties.Contains(userGroupId)).ToListAsync();
    }
}