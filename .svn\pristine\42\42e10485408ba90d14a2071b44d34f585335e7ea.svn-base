using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RoboCopyJobRepositoryTests : IClassFixture<RoboCopyJobFixture>
{
    private readonly RoboCopyJobRepository _repository;
    private readonly RoboCopyJobFixture _fixture;

    public RoboCopyJobRepositoryTests(RoboCopyJobFixture fixture)
    {
        _fixture = fixture;
        var loggedInUserService = DbContextFactory.GetMockUserService();
        _repository = new RoboCopyJobRepository(_fixture.DbContext, loggedInUserService);
    }

    private async Task ClearDatabase()
    {
        _fixture.DbContext.RoboCopyJobs.RemoveRange(_fixture.DbContext.RoboCopyJobs);
        await _fixture.DbContext.SaveChangesAsync();
    }


    [Fact]
    public void Constructor_ShouldInitializeRepository_WithValidParameters()
    {
        // Arrange & Act
        var loggedInUserService = DbContextFactory.GetMockUserService();
        var repository = new RoboCopyJobRepository(_fixture.DbContext, loggedInUserService);

        // Assert
        Assert.NotNull(repository);
        Assert.IsAssignableFrom<RoboCopyJobRepository>(repository);
    }

    [Fact]
    public void Constructor_ShouldThrowArgumentNullException_WhenDbContextIsNull()
    {
        // Arrange
        var loggedInUserService = DbContextFactory.GetMockUserService();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => new RoboCopyJobRepository(null, loggedInUserService));
    }




    
}
