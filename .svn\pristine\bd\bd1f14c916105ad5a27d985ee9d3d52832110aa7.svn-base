﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.Report.Queries.GetSchedulerWorkflowActionResultsReport
{
    public class GetSchedulerWorkflowActionResultsQueryHandler : IRequestHandler<GetSchedulerWorkflowActionResultsQuery, SchedulerWorkflowActionResultsVm>
    {
        private readonly ILoggedInUserService _loggedInUserService;
        private readonly IPublisher _publisher;
        private readonly ILogger<GetSchedulerWorkflowActionResultsQueryHandler> _logger;
        private readonly ISchedulerWorkflowActionResultsRepository _schedulerWorkflowActionResultsRepository;
        private readonly IMapper _mapper;
        public GetSchedulerWorkflowActionResultsQueryHandler(ILoggedInUserService loggedInUserService, IPublisher publisher, 
            ILogger<GetSchedulerWorkflowActionResultsQueryHandler> logger, ISchedulerWorkflowActionResultsRepository schedulerWorkflowActionResultsRepository, IMapper mapper)
        {
            _loggedInUserService = loggedInUserService;
            _publisher = publisher;
            _logger = logger;
            _schedulerWorkflowActionResultsRepository = schedulerWorkflowActionResultsRepository;
            _mapper = mapper;
        }
        public async Task<SchedulerWorkflowActionResultsVm> Handle(GetSchedulerWorkflowActionResultsQuery request, CancellationToken cancellationToken)
        {
            var schedulerWorkflowActionList = await _schedulerWorkflowActionResultsRepository.GetSchedulerWorkflowActionResultListByWorkflowId(request.WorkflowId, request.InfraObjectId);
            if (schedulerWorkflowActionList.Count == 0) throw new InvalidException("No Data Found");
            var schedulerWorkflowActionMapped = _mapper.Map<List<ScheduleWorkflowActionResultsReportVm>>(schedulerWorkflowActionList);
            schedulerWorkflowActionMapped.ForEach(job =>
            {
                job.TotalTime = GetTotalTime(job.StartTime, job.EndTime);
            });
            return new SchedulerWorkflowActionResultsVm
            {
                ReportGeneratedBy = _loggedInUserService.LoginName.ToString(),
                ReportGeneratedTime = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss"),
                ScheduleWorkflowActionResultsReportVms = schedulerWorkflowActionMapped
            };
        }
        private static TimeSpan GetTotalTime(string startDate, string endDate)
        {
            try
            {
                var startTime = DateTime.Parse(startDate);
                var endTime = DateTime.Parse(endDate);
                var totalTime = endTime - startTime;
                return totalTime;
            }
            catch(Exception ex)
            {
                return TimeSpan.Zero;
            }
        }
    }
}
