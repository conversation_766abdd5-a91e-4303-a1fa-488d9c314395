﻿using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Events.DerivedLicenseEvent.Update;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Update;

public class
    UpdateDerivedLicenseCommandHandler : IRequestHandler<UpdateDerivedLicenseCommand, UpdateDerivedLicenseResponse>
{
    private readonly ILicenseHistoryRepository _licenseHistoryRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ILogger<UpdateBaseLicenseCommandHandler> _logger;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateDerivedLicenseCommandHandler(ILicenseManagerRepository licenseManagerRepository, IMapper mapper,
        ILicenseHistoryRepository licenseHistoryRepository, ILoggedInUserService loggedInUserService,
        IPublisher publisher, ILogger<UpdateBaseLicenseCommandHandler> logger)
    {
        _licenseManagerRepository = licenseManagerRepository;
        _mapper = mapper;
        _licenseHistoryRepository = licenseHistoryRepository;
        _loggedInUserService = loggedInUserService;
        _publisher = publisher;
        _logger = logger;
    }

    public async Task<UpdateDerivedLicenseResponse> Handle(UpdateDerivedLicenseCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _licenseManagerRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(eventToUpdate, nameof(Domain.Entities.LicenseManager),
            new NotFoundException(nameof(Domain.Entities.LicenseManager), request.Id));

        var derivedLicense = SecurityHelper.Decrypt(request.LicenseKey);

        var licenseList = derivedLicense.Split('*');

        var baseLicenseDtl =
            await _licenseManagerRepository.GetBaseLicenseDetailByDerivedLicenseDetailAsync(eventToUpdate.ParentId,
                SecurityHelper.Decrypt(eventToUpdate.ParentPoNumber));

        var derivedLicenseKey = string.Join("*", licenseList);

        var newChildRequest = new UpdateDerivedLicenseCommand
        {
            Id = request.Id,
            PONumber = eventToUpdate.PoNumber,
            CompanyId = request.CompanyId,
            CompanyName = request.CompanyName,
            CPHostName = SecurityHelper.Encrypt(baseLicenseDtl.HostName),
            IPAddress = SecurityHelper.Encrypt(baseLicenseDtl.IpAddress),
            MACAddress = SecurityHelper.Encrypt(baseLicenseDtl.MacAddress),
            Properties = SecurityHelper.Encrypt(request.Properties),
            Validity = SecurityHelper.Encrypt(baseLicenseDtl.Validity),
            ExpiryDate = SecurityHelper.Encrypt(baseLicenseDtl.ExpiryDate),
            ParentId = _loggedInUserService.CompanyId,
            IsParent = false,
            LicenseKey = SecurityHelper.Encrypt(derivedLicenseKey),
            ParentPONumber = SecurityHelper.Encrypt(baseLicenseDtl.PoNumber),
            IsState = baseLicenseDtl.IsState
        };
        _mapper.Map(newChildRequest, eventToUpdate, typeof(UpdateDerivedLicenseCommand),
            typeof(Domain.Entities.LicenseManager));

        await _licenseManagerRepository.UpdateAsync(eventToUpdate);

        await CreateLicenseHistory(eventToUpdate, _loggedInUserService.CompanyId, false);

        eventToUpdate.Properties = SecurityHelper.Encrypt(request.CommonBaseLicenseUpdateCommand.Properties);
        eventToUpdate.LicenseKey = request.CommonBaseLicenseUpdateCommand.LicenseKey;
        await _licenseManagerRepository.UpdateAsync(eventToUpdate);

        await CreateLicenseHistory(eventToUpdate, eventToUpdate.ParentId, _loggedInUserService.IsParent);

        var response = new UpdateDerivedLicenseResponse
        {
            LicenseId = newChildRequest.Id,

            Message = $"Derived License '{SecurityHelper.Decrypt(eventToUpdate.CompanyName)}' updated successfully."
        };
        await _publisher.Publish(new DerivedLicenseUpdatedEvent { PONumber = newChildRequest.PONumber },
            cancellationToken);

        return response;
    }

    private async Task CreateLicenseHistory(Domain.Entities.LicenseManager eventToUpdate, string parentCompanyId,
        bool isParent)
    {
        await _licenseHistoryRepository.AddAsync(new Domain.Entities.LicenseHistory
        {
            LicenseId = eventToUpdate.ReferenceId,
            PONumber = eventToUpdate.PoNumber,
            CompanyName = eventToUpdate.CompanyName,
            CompanyId = eventToUpdate.CompanyId,
            CPHostName = eventToUpdate.HostName,
            IPAddress = eventToUpdate.IpAddress,
            MACAddress = eventToUpdate.MacAddress,
            Properties = eventToUpdate.Properties,
            Validity = eventToUpdate.Validity,
            ExpiryDate = eventToUpdate.ExpiryDate,
            ParentId = parentCompanyId,
            IsParent = isParent,
            LicenseKey = eventToUpdate.LicenseKey,
            UpdaterId = _loggedInUserService.UserId,
            ParentPONumber = eventToUpdate.ParentPoNumber,
            IsState = eventToUpdate.IsState
        });
    }
}