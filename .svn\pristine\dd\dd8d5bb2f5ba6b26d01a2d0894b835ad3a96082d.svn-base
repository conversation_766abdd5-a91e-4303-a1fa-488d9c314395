﻿using ContinuityPatrol.Application.Features.BusinessService.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Create;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Update;
using ContinuityPatrol.Application.Features.DRCalendar.Events.PaginatedView;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Domain.ViewModels.DRCalendar;
using ContinuityPatrol.Domain.ViewModels.UserModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Helper;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class DRCalendarControllerTests
    {
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IBusinessServiceService> _mockBusinessService = new();
        private readonly Mock<IUserService> _mockUserService = new();
        private readonly Mock<IWorkflowProfileService> _mockProfileService = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<ILogger<DRCalendarController>> _mockLogger = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private DRCalendarController _controller;

        public DRCalendarControllerTests()
        {
            Initialize();
        }
        public void Initialize()
        {
            _controller = new DRCalendarController(
                _mockPublisher.Object,
                _mockMapper.Object,
                _mockLogger.Object,
                _mockDataProvider.Object
            );

            var httpContext = new DefaultHttpContext();
            httpContext.Session = new TestSession();
            _controller.ControllerContext.HttpContext = httpContext;
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");

            // Setup WebHelper.UserSession for tests that need it
            var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext);
            WebHelper.Configure(mockHttpContextAccessor.Object);

            WebHelper.UserSession = new UserSession
            {
                CompanyId = "test-company-123",
                LoggedUserId = "test-user-123"
            };
        }

        [Fact]
        public async Task List_ShouldReturnViewWithCorrectModel()
        {
            // Arrange
            var mockBusinessServiceList = new List<BusinessServiceListVm>
            {
                new BusinessServiceListVm { Id = "1", Name = "Service1" }
            };

            var mockUserList = new List<UserNameVm>
            {
                new UserNameVm { Id = "1", LoginName = "user1" },
                new UserNameVm { Id = "2", LoginName = "user2" }
            };

            var mockProfileList = new List<WorkflowProfileListVm>
            {
                new WorkflowProfileListVm { Id = "1", Name = "Profile1" }
            };

            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceList())
                .ReturnsAsync(mockBusinessServiceList);
            _mockDataProvider.Setup(dp => dp.User.GetUserNames())
                .ReturnsAsync(mockUserList);
            _mockDataProvider.Setup(dp => dp.WorkflowProfile.GetWorkflowProfileList())
                .ReturnsAsync(mockProfileList);

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            var model = result.Model as DrCalendarActivityViewModel;
            Assert.NotNull(model);
            Assert.Equal(mockBusinessServiceList, model.BusinessServices);
            Assert.NotNull(model.PaginatedUserList);
            Assert.Equal(2, model.PaginatedUserList.Data.Count);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<DrCalendarPaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_WithEmptyId_ShouldCreateAndRedirectToList()
        {
            // Arrange
            var viewModel = new DrCalendarActivityViewModel
            {
                ActivityName = "Test Activity",
                File = null
            };
            var createCommand = new CreateDrCalendarCommand();
            var response = new BaseResponse { Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateDrCalendarCommand>(It.IsAny<DrCalendarActivityViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.DRCalenderService.CreateAsync(It.IsAny<CreateDrCalendarCommand>()))
                .ReturnsAsync(response);

            // Mock Request.Form with empty id
            var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
                { "id", "" }
            });
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            Assert.Equal("test-company-123", viewModel.CompanyId); // Verify CompanyId was set
            _mockMapper.Verify(m => m.Map<CreateDrCalendarCommand>(It.IsAny<DrCalendarActivityViewModel>()), Times.Once);
            _mockDataProvider.Verify(dp => dp.DRCalenderService.CreateAsync(It.IsAny<CreateDrCalendarCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_WithFile_ShouldSetFileName()
        {
            // Arrange
            var mockFile = new Mock<IFormFile>();
            mockFile.Setup(f => f.FileName).Returns("test.pdf");
            mockFile.Setup(f => f.Length).Returns(1024);

            var viewModel = new DrCalendarActivityViewModel
            {
                ActivityName = "Test Activity",
                File = mockFile.Object
            };
            var createCommand = new CreateDrCalendarCommand();
            var response = new BaseResponse { Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateDrCalendarCommand>(It.IsAny<DrCalendarActivityViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.DRCalenderService.CreateAsync(It.IsAny<CreateDrCalendarCommand>()))
                .ReturnsAsync(response);

            // Mock Request.Form with empty id to trigger create path
            var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
                { "id", "" }
            });
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            Assert.Equal("test.pdf", viewModel.FileName); // Verify filename was set from the file
            Assert.Equal("test-company-123", viewModel.CompanyId); // Verify CompanyId was set

            // Verify the file was accessed
            mockFile.Verify(f => f.FileName, Times.AtLeastOnce);
        }

        [Fact]
        public async Task CreateOrUpdate_WithId_ShouldUpdateAndRedirectToList()
        {
            // Arrange
            var viewModel = new DrCalendarActivityViewModel
            {
                ActivityName = "Test Activity",
                File = null
            };
            var updateCommand = new UpdateDrCalendarCommand();
            var response = new BaseResponse { Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateDrCalendarCommand>(It.IsAny<DrCalendarActivityViewModel>()))
                .Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.DRCalenderService.UpdateAsync(It.IsAny<UpdateDrCalendarCommand>()))
                .ReturnsAsync(response);

            // Mock Request.Form with non-empty id to trigger update path
            var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
                { "id", "123" }
            });
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            Assert.Equal("test-company-123", viewModel.CompanyId); // Verify CompanyId was set
            _mockMapper.Verify(m => m.Map<UpdateDrCalendarCommand>(It.IsAny<DrCalendarActivityViewModel>()), Times.Once);
            _mockDataProvider.Verify(dp => dp.DRCalenderService.UpdateAsync(It.IsAny<UpdateDrCalendarCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidationException_ShouldRedirectToList()
        {
            // Arrange
            var viewModel = new DrCalendarActivityViewModel { ActivityName = "Test Activity" };
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("ActivityName", "Activity name is required"));
            var validationException = new ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<CreateDrCalendarCommand>(viewModel))
                .Returns(new CreateDrCalendarCommand());
            _mockDataProvider.Setup(dp => dp.DRCalenderService.CreateAsync(It.IsAny<CreateDrCalendarCommand>()))
                .ThrowsAsync(validationException);

            // Mock Request.Form
            var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
                { "id", "" }
            });
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_WithGeneralException_ShouldRedirectToList()
        {
            // Arrange
            var viewModel = new DrCalendarActivityViewModel { ActivityName = "Test Activity" };
            var exception = new Exception("Database error");

            _mockMapper.Setup(m => m.Map<CreateDrCalendarCommand>(viewModel))
                .Returns(new CreateDrCalendarCommand());
            _mockDataProvider.Setup(dp => dp.DRCalenderService.CreateAsync(It.IsAny<CreateDrCalendarCommand>()))
                .ThrowsAsync(exception);

            // Mock Request.Form
            var formCollection = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
                { "id", "" }
            });
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task GetUserList_ShouldReturnJsonResult_WithSuccess()
        {
            var mockUserList = new List<UserNameVm> {  };

            _mockDataProvider
                .Setup(x => x.User.GetUserNames())
                .ReturnsAsync(mockUserList);

            var result = await _controller.GetUserList();

            var jsonResult = result as JsonResult;
            var jsonData = jsonResult.Value as dynamic;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task Delete_ShouldReturnRedirectToList_WhenSuccessful()
        {
            // Arrange
            var id = "123";
            var response = new BaseResponse { Message = "Deleted successfully" };

            _mockDataProvider.Setup(dp => dp.DRCalenderService.DeleteAsync(id))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            _mockDataProvider.Verify(dp => dp.DRCalenderService.DeleteAsync(id), Times.Once);
        }

        [Fact]
        public async Task Delete_ShouldHandleException_AndRedirectToList()
        {
            // Arrange
            var id = "123";
            var exception = new Exception("Database error");

            _mockDataProvider.Setup(dp => dp.DRCalenderService.DeleteAsync(id))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task DrCalendarPaginationList_ShouldReturnJsonResult_WithSuccess()
        {
            // Arrange
            var query = new GetDrCalendarPaginatedListQuery();
            var mockData = new PaginatedResult<DrCalendarActivityListVm>
            {
                Data = new List<DrCalendarActivityListVm>
                {
                    new DrCalendarActivityListVm { Id = "1", ActivityName = "Activity1" }
                }
            };

            _mockDataProvider.Setup(dp => dp.DRCalenderService.GetPaginatedDrCalendar(query))
                .ReturnsAsync(mockData);

            // Act
            var result = await _controller.DrCalendarPaginationList(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            _mockDataProvider.Verify(dp => dp.DRCalenderService.GetPaginatedDrCalendar(query), Times.Once);
        }

        [Fact]
        public async Task DrCalendarPaginationList_ShouldHandleException()
        {
            // Arrange
            var query = new GetDrCalendarPaginatedListQuery();
            var exception = new Exception("Database error");

            _mockDataProvider.Setup(dp => dp.DRCalenderService.GetPaginatedDrCalendar(query))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.DrCalendarPaginationList(query);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task GetUserList_ShouldHandleException()
        {
            var exceptionMessage = "Test exception";
            _mockDataProvider
                .Setup(x => x.User.GetUserNames())
                .ThrowsAsync(new Exception(exceptionMessage));

            var result = await _controller.GetUserList();

            Assert.NotNull(result);
        }

        [Fact]
        public async Task IsActivityNameExist_ShouldReturnTrue_IfExists()
        {
            var activityName = "TestActivity";
            var id = "1";
            var scheduleStartTime = DateTime.UtcNow;
            _mockDataProvider
                .Setup(x => x.DRCalenderService.IsDrCalendarNameExist(activityName, id, scheduleStartTime))
                .ReturnsAsync(true);

            var result = await _controller.IsActivityNameExist(activityName, id, scheduleStartTime);

            Assert.True(result );
        }

        [Fact]
        public async Task IsActivityNameExist_ShouldReturnFalse_OnException()
        {
            var activityName = "TestActivity";
            var id = "1";
            var scheduleStartTime = DateTime.UtcNow;
            _mockDataProvider
                .Setup(x => x.DRCalenderService.IsDrCalendarNameExist(activityName, id, scheduleStartTime))
                .ThrowsAsync(new Exception("Test exception"));

            var result = await _controller.IsActivityNameExist(activityName, id, scheduleStartTime);

            Assert.False (result);
            
        }

        [Fact]
        public void DownloadFile_ShouldReturnBase64Content()
        {
            // Arrange
            var fileName = "test.txt";
            var testContent = "Test file content";
            var testBytes = System.Text.Encoding.UTF8.GetBytes(testContent);
            var expectedBase64 = Convert.ToBase64String(testBytes);

            // Create a temporary file for testing
            var tempDir = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "DRCalenderActivityDoc");
            Directory.CreateDirectory(tempDir);
            var filePath = Path.Combine(tempDir, fileName);
            File.WriteAllBytes(filePath, testBytes);

            try
            {
                // Act
                var result = _controller.DownloadFile(fileName);

                // Assert
                Assert.NotNull(result);
                Assert.IsType<ContentResult>(result);
                Assert.Equal(expectedBase64, result.Content);
            }
            finally
            {
                // Cleanup
                if (File.Exists(filePath))
                    File.Delete(filePath);
            }
        }

        [Fact]
        public async Task GetBusinessService_ShouldReturnJsonResult_WithSuccess()
        {
            var mockBusinessServiceList = new List<BusinessServiceListVm>
            {
                new BusinessServiceListVm { Id = "1", Name = "Service1" }
            };

            _mockDataProvider
                .Setup(x => x.BusinessService.GetBusinessServiceList())
                .ReturnsAsync(mockBusinessServiceList);

            var result = await _controller.GetBusinessService(new GetBusinessServicePaginatedListQuery());

            var jsonResult = result as JsonResult;
            var jsonData = jsonResult.Value as dynamic;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetBusinessService_ShouldHandleException()
        {
            // Arrange
            var exception = new Exception("Database error");
            _mockDataProvider
                .Setup(x => x.BusinessService.GetBusinessServiceList())
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.GetBusinessService(new GetBusinessServicePaginatedListQuery());

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task GetProfileNamesByBusinessServiceId_ShouldReturnFilteredProfiles()
        {
            var businessServiceId = "123";
            var mockWorkflowProfileList = new List<WorkflowProfileInfoNameVm>
        {
            new WorkflowProfileInfoNameVm { BusinessServiceId = "123", ProfileName = "Profile1" },
            new WorkflowProfileInfoNameVm { BusinessServiceId = "456", ProfileName = "Profile2" }
        };

            _mockDataProvider
                .Setup(x => x.WorkflowProfileInfo.WorkflowProfileInfoNames())
                .ReturnsAsync(mockWorkflowProfileList);

            var result = await _controller.GetProfileNamesByBusinessServiceId(businessServiceId);

            var jsonResult = result as JsonResult;
            var jsonData = jsonResult.Value as dynamic;
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetProfileNamesByBusinessServiceId_ShouldHandleException()
        {
            // Arrange
            var businessServiceId = "123";
            var exception = new Exception("Database error");

            _mockDataProvider
                .Setup(x => x.WorkflowProfileInfo.WorkflowProfileInfoNames())
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.GetProfileNamesByBusinessServiceId(businessServiceId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
        }
    }
}