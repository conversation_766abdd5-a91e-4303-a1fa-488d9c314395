﻿using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetList;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class MssqlAlwaysOnMonitorLogsController : CommonBaseController
{
    [HttpPost]
    [Authorize(Policy = Permissions.Manage.Create)]
    public async Task<ActionResult<CreateMSSQLAlwaysOnMonitorLogResponse>> CreateMSSQLAlwaysOnMonitorLog([FromBody] CreateMSSQLAlwaysOnMonitorLogCommand createMssqlAlwaysOnMonitorLogCommand)
    {
        Logger.LogDebug($"Create MSSQLAlwaysOn Monitor Log  '{createMssqlAlwaysOnMonitorLogCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateMSSQLAlwaysOnMonitorLog), await Mediator.Send(createMssqlAlwaysOnMonitorLogCommand));
    }

    [HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<MssqlAlwaysOnMonitorLogsListVm>>> GetAllMSSQLAlwaysOnMonitorLogs()
    {
        Logger.LogDebug("Get All MSSQLAlwaysOn Monitor Logs");

        return Ok(await Mediator.Send(new GetMSSQLAlwaysOnMonitorLogsListQuery()));
    }
    [HttpGet("{id}")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<MSSQLAlwaysOnMonitorLogsDetailVm>> GetMSSQLAlwaysOnMonitorLogsById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "MSSQLAlwaysOn MonitorLogs Detail By Id");

        Logger.LogDebug($"Get MSSQLAlwaysOn Monitor logs Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetMSSQLAlwaysOnMonitorLogsDetailQuery { Id = id }));
    }
    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<PaginatedResult<MssqlAlwaysOnMonitorLogsListVm>>> GetPaginatedMSSQLAlwaysOnMonitorLogs([FromQuery] GetMSSQLAlwaysOnMonitorLogsPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in MSSQLAlwaysOn MonitorLogs Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet("type")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<MSSQLAlwaysOnMonitorLogsDetailByTypeVm>> GetMSSQLAlwaysOnMonitorLogsByType(string type)
    {
        Guard.Against.NullOrEmpty(type, "MSSQLAlwaysOn MonitorLogs Detail By Type");

        Logger.LogDebug($"Get MSSQLAlwaysOn Monitor logs Detail by Id '{type}'");

        return Ok(await Mediator.Send(new GetMSSQLAlwaysOnMonitorLogsDetailByTypeQuery { Type = type }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = new string[] { ApplicationConstants.Cache.AllMSSQLAlwaysOnMonitorLogsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllMSSQLAlwaysOnMonitorLogsNameCacheKey };

        ClearCache(cacheKeys);
    }

}