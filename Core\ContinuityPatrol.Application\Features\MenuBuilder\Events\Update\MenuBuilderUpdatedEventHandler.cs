using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.MenuBuilder.Events.Update;

public class MenuBuilderUpdatedEventHandler : INotificationHandler<MenuBuilderUpdatedEvent>
{
    private readonly ILogger<MenuBuilderUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public MenuBuilderUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<MenuBuilderUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(MenuBuilderUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} MenuBuilder",
            Entity = "MenuBuilder",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"MenuBuilder '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"MenuBuilder '{updatedEvent.Name}' updated successfully.");
    }
}
