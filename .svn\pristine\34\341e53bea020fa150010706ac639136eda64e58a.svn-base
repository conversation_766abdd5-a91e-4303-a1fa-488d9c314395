﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class SvcMssqlMonitorLogFilterSpecification : Specification<SVCMssqlMonitorLog>
{
    public SvcMssqlMonitorLogFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("type=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Type.Contains(stringItem.Replace("type=", "", StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("workflowname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.WorkflowName.Contains(stringItem.Replace("workflowname=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("properties=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Properties.Contains(stringItem.Replace("properties=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("infraobjectname=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.InfraObjectName.Contains(stringItem.Replace("infraobjectname=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.Type.Contains(searchString) || p.InfraObjectName.Contains(searchString) ||
                    p.Properties.Contains(searchString) || p.WorkflowName.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.Type != null;
        }
    }
}