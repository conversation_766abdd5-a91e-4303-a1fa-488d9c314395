using AutoFixture;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class InfraDashboardViewFixture : IDisposable
{
    public List<InfraDashboardView> InfraDashboardViewPaginationList { get; set; }
    public List<InfraDashboardView> InfraDashboardViewList { get; set; }
    public InfraDashboardView InfraDashboardViewDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "BS_123";
    public const string BusinessFunctionId = "BF_123";
    public const string InfraObjectId = "INFRA_123";

    public ApplicationDbContext DbContext { get; private set; }

    public InfraDashboardViewFixture()
    {
        var fixture = new Fixture();

        InfraDashboardViewList = fixture.Create<List<InfraDashboardView>>();

        InfraDashboardViewPaginationList = fixture.CreateMany<InfraDashboardView>(20).ToList();

        // Setup proper test data for InfraDashboardViewPaginationList
        InfraDashboardViewPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraDashboardViewPaginationList.ForEach(x => x.IsActive = true);
        InfraDashboardViewPaginationList.ForEach(x => x.CompanyId = CompanyId);
        InfraDashboardViewPaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        InfraDashboardViewPaginationList.ForEach(x => x.BusinessFunctionId = BusinessFunctionId);
        InfraDashboardViewPaginationList.ForEach(x => x.DRReady = true);

        // Setup proper test data for InfraDashboardViewList
        InfraDashboardViewList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraDashboardViewList.ForEach(x => x.IsActive = true);
        InfraDashboardViewList.ForEach(x => x.CompanyId = CompanyId);
        InfraDashboardViewList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        InfraDashboardViewList.ForEach(x => x.BusinessFunctionId = BusinessFunctionId);
        InfraDashboardViewList.ForEach(x => x.DRReady = true);

        InfraDashboardViewDto = fixture.Create<InfraDashboardView>();
        InfraDashboardViewDto.ReferenceId = InfraObjectId;
        InfraDashboardViewDto.IsActive = true;
        InfraDashboardViewDto.CompanyId = CompanyId;
        InfraDashboardViewDto.BusinessServiceId = BusinessServiceId;
        InfraDashboardViewDto.BusinessServiceName = "Test Business Service";
        InfraDashboardViewDto.BusinessFunctionId = BusinessFunctionId;
        InfraDashboardViewDto.BusinessFunctionName = "Test Business Function";
        InfraDashboardViewDto.Name = "Test Infrastructure Object";
        InfraDashboardViewDto.Description = "Test infrastructure description";
        InfraDashboardViewDto.Status = "Online";
        InfraDashboardViewDto.DRReady = true;
        InfraDashboardViewDto.DROperationStatus = 1;
        InfraDashboardViewDto.ReplicationStatus = 1;
        InfraDashboardViewDto.ConfiguredRTO = "60";
        InfraDashboardViewDto.ConfiguredRPO = "30";
        InfraDashboardViewDto.CurrentRTO = "45";
        InfraDashboardViewDto.CurrentRPO = "25";
        InfraDashboardViewDto.DataLagValue = "5";
        InfraDashboardViewDto.IsAffected = false;
        InfraDashboardViewDto.Priority = 1;
        InfraDashboardViewDto.Type = 1;
        InfraDashboardViewDto.TypeName = "Server";
        InfraDashboardViewDto.SubType = "Windows Server";
        InfraDashboardViewDto.State = "Active";
        InfraDashboardViewDto.MonitorType = "Automatic";
        InfraDashboardViewDto.EntityId = "ENTITY_123";
        InfraDashboardViewDto.ReplicationTypeId = "RT_123";
        InfraDashboardViewDto.ReplicationTypeName = "Database Replication";
        InfraDashboardViewDto.ReplicationCategoryTypeId = "RCT_123";
        InfraDashboardViewDto.ReplicationCategoryType = "Synchronous";
        InfraDashboardViewDto.IsDRReady = true;
        InfraDashboardViewDto.IsDrift = false;
        InfraDashboardViewDto.IsPartial = false;
        InfraDashboardViewDto.WorkflowIsRunning = false;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
