﻿using ContinuityPatrol.Application.Features.BackUp.Events.Execute;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BackUp.Commands.Execute;

public class BackUpExecuteCommandHandler : IRequestHandler<BackUpExecuteCommand, BackUpExecuteResponse>
{
    private readonly IBackUpLogRepository _backUpLogRepository;
    private readonly IBackUpRepository _backUpRepository;
    private readonly IJobScheduler _client;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;
    private readonly IPublisher _publisher;
    private readonly IWindowsService _windowsService;

    public BackUpExecuteCommandHandler(IBackUpRepository backUpRepository,
        ILoadBalancerRepository nodeConfigurationRepository, IWindowsService windowsService,
        IBackUpLogRepository backUpLogRepository, IPublisher publisher, IJobScheduler client)
    {
        _backUpRepository = backUpRepository;
        _windowsService = windowsService;
        _nodeConfigurationRepository = nodeConfigurationRepository;
        _backUpLogRepository = backUpLogRepository;
        _publisher = publisher;
        _client = client;
    }

    public async Task<BackUpExecuteResponse> Handle(BackUpExecuteCommand request, CancellationToken cancellationToken)
    {
        var nodeConfig =
            await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL",ServiceType.LoadBalancer.ToString())
            ?? await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(), ServiceType.LoadBalancer.ToString());


        if (nodeConfig is null) throw new InvalidException("Load Balancer is not configured!.");
        var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

        var monitorUrl = UrlHelper.GenerateMonitorCheckUrl(nodeConfig.TypeCategory, baseUrl); 

        var monitorResponse = await _windowsService.CheckWindowsService(monitorUrl);

        if (!monitorResponse.Success) throw new WindowServiceException(monitorResponse.InActiveNodes, ServiceType.Monitor.ToString(),monitorResponse.Message);
        
        var eventToUpdate = await _backUpRepository.GetByReferenceIdAsync(request.Id);

        var backUpLog = new Domain.Entities.BackUpLog
        {
            HostName = eventToUpdate.HostName,
            DatabaseName = eventToUpdate.DatabaseName,
            UserName = eventToUpdate.UserName,
            IsLocalServer = eventToUpdate.IsLocalServer,
            IsBackUpServer = eventToUpdate.IsBackUpServer,
            Type = eventToUpdate.BackUpType,
            Status = "Pending"
        };

        backUpLog = await _backUpLogRepository.AddAsync(backUpLog);

        await _publisher.Publish(new BackUpExecutedEvent { BackUpName = backUpLog.UserName }, cancellationToken);

        var url = UrlHelper.GenerateBackUptUrl(nodeConfig.TypeCategory, baseUrl, backUpLog.ReferenceId); 

        
        await _client.ScheduleJob(backUpLog.ReferenceId, new Dictionary<string, string> { ["url"] = url });

        var response = new BackUpExecuteResponse
        {
            Message = "Backup request sent successfully"
            //Id = eventToUpdate.ReferenceId
        };
        
        return response;
    }
}