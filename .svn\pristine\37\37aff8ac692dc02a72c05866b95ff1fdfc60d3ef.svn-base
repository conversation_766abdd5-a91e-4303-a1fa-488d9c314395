using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class DriftParameterFilterSpecification : Specification<DriftParameter>
{
    public DriftParameterFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.Name != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("categoryid=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.DriftCategoryId.Contains(stringItem.Replace("categoryid=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("categoryname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.DriftCategoryName.Contains(stringItem.Replace("categoryname=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("impacttypeid=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.DriftImpactTypeId.Contains(stringItem.Replace("impacttypeid=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("impacttypename=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.DriftImpactTypeName.Contains(stringItem.Replace("impacttypename=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("properties=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Properties.Contains(stringItem.Replace("properties=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p => p.Name.Contains(searchString) || p.DriftCategoryId.Contains(searchString) ||
                                p.DriftCategoryName.Contains(searchString) ||
                                p.DriftImpactTypeId.Contains(searchString) ||
                                p.DriftImpactTypeName.Contains(searchString) || p.Properties.Contains(searchString);
            }
        }
    }
}