﻿using ContinuityPatrol.Shared.Core.Behaviors;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Settings;
using ContinuityPatrol.Shared.Infrastructure.HealthChecks;
using ContinuityPatrol.Shared.Infrastructure.Identity;
using ContinuityPatrol.Shared.Infrastructure.VersionManager;

namespace ContinuityPatrol.Shared.Infrastructure.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddSharedWebInfrastructure(this IServiceCollection services, IConfiguration config)
    {
        services.Configure<CacheSettings>(config.GetSection(nameof(CacheSettings)));
        services.Configure<JwtSettings>(config.GetSection(nameof(JwtSettings)));
        services.AddScoped<ILoggedInUserService, LoggedInUserService>();
        services.AddTransient<ITokenManager, TokenManager>();
        services.AddScoped<IVersionManager, CreateVersion>();
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));

        
        services.AddLazyCache(_ =>
        {
            var cache = new CachingService(CachingService.DefaultCacheProvider)
            {
                DefaultCachePolicy = { DefaultCacheDurationSeconds = 36000 }
            };
            return cache;
        });
        
        services.AddScoped<IAuthorizationHandler, PermissionAuthorizationHandler>();

        services.AddAuthorization(options =>
        {
            Permissions.All().ForEach(permission =>
            {
                options.AddPolicy(permission,
                    builder => { builder.AddRequirements(new PermissionRequirement(permission)); });
            });
            // The rest omitted for brevity.
        });

        services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
            .AddCookie(m =>
            {
                m.LoginPath = "/Account/Login";
                m.LogoutPath = "/Account/Logout";
                m.AccessDeniedPath = "/Account/Logout";
                m.Cookie.Name = "__Host-Identity";
                m.SlidingExpiration = true;
            });

        return services;
    }

    public static IServiceCollection AddHealthCheck(this IServiceCollection services, IConfiguration config)
    {

        var provider = config.GetConnectionString("DBProvider");
        var providerDecrypt = CryptographyHelper.Decrypt(provider);
        var connectionString = config.GetConnectionString("Default");
        var connectionStringDecrypt = CryptographyHelper.Decrypt(connectionString);
        var seqUrl = config["SeqConfig:ServerUrl"];
        var signalRUrl = config["SignalR:Url"];


        var healthChecksBuilder = services.AddHealthChecks();

        healthChecksBuilder.AddCheck("Application", () =>
            HealthCheckResult.Healthy("The application is running."), tags: new[] { "app" });

        switch (providerDecrypt.ToLower())
        {
            case "mysql":
                healthChecksBuilder
                     .AddMySql(connectionStringDecrypt, 
                        name: "MySql Health Check",
                    tags: new[] { "mysql" });
                break;
            case "mssql":
                healthChecksBuilder
                     .AddSqlServer(connectionStringDecrypt, 
                        name: "MSSQL Health Check", 
                        tags: new[] { "mssql" });
                break;
            case "oracle":
                healthChecksBuilder
                     .AddOracle(connectionStringDecrypt, 
                        name: "Oracle Health Check", 
                        tags: new[] { "oracle" });
                break;

            case "postgres":
                healthChecksBuilder
                     .AddNpgSql(connectionStringDecrypt, 
                        name: "Postgres Health Check", 
                        tags: new[] { "postgres" });
                break;
            default:
                healthChecksBuilder
                    .AddMySql(connectionStringDecrypt,
                        name: "MySql Health Check",
                        tags: new[] { "mysql" });
                break;

        }

        healthChecksBuilder
            .AddUrlGroup(
                new Uri($"{seqUrl}/health"),
                name: "Seq Health Check",
                tags: new[] { "seq" });


        services.AddHealthChecks()
            .AddCheck("SignalR WorkflowHub",
                new SignalRHubHealthCheck($"{signalRUrl}workflowhub"),
                tags: new[] { "signalr" });


        services.AddHealthChecks()
            .AddCheck("SignalR AlertHub",
                new SignalRHubHealthCheck($"{signalRUrl}alerthub"),
                tags: new[] { "signalr" });


        services.AddHealthChecks()
            .AddCheck("SignalR WorkflowActionResultHub",
                new SignalRHubHealthCheck($"{signalRUrl}workflowactionresulthub"),
                tags: new[] { "signalr" });


        services.AddHealthChecks()
            .AddCheck("SignalR LogHub",
                new SignalRHubHealthCheck($"{signalRUrl}loghub"),
                tags: new[] { "signalr" });


        services.AddHealthChecks()
            .AddCheck("SignalR NotificationHub",
                new SignalRHubHealthCheck($"{signalRUrl}notificationhub"),
                tags: new[] { "signalr" });

        return services;
    }



    public static IServiceCollection AddDatabaseContext<T>(this IServiceCollection services, IConfiguration config)
     where T : DbContext
    {
        var provider = config.GetConnectionString("DBProvider");
        var providerDecrypt = CryptographyHelper.Decrypt(provider);
        var connectionString = config.GetConnectionString("Default");
        var connectionStringDecrypt = CryptographyHelper.Decrypt(connectionString);
        var serverVersion = new MySqlServerVersion(new Version(8, 0, 13));
        var commandTimeout = config.GetValue<int>("DatabaseSettings:CommandTimeoutSeconds");

        try
        {
            switch (providerDecrypt.ToLower())
            {
                case "mysql":
                   
                    services.AddDbContext<T>(options => options.UseMySql(connectionStringDecrypt, serverVersion,
                        sqlOptions =>
                        {
                            sqlOptions.EnableRetryOnFailure(
                                5,
                                TimeSpan.FromSeconds(30),
                                null);

                             sqlOptions.CommandTimeout(commandTimeout);
                        }));
                    break;

                case "mssql":
                   
                        services.AddDbContext<T>(options => options.UseSqlServer(connectionStringDecrypt, sqlOptions =>
                        {
                            sqlOptions.EnableRetryOnFailure(
                                5,
                                TimeSpan.FromSeconds(30),
                                null);

                            sqlOptions.CommandTimeout(commandTimeout);
                        }));
                        break;
                
                case "oracle":

                    services.AddDbContext<T>(options => options.UseOracle(connectionStringDecrypt, sqlOptions =>
                    {
                        sqlOptions.UseOracleSQLCompatibility(OracleSQLCompatibility.DatabaseVersion19);
                        //sqlOptions.UseOracleSQLCompatibility(OracleSQLCompatibility.DatabaseVersion21);
                        //sqlOptions.UseOracleSQLCompatibility(OracleSQLCompatibility.DatabaseVersion23);
                        sqlOptions.CommandTimeout(commandTimeout);
                    }));

                    break;

                case "postgres":
                    services.AddDbContext<T>(options =>
                        options.UseNpgsql(connectionStringDecrypt, npgsqlOptionsAction: npgsqlOptions =>
                        {
                            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
                            npgsqlOptions.EnableRetryOnFailure(
                                maxRetryCount: 3,
                                maxRetryDelay: TimeSpan.FromSeconds(10),
                                errorCodesToAdd: null);

                            npgsqlOptions.CommandTimeout(commandTimeout);

                        }));

                    break;

                default:
                    services.AddDbContext<T>(options => options.UseMySql(connectionStringDecrypt, serverVersion,
                        sqlOptions =>
                        {
                            sqlOptions.EnableRetryOnFailure(
                                3,
                                TimeSpan.FromSeconds(10),
                                null);

                            sqlOptions.CommandTimeout(commandTimeout);
                        }));
                    break;
            }
        }catch(Exception ex)
        {
           Log.Error($"Error in AddDatabaseContext : {ex.GetMessage()}");
        }
        return services;
    }
}