﻿using ContinuityPatrol.Application.Features.FormType.Commands.Create;
using ContinuityPatrol.Application.Features.FormType.Commands.Update;
using ContinuityPatrol.Application.Features.FormType.Events.PaginatedView;
using ContinuityPatrol.Application.Features.FormType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.FormTypeModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class FormTypeController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly ILogger<FormTypeController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;

    public FormTypeController(IPublisher publisher, ILogger<FormTypeController> logger, IMapper mapper, IDataProvider dataProvider)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }

    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in FormType");
        await _publisher.Publish(new FormTypePaginatedViewEvent());
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(FormTypeViewModel formType)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in FormType");
        var formTypeId = Request.Form["id"].ToString();
        try
        {
            if (formTypeId.IsNullOrWhiteSpace())
            {
                formType.IsDelete = true;
                var formTypeCreateCommand = _mapper.Map<CreateFormTypeCommand>(formType);
                _logger.LogDebug($"Creating FormType '{formTypeCreateCommand.FormTypeName}'.");
                var response = await _dataProvider.FormType.CreateAsync(formTypeCreateCommand);
                _logger.LogDebug("Create operation completed successfully in FormType, returning view.");
                return Json(new { success = true, data = response });

            }
            else
            {
                formType.IsDelete = true;
                var formTypeUpdateCommand = _mapper.Map<UpdateFormTypeCommand>(formType);
                _logger.LogDebug($"Updating FormType '{formTypeUpdateCommand.FormTypeName}'.");
                var response = await _dataProvider.FormType.UpdateAsync(formTypeUpdateCommand);
                _logger.LogDebug("Update operation completed successfully in FormType, returning view.");
                return Json(new { success = true, data = response });

            }
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation exception on form type page: {ex.ValidationErrors.FirstOrDefault()}");
            return ex.GetJsonException();
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on form type page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in FormType");
        try
        {
            var result = await _dataProvider.FormType.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted record in FormType");
            return Json(new { success = true, data = result });

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on type.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<bool> FormTypeNameExist(string formTypeName, string id)
    {
        _logger.LogDebug("Entering FormTypeNameExist method in FormType");
        try
        {
            _logger.LogDebug("Returning result for FormTypeNameExist on FormType");
            return await _dataProvider.FormType.IsFormTypeNameExist(formTypeName, id);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on form type page while checking if form type name exists for : {formTypeName}.", ex);

            return false;
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetFormTypePaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in FormType");
        try
        {
            var formTypeList = await _dataProvider.FormType.GetPaginatedFormTypes(query);
            _logger.LogDebug("Successfully retrieved form type  paginated list on FormType page");
            return Json(new { success = true, data = formTypeList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on form type page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetFormTypeNames()
    {
        _logger.LogDebug("Entering GetFormTypeNames method in FormType");
        try
        {
            var formTypeNames = await _dataProvider.FormType.GetFormTypeNames();
            _logger.LogDebug("Successfully retrieved form type names in FormType");
            return Json(new { success = true, data = formTypeNames });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on form type page while retrieving the formType names.", ex);
            return ex.GetJsonException();
        }
    }
}