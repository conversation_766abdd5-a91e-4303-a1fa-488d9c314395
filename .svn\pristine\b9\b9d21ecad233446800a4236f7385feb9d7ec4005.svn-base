using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Xunit;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowExecutionEventLogRepositoryTests : IClassFixture<WorkflowExecutionEventLogFixture>
    {
        private readonly WorkflowExecutionEventLogFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowExecutionEventLogRepository _repository;

        public WorkflowExecutionEventLogRepositoryTests(WorkflowExecutionEventLogFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _repository = new WorkflowExecutionEventLogRepository(_dbContext, DbContextFactory.GetMockUserService());
        }

        // No custom methods to test, but we can test instantiation and base repository behavior if needed.
        [Fact]
        public void CanInstantiateRepository()
        {
            Assert.NotNull(_repository);
        }
    }
}