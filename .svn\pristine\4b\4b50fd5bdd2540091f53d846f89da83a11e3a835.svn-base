﻿using ContinuityPatrol.Application.Constants;
using ContinuityPatrol.Application.Features.WorkflowProfile.Events.UpdatePassword;

namespace ContinuityPatrol.Application.Features.WorkflowProfile.Commands.UpdateWorkflowProfilePassword;

public class UpdateWorkflowProfilePasswordCommandHandler : IRequestHandler<UpdateWorkflowProfilePasswordCommand,
    UpdateWorkflowProfilePasswordResponse>
{
    private readonly IPublisher _publisher;
    private readonly IWorkflowProfileRepository _workflowProfileRepository;

    public UpdateWorkflowProfilePasswordCommandHandler(IPublisher publisher,
        IWorkflowProfileRepository workflowProfileRepository)
    {
        _publisher = publisher;
        _workflowProfileRepository = workflowProfileRepository;
    }

    public async Task<UpdateWorkflowProfilePasswordResponse> Handle(UpdateWorkflowProfilePasswordCommand request,
        CancellationToken cancellationToken)
    {
        var evenToUpdate = await _workflowProfileRepository.GetByReferenceIdAsync(request.Id);

        if (SecurityHelper.Decrypt(request.OldPassword) != SecurityHelper.Decrypt(evenToUpdate.Password))
            throw new InvalidPasswordException(Authentication.InvalidOldPassword);

        if (SecurityHelper.Decrypt(request.NewPassword) == SecurityHelper.Decrypt(evenToUpdate.Password))
            throw new InvalidPasswordException(Authentication.PasswordUnique);

        if (SecurityHelper.Decrypt(request.NewPassword) != SecurityHelper.Decrypt(request.ConfirmPassword))
            throw new InvalidPasswordException(Authentication.InvalidConfirmPassword);

        evenToUpdate.Password = request.NewPassword;

        await _workflowProfileRepository.UpdateAsync(evenToUpdate);

        var response = new UpdateWorkflowProfilePasswordResponse
        {
            Message = $"Workflow profile '{evenToUpdate.Name}' password has been updated successfully.",
            Id = evenToUpdate.ReferenceId
        };
        await _publisher.Publish(new UpdatePasswordEvent { Name = evenToUpdate.Name }, cancellationToken);

        return response;
    }
}