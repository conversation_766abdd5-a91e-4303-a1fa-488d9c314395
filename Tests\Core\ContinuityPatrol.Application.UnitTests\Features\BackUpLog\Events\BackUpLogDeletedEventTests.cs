using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BackUpLog.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUpLog.Events;

public class BackUpLogDeletedEventTests : IClassFixture<BackUpLogFixture>
{
    private readonly BackUpLogFixture _backUpLogFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Mock<ILogger<BackUpLogDeletedEventHandler>> _mockLogger;
    private readonly BackUpLogDeletedEventHandler _handler;

    public BackUpLogDeletedEventTests(BackUpLogFixture backUpLogFixture)
    {
        _backUpLogFixture = backUpLogFixture;
        _mockUserActivityRepository = BackUpLogRepositoryMocks.CreateUserActivityRepository(_backUpLogFixture.UserActivities);
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<BackUpLogDeletedEventHandler>>();

        // Setup logged in user service
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/backuplog");
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        _handler = new BackUpLogDeletedEventHandler(
            _mockLoggedInUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_When_BackUpLogDeletedEventReceived()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }


    [Fact]
    public async Task Handle_SetCorrectUserInfo_When_BackUpLogDeleted()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var loginName = "DeleteBackupUser";
        var requestUrl = "/api/v6/backuplog/delete";
        var ipAddress = "192.168.1.300";

        _mockLoggedInUserService.Setup(x => x.UserId).Returns(userId);
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns(loginName);
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(requestUrl);
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns(ipAddress);

        var deletedEvent = new BackUpLogDeletedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == userId &&
            ua.LoginName == loginName &&
            ua.RequestUrl == requestUrl &&
            ua.HostAddress == ipAddress)), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_BackUpLogDeleted()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "CustomDeletedDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUpLog 'CustomDeletedDatabase' deleted successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectEntityAndModule_When_BackUpLogDeleted()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        deletedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_SetCorrectActivityType_When_BackUpLogDeleted()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityType == ActivityType.Delete.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_LogInformation_When_BackUpLogDeleted()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_BackUpLogDeletedWithNullName()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = null };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUpLog '' deleted successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleEmptyEventName_When_BackUpLogDeletedWithEmptyName()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = string.Empty };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUpLog '' deleted successfully.")), Times.Once);
    }


    [Fact]
    public async Task Handle_CallRepositoryOnce_When_EventHandled()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        _mockUserActivityRepository.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_CreateEventWithComplexDatabaseName_When_BackUpLogDeleted()
    {
        // Arrange
        var complexDatabaseName = "Deleted_Complex_Production_Database_789";
        var deletedEvent = new BackUpLogDeletedEvent { Name = complexDatabaseName };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == $"BackUpLog '{complexDatabaseName}' deleted successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_VerifyEventType_When_BackUpLogDeleted()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        deletedEvent.ShouldBeOfType<BackUpLogDeletedEvent>();
        deletedEvent.ShouldBeAssignableTo<INotification>();
    }

    [Fact]
    public async Task Handle_DifferentiateFromOtherEvents_When_BackUpLogDeleted()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityType == ActivityType.Delete.ToString() &&
            ua.ActivityDetails.Contains("deleted") &&
            !ua.ActivityDetails.Contains("created") &&
            !ua.ActivityDetails.Contains("updated"))), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActionFormat_When_BackUpLogDeleted()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        deletedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_PerformSoftDelete_When_BackUpLogDeleted()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        // Verify that the event handler logs the soft delete operation
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails.Contains("deleted successfully") &&
            ua.ActivityType == ActivityType.Delete.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteEventForDifferentialBackup_When_DifferentialBackupDeleted()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "DeletedDifferentialDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUpLog 'DeletedDifferentialDatabase' deleted successfully." &&
            ua.ActivityType == ActivityType.Delete.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteEventForTransactionLogBackup_When_TransactionLogBackupDeleted()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "DeletedTransactionLogDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        deletedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_DeleteEventForFailedBackup_When_FailedBackupDeleted()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "DeletedFailedDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        deletedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_DeleteEventForRemoteBackup_When_RemoteBackupDeleted()
    {
        // Arrange
        var deletedEvent = new BackUpLogDeletedEvent { Name = "DeletedRemoteDatabase" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

    }
}
