﻿using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;

namespace ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetByEntity;

public class
    GetLicenseInfoByEntityListQueryHandler : IRequestHandler<GetLicenseInfoByEntityListQuery,
        List<LicenseInfoByEntityListVm>>
{
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly IMapper _mapper;
    private readonly IServerRepository _serverRepository;

    public GetLicenseInfoByEntityListQueryHandler(ILicenseInfoRepository licenseInfoRepository, IMapper mapper,
        IServerRepository serverRepository)
    {
        _licenseInfoRepository = licenseInfoRepository;
        _mapper = mapper;
        _serverRepository = serverRepository;
    }

    public async Task<List<LicenseInfoByEntityListVm>> Handle(GetLicenseInfoByEntityListQuery request,
        CancellationToken cancellationToken)
    {
        var licenseInfoList = request.EntityType.IsNotNullOrWhiteSpace()
            ? await _licenseInfoRepository.GetLicenseInfoByLicenseIdAndEntityAndEntityType(request.LicenseId,
                request.Entity, request.EntityType)
            : await _licenseInfoRepository.GetLicenseInfoByLicenseIdAndEntity(request.LicenseId, request.Entity);

        // var licenseInfoType= licenseInfoList.Where(x=>x.Type != null && !string.IsNullOrWhiteSpace(x.Type)).DistinctBy(x=>x.Type);

        var licenseInfoEntityVm = _mapper.Map<List<LicenseInfoByEntityListVm>>(licenseInfoList);

        foreach (var lic in licenseInfoEntityVm.Where(lic => lic.Entity.ToLower() == "server"))
        {
            var server = await _serverRepository.GetByReferenceIdAsync(lic.EntityId);

            if (server != null)
            {
                var hostName = GetJsonProperties.IsConnectionHostName(server.Properties);

                lic.HostName = hostName;
            }
        }


        //licenseInfoEntityVm.ForEach(licenseInfo =>
        //{
        //    var typeDtl = request.EntityType.IsNotNullOrWhiteSpace()
        //        ? _licenseInfoRepository.GetLicenseInfoByLicenseIdAndTypeAndEntityType(request.LicenseId, licenseInfo.Type, request.EntityType).Result
        //        : _licenseInfoRepository.GetLicenseInfoByLicenseIdAndType(request.LicenseId, licenseInfo.Type).Result;

        //    licenseInfo.LicenseInfoTypeListVms = _mapper.Map<List<LicenseInfoTypeListVm>>(typeDtl);

        //});

        return licenseInfoEntityVm;
    }
}