using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberComponentMappingModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CyberComponentMappingControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CyberComponentMappingsController _controller;
    private readonly CyberComponentMappingFixture _cyberComponentMappingFixture;

    public CyberComponentMappingControllerTests()
    {
        _cyberComponentMappingFixture = new CyberComponentMappingFixture();

        var testBuilder = new ControllerTestBuilder<CyberComponentMappingsController>();
        _controller = testBuilder.CreateController(
            _ => new CyberComponentMappingsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetCyberComponentMappings_ReturnsExpectedList()
    {
        // Arrange
        var expectedCyberComponentMappings = new List<CyberComponentMappingListVm>
        {
            _cyberComponentMappingFixture.CyberComponentMappingListVm,
            _cyberComponentMappingFixture.CyberComponentMappingListVm,
            _cyberComponentMappingFixture.CyberComponentMappingListVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberComponentMappingListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCyberComponentMappings);

        // Act
        var result = await _controller.GetCyberComponentMappings();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberComponentMappings = Assert.IsAssignableFrom<List<CyberComponentMappingListVm>>(okResult.Value);
        Assert.Equal(3, cyberComponentMappings.Count);
    }

    [Fact]
    public async Task GetCyberComponentMappingById_ReturnsExpectedDetail()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentMappingDetailQuery>(q => q.Id == cyberComponentMappingId), default))
            .ReturnsAsync(_cyberComponentMappingFixture.CyberComponentMappingDetailVm);

        // Act
        var result = await _controller.GetCyberComponentMappingById(cyberComponentMappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberComponentMapping = Assert.IsType<CyberComponentMappingDetailVm>(okResult.Value);
        Assert.Equal(_cyberComponentMappingFixture.CyberComponentMappingDetailVm.Name, cyberComponentMapping.Name);
    }

    [Fact]
    public async Task GetPaginatedCyberComponentMappings_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetCyberComponentMappingPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = new List<CyberComponentMappingListVm>
        {
            _cyberComponentMappingFixture.CyberComponentMappingListVm,
            _cyberComponentMappingFixture.CyberComponentMappingListVm
        };
        var expectedResults = PaginatedResult<CyberComponentMappingListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentMappingPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberComponentMappings(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberComponentMappingListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task CreateCyberComponentMapping_ReturnsCreatedAtAction()
    {
        // Arrange
        var command = _cyberComponentMappingFixture.CreateCyberComponentMappingCommand;
        var expectedMessage = "CyberComponentMapping has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberComponentMappingResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberComponentMapping(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberComponentMappingResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberComponentMapping_ReturnsOk()
    {
        // Arrange
        var command = _cyberComponentMappingFixture.UpdateCyberComponentMappingCommand;
        var expectedMessage = "CyberComponentMapping has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberComponentMappingResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberComponentMapping(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberComponentMappingResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteCyberComponentMapping_ReturnsOk()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();
        var expectedMessage = "CyberComponentMapping has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCyberComponentMappingCommand>(c => c.Id == cyberComponentMappingId), default))
            .ReturnsAsync(new DeleteCyberComponentMappingResponse
            {
                Message = expectedMessage,
                IsActive = false
            });

        // Act
        var result = await _controller.DeleteCyberComponentMapping(cyberComponentMappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteCyberComponentMappingResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public async Task IsCyberComponentMappingNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var mappingName = "Existing Component Mapping";
        var mappingId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentMappingNameUniqueQuery>(q => 
                q.Name == mappingName && q.Id == mappingId), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsCyberComponentMappingNameExist(mappingName, mappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.True(exists);
    }

    [Fact]
    public async Task IsCyberComponentMappingNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var mappingName = "Unique Component Mapping Name";
        var mappingId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentMappingNameUniqueQuery>(q => 
                q.Name == mappingName && q.Id == mappingId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsCyberComponentMappingNameExist(mappingName, mappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }

    [Fact]
    public async Task GetCyberComponentMappings_HandlesEmptyList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberComponentMappingListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<CyberComponentMappingListVm>());

        // Act
        var result = await _controller.GetCyberComponentMappings();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberComponentMappings = Assert.IsAssignableFrom<List<CyberComponentMappingListVm>>(okResult.Value);
        Assert.Empty(cyberComponentMappings);
    }

    [Fact]
    public async Task GetCyberComponentMappingById_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetCyberComponentMappingById(invalidId));
    }

    [Fact]
    public async Task GetCyberComponentMappingById_HandlesNotFound()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentMappingDetailQuery>(q => q.Id == cyberComponentMappingId), default))
            .ThrowsAsync(new NotFoundException("CyberComponentMapping", cyberComponentMappingId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetCyberComponentMappingById(cyberComponentMappingId));
    }

    [Fact]
    public async Task DeleteCyberComponentMapping_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteCyberComponentMapping(invalidId));
    }

    [Fact]
    public async Task CreateCyberComponentMapping_HandlesComplexMapping()
    {
        // Arrange
        var command = new CreateCyberComponentMappingCommand
        {
            Name = "Enterprise Multi-Tier Application Mapping",
            Properties = "{\"enterpriseMapping\":{\"sourceEnvironment\":{\"type\":\"Production\",\"location\":\"Primary Data Center\",\"infrastructure\":{\"compute\":{\"servers\":[{\"name\":\"APP-PROD-01\",\"type\":\"Application Server\",\"specs\":\"32 cores, 128GB RAM\",\"os\":\"RHEL 8.5\"},{\"name\":\"DB-PROD-01\",\"type\":\"Database Server\",\"specs\":\"64 cores, 512GB RAM\",\"os\":\"Oracle Linux 8\"}],\"virtualization\":\"VMware vSphere 7.0\",\"storage\":\"NetApp All-Flash FAS\"},\"network\":{\"vlan\":\"VLAN-100\",\"subnet\":\"*************/24\",\"firewall\":\"Palo Alto PA-5220\",\"loadBalancer\":\"F5 BIG-IP\"}}},\"targetEnvironment\":{\"type\":\"Disaster Recovery\",\"location\":\"Secondary Data Center\",\"infrastructure\":{\"compute\":{\"servers\":[{\"name\":\"APP-DR-01\",\"type\":\"Application Server\",\"specs\":\"32 cores, 128GB RAM\",\"os\":\"RHEL 8.5\"},{\"name\":\"DB-DR-01\",\"type\":\"Database Server\",\"specs\":\"64 cores, 512GB RAM\",\"os\":\"Oracle Linux 8\"}],\"virtualization\":\"VMware vSphere 7.0\",\"storage\":\"NetApp All-Flash FAS\"},\"network\":{\"vlan\":\"VLAN-200\",\"subnet\":\"**********/24\",\"firewall\":\"Palo Alto PA-5220\",\"loadBalancer\":\"F5 BIG-IP\"}}},\"mappingRules\":{\"applicationTier\":{\"sourcePort\":8080,\"targetPort\":8080,\"protocol\":\"HTTPS\",\"healthCheck\":\"/health\"},\"databaseTier\":{\"sourcePort\":1521,\"targetPort\":1521,\"protocol\":\"TCP\",\"replication\":\"Oracle Data Guard\"},\"dataFlow\":{\"direction\":\"bidirectional\",\"encryption\":\"TLS 1.3\",\"compression\":\"enabled\",\"bandwidth\":\"10Gbps\"}},\"failoverPlan\":{\"rto\":\"4 hours\",\"rpo\":\"15 minutes\",\"testSchedule\":\"quarterly\",\"automatedFailover\":true}}}",
            Version = "2.0.0"
        };

        var expectedMessage = "CyberComponentMapping has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberComponentMappingResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberComponentMapping(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberComponentMappingResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberComponentMapping_HandlesVersionUpgrade()
    {
        // Arrange
        var command = new UpdateCyberComponentMappingCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Updated Enterprise Component Mapping",
            Properties = "{\"updatedMapping\":{\"version\":\"3.0.0\",\"enhancements\":[\"improved_security\",\"enhanced_monitoring\",\"automated_failover\"],\"newFeatures\":{\"aiPoweredAnalytics\":true,\"predictiveFailover\":true,\"realTimeSync\":true}}}",
           
        };

        var expectedMessage = "CyberComponentMapping version upgraded successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberComponentMappingResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberComponentMapping(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberComponentMappingResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
       
    }

    [Fact]
    public async Task GetPaginatedCyberComponentMappings_HandlesComplexFiltering()
    {
        // Arrange
        var query = new GetCyberComponentMappingPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 15,
            SearchString = "Enterprise"
          
        };

        var expectedData = new List<CyberComponentMappingListVm>
        {
            new CyberComponentMappingListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Production Mapping",
                Properties = "{\"updatedMapping\":{\"version\":\"3.0.0\",\"enhancements\":[\"improved_security\",\"enhanced_monitoring\",\"automated_failover\"],\"newFeatures\":{\"aiPoweredAnalytics\":true,\"predictiveFailover\":true,\"realTimeSync\":true}}}"
            }
        };
        var expectedResults = PaginatedResult<CyberComponentMappingListVm>.Success(expectedData, 1, 1, 15);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentMappingPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber &&
                q.PageSize == query.PageSize &&
                q.SearchString == query.SearchString), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberComponentMappings(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberComponentMappingListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        Assert.Contains("Enterprise", paginatedResult.Data.First().Name);
        
    }

    [Fact]
    public async Task CreateCyberComponentMapping_HandlesNetworkMapping()
    {
        // Arrange
        var command = new CreateCyberComponentMappingCommand
        {
            Name = "Enterprise Network Infrastructure Mapping",
            Properties = "{\"networkMapping\":{\"sourceNetwork\":{\"datacenter\":\"Primary\",\"switches\":[{\"name\":\"SW-CORE-01\",\"type\":\"Core Switch\",\"model\":\"Cisco Nexus 9000\"},{\"name\":\"SW-ACCESS-01\",\"type\":\"Access Switch\",\"model\":\"Cisco Catalyst 9300\"}],\"routers\":[{\"name\":\"RTR-EDGE-01\",\"type\":\"Edge Router\",\"model\":\"Cisco ASR 1000\"}],\"firewalls\":[{\"name\":\"FW-PERIMETER-01\",\"type\":\"Perimeter Firewall\",\"model\":\"Palo Alto PA-5220\"}]},\"targetNetwork\":{\"datacenter\":\"Secondary\",\"switches\":[{\"name\":\"SW-CORE-02\",\"type\":\"Core Switch\",\"model\":\"Cisco Nexus 9000\"},{\"name\":\"SW-ACCESS-02\",\"type\":\"Access Switch\",\"model\":\"Cisco Catalyst 9300\"}],\"routers\":[{\"name\":\"RTR-EDGE-02\",\"type\":\"Edge Router\",\"model\":\"Cisco ASR 1000\"}],\"firewalls\":[{\"name\":\"FW-PERIMETER-02\",\"type\":\"Perimeter Firewall\",\"model\":\"Palo Alto PA-5220\"}]},\"connectivity\":{\"primaryLink\":{\"type\":\"Dark Fiber\",\"bandwidth\":\"100Gbps\",\"latency\":\"<2ms\"},\"backupLink\":{\"type\":\"MPLS\",\"bandwidth\":\"10Gbps\",\"latency\":\"<10ms\"}},\"protocols\":{\"routing\":\"OSPF\",\"spanning_tree\":\"RSTP\",\"vlan_trunking\":\"802.1Q\"}}}",
            Version = "1.0.0"
        };

        var expectedMessage = "Network CyberComponentMapping created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberComponentMappingResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberComponentMapping(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberComponentMappingResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Network", response.Message);
    }

    [Fact]
    public async Task GetCyberComponentMappingById_HandlesDetailedMapping()
    {
        // Arrange
        var mappingId = Guid.NewGuid().ToString();
        var detailedMapping = new CyberComponentMappingDetailVm
        {
            Id = mappingId,
            Name = "Comprehensive Enterprise Mapping",
            Properties = "{\"comprehensiveMapping\":{\"infrastructure\":{\"compute\":\"VMware vSphere\",\"storage\":\"NetApp ONTAP\",\"network\":\"Cisco ACI\"},\"applications\":{\"tier1\":[\"ERP\",\"CRM\"],\"tier2\":[\"Email\",\"File Sharing\"]},\"security\":{\"encryption\":\"AES-256\",\"authentication\":\"Active Directory\",\"monitoring\":\"Splunk\"},\"compliance\":{\"standards\":[\"SOX\",\"PCI-DSS\",\"GDPR\"],\"auditing\":\"enabled\",\"reporting\":\"automated\"}}}",
           
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentMappingDetailQuery>(q => q.Id == mappingId), default))
            .ReturnsAsync(detailedMapping);

        // Act
        var result = await _controller.GetCyberComponentMappingById(mappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mapping = Assert.IsType<CyberComponentMappingDetailVm>(okResult.Value);
        Assert.Equal("Comprehensive Enterprise Mapping", mapping.Name);
  
        Assert.Contains("comprehensiveMapping", mapping.Properties);
    }

    [Fact]
    public async Task IsCyberComponentMappingNameExist_HandlesSpecialCharacters()
    {
        // Arrange
        var mappingName = "Enterprise-Mapping_v2.0 (Production)";
        var mappingId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentMappingNameUniqueQuery>(q =>
                q.Name == mappingName && q.Id == mappingId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsCyberComponentMappingNameExist(mappingName, mappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }

    [Fact]
    public async Task GetCyberComponentMappings_HandlesLargeDataset()
    {
        // Arrange
        var largeMappingList = new List<CyberComponentMappingListVm>();
        for (int i = 0; i < 1500; i++)
        {
            largeMappingList.Add(new CyberComponentMappingListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = $"Enterprise Mapping {i + 1}",
                Properties = "{\"updatedMapping\":{\"version\":\"3.0.0\",\"enhancements\":[\"improved_security\",\"enhanced_monitoring\",\"automated_failover\"],\"newFeatures\":{\"aiPoweredAnalytics\":true,\"predictiveFailover\":true,\"realTimeSync\":true}}}"
            });
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberComponentMappingListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(largeMappingList);

        // Act
        var result = await _controller.GetCyberComponentMappings();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mappings = Assert.IsAssignableFrom<List<CyberComponentMappingListVm>>(okResult.Value);
        Assert.Equal(1500, mappings.Count);
       
    }
}
