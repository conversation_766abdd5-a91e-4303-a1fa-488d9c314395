﻿using ContinuityPatrol.Application.Features.Workflow.Events.Draft;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixApprovalModel;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Core.Enums;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Workflow.Commands.Draft;

public class UpdateWorkflowIsDraftCommandHandler : IRequestHandler<UpdateWorkflowIsDraftCommand, UpdateWorkflowIsDraftResponse>
{
    private readonly IWorkflowRepository _workflowRepository;
    private readonly ILoadBalancerRepository _loadBalancerRepository;
   // private readonly IWindowsService _windowsService;
    private readonly IJobScheduler _client;
    private readonly IPublisher _publisher;
    private readonly IApprovalMatrixRepository _approvalMatrixRepository;
    private readonly IApprovalMatrixRequestRepository _approvalMatrixRequestRepository;
    private readonly IApprovalMatrixApprovalRepository _approvalMatrixApprovalRepository;
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IWorkflowTempRepository _workflowTempRepository;
    private readonly IVersionManager _versionManager;
    private readonly ISmtpConfigurationRepository _smtpConfigurationRepository;
    private readonly IEmailService _emailService;
    private readonly IApprovalMatrixUsersRepository _approvalMatrixUsersRepository;
    private readonly IConfiguration _configuration;

    public UpdateWorkflowIsDraftCommandHandler(IWorkflowRepository workflowRepository,
       /* IWindowsService windowsService,*/ ILoadBalancerRepository loadBalancerRepository,
      IJobScheduler client, IPublisher publisher, IApprovalMatrixRepository approvalMatrixRepository,
        IApprovalMatrixRequestRepository approvalMatrixRequestRepository,
        IApprovalMatrixApprovalRepository approvalMatrixApprovalRepository, 
        IWorkflowProfileInfoRepository workflowProfileInfoRepository, ILoggedInUserService loggedInUserService, 
        IWorkflowTempRepository workflowTempRepository, IVersionManager versionManager, ISmtpConfigurationRepository smtpConfigurationRepository, IEmailService emailService, IApprovalMatrixUsersRepository approvalMatrixUsersRepository, IConfiguration configuration)
    {
        _workflowRepository = workflowRepository;
       // _windowsService = windowsService;
        _loadBalancerRepository = loadBalancerRepository;
        _client = client;
        _publisher = publisher;
        _approvalMatrixRepository = approvalMatrixRepository;
        _approvalMatrixRequestRepository = approvalMatrixRequestRepository;
        _approvalMatrixApprovalRepository = approvalMatrixApprovalRepository;
        _workflowProfileInfoRepository = workflowProfileInfoRepository;
        _loggedInUserService = loggedInUserService;
        _workflowTempRepository = workflowTempRepository;
        _versionManager = versionManager;
        _smtpConfigurationRepository = smtpConfigurationRepository;
        _emailService = emailService;
        _approvalMatrixUsersRepository = approvalMatrixUsersRepository;
        _configuration = configuration;
    }

    public async Task<UpdateWorkflowIsDraftResponse> Handle(UpdateWorkflowIsDraftCommand request, CancellationToken cancellationToken)
    {
        var nodeConfig =
            await _loadBalancerRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString())
            ?? await _loadBalancerRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(),
                ServiceType.LoadBalancer.ToString());

        if (nodeConfig is null) throw new InvalidException("LoadBalancer not configured!.");

        var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

       // var monitorUrl = UrlHelper.GenerateMonitorCheckUrl(nodeConfig.TypeCategory, baseUrl);

        //var monitorResponse = await _windowsService.CheckWindowsService(monitorUrl);

        //if (!monitorResponse.Success) throw new WindowServiceException(monitorResponse.InActiveNodes, ServiceType.Monitor.ToString(), monitorResponse.Message);

        Guard.Against.InvalidGuidOrEmpty(request.Id, "Workflow Id");

        var eventToUpdate = await _workflowRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Workflow), request.Id);

        eventToUpdate.IsDraft = request.IsDraft;

        var workflowProfileInfo = await _workflowProfileInfoRepository.GetProfileIdAttachByWorkflowId(request.Id);

        if (workflowProfileInfo is null)
        {
            throw new Exception($"Workflow '{eventToUpdate.Name}' is not attached to any profile.");
        }

        var approvalMatrixDto =
            await _approvalMatrixRepository.GetApprovalMatrixByBusinessFunctionId(
                workflowProfileInfo.BusinessFunctionId);

        if (approvalMatrixDto is null)
        {
            throw new Exception($"Workflow '{eventToUpdate.Name}' is not attached to approval matrix template.");
        }

        var version = await _versionManager.GetUpdateVersion(eventToUpdate.Version);

        var requestId = $"CP-REQ-{DateTime.UtcNow:yyyyMMddHHmmssfff}-{Guid.NewGuid().ToString("N").Substring(0, 6)}";

        await _workflowTempRepository.AddAsync(new Domain.Entities.WorkflowTemp
        {
            ApprovalMatrixId = approvalMatrixDto.ReferenceId,
            WorkflowId = eventToUpdate.ReferenceId,
            RequestId = requestId,
            Name = eventToUpdate.Name,
            Properties = request.Properties,
            Version = version
        });

        var userLists = approvalMatrixDto.Properties.IsNotNullOrWhiteSpace() 
            ? JArray.Parse(approvalMatrixDto.Properties)
            .Select((obj, index) => new
            {
                ProcessName = (string)obj["name"],
                Description = (string)obj["description"],
                SLA = obj["SLA"]?.ToString(Formatting.None),
                Approvers = obj["userLists"]?.ToString(Formatting.None),
                IsApproval = index == 0
            }).ToList()
            :null;

        var userJson = userLists is not null ? JsonConvert.SerializeObject(userLists) : null;

        var approvalMatrixRequest = new Domain.Entities.ApprovalMatrixRequest
        {
            RequestId = requestId,
            ProcessName = requestId,
            ApprovalMatrixId = approvalMatrixDto.ReferenceId,
            WorkflowId = eventToUpdate.ReferenceId,
            WorkflowName = eventToUpdate.Name,
            Approvers = userJson,
            Status = "Waiting for Approval",
            UserName = _loggedInUserService.LoginName
        };

        await _approvalMatrixRequestRepository.AddAsync(approvalMatrixRequest);

        var approvalMatrixApprovalList = userLists!
            .SelectMany(user => JArray.Parse(user.Approvers)
                .Select(approver => new Domain.Entities.ApprovalMatrixApproval
                {
                    ApprovalMatrixId = approvalMatrixDto.ReferenceId,
                    RequestId = requestId,
                    ProcessName = user.ProcessName,
                    Description = user.Description,
                    UserId = _loggedInUserService.UserId,
                    UserName = _loggedInUserService.LoginName,
                    WorkflowId = eventToUpdate.ReferenceId,
                    WorkflowName = eventToUpdate.Name,
                    Status = "Waiting for Approval",
                    ApproverId = (string)approver["id"],
                    ApproverName = (string)approver["name"],
                    Sla = user.SLA,
                    IsApproval = user.IsApproval
                }))
            .ToList();

        await _approvalMatrixApprovalRepository.AddRangeAsync(approvalMatrixApprovalList);

        await _workflowRepository.UpdateAsync(eventToUpdate);

        var url = UrlHelper.GenerateApprovalRequestUrl(baseUrl, requestId);

        await _client.ScheduleJob(eventToUpdate.ReferenceId, new Dictionary<string, string> { ["url"] = url });

        await SendEmail(requestId, eventToUpdate.Name);

        var response = new UpdateWorkflowIsDraftResponse
        {
            WorkflowId = request.Id,
            Message = $"workflow {eventToUpdate.Name} IsDraft Updated successfully."
        };

        await _publisher.Publish(new UpdateWorkflowDraftEvent { WorkflowId = eventToUpdate.ReferenceId, WorkflowName = eventToUpdate.Name, IsDraft = request.IsDraft }, cancellationToken);

        return response;
    }

    private async Task SendEmail(string requestId, string workflowName)
    {
        var smtp = (await _smtpConfigurationRepository.ListAllAsync()).LastOrDefault();
        if (smtp == null) return;

        var approvals = (await _approvalMatrixApprovalRepository.GetApprovalMatrixApprovalByRequestId(requestId))
            .Where(x => x.IsApproval).ToList();

        var images = new List<string> { "abstract.png", "approval_matrix_request.png", "cp_logo" };

        var approvalIds = approvals.Select(x => x.ApproverId).Distinct().ToList();

        var approvalList = await _approvalMatrixUsersRepository.GetListByApprovalIdsAsync(approvalIds);

        var approvalDic = approvalList.ToDictionary(x => x.UserId, x => x.Email);

        await Parallel.ForEachAsync(approvals, new ParallelOptions { MaxDegreeOfParallelism = Math.Max(1, approvals.Count) },
        async (item, _) =>
        {
            var toEmail = approvalDic.TryGetValue(item.UserId, out var email) ? email : string.Empty;

                if (toEmail.IsNotNullOrWhiteSpace())
                {
                    var loginUrl = _configuration.GetValue<string>("SignalR:Url");

                    var sla = JObject.Parse(item.Sla);
                    var duration = sla!["duration"]?.ToString();
                    var period = sla["period"]?.ToString();


                    var endTime = CalculateEndTime(duration, period);

                    var token = JsonConvert.SerializeObject(new ApprovalToken
                    {
                        RequestId = item.RequestId,
                        ReferenceId = item.ReferenceId,
                        ProcessName = item.ProcessName,
                        Expiry = endTime
                    });

                    var encodedToken = Convert.ToBase64String(Encoding.UTF8.GetBytes(token));

                    var approvalUrl = $"{loginUrl}api/ApprovalMatrix/accept?token={encodedToken}";

                    var rejectUrl = $"{loginUrl}api/ApprovalMatrix/reject?token={encodedToken}";

                    var body = EmailTemplateHelper.ApprovalMatrixRequestEmailBody(item.UserName, workflowName, item.ApproverName, approvalUrl, rejectUrl);
                    var html = HtmlEmailBuilder.BuildHtmlView(body, images, "Request");

                await _emailService.SendEmail(new EmailDto
                    {
                        To = toEmail,
                        From = smtp.UserName,
                        SmtpHost = smtp.SmtpHost,
                        HtmlBody = html,
                        EnableSSL = smtp.EnableSSL,
                        Port = smtp.Port,
                        Password = smtp.Password
                    });
                }


        });
    }

    private DateTime CalculateEndTime(string durationStr, string period)
    {
        var duration = int.TryParse(durationStr, out var d) ? d : 0;
        var now = DateTime.Now;

        return period.ToLower() switch
        {
            "minutes" => now.AddMinutes(duration),
            "hours" => now.AddHours(duration),
            "days" => now.AddDays(duration),
            "months" => now.AddMonths(duration),
            "years" => now.AddYears(duration),
            _ => now // default to now if unknown period
        };
    }
}

