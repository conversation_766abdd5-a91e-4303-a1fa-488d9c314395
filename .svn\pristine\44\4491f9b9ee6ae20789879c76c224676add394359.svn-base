using ContinuityPatrol.Application.Contexts;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Persistence.Persistence;

public partial class ApplicationDbContext : IOrchestrationDbContext
{
    #region Context
	public DbSet<WorkflowApprovalMapping> WorkflowApprovalMappings { get; set; }
    public DbSet<WorkflowDrCalender> WorkflowDrCalenders { get; set; }
    public DbSet<WorkflowTemp> WorkflowTemps { get; set; }
    public DbSet<WorkflowRunningAction> WorkflowRunningActions { get; set; }
    public DbSet<WorkflowActionFieldMaster> WorkflowActionFieldMasters { get; set; }
    public DbSet<ApprovalMatrix> ApprovalMatrices { get; set; }
    public DbSet<Workflow> WorkFlows { get; set; }
    public DbSet<WorkflowProfileInfo> WorkflowProfileInfos { get; set; }
    public DbSet<WorkflowHistory> WorkFlowHistories { get; set; }
    public DbSet<WorkflowInfraObject> WorkflowInfraObjects { get; set; }
    public DbSet<WorkflowActionType> WorkflowActionTypes { get; set; }
    public DbSet<WorkflowProfile> WorkflowProfiles { get; set; }
    public DbSet<WorkflowOperation> WorkflowOperations { get; set; }
    public DbSet<WorkflowOperationGroup> WorkflowOperationGroups { get; set; }
    public DbSet<WorkflowActionResult> WorkflowActionResults { get; set; }
    public DbSet<WorkflowExecutionTemp> WorkflowExecutionTemps { get; set; }
    public DbSet<Template> Templates { get; set; }
    public DbSet<TemplateHistory> TemplateHistory { get; set; }
    public DbSet<NodeWorkflowExecution> NodeWorkflowExecutions { get; set; }
    public DbSet<WorkflowExecutionEventLog> WorkflowExecutionEventLogs { get; set; }
    public DbSet<WorkflowPermission> WorkflowPermissions { get; set; }
    public DbSet<InfraObjectSchedulerWorkflowDetail> InfraObjectSchedulerWorkflowDetails { get; set; }
    public DbSet<WorkflowPrediction> WorkflowPredictions { get; set; }
    public DbSet<WorkflowView> WorkflowViews { get; set; }
    public DbSet<WorkflowProfileInfoView> WorkflowProfileInfoViews { get; set; }

    public DbSet<RpForVmCgEnableDisableStatus> RPForVMCGEnableDisableStatuses { get; set; }
    public DbSet<SchedulerWorkflowActionResults> SchedulerWorkflowActionResults { get; set; }

    #endregion
}
