﻿namespace ContinuityPatrol.Application.Features.HeatMapLog.Queries.GetDetail;

public class GetHeatMapLogDetailQueryHandler : IRequestHandler<GetHeatMapLogDetailQuery, HeatMapLogDetailVm>
{
    private readonly IHeatMapLogRepository _heatMapLogRepository;
    private readonly IMapper _mapper;

    public GetHeatMapLogDetailQueryHandler(IMapper mapper, IHeatMapLogRepository heatMapLogRepository)
    {
        _mapper = mapper;
        _heatMapLogRepository = heatMapLogRepository;
    }

    public async Task<HeatMapLogDetailVm> Handle(GetHeatMapLogDetailQuery request, CancellationToken cancellationToken)
    {
        var heatMapLog = await _heatMapLogRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(heatMapLog, nameof(Domain.Entities.HeatMapLog),
            new NotFoundException(nameof(Domain.Entities.HeatMapLog), request.Id));

        var heatMapLogDetailDto = _mapper.Map<HeatMapLogDetailVm>(heatMapLog);

        return heatMapLogDetailDto ?? throw new NotFoundException(nameof(Domain.Entities.HeatMapLog), request.Id);
    }
}