using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class MsSqlNativeLogShippingMonitorStatusRepositoryTests : IClassFixture<MsSqlNativeLogShippingMonitorStatusFixture>
{
    private readonly MsSqlNativeLogShippingMonitorStatusFixture _msSqlNativeLogShippingMonitorStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly MsSqlNativeLogShippingMonitorStatusRepository _repository;

    public MsSqlNativeLogShippingMonitorStatusRepositoryTests(MsSqlNativeLogShippingMonitorStatusFixture msSqlNativeLogShippingMonitorStatusFixture)
    {
        _msSqlNativeLogShippingMonitorStatusFixture = msSqlNativeLogShippingMonitorStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new MsSqlNativeLogShippingMonitorStatusRepository(_dbContext);
    }

    private async Task ClearDatabase()
    {
        _dbContext.MsSqlNativeLogShippingMonitorStatus.RemoveRange(_dbContext.MsSqlNativeLogShippingMonitorStatus);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }



    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnActiveStatuses_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var statuses = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithMixedActiveStatus(testType);
        
        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddRangeAsync(statuses);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only active statuses should be returned
        Assert.All(result, status => Assert.True(status.IsActive));
        Assert.All(result, status => Assert.Equal(testType, status.Type));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType("NonExistentType");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsNull()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsEmpty()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType("");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnOnlyActiveStatuses()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var activeStatus = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: testType, isActive: true);
        var inactiveStatus = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: testType, isActive: false);
        
        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddRangeAsync(new[] { activeStatus, inactiveStatus });
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result[0].IsActive);
        Assert.Equal(testType, result[0].Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleWhitespaceInType()
    {
        // Arrange
        await ClearDatabase();
        var whitespaceStatus = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithWhitespace();
        whitespaceStatus.IsActive = true;
        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddAsync(whitespaceStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(whitespaceStatus.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(whitespaceStatus.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleVeryLongTypeNames()
    {
        // Arrange
        await ClearDatabase();
        var longTypeStatus = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithLongType(1000);
        longTypeStatus.IsActive = true;
        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddAsync(longTypeStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(longTypeStatus.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(longTypeStatus.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();
        var specialTypes = MsSqlNativeLogShippingMonitorStatusFixture.TestData.SpecialCharacterTypes;
        var statuses = new List<MsSqlNativeLogShippingMonitorStatus>();

        foreach (var type in specialTypes)
        {
            var status = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: type, isActive: true);
            statuses.Add(status);
        }

        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddRangeAsync(statuses);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        foreach (var type in specialTypes)
        {
            var result = await _repository.GetDetailByType(type);
            Assert.Single(result);
            Assert.Equal(type, result.First().Type);
            Assert.True(result.First().IsActive);
        }
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnMultipleStatuses_WhenMultipleActiveStatusesExist()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var statuses = _msSqlNativeLogShippingMonitorStatusFixture.CreateMultipleMsSqlNativeLogShippingMonitorStatusWithSameType(testType, 5);
        
        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddRangeAsync(statuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(5, result.Count);
        Assert.All(result, status => Assert.Equal(testType, status.Type));
        Assert.All(result, status => Assert.True(status.IsActive));
    }

    [Fact]
    public async Task GetDetailByType_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var lowerCaseType = "testtype";
        var upperCaseType = "TESTTYPE";
        
        var lowerCaseStatus = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: lowerCaseType, isActive: true);
        var upperCaseStatus = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: upperCaseType, isActive: true);
        
        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddRangeAsync(new[] { lowerCaseStatus, upperCaseStatus });
        await _dbContext.SaveChangesAsync();

        // Act
        var lowerResult = await _repository.GetDetailByType(lowerCaseType);
        var upperResult = await _repository.GetDetailByType(upperCaseType);

        // Assert
        Assert.Single(lowerResult);
        Assert.Single(upperResult);
        Assert.Equal(lowerCaseType, lowerResult.First().Type);
        Assert.Equal(upperCaseType, upperResult.First().Type);
    }

    #endregion

    #region GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId Tests

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId_ShouldReturnStatus_WhenInfraObjectIdExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "9a73e815-ab81-4d54-a468-72b4b9632866";
        var status = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithSpecificInfraObjectId(infraObjectId);

        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddAsync(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal(status.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId_ShouldReturnNull_WhenInfraObjectIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId("9a73e815-ab81-4d54-a468-72b4b9632866");

        // Assert
        Assert.Null(result);
    }

   
    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId_ShouldReturnFirstMatch_WhenMultipleStatusesExist()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "9a73e815-ab81-4d54-a468-72b4b9632866";
        var status1 = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithSpecificInfraObjectId(infraObjectId);
        var status2 = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithSpecificInfraObjectId(infraObjectId);

        // Add status1 first, then status2
        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddAsync(status1);
       _dbContext.SaveChanges();
        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddAsync(status2);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should return the first one added (FirstOrDefaultAsync behavior)
        Assert.Equal(status1.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId_ShouldReturnActiveAndInactiveStatuses()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "9a73e815-ab81-4d54-a468-72b4b9632866";
        var inactiveStatus = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithProperties(
            infraObjectId: infraObjectId,
            isActive: false);

        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddAsync(inactiveStatus);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.False(result.IsActive); // Should return inactive status as well
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId_ShouldHandleValidGuidFormats()
    {
        // Arrange
        await ClearDatabase();
        var validGuids = new[]
        {
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString("D"),
            Guid.NewGuid().ToString("N"),
            Guid.NewGuid().ToString("B"),
            Guid.NewGuid().ToString("P")
        };

        var statuses = new List<MsSqlNativeLogShippingMonitorStatus>();
        foreach (var guid in validGuids)
        {
            var status = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithSpecificInfraObjectId(guid);
            statuses.Add(status);
        }

        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddRangeAsync(statuses);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        foreach (var guid in validGuids)
        {
            var result = await _repository.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(guid);
            Assert.NotNull(result);
            Assert.Equal(guid, result.InfraObjectId);
        }
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var lowerCaseGuid = Guid.NewGuid().ToString().ToLower();
        var upperCaseGuid = lowerCaseGuid.ToUpper();

        var lowerCaseStatus = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithSpecificInfraObjectId(lowerCaseGuid);
        var upperCaseStatus = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithSpecificInfraObjectId(upperCaseGuid);

        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddRangeAsync(new[] { lowerCaseStatus, upperCaseStatus });
        _dbContext.SaveChanges();

        // Act
        var lowerResult = await _repository.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(lowerCaseGuid);
        var upperResult = await _repository.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(upperCaseGuid);

        // Assert
        Assert.NotNull(lowerResult);
        Assert.NotNull(upperResult);
        Assert.Equal(lowerCaseGuid, lowerResult.InfraObjectId);
        Assert.Equal(upperCaseGuid, upperResult.InfraObjectId);
        Assert.NotEqual(lowerResult.ReferenceId, upperResult.ReferenceId);
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId_ShouldHandleSpecialCharactersInGuid()
    {
        // Arrange
        await ClearDatabase();
        var guidWithDashes = "12345678-1234-1234-1234-123456789012";
        var guidWithBraces = "{12345678-1234-1234-1234-123456789013}";
        var guidWithParentheses = "(12345678-1234-1234-1234-123456789014)";

        var status1 = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithSpecificInfraObjectId(guidWithDashes);
        var status2 = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithSpecificInfraObjectId(guidWithBraces);
        var status3 = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithSpecificInfraObjectId(guidWithParentheses);

        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddRangeAsync(new[] { status1, status2, status3 });
        await _dbContext.SaveChangesAsync();

        // Act
        var result1 = await _repository.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(guidWithDashes);
        var result2 = await _repository.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(guidWithBraces);
        var result3 = await _repository.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(guidWithParentheses);

        // Assert
        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.NotNull(result3);
        Assert.Equal(guidWithDashes, result1.InfraObjectId);
        Assert.Equal(guidWithBraces, result2.InfraObjectId);
        Assert.Equal(guidWithParentheses, result3.InfraObjectId);
    }

    #endregion



    #region Edge Case Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var tasks = new List<Task>();

        // Act - Perform concurrent add operations
        for (int i = 0; i < 10; i++)
        {
            var status = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithProperties();
            tasks.Add(_repository.AddAsync(status));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allStatuses = await _repository.ListAllAsync();
        Assert.Equal(10, allStatuses.Count);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleUnicodeCharacters()
    {
        // Arrange
        await ClearDatabase();
        var unicodeType = "测试类型_тест_テスト";
        var status = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: unicodeType, isActive: true);
        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddAsync(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(unicodeType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(unicodeType, result[0].Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldUseEqualsMethod()
    {
        // Arrange
        await ClearDatabase();
        var exactType = "ExactType";
        var similarType = "ExactTypeExtra"; // Contains the exact type but is longer

        var exactStatus = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: exactType, isActive: true);
        var similarStatus = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: similarType, isActive: true);

        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddRangeAsync(new[] { exactStatus, similarStatus });
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(exactType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Should only return exact match, not partial match
        Assert.Equal(exactType, result[0].Type);
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId_ShouldUseEqualsMethod()
    {
        // Arrange
        await ClearDatabase();
        var exactInfraObjectId = "12345678-1234-1234-1234-123456789012";
        var similarInfraObjectId = "12345678-1234-1234-1234-123456789013"; // Similar but different

        var exactStatus = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithSpecificInfraObjectId(exactInfraObjectId);
        var similarStatus = _msSqlNativeLogShippingMonitorStatusFixture.CreateMsSqlNativeLogShippingMonitorStatusWithSpecificInfraObjectId(similarInfraObjectId);

        await _dbContext.MsSqlNativeLogShippingMonitorStatus.AddRangeAsync(new[] { exactStatus, similarStatus });
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(exactInfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(exactInfraObjectId, result.InfraObjectId);
        Assert.Equal(exactStatus.ReferenceId, result.ReferenceId);
    }



    #endregion
}
