global using AutoFixture;
global using AutoFixture.Xunit2;
global using AutoMapper;
global using ContinuityPatrol.Application.Contracts.Persistence;
global using ContinuityPatrol.Shared.Core.Contracts.Identity;
global using MediatR;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.Logging;
global using MockQueryable.Moq;
global using Moq;
global using Shouldly;
global using System.Collections.Generic;
global using System.Linq;
global using System.Threading;
global using System.Threading.Tasks;
global using Xunit;

