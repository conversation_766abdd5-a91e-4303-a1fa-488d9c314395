﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.UpdateLog;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetByWorkflowOperationId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetLogDataByGroupId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetNames;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatus;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatusList;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningUserDetails;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupByInfraObjectId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupByNodeId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupListByWorkflowId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowServiceStatus;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Orchestration;

public class WorkflowOperationGroupService : BaseService, IWorkflowOperationGroupService
{
    public WorkflowOperationGroupService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowOperationGroupCommand createWorkflowOperationGroupCommand)
    {
        Logger.LogDebug($" Create WorkflowOperationGroup '{createWorkflowOperationGroupCommand.ProfileId}'");

        return await Mediator.Send(createWorkflowOperationGroupCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "WorkflowOperationGroup Id");

        Logger.LogDebug($"Delete WorkflowOperationGroup Details by Id '{id}'");

        return await Mediator.Send(new DeleteWorkflowOperationGroupCommand { Id = id });
    }

    public async Task<List<WorkflowOperationGroupListByWorkflowIdVm>> GetOperationGroupByWorkflowIdAndOperationId(
        string workflowId, string workflowOperationId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowId, "WorkflowOperationGroup WorkflowId & WorkflowOperationId");

        Logger.LogDebug($"Get WorkflowOperationGroup List by WorkflowId {workflowId} and WorkflowOperationId {workflowOperationId} ");

        return await Mediator.Send(new GetWorkflowOperationGroupListByWorkflowIdQuery
            { WorkflowId = workflowId, WorkflowOperationId = workflowOperationId });
    }

    public async Task<WorkflowOperationGroupDetailVm> GetWorkflowOperationGroupById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "WorkflowOperationGroup Id");

        Logger.LogDebug($"Get WorkflowOperationGroup Detail by Id '{id}'");

        return await Mediator.Send(new GetWorkflowOperationGroupDetailQuery { Id = id });
    }

    public async Task<List<WorkflowOperationGroupByInfraObjectIdVm>> GetWorkflowOperationGroupByInfraObjectId(
        string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObject Id");

        Logger.LogDebug($"Get WorkflowOperationGroup Detail by Id '{infraObjectId}'");

        return await Mediator.Send(new GetWorkflowOperationGroupByInfraObjectIdQuery { InfraObjectId = infraObjectId });
    }

    public async Task<List<WorkflowOperationGroupByNodeIdVm>> GetWorkflowOperationGroupByNodeId(string nodeId)
    {
        Guard.Against.InvalidGuidOrEmpty(nodeId, "WorkflowOperationGroup nodeId");

        Logger.LogDebug($"Get WorkflowOperationGroup Detail by Id '{nodeId}'");

        return await Mediator.Send(new GetWorkflowOperationGroupByNodeIdQuery { NodeId = nodeId });
    }

    public async Task<List<WorkflowOperationGroupRunningUserVm>> GetWorkflowOperationGroupByRunningUserId(string userId)
    {
        Logger.LogDebug($"Get All Running WorkflowOperationGroup by UserId {userId}.");

        return await Mediator.Send(new GetWorkflowOperationGroupRunningUserDetailQuery { UserId = userId });
    }

    public async Task<List<GetByWorkflowOperationIdVm>> GetWorkflowOperationGroupByWorkflowOperationId(
        string workflowOperationId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowOperationId, "WorkflowOperationGroup Id");

        Logger.LogDebug($"Get WorkflowOperationGroup Detail by Id '{workflowOperationId}'");

        return await Mediator.Send(new GetByWorkflowOperationIdQuery { WorkflowOperationId = workflowOperationId });
    }

    public async Task<List<WorkflowOperationGroupListVm>> GetWorkflowOperationGroupList()
    {
        Logger.LogDebug("Get All WorkflowOperationGroup");

        return await Mediator.Send(new GetWorkflowOperationGroupListQuery());
    }

    public async Task<List<WorkflowOperationGroupNameVm>> GetWorkflowOperationGroupNames()
    {
        Logger.LogDebug("Get All WorkflowOperationGroup Names");

        return await Mediator.Send(new GetWorkflowOperationGroupNameQuery());
    }

    public async Task<List<WorkflowOperationGroupRunningStatusVm>> GetWorkflowOperationGroupRunningList()
    {
        Logger.LogDebug("Get All WorkflowOperationGroup Running Status.");

        return await Mediator.Send(new GetWorkflowOperationGroupRunningStatusQuery());
    }

    public async Task<bool> IsWorkflowOperationGroupNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "WorkflowOperationGroup Name");

        Logger.LogDebug($"Check Name Exists Detail by WorkflowOperationGroup Name '{name}' and id '{id}'");

        return await Mediator.Send(new GetWorkflowOperationGroupNameUniqueQuery
            { WorkflowOperationGroupName = name, WorkflowOperationGroupId = id });
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowOperationGroupCommand updateWorkflowOperationGroupCommand)
    {
        Logger.LogDebug(
            $"Update WorkflowOperationGroup '{updateWorkflowOperationGroupCommand.CurrentActionName}'");

        return await Mediator.Send(updateWorkflowOperationGroupCommand);
    }

    public async Task<List<GetLogByGroupIdVm>> GetLogDataByGroupId(string groupId)
    {
        Logger.LogDebug($"Get Seq Log Detail by WorkflowOperationGroupId '{groupId}'");

        return await Mediator.Send(new GetLogByGroupIdQuery { GroupId = groupId });
    }

    public async Task<GetWorkflowServiceResponse> CheckWindowsServiceConnection(string type)
    {
        Logger.LogInformation("Check Workflow Service Status");

        return await Mediator.Send(new GetWorkflowServiceStatusQuery{Type = type});
    }

    public async Task<PaginatedResult<WorkflowOperationGroupListVm>> GetPaginatedWorkflowOperationGroup(
        GetWorkflowOperationGroupPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in WorkflowOperationGroup Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<List<ProfileRunningCountListVm>> GetWorkflowOperationGroupRunningStatusList()
    {
        Logger.LogDebug("Get Workflow Operation Group Running status list");

        return await Mediator.Send(new GetWorkflowOperationGroupRunningStatusListQuery());
    }

    public async Task<UpdateOperationGroupLogResponse> UpdateOperationGroupLog(UpdateOperationGroupLogCommand updateWorkflowOperationGroupCommand)
    {
        Logger.LogDebug($"Update WorkflowOperationGroup workflowName '{updateWorkflowOperationGroupCommand.WorkflowName}'");

        return await Mediator.Send(updateWorkflowOperationGroupCommand);
    }
}