﻿using ContinuityPatrol.Application.Features.NodeWorkflowExecution.Commands.Create;
using ContinuityPatrol.Application.Features.NodeWorkflowExecution.Commands.Delete;
using ContinuityPatrol.Application.Features.NodeWorkflowExecution.Commands.Update;
using ContinuityPatrol.Application.Features.NodeWorkflowExecution.Queries.GetByInfraSchedulerId;
using ContinuityPatrol.Application.Features.NodeWorkflowExecution.Queries.GetByJobId;
using ContinuityPatrol.Application.Features.NodeWorkflowExecution.Queries.GetDetail;
using ContinuityPatrol.Application.Features.NodeWorkflowExecution.Queries.GetList;
using ContinuityPatrol.Application.Features.NodeWorkflowExecution.Queries.GetNodeWorkflowExecutionByWorkflowOperationId;
using ContinuityPatrol.Domain.ViewModels.NodeWorkflowExecutionModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class NodeWorkflowExecutionController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<NodeWorkflowExecutionListVm>>> GetNodeWorkflowExecutionList()
    {
        Logger.LogDebug("Get All NodeWorkflowExecutions");

        //return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllNodeWorkflowExecutionsCacheKey + LoggedInUserService.CompanyId,
        //   () => Mediator.Send(new GetNodeWorkflowExecutionListQuery()), CacheExpiry));

        return Ok(await Mediator.Send(new GetNodeWorkflowExecutionListQuery()));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Orchestration.Create)]
    public async Task<ActionResult<CreateNodeWorkflowExecutionResponse>> CreateNodeWorkflowExecution(
        [FromBody] CreateNodeWorkflowExecutionCommand createNodeWorkflowExecutionCommand)
    {
        Logger.LogDebug($"Create Template '{createNodeWorkflowExecutionCommand.NodeName}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateNodeWorkflowExecution),
            await Mediator.Send(createNodeWorkflowExecutionCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Orchestration.Edit)]
    public async Task<ActionResult<UpdateNodeWorkflowExecutionResponse>> UpdateNodeWorkflowExecution(
        [FromBody] UpdateNodeWorkflowExecutionCommand updateNodeWorkflowExecutionCommand)
    {
        Logger.LogDebug($"Update NodeWorkflowExecution '{updateNodeWorkflowExecutionCommand.NodeName}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateNodeWorkflowExecutionCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Orchestration.Delete)]
    public async Task<ActionResult<DeleteNodeWorkflowExecutionResponse>> DeleteNodeWorkflowExecution(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "NodeWorkflowExecution Id");

        Logger.LogDebug($"Delete NodeWorkflowExecution Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteNodeWorkflowExecutionCommand { Id = id }));
    }

    [HttpGet("{id}", Name = "GetNodeWorkflowExecution")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<NodeWorkflowExecutionDetailVm>> GetNodeWorkflowExecutionById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "NodeWorkflowExecutionId");

        Logger.LogDebug($"Get NodeWorkflowExecution Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetNodeWorkflowExecutionDetailQuery { Id = id }));
    }

    [HttpGet("workflowOperationId", Name = "GetNodeWorkflowExecutions")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<NodeWorkflowExecutionByWorkflowOperationIdVm>>
        GetNodeWorkflowExecutionsByWorkflowOperationId(string workflowOperationId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowOperationId, "WorkflowOperation Id");

        Logger.LogDebug($"Get NodeWorkflowExecution Details by WorkflowOperationId '{workflowOperationId}'");

        return Ok(await Mediator.Send(new GetNodeWorkflowExecutionByWorkflowOperationIdQuery
            { WorkflowOperationId = workflowOperationId }));
    }

    [HttpGet("infraSchedulerId")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<NodeWorkflowExecutionByInfraObjectSchedulerIdVm>>
        GetNodeWorkflowExecutionByInfraSchedulerId(string infraSchedulerId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraSchedulerId, "NodeWorkflowExecution by InfraSchedulerId");

        Logger.LogDebug($"Get NodeWorkflowExecution Detail by infraSchedulerId '{infraSchedulerId}'");

        return Ok(await Mediator.Send(new GetNodeWorkflowExecutionByInfraObjectSchedulerIdQuery
            { InfraSchedulerId = infraSchedulerId }));
    }

    [HttpGet("jobId")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<NodeWorkFlowExecutionByJobIdVm>> GetNodeWorkflowExecutionByJobId(string jobId)
    {
        Guard.Against.InvalidGuidOrEmpty(jobId, "NodeWorkflowExecution by jobId");

        Logger.LogDebug($"Get NodeWorkflowExecution Detail by Id '{jobId}'");

        return Ok(await Mediator.Send(new GetNodeWorkFlowExecutionByJobIdQuery { JobId = jobId }));
    }


    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys =
        {
            ApplicationConstants.Cache.AllNodeWorkflowExecutionsCacheKey + LoggedInUserService.CompanyId,
            ApplicationConstants.Cache.AllNodeWorkflowExecutionNamesCacheKey
        };

        ClearCache(cacheKeys);
    }
}