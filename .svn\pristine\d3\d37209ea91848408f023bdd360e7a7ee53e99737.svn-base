using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberComponentGroupModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ICyberComponentGroupService
{
    Task<List<CyberComponentGroupListVm>> GetCyberComponentGroupList();
    Task<BaseResponse> CreateAsync(CreateCyberComponentGroupCommand createCyberComponentGroupCommand);
    Task<BaseResponse> UpdateAsync(UpdateCyberComponentGroupCommand updateCyberComponentGroupCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<CyberComponentGroupDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsCyberComponentGroupNameExist(string name, string id);
   #endregion
    #region Paginated
    Task<PaginatedResult<CyberComponentGroupListVm>> GetPaginatedCyberComponentGroups(GetCyberComponentGroupPaginatedListQuery query);
    #endregion
}
