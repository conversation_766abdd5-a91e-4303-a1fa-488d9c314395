﻿using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CredentialProfile.Validators;

public class UpdateCredentialProfileValidatorTests
{
    private readonly Mock<ICredentialProfileRepository> _mockCredentialProfileRepository;

    public UpdateCredentialProfileValidatorTests()
    {
        var credentialProfiles = new Fixture().Create<List<Domain.Entities.CredentialProfile>>();

        _mockCredentialProfileRepository = CredentialProfileRepositoryMocks.UpdateCredentialProfileRepository(credentialProfiles);
    }

    //Name

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Name_InCredentialProfile_WithEmpty(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = "";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Name_InCredentialProfile_IsNull(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = null;

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameNotEmpty, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Name_InCredentialProfile_MinimumRange(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = "AR";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameRange, validateResult.Errors[0].ErrorMessage);

    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Name_InCredentialProfile_MaximumRange(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameRange, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Valid_Name_InCredentialProfile(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = "   PTS  ";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Valid_Name_InCredentialProfile_With_DoubleSpace_InFront(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = "  PTS India";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Valid_Name_InCredentialProfile_With_TripleSpace_InBetween(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = "PTS  Technosoft   India";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }
    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Valid_Name_InCredentialProfile_With_SpecialCharacters_InFront(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = " @#PTS India";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Valid_Name_InCredentialProfile_With_SpecialCharacters_Only(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = "@!!#$$%%^&*<>:";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }
    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Valid_Name_InCredentialProfile_With_UnderScore_InFront(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = "_PTS";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Valid_Name_InCredentialProfile_With_UnderScore_InFront_AndBack(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = "_PTSIndia_";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Valid_Name_InCredentialProfile_With_Numbers_InFront(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = "124PTSIndia";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Valid_Name_InCredentialProfile_With_UnderScoreAndNumbers_InFront_AndUnderscore_InBack(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = "_124PTSIndia_";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Valid_Name_InCredentialProfile_With_UnderScore_InFront_With_Numbers_InBack(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = "_PTSIndia234";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }


    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_Valid_Name_InCredentialProfile_With_Numbers_Only(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.Name = "1234567890";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    //CredentialType

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_CredentialProfileType_WithEmpty(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.CredentialType = "";

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileTypeRequired, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Update_CredentialProfileType_IsNull(UpdateCredentialProfileCommand updateCredentialProfileCommand)
    {
        var validator = new UpdateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        updateCredentialProfileCommand.CredentialType = null;

        var validateResult = await validator.ValidateAsync(updateCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileTypeNotNullRequired, validateResult.Errors[2].ErrorMessage);
    }
}