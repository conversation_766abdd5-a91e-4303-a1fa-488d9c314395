﻿using ContinuityPatrol.Application.Features.TeamResource.Events.Update;

namespace ContinuityPatrol.Application.Features.TeamResource.Commands.Update;

public class UpdateTeamResourceCommandHandler : IRequestHandler<UpdateTeamResourceCommand, UpdateTeamResourceResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly ITeamResourceRepository _teamResourceRepository;

    public UpdateTeamResourceCommandHandler(IMapper mapper, ITeamResourceRepository teamResourceRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _teamResourceRepository = teamResourceRepository;
        _publisher = publisher;
    }

    public async Task<UpdateTeamResourceResponse> Handle(UpdateTeamResourceCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _teamResourceRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.TeamResource), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateTeamResourceCommand), typeof(Domain.Entities.TeamResource));

        await _teamResourceRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateTeamResourceResponse
        {
            Message = Message.Update(nameof(Domain.Entities.TeamResource), eventToUpdate.TeamMasterName),

            Id = eventToUpdate.ReferenceId
        };
        await _publisher.Publish(new TeamResourceUpdatedEvent { TeamMasterName = eventToUpdate.TeamMasterName },
            cancellationToken);

        return response;
    }
}