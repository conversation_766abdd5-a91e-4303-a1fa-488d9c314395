﻿namespace ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateNodeStatus;

public class UpdateNodeStatusCommandHandler : IRequestHandler<UpdateNodeStatusCommand, UpdateNodeStatusResponse>
{
    private readonly ILoadBalancerRepository _loadBalancerRepository;

    public UpdateNodeStatusCommandHandler(ILoadBalancerRepository loadBalancerRepository)
    {
        _loadBalancerRepository = loadBalancerRepository;
    }

    public async Task<UpdateNodeStatusResponse> Handle(UpdateNodeStatusCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _loadBalancerRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.LoadBalancer), request.Name);

        eventToUpdate.IsNodeStatus = request.IsNodeStatus;

        await _loadBalancerRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateNodeStatusResponse
        { 
            Message = $"{eventToUpdate.TypeCategory} {eventToUpdate.Name} has been {(request.IsNodeStatus ? "turned on" : "turned off")} successfully.",

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}