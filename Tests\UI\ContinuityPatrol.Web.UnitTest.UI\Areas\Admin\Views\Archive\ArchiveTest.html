<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Archive Test Case</title>
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.19.4.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
    <style>
        html, body {
            width: 100vw;
            height: 100vh;
            margin: 0;
            padding: 0;
        }

        #qunit, #qunit-fixture {
            width: 100vw !important;
            max-width: 100vw !important;
            min-width: 100vw !important;
            box-sizing: border-box;
        }

        #qunit {
            margin: 0;
        }

        #qunit-fixture {
            min-height: 300px;
        }
    </style>
</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture"></div>
    <!-- jQuery and DataTables -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <!-- QUnit -->
    <script src="https://code.jquery.com/qunit/qunit-2.19.4.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Add Bootstrap JS bundle (includes toast) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- The full test suite JS -->
    <script src="/js/Common/common.js"></script>
    <script src="/js/Admin/Archive/Archive.js"></script>

    <!-- Test File -->
    <script src="/js/Admin/Archive/ArchiveTest.js"></script>
</body>
</html>