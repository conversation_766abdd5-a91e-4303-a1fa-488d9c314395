//using ContinuityPatrol.Persistence.Persistence;
//using ContinuityPatrol.Persistence.Repositories;
//using ContinuityPatrol.Persistence.UnitTests.Fixtures;
//using ContinuityPatrol.Shared.Tests.Infrastructure;
//using Xunit;

//namespace ContinuityPatrol.Persistence.UnitTests.Repository
//{
//    public class WorkflowPredictionRepositoryTests : IClassFixture<WorkflowPredictionFixture>
//    {
//        private readonly WorkflowPredictionFixture _fixture;
//        private readonly ApplicationDbContext _dbContext;
//        private readonly WorkflowPredictionRepository _repository;

//        public WorkflowPredictionRepositoryTests(WorkflowPredictionFixture fixture)
//        {
//            _fixture = fixture;
//            _dbContext = DbContextFactory.CreateInMemoryDbContext();
//            _repository = new WorkflowPredictionRepository(_dbContext);
//        }

//        [Fact]
//        public async Task GetWorkflowFailurePredictionById_ReturnsPredictions()
//        {
//            await _dbContext.WorkflowHistoricalFailureRateViews.AddRangeAsync(_fixture.WorkflowHistoricalFailureRateViewList);
//            await _dbContext.WorkflowRTOBreachRiskViews.AddRangeAsync(_fixture.WorkflowRTOBreachRiskViewList);
//            await _dbContext.WorkflowLastFailureRecencyViews.AddRangeAsync(_fixture.WorkflowLastFailureRecencyViewList);
//            await _dbContext.WorkflowInfrastructureHealthViews.AddRangeAsync(_fixture.WorkflowInfrastructureHealthViewList);
//            await _dbContext.WorkflowExecutionTimeDeviationViews.AddRangeAsync(_fixture.WorkflowExecutionTimeDeviationViewList);
//            await _dbContext.WorkflowRecentConfigurationChanges.AddRangeAsync(_fixture.WorkflowRecentConfigurationChangesViewList);
//            await _dbContext.SaveChangesAsync();

//            var ids = string.Join(",", _fixture.WorkflowHistoricalFailureRateViewList.Select(x => x.ReferenceId));
//            var result = await _repository.GetWorkflowFailurePredictionById(ids);

//            Assert.NotNull(result);
//            Assert.All(result, x => Assert.Contains(x.WorkflowId, ids));
//        }

//        [Fact]
//        public async Task GetWorkflowPredictionByActionId_ReturnsPredictions()
//        {
//            await _dbContext.WorkflowPredictions.AddRangeAsync(_fixture.WorkflowPredictionList);
//            await _dbContext.SaveChangesAsync();

//            var actionId = _fixture.WorkflowPredictionList.First().ActionId;
//            var result = await _repository.GetWorkflowPredictionByActionId(actionId);

//            Assert.All(result, x => Assert.Equal(actionId, x.ActionId));
//        }

//        [Fact]
//        public async Task GetWorkflowPredictionByNextPossibleId_ReturnsPredictions()
//        {
//            await _dbContext.WorkflowPredictions.AddRangeAsync(_fixture.WorkflowPredictionList);
//            await _dbContext.SaveChangesAsync();

//            var nextPossibleId = _fixture.WorkflowPredictionList.First().NextPossibleId;
//            var result = await _repository.GetWorkflowPredictionByNextPossibleId(nextPossibleId);

//            Assert.All(result, x => Assert.Equal(nextPossibleId, x.NextPossibleId));
//        }
//    }
//}