﻿using ContinuityPatrol.Application.Features.WorkflowAction.Events.SaveAs;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Events
{
    public class SaveAsWorkflowActionEventTests
    {
        private readonly Mock<ILogger<WorkflowActionSaveAsEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly WorkflowActionSaveAsEventHandler _handler;

        public SaveAsWorkflowActionEventTests()
        {
            _mockLogger = new Mock<ILogger<WorkflowActionSaveAsEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _mockUserService.Setup(svc => svc.UserId).Returns("test-user-id");
            _mockUserService.Setup(svc => svc.LoginName).Returns("test-login-name");
            _mockUserService.Setup(svc => svc.CompanyId).Returns("test-company-id");
            _mockUserService.Setup(svc => svc.RequestedUrl).Returns("http://test-url.com");
            _mockUserService.Setup(svc => svc.IpAddress).Returns("127.0.0.1");

            _handler = new WorkflowActionSaveAsEventHandler(
                _mockLogger.Object,
                _mockUserActivityRepository.Object,
                _mockUserService.Object
            );
        }

        [Fact]
        public async Task Handle_Should_LogAndSaveUserActivity_When_WorkflowActionIsSavedAs()
        {
            var notification = new WorkflowActionSaveAsEvent
            {
                ActionName = "TestSaveAsAction"
            };

            var expectedUserActivity = new Domain.Entities.UserActivity
            {
                UserId = _mockUserService.Object.UserId,
                LoginName = _mockUserService.Object.LoginName,
                CompanyId = _mockUserService.Object.CompanyId,
                RequestUrl = _mockUserService.Object.RequestedUrl,
                HostAddress = _mockUserService.Object.IpAddress,
                Action = $"{ActivityType.SaveAs} {Modules.WorkflowAction}",
                Entity = Modules.WorkflowAction.ToString(),
                ActivityType = ActivityType.SaveAs.ToString(),
                ActivityDetails = $"WorkflowAction '{notification.ActionName}' save-as successfully."
            };

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .ReturnsAsync(expectedUserActivity);

            await _handler.Handle(notification, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == _mockUserService.Object.UserId &&
                activity.LoginName == _mockUserService.Object.LoginName &&
                activity.CompanyId == _mockUserService.Object.CompanyId &&
                activity.RequestUrl == _mockUserService.Object.RequestedUrl &&
                activity.HostAddress == _mockUserService.Object.IpAddress &&
                activity.Action == $"{ActivityType.SaveAs} {Modules.WorkflowAction}" &&
                activity.Entity == Modules.WorkflowAction.ToString() &&
                activity.ActivityType == ActivityType.SaveAs.ToString() &&
                activity.ActivityDetails == $"WorkflowAction '{notification.ActionName}' save-as successfully."
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation(
                $"WorkflowAction '{notification.ActionName}' save-as successfully."), Times.Once);
        }
    }
}
