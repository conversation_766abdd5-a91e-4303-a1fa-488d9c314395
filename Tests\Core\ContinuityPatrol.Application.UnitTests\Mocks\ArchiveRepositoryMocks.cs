using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public static class ArchiveRepositoryMocks
{
    public static Mock<IArchiveRepository> CreateArchiveRepository(List<Archive> archives)
    {
        var mockArchiveRepository = new Mock<IArchiveRepository>();

        mockArchiveRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(archives);

        mockArchiveRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => archives.FirstOrDefault(x => x.ReferenceId == id));

        mockArchiveRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string name, string id) => 
                archives.Any(x => x.ArchiveProfileName == name && x.ReferenceId != id && x.IsActive));

        mockArchiveRepository.Setup(repo => repo.IsTableNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string tableName, string id) => 
                archives.Any(x => x.TableNameProperties.Contains(tableName) && x.ReferenceId != id && x.IsActive));

        mockArchiveRepository.Setup(repo => repo.GetByTableAccessId(It.IsAny<string>()))
            .ReturnsAsync((string id) => archives.Where(x => x.TableNameProperties.Contains(id) && x.IsActive).ToList());

        mockArchiveRepository.Setup(repo => repo.IsSolutionTypeExist(It.IsAny<string>()))
            .ReturnsAsync((string profileName) => archives.Any(x => x.ArchiveProfileName == profileName && x.IsActive));

        mockArchiveRepository.Setup(repo => repo.AddAsync(It.IsAny<Archive>()))
            .ReturnsAsync((Archive archive) =>
            {
                archive.ReferenceId = Guid.NewGuid().ToString();
                archive.Id = archives.Count + 1;
                archives.Add(archive);
                return archive;
            });

        mockArchiveRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Archive>()))
            .Returns((Archive archive) =>
            {
                var existingArchive = archives.FirstOrDefault(x => x.ReferenceId == archive.ReferenceId);
                if (existingArchive != null)
                {
                    existingArchive.CompanyId = archive.CompanyId;
                    existingArchive.TableNameProperties = archive.TableNameProperties;
                    existingArchive.ArchiveProfileName = archive.ArchiveProfileName;
                    existingArchive.Count = archive.Count;
                    existingArchive.CronExpression = archive.CronExpression;
                    existingArchive.ScheduleTime = archive.ScheduleTime;
                    existingArchive.ScheduleType = archive.ScheduleType;
                    existingArchive.BackUpType = archive.BackUpType;
                    existingArchive.Type = archive.Type;
                    existingArchive.ClearBackup = archive.ClearBackup;
                    existingArchive.NodeId = archive.NodeId;
                    existingArchive.NodeName = archive.NodeName;
                    existingArchive.IsActive = archive.IsActive;
                }
                return Task.CompletedTask;
            });

        mockArchiveRepository.Setup(repo => repo.DeleteAsync(It.IsAny<Archive>()))
            .Returns((Archive archive) =>
            {
                archives.Remove(archive);
                return Task.CompletedTask;
            });

        //mockArchiveRepository.Setup(repo => repo.PaginatedListAllAsync(
        //    It.IsAny<int>(), It.IsAny<int>(), It.IsAny<ISpecification<Archive>>(), 
        //    It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((int pageNumber, int pageSize, ISpecification<Archive> spec, string sortColumn, string sortOrder) =>
        //    {
        //        var filteredArchives = archives.Where(x => x.IsActive).ToList();
                
        //        if (spec != null)
        //        {
        //            // Apply specification filter if needed
        //            filteredArchives = filteredArchives.Where(spec.Criteria.Compile()).ToList();
        //        }

        //        var totalCount = filteredArchives.Count;
        //        var pagedArchives = filteredArchives
        //            .Skip((pageNumber - 1) * pageSize)
        //            .Take(pageSize)
        //            .ToList();

        //        return new PaginatedResult<Archive>
        //        {
        //            Data = pagedArchives,
        //            TotalCount = totalCount,
        //            PageSize = pageSize,
        //            TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
        //        };
        //    });

        return mockArchiveRepository;
    }

    public static Mock<IUserActivityRepository> CreateUserActivityRepository(List<UserActivity> userActivities)
    {
        var mockUserActivityRepository = new Mock<IUserActivityRepository>();

        mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
            .ReturnsAsync((UserActivity userActivity) =>
            {
                userActivity.Id = userActivities.Count + 1;
                userActivity.ReferenceId = Guid.NewGuid().ToString();
                userActivities.Add(userActivity);
                return userActivity;
            });

        mockUserActivityRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(userActivities);

        return mockUserActivityRepository;
    }

    public static Mock<ILoadBalancerRepository> CreateLoadBalancerRepository()
    {
        var mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();

        mockLoadBalancerRepository.Setup(repo => repo.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new LoadBalancer
            {
                ReferenceId = Guid.NewGuid().ToString(),
                TypeCategory = "LoadBalancer",
                Type = "ALL",
                ConnectionType = "http",
                IPAddress = "localhost",
                IsActive = true
            });

        return mockLoadBalancerRepository;
    }

    public static Mock<IJobScheduler> CreateJobScheduler()
    {
        var mockJobScheduler = new Mock<IJobScheduler>();

        mockJobScheduler.Setup(scheduler => scheduler.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
            .Returns(Task.CompletedTask);

        return mockJobScheduler;
    }
}
