using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Web.Middlewares;
using ValidationResult = FluentValidation.Results.ValidationResult;

namespace ContinuityPatrol.Web.UnitTests.Middlewares;

public class ErrorHandlingMiddlewareTests
{
    private readonly Mock<ILogger<ErrorHandlingMiddleware>> _loggerMock;
    private readonly DefaultHttpContext _context;

    public ErrorHandlingMiddlewareTests()
    {
        _loggerMock = new Mock<ILogger<ErrorHandlingMiddleware>>();
        _context = new DefaultHttpContext();
    }

        [Fact]
    public async Task InvokeAsync_NoException_CallsNextDelegate()
    {
       // var context = new DefaultHttpContext();
        var wasCalled = false;

        RequestDelegate next = ctx =>
        {
            wasCalled = true;
            return Task.CompletedTask;
        };

        var middleware = new ErrorHandlingMiddleware(next, _loggerMock.Object);

        // Act
        await middleware.InvokeAsync(_context);

        // Assert
        Assert.True(wasCalled);
        _loggerMock.VerifyNoOtherCalls();
    }


    [Fact]
    public async Task InvokeAsync_ValidationException_LogsEachError()
    {
        //var context = new DefaultHttpContext();

        var validationResult = new ValidationResult
        {
            Errors = new List<FluentValidation.Results.ValidationFailure>
                    {
                        new("Field1", "Field A is required"),
                        new("Field2", "Field B is invalid")
                    }
        };

        var validationException = new ValidationException(validationResult);

        RequestDelegate next = ctx => throw validationException;

        var middleware = new ErrorHandlingMiddleware(next, _loggerMock.Object);

        await middleware.InvokeAsync(_context);


        _loggerMock.Verify(x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, _) => v.ToString()!.Contains("Field A is required")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);


        _loggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, _) => v.ToString()!.Contains("Validation Error: Field B is invalid")),
            null,
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Fact]
    public async Task InvokeAsync_ExceptionThrown_LogsErrorAndReThrows()
    {
      
        var testException = new Exception("Something went wrong");

        RequestDelegate next = ctx => throw testException;

        var middleware = new ErrorHandlingMiddleware(next, _loggerMock.Object);

        var ex = await Assert.ThrowsAsync<Exception>(() => middleware.InvokeAsync(_context));
        Assert.Equal("Something went wrong", ex.Message);

        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, _) => v.ToString()!.Contains("ErrorHandlingMiddleware : ************ Exception Message : Something went wrong")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task InvokeAsync_ExceptionWithInnerException_LogsInnerExceptionMessage()
    {
        var innerEx = new Exception("Inner cause");
        var outerEx = new Exception("Outer error", innerEx);

        RequestDelegate next = ctx => throw outerEx;

        var middleware = new ErrorHandlingMiddleware(next, _loggerMock.Object);

        // Act & Assert
        var ex = await Assert.ThrowsAsync<Exception>(() => middleware.InvokeAsync(_context));
        Assert.Equal("Outer error", ex.Message);

        _loggerMock.Verify(x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Inner Exception : Inner cause")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

    }
}
