using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.AboutCp.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.AboutCpModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class AboutCpControllerTests : IClassFixture<AboutCpFixture>
{
    private readonly AboutCpFixture _aboutCpFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly AboutCpController _controller;

    public AboutCpControllerTests(AboutCpFixture aboutCpFixture)
    {
        _aboutCpFixture = aboutCpFixture;

        var testBuilder = new ControllerTestBuilder<AboutCpController>();
        _controller = testBuilder.CreateController(
            _ => new AboutCpController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAboutCpList_ReturnsExpectedList()
    {
        // Arrange
        var expectedAboutCpList = new List<GetAboutCpListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.0",
                LicenseType = "Enterprise",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now.AddDays(-30),
                RemainingDays = "335",
                AboutProduct = "Continuity Patrol™, part of Perpetuuiti's Resiliency Automation platform..."
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.1",
                LicenseType = "Standard",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now.AddDays(-60),
                RemainingDays = "305",
                AboutProduct = "Continuity Patrol™ Standard Edition..."
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedAboutCpList);

        // Act
        var result = await _controller.GetAboutCpList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var aboutCpList = Assert.IsAssignableFrom<List<GetAboutCpListVm>>(okResult.Value);
        Assert.Equal(2, aboutCpList.Count);
        Assert.Equal("6.0.0", aboutCpList[0].ProductVersion);
        Assert.Equal("Enterprise", aboutCpList[0].LicenseType);
    }

    [Fact]
    public async Task GetAboutCpList_ReturnsEmptyList_WhenNoAboutCpDataExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), default))
            .ReturnsAsync(new List<GetAboutCpListVm>());

        // Act
        var result = await _controller.GetAboutCpList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var aboutCpList = Assert.IsAssignableFrom<List<GetAboutCpListVm>>(okResult.Value);
        Assert.Empty(aboutCpList);
    }

    [Fact]
    public async Task GetAboutCpList_ReturnsOkResult_WithCorrectActionResultType()
    {
        // Arrange
        var expectedAboutCpList = new List<GetAboutCpListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.0",
                LicenseType = "Enterprise",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now,
                RemainingDays = "365",
                AboutProduct = "Test About Product"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedAboutCpList);

        // Act
        var result = await _controller.GetAboutCpList();

        // Assert
        Assert.IsType<ActionResult<List<GetAboutCpListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Equal(200, okResult.StatusCode);
    }

    [Fact]
    public async Task GetAboutCpList_CallsCorrectQuery()
    {
        // Arrange
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<GetAboutCpListVm>());

        // Act
        await _controller.GetAboutCpList();

        // Assert
        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAboutCpList_HandlesException_WhenMediatorThrows()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetAboutCpList());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public async Task GetAboutCpList_ReturnsCorrectDataStructure()
    {
        // Arrange
        var productId = Guid.NewGuid().ToString();
        var aboutCpId = Guid.NewGuid().ToString();
        var activationDate = DateTime.Now.AddDays(-100);

        var expectedAboutCpList = new List<GetAboutCpListVm>
        {
            new()
            {
                Id = aboutCpId,
                ProductVersion = "6.0.2",
                LicenseType = "Professional",
                ProductId = productId,
                ProductActivatedDate = activationDate,
                RemainingDays = "265",
                AboutProduct = "Continuity Patrol™ Professional Edition with advanced features"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedAboutCpList);

        // Act
        var result = await _controller.GetAboutCpList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var aboutCpList = Assert.IsAssignableFrom<List<GetAboutCpListVm>>(okResult.Value);
        var aboutCp = aboutCpList.First();

        Assert.Equal(aboutCpId, aboutCp.Id);
        Assert.Equal("6.0.2", aboutCp.ProductVersion);
        Assert.Equal("Professional", aboutCp.LicenseType);
        Assert.Equal(productId, aboutCp.ProductId);
        Assert.Equal(activationDate, aboutCp.ProductActivatedDate);
        Assert.Equal("265", aboutCp.RemainingDays);
        Assert.Contains("Professional Edition", aboutCp.AboutProduct);
    }

    [Fact]
    public async Task GetAboutCpList_HandlesNullProductActivatedDate()
    {
        // Arrange
        var expectedAboutCpList = new List<GetAboutCpListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.0",
                LicenseType = "Trial",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = null, // Null activation date
                RemainingDays = "30",
                AboutProduct = "Trial version"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedAboutCpList);

        // Act
        var result = await _controller.GetAboutCpList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var aboutCpList = Assert.IsAssignableFrom<List<GetAboutCpListVm>>(okResult.Value);
        var aboutCp = aboutCpList.First();

        Assert.Null(aboutCp.ProductActivatedDate);
        Assert.Equal("Trial", aboutCp.LicenseType);
        Assert.Equal("30", aboutCp.RemainingDays);
    }

    [Fact]
    public async Task GetAboutCpList_ReturnsMultipleItems_WithDifferentLicenseTypes()
    {
        // Arrange
        var expectedAboutCpList = new List<GetAboutCpListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.0",
                LicenseType = "Enterprise",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now.AddDays(-365),
                RemainingDays = "0",
                AboutProduct = "Enterprise Edition"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.0",
                LicenseType = "Standard",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now.AddDays(-180),
                RemainingDays = "185",
                AboutProduct = "Standard Edition"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.0",
                LicenseType = "Trial",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now.AddDays(-15),
                RemainingDays = "15",
                AboutProduct = "Trial Edition"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedAboutCpList);

        // Act
        var result = await _controller.GetAboutCpList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var aboutCpList = Assert.IsAssignableFrom<List<GetAboutCpListVm>>(okResult.Value);

        Assert.Equal(3, aboutCpList.Count);
        Assert.Contains(aboutCpList, x => x.LicenseType == "Enterprise");
        Assert.Contains(aboutCpList, x => x.LicenseType == "Standard");
        Assert.Contains(aboutCpList, x => x.LicenseType == "Trial");
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        var expectedCacheKey = ApplicationConstants.Cache.AllAboutCPCacheKey + companyId;

        // Note: Since ClearDataCache is a NonAction method and uses protected methods,
        // we can only test that it doesn't throw exceptions when called
        // The actual cache clearing logic would need integration tests

        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true); // Placeholder assertion since we can't directly test cache clearing
    }

    [Fact]
    public async Task GetAboutCpList_VerifiesQueryType()
    {
        // Arrange
        GetAboutCpListQuery capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<GetAboutCpListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetAboutCpListQuery;
            })
            .ReturnsAsync(new List<GetAboutCpListVm>());

        // Act
        await _controller.GetAboutCpList();

        // Assert
        Assert.NotNull(capturedQuery);
        Assert.IsType<GetAboutCpListQuery>(capturedQuery);
    }

    [Fact]
    public async Task GetAboutCpList_HandlesLargeDataSet()
    {
        // Arrange
        var largeAboutCpList = new List<GetAboutCpListVm>();
        for (int i = 0; i < 100; i++)
        {
            largeAboutCpList.Add(new GetAboutCpListVm
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = $"6.0.{i}",
                LicenseType = i % 3 == 0 ? "Enterprise" : i % 2 == 0 ? "Standard" : "Trial",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now.AddDays(-i),
                RemainingDays = (365 - i).ToString(),
                AboutProduct = $"About Product {i}"
            });
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(largeAboutCpList);

        // Act
        var result = await _controller.GetAboutCpList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var aboutCpList = Assert.IsAssignableFrom<List<GetAboutCpListVm>>(okResult.Value);
        Assert.Equal(100, aboutCpList.Count);
    }

    [Fact]
    public async Task GetAboutCpList_HandlesSpecialCharactersInData()
    {
        // Arrange
        var expectedAboutCpList = new List<GetAboutCpListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.0-β",
                LicenseType = "Enterprise™",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now,
                RemainingDays = "365",
                AboutProduct = "Continuity Patrol™ with special characters: àáâãäåæçèéêë & symbols: @#$%^&*()"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedAboutCpList);

        // Act
        var result = await _controller.GetAboutCpList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var aboutCpList = Assert.IsAssignableFrom<List<GetAboutCpListVm>>(okResult.Value);
        var aboutCp = aboutCpList.First();

        Assert.Contains("β", aboutCp.ProductVersion);
        Assert.Contains("™", aboutCp.LicenseType);
        Assert.Contains("àáâãäåæçèéêë", aboutCp.AboutProduct);
        Assert.Contains("@#$%^&*()", aboutCp.AboutProduct);
    }

    [Fact]
    public async Task GetAboutCpList_HandlesEmptyStrings()
    {
        // Arrange
        var expectedAboutCpList = new List<GetAboutCpListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "",
                LicenseType = "",
                ProductId = "",
                ProductActivatedDate = DateTime.Now,
                RemainingDays = "",
                AboutProduct = ""
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedAboutCpList);

        // Act
        var result = await _controller.GetAboutCpList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var aboutCpList = Assert.IsAssignableFrom<List<GetAboutCpListVm>>(okResult.Value);
        var aboutCp = aboutCpList.First();

        Assert.Equal(string.Empty, aboutCp.ProductVersion);
        Assert.Equal(string.Empty, aboutCp.LicenseType);
        Assert.Equal(string.Empty, aboutCp.ProductId);
        Assert.Equal(string.Empty, aboutCp.RemainingDays);
        Assert.Equal(string.Empty, aboutCp.AboutProduct);
    }

    [Fact]
    public async Task GetAboutCpList_HandlesVeryLongText()
    {
        // Arrange
        var longText = new string('A', 10000); // 10,000 character string
        var expectedAboutCpList = new List<GetAboutCpListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.0",
                LicenseType = "Enterprise",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now,
                RemainingDays = "365",
                AboutProduct = longText
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedAboutCpList);

        // Act
        var result = await _controller.GetAboutCpList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var aboutCpList = Assert.IsAssignableFrom<List<GetAboutCpListVm>>(okResult.Value);
        var aboutCp = aboutCpList.First();

        Assert.Equal(10000, aboutCp.AboutProduct.Length);
        Assert.True(aboutCp.AboutProduct.All(c => c == 'A'));
    }

    [Fact]
    public async Task GetAboutCpList_HandlesNegativeRemainingDays()
    {
        // Arrange
        var expectedAboutCpList = new List<GetAboutCpListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.0",
                LicenseType = "Trial",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now.AddDays(-400),
                RemainingDays = "-35", // Expired license
                AboutProduct = "Expired trial version"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedAboutCpList);

        // Act
        var result = await _controller.GetAboutCpList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var aboutCpList = Assert.IsAssignableFrom<List<GetAboutCpListVm>>(okResult.Value);
        var aboutCp = aboutCpList.First();

        Assert.Equal("-35", aboutCp.RemainingDays);
        Assert.Equal("Trial", aboutCp.LicenseType);
    }

    [Fact]
    public async Task GetAboutCpList_VerifiesMediatorCalledOnce()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<GetAboutCpListVm>());

        // Act
        await _controller.GetAboutCpList();

        // Assert
        _mediatorMock.Verify(
            m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GetAboutCpList_DoesNotCallMediatorMultipleTimes()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<GetAboutCpListVm>());

        // Act
        await _controller.GetAboutCpList();
        await _controller.GetAboutCpList();

        // Assert
        _mediatorMock.Verify(
            m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()),
            Times.Exactly(2));
    }

   

    [Fact]
    public async Task GetAboutCpList_HandlesExceptionFromMediator()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.GetAboutCpList());
    }

    [Fact]
    public async Task GetAboutCpList_HandlesTaskCancellation()
    {
        // Arrange
        var cancellationToken = new CancellationToken(true);
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new TaskCanceledException("Operation was cancelled"));

        // Act & Assert
        await Assert.ThrowsAsync<TaskCanceledException>(() => _controller.GetAboutCpList());
    }

    [Fact]
    public async Task GetAboutCpList_ReturnsConsistentResults()
    {
        // Arrange
        var expectedAboutCpList = _aboutCpFixture.AboutCpListVm;
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedAboutCpList);

        // Act
        var result1 = await _controller.GetAboutCpList();
        var result2 = await _controller.GetAboutCpList();

        // Assert
        var okResult1 = Assert.IsType<OkObjectResult>(result1.Result);
        var okResult2 = Assert.IsType<OkObjectResult>(result2.Result);

        var aboutCpList1 = Assert.IsAssignableFrom<List<GetAboutCpListVm>>(okResult1.Value);
        var aboutCpList2 = Assert.IsAssignableFrom<List<GetAboutCpListVm>>(okResult2.Value);

        Assert.Equal(aboutCpList1.Count, aboutCpList2.Count);
        Assert.Equal(aboutCpList1.First().Id, aboutCpList2.First().Id);
    }

    [Fact]
    public async Task GetAboutCpList_ValidatesProductVersionFormat()
    {
        // Arrange
        var expectedAboutCpList = new List<GetAboutCpListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ProductVersion = "6.0.0.1234", // Detailed version format
                LicenseType = "Enterprise",
                ProductId = Guid.NewGuid().ToString(),
                ProductActivatedDate = DateTime.Now,
                RemainingDays = "365",
                AboutProduct = "Enterprise Edition"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAboutCpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedAboutCpList);

        // Act
        var result = await _controller.GetAboutCpList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var aboutCpList = Assert.IsAssignableFrom<List<GetAboutCpListVm>>(okResult.Value);
        var aboutCp = aboutCpList.First();

        Assert.Matches(@"^\d+\.\d+\.\d+\.\d+$", aboutCp.ProductVersion);
    }
}
