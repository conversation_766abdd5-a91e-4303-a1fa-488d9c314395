﻿using ContinuityPatrol.Domain.ViewModels.WorkflowCategoryModel;
using ContinuityPatrol.Domain.Views;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.WorkflowCategory.Queries.GetWorkflowCategoryViewList;

public class
    WorkflowCategoryViewQueryHandler : IRequestHandler<GetWorkflowCategoryViewQuery, List<WorkflowCategoryViewListVm>>
{
    private readonly IWorkflowCategoryViewRepository _workflowCategoryViewRepository;

    public WorkflowCategoryViewQueryHandler(IWorkflowCategoryViewRepository workflowCategoryViewRepository)
    {
        _workflowCategoryViewRepository = workflowCategoryViewRepository;
    }

    public async Task<List<WorkflowCategoryViewListVm>> Handle(GetWorkflowCategoryViewQuery request,
        CancellationToken cancellationToken)
    {
        #region old code
        //    var workflowCategoryViewList = await _workflowCategoryViewRepository.GetAllWorkflowCategoriesAsync();

        //    var wfCategoryList = workflowCategoryViewList;
        //    var rootNodes = wfCategoryList.Where(x => x.Level == 1).ToList();

        //    var result = new List<WorkflowCategoryViewListVm>();
        //    foreach (var rootNode in rootNodes)
        //    {
        //        var rootVm = new WorkflowCategoryViewListVm
        //        {
        //            CategoryName = rootNode.Name, NodeId = rootNode.NodeId,
        //            CategoryId = rootNode.ReferenceId,
        //            NodeName = rootNode.NodeName,
        //            Color = rootNode.Color,
        //            Icon = rootNode.Icon,
        //            WorkflowCategoryBaseChildViewListVms = new List<WorkflowCategoryBaseChildViewListVm>()
        //        };

        //        BuildBaseHierarchyViewModels(rootNode, wfCategoryList, rootVm.WorkflowCategoryBaseChildViewListVms);
        //        result.Add(rootVm);
        //    }

        //    return result;
        //}

        //private void BuildBaseHierarchyViewModels(WorkflowCategoryView node, List<WorkflowCategoryView> allNodes,
        //    List<WorkflowCategoryBaseChildViewListVm> result)
        //{
        //    var children = allNodes.Where(x => x.ParentTitle == node.NodeName && x.Level == node.Level + 1).ToList();
        //    foreach (var child in children)
        //        if (!result.Any(x => x.Name == child.NodeName && x.Id == child.NodeId))
        //        {
        //            var baseChildVm = new WorkflowCategoryBaseChildViewListVm
        //            {
        //                Id = child?.NodeId,
        //                Name = child?.NodeName,
        //                Color = child?.Color,
        //                Icon = child?.Icon,
        //                WorkflowCategoryChildViewListVms = new List<WorkflowCategoryChildViewListVm>()
        //            };

        //            BuildChildHierarchyViewModels(child, allNodes, baseChildVm.WorkflowCategoryChildViewListVms);
        //            result.Add(baseChildVm);
        //        }
        //}

        //private void BuildChildHierarchyViewModels(WorkflowCategoryView node, List<WorkflowCategoryView> allNodes,
        //    List<WorkflowCategoryChildViewListVm> result)
        //{
        //    var children = allNodes.Where(x => x.ParentTitle == node.NodeName && x.Level == node.Level + 1).ToList();
        //    foreach (var child in children)
        //        if (!result.Any(x => x.Name == child.NodeName && x.Id == child.NodeId))
        //        {
        //            var childVm = new WorkflowCategoryChildViewListVm
        //            {
        //                Id = child?.NodeId,
        //                Name = child?.NodeName,
        //                Color = child?.Color,
        //                Icon = child?.Icon,
        //                ActionLists = child?.Level == 3 && child.ActionName.IsNotNullOrWhiteSpace()
        //                    ? new List<ActionList> { new() { ActionName = child.ActionName, ActionId = child.ActionId } }
        //                    : new List<ActionList>()
        //            };

        //            if (child?.Level != 3) BuildChildHierarchyViewModels(child, allNodes, result);

        //            result.Add(childVm);
        //        }
        //        else
        //        {
        //            var existingChildVm = result.First(x => x.Name == child.NodeName && x.Id == child.NodeId);
        //            if (child.Level == 3 && !existingChildVm.ActionLists.Any(a =>
        //                    a.ActionName == child.ActionName && a.ActionId == child.ActionId))
        //                existingChildVm.ActionLists.Add(new ActionList
        //                    { ActionName = child.ActionName, ActionId = child.ActionId });
        //        }
        //}
        #endregion

        #region  Refactored Correct Code  For New View
        var workflowCategoryViewList = await _workflowCategoryViewRepository.GetAllWorkflowCategoriesAsync();

        var wfCategoryList = workflowCategoryViewList.Select(x =>
        {
            var data = JsonConvert.DeserializeObject<WorkflowCategoryViewListVm>(x.UpdatedJson);

            data.CategoryName = x.Name;
            data.CategoryId = x.ReferenceId;

            return data;

        }).ToList();


        return wfCategoryList;
    }
        #endregion

        #region Correct Code after Old Code

        //    var workflowCategoryViewList = await _workflowCategoryViewRepository.GetAllWorkflowCategoriesAsync();

        //    var wfCategoryList = workflowCategoryViewList;
        //    var rootNodes = wfCategoryList.Where(x => x.Level == 1).ToList();

        //    var result = new List<WorkflowCategoryViewListVm>();
        //    foreach (var rootNode in rootNodes)
        //    {
        //        var rootVm = new WorkflowCategoryViewListVm
        //        {
        //            CategoryName = rootNode.Name,
        //            NodeId = rootNode.NodeId,
        //            CategoryId = rootNode.ReferenceId,
        //            NodeName = rootNode.NodeName,
        //            Color = rootNode.Color,
        //            Icon = rootNode.Icon,
        //            WorkflowCategoryBaseChildViewListVms = new List<WorkflowCategoryBaseChildViewListVm>()
        //        };

        //        BuildBaseHierarchyViewModels(rootNode, wfCategoryList, rootVm.WorkflowCategoryBaseChildViewListVms);
        //        result.Add(rootVm);
        //    }

        //    return result;
        //}

        //private void BuildBaseHierarchyViewModels(WorkflowCategoryView node, List<WorkflowCategoryView> allNodes,
        //    List<WorkflowCategoryBaseChildViewListVm> result)
        //{

        //    var children = allNodes.Where(x => x.ParentId == node.NodeId && x.ParentTitle == node.NodeName && x.Level == node.Level + 1).ToList();
        //    foreach (var child in children)
        //        if (!result.Any(x => x.Name == child.NodeName && x.Id == child.NodeId))
        //        {
        //            var baseChildVm = new WorkflowCategoryBaseChildViewListVm
        //            {
        //                Id = child?.NodeId,
        //                Name = child?.NodeName,
        //                Color = child?.Color,
        //                Icon = child?.Icon,
        //                WorkflowCategoryChildViewListVms = new List<WorkflowCategoryChildViewListVm>()
        //            };

        //            BuildChildHierarchyViewModels(child, allNodes, baseChildVm.WorkflowCategoryChildViewListVms);
        //            result.Add(baseChildVm);
        //        }


        //}

        //private void BuildChildHierarchyViewModels(WorkflowCategoryView node, List<WorkflowCategoryView> allNodes,
        //    List<WorkflowCategoryChildViewListVm> result)
        //{
        //    var children = allNodes.Where(x => x.ParentId == node.NodeId && x.ParentTitle == node.NodeName && x.Level == node.Level + 1).ToList();
        //    foreach (var child in children)
        //        if (!result.Any(x => x.Name == child.NodeName && x.Id == child.NodeId))
        //        {
        //            var childVm = new WorkflowCategoryChildViewListVm
        //            {
        //                Id = child?.NodeId,
        //                Name = child?.NodeName,
        //                Color = child?.Color,
        //                Icon = child?.Icon,
        //                ActionLists = child?.Level == 3 && child.ActionName.IsNotNullOrWhiteSpace()
        //                    ? new List<ActionList> { new() { ActionName = child.ActionName, ActionId = child.ActionId } }
        //                    : new List<ActionList>()
        //            };

        //            if (child?.Level != 3) BuildChildHierarchyViewModels(child, allNodes, result);

        //            result.Add(childVm);
        //        }
        //        else
        //        {
        //            var existingChildVm = result.First(x => x.Name == child.NodeName && x.Id == child.NodeId);
        //            if (child.Level == 3 && !existingChildVm.ActionLists.Any(a =>
        //                    a.ActionName == child.ActionName && a.ActionId == child.ActionId))
        //                existingChildVm.ActionLists.Add(new ActionList
        //                { ActionName = child.ActionName, ActionId = child.ActionId });
        //        }
        //}
        #endregion
    }