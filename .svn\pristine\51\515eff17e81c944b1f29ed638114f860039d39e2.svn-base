﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;

namespace ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetType;

public class GetHeatMapStatusTypeQueryHandler : IRequestHandler<GetHeatMapStatusTypeQuery, List<HeatMapStatusListVm>>
{
    private readonly IHeatMapStatusViewRepository _heatMapStatusViewRepository;
    private readonly IMapper _mapper;

    public GetHeatMapStatusTypeQueryHandler(IMapper mapper, IHeatMapStatusViewRepository heatMapStatusViewRepository)
    {
        _mapper = mapper;
        _heatMapStatusViewRepository = heatMapStatusViewRepository;
    }

    public async Task<List<HeatMapStatusListVm>> Handle(GetHeatMapStatusTypeQuery request,
        CancellationToken cancellationToken)
    {
        var heatMapStatus = request.BusinessServiceId.IsNotNullOrWhiteSpace()
            ? await _heatMapStatusViewRepository.GetHeatMapStatusesByBusinessServiceIdAndType(request.BusinessServiceId, request.Type,request.IsAffected)
            : await _heatMapStatusViewRepository.GetHeatMapStatusTypeAsync(request.Type,request.IsAffected);

        return heatMapStatus.Count <= 0
            ? new List<HeatMapStatusListVm>()
            : _mapper.Map<List<HeatMapStatusListVm>>(heatMapStatus);
    }
}