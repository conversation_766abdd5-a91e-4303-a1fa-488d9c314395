using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FiaCost.Events.Update;

public class FiaCostUpdatedEventHandler : INotificationHandler<FiaCostUpdatedEvent>
{
    private readonly ILogger<FiaCostUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FiaCostUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<FiaCostUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(FiaCostUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} FiaCost",
            Entity = "FiaCost",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"FiaCost '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"FiaCost '{updatedEvent.Name}' updated successfully.");
    }
}
