using AutoFixture;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class InfraDashboardViewRepositoryTests : IClassFixture<InfraDashboardViewFixture>, IDisposable
{
    private readonly InfraDashboardViewFixture _infraDashboardViewFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly InfraDashboardViewRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public InfraDashboardViewRepositoryTests(InfraDashboardViewFixture infraDashboardViewFixture)
    {
        _infraDashboardViewFixture = infraDashboardViewFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new InfraDashboardViewRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.InfraDashboardViews.RemoveRange(_dbContext.InfraDashboardViews);
        await _dbContext.SaveChangesAsync();
    }

    #region GetDrReadyInfraObjectList Tests

    [Fact]
    public async Task GetDrReadyInfraObjectList_ReturnsOnlyDrReadyObjects_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var infraDashboardViews = new List<InfraDashboardView>
        {
            new InfraDashboardView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "DR Ready Object 1",
                DRReady = true,
                BusinessServiceId = "BS_123",
                BusinessServiceName = "Service 1",
                IsActive = true
            },
            new InfraDashboardView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Non-DR Ready Object",
                DRReady = false,
                BusinessServiceId = "BS_456",
                BusinessServiceName = "Service 2",
                IsActive = true
            },
            new InfraDashboardView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "DR Ready Object 2",
                DRReady = true,
                BusinessServiceId = "BS_789",
                BusinessServiceName = "Service 3",
                IsActive = true
            }
        };

        await _dbContext.InfraDashboardViews.AddRangeAsync(infraDashboardViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDrReadyInfraObjectList();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.DRReady));
        Assert.Contains(result, x => x.Name == "DR Ready Object 1");
        Assert.Contains(result, x => x.Name == "DR Ready Object 2");
        Assert.DoesNotContain(result, x => x.Name == "Non-DR Ready Object");
    }

    [Fact]
    public async Task GetDrReadyInfraObjectList_FiltersCompanyId_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var infraDashboardViews = new List<InfraDashboardView>
        {
            new InfraDashboardView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Company DR Ready Object",
                CompanyId = companyId,
                DRReady = true,
                BusinessServiceId = "BS_123",
                BusinessServiceName = "Service 1",
                IsActive = true
            },
            new InfraDashboardView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Different Company DR Ready Object",
                CompanyId = "COMPANY_456",
                DRReady = true,
                BusinessServiceId = "BS_456",
                BusinessServiceName = "Service 2",
                IsActive = true
            }
        };

        await _dbContext.InfraDashboardViews.AddRangeAsync(infraDashboardViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDrReadyInfraObjectList();

        // Assert
        Assert.Single(result);
        Assert.Equal("Company DR Ready Object", result.First().Name);
        Assert.Equal(companyId, result.First().CompanyId);
        Assert.True(result.First().DRReady);
    }

    [Fact]
    public async Task GetDrReadyInfraObjectList_ReturnsEmpty_WhenNoDrReadyObjects()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var infraDashboardView = new InfraDashboardView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Non-DR Ready Object",
            DRReady = false,
            BusinessServiceId = "BS_123",
            BusinessServiceName = "Service 1",
            IsActive = true
        };

        await _dbContext.InfraDashboardViews.AddAsync(infraDashboardView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDrReadyInfraObjectList();

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetCurrentRtoByBusinessServiceId Tests

    [Fact]
    public async Task GetCurrentRtoByBusinessServiceId_ReturnsCorrectRtoCalculation_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        var businessServiceId = "BS_123";
        var woGroupInfraObjectIds = new List<string> { "INFRA_001", "INFRA_002", "INFRA_003" };

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var infraDashboardViews = new List<InfraDashboardView>
        {
            new InfraDashboardView
            {
                ReferenceId = "INFRA_001",
                BusinessServiceId = businessServiceId,
                CurrentRTO = "30",
                ConfiguredRTO = "60",
                IsActive = true
            },
            new InfraDashboardView
            {
                ReferenceId = "INFRA_002",
                BusinessServiceId = businessServiceId,
                CurrentRTO = "45",
                ConfiguredRTO = "60",
                IsActive = true
            },
            new InfraDashboardView
            {
                ReferenceId = "INFRA_003",
                BusinessServiceId = businessServiceId,
                CurrentRTO = "75",
                ConfiguredRTO = "60",
                IsActive = true
            },
            new InfraDashboardView
            {
                ReferenceId = "INFRA_004", // Not in woGroupInfraObjectIds
                BusinessServiceId = businessServiceId,
                CurrentRTO = "90",
                ConfiguredRTO = "60",
                IsActive = true
            }
        };

        await _dbContext.InfraDashboardViews.AddRangeAsync(infraDashboardViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetCurrentRtoByBusinessServiceId(businessServiceId, woGroupInfraObjectIds);

        // Assert
        Assert.Equal(businessServiceId, result.BusinessServiceId);
        Assert.Equal(2, result.RtoAchieved); // 30 and 45 are <= 60
        Assert.Equal(1, result.RtoExceeded); // 75 is > 60
    }

    [Fact]
    public async Task GetCurrentRtoByBusinessServiceId_FiltersCompanyId_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        var businessServiceId = "BS_123";
        var companyId = "COMPANY_123";
        var woGroupInfraObjectIds = new List<string> { "INFRA_001", "INFRA_002" };

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);

        var infraDashboardViews = new List<InfraDashboardView>
        {
            new InfraDashboardView
            {
                ReferenceId = "INFRA_001",
                BusinessServiceId = businessServiceId,
                CompanyId = companyId,
                CurrentRTO = "30",
                ConfiguredRTO = "60",
                IsActive = true
            },
            new InfraDashboardView
            {
                ReferenceId = "INFRA_002",
                BusinessServiceId = businessServiceId,
                CompanyId = "COMPANY_456", // Different company
                CurrentRTO = "45",
                ConfiguredRTO = "60",
                IsActive = true
            }
        };

        await _dbContext.InfraDashboardViews.AddRangeAsync(infraDashboardViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetCurrentRtoByBusinessServiceId(businessServiceId, woGroupInfraObjectIds);

        // Assert
        Assert.Equal(businessServiceId, result.BusinessServiceId);
        Assert.Equal(1, result.RtoAchieved); // Only INFRA_001 from correct company
        Assert.Equal(0, result.RtoExceeded);
    }

    #endregion

    #region GetInfraObjectViewByBusinessFunctionId Tests

    [Fact]
    public async Task GetInfraObjectViewByBusinessFunctionId_ReturnsMatchingObjects_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        var businessFunctionIds = new List<string> { "BF_123", "BF_456" };

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var infraDashboardViews = new List<InfraDashboardView>
        {
            new InfraDashboardView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessFunctionId = "BF_123",
                BusinessFunctionName = "Function 1",
                Name = "Object 1",
                Status = "Online",
                IsActive = true
            },
            new InfraDashboardView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessFunctionId = "BF_456",
                BusinessFunctionName = "Function 2",
                Name = "Object 2",
                Status = "Offline",
                IsActive = true
            },
            new InfraDashboardView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessFunctionId = "BF_789", // Not in the list
                BusinessFunctionName = "Function 3",
                Name = "Object 3",
                Status = "Online",
                IsActive = true
            }
        };

        await _dbContext.InfraDashboardViews.AddRangeAsync(infraDashboardViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraObjectViewByBusinessFunctionId(businessFunctionIds);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.BusinessFunctionId == "BF_123");
        Assert.Contains(result, x => x.BusinessFunctionId == "BF_456");
        Assert.DoesNotContain(result, x => x.BusinessFunctionId == "BF_789");
        Assert.Contains(result, x => x.Name == "Object 1");
        Assert.Contains(result, x => x.Name == "Object 2");
    }

    #endregion

    #region GetInfraObjectDetailsById Tests

    [Fact]
    public async Task GetInfraObjectDetailsById_ReturnsObjectDetails_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var infraDashboardView = new InfraDashboardView
        {
            ReferenceId = infraObjectId,
            Name = "Test Infrastructure",
            DROperationStatus = 1,
            ReplicationStatus = 2,
            ReplicationTypeName = "Database Replication",
            ReplicationCategoryType = "Synchronous",
            Properties = "Test Properties",
            ServerProperties = "Server Props",
            DatabaseProperties = "DB Props",
            ReplicationProperties = "Replication Props",
            DataLagValue = "5",
            ConfiguredRTO = "60",
            ConfiguredRPO = "30",
            CurrentRPO = "25",
            CurrentRTO = "45",
            RPOThreshold = "35",
            EstimatedRTO = "50",
            IsActive = true
        };

        await _dbContext.InfraDashboardViews.AddAsync(infraDashboardView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraObjectDetailsById(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.ReferenceId);
        Assert.Equal(1, result.DROperationStatus);
        Assert.Equal(2, result.ReplicationStatus);
        Assert.Equal("Database Replication", result.ReplicationTypeName);
        Assert.Equal("Synchronous", result.ReplicationCategoryType);
        Assert.Equal("Test Properties", result.Properties);
        Assert.Equal("Server Props", result.ServerProperties);
        Assert.Equal("DB Props", result.DatabaseProperties);
        Assert.Equal("Replication Props", result.ReplicationProperties);
        Assert.Equal("5", result.DataLagValue);
        Assert.Equal("60", result.ConfiguredRTO);
        Assert.Equal("30", result.ConfiguredRPO);
        Assert.Equal("25", result.CurrentRPO);
        Assert.Equal("45", result.CurrentRTO);
        Assert.Equal("35", result.RPOThreshold);
        Assert.Equal("50", result.EstimatedRTO);
    }

    [Fact]
    public async Task GetInfraObjectDetailsById_ReturnsNull_WhenObjectNotFound()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentId = "INFRA_999";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectDetailsById(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetInfraObjectViewByInfraObjectIds Tests

    [Fact]
    public async Task GetInfraObjectViewByInfraObjectIds_ReturnsMatchingObjects_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        var infraIds = new List<string> { "INFRA_001", "INFRA_002", "INFRA_003" };

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var infraDashboardViews = new List<InfraDashboardView>
        {
            new InfraDashboardView
            {
                ReferenceId = "INFRA_001",
                Name = "Object 1",
                Status = "Online",
                ConfiguredRTO = "60",
                IsAffected = false,
                IsActive = true
            },
            new InfraDashboardView
            {
                ReferenceId = "INFRA_002",
                Name = "Object 2",
                Status = "Offline",
                ConfiguredRTO = "120",
                IsAffected = true,
                IsActive = true
            },
            new InfraDashboardView
            {
                ReferenceId = "INFRA_004", // Not in the list
                Name = "Object 4",
                Status = "Online",
                ConfiguredRTO = "90",
                IsAffected = false,
                IsActive = true
            },
            new InfraDashboardView
            {
                ReferenceId = "", // Empty ReferenceId should be excluded
                Name = "Object Empty",
                Status = "Online",
                IsActive = true
            }
        };

        await _dbContext.InfraDashboardViews.AddRangeAsync(infraDashboardViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraObjectViewByInfraObjectIds(infraIds);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.ReferenceId == "INFRA_001");
        Assert.Contains(result, x => x.ReferenceId == "INFRA_002");
        Assert.DoesNotContain(result, x => x.ReferenceId == "INFRA_004");
        Assert.DoesNotContain(result, x => x.ReferenceId == "");
        Assert.Contains(result, x => x.Name == "Object 1");
        Assert.Contains(result, x => x.Name == "Object 2");
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddInfraDashboardView_WhenValidInfraDashboardView()
    {
        // Arrange
        await ClearDatabase();
        var infraDashboardView = new InfraDashboardView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            Name = "Test Infrastructure",
            Description = "Test infrastructure description",
            BusinessServiceId = "BS_123",
            BusinessServiceName = "Test Business Service",
            BusinessFunctionId = "BF_123",
            BusinessFunctionName = "Test Business Function",
            Status = "Online",
            DRReady = true,
            DROperationStatus = 1,
            ReplicationStatus = 1,
            ConfiguredRTO = "60",
            ConfiguredRPO = "30",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(infraDashboardView);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraDashboardView.Name, result.Name);
        Assert.Equal(infraDashboardView.Description, result.Description);
        Assert.Equal(infraDashboardView.BusinessServiceName, result.BusinessServiceName);
        Assert.Equal(infraDashboardView.BusinessFunctionName, result.BusinessFunctionName);
        Assert.Equal(infraDashboardView.Status, result.Status);
        Assert.Equal(infraDashboardView.DRReady, result.DRReady);
        Assert.Equal(infraDashboardView.ConfiguredRTO, result.ConfiguredRTO);
        Assert.Equal(infraDashboardView.ConfiguredRPO, result.ConfiguredRPO);
        Assert.Single(_dbContext.InfraDashboardViews);
    }

    #endregion
}
