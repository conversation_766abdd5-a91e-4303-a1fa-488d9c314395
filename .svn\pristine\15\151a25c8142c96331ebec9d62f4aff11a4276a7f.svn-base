﻿namespace ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDrReadinessByBusinessServices;

public class GetDrReadinessByBusinessServiceQueryHandler : IRequestHandler<GetDrReadinessByBusinessServiceQuery,
    GetDrReadinessByBusinessServiceVm>
{
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IDrReadyStatusRepository _drReadyStatusRepository;
    private readonly IBusinessFunctionRepository _functionRepository;
    private readonly IHeatMapStatusViewRepository _heatMapStatusViewRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IMapper _mapper;
    private readonly IMonitorServiceRepository _monitorServiceRepository;
    private readonly IWorkflowInfraObjectRepository _workflowInfraObjectRepository;

    public GetDrReadinessByBusinessServiceQueryHandler(I<PERSON><PERSON><PERSON> mapper, IHeatMapStatusViewRepository heatMapStatusViewRepository,
        IBusinessServiceRepository businessServiceRepository, IBusinessFunctionRepository businessFunctionRepository,
        IInfraObjectRepository infraObjectRepository, IDrReadyStatusRepository dRReadyStatusRepository,
        IWorkflowInfraObjectRepository workflowInfraObjectRepository,
        IMonitorServiceRepository monitorServiceRepository)
    {
        _mapper = mapper;
        _heatMapStatusViewRepository = heatMapStatusViewRepository;
        _businessServiceRepository = businessServiceRepository;
        _functionRepository = businessFunctionRepository;
        _infraObjectRepository = infraObjectRepository;
        _drReadyStatusRepository = dRReadyStatusRepository;
        _workflowInfraObjectRepository = workflowInfraObjectRepository;
        _monitorServiceRepository = monitorServiceRepository;
    }

    public async Task<GetDrReadinessByBusinessServiceVm> Handle(GetDrReadinessByBusinessServiceQuery request,
        CancellationToken cancellationToken)
    {
        var drReady = new GetDrReadinessByBusinessServiceVm();

        var businessServices = request.BusinessServiceId.IsNullOrWhiteSpace()
            ? (await _businessServiceRepository.ListAllAsync()).ToList()
            : await _businessServiceRepository.GetByReferenceIdAsync(request.BusinessServiceId) is not null
                ? new List<Domain.Entities.BusinessService>
                    { await _businessServiceRepository.GetByReferenceIdAsync(request.BusinessServiceId) }
                : new List<Domain.Entities.BusinessService>();

        drReady.TotalBusinessServiceCount = businessServices.Count;

        var heatMapStatus = request.BusinessServiceId.IsNullOrWhiteSpace()
            ? await _heatMapStatusViewRepository.ListAllAsync()
            : await _heatMapStatusViewRepository.GetHeatMapListByBusinessServiceId(request.BusinessServiceId);

        //var heatMapStatus = await _heatMapStatusRepository.ListAllAsync();

        var heatMap = _mapper.Map<Components>(heatMapStatus);

        drReady.Components = heatMap;

        foreach(var bs in businessServices)
        {
            var businessFunction =await
                _functionRepository.GetBusinessFunctionListByBusinessServiceId(bs.ReferenceId);

            foreach(var bf in businessFunction)
            {
                var infraObject =await _infraObjectRepository.GetInfraObjectByBusinessFunctionId(bf.ReferenceId);

                var drReadyInfra = infraObject.Where(x => x.DRReady).ToList();

                var infraDrDready = _mapper.Map<List<GetTotalDrReadyVm>>(drReadyInfra);

                drReady.TotalDrReadyInfraObjectCount += drReadyInfra.Count;

                drReady.Orchestration.TotalDrReadyCount += drReadyInfra.Count;

                drReady.Orchestration.GetTotalDrReadyVms.AddRange(infraDrDready);

                foreach(var dr in drReadyInfra)
                {
                    var monitorService = await _monitorServiceRepository.GetMonitorServiceByInfraObjectId(dr.ReferenceId);

                    foreach (var service in monitorService)
                    {
                        if (service.IsServiceUpdate.IsNotNullOrWhiteSpace())
                        {
                            var data = JArray.Parse(service.IsServiceUpdate);

                            var allFilteredServices = data.SelectMany(server =>
                            {
                                var type = server["Type"]?.ToString();
                                var ip = server["IpAddress"]?.ToString();
                                var services = server["Services"]?.ToList() ?? new List<JToken>();

                                var filtered = type == "DR"
                                          ? services.Where(s =>
                                              s["Status"]?.ToString() != "Stopped" &&
                                              !s["Status"]?.ToString().Equals("Running", StringComparison.OrdinalIgnoreCase) == true)
                                          : services;

                                return filtered.Select(s => new
                                {
                                    Type = type,
                                    IpAddress = ip,
                                    ServiceName = s["ServiceName"]?.ToString(),
                                    Status = s["Status"]?.ToString(),
                                    ErrorMessage = s["ErrorMessage"]?.ToString() ?? string.Empty
                                });

                            }).ToList();


                            var serviceError = allFilteredServices.Where(x => x.Status.ToLower() != "running").ToList();
                            var serviceNotReady = allFilteredServices.Where(x => x.Status.ToLower().Equals("stopped"))
                                .ToList();

                            drReady.RelatedServiceCount += allFilteredServices.Count;

                            drReady.RelatedService.RelatedServiceErrorCount += serviceError.Count;

                            drReady.RelatedService.RelatedServiceNotReadyCount +=
                                allFilteredServices.Count(x => x.Status.ToLower().Equals("stopped"));

                            var serviceErrorProperties = serviceError.Select(x => new ServiceProperties
                            {
                                Type = x.Type,
                                IpAddress =x.IpAddress,
                                ServiceName = x.ServiceName,
                                Status = x.Status,
                                ErrorMessage = x.ErrorMessage
                            }).ToList();


                            var serviceNotReadyProperties = serviceNotReady.Select(x => new ServiceProperties
                            {
                                Type = x.Type,
                                IpAddress = x.IpAddress,
                                ServiceName = x.ServiceName,
                                Status = x.Status,
                                ErrorMessage = x.ErrorMessage
                            }).ToList();


                            if(serviceError.Count > 0)
                            {
                                var drReadyErrorService = new RelatedServiceDto
                                {
                                    Id = service.ReferenceId,
                                    BusinessServiceId = service.BusinessServiceId,
                                    BusinessServiceName = service.BusinessServiceName,
                                    InfraObjectId = service.InfraObjectId,
                                    InfraObjectName = service.InfraObjectName,
                                    ServerId = service.ServerId,
                                    ServerName = service.ServerName,
                                    WorkflowId = service.WorkflowId,
                                    WorkflowName = service.WorkflowName,
                                    ServiceProperties = serviceErrorProperties,
                                    ServicePath = service.ServicePath,
                                    Type = service.Type,
                                    ThreadType = service.ThreadType,
                                    Status = service.Status,
                                    WorkflowType = service.WorkflowType,
                                    IsServiceUpdate = service.IsServiceUpdate
                                };

                                drReady.RelatedService.RelatedErrorService.AddRange(drReadyErrorService);
                            }

                            
                            if(serviceNotReady.Count > 0)
                            {
                                var notReadyService = new RelatedServiceDto
                                {
                                    Id = service.ReferenceId,
                                    BusinessServiceId = service.BusinessServiceId,
                                    BusinessServiceName = service.BusinessServiceName,
                                    InfraObjectId = service.InfraObjectId,
                                    InfraObjectName = service.InfraObjectName,
                                    ServerId = service.ServerId,
                                    ServerName = service.ServerName,
                                    WorkflowId = service.WorkflowId,
                                    WorkflowName = service.WorkflowName,
                                    ServiceProperties = serviceNotReadyProperties,
                                    ServicePath = service.ServicePath,
                                    Type = service.Type,
                                    ThreadType = service.ThreadType,
                                    Status = service.Status,
                                    WorkflowType = service.WorkflowType,
                                    IsServiceUpdate = service.IsServiceUpdate
                                };

                                drReady.RelatedService.RelatedNotReadyService.AddRange(notReadyService);
                            }
                        }
                        else
                        {
                            var jObject = JObject.Parse(service.Properties);
                            var count = jObject["details"]?.Count() ?? 0;

                            drReady.RelatedServiceCount += count;
                        }
                    }



                    //drReady.RelatedServiceCount += monitorService.Count;

                    //drReady.RelatedService.RelatedServiceNotReadyCount += monitorService.Count(x =>
                    //    x.Status.Trim().ToLower().Equals("stopped"));

                    //drReady.RelatedService.RelatedServiceErrorCount += monitorService.Count(x =>
                    //    !x.Status.Trim().ToLower().Equals("stopped") &&
                    //    (x.IsServiceUpdate?.Trim().ToLower()!= "running" || x.IsServiceUpdate == null));

                    //var drReadyService = _mapper.Map<List<RelatedServiceDto>>(monitorService.Where(x =>
                    //    !x.Status.Trim().ToLower().Equals("stopped") &&
                    //    (x.IsServiceUpdate?.Trim().ToLower() != "running" || x.IsServiceUpdate == null)));

                    //drReady.RelatedService.RelatedErrorService.AddRangeAsync(drReadyService);

                    //var drService = _mapper.Map<List<RelatedServiceDto>>(monitorService.Where(x =>
                    //    x.Status.Trim().ToLower().Equals("stopped")));

                    //drReady.RelatedService.RelatedNotReadyService.AddRangeAsync(drService);


                    var drReadies =await _drReadyStatusRepository.GetDrReadyStatusByInfraObjectId(dr.ReferenceId);

                    var workflowInfraObject =await _workflowInfraObjectRepository
                        .GetResilienceWorkflowByInfraObjectId(dr.ReferenceId);

                    drReady.TotalOrchestrationCount += workflowInfraObject.Count;

                    if (drReadies is not null)
                    {
                        if (drReadies.NotReady == "1")
                        {
                            drReady.Orchestration.ExecutedErrorCount += 1;

                            var drR = _mapper.Map<GetDrReadyErrorExecutionVm>(drReadies);

                            drReady.Orchestration.GetErrorExecutions.AddRange(drR);
                        }
                    }
                    else
                    {
                        //var drReadyWorkflow = workflowInfraObject.Where(x => x.ActionType.ToLower().Trim().Equals("resiliency ready")).ToList();

                        if (workflowInfraObject.Count == 0)
                        {
                            drReady.Components.WorkflowNotConfiguredCount += 1;

                            var workflowNotConfigured = _mapper.Map<GetWorkflowNotConfiguredVm>(dr);

                            drReady.Orchestration.GetWorkflowNotConfiguredVms.AddRange(workflowNotConfigured);
                        }
                    }
                }


                //var workflowInfraObject =await _workflowInfraObjectRepository.GetWorkflowInfraObjectFromInfraObjectId(bf.ReferenceId);    

                //var drReadyWorkflow = workflowInfraObject.Where(x=>x.ActionType.ToLower().Trim().Equals("dr ready")).ToList();

                //drReady.Orchestration.TotalDrReadyCount += drReadyWorkflow.Count;

                //foreach(var dr in drReadyWorkflow)
                //{
                //    var drReadyStatus =await _drReadyStatusRepository.GetDrReadyStatusByWorkflowId(dr.WorkflowId);
                //};
            }
        }

        return drReady;
    }
}