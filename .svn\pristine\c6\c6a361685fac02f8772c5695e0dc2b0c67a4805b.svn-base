using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.GlobalVariableModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IGlobalVariableService
{
    Task<List<GlobalVariableListVm>> GetGlobalVariableList();
    Task<BaseResponse> CreateAsync(CreateGlobalVariableCommand createGlobalVariableCommand);
    Task<BaseResponse> UpdateAsync(UpdateGlobalVariableCommand updateGlobalVariableCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<GlobalVariableDetailVm> GetByReferenceId(string id);
    Task<List<GlobalVariableDetailVm>> GetByVariableName(string name);
    #region NameExist
    Task<bool> IsGlobalVariableNameExist(string name, string id);
   #endregion
    #region Paginated
 Task<PaginatedResult<GlobalVariableListVm>> GetPaginatedGlobalVariables(GetGlobalVariablePaginatedListQuery query);
    #endregion
}
