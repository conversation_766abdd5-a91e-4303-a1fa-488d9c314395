using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class CompanyFixture : IDisposable
{
    public List<Company> CompanyPaginationList { get; set; }
    public List<Company> CompanyList { get; set; }
    public Company CompanyDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public CompanyFixture()
    {
        var fixture = new Fixture();

        CompanyList = fixture.Create<List<Company>>();

        CompanyPaginationList = fixture.CreateMany<Company>(20).ToList();

        CompanyPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        CompanyPaginationList.ForEach(x => x.IsActive = true);
        CompanyPaginationList.ForEach(x => x.ParentId = Guid.NewGuid().ToString());

        CompanyList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        CompanyList.ForEach(x => x.IsActive = true);
        CompanyList.ForEach(x => x.ParentId = Guid.NewGuid().ToString());

        CompanyDto = fixture.Create<Company>();
        CompanyDto.ReferenceId = Guid.NewGuid().ToString();
        CompanyDto.IsActive = true;
        CompanyDto.ParentId = Guid.NewGuid().ToString();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}