using AutoFixture;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class OneViewEntitiesEventViewFixture : IDisposable
{
    public static string UserId => "USER_123";

    public List<OneViewEntitiesEventView> OneViewEntitiesEventViewPaginationList { get; set; }
    public List<OneViewEntitiesEventView> OneViewEntitiesEventViewList { get; set; }
    public OneViewEntitiesEventView OneViewEntitiesEventViewDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public OneViewEntitiesEventViewFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data for the actual view structure
        _fixture.Customize<OneViewEntitiesEventView>(composer => composer
            .With(x => x.Id, () => _fixture.Create<int>())
            .With(x => x.Entity, () => _fixture.Create<string>())
            .With(x => x.Message, () => _fixture.Create<string>())
            .With(x => x.LastModifiedDate, () => DateTime.UtcNow)
            .With(x => x.LastModifiedBy, () => UserId));

        OneViewEntitiesEventViewPaginationList = _fixture.CreateMany<OneViewEntitiesEventView>(20).ToList();
        OneViewEntitiesEventViewList = _fixture.CreateMany<OneViewEntitiesEventView>(5).ToList();
        OneViewEntitiesEventViewDto = _fixture.Create<OneViewEntitiesEventView>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public OneViewEntitiesEventView CreateOneViewEntitiesEventViewWithProperties(
        int? id = null,
        string entity = null,
        string message = null,
        DateTime? lastModifiedDate = null,
        string lastModifiedBy = null)
    {
        return _fixture.Build<OneViewEntitiesEventView>()
            .With(x => x.Id, id ?? _fixture.Create<int>())
            .With(x => x.Entity, entity ?? _fixture.Create<string>())
            .With(x => x.Message, message ?? _fixture.Create<string>())
            .With(x => x.LastModifiedDate, lastModifiedDate ?? DateTime.UtcNow)
            .With(x => x.LastModifiedBy, lastModifiedBy ?? UserId)
            .Create();
    }

    public OneViewEntitiesEventView CreateOneViewEntitiesEventViewWithSpecificEntity(string entity)
    {
        return CreateOneViewEntitiesEventViewWithProperties(entity: entity);
    }

    public OneViewEntitiesEventView CreateOneViewEntitiesEventViewWithSpecificMessage(string message)
    {
        return CreateOneViewEntitiesEventViewWithProperties(message: message);
    }

    public List<OneViewEntitiesEventView> CreateMultipleOneViewEntitiesEventViewWithSameEntity(string entity, int count)
    {
        var events = new List<OneViewEntitiesEventView>();
        for (int i = 0; i < count; i++)
        {
            events.Add(CreateOneViewEntitiesEventViewWithProperties(entity: entity));
        }
        return events;
    }

    public List<OneViewEntitiesEventView> CreateOneViewEntitiesEventViewWithDifferentEntities()
    {
        return new List<OneViewEntitiesEventView>
        {
            CreateOneViewEntitiesEventViewWithProperties(entity: "Database"),
            CreateOneViewEntitiesEventViewWithProperties(entity: "Server"),
            CreateOneViewEntitiesEventViewWithProperties(entity: "Application"),
            CreateOneViewEntitiesEventViewWithProperties(entity: "Network")
        };
    }

    public List<OneViewEntitiesEventView> CreateOneViewEntitiesEventViewWithDateRange(DateTime startDate, DateTime endDate, int count)
    {
        var events = new List<OneViewEntitiesEventView>();
        var dateRange = (endDate - startDate).TotalDays;

        for (int i = 0; i < count; i++)
        {
            var randomDate = startDate.AddDays(Random.Shared.NextDouble() * dateRange);
            events.Add(CreateOneViewEntitiesEventViewWithProperties(lastModifiedDate: randomDate));
        }
        return events;
    }

    public List<OneViewEntitiesEventView> CreateOneViewEntitiesEventViewOutsideDateRange(DateTime startDate, DateTime endDate)
    {
        return new List<OneViewEntitiesEventView>
        {
            CreateOneViewEntitiesEventViewWithProperties(lastModifiedDate: startDate.AddDays(-5)), // Before range
            CreateOneViewEntitiesEventViewWithProperties(lastModifiedDate: endDate.AddDays(5)) // After range
        };
    }

    public OneViewEntitiesEventView CreateOneViewEntitiesEventViewWithLongMessage(int length)
    {
        var longMessage = new string('A', length);
        return CreateOneViewEntitiesEventViewWithProperties(message: longMessage);
    }

    public OneViewEntitiesEventView CreateOneViewEntitiesEventViewWithWhitespace()
    {
        return CreateOneViewEntitiesEventViewWithProperties(
            entity: "  Database  ",
            message: "  Test Message  ");
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonEntities = { "Database", "Server", "Application", "Network", "Storage", "Backup" };
        public static readonly string[] CommonMessages = { "System Alert", "Warning Message", "Error Occurred", "Info Update" };
        public static readonly string[] CommonUsers = { "USER_001", "USER_002", "USER_003", "SYSTEM", "ADMIN" };
        public static readonly string[] SpecialCharacterEntities = { "Entity@#$%", "Entity with spaces", "Entity_with_underscores", "Entity-with-dashes" };
        public static readonly string[] SpecialCharacterMessages = { "Message@#$%", "Message with spaces", "Message_with_underscores", "Message-with-dashes" };
    }
}
