﻿using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.Create;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.SendEmail;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.SendTestEmail;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.Update;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SmtpConfigurationModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class SmtpConfigurationService : BaseClient, ISmtpConfigurationService
{
    public SmtpConfigurationService(IConfiguration config, IAppCache cache, ILogger<SmtpConfigurationService> logger) : base(config, cache, logger)
    {
    }

    public async Task<SmtpConfigurationListVm> GetSmtpConfigurationList()
    {
        var request = new RestRequest("api/v6/smtpconfigurations");

        return await Get<SmtpConfigurationListVm>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateSmtpConfigurationCommand createSmtpConfigurationCommand)
    {
        var request = new RestRequest("api/v6/smtpconfigurations", Method.Post);

        request.AddJsonBody(createSmtpConfigurationCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateSmtpConfigurationCommand updateSmtpConfigurationCommand)
    {
        var request = new RestRequest("api/v6/smtpconfigurations", Method.Put);

        request.AddJsonBody(updateSmtpConfigurationCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/smtpconfigurations/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<SmtpConfigurationDetailVm> GetSmtpConfigurationById(string id)
    {
        var request = new RestRequest($"api/v6/smtpconfigurations/{id}");

        return await Get<SmtpConfigurationDetailVm>(request);
    }

    public async Task<BaseResponse> SendTestMail(SendTestEmailCommand sendTestEmailCommand)
    {
        var request = new RestRequest("api/v6/smtpconfigurations/sendtest-email", Method.Post);

        request.AddJsonBody(sendTestEmailCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> SendMail(SendEmailCommand sendEmailCommand)
    {
        var request = new RestRequest("api/v6/smtpconfigurations/send-email", Method.Post);

        request.AddJsonBody(sendEmailCommand);

        return await Post<SendEmailResponse>(request);
    }

    public async Task<PaginatedResult<SmtpConfigurationListVm>> GetPaginatedSmtpConfigurations(GetSmtpConfigurationPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/smtpconfigurations/paginated-list");

        return await Get<PaginatedResult<SmtpConfigurationListVm>>(request);
    }
}