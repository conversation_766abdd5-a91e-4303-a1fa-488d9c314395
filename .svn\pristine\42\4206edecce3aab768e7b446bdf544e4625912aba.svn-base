﻿//using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Queries.GetPaginatedList;
//using ContinuityPatrol.Application.Specifications;
//using ContinuityPatrol.Domain.Extensions;
//using ContinuityPatrol.Domain.ViewModels.BusinessServiceAvailabilityModel;
//using ContinuityPatrol.Domain.Wrapper;

//namespace ContinuityPatrol.Dashboard.Core.Features.BusinessServiceAvailability.Queries.GetPaginatedList;

//public class GetBusinessServiceAvailabilityPaginatedListQueryHandler : IRequestHandler<GetBusinessServiceAvailabilityPaginatedListQuery, PaginatedResult<BusinessServiceAvailabilityListVm>>
//{
//    private readonly IMapper _mapper;
//    private readonly IBusinessServiceAvailabilityRepository _businessServiceAvailabilityRepository;

//    public GetBusinessServiceAvailabilityPaginatedListQueryHandler(IMapper mapper, IBusinessServiceAvailabilityRepository businessServiceAvailabilityRepository)
//    {
//        _mapper = mapper;
//        _businessServiceAvailabilityRepository = businessServiceAvailabilityRepository;
//    }
//    public async Task<PaginatedResult<BusinessServiceAvailabilityListVm>> Handle(GetBusinessServiceAvailabilityPaginatedListQuery request, CancellationToken cancellationToken)
//    {
//        var queryable = _businessServiceAvailabilityRepository.GetPaginatedQuery();

//        var productFilterSpec = new BusinessServiceAvailabilityFilterSpecification(request.SearchString);

//        var businessServiceAvalabilityList = await queryable
//               .Specify(productFilterSpec)
//               .Select(m => _mapper.Map<BusinessServiceAvailabilityListVm>(m))
//               .ToPaginatedListAsync(request.PageNumber, request.PageSize);

//        return businessServiceAvalabilityList;
//    }
//}

