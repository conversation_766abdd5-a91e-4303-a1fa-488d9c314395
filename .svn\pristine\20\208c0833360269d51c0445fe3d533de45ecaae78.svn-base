﻿/**
 * ---------------------------------------
 * This demo was created using amCharts 4.
 * 
 * For more information visit:
 * https://www.amcharts.com/
 * 
 * Documentation is available at:
 * https://www.amcharts.com/docs/v4/
 * ---------------------------------------
 */
function DCMapChart(globalMapData, multiGeoLine) {
    
    // Themes begin
    am4core.useTheme(am4themes_animated);
    // Themes end

    // Create map instance
    var chart = am4core.create("DCMap-Chart", am4maps.MapChart);
    if (chart.logo) {
        chart.logo.disabled = true;
    }

    // Set projection
    chart.projection = new am4maps.projections.Miller();
    chart.homeZoomLevel = 4;
    chart.homeGeoPoint = {
        latitude: 20,
        longitude: 60
    };

    // Set map definition
    chart.geodata = am4geodata_worldIndiaLow;

    // Create map polygon series
    var polygonSeries = chart.series.push(new am4maps.MapPolygonSeries());

    // Exclude Antartica
    polygonSeries.exclude = ["AQ"];

    // Make map load polygon (like country names) data from GeoJSON
    polygonSeries.useGeodata = true;

    // Configure series
    /* Configure series */
    var polygonTemplate = polygonSeries.mapPolygons.template;
    polygonTemplate.tooltipText = "{name}";
    polygonTemplate.propertyFields.fill = "fill";

    /* Create hover state and set alternative fill color */
    var hs = polygonTemplate.states.create("hover");
    hs.properties.fill = am4core.color("#b6b6b6");

    // Add shadow
    var shadow = polygonSeries.filters.push(new am4core.DropShadowFilter());
    shadow.color = am4core.color("#696969");
    shadow.blur = 1;

    // Add image series
    var imageSeries = chart.series.push(new am4maps.MapImageSeries());
    imageSeries.mapImages.template.propertyFields.longitude = "longitude";
    imageSeries.mapImages.template.propertyFields.latitude = "latitude";
    imageSeries.mapImages.template.tooltipText = "{title}";
    imageSeries.mapImages.template.propertyFields.url = "url";
    
    var circle = imageSeries.mapImages.template.createChild(am4core.Circle);
    circle.radius = 3;
    circle.propertyFields.fill = "CircleColor";
    circle.nonScaling = true;

    var circle2 = imageSeries.mapImages.template.createChild(am4core.Circle);
    circle2.radius = 3;
    circle2.propertyFields.fill = "CircleColor";


    circle2.events.on("inited", function (event) {
        
        animateBullet(event.target);
    })
    circle2.events.on("hit", function (event) {
        
        animateBullet(event.target);
    })
   

    function animateBullet(circle) {
        var animation = circle.animate([{ property: "scale", from: 1 / chart.zoomLevel, to: 5 / chart.zoomLevel }, { property: "opacity", from: 1, to: 0 }], 1000, am4core.ease.circleOut);
        animation.events.on("animationended", function (event) {
            animateBullet(event.target.object);
        })
    }
    
    var colorSet = new am4core.ColorSet();
    var colorSet = new am4core.ColorSet();
    imageSeries.data = globalMapData
    //imageSeries.data = [{
    //    "title": "Brussels",
    //    "latitude": 50.8371,
    //    "longitude": 4.3676,
    //    "CircleColor": "#e03e96",
    //    "radius": 20,
    //    "color": colorSet.next()
    //}, {
    //    "title": "Copenhagen",
    //    "latitude": 55.6763,
    //    "longitude": 12.5681,
    //    "CircleColor": colorSet.next(),
    //    "color": colorSet.next()
    //}, {
    //    "title": "Paris",
    //    "latitude": 48.8567,
    //    "longitude": 2.3510,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "Reykjavik",
    //    "latitude": 64.1353,
    //    "longitude": -21.8952,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "Moscow",
    //    "latitude": 55.7558,
    //    "longitude": 37.6176,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "Madrid",
    //    "latitude": 40.4167,
    //    "longitude": -3.7033,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "London",
    //    "latitude": 51.5002,
    //    "longitude": -0.1262,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "Peking",
    //    "latitude": 39.9056,
    //    "longitude": 116.3958,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "New Delhi",
    //    "latitude": 28.6353,
    //    "longitude": 77.2250,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "Tokyo",
    //    "latitude": 35.6785,
    //    "longitude": 139.6823,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "Ankara",
    //    "latitude": 39.9439,
    //    "longitude": 32.8560,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "Buenos Aires",
    //    "latitude": -34.6118,
    //    "longitude": -58.4173,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "Brasilia",
    //    "latitude": -15.7801,
    //    "longitude": -47.9292,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "Ottawa",
    //    "latitude": 45.4235,
    //    "longitude": -75.6979,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "Washington",
    //    "latitude": 25.2048, "longitude": 55.2708,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "Kinshasa",
    //    "latitude": 13.0827,
    //    "longitude": 80.2707,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "Cairo",
    //    "latitude": 30.0571,
    //    "longitude": 31.2272,
    //    "CircleColor": colorSet.next()
    //}, {
    //    "title": "Pretoria",
    //    "latitude": 10.9601,
    //    "longitude": 78.0766,
    //    "CircleColor": colorSet.next()
    //}];


    polygonSeries.data = [{
        "id": "IN",
        "fill": am4core.color("#4f9acb")
    }, {
        "id": "AE",
        "fill": am4core.color("#4f9acb")
    }];

    // Add lines
    var lineSeries = chart.series.push(new am4maps.MapLineSeries());
    lineSeries.mapLines.template.line.strokeWidth = 1.5;
    lineSeries.mapLines.template.line.strokeOpacity = 0.4;
    lineSeries.mapLines.template.line.nonScalingStroke = true;
    lineSeries.mapLines.template.line.strokeDasharray = "3,1";
    //var lineTemplate = lineSeries.mapLines.template;
    //lineTemplate.nonScalingStroke = true;
    //lineTemplate.arrow.nonScaling = true;
    //lineTemplate.arrow.width = 6;
    //lineTemplate.arrow.height = 8;

    //lineTemplate.line.strokeOpacity = 0.4;

    var line = lineSeries.mapLines.create();
    line.multiGeoLine = [multiGeoLine];


}