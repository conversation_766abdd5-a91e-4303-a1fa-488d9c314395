﻿namespace ContinuityPatrol.Web.Areas.ITAutomation.Controllers;

[Area("ITAutomation")]
public class WorkflowExecutionEventLogController : BaseController
{

    private readonly ILogger<WorkflowExecutionEventLogController> _logger;
    public WorkflowExecutionEventLogController(ILogger<WorkflowExecutionEventLogController> logger)
    {
        _logger = logger;
    }

    public IActionResult List()
    {
        _logger.LogDebug("Entering List method in Workflow Execution Event Log");

        return View();
    }
}