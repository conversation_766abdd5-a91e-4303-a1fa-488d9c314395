﻿using ContinuityPatrol.Application.Features.InfraSummary.Commands.Create;
using ContinuityPatrol.Application.Features.InfraSummary.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.InfraSummaryModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Dashboard;

public class InfraSummaryService : BaseClient, IInfraSummaryService
{
    public InfraSummaryService(IConfiguration config, IAppCache cache, ILogger<InfraSummaryService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<InfraSummaryListVm>> GetInfraSummaries()
    {
        var request = new RestRequest($"api/v6/infrasummary");

        return await GetFromCache<List<InfraSummaryListVm>>(request, "GetInfraSummaries");
    }

    public async Task<BaseResponse> CreateAsync(CreateInfraSummaryCommand createInfraSummaryCommand)
    {
        var request = new RestRequest("api/v6/infrasummary", Method.Post);

        request.AddJsonBody(createInfraSummaryCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateInfraSummaryCommand updateInfraSummaryCommand)
    {
        var request = new RestRequest("api/v6/infrasummary", Method.Put);

        request.AddJsonBody(updateInfraSummaryCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string type)
    {
        var request = new RestRequest($"api/v6/infrasummary/{type}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }
}