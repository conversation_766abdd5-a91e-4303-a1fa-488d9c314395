using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DRReadyStatusFixture : IDisposable
{
    public List<DRReadyStatus> DRReadyStatusPaginationList { get; set; }
    public List<DRReadyStatus> DRReadyStatusList { get; set; }
    public DRReadyStatus DRReadyStatusDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "BS_123";
    public const string BusinessFunctionId = "BF_123";
    public const string InfraObjectId = "INFRA_123";
    public const string WorkflowId = "WF_123";

    public ApplicationDbContext DbContext { get; private set; }

    public DRReadyStatusFixture()
    {
        var fixture = new Fixture();

        DRReadyStatusList = fixture.Create<List<DRReadyStatus>>();

        DRReadyStatusPaginationList = fixture.CreateMany<DRReadyStatus>(20).ToList();

        DRReadyStatusPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DRReadyStatusPaginationList.ForEach(x => x.IsActive = true);
        DRReadyStatusPaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        DRReadyStatusPaginationList.ForEach(x => x.BusinessFunctionId = BusinessFunctionId);
        DRReadyStatusPaginationList.ForEach(x => x.InfraObjectId = InfraObjectId);
        DRReadyStatusPaginationList.ForEach(x => x.WorkflowId = WorkflowId);

        DRReadyStatusList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DRReadyStatusList.ForEach(x => x.IsActive = true);
        DRReadyStatusList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        DRReadyStatusList.ForEach(x => x.BusinessFunctionId = BusinessFunctionId);
        DRReadyStatusList.ForEach(x => x.InfraObjectId = InfraObjectId);
        DRReadyStatusList.ForEach(x => x.WorkflowId = WorkflowId);

        DRReadyStatusDto = fixture.Create<DRReadyStatus>();
        DRReadyStatusDto.ReferenceId = Guid.NewGuid().ToString();
        DRReadyStatusDto.IsActive = true;
        DRReadyStatusDto.BusinessServiceId = BusinessServiceId;
        DRReadyStatusDto.BusinessFunctionId = BusinessFunctionId;
        DRReadyStatusDto.InfraObjectId = InfraObjectId;
        DRReadyStatusDto.WorkflowId = WorkflowId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
