﻿using ContinuityPatrol.Domain.ViewModels.FormHistoryModel;

namespace ContinuityPatrol.Application.Features.FormHistory.Queries.GetList;

public class GetFormHistoryListQueryHandler : IRequestHandler<GetFormHistoryListQuery, List<FormHistoryListVm>>
{
    private readonly IFormHistoryRepository _formHistoryRepository;
    private readonly IMapper _mapper;

    public GetFormHistoryListQueryHandler(IMapper mapper, IFormHistoryRepository formHistoryRepository)
    {
        _mapper = mapper;
        _formHistoryRepository = formHistoryRepository;
    }

    public async Task<List<FormHistoryListVm>> Handle(GetFormHistoryListQuery request,
        CancellationToken cancellationToken)
    {
        var formHistoryList = (await _formHistoryRepository.ListAllAsync()).ToList();

        return formHistoryList.Count <= 0
            ? new List<FormHistoryListVm>()
            : _mapper.Map<List<FormHistoryListVm>>(formHistoryList);
    }
}