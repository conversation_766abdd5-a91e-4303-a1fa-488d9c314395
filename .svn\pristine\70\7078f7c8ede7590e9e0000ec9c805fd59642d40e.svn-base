﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.UserRole.Queries.GetNames;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class UserRoleRepository : BaseRepository<UserRole>, IUserRoleRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public UserRoleRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<UserRole>> ListAllUserRoles()
    {
        if (_loggedInUserService.IsSiteAdmin)
            return await SelectToUserRoles(_dbContext.UserRoles.Active()
                .Where(x => !x.Role.Equals("SiteAdmin") && x.Role.Equals("SuperAdmin")))
                .ToListAsync();
        if(_loggedInUserService.IsSuperAdmin)
            return await SelectToUserRoles(_dbContext.UserRoles.Active()
                .Where(x => !x.Role.Equals("SiteAdmin")))
                .ToListAsync();
        return await SelectToUserRoles(_dbContext.UserRoles.Active()
            .Where(x => !x.Role.Equals("SuperAdmin") && !x.Role.Equals("SiteAdmin")))
            .ToListAsync();
    }

    public async Task<UserRole> GetUserRoleById(string id)
    {
        var matches = await SelectToUserRoles(_dbContext.UserRoles
            .Active()
            .Where(r => r.ReferenceId.Equals(id) && !r.Role.Equals("SiteAdmin") && r.IsDelete && r.IsActive))
            .FirstOrDefaultAsync();

        return matches;
    }

    public Task<bool> IsUserRoleNameExist(string role, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.UserRoles.Any(e => e.Role.Equals(role)))
            : Task.FromResult(_dbContext.UserRoles.Where(e => e.Role.Equals(role)).ToList().Unique(id));
    }

    public Task<bool> IsUserRoleNameUnique(string role)
    {
        var matches = _dbContext.UserRoles.Any(e => e.Role.Equals(role));

        return Task.FromResult(matches);
    }

    public override IQueryable<UserRole> GetPaginatedQuery()
    {
        return SelectToUserRoles(Entities.Where(x => x.IsActive && !x.Role.Equals("SiteAdmin"))
            .AsNoTracking()
            .OrderByDescending(x => x.Id));
    }

    public override async Task<PaginatedResult<UserRole>> PaginatedListAllAsync(int pageNumber,int pageSize,Specification<UserRole> specification, string sortColumn, string sortOrder)
    {
        return await SelectToUserRoles(Entities.Specify(specification).DescOrderById().Where(x => x.IsActive && !x.Role.Equals("SiteAdmin")))
            .ToSortedPaginatedListAsync(pageNumber,pageSize,sortColumn,sortOrder);
    }
    public Task<UserRole> GetUserRoleByRoleId(string id)
    {
        var userRole = SelectToUserRoles(_dbContext.UserRoles.Active().AsNoTracking()).FirstOrDefault(x => x.ReferenceId.Equals(id));

        return Task.FromResult(userRole);
    }


    public override Task<UserRole> GetByReferenceIdAsync(string id)
    {
        var userRole = FindByFilter(userRole => userRole.ReferenceId.Equals(id) && !userRole.Role.Equals("SiteAdmin"))
            .Result.SingleOrDefault();

        Guard.Against.Null(userRole, nameof(UserRole), new NotFoundException(nameof(UserRole), id));

        return Task.FromResult(userRole);
    }

    private IQueryable<UserRole> SelectToUserRoles(IQueryable<UserRole> query)
    {
        return query.Select(x => new UserRole
        {
            ReferenceId = x.ReferenceId,
            Logo = x.Logo,
            Role = x.Role,
            IsDelete = x.IsDelete
        });
    }

    public async Task<List<UserRoleNamesVm>> GetRoles()
    {
        if (_loggedInUserService.IsSiteAdmin || _loggedInUserService.IsSuperAdmin)
            return await _dbContext.UserRoles.Active()
                .Where(x => !x.Role.Equals("SiteAdmin")).Select(ur => new UserRoleNamesVm
                {
                    Id = ur.ReferenceId,
                    Role = ur.Role
                }).ToListAsync();

        return await _dbContext.UserRoles.Active()
            .Where(x => !x.Role.Equals("SuperAdmin") && !x.Role.Equals("SiteAdmin"))
            .Select(ur=>new UserRoleNamesVm
            {
                Id = ur.ReferenceId,
                Role = ur.Role
            }).ToListAsync();
    }
}