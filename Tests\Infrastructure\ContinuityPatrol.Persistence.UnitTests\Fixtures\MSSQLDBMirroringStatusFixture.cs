using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MSSQLDBMirroringStatusFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "MSSQLDBMirroringStatus";

    public List<MSSQLDBMirroringStatus> MSSQLDBMirroringStatusPaginationList { get; set; }
    public List<MSSQLDBMirroringStatus> MSSQLDBMirroringStatusList { get; set; }
    public MSSQLDBMirroringStatus MSSQLDBMirroringStatusDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MSSQLDBMirroringStatusFixture()
    {
        _fixture = new Fixture();
        
        // Configure AutoFixture to generate valid data
        _fixture.Customize<MSSQLDBMirroringStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
    );

        MSSQLDBMirroringStatusPaginationList = _fixture.CreateMany<MSSQLDBMirroringStatus>(20).ToList();
        MSSQLDBMirroringStatusList = _fixture.CreateMany<MSSQLDBMirroringStatus>(5).ToList();
        MSSQLDBMirroringStatusDto = _fixture.Create<MSSQLDBMirroringStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MSSQLDBMirroringStatus CreateMSSQLDBMirroringStatusWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<MSSQLDBMirroringStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
       
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public MSSQLDBMirroringStatus CreateMSSQLDBMirroringStatusWithWhitespace()
    {
        return CreateMSSQLDBMirroringStatusWithProperties(type: "  MSSQLDBMirroringStatus  ");
    }

    public MSSQLDBMirroringStatus CreateMSSQLDBMirroringStatusWithLongType(int length)
    {
        var longType = new string('A', length);
        return CreateMSSQLDBMirroringStatusWithProperties(type: longType);
    }

    public MSSQLDBMirroringStatus CreateMSSQLDBMirroringStatusWithSpecificInfraObjectId(string infraObjectId)
    {
        return CreateMSSQLDBMirroringStatusWithProperties(infraObjectId: infraObjectId);
    }

    public List<MSSQLDBMirroringStatus> CreateMultipleMSSQLDBMirroringStatusWithSameType(string type, int count)
    {
        var statuses = new List<MSSQLDBMirroringStatus>();
        for (int i = 0; i < count; i++)
        {
            statuses.Add(CreateMSSQLDBMirroringStatusWithProperties(type: type, isActive: true));
        }
        return statuses;
    }

    public List<MSSQLDBMirroringStatus> CreateMSSQLDBMirroringStatusWithMixedActiveStatus(string type)
    {
        return new List<MSSQLDBMirroringStatus>
        {
            CreateMSSQLDBMirroringStatusWithProperties(type: type, isActive: true),
            CreateMSSQLDBMirroringStatusWithProperties(type: type, isActive: false),
            CreateMSSQLDBMirroringStatusWithProperties(type: type, isActive: true)
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "MSSQLDBMirroringStatus", "DBMirroringStatus", "MSSQL", "SQLMirroringStatus" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
        public static readonly string[] SpecialCharacterTypes = { "Type@#$%", "Type with spaces", "Type_with_underscores", "Type-with-dashes" };
    }
}
