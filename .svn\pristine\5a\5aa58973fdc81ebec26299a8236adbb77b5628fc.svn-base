﻿namespace ContinuityPatrol.Application.Features.UserInfo.Queries.GetUserName;

public class
    GetUserInfoByUserNameDetailQueryHandler : IRequestHandler<GetUserInfoByUserNameDetailQuery,
        UserInfoByUserNameDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IUserInfoRepository _userInfoRepository;

    public GetUserInfoByUserNameDetailQueryHandler(IMapper mapper, IUserInfoRepository userInfoRepository)
    {
        _mapper = mapper;
        _userInfoRepository = userInfoRepository;
    }

    public async Task<UserInfoByUserNameDetailVm> Handle(GetUserInfoByUserNameDetailQuery request,
        CancellationToken cancellationToken)
    {
        var userInfos = await _userInfoRepository.GetUserInfoByUserName(request.UserName);

        Guard.Against.NullOrDeactive(userInfos, nameof(Domain.Entities.UserInfo),
            new NotFoundException(nameof(Domain.Entities.UserInfo), request.UserName));

        var userInfoDetailDto = _mapper.Map<UserInfoByUserNameDetailVm>(userInfos);

        return userInfoDetailDto;
    }
}