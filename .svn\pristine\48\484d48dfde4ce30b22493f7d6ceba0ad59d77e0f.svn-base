﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.UserRoleModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.UserRole.Queries.GetPaginatedList;

public class
    GetUserRolePaginatedListQueryHandler : IRequestHandler<GetUserRolePaginatedListQuery,
        PaginatedResult<UserRoleListVm>>
{
    private readonly IMapper _mapper;
    private readonly IUserRoleRepository _userRoleRepository;

    public GetUserRolePaginatedListQueryHandler(IMapper mapper, IUserRoleRepository userRoleRepository)
    {
        _mapper = mapper;
        _userRoleRepository = userRoleRepository;
    }

    public async Task<PaginatedResult<UserRoleListVm>> Handle(GetUserRolePaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new UserRoleFilterSpecification(request.SearchString);

        var queryable = await _userRoleRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var userRolesList = _mapper.Map<PaginatedResult<UserRoleListVm>>(queryable);

        return userRolesList;
    }
}