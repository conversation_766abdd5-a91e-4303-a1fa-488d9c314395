using ContinuityPatrol.Application.Features.IncidentManagement.Events.Update;

namespace ContinuityPatrol.Application.Features.IncidentManagement.Commands.Update;

public class
    UpdateIncidentManagementCommandHandler : IRequestHandler<UpdateIncidentManagementCommand,
        UpdateIncidentManagementResponse>
{
    private readonly IIncidentManagementRepository _incidentManagementRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateIncidentManagementCommandHandler(IMapper mapper,
        IIncidentManagementRepository incidentManagementRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _incidentManagementRepository = incidentManagementRepository;
        _publisher = publisher;
    }

    public async Task<UpdateIncidentManagementResponse> Handle(UpdateIncidentManagementCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _incidentManagementRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.IncidentManagement), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateIncidentManagementCommand),
            typeof(Domain.Entities.IncidentManagement));

        await _incidentManagementRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateIncidentManagementResponse
        {
            Message = Message.Update(nameof(Domain.Entities.IncidentManagement), eventToUpdate.IncidentName),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new IncidentManagementUpdatedEvent { Name = eventToUpdate.IncidentName },
            cancellationToken);

        return response;
    }
}