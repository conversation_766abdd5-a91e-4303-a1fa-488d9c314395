﻿using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.CredentialProfileModel;

namespace ContinuityPatrol.Application.UnitTests.Features.CredentialProfile.Queries;

public class GetCredentialProfileNameQueryHandlerTests : IClassFixture<CredentialProfileFixture>
{
    private readonly CredentialProfileFixture _credentialProfileFixture;
    private Mock<ICredentialProfileRepository> _mockCredentialProfileRepository;
    private readonly GetCredentialProfileNameQueryHandler _handler;

    public GetCredentialProfileNameQueryHandlerTests(CredentialProfileFixture credentialProfileFixture)
    {
        _credentialProfileFixture = credentialProfileFixture;

        _mockCredentialProfileRepository = CredentialProfileRepositoryMocks.GetCredentialProfileNamesRepository(_credentialProfileFixture.CredentialProfiles);

        _handler = new GetCredentialProfileNameQueryHandler(_mockCredentialProfileRepository.Object, _credentialProfileFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_Active_CredentialProfiles_Name()
    {
        var result = await _handler.Handle(new GetCredentialProfileNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<CredentialProfileNameVm>>();

        result[0].Id.ShouldBe(_credentialProfileFixture.CredentialProfiles[0].ReferenceId);
        result[0].Name.ShouldBe(_credentialProfileFixture.CredentialProfiles[0].Name);
    }

    [Fact]
    public async Task Handle_Return_Active_CredentialProfileNamesCount()
    {
        var result = await _handler.Handle(new GetCredentialProfileNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<CredentialProfileNameVm>>();

        result.Count.ShouldBe(_credentialProfileFixture.CredentialProfiles.Count);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockCredentialProfileRepository = CredentialProfileRepositoryMocks.GetCredentialProfileEmptyRepository();

        var handler = new GetCredentialProfileNameQueryHandler(_mockCredentialProfileRepository.Object, _credentialProfileFixture.Mapper);

        var result = await handler.Handle(new GetCredentialProfileNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        await _handler.Handle(new GetCredentialProfileNameQuery(), CancellationToken.None);

        _mockCredentialProfileRepository.Verify(x => x.GetCredentialProfileNames(), Times.Once);
    }
}