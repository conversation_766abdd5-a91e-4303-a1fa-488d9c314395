﻿using ContinuityPatrol.Application.Features.FiaCost.Commands.Create;
using ContinuityPatrol.Application.Features.FiaCost.Commands.Update;
using ContinuityPatrol.Application.Features.FiaCost.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FiaCostModel;
using ContinuityPatrol.Domain.ViewModels.FiaTemplateModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class FIACostControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IDataProvider> _mockDataProvider;
        private readonly Mock<ILogger<CompanyController>> _mockLogger;
        private readonly FIACostController _controller;

        public FIACostControllerShould()
        {
            _mockPublisher = new Mock<IPublisher>();
            _mockMapper = new Mock<IMapper>();
            _mockDataProvider = new Mock<IDataProvider>();
            _mockLogger = new Mock<ILogger<CompanyController>>();

            _controller = new FIACostController(_mockMapper.Object, _mockLogger.Object, _mockPublisher.Object, _mockDataProvider.Object);

            // Setup HttpContext and TempData
            var httpContext = new DefaultHttpContext();
            httpContext.User = new ClaimsPrincipal(new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.Name, "testuser"),
                new Claim(ClaimTypes.NameIdentifier, "123")
            }, "mock"));

            _controller.ControllerContext = new ControllerContext()
            {
                HttpContext = httpContext
            };

            var tempData = new TempDataDictionary(httpContext, Mock.Of<ITempDataProvider>());
            _controller.TempData = tempData;
        }

        #region List Tests

        [Fact]
        public void List_ReturnsViewResult()
        {
            // Act
            var result = _controller.List();

            // Assert
            Assert.IsType<ViewResult>(result);
        }

        #endregion

        #region GetPagination Tests

        [Fact]
        public async Task GetPagination_WithValidQuery_ReturnsJsonResultWithData()
        {
            // Arrange
            var query = new GetFiaCostPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "test"
            };

            var paginatedResult = new PaginatedResult<FiaCostListVm>
            {
                Data = new List<FiaCostListVm>
                {
                    new FiaCostListVm
                    {
                        Id = "1",
                        BusinessFunctionId = "bf1",
                        BusinessFunctionName = "Test Business Function",
                        TemplateId = "t1",
                        TemplateName = "Test Template",
                        Properties = "test properties"
                    }
                },
                TotalCount = 1,
                CurrentPage = 1,
                PageSize = 10
            };

            _mockDataProvider.Setup(dp => dp.FiaCost.GetPaginatedFiaCosts(It.IsAny<GetFiaCostPaginatedListQuery>()))
                .ReturnsAsync(paginatedResult);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.FiaCost.GetPaginatedFiaCosts(It.IsAny<GetFiaCostPaginatedListQuery>()), Times.Once);
        }

        [Fact]
        public async Task GetPagination_WithException_ReturnsJsonException()
        {
            // Arrange
            var query = new GetFiaCostPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10
            };

            _mockDataProvider.Setup(dp => dp.FiaCost.GetPaginatedFiaCosts(It.IsAny<GetFiaCostPaginatedListQuery>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task GetPagination_WithNullQuery_CallsDataProvider()
        {
            // Arrange
            GetFiaCostPaginatedListQuery query = null;
            var paginatedResult = new PaginatedResult<FiaCostListVm>
            {
                Data = new List<FiaCostListVm>(),
                TotalCount = 0,
                CurrentPage = 1,
                PageSize = 10
            };

            _mockDataProvider.Setup(dp => dp.FiaCost.GetPaginatedFiaCosts(It.IsAny<GetFiaCostPaginatedListQuery>()))
                .ReturnsAsync(paginatedResult);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            _mockDataProvider.Verify(dp => dp.FiaCost.GetPaginatedFiaCosts(query), Times.Once);
        }

        #endregion

        #region GetFiaTemplateList Tests

        [Fact]
        public async Task GetFiaTemplateList_WithValidData_ReturnsJsonResultWithData()
        {
            // Arrange
            var templateList = new List<FiaTemplateListVm>
            {
                new FiaTemplateListVm
                {
                    Id = "1",
                    Name = "Test Template 1",
                    Description = "Test Description 1",
                    Properties = "test properties 1",
                    UserName = "testuser",
                    TemplateUsedBy = "FIA Cost",
                    TemplateInUsed = true,
                    CreatedDate = DateTime.Now
                },
                new FiaTemplateListVm
                {
                    Id = "2",
                    Name = "Test Template 2",
                    Description = "Test Description 2",
                    Properties = "test properties 2",
                    UserName = "testuser2",
                    TemplateUsedBy = "FIA Cost",
                    TemplateInUsed = false,
                    CreatedDate = DateTime.Now.AddDays(-1)
                }
            };

            _mockDataProvider.Setup(dp => dp.FiaTemplate.GetFiaTemplateList())
                .ReturnsAsync(templateList);

            // Act
            var result = await _controller.GetFiaTemplateList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));
            Assert.Equal(templateList, dataProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.FiaTemplate.GetFiaTemplateList(), Times.Once);
        }

        [Fact]
        public async Task GetFiaTemplateList_WithEmptyList_ReturnsJsonResultWithEmptyData()
        {
            // Arrange
            var templateList = new List<FiaTemplateListVm>();

            _mockDataProvider.Setup(dp => dp.FiaTemplate.GetFiaTemplateList())
                .ReturnsAsync(templateList);

            // Act
            var result = await _controller.GetFiaTemplateList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));
            Assert.Equal(templateList, dataProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.FiaTemplate.GetFiaTemplateList(), Times.Once);
        }

        [Fact]
        public async Task GetFiaTemplateList_WithException_ReturnsJsonException()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.FiaTemplate.GetFiaTemplateList())
                .ThrowsAsync(new Exception("Database connection error"));

            // Act
            var result = await _controller.GetFiaTemplateList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);

            _mockDataProvider.Verify(dp => dp.FiaTemplate.GetFiaTemplateList(), Times.Once);
        }

        #endregion

        #region CreateOrUpdate Tests

        [Fact]
        public async Task CreateOrUpdate_WithValidModelForCreate_ReturnsJsonWithSuccessAndData()
        {
            // Arrange
            var fiaCostModel = new FiaCostViewModel
            {
                BusinessFunctionId = "bf1",
                BusinessFunctionName = "Test Business Function",
                TemplateId = "t1",
                TemplateName = "Test Template",
                Properties = "test properties"
            };

            var createCommand = new CreateFiaCostCommand
            {
                BusinessFunctionId = fiaCostModel.BusinessFunctionId,
                BusinessFunctionName = fiaCostModel.BusinessFunctionName,
                TemplateId = fiaCostModel.TemplateId,
                TemplateName = fiaCostModel.TemplateName,
                Properties = fiaCostModel.Properties
            };

            var response = new CreateFiaCostResponse
            {
                Id = "new-id",
                Message = "FIA Cost created successfully"
            };

            // Setup form collection without id (for create)
            var formCollection = new FormCollection(new Dictionary<string, StringValues>());
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            _mockMapper.Setup(m => m.Map<CreateFiaCostCommand>(It.IsAny<FiaCostViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.FiaCost.CreateAsync(It.IsAny<CreateFiaCostCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(fiaCostModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));
            Assert.Equal(response, dataProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.FiaCost.CreateAsync(It.IsAny<CreateFiaCostCommand>()), Times.Once);
            _mockDataProvider.Verify(dp => dp.FiaCost.UpdateAsync(It.IsAny<UpdateFiaCostCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidModelForUpdate_ReturnsJsonWithSuccessAndData()
        {
            // Arrange
            var fiaCostModel = new FiaCostViewModel
            {
                Id = "existing-id",
                BusinessFunctionId = "bf1",
                BusinessFunctionName = "Updated Business Function",
                TemplateId = "t1",
                TemplateName = "Updated Template",
                Properties = "updated properties"
            };

            var updateCommand = new UpdateFiaCostCommand
            {
                Id = fiaCostModel.Id,
                BusinessFunctionId = fiaCostModel.BusinessFunctionId,
                BusinessFunctionName = fiaCostModel.BusinessFunctionName,
                TemplateId = fiaCostModel.TemplateId,
                TemplateName = fiaCostModel.TemplateName,
                Properties = fiaCostModel.Properties
            };

            var response = new UpdateFiaCostResponse
            {
                Id = "existing-id",
                Message = "FIA Cost updated successfully"
            };

            // Setup form collection with id (for update)
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "Id", new StringValues("existing-id") }
            });
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            _mockMapper.Setup(m => m.Map<UpdateFiaCostCommand>(It.IsAny<FiaCostViewModel>()))
                .Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.FiaCost.UpdateAsync(It.IsAny<UpdateFiaCostCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(fiaCostModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));
            Assert.Equal(response, dataProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.FiaCost.UpdateAsync(It.IsAny<UpdateFiaCostCommand>()), Times.Once);
            _mockDataProvider.Verify(dp => dp.FiaCost.CreateAsync(It.IsAny<CreateFiaCostCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithEmptyIdForCreate_ReturnsJsonWithSuccessAndData()
        {
            // Arrange
            var fiaCostModel = new FiaCostViewModel
            {
                BusinessFunctionId = "bf1",
                BusinessFunctionName = "Test Business Function",
                TemplateId = "t1",
                TemplateName = "Test Template",
                Properties = "test properties"
            };

            var createCommand = new CreateFiaCostCommand
            {
                BusinessFunctionId = fiaCostModel.BusinessFunctionId,
                BusinessFunctionName = fiaCostModel.BusinessFunctionName,
                TemplateId = fiaCostModel.TemplateId,
                TemplateName = fiaCostModel.TemplateName,
                Properties = fiaCostModel.Properties
            };

            var response = new CreateFiaCostResponse
            {
                Id = "new-id",
                Message = "FIA Cost created successfully"
            };

            // Setup form collection with empty id (for create)
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "Id", new StringValues("") }
            });
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            _mockMapper.Setup(m => m.Map<CreateFiaCostCommand>(It.IsAny<FiaCostViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.FiaCost.CreateAsync(It.IsAny<CreateFiaCostCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(fiaCostModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));
            Assert.Equal(response, dataProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.FiaCost.CreateAsync(It.IsAny<CreateFiaCostCommand>()), Times.Once);
            _mockDataProvider.Verify(dp => dp.FiaCost.UpdateAsync(It.IsAny<UpdateFiaCostCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithWhitespaceIdForCreate_ReturnsJsonWithSuccessAndData()
        {
            // Arrange
            var fiaCostModel = new FiaCostViewModel
            {
                BusinessFunctionId = "bf1",
                BusinessFunctionName = "Test Business Function",
                TemplateId = "t1",
                TemplateName = "Test Template",
                Properties = "test properties"
            };

            var createCommand = new CreateFiaCostCommand
            {
                BusinessFunctionId = fiaCostModel.BusinessFunctionId,
                BusinessFunctionName = fiaCostModel.BusinessFunctionName,
                TemplateId = fiaCostModel.TemplateId,
                TemplateName = fiaCostModel.TemplateName,
                Properties = fiaCostModel.Properties
            };

            var response = new CreateFiaCostResponse
            {
                Id = "new-id",
                Message = "FIA Cost created successfully"
            };

            // Setup form collection with whitespace id (for create)
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "Id", new StringValues("   ") }
            });
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            _mockMapper.Setup(m => m.Map<CreateFiaCostCommand>(It.IsAny<FiaCostViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.FiaCost.CreateAsync(It.IsAny<CreateFiaCostCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(fiaCostModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));
            Assert.Equal(response, dataProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.FiaCost.CreateAsync(It.IsAny<CreateFiaCostCommand>()), Times.Once);
            _mockDataProvider.Verify(dp => dp.FiaCost.UpdateAsync(It.IsAny<UpdateFiaCostCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithExceptionOnCreate_ReturnsJsonException()
        {
            // Arrange
            var fiaCostModel = new FiaCostViewModel
            {
                BusinessFunctionId = "bf1",
                BusinessFunctionName = "Test Business Function",
                TemplateId = "t1",
                TemplateName = "Test Template",
                Properties = "test properties"
            };

            var createCommand = new CreateFiaCostCommand
            {
                BusinessFunctionId = fiaCostModel.BusinessFunctionId,
                BusinessFunctionName = fiaCostModel.BusinessFunctionName,
                TemplateId = fiaCostModel.TemplateId,
                TemplateName = fiaCostModel.TemplateName,
                Properties = fiaCostModel.Properties
            };

            // Setup form collection without id (for create)
            var formCollection = new FormCollection(new Dictionary<string, StringValues>());
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            _mockMapper.Setup(m => m.Map<CreateFiaCostCommand>(It.IsAny<FiaCostViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.FiaCost.CreateAsync(It.IsAny<CreateFiaCostCommand>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.CreateOrUpdate(fiaCostModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);

            _mockDataProvider.Verify(dp => dp.FiaCost.CreateAsync(It.IsAny<CreateFiaCostCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_WithExceptionOnUpdate_ReturnsJsonException()
        {
            // Arrange
            var fiaCostModel = new FiaCostViewModel
            {
                Id = "existing-id",
                BusinessFunctionId = "bf1",
                BusinessFunctionName = "Updated Business Function",
                TemplateId = "t1",
                TemplateName = "Updated Template",
                Properties = "updated properties"
            };

            var updateCommand = new UpdateFiaCostCommand
            {
                Id = fiaCostModel.Id,
                BusinessFunctionId = fiaCostModel.BusinessFunctionId,
                BusinessFunctionName = fiaCostModel.BusinessFunctionName,
                TemplateId = fiaCostModel.TemplateId,
                TemplateName = fiaCostModel.TemplateName,
                Properties = fiaCostModel.Properties
            };

            // Setup form collection with id (for update)
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "Id", new StringValues("existing-id") }
            });
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            _mockMapper.Setup(m => m.Map<UpdateFiaCostCommand>(It.IsAny<FiaCostViewModel>()))
                .Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.FiaCost.UpdateAsync(It.IsAny<UpdateFiaCostCommand>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.CreateOrUpdate(fiaCostModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);

            _mockDataProvider.Verify(dp => dp.FiaCost.UpdateAsync(It.IsAny<UpdateFiaCostCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_WithNullModel_HandlesGracefully()
        {
            // Arrange
            FiaCostViewModel fiaCostModel = null;

            // Setup form collection without id (for create)
            var formCollection = new FormCollection(new Dictionary<string, StringValues>());
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            _mockMapper.Setup(m => m.Map<CreateFiaCostCommand>(It.IsAny<FiaCostViewModel>()))
                .Returns((CreateFiaCostCommand)null);
            _mockDataProvider.Setup(dp => dp.FiaCost.CreateAsync(It.IsAny<CreateFiaCostCommand>()))
                .ThrowsAsync(new ArgumentNullException("createCommand"));

            // Act
            var result = await _controller.CreateOrUpdate(fiaCostModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        #endregion
    }
}
