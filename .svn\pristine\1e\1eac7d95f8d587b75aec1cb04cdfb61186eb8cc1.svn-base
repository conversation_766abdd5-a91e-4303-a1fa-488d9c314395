﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowActionTypeRepositoryMocks
{
    public static Mock<IWorkflowActionTypeRepository> CreateWorkflowActionTypeRepository(List<WorkflowActionType> workflowActionTypes)
    {
        var mockWorkflowActionTypeRepository = new Mock<IWorkflowActionTypeRepository>();

        mockWorkflowActionTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActionTypes);

        mockWorkflowActionTypeRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowActionType>())).ReturnsAsync(
            (WorkflowActionType workflowActionType) =>
            {
                workflowActionType.Id = new Fixture().Create<int>();

                workflowActionType.ReferenceId = new Fixture().Create<Guid>().ToString();

                workflowActionTypes.Add(workflowActionType);

                return workflowActionType;
            });

        return mockWorkflowActionTypeRepository;
    }

    public static Mock<IWorkflowActionTypeRepository> UpdateWorkflowActionTypeRepository(List<WorkflowActionType> workflowActionTypes)
    {
        var mockWorkflowActionTypeRepository = new Mock<IWorkflowActionTypeRepository>();

        mockWorkflowActionTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActionTypes);

        mockWorkflowActionTypeRepository.Setup(repo => repo.GetWorkflowActionTypeById(It.IsAny<string>())).ReturnsAsync((string i) => workflowActionTypes.SingleOrDefault(x => x.ReferenceId == i));

        mockWorkflowActionTypeRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowActionType>())).ReturnsAsync((WorkflowActionType workflowActionType) =>
        {
            var index = workflowActionTypes.FindIndex(item => item.Id == workflowActionType.Id);

            workflowActionTypes[index] = workflowActionType;

            return workflowActionType;
        });

        return mockWorkflowActionTypeRepository;
    }

    public static Mock<IWorkflowActionTypeRepository> DeleteWorkflowActionTypeRepository(List<WorkflowActionType> workflowActionTypes)
    {
        var mockWorkflowActionTypeRepository = new Mock<IWorkflowActionTypeRepository>();

        mockWorkflowActionTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActionTypes);

        mockWorkflowActionTypeRepository.Setup(repo => repo.GetWorkflowActionTypeById(It.IsAny<string>())).ReturnsAsync((string i) => workflowActionTypes.SingleOrDefault(x => x.ReferenceId == i));

        mockWorkflowActionTypeRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowActionType>())).ReturnsAsync((WorkflowActionType workflowActionType) =>
        {
            var index = workflowActionTypes.FindIndex(item => item.Id == workflowActionType.Id);

            workflowActionType.IsActive = false;

            workflowActionTypes[index] = workflowActionType;

            return workflowActionType;
        });

        return mockWorkflowActionTypeRepository;
    }

    public static Mock<IWorkflowActionTypeRepository> GetWorkflowActionTypeRepository(List<WorkflowActionType> workflowActionTypes)
    {
        var mockWorkflowActionTypeRepository = new Mock<IWorkflowActionTypeRepository>();

        mockWorkflowActionTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActionTypes);

        mockWorkflowActionTypeRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowActionTypes.SingleOrDefault(x => x.ReferenceId == i));

        return mockWorkflowActionTypeRepository;
    }

    public static Mock<IWorkflowActionTypeRepository> GetWorkflowActionTypeEmptyRepository()
    {
        var mockWorkflowActionTypeRepository = new Mock<IWorkflowActionTypeRepository>();

        mockWorkflowActionTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowActionType>());

        return mockWorkflowActionTypeRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateWorkflowActionTypeEventRepository(List<UserActivity> userActivities)
    {
        var workflowActionTypeEventRepository = new Mock<IUserActivityRepository>();

        workflowActionTypeEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return workflowActionTypeEventRepository;
    }
}
