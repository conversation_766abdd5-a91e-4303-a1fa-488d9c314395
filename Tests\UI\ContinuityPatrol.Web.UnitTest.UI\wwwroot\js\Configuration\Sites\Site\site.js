﻿console.log("site.js")
const siteURL = {
    SiteExistUrl: "Configuration/Site/IsSiteNameExist",
    SitePaginatedUrl: "/Configuration/Site/GetPagination",
    siteCreateUpdateUrl: "Configuration/Site/SaveOrUpdate",
    siteDeleteUrl: "Configuration/Site/Delete"
}
let selectedValues = [];

let permission = {
    create: $("#siteConfigCreate").data("create-permission")?.toLowerCase(),
    delete: $("#siteConfigDelete").data("delete-permission")?.toLowerCase()
}

if (permission.create == 'false') $("#createSiteBtn").addClass('btn-disabled').css('pointer-events', 'none');

let dataTable = $('#siteTable').DataTable(
    {
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
            }
            , infoFiltered: ""
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        "processing": true,
        "serverSide": true,
        "filter": true,
        "order": [],
        "ajax": {
            "type": "GET",
            "url": siteURL.SitePaginatedUrl,
            "dataType": "json",
            "data": function (d) {
                let sortIndex = d?.order[0]?.column || '';
                let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "type" : sortIndex === 3 ? "companyName" :
                    sortIndex === 4 ? "location" : sortIndex === 5 ? "platformType" : sortIndex === 6 ? "status" : "";
                let orderValue = d?.order[0]?.dir || 'asc';
                d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                d.pageSize = d?.length;
                d.searchString = selectedValues?.length === 0 ? $('#siteSearchInp')?.val() : selectedValues?.join(';');
                d.sortColumn = sortValue;
                d.SortOrder = orderValue;
                selectedValues.length = 0;
            },
            "dataSrc": function (json) {
                json.recordsTotal = json?.data?.totalPages;
                json.recordsFiltered = json?.data?.totalCount;
                if (json?.success && Array.isArray(json?.data?.data) && json?.data?.data?.length) {
                    $(".dataTables_empty").text("");
                    $(".pagination-column").removeClass("disabled");
                    return json?.data?.data;
                } else {
                    $(".pagination-column").addClass("disabled");
                    if (!json?.success && json?.data?.message) {
                        $('.dataTables_empty').text('No Data Found');
                        notificationAlert('warning', json?.data?.message);
                    }
                    return [];
                }
            }
        },
        "columnDefs": [
            {
                "targets": [1, 2, 3, 4],
                "className": "truncate"
            }
        ],
        "columns": [
            {
                "data": null,
                "name": "Sr. No.",
                "autoWidth": true,
                "orderable": false,
                "render": function (data, type, row, meta) {
                    if (type === 'display') {
                        return meta?.row + 1;
                    }
                    return data;
                },
                "orderable": false
            },
            {
                "data": "name", "name": "Name", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return `<span title="${data || 'NA'}">${data || 'NA'}</span>`
                    }
                    return data;
                }
            },
            {
                "data": "type", "name": "Type", "autoWidth": true,
                "render": function (data, type, row) {
                    let icon;
                    switch (data?.toLowerCase()) {
                        case "prsite":
                            icon = "cp-prsites";
                            break;
                        case "drsite":
                            icon = "cp-physical-drsite";
                            break;
                        case "neardrsite":
                            icon = "cp-physical-neardrsite";
                            break;
                        default:
                            icon = "cp-custom-server-4"
                            break;
                    }
                    if (icon) {
                        if (type === 'display' && data) {
                            return `<span ><i class="${icon || 'NA'} me-1"></i>${data || 'NA'}</span>`;
                        }
                    }
                    if (type === 'display') {
                        return `<span>${data || 'NA'}</span>`
                    }
                    return data;
                }
            },
            {
                "data": "companyName", "name": "Display Name", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return `<span> ${data || 'NA'}</span>`;
                    }
                    return data;
                }
            },
            {
                "data": "location", "name": "Location", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return `<span> ${data || 'NA'}</span>`;
                    }
                    return data;
                }
            },
            {
                "data": "platformType", "name": "Platform", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return `<span> ${data || 'NA'}</span>`;
                    }
                    return data;
                }
            },
            {
                "render": function (data, type, row) {
                    return `<div class="d-flex align-items-center gap-2"> ${permission.create == 'true' ? `<span role="button" title="Edit" class="editbutton" data-site='${btoa(JSON.stringify(row || 'NA'))}'><i class="cp-edit"></i></span>` : `<span role="button" title="Edit" class="icon-disabled"><i class="cp-edit"></i></span>`}
                          ${permission.delete == 'true' ? `<span role="button" title="Delete" class="deletebutton" data-datasiteid="${row.id || 'NA'}" data-datasitename="${row.name || 'NA'}"><i class="cp-Delete"></i></span>` : `<span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i></span>`}</div>`;
                },
                "orderable": false
            }
        ],
        "rowCallback": function (row, data, index) {
            let api = this.api();
            let startIndex = api?.context[0]?._iDisplayStart;
            let counter = startIndex + index + 1;
            $('td:eq(0)', row).html(counter);
        },
        initComplete: function () {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
        }
    });
dataTable.on('draw.dt', function () {
    $('.paginate_button.page-item.previous').attr('title', 'Previous');
    $('.paginate_button.page-item.next').attr('title', 'Next');
});

$('#siteSearchInp').on('keydown input', function (e) {
    if (e.key === '=' || e.key === 'Enter' || (e.shiftKey && e.key === '<')) {
        e.preventDefault();
        return false;
    }
    handleSearchDebounced();
});

const handleSearchDebounced = commonDebounce(function () {

    const inputValue = $('#siteSearchInp').val();

    ["Name", "Type", "CompanyName", "Location", "PlatformType"].forEach(id => {
        const checkbox = $("#" + id);
        if (checkbox.is(':checked')) {
            selectedValues.push(checkbox?.val() + inputValue);
        }
    });
    dataTable.ajax.reload(function (json) {
        if (json.recordsFiltered === 0) {
            $('.dataTables_empty').text('No matching records found');
        }
    });
}, 500);

function applyPlatformIcons(platformType, prIcon, drIcon, nearDrIcon) {
    switch (platformType) {
        case 'Physical':
            prIcon.addClass('cp-prsites fs-1');
            drIcon.addClass('cp-physical-drsite fs-1');
            nearDrIcon.addClass('cp-physical-neardrsite fs-1');
            break;
        case 'Virtual':
            prIcon.addClass('cp-virtual-prsite fs-1');
            drIcon.addClass('cp-virtual-drsite fs-1');
            nearDrIcon.addClass('cp-virtual-neardrsite fs-1');
            break;
        case 'HCL':
            prIcon.addClass('cp-hcl-prsite fs-1');
            drIcon.addClass('cp-hcl-drsite fs-1')
            nearDrIcon.addClass('cp-hcl-neardrsite fs-1');
            break;
        case 'Cloud':
            prIcon.addClass('cp-cloud-prsite fs-1');
            drIcon.addClass('cp-cloud-drsite fs-1');
            nearDrIcon.addClass('cp-cloud-neardrsite fs-1');
            break;
        default:
            break;
    }
}

async function validateName(value, id = null, Url) {
    const errorElement = $('#siteNameError');
    if (!value) return errorElement.text('Enter site name').addClass('field-validation-error'), false;
    if (value.includes('<')) return errorElement.text('Special characters not allowed').addClass('field-validation-error'), false;

    let data = { siteName: value, id: id };
    const validationResults = [
        SpecialCharValidate(value),
        ShouldNotBeginWithSpace(value),
        ShouldNotBeginWithUnderScore(value),
        OnlyNumericsValidate(value),
        ShouldNotBeginWithNumber(value),
        ShouldNotEndWithSpace(value),
        ShouldNotAllowMultipleSpace(value),
        SpaceWithUnderScore(value),
        ShouldNotEndWithUnderScore(value),
        MultiUnderScoreRegex(value),
        SpaceAndUnderScoreRegex(value),
        minMaxlength(value),
        secondChar(value),
        await IsSiteNameExist(Url, data)
    ];
    return await CommonValidation(errorElement, validationResults);
}

async function IsSiteNameExist(url, data) {
    return !data?.siteName?.trim() ? true : (await getAysncWithHandler(url, data)) ? "Name already exists" : true;
}

function validateDropDown(value, errorMessage, errorElement) {
    if (!value) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}

function ValidateRadioButton(errorElement) {
    if ($(".siteTypeRadio:checked").length === 0) {
        errorElement.text("Select type").addClass('field-validation-error');;
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}

function populateSiteFields(siteData) {
    $('#siteName').val(siteData?.name).attr('siteId', siteData?.id);
    $('#selectLocation').val(siteData?.location).attr('siteLocDetails', JSON.stringify({ lat: siteData?.lat, lng: siteData?.lng, locationId: siteData?.locationId }));
    $('#drSiteTypeSelect').val(siteData?.dataTemperature);
    $('#companyNameDropdown').val(siteData?.companyName).attr('siteCompanyId', siteData?.companyId);
    $('#platformSelect').val(siteData?.platformType);
    $('#siteTypeCustom').attr('siteTypeId', siteData?.typeId);
    $(`.siteTypeRadio[value="${siteData.type}"]`).prop('checked', true);
    if (siteData?.typeId == '1738f813-6090-40cc-8869-25741a156f73') {
        $('#drsitefeild').hide(); $('#drSiteTypeSelect').val('');
    } else {
        $('#drsitefeild').show();
    }
    let prIcon = $('#icon1'), drIcon = $('#icon2'), nearDrIcon = $('#icon3');
    [prIcon, drIcon, nearDrIcon].forEach(iconElement => iconElement.removeClass());
    applyPlatformIcons(siteData?.platformType, prIcon, drIcon, nearDrIcon);
    $('#siteNameError, #sitePlatformError, #siteLocError, #siteCompError, #sitetypeError, #drSiteTypeError').removeClass('field-validation-error');
}

//Clear data before create
$('#createSiteBtn').on('click', async function () {
    $('#siteNameError, #sitePlatformError, #siteLocError, #siteCompError, #sitetypeError, #drSiteTypeError').text('').removeClass('field-validation-error');
    $('#selectLocation').val('').attr('siteLocDetails', '{}');
    $('#companyNameDropdown').val('').attr('siteCompanyId', "");
    $('#siteName').val('').attr('siteId', "");
    $('#platformSelect').val('');
    $('#drsitefeild').hide();
    $('#selectLocation option:first, #companyNameDropdown option:first, #platformSelect option:first, #drSiteTypeSelect option:first').prop('selected', true);
    $('#icon1').removeClass().addClass('cp-prsites fs-1');
    $('#icon2').removeClass().addClass('cp-physical-drsite fs-1');
    $('#icon3').removeClass().addClass('cp-physical-neardrsite fs-1');
    $('#siteTypeCustom .siteTypeRadio').prop('checked', false);
    $('#SaveFunction').text('Save');
    $('#siteCreateModal').modal('show');
});

//Save Update button on click
$("#siteSaveButton").on('click', async function () {
    let name = $("#siteName").val();
    let sitelocation = $("#selectLocation").val();
    let siteLocDetails = JSON.parse($('#selectLocation').attr('sitelocdetails') || '{}');
    let platform = $("#platformSelect").val();
    let company = $("#companyNameDropdown").val();
    let siteId = $('#siteName').attr('siteId');
    let DRSiteType = $('#drSiteTypeSelect').val();
    let IsName = await validateName(name, siteId, RootUrl + siteURL.SiteExistUrl);
    let IsType = ValidateRadioButton($('#sitetypeError'));
    let IsDRSiteType = validateDropDown(DRSiteType, 'Select site type', $('#drSiteTypeError'));
    let IsPlatform = validateDropDown(platform, 'Select platform type', $('#sitePlatformError'));
    let IsCompany = validateDropDown(company, 'Select company name', $('#siteCompError'));
    let IsLocation = validateDropDown(sitelocation, 'Select location', $('#siteLocError'));

    if (IsName && IsLocation && IsPlatform && IsCompany && IsType && ($("#drsitefeild").is(":visible") ? IsDRSiteType : true)) {

        sanitizeContainer(['siteName', 'selectLocation', 'dataTemperature']);

        let savedata = {
            Id: siteId, Name: name, LocationId: siteLocDetails.locationId, Location: sitelocation, TypeId: $('#siteTypeCustom').attr('siteTypeId'), Type: $('.siteTypeRadio:checked').val(),
            PlatformType: platform, CompanyId: $('#companyNameDropdown').attr('siteCompanyId'), CompanyName: company, Lat: siteLocDetails.lat, Lng: siteLocDetails.lng, DataTemperature: DRSiteType, __RequestVerificationToken: gettoken()
        }

        await $.ajax({
            url: RootUrl + siteURL.siteCreateUpdateUrl,
            type: "POST",
            dataType: "json",
            data: savedata,
            success: function (response) {
                if (response && response?.success && response?.data?.message) {
                    notificationAlert("success", response?.data?.message);
                    setTimeout(() => {
                        $('#siteCreateModal').modal('hide');
                        dataTable.ajax.reload()
                    }, 400);
                } else {
                    errorNotification(response);
                }
            }
        });
    }
});

//Edit icon on click
$('#siteTable').on('click', '.editbutton', function () {
    let siteData = JSON.parse(atob($(this).data("site")));
    if (siteData) {
        populateSiteFields(siteData);
        $('#SaveFunction').text('Update');
        $('#siteCreateModal').modal('show');
    }
});

//Delete icon on click
$('#siteTable').on('click', '.deletebutton', function () {
    let siteId = $(this).data('datasiteid');
    let siteName = $(this).data('datasitename');
    if (siteId) {
        $("#siteDeleteId").attr("title", siteName).text(siteName).val(siteId);
        $('#siteDeleteModal').modal('show');
    }
});

//Confirm delete button on click
$("#siteConfirmDeleteBtn").on('click', async function () {
    const deleteid = $('#siteDeleteId').val();
    if (deleteid) {
        await $.ajax({
            url: RootUrl + siteURL.siteDeleteUrl,
            type: "DELETE",
            dataType: "json",
            data: { id: deleteid, __RequestVerificationToken: gettoken() },
            success: function (response) {
                if (response && response?.success && response?.data?.message) {
                    notificationAlert("success", response?.data?.message);
                    setTimeout(() => {
                        $('#siteDeleteModal').modal('hide');
                        dataTable.ajax.reload();
                    }, 1000);
                } else {
                    errorNotification(response);
                    $('#siteDeleteModal').modal('hide');
                }
            },
        });
    }
});

//Company dropdown on change
$("#companyNameDropdown").on('change', function () {
    const selected = $(this).find(':selected');
    $('#companyNameDropdown').attr('siteCompanyId', selected.attr('id'));
    validateDropDown($(this).val(), 'Select company name', $('#siteCompError'));
});

//Location dropdown on change
$("#selectLocation").on('change', function () {
    const selected = $(this).find(':selected');
    $('#selectLocation').attr('siteLocDetails', JSON.stringify({
        lat: selected.attr('lat'), lng: selected.attr('lng'), locationId: selected.attr('id')
    }));
    validateDropDown($(this).val(), 'Select Location', $('#siteLocError'));
});

//Plaform dropdown on change
$('#platformSelect').on('change', function () {
    let platformType = $(this).val();
    validateDropDown(platformType, 'Select platform type', $('#sitePlatformError'));
    let prIcon = $('#icon1'), drIcon = $('#icon2');
    let nearDrIcon = $('#siteTypeCustom [id]').filter((_, element) => {
        let id = element.id;
        return id.startsWith('icon') && (parseInt(id.slice(4)) || 0) >= 3;
    });
    [prIcon, drIcon, nearDrIcon].forEach(iconElement => iconElement.removeClass());
    applyPlatformIcons(platformType, prIcon, drIcon, nearDrIcon);
});

//Site type Radio button on click
$('.siteTypeRadio').on('click', async function () {
    let siteTypeId = $(this).attr('data-typeName');
    if (siteTypeId == '1738f813-6090-40cc-8869-25741a156f73') {
        $('#drsitefeild').hide();
        $('#drSiteTypeSelect').val('').trigger('change');
        $('#drSiteTypeError').text('').removeClass('field-validation-error');
    } else {
        $('#drsitefeild').show();
        $('#drSiteTypeSelect').val('').trigger('change');
        $('#drSiteTypeError').text('').removeClass('field-validation-error');
    }
    $('#siteTypeCustom').attr('siteTypeId', siteTypeId);
    ValidateRadioButton($('#sitetypeError'));
});

//DrSite type on change
$('#drSiteTypeSelect').on('change', function () {
    validateDropDown($(this).val(), 'Select site type', $('#drSiteTypeError'));
});

//Common Validations Site name and Radio btn
$('#siteName').on('keydown', function (e) {
    if (e.key === 'Enter') e.preventDefault();
});

$('#siteName').on('input', commonDebounce(async function () {
    let siteId = $('#siteName').attr('siteId');
    let name = await sanitizeInput($(this).val());
    $(this).val(name);
    await validateName(name, siteId, RootUrl + siteURL.SiteExistUrl);
}, 400));
