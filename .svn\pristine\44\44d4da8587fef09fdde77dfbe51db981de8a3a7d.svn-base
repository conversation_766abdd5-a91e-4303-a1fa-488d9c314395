using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Withdraw;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixRequestModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IApprovalMatrixRequestService
{
    Task<WithdrawApprovalMatrixRequestResponse> WithdrawApprovalMatrixRequest(WithdrawApprovalMatrixRequestCommand command);
    Task<List<ApprovalMatrixRequestListVm>> GetApprovalMatrixRequestList();
    Task<BaseResponse> CreateAsync(CreateApprovalMatrixRequestCommand createApprovalMatrixRequestCommand);
    Task<BaseResponse> UpdateAsync(UpdateApprovalMatrixRequestCommand updateApprovalMatrixRequestCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<ApprovalMatrixRequestDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsApprovalMatrixRequestNameExist(string name, string? id);
    #endregion
    #region Paginated
    Task<PaginatedResult<ApprovalMatrixRequestListVm>> GetPaginatedApprovalMatrixRequests(GetApprovalMatrixRequestPaginatedListQuery query);
    #endregion
}
