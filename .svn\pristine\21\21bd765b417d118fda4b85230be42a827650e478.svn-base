﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@using ContinuityPatrol.Domain.ViewModels.CompanyModel
@using ContinuityPatrol.Shared.Services.Helper
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link rel="stylesheet" type="text/css" href="~/css/Icon.css">
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title" ><i class="cp-company"></i><span>Company</span></h6>
            <form class="d-flex gap-2 align-items-center">
                <div class="input-group w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown" title="Filter">
                            <span data-bs-toggle="dropdown" ><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="CompanyName">
                                        <label class="form-check-label" for="CompanyName">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="displayName=" id="DisplayName">
                                        <label class="form-check-label" for="DisplayName">
                                            Display Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="webAddress=" id="WebAddress">
                                        <label class="form-check-label" for="WebAddress">
                                            Web Address
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                @if (WebHelper.UserSession.IsParent)
                {
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal"  id="Company-CreateButton" data-bs-target="#CreateModal" ><i class="cp-add me-1"></i>Create</button>
                }
                else
                {
                    <button type="button" class="btn btn-disabled btn-sm"  id="Company-CreateButton" ><i class="cp-add me-1"></i>Create</button>
                }               
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="tblCompany" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead class="TableThead">
                    <tr>
                        <th class="SrNo_th" >Sr. No.</th>
                        <th >Name</th>
                        <th >Display Name</th>
                        <th >Web Address</th>
                        <th class="Action-th" >Action</th>
                    </tr>
                </thead>
                <tbody id="table-body">
                </tbody>
            </table>
        </div>
    </div>
</div>
<div id="ConfigurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true"></div>
<div id="ConfigurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true"></div>
<!--Modal Create-->
<div class="modal fade" id="CreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Configuration" model="new CompanyViewModel()" />
</div>
<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" model="new CompanyViewModel()" />
</div>
<!--Modal Video-->
<div class="modal fade" id="VideoModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content bg-transparent border-0">
            <div class="modal-header w-100 position-absolute p-2" style="z-index:1;">
                <button type="button" class="btn-close text-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
            </div>
        </div>
    </div>
</div>
@section Scripts
    {
    <partial name="_ValidationScriptsPartial" />
}
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Configuration/Company/company.js"></script>

