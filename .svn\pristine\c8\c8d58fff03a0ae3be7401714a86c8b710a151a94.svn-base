﻿using Microsoft.AspNetCore.Http;

namespace ContinuityPatrol.Shared.Tests.Helper
{
    public class TestSession : ISession
    {
        private readonly Dictionary<string, byte[]> _sessionStore = new Dictionary<string, byte[]>();

        public string Id { get; set; } = Guid.NewGuid().ToString();

        public bool IsAvailable => true;

        public IEnumerable<string> Keys => _sessionStore.Keys;

        public void Clear()
        {
            _sessionStore.Clear();
        }

        public Task CommitAsync(CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }

        public Task LoadAsync(CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }

        public void Remove(string key)
        {
            _sessionStore.Remove(key);
        }

        public void Set(string key, byte[] value)
        {
            _sessionStore[key] = value;
        }

        public bool TryGetValue(string key, out byte[] value)
        {
            return _sessionStore.TryGetValue(key, out value);
        }
    }
}
