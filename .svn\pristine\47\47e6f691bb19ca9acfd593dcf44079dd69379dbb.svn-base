﻿using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetNextPossibleId;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowPrediction.Queries
{
    public class GetWorkflowPredictionListByNextPossibleIdQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IWorkflowPredictionRepository> _mockWorkflowPredictionRepository;
        private readonly GetWorkflowPredictionListByNextPossibleIdQueryHandler _handler;

        public GetWorkflowPredictionListByNextPossibleIdQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockWorkflowPredictionRepository = new Mock<IWorkflowPredictionRepository>();
            _handler = new GetWorkflowPredictionListByNextPossibleIdQueryHandler(
                _mockMapper.Object,
                _mockWorkflowPredictionRepository.Object
            );
        }

        [Fact]
        public async Task Handle_ReturnsMappedPredictions_WhenPredictionsExist()
        {
            var query = new GetWorkflowPredictionListByNextPossibleIdQuery
            {
                NextPossibleId = "next-id-1"
            };

            var workflowPredictions = new List<Domain.Entities.WorkflowPrediction>
            {
                new Domain.Entities.WorkflowPrediction { ActionId = Guid.NewGuid().ToString(), NextPossibleId = "next-id-1" }
            };

            var mappedPredictions = new List<WorkflowPredictionListByNextPossibleIdVm>
            {
                new WorkflowPredictionListByNextPossibleIdVm { Id = workflowPredictions[0].Id.ToString(), NextPossibleId = "next-id-1" }
            };

            _mockWorkflowPredictionRepository
                .Setup(repo => repo.GetWorkflowPredictionByNextPossibleId(query.NextPossibleId))
                .ReturnsAsync(workflowPredictions);

            _mockMapper
                .Setup(mapper => mapper.Map<List<WorkflowPredictionListByNextPossibleIdVm>>(workflowPredictions))
                .Returns(mappedPredictions);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(mappedPredictions, result);
            _mockWorkflowPredictionRepository.Verify(repo => repo.GetWorkflowPredictionByNextPossibleId(query.NextPossibleId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<WorkflowPredictionListByNextPossibleIdVm>>(workflowPredictions), Times.Once);
        }

        [Fact]
        public async Task Handle_ReturnsEmptyList_WhenNoPredictionsExist()
        {
            var query = new GetWorkflowPredictionListByNextPossibleIdQuery
            {
                NextPossibleId = "non-matching-id"
            };

            var workflowPredictions = new List<Domain.Entities.WorkflowPrediction>();

            _mockWorkflowPredictionRepository
                .Setup(repo => repo.GetWorkflowPredictionByNextPossibleId(query.NextPossibleId))
                .ReturnsAsync(workflowPredictions);

            _mockMapper
                .Setup(mapper => mapper.Map<List<WorkflowPredictionListByNextPossibleIdVm>>(workflowPredictions))
                .Returns(new List<WorkflowPredictionListByNextPossibleIdVm>());

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);
            _mockWorkflowPredictionRepository.Verify(repo => repo.GetWorkflowPredictionByNextPossibleId(query.NextPossibleId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<WorkflowPredictionListByNextPossibleIdVm>>(workflowPredictions), Times.Once);
        }
    }
}
