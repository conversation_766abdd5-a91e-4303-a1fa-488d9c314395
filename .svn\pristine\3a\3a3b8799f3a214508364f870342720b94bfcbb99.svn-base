﻿//const loadSlaChart = (data) => {



//    // Add a legend
//    chart.legend = new am4charts.Legend();
//    chart.legend.position = "bottom";
//    chart.legend.valueLabels.template.disabled = true;
//    chart.legend.labels.template.text = "[font-size:12px ]{name}";
//    chart.legend.labels.template.fill = am4core.color("#6c757d");
//    chart.legend.itemContainers.template.padding(8, 0, 0, 0);
//    chart.legend.markers.template.children.getIndex(0).cornerRadius(30, 30, 30, 30);
//    var markerTemplate = chart.legend.markers.template;
//    markerTemplate.width = 12;
//    markerTemplate.height = 12;



//}
const loadSlaChart = (data) => {
    // Themes begin
    am4core.useTheme(am4themes_animated);
    // Themes end

    // Chart
    var chart = am4core.create("SLAChart", am4charts.XYChart);
    if (chart.logo) {
        chart.logo.disabled = true;
    }
    // Change the padding values
    chart.padding(-0, -0, -0, -0)

    chart.hiddenState.properties.opacity = 0; // this creates initial fade-in
    chart.colors.step = 1;
    chart.colors.list = [
        am4core.color("#0479ff"),
        am4core.color("#ddedeb"),
    ];
    // X axis
    var xAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    xAxis.dataFields.category = "x";
    xAxis.renderer.grid.template.location = 0;
    xAxis.renderer.minGridDistance = 10;
    xAxis.renderer.labels.template.disabled = true;
    xAxis.data = [{ x: "1" }, { x: "2" }, { x: "3" }, { x: "4" }, { x: "5" }];

    // Y axis
    var yAxis = chart.yAxes.push(new am4charts.CategoryAxis());
    yAxis.renderer.labels.template.disabled = true;
    yAxis.renderer.grid.template.location = 0;
    yAxis.renderer.minGridDistance = 10;
    yAxis.dataFields.category = "y";
    yAxis.data = [{ y: "1" }, { y: "2" }, { y: "3" }, { y: "4" }, { y: "5" }, { y: "6" }];

    // Legend
    chart.legend = new am4charts.Legend();

    // Create series
    function createSeries(name) {
        var series = chart.series.push(new am4charts.ColumnSeries());
        series.dataFields.categoryX = "x";
        series.dataFields.categoryY = "y";
        series.dataFields.valueY = "Impacted";
        series.dataFields.valueX = "date.formatDate('dd-MM-yyyy')";
        series.sequencedInterpolation = true;
        series.defaultState.transitionDuration = 3000;
        series.name = name;
        //series.columns.template.tooltipText = "{categoryY}: [bold]{categoryX}[/]";
        series.columns.template.tooltipText = "Date: [bold]{date.formatDate('dd-MM-yyyy')}[/] \n Impacted: [bold]{Impacted}[/] ";

        // Set up column appearance
        var column = series.columns.template;
        //column.strokeWidth = 10;
        column.strokeOpacity = 1;
        column.stroke = am4core.color("#ffffff");
        column.width = am4core.percent(100);
        column.height = am4core.percent(100);
        var markerTemplate = chart.legend.markers.template;
        markerTemplate.width = 10;
        markerTemplate.height = 10;
        return series;
    }

    let dataArray2 = data?.slaImpactList ?? [];
    let ImpactedCount = 0;
    let nonImpactedCount = 0;
    let value_y;
    let impactedArray = [];
    let nonImpactedArray = [];

    let formattedArray2 = dataArray2.map(function (item) {
        let dateObject = new Date(item.date);
        let formattedDate = $.datepicker.formatDate('yy-mm-dd', dateObject);

        item.date = formattedDate ? formattedDate : item?.date
        if (item?.impactCount != undefined && parseInt(item?.impactCount) > 0) {
            ImpactedCount++;
            impactedArray.push(item);
        } else {
            nonImpactedCount++;
            nonImpactedArray.push(item);
        }
        // value_x : item.Impacted,
        // value_y: item.Impacted - 30

    });

    let xPos_1 = 1;
    let yPos_1 = 0;
    let series1Data = [];
    let dataIndex = 0;

    for (let i = 0; i < 6; i++) {

        for (let j = 0; j < 6; j++) {
            //xPos_1++;
            yPos_1++;
            //console.log({x: xPos_1, y: yPos_1, Impacted: dataArray2[dataIndex].Impacted, Non_Impacted: dataArray2[dataIndex].Non_Impacted, date: dataArray2[dataIndex].date });
            //series1Data.push({x: xPos_1 , y: yPos_1});
            if (dataArray2[dataIndex]) {

                series1Data.push({ x: xPos_1, y: yPos_1, Impacted: dataArray2[dataIndex]?.impactCount, Non_Impacted: dataArray2[dataIndex]?.nonImpactCount, date: dataArray2[dataIndex].date });
            }

            dataIndex++;
        }

        yPos_1 = 0;
        xPos_1++;
    }

    let impactedArrNew = [];
    let nonImpactedArrNew = [];

    for (let k = 0; k < series1Data.length; k++) {

        if (series1Data[k]?.impactCount > 0) {
            impactedArrNew.push(series1Data[k]);
        } else {
            nonImpactedArrNew.push(series1Data[k]);
        }
    }

    let series1 = createSeries("Breach");
    series1.data = impactedArrNew;

    let series2 = createSeries("No Breach");
    series2.data = nonImpactedArrNew;
}
