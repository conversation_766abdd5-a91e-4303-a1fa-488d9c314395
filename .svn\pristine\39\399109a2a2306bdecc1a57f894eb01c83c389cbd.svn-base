﻿using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.DRReadyStatus.Queries;

public class GetDrReadyStatusDetailQueryHandlerTests : IClassFixture<DrReadyStatusFixture>
{
    private readonly DrReadyStatusFixture _drReadyStatusFixture;

    private readonly Mock<IDrReadyStatusRepository> _mockDrReadyStatusRepository;

    private readonly GetDRReadyStatusDetailQueryHandler _handler;

    public GetDrReadyStatusDetailQueryHandlerTests(DrReadyStatusFixture drReadyStatusFixture)
    {
        _drReadyStatusFixture = drReadyStatusFixture;

        _mockDrReadyStatusRepository = DrReadyStatusRepositoryMocks.GetDrReadyStatusRepository(_drReadyStatusFixture.DrReadyStatuses);

        _handler = new GetDRReadyStatusDetailQueryHandler(_drReadyStatusFixture.Mapper, _mockDrReadyStatusRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_DRReadyStatusDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetDRReadyStatusDetailQuery { Id = _drReadyStatusFixture.DrReadyStatuses[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<DRReadyStatusDetailVm>();
        result.Id.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].ReferenceId);
        result.UserId.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].UserId);
        result.BusinessServiceId.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].BusinessServiceId);
        result.BusinessServiceName.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].BusinessServiceName);
        result.BusinessFunctionId.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].BusinessFunctionId);
        result.BusinessFunctionName.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].BusinessFunctionName);
        result.IsProtected.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].IsProtected);
        result.AffectedInfra.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].AffectedInfra);
        result.ActiveInfra.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].ActiveInfra);
        result.WorkflowId.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].WorkflowId);
        result.WorkflowName.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].WorkflowName);
        result.WorkflowStatus.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].WorkflowStatus);
        result.FailedActionName.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].FailedActionName);
        result.FailedActionId.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].FailedActionId);
        result.ActiveBusinessFunction.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].ActiveBusinessFunction);
        result.AffectedBusinessFunction.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].AffectedBusinessFunction);
        result.DRReady.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].DRReady);
        result.NotReady.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].NotReady);
        result.WorkflowAttach.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].WorkflowAttach);
        result.InfraObjectId.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].InfraObjectId);
        result.InfraObjectName.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].InfraObjectName);
        result.ComponentName.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].ComponentName);
        result.Type.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].Type);
        result.ErrorMessage.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].ErrorMessage);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidDRReadyStatusId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetDRReadyStatusDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetDRReadyStatusDetailQuery { Id = _drReadyStatusFixture.DrReadyStatuses[0].ReferenceId }, CancellationToken.None);

        _mockDrReadyStatusRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}
