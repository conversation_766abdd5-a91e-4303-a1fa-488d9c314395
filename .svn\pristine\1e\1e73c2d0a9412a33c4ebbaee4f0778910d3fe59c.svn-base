﻿namespace ContinuityPatrol.Web.UnitTests.Extension;

public static class ExtendedSerializerExtensions
{
    private static readonly JsonSerializerSettings SerializerSettings = new JsonSerializerSettings
    {
        TypeNameHandling = TypeNameHandling.Auto,
    };

    public static byte[] Serialize<T>(this T source)
    {
        var asString = JsonConvert.SerializeObject(source, SerializerSettings);
        return Encoding.UTF8.GetBytes(asString);
    }

    public static string ToSerializeString<T>(this T source)
    {
        return JsonConvert.SerializeObject(source, SerializerSettings);
    }

    public static T? Deserialize<T>(this byte[] source)
    {
        var asString = Encoding.UTF8.GetString(source);
        return JsonConvert.DeserializeObject<T>(asString);
    }
}