﻿using ContinuityPatrol.Domain.ViewModels.UserModel.UserListModels;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Domain.ViewModels.UserGroupModel;

public class UserGroupModel
{
    public string Id { get; set; }

    public string GroupName { get; set; }

    public string GroupDescription { get; set; }

    public string userId { get; set; }

    public string Usernames { get; set; }

    public string Properties { get; set; }
    public PaginatedResult<UserListVm> PaginatedUsers { get; set; }

    public PaginatedResult<UserGroupListVm> PaginatedUserGroups { get; set; }
    public List<UserSelectionListVM> UserSelectionList { get; set; }

    public virtual UserGroupCommandVM usergroupcommand { get; set; }
}