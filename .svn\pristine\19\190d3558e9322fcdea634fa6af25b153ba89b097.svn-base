const replicationURL = {
    createOrUpdate: "Configuration/Replication/CreateOrUpdate",
    delete: "Configuration/Replication/Delete",
    getBusinessServiceNames: "Configuration/OperationalService/GetBusinessServiceNames",
    getByReplicationReferenceId: "Configuration/Replication/GetByReplicationReferenceId",
    getPagination: "/Configuration/Replication/GetPagination",
    getReplicationById: "Configuration/Replication/GetReplicationById",
    getReplicationNames: 'Configuration/Replication/GetReplicationNames',
    getreplicationslists: "Configuration/Replication/GetReplicationList",
    isReplicationNameExist: "Configuration/Replication/IsReplicationNameExist",
    replicationTypes: 'Admin/FormMapping/GetFormMappingListByName',
    rpList: 'Admin/FormBuilder/GetForms',
    saveAsReplication: "Configuration/Replication/SaveAsReplication",
    siteList: 'Configuration/Server/GetSiteNames'
};

let dataTable = "";
let editClicked = false;
let isEdit = false;
let propsReplication;
let propertyType;
const ReplicationType = $('#ddlReplicationTypeID');
let replicationList = "";
let replicationLogo = "";
let rSyncDataSyncRoboCopy;
let selectedValues = [];
let this1 = '';
let lunHeaderOne, lunHeaderTwo, lunHeaderThree, lunHeaderFour, lunHeaderFive, lunHeaderSix;
let sourcePath, destinationPath;
let btnDisableReplication = false;
let lssidState = false;
let lssidrangeState = false;
let sessionState = false;
let getreplication = '';
let licenseIdForCountError = "";
let clonedReplicationRowData = "";
let cloneReplicationSlNo = 0;
let clonedReplicationLists = {
    "ReplicationId": "",
    "ReplicationList": []
};
let deleteSaveAsReplRow = "";
let createPermission = $("#configurationCreate").data("create-permission").toLowerCase();
let deletePermission = $("#configurationDelete").data("delete-permission").toLowerCase();

//CheckSession
let isSession = false;

$(async function () {
    infraPreventSpecialKeys('#search-inp, #textName'); //commonfunctions.js
    $('#showHideLicenseKey').hide();
    $('#licensed').val("NA");
    $("#createBtn").toggleClass('btn-disabled', createPermission === 'false').css('pointer-events', createPermission === 'false' ? 'none' : '');

    (function () {
        let sessionData = sessionStorage.getItem('replicationFromITView');

        if (sessionData !== undefined && sessionData !== null && sessionData !== '') {
            getreplication = sessionData;
            isSession = true;
        }
    })();

    dataTable = $('#datatablelist').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": replicationURL.getPagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "type" : sortIndex === 3 ? "siteName" : "";
                    let orderValue = d?.order[0]?.dir || 'asc';
                    let selectedType = $('#search-in-type').val();
                    if (getreplication?.length > 0) {
                        selectedType = getreplication;
                    } else if (selectedType === "all") {
                        selectedType = "";
                    }
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.TypeId = selectedType;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    const { data, totalPages, totalCount } = json;
                    json.recordsTotal = totalPages;
                    json.recordsFiltered = totalCount;
                    let hasData = data.length === 0
                    $(".pagination-column").toggleClass("disabled", hasData);
                    return data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        return type === 'display' ? meta.row + 1 : data;
                    },
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title='${data || "NA"}'>${data || "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "type", "type": "Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            let iconList = JSON.parse(row?.properties) || {};
                            const iconName = iconList?.icon || "cp-images";
                            return `<span title='${data || "NA"}'><i class='${iconName} me-1'></i>${data || "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "siteName", "name": "Site", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>${data || "NA"}</span>` : data;
                    }
                },
                {
                    "orderable": false, "width": '100px',
                    "render": function (data, type, row) {
                        const rowID = row?.id;
                        const rowName = row?.name;
                        const rowType = row?.type;

                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="edit-button" data-replication='${rowID}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete" id="replication-delete-button" data-replication-id="${rowID}" 
                                                 data-replication="${rowType}" data-replication-name="${rowName}" data-bs-toggle="modal" 
                                                  data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>                                  
                                        </div>`;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="edit-button" data-replication='${rowID}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="icon-disabled">
                                                <i class="cp-Delete"></i>
                                            </span>                                  
                                        </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete" id="replication-delete-button" data-replication-id="${rowID}" 
                                              data-replication="${rowType}" data-replication-name="${rowName}" data-bs-toggle="modal" 
                                                data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>                                  
                                        </div>`;
                        }
                        else {
                            return `
                                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete" class="icon-disabled">
                                                <i class="cp-Delete"></i>
                                            </span>
                                        </div>`;
                        }
                    },
                    "orderable": false
                },
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart; // Get the start index of displayed data
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-in-type').on("change", function () {
        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    });

    $('#search-inp').on('input', commonDebounce(async function (e) {
        const checkboxes = ["Name", "Type", "Site"];
        let sanitizedValue = $(this).val().replace(/^\s/, '').replace(/\s+/g, ' ').replace(/\s$/, ' ');

        if (sanitizedValue?.trim() === "") {
            $(this).val("");
            sanitizedValue = "";
        } else {
            $(this).val(sanitizedValue);
        }

        checkboxes.forEach(id => {
            const checkbox = $(`#${id}`);

            if (checkbox.is(':checked')) {
                selectedValues.push(checkbox.val() + sanitizedValue);
            }
        });
        dataTable.ajax.reload(function (json) {
            let $dataTables_empty = $('.dataTables_empty');

            if (sanitizedValue.length === 0 && json?.data?.data?.length === 0) {
                $dataTables_empty.text('No Data Found');
            } else if (json?.recordsFiltered === 0) {
                $dataTables_empty.text('No matching records found');
            }
        })
    }))

    getReplicationType();

    $('.form-select-sm').select2({
        "language": {
            "noResults": function () {
                return "No results found";
            }
        },
    });   

    getReplicationLists();

    clearSessionReplication();

    $('.prev_btn').on('click', function () {
        $('#forModallgtoxl').removeClass("modal-xl").addClass("modal-lg");
        $("#nextButton").show();
        $("#previousButton, #saveButton").hide();  

        removeErrorForSelect('.dynamic-select-tag'); //commonfunctions.js
        const inputValues = $(".formeo-render .f-field-group input[type='text']:visible, .formeo-render .f-field-group input[type='number']:visible, .formeo-render .f-field-group input[type='password']:visible, .formeo-render .f-field-group input[type='date']:visible, .formeo-render .f-field-group textarea[type='textarea']:visible");
        removeErrorForInputs(inputValues, 'replication');          
    });

    $('#datatablelist').on('click', '.edit-button', async function () {
        clearInputFields('replication-form', ['#Name-error', '#Site-error', '#BusinessServiceIdError', '#Type-error', '#Licensekey-error']);
        editClicked = true;
        let replicationID = $(this).data("replication");
        $('#forModallgtoxl').removeClass("modal-xl").addClass("modal-lg");
        $("#nextButton").css("display", "");
        $("#previousButton").css("display", "none");
        isEdit = true;
        form.steps('previous');
        $("#saveButton").css("display", "none").text('Update');
        $('#CreateModal').modal('show');

        let getReplicationById = await infraGetRequestWithData(RootUrl + replicationURL.getReplicationById, { id: replicationID });

        if (getReplicationById && typeof getReplicationById === "object") {
            populateModalFields(getReplicationById?.response2);
            rSyncDataSyncRoboCopy = getReplicationById?.response1;
        }
    });

    $('#datatablelist').on('click', '#replication-delete-button', function () {
        let replName = $(this).data('replication-name');
        let ReplicationId = $(this).data('replication-id');
        $('#textDeleteId').val(ReplicationId);
        $('#deleteData').text(replName);
        $("#deleteData").attr("title", replName);
    });

    $("#confirmDeleteButton").on("click", async function () {
        const form = $('#replicationDelete')[0];
        const formData = new FormData(form);

        if (!btnDisableReplication) {
            btnDisableReplication = true;
            showLoaderAndDisableButton($('#loginLoader'), $(this));
            let response = await infraDeleteData(RootUrl + replicationURL.delete, formData); //commonfunctions.js
            $("#DeleteModal").modal("hide");            

            if (response?.success) {
                notificationAlert("success", response?.data?.message);
                setTimeout(() => {
                    dataTableDelete(dataTable);
                }, 2000)
            } else {
                errorNotification(response);
            }
            btnDisableReplication = false;
            hideLoaderAndEnableButton($('#loginLoader'), $(this));
        }
    });

    $("#createBtn").on("click", function () {
        $("#information").html("");
        licenseIdForCountError = "";
        rSyncDataSyncRoboCopy;
        editClicked = false;
        $('#showHideLicenseKey').hide();
        $('#forModallgtoxl').removeClass("modal-xl").addClass("modal-lg");
        $("#nextButton").css("display", "");
        $("#saveButton").css("display", "none").text('Save');
        $("#previousButton").css("display", "none");
        $('#replicationTitleLogo').removeClass().addClass("cp-replication-on");
        isEdit = false;
        clearInputFields('replication-form', ['#Name-error', '#Site-error', '#BusinessServiceIdError', '#Type-error', '#Licensekey-error']);
        $('#formRenderingArea, #licensed').empty();
        $("#replicationNameType").text("Replication");
        $('#textName, #siteNames, #licensed, #replicationID').val("");
        $("#replicationNameType, #ddlReplicationTypeID, #businessServiceID").val("");
        form.steps('previous');
    })

    $('#siteNames').on("change", function (e, val) {
        $("#names").val($(this).find(":selected").text())
    });

    $('.modal').on('hidden.bs.modal', function () {
        isEdit = false;
    });

    $('.btn-cancel').on('click', function () {
        nextButtonStyle('', '');
    });

    $('.replication').on('shown.bs.modal', function async() {
        this1 = $(this)
    });

    $('#textName').on('keyup', commonDebounce(function () {
        let replName = $(".infraComponentsReplicationName");
        replName.val(replName.val().replace(/\s{2,}/g, ' '));

        //CommonFunctions.js InfraNameValidation
        InfraNameValidation(replName.val(), $('#replicationID').val(), replicationURL.isReplicationNameExist,
            $("#Name-error"), "Enter replication name", 'Special characters not allowed', 'ReplicationName');
    }));

    $(".next_btn").on("click", async function () {
        let validateLicenseKey = true;
        let businessServiceName = commonValidationReplication($("#businessServiceID").val(), " Select operational service", "BusinessServiceIdError");
        let validateName = await InfraNameValidation($(".infraComponentsReplicationName").val(), $('#replicationID').val(),
            replicationURL.isReplicationNameExist, $("#Name-error"), "Enter replication name",
            'Special characters not allowed', 'ReplicationName');
        let validateSiteName = commonValidationReplication($(".infraComponentsSiteName").val(), " Select site name", "Site-error");
        let validateReplicatoionType = serverTypeValidation($("#ddlReplicationTypeID option:selected").text(), " Select replication type", "Type-error");

        if ($("#ddlReplicationTypeID option:selected").text().toLowerCase().includes("perpetuuiti")) {
            validateLicenseKey = commonValidationReplication($("#licensed option:selected").val(), " Select license key", "Licensekey-error");
            validateLicenseKey = licenseCountValidation();
        } else {
            validateLicenseKey = true;
            $("#licensed").val("NA")
        }

        if (validateName && validateLicenseKey && validateSiteName && validateReplicatoionType && businessServiceName) {
            form.steps('next');
            setTimeout(() => {
                formDynamicButton()
            }, 300);
        }
    });

    $("#licensed").on("change", function () {
        let licenseKey = $("#licensed :selected");
        $('#replicationLicenseKey').val(licenseKey.text());
        let count = licenseKey.attr('remainingcount');

        if (count !== null && count !== undefined) {
            $("#information").html(`<i class="cp-note me-1 fs-8"></i><span>Remaining count ${count} </span>`);
        }
        commonValidationReplication(licenseKey.text(), " Select license key", "Licensekey-error");
        licenseCountValidation();
    });

    await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + 'Configuration/OperationalService/GetBusinessServiceNames',
        dataType: "json",
        success: function (result) {
            if (result?.success && (Array.isArray(result?.data) && result?.data.length > 0)) {
                const sortedData = result?.data?.sort((a, b) => a?.name.toLowerCase().localeCompare(b?.name.toLowerCase()));
                let businessService = $('#businessServiceID');
                let options = [];
                businessService.empty().append($('<option>').val("").text("Select Operational Service"));
                sortedData.forEach(function (item) {
                    options.push($('<option>').val(item?.id).text(item?.name).attr('businessServiceName', item?.name));
                });
                businessService.append(options);
            } else {
                errorNotification(result)
            }
        },
    });

    let getReplLists = await infraGetRequestWithData(RootUrl + replicationURL.rpList, { "type": "replication" });
    if (getReplLists?.length) {
        replicationList = getReplLists;
    }

    let getSiteLists = await infraGetRequest(RootUrl + replicationURL.siteList);

    if (getSiteLists?.length) {
        const sortedData = getSiteLists?.sort((a, b) => a?.name.toLowerCase().localeCompare(b?.name.toLowerCase()));
        let siteNames = $('#siteNames');
        let options = [];
        let $name = $('#names');
        if ($name.val()?.length != 0) {
            siteNames.empty().append($('<option>').val("").text("Select Site Name"));
            let value = $name.val();
            sortedData?.forEach(function (item) {
                options.push($('<option>').val(item?.id).text(item?.name));
            });
            siteNames.append(options);
            $name.val(value);
            siteNames.val(siteNames.val());
        }
        else {
            siteNames.empty().append($('<option>').val('').text('Select Site'));
            sortedData?.forEach(function (item) {
                options.push($('<option>').val(item?.id).text(item?.name))
            });
            siteNames.append(options);
        }
    }
});
