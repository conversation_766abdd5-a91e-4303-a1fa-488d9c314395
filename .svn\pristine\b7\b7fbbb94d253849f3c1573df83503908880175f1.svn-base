﻿using ContinuityPatrol.Application.Constants;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.CommonBaseLicenseUpdate;

public class CommonBaseLicenseUpdateCommandValidator : AbstractValidator<CommonBaseLicenseUpdateCommand>
{
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ILogger<CommonBaseLicenseUpdateCommandValidator> _logger;

    public CommonBaseLicenseUpdateCommandValidator(ILicenseManagerRepository licenseManagerRepository,
        ILoggedInUserService loggedInUserService, ILogger<CommonBaseLicenseUpdateCommandValidator> logger)
    {
        _licenseManagerRepository = licenseManagerRepository;
        _loggedInUserService = loggedInUserService;
        _logger = logger;

        RuleFor(p => p).MustAsync(IsParentId);

        RuleFor(p => p).MustAsync(IsAuthorizedUser).WithMessage("Access Denied");

        /* RuleFor(p => p.LicenseKey)
             .NotEmpty().WithMessage("{PropertyName} is Required.")
             .Matches("^(?:[a-zA-Z0-9+/]{4})*(?:|(?:[a-zA-Z0-9+/]{3}=)|(?:[a-zA-Z0-9+/]{2}==)|(?:[a-zA-Z0-9+/]{1}===))$").WithMessage("Please Enter Valid {PropertyName}")
             .NotNull(); */

        RuleFor(p => p).MustAsync(IsValidated)
            .WithMessage("Invalid LicenseKey.");
    }

    private Task<bool> IsParentId(CommonBaseLicenseUpdateCommand commonBaseLicenseUpdateCommand,
        CancellationToken cancellation)
    {
        if (_loggedInUserService.IsParent) return Task.FromResult(true);
        _logger.LogInformation($"User Is Not Parent .{LicenseAuthentication.UserIsNotParent}");
        throw new InvalidException(LicenseAuthentication.UserIsNotParent);
    }

    private Task<bool> IsAuthorizedUser(CommonBaseLicenseUpdateCommand commonBaseLicenseUpdateCommand,
        CancellationToken cancellation)
    {
        if (_loggedInUserService.IsSiteAdmin || _loggedInUserService.IsSuperAdmin) return Task.FromResult(true);
        _logger.LogInformation(LicenseAuthentication.UserIsNotParent);
        return Task.FromResult(false);
    }

    private async Task<bool> IsValidated(CommonBaseLicenseUpdateCommand commonBaseLicenseUpdateCommand,
        CancellationToken token)
    {
        var match = await IsMatch(commonBaseLicenseUpdateCommand.LicenseKey);
        if (match.Equals(true))
        {
            await LicenseValidator(commonBaseLicenseUpdateCommand);
            return true;
        }

        return false;
    }

    private async Task LicenseValidator(CommonBaseLicenseUpdateCommand commonBaseLicenseUpdateCommand)
    {
        var licenseKey = SecurityHelper.Decrypt(commonBaseLicenseUpdateCommand.LicenseKey);

        var licenseList = licenseKey.Split('*');

        //var poNumber = licenseList[0];
        var cpHostName = licenseList[1];
        var ipAddress = licenseList[2];
        var macaddress = licenseList[3];
       // var serverCount = licenseList[4];
        //var databaseCount = licenseList[5];
        //var replicationCount = licenseList[6];
        //var expiryTime = licenseList[7];
        var licenseGeneratedDate = licenseList[9];

        // ValidateISLicenseAcceptable
        var existLicense = await _licenseManagerRepository.GetByReferenceIdAsync(commonBaseLicenseUpdateCommand.Id);

        var existLicenseDetail = SecurityHelper.Decrypt(existLicense.LicenseKey).Split('*');

        if (!existLicenseDetail[9].Equals(licenseGeneratedDate))
        {
            var date = DateTime.UtcNow.ToString(CultureInfo.GetCultureInfo("en-IN").DateTimeFormat.FullDateTimePattern);
            var currentDate = DateTime.Parse(date);

            var difference = currentDate.Subtract(DateTime.Parse(licenseGeneratedDate)).Days;

            if (difference > 15)
            {
                _logger.LogInformation("Base LicenseKey Usable Time Expired");

                _logger.LogInformation($"License Generated date and added day difference={difference}");

                throw new InvalidException(LicenseAuthentication.BaseLicenseKeyUsableTimeExpired);
            }
        }

        // Validate MAC Address

        var mac = macaddress.Split(',');

        if (!(await _licenseManagerRepository.GetMacAddress()).Any(m => mac.Contains(m)))
        {
            _logger.LogInformation($"{LicenseAuthentication.BaseLicenseKeyMACAddressMismatch}");
            throw new InvalidException(LicenseAuthentication.InvalidLicenseDetail);
        }

        //var macAddress = await _licenseManagerRepository.GetMacAddress();
        //if (!macaddress.Equals(macAddress))
        //{
        //    _logger.LogInformation($"{LicenseAuthentication.BaseLicenseKeyMACAddressMismatch}");
        //    throw new InvalidException(LicenseAuthentication.InvalidLicenseDetail);
        //}

        // Validate CPHost Name

        var hostName = cpHostName.Split(',');

        var hostNameValidation = await _licenseManagerRepository.GetHostName();

        if (!hostName.Contains(hostNameValidation))
        {
            _logger.LogInformation($"{LicenseAuthentication.BaseLicenseKeyHostNameMismatch}");
            throw new InvalidException(LicenseAuthentication.InvalidLicenseDetail);
        }

        //var hostName = await _licenseManagerRepository.GetHostName();

        //if (!cpHostName.Equals(hostName))
        //{
        //    _logger.LogInformation($"{LicenseAuthentication.BaseLicenseKeyHostNameMismatch}");
        //    throw new InvalidException(LicenseAuthentication.InvalidLicenseDetail);
        //}

        // Validate IPAddress

        var newIpAddress = ipAddress.Split(',');

        if (!(await _licenseManagerRepository.GetIpAddress()).Any(i => newIpAddress.Contains(i)))
        {
            _logger.LogInformation($"{LicenseAuthentication.BaseLicenseKeyIPAddressMismatch}");
            throw new InvalidException(LicenseAuthentication.InvalidLicenseDetail);
        }

        //var ipAddresses = await _licenseManagerRepository.GetIpAddress();
        //if (!ipAddresses.Any(ip => ip.Equals(ipAddress)))
        //{
        //    _logger.LogInformation($"{LicenseAuthentication.BaseLicenseKeyIPAddressMismatch}");
        //    throw new InvalidException(LicenseAuthentication.InvalidLicenseDetail);
        //}

        // Validate PONumber Exist
        //var poValidation = await _licenseManagerRepository.IsLicenseKeyPONumberExist(poNumber, CommonBaseLicenseUpdateCommand.Id);
        //if (!poValidation)
        //{
        //    throw new InvalidException(LicenseAuthentication.BaseLicenseKeyPONumberExist);
        //}
    }

    private Task<bool> IsMatch(string license)
    {
        if (string.IsNullOrWhiteSpace(license)) return Task.FromResult(false);
        var licenseSplit = license.Split('$');

        var pattern =
            new Regex(
                "^(?:[a-zA-Z0-9+/]{4})*(?:|(?:[a-zA-Z0-9+/]{3}=)|(?:[a-zA-Z0-9+/]{2}==)|(?:[a-zA-Z0-9+/]{1}===))$");

        var match = Task.FromResult(license.Length > 75 && pattern.Match(licenseSplit[1]).Success);

        return match;
    }
}