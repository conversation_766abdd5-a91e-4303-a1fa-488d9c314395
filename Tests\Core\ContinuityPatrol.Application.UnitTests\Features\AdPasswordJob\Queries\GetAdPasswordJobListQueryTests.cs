using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordJob.Queries;

public class GetAdPasswordJobListQueryTests : IClassFixture<AdPasswordJobFixture>
{
    private readonly AdPasswordJobFixture _adPasswordJobFixture;
    private readonly Mock<IAdPasswordJobRepository> _mockAdPasswordJobRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetAdPasswordJobListQueryHandler _handler;

    public GetAdPasswordJobListQueryTests(AdPasswordJobFixture adPasswordJobFixture)
    {
        _adPasswordJobFixture = adPasswordJobFixture;

        _mockAdPasswordJobRepository = AdPasswordJobRepositoryMocks.CreateQueryAdPasswordJobRepository(_adPasswordJobFixture.AdPasswordJobs);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<List<AdPasswordJobListVm>>(It.IsAny<List<Domain.Entities.AdPasswordJob>>()))
            .Returns((List<Domain.Entities.AdPasswordJob> entities) => entities.Select(entity => new AdPasswordJobListVm
            {
                Id = entity.ReferenceId,
                DomainServerId = entity.DomainServerId,
                DomainServer = entity.DomainServer,
                State = entity.State,
                IsSchedule = entity.IsSchedule,
                ScheduleType = entity.ScheduleType,
                CronExpression = entity.CronExpression,
                ScheduleTime = entity.ScheduleTime,
                NodeId = entity.NodeId,
                NodeName = entity.NodeName,
                ExceptionMessage = entity.ExceptionMessage
            }).ToList());

        _handler = new GetAdPasswordJobListQueryHandler(
            _mockMapper.Object,
            _mockAdPasswordJobRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_AdPasswordJobListVm_When_AdPasswordJobsExist()
    {
        // Arrange
        var query = new GetAdPasswordJobListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<AdPasswordJobListVm>));
        result.Count.ShouldBeGreaterThan(0);
        result.First().Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_Call_ListAllAsync_OnlyOnce()
    {
        // Arrange
        var query = new GetAdPasswordJobListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordJobRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var query = new GetAdPasswordJobListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<List<AdPasswordJobListVm>>(It.IsAny<List<Domain.Entities.AdPasswordJob>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoAdPasswordJobsExist()
    {
        // Arrange
        var query = new GetAdPasswordJobListQuery();
        _mockAdPasswordJobRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.AdPasswordJob>());

        _mockMapper.Setup(m => m.Map<List<AdPasswordJobListVm>>(It.IsAny<List<Domain.Entities.AdPasswordJob>>()))
            .Returns(new List<AdPasswordJobListVm>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<AdPasswordJobListVm>));
        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_MapEntitiesToViewModels_WithCorrectProperties()
    {
        // Arrange
        var testJob = _adPasswordJobFixture.AdPasswordJobs.First();
        testJob.DomainServerId = "DS001";
        testJob.DomainServer = "TestDomain.com";
        testJob.State = "Active";
        testJob.IsSchedule = 1;
        testJob.ScheduleType = 1;
        testJob.CronExpression = "0 0 12 * * ?";
        testJob.ScheduleTime = "12:00:00";
        testJob.NodeId = "Node001";
        testJob.NodeName = "TestNode";
        testJob.ExceptionMessage = "";

        var query = new GetAdPasswordJobListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBeGreaterThan(0);
        
        var firstItem = result.First();
        firstItem.Id.ShouldBe(testJob.ReferenceId);
        firstItem.DomainServerId.ShouldBe("DS001");
        firstItem.DomainServer.ShouldBe("TestDomain.com");
        firstItem.State.ShouldBe("Active");
        firstItem.IsSchedule.ShouldBe(1);
        firstItem.ScheduleType.ShouldBe(1);
        firstItem.CronExpression.ShouldBe("0 0 12 * * ?");
        firstItem.ScheduleTime.ShouldBe("12:00:00");
        firstItem.NodeId.ShouldBe("Node001");
        firstItem.NodeName.ShouldBe("TestNode");
        firstItem.ExceptionMessage.ShouldBe("");
    }

    [Fact]
    public async Task Handle_ReturnCorrectListType_When_MappingSuccessful()
    {
        // Arrange
        var query = new GetAdPasswordJobListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<List<AdPasswordJobListVm>>();
        result.GetType().ShouldBe(typeof(List<AdPasswordJobListVm>));
    }

    [Fact]
    public async Task Handle_ReturnAllActiveItems_When_RepositoryHasData()
    {
        // Arrange
        var query = new GetAdPasswordJobListQuery();
        var expectedCount = _adPasswordJobFixture.AdPasswordJobs.Count;

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(expectedCount);
    }

    [Fact]
    public async Task Handle_NotCallMapper_When_NoDataExists()
    {
        // Arrange
        var query = new GetAdPasswordJobListQuery();
        _mockAdPasswordJobRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.AdPasswordJob>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(0);
        _mockMapper.Verify(x => x.Map<List<AdPasswordJobListVm>>(It.IsAny<List<Domain.Entities.AdPasswordJob>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_MapScheduleProperties_WithCorrectValues()
    {
        // Arrange
        var testJob = _adPasswordJobFixture.AdPasswordJobs.First();
        testJob.IsSchedule = 1;
        testJob.ScheduleType = 2;
        testJob.CronExpression = "0 30 14 * * ?";
        testJob.ScheduleTime = "14:30:00";

        var query = new GetAdPasswordJobListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        var firstItem = result.First();
        firstItem.IsSchedule.ShouldBe(1);
        firstItem.ScheduleType.ShouldBe(2);
        firstItem.CronExpression.ShouldBe("0 30 14 * * ?");
        firstItem.ScheduleTime.ShouldBe("14:30:00");
    }

    [Fact]
    public async Task Handle_MapNodeProperties_WithCorrectValues()
    {
        // Arrange
        var testJob = _adPasswordJobFixture.AdPasswordJobs.First();
        testJob.NodeId = "Node123";
        testJob.NodeName = "ProductionNode";
        testJob.ExceptionMessage = "Test exception message";

        var query = new GetAdPasswordJobListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        var firstItem = result.First();
        firstItem.NodeId.ShouldBe("Node123");
        firstItem.NodeName.ShouldBe("ProductionNode");
        firstItem.ExceptionMessage.ShouldBe("Test exception message");
    }
}
