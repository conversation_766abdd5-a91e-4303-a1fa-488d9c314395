﻿using ContinuityPatrol.Application.Features.Server.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Queries;

public class GetServerNameUniqueQueryHandlerTests : IClassFixture<ServerFixture>
{
    private readonly ServerFixture _serverFixture;
    private Mock<IServerRepository> _mockServerRepository;
    private readonly GetServerNameUniqueQueryHandler _handler;

    public GetServerNameUniqueQueryHandlerTests(ServerFixture serverFixture)
    {
        _serverFixture = serverFixture;

        _mockServerRepository = ServerRepositoryMocks.GetServerNameUniqueRepository(_serverFixture.Servers);

        _handler = new GetServerNameUniqueQueryHandler(_mockServerRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_ServerName_Exist()
    {
        _serverFixture.Servers[0].Name = "PR_Server";

        var result = await _handler.Handle(new GetServerNameUniqueQuery { ServerName = _serverFixture.Servers[0].Name, ServerId = _serverFixture.Servers[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_ServerNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetServerNameUniqueQuery { ServerName = "DR_Server", ServerId = _serverFixture.Servers[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsServerNameExist_OneTime()
    {
        await _handler.Handle(new GetServerNameUniqueQuery(), CancellationToken.None);

        _mockServerRepository.Verify(x => x.IsServerNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_False_ServerName_NotMatch()
    {
        var result = await _handler.Handle(new GetServerNameUniqueQuery { ServerName = "PR_Pro", ServerId = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockServerRepository = ServerRepositoryMocks.GetServerEmptyRepository();

        var result = await _handler.Handle(new GetServerNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}