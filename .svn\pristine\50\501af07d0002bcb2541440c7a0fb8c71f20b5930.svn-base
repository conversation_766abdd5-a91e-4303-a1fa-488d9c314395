﻿using ContinuityPatrol.Application.Features.FastCopyMonitor.Queries.GetByDataSyncId;
using ContinuityPatrol.Application.Features.FastCopyMonitor.Queries.GetPagination;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage;

public  class FastCopyMonitorService:BaseClient, IFastCopyMonitorService
{
    public FastCopyMonitorService(IConfiguration configuration, IAppCache appCache, ILogger<FastCopyMonitorService> logger)
        : base(configuration, appCache, logger)
    {

    }
    public async Task<List<FatCopyMonitorListVm>> GetByDriftJobIds(List<string> dataSyncJobIds)
    {
        var request = new RestRequest($"api/v6/datasyncjobids/{dataSyncJobIds}");

        return await Get<List<FatCopyMonitorListVm>>(request);

    }

    public async  Task<PaginatedResult<FastCopyMonitorPaginatedListVm>> GetPagination(GetFastCopyMonitorPaginatedQuery query)
    {
        var request = new RestRequest($"api/v6/datasyncjobids/{query}");

        return await Get<PaginatedResult<FastCopyMonitorPaginatedListVm>>(request);
    }
}
