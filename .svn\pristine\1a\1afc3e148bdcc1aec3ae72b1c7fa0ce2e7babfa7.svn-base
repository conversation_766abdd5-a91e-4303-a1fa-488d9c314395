﻿
function generateMonitorServiceDetailsRow(workflow, application, monitorServiceDetails) {
    //function getIconClass(value, label, status) {
    //    let iconClass = '';

    //    if (status) {
    //        return `<i class="${status} me-1 fs-6"></i>`;
    //    }



    //    if (value) {
    //        const lowerValue = Array.isArray(value) ? value[0]?.toLowerCase() : value.toLowerCase();

    //        switch (lowerValue) {
    //            case 'na':
    //            case 'disabled':
    //            case 'no':
    //            case 'not allowed':
    //                iconClass = 'text-danger cp-disable';
    //                break;
    //            case 'stopped':
    //            case 'stopped -1':
    //            case 'stopped -2':
    //            case 'stopped -3':
    //            case 'error':
    //                iconClass = 'text-danger cp-fail-back';
    //                break;
    //            case 'running':
    //            case 'running -1':
    //            case 'running -2':
    //            case 'running -3':
    //                iconClass = 'text-success cp-reload cp-animate';
    //                break;
    //        }
    //    }

    //    return iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
    //}

    //function checkAndReplace(value) {
    //    return (value !== null && value !== '' && value !== undefined) ? value : "NA";
    //}

    if (!monitorServiceDetails?.length) {
        return '';
    } else {
        let prWfName = [], drWfName = [];
        let prStatusArr = [], drStatusArr = [];
        let prWfDisplay = '--', drWfDisplay = '--';
        let prStatusDisplay = '--', drStatusDisplay = '--';
        let iconWF = '', iconStatus = '';

        monitorServiceDetails.forEach(item => {
            let parsed = [];
            const isValidJson = item?.isServiceUpdate && item?.isServiceUpdate.trim().startsWith('[');
            if (isValidJson) {
                try {
                    parsed = JSON.parse(item.isServiceUpdate);
                } catch (err) {
                    console.warn('Invalid JSON in isServiceUpdate:', item.isServiceUpdate);
                    parsed = [];
                }
            }

            parsed?.forEach(entry => {
                entry?.Services?.forEach(service => {
                    if (entry?.Type?.toLowerCase() === 'pr') {
                        if (service?.ServiceName && service?.ServiceName !== 'NA') {
                            prWfName.push(service?.ServiceName);
                        }
                        if (service?.Status && service?.Status !== 'NA') {
                            prStatusArr.push(service?.Status);
                        }
                    } else if (entry?.Type?.toLowerCase() === 'dr') {
                        if (service?.ServiceName && service?.ServiceName !== 'NA') {
                            drWfName.push(service?.ServiceName);
                        }
                        if (service?.Status && service?.Status !== 'NA') {
                            drStatusArr.push(service?.Status);
                        }
                    }
                });
            });
        });

        // Get unique workflow names
        prWfDisplay = prWfName.length > 0 ? [...new Set(prWfName)].join(', ') : '--';
        drWfDisplay = drWfName.length > 0 ? [...new Set(drWfName)].join(', ') : '--';

        // Status summary logic
        function getStatusSummary(arr) {
            let countMap = {};
            arr.forEach(status => {
                countMap[status] = (countMap[status] || 0) + 1;
            });
            let total = arr.length;
            let statusSummary = Object.entries(countMap)
                .map(([status, count]) => `${count} ${status}`)
                .join(', ');
            return statusSummary ? `${statusSummary} / ${total}` : '--';
        }

        prStatusDisplay = getStatusSummary(prStatusArr);
        drStatusDisplay = getStatusSummary(drStatusArr);

        iconWF = (prWfDisplay !== '--' || drWfDisplay !== '--') ? '<i class="text-primary cp-monitoring-services me-1 fs-6"></i>' : '';
        iconStatus = (prStatusDisplay !== '--' || drStatusDisplay !== '--') ? '<i class="text-primary cp-Job-status me-1 fs-6"></i>' : '';

        let row = `
    <tr>
        <td>Monitoring Workflow</td>
        <td>${iconWF}${prWfDisplay}</td>
        <td>${iconWF}${drWfDisplay}</td>
    </tr>
    <tr>
        <td>Application Status</td>
        <td>${iconStatus}${prStatusDisplay}</td>
        <td>${iconStatus}${drStatusDisplay}</td>
    </tr>`;

        return row;
    }

}
