using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.RsyncOption.Events.Update;

public class RsyncOptionUpdatedEventHandler : INotificationHandler<RsyncOptionUpdatedEvent>
{
    private readonly ILogger<RsyncOptionUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public RsyncOptionUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<RsyncOptionUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(RsyncOptionUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} RsyncOption",
            Entity = "RsyncOption",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"RsyncOption '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"RsyncOption '{updatedEvent.Name}' updated successfully.");
    }
}