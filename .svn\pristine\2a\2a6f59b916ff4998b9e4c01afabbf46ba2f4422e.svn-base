using ContinuityPatrol.Application.Features.ImpactActivity.Commands.Create;
using ContinuityPatrol.Application.Features.ImpactActivity.Commands.Update;
using ContinuityPatrol.Application.Features.ImpactActivity.Queries.GetDetail;
//using ContinuityPatrol.Application.Features.ImpactActivity.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ImpactActivityModel;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IImpactActivityService
{
    Task<List<ImpactActivityListVm>> GetImpactActivityList();
    Task<BaseResponse> CreateAsync(CreateImpactActivityCommand createImpactActivityCommand);
    Task<BaseResponse> UpdateAsync(UpdateImpactActivityCommand updateImpactActivityCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<ImpactActivityDetailVm> GetByReferenceId(string id);
    #region NameExist
   // Task<bool> IsImpactActivityNameExist(string name, string? id);
   #endregion
    #region Paginated
   // Task<PaginatedResult<ImpactActivityListVm>> GetPaginatedImpactActivitys(GetImpactActivityPaginatedListQuery query);
    #endregion
}
