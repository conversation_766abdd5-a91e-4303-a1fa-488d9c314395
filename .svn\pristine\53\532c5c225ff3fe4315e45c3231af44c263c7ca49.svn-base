﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class AlertReceiverRepositoryMocks
{
    public static Mock<IAlertReceiverRepository> CreateAlertReceiverRepository(List<AlertReceiver> alertReceivers)
    {
        var alertReceiverRepository = new Mock<IAlertReceiverRepository>();

        alertReceiverRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alertReceivers);

        alertReceiverRepository.Setup(repo => repo.AddAsync(It.IsAny<AlertReceiver>())).ReturnsAsync(
            (AlertReceiver alertReceiver) =>
            {
                alertReceiver.Id = new Fixture().Create<int>();

                alertReceiver.ReferenceId = new Fixture().Create<Guid>().ToString();

                alertReceivers.Add(alertReceiver);

                return alertReceiver;
            });

        return alertReceiverRepository;
    }

    public static Mock<IAlertReceiverRepository> UpdateAlertReceiverRepository(List<AlertReceiver> alertReceivers)
    {
        var alertReceiverRepository = new Mock<IAlertReceiverRepository>();

        alertReceiverRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alertReceivers);

        alertReceiverRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alertReceivers.SingleOrDefault(x => x.ReferenceId == i));

        alertReceiverRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AlertReceiver>())).ReturnsAsync((AlertReceiver alertReceiver) =>
        {
            var index = alertReceivers.FindIndex(item => item.ReferenceId == alertReceiver.ReferenceId);

            alertReceivers[index] = alertReceiver;

            return alertReceiver;

        });
        return alertReceiverRepository;
    }

    public static Mock<IAlertReceiverRepository> DeleteAlertReceiverRepository(List<AlertReceiver> alertReceivers)
    {
        var alertReceiverRepository = new Mock<IAlertReceiverRepository>();

        alertReceiverRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alertReceivers);

        alertReceiverRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alertReceivers.SingleOrDefault(x => x.ReferenceId == i));

        alertReceiverRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AlertReceiver>())).ReturnsAsync((AlertReceiver alertReceiver) =>
        {
            var index = alertReceivers.FindIndex(item => item.ReferenceId == alertReceiver.ReferenceId);

            alertReceiver.IsActive = false;

            alertReceivers[index] = alertReceiver;

            return alertReceiver;
        });

        return alertReceiverRepository;
    }

    public static Mock<IAlertReceiverRepository> GetAlertReceiverRepository(List<AlertReceiver> alertReceivers)
    {
        var alertReceiverRepository = new Mock<IAlertReceiverRepository>();

        alertReceiverRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alertReceivers);

        alertReceiverRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alertReceivers.SingleOrDefault(x => x.ReferenceId == i));

        return alertReceiverRepository;
    }

    public static Mock<IAlertReceiverRepository> GetAlertReceiverEmptyRepository()
    {
        var alertReceiverRepository = new Mock<IAlertReceiverRepository>();

        alertReceiverRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<AlertReceiver>());
        
        return alertReceiverRepository;
    }

    public static Mock<IAlertReceiverRepository> GetPaginatedAlertReceiverRepository(List<AlertReceiver> alertReceivers)
    {
        var alertReceiverRepository = new Mock<IAlertReceiverRepository>();

        //var queryableAlertReceiver = alertReceivers.BuildMock();

        //alertReceiverRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableAlertReceiver);

        alertReceiverRepository.Setup(repo => repo.PaginatedListAllAsync(
        It.IsAny<int>(),
        It.IsAny<int>(),
        It.IsAny<Specification<AlertReceiver>>(),
        It.IsAny<string>(),
        It.IsAny<string>()))
    .ReturnsAsync((int pageNumber, int pageSize, Specification<AlertReceiver> spec, string sortColumn, string sortOrder) =>
    {
        var sortedAlertreceiver = alertReceivers.AsQueryable();

        if (spec.Criteria != null)
        {
            sortedAlertreceiver = sortedAlertreceiver.Where(spec.Criteria);
        }

        if (!string.IsNullOrWhiteSpace(sortColumn))
        {
            sortedAlertreceiver = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                ? sortedAlertreceiver.OrderByDescending(c => c.Name)
                : sortedAlertreceiver.OrderBy(c => c.Name);
        }

        var totalCount = sortedAlertreceiver.Count();
        var paginated = sortedAlertreceiver
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        return PaginatedResult<AlertReceiver>.Success(paginated, totalCount, pageNumber, pageSize);
    });

        return alertReceiverRepository;
    }

    public static Mock<IAlertReceiverRepository> GetAlertReceiverNameUniqueRepository(List<AlertReceiver> alertReceivers)
    {
        var alertReceiverRepository = new Mock<IAlertReceiverRepository>();

        alertReceiverRepository.Setup(repo => repo.IsAlertReceiverNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => alertReceivers.Exists(x => x.Name == i && x.ReferenceId == j));

        return alertReceiverRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateAlertReceiverEventRepository(List<UserActivity> userActivities)
    {
        var alertReceiverEventRepository = new Mock<IUserActivityRepository>();

        alertReceiverEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        alertReceiverEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return alertReceiverEventRepository;
    }
}