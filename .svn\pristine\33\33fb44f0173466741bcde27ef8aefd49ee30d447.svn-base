﻿@using Microsoft.AspNetCore.Mvc.TagHelpers;
@using ContinuityPatrol.Domain.Entities;
@model ContinuityPatrol.Domain.ViewModels.DataSetModel.DataSetModel;
@Html.AntiForgeryToken()

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}


<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title" >
                <i class="cp-configure-dataset"></i><span>Dataset </span>
            </h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" aria-expanded="false" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="dataSetName=" id="DatasetName">
                                        <label class="form-check-label" for="DatasetName">
                                            Name
                                        </label>
                                    </div>
                                </li>
                              @*<li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="description=" id="Description">
                                        <label class="form-check-label" for="Description">
                                            Description
                                        </label>
                                    </div>
                                </li>*@
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="storedQuery=" id="storedQuery">
                                        <label class="form-check-label" for="StoredQuery">
                                            Stored Query
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm" id="btnDataSetCreate" data-bs-toggle="modal"
                        data-bs-target="#CreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table class="table table-hover dataTable" style="width:100%" id="dataSetTabel">
                <thead>
                    <tr>
                        <th class="SrNo_th" >Sr. No.</th>
                        <th> Name</th>
                        <th> Description</th>
                        <th> Stored Query</th>
                        <th class="Action-th">Action</th>
                    </tr>                   

                </thead>
                <tbody>

                </tbody>
            </table>
        </div>
    </div>

    <div id="AdminCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.CreateAndEdit" aria-hidden="true"></div>
    <div id="AdminDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.Delete" aria-hidden="true"></div>
    <!--Modal Create-->
    <div class="modal fade " id="CreateModal" data-bs-backdrop="static" aria-labelledby="configureModalLabel" aria-hidden="true">
        <partial name="Configuration" />
    </div>
    <!--Modal Delete-->
    <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <partial name="Delete" />
    </div>
    <!--Modal Dataset-->
    <div class="modal fade " id="datasetModal" data-bs-backdrop="static" aria-labelledby="datasetModalLabel" aria-hidden="true">
        <partial name="QueryList" />
    </div>


</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Admin/Dataset/ConfigureDataset/DataSet.js"></script>
<script src="~/lib/jquery-multiselect/multiselect.plugin.js"></script>
<script>
    $('select').on('select2:closing', () => {
        $('.select2-selection').width('auto');
        var $choice = $('.select2-selection__choice');
        $choice.first().show();
        $choice.slice(7).hide();
        $choice.eq(7).after(`<li class='select2-selection__choice select2-selection__choice_more'>...</li>`);
    });
</script>
<script type="text/javascript">
    // jQuery(document).ready(function ($) {
       
    // });
</script>