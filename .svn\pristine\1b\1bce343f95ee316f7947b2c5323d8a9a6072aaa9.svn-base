using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;

namespace ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetList;

public class
    GetBulkImportActionResultListQueryHandler : IRequestHandler<GetBulkImportActionResultListQuery,
        List<BulkImportActionResultListVm>>
{
    private readonly IBulkImportActionResultRepository _bulkImportActionResultRepository;
    private readonly IMapper _mapper;

    public GetBulkImportActionResultListQueryHandler(IMapper mapper,
        IBulkImportActionResultRepository bulkImportActionResultRepository)
    {
        _mapper = mapper;
        _bulkImportActionResultRepository = bulkImportActionResultRepository;
    }

    public async Task<List<BulkImportActionResultListVm>> Handle(GetBulkImportActionResultListQuery request,
        CancellationToken cancellationToken)
    {
        var bulkImportActionResults = await _bulkImportActionResultRepository.ListAllAsync();

        if (bulkImportActionResults.Count <= 0) return new List<BulkImportActionResultListVm>();

        return _mapper.Map<List<BulkImportActionResultListVm>>(bulkImportActionResults);
    }
}