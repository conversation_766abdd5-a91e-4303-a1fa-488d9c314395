﻿using ContinuityPatrol.Application.Features.Server.Events.InfraSummaryEvents.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Events
{
    public class ServerInfraSummaryCreatedEventTests
    {
        private readonly Mock<IInfraSummaryRepository> _mockInfraSummaryRepository;
        private readonly Mock<ILogger<ServerInfraSummaryCreatedEventHandler>> _mockLogger;
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly ServerInfraSummaryCreatedEventHandler _handler;

        public ServerInfraSummaryCreatedEventTests()
        {
            _mockInfraSummaryRepository = new Mock<IInfraSummaryRepository>();
            _mockLogger = new Mock<ILogger<ServerInfraSummaryCreatedEventHandler>>();
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();

            _handler = new ServerInfraSummaryCreatedEventHandler(
                _mockLogger.Object,
                _mockInfraSummaryRepository.Object,
                _mockLoggedInUserService.Object);
        }

        [Fact]
        public async Task Handle_InfraSummaryDoesNotExist_AddNewInfraSummary()
        {
            var createdEvent = new ServerInfraSummaryCreatedEvent
            {
                TypeId = "type-id-123",
                Type = "Server",
                Logo = "ServerLogo.png",
                BusinessServiceId = "service-id-456",
                CompanyId = "company-id-789"
            };

            _mockInfraSummaryRepository
                .Setup(repo => repo.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    createdEvent.Type,
                    createdEvent.BusinessServiceId,
                    createdEvent.CompanyId))
                .ReturnsAsync((Domain.Entities.InfraSummary)null);

            await _handler.Handle(createdEvent, CancellationToken.None);

            _mockInfraSummaryRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.InfraSummary>(summary =>
                summary.TypeId == createdEvent.TypeId &&
                summary.Type == createdEvent.Type &&
                summary.Logo == createdEvent.Logo &&
                summary.Count == 1 &&
                summary.BusinessServiceId == createdEvent.BusinessServiceId &&
                summary.CompanyId == createdEvent.CompanyId
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation($"InfraSummary '{createdEvent.Type}' created successfully."), Times.Once);
        }

        [Fact]
        public async Task Handle_InfraSummaryExists_UpdateExistingInfraSummary()
        {
            var createdEvent = new ServerInfraSummaryCreatedEvent
            {
                TypeId = "type-id-123",
                Type = "Server",
                Logo = "UpdatedLogo.png",
                BusinessServiceId = "service-id-456",
                CompanyId = "company-id-789"
            };

            var existingSummary = new Domain.Entities.InfraSummary
            {
                TypeId = "type-id-123",
                Type = "Server",
                Logo = "OldLogo.png",
                Count = 2,
                BusinessServiceId = "service-id-456",
                CompanyId = "company-id-789"
            };

            _mockInfraSummaryRepository
                .Setup(repo => repo.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    createdEvent.Type,
                    createdEvent.BusinessServiceId,
                    createdEvent.CompanyId))
                .ReturnsAsync(existingSummary);

            await _handler.Handle(createdEvent, CancellationToken.None);

            _mockInfraSummaryRepository.Verify(repo => repo.UpdateAsync(It.Is<Domain.Entities.InfraSummary>(summary =>
                summary.TypeId == createdEvent.TypeId &&
                summary.Type == createdEvent.Type &&
                summary.Logo == createdEvent.Logo &&
                summary.Count == 3 &&
                summary.BusinessServiceId == createdEvent.BusinessServiceId &&
                summary.CompanyId == createdEvent.CompanyId
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation($"InfraSummary '{createdEvent.Type}' created successfully."), Times.Once);
        }
    }
}
