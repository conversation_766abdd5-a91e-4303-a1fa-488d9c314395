﻿namespace ContinuityPatrol.Application.Features.LicenseHistory.Queries.GetDetail;

public class GetLicenseHistoryDetailQueryHandler : IRequestHandler<GetLicenseHistoryDetailQuery, LicenseHistoryDetailVm>
{
    private readonly ILicenseHistoryRepository _licenseHistoryRepository;
    private readonly IMapper _mapper;

    public GetLicenseHistoryDetailQueryHandler(ILicenseHistoryRepository licenseHistoryRepository, IMapper mapper)
    {
        _licenseHistoryRepository = licenseHistoryRepository;
        _mapper = mapper;
    }

    public async Task<LicenseHistoryDetailVm> Handle(GetLicenseHistoryDetailQuery request,
        CancellationToken cancellationToken)
    {
        var licenseHistory = await _licenseHistoryRepository.GetLicenseHistoryByLicenseId(request.LicenseId);

        Guard.Against.NullOrDeactive(licenseHistory, nameof(Domain.Entities.LicenseHistory),
            new NotFoundException(nameof(Domain.Entities.LicenseHistory), request.LicenseId));

        var historyDetailVm = _mapper.Map<LicenseHistoryDetailVm>(licenseHistory);

        return historyDetailVm ??
               throw new NotFoundException(nameof(Domain.Entities.LicenseHistory), request.LicenseId);
    }
}