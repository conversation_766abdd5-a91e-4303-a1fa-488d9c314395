﻿namespace ContinuityPatrol.Domain.Views;

public class WorkflowHistoricalFailureRateView
{
    public int WorkflowId { get; set; }
    public string ReferenceId { get; set; }  
    public string WorkflowName { get; set; }
    public int TotalRuns { get; set; }
    public int FailedRuns { get; set; }
    public decimal FailureRate { get; set; }//decimal
    public string FailureFormula { get; set; }
    public int WeightagePoints { get; set; }
}
