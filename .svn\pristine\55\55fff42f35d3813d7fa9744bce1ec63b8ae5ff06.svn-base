using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IApprovalMatrixApprovalRepository : IRepository<ApprovalMatrixApproval>
{
    Task<bool> IsNameExist(string name, string id);
    Task<List<ApprovalMatrixApproval>> GetApprovalMatrixByApprovalIds(List<string> approvalIds);
    Task<List<ApprovalMatrixApproval>> GetUnapprovedByRequestId(string requestId);
}