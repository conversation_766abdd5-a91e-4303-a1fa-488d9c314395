﻿using ContinuityPatrol.Application.Features.PluginManagerHistory.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.PluginManagerHistory.Events;

public class PluginManagerHistoryUpdatedEventHandlerTests : IClassFixture<PluginManagerHistoryFixture>
{
    private readonly PluginManagerHistoryFixture _pluginManagerHistoryFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly PluginManagerHistoryUpdatedEventHandler _handler;

    public PluginManagerHistoryUpdatedEventHandlerTests(PluginManagerHistoryFixture pluginManagerHistoryFixture)
    {
        _pluginManagerHistoryFixture = pluginManagerHistoryFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockPluginManagerHistoryEventLogger = new Mock<ILogger<PluginManagerHistoryUpdatedEventHandler>>();

        _mockUserActivityRepository = PluginManagerHistoryRepositoryMocks.CreatePluginManagerHistoryEventRepository(_pluginManagerHistoryFixture.UserActivities);

        _handler = new PluginManagerHistoryUpdatedEventHandler(mockLoggedInUserService.Object, mockPluginManagerHistoryEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdatePluginManagerHistoryEventUpdated()
    {
        _pluginManagerHistoryFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_pluginManagerHistoryFixture.PluginManagerHistoryUpdatedEvent, CancellationToken.None);

        result.Equals(_pluginManagerHistoryFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_pluginManagerHistoryFixture.PluginManagerHistoryUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_UpdatePluginManagerHistoryEventUpdated()
    {
        _pluginManagerHistoryFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_pluginManagerHistoryFixture.PluginManagerHistoryUpdatedEvent, CancellationToken.None);

        result.Equals(_pluginManagerHistoryFixture.UserActivities[0].Id);

        result.Equals(_pluginManagerHistoryFixture.PluginManagerHistoryUpdatedEvent.PluginManagerName);

        await Task.CompletedTask;
    }
}