﻿
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateState;
using ContinuityPatrol.Application.Features.InfraObject.Events.UpdateStateToUnlock;

namespace ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateStateToUnlock;

public class UpdateStateToUnlockCommandHandler : IRequestHandler<UpdateStateToUnlockCommand, UpdateStateToUnlockResponse>
{
    private readonly IDashboardViewRepository _dashboardViewRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly ILogger<UpdateStateToUnlockCommandHandler> _logger;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IServerRepository _serverRepository;
    private readonly IDatabaseRepository _databaseRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly IInfraObjectInfoRepository _infraObjectInfoRepository;
    private bool _isAllUpdated = true;
        
    public UpdateStateToUnlockCommandHandler(
        IMapper mapper, IInfraObjectRepository infraObjectRepository,
        IDashboardViewRepository dashboardViewRepository, ILogger<UpdateStateToUnlockCommandHandler> logger,
        IPublisher publisher, IServerRepository serverRepository, IDatabaseRepository databaseRepository, IInfraObjectInfoRepository infraObjectInfoRepository,
        ILicenseManagerRepository licenseManagerRepository)
    {
        _mapper = mapper;
        _infraObjectRepository = infraObjectRepository;
        _dashboardViewRepository = dashboardViewRepository;
        _logger = logger;
        _publisher = publisher;
        _serverRepository = serverRepository;
        _databaseRepository = databaseRepository;
        _infraObjectInfoRepository = infraObjectInfoRepository;
        _licenseManagerRepository = licenseManagerRepository;

    }
    public async Task<UpdateStateToUnlockResponse> Handle(UpdateStateToUnlockCommand request, CancellationToken cancellationToken)
    {
        var serverList = (await _serverRepository.ListAllAsync()).ToList();

        var databaseList = (await _databaseRepository.ListAllAsync()).ToList();

        var infraObjectLockedList = await _infraObjectRepository.GetLockedInfraObjectListByIds(request.updateStateToUnlocks.Select(x => x.Id).ToList());

        var servers = serverList?.Where(server => infraObjectLockedList.Any(infra=>infra.ServerProperties !=null &&infra.ServerProperties.Contains(server.ReferenceId)))
            .Select(ent => new
            {
                EntityId = ent.ReferenceId,
                EntityName = ent.Name,
                LicenseId = ent.LicenseId,
                LicenseKey = ent.LicenseKey
            }).ToList();


        var databases = databaseList?.Where(database => infraObjectLockedList.Any(infra=>infra.DatabaseProperties !=null&& infra.DatabaseProperties.Contains(database.ReferenceId)))
            .Select(ent => new
            {
                EntityId = ent.ReferenceId,
                EntityName = ent.Name,
                LicenseId = ent.LicenseId,
                LicenseKey = ent.LicenseKey
            }).ToList();

        var entityList = servers.Concat(databases);

        foreach (var infra in request.updateStateToUnlocks)
            try
            {
                var eventToUpdate = infraObjectLockedList.Where(x => x.ReferenceId.Equals(infra.Id)).FirstOrDefault();

                if (eventToUpdate == null)
                    continue;

                var infraEntities = entityList.Where(x => eventToUpdate is not null && (eventToUpdate.ServerProperties.Contains(x.EntityId)|| eventToUpdate.DatabaseProperties.Contains(x.EntityId))).ToList();

                    var licenseIds = infraEntities.DistinctBy(x => x.LicenseId).Select(x => x.LicenseId).ToList();

                    var licenseDtls = await _licenseManagerRepository.GetLicenseExpiryDateByIds(licenseIds);

                    if (licenseDtls.Any(x => x.IsExpired))
                    {
                        _isAllUpdated = false;
                        _logger.LogInformation(
                               $"The InfraObject '{eventToUpdate.Name}' is in a locked state due to some of the mapped component licence was expired.");
                        continue;
                    }
                    var dataLagDtl = await _dashboardViewRepository.GetBusinessViewByInfraObjectId(infra.Id);

                    var infraObjectinfo = await _infraObjectInfoRepository.GetInfraObjectInfoByInfraObjectId(infra.Id);

                    if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.InfraObject), infra.Id);

                    eventToUpdate.State = infraObjectinfo?.PreviousState ?? "Maintenance";

                    //dataLagDtl.State = eventToUpdate.State;

                    //_mapper.Map(infra, eventToUpdate, typeof(UpdateInfraObjectStateCommand),
                    //    typeof(Domain.Entities.InfraObject));

                    await _infraObjectRepository.UpdateAsync(eventToUpdate);
                if (dataLagDtl is not null)
                {
                    eventToUpdate.Reason = infra.Reason;
                    await _dashboardViewRepository.UpdateAsync(dataLagDtl);
                }

                    await _publisher.Publish(
                       new UpdateInfraObjectStateTounlockEvent { InfraObjectName = eventToUpdate.Name, State = infra.State },
                        cancellationToken);

            }
            catch (Exception exc)
            {
                _logger.LogError(exc, $"Exception occurred while unlock infraObject state {infra.State}");
                continue;
            }
        return new UpdateStateToUnlockResponse
        {
            Success = true,

            Message = _isAllUpdated ? "InfraObject states unlocked successfully." : "InfraObject states unlocked successfully,but some components cannot be updated."
        };
    }
}
