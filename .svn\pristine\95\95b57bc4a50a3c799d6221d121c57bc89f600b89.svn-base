using ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Infrastructure.Hubs;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using AutoMapper;
using MediatR;
using ContinuityPatrol.Infrastructure.Contract;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class BulkImportRepositoryMocks
{
    public static Mock<IBulkImportOperationGroupRepository> CreateBulkImportOperationGroupRepository(List<BulkImportOperationGroup> bulkImportOperationGroups)
    {
        var mockBulkImportOperationGroupRepository = new Mock<IBulkImportOperationGroupRepository>();

        mockBulkImportOperationGroupRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportOperationGroups);

        mockBulkImportOperationGroupRepository.Setup(repo => repo.AddAsync(It.IsAny<BulkImportOperationGroup>())).ReturnsAsync(
            (BulkImportOperationGroup bulkImportOperationGroup) =>
            {
                bulkImportOperationGroup.Id = new Fixture().Create<int>();
                bulkImportOperationGroup.ReferenceId = new Fixture().Create<Guid>().ToString();
                bulkImportOperationGroups.Add(bulkImportOperationGroup);
                return bulkImportOperationGroup;
            });

        mockBulkImportOperationGroupRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BulkImportOperationGroup>()));
        //.Returns(Task.CompletedTask);

        mockBulkImportOperationGroupRepository.Setup(repo => repo.DeleteAsync(It.IsAny<BulkImportOperationGroup>()));
           // .Returns(Task.CompletedTask);

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportOperationGroups.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetBulkImportOperationGroupByBulkImportOperationIds(It.IsAny<List<string>>()))
            .ReturnsAsync((List<string> ids) => bulkImportOperationGroups.Where(x => ids.Contains(x.BulkImportOperationId)).ToList());

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetBulkImportOperationGroupByBulkImportOperationId(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportOperationGroups.Where(x => x.BulkImportOperationId == id).ToList());

        return mockBulkImportOperationGroupRepository;
    }

    public static Mock<IBulkImportActionResultRepository> CreateBulkImportActionResultRepository(List<BulkImportActionResult> bulkImportActionResults)
    {
        var mockBulkImportActionResultRepository = new Mock<IBulkImportActionResultRepository>();

        mockBulkImportActionResultRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportActionResults);

        mockBulkImportActionResultRepository.Setup(repo => repo.AddAsync(It.IsAny<BulkImportActionResult>())).ReturnsAsync(
            (BulkImportActionResult bulkImportActionResult) =>
            {
                bulkImportActionResult.Id = new Fixture().Create<int>();
                bulkImportActionResult.ReferenceId = new Fixture().Create<Guid>().ToString();
                bulkImportActionResults.Add(bulkImportActionResult);
                return bulkImportActionResult;
            });

        mockBulkImportActionResultRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BulkImportActionResult>()));
        // .Returns(Task.CompletedTask);

        mockBulkImportActionResultRepository.Setup(repo => repo.DeleteAsync(It.IsAny<BulkImportActionResult>()));
           // .Returns(Task.CompletedTask);

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportActionResults.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByEntityIdAndBulkImportOperationId(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string entityId, string operationId) => 
                bulkImportActionResults.FirstOrDefault(x => x.EntityId == entityId && x.BulkImportOperationId == operationId));

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByOperationIdAndOperationGroupId(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string operationId, string operationGroupId) => 
                bulkImportActionResults.Where(x => x.BulkImportOperationId == operationId && x.BulkImportOperationGroupId == operationGroupId).ToList());

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByOperationIdsAndOperationGroupIds(It.IsAny<List<string>>(), It.IsAny<List<string>>()))
            .ReturnsAsync((List<string> operationIds, List<string> operationGroupIds) => 
                bulkImportActionResults.Where(x => operationIds.Contains(x.BulkImportOperationId) && operationGroupIds.Contains(x.BulkImportOperationGroupId)).ToList());

        mockBulkImportActionResultRepository.Setup(repo => repo.GetActionByOperationGroupIdAndEntityType(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string operationGroupId, string entityType) => 
                bulkImportActionResults.FirstOrDefault(x => x.BulkImportOperationGroupId == operationGroupId && x.EntityType == entityType));

        return mockBulkImportActionResultRepository;
    }

    public static BulkImportHelperService CreateBulkImportHelperService()
    {
        // Since BulkImportHelperService methods are not virtual, we cannot mock them with Moq
        // Instead, we'll create a test stub that provides minimal functionality for testing
        // This requires creating mock dependencies for the BulkImportHelperService constructor

        var mockMapper = new Mock<IMapper>();
        var mockServerRepository = new Mock<IServerRepository>();
        var mockDatabaseRepository = new Mock<IDatabaseRepository>();
        var mockReplicationRepository = new Mock<IReplicationRepository>();
        var mockInfraObjectRepository = new Mock<IInfraObjectRepository>();
        var mockPublisher = new Mock<IPublisher>();
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var mockLicenseManagerRepository = new Mock<ILicenseManagerRepository>();
        var mockSiteRepository = new Mock<ISiteRepository>();
        var mockSiteTypeRepository = new Mock<ISiteTypeRepository>();
        var mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();
        var mockBulkImportActionResultRepository = new Mock<IBulkImportActionResultRepository>();
        var mockLicenseValidationService = new Mock<ILicenseValidationService>();
        var mockBulkImportOperationGroupRepository = new Mock<IBulkImportOperationGroupRepository>();
        var mockWorkflowRepository = new Mock<IWorkflowRepository>();
        var mockNodeRepository = new Mock<INodeRepository>();
        var mockJobRepository = new Mock<IJobRepository>();
        var mockWorkflowInfraObjectRepository = new Mock<IWorkflowInfraObjectRepository>();
        var mockInfraObjectSchedulerRepository = new Mock<IInfraObjectSchedulerRepository>();
        var mockWorkflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();
        var mockWorkflowHistoryRepository = new Mock<IWorkflowHistoryRepository>();
        var mockVersionManager = new Mock<IVersionManager>();
        var mockHubContext = new Mock<IHubContext<NotificationHub>>();
        var mockLogger = new Mock<ILogger<BulkImportHelperService>>();
        var mockCyberAirGapRepository = new Mock<ICyberAirGapRepository>();
        var mockJobScheduler = new Mock<IJobScheduler>();

        // Setup basic behaviors for repositories that might be called
        mockServerRepository.Setup(x => x.GetServerByServerName(It.IsAny<string>()))
            .ReturnsAsync((Server)null);

        mockBulkImportActionResultRepository.Setup(x => x.AddAsync(It.IsAny<BulkImportActionResult>()))
            .ReturnsAsync((BulkImportActionResult entity) => entity);

        return new BulkImportHelperService(
            mockMapper.Object,
            mockServerRepository.Object,
            mockDatabaseRepository.Object,
            mockReplicationRepository.Object,
            mockInfraObjectRepository.Object,
            mockPublisher.Object,
            mockLoggedInUserService.Object,
            mockLicenseManagerRepository.Object,
            mockSiteRepository.Object,
            mockSiteTypeRepository.Object,
            mockLoadBalancerRepository.Object,
            mockBulkImportActionResultRepository.Object,
            mockLicenseValidationService.Object,
            mockBulkImportOperationGroupRepository.Object,
            mockWorkflowRepository.Object,
            mockNodeRepository.Object,
            mockJobRepository.Object,
            mockWorkflowInfraObjectRepository.Object,
            mockInfraObjectSchedulerRepository.Object,
            mockWorkflowProfileInfoRepository.Object,
            mockWorkflowHistoryRepository.Object,
            mockVersionManager.Object,
            mockHubContext.Object,
            mockLogger.Object,
            mockCyberAirGapRepository.Object,
            mockJobScheduler.Object);
    }
}
