﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.UserLogin.Events.Logout;

public class UserLogoutEventHandler : INotificationHandler<UserLogoutEvent>
{
    private readonly ILogger<UserLogoutEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public UserLogoutEventHandler(ILoggedInUserService userService, ILogger<UserLogoutEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(UserLogoutEvent logoutEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Entity = Modules.UserLogout.ToString(),
            Action = $"{Modules.UserLogout}",
            ActivityType = ActivityType.Logout.ToString(),
            ActivityDetails = $"User '{logoutEvent.LoginName}' Logged out successfully.",
            CreatedBy = logoutEvent.UserId,
            LastModifiedBy = logoutEvent.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"User '{logoutEvent.LoginName}' Logged out successfully.");
    }
}