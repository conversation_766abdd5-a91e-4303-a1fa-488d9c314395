﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class UserInfraObjectRepositoryMocks
{
    public static Mock<IUserInfraObjectRepository> DeleteUserInfraObjectRepository(List<UserInfraObject> userInfraObjects)
    {
        var mockUserInfraObjectRepository = new Mock<IUserInfraObjectRepository>();

        mockUserInfraObjectRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userInfraObjects);

        mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectByUserIdAsync(It.IsAny<string>())).ReturnsAsync((string userId) =>
        {
            if (userInfraObjects == null || !userInfraObjects.Any())
            {
                return null;
            }
            var userInfraObject = userInfraObjects.FirstOrDefault(ui => ui?.UserId == userId);
            return userInfraObject ?? null;
        });

        mockUserInfraObjectRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => userInfraObjects.SingleOrDefault(x => x.ReferenceId == i));

        mockUserInfraObjectRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserInfraObject>())).ReturnsAsync((UserInfraObject userInfraObject) =>
        {
            if (userInfraObject == null)
            {
                return null;
            }
            var index = userInfraObjects.FindIndex(ui => ui?.UserId == userInfraObject.UserId);
            if (index >= 0)
            {
                userInfraObject.IsActive = false;

                userInfraObjects[index] = userInfraObject;
            }
            else
            {
                throw new Exception("UserInfraObject not found in the list.");
            }
            return userInfraObject;
        });

        return mockUserInfraObjectRepository;
    }

    public static Mock<IUserInfraObjectRepository> UpdateUserInfraObjectRepository(List<UserInfraObject> userInfraObjects)
    {
        var mockUserInfoRepository = new Mock<IUserInfraObjectRepository>();

        mockUserInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userInfraObjects);

        mockUserInfoRepository.Setup(repo => repo.GetUserInfraObjectByUserIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => userInfraObjects.SingleOrDefault(x => x.UserId == i));

        mockUserInfoRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserInfraObject>())).ReturnsAsync((UserInfraObject userInfraObject) =>
        {
            var index = userInfraObjects.FindIndex(item => item.UserId == userInfraObject.UserId);

            userInfraObjects[index] = userInfraObject;

            return userInfraObject;
        });

        return mockUserInfoRepository;
    }

    public static Mock<IUserInfraObjectRepository> GetUserInfraObjectByUserIdAsyncRepository(UserInfraObject userInfraObjects)
    {
        var mockUserInfraObjectRepository = new Mock<IUserInfraObjectRepository>();

        mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectByUserIdAsync(It.IsAny<string>())).ReturnsAsync(userInfraObjects);

        return mockUserInfraObjectRepository;
    }

    public static Mock<IUserInfraObjectRepository> GetUserInfraObjectRepository(List<UserInfraObject> userInfraObjects)
    {
        var mockUserInfraObjectRepository = new Mock<IUserInfraObjectRepository>();

        mockUserInfraObjectRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userInfraObjects);

        mockUserInfraObjectRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => userInfraObjects.SingleOrDefault(x => x.ReferenceId == i));

        return mockUserInfraObjectRepository;
    }

    public static Mock<IUserInfraObjectRepository> GetPaginatedUserInfraObjectRepository(List<UserInfraObject> userInfraObjects)
    {
        var mockUserInfraObjectRepository = new Mock<IUserInfraObjectRepository>();

        var queryableUsers = userInfraObjects.BuildMock();

        mockUserInfraObjectRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableUsers);

        return mockUserInfraObjectRepository;
    }
}
