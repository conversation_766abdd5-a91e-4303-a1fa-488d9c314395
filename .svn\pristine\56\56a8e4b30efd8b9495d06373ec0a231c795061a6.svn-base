﻿using ContinuityPatrol.Application.Features.Form.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Form.Commands;

public class UpdateFormTests : IClassFixture<FormFixture>, IClassFixture<FormHistoryFixture>
{
    private readonly FormFixture _formFixture;

    private readonly Mock<IFormRepository> _mockFormRepository;

    private readonly UpdateFormCommandHandler _handler;

    public UpdateFormTests(FormFixture formFixture, FormHistoryFixture formHistoryFixture)
    {
        _formFixture = formFixture;

        var mockPublisher = new Mock<IPublisher>();

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockVersionManager = new Mock<IVersionManager>();

        _mockFormRepository = FormRepositoryMocks.UpdateFormRepository(_formFixture.Forms);

        var mockFormHistoryRepository = FormHistoryRepositoryMocks.UpdateFormHistoryRepository(formHistoryFixture.FormHistories);

        _handler = new UpdateFormCommandHandler(_formFixture.Mapper, mockPublisher.Object, _mockFormRepository.Object, mockFormHistoryRepository.Object, mockLoggedInUserService.Object, mockVersionManager.Object);
    }

    [Fact]
    public async Task Handle_Valid_Form_UpdateToFormsRepo()
    {
        _formFixture.UpdateFormCommand.Id = _formFixture.Forms[0].ReferenceId;

        var result = await _handler.Handle(_formFixture.UpdateFormCommand, CancellationToken.None);

        var form = await _mockFormRepository.Object.GetByReferenceIdAsync(result.FormId);

        Assert.Equal(_formFixture.UpdateFormCommand.Name, form.Name);
    }

    [Fact]
    public async Task Handle_Return_Valid_FormResponse_When_FormUpdated()
    {
        _formFixture.UpdateFormCommand.Id = _formFixture.Forms[0].ReferenceId;

        var result = await _handler.Handle(_formFixture.UpdateFormCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateFormResponse));

        result.FormId.ShouldBeGreaterThan(0.ToString());

        result.FormId.ShouldBe(_formFixture.UpdateFormCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _formFixture.UpdateFormCommand.Id = _formFixture.Forms[0].ReferenceId;

        await _handler.Handle(_formFixture.UpdateFormCommand, CancellationToken.None);

        _mockFormRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockFormRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.Form>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_Invalid_FormId()
    {
        _formFixture.UpdateFormCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_formFixture.UpdateFormCommand, CancellationToken.None));
    }
}