﻿using ContinuityPatrol.Application.Features.Company.Events.Update;

namespace ContinuityPatrol.Application.Features.Company.Commands.Update;

public class UpdateCompanyCommandHandler : IRequestHandler<UpdateCompanyCommand, UpdateCompanyResponse>
{
    private readonly ICompanyRepository _companyRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateCompanyCommandHandler(IMapper mapper, ICompanyRepository companyRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _companyRepository = companyRepository;
        _publisher = publisher;
    }

    public async Task<UpdateCompanyResponse> Handle(UpdateCompanyCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _companyRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Company), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateCompanyCommand), typeof(Domain.Entities.Company));

        await _companyRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateCompanyResponse
        {
            Message = Message.Update(nameof(Domain.Entities.Company), eventToUpdate.Name),

            CompanyId = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new CompanyUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}