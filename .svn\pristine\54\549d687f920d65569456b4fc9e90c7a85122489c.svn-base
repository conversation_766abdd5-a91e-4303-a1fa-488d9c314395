﻿using ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Commands.Create;
using ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Commands.Update;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IRpoSlaDeviationReportService
{
    Task<BaseResponse> CreateAsync(CreateRpoSlaDeviationReportCommand createRpoSlaDeviationReportCommand);
    Task<BaseResponse> UpdateAsync(UpdateRpoSlaDeviationReportCommand updateRpoSlaDeviationReportCommand);
    Task<BaseResponse> DeleteAsync(string rpoSlaDeviationReportId);
}