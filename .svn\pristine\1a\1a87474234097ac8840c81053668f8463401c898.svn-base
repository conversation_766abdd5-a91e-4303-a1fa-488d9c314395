﻿using ContinuityPatrol.Application.Features.Server.Queries.GetRoleType;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Queries;

public class GetServerRoleTypeQueryHandlerTests : IClassFixture<ServerFixture>
{
    private readonly ServerFixture _serverFixture;

    private Mock<IServerRepository> _mockServerRepository;
    private Mock<IServerViewRepository> _mockServerViewRepository;

    private readonly GetServerRoleTypeQueryHandler _handler;

    public GetServerRoleTypeQueryHandlerTests(ServerFixture serverFixture)
    {
        _serverFixture = serverFixture;

        _mockServerViewRepository = new Mock<IServerViewRepository>();

        _mockServerRepository = ServerRepositoryMocks.GetServerRepository(_serverFixture.Servers);

        _handler = new GetServerRoleTypeQueryHandler(_serverFixture.Mapper, _mockServerViewRepository.Object);

    }

    [Fact]
    public async Task Handle_Return_Valid_ServersRoleType()
    {
        var result = await _handler.Handle(new GetServerRoleTypeQuery { RoleTypeId = _serverFixture.Servers[0].ServerType }, CancellationToken.None);

        result.ShouldBeOfType<List<ServerRoleTypeVm>>();

        result[0].Id.ShouldBe(_serverFixture.Servers[0].ReferenceId);
        result[0].Name.ShouldBe(_serverFixture.Servers[0].Name);
        result[0].SiteId.ShouldBe(_serverFixture.Servers[0].SiteId);
        result[0].SiteName.ShouldBe(_serverFixture.Servers[0].SiteName);
        result[0].RoleType.ShouldBe(_serverFixture.Servers[0].RoleType);
        result[0].Status.ShouldBe(_serverFixture.Servers[0].Status);
        result[0].ServerType.ShouldBe(_serverFixture.Servers[0].ServerType);
        result[0].OSType.ShouldBe(_serverFixture.Servers[0].OSType);
        result[0].Properties.ShouldBe(_serverFixture.Servers[0].Properties);
        result[0].LicenseKey.ShouldBe(_serverFixture.Servers[0].LicenseKey);

    }

    [Fact]
    public async Task Handle_ReturnEmptyRoleType_When_NoRecords()
    {
        _mockServerRepository = ServerRepositoryMocks.GetServerEmptyRepository();

        var handler = new GetServerRoleTypeQueryHandler(_serverFixture.Mapper, _mockServerViewRepository.Object);

        var result = await handler.Handle(new GetServerRoleTypeQuery { RoleTypeId = _serverFixture.Servers[0].ServerType }, CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetServerRoleTypeQuery { RoleTypeId = _serverFixture.Servers[0].ServerType }, CancellationToken.None);

        _mockServerRepository.Verify(repo => repo.GetRoleType(It.IsAny<string>()), Times.Once);
    }
}
