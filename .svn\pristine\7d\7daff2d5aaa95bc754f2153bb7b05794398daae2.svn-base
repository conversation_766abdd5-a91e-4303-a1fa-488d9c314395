using System.Text.Json.Serialization;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;

namespace ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;

public class CreateBulkImportOperationCommand : IRequest<CreateBulkImportOperationResponse>
{
    [JsonIgnore] public string CompanyId { get; set; }
    [JsonIgnore] public string UserName { get; set; }
    public string Description { get; set; }
    [JsonIgnore] public string Status { get; set; }
    [JsonIgnore] public DateTime StartTime { get; set; }
    [JsonIgnore] public DateTime EndTime { get; set; }

    public List<CreateBulkImportOperationListCommand> BulkImportOperationList { get; set; }
}

public class CreateBulkImportOperationListCommand
{
    public List<CreateBulkDataServerListCommand> ServerList { get; set; }
    public List<CreateBulkDataDataBaseListCommand> DatabaseList { get; set; }
    public List<CreateBulkDataReplicationListCommand> ReplicationList { get; set; }
    public CreateBulkDataInfraObjectListCommand InfraObject { get; set; }
    public bool IsSwitchOver { get; set; }
    public string SwitchOverTemplate { get; set; }
    public bool IsFailOver { get; set; }
    public string FailOverTemplate { get; set; }
    public bool IsSwitchBack { get; set; }
    public string SwitchBackTemplate { get; set; }
    public bool IsFailBack { get; set; }
    public string FailBackTemplate { get; set; }

    //public void Sanitize()
    //{
    //    SwitchOverTemplate = SwitchOverTemplate?.Trim();
    //    FailOverTemplate = FailOverTemplate?.Trim();
    //    SwitchBackTemplate = SwitchBackTemplate?.Trim();
    //    FailBackTemplate = FailBackTemplate?.Trim();

    //    ServerList?.ForEach(s => s?.Sanitize());
    //    DatabaseList?.ForEach(d => d?.Sanitize());
    //    ReplicationList?.ForEach(r => r?.Sanitize());
    //    InfraObject?.Sanitize();
    //}

}

