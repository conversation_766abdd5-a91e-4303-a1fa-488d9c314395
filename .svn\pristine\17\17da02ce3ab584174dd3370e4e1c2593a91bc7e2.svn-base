﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        @* <h6 class="page_title"><i class="cp-monitoring"></i><span>RSync_App_Infra</span></h6> *@
        <h6 class="page_title" title="RSYNC DETAIL MONITORING">
            <i class="cp-monitoring"></i><span>RSYNC DETAIL MONITORING :</span>
            <span id="infraName"></span>
        </h6>
        @* <span><i class="cp-time me-2"></i>Last Monitored Time : 12/9/2022 1:39:31 PM</span> *@
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time :"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-0 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow"></i>Back</a>
        </div>
    </div>
    <div class="monitor_pages">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-2 mt-0">
            <div class="col-6 d-grid">
                <div class="col d-grid">
                    <div class="card Card_Design_None mb-2">
                        <div class="card-header card-title">
                            RSync_App_Infra

                        </div>
                        <div class="card-body pt-0 p-2">
                            <table class="table mb-0" style="table-layout:fixed">
                                <thead>
                                    <tr>
                                        <th>
                                            Component
                                        </th>
                                        <th class="text-primary">Primary</th>
                                        <th class="text-info">DR</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="fw-semibold text-truncate "><i class="text-secondary cp-server me-1"></i>Server Name</td>
                                        <td class="text-truncate" id="PR_Server_Name"></td>
                                        <td class="text-truncate" id="DR_Server_Name"></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold text-truncate "><i class="text-secondary cp-ip-address me-1"></i>IP Address/HostName</td>
                                        <td class="text-truncate" id="PR_Server_IpAddress1"></td>
                                        <td class="text-truncate" id="DR_Server_IpAddress1"></td>

                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
               
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center pt-0">
                        @* <img src="~/img/isomatric/solutiondiagram.svg" height="159px;" /> *@
                        <div id="Solution_Diagram" style="width:100%; height:100%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-2">

            <div class="col d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        Rsync Replication Details
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed" id="rsynctable">
                            @*<thead>
                                <tr>
                                    <th>Replication Monitoring</th>
                                    <th></th>

                                </tr>
                            </thead>
                            <tbody >
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-custom-server-4 me-1"></i>Primary Server</td>
                                    <td class="text-truncate" id="PR_Server_IpAddress"> </td>


                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-custom-server-3 me-1"></i>Secondary Server</td>
                                    <td class="text-truncate" id="DR_Server_IpAddress"> </td>

                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-replication-source me-1"></i>Source Replication Path
                                    </td>
                                    <td class="text-truncate" id="SourcePath"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-file-location me-1"></i>Destination Path
                                    </td>
                                    <td class="text-truncate" id="DestinationPath"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-last-copied-transaction me-1"></i>Number of files
                                    </td>
                                    <td class="text-truncate" id="TotalNumberoffiles"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-file-size me-1"></i>Total file size
                                    </td>
                                    <td class="text-truncate" id="TotalFilesSize"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-last-backup me-1"></i>Number of regular files transferred
                                    </td>
                                    <td class="text-truncate" id="NumberOfRegFilesTransfer"></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-last-backup me-1"></i>Total transferred file size
                                    </td>
                                    <td class="text-truncate" id="TotalTransferfileSize"></td>
                                </tr> *@
                            @* </tbody> *@
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 d-grid">
            <div class="card Card_Design_None mb-2 h-100" id="mssqlserver">
                <div class="card-header card-title" title="Service/Process/Workflow">Service/Process/Workflow</div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0 noDataimg" style="table-layout:fixed" id="tableCluster">
                        <thead class="align-middle">
                            <tr>
                                <th rowspan="2">Service / Process / Workflow Name</th>
                                <th colspan="2" class="text-center">Server IP/HostName</th>
                            </tr>
                            <tr>
                                <th id="prIp"></th>
                                <th id="drIp"></th>
                            </tr>
                        </thead>
                        <tbody id="mssqlserverbody">
                        </tbody>

                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/js/Monitoring/MonitoringRSync.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>
<script src="~/js/Monitoring/MonitoringServiceDetails.js"></script>