﻿using ContinuityPatrol.Application.Features.SmsConfiguration.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SmsConfiguration.Commands;

public class DeleteSmsConfigurationTests : IClassFixture<SmsConfigurationFixture>
{
    private readonly SmsConfigurationFixture _smsConfigurationFixture;

    private readonly Mock<IPublisher> _mockPublisher;

    private readonly Mock<ISmsConfigurationRepository> _mockSmsConfigurationRepository;

    private readonly DeleteSmsConfigurationCommandHandler _handler;

    public DeleteSmsConfigurationTests(SmsConfigurationFixture smsConfigurationFixture)
    {
        _smsConfigurationFixture = smsConfigurationFixture;

        _mockPublisher = new Mock<IPublisher>();

        _mockSmsConfigurationRepository = SmsConfigurationRepositoryMocks.DeleteSmsConfigurationRepository(_smsConfigurationFixture.SmsConfigurations);

        _handler = new DeleteSmsConfigurationCommandHandler(_mockSmsConfigurationRepository.Object, _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateReferenceIdAsyncIsActiveFalse_When_SmsConfigurationDeleted()
    {
        var result = await _handler.Handle(new DeleteSmsConfigurationCommand { Id = _smsConfigurationFixture.SmsConfigurations[0].ReferenceId }, CancellationToken.None);

        Assert.True(result.Success);

        var smsConfiguration = await _mockSmsConfigurationRepository.Object.GetByReferenceIdAsync(_smsConfigurationFixture.SmsConfigurations[0].ReferenceId);
        Assert.False(smsConfiguration.IsActive);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulSmsConfigurationResponse_When_SmsConfigurationDeleted()
    {
        var result = await _handler.Handle(new DeleteSmsConfigurationCommand { Id = _smsConfigurationFixture.SmsConfigurations[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteSmsConfigurationResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Return_IsActive_False_When_DeleteReferenceIdAsync_SmsConfiguration()
    {
        await _handler.Handle(new DeleteSmsConfigurationCommand { Id = _smsConfigurationFixture.SmsConfigurations[0].ReferenceId }, CancellationToken.None);

        var smsConfiguration = await _mockSmsConfigurationRepository.Object.GetByReferenceIdAsync(_smsConfigurationFixture.SmsConfigurations[0].ReferenceId);

        smsConfiguration.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidSmsConfigurationId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteSmsConfigurationCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteSmsConfigurationCommand { Id = _smsConfigurationFixture.SmsConfigurations[0].ReferenceId }, CancellationToken.None);

        _mockSmsConfigurationRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockSmsConfigurationRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.SmsConfiguration>()), Times.Once);
    }
}