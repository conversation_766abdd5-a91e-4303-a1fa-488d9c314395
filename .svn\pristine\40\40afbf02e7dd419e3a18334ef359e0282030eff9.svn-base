﻿using ContinuityPatrol.Application.Features.FormType.Commands.Create;
using ContinuityPatrol.Application.Features.FormType.Commands.Update;
using ContinuityPatrol.Application.Features.FormType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FormType.Queries.GetNames;
using ContinuityPatrol.Domain.ViewModels.FormTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class FormTypeProfile : Profile
{
    public FormTypeProfile()
    {
        CreateMap<FormType, CreateFormTypeCommand>().ReverseMap();
        CreateMap<UpdateFormTypeCommand, FormType>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<FormTypeViewModel, CreateFormTypeCommand>().ReverseMap();
        CreateMap<FormTypeViewModel, UpdateFormTypeCommand>().ReverseMap();
        //CreateMap<ImportFormTypeCommand, CreateFormTypeCommand>().ReverseMap();
        //CreateMap<ImportFormTypeCommand, UpdateFormTypeCommand>().ReverseMap();

        CreateMap<FormType, FormTypeDetailVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<FormType, FormTypeListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<FormType, FormTypeNameVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<PaginatedResult<FormType>, PaginatedResult<FormTypeListVm>>()
              .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}