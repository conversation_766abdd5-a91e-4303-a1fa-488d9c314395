﻿using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftCategoryList;

public class DriftCategoryListQueryHandler : IRequestHandler<DriftCategoryListQuery, DriftCategoryListVm>
{
    private readonly IDriftManagementMonitorStatusRepository _driftManagementMonitorStatusRepository;

    public DriftCategoryListQueryHandler(IDriftManagementMonitorStatusRepository driftManagementMonitorStatusRepository)
    {
        _driftManagementMonitorStatusRepository = driftManagementMonitorStatusRepository;
    }

    public async Task<DriftCategoryListVm> Handle(DriftCategoryListQuery request, CancellationToken cancellationToken)
    {
        var driftCategoryList = await _driftManagementMonitorStatusRepository.ListAllAsync();

        var filteredList = driftCategoryList.Select(item => item.CategoryProperties).ToList();

        var jsonString = JsonConvert.SerializeObject(filteredList);

        var jsonStrings = JsonConvert.DeserializeObject<List<string>>(jsonString);

        // Step 2: Deserialize each string in the list to DriftCategoryListVm objects

        if (jsonString.IsNullOrWhiteSpace()) return new DriftCategoryListVm();

        // var deserializeObject = JsonConvert.DeserializeObject<List<DriftCategoryListVm>>(jsonString);

        var deserializeObject = jsonStrings
            .Select(json => JsonConvert.DeserializeObject<DriftCategoryListVm>(json))
            .ToList();

        var totalSummary = new DriftCategoryListVm
        {
            CountMismatch = deserializeObject.Sum(x => x.CountMismatch),
            VersionMismatch = deserializeObject.Sum(x => x.VersionMismatch),
            ConfigMismatch = deserializeObject.Sum(x => x.ConfigMismatch),
            PolicyMismatch = deserializeObject.Sum(x => x.PolicyMismatch)
        };

        return totalSummary;
    }
}