using ContinuityPatrol.Application.Features.GlobalVariable.Events.Create;

namespace ContinuityPatrol.Application.Features.GlobalVariable.Commands.Create;

public class CreateGlobalVariableCommandHandler : IRequestHandler<CreateGlobalVariableCommand, CreateGlobalVariableResponse>
{
    private readonly IGlobalVariableRepository _globalVariableRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateGlobalVariableCommandHandler(IMapper mapper, IGlobalVariableRepository globalVariableRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _globalVariableRepository = globalVariableRepository;
    }

    public async Task<CreateGlobalVariableResponse> Handle(CreateGlobalVariableCommand request, CancellationToken cancellationToken)
    {
        var globalVariable = _mapper.Map<Domain.Entities.GlobalVariable>(request);

        globalVariable = await _globalVariableRepository.AddAsync(globalVariable);

        var response = new CreateGlobalVariableResponse
        {
            Message = Message.Create(nameof(Domain.Entities.GlobalVariable), globalVariable.VariableName),

            Id = globalVariable.ReferenceId
        };

        await _publisher.Publish(new GlobalVariableCreatedEvent { Name = globalVariable.VariableName }, cancellationToken);

        return response;
    }
}
