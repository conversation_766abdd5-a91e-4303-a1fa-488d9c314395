﻿namespace ContinuityPatrol.Application.Features.DataSetColumns.Queries.GetDetail;

public class GetDataSetColumnsDetailQueryHandler : IRequestHandler<GetDataSetColumnsDetailQuery, DataSetColumnsDetailVm>
{
    private readonly IDataSetColumnsRepository _dataSetColumnsRepository;
    private readonly IMapper _mapper;

    public GetDataSetColumnsDetailQueryHandler(IMapper mapper, IDataSetColumnsRepository dataSetColumnsRepository)
    {
        _mapper = mapper;
        _dataSetColumnsRepository = dataSetColumnsRepository;
    }

    public async Task<DataSetColumnsDetailVm> Handle(GetDataSetColumnsDetailQuery request,
        CancellationToken cancellationToken)
    {
        var dataSetColumns = await _dataSetColumnsRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(dataSetColumns, nameof(Domain.Entities.DataSetColumns),
            new NotFoundException(nameof(Domain.Entities.DataSetColumns), request.Id));

        var dataSetColumnsDetailDto = _mapper.Map<DataSetColumnsDetailVm>(dataSetColumns);

        return dataSetColumnsDetailDto;
    }
}