﻿using ContinuityPatrol.Application.Features.InfraObject.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObject.Validators;

public class CreateInfraObjectValidatorTests
{
    private readonly Mock<IInfraObjectRepository> _mockInfraObjectRepository;

    public List<Domain.Entities.InfraObject> InfraObjects { get; set; }

    public CreateInfraObjectValidatorTests()
    {
        InfraObjects = new Fixture().Create<List<Domain.Entities.InfraObject>>();

        _mockInfraObjectRepository = InfraObjectRepositoryMocks.CreateInfraObjectRepository(InfraObjects);
    }

    //Name

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Name_InInfraObject_WithEmpty(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Name_InInfraObject_IsNull(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = null;
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameNotNullRequired, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Name_InInfraObject_MinimumRange(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "AB";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameRangeRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Name_InInfraObject_MaximumRange(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectDescriptionContainsInvalid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Valid_Name_InInfraObject(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "  PTS  ";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Valid_Name_InInfraObject_With_DoubleSpace_InFront(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "  PTS Technosoft";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Valid_Name_InInfraObject_With_DoubleSpace_InBack(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "PTS Technosoft  ";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Valid_Name_InInfraObject_With_TripleSpace_InBetween(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "Pts   India";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Valid_Name_InInfraObject_With_SpecialCharacters_InBetween(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "Pts%$India";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Valid_Name_InInfraObject_With_SpecialCharacters_InFront(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "@#PtsIndia";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Valid_Name_InInfraObject_With_SpecialCharacters_Only(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "@#$%%^&&*><{";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Valid_Name_InInfraObject_With_Underscore_InFront(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "_PTS";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Valid_Name_InInfraObject_With_Underscore_InFront_AndBack(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "_PTS_";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Valid_Name_InInfraObject_With_Numbers_InFront(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "234PTS";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Valid_Name_InInfraObject_With_UnderscoreAndNumbers_InFront_AndUnderScore_InBack(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "_234PTS_";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Valid_Name_InInfraObject_With_Underscore_InFront_With_Numbers_InBack(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "_PTS786";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_Valid_Name_InInfraObject_With_Numbers_Only(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.Name = "123456788900";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }


    //Description

    //[Theory]
    //[AutoInfraObjectData]
    //public async Task Verify_Create_Description_InInfraObject_IsNull(CreateInfraObjectCommand createInfraObjectCommand)
    //{
    //    var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

    //    createInfraObjectCommand.Description = null;
    //    createInfraObjectCommand.BusinessServiceId = "1c9187b5-b045-4d6b-ab70-0132a2ee2d31";
    //    createInfraObjectCommand.BusinessFunctionId = "82fda2ff-73c3-4efc-b836-d27ccdff47de";
    //    createInfraObjectCommand.ReplicationTypeId = "3565dd16-c177-4620-8772-a72e8c678583";
    //    createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
    //    createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
    //    createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
    //    createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
    //    createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
    //    createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
    //    createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
    //    createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
    //    createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
    //    createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

    //    var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.InfraObject.InfraObjectDescriptionNotNullRequired, validateResult.Errors[1].ErrorMessage);
    //}

    //[Theory]
    //[AutoInfraObjectData]
    //public async Task Verify_Create_Description_InInfraObject_MaximumRange(CreateInfraObjectCommand createInfraObjectCommand)
    //{
    //    var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

    //    createInfraObjectCommand.Description = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    //    createInfraObjectCommand.BusinessServiceId = "1c9187b5-b045-4d6b-ab70-0132a2ee2d31";
    //    createInfraObjectCommand.BusinessFunctionId = "82fda2ff-73c3-4efc-b836-d27ccdff47de";
    //    createInfraObjectCommand.ReplicationTypeId = "3565dd16-c177-4620-8772-a72e8c678583";
    //    createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
    //    createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
    //    createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
    //    createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
    //    createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
    //    createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
    //    createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
    //    createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
    //    createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
    //    createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

    //    var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.InfraObject.InfraObjectDescriptionRangeRequired, validateResult.Errors[1].ErrorMessage);
    //}

    //BusinessFunctionName

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_BusinessFunctionName_InInfraObject_WithEmpty(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.BusinessFunctionName = "";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectDescriptionContainsInvalid, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_BusinessFunctionName_InInfraObject_IsNull(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.BusinessFunctionName = null;
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectOperationalFunctionNameValid, validateResult.Errors[2].ErrorMessage);
    }

    //BusinessServiceName

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_BusinessServiceName_InInfraObject_WithEmpty(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.BusinessServiceName = "";
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectOperationalFunctionRequired, validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Create_BusinessServiceName_InInfraObject_IsNull(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var validator = new CreateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        createInfraObjectCommand.BusinessServiceName = null;
        createInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        createInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        createInfraObjectCommand.PRServerId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";
        createInfraObjectCommand.DRServerId = "5754e28f-f676-4394-9247-fa40f81ed2d6";
        createInfraObjectCommand.NearDRServerId = "8f81a9ca-7b47-4b47-84ea-4c36a7796de0";
        createInfraObjectCommand.PRDatabaseId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRDatabaseId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.PRReplicationId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        createInfraObjectCommand.DRReplicationId = "614c7c04-0ab3-417b-bdf3-92045ec4882d";
        createInfraObjectCommand.NearDRReplicationId = "7c89d072-24fa-4550-8493-08b088b310ba";
        createInfraObjectCommand.PairInfraObjectId = "c92adea8-a0dd-47c7-a010-97e5cb68c1ef";

        var validateResult = await validator.ValidateAsync(createInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectOperationalServiceRequired, validateResult.Errors[3].ErrorMessage);
    }
}