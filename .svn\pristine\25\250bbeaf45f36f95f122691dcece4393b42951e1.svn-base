﻿namespace ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetByRequestId;

public class GetApprovalMatrixByRequestIdQueryHandler : IRequestHandler<GetApprovalMatrixByRequestIdQuery, List<ApprovalMatrixByRequestIdVm>>
{

    private readonly IApprovalMatrixApprovalRepository _approvalMatrixApprovalRepository;
    private readonly IApprovalMatrixRequestRepository _approvalMatrixRequestRepository;
    private readonly IMapper _mapper;

    public GetApprovalMatrixByRequestIdQueryHandler(IApprovalMatrixApprovalRepository approvalMatrixApprovalRepository, IApprovalMatrixRequestRepository approvalMatrixRequestRepository, IMapper mapper)
    {
        _approvalMatrixApprovalRepository = approvalMatrixApprovalRepository;
        _approvalMatrixRequestRepository = approvalMatrixRequestRepository;
        _mapper = mapper;
    }

    public async Task<List<ApprovalMatrixByRequestIdVm>> Handle(GetApprovalMatrixByRequestIdQuery request, CancellationToken cancellationToken)
    {
        var approvalMatrixRequestDto = await _approvalMatrixRequestRepository.GetByRequestId(request.RequestId);

        Guard.Against.NullOrDeactive(approvalMatrixRequestDto, nameof(Domain.Entities.ApprovalMatrixRequest),
            new NotFoundException(nameof(Domain.Entities.ApprovalMatrixRequest), request.RequestId));

        var approvalMatrixApproval =
            await _approvalMatrixApprovalRepository.GetApprovalMatrixApprovalByRequestId(approvalMatrixRequestDto
                .RequestId);

        var approverNames = string.Join(", ", approvalMatrixApproval
            .Select(a => a.ApproverName)
            .Where(name => !string.IsNullOrWhiteSpace(name)));

        var firstString = $"Approval request created on {approvalMatrixRequestDto.CreatedDate.ToString("MMMM dd, yyyy")}.";
        var secondString = $"Approval forwarded to : {approverNames}";


        var approvalMatrixByRequestIdVms = new List<ApprovalMatrixByRequestIdVm>
        {
            new() { RequestId = approvalMatrixRequestDto.RequestId,Message = secondString,LastModifiedDate = approvalMatrixRequestDto.LastModifiedDate},
            new() { RequestId = approvalMatrixRequestDto.RequestId,Message = firstString, LastModifiedDate = approvalMatrixRequestDto.LastModifiedDate}
        };

        var approvalMatrixMap = _mapper.Map<List<ApprovalMatrixByRequestIdVm>>(approvalMatrixApproval);

        // approvalMatrixMap.InsertRange(0, approvalMatrixByRequestIdVms);

        approvalMatrixMap.AddRange(approvalMatrixByRequestIdVms);

        return approvalMatrixMap;
    }
}