﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class SingleSignOnRepositoryMocks
{
    public static Mock<ISingleSignOnRepository> CreateSingleSignOnRepository(List<SingleSignOn> singleSignOns)
    {
        var singleSignOnRepository = new Mock<ISingleSignOnRepository>();

        singleSignOnRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(singleSignOns);

        singleSignOnRepository.Setup(repo => repo.AddAsync(It.IsAny<SingleSignOn>())).ReturnsAsync((SingleSignOn singleSignOn) =>
        {
            singleSignOn.Id = new Fixture().Create<int>();

            singleSignOn.ReferenceId = new Fixture().Create<Guid>().ToString();

            singleSignOns.Add(singleSignOn);

            return singleSignOn;
        });

        return singleSignOnRepository;
    }

    public static Mock<ISingleSignOnRepository> UpdateSingleSignOnRepository(List<SingleSignOn> singleSignOns)
    {
        var singleSignOnRepository = new Mock<ISingleSignOnRepository>();

        singleSignOnRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(singleSignOns);

        singleSignOnRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => singleSignOns.SingleOrDefault(x => x.ReferenceId == i));

        singleSignOnRepository.Setup(repo => repo.UpdateAsync(It.IsAny<SingleSignOn>())).ReturnsAsync((SingleSignOn singleSignOn) =>
        {
            var index = singleSignOns.FindIndex(item => item.ReferenceId == singleSignOn.ReferenceId);

            singleSignOns[index] = singleSignOn;

            return singleSignOn;
        });

        return singleSignOnRepository;
    }

    public static Mock<ISingleSignOnRepository> DeleteSingleSignOnRepository(List<SingleSignOn> singleSignOns)
    {
        var singleSignOnRepository = new Mock<ISingleSignOnRepository>();

        singleSignOnRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(singleSignOns);

        singleSignOnRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => singleSignOns.SingleOrDefault(x => x.ReferenceId == i));

        //singleSignOnRepository.Setup(repo => repo.GetSingleSignOnByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(singleSignOns);

        singleSignOnRepository.Setup(repo => repo.UpdateAsync(It.IsAny<SingleSignOn>())).ReturnsAsync((SingleSignOn singleSignOn) =>
        {
            var index = singleSignOns.FindIndex(item => item.ReferenceId == singleSignOn.ReferenceId);

            singleSignOn.IsActive = false;

            singleSignOns[index] = singleSignOn;

            return singleSignOn;
        });

        return singleSignOnRepository;
    }

    public static Mock<ISingleSignOnRepository> GetSingleSignOnRepository(List<SingleSignOn> singleSignOns)
    {
        var singleSignOnRepository = new Mock<ISingleSignOnRepository>();

        singleSignOnRepository.Setup(repo => repo.GetType(It.IsAny<string>())).ReturnsAsync(singleSignOns);

        singleSignOnRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(singleSignOns);

        singleSignOnRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => singleSignOns.SingleOrDefault(x => x.ReferenceId == i));

        return singleSignOnRepository;
    }

    public static Mock<ISingleSignOnRepository> GetSingleSignOnNamesRepository(List<SingleSignOn> singleSignOn)
    {
        var singleSignOnRepository = new Mock<ISingleSignOnRepository>();

        singleSignOnRepository.Setup(repo => repo.GetSingleSignOnNames()).ReturnsAsync(singleSignOn);

        return singleSignOnRepository;
    }

    public static Mock<ISingleSignOnRepository> GetSingleSignOnNameUniqueRepository(List<SingleSignOn> singleSignOns)
    {
        var singleSignOnRepository = new Mock<ISingleSignOnRepository>();

        singleSignOnRepository.Setup(repo => repo.IsProfileNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => singleSignOns.Exists(x => x.ProfileName == i && x.ReferenceId == j));

        return singleSignOnRepository;
    }

    public static Mock<ISingleSignOnRepository> GetSingleSignOnEmptyRepository()
    {
        var singleSignOnRepository = new Mock<ISingleSignOnRepository>();

        singleSignOnRepository.Setup(repo => repo.GetType(It.IsAny<string>())).ReturnsAsync(new List<SingleSignOn>());

        singleSignOnRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<SingleSignOn>());

        //singleSignOnRepository.Setup(repo => repo.GetSingleSignOnByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(new List<SingleSignOn>());

        return singleSignOnRepository;
    }

    public static Mock<ISingleSignOnRepository> GetPaginatedSingleSignOnRepository(List<SingleSignOn> singleSignOns)
    {
        var singleSignOnRepository = new Mock<ISingleSignOnRepository>();

        var queryableSingleSignOn = singleSignOns.BuildMock();

        singleSignOnRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableSingleSignOn);

        return singleSignOnRepository;
    }

    public static Mock<ISingleSignOnRepository> GetSingleSignOnTypeRepository(List<SingleSignOn> singleSignOns)
    {
        var singleSignOnRepository = new Mock<ISingleSignOnRepository>();

        var queryableSingleSignOn = singleSignOns.BuildMock();

        singleSignOnRepository.Setup(repo => repo.GetSingleSignOnByType(It.IsAny<string>())).Returns(queryableSingleSignOn);

        return singleSignOnRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateSingleSignOnEventRepository(List<UserActivity> userActivities)
    {
        var singleSignOnEventRepository = new Mock<IUserActivityRepository>();

        singleSignOnEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        singleSignOnEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync((UserActivity userActivity) =>
        {
            userActivity.Id = new Fixture().Create<int>();

            userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

            userActivities.Add(userActivity);

            return userActivity;
        });

        return singleSignOnEventRepository;
    }
}