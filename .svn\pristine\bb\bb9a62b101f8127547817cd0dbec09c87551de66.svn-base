using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DataLag.Commands.Create;
using ContinuityPatrol.Application.Features.DataLag.Commands.Delete;
using ContinuityPatrol.Application.Features.DataLag.Commands.Update;
using ContinuityPatrol.Application.Features.DataLag.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.DataLag.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataLag.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.DataLagModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DataLagControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DataLagsController _controller;
    private readonly DataLagFixture _dataLagFixture;

    public DataLagControllerTests()
    {
        _dataLagFixture = new DataLagFixture();

        var testBuilder = new ControllerTestBuilder<DataLagsController>();
        _controller = testBuilder.CreateController(
            _ => new DataLagsController(),
            out _mediatorMock);
    }

    #region Core CRUD Operations

    [Fact]
    public async Task CreateDataLag_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _dataLagFixture.CreateDataLagCommand;
        var expectedResponse = _dataLagFixture.CreateDataLagResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataLag(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataLagResponse>(createdResult.Value);
        Assert.Equal("Enterprise Data Lag Service created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDataLag_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _dataLagFixture.UpdateDataLagCommand;
        var expectedResponse = _dataLagFixture.UpdateDataLagResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDataLag(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDataLagResponse>(okResult.Value);
        Assert.Equal("Enterprise Data Lag Service updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task DeleteDataLag_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var dataLagId = Guid.NewGuid().ToString();
        var expectedResponse = _dataLagFixture.DeleteDataLagResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDataLagCommand>(c => c.Id == dataLagId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDataLag(dataLagId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDataLagResponse>(okResult.Value);
        Assert.Equal("Enterprise Data Lag Service deleted successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task GetDataLags_ReturnsOkResult()
    {
        // Arrange
        var dataLagList = new List<DataLagListVm> { _dataLagFixture.DataLagListVm };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataLagListQuery>(), default))
            .ReturnsAsync(dataLagList);

        // Act
        var result = await _controller.GetDataLags();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataLagListVm>>(okResult.Value);
        Assert.Single(returnedList);
        Assert.Equal("Enterprise Data Lag List Service", returnedList.First().BusinessServiceName);
        Assert.Equal(35, returnedList.First().TotalBusinessFunction);
        Assert.Equal(70, returnedList.First().TotalInfraObject);
    }

    [Fact]
    public async Task GetDataLagById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var dataLagId = Guid.NewGuid().ToString();
        var dataLagDetail = _dataLagFixture.DataLagDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataLagDetailQuery>(q => q.Id == dataLagId), default))
            .ReturnsAsync(dataLagDetail);

        // Act
        var result = await _controller.GetDataLagById(dataLagId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DataLagDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Data Lag Detail Service", returnedDetail.BusinessServiceName);
        Assert.Equal(40, returnedDetail.TotalBusinessFunction);
        Assert.Equal(80, returnedDetail.TotalInfraObject);
        Assert.Equal(36, returnedDetail.BFAvailable);
        Assert.Equal(74, returnedDetail.InfraAvailable);
    }

    [Fact]
    public async Task GetDataLagByBusinessServiceId_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var dataLagDetail = _dataLagFixture.DataLagListVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataLagDetailByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(dataLagDetail);

        // Act
        var result = await _controller.GetDataLagByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DataLagListVm>(okResult.Value);
        Assert.Equal("Enterprise Data Lag List Service", returnedDetail.BusinessServiceName);
        Assert.Equal(35, returnedDetail.TotalBusinessFunction);
        Assert.Equal(70, returnedDetail.TotalInfraObject);
    }

    #endregion

    #region Error Handling

    [Fact]
    public async Task GetDataLagById_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDataLagById(invalidId));
    }

    [Fact]
    public async Task GetDataLagByBusinessServiceId_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDataLagByBusinessServiceId(invalidId));
    }

    [Fact]
    public async Task DeleteDataLag_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDataLag(invalidId));
    }

    [Fact]
    public async Task CreateDataLag_WhenMediatorThrowsException_PropagatesException()
    {
        // Arrange
        var command = _dataLagFixture.CreateDataLagCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.CreateDataLag(command));
        Assert.Contains("Database connection failed", exception.Message);
    }

    [Fact]
    public async Task UpdateDataLag_WhenMediatorThrowsNotFoundException_PropagatesException()
    {
        // Arrange
        var command = _dataLagFixture.UpdateDataLagCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("DataLag", command.Id));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<NotFoundException>(() => _controller.UpdateDataLag(command));
        Assert.Contains("DataLag", exception.Message);
        Assert.Contains(command.Id, exception.Message);
    }

    #endregion

    #region ClearDataCache

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act
        _controller.ClearDataCache();

        // Assert - This test verifies the method executes without throwing exceptions
        // The actual cache clearing logic is tested in integration tests
        Assert.True(true);
    }

    #endregion

  

    [Fact]
    public async Task GetDataLags_HandlesEmptyList()
    {
        // Arrange
        var emptyList = new List<DataLagListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataLagListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDataLags();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataLagListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task CreateDataLag_HandlesEnterpriseScaleData()
    {
        // Arrange
        var enterpriseCommand = new CreateDataLagCommand
        {
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Enterprise Global Data Lag Service",
            TotalBusinessFunction = 500,
            BFAvailable = 475,
            BFImpact = 15,
            BFExceed = 10,
            BFUnConfigured = 0,
            BFNotAvailable = 0,
            BFThreshold = 5,
            TotalInfraObject = 2000,
            InfraAvailable = 1900,
            InfraImpact = 60,
            InfraExceed = 40,
            InfraNotAvailable = 0,
            InfraThreshold = 20,
            InfraUnderConfigured = 0
        };

        var expectedResponse = new CreateDataLagResponse
        {
            Id = Guid.NewGuid().ToString(),
            Message = "Enterprise Global Data Lag Service created successfully!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(enterpriseCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataLag(enterpriseCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataLagResponse>(createdResult.Value);

        Assert.Equal("Enterprise Global Data Lag Service created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);

        // Validate enterprise scale metrics
        Assert.Equal(500, enterpriseCommand.TotalBusinessFunction);
        Assert.Equal(2000, enterpriseCommand.TotalInfraObject);
        Assert.Equal(95.0, (double)enterpriseCommand.BFAvailable / enterpriseCommand.TotalBusinessFunction * 100, 1); // 95% BF availability
        Assert.Equal(95.0, (double)enterpriseCommand.InfraAvailable / enterpriseCommand.TotalInfraObject * 100, 1); // 95% Infra availability
    }

    [Fact]
    public async Task UpdateDataLag_HandlesHighImpactScenario()
    {
        // Arrange
        var highImpactCommand = new UpdateDataLagCommand
        {
            Id = Guid.NewGuid().ToString(),
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Critical High Impact Data Lag Service",
            TotalBusinessFunction = 100,
            BFAvailable = 70,
            BFImpact = 20,
            BFExceed = 10,
            BFUnConfigured = 0,
            BFNotAvailable = 0,
            BFThreshold = 5,
            TotalInfraObject = 300,
            InfraAvailable = 200,
            InfraImpact = 70,
            InfraExceed = 30,
            InfraNotAvailable = 0,
            InfraThreshold = 15,
            InfraUnderConfigured = 0
        };

        var expectedResponse = new UpdateDataLagResponse
        {
            Id = highImpactCommand.Id,
            Message = "Critical High Impact Data Lag Service updated successfully!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(highImpactCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDataLag(highImpactCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDataLagResponse>(okResult.Value);

        Assert.Equal("Critical High Impact Data Lag Service updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);

        // Validate high impact metrics
        Assert.Equal(70.0, (double)highImpactCommand.BFAvailable / highImpactCommand.TotalBusinessFunction * 100, 1); // 70% BF availability
        Assert.Equal(66.67, (double)highImpactCommand.InfraAvailable / highImpactCommand.TotalInfraObject * 100, 2); // 66.67% Infra availability
        Assert.Equal(20.0, (double)highImpactCommand.BFImpact / highImpactCommand.TotalBusinessFunction * 100, 1); // 20% BF impact
        Assert.Equal(23.33, (double)highImpactCommand.InfraImpact / highImpactCommand.TotalInfraObject * 100, 2); // 23.33% Infra impact
    }

    [Fact]
    public async Task GetDataLagByBusinessServiceId_HandlesMultiSiteConfiguration()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var multiSiteDataLag = new DataLagListVm
        {
            Id = Guid.NewGuid().ToString(),
            BusinessServiceId = businessServiceId,
            BusinessServiceName = "Multi-Site Enterprise Data Lag Service",
            TotalBusinessFunction = 200,
            BFAvailable = 180,
            BFImpact = 12,
            BFExceed = 8,
            BFUnConfigured = 0,
            BFNotAvailable = 0,
            BFThreshold = 4,
            TotalInfraObject = 800,
            InfraAvailable = 720,
            InfraImpact = 50,
            InfraExceed = 30,
            InfraNotAvailable = 0,
            InfraThreshold = 10,
            InfraUnderConfigured = 0
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataLagDetailByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(multiSiteDataLag);

        // Act
        var result = await _controller.GetDataLagByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DataLagListVm>(okResult.Value);

        Assert.Equal("Multi-Site Enterprise Data Lag Service", returnedDetail.BusinessServiceName);
        Assert.Equal(businessServiceId, returnedDetail.BusinessServiceId);

        // Validate multi-site metrics
        Assert.Equal(90.0, (double)returnedDetail.BFAvailable / returnedDetail.TotalBusinessFunction * 100, 1); // 90% BF availability
        Assert.Equal(90.0, (double)returnedDetail.InfraAvailable / returnedDetail.TotalInfraObject * 100, 1); // 90% Infra availability

        // Validate impact distribution
        Assert.Equal(returnedDetail.BFImpact + returnedDetail.BFExceed, 20); // Total BF issues
        Assert.Equal(returnedDetail.InfraImpact + returnedDetail.InfraExceed, 80); // Total Infra issues

        // Validate threshold compliance
        Assert.True(returnedDetail.BFThreshold < returnedDetail.BFImpact); // BF impact exceeds threshold
        Assert.True(returnedDetail.InfraThreshold < returnedDetail.InfraImpact); // Infra impact exceeds threshold
    }

    [Fact]
    public async Task GetDataLags_HandlesLargeDatasetWithMultipleServices()
    {
        // Arrange
        var largeDataLagList = new List<DataLagListVm>
        {
            new DataLagListVm
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Primary Production Data Lag Service",
                TotalBusinessFunction = 150,
                BFAvailable = 140,
                BFImpact = 7,
                BFExceed = 3,
                TotalInfraObject = 500,
                InfraAvailable = 470,
                InfraImpact = 20,
                InfraExceed = 10
            },
            new DataLagListVm
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Secondary DR Data Lag Service",
                TotalBusinessFunction = 100,
                BFAvailable = 95,
                BFImpact = 3,
                BFExceed = 2,
                TotalInfraObject = 300,
                InfraAvailable = 285,
                InfraImpact = 10,
                InfraExceed = 5
            },
            new DataLagListVm
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Development Data Lag Service",
                TotalBusinessFunction = 50,
                BFAvailable = 48,
                BFImpact = 1,
                BFExceed = 1,
                TotalInfraObject = 150,
                InfraAvailable = 145,
                InfraImpact = 3,
                InfraExceed = 2
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataLagListQuery>(), default))
            .ReturnsAsync(largeDataLagList);

        // Act
        var result = await _controller.GetDataLags();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataLagListVm>>(okResult.Value);

        Assert.Equal(3, returnedList.Count);

        // Validate service distribution
        Assert.Contains(returnedList, s => s.BusinessServiceName.Contains("Primary Production"));
        Assert.Contains(returnedList, s => s.BusinessServiceName.Contains("Secondary DR"));
        Assert.Contains(returnedList, s => s.BusinessServiceName.Contains("Development"));

        // Validate aggregate metrics
        var totalBF = returnedList.Sum(s => s.TotalBusinessFunction);
        var totalInfra = returnedList.Sum(s => s.TotalInfraObject);
        var totalBFAvailable = returnedList.Sum(s => s.BFAvailable);
        var totalInfraAvailable = returnedList.Sum(s => s.InfraAvailable);

        Assert.Equal(300, totalBF);
        Assert.Equal(950, totalInfra);
        Assert.Equal(94.33, (double)totalBFAvailable / totalBF * 100, 2); // 94.33% overall BF availability
        Assert.Equal(94.74, (double)totalInfraAvailable / totalInfra * 100, 2); // 94.74% overall Infra availability
    }

   

   

    [Fact]
    public async Task CreateDataLag_HandlesComplexBusinessServiceConfiguration()
    {
        // Arrange
        var complexCommand = new CreateDataLagCommand
        {
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Complex Multi-Tier Business Service",
            TotalBusinessFunction = 150,
            BFAvailable = 135,
            BFImpact = 10,
            BFExceed = 3,
            BFUnConfigured = 2,
            BFNotAvailable = 0,
            BFThreshold = 8,
            TotalInfraObject = 500,
            InfraAvailable = 450,
            InfraImpact = 30,
            InfraExceed = 15,
            InfraNotAvailable = 5,
            InfraThreshold = 25,
            InfraUnderConfigured = 0
        };

        var expectedResponse = _dataLagFixture.CreateDataLagResponse;

        _mediatorMock
            .Setup(m => m.Send(complexCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataLag(complexCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataLagResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal(90.0, (double)complexCommand.BFAvailable / complexCommand.TotalBusinessFunction * 100, 1);
        Assert.Equal(90.0, (double)complexCommand.InfraAvailable / complexCommand.TotalInfraObject * 100, 1);
    }

    [Fact]
    public async Task UpdateDataLag_HandlesRealTimeDataLagScenario()
    {
        // Arrange
        var realTimeCommand = _dataLagFixture.UpdateDataLagCommand;
        realTimeCommand.BusinessServiceName = "Real-Time Data Processing Service";
        realTimeCommand.TotalBusinessFunction = 80;
        realTimeCommand.BFAvailable = 75;
        realTimeCommand.BFImpact = 3;
        realTimeCommand.BFExceed = 2;
        realTimeCommand.TotalInfraObject = 200;
        realTimeCommand.InfraAvailable = 185;
        realTimeCommand.InfraImpact = 10;
        realTimeCommand.InfraExceed = 5;

        var expectedResponse = _dataLagFixture.UpdateDataLagResponse;

        _mediatorMock
            .Setup(m => m.Send(realTimeCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDataLag(realTimeCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDataLagResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal(93.75, (double)realTimeCommand.BFAvailable / realTimeCommand.TotalBusinessFunction * 100, 2);
        Assert.Equal(92.5, (double)realTimeCommand.InfraAvailable / realTimeCommand.TotalInfraObject * 100, 1);
    }

    [Fact]
    public async Task GetDataLagByBusinessServiceId_HandlesMultiRegionDeployment()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var multiRegionDataLag = new DataLagListVm
        {
            Id = Guid.NewGuid().ToString(),
            BusinessServiceId = businessServiceId,
            BusinessServiceName = "Multi-Region Global Service",
            TotalBusinessFunction = 200,
            BFAvailable = 180,
            BFImpact = 15,
            BFExceed = 5,
            TotalInfraObject = 800,
            InfraAvailable = 720,
            InfraImpact = 60,
            InfraExceed = 20
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataLagDetailByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(multiRegionDataLag);

        // Act
        var result = await _controller.GetDataLagByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDataLag = Assert.IsType<DataLagListVm>(okResult.Value);

        Assert.Equal("Multi-Region Global Service", returnedDataLag.BusinessServiceName);
        Assert.Equal(90.0, (double)returnedDataLag.BFAvailable / returnedDataLag.TotalBusinessFunction * 100, 1);
        Assert.Equal(90.0, (double)returnedDataLag.InfraAvailable / returnedDataLag.TotalInfraObject * 100, 1);
    }

    [Fact]
    public async Task DeleteDataLag_HandlesLegacySystemCleanup()
    {
        // Arrange
        var legacyDataLagId = Guid.NewGuid().ToString();
        var expectedResponse = _dataLagFixture.DeleteDataLagResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDataLagCommand>(c => c.Id == legacyDataLagId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDataLag(legacyDataLagId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDataLagResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Contains("successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task GetDataLagList_HandlesEmptyResultSet()
    {
        // Arrange
        var emptyDataLagList = new List<DataLagListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataLagListQuery>(), default))
            .ReturnsAsync(emptyDataLagList);

        // Act
        var result = await _controller.GetDataLags();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DataLagListVm>>(okResult.Value);

        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDataLagDetail_HandlesDetailedMetricsAnalysis()
    {
        // Arrange
        var dataLagId = Guid.NewGuid().ToString();
        var detailedDataLag = _dataLagFixture.DataLagDetailVm;
        detailedDataLag.BusinessServiceName = "Detailed Analytics Service";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataLagDetailQuery>(q => q.Id == dataLagId), default))
            .ReturnsAsync(detailedDataLag);

        // Act
        var result = await _controller.GetDataLagById(dataLagId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DataLagDetailVm>(okResult.Value);

        Assert.Equal("Detailed Analytics Service", returnedDetail.BusinessServiceName);
        Assert.NotNull(returnedDetail.Id);
    }
    
}
