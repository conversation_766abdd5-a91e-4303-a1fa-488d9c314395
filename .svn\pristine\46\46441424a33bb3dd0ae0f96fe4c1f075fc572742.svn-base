﻿using ContinuityPatrol.Application.Features.DataSetColumns.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.DataSetColumns.Commands;

public class CreateDataSetColumnsTests : IClassFixture<DataSetColumnsFixture>
{
    private readonly DataSetColumnsFixture _dataSetColumnsFixture;

    private readonly Mock<IDataSetColumnsRepository> _mockDataSetColumnsRepository;

    private readonly CreateDataSetColumnsCommandHandler _handler;

    public CreateDataSetColumnsTests(DataSetColumnsFixture dataSetColumnsFixture)
    {
        _dataSetColumnsFixture = dataSetColumnsFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockDataSetColumnsRepository = DataSetColumnsRepositoryMocks.CreateDataSetColumnsRepository(_dataSetColumnsFixture.DataSetColumns);

        _handler = new CreateDataSetColumnsCommandHandler(_dataSetColumnsFixture.Mapper, _mockDataSetColumnsRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Should_Increase_Count_When_AddValid_DataSetColumns()
    {
        await _handler.Handle(_dataSetColumnsFixture.CreateDataSetColumnsCommand, CancellationToken.None);

        var allCategories = await _mockDataSetColumnsRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_dataSetColumnsFixture.DataSetColumns.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulDataSetColumnsResponse_When_AddValidDataSetColumns()
    {
        var result = await _handler.Handle(_dataSetColumnsFixture.CreateDataSetColumnsCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateDataSetColumnsResponse));

        result.DataSetColumnsId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_dataSetColumnsFixture.CreateDataSetColumnsCommand, CancellationToken.None);

        _mockDataSetColumnsRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.DataSetColumns>()), Times.Once);
    }
}