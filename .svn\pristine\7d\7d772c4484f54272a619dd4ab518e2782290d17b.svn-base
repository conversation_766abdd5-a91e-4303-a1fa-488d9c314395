﻿using AutoFixture;
using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Create;
using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Update;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.SingleSignOnModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class SingleSignOnControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<ILogger<SingleSignOnController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private  SingleSignOnController _controller;

        public SingleSignOnControllerShould()
        {
            
            _controller = new SingleSignOnController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        
        [Fact]
        public async Task List_ReturnsViewResult()
        {
            // Arrange
            // No setup needed as List() just publishes event and returns View()

            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Null(viewResult.Model); // List method returns View() without model
            _mockPublisher.Verify(p => p.Publish(It.IsAny<INotification>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_CreatesSingleSignOn_WhenIdIsEmpty()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<SingleSignOnViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateSingleSignOnCommand();
            _mockMapper.Setup(m => m.Map<CreateSingleSignOnCommand>(viewModel)).Returns(command);
            _mockDataProvider.Setup(dp => dp.SingleSignOn.CreateAsync(command)).ReturnsAsync(new BaseResponse { Message = "Created" });

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            _mockMapper.Verify(m => m.Map<CreateSingleSignOnCommand>(viewModel), Times.Once);
            _mockDataProvider.Verify(dp => dp.SingleSignOn.CreateAsync(command), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesSingleSignOn_WhenIdIsNotEmpty()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<SingleSignOnViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateSingleSignOnCommand();
            _mockMapper.Setup(m => m.Map<UpdateSingleSignOnCommand>(viewModel)).Returns(command);
            _mockDataProvider.Setup(dp => dp.SingleSignOn.UpdateAsync(command)).ReturnsAsync(new BaseResponse { Message = "Updated" });

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            _mockMapper.Verify(m => m.Map<UpdateSingleSignOnCommand>(viewModel), Times.Once);
            _mockDataProvider.Verify(dp => dp.SingleSignOn.UpdateAsync(command), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesValidationException()
        {
            // Arrange
            var viewModel = new SingleSignOnViewModel();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var validationResult = new FluentValidation.Results.ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("ProfileName", "Profile name is required"));
            var validationException = new ValidationException(validationResult);
            _mockMapper.Setup(m => m.Map<CreateSingleSignOnCommand>(viewModel)).Returns(new CreateSingleSignOnCommand());
            _mockDataProvider.Setup(dp => dp.SingleSignOn.CreateAsync(It.IsAny<CreateSingleSignOnCommand>())).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesGeneralException()
        {
            // Arrange
            var viewModel = new SingleSignOnViewModel();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var exception = new Exception("General error");
            _mockMapper.Setup(m => m.Map<CreateSingleSignOnCommand>(viewModel)).Returns(new CreateSingleSignOnCommand());
            _mockDataProvider.Setup(dp => dp.SingleSignOn.CreateAsync(It.IsAny<CreateSingleSignOnCommand>())).ThrowsAsync(exception);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        

        [Fact]
        public async Task Delete_ReturnsJsonResult_WhenSuccessful()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Message = "Deleted" };
            _mockDataProvider.Setup(dp => dp.SingleSignOn.DeleteAsync(id)).ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            _mockDataProvider.Verify(dp => dp.SingleSignOn.DeleteAsync(id), Times.Once);
        }

        [Fact]
        public async Task Delete_HandlesGeneralException()
        {
            // Arrange
            var id = "1";
            var exception = new Exception("General error");
            _mockDataProvider.Setup(dp => dp.SingleSignOn.DeleteAsync(id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResult()
        {
            // Arrange
            var query = new GetSingleSignOnPaginatedListQuery();
            var resultList = new PaginatedResult<SingleSignOnListVm>();
            _mockDataProvider.Setup(dp => dp.SingleSignOn.GetPaginatedSingleSignOns(query)).ReturnsAsync(resultList);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetPagination_HandlesException()
        {
            // Arrange
            var query = new GetSingleSignOnPaginatedListQuery();
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.SingleSignOn.GetPaginatedSingleSignOns(query)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetSingleSignOnList_ReturnsJsonResult()
        {
            // Arrange
            var singleSignOnList = new List<SingleSignOnListVm>();
            _mockDataProvider.Setup(dp => dp.SingleSignOn.GetSingleSignOnList()).ReturnsAsync(singleSignOnList);

            // Act
            var result = await _controller.GetSingleSignOnList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetSingleSignOnList_HandlesException()
        {
            // Arrange
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.SingleSignOn.GetSingleSignOnList()).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetSingleSignOnList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetSingleSignOnByType_ReturnsJsonResult()
        {
            // Arrange
            var type = "someType";
            var singleSignOnList = new List<SingleSignOnTypeVm>();
            _mockDataProvider.Setup(dp => dp.SingleSignOn.GetSingleSignOnByType(type)).ReturnsAsync(singleSignOnList);

            // Act
            var result = await _controller.GetSingleSignOnByType(type);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetSingleSignOnByType_HandlesException()
        {
            // Arrange
            var type = "someType";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.SingleSignOn.GetSingleSignOnByType(type)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetSingleSignOnByType(type);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task IsSingleSignOnNameExist_ReturnsTrue()
        {
            // Arrange
            var name = "TestSSO";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.SingleSignOn.IsSingleSignOnProfileNameExist(name, id)).ReturnsAsync(true);

            // Act
            var result = await _controller.IsSingleSignOnNameExist(name, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task IsSingleSignOnNameExist_ReturnsFalse()
        {
            // Arrange
            var name = "TestSSO";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.SingleSignOn.IsSingleSignOnProfileNameExist(name, id)).ReturnsAsync(false);

            // Act
            var result = await _controller.IsSingleSignOnNameExist(name, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsSingleSignOnNameExist_HandlesException_ReturnsFalse()
        {
            // Arrange
            var name = "TestSSO";
            var id = "1";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.SingleSignOn.IsSingleSignOnProfileNameExist(name, id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.IsSingleSignOnNameExist(name, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetSingleSignOnById_ReturnsJsonResult()
        {
            // Arrange
            var id = "1";
            var singleSignOn = new SingleSignOnDetailVm();
            _mockDataProvider.Setup(dp => dp.SingleSignOn.GetSingleSignOnById(id)).ReturnsAsync(singleSignOn);

            // Act
            var result = await _controller.GetSingleSignOnById(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetSingleSignOnById_HandlesException()
        {
            // Arrange
            var id = "1";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.SingleSignOn.GetSingleSignOnById(id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetSingleSignOnById(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }
    }
}
