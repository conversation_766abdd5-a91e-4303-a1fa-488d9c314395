{"version": 3, "file": "aspnet-validation.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAA0B,iBAAID,IAE9BD,EAAuB,iBAAIC,GAC5B,CATD,CASGK,MAAM,I,mBCRT,IAAIC,EAAsB,CCA1BA,EAAwB,CAACL,EAASM,KACjC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDF,EAAwB,CAACQ,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFT,EAAyBL,IACH,oBAAXkB,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeV,EAASkB,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeV,EAAS,aAAc,CAAEoB,OAAO,GAAO,G,o8CCkBxDC,EAAa,IAAI,WAAC,aAEpB,KAAAC,KAAOC,WAAWC,QAAQF,IAC9B,QAFI,YAAAG,IAAA,SAAIC,G,IAAW,uDAAyB,EAE5C,EAHuB,IAeVC,EAAgB,SAACC,GAC1B,OAAAA,aAAmBC,kBAChBD,aAAmBE,mBACnBF,aAAmBG,mBAFtB,EAIEC,EAA0B,CAAC,QAAS,SAAU,YAO9CC,EAAsB,SAACC,GACzB,OAAAF,EAAwBG,KAAI,SAAAC,GAAK,gBAAGA,GAAC,OAAGF,GAAY,GAAnB,IAAyBG,KAAK,IAA/D,EAgDJ,SAASC,EAAuBV,EAA6BM,GAKzD,IAAIK,EAAcX,EAAQY,KACtBC,EAAeP,EAASQ,UAAU,GAGlCC,EAAcJ,EAAYK,YAAY,KAC1C,GAAID,GAAe,EAAG,CAKlB,IAAME,EAHON,EAAYG,UAAU,EAAGC,GAGG,IAAMF,EACzCK,EAAkBC,SAASC,kBAAkBH,GAAqB,GACxE,GAAIlB,EAAcmB,GACd,OAAOA,CAEf,CAGA,OAAOlB,EAAQqB,KAAKC,cAAcjB,EAAoB,gBAASQ,EAAY,MAC/E,CAKA,iBAII,KAAAU,SAA+B,SAAC/B,EAAOQ,EAASwB,GAE5C,IAAMC,EAAczB,EAAQ0B,KAAKC,cACjC,GAAoB,aAAhBF,GAA8C,UAAhBA,EAAyB,CAEvD,IADA,IACoB,MADUG,MAAMC,KAAK7B,EAAQqB,KAAKS,iBAAiBzB,EAAoB,iBAAUL,EAAQY,KAAI,oBAAYa,EAAW,SACpH,eAAuB,CAAtC,IAAI,EAAO,KACZ,GAAI,aAAmBxB,mBAAwC,IAApB,EAAQ8B,QAC/C,OAAO,CAEf,CAKA,GAAoB,aAAhBN,EAA4B,CAC5B,IAAMO,EAAsBhC,EAAQqB,KAAKC,cAAc,sBAAetB,EAAQY,KAAI,sBAClF,GAAIoB,aAA+B/B,kBAAkD,UAA9B+B,EAAoBxC,MACvE,OAAO,CAEf,CAEA,OAAO,CACX,CAGA,OAAOyC,QAAQzC,aAAK,EAALA,EAAO0C,OAC1B,EAKA,KAAAC,aAAmC,SAAC3C,EAAOQ,EAASwB,GAChD,IAAKhC,EACD,OAAO,EAGX,GAAIgC,EAAOY,IAAK,CACZ,IAAIA,EAAMC,SAASb,EAAOY,KAC1B,GAAI5C,EAAM8C,OAASF,EACf,OAAO,CAEf,CAEA,GAAIZ,EAAOe,IAAK,CACZ,IAAIA,EAAMF,SAASb,EAAOe,KAC1B,GAAI/C,EAAM8C,OAASC,EACf,OAAO,CAEf,CAEA,OAAO,CACX,EAKA,KAAAC,QAA8B,SAAChD,EAAOQ,EAASwB,GAC3C,IAAKA,EAAOiB,MACR,OAAO,EAGX,IAAIC,EAAehC,EAAuBV,EAASwB,EAAOiB,OAC1D,OAAKC,GAIGA,EAAalD,QAAUA,CACnC,EAKA,KAAAmD,MAA4B,SAACnD,EAAOQ,EAASwB,GACzC,IAAKhC,EACD,OAAO,EAGX,IAAIoD,EAAMC,WAAWrD,GACrB,QAAIsD,MAAMF,IAINpB,EAAOY,KAEHQ,EADMC,WAAWrB,EAAOY,MAM5BZ,EAAOe,KAEHK,EADMC,WAAWrB,EAAOe,KAOpC,EAKA,KAAAQ,MAA4B,SAACvD,EAAOQ,EAASwB,GACzC,OAAKhC,IAAUgC,EAAOwB,SAId,IAAIC,OAAOzB,EAAOwB,SACjBE,KAAK1D,EAClB,EAKA,KAAA2D,MAA4B,SAAC3D,EAAOQ,EAASwB,GACzC,OAAKhC,GASG,4gBACC0D,KAAK1D,EAClB,EAKA,KAAA4D,WAAiC,SAAC5D,EAAOQ,EAASwB,GAC9C,IAAKhC,EACD,OAAO,EAQX,GAAI,aAAa0D,KAAK1D,GAClB,OAAO,EAGX,IAGI6D,EAAGC,EAHHC,EAAS,EACTC,EAAS,EACTC,GAAQ,EAMZ,IAHAjE,EAAQA,EAAMkE,QAAQ,MAAO,KAGnBpB,OAAS,IAAM9C,EAAM8C,OAAS,GACpC,OAAO,EAGX,IAAKe,EAAI7D,EAAM8C,OAAS,EAAGe,GAAK,EAAGA,IAC/BC,EAAS9D,EAAMmE,OAAON,GACtBG,EAASnB,SAASiB,EAAQ,IACtBG,IACKD,GAAU,GAAK,IAChBA,GAAU,GAIlBD,GAAUC,EACVC,GAASA,EAGb,OAAQF,EAAS,IAAQ,CAC7B,EAKA,KAAAK,IAA0B,SAACpE,EAAOQ,EAASwB,GACvC,IAAKhC,EACD,OAAO,EAGX,IAAIqE,EAAiBrE,EAAMmC,cAG3B,OAAOkC,EAAeC,QAAQ,YAAc,GACrCD,EAAeC,QAAQ,aAAe,GACtCD,EAAeC,QAAQ,WAAa,CAC/C,EAKA,KAAAC,MAA4B,SAACvE,EAAOQ,EAASwB,GACzC,OAAKhC,IAKsB,kBACF0D,KAAK1D,IAItB,kBACC0D,KAAK1D,EAClB,EAKA,KAAAwE,OAA6B,SAACxE,EAAOQ,EAASwB,GAC1C,IAAKhC,EACD,OAAO,EAOX,IAHA,IAAIyE,EAA4BzC,EAAO0C,iBAA4BC,MAAM,KACrEC,EAA6B,CAAC,EAER,MAAAH,EAAA,eAAgB,CAArC,IAAII,EAAa,KACdC,EAAYD,EAAcE,OAAO,GACjCC,EAAe9D,EAAuBV,EAASqE,GAEpCpC,QAAQuC,GAAgBA,EAAahF,SAKhDgF,aAAwBvE,mBACD,aAAtBuE,EAAa9C,MAA6C,UAAtB8C,EAAa9C,MAClD0C,EAAOE,GAAaE,EAAazC,QAAUyC,EAAahF,MAAQ,GAEhE4E,EAAOE,GAAaE,EAAahF,MAEzC,CAEA,IAAIoE,EAAcpC,EAAY,IAE1BiD,EAA0B,GAC9B,IAAK,IAAIH,KAAaF,EAAQ,CAC1B,IAAIM,EAAeC,mBAAmBL,GAAa,IAAMK,mBAAmBP,EAAOE,IACnFG,EAAcG,KAAKF,EACvB,CACA,IAAIG,EAAUJ,EAAchE,KAAK,KAEjC,OAAO,IAAIqE,SAAQ,SAACC,EAAIC,GACpB,IAAIC,EAAU,IAAIC,eAElB,GAAI1D,EAAOE,MAAsC,SAA9BF,EAAOE,KAAKC,cAA0B,CACrD,IAAIwD,EAAW,IAAIC,SACnB,IAAK,IAAId,KAAaF,EAClBe,EAASE,OAAOf,EAAWF,EAAOE,IAEtCW,EAAQK,KAAK,OAAQ1B,GACrBqB,EAAQM,iBAAiB,eAAgB,qCACzCN,EAAQO,KAAKX,EACjB,MACII,EAAQK,KAAK,MAAO1B,EAAM,IAAMiB,GAChCI,EAAQO,OAGZP,EAAQQ,OAAS,SAAAC,GACb,GAAIT,EAAQU,QAAU,KAAOV,EAAQU,OAAS,IAAK,CAC/C,IAAIC,EAAOC,KAAKC,MAAMb,EAAQc,cAC9BhB,EAAGa,EACP,MACIZ,EAAO,CACHW,OAAQV,EAAQU,OAChBK,WAAYf,EAAQe,WACpBJ,KAAMX,EAAQc,cAG1B,EAEAd,EAAQgB,QAAU,SAAAP,GACdV,EAAO,CACHW,OAAQV,EAAQU,OAChBK,WAAYf,EAAQe,WACpBJ,KAAMX,EAAQc,cAEtB,CACJ,GACJ,CACJ,EAcA,aAgEI,WAAYG,GAAZ,WA5DQ,KAAAC,UAAoD,CAAC,EAKrD,KAAAC,WAAmE,CAAC,EAKpE,KAAAC,YAA4B,GAK5B,KAAAC,aAA2C,CAAC,EAK5C,KAAAC,WAA8C,CAAC,EAK/C,KAAAC,WAAgD,CAAC,EAKjD,KAAAC,WAA0E,CAAC,EAK3E,KAAAC,YAA+D,CAAC,EAKhE,KAAAC,QAA8B,CAAC,EAUvC,KAAAC,SAAW,IAKX,KAAAC,mBAAoB,EAqPpB,KAAAC,aAAe,SAAOzF,EAAuB0F,GAA4B,qC,4DACrE,KAAM1F,aAAgB2F,iBAClB,MAAM,IAAIC,MAAM,wD,OAEhBC,EAAUC,KAAKC,cAAc/F,GAC7BgG,EAAsBF,KAAKV,WAAWS,IACnC,GAACG,GAAD,MACH,GAAMA,OAAoBC,EAAWP,I,SAArC,S,iBADJ,MAAO,CAAP,K,QAUJ,KAAAQ,cAAgB,SAAOC,EAA2BT,GAA4B,qC,mEACtEU,EAAWN,KAAKC,cAAcI,GAC9BE,EAAuBP,KAAKT,YAAYe,IACrC,GAACC,GAAD,MACH,GAAMA,OAAqBJ,EAAWP,I,SAAtC,S,iBADJ,MAAO,CAAP,K,QASJ,KAAAY,YAAc,SAACC,GACXA,EAAYC,iBACZD,EAAYE,0BAChB,EAUA,KAAAC,gBAAkB,SAAC1G,EAAuB2G,EAAkBJ,GACxD,KAAMvG,aAAgB2F,iBAClB,MAAM,IAAIC,MAAM,2DAEhBe,EACIJ,GACA,EAAKK,gBAAgB5G,EAAMuG,GAI/B,EAAKM,kBAAkB7G,EAE/B,EAWA,KAAA4G,gBAAkB,SAAC5G,EAAuBuG,GACtC,KAAMvG,aAAgB2F,iBAClB,MAAM,IAAIC,MAAM,2DAEpB,IAAMkB,EAAW,IAAIC,YAAY,SAAUR,GAC3C,GAAIvG,EAAKgH,cAAcF,GAAW,CAG9B,IAAMG,EAAYV,EAAYU,UAC1BC,EAA0C,KACxCC,EAAoBnH,EAAKoH,OAC/B,GAAIH,EAAW,CACX,IAAM,EAAOA,EAAUI,aAAa,QAEhC,KACAH,EAAiBpH,SAASwH,cAAc,UACzBjH,KAAO,SACtB6G,EAAe3H,KAAO,EACtB2H,EAAe/I,MAAQ8I,EAAUI,aAAa,SAC9CrH,EAAKuH,YAAYL,IAGrB,IAAMM,EAAaP,EAAUI,aAAa,cACtCG,IACAxH,EAAKoH,OAASI,EAEtB,CAEA,IACIxH,EAAKyH,QACT,C,QACQP,GAEAlH,EAAK0H,YAAYR,GAErBlH,EAAKoH,OAASD,CAClB,CACJ,CACJ,EAMA,KAAAN,kBAAoB,SAAC7G,GACjB,KAAMA,aAAgB2F,iBAClB,MAAM,IAAIC,MAAM,6DAEpB,IAAIC,EAAU,EAAKE,cAAc/F,GAC7B2H,EAAgB,EAAKzC,WAAWW,GAChC+B,EAAsBD,aAAa,EAAbA,EAAeE,MAAK,SAAAC,GAAO,SAAKxC,QAAQwC,EAAb,IAErD,GAAIF,EAAqB,CACrB,IAAMG,EAAe,EAAK9C,aAAa2C,GACnCG,aAAwBC,aACxBD,EAAaE,OAErB,CACJ,EAUA,KAAAC,QAAU,SAAClI,EAAuBmI,EAA6BzC,GAC3D,QAD8B,IAAAyC,IAAAA,GAAA,KACxBnI,aAAgB2F,iBAClB,MAAM,IAAIC,MAAM,mDAEhBuC,GACA,EAAK1C,aAAazF,EAAM0F,GAE5B,IAAIG,EAAU,EAAKE,cAAc/F,GAC7B2H,EAAgB,EAAKzC,WAAWW,GAEpC,SADsE,KAAlD8B,aAAa,EAAbA,EAAeS,MAAK,SAAAN,GAAO,SAAKxC,QAAQwC,EAAb,KAEnD,EAUA,KAAAO,aAAe,SAAClC,EAA2BgC,EAA6BzC,QAA7B,IAAAyC,IAAAA,GAAA,GACnCA,GACA,EAAKjC,cAAcC,EAAOT,GAG9B,IAAIU,EAAW,EAAKL,cAAcI,GAClC,YAAkCF,IAA3B,EAAKX,QAAQc,EACxB,EAwfQ,KAAAkC,QAAoC,CACxCzL,KAAMiD,SAASyI,KACfC,OAAO,EACPC,eAAe,GAsHnB,KAAAC,4BAA8B,yBAK9B,KAAAC,iCAAmC,yBAKnC,KAAAC,8BAAgC,yBAKhC,KAAAC,mCAAqC,yBAKrC,KAAAC,8BAAgC,4BAKhC,KAAAC,mCAAqC,2BAzhCjCjD,KAAKjB,OAASA,GAAUzG,CAC5B,CAyhCJ,OAjhCI,YAAA4K,YAAA,SAAYzJ,EAAcmG,GAClBI,KAAKhB,UAAUvF,KAKnBuG,KAAKjB,OAAOrG,IAAI,0BAA2Be,GAC3CuG,KAAKhB,UAAUvF,GAAQmG,EAC3B,EAKQ,YAAAuD,gBAAR,WACI,IAAIC,EAAM,IAAIC,EAGdrD,KAAKkD,YAAY,WAAYE,EAAIhJ,UAEjC4F,KAAKkD,YAAY,SAAUE,EAAIpI,cAC/BgF,KAAKkD,YAAY,YAAaE,EAAIpI,cAClCgF,KAAKkD,YAAY,YAAaE,EAAIpI,cAElCgF,KAAKkD,YAAY,UAAWE,EAAI/H,SAEhC2E,KAAKkD,YAAY,QAASE,EAAI5H,OAE9BwE,KAAKkD,YAAY,QAASE,EAAIxH,OAE9BoE,KAAKkD,YAAY,aAAcE,EAAInH,YAEnC+D,KAAKkD,YAAY,QAASE,EAAIpH,OAE9BgE,KAAKkD,YAAY,MAAOE,EAAI3G,KAE5BuD,KAAKkD,YAAY,QAASE,EAAIxG,OAE9BoD,KAAKkD,YAAY,SAAUE,EAAIvG,OACnC,EAOQ,YAAAyG,aAAR,SAAqBvM,EAAkBwM,GAGnC,IADA,IACiB,MADe9I,MAAMC,KAAK3D,EAAK4D,iBAA8B,eAC7D,eAA2B,CAAvC,IAAI6I,EAAI,MACLtJ,EAAOF,SAASyJ,eAAeD,EAAKjC,aAAa,oBACjC1B,iBAChB0D,EAAGrL,KAAK8H,KAAM9F,EAAMsJ,EAE5B,CAGA,IAAIE,EAAQjJ,MAAMC,KAAK3D,EAAK4D,iBAAkC,SAC1D5D,aAAgB8I,iBAGhB6D,EAAMjG,KAAK1G,GAGf,IAAM4M,EAAkB5M,aAAgB6M,QAAW7M,EAAK8M,QAAQ,QAAU,KACtEF,GACAD,EAAMjG,KAAKkG,GAGf,IAAiB,UAAAD,EAAA,eAGb,IAHC,IAAIxJ,EAAI,KAGQ,MAFeO,MAAMC,KAAKR,EAAKS,iBAA8B,sBAE7D,eAAR6I,EAAI,KACTD,EAAGrL,KAAK8H,KAAM9F,EAAMsJ,EAGhC,EAEQ,YAAAM,0BAAR,SAAkC5J,EAAmBsJ,G,UAC7CO,EAAS/D,KAAKC,cAAc/F,GAC5B8J,EAAkC,QAAzB,KAAGhE,KAAKf,YAAW8E,UAAM,UAANA,GAAY,CAAC,EAEzCE,EAAeT,EAAKjC,aAAa,mBACrC,GAAK0C,EAAL,CAEA,IAAIC,EAA8B,QAAzB,EAAGF,EAAUC,UAAY,QAAtBD,EAAUC,GAAkB,GACpCC,EAAMvH,QAAQ6G,GAAQ,EACtBU,EAAMzG,KAAK+F,GAGXxD,KAAKjB,OAAOrG,IAAI,iDAAkDe,KAAM+J,EAPnD,CAS7B,EAEQ,YAAAW,4BAAR,SAAoCjK,EAAmBsJ,GACnD,IAAIO,EAAS/D,KAAKC,cAAc/F,GAC5B8J,EAAYhE,KAAKf,WAAW8E,GAChC,GAAKC,EAAL,CAEA,IAAIC,EAAeT,EAAKjC,aAAa,mBACrC,GAAK0C,EAAL,CAEA,IAAIC,EAAQF,EAAUC,GACtB,GAAKC,EAAL,CAGA,IAAIE,EAAQF,EAAMvH,QAAQ6G,GACtBY,GAAS,EACTF,EAAMG,OAAOD,EAAO,GAGpBpE,KAAKjB,OAAOrG,IAAI,kDAAmDe,KAAM+J,EAN7E,CALyB,CAHH,CAgB1B,EAMA,YAAAc,gBAAA,SAAgBC,GAKZ,IAJA,IAAIC,EAAkC,CAAC,EACnCC,EAA0C,CAAC,EAGtCC,EAAI,EAAGA,EAAIH,EAAWpJ,OAAQuJ,IAAK,CACxC,IAAIC,EAAIJ,EAAWG,GACnB,GAAoC,IAAhCC,EAAElL,KAAKkD,QAAQ,aAAoB,CACnC,IAAInF,EAAMmN,EAAElL,KAAK2D,OAJf,GAKFqH,EAAoBjN,GAAOmN,EAAEtM,KACjC,CACJ,C,eAESb,GACL,IAA0B,IAAtBA,EAAImF,QAAQ,KAAa,CAWzB,IAVA,IAAIiI,EAAalN,OAAOmN,KAAKJ,GAAqBK,QAAO,SAAAC,GACrD,OAAQA,IAAMvN,GAA4B,IAAnBuN,EAAEpI,QAAQnF,EACrC,IAEIwN,EAAyC,CACzCC,MAAOR,EAAoBjN,GAC3B6C,OAAQ,CAAC,GAGT6K,GAAQ1N,EAAM,KAAK2D,OACduJ,EAAI,EAAGA,EAAIE,EAAWzJ,OAAQuJ,IAAK,CACxC,IAAIS,EAASV,EAAoBG,EAAWF,IACxCU,EAAOR,EAAWF,GAAGtH,OAAO8H,GAEhCF,EAAU3K,OAAO+K,GAAQD,CAC7B,CAEAX,EAAWhN,GAAOwN,CACtB,C,EApBJ,IAAK,IAAIxN,KAAOiN,E,EAAPjN,GAuBT,OAAOgN,CACX,EAKQ,YAAAa,MAAR,WAII,MAAO,uCAAuC9I,QAAQ,SAAS,SAAU+I,GACrE,IAAMC,EAAoB,GAAhBC,KAAKC,SAAgB,EAC/B,OAD2C,KAALH,EAAWC,EAAS,EAAJA,EAAU,GACvDG,SAAS,GACtB,GACJ,EAMQ,YAAAzF,cAAR,SAAsB0F,GAClB,IAAIC,EAAI5F,KAAKd,YAAY4F,QAAO,SAAAvG,GAC5B,OAAOA,EAAEoH,OAASA,CACtB,IAAG,GAEH,GAAIC,EACA,OAAOA,EAAE5D,IAGb,IAAIA,EAAMhC,KAAKqF,QAMf,OALArF,KAAKd,YAAYzB,KAAK,CAClBkI,KAAMA,EACN3D,IAAKA,IAEThC,KAAKb,aAAa6C,GAAO2D,EAClB3D,CACX,EAMQ,YAAA6D,sBAAR,SAA8B9F,GAC1B,IAAI8B,EAAgB7B,KAAKZ,WAAWW,GACpC,IAAK8B,GAA0C,IAAzBA,EAAc1G,OAChC,OAAOwC,QAAQmI,SAAQ,GAK3B,IAFA,IAAIC,EAA8B,GAEb,MAAAlE,EAAA,eAAe,CAA/B,IAAImE,EAAQ,KACPC,EAAYjG,KAAKX,WAAW2G,GAC9BC,GACAF,EAAetI,KAAKwI,EAE5B,CAEA,IAAIC,EAAQH,EAAe3M,KAAI,SAAApC,GAAW,OAAAA,GAAA,IAC1C,OAAO2G,QAAQwI,IAAID,GAAOE,MAAK,SAAAC,GAAU,OAAAA,EAAOC,OAAM,SAAA/H,GAAK,OAAAA,CAAA,GAAlB,GAC7C,EAGQ,YAAAgI,cAAR,SAAsBC,G,MAClB,GAAKA,EAAMtM,KAAX,CAGA,IAAI6J,EAAS/D,KAAKC,cAAcuG,EAAMtM,MACtC,OAA8B,QAAvB,EAAA8F,KAAKf,WAAW8E,UAAO,eAAGyC,EAAM/M,KAFvC,CAGJ,EA8KQ,YAAAgN,eAAR,SAAuBlI,GAEnB,QAASA,GAAKA,EAAa,WAAKA,EAAa,UAAkB,eACnE,EAOQ,YAAAmI,eAAR,SAAuBxM,EAAuB8L,GAA9C,I,IAAA,OACQjG,EAAUC,KAAKC,cAAc/F,GAC7B2H,EAAuC,QAA1B,KAAG7B,KAAKZ,YAAWW,UAAO,UAAPA,GAAa,GAiBjD,IAhB+C,IAArC8B,EAAclF,QAAQqJ,IAE5BnE,EAAcpE,KAAKuI,GAEfhG,KAAKwC,QAAQG,eACb3C,KAAKjB,OAAOrG,IAAI,6BAA8BwB,GAC9CA,EAAKyM,aAAa,aAAc,eAGhC3G,KAAKjB,OAAOrG,IAAI,iCAAkCwB,IAItD8F,KAAKjB,OAAOrG,IAAI,6CAA8CsN,IAG9DhG,KAAKV,WAAWS,GAApB,CAIA,IAAI6G,EAA0C,KAC1CrD,EAA2C,SAAChF,EAAGqB,GAE/C,OAAIgH,IAIC,EAAKH,eAAelI,IAIzBqI,EAAiB,EAAKf,sBAAsB9F,GAGxCxB,GACA,EAAKiC,YAAYjC,GAGrB,EAAKQ,OAAOrG,IAAI,aAAcwB,GAEvB0M,EAAeR,MAAK,SAAMvF,GAAO,qC,wDAEpC,OADAb,KAAKjB,OAAOrG,IAAI,2BAA4BmI,EAAS3G,GACjD0F,GACAA,EAASiB,GACF,CAAP,EAAOA,KAGLgG,EAAkB,IAAIC,YAAY,aACpC,CACIC,OAAQ,CAAEC,MAAOnG,KAEzB3G,EAAKgH,cAAc2F,GAGnB,GAAM,IAAIlJ,SAAQ,SAAAmI,GAAW,OAAAmB,WAAWnB,EAAS,EAApB,M,OAE7B,OAFA,SACA9F,KAAKY,gBAAgB1G,EAAM2G,EAAStC,GAC7B,CAAP,EAAOsC,G,UACRqG,OAAM,SAAAjC,GAEL,OADA,EAAKlG,OAAOrG,IAAI,mBAAoBuM,IAC7B,CACX,IAAGkC,SAAQ,WACPP,EAAiB,IACrB,KAlCWjJ,QAAQmI,SAAQ,GAmC/B,EAEA5L,EAAKkN,iBAAiB,SAAU7D,GAEhC,IAAM8D,EAAU,SAAC9I,GAGb,IAFA,IAEqB,MAFC,EAAKa,WAAWW,GAEjB,eAAe,CAA/B,IAAI,EAAQ,KACb,EAAKuH,WAAW,EACpB,CACA,EAAKC,eACT,EACArN,EAAKkN,iBAAiB,QAASC,GAE/B9D,EAAGiE,OAAS,WACRtN,EAAKuN,oBAAoB,SAAUlE,GACnCrJ,EAAKuN,oBAAoB,QAASJ,EACtC,EAEArH,KAAKV,WAAWS,GAAWwD,CAhE3B,CAiEJ,EAKA,YAAAmE,MAAA,SAAMlB,GACExG,KAAK2H,WAAWnB,GAChBxG,KAAKsH,WAAWtH,KAAKC,cAAcuG,IAGnCxG,KAAK4H,KAAKpB,EAElB,EAEQ,YAAAc,WAAR,SAAmBtB,GACf,IAAIQ,EAAQxG,KAAKb,aAAa6G,GAC9BhG,KAAK6H,YAAYrB,EAAO,GAAIxG,KAAK4C,6BACjC5C,KAAK6H,YAAYrB,EAAO,GAAIxG,KAAK6C,kCAEjC,IAAIqB,EAAQtL,EAAc4N,IAAUxG,KAAKuG,cAAcC,GACvD,GAAItC,EACA,IAAK,IAAIQ,EAAI,EAAGA,EAAIR,EAAM/I,OAAQuJ,IAC9BR,EAAMQ,GAAGoD,UAAY,GACrB9H,KAAK6H,YAAY3D,EAAMQ,GAAI,GAAI1E,KAAK8C,+BACpC9C,KAAK6H,YAAY3D,EAAMQ,GAAI,GAAI1E,KAAK+C,2CAIrC/C,KAAKR,QAAQwG,EACxB,EAEQ,YAAA+B,iBAAR,SAAyB7N,EAAuB8L,G,MACxCjG,EAAUC,KAAKC,cAAc/F,GAC7B2H,EAAgB7B,KAAKZ,WAAWW,GACpC,GAAK8B,EAAL,CAGA,IAAImG,EAAgBnG,EAAclF,QAAQqJ,GACtCgC,GAAiB,GACjBnG,EAAcwC,OAAO2D,EAAe,GAE/BnG,EAAc1G,SACS,QAAxB,EAAA6E,KAAKV,WAAWS,UAAQ,SAAEyH,gBACnBxH,KAAKV,WAAWS,UAChBC,KAAKZ,WAAWW,UAChBC,KAAKf,WAAWc,KAI3BC,KAAKjB,OAAOrG,IAAI,8CAA+CsN,EAbnE,CAeJ,EAOA,YAAAiC,SAAA,SAASzB,GAAT,I,EAAA,OACQxE,EAAMhC,KAAKC,cAAcuG,GAEzBhC,EAAaxE,KAAKsE,gBAAgBkC,EAAMjC,YAO5C,GANAvE,KAAKX,WAAW2C,GAAOhC,KAAKkI,gBAAgB1B,EAAOhC,GAE/CgC,EAAMtM,MACN8F,KAAK0G,eAAeF,EAAMtM,KAAM8H,IAGhChC,KAAKT,YAAYyC,GAArB,CAIA,IAAMuB,EAA8B,SAAO4E,EAAOvI,GAAQ,qC,4DAEtD,KADIwI,EAAWpI,KAAKX,WAAW2C,IAChB,MAAO,CAAP,GAAO,GAEtB,IACKwE,EAAM6B,QAAQC,UACfH,GAAwB,UAAfA,EAAM5N,OACdiM,EAAM+B,UAAUC,SAASxI,KAAK4C,6BAG/B,MAAO,CAAP,GAAO,GAGX5C,KAAKjB,OAAOrG,IAAI,aAAc,CAAEyP,MAAK,I,iBAEjB,O,sBAAA,GAAMC,K,OAEtB,OAFMvH,EAAU,SAChBjB,EAASiB,GACF,CAAP,EAAOA,G,OAIP,O,WADAb,KAAKjB,OAAOrG,IAAI,mBAAoB,GAC7B,CAAP,GAAO,G,yBAIX+P,EAA2C,KAC/ClF,EAAGmF,UAAY,SAACP,EAAOvI,GACO,OAAtB6I,GACAE,aAAaF,GAEjBA,EAAoBxB,YAAW,WAC3B1D,EAAG4E,EAAOvI,EACd,GAAG,EAAKH,SACZ,EAEA,IAAMmJ,EAAepC,aAAiBzN,kBAAoB,SAAW,eAE/D8P,GADsC,QAAtB,EAAArC,EAAM6B,QAAQC,gBAAQ,QAAIM,GACnB5L,MAAM,KAEnC6L,EAAOC,SAAQ,SAACC,GACdvC,EAAMY,iBAAiB2B,EAAWxF,EAAGmF,UACvC,IAEAnF,EAAGiE,OAAS,WACVqB,EAAOC,SAAQ,SAACC,GACdvC,EAAMiB,oBAAoBsB,EAAWxF,EAAGmF,UAC1C,GACF,EAEA1I,KAAKT,YAAYyC,GAAOuB,CAnDxB,CAoDJ,EAEA,YAAAyF,YAAA,SAAYxC,GACR,IAAIxE,EAAMhC,KAAKC,cAAcuG,GAGzBjD,EAAKvD,KAAKT,YAAYyC,IACtBuB,aAAE,EAAFA,EAAIiE,UACJjE,EAAGiE,gBACIjE,EAAGiE,eAGPxH,KAAKR,QAAQwC,UACbhC,KAAKT,YAAYyC,UACjBhC,KAAKX,WAAW2C,GAEnBwE,EAAMtM,MACN8F,KAAK+H,iBAAiBvB,EAAMtM,KAAM8H,EAE1C,EAOQ,YAAAiH,WAAR,SAAmBlS,EAAkBwM,GACjC,IAAI2F,EAASzO,MAAMC,KAAK3D,EAAK4D,iBAAqCzB,EAAoB,uBAIlFN,EAAc7B,IAA2C,SAAlCA,EAAKwK,aAAa,aACzC2H,EAAOzL,KAAK1G,GAGhB,IAAK,IAAI2N,EAAI,EAAGA,EAAIwE,EAAO/N,OAAQuJ,IAAK,CACpC,IAAI8B,EAAQ0C,EAAOxE,GACnBnB,EAAGrL,KAAK8H,KAAMwG,EAClB,CACJ,EAKA,YAAA2C,iBAAA,WACI,IAAKzR,OAAOmN,KAAK7E,KAAKR,SAASrE,OAC3B,OAAO,KAGX,IAAIiO,EAAmB,GACnBC,EAAKrP,SAASwH,cAAc,MAChC,IAAK,IAAIhK,KAAOwI,KAAKR,QAAS,CAG1B,IAAM8J,EAAkBtJ,KAAKb,aAAa3H,GAC1C,KAAI8R,aAA2BxQ,mBACE,aAAzBwQ,EAAgB/O,MAAgD,UAAzB+O,EAAgB/O,OACnD+O,EAAgBC,YAAcvJ,KAAK6C,kCAQ3CuG,EAAiBzM,QAAQqD,KAAKR,QAAQhI,KAAS,GAAnD,CAIA,IAAIgS,EAAKxP,SAASwH,cAAc,MAChCgI,EAAG1B,UAAY9H,KAAKR,QAAQhI,GAC5B6R,EAAG5H,YAAY+H,GACfJ,EAAiB3L,KAAKuC,KAAKR,QAAQhI,GALnC,CAMJ,CACA,OAAO6R,CACX,EAKQ,YAAA9B,cAAR,WACI,IAAIkC,EAAkBzP,SAASW,iBAAiB,gCAChD,GAAK8O,EAAgBtO,OAArB,CAMA,IAAIuO,EAAShL,KAAKiL,UAAU3J,KAAKR,QAAS9H,OAAOmN,KAAK7E,KAAKR,SAASoK,QACpE,GAAIF,IAAW1J,KAAK6J,oBAApB,CAKA7J,KAAK6J,oBAAsBH,EAG3B,IAFA,IAAIL,EAAKrJ,KAAKmJ,mBAELzE,EAAI,EAAGA,EAAI+E,EAAgBtO,OAAQuJ,IAAK,CAK7C,IAJA,IAAInG,EAAIkL,EAAgB/E,GAGpBoF,EAAevL,EAAE5D,iBAAiB,MAC7BoP,EAAI,EAAGA,EAAID,EAAa3O,OAAQ4O,IACrCD,EAAaC,GAAGvC,SAIhB6B,GAAMA,EAAGW,iBACThK,KAAK6H,YAAYtJ,EACbyB,KAAKgD,8BACLhD,KAAKiD,oCACT1E,EAAEkD,YAAY4H,EAAGY,WAAU,KAE3BjK,KAAK6H,YAAYtJ,EACbyB,KAAKiD,mCACLjD,KAAKgD,8BAEjB,CA1BA,CAPA,CAkCJ,EAOA,YAAAkH,SAAA,SAAS1D,EAA2B2D,GAChC,IAAIjG,EAAQlE,KAAKuG,cAAcC,GAC/B,GAAItC,EACA,IAAK,IAAIQ,EAAI,EAAGA,EAAIR,EAAM/I,OAAQuJ,IACjBR,EAAMQ,GACnBR,EAAMQ,GAAGoD,UAAYqC,EACrBnK,KAAK6H,YAAY3D,EAAMQ,GACnB1E,KAAK8C,8BACL9C,KAAK+C,oCAQjB,GAJA/C,KAAK6H,YAAYrB,EACbxG,KAAK4C,4BACL5C,KAAK6C,kCAEL2D,EAAMtM,KAEN,KAAMgP,EAAS1C,EAAMtM,KAAKS,iBAAiBzB,EAAoB,iBAAUsN,EAAM/M,KAAI,QACnF,IAASiL,EAAI,EAAGA,EAAIwE,EAAO/N,OAAQuJ,IAAK,CACpC1E,KAAK6H,YAAYqB,EAAOxE,GACpB1E,KAAK4C,4BACL5C,KAAK6C,kCAET,IAAIb,EAAMhC,KAAKC,cAAciJ,EAAOxE,IACpC1E,KAAKR,QAAQwC,GAAOmI,CACxB,CARyF,CAW7FnK,KAAKuH,eACT,EAMA,YAAA6C,YAAA,SAAY5D,GACR,IAAItC,EAAQlE,KAAKuG,cAAcC,GAC/B,GAAItC,EACA,IAAK,IAAIQ,EAAI,EAAGA,EAAIR,EAAM/I,OAAQuJ,IAC9BR,EAAMQ,GAAGoD,UAAY,GACrB9H,KAAK6H,YAAY3D,EAAMQ,GACnB1E,KAAK+C,mCACL/C,KAAK8C,+BASjB,GALA9C,KAAK6H,YAAYrB,EACbxG,KAAK6C,iCACL7C,KAAK4C,6BAGL4D,EAAMtM,KACN,KAAMgP,EAAS1C,EAAMtM,KAAKS,iBAAiBzB,EAAoB,iBAAUsN,EAAM/M,KAAI,QACnF,IAASiL,EAAI,EAAGA,EAAIwE,EAAO/N,OAAQuJ,IAAK,CACpC1E,KAAK6H,YAAYqB,EAAOxE,GACpB1E,KAAK6C,iCACL7C,KAAK4C,6BAET,IAAIZ,EAAMhC,KAAKC,cAAciJ,EAAOxE,WAC7B1E,KAAKR,QAAQwC,EACxB,CARyF,CAW7FhC,KAAKuH,eACT,EAOA,YAAAW,gBAAA,SAAgB1B,EAA2BhC,GAA3C,WACI,OAAO,gD,+EAEExE,KAAKqK,SAAS7D,IAAWxG,KAAK2H,WAAWnB,GAA1C,Y,gBACgBhC,E,+DACRQ,EAAYR,E,MACZ8F,EAAWtK,KAAKhB,UAAUxH,KAM9BwI,KAAKjB,OAAOrG,IAAI,kCAAmClB,EAAKgP,GAEpDH,EAASiE,EAAS9D,EAAMnO,MAAOmO,EAAOxB,EAAU3K,QAChD2M,GAAQ,EACR/B,EAAQD,EAAUC,MAEA,kBAAXoB,EAAP,OACAW,EAAQX,E,SAVRrG,KAAKjB,OAAOrG,IAAI,iDAAkDlB,GAClE,Q,yBAUyB,iBAAX6O,EAAP,OACPW,GAAQ,EACR/B,EAAQoB,E,cAES,SAAMA,G,OACG,kBADtBkE,EAAa,UAEbvD,EAAQuD,GAERvD,GAAQ,EACR/B,EAAQsF,G,iBAIhB,IAAKvD,EAED,OADAhH,KAAKkK,SAAS1D,EAAOvB,GACd,CAAP,GAAO,G,yCAMnB,OADAjF,KAAKoK,YAAY5D,GACV,CAAP,GAAO,G,OAEf,EAOQ,YAAA6D,SAAR,SAAiB7D,GACb,QAASxG,KAAKN,mBAAqB8G,EAAMgE,aAAehE,EAAMiE,cAAgBjE,EAAMkE,iBAAiBvP,OACzG,EAOQ,YAAAwM,WAAR,SAAmBnB,GAGf,OAAQA,EAA6BmE,QACzC,EAQQ,YAAA9C,YAAR,SAAoBhP,EAAkB+R,EAAkBC,IAChDD,GAAa5K,KAAK2H,WAAW9O,IAAaA,EAAQ0P,UAAUC,SAASoC,IACrE/R,EAAQ0P,UAAUuC,IAAIF,GAEtB/R,EAAQ0P,UAAUC,SAASqC,IAC3BhS,EAAQ0P,UAAUf,OAAOqD,EAEjC,EAgBA,YAAAE,UAAA,SAAUvI,GAAV,WACI9K,OAAOsT,OAAOhL,KAAKwC,QAASA,GAE5BxC,KAAKmD,kBACL,IAAInJ,EAAWiR,OAAOjR,SAChBjD,EAAOiJ,KAAKwC,QAAQzL,KACpBmU,EAAO,WACT,EAAKtD,KAAK7Q,GAGN,EAAKyL,QAAQE,OACb,EAAKA,MAAM3L,EAEnB,EAG4B,aAAxBiD,EAASmR,YAAqD,gBAAxBnR,EAASmR,WAC/CD,IAIAlR,EAASoN,iBAAiB,mBAAoB8D,EAEtD,EAMA,YAAAtD,KAAA,SAAK7Q,GACDA,UAAAA,EAASiJ,KAAKwC,QAAQzL,MACtBiJ,KAAKjB,OAAOrG,IAAI,WAAY3B,GAC5BiJ,KAAKsD,aAAavM,EAAMiJ,KAAK8D,2BAC7B9D,KAAKiJ,WAAWlS,EAAMiJ,KAAKiI,SAC/B,EAMA,YAAAT,OAAA,SAAOzQ,GACHA,UAAAA,EAASiJ,KAAKwC,QAAQzL,MACtBiJ,KAAKjB,OAAOrG,IAAI,WAAY3B,GAC5BiJ,KAAKsD,aAAavM,EAAMiJ,KAAKmE,6BAC7BnE,KAAKiJ,WAAWlS,EAAMiJ,KAAKgJ,YAC/B,EAMA,YAAAtG,MAAA,SAAM3L,GAAN,WACIA,UAAAA,EAASiJ,KAAKwC,QAAQzL,MACtBiJ,KAAKoL,SAAW,IAAIC,kBAAiB,SAAAC,GACjCA,EAAUxC,SAAQ,SAAAyC,GACd,EAAKC,SAASD,EAClB,GACJ,IACAvL,KAAKoL,SAASK,QAAQ1U,EAAM,CACxBwN,YAAY,EACZmH,WAAW,EACXC,SAAS,IAEb3L,KAAKjB,OAAOrG,IAAI,yBACpB,EAEQ,YAAA8S,SAAR,SAAiBD,G,UACb,GAAsB,cAAlBA,EAAShR,KAAsB,CAC/B,IAAK,IAAImK,EAAI,EAAGA,EAAI6G,EAASK,WAAWzQ,OAAQuJ,IAAK,CACjD,IAAIiB,EAAO4F,EAASK,WAAWlH,GAC/B1E,KAAKjB,OAAOrG,IAAI,aAAciN,GAC1BA,aAAgBzD,aAChBlC,KAAK4H,KAAKjC,EAElB,CACA,IAASjB,EAAI,EAAGA,EAAI6G,EAASM,aAAa1Q,OAAQuJ,IAC1CiB,EAAO4F,EAASM,aAAanH,GACjC1E,KAAKjB,OAAOrG,IAAI,eAAgBiN,GAC5BA,aAAgBzD,aAChBlC,KAAKwH,OAAO7B,EAGxB,MAAO,GAAsB,eAAlB4F,EAAShR,MACZgR,EAASO,kBAAkB5J,YAI3B,GAAsB,aAHAqJ,EAASQ,cAGG,CAC9B,IAAMD,EAASP,EAASO,OACxB9L,KAAK0H,MAAMoE,EACf,KACK,CACD,IAAME,EAA4B,QAAjB,EAAAT,EAASS,gBAAQ,QAAI,GAChCC,EAAoE,QAAzD,EAAkD,QAAlD,EAAAV,EAASO,OAAOvH,WAAWgH,EAASQ,sBAAc,eAAE1T,aAAK,QAAI,GAC9E2H,KAAKjB,OAAOrG,IAAI,2CACZ6S,EAASQ,cACTC,EACAC,EACAV,EAASO,QACTE,IAAaC,GACbjM,KAAK4H,KAAK2D,EAASO,OAE3B,CAGZ,EA+BJ,EA3lCA,G", "sources": ["webpack://aspnetValidation/webpack/universalModuleDefinition", "webpack://aspnetValidation/webpack/bootstrap", "webpack://aspnetValidation/webpack/runtime/define property getters", "webpack://aspnetValidation/webpack/runtime/hasOwnProperty shorthand", "webpack://aspnetValidation/webpack/runtime/make namespace object", "webpack://aspnetValidation/./src/index.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"aspnetValidation\"] = factory();\n\telse\n\t\troot[\"aspnetValidation\"] = factory();\n})(self, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/**\n * A simple IDictionary<string, string>\n */\nexport interface StringKeyValuePair {\n    [key: string]: string\n}\n\n/**\n * A duplex key-value pair for an element, by GUID or its DOM object reference.\n */\ninterface ElementUID {\n    node: Element,\n    uid: string;\n}\n\n/**\n * A simple logging interface that mirrors the Console object.\n */\nexport interface Logger {\n    log(message: string, ...args: any[]): void;\n    warn(message: string, ...args: any[]): void;\n}\n\nconst nullLogger = new (class implements Logger {\n    log(_: string, ..._args: any[]): void { }\n    warn = globalThis.console.warn;\n})();\n\n/**\n * An `HTMLElement` that can be validated (`input`, `select`, `textarea`).\n */\nexport type ValidatableElement = HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;\n\n/**\n * Checks if `element` is validatable (`input`, `select`, `textarea`).\n * @param element The element to check.\n * @returns `true` if validatable, otherwise `false`.\n */\nexport const isValidatable = (element: Node): element is ValidatableElement =>\n    element instanceof HTMLInputElement\n    || element instanceof HTMLSelectElement\n    || element instanceof HTMLTextAreaElement;\n\nconst validatableElementTypes = ['input', 'select', 'textarea'];\n\n/**\n * Generates a selector to match validatable elements (`input`, `select`, `textarea`).\n * @param selector An optional selector to apply to the valid input types, e.g. `[data-val=\"true\"]`.\n * @returns The validatable elements.\n */\nconst validatableSelector = (selector?: string) =>\n    validatableElementTypes.map(t => `${t}${selector || ''}`).join(',');\n\n/**\n * Parameters passed into validation providers from the element attributes.\n * error property is read from data-val-[Provider Name] attribute.\n * params property is populated from data-val-[Provider Name]-[Parameter Name] attributes.\n */\nexport interface ValidationDirectiveBindings {\n    error: string,\n    params: StringKeyValuePair\n}\n\n/**\n * A key-value pair describing what validations to enforce to an input element, with respective parameters.\n */\nexport type ValidationDirective = {\n    [key: string]: ValidationDirectiveBindings\n};\n\n/**\n * Validation plugin signature with multitype return.\n * Boolean return signifies the validation result, which uses the default validation error message read from the element attribute.\n * String return signifies failed validation, which then will be used as the validation error message.\n * Promise return signifies asynchronous plugin behavior, with same behavior as Boolean or String.\n */\nexport type ValidationProvider = (value: string, element: ValidatableElement, params: StringKeyValuePair) => boolean | string | Promise<boolean | string>;\n\n/**\n * Callback to receive the result of validating a form.\n */\nexport type ValidatedCallback = (success: boolean) => void;\n\ninterface ValidationEventCallback<TEvent extends Event = Event> {\n    (e?: TEvent, callback?: ValidatedCallback): Promise<boolean>;\n    debounced?: (e?: TEvent, callback?: ValidatedCallback) => void;\n    remove?: () => void;\n}\n\n/**\n * A callback method signature that kickstarts a new validation task for an input element, as a Boolean Promise.\n */\ntype Validator = () => Promise<boolean>;\n\n/**\n * Resolves and returns the element referred by original element using ASP.NET selector logic.\n * @param element - The input to validate\n * @param selector - Used to find the field. Ex. *.Password where * replaces whatever prefixes asp.net might add.\n */\nfunction getRelativeFormElement(element: ValidatableElement, selector: string): ValidatableElement {\n    // example elementName: Form.PasswordConfirm, Form.Email\n    // example selector (dafuq): *.Password, *.__RequestVerificationToken\n    // example result element name: Form.Password, __RequestVerificationToken\n\n    let elementName = element.name;\n    let selectedName = selector.substring(2); // Password, __RequestVerificationToken\n    let objectName = '';\n\n    let dotLocation = elementName.lastIndexOf('.');\n    if (dotLocation > -1) {\n        // Form\n        objectName = elementName.substring(0, dotLocation);\n\n        // Form.Password\n        const relativeElementName = objectName + '.' + selectedName;\n        const relativeElement = document.getElementsByName(relativeElementName)[0];\n        if (isValidatable(relativeElement)) {\n            return relativeElement;\n        }\n    }\n\n    // __RequestVerificationToken\n    return element.form.querySelector(validatableSelector(`[name=${selectedName}]`));\n}\n\n/**\n * Contains default implementations for ASP.NET Core MVC validation attributes.\n */\nexport class MvcValidationProviders {\n    /**\n     * Validates whether the input has a value.\n     */\n    required: ValidationProvider = (value, element, params) => {\n        // Handle single and multiple checkboxes/radio buttons.\n        const elementType = element.type.toLowerCase();\n        if (elementType === \"checkbox\" || elementType === \"radio\") {\n            const allElementsOfThisName = Array.from(element.form.querySelectorAll(validatableSelector(`[name='${element.name}'][type='${elementType}']`)));\n            for (let element of allElementsOfThisName) {\n                if (element instanceof HTMLInputElement && element.checked === true) {\n                    return true;\n                }\n            }\n\n            // Checkboxes do not submit a value when unchecked. To work around this, platforms such as ASP.NET render a\n            // hidden input with the same name as the checkbox so that a value (\"false\") is still submitted even when\n            // the checkbox is not checked. We check this special case here.\n            if (elementType === \"checkbox\") {\n                const checkboxHiddenInput = element.form.querySelector(`input[name='${element.name}'][type='hidden']`);\n                if (checkboxHiddenInput instanceof HTMLInputElement && checkboxHiddenInput.value === \"false\") {\n                    return true;\n                }\n            }\n\n            return false;\n        }\n\n        // Default behavior otherwise (trim ensures whitespace only is not seen as valid).\n        return Boolean(value?.trim());\n    }\n\n    /**\n     * Validates whether the input value satisfies the length contstraint.\n     */\n    stringLength: ValidationProvider = (value, element, params) => {\n        if (!value) {\n            return true;\n        }\n\n        if (params.min) {\n            let min = parseInt(params.min);\n            if (value.length < min) {\n                return false;\n            }\n        }\n\n        if (params.max) {\n            let max = parseInt(params.max);\n            if (value.length > max) {\n                return false;\n            }\n        }\n\n        return true;\n    }\n\n    /**\n     * Validates whether the input value is equal to another input value.\n     */\n    compare: ValidationProvider = (value, element, params) => {\n        if (!params.other) {\n            return true;\n        }\n\n        let otherElement = getRelativeFormElement(element, params.other);\n        if (!otherElement) {\n            return true;\n        }\n\n        return (otherElement.value === value);\n    }\n\n    /**\n     * Validates whether the input value is a number within a given range.\n     */\n    range: ValidationProvider = (value, element, params) => {\n        if (!value) {\n            return true;\n        }\n\n        let val = parseFloat(value);\n        if (isNaN(val)) {\n            return false;\n        }\n\n        if (params.min) {\n            let min = parseFloat(params.min);\n            if (val < min) {\n                return false;\n            }\n        }\n\n        if (params.max) {\n            let max = parseFloat(params.max);\n            if (val > max) {\n                return false;\n            }\n        }\n\n        return true;\n    }\n\n    /**\n     * Validates whether the input value satisfies a regular expression pattern.\n     */\n    regex: ValidationProvider = (value, element, params) => {\n        if (!value || !params.pattern) {\n            return true;\n        }\n\n        let r = new RegExp(params.pattern);\n        return r.test(value);\n    }\n\n    /**\n     * Validates whether the input value is an email in accordance to RFC822 specification, with a top level domain.\n     */\n    email: ValidationProvider = (value, element, params) => {\n        if (!value) {\n            return true;\n        }\n\n        // RFC822 email address with .TLD validation\n        // (c) Richard Willis, Chris Ferdinandi, MIT Licensed\n        // https://gist.github.com/badsyntax/719800\n        // https://gist.github.com/cferdinandi/d04aad4ce064b8da3edf21e26f8944c4\n\n        let r = /^([^\\x00-\\x20\\x22\\x28\\x29\\x2c\\x2e\\x3a-\\x3c\\x3e\\x40\\x5b-\\x5d\\x7f-\\xff]+|\\x22([^\\x0d\\x22\\x5c\\x80-\\xff]|\\x5c[\\x00-\\x7f])*\\x22)(\\x2e([^\\x00-\\x20\\x22\\x28\\x29\\x2c\\x2e\\x3a-\\x3c\\x3e\\x40\\x5b-\\x5d\\x7f-\\xff]+|\\x22([^\\x0d\\x22\\x5c\\x80-\\xff]|\\x5c[\\x00-\\x7f])*\\x22))*\\x40([^\\x00-\\x20\\x22\\x28\\x29\\x2c\\x2e\\x3a-\\x3c\\x3e\\x40\\x5b-\\x5d\\x7f-\\xff]+|\\x5b([^\\x0d\\x5b-\\x5d\\x80-\\xff]|\\x5c[\\x00-\\x7f])*\\x5d)(\\x2e([^\\x00-\\x20\\x22\\x28\\x29\\x2c\\x2e\\x3a-\\x3c\\x3e\\x40\\x5b-\\x5d\\x7f-\\xff]+|\\x5b([^\\x0d\\x5b-\\x5d\\x80-\\xff]|\\x5c[\\x00-\\x7f])*\\x5d))*(\\.\\w{2,})+$/;\n        return r.test(value);\n    }\n\n    /**\n     * Validates whether the input value is a credit card number, with Luhn's Algorithm.\n     */\n    creditcard: ValidationProvider = (value, element, params) => {\n        if (!value) {\n            return true;\n        }\n\n        // (c) jquery-validation, MIT Licensed\n        // https://github.com/jquery-validation/jquery-validation/blob/master/src/additional/creditcard.js\n        // based on https://en.wikipedia.org/wiki/Luhn_algorithm\n\n        // Accept only spaces, digits and dashes\n        if (/[^0-9 \\-]+/.test(value)) {\n            return false;\n        }\n\n        var nCheck = 0,\n            nDigit = 0,\n            bEven = false,\n            n, cDigit;\n\n        value = value.replace(/\\D/g, \"\");\n\n        // Basing min and max length on https://developer.ean.com/general_info/Valid_Credit_Card_Types\n        if (value.length < 13 || value.length > 19) {\n            return false;\n        }\n\n        for (n = value.length - 1; n >= 0; n--) {\n            cDigit = value.charAt(n);\n            nDigit = parseInt(cDigit, 10);\n            if (bEven) {\n                if ((nDigit *= 2) > 9) {\n                    nDigit -= 9;\n                }\n            }\n\n            nCheck += nDigit;\n            bEven = !bEven;\n        }\n\n        return (nCheck % 10) === 0;\n    }\n\n    /**\n     * Validates whether the input value is a URL.\n     */\n    url: ValidationProvider = (value, element, params) => {\n        if (!value) {\n            return true;\n        }\n\n        let lowerCaseValue = value.toLowerCase();\n\n        // Match the logic in `UrlAttribute`\n        return lowerCaseValue.indexOf('http://') > -1\n            || lowerCaseValue.indexOf('https://') > -1\n            || lowerCaseValue.indexOf('ftp://') > -1;\n    }\n\n    /**\n     * Validates whether the input value is a phone number.\n     */\n    phone: ValidationProvider = (value, element, params) => {\n        if (!value) {\n            return true;\n        }\n\n        // Allows whitespace or dash as number separator because some people like to do that...\n        let consecutiveSeparator = /[\\+\\-\\s][\\-\\s]/g;\n        if (consecutiveSeparator.test(value)) {\n            return false;\n        }\n\n        let r = /^\\+?[0-9\\-\\s]+$/;\n        return r.test(value);\n    }\n\n    /**\n     * Asynchronously validates the input value to a JSON GET API endpoint.\n     */\n    remote: ValidationProvider = (value, element, params) => {\n        if (!value) {\n            return true;\n        }\n\n        // params.additionalfields: *.Email,*.Username\n        let fieldSelectors: string[] = (params.additionalfields as string).split(',');\n        let fields: StringKeyValuePair = {};\n\n        for (let fieldSelector of fieldSelectors) {\n            let fieldName = fieldSelector.substr(2);\n            let fieldElement = getRelativeFormElement(element, fieldSelector);\n\n            let hasValue = Boolean(fieldElement && fieldElement.value);\n            if (!hasValue) {\n                continue;\n            }\n\n            if (fieldElement instanceof HTMLInputElement &&\n                (fieldElement.type === 'checkbox' || fieldElement.type === 'radio')) {\n                fields[fieldName] = fieldElement.checked ? fieldElement.value : '';\n            } else {\n                fields[fieldName] = fieldElement.value;\n            }\n        }\n\n        let url: string = params['url'];\n\n        let encodedParams: string[] = [];\n        for (let fieldName in fields) {\n            let encodedParam = encodeURIComponent(fieldName) + '=' + encodeURIComponent(fields[fieldName]);\n            encodedParams.push(encodedParam);\n        }\n        let payload = encodedParams.join('&');\n\n        return new Promise((ok, reject) => {\n            let request = new XMLHttpRequest();\n\n            if (params.type && params.type.toLowerCase() === 'post') {\n                let postData = new FormData();\n                for (let fieldName in fields) {\n                    postData.append(fieldName, fields[fieldName]);\n                }\n                request.open('post', url);\n                request.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');\n                request.send(payload);\n            } else {\n                request.open('get', url + '?' + payload);\n                request.send();\n            }\n\n            request.onload = e => {\n                if (request.status >= 200 && request.status < 300) {\n                    let data = JSON.parse(request.responseText);\n                    ok(data);\n                } else {\n                    reject({\n                        status: request.status,\n                        statusText: request.statusText,\n                        data: request.responseText\n                    });\n                }\n            };\n\n            request.onerror = e => {\n                reject({\n                    status: request.status,\n                    statusText: request.statusText,\n                    data: request.responseText\n                });\n            };\n        });\n    }\n}\n\n/**\n * Configuration for @type {ValidationService}.\n */\nexport interface ValidationServiceOptions {\n    watch: boolean;\n    root: ParentNode;\n    addNoValidate: boolean;\n}\n\n/**\n * Responsible for managing the DOM elements and running the validation providers.\n */\nexport class ValidationService {\n    /**\n     * A key-value collection of loaded validation plugins.\n     */\n    private providers: { [name: string]: ValidationProvider } = {};\n\n    /**\n     * A key-value collection of form UIDs and their <span> elements for displaying validation messages for an input (by DOM name).\n     */\n    private messageFor: { [formUID: string]: { [name: string]: Element[] } } = {};\n\n    /**\n     * A list of managed elements, each having a randomly assigned unique identifier (UID).\n     */\n    private elementUIDs: ElementUID[] = [];\n\n    /**\n     * A key-value collection of UID to Element for quick lookup.\n     */\n    private elementByUID: { [uid: string]: Element } = {};\n\n    /**\n     * A key-value collection of input UIDs for a <form> UID.\n     */\n    private formInputs: { [formUID: string]: string[] } = {};\n\n    /**\n     * A key-value map for input UID to its validator factory.\n     */\n    private validators: { [inputUID: string]: Validator } = {};\n\n    /**\n     * A key-value map for form UID to its trigger element (submit event for <form>).\n     */\n    private formEvents: { [formUID: string]: ValidationEventCallback<SubmitEvent> } = {};\n\n    /**\n     * A key-value map for element UID to its trigger element (input event for <textarea> and <input>, change event for <select>).\n     */\n    private inputEvents: { [inputUID: string]: ValidationEventCallback } = {};\n\n    /**\n     * A key-value map of input UID to its validation error message.\n     */\n    private summary: StringKeyValuePair = {};\n\n    /**\n     * A serialized representation of the validation error message summary rendered to the user.\n     */\n    private renderedSummaryJSON: string;\n\n    /**\n     * In milliseconds, the rate of fire of the input validation.\n     */\n    debounce = 300;\n\n    /**\n     * Allow hidden fields validation\n     */\n    allowHiddenFields = false;\n\n    private logger: Logger;\n    observer?: MutationObserver;\n\n    constructor(logger?: Logger) {\n        this.logger = logger || nullLogger;\n    }\n\n    /**\n     * Registers a new validation plugin of the given name, if not registered yet.\n     * Registered plugin validates inputs with data-val-[name] attribute, used as error message.\n     * @param name\n     * @param callback\n     */\n    addProvider(name: string, callback: ValidationProvider) {\n        if (this.providers[name]) {\n            // First-Come-First-Serve validation plugin design.\n            // Allows developers to override the default MVC Providers by adding custom providers BEFORE bootstrap() is called!\n            return;\n        }\n        this.logger.log(\"Registered provider: %s\", name);\n        this.providers[name] = callback;\n    }\n\n    /**\n     * Registers the default providers for enabling ASP.NET Core MVC client-side validation.\n     */\n    private addMvcProviders() {\n        let mvc = new MvcValidationProviders();\n\n        // [Required]\n        this.addProvider('required', mvc.required);\n        // [StringLength], [MinLength], [MaxLength]\n        this.addProvider('length', mvc.stringLength);\n        this.addProvider('maxlength', mvc.stringLength);\n        this.addProvider('minlength', mvc.stringLength);\n        // [Compare]\n        this.addProvider('equalto', mvc.compare);\n        // [Range]\n        this.addProvider('range', mvc.range);\n        // [RegularExpression]\n        this.addProvider('regex', mvc.regex);\n        // [CreditCard]\n        this.addProvider('creditcard', mvc.creditcard);\n        // [EmailAddress]\n        this.addProvider('email', mvc.email);\n        // [Url]\n        this.addProvider('url', mvc.url);\n        // [Phone]\n        this.addProvider('phone', mvc.phone);\n        // [Remote]\n        this.addProvider('remote', mvc.remote);\n    }\n\n    /**\n     * Scans `root` for all validation message <span> generated by ASP.NET Core MVC, then calls `cb` for each.\n     * @param root The root node to scan\n     * @param cb The callback to invoke with each form and span\n     */\n    private scanMessages(root: ParentNode, cb: (form: HTMLFormElement, span: HTMLElement) => void) {\n        /* If a validation span explicitly declares a form, we group the span with that form. */\n        let validationMessageElements = Array.from(root.querySelectorAll<HTMLElement>('span[form]'));\n        for (let span of validationMessageElements) {\n            let form = document.getElementById(span.getAttribute('form'));\n            if (form instanceof HTMLFormElement) {\n                cb.call(this, form, span);\n            }\n        }\n\n        // Otherwise if a validation message span is inside a form, we group the span with the form it's inside.\n        let forms = Array.from(root.querySelectorAll<HTMLFormElement>('form'));\n        if (root instanceof HTMLFormElement) {\n            // querySelectorAll does not include the root element itself.\n            // we could use 'matches', but that's newer than querySelectorAll so we'll keep it simple and compatible.\n            forms.push(root);\n        }\n        // If root is the descendant of a form, we want to include that form too.\n        const containingForm = (root instanceof Element) ? root.closest('form') : null;\n        if (containingForm) {\n            forms.push(containingForm);\n        }\n\n        for (let form of forms) {\n            let validationMessageElements = Array.from(form.querySelectorAll<HTMLElement>('[data-valmsg-for]'));\n\n            for (let span of validationMessageElements) {\n                cb.call(this, form, span);\n            }\n        }\n    }\n\n    private pushValidationMessageSpan(form: HTMLElement, span: HTMLElement) {\n        let formId = this.getElementUID(form);\n        let formSpans = this.messageFor[formId] ??= {};\n\n        let messageForId = span.getAttribute('data-valmsg-for');\n        if (!messageForId) return;\n\n        let spans = formSpans[messageForId] ??= [];\n        if (spans.indexOf(span) < 0) {\n            spans.push(span);\n        }\n        else {\n            this.logger.log(\"Validation element for '%s' is already tracked\", name, span);\n        }\n    }\n\n    private removeValidationMessageSpan(form: HTMLElement, span: HTMLElement) {\n        let formId = this.getElementUID(form);\n        let formSpans = this.messageFor[formId];\n        if (!formSpans) return;\n\n        let messageForId = span.getAttribute('data-valmsg-for');\n        if (!messageForId) return;\n\n        let spans = formSpans[messageForId];\n        if (!spans) {\n            return;\n        }\n        let index = spans.indexOf(span);\n        if (index >= 0) {\n            spans.splice(index, 1);\n        }\n        else {\n            this.logger.log(\"Validation element for '%s' was already removed\", name, span);\n        }\n    }\n\n    /**\n     * Given attribute map for an HTML input, returns the validation directives to be executed.\n     * @param attributes\n     */\n    parseDirectives(attributes: NamedNodeMap) {\n        let directives: ValidationDirective = {};\n        let validationAtributes: StringKeyValuePair = {};\n\n        let cut = 'data-val-'.length;\n        for (let i = 0; i < attributes.length; i++) {\n            let a = attributes[i];\n            if (a.name.indexOf('data-val-') === 0) {\n                let key = a.name.substr(cut);\n                validationAtributes[key] = a.value;\n            }\n        }\n\n        for (let key in validationAtributes) {\n            if (key.indexOf('-') === -1) {\n                let parameters = Object.keys(validationAtributes).filter(Q => {\n                    return (Q !== key) && (Q.indexOf(key) === 0);\n                });\n\n                let directive: ValidationDirectiveBindings = {\n                    error: validationAtributes[key],\n                    params: {}\n                };\n\n                let pcut = (key + '-').length;\n                for (let i = 0; i < parameters.length; i++) {\n                    let pvalue = validationAtributes[parameters[i]];\n                    let pkey = parameters[i].substr(pcut);\n\n                    directive.params[pkey] = pvalue;\n                }\n\n                directives[key] = directive;\n            }\n        }\n\n        return directives;\n    }\n\n    /**\n     *  Returns an RFC4122 version 4 compliant GUID.\n     */\n    private guid4() {\n        // (c) broofa, MIT Licensed\n        // https://stackoverflow.com/questions/105034/create-guid-uuid-in-javascript/2117523#2117523\n\n        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n            const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);\n            return v.toString(16);\n        });\n    }\n\n    /**\n     * Gets a UID for an DOM element.\n     * @param node\n     */\n    private getElementUID(node: Element) {\n        let x = this.elementUIDs.filter(e => {\n            return e.node === node;\n        })[0];\n\n        if (x) {\n            return x.uid;\n        }\n\n        let uid = this.guid4();\n        this.elementUIDs.push({\n            node: node,\n            uid: uid\n        });\n        this.elementByUID[uid] = node;\n        return uid;\n    }\n\n    /**\n     * Returns a Promise that returns validation result for each and every inputs within the form.\n     * @param formUID\n     */\n    private getFormValidationTask(formUID: string) {\n        let formInputUIDs = this.formInputs[formUID];\n        if (!formInputUIDs || formInputUIDs.length === 0) {\n            return Promise.resolve(true);\n        }\n\n        let formValidators: Validator[] = [];\n\n        for (let inputUID of formInputUIDs) {\n            const validator = this.validators[inputUID];\n            if (validator) {\n                formValidators.push(validator);\n            }\n        }\n\n        let tasks = formValidators.map(factory => factory());\n        return Promise.all(tasks).then(result => result.every(e => e));\n    }\n\n    // Retrieves the validation span for the input.\n    private getMessageFor(input: ValidatableElement) {\n        if (!input.form) {\n            return undefined;\n        }\n        let formId = this.getElementUID(input.form);\n        return this.messageFor[formId]?.[input.name];\n    }\n\n    /**\n     * Fires off validation for elements within the provided form and then calls the callback\n     * @param form The form to validate.\n     * @param callback Receives true or false indicating validity after all validation is complete.\n     * @returns Promise that resolves to true or false indicating validity after all validation is complete.\n     */\n    validateForm = async (form: HTMLFormElement, callback?: ValidatedCallback) => {\n        if (!(form instanceof HTMLFormElement)) {\n            throw new Error('validateForm() can only be called on <form> elements');\n        }\n        let formUID = this.getElementUID(form);\n        let formValidationEvent = this.formEvents[formUID];\n        return !formValidationEvent ||\n            await formValidationEvent(undefined, callback);\n    }\n\n    /**\n     * Fires off validation for the provided element and then calls the callback\n     * @param field The element to validate.\n     * @param callback Receives true or false indicating validity after all validation is complete.\n     * @returns Promise that resolves to true or false indicating validity after all validation is complete\n     */\n    validateField = async (field: ValidatableElement, callback?: ValidatedCallback) => {\n        let fieldUID = this.getElementUID(field);\n        let fieldValidationEvent = this.inputEvents[fieldUID];\n        return !fieldValidationEvent ||\n            await fieldValidationEvent(undefined, callback);\n    }\n\n    /**\n     * Called before validating form submit events.\n     * Default calls `preventDefault()` and `stopImmediatePropagation()`.\n     * @param submitEvent The `SubmitEvent`.\n     */\n    preValidate = (submitEvent: SubmitEvent) => {\n        submitEvent.preventDefault();\n        submitEvent.stopImmediatePropagation();\n    }\n\n    /**\n     * Handler for validated form submit events.\n     * Default calls `submitValidForm(form, submitEvent)` on success\n     * and `focusFirstInvalid(form)` on failure.\n     * @param form The form that has been validated.\n     * @param success The validation result.\n     * @param submitEvent The `SubmitEvent`.\n     */\n    handleValidated = (form: HTMLFormElement, success: boolean, submitEvent?: SubmitEvent) => {\n        if (!(form instanceof HTMLFormElement)) {\n            throw new Error('handleValidated() can only be called on <form> elements');\n        }\n        if (success) {\n            if (submitEvent) {\n                this.submitValidForm(form, submitEvent);\n            }\n        }\n        else {\n            this.focusFirstInvalid(form);\n        }\n    }\n\n    /**\n     * Dispatches a new `SubmitEvent` on the provided form,\n     * then calls `form.submit()` unless `submitEvent` is cancelable\n     * and `preventDefault()` was called by a handler that received the new event.\n     *\n     * This is equivalent to `form.requestSubmit()`, but more flexible.\n     * @param form The validated form to submit\n     * @param submitEvent The `SubmitEvent`.\n     */\n    submitValidForm = (form: HTMLFormElement, submitEvent: SubmitEvent) => {\n        if (!(form instanceof HTMLFormElement)) {\n            throw new Error('submitValidForm() can only be called on <form> elements');\n        }\n        const newEvent = new SubmitEvent('submit', submitEvent);\n        if (form.dispatchEvent(newEvent)) {\n            // Because the submitter is not propagated when calling\n            // form.submit(), we recreate it here.\n            const submitter = submitEvent.submitter;\n            let submitterInput: HTMLInputElement | null = null;\n            const initialFormAction = form.action;\n            if (submitter) {\n                const name = submitter.getAttribute('name');\n                // If name is null, a submit button is not submitted.\n                if (name) {\n                    submitterInput = document.createElement('input');\n                    submitterInput.type = 'hidden';\n                    submitterInput.name = name;\n                    submitterInput.value = submitter.getAttribute('value');\n                    form.appendChild(submitterInput)\n                }\n\n                const formAction = submitter.getAttribute('formaction');\n                if (formAction) {\n                    form.action = formAction;\n                }\n            }\n\n            try {\n                form.submit();\n            } finally {\n                if (submitterInput) {\n                    // Important to clean up the submit input we created.\n                    form.removeChild(submitterInput);\n                }\n                form.action = initialFormAction;\n            }\n        }\n    }\n\n    /**\n     * Focuses the first invalid element within the provided form\n     * @param form\n     */\n    focusFirstInvalid = (form: HTMLFormElement) => {\n        if (!(form instanceof HTMLFormElement)) {\n            throw new Error('focusFirstInvalid() can only be called on <form> elements');\n        }\n        let formUID = this.getElementUID(form);\n        let formInputUIDs = this.formInputs[formUID];\n        let invalidFormInputUID = formInputUIDs?.find(uid => this.summary[uid]);\n\n        if (invalidFormInputUID) {\n            const firstInvalid = this.elementByUID[invalidFormInputUID];\n            if (firstInvalid instanceof HTMLElement) {\n                firstInvalid.focus();\n            }\n        }\n    }\n\n    /**\n     * Returns true if the provided form is currently valid.\n     * The form will be validated unless prevalidate is set to false.\n     * @param form The form to validate.\n     * @param prevalidate Whether the form should be validated before returning.\n     * @param callback A callback that receives true or false indicating validity after all validation is complete. Ignored if prevalidate is false.\n     * @returns The current state of the form. May be inaccurate if any validation is asynchronous (e.g. remote); consider using `callback` instead.\n     */\n    isValid = (form: HTMLFormElement, prevalidate: boolean = true, callback?: ValidatedCallback) => {\n        if (!(form instanceof HTMLFormElement)) {\n            throw new Error('isValid() can only be called on <form> elements');\n        }\n        if (prevalidate) {\n            this.validateForm(form, callback);\n        }\n        let formUID = this.getElementUID(form);\n        let formInputUIDs = this.formInputs[formUID];\n        let formIsInvalid = formInputUIDs?.some(uid => this.summary[uid]) === true;\n        return !formIsInvalid;\n    }\n\n    /**\n     * Returns true if the provided field is currently valid.\n     * The field will be validated unless prevalidate is set to false.\n     * @param field The field to validate.\n     * @param prevalidate Whether the field should be validated before returning.\n     * @param callback A callback that receives true or false indicating validity after all validation is complete. Ignored if prevalidate is false.\n     * @returns The current state of the field. May be inaccurate if any validation is asynchronous (e.g. remote); consider using `callback` instead.\n     */\n    isFieldValid = (field: ValidatableElement, prevalidate: boolean = true, callback?: ValidatedCallback) => {\n        if (prevalidate) {\n            this.validateField(field, callback);\n        }\n\n        let fieldUID = this.getElementUID(field);\n        return this.summary[fieldUID] === undefined;\n    }\n\n    /**\n     * Returns true if the event triggering the form submission indicates we should validate the form.\n     * @param e\n     */\n    private shouldValidate(e?: Event) {\n        // Skip client-side validation if the form has been submitted via a button that has the \"formnovalidate\" attribute.\n        return !(e && e['submitter'] && e['submitter']['formNoValidate']);\n    }\n\n    /**\n     * Tracks a <form> element as parent of an input UID. When the form is submitted, attempts to validate the said input asynchronously.\n     * @param form\n     * @param inputUID\n     */\n    private trackFormInput(form: HTMLFormElement, inputUID: string) {\n        let formUID = this.getElementUID(form);\n        let formInputUIDs = this.formInputs[formUID] ??= [];\n        let add = formInputUIDs.indexOf(inputUID) === -1;\n        if (add) {\n            formInputUIDs.push(inputUID);\n\n            if (this.options.addNoValidate) {\n                this.logger.log('Setting novalidate on form', form);\n                form.setAttribute('novalidate', 'novalidate');\n            }\n            else {\n                this.logger.log('Not setting novalidate on form', form);\n            }\n        }\n        else {\n            this.logger.log(\"Form input for UID '%s' is already tracked\", inputUID);\n        }\n\n        if (this.formEvents[formUID]) {\n            return;\n        }\n\n        let validationTask: Promise<boolean> | null = null;\n        let cb: ValidationEventCallback<SubmitEvent> = (e, callback) => {\n            // Prevent recursion\n            if (validationTask) {\n                return validationTask;\n            }\n\n            if (!this.shouldValidate(e)) {\n                return Promise.resolve(true);\n            }\n\n            validationTask = this.getFormValidationTask(formUID);\n\n            //`preValidate` typically prevents submit before validation\n            if (e) {\n                this.preValidate(e);\n            }\n\n            this.logger.log('Validating', form);\n\n            return validationTask.then(async success => {\n                this.logger.log('Validated (success = %s)', success, form);\n                if (callback) {\n                    callback(success);\n                    return success;\n                }\n\n                const validationEvent = new CustomEvent('validation',\n                    {\n                        detail: { valid: success }\n                    });\n                form.dispatchEvent(validationEvent);\n\n                // Firefox fix: redispatch 'submit' after finished handling this event\n                await new Promise(resolve => setTimeout(resolve, 0));\n                this.handleValidated(form, success, e);\n                return success;\n            }).catch(error => {\n                this.logger.log('Validation error', error);\n                return false;\n            }).finally(() => {\n                validationTask = null;\n            });\n        };\n\n        form.addEventListener('submit', cb);\n\n        const cbReset = (e: Event) => {\n            const formInputUIDs = this.formInputs[formUID];\n\n            for (let inputUID of formInputUIDs) {\n                this.resetField(inputUID);\n            }\n            this.renderSummary();\n        };\n        form.addEventListener('reset', cbReset);\n\n        cb.remove = () => {\n            form.removeEventListener('submit', cb);\n            form.removeEventListener('reset', cbReset);\n        }\n\n        this.formEvents[formUID] = cb;\n    }\n\n    /*\n        Reset the state of a validatable input. This is used when it's enabled or disabled.\n    */\n    reset(input: HTMLElement) {\n        if (this.isDisabled(input)) {\n            this.resetField(this.getElementUID(input));\n        }\n        else {\n            this.scan(input);\n        }\n    }\n\n    private resetField(inputUID: string) {\n        let input = this.elementByUID[inputUID];\n        this.swapClasses(input, '', this.ValidationInputCssClassName);\n        this.swapClasses(input, '', this.ValidationInputValidCssClassName);\n\n        let spans = isValidatable(input) && this.getMessageFor(input);\n        if (spans) {\n            for (let i = 0; i < spans.length; i++) {\n                spans[i].innerHTML = '';\n                this.swapClasses(spans[i], '', this.ValidationMessageCssClassName);\n                this.swapClasses(spans[i], '', this.ValidationMessageValidCssClassName);\n            }\n        }\n\n        delete this.summary[inputUID];\n    }\n\n    private untrackFormInput(form: HTMLFormElement, inputUID: string) {\n        let formUID = this.getElementUID(form);\n        let formInputUIDs = this.formInputs[formUID]\n        if (!formInputUIDs) {\n            return;\n        }\n        let indexToRemove = formInputUIDs.indexOf(inputUID);\n        if (indexToRemove >= 0) {\n            formInputUIDs.splice(indexToRemove, 1);\n\n            if (!formInputUIDs.length) {\n                this.formEvents[formUID]?.remove();\n                delete this.formEvents[formUID];\n                delete this.formInputs[formUID];\n                delete this.messageFor[formUID];\n            }\n        }\n        else {\n            this.logger.log(\"Form input for UID '%s' was already removed\", inputUID);\n        }\n    }\n\n    /**\n     * Adds an input element to be managed and validated by the service.\n     * Triggers a debounced live validation when input value changes.\n     * @param input\n     */\n    addInput(input: ValidatableElement) {\n        let uid = this.getElementUID(input);\n\n        let directives = this.parseDirectives(input.attributes);\n        this.validators[uid] = this.createValidator(input, directives);\n\n        if (input.form) {\n            this.trackFormInput(input.form, uid);\n        }\n\n        if (this.inputEvents[uid]) {\n            return;\n        }\n\n        const cb: ValidationEventCallback = async (event, callback) => {\n            let validate = this.validators[uid];\n            if (!validate) return true;\n            \n            if (\n                !input.dataset.valEvent &&\n                event && event.type === 'input' &&\n                !input.classList.contains(this.ValidationInputCssClassName)\n            ) {\n                // When no data-val-event specified on a field, \"input\" event only takes it back to valid. \"Change\" event can make it invalid.\n                return true;\n            }\n\n            this.logger.log('Validating', { event });\n            try {\n                const success = await validate();\n                callback(success);\n                return success;\n            }\n            catch (error) {\n                this.logger.log('Validation error', error);\n                return false;\n            }\n        };\n\n        let debounceTimeoutID: NodeJS.Timeout | null = null;\n        cb.debounced = (event, callback) => {\n            if (debounceTimeoutID !== null) {\n                clearTimeout(debounceTimeoutID);\n            }\n            debounceTimeoutID = setTimeout(() => {\n                cb(event, callback);\n            }, this.debounce);\n        };\n\n        const defaultEvent = input instanceof HTMLSelectElement ? 'change' : 'input change';\n        const validateEvent = input.dataset.valEvent ?? defaultEvent;\n        const events = validateEvent.split(' ');\n\n        events.forEach((eventName) => {\n          input.addEventListener(eventName, cb.debounced);\n        });\n\n        cb.remove = () => {\n          events.forEach((eventName) => {\n            input.removeEventListener(eventName, cb.debounced);\n          });\n        };\n\n        this.inputEvents[uid] = cb;\n    }\n\n    removeInput(input: ValidatableElement) {\n        let uid = this.getElementUID(input);\n\n        // Clean up event listener\n        let cb = this.inputEvents[uid];\n        if (cb?.remove) {\n            cb.remove();\n            delete cb.remove;\n        }\n\n        delete this.summary[uid];\n        delete this.inputEvents[uid];\n        delete this.validators[uid];\n\n        if (input.form) {\n            this.untrackFormInput(input.form, uid);\n        }\n    }\n\n    /**\n     * Scans `root` for input elements to be validated, then calls `cb` for each.\n     * @param root The root node to scan\n     * @param cb The callback to invoke with each input\n     */\n    private scanInputs(root: ParentNode, cb: (input: ValidatableElement) => void) {\n        let inputs = Array.from(root.querySelectorAll<ValidatableElement>(validatableSelector('[data-val=\"true\"]')));\n\n        // querySelectorAll does not include the root element itself.\n        // we could use 'matches', but that's newer than querySelectorAll so we'll keep it simple and compatible.\n        if (isValidatable(root) && root.getAttribute(\"data-val\") === \"true\") {\n            inputs.push(root);\n        }\n\n        for (let i = 0; i < inputs.length; i++) {\n            let input = inputs[i];\n            cb.call(this, input);\n        }\n    }\n\n    /**\n     * Returns a <ul> element as a validation errors summary.\n     */\n    createSummaryDOM() {\n        if (!Object.keys(this.summary).length) {\n            return null;\n        }\n\n        let renderedMessages = [];\n        let ul = document.createElement('ul');\n        for (let key in this.summary) {\n            // It could be that the message we are rendering belongs to one of a fieldset of multiple inputs that's not selected,\n            // even if another one in the fieldset is. In that case the fieldset is valid, and we shouldn't render the message.\n            const matchingElement = this.elementByUID[key];\n            if (matchingElement instanceof HTMLInputElement) {\n                if (matchingElement.type === \"checkbox\" || matchingElement.type === \"radio\") {\n                    if (matchingElement.className === this.ValidationInputValidCssClassName) {\n                        continue;\n                    }\n                }\n            }\n\n            // With required multiple inputs, such as a checkbox list, we'll have one message per input.\n            // It's one from the inputs that's required, not all, so we should only have one message displayed.\n            if (renderedMessages.indexOf(this.summary[key]) > -1) {\n                continue;\n            }\n\n            let li = document.createElement('li');\n            li.innerHTML = this.summary[key];\n            ul.appendChild(li);\n            renderedMessages.push(this.summary[key]);\n        }\n        return ul;\n    }\n\n    /**\n     * Displays validation summary to ASP.NET Core MVC designated elements, when it actually gets updated.\n     */\n    private renderSummary() {\n        let summaryElements = document.querySelectorAll('[data-valmsg-summary=\"true\"]');\n        if (!summaryElements.length) {\n            return;\n        }\n\n        // Prevents wasteful re-rendering of summary list element with identical items!\n        // Using JSON.stringify for quick and painless deep compare of simple KVP. You need to sort the keys first, tho...\n        let shadow = JSON.stringify(this.summary, Object.keys(this.summary).sort());\n        if (shadow === this.renderedSummaryJSON) {\n            return;\n        }\n\n        // Prevents wasteful re-rendering of summary list element with identical items!\n        this.renderedSummaryJSON = shadow;\n        let ul = this.createSummaryDOM();\n\n        for (let i = 0; i < summaryElements.length; i++) {\n            let e = summaryElements[i];\n\n            // Remove existing list elements, but keep the summary's message.\n            let listElements = e.querySelectorAll(\"ul\");\n            for (let j = 0; j < listElements.length; j++) {\n                listElements[j].remove();\n            }\n\n            // Style the summary element as valid/invalid depending on whether there are any messages to display.\n            if (ul && ul.hasChildNodes()) {\n                this.swapClasses(e,\n                    this.ValidationSummaryCssClassName,\n                    this.ValidationSummaryValidCssClassName)\n                e.appendChild(ul.cloneNode(true));\n            } else {\n                this.swapClasses(e,\n                    this.ValidationSummaryValidCssClassName,\n                    this.ValidationSummaryCssClassName)\n            }\n        }\n    }\n\n    /**\n     * Adds an error message to an input element, which also updates the validation message elements and validation summary elements.\n     * @param input\n     * @param message\n     */\n    addError(input: ValidatableElement, message: string) {\n        let spans = this.getMessageFor(input);\n        if (spans) {\n            for (let i = 0; i < spans.length; i++) {\n                const span = spans[i];\n                spans[i].innerHTML = message;\n                this.swapClasses(spans[i],\n                    this.ValidationMessageCssClassName,\n                    this.ValidationMessageValidCssClassName);\n            }\n        }\n\n        this.swapClasses(input,\n            this.ValidationInputCssClassName,\n            this.ValidationInputValidCssClassName);\n\n        if (input.form) {\n            // Adding an error to one input should also add it to others with the same name (i.e. for radio button and checkbox lists).\n            const inputs = input.form.querySelectorAll(validatableSelector(`[name=\"${input.name}\"]`));\n            for (let i = 0; i < inputs.length; i++) {\n                this.swapClasses(inputs[i],\n                    this.ValidationInputCssClassName,\n                    this.ValidationInputValidCssClassName);\n\n                let uid = this.getElementUID(inputs[i]);\n                this.summary[uid] = message;\n            }\n        }\n\n        this.renderSummary();\n    }\n\n    /**\n     * Removes an error message from an input element, which also updates the validation message elements and validation summary elements.\n     * @param input\n     */\n    removeError(input: ValidatableElement) {\n        let spans = this.getMessageFor(input);\n        if (spans) {\n            for (let i = 0; i < spans.length; i++) {\n                spans[i].innerHTML = '';\n                this.swapClasses(spans[i],\n                    this.ValidationMessageValidCssClassName,\n                    this.ValidationMessageCssClassName);\n            }\n        }\n\n        this.swapClasses(input,\n            this.ValidationInputValidCssClassName,\n            this.ValidationInputCssClassName);\n\n        // Removing an error from one input should also remove it from others with the same name (i.e. for radio button and checkbox lists).\n        if (input.form) {\n            const inputs = input.form.querySelectorAll(validatableSelector(`[name=\"${input.name}\"]`));\n            for (let i = 0; i < inputs.length; i++) {\n                this.swapClasses(inputs[i],\n                    this.ValidationInputValidCssClassName,\n                    this.ValidationInputCssClassName);\n\n                let uid = this.getElementUID(inputs[i]);\n                delete this.summary[uid];\n            }\n        }\n\n        this.renderSummary();\n    }\n\n    /**\n     * Returns a validation Promise factory for an input element, using given validation directives.\n     * @param input\n     * @param directives\n     */\n    createValidator(input: ValidatableElement, directives: ValidationDirective) {\n        return async () => {\n            // only validate visible and enabled fields\n            if (!this.isHidden(input) && !this.isDisabled(input)) {\n                for (let key in directives) {\n                    let directive = directives[key];\n                    let provider = this.providers[key];\n\n                    if (!provider) {\n                        this.logger.log('aspnet-validation provider not implemented: %s', key);\n                        continue;\n                    }\n                    this.logger.log(\"Running %s validator on element\", key, input);\n\n                    let result = provider(input.value, input, directive.params);\n                    let valid = false;\n                    let error = directive.error;\n\n                    if (typeof result === 'boolean') {\n                        valid = result;\n                    } else if (typeof result === 'string') {\n                        valid = false;\n                        error = result;\n                    } else {\n                        let resolution = await result;\n                        if (typeof resolution === 'boolean') {\n                            valid = resolution;\n                        } else {\n                            valid = false;\n                            error = resolution;\n                        }\n                    }\n\n                    if (!valid) {\n                        this.addError(input, error);\n                        return false;\n                    }\n                }\n            }\n\n            this.removeError(input);\n            return true;\n        };\n    }\n\n    /**\n     * Checks if the provided input is hidden from the browser\n     * @param input\n     * @returns\n     */\n    private isHidden(input: HTMLElement) {\n        return !(this.allowHiddenFields || input.offsetWidth || input.offsetHeight || input.getClientRects().length);\n    }\n\n    /**\n     * Checks if the provided input is disabled\n     * @param input\n     * @returns\n     */\n    private isDisabled(input: Element) {\n        // If the input is validatable, we check the `disabled` property.\n        // Otherwise the `disabled` property is undefined and this returns false.\n        return (input as ValidatableElement).disabled;\n    }\n\n    /**\n     * Adds addClass and removes removeClass\n     * @param element Element to modify\n     * @param addClass Class to add\n     * @param removeClass Class to remove\n     */\n    private swapClasses(element: Element, addClass: string, removeClass: string) {\n        if (addClass && !this.isDisabled(element) && !element.classList.contains(addClass)) {\n            element.classList.add(addClass);\n        }\n        if (element.classList.contains(removeClass)) {\n            element.classList.remove(removeClass);\n        }\n    }\n\n    /**\n     * Options for this instance of @type {ValidationService}.\n     */\n    private options: ValidationServiceOptions = {\n        root: document.body,\n        watch: false,\n        addNoValidate: true,\n    }\n\n    /**\n     * Load default validation providers and scans the entire document when ready.\n     * @param options.watch If set to true, a MutationObserver will be used to continuously watch for new elements that provide validation directives.\n     * @param options.addNoValidate If set to true (the default), a novalidate attribute will be added to the containing form in validate elements.\n     */\n    bootstrap(options?: Partial<ValidationServiceOptions>) {\n        Object.assign(this.options, options);\n\n        this.addMvcProviders();\n        let document = window.document;\n        const root = this.options.root;\n        const init = () => {\n            this.scan(root);\n\n            // Watch for further mutations after initial scan\n            if (this.options.watch) {\n                this.watch(root);\n            }\n        }\n\n        // If the document is done loading, scan it now.\n        if (document.readyState === 'complete' || document.readyState === 'interactive') {\n            init();\n        }\n        else {\n            // Otherwise wait until the document is done loading.\n            document.addEventListener('DOMContentLoaded', init);\n        }\n    }\n\n    /**\n     * Scans the root element for any validation directives and attaches behavior to them.\n     * @param root The root node to scan; if not provided, `options.root` (default: `document.body`) will be scanned\n     */\n    scan(root?: ParentNode) {\n        root ??= this.options.root;\n        this.logger.log('Scanning', root);\n        this.scanMessages(root, this.pushValidationMessageSpan);\n        this.scanInputs(root, this.addInput);\n    }\n\n    /**\n     * Scans the root element for any validation directives and removes behavior from them.\n     * @param root The root node to scan; if not provided, `options.root` (default: `document.body`) will be scanned\n     */\n    remove(root?: ParentNode) {\n        root ??= this.options.root;\n        this.logger.log('Removing', root);\n        this.scanMessages(root, this.removeValidationMessageSpan);\n        this.scanInputs(root, this.removeInput);\n    }\n\n    /**\n     * Watches the provided root element for mutations, and scans for new validation directives to attach behavior.\n     * @param root The root node to watch; if not provided, `options.root` (default: `document.body`) will be watched\n     */\n    watch(root?: ParentNode) {\n        root ??= this.options.root;\n        this.observer = new MutationObserver(mutations => {\n            mutations.forEach(mutation => {\n                this.observed(mutation);\n            });\n        });\n        this.observer.observe(root, {\n            attributes: true,\n            childList: true,\n            subtree: true\n        });\n        this.logger.log(\"Watching for mutations\");\n    }\n\n    private observed(mutation: MutationRecord) {\n        if (mutation.type === 'childList') {\n            for (let i = 0; i < mutation.addedNodes.length; i++) {\n                let node = mutation.addedNodes[i];\n                this.logger.log('Added node', node);\n                if (node instanceof HTMLElement) {\n                    this.scan(node);\n                }\n            }\n            for (let i = 0; i < mutation.removedNodes.length; i++) {\n                let node = mutation.removedNodes[i];\n                this.logger.log('Removed node', node);\n                if (node instanceof HTMLElement) {\n                    this.remove(node);\n                }\n            }\n        } else if (mutation.type === 'attributes') {\n            if (mutation.target instanceof HTMLElement) {\n                const attributeName = mutation.attributeName;\n\n                // Special case for disabled.\n                if (attributeName === 'disabled') {\n                    const target = mutation.target as ValidatableElement;\n                    this.reset(target);\n                }\n                else {\n                    const oldValue = mutation.oldValue ?? '';\n                    const newValue = mutation.target.attributes[mutation.attributeName]?.value ?? '';\n                    this.logger.log(\"Attribute '%s' changed from '%s' to '%s'\",\n                        mutation.attributeName,\n                        oldValue,\n                        newValue,\n                        mutation.target);\n                    if (oldValue !== newValue) {\n                        this.scan(mutation.target);\n                    }\n                }\n            }\n        }\n    }\n\n    /**\n     * Override CSS class name for input validation error. Default: 'input-validation-error'\n     */\n    ValidationInputCssClassName = \"input-validation-error\";\n\n    /**\n     * Override CSS class name for valid input validation. Default: 'input-validation-valid'\n     */\n    ValidationInputValidCssClassName = \"input-validation-valid\";\n\n    /**\n     * Override CSS class name for field validation error. Default: 'field-validation-error'\n     */\n    ValidationMessageCssClassName = \"field-validation-error\";\n\n    /**\n     * Override CSS class name for valid field validation. Default: 'field-validation-valid'\n     */\n    ValidationMessageValidCssClassName = \"field-validation-valid\";\n\n    /**\n     * Override CSS class name for validation summary error. Default: 'validation-summary-errors'\n     */\n    ValidationSummaryCssClassName = \"validation-summary-errors\";\n\n    /**\n     * Override CSS class name for valid validation summary. Default: 'validation-summary-valid'\n     */\n    ValidationSummaryValidCssClassName = \"validation-summary-valid\";\n}\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "nullLogger", "warn", "globalThis", "console", "log", "_", "isValidatable", "element", "HTMLInputElement", "HTMLSelectElement", "HTMLTextAreaElement", "validatableElementTypes", "validatableSelector", "selector", "map", "t", "join", "getRelativeFormElement", "elementName", "name", "<PERSON><PERSON><PERSON>", "substring", "dotLocation", "lastIndexOf", "relativeElementName", "relativeElement", "document", "getElementsByName", "form", "querySelector", "required", "params", "elementType", "type", "toLowerCase", "Array", "from", "querySelectorAll", "checked", "checkboxHiddenInput", "Boolean", "trim", "stringLength", "min", "parseInt", "length", "max", "compare", "other", "otherElement", "range", "val", "parseFloat", "isNaN", "regex", "pattern", "RegExp", "test", "email", "creditcard", "n", "cDigit", "nCheck", "nDigit", "b<PERSON><PERSON>", "replace", "char<PERSON>t", "url", "lowerCaseValue", "indexOf", "phone", "remote", "fieldSelectors", "additionalfields", "split", "fields", "fieldSelector", "fieldName", "substr", "fieldElement", "encodedParams", "encodedParam", "encodeURIComponent", "push", "payload", "Promise", "ok", "reject", "request", "XMLHttpRequest", "postData", "FormData", "append", "open", "setRequestHeader", "send", "onload", "e", "status", "data", "JSON", "parse", "responseText", "statusText", "onerror", "logger", "providers", "messageFor", "elementUIDs", "elementByUID", "formInputs", "validators", "formEvents", "inputEvents", "summary", "debounce", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validateForm", "callback", "HTMLFormElement", "Error", "formUID", "this", "getElementUID", "formValidationEvent", "undefined", "validateField", "field", "fieldUID", "fieldValidationEvent", "preValidate", "submitEvent", "preventDefault", "stopImmediatePropagation", "handleValidated", "success", "submitValidForm", "focusFirstInvalid", "newEvent", "SubmitEvent", "dispatchEvent", "submitter", "submitterInput", "initialFormAction", "action", "getAttribute", "createElement", "append<PERSON><PERSON><PERSON>", "formAction", "submit", "<PERSON><PERSON><PERSON><PERSON>", "formInputUIDs", "invalidFormInputUID", "find", "uid", "firstInvalid", "HTMLElement", "focus", "<PERSON><PERSON><PERSON><PERSON>", "prevalidate", "some", "isField<PERSON><PERSON>d", "options", "body", "watch", "addNoValidate", "ValidationInputCssClassName", "ValidationInputValidCssClassName", "ValidationMessageCssClassName", "ValidationMessageValidCssClassName", "ValidationSummaryCssClassName", "ValidationSummaryValidCssClassName", "addProvider", "addMvcProviders", "mvc", "MvcValidationProviders", "scanMessages", "cb", "span", "getElementById", "forms", "containingForm", "Element", "closest", "pushValidationMessageSpan", "formId", "formSpans", "messageForId", "spans", "removeValidationMessageSpan", "index", "splice", "parseDirectives", "attributes", "directives", "validationAtributes", "i", "a", "parameters", "keys", "filter", "Q", "directive", "error", "pcut", "pvalue", "pkey", "guid4", "c", "r", "Math", "random", "toString", "node", "x", "getFormValidationTask", "resolve", "formValidators", "inputUID", "validator", "tasks", "all", "then", "result", "every", "getMessageFor", "input", "shouldValidate", "trackFormInput", "setAttribute", "validationTask", "validationEvent", "CustomEvent", "detail", "valid", "setTimeout", "catch", "finally", "addEventListener", "cb<PERSON><PERSON><PERSON>", "reset<PERSON>ield", "renderSummary", "remove", "removeEventListener", "reset", "isDisabled", "scan", "swapClasses", "innerHTML", "untrackFormInput", "indexToRemove", "addInput", "createValidator", "event", "validate", "dataset", "valEvent", "classList", "contains", "debounceTimeoutID", "debounced", "clearTimeout", "defaultEvent", "events", "for<PERSON>ach", "eventName", "removeInput", "scanInputs", "inputs", "createSummaryDOM", "renderedMessages", "ul", "matchingElement", "className", "li", "summaryElements", "shadow", "stringify", "sort", "renderedSummaryJSON", "listElements", "j", "hasChildNodes", "cloneNode", "addError", "message", "removeError", "isHidden", "provider", "resolution", "offsetWidth", "offsetHeight", "getClientRects", "disabled", "addClass", "removeClass", "add", "bootstrap", "assign", "window", "init", "readyState", "observer", "MutationObserver", "mutations", "mutation", "observed", "observe", "childList", "subtree", "addedNodes", "removedNodes", "target", "attributeName", "oldValue", "newValue"], "sourceRoot": ""}