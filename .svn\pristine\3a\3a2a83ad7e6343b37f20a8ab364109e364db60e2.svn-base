﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class FormTypeCategoryRepositoryMocks
{
    public static Mock<IFormTypeCategoryRepository> GetFormTypeCategoryByFormTypeIdRepository(FormTypeCategory formTypeCategories)
    {
        var formTypeCategoryRepository = new Mock<IFormTypeCategoryRepository>();

        formTypeCategoryRepository.Setup(repo => repo.GetFormTypeCategoryByFormTypeId(It.IsAny<string>())).ReturnsAsync(formTypeCategories);

        return formTypeCategoryRepository;
    }

    public static Mock<IFormTypeCategoryRepository> GetFormTypeCategoryEmptyRepository()
    {
        var mockFormTypeRepository = new Mock<IFormTypeCategoryRepository>();

        mockFormTypeRepository.Setup(repo => repo.GetFormTypeCategoryByFormTypeId(It.IsAny<string>())).ReturnsAsync(new FormTypeCategory());

        mockFormTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<FormTypeCategory>());

        return mockFormTypeRepository;
    }

    public static Mock<IFormTypeCategoryRepository> GetPaginatedFormTypeCategoryRepository(List<FormTypeCategory> formTypeCategories)
    {
        var formTypeCategoryRepository = new Mock<IFormTypeCategoryRepository>();

        var queryableFormType = formTypeCategories.BuildMock();

        formTypeCategoryRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableFormType);

        return formTypeCategoryRepository;
    }
}