﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IWorkflowProfileInfoRepository : IRepository<WorkflowProfileInfo>
{
    Task<List<WorkflowProfileInfo>> GetWorkflowProfileInfoNames();
    Task<bool> IsWorkflowProfileInfoNameExist(string name, string id);
    Task<WorkflowProfileInfo> GetWorkflowProfileInfoByProfileId(string profileId);
    Task<bool> IsWorkflowNameAndProfileNameUnique(string workflowId, string profileId);
    Task<List<WorkflowProfileInfo>> GetWorkflowProfileInfoByProfileIdAsync(string profileId);
    Task<WorkflowProfileInfo> GetProfileIdAttachByWorkflowId(string workflowId);
    Task<bool> IsWorkflowIdUnique(string workflowId);
    Task<List<WorkflowProfileInfo>> GetProfileIdAttachByInfraObjectId(string infraObjectId);
    Task<List<WorkflowProfileInfo>> GetWorkflowProfileByInfraId(string infraId);
    Task<List<WorkflowProfileInfo>> GetWorkflowProfileByWorkflowId(string workflowId);
    Task<WorkflowProfileInfo> GetWorkflowProfileFilterByProfileId(string profileId);

    Task<WorkflowProfileInfo> GetWorkflowProfileInfoByWorkflowIdAndInfraObjectId(string workflowId,
        string infraObjectId);

    Task<List<WorkflowProfileInfo>> GetWorkflowProfileInfoByProfileIds(List<string> profileIds);
    Task<IReadOnlyList<WorkflowProfileInfo>> ConfiguredProfileInfo();
}