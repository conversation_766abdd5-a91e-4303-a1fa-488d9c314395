﻿namespace ContinuityPatrol.Domain.Entities;

public class Replication : AuditableEntity
{
    public string Name { get; set; }

    public string Type { get; set; }

    public string TypeId { get; set; }

    public string CompanyId { get; set; }

    public string SiteId { get; set; }

    public string SiteName { get; set; }

    [Column(TypeName = "NCLOB")] public string Properties { get; set; }

    public string LicenseId { get; set; }

    public string LicenseKey { get; set; }

    public string BusinessServiceId { get; set; }

    public string BusinessServiceName { get; set; }
    public string FormVersion { get; set; }
}