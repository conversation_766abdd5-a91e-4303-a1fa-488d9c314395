﻿using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.ReplicationJobModel;

namespace ContinuityPatrol.Application.UnitTests.Features.ReplicationJob.Queries
{
    public class GetReplicationJobListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IReplicationJobRepository> _mockReplicationJobRepository;
        private readonly GetReplicationJobListQueryHandler _handler;

        public GetReplicationJobListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockReplicationJobRepository = new Mock<IReplicationJobRepository>();
            _handler = new GetReplicationJobListQueryHandler(_mockMapper.Object, _mockReplicationJobRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnReplicationJobList_WhenJobsExist()
        {
            var replicationJobs = new List<Domain.Entities.ReplicationJob>
            {
                new Domain.Entities.ReplicationJob { Id = 1, IsActive = true },
                new Domain.Entities.ReplicationJob { Id = 2, IsActive = true }
            };

            var expectedVmList = replicationJobs.Select(job => new ReplicationJobListVm { Id = Guid.NewGuid().ToString() }).ToList();

            _mockReplicationJobRepository
                .Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(replicationJobs);

            _mockMapper
                .Setup(mapper => mapper.Map<List<ReplicationJobListVm>>(replicationJobs))
                .Returns(expectedVmList);

            var query = new GetReplicationJobListQuery();

            var result = await _handler.Handle(query, CancellationToken.None);

                //  result.Should().BeEquivalentTo(expectedVmList);
            _mockReplicationJobRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<ReplicationJobListVm>>(replicationJobs), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoJobsExist()
        {
            var replicationJobs = new List<Domain.Entities.ReplicationJob>();

            _mockReplicationJobRepository
                .Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(replicationJobs);

            _mockMapper
                .Setup(mapper => mapper.Map<List<ReplicationJobListVm>>(replicationJobs))
                .Returns(new List<ReplicationJobListVm>());

            var query = new GetReplicationJobListQuery();

            var result = await _handler.Handle(query, CancellationToken.None);

           // result.Should().BeEmpty();
            _mockReplicationJobRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<ReplicationJobListVm>>(replicationJobs), Times.Once);
        }
    }
}
