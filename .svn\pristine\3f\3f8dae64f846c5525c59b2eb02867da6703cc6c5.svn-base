﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.SiteModel.SiteViewModel

<div class="modal-dialog modal-dialog-centered modal-lg">
    <form class="modal-content" id="CreateForm" method="post" enctype="multipart/form-data" autocomplete="off">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-web"></i><span>Site Configuration</span></h6>
            <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">            
            <div class="form-group">
                <div class="form-label">Name</div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-name"></i></span>
                    <input type="text" maxlength="100" id="siteName" class="form-control" placeholder="Enter Site Name" />
                </div>
                <span id="siteNameError"></span>
            </div>
            <div class="form-group">
                <div class="form-label">Location</div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-location"></i></span>
                    <select id="selectLocation" class="form-select-modal" aria-label="Default select example" data-live-search="true" data-placeholder="Select Location">
                        <option value="" disabled selected>Select Location</option>
                        @foreach (var siteLocation in Model.SiteLocations)
                        {
                            <option id="@siteLocation.Id" value="@siteLocation.City" lng="@siteLocation.Lng" lat="@siteLocation.Lat">@siteLocation.City</option>                
                        }
                    </select>
                </div>            
               <span id="siteLocError"></span>
            </div>
            <div class="form-group">
                <div class="form-label">Company Name</div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-company"></i></span>
                    <select id="companyNameDropdown" class="form-select-modal" aria-label="Default select example" data-live-search="true" data-placeholder="Select Company Name">
                        <option value="" disabled selected>Select Company</option>
                        @foreach (var company in Model.Companies)
                        {
                            <option id="@company.Value" value="@company.Text">@company.Text</option>
                        }
                    </select>
                </div>
                <span id="siteCompError"></span>
            </div>
            <div class="form-group">
                <div class="form-label">Platform Type</div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-platform"></i></span>
                    <select id="platformSelect" class="form-select-modal" data-live-search="true" data-placeholder="Select Platform Type">
                        <option value="" disabled selected>Select Platform Type</option>
                        <option value="Physical">Physical</option>
                        <option value="Virtual">Virtual</option>
                        <option value="Cloud">Cloud</option>
                    </select>
                </div>
                <span id="sitePlatformError"></span>
            </div>
            <div class="form-group">
                <div class="form-label">Type</div>
                <div class="d-flex gap-4 flex-wrap" id="siteTypeCustom">
                    @{
                        var i = 1;
                    }
                    @foreach (var siteType in Model.SiteTypes)
                    {                 
                        <div class="position-relative">
                            <input class="btn-check siteTypeRadio" type="radio" name="type" value="@siteType.Type" data-typeName="@siteType.Id" id="option@(i)" data-toggle="button" autocomplete="off" />
                            <label class="site_type btn border-secondary-subtle" for="option@(i)">
                                <i class="@siteType.Icon fs-1  @siteType.Category.Replace(" ","")" id="icon@(i)"></i><br />
                            </label>
                            <div class="text-center mt-2 d-block text-truncate" style="max-width:80px" title="@siteType.Type"> @siteType.Type</div>
                                
                        </div>
                        i++;
                    }                        
                </div>                
                <span id="sitetypeError"></span>
            </div>
            <div class="form-group" id="drsitefeild">
                <div class="form-label">
                    Site Type
                </div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-list-drsite"></i></span>
                    <select class="form-select-modal" data-live-search="true" id="drSiteTypeSelect" data-placeholder="Select Site Type">
                        <option value="" disabled selected>Select  DR Site Type</option>
                        <option value="Hot">Hot</option>
                        <option value="Warm">Warm</option>
                        <option value="Cold">Cold</option>
                    </select>
                </div>
                 <input type="hidden" id="dataTemperature" /> 
                <span id="drSiteTypeError"></span>
            </div>
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary ModalFooter-Note-Text"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div class="gap-2 d-flex">
                <button type="button" id="cancelFunction"  class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" id="siteSaveButton">Save</button>
            </div>
        </div>
    </form>
</div>
@section Scripts
    {
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}
