﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowRepositoryMocks
{
    public static Mock<IWorkflowRepository> CreateWorkflowRepository(List<Workflow> workflows)
    {
        var workflowRepository = new Mock<IWorkflowRepository>();
        workflowRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflows);
        workflowRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflows.SingleOrDefault(x => x.ReferenceId == i));
        workflowRepository.Setup(repo => repo.AddAsync(It.IsAny<Workflow>())).ReturnsAsync(
            (Workflow workflow) =>
            {
                workflow.Id = new Fixture().Create<int>();
                workflow.ReferenceId = new Fixture().Create<Guid>().ToString();
                workflows.Add(workflow);
                return workflow;
            });

        return workflowRepository;
    }

    public static Mock<IWorkflowRepository> UpdateWorkflowRepository(List<Workflow> workflows)
    {
        var workflowRepository = new Mock<IWorkflowRepository>();

        workflowRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflows);

        workflowRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflows.SingleOrDefault(x => x.ReferenceId == i));

        workflowRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Workflow>())).ReturnsAsync((Workflow workflow) =>
        {
            var index = workflows.FindIndex(item => item.ReferenceId == workflow.ReferenceId);
            workflows[index] = workflow;
            return workflow;
        });

        return workflowRepository;
    }

    public static Mock<IWorkflowRepository> DeleteWorkflowRepository(List<Workflow> workflows)
    {
        var workflowRepository = new Mock<IWorkflowRepository>();
        workflowRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflows);

        workflowRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflows.SingleOrDefault(x => x.ReferenceId == i));

        workflowRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Workflow>())).ReturnsAsync((Workflow workflow) =>
        {
            var index = workflows.FindIndex(item => item.ReferenceId == workflow.ReferenceId);
            workflow.IsActive = false;
            workflows[index] = workflow;

            return workflow;
        });

        return workflowRepository;
    }

    public static Mock<IWorkflowRepository> GetWorkflowRepository(List<Workflow> workflows)
    {
        var workflowRepository = new Mock<IWorkflowRepository>();

        workflowRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflows);

        workflowRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflows);

        workflowRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflows.SingleOrDefault(x => x.ReferenceId == i));

        return workflowRepository;
    }

    public static Mock<IWorkflowRepository> GetWorkflowNameUniqueRepository(List<Workflow> workflows)
    {
        var workflowRepository = new Mock<IWorkflowRepository>();

        workflowRepository.Setup(repo => repo.IsWorkflowNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => workflows.Exists(x => x.Name == i && x.ReferenceId == j));

        return workflowRepository;
    }

    public static Mock<IWorkflowRepository> GetWorkflowEmptyRepository()
    {
        var mockWorkflowRepository = new Mock<IWorkflowRepository>();

        mockWorkflowRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Workflow>());

        mockWorkflowRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Workflow>());

        mockWorkflowRepository.Setup(repo => repo.GetWorkflowPermissions(It.IsAny<string>())).ReturnsAsync(new List<string>());

        return mockWorkflowRepository;
    }

    public static Mock<IWorkflowRepository> GetWorkflowNamesRepository(List<Workflow> workflows)
    {
        var workflowRepository = new Mock<IWorkflowRepository>();

        workflowRepository.Setup(repo => repo.GetWorkflowNames()).ReturnsAsync(workflows);

        return workflowRepository;
    }

    public static Mock<IWorkflowRepository> GetPaginatedWorkflowRepository(List<Workflow> workflows)
    {
        var workflowRepository = new Mock<IWorkflowRepository>();

        var queryableNode = workflows.BuildMock();

        workflowRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableNode);

        return workflowRepository;
    }

    public static Mock<IWorkflowRepository> GetWorkflowByInfraObjectIdRepository(List<WorkflowInfraObject> workflowInfraObjects, List<Workflow> workflows)
    {
        var workflowRepository = new Mock<IWorkflowRepository>();

        //workflowRepository.Setup(repo => repo.GetWorkflowFromInfraObjectId(It.IsAny<string>())).ReturnsAsync((string i) => workflowInfraObjects.Where(x => x.InfraObjectId == i && x.IsActive).ToList());

        //workflowRepository.Setup(repo => repo.GetWorkflowFromId(It.IsAny<string>())).ReturnsAsync((string i) => workflows.Where(x => x.ReferenceId == i && x.IsActive).ToList());

        return workflowRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateWorkflowEventRepository(List<UserActivity> userActivities)
    {
        var workflowEventRepository = new Mock<IUserActivityRepository>();

        workflowEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return workflowEventRepository;
    }
}