﻿namespace ContinuityPatrol.Application.Features.OracleRACMonitorStatus.Commands.Create;

public class
    CreateOracleRACStatusCommandHandler : IRequestHandler<CreateOracleRACStatusCommand, CreateOracleRACStatusResponse>
{
    private readonly IMapper _mapper;
    private readonly IOracleRacMonitorStatusRepository _oracleRacMonitorStatusRepository;

    public CreateOracleRACStatusCommandHandler(IMapper mapper,
        IOracleRacMonitorStatusRepository oracleRacMonitorStatusRepository)
    {
        _mapper = mapper;
        _oracleRacMonitorStatusRepository = oracleRacMonitorStatusRepository;
    }

    public async Task<CreateOracleRACStatusResponse> Handle(CreateOracleRACStatusCommand request,
        CancellationToken cancellationToken)
    {
        var oracleRACStatus = _mapper.Map<Domain.Entities.OracleRACMonitorStatus>(request);

        oracleRACStatus = await _oracleRacMonitorStatusRepository.AddAsync(oracleRACStatus);

        var response = new CreateOracleRACStatusResponse
        {
            Message = Message.Create(nameof(Domain.Entities.OracleRACMonitorStatus), oracleRACStatus.ReferenceId),
            Id = oracleRACStatus.ReferenceId
        };

        return response;
    }
}