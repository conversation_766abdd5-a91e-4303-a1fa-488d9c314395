﻿namespace ContinuityPatrol.Application.Features.RiskMitigation.Queries.GetDetail;

public class GetRiskMitigationDetailQueryHandler : IRequestHandler<GetRiskMitigationDetailQuery, RiskMitigationDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IRiskMitigationRepository _riskMitigationRepository;

    public GetRiskMitigationDetailQueryHandler(IMapper mapper, IRiskMitigationRepository riskMitigationRepository)
    {
        _mapper = mapper;
        _riskMitigationRepository = riskMitigationRepository;
    }

    public async Task<RiskMitigationDetailVm> Handle(GetRiskMitigationDetailQuery request,
        CancellationToken cancellationToken)
    {
        var riskMitigation = await _riskMitigationRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(riskMitigation, nameof(Domain.Entities.RiskMitigation),
            new NotFoundException(nameof(Domain.Entities.RiskMitigation), request.Id));

        var riskMitigationDetailDto = _mapper.Map<RiskMitigationDetailVm>(riskMitigation);

        return riskMitigationDetailDto ??
               throw new NotFoundException(nameof(Domain.Entities.RiskMitigation), request.Id);
    }
}