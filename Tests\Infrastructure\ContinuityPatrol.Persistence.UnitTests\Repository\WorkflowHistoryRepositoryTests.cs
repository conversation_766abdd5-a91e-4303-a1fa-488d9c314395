using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Xunit;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowHistoryRepositoryTests : IClassFixture<WorkflowHistoryFixture>
    {
        private readonly WorkflowHistoryFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowHistoryRepository _repositoryParent;
        private readonly WorkflowHistoryRepository _repositoryIsNotParent;

        public WorkflowHistoryRepositoryTests(WorkflowHistoryFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _repositoryParent = new WorkflowHistoryRepository(_dbContext, DbContextFactory.GetMockUserService());
            _repositoryIsNotParent = new WorkflowHistoryRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
        }

        [Fact]
        public async Task ListAllAsync_ReturnsAll_WhenIsParent()
        {
            await _dbContext.WorkFlowHistories.AddRangeAsync(_fixture.WorkflowHistoryList);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.ListAllAsync();

            Assert.Equal(_fixture.WorkflowHistoryList.Count, result.Count);
        }

        [Fact]
        public async Task ListAllAsync_ReturnsFiltered_WhenNotParent()
        {
            var entity = _fixture.WorkflowHistoryDto;
            entity.CompanyId = "CHILD_COMPANY_123";
            await _dbContext.WorkFlowHistories.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryIsNotParent.ListAllAsync();

            Assert.All(result, x => Assert.Equal("CHILD_COMPANY_123", x.CompanyId));
        }

        [Fact]
        public async Task GetByReferenceIdAsync_ReturnsEntity_WhenIsParent()
        {
            var entity = _fixture.WorkflowHistoryDto;
            await _dbContext.WorkFlowHistories.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.GetByReferenceIdAsync(entity.ReferenceId);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.ReferenceId);
        }

        [Fact]
        public async Task GetByReferenceIdAsync_ReturnsEntity_WhenNotParent()
        {
            var entity = _fixture.WorkflowHistoryDto;
            entity.CompanyId = "ChHILD_COMPANY_123";
            await _dbContext.WorkFlowHistories.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryIsNotParent.GetByReferenceIdAsync(entity.ReferenceId);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.ReferenceId);
        }

        [Fact]
        public void GetPaginatedQuery_ReturnsActiveOrdered_WhenIsParent()
        {
            _dbContext.WorkFlowHistories.AddRange(_fixture.WorkflowHistoryPaginationList);
            _dbContext.SaveChanges();

            var result = _repositoryParent.GetPaginatedQuery().ToList();

            Assert.All(result, x => Assert.True(x.IsActive));
            Assert.True(result.SequenceEqual(result.OrderByDescending(x => x.Id)));
        }

        [Fact]
        public void GetPaginatedQuery_ReturnsActiveOrdered_WhenNotParent()
        {
            _dbContext.WorkFlowHistories.AddRange(_fixture.WorkflowHistoryPaginationList);
            _dbContext.SaveChanges();

            var result = _repositoryIsNotParent.GetPaginatedQuery().ToList();

            Assert.All(result, x => Assert.True(x.IsActive));
            Assert.True(result.SequenceEqual(result.OrderByDescending(x => x.Id)));
        }

        [Fact]
        public async Task GetWorkflowHistoryNames_ReturnsNames_WhenIsParent()
        {
            await _dbContext.WorkFlowHistories.AddRangeAsync(_fixture.WorkflowHistoryList);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.GetWorkflowHistoryNames();

            Assert.Equal(_fixture.WorkflowHistoryList.Count, result.Count);
            Assert.All(result, x => Assert.Contains(_fixture.WorkflowHistoryList, y => y.WorkflowName == x.WorkflowName));
        }

        [Fact]
        public async Task GetWorkflowHistoryNames_ReturnsNames_WhenNotParent()
        {
            var entity = _fixture.WorkflowHistoryDto;
            entity.CompanyId = "CHILD_COMPANY_123";
            entity.WorkflowName = "TestWorkflow";
            await _dbContext.WorkFlowHistories.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryIsNotParent.GetWorkflowHistoryNames();

            Assert.All(result, x => Assert.Equal("TestWorkflow", x.WorkflowName));
        }

        [Fact]
        public async Task GetWorkflowHistoryByWorkflowId_ReturnsResults_WhenIsParent()
        {
            var entity = _fixture.WorkflowHistoryDto;
            entity.WorkflowId = "WID1";
            await _dbContext.WorkFlowHistories.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.GetWorkflowHistoryByWorkflowId("WID1");

            Assert.All(result, x => Assert.Equal("WID1", x.WorkflowId));
        }

        [Fact]
        public async Task GetWorkflowHistoryByWorkflowId_ReturnsResults_WhenNotParent()
        {
            var entity = _fixture.WorkflowHistoryDto;
            entity.WorkflowId = "WID2";
            entity.CompanyId = "CHILD_COMPANY_123";
            await _dbContext.WorkFlowHistories.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryIsNotParent.GetWorkflowHistoryByWorkflowId("WID2");

            Assert.All(result, x => Assert.Equal("WID2", x.WorkflowId));
            Assert.All(result, x => Assert.Equal("CHILD_COMPANY_123", x.CompanyId));
        }

        [Fact]
        public async Task IsWorkflowHistoryNameExist_ReturnsTrue_WhenNameExists_AndIdIsInvalidGuid()
        {
            var entity = _fixture.WorkflowHistoryDto;
            entity.WorkflowName = "TestName";
            await _dbContext.WorkFlowHistories.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.IsWorkflowHistoryNameExist("TestName", "invalid-guid");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowHistoryNameExist_ReturnsFalse_WhenNameDoesNotExist_AndIdIsInvalidGuid()
        {
            var result = await _repositoryParent.IsWorkflowHistoryNameExist("NonExistent", "invalid-guid");

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowHistoryNameExist_ReturnsExpected_WhenIdIsValidGuid()
        {
            var id = Guid.NewGuid().ToString();
            var entity = _fixture.WorkflowHistoryDto;
            entity.ReferenceId = id;
            entity.WorkflowName = "UniqueName";
            await _dbContext.WorkFlowHistories.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.IsWorkflowHistoryNameExist("UniqueName", id);

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowHistoryNameUnique_ReturnsTrue_WhenNameExists()
        {
            var entity = _fixture.WorkflowHistoryDto;
            entity.WorkflowName = "UniqueName";
            await _dbContext.WorkFlowHistories.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.IsWorkflowHistoryNameUnique("UniqueName");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowHistoryNameUnique_ReturnsFalse_WhenNameDoesNotExist()
        {
            var result = await _repositoryParent.IsWorkflowHistoryNameUnique("NonExistent");

            Assert.False(result);
        }

        [Fact]
        public async Task GetWorkflowHistoryByWorkflowIdAndVersion_ReturnsEntity_WhenIsParent()
        {
            var entity = _fixture.WorkflowHistoryDto;
            entity.WorkflowId = "WID3";
            entity.Version = "v1";
            await _dbContext.WorkFlowHistories.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.GetWorkflowHistoryByWorkflowIdAndVersion("WID3", "v1");

            Assert.NotNull(result);
            Assert.Equal("WID3", result.WorkflowId);
            Assert.Equal("v1", result.Version);
        }

        [Fact]
        public async Task GetWorkflowHistoryByWorkflowIdAndVersion_ReturnsEntity_WhenNotParent()
        {
            var entity = _fixture.WorkflowHistoryDto;
            entity.WorkflowId = "WID4";
            entity.Version = "v2";
            entity.CompanyId = "ChHILD_COMPANY_123";
            await _dbContext.WorkFlowHistories.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryIsNotParent.GetWorkflowHistoryByWorkflowIdAndVersion("WID4", "v2");

            Assert.NotNull(result);
            Assert.Equal("WID4", result.WorkflowId);
            Assert.Equal("v2", result.Version);
            Assert.Equal("ChHILD_COMPANY_123", result.CompanyId);
        }
    }
}