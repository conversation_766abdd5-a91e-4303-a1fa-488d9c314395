﻿namespace ContinuityPatrol.Application.Features.BusinessServiceAvailability.Commands.Update;

public class UpdateBusinessServiceAvailabilityCommandHandler : IRequestHandler<UpdateBusinessServiceAvailabilityCommand,
    UpdateBusinessServiceAvailabilityResponse>
{
    private readonly IBusinessServiceAvailabilityRepository _businessServiceAvailabilityRepository;
    private readonly IMapper _mapper;

    public UpdateBusinessServiceAvailabilityCommandHandler(
        IBusinessServiceAvailabilityRepository businessServiceAvailabilityRepository, IMapper mapper)
    {
        _businessServiceAvailabilityRepository = businessServiceAvailabilityRepository;
        _mapper = mapper;
    }

    public async Task<UpdateBusinessServiceAvailabilityResponse> Handle(
        UpdateBusinessServiceAvailabilityCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _businessServiceAvailabilityRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.BusinessServiceAvailability), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateBusinessServiceAvailabilityCommand),
            typeof(Domain.Entities.BusinessServiceAvailability));

        await _businessServiceAvailabilityRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateBusinessServiceAvailabilityResponse
        {
            Message = Message.Update(nameof(Domain.Entities.BusinessServiceAvailability), eventToUpdate.ReferenceId),

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}