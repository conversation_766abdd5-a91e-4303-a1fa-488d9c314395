using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DataSyncOptionsRepositoryTests : IClassFixture<DataSyncOptionsFixture>
{
    private readonly DataSyncOptionsFixture _dataSyncOptionsFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DataSyncOptionsRepository _repository;

    public DataSyncOptionsRepositoryTests(DataSyncOptionsFixture dataSyncOptionsFixture)
    {
        _dataSyncOptionsFixture = dataSyncOptionsFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DataSyncOptionsRepository(_dbContext,DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsDto;

        // Act
        await _dbContext.DataSyncOptions.AddAsync(dataSyncOptions);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(dataSyncOptions.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSyncOptions.Name, result.Name);
        Assert.Equal(dataSyncOptions.ReplicationType, result.ReplicationType);
        Assert.Single(_dbContext.DataSyncOptions);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsDto;
        await _dbContext.DataSyncOptions.AddAsync(dataSyncOptions);
        await _dbContext.SaveChangesAsync();

        dataSyncOptions.Name = "UpdatedDataSyncOptionName";

        // Act
        _dbContext.DataSyncOptions.Update(dataSyncOptions);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(dataSyncOptions.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedDataSyncOptionName", result.Name);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsDto;
        await _dbContext.DataSyncOptions.AddAsync(dataSyncOptions);
        await _dbContext.SaveChangesAsync();

        // Act
        dataSyncOptions.IsActive = false;

        _dbContext.DataSyncOptions.Update(dataSyncOptions);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsDto;
        var addedEntity = await _repository.AddAsync(dataSyncOptions);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsDto;
        await _repository.AddAsync(dataSyncOptions);

        // Act
        var result = await _repository.GetByReferenceIdAsync(dataSyncOptions.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSyncOptions.ReferenceId, result.ReferenceId);
        Assert.Equal(dataSyncOptions.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsList;
        await _repository.AddRangeAsync(dataSyncOptions);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSyncOptions.Count, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldNotReturnInactiveEntities()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsList;
        dataSyncOptions.First().IsActive = false; // Make one inactive
        await _repository.AddRangeAsync(dataSyncOptions);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSyncOptions.Count - 1, result.Count); // Should exclude the inactive one
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public async Task  GetPaginatedQuery_ShouldReturnQueryableOrderedByIdDescending()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsList;

        await _repository.AddRangeAsync(dataSyncOptions);

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Any());
        
        // Verify ordering by checking if the first item has a higher ID than the last
        var resultList = result.ToList();
        if (resultList.Count > 1)
        {
            Assert.True(resultList.First().Id >= resultList.Last().Id);
        }
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnEmptyQueryable_WhenNoEntities()
    {
        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.False(result.Any());
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsDto;
        dataSyncOptions.Name = "ExistingDataSyncOptionName";
        await _repository.AddAsync(dataSyncOptions);

        // Act
        var result = await _repository.IsNameExist("ExistingDataSyncOptionName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsList;
        await _repository.AddRangeAsync(dataSyncOptions);

        // Act
        var result = await _repository.IsNameExist("NonExistentDataSyncOptionName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsDto;
        dataSyncOptions.Name = "SameDataSyncOptionName";
        await _repository.AddAsync(dataSyncOptions);

        // Act
        var result = await _repository.IsNameExist("SameDataSyncOptionName", dataSyncOptions.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        var dataSyncOptions1 = _dataSyncOptionsFixture.DataSyncOptionsDto;
        dataSyncOptions1.Name = "ExistingDataSyncOptionName";
        await _repository.AddAsync(dataSyncOptions1);

        var dataSyncOptions2 = _dataSyncOptionsFixture.DataSyncOptionsDto;
        dataSyncOptions2.ReferenceId = Guid.NewGuid().ToString();
        dataSyncOptions2.Name = "DifferentDataSyncOptionName";
        await _repository.AddAsync(dataSyncOptions2);

        // Act
        var result = await _repository.IsNameExist("ExistingDataSyncOptionName", dataSyncOptions2.ReferenceId);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsList;

        // Act
        var result = await _repository.AddRangeAsync(dataSyncOptions);

        // Assert
        Assert.Equal(dataSyncOptions.Count, result.Count());
        Assert.Equal(dataSyncOptions.Count, _dbContext.DataSyncOptions.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsList;
        await _repository.AddRangeAsync(dataSyncOptions);

        // Act
        var result = await _repository.RemoveRangeAsync(dataSyncOptions);

        // Assert
        Assert.Equal(dataSyncOptions.Count, result.Count());
        Assert.Empty(_dbContext.DataSyncOptions);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var dataSyncOptions = _dataSyncOptionsFixture.DataSyncOptionsList.Take(5).ToList();
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(dataSyncOptions);
        var initialCount = dataSyncOptions.Count;
        
        var toUpdate = dataSyncOptions.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedDataSyncOptionName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = dataSyncOptions.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.Name == "UpdatedDataSyncOptionName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
