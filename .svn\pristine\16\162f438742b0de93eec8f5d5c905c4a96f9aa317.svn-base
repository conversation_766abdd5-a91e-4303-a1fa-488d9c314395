﻿namespace ContinuityPatrol.Application.Features.Template.Queries.GetByInfraObjectIdandActiontype;

public class TemplateByInfraObjectIdandActiontypeVm
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Properties { get; set; }
    public string Icon { get; set; }
    public string Version { get; set; }
    public string Type { get; set; }
    public string ReplicationTypeId { get; set; }
    public string ReplicationTypeName { get; set; }
    public string ActionType { get; set; }
    public string ServerProperties { get; set; }
    public string DatabaseProperties { get; set; }
    public string ReplicationProperties { get; set; }



    //public string PRServerId { get; set; }
    //public string PRServerName { get; set; }
    //public string DRServerId { get; set; }
    //public string DRServerName { get; set; }
    //public string NearDRServerId { get; set; }
    //public string NearDRServerName { get; set; }
    //public string PRDatabaseId { get; set; }
    //public string PRDatabaseName { get; set; }
    //public string DRDatabaseId { get; set; }
    //public string DRDatabaseName { get; set; }
    //public string NearDRDatabaseId { get; set; }
    //public string NearDRDatabaseName { get; set; }
    //public string PRReplicationId { get; set; }
    //public string PRReplicationName { get; set; }
    //public string DRReplicationId { get; set; }
    //public string DRReplicationName { get; set; }
    //public string NearDRReplicationId { get; set; }
    //public string NearDRReplicationName { get; set; }
}