﻿using ContinuityPatrol.Application.Features.WorkflowPermission.Events.Create;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.WorkflowPermission.Commands.Create;

public class
    CreateWorkflowPermissionCommandHandler : IRequestHandler<CreateWorkflowPermissionCommand,
        CreateWorkflowPermissionResponse>
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IWorkflowPermissionRepository _workflowPermissionRepository;

    public CreateWorkflowPermissionCommandHandler(IMapper mapper,
        IWorkflowPermissionRepository workflowPermissionRepository, ILoggedInUserService loggedInUserService,
        IPublisher publisher)
    {
        _mapper = mapper;
        _workflowPermissionRepository = workflowPermissionRepository;
        _loggedInUserService = loggedInUserService;
        _publisher = publisher;
    }

    public async Task<CreateWorkflowPermissionResponse> Handle(CreateWorkflowPermissionCommand request,
        CancellationToken cancellationToken)
    {
        request.CompanyId = _loggedInUserService.CompanyId;

        var workflowPermission = _mapper.Map<Domain.Entities.WorkflowPermission>(request);

        workflowPermission = await _workflowPermissionRepository.AddAsync(workflowPermission);

        var itemNames = JArray.Parse(workflowPermission.AccessProperties)
            .Select(item => item["name"].ToString())
            .ToList();

        var nameList = string.Join(", ", itemNames);

        var response = new CreateWorkflowPermissionResponse
        {
            Message = Message.Create("User Privilege", nameList),
            WorkflowPermissionId = workflowPermission.ReferenceId
        };

        await _publisher.Publish(new WorkflowPermissionCreatedEvent { AccessProperties = nameList }, cancellationToken);

        return response;
    }
}