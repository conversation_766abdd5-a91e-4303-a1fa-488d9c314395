using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class IncidentRepositoryTests : IClassFixture<IncidentFixture>, IDisposable
{
    private readonly IncidentFixture _incidentFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly IncidentRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public IncidentRepositoryTests(IncidentFixture incidentFixture)
    {
        _incidentFixture = incidentFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new IncidentRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.Incidents.RemoveRange(_dbContext.Incidents);
        _dbContext.InfraObjects.RemoveRange(_dbContext.InfraObjects);
        await _dbContext.SaveChangesAsync();
    }

    #region GetAllIncidentNames Tests

    [Fact]
    public async Task GetAllIncidentNames_ThrowsNotImplementedException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<NotImplementedException>(() => 
            _repository.GetAllIncidentNames());
    }

    #endregion

    #region IsIncidentNameUnique Tests

    [Fact]
    public async Task IsIncidentNameUnique_ReturnsTrue_WhenInfraObjectWithNameExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure";

        var infraObject = new InfraObject
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = infraObjectName,
            Description = "Test infrastructure object",
            CompanyId = "COMPANY_123",
            BusinessServiceId = "BS_123",
            BusinessServiceName = "Test Business Service",
            BusinessFunctionId = "BF_123",
            BusinessFunctionName = "Test Business Function",
            Type = 1,
            SubType = "Server",
            DRReady = true,
            NearDR = false,
            RecoveryType = 1,
            Priority = "High",
            State = "Active",
            ReplicationStatus = 1,
            DROperationStatus = 1,
            IsPair = false,
            IsDrift = false,
            IsAssociate = false,
            IsActive = true
        };

        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsIncidentNameUnique(infraObjectName);

        // Assert
        Assert.True(result); // Returns true when name exists in InfraObjects
    }

    [Fact]
    public async Task IsIncidentNameUnique_ReturnsFalse_WhenInfraObjectWithNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "Non-Existent Infrastructure";

        var infraObject = new InfraObject
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Different Infrastructure",
            Description = "Test infrastructure object",
            CompanyId = "COMPANY_123",
            BusinessServiceId = "BS_123",
            BusinessServiceName = "Test Business Service",
            BusinessFunctionId = "BF_123",
            BusinessFunctionName = "Test Business Function",
            Type = 1,
            SubType = "Server",
            DRReady = true,
            NearDR = false,
            RecoveryType = 1,
            Priority = "High",
            State = "Active",
            ReplicationStatus = 1,
            DROperationStatus = 1,
            IsPair = false,
            IsDrift = false,
            IsAssociate = false,
            IsActive = true
        };

        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsIncidentNameUnique(nonExistentName);

        // Assert
        Assert.False(result); // Returns false when name doesn't exist in InfraObjects
    }

    [Fact]
    public async Task IsIncidentNameUnique_ReturnsFalse_WhenDatabaseIsEmpty()
    {
        // Arrange
        await ClearDatabase();
        var testName = "Any Name";

        // Act
        var result = await _repository.IsIncidentNameUnique(testName);

        // Assert
        Assert.False(result); // Returns false when no InfraObjects exist
    }

    [Fact]
    public async Task IsIncidentNameUnique_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure";

        var infraObject = new InfraObject
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = infraObjectName,
            Description = "Test infrastructure object",
            CompanyId = "COMPANY_123",
            BusinessServiceId = "BS_123",
            BusinessServiceName = "Test Business Service",
            BusinessFunctionId = "BF_123",
            BusinessFunctionName = "Test Business Function",
            Type = 1,
            SubType = "Server",
            DRReady = true,
            NearDR = false,
            RecoveryType = 1,
            Priority = "High",
            State = "Active",
            ReplicationStatus = 1,
            DROperationStatus = 1,
            IsPair = false,
            IsDrift = false,
            IsAssociate = false,
            IsActive = true
        };

        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        // Act
        var resultExactCase = await _repository.IsIncidentNameUnique("Test Infrastructure");
        var resultDifferentCase = await _repository.IsIncidentNameUnique("test infrastructure");

        // Assert
        Assert.True(resultExactCase); // Exact case match should return true
        Assert.False(resultDifferentCase); // Different case should return false (case sensitive)
    }

    [Fact]
    public async Task IsIncidentNameUnique_HandlesNullName()
    {
        // Arrange
        await ClearDatabase();

        var infraObject = new InfraObject
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Test Infrastructure",
            Description = "Test infrastructure object",
            CompanyId = "COMPANY_123",
            IsActive = true
        };

        await _dbContext.InfraObjects.AddAsync(infraObject);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsIncidentNameUnique(null);

        // Assert
        Assert.False(result); // Should return false for null name
    }

    #endregion

    #region IsIncidentNameExist Tests

    [Fact]
    public async Task IsIncidentNameExist_ReturnsTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var incidentName = "Test Incident";
        var invalidId = "invalid-guid";

        var incident = new Incident
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IncidentName = incidentName,
            IncidentNumber = "INC-001",
            Description = "Test incident",
            IsActive = true
        };

        await _dbContext.Incidents.AddAsync(incident);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsIncidentNameExist(incidentName, invalidId);

        // Assert
        Assert.True(result); // Should return true when name exists and ID is invalid
    }

    [Fact]
    public async Task IsIncidentNameExist_ReturnsFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "Non-Existent Incident";
        var invalidId = "invalid-guid";

        var incident = new Incident
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IncidentName = "Different Incident",
            IncidentNumber = "INC-001",
            Description = "Test incident",
            IsActive = true
        };

        await _dbContext.Incidents.AddAsync(incident);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsIncidentNameExist(nonExistentName, invalidId);

        // Assert
        Assert.False(result); // Should return false when name doesn't exist
    }

    [Fact]
    public async Task IsIncidentNameExist_ReturnsTrue_WhenNameExistsWithDifferentValidId()
    {
        // Arrange
        await ClearDatabase();
        var incidentName = "Test Incident";
        var existingId = Guid.NewGuid().ToString();
        var differentId = Guid.NewGuid().ToString();

        var incident = new Incident
        {
            ReferenceId = existingId,
            IncidentName = incidentName,
            IncidentNumber = "INC-001",
            Description = "Test incident",
            IsActive = true
        };

        await _dbContext.Incidents.AddAsync(incident);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsIncidentNameExist(incidentName, differentId);

        // Assert
        Assert.True(result); // Should return true when name exists with different ID
    }

    [Fact]
    public async Task IsIncidentNameExist_ReturnsFalse_WhenNameExistsWithSameValidId()
    {
        // Arrange
        await ClearDatabase();
        var incidentName = "Test Incident";
        var existingId = Guid.NewGuid().ToString();

        var incident = new Incident
        {
            ReferenceId = existingId,
            IncidentName = incidentName,
            IncidentNumber = "INC-001",
            Description = "Test incident",
            IsActive = true
        };

        await _dbContext.Incidents.AddAsync(incident);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsIncidentNameExist(incidentName, existingId);

        // Assert
        Assert.False(result); // Should return false when name exists with same ID (editing same record)
    }

    [Fact]
    public async Task IsIncidentNameExist_ReturnsFalse_WhenNameDoesNotExistWithValidId()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "Non-Existent Incident";
        var validId = Guid.NewGuid().ToString();

        var incident = new Incident
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IncidentName = "Different Incident",
            IncidentNumber = "INC-001",
            Description = "Test incident",
            IsActive = true
        };

        await _dbContext.Incidents.AddAsync(incident);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsIncidentNameExist(nonExistentName, validId);

        // Assert
        Assert.False(result); // Should return false when name doesn't exist
    }

    [Fact]
    public async Task IsIncidentNameExist_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var incidentName = "Test Incident";
        var invalidId = "invalid-guid";

        var incident = new Incident
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IncidentName = incidentName,
            IncidentNumber = "INC-001",
            Description = "Test incident",
            IsActive = true
        };

        await _dbContext.Incidents.AddAsync(incident);
        await _dbContext.SaveChangesAsync();

        // Act
        var resultExactCase = await _repository.IsIncidentNameExist("Test Incident", invalidId);
        var resultDifferentCase = await _repository.IsIncidentNameExist("test incident", invalidId);

        // Assert
        Assert.True(resultExactCase); // Exact case match should return true
        Assert.False(resultDifferentCase); // Different case should return false (case sensitive)
    }

    [Fact]
    public async Task IsIncidentNameExist_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var incidentName = "Test Incident";
        var invalidId = "invalid-guid";

        // Act
        var result = await _repository.IsIncidentNameExist(incidentName, invalidId);

        // Assert
        Assert.False(result); // Should return false when database is empty
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddIncident_WhenValidIncident()
    {
        // Arrange
        await ClearDatabase();
        var incident = new Incident
        {
            ReferenceId = Guid.NewGuid().ToString(),
            AlertId = "ALERT_123",
            WorkflowId = "WF_123",
            WorkflowName = "Test Workflow",
            BusinessServiceId = "BS_123",
            BusinessServiceName = "Test Business Service",
            BusinessFunctionId = "BF_123",
            BusinessFunctionName = "Test Business Function",
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Test Infrastructure Object",
            IncidentNumber = "INC-001",
            IncidentName = "Test Incident",
            Description = "Test incident description",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(incident);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(incident.AlertId, result.AlertId);
        Assert.Equal(incident.WorkflowId, result.WorkflowId);
        Assert.Equal(incident.WorkflowName, result.WorkflowName);
        Assert.Equal(incident.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(incident.BusinessServiceName, result.BusinessServiceName);
        Assert.Equal(incident.BusinessFunctionId, result.BusinessFunctionId);
        Assert.Equal(incident.BusinessFunctionName, result.BusinessFunctionName);
        Assert.Equal(incident.InfraObjectId, result.InfraObjectId);
        Assert.Equal(incident.InfraObjectName, result.InfraObjectName);
        Assert.Equal(incident.IncidentNumber, result.IncidentNumber);
        Assert.Equal(incident.IncidentName, result.IncidentName);
        Assert.Equal(incident.Description, result.Description);
        Assert.Single(_dbContext.Incidents);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenIncidentIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsIncident_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var incident = new Incident
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IncidentName = "Test Incident",
            IncidentNumber = "INC-001",
            Description = "Test description",
            IsActive = true
        };

        await _dbContext.Incidents.AddAsync(incident);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(incident.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(incident.Id, result.Id);
        Assert.Equal(incident.IncidentName, result.IncidentName);
        Assert.Equal(incident.IncidentNumber, result.IncidentNumber);
        Assert.Equal(incident.Description, result.Description);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion
}
