namespace ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Create;

public class CreateAdPasswordExpireCommand : IRequest<CreateAdPasswordExpireResponse>
{
    public string DomainServerId { get; set; }
    public string DomainServer { get; set; }
    public string UserName { get; set; }
    public string Email { get; set; }
    public string ServerList { get; set; }
	public string NotificationDays { get; set; }
	public bool IsPassword { get; set; }
}
