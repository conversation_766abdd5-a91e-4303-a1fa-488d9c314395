﻿using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessFunction.Queries;

public class GetBusinessFunctionPaginatedListQueryHandlerTests : IClassFixture<BusinessFunctionFixture>
{
    private readonly BusinessFunctionFixture _businessFunctionFixture;
    private readonly GetBusinessFunctionPaginatedListQueryHandler _handler;
    private readonly Mock<IBusinessFunctionRepository> _mockBusinessFunctionRepository;

    public GetBusinessFunctionPaginatedListQueryHandlerTests(BusinessFunctionFixture businessFunctionFixture)
    {
        _businessFunctionFixture = businessFunctionFixture;

        _businessFunctionFixture.BusinessFunctions[0].Name = "Demo_Test";
        _businessFunctionFixture.BusinessFunctions[0].BusinessServiceName = "Simple_File";
        _businessFunctionFixture.BusinessFunctions[0].Description = "Details12";
        _businessFunctionFixture.BusinessFunctions[0].CriticalityLevel = "Medium";

        _businessFunctionFixture.BusinessFunctions[1].Name = "Testing";
        _businessFunctionFixture.BusinessFunctions[1].Description = "Details";
        _businessFunctionFixture.BusinessFunctions[1].CriticalityLevel = "Low";
        _businessFunctionFixture.BusinessFunctions[1].BusinessServiceName = "Simple_DR";

        _mockBusinessFunctionRepository = BusinessFunctionRepositoryMocks.GetPaginatedBusinessFunctionRepository(_businessFunctionFixture.BusinessFunctions);

        _handler = new GetBusinessFunctionPaginatedListQueryHandler(_businessFunctionFixture.Mapper, _mockBusinessFunctionRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetBusinessFunctionPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessFunctionListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_BusinessFunctions_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetBusinessFunctionPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Name=Demo_Test;BusinessServiceName=Simple_File;CriticalityLevel=Medium;Description=Details" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessFunctionListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Name.ShouldBe("Demo_Test");

        result.Data[0].BusinessServiceName.ShouldBe("Simple_File");

        result.Data[0].CriticalityLevel.ShouldBe("Medium");

        result.Data[0].Description.ShouldBe("Details12");
    }

    [Fact]
    public async Task Handle_Return_PaginatedBusinessFunctions_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetBusinessFunctionPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Simple_File" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessFunctionListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<BusinessFunctionListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].BusinessServiceId.ShouldBe(_businessFunctionFixture.BusinessFunctions[0].BusinessServiceId);

        result.Data[0].BusinessServiceName.ShouldBe("Simple_File");

        result.Data[0].CriticalityLevel.ShouldBe(_businessFunctionFixture.BusinessFunctions[0].CriticalityLevel);

        result.Data[0].ConfiguredRPO.ShouldNotBeEmpty();

        result.Data[0].ConfiguredRTO.ShouldNotBeEmpty();

        result.Data[0].ConfiguredMAO.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetBusinessFunctionPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABCD" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<BusinessFunctionListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetBusinessFunctionPaginatedListQuery(), CancellationToken.None);

        _mockBusinessFunctionRepository.Verify(x => x.PaginatedListAllAsync(It.IsAny<int>(),
       It.IsAny<int>(), It.IsAny<BusinessFunctionFilterSpecification>(),
       It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }
}