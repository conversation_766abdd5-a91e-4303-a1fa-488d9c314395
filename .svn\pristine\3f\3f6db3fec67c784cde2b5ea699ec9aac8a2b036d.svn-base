﻿@model ContinuityPatrol.Domain.ViewModels.LicenseManagerModel.BaseLicenseViewModel

    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg Organization_modal">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title">
                    <i class="cp-license-manager"></i><span>License Configuration</span>
                </h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <div class="wizard-content">
                <form id="example-form" asp-area="Admin" asp-controller="LicenseManager" asp-action="BaseLicenseCreateOrUpdate" method="post" class="">
                        <section>
                            <div class="mb-3 form-group">
                                <div class="form-label">License Key</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-name"></i>
                                    </span>
                                    <input type="text" class="form-control" asp-for="LicenseKey" autocomplete="off" id="licensekey" placeholder="Enter License Key" />
                                    <input asp-for="Id" type="hidden" id="licenseId" />
                                </div>
                                <span asp-validation-for="LicenseKey" id="LicenseKey-error"></span>
                            </div>

                        </section>
                        <input asp-for="Id"  type="hidden" id="licenseId" />
                        
                    </form>
                </div>
            </div>
        <div class="modal-footer d-flex justify-content-between align-items-center">
            <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button class="btn btn-primary btn-sm"  style="cursor: none; pointer-events: none; opacity: 0.5;" id="SaveFunction">Save</button>
            </div>
        </div>
            
        </div>
    </div>

<script src="~/js/Admin/License/License Manager/LicenseManager.js"></script>
@section Scripts
    {
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}