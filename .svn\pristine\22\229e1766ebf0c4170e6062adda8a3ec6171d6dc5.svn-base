﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.AccessManagerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.AccessManager.Queries.GetPaginatedList;

public class GetAccessManagerPaginatedListQueryHandler : IRequestHandler<GetAccessManagerPaginatedListQuery,
    PaginatedResult<AccessManagerListVm>>
{
    private readonly IAccessManagerRepository _accessManagerRepository;
    private readonly IMapper _mapper;

    public GetAccessManagerPaginatedListQueryHandler(IMapper mapper, IAccessManagerRepository accessManagerRepository)
    {
        _mapper = mapper;
        _accessManagerRepository = accessManagerRepository;
    }

    public async Task<PaginatedResult<AccessManagerListVm>> Handle(GetAccessManagerPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new AccessManagerFilterSpecification(request.SearchString);

        var queryable =await _accessManagerRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var accessManagerList = _mapper.Map<PaginatedResult<AccessManagerListVm>>(queryable);


        return accessManagerList;
        //var queryable = _accessManagerRepository.PaginatedListAllAsync();

        //var productFilterSpec = new AccessManagerFilterSpecification(request.SearchString);

        //var accessManagerList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<AccessManagerListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return accessManagerList;
    }
}