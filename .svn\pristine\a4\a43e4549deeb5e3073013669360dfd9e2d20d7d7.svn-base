﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class AlertReceiverRepository : BaseRepository<AlertReceiver>, IAlertReceiverRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public AlertReceiverRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<PaginatedResult<AlertReceiver>>PaginatedListAllAsync(int pageNumber, int pageSize, Specification<AlertReceiver> productFilterSpec, string sortColumn, string sortOrder)
    {
        var alertReceiver= await FilterRequiredField(_loggedInUserService.IsParent
            ? Entities.Specify(productFilterSpec).DescOrderById()
            : Entities.Specify(productFilterSpec).Where(x => x.ReferenceId.Equals(_loggedInUserService.CompanyId)).DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);

        return alertReceiver;
    }

    public override IQueryable<AlertReceiver> GetPaginatedQuery()
    {
        return _loggedInUserService.IsParent
            ? base.GetPaginatedQuery()
            : Entities.Where(x => x.IsActive && x.ReferenceId.Equals(_loggedInUserService.CompanyId)).DescOrderById();
    }

    public Task<bool> IsAlertReceiverNameUnique(string name)
    {
        var matches = _dbContext.AlertReceivers.Any(x => x.Name.Equals(name));

        return Task.FromResult(matches);
    }

    public Task<bool> IsAlertReceiverNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.AlertReceivers.Any(e => e.Name.Equals(name)))
            : Task.FromResult(_dbContext.AlertReceivers.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    private static IQueryable<AlertReceiver> FilterRequiredField(IQueryable<AlertReceiver> query)
    {
        return query.Select(u => new AlertReceiver
        {
            Id = u.Id,
            ReferenceId = u.ReferenceId,
            CompanyId = u.CompanyId,
            EmailAddress = u.EmailAddress,
            MobileNumber = u.MobileNumber,
            Name = u.Name,
            Properties = u.Properties,
            IsMail = u.IsMail,
            IsActiveUser = u.IsActiveUser,
            IsSendReport = u.IsSendReport
        });
    }
}