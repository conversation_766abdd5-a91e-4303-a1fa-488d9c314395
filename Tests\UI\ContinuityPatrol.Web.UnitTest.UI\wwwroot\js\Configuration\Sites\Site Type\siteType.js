﻿const siteTypeURL = {
    SiteTypePaginatedUrl: "/Configuration/SiteType/GetPagination",
    SiteTypeExistUrl: "Configuration/SiteType/IsSiteTypeExist",
    siteTypeSaveUrl: "Configuration/SiteType/CreateOrUpdate",
    siteTypeDeleteUrl: "Configuration/SiteType/Delete",
    sieTypeListUrl: "Configuration/SiteType/GetSiteTypeList"
}
let selectedValues = [];
let SiteValue = [];
let globalIsDelete = true;

const permission = {
    create: $("#siteTypeConfigCreate").data("create-permission")?.toLowerCase(),
    delete: $("#siteTypeConfigDelete").data("delete-permission")?.toLowerCase()
};

if (permission.create === 'false') $("#siteTypeCreateButton").addClass('btn-disabled').css('pointer-events','none');

const dataTable = $('#siteTypeTable').DataTable({
    language: {
        paginate: {
            next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
            previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
        },
        infoFiltered: ""
    },
    dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
    scrollY: true,
    deferRender: true,
    scroller: true,
    processing: true,
    serverSide: true,
    filter: true,
    order: [],
    ajax: {
        type: "GET",
        url: siteTypeURL.SiteTypePaginatedUrl,
        dataType: "json",
        data: function (d) {
            let sortIndex = d?.order[0]?.column || '';
            let sortValue = sortIndex === 1 ? "type" : sortIndex === 2 ? "category" : sortIndex === 3 ? "status" : "";
            let orderValue = d?.order[0]?.dir || 'asc';
            d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
            d.pageSize = d?.length;
            d.searchString = selectedValues?.length === 0 ? $('#siteTypeSearch').val() : selectedValues?.join(';');
            d.sortColumn = sortValue;
            d.SortOrder = orderValue;
            selectedValues.length = 0;
        },
        dataSrc: function (json) {
            SiteValue = [];
            json.recordsTotal = json?.data?.totalPages;
            json.recordsFiltered = json?.data?.totalCount;
            if (json?.success && Array.isArray(json?.data?.data) && json?.data?.data?.length) {
                SiteValue = [json.data.data];
                $(".pagination-column").removeClass("disabled");
                return json.data.data;
            }
            $(".pagination-column").addClass("disabled");
            if (!json.success && json?.data?.messages?.length) {
                notificationAlert('warning', json.data.messages);
            }
            return [];
        }
    },
    columnDefs: [{ targets: [1], className: "truncate" }],
    columns: [
        {
            data: null,
            name: "Sr. No.",
            autoWidth: true,
            orderable: false,
            render: (data, type, row, meta) => type === 'display' ? meta.row + 1 : data
        },
        {
            data: "type",
            name: "name",
            autoWidth: true,
            render: (data, type, row) => {
                if (type === 'display') {
                    const iconClass = {
                        "1738f813-6090-40cc-8869-25741a156f73": "cp-prsites",
                        "2914c9b2-91d3-4e03-baed-77824f16327c": "cp-physical-drsite",
                        "0e3ecd95-ca88-42be-b6f3-01b68147c346": "cp-physical-neardrsite"
                    }[row?.id] || (row?.icon?.split(' ')[0] || 'NA');
                    return `<span title="${data || 'NA'}"><i class="${iconClass}"></i>${data || 'NA'}</span>`;
                }
                return data;
            }
        },
        {
            data: "category",
            name: "type",
            autoWidth: true,
            render: (data, type, row) => type === 'display' ? `<td><span>${row?.category || 'NA'}</span></td>` : data
        },
        {
            render: (data, type, row) => {
                const editBtn = permission.create === 'true'
                    ? `<span role="button" title="Edit" class="siteTypeEditbtn" data-sitedata='${btoa(JSON.stringify(row || 'NA'))}'><i class="cp-edit"></i></span>`
                    : `<span role="button" title="Edit" class="icon-disabled"><i class="cp-edit"></i></span>`;

                const deleteBtn = permission.delete === 'true'
                    ? (row?.isDelete && ['Primary', 'DR'].includes(row?.category)
                        ? `<span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i></span>`
                        : `<span role="button" title="Delete" class="siteTypeDeletebtn" data-sitetypeid="${row?.id || 'NA'}" data-sitetypename="${row?.type || 'NA'}"><i class="cp-Delete"></i></span>`)
                    : `<span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i></span>`;

                return `<div class="d-flex align-items-center gap-2">${editBtn} ${deleteBtn}</div>`;
            },
            orderable: false
        }
    ],
    rowCallback: function (row, data, index) {
        const counter = this.api().context[0]?._iDisplayStart + index + 1;
        $('td:eq(0)', row).html(counter);
    },
    initComplete: function () {
        updatePaginationTitles();
    }
});

dataTable.on('draw.dt', updatePaginationTitles);

function updatePaginationTitles() {
    $('.paginate_button.page-item.previous').attr('title', 'Previous');
    $('.paginate_button.page-item.next').attr('title', 'Next');
}

$('#siteTypeSearch').on('keydown input', function (e) {
    if (['=', 'Enter'].includes(e.key) || (e.shiftKey && e.key === '<')) {
        e.preventDefault();
        return false;
    }
    handleSearchDebounced();
});

const handleSearchDebounced = commonDebounce(function () {
    const nameVal = $('#siteTypeSearch').val();
    if ($('#stNameFilter').is(':checked')) selectedValues.push($('#stNameFilter').val() + nameVal);
    if ($('#stypeFilter').is(':checked')) selectedValues.push($('#stypeFilter').val() + nameVal);
    dataTable.ajax.reload(json => {
        if (json.recordsFiltered === 0) $('.dataTables_empty').text('No records found');
    });
}, 500);

async function IsNameExist(url, data) {
    return !data?.type?.trim() ? true : (await getAysncWithHandler(url, data)) ? "Name already exists" : true;
}

// Populate on Edit Click
function populateSiteType(data) {
    globalIsDelete = data?.isDelete;
    $('#siteTypeName').attr({ siteTypeNameId: data?.id, hiddenIcon: data?.icon }).val(data?.type);
    const dropdown = $('#siteTypeDropdown').val(data?.category);
    dropdown.prop('disabled', ['primary', 'dr'].includes(data?.category?.toLowerCase()));
    dropdown.find('option[value="Primary"], option[value="DR"]').prop('disabled', !dropdown.prop('disabled'));
    $('#siteTypeNameError,#siteTypeError').text('').removeClass('field-validation-error');
}

function clearInputField() {
    $('#siteTypeName, #siteTypeDropdown').val('');
    $('#siteTypeSaveBtn').text('Save');
    $('#siteTypeNameError, #siteTypeError').text('').removeClass('field-validation-error');
}

async function validateType(value, id = null) {
    const errorElement = $('#siteTypeNameError');
    if (!value) return errorElement.text('Enter site type name').addClass('field-validation-error'), false;
    if (value.includes('<')) return errorElement.text('Special characters not allowed').addClass('field-validation-error'), false;

    const url = RootUrl + siteTypeURL.SiteTypeExistUrl;
    const data = { type: value, id };
    const validations = [
        SpecialCharValidate(value), ShouldNotBeginWithSpace(value), ShouldNotBeginWithUnderScore(value),
        ShouldNotBeginWithDotAndHyphen(value), ShouldNotConsecutiveDotAndHyphen(value), OnlyNumericsValidate(value),
        ShouldNotBeginWithNumber(value), ShouldNotEndWithSpace(value), ShouldNotAllowMultipleSpace(value),
        SpaceWithUnderScore(value), ShouldNotEndWithUnderScore(value), MultiUnderScoreRegex(value),
        SpaceAndUnderScoreRegex(value), minMaxlength(value), secondChar(value), await IsNameExist(url, data)
    ];
    return CommonValidation(errorElement, validations);
}

function validateDropDown(value, msg, id) {
    const el = $('#' + id);
    return value ? el.text('').removeClass('field-validation-error') : el.text(msg).addClass('field-validation-error'), !!value;
}
console.log("Site Type JS events Loaded");

// Create btn on click
$('#siteTypeCreateButton').on('click', function () {
    $('#siteTypeName').attr('siteTypeNameId', "");
    $('#siteTypeDropdown').prop('disabled', false);
    clearInputField();

    const options = $('#siteTypeDropdown option');
    options.prop('disabled', false);
    SiteValue.flat().forEach(item => {
        if (item.category === 'Primary') options.filter('[value="Primary"]').prop('disabled', true);
        if (item.category === 'DR') options.filter('[value="DR"]').prop('disabled', true);
    });

    $('#siteTypeCreateModal').modal('show');
});

// Edit in click
$('#siteTypeTable').on('click', '.siteTypeEditbtn', function () {
    const data = JSON.parse(atob($(this).data("sitedata")));
    if (data) {
        populateSiteType(data);
        $('#siteTypeSaveBtn').text('Update');
        $('#siteTypeCreateModal').modal('show');
    }
});

// Save function on click
$('#siteTypeSaveBtn').on('click', commonDebounce(async function () {
    $('#siteTypeName').attr('deleteSiteStatus', globalIsDelete);
    const name = $('#siteTypeName').val();
    const site = $('#siteTypeDropdown').val();
    const siteId = $('#siteTypeName').attr('siteTypeNameId');
    const isSiteValid = validateDropDown(site, 'Select type', 'siteTypeError');
    const isTypeValid = await validateType(name, siteId);
    const hiddenIcon = $('#siteTypeName').attr('hiddenIcon');

    if (isTypeValid && isSiteValid) {
        sanitizeContainer(['siteTypeName', 'Site']);
        const payload = { Id: siteId, Type: name, Icon: hiddenIcon, IsDelete: globalIsDelete, Category: site, __RequestVerificationToken: gettoken() };
        await $.post({
            url: RootUrl + siteTypeURL.siteTypeSaveUrl,
            dataType: 'json',
            data: payload,
            success: response => {
                if (response?.success && response?.data?.message) {
                    $('#siteTypeCreateModal').modal('hide');
                    notificationAlert("success", response.data.message);
                    setTimeout(() => dataTable.ajax.reload(), 1000);
                } else errorNotification(response);
            }
        });
    }
}, 800));

// Delete icon on click
$('#siteTypeTable').on('click', '.siteTypeDeletebtn', function () {
    const id = $(this).data('sitetypeid');
    const name = $(this).data('sitetypename');
    if (id) {
        $('#siteTypeDeleteId').attr('title', name).text(name).val(id);
        $('#siteTypeDeleteModal').modal('show');
    }
});

// Confirm Delete on click
$('#siteTypeDeleteButton').on('click', async function () {
    const id = $('#siteTypeDeleteId').val();
    const name = $('#siteTypeDeleteId').attr('title');
    if (id) {
        await $.ajax({
            url: RootUrl + siteTypeURL.siteTypeDeleteUrl,
            type: "DELETE",
            dataType: "json",
            data: { id, name, __RequestVerificationToken: gettoken() },
            success: response => {
                $('#siteTypeDeleteModal').modal('hide');
                if (response?.success && response?.data?.message) {
                    notificationAlert("success", response.data.message);
                    setTimeout(() => dataTable.ajax.reload(), 400);
                } else errorNotification(response);
            }
        });
    }
});

// Input Validations siteTypeName, Site
$('#siteTypeName').on('keyup', commonDebounce(async function () {
    const val = $(this).val().replace(/\s{2,}/g, ' ');
    $(this).val(val);
    $(this).attr('hiddenIcon', 'cp-custom-server-4');
    await validateType(val, $(this).attr('siteTypeNameId'));
}, 400));

$('#siteTypeDropdown').on('change', function () {
    validateDropDown($(this).val(), ' Select type', 'siteTypeError');
});

$('#siteTypeCreateForm').on('keypress', function (e) {
    if (e.key === 'Enter') e.preventDefault();
});