﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Workflow.Events.PaginatedView;

public class WorkflowPaginatedEventHandler : INotificationHandler<WorkflowPaginatedEvent>
{
    private readonly ILogger<WorkflowPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowPaginatedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowPaginatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Entity = Modules.Workflow.ToString(),
            Action = $"{ActivityType.View} {Modules.Workflow}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Workflow List viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Workflow List viewed");
    }
}