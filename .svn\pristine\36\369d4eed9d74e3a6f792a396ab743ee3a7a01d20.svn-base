namespace ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Create;

public class CreateBulkImportActionResultCommandHandler : IRequestHandler<CreateBulkImportActionResultCommand,
    CreateBulkImportActionResultResponse>
{
    private readonly IBulkImportActionResultRepository _bulkImportActionResultRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateBulkImportActionResultCommandHandler(IMapper mapper,
        IBulkImportActionResultRepository bulkImportActionResultRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _bulkImportActionResultRepository = bulkImportActionResultRepository;
    }

    public async Task<CreateBulkImportActionResultResponse> Handle(CreateBulkImportActionResultCommand request,
        CancellationToken cancellationToken)
    {
        var bulkImportActionResult = _mapper.Map<Domain.Entities.BulkImportActionResult>(request);

        bulkImportActionResult = await _bulkImportActionResultRepository.AddAsync(bulkImportActionResult);

        var response = new CreateBulkImportActionResultResponse
        {
            //  Message = Message.Create(nameof(Domain.Entities.BulkImportActionResult), bulkImportActionResult.Name),

            Id = bulkImportActionResult.ReferenceId
        };

        // await _publisher.Publish(new BulkImportActionResultCreatedEvent { Name = bulkImportActionResult.Name }, cancellationToken);

        return response;
    }
}