﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Replication.Events.Create;

public class ReplicationCreatedEventHandler : INotificationHandler<ReplicationCreatedEvent>
{
    private readonly ILogger<ReplicationCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ReplicationCreatedEventHandler(ILoggedInUserService userService,
        ILogger<ReplicationCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ReplicationCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress ?? "::1",
            Entity = Modules.Replication.ToString(),
            Action = $"{ActivityType.Create} {Modules.Replication}",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Replication '{createdEvent.ReplicationName}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Replication '{createdEvent.ReplicationName}' created successfully.");
    }
}