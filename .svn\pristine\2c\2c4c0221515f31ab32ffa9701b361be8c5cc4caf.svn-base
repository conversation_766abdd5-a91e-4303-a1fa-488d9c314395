﻿namespace ContinuityPatrol.Application.Features.DataSet.Queries.GetDetail;

public class GetDataSetDetailQueryHandler : IRequestHandler<GetDataSetDetailQuery, DataSetDetailVm>
{
    private readonly IDataSetRepository _dataSetRepository;

    private readonly IMapper _mapper;

    public GetDataSetDetailQueryHandler(IMapper mapper, IDataSetRepository dataSetRepository)
    {
        _mapper = mapper;

        _dataSetRepository = dataSetRepository;
    }

    public async Task<DataSetDetailVm> Handle(GetDataSetDetailQuery request, CancellationToken cancellationToken)
    {
        var dataSet = await _dataSetRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(dataSet, nameof(Domain.Entities.DataSet),
            new NotFoundException(nameof(Domain.Entities.DataSet), request.Id));

        var siteDetailDto = _mapper.Map<DataSetDetailVm>(dataSet);

        return siteDetailDto;
    }
}