﻿using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.DriftProfile.Commands.Create;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.FormType.Queries.GetNames;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Domain.ViewModels.DriftProfileModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Drift.Controllers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Drift.Controllers;

public class DriftProfileControllerTests
{
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private readonly Mock<ILogger<DriftParameterController>> _mockLogger =  new();
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<IPublisher> _mockPublisher = new();
    private  DriftProfileController _controller;

    public DriftProfileControllerTests()
    { 
        Initialize ();
    }

    public void Initialize()
    { 
        _controller = new DriftProfileController(
            //_mockPublisher.Object,
            _mockLogger.Object,
            _mockDataProvider.Object,
            _mockMapper.Object, _mockPublisher.Object
        );
        _controller.ControllerContext.HttpContext = new DefaultHttpContext();
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
    }

    [Fact]
    public async Task AllList_ShouldReturnJsonResult_WithSuccess()
    {
        var mockDriftProfileList = new PaginatedResult<DriftProfileListVm>
        {
            Data = new List<DriftProfileListVm>()
        };

        _mockDataProvider
            .Setup(x => x.DriftProfile.GetPaginatedDriftProfiles(It.IsAny<GetDriftProfilePaginatedListQuery>()))
            .ReturnsAsync(mockDriftProfileList);

        var result = await _controller.GetPagination(new GetDriftProfilePaginatedListQuery());

        var jsonResult = result as JsonResult;
        var jsonData = jsonResult.Value as dynamic;
        Assert.NotNull(result);
    }

    [Fact]
    public async Task AllList_ShouldHandleException()
    {
        _mockDataProvider
            .Setup(x => x.DriftProfile.GetPaginatedDriftProfiles(It.IsAny<GetDriftProfilePaginatedListQuery>()))
            .ThrowsAsync(new Exception("Test exception"));

        var result = await _controller.GetPagination(new GetDriftProfilePaginatedListQuery());

        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetFormtypeName_ShouldReturnJsonResult_WithSuccess()
    {
        var mockFormTypeList = new List<FormTypeNameVm> { };

        _mockDataProvider
            .Setup(x => x.FormType.GetFormTypeNames())
            .ReturnsAsync(mockFormTypeList);

        var result = await _controller.GetFormTypeName();

        var jsonResult = result as JsonResult;
        var jsonData = jsonResult.Value as dynamic;
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
      
    }

    [Fact]
    public async Task ServerList_ShouldReturnJsonResult_WithFilteredSuccess()
    {
        var mockServerList = new List<ComponentTypeListVm> {
            new ComponentTypeListVm { ComponentName = "Server1" },
            new ComponentTypeListVm { ComponentName = "Server2" }
        };

        _mockDataProvider
            .Setup(x => x.ComponentType.GetComponentTypeList())
            .ReturnsAsync(mockServerList);

        var result = await _controller.GetComponentTypeList("Server1");

        var jsonResult = result as JsonResult;
        var jsonData = jsonResult.Value as dynamic;
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"success\":true", json);
        
    }
    [Fact]
    public async Task CreateOrUpdate_ShouldReturnJsonResult_WithSuccess_OnCreate()
    {
        var fixture = new AutoFixture.Fixture();
        var createDriftProfileCommand = fixture.Create<CreateDriftProfileCommand>();

        var formData = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
        {
            { "Id", "" }
        };
        var formCollection = new FormCollection(formData);

        var mockHttpContext = new Mock<HttpContext>();
        mockHttpContext.Setup(x => x.Request.Form).Returns(formCollection);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = mockHttpContext.Object
        };
        var parameterListVm = new DriftProfileListVm();

        var mockResponse = new BaseResponse { Success = true };

        _mockMapper
            .Setup(x => x.Map<CreateDriftProfileCommand>(parameterListVm))
            .Returns(createDriftProfileCommand);

        _mockDataProvider
            .Setup(x => x.DriftProfile.CreateAsync(It.IsAny<CreateDriftProfileCommand>()))
            .ReturnsAsync(mockResponse);

        var result = await _controller.CreateOrUpdate(parameterListVm);

        var jsonResult = result as JsonResult;
        Assert.NotNull(jsonResult); 

        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":true", json);  
    }


    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_IfExists()
    {
        var name = "TestName";
        var id = "123";
        _mockDataProvider
            .Setup(x => x.DriftProfile.IsDriftProfileNameExist(name, id))
            .ReturnsAsync(true);

        var result = await _controller.IsNameExist(name, id);

        Assert.True(result);
    }

    [Fact]
    public async Task Delete_data_ShouldReturnJsonResult_WithSuccess()
    {
        var id = "123";
        var mockResponse = new BaseResponse { Success = true };

        _mockDataProvider
            .Setup(x => x.DriftProfile.DeleteAsync(id))
            .ReturnsAsync(mockResponse);

        var result = await _controller.Delete(id);

        var jsonResult = result as JsonResult;
        var jsonData = jsonResult.Value as dynamic;
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":true", json);
    }
}