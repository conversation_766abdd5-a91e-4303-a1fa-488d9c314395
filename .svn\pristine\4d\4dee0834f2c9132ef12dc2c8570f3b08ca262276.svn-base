﻿using ContinuityPatrol.Application.Features.User.Queries.GetUserRole;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.User.Queries
{
    public class GetUserRoleQueryHandlerTests
    {
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly Mock<IUserRoleRepository> _mockUserRoleRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetUserRoleQueryHandler _handler;

        public GetUserRoleQueryHandlerTests()
        {
            _mockUserRepository = new Mock<IUserRepository>();
            _mockUserRoleRepository = new Mock<IUserRoleRepository>();
            _mockMapper = new Mock<IMapper>();

            _handler = new GetUserRoleQueryHandler(
                _mockMapper.Object,
                _mockUserRepository.Object,
                _mockUserRoleRepository.Object);
        }

        [Fact]
        public async Task Handle_ReturnsUserRoleVm_WhenDataIsValid()
        {
            var userId = Guid.NewGuid().ToString();
            var user = new Domain.Entities.User
            {
                ReferenceId = userId.ToString(),
                Role = "AdminRoleId"
            };
            var userRole = new Domain.Entities.UserRole
            {
                ReferenceId = "AdminRoleId",
                Role = "Admin"
            };
            var userRoleVm = new UserRoleVm { Role = "Admin" };

            _mockUserRepository.Setup(repo => repo.GetByReferenceIdAsync(userId)).ReturnsAsync(user);
            _mockUserRoleRepository.Setup(repo => repo.GetByReferenceIdAsync(user.Role)).ReturnsAsync(userRole);
            _mockMapper.Setup(mapper => mapper.Map<UserRoleVm>(user)).Returns(userRoleVm);

            var query = new GetUserRoleQuery { Id = userId };
            var cancellationToken = CancellationToken.None;

            var result = await _handler.Handle(query, cancellationToken);

            Assert.NotNull(result);
            Assert.Equal(userRole.Role, result.Role);

            _mockUserRepository.Verify(repo => repo.GetByReferenceIdAsync(userId), Times.Once);
            _mockUserRoleRepository.Verify(repo => repo.GetByReferenceIdAsync(user.Role), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<UserRoleVm>(user), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenUserIsNotFound()
        {
            var userId = Guid.NewGuid().ToString();
            _mockUserRepository.Setup(repo => repo.GetByReferenceIdAsync(userId)).ReturnsAsync((Domain.Entities.User)null);

            var query = new GetUserRoleQuery { Id = userId };
            var cancellationToken = CancellationToken.None;

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, cancellationToken));

            _mockUserRepository.Verify(repo => repo.GetByReferenceIdAsync(userId), Times.Once);
            _mockUserRoleRepository.Verify(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()), Times.Never);
            _mockMapper.Verify(mapper => mapper.Map<UserRoleVm>(It.IsAny<Domain.Entities.User>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenUserRoleIsNotFound()
        {
            var userId = Guid.NewGuid().ToString();
            var user = new Domain.Entities.User
            {
                ReferenceId = userId.ToString(),
                Role = "InvalidRoleId"
            };

            _mockUserRepository.Setup(repo => repo.GetByReferenceIdAsync(userId)).ReturnsAsync(user);
            _mockUserRoleRepository.Setup(repo => repo.GetByReferenceIdAsync(user.Role)).ReturnsAsync((Domain.Entities.UserRole)null);

            var query = new GetUserRoleQuery { Id = userId };
            var cancellationToken = CancellationToken.None;

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, cancellationToken));

            _mockUserRepository.Verify(repo => repo.GetByReferenceIdAsync(userId), Times.Once);
            _mockUserRoleRepository.Verify(repo => repo.GetByReferenceIdAsync(user.Role), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<UserRoleVm>(It.IsAny<Domain.Entities.User>()), Times.Never);
        }
    }
}
