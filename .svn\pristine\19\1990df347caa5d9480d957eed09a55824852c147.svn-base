﻿namespace ContinuityPatrol.Application.Features.WorkflowExecutionTemp.Commands.Update;

public class UpdateWorkflowExecutionTempCommand : IRequest<UpdateWorkflowExecutionTempResponse>
{
    public string Id { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string Properties { get; set; }

    public override string ToString()
    {
        return $"Workflow Name: {WorkflowName}; Id:{Id};";
    }
}