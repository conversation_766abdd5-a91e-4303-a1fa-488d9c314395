﻿using ContinuityPatrol.Domain.ViewModels.UserLoginModel;

namespace ContinuityPatrol.Application.Features.UserLogin.Queries.GetList;

public class GetUserLoginListQueryHandler : IRequestHandler<GetUserLoginListQuery, List<UserLoginListVm>>
{
    private readonly IMapper _mapper;
    private readonly IUserLoginRepository _userLoginRepository;

    public GetUserLoginListQueryHandler(IMapper mapper, IUserLoginRepository userLoginRepository)
    {
        _mapper = mapper;
        _userLoginRepository = userLoginRepository;
    }

    public async Task<List<UserLoginListVm>> Handle(GetUserLoginListQuery request, CancellationToken cancellationToken)
    {
        var userLogin = await _userLoginRepository.ListAllAsync();


        return userLogin.Count <= 0 ? new List<UserLoginListVm>() : _mapper.Map<List<UserLoginListVm>>(userLogin);
    }
}