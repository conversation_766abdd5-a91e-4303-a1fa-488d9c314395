﻿using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.ComponentType.Commands.Create;

public class CreateComponentTypeCommandValidator : AbstractValidator<CreateComponentTypeCommand>
{
    private readonly IComponentTypeRepository _componentTypeRepository;

    public CreateComponentTypeCommandValidator(IComponentTypeRepository componentTypeRepository)
    {
        _componentTypeRepository = componentTypeRepository;

        RuleFor(p => p.FormTypeName)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^[a-zA-Z\d]+([_\s\-\.][a-zA-Z\d]+)*$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();
        RuleFor(p => p.ComponentName)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^[a-zA-Z\d]+([_\s\-\.\][a-zA-Z\d]+)*$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.")
            .NotNull();
        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .NotNull()
            .Must(p => IsValidJsonObjcet(p)).WithMessage("{PropertyName} must be a valid json string.");

        RuleFor(p => p)
            .MustAsync(IsComponentTypeNameUnique)
            .WithMessage("A same name already exists");
    }

    public async Task<bool> IsComponentTypeNameUnique(CreateComponentTypeCommand createComponentTypeCommand,
        CancellationToken cancellationToken)
    {
        return !await _componentTypeRepository.IsComponentTypeNameExist(createComponentTypeCommand.ComponentName,
            string.Empty);
    }


    private bool IsValidJsonObjcet(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                var obj = JsonConvert.DeserializeObject(properties);
                return true;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }
}