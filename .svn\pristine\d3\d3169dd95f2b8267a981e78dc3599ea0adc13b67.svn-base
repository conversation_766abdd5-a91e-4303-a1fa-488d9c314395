﻿using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Mocks;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using Moq;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class LicenseManagerControllerTests
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IDataProvider> _mockProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<ILogger<LicenseManagerController>> _mockLogger = new();
        private  LicenseManagerController _controller;

        public LicenseManagerControllerTests()
        {
            Initialze();
        }
        public void Initialze()
        { 
        _controller = new LicenseManagerController
            (_mockPublisher.Object,
             _mockProvider.Object,
             _mockMapper.Object,
             _mockLogger.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public async Task List_ReturnsViewResult_WithBaseLicenseViewModel()
        {
            var licenseDetails = new List<LicenseManagerDetailViewVm>();
            var licenseInfoDetails = new List<LicenseInfoListVm>();
            var companyNames = new List<CompanyListVm>();
            var licenseMangerPoVm = new List<LicenseManagerNameVm>();

            _mockProvider.Setup(p => p.LicenseManager.LicenseManagerDetailView()).ReturnsAsync(licenseDetails);
            _mockProvider.Setup(p => p.LicenseInfo.GetLicenseInfo()).ReturnsAsync(licenseInfoDetails);
            _mockProvider.Setup(p => p.Company.GetCompanies()).ReturnsAsync(companyNames);
            _mockProvider.Setup(p => p.LicenseManager.GetAllPoNumbers()).ReturnsAsync(licenseMangerPoVm);

            _mockMapper.Setup(m => m.Map<List<SelectListItem>>(It.IsAny<List<CompanyViewModel>>()))
                .Returns(new List<SelectListItem>());

            var result = await _controller.List();

            var viewResult = Assert.IsType<ViewResult>(result);
            var model = Assert.IsType<BaseLicenseViewModel>(viewResult.Model);
            Assert.Equal(licenseDetails, model.LicenseManagerDetails);
            Assert.Equal(licenseInfoDetails, model.LicenseInfoLists);
        }

        [Fact]
        public async Task CreateOrUpdate_ValidLicense_ReturnsRedirectToActionResult()
        {
            var viewModel = new Fixture().Create<BaseLicenseViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateBaseLicenseCommand();

            var baseLicenseViewModel = new BaseLicenseViewModel();
            var createCommand = new CreateBaseLicenseCommand();
            var updateCommand = new UpdateBaseLicenseCommand();

            _mockMapper.Setup(m => m.Map<CreateBaseLicenseCommand>(baseLicenseViewModel)).Returns(createCommand);
            _mockMapper.Setup(m => m.Map<UpdateBaseLicenseCommand>(baseLicenseViewModel)).Returns(updateCommand);

            _mockProvider.Setup(p => p.LicenseManager.CreateBaseLicense(createCommand))
                .ReturnsAsync(new BaseResponse { Message = "License created successfully" });

            _mockProvider.Setup(p => p.LicenseManager.UpdateBaseLicense(updateCommand))
                .ReturnsAsync(new BaseResponse { Message = "License updated successfully" });

            var result = await _controller.CreateOrUpdate(baseLicenseViewModel);

            var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectToActionResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_ThrowsValidationException_ReturnsRedirectToActionResult()
        {
            var viewModel = new Fixture().Create<BaseLicenseViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createcommand = new CreateBaseLicenseCommand { 
                 
                LicenseKey = "id",
            };
			var updatecommand = new UpdateBaseLicenseCommand();
			var baseLicenseViewModel = new BaseLicenseViewModel();
            var validationErrors = new List<BaseResponse> ();
			_mockMapper.Setup(m => m.Map<CreateBaseLicenseCommand>(viewModel)).Returns(createcommand);
			_mockMapper.Setup(m => m.Map<UpdateBaseLicenseCommand>(viewModel)).Returns(updatecommand);
			_mockProvider.Setup(p => p.LicenseManager.CreateBaseLicense(createcommand))
                .ThrowsAsync(new ());
			var licenseKey = SecurityHelper.Decrypt(viewModel.LicenseKey);
			var poNumber = licenseKey.Split("*");
			_mockProvider.Setup(p => p.LicenseManager.GetLicenseDetailsByPoNumber(poNumber[0])).ReturnsAsync(new Application.Features.LicenseManager.Queries.GetLicenseByPONumber.GetLicenseByPONumberVm { });
			var companyList = new List<UserSession> { new UserSession {ParentCompanyId="r" } };
			_controller.ControllerContext = new ControllerContextMocks().Default();
			WebHelper.CurrentSession.Set("Session", companyList);

			var result = await _controller.CreateOrUpdate(viewModel);

            var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("LicenseLanding", redirectToActionResult.ActionName);
        }

        [Fact]
        public async Task Delete_ValidId_ReturnsRedirectToActionResult()
        {         
            var id = "123";
            _mockProvider.Setup(p => p.LicenseManager.DeleteAsync(id));
                
            var result = await _controller.Delete(id);

            var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectToActionResult.ActionName);
        }

        [Fact]
        public async Task Delete_ThrowsException_ReturnsRedirectToActionResult()
        {
            var id = "123";
            var exceptionMessage = "An error occurred";

            _mockProvider.Setup(p => p.LicenseManager.DeleteAsync(id))
                .ThrowsAsync(new System.Exception(exceptionMessage));

            var result = await _controller.Delete(id);

            var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectToActionResult.ActionName);
        }
    }
}
