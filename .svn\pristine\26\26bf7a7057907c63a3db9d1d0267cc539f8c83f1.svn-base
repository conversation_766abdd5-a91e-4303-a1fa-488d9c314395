﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfile.Queries;

public class GetWorkflowProfileNameUniqueQueryHandlerTests : IClassFixture<WorkflowProfileFixture>
{
    private readonly WorkflowProfileFixture _workflowProfileFixture;

    private Mock<IWorkflowProfileRepository> _mockWorkflowProfileRepository;

    private readonly GetWorkflowProfileNameUniqueQueryHandler _handler;

    public GetWorkflowProfileNameUniqueQueryHandlerTests(WorkflowProfileFixture workflowProfileFixture)
    {
        _workflowProfileFixture = workflowProfileFixture;

        _mockWorkflowProfileRepository = WorkflowProfileRepositoryMocks.GetWorkflowProfileNameUniqueRepository(_workflowProfileFixture.WorkflowProfiles);

        _handler = new GetWorkflowProfileNameUniqueQueryHandler(_mockWorkflowProfileRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_WorkflowProfileName_Exist()
    {
        _workflowProfileFixture.WorkflowProfiles[0].Name = "PR_Site";
        _workflowProfileFixture.WorkflowProfiles[0].IsActive = true;

        var result = await _handler.Handle(new GetWorkflowProfileNameUniqueQuery { ProfileId = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId, ProfileName = _workflowProfileFixture.WorkflowProfiles[0].Name }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_WorkflowProfileNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowProfileNameUniqueQuery { ProfileName = "Testing", ProfileId = 1.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_WorkflowProfileName_NotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowProfileNameUniqueQuery { ProfileName = "Demo", ProfileId = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsWorkflowProfileNameExist_OneTime()
    {
        await _handler.Handle(new GetWorkflowProfileNameUniqueQuery(), CancellationToken.None);

        _mockWorkflowProfileRepository.Verify(x => x.IsWorkflowProfileNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockWorkflowProfileRepository = WorkflowProfileRepositoryMocks.GetWorkflowProfileEmptyRepository();

        var result = await _handler.Handle(new GetWorkflowProfileNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}