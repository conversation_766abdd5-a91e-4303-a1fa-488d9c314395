using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapStatusModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class CyberAirGapStatussController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<List<CyberAirGapStatusListVm>>> GetCyberAirGapStatuss()
    {
        Logger.LogDebug("Get All CyberAirGapStatuss");

        return Ok(await Mediator.Send(new GetCyberAirGapStatusListQuery()));
    }

    [HttpGet("{id}", Name = "GetCyberAirGapStatus")]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<CyberAirGapStatusDetailVm>> GetCyberAirGapStatusById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberAirGapStatus Id");

        Logger.LogDebug($"Get CyberAirGapStatus Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetCyberAirGapStatusDetailQuery { Id = id }));
    }

    #region Paginated
    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<PaginatedResult<CyberAirGapStatusListVm>>> GetPaginatedCyberAirGapStatuss([FromQuery] GetCyberAirGapStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in CyberAirGapStatus Paginated List");

        return Ok(await Mediator.Send(query));
    }
    #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Cyber.Create)]
    public async Task<ActionResult<CreateCyberAirGapStatusResponse>> CreateCyberAirGapStatus([FromBody] CreateCyberAirGapStatusCommand createCyberAirGapStatusCommand)
    {
        Logger.LogDebug($"Create CyberAirGapStatus '{createCyberAirGapStatusCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateCyberAirGapStatus), await Mediator.Send(createCyberAirGapStatusCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Cyber.Edit)]
    public async Task<ActionResult<UpdateCyberAirGapStatusResponse>> UpdateCyberAirGapStatus([FromBody] UpdateCyberAirGapStatusCommand updateCyberAirGapStatusCommand)
    {
        Logger.LogDebug($"Update CyberAirGapStatus '{updateCyberAirGapStatusCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateCyberAirGapStatusCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Cyber.Delete)]
    public async Task<ActionResult<DeleteCyberAirGapStatusResponse>> DeleteCyberAirGapStatus(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberAirGapStatus Id");

        Logger.LogDebug($"Delete CyberAirGapStatus Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteCyberAirGapStatusCommand { Id = id }));
    }

    [HttpPut,Route("UpdateStatus")]
    [Authorize(Policy = Permissions.Cyber.Edit)]
    public async Task<ActionResult<UpdateAirGapStatusResponse>> UpdateStatus([FromBody] UpdateAirGapStatusCommand updateAirGapStatusCommand)
    {
        Logger.LogDebug($"Update AirGapStatus '{updateAirGapStatusCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateAirGapStatusCommand));
    }


    #region NameExist
    [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsCyberAirGapStatusNameExist(string cyberAirGapStatusName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(cyberAirGapStatusName, "CyberAirGapStatus Name");

     Logger.LogDebug($"Check Name Exists Detail by CyberAirGapStatus Name '{cyberAirGapStatusName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetCyberAirGapStatusNameUniqueQuery { Name = cyberAirGapStatusName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


