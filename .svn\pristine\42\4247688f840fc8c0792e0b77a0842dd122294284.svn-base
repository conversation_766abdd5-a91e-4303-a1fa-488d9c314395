﻿using ContinuityPatrol.Domain.ViewModels.SingleSignOnModel;

namespace ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetList;

public class GetSingleSignOnListQueryHandler : IRequestHandler<GetSingleSignOnListQuery, List<SingleSignOnListVm>>
{
    private readonly IMapper _mapper;
    private readonly ISingleSignOnRepository _singleSignOnRepository;

    public GetSingleSignOnListQueryHandler(IMapper mapper, ISingleSignOnRepository singleSignOnRepository)
    {
        _mapper = mapper;
        _singleSignOnRepository = singleSignOnRepository;
    }

    public async Task<List<SingleSignOnListVm>> Handle(GetSingleSignOnListQuery request,
        CancellationToken cancellationToken)
    {
        var singleSignOnList = new List<SingleSignOnListVm>();

        var allSingleSignOn = (await _singleSignOnRepository.ListAllAsync()).ToList();

        if (allSingleSignOn.Count > 0)
            foreach (var singleSignOn in allSingleSignOn)
            {
                var singleSignOnItem = _mapper.Map<SingleSignOnListVm>(singleSignOn);

                singleSignOnList.Add(singleSignOnItem);
            }

        return singleSignOnList;
    }
}