﻿using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDrDrillByBusinessServiceId;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IWorkflowOperationRepository : IRepository<WorkflowOperation>
{
    Task<List<WorkflowOperation>> GetWorkflowOperationNames();
    Task<List<WorkflowOperation>> GetWorkflowOperationByRunningStatus();
    Task<List<WorkflowOperation>> GetWorkflowOperationByRunningUsers();
    Task<List<WorkflowOperation>> GetWorkflowOperationByRunningUserId(List<string> userId);
    Task<List<WorkflowOperation>> GetDescriptionByStartTimeAndEndTime(string startDate, string endDate);
    Task<List<WorkflowOperation>> GetWorkflowOperationByProfileId(string profileId);
    Task<List<WorkflowOperation>> GetWorkflowOperationByProfileIdAndStatus(string profileId, string status);
    Task<WorkflowOperation> GetByReferenceIdAndRunMode(string id, string runMode);
    Task<List<WorkflowOperation>> GetWorkflowOperationByOperationId(string operationId);
    Task<IReadOnlyList<WorkflowOperation>> GetFilterListAsync();

    Task<(List<WorkflowOperationDrDrillVm> WorkflowOperationDrDrillVm, List<string> InfraIds)>
        GetDrillDetailsByBusinessServiceId(string businessServiceId);
    Task<WorkflowOperation> GetLastWorkflowOperation();
}