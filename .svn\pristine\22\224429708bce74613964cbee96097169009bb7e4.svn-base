﻿const componentURL = {
    NameExist: "Admin/ComponentType/ComponentTypeNameExist",
    GetFormTypeNames: "Admin/FormType/GetFormTypeNames",
};

let selectedValues = [], versions = [], btnDisableComponentType = false, dataTable = "";

$('#DeleteModal').attr('data-bs-backdrop', 'static');

$(async function () {
    preventSpecialKeys('#search-inp, #componentTypeName'); //commonfunctions.js

    $('#componentTypelogo').hide();

    dataTable = $('#componentTypeTable').DataTable(
        {
            language: {
                decimal: ",",
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous" ></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            processing: true,
            serverSide: true,
            filter: true,
            Sortable: true,
            order: [],
            fixedColumns: { left: 1, right: 1 },
            "ajax": {
                "type": "GET",
                "url": "/Admin/ComponentType/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    const sortIndex = d?.order[0]?.column ?? '';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length ? selectedValues.join(';') : $('#search-inp').val();
                    d.sortColumn = ["", "formTypeName", "componentName", "version"][sortIndex] ?? "";;
                    d.SortOrder = d?.order[0]?.dir || 'asc';;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json?.success) {
                        const data = json?.data;
                        json.recordsTotal = data?.totalPages;
                        json.recordsFiltered = data?.totalCount;
                        data?.data?.forEach(d => d.properties = JSON.parse(d.properties));
                        $(".pagination-column").toggleClass("disabled", !data?.data?.length);
                        return data?.data;
                    }
                    else {
                        errorNotification(json);
                        return [];
                    }
                },
            },
            "columnDefs": [{ "targets": [1, 2, 3], "className": "truncate" }],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta?.row + 1;
                        }
                        return data;
                    }
                },
                {
                    "data": "formTypeName", "name": "Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title='${data || 'NA'}'>${data || 'NA'} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": null, name: 'Name', autowidth: true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            const { name = 'NA', icon = 'NA' } = row?.properties || {};
                            return `<span title="${name}"><i class='${icon} me-1'></i>${name}</span>`;
                        }
                        const { name = 'NA', icon = 'NA' } = row?.properties || {};
                        return `<span title="${name}"><i class='${icon} me-1'></i>${name}</span>`;
                    }
                },
                {
                    "data": 'properties', name: 'version', autowidth: true,
                    "render": function (data, type) {
                        const version = data?.version?.replace(/[[\]"\s]/g, "") || 'NA';
                        if (type === 'display') {
                            return `<span title='${version}'>${version}</span>`;
                        }
                        return `<span title='${version}'>${version}</span>`;
                    }
                },
                {
                    "orderable": false,
                    "render": function (data, type, row) {
                        if (row?.isMapped) {
                            return `
                                <div class="d-flex align-items-center gap-2">                   
                                    <span role="button" title="Edit" class="form-delete-disable">
                                        <i class="cp-edit"></i>
                                    </span>
                                    <span role="button" title="Delete" class="form-delete-disable">
                                        <i class="cp-Delete "></i>
                                    </span>                 
                                </div>`;
                        } else {
                            return `
                                <div class="d-flex align-items-center gap-2">                   
                                    <span role="button" title="Edit" class="edit-button" data-servertype='${JSON.stringify(row)}'>
                                        <i class="cp-edit"></i>
                                    </span>
                                    <span role="button" title="Delete" class="delete-button" data-servertype-id="${row?.id}" 
                                            data-name="${row?.properties?.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete "></i>
                                    </span>                 
                                </div>`;
                        }
                    }
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api?.context[0]?._iDisplayStart; // Get the start index of displayed data
                var counter = startIndex + index + 1; // Calculate the serial number based on start index and index
                $('td:eq(0)', row).html(counter); // Update the cell in the first column with the serial number
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },
        });

    $('#search-inp').on('input', commonDebounce(async function (e) {
        let sanitizedValue = $(this).val().replace(/^\s/, '').replace(/\s+/g, ' ').replace(/\s$/, ' ');

        if (sanitizedValue.trim() === "") {
            $(this).val("");
            sanitizedValue = "";
        } else {
            $(this).val(sanitizedValue);
        }

        const checkboxes = { type: $("#compType"), name: $("#compName"), version: $("#compVersion") };
        Object.entries(checkboxes).forEach(([key, checkbox]) => {
            if (checkbox.is(':checked')) {
                selectedValues.push(checkbox.val() + sanitizedValue);
            }
        });

        dataTable.ajax.reload(function (json) {
            const message = sanitizedValue.length === 0 ? 'No Data Found' : 'No matching records found';
            $('.dataTables_empty').text(json?.recordsFiltered === 0 ? message : '');
        })

    }));

    $('#componentTypeName').on('keyup', commonDebounce(function () {
        const val = $(this).val().replace(/\s{2,}/g, ' ');
        $(this).val(val);
        $('#componentName').val(val);
        let data = {
            serverTypeId: $('#textServerTypeId').val(),
            serverTypeName: val
        };
        moduleNameValidation('componenttype', val, componentURL.NameExist, $("#componentNameError"), "Enter name", data); //commonfunctions.js
    }));

    //Save
    $("#componentTypeSaveButton").on("click", async function (event) {
        const buttons = document.querySelectorAll(".remove-button");
        const componentVersion = buttons.length ? JSON.stringify([...buttons].map(b => b.textContent.replace("X", "").trim())) : "";
        let componentType = $("#selectComponentType").val();
        let data = {
            serverTypeId: $('#textServerTypeId').val(),
            serverTypeName: $('#componentTypeName').val()
        };
        let serverTypeValidation = await moduleNameValidation('componenttype', $('#componentTypeName').val(), componentURL.NameExist, $("#componentNameError"), "Enter name", data); //commonfunctions.js
        let verValidation = componentVersionValidation(componentVersion, " Add version", "componentTypeVersionError");
        let TypeValidation = componentTypeValidation(componentType, " Select type", "componentTypeError");

        if (serverTypeValidation && verValidation && TypeValidation) {
            if (componentType === 'Replication') {
                enableInfraComponent('isServerChecked', '#enableInfraServer');
                enableInfraComponent('isDatabaseChecked', '#enableInfraDatabase');
                enableInfraComponent('isReplicationChecked', '#enableInfrareplication');
                enableInfraComponent('isClusterChecked', '#enableInfraIsCluster');
            } else {
                $('#enableInfraServer, #enableInfraDatabase, #enableInfrareplication, #enableInfraIsCluster').val(false);
            }

            $("#versionDatas").val(componentVersion);
            let iconValue = $("#componentTypeIcon").val() || document.getElementById('componentTypelogo').className;
            $("#iconName").val(iconValue);
            let propertiesObject = {
                name: $("#componentTypeName").val(),
                version: componentVersion,
                icon: iconValue
            };

            if (!btnDisableComponentType) {
                btnDisableComponentType = true;
                let encryption = await propertyEncryption(propertiesObject);
                $("#propertiesData").val(encryption)

                const form = $('#createComponentForm')[0];
                const formData = new FormData(form);

                let response = await createOrUpdate(RootUrl + "Admin/ComponentType/CreateOrUpdate", formData); //commonfunctions.js
                $('#CreateModal').modal('hide');
                btnDisableComponentType = false;

                if (response?.success) {
                    notificationAlert("success", response?.data?.message);
                    setTimeout(() => {
                        dataTableCreateAndUpdate($("#componentTypeSaveButton"), dataTable);
                    }, 2000)
                } else {
                    errorNotification(response);
                }
            }
        }
    });

    $("#CreateModalForm").on("click", function () {
        $('#componentTypeSaveButton').text('Save');
        $('#textServerTypeId').val('');
        $(".hideForReplication").show();
        $(".hideForSelectVersion, .hideInfraComponent, #componentTypelogo").hide();
        $("#selectedVersionDetails").empty();
        clearServerTypeErrorMessage();
        versions = [];
        let myCollapse = new bootstrap.Collapse(document.getElementById('collapseExample'), {
            toggle: false
        });
        myCollapse.hide();
    });

    $(".disable-button").css({
        "pointer-events": "none",
        "color": "gray",
        "cursor": "not-allowed"
    });

    //delete
    $('#componentTypeTable')
        .on('click', '.delete-button', function () {
            $("#deleteData").attr("title", $(this).data('name')).text($(this).data('name'));
            $('#textDeleteId').val($(this).data('servertype-id'));
        })
        .on('click', '.edit-button', function () {
            clearServerTypeErrorMessage();
            populateDatabaseModalFields($(this).data("servertype"));
            $('#componentTypeSaveButton').text('Update');
            $('#CreateModal').modal('show');
        });

    $("#confirmDeleteButton").on("click", async function () {
        const form = $('#deleteComponentType')[0];
        const formData = new FormData(form);

        if (!btnDisableComponentType) {
            btnDisableComponentType = true;
            let response = await deleteData(RootUrl + "Admin/ComponentType/Delete", formData); //commonfunctions.js
            $("#DeleteModal").modal("hide");
            btnDisableComponentType = false;

            if (response?.success) {               
                notificationAlert("success", response?.data?.message);              
                setTimeout(() => {
                    dataTableDelete(dataTable);
                }, 2000)
            } else {
                errorNotification(response);
            }
        }
    });

    $('#Server_icons, #Database_icons').hide();

    $('#selectComponentType').on('change', function () {
        let component = $('#selectComponentType').val();
        $("#formTypeId").val($("#selectComponentType :selected").attr("id"));
        $('#Server_icons').toggle(component === 'Server');
        $('#Database_icons').toggle(component === 'Database');
        $('#collapseExample').removeClass('show');
        validateComponentType();
    });
    formTypeNames();
});

$(document).on('click', '#iconchange td', function () {
    const className = this.firstElementChild?.className?.split(' ')[0];
    $('#componentTypelogo').removeClass().addClass(className);
    $('#componentTypeIcon').val(className);
    $('#collapseExample').removeClass('show')
});

//functions

async function formTypeNames(value = null) {
    let result = await getRequest(RootUrl + componentURL.GetFormTypeNames); //commonfunctions.js
    if (result?.length) {
        const options = [];
        let componentType = $('#selectComponentType');
        componentType.empty().append($('<option>').val("").text("Select Type"));
        result?.forEach(function (item) {
            const formTypeName = item?.formTypeName || "";
            const modifiedType = formTypeName?.toLowerCase()?.replace(/\s+/g, "");
            const displayText = modifiedType === "singlesignon" ? "Single Sign-On" : formTypeName;
            options.push($('<option>').val(formTypeName).text(displayText).attr('id', item?.id)
            );
        });
        componentType.append(options);

        if (value) {
            $('#selectComponentType').val(value).trigger('change');
        }
    }
}

async function validateComponentType() {
    const serverTableBody =
        `<tbody id="serverIcon">
            <tr>
                <td><i title="Server" style="cursor:pointer" class="cp-server custom-cursor-on-hover"></i></td>
                <td><i title="IBM-AIX" style="cursor:pointer" class="cp-IBM-AIX custom-cursor-on-hover"></i></td>
                <td><i title="HP" style="cursor:pointer" class="cp-hp custom-cursor-on-hover"></i></td>
                <td><i title="Linux" style="cursor:pointer" class="cp-linux" cursorshover="true"></i></td>
                <td><i title="Solaris" style="cursor:pointer" class="cp-solaris custom-cursor-on-hover" cursorshover="true"></i></td>
                <td><i title="MSSQL" style="cursor:pointer" class="cp-mssql custom-cursor-on-hover"></i></td>
                <td><i title="Windows" style="cursor:pointer" class="cp-windows custom-cursor-on-hover" cursorshover="true"></i></td>
            </tr>
            <tr>
                <td><i title="AIX" class="cp-aix custom-cursor-on-hover" cursorshover="true"></i></td>
            </tr>
    </tbody>`;

    const databaseTableBody =
        `<tbody id="databaseIcon">
            <tr>
                <td><i title="Database" style="cursor:pointer" class="cp-database custom-cursor-on-hover"></i></td>
                <td><i title="Oracle" style="cursor:pointer" class="cp-oracle custom-cursor-on-hover"></i></td>
                <td><i title="Postgres" style="cursor:pointer" class="cp-postgres custom-cursor-on-hover" cursorshover="true"></i></td>
                <td><i title="MYSQL" style="cursor:pointer" class="cp-mysql custom-cursor-on-hover" cursorshover="true"></i></td>
                <td><i title="MSSQL" style="cursor:pointer" class="cp-mssql custom-cursor-on-hover"></i></td>
                <td><i title="Mongo DB" style="cursor:pointer" class="cp-mongo-db"></i></td>
                <td><i title="IBM-AIX" style="cursor:pointer" class="cp-IBM-AIX custom-cursor-on-hover"></i></td>
            </tr>
            <tr>
                <td><i title="Mailing System" style="cursor:pointer" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                <td><i title="Nutanix" style="cursor:pointer" class="cp-nutanix custom-cursor-on-hover"></i></td>
                <td><i title="NetApp" style="cursor:pointer" class="cp-power-cli" cursorshover="true"></i></td>
                <td><i title="VMware" style="cursor:pointer" class="cp-vmware custom-cursor-on-hover"></i></td>
                <td><i title="Rsync" style="cursor:pointer" class="cp-rsync custom-cursor-on-hover"></i></td>
                <td><i title="EMC" style="cursor:pointer" class="cp-EMC custom-cursor-on-hover"></i></td>
                <td><i title="Oracle Ops Center" style="cursor:pointer" class="cp-microsoft custom-cursor-on-hover"></i></td>
            </tr>
            <tr>               
                <td><i title="Veritas Cluster" class="cp-veritas-cluster custom-cursor-on-hover"></i></td>
            </tr>
    </tbody>`;

    const replicationTableBody =
        `<tbody id="replicationIcon">
        <div>
            <tr>
                <td><i title="Replication Type" style="cursor:pointer" class="cp-replication-type custom-cursor-on-hover" cursorshover="true"></i></td>
                <td><i title="Oracle" style="cursor:pointer" class="cp-oracle custom-cursor-on-hover"></i></td>
                <td><i title="Postgres" style="cursor:pointer" class="cp-postgres custom-cursor-on-hover" cursorshover="true"></i></td>
                <td><i title="MYSQL" style="cursor:pointer" class="cp-mysql custom-cursor-on-hover" cursorshover="true"></i></td>
                <td><i title="MSSQL" style="cursor:pointer" class="cp-mssql custom-cursor-on-hover"></i></td>
                <td><i title="Mongo DB" style="cursor:pointer" class="cp-mongo-db"></i></td>
                <td><i title="IBM-AIX" style="cursor:pointer" class="cp-IBM-AIX custom-cursor-on-hover"></i></td>
            </tr>
            <tr>
                <td><i title="Mailing System" style="cursor:pointer" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                <td><i title="EMC" style="cursor:pointer" class="cp-EMC custom-cursor-on-hover"></i></td>
                <td><i title="Nutanix" style="cursor:pointer" class="cp-nutanix custom-cursor-on-hover"></i></td>
                <td><i title="NetApp" style="cursor:pointer" class="cp-power-cli" cursorshover="true"></i></td>
                <td><i title="VMware" style="cursor:pointer" class="cp-vmware custom-cursor-on-hover"></i></td>
                <td><i title="Rsync" style="cursor:pointer" class="cp-rsync-options custom-cursor-on-hover" cursorshover="true"></i></td>
                <td><i title="RoboCopy" style="cursor:pointer" class="cp-robocopy custom-cursor-on-hover"></i></td>                
            </tr>
             <tr>
                <td><i title="Oracle Ops Center" style="cursor:pointer" class="cp-microsoft custom-cursor-on-hover"></i></td>
            </tr>          
        </div>
    </tbody>`;

    const singleSignOnTableBody =
        `<tbody id="singleSignonIcon">
            <tr>
                <td><i title="Single Sign-On" style="cursor:pointer" class="cp-single-sign_on custom-cursor-on-hover" cursorshover="true"></i></td>
            </tr>
    </tbody>`;

    const nodeTableBody =
        `<tbody id="nodeIcon">
            <tr>
                <td><i title="Node" style="cursor:pointer" class="cp-network custom-cursor-on-hover" cursorshover="true"></i></td>
            </tr>
    </tbody>`;

    const OtherTypeTableBody =
        `<tbody id="otherType">
            <tr>
                <td><i title="Mapping" style="cursor:pointer" class="cp-mapping custom-cursor-on-hover" cursorshover="true"></i></td>
            </tr>
    </tbody>`;

    const componentTableBodies = {
        "Server": serverTableBody,
        "Database": databaseTableBody,
        "Replication": replicationTableBody,
        "Single SignOn": singleSignOnTableBody,
        "Node": nodeTableBody
    };

    const componentIcons = {
        "Server": "cp-server",
        "Database": "cp-database",
        "Replication": "cp-replication-rotate",
        "Single SignOn": "cp-single-sign_on",
        "Node": "cp-network",
    };

    let componentType = $("#selectComponentType").val();
    $('#componentTypelogo').show();
    let table = document.getElementById('iconchange');
    while (table.firstChild) {
        table.removeChild(table.firstChild);
    }
    const selectedTableBody = componentTableBodies[componentType];
    table.insertAdjacentHTML('beforeend', selectedTableBody ? selectedTableBody : OtherTypeTableBody);
    const icon = componentIcons[componentType] || "cp-mapping";
    //let tdElements = document.getElementById('componentTypelogo');
    //tdElements.className = "cp-images";
    $("#componentTypelogo").attr("class", icon);
    $('#componentTypeIcon').val(icon);

    $('#collapseExample').addClass('hide');
    $("#componentNameError, #componentTypeVersionError").text("").removeClass('field-validation-error');
    componentTypeValidation(componentType, " Select type", "componentTypeError");
};

function componentTypeValidation(value, errorMessage, errorElement) {
    if (value === "Server" || value === "Database" || value == "") {
        $(".hideForReplication").show();
    } else {
        $(".hideForReplication, .hideForSelectVersion").hide();
        $("#selectedVersionDetails").empty();
    }
    $(".hideInfraComponent").toggle(value === "Replication");
    //if (value === "Server") {
    //    $(".hideInfraComponent").show();
    //} else {
    //    $(".hideInfraComponent").hide();
    //}
    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}

function validateComponentVersion() {
    let version = $("#selectedVersionDetails").val() || $("#componentTypeVersion").val();
    let inputField = document.getElementById('componentTypeVersion');
    inputField.addEventListener('keydown', function (event) {
        if (event.key === ' ') {
            const value = this.value;
            const selectionStart = this.selectionStart;
            const len = value.trim().length;
            if (len < selectionStart) {
                $('#componentTypeVersionError').text("Should not end with space").addClass('field-validation-error');
                event.preventDefault(); // Prevent adding more than two consecutive spaces
            }
        }
    });
    componentVersionValidation(version, " Add version", "componentTypeVersionError");
};

function componentVersionValidation(value, errorMessage, errorElement) {
    let componentType = $("#selectComponentType").val();
    if (componentType == "Server" || componentType == "Database" || componentType === "") {
        if (!value) {
            $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            $('#' + errorElement).text('').removeClass('field-validation-error');
            return true;
        }
    } else {
        $(".hideForReplication").hide();
        return true;
    }
}

function addVersion(version) {
    let $componentTypeVersionError = $("#componentTypeVersionError");
    $componentTypeVersionError.text("").removeClass('field-validation-error');
    if (version?.includes(',')) {
        versions?.push(...version?.split(','));
    } else if (versions?.includes(version)) {
        $componentTypeVersionError.text("Version already exists").addClass('field-validation-error');
    } else {
        $componentTypeVersionError.text("").removeClass('field-validation-error');
        versions?.push(version);
    }
    updateSelectedVersions();
}

function updateSelectedVersions() {
    $("#selectedVersionDetails").empty();
    if (versions?.length === 0) {
        $(".hideForSelectVersion").hide();
    }
    versions?.forEach((version, index) => {
        const versionElement = document.createElement("span");
        versionElement.style.marginRight = "10px";
        const addValue = document.createElement("button");
        addValue.innerHTML = `${version}<span >&nbsp; X</span>`;
        addValue.className = `remove-button btn btn-secondary rounded-pill btn-sm shadow mt-2`;
        addValue.title = "Remove";
        addValue.addEventListener("click", function () {
            removeVersion(index);
        });
        versionElement.appendChild(addValue);
        $("#selectedVersionDetails").append(versionElement);
    });
}

function removeVersion(index) {
    versions.splice(index, 1);
    updateSelectedVersions();
}

function addVersionData() {
    let data = $("#componentTypeVersion").val();
    if (data.trim()) {
        addVersion(data);
        $(".hideForSelectVersion").show();
    } else {
        validateComponentVersion();
    }
    $("#componentTypeVersion").val("");
}

//List.cshtml
function handleKeyPress(event) {
    if (event.keyCode === 13 && event.target.id === 'componentTypeVersion') { // 13 is the key code for Enter
        event.preventDefault(); // Prevent the default Enter key behavior.
        document.getElementById("btnSaveProfile").click();
    }
}

function enableInfraComponent(infraid, valueid) {
    let infraChecked = document.getElementById(infraid);
    if (infraChecked.checked) {
        $(valueid).val(true);
    } else {
        $(valueid).val(false);
    }
}

function clearServerTypeErrorMessage() {
    const errorElements = ['#componentTypeError', '#componentNameError', '#componentTypeVersionError'];
    clearInputFields('createComponentForm', errorElements);
}

async function populateDatabaseModalFields(serverTypeData) {
    $('#textServerTypeId').val(serverTypeData?.id);
    const formTypeId = $("#selectComponentType :selected").attr("id");
    $("#formTypeId").val(formTypeId);
    $("#isDatabaseChecked").prop('checked', serverTypeData?.isDatabase);
    $("#isReplicationChecked").prop('checked', serverTypeData?.isReplication);
    $("#isClusterChecked").prop('checked', serverTypeData?.isCustom);
    $("#componentTypeName").val(serverTypeData?.properties?.name);
    $("#componentName").val(serverTypeData?.properties?.name);
    $("#selectedVersionDetails").empty();
    versions = [];
    const className = serverTypeData?.properties?.icon;
    let formTypeName = serverTypeData?.formTypeName;
    const isServerOrDatabase = formTypeName === "Server" || formTypeName === "Database" || formTypeName === "";

    if (isServerOrDatabase) {
        $(".hideForReplication").show();
        $(".hideForSelectVersion").show();
    } else {
        $(".hideForReplication").hide();
        $(".hideForSelectVersion").hide();
    }
    await formTypeNames(serverTypeData?.formTypeName)

    $("#componentTypelogo").attr("class", className);
    $('#componentTypeIcon').val(className);

    if (serverTypeData?.properties?.version) {
        addVersion(serverTypeData?.properties?.version?.replace(/[\[\]"\s]/g, ""));
    }
}
