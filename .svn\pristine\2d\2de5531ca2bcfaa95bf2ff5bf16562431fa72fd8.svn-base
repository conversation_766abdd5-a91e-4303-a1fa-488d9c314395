using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BulkImportOperationGroup.Events.Create;

public class BulkImportOperationGroupCreatedEventHandler : INotificationHandler<BulkImportOperationGroupCreatedEvent>
{
    private readonly ILogger<BulkImportOperationGroupCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BulkImportOperationGroupCreatedEventHandler(ILoggedInUserService userService,
        ILogger<BulkImportOperationGroupCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(BulkImportOperationGroupCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} BulkImportOperationGroup",
            Entity = "BulkImportOperationGroup",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"BulkImportOperationGroup '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"BulkImportOperationGroup '{createdEvent.Name}' created successfully.");
    }
}