﻿using ContinuityPatrol.Application.Features.OracleRACMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.OracleRACMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.OracleRACMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.OracleRACMonitorLogsModel;

namespace ContinuityPatrol.Application.Mappings;

public class OracleRacMonitorLogsProfile : Profile
{
    public OracleRacMonitorLogsProfile()
    {
        CreateMap<OracleRACMonitorLogs, CreateOracleRACMonitorLogCommand>().ReverseMap();
        CreateMap<OracleRACMonitorLogs, OracleRACMonitorLogsDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<OracleRACMonitorLogs, OracleRACMonitorLogsListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<OracleRACMonitorLogs, OracleRACMonitorLogsDetailByTypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}