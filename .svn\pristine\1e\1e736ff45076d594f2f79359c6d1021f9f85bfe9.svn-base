﻿namespace ContinuityPatrol.Application.Features.StateMonitorStatus.Commands.Update;

public class
    UpdateStateMonitorStatusCommandHandler : IRequestHandler<UpdateStateMonitorStatusCommand,
        UpdateStateMonitorStatusResponse>
{
    private readonly IMapper _mapper;
    private readonly IStateMonitorStatusRepository _stateMonitorStatusRepository;

    public UpdateStateMonitorStatusCommandHandler(IStateMonitorStatusRepository stateMonitorStatusRepository,
        IMapper mapper)
    {
        _stateMonitorStatusRepository = stateMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<UpdateStateMonitorStatusResponse> Handle(UpdateStateMonitorStatusCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _stateMonitorStatusRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(eventToUpdate, nameof(Domain.Entities.StateMonitorStatus),
            new NotFoundException(nameof(Domain.Entities.StateMonitorStatus), request.Id));

        _mapper.Map(request, eventToUpdate, typeof(UpdateStateMonitorStatusCommand),
            typeof(Domain.Entities.StateMonitorStatus));

        await _stateMonitorStatusRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateStateMonitorStatusResponse
        {
            Message = "State monitor Status Updated Successfully.",

            Id = eventToUpdate.ReferenceId
        };
        return response;
    }
}