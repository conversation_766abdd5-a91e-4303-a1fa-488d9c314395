﻿using ContinuityPatrol.Application.Features.AlertNotification.Commands.Create;
using ContinuityPatrol.Application.Features.AlertNotification.Commands.Delete;
using ContinuityPatrol.Application.Features.AlertNotification.Commands.Update;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetList;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertNotificationModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Alert;

public class AlertNotificationService : BaseService, IAlertNotificationService
{
    public AlertNotificationService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAlertNotification(
        CreateAlertNotificationCommand createAlertNotificationCommand)
    {
        Logger.LogDebug($"Create AlertNotification '{createAlertNotificationCommand.InfraObjectId}'");

        return await Mediator.Send(createAlertNotificationCommand);
    }

    public async Task<BaseResponse> DeleteAlertNotification(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AlertNotification Id");

        Logger.LogDebug($"Delete AlertNotification Details by Id '{id}'");

        return await Mediator.Send(new DeleteAlertNotificationCommand { Id = id });
    }

    public async Task<AlertNotificationDetailVm> GetAlertNotificationById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AlertNotification Id");

        Logger.LogDebug($"Get AlertNotification Detail by Id '{id}'");

        return await Mediator.Send(new GetAlertNotificationDetailQuery { Id = id });
    }

    public async Task<List<AlertNotificationDetailByInfraObjectIdVm>> GetAlertNotificationByInfraObjectIdAndAlertCode(
        string infraObjectId, string alertCode)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "AlertNotification Id");

        Logger.LogDebug($"Get AlertNotification Detail by Id '{infraObjectId}' and  AlertCode '{alertCode}.");

        return await Mediator.Send(new GetAlertNotificationDetailByInfraObjectIdQuery
            { InfraObjectId = infraObjectId, AlertCode = alertCode });
    }

    public async Task<List<AlertNotificationListVm>> GetAlertNotificationsList()
    {
        Logger.LogDebug("Get All AlertNotifications");

        return await Mediator.Send(new GetAlertNotificationListQuery());
    }

    public async Task<PaginatedResult<AlertNotificationListVm>> GetPaginatedAlertNotifications(
        GetAlertNotificationPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in AlertNotification Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<BaseResponse> UpdateAlertNotification(
        UpdateAlertNotificationCommand updateAlertNotificationCommand)
    {
        Logger.LogDebug($"Update AlertNotification '{updateAlertNotificationCommand.InfraObjectId}'");

        return await Mediator.Send(updateAlertNotificationCommand);
    }
}