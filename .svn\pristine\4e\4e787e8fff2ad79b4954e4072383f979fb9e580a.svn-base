using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Events.Update;

public class ApprovalMatrixApprovalUpdatedEventHandler : INotificationHandler<ApprovalMatrixApprovalUpdatedEvent>
{
    private readonly ILogger<ApprovalMatrixApprovalUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ApprovalMatrixApprovalUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<ApprovalMatrixApprovalUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(ApprovalMatrixApprovalUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} ApprovalMatrixApproval",
            Entity = "ApprovalMatrixApproval",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"ApprovalMatrixApproval '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"ApprovalMatrixApproval '{updatedEvent.Name}' updated successfully.");
    }
}