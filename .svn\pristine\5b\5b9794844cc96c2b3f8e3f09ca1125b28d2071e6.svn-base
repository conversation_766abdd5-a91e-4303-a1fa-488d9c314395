﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.InfraReplicationMapping.Event.PaginatedView;

public class InfraReplicationMappingPaginatedEventHandler : INotificationHandler<InfraReplicationMappingPaginatedEvent>
{
    private readonly ILogger<InfraReplicationMappingPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public InfraReplicationMappingPaginatedEventHandler(ILogger<InfraReplicationMappingPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(InfraReplicationMappingPaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.InfraReplicationMapping.ToString(),
            Action = $"{ActivityType.View} {Modules.InfraReplicationMapping}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Infra-Replication Mapping viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Infra-Replication Mapping viewed");
    }
}