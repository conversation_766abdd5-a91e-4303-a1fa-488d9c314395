using ContinuityPatrol.Application.Features.AdPasswordJob.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordJob.Events;

public class UpdateAdPasswordJobEventTests : IClassFixture<AdPasswordJobFixture>, IClassFixture<UserActivityFixture>
{
    private readonly AdPasswordJobFixture _adPasswordJobFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly AdPasswordJobUpdatedEventHandler _handler;

    public UpdateAdPasswordJobEventTests(AdPasswordJobFixture adPasswordJobFixture, UserActivityFixture userActivityFixture)
    {
        _adPasswordJobFixture = adPasswordJobFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");
        mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/adpasswordjob");
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        var mockAdPasswordJobEventLogger = new Mock<ILogger<AdPasswordJobUpdatedEventHandler>>();

        _mockUserActivityRepository = CompanyRepositoryMocks.CreateCompanyEventRepository(_userActivityFixture.UserActivities);

        _handler = new AdPasswordJobUpdatedEventHandler(
            mockLoggedInUserService.Object, 
            mockAdPasswordJobEventLogger.Object, 
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateAdPasswordJobEventCreated()
    {
        // Arrange
        _userActivityFixture.UserActivities[0].LoginName = "Tester";
        var adPasswordJobUpdatedEvent = new AdPasswordJobUpdatedEvent { Name = "TestDomainServer" };

        // Act
        var result = _handler.Handle(adPasswordJobUpdatedEvent, CancellationToken.None);

        // Assert
        Assert.True(result.IsCompleted);
        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var adPasswordJobUpdatedEvent = new AdPasswordJobUpdatedEvent { Name = "TestDomainServer" };

        // Act
        await _handler.Handle(adPasswordJobUpdatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_WithCorrectProperties()
    {
        // Arrange
        var adPasswordJobUpdatedEvent = new AdPasswordJobUpdatedEvent { Name = "TestDomainServer" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(adPasswordJobUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.LoginName.ShouldBe("Tester");
        capturedUserActivity.Action.ShouldContain("Update AdPasswordJob");
        capturedUserActivity.Entity.ShouldBe("AdPasswordJob");
        capturedUserActivity.ActivityType.ShouldBe("Update");
        capturedUserActivity.ActivityDetails.ShouldContain("TestDomainServer");
        capturedUserActivity.ActivityDetails.ShouldContain("updated successfully");
    }

    [Fact]
    public async Task Handle_LogCorrectInformation_When_AdPasswordJobUpdated()
    {
        // Arrange
        var adPasswordJobUpdatedEvent = new AdPasswordJobUpdatedEvent { Name = "TestDomainServer" };
        var mockLogger = new Mock<ILogger<AdPasswordJobUpdatedEventHandler>>();

        var handler = new AdPasswordJobUpdatedEventHandler(
            new Mock<ILoggedInUserService>().Object,
            mockLogger.Object,
            _mockUserActivityRepository.Object);

        // Act
        await handler.Handle(adPasswordJobUpdatedEvent, CancellationToken.None);

        // Assert
        //mockLogger.Verify(
        //    x => x.Log(
        //        LogLevel.Information,
        //        It.IsAny<EventId>(),
        //        It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("TestDomainServer") && v.ToString().Contains("updated successfully")),
        //        It.IsAny<Exception>(),
        //        It.IsAny<Func<It.IsAnyType, Exception, string>>()),
        //    Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectUserActivityProperties_When_UserServiceHasData()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var testUrl = "/api/test";
        var testIpAddress = "***********";
        var testLoginName = "TestUser";

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);
        mockLoggedInUserService.Setup(x => x.LoginName).Returns(testLoginName);
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testUrl);
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testIpAddress);

        var handler = new AdPasswordJobUpdatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<AdPasswordJobUpdatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var adPasswordJobUpdatedEvent = new AdPasswordJobUpdatedEvent { Name = "TestDomainServer" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(adPasswordJobUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.UserId.ShouldBe(testUserId);
        capturedUserActivity.LoginName.ShouldBe(testLoginName);
        capturedUserActivity.RequestUrl.ShouldBe(testUrl);
        capturedUserActivity.HostAddress.ShouldBe(testIpAddress);
    }
}
