using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Approval;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixApprovalModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage;

public class ApprovalMatrixApprovalService : BaseClient, IApprovalMatrixApprovalService
{
    public ApprovalMatrixApprovalService(IConfiguration config, IAppCache cache, ILogger<ApprovalMatrixApprovalService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<ApprovalMatrixApprovalListVm>> GetApprovalMatrixApprovalList()
    {
        var request = new RestRequest("api/v6/approvalmatrixapprovals");

        return await GetFromCache<List<ApprovalMatrixApprovalListVm>>(request, "GetApprovalMatrixApprovalList");
    }

    public async Task<BaseResponse> CreateAsync(CreateApprovalMatrixApprovalCommand createApprovalMatrixApprovalCommand)
    {
        var request = new RestRequest("api/v6/approvalmatrixapprovals", Method.Post);

        request.AddJsonBody(createApprovalMatrixApprovalCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateApprovalMatrixApprovalCommand updateApprovalMatrixApprovalCommand)
    {
        var request = new RestRequest("api/v6/approvalmatrixapprovals", Method.Put);

        request.AddJsonBody(updateApprovalMatrixApprovalCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/approvalmatrixapprovals/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<ApprovalMatrixApprovalDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/approvalmatrixapprovals/{id}");

        return await Get<ApprovalMatrixApprovalDetailVm>(request);
    }

    public async Task<ApprovalMatrixApprovalResponse> UpdateApprovalMatrixStatus(ApprovalMatrixApprovalCommand command)
    {
        var request = new RestRequest("api/v6/approvalmatrixapprovals/update-status", Method.Put);

        request.AddJsonBody(command);

        return await Put<ApprovalMatrixApprovalResponse>(request);
    }

    #region NameExist
    public async Task<bool> IsApprovalMatrixApprovalNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/approvalmatrixapprovals/name-exist?approvalmatrixapprovalName={name}&id={id}");

        return await Get<bool>(request);
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<ApprovalMatrixApprovalListVm>> GetPaginatedApprovalMatrixApprovals(GetApprovalMatrixApprovalPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/approvalmatrixapprovals/paginated-list");

        return await Get<PaginatedResult<ApprovalMatrixApprovalListVm>>(request);
    }


    #endregion
}
