using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Commands.Delete;
using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Queries.GetList;
using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceHealthLogModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class BusinessServiceHealthLogControllerTests : IClassFixture<BusinessServiceHealthLogFixture>
{
    private readonly BusinessServiceHealthLogFixture _businessServiceHealthLogFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly BusinessServiceHealthLogController _controller;

    public BusinessServiceHealthLogControllerTests(BusinessServiceHealthLogFixture businessServiceHealthLogFixture)
    {
        _businessServiceHealthLogFixture = businessServiceHealthLogFixture;

        var testBuilder = new ControllerTestBuilder<BusinessServiceHealthLogController>();
        _controller = testBuilder.CreateController(
            _ => new BusinessServiceHealthLogController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetBusinessServiceHealthLog_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessServiceHealthLogListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_businessServiceHealthLogFixture.BusinessServiceHealthLogListVm);

        // Act
        var result = await _controller.GetBusinessServiceHealthLog();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var healthLogs = Assert.IsAssignableFrom<List<BusinessServiceHealthLogListVm>>(okResult.Value);
        Assert.Equal(3, healthLogs.Count);
    }

    [Fact]
    public async Task GetDrReadyLogById_ReturnsExpectedDetail()
    {
        // Arrange
        var healthLogId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessServiceHealthLogDetailQuery>(q => q.Id == healthLogId), default))
            .ReturnsAsync(_businessServiceHealthLogFixture.BusinessServiceHealthLogDetailVm);

        // Act
        var result = await _controller.GetDrReadyLogById(healthLogId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var healthLog = Assert.IsType<BusinessServiceHealthLogDetailVm>(okResult.Value);
        Assert.NotNull(healthLog);
    }

    [Fact]
    public async Task CreateBusinessServiceHealthLog_Returns201Created()
    {
        // Arrange
        var command = _businessServiceHealthLogFixture.CreateBusinessServiceHealthLogCommand;
        var expectedMessage = "BusinessServiceHealthLog has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBusinessServiceHealthLogResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBusinessServiceHealthLog(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBusinessServiceHealthLogResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBusinessServiceHealthLog_ReturnsOk()
    {
        // Arrange
        var command = _businessServiceHealthLogFixture.UpdateBusinessServiceHealthLogCommand;
        var expectedMessage = "BusinessServiceHealthLog has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateBusinessServiceHealthLogResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateBusinessServiceHealthLog(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBusinessServiceHealthLogResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBusinessServiceHealthLog_ReturnsOk()
    {
        // Arrange
        var healthLogId = Guid.NewGuid().ToString();
        var expectedMessage = "BusinessServiceHealthLog has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBusinessServiceHealthLogCommand>(c => c.Id == healthLogId), default))
            .ReturnsAsync(new DeleteBusinessServiceHealthLogResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteBusinessServiceHealthLog(healthLogId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBusinessServiceHealthLogResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public async Task GetPaginatedBusinessServiceHealthLog_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetBusinessServiceHealthLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _businessServiceHealthLogFixture.BusinessServiceHealthLogListVm.Take(2).ToList();
        var expectedResults = PaginatedResult<BusinessServiceHealthLogListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessServiceHealthLogPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedBusinessServiceHealthLog(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<BusinessServiceHealthLogListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(2, paginatedResult.TotalCount);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task GetDrReadyLogById_HandlesInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetDrReadyLogById("invalid-guid"));
    }

    [Fact]
    public async Task CreateBusinessServiceHealthLog_ValidatesConfiguredCount()
    {
        // Arrange
        var command = new CreateBusinessServiceHealthLogCommand
        {
            ConfiguredCount = -1, // Negative count should cause validation error
            DRReadyCount = 10,
            DRNotReadyCount = 5,
            ProblemState = "Test State"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("ConfiguredCount must be non-negative"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBusinessServiceHealthLog(command));
    }

    [Fact]
    public async Task UpdateBusinessServiceHealthLog_ValidatesHealthLogExists()
    {
        // Arrange
        var command = new UpdateBusinessServiceHealthLogCommand
        {
            Id = Guid.NewGuid().ToString(),
            ConfiguredCount = 50,
            DRReadyCount = 45,
            DRNotReadyCount = 5,
            ProblemState = "Updated State"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("BusinessServiceHealthLog not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateBusinessServiceHealthLog(command));
    }

    [Fact]
    public async Task CreateBusinessServiceHealthLog_HandlesComplexHealthData()
    {
        // Arrange
        var command = new CreateBusinessServiceHealthLogCommand
        {
            ConfiguredCount = 250,
            DRReadyCount = 220,
            DRNotReadyCount = 30,
            ProblemState = "Enterprise Infrastructure - Multiple DR sites configured with automated failover capabilities. Minor issues detected in secondary site connectivity."
        };

        var expectedMessage = "BusinessServiceHealthLog has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBusinessServiceHealthLogResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBusinessServiceHealthLog(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBusinessServiceHealthLogResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBusinessServiceHealthLog_HandlesHealthStatusChange()
    {
        // Arrange
        var command = new UpdateBusinessServiceHealthLogCommand
        {
            Id = Guid.NewGuid().ToString(),
            ConfiguredCount = 250,
            DRReadyCount = 248,
            DRNotReadyCount = 2,
            ProblemState = "All systems operational - DR readiness at 99.2% with only minor maintenance items remaining"
        };

        var expectedMessage = "BusinessServiceHealthLog has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateBusinessServiceHealthLogResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateBusinessServiceHealthLog(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBusinessServiceHealthLogResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }
}
