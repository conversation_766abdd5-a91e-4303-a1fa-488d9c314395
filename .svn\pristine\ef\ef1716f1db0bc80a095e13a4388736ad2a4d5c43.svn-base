using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IApprovalMatrixUsersRepository : IRepository<ApprovalMatrixUsers>
{
    Task<bool> IsNameExist(string name, string id);
    Task<ApprovalMatrixUsers> GetByUserIdAsync(string userId);
    Task<List<ApprovalMatrixUsers>> GetListByApprovalIdsAsync(List<string> userIds);
}
