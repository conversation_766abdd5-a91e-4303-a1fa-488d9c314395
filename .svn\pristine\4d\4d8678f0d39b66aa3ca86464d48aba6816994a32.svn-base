﻿
const companyURL = {
    nameExistUrl : "Configuration/Company/IsCompanyNameExist",
    displayNameExistUrl : "Configuration/Company/IsCompanyDisplayNameExist",
    Pagination: "/Configuration/Company/GetPagination"
}

let createPermission = $("#ConfigurationCreate").data("create-permission").toLowerCase();
let deletePermission = $("#ConfigurationDelete").data("delete-permission").toLowerCase();
let errorElements = ['#clusterProfileName-error', '#clusterServerName-error', '#clusterName-error', '#clusterBinPath-error'];
let btanfalse = false;
$(function () {
 
    if (createPermission == 'false') {
        $("#Company-CreateButton").removeClass('#Company-CreateButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    let selectedValues = [];

    let dataTable = $('#tblCompany').DataTable(
        {

            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }
                , infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            Sortable: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": companyURL.Pagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "displayName" : sortIndex === 3 ? "webAddress" :
                    sortIndex === 4 ? "status" : "";
                    let orderValue = d.order[0]?.dir || 'asc';

                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {               
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json.data.length === 0) {
                        $(".pagination-column").addClass("disabled")

                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                        if ($('#currentUserRole').text().toLowerCase() === 'siteadmin') {
                            json.data = json.data.filter((d) => d.isParent)
                        }
                    }
                    return json?.data;
                },
            },
            "columnDefs": [
                {
                    "targets": [1,2],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false
                },
                {
                    data: "name",
                    name: 'Company',
                    autoWidth: true,
                    render: function (data, type, row) {
                        let companyName = row.name;
                        let companyLogo = row.companyLogo;

                        let nameSplit = companyName.split(/[ _]+/);
                        let initials = nameSplit.length > 1 ?
                            nameSplit[0].trim().substring(0, 1) + nameSplit[1].trim().substring(0, 1).toUpperCase() :
                            nameSplit[0].trim().substring(0, 1).toUpperCase();

                        if (type === 'display') {
                            let titleAttribute = `title="${companyName}"`;
                            if (companyLogo) {
                                return `
                                <div class="d-flex align-items-center">
                               <span> <img class="Avatar_Logo" ${titleAttribute} src="${companyLogo}" alt="${companyName}" /></span>
                               <span ${titleAttribute} class="text-truncate d-inline-block" style="max-width:150px">${companyName}</span></div>`;
                            } else {
                                return `
                                <span class="Avatar_Logo"> <span id="companyname" class="Icon" ${titleAttribute}>${initials}</span>
                                </span>
                               <span ${titleAttribute}>${companyName}</span>`;
                            }
                        } else {
                            return data;
                        }
                    }
                },

                {
                    "data": "displayName", "name": "Display Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "webAddress", "name": "Web Address", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (createPermission === 'true' && deletePermission === "true") {
                            const isParent = row.isParent;
                            return `<div class="d-flex align-items-center  gap-2">                                       
                                <span role="button" title="Edit"  class="edit-button" data-company='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>
                                ${isParent ? `
                                    <span title="Delete" style="cursor:not-allowed; opacity:0.50;" class="delete-button ">
                                        <i class="cp-Delete"></i>
                                    </span>` :
                                    `
                                    <span role="button" title="Delete" class="delete-button" data-company-id="${row.id}" data-company-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete"></i>
                                    </span>`
                                }
                            </div>
                     `;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                       <div class="d-flex align-items-center  gap-2">
                           <span role="button" title="Edit" class="edit-button" data-company='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>                                                                                       
                                            <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>                                  
                                            
                                </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="delete-button" data-company-id="${row.id}" data-company-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }
                        else {
                            return `
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }
                    },
                    "orderable": false
                }
            ],
            "columnDefs": [
                {
                    "targets": [1, 2, 3],
                    "className": "truncate"
                }
              
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },

            "drawCallback": function () {
                const randomColor = () => {
                    return "#" + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0').toUpperCase();
                }
                const namelist = document.querySelectorAll("#companyname");
                namelist.forEach((name) => {
                    name.style.backgroundColor = randomColor();
                });
            }
        });

    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const CompanyNameCheckbox = $("#CompanyName");
        const DisplayNameCheckbox = $("#DisplayName");
        const WebAddressCheckbox1 = $("#WebAddress");
        const inputValue = $('#search-inp').val();
        if (CompanyNameCheckbox.is(':checked')) {
            selectedValues.push(CompanyNameCheckbox.val() + inputValue);
        } 
        if (DisplayNameCheckbox.is(':checked')) {
            selectedValues.push(DisplayNameCheckbox.val() + inputValue);
        }
        if (WebAddressCheckbox1.is(':checked')) {
            selectedValues.push(WebAddressCheckbox1.val() + inputValue);
        }
        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    },500))
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });


    $('.form-select-sm').select2({
        minimumResultsForSearch: Infinity
    });
  
    //Update
    $('#tblCompany').on('click', '.edit-button', function () {   
        let companyData = $(this).data('company');
        populateModalFields(companyData);
        $('#SaveFunction').text('Update')
        $('#CreateModal').modal('show');
    });

    //delete
    $('#tblCompany').on('click', '.delete-button', function () {
        let companyId = $(this).data('company-id');
        let companyName = $(this).data('company-name');
        $("#deleteData").attr("title", companyName);
        $('#textDeleteId').val(companyId);
        $('#deleteData').text(companyName);
    });

 //  validation
    $('#textName').on('keyup', commonDebounce(async function () {
        let companyId = $('#textCompanyId').val();
        const value = $(this).val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await validateName(value, companyId, companyURL.nameExistUrl);
    },400));

    $('#textDisplayName').on('keyup', async function () {
        let companyId = $('#textCompanyId').val();
        const value = $(this).val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await validateDisplayName(value, companyId,companyURL.displayNameExistUrl);
    });

    $('#textWebAddress').on('keyup', async function () {
        const value = $(this).val();
        if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }
        await validateWebAddress(value);
    });

    let fileInput = document.getElementById('textCompanyLogo');
    let lastSelectedFile = null;

    $('#textCompanyLogo').on('click', function () {
        
        lastSelectedFile = fileInput.files.length ? fileInput.files[0] : null;
    });

    $('#textCompanyLogo').on('change', async function (event) {
        
        if (!event.target.files.length && lastSelectedFile) {
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(lastSelectedFile);
            fileInput.files = dataTransfer.files;         
            let create = "new";
            await FileValidation(fileInput, create);
        } else if (event.target.files.length) {          
            let create = "new";
            await FileValidation(fileInput, create);
         
            lastSelectedFile = fileInput.files[0];
        }
        if (fileInput.files.length !== 0) {
            $("#removeImgss").prop("disabled", false);
        } else {
            $("#removeImgss").prop("disabled", true);
        }
        
    });

    $("#SaveFunction").on('click', async function () { 
        
        let form = $("#CreateForm")
        let name = $("#textName").val();
        let displayName = $("#textDisplayName").val();
        let webAddress = $("#textWebAddress").val();
        let companyId = $('#textCompanyId').val();
        let fileInput = document.getElementById('textCompanyLogo');
        let isName = await validateName(name, companyId, companyURL.nameExistUrl);
        let isDisplayName = await validateDisplayName(displayName, companyId, companyURL.displayNameExistUrl);
        let isWebAddress =  await validateWebAddress(webAddress);
        let logoVal = $("#textCompanyLogo").val();
        let isFileName = await FileValidation(fileInput);
        
        let sanitizeArray = ['textName', 'textDisplayName', 'textWebAddress', 'textCompanyId', 'textParentComapnyName', 'textParentId', 'textLogoName', 'hiddedCompanyLogo']
        sanitizeContainer(sanitizeArray)
        setTimeout(() => {
            if (isName && isDisplayName && isWebAddress && (logoVal ? isFileName : true) && !btanfalse) {    
                btanfalse = true
                form.trigger('submit');
            }
        },200)       
    });

    async function validateName(value, id = null, url) {
        const errorElement = $('#Name-error');

        if (!value) {
            errorElement.text('Enter company name')
                .addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        var url1 = RootUrl + url;
        var data = {};
        data.companyName = value
        data.id = id;

        const validationResults = [
             SpecialCharValidate(value),
             ShouldNotBeginWithUnderScore(value),
             ShouldNotBeginWithSpace(value),
             OnlyNumericsValidate(value),
             SpaceWithUnderScore(value),
             ShouldNotEndWithUnderScore(value),
             ShouldNotEndWithSpace(value),
             MultiUnderScoreRegex(value),
             SpaceAndUnderScoreRegex(value),
             minMaxCompanylength(value),
             secondChar(value),
            await IsNameAlreadyExist(url1, data, ' Name already exists')
        ];

        return  CommonValidation(errorElement, validationResults);
    }
    async function validateDisplayName(value, id = null, url) {

        const errorElement = $('#DisplayName-error');

        if (!value) {
            errorElement.text('Enter display name')
                .addClass('field-validation-error')
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
       
        var url = RootUrl + url;
        var data = {};
        data.displayName = value.trim();
        data.id = id;

        const validationResults = [
             SpecialCharValidate(value),
             OnlyNumericsValidate(value),
             ShouldNotBeginWithUnderScore(value),
             ShouldNotBeginWithSpace(value),
             SpaceWithUnderScore(value),
             ShouldNotEndWithUnderScore(value),
             ShouldNotEndWithSpace(value),
             MultiUnderScoreRegex(value),
             SpaceAndUnderScoreRegex(value),
             DisplayLength(value),
             secondChar(value),
            await IsNameAlreadyExist(url, data, "Display name already exists")
        ];

        return CommonValidation(errorElement, validationResults);
    }

    async function validateWebAddress(value) {

        const errorElement = $('#WebAddress-error');
        let format = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
        if (!value) {
            errorElement.text('Enter company web address')
                .addClass('field-validation-error');
            return false;
        }
        if (format.test(value.charAt(0))) {
            errorElement.text('Enter valid company web addresss')
                .addClass('field-validation-error');
            return false;
        } 
        const validationResults = [
             await WebAddressValidate(value),
            //await webAddValidation(value)
        ];

        return  CommonValidation(errorElement, validationResults);
    }


    async function FileValidation(fileInput, create = null) {
        
        errorElement = $('#companyLog-error');
        if (fileInput.files.length === 0 || fileInput.files.length == null || fileInput.files.length == undefined || fileInput?.files[0]?.name === "No file chosen") {
            return true;
        }

        let fileSize = fileInput.files[0].size;
        let fileName = fileInput.files[0].name;
        
        if (!FileFormatValidate(fileName)) {
            errorElement.text('Only PNG images are allowed')
                .addClass('field-validation-error');
           
            return false;
        }


        if (create === "new") {
            let base64 = await convertToBase64(fileInput);
            $('#hiddedCompanyLogo').val(base64);
        }

        let existingBase64 = $('#hiddedCompanyLogo').val();
        if (!existingBase64) {
            let base64 = await convertToBase64(fileInput);
            $('#hiddedCompanyLogo').val(base64);
        }

        $('#textLogoName').val(fileName)

        const validationResults = [
           //  FileFormatValidate(fileName),
             FileSizeValidate(fileSize)
        ];

        return CommonValidation(errorElement, validationResults);
    }

    function FileFormatValidate(fileName) {
        const validExtension = 'png'; 
        const fileExtension = fileName.split('.').pop().toLowerCase();
        return fileExtension === validExtension;
    }


    function populateModalFields(companyData) {
      
        $('#textName').val(companyData.name);
        $('#textDisplayName').val(companyData.displayName); 
        $('#textWebAddress').val(companyData.webAddress);
        $('#textParentId').val(companyData.parentId);
        $('#textIsParent').val(companyData.isParent);
        $('#textCompanyId').val(companyData.id);
        $('#hiddedCompanyLogo').val(companyData.companyLogo);
        $('#textParentComapnyName').val(companyData.parentName);

        let name = "";
        const fileInput = document.querySelector('input[type="file"]');
        
        if (companyData.logoName === null || companyData.logoName === '') {
            name = "No file chosen";
            $("#removeImgss").prop("disabled", true);
        } else {
            name = companyData.logoName;
            $("#removeImgss").prop("disabled", false);
        }
        const myFile = new File([companyData.companyLogo], name, {
            type: 'text/plain',
        });

        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(myFile);
        fileInput.files = dataTransfer.files;

        let errorElement = ['#Name-error', '#DisplayName-error', '#WebAddress-error', '#companyLog-error']
        errorElement.forEach(element => {
            $(element).text('').removeClass('field-validation-error')
        });
    }
       
    $("#removeImgss").on("click", function (e) {
        e.stopPropagation();
        $("#hiddedCompanyLogo").val('');
        $("#textCompanyLogo").val('')
        $('#textLogoName').val('').trigger("change");
        let file = $('#textCompanyLogo').val() 
        
        if (file.length==0) {
            $('#companyLog-error').text('').removeClass('field-validation-error');
            $("#removeImgss").prop("disabled", true);
        }                 
    });
 
    //Clear data
    $('#Company-CreateButton').on('click', async function () {
        $("#removeImgss").prop("disabled", true);
        const errorElements = ['#Name-error', '#DisplayName-error', '#WebAddress-error', '#companyLog-error'];
        clearInputFields('CreateForm', errorElements);
        let parentCompanyId = document.getElementById('textParentComapnyName').value;
         
        await $.ajax({
            url: RootUrl + "Configuration/Company/GetCompanyById",
            method: 'GET',
            dataType: 'json',
            data: {
                id: parentCompanyId
            },
            success: function (data) {

                $('#textParentComapnyName').val(data.displayName);
                $('#SaveFunction').text('Save');
            },
            error: function (error) {
                console.error('Error:', error);
            }
        });
    });
});


function dataURLtoFile(base64, filename) {
    let arr = base64.split(","),
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);

    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }

    let blob = new Blob([u8arr], { type: "image/png" });

    return new File([blob], filename);
}
function convertToBase64(fileInput) {

    return new Promise((resolve, reject) => {
        const selectedFile = fileInput.files[0];
        if (selectedFile) {
            const reader = new FileReader();
            reader.onload = function (event) {
                const base64String = event.target.result;
                resolve(base64String);
            };
            reader.readAsDataURL(selectedFile);
        } else {
            reject('No file selected.');
        }
    });
}
