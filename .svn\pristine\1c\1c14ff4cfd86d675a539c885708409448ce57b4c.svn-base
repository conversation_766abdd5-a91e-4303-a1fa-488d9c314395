﻿using ContinuityPatrol.Application.Features.TeamResource.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.TeamResourceModel;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamResource.Queries;

public class GetTeamResourceListQueryHandlerTests : IClassFixture<TeamResourceFixture>
{
    private readonly TeamResourceFixture _teamResourceFixture;

    private Mock<ITeamResourceRepository> _mockTeamResourceRepository;

    private readonly GetTeamResourceListQueryHandler _handler;

    public GetTeamResourceListQueryHandlerTests(TeamResourceFixture teamResourceFixture)
    {
        _teamResourceFixture = teamResourceFixture;

        _mockTeamResourceRepository = TeamResourceRepositoryMocks.GetTeamResourceRepository(_teamResourceFixture.TeamResources);

        _handler = new GetTeamResourceListQueryHandler(_teamResourceFixture.Mapper, _mockTeamResourceRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_TeamResourcesCount()
    {
        var result = await _handler.Handle(new GetTeamResourceListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<TeamResourceListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_Valid_TeamResourcesList()
    {
        var result = await _handler.Handle(new GetTeamResourceListQuery(), CancellationToken.None);
        result.ShouldBeOfType<List<TeamResourceListVm>>();
        result[0].Id.ShouldBe(_teamResourceFixture.TeamResources[0].ReferenceId);
        result[0].TeamMasterId.ShouldBe(_teamResourceFixture.TeamResources[0].TeamMasterId);
        result[0].TeamMasterName.ShouldBe(_teamResourceFixture.TeamResources[0].TeamMasterName);
        result[0].ResourceId.ShouldBe(_teamResourceFixture.TeamResources[0].ResourceId);
        result[0].ResourceName.ShouldBe(_teamResourceFixture.TeamResources[0].ResourceName);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockTeamResourceRepository = TeamResourceRepositoryMocks.GetTeamResourceEmptyRepository();

        var handler = new GetTeamResourceListQueryHandler(_teamResourceFixture.Mapper, _mockTeamResourceRepository.Object);

        var result = await handler.Handle(new GetTeamResourceListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetTeamResourceListQuery(), CancellationToken.None);

        _mockTeamResourceRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}
