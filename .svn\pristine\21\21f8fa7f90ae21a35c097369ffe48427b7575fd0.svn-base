﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;

public class LicenseManagerDetailViewVm
{
    public string Id { get; set; }
    public string PoNumber { get; set; }
    public string CompanyId { get; set; }
    public string CompanyName { get; set; }
    public string Properties { get; set; }
    public string Validity { get; set; }
    public string ParentPoNumber { get; set; }
    public string LicenseKey { get; set; }
    public string ExpiryDate { get; set; }
    public string CreatedDate { get; set; }
    public string RenewalDate { get; set; }
    public bool IsLicenseExpiry { get; set; }
    public int SiteCount { get; set; }
    public bool IsState { get; set; }
    public bool IsAmc { get; set; }
    public bool IsWarranty { get; set; }
    public string WarrantyPlan { get; set; }
    public string WarrantyEndDate { get; set; }
    public string AmcPlan { get; set; }
    public string AmcStartDate { get; set; }
    public string AmcEndDate { get; set; }
    [JsonIgnore] public string LastModifiedDate { get; set; }

    public BaseLicenseCountVm BaseLicenseCountVm { get; set; } = new();
}
public class GroupedLicenseInfo
{
    public string LicenseId { get; set; }
    public string PoNumber { get; set; }
    public string Entity { get; set; }
    public string EntityType { get; set; }
    public int Count { get; set; }
}

public class LicensePropertiesCount
{
    public bool IsDatabase { get; set; }
    public int DatabaseCount { get; set; }
    public int ReplicationCount { get; set; }
    public int StorageCount { get; set; }
    public int VirtualizationCount { get; set; }
    public int ApplicationCount { get; set; }
    public int DnsCount { get; set; }
    public int NetworkCount { get; set; }
    public int ThirdPartyCount { get; set; }
    public DatabaseTypeCountDto DatabaseDto { get; set; }
}

public class DatabaseTypeCountDto
{
    public Dictionary<string, List<DatabaseItem>> Databases { get; set; }
}

public class DatabaseItem
{
    public string Id { get; set; }
    public string Type { get; set; }
    public int Count { get; set; }
    public string Logo { get; set; }
}

public class BaseLicenseCountVm
{
    public int TotalCount { get; set; }
    public int DerivedTotalCount { get; set; }
    public int TotalUsedCount { get; set; }
    public int TotalNotUsedCount { get; set; }
    public int DatabaseUsedCount { get; set; }
    public int ReplicationUsedCount { get; set; }
    public int StorageUsedCount { get; set; }
    public int ApplicationUsedCount { get; set; }
    public int DnsUsedCount { get; set; }
    public int NetworkUsedCount { get; set; }
    public int ThirdPartyUsedCount { get; set; }
    public int VirtualizationUsedCount { get; set; }

    public int DatabaseAvailableCount { get; set; }
    public int ReplicationAvailableCount { get; set; }
    public int VirtualizationAvailableCount { get; set; }
    public int ApplicationAvailableCount { get; set; }
    public int StorageAvailableCount { get; set; }
    public int DnsAvailableCount { get; set; }
    public int NetworkAvailableCount { get; set; }
    public int ThirdPartyAvailableCount { get; set; }


    public int DerivedDatabaseUsedCount { get; set; }
    public int DerivedReplicationUsedCount { get; set; }
    public int DerivedStorageUsedCount { get; set; }
    public int DerivedApplicationUsedCount { get; set; }
    public int DerivedDnsUsedCount { get; set; }
    public int DerivedNetworkUsedCount { get; set; }
    public int DerivedThirdPartyUsedCount { get; set; }
    public int DerivedVirtualizationUsedCount { get; set; }


    public int DerivedDatabaseAvailableCount { get; set; }
    public int DerivedReplicationAvailableCount { get; set; }
    public int DerivedVirtualizationAvailableCount { get; set; }
    public int DerivedApplicationAvailableCount { get; set; }
    public int DerivedStorageAvailableCount { get; set; }
    public int DerivedDnsAvailableCount { get; set; }
    public int DerivedNetworkAvailableCount { get; set; }
    public int DerivedThirdPartyAvailableCount { get; set; }

    public DatabaseTypeCountDto DatabaseDto { get; set; }
}