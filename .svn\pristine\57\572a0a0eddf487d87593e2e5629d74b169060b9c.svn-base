﻿//using ContinuityPatrol.Application.Features.Database.Commands.Delete;
//using ContinuityPatrol.Shared.Core.Exceptions;

//namespace ContinuityPatrol.Core.UnitTests.Domains.Database.Commands;

//public class DeleteDatabaseTests : IClassFixture<DatabaseFixture>, IClassFixture<InfraObjectFixture>
//{
//    private readonly DatabaseFixture _databaseFixture;

//    private readonly Mock<IDatabaseRepository> _mockDatabaseRepository;

//    private readonly DeleteDatabaseCommandHandler _handler;

//    public DeleteDatabaseTests(DatabaseFixture databaseFixture, InfraObjectFixture infraObjectFixture, WorkflowFixture workflowFixture)
//    {
//        _databaseFixture = databaseFixture;

//        var mockPublisher = new Mock<IPublisher>();

//        _mockDatabaseRepository = DatabaseRepositoryMocks.DeleteDatabaseRepository(_databaseFixture.Databases);

//        var mockInfraObjectRepository = InfraObjectRepositoryMocks.DeleteInfraObjectRepository(infraObjectFixture.InfraObjects);

//        var mockWorkflowRepository = WorkflowRepositoryMocks.DeleteWorkflowRepository(workflowFixture.Workflows);

//        _handler = new DeleteDatabaseCommandHandler(_mockDatabaseRepository.Object, mockPublisher.Object, mockInfraObjectRepository.Object, mockWorkflowRepository.Object);
//    }

//    [Fact]
//    public async Task Handle_UpdateIsActiveFalse_When_DatabaseDeleted()
//    {
//        var result = await _handler.Handle(new DeleteDatabaseCommand { Id = _databaseFixture.Databases[0].ReferenceId }, CancellationToken.None);

//        result.IsActive.ShouldBeFalse();
//    }

//    [Fact]
//    public async Task Handle_Return_DeleteDatabaseResponse_When_DatabaseDeleted()
//    {
//        var result = await _handler.Handle(new DeleteDatabaseCommand() { Id = _databaseFixture.Databases[0].ReferenceId }, CancellationToken.None);

//        result.ShouldBeOfType(typeof(DeleteDatabaseResponse));

//        result.IsActive.ShouldBeFalse();

//        result.Success.ShouldBeTrue();

//        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
//    }

//    [Fact]
//    public async Task Handle_Update_IsActiveFalse_When_DatabaseDeleted()
//    {
//        await _handler.Handle(new DeleteDatabaseCommand { Id = _databaseFixture.Databases[0].ReferenceId }, CancellationToken.None);

//        var database = await _mockDatabaseRepository.Object.GetByReferenceIdAsync(_databaseFixture.Databases[0].ReferenceId);

//        database.IsActive.ShouldBeFalse();
//    }

//    [Fact]
//    public async Task Handle_ThrowNotFoundException_When_InvalidDatabaseId()
//    {
//        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteDatabaseCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
//    }

//    [Fact]
//    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
//    {
//        await _handler.Handle(new DeleteDatabaseCommand { Id = _databaseFixture.Databases[0].ReferenceId }, CancellationToken.None);

//        _mockDatabaseRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

//        _mockDatabaseRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.Database>()), Times.Once);
//    }
//}