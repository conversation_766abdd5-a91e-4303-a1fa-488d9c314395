﻿using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MongoDbMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage;

public class MongoDbMonitorStatusService : BaseClient, IMongoDbMonitorStatusService
{
    public MongoDbMonitorStatusService(IConfiguration config, IAppCache cache, ILogger<MongoDbMonitorStatusService> logger) 
        : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateMongoDbMonitorStatusCommand createMongoDbMonitorStatusCommand)
    {
        var request = new RestRequest("api/v6/mongodbmonitorstatus", Method.Post);

        request.AddJsonBody(createMongoDbMonitorStatusCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateMongoDbMonitorStatusCommand updateMongoDbMonitorStatusCommand)
    {
        var request = new RestRequest("api/v6/mongodbmonitorstatus", Method.Put);

        request.AddJsonBody(updateMongoDbMonitorStatusCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<List<MongoDbMonitorStatusListVm>> GetAllMongoDbMonitorStatus()
    {
        var request = new RestRequest("api/v6/mongodbmonitorstatus");

        return await GetFromCache<List<MongoDbMonitorStatusListVm>>(request, "GetAllMongoDbMonitorStatus");
    }

    public async Task<MongoDbMonitorStatusDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/mongodbmonitorstatus/{id}");

        return await Get<MongoDbMonitorStatusDetailVm>(request);
    }

    public async Task<List<MongoDbMonitorStatusDetailByTypeVm>> GetMongoDbMonitorStatusByType(string type)
    {
        var request = new RestRequest($"api/v6/mongodbmonitorstatus/type?type={type}");

        return await Get<List<MongoDbMonitorStatusDetailByTypeVm>>(request);
    }

    public async Task<PaginatedResult<MongoDbMonitorStatusListVm>> GetPaginatedMongoDbMonitorStatus(GetMongoDbMonitorStatusPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/mongodbmonitorstatus/paginated-list?SearchString=");

        return await Get<PaginatedResult<MongoDbMonitorStatusListVm>>(request);
    }

    //public async Task<string> GetMongoDbMonitorStatusByInfraObjectId(string infraObjectId)
    //{
    //    var request = new RestRequest($"api/v6/mongodbmonitorstatus/by/{infraObjectId}", Method.Get);

    //    return await Get<string>(request);
    //}
}