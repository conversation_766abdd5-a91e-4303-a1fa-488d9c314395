﻿using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetMSSQLMonitorStatusByInfraObjectId;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;


[ApiVersion("6")]
public class MssqlMonitorStatusController : CommonBaseController
{
    [HttpPost]
    [Authorize(Policy = Permissions.Manage.Create)]
    public async Task<ActionResult<CreateMSSQLMonitorStatusResponse>> CreateMssqlMonitorStatus([FromBody] CreateMSSQLMonitorStatusCommand createMssqlMonitorStatusCommand)
    {
        Logger.LogDebug($"Create MSSQL Monitor Status '{createMssqlMonitorStatusCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateMssqlMonitorStatus), await Mediator.Send(createMssqlMonitorStatusCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Manage.Edit)]
    public async Task<ActionResult<UpdateMSSQLMonitorStatusResponse>> UpdateMssqlMonitorStatus([FromBody] UpdateMSSQLMonitorStatusCommand updateMssqlMonitorStatusCommand)
    {
        Logger.LogDebug($"Update MSSQL Monitor Status '{updateMssqlMonitorStatusCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateMssqlMonitorStatusCommand));
    }

    [HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<MSSQLMonitorStatusListVm>>> GetAllMssqlMonitorStatus()
    {
        Logger.LogDebug("Get All  MSSQL Monitor Status");

        return Ok(await Mediator.Send(new GetMSSQLMonitorStatusListQuery()));
    }
    [HttpGet("{id}")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<MSSQLMonitorStatusDetailVm>> GetMssqlMonitorStatusById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "MSSQL MonitorStatus Detail By Id");

        Logger.LogDebug($"Get  MSSQL Monitor Status Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetMSSQLMonitorStatusDetailQuery { Id = id }));
    }
    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<MSSQLMonitorStatusDetailVm>>> GetPaginatedMssqlMonitorStatus([FromQuery] GetMSSQLMonitorStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in MSSQL MonitorStatus Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet("type")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<MSSQLMonitorStatusDetailByTypeVm>> GetMssqlMonitorStatusByType(string type)
    {
        Guard.Against.NullOrEmpty(type, "MSSQL Monitor Status Detail By Type");

        Logger.LogDebug($"Get  MSSQL Monitor Status Detail by Id '{type}'");

        return Ok(await Mediator.Send(new GetMSSQLMonitorStatusDetailByTypeQuery { Type = type }));
    }

    [HttpGet("by/{infraObjectId}")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<string>> GetMssqlMonitorStatusByInfraObjectId(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "MSSQL MonitorStatus InfraObjectId");

        Logger.LogDebug($"Get MSSQL MonitorStatus Detail by InfraObjectId '{infraObjectId}'");

        return Ok(await Mediator.Send(new GetMSSQLMonitorStatusByInfraObjectIdQuery{ InfraObjectId = infraObjectId }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllMSSQLMonitorStatusCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllMSSQLMonitorStatusNameCacheKey };

        ClearCache(cacheKeys);
    }
}
