﻿using ContinuityPatrol.Application.Features.Workflow.Commands.Create;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Infrastructure.Hubs;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Workflow.Commands.CreateBotWorkFlow;

public class CreateBotWorkflowCommandHandler : IRequestHandler<CreateBotWorkflowCommand, CreateBotWorkflowResponse>
{
    private readonly IHubContext<NotificationHub> _hubContext;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ILogger<CreateWorkflowCommand> _logger;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly ITemplateRepository _templateRepository;
    private readonly IVersionManager _versionManager;
    private readonly IWorkflowHistoryRepository _workflowHistoryRepository;
    private readonly IWorkflowRepository _workflowRepository;

    public CreateBotWorkflowCommandHandler(IMapper mapper, IWorkflowRepository workflowRepository,
        IWorkflowHistoryRepository workflowHistoryRepository, ILoggedInUserService loggedInUserService,
        IPublisher publisher, IVersionManager versionManager, ITemplateRepository templateRepository,
        ILogger<CreateWorkflowCommand> logger, IHubContext<NotificationHub> hubContext)
    {
        _mapper = mapper;
        _workflowRepository = workflowRepository;
        _workflowHistoryRepository = workflowHistoryRepository;
        _loggedInUserService = loggedInUserService;
        _templateRepository = templateRepository;
        _publisher = publisher;
        _versionManager = versionManager;
        _logger = logger;
        _hubContext = hubContext;
    }

    public async Task<CreateBotWorkflowResponse> Handle(CreateBotWorkflowCommand request,
        CancellationToken cancellationToken)
    {
        var templateDetail = await _templateRepository.GetByReferenceIdAsync(request.TemplateId);

        var templateProperty = JsonConvert.DeserializeObject<dynamic>(templateDetail.Properties);

        for (var i = 0; i < templateProperty.SelectToken("nodes").Count; i++)
        {
            var node = templateProperty.SelectToken("nodes[" + i + "].actionInfo.properties");

            node["@@PRServerName"] = request.PRServerName;
            node["@@PRDBName"] = request.PRDBName;
            node["@@DRServerName"] = request.DRServerName;
            node["@@DRDBName"] = request.DRDBName;
            ;
            templateProperty["nodes[" + i + "].actionInfo.properties"] = node;

            templateDetail.Properties = JsonConvert.SerializeObject(templateProperty);
        }

        var createWorkflowCommandHandler = new CreateWorkflowCommandHandler(_mapper, _workflowRepository,
            _workflowHistoryRepository, _loggedInUserService, _publisher, _versionManager, _hubContext);

        var newWorkflowRequest = new CreateWorkflowCommand
        {
            Name = request.WorkflowName,
            CompanyId = string.Empty,
            Properties = templateDetail.Properties,
            Version = string.Empty,
            Comments = string.Empty
        };
        var createWorkflow = await createWorkflowCommandHandler.Handle(newWorkflowRequest, cancellationToken);

        var response = new CreateBotWorkflowResponse
        {
            Message = Message.Create(nameof(Domain.Entities.Workflow), newWorkflowRequest.Name),
            WorkflowId = createWorkflow.WorkflowId
        };
        return response;
    }
}