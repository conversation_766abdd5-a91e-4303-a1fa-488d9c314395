﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.LicenseManager.Events.BaseLicenseEvent.Delete;

public class BaseLicenseDeletedEventHandler : INotificationHandler<BaseLicenseDeletedEvent>
{
    private readonly ILogger<BaseLicenseDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BaseLicenseDeletedEventHandler(ILoggedInUserService userService,
        ILogger<BaseLicenseDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(BaseLicenseDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.BaseLicense}",
            Entity = Modules.BaseLicense.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Base License '{deletedEvent.PONumber}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Base License '{deletedEvent.PONumber}' deleted successfully.");
    }
}