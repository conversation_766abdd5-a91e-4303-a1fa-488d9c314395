﻿using ContinuityPatrol.Application.Features.ImpactAvailability.Commands.Create;
using ContinuityPatrol.Application.Features.ImpactAvailability.Commands.Update;
using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetImpactAvailabilityList;
using ContinuityPatrol.Domain.ViewModels.ImpactAvailabilityModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class ImpactAvailabilityProfile : Profile
{
    public ImpactAvailabilityProfile()
    {
        CreateMap<ImpactAvailability, CreateImpactAvailabilityCommand>().ReverseMap();
        CreateMap<UpdateImpactAvailabilityCommand, ImpactAvailability>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<ImpactAvailability, ImpactAvailabilityDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<ImpactAvailability, ImpactAvailabilityViewModel>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<ImpactAvailability, ImpactAvailabilityDetailListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        // ImpactView
        CreateMap<List<ImpactAvailability>, ImpactAvailabilityListVm>()
            .ForMember(dest => dest.TotalServiceCount, opt => opt.MapFrom(src => src.Count))
            .ForMember(dest => dest.ServiceUp, opt => opt.MapFrom(src => src.Sum(x => Convert.ToInt32(x.ServiceUp))))
            .ForMember(dest => dest.ServiceDown,
                opt => opt.MapFrom(src => src.Sum(x => Convert.ToInt32(x.ServiceDown))))
            .ForMember(dest => dest.TotalBusinessFunctionCount,
                opt => opt.MapFrom(src => src.Sum(x => Convert.ToInt32(x.TotalBusinessFunctionCount))))
            .ForMember(dest => dest.BusinessFunctionUp,
                opt => opt.MapFrom(src => src.Sum(x => Convert.ToInt32(x.BusinessFunctionUp))))
            .ForMember(dest => dest.BusinessFunctionDown,
                opt => opt.MapFrom(src => src.Sum(x => Convert.ToInt32(x.BusinessFunctionDown))))
            .ForMember(dest => dest.TotalInfraObjectCount,
                opt => opt.MapFrom(src => src.Sum(x => Convert.ToInt32(x.TotalInfraObjectCount))))
            .ForMember(dest => dest.InfraObjectUp,
                opt => opt.MapFrom(src => src.Sum(x => Convert.ToInt32(x.InfraObjectUp))))
            .ForMember(dest => dest.InfraObjectDown,
                opt => opt.MapFrom(src => src.Sum(x => Convert.ToInt32(x.InfraObjectDown))));

        CreateMap<ImpactAvailability, ImpactListVm>().ReverseMap();

        CreateMap<PaginatedResult<ImpactAvailability>, PaginatedResult<ImpactAvailabilityViewModel>>()
             .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}