﻿using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using System.Net;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.IdentityModel.Tokens;


namespace ContinuityPatrol.Web.Middlewares;

public class ErrorHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ErrorHandlingMiddleware> _logger;
    public ErrorHandlingMiddleware(RequestDelegate next, ILogger<ErrorHandlingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
           // _logger.LogInformation($"{context.Request.Method} : {context.Request.Path}");

            await _next(context);
        }
        catch (ValidationException ex)
        {
            foreach (var error in ex.ValidationErrors)
            {
                _logger.LogError($"Validation Error: {error}");
            }
        }
        catch (Exception ex)
        {
            context.Features.Set<IExceptionHandlerPathFeature>(new ExceptionHandlerFeature
            {
                Error = ex,
                Path = context.Request.Path.Value!
            });

            _logger.LogError(GetErrorMessage(ex));

            throw;
        }
    }
    private string GetErrorMessage(Exception exception)
    {
        var message = "Exception Message : " + exception.Message + ".";

        if (exception.InnerException != null)
        {
            message = " Inner Exception : " + exception.InnerException.Message;
        }

        return $"ErrorHandlingMiddleware : ************ {message} ************";
    }

    private Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var httpStatusCode = HttpStatusCode.InternalServerError;

        context.Response.ContentType = "application/json";

        BaseResponse result = null;

        switch (exception)
        {
            case ValidationException validationException:
                httpStatusCode = HttpStatusCode.BadRequest;
                result = new ValidationResponse { Success = false, Message = "Invalid Parameter(s).", ValidationErrors = validationException.ValidationErrors };
                break;
            case BadRequestException badRequestException:
                httpStatusCode = HttpStatusCode.BadRequest;
                result = new BaseResponse { Success = false, Message = badRequestException.Message };
                break;
            case NotFoundException:
                httpStatusCode = HttpStatusCode.NotFound;
                break;
            case AuthenticationException authenticationException:
                httpStatusCode = HttpStatusCode.Unauthorized;
                result = new BaseResponse { Success = false, Message = authenticationException.Message, ErrorCode = authenticationException.ErrorCode };

                break;
            case InvalidArgumentException:
                httpStatusCode = HttpStatusCode.BadRequest;
                break;

            case SecurityTokenExpiredException:
                httpStatusCode = HttpStatusCode.Unauthorized;
                result = new BaseResponse { Success = false, Message = "Token_Expired", ErrorCode = 4005 };
                break;
            case TokenExpiredException tokenExpiredException:
                httpStatusCode = HttpStatusCode.Unauthorized;
                result = new BaseResponse { Success = false, Message = tokenExpiredException.Message, ErrorCode = 4005 };

                break;
            case not null:
                httpStatusCode = HttpStatusCode.BadRequest;
                break;
        }

        context.Response.StatusCode = (int)httpStatusCode;

        if (result == null)
        {
            if (exception != null)
            {
                result = new BaseResponse { Success = false, Message = exception.GetMessage() };
            }
        }

        WebHelper.Current.Session.Set<BaseResponse>("Exception", result);
        return Task.CompletedTask;
    }
}
