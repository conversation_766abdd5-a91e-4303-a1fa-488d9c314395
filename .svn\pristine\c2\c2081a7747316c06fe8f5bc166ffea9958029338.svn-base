<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>QUnit Tests for groupPolicy</title>
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.19.1.css">
    <!-- Include all required dependencies -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/qunit/qunit-2.19.1.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sinon.js/14.0.0/sinon.js"></script>

    <script src="/js/Common/common.js"></script>
    <script src="/js/Admin/GroupNodePolicy/groupPolicy.js"></script>
    <script src="/js/Admin/GroupNodePolicy/groupPolicyTest.js"></script>
</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture"></div>
</body>
</html>