﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class ApprovalMatrixFilterSpecification : Specification<ApprovalMatrix>
{
    public ApprovalMatrixFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                    {
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "", StringComparison.OrdinalIgnoreCase)));
                    }
                    else if (stringItem.Contains("username=", StringComparison.OrdinalIgnoreCase))
                    {
                    }
                    else if (stringItem.Contains("description=", StringComparison.OrdinalIgnoreCase))
                    {
                        Or(p => p.Description.Contains(stringItem.Replace("description=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    }
                    else if (stringItem.Contains("notificationtype=", StringComparison.OrdinalIgnoreCase))
                    {
                    }
                    else if (stringItem.Contains("properties=", StringComparison.OrdinalIgnoreCase))
                    {
                    }
            }
        }
        else
        {
            Criteria = p => p.Name != null;
        }
    }
}