using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Delete;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetList;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class AdPasswordJobsControllerTests : IClassFixture<AdPasswordJobFixture>
{
    private readonly AdPasswordJobFixture _adPasswordJobFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly AdPasswordJobsController _controller;

    public AdPasswordJobsControllerTests(AdPasswordJobFixture adPasswordJobFixture)
    {
        _adPasswordJobFixture = adPasswordJobFixture;

        var testBuilder = new ControllerTestBuilder<AdPasswordJobsController>();
        _controller = testBuilder.CreateController(
            _ => new AdPasswordJobsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAdPasswordJobs_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAdPasswordJobListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_adPasswordJobFixture.AdPasswordJobListVm);

        // Act
        var result = await _controller.GetAdPasswordJobs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var adPasswordJobs = Assert.IsAssignableFrom<List<AdPasswordJobListVm>>(okResult.Value);
        Assert.Equal(3, adPasswordJobs.Count);
    }

    [Fact]
    public async Task GetAdPasswordJobs_ReturnsEmptyList_WhenNoAdPasswordJobsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAdPasswordJobListQuery>(), default))
            .ReturnsAsync(new List<AdPasswordJobListVm>());

        // Act
        var result = await _controller.GetAdPasswordJobs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var adPasswordJobs = Assert.IsAssignableFrom<List<AdPasswordJobListVm>>(okResult.Value);
        Assert.Empty(adPasswordJobs);
    }

    [Fact]
    public async Task GetAdPasswordJobById_ReturnsAdPasswordJob_WhenIdIsValid()
    {
        // Arrange
        var adPasswordJobId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAdPasswordJobDetailQuery>(q => q.Id == adPasswordJobId), default))
            .ReturnsAsync(_adPasswordJobFixture.AdPasswordJobDetailVm);

        // Act
        var result = await _controller.GetAdPasswordJobById(adPasswordJobId);

        // Assert
        Assert.IsType<OkObjectResult>(result.Result);
    }

    [Fact]
    public async Task GetAdPasswordJobById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetAdPasswordJobById("invalid-guid"));
    }

    [Fact]
    public async Task CreateAdPasswordJob_Returns201Created()
    {
        // Arrange
        var command = _adPasswordJobFixture.CreateAdPasswordJobCommand;
        var expectedMessage = $"AdPasswordJob '{command.DomainServer}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAdPasswordJobResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateAdPasswordJob(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateAdPasswordJobResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task CreateAdPasswordJob_Throws_WhenJobExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateAdPasswordJobCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("Job exists"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.CreateAdPasswordJob(_adPasswordJobFixture.CreateAdPasswordJobCommand));
    }

    [Fact]
    public async Task UpdateAdPasswordJob_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"AdPasswordJob '{_adPasswordJobFixture.UpdateAdPasswordJobCommand.DomainServer}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateAdPasswordJobCommand>(), default))
            .ReturnsAsync(new UpdateAdPasswordJobResponse
            {
                Message = expectedMessage,
                Id = _adPasswordJobFixture.UpdateAdPasswordJobCommand.Id
            });

        // Act
        var result = await _controller.UpdateAdPasswordJob(_adPasswordJobFixture.UpdateAdPasswordJobCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateAdPasswordJobResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAdPasswordJob_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "AdPasswordJob 'DC01.contoso.com' has been deleted successfully!.";
        var adPasswordJobId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAdPasswordJobCommand>(c => c.Id == adPasswordJobId), default))
            .ReturnsAsync(new DeleteAdPasswordJobResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAdPasswordJob(adPasswordJobId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAdPasswordJobResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAdPasswordJob_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteAdPasswordJob("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedAdPasswordJobs_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetAdPasswordJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _adPasswordJobFixture.AdPasswordJobListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAdPasswordJobPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<AdPasswordJobListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        var result = await _controller.GetPaginatedAdPasswordJobs(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<AdPasswordJobListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<AdPasswordJobListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(3, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task IsAdPasswordJobNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAdPasswordJobNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsAdPasswordJobNameExist("ExistingJob", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsAdPasswordJobNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetAdPasswordJobNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsAdPasswordJobNameExist("NewJob", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsAdPasswordJobNameExist_ThrowsInvalidArgumentException_WhenNameIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsAdPasswordJobNameExist("", null));
    }

    [Fact]
    public async Task GetAdPasswordJobs_CallsCorrectQuery()
    {
        // Arrange
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAdPasswordJobListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<AdPasswordJobListVm>());

        // Act
        await _controller.GetAdPasswordJobs();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public async Task CreateAdPasswordJob_ClearsCacheAfterCreation()
    {
        // Arrange
        var command = _adPasswordJobFixture.CreateAdPasswordJobCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAdPasswordJobResponse
            {
                Message = "Created successfully",
                Id = Guid.NewGuid().ToString()
            });

        // Act
        await _controller.CreateAdPasswordJob(command);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public async Task UpdateAdPasswordJob_ClearsCacheAfterUpdate()
    {
        // Arrange
        var command = _adPasswordJobFixture.UpdateAdPasswordJobCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateAdPasswordJobResponse
            {
                Message = "Updated successfully",
                Id = command.Id
            });

        // Act
        await _controller.UpdateAdPasswordJob(command);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public async Task DeleteAdPasswordJob_ClearsCacheAfterDeletion()
    {
        // Arrange
        var adPasswordJobId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAdPasswordJobCommand>(c => c.Id == adPasswordJobId), default))
            .ReturnsAsync(new DeleteAdPasswordJobResponse
            {
                IsActive = false,
                Message = "Deleted successfully"
            });

        // Act
        await _controller.DeleteAdPasswordJob(adPasswordJobId);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

   
    [Fact]
    public async Task IsAdPasswordJobNameExist_HandlesComplexJobNames()
    {
        // Arrange
        var complexJobName = "Daily Password Check - Domain Controllers (Primary & Backup)";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAdPasswordJobNameUniqueQuery>(q => q.Name == complexJobName), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsAdPasswordJobNameExist(complexJobName, null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

  

   
    [Fact]
    public async Task DeleteAdPasswordJob_VerifiesJobIsDeactivated()
    {
        // Arrange
        var adPasswordJobId = Guid.NewGuid().ToString();
        var expectedMessage = "AdPasswordJob 'Test Job' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAdPasswordJobCommand>(c => c.Id == adPasswordJobId), default))
            .ReturnsAsync(new DeleteAdPasswordJobResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAdPasswordJob(adPasswordJobId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAdPasswordJobResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }


}
