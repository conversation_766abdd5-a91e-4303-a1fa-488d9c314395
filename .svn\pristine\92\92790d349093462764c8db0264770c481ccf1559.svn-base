﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CompanyRepository : BaseRepository<Company>, ICompanyRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public CompanyRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<Company>> ListAllAsync()
    {
        return IsParent
            ? await base.ListAllAsync()
            : await FindByFilterAsync(company => company.ReferenceId.Equals(_loggedInUserService.CompanyId));
    }

    public override async Task<Company> GetByReferenceIdAsync(string id)
    {
        if (IsParent)
            return await base.GetByReferenceIdAsync(id);

        return await Entities
            .AsNoTracking()
            .FirstOrDefaultAsync(company =>
                company.ReferenceId.Equals(id) &&
                company.ReferenceId.Equals(_loggedInUserService.CompanyId));

    }
    public override async Task<PaginatedResult<Company>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<Company> specification, string sortColumn, string sortOrder)
    {
        var query = IsParent
            ? Entities.AsNoTracking().Specify(specification)
            : Entities.AsNoTracking().Where(x => x.ReferenceId.Equals(_loggedInUserService.CompanyId)).Specify(specification);

        return await SelectCompany(query.DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public override IQueryable<Company> GetPaginatedQuery()
    { 
        return IsParent
            ? base.GetPaginatedQuery()
            : Entities.AsNoTracking().Where(x => x.IsActive && x.ReferenceId.Equals(_loggedInUserService.CompanyId)).DescOrderById();
    }

    public async Task<List<Company>> GetAllCompanyNames()
    {
        return IsParent || !_loggedInUserService.UserId.IsValidGuid()
            ? await GetAllNames()
            : await GetChildCompanyNames();
    }

    public async Task<Company> GetCompanyByLoginCompanyId(string id)
    {
        return await base.GetByReferenceIdAsync(id);
    }

    public async Task<Company> GetParentCompanyByLoginCompanyId(string id)
    {
        return await Entities.AsNoTracking()
            .Where(x => x.ReferenceId.Equals(id))
            .Select(x => new Company
            {
                Id = x.Id,
                ReferenceId = x.ReferenceId,
                DisplayName = x.DisplayName,
                IsParent = x.IsParent
            })
            .FirstOrDefaultAsync();
    }

    public async Task<bool> IsCompanyAndDisplayNameUnique(string name, string displayName)
    {
        if (string.IsNullOrEmpty(name) && string.IsNullOrEmpty(displayName))
            return false;

        return await _dbContext.Companies.AnyAsync(e =>
                (name != null && e.Name.Equals(name)) ||
                (displayName != null && e.DisplayName.Equals(displayName)));
    }

    public async Task<bool> IsDisplayNameExist(string displayName, string id)
    {
        if (string.IsNullOrEmpty(displayName))
            return false;

        if (!id.IsValidGuid())
            return await Entities.AnyAsync(e => e.DisplayName.Equals(displayName));

        return await Entities.AnyAsync(e => e.DisplayName.Equals(displayName) && !e.ReferenceId.Equals(id));
    }

    public async Task<bool> IsNameExist(string name, string id)
    {
        if (string.IsNullOrEmpty(name))
            return false;

        if (!id.IsValidGuid())
            return await Entities.AnyAsync(e => e.Name.Equals(name));

        return await Entities.AnyAsync(e => e.Name.Equals(name) && !e.ReferenceId.Equals(id));
    }

    public override async Task<Company> GetByIdAsync(int id)
    {
        if (id <= 0)
            return null;

        if (IsParent)
        {
            return await base.GetByIdAsync(id); }

        return await Entities.AsNoTracking()
            .FirstOrDefaultAsync(company => company.Id == id && company.ReferenceId.Equals(_loggedInUserService.CompanyId));
    }

    private async Task<List<Company>> GetAllNames()
    {
        return await Entities
            .Where(x => x.IsActive)
            .Select(x => new Company
            {
                ReferenceId = x.ReferenceId,
                DisplayName = x.DisplayName,
                IsParent = x.IsParent
            })
            .OrderBy(x => x.DisplayName)
            .ToListAsync();
    }

    private async Task<List<Company>> GetChildCompanyNames()
    {
        return await Entities
            .Where(x => x.IsActive && x.ReferenceId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new Company { ReferenceId = x.ReferenceId, DisplayName = x.DisplayName, IsParent = x.IsParent })
            .OrderBy(x => x.DisplayName)
            .ToListAsync();
    }
    private static IQueryable<Company> SelectCompany(IQueryable<Company> query)
    {
        return query.Select(x => new Company
        {
            Id = x.Id,
            CompanyLogo = x.CompanyLogo,
            DisplayName = x.DisplayName,
            IsParent = x.IsParent,
            LogoName = x.LogoName,
            Name = x.Name,
            ParentId = x.ParentId,
            ReferenceId = x.ReferenceId,
            WebAddress = x.WebAddress
        });
    }
}