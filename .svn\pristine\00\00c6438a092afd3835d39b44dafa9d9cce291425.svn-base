using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetPaginatedList;

public class GetDynamicDashboardPaginatedListQueryHandler : IRequestHandler<GetDynamicDashboardPaginatedListQuery,
    PaginatedResult<DynamicDashboardListVm>>
{
    private readonly IDynamicDashboardRepository _dynamicDashboardRepository;
    private readonly IMapper _mapper;

    public GetDynamicDashboardPaginatedListQueryHandler(IMapper mapper,
        IDynamicDashboardRepository dynamicDashboardRepository)
    {
        _mapper = mapper;
        _dynamicDashboardRepository = dynamicDashboardRepository;
    }

    public async Task<PaginatedResult<DynamicDashboardListVm>> Handle(GetDynamicDashboardPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DynamicDashboardFilterSpecification(request.SearchString);

        var queryable = await _dynamicDashboardRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var dynamicDashboardList = _mapper.Map<PaginatedResult<DynamicDashboardListVm>>(queryable);

        return dynamicDashboardList;
        //var queryable = _dynamicDashboardRepository.PaginatedListAllAsync();

        //var productFilterSpec = new DynamicDashboardFilterSpecification(request.SearchString);

        //var dynamicDashboardList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<DynamicDashboardListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return dynamicDashboardList;
    }
}