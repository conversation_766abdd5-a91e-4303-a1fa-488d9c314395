﻿using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IMssqlAlwaysOnMonitorStatusService
{
    Task<BaseResponse> CreateAsync(CreateMSSQLAlwaysOnMonitorStatusCommand createMssqlAlwaysOnMonitorStatusCommand);
    Task<List<MSSQLAlwaysOnMonitorStatusListVm>> GetAllMSSQLAlwaysOnMonitorStatus();
    Task<MSSQLAlwaysOnMonitorStatusDetailVm> GetByReferenceId(string id);
    Task<List<MSSQLAlwaysOnMonitorStatusDetailByTypeVm>> GetMSSQLAlwaysOnMonitorStatusByType(string type);
    Task<PaginatedResult<MSSQLAlwaysOnMonitorStatusListVm>> GetPaginatedMSSQLAlwaysOnMonitorStatus(GetMSSQLAlwaysOnMonitorStatusPaginatedListQuery query);
}