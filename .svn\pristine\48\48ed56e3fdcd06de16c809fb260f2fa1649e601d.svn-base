﻿using ContinuityPatrol.Application.Features.UserLogin.Events.Login;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.UserLogin.Events;

public class UserLoginEventTests : IClassFixture<UserLoginFixture>
{
    private readonly UserLoginFixture _userLoginFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly UserLoginEventHandler _handler;

    public UserLoginEventTests(UserLoginFixture userLoginFixture)
    {
        _userLoginFixture = userLoginFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockUserLoginEventLogger = new Mock<ILogger<UserLoginEventHandler>>();

        _mockUserActivityRepository = UserLoginRepositoryMocks.CreateUserLoginEventRepository(_userLoginFixture.UserActivities);

        _handler = new UserLoginEventHandler(mockLoggedInUserService.Object, mockUserLoginEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UserLoginEvent()
    {
        _userLoginFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_userLoginFixture.UserLoginEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_userLoginFixture.UserLoginEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_UserLoginEvent()
    {
        _userLoginFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_userLoginFixture.UserLoginEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }
}