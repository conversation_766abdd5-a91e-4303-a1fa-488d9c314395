﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;

public class UpdateBaseLicenseCommand : IRequest<UpdateBaseLicenseResponse>
{
    public string Id { get; set; }
    [JsonIgnore] public string PoNumber { get; set; }
    [JsonIgnore] public string CompanyId { get; set; }
    [JsonIgnore] public string CompanyName { get; set; }
    [JsonIgnore] public string HostName { get; set; }
    [JsonIgnore] public string IpAddress { get; set; }
    [JsonIgnore] public string MacAddress { get; set; }
    [JsonIgnore] public string Properties { get; set; }
    [JsonIgnore] public string Validity { get; set; }
    [JsonIgnore] public string ExpiryDate { get; set; }
    [JsonIgnore] public bool IsParent { get; set; }
    [JsonIgnore] public string ParentId { get; set; }
    [JsonIgnore] public string ParentPoNumber { get; set; }
    [JsonIgnore] public bool IsPrimary { get; set; }
    [JsonIgnore] public bool IsAmc { get; set; }
    [JsonIgnore] public string AmcPlan { get; set; }
    [JsonIgnore] public string AmcStartDate { get; set; }
    [JsonIgnore] public string AmcEndDate { get; set; }
    public string LicenseKey { get; set; }
    public bool IsState { get; set; }
}