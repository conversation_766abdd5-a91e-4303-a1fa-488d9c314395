﻿function JsonTreeView(s, id, funcId, infraId) {
    const isMatch = (target, matchId) => target?.id === matchId;

    const updateBusinessFunctionSelection = (bf, selected) => {
        bf.isAll = selected;
        bf.isPartial = false;
        bf.assignedInfraObjects?.forEach(infra => {
            if (infra?.id != null) {
                infra.isSelected = selected;
                if (selected) $('#treeList-error').text('').removeClass('field-validation-error');
            }
        });
    };

    const updateInfraSelection = (infraList, infraId, selected) => {
        for (const infra of infraList) {
            if (infra?.id === infraId) {
                infra.isSelected = selected;
                if (selected) $('#treeList-error').text('').removeClass('field-validation-error');
                return;
            }
        }
    };

    const updateCountsAndFlags = (d) => {
        const selectedFCount = d.assignedBusinessFunctions.reduce((count, f) => count + (f.isAll ? 1 : 0), 0);
        const partialFCount = d.assignedBusinessFunctions.reduce((count, f) => count + (f.isPartial ? 1 : 0), 0);
        businessFunctionCondition(selectedFCount, partialFCount, d);
    };

    jsonData.assignedBusinessServices.forEach(d => {
        if (isMatch(d, id)) {
            const bfs = d.assignedBusinessFunctions;

            // Case: BusinessFunction level toggle
            if (funcId && !infraId) {
                bfs.forEach(f => {
                    if (isMatch(f, funcId)) {
                        updateBusinessFunctionSelection(f, s);
                        updateCountsAndFlags(d);
                    }
                });

                // Case: BusinessService level toggle
            } else if (!funcId && !infraId) {
                d.isAll = s;
                d.isPartial = s;

                bfs.forEach(f => {
                    if (f) {
                        f.isAll = s;
                        f.isPartial = s;
                        f.assignedInfraObjects?.forEach(infra => {
                            if (infra?.id != null) {
                                infra.isSelected = s;
                                if (s) $('#treeList-error').text('').removeClass('field-validation-error');
                            }
                        });
                    }
                });

                // Case: InfraObject level toggle
            } else if (funcId && infraId != null) {
                bfs.forEach(f => {
                    if (f) {
                        updateInfraSelection(f.assignedInfraObjects, infraId, s);

                        const selectedInfraCount = f.assignedInfraObjects.reduce((count, infra) => count + (infra.isSelected ? 1 : 0), 0);
                        infraObjectData(selectedInfraCount, f);
                    }
                });

                updateCountsAndFlags(d);
            }
        }
    });

    // Global flag for "select all"
    const allSelected = jsonData.assignedBusinessServices.every(service => service.isAll === true);
    $("#txtinfraObjectAllFlag").val(String(allSelected));
    $("#userWorkflowAll").prop("checked", allSelected);
    jsonData.isAll = allSelected;

    // Re-render tree and update hidden field
    $("#treeview").empty();
    $('#userProperties').val(JSON.stringify(jsonData));
    createTreeView($("#treeview"), jsonData, id);

    const validateData = JSON.stringify(jsonData);
    filterJSONData(validateData);
    areAllValuesFalse(validateData);
}

async function treeListView() {
    const $treeview = $("#treeview").empty();
    const $companySelect = $('#userCompanyName').children(":selected");
    const companyId = $companySelect.attr('id');
    const isParentCompany = $companySelect.data('isparent')?.toString().toLowerCase() === 'true';
    const roleName = $("#userRoleName :selected").text();

    const data = !isParentCompany ? { companyId } : {};

    try {
        await $.ajax({
            url: RootUrl + userManageURL.infraObjectList,
            type: "GET",
            dataType: "json",
            data,
            success: function (result) {
                if (result) {
                    jsonData = result;
                    JSONDataForClickEditButton = result;
                    populateTreeView(jsonData);
                    $('#selectAllOpen details').removeAttr('open');
                } else {
                    errorNotification(result);
                }
            }
        });
    } catch (error) {
        errorNotification(error);
    }

    if (!isCreate) {
        updateTreeView(userData);
    }

    switch (roleName) {
        case "SuperAdmin":
            SelectAllTreeView(true);
            $(".selecttree, #userWorkflowAll").prop("disabled", true);
            $("#txtinfraObjectAllFlag").val("true");
            break;
        case "Administrator":
            $(".selecttree, #userWorkflowAll").prop("disabled", false);
            $("#txtinfraObjectAllFlag").val("false");
            break;
        default:
            filterJSONData(jsonData);
            $(".selecttree, #userWorkflowAll").prop("disabled", false);
            $("#txtinfraObjectAllFlag").val("false");
            break;
    }
}
function getFromFields() {
    return {
        type: $("#userLoginType").val(),
        type_error: $('#LoginType-error'),
        loginId: $("#textLoginId").val(),
        loginName: $("#textLoginName").val().toLowerCase(),
        loginPassword: $("#encriptedPassword").val(),
        confirmPassword: $("#userConfirmPassword").val(),
        companyName: $("#userCompanyName").val(),
        roleName: $("#userRoleName :selected").text(),
        userName: $("#textUserName").val(),
        email: $("#textEmail").val(),
        isGroup: $("#ADGroup").prop('checked'),
        isMobileChk: $("#chk-LDAP").prop('checked'),
        mobilePre: $("#userCountryCode").val(),
        mobileNo: $("#userMobileNum").val(),
        sessionTimeout: $("#usersSessionTimeout").val(),
        errorElementCompany: $('#Company-error'),
        errorElementRole: $('#Role-error'),
        properties: $('#userProperties').val(),
        adDomain: $('#userDomain').val(),
        infraAllFlag: $('#txtinfraObjectAllFlag').val(),
        btnSave: $('#SaveFunction').text()
    }

}
function handleMobileConcat({ isMobileChk, mobilePre, mobileNo }) {
    const full = mobilePre && mobileNo ? `${mobilePre}-${mobileNo}` : '';
    $('#comMobile').val(isMobileChk ? full : '');
}

async function runPrimaryValidations(fields) {
    const isName = await validateLoginName(fields.loginName, fields.loginId, userManageURL.nameExist);
    let isloginPassword = false;
    let isconfirmPassword = false;
    let isADUserName = false;
    if (fields.type === 'AD') {
        isloginPassword = true;
        isconfirmPassword = true;
        const adUser = fields.isGroup ? $('#userDomainGroup').val() : $('#userDomainUser').val();
        const targetError = fields.isGroup ? $('#AdGroup-error') : $('#AdUser-error');
        isADUserName = await validateDropDown(adUser, fields.isGroup ? 'Select group' : 'Select user', targetError);
    } else {
        isloginPassword = await validateLoginPassword(fields.loginPassword, $('#LoginPassword-error'));
        isconfirmPassword = await validateConfirmPassword(fields.confirmPassword, $('#ConfirmPassword-error'));
    }
    const iscompanyName = await validateDropDown(fields.companyName, 'Select company name', $('#Company-error'));
    const isroleName = $("#userRoleName").prop('disabled') ? true : await validateDropDown(fields.roleName, 'Select role', $('#Role-error'));
    const isTypename = await validateDropDown(fields.type, 'Select authentication type', $('#LoginType-error'));
    const isADDomainName = (fields.type?.toLowerCase() === 'ad')
        ? await validateDropDown(fields.adDomain, 'Select domain', $('#AdDomain-error'))
        : false;

    const isuserName = "";
    const isemail = "";
    const isMobilePre = "";
    const isMobile = "";
    const isDashboard = "";
    const isTimeout = "";

    return { isName, iscompanyName, isroleName, isTypename, isADUserName, isADDomainName, isloginPassword, isconfirmPassword, isuserName, isemail, isMobilePre, isMobile, isDashboard, isTimeout };
}
function shouldValidateLogin() {
    return $('#textLoginName').is(':visible') || $('#ADGroupName').is(':visible');
}

async function encryptPasswordsIfNeeded({ loginName, loginPassword, confirmPassword }) {
    if (loginPassword?.length < 60) {
        const encrypted = await EncryptPassword(loginName + loginPassword);
        $("#userPassword").val(encrypted);
    }
    if (confirmPassword?.length < 60) {
        const encrypted = await EncryptPassword(loginName + confirmPassword);
        $("#userConfirmPassword").val(encrypted);
    }
}

async function handleUserInfoValidation(fields, v) {

    if (fields.btnSave == 'Save') {
        if (v.isName && v.iscompanyName && v.isroleName && v.isTypename &&
            v.isconfirmPassword && v.isloginPassword && (fields.type === 'AD' ? (v.isADDomainName && v.isADUserName) : true)) {
            let decryptedPwd = "";
            if (fields.confirmPassword) {
                decryptedPwd = await DecryptPassword(fields.confirmPassword);
            }
            let decryptedLogin = await DecryptPassword(fields.loginPassword);

            if (decryptedPwd.decrypt === decryptedLogin.decrypt) {
                await encryptPasswordsIfNeeded(fields);
                treeListView();
                form.steps('next');
            } else {
                await validateConfirmPassword(decryptedPwd.decrypt);
            }
        }
        else if (v.isName && v.iscompanyName && v.isroleName && (fields.type == 'AD' ? (v.isADDomainName && v.isADUserName) : true) && fields.type === 'AD') {
            treeListView();
            form.steps('next');
        }
    }
    else {
        if (v.isName && v.iscompanyName && v.isroleName) {
            if (fields.loginPassword?.length < 60) {
                let loginPass = await EncryptPassword(fields.loginName + fields.loginPassword);
                $("#userPassword").val(loginPass)
            }
            if (fields.confirmPassword?.length < 60) {
                let confirmPass = await EncryptPassword(fields.loginName + fields.confirmPassword);
                $("#userConfirmPassword").val(confirmPass)
            }
            treeListView();
            form.steps('next');
        }
    }
    return false;

}

async function handleSecondaryValidation(fields, v, isStep) {
    v.isuserName = await validateUserName(fields.userName);
    v.isemail = await validateEmail(fields.email);
    const isMobileChk = $("#chk-LDAP").prop('checked');
    const isDashboardChk = $("#dashboardMode").prop('checked');

    v.isMobilePre = isMobileChk ? await validateMobilePre(fields.mobilePre) : true;
    v.isMobile = isMobileChk ? await validateMobile(fields.mobileNo) : true;
    v.isDashboard = isDashboardChk ? await validateDropDown($('#dashboardList').val(), 'Select dashboard', $('#dashboard-error')) : true;
    v.isTimeout = await validateSessionTimeout(fields.sessionTimeout);

    $("#isPreferedChk").val($('#chk-workflowtype').prop('checked'));

    if (isStep && $('#textEmail').is(':visible')) {
        if ($('#chk-LDAP').prop('checked')) {
            if (v.isuserName && v.isemail && b.isMobilePre && v.isMobile && v.isDashboard && v.isTimeout) {
                form.steps('next');
            }
        } else {
            if (v.isuserName && v.isemail && v.isDashboard && v.isTimeout) {
                form.steps('next');
            }
        }
    } else {
        form.steps('next');
    }

}

async function processInfraTree(fields) {

    const role = userRoleValue.toLowerCase();
    if (role !== "siteadmin") {
        isTreeList = fields.properties && await areAllValuesFalse(fields.properties);
        let parsed = JSON.parse(fields.properties);
        if (parsed?.isAll) $("#txtinfraObjectAllFlag").val("true");
    }

    if (role === "siteadmin") {
        $("#userProperties").val('{"isAll":true}');
        $("#txtinfraObjectAllFlag").val("true");
        isTreeList = true;
    }
}
function enableFields() {
    $('#textLoginName,#userCompanyName').prop('disabled', false);
    if (userData?.roleName && userData?.roleName?.toLowerCase() !== "siteadmin") $('#userRoleName').prop('disabled', false);
}
function buildInfraObject(jsonData) {
    let businessServiceObject = {
        isAll: false,
        assignedBusinessServices: []
    };

    jsonData?.assignedBusinessServices?.forEach(bservice => {
        if (bservice.isAll) {
            businessServiceObject.assignedBusinessServices.push(bservice);
        } else {
            let businessFunctionArray = [];

            bservice?.assignedBusinessFunctions?.forEach(bfunction => {
                if (bfunction.isAll) {
                    businessFunctionArray.push(bfunction);
                } else {
                    let infraObjectArray = bfunction?.assignedInfraObjects?.filter(infra => infra.isSelected) || [];

                    if (infraObjectArray.length > 0) {
                        let clonedFunction = { ...bfunction, assignedInfraObjects: infraObjectArray };
                        businessFunctionArray.push(clonedFunction);
                    }
                }
            });

            if (businessFunctionArray.length > 0) {
                let clonedService = { ...bservice, assignedBusinessFunctions: businessFunctionArray };
                businessServiceObject.assignedBusinessServices.push(clonedService);
            }
        }
    });

    if (businessServiceObject.assignedBusinessServices.length === 0) {
        businessServiceObject.isAll = true;
        $("#txtinfraObjectAllFlag").val("true");
    }
    return businessServiceObject;
}
function buildCreateUserPayload() {
    return {
        Id: $("#textLoginId").val(),
        LoginType: $("#loginType").val(),
        CompanyId: $("#userCompanyId").val(),
        CompanyName: $("#userCompanyName").val(),
        LoginName: $("#textLoginName").val(),
        LoginPassword: $("#userPassword").val(),
        ConfirmPassword: $("#userConfirmPassword").val(),
        EncryptPassword: $("#encriptedPassword").val(),
        Role: $("#userRoleId").val(),
        RoleName: $("#userRoleName :selected").text(),
        IsLock: $("#loginType").val(),
        IsGroup: $("#ADGroup").prop("checked"),
        IsReset: "",
        InfraObjectAllFlag: $("#txtinfraObjectAllFlag").val(),
        SessionTimeout: $("#usersSessionTimeout").val(),
        IsVerify: "",
        TwoFactorAuthentication: "",
        Url: $("#dashboardList").val(),
        IsDefaultDashboard: $("#dashboardModeValue").val(),

        UserInfoCommand: {
            UserName: $("#textUserName").val(),
            Email: $("#textEmail").val(),
            AlertMode: $("#chk-AD").val() ? $("#chk-AD").val() : $("#chk-LDAP").val(),
            Mobile: $("#comMobile").val(),
            IsPreferredMode: $("#chk-workflowtype").is(":checked")
        },

        UserInfraObjectCommand: {
            Properties: $("#userProperties").val(),
            UserId: $("#textUserId").val()
        }
    };
}

async function createOrUpdateUser(createUser) {
    let response = await $.ajax({
        type: "POST",
        url: RootUrl + userManageURL.createOrUpdate,
        dataType: "json",
        headers: {
            'RequestVerificationToken': await gettoken()
        },
        data: createUser,
    });

    if (response.success) {
        $("#userCreateModal").modal('hide');
        notificationAlert("success", response.message.message);
        dataTable.ajax.reload();
        setTimeout(() => {
            if (editedLoginID.trim() === loggedInUserId && userRoleValue !== $("#ddlRoleName :selected").text()) {
                notificationAlert("warning", "Sesison invalid");
                setTimeout(() => {
                    window.location.assign(RootUrl + 'Account/Logout');
                }, 2500)
            }
        }, 2500);
    } else {
        $("#userCreateModal").modal('hide');
        errorNotification(response);
        dataTable.ajax.reload();
    }
}

async function finalCreateUserProcess(fields, v) {
    const roleText = $("#userRoleName :selected").text()?.toLowerCase();
    let isTreeList = await areAllValuesFalse($('#userProperties').val());

    if (!jsonData.isAll) {
        const updatedProps = buildInfraObject(jsonData);
        $('#userProperties').val(JSON.stringify(updatedProps));
    }


    setTimeout(async () => {
        if (!isTreeList && roleText !== 'superadmin') {
            btnCrudEnable('SaveFunction');
            $('#treeList-error').text('Select at least one infraobject').addClass('field-validation-error');
            $(".content").scrollTop($(".content")[0].scrollHeight);
        } else {
            $('#treeList-error').text('').removeClass('field-validation-error');
            btnCrudDiasable('SaveFunction');



            let createUser = buildCreateUserPayload();
            const isLDAP = $('#chk-LDAP').prop('checked');

            const isAD = fields.type.toLowerCase() === 'ad';
            const isLoginPwdValid = isAD || v.isloginPassword;
            const commonValidations = v.isName && v.isloginPassword && v.iscompanyName && v.isroleName && v.isemail && v.isTimeout && isTreeList;
            const nameValid = v.isName || v.isADgroupName;

            if (fields.loginId) {
                const baseValid = v.isName && v.isuserName && v.iscompanyName && v.isroleName && v.isemail && v.isTimeout && isTreeList;
                const allValid = isLDAP ? baseValid && v.ismobile : baseValid;
                if (allValid) {
                    createOrUpdateUser(createUser);
                }
            } else {
                const allValid = isLDAP ? nameValid && commonValidations && validation.ismobile : nameValid && commonValidations;
                if (allValid) {
                    createOrUpdateUser(createUser);
                }
            }
        }
    }, 300);
}

async function ValidateAll(value) {

    $('#userProperties').val(JSON.stringify(jsonData));
    let fields = getFromFields();
    handleMobileConcat(fields);
    let flag = true;
    let flagTree = true;
    const validations = await runPrimaryValidations(fields);

    if (value && (shouldValidateLogin())) {
        isNextStep = await handleUserInfoValidation(fields, validations);
        if (isNextStep == false) {
            flag = false;
        }
    }
    if (flag) {
        isNextStep = await handleSecondaryValidation(fields, validations, value,);
    }
    if (flagTree) {
        await processInfraTree(fields);
    }
    enableFields();
    if (!value) {
        await finalCreateUserProcess(fields, validations);
    }
    return;
}

async function populateModalFields(userData) {
    const {
        loginType, companyName, id, loginName, companyId, role, userName,
        email, roleName, isDefaultDashboard, url, mobile, isPreferredMode,
        sessionTimeout, properties
    } = userData || {};

    // Hide AD and mobile input sections initially
    $('#ad_hide, #mobileInputContainer').hide();

    // Set core fields
    $('#userLoginType, #loginType').val(loginType);
    $('#userCompanyName').val(companyName).trigger('change');
    $('#textLoginId, #textUserId').val(id);
    $('#textLoginName').val(loginName);
    $('#userCompanyId').val(companyId);
    $('#userRoleId').val(role);
    $('#textUserName').val(userName);
    $('#textEmail').val(email);
    $('#usersSessionTimeout').val(sessionTimeout);
    $('#userProperties').val(properties);
    // Disable login type selection
    $('#userLoginType').prop('disabled', true);

    // Enable/disable fields based on role
    const isSiteAdmin = roleName === "SiteAdmin";
    $('#chk-LDAP, #dashboardMode').prop('disabled', isSiteAdmin);

    // Handle dashboard preferences
    const showDashboard = Boolean(isDefaultDashboard);
    $('#dashboardMode').prop('checked', showDashboard);
    $('#dashboardModeValue').val(showDashboard);
    $('#dashboardList').val(showDashboard ? url : '');
    $('#dashboardInputContainer').toggleClass('d-none', !showDashboard);

    // Handle mobile number if valid
    if (mobile && mobile !== "NA") {
        const [countryCode = '', number = ''] = mobile.split('-');
        $('#userCountryCode').val(countryCode);
        $('#userMobileNum').val(number);
        $('#chk-LDAP').prop('checked', true);
        $('#mobileInputContainer').show();
    } else {
        $('#chk-LDAP').prop('checked', false);
        $('#mobileInputContainer').hide();
        $('#textAlertMode').val('');
        $('#AlertMode-error').text('').removeClass('field-validation-error');
    }

    // Preferred workflow type
    $('#chk-workflowtype').prop('checked', Boolean(isPreferredMode));

    // Hide confirmation email section
    $('#confirmation_mail').addClass('d-none');

    // Clear all possible error fields
    const errorFields = [
        '#LoginName-error', '#LoginPassword-error', '#ConfirmPassword-error',
        '#Company-error', '#UserName-error', '#Role-error', '#Email-error',
        '#AlertMode-error', '#SessionTimeout-error', '#MobilePre-error',
        '#Mobile-error', '#treeList-error', '#dashboard-error'
    ];
    errorFields.forEach(selector => {
        $(selector).text('').removeClass('field-validation-error');
    });
}