using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MongoDbMonitorStatusFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";

    public List<MongoDbMonitorStatus> MongoDbMonitorStatusPaginationList { get; set; }
    public List<MongoDbMonitorStatus> MongoDbMonitorStatusList { get; set; }
    public MongoDbMonitorStatus MongoDbMonitorStatusDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MongoDbMonitorStatusFixture()
    {
        _fixture = new Fixture();
        
        // Configure AutoFixture to generate valid data
        _fixture.Customize<MongoDbMonitorStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => _fixture.Create<string>())
            .With(x => x.InfraObjectId, () => Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .With(x => x.LastModifiedBy, UserId)
            .With(x => x.LastModifiedDate, DateTime.UtcNow)
        );

        MongoDbMonitorStatusList = _fixture.Create<List<MongoDbMonitorStatus>>();
        MongoDbMonitorStatusPaginationList = _fixture.CreateMany<MongoDbMonitorStatus>(20).ToList();
        MongoDbMonitorStatusDto = _fixture.Create<MongoDbMonitorStatus>();
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MongoDbMonitorStatus CreateMongoDbMonitorStatus(
        string type = null,
        string infraObjectId = null,
        string referenceId = null,
        bool isActive = true)
    {
        var mongoDbMonitorStatus = _fixture.Create<MongoDbMonitorStatus>();
        
        if (!string.IsNullOrEmpty(type))
            mongoDbMonitorStatus.Type = type;
            
        if (!string.IsNullOrEmpty(infraObjectId))
            mongoDbMonitorStatus.InfraObjectId = infraObjectId;
            
        if (!string.IsNullOrEmpty(referenceId))
            mongoDbMonitorStatus.ReferenceId = referenceId;
            
        mongoDbMonitorStatus.IsActive = isActive;
            
        return mongoDbMonitorStatus;
    }

    public MongoDbMonitorStatus CreateMongoDbMonitorStatusWithProperties(
        string type = null,
        string infraObjectId = null,
        string infraObjectName = null,
        string workflowId = null,
        string workflowName = null,
        string properties = null,
        string configuredRPO = null,
        string dataLagValue = null,
        string threshold = null,
        bool isActive = true)
    {
        var mongoDbMonitorStatus = CreateMongoDbMonitorStatus(type, infraObjectId, null, isActive);
        
        if (!string.IsNullOrEmpty(infraObjectName))
            mongoDbMonitorStatus.InfraObjectName = infraObjectName;
            
        if (!string.IsNullOrEmpty(workflowId))
            mongoDbMonitorStatus.WorkflowId = workflowId;
            
        if (!string.IsNullOrEmpty(workflowName))
            mongoDbMonitorStatus.WorkflowName = workflowName;
            
        if (!string.IsNullOrEmpty(properties))
            mongoDbMonitorStatus.Properties = properties;
            
        if (!string.IsNullOrEmpty(configuredRPO))
            mongoDbMonitorStatus.ConfiguredRPO = configuredRPO;
            
        if (!string.IsNullOrEmpty(dataLagValue))
            mongoDbMonitorStatus.DataLagValue = dataLagValue;
            
        if (!string.IsNullOrEmpty(threshold))
            mongoDbMonitorStatus.Threshold = threshold;
        
        return mongoDbMonitorStatus;
    }

    public List<MongoDbMonitorStatus> CreateMultipleMongoDbMonitorStatuses(
        int count, 
        string baseType = "MongoDBType",
        string baseInfraObjectId = null)
    {
        var mongoDbMonitorStatuses = new List<MongoDbMonitorStatus>();
        var infraObjectId = baseInfraObjectId ?? Guid.NewGuid().ToString();
        
        for (int i = 1; i <= count; i++)
        {
            var mongoDbMonitorStatus = CreateMongoDbMonitorStatus($"{baseType}{i}", infraObjectId);
            mongoDbMonitorStatuses.Add(mongoDbMonitorStatus);
        }
        
        return mongoDbMonitorStatuses;
    }

    public MongoDbMonitorStatus CreateInactiveMongoDbMonitorStatus(string type = null)
    {
        return CreateMongoDbMonitorStatusWithProperties(type: type, isActive: false);
    }

    public MongoDbMonitorStatus CreateMongoDbMonitorStatusWithSpecificType(string type)
    {
        return CreateMongoDbMonitorStatus(type: type);
    }

    public MongoDbMonitorStatus CreateMongoDbMonitorStatusWithSpecificInfraObjectId(string infraObjectId)
    {
        return CreateMongoDbMonitorStatus(infraObjectId: infraObjectId);
    }

    public MongoDbMonitorStatus CreateMongoDbMonitorStatusWithSpecialCharacters()
    {
        return CreateMongoDbMonitorStatusWithProperties(
            type: "MongoDB@Replica#Set$123!",
            infraObjectName: "Infra@Object#Name$456!",
            properties: "Special@Properties#With$Characters!"
        );
    }

    public MongoDbMonitorStatus CreateMongoDbMonitorStatusWithUnicodeCharacters()
    {
        return CreateMongoDbMonitorStatusWithProperties(
            type: "MongoDB复制集",
            infraObjectName: "インフラオブジェクト",
            properties: "خصائص خاصة"
        );
    }

    public MongoDbMonitorStatus CreateMongoDbMonitorStatusWithEmptyType()
    {
        return CreateMongoDbMonitorStatus(type: "");
    }

    public MongoDbMonitorStatus CreateMongoDbMonitorStatusWithNullType()
    {
        var mongoDbMonitorStatus = CreateMongoDbMonitorStatus();
        mongoDbMonitorStatus.Type = null;
        return mongoDbMonitorStatus;
    }

    public MongoDbMonitorStatus CreateMongoDbMonitorStatusWithLongType(int length)
    {
        var longType = new string('A', length);
        return CreateMongoDbMonitorStatus(type: longType);
    }

    public MongoDbMonitorStatus CreateMongoDbMonitorStatusWithWhitespace()
    {
        return CreateMongoDbMonitorStatus(type: "   MongoDB Type   ");
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = 
        {
            "MongoDB Replica Set",
            "MongoDB Sharded Cluster",
            "MongoDB Standalone",
            "MongoDB Atlas",
            "MongoDB GridFS"
        };
        
        public static readonly string[] SpecialCharacterTypes =
        {
            "MongoDB@Replica#Set",
            "MongoDB$Cluster%Test",
            "MongoDB&Atlas*Config",
            "MongoDB+GridFS-Setup"
        };
        
        public static readonly string[] UnicodeTypes =
        {
            "MongoDB复制集",
            "MongoDBレプリカセット",
            "MongoDB복제세트",
            "MongoDBРепликаСет",
            "MongoDBمجموعةالنسخ"
        };
        
        public static readonly string ValidGuid = Guid.NewGuid().ToString();
        public static readonly string InvalidGuid = "INVALID_GUID";
        public static readonly string EmptyGuid = Guid.Empty.ToString();
    }
}
