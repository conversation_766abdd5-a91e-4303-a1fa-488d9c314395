namespace ContinuityPatrol.Domain.ViewModels.DriftJobModel;

public record DriftJobListVm
{
    public string Id { get; set; }
	public string Name { get; set; }
	public string Properties { get; set; }
    public string SolutionTypeId { get; set; }
    public string SolutionTypeName { get; set; }
    public string NodeId { get; set; }
	public string NodeName { get; set; }
	public string Status { get; set; }
	public string State { get; set; }
	public string CronExpression { get; set; }
	public int IsSchedule { get; set; }
	public int ScheduleType { get; set; }
	public string ScheduleTime { get; set; }
	public string ExceptionMessage { get; set; }
    public string LastExecutionTime { get; set; }

}
