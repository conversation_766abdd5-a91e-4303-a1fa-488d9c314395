using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DrReady.Commands.Create;
using ContinuityPatrol.Application.Features.DrReady.Commands.Delete;
using ContinuityPatrol.Application.Features.DrReady.Commands.Update;
using ContinuityPatrol.Application.Features.DrReady.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DrReady.Queries.GetDrReadyByBusinessServiceId;
using ContinuityPatrol.Domain.ViewModels.DrReadyModel;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DrReadyControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DrReadysController _controller;
    private readonly DrReadyFixture _drReadyFixture;

    public DrReadyControllerTests()
    {
        _drReadyFixture = new DrReadyFixture();

        var testBuilder = new ControllerTestBuilder<DrReadysController>();
        _controller = testBuilder.CreateController(
            _ => new DrReadysController(),
            out _mediatorMock);
    }

    #region Core CRUD Operations

    [Fact]
    public async Task CreateDrReady_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _drReadyFixture.CreateDrReadyCommand;
        var expectedResponse = _drReadyFixture.CreateDrReadyResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrReady(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDrReadyResponse>(createdResult.Value);
        Assert.Equal("Enterprise DR Ready status created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDrReady_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _drReadyFixture.UpdateDrReadyCommand;
        var expectedResponse = _drReadyFixture.UpdateDrReadyResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDrReady(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDrReadyResponse>(okResult.Value);
        Assert.Equal("Enterprise DR Ready status updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task DeleteDrReady_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var drReadyId = Guid.NewGuid().ToString();
        var expectedResponse = _drReadyFixture.DeleteDrReadyResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDrReadyCommand>(c => c.Id == drReadyId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDrReady(drReadyId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDrReadyResponse>(okResult.Value);
        Assert.Equal("Enterprise DR Ready status deleted successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
    }

    [Fact]
    public async Task GetDrReadyById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var drReadyId = Guid.NewGuid().ToString();
        var expectedDetail = _drReadyFixture.DrReadyDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrReadyDetailQuery>(q => q.Id == drReadyId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDrReadyById(drReadyId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DrReadyDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Detail Business Service", returnedDetail.BusinessServiceName);
        Assert.Equal(35, returnedDetail.TotalBusinessFunction);
        Assert.Equal(200, returnedDetail.TotalInfraObject);
        Assert.Equal(30, returnedDetail.BFDRReady);
        Assert.Equal(180, returnedDetail.InfraDRReady);
    }

    [Fact]
    public async Task GetDrReadyByBusinessServiceId_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var expectedDetail = _drReadyFixture.DrReadyByBusinessServiceIdVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrReadyByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDrReadyByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DrReadyByBusinessServiceIdVm>(okResult.Value);
        Assert.Equal(40, returnedDetail.TotalBusinessFunction);
        Assert.Equal(220, returnedDetail.TotalInfraObject);
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public async Task CreateDrReady_CallsClearDataCache()
    {
        // Arrange
        var command = _drReadyFixture.CreateDrReadyCommand;
        var expectedResponse = _drReadyFixture.CreateDrReadyResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.CreateDrReady(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task UpdateDrReady_CallsClearDataCache()
    {
        // Arrange
        var command = _drReadyFixture.UpdateDrReadyCommand;
        var expectedResponse = _drReadyFixture.UpdateDrReadyResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.UpdateDrReady(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task DeleteDrReady_CallsClearDataCache()
    {
        // Arrange
        var drReadyId = Guid.NewGuid().ToString();
        var expectedResponse = _drReadyFixture.DeleteDrReadyResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDrReadyCommand>(c => c.Id == drReadyId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.DeleteDrReady(drReadyId);

        // Assert
        _mediatorMock.Verify(m => m.Send(It.Is<DeleteDrReadyCommand>(c => c.Id == drReadyId), default), Times.Once);
    }

    #endregion

    
    [Fact]
    public async Task CreateDrReady_HandlesLargeEnterpriseEnvironment()
    {
        // Arrange
        var largeEnterpriseCommand = _drReadyFixture.CreateDrReadyCommand;
        largeEnterpriseCommand.BusinessServiceName = "Enterprise Large Scale Business Service";
        largeEnterpriseCommand.TotalBusinessFunction = 100;
        largeEnterpriseCommand.BFAvailable = 95;
        largeEnterpriseCommand.BFImpact = 5;
        largeEnterpriseCommand.BFDRReady = 85;
        largeEnterpriseCommand.BFDRNotReady = 15;
        largeEnterpriseCommand.TotalInfraObject = 1000;
        largeEnterpriseCommand.InfraAvailable = 980;
        largeEnterpriseCommand.InfraImpact = 20;
        largeEnterpriseCommand.InfraDRReady = 900;
        largeEnterpriseCommand.InfraDRNotReady = 100;

        var expectedResponse = _drReadyFixture.CreateDrReadyResponse;

        _mediatorMock
            .Setup(m => m.Send(largeEnterpriseCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrReady(largeEnterpriseCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDrReadyResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Large Scale Business Service", largeEnterpriseCommand.BusinessServiceName);
        Assert.Equal(100, largeEnterpriseCommand.TotalBusinessFunction);
        Assert.Equal(1000, largeEnterpriseCommand.TotalInfraObject);
        Assert.Equal(85, largeEnterpriseCommand.BFDRReady);
        Assert.Equal(900, largeEnterpriseCommand.InfraDRReady);
    }

    [Fact]
    public async Task UpdateDrReady_HandlesImprovementScenario()
    {
        // Arrange
        var improvementCommand = _drReadyFixture.UpdateDrReadyCommand;
        improvementCommand.BusinessServiceName = "Enterprise Improved Business Service";
        improvementCommand.TotalBusinessFunction = 50;
        improvementCommand.BFAvailable = 50;
        improvementCommand.BFImpact = 0;
        improvementCommand.BFDRReady = 48;
        improvementCommand.BFDRNotReady = 2;
        improvementCommand.TotalInfraObject = 300;
        improvementCommand.InfraAvailable = 300;
        improvementCommand.InfraImpact = 0;
        improvementCommand.InfraDRReady = 290;
        improvementCommand.InfraDRNotReady = 10;

        var expectedResponse = _drReadyFixture.UpdateDrReadyResponse;

        _mediatorMock
            .Setup(m => m.Send(improvementCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDrReady(improvementCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDrReadyResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Improved Business Service", improvementCommand.BusinessServiceName);
        Assert.Equal(0, improvementCommand.BFImpact);
        Assert.Equal(0, improvementCommand.InfraImpact);
        Assert.Equal(48, improvementCommand.BFDRReady);
        Assert.Equal(290, improvementCommand.InfraDRReady);
    }

    [Fact]
    public async Task GetDrReadyByBusinessServiceId_HandlesNullBusinessServiceId()
    {
        // Arrange
        string? businessServiceId = null;
        var expectedDetail = _drReadyFixture.DrReadyByBusinessServiceIdVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrReadyByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDrReadyByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DrReadyByBusinessServiceIdVm>(okResult.Value);
        Assert.NotNull(returnedDetail);
    }

    [Fact]
    public async Task CreateDrReady_HandlesHighAvailabilityRequirements()
    {
        // Arrange
        var haCommand = _drReadyFixture.CreateDrReadyCommand;
        haCommand.BusinessServiceName = "Enterprise High Availability Service";
        haCommand.TotalBusinessFunction = 75;
        haCommand.BFAvailable = 75;
        haCommand.BFImpact = 0;
        haCommand.BFDRReady = 75;
        haCommand.BFDRNotReady = 0;
        haCommand.TotalInfraObject = 500;
        haCommand.InfraAvailable = 500;
        haCommand.InfraImpact = 0;
        haCommand.InfraDRReady = 500;
        haCommand.InfraDRNotReady = 0;

        var expectedResponse = _drReadyFixture.CreateDrReadyResponse;

        _mediatorMock
            .Setup(m => m.Send(haCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrReady(haCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDrReadyResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise High Availability Service", haCommand.BusinessServiceName);
        Assert.Equal(0, haCommand.BFDRNotReady);
        Assert.Equal(0, haCommand.InfraDRNotReady);
        Assert.Equal(75, haCommand.BFDRReady);
        Assert.Equal(500, haCommand.InfraDRReady);
    }

 
   
    [Fact]
    public async Task CreateDrReady_WithMultiTierArchitecture_ReturnsCreatedResult()
    {
        // Arrange
        var multiTierCommand = _drReadyFixture.CreateDrReadyCommand;
        multiTierCommand.BusinessServiceName = "Enterprise Multi-Tier Business Service";
        multiTierCommand.TotalBusinessFunction = 45;
        multiTierCommand.BFAvailable = 42;
        multiTierCommand.BFImpact = 3;
        multiTierCommand.BFDRReady = 38;
        multiTierCommand.BFDRNotReady = 7;
        multiTierCommand.TotalInfraObject = 300;
        multiTierCommand.InfraAvailable = 285;
        multiTierCommand.InfraImpact = 15;
        multiTierCommand.InfraDRReady = 270;
        multiTierCommand.InfraDRNotReady = 30;

        var expectedResponse = _drReadyFixture.CreateDrReadyResponse;
        expectedResponse.Message = "Enterprise Multi-Tier DR Ready status created successfully!";

        _mediatorMock
            .Setup(m => m.Send(multiTierCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrReady(multiTierCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDrReadyResponse>(createdResult.Value);
        Assert.Equal("Enterprise Multi-Tier DR Ready status created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDrReady_WithCloudHybridConfiguration_ReturnsOkResult()
    {
        // Arrange
        var cloudHybridCommand = _drReadyFixture.UpdateDrReadyCommand;
        cloudHybridCommand.BusinessServiceName = "Enterprise Cloud Hybrid Business Service";
        cloudHybridCommand.TotalBusinessFunction = 60;
        cloudHybridCommand.BFAvailable = 55;
        cloudHybridCommand.BFImpact = 5;
        cloudHybridCommand.BFDRReady = 50;
        cloudHybridCommand.BFDRNotReady = 10;
        cloudHybridCommand.TotalInfraObject = 400;
        cloudHybridCommand.InfraAvailable = 380;
        cloudHybridCommand.InfraImpact = 20;
        cloudHybridCommand.InfraDRReady = 360;
        cloudHybridCommand.InfraDRNotReady = 40;

        var expectedResponse = _drReadyFixture.UpdateDrReadyResponse;
        expectedResponse.Message = "Enterprise Cloud Hybrid DR Ready status updated successfully!";

        _mediatorMock
            .Setup(m => m.Send(cloudHybridCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDrReady(cloudHybridCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDrReadyResponse>(okResult.Value);
        Assert.Equal("Enterprise Cloud Hybrid DR Ready status updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task GetDrReadyById_WithMicroservicesArchitecture_ReturnsOkResult()
    {
        // Arrange
        var drReadyId = Guid.NewGuid().ToString();
        var microservicesDrReady = _drReadyFixture.DrReadyDetailVm;
        microservicesDrReady.Id = drReadyId;
        microservicesDrReady.BusinessServiceName = "Enterprise Microservices Business Service";
        microservicesDrReady.TotalBusinessFunction = 80;
        microservicesDrReady.BFAvailable = 75;
        microservicesDrReady.BFImpact = 5;
        microservicesDrReady.BFDRReady = 70;
        microservicesDrReady.BFDRNotReady = 10;
        microservicesDrReady.TotalInfraObject = 500;
        microservicesDrReady.InfraAvailable = 480;
        microservicesDrReady.InfraImpact = 20;
        microservicesDrReady.InfraDRReady = 450;
        microservicesDrReady.InfraDRNotReady = 50;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrReadyDetailQuery>(q => q.Id == drReadyId), default))
            .ReturnsAsync(microservicesDrReady);

        // Act
        var result = await _controller.GetDrReadyById(drReadyId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDrReady = Assert.IsType<DrReadyDetailVm>(okResult.Value);
        Assert.Equal(drReadyId, returnedDrReady.Id);
        Assert.Equal("Enterprise Microservices Business Service", returnedDrReady.BusinessServiceName);
        Assert.Equal(80, returnedDrReady.TotalBusinessFunction);
        Assert.Equal(75, returnedDrReady.BFAvailable);
        Assert.Equal(70, returnedDrReady.BFDRReady);
        Assert.Equal(500, returnedDrReady.TotalInfraObject);
        Assert.Equal(450, returnedDrReady.InfraDRReady);
    }

    [Fact]
    public async Task GetDrReadyByBusinessServiceId_WithHighAvailabilityRequirements_ReturnsOkResult()
    {
        // Arrange
        var totalBusinessFunction = 100;
        var businessServiceId = Guid.NewGuid().ToString();
        var expectedDetail = _drReadyFixture.DrReadyByBusinessServiceIdVm; 
        expectedDetail.TotalBusinessFunction = totalBusinessFunction; 

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrReadyByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDrReadyByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DrReadyByBusinessServiceIdVm>(okResult.Value);
        Assert.Equal(totalBusinessFunction, returnedDetail.TotalBusinessFunction);
    }

    [Fact]
    public async Task DeleteDrReady_WithEnterpriseCompliance_ReturnsOkResult()
    {
        // Arrange
        var drReadyId = Guid.NewGuid().ToString();
        var expectedResponse = _drReadyFixture.DeleteDrReadyResponse;
        expectedResponse.Message = "Enterprise DR Ready status deleted successfully with compliance audit!";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDrReadyCommand>(c => c.Id == drReadyId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDrReady(drReadyId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDrReadyResponse>(okResult.Value);
        Assert.Equal("Enterprise DR Ready status deleted successfully with compliance audit!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
    }

}
