﻿using ContinuityPatrol.Application.Features.AlertInformation.Commands.Create;
using ContinuityPatrol.Application.Features.AlertInformation.Commands.Update;
using ContinuityPatrol.Application.Features.AlertInformation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AlertInformation.Queries.GetDetailByCode;
using ContinuityPatrol.Application.Features.AlertInformation.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertInformationModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Alert;
public class AlertInformationService : BaseClient, IAlertInformationService
{
	public AlertInformationService(IConfiguration config,IAppCache cache,ILogger<AlertInformationService> logger):base(config,cache, logger)
	{
			
	}

    public async Task<BaseResponse> CreateAsync(CreateAlertInformationCommand createAlertInformationCommand)
    {
        var request = new RestRequest("api/v6/alertinformation", Method.Post);

        ClearCache("GetAlertInformationList");

        request.AddJsonBody(createAlertInformationCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/alertinformation/{id}", Method.Delete);

        ClearCache("GetAlertInformationList");

        return await Delete<BaseResponse>(request);
    }

    public async Task<List<AlertInformationListVm>> GetAlertInformationList()
    {
        var request = new RestRequest("/api/v6/alertinformation");

        return await GetFromCache<List<AlertInformationListVm>>(request, "GetAlertInformationList");
    }

    public async  Task<List<AlertInformationDetailByCodeVm>> GetAlertInformationByCode(string code)
    {
        var request = new RestRequest($"api/v6/alertinformation/code?code={code}");

        return await Get<List<AlertInformationDetailByCodeVm>>(request);
    }

    public async Task<AlertInformationDetailVm> GetAlertInformationById(string id)
    {
        var request = new RestRequest($"api/v6/alertinformation/{id}");

        return await Get<AlertInformationDetailVm>(request);
    }

    public async Task<PaginatedResult<AlertInformationListVm>> GetPaginatedAlertInformation(GetAlertInformationPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/alerts/paginated-list");

        return await Get<PaginatedResult<AlertInformationListVm>>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateAlertInformationCommand updateAlertInformationCommand)
    {
        var request = new RestRequest("api/v6/alertinformation", Method.Put);

        ClearCache("GetAlertInformationList");

        request.AddJsonBody(updateAlertInformationCommand);

        return await Put<BaseResponse>(request);
    }
}