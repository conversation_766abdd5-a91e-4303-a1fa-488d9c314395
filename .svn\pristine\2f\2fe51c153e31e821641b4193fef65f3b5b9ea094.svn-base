﻿using ContinuityPatrol.Application.Features.UserRole.Commands.Create;
using ContinuityPatrol.Application.Features.UserRole.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoUserRoleDataAttribute : AutoDataAttribute
{
    public AutoUserRoleDataAttribute()
       : base(() =>
       {
           var fixture = new Fixture();

           fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateUserRoleCommand>(p => p.Role, 10));
           fixture.Customize<CreateUserRoleCommand>(c => c.With(b => b.Role, 0.ToString));

           fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateUserRoleCommand>(p => p.Role, 10));
           fixture.Customize<UpdateUserRoleCommand>(c => c.With(b => b.Id, 0.ToString));

           return fixture;
       })
    {

    }
}