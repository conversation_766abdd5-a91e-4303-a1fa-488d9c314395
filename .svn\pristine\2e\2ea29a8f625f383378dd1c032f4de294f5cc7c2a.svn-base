﻿using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Create;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.TestConnection;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Update;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateDefault;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateNodeStatus;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetNamesByType;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.LoadBalancerModel;
using ContinuityPatrol.Domain.ViewModels.StateMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using Microsoft.AspNetCore.Mvc;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class LoadBalancerService : BaseClient, ILoadBalancerService
{
    public LoadBalancerService(IConfiguration config, IAppCache cacheService, ILogger<LoadBalancerService> logger)
    : base(config, cacheService, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateLoadBalancerCommand createNodeConfigurationCommand)
    {
        var request = new RestRequest("api/v6/nodeconfiguration", Method.Post);

        request.AddJsonBody(createNodeConfigurationCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> TestConnection(LoadBalancerTestConnectionCommand nodeConfigurationTestConnectionCommand)
    {
        var request = new RestRequest("api/v6/nodeconfiguration", Method.Post);

        request.AddJsonBody(nodeConfigurationTestConnectionCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateLoadBalancerCommand updateNodeConfigurationCommand)
    {
        var request = new RestRequest("api/v6/nodeconfiguration", Method.Put);

        request.AddJsonBody(updateNodeConfigurationCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string loadBalancerId)
    {
        var request = new RestRequest($"api/v6/nodeconfiguration/{loadBalancerId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<List<LoadBalancerListVm>> GetLoadBalancerList()
    {
        var request = new RestRequest("api/v6/nodeconfiguration");

        return await Get<List<LoadBalancerListVm>>(request);
    }

    public async Task<bool> IsLoadBalancerNameExist(string loadBalancerName, string id)
    {
        var request = new RestRequest($"api/v6/nodeconfiguration/name-exist?nodeConfigurationName={loadBalancerName}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<LoadBalancerDetailVm> GetLoadBalancerById(string id)
    {
        var request = new RestRequest($"api/v6/nodeconfiguration/{id}");

        return await Get<LoadBalancerDetailVm>(request);
    }

    public async Task<PaginatedResult<LoadBalancerListVm>> GetPaginatedNodeConfigurations(GetLoadBalancerPaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/nodeconfiguration/paginated-list{query}");

        return await Get<PaginatedResult<LoadBalancerListVm>>(request);
    }

    public async Task<List<StateMonitorStatusListVm>> GetStateMonitorStatusList()
    {
        var request = new RestRequest($"api/v6/statemonitorstatus");

        return await Get<List<StateMonitorStatusListVm>>(request);
    }

    public async  Task<bool> IsIpAddressAndPortExist(string ipAddress, int port, string id)
    {
        var request = id.IsValidGuid() ? new RestRequest($"api/v6/loadbalancer/ipaddress-port-exist?ipAddress={ipAddress}&port={port}&id={id}")
            : new RestRequest($"api/v6/loadbalancer/ipaddress-port-exist?ipAddress={ipAddress}&port={port}");
            
        return await Get<bool>(request);
    }

    public async Task<UpdateNodeStatusResponse> UpdateNodeStatus(UpdateNodeStatusCommand command)
    {
        var request = new RestRequest("api/v6/loadbalancer/update-node-status", Method.Put);

        request.AddJsonBody(command);

        return await Put<UpdateNodeStatusResponse>(request);
    }

    public async Task<UpdateLoadBalancerDefaultResponse> UpdateDefault(UpdateLoadBalancerDefaultCommand command)
    {
        var request = new RestRequest("api/v6/loadbalancer/update-default", Method.Put);

        request.AddJsonBody(command);

        return await Put<UpdateLoadBalancerDefaultResponse>(request);
    }

    public async Task<List<LoadBalancerNameVm>> GetLoadBalancerNamesByType(string? type=null)
    {
        var request = new RestRequest($"api/v6/loadbalancer/nodeName");

        return await Get<List<LoadBalancerNameVm>>(request);
    }
}