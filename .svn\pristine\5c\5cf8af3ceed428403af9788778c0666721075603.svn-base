﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.Database.Commands.SaveAll;

public class SaveAllDatabaseCommandHandler : IRequestHandler<SaveAllDatabaseCommand, SaveAllDatabaseResponse>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    public SaveAllDatabaseCommandHandler(IDatabaseRepository databaseRepository, ILoggedInUserService loggedInUserService)
    {
        _databaseRepository = databaseRepository;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<SaveAllDatabaseResponse> Handle(SaveAllDatabaseCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _databaseRepository.GetByReferenceIdAsync(request.DatabaseId);

        Guard.Against.NullOrDeactive(eventToUpdate, nameof(Domain.Entities.Database),
            new NotFoundException(nameof(Domain.Entities.Database), request.DatabaseName));

        var databaseList = request.DatabaseList.Select(database => new Domain.Entities.Database
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = database.Name,
            Type = eventToUpdate.Type,
            ServerId = database.ServerId,
            ServerName = database.ServerName,
            ModeType = "Pending",
            DatabaseTypeId = database.DatabaseTypeId,
            DatabaseType = database.DatabaseType,
            Properties = database.Properties,
            LicenseId = database.LicenseId,
            LicenseKey = database.LicenseKey,
            CompanyId = eventToUpdate.CompanyId,
            BusinessServiceId = eventToUpdate.BusinessServiceId,
            BusinessServiceName = eventToUpdate.BusinessServiceName,
            Version = eventToUpdate.Version,
            FormVersion = eventToUpdate.FormVersion,
            IsActive = true,
            CreatedBy = _loggedInUserService.UserId,
            CreatedDate = DateTime.Now,
            LastModifiedBy = _loggedInUserService.UserId,
            LastModifiedDate = DateTime.Now

        }).ToList();

        await _databaseRepository.AddRange(databaseList);


        var response = new SaveAllDatabaseResponse
        {
            Message = "Database save-all successfully!."
        };

        return response;
    }
}