﻿using ContinuityPatrol.Application.Features.WorkflowPermission.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowPermission.Events;

public class DeleteWorkflowPermissionEventTests : IClassFixture<WorkflowPermissionFixture>, IClassFixture<UserActivityFixture>
{
    private readonly WorkflowPermissionFixture _workflowPermissionFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly WorkflowPermissionDeletedEventHandler _handler;

    public DeleteWorkflowPermissionEventTests(WorkflowPermissionFixture workflowPermissionFixture, UserActivityFixture userActivityFixture)
    {
        _workflowPermissionFixture = workflowPermissionFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockSiteTypeEventLogger = new Mock<ILogger<WorkflowPermissionDeletedEventHandler>>();

        _mockUserActivityRepository = WorkflowPermissionRepositoryMocks.CreateWorkflowPermissionEventRepository(_userActivityFixture.UserActivities);

        _handler = new WorkflowPermissionDeletedEventHandler(mockLoggedInUserService.Object, mockSiteTypeEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteWorkflowPermissionEventDeleted()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_workflowPermissionFixture.WorkflowPermissionDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowPermissionFixture.WorkflowPermissionDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}