﻿using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Services.Exceptions;
using System.Net;

namespace ContinuityPatrol.Shared.Services.Extension
{
    public static class RestResponseExtension
    {
        public static string DetailErrorMessage(this RestResponse response)
        {
            var resultMessage = "";

            if (!response.IsSuccessStatusCode)
            {
                if (string.IsNullOrWhiteSpace(resultMessage))
                {
                    resultMessage = "An error occurred while processing the request: "
                                    + response.StatusDescription;
                }
            }

            if (response.ErrorException == null) return resultMessage;

            if (string.IsNullOrWhiteSpace(resultMessage))
            {
                resultMessage = "An exception occurred while processing the request: "
                                + response.ErrorException.Message;
            }
            return resultMessage;
        }
        public static void Throw(this RestResponse response)
        {
            if (response.StatusCode.Equals(HttpStatusCode.Unauthorized))
            {
                if (response.Content != null)
                {
                    throw JsonConvert.DeserializeObject<AuthenticationException>(response.Content!);
                }

                throw new AuthenticationException(response.ErrorException != null ? response.ErrorException.Message : "Unauthorized", 10006);
            }

            throw new RestApiException(response.ErrorException != null
                ? response.ErrorException.Message
                : "Bad Request");
        }
    }
}
