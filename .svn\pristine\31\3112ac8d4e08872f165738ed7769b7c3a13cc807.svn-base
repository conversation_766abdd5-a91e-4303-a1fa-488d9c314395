﻿
let types = [], dataTable, selectedValues = [];
function ExecutionHistorydebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
$(function () {
    dataTable = $('#WorkflowScheduleExecutionHistorytabledata').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                },

            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/ITAutomation/WorkflowScheduleExecutionHistory/GetPagination",
                "dataType": "json",

                "data": function (d) {
                    //let sortIndex = d?.order[0]?.column || '';
                    //let sortValue = sortIndex === 1 ? "type" : sortIndex === 2 ? "severity" : sortIndex === 3 ? "systemMessage" :
                    //    sortIndex === 4 ? "jobName" : sortIndex === 5 ? "infraObjectName" : sortIndex === 6 ? "createdDate" : "";
                    //let orderValue = d.order[0]?.dir || 'asc';


                    //d.sortColumn = sortValue;
                    //d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');

                    d.workflowType = $('#typeValue').val() === "All" ? '' : $('#typeValue').val()
                    d.start = $('#startDate').find("input[type=date]").val() === "" ? '' : $('#startDate').find("input[type=date]").val()
                    d.end = $('#endDate').find("input[type=date]").val() === "" ? '' : $('#endDate').find("input[type=date]").val();
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {

                        json.recordsTotal = json.data.totalPages;
                        json.recordsFiltered = json.data.totalCount;
                        if (json.data.data.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        json.data.data.forEach(function (item, i) {
                     
                            types.push(item.workflowType)
                        });
                             
                        types.forEach(function (value, index) {
                            $('#typeValue').append('<option value="' + value + '">' + value + '</option>')
                        })
                        $("#typeValue option").each(function () {
                            $(this).siblings('[value="' + this.value + '"]').remove()
                        })
                
                        return json.data.data;
                    } else {
                        errorNotification(json.data.data)
                    }

                }
            },
            "columnDefs": [
                {
                    "targets": [0, 1, 2, 3, 4, 5],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            //return meta.row + 1;
                            var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                            return (page * meta.settings._iDisplayLength) + meta.row + 1;
                        }
                        return data;
                    }, "orderable": false,
                },
                {
                    "data": "beforeSwitchOverWorkflowName", "name": "Workflow Name", "autoWidth": true, "render": function (data, type, row) {
                        return `<td><span  title="${data == null ? "NA" : data}">${data == null ? "NA" : data}</span></td>`;
                    }
                },
                //{
                //    "data": "infraObjectName",
                //    "name": "Infraobject Name",
                //    "autoWidth": true,
                //    "render": function (data, type, row) { 
                //        return `<td><i class="me-1 "></i> ${data == null ? "NA" : data} </td>`;
                //    }
                //},
                {
                    "data": "nodeName",
                    "name": "Node Name",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td class="truncate"><span  title="${data == null ? "NA" : data}">${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "data": "workflowType", "name": "Type", "autoWidth": true, "render": function (data, type, row) {
                        return `<td>${data == null ? "NA" : data} </td>`;
                    }
                },
                //{
                //    "data": "scheduleTime", "name": "scheduler", "autoWidth": true, "render": function (data, type, row) {
                //        return `<td> ${data == null ? "NA" : data} </td>`;
                //    }
                //},
                {
                    "data": 'createdDate', "name": "Start Time", "autoWidth": true, "render": function (data, type, row) {
                        return `<td> ${data == null ? "NA" : data.replace('T', ' ').split('.')[0]} </td>`;
                    }
                },
                {
                    "data": "lastExecutionTime", "name": "End Time", "autoWidth": true, "render": function (data, type, row) {
                        return `<td> ${data == null ? "NA" : data} </td>`;
                    }
                },
                {
                    "data": "status", "name": "Status", "autoWidth": true, "render": function (data, type, row) {
                        var iconClass = '';
                        if (data == "Pending") {
                            iconClass = "cp-pending text-warning me-1";
                        } else if (data == "Running") {
                            iconClass = "text-success cp-reload cp-animate me-1";
                        } else if (data == "Success") {
                            iconClass = "cp-success text-success me-1";
                        } else {
                            iconClass = "cp-error text-danger me-1";
                        }
                        return `<td><i class="${iconClass}" id="icon" title="${data || "NA"}" ></i></td>
                              <td><span id="jobmanagestate">${data || "NA"}</span></td>`;
                    }
                },
                {
                    "data": "exceptionMessage", "name": "Action", "autoWidth": true, "render": function (data, type, row) {
                        let errormsg = row.status.toLowerCase() === 'error' ? data : ""
                        return `<div class="d-flex align-items-center gap-2"> 
                                  <span title="Report" class="ButtonHistoryReport"  id="${row.beforeSwitchOverWorkflowId}" data-type="${row.beforeSwitchOverWorkflowName}" data-infraReferenceId="${row.id}"><i class="cp-report me-2"></i></span>
                                     <span title="Error Message" errorMessage="${btoa(errormsg)}" class=" Error-button ${row.status.toLowerCase() === 'error' ? '' : 'd-none' } " role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-fail-back blink text-danger"></i>                                    
                                                    </span>
                                  </div>`;
                    }
               },
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;

                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        }
    )
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });
    $('#search-inp').on('keydown input', ExecutionHistorydebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const inputValue = $('#search-inp').val();
        selectedValues.push(inputValue);
        var currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {

                if (e.target.value && json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }
    }, 500));
})
$("#startDate,#endDate").on("keypress", function (e) {
    e.preventDefault()
})
$("#typeValue,#startDate,#endDate").on('change', function () {
    dataTable.ajax.reload()
    if ($('#startDate').find("input[type=date]").val() == "" && $('#endDate').find("input[type=date]").val()) {
        $("#startdate-error").text("Select the start date before end date").addClass('field-validation-error');
        return false;
    }
    else if ($('#endDate').find("input[type=date]").val() != "" && $('#startDate').find("input[type=date]").val() > $('#endDate').find("input[type=date]").val()) {
        $("#startdate-error").text("Start date lesser than end date").addClass('field-validation-error');
        return false;
    } else if ($('#endDate').find("input[type=date]").val() == "" || $('#startDate').find("input[type=date]").val()) {
        $('#endDate').find("input[type=date]").val()
        $("#startdate-error").text('').removeClass('field-validation-error');
        return true;
    } else {
        $("#startdate-error").text('').removeClass('field-validation-error');
        return true;
    }
})
$('#WorkflowScheduleExecutionHistorytabledata').on('click', '.Error-button', function () {
    let noData = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img" style="padding:10px">'
    let job_error_message = atob($(this).attr('errorMessage'))
    if (!job_error_message || job_error_message == 'null') {
        $("#error_message").css('text-align', 'center')
            .html(noData);
    } else {
        $('#error_message').text(job_error_message);
    }
});

$(document).on('click', ".ButtonHistoryReport", async function () {
    let WorkFlowId = $(this).attr("id");
    let WorkFlowName = $(this).attr("data-type");
    let InfraReferenceId = $(this).attr("data-infraReferenceId");

    const url = `/ITAutomation/WorkflowScheduleExecutionHistory/DownloadReport?workflowId=${WorkFlowId}&workflowName=${WorkFlowName}&InfraReferenceId=${InfraReferenceId}`;
    const response = await fetch(url);
    if (response.ok) {
        const blob = await response.blob();
        var alertClass, reportIconClass, message;
        if (blob.size > 0 && blob.type === "application/pdf") {
            const DateTime = new Date().toLocaleString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit', fractionalSecondDigits: 3, hour12: false }).replace(/[^0-9]/g, '');
            downloadFile(blob, "ScheduledJobWorkflowReport_ " + WorkFlowName+ "_" + + DateTime + ".pdf", "application/pdf");
            alertClass = "success-toast";
            reportIconClass = "cp-check toast_icon";
            message = "Scheduled Job Workflow Report downloaded successfully";
        }
        else {
            alertClass = "warning-toast";
            reportIconClass = "cp-exclamation toast_icon";
            message = "No Data Found";
        }
    }
    else {
        alertClass = "warning-toast";
        reportIconClass = "cp-exclamation toast_icon";
        message = "Scheduled Job Workflow Report Download Error";
    }
    $('#alertClass').removeClass().addClass(alertClass);
    $('#icon_Detail').removeClass().addClass(reportIconClass);
    $('#notificationAlertmessage').text(message);
    $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
});
function downloadFile(blob, fileName, contentType) {
    try {
        const link = document.createElement("a");
        link.download = fileName;
        link.href = window.URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error("Error downloading file: " + error.message);
    }
}
