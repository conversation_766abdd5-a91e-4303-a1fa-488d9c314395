using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Events.Create;

public class
    DriftManagementMonitorStatusCreatedEventHandler : INotificationHandler<DriftManagementMonitorStatusCreatedEvent>
{
    private readonly ILogger<DriftManagementMonitorStatusCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DriftManagementMonitorStatusCreatedEventHandler(ILoggedInUserService userService,
        ILogger<DriftManagementMonitorStatusCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(DriftManagementMonitorStatusCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} DriftManagementMonitorStatus",
            Entity = "DriftManagementMonitorStatus",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"DriftManagementMonitorStatus '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"DriftManagementMonitorStatus '{createdEvent.Name}' created successfully.");
    }
}