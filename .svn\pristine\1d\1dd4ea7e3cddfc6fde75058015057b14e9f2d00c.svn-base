﻿namespace ContinuityPatrol.Application.Features.PostgresMonitorStatus.Commands.Update;

public class UpdatePostgresMonitorStatusCommandHandler : IRequestHandler<UpdatePostgresMonitorStatusCommand,
    UpdatePostgresMonitorStatusResponse>
{
    private readonly IMapper _mapper;
    private readonly IPostgresMonitorStatusRepository _postgresMonitorStatusRepository;

    public UpdatePostgresMonitorStatusCommandHandler(IMapper mapper,
        IPostgresMonitorStatusRepository postgresMonitorStatusRepository)
    {
        _mapper = mapper;
        _postgresMonitorStatusRepository = postgresMonitorStatusRepository;
    }

    public async Task<UpdatePostgresMonitorStatusResponse> Handle(UpdatePostgresMonitorStatusCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _postgresMonitorStatusRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.PostgresMonitorStatus), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdatePostgresMonitorStatusCommand),
            typeof(Domain.Entities.PostgresMonitorStatus));

        await _postgresMonitorStatusRepository.UpdateAsync(eventToUpdate);

        var response = new UpdatePostgresMonitorStatusResponse
        {
            Message = Message.Update(nameof(Domain.Entities.PostgresMonitorStatus), eventToUpdate.ReferenceId),
            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}