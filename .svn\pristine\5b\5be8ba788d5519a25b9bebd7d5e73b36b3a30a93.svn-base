﻿//using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Create;
//using ContinuityPatrol.Infrastructure.Contract;
//using ContinuityPatrol.Shared.Tests.Mocks;

//namespace ContinuityPatrol.Core.UnitTests.Domains.InfraObjectScheduler.Commands;

//public class CreateInfraObjectSchedulerTests : IClassFixture<InfraObjectSchedulerFixture>
//{
//    private readonly InfraObjectSchedulerFixture _infraObjectSchedulerFixture;

//    private readonly Mock<IInfraObjectSchedulerRepository> _mockInfraObjectSchedulerRepository;

//    private readonly CreateInfraObjectSchedulerCommandHandler _handler;

//    public CreateInfraObjectSchedulerTests(InfraObjectSchedulerFixture infraObjectSchedulerFixture)
//    {
//        _infraObjectSchedulerFixture = infraObjectSchedulerFixture;

//        var mockPublisher = new Mock<IPublisher>();

//        //var config = new Mock<IConfiguration>();

//        var mockConfigurationRepository = ConfigurationRepositoryMocks.GetValue();

//        var windowService = new Mock<IWindowsService>();

//        _mockInfraObjectSchedulerRepository = InfraObjectSchedulerRepositoryMocks.CreateInfraObjectSchedulerRepository(_infraObjectSchedulerFixture.InfraObjectSchedulers);

//        _handler = new CreateInfraObjectSchedulerCommandHandler(_infraObjectSchedulerFixture.Mapper, _mockInfraObjectSchedulerRepository.Object, mockPublisher.Object, mockConfigurationRepository.Object, windowService.Object);
//    }

//    [Fact]
//    public async Task Handle_Should_IncreaseInfraObjectSchedulerCount_When_AddValidInfraObjectScheduler()
//    {
//        _infraObjectSchedulerFixture.InfraObjectSchedulers[0].ReferenceId = "09c87262-6da8-4e45-9bd1-8500a14e6736";

//        await _handler.Handle(_infraObjectSchedulerFixture.CreateInfraObjectSchedulerCommand, CancellationToken.None);

//        var allCategories = await _mockInfraObjectSchedulerRepository.Object.ListAllAsync();

//        allCategories.Count.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers.Count);
//    }

//    [Fact]
//    public async Task Handle_Return_SuccessfulInfraObjectSchedulerResponse_When_AddValidInfraObjectScheduler()
//    {
//        _infraObjectSchedulerFixture.InfraObjectSchedulers[0].ReferenceId = "09c87262-6da8-4e45-9bd1-8500a14e6736";

//        var result = await _handler.Handle(_infraObjectSchedulerFixture.CreateInfraObjectSchedulerCommand, CancellationToken.None);

//        result.ShouldBeOfType(typeof(CreateInfraObjectSchedulerResponse));

//        result.InfraObjectSchedulerId.ShouldBeGreaterThan(0.ToString());

//        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
//    }

//    [Fact]
//    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
//    {
//        _infraObjectSchedulerFixture.InfraObjectSchedulers[0].ReferenceId = "09c87262-6da8-4e45-9bd1-8500a14e6736";

//        await _handler.Handle(_infraObjectSchedulerFixture.CreateInfraObjectSchedulerCommand, CancellationToken.None);

//        _mockInfraObjectSchedulerRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.InfraObjectScheduler>()), Times.Once);
//    }
//}