﻿namespace ContinuityPatrol.Application.Features.ReportSchedule.Queries.GetDetail;

public class GetReportScheduleDetailQueryHandler : IRequestHandler<GetReportScheduleDetailQuery, ReportScheduleDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IReportScheduleRepository _reportScheduleRepository;

    public GetReportScheduleDetailQueryHandler(IMapper mapper, IReportScheduleRepository reportScheduleRepository)
    {
        _mapper = mapper;
        _reportScheduleRepository = reportScheduleRepository;
    }

    public async Task<ReportScheduleDetailVm> Handle(GetReportScheduleDetailQuery request,
        CancellationToken cancellationToken)
    {
        var reportSchedule = await _reportScheduleRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(reportSchedule, nameof(Domain.Entities.ReportSchedule),
            new NotFoundException(nameof(Domain.Entities.ReportSchedule), request.Id));

        var reportScheduleDto = _mapper.Map<ReportScheduleDetailVm>(reportSchedule);

        return reportScheduleDto;
    }
}