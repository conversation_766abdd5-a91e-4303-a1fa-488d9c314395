﻿using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Domain.ViewModels.TeamMasterModel;

public class TeamMasterViewModel
{
    public string Id { get; set; }
    public string GroupName { get; set; }
    public string Description { get; set; }

    public string MemberCount { get; set; }
    public PaginatedResult<TeamMasterListVm> PaginatedTeamConfigurations { get; set; }
    public List<TeamMasterListVm> TeamConfigurationsList { get; set; }
}