﻿using ContinuityPatrol.Application.Features.Site.Commands.Create;
using ContinuityPatrol.Application.Features.Site.Commands.Update;
using ContinuityPatrol.Application.Features.Site.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SiteModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Domain.ViewModels.SiteLocationModel;
using ContinuityPatrol.Application.Features.Site.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class SiteController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IMapper _mapper;
    private readonly IDataProvider _provider;
    private readonly ILogger<SiteController> _logger;

    public SiteController(IPublisher publisher, IDataProvider provider, IMapper mapper, ILogger<SiteController> logger)
    {
        _publisher = publisher;
        _mapper = mapper;
        _provider = provider;
        _logger = logger;
    }
    [AntiXss]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in Site");

        await _publisher.Publish(new SitePaginatedEvent());
        var companyNames = await _provider.Company.GetCompanyNamesOnLogin();
        var siteTypes = await _provider.SiteType.GetSiteTypeList();
        var siteLocationList = await _provider.SiteLocation.GetSiteLocationList();

        var companies = _mapper.Map<List<SelectListItem>>(companyNames);
        var siteLocations = _mapper.Map<List<SiteLocationListVm>>(siteLocationList);
        var siteViewModel = new SiteViewModel
        {
            Companies = companies,
            SiteLocations = siteLocations,
            SiteTypes = siteTypes
        };
        return View(siteViewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Configuration.CreateAndEdit)]
    public async Task<IActionResult> SaveOrUpdate(SiteViewModel site)
    {
        _logger.LogDebug("Entering SaveOrUpdate method in Site");

        var siteId = Request.Form["id"].ToString();

        try
        {
            if (siteId.IsNullOrWhiteSpace())
            {
                _logger.LogDebug($"Creating Site '{site.Name}'");
                var createSite = _mapper.Map<CreateSiteCommand>(site);
                var response = await _provider.Site.CreateAsync(createSite);
                return Json(new { success = true, data = response });
            }
            else
            {
                _logger.LogDebug($"Updating Site '{site.Name}'");
                var updateCommand = _mapper.Map<UpdateSiteCommand>(site);
                var response = await _provider.Site.UpdateAsync(updateCommand);
                return Json(new { success = true, data = response });
            }
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Site option page while processing the request for save or update.", ex);

            if (ex is ValidationException vex)
            {
                return Json(new { success = false, errors = vex.ValidationErrors });
            }

            return ex.GetJsonException();
        }

    }
    [HttpDelete]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in Site");

        try
        {
            var response = await _provider.Site.DeleteAsync(id);

            _logger.LogDebug("Successfully deleted record in Site");

            return Json(new { success = true, data = response });

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on Site.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<IActionResult> IsSiteNameExist(string siteName, string id)
    {
        _logger.LogDebug("Entering IsSiteNameExist method in Site");

        try
        {
            var nameExist = await _provider.Site.IsSiteNameExist(siteName, id);

            _logger.LogDebug("Returning result for IsSiteNameExist on Site");

            return Json(new { success = true, data = nameExist });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on site page while checking if site name exists for : {siteName}.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetSiteById(string siteId)
    {
        _logger.LogDebug("Entering GetSiteById method in Site");

        try
        {
            var siteList = await _provider.Site.GetSiteById(siteId);

            _logger.LogDebug($"Successfully retrieved Site details by id '{siteId}'");

            return Json(new { success = true, data = siteList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on SingleSignOn page while retrieving the site by id .", ex);

            return ex.GetJsonException();
        }
    }


    [HttpGet]
    public async Task<IActionResult> GetSiteByTypeAndCompanyId(string companyId, string siteType)
    {
        try
        {
            _logger.LogDebug("Entering GetSiteByTypeAndCompanyId method in site");

            var siteList = await _provider.Site.GetSiteByTypeAndCompanyId(companyId, siteType);

            _logger.LogDebug($"Successfully retrieved Get Site By Type '{siteType}' And CompanyId '{companyId}' on Site");

            return Json(new { success = true, data = siteList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on SingleSignOn page while retrieving the site by companyId and siteType.", ex);

            return ex.GetJsonException();
        }


    }

    [HttpGet]
    public async Task<IActionResult> GetPagination(GetSitePaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in site");

        try
        {
            var result = await _provider.Site.GetSitePaginatedList(query);

            _logger.LogDebug("Successfully retrieved site paginated list on site page");

            return Json(new { success = true, data = result }); 
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on site page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }
    }


    public async Task<IActionResult> GetComponentDetails()
    {
        _logger.LogDebug("Entering GetComponentDetails method in site");

        try
        {
            var businessService = await _provider.BusinessService.GetBusinessServiceNames();
            var site = await _provider.Site.GetSiteNames();
            var operationalFunction = await _provider.BusinessFunction.GetBusinessFunctionNames();
            var server = await _provider.Server.GetServerNames();
            var database = await _provider.Database.GetDatabaseNames();
            var replication = await _provider.Replication.GetReplicationNames();
            var infraObject = await _provider.InfraObject.GetInfraObjectNames();
            var filteredPrServers = server.Where(a => a.ServerType == "PRDBServer").ToList();
            var filteredDrServers = server.Where(a => a.ServerType == "DRDBServer").ToList();
            var configCounts = new { sites = site.Count, operationService = businessService.Count, operationFunction = operationalFunction.Count, server = server.Count, database = database.Count, replication = replication.Count, infraObject = infraObject.Count };

            return Json(new { Success = true, data = configCounts, filteredPrServers, filteredDrServers });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on site page while retrieving GetComponentDetails.", ex);
            return ex.GetJsonException();
        }
    }
}