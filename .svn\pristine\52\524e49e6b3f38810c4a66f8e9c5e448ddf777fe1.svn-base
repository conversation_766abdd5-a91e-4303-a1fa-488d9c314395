using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetPaginatedList;

public class
    GetCyberAirGapPaginatedListQueryHandler : IRequestHandler<GetCyberAirGapPaginatedListQuery,
        PaginatedResult<CyberAirGapListVm>>
{
    private readonly ICyberAirGapRepository _cyberAirGapRepository;
    private readonly IMapper _mapper;

    public GetCyberAirGapPaginatedListQueryHandler(IMapper mapper, ICyberAirGapRepository cyberAirGapRepository)
    {
        _mapper = mapper;
        _cyberAirGapRepository = cyberAirGapRepository;
    }

    public async Task<PaginatedResult<CyberAirGapListVm>> Handle(GetCyberAirGapPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new CyberAirGapFilterSpecification(request.SearchString);

        var queryable =await _cyberAirGapRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var cyberAirGapList = _mapper.Map<PaginatedResult<CyberAirGapListVm>>(queryable);

        return cyberAirGapList;
        //var queryable = _cyberAirGapRepository.GetPaginatedQuery();

        //var productFilterSpec = new CyberAirGapFilterSpecification(request.SearchString);

        //var cyberAirGapList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<CyberAirGapListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return cyberAirGapList;
    }
}