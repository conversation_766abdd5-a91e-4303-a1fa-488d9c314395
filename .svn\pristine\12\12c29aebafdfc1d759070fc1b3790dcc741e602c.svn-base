﻿using ContinuityPatrol.Application.Features.CyberJobManagement.Events.UpdateStatus;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberJobManagement.Commands.UpdateJobStatus;

public class UpdateCyberJobManagementStatusCommandHandler : IRequestHandler<UpdateCyberJobManagementStatusCommand,
    UpdateCyberJobManagementStatusResponse>
{
    private readonly ICyberJobManagementRepository _cyberJobManagementRepository;
    private readonly ILoadBalancerRepository _loadBalancerRepository;
    private readonly ILogger<UpdateCyberJobManagementStatusCommandHandler> _logger;
    private readonly IPublisher _publisher;
    private readonly IWindowsService _windowsService;

    public UpdateCyberJobManagementStatusCommandHandler(ICyberJobManagementRepository cyberJobManagementRepository,
        IPublisher publisher, IWindowsService windowsService, ILoadBalancerRepository loadBalancerRepository,
        ILogger<UpdateCyberJobManagementStatusCommandHandler> logger)
    {
        _cyberJobManagementRepository = cyberJobManagementRepository;
        _publisher = publisher;
        _windowsService = windowsService;
        _loadBalancerRepository = loadBalancerRepository;
        _logger = logger;
    }


    public async Task<UpdateCyberJobManagementStatusResponse> Handle(UpdateCyberJobManagementStatusCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _cyberJobManagementRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(eventToUpdate, nameof(Domain.Entities.CyberJobManagement),
            new NotFoundException(nameof(Domain.Entities.CyberJobManagement), request.Id));

        eventToUpdate.Status = request.Status;
        await _cyberJobManagementRepository.UpdateAsync(eventToUpdate);

        await _publisher.Publish(
          new JobStatusUpdatedEvent { JobName = eventToUpdate.Name, Status = eventToUpdate.State },
          cancellationToken);

        var nodeConfig =
            await _loadBalancerRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString())
            ?? await _loadBalancerRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(),
                ServiceType.LoadBalancer.ToString())
            ?? await _loadBalancerRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(),
                ServiceType.CPNode.ToString());

        var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

        var url = UrlHelper.GenerateAirGapMonitorStatusUrl(nodeConfig.TypeCategory, baseUrl, eventToUpdate.ReferenceId);

        try
        {
            await _windowsService.GetAsync(url);
        }
        catch (Exception exc)
        {
            _logger.LogInformation($"Monitor Service URL call : {url} throws an exception - {exc.Message}.");
        }

        var response = new UpdateCyberJobManagementStatusResponse
        {
            Message = "Cyber resiliency job management status has been updated successfully",

            Id = eventToUpdate.ReferenceId
        };
      

        return response;
    }
}