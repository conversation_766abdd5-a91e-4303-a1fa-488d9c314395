﻿namespace ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Commands.Create;

public class CreateInfraObjectSchedulerWorkflowDetailCommandHandler : IRequestHandler<
    CreateInfraObjectSchedulerWorkflowDetailCommand, CreateInfraObjectSchedulerWorkflowDetailResponse>
{
    private readonly IInfraObjectSchedulerWorkflowDetailRepository _infraObjectSchedulerWorkflowDetailRepository;
    private readonly IMapper _mapper;

    public CreateInfraObjectSchedulerWorkflowDetailCommandHandler(IMapper mapper,
        IInfraObjectSchedulerWorkflowDetailRepository infraObjectSchedulerWorkflowDetailRepository)
    {
        _mapper = mapper;
        _infraObjectSchedulerWorkflowDetailRepository = infraObjectSchedulerWorkflowDetailRepository;
    }

    public async Task<CreateInfraObjectSchedulerWorkflowDetailResponse> Handle(
        CreateInfraObjectSchedulerWorkflowDetailCommand request, CancellationToken cancellationToken)
    {
        var infraObjectSchedulerWorkflow = _mapper.Map<Domain.Entities.InfraObjectSchedulerWorkflowDetail>(request);

        infraObjectSchedulerWorkflow =
            await _infraObjectSchedulerWorkflowDetailRepository.AddAsync(infraObjectSchedulerWorkflow);

        var response = new CreateInfraObjectSchedulerWorkflowDetailResponse
        {
            Message = Message.Create(nameof(Domain.Entities.InfraObjectSchedulerWorkflowDetail),
                infraObjectSchedulerWorkflow.InfraObjectName),
            Id = infraObjectSchedulerWorkflow.ReferenceId
        };

        return response;
    }
}