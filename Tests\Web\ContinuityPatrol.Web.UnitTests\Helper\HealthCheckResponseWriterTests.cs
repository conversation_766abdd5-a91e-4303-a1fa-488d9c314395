﻿using ContinuityPatrol.Web.Helper;

namespace ContinuityPatrol.Web.UnitTests.Helper;

public class HealthCheckResponseWriterTests
{
    private static async Task<string> InvokeWriterAndGetJsonAsync(HealthReport report)
    {
        var memoryStream = new MemoryStream();
        var context = new DefaultHttpContext();
        context.Response.Body = memoryStream;

        await HealthCheckResponseWriter.WriteJsonResponse(context, report);

        memoryStream.Seek(0, SeekOrigin.Begin);
        return await new StreamReader(memoryStream).ReadToEndAsync();
    }


    [Fact]
    public async Task WriteJsonResponse_ReturnsHealthyStatusAndDefaultDescription()
    {
        // Arrange
        var report = new HealthReport(
            new Dictionary<string, HealthReportEntry>
            {
                ["MSSQL Health Check"] = new(
                    HealthStatus.Healthy,
                    description: null,
                    duration: TimeSpan.FromMilliseconds(100),
                    exception: null,
                    data: new Dictionary<string, object>())
            },
            totalDuration: TimeSpan.FromMilliseconds(100)
        );

        // Act
        var json = await InvokeWriterAndGetJsonAsync(report);
        var doc = JsonDocument.Parse(json);

        // Assert
        Assert.Equal("Healthy", doc.RootElement.GetProperty("status").GetString());

        var entries = doc.RootElement.GetProperty("entries");
        var entry = entries.GetProperty("MSSQL Health Check");

        Assert.Equal("Healthy", entry.GetProperty("status").GetString());
        Assert.Equal("Checks SQL Server database connectivity", entry.GetProperty("description").GetString());
    }
    [Fact]
    public async Task WriteJsonResponse_ReturnsUnhealthyStatus_WithCustomDescription()
    {
        var report = new HealthReport(
            new Dictionary<string, HealthReportEntry>
            {
                ["Oracle Health Check"] = new(
                    HealthStatus.Unhealthy,
                    description: "Custom error",
                    duration: TimeSpan.FromMilliseconds(200),
                    exception: null,
                    data: new Dictionary<string, object>())
            },
            totalDuration: TimeSpan.FromMilliseconds(200)
        );

        var json = await InvokeWriterAndGetJsonAsync(report);
        var doc = JsonDocument.Parse(json);

        Assert.Equal("Unhealthy", doc.RootElement.GetProperty("status").GetString());

        var entries = doc.RootElement.GetProperty("entries");
        var entry = entries.GetProperty("Oracle Health Check");

        Assert.Equal("Unhealthy", entry.GetProperty("status").GetString());
        Assert.Equal("Custom error", entry.GetProperty("description").GetString());
    }

    [Fact]
    public async Task WriteJsonResponse_HandlesUnknownCheck_WithNullDescription()
    {
        var report = new HealthReport(
            new Dictionary<string, HealthReportEntry>
            {
                ["Unknown Service"] = new(
                    HealthStatus.Degraded,
                    description: null,
                    duration: TimeSpan.FromMilliseconds(300),
                    exception: null,
                    data: new Dictionary<string, object>())
            },
            totalDuration: TimeSpan.FromMilliseconds(300)
        );

        var json = await InvokeWriterAndGetJsonAsync(report);
        var doc = JsonDocument.Parse(json);

        Assert.Equal("Degraded", doc.RootElement.GetProperty("status").GetString());

        var entries = doc.RootElement.GetProperty("entries");
        var entry = entries.GetProperty("Unknown Service");

        Assert.Equal("Degraded", entry.GetProperty("status").GetString());
        Assert.Equal(JsonValueKind.Null, entry.GetProperty("description").ValueKind);
    }

    [Fact]
    public async Task WriteJsonResponse_ContainsTotalDuration()
    {
        var duration = TimeSpan.FromMilliseconds(1234);
        var report = new HealthReport(
            new Dictionary<string, HealthReportEntry>
            {
                ["Application"] = new(
                    HealthStatus.Healthy,
                    description: null,
                    duration: TimeSpan.FromMilliseconds(1234),
                    exception: null,
                    data: new Dictionary<string, object>())
            },
            totalDuration: duration
        );

        var json = await InvokeWriterAndGetJsonAsync(report);
        var doc = JsonDocument.Parse(json);

        var totalDuration = doc.RootElement.GetProperty("totalDuration").GetDouble();

        Assert.True(totalDuration >= 1234);
    }
    [Fact]
    public async Task WriteJsonResponse_ReturnsHealthyStatus_MySqlHealthCheck()
    {
        var report = new HealthReport(
            new Dictionary<string, HealthReportEntry>
            {
                ["MySql Health Check"] = new(
                    HealthStatus.Healthy,
                    description: null,
                    duration: TimeSpan.FromMilliseconds(50),
                    exception: null,
                    data: new Dictionary<string, object>())
            },
            totalDuration: TimeSpan.FromMilliseconds(50)
        );

        var json = await InvokeWriterAndGetJsonAsync(report);
        var doc = JsonDocument.Parse(json);

        Assert.Equal("Healthy", doc.RootElement.GetProperty("status").GetString());

        var entry = doc.RootElement.GetProperty("entries").GetProperty("MySql Health Check");
        Assert.Equal("Healthy", entry.GetProperty("status").GetString());
        Assert.Equal("Checks MySQL database connectivity", entry.GetProperty("description").GetString());
    }

    [Fact]
    public async Task WriteJsonResponse_ReturnsHealthyStatus_OracleHealthCheck()
    {
        var report = new HealthReport(
            new Dictionary<string, HealthReportEntry>
            {
                ["Oracle Health Check"] = new(
                    HealthStatus.Healthy,
                    description: null,
                    duration: TimeSpan.FromMilliseconds(70),
                    exception: null,
                    data: new Dictionary<string, object>())
            },
            totalDuration: TimeSpan.FromMilliseconds(70)
        );

        var json = await InvokeWriterAndGetJsonAsync(report);
        var doc = JsonDocument.Parse(json);

        var entry = doc.RootElement.GetProperty("entries").GetProperty("Oracle Health Check");
        Assert.Equal("Healthy", entry.GetProperty("status").GetString());
        Assert.Equal("Checks Oracle database connectivity", entry.GetProperty("description").GetString());
    }
    [Fact]
    public async Task WriteJsonResponse_ReturnsHealthyStatus_PostgresHealthCheck()
    {
        var report = new HealthReport(
            new Dictionary<string, HealthReportEntry>
            {
                ["Postgres Health Check"] = new(
                    HealthStatus.Healthy,
                    description: null,
                    duration: TimeSpan.FromMilliseconds(80),
                    exception: null,
                    data: new Dictionary<string, object>())
            },
            totalDuration: TimeSpan.FromMilliseconds(80)
        );

        var json = await InvokeWriterAndGetJsonAsync(report);
        var doc = JsonDocument.Parse(json);

        var entry = doc.RootElement.GetProperty("entries").GetProperty("Postgres Health Check");
        Assert.Equal("Healthy", entry.GetProperty("status").GetString());
        Assert.Equal("Checks PostgreSQL database connectivity", entry.GetProperty("description").GetString());
    }

    [Fact]
    public async Task WriteJsonResponse_ReturnsHealthyStatus_SeqHealthCheck()
    {
        var report = new HealthReport(
            new Dictionary<string, HealthReportEntry>
            {
                ["Seq Health Check"] = new(
                    HealthStatus.Healthy,
                    description: null,
                    duration: TimeSpan.FromMilliseconds(90),
                    exception: null,
                    data: new Dictionary<string, object>())
            },
            totalDuration: TimeSpan.FromMilliseconds(90)
        );

        var json = await InvokeWriterAndGetJsonAsync(report);
        var doc = JsonDocument.Parse(json);

        var entry = doc.RootElement.GetProperty("entries").GetProperty("Seq Health Check");
        Assert.Equal("Healthy", entry.GetProperty("status").GetString());
        Assert.Equal("The seq service is running.", entry.GetProperty("description").GetString());
    }

}