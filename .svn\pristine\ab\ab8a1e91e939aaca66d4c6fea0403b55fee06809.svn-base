using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class IncidentFixture : IDisposable
{
    public List<Incident> IncidentPaginationList { get; set; }
    public List<Incident> IncidentList { get; set; }
    public Incident IncidentDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public IncidentFixture()
    {
        var fixture = new Fixture();

        IncidentList = fixture.Create<List<Incident>>();

        IncidentPaginationList = fixture.CreateMany<Incident>(20).ToList();

        IncidentDto = fixture.Create<Incident>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
