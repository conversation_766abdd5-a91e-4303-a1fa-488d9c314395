using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class ArchiveFilterSpecification : Specification<Archive>
{
    public ArchiveFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.ArchiveProfileName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("companyid=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.CompanyId.Contains(stringItem.Replace("companyid=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("tablename=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.TableNameProperties.Contains(stringItem.Replace("tablename=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("cronexpression=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.CronExpression.Contains(stringItem.Replace("cronexpression=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("archiveProfileName=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ArchiveProfileName.Contains(stringItem.Replace("archiveProfileName=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("type=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Type.Contains(stringItem.Replace("type=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("scheduletime=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ScheduleTime.Contains(stringItem.Replace("scheduletime=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("backup=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.BackUpType.Contains(stringItem.Replace("backup=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.CompanyId.Contains(searchString) || p.TableNameProperties.Contains(searchString) ||
                    p.CronExpression.Contains(searchString) ||
                    p.ScheduleTime.Contains(searchString) || p.ArchiveProfileName.Contains(searchString) ||
                    p.Type.Contains(searchString) || p.BackUpType.Contains(searchString);
            }
        }
    }
}