using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Create;
using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Delete;
using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Update;
using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetList;
using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertReceiverModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class AlertReceiversControllerTests : IClassFixture<AlertReceiverFixture>
{
    private readonly AlertReceiverFixture _alertReceiverFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly AlertReceiversController _controller;

    public AlertReceiversControllerTests(AlertReceiverFixture alertReceiverFixture)
    {
        _alertReceiverFixture = alertReceiverFixture;

        var testBuilder = new ControllerTestBuilder<AlertReceiversController>();
        _controller = testBuilder.CreateController(
            _ => new AlertReceiversController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAlertReceivers_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertReceiverListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_alertReceiverFixture.AlertReceiverListVm);

        // Act
        var result = await _controller.GetAlertReceivers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertReceivers = Assert.IsAssignableFrom<List<AlertReceiverListVm>>(okResult.Value);
        Assert.Equal(3, alertReceivers.Count);
    }

    [Fact]
    public async Task GetAlertReceivers_ReturnsEmptyList_WhenNoAlertReceiversExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertReceiverListQuery>(), default))
            .ReturnsAsync(new List<AlertReceiverListVm>());

        // Act
        var result = await _controller.GetAlertReceivers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertReceivers = Assert.IsAssignableFrom<List<AlertReceiverListVm>>(okResult.Value);
        Assert.Empty(alertReceivers);
    }

    [Fact]
    public async Task GetAlertReceiverById_ReturnsAlertReceiver_WhenIdIsValid()
    {
        // Arrange
        var alertReceiverId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertReceiverDetailQuery>(q => q.Id == alertReceiverId), default))
            .ReturnsAsync(_alertReceiverFixture.AlertReceiverDetailVm);

        // Act
        var result = await _controller.GetAlertReceiverById(alertReceiverId);

        // Assert
        Assert.IsType<OkObjectResult>(result.Result);
    }

    [Fact]
    public async Task GetAlertReceiverById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetAlertReceiverById("invalid-guid"));
    }

    [Fact]
    public async Task CreateAlertReceiver_Returns201Created()
    {
        // Arrange
        var command = _alertReceiverFixture.CreateAlertReceiverCommand;
        var expectedMessage = $"AlertReceiver '{command.Name}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAlertReceiverResponse
            {
                Message = expectedMessage,
                AlertReceiverId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateAlertReceiver(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateAlertReceiverResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task CreateAlertReceiver_Throws_WhenNameExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateAlertReceiverCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("Name exists"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.CreateAlertReceiver(_alertReceiverFixture.CreateAlertReceiverCommand));
    }

    [Fact]
    public async Task UpdateAlertReceiver_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"AlertReceiver '{_alertReceiverFixture.UpdateAlertReceiverCommand.Name}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateAlertReceiverCommand>(), default))
            .ReturnsAsync(new UpdateAlertReceiverResponse
            {
                Message = expectedMessage,
                AlertReceiverId = _alertReceiverFixture.UpdateAlertReceiverCommand.Id
            });

        // Act
        var result = await _controller.UpdateAlertReceiver(_alertReceiverFixture.UpdateAlertReceiverCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateAlertReceiverResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAlertReceiver_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "AlertReceiver 'John Doe' has been deleted successfully!.";
        var alertReceiverId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAlertReceiverCommand>(c => c.Id == alertReceiverId), default))
            .ReturnsAsync(new DeleteAlertReceiverResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAlertReceiver(alertReceiverId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAlertReceiverResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAlertReceiver_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteAlertReceiver("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedAlertReceivers_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetAlertReceiverPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _alertReceiverFixture.AlertReceiverListVm;
        var expectedPaginatedResult = PaginatedResult<AlertReceiverListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertReceiverPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedAlertReceivers(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<AlertReceiverListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<AlertReceiverListVm>>(okResult.Value);

        Assert.NotNull(paginatedResult);
        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(3, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task IsAlertReceiverNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertReceiverNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsAlertReceiverNameExist("ExistingReceiver", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsAlertReceiverNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetAlertReceiverNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsAlertReceiverNameExist("NewReceiver", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsAlertReceiverNameExist_ThrowsInvalidArgumentException_WhenNameIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsAlertReceiverNameExist("", null));
    }

    [Fact]
    public async Task IsAlertReceiverNameExist_ThrowsArgumentNullException_WhenNameIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _controller.IsAlertReceiverNameExist(null!, null));
    }

    [Fact]
    public async Task GetAlertReceivers_CallsCorrectQuery()
    {
        // Arrange
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertReceiverListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<AlertReceiverListVm>());

        // Act
        await _controller.GetAlertReceivers();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public async Task CreateAlertReceiver_ClearsCacheAfterCreation()
    {
        // Arrange
        var command = _alertReceiverFixture.CreateAlertReceiverCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAlertReceiverResponse
            {
                Message = "Created successfully",
                AlertReceiverId = Guid.NewGuid().ToString()
            });

        // Act
        await _controller.CreateAlertReceiver(command);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public async Task UpdateAlertReceiver_ClearsCacheAfterUpdate()
    {
        // Arrange
        var command = _alertReceiverFixture.UpdateAlertReceiverCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateAlertReceiverResponse
            {
                Message = "Updated successfully",
                AlertReceiverId = command.Id
            });

        // Act
        await _controller.UpdateAlertReceiver(command);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public async Task DeleteAlertReceiver_ClearsCacheAfterDeletion()
    {
        // Arrange
        var alertReceiverId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAlertReceiverCommand>(c => c.Id == alertReceiverId), default))
            .ReturnsAsync(new DeleteAlertReceiverResponse
            {
                IsActive = false,
                Message = "Deleted successfully"
            });

        // Act
        await _controller.DeleteAlertReceiver(alertReceiverId);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateAlertReceiver_ValidatesEmailFormat()
    {
        // Arrange
        var command = new CreateAlertReceiverCommand
        {
            EmailAddress = "invalid-email", // Invalid email format
            MobileNumber = "******-9999",
            Name = "Test User",
            Properties = "{}",
            IsMail = true,
            IsActiveUser = true,
            IsSendReport = false
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("Invalid email format"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateAlertReceiver(command));
    }

    [Fact]
    public async Task UpdateAlertReceiver_ValidatesReceiverExists()
    {
        // Arrange
        var command = new UpdateAlertReceiverCommand
        {
            Id = Guid.NewGuid().ToString(),
            EmailAddress = "<EMAIL>",
            MobileNumber = "******-8888",
            Name = "Updated User",
            Properties = "{}",
            IsMail = true,
            IsActiveUser = true,
            IsSendReport = true
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("AlertReceiver not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateAlertReceiver(command));
    }

    [Fact]
    public async Task GetPaginatedAlertReceivers_HandlesFilteringByActiveStatus()
    {
        // Arrange
        var query = new GetAlertReceiverPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var activeReceiversOnly = new List<AlertReceiverListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                EmailAddress = "<EMAIL>",
                Name = "Active User 1",
                IsActiveUser = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                EmailAddress = "<EMAIL>",
                Name = "Active User 2",
                IsActiveUser = true
            }
        };

        var expectedPaginatedResult = PaginatedResult<AlertReceiverListVm>.Success(
            data: activeReceiversOnly,
            count: activeReceiversOnly.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertReceiverPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedAlertReceivers(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<List<AlertReceiverListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<AlertReceiverListVm>>(okResult.Value);

        Assert.NotNull(paginatedResult);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, receiver => Assert.True(receiver.IsActiveUser));
    }

  

    [Fact]
    public async Task CreateAlertReceiver_HandlesComplexProperties()
    {
        // Arrange
        var complexProperties = @"{
            ""department"": ""IT Security"",
            ""role"": ""Senior Administrator"",
            ""clearanceLevel"": ""Top Secret"",
            ""contactPreferences"": {
                ""email"": true,
                ""sms"": false,
                ""phone"": true
            },
            ""workSchedule"": {
                ""timezone"": ""UTC-5"",
                ""workHours"": ""09:00-17:00"",
                ""onCall"": true
            }
        }";

        var command = new CreateAlertReceiverCommand
        {
            EmailAddress = "<EMAIL>",
            MobileNumber = "******-0001",
            Name = "Security Administrator",
            Properties = complexProperties,
            IsMail = true,
            IsActiveUser = true,
            IsSendReport = true
        };

        var expectedMessage = $"AlertReceiver '{command.Name}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAlertReceiverResponse
            {
                Message = expectedMessage,
                AlertReceiverId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateAlertReceiver(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateAlertReceiverResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAlertReceiver_VerifiesReceiverIsDeactivated()
    {
        // Arrange
        var alertReceiverId = Guid.NewGuid().ToString();
        var expectedMessage = "AlertReceiver 'Test Receiver' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAlertReceiverCommand>(c => c.Id == alertReceiverId), default))
            .ReturnsAsync(new DeleteAlertReceiverResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAlertReceiver(alertReceiverId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAlertReceiverResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetAlertReceivers_HandlesLargeDataSets()
    {
        // Arrange
        var largeDataSet = new List<AlertReceiverListVm>();
        for (int i = 0; i < 1000; i++)
        {
            largeDataSet.Add(new AlertReceiverListVm
            {
                Id = Guid.NewGuid().ToString(),
                EmailAddress = $"user{i}@company.com",
                Name = $"User {i}",
                IsActiveUser = i % 2 == 0 // Alternate active/inactive
            });
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertReceiverListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(largeDataSet);

        // Act
        var result = await _controller.GetAlertReceivers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertReceivers = Assert.IsAssignableFrom<List<AlertReceiverListVm>>(okResult.Value);
        Assert.Equal(1000, alertReceivers.Count);
    }
}
