﻿namespace ContinuityPatrol.Application.Features.TableAccess.Commands.Update;

public class UpdateTableAccessCommandValidator : AbstractValidator<UpdateTableAccessCommand>
{
    private readonly ITableAccessRepository _tableRepository;

    public UpdateTableAccessCommandValidator(ITableAccessRepository tableAccessRepository)
    {
        _tableRepository = tableAccessRepository;

        RuleForEach(p => p.UpdateTableAccess)
            .SetValidator(new UpdateTableAccessValidator(_tableRepository));
    }

    public class UpdateTableAccessValidator : AbstractValidator<UpdateTableAccess>
    {
        private readonly ITableAccessRepository _tableAccessRepository;

        public UpdateTableAccessValidator(ITableAccessRepository tableAccessRepository)
        {
            _tableAccessRepository = tableAccessRepository;
            RuleFor(p => p.TableName)
                .NotEmpty().WithMessage("TableName is required.")
                .NotNull()
                .Matches(@"^([a-zA-Z]+[_\s-]?)([a-zA-Z\d]+[_\s-])*[a-zA-Z\d]+$")
                .WithMessage("Please enter a valid TableName.")
                .Length(3, 100).WithMessage("TableName should contain between 3 to 100 characters.");

            //RuleFor(p => p.SchemaName)
            //    .NotEmpty().WithMessage("{PropertyName} is Required.")
            //    .NotNull()
            //    .Matches(@"^[a-zA-Z\d]+([_\s\-\.][a-zA-Z\d]+)*$")
            //    .WithMessage("Please Enter Valid {PropertyName}.")
            //    .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

            RuleFor(y => y)
                .NotNull()
                .MustAsync(VerifyGuid)
                .WithMessage("Id is invalid");

            //RuleFor(e => e)
            //    .MustAsync(TableAccessNameUnique)
            //    .WithMessage("A same name already exists.");
        }

        private Task<bool> VerifyGuid(UpdateTableAccess p, CancellationToken cancellationToken)
        {
            Guard.Against.InvalidGuidOrEmpty(p.Id, "TableAccess Id");

            return Task.FromResult(true);
        }

        private async Task<bool> TableAccessNameUnique(UpdateTableAccess e, CancellationToken cancellationToken)
        {
            return !await _tableAccessRepository.IsTableAccessNameExist(e.TableName, e.Id);
        }
    }
}