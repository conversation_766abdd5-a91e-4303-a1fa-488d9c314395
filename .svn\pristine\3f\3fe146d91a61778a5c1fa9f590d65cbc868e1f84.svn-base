using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DashboardView.Commands.Create;
using ContinuityPatrol.Application.Features.DashboardView.Commands.Delete;
using ContinuityPatrol.Application.Features.DashboardView.Commands.Update;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetNames;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetBusinessViewPaginatedList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByLast7Days;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessFunctionId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDcMappingList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetServiceTopologyList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetDrillAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetSlaBreach;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetBusinessImpactAnalysis;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetSiteList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetVerifyWorkflowDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetDCMappingBySites;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetTotalSiteDetailForOneView;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetBreachDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetLastDrillDetails;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDatalagByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetComponentFailureAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalAvailabilityAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalHealthSummary;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetWorkflowAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.OneViewEntitiesEvent;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.OneViewRiskMitigationCyberSecurity;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.OneViewRiskMitigationFailedDrill;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetResilienceHealthStatus;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetSitePropertiesByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetImpactAvailabilityByBusinessServiceId;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DashboardViewControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DashboardViewController _controller;
    private readonly DashboardViewFixture _dashboardViewFixture;

    public DashboardViewControllerTests()
    {
        _dashboardViewFixture = new DashboardViewFixture();

        var testBuilder = new ControllerTestBuilder<DashboardViewController>();
        _controller = testBuilder.CreateController(
            _ => new DashboardViewController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetDashboardViews_ReturnsExpectedList()
    {
        // Arrange
        var expectedDashboardViews = new List<DashboardViewListVm>
        {
            _dashboardViewFixture.DashboardViewListVm,
            _dashboardViewFixture.DashboardViewListVm,
            _dashboardViewFixture.DashboardViewListVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDashboardViewListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedDashboardViews);

        // Act
        var result = await _controller.GetDashboardViews();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var dashboardViews = Assert.IsAssignableFrom<List<DashboardViewListVm>>(okResult.Value);
        Assert.Equal(3, dashboardViews.Count);
    }

    [Fact]
    public async Task GetDashboardViewById_ReturnsExpectedDetail()
    {
        // Arrange
        var dashboardViewId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewDetailQuery>(q => q.Id == dashboardViewId), default))
            .ReturnsAsync(_dashboardViewFixture.DashboardViewDetailVm);

        // Act
        var result = await _controller.GetDashboardViewById(dashboardViewId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var dashboardView = Assert.IsType<DashboardViewDetailVm>(okResult.Value);
        Assert.Equal(_dashboardViewFixture.DashboardViewDetailVm.BusinessServiceName, dashboardView.BusinessServiceName);
    }

    [Fact]
    public async Task GetBusinessViews_ReturnsExpectedBusinessViews()
    {
        // Arrange
        var expectedBusinessViews = new List<BusinessViewPaginatedList>
        {
            _dashboardViewFixture.BusinessViewPaginatedList,
            _dashboardViewFixture.BusinessViewPaginatedList
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessViewPaginatedListQuery>(), default))
            .ReturnsAsync(expectedBusinessViews);

        // Act
        var result = await _controller.GetBusinessViews();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var businessViews = Assert.IsAssignableFrom<List<BusinessViewPaginatedList>>(okResult.Value);
        Assert.Equal(2, businessViews.Count);
    }

    [Fact]
    public async Task GetDashboardNames_ReturnsExpectedNames()
    {
        // Arrange
        var expectedNames = new List<DashboardViewNameVm>
        {
            _dashboardViewFixture.DashboardViewNameVm,
            _dashboardViewFixture.DashboardViewNameVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDashboardNameQuery>(), default))
            .ReturnsAsync(expectedNames);

        // Act
        var result = await _controller.GetDashboardNames();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var dashboardNames = Assert.IsAssignableFrom<List<DashboardViewNameVm>>(okResult.Value);
        Assert.Equal(2, dashboardNames.Count);
    }

    [Fact]
    public async Task CreateDashboardView_ReturnsCreatedAtAction()
    {
        // Arrange
        var command = _dashboardViewFixture.CreateDashboardViewCommand;
        var expectedMessage = "DashboardView has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateDashboardViewResponse
            {
                Message = expectedMessage,
                BusinessViewId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateDashboardView(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateDashboardViewResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateDashboardView_ReturnsOk()
    {
        // Arrange
        var command = _dashboardViewFixture.UpdateDashboardViewCommand;
        var expectedMessage = "DashboardView has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateDashboardViewResponse
            {
                Message = expectedMessage,
                BusinessViewId = command.Id
            });

        // Act
        var result = await _controller.UpdateDashboardView(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateDashboardViewResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteDashboardView_ReturnsOk()
    {
        // Arrange
        var dashboardViewId = Guid.NewGuid().ToString();
        var expectedMessage = "DashboardView has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDashboardViewCommand>(c => c.BusinessViewId == dashboardViewId), default))
            .ReturnsAsync(new DeleteDashboardViewResponse
            {
                Message = expectedMessage,
                IsActive = false
            });

        // Act
        var result = await _controller.DeleteDashboardView(dashboardViewId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteDashboardViewResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public async Task GetDashboardViewListByBusinessServiceId_ReturnsExpectedViews()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var expectedViews = new List<DashboardViewByBusinessServiceIdVm>
        {
            _dashboardViewFixture.DashboardViewByBusinessServiceIdVm,
            _dashboardViewFixture.DashboardViewByBusinessServiceIdVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(expectedViews);

        // Act
        var result = await _controller.GetDashboardViewListByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var views = Assert.IsAssignableFrom<List<DashboardViewByBusinessServiceIdVm>>(okResult.Value);
        Assert.Equal(2, views.Count);
    }

    [Fact]
    public async Task GetDashboardViewListByInfraObjectId_ReturnsExpectedView()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), default))
            .ReturnsAsync(_dashboardViewFixture.GetDashboardViewByInfraObjectIdVm);

        // Act
        var result = await _controller.GetDashboardViewListByInfraObjectId(infraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var view = Assert.IsType<GetDashboardViewByInfraObjectIdVm>(okResult.Value);
        Assert.Equal(_dashboardViewFixture.GetDashboardViewByInfraObjectIdVm.InfraObjectName, view.InfraObjectName);
    }

    [Fact]
    public async Task GetDatalagStatusByLast7DaysList_ReturnsExpectedStatus()
    {
        // Arrange
        var expectedStatus = new List<DataLagStatusbyLast7DaysVm>
        {
            _dashboardViewFixture.DataLagStatusbyLast7DaysVm,
            _dashboardViewFixture.DataLagStatusbyLast7DaysVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataLagStatusbyLast7DaysQuery>(), default))
            .ReturnsAsync(expectedStatus);

        // Act
        var result = await _controller.GetDatalagStatusByLast7DaysList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var statusList = Assert.IsAssignableFrom<List<DataLagStatusbyLast7DaysVm>>(okResult.Value);
        Assert.Equal(2, statusList.Count);
    }

    [Fact]
    public async Task GetDashboardViews_HandlesEmptyList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDashboardViewListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<DashboardViewListVm>());

        // Act
        var result = await _controller.GetDashboardViews();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var dashboardViews = Assert.IsAssignableFrom<List<DashboardViewListVm>>(okResult.Value);
        Assert.Empty(dashboardViews);
    }

    [Fact]
    public async Task GetDashboardViewById_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDashboardViewById(invalidId));
    }

    [Fact]
    public async Task GetDashboardViewById_HandlesNotFound()
    {
        // Arrange
        var dashboardViewId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewDetailQuery>(q => q.Id == dashboardViewId), default))
            .ThrowsAsync(new NotFoundException("DashboardView", dashboardViewId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetDashboardViewById(dashboardViewId));
    }

    [Fact]
    public async Task DeleteDashboardView_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDashboardView(invalidId));
    }

    [Fact]
    public void ClearDataCache_ClearsExpectedCacheKeys()
    {
        // Arrange & Act
        _controller.ClearDataCache();

        // Assert
        // Note: Since ClearDataCache is a void method that calls protected methods,
        // we verify it doesn't throw exceptions and completes successfully
        Assert.True(true); // Test passes if no exception is thrown
    }

    [Fact]
    public async Task CreateDashboardView_HandlesEnterpriseMissionCriticalView()
    {
        // Arrange
        var command = new CreateDashboardViewCommand
        {
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Enterprise Mission-Critical Financial Services",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "Enterprise Core Banking Operations",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "Enterprise Primary Database Cluster",
            Priority = 1,
            CompanyId = Guid.NewGuid().ToString(),
            Description = "Mission-critical enterprise dashboard view for monitoring core banking operations with real-time transaction processing, fraud detection, and regulatory compliance tracking",
            EntityId = Guid.NewGuid().ToString(),
            MonitorType = "Real-time Transaction Monitoring",
            Type = 1,
            DataLagValue = "00:00:30",
            Status = "Active",
            Properties = "{\"enterpriseDashboard\":{\"monitoring\":{\"type\":\"mission_critical\",\"frequency\":\"real_time\",\"sla\":\"99.99%\"},\"compliance\":{\"standards\":[\"PCI-DSS\",\"SOX\",\"Basel III\"],\"reporting\":\"automated\"},\"security\":{\"encryption\":\"AES-256\",\"authentication\":\"multi_factor\",\"audit_trail\":\"comprehensive\"},\"performance\":{\"thresholds\":{\"response_time\":\"<100ms\",\"throughput\":\">10000_tps\",\"availability\":\"99.99%\"}},\"disaster_recovery\":{\"rpo\":\"30_seconds\",\"rto\":\"5_minutes\",\"testing\":\"monthly\"}}}",
            ReplicationStatus = 1,
            DROperationStatus = 1,
            ConfiguredRPO = "00:00:30",
            ConfiguredRTO = "00:05:00",
            RPOThreshold = "00:00:15",
            SiteProperties = "{\"site\":\"primary\",\"location\":\"datacenter1\"}",
            CurrentRPO = "00:00:10",
            CurrentRTO = "00:03:30",
            RPOGeneratedDate = DateTime.Now.AddMinutes(-2).ToString("yyyy-MM-dd HH:mm:ss"),
            RTOGeneratedDate = DateTime.Now.AddMinutes(-2).ToString("yyyy-MM-dd HH:mm:ss"),
            State = "Mission-Critical",
            ErrorMessage = null,
            EstimatedRTO = "00:04:30",
            IsDRReady = true
        };

        var expectedMessage = "Enterprise Mission-Critical DashboardView created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateDashboardViewResponse
            {
                Message = expectedMessage,
                BusinessViewId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateDashboardView(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateDashboardViewResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Enterprise Mission-Critical", response.Message);
    }

    [Fact]
    public async Task UpdateDashboardView_HandlesPerformanceOptimization()
    {
        // Arrange
        var command = new UpdateDashboardViewCommand
        {
            Id = Guid.NewGuid().ToString(),
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Performance-Optimized Enterprise Service",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "High-Performance Computing Function",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "Optimized Enterprise Infrastructure",
            Priority = 1,
            CompanyId = Guid.NewGuid().ToString(),
            Description = "Performance-optimized enterprise dashboard view with advanced analytics, predictive monitoring, and automated scaling capabilities",
            EntityId = Guid.NewGuid().ToString(),
            MonitorType = "AI-Powered Performance Monitoring",
            Type = 1,
            DataLagValue = "00:00:15",
            Status = "Active",
            Properties = "{\"performanceOptimization\":{\"baseline\":{\"response_time\":\"50ms\",\"throughput\":\"15000_tps\",\"cpu_utilization\":\"60%\",\"memory_usage\":\"70%\"},\"optimizations\":[{\"type\":\"caching\",\"implementation\":\"distributed_redis\",\"improvement\":\"60% response time reduction\"},{\"type\":\"load_balancing\",\"implementation\":\"intelligent_routing\",\"improvement\":\"80% throughput increase\"},{\"type\":\"auto_scaling\",\"implementation\":\"predictive_scaling\",\"improvement\":\"40% resource efficiency\"}],\"ai_analytics\":{\"anomaly_detection\":\"enabled\",\"predictive_alerts\":\"configured\",\"performance_forecasting\":\"active\"},\"results\":{\"response_time\":\"20ms\",\"throughput\":\"27000_tps\",\"cpu_utilization\":\"45%\",\"memory_usage\":\"55%\"}}}",
            ReplicationStatus = 1,
            DROperationStatus = 1,
            ConfiguredRPO = "00:00:15",
            ConfiguredRTO = "00:02:30",
            RPOThreshold = "00:00:10",
            SiteProperties = "{\"site\":\"optimized\",\"location\":\"datacenter2\"}",
            RPOGeneratedDate = DateTime.Now.AddMinutes(-1).ToString("yyyy-MM-dd HH:mm:ss"),
            RTOGeneratedDate = DateTime.Now.AddMinutes(-1).ToString("yyyy-MM-dd HH:mm:ss"),
            CurrentRPO = "00:00:05",
            CurrentRTO = "00:01:45",
            State = "Optimized",
            ErrorMessage = null,
            EstimatedRTO = "00:01:30",
            IsDRReady = true
        };

        var expectedMessage = "Performance optimization completed successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateDashboardViewResponse
            {
                Message = expectedMessage,
                BusinessViewId = command.Id
            });

        // Act
        var result = await _controller.UpdateDashboardView(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateDashboardViewResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Performance optimization", response.Message);
    }

    [Fact]
    public async Task GetBusinessViews_HandlesEnterpriseMultiTierArchitecture()
    {
        // Arrange
        var enterpriseBusinessViews = new List<BusinessViewPaginatedList>
        {
            new BusinessViewPaginatedList
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Tier-1 Critical Service",
                Priority = 1,
                Status = "Active",
                IsDRReady = true,
                ConfiguredRPO = "00:00:30",
                ConfiguredRTO = "00:05:00",
                RPOThreshold = "00:00:15",
                CurrentRPO = "00:00:10",
                CurrentRTO = "00:03:30",
                Percentage = "99.95",
                IsDataLagExceed = false,
                IsPartial = false,
                WorkflowIsRunning = false
            },
            new BusinessViewPaginatedList
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Tier-2 Business Service",
                Priority = 2,
                Status = "Active",
                IsDRReady = true,
                ConfiguredRPO = "00:02:00",
                ConfiguredRTO = "00:15:00",
                RPOThreshold = "00:01:00",
                CurrentRPO = "00:00:45",
                CurrentRTO = "00:12:00",
                Percentage = "98.5",
                IsDataLagExceed = false,
                IsPartial = false,
                WorkflowIsRunning = true
            },
            new BusinessViewPaginatedList
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Tier-3 Support Service",
                Priority = 3,
                Status = "Active",
                IsDRReady = false,
                ConfiguredRPO = "00:15:00",
                ConfiguredRTO = "01:00:00",
                RPOThreshold = "00:10:00",
                CurrentRPO = "00:08:00",
                CurrentRTO = "00:45:00",
                Percentage = "95.0",
                IsDataLagExceed = false,
                IsPartial = true,
                WorkflowIsRunning = false
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessViewPaginatedListQuery>(), default))
            .ReturnsAsync(enterpriseBusinessViews);

        // Act
        var result = await _controller.GetBusinessViews();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var businessViews = Assert.IsAssignableFrom<List<BusinessViewPaginatedList>>(okResult.Value);
        Assert.Equal(3, businessViews.Count);

        var tier1Service = businessViews.First(bv => bv.Priority == 1);
        Assert.Contains("Tier-1", tier1Service.BusinessServiceName);
        Assert.True(tier1Service.IsDRReady);
        Assert.Equal("99.95", tier1Service.Percentage);

        var tier2Service = businessViews.First(bv => bv.Priority == 2);
        Assert.Contains("Tier-2", tier2Service.BusinessServiceName);
        Assert.True(tier2Service.WorkflowIsRunning);

        var tier3Service = businessViews.First(bv => bv.Priority == 3);
        Assert.Contains("Tier-3", tier3Service.BusinessServiceName);
        Assert.False(tier3Service.IsDRReady);
        Assert.True(tier3Service.IsPartial);
    }

    [Fact]
    public async Task GetDashboardViewById_HandlesComplexEnterpriseConfiguration()
    {
        // Arrange
        var dashboardViewId = Guid.NewGuid().ToString();
        var complexDashboardView = new DashboardViewDetailVm
        {
            Id = dashboardViewId,
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Enterprise Complex Multi-Cloud Business Service",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "Enterprise Hybrid Cloud Operations",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "Enterprise Multi-Cloud Infrastructure Cluster",
            Priority = 1,
            CompanyId = Guid.NewGuid().ToString(),
            Description = "Comprehensive enterprise dashboard view managing complex multi-cloud infrastructure with advanced monitoring, compliance tracking, and automated disaster recovery across AWS, Azure, and Google Cloud platforms",
            EntityId = Guid.NewGuid().ToString(),
            MonitorType = "Multi-Cloud Advanced Monitoring",
            Type = 1,
            DataLagValue = "00:00:45",
            Status = "Active",
            Properties = "{\"complexEnterprise\":{\"multiCloud\":{\"providers\":[{\"name\":\"AWS\",\"region\":\"us-east-1\",\"services\":[\"EC2\",\"RDS\",\"S3\"],\"status\":\"active\"},{\"name\":\"Azure\",\"region\":\"East US\",\"services\":[\"VMs\",\"SQL Database\",\"Blob Storage\"],\"status\":\"active\"},{\"name\":\"Google Cloud\",\"region\":\"us-central1\",\"services\":[\"Compute Engine\",\"Cloud SQL\",\"Cloud Storage\"],\"status\":\"standby\"}],\"orchestration\":{\"tool\":\"Terraform Enterprise\",\"automation\":\"full\",\"compliance\":\"continuous\"},\"monitoring\":{\"unified_dashboard\":\"enabled\",\"cross_cloud_analytics\":\"active\",\"cost_optimization\":\"automated\"},\"disaster_recovery\":{\"strategy\":\"active_active\",\"failover\":\"automatic\",\"testing\":\"weekly\"}},\"compliance\":{\"frameworks\":[\"SOC2\",\"ISO27001\",\"GDPR\",\"HIPAA\"],\"auditing\":\"real_time\",\"reporting\":\"automated\"},\"security\":{\"zero_trust\":\"implemented\",\"encryption\":\"end_to_end\",\"threat_detection\":\"ai_powered\"}}}",
            ReplicationStatus = 1,
            DROperationStatus = 1,
            ConfiguredRPO = "00:00:45",
            ConfiguredRTO = "00:03:00",
            RPOThreshold = "00:00:30",
            SiteProperties = "{\"site\":\"multi_cloud\",\"providers\":[\"AWS\",\"Azure\",\"GCP\"]}",
            CurrentRPO = "00:00:20",
            CurrentRTO = "00:02:15",
            State = "Multi-Cloud Active",
            ErrorMessage = null,
            IsDRReady = true
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewDetailQuery>(q => q.Id == dashboardViewId), default))
            .ReturnsAsync(complexDashboardView);

        // Act
        var result = await _controller.GetDashboardViewById(dashboardViewId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var dashboardView = Assert.IsType<DashboardViewDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Complex Multi-Cloud Business Service", dashboardView.BusinessServiceName);
        Assert.Contains("complexEnterprise", dashboardView.Properties);
        Assert.Contains("multiCloud", dashboardView.Properties);
        Assert.Contains("AWS", dashboardView.Properties);
        Assert.Contains("Azure", dashboardView.Properties);
        Assert.Contains("Google Cloud", dashboardView.Properties);
        Assert.Equal("Multi-Cloud Active", dashboardView.State);
    }

    [Fact]
    public async Task GetDashboardViewListByBusinessServiceId_HandlesMultipleInfrastructureObjects()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var multipleInfraViews = new List<DashboardViewByBusinessServiceIdVm>
        {
            new DashboardViewByBusinessServiceIdVm
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessServiceName = "Enterprise Database Service",
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = "Primary Database Function",
                InfraObjectId = Guid.NewGuid().ToString(),
                InfraObjectName = "Primary Database Cluster",
                Priority = 1,
                CompanyId = Guid.NewGuid().ToString(),
                Description = "Primary database cluster monitoring",
                EntityId = Guid.NewGuid().ToString(),
                MonitorType = "Database Monitoring",
                Type = 1,
                DataLagValue = "00:01:30",
                Status = "Active",
                Properties = "{\"database\":{\"type\":\"primary\"}}",
                ReplicationStatus = 1,
                DROperationStatus = 1,
                ConfiguredRPO = "00:05:00",
                ConfiguredRTO = "00:15:00",
                RPOThreshold = "00:03:00",
                SiteProperties = "{\"site\":\"primary\"}",
                CurrentRPO = "00:01:30",
                CurrentRTO = "00:12:00",
                State = "Active",
                ErrorMessage = null,
                IsDRReady = true
            },
            new DashboardViewByBusinessServiceIdVm
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessServiceName = "Enterprise Database Service",
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = "Secondary Database Function",
                InfraObjectId = Guid.NewGuid().ToString(),
                InfraObjectName = "Secondary Database Cluster",
                Priority = 2,
                CompanyId = Guid.NewGuid().ToString(),
                Description = "Secondary database cluster monitoring",
                EntityId = Guid.NewGuid().ToString(),
                MonitorType = "Database Monitoring",
                Type = 1,
                DataLagValue = "00:02:00",
                Status = "Standby",
                Properties = "{\"database\":{\"type\":\"secondary\"}}",
                ReplicationStatus = 1,
                DROperationStatus = 2,
                ConfiguredRPO = "00:05:00",
                ConfiguredRTO = "00:15:00",
                RPOThreshold = "00:03:00",
                SiteProperties = "{\"site\":\"secondary\"}",
                CurrentRPO = "00:02:00",
                CurrentRTO = "00:13:00",
                State = "Standby",
                ErrorMessage = null,
                IsDRReady = true
            },
            new DashboardViewByBusinessServiceIdVm
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessServiceName = "Enterprise Database Service",
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = "Archive Database Function",
                InfraObjectId = Guid.NewGuid().ToString(),
                InfraObjectName = "Archive Database Storage",
                Priority = 3,
                CompanyId = Guid.NewGuid().ToString(),
                Description = "Archive database storage monitoring",
                EntityId = Guid.NewGuid().ToString(),
                MonitorType = "Storage Monitoring",
                Type = 1,
                DataLagValue = "00:15:00",
                Status = "Active",
                Properties = "{\"database\":{\"type\":\"archive\"}}",
                ReplicationStatus = 1,
                DROperationStatus = 1,
                ConfiguredRPO = "01:00:00",
                ConfiguredRTO = "04:00:00",
                RPOThreshold = "00:30:00",
                SiteProperties = "{\"site\":\"archive\"}",
                CurrentRPO = "00:15:00",
                CurrentRTO = "03:30:00",
                State = "Active",
                ErrorMessage = null,
                IsDRReady = false
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(multipleInfraViews);

        // Act
        var result = await _controller.GetDashboardViewListByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var views = Assert.IsAssignableFrom<List<DashboardViewByBusinessServiceIdVm>>(okResult.Value);
        Assert.Equal(3, views.Count);
        Assert.All(views, v => Assert.Equal(businessServiceId, v.BusinessServiceId));
        Assert.Contains(views, v => v.InfraObjectName == "Primary Database Cluster");
        Assert.Contains(views, v => v.InfraObjectName == "Secondary Database Cluster");
        Assert.Contains(views, v => v.InfraObjectName == "Archive Database Storage");
    }

    [Fact]
    public async Task GetDatalagStatusByLast7DaysList_HandlesWeeklyTrendAnalysis()
    {
        // Arrange
        var weeklyTrendData = new List<DataLagStatusbyLast7DaysVm>();
        for (int i = 0; i < 7; i++)
        {
            weeklyTrendData.Add(new DataLagStatusbyLast7DaysVm
            {
                Id = Guid.NewGuid().ToString(),
                InfraObjectId = Guid.NewGuid().ToString(),
                InfraObjectName = $"Enterprise Infrastructure Day {i + 1}",
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Trend Analysis Service",
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = $"Enterprise Function Day {i + 1}",
                CompanyId = Guid.NewGuid().ToString(),
                EntityId = Guid.NewGuid().ToString(),
                MonitorType = "7-Day Trend Monitoring",
                Type = 1,
                DataLagValue = $"00:0{i + 2}:00",
                Status = i < 5 ? "Active" : "Maintenance",
                Properties = "{\"trend\":{\"day\":" + (i + 1) + "}}",
                ReplicationStatus = 1,
                DROperationStatus = 1,
                CurrentRTO = $"00:{(i + 10):00}:00"
            });
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataLagStatusbyLast7DaysQuery>(), default))
            .ReturnsAsync(weeklyTrendData);

        // Act
        var result = await _controller.GetDatalagStatusByLast7DaysList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var statusList = Assert.IsAssignableFrom<List<DataLagStatusbyLast7DaysVm>>(okResult.Value);
        Assert.Equal(7, statusList.Count);
        Assert.Equal(5, statusList.Count(s => s.Status == "Active"));
        Assert.Equal(2, statusList.Count(s => s.Status == "Maintenance"));
        Assert.All(statusList, s => Assert.Contains("Enterprise", s.BusinessServiceName));
    }

    [Fact]
    public async Task CreateDashboardView_ValidatesRequiredFields()
    {
        // Arrange
        var command = new CreateDashboardViewCommand
        {
            BusinessServiceId = "", // Empty required field
            BusinessServiceName = "Test Service",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "Test Function"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidArgumentException("BusinessServiceId is required"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.CreateDashboardView(command));
    }

    [Fact]
    public async Task UpdateDashboardView_HandlesDisasterRecoveryConfiguration()
    {
        // Arrange
        var command = new UpdateDashboardViewCommand
        {
            Id = Guid.NewGuid().ToString(),
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Enterprise Disaster Recovery Service",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "Enterprise DR Operations",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "Enterprise DR Infrastructure",
            Priority = 1,
            CompanyId = Guid.NewGuid().ToString(),
            Description = "Enterprise disaster recovery dashboard view with automated failover, continuous replication, and comprehensive testing procedures",
            EntityId = Guid.NewGuid().ToString(),
            MonitorType = "Disaster Recovery Monitoring",
            Type = 1,
            DataLagValue = "00:00:05",
            Status = "Active",
            Properties = "{\"disasterRecovery\":{\"strategy\":\"active_passive\",\"primary_site\":{\"location\":\"New York\",\"capacity\":\"100%\",\"status\":\"active\"},\"dr_site\":{\"location\":\"Chicago\",\"capacity\":\"100%\",\"status\":\"standby\"},\"replication\":{\"method\":\"synchronous\",\"frequency\":\"real_time\",\"lag\":\"<5_seconds\"},\"testing\":{\"frequency\":\"monthly\",\"type\":\"full_failover\",\"last_test\":\"2024-01-15\",\"success_rate\":\"100%\"},\"automation\":{\"failover\":\"automatic\",\"failback\":\"manual_approval\",\"monitoring\":\"24x7\"}}}",
            ReplicationStatus = 1,
            DROperationStatus = 1,
            ConfiguredRPO = "00:00:05",
            ConfiguredRTO = "00:01:00",
            RPOThreshold = "00:00:03",
            SiteProperties = "{\"site\":\"dr_primary\",\"dr_site\":\"dr_secondary\"}",
            RPOGeneratedDate = DateTime.Now.AddMinutes(-1).ToString("yyyy-MM-dd HH:mm:ss"),
            RTOGeneratedDate = DateTime.Now.AddMinutes(-1).ToString("yyyy-MM-dd HH:mm:ss"),
            CurrentRPO = "00:00:02",
            CurrentRTO = "00:00:45",
            State = "DR Ready",
            ErrorMessage = null,
            EstimatedRTO = "00:00:40",
            IsDRReady = true
        };

        var expectedMessage = "Disaster Recovery configuration updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateDashboardViewResponse
            {
                Message = expectedMessage,
                BusinessViewId = command.Id
            });

        // Act
        var result = await _controller.UpdateDashboardView(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateDashboardViewResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Disaster Recovery", response.Message);
    }

    [Fact]
    public async Task GetDashboardNames_HandlesLargeEnterpriseDeployment()
    {
        // Arrange
        var largeDashboardNames = new List<DashboardViewNameVm>();
        var businessServices = new[] { "Financial", "Trading", "Risk Management", "Compliance", "Operations", "Technology", "Human Resources", "Legal" };
        var functions = new[] { "Core Processing", "Analytics", "Reporting", "Monitoring", "Security", "Backup", "Archive", "Integration" };

        foreach (var service in businessServices)
        {
            foreach (var function in functions)
            {
                largeDashboardNames.Add(new DashboardViewNameVm
                {
                    InfraObjectId = Guid.NewGuid().ToString(),
                    InfraObjectName = $"Enterprise {service} Infrastructure",
                    EntityId = Guid.NewGuid().ToString(),
                    MonitorType = $"Enterprise {function} Monitoring"
                });
            }
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDashboardNameQuery>(), default))
            .ReturnsAsync(largeDashboardNames);

        // Act
        var result = await _controller.GetDashboardNames();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var dashboardNames = Assert.IsAssignableFrom<List<DashboardViewNameVm>>(okResult.Value);
        Assert.Equal(64, dashboardNames.Count); // 8 services × 8 functions
        Assert.Contains(dashboardNames, dn => dn.InfraObjectName == "Enterprise Financial Infrastructure");
        Assert.Contains(dashboardNames, dn => dn.MonitorType == "Enterprise Core Processing Monitoring");
        Assert.All(dashboardNames, dn => Assert.Contains("Enterprise", dn.InfraObjectName));
    }

    [Fact]
    public async Task DeleteDashboardView_HandlesViewInUse()
    {
        // Arrange
        var dashboardViewId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDashboardViewCommand>(c => c.BusinessViewId == dashboardViewId), default))
            .ThrowsAsync(new EntityAssociatedException("The dashboard view is currently in use by active monitoring workflows."));

        // Act & Assert
        await Assert.ThrowsAsync<EntityAssociatedException>(() => _controller.DeleteDashboardView(dashboardViewId));
    }

    [Fact]
    public async Task GetDashboardViewListByInfraObjectId_HandlesEmptyResult()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), default))
            .ReturnsAsync((GetDashboardViewByInfraObjectIdVm)null);

        // Act
        var result = await _controller.GetDashboardViewListByInfraObjectId(infraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Null(okResult.Value);
    }

    [Fact]
    public async Task GetDashboardViews_HandlesLargeDataset()
    {
        // Arrange
        var largeDashboardViews = new List<DashboardViewListVm>();
        for (int i = 0; i < 1000; i++)
        {
            largeDashboardViews.Add(new DashboardViewListVm
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = $"Enterprise Service {i + 1:D4}",
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = $"Enterprise Function {i + 1:D4}",
                InfraObjectId = Guid.NewGuid().ToString(),
                InfraObjectName = $"Enterprise Infrastructure {i + 1:D4}",
                Priority = (i % 3) + 1,
                CompanyId = Guid.NewGuid().ToString(),
                Description = $"Enterprise dashboard view {i + 1} for large-scale monitoring",
                EntityId = Guid.NewGuid().ToString(),
                MonitorType = i % 2 == 0 ? "Real-time Monitoring" : "Batch Monitoring",
                Type = 1,
                DataLagValue = $"00:{(i % 60):D2}:00",
                Status = i % 10 == 0 ? "Maintenance" : "Active",
                Properties = $"{{\"large_dataset\":{{\"index\":{i},\"batch\":\"enterprise\"}}}}",
                ReplicationStatus = 1,
                DROperationStatus = 1,
                ConfiguredRPO = "00:15:00",
                ConfiguredRTO = "01:00:00",
                RPOThreshold = "00:10:00",
                CurrentRPO = $"00:{(i % 10):D2}:00",
                CurrentRTO = $"00:{(i % 45 + 15):D2}:00",
                State = i % 5 == 0 ? "Optimizing" : "Active",
                IsDRReady = i % 3 != 0
            });
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDashboardViewListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(largeDashboardViews);

        // Act
        var result = await _controller.GetDashboardViews();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var dashboardViews = Assert.IsAssignableFrom<List<DashboardViewListVm>>(okResult.Value);
        Assert.Equal(1000, dashboardViews.Count);
        Assert.Contains(dashboardViews, dv => dv.BusinessServiceName.Contains("Enterprise Service"));
        Assert.Contains(dashboardViews, dv => dv.Status == "Maintenance");
        Assert.Contains(dashboardViews, dv => dv.State == "Optimizing");
    }

    [Fact]
    public async Task CreateDashboardView_HandlesHighAvailabilityConfiguration()
    {
        // Arrange
        var command = new CreateDashboardViewCommand
        {
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Enterprise High-Availability Financial Trading Service",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "Enterprise Real-Time Trading Operations",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "Enterprise HA Trading Infrastructure Cluster",
            Priority = 1,
            CompanyId = Guid.NewGuid().ToString(),
            Description = "High-availability enterprise dashboard view for real-time financial trading operations with sub-second latency requirements, 99.999% uptime SLA, and comprehensive regulatory compliance monitoring",
            EntityId = Guid.NewGuid().ToString(),
            MonitorType = "Ultra-Low Latency Trading Monitoring",
            Type = 1,
            DataLagValue = "00:00:01",
            Status = "Active",
            Properties = "{\"highAvailability\":{\"architecture\":\"active_active_active\",\"nodes\":[{\"location\":\"NYC\",\"status\":\"primary\",\"latency\":\"<1ms\"},{\"location\":\"London\",\"status\":\"active\",\"latency\":\"<2ms\"},{\"location\":\"Tokyo\",\"status\":\"active\",\"latency\":\"<3ms\"}],\"failover\":{\"automatic\":true,\"detection_time\":\"<100ms\",\"recovery_time\":\"<500ms\"},\"monitoring\":{\"frequency\":\"real_time\",\"metrics\":[\"latency\",\"throughput\",\"error_rate\",\"availability\"],\"thresholds\":{\"latency\":\"1ms\",\"throughput\":\"1M_tps\",\"error_rate\":\"0.001%\",\"availability\":\"99.999%\"}},\"compliance\":{\"regulations\":[\"MiFID_II\",\"Dodd_Frank\",\"EMIR\",\"Basel_III\"],\"reporting\":\"real_time\",\"audit_trail\":\"comprehensive\"},\"security\":{\"encryption\":\"AES_256_GCM\",\"authentication\":\"hardware_tokens\",\"network\":\"dedicated_lines\"}}}",
            ReplicationStatus = 1,
            DROperationStatus = 1,
            ConfiguredRPO = "00:00:01",
            ConfiguredRTO = "00:00:30",
            RPOThreshold = "00:00:00.5",
            SiteProperties = "{\"site\":\"multi_region\",\"regions\":[\"us_east\",\"eu_west\",\"asia_pacific\"]}",
            CurrentRPO = "00:00:00.2",
            CurrentRTO = "00:00:15",
            RPOGeneratedDate = DateTime.Now.AddSeconds(-30).ToString("yyyy-MM-dd HH:mm:ss.fff"),
            RTOGeneratedDate = DateTime.Now.AddSeconds(-30).ToString("yyyy-MM-dd HH:mm:ss.fff"),
            State = "Ultra-High-Performance",
            ErrorMessage = null,
            EstimatedRTO = "00:00:10",
            IsDRReady = true
        };

        var expectedMessage = "High-Availability Trading DashboardView created successfully with ultra-low latency configuration!";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateDashboardViewResponse
            {
                Message = expectedMessage,
                BusinessViewId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateDashboardView(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateDashboardViewResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("High-Availability", response.Message);
        Assert.Contains("ultra-low latency", response.Message);
    }

    [Fact]
    public async Task UpdateDashboardView_HandlesAIMLIntegration()
    {
        // Arrange
        var command = new UpdateDashboardViewCommand
        {
            Id = Guid.NewGuid().ToString(),
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Enterprise AI/ML-Enhanced Business Intelligence Service",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "Enterprise Predictive Analytics and Machine Learning",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "Enterprise AI/ML Infrastructure Platform",
            Priority = 1,
            CompanyId = Guid.NewGuid().ToString(),
            Description = "AI/ML-enhanced enterprise dashboard view with predictive analytics, anomaly detection, automated decision-making, and intelligent resource optimization",
            EntityId = Guid.NewGuid().ToString(),
            MonitorType = "AI-Powered Predictive Monitoring",
            Type = 1,
            DataLagValue = "00:00:30",
            Status = "Active",
            Properties = "{\"aimlIntegration\":{\"models\":[{\"type\":\"anomaly_detection\",\"algorithm\":\"isolation_forest\",\"accuracy\":\"99.7%\",\"training_data\":\"6_months\"},{\"type\":\"predictive_scaling\",\"algorithm\":\"lstm_neural_network\",\"accuracy\":\"95.2%\",\"prediction_horizon\":\"4_hours\"},{\"type\":\"failure_prediction\",\"algorithm\":\"random_forest\",\"accuracy\":\"97.8%\",\"early_warning\":\"30_minutes\"}],\"automation\":{\"auto_scaling\":\"enabled\",\"self_healing\":\"enabled\",\"resource_optimization\":\"continuous\",\"alert_correlation\":\"intelligent\"},\"data_sources\":[\"metrics\",\"logs\",\"traces\",\"events\",\"external_feeds\"],\"processing\":{\"real_time_inference\":\"enabled\",\"batch_training\":\"daily\",\"model_updates\":\"weekly\",\"feature_engineering\":\"automated\"},\"insights\":{\"trend_analysis\":\"enabled\",\"capacity_planning\":\"automated\",\"cost_optimization\":\"continuous\",\"performance_tuning\":\"ai_driven\"}}}",
            ReplicationStatus = 1,
            DROperationStatus = 1,
            ConfiguredRPO = "00:01:00",
            ConfiguredRTO = "00:05:00",
            RPOThreshold = "00:00:30",
            SiteProperties = "{\"site\":\"ai_ml_cluster\",\"gpu_nodes\":\"8\",\"cpu_nodes\":\"16\"}",
            RPOGeneratedDate = DateTime.Now.AddMinutes(-2).ToString("yyyy-MM-dd HH:mm:ss"),
            RTOGeneratedDate = DateTime.Now.AddMinutes(-2).ToString("yyyy-MM-dd HH:mm:ss"),
            CurrentRPO = "00:00:25",
            CurrentRTO = "00:03:45",
            State = "AI-Enhanced",
            ErrorMessage = null,
            EstimatedRTO = "00:03:00",
            IsDRReady = true
        };

        var expectedMessage = "AI/ML integration updated successfully with enhanced predictive capabilities!";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateDashboardViewResponse
            {
                Message = expectedMessage,
                BusinessViewId = command.Id
            });

        // Act
        var result = await _controller.UpdateDashboardView(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateDashboardViewResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("AI/ML integration", response.Message);
        Assert.Contains("predictive capabilities", response.Message);
    }

    [Fact]
    public async Task GetBusinessViews_HandlesZeroDowntimeUpgrade()
    {
        // Arrange
        var zeroDowntimeBusinessViews = new List<BusinessViewPaginatedList>
        {
            new BusinessViewPaginatedList
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Zero-Downtime Core Banking Service",
                Priority = 1,
                Status = "Upgrading",
                IsDRReady = true,
                ConfiguredRPO = "00:00:00",
                ConfiguredRTO = "00:00:00",
                RPOThreshold = "00:00:00",
                CurrentRPO = "00:00:00",
                CurrentRTO = "00:00:00",
                Percentage = "100.0",
                IsDataLagExceed = false,
                IsPartial = false,
                WorkflowIsRunning = true
            },
            new BusinessViewPaginatedList
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Blue-Green Deployment Service",
                Priority = 1,
                Status = "Switching",
                IsDRReady = true,
                ConfiguredRPO = "00:00:01",
                ConfiguredRTO = "00:00:05",
                RPOThreshold = "00:00:00.5",
                CurrentRPO = "00:00:00.1",
                CurrentRTO = "00:00:02",
                Percentage = "99.999",
                IsDataLagExceed = false,
                IsPartial = false,
                WorkflowIsRunning = true
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessViewPaginatedListQuery>(), default))
            .ReturnsAsync(zeroDowntimeBusinessViews);

        // Act
        var result = await _controller.GetBusinessViews();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var businessViews = Assert.IsAssignableFrom<List<BusinessViewPaginatedList>>(okResult.Value);
        Assert.Equal(2, businessViews.Count);

        var zeroDowntimeService = businessViews.First(bv => bv.BusinessServiceName.Contains("Zero-Downtime"));
        Assert.Equal("Upgrading", zeroDowntimeService.Status);
        Assert.Equal("100.0", zeroDowntimeService.Percentage);
        Assert.Equal("00:00:00", zeroDowntimeService.CurrentRPO);

        var blueGreenService = businessViews.First(bv => bv.BusinessServiceName.Contains("Blue-Green"));
        Assert.Equal("Switching", blueGreenService.Status);
        Assert.Equal("99.999", blueGreenService.Percentage);
        Assert.True(blueGreenService.WorkflowIsRunning);
    }

    [Fact]
    public async Task GetDashboardViewById_HandlesQuantumComputingIntegration()
    {
        // Arrange
        var dashboardViewId = Guid.NewGuid().ToString();
        var quantumDashboardView = new DashboardViewDetailVm
        {
            Id = dashboardViewId,
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Enterprise Quantum-Enhanced Cryptographic Security Service",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "Enterprise Quantum Cryptography and Security Operations",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "Enterprise Quantum Computing Security Infrastructure",
            Priority = 1,
            CompanyId = Guid.NewGuid().ToString(),
            Description = "Quantum-enhanced enterprise dashboard view providing quantum-resistant cryptography, quantum key distribution, and post-quantum security monitoring for ultra-secure enterprise operations",
            EntityId = Guid.NewGuid().ToString(),
            MonitorType = "Quantum-Enhanced Security Monitoring",
            Type = 1,
            DataLagValue = "00:00:00.001",
            Status = "Active",
            Properties = "{\"quantumIntegration\":{\"quantum_processors\":[{\"type\":\"IBM_Q_System_One\",\"qubits\":127,\"coherence_time\":\"100_microseconds\",\"gate_fidelity\":\"99.9%\"},{\"type\":\"Google_Sycamore\",\"qubits\":70,\"coherence_time\":\"80_microseconds\",\"gate_fidelity\":\"99.8%\"}],\"cryptography\":{\"quantum_key_distribution\":\"enabled\",\"post_quantum_algorithms\":[\"CRYSTALS_Kyber\",\"CRYSTALS_Dilithium\",\"FALCON\",\"SPHINCS+\"],\"quantum_random_number_generation\":\"hardware_based\",\"quantum_resistant_protocols\":\"implemented\"},\"security\":{\"quantum_threat_detection\":\"active\",\"quantum_safe_communication\":\"enabled\",\"quantum_authentication\":\"multi_factor\",\"quantum_audit_trail\":\"immutable\"},\"performance\":{\"quantum_speedup\":\"exponential\",\"classical_fallback\":\"seamless\",\"hybrid_processing\":\"optimized\",\"error_correction\":\"surface_code\"}}}",
            ReplicationStatus = 1,
            DROperationStatus = 1,
            ConfiguredRPO = "00:00:00.001",
            ConfiguredRTO = "00:00:01",
            RPOThreshold = "00:00:00.0005",
            SiteProperties = "{\"site\":\"quantum_facility\",\"temperature\":\"15_millikelvin\",\"isolation\":\"electromagnetic\"}",
            CurrentRPO = "00:00:00.0002",
            CurrentRTO = "00:00:00.5",
            State = "Quantum-Secured",
            ErrorMessage = null,
            IsDRReady = true
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewDetailQuery>(q => q.Id == dashboardViewId), default))
            .ReturnsAsync(quantumDashboardView);

        // Act
        var result = await _controller.GetDashboardViewById(dashboardViewId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var dashboardView = Assert.IsType<DashboardViewDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Quantum-Enhanced Cryptographic Security Service", dashboardView.BusinessServiceName);
        Assert.Contains("quantumIntegration", dashboardView.Properties);
        Assert.Contains("quantum_processors", dashboardView.Properties);
        Assert.Contains("IBM_Q_System_One", dashboardView.Properties);
        Assert.Contains("Google_Sycamore", dashboardView.Properties);
        Assert.Equal("Quantum-Secured", dashboardView.State);
        Assert.Equal("00:00:00.001", dashboardView.DataLagValue);
    }

    [Fact]
    public async Task GetDashboardViewListByBusinessServiceId_HandlesEdgeComputingDistribution()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var edgeComputingViews = new List<DashboardViewByBusinessServiceIdVm>();

        var edgeLocations = new[] { "New York", "London", "Tokyo", "Sydney", "São Paulo", "Mumbai", "Frankfurt", "Singapore" };

        foreach (var location in edgeLocations)
        {
            edgeComputingViews.Add(new DashboardViewByBusinessServiceIdVm
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessServiceName = "Enterprise Global Edge Computing Service",
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = $"Enterprise Edge Computing - {location}",
                InfraObjectId = Guid.NewGuid().ToString(),
                InfraObjectName = $"Enterprise Edge Node - {location}",
                Priority = 1,
                CompanyId = Guid.NewGuid().ToString(),
                Description = $"Enterprise edge computing node in {location} providing low-latency processing and local data residency",
                EntityId = Guid.NewGuid().ToString(),
                MonitorType = "Edge Computing Monitoring",
                Type = 1,
                DataLagValue = "00:00:05",
                Status = "Active",
                Properties = $"{{\"edgeComputing\":{{\"location\":\"{location}\",\"latency\":\"<10ms\",\"bandwidth\":\"10Gbps\",\"local_storage\":\"100TB\",\"compute_capacity\":\"1000_cores\"}}}}",
                ReplicationStatus = 1,
                DROperationStatus = 1,
                ConfiguredRPO = "00:00:30",
                ConfiguredRTO = "00:02:00",
                RPOThreshold = "00:00:15",
                SiteProperties = $"{{\"site\":\"edge_node\",\"location\":\"{location}\",\"timezone\":\"local\"}}",
                CurrentRPO = "00:00:10",
                CurrentRTO = "00:01:30",
                State = "Edge-Active",
                ErrorMessage = null,
                IsDRReady = true
            });
        }

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(edgeComputingViews);

        // Act
        var result = await _controller.GetDashboardViewListByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var views = Assert.IsAssignableFrom<List<DashboardViewByBusinessServiceIdVm>>(okResult.Value);
        Assert.Equal(8, views.Count);
        Assert.All(views, v => Assert.Equal(businessServiceId, v.BusinessServiceId));
        Assert.All(views, v => Assert.Equal("Edge-Active", v.State));
        Assert.Contains(views, v => v.BusinessFunctionName.Contains("New York"));
        Assert.Contains(views, v => v.BusinessFunctionName.Contains("London"));
        Assert.Contains(views, v => v.BusinessFunctionName.Contains("Tokyo"));
        Assert.Contains(views, v => v.InfraObjectName.Contains("Edge Node"));
    }

    [Fact]
    public async Task GetDatalagStatusByLast7DaysList_HandlesRealTimeAnalytics()
    {
        // Arrange
        var realTimeAnalyticsData = new List<DataLagStatusbyLast7DaysVm>();
        var analyticsTypes = new[] { "Financial", "Healthcare", "Manufacturing", "Retail", "Energy", "Transportation", "Telecommunications" };

        for (int day = 0; day < 7; day++)
        {
            foreach (var type in analyticsTypes)
            {
                realTimeAnalyticsData.Add(new DataLagStatusbyLast7DaysVm
                {
                    Id = Guid.NewGuid().ToString(),
                    InfraObjectId = Guid.NewGuid().ToString(),
                    InfraObjectName = $"Enterprise {type} Real-Time Analytics Infrastructure",
                    BusinessServiceId = Guid.NewGuid().ToString(),
                    BusinessServiceName = $"Enterprise {type} Analytics Service",
                    BusinessFunctionId = Guid.NewGuid().ToString(),
                    BusinessFunctionName = $"Enterprise {type} Data Processing",
                    CompanyId = Guid.NewGuid().ToString(),
                    EntityId = Guid.NewGuid().ToString(),
                    MonitorType = "Real-Time Analytics Monitoring",
                    Type = 1,
                    DataLagValue = day == 0 ? "00:00:01" : $"00:00:{(day * 5):D2}",
                    Status = day < 6 ? "Active" : "Optimizing",
                    Properties = $"{{\"realTimeAnalytics\":{{\"type\":\"{type}\",\"day\":{day + 1},\"processing_rate\":\"1M_events_per_second\",\"latency\":\"<100ms\"}}}}",
                    ReplicationStatus = 1,
                    DROperationStatus = 1,
                    CurrentRTO = $"00:{(day + 1):D2}:00"
                });
            }
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataLagStatusbyLast7DaysQuery>(), default))
            .ReturnsAsync(realTimeAnalyticsData);

        // Act
        var result = await _controller.GetDatalagStatusByLast7DaysList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var statusList = Assert.IsAssignableFrom<List<DataLagStatusbyLast7DaysVm>>(okResult.Value);
        Assert.Equal(49, statusList.Count); // 7 days × 7 analytics types
        Assert.Contains(statusList, s => s.BusinessServiceName.Contains("Financial"));
        Assert.Contains(statusList, s => s.BusinessServiceName.Contains("Healthcare"));
        Assert.Contains(statusList, s => s.DataLagValue == "00:00:01");
        Assert.All(statusList, s => Assert.Contains("Real-Time Analytics", s.MonitorType));
    }

    [Fact]
    public async Task CreateDashboardView_HandlesBlockchainIntegration()
    {
        // Arrange
        var command = new CreateDashboardViewCommand
        {
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Enterprise Blockchain-Based Supply Chain Service",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "Enterprise Blockchain Supply Chain Operations",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "Enterprise Blockchain Infrastructure Network",
            Priority = 1,
            CompanyId = Guid.NewGuid().ToString(),
            Description = "Blockchain-integrated enterprise dashboard view providing immutable supply chain tracking, smart contract monitoring, and decentralized consensus validation",
            EntityId = Guid.NewGuid().ToString(),
            MonitorType = "Blockchain Network Monitoring",
            Type = 1,
            DataLagValue = "00:00:15",
            Status = "Active",
            Properties = "{\"blockchainIntegration\":{\"networks\":[{\"type\":\"Ethereum\",\"consensus\":\"Proof_of_Stake\",\"tps\":\"15000\",\"finality\":\"12_seconds\"},{\"type\":\"Hyperledger_Fabric\",\"consensus\":\"PBFT\",\"tps\":\"20000\",\"finality\":\"3_seconds\"},{\"type\":\"Polygon\",\"consensus\":\"PoS_Checkpoint\",\"tps\":\"65000\",\"finality\":\"2_seconds\"}],\"smart_contracts\":[{\"name\":\"SupplyChainTracker\",\"language\":\"Solidity\",\"gas_optimization\":\"enabled\",\"audit_status\":\"verified\"},{\"name\":\"QualityAssurance\",\"language\":\"Chaincode\",\"performance\":\"optimized\",\"compliance\":\"ISO_certified\"}],\"monitoring\":{\"transaction_throughput\":\"real_time\",\"block_confirmation\":\"instant\",\"network_health\":\"continuous\",\"consensus_monitoring\":\"active\"},\"security\":{\"multi_signature\":\"enabled\",\"hardware_security_modules\":\"integrated\",\"zero_knowledge_proofs\":\"implemented\",\"formal_verification\":\"completed\"}}}",
            ReplicationStatus = 1,
            DROperationStatus = 1,
            ConfiguredRPO = "00:00:15",
            ConfiguredRTO = "00:01:00",
            RPOThreshold = "00:00:10",
            SiteProperties = "{\"site\":\"blockchain_network\",\"nodes\":\"50\",\"consensus\":\"distributed\"}",
            CurrentRPO = "00:00:08",
            CurrentRTO = "00:00:45",
            RPOGeneratedDate = DateTime.Now.AddMinutes(-1).ToString("yyyy-MM-dd HH:mm:ss"),
            RTOGeneratedDate = DateTime.Now.AddMinutes(-1).ToString("yyyy-MM-dd HH:mm:ss"),
            State = "Blockchain-Secured",
            ErrorMessage = null,
            EstimatedRTO = "00:00:30",
            IsDRReady = true
        };

        var expectedMessage = "Blockchain-integrated DashboardView created successfully with immutable tracking!";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateDashboardViewResponse
            {
                Message = expectedMessage,
                BusinessViewId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateDashboardView(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateDashboardViewResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Blockchain-integrated", response.Message);
        Assert.Contains("immutable tracking", response.Message);
    }

    [Fact]
    public async Task UpdateDashboardView_HandlesIoTIntegration()
    {
        // Arrange
        var command = new UpdateDashboardViewCommand
        {
            Id = Guid.NewGuid().ToString(),
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Enterprise IoT-Enabled Smart Manufacturing Service",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "Enterprise IoT Manufacturing Operations",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "Enterprise IoT Manufacturing Infrastructure",
            Priority = 1,
            CompanyId = Guid.NewGuid().ToString(),
            Description = "IoT-integrated enterprise dashboard view managing millions of connected devices, real-time sensor data processing, and automated manufacturing workflows",
            EntityId = Guid.NewGuid().ToString(),
            MonitorType = "IoT Device and Sensor Monitoring",
            Type = 1,
            DataLagValue = "00:00:02",
            Status = "Active",
            Properties = "{\"iotIntegration\":{\"devices\":[{\"type\":\"temperature_sensors\",\"count\":50000,\"frequency\":\"1_second\",\"accuracy\":\"±0.1°C\"},{\"type\":\"pressure_sensors\",\"count\":25000,\"frequency\":\"500ms\",\"accuracy\":\"±0.01_bar\"},{\"type\":\"vibration_sensors\",\"count\":15000,\"frequency\":\"100ms\",\"accuracy\":\"±0.001g\"},{\"type\":\"smart_cameras\",\"count\":5000,\"resolution\":\"4K\",\"fps\":\"60\"}],\"connectivity\":[{\"protocol\":\"5G\",\"bandwidth\":\"10Gbps\",\"latency\":\"<1ms\"},{\"protocol\":\"WiFi_6E\",\"bandwidth\":\"9.6Gbps\",\"latency\":\"<5ms\"},{\"protocol\":\"LoRaWAN\",\"range\":\"15km\",\"battery_life\":\"10_years\"}],\"data_processing\":{\"edge_computing\":\"enabled\",\"real_time_analytics\":\"active\",\"machine_learning\":\"continuous\",\"predictive_maintenance\":\"automated\"},\"automation\":{\"quality_control\":\"ai_powered\",\"production_optimization\":\"real_time\",\"supply_chain_integration\":\"seamless\",\"energy_management\":\"intelligent\"}}}",
            ReplicationStatus = 1,
            DROperationStatus = 1,
            ConfiguredRPO = "00:00:05",
            ConfiguredRTO = "00:00:30",
            RPOThreshold = "00:00:03",
            SiteProperties = "{\"site\":\"smart_factory\",\"devices\":\"95000\",\"protocols\":\"multi\"}",
            RPOGeneratedDate = DateTime.Now.AddMinutes(-1).ToString("yyyy-MM-dd HH:mm:ss"),
            RTOGeneratedDate = DateTime.Now.AddMinutes(-1).ToString("yyyy-MM-dd HH:mm:ss"),
            CurrentRPO = "00:00:02",
            CurrentRTO = "00:00:20",
            State = "IoT-Optimized",
            ErrorMessage = null,
            EstimatedRTO = "00:00:15",
            IsDRReady = true
        };

        var expectedMessage = "IoT integration updated successfully with enhanced device management!";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateDashboardViewResponse
            {
                Message = expectedMessage,
                BusinessViewId = command.Id
            });

        // Act
        var result = await _controller.UpdateDashboardView(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateDashboardViewResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("IoT integration", response.Message);
        Assert.Contains("enhanced device management", response.Message);
    }

    [Fact]
    public async Task GetDashboardNames_HandlesGlobalEnterpriseDeployment()
    {
        // Arrange
        var globalDashboardNames = new List<DashboardViewNameVm>();
        var regions = new[] { "North America", "Europe", "Asia Pacific", "Latin America", "Middle East", "Africa" };
        var infrastructureTypes = new[] { "Data Centers", "Cloud Regions", "Edge Nodes", "Satellite Links", "Submarine Cables", "5G Networks" };

        foreach (var region in regions)
        {
            foreach (var infraType in infrastructureTypes)
            {
                for (int i = 1; i <= 5; i++)
                {
                    globalDashboardNames.Add(new DashboardViewNameVm
                    {
                        InfraObjectId = Guid.NewGuid().ToString(),
                        InfraObjectName = $"Enterprise {region} {infraType} Infrastructure {i:D2}",
                        EntityId = Guid.NewGuid().ToString(),
                        MonitorType = $"Enterprise {region} {infraType} Monitoring"
                    });
                }
            }
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDashboardNameQuery>(), default))
            .ReturnsAsync(globalDashboardNames);

        // Act
        var result = await _controller.GetDashboardNames();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var dashboardNames = Assert.IsAssignableFrom<List<DashboardViewNameVm>>(okResult.Value);
        Assert.Equal(180, dashboardNames.Count); // 6 regions × 6 infrastructure types × 5 instances
        Assert.Contains(dashboardNames, dn => dn.InfraObjectName.Contains("North America"));
        Assert.Contains(dashboardNames, dn => dn.InfraObjectName.Contains("Data Centers"));
        Assert.Contains(dashboardNames, dn => dn.MonitorType.Contains("Satellite Links"));
        Assert.All(dashboardNames, dn => Assert.Contains("Enterprise", dn.InfraObjectName));
    }

    [Fact]
    public async Task DeleteDashboardView_HandlesComplexDependencies()
    {
        // Arrange
        var dashboardViewId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDashboardViewCommand>(c => c.BusinessViewId == dashboardViewId), default))
            .ThrowsAsync(new EntityAssociatedException("The dashboard view has complex dependencies: 15 active workflows, 8 alert configurations, 23 user permissions, 5 scheduled reports, and 12 API integrations. Please resolve these dependencies before deletion."));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<EntityAssociatedException>(() => _controller.DeleteDashboardView(dashboardViewId));
        Assert.Contains("complex dependencies", exception.Message);
        Assert.Contains("15 active workflows", exception.Message);
        Assert.Contains("8 alert configurations", exception.Message);
        Assert.Contains("23 user permissions", exception.Message);
        Assert.Contains("5 scheduled reports", exception.Message);
        Assert.Contains("12 API integrations", exception.Message);
    }

    [Fact]
    public async Task CreateDashboardView_HandlesQuantumResistantSecurity()
    {
        // Arrange
        var command = new CreateDashboardViewCommand
        {
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Enterprise Quantum-Resistant Security Operations Center",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "Enterprise Post-Quantum Cryptographic Operations",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "Enterprise Quantum-Safe Security Infrastructure",
            Priority = 1,
            CompanyId = Guid.NewGuid().ToString(),
            Description = "Quantum-resistant enterprise dashboard view implementing post-quantum cryptography, quantum-safe protocols, and future-proof security measures against quantum computing threats",
            EntityId = Guid.NewGuid().ToString(),
            MonitorType = "Post-Quantum Security Monitoring",
            Type = 1,
            DataLagValue = "00:00:00.1",
            Status = "Active",
            Properties = "{\"quantumResistantSecurity\":{\"postQuantumAlgorithms\":[{\"name\":\"CRYSTALS_Kyber\",\"type\":\"KEM\",\"security_level\":\"256_bit\",\"performance\":\"optimized\"},{\"name\":\"CRYSTALS_Dilithium\",\"type\":\"Digital_Signature\",\"security_level\":\"256_bit\",\"signature_size\":\"2420_bytes\"},{\"name\":\"FALCON\",\"type\":\"Digital_Signature\",\"security_level\":\"512_bit\",\"signature_size\":\"690_bytes\"},{\"name\":\"SPHINCS+\",\"type\":\"Hash_Based_Signature\",\"security_level\":\"256_bit\",\"stateless\":true}],\"quantumSafeProtocols\":[{\"protocol\":\"TLS_1.3_PQ\",\"cipher_suites\":\"hybrid_classical_quantum\",\"handshake_time\":\"<50ms\"},{\"protocol\":\"IPSec_PQ\",\"encryption\":\"AES_256_GCM_Kyber\",\"authentication\":\"Dilithium_3\"},{\"protocol\":\"SSH_PQ\",\"key_exchange\":\"Kyber_1024\",\"host_authentication\":\"Dilithium_5\"}],\"threatDetection\":{\"quantum_computer_detection\":\"enabled\",\"cryptographic_agility\":\"automated\",\"algorithm_migration\":\"seamless\",\"security_assessment\":\"continuous\"},\"compliance\":{\"nist_standards\":\"compliant\",\"quantum_readiness\":\"level_5\",\"migration_timeline\":\"2024_2030\",\"risk_assessment\":\"minimal\"}}}",
            ReplicationStatus = 1,
            DROperationStatus = 1,
            ConfiguredRPO = "00:00:00.1",
            ConfiguredRTO = "00:00:05",
            RPOThreshold = "00:00:00.05",
            SiteProperties = "{\"site\":\"quantum_safe_facility\",\"security_level\":\"maximum\",\"quantum_readiness\":\"certified\"}",
            CurrentRPO = "00:00:00.02",
            CurrentRTO = "00:00:02",
            RPOGeneratedDate = DateTime.Now.AddSeconds(-10).ToString("yyyy-MM-dd HH:mm:ss.fff"),
            RTOGeneratedDate = DateTime.Now.AddSeconds(-10).ToString("yyyy-MM-dd HH:mm:ss.fff"),
            State = "Quantum-Safe",
            ErrorMessage = null,
            EstimatedRTO = "00:00:01",
            IsDRReady = true
        };

        var expectedMessage = "Quantum-resistant security DashboardView created successfully with post-quantum cryptography!";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateDashboardViewResponse
            {
                Message = expectedMessage,
                BusinessViewId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateDashboardView(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateDashboardViewResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Quantum-resistant security", response.Message);
        Assert.Contains("post-quantum cryptography", response.Message);
    }

    [Fact]
    public async Task GetDashboardViewById_HandlesTimeoutScenario()
    {
        // Arrange
        var dashboardViewId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewDetailQuery>(q => q.Id == dashboardViewId), default))
            .ThrowsAsync(new TimeoutException("Dashboard view retrieval timed out after 30 seconds due to high system load"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<TimeoutException>(() => _controller.GetDashboardViewById(dashboardViewId));
        Assert.Contains("timed out", exception.Message);
        Assert.Contains("30 seconds", exception.Message);
        Assert.Contains("high system load", exception.Message);
    }

    [Fact]
    public async Task UpdateDashboardView_HandlesOptimisticConcurrencyConflict()
    {
        // Arrange
        var command = _dashboardViewFixture.UpdateDashboardViewCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("Optimistic concurrency conflict: The dashboard view has been modified by another user. Please refresh and try again."));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateDashboardView(command));
        Assert.Contains("Optimistic concurrency conflict", exception.Message);
        Assert.Contains("modified by another user", exception.Message);
        Assert.Contains("refresh and try again", exception.Message);
    }

    [Fact]
    public async Task GetBusinessViews_HandlesMemoryPressureScenario()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessViewPaginatedListQuery>(), default))
            .ThrowsAsync(new OutOfMemoryException("Insufficient memory to process large business view dataset. Consider implementing pagination or filtering."));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<OutOfMemoryException>(() => _controller.GetBusinessViews());
        Assert.Contains("Insufficient memory", exception.Message);
        Assert.Contains("large business view dataset", exception.Message);
        Assert.Contains("pagination or filtering", exception.Message);
    }



    [Fact]
    public async Task GetItViewByInfraObjectId_ReturnsOkResult()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var itView = _dashboardViewFixture.ItViewByInfraObjectIdVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetItViewByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), default))
            .ReturnsAsync(itView);

        // Act
        var result = await _controller.GetItViewByInfraObjectId(infraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedItView = Assert.IsType<ItViewByInfraObjectIdVm>(okResult.Value);
        Assert.Equal(itView.Id, returnedItView.Id);
        Assert.Equal("Enterprise Primary Server", returnedItView.PRServerName);
        Assert.Equal("Enterprise DR Server", returnedItView.DRServerName);
        Assert.Equal("Active", returnedItView.PRServerStatus);
        Assert.Equal("Standby", returnedItView.DRServerStatus);
        Assert.Equal("Synchronous", returnedItView.ReplicationType);
    }

    [Fact]
    public async Task GetItViewByInfraObjectId_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidInfraObjectId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetItViewByInfraObjectId(invalidInfraObjectId));
    }

    [Fact]
    public async Task GetDashboardViewListByBusinessFunctionId_ReturnsOkResult()
    {
        // Arrange
        var businessFunctionId = Guid.NewGuid().ToString();
        var dashboardViews = new List<DashboardViewByBusinessFunctionIdVm>
        {
            _dashboardViewFixture.DashboardViewByBusinessFunctionIdVm,
            new DashboardViewByBusinessFunctionIdVm
            {
                Id = Guid.NewGuid().ToString(),
                BusinessFunctionId = businessFunctionId,
                BusinessFunctionName = "Enterprise Secondary Function",
                InfraObjectName = "Enterprise Secondary Infrastructure",
                Status = "Active",
                DataLagValue = "00:03:00"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewByBusinessFunctionIdQuery>(q => q.BusinessFunctionId == businessFunctionId), default))
            .ReturnsAsync(dashboardViews);

        // Act
        var result = await _controller.GetDashboardViewListByBusinessFunctionId(businessFunctionId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedViews = Assert.IsAssignableFrom<List<DashboardViewByBusinessFunctionIdVm>>(okResult.Value);
        Assert.Equal(2, returnedViews.Count);
        Assert.Contains(returnedViews, v => v.BusinessFunctionName == "Enterprise Core Function");
        Assert.Contains(returnedViews, v => v.BusinessFunctionName == "Enterprise Secondary Function");
        Assert.All(returnedViews, v => Assert.Equal("Active", v.Status));
    }

    [Fact]
    public async Task GetDcMappingDetails_ReturnsOkResult()
    {
        // Arrange
        var siteId = Guid.NewGuid().ToString();
        var dcMappings = new List<GetDcMappingListVm>
        {
            _dashboardViewFixture.GetDcMappingListVm,
            new GetDcMappingListVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Secondary DC Service",
                SiteProperties = "{\"site\":{\"type\":\"datacenter\",\"location\":\"secondary\"}}",
                Status = "Active",
                Priority = 2
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDcMappingListQuery>(q => q.SiteId == siteId), default))
            .ReturnsAsync(dcMappings);

        // Act
        var result = await _controller.GetDcMappingDetails(siteId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedMappings = Assert.IsAssignableFrom<List<GetDcMappingListVm>>(okResult.Value);
        Assert.Equal(2, returnedMappings.Count);
        Assert.Contains(returnedMappings, m => m.BusinessServiceName == "Enterprise DC Mapping Service");
        Assert.Contains(returnedMappings, m => m.BusinessServiceName == "Enterprise Secondary DC Service");
        Assert.All(returnedMappings, m => Assert.Equal("Active", m.Status));
    }

    [Fact]
    public async Task GetBusinessServiceTopologyByBusinessServiceId_ReturnsOkResult()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var topologies = new List<GetServiceTopologyListVm>
        {
            _dashboardViewFixture.GetServiceTopologyListVm,
            new GetServiceTopologyListVm
            {
                SiteId = Guid.NewGuid().ToString(),
                SiteName = "Enterprise Secondary Topology Site",
                SiteType = "Secondary",
                ReplicationStatus = new List<int> { 1, 3 },
                DROperationStatus = new List<int> { 1, 3 }
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetServiceTopologyListQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(topologies);

        // Act
        var result = await _controller.GetBusinessServiceTopologyByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedTopologies = Assert.IsAssignableFrom<List<GetServiceTopologyListVm>>(okResult.Value);
        Assert.Equal(2, returnedTopologies.Count);
        Assert.Contains(returnedTopologies, t => t.SiteName == "Enterprise Topology Site");
        Assert.Contains(returnedTopologies, t => t.SiteName == "Enterprise Secondary Topology Site");
        Assert.Contains(returnedTopologies, t => t.SiteType == "Primary");
        Assert.Contains(returnedTopologies, t => t.SiteType == "Secondary");
    }

    [Fact]
    public async Task GetItViewList_ReturnsOkResult()
    {
        // Arrange
        var itViews = new List<GetItViewListVm>
        {
            _dashboardViewFixture.GetItViewListVm,
            new GetItViewListVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Secondary IT View Service",
                Status = "Active"
            },
            new GetItViewListVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Maintenance IT View Service",
                Status = "Maintenance"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetItViewListQuery>(), default))
            .ReturnsAsync(itViews);

        // Act
        var result = await _controller.GetItViewList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedViews = Assert.IsAssignableFrom<List<GetItViewListVm>>(okResult.Value);
        Assert.Equal(3, returnedViews.Count);
        Assert.Contains(returnedViews, v => v.BusinessServiceName == "Enterprise IT View Service");
        Assert.Contains(returnedViews, v => v.Status == "Active");
        Assert.Contains(returnedViews, v => v.Status == "Maintenance");
    }

    [Fact]
    public async Task GetDrillAnalytic_ReturnsOkResult()
    {
        // Arrange
        var drillAnalytics = _dashboardViewFixture.DrillAnalyticsDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetOperationalReadinessQuery>(), default))
            .ReturnsAsync(drillAnalytics);

        // Act
        var result = await _controller.GetDrillAnalytic();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedAnalytics = Assert.IsType<DrillAnalyticsDetailVm>(okResult.Value);
        Assert.Equal(25, returnedAnalytics.ConfiguredProfileCount);
        Assert.Equal(20, returnedAnalytics.ExecutedProfileCount);
        Assert.Equal(18, returnedAnalytics.ExecutedWorkflowCount);
    }

    [Fact]
    public async Task GetSlaBreach_ReturnsOkResult()
    {
        // Arrange
        var slaBreach = _dashboardViewFixture.GetSlaBreachListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetSlaBreachListQuery>(), default))
            .ReturnsAsync(slaBreach);

        // Act
        var result = await _controller.GetSlaBreach();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedBreach = Assert.IsType<GetSlaBreachListVm>(okResult.Value);
        Assert.Equal(5, returnedBreach.SlaBreachCount);
        Assert.Equal(95, returnedBreach.SlaNonBreachCount);
        Assert.Equal(90, returnedBreach.SlaMeetingRtoCount);
        Assert.Equal(3, returnedBreach.ActiveAlertCount);
    }

    [Fact]
    public async Task GetBusinessImpactAnalysis_ReturnsOkResult()
    {
        // Arrange
        var businessImpactAnalysis = new List<BusinessImpactAnalysisVm>
        {
            _dashboardViewFixture.BusinessImpactAnalysisVm,
            new BusinessImpactAnalysisVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                ImpactType = "Medium",
                Count = 10,
                ImpactPercentage = 70
            },
            new BusinessImpactAnalysisVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                ImpactType = "Low",
                Count = 5,
                ImpactPercentage = 95
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessImpactAnalysisQuery>(), default))
            .ReturnsAsync(businessImpactAnalysis);

        // Act
        var result = await _controller.GetBusinessImpactAnalysis();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedAnalysis = Assert.IsAssignableFrom<List<BusinessImpactAnalysisVm>>(okResult.Value);
        Assert.Equal(3, returnedAnalysis.Count);
        Assert.Contains(returnedAnalysis, a => a.ImpactType == "High" && a.ImpactPercentage == 85);
        Assert.Contains(returnedAnalysis, a => a.ImpactType == "Medium" && a.ImpactPercentage == 70);
        Assert.Contains(returnedAnalysis, a => a.ImpactType == "Low" && a.ImpactPercentage == 95);
    }

    [Fact]
    public async Task GetSiteCountList_ReturnsOkResult()
    {
        // Arrange
        var siteCountList = new List<SiteCountListVm>
        {
            _dashboardViewFixture.SiteCountListVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetSiteCountListQuery>(), default))
            .ReturnsAsync(siteCountList);

        // Act
        var result = await _controller.GetSiteCountList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedSiteCountList = Assert.IsAssignableFrom<List<SiteCountListVm>>(okResult.Value);
        Assert.Single(returnedSiteCountList);

        var siteCount = returnedSiteCountList.First();
        Assert.Equal(12, siteCount.SiteCount);
        Assert.Equal(10, siteCount.TotalDrReadyCount);
        Assert.Equal(8, siteCount.DrReadyCount);
        Assert.Equal(2, siteCount.NotDrReadyCount);
        Assert.NotNull(siteCount.GroupBySites);
    }

    [Fact]
    public async Task GetSiteCountList_HandlesEmptyResult()
    {
        // Arrange
        var emptySiteCountList = new List<SiteCountListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetSiteCountListQuery>(), default))
            .ReturnsAsync(emptySiteCountList);

        // Act
        var result = await _controller.GetSiteCountList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedSiteCountList = Assert.IsAssignableFrom<List<SiteCountListVm>>(okResult.Value);
        Assert.Empty(returnedSiteCountList);
    }

    [Fact]
    public async Task GetVerifiedWorkflowList_ReturnsOkResult()
    {
        // Arrange
        var verifyWorkflow = _dashboardViewFixture.VerifyWorkflowDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetVerifyWorkflowDetailQuery>(), default))
            .ReturnsAsync(verifyWorkflow);

        // Act
        var result = await _controller.GetVerifiedWorkflowList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedWorkflow = Assert.IsType<VerifyWorkflowDetailVm>(okResult.Value);
        Assert.Equal(50, returnedWorkflow.TotalWorkflowCount);
        Assert.Equal(45, returnedWorkflow.WorkflowVerifyCount);
        Assert.Equal(5, returnedWorkflow.WorkflowNotVerifyCount);
    }

    [Fact]
    public async Task GetDcMappingSiteDetails_ReturnsOkResult()
    {
        // Arrange
        var dcMappingSites = _dashboardViewFixture.GetDcMappingSitesVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDcMappingSitesQuery>(), default))
            .ReturnsAsync(dcMappingSites);

        // Act
        var result = await _controller.GetDcMappingSiteDetails();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedSites = Assert.IsType<GetDcMappingSitesVm>(okResult.Value);
        Assert.Equal(8, returnedSites.TotalSiteCount);
        Assert.Equal(25, returnedSites.TotalAppCount);
        Assert.NotNull(returnedSites.DcMappingSites);
        Assert.NotNull(returnedSites.DcMappingBusinessServices);
    }

    [Fact]
    public async Task GetTotalSiteDetailsForOneView_ReturnsOkResult()
    {
        // Arrange
        var siteId = "[\"site1\",\"site2\"]";
        var category = "Production";
        var totalSiteDetails = _dashboardViewFixture.TotalSiteDetailForOneViewListVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetTotalSiteDetailForOneViewQuery>(q =>
                q.SiteIds.Count == 2 && q.CategoryType == category), default))
            .ReturnsAsync(totalSiteDetails);

        // Act
        var result = await _controller.GetTotalSiteDetailsForOneView(siteId, category);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetails = Assert.IsType<TotalSiteDetailForOneViewListVm>(okResult.Value);
        Assert.NotNull(returnedDetails.ServerTypeWithListVms);
    }

    [Fact]
    public async Task GetBreachDetails_ReturnsOkResult()
    {
        // Arrange
        var breachDetails = _dashboardViewFixture.BreachDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBreachDetailQuery>(), default))
            .ReturnsAsync(breachDetails);

        // Act
        var result = await _controller.GetBreachDetails();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedBreach = Assert.IsType<BreachDetailVm>(okResult.Value);
        Assert.Equal(85, returnedBreach.RpoAchievedCount);
        Assert.Equal(15, returnedBreach.RpoExceededCount);
        Assert.Equal(90, returnedBreach.RtoAchievedCount);
        Assert.Equal(10, returnedBreach.RtoExceededCount);
    }

    [Fact]
    public async Task GetLastDrillDetails_ReturnsOkResult()
    {
        // Arrange
        var lastDrillDetails = _dashboardViewFixture.LastDrillDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<LastDrillDetailQuery>(), default))
            .ReturnsAsync(lastDrillDetails);

        // Act
        var result = await _controller.GetLastDrillDetails();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDrill = Assert.IsType<LastDrillDetailVm>(okResult.Value);
        Assert.Equal("Enterprise DR Test Profile", returnedDrill.ProfileName);
        Assert.Equal("02:15:30", returnedDrill.Duration);
        Assert.Equal("Success", returnedDrill.Status);
        Assert.True(returnedDrill.LastExecutionTime > DateTime.Now.AddDays(-2));
    }

    [Fact]
    public async Task GetDatalagDetailByBusinessServiceId_ReturnsOkResult()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var datalagDetails = _dashboardViewFixture.DatalagByBusinessServiceIdVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataLagDetailByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(datalagDetails);

        // Act
        var result = await _controller.GetDatalagDetailByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDatalag = Assert.IsType<DatalagByBusinessServiceIdVm>(okResult.Value);
        Assert.Equal("Enterprise Data Lag Service", returnedDatalag.BusinessServiceName);
        Assert.Equal(20, returnedDatalag.TotalBusinessFunctionCount);
        Assert.Equal(14, returnedDatalag.BusinessFunctionUnderRPOCount);
        Assert.Equal(50, returnedDatalag.TotalInfraObjectCount);
        Assert.Equal(34, returnedDatalag.InfraUnderRPOCount);
    }

    [Fact]
    public async Task CreateDashboardView_ReturnsCreatedAtActionResult()
    {
        // Arrange
        var command = _dashboardViewFixture.CreateDashboardViewCommand;
        var expectedResponse = new CreateDashboardViewResponse
        {
            Message = "Enterprise Dashboard Service created successfully!",
            BusinessViewId = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDashboardView(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateDashboardViewResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedResponse.Message, response.Message);
        Assert.Equal(expectedResponse.BusinessViewId, response.BusinessViewId);
        Assert.Contains("created successfully", response.Message);
    }

    [Fact]
    public async Task GetDashboardViewListByInfraObjectId_ReturnsOkResult()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var dashboardView = _dashboardViewFixture.GetDashboardViewByInfraObjectIdVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), default))
            .ReturnsAsync(dashboardView);

        // Act
        var result = await _controller.GetDashboardViewListByInfraObjectId(infraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedView = Assert.IsType<GetDashboardViewByInfraObjectIdVm>(okResult.Value);
        Assert.Equal(dashboardView.Id, returnedView.Id);
        Assert.Equal(dashboardView.InfraObjectName, returnedView.InfraObjectName);
    }

    [Fact]
    public async Task GetByEntityId_ReturnsOkResult()
    {
        // Arrange
        var entityId = Guid.NewGuid().ToString();
        var type = "Server";
        var entityView = _dashboardViewFixture.GetByEntityIdVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByEntityIdQuery>(q => q.EntityId == entityId && q.Type == type), default))
            .ReturnsAsync(entityView);

        // Act
        var result = await _controller.GetMonitorServiceStatusByIdAndType(entityId, type);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedView = Assert.IsType<GetByEntityIdVm>(okResult.Value);
        Assert.Equal(entityView.Id, returnedView.Id);
        Assert.Equal(entityView.Type, returnedView.Type);
        Assert.Equal(entityView.InfraObjectName, returnedView.InfraObjectName);
    }

  

    [Fact]
    public async Task GetItViewByInfraObjectId_HandlesComplexInfrastructure()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var complexItView = new ItViewByInfraObjectIdVm
        {
            Id = Guid.NewGuid().ToString(),
            Properties = "{\"complexInfra\":{\"multiTier\":true,\"loadBalanced\":true,\"clustered\":true,\"geoDistributed\":true}}",
            PRServerId = Guid.NewGuid().ToString(),
            DRServerId = Guid.NewGuid().ToString(),
            PRServerName = "Enterprise Multi-Tier Primary Cluster Node 01",
            DRServerName = "Enterprise Multi-Tier DR Cluster Node 01",
            PRServerStatus = "Active-Primary",
            DRServerStatus = "Active-Standby",
            PRDatabaseId = Guid.NewGuid().ToString(),
            DRDatabaseId = Guid.NewGuid().ToString(),
            PRDatabaseName = "Enterprise Multi-Tier Primary Database Cluster",
            DRDatabaseName = "Enterprise Multi-Tier DR Database Cluster",
            PRDatabaseStatus = "Online-Clustered",
            DRDatabaseStatus = "Synchronized-Clustered",
            ReplicationType = "Asynchronous-Multi-Site",
            ReplicationCategoryType = "Database-Cluster",
            DROperationStatus = "Ready-Multi-Site"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetItViewByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), default))
            .ReturnsAsync(complexItView);

        // Act
        var result = await _controller.GetItViewByInfraObjectId(infraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedItView = Assert.IsType<ItViewByInfraObjectIdVm>(okResult.Value);
        Assert.Contains("Multi-Tier", returnedItView.PRServerName);
        Assert.Contains("Cluster", returnedItView.PRDatabaseName);
        Assert.Contains("complexInfra", returnedItView.Properties);
        Assert.Equal("Asynchronous-Multi-Site", returnedItView.ReplicationType);
        Assert.Equal("Ready-Multi-Site", returnedItView.DROperationStatus);
    }

    [Fact]
    public async Task GetDashboardViewListByBusinessFunctionId_HandlesEmptyResult()
    {
        // Arrange
        var businessFunctionId = Guid.NewGuid().ToString();
        var emptyList = new List<DashboardViewByBusinessFunctionIdVm>();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewByBusinessFunctionIdQuery>(q => q.BusinessFunctionId == businessFunctionId), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDashboardViewListByBusinessFunctionId(businessFunctionId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedViews = Assert.IsAssignableFrom<List<DashboardViewByBusinessFunctionIdVm>>(okResult.Value);
        Assert.Empty(returnedViews);
    }

    [Fact]
    public async Task GetDcMappingDetails_WithNullSiteId_ReturnsOkResult()
    {
        // Arrange
        string siteId = null;
        var dcMappings = new List<GetDcMappingListVm>
        {
            new GetDcMappingListVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Global DC Mapping Service",
                SiteProperties = "{\"site\":{\"type\":\"global\",\"scope\":\"all\"}}",
                Status = "Active",
                Priority = 1
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDcMappingListQuery>(q => q.SiteId == null), default))
            .ReturnsAsync(dcMappings);

        // Act
        var result = await _controller.GetDcMappingDetails(siteId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedMappings = Assert.IsAssignableFrom<List<GetDcMappingListVm>>(okResult.Value);
        Assert.Single(returnedMappings);
        Assert.Contains("Global", returnedMappings.First().BusinessServiceName);
    }

    [Fact]
    public async Task GetBusinessServiceTopologyByBusinessServiceId_WithNullBusinessServiceId_ReturnsOkResult()
    {
        // Arrange
        string businessServiceId = null;
        var topologies = new List<GetServiceTopologyListVm>
        {
            new GetServiceTopologyListVm
            {
                SiteId = Guid.NewGuid().ToString(),
                SiteName = "Enterprise Global Topology",
                SiteType = "Global",
                ReplicationStatus = new List<int> { 1, 2, 3 },
                DROperationStatus = new List<int> { 1, 2, 3 }
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetServiceTopologyListQuery>(q => q.BusinessServiceId == null), default))
            .ReturnsAsync(topologies);

        // Act
        var result = await _controller.GetBusinessServiceTopologyByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedTopologies = Assert.IsAssignableFrom<List<GetServiceTopologyListVm>>(okResult.Value);
        Assert.Single(returnedTopologies);
        Assert.Equal("Enterprise Global Topology", returnedTopologies.First().SiteName);
        Assert.Equal("Global", returnedTopologies.First().SiteType);
    }

   

    [Fact]
    public async Task GetComponentFailureAnalytics_ReturnsOkResult()
    {
        // Arrange
        var componentFailureAnalytics = _dashboardViewFixture.ComponentFailureAnalyticsDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<ComponentFailureAnalyticsDetailQuery>(), default))
            .ReturnsAsync(componentFailureAnalytics);

        // Act
        var result = await _controller.GetComponentFailureAnalytics();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedAnalytics = Assert.IsType<ComponentFailureAnalyticsDetailVm>(okResult.Value);
        Assert.Equal(150, returnedAnalytics.TotalComponent);
        Assert.Equal(135, returnedAnalytics.AvailableCount);
        Assert.Equal(15, returnedAnalytics.FailedCount);
        Assert.Equal(8, returnedAnalytics.ComponentsAffectedToday);
        Assert.Equal(75, returnedAnalytics.TotalDatabaseCount);
        Assert.Equal(70, returnedAnalytics.DatabaseUpCount);
        Assert.Equal(5, returnedAnalytics.DatabaseDownCount);
        Assert.Equal(50, returnedAnalytics.TotalReplicationCount);
        Assert.Equal(48, returnedAnalytics.ReplicationUpCount);
        Assert.Equal(2, returnedAnalytics.ReplicationDownCount);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_HandlesHighFailureRate()
    {
        // Arrange
        var highFailureAnalytics = new ComponentFailureAnalyticsDetailVm
        {
            TotalComponent = 200,
            AvailableCount = 120,
            FailedCount = 80,
            ComponentsAffectedToday = 45,
            TotalDatabaseCount = 100,
            DatabaseUpCount = 60,
            DatabaseDownCount = 40,
            TotalReplicationCount = 75,
            ReplicationUpCount = 35,
            ReplicationDownCount = 40,
            ServerDtl = new List<Dictionary<string, dynamic>>
            {
                new Dictionary<string, dynamic> { { "ServerName", "Critical-Server-01" }, { "Status", "Failed" }, { "Impact", "High" } },
                new Dictionary<string, dynamic> { { "ServerName", "Critical-Server-02" }, { "Status", "Failed" }, { "Impact", "High" } }
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<ComponentFailureAnalyticsDetailQuery>(), default))
            .ReturnsAsync(highFailureAnalytics);

        // Act
        var result = await _controller.GetComponentFailureAnalytics();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedAnalytics = Assert.IsType<ComponentFailureAnalyticsDetailVm>(okResult.Value);
        Assert.Equal(80, returnedAnalytics.FailedCount);
        Assert.Equal(45, returnedAnalytics.ComponentsAffectedToday);
        Assert.Equal(40, returnedAnalytics.DatabaseDownCount);
        Assert.Equal(40, returnedAnalytics.ReplicationDownCount);
        Assert.Equal(2, returnedAnalytics.ServerDtl.Count);
        Assert.Contains(returnedAnalytics.ServerDtl, s => s.ContainsValue("Critical-Server-01"));
    }

    [Fact]
    public async Task GetDashBoardImpactAvailabilityByBusinessServiceId_ReturnsOkResult()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var impactAvailability = _dashboardViewFixture.DashboardImpactAvailabilityDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetImpactAvailabilityByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(impactAvailability);

        // Act
        var result = await _controller.GetDashBoardImpactAvailabilityByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedImpact = Assert.IsType<DashboardImpactAvailabilityDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Impact Availability Service", returnedImpact.BusinessServiceName);
        Assert.Equal(30, returnedImpact.TotalBusinessFunctionCount);
        Assert.Equal(5, returnedImpact.BusinessFunctionTotalImpacted);
        Assert.Equal(2, returnedImpact.BusinessFunctionMajorImpactCount);
        Assert.Equal(3, returnedImpact.BusinessFunctionPartialImpactCount);
        Assert.Equal(25, returnedImpact.BusinessFunctionUnderRPOCount);
        Assert.Equal(80, returnedImpact.TotalInfraObjectCount);
        Assert.Equal(8, returnedImpact.InfraTotalImpactCount);
        Assert.Equal(3, returnedImpact.InfraMajorImpactCount);
        Assert.Equal(5, returnedImpact.InfraPartialImpactCount);
        Assert.Equal(72, returnedImpact.InfraUnderRPOCount);
    }

    [Fact]
    public async Task GetItViewByBusinessServiceId_ReturnsOkResult()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var itViews = new List<ItViewByBusinessServiceIdVm>
        {
            _dashboardViewFixture.ItViewByBusinessServiceIdVm,
            new ItViewByBusinessServiceIdVm
            {
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = "Enterprise Secondary IT Function",
                Status = "Active",
                InfraObjectDataLag = new List<ItViewInfraObjectList>
                {
                    new ItViewInfraObjectList
                    {
                        InfraObjectId = Guid.NewGuid().ToString(),
                        InfraObjectName = "Enterprise IT Infrastructure 01",
                        DataLagValue = "00:02:30"
                    }
                }
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetItViewByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(itViews);

        // Act
        var result = await _controller.GetItViewByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedViews = Assert.IsAssignableFrom<List<ItViewByBusinessServiceIdVm>>(okResult.Value);
        Assert.Equal(2, returnedViews.Count);
        Assert.Contains(returnedViews, v => v.BusinessFunctionName == "Enterprise IT Business Function");
        Assert.Contains(returnedViews, v => v.BusinessFunctionName == "Enterprise Secondary IT Function");
        Assert.All(returnedViews, v => Assert.Equal("Active", v.Status));
    }

    [Fact]
    public async Task GetOneViewEntityEventList_ReturnsOkResult()
    {
        // Arrange
        var entityEvents = new List<OneViewEntitiesEventView>
        {
            _dashboardViewFixture.OneViewEntitiesEventView,
            new OneViewEntitiesEventView
            {
                Id = 2,
                Entity = "Enterprise Database Entity",
                Message = "Database backup completed successfully",
                LastModifiedDate = DateTime.Now.AddHours(-1),
                LastModifiedBy = "Enterprise Backup System"
            },
            new OneViewEntitiesEventView
            {
                Id = 3,
                Entity = "Enterprise Network Entity",
                Message = "Network configuration updated",
                LastModifiedDate = DateTime.Now.AddMinutes(-30),
                LastModifiedBy = "Enterprise Network Admin"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetOneViewEntitiesEventQuery>(), default))
            .ReturnsAsync(entityEvents);

        // Act
        var result = await _controller.GetOneViewEntityEventList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedEvents = Assert.IsAssignableFrom<List<OneViewEntitiesEventView>>(okResult.Value);
        Assert.Equal(3, returnedEvents.Count);
        Assert.Contains(returnedEvents, e => e.Entity == "Enterprise Entity");
        Assert.Contains(returnedEvents, e => e.Entity == "Enterprise Database Entity");
        Assert.Contains(returnedEvents, e => e.Entity == "Enterprise Network Entity");
        Assert.All(returnedEvents, e => Assert.True(e.LastModifiedDate > DateTime.Now.AddDays(-1)));
    }

    [Fact]
    public async Task GetOneViewRiskmitigationCyberSecurityList_ReturnsOkResult()
    {
        // Arrange
        var cyberSecurityViews = new List<OneViewRiskMitigationCyberSecurityView>
        {
            _dashboardViewFixture.OneViewRiskMitigationCyberSecurityView,
            new OneViewRiskMitigationCyberSecurityView
            {
                Status = "Warning",
                ServerCount = 25,
                StatusScore = 75,
                StatusPercentage = "75.0%",
                HealthContributionPercentage = "15.5%"
            },
            new OneViewRiskMitigationCyberSecurityView
            {
                Status = "Critical",
                ServerCount = 10,
                StatusScore = 45,
                StatusPercentage = "45.0%",
                HealthContributionPercentage = "8.2%"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetOneViewRiskMitigationCyberSecurityQuery>(), default))
            .ReturnsAsync(cyberSecurityViews);

        // Act
        var result = await _controller.GetOneViewRiskmitigationCyberSecurityList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedViews = Assert.IsAssignableFrom<List<OneViewRiskMitigationCyberSecurityView>>(okResult.Value);
        Assert.Equal(3, returnedViews.Count);
        Assert.Contains(returnedViews, v => v.Status == "Secure" && v.StatusScore == 95);
        Assert.Contains(returnedViews, v => v.Status == "Warning" && v.StatusScore == 75);
        Assert.Contains(returnedViews, v => v.Status == "Critical" && v.StatusScore == 45);
        Assert.Equal(80, returnedViews.Sum(v => v.ServerCount)); // 45 + 25 + 10
    }

    [Fact]
    public async Task GetOneViewRiskmitigationFailedDrillList_ReturnsOkResult()
    {
        // Arrange
        var failedDrillViews = new List<OneViewRiskMitigationFailedDrillView>
        {
            _dashboardViewFixture.OneViewRiskMitigationFailedDrillView,
            new OneViewRiskMitigationFailedDrillView
            {
                ActionDate = DateTime.Now.AddDays(-2),
                FailedCount = 5,
                FailedWorkflowOperationGroupIds = "group4,group5,group6,group7,group8"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetOneViewRiskMitigationFailedDrillQuery>(), default))
            .ReturnsAsync(failedDrillViews);

        // Act
        var result = await _controller.GetOneViewRiskmitigationFailedDrillList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedViews = Assert.IsAssignableFrom<List<OneViewRiskMitigationFailedDrillView>>(okResult.Value);
        Assert.Equal(2, returnedViews.Count);
        Assert.Equal(8, returnedViews.Sum(v => v.FailedCount)); // 3 + 5
        Assert.Contains(returnedViews, v => v.FailedWorkflowOperationGroupIds.Contains("group1"));
        Assert.Contains(returnedViews, v => v.FailedWorkflowOperationGroupIds.Contains("group4"));
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_ReturnsOkResult()
    {
        // Arrange
        var operationalAnalytics = _dashboardViewFixture.GetOperationalAvailabilityAnalyticsDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetOperationalAvailabilityAnalyticsQuery>(), default))
            .ReturnsAsync(operationalAnalytics);

        // Act
        var result = await _controller.GetOperationalAvailabilityAnalytics();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedAnalytics = Assert.IsType<GetOperationalAvailabilityAnalyticsDetailVm>(okResult.Value);
        Assert.Equal(25, returnedAnalytics.TotalBusinessServiceCount);
        Assert.Equal(22, returnedAnalytics.BusinessServiceSuccessCount);
        Assert.Equal(3, returnedAnalytics.BusinessServiceErrorCount);
        Assert.Equal(100, returnedAnalytics.TotalInfraObjectCount);
        Assert.Equal(95, returnedAnalytics.InfraObjectSuccessCount);
        Assert.Equal(5, returnedAnalytics.InfraObjectErrorCount);
        Assert.NotNull(returnedAnalytics.SiteRunningListVm);
    }

    [Fact]
    public async Task GetOperationalServiceHealthSummary_ReturnsOkResult()
    {
        // Arrange
        var healthSummary = new List<OperationalHealthSummaryDetailVm>
        {
            _dashboardViewFixture.OperationalHealthSummaryDetailVm,
            new OperationalHealthSummaryDetailVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Secondary Health Service",
                HealthyCount = 70,
                UnHealthyCount = 15,
                MaintenanceCount = 8
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<OperationalHealthSummaryQuery>(), default))
            .ReturnsAsync(healthSummary);

        // Act
        var result = await _controller.GetOperationalServiceHealthSummary();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedSummary = Assert.IsAssignableFrom<List<OperationalHealthSummaryDetailVm>>(okResult.Value);
        Assert.Equal(2, returnedSummary.Count);
        Assert.Equal(155, returnedSummary.Sum(s => s.HealthyCount)); // 85 + 70
        Assert.Equal(25, returnedSummary.Sum(s => s.UnHealthyCount)); // 10 + 15
        Assert.Equal(13, returnedSummary.Sum(s => s.MaintenanceCount)); // 5 + 8
        Assert.Contains(returnedSummary, s => s.BusinessServiceName == "Enterprise Operational Health Service");
    }

    [Fact]
    public async Task GetResilienceHealthStatusByInfraObjectId_ReturnsOkResult()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var resilienceHealth = _dashboardViewFixture.ResilienceHealthStatusDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<ResilienceHealthStatusDetailQuery>(q => q.InfraObjectId == infraObjectId), default))
            .ReturnsAsync(resilienceHealth);

        // Act
        var result = await _controller.GetResilienceHealthStatusByInfraObjectId(infraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedHealth = Assert.IsType<ResilienceHealthStatusDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Resilience Infrastructure", returnedHealth.InfraObjectName);
        Assert.Equal(97.5, returnedHealth.Percentage);
    }

    [Fact]
    public async Task GetSitePropertiesByBusinessServiceId_ReturnsOkResult()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var siteProperties = _dashboardViewFixture.SitePropertiesByBusinessServiceIdVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetSitePropertiesByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(siteProperties);

        // Act
        var result = await _controller.GetSitePropertiesByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedProperties = Assert.IsType<SitePropertiesByBusinessServiceIdVm>(okResult.Value);
        Assert.Contains("site1", returnedProperties.SiteProperties);
        Assert.Contains("Primary Site", returnedProperties.SiteProperties);
        Assert.Contains("DR Site", returnedProperties.SiteProperties);
        Assert.Contains(1, returnedProperties.ReplicationStatus);
        Assert.Contains(2, returnedProperties.ReplicationStatus);
        Assert.Contains(1, returnedProperties.DROperationStatus);
        Assert.Contains(2, returnedProperties.DROperationStatus);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_ReturnsOkResult()
    {
        // Arrange
        var workflowAnalytics = _dashboardViewFixture.GetWorkflowAnalyticsDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetWorkflowAnalyticsQuery>(), default))
            .ReturnsAsync(workflowAnalytics);

        // Act
        var result = await _controller.GetWorkflowAnalytics();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedAnalytics = Assert.IsType<GetWorkflowAnalyticsDetailVm>(okResult.Value);
        Assert.Equal(120, returnedAnalytics.ExecutedWorkFlowCount);
        Assert.Equal(110, returnedAnalytics.WorkFlowSuccessCount);
        Assert.Equal(10, returnedAnalytics.WorkFlowErrorCount);
        Assert.Equal(150, returnedAnalytics.TotalConfigured);
    }

   

    [Fact]
    public async Task GetComponentFailureAnalytics_HandlesZeroFailures()
    {
        // Arrange
        var perfectAnalytics = new ComponentFailureAnalyticsDetailVm
        {
            TotalComponent = 200,
            AvailableCount = 200,
            FailedCount = 0,
            ComponentsAffectedToday = 0,
            TotalDatabaseCount = 50,
            DatabaseUpCount = 50,
            DatabaseDownCount = 0,
            TotalReplicationCount = 30,
            ReplicationUpCount = 30,
            ReplicationDownCount = 0,
            ServerDtl = new List<Dictionary<string, dynamic>>()
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<ComponentFailureAnalyticsDetailQuery>(), default))
            .ReturnsAsync(perfectAnalytics);

        // Act
        var result = await _controller.GetComponentFailureAnalytics();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedAnalytics = Assert.IsType<ComponentFailureAnalyticsDetailVm>(okResult.Value);

        // Validate perfect system state
        Assert.Equal(0, returnedAnalytics.FailedCount);
        Assert.Equal(0, returnedAnalytics.ComponentsAffectedToday);
        Assert.Equal(0, returnedAnalytics.DatabaseDownCount);
        Assert.Equal(0, returnedAnalytics.ReplicationDownCount);
        Assert.Empty(returnedAnalytics.ServerDtl);

        // Validate 100% availability
        Assert.Equal(100.0, (double)returnedAnalytics.AvailableCount / returnedAnalytics.TotalComponent * 100, 1);
        Assert.Equal(100.0, (double)returnedAnalytics.DatabaseUpCount / returnedAnalytics.TotalDatabaseCount * 100, 1);
        Assert.Equal(100.0, (double)returnedAnalytics.ReplicationUpCount / returnedAnalytics.TotalReplicationCount * 100, 1);

        // Validate totals
        Assert.Equal(200, returnedAnalytics.TotalComponent);
        Assert.Equal(200, returnedAnalytics.AvailableCount);
        Assert.Equal(50, returnedAnalytics.TotalDatabaseCount);
        Assert.Equal(50, returnedAnalytics.DatabaseUpCount);
        Assert.Equal(30, returnedAnalytics.TotalReplicationCount);
        Assert.Equal(30, returnedAnalytics.ReplicationUpCount);
    }

    [Fact]
    public async Task GetDashBoardImpactAvailabilityByBusinessServiceId_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidBusinessServiceId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDashBoardImpactAvailabilityByBusinessServiceId(invalidBusinessServiceId));
    }

    [Fact]
    public async Task GetItViewByBusinessServiceId_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidBusinessServiceId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetItViewByBusinessServiceId(invalidBusinessServiceId));
    }

    [Fact]
    public async Task GetResilienceHealthStatusByInfraObjectId_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidInfraObjectId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetResilienceHealthStatusByInfraObjectId(invalidInfraObjectId));
    }

    [Fact]
    public async Task GetSitePropertiesByBusinessServiceId_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidBusinessServiceId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetSitePropertiesByBusinessServiceId(invalidBusinessServiceId));
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_HandlesHighErrorRate()
    {
        // Arrange
        var highErrorAnalytics = _dashboardViewFixture.GetOperationalAvailabilityAnalyticsDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetOperationalAvailabilityAnalyticsQuery>(), default))
            .ReturnsAsync(highErrorAnalytics);

        // Act
        var result = await _controller.GetOperationalAvailabilityAnalytics();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedAnalytics = Assert.IsType<GetOperationalAvailabilityAnalyticsDetailVm>(okResult.Value);

        // Validate fixture values
        Assert.Equal(3, returnedAnalytics.BusinessServiceErrorCount); // Fixture value
        Assert.Equal(5, returnedAnalytics.InfraObjectErrorCount); // Fixture value
        Assert.Equal(25, returnedAnalytics.TotalBusinessServiceCount); // Fixture value
        Assert.Equal(22, returnedAnalytics.BusinessServiceSuccessCount); // Fixture value
        Assert.Equal(100, returnedAnalytics.TotalInfraObjectCount); // Fixture value
        Assert.Equal(95, returnedAnalytics.InfraObjectSuccessCount); // Fixture value

        // Validate site running list (fixture has empty list)
        Assert.NotNull(returnedAnalytics.SiteRunningListVm);
        Assert.IsType<List<SiteRunningListVm>>(returnedAnalytics.SiteRunningListVm);

        // Validate error rates using fixture values
        var businessServiceErrorRate = (double)returnedAnalytics.BusinessServiceErrorCount / returnedAnalytics.TotalBusinessServiceCount * 100;
        var infraObjectErrorRate = (double)returnedAnalytics.InfraObjectErrorCount / returnedAnalytics.TotalInfraObjectCount * 100;

        Assert.Equal(12.0, businessServiceErrorRate, 1); // 3/25 = 12%
        Assert.Equal(5.0, infraObjectErrorRate, 1); // 5/100 = 5%
    }

    [Fact]
    public async Task GetWorkflowAnalytics_HandlesLowSuccessRate()
    {
        // Arrange
        var lowSuccessAnalytics = _dashboardViewFixture.GetWorkflowAnalyticsDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetWorkflowAnalyticsQuery>(), default))
            .ReturnsAsync(lowSuccessAnalytics);

        // Act
        var result = await _controller.GetWorkflowAnalytics();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedAnalytics = Assert.IsType<GetWorkflowAnalyticsDetailVm>(okResult.Value);

        // Validate fixture values
        Assert.Equal(10, returnedAnalytics.WorkFlowErrorCount); // Fixture value
        Assert.Equal(110, returnedAnalytics.WorkFlowSuccessCount); // Fixture value
        Assert.Equal(120, returnedAnalytics.ExecutedWorkFlowCount); // Fixture value
        Assert.Equal(150, returnedAnalytics.TotalConfigured); // Fixture value

        // Validate workflow metrics using fixture values
        var successRate = (double)returnedAnalytics.WorkFlowSuccessCount / returnedAnalytics.ExecutedWorkFlowCount * 100;
        var errorRate = (double)returnedAnalytics.WorkFlowErrorCount / returnedAnalytics.ExecutedWorkFlowCount * 100;
        var executionCoverage = (double)returnedAnalytics.ExecutedWorkFlowCount / returnedAnalytics.TotalConfigured * 100;

        Assert.Equal(91.67, successRate, 2); // 110/120 = 91.67% success rate
        Assert.Equal(8.33, errorRate, 2); // 10/120 = 8.33% error rate
        Assert.Equal(80.0, executionCoverage, 1); // 120/150 = 80% execution coverage
        Assert.Equal(30, returnedAnalytics.TotalConfigured - returnedAnalytics.ExecutedWorkFlowCount); // 30 workflows not executed (150-120)

        // Validate that success + error = executed
        Assert.Equal(returnedAnalytics.ExecutedWorkFlowCount,
                    returnedAnalytics.WorkFlowSuccessCount + returnedAnalytics.WorkFlowErrorCount);
    }

   
  

    [Fact]
    public async Task GetComponentFailureAnalytics_HandlesComplexEnterpriseScenario()
    {
        // Arrange
        var complexEnterpriseAnalytics = new ComponentFailureAnalyticsDetailVm
        {
            TotalComponent = 500,
            AvailableCount = 475,
            FailedCount = 25,
            ComponentsAffectedToday = 12,
            TotalDatabaseCount = 200,
            DatabaseUpCount = 190,
            DatabaseDownCount = 10,
            TotalReplicationCount = 150,
            ReplicationUpCount = 145,
            ReplicationDownCount = 5,
            ServerDtl = new List<Dictionary<string, dynamic>>
            {
                new Dictionary<string, dynamic>
                {
                    { "ServerName", "Enterprise-DB-Primary-01" },
                    { "Status", "Critical" },
                    { "Impact", "High" },
                    { "LastFailure", DateTime.Now.AddHours(-2) },
                    { "FailureType", "Database Connection" }
                },
                new Dictionary<string, dynamic>
                {
                    { "ServerName", "Enterprise-Web-Cluster-03" },
                    { "Status", "Warning" },
                    { "Impact", "Medium" },
                    { "LastFailure", DateTime.Now.AddHours(-1) },
                    { "FailureType", "High CPU Usage" }
                },
                new Dictionary<string, dynamic>
                {
                    { "ServerName", "Enterprise-Replication-Node-02" },
                    { "Status", "Failed" },
                    { "Impact", "High" },
                    { "LastFailure", DateTime.Now.AddMinutes(-30) },
                    { "FailureType", "Replication Lag" }
                }
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<ComponentFailureAnalyticsDetailQuery>(), default))
            .ReturnsAsync(complexEnterpriseAnalytics);

        // Act
        var result = await _controller.GetComponentFailureAnalytics();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedAnalytics = Assert.IsType<ComponentFailureAnalyticsDetailVm>(okResult.Value);

        // Validate overall metrics
        Assert.Equal(500, returnedAnalytics.TotalComponent);
        Assert.Equal(95.0, (double)returnedAnalytics.AvailableCount / returnedAnalytics.TotalComponent * 100, 1); // 95% availability
        Assert.Equal(25, returnedAnalytics.FailedCount);
        Assert.Equal(12, returnedAnalytics.ComponentsAffectedToday);

        // Validate database metrics
        Assert.Equal(200, returnedAnalytics.TotalDatabaseCount);
        Assert.Equal(95.0, (double)returnedAnalytics.DatabaseUpCount / returnedAnalytics.TotalDatabaseCount * 100, 1); // 95% DB availability

        // Validate replication metrics
        Assert.Equal(150, returnedAnalytics.TotalReplicationCount);
        Assert.Equal(96.67, (double)returnedAnalytics.ReplicationUpCount / returnedAnalytics.TotalReplicationCount * 100, 2); // 96.67% replication availability

        // Validate server details
        Assert.Equal(3, returnedAnalytics.ServerDtl.Count);
        Assert.Contains(returnedAnalytics.ServerDtl, s => s.ContainsValue("Enterprise-DB-Primary-01"));
        Assert.Contains(returnedAnalytics.ServerDtl, s => s.ContainsValue("Database Connection"));
        Assert.Contains(returnedAnalytics.ServerDtl, s => s.ContainsValue("Replication Lag"));
    }

    [Fact]
    public async Task GetDashBoardImpactAvailabilityByBusinessServiceId_HandlesMultiTierArchitecture()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var multiTierImpact = new DashboardImpactAvailabilityDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            BusinessServiceId = businessServiceId,
            BusinessServiceName = "Enterprise Multi-Tier E-Commerce Platform",
            TotalBusinessFunctionCount = 50,
            BusinessFunctionTotalImpacted = 12,
            BusinessFunctionMajorImpactCount = 4,
            BusinessFunctionPartialImpactCount = 8,
            BusinessFunctionUnderRPOCount = 38,
            TotalInfraObjectCount = 200,
            InfraTotalImpactCount = 25,
            InfraMajorImpactCount = 8,
            InfraPartialImpactCount = 17,
            InfraUnderRPOCount = 175
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetImpactAvailabilityByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(multiTierImpact);

        // Act
        var result = await _controller.GetDashBoardImpactAvailabilityByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedImpact = Assert.IsType<DashboardImpactAvailabilityDetailVm>(okResult.Value);

        // Validate business function metrics
        Assert.Equal(50, returnedImpact.TotalBusinessFunctionCount);
        Assert.Equal(24.0, (double)returnedImpact.BusinessFunctionTotalImpacted / returnedImpact.TotalBusinessFunctionCount * 100, 1); // 24% impacted
        Assert.Equal(76.0, (double)returnedImpact.BusinessFunctionUnderRPOCount / returnedImpact.TotalBusinessFunctionCount * 100, 1); // 76% under RPO

        // Validate infrastructure metrics
        Assert.Equal(200, returnedImpact.TotalInfraObjectCount);
        Assert.Equal(12.5, (double)returnedImpact.InfraTotalImpactCount / returnedImpact.TotalInfraObjectCount * 100, 1); // 12.5% impacted
        Assert.Equal(87.5, (double)returnedImpact.InfraUnderRPOCount / returnedImpact.TotalInfraObjectCount * 100, 1); // 87.5% under RPO

        // Validate impact distribution
        Assert.Equal(returnedImpact.BusinessFunctionTotalImpacted,
                    returnedImpact.BusinessFunctionMajorImpactCount + returnedImpact.BusinessFunctionPartialImpactCount);
        Assert.Equal(returnedImpact.InfraTotalImpactCount,
                    returnedImpact.InfraMajorImpactCount + returnedImpact.InfraPartialImpactCount);

        Assert.Contains("Multi-Tier", returnedImpact.BusinessServiceName);
        Assert.Contains("E-Commerce", returnedImpact.BusinessServiceName);
    }

    [Fact]
    public async Task GetItViewByBusinessServiceId_HandlesGlobalDistributedInfrastructure()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var globalItViews = new List<ItViewByBusinessServiceIdVm>
        {
            new ItViewByBusinessServiceIdVm
            {
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = "Enterprise Global Payment Processing",
                Status = "Active",
                InfraObjectDataLag = new List<ItViewInfraObjectList>
                {
                    new ItViewInfraObjectList
                    {
                        InfraObjectId = Guid.NewGuid().ToString(),
                        InfraObjectName = "US-East Payment Gateway Cluster",
                        DataLagValue = "00:00:15"
                    },
                    new ItViewInfraObjectList
                    {
                        InfraObjectId = Guid.NewGuid().ToString(),
                        InfraObjectName = "EU-West Payment Gateway Cluster",
                        DataLagValue = "00:00:25"
                    },
                    new ItViewInfraObjectList
                    {
                        InfraObjectId = Guid.NewGuid().ToString(),
                        InfraObjectName = "APAC Payment Gateway Cluster",
                        DataLagValue = "00:00:35"
                    }
                }
            },
            new ItViewByBusinessServiceIdVm
            {
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = "Enterprise Global Fraud Detection",
                Status = "Active",
                InfraObjectDataLag = new List<ItViewInfraObjectList>
                {
                    new ItViewInfraObjectList
                    {
                        InfraObjectId = Guid.NewGuid().ToString(),
                        InfraObjectName = "Global AI Fraud Detection Engine",
                        DataLagValue = "00:00:05"
                    },
                    new ItViewInfraObjectList
                    {
                        InfraObjectId = Guid.NewGuid().ToString(),
                        InfraObjectName = "Real-time Risk Assessment Platform",
                        DataLagValue = "00:00:08"
                    }
                }
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetItViewByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(globalItViews);

        // Act
        var result = await _controller.GetItViewByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedViews = Assert.IsAssignableFrom<List<ItViewByBusinessServiceIdVm>>(okResult.Value);

        Assert.Equal(2, returnedViews.Count);
        Assert.All(returnedViews, v => Assert.Equal("Active", v.Status));

        // Validate payment processing function
        var paymentFunction = returnedViews.First(v => v.BusinessFunctionName.Contains("Payment Processing"));
        Assert.Equal(3, paymentFunction.InfraObjectDataLag.Count);
        Assert.Contains(paymentFunction.InfraObjectDataLag, i => i.InfraObjectName.Contains("US-East"));
        Assert.Contains(paymentFunction.InfraObjectDataLag, i => i.InfraObjectName.Contains("EU-West"));
        Assert.Contains(paymentFunction.InfraObjectDataLag, i => i.InfraObjectName.Contains("APAC"));

        // Validate fraud detection function
        var fraudFunction = returnedViews.First(v => v.BusinessFunctionName.Contains("Fraud Detection"));
        Assert.Equal(2, fraudFunction.InfraObjectDataLag.Count);
        Assert.Contains(fraudFunction.InfraObjectDataLag, i => i.InfraObjectName.Contains("AI Fraud Detection"));
        Assert.Contains(fraudFunction.InfraObjectDataLag, i => i.InfraObjectName.Contains("Risk Assessment"));

        // Validate data lag performance
        var allDataLags = returnedViews.SelectMany(v => v.InfraObjectDataLag).ToList();
        Assert.Equal(5, allDataLags.Count);
        Assert.All(allDataLags, lag => Assert.True(TimeSpan.Parse(lag.DataLagValue) < TimeSpan.FromMinutes(1))); // All under 1 minute
    }

    

    [Fact]
    public async Task GetOneViewEntityEventList_HandlesRealTimeEventStream()
    {
        // Arrange
        var realTimeEvents = new List<OneViewEntitiesEventView>
        {
            new OneViewEntitiesEventView
            {
                Id = 1,
                Entity = "Enterprise Payment Gateway",
                Message = "High-volume transaction processing initiated - 50,000 TPS",
                LastModifiedDate = DateTime.Now.AddMinutes(-5),
                LastModifiedBy = "Enterprise Payment System"
            },
            new OneViewEntitiesEventView
            {
                Id = 2,
                Entity = "Enterprise Security Engine",
                Message = "Advanced threat detected and mitigated - IP blocked: *************",
                LastModifiedDate = DateTime.Now.AddMinutes(-3),
                LastModifiedBy = "Enterprise Security AI"
            },
            new OneViewEntitiesEventView
            {
                Id = 3,
                Entity = "Enterprise Database Cluster",
                Message = "Automatic failover completed successfully - Primary to Secondary in 2.3 seconds",
                LastModifiedDate = DateTime.Now.AddMinutes(-2),
                LastModifiedBy = "Enterprise HA Manager"
            },
            new OneViewEntitiesEventView
            {
                Id = 4,
                Entity = "Enterprise Load Balancer",
                Message = "Traffic spike detected - Auto-scaling triggered: 5 -> 12 instances",
                LastModifiedDate = DateTime.Now.AddMinutes(-1),
                LastModifiedBy = "Enterprise Auto-Scaler"
            },
            new OneViewEntitiesEventView
            {
                Id = 5,
                Entity = "Enterprise Monitoring System",
                Message = "Performance optimization completed - 15% latency reduction achieved",
                LastModifiedDate = DateTime.Now.AddSeconds(-30),
                LastModifiedBy = "Enterprise Performance Optimizer"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetOneViewEntitiesEventQuery>(), default))
            .ReturnsAsync(realTimeEvents);

        // Act
        var result = await _controller.GetOneViewEntityEventList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedEvents = Assert.IsAssignableFrom<List<OneViewEntitiesEventView>>(okResult.Value);

        Assert.Equal(5, returnedEvents.Count);
        Assert.All(returnedEvents, e => Assert.True(e.LastModifiedDate > DateTime.Now.AddMinutes(-10)));

        // Validate specific events
        Assert.Contains(returnedEvents, e => e.Entity == "Enterprise Payment Gateway" && e.Message.Contains("50,000 TPS"));
        Assert.Contains(returnedEvents, e => e.Entity == "Enterprise Security Engine" && e.Message.Contains("threat detected"));
        Assert.Contains(returnedEvents, e => e.Entity == "Enterprise Database Cluster" && e.Message.Contains("failover"));
        Assert.Contains(returnedEvents, e => e.Entity == "Enterprise Load Balancer" && e.Message.Contains("Auto-scaling"));
        Assert.Contains(returnedEvents, e => e.Entity == "Enterprise Monitoring System" && e.Message.Contains("optimization"));

        // Validate chronological order (most recent events)
        var sortedEvents = returnedEvents.OrderByDescending(e => e.LastModifiedDate).ToList();
        Assert.Equal("Enterprise Monitoring System", sortedEvents.First().Entity);
        Assert.Equal("Enterprise Payment Gateway", sortedEvents.Last().Entity);
    }

    [Fact]
    public async Task GetOneViewRiskmitigationCyberSecurityList_HandlesAdvancedThreatLandscape()
    {
        // Arrange
        var advancedCyberSecurityViews = new List<OneViewRiskMitigationCyberSecurityView>
        {
            new OneViewRiskMitigationCyberSecurityView
            {
                Status = "Secure",
                ServerCount = 150,
                StatusScore = 98,
                StatusPercentage = "98.5%",
                HealthContributionPercentage = "25.2%"
            },
            new OneViewRiskMitigationCyberSecurityView
            {
                Status = "Monitoring",
                ServerCount = 75,
                StatusScore = 85,
                StatusPercentage = "85.0%",
                HealthContributionPercentage = "18.7%"
            },
            new OneViewRiskMitigationCyberSecurityView
            {
                Status = "Warning",
                ServerCount = 45,
                StatusScore = 72,
                StatusPercentage = "72.3%",
                HealthContributionPercentage = "12.1%"
            },
            new OneViewRiskMitigationCyberSecurityView
            {
                Status = "Critical",
                ServerCount = 15,
                StatusScore = 45,
                StatusPercentage = "45.0%",
                HealthContributionPercentage = "5.8%"
            },
            new OneViewRiskMitigationCyberSecurityView
            {
                Status = "Compromised",
                ServerCount = 5,
                StatusScore = 15,
                StatusPercentage = "15.0%",
                HealthContributionPercentage = "2.1%"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetOneViewRiskMitigationCyberSecurityQuery>(), default))
            .ReturnsAsync(advancedCyberSecurityViews);

        // Act
        var result = await _controller.GetOneViewRiskmitigationCyberSecurityList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedViews = Assert.IsAssignableFrom<List<OneViewRiskMitigationCyberSecurityView>>(okResult.Value);

        Assert.Equal(5, returnedViews.Count);
        Assert.Equal(290, returnedViews.Sum(v => v.ServerCount)); // Total servers across all security levels

        // Validate security distribution
        var secureServers = returnedViews.Where(v => v.Status == "Secure").Sum(v => v.ServerCount);
        var criticalServers = returnedViews.Where(v => v.Status == "Critical" || v.Status == "Compromised").Sum(v => v.ServerCount);

        Assert.Equal(150, secureServers);
        Assert.Equal(20, criticalServers); // 15 + 5
        Assert.True(secureServers > criticalServers * 7); // Secure servers should be at least 7x critical servers

        // Validate score correlation with status
        var secureView = returnedViews.First(v => v.Status == "Secure");
        var compromisedView = returnedViews.First(v => v.Status == "Compromised");

        Assert.True(secureView.StatusScore > compromisedView.StatusScore * 6); // Secure should be 6x+ compromised score
        Assert.True(secureView.HealthContributionPercentage.Replace("%", "") != compromisedView.HealthContributionPercentage.Replace("%", ""));

        // Validate overall security posture
        var weightedScore = returnedViews.Sum(v => v.StatusScore * v.ServerCount) / (double)returnedViews.Sum(v => v.ServerCount);
        Assert.True(weightedScore > 80); // Overall weighted score should be above 80
    }

  
    [Fact]
    public async Task GetOperationalServiceHealthSummary_HandlesEnterpriseServicePortfolio()
    {
        // Arrange
        var enterpriseHealthSummary = new List<OperationalHealthSummaryDetailVm>
        {
            new OperationalHealthSummaryDetailVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Core Banking Platform",
                HealthyCount = 180,
                UnHealthyCount = 15,
                MaintenanceCount = 5
            },
            new OperationalHealthSummaryDetailVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Digital Payment Gateway",
                HealthyCount = 145,
                UnHealthyCount = 8,
                MaintenanceCount = 7
            },
            new OperationalHealthSummaryDetailVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Customer Relationship Management",
                HealthyCount = 120,
                UnHealthyCount = 12,
                MaintenanceCount = 8
            },
            new OperationalHealthSummaryDetailVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Risk Management System",
                HealthyCount = 95,
                UnHealthyCount = 5,
                MaintenanceCount = 10
            },
            new OperationalHealthSummaryDetailVm
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Enterprise Business Intelligence Platform",
                HealthyCount = 85,
                UnHealthyCount = 10,
                MaintenanceCount = 5
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<OperationalHealthSummaryQuery>(), default))
            .ReturnsAsync(enterpriseHealthSummary);

        // Act
        var result = await _controller.GetOperationalServiceHealthSummary();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedSummary = Assert.IsAssignableFrom<List<OperationalHealthSummaryDetailVm>>(okResult.Value);

        Assert.Equal(5, returnedSummary.Count);

        // Validate overall portfolio health
        var totalHealthy = returnedSummary.Sum(s => s.HealthyCount);
        var totalUnhealthy = returnedSummary.Sum(s => s.UnHealthyCount);
        var totalMaintenance = returnedSummary.Sum(s => s.MaintenanceCount);
        var totalComponents = totalHealthy + totalUnhealthy + totalMaintenance;

        Assert.Equal(625, totalHealthy);
        Assert.Equal(50, totalUnhealthy);
        Assert.Equal(35, totalMaintenance);
        Assert.Equal(710, totalComponents);

        // Validate health percentages
        var healthPercentage = (double)totalHealthy / totalComponents * 100;
        var unhealthyPercentage = (double)totalUnhealthy / totalComponents * 100;

        Assert.True(healthPercentage > 85); // Should be above 85% healthy
        Assert.True(unhealthyPercentage < 10); // Should be below 10% unhealthy

        // Validate critical services
        var coreBanking = returnedSummary.First(s => s.BusinessServiceName.Contains("Core Banking"));
        var paymentGateway = returnedSummary.First(s => s.BusinessServiceName.Contains("Payment Gateway"));

        Assert.True(coreBanking.HealthyCount > 150); // Core banking should have high healthy count
        Assert.True(paymentGateway.HealthyCount > 140); // Payment gateway should have high healthy count

        // Validate service-specific health ratios
        foreach (var service in returnedSummary)
        {
            var serviceTotal = service.HealthyCount + service.UnHealthyCount + service.MaintenanceCount;
            var serviceHealthRatio = (double)service.HealthyCount / serviceTotal;
            Assert.True(serviceHealthRatio > 0.8); // Each service should be at least 80% healthy
        }
    }


    [Fact]
    public async Task GetOneViewRiskmitigationFailedDrillList_HandlesComplexDRScenarios()
    {
        // Arrange
        var complexFailedDrillViews = new List<OneViewRiskMitigationFailedDrillView>
        {
            new OneViewRiskMitigationFailedDrillView
            {
                ActionDate = DateTime.Now.AddDays(-1),
                FailedCount = 8,
                FailedWorkflowOperationGroupIds = "banking-core-failover,payment-gateway-switch,database-cluster-recovery,network-rerouting"
            },
            new OneViewRiskMitigationFailedDrillView
            {
                ActionDate = DateTime.Now.AddDays(-3),
                FailedCount = 12,
                FailedWorkflowOperationGroupIds = "datacenter-evacuation,cross-region-failover,backup-restoration,communication-systems,security-protocols,compliance-validation"
            },
            new OneViewRiskMitigationFailedDrillView
            {
                ActionDate = DateTime.Now.AddDays(-7),
                FailedCount = 5,
                FailedWorkflowOperationGroupIds = "cyber-incident-response,threat-isolation,forensic-analysis"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetOneViewRiskMitigationFailedDrillQuery>(), default))
            .ReturnsAsync(complexFailedDrillViews);

        // Act
        var result = await _controller.GetOneViewRiskmitigationFailedDrillList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedViews = Assert.IsAssignableFrom<List<OneViewRiskMitigationFailedDrillView>>(okResult.Value);

        Assert.Equal(3, returnedViews.Count);
        Assert.Equal(25, returnedViews.Sum(v => v.FailedCount)); // Total failed drills: 8 + 12 + 5

        // Validate drill complexity and scenarios
        Assert.Contains(returnedViews, v => v.FailedWorkflowOperationGroupIds.Contains("banking-core-failover"));
        Assert.Contains(returnedViews, v => v.FailedWorkflowOperationGroupIds.Contains("datacenter-evacuation"));
        Assert.Contains(returnedViews, v => v.FailedWorkflowOperationGroupIds.Contains("cyber-incident-response"));

        // Validate most recent drill
        var mostRecentDrill = returnedViews.OrderByDescending(v => v.ActionDate).First();
        Assert.Equal(8, mostRecentDrill.FailedCount);
        Assert.Contains("payment-gateway-switch", mostRecentDrill.FailedWorkflowOperationGroupIds);

        // Validate largest failure scenario
        var largestFailure = returnedViews.OrderByDescending(v => v.FailedCount).First();
        Assert.Equal(12, largestFailure.FailedCount);
        Assert.Contains("cross-region-failover", largestFailure.FailedWorkflowOperationGroupIds);
        Assert.Contains("compliance-validation", largestFailure.FailedWorkflowOperationGroupIds);

        // Validate timeline distribution
        Assert.All(returnedViews, v => Assert.True(v.ActionDate > DateTime.Now.AddDays(-10)));
        Assert.All(returnedViews, v => Assert.True(v.FailedCount > 0));
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_HandlesCriticalSystemFailure()
    {
        // Arrange
        var criticalFailureAnalytics = _dashboardViewFixture.ComponentFailureAnalyticsDetailVm;
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<ComponentFailureAnalyticsDetailQuery>(), default))
            .ReturnsAsync(criticalFailureAnalytics);

        // Act
        var result = await _controller.GetComponentFailureAnalytics();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedAnalytics = Assert.IsType<ComponentFailureAnalyticsDetailVm>(okResult.Value);

        // Validate fixture-based metrics (using actual fixture values)
        Assert.Equal(90.0, (double)returnedAnalytics.AvailableCount / returnedAnalytics.TotalComponent * 100, 1); // 135/150 = 90% availability
        Assert.Equal(10.0, (double)returnedAnalytics.FailedCount / returnedAnalytics.TotalComponent * 100, 1); // 15/150 = 10% failure rate
        Assert.Equal(5.33, (double)returnedAnalytics.ComponentsAffectedToday / returnedAnalytics.TotalComponent * 100, 2); // 8/150 = 5.33% affected today

        // Validate database impact (using fixture values)
        Assert.Equal(93.33, (double)returnedAnalytics.DatabaseUpCount / returnedAnalytics.TotalDatabaseCount * 100, 2); // 70/75 = 93.33% DB availability
        Assert.Equal(6.67, (double)returnedAnalytics.DatabaseDownCount / returnedAnalytics.TotalDatabaseCount * 100, 2); // 5/75 = 6.67% DB failure

        // Validate replication impact (using fixture values)
        Assert.Equal(96.0, (double)returnedAnalytics.ReplicationUpCount / returnedAnalytics.TotalReplicationCount * 100, 1); // 48/50 = 96% replication availability
        Assert.Equal(4.0, (double)returnedAnalytics.ReplicationDownCount / returnedAnalytics.TotalReplicationCount * 100, 1); // 2/50 = 4% replication failure

        // Validate fixture values directly
        Assert.Equal(150, returnedAnalytics.TotalComponent);
        Assert.Equal(135, returnedAnalytics.AvailableCount);
        Assert.Equal(15, returnedAnalytics.FailedCount);
        Assert.Equal(8, returnedAnalytics.ComponentsAffectedToday);
        Assert.Equal(75, returnedAnalytics.TotalDatabaseCount);
        Assert.Equal(70, returnedAnalytics.DatabaseUpCount);
        Assert.Equal(5, returnedAnalytics.DatabaseDownCount);
        Assert.Equal(50, returnedAnalytics.TotalReplicationCount);
        Assert.Equal(48, returnedAnalytics.ReplicationUpCount);
        Assert.Equal(2, returnedAnalytics.ReplicationDownCount);

        // Validate server details (fixture has empty list)
        Assert.NotNull(returnedAnalytics.ServerDtl);
        Assert.IsType<List<Dictionary<string, dynamic>>>(returnedAnalytics.ServerDtl);
    }

    [Fact]
    public async Task GetDashBoardImpactAvailabilityByBusinessServiceId_HandlesZeroImpactScenario()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var zeroImpact = new DashboardImpactAvailabilityDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            BusinessServiceId = businessServiceId,
            BusinessServiceName = "Enterprise Perfect Availability Service",
            TotalBusinessFunctionCount = 25,
            BusinessFunctionTotalImpacted = 0,
            BusinessFunctionMajorImpactCount = 0,
            BusinessFunctionPartialImpactCount = 0,
            BusinessFunctionUnderRPOCount = 25,
            TotalInfraObjectCount = 100,
            InfraTotalImpactCount = 0,
            InfraMajorImpactCount = 0,
            InfraPartialImpactCount = 0,
            InfraUnderRPOCount = 100
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetImpactAvailabilityByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(zeroImpact);

        // Act
        var result = await _controller.GetDashBoardImpactAvailabilityByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedImpact = Assert.IsType<DashboardImpactAvailabilityDetailVm>(okResult.Value);

        // Validate perfect availability
        Assert.Equal(0, returnedImpact.BusinessFunctionTotalImpacted);
        Assert.Equal(0, returnedImpact.InfraTotalImpactCount);
        Assert.Equal(100.0, (double)returnedImpact.BusinessFunctionUnderRPOCount / returnedImpact.TotalBusinessFunctionCount * 100, 1); // 100% under RPO
        Assert.Equal(100.0, (double)returnedImpact.InfraUnderRPOCount / returnedImpact.TotalInfraObjectCount * 100, 1); // 100% under RPO

        // Validate zero impact counts
        Assert.Equal(0, returnedImpact.BusinessFunctionMajorImpactCount);
        Assert.Equal(0, returnedImpact.BusinessFunctionPartialImpactCount);
        Assert.Equal(0, returnedImpact.InfraMajorImpactCount);
        Assert.Equal(0, returnedImpact.InfraPartialImpactCount);

        // Validate impact distribution (should be zero)
        Assert.Equal(returnedImpact.BusinessFunctionTotalImpacted,
                    returnedImpact.BusinessFunctionMajorImpactCount + returnedImpact.BusinessFunctionPartialImpactCount);
        Assert.Equal(returnedImpact.InfraTotalImpactCount,
                    returnedImpact.InfraMajorImpactCount + returnedImpact.InfraPartialImpactCount);

        Assert.Contains("Perfect Availability", returnedImpact.BusinessServiceName);
    }

    [Fact]
    public async Task GetItViewByBusinessServiceId_HandlesEmptyInfrastructureList()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var emptyInfraViews = new List<ItViewByBusinessServiceIdVm>
        {
            new ItViewByBusinessServiceIdVm
            {
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = "Enterprise Minimal Function",
                Status = "Active",
                InfraObjectDataLag = new List<ItViewInfraObjectList>() // Empty infrastructure list
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetItViewByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(emptyInfraViews);

        // Act
        var result = await _controller.GetItViewByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedViews = Assert.IsAssignableFrom<List<ItViewByBusinessServiceIdVm>>(okResult.Value);

        Assert.Single(returnedViews);
        Assert.Equal("Active", returnedViews.First().Status);
        Assert.Empty(returnedViews.First().InfraObjectDataLag);
        Assert.Equal("Enterprise Minimal Function", returnedViews.First().BusinessFunctionName);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_HandlesZeroErrorScenario()
    {
        // Arrange
        var perfectOperationalAnalytics = new GetOperationalAvailabilityAnalyticsDetailVm
        {
            TotalBusinessServiceCount = 75,
            BusinessServiceSuccessCount = 75,
            BusinessServiceErrorCount = 0,
            TotalInfraObjectCount = 300,
            InfraObjectSuccessCount = 300,
            InfraObjectErrorCount = 0,
            SiteRunningListVm = new List<SiteRunningListVm>
            {
                new SiteRunningListVm { SiteType = "Primary Production", TotalRunningCount = 200 },
                new SiteRunningListVm { SiteType = "Secondary Production", TotalRunningCount = 150 },
                new SiteRunningListVm { SiteType = "DR Site", TotalRunningCount = 100 }
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetOperationalAvailabilityAnalyticsQuery>(), default))
            .ReturnsAsync(perfectOperationalAnalytics);

        // Act
        var result = await _controller.GetOperationalAvailabilityAnalytics();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedAnalytics = Assert.IsType<GetOperationalAvailabilityAnalyticsDetailVm>(okResult.Value);

        // Validate perfect availability
        Assert.Equal(100.0, (double)returnedAnalytics.BusinessServiceSuccessCount / returnedAnalytics.TotalBusinessServiceCount * 100, 1); // 100% business service success
        Assert.Equal(100.0, (double)returnedAnalytics.InfraObjectSuccessCount / returnedAnalytics.TotalInfraObjectCount * 100, 1); // 100% infrastructure success
        Assert.Equal(0, returnedAnalytics.BusinessServiceErrorCount);
        Assert.Equal(0, returnedAnalytics.InfraObjectErrorCount);

        // Validate site distribution
        Assert.Equal(3, returnedAnalytics.SiteRunningListVm.Count);
        Assert.Equal(450, returnedAnalytics.SiteRunningListVm.Sum(s => s.TotalRunningCount)); // Total running: 200 + 150 + 100

        // Validate site hierarchy
        var primarySite = returnedAnalytics.SiteRunningListVm.First(s => s.SiteType.Contains("Primary"));
        var secondarySite = returnedAnalytics.SiteRunningListVm.First(s => s.SiteType.Contains("Secondary"));
        var drSite = returnedAnalytics.SiteRunningListVm.First(s => s.SiteType.Contains("DR"));

        Assert.True(primarySite.TotalRunningCount > secondarySite.TotalRunningCount);
        Assert.True(secondarySite.TotalRunningCount > drSite.TotalRunningCount);

        // Validate perfect metrics
        Assert.Equal(75, returnedAnalytics.TotalBusinessServiceCount);
        Assert.Equal(75, returnedAnalytics.BusinessServiceSuccessCount);
        Assert.Equal(300, returnedAnalytics.TotalInfraObjectCount);
        Assert.Equal(300, returnedAnalytics.InfraObjectSuccessCount);
    }

    
}
