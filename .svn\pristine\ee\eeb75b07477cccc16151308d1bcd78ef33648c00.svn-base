QUnit.module("AccessManager - Functions", hooks => {
    let $fixture;

    hooks.beforeEach(() => {
        $fixture = $("#qunit-fixture");
        $fixture.html(`
            <div id="treeList-error"></div>
            <div id="selectUserRole-error"></div>
            <button id="btnAccSave"></button>
            <input type="checkbox" class="form-check-input" id="chkMonitor">
        `);
    });
    function validateDropDown(value, errorMessage, errorElement) {
        if (!value) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
    function anyCheckboxChecked() {
        return $('.form-check-input').is(':checked');
    };
    function updateCheckboxByRole(role) {
        let checkboxes = $('input[type="checkbox"]'),
            disableAll = ["SuperAdmin", "Administrator", "Operator", "Manager"].includes(role);
        checkboxes.toggleClass('opacity-80', disableAll);
        checkboxes.prop({ 'checked': !disableAll, 'disabled': disableAll });
        $("#btnAccSave").prop("disabled", true);
    };
    function getCheckedCategories(data) {
        let count = 0;
        for (const category in data.Permissions) {
            const permissions = data.Permissions[category];
            const categoryChecked = Object.values(permissions).some(checkbox => checkbox);
            if (categoryChecked) {
                count++;
            }
        }
        const errorElement = document.getElementById('treeList-error');
        if (count === 0) {
            errorElement.textContent = 'Please check at least one checkbox';
            errorElement.classList.add('field-validation-error');
            return false;
        } else {
            errorElement.textContent = '';
            errorElement.classList.remove('field-validation-error');
            return true;
        }
    }

    QUnit.test("updateCheckboxByRole disables checkboxes for restricted roles", assert => {
        const role = "SuperAdmin";
        let checkboxes = $('<input type="checkbox"><input type="checkbox">');
        $fixture.append(checkboxes);
        updateCheckboxByRole(role);
        checkboxes.each(function () {
            assert.ok($(this).prop('disabled'), "Checkbox is disabled");
        });
    });

    QUnit.test("validateDropDown returns false for empty value", assert => {
        let result = validateDropDown('', "Please select", $('#selectUserRole-error'));
        assert.strictEqual(result, false, "Validation fails on empty input");
    });

    QUnit.test("validateDropDown returns true for valid input", assert => {
        let result = validateDropDown('Role', "Please select", $('#selectUserRole-error'));
        assert.strictEqual(result, true, "Validation passes for valid input");
    });

    QUnit.test("anyCheckboxChecked detects a checked checkbox", assert => {
        $('#chkMonitor').prop('checked', true);
        assert.ok(anyCheckboxChecked(), "Checkbox is checked");
    });

    QUnit.test("anyCheckboxChecked returns false when none checked", assert => {
        $('#chkMonitor').prop('checked', false);
        assert.notOk(anyCheckboxChecked(), "No checkboxes are checked");
    });

    QUnit.test("getCheckedCategories returns false when none checked", assert => {
        const mockData = { Permissions: { Dashboard: { View: false } } };
        let result = getCheckedCategories(mockData);
        assert.false(result, "Returns false when all checkboxes are false");
    });

    QUnit.test("getCheckedCategories returns true when at least one is checked", assert => {
        const mockData = { Permissions: { Dashboard: { View: true } } };
        let result = getCheckedCategories(mockData);
        assert.true(result, "Returns true when at least one is checked");
    });
});

QUnit.module("AccessManager - Events", hooks => {
    let $fixture;

    hooks.beforeEach(() => {
        $fixture = $("#qunit-fixture");
        $fixture.html(`
            <select id="txtUserRoleName">
                <option id="1">SuperAdmin</option>
                <option id="2">CustomRole</option>
            </select>
            <input type="checkbox" class="form-check-input" id="chkMonitor">
            <button id="btnAccSave"></button>
            <button id="btnCancel"></button>
            <div id="selectUserRole-error"></div>
            <div id="treeList-error"></div>
            <div id="AdminCreate" data-create-permission="false"></div>
        `);
    });

    QUnit.test("#txtUserRoleName change updates UI", assert => {
        $("#txtUserRoleName").val("CustomRole");
        $("#txtUserRoleName").trigger("change");
        assert.ok(true, "Dropdown change triggered successfully (UI logic tested visually)");
    });

    QUnit.test("#btnCancel click resets form", assert => {
        $("#txtUserRoleName").val("CustomRole");
        $("#btnCancel").trigger("click");
        assert.ok(true, "Cancel button clicked and handled");
    });

    QUnit.test(".form-check-input change toggles Save/Cancel buttons", assert => {
        $('#chkMonitor').trigger("change");
        assert.ok(true, "Checkbox change triggered successfully");
    });
});
