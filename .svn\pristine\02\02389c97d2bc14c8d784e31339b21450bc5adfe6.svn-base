﻿using ContinuityPatrol.Domain.ViewModels.WorkflowOperationModel;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IWorkflowOperationGroupRepository : IRepository<WorkflowOperationGroup>
{
    Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupNames();
    Task<bool> IsWorkflowOperationGroupNameExist(string name, string id);
    Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByWorkflowOperationId(string id);
    Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByWorkflowOperationIdByReport(string id);

    Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByWorkflowOperationIdAndNodeId(
        string workflowOperationId, string nodeId);

    Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByInfraObjectId(string infraObjectId);
    Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByBusinessServiceId(string businessServiceId);

    Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupListByWorkflowId(string workflowId,
        string workflowOperationId);

    Task<List<WorkflowOperationGroup>> GetWorkflowOperationByWorkflowOperationId(string workflowOperationId);

    Task<List<WorkflowOperationGroup>> GetOperationGroupByWorkflowOperationIds(List<string> workflowOperationIds);
    Task<IReadOnlyList<WorkflowOperationGroup>> ListAllAsyncForDashBoardView();

    Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByWorkflowOperationIds(List<string> workflowOperationIds);
    Task<ProfileExecutorByBusinessServiceIdVm> GetDrillProfileCountByBusinessServiceId(string businessServiceId);
    Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByWorkflowOperationIdAndWorkflowIds(List<string> workflowIds, string workflowOperationId);

    Task<bool> IsWorkflowIdExist(string workflowId);
}