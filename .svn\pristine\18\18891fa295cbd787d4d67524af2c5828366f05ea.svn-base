using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Web.Middlewares;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Moq;
using System.Security.Claims;
using System.Text;
using Xunit;
using Microsoft.AspNetCore.Builder;

namespace ContinuityPatrol.Web.UnitTests.Middlewares;

public class PreventMultipleLoginMiddlewareTests
{
    private readonly Mock<RequestDelegate> _nextMock;
    private readonly Mock<ILogger<PreventMultipleLoginMiddleware>> _loggerMock;
    private readonly Mock<IDistributedCache> _cacheMock;
    private readonly PreventMultipleLoginMiddleware _middleware;
    private readonly DefaultHttpContext _context;

    public PreventMultipleLoginMiddlewareTests()
    {
        _nextMock = new Mock<RequestDelegate>();
        _loggerMock = new Mock<ILogger<PreventMultipleLoginMiddleware>>();
        _cacheMock = new Mock<IDistributedCache>();
        _middleware = new PreventMultipleLoginMiddleware(_nextMock.Object, _loggerMock.Object, _cacheMock.Object);
        _context = new DefaultHttpContext();
        _context.Response.Body = new MemoryStream();
    }

    [Theory]
    [InlineData("/error")]
    [InlineData("/Error")]
    [InlineData("/error/404")]
    public async Task Invoke_WithErrorPath_ShouldCallNext(string path)
    {
        // Arrange
        _context.Request.Path = path;

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _nextMock.Verify(next => next(_context), Times.Once);
        _cacheMock.Verify(cache => cache.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory]
    [InlineData("/api/users")]
    [InlineData("/API/data")]
    [InlineData("/api/test")]
    public async Task Invoke_WithApiPath_ShouldCallNext(string path)
    {
        // Arrange
        _context.Request.Path = path;

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _nextMock.Verify(next => next(_context), Times.Once);
        _cacheMock.Verify(cache => cache.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory]
    [InlineData("/health")]
    [InlineData("/Health")]
    [InlineData("/health/check")]
    public async Task Invoke_WithHealthPath_ShouldCallNext(string path)
    {
        // Arrange
        _context.Request.Path = path;

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _nextMock.Verify(next => next(_context), Times.Once);
        _cacheMock.Verify(cache => cache.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Invoke_WithUnauthenticatedUser_ShouldCallNext()
    {
        // Arrange
        _context.Request.Path = "/dashboard";
        _context.User = new ClaimsPrincipal(); // No identity

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _nextMock.Verify(next => next(_context), Times.Once);
        _cacheMock.Verify(cache => cache.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Invoke_WithEmptyUserName_ShouldCallNext()
    {
        // Arrange
        _context.Request.Path = "/dashboard";
        var identity = new ClaimsIdentity();
        _context.User = new ClaimsPrincipal(identity);

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _nextMock.Verify(next => next(_context), Times.Once);
        _cacheMock.Verify(cache => cache.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Invoke_WithAuthenticatedUserButNoCacheEntry_ShouldSignOut()
    {
        // Arrange
        SetupAuthenticatedUser("testuser");
        _context.Request.Path = "/dashboard";
        _cacheMock.Setup(c => c.GetAsync("Web:testuser", It.IsAny<CancellationToken>()))
                  .ReturnsAsync((byte[])null);

        var authServiceMock = new Mock<IAuthenticationService>();
        _context.RequestServices = CreateServiceProvider(authServiceMock.Object);

        // Act
        await _middleware.Invoke(_context);

        // Assert
        authServiceMock.Verify(
            auth => auth.SignOutAsync(_context, CookieAuthenticationDefaults.AuthenticationScheme, null),
            Times.Once);
    }

    [Fact]
    public async Task Invoke_WithValidSessionAndMatchingUser_ShouldCallNext()
    {
        // Arrange
        var userName = "testuser";
        var sessionId = "session123";
        
        SetupAuthenticatedUser(userName);
        SetupUserSession(userName, "forms");
        _context.Request.Path = "/dashboard";
        _context.Session = CreateMockSession(sessionId);

        var cacheValue = Encoding.UTF8.GetBytes(sessionId);
        _cacheMock.Setup(c => c.GetAsync($"Web:{userName}", It.IsAny<CancellationToken>()))
                  .ReturnsAsync(cacheValue);

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _nextMock.Verify(next => next(_context), Times.Once);
    }

    [Fact]
    public async Task Invoke_WithDifferentSessionId_ShouldThrowSessionExpiredException()
    {
        // Arrange
        var userName = "testuser";
        var currentSessionId = "session123";
        var cachedSessionId = "session456";
        
        SetupAuthenticatedUser(userName);
        SetupUserSession(userName, "forms");
        _context.Request.Path = "/dashboard";
        _context.Session = CreateMockSession(currentSessionId);

        var cacheValue = Encoding.UTF8.GetBytes(cachedSessionId);
        _cacheMock.Setup(c => c.GetAsync($"Web:{userName}", It.IsAny<CancellationToken>()))
                  .ReturnsAsync(cacheValue);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<SessionExpiredException>(
            () => _middleware.Invoke(_context));

        Assert.Contains("Session logged out, another user logged in", exception.Message);
        
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<object>(v => v.ToString()!.Contains($"'{userName}' Session logged out, another user logged in")),
                It.IsAny<Exception>(),
                It.IsAny<Func<object, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Invoke_WithADUserAndDifferentLoginName_ShouldThrowSessionExpiredException()
    {
        // Arrange
        var userName = "DOMAIN\\testuser";
        var loginName = "differentuser";
        var sessionId = "session123";
        
        SetupAuthenticatedUser(userName);
        SetupUserSession(loginName, "ad");
        _context.Request.Path = "/dashboard";
        _context.Session = CreateMockSession(sessionId);

        var cacheValue = Encoding.UTF8.GetBytes(sessionId);
        _cacheMock.Setup(c => c.GetAsync($"Web:{userName.ToLower()}", It.IsAny<CancellationToken>()))
                  .ReturnsAsync(cacheValue);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<SessionExpiredException>(
            () => _middleware.Invoke(_context));

        Assert.Contains("Session logged out, another user logged in", exception.Message);
    }

    [Fact]
    public async Task Invoke_WithADUserAndMatchingLoginName_ShouldCallNext()
    {
        // Arrange
        var userName = "DOMAIN\\testuser";
        var loginName = "testuser";
        var sessionId = "session123";
        
        SetupAuthenticatedUser(userName);
        SetupUserSession(loginName, "ad");
        _context.Request.Path = "/dashboard";
        _context.Session = CreateMockSession(sessionId);

        var cacheValue = Encoding.UTF8.GetBytes(sessionId);
        _cacheMock.Setup(c => c.GetAsync($"Web:{userName.ToLower()}", It.IsAny<CancellationToken>()))
                  .ReturnsAsync(cacheValue);

        // Act
        await _middleware.Invoke(_context);

        // Assert
        _nextMock.Verify(next => next(_context), Times.Once);
    }

    private void SetupAuthenticatedUser(string userName)
    {
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.Name, userName)
        };
        var identity = new ClaimsIdentity(claims, "TestAuth");
        identity.AddClaim(new Claim(ClaimTypes.Authentication, "true"));
        _context.User = new ClaimsPrincipal(identity);
    }

    private void SetupUserSession(string loginName, string authType)
    {
        // This would require mocking WebHelper.UserSession
        // For now, we'll assume it's properly configured in the test environment
    }

    private ISession CreateMockSession(string sessionId)
    {
        var sessionMock = new Mock<ISession>();
        sessionMock.Setup(s => s.Id).Returns(sessionId);
        return sessionMock.Object;
    }

    private IServiceProvider CreateServiceProvider(IAuthenticationService authService)
    {
        var serviceProviderMock = new Mock<IServiceProvider>();
        serviceProviderMock.Setup(sp => sp.GetService(typeof(IAuthenticationService)))
                          .Returns(authService);
        return serviceProviderMock.Object;
    }
}

public class PreventMultipleLoginMiddlewareExtensionTests
{
    [Fact]
    public void UsePreventMultipleLogin_ShouldRegisterMiddleware()
    {
        // Arrange
        var app = new Mock<IApplicationBuilder>();
        app.Setup(x => x.UseMiddleware<PreventMultipleLoginMiddleware>()).Returns(app.Object);

        // Act
        var result = app.Object.UsePreventMultipleLogin();

        // Assert
        Assert.NotNull(result);
        app.Verify(x => x.UseMiddleware<PreventMultipleLoginMiddleware>(), Times.Once);
    }
}
