﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class BiaRulesFilterSpecification : Specification<BiaRules>
{
    public BiaRulesFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.Type != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("properties=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Properties.Contains(stringItem.Replace("properties=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("type=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Type.Contains(stringItem.Replace("type=", "", StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p => p.Properties.Contains(searchString) || p.Type.Contains(searchString);
            }
        }
    }
}