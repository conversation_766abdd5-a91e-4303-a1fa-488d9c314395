﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IApprovalMatrixRepository : IRepository<ApprovalMatrix>
{
    Task<bool> IsApprovalMatrixNameUnique(string name);
    Task<bool> IsApprovalMatrixNameExist(string name, string id);
    Task<ApprovalMatrix> GetApprovalMatrixByBusinessFunctionId(string businessFunctionId);
}