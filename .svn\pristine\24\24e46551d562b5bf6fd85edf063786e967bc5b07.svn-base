﻿using ContinuityPatrol.Application.Features.WorkflowPermission.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowPermissionModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowPermission.Queries;

public class GetWorkflowPermissionPaginatedListQueryHandlerTests : IClassFixture<WorkflowPermissionFixture>
{
    private readonly GetWorkflowPermissionPaginatedListQueryHandler _handler;

    private readonly Mock<IWorkflowPermissionRepository> _mockWorkflowPermissionRepository;

    public GetWorkflowPermissionPaginatedListQueryHandlerTests(WorkflowPermissionFixture workflowPermissionFixture)
    {
        workflowPermissionFixture.WorkflowPermissions = new List<Domain.Entities.WorkflowPermission>
            {
                new Domain.Entities.WorkflowPermission
                {
                    AccessProperties = "[{\"id\":\"d09583f7-be74-4815-b802-8a0b25d72c2f\",\"name\":\"testt1\"}]",
                    AccessType = "Admin",
                    ReferenceId = "a9b18f42-5ca6-4f9a-97b0-0ca1567c1cfa",
                    UserProperties = "[{\"id\":\"19c4267d-aa08-4672-b67e-07457e39f272\",\"name\":\"Abhijeet_Tate\"}]"
                },
                new Domain.Entities.WorkflowPermission
                {
                    AccessProperties = "[{\"id\":\"c06fc2a3-cdad-4dec-93c3-fe1bbbcf9818\",\"name\":\"man12f\"}]",
                    AccessType = "SuperAdmin",
                    ReferenceId = "a9b18f42-5ca6-4f9a-97b0-0ca1567c1cfa",
                    UserProperties = "[{\"id\":\"960337c6-dc27-487a-8aa1-580ec6b6a48d\",\"name\":\"aro\"}]"
                }
            };
        _mockWorkflowPermissionRepository = new Mock<IWorkflowPermissionRepository>();

        var queryable = workflowPermissionFixture.WorkflowPermissions.AsQueryable();

        _mockWorkflowPermissionRepository.Setup(x => x.PaginatedListAllAsync()).Returns(queryable);

        _mockWorkflowPermissionRepository = WorkflowPermissionRepositoryMocks.GetPaginatedWorkflowPermissionRepository(workflowPermissionFixture.WorkflowPermissions);

        _handler = new GetWorkflowPermissionPaginatedListQueryHandler(workflowPermissionFixture.Mapper,_mockWorkflowPermissionRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var query = new GetWorkflowPermissionPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "NonExistingSearchString"
        };
        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowPermissionListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedWorkflowPermission_When_QueryStringMatch()
    {
        var query = new GetWorkflowPermissionPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "test"
        };
        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowPermissionListVm>>();

        result.TotalCount.ShouldBeGreaterThan(0);

        result.Data.ShouldNotBeEmpty();

        var firstItem = result.Data.First();

        firstItem.ShouldBeOfType<WorkflowPermissionListVm>();

        firstItem.Id.ShouldNotBeNullOrEmpty();

        firstItem.UserProperties.ShouldContain("Abhijeet_Tate");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var query = new GetWorkflowPermissionPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };
        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowPermissionListVm>>();

        result.TotalCount.ShouldBe(2);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_WorkflowPermission_With_MultipleQueryStringParameter()
    {
        var query = new GetWorkflowPermissionPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "AccessType=SuperAdmin"
        };
        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowPermissionListVm>>();

        result.TotalCount.ShouldBe(1);

        var item = result.Data.FirstOrDefault();

        item.ShouldNotBeNull();

        item.AccessType.ShouldBe("SuperAdmin");

        item.UserProperties.ShouldBe("[{\"id\":\"960337c6-dc27-487a-8aa1-580ec6b6a48d\",\"name\":\"aro\"}]");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetWorkflowPermissionPaginatedListQuery(), CancellationToken.None);
        
        _mockWorkflowPermissionRepository.Verify(x => x.PaginatedListAllAsync(), Times.Once);
    }
}