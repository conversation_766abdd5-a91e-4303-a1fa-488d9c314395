﻿namespace ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Create;

public class
    CreateHeatMapStatusCommandHandler : IRequestHandler<CreateHeatMapStatusCommand, CreateHeatMapStatusResponse>
{
    private readonly IHeatMapStatusRepository _heatMapStatusRepository;
    private readonly IMapper _mapper;

    public CreateHeatMapStatusCommandHandler(IMapper mapper, IHeatMapStatusRepository heatMapStatusRepository)
    {
        _mapper = mapper;
        _heatMapStatusRepository = heatMapStatusRepository;
    }

    public async Task<CreateHeatMapStatusResponse> Handle(CreateHeatMapStatusCommand request,
        CancellationToken cancellationToken)
    {
        var heatMapStatus = _mapper.Map<Domain.Entities.HeatMapStatus>(request);

        heatMapStatus = await _heatMapStatusRepository.AddAsync(heatMapStatus);

        var response = new CreateHeatMapStatusResponse
        {
            Message = Message.Create(nameof(Domain.Entities.HeatMapStatus), heatMapStatus.InfraObjectName),

            Id = heatMapStatus.ReferenceId
        };

        return response;
    }
}