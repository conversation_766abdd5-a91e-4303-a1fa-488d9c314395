using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ImpactAvailabilityFixture : IDisposable
{
    public List<ImpactAvailability> ImpactAvailabilityPaginationList { get; set; }
    public List<ImpactAvailability> ImpactAvailabilityList { get; set; }
    public ImpactAvailability ImpactAvailabilityDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public ImpactAvailabilityFixture()
    {
        var fixture = new Fixture();

        ImpactAvailabilityList = fixture.Create<List<ImpactAvailability>>();

        ImpactAvailabilityPaginationList = fixture.CreateMany<ImpactAvailability>(20).ToList();

        // Setup proper test data for ImpactAvailabilityPaginationList
        ImpactAvailabilityPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        ImpactAvailabilityPaginationList.ForEach(x => x.IsActive = true);
        ImpactAvailabilityPaginationList.ForEach(x => x.BusinessServiceId = Guid.NewGuid().ToString());

        // Setup proper test data for ImpactAvailabilityList
        ImpactAvailabilityList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        ImpactAvailabilityList.ForEach(x => x.IsActive = true);
        ImpactAvailabilityList.ForEach(x => x.BusinessServiceId = Guid.NewGuid().ToString());

        ImpactAvailabilityDto = fixture.Create<ImpactAvailability>();
        ImpactAvailabilityDto.ReferenceId = Guid.NewGuid().ToString();
        ImpactAvailabilityDto.IsActive = true;
        ImpactAvailabilityDto.BusinessServiceId = Guid.NewGuid().ToString();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
