﻿namespace ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetBusinessServiceAvailability;

public class
    GetBusinessServiceAvailabilityQueryHandler : IRequestHandler<GetBusinessServiceAvailabilityQuery,
        BusinessServiceAvailabilityVm>
{
    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IHeatMapStatusRepository _heatMapStatusRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IMapper _mapper;

    public GetBusinessServiceAvailabilityQueryHandler(IMapper mapper,
        IBusinessServiceRepository businessServiceRepository, IBusinessFunctionRepository businessFunctionRepository,
        IInfraObjectRepository infraObjectRepository, IHeatMapStatusRepository heatMapStatusRepository)
    {
        _mapper = mapper;
        _businessServiceRepository = businessServiceRepository;
        _businessFunctionRepository = businessFunctionRepository;
        _infraObjectRepository = infraObjectRepository;
        _heatMapStatusRepository = heatMapStatusRepository;
    }

    public async Task<BusinessServiceAvailabilityVm> Handle(GetBusinessServiceAvailabilityQuery request,
        CancellationToken cancellationToken)
    {
        var heatMapStatus = new BusinessServiceAvailabilityVm();

        var businessService = await _businessServiceRepository.ListAllAsync();

        var bsAvailable = false;

        foreach(var bs in businessService)
        {
            heatMapStatus.BSConfiguredCount += 1;

            var bsMap = _mapper.Map<ServiceAvailability>(bs);

            var businessFunctions =await _businessFunctionRepository
                .GetBusinessFunctionListByBusinessServiceId(bs.ReferenceId);

            if (businessFunctions.Count > 0)
            {
                var infraAvailable = true;

                foreach(var bf in businessFunctions)
                {
                    var infraObjects =await _infraObjectRepository.GetInfraObjectByBusinessFunctionId(bf.ReferenceId);

                    if (infraObjects.Count > 0)
                    {
                        bsMap.ServiceRemark.ConfiguredInfraObjectCount += infraObjects.Count;

                        foreach(var infra in infraObjects)
                        {
                            var heatMapStatsInfra =await _heatMapStatusRepository
                                .GetHeatMapByInfraObjectId(infra.ReferenceId);

                            if (heatMapStatsInfra.Count > 0)
                            {
                                var affectedCount = heatMapStatsInfra.Count(x => x.IsAffected);

                                bsMap.ServiceRemark.AffectedInfraObjectCount += affectedCount;

                                if (affectedCount > 0)
                                {
                                    bsMap.IsAffected = heatMapStatsInfra.Any(x => x.IsAffected);

                                    var affectedList = heatMapStatsInfra.Where(x => x.IsAffected).ToList();

                                    var heatMapsList = _mapper.Map<List<HeatmapStatusDto>>(affectedList);

                                    bsAvailable = false;

                                    infraAvailable = false;

                                    //heatMapStatus.BSNotAvailableCount += 1;

                                    bsMap.ServiceRemark.HeatmapStatusesDto.AddRange(heatMapsList);
                                }
                                else if (infraAvailable)
                                {
                                    bsAvailable = true;
                                    bsMap.IsAffected = false;
                                }
                            }
                            else
                            {
                                bsAvailable = true;
                                bsMap.IsAffected = false;
                            }
                        };
                    }
                    else
                    {
                        bsAvailable = false;
                        // heatMapStatus.BSNotAvailableCount += 1;
                    }
                };

                if (bsAvailable)
                    heatMapStatus.BSAvailableCount += 1;
                else
                    heatMapStatus.BSNotAvailableCount += 1;

                heatMapStatus.ServiceAvailability.AddRange(bsMap);
            }
            else
            {
                bsAvailable = false;
                heatMapStatus.BSNotAvailableCount += 1;
                heatMapStatus.ServiceAvailability.AddRange(bsMap);
            }
        };

        return heatMapStatus;
    }
}