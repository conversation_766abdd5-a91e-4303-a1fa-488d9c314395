﻿namespace ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDrReadinessByBusinessServices;

public class GetDrReadinessByBusinessServiceQueryHandler : IRequestHandler<GetDrReadinessByBusinessServiceQuery,
    GetDrReadinessByBusinessServiceVm>
{
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IDrReadyStatusRepository _drReadyStatusRepository;
    private readonly IBusinessFunctionRepository _functionRepository;
    private readonly IHeatMapStatusRepository _heatMapStatusRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IMapper _mapper;
    private readonly IMonitorServiceRepository _monitorServiceRepository;
    private readonly IWorkflowInfraObjectRepository _workflowInfraObjectRepository;

    public GetDrReadinessByBusinessServiceQueryHandler(I<PERSON>apper mapper, IHeatMapStatusRepository heatMapStatusRepository,
        IBusinessServiceRepository businessServiceRepository, IBusinessFunctionRepository businessFunctionRepository,
        IInfraObjectRepository infraObjectRepository, IDrReadyStatusRepository dRReadyStatusRepository,
        IWorkflowInfraObjectRepository workflowInfraObjectRepository,
        IMonitorServiceRepository monitorServiceRepository)
    {
        _mapper = mapper;
        _heatMapStatusRepository = heatMapStatusRepository;
        _businessServiceRepository = businessServiceRepository;
        _functionRepository = businessFunctionRepository;
        _infraObjectRepository = infraObjectRepository;
        _drReadyStatusRepository = dRReadyStatusRepository;
        _workflowInfraObjectRepository = workflowInfraObjectRepository;
        _monitorServiceRepository = monitorServiceRepository;
    }

    public async Task<GetDrReadinessByBusinessServiceVm> Handle(GetDrReadinessByBusinessServiceQuery request,
        CancellationToken cancellationToken)
    {
        var drReady = new GetDrReadinessByBusinessServiceVm();

        var businessServices = request.BusinessServiceId.IsNullOrWhiteSpace()
            ? (await _businessServiceRepository.ListAllAsync()).ToList()
            : await _businessServiceRepository.GetByReferenceIdAsync(request.BusinessServiceId) is not null
                ? new List<Domain.Entities.BusinessService>
                    { await _businessServiceRepository.GetByReferenceIdAsync(request.BusinessServiceId) }
                : new List<Domain.Entities.BusinessService>();

        drReady.TotalBusinessServiceCount = businessServices.Count;

        var heatMapStatus = request.BusinessServiceId.IsNullOrWhiteSpace()
            ? await _heatMapStatusRepository.ListAllAsync()
            : await _heatMapStatusRepository.GetHeatMapListByBusinessServiceId(request.BusinessServiceId);

        //var heatMapStatus = await _heatMapStatusRepository.ListAllAsync();

        var heatMap = _mapper.Map<Components>(heatMapStatus);

        drReady.Components = heatMap;

        foreach(var bs in businessServices)
        {
            var businessFunction =await
                _functionRepository.GetBusinessFunctionListByBusinessServiceId(bs.ReferenceId);

            foreach(var bf in businessFunction)
            {
                var infraObject =await _infraObjectRepository.GetInfraObjectByBusinessFunctionId(bf.ReferenceId);

                var drReadyInfra = infraObject.Where(x => x.DRReady).ToList();

                var infraDrDready = _mapper.Map<List<GetTotalDrReadyVm>>(drReadyInfra);

                drReady.TotalDrReadyInfraObjectCount += drReadyInfra.Count;

                drReady.Orchestration.TotalDrReadyCount += drReadyInfra.Count;

                drReady.Orchestration.GetTotalDrReadyVms.AddRange(infraDrDready);

                foreach(var dr in drReadyInfra)
                {
                    var monitorService =await _monitorServiceRepository.GetMonitorServiceByInfraObjectId(dr.ReferenceId);
                        

                    drReady.RelatedServiceCount += monitorService.Count;

                    drReady.RelatedService.RelatedServiceNotReadyCount += monitorService.Count(x =>
                        x.Status.Trim().ToLower().Equals("stopped"));

                    drReady.RelatedService.RelatedServiceErrorCount += monitorService.Count(x =>
                        !x.Status.Trim().ToLower().Equals("stopped") &&
                        (x.IsServiceUpdate?.Trim().ToLower()!= "running" || x.IsServiceUpdate == null));

                    var drReadyService = _mapper.Map<List<RelatedServiceDto>>(monitorService.Where(x =>
                        !x.Status.Trim().ToLower().Equals("stopped") &&
                        (x.IsServiceUpdate?.Trim().ToLower() != "running" || x.IsServiceUpdate == null)));

                    drReady.RelatedService.RelatedErrorService.AddRange(drReadyService);

                    var drService = _mapper.Map<List<RelatedServiceDto>>(monitorService.Where(x =>
                        x.Status.Trim().ToLower().Equals("stopped")));

                    drReady.RelatedService.RelatedNotReadyService.AddRange(drService);

                    var drReadies =await _drReadyStatusRepository.GetDrReadyStatusByInfraObjectId(dr.ReferenceId);

                    var workflowInfraObject =await _workflowInfraObjectRepository
                        .GetResilienceWorkflowByInfraObjectId(dr.ReferenceId);

                    drReady.TotalOrchestrationCount += workflowInfraObject.Count;

                    if (drReadies is not null)
                    {
                        if (drReadies.NotReady == "1")
                        {
                            drReady.Orchestration.ExecutedErrorCount += 1;

                            var drR = _mapper.Map<GetDrReadyErrorExecutionVm>(drReadies);

                            drReady.Orchestration.GetErrorExecutions.AddRange(drR);
                        }
                    }
                    else
                    {
                        //var drReadyWorkflow = workflowInfraObject.Where(x => x.ActionType.ToLower().Trim().Equals("resiliency ready")).ToList();

                        if (workflowInfraObject.Count == 0)
                        {
                            drReady.Components.WorkflowNotConfiguredCount += 1;

                            var workflowNotConfigured = _mapper.Map<GetWorkflowNotConfiguredVm>(dr);

                            drReady.Orchestration.GetWorkflowNotConfiguredVms.AddRange(workflowNotConfigured);
                        }
                    }
                };


                //var workflowInfraObject =await _workflowInfraObjectRepository.GetWorkflowInfraObjectFromInfraObjectId(bf.ReferenceId);    

                //var drReadyWorkflow = workflowInfraObject.Where(x=>x.ActionType.ToLower().Trim().Equals("dr ready")).ToList();

                //drReady.Orchestration.TotalDrReadyCount += drReadyWorkflow.Count;

                //foreach(var dr in drReadyWorkflow)
                //{
                //    var drReadyStatus =await _drReadyStatusRepository.GetDrReadyStatusByWorkflowId(dr.WorkflowId);
                //};
            };
        };

        return drReady;
    }
}