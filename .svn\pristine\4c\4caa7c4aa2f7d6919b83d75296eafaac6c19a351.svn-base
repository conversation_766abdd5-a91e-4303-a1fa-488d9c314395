﻿namespace ContinuityPatrol.Application.Features.Node.Commands.Update;

public class UpdateNodeCommand : IRequest<UpdateNodeResponse>
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string ServerId { get; set; }
    public string ServerName { get; set; }
    public string TypeId { get; set; }
    public string Type { get; set; }
    public string Properties { get; set; }
    public string FormVersion { get; set; }

    public override string ToString()
    {
        return $"Name: {Name}; Id:{Id};";
    }
}