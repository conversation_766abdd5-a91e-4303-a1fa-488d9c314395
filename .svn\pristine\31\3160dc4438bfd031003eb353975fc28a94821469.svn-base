﻿using ContinuityPatrol.Application.Features.TeamResource.Commands.Create;
using ContinuityPatrol.Application.Features.TeamResource.Commands.Delete;
using ContinuityPatrol.Application.Features.TeamResource.Queries.GetList;
using ContinuityPatrol.Application.Features.TeamResource.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.TeamResource.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.TeamResource.Queries.GetTeamMasterIdByTeamResource;
using ContinuityPatrol.Domain.ViewModels.TeamResourceModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class TeamResourceService : BaseService, ITeamResourceService
{
    public TeamResourceService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateTeamResourceCommand team)
    {
        Logger.LogDebug($"Creating Team '{team.ResourceName}'");
        return await Mediator.Send(team);
    }

    public async Task<BaseResponse> DeleteAsync(string teamId)
    {
        Guard.Against.InvalidGuidOrEmpty(teamId, "Team Id");

        Logger.LogDebug($"Deleting Team Details by Id '{teamId}'");

        return await Mediator.Send(new DeleteTeamResourceCommand { Id = teamId });
    }

    public async Task<List<TeamResourceListVm>> GetTeamMasterIdByTeamMember(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Team Id");

        Logger.LogDebug($"Get Detail by Id '{id}'");

        return await Mediator.Send(new GetTeamMasterIdByTeamResourceQuery { TeamMasterId = id });
    }

    public async Task<PaginatedResult<TeamResourceListVm>> GetTeamMemberList(GetTeamResourcePaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in TeamMember Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<List<TeamResourceListVm>> GetTeamMemberNames()
    {
        Logger.LogDebug("Get All TeamMember ");

        return await Mediator.Send(new GetTeamResourceListQuery());
    }

    public async Task<bool> IsTeamMemberNameExist(string name, string id)
    {
        Guard.Against.NullOrWhiteSpace(name, "Resource Name");

        Logger.LogDebug($"Check Name Exists Detail by Display Name '{name}'and Id '{id}'");

        return await Mediator.Send(new GetTeamResourceNameUniqueQuery { ResourceName = name, ResourceId = id });
    }

    public async Task<bool> IsTeamMemberNameUnique(string name)
    {
        Guard.Against.NullOrWhiteSpace(name, "Resource Name");

        Logger.LogDebug($"Check Name Exists Detail by Display Name '{name}'");

        return await Mediator.Send(new GetTeamResourceNameUniqueQuery { ResourceName = name });
    }

    public async Task<PaginatedResult<TeamResourceListVm>> GetTeamMemberListAll(string query)
    {
        Logger.LogDebug("Get Searching Details in TeamMember Paginated List");

        return await Mediator.Send(new GetTeamResourcePaginatedListQuery { TeamName = query });
    }
}