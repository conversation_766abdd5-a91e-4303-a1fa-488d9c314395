using ContinuityPatrol.Application.Features.BulkImportOperation.Events.Delete;

namespace ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Delete;

public class
    DeleteBulkImportOperationCommandHandler : IRequestHandler<DeleteBulkImportOperationCommand,
        DeleteBulkImportOperationResponse>
{
    private readonly IBulkImportOperationRepository _bulkImportOperationRepository;
    private readonly IPublisher _publisher;

    public DeleteBulkImportOperationCommandHandler(IBulkImportOperationRepository bulkImportOperationRepository,
        IPublisher publisher)
    {
        _bulkImportOperationRepository = bulkImportOperationRepository;

        _publisher = publisher;
    }

    public async Task<DeleteBulkImportOperationResponse> Handle(DeleteBulkImportOperationCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _bulkImportOperationRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.BulkImportOperation),
            new NotFoundException(nameof(Domain.Entities.BulkImportOperation), request.Id));

        eventToDelete.IsActive = false;

        await _bulkImportOperationRepository.UpdateAsync(eventToDelete);

        var response = new DeleteBulkImportOperationResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.BulkImportOperation), eventToDelete.UserName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new BulkImportOperationDeletedEvent { Name = eventToDelete.UserName },
            cancellationToken);

        return response;
    }
}