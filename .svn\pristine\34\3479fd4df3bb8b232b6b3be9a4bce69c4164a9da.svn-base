﻿namespace ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Request;

public class ApprovalMatrixRequestCommand : IRequest<ApprovalMatrixRequestResponse>
{
    public string Id { get; set; }
    public string ProcessName { get; set; }
    public string Description { get; set; }
    public string UserName { get; set; }
    public string Status { get; set; }
    public string Approvers { get; set; }
    public DateTime StartDateTime { get; set; }
    public DateTime EndDateTime { get; set; }
    public bool IsRequest { get; set; }
}