﻿namespace ContinuityPatrol.Application.Features.StateMonitorLog.Commands.Update;

public class
    UpdateStateMonitorLogCommandHandler : IRequestHandler<UpdateStateMonitorLogCommand, UpdateStateMonitorLogResponse>
{
    private readonly IMapper _mapper;
    private readonly IStateMonitorLogRepository _stateMonitorLogRepository;

    public UpdateStateMonitorLogCommandHandler(IStateMonitorLogRepository stateMonitorLogRepository, IMapper mapper)
    {
        _stateMonitorLogRepository = stateMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<UpdateStateMonitorLogResponse> Handle(UpdateStateMonitorLogCommand request,
        CancellationToken cancellationToken)
    {
        var evenToUpdate = await _stateMonitorLogRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(evenToUpdate, nameof(Domain.Entities.StateMonitorLog),
            new NotFoundException(nameof(Domain.Entities.StateMonitorLog), request.Id));

        _mapper.Map(request, evenToUpdate, typeof(UpdateStateMonitorLogCommand),
            typeof(Domain.Entities.StateMonitorLog));

        await _stateMonitorLogRepository.UpdateAsync(evenToUpdate);

        var response = new UpdateStateMonitorLogResponse
        {
            Message = "State Monitor Log Updated Successfully.",
            Id = evenToUpdate.ReferenceId
        };
        return response;
    }
}