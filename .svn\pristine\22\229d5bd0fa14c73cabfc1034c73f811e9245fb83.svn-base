﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.TableAccess.Event.Update;

public class TableAccessUpdatedEventHandler : INotificationHandler<TableAccessUpdatedEvent>
{
    private readonly ILogger<TableAccessUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public TableAccessUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<TableAccessUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(TableAccessUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.TableAccess}",
            Entity = Modules.TableAccess.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"TableAccess '{updatedEvent.TableName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"TableAccess '{updatedEvent.TableName}' updated successfully.");
    }
}