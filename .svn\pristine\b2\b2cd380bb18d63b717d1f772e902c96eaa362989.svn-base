﻿using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.Replace;
using ContinuityPatrol.Application.Features.LicenseManager.Command.UpdateState;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetByPoNumber;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetChildLicenseByParentId;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseCount;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseExpireList;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetPoNumber;
using ContinuityPatrol.Application.Features.User.Commands.UserLock;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Helper;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class LicenseManagerControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IDataProvider> _mockProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<ILogger<LicenseManagerController>> _mockLogger = new();
        private LicenseManagerController _controller;

        public LicenseManagerControllerShould()
        {
            Initialze();
        }
        public void Initialze()
        {
            _controller = new LicenseManagerController
                (_mockPublisher.Object,
                 _mockProvider.Object,
                 _mockMapper.Object,
                 _mockLogger.Object
                );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public async Task List_ReturnsViewResult_WithBaseLicenseViewModel()
        {
            var licenseDetails = new List<LicenseManagerDetailViewVm>();
            var licenseInfoDetails = new List<LicenseInfoListVm>();
            var companyNames = new List<CompanyListVm>();
            var licenseMangerPoVm = new List<LicenseManagerNameVm>();

            _mockProvider.Setup(p => p.LicenseManager.LicenseManagerDetailView()).ReturnsAsync(licenseDetails);
            _mockProvider.Setup(p => p.LicenseInfo.GetLicenseInfo()).ReturnsAsync(licenseInfoDetails);
            _mockProvider.Setup(p => p.Company.GetCompanies()).ReturnsAsync(companyNames);
            _mockProvider.Setup(p => p.LicenseManager.GetAllPoNumbers()).ReturnsAsync(licenseMangerPoVm);

            _mockMapper.Setup(m => m.Map<List<SelectListItem>>(It.IsAny<List<CompanyViewModel>>()))
                .Returns(new List<SelectListItem>());

            var result = await _controller.List();

            var viewResult = Assert.IsType<ViewResult>(result);
            var model = Assert.IsType<BaseLicenseViewModel>(viewResult.Model);
            Assert.Equal(licenseDetails, model.LicenseManagerDetails);
            Assert.Equal(licenseInfoDetails, model.LicenseInfoLists);
        }

        [Fact]
        public async Task CreateOrUpdate_ValidLicense_Create_ReturnsJsonResult()
        {
            // Arrange
            var licenseKey = "test*host*ip*mac*100*database*random*date*action*renewal*support*tool*request*company"; // Decrypted test data with 'tool' keyword
            var encryptedLicenseKey = SecurityHelper.Encrypt(licenseKey); // Use SecurityHelper.Encrypt instead of Base64
            var viewModel = new BaseLicenseViewModel { LicenseKey = encryptedLicenseKey };

            // Setup form data for the request
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create
            var collection = new FormCollection(dic);

            // Setup HttpContext and Request.Form
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Form = collection;
            _controller.ControllerContext.HttpContext = httpContext;

            var createCommand = new CreateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<CreateBaseLicenseCommand>(viewModel)).Returns(createCommand);

            // Mock the license lookup to return null (indicating new license)
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseDetailsByPoNumber("test"))
                .ReturnsAsync((Application.Features.LicenseManager.Queries.GetLicenseByPONumber.GetLicenseByPONumberVm)null);

            // Mock the create license operation
            _mockProvider.Setup(p => p.LicenseManager.CreateBaseLicense(createCommand))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "License created successfully" });

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"message\":\"License created successfully\"", json);

            // Verify the service calls
            _mockProvider.Verify(p => p.LicenseManager.GetLicenseDetailsByPoNumber("test"), Times.Once);
            _mockProvider.Verify(p => p.LicenseManager.CreateBaseLicense(createCommand), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ValidationException_ReturnsJsonError()
        {
            // Arrange
            var licenseKey = "dGVzdCpob3N0KmlwKm1hYyoxMDAqZGF0YWJhc2UqcmFuZG9tKmRhdGUqYWN0aW9uKnJlbmV3YWwqc3VwcG9ydCp0b29sKnJlcXVlc3QqY29tcGFueQ=="; // Base64 encoded test data
            var viewModel = new BaseLicenseViewModel { LicenseKey = licenseKey };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<CreateBaseLicenseCommand>(viewModel)).Returns(createCommand);
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseDetailsByPoNumber(It.IsAny<string>())).ReturnsAsync((Application.Features.LicenseManager.Queries.GetLicenseByPONumber.GetLicenseByPONumberVm)null);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockProvider.Setup(p => p.LicenseManager.CreateBaseLicense(createCommand)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":false", json);
            Assert.Contains("\"message\":", json);
        }

        [Fact]
        public async Task Delete_ValidId_ReturnsRedirectToActionResult()
        {
            var id = "123";
            _mockProvider.Setup(p => p.LicenseManager.DeleteAsync(id));

            var result = await _controller.Delete(id);

            var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectToActionResult.ActionName);
        }

        [Fact]
        public async Task Delete_ThrowsException_ReturnsRedirectToActionResult()
        {
            var id = "123";
            var exceptionMessage = "An error occurred";

            _mockProvider.Setup(p => p.LicenseManager.DeleteAsync(id))
                .ThrowsAsync(new System.Exception(exceptionMessage));

            var result = await _controller.Delete(id);

            var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectToActionResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_Update_ReturnsJsonResult()
        {
            // Arrange
            var licenseKey = "dGVzdCpob3N0KmlwKm1hYyoxMDAqZGF0YWJhc2UqcmFuZG9tKmRhdGUqYWN0aW9uKnJlbmV3YWwqc3VwcG9ydCp0b29sKnJlcXVlc3QqY29tcGFueQ=="; // Base64 encoded test data
            var viewModel = new BaseLicenseViewModel { LicenseKey = licenseKey };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123"); // Non-empty for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<UpdateBaseLicenseCommand>(viewModel)).Returns(updateCommand);
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseDetailsByPoNumber(It.IsAny<string>())).ReturnsAsync((Application.Features.LicenseManager.Queries.GetLicenseByPONumber.GetLicenseByPONumberVm)null);
            _mockProvider.Setup(p => p.LicenseManager.UpdateBaseLicense(updateCommand))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "License updated successfully" });

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"message\":\"License updated successfully\"", json);
        }

        [Fact]
        public async Task CreateOrUpdate_Exception_ReturnsJsonError()
        {
            // Arrange
            var licenseKey = "dGVzdCpob3N0KmlwKm1hYyoxMDAqZGF0YWJhc2UqcmFuZG9tKmRhdGUqYWN0aW9uKnJlbmV3YWwqc3VwcG9ydCp0b29sKnJlcXVlc3QqY29tcGFueQ=="; // Base64 encoded test data
            var viewModel = new BaseLicenseViewModel { LicenseKey = licenseKey };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<CreateBaseLicenseCommand>(viewModel)).Returns(createCommand);
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseDetailsByPoNumber(It.IsAny<string>())).ReturnsAsync((Application.Features.LicenseManager.Queries.GetLicenseByPONumber.GetLicenseByPONumberVm)null);
            _mockProvider.Setup(p => p.LicenseManager.CreateBaseLicense(createCommand)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":false", json);
            Assert.Contains("\"message\":", json);
        }

        [Fact]
        public async Task BaseLicenseCreateOrUpdate_Create_ReturnsRedirectToActionResult()
        {
            // Arrange
            var viewModel = new BaseLicenseViewModel { Id = "" }; // Empty for create

            var createCommand = new CreateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<CreateBaseLicenseCommand>(viewModel)).Returns(createCommand);
            _mockProvider.Setup(p => p.LicenseManager.CreateBaseLicense(createCommand))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "License created successfully" });

            // Act
            var result = await _controller.BaseLicenseCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task BaseLicenseCreateOrUpdate_Update_ReturnsRedirectToActionResult()
        {
            // Arrange
            var viewModel = new BaseLicenseViewModel { Id = "123" }; // Non-empty for update

            var updateCommand = new UpdateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<UpdateBaseLicenseCommand>(viewModel)).Returns(updateCommand);
            _mockProvider.Setup(p => p.LicenseManager.UpdateBaseLicense(updateCommand))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "License updated successfully" });

            // Act
            var result = await _controller.BaseLicenseCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task BaseLicenseCreateOrUpdate_ValidationException_ReturnsRedirectToActionResult()
        {
            // Arrange
            var viewModel = new BaseLicenseViewModel { Id = "" }; // Empty for create

            var createCommand = new CreateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<CreateBaseLicenseCommand>(viewModel)).Returns(createCommand);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockProvider.Setup(p => p.LicenseManager.CreateBaseLicense(createCommand)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.BaseLicenseCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task BaseLicenseCreateOrUpdate_Exception_ReturnsRedirectToActionResult()
        {
            // Arrange
            var viewModel = new BaseLicenseViewModel { Id = "" }; // Empty for create

            var createCommand = new CreateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<CreateBaseLicenseCommand>(viewModel)).Returns(createCommand);
            _mockProvider.Setup(p => p.LicenseManager.CreateBaseLicense(createCommand)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.BaseLicenseCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task DerivedLicenseCreateOrUpdate_Create_ReturnsRedirectToActionResult()
        {
            // Arrange
            var viewModel = new DerivedLicenseViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateDerivedLicenseCommand();
            _mockMapper.Setup(m => m.Map<CreateDerivedLicenseCommand>(viewModel)).Returns(createCommand);
            _mockProvider.Setup(p => p.LicenseManager.CreateDerivedLicense(createCommand))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Derived license created successfully" });

            // Act
            var result = await _controller.DerivedLicenseCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task DerivedLicenseCreateOrUpdate_Update_ReturnsRedirectToActionResult()
        {
            // Arrange
            var viewModel = new DerivedLicenseViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123"); // Non-empty for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateDerivedLicenseCommand();
            _mockMapper.Setup(m => m.Map<UpdateDerivedLicenseCommand>(viewModel)).Returns(updateCommand);
            _mockProvider.Setup(p => p.LicenseManager.UpdateDerivedLicense(updateCommand))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Derived license updated successfully" });

            // Act
            var result = await _controller.DerivedLicenseCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task DerivedLicenseCreateOrUpdate_Exception_ReturnsRedirectToActionResult()
        {
            // Arrange
            var viewModel = new DerivedLicenseViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateDerivedLicenseCommand();
            _mockMapper.Setup(m => m.Map<CreateDerivedLicenseCommand>(viewModel)).Returns(createCommand);
            _mockProvider.Setup(p => p.LicenseManager.CreateDerivedLicense(createCommand)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DerivedLicenseCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task DeleteDerived_Success_ReturnsRedirectToActionResult()
        {
            // Arrange
            var id = "123";
            _mockProvider.Setup(p => p.LicenseManager.DeleteDerivedLicense(id)).ReturnsAsync(new BaseResponse { Success = true, Message = "Deleted successfully" });

            // Act
            var result = await _controller.DeleteDerived(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task DeleteDerived_Exception_ReturnsRedirectToActionResult()
        {
            // Arrange
            var id = "123";
            _mockProvider.Setup(p => p.LicenseManager.DeleteDerivedLicense(id)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteDerived(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public void LoadPartialView_ReturnsPartialViewResult()
        {
            // Arrange
            var name = "TestPartialView";
            var licenseDetails = new List<LicenseManagerDetailViewVm>();
            _mockProvider.Setup(p => p.LicenseManager.LicenseManagerDetailView()).ReturnsAsync(licenseDetails);

            // Act
            var result = _controller.LoadPartialView(name);

            // Assert
            var partialViewResult = Assert.IsType<PartialViewResult>(result);
            Assert.Equal(name, partialViewResult.ViewName);
            Assert.IsType<BaseLicenseViewModel>(partialViewResult.Model);
        }

        [Fact]
        public async Task GetLicenseManagerByPoNumber_ReturnsJsonResult()
        {
            // Arrange
            var poNumber = "PO123";
            var licenseData = new LicenseManagerByPoNumberVm();
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseManagerByPoNumber(poNumber)).ReturnsAsync(licenseData);

            // Act
            var result = await _controller.GetLicenseManagerByPoNumber(poNumber);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(licenseData, jsonResult.Value);
        }

        [Fact]
        public async Task GetLicenseManagerByPoNumber_Exception_ReturnsJsonError()
        {
            // Arrange
            var poNumber = "PO123";
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseManagerByPoNumber(poNumber)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetLicenseManagerByPoNumber(poNumber);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetList_ReturnsJsonResult()
        {
            // Arrange
            var licenseDetails = new List<LicenseManagerDetailViewVm>();
            _mockProvider.Setup(p => p.LicenseManager.LicenseManagerDetailView()).ReturnsAsync(licenseDetails);

            // Act
            var result = await _controller.GetList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(licenseDetails, jsonResult.Value);
        }

        [Fact]
        public async Task GetList_Exception_ReturnsJsonError()
        {
            // Arrange
            _mockProvider.Setup(p => p.LicenseManager.LicenseManagerDetailView()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetLicenseManagerCount_ReturnsJsonResult()
        {
            // Arrange
            var countVm = new LicenseCountVm { TotalLicenseCount = 10 };
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseManagerCount()).ReturnsAsync(countVm);

            // Act
            var result = await _controller.GetLicenseManagerCount();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(countVm, jsonResult.Value);
        }

        [Fact]
        public async Task GetLicenseManagerCount_Exception_ReturnsJsonError()
        {
            // Arrange
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseManagerCount()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetLicenseManagerCount();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetLicensesNames_ReturnsJsonResult()
        {
            // Arrange
            var licenseNames = new List<LicenseManagerNameVm>();
            _mockProvider.Setup(p => p.LicenseManager.GetAllPoNumbers()).ReturnsAsync(licenseNames);

            // Act
            var result = await _controller.GetLicensesNames();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task GetLicensesNames_Exception_ReturnsJsonError()
        {
            // Arrange
            _mockProvider.Setup(p => p.LicenseManager.GetAllPoNumbers()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetLicensesNames();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":false", json);
            Assert.Contains("\"message\":", json);
        }

        [Fact]
        public async Task GetLicensesNamesWithCount_ReturnsJsonResult()
        {
            // Arrange
            var type = "database";
            var roleType = "primary";
            var siteId = "site1";
            var serverId = "server1";
            var replicationType = "sync";
            var databaseTypeId = "db1";
            var licenseNamesWithCount = new List<GetPoNumberListVm>();
            _mockProvider.Setup(p => p.LicenseManager.GetPoNumber(type, roleType, siteId, serverId, replicationType, databaseTypeId)).ReturnsAsync(licenseNamesWithCount);

            // Act
            var result = await _controller.GetLicensesNamesWithCount(type, roleType, siteId, serverId, replicationType, databaseTypeId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task GetLicensesNamesWithCount_Exception_ReturnsJsonError()
        {
            // Arrange
            var type = "database";
            var roleType = "primary";
            var siteId = "site1";
            var serverId = "server1";
            var replicationType = "sync";
            var databaseTypeId = "db1";
            _mockProvider.Setup(p => p.LicenseManager.GetPoNumber(type, roleType, siteId, serverId, replicationType, databaseTypeId)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetLicensesNamesWithCount(type, roleType, siteId, serverId, replicationType, databaseTypeId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":false", json);
            Assert.Contains("\"message\":", json);
        }

        [Fact]
        public async Task GetExpiredLicensesList_ReturnsJsonResult()
        {
            // Arrange
            var licenseNames = new List<LicenseManagerNameVm>
            {
                new LicenseManagerNameVm { ExpiryDate = "01 January 2020" }, // Expired
                new LicenseManagerNameVm { ExpiryDate = "01 January 2030" }  // Not expired
            };
            _mockProvider.Setup(p => p.LicenseManager.GetAllPoNumbers()).ReturnsAsync(licenseNames);

            // Act
            var result = await _controller.GetExpiredLicensesList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task GetExpiredLicensesList_Exception_ReturnsJsonError()
        {
            // Arrange
            _mockProvider.Setup(p => p.LicenseManager.GetAllPoNumbers()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetExpiredLicensesList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":false", json);
            Assert.Contains("\"message\":", json);
        }

        [Fact]
        public async Task GetListById_ReturnsJsonResult()
        {
            // Arrange
            var id = "123";
            var licenseDetails = new LicenseManagerDetailVm();
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseManagerById(id)).ReturnsAsync(licenseDetails);

            // Act
            var result = await _controller.GetListById(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(licenseDetails, jsonResult.Value);
        }

        [Fact]
        public async Task GetListById_Exception_ReturnsJsonError()
        {
            // Arrange
            var id = "123";
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseManagerById(id)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetListById(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetEntityList_ReturnsJsonResult()
        {
            // Arrange
            var licenseId = "123";
            var entity = "server";
            var entityType = "database";
            var entityList = new List<LicenseInfoByEntityListVm>();
            _mockProvider.Setup(p => p.LicenseInfo.GetLicenseInfoByEntity(licenseId, entity, entityType)).ReturnsAsync(entityList);

            // Act
            var result = await _controller.GetEntityList(licenseId, entity, entityType);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(entityList, jsonResult.Value);
        }

        [Fact]
        public async Task GetEntityList_Exception_ReturnsJsonError()
        {
            // Arrange
            var licenseId = "123";
            var entity = "server";
            var entityType = "database";
            _mockProvider.Setup(p => p.LicenseInfo.GetLicenseInfoByEntity(licenseId, entity, entityType)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetEntityList(licenseId, entity, entityType);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetTypeList_ReturnsJsonResult()
        {
            // Arrange
            var licenseId = "123";
            var type = "database";
            var entityType = "server";
            var typeList = new List<LicenseInfoTypeListVm>();
            _mockProvider.Setup(p => p.LicenseInfo.GetLicenseInfoByType(licenseId, type, entityType)).ReturnsAsync(typeList);

            // Act
            var result = await _controller.GetTypeList(licenseId, type, entityType);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(typeList, jsonResult.Value);
        }

        [Fact]
        public async Task GetTypeList_Exception_ReturnsJsonError()
        {
            // Arrange
            var licenseId = "123";
            var type = "database";
            var entityType = "server";
            _mockProvider.Setup(p => p.LicenseInfo.GetLicenseInfoByType(licenseId, type, entityType)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetTypeList(licenseId, type, entityType);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetDerivedList_ReturnsJsonResult()
        {
            // Arrange
            var parentId = "123";
            var parentPoNumber = "PO123";
            var derivedList = new List<ChildLicenseDetailByParentIdVm>();
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseByParentIdAndParentPoNumber(parentId, parentPoNumber)).ReturnsAsync(derivedList);

            // Act
            var result = await _controller.GetDerivedList(parentId, parentPoNumber);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(derivedList, jsonResult.Value);
        }

        [Fact]
        public async Task GetDerivedList_Exception_ReturnsJsonError()
        {
            // Arrange
            var parentId = "123";
            var parentPoNumber = "PO123";
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseByParentIdAndParentPoNumber(parentId, parentPoNumber)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetDerivedList(parentId, parentPoNumber);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetDecommissionList_ReturnsJsonResult()
        {
            // Arrange
            var licenseId = "123";
            var entityId = "entity123";
            var entityType = "server";
            var decommissionList = new object();
            _mockProvider.Setup(p => p.LicenseManager.GetDecommissionByIdAndEntityId(licenseId, entityId, entityType)).ReturnsAsync(decommissionList);

            // Act
            var result = await _controller.GetDecommissionList(licenseId, entityId, entityType);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(decommissionList, jsonResult.Value);
        }

        [Fact]
        public async Task GetDecommissionList_Exception_ReturnsJsonError()
        {
            // Arrange
            var licenseId = "123";
            var entityId = "entity123";
            var entityType = "server";
            _mockProvider.Setup(p => p.LicenseManager.GetDecommissionByIdAndEntityId(licenseId, entityId, entityType)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetDecommissionList(licenseId, entityId, entityType);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task DeleteDecommission_Success_ReturnsJsonResult()
        {
            // Arrange
            var entityId = "entity123";
            var licenseId = "123";
            var entityType = "server";
            var entityName = "TestServer";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockProvider.Setup(p => p.LicenseManager.DeleteDecommissionByEntityId(entityId, licenseId, entityType, entityName)).ReturnsAsync(response);

            // Act
            var result = await _controller.DeleteDecommission(entityId, licenseId, entityType, entityName);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(response, jsonResult.Value);
        }

        [Fact]
        public async Task DeleteDecommission_Exception_ReturnsJsonError()
        {
            // Arrange
            var entityId = "entity123";
            var licenseId = "123";
            var entityType = "server";
            var entityName = "TestServer";
            _mockProvider.Setup(p => p.LicenseManager.DeleteDecommissionByEntityId(entityId, licenseId, entityType, entityName)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteDecommission(entityId, licenseId, entityType, entityName);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task DecommissionAuthentication_Success_ReturnsJsonResult()
        {
            // Arrange
            var command = new UserLockCommand { UserName = "testuser", Password = "testpassword" };
            var response = new BaseResponse { Success = true, Message = "Authentication successful" };

            // Setup the controller with proper HttpContext and session data
            var httpContext = new DefaultHttpContext();
            var session = new TestSession(); // Use TestSession instead of Mock<ISession>
            httpContext.Session = session;
            _controller.ControllerContext.HttpContext = httpContext;

            // Setup WebHelper.UserSession for LoggedInAuthenticationType
            var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext);
            WebHelper.Configure(mockHttpContextAccessor.Object);

            WebHelper.UserSession = new UserSession
            {
                AuthenticationType = "forms",
                LoggedUserId = "test-user-123",
                CompanyId = "test-company-123"
            };

            // Mock the authentication service
            _mockProvider.Setup(p => p.User.UsersAuthentication(It.IsAny<UserLockCommand>())).ReturnsAsync(response);

            // Act
            var result = await _controller.DecommissionAuthentication(command);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(response, jsonResult.Value);
            _mockProvider.Verify(p => p.User.UsersAuthentication(It.IsAny<UserLockCommand>()), Times.Once);

            // Verify that the command was modified with authentication type and encrypted password
            _mockProvider.Verify(p => p.User.UsersAuthentication(It.Is<UserLockCommand>(cmd =>
                cmd.UserName == "testuser" &&
                cmd.AuthenticationType == "forms" &&
                !string.IsNullOrEmpty(cmd.Password) &&
                cmd.Password != "testpassword")), Times.Once);
        }

        [Fact]
        public async Task DecommissionAuthentication_Exception_ReturnsJsonError()
        {
            // Arrange
            var command = new UserLockCommand { UserName = "testuser", Password = "testpassword" };
            _mockProvider.Setup(p => p.User.UsersAuthentication(command)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DecommissionAuthentication(command);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task ReplaceLicenseDetails_Success_ReturnsJsonResult()
        {
            // Arrange
            var command = new LicenseReplaceCommand();
            var response = new BaseResponse { Success = true, Message = "License replaced successfully" };
            _mockProvider.Setup(p => p.LicenseManager.ReplaceLicenseDetails(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.ReplaceLicenseDetails(command);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task ReplaceLicenseDetails_Exception_ReturnsJsonError()
        {
            // Arrange
            var command = new LicenseReplaceCommand();
            _mockProvider.Setup(p => p.LicenseManager.ReplaceLicenseDetails(command)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.ReplaceLicenseDetails(command);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetLicenseExpiresByCompanyId_ReturnsJsonResult()
        {
            // Arrange
            var companyId = "123";
            var expiredLicenses = new List<LicenseExpireListVm>();
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseExpiresByCompanyId(companyId)).ReturnsAsync(expiredLicenses);

            // Act
            var result = await _controller.GetLicenseExpiresByCompanyId(companyId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(expiredLicenses, jsonResult.Value);
        }

        [Fact]
        public async Task GetLicenseExpiresByCompanyId_Exception_ReturnsJsonError()
        {
            // Arrange
            var companyId = "123";
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseExpiresByCompanyId(companyId)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetLicenseExpiresByCompanyId(companyId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task UpdateLicenseState_Success_ReturnsJsonResult()
        {
            // Arrange
            var command = new UpdateLicenseStateCommand();
            var response = new BaseResponse { Success = true, Message = "License state updated successfully" };
            _mockProvider.Setup(p => p.LicenseManager.UpdateLicenseState(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.UpdateLicenseState(command);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task UpdateLicenseState_Exception_ReturnsJsonError()
        {
            // Arrange
            var command = new UpdateLicenseStateCommand();
            _mockProvider.Setup(p => p.LicenseManager.UpdateLicenseState(command)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdateLicenseState(command);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public void LicenseLanding_ReturnsViewResult()
        {
            // Act
            var result = _controller.LicenseLanding();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Null(viewResult.ViewName); // Default view name
        }

        [Fact]
        public void LicenseExpiredLanding_ReturnsViewResult()
        {
            // Act
            var result = _controller.LicenseExpiredLanding();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Null(viewResult.ViewName); // Default view name
        }

        [Fact]
        public async Task GetLicenseGeneratorKeyById_Success_ReturnsJsonResult()
        {
            // Arrange
            var id = "123";
            var type = "renewal";
            var renewalType = "extend";

            // Create a proper encrypted license key that will decrypt to a valid format
            var decryptedLicenseKey = "test*host*ip*mac*100*database*random*date*action*renewal*support*tool*request*company";
            var encryptedLicenseKey = SecurityHelper.Encrypt(decryptedLicenseKey);
            var encryptedAmcPlan = SecurityHelper.Encrypt("support");
            var encryptedPoNumber = SecurityHelper.Encrypt("test");

            var licenseDetails = new LicenseManagerDetailVm
            {
                Id = id,
                LicenseKey = encryptedLicenseKey,
                CompanyName = "Test Company",
                AmcPlan = encryptedAmcPlan,
                PoNumber = encryptedPoNumber
            };

            // Mock the license details retrieval
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseManagerById(id)).ReturnsAsync(licenseDetails);

            // Mock the mapper for string array to LicenseDto conversion
            var expectedLicenseDto = new LicenseDto
            {
                PoNumber = "test",
                CpHostName = "host",
                IpAddress = "ip",
                MacAddress = "mac",
                LicenseCount = "100",
                LicenseType = "database",
                RandomString = "random",
                LicenseGeneratorDate = "date",
                LicenseActionType = "action",
                LicenseAction = "renewal",
                SupportPlan = "support",
                Request = "tool",
                CompanyName = "request",
                AuthenticationId = "company"
            };
            _mockMapper.Setup(m => m.Map<LicenseDto>(It.IsAny<string[]>())).Returns(expectedLicenseDto);

            // Act
            var result = await _controller.GetLicenseGeneratorKeyById(id, type, renewalType);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task GetLicenseGeneratorKeyById_LicenseNotFound_ReturnsJsonError()
        {
            // Arrange
            var id = "123";
            var type = "renewal";
            var renewalType = "extend";
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseManagerById(id)).ReturnsAsync((LicenseManagerDetailVm)null);

            // Act
            var result = await _controller.GetLicenseGeneratorKeyById(id, type, renewalType);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":false", json);
            Assert.Contains("\"message\":\"License not found\"", json);
        }

        [Fact]
        public async Task GetLicenseGeneratorKeyById_Exception_ReturnsJsonError()
        {
            // Arrange
            var id = "123";
            var type = "renewal";
            var renewalType = "extend";
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseManagerById(id)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetLicenseGeneratorKeyById(id, type, renewalType);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        // ===== ADDITIONAL TESTS TO COVER UNCOVERED LINES =====

        [Fact]
        public void LoadPartialView_ProcessesLicenseDetailsSuccessfully()
        {
            // Arrange
            var name = "TestPartialView";
            var licenseDetails = new List<LicenseManagerDetailViewVm>
            {
                new LicenseManagerDetailViewVm { Id = "1" },
                new LicenseManagerDetailViewVm { Id = "2" }
            };
            _mockProvider.Setup(p => p.LicenseManager.LicenseManagerDetailView()).ReturnsAsync(licenseDetails);

            // Act
            var result = _controller.LoadPartialView(name);

            // Assert
            var partialViewResult = Assert.IsType<PartialViewResult>(result);
            Assert.Equal(name, partialViewResult.ViewName);
            var model = Assert.IsType<BaseLicenseViewModel>(partialViewResult.Model);
            Assert.Equal(licenseDetails, model.LicenseManagerDetails);
        }

        [Fact]
        public void LoadPartialView_HandlesExceptionAndReturnsRedirect()
        {
            // Arrange
            var name = "TestPartialView";
            _mockProvider.Setup(p => p.LicenseManager.LicenseManagerDetailView()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = _controller.LoadPartialView(name);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_ExistingLicenseFoundWithValidTool_UpdatesLicense()
        {
            // Arrange
            var licenseKey = "test*host*ip*mac*100*database*random*date*action*renewal*support*tool*request*company";
            var encryptedLicenseKey = SecurityHelper.Encrypt(licenseKey);
            var viewModel = new BaseLicenseViewModel { LicenseKey = encryptedLicenseKey };

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create initially
            var collection = new FormCollection(dic);

            // Setup HttpContext and Request.Form
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Form = collection;
            _controller.ControllerContext.HttpContext = httpContext;

            // Mock existing license found
            var existingLicense = new Application.Features.LicenseManager.Queries.GetLicenseByPONumber.GetLicenseByPONumberVm
            {
                Id = "existing-123"
            };
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseDetailsByPoNumber("test")).ReturnsAsync(existingLicense);

            var updateCommand = new UpdateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<UpdateBaseLicenseCommand>(viewModel)).Returns(updateCommand);
            _mockProvider.Setup(p => p.LicenseManager.UpdateBaseLicense(updateCommand))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "License updated successfully" });

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"message\":\"License updated successfully\"", json);
        }



        [Fact]
        public async Task CreateOrUpdate_ExistingLicenseFoundWithoutTool_ReturnsInvalidLicenseKey()
        {
            // Arrange - Test for lines 91-95: when licenses != null and poNumber[11] doesn't contain "tool"
            var licenseKey = "test*host*ip*mac*100*database*random*date*action*renewal*support*basic*request*company";
            var encryptedLicenseKey = SecurityHelper.Encrypt(licenseKey);

            var viewModel = new BaseLicenseViewModel { LicenseKey = encryptedLicenseKey, Id = string.Empty };

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create initially
            var collection = new FormCollection(dic);

            // Setup HttpContext and Request.Form
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Form = collection;
            _controller.ControllerContext.HttpContext = httpContext;

            // Mock existing license found - this ensures licenses != null (line 88)
            var existingLicense = new Application.Features.LicenseManager.Queries.GetLicenseByPONumber.GetLicenseByPONumberVm
            {
                Id = "existing-123"
            };
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseDetailsByPoNumber("test")).ReturnsAsync(existingLicense);

            // Note: We don't mock the UpdateBaseLicense method because the controller should not reach that point
            // The controller should return "Invalid license key" before attempting to update

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);

            // This should hit lines 91-95: existing license found but no "tool" in license key
            Assert.Contains("\"success\":false", json);
            Assert.Contains("\"message\":\"Invalid license key\"", json);
        }

        [Fact]
        public async Task CreateOrUpdate_ExistingLicenseFoundWithTool_UpdatesLicense()
        {
            // Arrange - Test the positive path for lines 91-95: when licenses != null and poNumber[11] contains "tool"
            var licenseKey = "test*host*ip*mac*100*database*random*date*action*renewal*support*tool*request*company";
            var encryptedLicenseKey = SecurityHelper.Encrypt(licenseKey);
            var viewModel = new BaseLicenseViewModel { LicenseKey = encryptedLicenseKey, Id = string.Empty };

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create initially
            var collection = new FormCollection(dic);

            // Setup HttpContext and Request.Form
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Form = collection;
            _controller.ControllerContext.HttpContext = httpContext;

            // Mock existing license found - this ensures licenses != null (line 88)
            var existingLicense = new Application.Features.LicenseManager.Queries.GetLicenseByPONumber.GetLicenseByPONumberVm
            {
                Id = "existing-123"
            };
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseDetailsByPoNumber("test")).ReturnsAsync(existingLicense);

            // Mock mapper for BaseLicense mapping (needed for the controller logic)
            var updateCommand = new Application.Features.LicenseManager.Command.BaseLicense.Update.UpdateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<Application.Features.LicenseManager.Command.BaseLicense.Update.UpdateBaseLicenseCommand>(It.IsAny<BaseLicenseViewModel>())).Returns(updateCommand);

            // Mock successful update
            var updateResult = new Application.Features.LicenseManager.Command.BaseLicense.Update.UpdateBaseLicenseResponse
            {
                Message = "License updated successfully"
            };
            _mockProvider.Setup(p => p.LicenseManager.UpdateBaseLicense(It.IsAny<UpdateBaseLicenseCommand>())).ReturnsAsync(updateResult);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"message\":\"License updated successfully\"", json);
        }

        [Fact]
        public async Task CreateOrUpdate_NoExistingLicenseWithTool_CreatesLicense()
        {
            // Arrange - Test the positive path for lines 104-108: when licenseId.IsNullOrWhiteSpace() and poNumber[11] contains "tool"
            var licenseKey = "test*host*ip*mac*100*database*random*date*action*renewal*support*tool*request*company";
            var encryptedLicenseKey = SecurityHelper.Encrypt(licenseKey);
            var viewModel = new BaseLicenseViewModel { LicenseKey = encryptedLicenseKey, Id = string.Empty };

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create - this ensures licenseId.IsNullOrWhiteSpace() is true
            var collection = new FormCollection(dic);

            // Setup HttpContext and Request.Form
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Form = collection;
            _controller.ControllerContext.HttpContext = httpContext;

            // Mock no existing license found (returns null) - this ensures licenses == null
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseDetailsByPoNumber("test")).ReturnsAsync((Application.Features.LicenseManager.Queries.GetLicenseByPONumber.GetLicenseByPONumberVm)null);

            // Mock mapper for BaseLicense mapping (needed for the controller logic)
            var createCommand = new Application.Features.LicenseManager.Command.BaseLicense.Create.CreateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<Application.Features.LicenseManager.Command.BaseLicense.Create.CreateBaseLicenseCommand>(It.IsAny<BaseLicenseViewModel>())).Returns(createCommand);

            // Mock successful creation
            var createResult = new Application.Features.LicenseManager.Command.BaseLicense.Create.CreateBaseLicenseResponse
            {
                Message = "License created successfully"
            };
            _mockProvider.Setup(p => p.LicenseManager.CreateBaseLicense(It.IsAny<CreateBaseLicenseCommand>())).ReturnsAsync(createResult);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"message\":\"License created successfully\"", json);
        }

        [Fact]
        public async Task List_FiltersCompaniesBasedOnChildLicenses()
        {
            // Arrange
            var licenseDetails = new List<LicenseManagerDetailViewVm>();
            var licenseInfoDetails = new List<LicenseInfoListVm>();

            var companyNames = new List<CompanyListVm>
            {
                new CompanyListVm { Id = "company1", IsParent = false },
                new CompanyListVm { Id = "company2", IsParent = false },
                new CompanyListVm { Id = "company3", IsParent = true } // This should be filtered out by Where clause
            };

            var licenseMangerPoVm = new List<LicenseManagerNameVm>
            {
                new LicenseManagerNameVm { CompanyId = "company1", PoNumber = "PO123" }
            };

            var childLicenses = new List<ChildLicenseDetailByParentIdVm>
            {
                new ChildLicenseDetailByParentIdVm { CompanyId = "company1" }
            };

            _mockProvider.Setup(p => p.LicenseManager.LicenseManagerDetailView()).ReturnsAsync(licenseDetails);
            _mockProvider.Setup(p => p.LicenseInfo.GetLicenseInfo()).ReturnsAsync(licenseInfoDetails);
            _mockProvider.Setup(p => p.Company.GetCompanies()).ReturnsAsync(companyNames);
            _mockProvider.Setup(p => p.LicenseManager.GetAllPoNumbers()).ReturnsAsync(licenseMangerPoVm);
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseByParentIdAndParentPoNumber("company1", "PO123")).ReturnsAsync(childLicenses);

            _mockMapper.Setup(m => m.Map<List<SelectListItem>>(It.IsAny<List<CompanyViewModel>>()))
                .Returns(new List<SelectListItem>());

            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            var model = Assert.IsType<BaseLicenseViewModel>(viewResult.Model);
            Assert.Equal(licenseDetails, model.LicenseManagerDetails);
            Assert.Equal(licenseInfoDetails, model.LicenseInfoLists);

            // Verify that GetLicenseByParentIdAndParentPoNumber was called
            _mockProvider.Verify(p => p.LicenseManager.GetLicenseByParentIdAndParentPoNumber("company1", "PO123"), Times.Once);
        }

        [Fact]
        public async Task Delete_SuccessfulDeletion_ReturnsRedirectWithSuccessMessage()
        {
            // Arrange
            var id = "123";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockProvider.Setup(p => p.LicenseManager.DeleteAsync(id)).ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectToActionResult.ActionName);

            // Verify that DeleteAsync was called
            _mockProvider.Verify(p => p.LicenseManager.DeleteAsync(id), Times.Once);
        }

        [Fact]
        public async Task DerivedLicenseCreateOrUpdate_ValidationException_ReturnsRedirectWithWarning()
        {
            // Arrange
            var viewModel = new DerivedLicenseViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create
            var collection = new FormCollection(dic);

            // Setup HttpContext and Request.Form
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Form = collection;
            _controller.ControllerContext.HttpContext = httpContext;

            var createCommand = new CreateDerivedLicenseCommand();
            _mockMapper.Setup(m => m.Map<CreateDerivedLicenseCommand>(viewModel)).Returns(createCommand);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockProvider.Setup(p => p.LicenseManager.CreateDerivedLicense(createCommand)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.DerivedLicenseCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);

            // Verify that CreateDerivedLicense was called and exception was handled
            _mockProvider.Verify(p => p.LicenseManager.CreateDerivedLicense(createCommand), Times.Once);
        }

        [Fact]
        public async Task BaseLicenseCreateOrUpdate_InvalidLicenseKeyException_ReturnsRedirectWithSpecificWarning()
        {
            // Arrange
            var viewModel = new BaseLicenseViewModel { Id = "" }; // Empty for create

            var createCommand = new CreateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<CreateBaseLicenseCommand>(viewModel)).Returns(createCommand);
            _mockProvider.Setup(p => p.LicenseManager.CreateBaseLicense(createCommand))
                .ThrowsAsync(new Exception("Index was outside the bounds of the array"));

            // Act
            var result = await _controller.BaseLicenseCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);

            // Verify that CreateBaseLicense was called and exception was handled
            _mockProvider.Verify(p => p.LicenseManager.CreateBaseLicense(createCommand), Times.Once);
        }

        [Fact]
        public async Task BaseLicenseCreateOrUpdate_Base64Exception_ReturnsRedirectWithSpecificWarning()
        {
            // Arrange
            var viewModel = new BaseLicenseViewModel { Id = "" }; // Empty for create

            var createCommand = new CreateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<CreateBaseLicenseCommand>(viewModel)).Returns(createCommand);
            _mockProvider.Setup(p => p.LicenseManager.CreateBaseLicense(createCommand))
                .ThrowsAsync(new Exception("The input is not a valid Base-64 string"));

            // Act
            var result = await _controller.BaseLicenseCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);

            // Verify that CreateBaseLicense was called and exception was handled
            _mockProvider.Verify(p => p.LicenseManager.CreateBaseLicense(createCommand), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_NoExistingLicenseWithoutTool_ReturnsInvalidLicenseKey()
        {
            // Arrange - Test for lines 104-108: when licenseId.IsNullOrWhiteSpace() is true and no "tool" in license key
            var licenseKey = "test*host*ip*mac*100*database*random*date*action*renewal*support*basic*request*company";
            var encryptedLicenseKey = SecurityHelper.Encrypt(licenseKey);
            var viewModel = new BaseLicenseViewModel { LicenseKey = encryptedLicenseKey, Id = string.Empty };

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create - this ensures licenseId.IsNullOrWhiteSpace() is true
            var collection = new FormCollection(dic);

            // Setup HttpContext and Request.Form
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Form = collection;
            _controller.ControllerContext.HttpContext = httpContext;

            // Mock no existing license found (returns null) - this ensures licenses == null
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseDetailsByPoNumber("test")).ReturnsAsync((Application.Features.LicenseManager.Queries.GetLicenseByPONumber.GetLicenseByPONumberVm)null);

            // Mock mapper for BaseLicense mapping (needed for the controller logic)
            var createCommand = new Application.Features.LicenseManager.Command.BaseLicense.Create.CreateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<Application.Features.LicenseManager.Command.BaseLicense.Create.CreateBaseLicenseCommand>(It.IsAny<BaseLicenseViewModel>())).Returns(createCommand);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);

            // This should hit lines 104-108: no existing license and no "tool" in license key
            Assert.Contains("\"success\":false", json);
            Assert.Contains("\"message\":\"Invalid license key\"", json);
        }

        [Fact]
        public async Task CreateOrUpdate_ValidationException_ReturnsJsonWithValidationError()
        {
            // Arrange - Test for lines 125-130: ValidationException catch block
            var licenseKey = "test*host*ip*mac*100*database*random*date*action*renewal*support*tool*request*company";
            var encryptedLicenseKey = SecurityHelper.Encrypt(licenseKey);
            var viewModel = new BaseLicenseViewModel { LicenseKey = encryptedLicenseKey };

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create
            var collection = new FormCollection(dic);

            // Setup HttpContext and Request.Form
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Form = collection;
            _controller.ControllerContext.HttpContext = httpContext;

            // Mock no existing license found
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseDetailsByPoNumber("test")).ReturnsAsync((Application.Features.LicenseManager.Queries.GetLicenseByPONumber.GetLicenseByPONumberVm)null);

            // Setup mapper and provider to throw ValidationException
            var createCommand = new CreateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<CreateBaseLicenseCommand>(viewModel)).Returns(createCommand);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("LicenseKey", "License key is required"));
            var validationException = new ValidationException(validationResult);
            _mockProvider.Setup(p => p.LicenseManager.CreateBaseLicense(createCommand)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            // The controller returns uppercase when it's an exception response
            Assert.True(json.Contains("\"success\":false") || json.Contains("\"Success\":false"));
            Assert.True(json.Contains("\"message\":") || json.Contains("\"Message\":"));
        }

        [Fact]
        public async Task CreateOrUpdate_GeneralException_ReturnsJsonWithGeneralError()
        {
            // Arrange - Test for lines 141-142: General exception catch block
            var licenseKey = "test*host*ip*mac*100*database*random*date*action*renewal*support*tool*request*company";
            var encryptedLicenseKey = SecurityHelper.Encrypt(licenseKey);
            var viewModel = new BaseLicenseViewModel { LicenseKey = encryptedLicenseKey };

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create
            var collection = new FormCollection(dic);

            // Setup HttpContext and Request.Form
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Form = collection;
            _controller.ControllerContext.HttpContext = httpContext;

            // Mock no existing license found
            _mockProvider.Setup(p => p.LicenseManager.GetLicenseDetailsByPoNumber("test")).ReturnsAsync((Application.Features.LicenseManager.Queries.GetLicenseByPONumber.GetLicenseByPONumberVm)null);

            // Setup mapper and provider to throw a general exception (not ValidationException and not containing specific messages)
            var createCommand = new CreateBaseLicenseCommand();
            _mockMapper.Setup(m => m.Map<CreateBaseLicenseCommand>(viewModel)).Returns(createCommand);

            // Throw a general exception that doesn't contain "Index was outside the bounds of the array" or "valid Base-64"
            var generalException = new InvalidOperationException("Database connection failed");
            _mockProvider.Setup(p => p.LicenseManager.CreateBaseLicense(createCommand)).ThrowsAsync(generalException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);

            // This should hit lines 141-142: general exception handling
            // The controller should call ex.GetJsonException() which returns the exception in JSON format
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("\"Message\":", json);
            Assert.Contains("Database connection failed", json);
        }
    }
}
