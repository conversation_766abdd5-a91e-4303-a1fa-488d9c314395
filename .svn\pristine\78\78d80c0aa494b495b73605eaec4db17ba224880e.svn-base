﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkFlowRepository : BaseRepository<Workflow>, IWorkflowRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public WorkFlowRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<Workflow>> ListAllAsync()
    {
        var workflowPermission = await GetWorkflowPermissions("workflow");

        var workflow = _loggedInUserService.IsParent
           ? await base.FindByFilterAsync(x =>
               (x.CreatedBy.Equals(_loggedInUserService.UserId) && !x.IsPublish) || (x.IsPublish))
           : await base.FindByFilterAsync(x =>
               x.CreatedBy.Equals(_loggedInUserService.UserId) && x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
               (x.IsPublish || (!x.IsPublish)));

        return workflowPermission.Count > 0
            ? workflow.Concat(await base.FindByFilterAsync(x => workflowPermission.Contains(x.ReferenceId))).DistinctBy(x => x.ReferenceId).ToList()
            : workflow;
    }
    public async Task<IReadOnlyList<Workflow>> ListAllAsyncForDashboardView()
    {
        var workflowPermission = await GetWorkflowPermissions("workflow");

        var workflow = await(_loggedInUserService.IsParent 
           ?  Entities.AsNoTracking()
            .DescOrderById().Where(x =>
                (x.CreatedBy.Equals(_loggedInUserService.UserId) && (!x.IsPublish || x.IsPublish))
                || x.IsPublish)

           :  Entities.AsNoTracking().DescOrderById().Where(x =>
                x.CreatedBy.Equals(_loggedInUserService.UserId) && 
                 (x.CreatedBy.Equals(_loggedInUserService.UserId) && (!x.IsPublish || x.IsPublish))
                || x.IsPublish)).Select(x=>new Workflow
               {
                   Id = x.Id,
                   ReferenceId = x.ReferenceId,
                   Name=x.Name,
               }).ToListAsync();
        return workflowPermission.Count > 0
            ? workflow.Concat(await base.FindByFilterAsync(x => workflowPermission.Contains(x.ReferenceId))).DistinctBy(x => x.ReferenceId).ToList()
            : workflow;

    }

    public override async Task<Workflow> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? await base.GetByReferenceIdAsync(id)
            : FindByFilterAsync(workflow =>
                    workflow.ReferenceId.Equals(id) && workflow.CompanyId.Equals(_loggedInUserService.CompanyId)).Result.SingleOrDefault();

    }

    public override IQueryable<Workflow> GetPaginatedQuery()
    {
        return _loggedInUserService.IsParent
            ? _dbContext.WorkFlows.Active()
                .Where(x => (x.CreatedBy.Equals(_loggedInUserService.UserId) && !x.IsPublish) || x.IsPublish)
                .AsNoTracking()
                .OrderByDescending(x => x.Id)
            : _dbContext.WorkFlows.Active()
                .Where(x => x.CreatedBy.Equals(_loggedInUserService.UserId) &&
                            x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                            ((!_loggedInUserService.IsParent && x.IsPublish) || !x.IsPublish))
                .AsNoTracking()
                .OrderByDescending(x => x.Id);
    }

    public async Task<List<Workflow>> GetWorkflowNames()
    {
        var workflowPermission = await GetWorkflowPermissions("workflow");

        var workflow = _loggedInUserService.IsParent
            ? await _dbContext.WorkFlows.Active()
                .Where(x => (x.CreatedBy.Equals(_loggedInUserService.UserId) && !x.IsPublish) || x.IsPublish)
                .Select(x => new Workflow
                {
                    ReferenceId = x.ReferenceId,
                    Name = x.Name,
                    IsLock = x.IsLock,
                    IsPublish = x.IsPublish,
                    IsVerify = x.IsVerify,
                    CreatedBy = x.CreatedBy
                })
                .OrderBy(x => x.Name).ToListAsync()
            : await _dbContext.WorkFlows.Active()
                .Where(x => x.CreatedBy.Equals(_loggedInUserService.UserId) &&
                            x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                            ((!_loggedInUserService.IsParent && x.IsPublish) || !x.IsPublish))
                .Select(x => new Workflow
                {
                    ReferenceId = x.ReferenceId,
                    Name = x.Name,
                    IsLock = x.IsLock,
                    IsPublish = x.IsPublish,
                    IsVerify = x.IsVerify,
                    CreatedBy = x.CreatedBy
                })
                .OrderBy(x => x.Name).ToListAsync();

        return workflowPermission.Count > 0
            ? workflow.Concat(await _dbContext.WorkFlows.Active().Where(x => workflowPermission.Contains(x.ReferenceId))
                           .Select(x => new Workflow
                           {
                               ReferenceId = x.ReferenceId,
                               Name = x.Name,
                               IsLock = x.IsLock,
                               IsPublish = x.IsPublish,
                               IsVerify = x.IsVerify,
                               CreatedBy = x.CreatedBy
                           }).ToListAsync()).DistinctBy(x => x.ReferenceId).ToList()
            : workflow;

    }

    public Task<bool> IsWorkflowNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.WorkFlows.Any(e => e.Name.Equals(name)))
            : Task.FromResult(_dbContext.WorkFlows.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public async Task<List<Workflow>> GetWorkflowPropertiesByServerId(string serverId)
    {
        return await _dbContext.WorkFlows.Active().Where(x => x.Properties.Contains(serverId)).ToListAsync();
    }

    public async Task<List<Workflow>> GetWorkflowPropertiesByDatabaseId(string databaseId)
    {
        return await _dbContext.WorkFlows.Active().Where(x => x.Properties.Contains(databaseId)).ToListAsync();
    }

    public async Task<List<Workflow>> GetWorkflowPropertiesByReplicationId(string replicationId)
    {
        return await _dbContext.WorkFlows.Active().Where(x => x.Properties.Contains(replicationId)).ToListAsync();
    }

    public Task<bool> IsWorkflowNameUnique(string name)
    {
        var matches = _dbContext.WorkFlows.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }

    //public Task<List<ManageWorkflowModel>> GetDetailsListAllAsync()
    //{
    //    var workflowList = from wf in _dbContext.WorkFlows
    //                       join usr in _dbContext.WorkflowInfraObjects on wf.ReferenceId equals usr.WorkflowId
    //                       select new ManageWorkflowModel
    //                       {
    //                           Id = wf.ReferenceId,
    //                           workflowName = wf.Name,
    //                           InfraObjectName = usr.InfraObjectName,
    //                           CreatedDate = wf.CreatedDate,
    //                           CreatedBy = wf.CreatedBy,
    //                           IsFourEye = wf.IsFourEye
    //                       };
    //    return Task.FromResult(workflowList.ToList());
    //}

    public async Task<List<string>> GetWorkflowPermissions(string type)
   {
        var ids = new List<string>();

        var workflowPermission =
           await _dbContext.WorkflowPermissions
            .Active().AsNoTracking().Where(x => x.Type.ToLower().Equals(type.Trim().ToLower()))
            .Select(x => new WorkflowPermission
            {
                ReferenceId = x.ReferenceId,
                UserAccess = x.UserAccess,
                UserProperties = x.UserProperties,
                AccessType = x.AccessType,
                ScheduleTime = x.ScheduleTime,
                Type = x.Type
            })
            .ToListAsync();

        if (workflowPermission.Any())
        {
            var userAccessList = workflowPermission
                .Where(x => x.UserAccess.ToLower().Equals("users")).ToList();

            var userGroupAccessList = workflowPermission
                .Where(x => x.UserAccess.ToLower().Equals("usergroup")).ToList();

            var dateTimeFormat = new List<string> { "dd/MM/yyyy HH:mm:ss tt", "dd/MM/yyyy hh:mm:ss tt" };

            if (userAccessList.Any())
            {
                var users = userAccessList
                    .Where(x => x.UserProperties.Contains(_loggedInUserService.UserId))
                    .Where(x =>
                    {
                        if (x.AccessType.ToLower() is "temporary" && x.ScheduleTime.IsNotNullOrWhiteSpace())
                        {
                            var any = JArray.Parse(x.ScheduleTime)
                                .Any(item =>
                                {
                                    var endDate = item["endDate"]?.ToString();

                                    var endDateTime = DateTime.Now;

                                    var isValid = dateTimeFormat.Any(format =>
                                    DateTime.TryParseExact(endDate, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out endDateTime));

                                   
                                    if (!isValid) return false;

                                    return DateTime.Now < endDateTime;
                                });

                            return any;
                        }

                        return x.AccessType.ToLower() is "permanent";

                    }).Select(x => x.AccessProperties).ToList();

                foreach (var per in users)
                {
                    dynamic accessProperties = JsonConvert.DeserializeObject(per);

                    if (accessProperties != null)
                    {
                        foreach (var item in accessProperties!)
                        {
                            ids.Add(item["id"].ToString());
                        }
                    }
                }
            }

            if (userGroupAccessList.Any())
            {
                foreach (var userGroup in userGroupAccessList)
                {
                    var users = await _dbContext.UserGroup.Active()
                        .AsNoTracking()
                        .Where(x => userGroup.UserProperties.Contains(x.ReferenceId) && x.UserProperties.Contains(_loggedInUserService.UserId))
                        .AnyAsync();

                    if (users)
                    {
                        if (userGroup.AccessType.ToLower() is "temporary" && userGroup.ScheduleTime.IsNotNullOrWhiteSpace())
                        {
                            var scheduleTime = JArray.Parse(userGroup.ScheduleTime);

                            JToken endDateToken = scheduleTime[0].SelectToken("endDate")?.ToString();

                            var endDate = endDateToken?.ToString();

                            var endDateTime = DateTime.Now; 

                            var isValid = dateTimeFormat.Any(format =>
                            DateTime.TryParseExact(endDate, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out endDateTime));

                            if (isValid &&DateTime.Now <= endDateTime)
                            {
                                dynamic accessProperties = JsonConvert.DeserializeObject(userGroup.AccessProperties);

                                foreach (var item in accessProperties!)
                                {
                                    ids.Add(item["id"].ToString());
                                }
                            }
                        }

                        if (userGroup.AccessType.ToLower() is "permanent")
                        {
                            dynamic accessProperties = JsonConvert.DeserializeObject(userGroup.AccessProperties);

                            foreach (var item1 in accessProperties!)
                            {
                                ids.Add(item1["id"].ToString());
                            }
                        }

                    }
                }
            }
        }
        return ids.Count == 0 ? new List<string>() : ids;
    }

}