﻿namespace ContinuityPatrol.Shared.Core.Filters;

public class PaginatedFilter : BaseFilter
{
    public PaginatedFilter()
    {
        PageNumber = 1;
        PageSize = 10000;
    }

    public PaginatedFilter(int pageNumber, int pageSize)
    {
        PageNumber = pageNumber < 1 ? 1 : pageNumber;
        PageSize = pageSize > 10000 ? 10000 : pageSize;
    }

    public int PageNumber { get; set; }

    public int PageSize { get; set; }

    public string SearchString { get; set; }
}