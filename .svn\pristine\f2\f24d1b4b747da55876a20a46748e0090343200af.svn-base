using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.CyberComponentModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.CyberComponent.Queries.GetPaginatedList;

public class GetCyberComponentPaginatedListQueryHandler : IRequestHandler<GetCyberComponentPaginatedListQuery,
    PaginatedResult<CyberComponentListVm>>
{
    private readonly ICyberComponentRepository _cyberComponentRepository;
    private readonly IMapper _mapper;

    public GetCyberComponentPaginatedListQueryHandler(IMapper mapper,
        ICyberComponentRepository cyberComponentRepository)
    {
        _mapper = mapper;
        _cyberComponentRepository = cyberComponentRepository;
    }

    public async Task<PaginatedResult<CyberComponentListVm>> Handle(GetCyberComponentPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new CyberComponentFilterSpecification(request.SearchString);

        var queryable =await _cyberComponentRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var cyberComponentList = _mapper.Map<PaginatedResult<CyberComponentListVm>>(queryable);
           
        return cyberComponentList;

        //var queryable = _cyberComponentRepository.GetPaginatedQuery();

        //var productFilterSpec = new CyberComponentFilterSpecification(request.SearchString);

        //var cyberComponentList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<CyberComponentListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return cyberComponentList;
    }
}