﻿using ContinuityPatrol.Application.Features.User.Commands.Create;
using ContinuityPatrol.Application.Features.User.Commands.ResetPassword;
using ContinuityPatrol.Application.Features.User.Commands.Update;
using ContinuityPatrol.Application.Features.User.Commands.UpdatePassword;
using ContinuityPatrol.Application.Features.User.Commands.UserLock;
using ContinuityPatrol.Application.Features.User.Events.PaginatedView;
using ContinuityPatrol.Application.Features.User.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.UserInfo.Commands.Update;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByBusinessService;
using ContinuityPatrol.Domain.ViewModels.UserModel.ChangePasswordModels;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Application.Features.User.Commands.UserUnLock;
using DocumentFormat.OpenXml.Office2010.Excel;
using MimeKit;
using ContinuityPatrol.Application.Features.User.Queries.GetDomainGroup;


namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class UserController : BaseController
{

    private readonly IPublisher _publisher;

    private readonly IDataProvider _dataProvider;

    private readonly IMapper _mapper;


    private readonly ILogger<UserController> _logger;

    private static readonly int MaximumUnlockAttempt = 3;

    public int UnlockAttempt => Convert.ToInt32(WebHelper.CurrentSession.Get<string>("UnlockAttempt") ?? "0");

    public UserController(IPublisher publisher, IDataProvider dataProvider, IMapper mapper, ILogger<UserController> logger)
    {
        _publisher = publisher;
        _dataProvider = dataProvider;
        _mapper = mapper;
        _logger = logger;

    }

    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in User");

        await _publisher.Publish(new UserPaginatedEvent());
       
      //  var companyNames = await _dataProvider.Company.GetCompanies();
       var companyNames = await _dataProvider.Company.GetCompanyNamesOnLogin();


        var userViewModel = new UserViewModel
        {
           
            Companies = companyNames,
            
        };

        return View(userViewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Admin.CreateAndEdit)]
    public async Task<JsonResult> CreateOrUpdate(UserViewModel createUser)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in User");

        try
        {

            if (createUser.Id.IsNullOrWhiteSpace())
            {

                var user = _mapper.Map<CreateUserCommand>(createUser);
                user.IsReset = true;
                var result = await _dataProvider.User.CreateAsync(user);
                _logger.LogDebug($"Creating User '{user.LoginName}'");
                return Json(new { Success = true, Message = result });
                //TempData.NotifySuccess(result.Message);
            }
            else
            {
                var user = _mapper.Map<UpdateUserCommand>(createUser);
                var result = await _dataProvider.User.UpdateAsync(user);
                _logger.LogDebug($"Updating User '{user.LoginName}'");
                return Json(new { Success = true, Message = result });
                //TempData.NotifySuccess(result.Message);
            }
            //_logger.LogDebug("CreateOrUpdate operation completed successfully in user, returning view.");
            //return RedirectToAction("List");

        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on user page: {ex.ValidationErrors.FirstOrDefault()}");

            return ex.GetJsonException();
            //TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());
            //return Json(new { Success = true, Message = domainList });

            //return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on user page while processing the request for create or update.", ex);

            return ex.GetJsonException();
            //TempData.NotifyWarning(ex.GetMessage());
            //return Json(new { Success = true, Message = domainList });

            //return RedirectToAction("List");
        }

    }

    [HttpGet]
    public async Task<bool> IsLoginNameExist(string loginName, string id)
    {
        _logger.LogDebug("Entering IsLoginNameExist method in User");

        try
        {
            var nameExist = await _dataProvider.User.IsLoginNameExist(loginName, id);
            _logger.LogDebug("Returning result for IsLoginNameExist on User");
            return nameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on user page while checking if login name exists for : {loginName}.", ex);
            return false;
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetDomains()
    {
        _logger.LogDebug("Entering GetDomains method in User");

        try
        {
            var domainList = await _dataProvider.User.GetDomainList();
            _logger.LogDebug("Successfully retrieved domain list on User page");
            return Json(new { Success = true, Message = domainList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on user page while retrieving the domain list.", ex);
            return Json(new { Success = false, Message = "Domain user's not found." });
        }

    }

    [HttpGet]
    public async Task<List<string>> GetDomainUsers(string domainName, string domainUserName)
    {
        _logger.LogDebug("Entering GetDomainUsers method in User");
        try
        {
            var domainUsers = await _dataProvider.User.GetDomainUsers(domainName, domainUserName);
            _logger.LogDebug($"Successfully retrieved domain user list by domainName'{domainName}' and userName '{domainUserName}' in User");
            return domainUsers;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on user page while retrieving the domain users list by search value '{domainUserName}'.", ex);
            return new List<string>();
        }

    }

    [HttpGet]
    public async Task<List<string>> GetDomainGroups(string domainName, string domainUserName)
    {
        _logger.LogDebug("Entering GetDomainUsers method in User");
        try
        {
            var domainUsers = await _dataProvider.User.GetDomainGroups(domainName, domainUserName);
            _logger.LogDebug($"Successfully retrieved domain group list by domainName'{domainName}' in User");
            return domainUsers;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on user page while retrieving the domain group list by search value '{domainUserName}'.", ex);
            return new List<string>();
        }

    }


    [HttpGet]
    public async Task<GetUserInfraObjectByBusinessServiceVm> GetAllInfraObjectList(string companyId)
    {
        _logger.LogDebug("Entering GetAllInfraObjectList method in User");

        try
        {
            var bsInfraDetails = await _dataProvider.UserInfraObject.GetUserInfraObjects(companyId);
            _logger.LogDebug($"Successfully retrieved infraObject list by companyId '{companyId}' in User");
            return bsInfraDetails;
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on user page while retrieving infraObjectList by companyId", ex);
            return new GetUserInfraObjectByBusinessServiceVm();
        }
    }


    //[HttpGet]
    //public async Task<GetUserInfraObjectByBusinessServiceVm> GetUserInfraByCompany(string data)
    //{
    //    _logger.LogDebug("Entering GetUserInfraByCompany method in User");

    //    var companyInfra = await _dataProvider.UserInfraObject.GetUserInfraObjects(data);
    //    return companyInfra;
    //}

    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in User");
        try
        {
            var userDto = await _dataProvider.User.DeleteAsync(id);
            TempData.NotifySuccess(userDto.Message);
            _logger.LogDebug("Successfully deleted record in User");
            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on user.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    public IActionResult Lock()
    {
        _logger.LogDebug("Entering Lock method in User");

        try
        {
            var loginName = WebHelper.UserSession.LoginName;

            if (loginName.IsNullOrWhiteSpace())
            {
                _logger.LogInformation("LoginName is null or empty in Lock method on User.");
            }

            var useLock = new UserLockCommand
            {
                UserName = WebHelper.UserSession.LoginName,
                AuthenticationType = WebHelper.UserSession.AuthenticationType
            };
            _logger.LogDebug($"Successfully screen locked for user '{loginName}'.");
            return View(useLock);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on lock in user while attempting to lock the screen.", ex);
            TempData.NotifyWarning(ex.Message);
            return RedirectToAction("List", "MonitoringJob", new { Area = "Manage" });
        }
    }

    [HttpPost]
    [AllowAnonymous]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> LockLogin([FromBody] UserLockCommand userLock)
    {
        _logger.LogDebug("Entering LockLogin method in User");
        try
        {
            if (ModelState.IsValid)
            {
                _logger.LogDebug("Model state is valid in LockLogin method on User.");
                userLock.AuthenticationType = WebHelper.UserSession.AuthenticationType;

                if (WebHelper.UserSession.IsUserAuthenticated)
                {
                    _logger.LogDebug("User is authenticated in LockLogin method on User.");

                    if (userLock.AuthenticationType == AuthenticationType.AD.ToString())
                    {
                        userLock.UserName = WebHelper.UserSession.AdUserName;
                    }
                    else
                    {
                        userLock.UserName = LoggedInName.ToLower();
                    }
                    var result = await _dataProvider.User.UsersAuthentication(userLock);
                    var count = IsExceedUnlockAttempt();
                    if (result.Success)
                    {                       
                        _logger.LogDebug($"Removing unlock attempt session for user '{userLock.UserName}' in User.");
                        WebHelper.CurrentSession.Remove("UnlockAttempt");
                        return Json(new { success = true, validCount = count });
                    }
                    else
                    {
                        _logger.LogDebug($"Unlock attempt not exceeded for user '{userLock.UserName}' in User. Returning to lock screen.");                     
                        return Json(new { success = false, validCount = count });
                    }                         
                }
                _logger.LogDebug($"{userLock.UserName}'s authentication has expired in LockLogin method on User. Redirecting to logout.");                
                return Json(new { success = false, validCount = 3 });
            }
            _logger.LogDebug("Model state is invalid in LockLogin method on User. Returning to lock view.");
            return Json(new { success = false, validCount = 2 });     
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occured on lock login method while attempting to unlock screen.", ex);
            return Json(new { success = false, redirectUrl = Url.Action("List", "MonitoringJob", new { area = "Manage" }) });
        }
    }

    [HttpGet]
    public async Task<IActionResult> UserActivityProfile(string startDate, string endDate)
    {
        _logger.LogDebug("Entering UserProfile method in User");

        try
        {
            string loginName;

            if (!string.IsNullOrEmpty(WebHelper.UserSession.AdUserName))
            {
                loginName = WebHelper.UserSession.AdUserName;
            }
            else
            {
                loginName = WebHelper.UserSession.LoginName;
            }

            var userActivityStartDateEndDate = await _dataProvider.UserActivity.GetStartTimeEndTimeByUser(loginName, startDate, endDate);

            var userDtl = new UserViewModel
            {
                GetStartTimeEndTimeByUser = userActivityStartDateEndDate,
            };

            _logger.LogDebug($"Successfully retrieved user detail by startDate '{startDate}' and endDate '{endDate}' for user profile.");

            return Json(new { Success = true, data = userDtl });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on user Profile page while retrieving user details by startDate and endDate for user profile.", ex);
            return ex.GetJsonException();
        }
    }


    [HttpGet]
    [AllowAnonymous]
    public async Task<JsonResult> GetUserLoginName(string loginName)
    {
        _logger.LogDebug("Entering GetUserLoginName method in User");

        try
        {
            var users = await _dataProvider.User.GetUserByLoginName(loginName);
            _logger.LogDebug($"Successfully retrieved user loginName by '{loginName}'");
            return Json(new { Success = true, Message = users });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on user page while retrieving user login name.", ex);
            return Json(new { Success = false, Message = ex.GetMessage() });
        }
    }

    public IActionResult SiteAdminLanding()
    {
        return View();
    }
    //public IActionResult UserProfile()
    //{
    //    _logger.LogDebug("Entering UserProfile method.");

    //    return View();
    //}
    [HttpGet]
    public async Task<ActionResult> UserProfiles(string userId)
    {
        try
        {
            var userProfile = await _dataProvider.User.GetUserProfile(userId);
            return Json(userProfile);
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on server type page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]

    public async Task<JsonResult> UpdateUserProfileImage(UserInfoCommand updateUserInfoCommand)
    {
        _logger.LogDebug("Entering UpdateUserProfileImage method");

        if (updateUserInfoCommand.UserId.IsNullOrWhiteSpace())
            return Json(new { Success = false, Message = "UserId is not valid format", ErrorCode = 0 });
       
        try
        {
           
            var userProfile = await _dataProvider.User.GetUserProfileById(updateUserInfoCommand.UserId);
            userProfile.LogoName = updateUserInfoCommand.LogoName;
            var userProfileUpdate = _mapper.Map<UpdateUserInfoCommand>(userProfile);
            var result = await _dataProvider.User.UpdateUserProfileImage(userProfileUpdate);
            _logger.LogDebug($"UserProfile '{userProfile.UserName}' image updated successfully.");
            return Json(result);
            //TempData.NotifySuccess(result.Message);

            //return RedirectToAction("UserProfile");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while updating user profile image.", ex);
            //TempData.NotifyWarning(ex.Message);
            //return RedirectToAction("UserProfile");
            return Json(ex.GetJsonException());
        }
    }
 
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> UserProfileDelete(UserInfoCommand updateUserInfoCommand)
    {
        _logger.LogDebug("Entering DeleteUserProfileImage method");

        if (updateUserInfoCommand.UserId.IsNullOrWhiteSpace())
            return Json(new { Success = false, Message = "UserId is not valid format", ErrorCode = 0 });

        try
        {

            var userProfile = await _dataProvider.User.GetUserProfileById(updateUserInfoCommand.UserId);
            userProfile.LogoName = updateUserInfoCommand.LogoName;
            var userProfileUpdate = _mapper.Map<UpdateUserInfoCommand>(userProfile);
            var result = await _dataProvider.User.UpdateUserProfileImage(userProfileUpdate);
            _logger.LogDebug($"UserProfile '{userProfile.UserName}' image Delete successfully.");
            if (result.Success)
          {
                result.Message = $"UserProfile '{userProfile.UserName}' image Delete successfully."; 
            }
            return Json(result);
           
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while Delete user profile image.", ex);
            
            return Json(ex.GetJsonException());
        }
    }
   
    public IActionResult ChangePassword()
    {
        return View();
    }

    [HttpPost]

    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ChangePassword(ChangePasswordViewModel changePasswordViewModel)
    {
        _logger.LogDebug("Entering ChangePassword method in User");

        try
        {
            var changePasswordCommand = _mapper.Map<UpdatePasswordCommand>(changePasswordViewModel);

            var result = await _dataProvider.User.UpdateUserPassword(changePasswordCommand);
            _logger.LogDebug($"Successfully changed password for '{changePasswordCommand.LoginName}' in User");
          
            return Json(new { Success = true, data = result });

        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occured on user page while changing the password for '{changePasswordViewModel.LoginName}'.", ex);
            return ex.GetJsonException();
        }
    }

    [AllowAnonymous]

    [HttpGet]
    public JsonResult HashPassword(string loginName, string password)
    {
        _logger.LogDebug("Entering HashPassword method in User");

        try
        {
            if (loginName.IsNullOrWhiteSpace())
            {
                return Json(new { encrypt = SecurityHelper.Encrypt($"{password}") });
            }

            loginName = loginName.ToLower();

            var encryptedValue = SecurityHelper.Encrypt($"{loginName}{password}");
            _logger.LogDebug($"Successfully encrypted password in user page for '{loginName}'");

            return Json(new { encrypt = encryptedValue });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on user page while encrypting the password for '{loginName}'.", ex);
            return Json("");
        }
    }

    [AllowAnonymous]
    [HttpGet]
    public JsonResult HashPasswordForEmail(string password)
    {
        _logger.LogDebug("Entering HashPasswordForEmail method in User");

        try
        {
            if (password.IsNullOrWhiteSpace())
            {
                throw new ArgumentException("Encryption failed: input text must not be null, empty, or whitespace.");
                //return Json(new { encrypt = "" });
            }

            var encryptedValue = SecurityHelper.Encrypt($"{password}");
            _logger.LogDebug($"Successfully encrypted password in user page for email");
            return Json(new { encrypt = encryptedValue });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on user page while encrypting the password for email.", ex);
            return Json("");
        }
    }

    [AllowAnonymous]
    [HttpGet]
    public JsonResult DecryptPassword(string password)
    {
        _logger.LogDebug("Entering DecryptPassword method in User");
        try
        {
            if (password.IsNullOrWhiteSpace())
            {
                return Json(new { decrypt = "" });
            }

            var decryptedValue = SecurityHelper.Decrypt($"{password}");
            _logger.LogDebug($"Successfully decrypted password in user page.");
            return Json(new { decrypt = decryptedValue });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on user page while decrypting the password.", ex);
            return Json("");
        }
    }

    private int IsExceedUnlockAttempt()
    {
        _logger.LogDebug("Entering IsExceedUnlockAttempt method in User");

        var attempt = UnlockAttempt;

        attempt += 1;

        WebHelper.CurrentSession.Set("UnlockAttempt", attempt);
      
        return attempt;
    }
  
    [HttpGet]
    public async Task<JsonResult> GetPagination(GetUserPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in User");
        try
        {
            _logger.LogDebug("Successfully retrieved user paginated list on User");
            return Json(await _dataProvider.User.GetUserPaginatedList(query));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on user page while processing the pagination request.", ex);
            return Json("");
        }
    }
    //ResetPassword
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ResetPassword(UserViewModel resetUser)
    {
        _logger.LogDebug("Entering ResetPassword method in User");

        try
        {
            var resetData = resetUser.userResetViewModal;
            var resetPassword = _mapper.Map<ResetPasswordCommand>(resetData);
            var response = await _dataProvider.User.ResetPassword(resetPassword);
            TempData.NotifySuccess(response.Message);
            _logger.LogDebug($"Successfully resetting the password for '{resetData.LoginName}' in User");
            return RedirectToAction("List", "User", new { area = "Admin" });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on user page while resetting the password for '{resetUser.LoginName}'.", ex);
            TempData.NotifyError(ex.Message);
            return RedirectToAction("List");
        }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Unlock(UserViewModel resetUser)
    {
        _logger.LogDebug("Entering Unlock method in User");

        try
        {
            var unlockData = resetUser.UserUnLockModel;
            var unlockPassword = _mapper.Map<UserUnLockCommand>(unlockData);
            var response = await _dataProvider.User.UserUnLock(unlockPassword);
            TempData.NotifySuccess(response.Message);
            _logger.LogDebug($"Successfully unlocking the user '{resetUser.LoginName}' in User");
            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occured on user page while unlocking the user '{resetUser.LoginName}'.", ex);
            TempData.NotifyError(ex.Message);
            return RedirectToAction("List");
        }
    }
    [HttpGet]
    public async Task<JsonResult> GetCompanyById(string id)
    {
        _logger.LogDebug("Entering GetCompanyById method in User");
        try
        {
            _logger.LogDebug($"Successfully retrieved company by id '{id}' in User.");
            return Json(await _dataProvider.Company.GetCompanyById(id));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occured on user page while retrieving the company by id.", ex);
            return Json("");
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetUserRoleList()
    {
        _logger.LogDebug("Entering GetUserRoleList method in User");

        try
        {
            var list = await _dataProvider.UserRole.GetUserRoles();
            _logger.LogDebug("Successfully retrieved user role list in User.");
            return Json(new { success = true, data = list });

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on user page while retrieving the user role list.", ex);
            return ex.GetJsonException();
        }
    }
    public async Task<bool> IsNewPasswordInLastFive(string userId, string newPassword)
    {
        _logger.LogDebug("Entering IsNewPasswordInLastFive method in User");

        try
        {
            var isNewPassword = await _dataProvider.User.IsNewPasswordInLastFive(userId, newPassword);
            _logger.LogDebug("Returning result for IsNewPasswordInLastFive on User");
            return isNewPassword;
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on while checking if user new password is reused in last five password.", ex);
            return false;
        }

    }
}
