﻿using ContinuityPatrol.Application.Features.DashboardView.Event.ResiliencyDashboardView;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;

namespace ContinuityPatrol.Web.Areas.ResiliencyReadiness.Controllers;

[Area("ResiliencyReadiness")]
public class ResiliencyDashboardController : BaseController
{
    private readonly IDataProvider _provider;
    private readonly ILogger<ResiliencyDashboardController> _logger;
    private readonly IPublisher _publisher;
    public ResiliencyDashboardController(IDataProvider dataProvider, ILogger<ResiliencyDashboardController> logger, IPublisher publisher)
    {
        _provider = dataProvider;
        _logger = logger;
        _publisher = publisher;
    }

    public async Task<IActionResult> List()
    {
        await _publisher.Publish(new ResiliencyDashboardEvent());

        _logger.LogDebug("Entering List method in ResiliencyDashboard");

        return View();
    }

    public async Task<IActionResult> GetReadinessDetails(string businessServiceId)
    {
        _logger.LogDebug("Entering GetReadinessDetails method in ResiliencyDashboard");

        try
        {
            var readinessDetails = string.IsNullOrEmpty(businessServiceId)
                ? await _provider.DrReadyStatus.GetReadinessDetails("")
                : await _provider.DrReadyStatus.GetReadinessDetails(businessServiceId);
            _logger.LogDebug($"Successfully retrieved resiliency dashboard details by businessServiceId '{businessServiceId}'");
            return Json(new { success = true, data = readinessDetails });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on resiliency dashboard page while retrieving readiness details by businessServiceId.", ex);
            return ex.GetJsonException();
        }

    }
    public async Task<IActionResult> GetBusinessServiceDrReadyByBusinessServiceId(string businessServiceId)
    {

        _logger.LogDebug("Entering GetBusinessServiceDrReadyByBusinessServiceId method in service availability page.");
        try
        {
            if (string.IsNullOrEmpty(businessServiceId))
            {
                var drStatusList = await _provider.DrReadyStatus.GetBusinessServiceDrReady("");

                _logger.LogDebug($"Successfully retrieved business service dr ready list by business service id '{businessServiceId}' in service availability page.");

                return Json(new { Success = true, data = drStatusList });
            }
            else
            {
                var drStatusList = await _provider.DrReadyStatus.GetBusinessServiceDrReady(businessServiceId);

                _logger.LogDebug($"Successfully retrieved business service dr ready list by business service id'{businessServiceId}' in service availability page.");

                return Json(new { Success = true, data = drStatusList });
            }

        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on service availability page while retrieving the business service dr ready list.", ex);

            return ex.GetJsonException();
        }

    }

    public async Task<IActionResult> ImpactDetails(string businessServiceId, string heatmapType,bool isAffected)
    {
        _logger.LogDebug("Entering GetReadinessDetails method in ResiliencyDashboard");

        if (heatmapType.IsNullOrWhiteSpace() && businessServiceId.IsNullOrWhiteSpace())
        {

            return Json(new { Success = false, Message = "BusinessServiceId is not valid format", ErrorCode = 0 });
        }

        try
        {
            var downDetails = await _provider.HeatMapStatus.GetHeatMapStatusByType(businessServiceId, heatmapType, isAffected);
           
            _logger.LogDebug("Successfully retrieved impact details in resiliency dashboard page");
            return Json(downDetails);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on resiliency dashboard page while retrieving impact details.", ex);
            return Json("");
        }
    }
}