﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using ContinuityPatrol.Application.Features.UserGroup.Commands.Create;
using ContinuityPatrol.Application.Features.UserGroup.Commands.Update;
using ContinuityPatrol.Application.Features.UserGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.UserGroupModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using AutoMapper;
using MediatR;
using ContinuityPatrol.Domain.ViewModels.UserModel;
using AutoFixture;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Fakes;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class UserGroupControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<ILogger<UserGroupController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private  UserGroupController _controller;

        public UserGroupControllerShould()
        {
            Initialize();
        }
        internal void Initialize()
        {
            _controller = new UserGroupController(
                _mockPublisher.Object,
                _mockMapper.Object,
                _mockLogger.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public void List_ShouldPublishUserGroupPaginatedEvent()
        {

           // _mockPublisher.Setup(p => p.Publish(It.IsAny<UserGroupPaginatedEvent>).Returns(It.IsAny<IActionResult>);
            var result = _controller.List();

            
            Assert.NotNull( result );
            Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidModel_ShouldCreateUserGroup()
        {

            var userGroupModel = new AutoFixture.Fixture().Create<UserGrouplistModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateUserGroupCommand ();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };
            _mockMapper.Setup(m => m.Map<CreateUserGroupCommand>(userGroupModel)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.UserGroup.CreateAsync(createCommand)).ReturnsAsync(response);

           
            var result = await _controller.CreateOrUpdate(userGroupModel);

            
            Assert.IsType<RedirectToActionResult>(result);
            var redirectResult = (RedirectToActionResult)result;
            Assert.Equal("List", redirectResult.ActionName);
            Assert.Equal("UserGroup", redirectResult.ControllerName);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidModel_ShouldUpdateUserGroup()
        {

            var userGroupModel = new AutoFixture.Fixture().Create<UserGrouplistModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateUserGroupCommand ();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };
            _mockMapper.Setup(m => m.Map<UpdateUserGroupCommand>(It.IsAny<UserGrouplistModel>())).Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.UserGroup.UpdateAsync(updateCommand)).ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(userGroupModel);

            
            Assert.IsType<RedirectToActionResult>(result);
            var redirectResult = (RedirectToActionResult)result;
            Assert.Equal("List", redirectResult.ActionName);
            Assert.Equal("UserGroup", redirectResult.ControllerName);
        }

        [Fact]
        public async Task GetUserNames_ShouldReturnUserDetails()
        {
            
            var userDetails = new List<UserNameVm> ();
            _mockDataProvider.Setup(dp => dp.User.GetUserNames()).ReturnsAsync(userDetails);

            
            var result = await _controller.GetUserNames();

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult);
            Assert.Contains("\"Success\":true", json);

        }

        [Fact]
        public async Task IsGroupNameExist_ShouldReturnTrue()
        {
            
            var groupName = "TestGroup";
            var id = "123";
            _mockDataProvider.Setup(dp => dp.UserGroup.IsGroupNameExist(groupName, id)).ReturnsAsync(true);

            
            var result = await _controller.IsGroupNameExist(groupName, id);

            
            Assert.True(result);
        }

        [Fact]
        public async Task IsGroupNameExist_ShouldReturnFalse()
        {
            
            var groupName = "TestGroup";
            var id = "123";
            _mockDataProvider.Setup(dp => dp.UserGroup.IsGroupNameExist(groupName, id)).ReturnsAsync(false);

            
            var result = await _controller.IsGroupNameExist(groupName, id);

            Assert.False(result);
        }

        [Fact]
        public async Task Delete_ShouldDeleteUserGroup()
        {
            
            var id = "123";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockDataProvider.Setup(dp => dp.UserGroup.DeleteAsync(id)).ReturnsAsync(response);

            
            var result = await _controller.Delete(id);

            
            Assert.IsType<RedirectToActionResult>(result);
            var redirectResult = (RedirectToActionResult)result;
            Assert.Equal("List", redirectResult.ActionName);
            Assert.Equal("UserGroup", redirectResult.ControllerName);
        }

        [Fact]
        public async Task Delete_ShouldHandleException()
        {
            
            var id = "123";
            _mockDataProvider.Setup(dp => dp.UserGroup.DeleteAsync(id)).ThrowsAsync(new Exception("Error"));

            
            var result = await _controller.Delete(id);

            
            Assert.IsType<RedirectToActionResult>(result);
            var redirectResult = (RedirectToActionResult)result;
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task GetPaginationList_ShouldReturnPaginatedUserGroups()
        {
            
            var query = new AutoFixture.Fixture().Create<GetUserGroupPaginatedListQuery>();
            var paginatedList = new List<PaginatedResult<UserGroupListVm>> ();
            var userGroupModel = new AutoFixture.Fixture().Create <UserGrouplistModel>();
            _mockMapper.Setup(m => m.Map<GetUserGroupPaginatedListQuery>(userGroupModel)).Returns(query);
           _mockDataProvider.Setup(m=>m.UserGroup.GetPaginatedUserGroups(query)).ReturnsAsync(It.IsAny<PaginatedResult<UserGroupListVm>>);
            var result = await _controller.GetPaginationList(query);
            Assert.NotNull(result);
        }
    }
}
