﻿using ContinuityPatrol.Application.Features.TeamResource.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamResource.Queries;

public class GetTeamResourceDetailQueryHandlerTests : IClassFixture<TeamResourceFixture>
{
    private readonly TeamResourceFixture _teamResourceFixture;

    private readonly Mock<ITeamResourceRepository> _mockTeamResourceRepository;

    private readonly GetTeamResourceDetailQueryHandler _handler;

    public GetTeamResourceDetailQueryHandlerTests(TeamResourceFixture teamResourceFixture)
    {
        _teamResourceFixture = teamResourceFixture;

        _mockTeamResourceRepository = TeamResourceRepositoryMocks.GetTeamResourceRepository(_teamResourceFixture.TeamResources);

        _handler = new GetTeamResourceDetailQueryHandler(_teamResourceFixture.Mapper, _mockTeamResourceRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_TeamResourceDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetTeamResourceDetailQuery { Id = _teamResourceFixture.TeamResources[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<TeamResourceDetailVm>();
        result.Id.ShouldBe(_teamResourceFixture.TeamResources[0].ReferenceId);
        result.TeamMasterId.ShouldBe(_teamResourceFixture.TeamResources[0].TeamMasterId);
        result.TeamMasterName.ShouldBe(_teamResourceFixture.TeamResources[0].TeamMasterName);
        result.ResourceId.ShouldBe(_teamResourceFixture.TeamResources[0].ResourceId);
        result.ResourceName.ShouldBe(_teamResourceFixture.TeamResources[0].ResourceName);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidTeamResourceId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetTeamResourceDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetTeamResourceDetailQuery { Id = _teamResourceFixture.TeamResources[0].ReferenceId }, CancellationToken.None);

        _mockTeamResourceRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}
