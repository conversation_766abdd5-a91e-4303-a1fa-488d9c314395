using ContinuityPatrol.Application.Features.AdPasswordExpire.Events.Create;

namespace ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Create;

public class CreateAdPasswordExpireCommandHandler : IRequestHandler<CreateAdPasswordExpireCommand, CreateAdPasswordExpireResponse>
{
    private readonly IAdPasswordExpireRepository _adPasswordExpireRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateAdPasswordExpireCommandHandler(IMapper mapper, IAdPasswordExpireRepository adPasswordExpireRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _adPasswordExpireRepository = adPasswordExpireRepository;
    }

    public async Task<CreateAdPasswordExpireResponse> Handle(CreateAdPasswordExpireCommand request, CancellationToken cancellationToken)
    {
        var adPasswordExpire = _mapper.Map<Domain.Entities.AdPasswordExpire>(request);

        adPasswordExpire = await _adPasswordExpireRepository.AddAsync(adPasswordExpire);

        var response = new CreateAdPasswordExpireResponse
        {
            Message = Message.Create(nameof(Domain.Entities.AdPasswordExpire), adPasswordExpire.UserName),

            Id = adPasswordExpire.ReferenceId
        };

        await _publisher.Publish(new AdPasswordExpireCreatedEvent { Name = adPasswordExpire.UserName }, cancellationToken);

        return response;
    }
}
