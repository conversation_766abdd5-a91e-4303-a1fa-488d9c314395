
let globalSettingURL = {
    getList: "Admin/GlobalSettings/GetGlobalSettingList",
    postUrl : "Admin/GlobalSettings/CreateOrUpdate"
}

$(function () {   
    let createPermission = $("#adminCreate").data("create-permission").toLowerCase();
    if (createPermission == 'false') {
        $('input[type="checkbox"]').prop('disabled', true);
    }
    function handleSuccess(response) {       
        if (response || response?.length > 0 || response != null) {
            handleSetting(response, 'Two Factor Authentication', '#txtId', '#chk-authentication');
            handleSetting(response, 'DR Calendar', '#txtDRId', '#chk-dr');
            handleSetting(response, 'Notification', '#txtNotificationId', '#chk-notification');
            handleSetting(response, 'SMS Notification', '#txtSMSId', '#chk-sms');
            handleSetting(response, 'Email Notification', '#txtEmailId', '#chk-email');
            handleSetting(response, 'Bulk Import', '#txtImportId', '#chk-import');
            handleSetting(response, 'Approval Matrix', '#txtMatrixId', '#chk-matrix');
            handleSetting(response, 'Escalation Matrix', '#txtEscalationId', '#chk-escalation');
            handleSetting(response, 'SecurityKey', '#txtPasswordId', '#chk-password','#txtPassword');
        }
    }
    // Function to handle individual setting
    function handleSetting(response, key, idSelector, checkboxSelector, passwordId='') {
        if (response || response?.length) { 
            let settingData = response?.filter(active => active?.globalSettingKey === key);            
        if (settingData?.length) {
            const settingValue = settingData[0];            
            $(idSelector).val(settingValue.id);
            $(checkboxSelector).prop('checked', settingValue?.globalSettingValue === 'true');           
            if (passwordId != '') {
                $(passwordId).val(settingValue.passwordProtection);
            }
        }       
        }
    }
    // Function to handle errors
    function handleError(error) {
        console.error('Error fetching data from the API:', error);
    }
    $.ajax({
        type: 'GET',
        url: RootUrl + globalSettingURL.getList,
        async: true,
        success: handleSuccess,
        error: handleError
    });
   
})

//SiteAdmin Based SecretKey only show
let userRole = $('#txtUserRole').data('role')
if (userRole === 'SiteAdmin') {
    $('#secretKeyDiv').show()
} else {
    $('#secretKeyDiv').hide()
}
$('.copy-password').on('click', function () {
    let pwd = $('#txtPassword').val();    
    let tempinput = $('<input>');    
    $('body').append(tempinput);    
    tempinput.val(pwd).select();
    document.execCommand('copy');
    tempinput.remove();
    notificationAlert('success','Password copied to clipboard');

})

const handleChange = (event, id) => {   
    let loginId = $('#txtLoginUser').data('login');
    $('#txtLoginUser').val(loginId);
    let pwd = $('#txtPassword').val();
   
    let tempData = {
        globalSettingKey: event.target.name,
        globalSettingValue: event.target.checked,
        loginUserId: loginId,
        passwordProtection: pwd,
        id: $('#' + id).val() ? $('#' + id).val() : '',
        __RequestVerificationToken: gettoken()
    };

    $.ajax({
        type: "POST",
        url: RootUrl + globalSettingURL.postUrl,
        data: tempData,        
        dataType: "json",
        traditional: true,
        success: function (data) { 
            if (data?.success) {
                $('#' + id).val(data?.id)
                notificationAlert("success", data.message);
                //window reload for disable dropdown on navbar
                setTimeout(() => {
                    window.location.reload();
                },2000)
                
            } else {
                notificationAlert("warning", data.message)
            }
        },
        error: function (xhr, status, error) { 
            console.log(xhr.responseJSON.message,'error')
            notificationAlert("error", xhr.responseJSON.message)
        }
    })
}
