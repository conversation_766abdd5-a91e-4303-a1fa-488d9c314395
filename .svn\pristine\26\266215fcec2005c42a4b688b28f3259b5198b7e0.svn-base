﻿using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixModel;

namespace ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetList;

public class GetApprovalMatrixListQueryHandler : IRequestHandler<GetApprovalMatrixListQuery, List<ApprovalMatrixListVm>>
{
    private readonly IApprovalMatrixRepository _approvalMatrixRepository;
    private readonly IMapper _mapper;

    public GetApprovalMatrixListQueryHandler(IMapper mapper, IApprovalMatrixRepository approvalMatrixRepository)
    {
        _approvalMatrixRepository = approvalMatrixRepository;
        _mapper = mapper;
    }

    public async Task<List<ApprovalMatrixListVm>> Handle(GetApprovalMatrixListQuery request,
        CancellationToken cancellationToken)
    {
        var approvalMatrix = (await _approvalMatrixRepository.ListAllAsync()).ToList();

        return approvalMatrix.Count == 0
            ? new List<ApprovalMatrixListVm>()
            : _mapper.Map<List<ApprovalMatrixListVm>>(approvalMatrix);
    }
}