﻿using ContinuityPatrol.Application.Features.Replication.Events.LicenseInfoEvents.Create;
using ContinuityPatrol.Application.Features.Replication.Events.SaveAll;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.Replication.Commands.SaveAll;

public class SaveAllReplicationCommandHandler : IRequestHandler<SaveAllReplicationCommand, SaveAllReplicationResponse>
{
    private readonly IReplicationRepository _replicationRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ISiteTypeRepository _siteTypeRepository;
    private readonly ISiteRepository _siteRepository;
    private readonly IPublisher _publisher;
    private readonly ILogger<SaveAllReplicationCommandHandler> _logger;
    public SaveAllReplicationCommandHandler(IReplicationRepository replicationRepository, ILoggedInUserService loggedInUserService, ISiteTypeRepository siteTypeRepository, ISiteRepository siteRepository, IPublisher publisher, ILogger<SaveAllReplicationCommandHandler> logger)
    {
        _replicationRepository = replicationRepository;
        _loggedInUserService = loggedInUserService;
        _siteTypeRepository = siteTypeRepository;
        _siteRepository = siteRepository;
        _publisher = publisher;
        _logger = logger;
    }
    public async Task<SaveAllReplicationResponse> Handle(SaveAllReplicationCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _replicationRepository.GetByReferenceIdAsync(request.ReplicationId);

        Guard.Against.NullOrDeactive(eventToUpdate, nameof(Domain.Entities.Replication),
            new NotFoundException(nameof(Domain.Entities.Replication), request.ReplicationName));

        var replicationList = request.replictionListCommands.Select(replication=> new  Domain.Entities.Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = replication.Name,
            SiteId = replication.SiteId,
            SiteName = replication.SiteName,
            Type = replication.Type,
            TypeId= replication.TypeId,
            Properties = replication.Properties,
            LicenseId = replication.LicenseId,
            LicenseKey = (replication.LicenseId != "NA" || replication.LicenseKey.IsNotNullOrWhiteSpace())
            ? SecurityHelper.Encrypt(replication.LicenseKey) : replication.LicenseKey,
            CompanyId = eventToUpdate.CompanyId,
            BusinessServiceId = eventToUpdate.BusinessServiceId,
            BusinessServiceName = eventToUpdate.BusinessServiceName,
            FormVersion= eventToUpdate.FormVersion,
            IsActive = true,
            CreatedBy = _loggedInUserService.UserId,
            CreatedDate = DateTime.Now,
            LastModifiedBy = _loggedInUserService.UserId,
            LastModifiedDate = DateTime.Now,

}).ToList();

       // await _replicationRepository.AddRange(replicationList);

        var replicationname = new List<string>();

        foreach (var replication in replicationList)
        {
            try
            {
                await _replicationRepository.AddAsync(replication);

                if (replication.Type.ToLower().Contains("perpetuuiti"))
                {
                    var logo = GetJsonProperties.GetJsonValue(replication?.Properties, "icon");

                    var site = await _siteRepository.GetByReferenceIdAsync(replication.SiteId);

                    var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

                    if (siteType.Category.ToLower().Contains("primary"))
                        await _publisher.Publish(new ReplicationLicenseInfoCreatedEvent
                        {
                            EntityName = replication.Name,
                            LicenseId = replication.LicenseId,
                            PONumber = replication.LicenseKey,
                            EntityId = replication.ReferenceId,
                            EntityField = replication.SiteName,
                            Type = replication.Type,
                            BusinessServiceId = replication.BusinessServiceId,
                            BusinessServiceName = replication.BusinessServiceName,
                            Category = replication.Type,
                            Logo = logo
                        }, cancellationToken);
                }

                replicationname.Add(replication.Name);
            }
            catch(Exception ex){
                _logger.LogInformation($"Error while process save-as for {replication.Name} in Replication {ex.Message}");
                continue;
            }
        }

        await _publisher.Publish(new SaveAllReplicationEvent { ReplicationOrginal=eventToUpdate.Name,ReplicationNames = replicationname }, cancellationToken);
        return new SaveAllReplicationResponse
        {
            Message = "Replication save-as Completed!."
        };
    }

}