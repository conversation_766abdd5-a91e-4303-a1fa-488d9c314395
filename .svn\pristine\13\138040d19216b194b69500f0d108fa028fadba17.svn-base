using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Xunit;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowCategoryRepositoryTests : IClassFixture<WorkflowCategoryFixture>
    {
        private readonly WorkflowCategoryFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowCategoryRepository _repository;

        public WorkflowCategoryRepositoryTests(WorkflowCategoryFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _repository = new WorkflowCategoryRepository(_dbContext, DbContextFactory.GetMockUserService());
        }

        [Fact]
        public async Task GetWorkflowCategoryNames_ReturnsAllCategoryNames()
        {
            await _dbContext.WorkflowCategories.AddRangeAsync(_fixture.WorkflowCategoryList);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.GetWorkflowCategoryNames();

            Assert.Equal(_fixture.WorkflowCategoryList.Count, result.Count);
            Assert.All(result, x => Assert.Contains(_fixture.WorkflowCategoryList, y => y.Name == x.Name));
        }

        [Fact]
        public async Task IsWorkflowCategoryNameExist_ReturnsTrue_WhenNameExists_AndIdIsInvalidGuid()
        {
            var entity = _fixture.WorkflowCategoryDto;
            entity.Name = "TestCategory";
            _dbContext.WorkflowCategories.Add(entity);
            _dbContext.SaveChanges();

            var result = await _repository.IsWorkflowCategoryNameExist("TestCategory", "invalid-guid");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowCategoryNameExist_ReturnsFalse_WhenNameDoesNotExist_AndIdIsInvalidGuid()
        {
            var result = await _repository.IsWorkflowCategoryNameExist("NonExistent", "invalid-guid");

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowCategoryNameExist_ReturnsExpected_WhenIdIsValidGuid()
        {
            var id = Guid.NewGuid().ToString();
            var entity = _fixture.WorkflowCategoryDto;
            entity.ReferenceId = id;
            entity.Name = "UniqueCategory";
            await _dbContext.WorkflowCategories.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.IsWorkflowCategoryNameExist("UniqueCategory", id);

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowCategoryNameUnique_ReturnsTrue_WhenNameExists()
        {
            var entity = _fixture.WorkflowCategoryDto;
            entity.Name = "UniqueCategory";
            _dbContext.WorkflowCategories.Add(entity);
            _dbContext.SaveChanges();

            var result = await _repository.IsWorkflowCategoryNameUnique("UniqueCategory");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowCategoryNameUnique_ReturnsFalse_WhenNameDoesNotExist()
        {
            var result = await _repository.IsWorkflowCategoryNameUnique("NonExistent");

            Assert.False(result);
        }

        [Fact]
        public async Task ListAllAsync_ReturnsAllCategories()
        {
            await _dbContext.WorkflowCategories.AddRangeAsync(_fixture.WorkflowCategoryList);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.ListAllAsync();

            Assert.Equal(_fixture.WorkflowCategoryList.Count, result.Count);
        }

        [Fact]
        public void GetPaginatedQuery_ReturnsActiveCategoriesOrdered()
        {
            _dbContext.WorkflowCategories.AddRange(_fixture.WorkflowCategoryPaginationList);
            _dbContext.SaveChanges();

            var result = _repository.GetPaginatedQuery().ToList();

            Assert.All(result, x => Assert.True(x.IsActive));
            Assert.True(result.SequenceEqual(result.OrderByDescending(x => x.Id)));
        }
    }
}