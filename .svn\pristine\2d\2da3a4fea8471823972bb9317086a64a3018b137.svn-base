﻿using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Create;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoLoadBalancerDataAttribute : AutoDataAttribute
{
    public AutoLoadBalancerDataAttribute()
        : base(() =>
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateLoadBalancerCommand>(p => p.Name, 10));
            fixture.Customize<CreateLoadBalancerCommand>(c => c.With(b => b.IPAddress, 0.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateLoadBalancerCommand>(p => p.Name, 10));
            fixture.Customize<UpdateLoadBalancerCommand>(c => c.With(b => b.Id, 0.ToString));

            return fixture;
        })
   {
   }
}