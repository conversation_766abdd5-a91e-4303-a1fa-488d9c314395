﻿let msId = "";
let datas = [];
let msInfra = [];
let selectedValues = [];
let actionData = {};
let addedname = [];
let dataTable = [];
//let allServerIds = []; deadcode
let monitorURL = {
    nameExistUrl: "Manage/MonitoringServices/IsServiceNameExist",
    getPagination: "/Manage/MonitoringServices/GetPagination",
    createOrUpdate: "Manage/MonitoringServices/CreateOrUpdate",
    delete: "Manage/MonitoringServices/Delete",
    getInfraObjectsByBusinessServiceId: "Configuration/InfraObject/GetInfraObjectByBusinessServiceId",
    getServersByInfraObjectId: "Configuration/InfraObject/GetServersByInfraObjectId",
    getServerDataByServerId: "Manage/MonitoringServices/GetServerById",
    getWorkflowByType: "ITAutomation/WorkflowInfraObject/GetWorkflowByInfraObjectIdAndActionType",
    updateMonitorStatus: "Manage/MonitoringServices/UpdateMonitorStatus",
    checkWindowServiceurl: 'ITAutomation/WorkflowExecution/CheckWindowsService',
}

$(function () {
  
    let Permission = {
        'Create': $("#manageMSCreate").data("create-permission").toLowerCase(),
        'Delete': $("#manageMSDelete").data("delete-permission").toLowerCase()
    }

    if (Permission.Create == 'false') {
        $("#btnMonitoringServiceCreate").addClass("btn-disabled").css("cursor", "not-allowed").removeAttr("data-bs-toggle data-bs-target id");
    }
 
     dataTable = $('#tblMoniterService').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        "processing": true,
        "serverSide": false,
        "filter": true,
        "order": [],
        "ajax": {
            "type": "GET",  
            "url": monitorURL.getPagination,
            "dataType": "json",
            "data": function (d) {
                d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                d.pageSize = d?.length;
                d.searchString = selectedValues?.length === 0 ? $('#msSearch').val() : selectedValues.join(';');
                selectedValues.length = 0;
                d.businessServiceId = $('#listMSOperationService').find("option:selected").val() === "All" ? '' : $('#listMSOperationService').find("option:selected").val();
                d.infraObjectId = $('#listMSInfra').find("option:selected").val() === "All" ? '' : $('#listMSInfra').find("option:selected").val()
            },
            "dataSrc": function (json) {
                if (json?.success && json?.data?.data && json?.data?.data.length) {
                    json?.data?.data?.forEach((item) => {
                        if (item.lastExecutionTime) {
                            let date = new Date(item.lastExecutionTime);
                            item.lastExecutionTime = `${('0' + date.getDate()).slice(-2)}-${('0' + (date.getMonth() + 1)).slice(-2)}-${date.getFullYear()} ${('0' + date.getHours()).slice(-2)}:${('0' + date.getMinutes()).slice(-2)}:${('0' + date.getSeconds()).slice(-2)}`;
                        }
                        datas.push(item);
                        if (item.businessServiceName) {
                            $('#listMSOperationService').append(`<option value="${item.businessServiceId}">${item.businessServiceName}</option>`);
                        }
                    });
                } else {
                    notificationAlert("warning", json?.message);
                    return false;
                }

                $('#listMSOperationService').find('option').each(function () {
                    $(this).siblings(`[value="${this.value}"]`).remove();
                });
                json.recordsTotal = json?.totalPages;
                json.recordsFiltered = json?.totalCount;
                $(".pagination-column").toggleClass("disabled", json?.data?.data?.length === 0);
                return json?.data?.data;
            }
        },
        "columnDefs": [
            { "targets": [1, 2, 3, 4], "className": "truncate" }
        ],
        "columns": [
            {
                "data": null,
                "name": "Sr. No.",
                "orderable": false,
                "render": (data, type, row, meta) => type === 'display' ? meta.row + 1 : data
            },
            { "data": "businessServiceName", "name": "businessServiceName" },
            { "data": "infraObjectName", "name": "infraObjectName" },
            { "data": "serverName", "name": "serverName" },
            {   "data": "authenticationType","name": "authenticationType",
                "render": (data, type, row) => {
                    return row.type ? row.type.replace(/^use\s+/i, '') : '';
                }
            },
            {
                "data": "servicePath", "name": "servicePath",
                "render": (data, type, row) => {    
                    if (row?.properties) {
                        const props = JSON.parse(row.properties);
                        if (props?.details?.length) {
                            return type === 'display'
                                ? `<span title="${props.details.map(item => item.name).join(', ') || 'NA'}" class="text-truncate" style="max-width:450px; display:inline-block">${props.details.map(item => item.name).join(', ') || 'NA'}</span>`
                                : data;
                        }
                    } else {
                        return '-';
                    }              
                    
                }
            },
            
            {
                "data": "lastExecutionTime", "name": "lastExecutionTime",
                "render": (data) => data || 'NA'
            },         
            {
                "data": "status", "name": "Status",
                "render": (data) => {
                    if (!data) return `<td></td>`;

                    let status = data.toLowerCase();
                    let iconClass = {
                        started: "text-success cp-success me-1",
                        stopped: "text-danger cp-error me-1",
                        pending: "cp-pending text-warning me-1"
                    }[status];
                    let title = data.charAt(0).toUpperCase() + data.slice(1);
                    return `<td><i class="${iconClass}" title="${title}"></i></td><td><span>${title}</span></td>`;
                }
            },
            {
                "orderable": false,
                "render": (data, type, row) => {
                    let isEditAllowed = Permission.Create === "true";
                    let isDeleteAllowed = Permission.Delete === "true";
                    let isStarted = row?.status?.toLowerCase() === 'started';
                    let isPending = row?.status?.toLowerCase() === 'pending';

                    let statusIcon = (!isPending && row?.status) ? `<i id='MonitorStatus' class="${isStarted ? 'text-danger cp-Stopped' : 'text-success cp-circle-play'}"  data-moniter-name="${row.infraObjectName}"  title="${isStarted ? 'Stop' : 'Start'}"   data-title="${isStarted ? 'Stop' : 'Start'}" data-status="${row.status}" data-moniter-state="${isStarted ? 'Stopped' : 'Started'}" data-moniter-id="${row.id}"></i>`
                        : '';
                    let editBtn = isEditAllowed
                        ? `<span role="button" title="Edit" class="btnMSEdit ${isStarted ? 'form-delete-disable' : ''}" data-moniter-service='${btoa(JSON.stringify(row))}'><i class="cp-edit"></i></span>`
                        : `<span role="button" title="Edit" class="btn-disabled"><i class="cp-edit"></i></span>`;
                    let deleteBtn = isDeleteAllowed
                        ? `<span role="button" title="Delete" class="btnMSDelete ${isStarted ? 'form-delete-disable' : ''}" data-moniter-id="${row.id}" data-moniter-name="${row.infraObjectName}"><i class="cp-Delete"></i></span>`
                        : `<span role="button" title="Delete" class="btn-disabled"><i class="cp-Delete"></i></span>`;

                    return `<div class="d-flex align-items-center gap-2">${editBtn}${deleteBtn}${statusIcon}</div>`;
                }
            }
        ], "rowCallback": function (row, data, index) {
            let api = this.api();
            let startIndex = api.context[0]._iDisplayStart;
            let counter = startIndex + index + 1;
            $('td:eq(0)', row).html(counter);
        },
        initComplete: function () {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
            $('th.Sr_No').removeClass('sorting_asc');
        }
    });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
        $('th.Sr_No').removeClass('sorting_asc');
    });

    document.getElementById('msSearch')?.addEventListener('keypress', preventEnterKey);

    $('#msSearch').on('input', commonDebounce(function (e) {
        let inputValue = $('#msSearch').val();
        let checkboxes = [
            $('#filterBusinessServiceName'),
            $('#filterInfraObjectName'),
            $('#filterServerName')
        ];
        selectedValues = checkboxes
            .filter(checkbox => checkbox.is(':checked'))
            .map(checkbox => checkbox.val() + inputValue);
        let currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {
                if (e?.target?.value && json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false);
        }
    }, 500));

    $(async function () {
        await monitoringServiceDetails()
    })

    $('#btnWorkflowSaveProfile').on('click', async function () {
        let WorkflowName = $("#workflowMS option:selected").text();
        let WorkflowId = $("#workflowMS").val();
        let WorkflowType = $("#msWorkflowType").val();
        await addPaths(WorkflowName, WorkflowId, WorkflowType);
        $('#workflowMS').val('').trigger('change');
        $('#msWorkflowAdded').hide();
    });

    $('#btnProcessSaveProfile').on('click', async function () {
        let ProcessName = $('#msProcessName').val();
        let threadType = $('.msradio:checked').val();
        await addPaths(ProcessName, "", threadType);
        $('#msProcessName').val('');
        $('#msProcessAdded').hide();

    }); 

    $('#btnServiceSaveProfile').on('click', async function () {
        let ServiceName = $('#msServiceName').val();
        let threadType = $('.msradio:checked').val();
        await addPaths(ServiceName, "", threadType);
        $('#msServiceName').val('');
        $('#msServiceAdded').hide();

    });

    $('#btnMonitoringServiceCreate').on('click', async function () {
        await monitorServiceValidateFields();
        msId = "";
        $('#msServiceName,#msProcessName').val('');
        ['#msBusinessService', '#msInfraObject', '#serverMS', '#authenticationTypeMS', '#msWorkflowType', '#workflowMS'].forEach(selector => $(selector).prop('selectedIndex', 0));
        $('#btnMSSave').text('Save');
        $('#msInfraObject,#serverMS,#workflowMS,#authenticationTypeMS,#selectedPathDetails').empty().prop('disabled', false);
        $('.msradio').prop("checked", false);
        $('#msMonitoringType,#msServiceDiv,#msProcessDiv,#workflowType,#workflow,#command,#msProcessAdded,#msServiceAdded,#msWorkflowAdded,#msAddedName').hide();
        $('#createMSModal').modal('show');
    });

    $('.msradio').on('click', function () {
        let selectedValue = document.querySelector('.msradio:checked').value;
        let targetBox = $("." + selectedValue);
        $(".box").not(targetBox).hide();
        $(targetBox).show();
        addedname = [];
        $('#selectedPathDetails').empty();
        $('#msAddedName').hide();
        if (selectedValue) {
            $('#msProcessName,#msServiceName').val('');
            $("#ProcessPath-error,#ServicePath-error,#ThreadType-error").text("").removeClass('field-validation-error');
        }        
    });

    $('#tblMoniterService').on('click', '#MonitorStatus', function () {
        let tblMS = $(this);
        let monitorId = tblMS.data('moniter-id');
        let status = tblMS.data('status');
        let textStatus = tblMS.data('title').toLowerCase();
        let monitorName = tblMS.data('moniter-name');
        let newStatus = status === 'Stopped' ? 'Started' : 'Stopped';
        actionData = { id: monitorId, status: newStatus, isServiceUpdate: newStatus };
        $('#statusData').text(textStatus);
        $('#monitorData').text(monitorName);
        $('#msControlModal').modal('show');
    });

    $("#btnMSControl").on('click', async function () {
        $('#btnMSControl').prop('disabled', true);
        $('#MSLoader').removeClass('d-none').show();
        await $.ajax({
            type: "POST",
            url: RootUrl + monitorURL.updateMonitorStatus,
            data: actionData,
            traditional: true,
            headers: { 'RequestVerificationToken': gettoken() },
            dataType: 'json',
            success: function (result) {
                let dt = $('#tblMoniterService').DataTable();
                if (result && result?.data?.success) {
                    let page = dt.page();
                    dt.ajax.reload(() => dt.page(page).draw(false), false);
                    notificationAlert("success", result.data.message);
                } else {
                    $('.btnMSEdit, .btnMSDelete, .text-danger.cp-Stopped, .text-success.cp-circle-play')
                        .addClass('icon-disabled').prop('disabled', true)
                        .removeAttr('data-bs-toggle data-bs-target');
                    errorNotification(result);
                }
                $('#MSLoader').addClass('d-none').hide();
                $('#msControlModal').modal('hide');
                $("#btnMSControl").prop("disabled", false);
            }
        });
    });

    $('#msWorkflowType').on('change', async function () {
        let type = $(this).val();
        let infraId = $('#msInfraObject option:selected').data('infraid');
        let errorElement = $('#WorkflowType-error');
        await monitoringServiceValidate(type, 'Select workflow type', errorElement);
        if (infraId && type) {
            await getWorkflowsByType(infraId, type);
        }
    });

    $('#listMSOperationService').on('change', function () {
        $('#listMSInfra').empty()
        msInfra = [];
        $('#listMSInfra').append('<option value="All">All</option>');
        datas && datas?.forEach(function (value) {
            if ($('#listMSOperationService').find(':selected').val() === value.businessServiceId && !msInfra.some(item => item.infraObjectId === value.infraObjectId)) {
                let bsValue = value.businessServiceName
                let infraValue = value.infraObjectName
                let infraId = value.infraObjectId
                $('#listMSInfra').append('<option value="' + value.infraObjectId + '">' + value.infraObjectName + '</option>');
                msInfra.push({ infraObjectId: infraId, infraObjectName: infraValue })
                selectedValues.push(`businessServiceName=${bsValue}`)

            }
        });
        dataTable.ajax.reload()
    })

    $("#listMSInfra").on("change", async function () {
        let selectedInfraId = $(this).val();
        if (selectedInfraId !== 'All') {
            let selectedInfra = msInfra.find(item => item.infraObjectId === selectedInfraId);
            if (selectedInfra) {
                selectedValues.push(`infraObjectName=${selectedInfra.infraObjectName}`);
            }
        } else {
            msInfra && msInfra?.forEach(({ infraObjectName }) => {
                selectedValues.push(`infraObjectName=${infraObjectName}`);
            });
        }
        dataTable.ajax.reload();
    });

    $('#msInfraObject').on('change', async function () {
        const value = $(this).val();
        const infraID = $(this).find('option:selected').data('infraid');
        const errorElement = $('#InfraObjectId-error');
        await infraChange(infraID);       
        await monitoringServiceValidate(value, 'Select infraobject', errorElement);
    });

    $(document).on('click', '.serverMSRadio', async function () {
        let ServerType = document.querySelector('.serverMSRadio:checked').value;
        let id = $('#msInfraObject').find('option:selected').data('infraid');
        let errorElement = $('#MonitoringType-error');
        $('#serverMS').prop('disabled', false);
        $('.msradio').prop("checked", false);
        $('#MSServerDiv').show();
        $('#authenticationTypeMS').empty();
        await getServersByInfraObjectId(id, ServerType);
        await monitoringServiceValidate(ServerType, 'Select type', errorElement);
        $('#command,#msAddedName,#workflowType,#workflow,#msServiceDiv').hide(); 
    });

    $('#serverMS').on('change',async function () {
        let value = $(this).val();
        let id = $(this).find('option:selected').data('serverid');
        let errorElement = $('#ServerId-error');
        $('#workflowType,#workflow,#command').hide();
        $('#msServiceName,#msWorkflowType, #workflowMS').val('');
        await getServerData(id)
        await monitoringServiceValidate(value, 'Select server', errorElement);
    });

    $('#authenticationTypeMS').on('change', async function () {
        let value = $(this).val();
        let isWorkflow = value === 'Use Workflow';
        let isCommandBased = ['Use ps -ef', 'Use WMI', 'Use SSH', 'Use PowerShell', 'Use Telnet'].includes(value);
        $('#workflowType, #workflow').toggle(isWorkflow);
        $('#command').toggle(isCommandBased);
        $('#msServiceName,#msWorkflowType, #workflowMS').val('').trigger('change');
        $('#selectedPathDetails').val('');
        addedname = [];
        let errorIds = [
            '#WorkflowType-error',
            '#WorkflowId-error',
            '#ThreadType-error',
            '#ServicePath-error'
        ];
        errorIds.forEach(id => $(id).text('').removeClass('field-validation-error'));
        $('#msServiceDiv, #msProcessDiv,#msAddedName').hide();
        $('.msradio').prop("checked", false);
        $('#ProcessPath-error').val('').trigger('change');

        if (!$('#msWorkflowType').val()) {
            $('#msWorkflowType').append('<option value="" disabled selected>Select Workflow Type</option>');
        }
        if (!$('#workflowMS').val()) {
            $('#workflowMS').append('<option value="" disabled selected>Select Workflow Name</option>');
        }
        let errorElement = $('#Type-error');
        await monitoringServiceValidate(value, 'Select server authentication type', errorElement);

    });

    $('#workflowMS').on('change', async function () {
        let workflowId = $("#workflowMS").val();
        let workflowName = $("#workflowMS option:selected").text();
        let workflowType = $('#msWorkflowType').val();

        let monitorId = msId;
        let infraId = $('#msInfraObject option:selected').data('infraid');
        let serverId = $('#serverMS option:selected').data('serverid');
        let type = $('#authenticationTypeMS option:selected').text();
        let errorElement = $('#WorkflowId-error');
        if (addedname?.length == 0) {
            await workflowNameValidate(workflowId, monitorId, infraId, type, serverId, workflowType, workflowName, monitorURL.nameExistUrl, 'Select workflow name', errorElement);
        }

        if (workflowId) { $('#msWorkflowAdded').show(); } else { $('#msWorkflowAdded').hide(); }
    });

    $("#btnMSSave").on('click', async function () {
        let businessServiceId = $("#msBusinessService option:selected").attr("id");
        let businessServiceName = $("#msBusinessService").val();
        let infraId = $("#msInfraObject option:selected").data("infraid");
        let infraName = $("#msInfraObject").val();
        let addedDetails = "";
        let monitoringType = "";

        if ($('.serverMSRadio:checked').length > 0) {
            monitoringType = $('.serverMSRadio:checked').val();
        } else {
            monitoringType = 'All'
        }
        //let serverId = "";   //dead code
        //if (monitoringType == "All") {
        //    serverId = allServerIds;
        //} else {
        //    serverId = $("#serverMS option:selected").data("serverid");
        //}  
        
        let serverId = $("#serverMS option:selected").data("serverid");
        let serverName = $("#serverMS").val();
        let msServiceStatus = $("#serverMS").attr("ServiceStatus");
        let msStatus = $("#serverMS").attr("Status");
        let type = $("#authenticationTypeMS").val();       
        let id = msId;
        let threadType = $('.msradio:checked').val();
        let subType = (type === "Use Workflow") ? "Workflow" : threadType;
        let isMonitoringType = true;
        let workflowType = $("#msWorkflowType").val();
        let workflow = $("#workflowMS").val();
        let workflowName = workflow ? $("#workflowMS option:selected").text() : null;
        let servicePath = threadType === 'Service' ? $('#msServiceName').val() : threadType === 'Process' ? $('#msProcessName').val() : '';
        let errors = {
            Bsservice: $('#BusinessServiceId-error'),
            Infra: $('#InfraObjectId-error'),
            Server: $('#ServerId-error'),
            Type: $('#Type-error'),
            Workflowtype: $('#WorkflowType-error'),
            Workflow: $('#WorkflowId-error'),
            Servicepath: $('#ServicePath-error'),
            Processpath: $('#ProcessPath-error'),
            Threadtype: $('#ThreadType-error'),
            MonitoringType: $('#MonitoringType-error')
        };
        let isBsService = await monitoringServiceValidate(businessServiceName, 'Select operational service', errors.Bsservice);
        let isInfra = await monitoringServiceValidate(infraName, 'Select infraobject', errors.Infra);
            
        let isServer = await monitoringServiceValidate(serverName, 'Select server', errors.Server);
        if (isInfra && monitoringType == "" && !isServer) {
            isMonitoringType = await monitoringServiceValidate(monitoringType, 'Select type', errors.MonitoringType);
        }  
        let isType =await monitoringServiceValidate(type, 'Select server authentication type', errors.Type);
        let isWorkflowType = await monitoringServiceValidate(workflowType, 'Select workflow type', errors.Workflowtype);
        let isThreadType = await monitoringServiceValidate(threadType, 'Select any one', errors.Threadtype);
        let isServicePath = false;
       
        if (type == "Use Workflow") {
            let Workflow = await getSelectedWorkflowDetails();
            addedDetails = Workflow;
            if (workflowName && Workflow.length > 0) {
                isWorkflow = await monitoringServiceValidate("", 'Add workflow name', errors.Workflow)
            }
            else if (!workflowName && Workflow.length > 0) {
                const names = Workflow.map(item => item.name);
                workflowName = names.join(', ');
                const values = workflowName.includes(',') ? workflowName.split(',').map(v => v.trim()).filter(v => v) : [workflowName];
                for (let val of values) {
                    isWorkflow = await workflowNameValidate(val, id, infraId, type, serverId, workflowType, workflowName, monitorURL.nameExistUrl, 'Select workflow name', errors.Workflow);
                }
            }
            else {
                addedDetails = [{ name: workflowName, id: workflow, type: workflowType }]; 
                isWorkflow = await workflowNameValidate(workflowName, id, infraId, type, serverId, workflowType, workflowName, monitorURL.nameExistUrl, 'Select workflow name', errors.Workflow);
            }
        } else {
            let servicePaths = await getSelectedPathDetails();
            addedDetails = servicePaths;
            if (servicePath && servicePaths.length > 0) {
               isServicePath = await monitoringServiceValidate("", 'Add service name', errors.Servicepath)
            }
            else if (!servicePath && servicePaths.length > 0) {
                const names = servicePaths.map(item => item.name);
                servicePath = names.join(', ');
                const values = servicePath.includes(',') ? servicePath.split(',').map(v => v.trim()).filter(v => v) : [servicePath];
                for (let val of values) {
                    isServicePath = await pathNameValidate(val, id, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter service name', errors.Servicepath);
                }
            } else {
                 addedDetails = [{ name: servicePath }];                
                if (threadType === 'Service') {
                    isServicePath = await pathNameValidate(servicePath, id, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter service name', errors.Servicepath);
                } else if (threadType === 'Process') {
                    isServicePath = await pathNameValidate(servicePath, id, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter process name', errors.Processpath);
                }
            }
        }
        let TypeValue = type === "Use Workflow";
        let isCommandType = ["Use ps -ef", "Use SSH", "Use WMI", "Use PowerShell", "Use Telnet"].includes(type);
        let commonData = {
            BusinessServiceId: businessServiceId,
            BusinessServiceName: businessServiceName,
            InfraObjectId: infraId,
            InfraObjectName: infraName,
            MonitoringType: monitoringType,
            ServerId: serverId,
            ServerName: serverName,
            Type: type,
            Id: id,
            IsServiceUpdate: msServiceStatus,
            Status: msStatus,
            Properties: JSON.stringify({
                SubType: subType,
                details: addedDetails,
            }),
            __RequestVerificationToken: gettoken()
        };

        
        if (TypeValue) {
            if (isBsService && isInfra && isServer && isType && isWorkflowType && isMonitoringType && isWorkflow) {
                await msCreateOrUpdate(commonData);
            }
        } else if (isCommandType) {
            if (isBsService && isInfra && isServer && isType && isServicePath && isMonitoringType && isThreadType) {
                await msCreateOrUpdate(commonData);
            }
        }
    });

    $('#msBusinessService').on('change', async function () {
        let value = $(this).val();
        let id = $(this).find('option:selected').attr('id');
        let errorElement = $('#BusinessServiceId-error');
        await monitoringServiceValidate(value, 'Select operational service', errorElement);
        $('#serverMS, #authenticationTypeMS').empty().prop('disabled', false);
        $('#workflowType, #workflow,#command,#msMonitoringType,#msAddedName').hide();
        $('#MSServerDiv').show();
        $('#msServiceName,#msWorkflowType, #workflowMS').val('');
        $('.serverMSRadio').prop("checked", false);
        await getInfraObjectsByBusinessServiceId(id);
    });

    $('#msServiceName').on('input', commonDebounce(async function () {
      let value = $(this).val();
        if (value !== undefined) {
            $(this).val(value.replace(/ {2,}/g, " "));
        }
        let infraId = $('#msInfraObject option:selected').data('infraid');
        let serverId = $('#serverMS option:selected').data('serverid');
        let type = $('#authenticationTypeMS option:selected').text()
        let threadType = $('.msradio:checked').val();
       
        let monitorId = msId;
        let errorElementPath = $('#ServicePath-error');
        let validation = await pathNameValidate(value, monitorId, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter service name', errorElementPath);
        if (validation) { $('#msServiceAdded').show() } else { $('#msServiceAdded').hide() }
    }, 400));

    $('#msProcessName').on('input', commonDebounce(async function () {
        let value = $(this).val();
        if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }
        let infraId = $('#msInfraObject option:selected').data('infraid');
        let serverId = $('#serverMS option:selected').data('serverid');
        let type = $('#authenticationTypeMS option:selected').text()
        let threadType = $('.msradio:checked').val();
        let monitorId = msId;
        let errorElementPath = $('#ProcessPath-error');
        let validation = await pathNameValidate(value, monitorId, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter process name', errorElementPath);
        if (validation) { $('#msProcessAdded').show() } else { $('#msProcessAdded').hide() }
    }, 400));

    $('#tblMoniterService').on('click', '.btnMSEdit', async function () {
        let encodedData = $(this).data('moniterService');
        let monitorData = JSON.parse(decodeURIComponent(atob(encodedData)));
        addedname = [];
        $('#selectedPathDetails').empty();
        $('#serverMS').prop('disabled', false);
        $('#MSServerDiv').show();
        $('#msWorkflowAdded,#msProcessAdded,#msServiceAdded').hide();
        await monitorServiceValidateFields();
        await populateMSFields(monitorData);
        $('#btnMSSave').text('Update');
        $('#createMSModal').modal('show');
    });

    $('#tblMoniterService').on('click', '.btnMSDelete', function () {
        let monitorId = $(this).data('moniter-id');
        let monitorName = $(this).data('moniter-name');
        $('#deleteData').text(monitorName);
        msId = monitorId;
        $('#deleteMSModel').modal('show');
    });
    
    $('#btnMSConfirmDelete').on('click', async function () {
        let monitorId = msId;       
        await $.ajax({
            url: RootUrl + monitorURL.delete,
            type: "POST",
            dataType: "json",
            data: { id: monitorId },
            success: function (result) {            
                if (result && result?.success) {
                    notificationAlert("success", result?.data);
                    $('#deleteMSModel').modal('hide');
                    setTimeout(() => dataTable.ajax.reload(), 1500);
                } else {
                    errorNotification(result);
                }
            }      
        });
    });
});