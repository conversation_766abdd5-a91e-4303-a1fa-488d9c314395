﻿using ContinuityPatrol.Application.Features.Setting.Commands.Create;
using ContinuityPatrol.Application.Features.Setting.Commands.Update;
using ContinuityPatrol.Application.Features.Setting.Events.PaginatedView;
using ContinuityPatrol.Domain.ViewModels.SettingModel;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class SettingsController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly ILogger<SettingsController> _logger;
    private readonly IDataProvider _dataProvider;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;

    public SettingsController(IPublisher publisher, ILogger<SettingsController> logger, IDataProvider dataProvider, ILoggedInUserService loggedInUserService, IMapper mapper)
    {
        _publisher = publisher;
        _logger = logger;
        _dataProvider = dataProvider;
        _loggedInUserService = loggedInUserService;
        _mapper = mapper;
    }

    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in Settings");

        await _publisher.Publish(new SettingPaginatedEvent());
        return View();
    }

    public async Task<IActionResult> GetList()
    {
        _logger.LogDebug("Entering GetList method in Settings");

        try
        {
            //var settingView = await _dataProvider.Setting.GetSettingPaginatedList(new GetSettingPaginatedListQuery());
            var settingView = await _dataProvider.Setting.GetSettingsList();
            _logger.LogDebug("Successfully retrieving settings list in Settings");

            return Json(settingView);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on settings page while retrieving settings list.", ex);
            return Json("");
        }

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Admin.CreateAndEdit)]
    public async Task<IActionResult> CreateOrUpdate(SettingModel settingModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in Settings");
        settingModel.LoginUserId = _loggedInUserService.UserId;

        try
        {

            if (settingModel.Id.IsNullOrWhiteSpace())
            {
                var createCommand = _mapper.Map<CreateSettingCommand>(settingModel);
                var result = await _dataProvider.Setting.CreateAsync(createCommand);
                _logger.LogDebug("Creating Settings");

                if (result.Success)
                {
                    TempData.NotifySuccess(result.Message);
                }
                else
                {
                    TempData.NotifyWarning(result.Message);
                }
            }
            else
            {
                var updateCommand = _mapper.Map<UpdateSettingCommand>(settingModel);
                var result = await _dataProvider.Setting.UpdateAsync(updateCommand);
                _logger.LogDebug("Updating Settings");
                if (result.Success)
                {
                    TempData.NotifySuccess(result.Message);
                }
                else
                {
                    TempData.NotifyWarning(result.Message);
                }
            }
            _logger.LogDebug("CreateOrUpdate operation completed successfully in Settings, returning view.");
            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on settings page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on settings page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    public IActionResult SmtpConfiguration()
    {
        return View();
    }

}