﻿let globalreport = [];
var ReportData = [];
var dataForDownload = []; 
let IDworkflow = [];
let IDInfra = [];
let IDBusinessservice = [];
let IDDrBusinesservice = [];
let nameuser = [];
let values = "";
var currentDate = new Date().toLocaleDateString('en-CA');
var currentDate1 = new Date();
currentDate1.setDate(currentDate1.getDate() - 7);
var defaultStartDate = currentDate1.toLocaleDateString('en-CA');
var yesterday = new Date();
yesterday.setDate(yesterday.getDate() - 1);
var tdyYesterday = yesterday.toLocaleDateString('en-CA');
var EmptyAlert = document.getElementById("EmptyAlert");
var Loading = document.getElementById("Loader");
var ReportDefaultImage = document.getElementById("ReportDefaultImage");
ReportDefaultImage.hidden = false;
var rpostartDateInput = document.getElementById("rpoclndrStart");
rpostartDateInput.max = currentDate;
var rpoSLAStartDate;
var rpoSLAEndDate;
let infraObjectName = [];
let AirGapName = [];
let SnapReport = [];
let SnapDropDownValues = [];
let snapStartDate = [];
let snapEndDate = [];
let isCustom = "";
let drdrill = "";
var RPOSLAclndrClick = false;
let currentAjaxRequest = null;
$(document).ready(function () {
    $("#ReportsName").text("Prebuild Reports");
    $("#licensesubmit").hide();
    $.ajax({
        url: "/Report/PreBuildReport/GetInfraobject",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (response) {
            if (response.success == true) {
                var data = response.data;
                var selectElement = document.getElementById("Infraobject");
                for (var i = 0; i < data.length; i++) {
                    var option = document.createElement("option");
                    option.value = data[i].infraObjectId;
                    option.textContent = data[i].infraObjectName;
                    option.setAttribute("data-type", data[i].monitorType);
                    selectElement.appendChild(option);
                }
            }
        }

    })



    $.ajax({
        url: "/Report/PreBuildReport/GetUserNames",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (response) {
            if (response != 0 && response != null && response.success != false) {
                var data = response.data;
                var selectElement = document.getElementById("AllUsers");
                for (var i = 0; i < data.length; i++) {
                    var option = document.createElement("option");
                    option.value = data[i].id;
                    option.textContent = data[i].loginName;
                    selectElement.appendChild(option);
                }
            }
            else {
                NotificationMsg(response.message);
                console.error(responce);
            }
        },
        error: function (error) {
            console.error(error);
        }

    })    

    $.ajax({
        url: "/Report/PreBuildReport/GetLicensePONumber",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (response) {
            if (response.success != false) {
                var data = response.data;
                var selectElement = document.getElementById("ddlLicenceID");
                const allOptions = $("#ddlLicenceID option[value='All']");

                data.length < 2 ? allOptions.remove() : allOptions.not(":first").remove().end().show();
                for (var i = 0; i < data.length; i++) {
                    var option = document.createElement("option");
                    option.value = data[i].id;
                    option.textContent = data[i].poNumber;
                    selectElement.appendChild(option);
                }
            }
            else { NotificationMsg(response.message); }
        }

    })

    $.ajax({
        url: "/Report/PreBuildReport/GetbusinessserviceName",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (response) {
            if (response.success != false) {
                var data = response.data;
                var selectElement = document.getElementById("BusinessServiceID");
                for (var i = 0; i < data.length; i++) {
                    var option = document.createElement("option");
                    option.value = data[i].id;
                    option.textContent = data[i].name;
                    selectElement.appendChild(option);
                }
            }
            else { NotificationMsg(response.message); }
        }
    })

    //Resilience Readiness Report Operational Service dropdown
    $.ajax({
        url: "/Report/PreBuildReport/GetDRReadyBusinessServiceName",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (response) {
            if (response.success != false) {
                var data = response.data;
                if (data.length < 2) {
                    $("#drReadyBusinessServiceId option[value='All']").remove();
                }
                else {
                    $("#drReadyBusinessServiceId option[value='All']").show();
                }
                var a = data;
                var selectElement = document.getElementById("drReadyBusinessServiceId");
                for (var i = 0; i < data.length; i++) {
                    if (data[i].businessServiceId && data[i].businessServiceName) {
                        var option = document.createElement("option");
                        option.value = data[i].businessServiceId;
                        option.textContent = data[i].businessServiceName;
                        selectElement.appendChild(option);
                    }
                }
            }
            else { NotificationMsg(response.message); }
        }
    })

    $.ajax({
        url: "/Report/PreBuildReport/GetbusinessserviceName",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (response) {
            if (response.success != false) {
                var data = response.data;
                var selectElement = document.getElementById("BusinessServiceIDLicense");
                const allOptions = $("#BusinessServiceIDLicense option[value='All']");

                data.length < 2 ? allOptions.remove() : allOptions.not(":first").remove().end().show();
                for (var i = 0; i < data.length; i++) {
                    var option = document.createElement("option");
                    option.value = data[i].id;
                    option.textContent = data[i].name;
                    selectElement.appendChild(option);
                }
            }
            else { NotificationMsg(response.message); }
        }
    })

    $.ajax({
        url: "/Report/PreBuildReport/ChildLicensePONumber",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (response) {
            if (response.success != false) {
                var data = response.data;
                var selectElement = document.getElementById("ddlDerivedID");
                const allOptions = $("#ddlDerivedID option[value='All']");

                data.length < 2 ? allOptions.remove() : allOptions.not(":first").remove().end().show();
                for (var i = 0; i < data.length; i++) {
                    var option = document.createElement("option");
                    option.value = data[i].id;
                    option.textContent = data[i].poNumber;
                    selectElement.appendChild(option);
                }
            }
            else { NotificationMsg(response.message); }
        }

    })
    $('#customizedContainer').on('change', function () {
             $('#btndrdrill').hide();
              $('#workflowid').val('').trigger('change');
              if (drdrill == null || drdrill.length == 0) {
                  EmptyAlert.hidden = false;
                  ReportDefaultImage.hidden = true;
              }
              else
              {
                       ReportDefaultImage.hidden = false;
              }
    });
    //drdrill
    $('#workflowendDate').on('change', async function () {
        Loading.hidden = true;
        EmptyAlert.hidden = true;
        ReportDefaultImage.hidden = false;
        var ExecutionMode = document.getElementById("executionModeId");
        var ExecutionModes = ExecutionMode.options[ExecutionMode.selectedIndex];
        var Modes = ExecutionModes.textContent;

        $("#workflowid").empty().append('<option value="" disabled selected>Select Workflow Name</option>');
        var StartDate = $('#startDate').find("input[type=date]").val();
        var EndDate = $('#workflowendDate').find("input[type=date]").val();
        if (globalreport == "DRReadinessLog") { $("#BusinessServiceID").val("").trigger("change"); return false; }
        /* if (!StartDate && !EndDate) { StartDate = defaultStartDate; EndDate = currentDate; }*/
        if (!StartDate && !EndDate) { StartDate = ""; EndDate = ""; }
        if (globalreport === "DRDrillReport") {
            if (StartDate && EndDate && StartDate <= EndDate && currentDate >= EndDate && currentDate >= StartDate) {
                $.ajax({
                    url: "/Report/PreBuildReport/GetWorkflow",
                    type: 'GET',
                    data: {
                        startDate: StartDate,
                        endDate: EndDate,
                        runMode: Modes
                    },
                    success: function (response) {
                        if (response.success != false) {
                            var data = response.data;
                            drdrill = response.data;
                            if (data != 0) {
                                document.getElementById("EmptyAlert").hidden = true;
                                var selectElement = document.getElementById("workflowid");
                                while (selectElement.options.length > 1) {
                                    selectElement.remove(1);
                                }
                                for (var i = 0; i < data.length; i++) {
                                    var option = document.createElement("option");
                                    option.value = data[i].id;
                                    option.textContent = data[i].description;
                                    selectElement.appendChild(option);
                                }
                                $("#workflowid").val("");
                            }
                            else {
                                hideReportElements();
                                ReportDefaultImage.hidden = true;
                            }
                        }
                        else { NotificationMsg(response.message); }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                        ReportDefaultImage.hidden = true;
                    }
                });
            }
            else { $("#workflowid").empty(); enableAllLinks(); }
        }
        else if (globalreport == "InfraObjectSchedulerLogReport" || globalreport == "CyberResiliencyScheduleLogReport") {
            validateDates();
            if (StartDate && EndDate && StartDate <= EndDate && currentDate >= EndDate && currentDate >= StartDate) {
                ReportDefaultImage.hidden = true;
                Loading.hidden = false;
                EmptyAlert.hidden = true;
                $('#btndrdrill').html('');
                disableAllLinks();
                if (globalreport == "InfraObjectSchedulerLogReport") {
                    //var WorkflowName = selectedOption.textContent;
                    $.ajax({
                        url: "/Report/PreBuildReport/GetInfraObjectSchedulerReportList",
                        type: 'GET',
                        data: {
                            startDate: StartDate,
                            endDate: EndDate,
                            type: "PDF"
                        },
                        success: function (response) {
                            if (response.data != 0 && response.data != null && response.success != false) {
                                var reportName = "InfraObjectSchedulerLogReport";
                                var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                                loadAjax(loadReportUrl, reportName, response.data);
                            }
                            else if (response.success == false) { hideReportElements(); NotificationMsg(response.message); }
                        },
                        error: function (error) {
                            console.error(error);
                            hideReportElements();
                        }
                    });
                }
                if (globalreport == "CyberResiliencyScheduleLogReport") {
                    //var WorkflowName = selectedOption.textContent;
                    $.ajax({
                        url: "/Report/PreBuildReport/GetCyberResiliencyScheduleLogReportList",
                        type: 'GET',
                        data: {
                            startDate: StartDate,
                            endDate: EndDate,
                            type: "PDF"
                        },
                        success: function (response) {
                            if (response.data != 0 && response.data != null && response.success != false) {
                                var reportName = "CyberResiliencyScheduleLogReport";
                                var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                                loadAjax(loadReportUrl, reportName, response.data);
                            }
                            else if (response.success == false) { hideReportElements(); NotificationMsg(response.message); }
                        },
                        error: function (error) {
                            console.error(error);
                            hideReportElements();
                        }
                    });
                }
            }
        }
        else if (globalreport == "BulkImportReport") {
            if (StartDate && EndDate && StartDate <= EndDate && currentDate >= EndDate && currentDate >= StartDate) {
                $.ajax({
                    url: "/Report/PreBuildReport/GetInfraObjectIdBulkImport",
                    type: 'GET',
                    data: {
                        startDate: StartDate,
                        endDate: EndDate
                    },
                    success: function (response) {
                        var data = response.data;
                        if (data.length < 2) {
                            $("#bulkImportId").find("option[value='all']").remove();
                        } else {
                            if (!$("#bulkImportId").find("option[value='all']").length) {
                                $("#bulkImportId").append('<option value="all">All</option>');
                            }
                        }
                        if (data != 0 && response.success != false) {
                            document.getElementById("EmptyAlert").hidden = true;
                            var selectElement = document.getElementById("bulkImportId");
                            while (selectElement.options.length > 1) {
                                selectElement.remove(1);
                            }
                            for (var i = 0; i < data.length; i++) {
                                var option = document.createElement("option");
                                option.value = data[i].id;
                                option.textContent = data[i].description;
                                selectElement.appendChild(option);
                            }
                            $("#bulkImportId").val("");
                        }
                        else if (response.success == false) { NotificationMsg(response.message); }
                        else {
                            hideReportElements();
                            ReportDefaultImage.hidden = true;
                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                        ReportDefaultImage.hidden = true;
                    }
                });
            }
            else { $("#bulkImportId").empty(); enableAllLinks(); }
        }
        else if (globalreport == "DriftReport") {
            $("#StatusId option").remove();
            $('#btndrdrill').html('');
            if (StartDate && EndDate && StartDate <= EndDate && currentDate >= EndDate && currentDate >= StartDate) {
                $.ajax({
                    url: "/Report/PreBuildReport/GetDriftReportInfraName",
                    type: 'GET',
                    data: {
                        startDate: StartDate,
                        endDate: EndDate
                    },
                    success: function (response) {
                        $("#InfraobjectDriftId option").remove();
                        var data = response.data;
                        if (data.length < 2) {
                            $("#InfraobjectDriftId").find("option[value='all']").remove();
                        } else {
                            if (!$("#InfraobjectDriftId").find("option[value='all']").length) {
                                $("#InfraobjectDriftId").append('<option value="all">All</option>');
                            }
                        }
                        if (data != 0 && response.success != false) {
                            document.getElementById("EmptyAlert").hidden = true;
                            var selectElement = document.getElementById("InfraobjectDriftId");
                            while (selectElement.options.length > 1) {
                                selectElement.remove(1);
                            }
                            for (var i = 0; i < data.length; i++) {
                                var option = document.createElement("option");
                                option.value = data[i].infraObjectId;
                                option.textContent = data[i].infraObjectName;
                                selectElement.appendChild(option);
                            }
                            $("#InfraobjectDriftId").val("");
                        }
                        else if (response.success == false) { NotificationMsg(response.message); }
                        else {
                            hideReportElements();
                            ReportDefaultImage.hidden = true;
                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                        ReportDefaultImage.hidden = true;
                    }
                });
            }
            else { $("#InfraobjectDriftId").empty(); enableAllLinks(); }
        }
        else if (globalreport == "AirGapReport") {
            if (values == "Today" || values == "Yesterday") {
                $('#btndrdrill').html('');
                StartDate = values == "Today" ? currentDate : tdyYesterday;
                EndDate = values == "Today" ? currentDate : tdyYesterday;
                $("#clndrStart").val(StartDate);
                $("#workflowEnd").val(EndDate);
            }
            if (StartDate && EndDate && StartDate <= EndDate && currentDate >= EndDate && currentDate >= StartDate && validateDates()) {
                $("#airGapList option").remove();
                $.ajax({
                    url: "/Report/PreBuildReport/GetAirGapList",
                    type: 'GET',
                    data: {
                        startDate: StartDate,
                        endDate: EndDate
                    },
                    success: function (response) {
                        if (response != 0 && response != null && response.success != false && response.data.length > 0) {
                            var data = response.data;
                            if (data.length < 2) {
                                $("#airGapList").find("option[value='all']").remove();
                            }
                            else {
                                $("#airGapList").append('<option value="all">All</option>');
                            }
                            var selectElement = document.getElementById("airGapList");
                            for (var i = 0; i < data.length; i++) {
                                var option = document.createElement("option");
                                option.value = data[i].airGapId;
                                option.textContent = data[i].airGapName;
                                selectElement.appendChild(option);
                            }
                            $("#airGapList").val('');
                            EmptyAlert.hidden = true;
                            $("#airGapList").val('');
                            ReportDefaultImage.hidden = false;
                        }
                        else if (response.success == false) { NotificationMsg(response.message); }
                        else {
                            hideReportElements();
                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                        ReportDefaultImage.hidden = true;
                    }
                });
            }
            else { $("#airGapList").empty(); enableAllLinks(); }
        }
        else if (globalreport == "SnapReport") {
            $('#btndrdrill').html('');
            $("#SnapsDropDownId").empty();
            ReportDefaultImage.hidden = false;
            if (StartDate && EndDate && StartDate <= EndDate && currentDate >= EndDate && currentDate >= StartDate && validateDates()) {
                var dateMix = StartDate + "," + EndDate; getSnapList(dateMix);
            }
        }
        else if (globalreport != "UserActivityReport") {
            if (StartDate && EndDate && StartDate <= EndDate && currentDate >= EndDate && currentDate >= StartDate) {
                $.ajax({
                    url: "/Report/PreBuildReport/GetWorkflow",
                    type: 'GET',
                    data: {
                        startDate: StartDate,
                        endDate: EndDate
                    },
                    success: function (response) {
                        var data = response.data;
                        if (data != 0 && response.success != false) {
                            document.getElementById("EmptyAlert").hidden = true;
                            var selectElement = document.getElementById("workflowid");
                            while (selectElement.options.length > 1) {
                                selectElement.remove(1);
                            }
                            for (var i = 0; i < data.length; i++) {
                                var option = document.createElement("option");
                                option.value = data[i].id;
                                option.textContent = data[i].description;
                                selectElement.appendChild(option);
                            }
                            $("#workflowid").val("");
                        }
                        else if (response.success == false) { NotificationMsg(response.message); }
                        else {
                            hideReportElements();
                            ReportDefaultImage.hidden = true;
                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                        ReportDefaultImage.hidden = true;
                    }
                });
            }
            else { $("#workflowid").empty(); enableAllLinks(); }
        }
    });
    $('#workflowid').on('change', async function () {
        var StartDate = $('#startDate').find("input[type=date]").val();
        var EndDate = $('#workflowendDate').find("input[type=date]").val();
        var WorkflowID = workflowid.value;
        IDworkflow = WorkflowID;
        var selectElement = document.getElementById("workflowid");
        var selectedOption = selectElement.options[selectElement.selectedIndex];
        if (!selectedOption || selectedOption.value === "") {
            enableAllLinks();
            EmptyAlert.hidden = true;
            Loading.hidden = true;
            return;
        }
        ReportData = selectedOption.textContent.split(/( )/)[0];        
        var ExecutionMode = document.getElementById("executionModeId");
        var ExecutionModes = ExecutionMode.options[ExecutionMode.selectedIndex];
        var Modes = ExecutionModes.textContent;
        isCustom = $('#customizedContainer').find('option:selected').text();
        if (isCustom == "Default") {
            isCustom = false;
        } else {
            isCustom = true;
        }

        if (WorkflowID != "") {
            ReportDefaultImage.hidden = true;
            Loading.hidden = false;
            EmptyAlert.hidden = true;
            $('#btndrdrill').html('');
            disableAllLinks();
            if (globalreport == "RTOReport") {
                var WorkflowName = selectedOption.textContent;
                $.ajax({
                    url: "/Report/PreBuildReport/GetRtoReport",
                    type: 'GET',
                    data: {
                        workflowId: WorkflowID,
                        workflowName: WorkflowName,
                        startDate:StartDate,
                        endDate:EndDate,
                        type: "PDF"
                    },
                    success: function (response) {
                        if (response.data != 0 && response.data != null && response.success != false) {
                            var reportName = "RTOReport";
                            var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                            loadAjax(loadReportUrl, reportName, response.data);
                        }
                        else if (response.success == false) { NotificationMsg(response.message); }
                        else {
                            hideReportElements();
                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                    }
                });

            }
            else {

                $.ajax({
                    url: "/Report/PreBuildReport/GetAllWorkflow",
                    type: 'GET',
                    data: {
                        workflowId: WorkflowID,
                        type: "PDF",
                        runMode: Modes,
                        isCustom: isCustom
                    },
                    success: function (response) {
                        disableAllLinks();
                        if (response != 0 && response != null && response.success != false) {
                            var reportName = "DRDrillReport";
                            var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                            loadAjax(loadReportUrl, reportName, response.data);
                        }
                        else {
                            hideReportElements();

                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                    }
                });
            }
        }
        else {
            enableAllLinks();
            EmptyAlert.hidden = true;
            Loading.hidden = true;
        }
    });

    $('#SnapsDropDownId').on('change', async function () {
        var snapReportElement = document.getElementById("SnapsDropDownId");
        if (snapReportElement && snapReportElement.selectedIndex >= 0) {
            var SnapsDropDownIds = SnapsDropDownId.value;
            SnapDropDownValues = SnapsDropDownIds;
            var snapReportElementText = snapReportElement.options[snapReportElement.selectedIndex].textContent;
            ReportData = snapReportElementText == 'All' ? 'All_SnapTag' : snapReportElementText;

            if (SnapsDropDownIds != "") {
                ReportDefaultImage.hidden = true;
                EmptyAlert.hidden = true;
                Loading.hidden = false;
                disableAllLinks();
                if (globalreport == "SnapReport") {

                    $.ajax({
                        url: "/Report/PreBuildReport/GetCyberSnapsBySnapId",
                        type: 'GET',
                        data: {
                            cyberSnapTagName: SnapsDropDownIds,
                            snapStartDate: snapStartDate,
                            snapEndDate: snapEndDate,
                            type: "PDF"
                        },
                        success: function (response) {
                            if (response.data != 0 && response.data != null && response.success != false) {
                                var reportName = "cyberSnapReport";
                                var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                                loadAjax(loadReportUrl, reportName, response.data);
                            }
                            else if (response.success == false) {
                                NotificationMsg(response.message);
                            }
                            else {
                                hideReportElements();
                            }
                        },
                        error: function (error) {
                            console.error(error);
                            hideReportElements();
                        }
                    });

                }
            }
        }
    });

    async function RPOSLADeviationInfraDDL() {
        var selectElement = document.getElementById("InfraobjectDeviation");
        selectElement.innerHTML = '';
        var option = document.createElement("option");
        option.value = '';
        option.textContent = "Select InfraObject Name";
        selectElement.appendChild(option);
    }
    //Resilience Readiness Report Operational Service dropdown
    $('#drReadyBusinessServiceId').on('change', async function () {
        $('#btndrdrill').html('');
        var Businessserviceid = drReadyBusinessServiceId.value;
        var drReadyBusinessServiceElement = document.getElementById("drReadyBusinessServiceId");
        var drReadyBusinessServiceElementText = drReadyBusinessServiceElement.options[drReadyBusinessServiceElement.selectedIndex].textContent;
        ReportData = drReadyBusinessServiceElementText == 'All' ? 'All Operational Service' : drReadyBusinessServiceElementText;
        IDDrBusinesservice = Businessserviceid;
        if (Businessserviceid != "") {
            ReportDefaultImage.hidden = true;
            EmptyAlert.hidden = true;
            Loading.hidden = false;
            disableAllLinks();
            $.ajax({
                url: "/Report/PreBuildReport/GetDRReadyStatus",
                type: 'GET',
                data: {
                    businessServiceId: Businessserviceid,
                },
                success: function (response) {
                    disableAllLinks();
                    if (response.success == true && response.data.length > 0) {
                        var reportName = "DRReadyReport";
                        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                        loadAjax(loadReportUrl, reportName, response.data);
                    }
                    else {
                        hideReportElements();
                    }
                },
                error: function (error) {
                    console.error(error);
                    hideReportElements();
                }
            });
        }
    });

    $('#BusinessServiceID').on('change', async function () {
        $('#btndrdrill').html('');         
        var Businessserviceid = BusinessServiceID.value;
        IDBusinessservice = Businessserviceid;    
        var businessServiceElements = document.getElementById("BusinessServiceID");
        var businessServiceText = businessServiceElements.options[businessServiceElements.selectedIndex].text;
        ReportData = businessServiceText == "All" ? "All Operational Service" : businessServiceText;
        if (Businessserviceid != "" ) {
            var StartDate = $('#startDate').find("input[type=date]").val();
            var EndDate = $('#endDate').find("input[type=date]").val(); 
            if (!StartDate && !EndDate) { StartDate = defaultStartDate; EndDate = currentDate; }
            if (globalreport == "RPOSLADeviationReport") {
                RPOSLAclndrClick = false;
                var validation = validateDates();
                ReportDefaultImage.hidden = false;
                EmptyAlert.hidden = true;
                $("#InfraobjectDeviation").val("").empty().trigger("change");
                $.ajax({
                    url: "/Report/PreBuildReport/GetInfraObjectByBusinessServiceId",
                    type: 'GET',
                    data: {
                        businessServiceId: Businessserviceid,
                    },
                    success: function (response) {
                        if (response.success) {
                            var data = response.data;
                            RPOSLADeviationInfraDDL();
                            var selectElement = document.getElementById("InfraobjectDeviation");
                            for (var i = 0; i < data.length; i++) {
                                var option = document.createElement("option");
                                option.value = data[i].id;
                                option.textContent = data[i].name;
                                selectElement.appendChild(option);
                            }
                        }
                        else {
                            hideReportElements();
                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                    }
                });
            }
            else if (globalreport == "DRReadinessLog") {
                var StartDate = $('#startDate').find("input[type=date]").val();
                var EndDate = $('#workflowendDate').find("input[type=date]").val();
                if (!StartDate && !EndDate) { StartDate = defaultStartDate; EndDate = currentDate; }
                if (validateDates()) {
                    disableAllLinks();
                    ReportDefaultImage.hidden = true;
                    EmptyAlert.hidden = true;
                    Loading.hidden = false;
                    $.ajax({
                        url: "/Report/PreBuildReport/GetDrReadyExecution",
                        type: 'GET',
                        data: {
                            startDate: StartDate,
                            endDate: EndDate,
                            businessServiceId: Businessserviceid
                        },
                        success: function (response) {
                            if (response.data != 0 && response.success != false) {
                                var reportName = "DRReadinessLog";
                                var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                                loadAjax(loadReportUrl, reportName, response.data);
                            }
                            else {
                                hideReportElements();
                            }
                        },
                        error: function (error) {
                            console.error(error);
                            hideReportElements();
                        }
                    });
                }
            }
            else if (globalreport == "InfraObjectSummaryReport") {
                ReportDefaultImage.hidden = true;
                EmptyAlert.hidden = true;
                Loading.hidden = false;
                disableAllLinks();
                $.ajax({
                    url: "/Report/PreBuildReport/GetInfraobjectSummary",
                    type: 'GET',
                    data: {
                        businessServiceId: Businessserviceid,
                        type: "PDF"
                    },
                    success: function (response) {
                        if (response.data.length > 0 && response.success != false) {
                            var reportName = "InfraObjectSummaryReport";
                            var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                            loadAjax(loadReportUrl, reportName, response.data);
                        }
                        else {
                            hideReportElements();
                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                    }
                });
            }            
        }
        else { enableAllLinks(); EmptyAlert.hidden = true; Loading.hidden = true; }
    });

    $('#InfraobjectDeviation').on('change', async function () {
        var Businessserviceid = BusinessServiceID.value;
        var rpoOption = $('#dateOption').val();
        var InfraId = $('#InfraobjectDeviation').val();
        RPOSLAclndrClick = false;
        if (InfraId != "" && InfraId != null) {
            rpoSLAStartDate = null;
            rpoSLAEndDate = null;
            var validation = validateDates();          
            if (validation) {
                if (rpoOption == "Daily") {
                    var rpoStartDate = $('#rpostartDate').find("input[type=date]").val();
                    rpoStartDate = new Date(rpoStartDate);
                    rpoSLAEndDate = rpoStartDate.toLocaleDateString('en-CA');
                    rpoSLAStartDate = rpoStartDate.toLocaleDateString('en-CA');
                }
                else if (rpoOption == "Weekly") {
                    var rpoStartDate = $('#rpostartDate').find("input[type=week]").val();
                    const selectedWeek = rpoStartDate;
                    const [year, week] = selectedWeek.split('-W');
                    const selectedDate = new Date(year, 0, 1 + (week - 1) * 7 - (new Date(year, 0, 1).getDay() || 7));
                    const startDate = new Date(selectedDate);
                    startDate.setDate(startDate.getDate() + 1);
                    var endDate = new Date(startDate);
                    endDate.setDate(startDate.getDate() + 6);
                    var todayDate = new Date();
                    if (endDate > todayDate) {
                        endDate = new Date(todayDate);
                    }
                    const startDateFormatted = startDate.toLocaleDateString('en-CA');// Format dates to dd-mm-yyyy format
                    const endDateFormatted = endDate.toLocaleDateString('en-CA');

                    rpoSLAEndDate = endDateFormatted;
                    rpoSLAStartDate = startDateFormatted;
                }
                else if (rpoOption == "Monthly") {
                    var selectedMonth = $('#rpoclndrStart').val();
                    var startDate = new Date(selectedMonth + '-01');
                    var today = new Date();
                    var endDate;

                    if (startDate.getFullYear() === today.getFullYear() && startDate.getMonth() === today.getMonth()) {
                        endDate = today;
                    } else {
                        endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
                    }
                    endDate.setHours(23, 59, 59, 999);
                    rpoSLAEndDate = endDate.toLocaleDateString('en-CA');
                    rpoSLAStartDate = startDate.toLocaleDateString('en-CA');
                }              
                if (rpoSLAStartDate && rpoSLAEndDate && rpoSLAEndDate >= rpoSLAStartDate) {
                    var businessServiceElementId = document.getElementById("BusinessServiceID");
                    ReportData = businessServiceElementId.options[businessServiceElementId.selectedIndex].text;
                    ReportDefaultImage.hidden = true;
                    EmptyAlert.hidden = true;
                    Loading.hidden = false;
                    disableAllLinks();
                    $.ajax({
                        url: "/Report/PreBuildReport/GetRpoSlaDeviationReport",
                        type: 'GET',
                        data: {
                            businessServiceId: Businessserviceid,
                            infraObjectId: InfraId,
                            startDate: rpoSLAStartDate,
                            endDate: rpoSLAEndDate
                        },
                        success: function (response) {
                            if (response.data != 0 && response.success != false) {
                                var reportName = "RPOSLADeviationReport";
                                var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                                loadAjax(loadReportUrl, reportName, response.data);
                            }
                            else {
                                hideReportElements();
                            }
                        },
                        error: function (error) {
                            console.error(error);
                            hideReportElements();
                        }
                    });
                };
            }

        }
    });

    $('#BusinessServiceIDLicense').on('change', async function () {
        clearDateError();
        var selectedValues = $(this).val();
        $('#BusinessServiceIDLicense option').prop('disabled', false);
        if (selectedValues.length > 0) {
            var isAllSelected = selectedValues[0] === "All";
            $('#BusinessServiceIDLicense option').each(function () {
                $(this).prop('disabled', ($(this).val() !== "All") === isAllSelected);
            });
        }
    });

    $('#LicenseButtonID').on('click', function () {
        $('#btndrdrill').html('');
        var selectedValuesBS = Array.from($('#BusinessServiceIDLicense').val());
        var Businessserviceid = selectedValuesBS.join(',');
        var selectedValuesLpo = $('#ddlLicenceID').val();
        var Licensepoid = selectedValuesLpo.join(',');
        var ddlDerivedID = $('#ddlDerivedID').val();
        var option = $('#licenseOption').val();
        var Childpoid = ddlDerivedID.join(',');
        if (option === "Operational Service" && Businessserviceid === '') {
            showError("errorBLicense", "Select operational service name"); return false;
        }
        else if (option === "PO Number" && Licensepoid === '') {
            showError("errorLicensepo", "Select license PO number"); return false;

        }
        else {
            if (globalreport == "LicenseUtilizationReport") {
                disableAllLinks();
                if (Licensepoid != "" || Businessserviceid != "") {
                    ReportDefaultImage.hidden = true;
                    EmptyAlert.hidden = true;
                    Loading.hidden = false;
                    $.ajax({
                        url: "/Report/PreBuildReport/LicenseUtilizationReport",
                        type: 'POST',
                        traditional: true,
                        data: {
                            Businessserviceid: Businessserviceid,
                            Licensepoid: Licensepoid,
                            Derivedpoid: Childpoid,
                            Type: "pdf"

                        },
                        dataType: 'json',
                        success: function (response) {
                            if (response.success != false ) {
                                var reportName = "LicenseUtilizationReport";
                                var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                                loadAjax(loadReportUrl, reportName, response.data);
                            }
                            else {
                                hideReportElements();
                            }
                        },
                        error: function (error) {
                            console.error(error);
                            hideReportElements();
                        }
                    });
                }
                else {
                    enableAllLinks();
                    EmptyAlert.hidden = true;
                    Loading.hidden = true;
                }
            }
        }
    });

    $('#LicenseCancelId').on('click', function () {

        $('#BusinessServiceIDLicense').val('').trigger('change');
        $('#ddlLicenceID').val('').trigger('change');
        $('#ddlDerivedID').val('').trigger('change');
        EmptyAlert.hidden = true;
        Loading.hidden = true;
        $('#btndrdrill').html('');
        ReportDefaultImage.hidden = false;
    });

    $('#ddlLicenceID').on('change', async function () {
        clearDateError();
        var Businessserviceid = $('#BusinessServiceIDLicense');
        var ddlDerivedID = $('#ddlDerivedID');
        var selectedValuesLpo = $(this).val();
        $('#ddlLicenceID option').prop('disabled', false);
        if (selectedValuesLpo.length > 0) {
            var isAllSelect = selectedValuesLpo[0] === "All";
            $('#ddlLicenceID option').each(function () {
                $(this).prop('disabled', ($(this).val() !== "All") === isAllSelect);
            });
        }
        var parentPonumber = selectedValuesLpo.join(',');
        if (parentPonumber === '') {
            $("#ddlDerivedID").val("");
        }
        //DropDown Value Change Withrespect to Parent PO NUmber
        $.ajax({
            url: "/Report/PreBuildReport/GetChildPONumberByParentId",
            type: 'GET',
            data: { parentPoNumber: parentPonumber },
            dataType: 'json',
            success: function (datas) {
                if (datas.success != false) {
                    var response = datas.data;
                    var selectElement = document.getElementById("ddlDerivedID");
                    $('#ddlDerivedID').empty();
                    if (response.length > 1) { $("#ddlDerivedID").append("<option value='All'> All </option>"); }
                    $("#ddlDerivedID").prop('disabled', !(response && response.length > 0));
                    for (var i = 0; i < response.length; i++) {
                        var option = document.createElement("option");
                        option.value = response[i].id;
                        option.textContent = response[i].poNumber;
                        selectElement.appendChild(option);
                    }
                }
                else { NotificationMsg(datas.message); }
            },
            error: function (error) {
                debugger;
                // console.error('Error fetching options:', error);
            }
        });

        function updateDropdown2(options) {
            // Clear existing options in dropdown2
            $('#ddlDerivedID').empty();
            $("#ddlDerivedID").append("<option value='All'> All </option>");

            // Add new options based on the server response
            for (var i = 0; i < options.length; i++) {
                $('#ddlDerivedID').append('<option value="' + options[i].value + '">' + options[i].poNumber + '</option>');
            }
        }
    });

    $('#ddlDerivedID').on('change', async function () {
        clearDateError();
        var ddlLicenceID = $('#ddlLicenceID');
        var Businessserviceid = $('#BusinessServiceIDLicense');
        var selectedValuesLpo = $(this).val();
        $('#ddlDerivedID option').prop('disabled', false);
        if (selectedValuesLpo.length > 0) {
            var isAllSelect = selectedValuesLpo[0] === "All";
            $('#ddlDerivedID option').each(function () {
                $(this).prop('disabled', ($(this).val() !== "All") === isAllSelect);
            });
        }
        var childPonumber = selectedValuesLpo.join(',');
    });
    $('#AllUsers').on('change', async function () {      
        $('#btndrdrill').html('');
        currentAjaxRequest && currentAjaxRequest.abort();
        var UserId = $('#AllUsers').val();
        ReportData = UserId == "All" ? "All Users" : $('#AllUsers option:selected').text();
        nameuser = ReportData;
        var StartDate = $('#startDate').find("input[type=date]").val();
        var EndDate = $('#endDate').find("input[type=date]").val();
        if (!StartDate && !EndDate) { StartDate = defaultStartDate; EndDate = currentDate; }
        if (StartDate && EndDate && UserId && EndDate >= StartDate) {
            disableAllLinks();
            ReportDefaultImage.hidden = true;
            EmptyAlert.hidden = true;
            Loading.hidden = false;
            currentAjaxRequest = $.ajax({
                url: "/Report/PreBuildReport/GetUserActivityDetails",
                type: 'GET',
                data: {
                    userId: UserId,
                    startDate: StartDate,
                    endDate: EndDate
                },
                success: function (response) {
                    currentAjaxRequest = null
                    if (response.data != 0 && response != null && response.success != false) {
                        var reportName = "UserActivityReport";
                        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                        loadAjax(loadReportUrl, reportName,response.data);
                    }
                    else {
                        hideReportElements();
                    }
                },
                error: function (error) {
                        currentAjaxRequest = null
                        //console.error(error);
                        hideReportElements();
                }
            });
        }
    });
    var dropdown = document.getElementById("reportDropdown");

    dropdown.addEventListener("change", function () {
        var selectedReport = dropdown.value;
        ;
    });

    $('#dateOption').on('change', async function () {
        clearDateError();
        hideReportElements();
        ReportDefaultImage.hidden = false;
        RPOSLAclndrClick = false;
        $("#Infraobject").val("").trigger("change");
        $("#BusinessServiceID").val("").trigger("change");
        $("#InfraobjectDeviation").val("").empty().trigger("change");
        var rpoOption = $('#dateOption').val();
        if (rpoOption != null) {
            rpostartDateInput.max = currentDate;
            if (rpoOption === "Daily") {

                $('#rpoclndrStart').attr('type', 'date');
                $('#rpostartDate input[type=date]').val('');
                $('#spanStartDate').text('Select Date');
                $('#rpoendDate').hide();
                $('#rpocalendaricon').show();
            }
            else if (rpoOption === "Weekly") {
                $('#rpoclndrStart').attr('type', 'week');
                $('#rpostartDate input[type=week]').val('');
                $('#spanStartDate').text('Select Week');
                $('#rpoendDate').hide();
                $('#rpocalendaricon').hide();
                var today = new Date();

                var year = today.getFullYear();
                var week = getWeekNumber(today);
                var month = today.getMonth() + 1;
                if ((week == "1" && month == "12") || week == "53"){
                    var currentWeek = (year + 1) + '-W01';
                }
                else {
                    var currentWeek = year + '-W' + (week < 10 ? '0' + week : week);
                }

                $('#rpoclndrStart').attr('max', currentWeek);
            }
            else if (rpoOption === "Monthly") { 
                $('#rpoclndrStart').attr('type', 'month');
                $('#rpostartDate input[type=month]').val('');
                $('#spanStartDate').text('Select Month');
                $('#rpocalendaricon').hide();
                $('#rpoendDate').hide();
                var today = new Date();
                var year = today.getFullYear();
                var month = today.getMonth() + 1;
                $('#rpoclndrStart').attr('max', year + "-" + (month < 10 ? '0' + month : month));
            }
        }
        else {
            $('#rpostartDate input[type=date]').val('');
            $('#rpostartDate input[type=week]').val('');
            $('#rpostartDate input[type=month]').val('');
            $('#rpoclndrStart').attr('type', 'date');
            $('#rpocalendaricon').show();
            $('#spanStartDate').text('Select Date');
        }
    });
    function getWeekNumber(today) {
        var startOfYear = new Date(today.getFullYear(), 0, 1);
        var dayOfYear = ((today - startOfYear + 86400000) / 86400000);
        var currentWeek = Math.ceil((dayOfYear + startOfYear.getDay()) / 7);
        return currentWeek;
    }
    $('#rpoclndrStart').on('change', function () {
        clearDateError();
        hideReportElements();
        ReportDefaultImage.hidden = false;
        $("#Infraobject").val("").trigger("change");
        $("#BusinessServiceID").val("").trigger("change");
        $("#rpoerrorStart").hide().text("");
        $("#InfraobjectDeviation").val("").empty().trigger("change");
        RPOSLAclndrClick = true;
        var validation = validateDates();
    });
    $('#Infraobject').on('change', async function () {
        var Infraobject = document.getElementById('Infraobject');
        var InfraId = Infraobject.value;
        ReportData = Infraobject.options[Infraobject.selectedIndex].text;
        IDInfra = InfraId;
        if (InfraId != "" && InfraId != null) {
            $('#btndrdrill').html('');
            var InfraType = $('select[id="Infraobject"] :selected').attr('data-type');
            var selectElement = document.getElementById("Infraobject");
            var selectedOption = selectElement.options[selectElement.selectedIndex];
            infraObjectName = selectedOption.textContent;
            EmptyAlert.hidden = true;
            if (globalreport == "RPOSLAReport") {

                rpoSLAStartDate = null;
                rpoSLAEndDate = null;
                var validation = validateDates();
                if (validation) {
                    if (InfraType != '' && InfraType != null) {
                        var rpoOption = $('#dateOption').val();
                        var rpoStartDate = $('#rpostartDate').find("input[type=date]").val();
                        rpoStartDate = new Date(rpoStartDate);
                        if (rpoOption == "Daily") {
                            rpoSLAEndDate = rpoStartDate.toLocaleDateString('en-CA');
                            rpoSLAStartDate = rpoStartDate.toLocaleDateString('en-CA');
                        }
                        else if (rpoOption == "Weekly") {
                            var rpoStartDate = $('#rpostartDate').find("input[type=week]").val();
                            const selectedWeek = rpoStartDate;
                            const [year, week] = selectedWeek.split('-W');
                            const selectedDate = new Date(year, 0, 1 + (week - 1) * 7 - (new Date(year, 0, 1).getDay() || 7));
                            const startDate = new Date(selectedDate);
                            startDate.setDate(startDate.getDate() + 1); //add one date for get correct selected date 
                            var endDate = new Date(startDate);
                            endDate.setDate(startDate.getDate() + 6);
                            var todayDate = new Date();
                            if (endDate > todayDate) {
                                endDate = new Date(todayDate);
                            }
                            // Format dates to dd-mm-yyyy format
                            const startDateFormatted = startDate.toLocaleDateString('en-GB');
                            const endDateFormatted = endDate.toLocaleDateString('en-GB');

                            rpoSLAEndDate = endDateFormatted;
                            rpoSLAStartDate = startDateFormatted;
                        }
                        else if (rpoOption == "Monthly") {
                            var selectedMonth = $('#rpoclndrStart').val();
                            var startDate = new Date(selectedMonth + '-01');
                            var today = new Date();
                            var endDate;

                            if (startDate.getFullYear() === today.getFullYear() && startDate.getMonth() === today.getMonth()) {
                                endDate = today;
                            } else {
                                endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
                            }
                            endDate.setHours(23, 59, 59, 999);
                            rpoSLAEndDate = endDate.toLocaleDateString('en-CA');
                            rpoSLAStartDate = startDate.toLocaleDateString('en-CA');
                        }
                        if (validation && rpoSLAStartDate != null && rpoSLAEndDate != null) {
                            disableAllLinks();
                            ReportDefaultImage.hidden = true;
                            Loading.hidden = false;
                            $.ajax({
                                url: "/Report/PreBuildReport/GetRpoSLAReport",
                                type: 'GET',
                                data: {
                                    infraId: InfraId,
                                    startDate: rpoSLAStartDate,
                                    endDate: rpoSLAEndDate,
                                    type: InfraType,
                                    dateOption: rpoOption,
                                    infraName: infraObjectName
                                },
                                success: function (response) {
                                    if (response.success == true) {
                                        var reportName = "RPOSLAReport";
                                        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                                        loadAjax(loadReportUrl, reportName, response.data);
                                    }
                                    else {
                                        hideReportElements();
                                    }
                                },
                                error: function (error) {
                                    console.error(error);
                                    hideReportElements();
                                }
                            });
                        }
                    }
                    else {
                        ReportDefaultImage.hidden = true;
                        hideReportElements();
                    }
                }
            }
            else {
                disableAllLinks();
                ReportDefaultImage.hidden = true;
                Loading.hidden = false;
                $.ajax({
                    url: "/Report/PreBuildReport/GetInfraObjectList",
                    type: 'GET',
                    data: {
                        infraId: InfraId,
                        infraObjectType: InfraType
                    },
                    success: function (response) {
                        var reportName = "InfraObjectConfigurationReport";
                        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                        loadAjax(loadReportUrl, reportName,response.data);
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                    }
                });
            }
        }
        else {
            enableAllLinks();
            EmptyAlert.hidden = true;
            Loading.hidden = true;
        }
    });
    $('#todayId, #yesterdayId, #customId').on('click', function () {
        if (globalreport == "DRDrillReport") {
            var executionMode = "";

            if ($(this).is('#todayId')) {
                values = "Today";
                $("#startDate, #headStartDate, #headWorkflowendDate, #workflowendDate").hide();
                executionMode = "Execution";
            } else if ($(this).is('#yesterdayId')) {
                values = "Yesterday";
                $("#startDate, #headStartDate, #headWorkflowendDate, #workflowendDate").hide();
            } else if ($(this).is('#customId')) {
                values = "Custom";
                $("#clndrStart, #workflowEnd").val("").trigger("change");
                $("#startDate, #headStartDate, #headWorkflowendDate, #workflowendDate").show();
            }
            $("#customizedContainer select").val("Default").trigger("change");
            $("#executionModeId").val(executionMode).trigger("change");
            $("#startDate, #workflowendDate").toggle($(this).is('#customId'));
            $("#Workflow").show();
        }
        if (globalreport == "AirGapReport") {
            clearDateError();
            $("#airGapList option").remove();
            if ($(this).is('#todayId')) {
                values = "Today";
                executionMode = "Execution";
            } else if ($(this).is('#yesterdayId')) {
                values = "Yesterday";
            } else if ($(this).is('#customId')) {
                values = "Custom";
                $("#airGapList option").remove();
                $("#clndrStart, #workflowEnd").val("").trigger("change");
                $("#startDate, #headStartDate, #headWorkflowendDate, #workflowendDate").show();
            }
            $("#startDate, #workflowendDate").toggle($(this).is('#customId'));
            $(" #workflowEnd").val("").trigger("change");
        }
        if (globalreport == "SnapReport") {
            $('#btndrdrill').html('');
            clearDateError();
            $("#SnapsDropDownId").empty();
            if ($(this).is('#todayId')) {
                values = "Today";
                getSnapList(values);
            } else if ($(this).is('#yesterdayId')) {
                values = "Yesterday";
                getSnapList(values);
            } else if ($(this).is('#customId')) {
                values = "Custom";
                $("#clndrStart, #workflowEnd").val("").trigger("change");
                $("#startDate, #headStartDate, #headWorkflowendDate, #workflowendDate").show();
            }

            $("#startDate, #workflowendDate").toggle($(this).is('#customId'));
        }
    });

    function getSnapList(value) {
        var valied = false;
        $("#SnapsDropDownId").val("");

        var snapSplit = value.split(',');

        if (snapSplit.length == 1) {
            if (value == "Today") { snapStartDate = currentDate; snapEndDate = currentDate; }
            else if (value == "Yesterday") { snapStartDate = tdyYesterday; snapEndDate = currentDate; }
            valied = true;
        }
        else if (snapSplit.length == 2) {
            snapStartDate = snapSplit[0];
            snapEndDate = snapSplit[1];
            valied = validateDates();
        }
        if (valied) {

            $.ajax({
                url: "/Report/PreBuildReport/GetCyberSnapsList",
                dataType: "json",
                traditional: true,
                type: 'GET',
                data: {
                    snapStartDate: snapStartDate,
                    snapEndDate: snapEndDate
                },
                success: function (response) {
                    if (response != 0 && response != null && response.success != false) {
                        var data = response.data;
                        if (data != 0) {
                            var selectElement = $("#SnapsDropDownId").empty();
                            selectElement.append('<option value="" disabled selected>Select SnapTag Name</option>');
                            if (data.length >= 2) {
                                selectElement.append('<option value="all">All</option>');
                            }
                            data.forEach(item => {
                                selectElement.append(`<option value="${item.tag}">${item.tag}</option>`);
                            });
                            selectElement.val('');

                            ReportDefaultImage.hidden = false;                                                     
                        }
                        else { $("#SnapsDropDownId").empty(); hideReportElements(); }
                    }
                    else {
                        hideReportElements();
                        NotificationMsg(response.message);
                        console.error(response);
                    }
                },
                error: function (error) {
                    console.error(error);
                }

            })
        }
    }
    $('#executionModeId').on('change', function () {
        const ExecutionMode = $("#executionModeId").val();
        if (ExecutionMode) {
            showError("errorExecutionMode", "");
            $("#workflowid").val("");
            clearDateError();
        }
        $("#clndrStart, #workflowEnd").val("").trigger("change");

        if (ExecutionMode && (values === "Today" || values === "Yesterday")) {
            const date = (values === "Today") ? new Date() : yesterday;
            const dateString = date.toLocaleDateString('en-CA');
            const isCustom = $('#customizedContainer').find('option:selected').text(); 
            let vs = "";
            $.ajax({
                url: "/Report/PreBuildReport/GetWorkflow",
                type: 'GET',
                data: {
                    startDate: dateString,
                    endDate: dateString,
                    runMode: ExecutionMode,
                    isCustom: isCustom
                },
                success: function (response) {
                    var data = response.data;
                    drdrill = response.data;
                    if (data != 0 && response.success != false) {
                        $("#EmptyAlert").prop("hidden", true);
                        let selectElement = $("#workflowid").empty();
                        data.forEach(item => {
                            selectElement.append(new Option(item.description, item.id));
                        });
                        $("#workflowid").val("");
                    }
                    else if (response.success == false) { NotificationMsg(response.message); }
                    else {
                        hideReportElements();
                        ReportDefaultImage.hidden = true;
                    }
                },
                error: function (error) {
                    console.error(error);
                    hideReportElements();
                    ReportDefaultImage.hidden = true;
                }
            });
        } else {
            $("#workflowid").empty();
            enableAllLinks();
        }
    });

    $(".nav-link").on('click', function () {
        $(".nav-link").removeClass("active");
        $(this).addClass("active");
    });
    $("#airGapList").on("change", function () {
        var airGapName = airGapList.value;
        var selectElement = document.getElementById("airGapList");
        var selectedOption = selectElement.options[selectElement.selectedIndex];
        var StartDate = $('#startDate').find("input[type=date]").val();
        var EndDate = $('#workflowendDate').find("input[type=date]").val();
        if (values == "Today" || values == "Yesterday") {
            StartDate = values == "Today" ? currentDate : tdyYesterday;
            EndDate = values == "Today" ? currentDate : tdyYesterday;
        }
        AirGapName = selectedOption.textContent;
        ReportData = AirGapName;
        if (airGapName != null && airGapName != "" && validateDates()) {
            Loading.hidden = false;
            ReportDefaultImage.hidden = true;
            EmptyAlert.hidden = true;
            disableAllLinks();
            $.ajax({
                url: "/Report/PreBuildReport/GetAirGapReport",
                type: 'GET',
                data: {
                    startDate: StartDate,
                    endDate: EndDate,
                    airGapId: airGapName
                },
                success: function (response) {
                    try {
                        if (response.data.length > 0 && response.success !== false) {
                            var reportName = "AirGapReport";
                            var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                            loadAjax(loadReportUrl, reportName, response.data);
                        } else {
                            hideReportElements();
                        }
                    } catch (error) {
                        //  console.error(error);
                        hideReportElements();
                    }
                },
                error: function (error) {
                    // console.error(error);
                    hideReportElements();
                }
            });
        }
    })
    $('#bulkImportDropdown').on('change', function () {
        var operationId = bulkImportId.value;
        const bulkImport = document.getElementById("bulkImportId");
        const bulkImportOption = bulkImport.options[bulkImport.selectedIndex];
        const bulkImportName = bulkImportOption.textContent;
        ReportData = bulkImportName;
        var operationName = bulkImportName;
        $('#btndrdrill').html('');
        ReportDefaultImage.hidden = true;
        EmptyAlert.hidden = true;
        Loading.hidden = false;
        disableAllLinks();
        $.ajax({
            url: "/Report/PreBuildReport/Download_BulkImport_Report",
            type: 'GET',
            data: {
                operationId: operationId,
                operationName: operationName
            },
            success: function (response) {
                disableAllLinks();
                if (response.success == true && response.data.length > 0) {
                    var reportName = "BulkImportReport";
                    var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                    loadAjax(loadReportUrl, reportName, response.data);
                }
                else {
                    hideReportElements();
                }
            },
            error: function (error) {
                console.error(error);
                hideReportElements();
            }
        });

    });

    $('#InfraDrift').on('change', function () {
        $('#btndrdrill').html('');
        $("#StatusId option").remove();
        ReportDefaultImage.hidden = false;
        var DriftInfraId = InfraobjectDriftId.value;
        var StartDate = $('#startDate').find("input[type=date]").val();
        var EndDate = $('#workflowendDate').find("input[type=date]").val();      
        $.ajax({
            url: "/Report/PreBuildReport/GetDriftStatus",
            type: 'GET',
            data: {
                startDate: StartDate,
                endDate: EndDate,
                InfraId: DriftInfraId,               
            },
            success: function (response) {
                var data = response.data;
                if (data.length < 2) {
                    $("#StatusId").find("option[value='all']").remove();
                } else {
                    if (!$("#StatusId").find("option[value='all']").length) {
                        $("#StatusId").append('<option value="all">All</option>');
                    }
                }
                if (data != 0 && response.success != false) {
                    document.getElementById("EmptyAlert").hidden = true;
                    var selectElement = document.getElementById("StatusId");
                    while (selectElement.options.length > 1) {
                        selectElement.remove(1);
                    }
                    for (var i = 0; i < data.length; i++) {
                        var option = document.createElement("option");
                        option.value = data[i].entityStatus;
                        option.textContent = data[i].entityStatus;
                        selectElement.appendChild(option);
                    }
                    $("#StatusId").val("");
                }
                else if (response.success == false) {
                    NotificationMsg(response.message);
                    $("#StatusId").empty();
                }
                else {
                    hideReportElements();
                    ReportDefaultImage.hidden = true;
                    $("#StatusId").empty();
                }
            },
            error: function (error) {
                console.error(error);
                hideReportElements();
                ReportDefaultImage.hidden = true;
                $("#StatusId").empty();
            }
        });

    });
    $('#DriftStatus').on('change', function () {
        var DriftStatusId = StatusId.value;
        var DriftInfraId = InfraobjectDriftId.value;
        var StartDate = $('#startDate').find("input[type=date]").val();
        var EndDate = $('#workflowendDate').find("input[type=date]").val();
           disableAllLinks();
            ReportDefaultImage.hidden = true;
            Loading.hidden = false;
            $.ajax({
                url: "/Report/PreBuildReport/GetDriftReport",
                type: 'GET',
                data: {
                    startDate: StartDate,
                    endDate: EndDate,
                    InfraId: DriftInfraId,   
                    DriftStatusId: DriftStatusId,
                },
                success: function (response) {
                    try {
                        if (response.data.length > 0 && response.success !== false) {
                            var reportName = "DriftReport";
                            var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                            loadAjax(loadReportUrl, reportName, response.data);
                        } else {
                            hideReportElements();
                        }
                    } catch (error) {
                        //  console.error(error);
                        hideReportElements();
                    }
                },
                error: function (error) {
                    // console.error(error);
                    hideReportElements();
                }
            });

    });
    $('#licenseOption').on('change', function () {
        var option = $(this).val();
        $("#BusinessServiceLicense,#headBusinessServiceLicense").toggle(option === "Operational Service");
        $("#BusinessServiceIDLicense,#headBusinessServiceLicense").prop('disabled', !(option === "Operational Service")).val('');
        $("#LicenceID,#headLicenceID").toggle(option === "PO Number");
        $("#ddlLicenceID,#headLicenceID").prop('disabled', !(option === "PO Number"));
        $("#LicenseDerived,#headLicenseDerived").toggle(option === "PO Number");
        $("#ddlDerivedID,#headLicenseDerived").prop('disabled', !(option === "PO Number"));
        $("#ddlDerivedID,#BusinessServiceIDLicense,#ddlLicenceID").val("").trigger("change");
    });
    $('.load-partial').on('click', function () {
        $('#btndrdrill').html('');
        ReportDefaultImage.hidden = false;
        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
        var reportName = $(this).data('report-name');
        globalreport = reportName;

        if (reportName == "DRReadyReport") {
            $('#drReadyBusinessServiceId').val('').trigger("change");
        }

        if (reportName == "BusinessServiceSummaryReport") {
            $("#ReportsName").text("Operational Service Summary");
        }
        else {
            $("#ReportsName").text($(this).text());
        }
        ClearAndHide();
        var elementsToToggle = {
            UserActivityReport: ["startDate", "endDate", "headStartDate", "headEndDate", "UserDetails"],
            DRReadyReport: ["drReadyBusinessId"],
            InfraObjectConfigurationReport: ["InfraDgn"],
            DRDrillReport: ["navigationID", "executionMode", "headExecutionMode", "customizedContainer", "Workflow"],
            BusinessServiceSummaryReport: [],
            InfraObjectSummaryReport: ["BusinessID"],
            LicenseUtilizationReport: ["licenseFormat", "licensesubmit"],
            DataLagStatusReport: [],
            AirGapReport: ["navigationID", "AirGap"],
            SnapReport: ["navigationID", "SnapsDropDown"],
            DriftReport: ["startDate", "headStartDate", "workflowendDate", "headWorkflowendDate","InfraDrift","DriftStatus"],
            BulkImportReport: ["startDate", "headStartDate", "workflowendDate", "headWorkflowendDate", "bulkImportDropdown"],
            RPOSLAReport: ["rpostartDate", "headRpostartDate", "dateFormat", "headDateFormat", "InfraDgn"],
            RPOSLADeviationReport: ["rpostartDate", "headRpostartDate", "dateFormat", "headDateFormat", "BusinessID", "InfraDgnRPOSLADeviation"],
            DRReadinessLog: ["startDate", "headStartDate", "workflowendDate", "headWorkflowendDate", "BusinessID"],
            RTOReport: ["Workflow", "startDate", "headStartDate", "workflowendDate", "headWorkflowendDate"],
            CMDBImportSummary: [],
            InfraObjectSchedulerLogReport: ["startDate", "headStartDate", "workflowendDate", "headWorkflowendDate"],
            CyberResiliencyScheduleLogReport: ["startDate", "headStartDate", "workflowendDate", "headWorkflowendDate"],
        };
        if (reportName == "LicenseUtilizationReport") {
            $("#licensesubmit").show();
            $("#licenseOption").val("Operational Service").change();
        }
        EmptyAlert.hidden = true;
        Loading.hidden = true;
        elementsToToggle[reportName].forEach(element => $("#" + element).show());
        $("#BusinessServiceID,#Infraobject,#AllUsers,#dateOption,#InfraobjectDeviation").val("").trigger("change");
        clearDateError();
        $('#clndrEnd').prop('min', '2011-01-01');
        $('#workflowEnd').prop('min', '2011-01-01');
        if (reportName == "LicenseUtilizationReport") {
            $("#ddlDerivedID,#BusinessServiceIDLicense,#ddlLicenceID").val("").trigger("change");
        }
        if (reportName == "DRDrillReport" || reportName == "SnapReport") {
            $(".nav-link").removeClass("active");
            $("#todayId").addClass("active");
            document.getElementById('todayId').click();
            $("#workflowid").val("").trigger("change");
        }
        if (reportName == "RTOReport") {
            $("#workflowid").val("").trigger("change");
            $("#workflowid option").remove();
        }
        if (reportName == "SnapReport") {
            $("#SnapsDropDownId").val("").trigger("change");
        }
        //  $("#workflowendDate").trigger("change");
        var newOption = new Option('All', 'All');

        if (reportName == "RPOSLADeviationReport") {
            $("#BusinessServiceID option[value='All']").remove();
            RPOSLADeviationInfraDDL();
        } else if ($("#BusinessServiceID option[value='All']").length === 0) {
            $("#BusinessServiceID").prepend(new Option('All', 'All'));
        }
        if (reportName == "AirGapReport") {
            $("#airGapList option").remove();
            $(".nav-link").removeClass("active");
            $("#todayId").addClass("active");
            document.getElementById('todayId').click();
        }
        if (reportName == "BulkImportReport") {
            $("#bulkImportId option").remove();
        }
        if (reportName == "DriftReport") {
            $("#InfraobjectDriftId option").remove();
            $("#StatusId option").remove();
        }


        if (["BusinessServiceSummaryReport", "DataLagStatusReport", "CMDBImportSummary"].includes(reportName)) {
            if (reportName == "DataLagStatusReport") {
                disableAllLinks();
                ReportDefaultImage.hidden = true;
                Loading.hidden = false;
                $.ajax({
                    url: "/Report/PreBuildReport/GetDatalagStatusReport",
                    type: 'GET',
                    success: function (response) {
                        try {
                            if (response.data.length > 0 && response.success !== false) {
                                var reportName = "DataLagStatusReport";
                                var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                                loadAjax(loadReportUrl, reportName,response.data);
                            } else {
                                hideReportElements();
                            }
                        } catch (error) {
                            //  console.error(error);
                            hideReportElements();
                        }
                    },
                    error: function (error) {
                        // console.error(error);
                        hideReportElements();
                    }
                });
            }
            else if (reportName == "BusinessServiceSummaryReport") {
                disableAllLinks();
                ReportDefaultImage.hidden = true;
                Loading.hidden = false;
                $.ajax({
                    url: "/Report/PreBuildReport/GetBusinessServiceSummaryReport",
                    type: 'GET',
                    success: function (response) {
                        try {
                            if (response.data.length > 0 && response.success !== false) {
                                var reportName = "BusinessServiceSummaryReport";
                                var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                                loadAjax(loadReportUrl, reportName, response.data);
                            } else {
                                hideReportElements();
                            }
                        } catch (error) {
                            //  console.error(error);
                            hideReportElements();
                        }
                    },
                    error: function (error) {
                        // console.error(error);
                        hideReportElements();
                    }
                });
            }
            else {
                disableAllLinks();
                ReportDefaultImage.hidden = true;
                Loading.hidden = false;
                loadAjax(loadReportUrl, reportName);
            }
        }
    });

    var startDateInput = document.getElementById("clndrStart");
    var endDateInput = document.getElementById("clndrEnd");
    var endwfDateInput = document.getElementById("workflowEnd");

    startDateInput.max = currentDate;
    endDateInput.max = currentDate;
    endwfDateInput.max = currentDate;

});

function NotificationMsg(errorMessage) {
    var alertClass, iconClass, message;
    alertClass = "warning-toast";
    iconClass = "cp-exclamation toast_icon";
    message = ` ${errorMessage}`;
    $('#alertClass').removeClass().addClass(alertClass);
    $('#icon').removeClass().addClass(iconClass);
    $('#message').text(message);
    $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
    $(".dx-context-menu-container-border").css("display", "none");
    $(".dx-item ").css('background-color', '#F5F5F5s');
    $(".dx-context-menu-content-delimiter").css('height', '0.1px');
}

function ClearAndHide() {
    $('#Infraobject, #startDate input[type=date], #endDate input[type=date], #AllUsers, #workflowid, #BusinessServiceID,#BusinessServiceIDLicense,#ddlDerivedID,#ddlLicenceID,#workflowendDate input[type=date],#dateOption,#rpostartDate input[type=date],#InfraobjectDeviation,#drReadyBusinessServiceId,#executionModeId,#bulkImportId,#SnapsDropDownId,#InfraobjectDriftId,#StatusId, #customizedContainer').val('');
    ["startDate", "endDate", "UserDetails", "AirGap", "Workflow", "InfraDgn", "BusinessID", "LicenceID", "workflowendDate", "LicenseDerived", "BusinessServiceLicense", "licensesubmit", "dateFormat", "rpostartDate", "InfraDgnRPOSLADeviation", "drReadyBusinessId", "licenseFormat", "headExecutionMode", "headDateFormat", "headRpostartDate", "headStartDate", "headEndDate", "headWorkflowendDate", "headBusinessServiceLicense", "headLicenceID", "headLicenseDerived", "executionMode", "navigationID", "bulkImportDropdown", "SnapsDropDown", "DriftStatus", "InfraDrift", "customizedContainer"].forEach(element => {
        document.getElementById(element).style.display = "none";
    });
}

const elementEvents = [
    { id: "clndrEnd", events: ["change"] },
    { id: "workflowEnd", events: ["change"] },
    { id: "UserDetails", events: ["click", "change"] },
    { id: "Workflow", events: ["click", "change"] },
    { id: "InfraDgnRPOSLADeviation", events: ["click"] },
    { id: "InfraobjectDeviation", events: ["change"] }
];

// Dynamically add event listeners
elementEvents.forEach(({ id, events }) => {
    const element = document.getElementById(id);
    if (element) {
        events.forEach(eventType => {
            element.addEventListener(eventType, validateDates);
        });
    }
});

$(document).on('click', '.dxrd-image-zoomin, .dxrd-image-zoomout, .dx-list-item-selected', function () {
    var zoomValue = parseFloat($('input[aria-label="Zoom"]').val());
    $('.dxrd-image-zoomin').toggleClass('disables', zoomValue >= 500);
    $('.dxrd-image-zoomout').toggleClass('disables', zoomValue <= 25);
});

$('#clndrStart').on('change', function () {
    const startDate = $("#clndrStart").val();
    $('#clndrEnd').prop('min', startDate);
    $('#workflowEnd').prop('min', startDate);
    $("#workflowEnd").val("");
    $("#workflowid").empty();
    $("#clndrEnd").val("");
    $("#AllUsers").val("").trigger("change");
    $("#workflowendDate").trigger("change");
    $("#Infraobject").val("").trigger("change");
    $("#BusinessServiceID").val("").trigger("change");
    $("#SnapsDropDownId").empty();

    clearDateError();

    const errorExecutionMode = $("#executionModeId").val();
    if (startDate == currentDate) {
        $("#workflowEnd").val(startDate).trigger("change"); $("#clndrEnd").val(startDate);
    }
    if (globalreport === "DRDrillReport" && !errorExecutionMode && startDate) {
        showError("errorExecutionMode", !errorExecutionMode ? "Select execution mode" : "Select execution mode");
        $("#workflowid").val("");
        return false;
    } 

    //if (startDate) { clearDateError(); }
});
$('#clndrEnd').on('change', function () {
    var endDate = $("#clndrEnd").val();
    $("#AllUsers").val("").trigger("change");
    $("#Infraobject").val("").trigger("change");
    $("#BusinessServiceID").val("").trigger("change");
    if (endDate) { $("#errorEnd").hide().text(""); }
});
$('#workflowid').on('click', function () {
    const startDate = $("#clndrStart").val();
    const endDate = $("#workflowEnd").val();
    if (!startDate || !endDate || !startDate < endDate)
        $("#workflowid").empty(); 
});
function validateDates(event) {
    const triggeredElementId = event?.target?.id || "directCall"; 
    const startDate = $("#clndrStart").val();
    const endDate = $("#clndrEnd").val();
    const endDateWorkflow = $("#workflowEnd").val();
    const errorExecutionMode = $("#executionModeId").val();
    var Globalreport = globalreport;


    clearDateError();

    if (Globalreport === 'UserActivityReport') {
        if (!startDate && endDate) {
            showError("errorStart", !startDate ? "Select start date" : "Select current date only");
            $("#AllUsers").val("");
            return false;
        }

        if (startDate && !endDate) {
            showError("errorEnd", !endDate ? "Select end date" : "Select current date only");
            $("#AllUsers").val("");
            return false;
        }

        if (endDate < startDate) {
            showError("errorEnd", "End date must be later than start date");
            $("#AllUsers").val("");
            return false;
        }
    } else if (globalreport == "BulkImportReport" || globalreport == "AirGapReport" || globalreport == "DriftReport" || (Globalreport === "SnapReport" && values == "Custom")) {
        if (!startDate && endDateWorkflow) {
            showError("errorStart", !startDate ? "Select start date" : "Select current date only");
            $("#AllUsers").val("");
            return false;
        }
        if (startDate && !endDateWorkflow) {
            showError("errorEnd", !endDate ? "Select end date" : "Select current date only");
            $("#AllUsers").val("");
            return false;
        }
        return true;
    } else if (Globalreport === "DRDrillReport" || Globalreport === "RTOReport" || Globalreport === "DRReadinessLog") {
        if (Globalreport === "DRDrillReport" && values == "Custom") {
            if (!errorExecutionMode) showError("errorExecutionMode", "Select execution mode");
            if (!endDateWorkflow) showError("errorEndWF", "Select end date");
            if (errorExecutionMode && startDate && !endDateWorkflow) showError("errorEndWF", "Select end date");
            if (!startDate) showError("errorStart", "Select start date");
            if (!errorExecutionMode || !endDateWorkflow || !startDate) {
                if (!errorExecutionMode && !endDateWorkflow && !startDate) clearDateError();
                $("#workflowid").empty();
                return false;
            }
        }
        if (!startDate && endDateWorkflow) {
            showError("errorStart", !startDate ? "Select start date" : "Select current date only");
            $("#workflowid").val("");
            return false;
        }

        if (startDate && !endDateWorkflow) {
            showError("errorEndWF", !endDateWorkflow ? "Select end date" : "Select current date only");
            $("#workflowid").val("");
            return false;
        }

        if (endDateWorkflow < startDate) {
            showError("errorEndWF", "End date must be later than start date");
            $("#workflowid").val("");
            return false;
        }

        if ((!startDate && endDateWorkflow) || (startDate && !endDateWorkflow) || endDateWorkflow < startDate) {
            endDateWorkflow.value = '';
            endDate.value = '';
            $("#workflowid").empty();
        }
        return true;
    }
    else if (Globalreport === "RPOSLAReport" || Globalreport === "RPOSLADeviationReport") {
        var errorMessage;
        var Businessserviceid = BusinessServiceID.value;
        var rpoOption = $('#dateOption').val();
        if (rpoOption === "Monthly") {
            var rpoStartDate = $('#rpostartDate').find("input[type=month]").val();
            errorMessage = "Select month";
        }
        else if (rpoOption === "Weekly") {
            var rpoStartDate = $('#rpostartDate').find("input[type=week]").val();
            errorMessage = "Select week";
        }
        else {
            var rpoStartDate = $('#rpostartDate').find("input[type=date]").val();
            errorMessage = "Select date";
        }
        if (Globalreport === "RPOSLADeviationReport" && (triggeredElementId == "InfraDgnRPOSLADeviation" || triggeredElementId == "directCall" || triggeredElementId == "select2-InfraobjectDeviation-container")) {
            if (!Businessserviceid && !rpoOption && !rpoStartDate) {
                showError("errordateOption", "Select date format");
                showError("rpoerrorStart", errorMessage);
                showError("errorBusinessID", "Select operational service name");
                $("#Infraobject").val("");
                return false;
            }
            if (!RPOSLAclndrClick) {
                if (!Businessserviceid && rpoOption && !rpoStartDate) {
                    showError("rpoerrorStart", errorMessage);
                    showError("errorBusinessID", "Select operational service name");
                    $("#Infraobject").val("");
                    return false;
                }

                if (!Businessserviceid && rpoOption && rpoStartDate) {
                    showError("errorBusinessID", "Select operational service name");
                    $("#Infraobject").val("");
                    return false;
                }
            }

        }
        if (!rpoOption && rpoStartDate) {
            showError("errordateOption", "Select date format");
            $("#Infraobject").val("");
            return false;
        }

        if (rpoOption && !rpoStartDate) {
            showError("rpoerrorStart", errorMessage);
            $("#Infraobject").val("");
            return false;
        }

        if (!rpoOption && !rpoStartDate) {
            showError("errordateOption", "Select date format");
            showError("rpoerrorStart", errorMessage);
            $("#Infraobject").val("");
            return false;
        }
        return true;
    }
    else if (globalreport == "InfraObjectSchedulerLogReport" || globalreport == "CyberResiliencyScheduleLogReport") {
        if (!startDate && endDateWorkflow) {
            showError("errorStart", !startDate ? "Select start date" : "Select current date only");
            /*$("#workflowid").val("");*/
            return false;
        }
    }
}

function clearDateError() {
    $("#errorStart, #errorEnd,#errorBusinessID, #errorEndWF, #errorBLicense, #errorLicensepo, #errorLicensechildpo,#errordateOption,#rpoerrorStart,#errorExecutionMode").hide().text("");
}

function showError(elementId, errorMessage) {
    // document.getElementById(elementId).removeAttribute('style');
    $("#" + elementId).css("display", "").text(errorMessage);
}

$(".card.text-center").on('click', function () {
    $(".card.text-center").removeClass("Active-Card");
    $(this).addClass("Active-Card");
});
$('#reportDropdown').on('change', function () {
    var searchTerm = $(this).val().replaceAll(" ", "").toLowerCase();

    $(".list-group1 a").each(function () {
        var reportName = $(this).text().replaceAll(" ", "").toLowerCase();
        $(this).toggle(reportName.includes(searchTerm));

        if (searchTerm !== '' && reportName.includes(searchTerm)) {
            $(this).click();
            if ($(this)[0].outerHTML.includes('class="card text-center mb-0"')) {
                $(this).find('.card.text-center.mb-0').addClass('Active-Card');
            }
        }
        if (searchTerm === '') {
            ClearAndHide();
            $("#ReportsName").text("Prebuild Reports");
            $(".card.text-center.mb-0").removeClass("Active-Card");
            EmptyAlert.hidden = true;
            Loading.hidden = true;
            $('#btndrdrill').html('');
            ReportDefaultImage.hidden = false;
        }
    });
});

function loadAjax(loadReportUrl, reportName, response) {
    dataForDownload = "";
    dataForDownload = response;
    $.ajax({
        url: loadReportUrl,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            reportName: reportName,
            responseData: response
        }),
        
        success: function (data) {
            if (data != null) {
                click1 = 0; click2 = 0;
                ReportDefaultImage.hidden = true;
                EmptyAlert.hidden = true;
                $('#btndrdrill').html('');
                Loading.hidden = true;
                $('#btndrdrill').html(data);
                $('#btndrdrill').show();
                $('#Loaders').hide();
                $('.dxrd-report-preview-holder').show();
                $('.dxrd-tab-item').hide();
                enableAllLinks();
                RefreshButton();
                toggleReportScroll("auto");
                $('.dxrd-right-tabs').hide();
                hidepageprinticon();
            }
        },
        error: function (error) {
            ReportDefaultImage.hidden = true;
            console.error(error);
            $('#btndrdrill').hide();
            enableAllLinks();
            EmptyAlert.hidden = false;
            Loading.hidden = true;

        }
    });
};

function hidepageprinticon() {
    var printpageicon = document.querySelectorAll('.dxrd-toolbar-item.dxrd-toolbar-item-11');
    printpageicon.forEach(function (element) {
        element.style.display = 'none';
    });
    var editicon = document.querySelector('.dxrd-toolbar-item.dxrd-toolbar-item-9.dxrd-toolbar-item-with-separator.dxd-border-primary');
    if (editicon != null) { editicon.style.display = 'none'; }
};


function hideReportElements() {
    ReportDefaultImage.hidden = true;
    $('#btndrdrill').hide();
    var EmptyAlert = document.getElementById("EmptyAlert");
    EmptyAlert.hidden = false;
    var Loading = document.getElementById("Loader");
    Loading.hidden = true;
    enableAllLinks();
};


function disableAllLinks() {
    var links = document.querySelectorAll('.list-group1-item-action');
    $('#rptdropdown').addClass('disables');
    links.forEach(function (link) {
        link.setAttribute('disables', true);
        link.classList.add('disables');
        link.addEventListener('click', function (event) {
            event.preventDefault();
        });
    });
};
$(document).on('click', '.dxrd-image-print', function () {
    //$('.dx-overlay-content').hide();
    //$('.dx-menu-item-popout').hide();
    $('.dx-overlay-content').css('display', 'none');
});
$(document).on('click', '.dxrd-image-print-page', function () {
    $('.dx-overlay-content').hide();
});
function enableAllLinks() {
    $('#rptdropdown').removeClass('disables');
    var links = document.querySelectorAll('.list-group1-item-action');
    var element = document.querySelector('.dxrd-right-tabs');
    if (element) {
        element.style.width = '25px';
    }
    var element = document.querySelector('.dx-shadow-right');
    if (element) {
        element.style.width = '25px';
    }
    var searchimager = document.querySelector('.dxrd-image-padding.dxrd-image-search');
    if (searchimager) {
        searchimager.style.width = '25px';
    }
    links.forEach(function (link) {
        link.removeAttribute('disables');
        link.classList.remove('disables');
        link.removeEventListener('click', function (event) {
            event.preventDefault();
        });
    });
    if (element != null) {
        if (globalreport === "DataLagStatusReport") {
            document.querySelector('.dxrd-image-zoomin').click();
            document.querySelector('.dxrd-image-zoomin').click();
        }
        else {
            document.querySelector('.dxrd-image-zoomin').click();
            document.querySelector('.dxrd-image-zoomout').click();
        }
    }
    const pagecount = document.querySelectorAll('.dx-texteditor-input');
    if (pagecount) {
        pagecount.forEach((input) => {
            input.addEventListener('keydown', (e) => {
                e.preventDefault();
            });
        });
    }
};
   
//let click1 = 0;
let isCollapsed = false;
$(document).on('click', '.dxrd-image-preview-single-page, .dxrd-image-preview-multipage', function () { 
    var isSinglePage = $(this).hasClass('dxrd-image-preview-single-page');
    toggleReportScroll(isSinglePage ? "hidden" : "auto");
    var designerWrapper = document.querySelector('.dxrd-preview.dxrd-designer-wrapper');
    if (designerWrapper) {
        designerWrapper.style.height = isSinglePage ? "40%" : "100%";
    } 
});
function toggleReportScroll(overflowValue) {
    var ScrollContainer = document.querySelector(".Report-Scroll");
    if (ScrollContainer) {
        ScrollContainer.style.overflow = overflowValue;
    } else {
        console.error("Scroll container not found!");
    }
    const divElement = document.querySelector('.dxrd-tab-panel-wrapper');
    if (divElement.getAttribute('class').includes('dx-tab-panel-collapsed')) {
        $('.dxrd-designer-wrapper .dx-shadow.dxrd-tab-panel-right').css('right', 'auto');
    } else {
        $('.dxrd-designer-wrapper .dx-shadow.dxrd-tab-panel-right').css('right', '0');
    }
    reportsearch();
}
function reportsearch() {
    var reportsearch = document.querySelector('.dxrd-preview-wrapper');
    if (reportsearch) {
        reportsearch.style.right = '0px';
    }
}
$(document).on('click', '.dxrd-image-search', function () {
    if (click1 === 0) {      
        document.querySelector('.dxd-icon-highlighted.dxrd-image-search').click();       
        click1++;
    }
    if (click1 === 3) {
        document.querySelector('.dxrd-image-padding.dxrd-image-search').click();
    }
    reportsearch();
    var element = document.querySelector('.dxrd-right-tabs');
    if (element) {
        element.style.width = '25px';
    }
    var searchbar = document.querySelector('.dxrd-right-panel.dxd-property-grid-group-header-back-color.dxd-back-primary2.dxrd-tab-panel-right.ui-resizable');
    if (searchbar) {
        searchbar.style.right = '0px';
    }
    var shadowRight = document.querySelector('.dx-shadow-right');
    if (shadowRight) {
        shadowRight.style.width = '25px';
    }
    var searchimager = document.querySelector('.dxrd-image-padding.dxrd-image-search');
    if (searchimager) {
        searchimager.style.width = '25px';
    }
    if (click1 === 2) {
        clearSearch();
    }  
    var searchwrapper = document.querySelector(".dxrd-right-panel");
    if (searchwrapper) {
        searchwrapper.style.height = globalreport === "DataLagStatusReport" ? "910px" : "834px";
    }
    click1 = 2;
    const divElement = document.querySelector('.dxrd-tab-panel-wrapper');
    if (divElement.getAttribute('class').includes('dx-tab-panel-collapsed')) {
        $('.dxrd-designer-wrapper .dx-shadow.dxrd-tab-panel-right').css('right', 'auto');
    } else {
        $('.dxrd-designer-wrapper .dx-shadow.dxrd-tab-panel-right').css('right', '0');
    }   
});

function clearSearch() {
    var element = document.querySelector('.dx-shadow.dx-shadow-right.dxd-border-secondary.dxrd-tab-panel-wrapper.dxrd-tab-panel-right');

    if (element) {
        if (element.classList.contains('dx-tab-panel-collapsed')) {
            element.classList.remove('dx-tab-panel-collapsed');
            isCollapsed = false;
        } else {
            element.classList.add('dx-tab-panel-collapsed');
            isCollapsed = true;
        }
    }
}

document.addEventListener('fullscreenchange', () => {   
    const scrollContainer = document.querySelector(".Report-Scroll");
    const designerWrapper = document.querySelector('.dxrd-preview.dxrd-designer-wrapper');
    const singlePageElement = document.querySelector('.dxrd-image-preview-single-page');
    const icon = document.querySelector('.dxrd-image-fullscreen');
    if (icon) {
        icon.title = document.fullscreenElement ? 'Exit Full Screen' : 'Full Screen';
    }
    if (!document.fullscreenElement && singlePageElement && scrollContainer && designerWrapper) {
        scrollContainer.style.overflow = "hidden";
        designerWrapper.style.height = "40%";
    }
    reportsearch();
});
document.addEventListener('DOMContentLoaded', function () {
    document.addEventListener('mouseover', function (event) {
        if (event.target.classList.contains('dxrd-preview-export-menu-item')) {
            event.target.title = 'Export';
        }
    });
});
document.addEventListener('DOMContentLoaded', function () {
    var exportToolbarItems = document.querySelectorAll('.dxrd-preview-export-toolbar-item');
    exportToolbarItems.forEach(function (exportToolbarItem) {
        exportToolbarItem.setAttribute('aria-label', 'Export');
    });
});
$(document).on('click', '.dxrd-toolbar-item-image', function () {
    var targetElement = document.querySelector('.dxrd-toolbar-item-image.dxd-state-normal.dxd-icon-highlighted.dxrd-image-fullscreen.dxd-button-back-color.dxd-back-highlighted.dxd-state-active');
    var ScrollContainer = document.querySelector(".Report-Scroll");
    var designerWrapper = document.querySelector('.dxrd-preview.dxrd-designer-wrapper');
    document.querySelector('.dxrd-image-preview-multipage')?.setAttribute('title', 'Toggle Multipage Mode');
    document.querySelector('.dxrd-image-preview-single-page') && (ScrollContainer.style.overflow = "hidden", designerWrapper.style.height = "40%", document.querySelector('.dxrd-image-preview-single-page').setAttribute('title', 'Toggle Singlepage Mode'));
    if (targetElement) {
        designerWrapper.style.height = "100%";
        ScrollContainer.style.overflow = "auto";
    }
});
