﻿using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Events.Update;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionFieldMaster.Commands
{
    public class UpdateWorkflowActionFieldMasterTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IWorkflowActionFieldMasterRepository> _mockWorkflowActionFieldMasterRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly UpdateWorkflowActionFieldMasterCommandHandler _handler;

        public UpdateWorkflowActionFieldMasterTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockWorkflowActionFieldMasterRepository = new Mock<IWorkflowActionFieldMasterRepository>();
            _mockPublisher = new Mock<IPublisher>();
            _handler = new UpdateWorkflowActionFieldMasterCommandHandler(
                _mockMapper.Object,
                _mockWorkflowActionFieldMasterRepository.Object,
                _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnResponse_WhenWorkflowActionFieldMasterIsUpdatedSuccessfully()
        {
            var command = new UpdateWorkflowActionFieldMasterCommand
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Updated Action Field Master"
            };

            var existingEntity = new Domain.Entities.WorkflowActionFieldMaster
            {
                ReferenceId = command.Id,
                Name = "Test Action Field Master",
                IsActive = true
            };

            var updatedEntity = new Domain.Entities.WorkflowActionFieldMaster
            {
                ReferenceId = command.Id,
                Name = command.Name,
                IsActive = true
            };

            _mockWorkflowActionFieldMasterRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(existingEntity);

            _mockMapper
                .Setup(m => m.Map(command, existingEntity, typeof(UpdateWorkflowActionFieldMasterCommand), typeof(Domain.Entities.WorkflowActionFieldMaster)))
                .Callback(() => existingEntity.Name = command.Name);

            _mockWorkflowActionFieldMasterRepository
                .Setup(repo => repo.UpdateAsync(existingEntity))
                .ReturnsAsync(updatedEntity);

            var responseMessage = $" WorkflowActionFieldMaster '{"Updated Action Field Master"}' has been updated successfully";

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(responseMessage, result.Message);
            Assert.Equal(updatedEntity.ReferenceId, result.Id);

            _mockWorkflowActionFieldMasterRepository.Verify(repo => repo.UpdateAsync(existingEntity), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<WorkflowActionFieldMasterUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenWorkflowActionFieldMasterNotFound()
        {
            var command = new UpdateWorkflowActionFieldMasterCommand
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Updated Action Field Master"
            };

            _mockWorkflowActionFieldMasterRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync((Domain.Entities.WorkflowActionFieldMaster)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
        }
    }
}
