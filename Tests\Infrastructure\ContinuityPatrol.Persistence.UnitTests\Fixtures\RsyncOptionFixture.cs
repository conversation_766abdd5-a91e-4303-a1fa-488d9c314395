using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RsyncOptionFixture : IDisposable
{
    public List<RsyncOption> RsyncOptionPaginationList { get; set; }
    public List<RsyncOption> RsyncOptionList { get; set; }
    public RsyncOption RsyncOptionDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string Name = "Test Rsync Option";
    public const string ReplicationType = "Rsync";
    public const string UserId = "USER_123";

    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public RsyncOptionFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<RsyncOption>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Name, () => Name)
            .With(x => x.ReplicationType, () => ReplicationType)
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
        );

        RsyncOptionList = _fixture.CreateMany<RsyncOption>(5).ToList();
        RsyncOptionPaginationList = _fixture.CreateMany<RsyncOption>(20).ToList();
        RsyncOptionDto = _fixture.Create<RsyncOption>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public RsyncOption CreateRsyncOptionWithName(string name)
    {
        return _fixture.Build<RsyncOption>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Name, name)
            .With(x => x.ReplicationType, ReplicationType)
            .With(x => x.Properties, _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RsyncOption CreateRsyncOptionWithReplicationType(string replicationType)
    {
        return _fixture.Build<RsyncOption>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Name, Name)
            .With(x => x.ReplicationType, replicationType)
            .With(x => x.Properties, _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RsyncOption CreateRsyncOptionWithProperties(
        string name = null,
        string replicationType = null,
        string properties = null,
        bool isActive = true)
    {
        return _fixture.Build<RsyncOption>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Name, name ?? Name)
            .With(x => x.ReplicationType, replicationType ?? ReplicationType)
            .With(x => x.Properties, properties ?? _fixture.Create<string>())
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RsyncOption CreateRsyncOptionWithLongProperties(int propertyLength)
    {
        var longProperty = new string('A', propertyLength);
        return CreateRsyncOptionWithProperties(properties: longProperty);
    }

    public RsyncOption CreateRsyncOptionWithSpecialCharacters()
    {
        return CreateRsyncOptionWithProperties(
            name: "Option@#$% with Special Characters",
            replicationType: "Type_with_underscores",
            properties: "{\"special\":\"@#$%^&*()\",\"unicode\":\"αβγδε\"}"
        );
    }

    public List<RsyncOption> CreateMultipleRsyncOptionsWithSameName(string name, int count)
    {
        var options = new List<RsyncOption>();
        for (int i = 0; i < count; i++)
        {
            options.Add(CreateRsyncOptionWithName(name));
        }
        return options;
    }

    public List<RsyncOption> CreateRsyncOptionsForPagination(int totalCount)
    {
        var options = new List<RsyncOption>();
        for (int i = 1; i <= totalCount; i++)
        {
            options.Add(CreateRsyncOptionWithProperties(
                name: $"Option_{i:D3}",
                replicationType: i % 2 == 0 ? "Rsync" : "RoboCopy",
                properties: $"{{\"option{i}\":\"value{i}\"}}"
            ));
        }
        return options;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonNames = { "Standard Rsync", "Fast Rsync", "Secure Rsync", "Backup Rsync", "Mirror Rsync" };
        public static readonly string[] CommonReplicationTypes = { "Rsync", "RoboCopy", "DataSync", "FastCopy" };
        public static readonly string[] SpecialCharacterNames = { "Name@#$%", "Name with spaces", "Name_with_underscores", "Name-with-dashes" };
        public static readonly string[] CommonProperties = {
            "{\"archive\":true,\"verbose\":true}",
            "{\"compress\":true,\"delete\":true}",
            "{\"recursive\":true,\"preserve_permissions\":true}",
            "{\"dry_run\":false,\"stats\":true}",
            "{\"bandwidth_limit\":\"1000\",\"timeout\":\"300\"}"
        };
        
        public static readonly string ValidGuid = Guid.NewGuid().ToString();
        public static readonly string InvalidGuid = "INVALID_GUID";
        public static readonly string EmptyGuid = Guid.Empty.ToString();
        
        public static readonly Dictionary<string, string> ReplicationTypeProperties = new()
        {
            { "Rsync", "{\"archive\":true,\"verbose\":true,\"compress\":true}" },
            { "RoboCopy", "{\"mirror\":true,\"retry\":3,\"wait\":30}" },
            { "DataSync", "{\"sync_mode\":\"incremental\",\"verify\":true}" },
            { "FastCopy", "{\"buffer_size\":\"32MB\",\"verify\":true}" }
        };
    }
}
