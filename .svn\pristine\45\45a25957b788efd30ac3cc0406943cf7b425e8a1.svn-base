namespace ContinuityPatrol.Application.Features.FiaTemplate.Commands.Create;

public class CreateFiaTemplateCommand : IRequest<CreateFiaTemplateResponse>
{
    public string Name { get; set; }
    public string Description { get; set; }
   public string Properties { get; set; }
    public string UserName { get; set; }
    public string TemplateUsedBy { get; set; }
    public bool TemplateInUsed { get; set; }
}