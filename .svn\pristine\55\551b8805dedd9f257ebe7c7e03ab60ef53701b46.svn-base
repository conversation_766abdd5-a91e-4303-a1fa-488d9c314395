﻿using ContinuityPatrol.Application.Features.ComponentType.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;
using FluentValidation.TestHelper;

namespace ContinuityPatrol.Application.UnitTests.Features.ComponentType.Validators;

public class CreateComponentTypeValidatorTests
{
    public List<Domain.Entities.ComponentType> ComponentTypes { get; set; }

    private readonly CreateComponentTypeCommandValidator _validator;

    public CreateComponentTypeValidatorTests()
    {
        ComponentTypes = new Fixture().Create<List<Domain.Entities.ComponentType>>();

        Mock<IComponentTypeRepository> mockComponentTypeRepository = new();

        mockComponentTypeRepository
            .Setup(repo => repo.IsComponentTypeNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(false);

        _validator = new CreateComponentTypeCommandValidator(mockComponentTypeRepository.Object);

        ComponentTypeRepositoryMocks.CreateComponentTypeRepository(ComponentTypes);
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Type_InComponentType_With_Empty(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldHaveValidationErrorFor(x => x.ComponentName)
            .WithErrorMessage("Name is required");
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Type_InComponentType_With_IsNull(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = null;

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldHaveValidationErrorFor(x => x.ComponentName)
            .WithErrorMessage("'Name' must not be empty.");
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Type_InComponentType_With_MinimumRange(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "NS";
        createComponentTypeCommand.Properties = "{\"name\":\"MIMIX\",\"version\":\"\"}";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldNotHaveValidationErrorFor(x => x.Properties);
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Type_InComponentType_With_MaximumRange(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "ABCDEFGHIJKLMNOPQRSTUWXYZ_ZYXWUTSRQPONM";
        createComponentTypeCommand.Properties = "{\"name\":\"MIMIX\",\"version\":\"\"}";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldNotHaveValidationErrorFor(x => x.Properties);
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Valid_Type_InComponentType(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "  PTYS  ";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldHaveValidationErrorFor(x => x.ComponentName)
            .WithErrorMessage("Please Enter Valid Name");
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Valid_Type_InComponentType_With_DoubleSpace_InFront(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "  PTYS";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldHaveValidationErrorFor(x => x.ComponentName)
            .WithErrorMessage("Please Enter Valid Name");
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Valid_Type_InComponentType_With_DoubleSpace_InBack(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "PTYS  ";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldHaveValidationErrorFor(x => x.ComponentName)
            .WithErrorMessage("Please Enter Valid Name");
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Valid_Type_InComponentType_With_TripleSpace_InBetween(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "PTYS   Tech";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldHaveValidationErrorFor(x => x.ComponentName)
            .WithErrorMessage("Please Enter Valid Name");
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Valid_Type_InComponentType_With_SpecialCharacters_InFront(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "&^$#PTYS Tech";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldHaveValidationErrorFor(x => x.ComponentName)
            .WithErrorMessage("Please Enter Valid Name");
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Valid_Type_InComponentType_With_SpecialCharacters_InBetween(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "PTYS#@$#Tech";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldHaveValidationErrorFor(x => x.ComponentName)
            .WithErrorMessage("Please Enter Valid Name");
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Valid_Type_InComponentType_With_SpecialCharacters_Only(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "%$&^*^%%$$#";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldHaveValidationErrorFor(x => x.ComponentName)
            .WithErrorMessage("Please Enter Valid Name");
    }


    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Valid_Type_InComponentType_With_UnderScore_InFront(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "_PTS Techno";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldHaveValidationErrorFor(x => x.ComponentName)
            .WithErrorMessage("Please Enter Valid Name");
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Valid_Type_InComponentType_With_UnderScore_InFront_AndBack(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "_PTS Techno_";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldHaveValidationErrorFor(x => x.ComponentName)
            .WithErrorMessage("Please Enter Valid Name");
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Valid_Type_InComponentType_With_Numbers_InFront(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "254PTS Techno";
        createComponentTypeCommand.Properties = "{\"name\":\"MIMIX\",\"version\":\"\"}";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldNotHaveValidationErrorFor(x => x.Properties);
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Valid_Type_InComponentType_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "_254PTS Techno_";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldHaveValidationErrorFor(x => x.ComponentName)
            .WithErrorMessage("Please Enter Valid Name");
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Valid_Type_InComponentType_With_UnderScore_InFront_AndNumbers_InBack(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "_PTS Techno561";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldHaveValidationErrorFor(x => x.ComponentName)
            .WithErrorMessage("Please Enter Valid Name");
    }

    [Theory]
    [AutoComponentTypeData]
    public async Task Verify_Create_Valid_Type_InComponentType_With_Numbers_Only(CreateComponentTypeCommand createComponentTypeCommand)
    {
        createComponentTypeCommand.ComponentName = "86256456325";
        createComponentTypeCommand.Properties = "{\"name\":\"MIMIX\",\"version\":\"\"}";

        var result = await _validator.TestValidateAsync(createComponentTypeCommand);
        result.ShouldNotHaveValidationErrorFor(x => x.Properties);
    }
}
