﻿using ContinuityPatrol.Shared.Core.Extensions;

namespace ContinuityPatrol.Shared.Core.Helper;

public static class SecurityHelper
{
    public static string Decrypt(string cipherText)
    {
        if (cipherText.IsNullOrWhiteSpace() || cipherText is { Length: < 64 })
        {
            return "";
        }
        // if (cipherText == null) { return ""; } // Return empty string if null
        var splitPass = cipherText.Split("$");

        if (splitPass.Length < 2)
        {
            return cipherText;
        } // Return empty string if not in correct format

        var base64Key = splitPass[0];

        var base64Ciphertext = splitPass[1];

        // Convert from base64 to raw bytes spans
        var encryptedData = Convert.FromBase64String(base64Ciphertext).AsSpan();
        var key = Convert.FromBase64String(base64Key).AsSpan();

        var tagSizeBytes = 16; // 256-bit encryption / 8 bit = 32 bytes
        var ivSizeBytes = 12; // 12 bytes iv

        // Ciphertext size is the whole data - iv - tag
        var cipherSize = encryptedData.Length - tagSizeBytes - ivSizeBytes;

        // Extract iv (nonce) 12 bytes prefix
        var iv = encryptedData.Slice(0, ivSizeBytes);

        // Followed by the real ciphertext
        var cipherBytes = encryptedData.Slice(ivSizeBytes, cipherSize);

        // Followed by the tag (trailer)
        var tagStart = ivSizeBytes + cipherSize;
        var tag = encryptedData.Slice(tagStart, tagSizeBytes);

        // Now that we have all the parts, perform decryption
        var plainBytes = cipherSize < 1024
            ? stackalloc byte[cipherSize]
            : new byte[cipherSize];
        using var aes = new AesGcm(key, 16);
        aes.Decrypt(iv, cipherBytes, tag, plainBytes);
        return Encoding.UTF8.GetString(plainBytes);
    }

    public static string Encrypt(string text)
    {
        if (text.IsNullOrWhiteSpace())
        {
            throw new ArgumentException("Encryption failed: input text must not be null, empty, or whitespace.");
        }

        var bytes = Encoding.UTF8.GetBytes(text);

        var base64Key = GenerateKey();

        var key = Base64ToBuffer(base64Key);
        var iv = new byte[12]; // Generate your own random IV here (256 bits)

        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(iv);

        using var aes = new AesGcm(key, 16);
        var cipherData = new byte[bytes.Length];
        var tag = new byte[16]; // Change the tag size to 256 bits (32 bytes)

        aes.Encrypt(iv, bytes, cipherData, tag);

        var cipherText = ConcatenateBuffers(iv, ConcatenateBuffers(cipherData, tag));

        return base64Key + "$" + BufferToBase64(cipherText);
    }


    public static string GenerateKey()
    {
        using var aes = Aes.Create();
        aes.KeySize = 256;
        aes.GenerateKey();

        return BufferToBase64(aes.Key);
    }

    public static byte[] ConcatenateBuffers(byte[] buffer1, byte[] buffer2)
    {
        var tmp = new byte[buffer1.Length + buffer2.Length];
        Buffer.BlockCopy(buffer1, 0, tmp, 0, buffer1.Length);
        Buffer.BlockCopy(buffer2, 0, tmp, buffer1.Length, buffer2.Length);
        return tmp;
    }

    public static string BufferToBase64(byte[] buffer)
    {
        return Convert.ToBase64String(buffer);
    }

    public static byte[] Base64ToBuffer(string base64)
    {
        return Convert.FromBase64String(base64);
    }
}