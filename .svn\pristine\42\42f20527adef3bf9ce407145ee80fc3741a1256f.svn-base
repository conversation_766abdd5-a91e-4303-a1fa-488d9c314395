const mappingURL = {
    createOrUpdate: "Admin/FormMapping/CreateOrUpdate",
    delete: "Admin/FormMapping/Delete",
    getFormType: 'Admin/FormType/GetFormTypeNames',
    getNames: 'Admin/FormBuilder/GetNames',
    getPagination: "/Admin/FormMapping/GetPagination",
    nameExist: "Admin/FormMapping/FormMappingNameExist",
    typeListByName: "Admin/ComponentType/GetComponentTypeListByName"
};

let selectedValues = [];
let dataTable = "";
let btnDisableFormMapping = false;
let versions = "";
let formName = "Select os name";
let formMappingTypeID = "";
let nameicon = $("#iconChange");
let typeicon = $("#typeIcon");

$(function () {
    preventSpecialKeys('#search-inp'); //commonfunctions.js

    dataTable = $('#tblFormMapping').DataTable(
        {
            language: {
                decimal: ",",
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "Sortable": true,
            "order": [],
            fixedColumns: {
                left: 1,
                right: 1
            },
            "ajax": {
                "type": "GET",
                "url": mappingURL.getPagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "formName" : sortIndex === 2 ? "formTypeName" : sortIndex === 3 ? "name" :
                        sortIndex === 4 ? "version" : "";
                    let orderValue = d?.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        const { totalPages, totalCount, data } = json?.data;
                        json.recordsTotal = totalPages;
                        json.recordsFiltered = totalCount;
                        $(".pagination-column").toggleClass("disabled", data?.length === 0);
                        return data;
                    }
                    else {
                        errorNotification(json)
                    }
                },
            },
            "columnDefs": [
                {
                    "targets": [1, 2, 3, 4],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta?.row + 1;
                        }
                        return data;
                    }
                },
                {
                    "data": "formName", "name": "Form Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title='${data || 'NA'}'>${data || 'NA'} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "formTypeName", "name": "Form Type Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title='${data || 'NA'}'><i class='${row?.logo} me-1'></i> ${data || 'NA'} </span>`
                        }
                        return data;
                    }
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title='${data || 'NA'}'>${data || 'NA'} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "version", "name": "version", "autoWidth": true,
                    "render": function (data, type, row) {
                        let cleanedVersion = JSON?.parse(data);
                        let versiondata = cleanedVersion?.Version || 'NA';
                        if (versiondata?.length > 0 && Array?.isArray(versiondata)) {
                            versiondata = versiondata?.filter(function (item) {
                                return item?.trim() !== '';
                            }).join(', ');
                        }
                        else {
                            versiondata = 'NA';
                        }
                        if (type === 'display') {
                            return `<span title="${versiondata}">${versiondata}</span>`;
                        }
                        return versiondata;
                    }
                },
                {
                    "orderable": false,
                    "render": function (data, type, row) {
                        if (row?.isMapped) {
                            return `
                                    <div class="d-flex align-items-center gap-2">                                                                     
                                        <span role="button" title="Edit" class="form-delete-disable">
                                              <i class="cp-edit"></i>
                                         </span>                                     
                                         <span role="button" title="Delete" class="form-delete-disable">
                                              <i class="cp-Delete"></i>
                                         </span>                                                                                                                                 
                                   </div>`;
                        } else {
                            return `
                                    <div class="d-flex align-items-center gap-2">                                                                     
                                        <span role="button" title="Edit" class="edit-button" data-form='${JSON?.stringify(row)}'>
                                              <i class="cp-edit"></i>
                                         </span>                                     
                                         <span role="button" title="Delete" class="delete-button" data-form-id="${row?.id}" 
                                              data-form-name="${row?.formName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                              <i class="cp-Delete"></i>
                                         </span>                                                                                                                                 
                                   </div>`;
                        }
                    }
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart; // Get the start index of displayed data
                var counter = startIndex + index + 1; // Calculate the serial number based on start index and index
                $('td:eq(0)', row).html(counter); // Update the cell in the first column with the serial number
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },
        });

    $('#search-inp').on('input', commonDebounce(async function (e) {
        let sanitizedValue = $(this).val().replace(/^\s/, '').replace(/\s+/g, ' ').replace(/\s$/, ' ');

        if (sanitizedValue.trim() === "") {
            $(this).val("");
            sanitizedValue = "";
        } else {
            $(this).val(sanitizedValue);
        }
        const checkboxes = [
            { checkbox: $("#formName"), value: $("#formName").val() },
            { checkbox: $("#Name"), value: $("#Name").val() },
            { checkbox: $("#FormTypeName"), value: $("#FormTypeName").val() }
        ];
        checkboxes.forEach(({ checkbox, value }) => {

            if (checkbox.is(':checked')) {
                selectedValues.push(value + sanitizedValue);
            }
        });

        dataTable.ajax.reload(function (json) {
            let message = $('.dataTables_empty');

            if (sanitizedValue.length === 0 && json?.data?.data?.length === 0) {
                message.text('No Data Found');
            } else if (json?.recordsFiltered === 0) {
                message.text('No matching records found');
            }
        })
    }));

    $("#saveButton").on("click", async function () {
        let ver = $("#selectedVersion").val();
        $("#formMappingVersion").val(JSON.stringify({ "Version": ver }));
        let $formMappingName = $("#formMappingName :selected");
        let $formMappingOSName = $("#formMappingOSName :selected");
        let formMappingName = $formMappingName.val();
        let formMappingOSName = $formMappingOSName.text();
        let formMappingOSNameID = $formMappingOSName.val();
        let formMappingVersion = $("#selectedVersion").val();
        let formMappingType = $("#formMappingType").val();
        $("#formId").val($formMappingName.attr("formmappingformid"));
        $("#formTypeId").val(formMappingOSName);

        let OSValidation = await formMappingOSNameValidation(formMappingOSName, formName, "osNameError", formMappingOSNameID);
        let nameValidation = formMappingNameValidation(formMappingName, " Select form name", "formNameError");
        let typeValidation = formMappingTypeValidation(formMappingType, " Select form type", "typeError");
        let versionValidation;
        let ModifiedFormMappingType = formMappingType.toLowerCase().split(" ").join("");

        if (ModifiedFormMappingType === "server" || ModifiedFormMappingType === "database" || formMappingVersion.length > 0) {
            versionValidation = formMappingVersionValidation(formMappingVersion, " Select version", "versionError");
        }

        if (ModifiedFormMappingType === "server" || ModifiedFormMappingType === "database") {

            if (nameValidation && OSValidation && versionValidation && typeValidation) {

                if (!btnDisableFormMapping) {
                    btnDisableFormMapping = true;
                    formMappingSaveAndUpdate();
                }
            }
        } else {
            if (nameValidation && OSValidation) {

                if (!btnDisableFormMapping) {
                    btnDisableFormMapping = true;
                    formMappingSaveAndUpdate();
                }
            }
        }
    });

    $(".create-model-btn").on("click", function () {
        $('.SaveFunction').text('Save');
        $('#textFormMappingId').val('');
        formMappingTypeID = "create"
        formName = "Select OS type";
        $("#formMappingOSName").empty().append($("<option>").val('').text("Select OS Type"));
        $('.hideForReplication').hide();
        $('#typeName').html('OS Type');
        clearErrorMessages();
        nameicon.removeClass().addClass("cp-os-type");
        typeicon.removeClass().addClass("cp-form-name");
    });

    $('#tblFormMapping').on('click', '.delete-button', function () {
        $("#deleteData").text($(this).data("form-name"));
        $("#textDeleteId").val($(this).data("form-id"));
    });

    $("#confirmDeleteButton").on("click", async function () {
        const form = $('#DeleteFormMapping')[0];
        const formData = new FormData(form);

        if (!btnDisableFormMapping) {
            btnDisableFormMapping = true;
            let response = await deleteData(RootUrl + mappingURL.delete, formData); //commonfunctions.js
            $("#DeleteModal").modal("hide");
            btnDisableFormMapping = false;

            if (response?.success) {
                notificationAlert("success", response?.data?.message);
                setTimeout(() => {
                    dataTableDelete(dataTable);
                }, 2000)
            } else {
                errorNotification(response);
            }
        }
    });

    $('#tblFormMapping').on("click", '.edit-button', function () {
        $('.SaveFunction').text('Update');
        $("#selectedVersion").val("");
        clearErrorMessages();
        populateModelFields($(this).data("form"));
        $('#CreateModal').modal('show');
    });

    $("#formMappingName").on("change", function () {
        formMappingNameValidation($(this).val(), " Select form name", "formNameError");
    });

    getFormNamesAndTypes();
});
