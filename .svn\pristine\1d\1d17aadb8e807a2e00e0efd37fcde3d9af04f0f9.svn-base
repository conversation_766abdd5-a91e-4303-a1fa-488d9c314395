﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.CommonBaseLicenseUpdate;

public class CommonBaseLicenseUpdateCommand
{
    public string Id { get; set; }

    [JsonIgnore] public string PONumber { get; set; }

    [JsonIgnore] public string CompanyId { get; set; }

    [JsonIgnore] public string CompanyName { get; set; }

    [JsonIgnore] public string CPHostName { get; set; }

    [JsonIgnore] public string IPAddress { get; set; }

    [JsonIgnore] public string MACAddress { get; set; }

    [JsonIgnore] public string Validity { get; set; }

    [JsonIgnore] public string ExpiryDate { get; set; }

    [JsonIgnore] public bool IsParent { get; set; }

    [JsonIgnore] public string ParentId { get; set; }

    [JsonIgnore] public string ParentPONumber { get; set; }
    [JsonIgnore] public bool IsState { get; set; }
    public string Properties { get; set; }
    public string LicenseKey { get; set; }
}