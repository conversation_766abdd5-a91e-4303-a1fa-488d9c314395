using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class HacmpClusterFixture : IDisposable
{
    public List<HacmpCluster> HacmpClusterPaginationList { get; set; }
    public List<HacmpCluster> HacmpClusterList { get; set; }
    public HacmpCluster HacmpClusterDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public HacmpClusterFixture()
    {
        var fixture = new Fixture();

        HacmpClusterList = fixture.Create<List<HacmpCluster>>();

        HacmpClusterPaginationList = fixture.CreateMany<HacmpCluster>(20).ToList();


        HacmpClusterDto = fixture.Create<HacmpCluster>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
