﻿using ContinuityPatrol.Application.Features.DataSet.Commands.Create;
using ContinuityPatrol.Application.Features.DataSet.Commands.Update;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetDataSetById;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetRunQuery;
using ContinuityPatrol.Domain.ViewModels.DataSetModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IDataSetService
{
    Task<List<DataSetListVm>> GetDataSetList();
    Task<DataSetDetailVm> GetDataSetById(string id);
    Task<BaseResponse> CreateAsync(CreateDataSetCommand createDataSetCommand);
    Task<BaseResponse> UpdateAsync(UpdateDataSetCommand updateDataSetCommand);
    Task<BaseResponse> DeleteAsync(string dataSetId);
    Task<PaginatedResult<DataSetListVm>> GetDataSetPaginatedList(GetDataSetPaginatedListQuery query);
    Task<DataSetRunQueryVm> RunQuery(string query);
    Task<bool> IsDataSetNameExist(string name, string id);
    Task<GetDataSetByIdVm> GetRunQueryById(string id);
}