﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class InfraSummaryRepository : BaseRepository<InfraSummary>, IInfraSummaryRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public InfraSummaryRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext,loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<InfraSummary>>ListAllAsync()
    {
        var list = await base.ListAllAsync();
        var businessServiceBasedList = list.Where(businessService =>
            businessService.CompanyId.Equals(_loggedInUserService.CompanyId));
        var services = _loggedInUserService.IsAllInfra 
            ? businessServiceBasedList.ToList()
            : businessServiceBasedList
    .Where(businessService =>
        AssignedEntity.AssignedBusinessServices.Count > 0 &&
         AssignedEntity.AssignedBusinessServices
            .Any(assignedBusinessService =>
                businessService?.BusinessServiceId == assignedBusinessService.Id)).ToList();

        return services;

        //var list = _loggedInUserService.IsParent
        //    ? await base.ListAllAsync()
        //    : await FindByFilterAsync(company => company.CompanyId.Equals(_loggedInUserService.CompanyId));

        //return services;

    }



    public async Task<InfraSummary> GetInfraSummaryByType(string type)
    {
        var matches = await _dbContext.InfraSummaries.Where(x => x.Type == type).FirstOrDefaultAsync();
        return matches;
    }

    //public async Task<InfraSummary> GetInfraSummaryByTypeAndCompanyId(string type)
    //{
    //    var match = await _dbContext.InfraSummaries.Where(x => x.Type == type && x.CompanyId == _loggedInUserService.CompanyId).FirstOrDefaultAsync();
    //    return match;
    //}

    public async Task<InfraSummary> GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(string type, string businessServiceId,string companyId)
    {
        var match= await _dbContext.InfraSummaries.Where(x => x.Type == type && x.CompanyId ==companyId && x.BusinessServiceId == businessServiceId).FirstOrDefaultAsync();
        return match;
    }
}