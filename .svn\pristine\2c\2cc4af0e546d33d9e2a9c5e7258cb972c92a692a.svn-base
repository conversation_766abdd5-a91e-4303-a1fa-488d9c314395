﻿@model ContinuityPatrol.Domain.ViewModels.RsyncOptionModel.RsyncOptionViewModel;
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-rsync-options"></i><span>R-Sync Options</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="name=" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm createButton" id="rsync-createbutton" data-bs-toggle="modal" data-bs-target="#RSyncCreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="pt-1 card-body" style="height: calc(100vh - 141px);">
            <div>
                <table id="rSyncDatatablelist" class="datatable table table-hover dataTable no-footer" style="width:100%">
                    <thead>
                        <tr>
                            <th class="SrNo_th">Sr. No.</th>
                            <th>Name</th>
                            <th>General Options</th>
                            <th>Advanced Options</th>
                            <th>Custom Command</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div id="configurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>

<div id="configurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>

<!--Modal Create-->
<div class="modal fade" data-bs-backdrop="static" id="RSyncCreateModal" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-rsync-options"></i><span>R-Sync Options Configuration</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createFormRSync" class="mb-2 example-form">
                    @* asp-controller="RSyncOptions" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data" *@
                    <input asp-for="Id" type="hidden" id="rsyncID" />
                    <input asp-for="Properties" type="hidden" id="rsyncProperties" />
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <div class="form-label">Name</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-name"></i></span>
                                    <input asp-for="Name" type="text" maxlength="100" class="form-control" id="rsyncOptionName"
                                           placeholder="Enter R-Sync Options Name" autocomplete="off" />
                                </div>
                                <span asp-validation-for="Name" id="nameError"></span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <div class="form-label">Replication Type</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-replication-type"></i></span>
                                    <select asp-for="ReplicationType" class="form-select-modal" data-live-search="true"
                                            onchange="validateReplicationType()" id="replicationType" data-placeholder="Select Replication Type">
                                        <option value=""></option>
                                        <option value="Application">Application</option>
                                        <option value="Database">Database</option>
                                    </select>
                                </div>
                                <span asp-validation-for="ReplicationType" id="replicationTypeError"></span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="gap-2 d-flex ">
                            <div class="form-label me-3">Options Selection</div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" onchange="optionsSelectionChange()" name="inlineRadioOptions" id="inlineRadio1" value="userSelection">
                                <label class="form-check-label" for="inlineRadio1">User Selection</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" onchange="optionsSelectionChange()" name="inlineRadioOptions" id="inlineRadio2" value="custom">
                                <label class="form-check-label" for="inlineRadio2">Custom Command</label>
                            </div>
                        </div>
                        <span asp-validation-for="Properties" id="optionSelectionError"></span>
                    </div>
                    <div id="showHideGeneralAdvancedOptions">
                        <div class="form-group p-3 border border-1 border-secondary-subtle">
                            <div class="form-label mb-2 border-bottom">General Options</div>
                            <div>
                                <div class="row">
                                    <div class="col-3">
                                        <div class="form-check">
                                            <input class="form-check-input me-1" onchange="generalOptionsChange()" type="checkbox" value="v" id="verbosityCheckbox">
                                            <label class="form-check-label" for="verbosityCheckbox">-v(verbosity)</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input me-1" onchange="generalOptionsChange()" type="checkbox" value="q" id="quietCheckbox">
                                            <label class="form-check-label" for="quietCheckbox">-q(quiet)</label>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-check">
                                            <input class="form-check-input me-1" onchange="generalOptionsChange()" type="checkbox" value="c" id="checksumCheckbox">
                                            <label class="form-check-label" for="checksumCheckbox">-c(checksum)</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input me-1" onchange="generalOptionsChange()" type="checkbox" value="a" id="archiveCheckbox">
                                            <label class="form-check-label" for="archiveCheckbox">-a(archive)</label>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="form-check">
                                            <input class="form-check-input me-1" onchange="generalOptionsChange()" type="checkbox" value="z" id="compressionCheckbox">
                                            <label class="form-check-label" for="compressionCheckbox">-z(compression)</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input me-1" onchange="generalOptionsChange()" type="checkbox" value="r" id="recursiveCheckbox">
                                            <label class="form-check-label" for="recursiveCheckbox">-r(recursive)</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <span asp-validation-for="Properties" id="generalOptionsError" style="width:97%"></span>
                        </div>
                        <div class="form-group  p-3 border border-1 border-secondary-subtle">
                            <div class="form-label mb-2 border-bottom">Advanced Options</div>
                            <div class="row">
                                <div class="col-3">
                                    <div class="form-check">
                                        <input class="form-check-input me-1" type="checkbox" value="--progress" id="progressCheckbox">
                                        <label class="form-check-label" for="progressCheckbox">--progress</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input me-1" type="checkbox" value="--copy-links" id="copyLinksCheckbox">
                                        <label class="form-check-label" for="copyLinksCheckbox">--copy-links</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input me-1" type="checkbox" value="--keep-dirlinks" id="keepDirlinksCheckbox">
                                        <label class="form-check-label" for="keepDirlinksCheckbox">--keep-dirlinks</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input me-1" type="checkbox" value="--delete" id="deleteCheckbox">
                                        <label class="form-check-label" for="deleteCheckbox">--delete</label>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="form-check">
                                        <input class="form-check-input me-1" type="checkbox" value="--links" id="linksCheckbox">
                                        <label class="form-check-label" for="linksCheckbox">--links</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input me-1" type="checkbox" value="--copy-dirlinks" id="copyDirlinksCheckbox">
                                        <label class="form-check-label" for="copyDirlinksCheckbox">--copy-dirlinks</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input me-1" type="checkbox" value="--hard-links" id="hardLinksCheckbox">
                                        <label class="form-check-label" for="hardLinksCheckbox">--hard-links</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input me-1" checked disabled type="checkbox" value="--stats" id="statsCheckbox">
                                        <label class="form-check-label" for="statsCheckbox">--stats</label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <table class="table table-sm table-borderless mb-0">
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <div class="mt-2">
                                                        <input class="form-check-input me-1" onchange="enableTextBox('#logFileCheckbox','#logFileTextBox','#logFileTextBoxError')" type="checkbox" value="--log-file" id="logFileCheckbox" autocomplete="off">
                                                        <label class="form-check-label" for="logFileCheckbox">--log-file</label>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="form-group">
                                                        <input type="text" autocomplete="off" id="logFileTextBox" oninput="textBoxChange('#logFileTextBoxError')" disabled class="form-control border form-control-sm" placeholder="Enter Log-File" />
                                                        <span asp-validation-for="Properties" id="logFileTextBoxError"></span>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="mt-3">
                                                        <input class="form-check-input me-1" onchange="enableTextBox('#excludeCheckbox','#excludeTextBox','#excludeTextBoxError')" type="checkbox" value="--exclude" id="excludeCheckbox" autocomplete="off">
                                                        <label class="form-check-label" for="excludeCheckbox">--exclude</label>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="form-group">
                                                        <input type="text" autocomplete="off" id="excludeTextBox" oninput="textBoxChange('#excludeTextBoxError')" disabled class="mt-2 form-control border form-control-sm" placeholder="Enter Exclude" />
                                                        <span asp-validation-for="Properties" id="excludeTextBoxError"></span>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="mt-3">
                                                        <input class="form-check-input me-1" onchange="enableTextBox('#excludeFromCheckbox','#excludeFromTextBox','#excludeFromTextBoxError')" type="checkbox" value="--exclude-from" id="excludeFromCheckbox" autocomplete="off">
                                                        <label class="form-check-label" for="excludeFromCheckbox">--exclude-from</label>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="form-group">
                                                        <input type="text" autocomplete="off" id="excludeFromTextBox" oninput="textBoxChange('#excludeFromTextBoxError')" disabled class="mt-2 form-control border form-control-sm" placeholder="Enter Exclude-From" />
                                                        <span asp-validation-for="Properties" id="excludeFromTextBoxError"></span>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="mt-3">
                                                        <input class="form-check-input me-1" onchange="enableTextBox('#portCheckbox','#portTextBox','#portTextBoxError')" type="checkbox" value="--port" id="portCheckbox" autocomplete="off">
                                                        <label class="form-check-label" for="portCheckbox">--port</label>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="form-group">
                                                        <input type="number" autocomplete="off" maxlength="5" id="portTextBox" oninput="textBoxChange('#portTextBoxError')" disabled class="mt-2 form-control border form-control-sm" placeholder="Enter Port" />
                                                        <span asp-validation-for="Properties" id="portTextBoxError"></span>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="mt-3">
                                                        <input class="form-check-input me-1" onchange="enableTextBox('#includeCheckbox','#includeTextBox','#includeTextBoxError')" type="checkbox" value="--include" id="includeCheckbox" autocomplete="off">
                                                        <label class="form-check-label" for="includeCheckbox">--include</label>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="form-group">
                                                        <input type="text" autocomplete="off" id="includeTextBox" oninput="textBoxChange('#includeTextBoxError')" disabled class="mt-2 form-control border form-control-sm" placeholder="Enter Include" />
                                                        <span asp-validation-for="Properties" id="includeTextBoxError"></span>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="mt-3">
                                                        <input class="form-check-input me-1" onchange="enableTextBox('#includeFromCheckbox','#includeFromTextBox','#includeFromTextBoxError')" type="checkbox" value="--include-from" id="includeFromCheckbox" autocomplete="off">
                                                        <label class="form-check-label" for="includeFromCheckbox">--include-from</label>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="form-group">
                                                        <input type="text" autocomplete="off" id="includeFromTextBox" oninput="textBoxChange('#includeFromTextBoxError')" disabled class="mt-2 form-control border form-control-sm" placeholder="Enter Include-From" />
                                                        <span asp-validation-for="Properties" id="includeFromTextBoxError"></span>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="mt-3">
                                                        <input class="form-check-input me-1" onchange="enableTextBox('#additionalCheckbox','#additionalTextBox','#additionalTextBoxError')" type="checkbox" value="additional" id="additionalCheckbox" autocomplete="off">
                                                        <label class="form-check-label" for="additionalCheckbox">Additional</label>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="form-group">
                                                        <input type="text" autocomplete="off" id="additionalTextBox" oninput="textBoxChange('#additionalTextBoxError')" disabled class="mt-2 form-control border form-control-sm" placeholder="Enter Additional" />
                                                        <span asp-validation-for="Properties" id="additionalTextBoxError"></span>
                                                    </span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <span asp-validation-for="Properties" id="advancedOptionsError" style="width:97%"></span>
                        </div>
                    </div>
                    <div id="showHideCustom">
                        <div class="form-group">
                            <div class="form-label">Custom Command</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-name"></i></span>
                                <textarea class="form-control" oninput="customFieldChange()" id="customFieldTextarea" rows="1" placeholder="Enter Custom Command"></textarea>
                            </div>
                            <span asp-validation-for="Properties" id="customCommandError"></span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm cancelRSyncData" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm saveRSyncData">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="configurationrsyncCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
</div>

<div id="configurationrsyncDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
</div>

<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <form id="deleteRsync">
                @* asp-controller="RSyncOptions" asp-action="Delete" asp-route-id="textDeleteId" enctype="multipart/form-data" *@
                <div class="modal-header p-0">
                    <img class="delete-img" src="~/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body text-center pt-0">
                    <h5 class="fw-bold">Are you sure?</h5>
                    <p class="d-flex align-items-center justify-content-center gap-1">You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="deleteData"></span> data?</p>
                    <input asp-for="Id" type="hidden" id="textDeleteId" name="id" class="form-control" />
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary btn-sm" id="confirmDeleteButton">Yes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<script src="~/js/configuration/infra components/commonfunctions.js"></script>
<script src="~/js/configuration/infra components/form builder/formbuildercommonfunctions.js"></script>
<script src="~/js/configuration/infra components/rsync options/rsyncfunctions.js"></script>
<script src="~/js/configuration/infra components/rsync options/rsyncoptions.js"></script>
