﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class ApprovalMatrixRepositoryMocks
{
    public static Mock<IApprovalMatrixRepository> CreateApprovalMatrixRepository(List<ApprovalMatrix> approvalMatrices)
    {
        var approvalMatrixRepository = new Mock<IApprovalMatrixRepository>();
        approvalMatrixRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(approvalMatrices);
        approvalMatrixRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => approvalMatrices.SingleOrDefault(x => x.ReferenceId == i));
        approvalMatrixRepository.Setup(repo => repo.AddAsync(It.IsAny<ApprovalMatrix>())).ReturnsAsync(
            (ApprovalMatrix approvalMatrix) =>
            {
                approvalMatrix.Id = new Fixture().Create<int>();
                approvalMatrix.ReferenceId = new Fixture().Create<Guid>().ToString();
                approvalMatrices.Add(approvalMatrix);
                return approvalMatrix;

            });

        return approvalMatrixRepository;
    }

    public static Mock<IApprovalMatrixRepository> UpdateApprovalMatrixRepository(List<ApprovalMatrix> approvalMatrices)
    {
        var approvalMatrixRepository = new Mock<IApprovalMatrixRepository>();

        approvalMatrixRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(approvalMatrices);

        approvalMatrixRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => approvalMatrices.SingleOrDefault(x => x.ReferenceId == i));

        approvalMatrixRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ApprovalMatrix>())).ReturnsAsync((ApprovalMatrix approvalMatrix) =>
        {
            var index = approvalMatrices.FindIndex(item => item.ReferenceId == approvalMatrix.ReferenceId);
            approvalMatrices[index] = approvalMatrix;
            return approvalMatrix;
        });

        return approvalMatrixRepository;
    }

    public static Mock<IApprovalMatrixRepository> DeleteApprovalMatrixRepository(List<ApprovalMatrix> approvalMatrices)
    {
        var approvalMatrixRepository = new Mock<IApprovalMatrixRepository>();
        approvalMatrixRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(approvalMatrices);

        approvalMatrixRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => approvalMatrices.SingleOrDefault(x => x.ReferenceId == i));

        approvalMatrixRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ApprovalMatrix>())).ReturnsAsync((ApprovalMatrix approvalMatrix) =>
        {
            var index = approvalMatrices.FindIndex(item => item.ReferenceId == approvalMatrix.ReferenceId);
            approvalMatrix.IsActive = false;
            approvalMatrices[index] = approvalMatrix;

            return approvalMatrix;
        });

        return approvalMatrixRepository;
    }

    public static Mock<IApprovalMatrixRepository> GetApprovalMatrixRepository(List<ApprovalMatrix> approvalMatrices)
    {
        var approvalMatrixRepository = new Mock<IApprovalMatrixRepository>();

        approvalMatrixRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(approvalMatrices);

        approvalMatrixRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => approvalMatrices.SingleOrDefault(x => x.ReferenceId == i));

        return approvalMatrixRepository;
    }

    public static Mock<IApprovalMatrixRepository> GetApprovalMatrixEmptyRepository()
    {
        var approvalMatrixRepository = new Mock<IApprovalMatrixRepository>();

        approvalMatrixRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<ApprovalMatrix>());

        return approvalMatrixRepository;
    }

    public static Mock<IApprovalMatrixRepository> GetPaginatedApprovalMatrixRepository(List<ApprovalMatrix> approvalMatrices)
    {
        var approvalMatrixRepository = new Mock<IApprovalMatrixRepository>();

        //var queryableApprovalMatrix = approvalMatrices.BuildMock();

        //approvalMatrixRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableApprovalMatrix);

                approvalMatrixRepository.Setup(repo => repo.PaginatedListAllAsync(
          It.IsAny<int>(),
          It.IsAny<int>(),
          It.IsAny<Specification<ApprovalMatrix>>(),
          It.IsAny<string>(),
          It.IsAny<string>()))
        .ReturnsAsync((int pageNumber, int pageSize, Specification<ApprovalMatrix> spec, string sortColumn, string sortOrder) =>
        {
          var sortedApprovalmatrix = approvalMatrices.AsQueryable();

          if (spec.Criteria != null)
          {
                sortedApprovalmatrix = sortedApprovalmatrix.Where(spec.Criteria);
          }

          if (!string.IsNullOrWhiteSpace(sortColumn))
          {
                sortedApprovalmatrix = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                  ? sortedApprovalmatrix.OrderByDescending(c => c.Name)
                  : sortedApprovalmatrix.OrderBy(c => c.Name);
          }

          var totalCount = sortedApprovalmatrix.Count();
          var paginated = sortedApprovalmatrix
              .Skip((pageNumber - 1) * pageSize)
              .Take(pageSize)
              .ToList();

          return PaginatedResult<ApprovalMatrix>.Success(paginated, totalCount, pageNumber, pageSize);
        });

        return approvalMatrixRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateApprovalMatrixEventRepository(List<UserActivity> userActivities)
    {
        var approvalMatrixEventRepository = new Mock<IUserActivityRepository>();

        approvalMatrixEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync((UserActivity userActivity) =>
        {
            userActivity.LoginName = new Fixture().Create<string>();

            userActivities.Add(userActivity);

            return userActivity;
        });

        return approvalMatrixEventRepository;
    }
}