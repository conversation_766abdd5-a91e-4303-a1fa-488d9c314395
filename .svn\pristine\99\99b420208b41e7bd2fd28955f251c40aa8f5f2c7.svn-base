using AutoFixture;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapStatusModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class CyberAirGapStatusFixture
{
    public CreateCyberAirGapStatusCommand CreateCyberAirGapStatusCommand { get; }
    public UpdateCyberAirGapStatusCommand UpdateCyberAirGapStatusCommand { get; }
    public DeleteCyberAirGapStatusCommand DeleteCyberAirGapStatusCommand { get; }
    public UpdateAirGapStatusCommand UpdateAirGapStatusCommand { get; }
    public CyberAirGapStatusListVm CyberAirGapStatusListVm { get; }
    public CyberAirGapStatusDetailVm CyberAirGapStatusDetailVm { get; }

    public CyberAirGapStatusFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateCyberAirGapStatusCommand>(c => c
            .With(b => b.AirGapId, Guid.NewGuid().ToString)
            .With(b => b.AirGapName, "Enterprise Air Gap Status Monitor")
            .With(b => b.Description, "Real-time status monitoring for air gap replication")
            .With(b => b.SourceSiteId, Guid.NewGuid().ToString)
            .With(b => b.SourceSiteName, "Primary Data Center")
            .With(b => b.TargetSiteId, Guid.NewGuid().ToString)
            .With(b => b.TargetSiteName, "DR Site")
            .With(b => b.Port, 8443)
            .With(b => b.Source, "{\"statusMonitor\":{\"enabled\":true,\"interval\":\"30s\",\"healthCheck\":\"active\",\"lastUpdate\":\"2024-01-15T10:30:00Z\"}}")
            .With(b => b.Target, "{\"statusMonitor\":{\"enabled\":true,\"interval\":\"30s\",\"healthCheck\":\"active\",\"lastUpdate\":\"2024-01-15T10:30:05Z\"}}")
            .With(b => b.SourceComponentId, Guid.NewGuid().ToString)
            .With(b => b.SourceComponentName, "Primary Status Monitor")
            .With(b => b.TargetComponentId, Guid.NewGuid().ToString)
            .With(b => b.TargetComponentName, "DR Status Monitor")
            .With(b => b.WorkflowId, Guid.NewGuid().ToString)
            .With(b => b.WorkflowStatus, "Active")
            .With(b => b.StartTime, DateTime.Now.AddHours(-1))
            .With(b => b.EndTime, DateTime.Now.AddHours(23))
            .With(b => b.RPO, "1 minute")
            .With(b => b.Status, "Healthy")
            .With(b => b.IsFileTransfered, true));

        fixture.Customize<UpdateCyberAirGapStatusCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.AirGapId, Guid.NewGuid().ToString)
            .With(b => b.AirGapName, "Updated Enterprise Air Gap Status Monitor")
            .With(b => b.Description, "Updated real-time status monitoring for air gap replication")
            .With(b => b.SourceSiteId, Guid.NewGuid().ToString)
            .With(b => b.SourceSiteName, "Updated Primary Data Center")
            .With(b => b.TargetSiteId, Guid.NewGuid().ToString)
            .With(b => b.TargetSiteName, "Updated DR Site")
            .With(b => b.Port, 9443)
            .With(b => b.Source, "{\"statusMonitor\":{\"enabled\":true,\"interval\":\"15s\",\"healthCheck\":\"active\",\"lastUpdate\":\"2024-01-15T11:30:00Z\"}}")
            .With(b => b.Target, "{\"statusMonitor\":{\"enabled\":true,\"interval\":\"15s\",\"healthCheck\":\"active\",\"lastUpdate\":\"2024-01-15T11:30:05Z\"}}")
            .With(b => b.SourceComponentId, Guid.NewGuid().ToString)
            .With(b => b.SourceComponentName, "Updated Primary Status Monitor")
            .With(b => b.TargetComponentId, Guid.NewGuid().ToString)
            .With(b => b.TargetComponentName, "Updated DR Status Monitor")
            .With(b => b.WorkflowId, Guid.NewGuid().ToString)
            .With(b => b.WorkflowStatus, "Updated")
            .With(b => b.StartTime, DateTime.Now.AddHours(-2))
            .With(b => b.EndTime, DateTime.Now.AddHours(22))
            .With(b => b.RPO, "30 seconds")
            .With(b => b.Status, "Warning")
            .With(b => b.IsFileTransfered, false));

        fixture.Customize<DeleteCyberAirGapStatusCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString));

        fixture.Customize<UpdateAirGapStatusCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Status, "Maintenance"));

        fixture.Customize<CyberAirGapStatusListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.AirGapId, Guid.NewGuid().ToString)
            .With(b => b.AirGapName, () => $"AirGapStatus-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.Description, () => $"Status monitor - {fixture.Create<string>().Substring(0, 10)}")
            .With(b => b.SourceSiteId, Guid.NewGuid().ToString)
            .With(b => b.SourceSiteName, () => $"Source-Site-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.TargetSiteId, Guid.NewGuid().ToString)
            .With(b => b.TargetSiteName, () => $"Target-Site-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.Port, () => fixture.Create<int>() % 9000 + 1000)
            .With(b => b.Source, () => $"{{\"status\":\"monitoring\",\"id\":\"{fixture.Create<string>().Substring(0, 8)}\"}}")
            .With(b => b.Target, () => $"{{\"status\":\"monitoring\",\"id\":\"{fixture.Create<string>().Substring(0, 8)}\"}}")
            .With(b => b.SourceComponentId, Guid.NewGuid().ToString)
            .With(b => b.SourceComponentName, () => $"Source-Monitor-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.TargetComponentId, Guid.NewGuid().ToString)
            .With(b => b.TargetComponentName, () => $"Target-Monitor-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.EnableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.DisableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.ErrorMessage, () => fixture.Create<bool>() ? null : $"Error-{fixture.Create<string>().Substring(0, 10)}")
            .With(b => b.WorkflowStatus, () => fixture.Create<bool>() ? "Active" : "Inactive")
            .With(b => b.StartTime, () => DateTime.Now.AddMinutes(-(fixture.Create<int>() % 120)))
            .With(b => b.EndTime, () => DateTime.Now.AddMinutes(fixture.Create<int>() % 120))
            .With(b => b.RPO, () => $"{fixture.Create<int>() % 60 + 1} seconds")
            .With(b => b.Status, () => fixture.Create<bool>() ? "Healthy" : "Warning")
            .With(b => b.IsFileTransfered, () => fixture.Create<bool>()));

        fixture.Customize<CyberAirGapStatusDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.AirGapId, Guid.NewGuid().ToString)
            .With(b => b.AirGapName, "Enterprise Air Gap Status Detailed Monitor")
            .With(b => b.SourceSiteId, Guid.NewGuid().ToString)
            .With(b => b.SourceSiteName, "Production Data Center")
            .With(b => b.TargetSiteId, Guid.NewGuid().ToString)
            .With(b => b.TargetSiteName, "Disaster Recovery Center")
            .With(b => b.Port, 8443)
            .With(b => b.Description, "Comprehensive status monitoring and health checking for enterprise air gap replication system")
            .With(b => b.Source, "{\"statusDetails\":{\"health\":\"HEALTHY\",\"lastHeartbeat\":\"2024-01-15T14:30:00Z\",\"metrics\":{\"cpu\":\"15%\",\"memory\":\"45%\",\"disk\":\"60%\",\"network\":\"10Mbps\"},\"services\":{\"replicationAgent\":\"RUNNING\",\"monitoringService\":\"RUNNING\",\"alertingService\":\"RUNNING\"},\"alerts\":[],\"uptime\":\"99.99%\"}}")
            .With(b => b.Target, "{\"statusDetails\":{\"health\":\"HEALTHY\",\"lastHeartbeat\":\"2024-01-15T14:30:05Z\",\"metrics\":{\"cpu\":\"12%\",\"memory\":\"40%\",\"disk\":\"55%\",\"network\":\"10Mbps\"},\"services\":{\"replicationReceiver\":\"RUNNING\",\"monitoringService\":\"RUNNING\",\"alertingService\":\"RUNNING\"},\"alerts\":[],\"uptime\":\"99.98%\"}}")
            .With(b => b.SourceComponentId, Guid.NewGuid().ToString)
            .With(b => b.SourceComponentName, "Production Status Monitoring Agent")
            .With(b => b.TargetComponentId, Guid.NewGuid().ToString)
            .With(b => b.TargetComponentName, "DR Status Monitoring Agent")
            .With(b => b.EnableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.DisableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.ErrorMessage, (string)null)
            .With(b => b.WorkflowStatus, "Actively Monitoring")
            .With(b => b.StartTime, DateTime.Now.AddHours(-24))
            .With(b => b.EndTime, DateTime.Now.AddHours(24))
            .With(b => b.RPO, "30 seconds")
            .With(b => b.Status, "Healthy")
            .With(b => b.IsFileTransfered, true));

        CreateCyberAirGapStatusCommand = fixture.Create<CreateCyberAirGapStatusCommand>();
        UpdateCyberAirGapStatusCommand = fixture.Create<UpdateCyberAirGapStatusCommand>();
        DeleteCyberAirGapStatusCommand = fixture.Create<DeleteCyberAirGapStatusCommand>();
        UpdateAirGapStatusCommand = fixture.Create<UpdateAirGapStatusCommand>();
        CyberAirGapStatusListVm = fixture.Create<CyberAirGapStatusListVm>();
        CyberAirGapStatusDetailVm = fixture.Create<CyberAirGapStatusDetailVm>();
    }
}
