﻿using ContinuityPatrol.Application.Features.LicenseManager.Events.Replace;
using ContinuityPatrol.Application.Helper;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.LicenseManager.Command.Replace;

public class LicenseReplaceCommandHandler : IRequestHandler<LicenseReplaceCommand, LicenseReplaceResponse>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly IPublisher _publisher;
    private readonly IServerRepository _serverRepository;

    public LicenseReplaceCommandHandler(IServerRepository serverRepository,
        ILicenseInfoRepository licenseInfoRepository, IDatabaseRepository databaseRepository, IPublisher publisher)
    {
        _serverRepository = serverRepository;
        _licenseInfoRepository = licenseInfoRepository;
        _databaseRepository = databaseRepository;
        _publisher = publisher;
    }

    public async Task<LicenseReplaceResponse> Handle(LicenseReplaceCommand request, CancellationToken cancellationToken)
    {
        if (request.EntityType.Trim().ToLower().Equals("server"))
        {
            var server = await _serverRepository.GetByReferenceIdAsync(request.EntityId);

            Guard.Against.NullOrDeactive(server, nameof(Domain.Entities.Server),
                new NotFoundException(nameof(Domain.Entities.Server), request.EntityId));

            var jsonProperties = JsonConvert.DeserializeObject<dynamic>(server.Properties);

            jsonProperties =
                GetJsonProperties.ReplaceServerIpAndHostName(jsonProperties, request.IpAddress, request.HostName);

            var modifiedJson = jsonProperties.ToString();
            var sanitizedJson = modifiedJson.Replace("\r\n", "");

            server.Properties = sanitizedJson;

            await _serverRepository.UpdateAsync(server);

            var licenseInfo = await _licenseInfoRepository.GetLicenseInfoByEntityId(request.EntityId);

            var serverDto = await _serverRepository.GetByReferenceIdAsync(request.EntityId);

            var ipAddress = GetJsonProperties.GetIpAddressFromProperties(serverDto.Properties);

            var hostName = GetJsonProperties.GetHostNameFromProperties(serverDto.Properties);

            licenseInfo.EntityField = $"{ipAddress},{hostName}";

            await _licenseInfoRepository.UpdateAsync(licenseInfo);

            var response = new LicenseReplaceResponse
            {
                Message = Message.Update(nameof(Domain.Entities.Server), server.Name)
            };

            await _publisher.Publish(
                new LicenseReplaceEvent
                {
                    PONumber = SecurityHelper.Decrypt(licenseInfo.PONumber), EntityName = request.EntityName,
                    EntityField = licenseInfo.EntityField
                }, cancellationToken);

            return response;
        }

        if (request.EntityType.Trim().ToLower().Equals("database"))
        {
            var database = await _databaseRepository.GetByReferenceIdAsync(request.EntityId);

            Guard.Against.NullOrDeactive(database, nameof(Domain.Entities.Database),
                new NotFoundException(nameof(Domain.Entities.Database), request.EntityId));

            var server = await _serverRepository.GetByReferenceIdAsync(database.ServerId);

            if (server is not null)
            {
                var serverProperties = JsonConvert.DeserializeObject<dynamic>(server.Properties);

                serverProperties =
                    GetJsonProperties.ReplaceServerIpAndHostName(serverProperties, request.IpAddress, request.HostName);

                var modifiedServerJson = serverProperties.ToString();
                var sanitizedServerJson = modifiedServerJson.Replace("\r\n", "");

                server.Properties = sanitizedServerJson;

                await _serverRepository.UpdateAsync(server);
            }

            var jsonProperties =
                GetJsonProperties.GetJsonKeysContainingOracleSid(database.Properties, request.Sid);

            var modifiedJson = jsonProperties;
            var sanitizedJson = modifiedJson.Replace("\r\n", "");

            database.Properties = sanitizedJson;

            await _databaseRepository.UpdateAsync(database);

            var licenseInfo = await _licenseInfoRepository.GetLicenseInfoByEntityId(request.EntityId);

            var serverDto = await _serverRepository.GetByReferenceIdAsync(database.ServerId);

            var sid = GetJsonProperties.GetJsonDatabaseSidValue(database.Properties);

            var ipAddress = GetJsonProperties.GetIpAddressFromProperties(serverDto.Properties);

            var hostName = GetJsonProperties.GetHostNameFromProperties(serverDto.Properties);

            licenseInfo.EntityField = $"{ipAddress},{hostName},{sid}";

            await _licenseInfoRepository.UpdateAsync(licenseInfo);

            var response = new LicenseReplaceResponse
            {
                Message = Message.Update(nameof(Domain.Entities.Database), database.Name)
            };

            await _publisher.Publish(
                new LicenseReplaceEvent
                {
                    PONumber = SecurityHelper.Decrypt(licenseInfo.PONumber), EntityName = request.EntityName,
                    EntityField = licenseInfo.EntityField
                }, cancellationToken);

            return response;
        }

        throw new InvalidException($"Unsupported type:{request.EntityType}");
    }
}