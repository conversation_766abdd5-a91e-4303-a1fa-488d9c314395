﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberJobManagement.Events.Paginated;

public class CyberJobManagementPaginatedEventHandler : INotificationHandler<CyberJobManagementPaginatedEvent>
{
    private readonly ILogger<CyberJobManagementPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberJobManagementPaginatedEventHandler(ILogger<CyberJobManagementPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(CyberJobManagementPaginatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.View} {Modules.CyberJobManagement}",
            Entity = Modules.CyberJobManagement.ToString(),
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Cyber Resiliency JobManagement viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Cyber Resiliency JobManagement viewed");
    }
}