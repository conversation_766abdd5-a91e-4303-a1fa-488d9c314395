﻿using ContinuityPatrol.Application.Features.InfraObject.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObject.Queries;

public class GetInfraObjectPaginatedListQueryHandlerTests : IClassFixture<InfraObjectFixture>
{
    private readonly GetInfraObjectPaginatedListQueryHandler _handler;
    private readonly Mock<IInfraObjectRepository> _mockInfraObjectRepository;
    private readonly Mock<IInfraObjectViewRepository> _mockInfraObjectViewRepository;
    private readonly InfraObjectFixture _infraObjectFixture;

    public GetInfraObjectPaginatedListQueryHandlerTests(InfraObjectFixture infraObjectFixture)
    {
        _infraObjectFixture = infraObjectFixture;

        _mockInfraObjectViewRepository = new Mock<IInfraObjectViewRepository>();

        _mockInfraObjectRepository = InfraObjectRepositoryMocks.GetPaginatedInfraObjectRepository(_infraObjectFixture.InfraObjects);

        _handler = new GetInfraObjectPaginatedListQueryHandler(_infraObjectFixture.Mapper, _mockInfraObjectViewRepository.Object);

        _infraObjectFixture.InfraObjects[0].Name = "Test_Infra";
        _infraObjectFixture.InfraObjects[0].BusinessFunctionName = "BF_Infra";
        _infraObjectFixture.InfraObjects[0].BusinessServiceName = "BS_Infra";
        _infraObjectFixture.InfraObjects[0].TypeName = "Type_Infra";
        _infraObjectFixture.InfraObjects[0].ReplicationTypeName = "Repli_Infra";
        _infraObjectFixture.InfraObjects[0].ReplicationCategoryType = "OS_Type";
        _infraObjectFixture.InfraObjects[0].Description = "Pending";
        _infraObjectFixture.InfraObjects[0].SubType = "Linux";
        //_infraObjectFixture.InfraObjects[0].PRServerName = "PR_Site";
        //_infraObjectFixture.InfraObjects[0].DRServerName = "DR_Site";
        //_infraObjectFixture.InfraObjects[0].NearDRServerName = "Replication";
        //_infraObjectFixture.InfraObjects[0].PRDatabaseName = "Site";
        //_infraObjectFixture.InfraObjects[0].DRDatabaseName = "Down";
        //_infraObjectFixture.InfraObjects[0].NearDRDatabaseName = "Updated";
        //_infraObjectFixture.InfraObjects[0].PRReplicationName = "Replication_Count";
        //_infraObjectFixture.InfraObjects[0].DRReplicationName = "Type_Base";
        //_infraObjectFixture.InfraObjects[0].NearDRReplicationName = "Near_Site";
        _infraObjectFixture.InfraObjects[0].Priority = "Medium";

        _infraObjectFixture.InfraObjects[1].Name = "Test_Infra123";
        _infraObjectFixture.InfraObjects[1].BusinessFunctionName = "BF_Infra123";
        _infraObjectFixture.InfraObjects[1].BusinessServiceName = "BS_Infra123";
        _infraObjectFixture.InfraObjects[1].TypeName = "Type_Infra123";
        _infraObjectFixture.InfraObjects[1].ReplicationTypeName = "Repli_Infra123";
        _infraObjectFixture.InfraObjects[1].ReplicationCategoryType = "Base_Type";
        _infraObjectFixture.InfraObjects[1].Description = "Started";
        _infraObjectFixture.InfraObjects[1].SubType = "Windows";
        //_infraObjectFixture.InfraObjects[1].PRServerName = "PR_Server";
        //_infraObjectFixture.InfraObjects[1].DRServerName = "DR_Server";
        //_infraObjectFixture.InfraObjects[1].NearDRServerName = "DataBase";
        //_infraObjectFixture.InfraObjects[1].PRDatabaseName = "InfraObject";
        //_infraObjectFixture.InfraObjects[1].DRDatabaseName = "Infra_Up";
        //_infraObjectFixture.InfraObjects[1].NearDRDatabaseName = "Deleted";
        //_infraObjectFixture.InfraObjects[1].PRReplicationName = "Count_Server";
        //_infraObjectFixture.InfraObjects[1].DRReplicationName = "Site_Down";
        //_infraObjectFixture.InfraObjects[1].NearDRReplicationName = "Near_Server";
        _infraObjectFixture.InfraObjects[1].Priority = "High";
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetInfraObjectPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraObjectListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedInfraObjects_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetInfraObjectPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Infra" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraObjectListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<InfraObjectListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("Test_Infra");

        result.Data[0].BusinessFunctionName.ShouldBe("BF_Infra");

        result.Data[0].BusinessServiceName.ShouldBe("BS_Infra");

        result.Data[0].ReplicationTypeName.ShouldBe("Repli_Infra");

        result.Data[0].ReplicationCategoryType.ShouldBe("OS_Type");

        result.Data[0].TypeName.ShouldBe("Type_Infra");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetInfraObjectPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraObjectListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_InfraObjects_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetInfraObjectPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=Test;businessfunctionname=BF;businessservicename=BS;replicationname=Rep;typename=Infra;replicationtype=OS_Type;description=Pending;subtype=Linux;prservername=PR_Site;drservername=DR_Site;neardrservername=Replication;prdatabasename=Site;drdatabasename=Down;neardrdatabasename=Updated;prreplicationname=Replication_Count;drreplicationname=Type_Base;neardrreplicationname=Near_Site;priority=Medium" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraObjectListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("Test_Infra");

        result.Data[0].BusinessFunctionName.ShouldBe("BF_Infra");

        result.Data[0].BusinessServiceName.ShouldBe("BS_Infra");

        result.Data[0].ReplicationTypeName.ShouldBe("Repli_Infra");

        result.Data[0].TypeName.ShouldBe("Type_Infra");

        result.Data[0].ReplicationCategoryType.ShouldBe("OS_Type");

            // result.Data[0].DRDatabaseId.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Description.ShouldBe(_infraObjectFixture.InfraObjects[0].Description);

    //    result.Data[0].PRServerId.ShouldBeGreaterThan(0.ToString());

        result.Data[0].ReplicationTypeId.ShouldBe(_infraObjectFixture.InfraObjects[0].ReplicationTypeId);

        result.Data[0].PairInfraObjectId.ShouldBe(_infraObjectFixture.InfraObjects[0].PairInfraObjectId);

        //result.Data[0].NearDRReplicationId.ShouldBe(_infraObjectFixture.InfraObjects[0].NearDRReplicationId);

        //result.Data[0].PRReplicationId.ShouldBe(_infraObjectFixture.InfraObjects[0].PRReplicationId);

        //result.Data[0].DRReplicationId.ShouldBe(_infraObjectFixture.InfraObjects[0].DRReplicationId);

        //result.Data[0].NearDRDatabaseId.ShouldBe(_infraObjectFixture.InfraObjects[0].NearDRDatabaseId);

        //result.Data[0].PRDatabaseId.ShouldBe(_infraObjectFixture.InfraObjects[0].PRDatabaseId);

        //result.Data[0].NearDRServerId.ShouldBe(_infraObjectFixture.InfraObjects[0].NearDRServerId);

        //result.Data[0].DRServerId.ShouldBe(_infraObjectFixture.InfraObjects[0].DRServerId);

        result.Data[0].BusinessFunctionId.ShouldBeGreaterThan(0.ToString());

        result.Data[0].BusinessServiceId.ShouldBeGreaterThan(0.ToString());

        result.Data[0].CompanyId.ShouldBeGreaterThan(0.ToString());

        result.Data[0].State.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Priority.ShouldBeGreaterThan(0.ToString());

        //result.Data[0].PRServerName.ShouldBeGreaterThan(0.ToString());

        //result.Data[0].DRServerName.ShouldBeGreaterThan(0.ToString());

        //result.Data[0].NearDRDatabaseName.ShouldBe(_infraObjectFixture.InfraObjects[0].NearDRDatabaseName);

        //result.Data[0].NearDRReplicationName.ShouldBe(_infraObjectFixture.InfraObjects[0].NearDRReplicationName);

        //result.Data[0].DRReplicationName.ShouldBeGreaterThan(0.ToString());

        //result.Data[0].PRReplicationName.ShouldBeGreaterThan(0.ToString());

        //result.Data[0].PRDatabaseName.ShouldBeGreaterThan(0.ToString());

        //result.Data[0].DRDatabaseName.ShouldBeGreaterThan(0.ToString());

        //result.Data[0].NearDRServerName.ShouldBe(_infraObjectFixture.InfraObjects[0].NearDRServerName);

        result.Data[0].DRReady.ShouldBeTrue();

        result.Data[0].IsPair.ShouldBeTrue();

      //  result.Data[0].IsAssociate.ShouldBeFalse();

        result.Data[0].SubType.ShouldBeGreaterThan(0.ToString());

        result.Data[0].RecoveryType.ShouldBeGreaterThan(0);

        result.Data[0].ReplicationStatus.ShouldBeGreaterThan(0);

        result.Data[0].DROperationStatus.ShouldBeGreaterThan(0);

        result.Data[0].NearDR.ShouldBeFalse();

        result.Data[0].Type.ShouldBeGreaterThan(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetInfraObjectPaginatedListQuery(), CancellationToken.None);

        _mockInfraObjectRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}