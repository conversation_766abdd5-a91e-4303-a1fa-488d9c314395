﻿using System.Globalization;
using ContinuityPatrol.Application.Features.UserLogin.Events.Login;
using ContinuityPatrol.Application.Features.UserLogin.Events.Logout;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Application.Services;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.RefreshToken;
using ContinuityPatrol.Shared.Infrastructure.Controllers;


namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class AccountsController : CommonBaseController
{
    private readonly IAuthenticationServiceFactory _authenticationFactory;
    private readonly ITokenManager _tokenManager;
    private readonly IPublisher _publisher;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMemoryCache _memoryCache;

    public AccountsController(IAuthenticationServiceFactory authenticationFactory, ITokenManager tokenManager, IPublisher publisher, ILoggedInUserService loggedInUserService, IMemoryCache memoryCache)
    {
        _authenticationFactory = authenticationFactory;
        _tokenManager = tokenManager;
        _publisher = publisher;
        _loggedInUserService = loggedInUserService;
        _memoryCache = memoryCache;
    }

    [HttpPost("authenticate")]
    [AllowAnonymous]
    public async Task<ActionResult<AuthenticationResponse>> AuthenticateAsync(AuthenticationRequest request)
    {
        var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();

        request.IpAddress = ipAddress ?? "localhost";

        var authenticationService = _authenticationFactory.GetAuthenticationService(request.AuthenticationType);

        var response = await authenticationService.AuthenticateAsync(request);

        if (!response.IsAuthorized) return Unauthorized();

        Logger.LogInformation($"User '{request.LoginName}' authorized.");

        var token = await _tokenManager.GenerateToken(response);

        var refreshToken = await _tokenManager.GenerateRefreshToken();

        SetRefreshToken(refreshToken, request);

        Logger.LogDebug($"User '{request.LoginName}' authentication token generated successfully.");

        var permission = JsonConvert.SerializeObject(response.Permissions);

        var authenticationResponse = new AuthenticationResponse
        {
            Token = token,
            UserId = response.UserId,
            UserName = response.LoginName,
            CompanyId = response.CompanyId,
            CompanyName = response.CompanyName,
            RoleId = response.Role,
            RoleName = response.RoleName,
            Permissions = permission,
            TwoFactorAuthentication = response.TwoFactorAuthentication,
            IsReset = response.IsReset,
            RefreshToken = refreshToken.Token,
            IsParent = response.IsParent,
            IsAuthorized = true,
            Expires = response.SessionTimeout,
            AuthenticationType = response.AuthenticationType,
            IsLicenseValidity = response.IsLicenseValidity,
            LicenseEmpty = response.LicenseEmpty,
            AssignedInfras = response.AssignedInfras,
            IsDefaultDashboard = response.IsDefaultDashboard,
            Url = response.Url
        };

        await _publisher.Publish(new UserLoginEvent { UserId = response.UserId, LoginName = response.LoginName,CompanyId = response.CompanyId });

        return Ok(authenticationResponse);
    }

    [HttpPost("refresh-token")]
    public async Task<ActionResult> RefreshToken(string refreshToken)
    {
        var bytes = (byte[])_memoryCache.Get(_loggedInUserService.LoginName)!;

        var passwordBytes = (byte[])_memoryCache.Get(refreshToken)!;

        var cacheRefreshToken = Encoding.UTF8.GetString(bytes);

        var cachePasswordValue = Encoding.UTF8.GetString(passwordBytes);

        var dateRefreshTokenBytes = (byte[])_memoryCache.Get(cachePasswordValue)!;

        var cacheDateTimeValue = Encoding.UTF8.GetString(dateRefreshTokenBytes);

        if (!cacheRefreshToken.Equals(refreshToken))
        {
            return Unauthorized("Invalid Refresh Token.");
        }
        else if ((DateTime.UtcNow - Convert.ToDateTime(cacheDateTimeValue)).TotalDays > 1)
        {
            return Unauthorized("Token expired.");
        }

        var request = new AuthenticationRequest
        {
            LoginName = _loggedInUserService.LoginName,
            Password = cachePasswordValue,
            CompanyId = Encoding.UTF8.GetString(((byte[])_memoryCache.Get(_loggedInUserService.CompanyId)!)),
            AuthenticationType = Encoding.UTF8.GetString(((byte[])_memoryCache.Get("authenticationType")!)),
            IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "localhost"
        };

        var authenticationService = _authenticationFactory.GetAuthenticationService(request.AuthenticationType);

        var response = await authenticationService.AuthenticateAsync(request);

        var token = await _tokenManager.GenerateToken(response);

        var newRefreshToken = await _tokenManager.GenerateRefreshToken();

        SetRefreshToken(newRefreshToken, request);

        Logger.LogInformation($"User '{request.LoginName}' authentication refresh token generated successfully.");

        var refreshTokenResponse = new RefreshTokenResponse
        {
            Token = token,
            RefreshToken = newRefreshToken.Token
        };

        return Ok(refreshTokenResponse);
    }
    private void SetRefreshToken(RefreshTokenRequest newRefreshToken, AuthenticationRequest request)
    {
        var options = new MemoryCacheEntryOptions()
               .SetAbsoluteExpiration(DateTime.UtcNow.AddDays(1));

        _memoryCache.Set(request.LoginName, Encoding.UTF8.GetBytes(newRefreshToken.Token));
        _memoryCache.Set(newRefreshToken.Token, Encoding.UTF8.GetBytes(request.Password));
        _memoryCache.Set(request.Password, Encoding.UTF8.GetBytes(newRefreshToken.Created.ToString(CultureInfo.InvariantCulture)), options);
        _memoryCache.Set("authenticationType", Encoding.UTF8.GetBytes(request.AuthenticationType));
        _memoryCache.Set(request.CompanyId, Encoding.UTF8.GetBytes(request.CompanyId));
    }

    [HttpPost("send-otp")]
    public Task<ActionResult> TwoFactorAuthenticationAsync(TwoFactorAuthenticationRequest request)
    {
        try
        {
            if (_loggedInUserService.LoginName != request.LoginName) return Task.FromResult<ActionResult>(Unauthorized());

            var otp = new Random().Next(1, 10000).ToString("D4");

            var options = new MemoryCacheEntryOptions()
                .SetAbsoluteExpiration(DateTime.UtcNow.AddMinutes(3));

            _memoryCache.Set(request.LoginName, Encoding.UTF8.GetBytes(otp));
            _memoryCache.Set(otp, Encoding.UTF8.GetBytes(DateTime.UtcNow.ToString(CultureInfo.InvariantCulture)), options);

            //var model = new SmsMessage
            //{
            //    Message = $"Your OTP code is: {otp}"
            //};
            //await MessageResource.CreateAsync(
            //to: new PhoneNumber($"+91{request.PhoneNumber}"),
            //from: new PhoneNumber("+16207054434"),
            //body: model.Message,
            //client: _client);
        }
        catch (Exception ex)
        {
            throw new InvalidException($"Failed to send SMS: {ex.Message}");
        }

        return Task.FromResult<ActionResult>(Ok(new TwoFactorAuthenticationResponse
        {
            Message = "SMS Send Successfully!."
        }));
    }

    [HttpPost("submit-otp")]
    public ActionResult<bool> SubmitOtp(string loginName, string otp)
    {
        if (_loggedInUserService.LoginName != loginName)
            return false;

        var bytes = (byte[])_memoryCache.Get(loginName)!;
        var dateTimeBytes = (byte[])_memoryCache.Get(otp)!;

        var cacheOtpValue = Encoding.UTF8.GetString(bytes);
        var cacheDateTimeValue = Encoding.UTF8.GetString(dateTimeBytes);

        if (cacheOtpValue != otp)
            return false;

        if ((DateTime.UtcNow - Convert.ToDateTime(cacheDateTimeValue)).TotalMinutes > 3)
            return false;

        _memoryCache.Remove(otp);
        //_memoryCache.Remove(loginName);
        return true;
    }

    [HttpPost("logout")]
    public async Task<ActionResult> LogoutAsync()
    {
        Logger.LogInformation($"Logged out user '{LoggedInUserService.LoginName}'");

        await _tokenManager.Clear(LoggedInUserService.LoginName);

        await _publisher.Publish(new UserLogoutEvent { UserId = LoggedInUserService.UserId, LoginName = LoggedInUserService.LoginName });

        return Ok();
    }
    [NonAction]
    public override void ClearDataCache()
    {
        throw new NotImplementedException();
    }
}