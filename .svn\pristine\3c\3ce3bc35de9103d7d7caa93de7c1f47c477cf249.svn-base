﻿using ContinuityPatrol.Domain.ViewModels.BusinessServiceEvaluationModel;

namespace ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Queries.GetList;

public class GetBusinessServiceEvaluationListQueryHandler : IRequestHandler<GetBusinessServiceEvaluationListQuery,
    List<BusinessServiceEvaluationListVm>>
{
    private readonly IBusinessServiceEvaluationRepository _businessServiceEvaluationRepository;
    private readonly IMapper _mapper;

    public GetBusinessServiceEvaluationListQueryHandler(IMapper mapper,
        IBusinessServiceEvaluationRepository businessServiceEvaluationRepository)
    {
        _mapper = mapper;
        _businessServiceEvaluationRepository = businessServiceEvaluationRepository;
    }

    public async Task<List<BusinessServiceEvaluationListVm>> Handle(GetBusinessServiceEvaluationListQuery request,
        CancellationToken cancellationToken)
    {
        var businessServiceEvaluations = (await _businessServiceEvaluationRepository.ListAllAsync())
            .OrderByDescending(x => x.GradeValue).ToList();

        return businessServiceEvaluations.Count <= 0
            ? new List<BusinessServiceEvaluationListVm>()
            : _mapper.Map<List<BusinessServiceEvaluationListVm>>(businessServiceEvaluations);
    }
}