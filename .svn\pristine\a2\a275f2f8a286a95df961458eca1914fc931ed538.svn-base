using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperationGroup.Queries;

public class GetBulkImportOperationGroupDetailQueryTests : IClassFixture<BulkImportOperationGroupFixture>
{
    private readonly BulkImportOperationGroupFixture _bulkImportOperationGroupFixture;
    private readonly Mock<IBulkImportOperationGroupRepository> _mockBulkImportOperationGroupRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetBulkImportOperationGroupDetailsQueryHandler _handler;

    public GetBulkImportOperationGroupDetailQueryTests(BulkImportOperationGroupFixture bulkImportOperationGroupFixture)
    {
        _bulkImportOperationGroupFixture = bulkImportOperationGroupFixture;

        _mockBulkImportOperationGroupRepository = BulkImportOperationGroupRepositoryMocks.CreateQueryBulkImportOperationGroupRepository(_bulkImportOperationGroupFixture.BulkImportOperationGroups);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<BulkImportOperationGroupDetailVm>(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
            .Returns((Domain.Entities.BulkImportOperationGroup entity) => new BulkImportOperationGroupDetailVm
            {
                Id = entity.ReferenceId,
                BulkImportOperationId = entity.BulkImportOperationId,
                CompanyId = entity.CompanyId,
                Properties = entity.Properties,
                Status = entity.Status,
                ProgressStatus = entity.ProgressStatus,
                ErrorMessage = entity.ErrorMessage,
                ConditionalOperation = entity.ConditionalOperation,
                NodeId = entity.NodeId,
                InfraObjectName = entity.InfraObjectName
            });

        _handler = new GetBulkImportOperationGroupDetailsQueryHandler(
            _mockMapper.Object,
            _mockBulkImportOperationGroupRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_BulkImportOperationGroupDetailVm_When_BulkImportOperationGroupExists()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var query = new GetBulkImportOperationGroupDetailQuery { Id = existingGroup.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(BulkImportOperationGroupDetailVm));
        result.Id.ShouldBe(existingGroup.ReferenceId);
        result.BulkImportOperationId.ShouldBe(existingGroup.BulkImportOperationId);
        result.CompanyId.ShouldBe(existingGroup.CompanyId);
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var query = new GetBulkImportOperationGroupDetailQuery { Id = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(query, CancellationToken.None);

    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var query = new GetBulkImportOperationGroupDetailQuery { Id = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(query, CancellationToken.None);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationGroupNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var query = new GetBulkImportOperationGroupDetailQuery { Id = nonExistentId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportOperationGroup)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationGroupIsInactive()
    {
        // Arrange
        var inactiveGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        inactiveGroup.IsActive = false;
        var query = new GetBulkImportOperationGroupDetailQuery { Id = inactiveGroup.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_MapEntityToViewModel_WithCorrectProperties()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        existingGroup.BulkImportOperationId = "TestOperationId";
        existingGroup.CompanyId = "TestCompanyId";
        existingGroup.Properties = "{\"test\":\"value\"}";
        existingGroup.Status = "Pending";
        existingGroup.ProgressStatus = "0/5";
        existingGroup.ErrorMessage = "";
        existingGroup.ConditionalOperation = 1;
        existingGroup.NodeId = "Node001";
        existingGroup.InfraObjectName = "TestInfraObject";

        var query = new GetBulkImportOperationGroupDetailQuery { Id = existingGroup.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var query = new GetBulkImportOperationGroupDetailQuery { Id = testId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_bulkImportOperationGroupFixture.BulkImportOperationGroups.First());

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectViewModelType_When_MappingSuccessful()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var query = new GetBulkImportOperationGroupDetailQuery { Id = existingGroup.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<BulkImportOperationGroupDetailVm>();
        result.GetType().ShouldBe(typeof(BulkImportOperationGroupDetailVm));
    }

    [Fact]
    public async Task Handle_MapProgressProperties_WithCorrectValues()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        existingGroup.Status = "In Progress";
        existingGroup.ProgressStatus = "3/10";
        existingGroup.ErrorMessage = "Some error occurred";

        var query = new GetBulkImportOperationGroupDetailQuery { Id = existingGroup.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Status.ShouldBe("In Progress");
        result.ProgressStatus.ShouldBe("3/10");
        result.ErrorMessage.ShouldBe("Some error occurred");
    }

    [Fact]
    public async Task Handle_MapConditionalOperationAndNode_WithCorrectValues()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 2;
        existingGroup.NodeId = "Node123";
        existingGroup.InfraObjectName = "ProductionInfra";

        var query = new GetBulkImportOperationGroupDetailQuery { Id = existingGroup.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
    }
}
