using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ServerSubTypeFixture : IDisposable
{
    public List<ServerSubType> ServerSubTypePaginationList { get; set; }
    public List<ServerSubType> ServerSubTypeList { get; set; }
    public ServerSubType ServerSubTypeDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ServerSubTypeFixture()
    {
        var fixture = new Fixture();

        ServerSubTypeList = fixture.Create<List<ServerSubType>>();

        ServerSubTypePaginationList = fixture.CreateMany<ServerSubType>(20).ToList();

        ServerSubTypeDto = fixture.Create<ServerSubType>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public ServerSubType CreateServerSubType(
        string name = "TestServerSubType",
        string serverTypeId = "SERVER_TYPE_001",
        string serverTypeName = "Test Server Type",
        string logo = "test-logo.png",
        bool isActive = true)
    {
        return new ServerSubType
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = name,
            ServerTypeId = serverTypeId,
            ServerTypeName = serverTypeName,
            Logo = logo,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.Now,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.Now
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
