﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Create;

public class
    CreateWorkflowOperationCommandHandler : IRequestHandler<CreateWorkflowOperationCommand,
        CreateWorkflowOperationResponse>
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IWorkflowOperationRepository _workflowOperationRepository;

    public CreateWorkflowOperationCommandHandler(IMapper mapper,
        IWorkflowOperationRepository workflowOperationRepository, IPublisher publisher,
        ILoggedInUserService loggedInUserService)
    {
        _mapper = mapper;
        _workflowOperationRepository = workflowOperationRepository;
        _publisher = publisher;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<CreateWorkflowOperationResponse> Handle(CreateWorkflowOperationCommand request,
        CancellationToken cancellationToken)
    {
        request.CompanyId = _loggedInUserService.CompanyId;
        request.UserName = _loggedInUserService.LoginName;

        var workflowOperation = _mapper.Map<Domain.Entities.WorkflowOperation>(request);

        await _workflowOperationRepository.AddAsync(workflowOperation);

        var response = new CreateWorkflowOperationResponse
        {
            Message = Message.Create(nameof(Domain.Entities.WorkflowOperation), workflowOperation.ProfileId),

            WorkflowOperationId = workflowOperation.ReferenceId
        };

        //await _publisher.Publish(new WorkflowOperationCreatedEvent { Description = workflowOperation.Description }, cancellationToken);

        return response;
    }
}