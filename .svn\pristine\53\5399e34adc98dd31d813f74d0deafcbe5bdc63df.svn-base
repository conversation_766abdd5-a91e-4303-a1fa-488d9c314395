﻿namespace ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.Update;

public class UpdateSmtpConfigurationCommandValidator : AbstractValidator<UpdateSmtpConfigurationCommand>
{
    public UpdateSmtpConfigurationCommandValidator()
    {
        RuleFor(p => p.SmtpHost)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(
                @"^((?=.{1,253}$)(?:(?!-)[A-Za-z0-9-]{1,63}(?<!-)\.)+[A-Za-z]{2,6}|((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))$")
            .WithMessage("Please enter a valid {PropertyName}.");

        RuleFor(p => p.Port)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$")
            .WithMessage("Please enter a valid {PropertyName}.");

        RuleFor(p => p.UserName)
            .NotEmpty().WithMessage("Email is required.")
            /*MustAsync(IsMatch).WithMessage("Invalid email.")*/
            .NotNull();

        RuleFor(y => y)
            .NotNull()
            .MustAsync(VerifyGuid)
            .WithMessage("Id is invalid");
    }

    private Task<bool> VerifyGuid(UpdateSmtpConfigurationCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.Id, "SmtpConfiguration Id");

        return Task.FromResult(true);
    }

    private async Task<bool> IsMatch(string userName, CancellationToken cancellationToken)
    {
        var regexMail = new Regex(@"^[^\s@]+@[^\s@]+\.[^\s@]+$");
        var decryptedUserName = SecurityHelper.Decrypt(userName);
        var userMatch = regexMail.Match(decryptedUserName);
        return await Task.FromResult(userMatch.Success);
    }
}