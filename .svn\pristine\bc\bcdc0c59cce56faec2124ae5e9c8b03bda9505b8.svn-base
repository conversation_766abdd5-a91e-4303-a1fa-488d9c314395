using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MenuBuilderFixture : IDisposable
{
    public List<MenuBuilder> MenuBuilderPaginationList { get; set; }
    public List<MenuBuilder> MenuBuilderList { get; set; }
    public MenuBuilder MenuBuilderDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public MenuBuilderFixture()
    {
        var fixture = new Fixture();

        MenuBuilderList = fixture.Create<List<MenuBuilder>>();

        MenuBuilderPaginationList = fixture.CreateMany<MenuBuilder>(20).ToList();

        MenuBuilderDto = fixture.Create<MenuBuilder>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
