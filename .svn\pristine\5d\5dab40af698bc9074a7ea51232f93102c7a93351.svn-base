﻿namespace ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDrDrillByBusinessServiceId;

public class WorkflowOperationGroupDrDrillVm
{
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string StartTime { get; set; }
    public string EndTime { get; set; }
    public TimeSpan TotalTime { get; set; }
    public string PrIpAddress { get; set; }
    public string DrIpAddress { get; set; }
    public string Status { get; set; }
}