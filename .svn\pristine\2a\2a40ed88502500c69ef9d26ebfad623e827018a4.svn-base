﻿using ContinuityPatrol.Application.Features.Setting.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Setting.Commands;

public class DeleteSettingTests : IClassFixture<SettingFixture>
{
    private readonly SettingFixture _settingFixture;

    private readonly Mock<ISettingRepository> _mockSettingRepository;

    private readonly DeleteSettingCommandHandler _handler;

    public DeleteSettingTests(SettingFixture settingFixture)
    {
        _settingFixture = settingFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockSettingRepository = SettingRepositoryMocks.DeleteSettingRepository(_settingFixture.Settings);

        _handler = new DeleteSettingCommandHandler(_mockSettingRepository.Object, mockPublisher.Object);
    }


    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_SettingDeleted()
    {
        var validGuid = Guid.NewGuid();

        _settingFixture.Settings[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteSettingCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteSettingResponse_When_SettingDeleted()
    {
        var validGuid = Guid.NewGuid();

        _settingFixture.Settings[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteSettingCommand() { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteSettingResponse));

        result.IsActive.ShouldBeFalse();

        result.Success.ShouldBeTrue();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_UpdateReferenceIdAsync_IsActiveFalse_When_SettingDeleted()
    {
        var validGuid = Guid.NewGuid();

        _settingFixture.Settings[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteSettingCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var setting = await _mockSettingRepository.Object.GetByReferenceIdAsync(_settingFixture.Settings[0].ReferenceId);

        setting.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidSettingId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteSettingCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();

        _settingFixture.Settings[0].ReferenceId = validGuid.ToString();

        var setting = _settingFixture.Settings[0];

        var ValidGuid = _settingFixture.Settings[0].ReferenceId.ToString();

        _settingFixture.Settings[0].ReferenceId = Guid.NewGuid().ToString();

        var result = await _handler.Handle(new DeleteSettingCommand { Id = _settingFixture.Settings[0].ReferenceId }, CancellationToken.None);

        _mockSettingRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockSettingRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.Setting>()), Times.Once);
    }
}