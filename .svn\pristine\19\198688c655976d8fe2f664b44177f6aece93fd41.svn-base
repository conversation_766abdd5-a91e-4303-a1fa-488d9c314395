using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class SiteLocationFilterSpecification : Specification<SiteLocation>
{
    public SiteLocationFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.City != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("city=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.City.Contains(stringItem.Replace("city=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("cityascii=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.CityAscii.Contains(stringItem.Replace("cityascii=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("lat=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Lat.Contains(stringItem.Replace("lat=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("lng=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Lng.Contains(stringItem.Replace("lng=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("country=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Country.Contains(stringItem.Replace("country=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("iso2=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Iso2.Contains(stringItem.Replace("iso2=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("iso3=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Iso3.Contains(stringItem.Replace("iso3=", "", StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p => p.City.Contains(searchString) || p.CityAscii.Contains(searchString) ||
                                p.Lat.Contains(searchString) || p.Lng.Contains(searchString) ||
                                p.Country.Contains(searchString) || p.Iso2.Contains(searchString) ||
                                p.Iso3.Contains(searchString);
            }
        }
    }
}