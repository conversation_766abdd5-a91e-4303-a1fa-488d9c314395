﻿using ContinuityPatrol.Application.Features.AlertNotification.Commands.Create;
using ContinuityPatrol.Application.Features.AlertNotification.Commands.Update;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.AlertNotificationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class AlertNotificationProfile : Profile
{
    public AlertNotificationProfile()
    {
        CreateMap<AlertNotification, CreateAlertNotificationCommand>().ReverseMap();
        CreateMap<UpdateAlertNotificationCommand, AlertNotification>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<AlertNotification, AlertNotificationListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<AlertNotification, AlertNotificationDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<AlertNotification, AlertNotificationListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<AlertNotification, AlertNotificationDetailByInfraObjectIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<PaginatedResult<AlertNotification>, PaginatedResult<AlertNotificationListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}