using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class StateMonitorLogFixture : IDisposable
{
    public List<StateMonitorLog> StateMonitorLogPaginationList { get; set; }
    public List<StateMonitorLog> StateMonitorLogList { get; set; }
    public StateMonitorLog StateMonitorLogDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public StateMonitorLogFixture()
    {
        var fixture = new Fixture();

        StateMonitorLogList = fixture.Create<List<StateMonitorLog>>();

        StateMonitorLogPaginationList = fixture.CreateMany<StateMonitorLog>(20).ToList();

        StateMonitorLogDto = fixture.Create<StateMonitorLog>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
