﻿using ContinuityPatrol.Domain.ViewModels;
using ContinuityPatrol.Domain.ViewResults;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Infrastructure;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Controllers;
using ContinuityPatrol.Application.Features.User.Commands.ForgotPassword;
using ContinuityPatrol.Application.Features.User.Commands.UpdatePassword;
using ContinuityPatrol.Application.Features.User.Queries.GetLoginName;
using ContinuityPatrol.Application.Features.UserLogin.Commands.Update;
using ContinuityPatrol.Application.Features.UserLogin.Queries.GetDetail;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.ViewModels.UserModel.ChangePasswordModels;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels;
using ContinuityPatrol.Domain.ViewModels.AboutCpModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Tests.Constants;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Helper;
using ContinuityPatrol.Shared.Tests.Mocks;
using ContinuityPatrol.Application.Features.UserLogin.Commands.ClearSession;
using ContinuityPatrol.Application.Features.UserLogin.Events.Logout;

namespace ContinuityPatrol.Web.UnitTests.Controller;
 
public class AccountControllerShould
{
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private readonly Mock<ILogger<AccountController>> _mockLogger = new();
    private readonly Mock<IDomainService> _mockDomainService = new();
    private readonly Mock<IPublisher> _mockPublisher = new();
    private readonly Mock<IDistributedCache> _mockDistributedCache = new();
    private Mock<ILoginService> _mockLoginService;

    private AccountController _sut = null!;

    public const string UserId = "********-072f-4ed0-9848-a469cbcfabdc";

    public AccountControllerShould()
    {
        _mockLoginService = LoginServiceMocks.PrepareLoginView(ApplicationStatus.Qualified);
        Initialize();
    }

    internal void Initialize()
    {
        _sut = new AccountController(
            _mockDataProvider.Object,
            _mockLogger.Object,
            _mockMapper.Object,
            _mockLoginService.Object,
            _mockDomainService.Object,
            _mockPublisher.Object,
            _mockDistributedCache.Object);
        _sut.ControllerContext = new ControllerContextMocks().Default();
        _sut.TempData = TempDataFakes.GeTempDataDictionary(_sut.HttpContext, "Test", "Test");
    }

    public static LoginViewModel InHouseLoginViewModel => LoginFakes.InHouseLoginViewModel;
    public static LoginViewModel AdLoginViewModel => LoginFakes.AdLoginViewModel;

    #region Login


    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_Verify_OldLogin_Session_ShouldRedirect_WhenSessionExists()
    {
        var controller = _sut;

        var context = new DefaultHttpContext();

        context.Request.Headers["Cookie"] = "__Host-Identity=some-value";

        controller.ControllerContext = new ControllerContext
        {
            HttpContext = context
        };

        _mockDataProvider.Setup(dp => dp.UserLogin.ClearDatabaseSession(It.IsAny<string>())).ReturnsAsync(new ClearSessionUserLoginResponse
        {
            Message = "Session cleared successfully",
        });

        WebHelper.UserSession = new UserSession { LoggedUserId = "test-user-id" };

        var result = await controller.Login();

        var redirect = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("PreLogin", redirect.ActionName);
    }


    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_Return_ViewResult()
    {
        var result = await _sut.Login();
        var viewResult = Assert.IsType<ViewResult>(result);
        viewResult.Model.ShouldBeOfType<LoginViewModel>();
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_WhenOldSessionExists_RedirectsToPreLogin()
    {
        _sut.ControllerContext = new ControllerContextMocks().OldLogin();
        WebHelper.UserSession = new UserSession() { LoggedUserId = UserId };
        ClearDatabaseSessionMock();

        var result = await _sut.Login();

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        
        redirectToActionResult.ActionName.ShouldBe("PreLogin");
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_Call_PrepareView_OnlyOnce()
    {
        await _sut.Login();

        _mockLoginService.Verify(x => x.PrepareLoginViewAsync(), Times.Once);
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_Companies_NotEmpty_PrepareView_NeverCall()
    {
        WebHelper.CurrentSession.Set("CompanyProfiles", CompanyFakes.GetCompanyListItems());

        await _sut.Login();

        _mockLoginService.Verify(x => x.PrepareLoginViewAsync(), Times.Never);
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_ReturnsViewResult_WhenCompanyListIsAvailable()
    {
        var companyList = new List<SelectListItem> { new() { Text = "Company1", Value = "1" } };
        WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
        var result = await _sut.Login();
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.NotNull(viewResult);
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_ReturnsView_WhenApplicationStatusIsUnhandledError()
    {
        
        WebHelper.CurrentSession.Remove("CompanyProfiles");

        var viewResultMock = new PreLoginViewResult { ApplicationStatus = ApplicationStatus.UnhandledError };
        _mockLoginService.Setup(ls => ls.PrepareLoginViewAsync()).ReturnsAsync(viewResultMock);

        
        var result = await _sut.Login();

        
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.NotNull(viewResult);
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_ReturnsView_WhenApplicationStatusIsQualified()
    {

        WebHelper.CurrentSession.Remove("CompanyProfiles");

        var viewResultMock = new PreLoginViewResult { ApplicationStatus = ApplicationStatus.Qualified };
        _mockLoginService.Setup(ls => ls.PrepareLoginViewAsync()).ReturnsAsync(viewResultMock);

        var result = await _sut.Login();

        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.NotNull(viewResult);
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_ReturnsView_When_PreLoginView_Has_Message()
    {
        // Arrange
        WebHelper.CurrentSession.Remove("CompanyProfiles");

        var viewResultMock = new PreLoginViewResult
        {
            ApplicationStatus = ApplicationStatus.Qualified,
            Message = "Login"
        };

        _mockLoginService
            .Setup(ls => ls.PrepareLoginViewAsync())
            .ReturnsAsync(viewResultMock);

        // Act
        var result = await _sut.Login();

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result); 
        Assert.IsType<LoginViewModel>(viewResult.Model);
    }

   


    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_ReturnsView_WhenApplicationStatusIsNotAllowedToConnectTheServer()
    {

        WebHelper.CurrentSession.Remove("CompanyProfiles");

        var viewResultMock = new PreLoginViewResult { ApplicationStatus = ApplicationStatus.NotAllowedToConnectTheServer };
        _mockLoginService.Setup(ls => ls.PrepareLoginViewAsync()).ReturnsAsync(viewResultMock);

        var result = await _sut.Login();

        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.NotNull(viewResult);
    }


    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_RedirectsToUserInfo_WhenApplicationStatusIsEmptyUser()
    {
        
        WebHelper.CurrentSession.Remove("CompanyProfiles");

        var viewResultMock = new PreLoginViewResult { ApplicationStatus = ApplicationStatus.EmptyUser };
        _mockLoginService.Setup(ls => ls.PrepareLoginViewAsync()).ReturnsAsync(viewResultMock);

        
        var result = await _sut.Login();

        
        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Configuration", redirectToActionResult.ActionName);
        Assert.Equal("Basic", redirectToActionResult.ControllerName);
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_RedirectsToConfiguration_WhenApplicationStatusIsEmptyCompanyProfile()
    {
        WebHelper.CurrentSession.Remove("CompanyProfiles");

        var viewResultMock = new PreLoginViewResult { ApplicationStatus = ApplicationStatus.EmptyCompanyProfile };
        _mockLoginService.Setup(ls => ls.PrepareLoginViewAsync()).ReturnsAsync(viewResultMock);

        var result = await _sut.Login();

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Configuration", redirectToActionResult.ActionName);
        Assert.Equal("Basic", redirectToActionResult.ControllerName);
    }

    #endregion


    #region LoginAsync

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ReturnsRedirectToActionResult_WhenModelStateIsInvalid()
    {
        _sut.ModelState.AddModelError("X", "Test Error");

        var result = await _sut.LoginAsync(InHouseLoginViewModel, "") as RedirectToActionResult;

        result!.ActionName.ShouldBe("Login");
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_Return_Login_When_NonLocalReturnUrl()
    {
        _sut.Url = UrlMocks.IsLocalUrl(false);

        var result = await _sut.LoginAsync(InHouseLoginViewModel, "https://www.google.com") as RedirectToActionResult;

        result!.ActionName.ShouldBe("Login");
    }
    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ShouldReturnJson_WhenLoginStatusIsNone()
    {
        // Arrange
        string expectedReturnUrl = "/home";
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.None,
            LoginName = "user",
            LoginId = "user-id"
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        _sut.Url = UrlMocks.IsLocalUrl(true); // Optional depending on your controller logic

        // Act
        var result = await _sut.LoginAsync(InHouseLoginViewModel, returnUrl: expectedReturnUrl, isMultipleLogin: false);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var resultData = new JsonResultDynamicWrapper(jsonResult);

        Assert.NotNull(resultData);

    }



    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task RouteToPostView_ShouldRedirectToDashboard_WhenLoginSucceeded()
    {

        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.Succeeded,
            LoginName = "testUser",
            LoginId = UserId,
            IsDefaultDashboard = true,
            Url = "/dashboard"
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        _sut.Url = UrlMocks.IsLocalUrl(true);

        var actionResult = await _sut.LoginAsync(InHouseLoginViewModel, "/dashboard");

        var redirect = Assert.IsType<LocalRedirectResult>(actionResult);
        Assert.Equal("/dashboard", redirect.Url);
    }


    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task RouteToPostView_ShouldIsMultipleLogin_WhenLoginSucceeded()
    {
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.Succeeded,
            LoginName = "testUser",
            LoginId = UserId,
            IsDefaultDashboard = true,
            Message = "Multiple login session found.",
            Url = "/dashboard"
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        _sut.Url = UrlMocks.IsLocalUrl(false);

        var actionResult = await _sut.LoginAsync(InHouseLoginViewModel, "", true);

        var json = Assert.IsType<JsonResult>(actionResult);

        var resultData = new JsonResultDynamicWrapper(json);

        Assert.NotNull(resultData);
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ShouldReturnJson_WhenMultipleLoginIsTrue_AndDefaultDashboardIsFalse()
    {
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.Succeeded,
            LoginName = "testUser",
            LoginId = UserId,
            IsDefaultDashboard = false,
            Message = "Multiple login session found.",
            Url = "/dashboard/ServiceAvailability/List"
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        _sut.Url = UrlMocks.IsLocalUrl(false);

        var actionResult = await _sut.LoginAsync(InHouseLoginViewModel, "", true);

        var json = Assert.IsType<JsonResult>(actionResult);

        var resultData = new JsonResultDynamicWrapper(json);

        Assert.NotNull(resultData);
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ShouldActionResult_WhenMultipleLoginIsFalse_AndDefaultDashboardIsTrue()
    {
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.Succeeded,
            LoginName = "testUser",
            LoginId = UserId,
            IsDefaultDashboard = true,
            Message = "Multiple login session found.",
            Url = "/dashboard/ServiceAvailability/List"
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        _sut.Url = UrlMocks.IsLocalUrl(false);

        var actionResult = await _sut.LoginAsync(InHouseLoginViewModel, "");

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(actionResult);
        Assert.Equal("List", redirectToActionResult.ActionName);
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ShouldActionResult_WhenMultipleLoginIsFalse_AndDashboardIsTrue()
    {
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.Succeeded,
            LoginName = "testUser",
            LoginId = UserId,
            IsDefaultDashboard = true,
            Message = "Multiple login session found.",
            Url = "dashboard/ServiceAvailability/List"
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        _sut.Url = UrlMocks.IsLocalUrl(false);

        var actionResult = await _sut.LoginAsync(InHouseLoginViewModel, "");

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(actionResult);
        Assert.Equal("List", redirectToActionResult.ActionName);
    }
    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ShouldActionResult_WhenMultipleLoginIsFalse_AndDashboardIsFalse()
    {
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.Succeeded,
            LoginName = "testUser",
            LoginId = UserId,
            IsDefaultDashboard = false,
            Message = "Multiple login session found.",
            Url = ""
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        _sut.Url = UrlMocks.IsLocalUrl(false);

        var actionResult = await _sut.LoginAsync(InHouseLoginViewModel, "");

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(actionResult);
        Assert.Equal("List", redirectToActionResult.ActionName);
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ShouldReturnJson_WithSiteAdminRole_WhenSiteAdminLandingPage_AndMultipleLoginIsTrue()
    {
        // Arrange
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.SiteAdminLandingPage,
            LoginName = "adminUser",
            LoginId = "adminId"
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        var returnUrl = "/admin";
        _sut.Url = UrlMocks.IsLocalUrl(true);

        // Act
        var result = await _sut.LoginAsync(InHouseLoginViewModel, returnUrl, isMultipleLogin: true);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);

        var resultData = new JsonResultDynamicWrapper(jsonResult);

        Assert.NotNull(resultData);

    }
    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ShouldRedirectToSiteAdminLanding_WhenSiteAdminLandingPage_AndMultipleLoginIsFalse()
    {
        // Arrange
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.SiteAdminLandingPage,
            LoginName = "adminUser",
            LoginId = "adminId"
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        _sut.Url = UrlMocks.IsLocalUrl(true);

        // Act
        var result = await _sut.LoginAsync(InHouseLoginViewModel, "", isMultipleLogin: false);

        // Assert
        var redirect = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("SiteAdminLanding", redirect.ActionName);
        Assert.Equal("User", redirect.ControllerName);
        Assert.Equal("Admin", redirect.RouteValues?["Area"]);
    }
    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ShouldRedirectToChangePassword_WhenLoginStatusIsPasswordReset()
    {
        // Arrange
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.PasswordReset,
            LoginName = "user",
            LoginId = "user-id",
            Message = "Your password has expired. Please reset."
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        // Act
        var result = await _sut.LoginAsync(InHouseLoginViewModel, "", isMultipleLogin: false);

        // Assert
        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("ChangePassword", redirectResult.ActionName);
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ShouldRedirectToLicenseLanding_WhenLoginStatusIsEmptyLicense()
    {
        // Arrange
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.EmptyLicense,
            LoginName = "testUser",
            LoginId = "testUserId"
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        _sut.Url = UrlMocks.IsLocalUrl(true); // Assuming UrlMocks is setup for tests

        // Act
        var result = await _sut.LoginAsync(InHouseLoginViewModel, "", isMultipleLogin: false);

        // Assert
        var redirect = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("LicenseLanding", redirect.ActionName);
        Assert.Equal("LicenseManager", redirect.ControllerName);
        Assert.Equal("Admin", redirect.RouteValues?["Area"]);
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ShouldRedirectToLicenseExpiredLanding_WhenLoginStatusIsLicenseExpiry()
    {
        // Arrange
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.LicenseExpiry,
            LoginName = "expiredUser",
            LoginId = "expiredUserId"
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        _sut.Url = UrlMocks.IsLocalUrl(true); // If your code uses Url.IsLocalUrl()

        // Act
        var result = await _sut.LoginAsync(InHouseLoginViewModel, "", isMultipleLogin: false);

        // Assert
        var redirect = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("LicenseExpiredLanding", redirect.ActionName);
        Assert.Equal("LicenseManager", redirect.ControllerName);
        Assert.Equal("Admin", redirect.RouteValues?["Area"]);
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ShouldRedirectToLogin_WhenLoginStatusIsInvalidLoginAttempt()
    {
        // Arrange
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.InvalidLoginAttempt,
            LoginName = "testUser",
            LoginId = "testUserId",
            Message = "Invalid credentials."
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        // Act
        var result = await _sut.LoginAsync(InHouseLoginViewModel, "", isMultipleLogin: false);

        // Assert
        var redirect = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Login", redirect.ActionName);
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ShouldRedirectToLogin_WhenLoginStatusIsAccountLocked()
    {
        // Arrange
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.AccountLocked,
            LoginName = "lockedUser",
            LoginId = "lockedUserId",
            Message = "Your account is locked."
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        // Mock TempData
        var tempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>());
        _sut.TempData = tempData;

        // Act
        var result = await _sut.LoginAsync(InHouseLoginViewModel, "", isMultipleLogin: false);

        // Assert
        var redirect = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Login", redirect.ActionName);

    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ShouldRedirectToLogin_WhenLoginStatusIsInvalidCredential()
    {
        // Arrange
        var postLoginViewResult = new PostLoginViewResult
        {
            LogInStatus = LogInStatus.InvalidCredential,
            LoginName = "wrongUser",
            LoginId = "wrongUserId",
            Message = "Invalid username or password."
        };

        _mockLoginService
            .Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
            .ReturnsAsync(postLoginViewResult);

        // Act
        var result = await _sut.LoginAsync(InHouseLoginViewModel, "", isMultipleLogin: false);

        // Assert
        var redirect = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Login", redirect.ActionName);
    }




    [Theory]
    [Trait("Category", "AccountController_LoginAsync")]
    [InlineData(false)]
    [InlineData(true)]
    public async Task LoginAsync_Redirect_ServiceAvailabilityPage_When_Authenticate(bool isAdAuthentication)
    {
        _mockLoginService = LoginServiceMocks.Authenticate(LogInStatus.Succeeded, isAdAuthentication);

        Initialize();

        _sut.Url = UrlMocks.IsLocalUrl(true);

        var result = await _sut.LoginAsync(isAdAuthentication ? AdLoginViewModel : InHouseLoginViewModel, "") as RedirectToActionResult;

        result!.ActionName.ShouldBe("List");
        result.ControllerName.ShouldBe("ServiceAvailability");
    }

    [Theory]
    [Trait("Category", "AccountController_LoginAsync")]
    [InlineData("", "test", AccountConstant.RequiredLoginName)]
    [InlineData(" ", "test", AccountConstant.RequiredLoginName)]
    [InlineData("test", "", AccountConstant.RequiredPassword)]
    [InlineData("test", " ", AccountConstant.RequiredPassword)]
    public async Task LoginAsync_ThrowInvalidArgumentException_WhenModelInHouseModelWithEmpty(string user, string password, string errorMsg)
    {
        var model = LoginFakes.GetInHouseLoginViewModel(user, password, "********-072f-4ed0-9848-a469cbcfabdc");

        var exception = await Should.ThrowAsync<InvalidArgumentException>(() => _sut.LoginAsync(model, ""));

        exception.Message.ShouldBe(errorMsg);
    }


    [Theory]
    [Trait("Category", "AccountController_LoginAsync")]
    [MemberData(nameof(GetInvalidCompanyId))]
    public async Task LoginAsync_ThrowInvalidArgumentException_WhenInvalidCompanyId(string companyId)
    {
        var model = LoginFakes.GetInHouseLoginViewModel("test", "password", companyId);

       var exception = await Should.ThrowAsync<InvalidArgumentException>(() => _sut.LoginAsync(model, ""));

       exception.Message.ShouldBe("Input 'CompanyId' is not valid format.");
    }
    

    [Theory]
    [Trait("Category", "AccountController_LoginAsync")]
    [InlineData(null, "test", "********-072f-4ed0-9848-a469cbcfabdc", AccountConstant.NullLoginName)]
    [InlineData("test", null, "********-072f-4ed0-9848-a469cbcfabdc", AccountConstant.NullPassword)]
    [InlineData("test", "test", null, AccountConstant.NullCompanyId)]
    public async Task LoginAsync_ThrowArgumentNullException_WhenInHouseModelIsNull(string? user, string? password, string? companyId, string errorMsg)
    {
        var model = LoginFakes.GetInHouseLoginViewModel(user!, password!, companyId!);

        var exception = await Should.ThrowAsync<ArgumentNullException>(() => _sut.LoginAsync(model, ""));

        exception.Message.ShouldBe(errorMsg);
    }


    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ReturnsRedirectToAction_WhenUserIsAlreadyLoggedIn()
    {
        _mockLoginService = LoginServiceMocks.Authenticate(LogInStatus.MultipleLoginSessionFound, false);

        Initialize();

        _sut.Url = UrlMocks.IsLocalUrl(true);

        var result = await _sut.LoginAsync(InHouseLoginViewModel, "") as RedirectToActionResult;

        result!.ActionName.ShouldBe("Login");
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ReturnsRedirectToAction_WhenModelStateIsInvalid()
    {
        _sut.ModelState.AddModelError("Error", "Invalid model state");
        _sut.Url = UrlMocks.IsLocalUrl(true);

        var result = await _sut.LoginAsync(InHouseLoginViewModel, "") as RedirectToActionResult;

        result!.ShouldBeOfType<RedirectToActionResult>();
        result.ActionName.ShouldBe("Login");
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ReturnsRedirectToAction_WhenReturnUrlIsInvalid()
    {  
        var returnUrl = "http://malicious-site.com";
        _sut.Url = new Mock<IUrlHelper>().Object;

        var result = await _sut.LoginAsync(InHouseLoginViewModel, returnUrl);

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Login", redirectToActionResult.ActionName);
    }
    #endregion

    #region PreLogin

    [Fact]
    [Trait("Category", "AccountController_PreLogin")]
    public async Task PreLogin_Redirect_To_Login()
    {
        var result = await _sut.PreLogin() as RedirectToActionResult;

        result!.ActionName.ShouldBe("Login");
    }

    #endregion

    #region ChangePassword
    [Fact]
    [Trait("Category", "AccountController_ChangePassword")]
    public void ChangePassword_ReturnsViewResult()
    {
        var result = _sut.ChangePassword();

        var viewResult = Assert.IsType<ViewResult>(result);

        Assert.Null(viewResult.ViewName);  
    }

    #endregion

    #region  ChangePasswordAsync
    [Fact]
    [Trait("Category", "AccountController_ChangePasswordAsync")]
    public async Task ChangePasswordAsync_ReturnsRedirectToAction_WhenPasswordChangeIsSuccessful()
    {
        var model = new ChangePasswordViewModel { UserId = UserId, LoginName = "test"};
        var command = new UpdatePasswordCommand();
        _mockMapper.Setup(m => m.Map<UpdatePasswordCommand>(model)).Returns(command);

        _mockDataProvider.Setup(dp => dp.User.UpdateUserPassword(command)).ReturnsAsync(new BaseResponse { Success = true, Message = "Password changed successfully" });
        _mockDataProvider.Setup(dp => dp.User.GetUserByLoginName(It.IsAny<string>())).ReturnsAsync(new UserLoginNameVm() { Id = UserId });
      
        ClearDatabaseSessionMock();

        var result = await _sut.ChangePasswordAsync(model);

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Login", redirectToActionResult.ActionName);
    }

    private void ClearDatabaseSessionMock()
    {
        _mockDataProvider.Setup(dp => dp.UserLogin.GetUserInfoByUserId(It.IsAny<string>()))
            .ReturnsAsync(new UserLoginDetailVm());
        _mockDataProvider.Setup(dp => dp.UserLogin.UpdateAsync(It.IsAny<UpdateUserLoginCommand>()))
            .ReturnsAsync(It.IsAny<BaseResponse>());

        _mockMapper.Setup(m => m.Map<UpdateUserLoginCommand>(It.IsAny<UserLoginDetailVm>()))
            .Returns(new UpdateUserLoginCommand()); // mapping data
    }

    [Fact]
    [Trait("Category", "AccountController_ChangePasswordAsync")]
    public async Task ChangePasswordAsync_ReturnsRedirectToAction_WhenExceptionIsThrown()
    {
        var model = new ChangePasswordViewModel { UserId = UserId };
        _mockDataProvider.Setup(dp => dp.User.UpdateUserPassword(It.IsAny<UpdatePasswordCommand>())).ThrowsAsync(new Exception("Error"));

        var result = await _sut.ChangePasswordAsync(model);

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("ChangePassword", redirectToActionResult.ActionName);
    }

    #endregion

    #region ForgotPassword
    [Fact]
    [Trait("Category", "AccountController_ForgotPassword")]
    public void ForgotPassword_ReturnsViewResult()
    {
        var result = _sut.ForgotPassword();

        var viewResult = Assert.IsType<ViewResult>(result);

        Assert.Null(viewResult.ViewName);
    }

    #endregion

    #region ForgotPasswordAsync

    [Fact]
    [Trait("Category", "AccountController_ForgotPasswordAsync")]
    public async Task ForgotPasswordAsync_ReturnsRedirectToAction_WhenModelStateIsInvalid()
    {
        _sut.ModelState.AddModelError("Error", "Invalid model state");
        var model = new ForgotPasswordViewModel();
        
        var result = await _sut.ForgotPasswordAsync(model);
        
        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("ForgotPassword", redirectToActionResult.ActionName);
    }

    [Fact]
    [Trait("Category", "AccountController_ForgotPasswordAsync")]
    public async Task ForgotPasswordAsync_ReturnsRedirectToAction_WhenForgotPasswordIsSuccessful()
    {
        var model = new ForgotPasswordViewModel();
        var command = new ForgotPasswordCommand();
        _mockMapper.Setup(m => m.Map<ForgotPasswordCommand>(model)).Returns(command);
        _mockDataProvider.Setup(dp => dp.User.ForgotPassword(command)).ReturnsAsync(new BaseResponse { Message = "Password changed successfully" });

        var result = await _sut.ForgotPasswordAsync(model);

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Login", redirectToActionResult.ActionName);
    }

    [Fact]
    [Trait("Category", "AccountController_ForgotPasswordAsync")]
    public async Task ForgotPasswordAsync_ReturnsRedirectToAction_WhenExceptionIsThrown()
    {
        var model = new ForgotPasswordViewModel();
        _mockDataProvider.Setup(dp => dp.User.ForgotPassword(It.IsAny<ForgotPasswordCommand>())).ThrowsAsync(new Exception("Error"));

        var result = await _sut.ForgotPasswordAsync(model);
        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("ForgotPassword", redirectToActionResult.ActionName);
    }

    #endregion

    #region PatchList

    [Fact]
    [Trait("Category", "AccountController_PatchList")]
    public void PatchList_ReturnsViewResult()
    {
        var result = _sut.PatchList();

        var viewResult = Assert.IsType<ViewResult>(result);

        Assert.Null(viewResult.ViewName);
    }

    #endregion

    #region UserProfile
    [Fact]
    [Trait("Category", "AccountController_UserProfile")]
    public void UserProfile_ReturnsViewResult()
    {
        var result = _sut.UserProfile();

        var viewResult = Assert.IsType<ViewResult>(result);

        Assert.Null(viewResult.ViewName);
    }

    #endregion

    #region Logout

    [Fact]
    [Trait("Category", "AccountController_Logout")]
    public async Task Logout_ReturnsViewResult()
    {
        var result = await _sut.Logout();
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.NotNull(viewResult);
    }

    [Fact]
    [Trait("Category", "AccountController_Logout")]
    public async Task Logout_Should_Publish_UserLogoutEvent_And_ClearSession_When_UserId_Is_Present()
    {
        // Arrange
        var testUserId = "user123";
        var testLoginName = "DemoUser";

        // Set static session values
        WebHelper.UserSession = new UserSession
        {
            LoggedUserId = testUserId,
            LoginName = testLoginName
        };

        // Set up mock publisher
        _mockPublisher
            .Setup(p => p.Publish(It.Is<UserLogoutEvent>(e =>
                e.UserId == testUserId && e.LoginName == testLoginName), CancellationToken.None))
            .Returns(Task.CompletedTask)
            .Verifiable();


        var mockCache = new Mock<IDistributedCache>();

        // Set up mock cache
        mockCache
            .Setup(c => c.RemoveAsync($"Web:{testLoginName.ToLower()}", CancellationToken.None))
            .Returns(Task.CompletedTask)
            .Verifiable();

        _mockDataProvider.Setup(dp => dp.UserLogin.ClearDatabaseSession(testUserId))
            .ReturnsAsync(new ClearSessionUserLoginResponse());

        // Act
        var result = await _sut.Logout();

        // Assert
        Assert.IsType<ViewResult>(result);

        _mockPublisher.Verify(); 
        _mockLoginService.Verify(); 
    }




    #endregion

    #region Data

    public static TheoryData<string> GetInvalidCompanyId =>
        new()
        {
            "123e4567-e89b-12d3-a456",
            "123e4567-e89b-12d3-a456-4266141740001234",
            "123e4567-e89b-12d3-a456-42661417XXXX",
            "",
            " ",
            "123e4567-e89b-12d3-a456-426614174000!",
            "123e4567-e89b-12d3-a456-42661417400",
            "123e4567-e89b 12d3-a456/426614174000",
            "123e4567--12d3-a456-426614174000",
            "123e4567--e89b-12d3-a456--426614174000",
            "123e4567%2De89b%2D12d3%2Da456%2D426614174000",
            "123e4567-e89b-12d3-a456-426614174000; DROP TABLE users;",
            "123e4567-e89b-12d3-a456-426614174000<script>alert('xss')</script>",
            "00000000-0000-0000-0000-000000000000"
        };

    #endregion

    #region ClearSession
    [Fact]
    public async Task ClearSession_ValidLoginName_ReturnsSuccess()
    {
        var loginName = "validUser";
        var user = new UserLoginNameVm() { Id = UserId };
        _mockDataProvider.Setup(dp => dp.User.GetUserByLoginName(loginName)).ReturnsAsync(user);
        _mockDistributedCache.Setup(c => c.RemoveAsync(It.IsAny<string>(),CancellationToken.None)).Returns(Task.CompletedTask);
        ClearDatabaseSessionMock();
        WebHelper.CurrentSession.Set("LoginViewModel" + loginName, new LoginViewModel(){LoginName = "testUser"});
        
        var result = await _sut.ClearSession(loginName) as JsonResult;
        
        result.ShouldNotBeNull();
        dynamic resultData = new JsonResultDynamicWrapper(result);
        bool success = resultData.Success;
        success.ShouldBeTrue();
        LoginViewModel loginViewModel = resultData.loginViewModel;
        loginViewModel.LoginName.ShouldBe("testUser");
    }

   





    //[Fact]
    //[Trait("Category", "AccountController_Login")]
    //public async Task Login_Verify_OldLogin_Session()
    //{
    //    var mockRequestCookies = new Mock<IRequestCookieCollection>();
    //    mockRequestCookies.Setup(x => x.Keys).Returns(new List<string>());

    //    var result = await _sut.Login();

    //    var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);


    //    Assert.Equal("Configuration", redirectToActionResult.ActionName);
    //    Assert.Equal("Basic", redirectToActionResult.ControllerName);






    //    //var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
    //    //Assert.Equal("Configuration", redirectToActionResult.ActionName);
    //    //Assert.Equal("Basic", redirectToActionResult.ControllerName);
    //}




    [Fact]
    public async Task ClearSession_InvalidUserId_ShouldSignOutAndReturnFailure()
    {
        // Arrange
        var loginName = "invalidUser";
        var user = new UserLoginNameVm() { Id = "invalid-guid" }; // Invalid GUID

        var claimsIdentity = new ClaimsIdentity(
            new[] { new Claim(ClaimTypes.Name, "testuser") },
            CookieAuthenticationDefaults.AuthenticationScheme);

        var claimsPrincipal = new ClaimsPrincipal(claimsIdentity);

        var mockRequestCookies = new Mock<IRequestCookieCollection>();
        mockRequestCookies.Setup(x => x.Keys).Returns(new List<string> { "authCookie", "sessionCookie" });

        var mockResponseCookies = new Mock<IResponseCookies>();

        _mockDataProvider.Setup(dp => dp.User.GetUserByLoginName(loginName))
            .ReturnsAsync(user);

        var mockAuthService = new Mock<IAuthenticationService>();
        mockAuthService.Setup(a => a.SignOutAsync(
                It.IsAny<HttpContext>(),
                CookieAuthenticationDefaults.AuthenticationScheme,
                null))
            .Returns(Task.CompletedTask);

        var mockSession = new Mock<ISession>();
        mockSession.Setup(x => x.Clear()).Verifiable();

        var mockCookies = new Mock<IResponseCookies>();
        mockCookies.Setup(x => x.Delete(It.IsAny<string>())).Verifiable();

        var mockHttpContext = new Mock<HttpContext>();
        mockHttpContext.SetupGet(c => c.User).Returns(claimsPrincipal);
        mockHttpContext.Setup(x => x.Session).Returns(mockSession.Object);
        mockHttpContext.Setup(x => x.Response.Cookies).Returns(mockCookies.Object);
        mockHttpContext.SetupGet(c => c.User.Identity).Returns(claimsIdentity);

        var mockRequest = new Mock<HttpRequest>();
        mockRequest.Setup(x => x.Cookies).Returns(mockRequestCookies.Object);
        mockHttpContext.Setup(x => x.Request).Returns(mockRequest.Object);

        var mockResponse = new Mock<HttpResponse>();
        mockResponse.Setup(x => x.Cookies).Returns(mockResponseCookies.Object);
        mockHttpContext.Setup(x => x.Response).Returns(mockResponse.Object);


        var services = new ServiceCollection()
            .AddSingleton(mockAuthService.Object)
            .BuildServiceProvider();
        mockHttpContext.Setup(x => x.RequestServices).Returns(services);


        _mockDistributedCache.Setup(c => c.RemoveAsync(It.IsAny<string>(), CancellationToken.None))
            .Returns(Task.CompletedTask);
        ClearDatabaseSessionMock();

        _sut.ControllerContext = new ControllerContext
        {
            HttpContext = mockHttpContext.Object
        };

        _mockDistributedCache.Setup(c => c.RemoveAsync(It.IsAny<string>(), CancellationToken.None))
            .Returns(Task.CompletedTask);
        ClearDatabaseSessionMock();

        var result = await _sut.ClearSession(loginName) as JsonResult;

        result.ShouldNotBeNull();
        dynamic resultData = new JsonResultDynamicWrapper(result);
        bool success = resultData.Success;
        success.ShouldBeFalse();
    }




    //[Fact]
    //public async Task ClearSession_InvalidLoginName_ReturnsFailure()
    //{
    //    // Arrange
    //    var loginName = "invalidUser";
    //    _dataProviderMock.Setup(dp => dp.User.GetUserByLoginName(loginName)).ReturnsAsync((User)null);

    //    // Act
    //    var result = await _controller.ClearSession(loginName) as JsonResult;

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.False((bool)((dynamic)result.Value).Success);
    //}

    //[Fact]
    //public async Task ClearSession_NullOrEmptyLoginName_ThrowsException()
    //{
    //    // Arrange
    //    string loginName = null;

    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.ClearSession(loginName));
    //}

    //[Fact]
    //public async Task ClearSession_LoginInfoIdIsNullOrWhitespace_ReturnsFailure()
    //{
    //    // Arrange
    //    var loginName = "userWithWhitespaceId";
    //    var user = new User { Id = " " };
    //    _dataProviderMock.Setup(dp => dp.User.GetUserByLoginName(loginName)).ReturnsAsync(user);

    //    // Act
    //    var result = await _controller.ClearSession(loginName) as JsonResult;

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.False((bool)((dynamic)result.Value).Success);
    //}

    //[Fact]
    //public async Task ClearSession_CacheRemovedSuccessfully_ReturnsSuccess()
    //{
    //    // Arrange
    //    var loginName = "userWithCache";
    //    var user = new User { Id = "userId" };
    //    _dataProviderMock.Setup(dp => dp.User.GetUserByLoginName(loginName)).ReturnsAsync(user);
    //    _cacheMock.Setup(c => c.RemoveAsync(It.IsAny<string>())).Returns(Task.CompletedTask);

    //    // Act
    //    var result = await _controller.ClearSession(loginName) as JsonResult;

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.True((bool)((dynamic)result.Value).Success);
    //}

    //[Fact]
    //public async Task ClearSession_LoginViewModelNotFound_ReturnsSuccessWithoutLoginViewModel()
    //{
    //    // Arrange
    //    var loginName = "userWithoutLoginViewModel";
    //    var user = new User { Id = "userId" };
    //    _dataProviderMock.Setup(dp => dp.User.GetUserByLoginName(loginName)).ReturnsAsync(user);
    //    WebHelper.CurrentSession.Set("LoginViewModel" + loginName, null);

    //    // Act
    //    var result = await _controller.ClearSession(loginName) as JsonResult;

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.True((bool)((dynamic)result.Value).Success);
    //}

    //[Fact]
    //public async Task ClearSession_LoginViewModelFound_ReturnsSuccessWithLoginViewModel()
    //{
    //    // Arrange
    //    var loginName = "userWithLoginViewModel";
    //    var user = new User { Id = "userId" };
    //    var loginViewModel = new LoginViewModel { LoginName = loginName };
    //    _dataProviderMock.Setup(dp => dp.User.GetUserByLoginName(loginName)).ReturnsAsync(user);
    //    WebHelper.CurrentSession.Set("LoginViewModel" + loginName, loginViewModel);

    //    // Act
    //    var result = await _controller.ClearSession(loginName) as JsonResult;

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.True((bool)((dynamic)result.Value).Success);
    //    Assert.NotNull(((dynamic)result.Value).loginViewModel);
    //}
    #endregion

    #region HashPassword Method Tests

    [Fact]
    [Trait("Category", "AccountController_HashPassword")]
    public void HashPassword_ReturnsJsonResult_WithEncryptedPassword()
    {
        // Arrange
        var loginName = "testuser";
        var password = "testpassword";
        var adChecked = false;

        // Act
        var result = _sut.HashPassword(loginName, password, adChecked);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        dynamic resultData = new JsonResultDynamicWrapper(jsonResult);
        string encrypt = resultData.encrypt;
        Assert.NotNull(encrypt);
        Assert.NotEmpty(encrypt);
    }

    [Fact]
    [Trait("Category", "AccountController_HashPassword")]
    public void HashPassword_ReturnsJsonResult_WithEncryptedPassword_WhenAdChecked()
    {
        // Arrange
        var loginName = "testuser";
        var password = "testpassword";
        var adChecked = true;

        // Act
        var result = _sut.HashPassword(loginName, password, adChecked);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        dynamic resultData = new JsonResultDynamicWrapper(jsonResult);
        string encrypt = resultData.encrypt;
        Assert.NotNull(encrypt);
        Assert.NotEmpty(encrypt);
    }

    [Fact]
    [Trait("Category", "AccountController_HashPassword")]
    public void HashPassword_ReturnsEmptyEncrypt_WhenLoginNameIsNull()
    {
        // Arrange
        string? loginName = null;
        var password = "testpassword";
        var adChecked = false;

        // Act
        var result = _sut.HashPassword(loginName!, password, adChecked);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        dynamic resultData = new JsonResultDynamicWrapper(jsonResult);
        string encrypt = resultData.encrypt;
        Assert.Equal("", encrypt);
    }

    [Fact]
    [Trait("Category", "AccountController_HashPassword")]
    public void HashPassword_ReturnsEmptyEncrypt_WhenPasswordIsNull()
    {
        // Arrange
        var loginName = "testuser";
        string? password = null;
        var adChecked = false;

        // Act
        var result = _sut.HashPassword(loginName, password!, adChecked);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        dynamic resultData = new JsonResultDynamicWrapper(jsonResult);
        string encrypt = resultData.encrypt;
        Assert.Equal("", encrypt);
    }

    [Fact]
    [Trait("Category", "AccountController_HashPassword")]
    public void HashPassword_ReturnsEmptyEncrypt_WhenLoginNameIsWhitespace()
    {
        // Arrange
        var loginName = "   ";
        var password = "testpassword";
        var adChecked = false;

        // Act
        var result = _sut.HashPassword(loginName, password, adChecked);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        dynamic resultData = new JsonResultDynamicWrapper(jsonResult);
        string encrypt = resultData.encrypt;
        Assert.Equal("", encrypt);
    }

    [Fact]
    [Trait("Category", "AccountController_HashPassword")]
    public void HashPassword_ReturnsEmptyEncrypt_WhenPasswordIsWhitespace()
    {
        // Arrange
        var loginName = "testuser";
        var password = "   ";
        var adChecked = false;

        // Act
        var result = _sut.HashPassword(loginName, password, adChecked);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        dynamic resultData = new JsonResultDynamicWrapper(jsonResult);
        string encrypt = resultData.encrypt;
        Assert.Equal("", encrypt);
    }

    [Fact]
    [Trait("Category", "AccountController_HashPassword")]
    public void HashPassword_DifferentEncryption_ForAdAndInHouse()
    {
        // Arrange
        var loginName = "testuser";
        var password = "testpassword";

        // Act
        var adResult = _sut.HashPassword(loginName, password, true);
        var inHouseResult = _sut.HashPassword(loginName, password, false);

        // Assert
        var adJsonResult = Assert.IsType<JsonResult>(adResult);
        var inHouseJsonResult = Assert.IsType<JsonResult>(inHouseResult);

        dynamic adResultData = new JsonResultDynamicWrapper(adJsonResult);
        dynamic inHouseResultData = new JsonResultDynamicWrapper(inHouseJsonResult);

        string adEncrypt = adResultData.encrypt;
        string inHouseEncrypt = inHouseResultData.encrypt;

        Assert.NotEqual(adEncrypt, inHouseEncrypt);
    }

    #endregion

    #region GetDomains Method Tests

    [Fact]
    [Trait("Category", "AccountController_GetDomains")]
    public async Task GetDomains_ReturnsJsonResult_WithDomainList()
    {
        // Arrange
        var domainNames = new List<SelectListItem>
        {
            new("Domain1", "domain1.com"),
            new("Domain2", "domain2.com")
        };
        _mockDomainService.Setup(ds => ds.GetDomains()).ReturnsAsync(domainNames);

        // Act
        var result = await _sut.GetDomains();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        dynamic resultData = new JsonResultDynamicWrapper(jsonResult);
        var addomainlist = resultData.addomainlist;
        Assert.NotNull(addomainlist);
    }

    [Fact]
    [Trait("Category", "AccountController_GetDomains")]
    public async Task GetDomains_CallsDomainServiceGetDomains_OnlyOnce()
    {
        // Arrange
        var domainNames = new List<SelectListItem>();
        _mockDomainService.Setup(ds => ds.GetDomains()).ReturnsAsync(domainNames);

        // Act
        await _sut.GetDomains();

        // Assert
        _mockDomainService.Verify(x => x.GetDomains(), Times.Once);
    }

    [Fact]
    [Trait("Category", "AccountController_GetDomains")]
    public async Task GetDomains_SetsDomainsInSession_WhenDomainListIsNotEmpty()
    {
        // Arrange
        var domainNames = new List<SelectListItem>
        {
            new("Domain1", "domain1.com"),
            new("Domain2", "domain2.com")
        };
        _mockDomainService.Setup(ds => ds.GetDomains()).ReturnsAsync(domainNames);

        // Act
        await _sut.GetDomains();

        // Assert
        // Verify that domains were set in session (this would be tested through WebHelper.CurrentSession)
        _mockDomainService.Verify(x => x.GetDomains(), Times.Once);
    }

    [Fact]
    [Trait("Category", "AccountController_GetDomains")]
    public async Task GetDomains_HandlesException_ReturnsJsonWithEmptyDomainList()
    {
        // Arrange
        var exception = new Exception("Domain service error");
        _mockDomainService.Setup(ds => ds.GetDomains()).ThrowsAsync(exception);

        // Act
        var result = await _sut.GetDomains();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        dynamic resultData = new JsonResultDynamicWrapper(jsonResult);
        var addomainlist = resultData.addomainlist;
        Assert.NotNull(addomainlist);
    }

    #endregion

    #region GetADGroup Method Tests

    [Fact]
    [Trait("Category", "AccountController_GetADGroup")]
    public async Task GetADGroup_ReturnsJsonResult_WithSuccessAndData()
    {
        // Arrange
        var domainName = "testdomain.com";
        var groupNames = new List<string> { "Group1", "Group2", "Group3" };
        _mockDataProvider.Setup(dp => dp.User.GetDomainGroups(domainName, "")).ReturnsAsync(groupNames);

        // Act
        var result = await _sut.GetADGroup(domainName);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        dynamic resultData = new JsonResultDynamicWrapper(jsonResult);
        bool success = resultData.Success;
        var data = resultData.data;

        Assert.True(success);
        Assert.NotNull(data);
    }

    [Fact]
    [Trait("Category", "AccountController_GetADGroup")]
    public async Task GetADGroup_CallsDataProviderGetDomainGroups_OnlyOnce()
    {
        // Arrange
        var domainName = "testdomain.com";
        var groupNames = new List<string>();
        _mockDataProvider.Setup(dp => dp.User.GetDomainGroups(domainName, "")).ReturnsAsync(groupNames);

        // Act
        await _sut.GetADGroup(domainName);

        // Assert
        _mockDataProvider.Verify(x => x.User.GetDomainGroups(domainName, ""), Times.Once);
    }

    [Fact]
    [Trait("Category", "AccountController_GetADGroup")]
    public async Task GetADGroup_HandlesException_ReturnsJsonException()
    {
        // Arrange
        var domainName = "testdomain.com";
        var exception = new Exception("AD service error");
        _mockDataProvider.Setup(dp => dp.User.GetDomainGroups(domainName, "")).ThrowsAsync(exception);

        // Act
        var result = await _sut.GetADGroup(domainName);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);
    }

    #endregion

    #region WriteLogAjaxError Method Tests

    [Fact]
    [Trait("Category", "AccountController_WriteLogAjaxError")]
    public void WriteLogAjaxError_ReturnsJsonResult_WithSuccess()
    {
        // Arrange
        var message = "Test error message";
        var url = "/test/url";

        // Act
        var result = _sut.WriteLogAjaxError(message, url);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        dynamic resultData = new JsonResultDynamicWrapper(jsonResult);
        bool success = resultData.Success;
        Assert.True(success);
    }

    [Fact]
    [Trait("Category", "AccountController_WriteLogAjaxError")]
    public void WriteLogAjaxError_AcceptsNullMessage()
    {
        // Arrange
        string? message = null;
        var url = "/test/url";

        // Act
        var result = _sut.WriteLogAjaxError(message!, url);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        dynamic resultData = new JsonResultDynamicWrapper(jsonResult);
        bool success = resultData.Success;
        Assert.True(success);
    }

    [Fact]
    [Trait("Category", "AccountController_WriteLogAjaxError")]
    public void WriteLogAjaxError_AcceptsNullUrl()
    {
        // Arrange
        var message = "Test error message";
        string? url = null;

        // Act
        var result = _sut.WriteLogAjaxError(message, url!);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        dynamic resultData = new JsonResultDynamicWrapper(jsonResult);
        bool success = resultData.Success;
        Assert.True(success);
    }

    #endregion

    #region About Method Tests

    [Fact]
    [Trait("Category", "AccountController_About")]
    public async Task About_ReturnsViewResult_WithAboutListVm()
    {
        // Arrange
        var aboutListVm = new List<GetAboutCpListVm>
        {
            new() { Id = "1", ProductVersion = "1.0", LicenseType = "Enterprise" },
            new() { Id = "2", ProductVersion = "1.1", LicenseType = "Subscription" }
        };
        _mockDataProvider.Setup(dp => dp.AboutCP.GetAboutCpList()).ReturnsAsync(aboutListVm);

        // Act
        var result = await _sut.About();

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Equal(aboutListVm, viewResult.Model);
    }

    [Fact]
    [Trait("Category", "AccountController_About")]
    public async Task About_CallsDataProviderGetAboutCpList_OnlyOnce()
    {
        // Arrange
        var aboutListVm = new List<GetAboutCpListVm>();
        _mockDataProvider.Setup(dp => dp.AboutCP.GetAboutCpList()).ReturnsAsync(aboutListVm);

        // Act
        await _sut.About();

        // Assert
        _mockDataProvider.Verify(x => x.AboutCP.GetAboutCpList(), Times.Once);
    }

    [Fact]
    [Trait("Category", "AccountController_About")]
    public async Task About_HandlesException_ReturnsJsonResult()
    {
        // Arrange
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.AboutCP.GetAboutCpList()).ThrowsAsync(exception);

        // Act
        var result = await _sut.About();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);

        dynamic resultData = new JsonResultDynamicWrapper(jsonResult);
        bool success = resultData.success;
        string message = resultData.message;

        Assert.False(success);
        Assert.Equal("Database error", message);
    }

    #endregion

}