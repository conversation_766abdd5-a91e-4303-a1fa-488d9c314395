﻿using ContinuityPatrol.Infrastructure.Contract;

namespace ContinuityPatrol.Application.Features.Database.Commands.SaveAs;

public class SaveAsDatabaseCommandValidator : AbstractValidator<SaveAsDatabaseCommand>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILicenseValidationService _licenseValidationService;
    private readonly IServerRepository _serverRepository;
    private readonly ISiteRepository _siteRepository;
    private readonly ISiteTypeRepository _siteTypeRepository;
    public Domain.Entities.LicenseManager licenseDtl = null;
    public Domain.Entities.Database dataBaseDtl=null ;

    public SaveAsDatabaseCommandValidator(IDatabaseRepository databaseRepository, IServerRepository serverRepository,
        ISiteRepository siteRepository, ILicenseValidationService licenseValidationService,
        ILicenseManagerRepository licenseManagerRepository, ISiteTypeRepository siteTypeRepository)
    {
        _databaseRepository = databaseRepository;
        _serverRepository = serverRepository;
        _siteRepository = siteRepository;
        _licenseValidationService = licenseValidationService;
        _licenseManagerRepository = licenseManagerRepository;
        _siteTypeRepository = siteTypeRepository;


        RuleFor(p => p.DatabaseId)
            .NotEmpty().WithMessage("{PropertyName} is required.").NotNull()
            .WithMessage("{PropertyName} cannot be null.")
            .MustAsync(async (DatabaseId, _) => await GetDataBaseDetailAsync(DatabaseId))
            .WithMessage("database detail was not found.");

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z\d]+[_\s]?)([a-zA-Z\d]+[_\s-])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p)
          .MustAsync(IsLicenseActiveAsync)
          .WithMessage("License is in 'InActive' state.");

        RuleFor(p => p)
            .MustAsync(IsLicenseExpiredAsync)
            .WithMessage("The license key has expired.");


        RuleFor(p => p)
            .MustAsync(ValidateLicenseCountAsync)
            .WithMessage("Database count reached maximum limit.");

    }

    private async Task<bool> GetDataBaseDetailAsync(string serverId)
    {
        var database = await _databaseRepository.GetByReferenceIdAsync(serverId);

        dataBaseDtl = database ?? throw new InvalidException("Database detail was not found.");

        return true;
    }
    private async Task<bool> IsLicenseActiveAsync(SaveAsDatabaseCommand p, CancellationToken cancellationToken)
    {
        licenseDtl = await _licenseManagerRepository.GetLicenseDetailByIdAsync(dataBaseDtl?.LicenseId) ??
                         throw new InvalidException("License detail was not found.");

        return licenseDtl.IsState;
    }
    private async Task<bool> IsLicenseExpiredAsync(SaveAsDatabaseCommand p, CancellationToken cancellationToken)
    {
        return await _licenseValidationService.IsLicenseExpired(licenseDtl.ExpiryDate);
    }


    private async Task<bool> ValidateLicenseCountAsync(SaveAsDatabaseCommand p, CancellationToken cancellationToken)
    {
        var server = await _serverRepository.GetByReferenceIdAsync(dataBaseDtl.ServerId);

        Guard.Against.NullOrDeactive(server, nameof(Domain.Entities.Server),
            new NotFoundException(nameof(Domain.Entities.Server), dataBaseDtl.ServerId));

        var site = await _siteRepository.GetByReferenceIdAsync(server.SiteId);

        Guard.Against.NullOrDeactive(site, nameof(Domain.Entities.Site),
            new NotFoundException(nameof(Domain.Entities.Site), server.SiteId));

        var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

        Guard.Against.NullOrDeactive(siteType, nameof(Domain.Entities.SiteType),
            new NotFoundException(nameof(Domain.Entities.SiteType), site.TypeId));

        var index = await _siteTypeRepository.GetSiteTypeIndexByIdAsync(siteType.ReferenceId);

        var siteVm = await _siteRepository.GetSiteBySiteTypeId(siteType.ReferenceId);

        var siteIds = siteVm.Select(x => x.ReferenceId).ToList();

        var databaseCount = await _databaseRepository.GetDatabaseCountByLicenseKey(dataBaseDtl.LicenseId, siteIds);

        var license=
            await _licenseValidationService.IsDatabaseLicenseCountExitMaxLimit(licenseDtl, siteType, databaseCount,
                index);

        return license;
    }

}