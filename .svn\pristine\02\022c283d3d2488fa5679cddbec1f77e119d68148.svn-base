﻿@using ContinuityPatrol.Application.Features.User.Commands.UserLock;
@using ContinuityPatrol.Application.Features.LicenseManager.Command.Replace;
@using ContinuityPatrol.Shared.Core.Helper
@using Newtonsoft.Json;
@model ContinuityPatrol.Domain.ViewModels.LicenseManagerModel.BaseLicenseViewModel

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()

<link href="~/css/timeline.css" rel="stylesheet" />
<style>

    .license-dropdown .dropdown-toggle::after {
        display: none; /* Removes the dropdown indicator arrow */
    }

    .dropdown:hover .licenseChild-dropdown-menu-end {
        display: block;
        margin-top: 52px;
        max-height: 184px;
        overflow-y: auto;
    }

    .dropdown:hover .license-dropdown-menu-end {
        display: block;
        right: 100%;
        left: 100%;
    }
    .text-indent {
        display: inline-block;
        vertical-align: middle;
    }
    .badge-light-success {
        color: #003600;
        background-color: #E0FFE2;
    }
    .tooltip-inner {
        max-width: 500px !important;
        padding: 8px;
        color: #000000;
        text-align: left;
        text-decoration: none;
        background-color: #fff;
        border-radius: 10px;
        -webkit-box-shadow: 3px 7px 16px 0px rgba(224,201,224,1);
        -moz-box-shadow: 3px 7px 16px 0px rgba(224,201,224,1);
        box-shadow: 3px 7px 16px 0px rgba(224,201,224,1);
    }
    .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before, .bs-tooltip-start .tooltip-arrow::before {
        border-left-color: #fff;
    }
</style>

<div class="page-content" id="">
    <div class="row g-2">
        <div class="col-12 header">
            <h6 class="page_title">
                <i class="cp-license-manager"></i><span>
                    License Manager
                </span>
            </h6>
            <form class="d-flex align-items-end tablecheck gap-2">
                <button id="BtnLicenseDownload" type="button" title="Download" class="btn btn-primary btn-sm"><i class="cp-download"></i></button>
                @if (ContinuityPatrol.Shared.Services.Helper.WebHelper.UserSession.IsParent == true)
                {
                    <button type="button" class="btn btn-primary btn-sm btn-Create" data-bs-toggle="modal"
                            data-bs-target="#CreateModal">
                        <i class="cp-add me-1"></i>Add
                    </button>
                }
                else
                {
                    <button type="button" class="btn-disabled" hidden
                            title="Add">
                        <i class="cp-add me-1"></i>Add
                    </button>
                }
            </form>
        </div>
    </div>
    <div class="row g-2 mt-0">
        <div class="col-xxl-2 col-xl-2 col-lg-2 col-md-2 col-12">
            <div class="card Card_Design_None ">
                <div class="card-header py-0">
                    <div id="LicenseChart"></div>
                </div>
                <div class="card-body p-2 pt-0" id="chartdata" style="height:calc(100vh - 254px); overflow-y:auto;">
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card Card_Design_None ">
                <div class="card-header p-0">
                    <div class="list-group list-group-horizontal fw-semibold">
                        <div class="border-0 p-2 text-center list-group-item" style="width: 5%;">Sr.No.</div>
                        <div class="border-0 w-25 flex-fill p-2 list-group-item">PO Number</div>
                        <div class="border-0 w-25 flex-fill p-2 list-group-item">License Type</div>
                        <div class="border-0 w-25 flex-fill p-2 list-group-item">License Validity</div>
                        <div class="border-0 w-25 flex-fill p-2 list-group-item">License Count</div>
                        <div class="border-0 rounded-0 p-2 list-group-item" style="width: 14%;">Action</div>
                    </div>
                </div>
                <div class="card-body p-0 defaultloading" style="height: calc(100vh - 139px); overflow-y: auto;">
                    @{
                        int i = 1;
                    }
                    <div id="myGroup">
                       
                        @foreach (var license in Model.LicenseManagerDetails)
                        {
                            <div class="align-items-center  border-top rounded-0 list-group list-group-horizontal" style="border-color: #dee2e6!important;">
                                <div class="rounded-0 p-2 text-center border-0 list-group-item" style="width: 5%;">
                                    <span>@i</span>
                                </div>
                                @{
                                    if (license.RenewalDate != "" && license.RenewalDate != null)
                                    {
                                        <div class="w-25 flex-fill p-2 border-0 list-group-item loadPartialViewButton commonPoDetail license_manager_collapse group fw-semibold" role="button" id="loadPartialViewButton"
                                             data-bs-toggle="collapse" href="#collapseExample@(i)" aria-expanded="false" data-derived-name="Derived"
                                             aria-controls="collapseExample" data-table-name="data@(i)" data-parent-id="@license.CompanyId" data-parent-ponumber="@license.PoNumber">

                                            <div class="dropdown">
                                                <div class="btn-group dropend">
                                                    <span id="dropdownMenuButton" data-bs-toggle="dropdown license-dropdown" aria-expanded="false">
                                                        @license.CompanyName (@license.PoNumber)
                                                    </span>
                                                    <ul class="dropdown-menu license-dropdown-menu-end p-2" aria-labelledby="dropdownMenuButton" style="width: 20rem;">
                                                        <li>
                                                            <div class="table-responsive">
                                                                <table class="table table-sm table-borderless fs-7 w-100">
                                                                    <tbody>
                                                                    <tr>
                                                                        <th>License Type</th>
                                                                        <td>:</td>
                                                                        <td>@license.Validity</td>
                                                                    </tr>
                                                                     <tr>
                                                                        <th>Create Date</th>
                                                                        <td>:</td>
                                                                        <td>@license.CreatedDate</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th>Renewal Date</th>
                                                                        <td>:</td>
                                                                        <td>@license.RenewalDate</td>
                                                                    </tr>
                                                                   
                                                                    <tr>
                                                                        <th>Expiry Date</th>
                                                                        <td>:</td>
                                                                        <td>@license.ExpiryDate</td>
                                                                    </tr>
                                                                    @if (license.IsWarranty)
                                                                    {
                                                                        <tr>
                                                                            <th>Warranty Plan</th>
                                                                            <td>:</td>
                                                                            <td>@($"{license.WarrantyPlan} Year")</td>
                                                                        </tr>
                                                                        <tr>
                                                                            <th>Warranty End Date</th>
                                                                            <td>:</td>
                                                                            <td>@license.WarrantyEndDate</td>
                                                                        </tr>
                                                                    }
                                                                    @if (license.IsAmc)
                                                                    {
                                                                        <tr>
                                                                            <th>AMC Start Date</th>
                                                                            <td>:</td>
                                                                            <td>@license.AmcStartDate</td>
                                                                        </tr>
                                                                        <tr>
                                                                            <th>AMC End Date</th>
                                                                            <td>:</td>
                                                                            <td>@license.AmcEndDate</td>
                                                                        </tr>
                                                                    }

                                                                    <tr>
                                                                        <th>Sites count</th>
                                                                        <td>:</td>
                                                                        <td>@license.SiteCount</td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>

                                            </div>




                                            @if (license.IsAmc)
                                            {
                                                <span class="badge-light-success fw-normal badge mx-2">AMC Applied</span>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="w-25 flex-fill p-2 border-0 list-group-item loadPartialViewButton commonPoDetail license_manager_collapse group fw-semibold" role="button" id="loadPartialViewButton"
                                             data-bs-toggle="collapse"  href="#collapseExample@(i)" aria-expanded="false" data-derived-name="Derived"
                                             aria-controls="collapseExample" data-table-name="data@(i)" data-parent-id="@license.CompanyId" data-parent-ponumber="@license.PoNumber">

                                             <div class="dropdown">
                                                <div class="btn-group dropend">
                                                    <span id="dropdownMenuButton" data-bs-toggle="dropdown license-dropdown" aria-expanded="false">
                                                        @license.CompanyName (@license.PoNumber)
                                                    </span>
                                                    <ul class="dropdown-menu license-dropdown-menu-end p-2" aria-labelledby="dropdownMenuButton" style="width: 20rem;">
                                                        <li>
                                                            <div class="table-responsive">
                                                                <table class="table table-sm table-borderless fs-7 w-100">
                                                                    <tbody>
                                                                    <tr>
                                                                        <th>License Type</th>
                                                                        <td>:</td>
                                                                        <td>@license.Validity</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th>Create Date</th>
                                                                        <td>:</td>
                                                                        <td>@license.CreatedDate</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th>Expiry Date</th>
                                                                        <td>:</td>
                                                                        <td>@license.ExpiryDate</td>
                                                                    </tr>
                                                                    @if (license.IsWarranty)
                                                                    {
                                                                        <tr>
                                                                            <th>Warranty Plan</th>
                                                                            <td>:</td>
                                                                            <td>@($"{license.WarrantyPlan} Year")</td>
                                                                        </tr>
                                                                        <tr>
                                                                            <th>Warranty End Date</th>
                                                                            <td>:</td>
                                                                            <td>@license.WarrantyEndDate</td>
                                                                        </tr>
                                                                    }
                                                                    @if (license.IsAmc)
                                                                    {
                                                                        <tr>
                                                                            <th>AMC Start Date</th>
                                                                            <td>:</td>
                                                                            <td>@license.AmcStartDate</td>
                                                                        </tr>
                                                                        <tr>
                                                                            <th>AMC End Date</th>
                                                                            <td>:</td>
                                                                            <td>@license.AmcEndDate</td>
                                                                        </tr>
                                                                    }

                                                                    <tr>
                                                                        <th>Sites count</th>
                                                                        <td>:</td>
                                                                        <td>@license.SiteCount</td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>

                                            </div>

                                            @if (license.IsAmc)
                                            {
                                                <span class="badge-light-success fw-normal badge mx-2">AMC Applied</span>
                                            }
                                        </div>
                                    }
                                }
                                <div class="w-25 flex-fill p-2 border-0 list-group-item loadPartialViewButton license_manager_collapse group text-light">
                                    @license.Validity
                                </div>
                                @{
                                    var expired = license.IsLicenseExpiry == true ? license.ExpiryDate : "Expired";
                                }
                                <div class="w-25 flex-fill p-2 border-0 list-group-item loadPartialViewButton license_manager_collapse group text-light">
                                    @if (expired == "Expired")

                                    {
                                        <span class="mx-2 align-middle bg-danger-subtle text-danger badge">Expired</span>
                                     
                                    }
                                    else
                                    {
                                        <div class="text-light">@expired</div>
                                    }
                                </div>
                                <div class="w-25 flex-fill p-2 border-0  list-group-item">
                                    <div class="d-flex gap-2">
                                        <div class="w-100">
                                            <div class="progress" aria-label="Animated striped" role="progressbar" style="height:12px">
                                                @{
                                                    var total = license.BaseLicenseCountVm.TotalNotUsedCount;
                                                }
                                                @{
                                                    var totalProgress = license.BaseLicenseCountVm.TotalUsedCount;
                                                }
                                                @{
                                                    var remaining = license.BaseLicenseCountVm.DerivedTotalCount;
                                                }
                                                @{
                                                    var totalSum = total + totalProgress + remaining;
                                                }
                                                @if (expired == "Expired")
                                                {
                                                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-danger" role="button" title="@(totalProgress) License Used" style="width: @(totalProgress >= 1 ? (totalProgress * 100.0 / totalSum) : 0)%;" aria-valuenow="@totalProgress" aria-valuemin="0" aria-valuemax="@totalSum">

                                                        <div style="color: white; font-size: 10px;">@totalProgress</div>
                                                    </div>
                                                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-danger" role="button" title="@(remaining) Derived Used" style="width: @(remaining >= 1 ? (remaining * 100.0 / totalSum) : 0)%;" aria-valuenow="@remaining" aria-valuemin="0" aria-valuemax="@totalSum">
                                                        <div style="color: white; font-size: 10px;">@remaining</div>
                                                    </div>
                                                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-danger" role="button" title="@(total) Available" style="width: @(total >= 1 ? (total * 100.0 / totalSum) : 0)%;" aria-valuenow="@total" aria-valuemin="0" aria-valuemax="@totalSum">
                                                        <div style="color: white; font-size: 10px;">@total</div>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="button" title="@(totalProgress) License Used" style="width: @(totalProgress >= 1 ? (totalProgress * 100.0 / totalSum) : 0)%;background-color:#4cb9e7" aria-valuenow="@totalProgress" aria-valuemin="0" aria-valuemax="@totalSum">

                                                        <div style="color: white; font-size: 10px;">@totalProgress</div>
                                                    </div>
                                                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="button" title="@(remaining) Derived Used" style="width: @(remaining >= 1 ? (remaining * 100.0 / totalSum) : 0)%;background-color:#fbb50a" aria-valuenow="@remaining" aria-valuemin="0" aria-valuemax="@totalSum">
                                                        <div style="color: white; font-size: 10px;">@remaining</div>
                                                    </div>
                                                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="button" title="@(total) Available" style="width: @(total >= 1 ? (total * 100.0 / totalSum) : 0)%;background-color:#60a843" aria-valuenow="@total" aria-valuemin="0" aria-valuemax="@totalSum">
                                                        <div style="color: white; font-size: 10px;">@total</div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                        <div class="fw-semibold text-truncate" style="min-width: 34px;" title="@license.BaseLicenseCountVm.TotalCount">@license.BaseLicenseCountVm.TotalCount</div>
                                    </div>
                                </div>
                                <div class="rounded-0 p-2 text-center border-0 list-group-item" style="width: 14%;">
                                    <div class="d-flex align-items-center gap-2">
                                        <span title="LicenseRequest" role="button" id="requestlicense" data-licenserequest='@Json.Serialize(license)' data-bs-toggle="modal" data-bs-target="#LicenseRequest">
                                            <i class="cp-license-request"></i>
                                        </span>
                                        <span type="button" class="active_inactive"
                                              data-licensestate='@Json.Serialize(license)'>
                                            @{
                                                if (license.IsState == true)
                                                {
                                                    <i class="cp-active-inactive text-success fw-semibold  " title="Active"></i>
                                                }
                                                else
                                                {
                                                    <i class="cp-active-inactive text-danger fw-semibold  " title="InActive"></i>
                                                }
                                            }

                                        </span>
                                        @if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.CreateAndEdit == true && ContinuityPatrol.Shared.Services.Helper.WebHelper.UserSession.IsParent == true)
                                        {
                                            <span class="edit-button" title="Upgrade" href="#" id="edit-button@(i)" data-license='@Json.Serialize(license)'><i class="cp-upgrade"></i></span>
                                        }
                                        else
                                        {
                                            <span class="icon-disabled" title="Upgrade" hidden><i class="cp-edit"></i></span>
                                        }
                                        @if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.Delete && ContinuityPatrol.Shared.Services.Helper.WebHelper.UserSession.IsParent == true)
                                        {
                                            <span role="button" title="Delete" id="delete@(i)" class="delete-button" data-license-id="@license.Id" data-license-name="@license.PoNumber" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                        }
                                        else
                                        {
                                            <span role="button" title="Delete" class="icon-disabled" hidden>
                                                <i class="cp-Delete"></i>
                                            </span>
                                        }
                                        @{
                                            var isParent = ContinuityPatrol.Shared.Services.Helper.WebHelper.UserSession.IsParent;
                                            var isNotPOC = !license.Validity.Contains("POC");
                                            var isNotExpired = expired != "Expired";
                                        }
                                        @if (isParent && (isNotPOC && isNotExpired))
                                        {
                                            <span id="derived@(i)" title="Derived" class="derivedbase" role="button" data-bs-toggle="modal" data-bs-target="#DerivedLicense" data-baselicense='@Json.Serialize(license)' data-licensehelper="@SecurityHelper.Decrypt(license.LicenseKey)" data-license-id="@license.Id" data-license-key="@license.LicenseKey">
                                                <i class="cp-derived-licence"></i>
                                            </span>
                                        }
                                        else
                                        {
                                            <span role="button" title="Derived" class="btn-disabled" style="pointer-events:none; opacity:0">
                                                <i class="cp-derived-licence"></i>
                                            </span>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-group ps-5">
                                <div class="child_Accordion collapse list-group indent childgroup" id="collapseExample@(i)" data-bs-parent="#myGroup" data-hotspot="1">
                                    <div class="License_Nav nav nav-tabs nav-justified parentcollapse" role="tablist" id="myTab@(i)" style="z-index:1">
                                       <div class="nav-item">
                                            <a class="nav-link license navgroup" id="nav-application-tab@(i)" role="presentation" data-bs-toggle="tab"
                                               data-bs-target="#navChildTab@(i)" type="button" role="tab"
                                               aria-controls="nav-application@(i)" aria-selected="false" data-parentEntity-id="collapsedserver@(i)" data-entity-id="@license.Id" data-entity-name="server" data-entity-type="application">
                                                <div class="mb-0 text-start card dropdown">
                                                    <div class="card-body p-2" data-bs-toggle="dropdown license-dropdown" aria-expanded="false">
                                                        <div class="d-flex align-items-center">
                                                            <i class="cp-application fs-4 license_application"></i>
                                                            <div class="ms-2 d-grid">
                                                                @{
                                                                    var applicationcount = license.BaseLicenseCountVm.ApplicationUsedCount + license.BaseLicenseCountVm.DerivedApplicationAvailableCount;
                                                                    var applicationli = SecurityHelper.Decrypt(license.LicenseKey);
                                                                    var applicationLicenseKeyDetail = applicationli.Split('*');
                                                                    var applicationvalues = applicationLicenseKeyDetail[4];
                                                                    dynamic applicationjson = JsonConvert.DeserializeObject(applicationvalues);
                                                                    var applicationdb = applicationjson.primaryapplicationCount;
                                                                    var applicationcountused = license.BaseLicenseCountVm.ApplicationUsedCount;
                                                                    var applicationderivedcountused = license.BaseLicenseCountVm.DerivedApplicationAvailableCount;
                                                                }
                                                                <span class="fw-semibold"> @applicationcount / @applicationdb</span>
                                                                <small class="text-light">Application</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <ul class="dropdown-menu licenseChild-dropdown-menu-end p-2" aria-labelledby="dropdownMenuButton" style="width:8rem;">
                                                        <li>
                                                            <div class="table-responsive" style=" height: 60px;overflow: auto;">
                                                                <table class="table table-sm fs-7 w-100 mb-0">
                                                                    <tbody>
                                                                        <tr><td>Parent Utilize : @applicationcountused</td> </tr>
                                                                        <tr><td>Child Utilize : @applicationderivedcountused</td></tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="nav-item">
                                            <a class="nav-link license navgroup" id="nav-database-tab@(i)" role="presentation" data-bs-toggle="tab"
                                               data-bs-target="#navChildTab@(i)" type="button" role="tab"
                                               aria-controls="nav-database@(i)" aria-selected="true" data-parentEntity-id="collapsedserver@(i)" data-entity-id="@license.Id" data-entity-name="Database">
                                                <div class="mb-0 text-start card dropdown">
                                                    <div class="card-body p-2" data-bs-toggle="dropdown license-dropdown" aria-expanded="false">
                                                        <div class="d-flex align-items-center">
                                                            <i class="cp-database fs-4 license_database"></i>
                                                            <div class="ms-2 d-grid">
                                                                @{
                                                                    var databasecount = license.BaseLicenseCountVm.DatabaseUsedCount + license.BaseLicenseCountVm.DerivedDatabaseAvailableCount;
                                                                    var databaseli = SecurityHelper.Decrypt(license.LicenseKey);
                                                                    var databaseLicenseKeyDetail = databaseli.Split('*');
                                                                    var databasevalues = databaseLicenseKeyDetail[4];
                                                                    var databsedto = license.BaseLicenseCountVm?.DatabaseDto?.Databases;
                                                                    dynamic databasejson = JsonConvert.DeserializeObject(databasevalues);
                                                                    var databasedb = databasejson.primarydatabaseCount;
                                                                    var databasecountused = license.BaseLicenseCountVm.DatabaseUsedCount;
                                                                    var databasederivedcountused = license.BaseLicenseCountVm.DerivedDatabaseAvailableCount;
                                                                    
                                                                }
                                                                <span class="fw-semibold" >@databasecount / @databasedb</span>
                                                                <small class="text-light">Database</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <ul class="dropdown-menu licenseChild-dropdown-menu-end p-2" aria-labelledby="dropdownMenuButton" style="width: 8rem;">
                                                        <li>
                                                            <div class="table-responsive" style=" height: 90px;overflow: auto;">
                                                                <table class="table table-sm fs-7 w-100 mb-0">
                                                                    <tbody>
                                                                        <tr><td>Parent Utilize : @databasecountused</td> </tr>
                                                                        <tr><td>Child Utilize : @databasederivedcountused</td></tr>
                                                                        @if(databsedto != null){
                                                                            @foreach (var d in databsedto)
                                                                            {
                                                                                if (d.Key.ToLower() == "primary")
                                                                                {
                                                                                    @foreach (var data in d.Value)
                                                                                    {

                                                                                        var logo = @data.Logo == null ? "cp-others" : @data.Logo;
                                                                                        <tr>
                                                                                            <td><i class="me-2 @logo "></i>@data.Type (@data.Count)</td>
                                                                                        </tr>
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="nav-item">
                                            <a class="nav-link license navgroup" id="nav-replication-tab@(i)" role="presentation" data-bs-toggle="tab"
                                               data-bs-target="#navChildTab@(i)" type="button" role="tab"
                                               aria-controls="nav-replication@(i)" aria-selected="false" data-parentEntity-id="collapsedserver@(i)" data-entity-id="@license.Id" data-entity-name="Replication">
                                                <div class="mb-0 text-start card dropdown">
                                                    <div class="card-body p-2" data-bs-toggle="dropdown license-dropdown" aria-expanded="false">
                                                        <div class="d-flex align-items-center">
                                                            <i class="cp-replication-on fs-4 license_replication"></i>
                                                            <div class="ms-2 d-grid">
                                                                @{
                                                                    var replicationcount = license.BaseLicenseCountVm.ReplicationUsedCount + license.BaseLicenseCountVm.DerivedReplicationAvailableCount;
                                                                    var replicationli = SecurityHelper.Decrypt(license.LicenseKey);
                                                                    var replicationLicenseKeyDetail = replicationli.Split('*');
                                                                    var replicationvalues = replicationLicenseKeyDetail[4];
                                                                    dynamic replicationjson = JsonConvert.DeserializeObject(replicationvalues);
                                                                    var replicationdb = replicationjson.primaryreplicationCount;
                                                                    var replicationcountused = license.BaseLicenseCountVm.ReplicationUsedCount;
                                                                    var replicatioderivedcountused = license.BaseLicenseCountVm.DerivedReplicationAvailableCount;
                                                                }
                                                                <span class="fw-semibold" >@replicationcount / @replicationdb</span>
                                                                <small class="text-light">Replication</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <ul class="dropdown-menu licenseChild-dropdown-menu-end p-2" aria-labelledby="dropdownMenuButton" style="width:8rem;">
                                                        <li>
                                                            <div class="table-responsive" style=" height: 60px;overflow: auto;">
                                                                <table class="table table-sm fs-7 w-100 mb-0">
                                                                    <tbody>
                                                                        <tr><td>Parent Utilize : @replicationcountused</td> </tr>
                                                                        <tr><td>Child Utilize : @replicatioderivedcountused</td></tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="nav-item">
                                            <a class="nav-link license navgroup" id="nav-network-tab@(i)" role="presentation" data-bs-toggle="tab"
                                               data-bs-target="#navChildTab@(i)" type="button" role="tab"
                                               aria-controls="nav-network@(i)" aria-selected="false" data-parentEntity-id="collapsedserver@(i)" data-entity-id="@license.Id" data-entity-name="server" data-entity-type="network">
                                                <div class="mb-0 text-start card dropdown">
                                                    <div class="card-body p-2" data-bs-toggle="dropdown license-dropdown" aria-expanded="false">
                                                        <div class="d-flex align-items-center">
                                                            <i class="cp-network fs-4 license_network"></i>
                                                            <div class="ms-2 d-grid">
                                                                @{
                                                                    var networkcount = license.BaseLicenseCountVm.NetworkUsedCount + license.BaseLicenseCountVm.DerivedNetworkAvailableCount;
                                                                    var networkli = SecurityHelper.Decrypt(license.LicenseKey);
                                                                    var networkLicenseKeyDetail = networkli.Split('*');
                                                                    var networkvalues = networkLicenseKeyDetail[4];
                                                                    dynamic networkjson = JsonConvert.DeserializeObject(networkvalues);
                                                                    var networkdb = networkjson.primarynetworkCount;
                                                                    var networkcountused = license.BaseLicenseCountVm.NetworkUsedCount;
                                                                    var networkderivedcountused = license.BaseLicenseCountVm.DerivedNetworkAvailableCount;
                                                                }
                                                                <span class="fw-semibold" >@networkcount / @networkdb</span>
                                                                <small class="text-light">Network</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <ul class="dropdown-menu licenseChild-dropdown-menu-end p-2" aria-labelledby="dropdownMenuButton" style="width:8rem;">
                                                        <li>
                                                            <div class="table-responsive" style=" height: 60px;overflow: auto;">
                                                                <table class="table table-sm fs-7 w-100 mb-0">
                                                                    <tbody>
                                                                        <tr><td>Parent Utilize : @networkcountused</td> </tr>
                                                                        <tr><td>Child Utilize : @networkderivedcountused</td></tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="nav-item">
                                            <a class="nav-link license navgroup" id="nav-stroage-tab@(i)" role="presentation" data-bs-toggle="tab" data-bs-target="#navChildTab@(i)" type="button" role="tab"
                                               aria-controls="nav-stroage@(i)" aria-selected="false" data-parentEntity-id="collapsedserver@(i)" data-entity-id="@license.Id" data-entity-name="server" data-entity-type="storage">
                                                <div class="mb-0 text-start card dropdown">
                                                    <div class="card-body p-2" data-bs-toggle="dropdown license-dropdown" aria-expanded="false">
                                                        <div class="d-flex align-items-center">
                                                            <i class="cp-storage fs-4 license_stroage"></i>
                                                            <div class="ms-2 d-grid">
                                                                @{
                                                                    var storagecount = license.BaseLicenseCountVm.StorageUsedCount + license.BaseLicenseCountVm.DerivedStorageAvailableCount;
                                                                    var storageli = SecurityHelper.Decrypt(license.LicenseKey);
                                                                    var storageLicenseKeyDetail = storageli.Split('*');
                                                                    var storagevalues = storageLicenseKeyDetail[4];
                                                                    dynamic storagejson = JsonConvert.DeserializeObject(storagevalues);
                                                                    var storagedb = storagejson.primarystorageCount;
                                                                    var storagecountused = license.BaseLicenseCountVm.StorageUsedCount;
                                                                    var storagederivedcountused = license.BaseLicenseCountVm.DerivedStorageAvailableCount;
                                                                }
                                                                <span class="fw-semibold" >@storagecount / @storagedb</span>
                                                                <small class="text-light">Storage</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <ul class="dropdown-menu licenseChild-dropdown-menu-end p-2" aria-labelledby="dropdownMenuButton" style="width:8rem;">
                                                        <li>
                                                            <div class="table-responsive" style=" height: 60px;overflow: auto;">
                                                                <table class="table table-sm fs-7 w-100 mb-0">
                                                                    <tbody>
                                                                        <tr><td>Parent Utilize : @storagecountused</td> </tr>
                                                                        <tr><td>Child Utilize : @storagederivedcountused</td></tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="nav-item">
                                            <a class="nav-link license navgroup" id="nav-virtual-tab@(i)" role="presentation" data-bs-toggle="tab"
                                               data-bs-target="#navChildTab@(i)" type="button" role="tab" aria-controls="nav-virtual@(i)"
                                               aria-selected="false" data-parentEntity-id="collapsedserver@(i)" data-entity-id="@license.Id" data-entity-name="server" data-entity-type="virtualization">
                                                <div class="mb-0 text-start card dropdown">
                                                    <div class="card-body p-2" data-bs-toggle="dropdown license-dropdown" aria-expanded="false">
                                                        <div class="d-flex align-items-center">
                                                            <i class="cp-virtualization fs-4 license_virtualization"></i>
                                                            <div class="ms-2 d-grid">
                                                                @{
                                                                    var virtualizationcount = license.BaseLicenseCountVm.VirtualizationUsedCount + license.BaseLicenseCountVm.DerivedVirtualizationAvailableCount;
                                                                    var virtualizationli = SecurityHelper.Decrypt(license.LicenseKey);
                                                                    var virtualizationLicenseKeyDetail = virtualizationli.Split('*');
                                                                    var virtualizationvalues = virtualizationLicenseKeyDetail[4];
                                                                    dynamic virtualizationjson = JsonConvert.DeserializeObject(virtualizationvalues);
                                                                    var virtualizationdb = virtualizationjson.primaryvirtualizationCount;
                                                                    var visualcountused = license.BaseLicenseCountVm.VirtualizationUsedCount;
                                                                    var visualderivedcountused = license.BaseLicenseCountVm.DerivedVirtualizationUsedCount;
                                                                    var Virtualizationcountused = license.BaseLicenseCountVm.VirtualizationUsedCount;
                                                                    var Virtualizationderivedcountused = license.BaseLicenseCountVm.DerivedVirtualizationAvailableCount;
                                                                }
                                                                <span class="fw-semibold" >@virtualizationcount / @virtualizationdb</span>
                                                                <small class="text-light">Virtualization</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <ul class="dropdown-menu licenseChild-dropdown-menu-end p-2" aria-labelledby="dropdownMenuButton" style="width:8rem;">
                                                        <li>
                                                            <div class="table-responsive" style=" height: 60px;overflow: auto;">
                                                                <table class="table table-sm fs-7 w-100 mb-0">
                                                                    <tbody>
                                                                        <tr><td>Parent Utilize : @Virtualizationcountused</td> </tr>
                                                                        <tr><td>Child Utilize : @Virtualizationderivedcountused</td></tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="nav-item">
                                            <a class="nav-link license navgroup" id="nav-third-tab@(i)" role="presentation" data-bs-toggle="tab"
                                               data-bs-target="#navChildTab@(i)" type="button" role="tab" aria-controls="nav-third@(i)"
                                               aria-selected="false" data-parentEntity-id="collapsedserver@(i)" data-entity-id="@license.Id" data-entity-name="server" data-entity-type="thirdParty">
                                                <div class="mb-0 text-start card dropdown" >
                                                    <div class="card-body p-2" data-bs-toggle="dropdown license-dropdown" aria-expanded="false">
                                                        <div class="d-flex align-items-center">
                                                            <i class="cp-api fs-4 license_api"></i>
                                                            <div class="ms-2 d-grid">
                                                                @{
                                                                    var thirdpartycount = license.BaseLicenseCountVm.ThirdPartyUsedCount + license.BaseLicenseCountVm.DerivedThirdPartyAvailableCount;
                                                                    var thirdpartyli = SecurityHelper.Decrypt(license.LicenseKey);
                                                                    var thirdpartyLicenseKeyDetail = thirdpartyli.Split('*');
                                                                    var thirdpartyvalues = thirdpartyLicenseKeyDetail[4];
                                                                    dynamic thirdpartyjson = JsonConvert.DeserializeObject(thirdpartyvalues);
                                                                    var thirdpartydb = thirdpartyjson.primarythirdPartyCount;
                                                                    var ThirdPartycountused = license.BaseLicenseCountVm.ThirdPartyUsedCount;
                                                                    var ThirdPartyderivedcountused = license.BaseLicenseCountVm.DerivedThirdPartyAvailableCount;
                                                                }
                                                                <span class="fw-semibold" >@thirdpartycount / @thirdpartydb</span>
                                                                <small class="text-light">ThirdParty</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <ul class="dropdown-menu licenseChild-dropdown-menu-end p-2" aria-labelledby="dropdownMenuButton" style="width:8rem;">
                                                        <li>
                                                            <div class="table-responsive" style=" height: 60px;overflow: auto;">
                                                                <table class="table table-sm fs-7 w-100 mb-0">
                                                                    <tbody>
                                                                        <tr><td>Parent Utilize : @ThirdPartycountused</td> </tr>
                                                                        <tr><td>Child Utilize : @ThirdPartyderivedcountused</td></tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="nav-item">
                                            <a class="nav-link license navgroup" id="nav-dns-tab@(i)" role="presentation" data-bs-toggle="tab"
                                               data-bs-target="#navChildTab@(i)" type="button" role="tab" aria-controls="nav-dns@(i)"
                                               aria-selected="false" data-parentEntity-id="collapsedserver@(i)" data-entity-id="@license.Id" data-entity-name="server" data-entity-type="dns">
                                                <div class="mb-0 text-start card dropdown">
                                                    <div class="card-body p-2" data-bs-toggle="dropdown license-dropdown" aria-expanded="false">
                                                        <div class="d-flex align-items-center">
                                                            <i class="cp-DNS fs-4 license_dns"></i>
                                                            <div class="ms-2 d-grid">
                                                                @{
                                                                    var dnscount = license.BaseLicenseCountVm.DnsUsedCount + license.BaseLicenseCountVm.DerivedDnsAvailableCount;
                                                                    var dnsli = SecurityHelper.Decrypt(license.LicenseKey);
                                                                    var dnsLicenseKeyDetail = dnsli.Split('*');
                                                                    var dnsvalues = dnsLicenseKeyDetail[4];
                                                                    dynamic dnsjson = JsonConvert.DeserializeObject(dnsvalues);
                                                                    var dnsdb = dnsjson.primarydnsCount;
                                                                    var Dnscountused = license.BaseLicenseCountVm.DnsUsedCount;
                                                                    var Dnsderivedcountused = license.BaseLicenseCountVm.DerivedDnsAvailableCount;
                                                                }
                                                                <span class="fw-semibold" >@dnscount / @dnsdb</span>
                                                                <small class="text-light">DNS</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <ul class="dropdown-menu licenseChild-dropdown-menu-end p-2" aria-labelledby="dropdownMenuButton" style="width:8rem;">
                                                        <li>
                                                            <div class="table-responsive" style=" height: 60px;overflow: auto;">
                                                                <table class="table table-sm fs-7 w-100 mb-0">
                                                                    <tbody>
                                                                        <tr><td>Parent Utilize : @Dnscountused</td></tr>
                                                                        <tr><td>Child Utilize : @Dnsderivedcountused</td></tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="License_Tab tab-content" id="myTabContent">
                                        <div class="tab-pane fade fadegroup" id="navChildTab@(i)" role="tabpanel" aria-labelledby="nav-database-tab">
                                            <div class="collapsedserver@(i)" id="collapseAttach@(i)">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="accordion-flush mt-2" id="sub-accordionExample">
                                        <div class="accordion-item">
                                            <div id="data@(i)" data-bs-parent="#sub-accordionExample">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            i++;
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class='Notification'>
    <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
        <div class='d-flex'>
            <div class='toast-body'>
                <span id="alertClass" class='success-toast'>
                    <i id="icon_Detail" class='cp-check toast_icon'></i>
                </span>
                <span id="notificationAlertmessage">

                </span>
            </div>
            <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
        </div>
    </div>
</div>
<div id="AdminCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.CreateAndEdit" aria-hidden="true"></div>
<div id="AdminDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.Delete" aria-hidden="true"></div>

<div class="modal fade" id="CreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="configureModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>
<div class="modal fade" id="DerivedLicense" data-bs-backdrop="static" aria-labelledby="configureModalLabel" aria-hidden="true">
    <partial name="DerivedLicense" />
</div>
<div class="modal fade" id="Authentication" data-bs-backdrop="static" aria-labelledby="configureModalLabel" aria-hidden="true">
    <partial name="Authentication" model="new UserLockCommand()" />
</div>
<div class="modal fade" id="serverattachedModal" data-bs-backdrop="static" tabindex="-1" aria-hidden="true">
    <partial name="ServerAttached" />
</div>
<div class="modal fade" id="ReplaceModal" data-bs-backdrop="static" aria-labelledby="configureModalLabel" aria-hidden="true">
    <partial name="Replace" model="new LicenseReplaceCommand()" />
</div>
<div class="modal fade" id="DeleteModal" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>
<div class="modal fade" id="DerivedDeleteModal" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="DerivedDelete" />
</div>
<div class="modal fade" id="LicenseRequest" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
        <div class="modal-content">

            <div class="modal-header">
                <h6 class="page_title" title="Authenticate"><i class="cp-license-request"></i><span>License Request Configuration</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <div class="myDiv">
                        <div class="form-label">Type</div>
                        <div class="my-2 ">
                            <div class="d-flex gap-4 flex-wrap mb-4">
                                <div class="position-relative Renewal">
                                    <input class="btn-check" type="radio" name="type" value="Renewal" id="option1" data-toggle="button" autocomplete="off">
                                    <label class="site_type btn border-secondary-subtle" for="option1">
                                        <i class="cp-license-renewal fs-1"></i><br>
                                    </label>
                                    <div class="text-center mt-2 d-block text-truncate" style="max-width:80px" title="Renewal">Renewal</div>
                                </div>
                                <div class="position-relative Upgrade">
                                    <input class="btn-check" type="radio" name="type" value="Upgrade" id="option2" data-toggle="button" autocomplete="off">
                                    <label class="site_type btn border-secondary-subtle" for="option2">
                                        <i class="cp-license-upgrade fs-1"></i><br>
                                    </label>
                                    <div class="text-center mt-2 d-block text-truncate" style="max-width:80px" title=Upgrade>Upgrade</div>
                                </div>
                                <div class="position-relative AMC">
                                    <input class="btn-check" type="radio" name="type" value="AMC" id="option3" data-toggle="button" autocomplete="off">
                                    <label class="site_type btn border-secondary-subtle" for="option3">
                                        <i class="cp-AMC fs-1"></i><br>
                                    </label>
                                    <div class="text-center mt-2 d-block text-truncate" style="max-width:80px" title="AMC">AMC</div>
                                </div>
                            </div>
                            <div class="form-group d-none" id="renewdate">
                            
                                <div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input renewtype" type="checkbox" name="renewtype" id="inlineCheckbox1" value="CountOnly">
                                        <label class="form-check-label" for="inlineCheckbox1">CountOnly</label>
                                    </div>
                                    <div class="form-check form-check-inline renewchild">
                                        <input class="form-check-input renewtype" type="checkbox" name="renewtype" id="inlineCheckbox2" value="ExpiryDateOnly">
                                        <label class="form-check-label" for="inlineCheckbox2">ExpiryDateOnly</label>
                                    </div>
                                    <div class="form-check form-check-inline renewchild">
                                        <input class="form-check-input renewtype" type="checkbox" name="renewtype" id="inlineCheckbox3" value="Both">
                                        <label class="form-check-label" for="inlineCheckbox3">Both</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group d-none" id="amcRenew">
                                <div>
                                    <div class="form-check form-check-inline amcnew">
                                        <input class="form-check-input amctype" type="checkbox" name="amctype" id="chkamcNew" value="AMCNew">
                                        <label class="form-check-label" for="inlineCheckbox1">AMC New</label>
                                    </div>
                                    <div class="form-check form-check-inline amcrenewal">
                                        <input class="form-check-input amctype" type="checkbox" name="amctype" id="chkamcRenewal" value="AMCRenewal">
                                        <label class="form-check-label" for="inlineCheckbox2">AMC Renewal</label>
                                    </div>
                                   
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="myDiv2 d-none">
                        <div class="form-label fs-7">License Key </div>
                        <p class="my-2" id="encryptlicense" style="white-space: normal;overflow-wrap: break-word;">
                        </p>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between align-items-center">
                <small class="text-secondary "><i class=" me-1"></i></small>
                <div class="gap-2 d-flex align-items-center">
                    <span><i class="cp-copy fs-5" title="Copy" id="copy_request" role="button"></i></span>
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm" id="generate">Generate</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="LicenseTimeLine" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title" title="Authenticate"><i class="cp-license-timeline"></i><span>License TimeLine View</span></h6>
                <div>
                    <button type="button" class="btn-sm btn btn-primary" data-bs-dismiss="modal" title="Export"><i class="cp-export me-1"></i>Export</button>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-md-4 col-lg-4">
                        <div class="card shadow-sm p-3 " style="height:calc(100vh - 160px);overflow:auto">
                            <div class="card-header px-0 border-0 bg-transparent">
                                <h6 class="card-subtitle">Select License</h6>
                            </div>
                            <div class="tree-menu">
                                <ul class="tree">
                                    <li class="has-children">
                                        <span class="text-indent">
                                            <span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />All License</span>
                                        </span>
                                        <ul class="sub-parent">
                                            <li class="has-children">
                                                <span class="text-indent"><span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />Perpetuuiti India </span></span>
                                                <ul class="sub-parent">
                                                    <li>
                                                        <span class="text-indent"><span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />PTS PO-6402</span></span>
                                                    </li>
                                                    <li>
                                                        <span class="text-indent"><span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />PTS PO-6403</span></span>
                                                    </li>
                                                    <li>
                                                        <span class="text-indent"><span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />PTS PO-6404</span></span>
                                                    </li>
                                                </ul>
                                            </li>
                                            <li>
                                                <span class="text-indent">
                                                    <span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />Perpetuuiti UAE</span>
                                                </span>
                                                <ul class="sub-parent">
                                                    <li>
                                                        <span class="text-indent"><span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />PTS PO-6411</span></span>
                                                    </li>
                                                    <li>
                                                        <span class="text-indent"><span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />PTS PO-6412</span></span>
                                                        <ul class="sub-parent">
                                                            <li>
                                                                <span class="text-indent"><span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />PTS PO-64</span></span>
                                                            </li>
                                                            <li>
                                                                <span class="text-indent"><span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />PTS PO-6413</span></span>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </li>
                                            <li>
                                                <span class="text-indent">
                                                    <span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />Perpetuuiti Arab</span>
                                                </span>
                                                <ul class="sub-parent">
                                                    <li>
                                                        <span class="text-indent"><span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />PTS PO-6411</span></span>
                                                    </li>
                                                    <li>
                                                        <span class="text-indent"><span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />PTS PO-6412</span></span>
                                                        <ul class="sub-parent">
                                                            <li>
                                                                <span class="text-indent"><span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />PTS PO-6413</span></span>
                                                            </li>
                                                            <li>
                                                                <span class="text-indent"><span class="d-flex align-items-center gap-2"><input class="form-check" type="checkbox" />Core Banking_BS</span></span>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8 col-lg-8">
                        <div class="card shadow-sm p-3" style="height:calc(100vh - 160px);overflow:auto">
                            <div class="d-none">
                                <img src="/img/isomatric/isometric_license_log.svg" />
                            </div>
                            <div class="vertical-timeline-wrapper">
                                <ul class="sessions">
                                    <li data-color="primary">
                                        <div class="accordion " id="accordionExample">
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                                        <div>
                                                            <p class="m-0">
                                                                May 22 2024 08:30:53 &nbsp;<span class="text-primary">PO -6403</span>
                                                            </p>
                                                            <small class="text-muted">01 Database License Assigned for Chennai</small>
                                                        </div>
                                                    </button>
                                                </h2>
                                                <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                                                    <div class="accordion-body">
                                                        A Chennai database license for IP address *********** is active
                                                        under a one-year subscription, valid from 22nd May 2024 to
                                                        22nd February 2024.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                    <li data-color="danger">
                                        <div class="accordion " id="accordionExample2">
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                                        <div>
                                                            <p class="m-0">
                                                                May 23 2024 08:30:53 &nbsp;<span class="text-danger">PO -6403</span>
                                                            </p>
                                                            <small class="text-muted">01 Database License Assigned for Chennai</small>
                                                        </div>
                                                    </button>
                                                </h2>
                                                <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample2">
                                                    <div class="accordion-body">
                                                        A Chennai database license for IP address *********** is active
                                                        under a one-year subscription, valid from 22nd May 2024 to
                                                        22nd February 2024.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                    <li data-color="primary">
                                        <div class="accordion " id="accordionExample3">
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3" aria-expanded="false" aria-controls="collapse3">
                                                        <div>
                                                            <p class="m-0">
                                                                May 24 2024 08:30:53 &nbsp;<span class="text-primary">PO -6403</span>
                                                            </p>
                                                            <small class="text-muted">01 Database License Assigned for Chennai</small>
                                                        </div>

                                                    </button>
                                                </h2>
                                                <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#accordionExample3">
                                                    <div class="accordion-body">
                                                        A Chennai database license for IP address *********** is active
                                                        under a one-year subscription, valid from 22nd May 2024 to
                                                        22nd February 2024.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                    <li data-color="primary">
                                        <div class="accordion " id="accordionExample4">
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">

                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4" aria-expanded="false" aria-controls="collapse4">
                                                        <div>
                                                            <p class="m-0">
                                                                May 25 2024 08:30:53 &nbsp;<span class="text-primary">PO -6403</span>
                                                            </p>
                                                            <small class="text-muted">01 Database License Assigned for Chennai</small>
                                                        </div>

                                                    </button>
                                                </h2>
                                                <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#accordionExample4">
                                                    <div class="accordion-body">
                                                        A Chennai database license for IP address *********** is active
                                                        under a one-year subscription, valid from 22nd May 2024 to
                                                        22nd February 2024.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="confirm_active_main" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" >
        <div class="modal-content">
            <div>
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/maintanence.svg" alt="Delete Img" style="margin: -150px 0 0 -38px; width:370px;" />
                </div>
                <div class="modal-body text-center pt-0">
                    <h4>Are you sure?</h4>
                    <p>
                        Do you want to switch the state to <span class="font-weight-bolder text-primary" id="activeMaintenance">Active</span>
                        ?
                    </p>
                    <input type="hidden" class="form-control" />
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm activeMaintenanceIcon">Yes</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/js/Admin/License/License Manager/licensemangerChart.js"></script>
<script src="~/js/Admin/License/License Manager/LicenseManager.js"></script>
<script>
    // $(".myDiv2").hide();
    // $("#hide").click(function () {
    //     $(".myDiv").hide();
    //     $(".myDiv2").show()
    // });


</script>
