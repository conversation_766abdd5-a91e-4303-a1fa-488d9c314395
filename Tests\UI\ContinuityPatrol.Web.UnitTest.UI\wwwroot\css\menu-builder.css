﻿/* Menu Builder Custom Styles */


/* Menu Container Styles */
.menu-container {
    overflow-y: auto;
    padding: 1rem;
    height: calc(100vh - 250px);
    padding: 1rem;
}

/* Menu Item Styles */
.menu-item {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
    position: relative;
}

    .menu-item:hover {
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        border-color: #007bff;
    }

    .menu-item.ui-sortable-helper {
        transform: rotate(2deg);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.2);
        z-index: 1000;
    }

    .menu-item.ui-sortable-placeholder {
        background: #e9ecef;
        border: 2px dashed #6c757d;
        height: 60px;
        visibility: visible !important;
    }

/* Menu Item Header */
.menu-item-header {
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: move;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.375rem 0.375rem 0 0;
}

    .menu-item-header:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    }

.menu-item-info {
    display: flex;
    align-items: center;
    flex-grow: 1;
}

.menu-item-icon {
    width: 2rem;
    height: 2rem;
    background: #007bff;
    color: white;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 0.875rem;
}

.menu-item-details h6 {
    margin: 0;
    font-weight: 600;
    color: #212529;
}

.menu-item-meta {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.menu-item-actions {
    display: flex;
    gap: 0.25rem;
}

    .menu-item-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

/* Role Badges */
.role-badge {
    display: inline-block;
    padding: 0.125rem 0.375rem;
    font-size: 0.625rem;
    font-weight: 600;
    border-radius: 0.25rem;
    margin-right: 0.25rem;
    text-transform: uppercase;
}

    .role-badge.admin {
        background-color: #dc3545;
        color: white;
    }

    .role-badge.manager {
        background-color: #fd7e14;
        color: white;
    }

    .role-badge.user {
        background-color: #198754;
        color: white;
    }

    .role-badge.guest {
        background-color: #6c757d;
        color: white;
    }

/* Menu Type Indicators */
.menu-type-indicator {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
}

    .menu-type-indicator.category {
        background-color: #007bff;
    }

    .menu-type-indicator.subcategory {
        background-color: #6f42c1;
    }

    .menu-type-indicator.page {
        background-color: #198754;
    }

/* Nested Menu Items */
.menu-children {
    padding-left: 2rem;
    border-left: 2px solid #e9ecef;
    margin-left: 1rem;
    margin-top: 0.5rem;
}

    .menu-children .menu-item {
        margin-bottom: 0.25rem;
    }

    .menu-children .menu-item-header {
        padding: 0.5rem 0.75rem;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    }

/* Sub-category with pages indicator */
.menu-item[data-type="subcategory"] .menu-item-header::after {
    content: "";
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    width: 0.25rem;
    height: 0.25rem;
    background-color: #6f42c1;
    border-radius: 50%;
}

/* Page count indicator for categories/subcategories with children */
.menu-item-page-count {
    background: #6c757d;
    color: white;
    font-size: 0.625rem;
    padding: 0.125rem 0.375rem;
    border-radius: 0.75rem;
    margin-left: 0.5rem;
}

/* Collapse/Expand Functionality */
.menu-item.collapsed .menu-children {
    display: none;
}

.collapse-toggle {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 0.875rem;
    padding: 0;
    margin-right: 0.5rem;
    cursor: pointer;
    transition: transform 0.2s ease;
}

    .collapse-toggle:hover {
        color: #007bff;
    }

.menu-item.collapsed .collapse-toggle {
    transform: rotate(-90deg);
}

/* Page Search Dropdown */
.dropdown-menu {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-bottom: 1px solid #f8f9fa;
}

    .dropdown-item:last-child {
        border-bottom: none;
    }

    .dropdown-item:hover {
        background-color: #e9ecef;
    }

    .dropdown-item.active {
        background-color: #007bff;
        color: white;
    }

/* Form Enhancements */
.form-select:focus,
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
    .menu-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .menu-item-actions {
        align-self: flex-end;
    }

    .menu-children {
        padding-left: 1rem;
        margin-left: 0.5rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-down {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        max-height: 0;
        opacity: 0;
    }

    to {
        max-height: 500px;
        opacity: 1;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

    .empty-state i {
        opacity: 0.5;
    }

/* Run Button Styling */
#runMenu {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(40, 167, 69, 0.3);
    transition: all 0.2s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

    #runMenu:hover {
        background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
        box-shadow: 0 0.25rem 0.5rem rgba(40, 167, 69, 0.4);
        transform: translateY(-1px);
    }

    #runMenu:active {
        transform: translateY(0);
        box-shadow: 0 0.125rem 0.25rem rgba(40, 167, 69, 0.3);
    }

/* Import Modal Styling */
#importModal .modal-dialog {
    max-width: 600px;
}

#importPreviewContent {
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
}

/* File Input Styling */
.form-control[type="file"] {
    padding: 0.5rem;
}

    .form-control[type="file"]:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

/* Import/Export Button Group */
.btn-group .btn {
    border-radius: 0.375rem !important;
}

    .btn-group .btn:not(:last-child) {
        margin-right: 0.25rem;
    }

/* Live Menu Preview Modal */
#liveMenuModal .modal-dialog {
    max-width: 95%;
    margin: 1rem auto;
}

#liveMenuModal .modal-body {
    padding: 0;
    max-height: 80vh;
    overflow-y: auto;
}

/* Live Navigation Styling */
.live-nav-link {
    transition: all 0.2s ease;
}

    .live-nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
    }

    .live-nav-link.active {
        background-color: rgba(255, 255, 255, 0.2) !important;
        font-weight: 600;
    }

/* Page Content Area */
#pageContent {
    min-height: 400px;
}

    #pageContent .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

/* Role Selector in Preview */
#previewRole {
    max-width: 150px;
}

/* Sample Page Content Styling */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dashboard Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info {
    border: none;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Activity List */
.list-group-item {
    border-left: none;
    border-right: none;
}

    .list-group-item:first-child {
        border-top: none;
    }

    .list-group-item:last-child {
        border-bottom: none;
    }

/* Navbar with Mega Menu Styles */
.live-preview-container {
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
}

/* Mega Menu Dropdown Styles */
.mega-menu-dropdown {
    position: static;
}

    .mega-menu-dropdown .dropdown-menu.mega-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        width: 100vw;
        max-width: none;
        border: none;
        border-radius: 0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        padding: 0;
        margin: 0;
        background: #fff;
        z-index: 1050;
        transform: translateX(calc(-50vw + 50%));
    }

        .mega-menu-dropdown .dropdown-menu.mega-menu .mega-menu-content {
            padding: 30px 0;
        }

    /* Create a hover bridge to prevent menu from disappearing */
    .mega-menu-dropdown::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        height: 10px;
        background: transparent;
        z-index: 1049;
        pointer-events: auto;
    }

.mega-menu-column {
    padding: 0 15px;
}

.mega-menu-section {
    margin-bottom: 25px;
}

    .mega-menu-section:last-child {
        margin-bottom: 0;
    }

.mega-menu-section-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
}

    .mega-menu-section-title i {
        color: #007bff;
    }

.mega-menu-links {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.mega-menu-link {
    display: flex;
    align-items: flex-start;
    padding: 10px 15px;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

    .mega-menu-link:hover {
        background-color: #f8f9fa;
        color: #007bff;
        text-decoration: none;
        border-color: #e9ecef;
        transform: translateX(5px);
    }

    .mega-menu-link i {
        color: #6c757d;
        margin-top: 2px;
        flex-shrink: 0;
    }

    .mega-menu-link:hover i {
        color: #007bff;
    }

.mega-menu-link-content {
    display: flex;
    flex-direction: column;
}

.mega-menu-link-title {
    font-weight: 500;
    font-size: 0.95rem;
    line-height: 1.3;
}

.mega-menu-link-desc {
    color: #6c757d;
    font-size: 0.8rem;
    margin-top: 2px;
    line-height: 1.2;
}

.mega-menu-single-link {
    margin-bottom: 10px;
}

    .mega-menu-single-link:last-child {
        margin-bottom: 0;
    }

/* Navbar hover effects */
.navbar-nav .nav-link {
    transition: all 0.2s ease;
    position: relative;
}

    .navbar-nav .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
    }

/* Navbar hover effects */
.mega-menu-dropdown .nav-link {
    transition: all 0.2s ease;
}

    .mega-menu-dropdown .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
    }


    .page-content h1 {
        color: #2c3e50;
        border-bottom: 3px solid #3498db;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }

    .page-content .lead {
        color: #7f8c8d;
        font-size: 1.1rem;
        line-height: 1.6;
    }

/* Responsive Design for Navbar Mega Menu */
@media (max-width: 991px) {
    .mega-menu-dropdown {
        position: relative;
    }

        .mega-menu-dropdown .dropdown-menu.mega-menu {
            position: static;
            transform: none;
            width: 100%;
            max-width: none;
            left: auto;
            right: auto;
            box-shadow: none;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-top: 10px;
        }

            .mega-menu-dropdown .dropdown-menu.mega-menu .mega-menu-content {
                padding: 20px 0;
            }

    .mega-menu-column {
        margin-bottom: 20px;
    }

        .mega-menu-column:last-child {
            margin-bottom: 0;
        }
}

@media (max-width: 768px) {
    .mega-menu-dropdown .dropdown-menu.mega-menu .container {
        padding: 0 15px;
    }

    .mega-menu-dropdown .dropdown-menu.mega-menu .row {
        margin: 0;
    }

    .mega-menu-dropdown .dropdown-menu.mega-menu [class*="col-"] {
        padding: 0;
        margin-bottom: 15px;
    }

    .mega-menu-section-title {
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .mega-menu-link {
        padding: 8px 12px;
    }

    .mega-menu-link-title {
        font-size: 0.9rem;
    }

    .mega-menu-link-desc {
        font-size: 0.75rem;
    }
}

@media (max-width: 576px) {
    .mega-menu-dropdown .dropdown-menu.mega-menu .mega-menu-content {
        padding: 15px 0;
    }

    .mega-menu-section {
        margin-bottom: 15px;
    }

    .mega-menu-section-title {
        font-size: 0.85rem;
        margin-bottom: 8px;
    }

    .mega-menu-link {
        padding: 6px 10px;
    }

        .mega-menu-link i {
            margin-right: 8px;
        }
}
