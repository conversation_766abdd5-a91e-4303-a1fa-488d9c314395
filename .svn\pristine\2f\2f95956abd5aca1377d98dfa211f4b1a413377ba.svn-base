﻿
$(function () {
    $("#ddlCompanyName").on('change', function () {
        const value = $(this).val();
        let companyId = $(this).children(":selected").attr("id");
        $('#textCompanyId').val(companyId);
        $('#ddlCompanyName').val(value);
        let errorElement = $('#Company-error');
        validateDropDown(value, 'Please select a company', errorElement);
    });
    let derivedLicense
    $(".derivedbase").on('click', function () {

        derivedLicense = $(this).data('baselicense');
        let derivedLicense1 = $(this).data('licensehelper');

        state = derivedLicense.isState
        var existLicenseKey = derivedLicense1.split('*');
        var values = existLicenseKey[4];
        var jsondata = JSON.parse(values)

        DBValue = jsondata.primarydatabaseCount
        ReplicaValue = jsondata.primaryreplicationCount
        ApplicationValue = jsondata.primaryapplicationCount
        NetworkValue = jsondata.primarynetworkCount
        StorageValue = jsondata.primarystorageCount
        VirtualValue = jsondata.primaryvirtualizationCount
        ThirdPartyValue = jsondata.primarythirdPartyCount
        DnsValue = jsondata.primarydnsCount


        databaseavaliablecount1 = (derivedLicense.baseLicenseCountVm.databaseUsedCount + derivedLicense.baseLicenseCountVm.derivedDatabaseAvailableCount)
        replicationavaliablecount1 = (derivedLicense.baseLicenseCountVm.replicationUsedCount + derivedLicense.baseLicenseCountVm.derivedReplicationAvailableCount)
        applicationavaliablecount1 = (derivedLicense.baseLicenseCountVm.applicationUsedCount + derivedLicense.baseLicenseCountVm.derivedApplicationAvailableCount)
        networkavaliablecount1 = (derivedLicense.baseLicenseCountVm.networkUsedCount + derivedLicense.baseLicenseCountVm.derivedNetworkAvailableCount)
        storageavaliablecount1 = (derivedLicense.baseLicenseCountVm.storageUsedCount + derivedLicense.baseLicenseCountVm.derivedStorageAvailableCount)
        virtualavaliablecount1 = (derivedLicense.baseLicenseCountVm.virtualizationUsedCount + derivedLicense.baseLicenseCountVm.derivedVirtualizationAvailableCount)
        thirdpartyavaliablecount1 = (derivedLicense.baseLicenseCountVm.thirdPartyUsedCount + derivedLicense.baseLicenseCountVm.derivedThirdPartyAvailableCount)
        dnsavaliablecount1 = (derivedLicense.baseLicenseCountVm.dnsUsedCount + derivedLicense.baseLicenseCountVm.derivedDnsAvailableCount)

        initialBaseDBValue = (DBValue - databaseavaliablecount1);
        initialBaseReplicaValue = (ReplicaValue - replicationavaliablecount1);
        initialBaseApplicationValue = (ApplicationValue - applicationavaliablecount1);
        initialBaseNetworkValue = (NetworkValue - networkavaliablecount1);
        initialBaseStorageValue = (StorageValue - storageavaliablecount1);
        initialBaseVirtualValue = (VirtualValue - virtualavaliablecount1);
        initialBaseThirdPartyValue = (ThirdPartyValue - thirdpartyavaliablecount1);
        initialBaseDnsValue = (DnsValue - dnsavaliablecount1);

        initialBaseDBValue1 = (derivedLicense.baseLicenseCountVm.databaseAvailableCount);
        initialBaseReplicaValue1 = (derivedLicense.baseLicenseCountVm.replicationAvailableCount);
        initialBaseApplicationValue1 = (derivedLicense.baseLicenseCountVm.applicationAvailableCount);
        initialBaseNetworkValue1 = (derivedLicense.baseLicenseCountVm.networkAvailableCount);
        initialBaseStorageValue1 = (derivedLicense.baseLicenseCountVm.storageAvailableCount);
        initialBaseVirtualValue1 = (derivedLicense.baseLicenseCountVm.virtualizationAvailableCount);
        initialBaseThirdPartyValue1 = (derivedLicense.baseLicenseCountVm.thirdPartyAvailableCount);
        initialBaseDnsValue1 = (derivedLicense.baseLicenseCountVm.dnsAvailableCount);

        $("#dbdata").text(" / " + DBValue);
        $("#replicadata").text(" / " + ReplicaValue);
        $("#applicadata").text(" / " + ApplicationValue);
        $("#networkdata").text(" / " + NetworkValue);
        $("#storagedata").text(" / " + StorageValue);
        $("#virtualdata").text(" / " + VirtualValue);
        $("#thirddata").text(" / " + ThirdPartyValue);
        $("#dnsdata").text(" / " + DnsValue);

        $("#baseDB").text(initialBaseDBValue);
        $("#baseReplica").text(initialBaseReplicaValue);
        $("#baseApplication").text(initialBaseApplicationValue);
        $("#baseNetwork").text(initialBaseNetworkValue);
        $("#baseStorage").text(initialBaseStorageValue);
        $("#baseVirtual").text(initialBaseVirtualValue);
        $("#baseThird").text(initialBaseThirdPartyValue);
        $("#baseDns").text(initialBaseDnsValue);

        $("#derivedDB").prop("readonly", false).attr("totalCount", DBValue - databaseavaliablecount1)
        $("#derivedReplica").prop("readonly", false).attr("totalCount", ReplicaValue - replicationavaliablecount1)
        $("#derivedApplication").prop("readonly", false).attr("totalCount", ApplicationValue - applicationavaliablecount1)
        $("#derivedNetwork").prop("readonly", false).attr("totalCount", NetworkValue - networkavaliablecount1)
        $("#derivedStorage").prop("readonly", false).attr("totalCount", StorageValue - storageavaliablecount1)
        $("#derivedVirtual").prop("readonly", false).attr("totalCount", VirtualValue - virtualavaliablecount1)
        $("#derivedThirdParty").prop("readonly", false).attr("totalCount", ThirdPartyValue - thirdpartyavaliablecount1)
        $("#derivedDns").attr("totalCount", DnsValue - dnsavaliablecount1)
        let licenseId = $(this).data('license-id');
        let licenseKey = $(this).data('license-key');
        $('#licenseKey').val(licenseKey);
        $('#parentId').val(licenseId);
        $('#textstate').val(state)
        clearFields();
    });
    let tableNamefirst = $(".commonPoDetail").first().attr("data-table-name")
    let parentIdfirst = $(".commonPoDetail").first().attr("data-parent-id")
    let ponumberfirst = $(".commonPoDetail").first().attr("data-parent-ponumber")
    loadPoLicense(parentIdfirst, ponumberfirst, tableNamefirst)

    $('.loadPartialViewButton').on('click', async function () {

        let parentId1 = $(this).data("parent-id");
        let ponumber = $(this).data("parent-ponumber");
        let tablename = $(this).data("tableName");
        $(".custom-tooltip").removeClass("show")
        loadPoLicense(parentId1, ponumber, tablename)
    });

    async function loadPoLicense(parentId1, ponumber, tablename) {

        let url = RootUrl + "Admin/LicenseManager/GetDerivedList";
        let data = {
            parentId: parentId1,
            parentPoNumber: ponumber
        };

        let value = await GetAsync(url, data, OnError);

        $('#' + tablename).html("")

        $.each(value, function (index, item) {

            let derivedproperties = item.properties
            let derivedjson = JSON.parse(derivedproperties)
            let totalchildcount = item.availableCount.totalCount
            let childCount = item.availableCount.totalNotUsedCount;
            let totalusedcount = item.availableCount.totalUsedCount
            let companyName = item.companyName;
            let childponumber = item.poNumber;

            let deriveddatabasecount = derivedjson.primarydatabaseCount
            let derivedreplicationcount = derivedjson.primaryreplicationCount
            let derivedapplicationcount = derivedjson.primaryapplicationCount
            let derivednetworkcount = derivedjson.primarynetworkCount
            let derivedstoragecount = derivedjson.primarystorageCount
            let derivedvirtualizationcount = derivedjson.primaryvirtualizationCount
            let derivedthirdpartycount = derivedjson.primarythirdPartyCount
            let deriveddnscount = derivedjson.primarydnsCount

            let databaseavaliablecount = item.availableCount.databaseUsedCount
            let replicationavaliablecount = item.availableCount.replicationUsedCount
            let applicationavaliablecount = item.availableCount.applicationUsedCount
            let networkavaliablecount = item.availableCount.networkUsedCount
            let storageavaliablecount = item.availableCount.storageUsedCount
            let virtualavaliablecount = item.availableCount.virtualizationUsedCount
            let thirdpartyavaliablecount = item.availableCount.thirdPartyUsedCount
            let dnsavaliablecount = item.availableCount.dnsUsedCount
            let Createdate = item.createdDate
            let Expirydate = item.expiryDate


            let licensetype = item.validity
            let id = item.id
            let maxChildCount = childCount + totalusedcount;

            let randomnumber = Math.floor(1000 + Math.random() * 9000);

            let html = `
           <div>
           
<div class="d-flex space-between align-items-center">
   <h2 class="w-25 flex-fill p-2 border-0 list-group-item  fw-semibold">
  
     <button type="button" class="ps-2 license_manager_collapse accordion-button collapsed${randomnumber}" 
    onclick="derivedchild()" data-bs-toggle="collapse" 
    data-index="${randomnumber}" href="#collapse-sub-child${randomnumber}" 
    role="button" aria-controls="collapse-sub-child${randomnumber}" 
    style="font-size: 11px;">
    
    <span class="me-1">${index + 1}.</span> 

    <div class="dropdown">
    <div class="btn-group dropend">
        <span id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
             ${companyName} <b>(${childponumber})</b>
        </span>
        <ul class="dropdown-menu license-dropdown-menu-end p-2" aria-labelledby="dropdownMenuButton" style="width: 18rem;">
            <li>
                <div class="table-responsive">
                    <table class="table table-sm table-borderless fs-7" style="width:15rem">
                        <tbody>
                            <tr><th>License Type</th>
                            <td>:</td>
                                <td> ${licensetype}</td>
                            </tr>
                            <tr>
                                <th>Create Date</th>
                                <td>:</td>
                                <td> ${Createdate}</td>
                            </tr>
                            <tr>
                                <th>Expiry Date</th>
                                <td>:</td>
                                <td>${Expirydate}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </li>
        </ul>
    </div>
</div>
    <b class="text-primary" style="font-size: 11px;">-(${childCount})</b>
</button>

   </h2>
   <div class="w-25 flex-fill p-2 border-0 list-group-item  text-light" style="visibility: hidden;">
      Enterprise-Unlimited
   </div>
   <div class="w-25 flex-fill p-2 border-0 list-group-item text-light" style="visibility: hidden;">
      Enterprise-Unlimited
   </div>
   <div class="w-25  p-2 border-0 me-2 list-group-item">
     <div class="d-flex align-items-center gap-2">
                <div class="w-100">
                      <div class="progress" aria-label="Animated striped" role="progressbar" style="height:12px;">
                             <div class="progress-bar progress-bar-striped progress-bar-animated" role="button" title="License Used Count" aria-valuenow="${totalusedcount}" aria-valuemin="0" aria-valuemax="${maxChildCount}" style="width:${(totalusedcount >= 1 ? (totalusedcount * 100.0) / maxChildCount : 0)}%; background-color: rgba(76,185,231,255);">
                                <div style="color: white;">${totalusedcount}</div>
                             </div>
                             <div class="progress-bar progress-bar-striped progress-bar-animated" role="button" title="Available Count" aria-valuenow="${childCount}" aria-valuemin="0" aria-valuemax="${maxChildCount}" style="width: ${(childCount >= 1 ? (childCount * 100.0) / maxChildCount : 0)}%; background-color: rgb(96,168,67);">
                                <div style="color: white;">${childCount}</div>
                             </div>
                          </div>
                </div>

   <div class="fw-semibold" style="min-width: 34px;">${totalchildcount}</div>
    </div>
    
   </div>
   <div  class="d-flex align-item-center justify-content-center gap-1"  style=" width:13%">
      <button type="button" id="derivededit${randomnumber}" title="Edit" class="p-0 btn btn-transparent derivededit disabled d-none" onclick="derivedEdit(this)" data-baselicense='${JSON.stringify(item)}' data-derivedlicense='${JSON.stringify(item)}'>
      </button>
    
      <button type="button" title="Delete" class="p-0 btn btn-transparent deriveddelete" onclick="derivedDelete(this)" data-bs-toggle="modal" data-bs-target="#DerivedDeleteModal" data-derivedlicense-id="${item.id}" data-derivedlicense-companyname="${item.companyName}">
      <i class="cp-Delete"></i>
      </button>
   </div>
</div>
<div>
<div class="accordion-collapse collapse collapse${randomnumber}" aria-labelledby="collapse-sub-child${randomnumber}" id="collapse-sub-child${randomnumber}">
   <div class="px-0 pt-0 accordion-body" >
      <div class="License_Nav nav nav-tabs nav-justified subChild mb-3" role="tablist" id="subChild${randomnumber}">
         <div class="nav-item">
            <a class="nav-link license d_chlid" id="nav--subchild-application-tab${randomnumber}" role="presentation" data-bs-toggle="tab"
               data-bs-target="#navSubChildTab${randomnumber}" type="button" role="tab"
               aria-controls="nav--subchild-replication${randomnumber}"  data-entity-id="${id}" data-entity-childId=collapsedChildserver${randomnumber} data-entity-name="Server" data-entity-type="application" onclick="testData(this)" aria-selected="false">
               <div class="mb-0 text-start card">
                  <div class="card-body p-2">
                     <div class="d-flex align-items-center">
                        <i class="cp-application fs-4 license_application"></i>
                        <div class="ms-2 d-grid">
                           <span class="fw-semibold"> ${applicationavaliablecount}/ ${derivedapplicationcount}</span>
                           <small class="text-light">Application</small>
                        </div>
                     </div>
                  </div>
               </div>
            </a>
         </div>
         <div class="nav-item">
            <a class="nav-link license d_chlid" id="nav--subchild-database-tab${randomnumber}" role="presentation" data-bs-toggle="tab"
               data-bs-target="#navSubChildTab${randomnumber}" type="button" role="tab"
               aria-controls="nav--subchild-database${randomnumber}" data-entity-id="${id}" data-entity-childId=collapsedChildserver${randomnumber} data-entity-name="Database"  aria-selected="false"  onclick="testData(this)">
               <div class="mb-0 text-start card">
                  <div class="card-body p-2">
                     <div class="d-flex align-items-center">
                        <i class="cp-database fs-4 license_database"></i>
                        <div class="ms-2 d-grid">
                           <span class="fw-semibold"> ${databaseavaliablecount}/ ${deriveddatabasecount}</span>
                           <small class="text-light">Database</small>
                        </div>
                     </div>
                  </div>
               </div>
            </a>
         </div>
         <div class="nav-item">
            <a class="nav-link license d_chlid" id="nav--subchild-replication-tab${randomnumber}" role="presentation" data-bs-toggle="tab"
               data-bs-target="#navSubChildTab${randomnumber}" type="button" role="tab"
               aria-controls="nav--subchild-replication${randomnumber}"  data-entity-id="${id}" data-entity-childId=collapsedChildserver${randomnumber} data-entity-name="Replication"  onclick="testData(this)" aria-selected="false">
               <div class="mb-0 text-start card">
                  <div class="card-body p-2">
                     <div class="d-flex align-items-center">
                        <i class="cp-replication-on fs-4 license_replication"></i>
                        <div class="ms-2 d-grid">
                           <span class="fw-semibold"> ${replicationavaliablecount}/ ${derivedreplicationcount}</span>
                           <small class="text-light">Replication</small>
                        </div>
                     </div>
                  </div>
               </div>
            </a>
         </div>
         <div class="nav-item">
            <a class="nav-link license d_chlid" id="nav--subchild-network-tab${randomnumber}" role="presentation" data-bs-toggle="tab"
               data-bs-target="#navSubChildTab${randomnumber}" type="button" role="tab"
               aria-controls="nav--subchild-replication${randomnumber}"  data-entity-id="${id}" data-entity-childId=collapsedChildserver${randomnumber} data-entity-name="Server" data-entity-type="network" onclick="testData(this)" aria-selected="false">
               <div class="mb-0 text-start card">
                  <div class="card-body p-2">
                     <div class="d-flex align-items-center">
                        <i class="cp-network fs-4 license_network"></i>
                        <div class="ms-2 d-grid">
                           <span class="fw-semibold"> ${networkavaliablecount}/ ${derivednetworkcount}</span>
                           <small class="text-light">Network</small>
                        </div>
                     </div>
                  </div>
               </div>
            </a>
         </div>
         <div class="nav-item">
            <a class="nav-link license d_chlid" id="nav--subchild-Storage-tab${randomnumber}" role="presentation" data-bs-toggle="tab"
               data-bs-target="#navSubChildTab${randomnumber}" type="button" role="tab"
               aria-controls="nav--subchild-replication${randomnumber}"  data-entity-id="${id}" data-entity-childId=collapsedChildserver${randomnumber} data-entity-name="Server" data-entity-type="storage" onclick="testData(this)" aria-selected="false">
               <div class="mb-0 text-start card">
                  <div class="card-body p-2">
                     <div class="d-flex align-items-center">
                        <i class="cp-storage fs-4 license_stroage"></i>
                        <div class="ms-2 d-grid">
                           <span class="fw-semibold"> ${storageavaliablecount}/ ${derivedstoragecount}</span>
                           <small class="text-light">Storage</small>
                        </div>
                     </div>
                  </div>
               </div>
            </a>
         </div>
         <div class="nav-item">
            <a class="nav-link license d_chlid" id="nav--subchild-virtual-tab${randomnumber}" role="presentation" data-bs-toggle="tab"
               data-bs-target="#navSubChildTab${randomnumber}" type="button" role="tab"
               aria-controls="nav--subchild-replication${randomnumber}"  data-entity-id="${id}" data-entity-childId=collapsedChildserver${randomnumber} data-entity-name="Server" data-entity-type="virtual" onclick="testData(this)" aria-selected="false">
               <div class="mb-0 text-start card">
                  <div class="card-body p-2">
                     <div class="d-flex align-items-center">
                        <i class="cp-virtualization fs-4 license_virtualization"></i>
                        <div class="ms-2 d-grid">
                           <span class="fw-semibold"> ${virtualavaliablecount}/ ${derivedvirtualizationcount}</span>
                           <small class="text-light">Virtualization</small>
                        </div>
                     </div>
                  </div>
               </div>
            </a>
         </div>
         <div class="nav-item">
            <a class="nav-link license d_chlid" id="nav--subchild-third-tab${randomnumber}" role="presentation" data-bs-toggle="tab"
               data-bs-target="#navSubChildTab${randomnumber}" type="button" role="tab"
               aria-controls="nav--subchild-replication${randomnumber}"  data-entity-id="${id}" data-entity-childId=collapsedChildserver${randomnumber} data-entity-name="Server" data-entity-type="thirdParty"  onclick="testData(this)" aria-selected="false">
               <div class="mb-0 text-start card">
                  <div class="card-body p-2">
                     <div class="d-flex align-items-center">
                        <i class="cp-api fs-4 license_api"></i>
                        <div class="ms-2 d-grid">
                           <span class="fw-semibold"> ${thirdpartyavaliablecount}/ ${derivedthirdpartycount}</span>
                           <small class="text-light">ThirdParty</small>
                        </div>
                     </div>
                  </div>
               </div>
            </a>
         </div>
         <div class="nav-item">
            <a class="nav-link license d_chlid" id="nav--subchild-dns-tab${randomnumber}" role="presentation" data-bs-toggle="tab"
               data-bs-target="#navSubChildTab${randomnumber}" type="button" role="tab"
               aria-controls="nav--subchild-replication${randomnumber}"  data-entity-id="${id}" data-entity-childId=collapsedChildserver${randomnumber} data-entity-name="Server" data-entity-type="dns" onclick="testData(this)" aria-selected="false">
               <div class="mb-0 text-start card">
                  <div class="card-body p-2">
                     <div class="d-flex align-items-center">
                        <i class="cp-DNS fs-4 license_dns"></i>
                        <div class="ms-2 d-grid">
                           <span class="fw-semibold"> ${dnsavaliablecount}/ ${deriveddnscount}</span>
                           <small class="text-light">DNS</small>
                        </div>
                     </div>
                  </div>
               </div>
            </a>
         </div>
      </div>
   </div>
   <div class="License_Tab tab-content" id="subChildContent${randomnumber}">
      <div class="tab-pane fade" id="navSubChildTab${randomnumber}" role="tabpanel"
         aria-labelledby="nav--subchild-database-tab">
         <div class="collapsedChildserver${randomnumber}" id="collapsesetAttach${randomnumber}">
         </div>
      </div>
   </div>
</div>
      
            `;


            $('#' + tablename).append(html);


      
        });
    }


    async function updateBaseDBValue(derivedDBValue, value) {

        try {

            await validateDBCount(derivedDBValue, value);

            if (!isNaN(derivedDBValue)) {
                let newValue = value - derivedDBValue;
                if (derivedDBValue > value) {
                    $("#baseDB").text(value);
                    $("#derivedDB").val("")
                } else {
                    $("#baseDB").text(newValue);
                }

            }

        } catch (error) {

        }
    }

  
    async function validateDBCount(val1, val2) {
        const errorElement = $('#db-error');
        const isValid = val1 <= val2;
        if (!isValid) {
            errorElement.text('Enter valid count').addClass('field-validation-error');

            $("#baseDB").val(0);
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');

            return true;
        }

    }

    $("#derivedDB").on('input', function () {
        let totalCount = parseInt($("#derivedDB").attr("totalCount"))

        const errorElement = $('#db-error');
        if ($("#derivedDB").val() == "") {
            $("#baseDB").text(initialBaseDBValue + parseInt(databasecount));
            errorElement.text('Enter database count').addClass('field-validation-error');
            return false;
        } else if (!/^[0-9]+$/.test($("#derivedDB").val())) {
            errorElement.text('Invalid database count').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            let derivedDBValue = parseInt($("#derivedDB").val());
            updateBaseDBValue(derivedDBValue, totalCount);
        }

    });

    async function updateBaseReplicaValue(derivedReplicaValue, value) {
        try {

            await validateReplicaCount(derivedReplicaValue, value);

            if (!isNaN(derivedReplicaValue)) {
                let newValue = value - derivedReplicaValue;
                if (derivedReplicaValue > value) {
                    $("#baseReplica").text(value);
                    $("#derivedReplica").val("")
                } else {
                    $("#baseReplica").text(newValue);
                }

            }
        } catch (error) {

        }

    }
    async function validateReplicaCount(val1, val2) {
        const errorElement = $('#replica-error');
        const isValid = val1 <= val2;
        if (!isValid) {
            errorElement.text('Enter valid count').addClass('field-validation-error');

            $("#baseReplica").val(0);
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');

            return true;
        }

    }

    $("#derivedReplica").on('input', function () {
        let totalCount = parseInt($("#derivedReplica").attr("totalCount"))
        const errorElement = $('#replica-error');
        if ($("#derivedReplica").val() == "") {
            $("#baseReplica").text(initialBaseReplicaValue + parseInt(replicationcount));
            errorElement.text('Enter replication count').addClass('field-validation-error');
            return false;
        } else if (!/^[0-9]+$/.test($("#derivedReplica").val())) {
            errorElement.text('Invalid replication count').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            let derivedDBValue = parseInt($("#derivedReplica").val());
            updateBaseReplicaValue(derivedDBValue, totalCount);
        }

    });
    async function updateBaseApplicationValue(derivedApplicationValue, value) {
        try {

            await validateApplicationCount(derivedApplicationValue, value);

            if (!isNaN(derivedApplicationValue)) {
                let newValue = value - derivedApplicationValue;
                if (derivedApplicationValue > value) {
                    $("#baseApplication").text(value);
                    $("#derivedApplication").val("")
                } else {
                    $("#baseApplication").text(newValue);
                }

            }
        } catch (error) {

        }

    }
    async function validateApplicationCount(val1, val2) {
        const errorElement = $('#application-error');
        const isValid = val1 <= val2;
        if (!isValid) {
            errorElement.text('Enter valid count').addClass('field-validation-error');

            $("#baseApplication").val(0);
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');

            return true;
        }

    }

    $("#derivedApplication").on('input', function () {
        let totalCount = parseInt($("#derivedApplication").attr("totalCount"))
        const errorElement = $('#application-error');
        if ($("#derivedApplication").val() == "") {
            $("#baseApplication").text(initialBaseApplicationValue + parseInt(applicationcount));
            errorElement.text('Enter application count').addClass('field-validation-error');
            return false;
        } else if (!/^[0-9]+$/.test($("#derivedApplication").val())) {
            errorElement.text('Invalid application count').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            let derivedApplicationValue = parseInt($("#derivedApplication").val());
            updateBaseApplicationValue(derivedApplicationValue, totalCount);
        }

    });
    async function updateBaseNetworkValue(derivedNetworkValue, value) {
        try {

            await validateNetworkCount(derivedNetworkValue, value);

            if (!isNaN(derivedNetworkValue)) {
                let newValue = value - derivedNetworkValue;
                if (derivedNetworkValue > value) {
                    $("#baseNetwork").text(value);
                    $("#derivedNetwork").val("")
                } else {
                    $("#baseNetwork").text(newValue);
                }

            }
        } catch (error) {

        }

    }
    async function validateNetworkCount(val1, val2) {
        const errorElement = $('#network-error');
        const isValid = val1 <= val2;
        if (!isValid) {
            errorElement.text('Enter valid count').addClass('field-validation-error');

            $("#baseNetwork").val(0);
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');

            return true;
        }

    }

    $("#derivedNetwork").on('input', function () {
        let totalCount = parseInt($("#derivedNetwork").attr("totalCount"))
        const errorElement = $('#network-error');
        if ($("#derivedNetwork").val() == "") {
            $("#baseNetwork").text(initialBaseNetworkValue + parseInt(networkcount));
            errorElement.text('Enter network count').addClass('field-validation-error');
            return false;
        } else if (!/^[0-9]+$/.test($("#derivedNetwork").val())) {
            errorElement.text('Invalid network count').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            let derivedNetworkValue = parseInt($("#derivedNetwork").val());
            updateBaseNetworkValue(derivedNetworkValue, totalCount);
        }

    });
    async function updateBaseStorageValue(derivedStorageValue, value) {
        try {

            await validateStorageCount(derivedStorageValue, value);

            if (!isNaN(derivedStorageValue)) {
                let newValue = value - derivedStorageValue;
                if (derivedStorageValue > value) {
                    $("#baseStorage").text(value);
                    $("#derivedStorage").val("")
                } else {
                    $("#baseStorage").text(newValue);
                }

            }

        } catch (error) {

        }

    }
    async function validateStorageCount(val1, val2) {
        const errorElement = $('#storage-error');
        const isValid = val1 <= val2;
        if (!isValid) {
            errorElement.text('Enter valid count').addClass('field-validation-error');

            $("#baseStorage").val(0);
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');

            return true;
        }

    }

    $("#derivedStorage").on('input', function () {
        let totalCount = parseInt($("#derivedStorage").attr("totalCount"))
        const errorElement = $('#storage-error');
        if ($("#derivedStorage").val() == "") {
            $("#baseStorage").text(initialBaseStorageValue + parseInt(storagecount));
            errorElement.text('Enter storage count').addClass('field-validation-error');
            return false;
        } else if (!/^[0-9]+$/.test($("#derivedStorage").val())) {
            errorElement.text('Invalid storage count').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            let derivedStorageValue = parseInt($("#derivedStorage").val());
            updateBaseStorageValue(derivedStorageValue, totalCount);
        }

    });
    async function updateBaseVirtualValue(derivedVirtualValue, value) {
        try {

            await validateVirtualCount(derivedVirtualValue, value);

            if (!isNaN(derivedVirtualValue)) {
                let newValue = value - derivedVirtualValue;
                if (derivedVirtualValue > value) {
                    $("#baseVirtual").text(value);
                    $("#derivedVirtual").val("")
                } else {
                    $("#baseVirtual").text(newValue);
                }

            }
        } catch (error) {

        }

    }
    async function validateVirtualCount(val1, val2) {
        const errorElement = $('#virtual-error');
        const isValid = val1 <= val2;
        if (!isValid) {
            errorElement.text('Enter valid count').addClass('field-validation-error');

            $("#baseVirtual").val(0);
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');

            return true;
        }

    }

    $("#derivedVirtual").on('input', function () {
        let totalCount = parseInt($("#derivedVirtual").attr("totalCount"))
        const errorElement = $('#virtual-error');
        if ($("#derivedVirtual").val() == "") {
            $("#baseVirtual").text(initialBaseVirtualValue + parseInt(virtualcount));
            errorElement.text('Enter virtual count').addClass('field-validation-error');
            return false;
        } else if (!/^[0-9]+$/.test($("#derivedVirtual").val())) {
            errorElement.text('Invalid virtual count').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            let derivedVirtualValue = parseInt($("#derivedVirtual").val());
            updateBaseVirtualValue(derivedVirtualValue, totalCount);
        }

    });
    async function updateBaseThirdpartyValue(derivedThirdpartyValue, value) {
        try {

            await validateThirdpartyCount(derivedThirdpartyValue, value);

            if (!isNaN(derivedThirdpartyValue)) {
                let newValue = value - derivedThirdpartyValue;
                if (derivedThirdpartyValue > value) {
                    $("#baseThird").text(value);
                    $("#derivedThirdParty").val("")
                } else {
                    $("#baseThird").text(newValue);
                }

            }
        } catch (error) {

        }

    }
    async function validateThirdpartyCount(val1, val2) {
        const errorElement = $('#thirdParty-error');
        const isValid = val1 <= val2;
        if (!isValid) {
            errorElement.text('Enter valid count').addClass('field-validation-error');

            $("#baseThird").val(0);
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');

            return true;
        }

    }

    $("#derivedThirdParty").on('input', function () {
        let totalCount = parseInt($("#derivedThirdParty").attr("totalCount"))
        const errorElement = $('#thirdParty-error');
        if ($("#derivedThirdParty").val() == "") {
            $("#baseThird").text(initialBaseThirdPartyValue + parseInt(thirdpartycount));
            errorElement.text('Enter thirdparty count').addClass('field-validation-error');
            return false;
        } else if (!/^[0-9]+$/.test($("#derivedThirdParty").val())) {
            errorElement.text('Invalid thirdparty count').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            let derivedThirdpartyValue = parseInt($("#derivedThirdParty").val());
            updateBaseThirdpartyValue(derivedThirdpartyValue, totalCount);
        }

    });
    async function updateBaseDnsValue(derivedDnsValue, value) {
        try {

            await validateDnsCount(derivedDnsValue, value);

            if (!isNaN(derivedDnsValue)) {
                let newValue = value - derivedDnsValue;
                if (derivedDnsValue > value) {
                    $("#baseDns").text(value);
                    $("#derivedDns").val("")
                } else {
                    $("#baseDns").text(newValue);
                }

            }
        } catch (error) {

        }

    }
    async function validateDnsCount(val1, val2) {
        const errorElement = $('#dns-error');
        const isValid = val1 <= val2;
        if (!isValid) {
            errorElement.text('Enter valid count').addClass('field-validation-error');

            $("#baseDns").val(0);
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');

            return true;
        }

    }

    $("#derivedDns").on('input', function () {
        let totalCount = parseInt($("#derivedDns").attr("totalCount"))
        const errorElement = $('#dns-error');
        if ($("#derivedDns").val() == "") {
            $("#baseDns").text(initialBaseDnsValue + parseInt(dnscount));
            errorElement.text('Enter dns count').addClass('field-validation-error');
            return false;
        } else if (!/^[0-9]+$/.test($("#derivedDns").val())) {
            errorElement.text('Invalid dns count').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            let derivedDnsValue = parseInt($("#derivedDns").val());
            updateBaseDnsValue(derivedDnsValue, totalCount);
        }

    });


    $("#derivedSave").on('click', async function () {



        let elementValue = $("#ddlCompanyName").val();
        let errorElement = $('#Company-error');

        let propdatas = derivedLicense.properties;
        propdatas = typeof propdatas === "string" ? JSON.parse(propdatas) : propdatas;

        let derivedFields = {
            databaseCount: $("#derivedDB").val(),
            replicationCount: $("#derivedReplica").val(),
            applicationCount: $("#derivedApplication").val(),
            networkCount: $("#derivedNetwork").val(),
            storageCount: $("#derivedStorage").val(),
            virtualizationCount: $("#derivedVirtual").val(),
            thirdPartyCount: $("#derivedThirdParty").val(),
            dnsCount: $("#derivedDns").val(),
        };

        let derivedDB = parseInt($("#derivedDB").val()) || 0;
        let derivedReplica = parseInt($("#derivedReplica").val()) || 0;
        let derivedApplication = parseInt($("#derivedApplication").val()) || 0;
        let derivedNetwork = parseInt($("#derivedNetwork").val()) || 0;
        let derivedStorage = parseInt($("#derivedStorage").val()) || 0;
        let derivedVirtual = parseInt($("#derivedVirtual").val()) || 0;
        let derivedThird = parseInt($("#derivedThirdParty").val()) || 0;
        let derivedDns = parseInt($("#derivedDns").val()) || 0;

        let baseFields = {
            databaseCount: initialBaseDBValue1 - derivedDB,
            replicationCount: initialBaseReplicaValue1 - derivedReplica,
            applicationCount: initialBaseApplicationValue1 - derivedApplication,
            networkCount: initialBaseNetworkValue1 - derivedNetwork,
            storageCount: initialBaseStorageValue1 - derivedStorage,
            virtualizationCount: initialBaseVirtualValue1 - derivedVirtual,
            thirdPartyCount: initialBaseThirdPartyValue1 - derivedThird,
            dnsCount: initialBaseDnsValue1 - derivedDns,
        };

        let baseLicenseProperties = {};
        let derivedLicenseProperties = {};


        Object.keys(propdatas).forEach(key => {
            //baseLicenseProperties[key] = baseFields.isPrimary;
            //baseLicenseProperties[key] = baseFields.isSecondary;
            //baseLicenseProperties[key] = baseFields.totalSites;
            //derivedLicenseProperties[key] = derivedFields.isPrimary;
            //derivedLicenseProperties[key] = derivedFields.isSecondary;
            //derivedLicenseProperties[key] = derivedFields.totalSites;
            if (key.startsWith('primary')) {

                if (key.includes('application')) {
                    baseLicenseProperties[key] = baseFields.applicationCount;
                    derivedLicenseProperties[key] = derivedFields.applicationCount;
                } else if (key.includes('replication')) {
                    baseLicenseProperties[key] = baseFields.replicationCount;
                    derivedLicenseProperties[key] = derivedFields.replicationCount;
                } else if (key.includes('database')) {
                    baseLicenseProperties[key] = baseFields.databaseCount;
                    derivedLicenseProperties[key] = derivedFields.databaseCount;
                } else if (key.includes('network')) {
                    baseLicenseProperties[key] = baseFields.networkCount;
                    derivedLicenseProperties[key] = derivedFields.networkCount;
                } else if (key.includes('storage')) {
                    baseLicenseProperties[key] = baseFields.storageCount;
                    derivedLicenseProperties[key] = derivedFields.storageCount;
                } else if (key.includes('virtualization')) {
                    baseLicenseProperties[key] = baseFields.virtualizationCount;
                    derivedLicenseProperties[key] = derivedFields.virtualizationCount;
                } else if (key.includes('thirdParty')) {
                    baseLicenseProperties[key] = baseFields.thirdPartyCount;
                    derivedLicenseProperties[key] = derivedFields.thirdPartyCount;
                } else if (key.includes('dns')) {
                    baseLicenseProperties[key] = baseFields.dnsCount;
                    derivedLicenseProperties[key] = derivedFields.dnsCount;
                }
            } else if (key.startsWith('dr')) {

                if (key.includes('application')) {
                    baseLicenseProperties[key] = baseFields.applicationCount;
                    derivedLicenseProperties[key] = derivedFields.applicationCount;
                } else if (key.includes('replication')) {
                    baseLicenseProperties[key] = baseFields.replicationCount;
                    derivedLicenseProperties[key] = derivedFields.replicationCount;
                } else if (key.includes('database')) {
                    baseLicenseProperties[key] = baseFields.databaseCount;
                    derivedLicenseProperties[key] = derivedFields.databaseCount;
                } else if (key.includes('network')) {
                    baseLicenseProperties[key] = baseFields.networkCount;
                    derivedLicenseProperties[key] = derivedFields.networkCount;
                } else if (key.includes('storage')) {
                    baseLicenseProperties[key] = baseFields.storageCount;
                    derivedLicenseProperties[key] = derivedFields.storageCount;
                } else if (key.includes('virtualization')) {
                    baseLicenseProperties[key] = baseFields.virtualizationCount;
                    derivedLicenseProperties[key] = derivedFields.virtualizationCount;
                } else if (key.includes('thirdParty')) {
                    baseLicenseProperties[key] = baseFields.thirdPartyCount;
                    derivedLicenseProperties[key] = derivedFields.thirdPartyCount;
                } else if (key.includes('dns')) {
                    baseLicenseProperties[key] = baseFields.dnsCount;
                    derivedLicenseProperties[key] = derivedFields.dnsCount;
                }
            } else if (key.includes('custom')) {

                if (key.includes('application')) {
                    baseLicenseProperties[key] = baseFields.applicationCount;
                    derivedLicenseProperties[key] = derivedFields.applicationCount;
                } else if (key.includes('replication')) {
                    baseLicenseProperties[key] = baseFields.replicationCount;
                    derivedLicenseProperties[key] = derivedFields.replicationCount;
                } else if (key.includes('database')) {
                    baseLicenseProperties[key] = baseFields.databaseCount;
                    derivedLicenseProperties[key] = derivedFields.databaseCount;
                } else if (key.includes('network')) {
                    baseLicenseProperties[key] = baseFields.networkCount;
                    derivedLicenseProperties[key] = derivedFields.networkCount;
                } else if (key.includes('storage')) {
                    baseLicenseProperties[key] = baseFields.storageCount;
                    derivedLicenseProperties[key] = derivedFields.storageCount;
                } else if (key.includes('virtualization')) {
                    baseLicenseProperties[key] = baseFields.virtualizationCount;
                    derivedLicenseProperties[key] = derivedFields.virtualizationCount;
                } else if (key.includes('thirdParty')) {
                    baseLicenseProperties[key] = baseFields.thirdPartyCount;
                    derivedLicenseProperties[key] = derivedFields.thirdPartyCount;
                } else if (key.includes('dns')) {
                    baseLicenseProperties[key] = baseFields.dnsCount;
                    derivedLicenseProperties[key] = derivedFields.dnsCount;
                }
            }
            else {
                let value = propdatas[key];
                baseLicenseProperties[key] = value;
                derivedLicenseProperties[key] = value;
            }
        });


        let IsCompany = validateDropDown(elementValue, 'Please select a company', errorElement);
        let validations = [
            validateDB(derivedFields.databaseCount),
            validateReplica(derivedFields.replicationCount),
            validateApplication(derivedFields.applicationCount),
            validateNetwork(derivedFields.networkCount),
            validateStorage(derivedFields.storageCount),
            validateVirtual(derivedFields.virtualizationCount),
            validateThirdparty(derivedFields.thirdPartyCount),
            validateDns(derivedFields.dnsCount)
        ];

        let allValid = validations.every(v => v);


        if (!IsCompany) {
            $(".modal-body").scrollTop(0);
        } else {
            if ($("#derivedDns").val() == "") {
                $("#derivedDns").focus();
                var modalBody = $(this).closest(".modal-body");
                modalBody.animate({ scrollTop: $("#derivedDns").offset().top - modalBody.offset().top + modalBody.scrollTop() }, "slow");
            }
        }


        $('#baseproperties').val(JSON.stringify(baseLicenseProperties));
        $('#derivedproperties').val(JSON.stringify(derivedLicenseProperties));
        $('#textstate').val(state);


        if (allValid && IsCompany) {
            $("#derived-form").trigger('submit');
        }
    });




    function validateDB(value) {
        const errorElement = $('#db-error');
        if (!value) {
            errorElement.text('Enter database count')
                .addClass('field-validation-error');
            return false;
        }
        return true;
    }
    function validateReplica(value) {
        const errorElement = $('#replica-error');
        if (!value) {
            errorElement.text('Enter replication count')
                .addClass('field-validation-error');
            return false;
        }
        return true;
    }
    function validateApplication(value) {
        const errorElement = $('#application-error');
        if (!value) {
            errorElement.text('Enter application count')
                .addClass('field-validation-error');
            return false;
        }
        return true;
    }
    function validateNetwork(value) {
        const errorElement = $('#network-error');
        if (!value) {
            errorElement.text('Enter network count')
                .addClass('field-validation-error');
            return false;
        }
        return true;
    }
    function validateStorage(value) {
        const errorElement = $('#storage-error');
        if (!value) {
            errorElement.text('Enter storage count')
                .addClass('field-validation-error');
            return false;
        }
        return true;
    }
    function validateVirtual(value) {
        const errorElement = $('#virtual-error');
        if (!value) {
            errorElement.text('Enter virtual count')
                .addClass('field-validation-error');
            return false;
        }
        return true;
    }
    function validateThirdparty(value) {
        const errorElement = $('#thirdParty-error');
        if (!value) {
            errorElement.text('Enter thirdparty count')
                .addClass('field-validation-error');
            return false;
        }
        return true;
    }
    function validateDns(value) {
        const errorElement = $('#dns-error');
        if (!value) {
            errorElement.text('Enter dns count')
                .addClass('field-validation-error');
            return false;
        }
        return true;
    }
    function validateDropDown(value, errorMessage, errorElement) {
        if (!value) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }

    const errorElements = ['#db-error', '#replica-error', '#application-error', '#network-error', '#storage-error', '#virtual-error', '#thirdParty-error', '#dns-error', '#Company-error'];

    const clearFields = () => {
        $('#ddlCompanyName option:first').prop('selected', 'selected');
        $('#ddlPlatform option:first').prop('selected', 'selected');
        // $('#derivedServer').val('');
        $('#derivedDB').val('');
        $('#derivedReplica').val('');
        $("#derivedApplication").val('');
        $("#derivedNetwork").val('');
        $("#derivedStorage").val('');
        $("#derivedVirtual").val('');
        $("#derivedThirdParty").val('');
        $("#derivedDns").val('');
        $('#derivedSave').text('Save');

        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    };
});

function initializeChildDataTable(tableChildId) {
    $('#' + tableChildId).DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
            }
        },
        lengthMenu: [
            [5, 25, 50, -1],
            [5, 25, 50, 'All']
        ]
    });
}
async function derivedchild() {
    $(".fade").removeClass("active")
    $(".d_chlid").removeClass("active")
}
async function testData(data) {

    try {
        const { entityId: licenseId, entityName: entity, entityType, entityChildid: childId } = data.dataset;
        const tableChildId = `tblChildCompany_${childId}`;
        const url = `${RootUrl}Admin/LicenseManager/GetEntityList`;

        const requestData = { licenseId, entity, entityType };
        const response = await GetAsync(url, requestData, OnError);

        $(`.${childId}`).empty();

        if (response && Array.isArray(response) && response.length > 0) {
            const value = response;
            let tableRows = '';
            const childServerNames = value[0].entity;

            value.forEach((list, i) => {
                let splitedData = list.entityField.split(",")
                const serverName = list.entityName;
                const childServiceName = list.businessServiceName;
                const type = list.type;
                let childhostName = splitedData[1];
                let childdatabaseSid = splitedData[2];
                const ipAddress = splitedData[0] || "NA";
                const parts = ipAddress.split(".");
                const maskedIpAddress = parts.length === 4 ? `xx.xx.xx.${parts[3]}` : ipAddress;
                const icons = list.logo ? list.logo.replace(/[^a-zA-Z0-9]/g, '_') + '.svg' :
                    childServerNames?.toLowerCase() === "server" ? 'cp_server.svg' :
                        childServerNames?.toLowerCase() === "database" ? 'cp_database.svg' :
                            childServerNames?.toLowerCase() === "replication" ? 'cp_replication_rotate.svg' : null;

                tableRows += `
                   <tr>
        <td>${i + 1}</td>
        <td>
            ${childServerNames.toLowerCase() === "server" ?
                        `<img src="/img/OS_Icon/${icon}" width="20">` :
                        (childServerNames.toLowerCase() === "database" || childServerNames.toLowerCase() === "replication" ?
                            `<img src="/img/DB-Logo/${icon}" width="20">` : '')
                    }
            ${type}
        </td>
        <td class="text-truncate d-inline-block align-bottom" >${serverName}</td>
        <td>${childServiceName}</td>
        ${childServerNames.toLowerCase() === "database" ?
                        `<td>${childdatabaseSid}</td>` :
                        (childServerNames.toLowerCase() === "replication" ?
                            `<td>${siteName}</td>` :
                            '<td></td>'
                        )
                    }
        ${childServerNames.toLowerCase() === "database" || childServerNames.toLowerCase() === "server" ? `<td>${maskedIpAddress}</td>` : '<td></td>'}          
        ${childServerNames.toLowerCase() === "database" || childServerNames.toLowerCase() === "server" ? `<td>${childhostName}</td>` : '<td></td>'}
        <td class="text-start">
            <span type="button" title="Decommission" class="me-2" 
                data-decommissionid="${list.licenseId}" 
                data-decommissionentity="${list.entityId}" 
                data-decommissionname="${list.entity}" 
                data-decommissiontype="${list.entityType}" 
                onclick="decommissionTable(this)" 
                data-bs-toggle="modal" 
                data-bs-target="#serverattachedModal">
                <i class="cp-file-not-to-delete"></i>
            </span>                           
            <span type="button" title="Replace" 
                class="replace-button"
                data-replaceentityid="${list.entityId}" 
                data-replaceentity="${list.entity}" 
                data-replaceentityname="${list.entityName}"        
                data-replaceipaddress="${splitData[0]}"
                data-replacesid="${splitData[2]}"
                data-replacehostname="${splitData[1]}"
                data-bs-toggle="modal" 
                id="Replace"
                data-bs-target="#ReplaceModal"
                ${list.entity && list.entity.toLowerCase() === 'replication' ? 'disabled' : ''} 
                style="${list.entity && list.entity.toLowerCase() === 'replication' ? 'pointer-events: none; opacity: 0.5;' : ''}"
            >
                <i class="cp-replace"></i>
            </span>
        </td>
    </tr>`;
            });

            let headerRow = `
<tr>
    <th class="SrNo_th">Sr.No.</th>
    <th>Type</th>
    <th>${serverNames} Name</th>
    <th>Service Name</th>
    <th>${serverNames.toLowerCase() === "database" ? "Database SID" : serverNames.toLowerCase() === "replication" ? "Site Name" : ""}</th>
    <th>${serverNames.toLowerCase() === "database" || serverNames.toLowerCase() === "server" ? "IP Address" : ""}</th>
    <th>${serverNames.toLowerCase() === "database" || serverNames.toLowerCase() === "server" ? "Host Name" : ""}</th>
    <th>Action</th>
</tr>`;;

            $(`.${childId}`).append(tableHtml);
            initializeDataTable(tableChildId);

        } else {
            displayChildNoDataMessage(childId);
        }
    } catch (error) {
        console.error("An error occurred:", error);
    }
}

function displayChildNoDataMessage(childId) {
    let html = `
        <div class="card card-body bg-white mt-2">
           <img src="/img/isomatric/license_expired.svg" height="120" style="width:fit-content" /> 
        </div>`;

    $("." + childId).append(html);
}

async function childdecommissionTable(data) {
    $('#attached').empty()
    let delicenseId = data?.dataset.decommissionid
    let deentityId = data?.dataset?.decommissionentity
    let deentityName = data?.dataset?.decommissionname
    let childtype = data?.dataset?.decommissiontype
    let dcType = childtype?.toLowerCase();
    let dcName = deentityName?.toLowerCase()
    if (dcName) {
        dcName = dcName?.charAt(0)?.toUpperCase() + dcName?.slice(1);
    }
    if (dcType) {
        dcType = dcType?.charAt(0)?.toUpperCase() + dcType?.slice(1);
    }
    let setChildtype = dcType !== "Null" && dcType !== "" && dcType !== "Database" ? dcType : dcName
    let setChildicon = dcType === "Database" ? "cp-server" : dcType === "Application" ? "cp-application" : dcType === "Network" ? "cp-network" :
        dcType === "Storage" ? "cp-storage" : dcType === "Virtualization" ? "cp-virtualization" : dcType === "ThirdParty" ? "cp-api" : dcName === "Replication" ? "cp-replication-on" : dcType === "Dns" ? "cp-DNS" : "cp-database"
    let url = RootUrl + "Admin/LicenseManager/GetDecommissionList";
    forcefullyDelete(delicenseId, deentityId, deentityName)
    $('#attached').empty()
    let requestData = {
        licenseId: delicenseId,
        entityId: deentityId,
        entityType: deentityName
    };
    $('#attached').empty()
    let value = await GetAsync(url, requestData, OnError);
    if (value.success === false) {
        notificationAlert('warning', value.message)
    }
    else {
        let decommissionTableBody = $('#decommissiondata');
        let headerattached = '<h6 class="page_title"><i class="' + setChildicon + '"></i><span>' + setChildtype + ' Attached With</span></h6>' +
            '<button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>'
        $('#attached').append(headerattached);

        function addRow(iconClass, title, dataArray, keyName) {
            let rowContent = `<tr>
            <th><i class="${iconClass} me-1"></i>${title}</th>
            <td>`;

            if (Array.isArray(dataArray) && keyName) {
                dataArray.forEach((dataItem) => {
                    rowContent += `${dataItem[keyName]}<br>`;
                });
            } else if (dataArray !== null && keyName) {

                if (dataArray === "NA") {
                    rowContent += 'NA';
                }
                else {
                    rowContent += dataArray[keyName] !== "" && dataArray[keyName] !== null ? dataArray[keyName] : "NA" + '<br>';
                }

            } else {
                rowContent += 'NA';
            }


            rowContent += `</td></tr>`;
            decommissionTableBody.append(rowContent);
        }

        decommissionTableBody.empty();
        if (value?.serverName) {
            addRow('cp-server', 'Server', value?.serverName ? value : value?.decommissionServerDetailVm.length > 0 ? value?.decommissionServerDetailVm : "NA", 'serverName');
            addRow('cp-database', 'Database', value?.databaseName ? value : value?.decommissionDatabaseDetailVm?.length > 0 ? value?.decommissionDatabaseDetailVm : "NA", 'databaseName');
            addRow('cp-replication-on', 'Replication', value?.replicationName ? value : value?.decommissionReplicationDetailVm?.length > 0 ? value?.decommissionReplicationDetailVm : "NA", 'replicationName');
            addRow('cp-infra-object', 'Infra Object', value?.infraObjectName ? value : value?.decommissionInfraObjectDetailVm?.length > 0 ? value?.decommissionInfraObjectDetailVm : "NA", 'infraObjectName');
            addRow('cp-workflow', 'Workflow', value?.workflowName ? value : value?.decommissionWorkflowDetailVms?.length > 0 ? value?.decommissionWorkflowDetailVms : "NA", 'workflowName');
            addRow('cp-workflow-profile', 'Workflow Profile', value?.profileName ? value : value?.decommissionWorkflowProfiles?.length > 0 ? value?.decommissionWorkflowProfiles : "NA", 'profileName');
        }
        if (value?.databaseName) {

            addRow('cp-database', 'Database', value?.databaseName ? value : value?.decommissionDatabaseDetailVm?.length > 0 ? value?.decommissionDatabaseDetailVm : "NA", 'databaseName');
            addRow('cp-replication-on', 'Replication', value?.replicationName ? value : value?.decommissionReplicationDetailVm?.length > 0 ? value?.decommissionReplicationDetailVm : "NA", 'replicationName');
            addRow('cp-infra-object', 'Infra Object', value?.infraObjectName ? value : value?.decommissionInfraObjectDetailVm?.length > 0 ? value?.decommissionInfraObjectDetailVm : "NA", 'infraObjectName');
            addRow('cp-workflow', 'Workflow', value?.workflowName ? value : value?.decommissionWorkflowDetailVms?.length > 0 ? value?.decommissionWorkflowDetailVms : "NA", 'workflowName');
            addRow('cp-workflow-profile', 'Workflow Profile', value?.profileName ? value : value?.decommissionWorkflowProfiles?.length > 0 ? value?.decommissionWorkflowProfiles : "NA", 'profileName');
        }
        if (value?.replicationName) {

            addRow('cp-replication-on', 'Replication', value?.replicationName ? value : value?.decommissionReplicationDetailVm?.length > 0 ? value?.decommissionReplicationDetailVm : "NA", 'replicationName');
            addRow('cp-infra-object', 'Infra Object', value?.infraObjectName ? value : value?.decommissionInfraObjectDetailVm?.length > 0 ? value?.decommissionInfraObjectDetailVm : "NA", 'infraObjectName');
            addRow('cp-workflow', 'Workflow', value?.workflowName ? value : value?.decommissionWorkflowDetailVms?.length > 0 ? value?.decommissionWorkflowDetailVms : "NA", 'workflowName');
            addRow('cp-workflow-profile', 'Workflow Profile', value?.profileName ? value : value?.decommissionWorkflowProfiles?.length > 0 ? value?.decommissionWorkflowProfiles : "NA", 'profileName');
        }
        if (value?.infraObjectName) {
            addRow('cp-infra-object', 'Infra Object', value?.infraObjectName ? value : value?.decommissionInfraObjectDetailVm?.length > 0 ? value?.decommissionInfraObjectDetailVm : "NA", 'infraObjectName');
            addRow('cp-workflow', 'Workflow', value?.workflowName ? value : value?.decommissionWorkflowDetailVms.length > 0 ? value?.decommissionWorkflowDetailVms : "NA", 'workflowName');
            addRow('cp-workflow-profile', 'Workflow Profile', value?.profileName ? value : value?.decommissionWorkflowProfiles.length > 0 ? value?.decommissionWorkflowProfiles : "NA", 'profileName');
        }
        if (value?.workflowName) {
            addRow('cp-workflow', 'Workflow', value?.workflowName ? value : value?.decommissionWorkflowDetailVms?.length > 0 ? value?.decommissionWorkflowDetailVms : "NA", 'workflowName');
            addRow('cp-workflow-profile', 'Workflow Profile', value?.profileName ? value : value?.decommissionWorkflowProfiles?.length > 0 ? value?.decommissionWorkflowProfiles : "NA", 'profileName');
        }

    }
}

function derivedEdit(data) {
    let derivedLicensejson = data.dataset.derivedlicense
    let derivedjson = JSON.parse(derivedLicensejson)
    let derivedLicenseData = JSON.parse(derivedjson.properties)

    basecount(derivedjson.parentPONumber, derivedLicenseData, derivedjson);


}
let initialBaseServerValue1
let initialBaseDBValue1
let initialBaseReplicaValue1
let initialBaseApplicationValue1
let initialBaseNetworkValue1
let initialBaseStorageValue1
let initialBaseVirtualValue1
let initialBaseThirdPartyValue1
let initialBaseDnsValue1

let ServerValue;
let DBValue;
let ReplicaValue;
let ApplicationValue
let NetworkValue
let StorageValue
let VirtualValue
let ThirdPartyValue
let DnsValue
let state
let initialBaseServerValue;
let initialBaseDBValue;
let initialBaseReplicaValue;
let initialBaseApplicationValue
let initialBaseNetworkValue
let initialBaseStorageValue
let initialBaseVirtualValue
let initialBaseThirdPartyValue
let initialBaseDnsValue

let serveravaliablecount1
let databaseavaliablecount1
let replicationavaliablecount1
let applicationavaliablecount1
let networkavaliablecount1
let storageavaliablecount1
let virtualavaliablecount1
let thirdpartyavaliablecount1
let dnsavaliablecount1
async function basecount(poNumber, derivedLicenseData, derivedjson) {

    let url = RootUrl + "Admin/LicenseManager/GetLicenseManagerByPoNumber?ponumber=" + poNumber
    let value = await GetAsync(url, OnError);
    let baseprop = value.properties;
    let basevalue = JSON.parse(baseprop)
    //  serveravaliablecount1 = (derivedjson.availableCount.serverDbUsedCount)
    databaseavaliablecount1 = (derivedjson.availableCount.databaseUsedCount)
    replicationavaliablecount1 = (derivedjson.availableCount.replicationUsedCount)
    applicationavaliablecount1 = (derivedjson.availableCount.applicationUsedCount)
    networkavaliablecount1 = (derivedjson.availableCount.networkUsedCount)
    storageavaliablecount1 = (derivedjson.availableCount.storageUsedCount)
    virtualavaliablecount1 = (derivedjson.availableCount.virtualizationUsedCount)
    thirdpartyavaliablecount1 = (derivedjson.availableCount.thirdPartyUsedCount)
    dnsavaliablecount1 = (derivedjson.availableCount.dnsUsedCount)
    //  initialBaseServerValue = parseInt(derivedLicenseData.serverDbCount - serveravaliablecount1);
    initialBaseDBValue = parseInt(derivedLicenseData.databaseCount - databaseavaliablecount1);
    initialBaseReplicaValue = parseInt(derivedLicenseData.replicationCount - replicationavaliablecount1);
    initialBaseApplicationValue = parseInt(derivedLicenseData.applicationCount - applicationavaliablecount1);
    initialBaseNetworkValue = parseInt(derivedLicenseData.networkCount - networkavaliablecount1);
    initialBaseStorageValue = parseInt(derivedLicenseData.storageCount - storageavaliablecount1);
    initialBaseVirtualValue = parseInt(derivedLicenseData.virtualizationCount - virtualavaliablecount1);
    initialBaseThirdPartyValue = parseInt(derivedLicenseData.thirdPartyCount - thirdpartyavaliablecount1);
    initialBaseDnsValue = parseInt(derivedLicenseData.dnsCount - dnsavaliablecount1);
    //$("#serverdata").text(" / " + derivedLicenseData.serverDbCount)
    $("#dbdata").text(" / " + derivedLicenseData.databaseCount)
    $("#replicadata").text(" / " + derivedLicenseData.replicationCount)
    $("#applicadata").text(" / " + derivedLicenseData.applicationCount)
    $("#networkdata").text(" / " + derivedLicenseData.networkCount)
    $("#storagedata").text(" / " + derivedLicenseData.storageCount)
    $("#virtualdata").text(" / " + derivedLicenseData.virtualizationCount)
    $("#thirddata").text(" / " + derivedLicenseData.thirdPartyCount)
    $("#dnsdata").text(" / " + derivedLicenseData.dnsCount)
    // $("#baseServer").text(initialBaseServerValue);
    $("#baseDB").text(initialBaseDBValue);
    $("#baseReplica").text(initialBaseReplicaValue);
    $("#baseApplication").text(initialBaseApplicationValue);
    $("#baseNetwork").text(initialBaseNetworkValue);
    $("#baseStorage").text(initialBaseStorageValue);
    $("#baseVirtual").text(initialBaseVirtualValue);
    $("#baseThird").text(initialBaseThirdPartyValue);
    $("#baseDns").text(initialBaseDnsValue);
    populateModalFields(derivedLicenseData, basevalue, derivedjson, value);
    $('#derivedSave').text('Update')
    $('#DerivedLicense').modal('show');
}
function derivedDelete(data) {
    let derivedLicenseId = data.dataset.derivedlicenseId;
    let derivedLicenseCompanyName = data.dataset.derivedlicenseCompanyname;

    $('#deleteName').text(derivedLicenseCompanyName);
    $('#DeleteId').val(derivedLicenseId);

}

let servercount = 0
let databasecount = 0
let replicationcount = 0
let applicationcount = 0
let networkcount = 0
let storagecount = 0
let virtualcount = 0
let thirdpartycount = 0
let dnscount = 0
function populateModalFields(derivedLicenseData, basevalue, value, derivedjson) {

    // $("#derivedServer").prop("readonly", true).attr("totalCount", parseInt(basevalue.serverDbCount) + parseInt(derivedLicenseData.serverDbCount))
    $("#derivedDB").prop("readonly", true).attr("totalCount", parseInt(basevalue.databaseCount) + parseInt(derivedLicenseData.databaseCount))
    $("#derivedReplica").prop("readonly", true).attr("totalCount", parseInt(basevalue.replicationCount) + parseInt(derivedLicenseData.replicationCount))
    $("#derivedApplication").prop("readonly", true).attr("totalCount", parseInt(basevalue.applicationCount) + parseInt(derivedLicenseData.applicationCount))
    $("#derivedNetwork").prop("readonly", true).attr("totalCount", parseInt(basevalue.networkCount) + parseInt(derivedLicenseData.networkCount))
    $("#derivedStorage").prop("readonly", true).attr("totalCount", parseInt(basevalue.storageCount) + parseInt(derivedLicenseData.storageCount))
    $("#derivedVirtual").prop("readonly", true).attr("totalCount", parseInt(basevalue.virtualizationCount) + parseInt(derivedLicenseData.virtualizationCount))
    $("#derivedThirdParty").prop("readonly", true).attr("totalCount", parseInt(basevalue.thirdPartyCount) + parseInt(derivedLicenseData.thirdPartyCount))
    $("#derivedDns").prop("readonly", true).attr("totalCount", parseInt(basevalue.dnsCount) + parseInt(derivedLicenseData.dnsCount))
    $("#derivedPONumber").val(value.poNumber)
    $("#derivedLicenseKey").val(value.licenseKey)
    $("#licenseKey").val(value.licenseKey)
    $("#parentId").val(derivedjson.id);
    $('#Id').val(value.id);
    $('#derivedId').val(value.id);
    $('#ddlCompanyName').val(value.companyName);
    $('#textCompanyId').val(value.companyId);
    // $("#baseServer").text(derivedLicenseData.serverDbCount - value.availableCount.serverDbUsedCount);
    $("#baseDB").text(derivedLicenseData.databaseCount - value.availableCount.databaseUsedCount);
    $("#baseReplica").text(derivedLicenseData.replicationCount - value.availableCount.replicationUsedCount);
    $("#baseApplication").text(derivedLicenseData.applicationCount - value.availableCount.applicationUsedCount);
    $("#baseNetwork").text(derivedLicenseData.networkCount - value.availableCount.networkUsedCount);
    $("#baseStorage").text(derivedLicenseData.storageCount - value.availableCount.storageUsedCount);
    $("#baseVirtual").text(derivedLicenseData.virtualizationCount - value.availableCount.virtualizationUsedCount);
    $("#baseThird").text(derivedLicenseData.thirdPartyCount - value.availableCount.thirdPartyUsedCount);
    $("#baseDns").text(derivedLicenseData.dnsCount - value.availableCount.dnsUsedCount);
    //$('#derivedServer').val(derivedLicenseData.serverDbCount);
    //servercount = derivedLicenseData.serverDbCount
    $('#derivedDB').val(derivedLicenseData.databaseCount);
    databasecount = derivedLicenseData.databaseCount
    $('#derivedReplica').val(derivedLicenseData.replicationCount);
    replicationcount = derivedLicenseData.replicationCount
    $('#derivedApplication').val(derivedLicenseData.applicationCount);
    applicationcount = derivedLicenseData.applicationCount
    $('#derivedNetwork').val(derivedLicenseData.networkCount);
    networkcount = derivedLicenseData.networkCount
    $('#derivedStorage').val(derivedLicenseData.storageCount);
    storagecount = derivedLicenseData.storageCount
    $('#derivedVirtual').val(derivedLicenseData.virtualizationCount);
    virtualcount = derivedLicenseData.virtualizationCount
    $('#derivedThirdParty').val(derivedLicenseData.thirdPartyCount);
    thirdpartycount = derivedLicenseData.thirdPartyCount
    $('#derivedDns').val(derivedLicenseData.dnsCount);
    dnscount = derivedLicenseData.dnsCount
    const errorElement = ['#db-error', '#replica-error', '#application-error', '#network-error', '#storage-error', '#virtual-error', '#thirdParty-error', '#dns-error', '#Company-error'];
    errorElement.forEach(element => {
        $(element).text('').removeClass('field-validation-error')
    });
}


    document.addEventListener("DOMContentLoaded", function () {
        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltipTriggerList.forEach(el => new bootstrap.Tooltip(el));

    });


