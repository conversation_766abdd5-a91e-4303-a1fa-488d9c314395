﻿using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;
using System.Linq.Expressions;

namespace ContinuityPatrol.Domain.Extensions;

public static class QueryableExtensions
{
    public static async Task<PaginatedResult<T>> ToPaginatedListAsync<T>(this IQueryable<T> source, int pageNumber,
            int pageSize)
            where T : class
    {
        if (source == null) throw new ApiException();
        pageNumber = pageNumber <= 0 ? 1 : pageNumber;
        pageSize = pageSize == 0 ? 10000 : pageSize;
        pageSize = pageSize > 10000 ? 10000 : pageSize;
        var count = await source.CountAsync();
        var items = await source.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToListAsync();
        return PaginatedResult<T>.Success(items, count, pageNumber, pageSize);
    }
    public static async Task<PaginatedResult<T>> ToSortedPaginatedListAsync<T>(
    this IQueryable<T> source,
    int pageNumber,
    int pageSize,
    string sortColumn,
    string sortOrder)
    where T : class
    {
        if (source == null) throw new ApiException();
        pageNumber = pageNumber <= 0 ? 1 : pageNumber;
        pageSize = pageSize == 0 ? 10000 : pageSize;
        pageSize = pageSize > 10000 ? 10000 : pageSize;
       
        if (sortColumn.IsNotNullOrWhiteSpace())
        {
            var orderByExpression = GetOrderByExpression<T>(sortColumn);
            source = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                ? source.OrderByDescending(orderByExpression)
                : source.OrderBy(orderByExpression);
        }
        var count = await source.CountAsync();
        var items = await source.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToListAsync();

        return PaginatedResult<T>.Success(items, count, pageNumber, pageSize);
    }

    private static Expression<Func<T, object>> GetOrderByExpression<T>(string sortColumn)
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        var property = Expression.Property(parameter, sortColumn);
        var converted = Expression.Convert(property, typeof(object));
        return Expression.Lambda<Func<T, object>>(converted, parameter);
    }
    public static IQueryable<T> Specify<T>(this IQueryable<T> query, ISpecification<T> spec) where T : class
    {
        var queryableResultWithIncludes = spec.Includes
            .Aggregate(query,
                (current, include) => current.Include(include));
        var secondaryResult = spec.IncludeStrings
            .Aggregate(queryableResultWithIncludes,
                (current, include) => current.Include(include));
        return secondaryResult.Where(spec.Criteria);
    }

    public static IQueryable<T> Active<T>(this IQueryable<T> query) where T : AuditableEntity
    {
        return query.Where(x => x.IsActive);
    }

    public static IQueryable<T> DescOrderById<T>(this IQueryable<T> query) where T : BaseEntity
    {
        return query.Where(x => x.IsActive)
            .AsNoTracking()
            .OrderByDescending(x => x.Id);
    }

   
}