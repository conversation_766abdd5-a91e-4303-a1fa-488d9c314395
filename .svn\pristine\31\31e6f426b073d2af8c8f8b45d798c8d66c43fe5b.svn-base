﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IWorkflowInfraObjectRepository : IRepository<WorkflowInfraObject>
{
    Task<bool> WorkflowIdAndInfraObjectIdUnique(string workflowId, string infraObjectId, string actionType);
    Task<WorkflowInfraObject> GetWorkflowInfraObjectByWorkflowIdAsync(string workflowId);
    Task<WorkflowInfraObject> GetWorkflowIdAttachByActionType(string workflowId, string actionType);

    Task<WorkflowInfraObject> GetInfraObjectIdAttachByWorkflowId(string workflowId, string infraObjectId,
        string actionType);

    Task<List<WorkflowInfraObject>> GetWorkflowInfraObjectFromInfraObjectId(string infraObjectId);
    Task<List<WorkflowInfraObject>> GetInfraObjectFromWorkflowId(string workflowId);

    Task<List<WorkflowInfraObject>> GetWorkflowInfraObjectByInfraObjectIdAndActionType(string infraObjectId,
        string actionType);

    Task<List<WorkflowInfraObject>> GetWorkflowInfraObjectFromWorkflowId(string workflowId, string infraObjectId);
    Task<bool> IsWorkflowIdUnique(string workflowId);
    Task<List<WorkflowInfraObject>> GetWorkflowInfraObjectDetailByInfraObjectId(string infraObjectId);
    Task<bool> IsInfraObjectIdUnique(string infraObjectId);
    Task<List<WorkflowInfraObject>> GetResilienceWorkflowByInfraObjectId(string infraObjectId);
    Task<WorkflowInfraObject> GetWorkflowInfraObjectByWorkflowIdForWorkflowList(string workflowId);
}