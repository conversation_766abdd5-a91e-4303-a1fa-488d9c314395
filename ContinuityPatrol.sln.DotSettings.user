﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/Environment/Highlighting/HighlightingSourceSnapshotLocation/@EntryValue">C:\Users\<USER>\AppData\Local\Temp\JetBrains\ReSharperPlatformVs17\vAny_cadffae9\CoverageData\_ContinuityPatrol.1037552401\Snapshot\snapshot.utdcvr</s:String>
	
	
	
	
	
	
	
	
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=151124a7_002Dc73d_002D44d9_002Dbd0b_002D49a10b56eacf/@EntryIndexedValue">&lt;SessionState ContinuousTestingIsOn="True" ContinuousTestingMode="3" Name="All tests from InfraObjectRepositoryTests.cs" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;ProjectFolder&gt;A6F7FE22-A620-4C2F-ADCD-ECEFA588EDDC/d:Repository&lt;/ProjectFolder&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=45c56eb1_002D4f40_002D4e83_002D89e7_002D52c3fdd13e08/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="Repository_ShouldCacheRepositoryInstance" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;Project Location="E:\CP6 Root Copy\CP6-.net 9 Root\UI\Tests\Infrastructure\ContinuityPatrol.Persistence.UnitTests" Presentation="&amp;lt;tests&amp;gt;\&amp;lt;infrastructure&amp;gt;\&amp;lt;ContinuityPatrol.Persistence.UnitTests&amp;gt;" /&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	
	
	
	</wpf:ResourceDictionary>