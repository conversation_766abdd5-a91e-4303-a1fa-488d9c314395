using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DataSyncOptionsFixture : IDisposable
{
    public List<DataSyncOptions> DataSyncOptionsPaginationList { get; set; }
    public List<DataSyncOptions> DataSyncOptionsList { get; set; }
    public DataSyncOptions DataSyncOptionsDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string Name = "TestDataSyncOption";
    public const string ReplicationType = "TestReplicationType";

    public ApplicationDbContext DbContext { get; private set; }

    public DataSyncOptionsFixture()
    {
        var fixture = new Fixture();

        DataSyncOptionsList = fixture.Create<List<DataSyncOptions>>();

        DataSyncOptionsPaginationList = fixture.CreateMany<DataSyncOptions>(20).ToList();

        DataSyncOptionsPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DataSyncOptionsPaginationList.ForEach(x => x.IsActive = true);
        DataSyncOptionsPaginationList.ForEach(x => x.Name = Name);
        DataSyncOptionsPaginationList.ForEach(x => x.ReplicationType = ReplicationType);

        DataSyncOptionsList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DataSyncOptionsList.ForEach(x => x.IsActive = true);
        DataSyncOptionsList.ForEach(x => x.Name = Name);
        DataSyncOptionsList.ForEach(x => x.ReplicationType = ReplicationType);

        DataSyncOptionsDto = fixture.Create<DataSyncOptions>();
        DataSyncOptionsDto.ReferenceId = Guid.NewGuid().ToString();
        DataSyncOptionsDto.IsActive = true;
        DataSyncOptionsDto.Name = Name;
        DataSyncOptionsDto.ReplicationType = ReplicationType;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
