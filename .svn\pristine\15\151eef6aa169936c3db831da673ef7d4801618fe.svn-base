﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using static ContinuityPatrol.Application.Constants.ErrorMessage;

namespace ContinuityPatrol.Persistence.Repositories;

public class RpoSlaDeviationReportRepository : BaseRepository<RpoSlaDeviationReport>, IRpoSlaDeviationReportRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public RpoSlaDeviationReportRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<RpoSlaDeviationReport>> GetRpoSlaDeviationReportListByBusinessServiceId(
        string businessServiceId)
    {
        var rpoSlaDeviationReports = await _dbContext.RpoSlaDeviationReports
            .Where(x => x.BusinessServiceId == businessServiceId && x.IsActive)
            .ToListAsync();

        return rpoSlaDeviationReports;
    }

    //public async Task<List<RpoSlaDeviationReport>> GetRpoSlaDeviationReportListByStartTimeAndEndTime(
    //    string businessServiceId, string createdDate, string lastModifiedDate)
    //{
    //    var rpoSlaDeviationReports = await _dbContext.RpoSlaDeviationReports
    //        .Where(x => x.BusinessServiceId == businessServiceId && x.CreatedDate.Date >= createdDate.ToDateTime() &&
    //                    x.LastModifiedDate.Date <= lastModifiedDate.ToDateTime() && x.IsActive)
    //        .ToListAsync();

    //    return rpoSlaDeviationReports;
    //}
    public async Task<List<RpoSlaDeviationReport>> GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndBusinessServiceId(
       string businessServiceId, string createdDate, string lastModifiedDate)
    {
        var rpoSlaDeviationReports = await _dbContext.RpoSlaDeviationReports
            .Where(x => x.BusinessServiceId == businessServiceId && x.CreatedDate.Date >= createdDate.ToDateTime() &&
                        x.CreatedDate.Date <= lastModifiedDate.ToDateTime() && x.IsActive)
            .ToListAsync();

        return rpoSlaDeviationReports;
    }


    public async Task<List<RpoSlaDeviationReport>> GetListByStartTimeAndEndTime(string createdDate,
        string lastModifiedDate, string InfraObjectId)
    {
        var rpoSlaDeviationReports = await _dbContext.RpoSlaDeviationReports
            .Where(x => x.InfraObjectId == InfraObjectId && x.CreatedDate.Date >= createdDate.ToDateTime() &&
                        x.LastModifiedDate.Date <= lastModifiedDate.ToDateTime() && x.IsActive)
            .ToListAsync();

        return rpoSlaDeviationReports;
    }

    public async Task<List<RpoSlaDeviationReport>> GetRpoSlaDeviationReportListByInfraObjectId(string infraObjectId)
    {
        var rpoSlaDeviationReports = await _dbContext.RpoSlaDeviationReports
           .Where(x => x.InfraObjectId == infraObjectId && x.IsActive)
           .ToListAsync();

        return rpoSlaDeviationReports;
    }
    public async Task<List<RpoSlaDeviationReport>> GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndInfraObjectId(
      string infraObjectId, string createdDate, string lastModifiedDate)
    {
        var rpoSlaDeviationReports = await _dbContext.RpoSlaDeviationReports
            .Where(x => x.InfraObjectId == infraObjectId && x.CreatedDate.Date >= createdDate.ToDateTime() &&
                        x.CreatedDate.Date <= lastModifiedDate.ToDateTime() && x.IsActive)
            .ToListAsync();

        return rpoSlaDeviationReports;
    }
}