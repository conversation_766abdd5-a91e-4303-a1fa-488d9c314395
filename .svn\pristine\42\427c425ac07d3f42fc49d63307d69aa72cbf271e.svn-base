﻿namespace ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;

public record GetWorkflowProfileInfoByProfileIdVm
{
    public string ProfileId { get; set; }
    public string ProfileName { get; set; }

    public List<WorkflowProfileInfoDto> WorkflowProfileInfos { get; set; } = new();
}

public record WorkflowProfileInfoDto
{
    public string Id { get; set; }
    public string ProfileId { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string State { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string WorkflowVersion { get; set; }
    public string CurrentActionId { get; set; }
    public string Status { get; set; }
    public string CurrentActionName { get; set; }
    public string Message { get; set; }
    public string ProgressStatus { get; set; }
    public int ConditionalOperation { get; set; }
    public string WorkflowType { get; set; }
    public string ActionMode { get; set; }
    public bool IsLock { get; set; }
    public bool IsParallel { get; set; }
    public bool IsRunning { get; set; }
    public string CustomId { get; set; }
    public bool IsCustom { get; set; }
    public int Index { get; set; }
    public int TotalCount { get; set; }
}