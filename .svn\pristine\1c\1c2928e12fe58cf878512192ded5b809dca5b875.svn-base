using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.Create;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.Update;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.UpdateJobState;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.UpdateJobStatus;
using ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.CyberJobManagementModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class CyberJobManagementFixture
{
    public CreateCyberJobManagementCommand CreateCyberJobManagementCommand { get; }
    public UpdateCyberJobManagementCommand UpdateCyberJobManagementCommand { get; }
    public DeleteCyberJobManagementCommand DeleteCyberJobManagementCommand { get; }
    public UpdateCyberJobManagementStateCommand UpdateCyberJobManagementStateCommand { get; }
    public UpdateCyberJobManagementStatusCommand UpdateCyberJobManagementStatusCommand { get; }
    public CyberJobManagementListVm CyberJobManagementListVm { get; }
    public CyberJobManagementDetailVm CyberJobManagementDetailVm { get; }
    public CyberJobManagementStatusVm CyberJobManagementStatusVm { get; }

    public CyberJobManagementFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateCyberJobManagementCommand> (c => c
            .With(b => b.Name, "Critical Database Backup Job")
            .With(b => b.AirgapId, Guid.NewGuid().ToString())
            .With(b => b.AirgapName, "Enterprise Air Gap Replication")
            .With(b => b.WorkflowId, Guid.NewGuid().ToString())
            .With(b => b.WorkflowName, "Database Backup and Replication Workflow")
            .With(b => b.SolutionId, Guid.NewGuid().ToString())
            .With(b => b.SolutionName, "Enterprise Data Protection Solution")
            .With(b => b.IsSchedule, 1)
            .With(b => b.ScheduleType, 2)
            .With(b => b.ScheduleTime, "02:00:00")
            .With(b => b.CronExpression, "0 0 2 * * ?")
            .With(b => b.Status, "Active")
            .With(b => b.State, "Scheduled")
            .With(b => b.NodeId, Guid.NewGuid().ToString())
            .With(b => b.NodeName, "Primary Backup Node")
            .With(b => b.ExceptionMessage, (string)null));

        fixture.Customize<UpdateCyberJobManagementCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Updated Critical Database Backup Job")
            .With(b => b.AirgapId, Guid.NewGuid().ToString())
            .With(b => b.AirgapName, "Enhanced Enterprise Air Gap Replication")
            .With(b => b.WorkflowId, Guid.NewGuid().ToString())
            .With(b => b.WorkflowName, "Enhanced Database Backup and Replication Workflow")
            .With(b => b.SolutionId, Guid.NewGuid().ToString())
            .With(b => b.SolutionName, "Enhanced Enterprise Data Protection Solution")
            .With(b => b.IsSchedule, 1)
            .With(b => b.ScheduleType, 1)
            .With(b => b.ScheduleTime, "01:30:00")
            .With(b => b.CronExpression, "0 30 1 * * ?")
            .With(b => b.Status, "Active")
            .With(b => b.State, "Running")
            .With(b => b.NodeId, Guid.NewGuid().ToString())
            .With(b => b.NodeName, "Enhanced Primary Backup Node")
            .With(b => b.ExceptionMessage, (string)null));

        fixture.Customize<DeleteCyberJobManagementCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<UpdateCyberJobManagementStateCommand>(c => c
            .With(b => b.updateCyberJobStates, new List<UpdateCyberJobState>
            {
                new UpdateCyberJobState
                {
                    Id = Guid.NewGuid().ToString(),
                    State = "Running"
                },
                new UpdateCyberJobState
                {
                    Id = Guid.NewGuid().ToString(),
                    State = "Completed"
                }
            }));

        fixture.Customize<UpdateCyberJobManagementStatusCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Status Update Job")
            .With(b => b.Status, "Completed"));

        fixture.Customize<CyberJobManagementListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, () => $"Job-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.AirgapId, Guid.NewGuid().ToString())
            .With(b => b.AirgapName, () => $"AirGap-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.WorkflowId, Guid.NewGuid().ToString())
            .With(b => b.WorkflowName, () => $"Workflow-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.SolutionId, Guid.NewGuid().ToString())
            .With(b => b.SolutionName, () => $"Solution-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.IsSchedule, () => fixture.Create<int>() % 2)
            .With(b => b.ScheduleType, () => fixture.Create<int>() % 3 + 1)
            .With(b => b.ScheduleTime, () => $"{fixture.Create<int>() % 24:D2}:{fixture.Create<int>() % 60:D2}:00")
            .With(b => b.CronExpression, () => $"0 {fixture.Create<int>() % 60} {fixture.Create<int>() % 24} * * ?")
            .With(b => b.Status, () => fixture.Create<bool>() ? "Active" : "Inactive")
            .With(b => b.State, () => fixture.Create<bool>() ? "Running" : "Scheduled")
            .With(b => b.NodeId, Guid.NewGuid().ToString())
            .With(b => b.NodeName, () => $"Node-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.ExceptionMessage, () => fixture.Create<bool>() ? null : $"Error-{fixture.Create<string>().Substring(0, 10)}")
            .With(b => b.LastExecutedTime, () => DateTime.Now.AddHours(-(fixture.Create<int>() % 24)).ToString()));

        fixture.Customize<CyberJobManagementDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Enterprise Mission-Critical Backup and Recovery Job")
            .With(b => b.AirgapId, Guid.NewGuid().ToString())
            .With(b => b.AirgapName, "Enterprise Air Gap Infrastructure")
            .With(b => b.WorkflowId, Guid.NewGuid().ToString())
            .With(b => b.WorkflowName, "Comprehensive Data Protection and Recovery Workflow")
            .With(b => b.SolutionId, Guid.NewGuid().ToString())
            .With(b => b.SolutionName, "Enterprise Cyber Resilience Solution")
            .With(b => b.IsSchedule, 1)
            .With(b => b.ScheduleType, 2)
            .With(b => b.ScheduleTime, "02:00:00")
            .With(b => b.CronExpression, "0 0 2 * * ? *")
            .With(b => b.Status, "Active")
            .With(b => b.State, "Scheduled")
            .With(b => b.NodeId, Guid.NewGuid().ToString())
            .With(b => b.NodeName, "Enterprise Backup Orchestration Node")
            .With(b => b.ExceptionMessage, (string)null)
            .With(b => b.LastExecutedTime, DateTime.Now.AddHours(-2).ToString("yyyy-MM-dd HH:mm:ss")));

        fixture.Customize<CyberJobManagementStatusVm>(c => c
            .With(b => b.Status, "Pending")
            .With(b => b.Count, 20));

        CreateCyberJobManagementCommand = fixture.Create<CreateCyberJobManagementCommand>();
        UpdateCyberJobManagementCommand = fixture.Create<UpdateCyberJobManagementCommand>();
        DeleteCyberJobManagementCommand = fixture.Create<DeleteCyberJobManagementCommand>();
        UpdateCyberJobManagementStateCommand = fixture.Create<UpdateCyberJobManagementStateCommand>();
        UpdateCyberJobManagementStatusCommand = fixture.Create<UpdateCyberJobManagementStatusCommand>();
        CyberJobManagementListVm = fixture.Create<CyberJobManagementListVm>();
        CyberJobManagementDetailVm = fixture.Create<CyberJobManagementDetailVm>();
        CyberJobManagementStatusVm = fixture.Create<CyberJobManagementStatusVm>();
    }
}
