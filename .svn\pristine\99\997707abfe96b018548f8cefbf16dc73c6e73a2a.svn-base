﻿
const nameExistUrl = "Manage/ReplicationJob/IsReplicationJobNameExist";
const url = "Manage/ReplicationJob/List";
let GroupPolicy = '', solutiontype = [], Arraydata = [], selectedValues = [], dataTable
const exceptThisSymbols = ["e", "E", "+", "-", "."];
let createPermission = $("#ReplicationCreate").data("create-permission").toLowerCase();
let deletePermission = $("#ReplicationDelete").data("delete-permission").toLowerCase();
function replicationjobdebouncedebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
if (createPermission == 'false') {
    $("#CreteButton").removeClass('#CreteButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
}
$(function () {
    dataTable = $('#tblJobManagement').DataTable(

        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""

            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/Manage/ReplicationJob/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 2 ? "name" : sortIndex === 3 ? "templateName" : sortIndex === 4 ? "nodeName" :
                        sortIndex === 5 ? "solutionType" : sortIndex === 6 ? "scheduleTime" : sortIndex === 7 ? "lastExecutionTime" : sortIndex === 8 ? "status" : sortIndex === 9 ? "state" : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json?.success) {
                        json.recordsTotal = json?.data?.totalPages;
                        json.recordsFiltered = json?.data?.totalCount;
                        if (json?.data?.data?.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        return json?.data?.data
                    } else {
                        errorNotification(json)
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [2, 3, 4, 5, 6, 7, 8, 9],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    }, "orderable": false,
                },
                {
                    "data": null, "name": "CheckboxAll", "autoWidth": true, "orderable": false,
                    "render": function (data, type, full, meta) {

                        return '<input type="checkbox" name="rowCheckbox" class="form-check-input ' + data.state + '" checkid="' + data.id + '" id="' + data.state + '">';

                    }
                },
                {
                    "data": "name", "name": "Job Name", "autoWidth": true,
                    "render": function (data, type, row) {

                        return `<td><span title="${data == null ? " NA" : data}">${data == null ? "NA" : data}</span></td>`;

                    }
                },
                {
                    "data": "templateName", "name": "Template Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data == null ? "NA" : data + '">' + data == null ? "NA" : data + '</span>';
                        }
                        return data;
                    }
                }, {
                    "data": "nodeName", "name": "Node Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title="${data || "NA"}">${data || "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "solutionType", "name": "Solution Type", "autoWidth": true,
                    "render": function (data, type, row) {

                        solutiontype.push(data)
                        if (type === 'display') {
                            return '<span title="' + data == null ? "NA" : data + '">' + data == null ? "NA" : data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "scheduleTime", "name": "Schedule Time", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `
                            <td><span title="${data == null ? "NA" : data}"> ${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "data": "lastExecutionTime", "name": "Last Monitoring Time", "autoWidth": true,
                    "render": function (data, type, row) {

                        return `<td><span title="${data == null ? "NA" : data}">${data == null ? "NA" : data}</span></td>`;

                    }
                },
                {
                    "data": "status", "name": "Status", "autoWidth": true,
                    "render": function (data, type, row) {
                        var iconClass = '';
                        if (data == "Pending" || data == null) {
                            iconClass = "cp-pending text-warning me-1";
                        } else if (data == "Running") {
                            iconClass = "text-primary cp-reload cp-animate me-1";
                        } else if (data == "Success") {
                            iconClass = "cp-success text-success me-1";
                        } else {
                            iconClass = "cp-error text-danger me-1";
                        }
                        return `<td><i class="${iconClass}"></i></td>
                              <td><span title="${data == null ? "Pending" : data}"> ${data == null ? "Pending" : data}</span></td>`;
                    }

                },
                {
                    "data": "state", "name": "State", "autoWidth": true,
                    "render": function (data, type, row) {

                        var iconClass = '';
                        if (data == "Active") {
                            iconClass = "cp-active-inactive text-success me-1";
                        } else if (data === 'InActive') {
                            iconClass = "cp-active-inactive text-danger me-1";
                        } else if (data === null) {
                            iconClass = "cp-active-inactive text-success me-1";
                        }
                        return `<td><i class="${iconClass}" id="icon" title="${data}" ></i></td>
                              <td><span id="jobmanagestate"> ${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "render": function (data, type, row) {
                        //if (createPermission === 'true' && deletePermission === "true") {
                        let errorVisible = row?.exceptionMessage === null || row?.exceptionMessage === undefined || row?.exceptionMessage === '';
                        let errmsg = row?.exceptionMessage
                        if (createPermission == "true" && deletePermission == "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="edit-button ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}"  data-job='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="delete-button ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}" data-job-id="${row.id}" data-job-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ViewHistory">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span>      
                                                      <span title="Error Message" job_error_message="'${btoa(errmsg)}'" class=" Error-button ${errorVisible ? 'd-none' : ''}"   role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-error-message"></i>                                    
                                                    </span>      
                                </div>`;
                        }
                        if (createPermission == "false" && deletePermission == "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="delete-button ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}" data-job-id="${row.id}" data-job-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ViewHistory">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span>      
                                                      <span title="Error Message" job_error_message="'${btoa(errmsg)}'" class=" Error-button ${errorVisible ? 'd-none' : ''}"   role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-error-message"></i>                                    
                                                    </span>      
                                </div>`;
                        }
                        if (createPermission == "true" && deletePermission == "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="edit-button ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}"  data-job='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ViewHistory">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span>      
                                                      <span title="Error Message" job_error_message="'${btoa(errmsg)}'" class=" Error-button ${errorVisible ? 'd-none' : ''}"   role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-error-message"></i>                                    
                                                    </span>      
                                </div>`;
                        }
                        if (createPermission == "false" && deletePermission == "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span  title="Reset" id="reset" class="icon-disabled"
                                                       data-bs-target="#ViewHistory">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span>      
                                                      <span title="Error Message" job_error_message="'${btoa(errmsg)}'" class=" Error-button ${errorVisible ? 'd-none' : ''}"   role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-error-message"></i>                                    
                                                    </span>      
                                </div>`;
                        }
                        //}
                        //else if (createPermission === 'true' && deletePermission === "false") {
                        //    return `
                        //    <div class="d-flex align-items-center gap-2">                                     
                        //                        <span role="button" title="Edit"  class="edit-button" data-job='${JSON.stringify(row)}'>
                        //                            <i class="cp-edit"></i>
                        //                        </span>  
                        //                        <span role="button" title="Delete"  class="icon-disabled"><i class="cp-Delete"></i>
                        //                            </span>      
                        //                                <span id="reset" title="Reset"  class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                        //                                   data-bs-target="#ViewHistory">
                        //                                    <i class="cp-job-reset"></i>                                    
                        //                                </span>                                 
                        //            </div>`;
                        //}
                        //else if (createPermission === 'false' && deletePermission === "true") {
                        //    return `
                        //    <div class="d-flex align-items-center gap-2">                                     
                        //                        <span role="button" title="Edit" class="icon-disabled">
                        //                            <i class="cp-edit"></i>
                        //                        </span>  
                        //                        <span role="button" title="Delete"  class="delete-button" data-job-id="${row.id}" data-job-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                        //                            </span>      
                        //                                <span  id="reset" title="Reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                        //                                   data-bs-target="#ViewHistory">
                        //                                    <i class="cp-job-reset"></i>                                    
                        //                                </span>                                 
                        //            </div>`;
                        //}
                        //else {
                        //    return `
                        //    <div class="d-flex align-items-center gap-2">                                     
                        //                        <span role="button" title="Edit" class="icon-disabled">
                        //                            <i class="cp-edit"></i>
                        //                        </span>  
                        //                        <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                        //                            </span>      
                        //                                <span  id="reset" title="Reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                        //                                   data-bs-target="#ViewHistory">
                        //                                    <i class="cp-job-reset"></i>                                    
                        //                                </span>                                 
                        //            </div>`;
                        //}
                    }, "orderable": false,
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });
    $('#search-inp').on('keydown input', replicationjobdebouncedebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        } else {
            const JobNameCheckbox = $("#JobName");
            const TemplatenameCheckbox = $("#Templatename");
            const SolutiontypeCheckBox = $("#Solutiontype");
            const StateCheckBox = $("#State");
            const inputValue = $('#search-inp').val();
            if (JobNameCheckbox.is(':checked')) {
                selectedValues.push(JobNameCheckbox.val() + inputValue);
            }
            if (TemplatenameCheckbox.is(':checked')) {
                selectedValues.push(TemplatenameCheckbox.val() + inputValue);
            }
            if (SolutiontypeCheckBox.is(':checked')) {
                selectedValues.push(SolutiontypeCheckBox.val() + inputValue);
            }
            if (StateCheckBox.is(':checked')) {
                selectedValues.push(StateCheckBox.val() + inputValue);
            }
            var currentPage = dataTable.page.info().page + 1;
            if (!isNaN(currentPage)) {
                dataTable.ajax.reload(function (json) {
                    if (e.target.value && json?.recordsFiltered === 0) {
                        $('.dataTables_empty').text('No matching records found');
                    }
                }, false)
            }
        }
    }));
})
$('#selectExecutionPolicy').on('change', function () {
    if ($(this).val() == '1') {
        $('#groupPolicy').show();
    }
    else {
        $('#groupPolicy').hide();
    }
})
$('#Activebtn').on('click',async function () {
    let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
    let datas = []
    checkFind?.forEach((obj, idx) => {
        if (obj.checked && obj.id != "Active") {
            datas.push({
                "Id": obj.getAttribute("checkid"),
                "State": "Active"
            })
        }
    })
    if (datas.length) {
       await $.ajax({
            url: "/Manage/ReplicationJob/UpdateReplicationJobState",
            type: 'PUT',
            data: { "UpdateReplicationJobStates": datas, __RequestVerificationToken: gettoken() },
            success: function (result) {
                if (result?.success) {
                    var data = result?.data
                    $('input[name="rowCheckbox"]').prop("checked", false)
                    $('input[name="checkboxAll"]').prop("checked", false)
                    notificationAlert("success", data?.message)
                    setTimeout(() => {
                        location.reload();
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            },
        });
    } else {
        if ($('input[name="checkboxAll"]').prop("checked") == true || $('input[name="rowCheckbox"]').prop("checked") == true) {
            if (datas?.length == 0) {
                if (datas?.length == 0) {
                    notificationAlert("warning", "Jobs state has already updated to 'Active' state")
                    setTimeout(() => {
                        location.reload();
                    }, 2000)
                }
            }
        }
    }
})
$('#Inactivebtn').on('click',async function () {
    let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
    let datas = []
    checkFind?.forEach((obj, idx) => {
        if (obj.checked && obj.id != "InActive") {
            datas.push({
                "Id": obj.getAttribute("checkid"),
                "State": "InActive"
            })
        }
    })
    if (datas.length) {
      await  $.ajax({
            url: "/Manage/ReplicationJob/UpdateReplicationJobState",
            type: 'PUT',
            data: { "UpdateReplicationJobStates": datas, __RequestVerificationToken: gettoken() },
            success: function (result) {
                if (result.success) {
                    var data = result?.data
                    $('input[name="rowCheckbox"]').prop("checked", false)
                    $('input[name="checkboxAll"]').prop("checked", false)
                    notificationAlert("success", data?.message)
                    setTimeout(() => {
                        location.reload();
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            },
        });
    } else {
        if ($('input[name="checkboxAll"]').prop("checked") == true || $('input[name="rowCheckbox"]').prop("checked") == true) {
            if (datas?.length == 0) {
                notificationAlert("warning", "Jobs state has already updated to 'InActive' state ")
                setTimeout(() => {
                    location.reload();
                }, 2000)
            }
        }
    }
})
$("#flexCheckDefault").on('change', function (e) {
    setTimeout(() => {
        if (e.target.checked) {
            $('input[name="rowCheckbox"]').prop("checked", true);

        } else {
            $('input[name="rowCheckbox"]').prop("checked", false)
        }
    }, 100)

})
$("#tblJobManagement").on('change', 'input[name="rowCheckbox"]', function (e) {
    $('input[name="checkboxAll"]').prop("checked", false)
})
$("#selectTemplateName").on('change',
    function () {
        var selectTemplateName = $("#selectTemplateName option:selected").attr('id');
        $('#textTemplateId').val(selectTemplateName);
        $('#textTemplateName').val($("#selectTemplateName option:selected").text());
        const value = $(this).val();
        var filtervalue = $.grep(value, function (value) {
            return value !== "";
        });
        validateJobDropDown(filtervalue, "Select template name", $('#TemplateName-error'));
    });
$('#selectExecutionPolicy').on('change', function () {
    GroupPolicy = $(this).val()
    $("#selectGroupPolicy").val('').trigger('change');
    $('#GroupPolicy-error').text('').removeClass('field-validation-error');
    validateJobDropDown($(this).val(), "Select execution policy", $('#ExecutionPolicy-error'));
})
$("#selectInfraObjectName").on('change', function () {
    var selectInfraObjectName = $(this).find('option:selected');
    Arraydata = []
    let strArrayData = '';
    selectInfraObjectName?.each(function () {
        let option = $(this);
        let id = option.attr('id');
        let obj = { Id: id, Name: option.text() };
        Arraydata.push(obj)
    });
    strArrayData = JSON.stringify(Arraydata)
    $('#textInfraObjectProperties').val(strArrayData)
    validateJobDropDown($(this).val(), "Select infraObject name", $('#InfraObjectName-error'));
    cronshow()
});
function cronshow() {
    if ($("#selectInfraObjectName").val().length > 0) {
        $("#cron").show()
    } else {
        $("#cron").hide()
    }
}
cronshow()
// Error message
$('#tblJobManagement').on('click', '.Error-button', function () {
    let noData = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img" style="padding:10px">'
    let job_error_message = $(this).attr('job-error_message')
    job_error_message = atob(job_error_message)
    if (!job_error_message || job_error_message == 'null') {
        $("#error_message").css('text-align', 'center')
            .html(noData);
    } else {
        $('#error_message').text(job_error_message);
    }
});
//Delete
$('#tblJobManagement').on('click', '.delete-button', function () {
    var jobId = $(this).data('job-id');
    var jobName = $(this).data('job-name');
    $("#deleteData").attr("title", jobName).text(jobName);
    $('#textDeleteId').val(jobId);
});
//Update   
$('#tblJobManagement').on('click', '.edit-button', function () {
    var jobData = $(this).data("job");
    populateModalFields(jobData);
    Tab_selection(jobData);
    Tab_schedule_type(jobData);
    $('#SaveFunction').text("Update");
    ClearJobErrorElements();
    $('#CreateModal').modal('show');
});
//reset
$('#tblJobManagement').on('click', '.reset-button',async function () {
    var jobData = $(this).data('job')
    jobData.__RequestVerificationToken = gettoken()
   await $.ajax({
        url: "/Manage/ReplicationJob/ResetJobStatus",
        type: 'POST',
        data: jobData,
        success: function (result) {
            if (result?.success) {
                notificationAlert("success", result?.data?.message);
                setTimeout(() => {
                    dataTable.ajax.reload();
                }, 2000)
            } else {
                errorNotification(result)
            }
        }
    });
});

$('#textJobName').on('input', replicationjobdebouncedebounce(async function () {
    let value = await sanitizeInput($("#textJobName").val());
    $("#textJobName").val(value);
    await validateJobName(value, $('#textJobId').val(), nameExistUrl);
}));

$('#selectTemplateName').on('change', function (event) {
    validateJobDropDown($(this).val(), "Select workflow templates", $('#TemplateName-error'));
});

$('#selectSolutionType').on('change', function (event) {
    validateJobDropDown($(this).val(), "Select solution type", $('#SolutionType-error'));
    var value = $("#selectSolutionType option:selected").attr('id');
    getInfraObjectDetails(value)
    $("#solutionTypeId").val(value);
    $("#solutionType").val($(this).val());
    //if ($("#SaveFunction").text() != "Update") {
    //    solutiontype.forEach(function (values, index) {
    //        if (values == selectsolutiontype) {
    //            setTimeout(() => {
    //                $("#selectSolutionType").val("").trigger("change")
    //                $('#SolutionType-error').text("Already scheduled the job").addClass('field-validation-error');
    //                return false;
    //            }, 400)
    //        } else {
    //            $('#SolutionType-error').text('').removeClass('field-validation-error');
    //            return true;
    //        }
    //    });
    //}
});

$('#selectGroupPolicy').on('change', function (event) {
    var val = $("#selectExecutionPolicy").val();
    if (val == "1") {
        var Id = $("#selectGroupPolicy option:selected").attr('id');
        $('#policyid').val(Id);
        validateJobDropDown($(this).val(), "Select group node policy", $('#GroupPolicy-error'));
    }
    if (val == "2") {
        $('#policyid').val("");
    }
});

$('#textStateActive').on('click', function (event) {
    ValidateRadioButton($('#state-error'));
});

$('#textStateInactive').on('click', function (event) {
    ValidateRadioButton($('#state-error'));
});

$('#txtMins').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#txtMins').val('');
    }
    if ($(this).val() == 0 || $(this).val() > 59) {
        $('#txtMins').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMinJobNumber($(this).val(), "Enter minutes", $('#CronMin-error'));
});

$('#txtHours').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#txtHours').val('');
    }
    if ($(this).val() > 23 || $(this).val() == 0) {
        $('#txtHours').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateHourJobNumber($(this).val(), "Enter hours", $('#CronHourly-error'));
});

$('#txtMinutes').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#txtMinutes').val('');
    }
    if ($(this).val() > 59) {
        $('#txtMinutes').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMiniteJobNumber($(this).val(), "Enter minutes", $('#CronHourMin-error'));
});
$("#txtMinutes,#txtHours").on("input", function () {
    if ($("#txtMinutes").val() == "00" && $("#txtHours").val() == "00" || $("#txtMinutes").val() == "0" && $("#txtHours").val() == "0") {
        $("#txtMinutes").val("")
        setTimeout(() => {
            $('#CronHourMin-error').text("Enter the proper hours and minites")
        }, 200)
    }
})
$('#everyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#everyHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#CroneveryHour-error'));
});

$('#everyMinutes').on('click', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69) {
        event.preventDefault();
        $('#everyMinutes').val('');
    }
    validateMinJobNumber($(this).val(), "Select minutes", $('#CroneveryMin-error'));
});
$('#ddlHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        $('#ddlHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#CronddlHour-error'));
});

$('#ddlMinutes').on('click', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69) {
        event.preventDefault();
        $('#ddlMinutes').val('');
    }
    validateMinJobNumber($(this).val(), "Select hours", $('#CronddlMin-error'));
});

$('#MonthlyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#MonthlyHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#MonthlyHours-error'));
});
function srvTime() {
    try {
        //FF, Opera, Safari, Chrome
        xmlHttp = new XMLHttpRequest();
    }
    catch (err1) {
        //IE
        try {
            xmlHttp = new ActiveXObject('Msxml2.XMLHTTP');
        }
        catch (err2) {
            try {
                xmlHttp = new ActiveXObject('Microsoft.XMLHTTP');
            }
            catch (eerr3) {
                //AJAX not supported, use CPU time.
                alert("AJAX not supported");
            }
        }
    }
    xmlHttp.open('HEAD', window.location.href.toString(), false);
    xmlHttp.setRequestHeader("Content-Type", "text/html");
    xmlHttp.send('');
    return xmlHttp.getResponseHeader("Date");
}

$('.datetimeCron').on('change', function () {
    validateDayNumber($(this).val(), "Select schedule time", $('#CronExpression-error'));
    let selectdate = new Date($(this).val())
    let currentdate = new Date(srvTime())
    if (selectdate > currentdate) {
        $('#CronExpression-error').text('').removeClass('field-validation-error');
        return true;
    } else if (selectdate < currentdate) {
        $('#CronExpression-error').text("Select schedule date and time should be greater than current date and time").addClass('field-validation-error');
        return false;
    }
});

$('#lblMonth').on("change", function () {
    $('input[name="Monthyday"]').prop("checked", false)
    validateDayNumber($(this).val(), "Select month and year", $('#CronMonthly-error'));
    var selectedDate = new Date($(this).val());

    var currentDate = new Date();
    const getDays = (year, month) => {
        return new Date(year, month, 0).getDate();
    };
    const daysInmonth = getDays(selectedDate.getFullYear(), selectedDate.getMonth() + 1)
    for (let i = 0; i < daysInmonth; i++) {
        var data = ""
        data = i + 1
        $('input[name="Monthyday"]')?.each(function () {
            var checkboxValue = parseInt($(this).val());
            if (checkboxValue > data) {
                $(this).css("display", "none")
            } else {
                $(this).css("display", "block")
            }
        })
        $(".checklabel")?.each(function () {
            var checkboxValue = parseInt($(this).text());
            if (checkboxValue > data) {
                $(this).css("display", "none")
            } else {
                $(this).css("display", "block")
            }
        })
    }
    if ($(this).val() == "") {
        $('input[name="Monthyday"]').prop('disabled', true);
        $('input[name="Monthyday"]').prop('checked', false);
        $("#CronMon-error").text("").removeClass("field-validation-error")
    } else {
        $('input[name="Monthyday"]')?.each(function () {
            var checkboxValue = parseInt($(this).val());
            if ((selectedDate.getMonth() === currentDate.getMonth() && selectedDate.getFullYear() === currentDate.getFullYear() && checkboxValue < currentDate.getDate()) ||
                (selectedDate.getMonth() < currentDate.getMonth() && selectedDate.getFullYear() <= currentDate.getFullYear())) {
                $(this).prop('disabled', true);
            } else {
                $(this).prop('disabled', false);
            }
        })
    }
});

$('input[name=weekDays]').on('click', function () {
    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var Dayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(Dayvalue, "Select day(s)", $('#CronDay-error'));
});

$('input[name=Monthyday]').on('click', function () {
    var checkedCheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    var MonthDayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(MonthDayvalue, "Select date(s)", $('#CronMon-error'));
});

$('.nav-link').on("click", function () {
    ClearCroneElements();
});

$('input[name = "switchPlan"]').on('click', function () {
    ClearCroneElements();
});

$('input[name=daysevery]').on('click', function () {
    ValidateCronRadioButton($('#Crondaysevery-error'));
});
function populateModalFields(jobData) {
    $("#txtlastexecutetime").val(jobData?.lastExecutionTime)
    $('#selectInfraObjectName').empty()
    getInfraObjectDetails(jobData?.solutionTypeId)
    //let Arr = [];
    //if (jobData?.infraObjectProperties != null) {
    //    JSON.parse(jobData?.infraObjectProperties).forEach((d) => {
    //        Arr.push(d.Id)
    //    })

    //}
    setTimeout(() => {
        /* $('#selectInfraObjectName').val(Arr).trigger('change')*/
        $('#selectTemplateName').val(jobData?.templateId).trigger("change")
    }, 500)
    $("#datetimeCronlist").val(jobData?.type)
    $('#policyid').val(jobData?.groupPolicyId);
    $('#textJobId').val(jobData?.id);
    $('#textJobName').val(jobData?.name);
    $('#textTemplateId').val(jobData?.templateId);
    $('#selectSolutionType').val(jobData?.solutionType);
    $('#solutionTypeId').val(jobData?.solutionTypeId);
    $('#textNodeId').val(jobData?.nodeId);
    $('#textNodeName').val(jobData?.nodeName);
    $('#textcompanyId').val(jobData?.companyId);

    //if (jobData.status == null || "Running" || "Success") {
    $('#textStatus').val("Pending");
    //} else {
    //    $('#textStatus').val(jobData.status);
    //}

    //$('#selectGroupPolicy').val(jobData.groupPolicyName);
    $('#selectExecutionPolicy').val(jobData?.executionPolicy);
    if (jobData?.executionPolicy == '1') {
        $('#groupPolicy').show();
        $('#selectGroupPolicy').val(jobData?.groupPolicyName);
    }
    else {
        $('#groupPolicy').hide();
    }
    var scheduleTime = jobData?.scheduleTime.split(" ")
    setTimeout(() => {
        if (jobData?.scheduleTime.includes("Every day") == true) {
            $("#defaultCheck-everyday").prop("checked", true)
            $("#everyHours").val(scheduleTime[4] + ":" + scheduleTime[6])
        }
        if (jobData?.scheduleTime.includes("MON-FRI") == true) {
            $("#defaultCheck-MON-FRI").prop("checked", true)
            $("#everyHours").val(scheduleTime[3] + ":" + scheduleTime[5])
        }
        if (scheduleTime?.length == 7) {
            $("#txtMinutes").val(scheduleTime[5])
            $("#txtHours").val(scheduleTime[1])
        }
        if ($("#defaultCheck-MON-FRI").prop("checked") != true) {
            if (jobData?.scheduleTime.includes("MON") == true) {
                $("#defaultCheck-1").prop("checked", true)
            }
            if (jobData?.scheduleTime.includes("TUE") == true) {
                $("#defaultCheck-2").prop("checked", true)
            }
            if (jobData?.scheduleTime.includes("WED") == true) {
                $("#defaultCheck-3").prop("checked", true)
            }
            if (jobData?.scheduleTime.includes("THU") == true) {
                $("#defaultCheck-4").prop("checked", true)
            }
            if (jobData?.scheduleTime.includes("FRI") == true) {
                $("#defaultCheck-5").prop("checked", true)
            }
            if (jobData?.scheduleTime.includes("SAT") == true) {
                $("#defaultCheck-6").prop("checked", true)
            }
            if (jobData?.scheduleTime.includes("SUN") == true) {
                $("#defaultCheck-0").prop("checked", true)
            }
            $("#ddlHours").val(scheduleTime[2] + ":" + scheduleTime[4])
        }

        if (scheduleTime?.length >= 12) {
            var year = parseInt(scheduleTime[12])
            var month = parseInt(scheduleTime[8] == "JAN" ? "01" : scheduleTime[8] == "FEB" ? "02" : scheduleTime[8] == "MAR" ? "03" : scheduleTime[8] == "APR" ? "04" :
                scheduleTime[8] == "MAY" ? "05" : scheduleTime[8] == "JUN" ? "06" : scheduleTime[8] == "JUL" ? "07" : scheduleTime[8] == "AUG" ? "08" : scheduleTime[8] == "SEP" ? "09" :
                    scheduleTime[8] == "OCT" ? "10" : scheduleTime[8] == "NOV" ? "11" : scheduleTime[8] == "DEC" ? "12" : "")
            if (month <= 9 && month > 0) {
                month = "0" + month;
            }
            else if (month == 0) {
                month = "12";
                year = year - 1;
            }
            var newdate = year + "-" + month;

            $("#lblMonth").val(newdate).trigger("change")
            scheduleTime[5]?.split(",").forEach(function (i) {
                if (i) {
                    $("#inlineCheckbox" + i).prop("checked", true)
                } else {
                    $("#inlineCheckbox" + i).prop("checked", false)
                }
            })
            $("#MonthlyHours").val(scheduleTime[0] + ":" + scheduleTime[2]);
        }
    }, 500)
    if (jobData?.state == "Active") {
        $("#textStateActive").prop("checked", true);
    }
    else {
        $("#textStateInactive").prop("checked", true);
    }
}

$('#SaveFunction').on("click", async function () {

    var form = $("#CreateForm");

    GetIsSchedule();
    Get_ScheduleTypes();

    var isName = await validateJobName($("#textJobName").val(), $("#textJobId").val(), nameExistUrl);

    var isPolicy = validateJobDropDown($('#selectExecutionPolicy').val(), "Select execution policy", $('#ExecutionPolicy-error'));

    var isSolutionType = validateJobDropDown($('#selectSolutionType').val(), "Select solution type", $('#SolutionType-error'));

    var isGroupPolicy = validateJobDropDown($('#selectGroupPolicy').val(), "Select group node policy", $('#GroupPolicy-error'));

    var isWorkflow = validateJobDropDown($('#selectTemplateName').val(), "Select workflow templates", $('#TemplateName-error'));

    var isStateActive = ValidateRadioButton($('#state-error'));
    var isScheduler = CronValidation();
    let crontype = $("#datetimeCronlist").val()
    var { CronExpression, listcron } = JobCronExpression();
    document.getElementById("textCronExpression").value = CronExpression;
    document.getElementById("textScheduleTime").value = listcron;
    document.getElementById("cronexpresstype").value = crontype
    document.getElementById("textStatus").value = "Pending"
    if (isName && isSolutionType && isPolicy && isStateActive && isScheduler && isWorkflow && (GroupPolicy == '1' ? isGroupPolicy : true)) {
        form.trigger("submit");
    }
});
$("#CreteButton").on('click', function () {
    $('#SaveFunction').text("Save");
    $('#groupPolicy').hide();
    $("#textStateActive").prop("checked", true)
    clearJobFields();
    jobOnce();
});
const clearJobFields = () => {
    $("#textJobId,#textJobName,#textTemplateId,#selectTemplateName,#selectSolutionType,#selectInfraObjectName,#selectExecutionPolicy,#textInfraObjectId,#textNodeId,#textNodeName,#textCronExpression,#textStatus,#textState,#selectGroupPolicy,#selectExecution,#textIsSchedule,#textScheduleType").val('');
    $("#textStateInactive").prop("checked", false)
    $('#selectGroupPolicy option:first').prop('selected', 'selected');
    $('#SaveFunction').text("Save");
    ClearJobErrorElements();
    ClearCroneElements();
}
async function validateJobName(value, id = null, url) {
    if (!value) {
        $('#Name-error').text('Enter job name').addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        $('#Name-error').text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    var url = RootUrl + url;
    var data = {};
    data.name = value;
    data.id = id;

    const validationResults = [
        await SpecialCharValidateCustom(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsSameNameExist(url, data)
    ];

    const failedValidations = validationResults.filter(result => result !== true);

    if (failedValidations.length > 0) {
        $('#Name-error').text(failedValidations[0]).addClass('field-validation-error');
        return false;
    } else {
        $('#Name-error').text('').removeClass('field-validation-error');
        return true;
    }
}
async function IsSameNameExist(url, inputValue) {
    return !inputValue.name.trim() ? true : (await GetAsync(url, inputValue, OnError)) ? " Job name already exists" : true;
}
async function SetGroupPolicy() {
    var url = RootUrl + "Manage/JobManagement/GetGroupPolicies";
    var data = {};

    var result = await GetAsync(url, data, OnError);

    for (var index = 0; index <= result.length; index++) {
        $('#selectGroupPolicy').append('<option value="' + result[index].groupName + '">' + result[index].groupName + '</option>');
    }
}
function ClearJobErrorElements() {
    $("#Name-error,#TemplateName-error,#SolutionType-error,#GroupPolicy-error,#state-error,#CronMin-error,#CronHourly-error,#CroneveryHour-error,#ExecutionPolicy-error,#CronddlMin-error,#CronddlHour-error,#CronDay-error,#CronExpression-error,#Crondaysevery-error, #InfraObjectName-error,#MonthlyHours-error,#CronMon-error,#CronMonthly-error,#CronExpression-error").text('').removeClass('field-validation-error');
}
$(".nav-link,#nav-Minutes-tab,#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab").on("click", function () {
    $("#CronMin-error,#CronHourly-error,#CronHourMin-error,#Crondaysevery-error,#CroneveryHour-error,#CronDay-error,#CronddlHour-error, #CronMonthly-error,#CronMon-error,#MonthlyHours-error,#CronExpression-error").text('').removeClass('field-validation-error');
})
$("#nav-Monthly-tab").on("click", function () {
    if ($("#SaveFunction").text() == "Save") {
        $('input[name=Monthyday]').attr('disabled', 'disabled');
    }
})
function ClearCroneElements() {
    $("#txtMins,#txtHours,#txtMinutes,#ddlHours,#everyHours,#lblMonth,#MonthlyHours,#datetimeCron").val('');
    $('input[name=weekDays]').prop("checked", false);
    $('input[name=daysevery]').prop("checked", false);
    $('input[name=Monthyday]').prop("checked", false);
    $('input[name="Monthyday"]').prop('disabled', false);
}
function JobCronExpression() {
    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
    var monthlymonth = $('#lblMonth').val();
    var CronExpression = "";
    var Minutes = $('#txtMins').val();
    var txtHours = $('#txtHours').val() == "00" ? "0" : $('#txtHours').val() == "01" ? "1" : $('#txtHours').val() == "03" ? "3" : $('#txtHours').val() == "04" ? "4" : $('#txtHours').val() == "05" ? "5" : $('#txtHours').val() == "06" ? "6" : $('#txtHours').val() == "07" ? "7" : $('#txtHours').val() == "08" ? "8" : $('#txtHours').val() == "09" ? "9" : $('#txtHours').val()
    var txtHourMinutes = $('#txtMinutes').val() == "00" ? "0" : $('#txtMinutes').val() == "01" ? "1" : $('#txtMinutes').val() == "03" ? "3" : $('#txtMinutes').val() == "04" ? "4" : $('#txtMinutes').val() == "05" ? "5" : $('#txtMinutes').val() == "06" ? "6" : $('#txtMinutes').val() == "07" ? "7" : $('#txtMinutes').val() == "08" ? "8" : $('#txtMinutes').val() == "09" ? "9" : $('#txtMinutes').val()
    var day = $('#ddlHours').val().split(":")
    var ddlHours = day[0]
    var ddlMinutes = day[1] //$('#ddlMinutes').val();
    var Daily = $('#everyHours').val().split(":")
    var everyHours = Daily[0]
    var everyMinutes = Daily[1]
    var month = $('#MonthlyHours').val().split(":")
    var MonthlyHours = month[0]
    var MonthlyMins = month[1] //$('#MonthlyMins').val()
    var weekDay = $('#defaultCheck-MON-FRI').val();
    var datetime = $('#datetimeCron').val()

    var schedule_model = document.querySelector('input[name="daysevery"]:checked');
    var listcron = '';
    if (datetime != '') {
        var { CronExpression, listCron } = DateTimeCronBuilder(datetime)
        CronExpression = CronExpression
        listcron = listCron;
    }
    else {
        if (Minutes != '') {
            CronExpression = "0" + " 0/" + Minutes + " * * * ?";
            listcron = "Every " + Minutes + " minutes"
        }
        else if (txtHours != '') {
            CronExpression = "0 " + " 0/" + txtHourMinutes + " 0/" + txtHours + " * * ?"
            listcron = "Every " + txtHours + " hours, " + " every " + txtHourMinutes + " minutes";
        }
        else if (txtDay != '') {
            CronExpression = "0 " + ddlMinutes + " " + ddlHours + " ? * " + txtDay + " *"
            listcron = txtDay + " at " + ddlHours + " hours " + ddlMinutes + " minutes";
        }
        else if (txtmonthday != '') {
            if (monthlymonth != '') {
                monthlymonth = monthlymonth.split('-');
                var txtmonth = monthlymonth[1] == "01" ? "JAN" : monthlymonth[1] == "02" ? "FEB" : monthlymonth[1] == "03" ? "MAR" : monthlymonth[1] == "04" ? "APR" :
                    monthlymonth[1] == "05" ? "MAY" : monthlymonth[1] == "06" ? "JUN" : monthlymonth[1] == "07" ? "JUL" : monthlymonth[1] == "08" ? "AUG" : monthlymonth[1] == "09" ? "SEP" :
                        monthlymonth[1] == "10" ? "OCT" : monthlymonth[1] == "11" ? "NOV" : monthlymonth[1] == "12" ? "DEC" : ""
                var txtyear = monthlymonth[0];
            }
            CronExpression = "0 " + MonthlyMins + " 0/" + MonthlyHours + " " + txtmonthday + " " + txtmonth + " ? " + txtyear
            listcron = MonthlyHours + " hours " + MonthlyMins + " minutes for " + txtmonthday + " day(s) on " + txtmonth + " in the year " + txtyear;
        }
        else if (schedule_model != null) {
            if (schedule_model.value == "everyday") {

                CronExpression = "0 " + everyMinutes + " " + everyHours + " * * ?"
                listcron = " Every day at " + everyHours + " hours " + everyMinutes + " minutes ";
            }
            else if (schedule_model.value == "MON-FRI") {

                CronExpression = "0 " + everyMinutes + " " + everyHours + " ? * " + weekDay + " * ";
                listcron = " MON-FRI at " + everyHours + " hours " + everyMinutes + " minutes ";
            }
        }
    }
    return { CronExpression, listcron };
}

function DateTimeCronBuilder(datetime) {
    var splitDate = datetime.split("T");
    var cronDate = splitDate[0].split("-");
    var cronTime = splitDate[1].split(":");
    var cronYear = cronDate[0];
    var cronMonth = cronDate[1]
    var cronDay = cronDate[2];
    var cronHours = cronTime[0];
    var cronMin = cronTime[1];
    var cronmonthexp = cronDate[1] == "01" ? "JAN" : cronDate[1] == "02" ? "FEB" : cronDate[1] == "03" ? "MAR" : cronDate[1] == "04" ? "APR" :
        cronDate[1] == "05" ? "MAY" : cronDate[1] == "06" ? "JUN" : cronDate[1] == "07" ? "JUL" : cronDate[1] == "08" ? "AUG" : cronDate[1] == "09" ? "SEP" :
            cronDate[1] == "10" ? "OCT" : cronDate[1] == "11" ? "NOV" : cronDate[1] == "12" ? "DEC" : ""
    CronExpression = "0 " + cronMin + " " + cronHours + " " + cronDay + " " + cronMonth + " ? " + cronYear;
    // monthname
    listCron = "At " + cronHours + ":" + cronMin + ", on day  " + cronDay + " of the month, only in " + cronmonthexp + ", only in " + cronYear;
    //At 12: 51 PM, on day 14 of the month, only in February, only in 2024
    return { CronExpression, listCron }
}
function DateTimeCronConventor(cron) {
    var splitcron = cron.split(" ");
    var cronYear = splitcron[6];
    var cronMonth = splitcron[4];
    var cronDay = splitcron[3];
    var cronHours = splitcron[2];
    var cronMin = splitcron[1];
    var cronDate = cronYear + "-" + cronMonth + "-" + cronDay + "T" + cronHours + ":" + cronMin
    //At 06:05:00am, on the 8th day, in May, in 2023
    return cronDate
}

function CronValidation() {
    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
    var monthlymonth = $('#lblMonth').val();
    var Minutes = $('#txtMins').val();
    var txtHours = $('#txtHours').val();
    var txtHourMinutes = $('#txtMinutes').val();
    //var ddlHours = $('#ddlHours').val();
    var ddlMinutes = $('#ddlMinutes').val();
    var everyHours = $('#everyHours').val();
    var everyMinutes = $('#everyMinutes').val();
    var datetime = $('#datetimeCron').val();
    var MonthlyHours = $('#MonthlyHours').val();
    var MonthlyMins = $('#MonthlyMins').val()
    var isScheduler = ''

    if (document.getElementById('switchMonthly').checked == true) {

        $('#datetimeCron').val('');
        var Scheduler_types = $('.nav-tabs .active').text().trim();

        switch (Scheduler_types) {

            case "Minutes":
                isScheduler = validateMinJobNumber(Minutes, "Enter minutes", $('#CronMin-error'));
                break;
            case "Hourly":
                isScheduler = validateHourJobNumber(txtHours, "Enter hours", $('#CronHourly-error'));
                isScheduler = validateMiniteJobNumber(txtHourMinutes, "Enter minutes", $('#CronHourMin-error'));
                break;
            case "Daily":
                isSchedulerHour = validateHourJobNumber(everyHours, "Select start time", $('#CroneveryHour-error'));
                isSchedulerDay = ValidateCronRadioButton($('#Crondaysevery-error'));
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;
            case "Weekly":
                isSchedulerHour = validateHourJobNumber($('#ddlHours').val(), "Select start time", $('#CronddlHour-error'));
                isSchedulerDay = validateDayNumber(txtDay, "Select day(s)", $('#CronDay-error'));
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;
            case "Monthly":
                isSchedulerHour = validateHourJobNumber(MonthlyHours, "Select start time", $('#MonthlyHours-error'));
                if (monthlymonth != "") {
                    isSchedulerDay = validateDayNumber(txtmonthday, "Select date(s)", $('#CronMon-error'));
                }
                isSchedulerMonth = validateDayNumber(monthlymonth, "Select month and year", $('#CronMonthly-error'));
                if (isSchedulerHour && isSchedulerDay && isSchedulerMonth) {
                    isScheduler = true;
                }
                break;
        }
    }
    else {
        isScheduler = validateDayNumber(datetime, "Select schedule time", $('#CronExpression-error')) && validateprevNumber(datetime, "", $('#CronExpression-error'));
    }
    return isScheduler;
}
function validateJobDropDown(value, errorMsg, errorElement) {
    if (!value || value?.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
function ValidateRadioButton(errorElement) {
    if ($('input[name=state]:checked').length > 0) {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
    else {
        errorElement.text("Select state").addClass('field-validation-error');;
        return false;
    }
}
function ValidateCronRadioButton(errorElement) {
    if ($('input[name=daysevery]:checked').length > 0) {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
    else {
        errorElement.text("Select day type").addClass('field-validation-error');;
        return false;
    }
}
function validateMiniteJobNumber(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else if ((Number(value) < 0) || (Number(value) >= 60)) {
        errorElement.text("Enter value between 0 to 59").addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
function validateMinJobNumber(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else if ((Number(value) < 0) || (Number(value) > 59)) {
        errorElement.text("Enter value between 1 to 59").addClass('field-validation-error');
        return false;
    } else if (Number(value) == "0") {
        errorElement.text("Enter the value more than 0").addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
function validateHourJobNumber(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else if ((Number(value) == 0)) {
        errorElement.text("Enter value greater than zero").addClass('field-validation-error');
        return false;
    }
    else if ((Number(value) < 1) || (Number(value) >= 24)) {
        errorElement.text("Enter value between 1 to 23").addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
function validateprevNumber(value, errorMsg, errorElement) {
    let selectdate = new Date(value)
    let currentdate = new Date()

    if (selectdate > currentdate) {
        $('#CronExpression-error').text('').removeClass('field-validation-error');
        return true;
    } else if (selectdate < currentdate) {
        $('#CronExpression-error').text("Select schedule date and time should be greater than current date and time").addClass('field-validation-error');
        return false;
    }
}
function validateDayNumber(value, errorMsg, errorElement) {
    if (!value || value.length == 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
function GetIsSchedule() {
    var schedule_type = document.querySelector('input[name = "switchPlan"]:checked');
    if (schedule_type.value === "Once") {
        $('#textIsSchedule').val(1);
    } else {
        $('#textIsSchedule').val(2);
    }
}
function Get_ScheduleTypes() {
    var Scheduler_types = $('.nav-tabs .active').text().trim();
    switch (Scheduler_types) {
        case "Minutes":
            $('#textScheduleType').val(1);
            break;
        case "Hourly":
            $('#textScheduleType').val(2);
            break;
        case "Daily":
            $('#textScheduleType').val(3);
            break;
        case "Weekly":
            $('#textScheduleType').val(4);
            break;
        case "Monthly":
            $('#textScheduleType').val(5);
            break;
    }
}
function Tab_selection(jobData) {
    if (jobData.isSchedule == 2) {
        Drready_SM1 = document.getElementById("switchMonthly");
        Drready_SM1.checked = true;
        var elementToHide1 = document.getElementById("monthgroup");
        elementToHide1.style.display = "block";
        var elementToHide22 = document.getElementById("yeargroup");
        elementToHide22.style.display = "none";
    } else {
        Drready_SM2 = document.getElementById("switchYearly");
        Drready_SM2.checked = true;
        var elementToHide11 = document.getElementById("monthgroup");
        elementToHide11.style.display = "none";
        var elementToHide22 = document.getElementById("yeargroup");
        elementToHide22.style.display = "block";
    }
}
function Tab_schedule_type(jobData) {
    var types = jobData.scheduleType;
    var clickedLink = "";
    var linkId = "";
    var divToHide = "";
    if (jobData.isSchedule == 1) {
        var datetime = DateTimeCronConventor(jobData.cronExpression)
        $('#datetimeCron').val(datetime)
    }
    else {
        switch (types) {
            case 1:
                linkId = "nav-Minutes-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { minutes } = parseMinCronExpression(jobData.cronExpression);
                    document.getElementById("txtMins").value = minutes;
                }, 150)
                break;
            case 2:
                linkId = "nav-Hourly-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { hours, minutes } = parseHoursCronExpression(jobData.cronExpression);
                    document.getElementById("txtHours").value = hours;
                    document.getElementById("txtMinutes").value = minutes;
                }, 150)
                break;
            case 3:
                linkId = "nav-Daily-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { hours, day } = parseDailyCronExpression(jobData.cronExpression);
                    document.getElementById("everyHours").value = hours;
                    //document.getElementById("everyMinutes").value = minutes;
                    if (day == "?") {
                        $("#defaultCheck-everyday").prop("checked", true);
                    }
                    else {
                        $("#defaultCheck-MON-FRI").prop("checked", true);
                    }
                }, 150)
                break;
            case 4:
                linkId = "nav-Weekly-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { hours, day } = parseWeeklyCronExpression(jobData.cronExpression);
                    document.getElementById("ddlHours").value = hours;
                    //document.getElementById("ddlMinutes").value = minutes;
                    dayconventor(day);
                }, 150)
                break;
            case 5:
                linkId = "nav-Monthly-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { hours, month, days } = parseCronMonthExpression(jobData.cronExpression);
                    document.getElementById("MonthlyHours").value = hours;
                    //document.getElementById("MonthlyMins").value = minutes;
                    //document.getElementById("MonthlyMins").value = minutes;
                    document.getElementById("lblMonth").value = month;
                    monthDayconventor(days);
                }, 150)
                break;
        }
    }
}
function monthDayconventor(days) {
    const day = days.split(" ")
    var checkboxes = document.querySelectorAll('input[name="Monthyday"]');
    checkboxes.forEach(function (checkbox) {
        if (day.includes(checkbox.value)) {
            checkbox.checked = true;
        }
    });
};

function parseCronMonthExpression(expression) {
    const parts = expression.split(' ');
    const minutes = parseInt(parts[1]);
    const hours = parseInt(parts[2].substring(2));
    const month = parts[6] + "-" + parts[4];
    const days = parts[3];
    return { minutes, hours, month, days };
};

function parseMinCronExpression(expression) {
    const parts = expression.split(' ');
    const minutes = parseInt(parts[1].substring(2));
    const hours = parseInt(parts[2].substring(2));
    const day = parseInt(parts[3].substring(2));
    return { hours, minutes, day };
}
function parseHoursCronExpression(expression) {
    const parts = expression.split(' ')
    const minutes = parseInt(parts[1]);
    const hours = parseInt(parts[2].substring(2));
    const day = parts[5];
    return { hours, minutes, day };
}
function parseDailyCronExpression(expression) {
    const parts = expression.split(' ')
    const minutes = parseInt(parts[1]);
    const hours = parseInt(parts[2].substring(2));
    const day = parts[5];
    return { hours, minutes, day };
}
function parseWeeklyCronExpression(expression) {
    const parts = expression.split(' ')
    const minutes = parseInt(parts[1]);
    const hours = parseInt(parts[2].substring(2));
    const day = parts[5];
    return { hours, minutes, day };
}
function dayconventor(day) {
    const daysMap = {
        MON: 1,
        TUE: 2,
        WED: 3,
        THU: 4,
        FRI: 5,
        SAT: 6,
        SUN: 0
    };
    const days = day.split(',');
    days.forEach(day => {
        const checkboxId = `#defaultCheck-${daysMap[day]}`;
        $(checkboxId).prop("checked", true);
    });
}
$(".replicationbtn_cancel").on("click", function () {
    $("#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab").removeClass("active");
    $("#nav-Hourly,#nav-Daily,#nav-Weekly,#nav-Monthly").removeClass("show active");
    $("#nav-Minutes").addClass("show active");
    $("#nav-Minutes-tab").addClass("active");
})
function jobOnce() {
    Drready_SM2 = document.getElementById("switchMonthly");
    Drready_SM2.checked = true;
    var elementToHide11 = document.getElementById("monthgroup");
    elementToHide11.style.display = "block";
    var elementToHide22 = document.getElementById("yeargroup");
    elementToHide22.style.display = "none";
}
var monthInput = document.getElementById("lblMonth");
var today = new Date();
var currentYear = today.getFullYear();
var currentMonth = today.getMonth() + 1;
var minMonth = currentYear + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
var maxMonth = (currentYear + 77) + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
monthInput.setAttribute("min", minMonth);
monthInput.setAttribute("max", maxMonth);

const now = new Date();
const year = now.getFullYear();
const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are 0-based
const day = String(now.getDate()).padStart(2, '0');
const hours = String(now.getHours()).padStart(2, '0');
const minutes = String(now.getMinutes()).padStart(2, '0');
const minformattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
const maxformattedDate = `${year + 77}-${month}-${day}T${hours}:${minutes}`;

const datetimeInput = document.getElementById('datetimeCron');
datetimeInput.min = minformattedDate;
datetimeInput.max = maxformattedDate;
const getInfraObjectDetails = async (id) => {
    $("#selectTemplateName").empty();
    $("#solutionTypeId").val(id);
    let data = {}
    data.replicationTypeId = id
    await $.ajax({
        type: "GET",
        url: RootUrl + 'Manage/MonitoringJob/GetTemplateByReplicationTypeId',
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {
                let html = ''
                let data = result?.data;
                html += '<option value="">Select Workflow Templates</option>';
                data.forEach((item) => {
                    html += `<option id='${item.id}' value='${item.id}'>${item.name}</option>`;
                })
                $('#selectTemplateName').append(html)
            } else {
                errorNotification(result)
            }
        }
    })
}