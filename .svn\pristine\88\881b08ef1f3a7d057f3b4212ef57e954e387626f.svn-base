using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BackUp.Commands.Create;
using ContinuityPatrol.Application.Features.BackUp.Commands.Delete;
using ContinuityPatrol.Application.Features.BackUp.Commands.Execute;
using ContinuityPatrol.Application.Features.BackUp.Commands.Update;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetByConfig;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetList;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BackUpModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class BackUpControllerTests : IClassFixture<BackUpFixture>
{
    private readonly BackUpFixture _backUpFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly BackUpsController _controller;

    public BackUpControllerTests(BackUpFixture backUpFixture)
    {
        _backUpFixture = backUpFixture;

        var testBuilder = new ControllerTestBuilder<BackUpsController>();
        _controller = testBuilder.CreateController(
            _ => new BackUpsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetBackUps_ReturnsExpectedList()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBackUpListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_backUpFixture.BackUpListVm);

        // Act
        var result = await _controller.GetBackUps();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var backUps = Assert.IsAssignableFrom<List<BackUpListVm>>(okResult.Value);
        Assert.Equal(3, backUps.Count);
    }

    [Fact]
    public async Task GetBackUps_ReturnsEmptyList_WhenNoBackUpsExist()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
      
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBackUpListQuery>(), default))
            .ReturnsAsync(new List<BackUpListVm>());

        // Act
        var result = await _controller.GetBackUps();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var backUps = Assert.IsAssignableFrom<List<BackUpListVm>>(okResult.Value);
        Assert.Empty(backUps);
    }

    [Fact]
    public async Task GetBackUpById_ReturnsBackUp_WhenIdIsValid()
    {
        // Arrange
        var backUpId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBackUpDetailQuery>(q => q.Id == backUpId), default))
            .ReturnsAsync(_backUpFixture.BackUpDetailVm);

        // Act
        var result = await _controller.GetBackUpById(backUpId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var backUp = Assert.IsType<BackUpDetailVm>(okResult.Value);
        Assert.NotNull(backUp);
    }

    [Fact]
    public async Task GetBackUpById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetBackUpById("invalid-guid"));
    }

    [Fact]
    public async Task CreateBackUp_Returns201Created()
    {
        // Arrange
        var command = _backUpFixture.CreateBackUpCommand;
        var expectedMessage = $"BackUp '{command.DatabaseName}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBackUpResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBackUp(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBackUpResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBackUp_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"BackUp '{_backUpFixture.UpdateBackUpCommand.DatabaseName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateBackUpCommand>(), default))
            .ReturnsAsync(new UpdateBackUpResponse
            {
                Message = expectedMessage,
                Id = _backUpFixture.UpdateBackUpCommand.Id
            });

        // Act
        var result = await _controller.UpdateBackUp(_backUpFixture.UpdateBackUpCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBackUpResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBackUp_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "BackUp 'Test Database' has been deleted successfully!.";
        var backUpId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBackUpCommand>(c => c.Id == backUpId), default))
            .ReturnsAsync(new DeleteBackUpResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteBackUp(backUpId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBackUpResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetPaginatedBackUps_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetBackUpPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _backUpFixture.BackUpListVm;
        var expectedPaginatedResult = PaginatedResult<BackUpListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBackUpPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedBackUps(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<BackUpListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<BackUpListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task ExecuteBackUp_ReturnsOk()
    {
        // Arrange
        var command = new BackUpExecuteCommand
        {
            Id = Guid.NewGuid().ToString()
        };

        var expectedMessage = "Backup request sent successfully";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new BackUpExecuteResponse
            {
                Message = expectedMessage
            });

        // Act
        var result = await _controller.ExecuteBackUp(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<BackUpExecuteResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

   
    [Fact]
    public async Task IsBackUpNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBackUpNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsBackUpNameExist("ExistingBackUp", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsBackUpNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetBackUpNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsBackUpNameExist("NewBackUp", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateBackUp_ValidatesBackUpPath()
    {
        // Arrange
        var command = new CreateBackUpCommand
        {
            HostName = "test-server",
            DatabaseName = "TestDB",
            BackUpPath = "", // Empty backup path should cause validation error
            BackUpType = "Full"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("BackUpPath is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBackUp(command));
    }

    [Fact]
    public async Task UpdateBackUp_ValidatesBackUpExists()
    {
        // Arrange
        var command = new UpdateBackUpCommand
        {
            Id = Guid.NewGuid().ToString(),
            HostName = "updated-server",
            DatabaseName = "UpdatedDB"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("BackUp not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateBackUp(command));
    }

    [Fact]
    public async Task ExecuteBackUp_HandlesExecutionFailure()
    {
        // Arrange
        var command = new BackUpExecuteCommand
        {
            Id = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("Backup execution failed"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.ExecuteBackUp(command));
    }

    [Fact]
    public async Task GetPaginatedBackUps_HandlesFilteringByBackUpType()
    {
        // Arrange
        var query = new GetBackUpPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var incrementalBackUps = new List<BackUpListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                HostName = "server1.company.com",
                DatabaseName = "DB1",
                BackUpType = "Incremental",
                ScheduleType = "Hourly"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                HostName = "server2.company.com",
                DatabaseName = "DB2",
                BackUpType = "Incremental",
                ScheduleType = "Every 6 Hours"
            }
        };

        var expectedPaginatedResult = PaginatedResult<BackUpListVm>.Success(
            data: incrementalBackUps,
            count: incrementalBackUps.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBackUpPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedBackUps(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<BackUpListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<BackUpListVm>>(okResult.Value);

        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, backUp => Assert.Equal("Incremental", backUp.BackUpType));
    }

    [Fact]
    public async Task CreateBackUp_HandlesComplexProperties()
    {
        // Arrange
        var command = new CreateBackUpCommand
        {
            HostName = "enterprise-db-cluster.company.com",
            DatabaseName = "EnterpriseApplicationDB",
            UserName = "enterprise_backup_service",
            Password = "complex_encrypted_password_with_special_chars!@#$",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = "\\\\enterprise-backup-cluster\\critical-data\\application-db",
            BackUpType = "Full",
            CronExpression = "0 0 2 * * SUN",
            ScheduleType = "Weekly",
            ScheduleTime = "02:00:00",
            Properties = "{\"compression\":\"high\",\"encryption\":\"AES256\",\"verification\":\"checksum\",\"retention\":\"365days\",\"priority\":\"critical\",\"notifications\":[\"<EMAIL>\",\"<EMAIL>\"]}",
            KeepBackUpLast = "365",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "Enterprise-Backup-Cluster-Node"
        };

        var expectedMessage = $"BackUp '{command.DatabaseName}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBackUpResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBackUp(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBackUpResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBackUp_VerifiesBackUpIsDeactivated()
    {
        // Arrange
        var backUpId = Guid.NewGuid().ToString();
        var expectedMessage = "BackUp 'Test Database' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBackUpCommand>(c => c.Id == backUpId), default))
            .ReturnsAsync(new DeleteBackUpResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteBackUp(backUpId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBackUpResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetBackUpByConfig_ReturnsConfigDetail()
    {
        // Arrange
        var expectedConfigDetail = new GetByConfigDetailVm();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetByConfigDetailQuery>(), default))
            .ReturnsAsync(expectedConfigDetail);

        // Act
        var result = await _controller.GetBackUpByConfig();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var configDetail = Assert.IsType<GetByConfigDetailVm>(okResult.Value);
        Assert.NotNull(configDetail);
    }
}
