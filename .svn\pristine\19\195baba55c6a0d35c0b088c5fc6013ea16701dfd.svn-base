﻿using ContinuityPatrol.Application.Features.StateMonitorLog.Commands.Create;
using ContinuityPatrol.Application.Features.StateMonitorLog.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class StateMonitorLogFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<StateMonitorLog> StateMonitorLogs { get; set; }
    public CreateStateMonitorLogCommand CreateStateMonitorLogCommand { get; set; }
    public UpdateStateMonitorLogCommand UpdateStateMonitorLogCommand { get; set; }

    public StateMonitorLogFixture()
    {
        StateMonitorLogs = AutoStateMonitorLogFixture.Create<List<StateMonitorLog>>();

        CreateStateMonitorLogCommand = AutoStateMonitorLogFixture.Create<CreateStateMonitorLogCommand>();

        UpdateStateMonitorLogCommand = AutoStateMonitorLogFixture.Create<UpdateStateMonitorLogCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<StateMonitorLogProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoStateMonitorLogFixture
    {
        get
        {
            var fixture = new Fixture();
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateStateMonitorLogCommand>(p => p.Properties, 10));
            fixture.Customize<CreateStateMonitorLogCommand>(c => c.With(b => b.Properties, 0.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateStateMonitorLogCommand>(p => p.Properties, 10));
            fixture.Customize<UpdateStateMonitorLogCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<StateMonitorLog>(c => c.With(b => b.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {

    }

}