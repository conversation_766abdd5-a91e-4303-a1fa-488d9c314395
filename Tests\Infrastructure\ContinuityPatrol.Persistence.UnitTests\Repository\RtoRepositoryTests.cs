using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RtoRepositoryTests : IClassFixture<RtoFixture>
{
    private readonly RtoFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly RtoRepository _repository;

    public RtoRepositoryTests(RtoFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new RtoRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region GetRtoByBusinessServiceId Tests

    [Fact]
    public async Task GetRtoByBusinessServiceId_ShouldReturnMatchingRecord_WhenRecordExists()
    {
        // Arrange
        var businessServiceId = "BS_TEST_001";
        var matchingRto = CreateRtoWithBusinessServiceId(businessServiceId);
        var nonMatchingRto = CreateRtoWithBusinessServiceId("DIFFERENT_BS");

        _dbContext.Rtos.Add(matchingRto);
        _dbContext.Rtos.Add(nonMatchingRto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRtoByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceId, result.BusinessServiceId);
        Assert.Equal(matchingRto.Id, result.Id);
        Assert.Equal(matchingRto.ReferenceId, result.ReferenceId);
        Assert.Equal(matchingRto.BusinessServiceName, result.BusinessServiceName);
        Assert.Equal(matchingRto.TotalBusinessFunction, result.TotalBusinessFunction);
        Assert.Equal(matchingRto.BFAvailable, result.BFAvailable);
        Assert.Equal(matchingRto.BFImpact, result.BFImpact);
        Assert.Equal(matchingRto.BFUnderRTO, result.BFUnderRTO);
        Assert.Equal(matchingRto.BFAboveRTO, result.BFAboveRTO);
        Assert.Equal(matchingRto.BFNotAvailable, result.BFNotAvailable);
        Assert.Equal(matchingRto.BFDrillNotExecuted, result.BFDrillNotExecuted);
        Assert.Equal(matchingRto.TotalInfraObject, result.TotalInfraObject);
        Assert.Equal(matchingRto.InfraAvailable, result.InfraAvailable);
        Assert.Equal(matchingRto.InfraImpact, result.InfraImpact);
        Assert.Equal(matchingRto.InfraUnderRTO, result.InfraUnderRTO);
        Assert.Equal(matchingRto.InfraAboveRTO, result.InfraAboveRTO);
        Assert.Equal(matchingRto.InfraNotAvailable, result.InfraNotAvailable);
        Assert.Equal(matchingRto.InfraDrillNotExecuted, result.InfraDrillNotExecuted);
    }

    [Fact]
    public async Task GetRtoByBusinessServiceId_ShouldReturnNull_WhenNoMatchingRecord()
    {
        // Arrange
        var rto = CreateRtoWithBusinessServiceId("DIFFERENT_BS");
        _dbContext.Rtos.Add(rto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRtoByBusinessServiceId("NON_EXISTENT_BS");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetRtoByBusinessServiceId_ShouldReturnFirstActiveRecord_WhenMultipleRecordsExist()
    {
        // Arrange
        var businessServiceId = "BS_MULTIPLE_TEST";
        var firstRto = CreateRtoWithBusinessServiceId(businessServiceId, isActive: true);
        var secondRto = CreateRtoWithBusinessServiceId(businessServiceId, isActive: true);

        _dbContext.Rtos.Add(firstRto);
        _dbContext.Rtos.Add(secondRto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRtoByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceId, result.BusinessServiceId);
        // Should return one of the matching active records
        Assert.True(result.Id == firstRto.Id || result.Id == secondRto.Id);
    }

    [Fact]
    public async Task GetRtoByBusinessServiceId_ShouldReturnOnlyActiveRecords()
    {
        // Arrange
        var businessServiceId = "BS_ACTIVE_TEST";
        var activeRto = CreateRtoWithBusinessServiceId(businessServiceId, isActive: true);
        var inactiveRto = CreateRtoWithBusinessServiceId(businessServiceId, isActive: false);

        _dbContext.Rtos.Add(activeRto);
        _dbContext.Rtos.Add(inactiveRto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRtoByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceId, result.BusinessServiceId);
        Assert.Equal(activeRto.Id, result.Id);
    }

    [Fact]
    public async Task GetRtoByBusinessServiceId_ShouldReturnNull_WhenOnlyInactiveRecordsExist()
    {
        // Arrange
        var businessServiceId = "abea4151-6ee0-4f6b-9fc0-2fa5c961d956";
        var inactiveRto = CreateRtoWithBusinessServiceId(businessServiceId, isActive: false);

        _dbContext.Rtos.Add(inactiveRto);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetRtoByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Null(result);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task GetRtoByBusinessServiceId_ShouldReturnNull_WhenBusinessServiceIdIsNullOrEmpty(string businessServiceId)
    {
        // Arrange
        var rto = CreateRtoWithBusinessServiceId("VALID_BS");
        _dbContext.Rtos.Add(rto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRtoByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetRtoByBusinessServiceId_ShouldUseAsNoTracking()
    {
        // Arrange
        var businessServiceId = "BS_NO_TRACKING_TEST";
        var rto = CreateRtoWithBusinessServiceId(businessServiceId);
        _dbContext.Rtos.Add(rto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRtoByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        
        // Verify that the entity is not being tracked
        var trackedEntity = _dbContext.Entry(result);
        Assert.Equal(Microsoft.EntityFrameworkCore.EntityState.Detached, trackedEntity.State);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddRto_WhenValidEntity()
    {
        // Arrange
        var rto = _fixture.RtoDto;
        rto.BusinessServiceId = "BS_TEST_001";
        rto.BusinessServiceName = "Test Business Service";
        rto.TotalBusinessFunction = 10;
        rto.BFAvailable = 8;
        rto.BFImpact = 1;
        rto.BFUnderRTO = 7;
        rto.BFAboveRTO = 2;
        rto.BFNotAvailable = 1;
        rto.BFDrillNotExecuted = 0;
        rto.TotalInfraObject = 15;
        rto.InfraAvailable = 12;
        rto.InfraImpact = 2;
        rto.InfraUnderRTO = 10;
        rto.InfraAboveRTO = 3;
        rto.InfraNotAvailable = 1;
        rto.InfraDrillNotExecuted = 1;

        // Act
        var result = await _repository.AddAsync(rto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(rto.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(rto.BusinessServiceName, result.BusinessServiceName);
        Assert.Equal(rto.TotalBusinessFunction, result.TotalBusinessFunction);
        Assert.Equal(rto.BFAvailable, result.BFAvailable);
        Assert.Equal(rto.BFImpact, result.BFImpact);
        Assert.Equal(rto.BFUnderRTO, result.BFUnderRTO);
        Assert.Equal(rto.BFAboveRTO, result.BFAboveRTO);
        Assert.Equal(rto.BFNotAvailable, result.BFNotAvailable);
        Assert.Equal(rto.BFDrillNotExecuted, result.BFDrillNotExecuted);
        Assert.Equal(rto.TotalInfraObject, result.TotalInfraObject);
        Assert.Equal(rto.InfraAvailable, result.InfraAvailable);
        Assert.Equal(rto.InfraImpact, result.InfraImpact);
        Assert.Equal(rto.InfraUnderRTO, result.InfraUnderRTO);
        Assert.Equal(rto.InfraAboveRTO, result.InfraAboveRTO);
        Assert.Equal(rto.InfraNotAvailable, result.InfraNotAvailable);
        Assert.Equal(rto.InfraDrillNotExecuted, result.InfraDrillNotExecuted);
        Assert.Single(_dbContext.Rtos);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var rto = _fixture.RtoDto;
        _dbContext.Rtos.Add(rto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(rto.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(rto.Id, result.Id);
        Assert.Equal(rto.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(rto.BusinessServiceName, result.BusinessServiceName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var rto = _fixture.RtoDto;
        rto.ReferenceId = "44245100-3539-4d73-a29c-9fa4311527a6";
        _dbContext.Rtos.Add(rto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(rto.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(rto.ReferenceId, result.ReferenceId);
        Assert.Equal(rto.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_WhenExists()
    {
        // Arrange
        var rto = _fixture.RtoDto;
        _dbContext.Rtos.Add(rto);
        await _dbContext.SaveChangesAsync();

        var updatedBusinessServiceName = "Updated Business Service Name";
        var updatedTotalBusinessFunction = 20;
        rto.BusinessServiceName = updatedBusinessServiceName;
        rto.TotalBusinessFunction = updatedTotalBusinessFunction;

        // Act
        var result = await _repository.UpdateAsync(rto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(updatedBusinessServiceName, result.BusinessServiceName);
        Assert.Equal(updatedTotalBusinessFunction, result.TotalBusinessFunction);
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity_WhenExists()
    {
        // Arrange
        var rto = _fixture.RtoDto;
        _dbContext.Rtos.Add(rto);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(rto);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(rto.Id);
        Assert.Null(deletedEntity);
    }

    [Fact]
    public async Task Repository_ShouldHandleZeroValues()
    {
        // Arrange
        var rto = CreateRtoWithBusinessServiceId("BS_ZERO_TEST");
        rto.TotalBusinessFunction = 0;
        rto.BFAvailable = 0;
        rto.BFImpact = 0;
        rto.BFUnderRTO = 0;
        rto.BFAboveRTO = 0;
        rto.BFNotAvailable = 0;
        rto.BFDrillNotExecuted = 0;
        rto.TotalInfraObject = 0;
        rto.InfraAvailable = 0;
        rto.InfraImpact = 0;
        rto.InfraUnderRTO = 0;
        rto.InfraAboveRTO = 0;
        rto.InfraNotAvailable = 0;
        rto.InfraDrillNotExecuted = 0;

        // Act
        var result = await _repository.AddAsync(rto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.TotalBusinessFunction);
        Assert.Equal(0, result.TotalInfraObject);
    }

    [Fact]
    public async Task Repository_ShouldHandleLargeValues()
    {
        // Arrange
        var rto = CreateRtoWithBusinessServiceId("BS_LARGE_TEST");
        rto.TotalBusinessFunction = int.MaxValue;
        rto.TotalInfraObject = int.MaxValue;

        // Act
        var result = await _repository.AddAsync(rto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(int.MaxValue, result.TotalBusinessFunction);
        Assert.Equal(int.MaxValue, result.TotalInfraObject);
    }

    #endregion

    #region Helper Methods

    private Rto CreateRtoWithBusinessServiceId(string businessServiceId, bool isActive = true)
    {
        return new Rto
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = businessServiceId,
            BusinessServiceName = $"Business Service for {businessServiceId}",
            TotalBusinessFunction = 10,
            BFAvailable = 8,
            BFImpact = 1,
            BFUnderRTO = 7,
            BFAboveRTO = 2,
            BFNotAvailable = 1,
            BFDrillNotExecuted = 0,
            TotalInfraObject = 15,
            InfraAvailable = 12,
            InfraImpact = 2,
            InfraUnderRTO = 10,
            InfraAboveRTO = 3,
            InfraNotAvailable = 1,
            InfraDrillNotExecuted = 1,
            IsActive = isActive,
            CreatedBy = "USER_123",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "USER_123",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.Rtos.RemoveRange(_dbContext.Rtos);
        await _dbContext.SaveChangesAsync();
    }
}
