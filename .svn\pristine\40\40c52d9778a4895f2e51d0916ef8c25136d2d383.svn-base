﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.UpdateWorkflowProfilePassword;
using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.WorkflowProfileAuthentication;
using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Orchestration;

public class WorkflowProfileService : BaseClient, IWorkflowProfileService
{
    public WorkflowProfileService(IConfiguration config, IAppCache cache, ILogger<WorkflowProfileService> logger)
        : base(config, cache, logger)
    {

    }

    public async Task<List<WorkflowProfileNameVm>> GetWorkflowProfileNames()
    {
        var request = new RestRequest("api/v6/workflowprofile/names");

        return await GetFromCache<List<WorkflowProfileNameVm>>(request, "GetWorkflowProfileNames");
    }

    public async Task<List<WorkflowProfileListVm>> GetWorkflowProfileList()
    {
        var request = new RestRequest("api/v6/workflowprofile");

        return await Get<List<WorkflowProfileListVm>>(request);
    }

    public async Task<WorkflowProfileDetailVm> GetByReferenceId(string workflowProfileId)
    {
        var request = new RestRequest($"api/v6/workflowprofile/{workflowProfileId}");

        return await Get<WorkflowProfileDetailVm>(request);
    }

    public async Task<bool> IsWorkflowProfileNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/workflowprofile/name-exist?name={name}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string workflowProfileId)
    {
        var request = new RestRequest($"api/v6/workflowprofile/{workflowProfileId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowProfileCommand updateWorkflowProfile)
    {
        var request = new RestRequest("api/v6/workflowprofile", Method.Put);

        request.AddJsonBody(updateWorkflowProfile);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowProfileCommand createWorkflowProfile)
    {
        var request = new RestRequest("api/v6/workflowprofile", Method.Post);

        request.AddJsonBody(createWorkflowProfile);

        return await Post<BaseResponse>(request);
    }

    public async Task<PaginatedResult<WorkflowProfileListVm>> GetPaginatedWorkflowProfile(GetWorkflowProfilePaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/workflowprofile/paginated-list");

        return await Get<PaginatedResult<WorkflowProfileListVm>>(request);
    }

    public async Task<WorkflowProfileAuthenticationResponse> WorkflowProfileAuthentication(WorkflowProfileAuthenticationCommand workflowProfileAuthenticationCommand)
    {
        var request = new RestRequest(" api/v6/workflowprofile/authentication", Method.Post);

        request.AddJsonBody(workflowProfileAuthenticationCommand);

        return await Post<WorkflowProfileAuthenticationResponse>(request);

    }

    public async Task<UpdateWorkflowProfilePasswordResponse> WorkflowProfileChangePassword(UpdateWorkflowProfilePasswordCommand updateWorkflowProfilePasswordCommand)
    {
        var request = new RestRequest("api/v6/workflowprofile/changepassword", Method.Put);

        request.AddJsonBody(updateWorkflowProfilePasswordCommand);

        return await Put<UpdateWorkflowProfilePasswordResponse>(request);
    }
    
    public async Task<bool> IsWorkflowProfilePasswordExist(string workflowProfileId, string password)
    {
        var request = new RestRequest($"api/v6/workflowprofile/password-exist?workflowProfileId={workflowProfileId}&password={password}");

        return await Get<bool>(request);
    }
}