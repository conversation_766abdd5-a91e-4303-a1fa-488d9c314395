﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DashboardView.Event.ServiceAvailabilityView;

public class ServiceAvailabilityEventHandler : INotificationHandler<ServiceAvailabilityEvent>
{
    private readonly ILogger<ServiceAvailabilityEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ServiceAvailabilityEventHandler(ILogger<ServiceAvailabilityEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(ServiceAvailabilityEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.View} ServiceAvailability",
            Entity = "ServiceAvailability",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Service Availability viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Service Availability viewed");
    }
}