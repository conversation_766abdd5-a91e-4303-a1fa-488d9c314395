using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class BulkImportActionResultFixture : IDisposable
{
    public List<BulkImportActionResult> BulkImportActionResultPaginationList { get; set; }
    public List<BulkImportActionResult> BulkImportActionResultList { get; set; }
    public BulkImportActionResult BulkImportActionResultDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public BulkImportActionResultFixture()
    {
        var fixture = new Fixture();

        BulkImportActionResultList = fixture.Create<List<BulkImportActionResult>>();

        BulkImportActionResultPaginationList = fixture.CreateMany<BulkImportActionResult>(20).ToList();

        BulkImportActionResultPaginationList.ForEach(x => x.CompanyId = CompanyId);
        BulkImportActionResultPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BulkImportActionResultPaginationList.ForEach(x => x.IsActive = true);

        BulkImportActionResultList.ForEach(x => x.CompanyId = CompanyId);
        BulkImportActionResultList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BulkImportActionResultList.ForEach(x => x.IsActive = true);

        BulkImportActionResultDto = fixture.Create<BulkImportActionResult>();
        BulkImportActionResultDto.CompanyId = CompanyId;
        BulkImportActionResultDto.ReferenceId = Guid.NewGuid().ToString();
        BulkImportActionResultDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
