﻿using ContinuityPatrol.Shared.Core.Attributes;

namespace ContinuityPatrol.Shared.Core.Enums;

[Serializable]
public enum ApplicationStatus
{
    [EnumDescription("None")]
    None,
    [EnumDescription("Empty Company Profile")]
    EmptyCompanyProfile,
    [EnumDescription("Empty User")]
    EmptyUser,
    [EnumDescription("Invalid Database Name")]
    InvalidDatabaseName,
    [EnumDescription("Invalid Database Credential")]
    InvalidDatabaseCredential,
    [EnumDescription("Invalid MySql Host Name")]
    InvalidMySqlHostName,
    [EnumDescription("Invalid Keyword Parameter")]
    InvalidKeywordParameter,
    [EnumDescription("UnhandledError")]
    UnhandledError,
    [EnumDescription("Qualified")]
    Qualified,
    [EnumDescription("Provider Already Present")]
    ProviderAlreadyPresent,
    [EnumDescription("Provider Not Present")]
    ProviderNotPresent,
    [EnumDescription("Invalid Assembly Version")]
    InvalidAssemblyVersion,
    [EnumDescription("Not Allowed To Connect The Server")]
    NotAllowedToConnectTheServer
}

[Serializable]
public enum LogInStatus
{
    [EnumDescription("None")]
    None,

    [EnumDescription("Success")]
    Succeeded,

    [EnumDescription("Failure")]
    Failure,

    [EnumDescription("Invalid Login Credential")]
    InvalidCredential = 1001,

    [EnumDescription("AccountLocked")]
    AccountLocked = 1002,

    [EnumDescription("Password Expired")]
    PasswordExpired = 1003,

    [EnumDescription("Multiple LoginSession Found")]
    MultipleLoginSessionFound = 1004,

    [EnumDescription("Invalid Login Attempt")]
    InvalidLoginAttempt = 1008,

    [EnumDescription("Undefined User Role")]
    UndefinedUserRole,

    [EnumDescription("Invalid Request")]
    InvalidRequest,

    [EnumDescription("Session Count Limit Exceeded")]
    SessionCountLimitExceeded,

    [EnumDescription("Trial Period Expired")]
    TrialPeriodExpired,

    [EnumDescription("Password Reset")]
    PasswordReset,

    [EnumDescription("License expiry or mismatch")]
    LicenseExpiry,

    [EnumDescription("Empty license")]
    EmptyLicense,

    [EnumDescription("SiteAdmin Landing Page")]
    SiteAdminLandingPage

}

public enum AuthenticationType
{
    InHouse,
    AD,
}
