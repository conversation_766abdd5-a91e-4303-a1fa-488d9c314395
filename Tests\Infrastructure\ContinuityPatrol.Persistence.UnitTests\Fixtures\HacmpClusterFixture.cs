using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class HacmpClusterFixture : IDisposable
{
    public List<HacmpCluster> HacmpClusterPaginationList { get; set; }
    public List<HacmpCluster> HacmpClusterList { get; set; }
    public HacmpCluster HacmpClusterDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public HacmpClusterFixture()
    {
        var fixture = new Fixture();

        HacmpClusterList = fixture.Create<List<HacmpCluster>>();

        HacmpClusterPaginationList = fixture.CreateMany<HacmpCluster>(20).ToList();

        // Setup proper test data for HacmpClusterPaginationList
        HacmpClusterPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        HacmpClusterPaginationList.ForEach(x => x.IsActive = true);

        // Setup proper test data for HacmpClusterList
        HacmpClusterList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        HacmpClusterList.ForEach(x => x.IsActive = true);

        HacmpClusterDto = fixture.Create<HacmpCluster>();
        HacmpClusterDto.ReferenceId = Guid.NewGuid().ToString();
        HacmpClusterDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
