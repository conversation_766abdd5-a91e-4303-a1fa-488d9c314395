﻿using ContinuityPatrol.Application.Features.Incident.Commands.Create;
using ContinuityPatrol.Application.Features.Incident.Commands.Update;
using ContinuityPatrol.Application.Features.Incident.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.IncidentModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class IncidentProfile : Profile
{
    public IncidentProfile()
    {
        CreateMap<Incident, CreateIncidentCommand>().ReverseMap();
        CreateMap<UpdateIncidentCommand, Incident>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<Incident, IncidentListVm>().ForMember(des => des.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<Incident, GetIncidentDetailVm>().ForMember(des => des.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PaginatedResult<Incident>,PaginatedResult<IncidentListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}