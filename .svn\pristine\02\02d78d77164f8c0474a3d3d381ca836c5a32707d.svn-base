﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class AccessManagerRepository : BaseRepository<AccessManager>, IAccessManagerRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public AccessManagerRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext,loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<AccessManager>> ListAllAsync()
    {
        var accessManagers =await SelectToAccessManagers(MapAccessManager(
            base.ListAllAsync(accessManagers => accessManagers.CompanyId.Equals(_loggedInUserService.CompanyId)))).ToListAsync();

        return accessManagers;
    }

    public override async Task<AccessManager> GetByReferenceIdAsync(string id)
    {
        var accessManagers = SelectToAccessManagers(base.GetByReferenceIdAsync(id,
            accessManagers => accessManagers.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                           accessManagers.ReferenceId.Equals(id)));

        var accessManager = MapAccessManager(accessManagers);

        return await accessManager.FirstOrDefaultAsync();
     
    }

    public Task<List<AccessManager>> GetAccessManagerRoles()
    {
        var accessManagers =
         base.ListAllAsync(accessManagers => accessManagers.CompanyId.Equals(_loggedInUserService.CompanyId));
        
         var accessManagerName = MapAccessManager(accessManagers);

        return accessManagerName.ToListAsync();
    }
    public override async Task<PaginatedResult<AccessManager>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<AccessManager> productFilterSpec, string sortColumn, string sortOrder)
    {
         return _loggedInUserService.IsParent
            ? await MapAccessManager(Entities.Specify(productFilterSpec).DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder)
            :await MapAccessManager(Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public override IQueryable<AccessManager> PaginatedListAllAsync()
    {

        var accessManagers =
          base.ListAllAsync(accessManagers => accessManagers.CompanyId.Equals(_loggedInUserService.CompanyId));

       var result = MapAccessManager(accessManagers);

        return result.OrderByDescending(x => x.Id);

    }

    public Task<bool> IsAccessManagerRoleUnique(string role)
    {
        var matches = _dbContext.AccessManagers.Any(e => e.RoleName.Equals(role));

        return Task.FromResult(matches);
    }

    public Task<bool> IsAccessManagerRoleExist(string role, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? _dbContext.AccessManagers.Any(e => e.RoleName.Equals(role))
            : _dbContext.AccessManagers.Where(e => e.RoleName.Equals(role)).ToList().Unique(id));
    }

    public async Task<AccessManager> GetAccessManagerByRoleId(string roleId)
    {
       return await MapAccessManager(_dbContext.AccessManagers
            .AsNoTracking()
            .Active())
            .FirstOrDefaultAsync(e => e.RoleId == roleId);
    }


    public override Task<AccessManager> GetByIdAsync(int id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByIdAsync(id)
            : Task.FromResult(FindByFilter(accessManager =>
                    accessManager.Id.Equals(id) && accessManager.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Result
                .SingleOrDefault())!;
    }
    public async Task<List<AccessManager>> GetAccessManagersByRoleIds(List<string> roleIds)
    {
        return await SelectToAccessManagers(_dbContext.AccessManagers
            .Where(am => roleIds.Contains(am.RoleId)))
            .ToListAsync();
    }


    private IQueryable<AccessManager> MapAccessManager(IQueryable<AccessManager> accessManagers)
    {
        return accessManagers
        .Select(access => new
        {
            accessManagers = access,
            UserRole = _dbContext.UserRoles.Active().AsNoTracking().FirstOrDefault(bs => bs.ReferenceId.Equals(access.RoleId)),
            //user = _dbContext.Users.Active().AsNoTracking().FirstOrDefault(bf => bf.ReferenceId.Equals(access.UserId))
            
        }).Select(result => new AccessManager
        {
            Id=result.accessManagers.Id,
            ReferenceId=result.accessManagers.ReferenceId,
            RoleId = result.UserRole.ReferenceId,
            RoleName=result.UserRole.Role,
            Properties= result.accessManagers.Properties,
            CompanyId=result.accessManagers.CompanyId,
            ProfileProperties = result.accessManagers.ProfileProperties,
            IsActive= result.accessManagers.IsActive,
            CreatedBy=result.accessManagers.CreatedBy,
            CreatedDate=result.accessManagers.CreatedDate,
            LastModifiedBy=result.accessManagers.LastModifiedBy,
            LastModifiedDate=result.accessManagers.LastModifiedDate
        });
    }

    private IQueryable<AccessManager> SelectToAccessManagers(IQueryable<AccessManager> query)
    {
        return query.Select(access =>new AccessManager
        {
            Id = access.Id,
            ReferenceId = access.ReferenceId,
            RoleId = access.RoleId,
            RoleName = access.RoleName,
            Properties = access.Properties,
            CompanyId = access.CompanyId,
            ProfileProperties = access.ProfileProperties,
        });
    }
}