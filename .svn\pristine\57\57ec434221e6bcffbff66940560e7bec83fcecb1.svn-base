using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DynamicDashboardWidgetFixture : IDisposable
{
    public List<DynamicDashboardWidget> DynamicDashboardWidgetPaginationList { get; set; }
    public List<DynamicDashboardWidget> DynamicDashboardWidgetList { get; set; }
    public DynamicDashboardWidget DynamicDashboardWidgetDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string WidgetName = "TestWidget";
    public const string DashboardId = "DASHBOARD_123";
    public const string WidgetType = "Chart";
    public const string WidgetConfig = "{ \"type\": \"bar\", \"data\": [] }";

    public ApplicationDbContext DbContext { get; private set; }

    public DynamicDashboardWidgetFixture()
    {
        var fixture = new Fixture();

        DynamicDashboardWidgetList = fixture.Create<List<DynamicDashboardWidget>>();

        DynamicDashboardWidgetPaginationList = fixture.CreateMany<DynamicDashboardWidget>(20).ToList();

        DynamicDashboardWidgetPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DynamicDashboardWidgetPaginationList.ForEach(x => x.IsActive = true);
      
        DynamicDashboardWidgetList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DynamicDashboardWidgetList.ForEach(x => x.IsActive = true);
       
        DynamicDashboardWidgetDto = fixture.Create<DynamicDashboardWidget>();
        DynamicDashboardWidgetDto.ReferenceId = Guid.NewGuid().ToString();
        DynamicDashboardWidgetDto.IsActive = true;
       
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
