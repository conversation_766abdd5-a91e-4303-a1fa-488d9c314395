﻿using ContinuityPatrol.Domain.ViewModels.UserActivityModel;

namespace ContinuityPatrol.Application.Features.UserActivity.Queries.GetStartTimeEndTimeByUserId;

public class
    GetStartTimeEndTimeByUserIdQueryHandler : IRequestHandler<GetStartTimeEndTimeByUserIdQuery,
        List<UserActivityListVm>>
{
    private readonly IMapper _mapper;
    private readonly IUserActivityRepository _userActivityRepository;

    public GetStartTimeEndTimeByUserIdQueryHandler(IMapper mapper, IUserActivityRepository userActivityRepository)
    {
        _mapper = mapper;
        _userActivityRepository = userActivityRepository;
    }

    public async Task<List<UserActivityListVm>> Handle(GetStartTimeEndTimeByUserIdQuery request,
        CancellationToken cancellationToken)
    {
        var userActivity = request.LoginName.IsNullOrEmpty()
            ? await _userActivityRepository.ListAllUserActivityAsync(request.StartDate, request.EndDate)
            : await _userActivityRepository.loginnameUserActivityAsync(request.LoginName, request.StartDate,
                request.EndDate);

        return userActivity.Count == 0
            ? new List<UserActivityListVm>()
            : _mapper.Map<List<UserActivityListVm>>(userActivity);
    }
}