using AutoFixture;
using ContinuityPatrol.Application.Features.CyberSnaps.Commands.Create;
using ContinuityPatrol.Application.Features.CyberSnaps.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberSnaps.Commands.Update;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPowerMax;
using ContinuityPatrol.Domain.ViewModels.CyberSnapsModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class CyberSnapsFixture
{
    public CreateCyberSnapsCommand CreateCyberSnapsCommand { get; }
    public UpdateCyberSnapsCommand UpdateCyberSnapsCommand { get; }
    public DeleteCyberSnapsCommand DeleteCyberSnapsCommand { get; }
    public CyberSnapsListVm CyberSnapsListVm { get; }
    public CyberSnapsDetailVm CyberSnapsDetailVm { get; }
    public CyberSnapsViewModel CyberSnapsViewModel { get; }
    public PowerMaxDetailVm PowerMaxDetailVm { get; }
    public CyberSnapsFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateCyberSnapsCommand>(c => c
            .With(b => b.Name, "Enterprise Database Snapshot")
            .With(b => b.Description, "Critical production database snapshot for disaster recovery and backup purposes")
            .With(b => b.Tag, "PROD-DB-SNAP-001")
            .With(b => b.Type, "Database")
            .With(b => b.Location, "/snapshots/production/database/")
            .With(b => b.Age, 7)
            .With(b => b.Size, 2048));

        fixture.Customize<UpdateCyberSnapsCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Updated Enterprise Database Snapshot")
            .With(b => b.Description, "Updated critical production database snapshot with enhanced compression")
            .With(b => b.Tag, "PROD-DB-SNAP-002")
            .With(b => b.Type, "Database")
            .With(b => b.Location, "/snapshots/production/database/v2/")
            .With(b => b.Age, 14)
            .With(b => b.Size, 1536));

        fixture.Customize<DeleteCyberSnapsCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<CyberSnapsListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, () => $"Snapshot-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.Description, () => $"Snapshot description - {fixture.Create<string>().Substring(0, 15)}")
            .With(b => b.Tag, () => $"TAG-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.Type, () => fixture.Create<bool>() ? "Database" : "FileSystem")
            .With(b => b.Location, () => $"/snapshots/{fixture.Create<string>().Substring(0, 8)}/")
            .With(b => b.Age, () => fixture.Create<int>() % 365 + 1)
            .With(b => b.Size, () => fixture.Create<int>() % 10240 + 512)
            .With(b => b.SID, () => $"SID-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.TimeStamp, () => DateTime.Now.AddDays(-(fixture.Create<int>() % 30)).ToString("yyyy-MM-dd HH:mm:ss"))
            .With(b => b.StorageGroupName, () => $"SG-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.SnapshotName, () => $"SNAP-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.LinkSG, () => $"LINK-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.LinkedSGTime, () => DateTime.Now.AddHours(-(fixture.Create<int>() % 24)).ToString("yyyy-MM-dd HH:mm:ss"))
            .With(b => b.UnlinkSGTime, () => fixture.Create<bool>() ? null : DateTime.Now.AddHours(-(fixture.Create<int>() % 12)).ToString("yyyy-MM-dd HH:mm:ss"))
            .With(b => b.Remark, () => $"Remark: {fixture.Create<string>().Substring(0, 20)}")
            .With(b => b.LinkedStatus, () => fixture.Create<bool>() ? "Linked" : "Unlinked")
            .With(b => b.AvailableStatus, () => fixture.Create<bool>() ? "Available" : "Unavailable")
            .With(b => b.SecureStatus, () => fixture.Create<bool>() ? "Secure" : "Unsecure")
            .With(b => b.Gen, () => $"Gen-{fixture.Create<int>() % 10 + 1}"));

        fixture.Customize<CyberSnapsDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Enterprise Mission-Critical Database Snapshot")
            .With(b => b.Description, "Comprehensive snapshot of the enterprise mission-critical database system including all transaction logs, indexes, and metadata for complete disaster recovery capability")
            .With(b => b.Tag, "ENTERPRISE-DB-MASTER-SNAP-001")
            .With(b => b.Type, "Enterprise Database")
            .With(b => b.Location, "/enterprise/snapshots/production/database/master/")
            .With(b => b.Age, 1)
            .With(b => b.Size, 51200)
            .With(b => b.SID, "ENT-DB-SID-001")
            .With(b => b.TimeStamp, DateTime.Now.AddHours(-2).ToString("yyyy-MM-dd HH:mm:ss"))
            .With(b => b.StorageGroupName, "ENTERPRISE-PROD-SG-001")
            .With(b => b.SnapshotName, "ENT-DB-SNAP-MASTER-20240115")
            .With(b => b.LinkSG, "ENT-LINK-SG-001")
            .With(b => b.LinkedSGTime, DateTime.Now.AddHours(-1).ToString("yyyy-MM-dd HH:mm:ss"))
            .With(b => b.UnlinkSGTime, (string)null)
            .With(b => b.Remark, "Critical production snapshot with full consistency verification, encryption validation, and integrity checks completed successfully. Snapshot includes all database objects, stored procedures, triggers, and user-defined functions.")
            .With(b => b.LinkedStatus, "Linked")
            .With(b => b.AvailableStatus, "Available")
            .With(b => b.SecureStatus, "Secure")
            .With(b => b.Gen, "Gen-1"));

        fixture.Customize<CyberSnapsViewModel>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Enterprise Snapshot View")
            .With(b => b.Description, "Comprehensive view of enterprise snapshot management")
            .With(b => b.Type, "Enterprise")
            .With(b => b.Location, "/enterprise/snapshots/")
            .With(b => b.Age, 3)
            .With(b => b.Tag, "ENT-VIEW-001")
            .With(b => b.Size, 25600)
            .With(b => b.LinkedStatus, "Linked")
            .With(b => b.AvailableStatus, "Available"));

        fixture.Customize<SnapshotDetail>(c => c
            .With(b => b.SnapshotId, () => $"SNAP-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.SnapshotName, () => $"Enterprise-Snapshot-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.CreationTime, () => DateTime.Now.AddDays(-(fixture.Create<int>() % 30)).ToString("yyyy-MM-dd HH:mm:ss"))
            .With(b => b.LinkedStatus, () => fixture.Create<bool>() ? "Linked" : "Unlinked")
            .With(b => b.Restored, () => fixture.Create<bool>() ? "Yes" : "No")
            .With(b => b.Expired, () => fixture.Create<bool>() ? "Yes" : "No")
            .With(b => b.ExpiryTime, () => DateTime.Now.AddDays(fixture.Create<int>() % 365 + 1).ToString("yyyy-MM-dd HH:mm:ss"))
            .With(b => b.Secured, () => fixture.Create<bool>() ? "Yes" : "No"));

        fixture.Customize<StorageGroupMonitoring>(c => c
            .With(b => b.StorageGroupName, () => $"ENTERPRISE-SG-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.Compliance, () => fixture.Create<bool>() ? "Compliant" : "Non-Compliant")
            .With(b => b.SRP, () => $"SRP-{fixture.Create<string>().Substring(0, 4)}")
            .With(b => b.ServiceLevel, () => fixture.Create<bool>() ? "Diamond" : "Gold")
            .With(b => b.Capacity, () => $"{fixture.Create<int>() % 10000 + 1000}GB")
            .With(b => b.Emulation, () => fixture.Create<bool>() ? "FBA" : "CKD")
            .With(b => b.SRDFReplicationstatus, () => fixture.Create<bool>() ? "Synchronized" : "Not Synchronized")
            .With(b => b.SnapshotCount, () => (fixture.Create<int>() % 50 + 1).ToString())
            .With(b => b.SnapshotDetails, () => fixture.CreateMany<SnapshotDetail>(fixture.Create<int>() % 5 + 1).ToList()));

        fixture.Customize<PowerMaxDetailVm>(c => c
            .With(b => b.IpAddress, "***********")
            .With(b => b.Version, "*********")
            .With(b => b.Array, "000297900001")
            .With(b => b.ModelName, "PowerMax 8000")
            .With(b => b.StorageGroupMonitoring, () => fixture.CreateMany<StorageGroupMonitoring>(fixture.Create<int>() % 10 + 5).ToList()));

        CreateCyberSnapsCommand = fixture.Create<CreateCyberSnapsCommand>();
        UpdateCyberSnapsCommand = fixture.Create<UpdateCyberSnapsCommand>();
        DeleteCyberSnapsCommand = fixture.Create<DeleteCyberSnapsCommand>();
        CyberSnapsListVm = fixture.Create<CyberSnapsListVm>();
        CyberSnapsDetailVm = fixture.Create<CyberSnapsDetailVm>();
        CyberSnapsViewModel = fixture.Create<CyberSnapsViewModel>();
        PowerMaxDetailVm = fixture.Create<PowerMaxDetailVm>();
    }
}
