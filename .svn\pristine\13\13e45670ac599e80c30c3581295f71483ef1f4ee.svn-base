using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Events.Delete;

namespace ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Delete;

public class DeleteDriftManagementMonitorStatusCommandHandler : IRequestHandler<
    DeleteDriftManagementMonitorStatusCommand, DeleteDriftManagementMonitorStatusResponse>
{
    private readonly IDriftManagementMonitorStatusRepository _driftManagementMonitorStatusRepository;
    private readonly IPublisher _publisher;

    public DeleteDriftManagementMonitorStatusCommandHandler(
        IDriftManagementMonitorStatusRepository driftManagementMonitorStatusRepository, IPublisher publisher)
    {
        _driftManagementMonitorStatusRepository = driftManagementMonitorStatusRepository;

        _publisher = publisher;
    }

    public async Task<DeleteDriftManagementMonitorStatusResponse> Handle(
        DeleteDriftManagementMonitorStatusCommand request, CancellationToken cancellationToken)
    {
        var eventToDelete = await _driftManagementMonitorStatusRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.DriftManagementMonitorStatus),
            new NotFoundException(nameof(Domain.Entities.DriftManagementMonitorStatus), request.Id));

        eventToDelete.IsActive = false;

        await _driftManagementMonitorStatusRepository.UpdateAsync(eventToDelete);

        var response = new DeleteDriftManagementMonitorStatusResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.DriftManagementMonitorStatus),
                eventToDelete.InfraObjectName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new DriftManagementMonitorStatusDeletedEvent { Name = eventToDelete.InfraObjectName },
            cancellationToken);

        return response;
    }
}