using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class TeamResourceFixture : IDisposable
{
    public List<TeamResource> TeamResourcePaginationList { get; set; }
    public List<TeamResource> TeamResourceList { get; set; }
    public TeamResource TeamResourceDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public TeamResourceFixture()
    {
        var fixture = new Fixture();

        TeamResourceList = fixture.Create<List<TeamResource>>();

        TeamResourcePaginationList = fixture.CreateMany<TeamResource>(20).ToList();

        TeamResourceDto = fixture.Create<TeamResource>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
