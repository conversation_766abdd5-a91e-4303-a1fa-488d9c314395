using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUpLog.Queries;

public class GetBackUpLogNameUniqueTests : IClassFixture<BackUpLogFixture>
{
    private readonly BackUpLogFixture _backUpLogFixture;
    private readonly Mock<IBackUpLogRepository> _mockBackUpLogRepository;
    private readonly GetBackUpLogNameUniqueQueryHandler _handler;

    public GetBackUpLogNameUniqueTests(BackUpLogFixture backUpLogFixture)
    {
        _backUpLogFixture = backUpLogFixture;
        _mockBackUpLogRepository = BackUpLogRepositoryMocks.CreateBackUpLogRepository(_backUpLogFixture.BackUpLogs);

        _handler = new GetBackUpLogNameUniqueQueryHandler(_mockBackUpLogRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnTrue_When_DatabaseNameExists()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = existingBackUpLog.DatabaseName,
            Id = Guid.NewGuid().ToString() // Different ID
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue(); // Returns true when name exists (not unique)

        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            existingBackUpLog.DatabaseName, 
            query.Id), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnFalse_When_DatabaseNameDoesNotExist()
    {
        // Arrange
        var nonExistentName = "NonExistentDatabase";
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = nonExistentName,
            Id = Guid.NewGuid().ToString()
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse(); // Returns false when name doesn't exist (is unique)

        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            nonExistentName, 
            query.Id), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnFalse_When_DatabaseNameExistsForSameId()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = existingBackUpLog.DatabaseName,
            Id = existingBackUpLog.ReferenceId // Same ID
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse(); // Returns false when name exists for same ID (is unique for update)

        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            existingBackUpLog.DatabaseName, 
            existingBackUpLog.ReferenceId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnTrue_When_DatabaseNameExistsForDifferentId()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var differentId = Guid.NewGuid().ToString();
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = existingBackUpLog.DatabaseName,
            Id = differentId // Different ID
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue(); // Returns true when name exists for different ID (not unique)

        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            existingBackUpLog.DatabaseName, 
            differentId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnFalse_When_NewDatabaseName()
    {
        // Arrange
        var newDatabaseName = "NewUniqueDatabase";
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = newDatabaseName,
            Id = Guid.NewGuid().ToString()
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse(); // Returns false when name is new (is unique)

        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            newDatabaseName, 
            query.Id), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnFalse_When_EmptyName()
    {
        // Arrange
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = string.Empty,
            Id = Guid.NewGuid().ToString()
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse(); // Returns false when name is empty (is unique)

        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            string.Empty, 
            query.Id), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnFalse_When_NullName()
    {
        // Arrange
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = null,
            Id = Guid.NewGuid().ToString()
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse(); // Returns false when name is null (is unique)

        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            null, 
            query.Id), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnFalse_When_EmptyId()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = existingBackUpLog.DatabaseName,
            Id = string.Empty
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue(); // Returns false when ID is empty

        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            existingBackUpLog.DatabaseName, 
            string.Empty), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnFalse_When_NullId()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = existingBackUpLog.DatabaseName,
            Id = null
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue(); // Returns false when ID is null

        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            existingBackUpLog.DatabaseName, 
            null), Times.Once);
    }

    [Fact]
    public async Task Handle_CheckCaseSensitivity_When_DatabaseNameWithDifferentCase()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var upperCaseName = existingBackUpLog.DatabaseName.ToUpper();
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = upperCaseName,
            Id = Guid.NewGuid().ToString()
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        // The result depends on the repository implementation's case sensitivity
        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            upperCaseName, 
            query.Id), Times.Once);
    }

    [Fact]
    public async Task Handle_CheckMultipleDatabases_When_MultipleDatabasesExist()
    {
        // Arrange
        // Add multiple databases to test uniqueness across all
        var additionalBackUpLog = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "AdditionalServer",
            DatabaseName = "AdditionalDatabase",
            UserName = "AdditionalUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\AdditionalDatabase.bak",
            Type = "Full",
            Status = "Completed",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(additionalBackUpLog);

        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = additionalBackUpLog.DatabaseName,
            Id = Guid.NewGuid().ToString() // Different ID
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue(); // Returns true when name exists (not unique)

        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            additionalBackUpLog.DatabaseName, 
            query.Id), Times.Once);
    }

    [Fact]
    public async Task Handle_CheckInactiveDatabases_When_InactiveDatabaseExists()
    {
        // Arrange
        // Add inactive database to test if it affects uniqueness
        var inactiveBackUpLog = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "InactiveServer",
            DatabaseName = "InactiveDatabase",
            UserName = "InactiveUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\InactiveDatabase.bak",
            Type = "Full",
            Status = "Completed",
            IsActive = false // Inactive
        };
        _backUpLogFixture.BackUpLogs.Add(inactiveBackUpLog);

        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = inactiveBackUpLog.DatabaseName,
            Id = Guid.NewGuid().ToString()
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        // The result depends on whether the repository considers inactive records
        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            inactiveBackUpLog.DatabaseName, 
            query.Id), Times.Once);
    }

    [Fact]
    public async Task Handle_CallRepositoryOnce_When_QueryExecuted()
    {
        // Arrange
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = "TestDatabase",
            Id = Guid.NewGuid().ToString()
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            query.Name, 
            query.Id), Times.Once);
        _mockBackUpLogRepository.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_ReturnBooleanType_When_ValidQuery()
    {
        // Arrange
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = "TestDatabase",
            Id = Guid.NewGuid().ToString()
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<bool>();
        (result == true || result == false).ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_ValidateUniquenessLogic_When_CreatingNewBackUpLog()
    {
        // Arrange - Simulating creation of new backup log
        var newDatabaseName = "NewBackupDatabase";
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = newDatabaseName,
            Id = null // Null ID for new creation
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse(); // Should be false (unique) for new database name

        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            newDatabaseName, 
            null), Times.Once);
    }

    [Fact]
    public async Task Handle_ValidateUniquenessLogic_When_UpdatingExistingBackUpLog()
    {
        // Arrange - Simulating update of existing backup log
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var query = new GetBackUpLogNameUniqueQuery 
        { 
            Name = existingBackUpLog.DatabaseName,
            Id = existingBackUpLog.ReferenceId // Same ID for update
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse(); // Should be false (unique) when updating same record

        _mockBackUpLogRepository.Verify(x => x.IsNameExist(
            existingBackUpLog.DatabaseName, 
            existingBackUpLog.ReferenceId), Times.Once);
    }
}
