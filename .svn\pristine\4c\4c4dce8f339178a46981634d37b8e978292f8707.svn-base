using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class InfraMasterRepositoryTests : IClassFixture<InfraMasterFixture>, IDisposable
{
    private readonly InfraMasterFixture _infraMasterFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly InfraMasterRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public InfraMasterRepositoryTests(InfraMasterFixture infraMasterFixture)
    {
        _infraMasterFixture = infraMasterFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new InfraMasterRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.InfraMasters.RemoveRange(_dbContext.InfraMasters);
        await _dbContext.SaveChangesAsync();
    }

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var infraMasterName = "Test Infrastructure Master";
        var invalidId = "invalid-guid";

        var infraMaster = new InfraMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = infraMasterName,
            IsActive = true
        };

        await _dbContext.InfraMasters.AddAsync(infraMaster);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(infraMasterName, invalidId);

        // Assert
        Assert.True(result); // Should return true when name exists and ID is invalid
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "Non-Existent Infrastructure Master";
        var invalidId = "invalid-guid";

        var infraMaster = new InfraMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Different Infrastructure Master",
            IsActive = true
        };

        await _dbContext.InfraMasters.AddAsync(infraMaster);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(nonExistentName, invalidId);

        // Assert
        Assert.False(result); // Should return false when name doesn't exist
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExistsWithDifferentValidId()
    {
        // Arrange
        await ClearDatabase();
        var infraMasterName = "Test Infrastructure Master";
        var existingId = Guid.NewGuid().ToString();
        var differentId = Guid.NewGuid().ToString();

        var infraMaster = new InfraMaster
        {
            ReferenceId = existingId,
            Name = infraMasterName,
            IsActive = true
        };

        await _dbContext.InfraMasters.AddAsync(infraMaster);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(infraMasterName, differentId);

        // Assert
        Assert.True(result); // Should return true when name exists with different ID
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameExistsWithSameValidId()
    {
        // Arrange
        await ClearDatabase();
        var infraMasterName = "Test Infrastructure Master";
        var existingId = Guid.NewGuid().ToString();

        var infraMaster = new InfraMaster
        {
            ReferenceId = existingId,
            Name = infraMasterName,
            IsActive = true
        };

        await _dbContext.InfraMasters.AddAsync(infraMaster);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(infraMasterName, existingId);

        // Assert
        Assert.False(result); // Should return false when name exists with same ID (editing same record)
    }

    [Fact]
    public async Task IsNameExist_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var infraMasterName = "Test Infrastructure Master";
        var invalidId = "invalid-guid";

        var infraMaster = new InfraMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = infraMasterName,
            IsActive = true
        };

        await _dbContext.InfraMasters.AddAsync(infraMaster);
        await _dbContext.SaveChangesAsync();

        // Act
        var resultExactCase = await _repository.IsNameExist("Test Infrastructure Master", invalidId);
        var resultDifferentCase = await _repository.IsNameExist("test infrastructure master", invalidId);

        // Assert
        Assert.True(resultExactCase); // Exact case match should return true
        Assert.False(resultDifferentCase); // Different case should return false (case sensitive)
    }

    [Fact]
    public async Task IsNameExist_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var infraMasterName = "Test Infrastructure Master";
        var invalidId = "invalid-guid";

        // Act
        var result = await _repository.IsNameExist(infraMasterName, invalidId);

        // Assert
        Assert.False(result); // Should return false when database is empty
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddInfraMaster_WhenValidInfraMaster()
    {
        // Arrange
        await ClearDatabase();
        var infraMaster = new InfraMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Test Infrastructure Master",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(infraMaster);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraMaster.Name, result.Name);
        Assert.Single(_dbContext.InfraMasters);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenInfraMasterIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion
}
