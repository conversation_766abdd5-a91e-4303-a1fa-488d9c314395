﻿using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserListModels;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using Microsoft.AspNetCore.Http;

namespace ContinuityPatrol.Domain.ViewModels.DRCalender;

public class DrCalenderActivityViewModel
{
    public string Id { get; set; }
    public string ActivityName { get; set; }
    public string ActivityType { get; set; }
    public string Description { get; set; }
    public string BusinessServiceId { get; set; }
    public int InvitationNo { get; set; }
    public string BusinessServiceName { get; set; }
    public string CompanyId { get; set; }
    public string Location { get; set; }
    public DateTime ScheduledStartDate { get; set; }
    public DateTime ScheduledEndDate { get; set; }
    public string Responsibility { get; set; }
    public string ActivityStatus { get; set; }
    public string RecipientTwo { get; set; }
    public string SetReminders { get; set; }
    public string WorkflowProfiles { get; set; }
    public IFormFile File { get; set; }
    public string FileName { get; set; }



    public List<WorkflowProfileListVm> PaginatedProfileList { get; set; }
    public PaginatedResult<DRCalendarUserList> PaginatedUserList { get; set; }
    public PaginatedResult<DRCalendarUserList> PaginatedUserList2 { get; set; }
    public List<BusinessServiceListVm> BusinessServices { get; set; }
}