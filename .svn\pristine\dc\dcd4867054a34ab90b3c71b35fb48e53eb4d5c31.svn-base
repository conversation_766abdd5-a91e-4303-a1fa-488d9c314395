using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IBulkImportActionResultRepository : IRepository<BulkImportActionResult>
{
    //Task<bool> IsNameExist(string name, string id);
    Task<BulkImportActionResult> GetByEntityIdAndBulkImportOperationId(string entityId, string bulkImportOperationId);
    Task<List<BulkImportActionResult>> GetByOperationIdAndOperationGroupId(string operationId, string operationGroupId);
    Task<List<BulkImportActionResult>> GetByOperationIdsAndOperationGroupIds(List<string> operationIds, List<string> operationGroupIds);
    Task<BulkImportActionResult> GetActionByOperationGroupIdAndEntityType(string operationGroupId,
        string entityType);

    Task<List<BulkImportActionResult>> GetBulkImportActionResultOperationGroupId(string operationGroupId);
}