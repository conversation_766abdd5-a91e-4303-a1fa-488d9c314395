﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = 'AzureMssqlPaas';
let infraObjectId = sessionStorage.getItem("infraobjectId");

setTimeout(() => { azuremssqlmonitorstatus(mId, monitortype) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
setTimeout(() => { azureSQLServer(infraObjectId) }, 250)

$('#mssqlserver').hide();
async function azureSQLServer(id) {

    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
    let data = {}
    data.infraObjectId = id;
    let mssqlServerData = await getAysncWithHandler(url, data);

    if (mssqlServerData != null && mssqlServerData?.length > 0) {
        mssqlServerData?.forEach(data => {
            let value = data?.isServiceUpdate
            let parsed = []
            if (value && value !== 'NA') parsed = JSON?.parse(value)
            if (Array.isArray(parsed)) {
                parsed?.forEach(s => {
                    if (s?.Services?.length) {
                        $('#mssqlserver').show();
                        bindAzureSQLServer(mssqlServerData)
                    }
                })
            }
        })

    } else {
        $('#mssqlserver').hide();
    }

}
function bindAzureSQLServer(mssqlServerData) {

    let prType = { IpAddress: '--', Services: [] };
    let drType = { IpAddress: '--', Services: [] };

    // Loop through each item to find PR and DR entries
    mssqlServerData?.forEach(item => {
        let parsedServices = [];
        try {
            const value = item?.isServiceUpdate
            if (value && value !== 'NA') {
                parsedServices = JSON.parse(item?.isServiceUpdate)
            }
        } catch (e) {
            console.error("Invalid JSON in isServiceUpdate:", item?.isServiceUpdate);
        }

        parsedServices?.forEach(serviceGroup => {
            if (serviceGroup?.Type === 'PR') {
                prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
                prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];
            } else if (serviceGroup?.Type === 'DR') {
                drType.IpAddress = serviceGroup?.IpAddress || drType?.IpAddress;
                drType.Services = [...drType.Services, ...(serviceGroup?.Services || [])];
            }
        });
    });

    // Set header IPs
    $('#prIp').text('Primary (' + prType?.IpAddress + ')');
    $('#drIp').text('DR (' + drType?.IpAddress + ')');

    const prStatusSummary = getStatusSummary(prType.Services.map(s => s.Status).filter(Boolean));
    const drStatusSummary = getStatusSummary(drType.Services.map(s => s.Status).filter(Boolean));
    $('#prIp').html(`Primary (${prType.IpAddress}) <br ><span class="status-summary text-muted small">${prStatusSummary}</span>`);
    $('#drIp').html(`DR (${drType.IpAddress}) <br ><span class="status-summary text-muted small">${drStatusSummary}</span>`);

    // Unique list of all service names from both PR and DR
    let allServiceNames = [...new Set([
        ...prType?.Services?.map(s => s?.ServiceName),
        ...drType?.Services?.map(s => s?.ServiceName)
    ])];

    // Build table rows
    let tbody = $('#mssqlserverbody');
    tbody.empty();

    allServiceNames?.forEach(serviceName => {
        let prService = prType?.Services?.find(s => s?.ServiceName === serviceName);
        let drService = drType?.Services?.find(s => s?.ServiceName === serviceName);

        let prStatus = prService ? prService?.Status : '--';
        let drStatus = drService ? drService?.Status : '--';
        let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
        let drIcon = drStatus !== '--' ? `<i class="${getStatusIconClass(drStatus)} me-2 fs-6"></i>` : '';
        const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";

        let row = `
            <tr>
                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
                <td>${prIcon}${prStatus}</td>
                <td>${drIcon}${drStatus}</td>
            </tr>
        `;
        tbody.append(row);
    });
}
function getStatusSummary(arr) {
    let countMap = {};
    arr?.forEach(status => {
        countMap[status] = (countMap[status] || 0) + 1;
    });
    let total = arr?.length;
    let statusSummary = Object.entries(countMap)
        .map(([status, count]) => `${count} ${status}`)
        .join(', ');
    return statusSummary ? `${statusSummary} out of ${total} services` : '--';
}
function getStatusIconClass(status) {
    if (!status) return "text-danger cp-disable";

    const lowerStatus = status.toLowerCase();
    if (lowerStatus === "running") {
        return "text-success cp-reload cp-animate";
    } else if (lowerStatus === "error" || lowerStatus === "stopped") {
        return "text-danger cp-fail-back";
    } else {
        return "text-danger cp-disable";
    }
}

$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})
async function azuremssqlmonitorstatus(id, type) {

    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
}
function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}

let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'

function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}
function propertiesData(value) {
    
    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    else {
        let data = JSON?.parse(value?.properties);
        console.log(data, 'mssqlproperties')
        let customSite = data?.AzureMssqlPaasMonitoring?.length > 1;
        if (customSite) {
            $("#Sitediv").show();
        } else {
            $("#Sitediv").hide();
        }


        $(".siteContainer").empty();
        data?.AzureMssqlPaasMonitoring?.forEach((a, index) => {
            let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        });

        if (data?.AzureMssqlPaasMonitoring?.length > 0) {
            
            $("#siteName0 .nav-link").addClass("active");
            displaySiteData(data?.AzureMssqlPaasMonitoring[0]);
        }

        let defaultSite = data?.AzureMssqlPaasMonitoring?.find(d => d?.Type === 'DR') || data?.AzureMssqlPaasMonitoring[0];
        
        if (defaultSite) {
            
            displaySiteData(defaultSite);
        }

        $(document).on('click', '.siteListChange', function () {
            
            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0]?.id
            let getSiteName = $(`#${siteId} .siteName`).text()
            $('#customSite').html(getSiteName)

            let MonitoringModel = data?.AzureMssqlPaasMonitoring.find(d => d?.Type === getSiteName);
            if (MonitoringModel) {
                
                displaySiteData(MonitoringModel);
            }
        });

        function displaySiteData(siteData) {
            
            let obj = {};
            // $('.dynamicSite-header').text(siteData.Type).attr('title', siteData.Type);

            for (let key in siteData?.Replica) {
                obj[`DR_` + key] = siteData?.Replica[key];
            }

            let MonitoringModelMssqlpass = [
                "DR_ServerName", "DR_ServerRegion", "DR_ServerFQDN",
                "DR_ServerRole", "DR_DatabaseName", "DR_DatabaseStatus", "DR_ReplicationState",
                "DR_ReplicationType", "DR_Datalag","DR_LastReplicationTime"
            ];

            if (Object.keys(obj)?.length > 0) {
                bindProperties(obj, MonitoringModelMssqlpass, value);
            }
        }

        let dbDetail = data?.AzureMssqlPaasPRMonitoring?.Primary
        console.log(dbDetail,'dbDetail')
        const dbDetailsProp = [
            "ServerName", "ServerRegion", "ServerFQDN",
            "ServerRole", "DatabaseName", "DatabaseStatus", "ReplicationState",
            "ReplicationType", "Datalag","LastReplicationTime"
        ];

        bindProperties(dbDetail, dbDetailsProp, value);
        //Database Details

        //Datalag
        const datalag = checkAndReplace(data?.PR_Datalag);
        let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0") ? `${(datalag)}` : 'NA';

        var result = "";

        if (dataLagValue?.includes(".")) {
            var value = dataLagValue?.split(".");
            var hours = value[0] * 24;
            var minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
            var min = minutes?.split(':');
            var firstValue = parseInt(min[0]) + parseInt(hours);
            result = firstValue + ":" + min[1];
            const minute = (parseInt(result[0]) * 60) + parseInt(result[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        }
        else if (dataLagValue?.includes("+")) {
            const value = dataLagValue.split(" ");
            result = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')

        }
        else {
            result = dataLagValue?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        }


    }
}

function setPropData(data, propSets, value) {
    
    propSets?.forEach(properties => {
        bindProperties(data, properties, value);
    });
}

function bindProperties(data, properties, value) {
    
           properties?.forEach(property => {
            const values = data[property];
            const displayedValue = value !== undefined ? checkAndReplace(values) : 'NA';
            // Displayed value with icon
            const iconHtml = getIconClass(displayedValue, property, data, value);
            const mergeValue = `${iconHtml}${displayedValue}`;
            $(`#${property}`).html(mergeValue).attr('title', displayedValue);
        });
   
}

function getIconClass(displayedValue, property, data, value) {

    let prservericon = data?.ServerName ? "text-primary cp-server me-1" : "text-danger cp-disable"
    let drservericon = data?.DR_ServerName ? "text-primary cp-server me-1" : "text-danger cp-disable"
    let prServerVersion = data?.ServerRegion ? "cp-location text-primary" : "text-danger cp-disable"
    let drServerVersion = data?.DR_ServerRegion ? "cp-location text-primary" : "text-danger cp-disable"
    let prserverfdqn = data?.ServerFQDN ? "cp-control-file-type text-primary" : "text-danger cp-disable"
    let drserverfdqn = data?.DR_ServerFQDN ? "cp-control-file-type text-primary" : "text-danger cp-disable"
    let prresource = data?.ServerRole ? "cp-manage-server text-primary" : "text-danger cp-disable"
    let drresource = data?.DR_ServerRole ? "cp-manage-server text-primary" : "text-danger cp-disable"
    let prlocation = data?.DatabaseName ? "cp-database text-primary" : "text-danger cp-disable"
    let drlocation = data?.DR_DatabaseName ? "cp-database text-primary" : "text-danger cp-disable"
    let prrole = data?.DatabaseStatus ? "cp-file-edits text-success" : "text-danger cp-disable"
    let drrole = data?.DR_DatabaseStatus ? "cp-file-edits text-success" : "text-danger cp-disable"
    let prreplication = data?.ReplicationType ? "cp-replication-type text-primary mt-2" : "text-danger cp-disable"
    let drreplication = data?.DR_ReplicationType ? "cp-replication-type text-primary mt-2" : "text-danger cp-disable"
    let prdatalag = data?.Datalag ? "cp-time text-primary mt-2" : "text-danger cp-disable"
    let drdatalag = data?.DR_Datalag ? "cp-time text-primary mt-2" : "text-danger cp-disable"

    const iconMapping = {
        'ServerName': prservericon,
        'DR_ServerName': drservericon,
        'ServerRegion': prServerVersion,
        'DR_ServerRegion': drServerVersion,
        'ServerFQDN': prserverfdqn,
        'DR_ServerFQDN': drserverfdqn,
        'ServerRole': prresource,
        'DR_ServerRole': drresource,
        'DatabaseName': prlocation,
        'DR_DatabaseName': drlocation,

        'DatabaseStatus': prrole,
        'DR_DatabaseStatus': drrole,
        'ReplicationType': prreplication,
        'DR_ReplicationType': drreplication,
        'Datalag': prdatalag,
        'DR_Datalag': drdatalag
    };

    let iconClass = iconMapping[property] || '';

    switch (displayedValue?.toLowerCase()) {

        case 'not allowed':
        case 'na':
            iconClass = 'text-danger cp-disable';
            break;
        case 'no':
            iconClass = 'text-danger cp-disagree';
            break;
        case 'streaming':
            iconClass = 'text-success cp-refresh';
            break;
        case 'running':
        case 'run':
            iconClass = 'text-success cp-reload cp-animate';
            break;
        case 'stopped':
        case 'stop':
            iconClass = 'text-danger cp-Stopped';
            break;
        case 'f':
        case 'false':
        case 'defer':
        case 'deferred':
            iconClass = 'text-danger cp-error';
            break;
        case 't':
        case 'yes':
        case 'deferred':
            iconClass = 'text-success cp-agree';
            break;
        case 'valid':
            iconClass = 'text-success cp-success';
            break;
        case 'pending':
            iconClass = 'text-warning cp-pending';
            break;
        case 'pause':
        case 'paused':
            iconClass = 'text-warning cp-circle-pause';
            break;
        case 'manual':
            iconClass = 'text-warning cp-settings';
            break;
        case 'synchronous_commit':
        case 'synchronized':
        case 'synchronizing':
        case 'sync':
            iconClass = 'text-success cp-refresh';
            break;
        case 'asynchronous_commit':
        case 'asynchronizing':
        case 'asynchronized':
        case 'async':
            iconClass = 'text-danger cp-refresh';
            break;
        case 'online':
            iconClass = 'text-success cp-online';
            break;
        case 'offline':
            iconClass = 'text-danger cp-offline';
            break;
        case 'disabled':
        case 'disable':
            iconClass = 'text-danger cp-disables';
            break;
        case 'enabled':
        case 'enable':
            iconClass = 'text-success cp-enables';
            break;
        case 'connected':
        case 'connect':
            iconClass = 'text-success cp-connected';
            break;
        case 'disconnected':
        case 'disconnect':
            iconClass = 'text-danger cp-disconnecteds';
            break;
        case 'standby':
        case 'to standby':
            iconClass = 'text-warning cp-control-file-type';
            break;
        case 'required':
        case 'require':
            iconClass = 'text-warning cp-warning';
            break
        case 'healthy':
            iconClass = 'text-success cp-health-success';
            break;
        case 'not_healthy':
        case 'nothealthy':
        case 'unhealthy':
            iconClass = 'text-danger cp-health-error';
            break;
        case 'error':
            iconClass = 'text-danger cp-fail-back';
            break;
        case 'on':
            iconClass = 'text-success cp-end';
            break;
        case 'off':
            iconClass = 'text-danger cp-end';
            break;
        case 'current':
        case 'read write':
            iconClass = 'text-success cp-file-edits';
            break
        case 'primary':
            iconClass = 'text-primary cp-list-prsite';
            break
        case 'secondary':
            iconClass = 'text-info cp-dr';
            break
        case 'physical standby':
            iconClass = 'text-info cp-physical-drsite';
            break
        default:

            break;

    }
    return iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
}