﻿using ContinuityPatrol.Application.Features.FormType.Commands.Create;
using ContinuityPatrol.Application.Features.FormType.Commands.Update;
using ContinuityPatrol.Application.Features.FormType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FormType.Queries.GetNames;
using ContinuityPatrol.Application.Features.FormType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FormTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class FormTypeService : BaseClient, IFormTypeService
{
    public FormTypeService(IConfiguration config, IAppCache cache, ILogger<FormTypeService> logger) : base(config, cache, logger)
    {
            
    }

    public async Task<List<FormTypeNameVm>> GetFormTypeNames()
    {
        var request = new RestRequest("api/v6/formtype/names");

        return await GetFromCache<List<FormTypeNameVm>>(request, "GetFormTypeNames");
    }

    public async Task<bool> IsFormTypeNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/formtype/name-exist?formTypeName={name}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<List<FormTypeListVm>> GetFormTypeList()
    {
        var request = new RestRequest("api/v6/formtype");

        return await GetFromCache<List<FormTypeListVm>>(request, "GetFormTypeList");
    }

    public async Task<BaseResponse> CreateAsync(CreateFormTypeCommand createFormTypeCommand)
    {
        var request = new RestRequest("api/v6/formtype", Method.Post);

        request.AddJsonBody(createFormTypeCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateFormTypeCommand updateFormTypeCommand)
    {
        var request = new RestRequest("api/v6/formtype", Method.Put);

        request.AddJsonBody(updateFormTypeCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string formTypeId)
    {
        var request = new RestRequest($"api/v6/formtype/{formTypeId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<FormTypeDetailVm> GetByReferenceId(string formTypeId)
    {
        var request = new RestRequest($"api/v6/formtype/{formTypeId}");

        return await Get<FormTypeDetailVm>(request);
    }

    public async Task<PaginatedResult<FormTypeListVm>> GetPaginatedFormTypes(GetFormTypePaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/formtype/paginated-list");

        return await Get<PaginatedResult<FormTypeListVm>>(request);
    }
}