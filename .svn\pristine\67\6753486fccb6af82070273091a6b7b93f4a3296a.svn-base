﻿// UserGroupTestNew.js - Complete QUnit test coverage for UserGroup.js

// Static test data for comprehensive testing
const STATIC_TEST_DATA = {
    validGroupName: "TestGroup123",
    invalidGroupName: "Test<Group>",
    longGroupName: "A".repeat(101),
    shortGroupName: "AB",
    numericGroupName: "12345",
    underscoreGroupName: "_TestGroup",
    spaceGroupName: " TestGroup",
    numberStartGroupName: "1TestGroup",
    validDescription: "This is a valid test description",
    longDescription: "A".repeat(251),
    invalidDescription: "Test<Description>",
    existingGroupName: "ExistingGroup",
    nonExistingGroupName: "NewGroup",
    validUsers: [1, 2],
    emptyUsers: [],
    multipleSpaces: "Test   Multiple   Spaces",
    leadingSpaces: "  Leading spaces"
};

// Mock AJAX response data
const MOCK_AJAX_DATA = {
    userListSuccess: {
        success: true,
        data: [
            { id: 1, loginName: "user1" },
            { id: 2, loginName: "user2" },
            { id: 3, loginName: "user3" },
            { id: 4, loginName: "user4" }
        ]
    },
    userListError: {
        success: false,
        message: "No users found"
    },
    userGroupPagination: {
        data: [
            {
                id: 1,
                groupName: "Test Group 1",
                groupDescription: "Test Description 1",
                userProperties: '[{"id":1,"loginname":"user1"},{"id":2,"loginname":"user2"}]',
                isParent: false
            },
            {
                id: 2,
                groupName: "Test Group 2",
                groupDescription: "Test Description 2",
                userProperties: '[{"id":3,"loginname":"user3"}]',
                isParent: true
            }
        ],
        totalPages: 1,
        totalCount: 2
    }
};

// Test Module 1: Basic Functionality Tests
QUnit.module("Basic UserGroup Functionality", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <table id="userGroupTable">
                <thead><tr><th>Sr. No.</th><th>Group Name</th><th>Group Description</th><th>Users</th><th>Actions</th></tr></thead>
                <tbody></tbody>
            </table>
            <input type="text" id="search-inp" placeholder="Search...">
            <input type="text" id="userGroupNameId" name="GroupName">
            <span id="userGroupNameErr"></span>
            <textarea id="userGroupDescriptionId" name="GroupDescription"></textarea>
            <span id="userGroupDescriptionErr"></span>
            <select id="userGroupUserId" name="UserIds" multiple></select>
            <span id="userGroupUserErr"></span>
            <button id="btnUserGroupSave">Save</button>
            <form id="CreateForm"></form>
            <input type="hidden" id="userGroupId" value="">
            <input type="hidden" id="userGroupProperties" value="">
            <div id="AdminCreate" data-create-permission="true"></div>
            <div id="AdminDelete" data-delete-permission="true"></div>
        `);

        // Reset global variables
        window.userGroupId = '';
        window.userGroupData = '';
        window.Userlist = [];
        window.UsersNameList = [];
        if (typeof window.selectedValues !== 'undefined') {
            window.selectedValues = [];
        }
    });

    QUnit.test("should initialize with correct global variables", assert => {
        assert.equal(window.userGroupId, '', "userGroupId should be empty initially");
        assert.deepEqual(window.Userlist, [], "Userlist should be empty array initially");
        assert.deepEqual(window.UsersNameList, [], "UsersNameList should be empty array initially");
    });

    QUnit.test("should handle permission checking with static data", assert => {
        $('#AdminCreate').data('create-permission', 'false');
        let permission = $("#AdminCreate").data("create-permission").toLowerCase();

        assert.equal(permission, 'false', "Should read create permission correctly");

        // Test permission true
        $('#AdminCreate').data('create-permission', 'true');
        permission = $("#AdminCreate").data("create-permission").toLowerCase();
        assert.equal(permission, 'true', "Should read true permission correctly");
    });

    QUnit.test("should handle form elements correctly", assert => {
        // Test form elements exist
        assert.ok($('#userGroupNameId').length, "Group name input should exist");
        assert.ok($('#userGroupDescriptionId').length, "Description textarea should exist");
        assert.ok($('#userGroupUserId').length, "User select should exist");
        assert.ok($('#btnUserGroupSave').length, "Save button should exist");

        // Test setting values with static data
        $('#userGroupNameId').val(STATIC_TEST_DATA.validGroupName);
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.validDescription);

        assert.equal($('#userGroupNameId').val(), STATIC_TEST_DATA.validGroupName, "Should set group name");
        assert.equal($('#userGroupDescriptionId').val(), STATIC_TEST_DATA.validDescription, "Should set description");
    });

    QUnit.test("should handle user selection with static data", assert => {
        // Add options with static data
        $('#userGroupUserId').append(`<option value="1" data-id="1" data-name="user1">user1</option>`);
        $('#userGroupUserId').append(`<option value="2" data-id="2" data-name="user2">user2</option>`);

        // Select users
        $('#userGroupUserId').val(STATIC_TEST_DATA.validUsers.map(String));

        assert.deepEqual($('#userGroupUserId').val(), ['1', '2'], "Should select users correctly");
    });
});

// Test Module 2: Input Validation Tests
QUnit.module("Input Validation", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <input type="text" id="userGroupNameId" name="GroupName">
            <span id="userGroupNameErr"></span>
            <textarea id="userGroupDescriptionId" name="GroupDescription"></textarea>
            <span id="userGroupDescriptionErr"></span>
            <select id="userGroupUserId" name="UserIds" multiple></select>
            <span id="userGroupUserErr"></span>
        `);
    });

    QUnit.test("should validate group name with static test data - valid name", assert => {
        $('#userGroupNameId').val(STATIC_TEST_DATA.validGroupName);
        const value = $('#userGroupNameId').val();

        // Basic validation checks
        assert.ok(value.length >= 3, "Valid name should be at least 3 characters");
        assert.ok(value.length <= 100, "Valid name should be at most 100 characters");
        assert.notOk(value.includes('<'), "Valid name should not contain < character");
        assert.notOk(/^[0-9]/.test(value), "Valid name should not start with number");
        assert.notOk(/^_/.test(value), "Valid name should not start with underscore");
        assert.notOk(/^\s/.test(value), "Valid name should not start with space");
    });

    QUnit.test("should validate group name with static test data - invalid characters", assert => {
        $('#userGroupNameId').val(STATIC_TEST_DATA.invalidGroupName);
        const value = $('#userGroupNameId').val();

        assert.ok(value.includes('<'), "Invalid name should contain < character");
    });

    QUnit.test("should validate group name with static test data - length limits", assert => {
        // Test too long
        $('#userGroupNameId').val(STATIC_TEST_DATA.longGroupName);
        let value = $('#userGroupNameId').val();
        assert.ok(value.length > 100, "Long name should exceed 100 characters");

        // Test too short
        $('#userGroupNameId').val(STATIC_TEST_DATA.shortGroupName);
        value = $('#userGroupNameId').val();
        assert.ok(value.length < 3, "Short name should be less than 3 characters");
    });

    QUnit.test("should validate description with static test data", assert => {
        // Test valid description
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.validDescription);
        let value = $('#userGroupDescriptionId').val();
        assert.ok(value.length <= 250, "Valid description should be within limit");
        assert.notOk(value.includes('<'), "Valid description should not contain < character");

        // Test long description
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.longDescription);
        value = $('#userGroupDescriptionId').val();
        assert.ok(value.length > 250, "Long description should exceed 250 characters");
    });

    QUnit.test("should handle input sanitization", assert => {
        // Test multiple spaces
        $('#userGroupNameId').val(STATIC_TEST_DATA.multipleSpaces);
        let value = $('#userGroupNameId').val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        assert.equal(sanitizedValue, "Test Multiple Spaces", "Should collapse multiple spaces");

        // Test leading spaces
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.leadingSpaces);
        value = $('#userGroupDescriptionId').val();
        sanitizedValue = value.replace(/^\s+/, '').replace(/\s{2,}/g, ' ');
        assert.equal(sanitizedValue, "Leading spaces", "Should remove leading spaces");
    });
});

// Test Module 3: AJAX Operations Tests
QUnit.module("AJAX Operations", hooks => {
    let server;

    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <select id="userGroupUserId" multiple></select>
            <span id="userGroupUserErr"></span>
        `);

        server = sinon.createFakeServer();
        server.respondImmediately = true;

        // Setup mock responses
        server.respondWith("GET", /api\/UserGroup\/GetUserNames/,
            [200, { "Content-Type": "application/json" }, JSON.stringify(MOCK_AJAX_DATA.userListSuccess)]);
        server.respondWith("GET", /api\/UserGroup\/IsGroupNameExist/,
            [200, { "Content-Type": "application/json" }, "false"]);
        server.respondWith("GET", /\/api\/UserGroup\/GetPaginationList/,
            [200, { "Content-Type": "application/json" }, JSON.stringify(MOCK_AJAX_DATA.userGroupPagination)]);
    });

    hooks.afterEach(() => {
        server.restore();
    });

    QUnit.test("should fetch user list successfully", assert => {
        const done = assert.async();

        $.ajax({
            type: "GET",
            url: "/api/UserGroup/GetUserNames",
            dataType: "json",
            success: function (result) {
                assert.ok(result.success, "AJAX call should succeed");
                assert.equal(result.data.length, 4, "Should return 4 users");
                assert.equal(result.data[0].loginName, "user1", "First user should be user1");
                done();
            }
        });
    });

    QUnit.test("should handle user list error response", assert => {
        server.respondWith("GET", /api\/UserGroup\/GetUserNames/,
            [200, { "Content-Type": "application/json" }, JSON.stringify(MOCK_AJAX_DATA.userListError)]);

        const done = assert.async();

        $.ajax({
            type: "GET",
            url: "/api/UserGroup/GetUserNames",
            dataType: "json",
            success: function (result) {
                assert.notOk(result.success, "Should handle unsuccessful response");
                done();
            }
        });
    });

    QUnit.test("should check group name existence", assert => {
        const done = assert.async();

        $.ajax({
            url: "/api/UserGroup/IsGroupNameExist",
            method: "GET",
            data: { Groupname: STATIC_TEST_DATA.existingGroupName, id: null },
            success: function (result) {
                let exists = (result === "true" || result === true);
                assert.notOk(exists, "Should return false for non-existing name");
                done();
            }
        });
    });

    QUnit.test("should handle AJAX server errors", assert => {
        server.respondWith("GET", /api\/UserGroup\/GetUserNames/,
            [500, {}, "Server error"]);

        const done = assert.async();

        $.ajax({
            url: "/api/UserGroup/GetUserNames",
            method: "GET",
            error: function (xhr) {
                assert.equal(xhr.status, 500, "Should handle server errors correctly");
                done();
            }
        });
    });
});

// Test Module 4: Event Handling Tests
QUnit.module("Event Handling", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <input type="text" id="userGroupNameId" name="GroupName">
            <span id="userGroupNameErr"></span>
            <textarea id="userGroupDescriptionId" name="GroupDescription"></textarea>
            <span id="userGroupDescriptionErr"></span>
            <select id="userGroupUserId" name="UserIds" multiple>
                <option value="1" data-id="1" data-name="user1">user1</option>
                <option value="2" data-id="2" data-name="user2">user2</option>
            </select>
            <span id="userGroupUserErr"></span>
            <button id="btnUserGroupSave">Save</button>
            <form id="CreateForm"></form>
            <input type="hidden" id="userGroupId" value="">
            <input type="hidden" id="userGroupProperties" value="">
        `);

        window.UsersNameList = [];
        window.userGroupId = '';
    });

    QUnit.test("should handle group name input event with static data", assert => {
        $('#userGroupNameId').val(STATIC_TEST_DATA.validGroupName);
        $('#userGroupNameId').trigger('input');

        assert.equal($('#userGroupNameId').val(), STATIC_TEST_DATA.validGroupName, "Input value should be set correctly");
    });

    QUnit.test("should handle description input event with static data", assert => {
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.validDescription);
        $('#userGroupDescriptionId').trigger('input');

        assert.equal($('#userGroupDescriptionId').val(), STATIC_TEST_DATA.validDescription, "Description value should be set correctly");
    });

    QUnit.test("should handle user selection change event", assert => {
        // Select users with static data
        $('#userGroupUserId').val(STATIC_TEST_DATA.validUsers.map(String));

        // Simulate building UsersNameList
        window.UsersNameList = [];
        $('#userGroupUserId').find('option:selected').each(function () {
            let id = $(this).data('id');
            let name = $(this).data('name');
            if (id && name) {
                window.UsersNameList.push({ id: id, loginname: name });
            }
        });

        assert.equal(window.UsersNameList.length, 2, "Should build UsersNameList correctly");
    });

    QUnit.test("should handle save button click event", assert => {
        let saveClicked = false;

        $('#btnUserGroupSave').on('click', function () {
            saveClicked = true;
        });

        $('#btnUserGroupSave').trigger('click');

        assert.ok(saveClicked, "Save button click Successfully");
    });

    QUnit.test("should prevent special characters in description", assert => {
        const specialChars = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "+", "=", "{", "}", "[", "]", "|", ":", ";", "'", "\"", "<", ">", "?", "/", "\\"];

        specialChars.forEach(char => {
            const event = { key: char, preventDefault: sinon.spy() };

            // Simulate the key check logic
            if (specialChars.includes(event.key)) {
                event.preventDefault();
            }

            assert.ok(event.preventDefault.calledOnce, `preventDefault should be called for ${char}`);
        });
    });
});

// Test Module 5: Form Submission and Validation Tests
QUnit.module("Form Submission and Validation", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <input type="text" id="userGroupNameId" name="GroupName">
            <span id="userGroupNameErr"></span>
            <textarea id="userGroupDescriptionId" name="GroupDescription"></textarea>
            <span id="userGroupDescriptionErr"></span>
            <select id="userGroupUserId" name="UserIds" multiple>
                <option value="1" data-id="1" data-name="user1">user1</option>
                <option value="2" data-id="2" data-name="user2">user2</option>
            </select>
            <span id="userGroupUserErr"></span>
            <button id="btnUserGroupSave">Save</button>
            <form id="CreateForm"></form>
            <input type="hidden" id="userGroupId" value="">
            <input type="hidden" id="userGroupProperties" value="">
        `);

        window.UsersNameList = [];
        window.userGroupId = '';
    });

    QUnit.test("should validate complete form with static data - valid case", assert => {
        // Setup valid form data
        $('#userGroupNameId').val(STATIC_TEST_DATA.validGroupName);
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.validDescription);
        $('#userGroupUserId').val(STATIC_TEST_DATA.validUsers.map(String));

        // Build UsersNameList
        window.UsersNameList = [];
        $('#userGroupUserId').find('option:selected').each(function () {
            let id = $(this).data('id');
            let name = $(this).data('name');
            if (id && name) {
                window.UsersNameList.push({ id: id, loginname: name });
            }
        });

        // Validate form data
        const nameValid = $('#userGroupNameId').val().length >= 3 && $('#userGroupNameId').val().length <= 100;
        const descValid = $('#userGroupDescriptionId').val().length <= 250;
        const usersValid = $('#userGroupUserId').val().length > 0;

        assert.ok(nameValid, "Group name should be valid");
        assert.ok(descValid, "Description should be valid");
        assert.ok(usersValid, "User selection should be valid");
        assert.equal(window.UsersNameList.length, 2, "UsersNameList should be populated");
    });

    QUnit.test("should validate complete form with static data - invalid cases", assert => {
        // Test invalid name
        $('#userGroupNameId').val(STATIC_TEST_DATA.invalidGroupName);
        const nameInvalid = $('#userGroupNameId').val().includes('<');
        assert.ok(nameInvalid, "Invalid group name should be detected");

        // Test long description
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.longDescription);
        const descInvalid = $('#userGroupDescriptionId').val().length > 250;
        assert.ok(descInvalid, "Long description should be detected");

        // Test empty user selection
        $('#userGroupUserId').val([]);
        const usersInvalid = $('#userGroupUserId').val().length === 0;
        assert.ok(usersInvalid, "Empty user selection should be detected");
    });

    QUnit.test("should handle form submission with user properties", assert => {
        // Setup form with static data
        $('#userGroupNameId').val(STATIC_TEST_DATA.validGroupName);
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.validDescription);
        $('#userGroupUserId').val(STATIC_TEST_DATA.validUsers.map(String));

        // Build UsersNameList
        window.UsersNameList = [
            { id: 1, loginname: "user1" },
            { id: 2, loginname: "user2" }
        ];

        // Simulate setting user properties
        const userProperties = JSON.stringify(window.UsersNameList);
        $('#userGroupProperties').val(userProperties);

        assert.equal($('#userGroupProperties').val(), userProperties, "User properties should be set correctly");

        // Validate JSON
        const parsedProperties = JSON.parse($('#userGroupProperties').val());
        assert.equal(parsedProperties.length, 2, "Parsed properties should have 2 users");
        assert.equal(parsedProperties[0].loginname, "user1", "First user should be user1");
    });
});

// Test Module 6: Edge Cases and Error Handling
QUnit.module("Edge Cases and Error Handling", hooks => {
    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <input type="text" id="userGroupNameId" name="GroupName">
            <span id="userGroupNameErr"></span>
            <textarea id="userGroupDescriptionId" name="GroupDescription"></textarea>
            <span id="userGroupDescriptionErr"></span>
            <select id="userGroupUserId" name="UserIds" multiple></select>
            <span id="userGroupUserErr"></span>
        `);
    });

    QUnit.test("should handle null and undefined values", assert => {
        // Test null values
        $('#userGroupNameId').val(null);
        assert.equal($('#userGroupNameId').val(), '', "Should handle null values");

        // Test undefined values
        $('#userGroupNameId').val(undefined);
        assert.equal($('#userGroupNameId').val(), '', "Should handle undefined values");

        // Test empty strings
        $('#userGroupNameId').val('');
        assert.equal($('#userGroupNameId').val(), '', "Should handle empty strings");
    });

    QUnit.test("should handle extreme length inputs", assert => {
        // Test with extremely long input
        const extremelyLongName = "A".repeat(1000);
        $('#userGroupNameId').val(extremelyLongName);
        assert.ok($('#userGroupNameId').val().length > 100, "Should handle extremely long inputs");

        // Test with exactly max length
        const maxLengthName = "A".repeat(100);
        $('#userGroupNameId').val(maxLengthName);
        assert.equal($('#userGroupNameId').val().length, 100, "Should handle max length inputs");
    });

    QUnit.test("should handle special Unicode characters", assert => {
        // Test with Unicode characters
        const unicodeInput = "Test🚀Group";
        $('#userGroupNameId').val(unicodeInput);
        assert.equal($('#userGroupNameId').val(), unicodeInput, "Should handle Unicode characters");

        // Test with HTML entities
        const htmlInput = "Test&lt;Group&gt;";
        $('#userGroupNameId').val(htmlInput);
        assert.equal($('#userGroupNameId').val(), htmlInput, "Should handle HTML entities");
    });

    QUnit.test("should handle malformed JSON in user properties", assert => {
        const malformedJSON = '{"id":1,"name":"user1"'; // Missing closing brace

        try {
            JSON.parse(malformedJSON);
            assert.ok(false, "Should throw error for malformed JSON");
        } catch (e) {
            assert.ok(true, "Should handle malformed JSON gracefully");
        }
    });

    QUnit.test("should handle empty arrays and objects", assert => {
        // Test empty user selection
        $('#userGroupUserId').val([]);
        const emptySelection = $('#userGroupUserId').val();
        assert.deepEqual(emptySelection, [], "Should handle empty user selection");

        // Test empty UsersNameList
        window.UsersNameList = [];
        const emptyUserProperties = JSON.stringify(window.UsersNameList);
        assert.equal(emptyUserProperties, '[]', "Should handle empty UsersNameList");
    });
});

// Test Module 7: Save Button Operations with Mock Values
QUnit.module("Save Button Operations", hooks => {
    let server;

    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <input type="text" id="userGroupNameId" name="GroupName">
            <span id="userGroupNameErr"></span>
            <textarea id="userGroupDescriptionId" name="GroupDescription"></textarea>
            <span id="userGroupDescriptionErr"></span>
            <select id="userGroupUserId" name="UserIds" multiple>
                <option value="1" data-id="1" data-name="user1">user1</option>
                <option value="2" data-id="2" data-name="user2">user2</option>
                <option value="3" data-id="3" data-name="user3">user3</option>
            </select>
            <span id="userGroupUserErr"></span>
            <button id="btnUserGroupSave">Save</button>
            <form id="CreateForm"></form>
            <input type="hidden" id="userGroupId" value="">
            <input type="hidden" id="userGroupProperties" value="">
        `);

        server = sinon.createFakeServer();
        server.respondImmediately = true;

        // Mock successful save response
        server.respondWith("POST", /api\/UserGroup\/CreateOrUpdate/,
            [200, { "Content-Type": "application/json" }, JSON.stringify({
                success: true,
                message: "User group saved successfully",
                data: { id: 123, groupName: "TestGroup123" }
            })]);

        window.UsersNameList = [];
        window.userGroupId = '';
        window.sanitizeContainer = sinon.spy();
    });

    hooks.afterEach(() => {
        server.restore();
    });

    QUnit.test("should handle save button click with valid mock data", assert => {
        const done = assert.async();
        let formSubmitted = false;

        // Setup valid form data with static test data
        $('#userGroupNameId').val(STATIC_TEST_DATA.validGroupName);
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.validDescription);
        $('#userGroupUserId').val(STATIC_TEST_DATA.validUsers.map(String));

        // Build UsersNameList with mock data
        window.UsersNameList = [
            { id: 1, loginname: "user1" },
            { id: 2, loginname: "user2" }
        ];

        // Mock form submission
        $('#CreateForm').on('submit', function (e) {
            e.preventDefault();
            formSubmitted = true;

            // Verify user properties are set
            const userProperties = JSON.stringify(window.UsersNameList);
            $('#userGroupProperties').val(userProperties);

            assert.equal($('#userGroupProperties').val(), userProperties, "User properties should be set");
            assert.ok(formSubmitted, "Form should be submitted");
            done();
        });

        // Mock validation functions to return true
        window.validateNames = sinon.stub().resolves(true);
        window.validateDescription = sinon.stub().resolves(true);
        window.validateDropDown = sinon.stub().returns(true);

        // Trigger save button click
        $('#btnUserGroupSave').trigger('click');
    });

    QUnit.test("should handle save button click with validation errors", assert => {
        const done = assert.async();

        // Setup invalid form data
        $('#userGroupNameId').val(''); // Empty name
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.longDescription); // Too long
        $('#userGroupUserId').val([]); // No users selected

        // Mock validation functions to return false
        window.validateNames = sinon.stub().resolves(false);
        window.validateDescription = sinon.stub().resolves(false);
        window.validateDropDown = sinon.stub().returns(false);

        let formSubmitted = false;
        $('#CreateForm').on('submit', function (e) {
            e.preventDefault();
            formSubmitted = true;
        });

        // Trigger save button click
        $('#btnUserGroupSave').trigger('click');

        setTimeout(() => {
            assert.notOk(formSubmitted, "Form should not be submitted with validation errors");
            done();
        }, 300);
    });

    QUnit.test("should handle save button with AJAX error response", assert => {
        const done = assert.async();

        // Mock error response
        server.respondWith("POST", /api\/UserGroup\/CreateOrUpdate/,
            [500, {}, "Server error"]);

        // Setup valid form data
        $('#userGroupNameId').val(STATIC_TEST_DATA.validGroupName);
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.validDescription);
        $('#userGroupUserId').val(STATIC_TEST_DATA.validUsers.map(String));

        window.UsersNameList = [{ id: 1, loginname: "user1" }];

        // Mock validation functions to return true
        window.validateNames = sinon.stub().resolves(true);
        window.validateDescription = sinon.stub().resolves(true);
        window.validateDropDown = sinon.stub().returns(true);

        // Mock form submission with AJAX call
        $('#CreateForm').on('submit', function (e) {
            e.preventDefault();

            $.ajax({
                url: "/api/UserGroup/CreateOrUpdate",
                method: "POST",
                data: $(this).serialize(),
                error: function (xhr) {
                    assert.equal(xhr.status, 500, "Should handle server error correctly");
                    done();
                }
            });
        });

        $('#btnUserGroupSave').trigger('click');
    });
});

// Test Module 8: Edit Button Operations with Mock Values
QUnit.module("Edit Button Operations", hooks => {
    let server;

    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <input type="text" id="userGroupNameId" name="GroupName">
            <span id="userGroupNameErr"></span>
            <textarea id="userGroupDescriptionId" name="GroupDescription"></textarea>
            <span id="userGroupDescriptionErr"></span>
            <select id="userGroupUserId" name="UserIds" multiple>
                <option value="1" data-id="1" data-name="user1">user1</option>
                <option value="2" data-id="2" data-name="user2">user2</option>
                <option value="3" data-id="3" data-name="user3">user3</option>
            </select>
            <span id="userGroupUserErr"></span>
            <button class="btn-edit" data-id="1" data-name="TestGroup" data-description="Test Description" data-users='[{"id":1,"loginname":"user1"},{"id":2,"loginname":"user2"}]'>Edit</button>
            <form id="CreateForm"></form>
            <input type="hidden" id="userGroupId" value="">
            <input type="hidden" id="userGroupProperties" value="">
            <div id="CreateModal" style="display: none;"></div>
        `);

        server = sinon.createFakeServer();
        server.respondImmediately = true;

        // Mock edit data response
        server.respondWith("GET", /api\/UserGroup\/GetById/,
            [200, { "Content-Type": "application/json" }, JSON.stringify({
                success: true,
                data: {
                    id: 1,
                    groupName: "TestGroup123",
                    groupDescription: "Test Description for Edit",
                    userProperties: '[{"id":1,"loginname":"user1"},{"id":2,"loginname":"user2"}]'
                }
            })]);

        window.UsersNameList = [];
        window.userGroupId = '';
        window.userGroupData = '';
    });

    hooks.afterEach(() => {
        server.restore();
    });

    QUnit.test("should handle edit button click with mock data", assert => {
        const mockEditData = {
            id: 1,
            groupName: STATIC_TEST_DATA.validGroupName,
            groupDescription: STATIC_TEST_DATA.validDescription,
            userProperties: '[{"id":1,"loginname":"user1"},{"id":2,"loginname":"user2"}]'
        };

        // Simulate userGroupEdit function
        function userGroupEdit(data) {
            // Parse user properties
            let userArray = [];
            let userProperties = JSON.parse(data.userProperties);
            for (let i = 0; i < userProperties.length; i++) {
                userArray.push(userProperties[i].id);
            }

            // Populate form
            $('#userGroupNameId').val(data.groupName);
            $('#userGroupDescriptionId').val(data.groupDescription);
            $('#userGroupId').val(data.id);
            $('#userGroupUserId').val(userArray);

            // Clear error messages
            $('#userGroupNameErr, #userGroupUserErr').text('').removeClass('field-validation-error');

            // Set global variables
            window.userGroupId = data.id;
            window.userGroupData = data;
            window.UsersNameList = userProperties;

            return true;
        }

        // Execute edit function
        const result = userGroupEdit(mockEditData);

        // Assertions
        assert.ok(result, "Edit function should execute successfully");
        assert.equal($('#userGroupNameId').val(), STATIC_TEST_DATA.validGroupName, "Should populate group name");
        assert.equal($('#userGroupDescriptionId').val(), STATIC_TEST_DATA.validDescription, "Should populate description");
        assert.equal($('#userGroupId').val(), "1", "Should set group ID");
        assert.deepEqual($('#userGroupUserId').val(), ["1", "2"], "Should select correct users");
        assert.equal(window.userGroupId, 1, "Should set global userGroupId");
        assert.equal(window.UsersNameList.length, 2, "Should populate UsersNameList");
    });

    QUnit.test("should handle edit button click event", assert => {
        let editClicked = false;

        $('.btn-edit').on('click', function () {
            editClicked = true;
            const id = $(this).data('id');
            const name = $(this).data('name');
            const description = $(this).data('description');
            const users = $(this).data('users');

            assert.equal(id, 1, "Should get correct ID from data attribute");
            assert.equal(name, "TestGroup", "Should get correct name from data attribute");
            assert.equal(description, "Test Description", "Should get correct description from data attribute");
            assert.ok(users, "Should get users data from data attribute");
        });

        $('.btn-edit').trigger('click');
        assert.ok(editClicked, "Edit button click should be handled");
    });

    QUnit.test("should handle edit with AJAX call", assert => {
        const done = assert.async();

        $.ajax({
            url: "/api/UserGroup/GetById",
            method: "GET",
            data: { id: 1 },
            success: function (result) {
                assert.ok(result.success, "AJAX call should succeed");
                assert.equal(result.data.id, 1, "Should return correct ID");
                assert.equal(result.data.groupName, "TestGroup123", "Should return correct group name");

                // Simulate populating form with response data
                $('#userGroupNameId').val(result.data.groupName);
                $('#userGroupDescriptionId').val(result.data.groupDescription);
                $('#userGroupId').val(result.data.id);

                assert.equal($('#userGroupNameId').val(), "TestGroup123", "Should populate form with AJAX data");
                done();
            }
        });
    });

    QUnit.test("should handle edit with malformed user properties", assert => {
        const mockEditDataWithBadJSON = {
            id: 1,
            groupName: "TestGroup",
            groupDescription: "Test Description",
            userProperties: '{"id":1,"loginname":"user1"' // Malformed JSON
        };

        function userGroupEditSafe(data) {
            try {
                let userProperties = JSON.parse(data.userProperties);
                return { success: true, users: userProperties };
            } catch (e) {
                return { success: false, error: "Invalid user properties JSON" };
            }
        }

        const result = userGroupEditSafe(mockEditDataWithBadJSON);
        assert.notOk(result.success, "Should handle malformed JSON gracefully");
        assert.equal(result.error, "Invalid user properties JSON", "Should return appropriate error message");
    });
});

// Test Module 9: Update Operations with Mock Values
QUnit.module("Update Operations", hooks => {
    let server;

    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <input type="text" id="userGroupNameId" name="GroupName">
            <span id="userGroupNameErr"></span>
            <textarea id="userGroupDescriptionId" name="GroupDescription"></textarea>
            <span id="userGroupDescriptionErr"></span>
            <select id="userGroupUserId" name="UserIds" multiple>
                <option value="1" data-id="1" data-name="user1">user1</option>
                <option value="2" data-id="2" data-name="user2">user2</option>
                <option value="3" data-id="3" data-name="user3">user3</option>
            </select>
            <span id="userGroupUserErr"></span>
            <button id="btnUserGroupSave">Update</button>
            <form id="CreateForm"></form>
            <input type="hidden" id="userGroupId" value="1">
            <input type="hidden" id="userGroupProperties" value="">
        `);

        server = sinon.createFakeServer();
        server.respondImmediately = true;

        // Mock successful update response
        server.respondWith("PUT", /api\/UserGroup\/Update/,
            [200, { "Content-Type": "application/json" }, JSON.stringify({
                success: true,
                message: "User group updated successfully",
                data: { id: 1, groupName: "UpdatedTestGroup" }
            })]);

        window.UsersNameList = [];
        window.userGroupId = '1'; // Set for update mode
    });

    hooks.afterEach(() => {
        server.restore();
    });

    QUnit.test("should handle update operation with mock data", assert => {
        const done = assert.async();

        // Setup form with updated data
        $('#userGroupNameId').val("UpdatedTestGroup");
        $('#userGroupDescriptionId').val("Updated description");
        $('#userGroupUserId').val(['1', '3']); // Changed user selection

        // Build updated UsersNameList
        window.UsersNameList = [
            { id: 1, loginname: "user1" },
            { id: 3, loginname: "user3" }
        ];

        // Mock form submission for update
        $('#CreateForm').on('submit', function (e) {
            e.preventDefault();

            const formData = {
                id: $('#userGroupId').val(),
                groupName: $('#userGroupNameId').val(),
                groupDescription: $('#userGroupDescriptionId').val(),
                userProperties: JSON.stringify(window.UsersNameList)
            };

            // Simulate update AJAX call
            $.ajax({
                url: "/api/UserGroup/Update",
                method: "PUT",
                data: formData,
                success: function (result) {
                    assert.ok(result.success, "Update should succeed");
                    assert.equal(result.message, "User group updated successfully", "Should return success message");
                    assert.equal(result.data.id, 1, "Should return correct ID");
                    assert.equal(formData.groupName, "UpdatedTestGroup", "Should send updated group name");
                    assert.equal(window.UsersNameList.length, 2, "Should have updated user list");
                    done();
                }
            });
        });

        // Mock validation functions to return true
        window.validateNames = sinon.stub().resolves(true);
        window.validateDescription = sinon.stub().resolves(true);
        window.validateDropDown = sinon.stub().returns(true);

        $('#btnUserGroupSave').trigger('click');
    });

    QUnit.test("should handle update with validation errors", assert => {
        // Setup invalid update data
        $('#userGroupNameId').val(''); // Empty name
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.longDescription);
        $('#userGroupUserId').val([]);

        // Mock validation functions to return false
        window.validateNames = sinon.stub().resolves(false);
        window.validateDescription = sinon.stub().resolves(false);
        window.validateDropDown = sinon.stub().returns(false);

        let updateAttempted = false;
        $('#CreateForm').on('submit', function (e) {
            e.preventDefault();
            updateAttempted = true;
        });

        $('#btnUserGroupSave').trigger('click');

        // Should not attempt update with validation errors
        setTimeout(() => {
            assert.notOk(updateAttempted, "Should not attempt update with validation errors");
        }, 100);
    });

    QUnit.test("should handle update with server error", assert => {
        const done = assert.async();

        // Mock server error response
        server.respondWith("PUT", /api\/UserGroup\/Update/,
            [500, {}, "Internal server error"]);

        // Setup valid form data
        $('#userGroupNameId').val(STATIC_TEST_DATA.validGroupName);
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.validDescription);
        $('#userGroupUserId').val(STATIC_TEST_DATA.validUsers.map(String));

        window.UsersNameList = [{ id: 1, loginname: "user1" }];

        $('#CreateForm').on('submit', function (e) {
            e.preventDefault();

            $.ajax({
                url: "/api/UserGroup/Update",
                method: "PUT",
                data: $(this).serialize(),
                error: function (xhr) {
                    assert.equal(xhr.status, 500, "Should handle server error correctly");
                    done();
                }
            });
        });

        // Mock validation functions to return true
        window.validateNames = sinon.stub().resolves(true);
        window.validateDescription = sinon.stub().resolves(true);
        window.validateDropDown = sinon.stub().returns(true);

        $('#btnUserGroupSave').trigger('click');
    });

    QUnit.test("should differentiate between create and update modes", assert => {
        // Test update mode (userGroupId is set)
        window.userGroupId = '1';
        $('#userGroupId').val('1');

        const isUpdateMode = window.userGroupId && window.userGroupId !== '';
        assert.ok(isUpdateMode, "Should detect update mode when userGroupId is set");

        // Test create mode (userGroupId is empty)
        window.userGroupId = '';
        $('#userGroupId').val('');

        const isCreateMode = !window.userGroupId || window.userGroupId === '';
        assert.ok(isCreateMode, "Should detect create mode when userGroupId is empty");
    });
});

// Test Module 10: Delete Operations with Mock Values
QUnit.module("Delete Operations", hooks => {
    let server;

    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <button class="btn-delete" data-id="1" data-name="TestGroup123">Delete</button>
            <button class="btn-delete" data-id="2" data-name="TestGroup456">Delete</button>
            <div id="DeleteModal" style="display: none;">
                <span id="deleteData"></span>
                <input type="hidden" id="textDeleteId" value="">
                <button id="btnConfirmDelete">Confirm Delete</button>
                <button id="btnCancelDelete">Cancel</button>
            </div>
            <table id="userGroupTable">
                <tbody>
                    <tr data-id="1"><td>TestGroup123</td><td><button class="btn-delete" data-id="1" data-name="TestGroup123">Delete</button></td></tr>
                    <tr data-id="2"><td>TestGroup456</td><td><button class="btn-delete" data-id="2" data-name="TestGroup456">Delete</button></td></tr>
                </tbody>
            </table>
        `);

        server = sinon.createFakeServer();
        server.respondImmediately = true;

        // Mock successful delete response
        server.respondWith("DELETE", /api\/UserGroup\/Delete/,
            [200, { "Content-Type": "application/json" }, JSON.stringify({
                success: true,
                message: "User group deleted successfully"
            })]);
    });

    hooks.afterEach(() => {
        server.restore();
    });

    QUnit.test("should handle delete button click with mock data", assert => {
        let deleteClicked = false;

        $('.btn-delete').first().on('click', function () {
            deleteClicked = true;
            const id = $(this).data('id');
            const name = $(this).data('name');

            // Simulate opening delete modal
            $('#deleteData').text(name);
            $('#textDeleteId').val(id);
            $('#DeleteModal').show();

            assert.equal(id, 1, "Should get correct ID from data attribute");
            assert.equal(name, "TestGroup123", "Should get correct name from data attribute");
        });

        $('.btn-delete').first().trigger('click');

        assert.ok(deleteClicked, "Delete button click should be handled");
        assert.equal($('#deleteData').text(), "TestGroup123", "Should display correct name in delete modal");
        assert.equal($('#textDeleteId').val(), "1", "Should store correct ID for deletion");
        assert.equal($('#DeleteModal').css('display'), 'block', "Delete modal should be shown");
    });

    QUnit.test("should handle confirm delete with AJAX call", assert => {
        const done = assert.async();

        // Setup delete modal with mock data
        $('#deleteData').text(STATIC_TEST_DATA.validGroupName);
        $('#textDeleteId').val('1');

        $('#btnConfirmDelete').on('click', function () {
            const deleteId = $('#textDeleteId').val();

            $.ajax({
                url: "/api/UserGroup/Delete",
                method: "DELETE",
                data: { id: deleteId },
                success: function (result) {
                    assert.ok(result.success, "Delete should succeed");
                    assert.equal(result.message, "User group deleted successfully", "Should return success message");

                    // Simulate removing row from table
                    $(`tr[data-id="${deleteId}"]`).remove();
                    assert.equal($('tr[data-id="1"]').length, 0, "Should remove row from table");

                    // Hide modal
                    $('#DeleteModal').hide();
                    assert.equal($('#DeleteModal').css('display'), 'none', "Should hide delete modal");

                    done();
                }
            });
        });

        $('#btnConfirmDelete').trigger('click');
    });

    QUnit.test("should handle cancel delete operation", assert => {
        // Setup delete modal
        $('#deleteData').text(STATIC_TEST_DATA.validGroupName);
        $('#textDeleteId').val('1');
        $('#DeleteModal').show();

        let cancelClicked = false;
        $('#btnCancelDelete').on('click', function () {
            cancelClicked = true;

            // Clear modal data
            $('#deleteData').text('');
            $('#textDeleteId').val('');
            $('#DeleteModal').hide();
        });

        $('#btnCancelDelete').trigger('click');

        assert.ok(cancelClicked, "Cancel button should be clicked");
        assert.equal($('#deleteData').text(), '', "Should clear delete data");
        assert.equal($('#textDeleteId').val(), '', "Should clear delete ID");
        assert.equal($('#DeleteModal').css('display'), 'none', "Should hide delete modal");
    });

    QUnit.test("should handle delete with server error", assert => {
        const done = assert.async();

        // Mock server error response
        server.respondWith("DELETE", /api\/UserGroup\/Delete/,
            [500, {}, "Internal server error"]);

        $('#textDeleteId').val('1');

        $('#btnConfirmDelete').on('click', function () {
            $.ajax({
                url: "/api/UserGroup/Delete",
                method: "DELETE",
                data: { id: $('#textDeleteId').val() },
                error: function (xhr) {
                    assert.equal(xhr.status, 500, "Should handle server error correctly");
                    done();
                }
            });
        });

        $('#btnConfirmDelete').trigger('click');
    });

    QUnit.test("should handle delete with validation - prevent delete if has dependencies", assert => {
        const mockGroupWithDependencies = {
            id: 1,
            name: "TestGroup123",
            hasUsers: true,
            isParent: true
        };

        function canDelete(groupData) {
            if (groupData.hasUsers || groupData.isParent) {
                return {
                    canDelete: false,
                    reason: "Cannot delete group with active users or child groups"
                };
            }
            return { canDelete: true };
        }

        const deleteCheck = canDelete(mockGroupWithDependencies);

        assert.notOk(deleteCheck.canDelete, "Should prevent deletion of group with dependencies");
        assert.equal(deleteCheck.reason, "Cannot delete group with active users or child groups", "Should provide appropriate reason");
    });

    QUnit.test("should handle multiple delete operations", assert => {
        const deleteIds = [1, 2];
        let deletedCount = 0;

        deleteIds.forEach(id => {
            // Simulate individual delete operations
            const deleteSuccess = true; // Mock successful delete
            if (deleteSuccess) {
                $(`tr[data-id="${id}"]`).remove();
                deletedCount++;
            }
        });

        assert.equal(deletedCount, 2, "Should handle multiple delete operations");
        assert.equal($('tr[data-id="1"]').length, 0, "Should remove first row");
        assert.equal($('tr[data-id="2"]').length, 0, "Should remove second row");
        assert.equal($('#userGroupTable tbody tr').length, 0, "Should remove all selected rows");
    });
});

// Test Module 11: Complete CRUD Integration Tests with Mock Values
QUnit.module("Complete CRUD Integration Tests", hooks => {
    let server;

    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <table id="userGroupTable">
                <thead><tr><th>Sr. No.</th><th>Group Name</th><th>Group Description</th><th>Users</th><th>Actions</th></tr></thead>
                <tbody></tbody>
            </table>
            <input type="text" id="userGroupNameId" name="GroupName">
            <span id="userGroupNameErr"></span>
            <textarea id="userGroupDescriptionId" name="GroupDescription"></textarea>
            <span id="userGroupDescriptionErr"></span>
            <select id="userGroupUserId" name="UserIds" multiple>
                <option value="1" data-id="1" data-name="user1">user1</option>
                <option value="2" data-id="2" data-name="user2">user2</option>
                <option value="3" data-id="3" data-name="user3">user3</option>
            </select>
            <span id="userGroupUserErr"></span>
            <button id="btnUserGroupSave">Save</button>
            <button class="btn-edit" data-id="1">Edit</button>
            <button class="btn-delete" data-id="1" data-name="TestGroup">Delete</button>
            <form id="CreateForm"></form>
            <input type="hidden" id="userGroupId" value="">
            <input type="hidden" id="userGroupProperties" value="">
            <div id="CreateModal" style="display: none;"></div>
            <div id="DeleteModal" style="display: none;">
                <span id="deleteData"></span>
                <input type="hidden" id="textDeleteId" value="">
                <button id="btnConfirmDelete">Confirm Delete</button>
            </div>
        `);

        server = sinon.createFakeServer();
        server.respondImmediately = true;

        // Setup all CRUD operation responses
        server.respondWith("POST", /api\/UserGroup\/Create/,
            [200, { "Content-Type": "application/json" }, JSON.stringify({
                success: true,
                message: "User group created successfully",
                data: { id: 123, groupName: "TestGroup123" }
            })]);

        server.respondWith("GET", /api\/UserGroup\/GetById/,
            [200, { "Content-Type": "application/json" }, JSON.stringify({
                success: true,
                data: {
                    id: 1,
                    groupName: "TestGroup123",
                    groupDescription: "Test Description",
                    userProperties: '[{"id":1,"loginname":"user1"},{"id":2,"loginname":"user2"}]'
                }
            })]);

        server.respondWith("PUT", /api\/UserGroup\/Update/,
            [200, { "Content-Type": "application/json" }, JSON.stringify({
                success: true,
                message: "User group updated successfully"
            })]);

        server.respondWith("DELETE", /api\/UserGroup\/Delete/,
            [200, { "Content-Type": "application/json" }, JSON.stringify({
                success: true,
                message: "User group deleted successfully"
            })]);

        window.UsersNameList = [];
        window.userGroupId = '';
    });

    hooks.afterEach(() => {
        server.restore();
    });

    QUnit.test("should complete full CREATE workflow with mock data", assert => {
        const done = assert.async();

        // Step 1: Setup form with static test data
        $('#userGroupNameId').val(STATIC_TEST_DATA.validGroupName);
        $('#userGroupDescriptionId').val(STATIC_TEST_DATA.validDescription);
        $('#userGroupUserId').val(STATIC_TEST_DATA.validUsers.map(String));

        // Step 2: Build UsersNameList
        window.UsersNameList = [
            { id: 1, loginname: "user1" },
            { id: 2, loginname: "user2" }
        ];

        // Step 3: Mock form submission
        $('#CreateForm').on('submit', function (e) {
            e.preventDefault();

            const formData = {
                groupName: $('#userGroupNameId').val(),
                groupDescription: $('#userGroupDescriptionId').val(),
                userProperties: JSON.stringify(window.UsersNameList)
            };

            $.ajax({
                url: "/api/UserGroup/Create",
                method: "POST",
                data: formData,
                success: function (result) {
                    assert.ok(result.success, "CREATE: Should succeed");
                    assert.equal(result.data.id, 123, "CREATE: Should return new ID");
                    assert.equal(formData.groupName, STATIC_TEST_DATA.validGroupName, "CREATE: Should send correct data");
                    done();
                }
            });
        });

        // Mock validation functions
        window.validateNames = sinon.stub().resolves(true);
        window.validateDescription = sinon.stub().resolves(true);
        window.validateDropDown = sinon.stub().returns(true);

        $('#btnUserGroupSave').trigger('click');
    });

    QUnit.test("should complete full READ workflow with mock data", assert => {
        const done = assert.async();

        // Simulate reading/loading user group data
        $.ajax({
            url: "/api/UserGroup/GetById",
            method: "GET",
            data: { id: 1 },
            success: function (result) {
                assert.ok(result.success, "READ: Should succeed");
                assert.equal(result.data.id, 1, "READ: Should return correct ID");
                assert.equal(result.data.groupName, "TestGroup123", "READ: Should return correct name");

                // Verify user properties parsing
                const userProperties = JSON.parse(result.data.userProperties);
                assert.equal(userProperties.length, 2, "READ: Should parse user properties correctly");
                assert.equal(userProperties[0].loginname, "user1", "READ: Should have correct user data");

                done();
            }
        });
    });

    QUnit.test("should complete full UPDATE workflow with mock data", assert => {
        const done = assert.async();

        // Step 1: Load existing data (simulate edit)
        window.userGroupId = '1';
        $('#userGroupId').val('1');
        $('#userGroupNameId').val("UpdatedTestGroup");
        $('#userGroupDescriptionId').val("Updated description");
        $('#userGroupUserId').val(['1', '3']);

        // Step 2: Update UsersNameList
        window.UsersNameList = [
            { id: 1, loginname: "user1" },
            { id: 3, loginname: "user3" }
        ];

        // Step 3: Submit update
        $('#CreateForm').on('submit', function (e) {
            e.preventDefault();

            const formData = {
                id: $('#userGroupId').val(),
                groupName: $('#userGroupNameId').val(),
                groupDescription: $('#userGroupDescriptionId').val(),
                userProperties: JSON.stringify(window.UsersNameList)
            };

            $.ajax({
                url: "/api/UserGroup/Update",
                method: "PUT",
                data: formData,
                success: function (result) {
                    assert.ok(result.success, "UPDATE: Should succeed");
                    assert.equal(formData.id, "1", "UPDATE: Should send correct ID");
                    assert.equal(formData.groupName, "UpdatedTestGroup", "UPDATE: Should send updated name");
                    assert.equal(window.UsersNameList.length, 2, "UPDATE: Should have updated user list");
                    done();
                }
            });
        });

        // Mock validation functions
        window.validateNames = sinon.stub().resolves(true);
        window.validateDescription = sinon.stub().resolves(true);
        window.validateDropDown = sinon.stub().returns(true);

        $('#btnUserGroupSave').trigger('click');
    });

    QUnit.test("should complete full DELETE workflow with mock data", assert => {
        const done = assert.async();

        // Step 1: Setup delete modal
        $('#deleteData').text("TestGroup123");
        $('#textDeleteId').val('1');

        // Step 2: Confirm delete
        $('#btnConfirmDelete').on('click', function () {
            const deleteId = $('#textDeleteId').val();

            $.ajax({
                url: "/api/UserGroup/Delete",
                method: "DELETE",
                data: { id: deleteId },
                success: function (result) {
                    assert.ok(result.success, "DELETE: Should succeed");
                    assert.equal(result.message, "User group deleted successfully", "DELETE: Should return success message");

                    // Simulate UI cleanup
                    $('#deleteData').text('');
                    $('#textDeleteId').val('');
                    $('#DeleteModal').hide();

                    assert.equal($('#deleteData').text(), '', "DELETE: Should clear modal data");
                    done();
                }
            });
        });

        $('#btnConfirmDelete').trigger('click');
    });

    QUnit.test("should handle complete error scenarios for all CRUD operations", assert => {
        const done = assert.async();
        let errorCount = 0;
        const expectedErrors = 4;

        // Mock all operations to return errors
        server.restore();
        server = sinon.createFakeServer();
        server.respondImmediately = true;

        server.respondWith("POST", /api\/UserGroup\/Create/, [500, {}, "Create error"]);
        server.respondWith("GET", /api\/UserGroup\/GetById/, [404, {}, "Not found"]);
        server.respondWith("PUT", /api\/UserGroup\/Update/, [500, {}, "Update error"]);
        server.respondWith("DELETE", /api\/UserGroup\/Delete/, [500, {}, "Delete error"]);

        // Test CREATE error
        $.ajax({
            url: "/api/UserGroup/Create",
            method: "POST",
            error: function (xhr) {
                assert.equal(xhr.status, 500, "CREATE ERROR: Should handle create error");
                errorCount++;
                if (errorCount === expectedErrors) done();
            }
        });

        // Test READ error
        $.ajax({
            url: "/api/UserGroup/GetById",
            method: "GET",
            error: function (xhr) {
                assert.equal(xhr.status, 404, "READ ERROR: Should handle read error");
                errorCount++;
                if (errorCount === expectedErrors) done();
            }
        });

        // Test UPDATE error
        $.ajax({
            url: "/api/UserGroup/Update",
            method: "PUT",
            error: function (xhr) {
                assert.equal(xhr.status, 500, "UPDATE ERROR: Should handle update error");
                errorCount++;
                if (errorCount === expectedErrors) done();
            }
        });

        // Test DELETE error
        $.ajax({
            url: "/api/UserGroup/Delete",
            method: "DELETE",
            error: function (xhr) {
                assert.equal(xhr.status, 500, "DELETE ERROR: Should handle delete error");
                errorCount++;
                if (errorCount === expectedErrors) done();
            }
        });
    });
});
