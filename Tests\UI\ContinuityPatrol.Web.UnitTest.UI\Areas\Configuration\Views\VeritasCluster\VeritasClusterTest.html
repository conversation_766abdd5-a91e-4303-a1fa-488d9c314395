<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>QUnit Tests for VeritasCluster.js</title>
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.20.1.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://code.jquery.com/qunit/qunit-2.20.1.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sinon.js/15.2.0/sinon.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script src="/js/Common/common.js"></script>
    <script src="/js/Configuration/Infra Components/Veritas Cluster/VeritasCluster.js"></script>
    <script src="/js/Configuration/Infra Components/Veritas Cluster/VeritasClusterTest.js"></script>
</head>
<body>
    <h1>VeritasCluster QUnit Test Runner</h1>
    <div id="qunit"></div>
    <div id="qunit-fixture"></div>
</body>
</html>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>