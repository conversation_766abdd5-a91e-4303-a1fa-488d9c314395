using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BackUpLog.Events.Create;

public class BackUpLogCreatedEventHandler : INotificationHandler<BackUpLogCreatedEvent>
{
    private readonly ILogger<BackUpLogCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BackUpLogCreatedEventHandler(ILoggedInUserService userService, ILogger<BackUpLogCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(BackUpLogCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} BackUpData",
            Entity = "BackUpData",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"BackUpLog '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"BackUpLog '{createdEvent.Name}' created successfully.");
    }
}