using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class AdPasswordJobService : BaseClient, IAdPasswordJobService
{
    public AdPasswordJobService(IConfiguration config, IAppCache cache, ILogger<AdPasswordJobService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<AdPasswordJobListVm>> GetAdPasswordJobList()
    {
        var request = new RestRequest("api/v6/adpasswordjobs");

        return await GetFromCache<List<AdPasswordJobListVm>>(request, "GetAdPasswordJobList");
    }

    public async Task<BaseResponse> CreateAsync(CreateAdPasswordJobCommand createAdPasswordJobCommand)
    {
        var request = new RestRequest("api/v6/adpasswordjobs", Method.Post);

        request.AddJsonBody(createAdPasswordJobCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateAdPasswordJobCommand updateAdPasswordJobCommand)
    {
        var request = new RestRequest("api/v6/adpasswordjobs", Method.Put);

        request.AddJsonBody(updateAdPasswordJobCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/adpasswordjobs/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<AdPasswordJobDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/adpasswordjobs/{id}");

        return await Get<AdPasswordJobDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsAdPasswordJobNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/adpasswordjobs/name-exist?adpasswordjobName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<AdPasswordJobListVm>> GetPaginatedAdPasswordJobs(GetAdPasswordJobPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/adpasswordjobs/paginated-list");

      return await Get<PaginatedResult<AdPasswordJobListVm>>(request);
  }
   #endregion
}
