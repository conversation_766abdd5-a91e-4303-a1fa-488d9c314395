using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BulkImportOperationGroup.Events.Delete;

public class BulkImportOperationGroupDeletedEventHandler : INotificationHandler<BulkImportOperationGroupDeletedEvent>
{
    private readonly ILogger<BulkImportOperationGroupDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BulkImportOperationGroupDeletedEventHandler(ILoggedInUserService userService,
        ILogger<BulkImportOperationGroupDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(BulkImportOperationGroupDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} BulkImportOperationGroup",
            Entity = "BulkImportOperationGroup",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"BulkImportOperationGroup '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"BulkImportOperationGroup '{deletedEvent.Name}' deleted successfully.");
    }
}