﻿using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.DRReadyLog.Queries;

public class GetDrReadyLogByBusinessServiceIdQueryHandlerTests : IClassFixture<DrReadyLogFixture>
{
    private readonly DrReadyLogFixture _drReadyLogFixture;

    private readonly Mock<IDrReadyLogRepository> _drReadyLogRepositoryMock;

    private readonly GetDRReadyLogByBusinessServiceIdQueryHandler _handler;

    public GetDrReadyLogByBusinessServiceIdQueryHandlerTests(DrReadyLogFixture drReadyLogFixture)
    {
        _drReadyLogFixture = drReadyLogFixture;
    
        _drReadyLogRepositoryMock = DrReadyLogRepositoryMocks.GetDrReadyLogByBusinessServiceIdRepository(_drReadyLogFixture.DrReadyLogs);
        
        _handler = new GetDRReadyLogByBusinessServiceIdQueryHandler(_drReadyLogFixture.Mapper, _drReadyLogRepositoryMock.Object);
    }

    [Fact]
    public async Task Handle_ReturnDrReadyLog_When_ValidBusinessServiceId()
    {
        var result = await _handler.Handle(new GetDRReadyLogByBusinessServiceIdQuery { BusinessServiceId = _drReadyLogFixture.DrReadyLogs[0].BusinessServiceId }, CancellationToken.None);

        result.ShouldBeOfType<DRReadyLogByBusinessServiceIdVm>();

        result.Id.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ReferenceId);
        result.UserId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].UserId);
        result.BusinessServiceId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].BusinessServiceId);
        result.BusinessServiceName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].BusinessServiceName);
        result.BusinessFunctionId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].BusinessFunctionId);
        result.BusinessFunctionName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].BusinessFunctionName);
        result.IsProtected.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].IsProtected);
        result.AffectedInfra.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].AffectedInfra);
        result.ActiveInfra.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ActiveInfra);
        result.WorkflowId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowId);
        result.WorkflowName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowName);
        result.WorkflowStatus.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowStatus);
        result.FailedActionName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].FailedActionName);
        result.FailedActionId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].FailedActionId);
        result.ActiveBusinessFunction.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ActiveBusinessFunction);
        result.AffectedBusinessFunction.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].AffectedBusinessFunction);
        result.DRReady.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].DRReady);
        result.NotReady.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].NotReady);
        result.WorkflowAttach.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowAttach);
        result.InfraObjectId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].InfraObjectId);
        result.InfraObjectName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].InfraObjectName);
        result.ComponentName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ComponentName);
        result.Type.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].Type);
        result.ErrorMessage.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ErrorMessage);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidDrReadyLogBusinessServiceId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetDRReadyLogByBusinessServiceIdQuery { BusinessServiceId = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("Not Found");
    }

    [Fact]
    public async Task Handle_Call_GetDRReadyLogByBusinessServiceIdMethod_OnlyOnce()
    {
        await _handler.Handle(new GetDRReadyLogByBusinessServiceIdQuery { BusinessServiceId = _drReadyLogFixture.DrReadyLogs[0].BusinessServiceId }, CancellationToken.None);

        _drReadyLogRepositoryMock.Verify(x => x.GetDrReadyLogByBusinessServiceId(It.IsAny<string>()), Times.Once);
    }
}