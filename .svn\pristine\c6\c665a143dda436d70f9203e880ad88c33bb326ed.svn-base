using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.FiaCostModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.FiaCost.Queries.GetPaginatedList;

public class GetFiaCostPaginatedListQueryHandler : IRequestHandler<GetFiaCostPaginatedListQuery, PaginatedResult<FiaCostListVm>>
{
    private readonly IFiaCostRepository _fiaCostRepository;
    private readonly IMapper _mapper;
    private readonly IBusinessFunctionRepository _businessFunctionRepository;

    public GetFiaCostPaginatedListQueryHandler(IMapper mapper, IFiaCostRepository fiaCostRepository, IBusinessFunctionRepository businessFunctionRepository)
    {
        _mapper = mapper;
        _fiaCostRepository = fiaCostRepository;
        _businessFunctionRepository = businessFunctionRepository;
    }

    public async Task<PaginatedResult<FiaCostListVm>> Handle(GetFiaCostPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new BusinessFunctionFilterSpecification(request.SearchString);

       // var productFilterSpec = new FiaCostFilterSpecification(request.SearchString);
       
         var queryable = await _businessFunctionRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

         var fiaCostList = _mapper.Map<PaginatedResult<FiaCostListVm>>(queryable);

         foreach (var fiaCost in fiaCostList.Data)
         {
             var fiaCostDto = await _fiaCostRepository.GetByBusinessFunctionId(fiaCost.BusinessFunctionId);

             if (fiaCostDto is not null)
             {
                 fiaCost.Id = fiaCostDto?.ReferenceId;
                 fiaCost.TemplateId = fiaCostDto?.TemplateId;
                 fiaCost.TemplateName = fiaCostDto?.TemplateName;
                fiaCost.Properties = fiaCostDto?.Properties;
            }
            else
            {
                fiaCost.Id = "";
            }
         }


       



        //var queryable = _fiaCostRepository.GetPaginatedQuery();

        //var productFilterSpec = new FiaCostFilterSpecification(request.SearchString);

        //var fiaCostList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<FiaCostListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return fiaCostList;
    }
}
