using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Create;

public class CreateDataSyncOptionsCommandValidator : AbstractValidator<CreateDataSyncOptionsCommand>
{
    private readonly IDataSyncOptionsRepository _dataSyncRepository;

    public CreateDataSyncOptionsCommandValidator(IDataSyncOptionsRepository dataSyncRepository)
    {
        _dataSyncRepository = dataSyncRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters");

        RuleFor(p => p.ReplicationType)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\rgd]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();

        RuleFor(p => p)
            .Must(p => !string.IsNullOrWhiteSpace(p.Properties) && IsValidJsonObjcet(p.Properties))
            .WithMessage("{PropertyName} must be a valid Json string.");

        RuleFor(p => p)
            .MustAsync(DataSyncNameUnique)
            .WithMessage("A same Name already exist.");
    }

    public async Task<bool> DataSyncNameUnique(CreateDataSyncOptionsCommand p, CancellationToken cancellationToken)
    {
        return !await _dataSyncRepository.IsDataSyncNameUnique(p.Name);
    }

    private bool IsValidJsonObjcet(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                var obj = JsonConvert.DeserializeObject(properties);
                return true;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }
}