﻿function RefreshButton() {
    var customButton = document.createElement("button");
    customButton.id = "customButton";
    customButton.title = "Refresh";
    customButton.className = "dxrd-toolbar-item dxrd-toolbar-item-16";
    customButton.style = "padding-bottom: 4px;padding-top: 4px;bottom: 24px;border: none;border-radius: 50%;width: 38px;height: 37px;cursor: pointer;font-size: 21px; background-color: transparent;";

    var refreshIcon = document.createElement("i");
    refreshIcon.classList.add('refresh-icon');
    refreshIcon.style.top = "10px";
    customButton.appendChild(refreshIcon);

    var targetElement = document.querySelector(".dxrd-toolbar");
    if (targetElement) {
        targetElement.appendChild(customButton);
    }
    customButton.addEventListener("click", function () {
        $('.dxrd-report-preview-holder').hide();

        var loaderDiv = document.createElement("div");
        loaderDiv.id = "Loaders";
        loaderDiv.className = "justify-content-center  h-100 gap-2";


        var innerDiv = document.createElement("div");
        innerDiv.className = "justify-content-center d-flex h-100 gap-2";
        innerDiv.style.marginTop = "150px";

        for (var i = 0; i < 3; i++) {
            var spinnerDiv = document.createElement("div");
            spinnerDiv.className = "spinner-grow text-primary";
            spinnerDiv.setAttribute("role", "status");
            spinnerDiv.style.width = "10px";
            spinnerDiv.style.height = "10px";

            var spanElement = document.createElement("span");
            spanElement.className = "visually-hidden";
            spanElement.textContent = "Loading...";

            spinnerDiv.appendChild(spanElement);

            innerDiv.appendChild(spinnerDiv);
        }

        loaderDiv.appendChild(innerDiv);
        var targetElement = document.querySelector(".dxrd-preview-surface");
        if (targetElement) {
            targetElement.appendChild(loaderDiv);
        }
        document.querySelector('.dxrd-image-preview-first').click();

        Prebuildreports();
    });
}

function Prebuildreports() {
    if (globalreport == "DataLagStatusReport") {
        disableAllLinks();
        ReportDefaultImage.hidden = true;
        //Loading.hidden = false;
        $.ajax({
            url: "/Report/PreBuildReport/GetDatalagStatusReport",
            type: 'GET',
            success: function (response) {
                try {

                    if (response.data.length > 0 && response.success !== false) {
                        var reportName = "DataLagStatusReport";
                        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                        loadAjax(loadReportUrl, reportName, response.data);

                    } else {
                        hideReportElements();
                    }
                } catch (error) {
                    console.error(error);
                    hideReportElements();
                }
            },
            error: function (error) {
                console.error(error);
                hideReportElements();
            }
        });
    }
    else if (globalreport == "BusinessServiceSummaryReport") {
        disableAllLinks();
        ReportDefaultImage.hidden = true;
        $.ajax({
            url: "/Report/PreBuildReport/GetBusinessServiceSummaryReport",
            type: 'GET',
            success: function (response) {
                try {
                    if (response.data.length > 0 && response.success !== false) {
                        var reportName = "BusinessServiceSummaryReport";
                        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                        loadAjax(loadReportUrl, reportName, response.data);
                    } else {
                        hideReportElements();
                    }
                } catch (error) {
                    //  console.error(error);
                    hideReportElements();
                }
            },
            error: function (error) {
                // console.error(error);
                hideReportElements();
            }
        });
    }
    else if (["BusinessServiceSummaryReport", "CMDBImportSummary"].includes(globalreport)) {
        //var reportName = "BusinessServiceSummaryReport";
        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
        disableAllLinks();
        ReportDefaultImage.hidden = true;
        //Loading.hidden = false;
        loadAjax(loadReportUrl, globalreport);

    }
    else if (globalreport == "BulkImportReport") {
        var operationId = bulkImportId.value;
        //$('#btndrdrill').html('');
        ReportDefaultImage.hidden = true;
        EmptyAlert.hidden = true;
        //Loading.hidden = false;
        disableAllLinks();
        $.ajax({
            url: "/Report/PreBuildReport/Download_BulkImport_Report",
            type: 'GET',
            data: {
                operationId: operationId,
            },
            success: function (response) {
                disableAllLinks();
                if (response.success == true && response.data.length > 0) {
                    var reportName = "BulkImportReport";
                    var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                    loadAjax(loadReportUrl, reportName, response.data);
                }
                else {
                    hideReportElements();
                }
            },
            error: function (error) {
                console.error(error);
                hideReportElements();
            }
        });
    }
    else if (globalreport == "DriftReport") {
        var DriftStatusId = StatusId.value;
        var DriftInfraId = InfraobjectDriftId.value;
        var StartDate = $('#startDate').find("input[type=date]").val();
        var EndDate = $('#workflowendDate').find("input[type=date]").val();
        disableAllLinks();
        ReportDefaultImage.hidden = true;
        //Loading.hidden = false;
        $.ajax({
            url: "/Report/PreBuildReport/GetDriftReport",
            type: 'GET',
            data: {
                startDate: StartDate,
                endDate: EndDate,
                InfraId: DriftInfraId,
                DriftStatusId: DriftStatusId,
            },
            success: function (response) {
                try {
                    if (response.data.length > 0 && response.success !== false) {
                        var reportName = "DriftReport";
                        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                        loadAjax(loadReportUrl, reportName, response.data);
                    } else {
                        hideReportElements();
                    }
                } catch (error) {
                    //  console.error(error);
                    hideReportElements();
                }
            },
            error: function (error) {
                // console.error(error);
                hideReportElements();
            }
        });
    }
    else if (globalreport == "AirGapReport") {
        var airGapName = airGapList.value;
        var selectElement = document.getElementById("airGapList");
        var selectedOption = selectElement.options[selectElement.selectedIndex];
        var StartDate = $('#startDate').find("input[type=date]").val();
        var EndDate = $('#workflowendDate').find("input[type=date]").val();
        if (values == "Today" || values == "Yesterday") {
            StartDate = values == "Today" ? currentDate : tdyYesterday;
            EndDate = values == "Today" ? currentDate : tdyYesterday;
        }
        AirGapName = selectedOption.textContent;
        if (airGapName != null && airGapName != "" && validateDates()) {
            //Loading.hidden = false;
            EmptyAlert.hidden = true;
            disableAllLinks();
            $.ajax({
                url: "/Report/PreBuildReport/GetAirGapReport",
                type: 'GET',
                data: {
                    startDate: StartDate,
                    endDate: EndDate,
                    airGapId: airGapName
                },
                success: function (response) {
                    try {
                        if (response.data.length > 0 && response.success !== false) {
                            var reportName = "AirGapReport";
                            var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                            loadAjax(loadReportUrl, reportName, response.data);
                        } else {
                            hideReportElements();
                        }
                    } catch (error) {
                        //  console.error(error);
                        hideReportElements();
                    }
                },
                error: function (error) {
                    // console.error(error);
                    hideReportElements();
                }
            });
        }
    }
    else if (globalreport == "DRDrillReport" || globalreport == "RTOReport") {

        var WorkflowID = IDworkflow;
        var ExecutionMode = document.getElementById("executionModeId");
        var ExecutionModes = ExecutionMode.options[ExecutionMode.selectedIndex];
        var Modes = ExecutionModes.textContent;
        var StartDate = $('#startDate').find("input[type=date]").val();
        var EndDate = $('#workflowendDate').find("input[type=date]").val();
        var selectElement = document.getElementById("workflowid");
        var selectedOption = selectElement.options[selectElement.selectedIndex];
        var WorkflowName = selectedOption.textContent;
        if (WorkflowID != "") {
            ReportDefaultImage.hidden = true;
            //Loading.hidden = false;
            EmptyAlert.hidden = true;
            //$('#btndrdrill').html('');
            disableAllLinks();
            if (globalreport == "RTOReport") {
                $.ajax({
                    url: "/Report/PreBuildReport/GetRtoReport",
                    type: 'GET',
                    data: {
                        workflowId: WorkflowID,
                        workflowName: WorkflowName,
                        type: "PDF",
                        startDate: StartDate,
                        endDate: EndDate
                    },
                    success: function (response) {
                        if (response.data != 0 && response.data != null && response.success != false) {
                            var reportName = "RTOReport";
                            var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                            loadAjax(loadReportUrl, reportName, response.data);

                        }
                        else if (response.success == false) { NotificationMsg(response.message); }
                        else {
                            hideReportElements();
                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                    }
                });

            }
            else {

                $.ajax({
                    url: "/Report/PreBuildReport/GetAllWorkflow",
                    type: 'GET',
                    data: {
                        workflowId: WorkflowID,
                        type: "PDF",
                        runMode: Modes,
                        isCustom: isCustom
                    },
                    success: function (response) {
                        disableAllLinks();
                        if (response != 0 && response != null && response.success != false) {
                            var reportName = "DRDrillReport";
                            var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                            loadAjax(loadReportUrl, reportName, response.data);
                        }
                        else {
                            hideReportElements();
                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                    }
                });
            }
        }
        else {
            enableAllLinks();
            EmptyAlert.hidden = true;
            Loading.hidden = true;
        }
    }
    else if (globalreport == "ResiliencyReadinessSchedulerLogReport" || globalreport == "CyberResiliencyScheduleLogReport") {
        var StartDate = $('#startDate').find("input[type=date]").val();
        var EndDate = $('#workflowendDate').find("input[type=date]").val();
        if (StartDate && EndDate && StartDate <= EndDate && currentDate >= EndDate && currentDate >= StartDate) {
            ReportDefaultImage.hidden = true;
            //Loading.hidden = false;
            EmptyAlert.hidden = true;
            //$('#btndrdrill').html('');
            disableAllLinks();
            if (globalreport == "ResiliencyReadinessSchedulerLogReport") {
                //var WorkflowName = selectedOption.textContent;
                $.ajax({
                    url: "/Report/PreBuildReport/GetResiliencyReadinessSchedulerLogReportList",
                    type: 'GET',
                    data: {
                        startDate: StartDate,
                        endDate: EndDate,
                        type: "PDF"
                    },
                    success: function (response) {
                        if (response.data != 0 && response.data != null && response.success != false) {
                            var reportName = "ResiliencyReadinessSchedulerLogReport";
                            var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                            loadAjax(loadReportUrl, reportName, response.data);
                        }
                        else if (response.success == false) { NotificationMsg(response.message); }
                        else {
                            hideReportElements();
                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                    }
                });
            }
            if (globalreport == "CyberResiliencyScheduleLogReport") {
                //var WorkflowName = selectedOption.textContent;
                $.ajax({
                    url: "/Report/PreBuildReport/GetCyberResiliencyScheduleLogReportList",
                    type: 'GET',
                    data: {
                        startDate: StartDate,
                        endDate: EndDate,
                        type: "PDF"
                    },
                    success: function (response) {
                        if (response.data != 0 && response.data != null && response.success != false) {
                            var reportName = "CyberResiliencyScheduleLogReport";
                            var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                            loadAjax(loadReportUrl, reportName, response.data);
                        }
                        else if (response.success == false) { NotificationMsg(response.message); }
                        else {
                            hideReportElements();
                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                    }
                });
            }
        }
    }
    else if (globalreport == "RPOSLAReport" || globalreport == "InfraObjectConfigurationReport") {
        var InfraId = IDInfra;
        if (InfraId != "") {
            var InfraType = $('select[id="Infraobject"] :selected').attr('data-type');
            var selectElement = document.getElementById("Infraobject");
            var selectedOption = selectElement.options[selectElement.selectedIndex];
            infraObjectName = selectedOption.textContent;
            EmptyAlert.hidden = true;
            if (globalreport == "RPOSLAReport") {
                rpoSLAStartDate = null;
                rpoSLAEndDate = null;
                var validation = validateDates();
                if (validation) {
                    if (InfraType != '') {
                        var rpoOption = $('#dateOption').val();
                        var rpoStartDate = $('#rpostartDate').find("input[type=date]").val();
                        rpoStartDate = new Date(rpoStartDate);
                        if (rpoOption == "Daily") {
                            rpoSLAEndDate = rpoStartDate.toLocaleDateString('en-CA');
                            rpoSLAStartDate = rpoStartDate.toLocaleDateString('en-CA');
                        }
                        else if (rpoOption == "Weekly") {
                            var rpoStartDate = $('#rpostartDate').find("input[type=week]").val();
                            const selectedWeek = rpoStartDate;
                            const [year, week] = selectedWeek.split('-W');
                            const selectedDate = new Date(year, 0, 1 + (week - 1) * 7 - (new Date(year, 0, 1).getDay() || 7));
                            const startDate = new Date(selectedDate);
                            startDate.setDate(startDate.getDate() + 1);//add one date for get correct selected date 
                            var endDate = new Date(startDate);
                            endDate.setDate(startDate.getDate() + 6);

                            var todayDate = new Date();
                            if (endDate > todayDate) {
                                endDate = new Date(todayDate);
                            }
                            const startDateFormatted = startDate.toLocaleDateString('en-GB'); // Format dates to dd-mm-yyyy format
                            const endDateFormatted = endDate.toLocaleDateString('en-GB');

                            rpoSLAEndDate = endDateFormatted;
                            rpoSLAStartDate = startDateFormatted;
                        }
                        else if (rpoOption == "Monthly") {
                            var selectedMonth = $('#rpoclndrStart').val();
                            var startDate = new Date(selectedMonth + '-01');
                            var today = new Date();
                            var endDate;

                            if (startDate.getFullYear() === today.getFullYear() && startDate.getMonth() === today.getMonth()) {
                                endDate = today;
                            } else {
                                endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
                            }
                            endDate.setHours(23, 59, 59, 999);
                            rpoSLAEndDate = endDate.toLocaleDateString('en-CA');
                            rpoSLAStartDate = startDate.toLocaleDateString('en-CA');
                        }
                        var StartDate = $('#startDate').find("input[type=date]").val();
                        var EndDate = $('#endDate').find("input[type=date]").val();
                        //if (!StartDate && !EndDate) { StartDate = defaultStartDate; EndDate = currentDate; }
                        if (validation && rpoSLAStartDate != null && rpoSLAEndDate != null) {
                            disableAllLinks();
                            ReportDefaultImage.hidden = true;
                            // Loading.hidden = false;
                            $.ajax({
                                url: "/Report/PreBuildReport/GetRpoSLAReport",
                                type: 'GET',
                                data: {
                                    infraId: InfraId,
                                    startDate: rpoSLAStartDate,
                                    endDate: rpoSLAEndDate,
                                    type: InfraType,
                                    dateOption: rpoOption,
                                    infraName: infraObjectName
                                },
                                success: function (response) {
                                    if (response.success == true) {
                                        var reportName = "RPOSLAReport";
                                        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                                        loadAjax(loadReportUrl, reportName, response.data);
                                    }
                                    else {
                                        hideReportElements();
                                    }
                                },
                                error: function (error) {
                                    console.error(error);
                                    hideReportElements();
                                }
                            });
                        }
                    }
                    else {
                        ReportDefaultImage.hidden = true;
                        hideReportElements();
                    }
                }
            }
            else {
                disableAllLinks();
                ReportDefaultImage.hidden = true;
                //Loading.hidden = false;
                $.ajax({
                    url: "/Report/PreBuildReport/GetInfraObjectList",
                    type: 'GET',
                    data: {
                        infraId: InfraId,
                        infraObjectType: InfraType
                    },
                    success: function (response) {
                        var reportName = "InfraObjectConfigurationReport";
                        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                        loadAjax(loadReportUrl, reportName, response.data);

                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                    }
                });
            }
        }
        else {
            enableAllLinks();
            EmptyAlert.hidden = true;
            Loading.hidden = true;
        }
    }
    else if (globalreport == "RPOSLADeviationReport" || globalreport == "DRReadinessLog" || globalreport == "InfraObjectSummaryReport" || globalreport == "DRReadyReport") {
        //  $('#btndrdrill').html('');
        var Businessserviceid = IDBusinessservice;
        var DrBusinessserviceid = IDDrBusinesservice;
        if (Businessserviceid != "" || DrBusinessserviceid != "") {
            var StartDate = $('#startDate').find("input[type=date]").val();
            var EndDate = $('#endDate').find("input[type=date]").val();
            if (!StartDate && !EndDate) { StartDate = defaultStartDate; EndDate = currentDate; }
            if (globalreport == "RPOSLADeviationReport") {
                var rpoOption = $('#dateOption').val();
                var InfraId = $('#InfraobjectDeviation').val();
                if (InfraId != "" && InfraId != null) {
                    rpoSLAStartDate = null;
                    rpoSLAEndDate = null;
                    var validation = validateDates();
                    if (validation) {
                        if (rpoOption == "Daily") {
                            var rpoStartDate = $('#rpostartDate').find("input[type=date]").val();
                            rpoStartDate = new Date(rpoStartDate);
                            rpoSLAEndDate = rpoStartDate.toLocaleDateString('en-CA');
                            rpoSLAStartDate = rpoStartDate.toLocaleDateString('en-CA');
                        }
                        else if (rpoOption == "Weekly") {
                            var rpoStartDate = $('#rpostartDate').find("input[type=week]").val();
                            const selectedWeek = rpoStartDate;
                            const [year, week] = selectedWeek.split('-W');
                            const selectedDate = new Date(year, 0, 1 + (week - 1) * 7 - (new Date(year, 0, 1).getDay() || 7));
                            const startDate = new Date(selectedDate);
                            startDate.setDate(startDate.getDate() + 1);
                            var endDate = new Date(startDate);
                            endDate.setDate(startDate.getDate() + 6);
                            // Format dates to dd-mm-yyyy format
                            var todayDate = new Date();
                            if (endDate > todayDate) {
                                endDate = new Date(todayDate);
                            }
                            const startDateFormatted = startDate.toLocaleDateString('en-GB');
                            const endDateFormatted = endDate.toLocaleDateString('en-GB');

                            rpoSLAEndDate = endDateFormatted;
                            rpoSLAStartDate = startDateFormatted;
                        }
                        else if (rpoOption == "Monthly") {
                            var selectedMonth = $('#rpoclndrStart').val();
                            var startDate = new Date(selectedMonth + '-01');
                            var today = new Date();
                            var endDate;

                            if (startDate.getFullYear() === today.getFullYear() && startDate.getMonth() === today.getMonth()) {
                                endDate = today;
                            } else {
                                endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
                            }
                            endDate.setHours(23, 59, 59, 999);
                            rpoSLAEndDate = endDate.toLocaleDateString('en-CA');
                            //rpoStartDate.setMonth(rpoStartDate.getMonth() - 1);
                            rpoSLAStartDate = startDate.toLocaleDateString('en-CA');
                        }
                        if (rpoSLAStartDate && rpoSLAEndDate && rpoSLAEndDate >= rpoSLAStartDate) {
                            ReportDefaultImage.hidden = true;
                            EmptyAlert.hidden = true;
                            //Loading.hidden = false;
                            disableAllLinks();
                            $.ajax({
                                url: "/Report/PreBuildReport/GetRpoSlaDeviationReport",
                                type: 'GET',
                                data: {
                                    businessServiceId: Businessserviceid,
                                    infraObjectId: InfraId,
                                    startDate: rpoSLAStartDate,
                                    endDate: rpoSLAEndDate
                                },
                                success: function (response) {
                                    if (response.data != 0 && response.success != false) {
                                        var reportName = "RPOSLADeviationReport";
                                        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                                        loadAjax(loadReportUrl, reportName, response.data);
                                    }
                                    else {
                                        hideReportElements();
                                    }
                                },
                                error: function (error) {
                                    console.error(error);
                                    hideReportElements();
                                }
                            });
                        };
                    }
                }
            }
            else if (globalreport == "DRReadinessLog") {
                var StartDate = $('#startDate').find("input[type=date]").val();
                var EndDate = $('#workflowendDate').find("input[type=date]").val();
                if (!StartDate && !EndDate) { StartDate = defaultStartDate; EndDate = currentDate; }
                if (StartDate && EndDate && EndDate >= StartDate) {
                    disableAllLinks();
                    ReportDefaultImage.hidden = true;
                    EmptyAlert.hidden = true;
                    //Loading.hidden = false;
                    $.ajax({
                        url: "/Report/PreBuildReport/GetDrReadyExecution",
                        type: 'GET',
                        data: {
                            startDate: StartDate,
                            endDate: EndDate,
                            businessServiceId: Businessserviceid
                        },
                        success: function (response) {
                            if (response.data != 0 && response.success != false) {
                                var reportName = "DRReadinessLog";
                                var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                                loadAjax(loadReportUrl, reportName, response.data);

                            }
                            else {
                                hideReportElements();
                            }
                        },
                        error: function (error) {
                            console.error(error);
                            hideReportElements();
                        }
                    });
                }
            }
            else if (globalreport == "InfraObjectSummaryReport") {
                ReportDefaultImage.hidden = true;
                EmptyAlert.hidden = true;
                //Loading.hidden = false;
                disableAllLinks();
                $.ajax({
                    url: "/Report/PreBuildReport/GetInfraobjectSummary",
                    type: 'GET',
                    data: {
                        businessServiceId: Businessserviceid,
                        type: "PDF"
                    },
                    success: function (response) {
                        if (response.data.length > 0 && response.success != false) {
                            var reportName = "InfraObjectSummaryReport";
                            var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                            loadAjax(loadReportUrl, reportName, response.data);

                        }
                        else {
                            hideReportElements();
                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                    }
                });
            }
            else {
                ReportDefaultImage.hidden = true;
                EmptyAlert.hidden = true;
                //Loading.hidden = false;
                disableAllLinks();
                $.ajax({
                    url: "/Report/PreBuildReport/GetDRReadyStatus",
                    type: 'GET',
                    data: {
                        businessServiceId: DrBusinessserviceid,
                    },
                    success: function (response) {
                        disableAllLinks();
                        if (response.success == true && response.data.length > 0) {
                            var reportName = "DRReadyReport";
                            var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                            loadAjax(loadReportUrl, reportName, response.data);
                        }
                        else {
                            hideReportElements();
                        }
                    },
                    error: function (error) {
                        console.error(error);
                        hideReportElements();
                    }
                });
            }
        }
        else { enableAllLinks(); EmptyAlert.hidden = true; Loading.hidden = true; }
    }
    else if (globalreport == "LicenseUtilizationReport") {
        disableAllLinks();
        var selectedValuesBS = Array.from($('#BusinessServiceIDLicense').val());
        var Businessserviceid = selectedValuesBS.join(',');
        var selectedValuesLpo = $('#ddlLicenceID').val();
        var Licensepoid = selectedValuesLpo.join(',');
        var ddlDerivedID = $('#ddlDerivedID').val();
        var Childpoid = ddlDerivedID.join(',');
        if (Licensepoid != "" || Businessserviceid != "") {
            ReportDefaultImage.hidden = true;
            EmptyAlert.hidden = true;
            //Loading.hidden = false;
            $.ajax({
                url: "/Report/PreBuildReport/LicenseUtilizationReport",
                type: 'POST',
                traditional: true,
                data: {
                    Businessserviceid: Businessserviceid,
                    Licensepoid: Licensepoid,
                    Derivedpoid: Childpoid,
                    Type: "pdf"

                },
                dataType: 'json',
                success: function (response) {
                    if (response.countValue != 0 && response.success != false) {
                        var reportName = "LicenseUtilizationReport";
                        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                        loadAjax(loadReportUrl, reportName, response.data);

                    }
                    else {
                        hideReportElements();
                    }
                },
                error: function (error) {
                    console.error(error);
                    hideReportElements();
                }
            });
        }
        else {
            enableAllLinks();
            EmptyAlert.hidden = true;
            Loading.hidden = true;
        }
    }
    else if (globalreport == "UserActivityReport") {

        // $('#btndrdrill').html('');
        currentAjaxRequest && currentAjaxRequest.abort();
        var UserId = $('#AllUsers').val();
        var StartDate = $('#startDate').find("input[type=date]").val();
        var EndDate = $('#endDate').find("input[type=date]").val();
        if (!StartDate && !EndDate) { StartDate = defaultStartDate; EndDate = currentDate; }
        if (StartDate && EndDate && UserId && EndDate >= StartDate) {
            disableAllLinks();
            ReportDefaultImage.hidden = true;
            EmptyAlert.hidden = true;
            //Loading.hidden = false;
            currentAjaxRequest=$.ajax({
                url: "/Report/PreBuildReport/GetUserActivityDetails",
                type: 'GET',
                data: {
                    userId: UserId,
                    startDate: StartDate,
                    endDate: EndDate
                },
                success: function (response) {
                    currentAjaxRequest = null;
                    if (response.data != 0 && response != null && response.success != false) {
                        var reportName = "UserActivityReport";
                        var loadReportUrl = '/Report/PreBuildReport/LoadReport';
                        loadAjax(loadReportUrl, reportName, response.data);
                    }
                    else {
                        hideReportElements();
                    }
                },
                error: function (error) {
                    currentAjaxRequest = null;
                   //console.error(error);
                   //hideReportElements();
                }
            });
        }
    }
    else if (globalreport == "SnapReport") {
        $('#SnapsDropDownId').trigger('change');
    }
}