﻿using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Events.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Events.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class ApprovalMatrixFixture : IDisposable
{
    public IMapper Mapper { get; }
    public List<ApprovalMatrix> ApprovalMatrices { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateApprovalMatrixCommand CreateApprovalMatrixCommand { get; set; }
    public UpdateApprovalMatrixCommand UpdateApprovalMatrixCommand { get; set; }
    public ApprovalMatrixCreatedEvent ApprovalMatrixCreatedEvent { get; set; }
    public ApprovalMatrixDeletedEvent ApprovalMatrixDeletedEvent { get; set; }
    public ApprovalMatrixUpdatedEvent ApprovalMatrixUpdatedEvent { get; set; }

    public ApprovalMatrixFixture()
    {
        ApprovalMatrices = AutoApprovalMatrixFixture.Create<List<ApprovalMatrix>>();
        UserActivities = AutoApprovalMatrixFixture.Create<List<UserActivity>>();
        CreateApprovalMatrixCommand = AutoApprovalMatrixFixture.Create<CreateApprovalMatrixCommand>();
        UpdateApprovalMatrixCommand = AutoApprovalMatrixFixture.Create<UpdateApprovalMatrixCommand>();
        ApprovalMatrixCreatedEvent = AutoApprovalMatrixFixture.Create<ApprovalMatrixCreatedEvent>();
        ApprovalMatrixDeletedEvent = AutoApprovalMatrixFixture.Create<ApprovalMatrixDeletedEvent>();
        ApprovalMatrixUpdatedEvent = AutoApprovalMatrixFixture.Create<ApprovalMatrixUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ApprovalMatrixProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoApprovalMatrixFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateApprovalMatrixCommand>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateApprovalMatrixCommand>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserActivity>(p => p.LoginName, 10));
            fixture.Customize<UpdateApprovalMatrixCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<ApprovalMatrix>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ApprovalMatrixCreatedEvent>(p => p.UserName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ApprovalMatrixDeletedEvent>(p => p.UserName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ApprovalMatrixUpdatedEvent>(p => p.UserName, 10));

            return fixture;
        }
    }
    public void Dispose()
    {

    }

}