using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class GlobalVariableRepositoryTests : IClassFixture<GlobalVariableFixture>, IDisposable
{
    private readonly GlobalVariableFixture _globalVariableFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly GlobalVariableRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public GlobalVariableRepositoryTests(GlobalVariableFixture globalVariableFixture)
    {
        _globalVariableFixture = globalVariableFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new GlobalVariableRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.GlobalVariables.RemoveRange(_dbContext.GlobalVariables);
        await _dbContext.SaveChangesAsync();
    }

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithoutId()
    {
        // Arrange
        await ClearDatabase();
        var variableName = "TestVariableName";
        var globalVariable = new GlobalVariable
        {
            ReferenceId = Guid.NewGuid().ToString(),
            VariableName = variableName,
            VariableValue = "TestValue",
            Type = "String",
            IsActive = true
        };

        await _dbContext.GlobalVariables.AddAsync(globalVariable);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(variableName, null);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameDoesNotExist_WithoutId()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "NonExistentVariable";

        // Act
        var result = await _repository.IsNameExist(nonExistentName, null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithDifferentId()
    {
        // Arrange
        await ClearDatabase();
        var variableName = "TestVariableName";
        var existingId = Guid.NewGuid().ToString();
        var differentId = Guid.NewGuid().ToString();

        var globalVariable = new GlobalVariable
        {
            ReferenceId = existingId,
            VariableName = variableName,
            VariableValue = "TestValue",
            Type = "String",
            IsActive = true
        };

        await _dbContext.GlobalVariables.AddAsync(globalVariable);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(variableName, differentId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameExists_WithSameId()
    {
        // Arrange
        await ClearDatabase();
        var variableName = "TestVariableName";
        var sameId = Guid.NewGuid().ToString();

        var globalVariable = new GlobalVariable
        {
            ReferenceId = sameId,
            VariableName = variableName,
            VariableValue = "TestValue",
            Type = "String",
            IsActive = true
        };

        await _dbContext.GlobalVariables.AddAsync(globalVariable);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(variableName, sameId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenInvalidGuid()
    {
        // Arrange
        await ClearDatabase();
        var variableName = "TestVariableName";
        var invalidGuid = "invalid-guid";

        var globalVariable = new GlobalVariable
        {
            ReferenceId = Guid.NewGuid().ToString(),
            VariableName = variableName,
            VariableValue = "TestValue",
            Type = "String",
            IsActive = true
        };

        await _dbContext.GlobalVariables.AddAsync(globalVariable);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(variableName, invalidGuid);

        // Assert
        Assert.True(result); // Should behave like no ID provided
    }

    #endregion

    #region GetByVariableName Tests

  

    [Fact]
    public async Task GetByVariableName_ReturnsEmpty_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "NonExistentVariable";

        // Act
        var result = await _repository.GetByVariableName(nonExistentName);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByVariableName_ReturnsEmpty_WhenNameExistsButIsInactive()
    {
        // Arrange
        await ClearDatabase();
        var variableName = "InactiveVariable";
        var globalVariable = new GlobalVariable
        {
            ReferenceId = Guid.NewGuid().ToString(),
            VariableName = variableName,
            VariableValue = "TestValue",
            Type = "String",
            IsActive = false
        };

        await _dbContext.GlobalVariables.AddAsync(globalVariable);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByVariableName(variableName);

        // Assert
        Assert.Empty(result);
    }

   

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddGlobalVariable_WhenValidGlobalVariable()
    {
        // Arrange
        await ClearDatabase();
        var globalVariable = new GlobalVariable
        {
            ReferenceId = Guid.NewGuid().ToString(),
            VariableName = "TestVariable",
            VariableValue = "TestValue",
            Type = "String",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(globalVariable);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(globalVariable.VariableName, result.VariableName);
        Assert.Equal(globalVariable.VariableValue, result.VariableValue);
        Assert.Equal(globalVariable.Type, result.Type);
        Assert.Single(_dbContext.GlobalVariables);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenGlobalVariableIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsGlobalVariable_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var globalVariable = new GlobalVariable
        {
            ReferenceId = Guid.NewGuid().ToString(),
            VariableName = "TestVariable",
            VariableValue = "TestValue",
            Type = "String",
            IsActive = true
        };

        await _dbContext.GlobalVariables.AddAsync(globalVariable);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(globalVariable.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(globalVariable.Id, result.Id);
        Assert.Equal(globalVariable.VariableName, result.VariableName);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsGlobalVariable_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var referenceId = Guid.NewGuid().ToString();
        var globalVariable = new GlobalVariable
        {
            ReferenceId = referenceId,
            VariableName = "TestVariable",
            VariableValue = "TestValue",
            Type = "String",
            IsActive = true
        };

        await _dbContext.GlobalVariables.AddAsync(globalVariable);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal(globalVariable.VariableName, result.VariableName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateGlobalVariable_WhenValidGlobalVariable()
    {
        // Arrange
        await ClearDatabase();
        var globalVariable = new GlobalVariable
        {
            ReferenceId = Guid.NewGuid().ToString(),
            VariableName = "TestVariable",
            VariableValue = "TestValue",
            Type = "String",
            IsActive = true
        };

        _dbContext.GlobalVariables.Add(globalVariable);
        await _dbContext.SaveChangesAsync();

        globalVariable.VariableName = "UpdatedVariable";
        globalVariable.VariableValue = "UpdatedValue";
        globalVariable.Type = "Integer";

        // Act
        var result = await _repository.UpdateAsync(globalVariable);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedVariable", result.VariableName);
        Assert.Equal("UpdatedValue", result.VariableValue);
        Assert.Equal("Integer", result.Type);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenGlobalVariableIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveGlobalVariable_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var globalVariable = new GlobalVariable
        {
            ReferenceId = Guid.NewGuid().ToString(),
            VariableName = "TestVariable",
            VariableValue = "TestValue",
            Type = "String",
            IsActive = true
        };

        await _dbContext.GlobalVariables.AddAsync(globalVariable);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(globalVariable);

        // Assert
        var deletedVariable = await _dbContext.GlobalVariables.FindAsync(globalVariable.Id);
        Assert.Null(deletedVariable);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsAllGlobalVariables_WhenDataExists()
    {
        // Arrange
        await ClearDatabase();
        var globalVariables = new List<GlobalVariable>
        {
            new GlobalVariable { ReferenceId = Guid.NewGuid().ToString(), VariableName = "Variable1", VariableValue = "Value1", Type = "String", IsActive = true },
            new GlobalVariable { ReferenceId = Guid.NewGuid().ToString(), VariableName = "Variable2", VariableValue = "Value2", Type = "Integer", IsActive = true },
            new GlobalVariable { ReferenceId = Guid.NewGuid().ToString(), VariableName = "Variable3", VariableValue = "Value3", Type = "Boolean", IsActive = false }
        };

        await _dbContext.GlobalVariables.AddRangeAsync(globalVariables);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(3, result.Count); // Should return all variables regardless of IsActive
        Assert.Contains(result, x => x.VariableName == "Variable1");
        Assert.Contains(result, x => x.VariableName == "Variable2");
        Assert.Contains(result, x => x.VariableName == "Variable3");
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoData()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    #endregion
}
