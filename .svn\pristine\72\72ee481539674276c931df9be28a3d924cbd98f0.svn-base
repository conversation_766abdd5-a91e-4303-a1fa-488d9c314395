﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class InfraObjectSchedulerWorkflowDetailRepository : BaseRepository<InfraObjectSchedulerWorkflowDetail>,
    IInfraObjectSchedulerWorkflowDetailRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public InfraObjectSchedulerWorkflowDetailRepository(ApplicationDbContext dbContext,
        ILoggedInUserService loggedInUserService) : base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    //public override IQueryable<InfraObjectSchedulerWorkflowDetail> GetPaginatedQuery()
    //{
    //    var infraObjectSchedulerWorkflowDetails =
    //        base.ListAllAsync(infraObject => infraObject.InfraObjectId.Equals(_loggedInUserService.CompanyId));

    //    return _loggedInUserService.IsAllInfra
    //        ? infraObjectSchedulerWorkflowDetails.AsNoTracking().OrderByDescending(x => x.Id)
    //        : GetPaginatedInfraObjects(infraObjectSchedulerWorkflowDetails).AsNoTracking().OrderByDescending(x => x.Id);
    //}
}