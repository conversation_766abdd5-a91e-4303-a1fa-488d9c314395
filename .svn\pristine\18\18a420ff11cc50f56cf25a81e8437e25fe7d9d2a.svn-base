﻿var createPermission = $("#ConfigurationCreatelev").data("create-permission").toLowerCase();
var deletePermission = $("#ConfigurationDeletelev").data("delete-permission").toLowerCase();
let _esclevData;
const ESLevelExistUrl = "/Manage/EscalationMatrix/CheckEscalationLevelNameExist";

const timelineListParent = document.getElementById('Escalation_Timeline_ul');
let additionalListItem = `<li class="li"></li>`;
//let additionalListItem = `<li class="li"><span> <button type="button" class="rounded-circle btn btn-primary btn-sm">+</button></span></li>`;

//function Escmatlevdis(id) {
//    debugger;
//    console.log("Document ready!");

//    var selectedValues = [];
//    debugger;
//    $("#tblEsclevel").dataTable().fnDestroy();
//    var dataTable = $('#tblEsclevel').DataTable({
//        // ... Existing DataTable configuration ...
//        language: {
//            paginate: {
//                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
//                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
//            }
//        },
//        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
//        scrollY: true,
//        deferRender: true,
//        scroller: true,
//        "processing": true,
//        "serverSide": true,
//        "filter": true,
//        "ajax": {
//            // ... Existing ajax configuration ...
//            "type": "GET",
//            "url": "/Manage/EscalationMatrix/GetPaginationEscMatLevel",
//            "dataType": "json",
//           /* "data": function (d) {
//                 console.log("Ajax data function:", d);
//                d.PageNumber = Math.ceil(d.start / d.length) + 1;
//                d.pageSize = d.length;
//                d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
//                selectedValues.length = 0;
//            },*/

//            data: {
//                'query': id,
//            },

//            "dataSrc": function (json) {

//                console.log("Ajax dataSrc function:", json);
//                json.recordsTotal = json.totalPages;
//                json.recordsFiltered = json.totalCount;
//                debugger;
//                if (json.data.length === 0) {
//                    $(".pagination-column").addClass("disabled")
//                    }
//                else {
//                    $(".pagination-column").removeClass("disabled")
//                    //$(".TableThead").removeClass("d-none")
//                 }

//                if (json.data.length !== 0) {
//                    _esclevData = json.data;
//                    GenerateEscalationMatrixTree(_esclevData);
//                }
//                else
//                {
//                    let _opesclev = `<li class="li"><div class="Escalation_Timeline_Card card"><div class="d-flex align-items-center"><span class="Timeline_Card_Level badge bg-primary"> Level 0 </span><div class="d-grid ms-3"><span class="mb-1 text-truncate" title="">Escalation Level is not Configured. </span></div></div></div></li>`;
//                    timelineListParent.innerHTML = additionalListItem + _opesclev;
//                }
//                return json.data;
//            }
//        },
//        "columnDefs": [
//            {
//                "targets": [1, 2, 3, 4],
//                "className": "truncate"
//            }
//        ],
//        "columns": [
//            {

//                "data": null,
//                "name": "Sr. No.",
//                "autoWidth": true,
//                "render": function (data, type, row, meta) {
//                    if (type === 'display') {
//                        /*return meta.row + 1;*/

//                        var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
//                        return (page * meta.settings._iDisplayLength) + meta.row + 1;
//                    }
//                    return data;
//                }
//            },

//            {
//                "data": "escLevName", "name": "Level Name", "autoWidth": true,
//                "render": function (data, type, row) {
//                    if (type === 'display') {
//                        return '<span title="' + data + '">' + data + '</span>';
//                    }
//                    return data;
//                }
//            },

//            {
//                "data": "escalationTime", "name": "Time", "autoWidth": true,
//                "render": function (data, type, row) {
//                    if (type === 'display') {
//                        return '<span title="' + row.escalationTime + row.escalationTimeUnit + '">' + row.escalationTime + row.escalationTimeUnit + '</span>';
//                    }
//                    return data;
//                }
//            },

//            {
//                "render": function (data, type, row) {
//                    if (createPermission === 'true' && deletePermission === "true") {

//                        return `
//                        <div class="d-flex align-items-center gap-2">
//                                            <span role="button" title="Update Escalation Matrix"  class="edit-button" data-escalation='${JSON.stringify(row)}' data-bs-toggle="modal"  data-bs-target="#levelModal" >
//                                    <i class="cp-refresh"></i>
//                                </span>
//                                            <span role="button" title="Delete"  class="delete-button" data-team-id="${row.id}" data-team-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
//                                                <i class="cp-Delete"></i>
//                                            </span>




//                        </div>`;


//                    }
//                    else if (createPermission === 'true' && deletePermission === "false") {
//                        return `
//                      <div class="d-flex align-items-center gap-2">
//                                            <span role="button" title="Update Escalation Matrix"  class="edit-button" data-escalation='${JSON.stringify(row)}'data-bs-toggle="modal"  data-bs-target="#levelModal" >
//                                    <i class="cp-refresh"></i>
//                                </span>
//                                            <span role="button" title="Delete"  class="delete-button" data-team-id="${row.id}" data-team-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModallevel">
//                                                <i class="cp-Delete"></i>
//                                            </span>




//                        </div>`;
//                    }
//                    else if (createPermission === 'false' && deletePermission === "true") {
//                        return `
//                            <div class="d-flex align-items-center gap-2">
//                                            <span role="button" title="Update Escalation Matrix"  class="edit-button" data-escalation='${JSON.stringify(row)}'  data-bs-toggle="modal" data-bs-target="#levelModal">
//                                    <i class="cp-refresh"></i>
//                                </span>
//                                            <span role="button" title="Delete"  class="delete-button" data-team-id="${row.id}" data-team-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModallevel">
//                                                <i class="cp-Delete"></i>
//                                            </span>




//                        </div>`;
//                    }
//                    else {
//                        return `
//                           <div class="d-flex align-items-center gap-2">
//                                            <span role="button" title="Update Escalation Matrix"   class="edit-button" data-bs-toggle="modal" data-bs-target="#levelModal">
//                                    <i class="cp-refresh"></i>
//                                </span>
//                                            <span role="button" title="Delete"  class="delete-button" data-team-id="${row.id}" data-team-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModallevel">
//                                                <i class="cp-Delete"></i>
//                                            </span>




//                        </div>`;
//                    }
//                },
//                "orderable": false
//            }
//        ],
//        //"drawCallback": function (settings) {
//        //    const randomColor = () => {
//        //        return "#" + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0').toUpperCase();
//        //    }

//        //    const namelist = document.querySelectorAll("#companyname");
//        //    namelist.forEach((name) => {
//        //        name.style.backgroundColor = randomColor();
//        //    });
//        //}

//    });

//}

/*$(function () {

    var dataTable2 = $('#tblesclevUser').DataTable();


            $('#escserlevusr').on('keyup input', function () {
                $('input[type="checkbox"]').each(function () {
                    if ($(this).is(':checked')) {
                        var checkboxValue = $(this).val();
                        var inputValue = $('#search-inp').val();
                        selectedValues.push(checkboxValue + inputValue);
                    }
                });
                dataTable2.search($(this).val()).draw();
            });
 
});*/


function Escmatlevdis(id) {
    debugger;
    console.log("Document ready!");

    var selectedValues = [];
    debugger;
    $("#tblEsclevel").dataTable().fnDestroy();
    var dataTable = $('#tblEsclevel').DataTable({
        // ... Existing DataTable configuration ...
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        "processing": true,
        "serverSide": true,
        "filter": true,
        "ajax": {
            // ... Existing ajax configuration ...
            "type": "GET",
            "url": "/Manage/EscalationMatrix/GetPaginationEscMatLevel",
            "dataType": "json",
            /* "data": function (d) {
                  console.log("Ajax data function:", d);
                 d.PageNumber = Math.ceil(d.start / d.length) + 1;
                 d.pageSize = d.length;
                 d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                 selectedValues.length = 0; 
             },*/

            data: {
                'query': id,
            },

            "dataSrc": function (json) {

                // console.log("Ajax dataSrc function:", json);
                json.recordsTotal = json?.totalPages;
                json.recordsFiltered = json?.totalCount;
                //debugger;
                //if (json.data.length === 0) {
                //    $(".pagination-column").addClass("disabled")
                //}
                //else {
                //    $(".pagination-column").removeClass("disabled")
                //    //$(".TableThead").removeClass("d-none")
                //}

                if (json?.data?.length !== 0) {
                    _esclevData = json.data;
                    GenerateEscalationMatrixTree(_esclevData);
                }
                else {
                    let _opesclev = `<li class="li"><div class="Escalation_Timeline_Card card"><div class="d-flex align-items-center"><span class="Timeline_Card_Level badge bg-primary"> Level 0 </span><div class="d-grid ms-3"><span class="mb-1 text-truncate" title="">Escalation Level is not Configured. </span></div></div></div></li>`;
                    timelineListParent.innerHTML = additionalListItem + _opesclev;
                }
                return json?.data;
            }
        },

        "columns": [
            {

                "data": null,
                "name": "Sr. No.",
                "autoWidth": true,
                "render": function (data, type, row, meta) {
                    if (type === 'display') {
                        /*return meta.row + 1;*/

                        var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                        return (page * meta.settings._iDisplayLength) + meta.row + 1;
                    }
                    return data;
                }
            },

            {
                "data": "escLevName", "name": "Level Name", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return '<span title="' + data + '">' + data + '</span>';
                    }
                    return data;
                }
            },

            {
                "data": "escalationTime", "name": "Time", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return '<span title="' + row.escalationTime + row.escalationTimeUnit + '">' + row.escalationTime + row.escalationTimeUnit + '</span>';
                    }
                    return data;
                }
            },

            {
                "render": function (data, type, row) {
                    if (createPermission === 'true' && deletePermission === "true") {

                        return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Update Escalation Matrix"  class="edit-button" data-escalation='${JSON.stringify(row)}' data-bs-toggle="modal"  data-bs-target="#levelModal" >
                                    <i class="cp-refresh"></i>
                                </span>
                                            <span role="button" title="Delete"  class="delete-button" data-team-id="${row.id}" data-team-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                             

                                           
                                  
                        </div>`;


                    }
                    else if (createPermission === 'true' && deletePermission === "false") {
                        return `
                      <div class="d-flex align-items-center gap-2"> 
                                            <span role="button" title="Update Escalation Matrix"  class="edit-button" data-escalation='${JSON.stringify(row)}'data-bs-toggle="modal"  data-bs-target="#levelModal" >
                                    <i class="cp-refresh"></i>
                                </span>
                                            <span role="button" title="Delete"  class="delete-button" data-team-id="${row.id}" data-team-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModallevel">
                                                <i class="cp-Delete"></i>
                                            </span>
                                             

                                           
                                  
                        </div>`;
                    }
                    else if (createPermission === 'false' && deletePermission === "true") {
                        return `
                            <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Update Escalation Matrix"  class="edit-button" data-escalation='${JSON.stringify(row)}'  data-bs-toggle="modal" data-bs-target="#levelModal">
                                    <i class="cp-refresh"></i>
                                </span>
                                            <span role="button" title="Delete"  class="delete-button" data-team-id="${row.id}" data-team-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModallevel">
                                                <i class="cp-Delete"></i>
                                            </span>
                                             

                                           
                                  
                        </div>`;
                    }
                    else {
                        return `
                           <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Update Escalation Matrix"   class="edit-button" data-bs-toggle="modal" data-bs-target="#levelModal">
                                    <i class="cp-refresh"></i>
                                </span>
                                            <span role="button" title="Delete"  class="delete-button" data-team-id="${row.id}" data-team-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModallevel">
                                                <i class="cp-Delete"></i>
                                            </span>
                                             

                                           
                                  
                        </div>`;
                    }
                },
                "orderable": false
            }
        ],
        "drawCallback": function (settings) {
            const randomColor = () => {
                return "#" + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0').toUpperCase();
            }

            const namelist = document.querySelectorAll("#companyname");
            namelist.forEach((name) => {
                name.style.backgroundColor = randomColor();
            });
        }

    });

}


$('#tblEsclevel').on('click', '.edit-button', function () {

    var companyData = $(this).data('escalation');
    populateModalFieldslev(companyData);
    $('#btkesclevesub').text('Update');
    $('#btkesclevesub').attr('title', 'Update');
    console.log("Hello");
    $('#LevelWizradModal').modal('show');
});

//delete
$('#tblEsclevel').on('click', '.delete-button', function () {

    var escId = $(this).data('team-id');
    var escName = $(this).data('team-name');
    $('#txtDeletelevId').val(escId);
    $('#esllID').text(escName);
    $('#DeleteModallevel').modal('show');
});

//$(function  () {
//    debugger;
//    console.log("Document ready!");

//    var selectedValues = [];
//    debugger;
//    var dataTable = $('#tblEsclevel').DataTable({
//        // ... Existing DataTable configuration ...
//        language: {
//            paginate: {
//                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
//                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
//            }
//        },
//        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
//        scrollY: true,
//        deferRender: true,
//        scroller: true,
//        "processing": true,
//        "serverSide": true,
//        "filter": true,
//        "ajax": {
//            // ... Existing ajax configuration ...
//            "type": "GET",
//            "url": "/Manage/EscalationMatrix/GetPaginationEscMatLevel",
//            "dataType": "json",
//            "data": function (d) {
//                 console.log("Ajax data function:", d);
//                d.PageNumber = Math.ceil(d.start / d.length) + 1;
//                d.pageSize = d.length;
//                d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
//                selectedValues.length = 0; 
//            },

//            "dataSrc": function (json) {

//                console.log("Ajax dataSrc function:", json);
//                json.recordsTotal = json.totalPages;
//                json.recordsFiltered = json.totalCount;
//                debugger;
//                if (json.data.length === 0) {
//                    $(".pagination-column").addClass("disabled")


//                }
//                else {
//                    $(".pagination-column").removeClass("disabled")
//                    //$(".TableThead").removeClass("d-none")
//                }
//                return json.data;
//            }
//        },

//        "columns": [
//            {
//                "data": null,
//                "name": "Sr. No.",
//                "autoWidth": true,
//                "render": function (data, type, row, meta) {
//                    if (type === 'display') {
//                        /*return meta.row + 1;*/
//                        var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
//                        return (page * meta.settings._iDisplayLength) + meta.row + 1;
//                    }
//                    return data;
//                }
//            },
//            {
//                "data": "escLevName", "name": "Level Name", "autoWidth": true,
//                "render": function (data, type, row) {
//                    if (type === 'display') {
//                        return '<span title="' + data + '">' + data + '</span>';
//                    }
//                    return data;
//                }
//            },

//            {
//                "data": "escalationTime", "name": "Time", "autoWidth": true,
//                "render": function (data, type, row) {
//                    if (type === 'display') {
//                        return '<span title="' + row.escalationTime + row.escalationTimeUnit + '">' + row.escalationTime + row.escalationTimeUnit + '</span>';
//                    }
//                    return data;
//                }
//            },

//            {
//                "render": function (data, type, row) {
//                    if (createPermission === 'true' && deletePermission === "true") {

//                        return `
//                        <div class="d-flex align-items-center gap-2">
//                                            <span role="button" title="Update Escalation Matrix"  class="edit-button" data-escalation='${JSON.stringify(row)}' data-bs-toggle="modal"  data-bs-target="#levelModal" >
//                                    <i class="cp-refresh"></i>
//                                </span>
//                                            <span role="button" title="Delete"  class="delete-button" data-team-id="${row.id}" data-team-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
//                                                <i class="cp-Delete"></i>
//                                            </span>




//                        </div>`;


//                    }
//                    else if (createPermission === 'true' && deletePermission === "false") {
//                        return `
//                      <div class="d-flex align-items-center gap-2"> 
//                                            <span role="button" title="Update Escalation Matrix"  class="edit-button" data-escalation='${JSON.stringify(row)}'data-bs-toggle="modal"  data-bs-target="#levelModal" >
//                                    <i class="cp-refresh"></i>
//                                </span>
//                                            <span role="button" title="Delete"  class="delete-button" data-team-id="${row.id}" data-team-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModallevel">
//                                                <i class="cp-Delete"></i>
//                                            </span>




//                        </div>`;
//                    }
//                    else if (createPermission === 'false' && deletePermission === "true") {
//                        return `
//                            <div class="d-flex align-items-center gap-2">
//                                            <span role="button" title="Update Escalation Matrix"  class="edit-button" data-escalation='${JSON.stringify(row)}'  data-bs-toggle="modal" data-bs-target="#levelModal">
//                                    <i class="cp-refresh"></i>
//                                </span>
//                                            <span role="button" title="Delete"  class="delete-button" data-team-id="${row.id}" data-team-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModallevel">
//                                                <i class="cp-Delete"></i>
//                                            </span>




//                        </div>`;
//                    }
//                    else {
//                        return `
//                           <div class="d-flex align-items-center gap-2">
//                                            <span role="button" title="Update Escalation Matrix"   class="edit-button" data-bs-toggle="modal" data-bs-target="#levelModal">
//                                    <i class="cp-refresh"></i>
//                                </span>
//                                            <span role="button" title="Delete"  class="delete-button" data-team-id="${row.id}" data-team-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModallevel">
//                                                <i class="cp-Delete"></i>
//                                            </span>




//                        </div>`;
//                    }
//                },
//                "orderable": false
//            }
//        ],
//        order: [[0, 'asc']],
//        columnDefs: [
//            {
//                targets: 0,
//                type: 'num'
//            }
//        ],
//        "drawCallback": function (settings) {
//            const randomColor = () => {
//                return "#" + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0').toUpperCase();
//            }

//            const namelist = document.querySelectorAll("#companyname");
//            namelist.forEach((name) => {
//                name.style.backgroundColor = randomColor();
//            });
//        }

//    });

//    /* $(document).on('keyup', '#search-inp', function () {
//         console.log("Keyup event triggered!");
//         $('input[type="checkbox"]').each(function () {
//             if ($(this).is(':checked')) {
//                 var checkboxValue = $(this).val();
//                 var inputValue = $('#search-inp').val();
//                 selectedValues.push(checkboxValue + inputValue);
//             }
//         });
//         dataTable.search($(this).val()).draw();
//     });*/

//    $('#tblEsclevel').on('click', '.edit-button', function  () {

//        var companyData = $(this).data('escalation');
//        populateModalFieldslev(companyData);
//        $('#btkesclevesub').text('Update');
//        $('#btkesclevesub').attr('title', 'Update');
//        console.log("Hello");
//        $('#LevelWizradModal').modal('show');
//    });


//    $('#tblEsclevel').on('click', '.delete-button', function () {

//        var escId = $(this).data('team-id');
//        var escName = $(this).data('team-name');
//        $('#txtDeletelevId').val(escId);
//        $('#esllID').text(escName);
//        $('#DeleteModallevel').modal('show');
//    });



//});


function openModal() {
    console.log("Hi");
}


function BindTeamNames() {
    alert("");
}

function BindTeamName(id) {
    var presentdata = $('#EsxLevTeam').val();



    if (presentdata.includes(id)) {
        let selectedDropDown = document.getElementById("EsxLevTeam");
        // let _selectedUser = selectedDropDown.getElementById(id);
        // selectedDropDown.remove(_selectedUser);
        var item = $("#EsxLevTeam option[value='" + id + "']").remove();
        $('#EsxLevTeam').change();
        const errorElement = $('#teams');
        errorElement.text('select team to add')
            .addClass('field-validation-error');
    }
    else {

        var username = $("#" + id).val();
        var userId = id;
        var data = username;
        //var data = $('#exampleCheck1').val();
        var option = document.createElement("option");
        option.text = data;
        option.value = id;
        option.Id = data;
        option.setAttribute("selected", "true");
        console.log(data);
        let selectedDropDown = document.getElementById("EsxLevTeam");
        selectedDropDown.add(option);
        var Isdatapresent = $('#EsxLevTeam').val();
        if (Isdatapresent.length != 0) {
            const errorElement = $('#teams');
            errorElement.text('')
                .removeClass('field-validation-error');
        }
    }


    var _Isdata = $('#EsxLevTeam').val();
    if (_Isdata.length != 0) {
        const errorElement = $('#teams');
        errorElement.text('')
            .removeClass('field-validation-error');
    }
}
function BindUserName(id) {
    var presentdata = $('#EsxLevMem').val();



    if (presentdata.includes(id)) {
        let selectedDropDown = document.getElementById("EsxLevMem");

        var _list = $("#esclevres").val();
        if (_list.includes(id)) {

        }
        // let _selectedUser = selectedDropDown.getElementById(id);
        // selectedDropDown.remove(_selectedUser);
        var item = $("#EsxLevMem option[value='" + id + "']").remove();
        $('#EsxLevMem').change();
        const errorElement = $('#teammembers');
        errorElement.text('select members to add')
            .addClass('field-validation-error');
    }
    else {
        var username = $("#" + id).text();
        var userId = id;
        var data = username;
        //var data = $('#exampleCheck1').val();
        var option = document.createElement("option");
        option.text = data;
        option.value = id;
        option.Id = data;
        option.setAttribute("selected", "true");
        console.log(data);
        let selectedDropDown = document.getElementById("EsxLevMem");
        selectedDropDown.add(option);
        var _datapresent = $('#EsxLevMem').val();
        if (_datapresent.length != 0) {
            const errorElement = $('#teammembers');
            errorElement.text('')
                .removeClass('field-validation-error');
        }
    }
    var IsData = $('#EsxLevMem').val();

    if (IsData.length != 0) {
        const errorElement = $('#teammembers');
        errorElement.text('')
            .removeClass('field-validation-error');
    }

}


$("#nextfunction").on('click', function () {
    let Activechecked = false
    if ($('.steps ul li.current')[0].textContent.includes('Teams')) {
        let checkActiveTeam = Array.prototype.slice.call($('.activeCheckteam')).some(x => x.checked === true)
        if (checkActiveTeam) {
            $('#sumaryLevelName').text($('#escalationLevelName').val());
            $('#sumaryLevelDescription').text($('#escalationLevelDescriptin').val())
            $('#sumaryLevelTime').text($('#escalationLevelTime').val() + ' ' + $('#escalationTimeZone').val())
            $('#summaryResources').text($('#EsxLevMem').text())
            $('#summaryTeams').text($('#EsxLevTeam').text())
            form.steps('next')
        }
        else {
            const errorElement = $('#teams');
            errorElement.text('Select Team to add')
                .addClass('field-validation-error');
            return false;
        }

    }
    if ($('.steps ul li.current')[0].textContent.includes('Members')) {
        let checkActive = Array.prototype.slice.call($('.activeCheck')).some(x => x.checked === true)
        if (checkActive) {
            var levelusers = $('#EsxLevMem').val();
            $('#esclevres').val(levelusers);
            /* var levelteams = $('#EsxLevTeam').val();
             $('#esclevteam').val(levelteams);
             var levelmatid = $('#textgroupId').val();
             $('#EscmattId').val(levelmatid);*/
            form.steps('next')
        } else {

            const errorElement = $('#teammembers');
            errorElement.text('Select Members to add')
                .addClass('field-validation-error');
            return false;

        }
    } else {

        var levelname = $('#escalationLevelName').val();
        var levelDescription = $('#escalationLevelDescriptin').val();
        var leveltime = $('#escalationLevelTime').val();
        var isLevelName = false;
        var isLevelDsc = false;
        var isLeveltime = false;
        var Iserror = $('#lvlName')[0].innerText;
        var Iserror1 = $('#lvlDesc')[0].innerText;

        if (levelname == "")
            addValidation();
        else if (Iserror == "")
            isLevelName = true;

        if (levelDescription == "")
            addDescriptionValidation();
        else if (Iserror1 == "")
            isLevelDsc = true;

        if (leveltime == "")
            addTimeValidation();
        else
            isLeveltime = true;

        if (isLevelName && isLevelDsc && isLeveltime) {
            var levelteams = $('#EsxLevTeam').val();
            $('#esclevteam').val(levelteams);
            var levelmatid = $('#textgroupId').val();
            $('#EscmattId').val(levelmatid);
            form.steps('next')
        }

    }


    //if ($('#EsxLevMem').has('option').length > 0) {
    //    alert("Please select atleast one member");
    //} else {
    //    //Proceed to "form.steps('next')" function
    //}
});


function populateModalFieldslev(escData) {
    $('#textgrouplelId').val(escData.id);
    $('#escalationLevelName').val(escData.escLevName);
    $('#escalationLevelDescriptin').val(escData.escLevDescription);
    $('#escalationLevelTime').val(escData.escalationTime);
    $('#escalationTimeZone').val(escData.escalationTimeUnit);
    //$('#esclevres').val(escData.escMatLevelResourceID);
    //$('#esclevteam').val(escData.escMatLevelTeamID);
    $('#EscmattId').val(escData.escMatrixID);

    let getCheck = escData.escMatLevelResourceID.split(',')
    getCheck.forEach(x => {
        $(`.activeCheck[checkBoxId=${x}]`).prop('checked', true)
        BindUserName(x)
    })

    let getCheckteam = escData.escMatLevelTeamID.split(',')
    getCheckteam.forEach(y => {
        $(`.activeCheckteam[checkBoxId=${y}]`).prop('checked', true)
        BindTeamName(y)
    })




}

$("#btkesclevesub").click(async function () {

    $('#CreateEscleveForm').submit();
    $("#CreateEscleveForm").submit();

});



function GenerateEscalationMatrixTree(treeData) {
    /*const timelineListParent = document.getElementById('Escalation_Timeline_ul');
    let additionalListItem = `<li class="li"><span> <button type="button" class="rounded-circle btn btn-primary btn-sm">+</button></span></li>`;*/
    let j = 0;
    var BorderColors = ['border-primary', 'border-success', 'border-warning', 'border-danger'];
    var circle = ['bg-primary', 'bg-success', 'bg-warning', 'bg-danger'];
    let output = " ";

    //console.log(treeData);

    for (let i = 0; i < treeData.length; i++) {
        //console.log(treeData[i].escLevName);
        if (treeData[i] != "undefined") {
            output += `<li class="li"><div class="Escalation_Timeline_Card card ${BorderColors[j]}"><div class="d-flex align-items-center"><span class="Timeline_Card_Level ${circle[j]} badge bg-primary"> Level ${i + 1} </span><div class="d-grid ms-3"><span class="mb-1 text-truncate" title="Management Approval"> ${treeData[i].escLevName} </span></div></div></div></li>`;
            j++;
            if (j > 3) { j = 0; }
        }
    }

    timelineListParent.innerHTML = output;
}




async function addValidation() {

    const value = $('#escalationLevelName').val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);


    await validateName(value, ESLevelExistUrl);

};

async function addDescriptionValidation() {

    const errorElement = $('#lvlDesc');

    const value = $('#escalationLevelDescriptin').val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    if (!value) {
        errorElement.text('Enter Level Description')
            .addClass('field-validation-error');
        return false;
    }

    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
    ];
    return await CommonValidation(errorElement, validationResults);

};

async function addTimeValidation() {

    const errorElement = $('#lvltime');

    const value = $('#escalationLevelTime').val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    if (!value) {
        errorElement.text('Enter Level Time')
            .addClass('field-validation-error');
        return false;
    }

    const validationResults = [
        //await OnlyNumericsValidate(value),             
    ];
    return await CommonValidation(errorElement, validationResults);

};



async function validateName(value, _url) {
    const errorElement = $('#lvlName');

    if (!value) {
        errorElement.text('Enter Level name')
            .addClass('field-validation-error');
        return false;
    }
    var url = _url;
    var data = {};
    data.LevelName = value;
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),

        await IsTemplateNameExist(url, data, OnError)
    ];
    return await CommonValidation(errorElement, validationResults);
}

async function IsTemplateNameExist(url, data, errorFunc) {

    return !data.LevelName.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}
