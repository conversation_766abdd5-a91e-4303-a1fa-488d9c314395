﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel.DataSyncOptionsViewModel
@using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel
@using ContinuityPatrol.Shared.Services.Helper
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title" ><i class="cp-data-sync"></i><span>DataSync Properties </span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input  class="form-check-input" type="checkbox" value="name=" id="name">
                                        <label class="form-check-label" for="name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="ReplicationType=" id="ReplicationType">
                                        <label class="form-check-label" for="ReplicationType">
                                            Replication&nbsp;Type
                                        </label>
                                    </div>
                                </li>                              
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" id="btnDataSyncCreate" class="btn btn-primary btn-sm"  data-bs-toggle="modal" data-bs-target="#CreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="pt-1 card-body" style="height: calc(100vh - 141px);">
         <div>
                <table class="table"  id="dataSyncTable" >
                    <thead>
                        <tr>
                            <th>Sr No</th>
                            <th >Name</th>
                            <th >Replication&nbsp;Type</th>
                            <th>SSH&nbsp;Private&nbsp;Key&nbsp;Path (Prod)</th>
                            <th>SSH&nbsp;Private&nbsp;Key&nbsp;Path (DR)</th>
                            <th>Retain&nbsp;Folder&nbsp;Permission</th>
                            <th>Incremental Replication</th>
                            <th>Shell&nbsp;Prompt&nbsp;(Prod)</th>
                            <th>Shell&nbsp;Prompt&nbsp;(DR)</th>
                            <th >Action</th>
                        </tr>
                    </thead>
                    <tbody>                    
                    </tbody>
                </table>
         </div>
        </div>
    </div>
    <!--Modal Create-->
    <div class="modal fade" data-bs-backdrop="static" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <form class="modal-content" id="CreateForm" >
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-data-sync"></i><span>DataSync Properties Configuration</span></h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <div class="form-label">Name</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-name"></i></span>
                                    <input asp-for="Name" id="dataSyncName" autocomplete="off" class="form-control" placeholder="Enter DataSync Properties Name" maxlength="100" />
                                </div>
                                <span asp-validation-for="Name" id="dataSyncNameError"></span>
                                <input asp-for="Id" type="hidden" id="dataSyncId" />
                                <input asp-for="Properties" type="hidden" id="dataSyncProperties" />
                            </div>
                        </div>  
                        <div class="col-6">
                            <div class="form-group">
                                <div class="form-label">Replication Type</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-replication-type"></i></span>
                                    <select asp-for="ReplicationType" id="dataSyncReplicationType" class=" form-select-modal" data-live-search="true" data-placeholder="Select Replication Type">
                                        <option></option>
                                        <option value=""></option>
                                        <option value="Application">Application</option>
                                        <option value="Database">Database</option>
                                    </select>
                                </div>
                                <span asp-validation-for="ReplicationType" id="replicationTypeError"></span>
                            </div>
                        </div>
                        <div class="col-12" id="radioBtn">
                            <div class="form-group gap-2 d-flex mb-0">
                                <div class="form-label me-3">Filter Option</div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="None" checked>
                                    <label class="form-check-label" for="inlineRadio1">None</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="Exclude">
                                    <label class="form-check-label" for="inlineRadio2">Exclude</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio3" value="Include">
                                    <label class="form-check-label" for="inlineRadio2">Include</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-12" id="excludeClm">
                            <div class="form-group">
                                <div class="form-label">File To Exclude</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-exclude"></i></span>
                                    <textarea class="form-control" id='txtFileExclude' rows="2"></textarea>
                                  @*   <input class="form-control" id='FileExclude' autocomplete="off" placeholder="Enter File To Exclude" style="height:20px" /> *@
                                </div>
                                <span asp-validation-for="Properties" id="fileExcludeError"></span>
                            </div>
                        </div>
                        <div class="col-12" id="IncludeClm1">
                            <div class="form-group">
                                <div class="form-label">File To Include</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-exclude"></i></span>
                                    <textarea class="form-control" id='txtFileInclude'></textarea>
                                   @*  <input class="form-control" id='FileInclude' autocomplete="off" placeholder="Enter File To Include" style="height:20px" /> *@
                                </div>
                                <span asp-validation-for="Properties" id="fileIncludeError"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group mt-2">
                                <div class="form-check form-check-inline" id="checkbox1">
                                    <input class="form-check-input Checkbox" type="checkbox" id="inlineCheckbox1" value="option1">
                                    <label class="form-check-label" for="inlineCheckbox1">Deletion Filter Option</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input Checkbox" type="checkbox" id="inlineCheckbox2" value="option2">
                                    <label class="form-check-label" for="inlineCheckbox2">Enable SSH Private Key (Production)</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input Checkbox" type="checkbox" id="inlineCheckbox3" value="option3">
                                    <label class="form-check-label" for="inlineCheckbox3">Enable SSH Private Key (DR)</label>
                                </div>
                                <div class="form-check form-check-inline" id="checkbox4">
                                    <input class="form-check-input Checkbox" type="checkbox" id="inlineCheckbox4" value="option4">
                                    <label class="form-check-label" for="inlineCheckbox4">Retain Folder Permission</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-12" id="fileDeleteClm">
                            <div class="form-group">
                                <div class="form-label">File Not To Delete</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-file-not-to-delete"></i></span>
                                    <textarea class="form-control" autocomplete="off" id='txtFileDelete' placeholder="Enter File Not To Delete"></textarea>
                                 @*    <input class="form-control"  id="FileDelete"  style="height:20px" /> *@
                                </div>
                                <span asp-validation-for="Properties" id="fileDeleteError"></span>
                            </div>                           
                        </div>
                        <div class="col-12" id="prSSHClm">
                            <div class="form-group">
                                <div class="form-label">SSH Private Key Path (Production) </div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-enable-ssh-private-key"></i></span>
                                    <input class="form-control" id="txtSSHPr" autocomplete="off" placeholder="Enter SSH Private Key Path (Production)" style="height:20px" />
                                </div>
                                <span asp-validation-for="Properties" id="prSSHError"></span>
                            </div>                          
                        </div>                      
                        <div class="col-12" id="drSSHClm">
                            <div class="form-group">
                                <div class="form-label">SSH Private Key Path (DR)</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-ssh-private-key-path"></i></span>
                                    <input class="form-control" id="SSHDr" autocomplete="off" placeholder="Enter SSH Private Key Path (DR)" style="height:20px" />
                                </div>
                                <span asp-validation-for="Properties" id="drSSHError"></span>
                            </div>                           
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <div class="form-label">Production Shell Prompt</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-shell-prompt"></i></span>
                                    <input class="form-control" id="prShellPrompt" autocomplete="off" placeholder="Enter Production Shell Prompt" maxlength="30" />
                                </div>
                                <span asp-validation-for="Properties" id="prShellPromptError"></span>
                            </div>
                        </div>                       
                        <div class="col-12">
                            <div class="form-group">
                                <div class="form-label">DR Shell Prompt</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-shell-prompt"></i></span>
                                    <input class="form-control" id="drShellPrompt" autocomplete="off" placeholder="Enter DR Shell Prompt" maxlength="30" />
                                </div>
                                <span asp-validation-for="Properties" id="drShellPromptError"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input Checkboxs" type="checkbox" id="inlineCheckboxs1" value="options1">
                                    <label class="form-check-label" for="inlineCheckboxs1">Enable Parallel Replication</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input Checkboxs" type="checkbox" id="inlineCheckboxs2" value="options2">
                                    <label class="form-check-label" for="inlineCheckboxs2">Enable Incremental Replication</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-12" id="threadsClm">
                            <div class="form-group">
                                <div class="form-label">Number Of Threads</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-thread"></i></span>
                                    <input class="form-control" id="txtThreads" autocomplete="off" placeholder="Enter Number Of Threads" maxlength="3" />
                                </div>
                                <span asp-validation-for="Properties" id="Threads-error"></span>
                            </div>
                        </div>
                    </div>                
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="dataSyncSave">Save</button>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>
<div id="configurationdataCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<div id="configurationdataDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
    <!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>
@* <script src="~/js/Configuration/Infra Components/DataSync Properties/dataSyncPagination.js"></script> *@
<script src="~/js/Configuration/Infra Components/DataSync Properties/DataSync.js"></script>




 
