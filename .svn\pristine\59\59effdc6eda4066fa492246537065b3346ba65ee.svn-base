﻿using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.WorkflowAction.Commands.Create;

public class CreateWorkflowActionCommandValidator : AbstractValidator<CreateWorkflowActionCommand>
{
    private readonly IWorkflowActionRepository _workflowActionRepository;

    public CreateWorkflowActionCommandValidator(IWorkflowActionRepository workflowActionRepository)
    {
        _workflowActionRepository = workflowActionRepository;

        RuleFor(w => w.ActionName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(w => w.Type)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please enter valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("Please enter the {PropertyName}.")
            .NotNull()
            .Must(IsValidJson)
            .WithMessage("{PropertyName} must be a valid json string.");


        RuleFor(w => w)
            .MustAsync(WorkflowActionListNameUnique)
            .WithMessage("A same name already exists.");
    }

    private bool IsValidJson(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                var obj = JsonConvert.DeserializeObject(properties);
                return obj != null;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }

    public async Task<bool> WorkflowActionListNameUnique(CreateWorkflowActionCommand createWorkflowActionCommand,
        CancellationToken cancellationToken)
    {
        return !await _workflowActionRepository.IsWorkflowActionNameUnique(createWorkflowActionCommand.ActionName);
    }
}