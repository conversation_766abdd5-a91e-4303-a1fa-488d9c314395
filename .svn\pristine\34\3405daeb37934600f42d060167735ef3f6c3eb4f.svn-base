﻿using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Replication.Commands.Delete;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Replication.Commands.Update;
using ContinuityPatrol.Application.Features.Replication.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Replication.Queries.GetList;
using ContinuityPatrol.Application.Features.Replication.Queries.GetNames;
using ContinuityPatrol.Application.Features.Replication.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Replication.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Replication.Queries.GetReplicationByLicensekey;
using ContinuityPatrol.Application.Features.Replication.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class ReplicationsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<ReplicationListVm>>> GetReplications()
    {
        Logger.LogDebug("Get All Replications ");

        //return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllReplicationsCacheKey + LoggedInUserService.CompanyId, () => Mediator.Send(new GetReplicationListQuery()), CacheExpiry));

        return Ok(await Mediator.Send(new GetReplicationListQuery()));
    }

    [HttpGet, Route("by/type")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<ReplicationTypeVm>>> GetReplicationByType(string? typeId)
    {
        Logger.LogDebug($"Get Replication by Type '{typeId}'");

        return Ok(await Mediator.Send(new GetReplicationTypeQuery { TypeId = typeId }));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateReplicationResponse>> CreateReplication([FromBody] CreateReplicationCommand createReplicationCommand)
    {
        Logger.LogDebug($"Create Replication '{createReplicationCommand.Name}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateReplication), await Mediator.Send(createReplicationCommand));
    }

    [HttpPost("save-as")]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<SaveAsReplicationResponse>> SaveAsReplication([FromBody] SaveAsReplicationCommand saveAsReplicationCommand)
    {
        Logger.LogDebug($"SaveAs Replication '{saveAsReplicationCommand.Name}'");

        ClearDataCache();

        return CreatedAtAction(nameof(SaveAsReplication), await Mediator.Send(saveAsReplicationCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateReplicationResponse>> UpdateReplication([FromBody] UpdateReplicationCommand updateReplicationCommand)
    {
        Logger.LogDebug($"Update Replication '{updateReplicationCommand.Name}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateReplicationCommand));
    }

    [HttpGet("{id}", Name = "Get Replication")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<ReplicationDetailVm>>> GetReplicationById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Replication Id");

        Logger.LogDebug($"Get Replication Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetReplicationDetailQuery { Id = id }));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteReplicationResponse>> DeleteReplication(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Replication Id");

        Logger.LogDebug($"Delete Replication Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteReplicationCommand { Id = id }));
    }

    [HttpGet, Route("names")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<ReplicationNameVm>>> GetReplicationNames()
    {
        Logger.LogDebug("Get All Replication Names");

        //return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllReplicationNameCacheKey,
        //    () => Mediator.Send(new GetReplicationNameQuery()), CacheExpiry));

        return Ok(await Mediator.Send(new GetReplicationNameQuery()));
    }

    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsReplicationNameExist(string replicationName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(replicationName, "Replication Name");

        Logger.LogDebug($"Check Name Exists Detail by Replication Name '{replicationName}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetReplicationNameUniqueQuery { ReplicationName = replicationName, ReplicationId = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<ReplicationListVm>>> GetPaginatedReplications([FromQuery] GetReplicationPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Replication Paginated List");

        return Ok(await Mediator.Send(query));
    }
    [HttpGet, Route("by/licenseId")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<GetReplicationByLicenseKeyListVm>>> GetReplicationByLicenseKey(string licenseId)
    {
        Logger.LogDebug($"Get Replication by License Key '{licenseId}'.");

        return Ok(await Mediator.Send(new GetReplicationByLicenseKeyQuery { LicenseId = licenseId }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllReplicationsCacheKey + LoggedInUserService.CompanyId,
            ApplicationConstants.Cache.AllReplicationNameCacheKey };

        ClearCache(cacheKeys);
    }
}