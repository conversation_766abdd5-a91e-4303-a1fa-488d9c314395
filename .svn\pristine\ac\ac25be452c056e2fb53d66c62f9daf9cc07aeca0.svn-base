using AutoFixture;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class HacmpClusterRepositoryTests : IClassFixture<HacmpClusterFixture>, IDisposable
{
    private readonly HacmpClusterFixture _hacmpClusterFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly HacmpClusterRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public HacmpClusterRepositoryTests(HacmpClusterFixture hacmpClusterFixture)
    {
        _hacmpClusterFixture = hacmpClusterFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new HacmpClusterRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.HacmpClusters.RemoveRange(_dbContext.HacmpClusters);
        await _dbContext.SaveChangesAsync();
    }

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithoutId()
    {
        // Arrange
        await ClearDatabase();
        var clusterName = "TestCluster";
        var hacmpCluster = new HacmpCluster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = clusterName,
            ServerId = "SERVER_123",
            ServerName = "Test Server",
            LSSRCPath = "/usr/sbin/lssrc",
            CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
            ResourceGroupName = "TestResourceGroup",
            IsActive = true
        };

        await _dbContext.HacmpClusters.AddAsync(hacmpCluster);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(clusterName, null);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameDoesNotExist_WithoutId()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "NonExistentCluster";

        // Act
        var result = await _repository.IsNameExist(nonExistentName, null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithInvalidGuid()
    {
        // Arrange
        await ClearDatabase();
        var clusterName = "TestCluster";
        var hacmpCluster = new HacmpCluster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = clusterName,
            ServerId = "SERVER_123",
            ServerName = "Test Server",
            LSSRCPath = "/usr/sbin/lssrc",
            CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
            ResourceGroupName = "TestResourceGroup",
            IsActive = true
        };

        await _dbContext.HacmpClusters.AddAsync(hacmpCluster);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(clusterName, "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithDifferentValidId()
    {
        // Arrange
        await ClearDatabase();
        var clusterName = "TestCluster";
        var existingId = Guid.NewGuid().ToString();
        var differentId = Guid.NewGuid().ToString();

        var hacmpCluster = new HacmpCluster
        {
            ReferenceId = existingId,
            Name = clusterName,
            ServerId = "SERVER_123",
            ServerName = "Test Server",
            LSSRCPath = "/usr/sbin/lssrc",
            CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
            ResourceGroupName = "TestResourceGroup",
            IsActive = true
        };

        await _dbContext.HacmpClusters.AddAsync(hacmpCluster);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(clusterName, differentId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameExists_WithSameValidId()
    {
        // Arrange
        await ClearDatabase();
        var clusterName = "TestCluster";
        var sameId = Guid.NewGuid().ToString();

        var hacmpCluster = new HacmpCluster
        {
            ReferenceId = sameId,
            Name = clusterName,
            ServerId = "SERVER_123",
            ServerName = "Test Server",
            LSSRCPath = "/usr/sbin/lssrc",
            CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
            ResourceGroupName = "TestResourceGroup",
            IsActive = true
        };

        await _dbContext.HacmpClusters.AddAsync(hacmpCluster);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(clusterName, sameId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var clusterName = "TestCluster";
        var hacmpCluster = new HacmpCluster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = clusterName,
            ServerId = "SERVER_123",
            ServerName = "Test Server",
            LSSRCPath = "/usr/sbin/lssrc",
            CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
            ResourceGroupName = "TestResourceGroup",
            IsActive = true
        };

        await _dbContext.HacmpClusters.AddAsync(hacmpCluster);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("TESTCLUSTER", null);

        // Assert
        Assert.False(result); // Should not match due to case sensitivity
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenMultipleNamesExist_WithDifferentId()
    {
        // Arrange
        await ClearDatabase();
        var clusterName = "TestCluster";
        var existingId1 = Guid.NewGuid().ToString();
        var existingId2 = Guid.NewGuid().ToString();
        var differentId = Guid.NewGuid().ToString();

        var hacmpClusters = new List<HacmpCluster>
        {
            new HacmpCluster
            {
                ReferenceId = existingId1,
                Name = clusterName,
                ServerId = "SERVER_123",
                ServerName = "Test Server 1",
                IsActive = true
            },
            new HacmpCluster
            {
                ReferenceId = existingId2,
                Name = clusterName,
                ServerId = "SERVER_456",
                ServerName = "Test Server 2",
                IsActive = true
            }
        };

        await _dbContext.HacmpClusters.AddRangeAsync(hacmpClusters);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(clusterName, differentId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenMultipleNamesExist_WithOneMatchingId()
    {
        // Arrange
        await ClearDatabase();
        var clusterName = "TestCluster";
        var existingId1 = "";
        var existingId2 = "";

        var hacmpClusters = new List<HacmpCluster>
        {
            new HacmpCluster
            {
                ReferenceId = existingId1,
                Name = clusterName,
                ServerId = "SERVER_123",
                ServerName = "Test Server 1",
                IsActive = true
            },
            new HacmpCluster
            {
                ReferenceId = existingId2,
                Name = clusterName,
                ServerId = "SERVER_456",
                ServerName = "Test Server 2",
                IsActive = true
            }
        };

        await _dbContext.HacmpClusters.AddRangeAsync(hacmpClusters);
        await _dbContext.SaveChangesAsync();

        // Act - Using existingId1 should return false because one of the matching records has this ID
        var result = await _repository.IsNameExist(clusterName, existingId1);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsFilteredResults_WithSpecification()
    {
        // Arrange
        await ClearDatabase();
        var hacmpClusters = new List<HacmpCluster>
        {
            new HacmpCluster
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Production Cluster",
                ServerId = "SERVER_123",
                ServerName = "Production Server",
                LSSRCPath = "/usr/sbin/lssrc",
                CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
                ResourceGroupName = "ProdResourceGroup",
                IsActive = true
            },
            new HacmpCluster
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Development Cluster",
                ServerId = "SERVER_456",
                ServerName = "Development Server",
                LSSRCPath = "/usr/sbin/lssrc",
                CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
                ResourceGroupName = "DevResourceGroup",
                IsActive = true
            },
            new HacmpCluster
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Test Environment",
                ServerId = "SERVER_789",
                ServerName = "Test Server",
                LSSRCPath = "/usr/sbin/lssrc",
                CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
                ResourceGroupName = "TestResourceGroup",
                IsActive = true
            }
        };

        await _dbContext.HacmpClusters.AddRangeAsync(hacmpClusters);
        await _dbContext.SaveChangesAsync();

        var filterSpec = new HacmpClusterFilterSpecification("Production");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, filterSpec, "Id", "desc");

        // Assert
        Assert.Equal(1, result.Data.Count);
        Assert.Equal("Production Cluster", result.Data.First().Name);
        Assert.Equal(1, result.TotalCount);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsAllResults_WithEmptyFilter()
    {
        // Arrange
        await ClearDatabase();
        var hacmpClusters = new List<HacmpCluster>
        {
            new HacmpCluster
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Cluster1",
                ServerId = "SERVER_123",
                ServerName = "Server1",
                LSSRCPath = "/usr/sbin/lssrc",
                CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
                ResourceGroupName = "ResourceGroup1",
                IsActive = true
            },
            new HacmpCluster
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Cluster2",
                ServerId = "SERVER_456",
                ServerName = "Server2",
                LSSRCPath = "/usr/sbin/lssrc",
                CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
                ResourceGroupName = "ResourceGroup2",
                IsActive = true
            }
        };

        await _dbContext.HacmpClusters.AddRangeAsync(hacmpClusters);
        await _dbContext.SaveChangesAsync();

        var filterSpec = new HacmpClusterFilterSpecification("");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, filterSpec, "Id", "desc");

        // Assert
        Assert.Equal(2, result.Data.Count);
        Assert.Equal(2, result.TotalCount);
        Assert.Contains(result.Data, x => x.Name == "Cluster1");
        Assert.Contains(result.Data, x => x.Name == "Cluster2");
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsOnlySelectedFields()
    {
        // Arrange
        await ClearDatabase();
        var hacmpCluster = new HacmpCluster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Test Cluster",
            ServerId = "SERVER_123",
            ServerName = "Test Server",
            LSSRCPath = "/usr/sbin/lssrc",
            CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
            ResourceGroupName = "TestResourceGroup",
            IsActive = true,
            CreatedBy = "TestUser",
            CreatedDate = DateTime.Now
        };

        await _dbContext.HacmpClusters.AddAsync(hacmpCluster);
        await _dbContext.SaveChangesAsync();

        var filterSpec = new HacmpClusterFilterSpecification("");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, filterSpec, "Id", "desc");

        // Assert
        Assert.Single(result.Data);
        var returnedCluster = result.Data.First();

        // These fields should be populated as they're selected in the method
        Assert.Equal(hacmpCluster.Id, returnedCluster.Id);
        Assert.Equal(hacmpCluster.ReferenceId, returnedCluster.ReferenceId);
        Assert.Equal(hacmpCluster.Name, returnedCluster.Name);
        Assert.Equal(hacmpCluster.ServerId, returnedCluster.ServerId);
        Assert.Equal(hacmpCluster.ServerName, returnedCluster.ServerName);
        Assert.Equal(hacmpCluster.CLRGInfoPath, returnedCluster.CLRGInfoPath);
        Assert.Equal(hacmpCluster.LSSRCPath, returnedCluster.LSSRCPath);
        Assert.Equal(hacmpCluster.ResourceGroupName, returnedCluster.ResourceGroupName);

        // These fields should be null/default as they're not selected in the projection
        Assert.Null(returnedCluster.CreatedBy);
        Assert.Equal(DateTime.MinValue, returnedCluster.CreatedDate);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var hacmpCluster = new HacmpCluster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Test Cluster",
            ServerId = "SERVER_123",
            ServerName = "Test Server",
            LSSRCPath = "/usr/sbin/lssrc",
            CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
            ResourceGroupName = "TestResourceGroup",
            IsActive = true
        };

        await _dbContext.HacmpClusters.AddAsync(hacmpCluster);
        await _dbContext.SaveChangesAsync();

        var filterSpec = new HacmpClusterFilterSpecification("NonExistentCluster");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, filterSpec, "Id", "desc");

        // Assert
        Assert.Empty(result.Data);
        Assert.Equal(0, result.TotalCount);
    }

    [Fact]
    public async Task PaginatedListAllAsync_RespectsPagination()
    {
        // Arrange
        await ClearDatabase();
        var hacmpClusters = new List<HacmpCluster>();
        for (int i = 1; i <= 15; i++)
        {
            hacmpClusters.Add(new HacmpCluster
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = $"Cluster{i:D2}",
                ServerId = $"SERVER_{i:D3}",
                ServerName = $"Server{i}",
                LSSRCPath = "/usr/sbin/lssrc",
                CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
                ResourceGroupName = $"ResourceGroup{i}",
                IsActive = true
            });
        }

        await _dbContext.HacmpClusters.AddRangeAsync(hacmpClusters);
        await _dbContext.SaveChangesAsync();

        var filterSpec = new HacmpClusterFilterSpecification("");

        // Act
        var result = await _repository.PaginatedListAllAsync(2, 5, filterSpec, "Id", "desc");

        // Assert
        Assert.Equal(5, result.Data.Count); // Page size
        Assert.Equal(15, result.TotalCount); // Total records
        Assert.Equal(2, result.CurrentPage);
        Assert.Equal(5, result.PageSize);
        Assert.Equal(3, result.TotalPages); // 15 records / 5 per page = 3 pages
    }

    [Fact]
    public async Task PaginatedListAllAsync_OrdersByIdDescending()
    {
        // Arrange
        await ClearDatabase();
        var hacmpClusters = new List<HacmpCluster>
        {
            new HacmpCluster
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "First Cluster",
                ServerId = "SERVER_123",
                ServerName = "First Server",
                IsActive = true
            },
            new HacmpCluster
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Second Cluster",
                ServerId = "SERVER_456",
                ServerName = "Second Server",
                IsActive = true
            }
        };

        await _dbContext.HacmpClusters.AddRangeAsync(hacmpClusters);
        await _dbContext.SaveChangesAsync();

        var filterSpec = new HacmpClusterFilterSpecification("");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, filterSpec, "Id", "desc");

        // Assert
        Assert.Equal(2, result.Data.Count);
        // Should be ordered by Id descending (higher Id first)
        Assert.True(result.Data[0].Id > result.Data[1].Id);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddHacmpCluster_WhenValidHacmpCluster()
    {
        // Arrange
        await ClearDatabase();
        var hacmpCluster = new HacmpCluster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Test Cluster",
            ServerId = "SERVER_123",
            ServerName = "Test Server",
            LSSRCPath = "/usr/sbin/lssrc",
            CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
            ResourceGroupName = "TestResourceGroup",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(hacmpCluster);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(hacmpCluster.Name, result.Name);
        Assert.Equal(hacmpCluster.ServerId, result.ServerId);
        Assert.Equal(hacmpCluster.ServerName, result.ServerName);
        Assert.Equal(hacmpCluster.LSSRCPath, result.LSSRCPath);
        Assert.Equal(hacmpCluster.CLRGInfoPath, result.CLRGInfoPath);
        Assert.Equal(hacmpCluster.ResourceGroupName, result.ResourceGroupName);
        Assert.Single(_dbContext.HacmpClusters);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenHacmpClusterIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsHacmpCluster_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var hacmpCluster = new HacmpCluster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Test Cluster",
            ServerId = "SERVER_123",
            ServerName = "Test Server",
            IsActive = true
        };

        await _dbContext.HacmpClusters.AddAsync(hacmpCluster);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(hacmpCluster.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(hacmpCluster.Id, result.Id);
        Assert.Equal(hacmpCluster.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsHacmpCluster_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var referenceId = Guid.NewGuid().ToString();
        var hacmpCluster = new HacmpCluster
        {
            ReferenceId = referenceId,
            Name = "Test Cluster",
            ServerId = "SERVER_123",
            ServerName = "Test Server",
            IsActive = true
        };

        await _dbContext.HacmpClusters.AddAsync(hacmpCluster);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal(hacmpCluster.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateHacmpCluster_WhenValidHacmpCluster()
    {
        // Arrange
        await ClearDatabase();
        var hacmpCluster = new HacmpCluster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Original Cluster",
            ServerId = "SERVER_123",
            ServerName = "Original Server",
            LSSRCPath = "/usr/sbin/lssrc",
            CLRGInfoPath = "/usr/es/sbin/cluster/utilities/clRGinfo",
            ResourceGroupName = "OriginalResourceGroup",
            IsActive = true
        };

        _dbContext.HacmpClusters.Add(hacmpCluster);
        await _dbContext.SaveChangesAsync();

        hacmpCluster.Name = "Updated Cluster";
        hacmpCluster.ServerName = "Updated Server";
        hacmpCluster.ResourceGroupName = "UpdatedResourceGroup";

        // Act
        var result = await _repository.UpdateAsync(hacmpCluster);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Cluster", result.Name);
        Assert.Equal("Updated Server", result.ServerName);
        Assert.Equal("UpdatedResourceGroup", result.ResourceGroupName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenHacmpClusterIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveHacmpCluster_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var hacmpCluster = new HacmpCluster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Test Cluster",
            ServerId = "SERVER_123",
            ServerName = "Test Server",
            IsActive = true
        };

        await _dbContext.HacmpClusters.AddAsync(hacmpCluster);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(hacmpCluster);

        // Assert
        var deletedCluster = await _dbContext.HacmpClusters.FindAsync(hacmpCluster.Id);
        Assert.Null(deletedCluster);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsAllHacmpClusters_WhenDataExists()
    {
        // Arrange
        await ClearDatabase();
        var hacmpClusters = new List<HacmpCluster>
        {
            new HacmpCluster { ReferenceId = Guid.NewGuid().ToString(), Name = "Cluster1", ServerId = "SERVER_123", ServerName = "Server1", IsActive = true },
            new HacmpCluster { ReferenceId = Guid.NewGuid().ToString(), Name = "Cluster2", ServerId = "SERVER_456", ServerName = "Server2", IsActive = true },
            new HacmpCluster { ReferenceId = Guid.NewGuid().ToString(), Name = "Cluster3", ServerId = "SERVER_789", ServerName = "Server3", IsActive = false }
        };

        await _dbContext.HacmpClusters.AddRangeAsync(hacmpClusters);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(3, result.Count); // Should return all clusters regardless of IsActive
        Assert.Contains(result, x => x.Name == "Cluster1");
        Assert.Contains(result, x => x.Name == "Cluster2");
        Assert.Contains(result, x => x.Name == "Cluster3");
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoData()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    #endregion
}
