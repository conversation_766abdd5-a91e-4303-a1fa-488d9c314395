﻿using AutoMapper.Features;
using ContinuityPatrol.Application.Features.SiteType.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.SiteTypeModel;
using Org.BouncyCastle.Bcpg.Sig;

namespace ContinuityPatrol.Application.UnitTests.Features.SiteType.Queries;

public class GetSiteTypeListQueryHandlerTests : IClassFixture<SiteTypeFixture>
{
    private readonly SiteTypeFixture _siteTypeFixture;

    private Mock<ISiteTypeRepository> _mockSiteTypeRepository;

    private readonly GetSiteTypeListQueryHandler _handler;

    public GetSiteTypeListQueryHandlerTests(SiteTypeFixture siteTypeFixture)
    {
        _siteTypeFixture = siteTypeFixture;

        _mockSiteTypeRepository = SiteTypeRepositoryMocks.GetSiteTypeRepository(_siteTypeFixture.SiteTypes);

        _handler = new GetSiteTypeListQueryHandler(_siteTypeFixture.Mapper, _mockSiteTypeRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_SiteTypesCount()
    {
        var query = new Fixture().Create<GetSiteTypeListQuery>();

        var result = await _handler.Handle(new GetSiteTypeListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<SiteTypeListVm>>();

        result.Count.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_Valid_SiteTypesList()
    {
        var query = new Fixture().Create<GetSiteTypeListQuery>();
        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldBeOfType<List<SiteTypeListVm>>();

        result[0].Id.ShouldBe(_siteTypeFixture.SiteTypes[0].ReferenceId);
        result[0].Type.ShouldBe(_siteTypeFixture.SiteTypes[0].Type);
        result[0].IsDelete.ShouldBe(_siteTypeFixture.SiteTypes[0].IsDelete);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockSiteTypeRepository = SiteTypeRepositoryMocks.GetSiteTypeEmptyRepository();

        var handler = new GetSiteTypeListQueryHandler(_siteTypeFixture.Mapper, _mockSiteTypeRepository.Object);

        var result = await handler.Handle(new GetSiteTypeListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetSiteTypeListQuery(), CancellationToken.None);

        _mockSiteTypeRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}