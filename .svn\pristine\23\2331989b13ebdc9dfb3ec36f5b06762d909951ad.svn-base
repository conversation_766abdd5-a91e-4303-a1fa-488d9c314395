﻿using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.GroupPolicy.Queries;

public class GetGroupPolicyNameQueryHandlerTests : IClassFixture<GroupPolicyFixture>
{
    private readonly GroupPolicyFixture _groupPolicyFixture;

    private Mock<IGroupPolicyRepository> _mockGroupPolicyRepository;

    private readonly GetGroupPolicyNameQueryHandler _handler;

    public GetGroupPolicyNameQueryHandlerTests(GroupPolicyFixture groupPolicyFixture)
    {
        _groupPolicyFixture = groupPolicyFixture;

        _mockGroupPolicyRepository = GroupPolicyRepositoryMocks.GetGroupPolicyNamesRepository(_groupPolicyFixture.GroupPolicies);

        _handler = new GetGroupPolicyNameQueryHandler(_mockGroupPolicyRepository.Object, _groupPolicyFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_Active_GroupPolicy_Name()
    {
        var result = await _handler.Handle(new GetGroupPolicyNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<GroupPolicyNameVm>>();

        result[0].Id.ShouldBe(_groupPolicyFixture.GroupPolicies[0].ReferenceId);
    }

    [Fact]
    public async Task Handle_Return_Active_GroupPolicyNamesCount()
    {
        var result = await _handler.Handle(new GetGroupPolicyNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<GroupPolicyNameVm>>();

        result.Count.ShouldBe(_groupPolicyFixture.GroupPolicies.Count);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockGroupPolicyRepository = GroupPolicyRepositoryMocks.GetGroupPolicyEmptyRepository();

        var handler = new GetGroupPolicyNameQueryHandler(_mockGroupPolicyRepository.Object, _groupPolicyFixture.Mapper);

        var result = await handler.Handle(new GetGroupPolicyNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetGroupPolicyNamesMethod_OneTime()
    {
        await _handler.Handle(new GetGroupPolicyNameQuery(), CancellationToken.None);

        _mockGroupPolicyRepository.Verify(x => x.GetGroupPolicyNames(), Times.Once);
    }
}