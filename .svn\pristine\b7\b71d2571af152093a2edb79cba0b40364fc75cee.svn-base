﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.MsSqlNativeLogShippingMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetPaginatedList;

public class GetMsSqlNativeLogShippingMonitorStatusPaginatedListQueryHandler : IRequestHandler<
    GetMsSqlNativeLogShippingMonitorStatusPaginatedListQuery,
    PaginatedResult<MsSqlNativeLogShippingMonitorStatusListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMsSqlNativeLogShippingMonitorStatusRepository _msSqlNativeLogShippingMonitorStatusRepository;

    public GetMsSqlNativeLogShippingMonitorStatusPaginatedListQueryHandler(IMapper mapper,
        IMsSqlNativeLogShippingMonitorStatusRepository msSqlNativeLogShippingMonitorStatusRepository)
    {
        _mapper = mapper;
        _msSqlNativeLogShippingMonitorStatusRepository = msSqlNativeLogShippingMonitorStatusRepository;
    }

    public async Task<PaginatedResult<MsSqlNativeLogShippingMonitorStatusListVm>> Handle(
        GetMsSqlNativeLogShippingMonitorStatusPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _msSqlNativeLogShippingMonitorStatusRepository.GetPaginatedQuery();

        var productFilterSpec = new MsSqlNativeLogShippingMonitorStatusFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<MsSqlNativeLogShippingMonitorStatusListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}