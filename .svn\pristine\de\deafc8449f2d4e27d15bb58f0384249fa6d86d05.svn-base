﻿using ContinuityPatrol.Web.TagHelpers;

namespace ContinuityPatrol.Web.UnitTests.TagHelpers;

public class NotificationTagHelperTests
{
    private static Task<TagHelperOutput> RunTagHelperAsync(NotificationTagHelper tagHelper)
    {
        var context = new TagHelperContext(
            tagName: "notification",
            allAttributes: new TagHelperAttributeList
            {
                { "class-name", tagHelper.ClassName },
                { "message", tagHelper.Message }
            },
            items: new Dictionary<object, object>(),
            uniqueId: "test");

        var output = new TagHelperOutput(
            "notification",
            attributes: new TagHelperAttributeList(),
            getChildContentAsync: (_, _) =>
            {
                var content = new DefaultTagHelperContent();
                content.SetContent("");
                return Task.FromResult<TagHelperContent>(content);
            });

        tagHelper.Process(context, output);
        return Task.FromResult(output);
    }

    [Theory]
    [InlineData("success", "cp-check")]
    [InlineData("info", "cp-note")]
    [InlineData("warning", "cp-exclamation")]
    [InlineData("error", "cp-close")]
    [InlineData("unauthorised", "cp-authentication-failed")]
    public async Task Process_SetsCorrectIconBasedOnClassName(string className, string expectedIcon)
    {
        // Arrange
        var tagHelper = new NotificationTagHelper
        {
            ClassName = className,
            Message = "Test message"
        };

        // Act
        var output = await RunTagHelperAsync(tagHelper);
        var content = output.Content.GetContent();

        // Assert
        Assert.Equal("div", output.TagName);
        Assert.Contains(expectedIcon, content);
        Assert.Contains("Test message", content);
    }

    [Fact]
    public async Task Process_DefaultClassName_DoesNotSetIcon()
    {
        // Arrange
        var tagHelper = new NotificationTagHelper
        {
            ClassName = "custom",
            Message = "Custom message"
        };

        // Act
        var output = await RunTagHelperAsync(tagHelper);
        var content = output.Content.GetContent();

        // Assert
        Assert.Equal("div", output.TagName);
        Assert.DoesNotContain("cp-check", content);
        Assert.DoesNotContain("cp-note", content);
        Assert.DoesNotContain("cp-exclamation", content);
        Assert.DoesNotContain("cp-close", content);
        Assert.DoesNotContain("cp-authentication-failed", content);
        Assert.Contains("Custom message", content);
    }
}