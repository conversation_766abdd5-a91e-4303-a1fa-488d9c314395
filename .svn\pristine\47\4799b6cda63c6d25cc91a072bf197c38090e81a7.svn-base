﻿namespace ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Create;

public class CreateHeatMapStatusCommand : IRequest<CreateHeatMapStatusResponse>
{
    public string BusinessServiceId { get; set; }

    public string BusinessServiceName { get; set; }

    public string BusinessFunctionId { get; set; }

    public string BusinessFunctionName { get; set; }

    public string InfraObjectId { get; set; }

    public string InfraObjectName { get; set; }

    public string EntityId { get; set; }

    public string HeatmapType { get; set; }

    public string HeatmapStatus { get; set; }
    public string Properties { get; set; }

    public bool IsAffected { get; set; }

    public string ErrorMessage { get; set; }

    public override string ToString()
    {
        return $"InfraObjectName: {InfraObjectName};";
    }
}