﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObjectScheduler.Queries;

public class GetInfraObjectSchedulerNameUniqueQueryHandlerTests : IClassFixture<InfraObjectSchedulerFixture>
{
    private readonly InfraObjectSchedulerFixture _infraObjectSchedulerFixture;

    private Mock<IInfraObjectSchedulerRepository> _mockInfraObjectSchedulerRepository;

    private readonly GetInfraObjectSchedulerNameUniqueQueryHandler _handler;

    public GetInfraObjectSchedulerNameUniqueQueryHandlerTests(InfraObjectSchedulerFixture infraObjectSchedulerFixture)
    {
        _infraObjectSchedulerFixture = infraObjectSchedulerFixture;

        _mockInfraObjectSchedulerRepository = InfraObjectSchedulerRepositoryMocks.GetInfraObjectSchedulerNameUniqueRepository(_infraObjectSchedulerFixture.InfraObjectSchedulers);

        _handler = new GetInfraObjectSchedulerNameUniqueQueryHandler(_mockInfraObjectSchedulerRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_InfraObjectName_Exist()
    {
        _infraObjectSchedulerFixture.InfraObjectSchedulers[0].InfraObjectName = "Object_Infra";

        var result = await _handler.Handle(new GetInfraObjectSchedulerNameUniqueQuery { InfraObjectName = _infraObjectSchedulerFixture.InfraObjectSchedulers[0].InfraObjectName, Id = _infraObjectSchedulerFixture.InfraObjectSchedulers[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Call_IsInfraObjectNameExist_OneTime()
    {
        await _handler.Handle(new GetInfraObjectSchedulerNameUniqueQuery(), CancellationToken.None);

        _mockInfraObjectSchedulerRepository.Verify(x => x.IsInfraObjectSchedulerNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_False_infraObjectName_NotMatch()
    {
        var result = await _handler.Handle(new GetInfraObjectSchedulerNameUniqueQuery { InfraObjectName = "Object_Infra", Id = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockInfraObjectSchedulerRepository = InfraObjectSchedulerRepositoryMocks.GetInfraObjectSchedulerEmptyRepository();

        var result = await _handler.Handle(new GetInfraObjectSchedulerNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}