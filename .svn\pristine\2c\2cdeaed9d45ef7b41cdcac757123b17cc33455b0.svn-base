using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Events.Update;

public class WorkflowActionFieldMasterUpdatedEventHandler : INotificationHandler<WorkflowActionFieldMasterUpdatedEvent>
{
    private readonly ILogger<WorkflowActionFieldMasterUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowActionFieldMasterUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<WorkflowActionFieldMasterUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(WorkflowActionFieldMasterUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} WorkflowActionFieldMaster",
            Entity = "WorkflowActionFieldMaster",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"WorkflowActionFieldMaster '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"WorkflowActionFieldMaster '{updatedEvent.Name}' updated successfully.");
    }
}