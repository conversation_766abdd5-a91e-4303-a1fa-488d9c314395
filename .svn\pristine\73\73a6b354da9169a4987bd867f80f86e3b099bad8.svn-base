using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AzureStorageAccountMonitorlogsFixture : IDisposable
{
    public List<AzureStorageAccountMonitorlogs> AzureStorageAccountMonitorlogsPaginationList { get; set; }
    public List<AzureStorageAccountMonitorlogs> AzureStorageAccountMonitorlogsList { get; set; }
    public AzureStorageAccountMonitorlogs AzureStorageAccountMonitorlogsDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public AzureStorageAccountMonitorlogsFixture()
    {
        var fixture = new Fixture();

        AzureStorageAccountMonitorlogsList = fixture.Create<List<AzureStorageAccountMonitorlogs>>();

        AzureStorageAccountMonitorlogsPaginationList = fixture.CreateMany<AzureStorageAccountMonitorlogs>(20).ToList();

        AzureStorageAccountMonitorlogsPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AzureStorageAccountMonitorlogsPaginationList.ForEach(x => x.IsActive = true);

        AzureStorageAccountMonitorlogsList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AzureStorageAccountMonitorlogsList.ForEach(x => x.IsActive = true);

        AzureStorageAccountMonitorlogsDto = fixture.Create<AzureStorageAccountMonitorlogs>();
        AzureStorageAccountMonitorlogsDto.ReferenceId = Guid.NewGuid().ToString();
        AzureStorageAccountMonitorlogsDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
