using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public static class BackUpLogRepositoryMocks
{
    public static Mock<IBackUpLogRepository> CreateBackUpLogRepository(List<BackUpLog> backUpLogs)
    {
        var mockBackUpLogRepository = new Mock<IBackUpLogRepository>();

        mockBackUpLogRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(backUpLogs);

        mockBackUpLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => backUpLogs.FirstOrDefault(x => x.ReferenceId == id && x.IsActive));

        mockBackUpLogRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string name, string id) => 
                backUpLogs.Any(x => x.DatabaseName == name && x.ReferenceId != id && x.IsActive));

        mockBackUpLogRepository.Setup(repo => repo.AddAsync(It.IsAny<BackUpLog>()))
            .ReturnsAsync((BackUpLog backUpLog) =>
            {
                backUpLog.ReferenceId = Guid.NewGuid().ToString();
                backUpLog.Id = backUpLogs.Count + 1;
                backUpLogs.Add(backUpLog);
                return backUpLog;
            });

        mockBackUpLogRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BackUpLog>()))
            .Returns<BackUpLog>(async (BackUpLog backUpLog) =>
            {
                var existingBackUpLog = backUpLogs.FirstOrDefault(x => x.ReferenceId == backUpLog.ReferenceId);
                if (existingBackUpLog != null)
                {
                    existingBackUpLog.HostName = backUpLog.HostName;
                    existingBackUpLog.DatabaseName = backUpLog.DatabaseName;
                    existingBackUpLog.UserName = backUpLog.UserName;
                    existingBackUpLog.IsLocalServer = backUpLog.IsLocalServer;
                    existingBackUpLog.IsBackUpServer = backUpLog.IsBackUpServer;
                    existingBackUpLog.BackUpPath = backUpLog.BackUpPath;
                    existingBackUpLog.Type = backUpLog.Type;
                    existingBackUpLog.Status = backUpLog.Status;
                    existingBackUpLog.Properties = backUpLog.Properties;
                    existingBackUpLog.IsActive = backUpLog.IsActive;
                    return existingBackUpLog;
                }
                return backUpLog;
            });

        mockBackUpLogRepository.Setup(repo => repo.DeleteAsync(It.IsAny<BackUpLog>()))
            .Returns<BackUpLog>(async (BackUpLog backUpLog) =>
            {
                var existingBackUpLog = backUpLogs.FirstOrDefault(x => x.ReferenceId == backUpLog.ReferenceId);
                if (existingBackUpLog != null)
                {
                    backUpLogs.Remove(existingBackUpLog);
                    existingBackUpLog.IsActive = false; // Soft delete
                }
                return backUpLog;
            });

        //mockBackUpLogRepository.Setup(repo => repo.PaginatedListAllAsync(
        //    It.IsAny<int>(), It.IsAny<int>(), It.IsAny<ISpecification<BackUpLog>>(), 
        //    It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((int pageNumber, int pageSize, ISpecification<BackUpLog> spec, string sortColumn, string sortOrder) =>
        //    {
        //        var filteredBackUpLogs = backUpLogs.Where(x => x.IsActive).ToList();
                
        //        if (spec != null)
        //        {
        //            // Apply specification filter if needed
        //            filteredBackUpLogs = filteredBackUpLogs.Where(spec.Criteria.Compile()).ToList();
        //        }

        //        var totalCount = filteredBackUpLogs.Count;
        //        var pagedBackUpLogs = filteredBackUpLogs
        //            .Skip((pageNumber - 1) * pageSize)
        //            .Take(pageSize)
        //            .ToList();

        //        return new PaginatedResult<BackUpLog>
        //        {
        //            Data = pagedBackUpLogs,
        //            TotalCount = totalCount,
        //            PageSize = pageSize,
        //            TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
        //        };
        //    });

        return mockBackUpLogRepository;
    }

    public static Mock<IUserActivityRepository> CreateUserActivityRepository(List<UserActivity> userActivities)
    {
        var mockUserActivityRepository = new Mock<IUserActivityRepository>();

        mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
            .ReturnsAsync((UserActivity userActivity) =>
            {
                userActivity.Id = userActivities.Count + 1;
                userActivity.ReferenceId = Guid.NewGuid().ToString();
                userActivities.Add(userActivity);
                return userActivity;
            });

        mockUserActivityRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(userActivities);

        return mockUserActivityRepository;
    }
}
