﻿using ContinuityPatrol.Application.Features.Job.Commands.Create;
using ContinuityPatrol.Application.Features.Job.Commands.Update;
using ContinuityPatrol.Application.Features.Job.Events.Create;
using ContinuityPatrol.Application.Features.Job.Events.Delete;
using ContinuityPatrol.Application.Features.Job.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Job.Events.Update;
using ContinuityPatrol.Application.Features.Job.Events.UpdateJobState;
using ContinuityPatrol.Application.Features.Job.Events.UpdateJobStatus;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;


public class JobFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<Job> Jobs { get; set; }
    public CreateJobCommand CreateJobCommand { get; set; }
    public UpdateJobCommand UpdateJobCommand { get; set; }
    public JobCreatedEvent JobCreatedEvent { get; set; }
    public JobDeletedEvent JobDeletedEvent { get; set; }
    public JobUpdatedEvent JobUpdatedEvent { get; set; }
    public JobPaginatedEvent JobPaginatedEvent { get; set; }
    public JobStatusUpdatedEvent JobStatusUpdatedEvent { get; set; }
    public JobStateUpdatedEvent JobStateUpdatedEvent { get; set; }

    public JobFixture()
    {
        Jobs = AutoJobFixture.Create<List<Job>>();
        CreateJobCommand = AutoJobFixture.Create<CreateJobCommand>();
        UpdateJobCommand = AutoJobFixture.Create<UpdateJobCommand>();
        JobCreatedEvent = AutoJobFixture.Create<JobCreatedEvent>();
        JobDeletedEvent = AutoJobFixture.Create<JobDeletedEvent>();
        JobUpdatedEvent = AutoJobFixture.Create<JobUpdatedEvent>();
        JobPaginatedEvent = AutoJobFixture.Create<JobPaginatedEvent>();
        JobStatusUpdatedEvent = AutoJobFixture.Create<JobStatusUpdatedEvent>();
        JobStateUpdatedEvent = AutoJobFixture.Create<JobStateUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<JobProfile>();
        });

        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoJobFixture
    {
        get
        {
            var fixture = new Fixture();
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateJobCommand>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateJobCommand>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<JobCreatedEvent>(p => p.JobName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<JobDeletedEvent>(p => p.JobName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<JobUpdatedEvent>(p => p.JobName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<JobPaginatedEvent>(p => p.JobName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<JobStatusUpdatedEvent>(p => p.JobName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<JobStateUpdatedEvent>(p => p.JobName, 10));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}