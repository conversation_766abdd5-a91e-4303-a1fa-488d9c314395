﻿namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatusList;

public class WorkflowOperationGroupRunningStatusListVm
{
    public string Id { get; set; }
    public string CurrentActionName { get; set; }
    public string Status { get; set; }
    public string ProgressStatus { get; set; }
    public string ActionMode { get; set; }
    public int IsResume { get; set; }
    public int IsReExecute { get; set; }
    public int IsPause { get; set; }
    public int IsAbort { get; set; }
    public int WaitToNext { get; set; }
    public int ConditionalOperation { get; set; }
}

public class ProfileRunningCountListVm
{
    public string WorkflowOperationId { get; set; }
    public int RunningCount { get; set; }
    public int ErrorCount { get; set; }
    public int SuccessCount { get; set; }
    public int SkipCount { get; set; }
    public int BypassedCount { get; set; }

    public List<WorkflowOperationGroupRunningStatusListVm> WorkflowOperationGroupRunningStatusListVm { get; set; } = new();
}
