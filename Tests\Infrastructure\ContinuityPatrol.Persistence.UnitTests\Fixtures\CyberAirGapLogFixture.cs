using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class CyberAirGapLogFixture : IDisposable
{
    public const string CompanyId = "550e8400-e29b-41d4-a716-446655440000";
    public const string ParentCompanyId = "550e8400-e29b-41d4-a716-446655440001";
    public const string AirGapId = "AIRGAP_001";
    public const string ParentAirGapId = "PARENT_AIRGAP_001";

    public List<CyberAirGapLog> CyberAirGapLogPaginationList { get; set; }
    public List<CyberAirGapLog> CyberAirGapLogList { get; set; }
    public CyberAirGapLog CyberAirGapLogDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public CyberAirGapLogFixture()
    {
        var fixture = new Fixture();

        CyberAirGapLogList = fixture.Create<List<CyberAirGapLog>>();

        CyberAirGapLogPaginationList = fixture.CreateMany<CyberAirGapLog>(20).ToList();

        CyberAirGapLogDto = fixture.Create<CyberAirGapLog>();
        CyberAirGapLogDto.ReferenceId = Guid.NewGuid().ToString();
        CyberAirGapLogDto.IsActive = true;
        CyberAirGapLogDto.AirGapId = AirGapId;
        CyberAirGapLogDto.AirGapName = "TestAirGap";
        CyberAirGapLogDto.Status = "Success";
        CyberAirGapLogDto.CreatedDate = DateTime.Now;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
