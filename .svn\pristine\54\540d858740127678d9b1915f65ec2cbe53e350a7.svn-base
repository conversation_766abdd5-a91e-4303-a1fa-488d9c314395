﻿using ContinuityPatrol.Application.Features.TeamMaster.Events.Update;

namespace ContinuityPatrol.Application.Features.TeamMaster.Commands.Update;

public class UpdateTeamMasterCommandHandler : IRequestHandler<UpdateTeamMasterCommand, UpdateTeamMasterResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly ITeamMasterRepository _teamMasterRepository;

    public UpdateTeamMasterCommandHandler(IMapper mapper, ITeamMasterRepository teamMasterRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _teamMasterRepository = teamMasterRepository;
        _publisher = publisher;
    }

    public async Task<UpdateTeamMasterResponse> Handle(UpdateTeamMasterCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _teamMasterRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.TeamMaster), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateTeamMasterCommand), typeof(Domain.Entities.TeamMaster));

        await _teamMasterRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateTeamMasterResponse
        {
            Message = Message.Update(nameof(Domain.Entities.TeamMaster), eventToUpdate.GroupName),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new TeamMasterUpdatedEvent { GroupName = eventToUpdate.GroupName }, cancellationToken);

        return response;
    }
}