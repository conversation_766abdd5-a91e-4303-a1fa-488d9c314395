﻿using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.InfraReplicationMappingModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraReplicationMapping.Queries;

public class GetInfraReplicationMappingPaginatedListQueryHandlerTests : IClassFixture<InfraReplicationMappingFixture>
{
    private readonly GetInfraReplicationMappingPaginatedListQueryHandler _handler;
    private readonly Mock<IInfraReplicationMappingRepository> _mockInfraReplicationMappingRepository;
    private readonly Mock<IPublisher> _publisher;

    public GetInfraReplicationMappingPaginatedListQueryHandlerTests(InfraReplicationMappingFixture infraReplicationMappingFixture)
    {
        var infraReplicationMappingNewFixture = infraReplicationMappingFixture;
        _publisher = new Mock<IPublisher>();

        _mockInfraReplicationMappingRepository = InfraReplicationMappingRepositoryMocks.GetPaginatedInfraReplicationMappingRepository(infraReplicationMappingNewFixture.InfraReplicationMappings);

        _handler = new GetInfraReplicationMappingPaginatedListQueryHandler(_mockInfraReplicationMappingRepository.Object, infraReplicationMappingNewFixture.Mapper);

        infraReplicationMappingNewFixture.InfraReplicationMappings[0].DatabaseName = "Testing";
        infraReplicationMappingNewFixture.InfraReplicationMappings[0].ReplicationMasterName = "Replication_Master";


        infraReplicationMappingNewFixture.InfraReplicationMappings[1].DatabaseName = "Replication_Type";
        infraReplicationMappingNewFixture.InfraReplicationMappings[1].ReplicationMasterName = "Replica_Infra";
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetInfraReplicationMappingPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraReplicationMappingListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedInfraReplicationMappings_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetInfraReplicationMappingPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Testing" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraReplicationMappingListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<InfraReplicationMappingListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].DatabaseName.ShouldBe("Testing");

        result.Data[0].ReplicationMasterName.ShouldBe("Replication_Master");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetInfraReplicationMappingPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraReplicationMappingListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_InfraReplicationMappings_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetInfraReplicationMappingPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "databasename=Testing;replicationmastername=Replication_Master" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraReplicationMappingListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].DatabaseName.ShouldBe("Testing");

        result.Data[0].ReplicationMasterName.ShouldBe("Replication_Master");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetInfraReplicationMappingPaginatedListQuery(), CancellationToken.None);

        _mockInfraReplicationMappingRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}