using ContinuityPatrol.Application.Features.BulkImport.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;
using MediatR;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImport.Commands;

public class CreateBulkImportTests : IClassFixture<BulkImportFixture>
{
    private readonly BulkImportFixture _bulkImportFixture;
    private readonly Mock<IBulkImportOperationGroupRepository> _mockBulkImportOperationGroupRepository;
    private readonly BulkImportHelperService _bulkImportHelperService;
    private readonly Mock<ILogger<CreateBulkImportCommandHandler>> _loggermock;
    private readonly CreateBulkImportCommandHandler _handler;

    public CreateBulkImportTests(BulkImportFixture bulkImportFixture)
    {
        _bulkImportFixture = bulkImportFixture;

        _mockBulkImportOperationGroupRepository = BulkImportRepositoryMocks.CreateBulkImportOperationGroupRepository(_bulkImportFixture.BulkImportOperationGroups);
        _bulkImportHelperService = BulkImportRepositoryMocks.CreateBulkImportHelperService();

        _loggermock = new Mock<ILogger<CreateBulkImportCommandHandler>>();

        _handler = new CreateBulkImportCommandHandler(
            _mockBulkImportOperationGroupRepository.Object,
            _bulkImportHelperService, _loggermock.Object);
    }

    [Fact]
    public async Task Handle_Return_CreateBulkImportResponse_When_BulkImportCreated()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var command = new CreateBulkImportCommand { Id = existingGroup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(CreateBulkImportResponse));
        result.BulkImportOperationGroupId.ShouldBe(existingGroup.ReferenceId);
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var command = new CreateBulkImportCommand { Id = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationGroupNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new CreateBulkImportCommand { Id = nonExistentId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportOperationGroup)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationGroupIsInactive()
    {
        // Arrange
        var inactiveGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        inactiveGroup.IsActive = false;
        var command = new CreateBulkImportCommand { Id = inactiveGroup.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_DeserializeProperties_When_ProcessingBulkImport()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var testProperties = new CreateBulkImportOperationListCommand
        {
            ServerList = new List<CreateBulkDataServerListCommand>
            {
                new CreateBulkDataServerListCommand { Name = "TestServer" }
            },
            DatabaseList = new List<CreateBulkDataDataBaseListCommand>
            {
                new CreateBulkDataDataBaseListCommand { Name = "TestDatabase" }
            },
            ReplicationList = new List<CreateBulkDataReplicationListCommand>(),
            InfraObject = null
        };

        existingGroup.Properties = JsonConvert.SerializeObject(testProperties);
        var command = new CreateBulkImportCommand { Id = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

    //    // Assert
    //    _mockBulkImportHelperService.Verify(x => x.CreateServer(It.IsAny<List<CreateBulkDataServerListCommand>>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    //    _mockBulkImportHelperService.Verify(x => x.CreateDatabase(It.IsAny<List<CreateBulkDataDataBaseListCommand>>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CallCreateServer_When_ServerListExists()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var testProperties = new CreateBulkImportOperationListCommand
        {
            ServerList = new List<CreateBulkDataServerListCommand>
            {
                new CreateBulkDataServerListCommand { Name = "TestServer1" },
                new CreateBulkDataServerListCommand { Name = "TestServer2" }
            },
            DatabaseList = new List<CreateBulkDataDataBaseListCommand>(),
            ReplicationList = new List<CreateBulkDataReplicationListCommand>(),
            InfraObject = null
        };

        existingGroup.Properties = JsonConvert.SerializeObject(testProperties);
        var command = new CreateBulkImportCommand { Id = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        //_mockBulkImportHelperService.Verify(x => x.CreateServer(
        //    It.Is<List<CreateBulkDataServerListCommand>>(list => list.Count == 2), 
        //    existingGroup.BulkImportOperationId, 
        //    existingGroup.ReferenceId, 
        //    It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CallCreateDatabase_When_DatabaseListExists()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var testProperties = new CreateBulkImportOperationListCommand
        {
            ServerList = new List<CreateBulkDataServerListCommand>(),
            DatabaseList = new List<CreateBulkDataDataBaseListCommand>
            {
                new CreateBulkDataDataBaseListCommand { Name = "TestDB1" },
                new CreateBulkDataDataBaseListCommand { Name = "TestDB2" }
            },
            ReplicationList = new List<CreateBulkDataReplicationListCommand>(),
            InfraObject = null
        };

        existingGroup.Properties = JsonConvert.SerializeObject(testProperties);
        var command = new CreateBulkImportCommand { Id = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        //_mockBulkImportHelperService.Verify(x => x.CreateDatabase(
        //    It.Is<List<CreateBulkDataDataBaseListCommand>>(list => list.Count == 2), 
        //    existingGroup.BulkImportOperationId, 
        //    existingGroup.ReferenceId, 
        //    It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CallCreateReplication_When_ReplicationListExists()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var testProperties = new CreateBulkImportOperationListCommand
        {
            ServerList = new List<CreateBulkDataServerListCommand>(),
            DatabaseList = new List<CreateBulkDataDataBaseListCommand>(),
            ReplicationList = new List<CreateBulkDataReplicationListCommand>
            {
                new CreateBulkDataReplicationListCommand { Name = "TestReplication" }
            },
            InfraObject = null
        };

        existingGroup.Properties = JsonConvert.SerializeObject(testProperties);
        var command = new CreateBulkImportCommand { Id = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        //_mockBulkImportHelperService.Verify(x => x.CreateReplication(
        //    It.Is<List<CreateBulkDataReplicationListCommand>>(list => list.Count == 1), 
        //    existingGroup.BulkImportOperationId, 
        //    existingGroup.ReferenceId, 
        //    It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CallCreateInfraObject_When_InfraObjectExists()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var testProperties = new CreateBulkImportOperationListCommand
        {
            ServerList = new List<CreateBulkDataServerListCommand>(),
            DatabaseList = new List<CreateBulkDataDataBaseListCommand>(),
            ReplicationList = new List<CreateBulkDataReplicationListCommand>()
        };

        existingGroup.Properties = JsonConvert.SerializeObject(testProperties);
        var command = new CreateBulkImportCommand { Id = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        //_mockBulkImportHelperService.Verify(x => x.CreateInfraObject(
        //    It.IsAny<CreateBulkImportOperationListCommand>(),
        //    existingGroup.BulkImportOperationId,
        //    existingGroup.ReferenceId,
        //    It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CallCreateWorkflow_When_WorkflowExists()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var testProperties = new CreateBulkImportOperationListCommand
        {
            ServerList = new List<CreateBulkDataServerListCommand>(),
            DatabaseList = new List<CreateBulkDataDataBaseListCommand>(),
            ReplicationList = new List<CreateBulkDataReplicationListCommand>(),
            InfraObject = null,
            IsSwitchOver = true,
            SwitchOverTemplate = "TestTemplate"
        };

        existingGroup.Properties = JsonConvert.SerializeObject(testProperties);
        var command = new CreateBulkImportCommand { Id = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        //_mockBulkImportHelperService.Verify(x => x.CreateWorkflow(
        //    It.IsAny<CreateBulkImportOperationListCommand>(), 
        //    existingGroup.BulkImportOperationId, 
        //    existingGroup.ReferenceId, 
        //    It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_OperationSuccessful()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var command = new CreateBulkImportCommand { Id = existingGroup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<CreateBulkImportResponse>();
        result.GetType().ShouldBe(typeof(CreateBulkImportResponse));
    }

    [Fact]
    public async Task Handle_ValidateGuidFormat_When_IdProvided()
    {
        // Arrange
        var invalidId = "invalid-guid";
        var command = new CreateBulkImportCommand { Id = invalidId };

        // Act & Assert
        await Should.ThrowAsync<InvalidException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var command = new CreateBulkImportCommand { Id = testId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_bulkImportFixture.BulkImportOperationGroups.First());

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }

    [Fact]
    public async Task Handle_NotCallHelperServices_When_ListsAreEmpty()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var testProperties = new CreateBulkImportOperationListCommand
        {
            ServerList = new List<CreateBulkDataServerListCommand>(),
            DatabaseList = new List<CreateBulkDataDataBaseListCommand>(),
            ReplicationList = new List<CreateBulkDataReplicationListCommand>(),
            InfraObject = null
        };

        existingGroup.Properties = JsonConvert.SerializeObject(testProperties);
        var command = new CreateBulkImportCommand { Id = existingGroup.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        //_mockBulkImportHelperService.Verify(x => x.CreateServer(It.IsAny<List<CreateBulkDataServerListCommand>>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
        //_mockBulkImportHelperService.Verify(x => x.CreateDatabase(It.IsAny<List<CreateBulkDataDataBaseListCommand>>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
        //_mockBulkImportHelperService.Verify(x => x.CreateReplication(It.IsAny<List<CreateBulkDataReplicationListCommand>>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
        //_mockBulkImportHelperService.Verify(x => x.CreateInfraObject(It.IsAny<CreateBulkImportOperationListCommand>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    }
    public class CreateBulkImportComponentInfraObjectCommand ();
}
