using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class BulkImportOperationFixture : IDisposable
{
    public List<BulkImportOperation> BulkImportOperationPaginationList { get; set; }
    public List<BulkImportOperation> BulkImportOperationList { get; set; }
    public BulkImportOperation BulkImportOperationDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public BulkImportOperationFixture()
    {
        var fixture = new Fixture();

        BulkImportOperationList = fixture.Create<List<BulkImportOperation>>();

        BulkImportOperationPaginationList = fixture.CreateMany<BulkImportOperation>(20).ToList();

        BulkImportOperationPaginationList.ForEach(x => x.CompanyId = CompanyId);

        BulkImportOperationList.ForEach(x => x.CompanyId = CompanyId);

        BulkImportOperationDto = fixture.Create<BulkImportOperation>();

        BulkImportOperationDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
