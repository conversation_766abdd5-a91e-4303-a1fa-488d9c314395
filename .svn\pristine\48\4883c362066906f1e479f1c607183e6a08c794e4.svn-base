﻿using ContinuityPatrol.Application.Features.DRCalendar.Commands.Create;

namespace ContinuityPatrol.Application.Features.DRCalendar.Commands.Update;

public class UpdateDrCalenderCommandValidator : AbstractValidator<UpdateDrCalenderCommand>
{
    private readonly IDrCalenderRepository _drCalenderRepository;
    private readonly IGlobalSettingRepository _globalSettingRepository;

    public UpdateDrCalenderCommandValidator(IDrCalenderRepository drCalenderRepository,
        IGlobalSettingRepository globalSettingRepository)
    {
        _drCalenderRepository = drCalenderRepository;
        _globalSettingRepository = globalSettingRepository;

        RuleFor(e => e)
            .MustAsync(IsPermissionAllowedInGlobalSetting)
            .WithMessage("The 'dr calendar' feature is not enabled in the Global Settings.");


        //RuleFor(p => p)
        //     .Must(IsValidGUID)
        //     .WithMessage("Invalid Id");

        //RuleFor(p => p.ActivityName)
        //    .NotEmpty().WithMessage("{PropertyName} is required.")
        //    .NotNull()
        //    .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
        //    .WithMessage("Please Enter Valid {PropertyName}")
        //    .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        //RuleFor(p => p.ActivityType)
        //    .NotEmpty().WithMessage("Select {PropertyName}..")
        //    .NotNull();

        //RuleFor(p => p.ActivityStatus)
        //    .NotEmpty().WithMessage("Select {PropertyName}.")
        //    .NotNull()
        //    .Must(p => (!string.IsNullOrWhiteSpace(p) && p.ToLower().Equals("initiated")) || (!string.IsNullOrWhiteSpace(p) && p.ToLower().Equals("scheduled")))
        //    .WithMessage("Please enter valid {PropertyName}.");


        //RuleFor(p => p.Responsibility)
        //    .NotEmpty().WithMessage("Select {PropertyName}..")
        //    .NotNull();
        //RuleFor(p => p.Recipient2)
        //    .NotEmpty().WithMessage("Select {PropertyName}..")
        //    .NotNull();

        //RuleFor(p => p.SetReminders)
        //    .NotEmpty().WithMessage("Select {PropertyName}..")
        //    .NotNull()
        ////    .Must(p => (!string.IsNullOrWhiteSpace(p) && p.ToLower().Equals("initiated")) || (!string.IsNullOrWhiteSpace(p) && p.ToLower().Equals("scheduled"))
        ////    || (!string.IsNullOrWhiteSpace(p) && p.ToLower().Equals("scheduled"))).WithMessage("Please enter valid {PropertyName}.") ;
        RuleFor(p => p.WorkflowProfiles)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull();

        //RuleFor(p => p.ActivityDetails)
        //    .NotEmpty().WithMessage("Select {PropertyName}..")
        //    .NotNull();


        RuleFor(e => e)
            .MustAsync(IsActivityNameExist)
            .WithMessage("An activity with the same scheduled start time already exists.");
    }

    private async Task<bool> IsActivityNameExist(UpdateDrCalenderCommand updateDRCalenderCommand,
        CancellationToken token)
    {
        return !await _drCalenderRepository.IsActivityNameExist(updateDRCalenderCommand.ActivityName, updateDRCalenderCommand.Id, updateDRCalenderCommand.ScheduledStartDate);
        
    }

    private bool IsValidGUID(UpdateDrCalenderCommand updateDRCalenderCommand)
    {
        Guard.Against.InvalidGuidOrEmpty(updateDRCalenderCommand.BusinessServiceId, "Operational service id");
        Guard.Against.InvalidGuidOrEmpty(updateDRCalenderCommand.CompanyId, "Company id");
        Guard.Against.InvalidGuidOrEmpty(updateDRCalenderCommand.Responsibility, "Responsibility");
        Guard.Against.InvalidGuidOrEmpty(updateDRCalenderCommand.Id, "id");
        return true;
    }

    private async Task<bool> IsPermissionAllowedInGlobalSetting(UpdateDrCalenderCommand createDrCalenderCommand,
        CancellationToken token)
    {
        var globalSetting = await _globalSettingRepository.GlobalSettingBySettingKey("DR Calendar");

        return globalSetting.GlobalSettingValue.Equals("true");
    }
}