﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class DatabaseViewRepository:BaseRepository<DatabaseView>, IDatabaseViewRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IInfraObjectRepository _infraObjectRepository;
    public DatabaseViewRepository(ApplicationDbContext dbContext,ILoggedInUserService loggedInUserService, IInfraObjectRepository infraObjectRepository) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
        _infraObjectRepository = infraObjectRepository;

    }
    public override async Task<IReadOnlyList<DatabaseView>> ListAllAsync()
    {
        var databases = SelectDatabase(base.QueryAll(database =>
            database.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await databases.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(databases);
    }

    public async Task<int> DatabaseCountAsync()
    {
        var databases = SelectDatabase(base.QueryAll(database =>
            database.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await databases.CountAsync()
            : GetAssignedBusinessServicesByDatabases(databases).Count;
    }

    public async Task<IReadOnlyList<DatabaseView>> GetDatabaseList()
    {
        var databases = base.QueryAll(database =>
           database.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x=> new DatabaseView { DatabaseType= x.DatabaseType, DatabaseTypeId=x.DatabaseTypeId, Properties=x.Properties,BusinessServiceId = x.BusinessServiceId});

        return _loggedInUserService.IsAllInfra
            ? await databases.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(databases);
    }
    public async Task<List<DatabaseView>> GetDatabaseByDatabaseTypeId(string databaseTypeId)
    {
        var database = SelectDatabase(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.DatabaseTypeId.Equals(databaseTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.DatabaseTypeId.Equals(databaseTypeId)));

        return _loggedInUserService.IsAllInfra
            ? await database.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(database).ToList();
    }
    public async Task<List<DatabaseView>> GetByDatabaseIdsAsync(List<string> ids)
    {
        return await (_loggedInUserService.IsParent
            ? base.FilterBy(x => ids.Contains(x.ReferenceId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && ids.Contains(x.ReferenceId)))
            .Select(x => new DatabaseView
            {
                ReferenceId=x.ReferenceId,
                Name=x.Name,
                ModeType=x.ModeType,
                ServerId = x.ServerId,
                ServerName=x.ServerName,
                SID =x.SID.IsNullOrWhiteSpace() ? x.InstanceName : x.SID,
                DatabaseType=x.DatabaseType,
                Type=x.Type,Version=x.Version,
                ExceptionMessage=x.ExceptionMessage

            }).ToListAsync();       
    }

    public async Task<List<DatabaseView>> GetAllByDatabaseIdsAsync(List<string> ids)
    {
        return await (_loggedInUserService.IsParent
                ? base.FilterBy(x => ids.Contains(x.ReferenceId))
                : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && ids.Contains(x.ReferenceId)))
            .ToListAsync();
    }


    public async Task<List<DatabaseView>> GetByUserName(string userName)
    {
        var database = SelectDatabase(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.UserName.ToLower().Equals(userName))
            : base.FilterBy(x => x.UserName.ToLower().Equals(userName) && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await database.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(database).ToList();
    }

    public async Task<List<DatabaseView>> GetByUserNameAndDatabaseType(string userName, string databaseTypeId)
    {
        var splitDatabaseTypeId = databaseTypeId.Split(',');

        var database = SelectDatabase(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.UserName.Equals(userName) && splitDatabaseTypeId.Contains(x.DatabaseTypeId))
            : base.FilterBy(x => x.UserName.Equals(userName) && splitDatabaseTypeId.Contains(x.DatabaseTypeId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await database.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(database).ToList();
    }


    public async Task<List<DatabaseView>> GetDatabaseNames()
    {
        var databases = base
            .QueryAll(database => database.CompanyId.Equals(_loggedInUserService.CompanyId));

        var database = _loggedInUserService.IsAllInfra
            ? await databases.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(databases);

        return database.Select(x => new DatabaseView { ReferenceId = x.ReferenceId, Name = x.Name }).ToList();
    }
    public async Task<List<DatabaseView>> GetDatabaseByServerIds(List<string> ids)
    {
        var serverIds = ids
        .SelectMany(id => id.Split(",", StringSplitOptions.RemoveEmptyEntries))
        .Select(id => id.Trim())      
        .ToList();

        var database = SelectDatabase(_loggedInUserService.IsParent
            ? base.FilterBy(x => serverIds.Contains(x.ServerId))
            : base.FilterBy(x => serverIds.Contains(x.ServerId) && _loggedInUserService.CompanyId.Equals(x.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await database.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(database).ToList();
    }
    public async Task<List<DatabaseView>> GetDatabaseByServerId(string serverId)
    {
        var serverIds = serverId.Split(",", StringSplitOptions.RemoveEmptyEntries);

        var database = SelectDatabase(_loggedInUserService.IsParent
            ? base.FilterBy(x=> serverIds.Contains(x.ServerId))
            : base.FilterBy(x=> serverIds.Contains(x.ServerId) && _loggedInUserService.CompanyId.Equals(x.CompanyId)));
      
        return _loggedInUserService.IsAllInfra
            ? await database.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(database).ToList();
    }
    public async Task<List<DatabaseView>> GetDatabaseListByLicenseKey(string licenseId)
    {
        var databases = SelectDatabase(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.LicenseId.Equals(licenseId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.LicenseId.Equals(licenseId)));

        return _loggedInUserService.IsAllInfra
            ? await databases.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(databases).ToList();
    }

    public async Task<List<DatabaseView>> GetByDatabaseTypeIdAndFormVersion(string databaseTypeId, string formVersion)
    {
        var databases = SelectDatabase(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.DatabaseTypeId.Equals(databaseTypeId) && x.FormVersion.Equals(formVersion))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.DatabaseTypeId.Equals(databaseTypeId) && x.FormVersion.Equals(formVersion)));

       
        return _loggedInUserService.IsAllInfra
            ? await databases.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(databases).ToList();
    }
    public async Task<List<DatabaseView>> GetDatabaseType(string type)
    {
        var databases = SelectDatabase(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.Type.Equals(type))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.Type.Equals(type)));

              return _loggedInUserService.IsAllInfra
            ? await databases.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(databases).ToList();
    }

    public async Task<List<DatabaseView>> GetDatabaseByBusinessServiceId(string businessServiceId)
    {
        var database = SelectDatabase(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.BusinessServiceId.Equals(businessServiceId)));

        return _loggedInUserService.IsAllInfra
            ? await database.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(database).ToList();
    }
    public async Task<List<DatabaseView>> GetDatabaseByNodeId(string nodeId)
    {
        var database = SelectDatabase(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.Properties.Equals(nodeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.Properties.Equals(nodeId)));

        
        return _loggedInUserService.IsAllInfra
            ? await database.ToListAsync()
            : GetAssignedBusinessServicesByDatabases(database).ToList();
    }
    public async Task<PaginatedResult<DatabaseView>>GetDatabaseByType(int pageNumber,int pageSize,Specification<DatabaseView> specification,string databaseTypeId,string sortColumn,string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        { 
            return await SelectDatabase(_loggedInUserService.IsAllInfra
            ? Entities.Specify(specification).Where(x => x.DatabaseTypeId.Equals(databaseTypeId)).DescOrderById()
            : GetPaginatedAssignedBusinessServicesByDatabases(Entities.Specify(specification).Where(x=> x.DatabaseTypeId.Equals(databaseTypeId)).DescOrderById()))
                .ToSortedPaginatedListAsync(pageNumber,pageSize,sortColumn,sortOrder);
        }

        return await SelectDatabase(_loggedInUserService.IsAllInfra
           ? Entities.Specify(specification).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.DatabaseTypeId.Equals(databaseTypeId)).DescOrderById()
           : GetPaginatedAssignedBusinessServicesByDatabases(Entities.Specify(specification).Where(x =>x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.DatabaseTypeId.Equals(databaseTypeId)).DescOrderById()))
            .ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
    }


    public IQueryable<DatabaseView> GetDatabaseByType(string databaseTypeId)
    {
        var databases = SelectDatabase(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.DatabaseTypeId.Equals(databaseTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.DatabaseTypeId.Equals(databaseTypeId)));

        return _loggedInUserService.IsAllInfra
            ? databases.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedAssignedBusinessServicesByDatabases(databases).AsNoTracking().OrderByDescending(x => x.Id);
    }

    private IQueryable<DatabaseView> GetPaginatedAssignedBusinessServicesByDatabases(IQueryable<DatabaseView> businessServices)
    {
        var assignedServiceIds =  AssignedEntity.AssignedBusinessServices.Select(s => s.Id);

        businessServices = businessServices.Where(s => assignedServiceIds.Contains(s.BusinessServiceId));

        var infraObjects = _infraObjectRepository.GetPaginatedQuery();


        businessServices = businessServices.Where(database => infraObjects.Any(x =>x.DatabaseProperties.Contains(database.ReferenceId)));
        //businessServices = businessServices.Where(server => infraObjects.Any(x =>
        //    server.ReferenceId.Equals(x.PRDatabaseId) ||
        //    server.ReferenceId.Equals(x.DRDatabaseId) ||
        //    server.ReferenceId.Equals(x.NearDRDatabaseId)));

        return businessServices;
    }
    public override async Task<PaginatedResult<DatabaseView>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<DatabaseView> specification, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await SelectDatabase(_loggedInUserService.IsAllInfra
            ? Entities.Specify(specification).DescOrderById()
            : GetPaginatedAssignedBusinessServicesByDatabases(Entities.Specify(specification)).DescOrderById())
                .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        }

        return await SelectDatabase(_loggedInUserService.IsAllInfra
           ? Entities.Specify(specification).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()
           : GetPaginatedAssignedBusinessServicesByDatabases(Entities.Specify(specification).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()))
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }


    public override IQueryable<DatabaseView> GetPaginatedQuery()
    {
        var databases = base.QueryAll(database =>
            database.CompanyId.Equals(_loggedInUserService.CompanyId));

        return _loggedInUserService.IsAllInfra
            ? databases.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedAssignedBusinessServicesByDatabases(databases).AsNoTracking().OrderByDescending(x => x.Id);
    }

    private IReadOnlyList<DatabaseView> GetAssignedBusinessServicesByDatabases(IQueryable<DatabaseView> businessServices)
    {
        var databases = new List<DatabaseView>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                databases.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                                   where businessService.BusinessServiceId == assignedBusinessService.Id
                                   select businessService);

        var infraObjects = _infraObjectRepository.GetPaginatedQuery();

        databases = databases.Where(database => infraObjects.Any(x => x.DatabaseProperties.Contains(database.ReferenceId))).ToList();
        //databases = databases.Where(server => infraObjects.Any(x =>
        //    server.ReferenceId.Equals(x.PRDatabaseId) ||
        //    server.ReferenceId.Equals(x.DRDatabaseId) ||
        //    server.ReferenceId.Equals(x.NearDRDatabaseId))).ToList();

        return databases;
    }


    public async Task<List<DatabaseView>> GetDatabaseByDatabaseTypeIds(List<string> databaseTypeIds)
    {
        var databases = (_loggedInUserService.IsParent
            ? base.FilterBy(x => databaseTypeIds.Contains(x.DatabaseTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && databaseTypeIds.Contains(x.DatabaseTypeId)))
            .Select(x => new DatabaseView
            {
                Id = x.Id,
                ReferenceId = x.ReferenceId,
                DatabaseType = x.DatabaseType,
                DatabaseTypeId = x.DatabaseTypeId,
            });

        return await (_loggedInUserService.IsAllInfra
            ? databases.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedAssignedBusinessServicesByDatabases(databases).AsNoTracking().OrderByDescending(x => x.Id)).ToListAsync();
    }

    private static IQueryable<DatabaseView> SelectDatabase(IQueryable<DatabaseView> query)
    {
        return query.Select(x => new DatabaseView
        {
            Id=x.Id,
            ReferenceId = x.ReferenceId,
            Name = x.Name,
            DatabaseType = x.DatabaseType,
            DatabaseTypeId = x.DatabaseTypeId,
            Type = x.Type,
            CompanyId = x.CompanyId,
            ServerId = x.ServerId,
            ServerName = x.ServerName,
            Properties = x.Properties,
            ModeType = x.ModeType,
            LicenseId = x.LicenseId,
            LicenseKey = SecurityHelper.Decrypt(x.LicenseKey),
            BusinessServiceId = x.BusinessServiceId,
            BusinessServiceName = x.BusinessServiceName,
            Version = x.Version,
            ExceptionMessage = x.ExceptionMessage,
            FormVersion = x.FormVersion,
            SID = x.SID,
            InstanceName = x.InstanceName,
            Port = x.Port,
            UserName=x.UserName,
            AuthenticationMode=x.AuthenticationMode
        });
    }
}
