﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowActionResult.Events.Create;

public class WorkflowActionResultCreatedEventHandler : INotificationHandler<WorkflowActionResultCreatedEvent>
{
    private readonly ILogger<WorkflowActionResultCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowActionResultCreatedEventHandler(IUserActivityRepository userActivityRepository,
        ILogger<WorkflowActionResultCreatedEventHandler> logger, ILoggedInUserService userService)
    {
        _userActivityRepository = userActivityRepository;
        _logger = logger;
        _userService = userService;
    }

    public async Task Handle(WorkflowActionResultCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Workflow Action Result '{createdEvent.WorkflowActionName}' Created successfully.");

        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} {Modules.WorkflowActionResult}",
            Entity = Modules.WorkflowActionResult.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Workflow Action Result '{createdEvent.WorkflowActionName}' Created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);
    }
}