using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.PageWidgetModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.PageWidget.Queries.GetPaginatedList;

public class
    GetPageWidgetPaginatedListQueryHandler : IRequestHandler<GetPageWidgetPaginatedListQuery,
        PaginatedResult<PageWidgetListVm>>
{
    private readonly IMapper _mapper;
    private readonly IPageWidgetRepository _pageWidgetRepository;

    public GetPageWidgetPaginatedListQueryHandler(IMapper mapper, IPageWidgetRepository pageWidgetRepository)
    {
        _mapper = mapper;
        _pageWidgetRepository = pageWidgetRepository;
    }

    public async Task<PaginatedResult<PageWidgetListVm>> Handle(GetPageWidgetPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new PageWidgetFilterSpecification(request.SearchString);

        var queryable =await _pageWidgetRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

         var pageWidgetList = _mapper.Map<PaginatedResult<PageWidgetListVm>>(queryable);

        return pageWidgetList;
    }
}