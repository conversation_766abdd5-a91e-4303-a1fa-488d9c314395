﻿
@{
    ViewData["Title"] = "List";
	Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/lib/jquery.steps/jquery.steps.css" rel="stylesheet" />
<link href="~/css/wizard.css" rel="stylesheet" />
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title" title="Credential Profile">
                <i class="cp-credential-profile"></i><span>  Credential Profile </span>
            </h6>
            <form class="d-flex">
                <div class="input-group ">
                    <div class="input-group me-2" style="width: 350px !important;">
                    <span class="input-group-text"><i class="cp-activity-type"></i></span>
                        <select class="form-select w-100" data-placeholder="Select Type" title="Select Type">
                        <option></option>
                        <option value="all">All</option>
                        <option value="Node1">Node1</option>
                        <option value="Node2">Node2</option>
                        <option value="Node3">Node3</option>
                        <option value="Node4">Node4</option>
                        <option value="Node5">Node5</option>
                    </select>
                    </div>
                </div>
                <div class="input-group mx-2 w-100">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown"  title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="" id="CompanyName">
                                        <label class="form-check-label" for="CompanyName">
                                            Site
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <button type="button" class="btn btn-primary btn-sm " title="Create" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="example" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Name</th>
                        <th>Server</th>
                        <th>Oracle SID</th>
                        <th class="Action-th">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>ND_VJ_RAC_DR2</td>
                        <td>VJ_RAC_DR2</td>
                        <td>DEV19C1</td>
                        <td class="Action-th">
                            <div class="dropdown">
                                <i class="cp-horizontal-dots me-2" role="button" data-bs-toggle="dropdown"></i>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#"><i class="cp-edit me-2"></i>Edit</a></li>
                                    <li><a role="button" data-bs-toggle="modal" data-bs-target="#DeleteModal" class="dropdown-item" href="#"><i class="cp-Delete me-2"></i>Delete</a></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!--Modal Create-->
<div class="modal fade " id="CreateModal" data-bs-backdrop="static" aria-labelledby="configureModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<!--Modal Delete-->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/delete.png" />
            </div>
            <div class="modal-body text-center pt-0">
                <h4>Are you sure?</h4>
                <p>You want to delete <span class="font-weight-bolder text-primary">Congizant Techno Services</span> data?</p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" title="No" data-bs-dismiss="modal">No</button>
                <button type="button" class="btn btn-primary btn-sm" title="Yes">Yes</button>
            </div>
        </div>
    </div>
</div>

<script src="~/js/wizard.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>




