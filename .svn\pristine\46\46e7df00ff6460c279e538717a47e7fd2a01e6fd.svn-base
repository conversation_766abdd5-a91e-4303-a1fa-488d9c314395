﻿function monitorTypeHyperV(value, infraObjectName, moniterType, parsedData) {
    if (moniterType === "HyperV") {
        
        let application = value?.monitorServiceDetails[0]?.isServiceUpdate?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.isServiceUpdate?.toLowerCase()?.includes("running") ? "text-success cp-reload cp-animate" : "text-danger cp-fail-back";
        let workflow = value?.monitorServiceDetails[0]?.workflowName?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.workflowName ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
        let pripaddress = value?.prServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : value?.prServerStatus?.toLowerCase() === "pending" ? "cp-pending me-1 text-warning" : value?.prServerStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
        let dripaddress = value?.drServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending me-1 text-warning" : value?.drServerStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
        let prServericon = parsedData?.PR_HyperVReplicationMonitoring?.PR_Server_Name ? "cp-server me-1 text-primary" : "cp-disable me-1 text-danger";
        let drServericon = parsedData?.DR_HyperVReplicationMonitoring?.DR_Server_Name ? "cp-server me-1 text-primary" : "cp-disable me-1 text-danger";
        let prVmName = parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_Name ? "cp-name me-1 text-primary" : "cp-disable me-1 text-danger";
        let drVmName = parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_Name ? "cp-name me-1 text-primary" : "cp-disable me-1 text-danger";
        let prVmNetworkStatus = parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_Networking_Status?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-success me-1 text-success";
        let drVmNetworkStatus = parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_Networking_Status?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-success me-1 text-success";
        let prreplicationtype = value?.replicationType?.includes("NA") ? "cp-disable me-1 text-danger" : value?.replicationType ? "cp-replication-type me-1 text-primary" : "cp-disable me-1 text-danger";
        let prReplicaState = parsedData?.PR_HyperVReplicationMonitoring?.PR_Replication_State?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-refresh me-1 text-primary";
        let drReplicaState = parsedData?.DR_HyperVReplicationMonitoring?.DR_Replication_State?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-refresh me-1 text-primary";
        let prReplicaMode = parsedData?.PR_HyperVReplicationMonitoring?.PR_Replication_Mode?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-refresh me-1 text-success";
        let drReplicaMode = parsedData?.DR_HyperVReplicationMonitoring?.DR_Replication_Mode?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-refresh me-1 text-success";
        let prprimaryServer = parsedData?.PR_HyperVReplicationMonitoring?.PR_Current_Primary_Server?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-manage-server me-1 text-primary";
        let drprimaryServer = parsedData?.DR_HyperVReplicationMonitoring?.DR_Current_Primary_Server?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-manage-server me-1 text-primary";
        let prreplicaServer = parsedData?.PR_HyperVReplicationMonitoring?.PR_Current_Replication_Server?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-manage-server me-1 text-primary";
        let drreplicaServer = parsedData?.DR_HyperVReplicationMonitoring?.DR_Current_Replication_Server?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-manage-server me-1 text-primary";
        let prhealth = parsedData?.PR_HyperVReplicationMonitoring?.PR_Replication_Health?.toLowerCase();
        let drhealth = parsedData?.DR_HyperVReplicationMonitoring?.DR_Replication_Health?.toLowerCase();


        let prhealthState;
        let drhealthState;
        if (prhealth === "normal" || drhealth==="normal") {
            prhealthState = "cp-health-success me-1 text-success";
            drhealthState = "cp-health-success me-1 text-success";
        }
        else if (prhealth === "warning" || drhealth === "warning") {
            prhealthState = "cp-health-warning me-1 text-warning";
            drhealthState = "cp-health-warning me-1 text-warning";
        }
        else if (prhealth === "critical") {
            prhealthState = "cp-health-error me-1 text-danger";
            drhealthState = "cp-health-error me-1 text-danger";
        }
        else {
            prhealthState = "cp-disable me-1 text-danger";
            drhealthState = "cp-disable me-1 text-danger";
        }
        
        let prdatalag = parsedData?.PR_Datalag?.includes("NA") ? "cp-disable me-1 text-danger" : "cp-time me-1 text-primary";
        let vmStatepr = parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_State?.toLowerCase();
        let prVmStateClass
        if (vmStatepr === "running" || vmStatepr === "saved" || vmStatepr === "starting") {
            prVmStateClass = "cp-reload cp-animate me-1 text-success";
        } else if (vmStatepr === "off" || vmStatepr === "stopping" || vmStatepr === "paused" || vmStatepr === "reset") {
            prVmStateClass = "cp-end me-1 text-danger";
        } else {
            prVmStateClass = "cp-disable me-1 text-danger";
        }
        let vmStatedr = parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_State?.toLowerCase();
        let drVmStateClass
        if (vmStatedr === "running" || vmStatedr === "saved" || vmStatedr === "starting") {
            prVmStateClass = "cp-reload cp-animate me-1 text-success";
        } else if (vmStatedr === "off" || vmStatedr === "stopping" || vmStatedr === "paused" || vmStatedr === "reset") {
            drVmStateClass = "cp-end me-1 text-danger";
        } else {
            drVmStateClass = "cp-disable me-1 text-danger";
        }
        let infraobjectdata =
            '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
            '<thead style="position: sticky;top: 0px;">' +
            '<tr>' +
            '<th>Hyper-V VM Summary</th>' +
            '<th>Production Server</th>' +
            '<th>DR Server</th>' +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + "Server Name" + '</td>' +
            '<td>' + '<i class="' + prServericon + '"></i>'+ (parsedData?.PR_HyperVReplicationMonitoring?.PR_Server_Name !== null && parsedData?.PR_HyperVReplicationMonitoring?.PR_Server_Name !== "" ? parsedData?.PR_HyperVReplicationMonitoring?.PR_Server_Name : 'NA') + '</td>' +
            '<td>' + '<i class="' + drServericon + '"></i>' +(parsedData?.DR_HyperVReplicationMonitoring?.DR_Server_Name !== null && parsedData?.DR_HyperVReplicationMonitoring?.DR_Server_Name !== "" ? parsedData?.DR_HyperVReplicationMonitoring?.DR_Server_Name : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + 'Hyper-V Host IP Address/HostName' + '</td>' +
            '<td>' + '<i class="' + pripaddress + '"></i>' + (parsedData?.PR_HyperVReplicationMonitoring?.PR_Hyper_V_Host_IP_Address !== null && parsedData?.PR_HyperVReplicationMonitoring?.PR_Hyper_V_Host_IP_Address !== "" ? parsedData?.PR_HyperVReplicationMonitoring?.PR_Hyper_V_Host_IP_Address : 'NA') + '</td>' +
            '<td>' + '<i class="' + dripaddress + '"></i>' + (parsedData?.DR_HyperVReplicationMonitoring?.DR_Hyper_V_Host_IP_Address !== null && parsedData?.DR_HyperVReplicationMonitoring?.DR_Hyper_V_Host_IP_Address !== "" ? parsedData?.DR_HyperVReplicationMonitoring?.DR_Hyper_V_Host_IP_Address : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + 'VM Name' + '</td>' +
            '<td>' + '<i class="' + prVmName + '"></i>' + (parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_Name !== null && parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_Name !== "" ? parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_Name : 'NA') + '</td>' +
            '<td>' + '<i class="' + drVmName + '"></i>' + (parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_Name !== null && parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_Name !== "" ? parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_Name : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "VM State" + '</td>' +
            '<td>' + '<i class="' + prVmStateClass + '"></i>' + (parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_State !== null && parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_State !== "" ? parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_State : 'NA') + '</td>' +
            '<td>' + '<i class="' + drVmStateClass + '"></i>' + (parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_State !== null && parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_State !== "" ? parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_State : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "VM IP Address/HostName" + '</td>' +
            '<td>' + '<i class="' + pripaddress + '"></i>' + (parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_IP_Addresses !== null && parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_IP_Addresses !== "" ? parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_IP_Addresses : 'NA') + '</td>' +
            '<td>' + '<i class="' + dripaddress + '"></i>' + (parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_IP_Addresses !== null && parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_IP_Addresses !== "" ? parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_IP_Addresses : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "VM Networking Status" + '</td>' +
            '<td>' + '<i class="' + prVmNetworkStatus + '"></i>' + (parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_Networking_Status !== null && parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_Networking_Status !== "" ? parsedData?.PR_HyperVReplicationMonitoring?.PR_VM_Networking_Status : 'NA') + '</td>' +
            '<td>' + '<i class="' + drVmNetworkStatus + '"></i>' + (parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_Networking_Status !== null && parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_Networking_Status !== "" ? parsedData?.DR_HyperVReplicationMonitoring?.DR_VM_Networking_Status : 'NA') + '</td>' +
            '</tr>';
        infraobjectdata += generateMonitorServiceDetailsRow(workflow, application, value?.monitorServiceDetails);

        infraobjectdata += '</tbody>' +
            '</table>' +
            '</div>' +
            '<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
            '<thead style="position: sticky;top: 0px;z-index: 1;">' +
            '<tr>' +
            '<th>VM Replication Health Status</th>' +
            '<th>Production Server</th>' +
            '<th>DR Server</th>' +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + "Replication Type" + '</td>' +
            '<td>' + '<i class="' + prreplicationtype + '"></i>' +(value?.replicationType !== null && value?.replicationType !== "" ? value?.replicationType : 'NA') + '</td>' +
            
            '</tr>' +
            '<tr>' +
            '<td>' + "Replication State" + '</td>' +
            '<td>' + '<i class="' + prReplicaState +'"></i>' + (parsedData?.PR_HyperVReplicationMonitoring?.PR_Replication_State !== null && parsedData?.PR_HyperVReplicationMonitoring?.PR_Replication_State !== "" ? parsedData?.PR_HyperVReplicationMonitoring?.PR_Replication_State : 'NA') + '</td>' +
            '<td>' + '<i class="' + drReplicaState +'"></i>' + (parsedData?.DR_HyperVReplicationMonitoring?.DR_Replication_State !== null && parsedData?.DR_HyperVReplicationMonitoring?.DR_Replication_State !== "" ? parsedData?.DR_HyperVReplicationMonitoring?.DR_Replication_State : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "Replication Mode" + '</td>' +
            '<td>' + '<i class="' + prReplicaMode + '"></i>' + (parsedData?.PR_HyperVReplicationMonitoring?.PR_Replication_Mode !== null && parsedData?.PR_HyperVReplicationMonitoring?.PR_Replication_Mode !== "" ? parsedData?.PR_HyperVReplicationMonitoring?.PR_Replication_Mode : 'NA') + '</td>' +
            '<td>' + '<i class="' + drReplicaMode + '"></i>' + (parsedData?.DR_HyperVReplicationMonitoring?.DR_Replication_Mode !== null && parsedData?.DR_HyperVReplicationMonitoring?.DR_Replication_Mode !== "" ? parsedData?.DR_HyperVReplicationMonitoring?.DR_Replication_Mode : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "Current Primary Server" + '</td>' +
            '<td>' + '<i class="' + prprimaryServer + '"></i>' + (parsedData?.PR_HyperVReplicationMonitoring?.PR_Current_Primary_Server !== null && parsedData?.PR_HyperVReplicationMonitoring?.PR_Current_Primary_Server !== "" ? parsedData?.PR_HyperVReplicationMonitoring?.PR_Current_Primary_Server : 'NA') + '</td>' +
            '<td>' + '<i class="' + drprimaryServer + '"></i>' + (parsedData?.DR_HyperVReplicationMonitoring?.DR_Current_Primary_Server !== null && parsedData?.DR_HyperVReplicationMonitoring?.DR_Current_Primary_Server !== "" ? parsedData?.DR_HyperVReplicationMonitoring?.DR_Current_Primary_Server : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "Current Replica Server" + '</td>' +
            '<td>' + '<i class="' + prreplicaServer + '"></i>' + (parsedData?.PR_HyperVReplicationMonitoring?.PR_Current_Replication_Server !== null && parsedData?.PR_HyperVReplicationMonitoring?.PR_Current_Replication_Server !== "" ? parsedData?.PR_HyperVReplicationMonitoring?.PR_Current_Replication_Server : 'NA') + '</td>' +
            '<td>' + '<i class="' + drreplicaServer + '"></i>' + (parsedData?.DR_HyperVReplicationMonitoring?.DR_Current_Replication_Server !== null && parsedData?.DR_HyperVReplicationMonitoring?.DR_Current_Replication_Server !== "" ? parsedData?.DR_HyperVReplicationMonitoring?.DR_Current_Replication_Server : 'NA') + '</td>' +
            '<tr>' +
            '<td>' + "Replication Health" + '</td>' +
            '<td>' + '<i class="' + prhealthState + '"></i>' + (parsedData?.PR_HyperVReplicationMonitoring?.PR_Replication_Health !== null && parsedData?.PR_HyperVReplicationMonitoring?.PR_Replication_Health !== "" ? parsedData?.PR_HyperVReplicationMonitoring?.PR_Replication_Health : 'NA') + '</td>' +
            '<td>' + '<i class="' + drhealthState + '"></i>' + (parsedData?.DR_HyperVReplicationMonitoring?.DR_Replication_Health !== null && parsedData?.DR_HyperVReplicationMonitoring?.DR_Replication_Health !== "" ? parsedData?.DR_HyperVReplicationMonitoring?.DR_Replication_Health : 'NA') + '</td>' +
            '</tr>' +
            '<tr>' +
            '<td>' + "Datalag(HH:MM:SS)" + '</td>' +
            '<td>' + '<i class="' + prdatalag  + '"></i>' + (parsedData?.PR_Datalag !== null && parsedData?.PR_Datalag !== "" ? parsedData?.PR_Datalag : 'NA') + '</td>' +           
            '</tr>' +
            '</tr>' +
            '</tbody style="">' +
            '</table>' +
            '</div>'



        setTimeout(() => {
            $("#infraobjectalldata").append(infraobjectdata);
        }, 200)


    }
    
}