namespace ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Create;

public class CreateBulkImportActionResultCommand : IRequest<CreateBulkImportActionResultResponse>
{
    public string CompanyId { get; set; }
    public string NodeId { get; set; }
    public string NodeName { get; set; }
    public string BulkImportOperationId { get; set; }
    public string BulkImportOperationGroupId { get; set; }
    public string EntityId { get; set; }
    public string EntityName { get; set; }
    public string EntityType { get; set; }
    public string Status { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string ErrorMessage { get; set; }
}