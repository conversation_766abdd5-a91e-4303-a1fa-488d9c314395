﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.BusinessService.Queries.GetPaginatedList;

public class GetBusinessServicePaginatedListQueryHandler : IRequestHandler<GetBusinessServicePaginatedListQuery,
    PaginatedResult<BusinessServiceListVm>>
{
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IMapper _mapper;

    public GetBusinessServicePaginatedListQueryHandler(IMapper mapper,
        IBusinessServiceRepository businessServiceRepository)
    {
        _mapper = mapper;
        _businessServiceRepository = businessServiceRepository;
    }

    public async Task<PaginatedResult<BusinessServiceListVm>> Handle(
        GetBusinessServicePaginatedListQuery request, CancellationToken cancellationToken)
    {
        var productFilterSpec1 = new BusinessServiceFilterSpecification(request.SearchString);

        var queryable = await _businessServiceRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec1, request.SortColumn, request.SortOrder);

        var businessServiceList = _mapper.Map<PaginatedResult<BusinessServiceListVm>>(queryable);

        return businessServiceList;

        //var queryable = _businessServiceRepository.GetPaginatedQuery();

        //var productFilterSpec = new BusinessServiceFilterSpecification(request.SearchString);

        //var businessServiceList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<BusinessServiceListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        ////await _publisher.Publish(new BusinessServicePaginatedEvent());
        //return businessServiceList;
    }
}