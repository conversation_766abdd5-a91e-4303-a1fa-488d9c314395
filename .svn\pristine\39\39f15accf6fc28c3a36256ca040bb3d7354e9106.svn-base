﻿using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetInfraObjectByWorkflowId;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowInfraObject.Queries
{
    public class GetInfraObjectByWorkflowIdQueryHandlerTests
    {
        private readonly Mock<IWorkflowInfraObjectRepository> _mockWorkflowInfraObjectRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetInfraObjectByWorkflowIdQueryHandler _handler;

        public GetInfraObjectByWorkflowIdQueryHandlerTests()
        {
            _mockWorkflowInfraObjectRepository = new Mock<IWorkflowInfraObjectRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetInfraObjectByWorkflowIdQueryHandler(_mockMapper.Object, _mockWorkflowInfraObjectRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedList_WhenDataIsFound()
        {
            var workflowId = Guid.NewGuid().ToString();

            var workflowInfraObject = new List<Domain.Entities.WorkflowInfraObject>
            {
                new Domain.Entities.WorkflowInfraObject
                {
                   InfraObjectId = Guid.NewGuid().ToString(),
                   WorkflowId = workflowId,
                   InfraObjectName = "Infra Object 1"
                }
            };

            var workflowInfraObjectVm = new List<GetInfraObjectByWorkflowIdVm>
            {
                new GetInfraObjectByWorkflowIdVm
                {
                    InfraObjectId = Guid.NewGuid().ToString(),
                    WorkflowId = workflowInfraObject[0].WorkflowId,
                    ActionType = "Custom",
                    InfraObjectName= "workflowInfraObject[0].Name",
                    WorkflowName = "workflowInfraObject[0].Name"
                }
            };
            _mockWorkflowInfraObjectRepository
                .Setup(repo => repo.GetInfraObjectFromWorkflowId(workflowId))
                .ReturnsAsync(workflowInfraObject);

            _mockMapper
                .Setup(m => m.Map<List<GetInfraObjectByWorkflowIdVm>>(It.IsAny<List<Domain.Entities.WorkflowInfraObject>>()))
                .Returns(workflowInfraObjectVm);

            var result = await _handler.Handle(new GetInfraObjectByWorkflowIdQuery { WorkflowId = workflowId }, CancellationToken.None);

            Assert.NotNull(result);
            Assert.IsType<List<GetInfraObjectByWorkflowIdVm>>(result);
            Assert.Equal(1, result.Count);
            Assert.Equal(workflowInfraObjectVm[0].WorkflowName, result[0].InfraObjectName);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoDataIsFound()
        {
            var workflowId = Guid.NewGuid().ToString();

            _mockWorkflowInfraObjectRepository
                .Setup(repo => repo.GetInfraObjectFromWorkflowId(workflowId))
                .ReturnsAsync(new List<Domain.Entities.WorkflowInfraObject>());

            _mockMapper
                .Setup(mapper => mapper.Map<List<GetInfraObjectByWorkflowIdVm>>(It.IsAny<List<Domain.Entities.WorkflowInfraObject>>()))
                .Returns(new List<GetInfraObjectByWorkflowIdVm>());

            var result = await _handler.Handle(new GetInfraObjectByWorkflowIdQuery { WorkflowId = workflowId }, CancellationToken.None);

            Assert.NotNull(result); 
            Assert.Empty(result);  
        }
    }
}
