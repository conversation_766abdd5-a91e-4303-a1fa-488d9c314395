using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Withdraw;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetByRequestId;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetList;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixRequestModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class ApprovalMatrixRequestsControllerTests : IClassFixture<ApprovalMatrixRequestFixture>
{
    private readonly ApprovalMatrixRequestFixture _approvalMatrixRequestFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly ApprovalMatrixRequestsController _controller;

    public ApprovalMatrixRequestsControllerTests(ApprovalMatrixRequestFixture approvalMatrixRequestFixture)
    {
        _approvalMatrixRequestFixture = approvalMatrixRequestFixture;

        var testBuilder = new ControllerTestBuilder<ApprovalMatrixRequestsController>();
        _controller = testBuilder.CreateController(
            _ => new ApprovalMatrixRequestsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetApprovalMatrixRequests_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixRequestListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_approvalMatrixRequestFixture.ApprovalMatrixRequestListVm);

        // Act
        var result = await _controller.GetApprovalMatrixRequests();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var requests = Assert.IsAssignableFrom<List<ApprovalMatrixRequestListVm>>(okResult.Value);
        Assert.Equal(3, requests.Count);
    }

    [Fact]
    public async Task GetApprovalMatrixRequests_ReturnsEmptyList_WhenNoRequestsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixRequestListQuery>(), default))
            .ReturnsAsync(new List<ApprovalMatrixRequestListVm>());

        // Act
        var result = await _controller.GetApprovalMatrixRequests();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var requests = Assert.IsAssignableFrom<List<ApprovalMatrixRequestListVm>>(okResult.Value);
        Assert.Empty(requests);
    }

    [Fact]
    public async Task GetApprovalMatrixRequestById_ReturnsRequest_WhenIdIsValid()
    {
        // Arrange
        var requestId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetApprovalMatrixRequestDetailQuery>(q => q.Id == requestId), default))
            .ReturnsAsync(_approvalMatrixRequestFixture.ApprovalMatrixRequestDetailVm);

        // Act
        var result = await _controller.GetApprovalMatrixRequestById(requestId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var request = Assert.IsType<ApprovalMatrixRequestDetailVm>(okResult.Value);
        Assert.NotNull(request);
    }

    [Fact]
    public async Task GetApprovalMatrixRequestById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetApprovalMatrixRequestById("invalid-guid"));
    }

    [Fact]
    public async Task CreateApprovalMatrixRequest_Returns201Created()
    {
        // Arrange
        var command = _approvalMatrixRequestFixture.CreateApprovalMatrixRequestCommand;
        var expectedMessage = $"ApprovalMatrixRequest '{command.ProcessName}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateApprovalMatrixRequestResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateApprovalMatrixRequest(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateApprovalMatrixRequestResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateApprovalMatrixRequest_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"ApprovalMatrixRequest '{_approvalMatrixRequestFixture.UpdateApprovalMatrixRequestCommand.ProcessName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateApprovalMatrixRequestCommand>(), default))
            .ReturnsAsync(new UpdateApprovalMatrixRequestResponse
            {
                Message = expectedMessage,
                Id = _approvalMatrixRequestFixture.UpdateApprovalMatrixRequestCommand.Id
            });

        // Act
        var result = await _controller.UpdateApprovalMatrixRequest(_approvalMatrixRequestFixture.UpdateApprovalMatrixRequestCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateApprovalMatrixRequestResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteApprovalMatrixRequest_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "ApprovalMatrixRequest 'Test Request' has been deleted successfully!.";
        var requestId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteApprovalMatrixRequestCommand>(c => c.Id == requestId), default))
            .ReturnsAsync(new DeleteApprovalMatrixRequestResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteApprovalMatrixRequest(requestId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteApprovalMatrixRequestResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetPaginatedApprovalMatrixRequests_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetApprovalMatrixRequestPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _approvalMatrixRequestFixture.ApprovalMatrixRequestListVm;
        var expectedPaginatedResult = PaginatedResult<ApprovalMatrixRequestListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixRequestPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedApprovalMatrixRequests(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<ApprovalMatrixRequestListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<ApprovalMatrixRequestListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task GetApprovalMatrixByRequestId_ReturnsRequests_WhenRequestIdIsValid()
    {
        // Arrange
        var requestId = "REQ_001";
        var expectedResult = _approvalMatrixRequestFixture.ApprovalMatrixByRequestIdVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetApprovalMatrixByRequestIdQuery>(q => q.RequestId == requestId), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetApprovalMatrixByRequestId(requestId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var requests = Assert.IsAssignableFrom<List<ApprovalMatrixByRequestIdVm>>(okResult.Value);
        Assert.Equal(2, requests.Count);
        Assert.All(requests, r => Assert.Equal(requestId, r.RequestId));
    }

    [Fact]
    public async Task GetApprovalMatrixRequests_CallsCorrectQuery()
    {
        // Arrange
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixRequestListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<ApprovalMatrixRequestListVm>());

        // Act
        await _controller.GetApprovalMatrixRequests();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateApprovalMatrixRequest_ValidatesRequiredFields()
    {
        // Arrange
        var command = new CreateApprovalMatrixRequestCommand
        {
            
            ProcessName = "Test Process",
            Description = "Test description"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("RequestId is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateApprovalMatrixRequest(command));
    }

    [Fact]
    public async Task UpdateApprovalMatrixRequest_ValidatesRequestExists()
    {
        // Arrange
        var command = new UpdateApprovalMatrixRequestCommand
        {
            Id = "REQ_NONEXISTENT",
            ProcessName = "Updated Process"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("ApprovalMatrixRequest not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateApprovalMatrixRequest(command));
    }

    [Fact]
    public async Task GetPaginatedApprovalMatrixRequests_HandlesFilteringByStatus()
    {
        // Arrange
        var query = new GetApprovalMatrixRequestPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var urgentRequests = new List<ApprovalMatrixRequestListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                RequestId = "REQ_URGENT_001",
                ProcessName = "Emergency Access Request",
                Status = "Urgent",
                UserName = "emergency.user",
                Approvers = "security.manager,ciso"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                RequestId = "REQ_URGENT_002",
                ProcessName = "Critical System Change",
                Status = "Urgent",
                UserName = "system.admin",
                Approvers = "change.manager,operations.director"
            }
        };

        var expectedPaginatedResult = PaginatedResult<ApprovalMatrixRequestListVm>.Success(
            data: urgentRequests,
            count: urgentRequests.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixRequestPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedApprovalMatrixRequests(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<ApprovalMatrixRequestListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<ApprovalMatrixRequestListVm>>(okResult.Value);

        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, request => Assert.Equal("Urgent", request.Status));
    }

    
    [Fact]
    public async Task GetApprovalMatrixByRequestId_HandlesEmptyResults()
    {
        // Arrange
        var requestId = "REQ_NONEXISTENT";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetApprovalMatrixByRequestIdQuery>(q => q.RequestId == requestId), default))
            .ReturnsAsync(new List<ApprovalMatrixByRequestIdVm>());

        // Act
        var result = await _controller.GetApprovalMatrixByRequestId(requestId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var requests = Assert.IsAssignableFrom<List<ApprovalMatrixByRequestIdVm>>(okResult.Value);
        Assert.Empty(requests);
    }

    [Fact]
    public async Task CreateApprovalMatrixRequest_HandlesComplexRequestWorkflow()
    {
        // Arrange
        var command = new CreateApprovalMatrixRequestCommand
        {
          
            
            ProcessName = "Multi-Stage Enterprise Approval Process",
            Description = "Complex enterprise-level approval requiring multiple departments and escalation procedures with detailed audit trail and compliance requirements",
            UserName = "enterprise.coordinator",
            Status = "Initiated",
            Approvers = "department.head,finance.director,legal.counsel,ceo"
        };

        var expectedMessage = $"ApprovalMatrixRequest '{command.ProcessName}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateApprovalMatrixRequestResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateApprovalMatrixRequest(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateApprovalMatrixRequestResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteApprovalMatrixRequest_VerifiesRequestIsDeactivated()
    {
        // Arrange
        var requestId = Guid.NewGuid().ToString();
        var expectedMessage = "ApprovalMatrixRequest 'Test Request' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteApprovalMatrixRequestCommand>(c => c.Id == requestId), default))
            .ReturnsAsync(new DeleteApprovalMatrixRequestResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteApprovalMatrixRequest(requestId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteApprovalMatrixRequestResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetApprovalMatrixRequests_HandlesEmptyDatabase()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixRequestListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<ApprovalMatrixRequestListVm>());

        // Act
        var result = await _controller.GetApprovalMatrixRequests();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var requests = Assert.IsAssignableFrom<List<ApprovalMatrixRequestListVm>>(okResult.Value);
        Assert.Empty(requests);
    }

    [Fact]
    public async Task WithdrawApprovalMatrixRequest_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _approvalMatrixRequestFixture.WithdrawApprovalMatrixRequestCommand;
        var expectedResponse = new WithdrawApprovalMatrixRequestResponse
        {
            Message = "Approval Matrix Request 'Test Process' has been withdraw successfully.",
            Id = command.Id
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.WithdrawApprovalMatrixRequest(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<WithdrawApprovalMatrixRequestResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, response.Message);
        Assert.Equal(expectedResponse.Id, response.Id);
    }

    [Fact]
    public async Task WithdrawApprovalMatrixRequest_WithInvalidId_ThrowsNotFoundException()
    {
        // Arrange
        var command = new WithdrawApprovalMatrixRequestCommand
        {
            Id = Guid.NewGuid().ToString(),
            RequestId = "REQ_INVALID",
            Status = "Withdraw"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("ApprovalMatrixRequest not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.WithdrawApprovalMatrixRequest(command));
    }

    [Fact]
    public async Task WithdrawApprovalMatrixRequest_UpdatesStatusToWithdraw()
    {
        // Arrange
        var command = new WithdrawApprovalMatrixRequestCommand
        {
            Id = Guid.NewGuid().ToString(),
            RequestId = "REQ_STATUS_TEST",
            Status = "Withdraw"
        };

        var expectedResponse = new WithdrawApprovalMatrixRequestResponse
        {
            Message = "Approval Matrix Request 'Status Test Process' has been withdraw successfully.",
            Id = command.Id
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<WithdrawApprovalMatrixRequestCommand>(c =>
                c.Status == "Withdraw"), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.WithdrawApprovalMatrixRequest(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<WithdrawApprovalMatrixRequestResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, response.Message);

        // Verify the mediator was called with the correct status
        _mediatorMock.Verify(m => m.Send(It.Is<WithdrawApprovalMatrixRequestCommand>(c =>
            c.Status == "Withdraw"), default), Times.Once);
    }

    [Fact]
    public async Task IsApprovalMatrixRequestNameExist_WithExistingName_ReturnsTrue()
    {
        // Arrange
        var requestName = "Existing Request Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetApprovalMatrixRequestNameUniqueQuery>(q =>
                q.Name == requestName && q.Id == id), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsApprovalMatrixRequestNameExist(requestName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsApprovalMatrixRequestNameExist_WithNewName_ReturnsFalse()
    {
        // Arrange
        var requestName = "New Request Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetApprovalMatrixRequestNameUniqueQuery>(q =>
                q.Name == requestName && q.Id == id), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsApprovalMatrixRequestNameExist(requestName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsApprovalMatrixRequestNameExist_WithNullOrEmptyName_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsApprovalMatrixRequestNameExist("", null));

        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsApprovalMatrixRequestNameExist("   ", null));

        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _controller.IsApprovalMatrixRequestNameExist(null!, null));
    }

    [Fact]
    public async Task IsApprovalMatrixRequestNameExist_WithNullId_WorksCorrectly()
    {
        // Arrange
        var requestName = "Test Request Name";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetApprovalMatrixRequestNameUniqueQuery>(q =>
                q.Name == requestName && q.Id == null), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsApprovalMatrixRequestNameExist(requestName, null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }
}
