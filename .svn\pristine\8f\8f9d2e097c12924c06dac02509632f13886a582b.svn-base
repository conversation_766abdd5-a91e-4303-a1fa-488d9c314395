using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Xunit;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowCategoryViewRepositoryTests : IClassFixture<WorkflowCategoryViewFixture>
    {
        private readonly WorkflowCategoryViewFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowCategoryViewRepository _repository;

        public WorkflowCategoryViewRepositoryTests(WorkflowCategoryViewFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _repository = new WorkflowCategoryViewRepository(_dbContext, DbContextFactory.GetMockUserService());
        }

        [Fact]
        public async Task GetAllWorkflowCategoriesAsync_ReturnsAllCategories()
        {
            await _dbContext.WorkflowCategoryViews.AddRangeAsync(_fixture.WorkflowCategoryViewList);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.GetAllWorkflowCategoriesAsync();

            Assert.Equal(_fixture.WorkflowCategoryViewList.Count, result.Count);
            Assert.All(result, x => Assert.Contains(_fixture.WorkflowCategoryViewList, y => y.ReferenceId == x.ReferenceId));
        }
    }
}