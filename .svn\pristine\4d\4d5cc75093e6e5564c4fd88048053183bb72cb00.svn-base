using ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Queries.GetList;
//using ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Queries.GetNameUnique;
//using ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowApprovalMappingModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Orchestration;

public class WorkflowApprovalMappingService : BaseService,IWorkflowApprovalMappingService
{
    public WorkflowApprovalMappingService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<WorkflowApprovalMappingListVm>> GetWorkflowApprovalMappingList()
    {
        Logger.LogInformation("Get All WorkflowApprovalMappings");

        return await Mediator.Send(new GetWorkflowApprovalMappingListQuery());
    }

    public async Task<WorkflowApprovalMappingDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "WorkflowApprovalMapping Id");

        Logger.LogInformation($"Get WorkflowApprovalMapping Detail by Id '{id}'");

        return await Mediator.Send(new GetWorkflowApprovalMappingDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateWorkflowApprovalMappingCommand createWorkflowApprovalMappingCommand)
    {
        Logger.LogInformation($"Create WorkflowApprovalMapping '{createWorkflowApprovalMappingCommand}'");

        return await Mediator.Send(createWorkflowApprovalMappingCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowApprovalMappingCommand updateWorkflowApprovalMappingCommand)
    {
        Logger.LogInformation($"Update WorkflowApprovalMapping '{updateWorkflowApprovalMappingCommand}'");

        return await Mediator.Send(updateWorkflowApprovalMappingCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "WorkflowApprovalMapping Id");

        Logger.LogInformation($"Delete WorkflowApprovalMapping Details by Id '{id}'");

        return await Mediator.Send(new DeleteWorkflowApprovalMappingCommand { Id = id });
    }
     #region NameExist
   // public async Task<bool> IsWorkflowApprovalMappingNameExist(string name, string? id)
   // {
   //     Guard.Against.NullOrWhiteSpace(name, "WorkflowApprovalMapping Name");
   //
   //     Logger.LogInformation($"Check Name Exists Detail by WorkflowApprovalMapping Name '{name}' and Id '{id}'");
   //
   //     return await Mediator.Send(new GetWorkflowApprovalMappingNameUniqueQuery { Name = name, Id = id });
   // }
    #endregion

     #region Paginated
    //public async Task<PaginatedResult<WorkflowApprovalMappingListVm>> GetPaginatedWorkflowApprovalMappings(GetWorkflowApprovalMappingPaginatedListQuery query)
    //{
    //    Logger.LogInformation("Get Searching Details in WorkflowApprovalMapping Paginated List");
    //
    //    return await Mediator.Send(query);
    //}
     #endregion
}
