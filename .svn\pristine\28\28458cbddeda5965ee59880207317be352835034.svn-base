using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardMapModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetPaginatedList;

public class GetDynamicDashboardMapPaginatedListQueryHandler : IRequestHandler<GetDynamicDashboardMapPaginatedListQuery,
    PaginatedResult<DynamicDashboardMapListVm>>
{
    private readonly IDynamicDashboardMapRepository _dynamicDashboardMapRepository;
    private readonly IMapper _mapper;

    public GetDynamicDashboardMapPaginatedListQueryHandler(IMapper mapper,
        IDynamicDashboardMapRepository dynamicDashboardMapRepository)
    {
        _mapper = mapper;
        _dynamicDashboardMapRepository = dynamicDashboardMapRepository;
    }

    public async Task<PaginatedResult<DynamicDashboardMapListVm>> Handle(
        GetDynamicDashboardMapPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DynamicDashboardMapFilterSpecification(request.SearchString);

        var queryable = await _dynamicDashboardMapRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var dynamicDashboardMapList = _mapper.Map<PaginatedResult<DynamicDashboardMapListVm>>(queryable);

        return dynamicDashboardMapList;
        
    }
}