namespace ContinuityPatrol.Domain.Entities;

public class BackUpLog : AuditableEntity
{
    public string HostName { get; set; }
    public string DatabaseName { get; set; }
    public string UserName { get; set; }
    public bool IsLocalServer { get; set; }
    public bool IsBackUpServer { get; set; }
    public string BackUpPath { get; set; }
    public string Type { get; set; }
    public string Status { get; set; }
}