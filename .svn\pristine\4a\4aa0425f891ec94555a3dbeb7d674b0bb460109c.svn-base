using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Create;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Update;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftImpactTypeMasterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Drift;

public class DriftImpactTypeMasterService : BaseClient, IDriftImpactTypeMasterService
{
    public DriftImpactTypeMasterService(IConfiguration config, IAppCache cache, ILogger<DriftImpactTypeMasterService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<DriftImpactTypeMasterListVm>> GetDriftImpactTypeMasterList()
    {
        var request = new RestRequest("api/v6/driftimpacttypemasters");

        return await GetFromCache<List<DriftImpactTypeMasterListVm>>(request, "GetDriftImpactTypeMasterList");
    }

    public async Task<BaseResponse> CreateAsync(CreateDriftImpactTypeMasterCommand createDriftImpacttypeMasterCommand)
    {
        var request = new RestRequest("api/v6/driftimpacttypemasters", Method.Post);

        request.AddJsonBody(createDriftImpacttypeMasterCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDriftImpactTypeMasterCommand updateDriftImpacttypeMasterCommand)
    {
        var request = new RestRequest("api/v6/driftimpacttypemasters", Method.Put);

        request.AddJsonBody(updateDriftImpacttypeMasterCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/driftimpacttypemasters/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<DriftImpactTypeMasterDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/driftimpacttypemasters/{id}");

        return await Get<DriftImpactTypeMasterDetailVm>(request);
    }
    #region NameExist
    public async Task<bool> IsDriftImpactTypeMasterNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/driftimpacttypemasters/name-exist?driftimpacttypemasterName={name}&id={id}");

        return await Get<bool>(request);
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<DriftImpactTypeMasterListVm>> GetPaginatedDriftImpactTypeMasters(GetDriftImpactTypeMasterPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/driftimpacttypemasters/paginated-list");

        return await Get<PaginatedResult<DriftImpactTypeMasterListVm>>(request);
    }
    #endregion
}
