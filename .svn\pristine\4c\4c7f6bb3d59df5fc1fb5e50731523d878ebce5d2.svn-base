﻿using DevExpress.XtraReports.UI;
using ContinuityPatrol.Web.Areas.Dashboard.Controllers;
using System.Drawing;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using Newtonsoft.Json;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using DocumentFormat.OpenXml.Drawing;
using Newtonsoft.Json.Linq;
using System.Text.Json.Nodes;
using DevExpress.XtraRichEdit.Import.Doc;
using ContinuityPatrol.Shared.Services.Helper;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class DatabaseComponentReport : DevExpress.XtraReports.UI.XtraReport
    {
        public string username;
        private readonly ILogger<DatabaseController> _logger; 
        public string ReportGeneratedName;
        bool IsNotNullOrEmpty(string value) => !string.IsNullOrEmpty(value);

        public DatabaseComponentReport(string dblist)
        {
            try
            {
                var DBcomponentreport = GetDBDetails(dblist);
                ReportGeneratedName = WebHelper.UserSession.LoginName;
                InitializeComponent();
                ClientCompanyLogo();
                this.DataSource = DBcomponentreport;
                tableCell8.BeforePrint += tableCell_SerialNumber_BeforePrint;                                 
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Database Component Report. The error message : " + ex.Message); throw; }
        }

        public List<DatabaseReportVm> GetDBDetails(string data)
        {
            try
            {
                var databaselist = JsonConvert.DeserializeObject<List<DatabaseListVm>>(data);
                var DBlistreport = databaselist;        
                var DBReportDetails = new List<DatabaseReportVm>();
                foreach (var Datalist in DBlistreport)
                {
                    var report = new DatabaseReportVm();
                    report.Name = Datalist.Name;
                    report.DBType = Datalist.DatabaseType;
                    JObject jsonObject = JObject.Parse(Datalist.Properties);
                    report.Sid = jsonObject.Properties()
                                .Where(p => p.Name.Contains("SID", StringComparison.OrdinalIgnoreCase))
                                .Select(p => p.Value.ToString())
                                .FirstOrDefault() ?? Datalist.SID;
                    report.Version = Datalist.Version;
                    report.Port = jsonObject.ContainsKey("Port")
                                 ? jsonObject["Port"]?.ToString() : Datalist.Port;
                    report.Status = Datalist.ModeType;
                    report.Type= Datalist.Type;
                    report.Server = Datalist.ServerName;
                    report.ErrorMessage = Datalist.ExceptionMessage;            
                    report.SSHUser = jsonObject.Properties()
                                .Where(p => p.Name.Contains("UserName", StringComparison.OrdinalIgnoreCase))
                                .Select(p => p.Value.ToString())
                                .FirstOrDefault();
                    if (Datalist.DatabaseType == "Oracle" && GetJsonValue("DatabaseAuthentication", jsonObject) == "True")
                    {
                        report.AuthenticationMode = GetJsonValue("Authentication", jsonObject);
                    }
                    else if (jsonObject.ContainsKey("AuthenticationMode"))
                    {
                        report.AuthenticationMode = GetJsonValue("AuthenticationMode", jsonObject);
                    }
                    else if (jsonObject.ContainsKey("AuthenticationType"))
                    {
                        report.AuthenticationMode = GetJsonValue("AuthenticationType", jsonObject);
                    }                  
                    DBReportDetails.Add(report);
                }
                return DBReportDetails;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while get parse data for the Database Component Report. The error message : " + ex.Message);
                throw;
            }
        }

        public string GetJsonValue(string path, JObject jsonObject)
        {
            var value = jsonObject.ContainsKey(path)
                     ? jsonObject[path]?.ToString() : default;
            return value;
        }
        private void xrPageInfo1_BeforePrint(object sender, CancelEventArgs e)
        {

        }
        private int serialNumber = 1;

        private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;
            cell.Text = serialNumber.ToString();
            serialNumber++;
        }
        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + ReportGeneratedName.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Database Component Report's User name. The error message : " + ex.Message); throw; }
        }

        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Database Component Report's CP Version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(DatabaseController.CompanyLogo) ? "NA" : DatabaseController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the customer logo in Database Component Report" + ex.Message.ToString());
            }
        }
    }
}
