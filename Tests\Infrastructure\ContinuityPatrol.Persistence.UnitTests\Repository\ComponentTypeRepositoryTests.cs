using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ComponentTypeRepositoryTests : IClassFixture<ComponentTypeFixture>
{
    private readonly ComponentTypeFixture _componentTypeFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ComponentTypeRepository _repository;

    public ComponentTypeRepositoryTests(ComponentTypeFixture componentTypeFixture)
    {
        _componentTypeFixture = componentTypeFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new ComponentTypeRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var componentType = _componentTypeFixture.ComponentTypeDto;

        // Act
        await _dbContext.ComponentTypes.AddAsync(componentType);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(componentType.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(componentType.ComponentName, result.ComponentName);
        Assert.Equal(componentType.FormTypeName, result.FormTypeName);
        Assert.Single(_dbContext.ComponentTypes);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var componentType = _componentTypeFixture.ComponentTypeDto;
        await _dbContext.ComponentTypes.AddAsync(componentType);
        await _dbContext.SaveChangesAsync();

        componentType.ComponentName = "UpdatedComponentName";
        componentType.FormTypeName = "UpdatedFormTypeName";
        componentType.Version = "2.0";

        // Act
        _dbContext.ComponentTypes.Update(componentType);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(componentType.ReferenceId);

        // Assert
        Assert.Equal("UpdatedComponentName", result.ComponentName);
        Assert.Equal("UpdatedFormTypeName", result.FormTypeName);
        Assert.Equal("2.0", result.Version);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var componentType = _componentTypeFixture.ComponentTypeDto;
        await _dbContext.ComponentTypes.AddAsync(componentType);
        await _dbContext.SaveChangesAsync();

        // Act
        componentType.IsActive = false;

        _dbContext.ComponentTypes.Update(componentType);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var componentType = _componentTypeFixture.ComponentTypeDto;
        await _dbContext.ComponentTypes.AddAsync(componentType);
        await _dbContext.SaveChangesAsync();
        var addedEntity = await _repository.GetByReferenceIdAsync(componentType.ReferenceId);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.ComponentName, result.ComponentName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var componentType = _componentTypeFixture.ComponentTypeDto;
        await _dbContext.ComponentTypes.AddAsync(componentType);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(componentType.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(componentType.ReferenceId, result.ReferenceId);
        Assert.Equal(componentType.ComponentName, result.ComponentName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var componentTypes = _componentTypeFixture.ComponentTypeList;
        await _repository.AddRangeAsync(componentTypes);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(componentTypes.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsFormNamesUnique Tests

    [Fact]
    public async Task IsFormNamesUnique_ShouldReturnMatchingNames()
    {
        // Arrange
        var componentTypes = new List<ComponentType>
        {
            new ComponentType 
            { 
                ComponentName = "Component1",
                FormTypeName = "FormType1",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ComponentType 
            { 
                ComponentName = "Component2",
                FormTypeName = "FormType2",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ComponentType 
            { 
                ComponentName = "Component3",
                FormTypeName = "FormType3",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(componentTypes);

        var searchNames = new List<string> { "Component1", "Component3", "NonExistentComponent" };

        // Act
        var result = await _repository.IsFormNamesUnique(searchNames);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains("Component1", result);
        Assert.Contains("Component3", result);
        Assert.DoesNotContain("NonExistentComponent", result);
    }

    [Fact]
    public async Task IsFormNamesUnique_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        var componentTypes = _componentTypeFixture.ComponentTypeList;
        await _repository.AddRangeAsync(componentTypes);

        var searchNames = new List<string> { "NonExistent1", "NonExistent2" };

        // Act
        var result = await _repository.IsFormNamesUnique(searchNames);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetComponentTypeByIds Tests

    [Fact]
    public async Task GetComponentTypeByIds_ShouldReturnMatchingComponentTypes()
    {
        // Arrange
        var componentTypeIds = new List<string> { "COMP_001", "COMP_002" };
        
        var componentTypes = new List<ComponentType>
        {
            new ComponentType 
            { 
                ComponentName = "Component1",
                ReferenceId = "COMP_001",
                IsActive = true
            },
            new ComponentType 
            { 
                ComponentName = "Component2",
                ReferenceId = "COMP_002",
                IsActive = true
            },
            new ComponentType 
            { 
                ComponentName = "Component3",
                ReferenceId = "COMP_003",
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(componentTypes);

        // Act
        var result = await _repository.GetComponentTypeByIds(componentTypeIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(x.ReferenceId, componentTypeIds));
    }

    [Fact]
    public async Task GetComponentTypeByIds_ShouldReturnEmpty_WhenNoMatch()
    {
        // Arrange
        var componentTypes = _componentTypeFixture.ComponentTypeList;
        await _repository.AddRangeAsync(componentTypes);

        var nonExistentIds = new List<string> { "NON_EXISTENT_001", "NON_EXISTENT_002" };

        // Act
        var result = await _repository.GetComponentTypeByIds(nonExistentIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetComponentTypeById Tests

    [Fact]
    public async Task GetComponentTypeById_ShouldReturnComponentType_WhenExists()
    {
        // Arrange
        var componentType = _componentTypeFixture.ComponentTypeDto;
        await _dbContext.ComponentTypes.AddAsync(componentType);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetComponentTypeById(componentType.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(componentType.ReferenceId, result.ReferenceId);
        Assert.Equal(componentType.ComponentName, result.ComponentName);
    }

    [Fact]
    public async Task GetComponentTypeById_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetComponentTypeById("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region IsComponentTypeNameExist Tests

    [Fact]
    public async Task IsComponentTypeNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var componentType = _componentTypeFixture.ComponentTypeDto;
        componentType.ComponentName = "ExistingComponentName";
        await _dbContext.ComponentTypes.AddAsync(componentType);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsComponentTypeNameExist("ExistingComponentName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsComponentTypeNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var componentTypes = _componentTypeFixture.ComponentTypeList;
        await _repository.AddRangeAsync(componentTypes);

        // Act
        var result = await _repository.IsComponentTypeNameExist("NonExistentComponentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsComponentTypeNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var componentType = _componentTypeFixture.ComponentTypeDto;
        componentType.ComponentName = "SameComponentName";
        await _dbContext.ComponentTypes.AddAsync(componentType);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsComponentTypeNameExist("SameComponentName", componentType.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var componentType = _componentTypeFixture.ComponentTypeList;
        var componentType1 = componentType[0];
        var componentType2 = componentType[1];
      
        componentType2.ComponentName = "DifferentComponentName";


        // Act
        await _dbContext.ComponentTypes.AddAsync(componentType1);
        await _dbContext.ComponentTypes.AddAsync(componentType2);
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync(); 

        // Assert
        Assert.Equal(2, results.Count);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.ComponentTypes.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var componentTypes = _componentTypeFixture.ComponentTypeList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(componentTypes);
        var initialCount = componentTypes.Count;

        var toUpdate = componentTypes.Take(2).ToList();
        toUpdate.ForEach(x => x.Version = "Updated");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = componentTypes.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Version == "Updated").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleComponentTypeFlags()
    {
        // Arrange
        var componentTypes = new List<ComponentType>
        {
            new ComponentType
            {
                ComponentName = "DatabaseComponent",
                IsDatabase = true,
                IsServer = false,
                IsReplication = false,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ComponentType
            {
                ComponentName = "ServerComponent",
                IsDatabase = false,
                IsServer = true,
                IsReplication = false,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new ComponentType
            {
                ComponentName = "ReplicationComponent",
                IsDatabase = false,
                IsServer = false,
                IsReplication = true,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(componentTypes);

        // Act
        var databaseComponents = await _repository.FindByFilterAsync(x => x.IsDatabase);
        var serverComponents = await _repository.FindByFilterAsync(x => x.IsServer);
        var replicationComponents = await _repository.FindByFilterAsync(x => x.IsReplication);

        // Assert
        Assert.Single(databaseComponents);
        Assert.Single(serverComponents);
        Assert.Single(replicationComponents);
        Assert.True(databaseComponents.First().IsDatabase);
        Assert.True(serverComponents.First().IsServer);
        Assert.True(replicationComponents.First().IsReplication);
    }

    #endregion
}
