﻿using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceEvaluationModel;
using ContinuityPatrol.Shared.Core.Extensions;
using System.Collections.Generic;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceEvaluation.Queries;

public class GetBusinessServiceEvaluationListQueryHandlerTests : IClassFixture<BusinessServiceEvaluationFixture>
{
    private readonly BusinessServiceEvaluationFixture _businessServiceEvaluationFixture;
    private Mock<IBusinessServiceEvaluationRepository> _mockBusinessServiceEvaluationRepository;
    private readonly GetBusinessServiceEvaluationListQueryHandler _handler;

    public GetBusinessServiceEvaluationListQueryHandlerTests(BusinessServiceEvaluationFixture businessServiceEvaluationFixture)
    {
        _businessServiceEvaluationFixture = businessServiceEvaluationFixture;

        _mockBusinessServiceEvaluationRepository = BusinessServiceEvaluationRepositoryMocks.GetBusinessServiceEvaluationRepository(_businessServiceEvaluationFixture.BusinessServiceEvaluations);

        _handler = new GetBusinessServiceEvaluationListQueryHandler(_businessServiceEvaluationFixture.Mapper, _mockBusinessServiceEvaluationRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_BusinessServiceEvaluationsCount()
    {
        var result = await _handler.Handle(new GetBusinessServiceEvaluationListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<BusinessServiceEvaluationListVm>>();

        result.Count.ShouldBe(3);
    }
    
    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        await _handler.Handle(new GetBusinessServiceEvaluationListQuery(), CancellationToken.None);

        _mockBusinessServiceEvaluationRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_Valid_BusinessServiceEvaluationList()
    {
        _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].Grade = "A";
        _businessServiceEvaluationFixture.BusinessServiceEvaluations[1].Grade = "B";
        _businessServiceEvaluationFixture.BusinessServiceEvaluations[2].Grade = "C";


        var evaluations = new List<Domain.Entities.BusinessServiceEvaluation>
        {
            new Domain.Entities.BusinessServiceEvaluation { 
                //Id =int.Parse(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId), 
                ReferenceId = _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId,
                Grade="A", 
                BusinessServiceId=_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].BusinessServiceId,
                BusinessServiceName=_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].BusinessServiceName,
                Description=_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].Description,
                GradeValue=_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].GradeValue,  
            }
           
        };

        var businessServive = new List<BusinessServiceEvaluationListVm>();
        _mockBusinessServiceEvaluationRepository.Setup(dp => dp.ListAllAsync()).ReturnsAsync(evaluations.AsReadOnly());

        var result = await _handler.Handle(new GetBusinessServiceEvaluationListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<BusinessServiceEvaluationListVm>>();

        result[0].Id.ShouldBe(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId);
        result[0].BusinessServiceId.ShouldBe(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].BusinessServiceId);
        result[0].BusinessServiceName.ShouldBe(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].BusinessServiceName);
        result[0].Description.ShouldBe(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].Description);
        result[0].Grade.ShouldBe(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].Grade);
        result[0].GradeValue.ShouldBe(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].GradeValue);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockBusinessServiceEvaluationRepository = BusinessServiceEvaluationRepositoryMocks.GetBusinessServiceEvaluationEmptyRepository();

        var handler = new GetBusinessServiceEvaluationListQueryHandler(_businessServiceEvaluationFixture.Mapper, _mockBusinessServiceEvaluationRepository.Object);

        var result = await handler.Handle(new GetBusinessServiceEvaluationListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }
}