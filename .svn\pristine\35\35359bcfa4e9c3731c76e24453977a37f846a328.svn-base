﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BusinessFunction.Events.Update;

public class BusinessFunctionUpdatedEventHandler : INotificationHandler<BusinessFunctionUpdatedEvent>
{
    private readonly ILogger<BusinessFunctionUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BusinessFunctionUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<BusinessFunctionUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(BusinessFunctionUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.OperationalFunction}",
            Entity = Modules.OperationalFunction.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"OperationalFunction '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"OperationalFunction '{updatedEvent.Name}' updated successfully.");
    }
}