﻿using ContinuityPatrol.Application.Features.UserLogin.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.UserLogin.Events;

public class DeleteUserLoginEventTests : IClassFixture<UserLoginFixture>, IClassFixture<UserActivityFixture>
{
    private readonly UserLoginFixture _userLoginFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly UserLoginDeletedEventHandler _handler;

    public DeleteUserLoginEventTests(UserLoginFixture userLoginFixture, UserActivityFixture userActivityFixture)
    {
        _userLoginFixture = userLoginFixture;
        
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockUserLoginEventLogger = new Mock<ILogger<UserLoginDeletedEventHandler>>();

        _mockUserActivityRepository =UserLoginRepositoryMocks.CreateUserLoginEventRepository(_userLoginFixture.UserActivities);

        _handler = new UserLoginDeletedEventHandler(mockLoggedInUserService.Object, mockUserLoginEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteUserLoginEventDeleted()
    {
        _userLoginFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_userLoginFixture.UserLoginDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_userLoginFixture.UserLoginDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}