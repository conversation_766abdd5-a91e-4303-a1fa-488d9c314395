﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class InfraObjectSchedulerFilterSpecification : Specification<InfraObjectScheduler>
{
    public InfraObjectSchedulerFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("infraobjectname=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.InfraObjectName.Contains(stringItem.Replace("infraobjectname=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("beforeswitchoverworkflowname=",
                                 StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.BeforeSwitchOverWorkflowName.Contains(stringItem.Replace(
                            "beforeswitchoverworkflowname=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("workflowversion=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.WorkflowVersion.Contains(stringItem.Replace("workflowversion=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("afterswitchoverworkflowname=",
                                 StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.AfterSwitchOverWorkflowName.Contains(stringItem.Replace(
                            "afterswitchoverworkflowname=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.InfraObjectName.Contains(searchString) || p.AfterSwitchOverWorkflowName.Contains(searchString) ||
                    p.BeforeSwitchOverWorkflowName.Contains(searchString) || p.WorkflowVersion.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.InfraObjectName != null;
        }
    }
}