﻿using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel;

public class FormMappingViewModel
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string FormId { get; set; }
    public string FormName { get; set; }
    public string FormTypeId { get; set; }
    public string FormTypeName { get; set; }
    public string Logo { get; set; }
    public string Version { get; set; }
    public string Properties { get; set; }

    public List<FormTypeCategoryListVm> FormTypeCategories { get; set; }
    public PaginatedResult<FormTypeCategoryListVm> PaginatedFormMapping { get; set; }
}