﻿@using ContinuityPatrol.Shared.Services.Helper
@{
    ViewData["Title"] = "List";
    //Layout = "~/Views/Shared/_MasterLayout.cshtml";
    Layout = "~/Views/Shared/_DashboardLayout.cshtml";
}
@Html.AntiForgeryToken()
<link href="~/css/dashboard.css" rel="stylesheet" />

<div class="page-content pageblur">
    <div class="row g-2">
        <div class="col-12 mb-2">
            <h6 class="page_title"><i class="cp-it-view"></i><span>IT Resiliency View</span></h6>
        </div>
    </div>
    <div class="row g-2">
        <div class="col-12 col-md-3 col-xl-2 col-xxl-2 d-grid">
            <div class="card BusinessCard_List Card_Design_None border mb-2">
                <div class="card-header header p-2">
                    <div class="input-group rounded-1 border-0 shadow-sm">
                        <input type="search" placeholder="Select Operational Services" class="form-control  bg-transparent" id="search-bar" />
                        <span class="input-group-text"><i class="cp-search" title="Search"></i></span>
                    </div>
                </div>
                <div class="card-body p-0 mb-2" style="height: calc(100vh - 427px); overflow-y: auto;">
                    <div id="businessServiceParentContainer" class="list-group">
                    </div>

                    <div id="search_noData" class="d-none text-center">
                        <img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img" style="width: 150px;margin-top:10px">
                    </div>
                </div>
            </div>
            <div class="card Card_Design_None mb-0 border rounded">
                <div class="card-header pb-0">
                    <span class="card-title">Resilience Health Score</span>
                </div>
                <div class="card-body pt-0 d-flex align-items-center justify-content-center" id="Resilience_data_Health">
                    <div>
                        <div id="ResilienceHealthChart" class="ResilienceHealthChartdata"></div>
                        <table class="table table-sm mb-0" id="ITcharttable">
                            <thead>
                                <tr>
                                    <th class="bg-transparent">Facilities</th>
                                    <th class="bg-transparent text-end">Health</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-light text-truncate d-inline-block align-bottom" style="max-width: 80px;" id="Resilience_infraobject"></td>
                                    <td class="text-end"><span class="" id="Resilience_Health"></span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>
        <div class="col-12 col-md-7 col-xl-4">
            <div class="card Card_Design_None mb-2 border" id="businessservice">
                <div class="card-header p-2 header fw-semibold" BS_Id=${value}>
                    <span class="card-title">Service Interdependency</span>
                    <div class="input-group rounded-1 border-0 shadow-sm" style="width:240px">
                        <input id="It_searchBusinessName" type="search" class="form-control" placeholder="Search" autocomplete="off" />

                        <div class="input-group-text pe-2">
                          
                            <span class="badge rounded-pill bg-danger fw-normal mx-2 align-middle d-none"><span style="max-width:45px" class="text-truncate d-inline-block me-1 align-middle fs-8" id="dataLagFilterValue"></span><span role="button" id="clearFilterValue"><i class="cp-error text-white fs-8"></i></span></span>
                            <div class="dropdown">
                                <i type="button" class="cp-filter" title="Filter" data-bs-toggle="dropdown" data-bs-auto-close="outside"></i>
                                <form class="dropdown-menu p-0" id="datalag_filter_form">
                                    <div class="accordion accordion-flush filter-accordion" id="datalag_filter_accordion">
                                        
                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                                                    SortBy DataLag
                                                </button>
                                            </h2>
                                            <div id="flush-collapseOne" class="accordion-collapse collapse" data-bs-parent="#datalag_filter_accordion">
                                                <div class="accordion-body p-0">
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item datalag_filter" role="button" mode="1"><i class="cp-down-linearrow text-danger fs-8 me-1"></i>High to Low</li>
                                                        <li class="list-group-item datalag_filter" role="button" mode="2"><i class="cp-up-linearrow text-success fs-8 me-1"></i>Low to High</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                                                    FilterBy State
                                                </button>
                                            </h2>
                                            <div id="flush-collapseTwo" class="accordion-collapse collapse" data-bs-parent="#datalag_filter_accordion">
                                                <div class="accordion-body p-0">
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item datalag_filter" role="button" state="active"><i class="cp-active-inactive text-success fs-8 me-1"></i> Active</li>
                                                        <li class="list-group-item datalag_filter" role="button" state="maintenance"><i class="cp-maintenance text-primary fs-8 me-1"></i> Maintenance</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                                                    FilterBy Type
                                                </button>
                                            </h2>
                                            <div id="flush-collapseThree" class="accordion-collapse collapse" data-bs-parent="#datalag_filter_accordion">
                                                <div class="accordion-body p-0">
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item datalag_filter" role="button" type="0"><i class="cp-on-arrow text-primary fs-8 me-1"></i>SwitchOver</li>
                                                        <li class="list-group-item datalag_filter" role="button" type="2"><i class="cp-off-arrow text-primary fs-8 me-1"></i>SwitchBack</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                       
                        @* <div class="input-group-text">
                            <div class="dropdown">
                                <span data-bs-toggle="dropdown"  class="" title="Filter"><i class="cp-filter"></i></span>
                                <ul class="dropdown-menu filter-dropdown" style=""> *@
                                   @*  <li>
                                        <h6 class="dropdown-header">Filter Search</h6>
                                    </li>
                                    <li class="dropdown-item" title="InfraObject Name">
                                        <div><input class="form-check-input itViewSearch" type="checkbox" value="displayName=" id="It_InfraObject" checked> <label class="form-check-label" for="It_InfraObject">InfraObject Name </label></div>
                                    </li>
                                    <li class="dropdown-item" title="Operational Function Name">
                                        <div><input class="form-check-input itViewSearch" type="checkbox" value="name=" id="It_OperationalFunction"> <label class="form-check-label" for="It_OperationalFunction">Operational Function Name </label></div>
                                    </li> *@
                               @*  </ul>
                            </div>
                        </div> *@

                    </div>
                </div>
                <div class="Workflow-Execution px-0" style="height: calc(100vh - 336px);">
                    <div class="accordion accordion-flush" id="accordionFlushExample">
                        <div class="accordion-item rounded-2">
                            <div id="BusinessFunction" class="accordion-header"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card Card_Design_None mb-2 border">
                <div class="card-header">
                    <span class="card-title">Solution Diagram</span>
                </div>
                <div class="card-body p-0" id="DRSolution">
                    <div id="ITView-SolutionDiagram" style="height:132px; justify-content: center;"></div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-5 col-xl-6 d-grid" id="infraobject" style="grid-template-rows: max-content;">
            <div class="card Card_Design_None ITView-Infraobject-Tab mb-2 border">
                <div class="d-flex justify-content-between ">
                    <div class="card-header p-2 card-title">Interdependency Relationship</div>
                    <div id="nodeRelationCont"></div>
                </div>
                <div class="card-body p-0">
                    <ul class="nav nav-tabs nav-fill" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active fs-7 py-1" id="home-tab" data-bs-toggle="tab" data-bs-target="#home-tab-pane" type="button" role="tab" aria-controls="home-tab-pane" aria-selected="true" title="Component Monitor">
                                <i class="cp-infra-component me-2"></i>Component Monitor
                            </button>
                        </li>
                        <li class="nav-item" role="presentation" id="replicationMonitorContainer">
                            <button class="nav-link fs-7 py-1" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile-tab-pane" type="button" role="tab" aria-controls="profile-tab-pane" aria-selected="false" title="Replication Monitor">
                                <i class="cp-replication-on me-2"></i>Replication Monitor
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content px-2 InfraComponentTable small" style="height:154px; overflow-y:auto; text-align:center" id="infraobjectalldata">
                    </div>
                </div>
            </div>
            <div class="card-group gap-2">
                <div class="card Card_Design_None mb-2 border rounded blue-shades-color">
                    <div class="card-header">
                        <span class="card-title">Operational Service RPO & RTO Summary</span>
                    </div>
                    <div class="card-body py-1">
                        <div id="chartdata" class="d-grid align-items-center h-100"></div>
                    </div>
                </div>
                <div class="card Card_Design_None border mb-2 green-shades-color rounded">
                    <div class="card-header pb-0 p-2"><span class="card-title">IT Infra Summary</span></div>
                    <div class="carousel-inner" id="infrasummary">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div>
    <span class="align-middle" id="userRole" style="display:none;">  @WebHelper.UserSession.RoleName</span>
    <span class="align-middle" id="userRoleValue" style="display:none;">  @WebHelper.UserSession.RoleName</span>
    <span class="align-middle" id="loggedInUserId" style="display:none;">  @WebHelper.UserSession.LoggedUserId</span>
</div>
<div id="monitorView" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Dashboard.Monitor" aria-hidden="true"></div>
<div class="modal fade" id="ActiveMaintanenceModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content" style="border-radius:70px 70px 10px 10px">
            <form id="SaveForm">
                <div class="modal-header p-0">
                    <img class="delete-img" src="~/img/isomatric/maintanence.svg" alt="Delete Img" />
                </div>
                <div class="modal-body text-center pt-0">
                    <h5 class="fw-semibold">Confirmation</h5>
                    <div class="mb-3 form-group" id="textbox">
                        <div class="form-label"><i class="cp-question-mark me-1"></i> Reason</div>
                        <div>
                            <textarea class="form-control" id="textArea" placeholder="Enter The Reason" rows="2" cols="50" style="resize: none;" maxlength="400"></textarea>
                        </div>
                    </div>
                    <p>Do you want to switch the state to <span class="font-weight-bolder text-primary" id="stateinfraname"></span> <span class="font-weight-bolder text-primary" id="state"></span> ?</p>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary btn-sm" id="ActiveMaintanenceButton">Yes</button>
                </div>
            </form>
        </div>
    </div>
</div>
@*alertNotification*@
@* <div class='Notification'>
    <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
        <div class='d-flex'>
            <div class='toast-body'>
                <span id="alertClass" class='{ClassName}-toast'>
                    <i id="icon_Detail" class=''></i>
                </span>
                <span id="message">
                </span>
            </div>
            <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
        </div>
    </div>
</div> *@
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard-Charts/RTOSummary.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard-Charts/RPOSummary.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/It_View.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard-Charts/ResilienceHealthChart.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewMysql.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewAlwaysOn.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewODG.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewOracleRac.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewPostgres.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewMssqlDBMirroring.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewEmptyData.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewMongoDB.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewHyperV.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewDB2HADR.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ITResiliencySolutionDiagram.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewService.js"></script>
@* <script src="~/js/dashboard-charts/dashboarditview/ItViewSummary.js"></script> *@
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewZertoVpg.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/itViewSRM.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewIbmSvcGM.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewMssqlNLS.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/itView_OCP.js"></script>
<script src="~/js/Dashboard/ITResiliencyView/ITResiliencyDashboard/ItViewRP4VM.js"></script>"