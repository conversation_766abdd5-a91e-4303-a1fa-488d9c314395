﻿using ContinuityPatrol.Application.Features.Site.Events.Update;

namespace ContinuityPatrol.Application.Features.Site.Commands.Update;

public class UpdateSiteCommandHandler : IRequestHandler<UpdateSiteCommand, UpdateSiteResponse>
{
    private readonly IMapper _mapper;

    private readonly IPublisher _publisher;

    private readonly ISiteRepository _siteRepository;

    public UpdateSiteCommandHandler(IMapper mapper, ISiteRepository siteRepository, IPublisher publisher)
    {
        _mapper = mapper;

        _publisher = publisher;

        _siteRepository = siteRepository;
    }

    public async Task<UpdateSiteResponse> Handle(UpdateSiteCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _siteRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Site), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateSiteCommand), typeof(Domain.Entities.Site));

        await _siteRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateSiteResponse
        {
            Message = Message.Update(nameof(Domain.Entities.Site), eventToUpdate.Name),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new SiteUpdatedEvent { SiteName = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}