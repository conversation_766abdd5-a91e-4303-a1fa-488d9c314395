﻿
function DCMapChart(globalMapData, multiGeoLine) {
    // Themes begin
    am4core.useTheme(am4themes_animated);
    // Themes end

    var continents = {
        "AF": 0,
        "AN": 1,
        "AS": 2,
        "EU": 3,
        "NA": 4,
        "OC": 5,
        "SA": 6
    }

    // Create map instance
    var chart = am4core.create("DCMappingChart", am4maps.MapChart);
    if (chart.logo) {
        chart.logo.disabled = true;
    }
    chart.maxZoomLevel = 3;
    //chart.zoomControl = new am4maps.ZoomControl();
    //chart.zoomControl.slider.height = 100;
    chart.projection = new am4maps.projections.AzimuthalEqualArea();

    // Set projection
    chart.projection = new am4maps.projections.Orthographic();
    chart.panBehavior = "rotateLongLat";
    chart.padding(0, 0, 0, 0);

    chart.backgroundSeries.mapPolygons.template.polygon.fill = am4core.color("#ededed");
    chart.backgroundSeries.mapPolygons.template.polygon.fillOpacity = 0.8;
    chart.deltaLongitude = 280;
    chart.deltaLatitude = -20;
    //chart.panBehavior = "rotateLongLat";
    chart.zoomControl = new am4maps.ZoomControl();

    // limits vertical rotation
    chart.adapter.add("deltaLatitude", function (delatLatitude) {
        return am4core.math.fitToRange(delatLatitude, -90, 90);
    })

    // Create map polygon series for world map
    var worldSeries = chart.series.push(new am4maps.MapPolygonSeries());
    worldSeries.useGeodata = true;
    worldSeries.geodata = am4geodata_worldIndiaLow;
    worldSeries.exclude = ["AQ"];

    //worldSeries.useGeodata = true;



    var worldPolygon = worldSeries.mapPolygons.template;
    worldPolygon.tooltipText = "{name}";
    worldPolygon.nonScalingStroke = true;
    worldPolygon.strokeOpacity = 0.5;
    worldPolygon.fill = am4core.color("#eee");
    worldPolygon.propertyFields.fill = "color";

    var hs = worldPolygon.states.create("hover");
    hs.properties.fill = chart.colors.getIndex(9);


    // Add shadow
    var shadow = worldPolygon.filters.push(new am4core.DropShadowFilter());
    shadow.color = am4core.color("#aeaeae");
    shadow.blur = 1.5;

    // Create country specific series (but hide it for now)
    var countrySeries = chart.series.push(new am4maps.MapPolygonSeries());
    countrySeries.useGeodata = true;
    countrySeries.hide();

    countrySeries.geodataSource.events.on("done", function (ev) {
        worldSeries.hide();
        countrySeries.show();
    });

    var countryPolygon = countrySeries.mapPolygons.template;
    countryPolygon.tooltipText = "{name}";
    countryPolygon.nonScalingStroke = true;
    countryPolygon.strokeOpacity = 0.5;
    countryPolygon.fill = am4core.color("#eee");

    var hs = countryPolygon.states.create("hover");
    hs.properties.fill = chart.colors.getIndex(9);

    var previousPolygon;

    worldPolygon.events.on("hit", function (event) {

        selectCountry(event.target);
    });

    function resetHover() {
        countrySeries.mapPolygons.each(function (polygon) {
            polygon.isHover = false;
        })
    }

    let currentPolygon = undefined
    function selectCountry(mapPolygon) {
        resetHover();
        countrySeries.hideTooltip();

        // make others inactive
        countrySeries.mapPolygons.each(function (polygon) {
            polygon.isActive = false;
        })

        mapPolygon.isActive = true;
        // meaning it's globe   
        // animate deltas (results the map to be rotated to the selected country)
        if (chart.zoomLevel != 1) {
            chart.goHome();
            rotateAndZoom(mapPolygon);
        }
        else {
            rotateAndZoom(mapPolygon);
        }
        // if it's not a globe, simply zoom to the country        
    }

    function rotateAndZoom(mapPolygon) {
        countrySeries.hideTooltip();
        var animation = chart.animate([{ property: "deltaLongitude", to: -mapPolygon.visualLongitude }, { property: "deltaLatitude", to: -mapPolygon.visualLatitude }], 1000)
        animation.events.on("animationended", function () {
            chart.zoomToMapObject(mapPolygon, getZoomLevel(mapPolygon));
        })
    }
    function getZoomLevel(mapPolygon) {
        var w = mapPolygon.polygon.bbox.width;
        var h = mapPolygon.polygon.bbox.width;
        return Math.min(chart.seriesWidth, chart.seriesHeight / (h * 10))
    }

    // Set up data for countries
    var data = [];
    for (var id in am4geodata_data_countries2) {
        if (am4geodata_data_countries2.hasOwnProperty(id)) {
            var country = am4geodata_data_countries2[id];
            if (country.maps.length) {
                data.push({
                    id: id,
                    color: chart.colors.getIndex(continents[country.continent_code]),
                    //map: country.maps[0]
                });
            }
        }
    }
    worldSeries.data = data;

    // Add image series
    var imageSeries = chart.series.push(new am4maps.MapImageSeries());
    imageSeries.mapImages.template.propertyFields.longitude = "longitude";
    imageSeries.mapImages.template.propertyFields.latitude = "latitude";
    imageSeries.mapImages.template.tooltipText = "{title}";
    imageSeries.mapImages.template.propertyFields.url = "url";

    //imageSeries.events.on('hit', (ev) => {

    //    window.location.href ='../../Dashboard/ITResiliencyView/List'
    //    //chart.openPopup("We clicked on <strong> <a href = https://duckduckgo.com/?q=' + (<any>ev.target.dataItem.dataContext).name.replace(/\s/g, '") + "&t=h_&ia=news&iar=news target=_blank>' + (<any>ev.target.dataItem.dataContext).name + '</a> </strong>');
    //});


    var circle = imageSeries.mapImages.template.createChild(am4core.Circle);
    circle.radius = 3;
    circle.propertyFields.fill = "color";
    circle.nonScaling = true;

    var circle2 = imageSeries.mapImages.template.createChild(am4core.Circle);
    circle2.radius = 3;
    circle2.propertyFields.fill = "color";

    circle2.events.on("inited", function (event) {
        animateBullet(event.target);
    })

    let label = imageSeries.mapImages.template.createChild(am4core.Label);
    label.propertyFields.text = "title";
    label.horizontalCenter = "middle";
    label.padding(5, 5, 5, 5);
    label.fontSize = 14;
    label.nonScaling = true;
    label.interactionsEnabled = true;
    label.background = new am4core.RoundedRectangle();
    label.background.cornerRadius(10, 10, 10, 10);
    label.background.opacity = 1;
    label.background.strokeWidth = 0;

    // Enable Tooltip
    label.tooltipText = "{title}";
    label.tooltipHTML = `
  <div class="d-flex align-items-center mb-2 text-dark">
    {imageUrl}
    <h6 class="card-title fw-bold mb-0">{type}</h6>
  </div>
  <div class="d-grid align-items-center gap-2 text-dark">
    {conditionalTemperature}
    <span class="d-flex align-items-center"><i class="cp-location me-1"></i>{title}</span>
  </div>
`;

    // Add adapter for conditional content
    label.adapter.add("tooltipHTML", function (html, target) {
        // Get data item
        const dataItem = target.dataItem;

        // Process image URL
        const imageHtml = dataItem.dataContext.imageUrl ?
            `<img src="${dataItem.dataContext.imageUrl}" class="img-fluid rounded me-1" alt="Image">` :
            '';

        // Process temperature
        const tempHtml = dataItem.dataContext.dataTemperature ?
            `<span class="d-flex align-items-center"><i class="cp-thermometer me-1"></i>${dataItem.dataContext.dataTemperature}</span>` :
            '';

        // Replace placeholders
        return html
            .replace("{imageUrl}", imageHtml)
            .replace("{conditionalTemperature}", tempHtml);
    });
    label.events.on("hit", function (event) {
        let dataItem = event.target.dataItem;
        if (dataItem) {
            let type = dataItem.dataContext.type;
            let sitetype = type.includes("NearDR") ? "Near DR Site" :
                type.includes("DR") ? "DR Site" :
                    type.includes("PR") ? "PR Site" :
                        type.includes("Custom") ? "Custom DR Site" : "Custom DR Site";
            let sitetypedetail = type.includes("NearDR") ? "NearDr" :
                type.includes("DR") ? "DR" :
                    type.includes("PR") ? "PR" :
                        type.includes("Custom") ? "Custom" : "Custom";

            const tabs = document.querySelectorAll(".siteGroup");
            let SiteConfiguredCount = 0;

            tabs.forEach((tab) => {
                let tapStatus = tab.getAttribute("tapstatus");
                let siteConfiguredElement = tab.querySelector(".SiteConfigured");
                if (tapStatus === sitetype && siteConfiguredElement) {
                    tab.setAttribute("aria-expanded", "true");
                    SiteConfiguredCount = siteConfiguredElement.textContent.trim();
                } else {
                    tab.setAttribute("aria-expanded", "false");
                }
            });
            document.getElementById("siteTitle").textContent = sitetype + " - " + SiteConfiguredCount;
            GetTotalSiteDetailsForOneViewList(sitetypedetail, "collapseExample");
        }
    });

    /*    label.padding(0, 0, 0, 1);*/

    function animateBullet(circle) {
        var animation = circle.animate([{ property: "scale", from: 1 / chart.zoomLevel, to: 5 / chart.zoomLevel }, { property: "opacity", from: 1, to: 0 }], 1000, am4core.ease.circleOut);
        animation.events.on("animationended", function (event) {
            animateBullet(event.target.object);
        })
    }

    var graticuleSeries = chart.series.push(new am4maps.GraticuleSeries());
    graticuleSeries.mapLines.template.stroke = am4core.color("#fff");
    graticuleSeries.fitExtent = false;
    graticuleSeries.mapLines.template.strokeOpacity = 0;
    graticuleSeries.mapLines.template.stroke = am4core.color("#fff");
    var colorSet = new am4core.ColorSet();
    imageSeries.data = globalMapData


    imageSeries.tooltip.getFillFromObject = false;
    imageSeries.tooltip.background.fill = am4core.color("#FFFFFF");

    // Add line series
    var lineSeries = chart.series.push(new am4maps.MapLineSeries());
    lineSeries.mapLines.template.stroke = am4core.color("#161616");
    lineSeries.mapLines.template.line.strokeWidth = 2;
    lineSeries.mapLines.template.line.nonScalingStroke = true;
    lineSeries.mapLines.template.line.strokeDasharray = "5 3";

    lineSeries.mapLines.template.line.adapter.add("strokeWidth", function (strokeWidth, target) {
        target.strokeDasharray = (5 / chart.zoomLevel) + " " + (3 / chart.zoomLevel)
        return strokeWidth;
    });


    // Create a map line

    lineSeries.data = [{
        "multiGeoLine": [
            multiGeoLine
        ]
    }];

    lineSeries.id = "myline";
    lineSeries.setClassName();
    // Add an arrow at the end of the line

    //var arrowSeries = chart.series.push(new am4maps.MapImageSeries());
    //var arrow = arrowSeries.mapImages.template.createChild(am4core.Sprite);
    //arrow.path = "M2,0 L2,20 L12,10 L2,0";
    //arrow.fill = am4core.color("#91a656");
    //arrow.fillOpacity = 1;
    //arrow.strokeWidth = 0;
    //arrow.horizontalCenter = "middle";
    //arrow.verticalCenter = "middle";

    //// Function to calculate midpoint and angle
    //function getMidpointAndAngle(from, to) {
    //    // Convert degrees to radians
    //    const φ1 = from.latitude * Math.PI / 180;
    //    const λ1 = from.longitude * Math.PI / 180;
    //    const φ2 = to.latitude * Math.PI / 180;
    //    const λ2 = to.longitude * Math.PI / 180;

    //    // Calculate midpoint (geographic center)
    //    const Bx = Math.cos(φ2) * Math.cos(λ2 - λ1);
    //    const By = Math.cos(φ2) * Math.sin(λ2 - λ1);
    //    const midLat = Math.atan2(
    //        Math.sin(φ1) + Math.sin(φ2),
    //        Math.sqrt((Math.cos(φ1) + Bx) * (Math.cos(φ1) + Bx) + By * By)
    //    );
    //    const midLong = λ1 + Math.atan2(By, Math.cos(φ1) + Bx);

    //    // Calculate initial bearing (angle)
    //    const y = Math.sin(λ2 - λ1) * Math.cos(φ2);
    //    const x = Math.cos(φ1) * Math.sin(φ2) -
    //        Math.sin(φ1) * Math.cos(φ2) * Math.cos(λ2 - λ1);
    //    const angle = Math.atan2(y, x) * 180 / Math.PI;

    //    return {
    //        latitude: midLat * 180 / Math.PI,
    //        longitude: midLong * 180 / Math.PI,
    //        angle: (angle + 360) % 270 // Normalize to 0-360
    //    };
    //}
    //debugger
    //// Add lines and arrows
    //for (let i = 0; i < multiGeoLine.length - 1; i++) {
    //    const from = multiGeoLine[i];
    //    const to = multiGeoLine[i + 1];

    //    // Add line
    //    let line = lineSeries.mapLines.create();
    //    line.multiGeoLine = [
    //        [
    //            [from.longitude, from.latitude],
    //            [to.longitude, to.latitude]
    //        ]
    //    ];

    //    // Add arrow
    //    const midpoint = getMidpointAndAngle(from, to);
    //    let arrowImage = arrowSeries.mapImages.create();
    //    arrowImage.latitude = midpoint.latitude;
    //    arrowImage.longitude = midpoint.longitude;
    //    arrowImage.rotation = midpoint.angle;
    //}




    //INITIAL ZOOM
    chart.homeZoomLevel = 1.01;

    //HOME BUTTON
    var button = chart.chartContainer.createChild(am4core.Button);
    //button.label.text = "Home";
    button.padding(5, 5, 5, 5);
    //button.width = 40;
    button.align = "right";
    button.marginRight = 15;
    button.events.on("hit", function () {
        chart.goHome();
    });
    button.icon = new am4core.Sprite();
    button.icon.path = "M16,8 L14,8 L14,16 L10,16 L10,10 L6,10 L6,16 L2,16 L2,8 L0,8 L8,0 L16,8 Z M16,8";

    //chart.smallMap = new am4maps.SmallMap();
    //chart.smallMap.series.push(worldSeries);

    chart.events.on("ready", function (ev) {
        var india = worldSeries.getPolygonById("IN");

        setTimeout(function () {
            // Pre-zoom
            chart.zoomToMapObject(india);
            // Set active state
            //india.isActive = true;
            //hideSpinner('spinnerOverlayChartThree', '.chartThree.opacity-75');
        }, 500);
    });
    chart.svgContainer.autoResize = false;
    chart.svgContainer.measure();
}





