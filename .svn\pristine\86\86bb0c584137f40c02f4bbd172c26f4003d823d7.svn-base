﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowCategoryRepository : BaseRepository<WorkflowCategory>, IWorkflowCategoryRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public WorkflowCategoryRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<WorkflowCategory>> GetWorkflowCategoryNames()
    {
        return await _dbContext.WorkflowCategories.Active().AsNoTracking()
            .Select(x => new WorkflowCategory { ReferenceId = x.ReferenceId, Name = x.Name })
            .ToListAsync();
    }

    public Task<bool> IsWorkflowCategoryNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsWorkflowCategoryNameUnique(string name)
    {
        var matches = _dbContext.WorkflowCategories.AsNoTracking().Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }

    public override async Task<IReadOnlyList<WorkflowCategory>> ListAllAsync()
    {
        return  await FilterRequiredField(Entities.AsNoTracking().DescOrderById()).ToListAsync();

    }

    //public override Task<WorkflowCategory> GetByReferenceIdAsync(string id)
    //{
    //    return _loggedInUserService.IsParent
    //        ? base.GetByReferenceIdAsync(id)
    //        : Task.FromResult(FindByFilterAsync(workflowCategory => workflowCategory.ReferenceId.Equals(id) && workflowCategory.CompanyId.Equals(_loggedInUserService.CompanyId)).Result.SingleOrDefault());
    //}

    public override IQueryable<WorkflowCategory> GetPaginatedQuery()
    {

        return Entities
            .Where(x => x.IsActive)
            .AsNoTracking()
            .OrderByDescending(x => x.Id);
    }

    private IQueryable<WorkflowCategory> FilterRequiredField(IQueryable<WorkflowCategory> workflowCategories)
    {
        return workflowCategories.Select(x => new WorkflowCategory
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Name = x.Name,
            CompanyId = x.CompanyId,
            Properties = x.Properties,
            Version = x.Version
        });
    }

}