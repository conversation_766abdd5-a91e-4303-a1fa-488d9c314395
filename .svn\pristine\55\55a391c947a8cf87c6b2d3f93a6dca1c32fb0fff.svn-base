﻿let urls = "/Dashboard/ITResiliencyView/GetInfraObjectDetailsById/"

globalBusinessServiceArray = [];
let ongoingAjax = [];
let infraObjectGlobalState = "";
let infraObjectGlobalId = "";
let action = {};
let globalInfraObjectId = '';
//const overallBS_noData = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">'
const overallBS_noData = '<svg width="200" height="133" viewBox="0 0 200 133" fill="none"><g clip-path="url(#clip0_2_9098)"><mask id="mask0_2_9098" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="200" height="133"><path d="M0 0.984863H200V132.235H0V0.984863Z" fill="white"/></mask><g mask="url(#mask0_2_9098)"><mask id="mask1_2_9098" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="200" height="133"><path d="M200 0.984863H0V132.235H200V0.984863Z" fill="white"/></mask><g mask="url(#mask1_2_9098)"><mask id="mask2_2_9098" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="200" height="133"><path d="M200 0.984863H0V132.235H200V0.984863Z" fill="white"/></mask><g mask="url(#mask2_2_9098)"><path d="M186.1 28.5871L183.309 42.1195L181.513 40.3649L178.459 42.5479L176.124 39.6428L170.888 43.4553L166.414 35.961L161.422 38.8888L159.443 36.157L153.105 39.7337L151.2 35.6428L155.642 14.0928L179.458 19.0189L186.1 28.5871Z" fill="#EBEBEB"/><path d="M179.457 19.02L186.099 28.5882L177.837 26.8797L179.457 19.02Z" fill="#DBDBDB"/><path d="M182.715 44.8241L178.625 64.6775L146.544 58.0434L150.605 38.3502L152.511 42.4383L158.852 38.8616L160.828 41.5951L165.822 38.669L170.294 46.1638L175.532 42.3513L177.865 45.2525L180.923 43.0729L182.715 44.8241ZM136.797 42.4974L104.105 44.5235L101.507 2.49111L125.783 0.984863L134.695 8.4735L136.797 42.4974Z" fill="#EBEBEB"/><path d="M125.783 0.984863L134.696 8.4735L126.278 8.99623L125.783 0.984863ZM116.733 29.2672L116.604 28.3184C116.275 26.353 116.812 24.1559 118.517 21.8582C120.057 19.8138 120.89 18.3542 120.797 16.6962C120.694 14.8269 119.445 13.6479 117.147 13.7411C115.829 13.8104 114.563 14.2786 113.516 15.0843L112.512 12.828C113.677 11.8792 115.723 11.1786 117.657 11.0752C121.863 10.8428 123.901 13.3354 124.071 16.119C124.209 18.6076 122.922 20.4803 121.284 22.6672C119.778 24.6616 119.286 26.3048 119.5 28.165L119.591 29.1155L116.733 29.2672ZM116.213 34.4377C116.137 33.0837 116.997 32.0797 118.278 32.0099C119.559 31.94 120.49 32.8394 120.567 34.1974C120.637 35.478 119.853 36.5195 118.499 36.5877C117.208 36.661 116.275 35.7212 116.201 34.4434L116.213 34.4377Z" fill="#DBDBDB"/><path d="M34.9575 22.1181L31.6752 20.626L31.2166 21.6317C31.1496 21.776 31.0443 21.8991 30.9122 21.9877C30.78 22.0762 30.6261 22.1268 30.4672 22.134C30.2877 22.1416 30.1093 22.1596 29.932 22.188C29.7752 22.2105 29.6153 22.1904 29.469 22.1297C29.3227 22.069 29.1955 21.97 29.1007 21.8431L28.4564 20.9499L25.5223 23.0578L26.1661 23.951C26.2552 24.0811 26.3075 24.2328 26.3175 24.3901C26.3275 24.5474 26.2948 24.7045 26.2229 24.8448C26.14 25.0055 26.0656 25.1705 26.0002 25.3391C25.9424 25.4871 25.8455 25.6166 25.7198 25.7137C25.5941 25.8108 25.4443 25.8718 25.2865 25.8902L24.182 25.9988L24.5388 29.5993L25.6433 29.4885C25.8018 29.4758 25.9607 29.5064 26.1031 29.5772C26.2454 29.648 26.3658 29.7562 26.4513 29.8902C26.4979 29.9669 26.5479 30.0385 26.599 30.1118C26.6513 30.1842 26.7049 30.2556 26.7598 30.326C26.8613 30.4489 26.9267 30.5975 26.9484 30.7554C26.9702 30.9132 26.9476 31.0741 26.8831 31.2198L26.4229 32.2363L29.7127 33.7283L30.1712 32.7152C30.2394 32.5715 30.3456 32.4491 30.4783 32.3612C30.6109 32.2733 30.765 32.2232 30.924 32.2164C31.1032 32.2092 31.2816 32.1914 31.4593 32.163C31.6158 32.1401 31.7756 32.1601 31.9217 32.2208C32.0677 32.2815 32.1946 32.3807 32.2888 32.5078L32.9343 33.401L35.8729 31.2931L35.2286 30.3908C35.1361 30.2623 35.0816 30.1104 35.0711 29.9524C35.0607 29.7944 35.0948 29.6366 35.1695 29.4971C35.2502 29.3368 35.3227 29.1732 35.3871 29.0061C35.4436 28.8572 35.5401 28.7269 35.666 28.6294C35.792 28.5319 35.9424 28.4712 36.1007 28.4539L37.2053 28.3431L36.8485 24.7482L35.7422 24.8556C35.5847 24.8691 35.4266 24.8388 35.2852 24.7681C35.1438 24.6975 35.0248 24.5891 34.9411 24.455C34.8917 24.3805 34.8413 24.3066 34.7899 24.2334C34.7391 24.1605 34.6855 24.0896 34.6291 24.0209C34.5303 23.8968 34.4668 23.7483 34.4454 23.5911C34.424 23.4338 34.4455 23.2738 34.5075 23.1277L34.9575 22.1181ZM33.0916 25.4488C33.3461 25.8049 33.5185 26.213 33.5966 26.6437C33.6747 27.0744 33.6565 27.517 33.5434 27.9398C33.4302 28.3626 33.2248 28.7552 32.942 29.0892C32.6592 29.4233 32.3059 29.6906 31.9075 29.8721C31.3747 30.1138 30.7821 30.1919 30.2048 30.0964C29.6275 30.0009 29.0916 29.7361 28.6649 29.3357C28.2738 28.9689 27.9895 28.503 27.8423 27.9874C27.6951 27.4718 27.6905 26.926 27.8291 26.408C27.9676 25.89 28.2441 25.4193 28.629 25.0461C29.014 24.6729 29.4929 24.411 30.0149 24.2885C30.585 24.1553 31.1818 24.1945 31.7296 24.4011C32.2774 24.6077 32.7515 24.9723 33.0916 25.4488ZM183.602 69.8817L181.409 71.1499L181.796 71.8203C181.854 71.9166 181.883 72.0276 181.88 72.1399C181.876 72.2523 181.841 72.3613 181.777 72.4539C181.709 72.5596 181.645 72.6669 181.586 72.7817C181.538 72.8823 181.462 72.967 181.367 73.0257C181.272 73.0845 181.162 73.115 181.051 73.1135H180.278V75.6505H181.055C181.166 75.6496 181.276 75.6806 181.371 75.7399C181.466 75.7991 181.541 75.8842 181.589 75.9851C181.647 76.0972 181.71 76.2061 181.779 76.3118C181.843 76.404 181.879 76.5129 181.882 76.6251C181.886 76.7374 181.856 76.8483 181.798 76.9442L181.411 77.6158L183.607 78.8845L183.998 78.2107C184.054 78.1149 184.136 78.0367 184.235 77.985C184.333 77.9333 184.444 77.9101 184.555 77.9181H184.927C185.038 77.9109 185.149 77.9344 185.247 77.9861C185.345 78.0377 185.427 78.1154 185.484 78.2107L185.875 78.8885L188.064 77.6215L187.676 76.9459C187.621 76.8491 187.594 76.7387 187.598 76.6272C187.602 76.5158 187.637 76.4077 187.7 76.3152C187.767 76.2101 187.831 76.0993 187.889 75.9885C187.937 75.8879 188.013 75.8031 188.108 75.7443C188.203 75.6854 188.312 75.6548 188.424 75.6561H189.198V73.1192H188.417C188.305 73.1206 188.195 73.0901 188.1 73.0312C188.006 72.9723 187.93 72.8875 187.881 72.7868C187.823 72.6754 187.76 72.5663 187.693 72.4595C187.63 72.3672 187.595 72.2592 187.591 72.1478C187.587 72.0365 187.614 71.9262 187.669 71.8294L188.056 71.1539L185.868 69.8868L185.477 70.5533C185.42 70.6491 185.337 70.727 185.238 70.7785C185.139 70.83 185.027 70.8529 184.916 70.8448H184.544C184.433 70.8516 184.322 70.8281 184.223 70.7768C184.124 70.7254 184.041 70.6482 183.983 70.5533L183.602 69.8817ZM184.738 72.3061C185.149 72.3054 185.551 72.4267 185.893 72.6547C186.235 72.8827 186.501 73.2071 186.659 73.5868C186.776 73.8706 186.83 74.177 186.815 74.4839C186.801 74.7908 186.718 75.0907 186.574 75.362C186.43 75.6333 186.227 75.8693 185.981 76.0531C185.735 76.2368 185.451 76.3638 185.15 76.4249C184.848 76.4847 184.537 76.4771 184.239 76.4024C183.941 76.3278 183.663 76.188 183.426 75.9932C183.188 75.7984 182.997 75.5533 182.865 75.2757C182.734 74.998 182.665 74.6946 182.665 74.3874C182.664 74.1143 182.717 73.8438 182.82 73.5911C182.924 73.3385 183.076 73.1088 183.269 72.9152C183.461 72.7216 183.69 72.5678 183.942 72.4627C184.194 72.3576 184.465 72.3033 184.738 72.3027V72.3061ZM41.6104 11.3294L39.6019 12.8692L40.0598 13.4914C40.1291 13.5794 40.1716 13.6855 40.1824 13.7969C40.1932 13.9084 40.1717 14.0207 40.1206 14.1203C40.0645 14.2361 40.0163 14.3555 39.9763 14.4777C39.9403 14.5829 39.8756 14.6759 39.7896 14.7463C39.7036 14.8167 39.5996 14.8616 39.4894 14.876L38.7183 14.976L39.045 17.4902L39.8155 17.3902C39.9278 17.3749 40.0421 17.3916 40.1453 17.4383C40.2485 17.4851 40.3365 17.56 40.399 17.6544C40.4687 17.756 40.5443 17.8529 40.6257 17.9453C40.7009 18.0282 40.7507 18.1308 40.7693 18.241C40.7879 18.3513 40.7746 18.4646 40.7308 18.5675L40.4382 19.2823L42.7718 20.2539L43.0627 19.5397C43.1068 19.438 43.1784 19.3507 43.2695 19.2875C43.3605 19.2242 43.4673 19.1876 43.578 19.1817L43.7655 19.1607L43.9439 19.1323C44.0519 19.1121 44.1634 19.1219 44.2663 19.1606C44.3691 19.1992 44.4594 19.2653 44.5274 19.3516L45.0019 19.9738L47.0178 18.4118L46.5485 17.7959C46.4801 17.7071 46.4382 17.6009 46.4274 17.4893C46.4167 17.3778 46.4375 17.2655 46.4877 17.1652C46.543 17.0524 46.5911 16.9368 46.632 16.8186C46.6677 16.713 46.7323 16.6194 46.8184 16.5486C46.9046 16.4778 47.0089 16.4326 47.1195 16.4181L47.8899 16.3181L47.5632 13.8039L46.7928 13.9044C46.6808 13.92 46.5667 13.9038 46.4635 13.8576C46.3603 13.8114 46.2722 13.737 46.2093 13.6431C46.139 13.5412 46.0621 13.444 45.9791 13.3522C45.9114 13.2674 45.8675 13.1661 45.8521 13.0587C45.8368 12.9513 45.8504 12.8417 45.8916 12.7414L46.1842 12.0266L43.8507 11.0527L43.5604 11.7675C43.515 11.8684 43.443 11.9549 43.3521 12.0179C43.2612 12.0809 43.1548 12.1179 43.0445 12.1249L42.857 12.1408H42.6791C42.5711 12.1615 42.4593 12.152 42.3564 12.1132C42.2534 12.0744 42.1631 12.0078 42.0956 11.9209L41.6104 11.3294ZM43.0479 13.5863C43.6035 13.5881 44.1356 13.8105 44.5274 14.2044C44.8191 14.4986 45.0174 14.8724 45.0973 15.2789C45.1771 15.6853 45.1351 16.1064 44.9764 16.489C44.8176 16.8717 44.5494 17.1989 44.2052 17.4295C43.8611 17.6601 43.4565 17.7838 43.0422 17.7851C42.4867 17.7849 41.9539 17.5642 41.561 17.1715C41.2683 16.8782 41.0689 16.5049 40.9878 16.0986C40.9067 15.6923 40.9475 15.2711 41.1052 14.8879C41.2628 14.5048 41.5302 14.1768 41.8737 13.9452C42.2173 13.7136 42.6216 13.5887 43.036 13.5863H43.0479ZM26.6024 77.2016C26.7115 74.3794 26.6723 71.5527 26.4848 68.7346C26.0686 62.9152 25.1826 57.139 23.8354 51.4624C23.095 48.2681 22.1854 45.1272 21.2348 41.9931C21.2064 41.8965 21.0564 41.9232 21.0814 42.0232C24.0155 53.0885 26.424 64.5039 26.2547 76.0067C26.2081 79.2226 25.8354 82.3868 25.5411 85.5868C25.5411 85.6709 25.6718 85.7067 25.6928 85.6192C26.2905 82.8658 26.4882 79.9982 26.6024 77.2016Z" fill="#EBEBEB"/><path d="M21.0808 42.0093C21.0808 42.0093 16.6376 53.3081 17.6325 55.9456C18.6274 58.5831 20.678 59.1615 20.678 59.1615C20.678 59.1615 17.9592 63.0496 19.8592 66.6962C21.7587 70.3428 25.9814 70.1712 26.2723 74.3059C26.2638 74.3292 26.5007 57.0428 21.0808 42.0093Z" fill="#EBEBEB"/><path d="M25.788 67.1922C25.7949 67.1733 25.7949 67.1526 25.788 67.1337C25.2025 59.4195 23.755 51.7951 21.4726 44.403C21.4726 44.3814 21.4283 44.3865 21.4351 44.403C22.3127 47.7007 23.0903 51.0242 23.767 54.3689C23.1697 53.8024 22.4395 53.3953 21.6437 53.1848C21.6437 53.1848 21.6209 53.2019 21.6437 53.2076C22.48 53.5138 23.2317 54.0143 23.8368 54.6678C23.9647 55.3246 24.0931 55.9786 24.213 56.6331C23.2187 55.5218 21.842 54.9689 20.4664 54.4439C20.4637 54.4471 20.4623 54.4511 20.4624 54.4553C20.4624 54.4598 20.4637 54.4638 20.4664 54.4672C21.9039 55.0519 23.3334 55.8036 24.3056 57.0456C24.6446 58.9199 24.942 60.8005 25.1976 62.6877C24.4789 61.936 23.5582 61.408 22.5465 61.1672C22.5147 61.1672 22.4965 61.2047 22.5289 61.2138C23.5708 61.5195 24.5008 62.1226 25.205 62.949H25.2221C25.3092 63.61 25.3925 64.2746 25.4721 64.9428C24.9169 64.473 24.2908 64.0942 23.617 63.8206C23.5993 63.8206 23.5868 63.8422 23.617 63.8473C24.3058 64.1458 24.9363 64.5638 25.4795 65.082H25.5022C25.576 65.7166 25.6488 66.3507 25.7113 66.9831C24.7113 65.7716 23.4127 64.8418 21.9437 64.2854C21.9406 64.2853 21.9376 64.2858 21.9348 64.2869C21.932 64.2879 21.9294 64.2896 21.9272 64.2916C21.9249 64.2939 21.9231 64.2965 21.922 64.2994C21.9208 64.3024 21.9203 64.3055 21.9204 64.3087C21.9204 64.3113 21.9207 64.3144 21.9215 64.3178C21.9225 64.3207 21.9243 64.3232 21.9266 64.3252C21.9287 64.3276 21.9315 64.3294 21.9346 64.3303C21.9372 64.3314 21.9402 64.332 21.9437 64.332C23.3935 64.9716 24.6672 65.9523 25.6562 67.1905C25.6677 67.2036 25.6826 67.2132 25.6993 67.2182C25.7159 67.2232 25.7337 67.2235 25.7505 67.2189C25.8471 68.1911 25.9289 69.1627 26.0039 70.1382C26.0046 70.1408 26.0062 70.143 26.0084 70.1445L26.0158 70.1462L26.0226 70.1445C26.0248 70.143 26.0264 70.1408 26.0272 70.1382C25.9461 69.1723 25.866 68.1903 25.788 67.1922Z" fill="white"/><path d="M22.2092 52.0725C21.4029 51.5706 20.4817 51.2832 19.533 51.2378C19.5308 51.2378 19.5287 51.2384 19.5268 51.2395C19.5237 51.241 19.5212 51.2433 19.5194 51.2463C19.5177 51.2492 19.5169 51.2526 19.5171 51.256C19.517 51.2604 19.5185 51.2647 19.5215 51.268C19.5245 51.2713 19.5286 51.2733 19.533 51.2736C20.4484 51.4475 21.337 51.7418 22.1751 52.1486C22.2194 52.1582 22.241 52.0952 22.2092 52.0725ZM22.7546 64.0338C22.1346 63.6755 21.4513 63.4401 20.7421 63.3406C20.7194 63.3406 20.7171 63.373 20.7421 63.3781C21.4319 63.5173 22.0989 63.7514 22.7245 64.0736C22.7546 64.0963 22.7745 64.0554 22.7546 64.0338Z" fill="white"/><path d="M38.2867 56.1496C36.4074 57.4508 34.6815 58.9606 33.1419 60.6501C31.5533 62.4409 30.3281 64.5235 29.5345 66.7819C27.8459 71.4865 26.9379 76.4355 26.8464 81.4331C26.7896 84.2018 27.0544 86.9677 27.6362 89.6751C27.6387 89.6852 27.6431 89.6946 27.6492 89.703C27.655 89.7117 27.6625 89.7191 27.6713 89.7246C27.6802 89.7302 27.69 89.7339 27.7004 89.7354C27.7106 89.7364 27.7209 89.736 27.7311 89.7342C27.75 89.7294 27.7665 89.7177 27.7774 89.7015C27.7883 89.6852 27.7929 89.6655 27.7901 89.6462C27.0674 84.8685 27.095 80.0073 27.872 75.2382C28.6356 70.566 29.8345 65.4791 32.8026 61.6808C34.4083 59.6121 36.4316 57.9865 38.422 56.303C38.5294 56.2155 38.3953 56.0723 38.2867 56.1496Z" fill="#EBEBEB"/><path d="M27.5 78.0678C28.5689 76.3353 29.8253 74.7258 31.2466 73.2683C33.4693 71.0405 35.3409 69.4581 35.633 67.9871C35.9261 66.5166 33.4051 65.4286 33.4051 65.4286C33.4051 65.4286 36.4807 65.9842 37.3301 64.6911C38.179 63.3967 38.5784 55.9053 38.5784 55.9053C38.5784 55.9053 32.9818 59.9075 30.3722 65.5195C27.7625 71.132 27.5 78.0678 27.5 78.0678Z" fill="#EBEBEB"/><path d="M36.6395 57.8272C34.2849 60.1499 31.9139 62.6118 30.5167 65.6584C30.0432 66.7059 29.63 67.7796 29.2792 68.8743C29.2094 69.0976 29.1361 69.3192 29.0684 69.5408C29.0646 69.5524 29.0646 69.565 29.0684 69.5766C28.4097 71.8524 27.8786 74.1632 27.4775 76.4982C27.4775 76.513 27.4969 76.5198 27.5003 76.4982C27.7429 75.33 28.0071 74.163 28.3048 72.9749H28.3406C28.8045 72.7766 29.296 72.6506 29.798 72.601C29.8304 72.601 29.8264 72.5391 29.798 72.5425C29.3022 72.5645 28.8118 72.6547 28.3406 72.8107C28.6116 71.7493 28.9059 70.6897 29.2327 69.6482C30.3252 69.3848 31.4431 69.241 32.5667 69.2192M32.5667 69.2033C31.4711 69.1519 30.3734 69.2449 29.302 69.48L29.4804 68.9442C30.2029 68.7715 30.9359 68.6463 31.6747 68.5692V68.5459C30.9474 68.5596 30.2224 68.6476 29.5128 68.8084C29.8069 67.9089 30.1395 67.0225 30.5099 66.1516C30.8128 65.4457 31.1664 64.7627 31.5679 64.1079C32.1544 63.9593 32.7505 63.8517 33.352 63.7857V63.7573C32.7875 63.715 32.22 63.7712 31.6747 63.9232C31.9683 63.4457 32.2808 62.9802 32.6116 62.5277C33.8599 62.251 35.1395 61.9919 36.4241 62.2363C36.4267 62.2356 36.4291 62.2343 36.431 62.2325C36.433 62.2307 36.4345 62.2285 36.4355 62.226C36.4376 62.221 36.4378 62.2153 36.4361 62.2101C36.4326 62.2053 36.4284 62.2011 36.4236 62.1976C35.2037 61.8743 33.9906 62.0618 32.7736 62.301C32.9059 62.1226 33.0429 61.9437 33.1787 61.7772C33.9349 61.5434 34.7207 61.419 35.5122 61.4079M35.5122 61.3811C34.7775 61.3152 34.0366 61.3811 33.3247 61.5755C34.3633 60.2857 35.5088 59.0738 36.6429 57.8721C36.7219 57.8039 36.6804 57.7715 36.6253 57.8272L35.5122 61.3811Z" fill="white"/><path d="M36.5669 62.9336C36.1984 62.8782 35.8247 62.8656 35.4533 62.8961C35.409 62.8961 35.4124 62.9711 35.4533 62.9745C35.8209 62.9745 36.1902 62.9569 36.5578 62.9745C36.5639 62.973 36.5686 62.9694 36.572 62.9637C36.5738 62.9612 36.575 62.9583 36.5755 62.9553C36.5761 62.9522 36.5761 62.9491 36.5754 62.9461C36.573 62.9415 36.5705 62.9373 36.5669 62.9336ZM32.547 70.1984C31.9556 70.2105 31.3709 70.3274 30.8203 70.5438V70.5705C31.4038 70.4325 31.9675 70.3773 32.5436 70.279C32.5535 70.2734 32.5614 70.2649 32.5664 70.2546C32.5708 70.2437 32.5708 70.2315 32.5664 70.2205C32.562 70.2116 32.5553 70.2039 32.547 70.1984ZM31.3385 67.6887C31.0933 67.6875 30.8485 67.709 30.6073 67.7529C30.6043 67.754 30.6017 67.7557 30.5994 67.7578C30.5972 67.76 30.5954 67.7625 30.5942 67.7654C30.5928 67.7681 30.592 67.7711 30.5918 67.7741C30.5916 67.7771 30.5921 67.7802 30.5931 67.783C30.5961 67.7895 30.6008 67.7942 30.6073 67.7972C30.8565 67.8109 31.1064 67.7993 31.3533 67.7626C31.3578 67.7603 31.362 67.7574 31.3658 67.754C31.369 67.7501 31.3717 67.7457 31.3737 67.741C31.3751 67.7362 31.3759 67.7312 31.376 67.7262C31.3757 67.7162 31.3717 67.7067 31.3646 67.6995C31.3575 67.6928 31.3482 67.689 31.3385 67.6887Z" fill="white"/><path d="M11.0911 64.0969C12.9133 64.7169 14.6592 65.5421 16.2951 66.5565C17.991 67.6349 19.4629 69.0303 20.6303 70.6662C23.0815 74.085 24.9579 77.8812 26.1854 81.9048C26.8705 84.1333 27.2931 86.4341 27.4445 88.7605C27.4421 88.776 27.434 88.79 27.4218 88.7997C27.4129 88.8074 27.4022 88.8125 27.3908 88.8146C27.3793 88.8167 27.3675 88.8158 27.3565 88.8118C27.3456 88.8078 27.3359 88.8009 27.3285 88.7919C27.321 88.7829 27.3162 88.772 27.3144 88.7605C26.7987 84.7274 25.6551 80.7998 23.9252 77.1202C22.2195 73.5247 20.0786 69.6838 16.807 67.2969C15.0229 65.9957 13.0172 65.1435 11.0178 64.2446C10.9127 64.2094 10.9803 64.0554 11.0911 64.0969Z" fill="#EBEBEB"/><path d="M24.8795 79.3381C23.6133 78.1842 22.2243 77.1729 20.7375 76.3222C18.4176 75.0358 16.5432 74.1886 15.9687 73.0631C15.3943 71.9375 17.1795 70.4864 17.1795 70.4864C17.1795 70.4864 14.8193 71.6483 13.8346 70.7864C12.85 69.9256 10.8022 63.9688 10.8022 63.9688C10.8022 63.9688 16.2557 65.9108 19.6574 69.8483C23.0596 73.7869 24.8795 79.3381 24.8795 79.3381Z" fill="#EBEBEB"/><path d="M24.5945 78.11C21.8633 72.9901 18.2275 68.3441 13.2451 65.2964C14.5477 66.1341 15.7696 67.0911 16.8951 68.1549C15.8801 68.0994 14.8684 68.3126 13.9622 68.7731C13.9601 68.774 13.9582 68.7753 13.9566 68.777C13.9551 68.7787 13.9539 68.7806 13.9531 68.7827C13.9521 68.7849 13.9516 68.7872 13.9516 68.7896C13.9516 68.7919 13.9521 68.7942 13.9531 68.7964L13.9622 68.8055C14.9264 68.4424 15.9508 68.2691 16.9803 68.2941C16.9917 68.2979 17.0031 68.2979 17.0144 68.2941C17.1928 68.4606 17.3713 68.6339 17.5389 68.8055C17.0127 68.8299 16.49 68.9004 15.9764 69.0163V69.0396C16.5212 68.936 17.0753 68.8898 17.6298 68.9015C18.5417 69.8258 19.4011 70.8006 20.2036 71.8214C19.8367 71.7695 19.4629 71.7962 19.107 71.8998C19.1017 71.9003 19.0966 71.9027 19.0928 71.9066C19.0894 71.9105 19.0874 71.9156 19.0872 71.9208C19.0872 71.9265 19.0891 71.9312 19.0928 71.935C19.0966 71.9388 19.1014 71.9407 19.107 71.9407C19.4945 71.8975 19.8852 71.8927 20.2735 71.9265C20.4213 72.1163 20.5644 72.3072 20.7195 72.5112C19.604 72.4402 18.4843 72.5486 17.4031 72.8322C17.3855 72.8322 17.4031 72.8612 17.4031 72.8577C18.5273 72.624 19.6768 72.5356 20.8235 72.5947C20.9622 72.7737 21.0945 72.9702 21.2315 73.1555C20.5701 73.1408 19.9098 73.2178 19.2695 73.3845L19.2627 73.3884L19.2582 73.3941L19.2565 73.4021C19.2565 73.4047 19.2572 73.4072 19.2588 73.4095L19.2627 73.4174L19.2695 73.422C19.9394 73.2772 20.6258 73.2243 21.3099 73.2646C21.894 74.074 22.4567 74.8988 22.9974 75.7379C22.7062 75.6837 22.4063 75.7014 22.1235 75.7896M22.1235 75.8129C22.4358 75.7914 22.7495 75.8068 23.0582 75.8589C23.5445 76.6157 24.0131 77.3799 24.4639 78.1515C24.5338 78.2282 24.6428 78.1782 24.6014 78.11L22.1235 75.8129Z" fill="white"/><path d="M20.3521 73.8566C19.8156 73.8199 19.2771 73.8864 18.7657 74.0526V74.0759C19.2872 73.9862 19.8142 73.9314 20.343 73.9117C20.3822 73.9117 20.3913 73.86 20.3521 73.8566ZM15.5941 67.6071C15.3494 67.5878 15.1033 67.6015 14.8623 67.648C14.8373 67.648 14.8447 67.6946 14.8623 67.6929C15.1049 67.6662 15.3407 67.6753 15.5759 67.6702C15.585 67.6698 15.5928 67.6666 15.5992 67.6605C15.6046 67.6555 15.6081 67.6488 15.6094 67.6415C15.6106 67.6342 15.6094 67.6268 15.606 67.6202C15.6036 67.6147 15.5994 67.6101 15.5941 67.6071Z" fill="white"/><path d="M31.9134 96.2969H19.6021L21.3577 83.4185L21.8066 80.1162H29.71L30.1577 83.4185L31.9134 96.2969Z" fill="#DBDBDB"/><path d="M30.1579 83.4185H21.3579L21.8073 80.1162H29.7108L30.1579 83.4185Z" fill="#C7C7C7"/><path d="M30.8382 78.9712H20.6763V81.7672H30.8376L30.8382 78.9712Z" fill="#DBDBDB"/><path d="M11.7769 96.2975L33.9331 96.2276L56.0922 96.2009L100.409 96.1509L144.724 96.2009L166.883 96.2276L189.041 96.2975L166.883 96.3708L144.724 96.3935L100.409 96.4441L56.0922 96.3935L33.9331 96.3668L11.7769 96.2975Z" fill="#263238"/><path d="M100.992 118.058C147.157 118.058 184.581 113.951 184.581 108.885C184.581 103.819 147.157 99.7119 100.992 99.7119C54.8273 99.7119 17.4028 103.818 17.4028 108.885C17.4028 113.951 54.8267 118.058 100.992 118.058Z" fill="#EBEBEB"/><path d="M81.5324 107.366C80.8528 107.596 61.3739 107.657 60.6318 107.196C60.3392 107.017 60.1733 104.18 60.0483 100.74C60.0222 99.9785 60.0008 99.2167 59.9841 98.4549L58.4341 88.0492L71.4568 87.9634L72.6841 98.3725L72.6346 100.662C72.6346 100.662 80.104 103.976 80.867 104.576C81.6307 105.176 82.2119 107.135 81.5324 107.366Z" fill="#EB9481"/><path d="M81.5331 107.365C80.8536 107.595 61.3746 107.656 60.6325 107.195C60.2882 106.979 60.0973 102.83 59.9956 98.4545L72.6615 98.3721L72.6115 100.661C72.6115 100.661 80.0808 103.975 80.8445 104.575C81.6081 105.175 82.2132 107.134 81.5331 107.365Z" fill="white"/><path d="M81.5329 107.366C80.8528 107.596 61.3738 107.657 60.6318 107.196C60.3392 107.017 60.1733 104.18 60.0488 100.741L72.6045 100.662C72.6045 100.662 80.0716 103.976 80.8352 104.576C81.5988 105.177 82.2125 107.135 81.5329 107.366Z" fill="#A6A6A6"/><path d="M81.2502 106.579C77.9837 106.508 64.807 106.539 61.5746 106.722C61.5479 106.722 61.5479 106.738 61.5746 106.742C64.811 106.879 77.9871 106.742 81.2502 106.63C81.32 106.634 81.32 106.579 81.2502 106.579ZM79.5911 103.88C79.2276 103.871 78.8663 103.938 78.5303 104.077C78.1942 104.216 77.8908 104.423 77.6394 104.686C77.1811 105.154 76.9221 105.781 76.9166 106.437C76.9166 106.458 76.9524 106.458 76.9541 106.437C77.0632 105.792 77.3775 105.199 77.8503 104.747C78.3231 104.294 78.9292 104.007 79.5786 103.926C79.5846 103.928 79.5907 103.926 79.5967 103.923C79.6023 103.92 79.6065 103.914 79.6087 103.908C79.6093 103.905 79.6094 103.902 79.6088 103.899C79.6082 103.896 79.607 103.893 79.6053 103.891C79.6038 103.888 79.6017 103.886 79.5993 103.884C79.5968 103.882 79.594 103.881 79.5911 103.88ZM73.8808 100.983C72.5729 100.969 71.0604 101.435 70.2502 102.509C70.2217 102.55 70.2791 102.595 70.3183 102.574C71.4591 101.97 72.6612 101.491 73.9041 101.143C73.9179 101.138 73.93 101.129 73.9393 101.118C73.9486 101.107 73.9547 101.093 73.957 101.078C73.9593 101.064 73.9577 101.049 73.9525 101.035C73.9472 101.022 73.9383 101.009 73.9269 101C73.9137 100.99 73.8976 100.984 73.8808 100.983ZM74.8939 101.371C73.5848 101.371 72.0718 101.827 71.2655 102.899C71.2331 102.942 71.2905 102.986 71.3314 102.965C72.4709 102.361 73.6727 101.881 74.9155 101.536C74.9299 101.531 74.9429 101.523 74.953 101.512C74.963 101.5 74.9699 101.486 74.9729 101.472C74.9758 101.457 74.9747 101.441 74.9698 101.427C74.9648 101.413 74.956 101.4 74.9445 101.39C74.9303 101.378 74.9124 101.371 74.8939 101.371ZM75.9041 101.755C74.5979 101.755 73.0837 102.207 72.2735 103.283C72.2445 103.324 72.3019 103.367 72.3411 103.347C73.4824 102.745 74.6847 102.266 75.9274 101.918C75.9477 101.911 75.9647 101.897 75.9751 101.879C75.9855 101.86 75.9886 101.838 75.9837 101.817C75.9794 101.799 75.9692 101.784 75.9547 101.772C75.9403 101.761 75.9225 101.755 75.9041 101.755ZM76.9149 102.153C75.6058 102.138 74.0928 102.605 73.2842 103.681C73.2524 103.72 73.3149 103.764 73.3524 103.745C74.4929 103.141 75.6951 102.662 76.9382 102.316C76.9585 102.309 76.9755 102.295 76.986 102.277C76.9966 102.258 76.9998 102.236 76.995 102.216C76.9912 102.197 76.9811 102.181 76.9664 102.17C76.9518 102.158 76.9336 102.152 76.9149 102.153ZM62.3968 103.62C60.9394 103.62 60.9394 105.912 62.3968 105.903C63.8547 105.894 63.8649 103.611 62.3968 103.62Z" fill="#263238"/><path d="M75.283 97.4843C74.6211 96.9355 73.7864 97.4843 73.3813 98.0468C72.7085 99.0434 72.3065 100.198 72.2148 101.397C72.2148 101.403 72.2158 101.408 72.2177 101.413C72.2201 101.417 72.2232 101.422 72.2268 101.425C72.2307 101.429 72.2351 101.432 72.2398 101.434C72.245 101.435 72.2503 101.436 72.2558 101.437C72.2647 101.476 72.2881 101.51 72.3212 101.532C72.3542 101.555 72.3946 101.564 72.4342 101.558C72.4516 101.554 72.4683 101.547 72.4836 101.538C73.4188 100.895 74.4893 100.325 75.2029 99.4031C75.5932 98.8707 75.9165 98.0025 75.283 97.4843ZM73.6898 100.27C73.2421 100.6 72.7785 100.906 72.3415 101.254C72.5522 100.594 72.7962 99.944 73.0728 99.3082C73.2099 99.0052 73.3675 98.7135 73.5455 98.4332C73.8359 97.9985 75.1063 96.9002 75.2416 98.3218C75.3166 99.1008 74.2234 99.8786 73.6898 100.27Z" fill="#263238"/><path d="M68.9583 101.896C70.0946 102.075 71.2776 101.739 72.3856 101.561C72.4264 101.549 72.4616 101.524 72.4844 101.488C72.495 101.47 72.5022 101.45 72.5054 101.429C72.5097 101.391 72.5016 101.354 72.4822 101.321L72.4901 101.311C72.4916 101.306 72.4925 101.302 72.4929 101.298C72.4931 101.293 72.4922 101.288 72.4903 101.284C72.4884 101.28 72.4856 101.276 72.4822 101.273C71.5861 100.469 70.4936 99.9154 69.3151 99.6686C68.6265 99.5385 67.6532 99.7277 67.5651 100.583C67.4668 101.398 68.2969 101.791 68.9583 101.896ZM68.1651 101.155C67.2907 100.035 68.9606 99.9385 69.4634 100.054C69.7888 100.137 70.1068 100.24 70.4174 100.364C71.0564 100.629 71.6797 100.927 72.2873 101.257C71.7327 101.312 71.1884 101.411 70.6373 101.487C69.9793 101.575 68.6554 101.773 68.1651 101.157V101.155Z" fill="#263238"/><path d="M39.2837 39.9521C39.2837 39.9521 54.4041 65.7146 54.8041 67.5266C55.4303 70.3084 58.7218 97.7521 58.7218 97.7521L74.4632 97.4664C74.4632 97.4664 71.7536 73.2652 69.1843 65.2914C67.336 59.5493 53.9905 40.501 53.9905 40.501L39.2837 39.9521Z" fill="#1A2E35"/><path d="M73.0314 95.0548C70.7729 95.0383 62.7649 94.9469 59.7552 95.2326C59.7319 95.2326 59.7354 95.2741 59.7552 95.2758C60.7899 95.4258 70.7695 95.1951 73.028 95.1224C73.0319 95.1242 73.0362 95.1247 73.0405 95.1241C73.0447 95.1241 73.0488 95.1229 73.0524 95.1207C73.0564 95.1195 73.0599 95.1169 73.0621 95.1133C73.0647 95.11 73.067 95.1064 73.0689 95.1025C73.0701 95.0986 73.0708 95.0946 73.0712 95.0906C73.0709 95.0864 73.0701 95.0822 73.0689 95.0781C73.0673 95.0743 73.0648 95.0709 73.0615 95.0684C73.0588 95.0653 73.0552 95.0629 73.0513 95.0616L73.0314 95.0548ZM41.8626 42.319C42.8547 43.8758 43.9035 45.3997 44.9138 46.9429C45.9229 48.4872 46.9365 50.0326 47.9354 51.5741C49.9297 54.6878 51.8729 57.8222 53.7649 60.9775C54.6893 62.4406 55.48 63.9839 56.1274 65.5889C56.7184 67.1939 57.1621 68.8494 57.453 70.5349C57.7632 72.2253 57.9666 73.9298 58.1558 75.6417C58.3498 77.4565 58.553 79.2736 58.7655 81.0929C59.1795 84.8111 59.5958 88.5304 60.0143 92.2508C60.0626 92.7059 60.1126 93.1616 60.1592 93.6207C60.1593 93.6328 60.1558 93.6446 60.1491 93.6547C60.1424 93.6648 60.1329 93.6726 60.1217 93.6772C60.1106 93.6819 60.0983 93.6831 60.0865 93.6807C60.0746 93.6783 60.0637 93.6725 60.0552 93.6639C60.0494 93.6584 60.0448 93.6516 60.0418 93.6442C60.0388 93.6367 60.0373 93.6287 60.0376 93.6207C59.574 89.9258 59.1888 86.2184 58.7694 82.5253C58.559 80.6833 58.3497 78.8413 58.1416 76.9991C57.9467 75.2838 57.7598 73.5684 57.4956 71.8656C57.2525 70.1856 56.8821 68.5264 56.3876 66.9025C55.8439 65.2187 55.1146 63.6007 54.2132 62.0781C52.3893 58.9264 50.4666 55.8247 48.5314 52.7361C46.5956 49.6463 44.6189 46.5128 42.5837 43.4446C42.3346 43.0655 42.0789 42.6908 41.8166 42.3207C41.7933 42.3156 41.8433 42.2906 41.8626 42.319Z" fill="#263238"/><path d="M44.2385 108.883C43.5254 108.839 24.9993 101.324 24.4851 100.611C24.4632 100.569 24.4534 100.521 24.4567 100.474C24.4567 99.8357 25.743 97.6039 26.8146 94.6721C26.8828 94.4937 27.3612 93.6164 28.0231 92.4459C29.8623 89.23 33.068 83.709 33.068 83.709L45.176 88.5329L39.3794 97.2181L38.4788 99.3209C38.4788 99.3209 44.1606 105.203 44.6385 106.036C45.1169 106.868 44.9521 108.923 44.2385 108.883Z" fill="#EB9481"/><path d="M44.2385 108.884C43.5255 108.84 24.9993 101.325 24.4851 100.612C24.4631 100.569 24.4533 100.522 24.4567 100.475C24.4567 99.8365 25.7431 97.6047 26.8147 94.6723C26.8829 94.4939 27.3613 93.6167 28.0232 92.4468L39.3925 97.2138L38.5005 99.3161C38.5005 99.3161 44.18 105.198 44.6584 106.031C45.1368 106.863 44.9522 108.923 44.2385 108.884Z" fill="white"/><path d="M44.2388 108.884C43.5252 108.839 24.9991 101.325 24.4854 100.612C24.2871 100.34 25.115 97.8081 26.8184 94.6729L38.4786 99.3183C38.4786 99.3183 44.1621 105.197 44.6422 106.043C45.1224 106.89 44.9525 108.923 44.2388 108.884Z" fill="#A6A6A6"/><path d="M44.2683 108.054C41.2689 106.764 29.0439 101.841 25.9785 100.796C25.9518 100.796 25.9467 100.796 25.9785 100.814C28.9263 102.158 41.1905 106.98 44.2569 108.113C44.316 108.127 44.341 108.081 44.2683 108.054ZM43.7427 104.922C43.4103 104.775 43.051 104.7 42.6876 104.7C42.3243 104.7 41.9649 104.775 41.6325 104.922C41.0341 105.185 40.56 105.668 40.3086 106.271C40.3086 106.288 40.3353 106.303 40.3444 106.285C40.6881 105.725 41.2044 105.291 41.8157 105.049C42.4271 104.808 43.1003 104.771 43.7342 104.945C43.7362 104.944 43.738 104.943 43.7395 104.941C43.741 104.939 43.7421 104.937 43.7427 104.935C43.7436 104.933 43.744 104.931 43.744 104.929C43.744 104.926 43.7436 104.924 43.7427 104.922ZM39.5359 100.091C38.3314 99.5879 36.753 99.4521 35.6007 100.145C35.5575 100.17 35.6007 100.232 35.6376 100.229C36.9217 100.099 38.2159 100.106 39.4984 100.25C39.5199 100.252 39.5412 100.246 39.5581 100.232C39.5746 100.218 39.5851 100.199 39.5876 100.177C39.59 100.159 39.586 100.14 39.5763 100.125C39.5667 100.11 39.5526 100.098 39.5359 100.091ZM40.3263 100.834C39.1217 100.33 37.5433 100.194 36.3905 100.888C36.3478 100.912 36.3905 100.975 36.428 100.972C37.7117 100.839 39.006 100.846 40.2882 100.993C40.3097 100.995 40.3311 100.988 40.3478 100.975C40.3645 100.961 40.3751 100.941 40.3774 100.92C40.3796 100.902 40.3757 100.883 40.3663 100.868C40.357 100.852 40.3432 100.84 40.3263 100.834ZM41.1297 101.575C39.9217 101.072 38.341 100.938 37.1944 101.629C37.1461 101.656 37.1944 101.717 37.2285 101.713C38.5121 101.581 39.8064 101.589 41.0888 101.735C41.0992 101.737 41.11 101.736 41.1204 101.734C41.1308 101.732 41.1406 101.727 41.149 101.72C41.1577 101.714 41.1651 101.706 41.1706 101.697C41.1762 101.688 41.1799 101.678 41.1814 101.667C41.1844 101.648 41.181 101.629 41.1717 101.612C41.1618 101.596 41.1471 101.583 41.1297 101.575ZM41.9092 102.315C40.703 101.812 39.1257 101.677 37.9734 102.366C37.9268 102.396 37.9734 102.456 38.0092 102.453C39.293 102.321 40.5872 102.328 41.8694 102.474C41.8899 102.478 41.9108 102.473 41.928 102.462C41.9452 102.45 41.9574 102.433 41.9621 102.412C41.9672 102.393 41.9645 102.372 41.9547 102.354C41.9447 102.336 41.9285 102.322 41.9092 102.315ZM27.903 98.2215C26.5399 97.6857 25.6961 99.801 27.0626 100.344C28.4291 100.887 29.2785 98.7646 27.903 98.2215Z" fill="#263238"/><path d="M42.1467 97.3773C41.7347 96.62 40.7643 96.8075 40.1717 97.1842C39.1751 97.854 38.3713 98.7731 37.8404 99.8501C37.8386 99.8543 37.8372 99.8586 37.8364 99.8631C37.8364 99.8677 37.837 99.8724 37.8381 99.8773C37.84 99.8814 37.8422 99.8852 37.8449 99.8887C37.8484 99.8918 37.8522 99.8945 37.8563 99.8967C37.8563 99.9933 37.9205 100.107 38.0347 100.075C39.1387 99.8217 40.3535 99.6893 41.3404 99.1069C41.9165 98.7785 42.5336 98.0921 42.1467 97.3773ZM39.6239 99.3592C39.0887 99.4967 38.5427 99.6075 38.0097 99.7643C38.451 99.2321 38.9184 98.7236 39.412 98.2387C39.6544 98.0069 39.912 97.7925 40.1847 97.5955C40.6069 97.3041 42.2035 96.7626 41.7898 98.1319C41.5722 98.8961 40.2682 99.1967 39.6239 99.3592Z" fill="#263238"/><path d="M34.6345 99.0913C35.6248 99.6754 36.8339 99.8061 37.9368 100.069C38.0504 100.095 38.126 99.9811 38.1152 99.8902L38.1271 99.8862C38.1306 99.8839 38.1339 99.8812 38.1368 99.8783C38.142 99.8714 38.1444 99.8629 38.1436 99.8544C38.1442 99.8497 38.1442 99.8449 38.1436 99.8402C37.6141 98.7577 36.8093 97.8335 35.8101 97.1601C35.2271 96.7794 34.251 96.5896 33.8373 97.3385C33.4231 98.0868 34.0549 98.7498 34.6345 99.0913ZM34.1726 98.1032C33.7674 96.7351 35.3623 97.2777 35.7782 97.5669C36.0506 97.7639 36.3074 97.979 36.5487 98.2123C37.0405 98.6982 37.5074 99.2087 37.9476 99.7419C37.4123 99.5845 36.8771 99.472 36.3419 99.3345C35.6924 99.1646 34.401 98.8714 34.1726 98.1032Z" fill="#263238"/><path d="M40.3206 96.7292L26.7627 91.2496C26.7627 91.2496 43.5655 66.5053 43.6866 64.6633C43.7991 63.0337 36.4172 48.8383 36.128 44.7002C35.8144 40.2337 37.9587 36.7797 40.3206 34.6343L54.2996 40.5871C54.2996 40.5871 52.8241 42.9491 50.5087 44.2655C50.5087 44.2655 57.6763 60.6729 57.2701 65.7189C56.8633 70.7644 40.3206 96.7292 40.3206 96.7292Z" fill="#1A2E35"/><path d="M40.9205 94.1472C38.9585 93.3035 39.5364 93.516 37.5671 92.687C36.604 92.2853 30.2773 89.6495 29.3057 89.4302C29.2841 89.4302 29.2699 89.4586 29.2898 89.4694C30.1335 89.9876 36.5114 92.5063 37.4813 92.8978C39.4671 93.6807 38.9085 93.4342 40.8977 94.1932C40.9002 94.196 40.9033 94.1982 40.9068 94.1995C40.9105 94.2008 40.9143 94.2018 40.9182 94.2023C40.9221 94.2018 40.9259 94.2008 40.9296 94.1995C40.9331 94.1982 40.9362 94.196 40.9387 94.1932C40.9413 94.1905 40.9433 94.1873 40.9447 94.1838C40.946 94.1803 40.9467 94.1766 40.9466 94.1728C40.9467 94.169 40.946 94.1653 40.9447 94.1618C40.9433 94.1583 40.9413 94.1551 40.9387 94.1523C40.9331 94.1497 40.9271 94.148 40.921 94.1472M52.8068 43.9643C51.3516 44.3059 49.9277 44.7691 48.55 45.3489C48.539 45.353 48.5299 45.3612 48.5246 45.3717C48.5194 45.3823 48.5183 45.3944 48.5216 45.4057C48.5257 45.4168 48.5339 45.4257 48.5445 45.4308C48.5551 45.4359 48.5673 45.4367 48.5784 45.433C50.0057 45.0165 51.4597 44.6256 52.8602 44.1319C52.879 44.1222 52.8937 44.1062 52.9018 44.0868C52.9099 44.0673 52.9108 44.0456 52.9044 44.0255C52.898 44.0054 52.8847 43.9882 52.8668 43.977C52.849 43.9658 52.8277 43.9613 52.8068 43.9643Z" fill="#263238"/><path d="M51.1422 43.3969C51.0058 43.639 50.8869 43.889 50.7853 44.1469C50.6858 44.3433 50.6252 44.5571 50.6069 44.7765C50.6069 44.8191 50.6638 44.8276 50.691 44.8015C50.8394 44.6268 50.9598 44.4301 51.0478 44.2185C51.1672 43.9884 51.2905 43.7594 51.4047 43.5236C51.4169 43.4976 51.4212 43.4686 51.4172 43.4401C51.4132 43.4117 51.4009 43.385 51.3819 43.3634C51.3631 43.3415 51.3381 43.3259 51.3102 43.3186C51.2823 43.3113 51.2528 43.3127 51.2257 43.3225C51.1892 43.3354 51.1592 43.3621 51.1422 43.3969Z" fill="#263238"/><path d="M51.3172 42.0797C51.4047 41.8638 51.4956 41.649 51.5797 41.4325C51.6632 41.2166 51.7831 40.995 51.8701 40.7677C51.8701 40.7325 51.9399 40.7677 51.9257 40.795C51.8382 41.0109 51.7831 41.2382 51.7166 41.4598C51.6502 41.6814 51.5723 41.9047 51.5007 42.1263C51.3644 42.5426 51.204 42.9492 51.0195 43.3462C51.249 43.3576 51.4781 43.3175 51.6899 43.2285C51.9067 43.0906 52.073 42.8863 52.1644 42.6462C52.2877 42.4064 52.4019 42.1598 52.5218 41.9206C52.6755 41.6607 52.8043 41.3891 52.9081 41.1058C52.9081 41.0717 52.978 41.1058 52.9638 41.1325C52.7742 41.7314 52.5248 42.3097 52.2195 42.8587C52.0899 43.0712 51.9882 43.2962 51.749 43.3785C51.4899 43.4501 51.2195 43.4694 50.9536 43.4337C50.9178 43.4337 50.8769 43.4337 50.8894 43.3802C51.0144 42.9354 51.1547 42.4996 51.3172 42.0791M50.7218 44.8001C51.2218 45.8547 51.6587 46.9052 52.1223 47.9558C52.5865 49.0064 52.9968 50.0229 53.4144 51.0683C54.2469 53.1778 54.9926 55.3205 55.6496 57.491C55.9751 58.5666 56.2725 59.6505 56.5416 60.7416C56.8361 61.8293 57.0513 62.937 57.1854 64.0558C57.3026 65.174 57.2033 66.3042 56.8928 67.3848C56.5734 68.4526 56.1622 69.4907 55.6638 70.4876C55.357 71.1189 55.2547 71.0348 55.4013 70.716C55.8189 69.8104 56.9104 67.07 56.8286 64.7649C56.7658 63.6624 56.5961 62.5686 56.3218 61.499C56.0667 60.3963 55.78 59.3011 55.4621 58.2149C54.8411 56.0712 54.103 53.9717 53.3462 51.8757C52.9201 50.7069 52.4706 49.5405 52.0337 48.3717C51.5968 47.203 51.1587 46.1297 50.6683 44.8399C50.6581 44.7876 50.7081 44.7683 50.7223 44.8001M40.4348 41.316C40.6757 41.2268 41.0166 41.3746 41.2627 41.416C41.8945 41.5229 42.5377 41.5444 43.1752 41.4802C43.4937 41.4713 43.8058 41.388 44.0865 41.2371C44.5479 40.9007 44.9435 40.4824 45.2536 40.003C45.6227 39.5128 45.9273 38.9771 46.1598 38.4092C46.1598 38.3734 46.2206 38.4092 46.2098 38.4319C46.1076 38.7558 46.0314 39.0825 45.9172 39.4007C45.7922 39.6996 45.6428 39.9882 45.4695 40.2621C45.1882 40.8134 44.7728 41.2853 44.2615 41.6342C43.6996 41.9558 42.9752 41.9268 42.349 41.9001C41.9854 41.8797 41.625 41.8321 41.2678 41.7575C41.011 41.7018 40.5797 41.6609 40.4172 41.4354C40.403 41.4205 40.395 41.4008 40.395 41.3802C40.395 41.3597 40.403 41.3399 40.4172 41.3251L40.4348 41.316Z" fill="#263238"/><path d="M30.6354 88.6535C32.865 85.2735 35.0545 81.8672 37.2036 78.4354C39.3543 75.0028 41.4708 71.5428 43.553 68.0553C44.053 67.224 44.6235 66.3956 44.9462 65.4717C45.303 64.4712 45.0979 63.5058 44.7842 62.5178C44.1857 60.7298 43.4877 58.9766 42.6933 57.2666C41.9121 55.5411 41.07 53.845 40.2422 52.1422C39.4144 50.4393 38.5473 48.7587 37.9087 46.9842C37.3274 45.2871 37.0899 43.5308 37.5519 41.7723C38.0138 40.0138 38.9791 38.4615 40.0104 36.9984C40.2774 36.6143 40.5541 36.232 40.8252 35.832C40.8467 35.8013 40.8899 35.832 40.87 35.8661C39.7411 37.3899 38.6416 39.0047 37.9797 40.795C37.6421 41.6774 37.4535 42.6099 37.4217 43.5541C37.4164 44.5124 37.5644 45.4654 37.8604 46.3768C38.4098 48.153 39.2752 49.8308 40.0831 51.5013C40.8916 53.1717 41.7536 54.8962 42.549 56.6178C43.3698 58.3492 44.0998 60.1222 44.7359 61.9297C45.007 62.732 45.3172 63.574 45.2945 64.4314C45.2746 65.3751 44.8252 66.2178 44.3592 67.0189C42.3188 70.4723 40.2321 73.8982 38.0996 77.2956C35.9586 80.7056 33.7808 84.0923 31.5666 87.4553L30.728 88.7251C30.6769 88.7774 30.603 88.7132 30.6354 88.6535Z" fill="#263238"/><path d="M55.0796 24.8495C56.3426 27.6222 62.7182 31.4438 65.7029 31.8569C67.8438 32.1478 74.3727 32.2535 76.6085 31.912C79.233 31.5086 77.2256 24.429 75.679 24.6137C71.4222 25.1103 67.8085 25.6643 66.6699 25.6643C65.2841 25.6643 57.7773 23.2523 56.8727 22.9109C54.6568 22.0853 54.3659 23.2751 55.0796 24.8495Z" fill="#FFC3BD"/><path d="M55.121 25.5883C56.7266 27.9559 62.0261 31.8275 62.0261 31.8275L62.7653 30.1911L65.2124 24.7843C65.2124 24.7843 60.3374 23.2513 57.1261 22.5513C56.749 22.4645 56.3652 22.41 55.9789 22.3883C53.9204 22.2758 53.742 23.5445 55.121 25.5883Z" fill="#007CFF"/><path d="M55.1204 25.5906C56.7261 27.9576 62.0261 31.8292 62.0261 31.8292L62.7648 30.1929C60.7846 27.89 57.7267 24.4008 55.9693 22.3957C53.9199 22.2781 53.742 23.5463 55.1204 25.5906Z" fill="#263238"/><path d="M61.0704 30.7294C61.475 29.734 63.2772 25.7266 63.7267 24.8902C63.7551 24.8311 63.8017 24.8385 63.7761 24.8902C62.9948 26.8839 62.1046 28.8333 61.1096 30.7294C61.1096 30.7652 61.054 30.7522 61.0704 30.7294Z" fill="#263238"/><path d="M74.897 24.6866C77.4856 24.3469 80.6896 23.7662 83.4027 24.919C83.7072 25.0389 83.9635 25.2577 84.1294 25.5406C85.3527 26.0372 85.4862 26.7645 85.4862 26.7645C86.4873 27.5758 86.5231 28.3406 86.5231 28.3406C86.5231 28.3406 87.9504 28.9247 87.7379 29.8645C87.4987 30.9043 85.5345 29.8645 84.1061 29.9497C80.8265 30.1543 76.235 31.3599 75.2879 31.5122L74.897 24.6866Z" fill="#FFC3BD"/><path d="M80.5218 27.7462C81.4803 27.6804 82.4422 27.6804 83.4007 27.7462C84.4456 27.8786 85.4786 28.0882 86.4922 28.374C86.4954 28.3752 86.4987 28.3757 86.5021 28.3754C86.5054 28.3751 86.5086 28.374 86.5115 28.3723C86.5176 28.3694 86.5223 28.3642 86.5245 28.3578C86.5268 28.3514 86.5264 28.3444 86.5235 28.3382C86.5202 28.3325 86.5149 28.3281 86.5087 28.3257C84.6067 27.5775 82.5354 27.3672 80.5218 27.7178C80.4752 27.711 80.4752 27.7502 80.5218 27.7462Z" fill="#FFC3BD"/><path d="M80.1134 25.7852C81.0549 25.7852 82.7106 25.8318 85.481 26.7983L80.1134 25.7852ZM85.481 26.7806C83.8217 25.9403 81.9609 25.5795 80.1077 25.7386C80.056 25.7386 80.0492 25.7818 80.1077 25.7852L85.481 26.7806ZM79.9112 24.7125C81.5163 24.7943 82.5117 25.0517 84.1208 25.5358L79.9112 24.7125ZM84.1208 25.5181C82.3498 24.7767 81.6237 24.7068 79.9038 24.6426C79.8487 24.6653 79.8509 24.7119 79.9038 24.7119L84.1208 25.5181Z" fill="#263238"/><path d="M80.171 30.4482C79.7539 30.6426 79.8551 30.8624 80.6528 31.0465C81.4505 31.2306 84.7562 31.2857 84.6403 32.442C84.5238 33.5977 79.8551 33.4511 78.5533 33.042C77.2511 32.6329 75.2886 31.513 75.2886 31.513C76.213 31.3596 78.3818 30.7715 80.171 30.4482Z" fill="#FFC3BD"/><path d="M58.5472 29.2702C58.5268 29.777 58.4809 30.2815 58.4097 30.7838C58.4078 30.7734 58.4088 30.7627 58.4126 30.7529C58.4164 30.7431 58.4229 30.7345 58.4313 30.7281C58.5273 30.4639 58.6239 30.1923 58.7399 29.9423H58.7666C58.7097 30.2207 58.6258 30.491 58.5148 30.7531C58.4631 30.8781 58.4131 31.0014 58.358 31.1247C58.322 31.3481 58.282 31.5709 58.2381 31.7929C58.1512 32.2111 57.7569 33.9855 57.6552 34.2855C57.637 34.348 57.562 34.3338 57.5677 34.2628C57.5893 33.9355 57.9245 32.0878 57.9853 31.6679C58.1148 30.8565 58.2887 30.052 58.5058 29.2594C58.5205 29.2435 58.5506 29.2554 58.5472 29.2702Z" fill="#263238"/><path d="M55.5156 42.5456C54.7361 42.9456 53.7315 43.5671 49.2145 42.6774C48.9279 42.6187 48.6439 42.5479 48.3633 42.4649C44.1281 41.191 39.0349 36.2007 38.7588 35.8541C38.7588 35.8541 45.4895 24.9251 54.1008 21.4893C55.1713 21.0654 56.9247 21.8836 57.89 22.9024C58.2361 23.2598 58.9923 25.9507 58.7031 28.1228C58.4139 30.295 56.2935 42.1456 55.5156 42.5456Z" fill="#007CFF"/><path d="M54.9094 28.0176C54.9412 28.4795 54.8769 28.943 54.7207 29.3789C54.6829 28.9168 54.7473 28.452 54.9094 28.0176ZM51.9719 35.4681C51.9912 35.6982 51.9832 35.9301 51.9491 36.1579C51.9207 36.388 51.865 36.6136 51.7832 36.8301C51.7628 36.5995 51.7706 36.3673 51.8065 36.1386C51.8368 35.909 51.8916 35.6855 51.9719 35.4681ZM46.9844 29.8329C47.0182 30.2963 46.9538 30.7615 46.7952 31.1982C46.7595 30.7348 46.824 30.2692 46.9844 29.8329ZM56.8281 35.0812C56.8463 35.3122 56.8387 35.5427 56.8054 35.7727C56.777 36.0028 56.7207 36.2283 56.6372 36.4443C56.6058 35.9814 56.6708 35.5176 56.8281 35.0812ZM50.4264 28.8698C50.6554 28.8501 50.886 28.8571 51.1133 28.8908C51.3436 28.92 51.5671 28.9761 51.7838 29.059C51.323 29.0935 50.8602 29.029 50.4264 28.8698ZM44.7633 34.246C45.2248 34.2131 45.6881 34.2777 46.123 34.4357C45.6614 34.4722 45.1973 34.4074 44.7633 34.246ZM51.4338 40.063C51.8949 40.0309 52.3575 40.0961 52.7917 40.2545C52.3306 40.2901 51.8671 40.2248 51.4338 40.063ZM43.0167 37.9499C43.4787 37.9198 43.9418 37.9874 44.3758 38.1482C43.9137 38.1836 43.4494 38.1162 43.0167 37.9499ZM54.8667 22.7823C55.328 22.7493 55.7911 22.8138 56.2258 22.9715C55.7645 23.0045 55.3015 22.9401 54.8667 22.7823ZM53.2167 24.6477C53.0695 24.8252 52.9012 24.9841 52.7156 25.121C52.535 25.2615 52.3393 25.3813 52.1321 25.4783C52.2791 25.3015 52.4444 25.1443 52.6281 25.0068C52.8099 24.8647 53.0077 24.7448 53.2167 24.6477ZM44.2423 29.942C44.0927 30.1174 43.9234 30.2751 43.7378 30.4119C43.5594 30.5555 43.3633 30.6756 43.1542 30.7693C43.3042 30.5943 43.4741 30.4363 43.6594 30.2994C43.8383 30.1573 44.0344 30.0374 44.2423 29.942ZM55.452 32.3301C55.302 32.5069 55.1336 32.6641 54.9469 32.8016C54.7684 32.9448 54.5719 33.0647 54.3639 33.1596C54.5133 32.9828 54.6826 32.8238 54.8684 32.6857C55.0479 32.5441 55.2439 32.4247 55.452 32.3301ZM48.1923 36.9079C48.0419 37.0848 47.8736 37.2426 47.6872 37.3812C47.5073 37.524 47.3128 37.6431 47.1037 37.7386C47.2541 37.5617 47.4224 37.4037 47.6088 37.2647C47.7892 37.1244 47.9853 37.0047 48.1923 36.9079ZM58.2321 26.0744C58.0817 26.2513 57.9133 26.4092 57.727 26.5482C57.5464 26.6885 57.3506 26.8082 57.1434 26.9051C57.2938 26.7289 57.4622 26.5717 57.6486 26.4335C57.8289 26.291 58.0234 26.1713 58.2321 26.0744ZM58.0775 31.3823C57.901 31.2325 57.7424 31.0628 57.6048 30.8766C57.4628 30.697 57.343 30.5009 57.248 30.2926C57.4253 30.441 57.5823 30.6085 57.719 30.7948C57.8592 30.9778 57.9787 31.1736 58.0775 31.3823ZM41.9008 35.0357C41.7241 34.887 41.5655 34.718 41.4281 34.5323C41.2874 34.3518 41.1676 34.1559 41.0713 33.9482C41.4185 34.2511 41.7007 34.6207 41.9008 35.0357ZM56.1139 39.3846C55.9367 39.237 55.7782 39.0683 55.6417 38.8823C55.5018 38.7013 55.3821 38.5054 55.2849 38.2982C55.4614 38.4479 55.6184 38.6158 55.7559 38.8022C55.8972 38.9825 56.0166 39.1766 56.1139 39.3846ZM48.9912 26.9903C48.8145 26.8419 48.6559 26.6733 48.5184 26.488C48.3788 26.3065 48.2592 26.1105 48.1616 25.9033C48.3385 26.0518 48.4955 26.2193 48.6327 26.4056C48.7728 26.5878 48.8921 26.7825 48.9906 26.9897M49.1162 41.8596C48.9395 41.7109 48.7808 41.5419 48.6434 41.3562C48.5034 41.175 48.3838 40.9789 48.2866 40.7715C48.4639 40.9193 48.623 41.088 48.7594 41.2738C48.9003 41.456 49.0192 41.6513 49.1162 41.8596ZM50.3258 33.0886C50.151 32.9387 49.9935 32.7697 49.8565 32.5846C49.713 32.406 49.5931 32.2097 49.4997 32.0005C49.6758 32.1509 49.8323 32.3194 49.969 32.5062C50.1111 32.6852 50.2304 32.8812 50.3258 33.0886Z" fill="white"/><path d="M56.3103 25.8074C55.2114 25.6034 54.1785 23.329 54.0483 21.7977C54.0483 21.6943 54.5603 20.8614 55.1472 19.8205C55.504 19.1858 55.8785 18.471 56.1785 17.7921C56.241 17.6563 56.5989 18.1188 56.5989 18.1188L57.3807 18.7034L59.9603 20.6864C59.3456 21.4246 58.8238 22.2354 58.4063 23.1006C58.3655 23.1948 58.3339 23.2928 58.312 23.3932V23.4415C58.1535 24.021 57.3631 25.9983 56.3103 25.8074Z" fill="#FFC3BD"/><path d="M58.3117 23.4088V23.4588C58.1587 23.4107 58.0096 23.351 57.8657 23.2804C55.0435 21.96 56.2924 18.1702 56.2924 18.1702L57.4003 18.7191L59.9799 20.702C59.3657 21.4404 58.8439 22.251 58.4259 23.1157C58.3789 23.2096 58.3407 23.3078 58.3117 23.4088ZM62.9265 12.6196C62.9265 12.6196 64.2162 13.4395 64.2236 14.8992C64.2305 16.3594 63.6401 17.7867 63.51 17.8401C63.3799 17.8935 62.9265 12.6196 62.9265 12.6196Z" fill="#263238"/><path d="M56.5172 13.3799C55.7417 14.9219 55.7826 19.7458 56.7513 20.9964C58.1553 22.8009 60.8541 23.4225 62.4956 21.6401C64.0797 19.9106 63.9229 13.6157 62.7882 12.4418C61.1468 10.6998 57.6661 11.1106 56.5172 13.3799Z" fill="#FFC3BD"/><path d="M60.6336 17.2446C60.6336 17.2446 60.6069 17.2628 60.6103 17.273C60.6285 17.6037 60.5927 17.9878 60.2983 18.1003V18.1168C60.6642 18.0628 60.7035 17.5571 60.6336 17.2446Z" fill="#263238"/><path d="M60.3349 16.9107C59.7997 16.8823 59.7872 17.9437 60.2792 17.9704C60.7718 17.9971 60.7769 16.934 60.3349 16.9107ZM62.4326 17.2442C62.4364 17.2442 62.44 17.245 62.4434 17.2465C62.4467 17.2485 62.4497 17.2508 62.4525 17.2533C62.4552 17.2562 62.4572 17.2597 62.4582 17.2636C62.4593 17.2673 62.4597 17.2711 62.4593 17.2749C62.4417 17.6033 62.4803 17.9897 62.7764 18.0988V18.1147C62.4093 18.0499 62.3593 17.5465 62.4326 17.2442Z" fill="#263238"/><path d="M62.7333 16.8981C63.2685 16.8663 63.2861 17.9294 62.7975 17.9578C62.3088 17.9862 62.2895 16.9248 62.7333 16.8981ZM60.0026 16.1032C60.142 16.0638 60.2787 16.0172 60.4128 15.9634C60.5658 15.9357 60.7052 15.8577 60.8088 15.7419C60.8343 15.6994 60.845 15.6498 60.8392 15.6006C60.8335 15.5515 60.8116 15.5057 60.777 15.4703C60.6986 15.4098 60.6054 15.3715 60.5071 15.3595C60.4088 15.3475 60.3091 15.3622 60.2185 15.4021C60.0201 15.4575 59.8496 15.5852 59.7407 15.76C59.7166 15.8006 59.705 15.8474 59.7073 15.8946C59.7096 15.9417 59.7257 15.9871 59.7536 16.0252C59.7815 16.0633 59.82 16.0923 59.8643 16.1087C59.9085 16.1251 59.9566 16.1281 60.0026 16.1174V16.1032ZM63.0929 16.5714C62.9511 16.5404 62.8112 16.5018 62.6736 16.4555C62.5197 16.4353 62.3771 16.3641 62.2685 16.2532C62.2405 16.2125 62.2271 16.1636 62.2304 16.1143C62.2337 16.065 62.2535 16.0182 62.2867 15.9816C62.3617 15.9175 62.4526 15.8747 62.5499 15.8577C62.6471 15.8407 62.7472 15.8501 62.8395 15.885C63.0384 15.9277 63.2157 16.0424 63.3355 16.2072C63.3526 16.2363 63.3634 16.2688 63.367 16.3023C63.3707 16.3359 63.3671 16.3699 63.3566 16.402C63.3462 16.4342 63.329 16.4637 63.3063 16.4887C63.2835 16.5137 63.2557 16.5335 63.2247 16.547C63.1835 16.5657 63.1376 16.5716 63.0929 16.564V16.5714ZM59.8878 20.3691C59.9662 20.4475 60.0412 20.5475 60.1605 20.5634C60.2838 20.5634 60.4054 20.5384 60.5174 20.4884C60.5174 20.4884 60.5372 20.4884 60.5174 20.5066C60.4706 20.572 60.4067 20.6233 60.3328 20.6547C60.2588 20.6862 60.1776 20.6967 60.098 20.685C60.0318 20.6664 59.9723 20.629 59.9268 20.5774C59.8812 20.5258 59.8515 20.4622 59.8412 20.3941L59.8389 20.3845L59.8412 20.3748L59.8469 20.3669L59.8554 20.3617C59.8609 20.3591 59.8671 20.3583 59.873 20.3595C59.8791 20.3606 59.884 20.3638 59.8878 20.3691ZM61.7941 18.9623C61.7941 18.9623 61.8083 19.4532 61.7941 19.6765C61.3458 19.6285 60.893 19.6441 60.4492 19.7231C60.3435 19.7458 60.356 19.6623 60.4759 19.6066C60.8331 19.4418 61.2318 19.3889 61.6196 19.4549C61.6424 19.3924 61.5799 18.6958 61.6196 18.6975C61.7952 18.7345 61.9657 18.7941 62.1259 18.8759C62.1259 17.8487 61.9617 16.8322 61.9776 15.8049C61.9776 15.7968 61.9808 15.789 61.9866 15.7832C61.9923 15.7775 62.0001 15.7742 62.0083 15.7742C62.0162 15.7742 62.0232 15.7773 62.0293 15.7833C62.0322 15.7861 62.0346 15.7894 62.0362 15.7931C62.0377 15.7969 62.0385 15.8009 62.0384 15.8049C62.2458 16.8924 62.3429 17.9981 62.3293 19.1049C62.3333 19.2333 61.8816 19.0089 61.7941 18.9623Z" fill="#263238"/><path d="M61.1384 19.5078C61.0219 19.8136 60.8065 20.0716 60.5264 20.2408C60.0861 20.4192 59.9435 20.0192 60.3855 19.6561C60.6181 19.5386 60.8786 19.4873 61.1384 19.5078Z" fill="#263238"/><path d="M60.6789 20.1496C60.6329 20.1878 60.5811 20.2185 60.5255 20.2405C60.1204 20.403 59.9602 20.0797 60.2863 19.7524C60.5079 19.7706 60.7607 19.853 60.6789 20.1496Z" fill="#FFC3BD"/><path d="M56.294 17.5101C56.8287 17.6726 57.4389 16.0681 57.6139 15.4663C57.7656 14.93 57.94 13.197 57.9707 13.0771C58.0008 12.9578 60.3025 14.605 61.6457 14.2067C62.9889 13.8084 63.8878 12.5987 63.9076 12.0408C63.9275 11.484 62.2576 10.0453 60.8162 9.97373C59.3747 9.90214 57.4156 11.7249 57.4156 11.7249C57.6527 11.4559 57.8669 11.1699 58.0582 10.8669C58.0349 10.8027 57.102 11.176 56.9287 11.8408C56.9287 11.8408 57.1162 11.0027 57.0463 10.9885C56.977 10.9743 56.2508 11.6891 56.4133 12.2391C56.4133 12.2391 55.5747 12.9663 55.4986 13.7635C55.4213 14.5601 55.669 17.3169 56.294 17.5101Z" fill="#263238"/><path d="M62.5909 13.9098C62.1942 14.1261 61.7664 14.2796 61.3227 14.3649C60.8231 14.4101 60.3202 14.3299 59.8596 14.1314C59.3972 13.9534 58.968 13.6991 58.5897 13.3791C58.2545 13.1036 57.9545 12.7678 57.596 12.5229C57.5051 12.4587 57.4034 12.5911 57.4585 12.6712C58.0562 13.5174 58.9108 14.1484 59.8954 14.4706C60.3527 14.6198 60.8408 14.6476 61.3121 14.5515C61.7834 14.4554 62.2217 14.2386 62.584 13.9223C62.584 13.9223 62.5982 13.9098 62.5909 13.9098Z" fill="#263238"/><path d="M56.8536 17.3666C56.8536 17.3666 56.0257 15.6854 55.278 15.9575C54.5308 16.2286 54.9877 18.5547 55.7547 18.982C55.9398 19.1014 56.1647 19.1425 56.3801 19.0962C56.5955 19.05 56.7837 18.9202 56.9036 18.7354L56.9195 18.707L56.8536 17.3666Z" fill="#FFC3BD"/><path d="M55.4268 16.6211C55.4268 16.6211 55.4092 16.6211 55.4268 16.6438C55.962 16.9586 56.1728 17.5194 56.3012 18.104C56.277 18.0424 56.2398 17.9867 56.192 17.9408C56.1443 17.8949 56.0872 17.8599 56.0246 17.8381C55.962 17.8163 55.8955 17.8084 55.8296 17.8148C55.7637 17.8212 55.6999 17.8417 55.6427 17.8751C55.6251 17.8751 55.6427 17.9075 55.6427 17.9075C55.7467 17.8918 55.8531 17.9085 55.9473 17.9555C56.0414 18.0025 56.1188 18.0774 56.1688 18.17C56.2522 18.3268 56.3188 18.4906 56.3688 18.6614C56.3847 18.7165 56.4813 18.7023 56.4722 18.6398C56.5989 17.9126 56.1904 16.812 55.4268 16.6211Z" fill="#263238"/><path d="M51.3876 24.8778C50.804 28.1846 54.3842 39.3306 56.6785 41.4119C60.091 44.5068 66.4029 46.5414 68.6916 47.2045C69.6893 47.496 72.4472 40.4687 71.437 40.0039C69.2768 39.0198 62.5677 36.9187 61.929 36.3931C61.2898 35.8681 57.4421 30.2988 55.7279 27.1022C53.4387 22.784 51.6773 23.2039 51.3876 24.8778Z" fill="#FFC3BD"/><path d="M69.7361 39.3857C69.7361 39.3857 72.7173 40.1471 74.6372 40.7346C75.7071 41.0636 77.4997 41.7909 78.1793 42.3926C79.1929 43.2948 82.8611 47.7085 81.8117 48.6232C80.8611 49.4522 78.1901 45.2568 78.1901 45.2568C78.1901 45.2568 81.2571 49.2499 79.9406 50.0363C78.6242 50.8221 76.2122 45.6801 76.2122 45.6801C76.2122 45.6801 79.2696 49.8539 77.7747 50.4471C76.644 50.8937 74.2173 46.359 74.2173 46.359C74.2173 46.359 76.8236 49.967 75.6747 50.4778C74.4105 51.0437 71.9111 46.9971 71.9111 46.9971C68.7798 47.7977 66.9565 46.9039 65.7469 46.1198C65.469 45.9284 69.7361 39.3857 69.7361 39.3857Z" fill="#FFC3BD"/><path d="M77.2186 43.8058C77.5971 44.2615 77.9823 44.7223 78.3175 45.2098C78.6546 45.6967 78.9684 46.1994 79.2579 46.716C79.652 47.3839 79.9675 48.095 80.1982 48.8354C79.6971 47.5507 78.5232 45.7854 78.1874 45.2973C77.8522 44.8098 77.5397 44.3041 77.2079 43.8166C77.1919 43.8053 77.2101 43.7939 77.2186 43.8058ZM75.4374 44.9865C76.0851 45.5007 77.4408 47.4109 77.9618 48.6922C77.9846 48.7456 77.9618 48.7422 77.9243 48.6922C77.1057 47.4504 76.2731 46.2179 75.4266 44.995C75.4141 44.9865 75.4283 44.9791 75.4374 44.9865ZM73.726 45.7348C74.5301 46.5797 75.1652 47.5706 75.5971 48.6541C75.6169 48.7058 75.5772 48.6973 75.5454 48.6541C74.9408 47.6695 74.4749 46.9013 73.6936 45.7598C73.6811 45.7348 73.7078 45.7081 73.726 45.7348Z" fill="#263238"/><path d="M54.6388 25.1178C51.9791 21.3496 50.386 22.745 50.9195 26.8382C51.4206 30.6655 52.7604 35.236 52.7604 35.236L59.4644 31.9769C59.4644 31.9769 56.3337 27.5337 54.6388 25.1178Z" fill="#007CFF"/><path d="M56.2573 26.6019C56.3465 26.8138 56.4107 27.036 56.4482 27.2627C56.4892 27.4922 56.5034 27.7233 56.4909 27.9558C56.4056 27.7416 56.3457 27.5181 56.3124 27.2899C56.2662 27.0637 56.2477 26.8326 56.2573 26.6019ZM52.2852 28.8928C52.7132 28.7189 53.1737 28.6394 53.6352 28.6598C53.4219 28.7454 53.2013 28.8051 52.9732 28.8388C52.7471 28.8843 52.5164 28.9024 52.2852 28.8928ZM52.4573 23.0303C52.2758 23.4565 52.0065 23.8396 51.667 24.1547C51.8469 23.7279 52.1162 23.3447 52.4568 23.0308M58.138 30.6547C57.9571 31.0805 57.6886 31.4634 57.3499 31.7786C57.5293 31.3519 57.798 30.9687 58.138 30.6547ZM53.4749 32.9394C53.0487 32.759 52.666 32.4893 52.3528 32.1485C52.7789 32.3297 53.1607 32.599 53.4749 32.9394Z" fill="white"/><path d="M55.5146 26.2549C55.8714 26.7475 56.2419 27.2299 56.6095 27.7145C56.5149 27.4706 56.4334 27.2217 56.3652 26.9691M56.3834 26.9691C56.4567 27.1623 56.5402 27.3515 56.6095 27.5537L56.7152 27.8464C56.7239 27.8664 56.7315 27.8867 56.7379 27.9072C57.0288 28.2844 57.3214 28.6611 57.589 29.0452C57.9192 29.5066 58.2368 29.9748 58.5578 30.4407C58.8788 30.9066 59.2021 31.4895 59.5481 31.9594C59.5799 32.0009 58.9646 32.2526 58.8987 32.2833C58.8328 32.314 58.797 32.26 58.8612 32.2401C58.9254 32.2202 59.414 31.9185 59.4197 31.9134C59.1004 31.4395 58.7288 31.0055 58.3936 30.5464C58.0584 30.0873 57.7265 29.6299 57.4038 29.1657C56.747 28.2202 56.1459 27.2395 55.493 26.2907L56.3834 26.9691ZM51.0782 29.5464C51.1858 29.7407 51.2828 29.9401 51.3692 30.1447L51.4799 30.3986C51.3265 29.6731 51.189 28.9384 51.0288 28.2134C51.3437 29.3922 51.6456 30.5745 51.9345 31.76C52.2061 32.893 52.5362 34.0384 52.693 35.1941C52.8965 35.1052 53.1071 35.0336 53.3226 34.9799C53.1066 35.1209 52.8735 35.2335 52.6288 35.3151C52.2773 34.1397 51.9854 32.9473 51.7544 31.7424C51.6816 31.4151 51.6135 31.0884 51.5442 30.7611C51.4692 30.5628 51.3782 30.3736 51.2873 30.177C51.1964 29.9805 51.1089 29.7623 51.0339 29.5549C51.0515 29.5447 51.0714 29.5322 51.0782 29.5464Z" fill="#263238"/><path d="M52.8592 34.0995C53.7922 33.5745 57.6905 31.554 58.5677 31.1802C58.6302 31.1535 58.6626 31.1802 58.6001 31.2211C56.7515 32.297 54.8459 33.2717 52.8916 34.141C52.8558 34.1461 52.8325 34.1137 52.8592 34.0995Z" fill="#263238"/><path d="M159.323 110.856H70.5049L83.3458 37.9077H172.163L159.323 110.856Z" fill="#115CF2"/><path d="M113.744 30.353H84.6776L83.3452 37.9087H114.451L113.744 30.353Z" fill="#007CFF"/><g opacity="0.1"><path d="M159.498 110.858H70.6797L83.5212 37.9106H172.339L159.498 110.858Z" fill="black"/><path d="M145.318 30.3545H116.252L114.919 37.9107H146.026L145.318 30.3545Z" fill="#007CFF"/></g><path d="M81.5393 33.4373C81.3444 32.6021 81.3444 31.7328 81.5393 30.897C81.733 31.7322 81.733 32.6015 81.5393 33.4373ZM78.9489 34.1845C78.6528 33.8759 78.3975 33.5307 78.1893 33.1572C77.9711 32.7904 77.7997 32.3978 77.6791 31.9885C77.9762 32.2958 78.2325 32.6407 78.4404 33.014C78.658 33.3816 78.8291 33.7748 78.9489 34.1845ZM77.0916 36.1271C76.2715 35.8781 75.52 35.4431 74.8955 34.8561C75.3042 34.9759 75.6962 35.1464 76.0626 35.3634C76.4359 35.5742 76.7813 35.8311 77.0916 36.1271ZM76.4393 38.7419C75.6049 38.9371 74.7367 38.9371 73.9023 38.7419C74.7367 38.5467 75.6049 38.5467 76.4393 38.7419ZM77.1961 41.3294C76.8874 41.625 76.5429 41.8808 76.1705 42.0907C75.8055 42.3093 75.4141 42.4804 75.0058 42.5998C75.3131 42.3021 75.6579 42.0456 76.0313 41.8367C76.3958 41.6176 76.7874 41.4471 77.1961 41.3294ZM79.1262 43.1953C78.8766 44.0159 78.4426 44.7686 77.8575 45.3958C77.9753 44.9862 78.1469 44.594 78.3677 44.2294C78.5751 43.8535 78.83 43.506 79.1262 43.1953ZM81.7444 43.8396C81.8461 44.2549 81.8944 44.6822 81.8893 45.11C81.8956 45.538 81.8469 45.965 81.7444 46.3805C81.6396 45.9654 81.5905 45.5381 81.5984 45.11C81.5937 44.6821 81.6427 44.2553 81.7444 43.8396ZM84.3262 43.1072C84.6234 43.4152 84.8791 43.7606 85.0881 44.1345C85.3069 44.4998 85.4773 44.893 85.595 45.3027C85.0097 44.6753 84.5757 43.9224 84.3262 43.1015V43.1072ZM86.1893 41.1504C87.0067 41.4018 87.756 41.8363 88.3802 42.4208C87.9715 42.3031 87.5799 42.1325 87.2154 41.9134C86.8418 41.7046 86.4968 41.4481 86.1893 41.1504ZM86.8342 38.5345C87.6691 38.339 88.5379 38.339 89.3728 38.5345C88.5379 38.7299 87.6691 38.7299 86.8342 38.5345ZM86.0853 35.947C86.3946 35.6518 86.7397 35.3966 87.1126 35.1873C87.4766 34.9662 87.8682 34.7943 88.2773 34.676C87.6541 35.2625 86.904 35.6974 86.0853 35.947ZM84.1501 34.0822C84.2705 33.6731 84.441 33.2811 84.658 32.914C84.867 32.5404 85.1231 32.1951 85.42 31.8867C85.1697 32.7056 84.7357 33.4565 84.1512 34.0822H84.1501Z" fill="#263238"/><path d="M159.191 110.856H70.182L57.5405 40.5576H146.554L159.191 110.856Z" fill="#007CFF"/><path d="M88.388 83.5335C90.4317 83.5335 92.088 81.8738 92.088 79.8273C92.088 77.7807 90.4317 76.1216 88.388 76.1216C86.3442 76.1216 84.688 77.7812 84.688 79.8273C84.688 81.8733 86.3448 83.5335 88.388 83.5335ZM117.633 83.5335C119.676 83.5335 121.332 81.8738 121.332 79.8273C121.332 77.7807 119.676 76.1216 117.633 76.1216C116.651 76.1226 115.709 76.5136 115.016 77.2084C114.322 77.9033 113.932 78.8452 113.932 79.8273C113.932 81.8738 115.589 83.5335 117.633 83.5335ZM111.199 93.6511L110.887 93.054C110.86 93.0023 107.924 87.5528 102.021 87.5528C96.6494 87.5528 95.3067 92.6801 95.2709 92.9034L95.1096 93.5551L93.8056 93.2386L93.9613 92.5841C93.9613 92.5204 95.5641 86.2045 102.021 86.2045C108.735 86.2045 111.944 92.1738 112.079 92.4301L112.389 93.0131L111.199 93.6511Z" fill="white"/><path d="M153.671 79.5464L153.61 79.2526C153.799 79.2117 153.986 79.1691 154.172 79.1191L154.245 79.4106C154.055 79.4595 153.863 79.5048 153.671 79.5464ZM155.374 79.0731L155.279 78.8004C155.644 78.6739 156.006 78.5361 156.363 78.3873L156.475 78.6572C156.116 78.8089 155.747 78.9481 155.374 79.0731ZM157.545 78.1424L157.413 77.8856C157.758 77.7086 158.094 77.514 158.419 77.3026L158.571 77.5492C158.245 77.7746 157.903 77.9723 157.545 78.1424ZM159.55 76.9078L159.372 76.672C159.681 76.4441 159.982 76.2059 160.275 75.9578L160.476 76.1697C160.171 76.4345 159.879 76.6828 159.55 76.9203V76.9078ZM161.334 75.3555L161.122 75.1532C161.389 74.86 161.643 74.5691 161.876 74.2782L162.106 74.4566C161.865 74.7722 161.61 75.0762 161.34 75.3674L161.334 75.3555ZM162.777 73.4862L162.53 73.3305C162.73 73.0044 162.909 72.6663 163.065 72.3163L163.333 72.4356C163.168 72.8015 162.981 73.1553 162.772 73.497L162.777 73.4862ZM163.743 71.3191L163.45 71.2384C163.556 70.8719 163.635 70.4982 163.687 70.1203L163.978 70.1578C163.93 70.5549 163.85 70.9475 163.739 71.3316L163.743 71.3191ZM164.065 68.9839H163.775V68.8998C163.775 68.5424 163.753 68.185 163.719 67.8276L164.01 67.7992C164.049 68.1633 164.069 68.5284 164.069 68.8947L164.065 68.9839ZM163.541 66.6987C163.463 66.3218 163.366 65.9492 163.25 65.5822L163.541 65.4947C163.661 65.8693 163.758 66.2498 163.832 66.6362L163.541 66.6987ZM162.851 64.4992C162.708 64.1407 162.548 63.789 162.373 63.4453L162.635 63.3146C162.814 63.6722 162.975 64.0295 163.118 64.3867L162.851 64.4992ZM161.83 62.4174C161.652 62.0992 161.45 61.764 161.247 61.4186L161.495 61.2634C161.7 61.5944 161.894 61.9317 162.078 62.2748L161.83 62.4174ZM160.612 60.4345C160.4 60.1146 160.184 59.7896 159.961 59.4663L160.203 59.3021C160.425 59.6265 160.64 59.9498 160.853 60.2776L160.612 60.4345ZM159.299 58.5032C159.081 58.1811 158.86 57.8578 158.644 57.5367L158.885 57.372C159.101 57.6936 159.32 58.0168 159.538 58.335L159.299 58.5032ZM157.991 56.5521C157.779 56.2237 157.571 55.893 157.367 55.56L157.615 55.4083C157.82 55.7407 158.027 56.0674 158.235 56.3947L157.991 56.5521ZM156.776 54.5475C156.577 54.1896 156.394 53.843 156.227 53.5078L156.489 53.3754C156.654 53.7095 156.846 54.0538 157.024 54.4038L156.776 54.5475ZM155.739 52.443C155.589 52.0814 155.449 51.7153 155.321 51.3453L155.613 51.2509C155.735 51.6081 155.872 51.9653 156.023 52.3225L155.739 52.443ZM154.983 50.2146C154.885 49.829 154.803 49.4397 154.737 49.0475L155.03 49.0015C155.094 49.3748 155.172 49.7538 155.269 50.1265L154.983 50.2146ZM154.589 47.8862C154.559 47.5262 154.544 47.1651 154.544 46.8038V46.7072H154.837V46.8004C154.829 47.1542 154.844 47.5072 154.88 47.8595L154.589 47.8862ZM154.88 45.5509L154.589 45.5208C154.593 45.439 154.6 45.3575 154.613 45.2765C154.646 44.9634 154.691 44.6526 154.743 44.3538L155.035 44.4043C154.983 44.7042 154.941 45.0054 154.907 45.3078C154.919 45.3919 154.915 45.4782 154.894 45.56L154.88 45.5509ZM155.285 43.2782L154.994 43.1998C155.101 42.8157 155.227 42.4384 155.367 42.0793L155.638 42.185C155.506 42.5461 155.392 42.9141 155.299 43.2873L155.285 43.2782ZM169.811 41.5776C169.644 41.5735 169.477 41.5579 169.312 41.5311C169.085 41.4982 168.859 41.4602 168.634 41.4169L168.692 41.1254C168.908 41.1651 169.132 41.2044 169.354 41.2362C169.513 41.2559 169.67 41.2703 169.826 41.2793L169.811 41.5776ZM171.01 41.4867L170.948 41.1953C171.314 41.1186 171.664 40.9823 171.986 40.7918L172.138 41.0396C171.794 41.2513 171.418 41.4052 171.025 41.4953L171.01 41.4867ZM156.125 41.1401L155.865 41.0021C156.051 40.654 156.256 40.3163 156.48 39.9907L156.719 40.1589C156.506 40.4775 156.313 40.8074 156.139 41.1487L156.125 41.1401ZM167.494 41.122C167.118 41.0019 166.748 40.8623 166.386 40.7038L166.506 40.4356C166.855 40.5913 167.211 40.7265 167.576 40.8413L167.494 41.122ZM173.025 40.2106L172.782 40.0481C172.99 39.7461 173.113 39.3936 173.139 39.0276L173.431 39.0492C173.406 39.4681 173.269 39.8725 173.033 40.2197L173.025 40.2106ZM165.329 40.168C164.987 39.9688 164.657 39.7511 164.34 39.5157L164.518 39.2816C164.827 39.5104 165.147 39.7206 165.48 39.9123L165.329 40.168ZM157.443 39.2566L157.227 39.06C157.493 38.7657 157.774 38.4882 158.071 38.2276L158.263 38.447C157.976 38.7034 157.706 38.9761 157.452 39.2651L157.443 39.2566ZM163.447 38.7634C163.156 38.4953 162.88 38.2113 162.621 37.9129L162.84 37.7197C163.092 38.0102 163.359 38.2862 163.64 38.5475L163.447 38.7634ZM172.932 37.9703C172.764 37.6364 172.533 37.3389 172.25 37.0947L172.443 36.8765C172.752 37.1457 173.007 37.4721 173.192 37.8379L172.932 37.9703ZM159.167 37.7396L158.999 37.5004C159.325 37.2774 159.663 37.0717 160.01 36.8839L160.148 37.1413C159.812 37.3265 159.487 37.529 159.173 37.7487L159.167 37.7396ZM161.889 36.9805C161.777 36.8218 161.67 36.6596 161.568 36.4941L161.19 36.6447L161.079 36.3748L161.413 36.2407C161.361 36.1532 161.309 36.0623 161.259 35.9782L161.513 35.835C161.571 35.9373 161.629 36.0407 161.691 36.1282C161.853 36.0695 162.019 36.0134 162.189 35.96L162.274 36.2532C162.13 36.2958 161.982 36.3441 161.848 36.3924C161.935 36.5386 162.032 36.678 162.139 36.8106L161.889 36.9805ZM171.314 36.4782C171.032 36.3372 170.739 36.2177 170.439 36.1208L170.247 36.06L170.33 35.7691L170.529 35.8333C170.842 35.9326 171.145 36.0557 171.438 36.2026L171.314 36.4782ZM163.389 35.9549L163.33 35.6651C163.716 35.5782 164.105 35.508 164.497 35.4549L164.54 35.7475C164.155 35.8083 163.773 35.8816 163.4 35.9657L163.389 35.9549ZM169.134 35.7975C168.755 35.7307 168.373 35.6814 167.99 35.6498L168.013 35.3561C168.418 35.3919 168.813 35.4418 169.18 35.5083L169.134 35.7975ZM165.678 35.6276L165.659 35.3367C166.004 35.3154 166.349 35.3046 166.695 35.3043H166.839V35.5958H166.698C166.369 35.6043 166.026 35.6134 165.687 35.6367L165.678 35.6276ZM160.753 34.9328C160.594 34.5754 160.46 34.2055 160.33 33.8288L160.621 33.7379C160.744 34.1055 160.88 34.468 161.031 34.8095L160.753 34.9328ZM160.014 32.6907C159.925 32.3045 159.853 31.9145 159.798 31.522L160.091 31.4811C160.146 31.8669 160.215 32.2492 160.3 32.6191L160.014 32.6907ZM159.688 30.3532C159.673 30.0874 159.665 29.8212 159.664 29.5549V29.1742H159.955V29.5475C159.955 29.81 159.955 30.0839 159.976 30.3305L159.688 30.3532ZM160.036 28.018L159.743 27.9896C159.784 27.6032 159.839 27.2089 159.907 26.8214L160.198 26.8714C160.118 27.2525 160.058 27.6365 160.019 28.0237L160.036 28.018ZM160.434 25.7384L160.143 25.6686C160.235 25.2919 160.344 24.9112 160.463 24.5322L160.754 24.6231C160.628 24.9917 160.518 25.3655 160.425 25.7436L160.434 25.7384ZM161.134 23.5282L160.863 23.4225C161.006 23.0657 161.154 22.6918 161.333 22.3401L161.594 22.4686C161.42 22.8176 161.263 23.1755 161.125 23.5407L161.134 23.5282ZM162.137 21.447L161.884 21.2964C162.082 20.9612 162.294 20.6265 162.515 20.3049L162.754 20.4714C162.531 20.7888 162.322 21.1161 162.129 21.4521L162.137 21.447ZM163.452 19.5458L163.223 19.3669C163.468 19.0759 163.732 18.7646 164.003 18.4919L164.212 18.6936C163.94 18.9678 163.684 19.2555 163.443 19.5566L163.452 19.5458ZM165.038 17.864L164.841 17.6481C165.134 17.3873 165.425 17.1316 165.733 16.8833L165.911 17.114C165.6 17.3623 165.304 17.6163 165.019 17.8697L165.038 17.864ZM166.847 16.4208L166.681 16.1725C167.004 15.9493 167.333 15.7359 167.669 15.5328L167.819 15.7811C167.48 15.9884 167.166 16.2009 166.838 16.4208H166.847ZM168.827 15.2163L168.683 14.9589C169.031 14.7771 169.383 14.6028 169.739 14.4362L169.86 14.7021C169.49 14.868 169.146 15.043 168.81 15.222L168.827 15.2163ZM170.934 14.2464L170.827 13.9714C171.194 13.8282 171.564 13.6799 171.931 13.5725L172.025 13.864C171.653 13.9774 171.286 14.1067 170.925 14.2515L170.934 14.2464ZM173.182 13.5078L173.109 13.2151C173.302 13.1641 173.497 13.1193 173.693 13.0811L173.746 13.3725C173.746 13.3725 173.535 13.4163 173.182 13.5078Z" fill="#263238"/><path d="M178.334 11.6213C177.601 9.31446 172.606 10.1434 173.584 13.1594C174.562 16.1753 179.078 13.9315 178.334 11.6213Z" fill="#263238"/><path d="M170.877 10.6992C170.915 9.68617 172.32 8.91287 174.307 9.27026C176.295 9.62764 178.165 10.9725 178.165 10.9725C178.165 10.9725 176.203 12.1646 174.194 12.3828C172.185 12.6004 170.838 11.7055 170.877 10.6992Z" fill="#EBEBEB"/><path d="M178.163 10.9623C177.13 10.9623 176.103 10.9322 175.074 10.8998C174.045 10.868 173.012 10.8163 171.984 10.7339C173.016 10.7339 174.048 10.7606 175.075 10.793C176.103 10.8254 177.138 10.8771 178.163 10.9629" fill="#263238"/><path d="M172.873 9.79736C173.459 10.0779 174.011 10.4237 174.52 10.828C174.23 10.688 173.949 10.5317 173.678 10.3599C173.399 10.1879 173.131 10.0004 172.873 9.79736ZM175.576 10.8655C175.049 11.2929 174.477 11.6624 173.871 11.9678C174.401 11.5396 174.975 11.17 175.585 10.8655H175.576ZM175.876 10.2957C176.17 10.4377 176.428 10.6451 176.629 10.903C176.333 10.7635 176.075 10.5556 175.876 10.2957Z" fill="#263238"/><path d="M172.772 16.7063C173.392 17.5051 174.987 17.3142 176.39 15.8597C177.792 14.4051 178.53 12.2188 178.53 12.2188C178.53 12.2188 176.235 12.3972 174.482 13.3869C172.73 14.3767 172.147 15.8932 172.772 16.7063Z" fill="#EBEBEB"/><path d="M178.533 12.2109C177.695 12.795 176.878 13.4439 176.059 14.0706C175.241 14.698 174.439 15.3463 173.654 16.0149C174.495 15.4303 175.31 14.7854 176.127 14.1581C176.944 13.528 177.747 12.8787 178.533 12.2109Z" fill="#263238"/><path d="M174.915 16.2458C175.07 15.9617 175.21 15.6698 175.333 15.3702C175.454 15.0691 175.557 14.7617 175.641 14.4481C175.484 14.7311 175.345 15.0235 175.224 15.3236C175.104 15.6251 175.001 15.9325 174.915 16.2458ZM176.494 13.8032C175.812 13.7674 175.128 13.8043 174.453 13.9128C174.793 13.9321 175.134 13.9321 175.474 13.9128C175.816 13.8946 176.156 13.8581 176.494 13.8037M177.057 14.0941C177.135 13.9494 177.194 13.7975 177.235 13.6384C177.278 13.4812 177.304 13.321 177.312 13.1577C177.157 13.4467 177.07 13.7668 177.057 14.0941Z" fill="#263238"/><path d="M178.335 11.6214C178.171 11.6751 177.994 11.6787 177.828 11.6315C177.661 11.5844 177.513 11.4886 177.401 11.3566C177.262 11.1911 177.19 10.9804 177.197 10.7647C177.205 10.5491 177.291 10.3437 177.441 10.1881C177.59 10.0325 177.792 9.93748 178.007 9.92139C178.222 9.90529 178.436 9.96922 178.607 10.1009C178.766 10.2263 178.876 10.4029 178.92 10.6007C178.963 10.7985 178.936 11.0052 178.844 11.1856C178.739 11.3924 178.556 11.5492 178.335 11.6214Z" fill="#455A64"/><path d="M178.872 13.2889C178.749 13.3288 178.619 13.3409 178.491 13.3243C178.363 13.3077 178.24 13.2629 178.131 13.1931C178.022 13.1232 177.93 13.0301 177.862 12.9204C177.793 12.8108 177.75 12.6872 177.735 12.5588C177.715 12.387 177.746 12.2131 177.824 12.0588C177.883 11.9433 177.966 11.8423 178.069 11.763C178.171 11.6837 178.29 11.6282 178.417 11.6004C178.543 11.5726 178.674 11.5733 178.801 11.6024C178.927 11.6315 179.045 11.6882 179.147 11.7686C179.248 11.8489 179.331 11.9508 179.388 12.0669C179.446 12.183 179.477 12.3104 179.479 12.44C179.481 12.5695 179.455 12.6979 179.401 12.8159C179.348 12.934 179.269 13.0387 179.17 13.1225C179.083 13.1974 178.982 13.2541 178.872 13.2889Z" fill="#455A64"/><path d="M83.2977 12.5428C83.1585 13.8818 82.6499 15.1559 81.8289 16.2229C81.0078 17.2899 79.9065 18.1078 78.6477 18.5854C77.3901 19.0627 76.0245 19.181 74.7035 18.927C73.3825 18.6731 72.1581 18.0569 71.167 17.1473L67.5312 17.6831L69.3511 14.5371C68.9353 13.5129 68.7579 12.4076 68.8322 11.3047C68.9065 10.2018 69.2306 9.1303 69.7801 8.17117C70.3297 7.21278 71.0904 6.39221 72.0045 5.77174C73.3824 4.83634 75.04 4.40338 76.6993 4.54553C78.3585 4.68769 79.9184 5.39629 81.117 6.55242C81.9125 7.31982 82.5229 8.25819 82.902 9.29644C83.2811 10.3347 83.419 11.4456 83.3051 12.545L83.2977 12.5428Z" fill="white"/><path d="M83.299 12.5437C83.1639 13.8842 82.6592 15.1608 81.841 16.2312C81.4422 16.7738 80.9606 17.2503 80.4137 17.6432C79.8807 18.0505 79.2921 18.3796 78.666 18.6204C77.7216 18.9916 76.7091 19.1578 75.6955 19.1079C75.3581 19.0989 75.0219 19.0638 74.6899 19.0028C73.3486 18.7534 72.1036 18.1345 71.095 17.2159L71.1802 17.2426L67.5501 17.8L67.2842 17.8415L67.42 17.6074L69.2274 14.454V14.579C68.8004 13.5361 68.6187 12.4091 68.6962 11.2849C68.7737 10.1607 69.1084 9.06926 69.6745 8.09488C70.2307 7.12303 71.0021 6.29153 71.9296 5.66419C73.3282 4.71943 75.011 4.28879 76.6915 4.44565C78.3719 4.6025 79.946 5.33715 81.1455 6.52442C81.937 7.29906 82.5421 8.24343 82.9152 9.28618C83.2882 10.3289 83.4195 11.4428 83.299 12.5437ZM83.299 12.5437C83.4436 11.1738 83.1927 9.79103 82.576 8.55916C81.9594 7.32729 81.0028 6.29783 79.8194 5.5926C78.6371 4.88834 77.2777 4.53798 75.9023 4.5831C74.5269 4.62821 73.1933 5.06691 72.0597 5.84715C71.1714 6.47237 70.4295 7.28311 69.8853 8.22328C69.3477 9.16548 69.0312 10.2175 68.9598 11.3C68.8883 12.3899 69.0633 13.4819 69.4717 14.4949L69.495 14.5642L69.4609 14.625L67.6359 17.7625L67.5069 17.5693L71.1467 17.0659H71.1967L71.2308 17.0977C72.2024 18.0049 73.4074 18.6237 74.7109 18.8846C75.0363 18.9513 75.3652 18.9911 75.6978 19.004C76.0285 19.0278 76.3596 19.0278 76.691 19.004C77.3544 18.95 78.0074 18.8058 78.6319 18.5756C79.2584 18.3471 79.848 18.028 80.3819 17.6284C80.9249 17.2388 81.4061 16.7695 81.8092 16.2364C82.6389 15.1674 83.1548 13.8892 83.299 12.5437Z" fill="#263238"/><path d="M72.5273 15.1799C72.5178 14.9932 72.582 14.8102 72.7062 14.6705C72.8303 14.5307 73.0044 14.4453 73.1909 14.4327H73.2409C73.3389 14.4328 73.436 14.4522 73.5265 14.4898C73.617 14.5274 73.6992 14.5824 73.7685 14.6518C73.8377 14.7212 73.8926 14.8036 73.93 14.8942C73.9674 14.9848 73.9865 15.0819 73.9864 15.1799C73.9862 15.3776 73.9077 15.5673 73.7679 15.7072C73.6282 15.8472 73.4387 15.926 73.2409 15.9265C73.0544 15.9259 72.8754 15.8526 72.7421 15.7221C72.6088 15.5915 72.5317 15.4141 72.5273 15.2276V15.1799ZM72.8176 13.4537L72.6506 7.66113H73.8154L73.6472 13.4537H72.8176ZM75.2943 15.1799C75.2856 14.9935 75.35 14.8111 75.4739 14.6716C75.5978 14.5321 75.7713 14.4466 75.9574 14.4333H76.0023C76.1004 14.4331 76.1975 14.4522 76.2882 14.4897C76.3788 14.5271 76.4612 14.5821 76.5305 14.6515C76.5998 14.7209 76.6548 14.8033 76.6921 14.894C76.7295 14.9846 76.7486 15.0818 76.7483 15.1799C76.748 15.3777 76.6693 15.5673 76.5295 15.7073C76.3897 15.8472 76.2001 15.926 76.0023 15.9265C75.8152 15.9259 75.6357 15.8521 75.5023 15.7208C75.3689 15.5896 75.2922 15.4113 75.2887 15.2242C75.292 15.2097 75.2939 15.1948 75.2943 15.1799ZM75.5852 13.4537L75.4262 7.66113H76.5909L76.421 13.4537H75.5852ZM78.0568 15.1799C78.0481 14.9934 78.1126 14.8109 78.2366 14.6714C78.3606 14.5319 78.5343 14.4464 78.7205 14.4333H78.7705C78.8685 14.4331 78.9656 14.4524 79.0562 14.4898C79.1468 14.5273 79.2291 14.5823 79.2983 14.6517C79.3676 14.7211 79.4225 14.8034 79.4598 14.8941C79.4972 14.9847 79.5162 15.0819 79.5159 15.1799C79.5158 15.3776 79.4372 15.5673 79.2975 15.7072C79.1578 15.8472 78.9682 15.926 78.7705 15.9265C78.5836 15.927 78.404 15.8539 78.2705 15.7232C78.137 15.5924 78.0603 15.4144 78.0568 15.2276V15.1799ZM78.3495 13.4537L78.1801 7.66113H79.3466L79.1807 13.4537H78.3495Z" fill="black"/></g></g></g></g><defs><clipPath id="clip0_2_9098"><rect width="200" height="131.25" fill="white" transform="translate(0 0.984863)"/></clipPath></defs></svg>'
//const solutionDiagram_noData = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'
const solutionDiagram_noData = '<svg width="300" height="130" viewBox="0 0 200 104" fill="none"><g clip-path="url(#clip0_2_295)"><mask id="mask0_2_295" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="-1" width="200" height="106"><path d="M0 -0.316406H200V104.316H0V-0.316406Z" fill="white"/></mask><g mask="url(#mask0_2_295)"><mask id="mask1_2_295" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="-1" width="200" height="106"><path d="M199.669 -0.31543H0.331055V104.317H199.669V-0.31543Z" fill="white"/></mask><g mask="url(#mask1_2_295)"><g filter="url(#filter0_d_2_295)"><path d="M47.7364 3.69336H129.473C133.329 3.69336 137.052 6.37141 137.785 9.67329L152.315 75.0545C153.049 78.3564 150.517 81.0344 146.66 81.0344H64.9238C61.0675 81.0344 57.345 78.3564 56.6112 75.0545L42.0814 9.67329C41.3483 6.37141 43.8794 3.69336 47.7364 3.69336Z" fill="white"/></g><path d="M46.0451 3.69336C42.1888 3.69336 41.3663 6.45287 42.1226 9.8554L42.3544 10.8997H138.06L137.828 9.8554C137.072 6.45287 134.781 3.69336 130.925 3.69336H46.0451Z" fill="#1C7AFF"/><path d="M44.7281 7.26946C44.5254 6.36016 45.2844 5.62305 46.4235 5.62305C47.5626 5.62305 48.6487 6.36016 48.8514 7.26946C49.0534 8.17943 48.2944 8.91654 47.156 8.91654C46.0175 8.91654 44.9307 8.17943 44.7281 7.26946ZM50.1388 7.26946C49.9368 6.36016 50.6382 5.62305 51.7064 5.62305C52.7747 5.62305 53.8025 6.36016 54.0045 7.26946C54.2065 8.17876 53.5058 8.91654 52.4383 8.91654C51.3707 8.91654 50.3408 8.17943 50.1388 7.26946ZM55.0337 7.26946C54.8317 6.36016 55.5331 5.62305 56.6007 5.62305C57.6682 5.62305 58.6974 6.36016 58.8994 7.26946C59.1014 8.17876 58.4 8.91654 57.3325 8.91654C56.2649 8.91654 55.235 8.17943 55.0337 7.26946Z" fill="white"/><path d="M69.4903 15.9627C70.2035 15.9627 70.8916 16.4727 71.03 17.0952L71.5042 19.2103C72.6807 19.4485 73.8268 19.8182 74.9208 20.3123L76.3625 18.7971C76.5525 18.5971 76.8426 18.4971 77.1506 18.4971C77.4585 18.4971 77.7883 18.5971 78.0678 18.7971L81.587 21.3109C82.1459 21.7102 82.2936 22.3572 81.9088 22.7572L80.4479 24.2969C81.0355 24.9614 81.5512 25.6861 81.9863 26.4591H83.8976C84.5625 26.4591 85.2029 26.9173 85.328 27.4822L86.1234 31.043C86.2492 31.6079 85.8121 32.0655 85.1486 32.0695H83.2367C83.1569 32.8161 82.9641 33.5462 82.6651 34.235L84.8121 35.768C85.3704 36.1673 85.5194 36.8183 85.134 37.2176L82.7406 39.7348C82.3598 40.1348 81.5943 40.1348 81.0353 39.7348L78.8876 38.1984C77.9575 38.6861 76.9611 39.0355 75.93 39.2355L76.3857 41.2672C76.5115 41.8321 76.0751 42.2936 75.4068 42.2936L71.2234 42.2969C70.5592 42.2969 69.9141 41.8393 69.789 41.2745L69.3029 39.105C68.5249 38.9247 67.7613 38.6874 67.0181 38.3951L65.7386 39.7454C65.3578 40.1447 64.5923 40.1447 64.0333 39.7454L60.5102 37.2316C59.9512 36.8316 59.8062 36.1813 60.1876 35.7813L61.3201 34.5899C60.6019 33.8332 59.9805 32.9901 59.4704 32.0801L56.9148 32.0827C56.2506 32.0827 55.6062 31.6251 55.4804 31.0602L54.6857 27.4994C54.5598 26.9346 54.9969 26.4763 55.6605 26.473L58.2155 26.4697C58.3168 25.5776 58.5585 24.734 58.9413 23.9592L57.2757 22.7711C56.7174 22.3718 56.5737 21.7241 56.9545 21.3248L59.3519 18.8037C59.7327 18.4044 60.4976 18.4044 61.0565 18.8037L62.9386 20.1467C63.2503 19.9988 63.5731 19.8646 63.9068 19.7441V19.7474C64.2115 19.6238 64.5272 19.5125 64.8539 19.4136L64.3333 17.0892C64.1943 16.4654 64.6598 15.9521 65.3731 15.9521L69.4903 15.9627ZM71.6128 24.2936C68.2327 23.2082 64.9386 24.4962 64.2512 27.165C63.5691 29.8378 65.7525 32.882 69.1327 33.9661C72.5174 35.0515 75.8128 33.7674 76.4996 31.098C77.1823 28.4292 74.9943 25.3823 71.6128 24.2936Z" fill="#9A9A9A"/><path d="M64.451 56.209C67 56.209 68.6755 54.4482 68.1927 52.2767C67.71 50.1053 65.2523 48.3438 62.7033 48.3438C60.1543 48.3438 58.4795 50.1053 58.9616 52.2767C59.4451 54.4482 61.9014 56.209 64.451 56.209ZM67.4199 69.5609C69.9689 69.5609 71.6444 67.8 71.1616 65.6286C70.6788 63.4571 68.2212 61.6963 65.6716 61.6963C63.1232 61.6963 61.4477 63.4565 61.9298 65.6286C62.4133 67.8 64.8702 69.5609 67.4199 69.5609ZM138.738 48.7835H71.4139L71.8484 50.739H139.172L138.738 48.7835ZM141.629 61.785H74.3047L74.7398 63.7412H142.064L141.629 61.785ZM111.332 53.8336H72.5378L72.9722 55.7892H111.766L111.332 53.8336ZM108.355 66.8358H75.4265L75.861 68.7921H108.789L108.355 66.8358Z" fill="#D9D9D9"/><path d="M90.3483 23.2237C89.3682 18.8153 92.2351 15.2412 96.7517 15.2412H172.868C177.384 15.2412 181.839 18.8153 182.819 23.2237L189.793 54.6034C190.772 59.0119 187.905 62.586 183.389 62.586H107.273C102.757 62.586 98.3013 59.0119 97.3219 54.6034L90.3483 23.2237Z" fill="#D9E8FF"/><path d="M95.7092 19.7324H93.8808C93.4901 19.7324 93.2424 20.041 93.3272 20.4231C93.4119 20.8046 93.7974 21.1138 94.1881 21.1138H96.0158C96.4072 21.1138 96.6549 20.8046 96.5701 20.4231C96.4853 20.0417 96.0999 19.7324 95.7092 19.7324ZM96.4721 23.184H94.6437C94.253 23.184 94.0053 23.4932 94.0901 23.8753C94.1748 24.2568 94.5603 24.566 94.951 24.566H96.7787C97.1694 24.566 97.4178 24.2568 97.333 23.8747C97.2482 23.4932 96.8628 23.184 96.4721 23.184ZM97.2429 26.6388H95.4145C95.0238 26.6388 94.7761 26.9481 94.8609 27.3295C94.9457 27.711 95.3311 28.0209 95.7218 28.0209H97.5495C97.9409 28.0209 98.1886 27.711 98.1038 27.3295C98.0191 26.9481 97.6337 26.6388 97.2429 26.6388ZM98.0071 30.0937H96.1794C95.7887 30.0937 95.5403 30.4029 95.6251 30.7844C95.7099 31.1658 96.0953 31.4751 96.486 31.4751H98.3144C98.7051 31.4751 98.9528 31.1658 98.868 30.7844C98.7833 30.4029 98.3979 30.0937 98.0071 30.0937ZM98.7753 33.5472H96.9469C96.5562 33.5472 96.3085 33.8564 96.3933 34.2379C96.4781 34.6193 96.8635 34.9286 97.2542 34.9286H99.0819C99.4727 34.9286 99.721 34.6193 99.6362 34.2379C99.5515 33.8564 99.166 33.5472 98.7753 33.5472ZM99.5455 37.0014H97.7171C97.3264 37.0014 97.0787 37.3113 97.1635 37.6927C97.2482 38.0742 97.6336 38.3834 98.0244 38.3834H99.8521C100.243 38.3834 100.491 38.0742 100.406 37.6927C100.322 37.3113 99.9362 37.002 99.5455 37.002M100.311 40.4582H98.4833C98.0926 40.4582 97.8442 40.7668 97.929 41.1482C98.0138 41.5297 98.3992 41.8396 98.7899 41.8396H100.618C101.009 41.8396 101.257 41.5303 101.172 41.1482C101.087 40.7668 100.702 40.4582 100.311 40.4582ZM101.078 43.9091H99.2495C98.8588 43.9091 98.6111 44.2183 98.6959 44.5998C98.7806 44.9819 99.166 45.2911 99.5568 45.2911H101.385C101.776 45.2911 102.024 44.9819 101.939 44.6004C101.854 44.2183 101.469 43.9091 101.078 43.9091ZM101.849 47.3626H100.021C99.6296 47.3626 99.3819 47.6719 99.4667 48.0533C99.5515 48.4348 99.9369 48.744 100.328 48.744H102.155C102.547 48.744 102.794 48.4348 102.71 48.0533C102.625 47.6719 102.239 47.3626 101.849 47.3626ZM102.613 50.8188H100.785C100.394 50.8188 100.146 51.128 100.231 51.5095C100.316 51.8909 100.701 52.2002 101.092 52.2002H102.92C103.311 52.2002 103.559 51.8909 103.474 51.5095C103.389 51.128 103.004 50.8188 102.613 50.8188ZM103.381 54.2723H101.553C101.162 54.2723 100.914 54.5816 100.999 54.963C101.084 55.3444 101.469 55.6537 101.86 55.6537H103.688C104.079 55.6537 104.327 55.3444 104.242 54.963C104.157 54.5816 103.772 54.2723 103.381 54.2723ZM104.149 57.7258H102.321C101.93 57.7258 101.682 58.0351 101.767 58.4165C101.851 58.798 102.237 59.1072 102.628 59.1072H104.456C104.847 59.1072 105.094 58.798 105.01 58.4165C104.925 58.0351 104.539 57.7258 104.149 57.7258Z" fill="white"/><path d="M113.773 35.3721H172.808" stroke="#8C8C8C" stroke-width="3.27075"/><path d="M128.329 41.425C132.157 41.425 134.839 38.466 134.319 34.8156C133.799 31.1651 130.273 28.2061 126.444 28.2061C122.616 28.2061 119.934 31.1651 120.454 34.8156C120.974 38.466 124.5 41.425 128.329 41.425Z" fill="#1C7AFF"/><path d="M159.393 41.1047C163.221 41.1047 165.903 38.145 165.383 34.4952C164.863 30.8454 161.337 27.8857 157.508 27.8857C153.68 27.8857 150.998 30.8448 151.519 34.4952C152.039 38.145 155.564 41.1047 159.393 41.1047Z" fill="#757474"/><path d="M143.561 39.6541C146.085 39.6541 147.854 37.7025 147.511 35.2953C147.168 32.8881 144.843 30.9365 142.318 30.9365C139.792 30.9365 138.024 32.8881 138.367 35.2953C138.71 37.7032 141.035 39.6541 143.561 39.6541ZM175.301 40.1508C178.209 40.1508 180.247 37.9031 179.851 35.1304C179.456 32.3576 176.778 30.1094 173.869 30.1094C170.961 30.1094 168.924 32.3576 169.319 35.1304C169.714 37.9031 172.392 40.1508 175.301 40.1508ZM113.955 40.1508C116.863 40.1508 118.901 37.9031 118.506 35.1304C118.11 32.3576 115.432 30.1094 112.524 30.1094C109.616 30.1094 107.579 32.3576 107.974 35.1304C108.369 37.9031 111.047 40.1508 113.955 40.1508Z" fill="white"/><path fill-rule="evenodd" clip-rule="evenodd" d="M155.619 33.5269C155.573 33.2024 155.639 32.8693 155.807 32.5362C155.98 32.2016 156.261 31.9235 156.65 31.7018C157.04 31.4813 157.519 31.374 158.079 31.374C158.604 31.374 159.075 31.4667 159.502 31.6482C159.924 31.8296 160.268 32.0797 160.535 32.3985C160.796 32.7124 160.958 33.0607 161.011 33.4289C161.053 33.7221 161.028 33.9786 160.935 34.1984C160.849 34.4081 160.727 34.6009 160.574 34.7679C160.426 34.9295 160.158 35.1991 159.769 35.5765C159.679 35.6648 159.595 35.7581 159.515 35.856C159.455 35.9394 159.41 36.0132 159.382 36.0772C159.356 36.1454 159.335 36.2096 159.323 36.2778C159.307 36.3952 159.294 36.5132 159.286 36.6315C159.27 36.9891 159.08 37.1659 158.719 37.1659C158.535 37.1659 158.366 37.1063 158.221 36.9891C158.076 36.8712 157.99 36.6997 157.957 36.4692C157.916 36.1796 157.926 35.9295 157.989 35.7189C158.051 35.5083 158.149 35.3216 158.28 35.1646C158.412 35.003 158.596 34.8163 158.827 34.5958C159.031 34.4044 159.18 34.258 159.263 34.1594C159.352 34.0622 159.424 33.9527 159.479 33.8309C159.534 33.713 159.546 33.5806 159.526 33.4382C159.486 33.1645 159.347 32.9325 159.108 32.7422C158.858 32.5521 158.552 32.4517 158.239 32.4574C157.843 32.4574 157.563 32.5508 157.406 32.7468C157.248 32.9382 157.128 33.2177 157.053 33.5905C156.985 33.9772 156.78 34.1702 156.436 34.1693C156.237 34.1706 156.045 34.0971 155.897 33.9633C155.735 33.8349 155.642 33.6898 155.619 33.5269ZM159.064 39.1804C158.843 39.1804 158.643 39.1122 158.458 38.9751C158.367 38.9078 158.291 38.8225 158.235 38.7245C158.178 38.6265 158.143 38.518 158.13 38.4056C158.1 38.1903 158.151 38.0082 158.289 37.8612C158.427 37.7142 158.613 37.6407 158.839 37.6407C159.065 37.6407 159.266 37.7142 159.447 37.8612C159.622 38.0082 159.725 38.1897 159.756 38.4056C159.789 38.6466 159.735 38.8365 159.595 38.9751C159.525 39.0432 159.442 39.0965 159.35 39.1318C159.259 39.167 159.162 39.1836 159.064 39.1804Z" fill="white"/><path fill-rule="evenodd" clip-rule="evenodd" d="M151.065 27.731C154.6 24.3125 160.815 24.6483 164.951 28.4859C168.682 31.9461 169.44 37.0737 166.952 40.5564L167.01 40.6093C167.244 40.8266 167.272 41.1603 167.071 41.3543L165.775 42.6093C165.574 42.8033 165.221 42.7848 164.986 42.5669L164.929 42.5139C161.307 44.9416 155.881 44.3304 152.15 40.8696C148.016 37.0293 147.529 31.1494 151.065 27.731ZM152.112 28.7005C149.07 31.6434 149.487 36.7029 153.043 40.0014C156.599 43.3013 161.947 43.5914 164.99 40.6491C168.031 37.7061 167.614 32.646 164.058 29.3455C160.5 26.0489 155.152 25.7589 152.112 28.7005Z" fill="#263238"/><path fill-rule="evenodd" clip-rule="evenodd" d="M167.07 41.3516L170.891 44.8974L169.59 46.1511L165.773 42.6066L167.07 41.3516Z" fill="#1C7AFF"/><path fill-rule="evenodd" clip-rule="evenodd" d="M169.593 46.1535L170.89 44.8985C171.091 44.7044 171.443 44.7236 171.678 44.9415L181.284 53.8571C181.519 54.075 181.546 54.4087 181.345 54.6028L180.049 55.8578C179.848 56.0518 179.495 56.0326 179.26 55.8154L169.654 46.8992C169.42 46.682 169.393 46.3442 169.593 46.1535Z" fill="#263238"/><path d="M40.4377 88.0077C62.5689 88.0077 80.5093 86.9647 80.5093 85.6787C80.5093 84.3926 62.5689 83.3496 40.4377 83.3496C18.3066 83.3496 0.366211 84.3926 0.366211 85.6787C0.366211 86.9647 18.3066 88.0077 40.4377 88.0077Z" fill="url(#paint0_radial_2_295)"/><path d="M52.2441 61.1417C52.3573 61.4011 52.4125 61.6821 52.4058 61.9651C52.399 62.2481 52.3305 62.5262 52.205 62.7799C52.0796 63.0336 51.9001 63.2569 51.6794 63.434C51.4586 63.6111 51.2017 63.7379 50.9268 63.8053C48.9433 64.5881 46.8273 62.3881 46.8273 62.3881L43.1252 58.2555C42.2444 58.0257 41.7914 56.3614 42.0212 55.4786C42.0751 55.2687 42.1698 55.0715 42.3 54.8983C42.4302 54.7251 42.5934 54.5794 42.78 54.4693C42.9667 54.3593 43.1733 54.2872 43.3879 54.2572C43.6024 54.2272 43.8208 54.2398 44.0305 54.2944L48.6777 59.731L47.0598 53.2036L48.6585 52.7891L52.2441 61.1417Z" fill="#407BFF"/><path opacity="0.6" d="M52.2431 61.1417C52.3567 61.401 52.4122 61.6821 52.4056 61.9652C52.399 62.2483 52.3306 62.5266 52.2051 62.7804C52.0795 63.0342 51.9 63.2575 51.679 63.4346C51.458 63.6116 51.2009 63.7382 50.9258 63.8053C48.9423 64.5881 46.8263 62.3881 46.8263 62.3881L43.1242 58.2555C42.2434 58.0257 41.7904 56.3614 42.0202 55.4786C42.0742 55.2687 42.169 55.0716 42.2992 54.8985C42.4294 54.7253 42.5926 54.5796 42.7792 54.4696C42.9659 54.3596 43.1724 54.2875 43.3869 54.2574C43.6015 54.2274 43.8199 54.2399 44.0296 54.2944L48.6767 59.731L47.0588 53.2036L48.6575 52.7891L52.2431 61.1417Z" fill="#2B4160"/><path d="M47.0662 53.191C47.0243 52.0983 47.3413 51.0219 47.9688 50.1263L48.684 49.1006L49.7263 50.5309L48.6032 52.7957L47.0662 53.191Z" fill="#FFC4C0"/><path d="M48.4176 49.6986L48.1494 48.5979L49.0329 47.876C49.0329 47.876 50.1654 49.9092 49.7256 50.543C48.8256 51.8311 48.4176 49.6986 48.4176 49.6986Z" fill="#FFC4C0"/><path opacity="0.2" d="M44.2961 57.7216C44.2961 57.5792 46.0013 61.4783 46.0013 61.4783L42.9307 58.0447L44.2961 57.7216Z" fill="black"/><path d="M43.3574 54.0346C43.3574 54.0346 46.4732 52.6625 42.6058 67.8063C42.266 69.1301 43.3077 70.589 43.0594 72.2168C42.37 76.653 35.4259 80.2582 32.3419 80.2066C30.6916 80.1807 32.018 78.5887 31.3254 78.5887C31.277 76.7862 28.7599 68.9421 30.8174 57.1471C31.2254 54.8141 32.2644 52.6717 34.6358 52.5876H34.7007C36.4318 52.5327 37.9563 53.1121 39.4767 53.277C40.7879 53.4274 42.0859 53.6803 43.3574 54.0346Z" fill="#407BFF"/><path opacity="0.6" d="M43.3574 54.0346C43.3574 54.0346 46.4732 52.6625 42.6058 67.8063C42.266 69.1301 43.3077 70.589 43.0594 72.2168C42.37 76.653 35.4259 80.2582 32.3419 80.2066C30.6916 80.1807 32.018 78.5887 31.3254 78.5887C31.277 76.7862 28.7599 68.9421 30.8174 57.1471C31.2254 54.8141 32.2644 52.6717 34.6358 52.5876H34.7007C36.4318 52.5327 37.9563 53.1121 39.4767 53.277C40.7879 53.4274 42.0859 53.6803 43.3574 54.0346Z" fill="#0479FF"/><path d="M64.4772 83.7207L60.5395 84.2552L54.7143 71.1655C52.5428 74.8257 49.8799 77.5892 46.815 81.3294C41.7766 87.4782 37.5276 86.1385 35.0911 83.8082C33.719 82.5142 33.7481 75.8124 33.7481 75.8124L41.9547 71.7351C41.9547 71.7351 53.0249 62.4413 56.3097 62.5903C59.7594 61.0017 64.4772 83.7207 64.4772 83.7207Z" fill="#263238"/><path opacity="0.2" d="M64.4772 83.7217L60.5395 84.2555L54.7143 71.1665C52.5428 74.826 49.8799 77.5895 46.815 81.3304C41.7766 87.4785 37.5276 86.1389 35.0911 83.8091C33.719 82.5145 33.7481 75.8134 33.7481 75.8134L41.9547 71.736C41.9547 71.736 53.0249 62.4423 56.3097 62.5913C59.7594 61.0026 64.4772 83.7217 64.4772 83.7217Z" fill="#263238"/><path d="M63.5641 83.3154L63.5674 83.3287L64.6131 87.5063L62.3018 88.2381L61.5223 85.8658L60.9594 84.1406L60.9561 84.1373L63.5641 83.3154Z" fill="#FFC4C0"/><path d="M64.9751 87.0161L61.8713 86.9612C61.8159 86.9587 61.7616 86.9763 61.7183 87.0107C61.6749 87.0452 61.6455 87.0941 61.6355 87.1486L61.1143 89.6664C61.1023 89.7284 61.104 89.7922 61.1194 89.8535C61.1347 89.9147 61.1632 89.9719 61.203 90.0209C61.2427 90.07 61.2928 90.1097 61.3495 90.1373C61.4063 90.165 61.4684 90.1799 61.5315 90.1809C62.6514 90.1809 64.2692 90.1452 65.6771 90.1809C67.3207 90.2101 67.3274 90.3008 69.2558 90.3359C70.4239 90.3558 70.7253 89.1843 70.2266 89.0677C67.9942 88.5399 67.5956 88.4816 65.6546 87.2618C65.4537 87.1215 65.2193 87.0367 64.9751 87.0161Z" fill="#27397A"/><path d="M66.1573 87.5133C66.5234 87.5133 66.8756 87.4743 66.9888 87.3127C67.0198 87.2699 67.0364 87.2183 67.0364 87.1654C67.0364 87.1125 67.0198 87.061 66.9888 87.0181C66.9405 86.9381 66.8628 86.8801 66.7723 86.8566C66.297 86.7209 65.3132 87.2671 65.2742 87.2902C65.2618 87.2969 65.252 87.3075 65.2461 87.3202C65.2403 87.333 65.2387 87.3473 65.2417 87.3611C65.2444 87.3752 65.2508 87.3873 65.2609 87.3975C65.2706 87.4072 65.2831 87.4137 65.2967 87.416C65.5805 87.4729 65.8673 87.5054 66.1573 87.5133ZM66.6009 86.9698C66.6446 86.9698 66.6876 86.9751 66.73 86.9857C66.7848 87.0003 66.8321 87.035 66.8624 87.083C66.9113 87.1671 66.8948 87.2088 66.8789 87.232C66.7492 87.4127 66.0315 87.4034 65.5006 87.3193C65.839 87.133 66.2105 87.0143 66.5942 86.9698H66.6009Z" fill="#407BFF"/><path d="M65.3014 87.4164C65.3055 87.4182 65.3101 87.4192 65.3146 87.4192C65.3192 87.4192 65.3237 87.4182 65.3278 87.4164C65.6669 87.2681 66.337 86.6662 66.2787 86.3424C66.2787 86.2683 66.2105 86.1742 66.0264 86.1517C65.9604 86.1429 65.8932 86.1478 65.8292 86.1662C65.7652 86.1845 65.7056 86.2159 65.6543 86.2583C65.2981 86.5497 65.2332 87.3131 65.2332 87.3456C65.2314 87.3572 65.2328 87.3691 65.2371 87.38C65.2419 87.3908 65.2494 87.4002 65.259 87.4071C65.2717 87.4149 65.2866 87.4182 65.3014 87.4164ZM65.9741 86.2775H66.0132C66.1363 86.2941 66.1423 86.3391 66.1456 86.3557C66.1813 86.5497 65.7284 87.0251 65.3821 87.229C65.4073 86.9086 65.5331 86.604 65.7417 86.3583C65.8069 86.3042 65.8894 86.2755 65.9741 86.2775Z" fill="#407BFF"/><path opacity="0.2" d="M63.5627 83.3203L63.566 83.3296L64.0548 85.2098L61.5209 85.8668L60.958 84.1422L63.5627 83.3203Z" fill="black"/><path d="M64.8155 81.9678C64.8798 82.8287 64.9314 84.1102 64.9314 84.1102L60.1226 85.2268L59.4365 83.2135L64.8155 81.9678Z" fill="#263238"/><path opacity="0.2" d="M64.8155 81.9678C64.8798 82.8287 64.9314 84.1102 64.9314 84.1102L60.1226 85.2268L59.4365 83.2135L64.8155 81.9678Z" fill="#2B4160"/><path d="M61.8978 83.7471L61.5521 84.0153L62.778 88.2194L60.3415 88.8532L58.7852 84.5557L61.8978 83.7471Z" fill="#FFC4C0"/><path d="M63.0895 87.6133H59.9862C59.9302 87.6122 59.8755 87.6311 59.8322 87.6667C59.7888 87.7022 59.7596 87.7521 59.7498 87.8074L59.271 90.3311C59.2603 90.3931 59.2631 90.4568 59.2794 90.5176C59.2957 90.5784 59.325 90.6349 59.3654 90.6832C59.4057 90.7315 59.456 90.7705 59.5129 90.7974C59.5698 90.8243 59.6319 90.8385 59.6949 90.8391C60.814 90.8232 62.4299 90.7616 63.8371 90.7649C65.4841 90.7649 65.4907 90.8589 67.4191 90.8649C68.5873 90.8649 68.872 89.6874 68.3899 89.5709C66.1503 89.0789 65.7496 89.0239 63.7914 87.8365C63.5828 87.6982 63.3397 87.6209 63.0895 87.6133Z" fill="#27397A"/><path d="M64.2652 88.0898C64.6307 88.0898 64.9802 88.0408 65.0928 87.8793C65.1215 87.8347 65.1354 87.7823 65.1325 87.7294C65.1297 87.6765 65.1102 87.6258 65.0769 87.5847C65.0285 87.5053 64.9507 87.4484 64.8604 87.4264C64.3817 87.2973 63.4079 87.8601 63.3655 87.8859C63.3542 87.8928 63.3452 87.9029 63.3397 87.915C63.3348 87.9272 63.3337 87.9406 63.3364 87.9534C63.3385 87.9674 63.3451 87.9804 63.3551 87.9904C63.3651 88.0004 63.378 88.0069 63.392 88.009C63.6802 88.061 63.9724 88.088 64.2652 88.0898ZM64.6989 87.5396C64.7425 87.5396 64.7856 87.5449 64.8279 87.5555C64.8836 87.5688 64.9319 87.6025 64.9637 87.6495C65.0127 87.7336 64.9994 87.776 64.9802 87.7985C64.8544 87.9825 64.1355 87.9859 63.6052 87.9084C63.9428 87.7157 64.3136 87.5906 64.6989 87.5396Z" fill="#407BFF"/><path d="M63.4053 88.0091H63.4305C63.7549 87.8542 64.4303 87.2424 64.3661 86.9219C64.3661 86.8444 64.2946 86.7504 64.1105 86.7312C64.0443 86.7242 63.9774 86.7309 63.9139 86.751C63.8505 86.771 63.7918 86.804 63.7417 86.8477C63.3888 87.1424 63.3371 87.9092 63.3371 87.9416C63.3362 87.9535 63.3385 87.9654 63.3437 87.976C63.3495 87.9866 63.357 87.9954 63.3662 88.0025C63.3781 88.0085 63.3921 88.0108 63.4053 88.0091ZM64.0516 86.861H64.09C64.2131 86.861 64.2224 86.9186 64.2224 86.9351C64.2621 87.1292 63.8152 87.6112 63.4722 87.8184C63.4927 87.498 63.6139 87.1921 63.8185 86.9451C63.8858 86.8875 63.9723 86.8574 64.0608 86.861H64.0516Z" fill="#407BFF"/><path opacity="0.2" d="M61.5449 83.8418L62.0396 85.6892L59.4387 86.3652L58.7852 84.5569L61.5449 83.8418Z" fill="black"/><path d="M62.8938 82.5762C62.9846 83.4338 63.0654 84.7153 63.0654 84.7153L58.295 85.9544L57.5605 83.961L62.8938 82.5762Z" fill="#263238"/><path opacity="0.3" d="M62.8938 82.5762C62.9846 83.4338 63.0654 84.7153 63.0654 84.7153L58.295 85.9544L57.5605 83.961L62.8938 82.5762Z" fill="#2B4160"/><path d="M40.765 59.6786C40.7101 61.8827 40.675 66.3544 39.6908 69.0988C39.5226 69.5651 40.0597 71.636 40.0597 71.636C40.5554 71.0143 40.9891 70.3455 41.3545 69.6393C41.9624 68.0213 41.7849 66.7266 41.8657 65.704C42.0399 63.4814 41.72 62.2609 41.3637 59.4521C41.2763 59.6177 40.9074 59.7952 40.765 59.6786Z" fill="#263238"/><path d="M62.4586 83.2659L58.2354 84.2758L52.1579 70.7206C50.1937 73.4417 46.9804 77.8879 44.3268 81.5997C39.6997 88.0717 32.8679 86.7154 31.7063 82.2467C31.1312 80.0008 30.7853 77.7024 30.6738 75.3867L38.9453 73.047C38.9453 73.047 48.744 62.3679 54.0705 62.5043C57.7367 61.1355 62.4586 83.2659 62.4586 83.2659Z" fill="#263238"/><path opacity="0.2" d="M38.9359 73.0459C38.9359 73.0459 34.4704 77.9325 34.3764 76.3855C34.2824 74.8386 38.9359 73.0459 38.9359 73.0459Z" fill="black"/><path d="M41.2596 59.7033C41.3231 59.576 41.3763 59.4438 41.4186 59.308C41.4995 58.5179 41.577 57.7276 41.651 56.9368C41.5669 56.8362 41.4603 56.817 41.3762 56.717C41.3146 56.8627 41.1822 58.8067 41.0881 58.9332H40.8485C40.6319 58.4187 39.5546 56.4998 39.5546 56.374C39.409 56.374 39.3282 56.623 39.2666 56.7521C39.409 56.9494 40.4154 59.2504 40.7584 59.7251C40.9253 59.7556 41.0961 59.7476 41.2596 59.7033Z" fill="#263238"/><path d="M36.5672 53.4142C36.3033 53.8566 36.2154 54.3821 36.3209 54.8864C36.4083 55.3877 36.9685 56.4168 37.1818 56.883C37.444 57.4725 38.1076 58.8897 39.2659 59.4009C39.8096 59.64 40.6672 58.8446 40.8421 57.8446C41.1149 58.3252 41.4525 58.766 41.8454 59.1546C42.3368 59.3294 42.2176 55.1711 41.5831 54.3427C40.1884 52.5433 37.146 52.5148 36.5672 53.4142Z" fill="#407BFF"/><path opacity="0.8" d="M36.5672 53.4142C36.3033 53.8566 36.2154 54.3821 36.3209 54.8864C36.4083 55.3877 36.9685 56.4168 37.1818 56.883C37.444 57.4725 38.1076 58.8897 39.2659 59.4009C39.8096 59.64 40.6672 58.8446 40.8421 57.8446C41.1149 58.3252 41.4525 58.766 41.8454 59.1546C42.3368 59.3294 42.2176 55.1711 41.5831 54.3427C40.1884 52.5433 37.146 52.5148 36.5672 53.4142Z" fill="#263238"/><path d="M40.8093 49.8457L41.3921 51.7066L42.1848 54.2403C41.953 54.372 41.7457 54.5442 41.5729 54.7482C41.3525 54.9509 41.2136 55.227 41.182 55.5247C41.1504 55.8225 41.2284 56.1216 41.4013 56.366C41.596 56.5859 41.1656 57.9064 39.6252 57.0779C39.1708 56.8733 38.7637 56.5768 38.4295 56.207C38.0954 55.8373 37.8414 55.4024 37.6836 54.9296C37.5604 54.4085 37.577 54.1621 38.065 53.8648C39.463 53.0171 40.2981 51.2854 40.8093 49.8457Z" fill="#FFC4C0"/><path opacity="0.2" d="M42.0009 53.6175L41.5737 51.8506L41.4797 51.9612C41.3495 52.1274 41.2328 52.3036 41.1306 52.4884C40.7843 53.1195 41.0366 54.2328 41.483 54.8573L41.5869 54.7474C41.753 54.5562 41.9417 54.3911 42.1532 54.252H42.1949L42.0009 53.6175Z" fill="black"/><path d="M44.8613 51.2787C46.6996 49.8741 46.9936 48.7252 48.3823 48.1689C48.9776 48.1689 49.408 49.502 48.1008 50.7767C46.793 52.0521 44.8613 51.2787 44.8613 51.2787Z" fill="#263238"/><path d="M45.9577 53.6487C45.7412 53.8672 45.4948 54.0546 45.2266 54.2049C43.4664 55.2115 41.0427 54.5029 40.5182 52.3348C40.48 52.1703 40.4508 52.0038 40.4308 51.8361C40.2824 50.4322 40.991 49.2899 42.0069 47.4032C42.2631 46.908 42.6401 46.4855 43.1029 46.1748C43.5658 45.8641 44.0996 45.6751 44.6549 45.6255C45.2101 45.5758 45.769 45.6671 46.2796 45.8908C46.7903 46.1144 47.2362 46.4633 47.5762 46.9052C49.0901 48.9011 47.5206 52.0858 45.9577 53.6487Z" fill="#FFC4C0"/><path opacity="0.2" d="M45.8866 53.7293C43.7508 55.5804 41.0553 54.5022 40.5308 52.3346C40.4926 52.17 40.4634 52.0036 40.4434 51.8359V51.6743C40.505 50.8908 40.6089 50.0755 41.0487 49.4093C41.1436 49.408 41.2374 49.398 41.3301 49.3795C41.3818 49.3596 41.8514 49.396 41.848 49.4543C41.7348 50.6643 42.7507 51.1014 43.6958 51.7458C43.9905 51.9458 44.4336 52.069 44.8832 52.3928C44.8832 52.4154 45.8866 53.1915 45.8866 53.7293Z" fill="black"/><path d="M40.8421 49.1951L41.67 49.8196L42.8091 48.7904L43.4568 48.1692L45.9224 47.3374C45.9224 47.3374 43.7449 45.3956 43.0362 44.9426C42.7899 44.781 41.5249 45.0592 41.2435 45.2989C40.8637 45.6386 40.5864 46.0777 40.4429 46.5666C40.2993 47.0555 40.2951 47.5748 40.4308 48.0659C40.5289 48.4553 40.6673 48.8341 40.8421 49.1951Z" fill="#263238"/><path d="M40.2126 48.4478C40.0451 48.9557 40.0696 49.5073 40.2808 49.9972C40.5622 50.6256 41.3323 50.4084 41.6686 49.8165C41.9666 49.2894 42.0408 48.3604 41.5587 47.93C41.0767 47.4996 40.4232 47.8175 40.2126 48.4478Z" fill="#FFC4C0"/><path d="M44.7115 50.1362C44.5982 50.2977 44.5916 50.4951 44.7115 50.5693C44.8307 50.6441 44.9797 50.5693 45.1029 50.4143C45.1583 50.3543 45.189 50.2757 45.189 50.1941C45.189 50.1125 45.1583 50.0339 45.1029 49.9739C44.9929 49.8964 44.8214 49.9547 44.7115 50.1362ZM46.5327 51.4514C46.4195 51.617 46.4128 51.8144 46.5327 51.8918C46.6519 51.97 46.8142 51.8918 46.9241 51.7336C47.0341 51.5746 47.0407 51.3746 46.9241 51.2965C46.8076 51.2183 46.6393 51.2892 46.5327 51.4514Z" fill="#263238"/><path d="M45.862 51.1514C45.7002 51.6907 45.6101 52.2489 45.5938 52.8117C45.453 52.8026 45.316 52.7622 45.1928 52.6933C45.0696 52.6244 44.9634 52.5289 44.8818 52.4137L45.862 51.1514Z" fill="#FBAFAA"/><path d="M44.7047 53.0594C44.5632 52.8774 44.3861 52.7262 44.1842 52.6149C43.9823 52.5037 43.7599 52.4348 43.5304 52.4124C43.5187 52.4111 43.5069 52.4135 43.4967 52.4194C43.4864 52.4252 43.4783 52.4341 43.4735 52.4449C43.47 52.4537 43.4685 52.4632 43.4693 52.4726C43.47 52.4821 43.4729 52.4912 43.4778 52.4994C43.4827 52.5075 43.4894 52.5144 43.4974 52.5196C43.5053 52.5247 43.5144 52.5279 43.5238 52.529C43.7729 52.5564 44.0122 52.6415 44.2228 52.7773C44.4333 52.9132 44.6094 53.0962 44.7371 53.3118C44.7417 53.3204 44.7483 53.3279 44.7563 53.3335C44.7644 53.3391 44.7737 53.3427 44.7834 53.344C44.7931 53.3452 44.803 53.3442 44.8122 53.3409C44.8214 53.3376 44.8297 53.3322 44.8364 53.325C44.8443 53.3164 44.8493 53.3054 44.8506 53.2938C44.8519 53.2821 44.8495 53.2703 44.8437 53.2601C44.7987 53.1919 44.7537 53.121 44.7047 53.0594ZM44.7457 49.3083C44.7312 49.2949 44.7199 49.2784 44.7126 49.26C44.7044 49.2299 44.7083 49.1977 44.7236 49.1705C44.7388 49.1433 44.7641 49.1231 44.7941 49.1143C44.9683 49.0568 45.154 49.0433 45.3347 49.0749C45.5155 49.1066 45.6856 49.1824 45.8299 49.2957C45.8484 49.3095 45.8621 49.3288 45.869 49.3508C45.8759 49.3728 45.8756 49.3964 45.8683 49.4183C45.8631 49.4327 45.8555 49.4462 45.8457 49.458C45.8238 49.4799 45.794 49.4922 45.763 49.4922C45.7319 49.4922 45.7022 49.4799 45.6802 49.458C45.5654 49.3714 45.4314 49.3138 45.2896 49.2901C45.1478 49.2663 45.0023 49.2771 44.8656 49.3216C44.8464 49.331 44.8249 49.3347 44.8037 49.3324C44.7825 49.33 44.7624 49.3217 44.7457 49.3083ZM47.2643 50.3964C47.2643 50.3757 47.2696 50.3563 47.2802 50.3382C47.2899 50.326 47.3019 50.316 47.3155 50.3087C47.3292 50.3014 47.3443 50.297 47.3597 50.2958C47.3908 50.293 47.4217 50.3025 47.4458 50.3223C47.5921 50.4328 47.7087 50.5778 47.7854 50.7443C47.8622 50.9107 47.8965 51.0936 47.8856 51.2766C47.8863 51.2933 47.8835 51.3099 47.8772 51.3254C47.8709 51.3408 47.8614 51.3547 47.8493 51.3662C47.8372 51.3776 47.8227 51.3863 47.8069 51.3917C47.7911 51.397 47.7743 51.3989 47.7577 51.3971C47.7327 51.3948 47.7091 51.3844 47.6906 51.3674C47.6721 51.3504 47.6596 51.3278 47.6551 51.3031C47.6523 51.2869 47.6525 51.2702 47.6557 51.2541C47.6623 51.1094 47.6333 50.9653 47.5713 50.8344C47.5093 50.7035 47.4162 50.5897 47.3001 50.5031C47.2858 50.4896 47.2751 50.4728 47.2689 50.4542C47.2626 50.4356 47.2611 50.4158 47.2643 50.3964ZM41.6496 45.6095C42.2708 46.4354 44.5106 47.0824 45.3418 47.6937C45.8454 48.1049 46.2871 48.5868 46.6531 49.1242C46.7524 48.5613 46.6041 48.2666 46.3776 47.7427C46.9234 48.4138 47.2976 49.2078 47.4677 50.056C47.7827 49.4985 47.8972 48.8499 47.7922 48.2182C48.368 49.0639 48.6549 50.073 48.6101 51.0951C48.963 50.3755 49.1572 49.5885 49.1796 48.7873C49.202 47.9861 49.0521 47.1895 48.7399 46.4513C48.0928 44.9625 45.753 43.0889 44.1735 43.4803C41.0052 44.2671 41.6496 45.6095 41.6496 45.6095Z" fill="#263238"/><path d="M40.985 66.5675C36.7004 67.6807 31.3225 56.4261 31.3225 56.4261C30.6755 55.6851 31.3026 53.9341 32.0443 53.2938C32.2204 53.1409 32.4249 53.0243 32.6461 52.9505C32.8673 52.8768 33.1009 52.8473 33.3335 52.8638C33.5661 52.8804 33.7931 52.9426 34.0017 53.0469C34.2102 53.1513 34.3961 53.2957 34.5488 53.4719C34.5488 53.4719 38.9136 62.5709 40.9658 62.412L49.5338 61.3736C52.0098 60.9273 50.9483 62.8656 50.0649 63.2895L42.1433 66.1337C41.7664 66.3023 41.38 66.4471 40.985 66.5675Z" fill="#407BFF"/><path opacity="0.7" d="M40.985 66.5675C36.7004 67.6807 31.3225 56.4261 31.3225 56.4261C30.6755 55.6851 31.3026 53.9341 32.0443 53.2938C32.2204 53.1409 32.4249 53.0243 32.6461 52.9505C32.8673 52.8768 33.1009 52.8473 33.3335 52.8638C33.5661 52.8804 33.7931 52.9426 34.0017 53.0469C34.2102 53.1513 34.3961 53.2957 34.5488 53.4719C34.5488 53.4719 38.9136 62.5709 40.9658 62.412L49.5338 61.3736C52.0098 60.9273 50.9483 62.8656 50.0649 63.2895L42.1433 66.1337C41.7664 66.3023 41.38 66.4471 40.985 66.5675Z" fill="#2B4160"/><path d="M55.8741 64.0209C55.8741 64.0209 54.0947 64.0434 53.7483 64.1281L52.7774 62.9626L50.6516 62.9983C50.5112 63.0044 50.3716 62.9747 50.2459 62.9119C50.1202 62.8491 50.0125 62.7553 49.9331 62.6394C49.8457 62.2414 50.2377 61.6685 50.8298 61.4738C50.9589 61.4321 54.5576 61.8202 54.5576 61.8202C55.6867 62.3288 55.8741 64.0209 55.8741 64.0209Z" fill="#FFC4C0"/></g></g></g><defs><filter id="filter0_d_2_295" x="35.5315" y="-0.081346" width="123.334" height="90.1881" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="2.64892"/><feGaussianBlur stdDeviation="3.21181"/><feComposite in2="hardAlpha" operator="out"/><feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/><feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2_295"/><feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2_295" result="shape"/></filter><radialGradient id="paint0_radial_2_295" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(40.4377 85.6787) rotate(90) scale(2.32907 40.0717)"><stop stop-color="#838383"/><stop offset="1" stop-color="#F5F5F5" stop-opacity="0"/></radialGradient><clipPath id="clip0_2_295"><rect width="200" height="104" fill="white"/></clipPath></defs></svg>'
//const rporto_noData = '<img  src="../../img/isomatric/No-Data-Found/RPO_RTO_Summary_No_Data_Found.svg" class="Card_NoData_Img d-flex h-100">'
const rporto_noData = '<svg class="mx-auto" width="300" height="250" viewBox="0 0 200 137" fill="none"><g clip-path="url(#clip0_2_258)"><mask id="mask0_2_258" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="2" width="200" height="146"><path d="M199.521 2.88281H0.478516V147.116H199.521V2.88281Z" fill="white"/></mask><g mask="url(#mask0_2_258)"><g opacity="0.4"><path d="M82.2952 60.4768C88.6656 42.7918 82.5914 23.5895 68.5709 12.6364C65.0866 9.92203 61.197 7.77264 57.0448 6.26705C44.107 1.54965 30.3827 3.56218 19.6786 10.5556C11.9982 15.5936 6.16593 22.9943 3.06292 31.6397C1.30814 36.4469 0.451594 41.5356 0.535949 46.6524C0.766723 63.7105 11.3727 79.6791 28.349 85.8484C50.2167 93.8332 74.3826 82.4503 82.2952 60.4768ZM31.9269 75.8838C19.219 71.2645 11.2727 59.2873 11.0765 46.4861C11.038 43.3624 11.4582 40.25 12.3237 37.2484C12.5208 36.5561 12.7506 35.8955 12.9804 35.2359C15.4103 28.4723 19.8758 23.0606 25.4586 19.4317C33.4702 14.1855 43.7801 12.6681 53.4668 16.198C56.579 17.3283 59.4932 18.9431 62.1016 20.9827C72.6095 29.1983 77.173 43.5832 72.4124 56.8469C66.4363 73.3434 48.3446 81.856 31.9269 75.8838Z" fill="#BDDBFF"/><path d="M25.4783 19.4309L19.6992 10.5556C30.4044 3.56106 44.129 1.54752 57.0661 6.23328C61.218 7.73841 65.1073 9.88718 68.5915 12.6008L62.123 20.9819C59.5623 19.0011 56.6728 17.352 53.488 16.1971C43.7684 12.6672 33.4901 14.1846 25.4783 19.4309Z" fill="#1C7AFF"/><path d="M12.3331 37.2441L2.18652 34.3065C2.45 33.4152 2.74424 32.492 3.0731 31.6007C6.32419 22.5945 12.2686 15.3683 19.6891 10.5518L25.4345 19.4271C19.8853 23.0561 15.4197 28.4669 12.9571 35.2316C12.76 35.8912 12.5302 36.5835 12.3331 37.2441Z" fill="#66A5FF"/><path d="M0.536044 46.6153C0.46684 42.4541 1.02044 38.306 2.17848 34.3086L12.3245 37.2444C11.4589 40.2465 11.0387 43.3594 11.0773 46.4836L0.536044 46.6153Z" fill="#0043A2"/></g><path d="M56.2925 50.3015C56.8579 49.6918 57.1406 48.877 57.1406 47.8571C57.1406 46.8372 56.8579 46.0224 56.2925 45.4128C55.738 44.8025 54.9896 44.4974 54.0472 44.4974C53.1164 44.4974 52.3683 44.8025 51.8029 45.4128C51.2484 46.0218 50.9711 46.8366 50.9711 47.8571C50.9711 48.8764 51.2484 49.6912 51.8029 50.3015C52.3683 50.9105 53.1164 51.215 54.0472 51.215C54.9896 51.215 55.738 50.9105 56.2925 50.3015ZM49.8067 52.1477C48.6541 51.0162 48.0778 49.5857 48.0778 47.8562C48.0778 46.1266 48.6541 44.7019 49.8067 43.582C50.9708 42.4511 52.3898 41.8857 54.0636 41.8857C55.7495 41.8857 57.1631 42.4511 58.3041 43.582C59.458 44.7019 60.0346 46.1266 60.034 47.8562C60.034 49.5851 59.4574 51.0149 58.3041 52.1458C57.1509 53.2766 55.7374 53.842 54.0636 53.842C52.3904 53.842 50.9715 53.2766 49.8067 52.1458M37.5158 44.2176V42.0559H46.7277V44.2176H43.5353V53.7295H40.6909V44.2176H37.5158ZM29.7194 49.1899V53.7295H26.876V42.0559H31.4819C32.8345 42.0559 33.8878 42.4053 34.6417 43.1041C35.3955 43.8022 35.7725 44.6445 35.7725 45.6311C35.7725 46.3074 35.5673 46.967 35.1571 47.61C34.7468 48.2536 34.0763 48.7081 33.1455 48.9735L36.0052 53.7295H32.6791L30.0521 49.1899H29.7194ZM29.7194 44.2185V47.0283H31.4819C31.9146 47.0283 32.2528 46.9007 32.4964 46.6455C32.7515 46.391 32.8791 46.0583 32.8791 45.6474C32.8791 45.2263 32.7515 44.8827 32.4964 44.6166C32.2413 44.3506 31.9031 44.2176 31.4819 44.2176L29.7194 44.2185Z" fill="#0862CA" fill-opacity="0.5"/><g filter="url(#filter0_d_2_258)"><path d="M37.1721 23.623H177.605C184.23 23.623 189.603 29.0241 189.603 35.6829V109.973C189.603 116.632 184.23 122.033 177.605 122.033H37.1721C30.547 122.033 25.1738 116.632 25.1738 109.973V35.6829C25.1738 29.0241 30.547 23.623 37.1721 23.623Z" fill="white"/></g><path opacity="0.25" d="M73.1429 73.0268C75.843 65.5304 73.2679 57.3897 67.3253 52.7472C65.8481 51.597 64.1994 50.6859 62.4396 50.0471C59.8404 49.094 57.0543 48.762 54.3039 49.0777C51.5535 49.3935 48.9151 50.3481 46.5995 51.8654C43.3446 54.0015 40.8727 57.1382 39.5569 60.8023C38.8128 62.84 38.4494 64.997 38.4848 67.166C38.5819 74.3971 43.0782 81.1666 50.2737 83.7821C59.5433 87.1668 69.7889 82.3416 73.1429 73.0268ZM51.7911 79.5579C46.4043 77.6001 43.0359 72.523 42.9523 67.0958C42.935 65.7718 43.1132 64.4524 43.4811 63.1803C43.5648 62.887 43.6629 62.6072 43.76 62.3274C44.7402 59.5746 46.5985 57.2205 49.0487 55.6281C50.7838 54.4896 52.7612 53.7726 54.8228 53.5345C56.8845 53.2964 58.9732 53.5438 60.9222 54.2569C62.2414 54.7368 63.4768 55.4215 64.5829 56.2858C66.8154 58.04 68.439 60.4525 69.2235 63.1812C70.0079 65.9099 69.9134 68.8163 68.9533 71.4883C66.4205 78.4809 58.751 82.0897 51.7911 79.5579Z" fill="#3C3C3C"/><path d="M61.9702 68.5631C62.2074 68.3067 62.326 67.9643 62.326 67.5361C62.326 67.1072 62.2074 66.7649 61.9702 66.5091C61.7375 66.2527 61.4234 66.1245 61.0278 66.1245C60.6368 66.1245 60.3224 66.2527 60.0845 66.5091C59.8512 66.7649 59.7345 67.1072 59.7345 67.5361C59.7345 67.9643 59.8512 68.3067 60.0845 68.5631C60.3217 68.8195 60.6358 68.9477 61.0269 68.9477C61.423 68.9477 61.7375 68.8195 61.9702 68.5631ZM59.246 69.3381C58.7614 68.8631 58.5191 68.2624 58.5191 67.5361C58.5191 66.8098 58.7614 66.2114 59.246 65.7409C59.7345 65.2658 60.3307 65.0283 61.0346 65.0283C61.7423 65.0283 62.3359 65.2655 62.8154 65.7399C63.3 66.2104 63.5423 66.8092 63.5423 67.5361C63.5423 68.2624 63.3 68.8631 62.8154 69.3381C62.3308 69.8131 61.7371 70.0506 61.0346 70.0506C60.332 70.0506 59.7358 69.8125 59.246 69.3381ZM57.945 66.6015C57.945 66.9322 57.8261 67.2512 57.5883 67.5582C57.4626 67.7166 57.2742 67.847 57.0229 67.9496C56.741 68.0532 56.4423 68.1031 56.1421 68.0967H55.4016V70.0035H54.2074V65.0995H56.1421C56.7107 65.0995 57.153 65.2463 57.469 65.5399C57.7857 65.8335 57.945 66.1873 57.945 66.6015ZM55.4007 67.188H56.1411C56.3238 67.188 56.4658 67.1345 56.5671 67.0274C56.6207 66.9719 56.6626 66.9062 56.6903 66.8342C56.7181 66.7622 56.7311 66.6853 56.7286 66.6082C56.7318 66.5289 56.7192 66.4499 56.6915 66.3756C56.6637 66.3013 56.6214 66.2332 56.5671 66.1755C56.5116 66.1195 56.445 66.0758 56.3717 66.047C56.2984 66.0182 56.2198 66.0049 56.1411 66.0082H55.4007V67.188ZM50.5976 68.0967V70.0035H49.4043V65.0995H51.339C51.907 65.0995 52.3493 65.2463 52.666 65.5399C52.9826 65.8335 53.141 66.1873 53.141 66.6015C53.141 66.8854 53.0551 67.1624 52.8833 67.4323C52.7108 67.7028 52.4288 67.8938 52.0371 68.0054L53.2391 70.0035H51.8419L50.738 68.0967H50.5976ZM50.5976 66.0082V67.188H51.338C51.5201 67.188 51.6621 67.1345 51.764 67.0274C51.8174 66.9718 51.8592 66.9061 51.8867 66.8341C51.9143 66.7621 51.9272 66.6853 51.9246 66.6082C51.9279 66.529 51.9154 66.45 51.8879 66.3757C51.8603 66.3014 51.8182 66.2333 51.764 66.1755C51.7085 66.1195 51.642 66.0758 51.5686 66.047C51.4953 66.0182 51.4168 66.0049 51.338 66.0082H50.5976Z" fill="#353535"/><path opacity="0.7" d="M49.0575 55.6308L46.6084 51.8691C48.9242 50.3531 51.5617 49.398 54.3112 49.0799C57.0608 48.7617 59.8467 49.0892 62.4475 50.0363C64.2073 50.6748 65.856 51.5856 67.3332 52.7355L64.5918 56.2885C63.4848 55.4255 62.2496 54.7409 60.9311 54.2596C58.9821 53.5465 56.8933 53.2991 54.8317 53.5372C52.7701 53.7753 50.7927 54.4923 49.0575 55.6308Z" fill="#3C3C3C"/><path opacity="0.6" d="M43.4888 63.1776L39.1875 61.9323C39.299 61.5544 39.4241 61.163 39.5625 60.7851C40.9415 56.9675 43.4609 53.9038 46.6063 51.8613L49.0421 55.6241C46.5991 57.2245 44.7428 59.5759 43.7532 62.3237C43.6696 62.6035 43.5725 62.8978 43.4888 63.1776Z" fill="#838383"/><g opacity="0.5"><path opacity="0.75" d="M38.4849 67.1538C38.4556 65.3897 38.6903 63.6312 39.1811 61.9365L43.4829 63.1819C43.1149 64.4542 42.9367 65.7737 42.9539 67.098L38.4849 67.1538Z" fill="#838383"/></g><path d="M74.6052 93.3887H38.875V96.158H74.6052V93.3887Z" fill="#676767"/><path d="M65.6135 99.2744H38.875V102.044H65.6135V99.2744Z" fill="#CECECE"/><path d="M74.6052 106.23H38.875V108.999H74.6052V106.23Z" fill="#A1A1A1"/><path d="M65.6135 112.114H38.875V114.884H65.6135V112.114Z" fill="#A9A9A9"/><path opacity="0.25" d="M122.183 73.0268C124.883 65.5304 122.309 57.3897 116.365 52.7472C114.888 51.597 113.239 50.6859 111.48 50.0471C108.88 49.094 106.094 48.762 103.344 49.0777C100.594 49.3935 97.9551 50.3481 95.6396 51.8654C92.3846 54.0015 89.9128 57.1382 88.597 60.8023C87.8529 62.84 87.4894 64.997 87.5248 67.166C87.6219 74.3971 92.1183 81.1666 99.3137 83.7821C108.584 87.1668 118.829 82.3416 122.183 73.0268ZM100.831 79.5579C95.4444 77.6001 92.076 72.523 91.9933 67.0958C91.976 65.7718 92.1542 64.4524 92.5221 63.1803C92.6048 62.887 92.7029 62.6072 92.8 62.3274C93.7805 59.5744 95.6392 57.2203 98.0897 55.6281C99.8247 54.4897 101.802 53.7728 103.863 53.5348C105.925 53.2967 108.013 53.544 109.962 54.2569C111.281 54.7368 112.517 55.4215 113.623 56.2858C115.855 58.04 117.479 60.4525 118.264 63.1812C119.048 65.9099 118.953 68.8163 117.993 71.4883C115.461 78.4809 107.791 82.0897 100.831 79.5579Z" fill="#3C3C3C"/><path opacity="0.7" d="M98.0966 55.6309L95.6475 51.8692C97.963 50.3525 100.6 49.397 103.35 49.0788C106.1 48.7607 108.886 49.0886 111.487 50.0364C113.246 50.6749 114.895 51.5857 116.372 52.7356L113.63 56.2886C112.523 55.4256 111.288 54.741 109.969 54.2597C108.02 53.5468 105.932 53.2995 103.87 53.5376C101.809 53.7756 99.8316 54.4925 98.0966 55.6309Z" fill="#3C3C3C"/><path opacity="0.6" d="M92.5288 63.1776L88.2275 61.9323C88.3391 61.5544 88.4641 61.163 88.6026 60.7851C89.9815 56.9675 92.501 53.9038 95.6464 51.8613L98.0822 55.6241C95.6392 57.2245 93.7829 59.5759 92.7933 62.3237C92.7096 62.6035 92.6125 62.8978 92.5288 63.1776Z" fill="#838383"/><g opacity="0.5"><path opacity="0.75" d="M87.5239 67.1538C87.4946 65.3897 87.7293 63.6312 88.2202 61.9365L92.521 63.1819C92.153 64.4542 91.9748 65.7737 91.992 67.098L87.5239 67.1538Z" fill="#838383"/></g><path d="M123.255 93.3887H87.5244V96.158H123.255V93.3887Z" fill="#676767"/><path d="M114.263 99.2744H87.5244V102.044H114.263V99.2744Z" fill="#CECECE"/><path d="M123.255 106.23H87.5244V108.999H123.255V106.23Z" fill="#A1A1A1"/><path d="M114.263 112.114H87.5244V114.884H114.263V112.114Z" fill="#B2B2B2"/><path d="M34.2654 23.624C27.6402 23.624 25.1738 29.1895 25.1738 36.0522V38.158H189.603V36.0522C189.603 29.1895 186.721 23.624 180.096 23.624H34.2654Z" fill="#1C7AFF"/><path d="M158.593 30.8379C158.593 29.0032 160.178 27.5166 162.135 27.5166C164.092 27.5166 165.678 29.0032 165.678 30.8379C165.678 32.6716 164.092 34.1582 162.135 34.1582C160.178 34.1582 158.593 32.6716 158.593 30.8379ZM167.894 30.8379C167.894 29.9573 168.244 29.1127 168.866 28.4899C169.489 27.8672 170.333 27.5172 171.214 27.5171C172.095 27.517 172.939 27.8667 173.562 28.4892C174.185 29.1118 174.535 29.9563 174.535 30.8369C174.535 31.7175 174.185 32.5621 173.562 33.1847C172.94 33.8074 172.095 34.1572 171.214 34.1572C170.334 34.1572 169.489 33.8074 168.867 33.1847C168.244 32.5621 167.894 31.7185 167.894 30.8379ZM176.306 30.8379C176.306 29.9572 176.656 29.1125 177.279 28.4897C177.901 27.867 178.746 27.5171 179.627 27.5171C180.507 27.5171 181.352 27.867 181.975 28.4897C182.598 29.1125 182.948 29.9572 182.948 30.8379C182.948 31.7186 182.598 32.5633 181.975 33.186C181.352 33.8088 180.507 34.1587 179.627 34.1587C178.746 34.1587 177.901 33.8088 177.279 33.186C176.656 32.5633 176.306 31.7186 176.306 30.8379Z" fill="white"/><path d="M172.949 101.433L141.745 101.578C137.548 101.598 134.866 97.1097 136.879 93.4278L152.355 65.119C154.435 61.3121 159.898 61.2852 162.013 65.0738H162.014L177.74 93.2336C179.79 96.9039 177.142 101.413 172.949 101.433Z" fill="#E9F0FF" stroke="#757474" stroke-width="4.80779"/><path d="M160.559 92.5571C160.561 92.1869 160.489 91.8201 160.347 91.4782C160.212 91.1545 160.01 90.8629 159.755 90.6224C159.49 90.3755 159.178 90.1845 158.838 90.0608C158.452 89.9215 158.044 89.8541 157.633 89.8618C157.22 89.8579 156.809 89.9291 156.421 90.0704C156.079 90.1954 155.765 90.3897 155.501 90.6406C155.248 90.883 155.049 91.1762 154.915 91.5003C154.776 91.8432 154.707 92.2103 154.712 92.5801C154.709 92.9484 154.781 93.3119 154.925 93.6504C155.062 93.9706 155.263 94.2581 155.517 94.4966C155.782 94.7466 156.096 94.937 156.438 95.06C156.828 95.1985 157.239 95.2658 157.653 95.2581C158.063 95.2629 158.47 95.1927 158.855 95.0504C159.193 94.9235 159.502 94.7302 159.763 94.4812C160.015 94.2408 160.214 93.9504 160.349 93.6292C160.491 93.2898 160.563 92.9253 160.559 92.5571ZM155.452 87.5434L159.796 87.5261L160.35 72.6582L154.772 72.6803L155.452 87.5434Z" fill="#757474"/><path d="M110.944 68.5553C111.172 68.3092 111.287 67.9797 111.287 67.5668C111.287 67.1534 111.172 66.8232 110.944 66.5764C110.719 66.3303 110.416 66.2072 110.035 66.2072C109.658 66.2072 109.355 66.3306 109.126 66.5774C108.902 66.8248 108.79 67.1547 108.79 67.5668C108.79 67.9797 108.902 68.3095 109.126 68.5563C109.355 68.8031 109.658 68.9265 110.035 68.9265C110.417 68.9265 110.72 68.8022 110.944 68.5553ZM108.318 69.3044C107.852 68.8467 107.618 68.2678 107.618 67.5678C107.618 66.8678 107.852 66.2908 108.318 65.837C108.79 65.3799 109.365 65.1514 110.042 65.1514C110.724 65.1514 111.296 65.3802 111.758 65.8379C112.225 66.2905 112.458 66.8671 112.458 67.5678C112.458 68.2685 112.225 68.8473 111.758 69.3044C111.291 69.7615 110.719 69.9903 110.042 69.991C109.365 69.991 108.79 69.7621 108.318 69.3044ZM103.477 66.0937V65.2187H107.206V66.0937H105.914V69.9429H104.763V66.0937H103.477ZM100.455 68.1053V69.9429H99.3037V65.2187H101.168C101.716 65.2187 102.142 65.36 102.447 65.6427C102.592 65.7704 102.709 65.9278 102.787 66.1044C102.866 66.2809 102.906 66.4724 102.905 66.6658C102.905 66.9396 102.822 67.2066 102.656 67.4668C102.489 67.7271 102.218 67.9111 101.841 68.0188L102.999 69.9439H101.653L100.589 68.1072L100.455 68.1053ZM100.455 66.0927V67.2303H101.168C101.244 67.2349 101.319 67.2236 101.39 67.197C101.461 67.1705 101.525 67.1295 101.579 67.0764C101.631 67.0231 101.671 66.9599 101.698 66.8904C101.724 66.821 101.737 66.7469 101.734 66.6726C101.737 66.5961 101.725 66.5197 101.698 66.4479C101.672 66.3761 101.631 66.3102 101.579 66.2543C101.526 66.1999 101.462 66.1574 101.391 66.1295C101.32 66.1017 101.244 66.0892 101.168 66.0927H100.455Z" fill="#353535"/></g></g><defs><filter id="filter0_d_2_258" x="15.8467" y="18.1422" width="183.083" height="117.063" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/><feOffset dy="3.84623"/><feGaussianBlur stdDeviation="4.66355"/><feComposite in2="hardAlpha" operator="out"/><feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/><feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2_258"/><feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2_258" result="shape"/></filter><clipPath id="clip0_2_258"><rect width="200" height="137" fill="white"/></clipPath></defs></svg>'
//const infraAll_noData = '<img src="../../img/isomatric/No-Data-Found/Component_Monitor_No_Data_Found.svg" class="Card_NoData_Img d-flex">'
const infraAll_noData = '<svg style="width:50%; height:100%;" width="200" height="134" viewBox="0 0 200 134" fill="none"><g clip-path="url(#clip0_2_89)"><mask id="mask0_2_89" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="2" y="-6" width="196" height="146"><path d="M197.421 -5.61719H2.57715V139.617H197.421V-5.61719Z" fill="white"/></mask><g mask="url(#mask0_2_89)"><mask id="mask1_2_89" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="2" y="-6" width="195" height="146"><path d="M196.784 -5.21973H2.59668V139.357H196.784V-5.21973Z" fill="white"/></mask><g mask="url(#mask1_2_89)"><path d="M196.784 121.819H2.59668V121.896H196.784V121.819ZM177.327 128.067H164.472V128.144H177.327V128.067ZM164.083 124.496H156.626V124.574H164.083V124.496ZM39.7646 125.117H22.9864V125.195H39.7646V125.117ZM45.6683 125.117H43.2212V125.195H45.6683V125.117ZM94.6422 104.513H19.6471C18.4433 104.513 17.4337 103.504 17.4337 102.302V-3.0077C17.4337 -4.21031 18.4433 -5.21973 19.6471 -5.21973H94.6422C95.8461 -5.21973 96.8564 -4.21031 96.8564 -3.0077V102.302C96.8564 103.543 95.8854 104.513 94.6422 104.513ZM19.6471 -5.14218C19.3667 -5.14357 19.0887 -5.08934 18.8293 -4.98263C18.5698 -4.87592 18.334 -4.71883 18.1355 -4.52042C17.9369 -4.32201 17.7795 -4.08619 17.6724 -3.82657C17.5652 -3.56694 17.5104 -3.28864 17.5111 -3.0077V102.302C17.5105 102.583 17.5653 102.861 17.6725 103.12C17.7798 103.38 17.9372 103.616 18.1357 103.814C18.3343 104.012 18.57 104.169 18.8294 104.276C19.0888 104.383 19.3667 104.437 19.6471 104.436H94.6422C94.9226 104.437 95.2005 104.383 95.4599 104.276C95.7193 104.169 95.9551 104.012 96.1537 103.814C96.3522 103.616 96.5096 103.38 96.6168 103.12C96.724 102.861 96.7789 102.583 96.7783 102.302V-3.0077C96.7789 -3.28858 96.724 -3.56682 96.6168 -3.82637C96.5096 -4.08592 96.3522 -4.32165 96.1537 -4.52C95.9551 -4.71834 95.7193 -4.87537 95.4599 -4.98204C95.2005 -5.08871 94.9226 -5.14291 94.6422 -5.14152L19.6471 -5.14218ZM178.647 104.513H103.652C102.448 104.513 101.438 103.504 101.438 102.302V-3.0077C101.438 -4.21031 102.448 -5.21973 103.652 -5.21973H178.647C179.851 -5.21973 180.861 -4.21031 180.861 -3.0077V102.302C180.861 103.543 179.89 104.513 178.647 104.513ZM103.652 -5.14218C103.372 -5.14357 103.094 -5.08934 102.834 -4.98263C102.575 -4.87592 102.339 -4.71883 102.141 -4.52042C101.942 -4.32201 101.785 -4.08619 101.677 -3.82657C101.57 -3.56694 101.516 -3.28864 101.516 -3.0077V102.302C101.516 102.583 101.57 102.861 101.678 103.12C101.785 103.38 101.942 103.616 102.141 103.814C102.339 104.012 102.575 104.169 102.835 104.276C103.094 104.383 103.372 104.437 103.652 104.436H178.647C178.928 104.437 179.206 104.383 179.465 104.276C179.724 104.169 179.96 104.012 180.159 103.814C180.357 103.616 180.515 103.38 180.622 103.12C180.729 102.861 180.784 102.583 180.783 102.302V-3.0077C180.784 -3.28858 180.729 -3.56682 180.622 -3.82637C180.515 -4.08592 180.357 -4.32165 180.159 -4.52C179.96 -4.71834 179.724 -4.87537 179.465 -4.98204C179.206 -5.08871 178.928 -5.14291 178.647 -5.14152L103.652 -5.14218Z" fill="#EBEBEB"/><path d="M115.096 40.9878H168.614V5.98828H115.096V40.9878Z" fill="#E6E6E6"/><path d="M113.476 40.9878H167.848V5.98828H113.476V40.9878Z" fill="#F0F0F0"/><path d="M115.096 47.8703H168.614V41.0029H115.096V47.8703Z" fill="#E6E6E6"/><path d="M110.754 47.8703H165.126V41.0029H110.754V47.8703Z" fill="#F0F0F0"/><path d="M165.555 38.742V8.28223H115.766V38.742H165.555Z" fill="#FAFAFA"/><path d="M154.337 38.7045L147.968 8.28418H138.063L144.394 38.7045H154.337Z" fill="white"/><path d="M164.512 36.4538C164.59 36.4538 164.668 36.3763 164.668 36.2987V9.75763C164.668 9.68009 164.59 9.60254 164.512 9.60254C164.472 9.60515 164.434 9.62231 164.405 9.65083C164.377 9.67936 164.359 9.7173 164.356 9.75763V36.2599C164.356 36.3763 164.434 36.4538 164.512 36.4538Z" fill="#F0F0F0"/><path d="M142.297 38.7045L135.928 8.28418H132.044L138.413 38.7045H142.297Z" fill="white"/><path d="M116.058 38.7235V8.26367H115.785V38.7235H116.058Z" fill="#E6E6E6"/><path opacity="0.6" d="M112.934 11.699H166.452L166.646 9.17676H113.128L112.934 11.699ZM112.934 15.8897H166.452L166.646 13.3675H113.128L112.934 15.8897ZM112.934 20.0805H166.452L166.646 17.5583H113.128L112.934 20.0805ZM112.934 24.2713H166.452L166.646 21.7491H113.128L112.934 24.2713ZM112.934 28.462H166.452L166.646 25.9398H113.128L112.934 28.462ZM112.934 32.6515H166.452L166.646 30.1299H113.128L112.934 32.6515Z" fill="#EBEBEB"/><path d="M31.3447 40.9878H84.8624V5.98828H31.3447V40.9878Z" fill="#E6E6E6"/><path d="M29.7246 40.9878H84.0978V5.98828H29.7253L29.7246 40.9878Z" fill="#F0F0F0"/><path d="M31.3447 47.8703H84.8624V41.0029H31.3447V47.8703Z" fill="#E6E6E6"/><path d="M27.0039 47.8703H81.3764V41.0029H27.0039V47.8703Z" fill="#F0F0F0"/><path d="M81.8119 38.7127V8.25293H32.0225V38.7127H81.8119Z" fill="#FAFAFA"/><path d="M70.6024 38.7045L64.2319 8.28418H54.2891L60.6589 38.7045H70.6024Z" fill="white"/><path d="M80.7778 36.4538C80.8554 36.4538 80.9336 36.3763 80.9336 36.2987V9.75763C80.9336 9.68009 80.8554 9.60254 80.7778 9.60254C80.7375 9.60515 80.6995 9.62231 80.6708 9.65083C80.6422 9.67936 80.6249 9.7173 80.6221 9.75763V36.2599C80.6221 36.3763 80.6608 36.4538 80.7778 36.4538Z" fill="#F0F0F0"/><path d="M58.5241 38.7045L52.1533 8.28418H48.3086L54.6787 38.7045H58.5241Z" fill="white"/><path d="M32.3147 38.7322V8.27246H32.042V38.7322H32.3147Z" fill="#E6E6E6"/><path opacity="0.6" d="M29.2012 11.699H82.7195L82.9137 9.17676H29.3954L29.2012 11.699Z" fill="#EBEBEB"/><path opacity="0.6" d="M29.2012 13.1733H82.7195L82.9137 10.6123H29.3954L29.2012 13.1733Z" fill="#EBEBEB"/><path opacity="0.6" d="M29.2012 14.6088H82.7195L82.9137 12.0479H29.3954L29.2012 14.6088Z" fill="#EBEBEB"/><path opacity="0.6" d="M29.2012 16.045H82.7195L82.9137 13.4834H29.3954L29.2012 16.045Z" fill="#EBEBEB"/><path opacity="0.6" d="M29.2012 17.4799H82.7195L82.9137 14.9189H29.3954L29.2012 17.4799Z" fill="#EBEBEB"/><path opacity="0.6" d="M29.2012 18.9165H82.7195L82.9137 16.3555H29.3954L29.2012 18.9165Z" fill="#EBEBEB"/><path d="M105.213 120.906C141.49 120.906 170.899 119.194 170.899 117.081C170.899 114.969 141.49 113.257 105.213 113.257C68.9356 113.257 39.5273 114.969 39.5273 117.081C39.5273 119.193 68.9356 120.906 105.213 120.906Z" fill="url(#paint0_radial_2_89)"/><path d="M43.0394 51.9696L42.6523 51.9972L43.1686 59.157L43.5557 59.1294L43.0394 51.9696ZM43.8214 62.828L43.4343 62.8562L43.6292 65.5651L44.0163 65.5375L43.8214 62.828ZM133.751 95.7443H50.5989C48.7351 95.7443 47.0649 94.1927 46.9487 92.3303L42.8701 35.6402C42.754 33.7778 44.1521 32.2256 46.0552 32.2256H129.246C131.11 32.2256 132.78 33.7778 132.896 35.6402L136.974 92.3303C137.052 94.1927 135.654 95.7443 133.751 95.7443Z" fill="#676767"/><path d="M134.061 95.7443H50.9104C49.0459 95.7443 47.3763 94.1927 47.2596 92.3303L43.1816 35.6402C43.0648 33.7778 44.4635 32.2256 46.3667 32.2256H129.556C131.421 32.2256 133.09 33.7778 133.207 35.6402L137.285 92.3303C137.401 94.1927 135.965 95.7443 134.061 95.7443Z" fill="#E0E0E0"/><path d="M129.635 33.9336H46.1734C44.0373 34.1275 44.4644 37.3476 46.6391 37.3476H129.946C132.082 37.3476 132.043 34.1275 129.907 33.9336H129.635Z" fill="#1C7AFF"/><path d="M48.6203 35.6404C48.659 36.0669 48.3093 36.4158 47.8822 36.4158C47.4551 36.4158 47.0667 36.0669 47.0273 35.6404C46.9886 35.2132 47.3383 34.8643 47.7654 34.8643C48.1925 34.8643 48.5809 35.2139 48.6203 35.6404ZM51.261 35.6404C51.3004 36.0669 50.9507 36.4158 50.5229 36.4158C50.0965 36.4158 49.7074 36.0669 49.6687 35.6404C49.6294 35.2132 49.9797 34.8643 50.4068 34.8643C50.8726 34.8643 51.261 35.2139 51.261 35.6404ZM53.9417 35.6404C53.9811 36.0669 53.6308 36.4158 53.2037 36.4158C52.7766 36.4158 52.3882 36.0669 52.3494 35.6404C52.3101 35.2132 52.6598 34.8643 53.0875 34.8643C53.514 34.8643 53.903 35.2139 53.9417 35.6404Z" fill="#FAFAFA"/><path d="M131.887 90.0791H52.3094C51.9725 90.0804 51.6478 89.9524 51.4021 89.7214C51.1564 89.4905 51.0083 89.174 50.9882 88.8371L47.6096 42.1971C47.5702 41.4986 48.0754 40.9551 48.7747 40.9551H128.353C128.69 40.9541 129.014 41.0823 129.259 41.3132C129.505 41.5442 129.653 41.8604 129.673 42.1971L133.052 88.8371C133.052 89.5357 132.547 90.0791 131.887 90.0791Z" fill="white"/><path d="M99.6768 75.7556L98.1803 54.7735L93.599 52.0088H80.9883L82.7124 75.7556H99.6768Z" fill="white"/><path d="M98.1831 54.7745L93.6016 52.0098L95.2799 55.6807L98.1831 54.7745Z" fill="#EBEBEB"/><path d="M99.6788 75.9847H82.6692C82.5328 75.9847 82.4422 75.8941 82.4422 75.8034L80.7188 52.0572C80.7188 52.0112 80.7188 51.9212 80.764 51.8758C80.8093 51.8305 80.8546 51.7852 80.9451 51.7852H93.5552C93.6011 51.7852 93.6464 51.7852 93.6464 51.8305L98.2276 54.5946C98.2729 54.6406 98.3182 54.6852 98.3182 54.7759L99.8153 75.758C99.8153 75.8034 99.8153 75.8941 99.77 75.9394C99.8153 75.9847 99.77 75.9847 99.6788 75.9847ZM82.8956 75.5767H99.4525L97.9554 54.9573L93.5099 52.2833H81.2174L82.8956 75.5767Z" fill="#757474"/><path d="M95.2795 55.9075C95.189 55.9075 95.0984 55.8622 95.0984 55.7715L93.4203 52.1012C93.3744 52.0105 93.4203 51.9198 93.4656 51.8291C93.5561 51.7831 93.6466 51.7384 93.7378 51.7831L98.3189 54.5478C98.4095 54.5938 98.4547 54.6839 98.4095 54.7746C98.4095 54.8653 98.3189 54.9106 98.2737 54.9559L95.3707 55.8628C95.3248 55.9075 95.2795 55.9075 95.2795 55.9075ZM94.1006 52.5993L95.416 55.4094L97.6839 54.7292L94.1006 52.5993ZM88.1156 62.7519C88.1608 63.2954 87.7528 63.7035 87.2083 63.7035C86.9467 63.7056 86.694 63.6088 86.5006 63.4324C86.3073 63.256 86.1875 63.013 86.1652 62.7519C86.1199 62.2078 86.528 61.7997 87.0725 61.7997C87.617 61.7997 88.0703 62.2078 88.1156 62.7519ZM94.3328 62.7506C94.3788 63.2948 93.9701 63.7029 93.4262 63.7029C92.8817 63.7029 92.4277 63.2948 92.3824 62.7506C92.3372 62.2071 92.7452 61.799 93.2897 61.799C93.8343 61.7537 94.2876 62.2071 94.3328 62.7506ZM96.1002 68.9589C95.9644 68.9589 95.8732 68.8682 95.8732 68.7775C95.7827 67.2365 93.3337 65.9675 90.476 65.9675C88.5709 65.9675 86.8927 66.5116 86.0307 67.4178C85.6679 67.8259 85.4862 68.2787 85.5314 68.7322C85.5314 68.8682 85.4409 68.9589 85.3504 68.9589C85.2139 68.9589 85.1234 68.8682 85.1234 68.7775C85.0781 68.188 85.3051 67.6446 85.7584 67.1458C86.6657 66.1488 88.5256 65.5147 90.5213 65.5147C93.6512 65.5147 96.2367 66.919 96.3725 68.7322C96.3272 68.8229 96.2367 68.9589 96.1002 68.9589ZM84.5775 60.8021C84.5323 60.8021 84.487 60.8021 84.4411 60.7568C84.4196 60.7363 84.4025 60.7117 84.3908 60.6844C84.3791 60.6571 84.373 60.6278 84.373 60.5981C84.373 60.5684 84.3791 60.539 84.3908 60.5117C84.4025 60.4844 84.4196 60.4598 84.4411 60.4394L85.3937 59.3971C85.4142 59.3756 85.4388 59.3585 85.4661 59.3469C85.4934 59.3352 85.5228 59.3292 85.5524 59.3292C85.5821 59.3292 85.6115 59.3352 85.6387 59.3469C85.666 59.3585 85.6907 59.3756 85.7112 59.3971C85.7328 59.4174 85.7501 59.4419 85.7619 59.4691C85.7737 59.4964 85.7798 59.5258 85.7798 59.5555C85.7798 59.5852 85.7737 59.6145 85.7619 59.6418C85.7501 59.669 85.7328 59.6936 85.7112 59.7138L84.7586 60.7568C84.6681 60.7568 84.6228 60.8021 84.5775 60.8021ZM95.6469 60.8021C95.6016 60.8021 95.5557 60.8021 95.5104 60.7568L94.3768 59.7138C94.3552 59.6936 94.3379 59.669 94.3261 59.6418C94.3143 59.6145 94.3082 59.5852 94.3082 59.5555C94.3082 59.5258 94.3143 59.4964 94.3261 59.4691C94.3379 59.4419 94.3552 59.4174 94.3768 59.3971C94.3973 59.3756 94.422 59.3585 94.4492 59.3469C94.4765 59.3352 94.5059 59.3292 94.5356 59.3292C94.5652 59.3292 94.5946 59.3352 94.6219 59.3469C94.6492 59.3585 94.6738 59.3756 94.6943 59.3971L95.828 60.4394C95.8495 60.4598 95.8666 60.4844 95.8783 60.5117C95.89 60.539 95.896 60.5684 95.896 60.5981C95.896 60.6278 95.89 60.6571 95.8783 60.6844C95.8666 60.7117 95.8495 60.7363 95.828 60.7568C95.7374 60.7568 95.6922 60.8021 95.6469 60.8021Z" fill="#757474"/><path d="M140.337 36.242C140.678 36.0873 141.007 35.9063 141.32 35.7005C141.658 35.4975 141.997 35.3286 142.336 35.1255C143.003 34.7278 143.658 34.3101 144.301 33.8729C145.575 33.0038 146.765 32.0182 147.858 30.9288L148.264 30.5227L148.467 30.319L148.569 30.2178L148.603 30.1501C148.569 30.2513 148.603 30.2178 148.603 30.1159C148.636 30.0489 148.636 29.8793 148.636 29.7439C148.671 29.1347 148.535 28.356 148.4 27.6114C148.095 26.0888 147.654 24.5319 147.18 23.0086L148.501 22.4336C149.314 23.8886 150.026 25.4119 150.568 27.0364C150.839 27.8487 151.076 28.6944 151.144 29.7104C151.144 29.9812 151.177 30.2519 151.11 30.5562C151.076 30.8611 150.974 31.2331 150.703 31.6734L150.635 31.7411L150.601 31.8088L150.534 31.8765L150.432 32.0118L150.229 32.2484C150.092 32.426 149.945 32.5956 149.788 32.7564C148.636 34.0425 147.316 35.1932 145.926 36.2085C145.241 36.7207 144.529 37.1952 143.792 37.63C143.42 37.8666 143.047 38.0703 142.674 38.2727C142.302 38.4764 141.929 38.6788 141.489 38.8819L140.337 36.242Z" fill="#FFC4C0"/><path d="M137.323 115.813C137.594 115.813 137.865 115.745 138.068 115.711C138.102 115.711 138.102 115.677 138.102 115.643C138.102 115.61 138.102 115.576 138.068 115.576C137.967 115.508 137.12 114.966 136.781 115.102C136.713 115.136 136.679 115.169 136.645 115.305C136.627 115.37 136.627 115.439 136.645 115.505C136.663 115.57 136.698 115.63 136.747 115.677C136.916 115.779 137.085 115.813 137.323 115.813ZM137.865 115.61C137.356 115.711 136.984 115.677 136.849 115.576C136.781 115.508 136.781 115.44 136.781 115.339C136.781 115.271 136.814 115.271 136.849 115.237C137.018 115.169 137.526 115.407 137.865 115.61Z" fill="#407BFF"/><path d="M138.069 115.711C138.136 115.677 138.136 115.677 138.136 115.643C138.136 115.609 138.136 114.797 137.832 114.526C137.758 114.459 137.661 114.423 137.561 114.425C137.391 114.458 137.357 114.56 137.323 114.628C137.221 114.932 137.764 115.576 138.069 115.711C138.035 115.711 138.035 115.711 138.069 115.711ZM137.561 114.56C137.628 114.56 137.662 114.594 137.696 114.628C137.865 114.797 137.934 115.237 137.967 115.508C137.696 115.305 137.391 114.831 137.425 114.628C137.425 114.628 137.459 114.594 137.561 114.56Z" fill="#407BFF"/><path d="M137.797 27.7812C137.458 29.4735 136.78 32.8585 137.932 34.0092C137.932 34.0092 137.458 35.7015 134.341 35.7015C130.886 35.7015 132.681 34.0092 132.681 34.0092C134.544 33.5689 134.51 32.181 134.172 30.8614L137.797 27.7812Z" fill="#FFC4C0"/><path d="M132.071 34.6863C131.528 34.754 132.002 33.3661 132.206 33.2307C132.714 32.8923 139.286 32.4185 139.219 33.2307C139.185 33.5692 139.015 34.246 138.744 34.4833C138.473 34.6522 136.779 33.9753 132.071 34.6863Z" fill="#263238"/><path d="M133.123 34.2104C132.682 34.3458 132.751 32.9585 132.886 32.7889C133.225 32.3828 138.544 31.029 138.679 31.8078C138.747 32.1462 138.781 32.7889 138.578 33.0597C138.408 33.2963 136.85 32.8908 133.123 34.2104ZM131.157 24.7682C131.124 24.7682 131.089 24.7682 131.055 24.734C130.683 24.2609 130.175 24.3279 130.175 24.3279C130.107 24.3279 130.039 24.2944 130.039 24.1925C130.039 24.1248 130.073 24.0571 130.175 24.0571C130.209 24.0571 130.819 23.9895 131.293 24.5651C131.327 24.6328 131.327 24.7005 131.259 24.7682C131.225 24.734 131.191 24.7682 131.157 24.7682Z" fill="#263238"/><path d="M130.514 26.3945C130.514 26.3945 130.209 27.4099 129.836 27.9178C130.175 28.1544 130.65 27.9855 130.65 27.9855L130.514 26.3945Z" fill="#FFC4C0"/><path d="M130.685 25.9878C130.719 26.2244 130.617 26.4282 130.447 26.4282C130.312 26.4282 130.176 26.2586 130.142 26.0555C130.108 25.819 130.21 25.6152 130.38 25.6152C130.516 25.6152 130.651 25.7848 130.685 25.9878Z" fill="#263238"/><path d="M130.412 25.6146L129.836 25.5127C129.87 25.5127 130.175 25.9188 130.412 25.6146Z" fill="#263238"/><path d="M141.356 115.644H138.509L138.746 109.077H141.592L141.356 115.644Z" fill="#FFC4C0"/><path d="M138.307 115.34H141.491C141.593 115.34 141.695 115.408 141.728 115.543L142.101 118.047C142.135 118.318 141.898 118.555 141.66 118.555C140.542 118.521 140.001 118.487 138.612 118.487C137.765 118.487 136.477 118.589 135.291 118.589C134.14 118.589 134.038 117.404 134.546 117.303C136.782 116.829 137.121 116.152 137.866 115.543C137.968 115.373 138.137 115.34 138.307 115.34Z" fill="#27397A"/><path opacity="0.2" d="M141.594 109.077H138.747L138.612 112.462H141.458L141.594 109.077Z" fill="black"/><path d="M130.074 37.9353C128.335 37.272 126.638 36.5034 124.992 35.6339C123.332 34.7881 121.673 33.874 120.114 32.6556C119.698 32.3428 119.312 31.9914 118.962 31.6061C118.861 31.5049 118.759 31.403 118.692 31.3018C118.59 31.1664 118.522 31.0981 118.386 30.9292C118.136 30.5685 117.984 30.1485 117.946 29.7108C117.912 29.2705 117.98 28.8309 118.116 28.4924C118.251 28.154 118.421 27.8497 118.59 27.6125C118.962 27.1051 119.335 26.7667 119.741 26.4282C120.521 25.7849 121.367 25.3111 122.214 24.8379C122.621 24.6007 123.061 24.3976 123.502 24.228C123.942 24.025 124.349 23.8561 124.823 23.7207L125.433 25.0403C123.942 25.9538 122.418 27.0033 121.3 28.154C121.044 28.4103 120.816 28.6941 120.622 29.0004C120.453 29.2712 120.453 29.4736 120.487 29.4407L120.521 29.4736L120.656 29.6096C120.69 29.6773 120.791 29.745 120.826 29.7785C121.097 30.0151 121.402 30.2865 121.74 30.5231C122.418 30.9969 123.163 31.4365 123.908 31.8768C124.673 32.3096 125.453 32.7162 126.246 33.0959C127.838 33.874 129.498 34.585 131.124 35.2619L130.074 37.9353Z" fill="#FFC4C0"/><path d="M126.212 24.091L126.686 23.0756L124.552 22.3311C124.552 22.3311 123.874 24.3282 124.687 25.2423C125.331 25.1405 125.907 24.7002 126.212 24.091Z" fill="#FFC4C0"/><path d="M126.685 20.8762L124.957 20.4023L124.551 22.3311L126.685 23.0757V20.8762ZM148.673 23.1776L148.91 20.8085L146.708 21.3158C146.708 21.3158 146.674 23.5502 147.826 23.8886L148.673 23.1776Z" fill="#FFC4C0"/><path d="M147.893 19.1514L146.436 19.7941L146.741 21.3174L148.942 20.8094L147.893 19.1514ZM138.238 24.4639C138.339 27.2727 138.441 28.4575 137.188 30.0479C135.29 32.417 131.801 31.9432 130.852 29.2021C130.006 26.7653 129.972 22.568 132.614 21.1807C135.189 19.8269 138.136 21.6545 138.238 24.4639Z" fill="#FFC4C0"/><path d="M137.017 30.0493C139.795 28.8644 141.59 26.3264 140.845 22.3999C140.099 18.6435 137.559 18.2715 136.509 19.0496C135.459 19.8277 132.85 18.6777 131.258 19.9973C128.548 22.2987 131.122 24.7354 132.443 26.2246C133.257 27.8498 134.307 31.2 137.017 30.0493Z" fill="#263238"/><path d="M135.868 21.7232C136.816 23.077 138.578 23.4496 139.865 22.5696C141.152 21.689 141.423 19.8956 140.475 18.5418C139.526 17.1881 137.764 16.8161 136.477 17.6961C135.224 18.576 134.953 20.3694 135.868 21.7232Z" fill="#0479FF"/><path d="M136.611 19.2868C136.205 17.0189 138.373 15.2584 141.218 16.1048C144.098 16.9512 142.574 19.4892 141.998 21.5199C141.422 23.5505 142.947 25.48 143.59 23.9231C144.234 22.3663 143.15 21.8925 143.15 21.8925C143.15 21.8925 146.199 22.6712 143.353 26.1233C140.507 29.5413 137.966 25.7507 138.644 23.517C139.22 21.7571 136.984 21.4528 136.611 19.2868ZM133.732 20.1661C132.411 19.4892 130.209 18.9142 128.955 21.0467C128.346 22.0614 128.582 23.4151 128.582 23.4151L132.445 23.6859L133.732 20.1661Z" fill="#263238"/><path d="M128.109 22.7389C128.041 22.7389 128.008 22.7047 128.041 22.637C128.041 22.5693 128.143 21.2156 128.956 20.3698C130.955 18.305 133.259 20.0307 133.936 20.6064C133.97 20.6399 133.97 20.6741 133.936 20.7417C133.902 20.7753 133.869 20.7753 133.801 20.7417C133.157 20.2002 130.921 18.5415 129.058 20.5052C128.279 21.3168 128.177 22.6705 128.177 22.6705C128.211 22.7047 128.143 22.7389 128.109 22.7389Z" fill="#263238"/><path d="M132.952 26.0891C132.952 26.6306 132.715 27.1721 132.376 27.5441C131.901 28.0856 131.359 27.8148 131.257 27.2056C131.156 26.6641 131.257 25.7165 131.834 25.3787C132.41 25.0738 132.952 25.4457 132.952 26.0885" fill="#FFC4C0"/><path d="M132.345 51.8457C132.345 51.8457 132.514 71.5095 134.242 82.4757C135.631 91.3429 138.172 111.82 138.172 111.82H142.034C142.034 111.82 142.407 92.0539 141.695 83.288C139.933 61.119 144.508 56.99 140.814 51.8457H132.345Z" fill="#263238"/><path opacity="0.1" d="M132.345 51.8457C132.345 51.8457 132.514 71.5095 134.242 82.4757C135.631 91.3429 138.172 111.82 138.172 111.82H142.034C142.034 111.82 142.407 92.0539 141.695 83.288C139.933 61.119 144.508 56.99 140.814 51.8457H132.345Z" fill="#263238"/><path opacity="0.3" d="M134.377 60.7468C135.732 66.6698 134.648 76.045 133.903 80.174C133.124 73.9125 132.717 65.8234 132.514 59.8668C133.225 58.7496 133.869 58.6484 134.377 60.7468Z" fill="black"/><path d="M137.628 111.887H142.541L142.812 110.161L137.595 109.991L137.628 111.887Z" fill="#0479FF"/><path d="M139.018 35.2274C139.492 34.314 141.965 33.7383 143.32 33.7383L144.336 38.2727C144.336 38.2727 141.626 42.3012 140.508 41.8609C139.187 41.3536 138.035 37.1904 139.018 35.2274Z" fill="#27397A"/><path opacity="0.4" d="M139.018 35.2274C139.492 34.314 141.965 33.7383 143.32 33.7383L144.336 38.2727C144.336 38.2727 141.626 42.3012 140.508 41.8609C139.187 41.3536 138.035 37.1904 139.018 35.2274Z" fill="#27397A"/><path d="M136.173 110.804V110.736C136.173 110.702 136.14 110.702 136.105 110.702C135.97 110.736 134.716 110.939 134.547 111.278C134.53 111.309 134.521 111.344 134.521 111.379C134.521 111.415 134.53 111.45 134.547 111.481C134.619 111.585 134.728 111.657 134.851 111.684C135.224 111.717 135.834 111.142 136.173 110.804C136.14 110.804 136.14 110.804 136.173 110.804ZM134.648 111.311C134.749 111.108 135.427 110.939 135.902 110.872C135.462 111.311 135.055 111.582 134.818 111.549C134.749 111.549 134.682 111.514 134.614 111.413C134.648 111.379 134.648 111.346 134.648 111.311Z" fill="#407BFF"/><path d="M136.171 110.804C136.171 110.771 136.171 110.771 136.171 110.804V110.736C136.137 110.703 135.663 110.127 135.188 110.093C135.053 110.093 134.917 110.127 134.816 110.229C134.68 110.364 134.68 110.465 134.714 110.533C134.85 110.838 135.765 110.939 136.137 110.838C136.137 110.804 136.137 110.804 136.171 110.804ZM134.816 110.364C134.816 110.33 134.85 110.33 134.883 110.296C134.957 110.229 135.054 110.193 135.154 110.195C135.459 110.229 135.799 110.533 135.968 110.703C135.561 110.736 134.917 110.601 134.816 110.432V110.364Z" fill="#407BFF"/><path d="M139.12 109.585L136.51 110.736L135.359 108.333L133.834 105.185L133.665 104.813L136.274 103.662L136.477 104.068L137.933 107.114L139.12 109.585Z" fill="#FFC4C0"/><path opacity="0.2" d="M137.933 107.115L135.358 108.333L133.834 105.186L136.476 104.068L137.933 107.115Z" fill="black"/><path d="M129.293 51.8457C129.293 51.8457 120.993 72.4236 123.263 82.1373C125.296 90.9032 134.273 107.488 134.273 107.488L137.728 105.761C137.728 105.761 132.172 85.7247 131.563 81.3927C130.377 72.931 137.932 60.9836 137.932 51.8792L129.293 51.8457Z" fill="#263238"/><path opacity="0.1" d="M129.293 51.8457C129.293 51.8457 120.993 72.4236 123.263 82.1373C125.296 90.9032 134.273 107.488 134.273 107.488L137.728 105.761C137.728 105.761 132.172 85.7247 131.563 81.3927C130.377 72.931 137.932 60.9836 137.932 51.8792L129.293 51.8457Z" fill="#263238"/><path d="M136.07 110.398L138.645 108.536C138.746 108.469 138.848 108.469 138.95 108.571L140.711 110.398C140.792 110.497 140.83 110.624 140.817 110.751C140.805 110.877 140.742 110.994 140.644 111.075C139.729 111.718 139.255 111.989 138.137 112.801C137.425 113.309 136.036 114.425 135.088 115.136C134.139 115.813 133.394 114.933 133.733 114.561C135.257 112.868 135.562 111.82 135.799 110.872C135.833 110.635 135.901 110.466 136.07 110.398Z" fill="#27397A"/><path d="M134.037 108.199L138.508 106.168L137.932 104.341L133.122 106.54L134.037 108.199Z" fill="#0479FF"/><path d="M131.497 34.9912C131.124 34.0429 127.872 32.9605 126.245 32.6221L125.534 37.9695C125.534 37.9695 128.177 41.7942 129.362 41.4551C130.717 41.0831 132.276 37.0553 131.497 34.9912Z" fill="#27397A"/><path opacity="0.4" d="M131.497 34.9912C131.124 34.0429 127.872 32.9605 126.245 32.6221L125.534 37.9695C125.534 37.9695 128.177 41.7942 129.362 41.4551C130.717 41.0831 132.276 37.0553 131.497 34.9912Z" fill="#27397A"/><path d="M127.941 34.7192C127.941 34.7192 126.586 35.1923 129.297 51.845H140.814C140.611 47.1738 140.611 44.2632 142.847 34.6515C142.847 34.6515 140.441 34.11 137.969 34.0081C136.037 33.9069 134.445 33.8392 132.752 34.0081C130.482 34.2118 127.941 34.7192 127.941 34.7192Z" fill="#0479FF"/><path d="M140.981 51.1668L141.489 52.1827C141.523 52.2504 141.421 52.3516 141.285 52.3516H129.192C129.09 52.3516 128.988 52.2839 128.988 52.2504L128.887 51.2345C128.887 51.1668 128.954 51.0991 129.09 51.0991H140.777C140.879 51.0656 140.947 51.0991 140.981 51.1668Z" fill="#407BFF"/><path opacity="0.3" d="M140.981 51.1668L141.489 52.1827C141.523 52.2504 141.421 52.3516 141.285 52.3516H129.192C129.09 52.3516 128.988 52.2839 128.988 52.2504L128.887 51.2345C128.887 51.1668 128.954 51.0991 129.09 51.0991H140.777C140.879 51.0656 140.947 51.0991 140.981 51.1668Z" fill="white"/><path d="M139.424 52.4559H139.729C139.796 52.4559 139.83 52.4218 139.83 52.3883L139.695 51.0345C139.695 51.0003 139.627 50.9668 139.559 50.9668H139.254C139.186 50.9668 139.153 51.0003 139.153 51.0345L139.288 52.3883C139.288 52.4218 139.356 52.4559 139.424 52.4559ZM131.834 52.4559H132.14C132.207 52.4559 132.241 52.4218 132.241 52.3883L132.105 51.0345C132.105 51.0003 132.038 50.9668 131.97 50.9668H131.665C131.598 50.9668 131.563 51.0003 131.563 51.0345L131.699 52.3883C131.733 52.4218 131.767 52.4559 131.834 52.4559Z" fill="#263238"/></g></g></g><defs><radialGradient id="paint0_radial_2_89" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(105.213 117.081) rotate(90) scale(3.82452 65.6859)"><stop stop-color="#838383"/><stop offset="1" stop-color="#F5F5F5" stop-opacity="0"/></radialGradient><clipPath id="clip0_2_89"><rect width="200" height="134" fill="white"/></clipPath></defs></svg>'
//const bf_noData = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img" style="margin-top:100px">'
const bf_noData = '<svg width="200" height="133" viewBox="0 0 200 133" fill="none"><g clip-path="url(#clip0_2_9098)"><mask id="mask0_2_9098" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="200" height="133"><path d="M0 0.984863H200V132.235H0V0.984863Z" fill="white"/></mask><g mask="url(#mask0_2_9098)"><mask id="mask1_2_9098" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="200" height="133"><path d="M200 0.984863H0V132.235H200V0.984863Z" fill="white"/></mask><g mask="url(#mask1_2_9098)"><mask id="mask2_2_9098" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="200" height="133"><path d="M200 0.984863H0V132.235H200V0.984863Z" fill="white"/></mask><g mask="url(#mask2_2_9098)"><path d="M186.1 28.5871L183.309 42.1195L181.513 40.3649L178.459 42.5479L176.124 39.6428L170.888 43.4553L166.414 35.961L161.422 38.8888L159.443 36.157L153.105 39.7337L151.2 35.6428L155.642 14.0928L179.458 19.0189L186.1 28.5871Z" fill="#EBEBEB"/><path d="M179.457 19.02L186.099 28.5882L177.837 26.8797L179.457 19.02Z" fill="#DBDBDB"/><path d="M182.715 44.8241L178.625 64.6775L146.544 58.0434L150.605 38.3502L152.511 42.4383L158.852 38.8616L160.828 41.5951L165.822 38.669L170.294 46.1638L175.532 42.3513L177.865 45.2525L180.923 43.0729L182.715 44.8241ZM136.797 42.4974L104.105 44.5235L101.507 2.49111L125.783 0.984863L134.695 8.4735L136.797 42.4974Z" fill="#EBEBEB"/><path d="M125.783 0.984863L134.696 8.4735L126.278 8.99623L125.783 0.984863ZM116.733 29.2672L116.604 28.3184C116.275 26.353 116.812 24.1559 118.517 21.8582C120.057 19.8138 120.89 18.3542 120.797 16.6962C120.694 14.8269 119.445 13.6479 117.147 13.7411C115.829 13.8104 114.563 14.2786 113.516 15.0843L112.512 12.828C113.677 11.8792 115.723 11.1786 117.657 11.0752C121.863 10.8428 123.901 13.3354 124.071 16.119C124.209 18.6076 122.922 20.4803 121.284 22.6672C119.778 24.6616 119.286 26.3048 119.5 28.165L119.591 29.1155L116.733 29.2672ZM116.213 34.4377C116.137 33.0837 116.997 32.0797 118.278 32.0099C119.559 31.94 120.49 32.8394 120.567 34.1974C120.637 35.478 119.853 36.5195 118.499 36.5877C117.208 36.661 116.275 35.7212 116.201 34.4434L116.213 34.4377Z" fill="#DBDBDB"/><path d="M34.9575 22.1181L31.6752 20.626L31.2166 21.6317C31.1496 21.776 31.0443 21.8991 30.9122 21.9877C30.78 22.0762 30.6261 22.1268 30.4672 22.134C30.2877 22.1416 30.1093 22.1596 29.932 22.188C29.7752 22.2105 29.6153 22.1904 29.469 22.1297C29.3227 22.069 29.1955 21.97 29.1007 21.8431L28.4564 20.9499L25.5223 23.0578L26.1661 23.951C26.2552 24.0811 26.3075 24.2328 26.3175 24.3901C26.3275 24.5474 26.2948 24.7045 26.2229 24.8448C26.14 25.0055 26.0656 25.1705 26.0002 25.3391C25.9424 25.4871 25.8455 25.6166 25.7198 25.7137C25.5941 25.8108 25.4443 25.8718 25.2865 25.8902L24.182 25.9988L24.5388 29.5993L25.6433 29.4885C25.8018 29.4758 25.9607 29.5064 26.1031 29.5772C26.2454 29.648 26.3658 29.7562 26.4513 29.8902C26.4979 29.9669 26.5479 30.0385 26.599 30.1118C26.6513 30.1842 26.7049 30.2556 26.7598 30.326C26.8613 30.4489 26.9267 30.5975 26.9484 30.7554C26.9702 30.9132 26.9476 31.0741 26.8831 31.2198L26.4229 32.2363L29.7127 33.7283L30.1712 32.7152C30.2394 32.5715 30.3456 32.4491 30.4783 32.3612C30.6109 32.2733 30.765 32.2232 30.924 32.2164C31.1032 32.2092 31.2816 32.1914 31.4593 32.163C31.6158 32.1401 31.7756 32.1601 31.9217 32.2208C32.0677 32.2815 32.1946 32.3807 32.2888 32.5078L32.9343 33.401L35.8729 31.2931L35.2286 30.3908C35.1361 30.2623 35.0816 30.1104 35.0711 29.9524C35.0607 29.7944 35.0948 29.6366 35.1695 29.4971C35.2502 29.3368 35.3227 29.1732 35.3871 29.0061C35.4436 28.8572 35.5401 28.7269 35.666 28.6294C35.792 28.5319 35.9424 28.4712 36.1007 28.4539L37.2053 28.3431L36.8485 24.7482L35.7422 24.8556C35.5847 24.8691 35.4266 24.8388 35.2852 24.7681C35.1438 24.6975 35.0248 24.5891 34.9411 24.455C34.8917 24.3805 34.8413 24.3066 34.7899 24.2334C34.7391 24.1605 34.6855 24.0896 34.6291 24.0209C34.5303 23.8968 34.4668 23.7483 34.4454 23.5911C34.424 23.4338 34.4455 23.2738 34.5075 23.1277L34.9575 22.1181ZM33.0916 25.4488C33.3461 25.8049 33.5185 26.213 33.5966 26.6437C33.6747 27.0744 33.6565 27.517 33.5434 27.9398C33.4302 28.3626 33.2248 28.7552 32.942 29.0892C32.6592 29.4233 32.3059 29.6906 31.9075 29.8721C31.3747 30.1138 30.7821 30.1919 30.2048 30.0964C29.6275 30.0009 29.0916 29.7361 28.6649 29.3357C28.2738 28.9689 27.9895 28.503 27.8423 27.9874C27.6951 27.4718 27.6905 26.926 27.8291 26.408C27.9676 25.89 28.2441 25.4193 28.629 25.0461C29.014 24.6729 29.4929 24.411 30.0149 24.2885C30.585 24.1553 31.1818 24.1945 31.7296 24.4011C32.2774 24.6077 32.7515 24.9723 33.0916 25.4488ZM183.602 69.8817L181.409 71.1499L181.796 71.8203C181.854 71.9166 181.883 72.0276 181.88 72.1399C181.876 72.2523 181.841 72.3613 181.777 72.4539C181.709 72.5596 181.645 72.6669 181.586 72.7817C181.538 72.8823 181.462 72.967 181.367 73.0257C181.272 73.0845 181.162 73.115 181.051 73.1135H180.278V75.6505H181.055C181.166 75.6496 181.276 75.6806 181.371 75.7399C181.466 75.7991 181.541 75.8842 181.589 75.9851C181.647 76.0972 181.71 76.2061 181.779 76.3118C181.843 76.404 181.879 76.5129 181.882 76.6251C181.886 76.7374 181.856 76.8483 181.798 76.9442L181.411 77.6158L183.607 78.8845L183.998 78.2107C184.054 78.1149 184.136 78.0367 184.235 77.985C184.333 77.9333 184.444 77.9101 184.555 77.9181H184.927C185.038 77.9109 185.149 77.9344 185.247 77.9861C185.345 78.0377 185.427 78.1154 185.484 78.2107L185.875 78.8885L188.064 77.6215L187.676 76.9459C187.621 76.8491 187.594 76.7387 187.598 76.6272C187.602 76.5158 187.637 76.4077 187.7 76.3152C187.767 76.2101 187.831 76.0993 187.889 75.9885C187.937 75.8879 188.013 75.8031 188.108 75.7443C188.203 75.6854 188.312 75.6548 188.424 75.6561H189.198V73.1192H188.417C188.305 73.1206 188.195 73.0901 188.1 73.0312C188.006 72.9723 187.93 72.8875 187.881 72.7868C187.823 72.6754 187.76 72.5663 187.693 72.4595C187.63 72.3672 187.595 72.2592 187.591 72.1478C187.587 72.0365 187.614 71.9262 187.669 71.8294L188.056 71.1539L185.868 69.8868L185.477 70.5533C185.42 70.6491 185.337 70.727 185.238 70.7785C185.139 70.83 185.027 70.8529 184.916 70.8448H184.544C184.433 70.8516 184.322 70.8281 184.223 70.7768C184.124 70.7254 184.041 70.6482 183.983 70.5533L183.602 69.8817ZM184.738 72.3061C185.149 72.3054 185.551 72.4267 185.893 72.6547C186.235 72.8827 186.501 73.2071 186.659 73.5868C186.776 73.8706 186.83 74.177 186.815 74.4839C186.801 74.7908 186.718 75.0907 186.574 75.362C186.43 75.6333 186.227 75.8693 185.981 76.0531C185.735 76.2368 185.451 76.3638 185.15 76.4249C184.848 76.4847 184.537 76.4771 184.239 76.4024C183.941 76.3278 183.663 76.188 183.426 75.9932C183.188 75.7984 182.997 75.5533 182.865 75.2757C182.734 74.998 182.665 74.6946 182.665 74.3874C182.664 74.1143 182.717 73.8438 182.82 73.5911C182.924 73.3385 183.076 73.1088 183.269 72.9152C183.461 72.7216 183.69 72.5678 183.942 72.4627C184.194 72.3576 184.465 72.3033 184.738 72.3027V72.3061ZM41.6104 11.3294L39.6019 12.8692L40.0598 13.4914C40.1291 13.5794 40.1716 13.6855 40.1824 13.7969C40.1932 13.9084 40.1717 14.0207 40.1206 14.1203C40.0645 14.2361 40.0163 14.3555 39.9763 14.4777C39.9403 14.5829 39.8756 14.6759 39.7896 14.7463C39.7036 14.8167 39.5996 14.8616 39.4894 14.876L38.7183 14.976L39.045 17.4902L39.8155 17.3902C39.9278 17.3749 40.0421 17.3916 40.1453 17.4383C40.2485 17.4851 40.3365 17.56 40.399 17.6544C40.4687 17.756 40.5443 17.8529 40.6257 17.9453C40.7009 18.0282 40.7507 18.1308 40.7693 18.241C40.7879 18.3513 40.7746 18.4646 40.7308 18.5675L40.4382 19.2823L42.7718 20.2539L43.0627 19.5397C43.1068 19.438 43.1784 19.3507 43.2695 19.2875C43.3605 19.2242 43.4673 19.1876 43.578 19.1817L43.7655 19.1607L43.9439 19.1323C44.0519 19.1121 44.1634 19.1219 44.2663 19.1606C44.3691 19.1992 44.4594 19.2653 44.5274 19.3516L45.0019 19.9738L47.0178 18.4118L46.5485 17.7959C46.4801 17.7071 46.4382 17.6009 46.4274 17.4893C46.4167 17.3778 46.4375 17.2655 46.4877 17.1652C46.543 17.0524 46.5911 16.9368 46.632 16.8186C46.6677 16.713 46.7323 16.6194 46.8184 16.5486C46.9046 16.4778 47.0089 16.4326 47.1195 16.4181L47.8899 16.3181L47.5632 13.8039L46.7928 13.9044C46.6808 13.92 46.5667 13.9038 46.4635 13.8576C46.3603 13.8114 46.2722 13.737 46.2093 13.6431C46.139 13.5412 46.0621 13.444 45.9791 13.3522C45.9114 13.2674 45.8675 13.1661 45.8521 13.0587C45.8368 12.9513 45.8504 12.8417 45.8916 12.7414L46.1842 12.0266L43.8507 11.0527L43.5604 11.7675C43.515 11.8684 43.443 11.9549 43.3521 12.0179C43.2612 12.0809 43.1548 12.1179 43.0445 12.1249L42.857 12.1408H42.6791C42.5711 12.1615 42.4593 12.152 42.3564 12.1132C42.2534 12.0744 42.1631 12.0078 42.0956 11.9209L41.6104 11.3294ZM43.0479 13.5863C43.6035 13.5881 44.1356 13.8105 44.5274 14.2044C44.8191 14.4986 45.0174 14.8724 45.0973 15.2789C45.1771 15.6853 45.1351 16.1064 44.9764 16.489C44.8176 16.8717 44.5494 17.1989 44.2052 17.4295C43.8611 17.6601 43.4565 17.7838 43.0422 17.7851C42.4867 17.7849 41.9539 17.5642 41.561 17.1715C41.2683 16.8782 41.0689 16.5049 40.9878 16.0986C40.9067 15.6923 40.9475 15.2711 41.1052 14.8879C41.2628 14.5048 41.5302 14.1768 41.8737 13.9452C42.2173 13.7136 42.6216 13.5887 43.036 13.5863H43.0479ZM26.6024 77.2016C26.7115 74.3794 26.6723 71.5527 26.4848 68.7346C26.0686 62.9152 25.1826 57.139 23.8354 51.4624C23.095 48.2681 22.1854 45.1272 21.2348 41.9931C21.2064 41.8965 21.0564 41.9232 21.0814 42.0232C24.0155 53.0885 26.424 64.5039 26.2547 76.0067C26.2081 79.2226 25.8354 82.3868 25.5411 85.5868C25.5411 85.6709 25.6718 85.7067 25.6928 85.6192C26.2905 82.8658 26.4882 79.9982 26.6024 77.2016Z" fill="#EBEBEB"/><path d="M21.0808 42.0093C21.0808 42.0093 16.6376 53.3081 17.6325 55.9456C18.6274 58.5831 20.678 59.1615 20.678 59.1615C20.678 59.1615 17.9592 63.0496 19.8592 66.6962C21.7587 70.3428 25.9814 70.1712 26.2723 74.3059C26.2638 74.3292 26.5007 57.0428 21.0808 42.0093Z" fill="#EBEBEB"/><path d="M25.788 67.1922C25.7949 67.1733 25.7949 67.1526 25.788 67.1337C25.2025 59.4195 23.755 51.7951 21.4726 44.403C21.4726 44.3814 21.4283 44.3865 21.4351 44.403C22.3127 47.7007 23.0903 51.0242 23.767 54.3689C23.1697 53.8024 22.4395 53.3953 21.6437 53.1848C21.6437 53.1848 21.6209 53.2019 21.6437 53.2076C22.48 53.5138 23.2317 54.0143 23.8368 54.6678C23.9647 55.3246 24.0931 55.9786 24.213 56.6331C23.2187 55.5218 21.842 54.9689 20.4664 54.4439C20.4637 54.4471 20.4623 54.4511 20.4624 54.4553C20.4624 54.4598 20.4637 54.4638 20.4664 54.4672C21.9039 55.0519 23.3334 55.8036 24.3056 57.0456C24.6446 58.9199 24.942 60.8005 25.1976 62.6877C24.4789 61.936 23.5582 61.408 22.5465 61.1672C22.5147 61.1672 22.4965 61.2047 22.5289 61.2138C23.5708 61.5195 24.5008 62.1226 25.205 62.949H25.2221C25.3092 63.61 25.3925 64.2746 25.4721 64.9428C24.9169 64.473 24.2908 64.0942 23.617 63.8206C23.5993 63.8206 23.5868 63.8422 23.617 63.8473C24.3058 64.1458 24.9363 64.5638 25.4795 65.082H25.5022C25.576 65.7166 25.6488 66.3507 25.7113 66.9831C24.7113 65.7716 23.4127 64.8418 21.9437 64.2854C21.9406 64.2853 21.9376 64.2858 21.9348 64.2869C21.932 64.2879 21.9294 64.2896 21.9272 64.2916C21.9249 64.2939 21.9231 64.2965 21.922 64.2994C21.9208 64.3024 21.9203 64.3055 21.9204 64.3087C21.9204 64.3113 21.9207 64.3144 21.9215 64.3178C21.9225 64.3207 21.9243 64.3232 21.9266 64.3252C21.9287 64.3276 21.9315 64.3294 21.9346 64.3303C21.9372 64.3314 21.9402 64.332 21.9437 64.332C23.3935 64.9716 24.6672 65.9523 25.6562 67.1905C25.6677 67.2036 25.6826 67.2132 25.6993 67.2182C25.7159 67.2232 25.7337 67.2235 25.7505 67.2189C25.8471 68.1911 25.9289 69.1627 26.0039 70.1382C26.0046 70.1408 26.0062 70.143 26.0084 70.1445L26.0158 70.1462L26.0226 70.1445C26.0248 70.143 26.0264 70.1408 26.0272 70.1382C25.9461 69.1723 25.866 68.1903 25.788 67.1922Z" fill="white"/><path d="M22.2092 52.0725C21.4029 51.5706 20.4817 51.2832 19.533 51.2378C19.5308 51.2378 19.5287 51.2384 19.5268 51.2395C19.5237 51.241 19.5212 51.2433 19.5194 51.2463C19.5177 51.2492 19.5169 51.2526 19.5171 51.256C19.517 51.2604 19.5185 51.2647 19.5215 51.268C19.5245 51.2713 19.5286 51.2733 19.533 51.2736C20.4484 51.4475 21.337 51.7418 22.1751 52.1486C22.2194 52.1582 22.241 52.0952 22.2092 52.0725ZM22.7546 64.0338C22.1346 63.6755 21.4513 63.4401 20.7421 63.3406C20.7194 63.3406 20.7171 63.373 20.7421 63.3781C21.4319 63.5173 22.0989 63.7514 22.7245 64.0736C22.7546 64.0963 22.7745 64.0554 22.7546 64.0338Z" fill="white"/><path d="M38.2867 56.1496C36.4074 57.4508 34.6815 58.9606 33.1419 60.6501C31.5533 62.4409 30.3281 64.5235 29.5345 66.7819C27.8459 71.4865 26.9379 76.4355 26.8464 81.4331C26.7896 84.2018 27.0544 86.9677 27.6362 89.6751C27.6387 89.6852 27.6431 89.6946 27.6492 89.703C27.655 89.7117 27.6625 89.7191 27.6713 89.7246C27.6802 89.7302 27.69 89.7339 27.7004 89.7354C27.7106 89.7364 27.7209 89.736 27.7311 89.7342C27.75 89.7294 27.7665 89.7177 27.7774 89.7015C27.7883 89.6852 27.7929 89.6655 27.7901 89.6462C27.0674 84.8685 27.095 80.0073 27.872 75.2382C28.6356 70.566 29.8345 65.4791 32.8026 61.6808C34.4083 59.6121 36.4316 57.9865 38.422 56.303C38.5294 56.2155 38.3953 56.0723 38.2867 56.1496Z" fill="#EBEBEB"/><path d="M27.5 78.0678C28.5689 76.3353 29.8253 74.7258 31.2466 73.2683C33.4693 71.0405 35.3409 69.4581 35.633 67.9871C35.9261 66.5166 33.4051 65.4286 33.4051 65.4286C33.4051 65.4286 36.4807 65.9842 37.3301 64.6911C38.179 63.3967 38.5784 55.9053 38.5784 55.9053C38.5784 55.9053 32.9818 59.9075 30.3722 65.5195C27.7625 71.132 27.5 78.0678 27.5 78.0678Z" fill="#EBEBEB"/><path d="M36.6395 57.8272C34.2849 60.1499 31.9139 62.6118 30.5167 65.6584C30.0432 66.7059 29.63 67.7796 29.2792 68.8743C29.2094 69.0976 29.1361 69.3192 29.0684 69.5408C29.0646 69.5524 29.0646 69.565 29.0684 69.5766C28.4097 71.8524 27.8786 74.1632 27.4775 76.4982C27.4775 76.513 27.4969 76.5198 27.5003 76.4982C27.7429 75.33 28.0071 74.163 28.3048 72.9749H28.3406C28.8045 72.7766 29.296 72.6506 29.798 72.601C29.8304 72.601 29.8264 72.5391 29.798 72.5425C29.3022 72.5645 28.8118 72.6547 28.3406 72.8107C28.6116 71.7493 28.9059 70.6897 29.2327 69.6482C30.3252 69.3848 31.4431 69.241 32.5667 69.2192M32.5667 69.2033C31.4711 69.1519 30.3734 69.2449 29.302 69.48L29.4804 68.9442C30.2029 68.7715 30.9359 68.6463 31.6747 68.5692V68.5459C30.9474 68.5596 30.2224 68.6476 29.5128 68.8084C29.8069 67.9089 30.1395 67.0225 30.5099 66.1516C30.8128 65.4457 31.1664 64.7627 31.5679 64.1079C32.1544 63.9593 32.7505 63.8517 33.352 63.7857V63.7573C32.7875 63.715 32.22 63.7712 31.6747 63.9232C31.9683 63.4457 32.2808 62.9802 32.6116 62.5277C33.8599 62.251 35.1395 61.9919 36.4241 62.2363C36.4267 62.2356 36.4291 62.2343 36.431 62.2325C36.433 62.2307 36.4345 62.2285 36.4355 62.226C36.4376 62.221 36.4378 62.2153 36.4361 62.2101C36.4326 62.2053 36.4284 62.2011 36.4236 62.1976C35.2037 61.8743 33.9906 62.0618 32.7736 62.301C32.9059 62.1226 33.0429 61.9437 33.1787 61.7772C33.9349 61.5434 34.7207 61.419 35.5122 61.4079M35.5122 61.3811C34.7775 61.3152 34.0366 61.3811 33.3247 61.5755C34.3633 60.2857 35.5088 59.0738 36.6429 57.8721C36.7219 57.8039 36.6804 57.7715 36.6253 57.8272L35.5122 61.3811Z" fill="white"/><path d="M36.5669 62.9336C36.1984 62.8782 35.8247 62.8656 35.4533 62.8961C35.409 62.8961 35.4124 62.9711 35.4533 62.9745C35.8209 62.9745 36.1902 62.9569 36.5578 62.9745C36.5639 62.973 36.5686 62.9694 36.572 62.9637C36.5738 62.9612 36.575 62.9583 36.5755 62.9553C36.5761 62.9522 36.5761 62.9491 36.5754 62.9461C36.573 62.9415 36.5705 62.9373 36.5669 62.9336ZM32.547 70.1984C31.9556 70.2105 31.3709 70.3274 30.8203 70.5438V70.5705C31.4038 70.4325 31.9675 70.3773 32.5436 70.279C32.5535 70.2734 32.5614 70.2649 32.5664 70.2546C32.5708 70.2437 32.5708 70.2315 32.5664 70.2205C32.562 70.2116 32.5553 70.2039 32.547 70.1984ZM31.3385 67.6887C31.0933 67.6875 30.8485 67.709 30.6073 67.7529C30.6043 67.754 30.6017 67.7557 30.5994 67.7578C30.5972 67.76 30.5954 67.7625 30.5942 67.7654C30.5928 67.7681 30.592 67.7711 30.5918 67.7741C30.5916 67.7771 30.5921 67.7802 30.5931 67.783C30.5961 67.7895 30.6008 67.7942 30.6073 67.7972C30.8565 67.8109 31.1064 67.7993 31.3533 67.7626C31.3578 67.7603 31.362 67.7574 31.3658 67.754C31.369 67.7501 31.3717 67.7457 31.3737 67.741C31.3751 67.7362 31.3759 67.7312 31.376 67.7262C31.3757 67.7162 31.3717 67.7067 31.3646 67.6995C31.3575 67.6928 31.3482 67.689 31.3385 67.6887Z" fill="white"/><path d="M11.0911 64.0969C12.9133 64.7169 14.6592 65.5421 16.2951 66.5565C17.991 67.6349 19.4629 69.0303 20.6303 70.6662C23.0815 74.085 24.9579 77.8812 26.1854 81.9048C26.8705 84.1333 27.2931 86.4341 27.4445 88.7605C27.4421 88.776 27.434 88.79 27.4218 88.7997C27.4129 88.8074 27.4022 88.8125 27.3908 88.8146C27.3793 88.8167 27.3675 88.8158 27.3565 88.8118C27.3456 88.8078 27.3359 88.8009 27.3285 88.7919C27.321 88.7829 27.3162 88.772 27.3144 88.7605C26.7987 84.7274 25.6551 80.7998 23.9252 77.1202C22.2195 73.5247 20.0786 69.6838 16.807 67.2969C15.0229 65.9957 13.0172 65.1435 11.0178 64.2446C10.9127 64.2094 10.9803 64.0554 11.0911 64.0969Z" fill="#EBEBEB"/><path d="M24.8795 79.3381C23.6133 78.1842 22.2243 77.1729 20.7375 76.3222C18.4176 75.0358 16.5432 74.1886 15.9687 73.0631C15.3943 71.9375 17.1795 70.4864 17.1795 70.4864C17.1795 70.4864 14.8193 71.6483 13.8346 70.7864C12.85 69.9256 10.8022 63.9688 10.8022 63.9688C10.8022 63.9688 16.2557 65.9108 19.6574 69.8483C23.0596 73.7869 24.8795 79.3381 24.8795 79.3381Z" fill="#EBEBEB"/><path d="M24.5945 78.11C21.8633 72.9901 18.2275 68.3441 13.2451 65.2964C14.5477 66.1341 15.7696 67.0911 16.8951 68.1549C15.8801 68.0994 14.8684 68.3126 13.9622 68.7731C13.9601 68.774 13.9582 68.7753 13.9566 68.777C13.9551 68.7787 13.9539 68.7806 13.9531 68.7827C13.9521 68.7849 13.9516 68.7872 13.9516 68.7896C13.9516 68.7919 13.9521 68.7942 13.9531 68.7964L13.9622 68.8055C14.9264 68.4424 15.9508 68.2691 16.9803 68.2941C16.9917 68.2979 17.0031 68.2979 17.0144 68.2941C17.1928 68.4606 17.3713 68.6339 17.5389 68.8055C17.0127 68.8299 16.49 68.9004 15.9764 69.0163V69.0396C16.5212 68.936 17.0753 68.8898 17.6298 68.9015C18.5417 69.8258 19.4011 70.8006 20.2036 71.8214C19.8367 71.7695 19.4629 71.7962 19.107 71.8998C19.1017 71.9003 19.0966 71.9027 19.0928 71.9066C19.0894 71.9105 19.0874 71.9156 19.0872 71.9208C19.0872 71.9265 19.0891 71.9312 19.0928 71.935C19.0966 71.9388 19.1014 71.9407 19.107 71.9407C19.4945 71.8975 19.8852 71.8927 20.2735 71.9265C20.4213 72.1163 20.5644 72.3072 20.7195 72.5112C19.604 72.4402 18.4843 72.5486 17.4031 72.8322C17.3855 72.8322 17.4031 72.8612 17.4031 72.8577C18.5273 72.624 19.6768 72.5356 20.8235 72.5947C20.9622 72.7737 21.0945 72.9702 21.2315 73.1555C20.5701 73.1408 19.9098 73.2178 19.2695 73.3845L19.2627 73.3884L19.2582 73.3941L19.2565 73.4021C19.2565 73.4047 19.2572 73.4072 19.2588 73.4095L19.2627 73.4174L19.2695 73.422C19.9394 73.2772 20.6258 73.2243 21.3099 73.2646C21.894 74.074 22.4567 74.8988 22.9974 75.7379C22.7062 75.6837 22.4063 75.7014 22.1235 75.7896M22.1235 75.8129C22.4358 75.7914 22.7495 75.8068 23.0582 75.8589C23.5445 76.6157 24.0131 77.3799 24.4639 78.1515C24.5338 78.2282 24.6428 78.1782 24.6014 78.11L22.1235 75.8129Z" fill="white"/><path d="M20.3521 73.8566C19.8156 73.8199 19.2771 73.8864 18.7657 74.0526V74.0759C19.2872 73.9862 19.8142 73.9314 20.343 73.9117C20.3822 73.9117 20.3913 73.86 20.3521 73.8566ZM15.5941 67.6071C15.3494 67.5878 15.1033 67.6015 14.8623 67.648C14.8373 67.648 14.8447 67.6946 14.8623 67.6929C15.1049 67.6662 15.3407 67.6753 15.5759 67.6702C15.585 67.6698 15.5928 67.6666 15.5992 67.6605C15.6046 67.6555 15.6081 67.6488 15.6094 67.6415C15.6106 67.6342 15.6094 67.6268 15.606 67.6202C15.6036 67.6147 15.5994 67.6101 15.5941 67.6071Z" fill="white"/><path d="M31.9134 96.2969H19.6021L21.3577 83.4185L21.8066 80.1162H29.71L30.1577 83.4185L31.9134 96.2969Z" fill="#DBDBDB"/><path d="M30.1579 83.4185H21.3579L21.8073 80.1162H29.7108L30.1579 83.4185Z" fill="#C7C7C7"/><path d="M30.8382 78.9712H20.6763V81.7672H30.8376L30.8382 78.9712Z" fill="#DBDBDB"/><path d="M11.7769 96.2975L33.9331 96.2276L56.0922 96.2009L100.409 96.1509L144.724 96.2009L166.883 96.2276L189.041 96.2975L166.883 96.3708L144.724 96.3935L100.409 96.4441L56.0922 96.3935L33.9331 96.3668L11.7769 96.2975Z" fill="#263238"/><path d="M100.992 118.058C147.157 118.058 184.581 113.951 184.581 108.885C184.581 103.819 147.157 99.7119 100.992 99.7119C54.8273 99.7119 17.4028 103.818 17.4028 108.885C17.4028 113.951 54.8267 118.058 100.992 118.058Z" fill="#EBEBEB"/><path d="M81.5324 107.366C80.8528 107.596 61.3739 107.657 60.6318 107.196C60.3392 107.017 60.1733 104.18 60.0483 100.74C60.0222 99.9785 60.0008 99.2167 59.9841 98.4549L58.4341 88.0492L71.4568 87.9634L72.6841 98.3725L72.6346 100.662C72.6346 100.662 80.104 103.976 80.867 104.576C81.6307 105.176 82.2119 107.135 81.5324 107.366Z" fill="#EB9481"/><path d="M81.5331 107.365C80.8536 107.595 61.3746 107.656 60.6325 107.195C60.2882 106.979 60.0973 102.83 59.9956 98.4545L72.6615 98.3721L72.6115 100.661C72.6115 100.661 80.0808 103.975 80.8445 104.575C81.6081 105.175 82.2132 107.134 81.5331 107.365Z" fill="white"/><path d="M81.5329 107.366C80.8528 107.596 61.3738 107.657 60.6318 107.196C60.3392 107.017 60.1733 104.18 60.0488 100.741L72.6045 100.662C72.6045 100.662 80.0716 103.976 80.8352 104.576C81.5988 105.177 82.2125 107.135 81.5329 107.366Z" fill="#A6A6A6"/><path d="M81.2502 106.579C77.9837 106.508 64.807 106.539 61.5746 106.722C61.5479 106.722 61.5479 106.738 61.5746 106.742C64.811 106.879 77.9871 106.742 81.2502 106.63C81.32 106.634 81.32 106.579 81.2502 106.579ZM79.5911 103.88C79.2276 103.871 78.8663 103.938 78.5303 104.077C78.1942 104.216 77.8908 104.423 77.6394 104.686C77.1811 105.154 76.9221 105.781 76.9166 106.437C76.9166 106.458 76.9524 106.458 76.9541 106.437C77.0632 105.792 77.3775 105.199 77.8503 104.747C78.3231 104.294 78.9292 104.007 79.5786 103.926C79.5846 103.928 79.5907 103.926 79.5967 103.923C79.6023 103.92 79.6065 103.914 79.6087 103.908C79.6093 103.905 79.6094 103.902 79.6088 103.899C79.6082 103.896 79.607 103.893 79.6053 103.891C79.6038 103.888 79.6017 103.886 79.5993 103.884C79.5968 103.882 79.594 103.881 79.5911 103.88ZM73.8808 100.983C72.5729 100.969 71.0604 101.435 70.2502 102.509C70.2217 102.55 70.2791 102.595 70.3183 102.574C71.4591 101.97 72.6612 101.491 73.9041 101.143C73.9179 101.138 73.93 101.129 73.9393 101.118C73.9486 101.107 73.9547 101.093 73.957 101.078C73.9593 101.064 73.9577 101.049 73.9525 101.035C73.9472 101.022 73.9383 101.009 73.9269 101C73.9137 100.99 73.8976 100.984 73.8808 100.983ZM74.8939 101.371C73.5848 101.371 72.0718 101.827 71.2655 102.899C71.2331 102.942 71.2905 102.986 71.3314 102.965C72.4709 102.361 73.6727 101.881 74.9155 101.536C74.9299 101.531 74.9429 101.523 74.953 101.512C74.963 101.5 74.9699 101.486 74.9729 101.472C74.9758 101.457 74.9747 101.441 74.9698 101.427C74.9648 101.413 74.956 101.4 74.9445 101.39C74.9303 101.378 74.9124 101.371 74.8939 101.371ZM75.9041 101.755C74.5979 101.755 73.0837 102.207 72.2735 103.283C72.2445 103.324 72.3019 103.367 72.3411 103.347C73.4824 102.745 74.6847 102.266 75.9274 101.918C75.9477 101.911 75.9647 101.897 75.9751 101.879C75.9855 101.86 75.9886 101.838 75.9837 101.817C75.9794 101.799 75.9692 101.784 75.9547 101.772C75.9403 101.761 75.9225 101.755 75.9041 101.755ZM76.9149 102.153C75.6058 102.138 74.0928 102.605 73.2842 103.681C73.2524 103.72 73.3149 103.764 73.3524 103.745C74.4929 103.141 75.6951 102.662 76.9382 102.316C76.9585 102.309 76.9755 102.295 76.986 102.277C76.9966 102.258 76.9998 102.236 76.995 102.216C76.9912 102.197 76.9811 102.181 76.9664 102.17C76.9518 102.158 76.9336 102.152 76.9149 102.153ZM62.3968 103.62C60.9394 103.62 60.9394 105.912 62.3968 105.903C63.8547 105.894 63.8649 103.611 62.3968 103.62Z" fill="#263238"/><path d="M75.283 97.4843C74.6211 96.9355 73.7864 97.4843 73.3813 98.0468C72.7085 99.0434 72.3065 100.198 72.2148 101.397C72.2148 101.403 72.2158 101.408 72.2177 101.413C72.2201 101.417 72.2232 101.422 72.2268 101.425C72.2307 101.429 72.2351 101.432 72.2398 101.434C72.245 101.435 72.2503 101.436 72.2558 101.437C72.2647 101.476 72.2881 101.51 72.3212 101.532C72.3542 101.555 72.3946 101.564 72.4342 101.558C72.4516 101.554 72.4683 101.547 72.4836 101.538C73.4188 100.895 74.4893 100.325 75.2029 99.4031C75.5932 98.8707 75.9165 98.0025 75.283 97.4843ZM73.6898 100.27C73.2421 100.6 72.7785 100.906 72.3415 101.254C72.5522 100.594 72.7962 99.944 73.0728 99.3082C73.2099 99.0052 73.3675 98.7135 73.5455 98.4332C73.8359 97.9985 75.1063 96.9002 75.2416 98.3218C75.3166 99.1008 74.2234 99.8786 73.6898 100.27Z" fill="#263238"/><path d="M68.9583 101.896C70.0946 102.075 71.2776 101.739 72.3856 101.561C72.4264 101.549 72.4616 101.524 72.4844 101.488C72.495 101.47 72.5022 101.45 72.5054 101.429C72.5097 101.391 72.5016 101.354 72.4822 101.321L72.4901 101.311C72.4916 101.306 72.4925 101.302 72.4929 101.298C72.4931 101.293 72.4922 101.288 72.4903 101.284C72.4884 101.28 72.4856 101.276 72.4822 101.273C71.5861 100.469 70.4936 99.9154 69.3151 99.6686C68.6265 99.5385 67.6532 99.7277 67.5651 100.583C67.4668 101.398 68.2969 101.791 68.9583 101.896ZM68.1651 101.155C67.2907 100.035 68.9606 99.9385 69.4634 100.054C69.7888 100.137 70.1068 100.24 70.4174 100.364C71.0564 100.629 71.6797 100.927 72.2873 101.257C71.7327 101.312 71.1884 101.411 70.6373 101.487C69.9793 101.575 68.6554 101.773 68.1651 101.157V101.155Z" fill="#263238"/><path d="M39.2837 39.9521C39.2837 39.9521 54.4041 65.7146 54.8041 67.5266C55.4303 70.3084 58.7218 97.7521 58.7218 97.7521L74.4632 97.4664C74.4632 97.4664 71.7536 73.2652 69.1843 65.2914C67.336 59.5493 53.9905 40.501 53.9905 40.501L39.2837 39.9521Z" fill="#1A2E35"/><path d="M73.0314 95.0548C70.7729 95.0383 62.7649 94.9469 59.7552 95.2326C59.7319 95.2326 59.7354 95.2741 59.7552 95.2758C60.7899 95.4258 70.7695 95.1951 73.028 95.1224C73.0319 95.1242 73.0362 95.1247 73.0405 95.1241C73.0447 95.1241 73.0488 95.1229 73.0524 95.1207C73.0564 95.1195 73.0599 95.1169 73.0621 95.1133C73.0647 95.11 73.067 95.1064 73.0689 95.1025C73.0701 95.0986 73.0708 95.0946 73.0712 95.0906C73.0709 95.0864 73.0701 95.0822 73.0689 95.0781C73.0673 95.0743 73.0648 95.0709 73.0615 95.0684C73.0588 95.0653 73.0552 95.0629 73.0513 95.0616L73.0314 95.0548ZM41.8626 42.319C42.8547 43.8758 43.9035 45.3997 44.9138 46.9429C45.9229 48.4872 46.9365 50.0326 47.9354 51.5741C49.9297 54.6878 51.8729 57.8222 53.7649 60.9775C54.6893 62.4406 55.48 63.9839 56.1274 65.5889C56.7184 67.1939 57.1621 68.8494 57.453 70.5349C57.7632 72.2253 57.9666 73.9298 58.1558 75.6417C58.3498 77.4565 58.553 79.2736 58.7655 81.0929C59.1795 84.8111 59.5958 88.5304 60.0143 92.2508C60.0626 92.7059 60.1126 93.1616 60.1592 93.6207C60.1593 93.6328 60.1558 93.6446 60.1491 93.6547C60.1424 93.6648 60.1329 93.6726 60.1217 93.6772C60.1106 93.6819 60.0983 93.6831 60.0865 93.6807C60.0746 93.6783 60.0637 93.6725 60.0552 93.6639C60.0494 93.6584 60.0448 93.6516 60.0418 93.6442C60.0388 93.6367 60.0373 93.6287 60.0376 93.6207C59.574 89.9258 59.1888 86.2184 58.7694 82.5253C58.559 80.6833 58.3497 78.8413 58.1416 76.9991C57.9467 75.2838 57.7598 73.5684 57.4956 71.8656C57.2525 70.1856 56.8821 68.5264 56.3876 66.9025C55.8439 65.2187 55.1146 63.6007 54.2132 62.0781C52.3893 58.9264 50.4666 55.8247 48.5314 52.7361C46.5956 49.6463 44.6189 46.5128 42.5837 43.4446C42.3346 43.0655 42.0789 42.6908 41.8166 42.3207C41.7933 42.3156 41.8433 42.2906 41.8626 42.319Z" fill="#263238"/><path d="M44.2385 108.883C43.5254 108.839 24.9993 101.324 24.4851 100.611C24.4632 100.569 24.4534 100.521 24.4567 100.474C24.4567 99.8357 25.743 97.6039 26.8146 94.6721C26.8828 94.4937 27.3612 93.6164 28.0231 92.4459C29.8623 89.23 33.068 83.709 33.068 83.709L45.176 88.5329L39.3794 97.2181L38.4788 99.3209C38.4788 99.3209 44.1606 105.203 44.6385 106.036C45.1169 106.868 44.9521 108.923 44.2385 108.883Z" fill="#EB9481"/><path d="M44.2385 108.884C43.5255 108.84 24.9993 101.325 24.4851 100.612C24.4631 100.569 24.4533 100.522 24.4567 100.475C24.4567 99.8365 25.7431 97.6047 26.8147 94.6723C26.8829 94.4939 27.3613 93.6167 28.0232 92.4468L39.3925 97.2138L38.5005 99.3161C38.5005 99.3161 44.18 105.198 44.6584 106.031C45.1368 106.863 44.9522 108.923 44.2385 108.884Z" fill="white"/><path d="M44.2388 108.884C43.5252 108.839 24.9991 101.325 24.4854 100.612C24.2871 100.34 25.115 97.8081 26.8184 94.6729L38.4786 99.3183C38.4786 99.3183 44.1621 105.197 44.6422 106.043C45.1224 106.89 44.9525 108.923 44.2388 108.884Z" fill="#A6A6A6"/><path d="M44.2683 108.054C41.2689 106.764 29.0439 101.841 25.9785 100.796C25.9518 100.796 25.9467 100.796 25.9785 100.814C28.9263 102.158 41.1905 106.98 44.2569 108.113C44.316 108.127 44.341 108.081 44.2683 108.054ZM43.7427 104.922C43.4103 104.775 43.051 104.7 42.6876 104.7C42.3243 104.7 41.9649 104.775 41.6325 104.922C41.0341 105.185 40.56 105.668 40.3086 106.271C40.3086 106.288 40.3353 106.303 40.3444 106.285C40.6881 105.725 41.2044 105.291 41.8157 105.049C42.4271 104.808 43.1003 104.771 43.7342 104.945C43.7362 104.944 43.738 104.943 43.7395 104.941C43.741 104.939 43.7421 104.937 43.7427 104.935C43.7436 104.933 43.744 104.931 43.744 104.929C43.744 104.926 43.7436 104.924 43.7427 104.922ZM39.5359 100.091C38.3314 99.5879 36.753 99.4521 35.6007 100.145C35.5575 100.17 35.6007 100.232 35.6376 100.229C36.9217 100.099 38.2159 100.106 39.4984 100.25C39.5199 100.252 39.5412 100.246 39.5581 100.232C39.5746 100.218 39.5851 100.199 39.5876 100.177C39.59 100.159 39.586 100.14 39.5763 100.125C39.5667 100.11 39.5526 100.098 39.5359 100.091ZM40.3263 100.834C39.1217 100.33 37.5433 100.194 36.3905 100.888C36.3478 100.912 36.3905 100.975 36.428 100.972C37.7117 100.839 39.006 100.846 40.2882 100.993C40.3097 100.995 40.3311 100.988 40.3478 100.975C40.3645 100.961 40.3751 100.941 40.3774 100.92C40.3796 100.902 40.3757 100.883 40.3663 100.868C40.357 100.852 40.3432 100.84 40.3263 100.834ZM41.1297 101.575C39.9217 101.072 38.341 100.938 37.1944 101.629C37.1461 101.656 37.1944 101.717 37.2285 101.713C38.5121 101.581 39.8064 101.589 41.0888 101.735C41.0992 101.737 41.11 101.736 41.1204 101.734C41.1308 101.732 41.1406 101.727 41.149 101.72C41.1577 101.714 41.1651 101.706 41.1706 101.697C41.1762 101.688 41.1799 101.678 41.1814 101.667C41.1844 101.648 41.181 101.629 41.1717 101.612C41.1618 101.596 41.1471 101.583 41.1297 101.575ZM41.9092 102.315C40.703 101.812 39.1257 101.677 37.9734 102.366C37.9268 102.396 37.9734 102.456 38.0092 102.453C39.293 102.321 40.5872 102.328 41.8694 102.474C41.8899 102.478 41.9108 102.473 41.928 102.462C41.9452 102.45 41.9574 102.433 41.9621 102.412C41.9672 102.393 41.9645 102.372 41.9547 102.354C41.9447 102.336 41.9285 102.322 41.9092 102.315ZM27.903 98.2215C26.5399 97.6857 25.6961 99.801 27.0626 100.344C28.4291 100.887 29.2785 98.7646 27.903 98.2215Z" fill="#263238"/><path d="M42.1467 97.3773C41.7347 96.62 40.7643 96.8075 40.1717 97.1842C39.1751 97.854 38.3713 98.7731 37.8404 99.8501C37.8386 99.8543 37.8372 99.8586 37.8364 99.8631C37.8364 99.8677 37.837 99.8724 37.8381 99.8773C37.84 99.8814 37.8422 99.8852 37.8449 99.8887C37.8484 99.8918 37.8522 99.8945 37.8563 99.8967C37.8563 99.9933 37.9205 100.107 38.0347 100.075C39.1387 99.8217 40.3535 99.6893 41.3404 99.1069C41.9165 98.7785 42.5336 98.0921 42.1467 97.3773ZM39.6239 99.3592C39.0887 99.4967 38.5427 99.6075 38.0097 99.7643C38.451 99.2321 38.9184 98.7236 39.412 98.2387C39.6544 98.0069 39.912 97.7925 40.1847 97.5955C40.6069 97.3041 42.2035 96.7626 41.7898 98.1319C41.5722 98.8961 40.2682 99.1967 39.6239 99.3592Z" fill="#263238"/><path d="M34.6345 99.0913C35.6248 99.6754 36.8339 99.8061 37.9368 100.069C38.0504 100.095 38.126 99.9811 38.1152 99.8902L38.1271 99.8862C38.1306 99.8839 38.1339 99.8812 38.1368 99.8783C38.142 99.8714 38.1444 99.8629 38.1436 99.8544C38.1442 99.8497 38.1442 99.8449 38.1436 99.8402C37.6141 98.7577 36.8093 97.8335 35.8101 97.1601C35.2271 96.7794 34.251 96.5896 33.8373 97.3385C33.4231 98.0868 34.0549 98.7498 34.6345 99.0913ZM34.1726 98.1032C33.7674 96.7351 35.3623 97.2777 35.7782 97.5669C36.0506 97.7639 36.3074 97.979 36.5487 98.2123C37.0405 98.6982 37.5074 99.2087 37.9476 99.7419C37.4123 99.5845 36.8771 99.472 36.3419 99.3345C35.6924 99.1646 34.401 98.8714 34.1726 98.1032Z" fill="#263238"/><path d="M40.3206 96.7292L26.7627 91.2496C26.7627 91.2496 43.5655 66.5053 43.6866 64.6633C43.7991 63.0337 36.4172 48.8383 36.128 44.7002C35.8144 40.2337 37.9587 36.7797 40.3206 34.6343L54.2996 40.5871C54.2996 40.5871 52.8241 42.9491 50.5087 44.2655C50.5087 44.2655 57.6763 60.6729 57.2701 65.7189C56.8633 70.7644 40.3206 96.7292 40.3206 96.7292Z" fill="#1A2E35"/><path d="M40.9205 94.1472C38.9585 93.3035 39.5364 93.516 37.5671 92.687C36.604 92.2853 30.2773 89.6495 29.3057 89.4302C29.2841 89.4302 29.2699 89.4586 29.2898 89.4694C30.1335 89.9876 36.5114 92.5063 37.4813 92.8978C39.4671 93.6807 38.9085 93.4342 40.8977 94.1932C40.9002 94.196 40.9033 94.1982 40.9068 94.1995C40.9105 94.2008 40.9143 94.2018 40.9182 94.2023C40.9221 94.2018 40.9259 94.2008 40.9296 94.1995C40.9331 94.1982 40.9362 94.196 40.9387 94.1932C40.9413 94.1905 40.9433 94.1873 40.9447 94.1838C40.946 94.1803 40.9467 94.1766 40.9466 94.1728C40.9467 94.169 40.946 94.1653 40.9447 94.1618C40.9433 94.1583 40.9413 94.1551 40.9387 94.1523C40.9331 94.1497 40.9271 94.148 40.921 94.1472M52.8068 43.9643C51.3516 44.3059 49.9277 44.7691 48.55 45.3489C48.539 45.353 48.5299 45.3612 48.5246 45.3717C48.5194 45.3823 48.5183 45.3944 48.5216 45.4057C48.5257 45.4168 48.5339 45.4257 48.5445 45.4308C48.5551 45.4359 48.5673 45.4367 48.5784 45.433C50.0057 45.0165 51.4597 44.6256 52.8602 44.1319C52.879 44.1222 52.8937 44.1062 52.9018 44.0868C52.9099 44.0673 52.9108 44.0456 52.9044 44.0255C52.898 44.0054 52.8847 43.9882 52.8668 43.977C52.849 43.9658 52.8277 43.9613 52.8068 43.9643Z" fill="#263238"/><path d="M51.1422 43.3969C51.0058 43.639 50.8869 43.889 50.7853 44.1469C50.6858 44.3433 50.6252 44.5571 50.6069 44.7765C50.6069 44.8191 50.6638 44.8276 50.691 44.8015C50.8394 44.6268 50.9598 44.4301 51.0478 44.2185C51.1672 43.9884 51.2905 43.7594 51.4047 43.5236C51.4169 43.4976 51.4212 43.4686 51.4172 43.4401C51.4132 43.4117 51.4009 43.385 51.3819 43.3634C51.3631 43.3415 51.3381 43.3259 51.3102 43.3186C51.2823 43.3113 51.2528 43.3127 51.2257 43.3225C51.1892 43.3354 51.1592 43.3621 51.1422 43.3969Z" fill="#263238"/><path d="M51.3172 42.0797C51.4047 41.8638 51.4956 41.649 51.5797 41.4325C51.6632 41.2166 51.7831 40.995 51.8701 40.7677C51.8701 40.7325 51.9399 40.7677 51.9257 40.795C51.8382 41.0109 51.7831 41.2382 51.7166 41.4598C51.6502 41.6814 51.5723 41.9047 51.5007 42.1263C51.3644 42.5426 51.204 42.9492 51.0195 43.3462C51.249 43.3576 51.4781 43.3175 51.6899 43.2285C51.9067 43.0906 52.073 42.8863 52.1644 42.6462C52.2877 42.4064 52.4019 42.1598 52.5218 41.9206C52.6755 41.6607 52.8043 41.3891 52.9081 41.1058C52.9081 41.0717 52.978 41.1058 52.9638 41.1325C52.7742 41.7314 52.5248 42.3097 52.2195 42.8587C52.0899 43.0712 51.9882 43.2962 51.749 43.3785C51.4899 43.4501 51.2195 43.4694 50.9536 43.4337C50.9178 43.4337 50.8769 43.4337 50.8894 43.3802C51.0144 42.9354 51.1547 42.4996 51.3172 42.0791M50.7218 44.8001C51.2218 45.8547 51.6587 46.9052 52.1223 47.9558C52.5865 49.0064 52.9968 50.0229 53.4144 51.0683C54.2469 53.1778 54.9926 55.3205 55.6496 57.491C55.9751 58.5666 56.2725 59.6505 56.5416 60.7416C56.8361 61.8293 57.0513 62.937 57.1854 64.0558C57.3026 65.174 57.2033 66.3042 56.8928 67.3848C56.5734 68.4526 56.1622 69.4907 55.6638 70.4876C55.357 71.1189 55.2547 71.0348 55.4013 70.716C55.8189 69.8104 56.9104 67.07 56.8286 64.7649C56.7658 63.6624 56.5961 62.5686 56.3218 61.499C56.0667 60.3963 55.78 59.3011 55.4621 58.2149C54.8411 56.0712 54.103 53.9717 53.3462 51.8757C52.9201 50.7069 52.4706 49.5405 52.0337 48.3717C51.5968 47.203 51.1587 46.1297 50.6683 44.8399C50.6581 44.7876 50.7081 44.7683 50.7223 44.8001M40.4348 41.316C40.6757 41.2268 41.0166 41.3746 41.2627 41.416C41.8945 41.5229 42.5377 41.5444 43.1752 41.4802C43.4937 41.4713 43.8058 41.388 44.0865 41.2371C44.5479 40.9007 44.9435 40.4824 45.2536 40.003C45.6227 39.5128 45.9273 38.9771 46.1598 38.4092C46.1598 38.3734 46.2206 38.4092 46.2098 38.4319C46.1076 38.7558 46.0314 39.0825 45.9172 39.4007C45.7922 39.6996 45.6428 39.9882 45.4695 40.2621C45.1882 40.8134 44.7728 41.2853 44.2615 41.6342C43.6996 41.9558 42.9752 41.9268 42.349 41.9001C41.9854 41.8797 41.625 41.8321 41.2678 41.7575C41.011 41.7018 40.5797 41.6609 40.4172 41.4354C40.403 41.4205 40.395 41.4008 40.395 41.3802C40.395 41.3597 40.403 41.3399 40.4172 41.3251L40.4348 41.316Z" fill="#263238"/><path d="M30.6354 88.6535C32.865 85.2735 35.0545 81.8672 37.2036 78.4354C39.3543 75.0028 41.4708 71.5428 43.553 68.0553C44.053 67.224 44.6235 66.3956 44.9462 65.4717C45.303 64.4712 45.0979 63.5058 44.7842 62.5178C44.1857 60.7298 43.4877 58.9766 42.6933 57.2666C41.9121 55.5411 41.07 53.845 40.2422 52.1422C39.4144 50.4393 38.5473 48.7587 37.9087 46.9842C37.3274 45.2871 37.0899 43.5308 37.5519 41.7723C38.0138 40.0138 38.9791 38.4615 40.0104 36.9984C40.2774 36.6143 40.5541 36.232 40.8252 35.832C40.8467 35.8013 40.8899 35.832 40.87 35.8661C39.7411 37.3899 38.6416 39.0047 37.9797 40.795C37.6421 41.6774 37.4535 42.6099 37.4217 43.5541C37.4164 44.5124 37.5644 45.4654 37.8604 46.3768C38.4098 48.153 39.2752 49.8308 40.0831 51.5013C40.8916 53.1717 41.7536 54.8962 42.549 56.6178C43.3698 58.3492 44.0998 60.1222 44.7359 61.9297C45.007 62.732 45.3172 63.574 45.2945 64.4314C45.2746 65.3751 44.8252 66.2178 44.3592 67.0189C42.3188 70.4723 40.2321 73.8982 38.0996 77.2956C35.9586 80.7056 33.7808 84.0923 31.5666 87.4553L30.728 88.7251C30.6769 88.7774 30.603 88.7132 30.6354 88.6535Z" fill="#263238"/><path d="M55.0796 24.8495C56.3426 27.6222 62.7182 31.4438 65.7029 31.8569C67.8438 32.1478 74.3727 32.2535 76.6085 31.912C79.233 31.5086 77.2256 24.429 75.679 24.6137C71.4222 25.1103 67.8085 25.6643 66.6699 25.6643C65.2841 25.6643 57.7773 23.2523 56.8727 22.9109C54.6568 22.0853 54.3659 23.2751 55.0796 24.8495Z" fill="#FFC3BD"/><path d="M55.121 25.5883C56.7266 27.9559 62.0261 31.8275 62.0261 31.8275L62.7653 30.1911L65.2124 24.7843C65.2124 24.7843 60.3374 23.2513 57.1261 22.5513C56.749 22.4645 56.3652 22.41 55.9789 22.3883C53.9204 22.2758 53.742 23.5445 55.121 25.5883Z" fill="#007CFF"/><path d="M55.1204 25.5906C56.7261 27.9576 62.0261 31.8292 62.0261 31.8292L62.7648 30.1929C60.7846 27.89 57.7267 24.4008 55.9693 22.3957C53.9199 22.2781 53.742 23.5463 55.1204 25.5906Z" fill="#263238"/><path d="M61.0704 30.7294C61.475 29.734 63.2772 25.7266 63.7267 24.8902C63.7551 24.8311 63.8017 24.8385 63.7761 24.8902C62.9948 26.8839 62.1046 28.8333 61.1096 30.7294C61.1096 30.7652 61.054 30.7522 61.0704 30.7294Z" fill="#263238"/><path d="M74.897 24.6866C77.4856 24.3469 80.6896 23.7662 83.4027 24.919C83.7072 25.0389 83.9635 25.2577 84.1294 25.5406C85.3527 26.0372 85.4862 26.7645 85.4862 26.7645C86.4873 27.5758 86.5231 28.3406 86.5231 28.3406C86.5231 28.3406 87.9504 28.9247 87.7379 29.8645C87.4987 30.9043 85.5345 29.8645 84.1061 29.9497C80.8265 30.1543 76.235 31.3599 75.2879 31.5122L74.897 24.6866Z" fill="#FFC3BD"/><path d="M80.5218 27.7462C81.4803 27.6804 82.4422 27.6804 83.4007 27.7462C84.4456 27.8786 85.4786 28.0882 86.4922 28.374C86.4954 28.3752 86.4987 28.3757 86.5021 28.3754C86.5054 28.3751 86.5086 28.374 86.5115 28.3723C86.5176 28.3694 86.5223 28.3642 86.5245 28.3578C86.5268 28.3514 86.5264 28.3444 86.5235 28.3382C86.5202 28.3325 86.5149 28.3281 86.5087 28.3257C84.6067 27.5775 82.5354 27.3672 80.5218 27.7178C80.4752 27.711 80.4752 27.7502 80.5218 27.7462Z" fill="#FFC3BD"/><path d="M80.1134 25.7852C81.0549 25.7852 82.7106 25.8318 85.481 26.7983L80.1134 25.7852ZM85.481 26.7806C83.8217 25.9403 81.9609 25.5795 80.1077 25.7386C80.056 25.7386 80.0492 25.7818 80.1077 25.7852L85.481 26.7806ZM79.9112 24.7125C81.5163 24.7943 82.5117 25.0517 84.1208 25.5358L79.9112 24.7125ZM84.1208 25.5181C82.3498 24.7767 81.6237 24.7068 79.9038 24.6426C79.8487 24.6653 79.8509 24.7119 79.9038 24.7119L84.1208 25.5181Z" fill="#263238"/><path d="M80.171 30.4482C79.7539 30.6426 79.8551 30.8624 80.6528 31.0465C81.4505 31.2306 84.7562 31.2857 84.6403 32.442C84.5238 33.5977 79.8551 33.4511 78.5533 33.042C77.2511 32.6329 75.2886 31.513 75.2886 31.513C76.213 31.3596 78.3818 30.7715 80.171 30.4482Z" fill="#FFC3BD"/><path d="M58.5472 29.2702C58.5268 29.777 58.4809 30.2815 58.4097 30.7838C58.4078 30.7734 58.4088 30.7627 58.4126 30.7529C58.4164 30.7431 58.4229 30.7345 58.4313 30.7281C58.5273 30.4639 58.6239 30.1923 58.7399 29.9423H58.7666C58.7097 30.2207 58.6258 30.491 58.5148 30.7531C58.4631 30.8781 58.4131 31.0014 58.358 31.1247C58.322 31.3481 58.282 31.5709 58.2381 31.7929C58.1512 32.2111 57.7569 33.9855 57.6552 34.2855C57.637 34.348 57.562 34.3338 57.5677 34.2628C57.5893 33.9355 57.9245 32.0878 57.9853 31.6679C58.1148 30.8565 58.2887 30.052 58.5058 29.2594C58.5205 29.2435 58.5506 29.2554 58.5472 29.2702Z" fill="#263238"/><path d="M55.5156 42.5456C54.7361 42.9456 53.7315 43.5671 49.2145 42.6774C48.9279 42.6187 48.6439 42.5479 48.3633 42.4649C44.1281 41.191 39.0349 36.2007 38.7588 35.8541C38.7588 35.8541 45.4895 24.9251 54.1008 21.4893C55.1713 21.0654 56.9247 21.8836 57.89 22.9024C58.2361 23.2598 58.9923 25.9507 58.7031 28.1228C58.4139 30.295 56.2935 42.1456 55.5156 42.5456Z" fill="#007CFF"/><path d="M54.9094 28.0176C54.9412 28.4795 54.8769 28.943 54.7207 29.3789C54.6829 28.9168 54.7473 28.452 54.9094 28.0176ZM51.9719 35.4681C51.9912 35.6982 51.9832 35.9301 51.9491 36.1579C51.9207 36.388 51.865 36.6136 51.7832 36.8301C51.7628 36.5995 51.7706 36.3673 51.8065 36.1386C51.8368 35.909 51.8916 35.6855 51.9719 35.4681ZM46.9844 29.8329C47.0182 30.2963 46.9538 30.7615 46.7952 31.1982C46.7595 30.7348 46.824 30.2692 46.9844 29.8329ZM56.8281 35.0812C56.8463 35.3122 56.8387 35.5427 56.8054 35.7727C56.777 36.0028 56.7207 36.2283 56.6372 36.4443C56.6058 35.9814 56.6708 35.5176 56.8281 35.0812ZM50.4264 28.8698C50.6554 28.8501 50.886 28.8571 51.1133 28.8908C51.3436 28.92 51.5671 28.9761 51.7838 29.059C51.323 29.0935 50.8602 29.029 50.4264 28.8698ZM44.7633 34.246C45.2248 34.2131 45.6881 34.2777 46.123 34.4357C45.6614 34.4722 45.1973 34.4074 44.7633 34.246ZM51.4338 40.063C51.8949 40.0309 52.3575 40.0961 52.7917 40.2545C52.3306 40.2901 51.8671 40.2248 51.4338 40.063ZM43.0167 37.9499C43.4787 37.9198 43.9418 37.9874 44.3758 38.1482C43.9137 38.1836 43.4494 38.1162 43.0167 37.9499ZM54.8667 22.7823C55.328 22.7493 55.7911 22.8138 56.2258 22.9715C55.7645 23.0045 55.3015 22.9401 54.8667 22.7823ZM53.2167 24.6477C53.0695 24.8252 52.9012 24.9841 52.7156 25.121C52.535 25.2615 52.3393 25.3813 52.1321 25.4783C52.2791 25.3015 52.4444 25.1443 52.6281 25.0068C52.8099 24.8647 53.0077 24.7448 53.2167 24.6477ZM44.2423 29.942C44.0927 30.1174 43.9234 30.2751 43.7378 30.4119C43.5594 30.5555 43.3633 30.6756 43.1542 30.7693C43.3042 30.5943 43.4741 30.4363 43.6594 30.2994C43.8383 30.1573 44.0344 30.0374 44.2423 29.942ZM55.452 32.3301C55.302 32.5069 55.1336 32.6641 54.9469 32.8016C54.7684 32.9448 54.5719 33.0647 54.3639 33.1596C54.5133 32.9828 54.6826 32.8238 54.8684 32.6857C55.0479 32.5441 55.2439 32.4247 55.452 32.3301ZM48.1923 36.9079C48.0419 37.0848 47.8736 37.2426 47.6872 37.3812C47.5073 37.524 47.3128 37.6431 47.1037 37.7386C47.2541 37.5617 47.4224 37.4037 47.6088 37.2647C47.7892 37.1244 47.9853 37.0047 48.1923 36.9079ZM58.2321 26.0744C58.0817 26.2513 57.9133 26.4092 57.727 26.5482C57.5464 26.6885 57.3506 26.8082 57.1434 26.9051C57.2938 26.7289 57.4622 26.5717 57.6486 26.4335C57.8289 26.291 58.0234 26.1713 58.2321 26.0744ZM58.0775 31.3823C57.901 31.2325 57.7424 31.0628 57.6048 30.8766C57.4628 30.697 57.343 30.5009 57.248 30.2926C57.4253 30.441 57.5823 30.6085 57.719 30.7948C57.8592 30.9778 57.9787 31.1736 58.0775 31.3823ZM41.9008 35.0357C41.7241 34.887 41.5655 34.718 41.4281 34.5323C41.2874 34.3518 41.1676 34.1559 41.0713 33.9482C41.4185 34.2511 41.7007 34.6207 41.9008 35.0357ZM56.1139 39.3846C55.9367 39.237 55.7782 39.0683 55.6417 38.8823C55.5018 38.7013 55.3821 38.5054 55.2849 38.2982C55.4614 38.4479 55.6184 38.6158 55.7559 38.8022C55.8972 38.9825 56.0166 39.1766 56.1139 39.3846ZM48.9912 26.9903C48.8145 26.8419 48.6559 26.6733 48.5184 26.488C48.3788 26.3065 48.2592 26.1105 48.1616 25.9033C48.3385 26.0518 48.4955 26.2193 48.6327 26.4056C48.7728 26.5878 48.8921 26.7825 48.9906 26.9897M49.1162 41.8596C48.9395 41.7109 48.7808 41.5419 48.6434 41.3562C48.5034 41.175 48.3838 40.9789 48.2866 40.7715C48.4639 40.9193 48.623 41.088 48.7594 41.2738C48.9003 41.456 49.0192 41.6513 49.1162 41.8596ZM50.3258 33.0886C50.151 32.9387 49.9935 32.7697 49.8565 32.5846C49.713 32.406 49.5931 32.2097 49.4997 32.0005C49.6758 32.1509 49.8323 32.3194 49.969 32.5062C50.1111 32.6852 50.2304 32.8812 50.3258 33.0886Z" fill="white"/><path d="M56.3103 25.8074C55.2114 25.6034 54.1785 23.329 54.0483 21.7977C54.0483 21.6943 54.5603 20.8614 55.1472 19.8205C55.504 19.1858 55.8785 18.471 56.1785 17.7921C56.241 17.6563 56.5989 18.1188 56.5989 18.1188L57.3807 18.7034L59.9603 20.6864C59.3456 21.4246 58.8238 22.2354 58.4063 23.1006C58.3655 23.1948 58.3339 23.2928 58.312 23.3932V23.4415C58.1535 24.021 57.3631 25.9983 56.3103 25.8074Z" fill="#FFC3BD"/><path d="M58.3117 23.4088V23.4588C58.1587 23.4107 58.0096 23.351 57.8657 23.2804C55.0435 21.96 56.2924 18.1702 56.2924 18.1702L57.4003 18.7191L59.9799 20.702C59.3657 21.4404 58.8439 22.251 58.4259 23.1157C58.3789 23.2096 58.3407 23.3078 58.3117 23.4088ZM62.9265 12.6196C62.9265 12.6196 64.2162 13.4395 64.2236 14.8992C64.2305 16.3594 63.6401 17.7867 63.51 17.8401C63.3799 17.8935 62.9265 12.6196 62.9265 12.6196Z" fill="#263238"/><path d="M56.5172 13.3799C55.7417 14.9219 55.7826 19.7458 56.7513 20.9964C58.1553 22.8009 60.8541 23.4225 62.4956 21.6401C64.0797 19.9106 63.9229 13.6157 62.7882 12.4418C61.1468 10.6998 57.6661 11.1106 56.5172 13.3799Z" fill="#FFC3BD"/><path d="M60.6336 17.2446C60.6336 17.2446 60.6069 17.2628 60.6103 17.273C60.6285 17.6037 60.5927 17.9878 60.2983 18.1003V18.1168C60.6642 18.0628 60.7035 17.5571 60.6336 17.2446Z" fill="#263238"/><path d="M60.3349 16.9107C59.7997 16.8823 59.7872 17.9437 60.2792 17.9704C60.7718 17.9971 60.7769 16.934 60.3349 16.9107ZM62.4326 17.2442C62.4364 17.2442 62.44 17.245 62.4434 17.2465C62.4467 17.2485 62.4497 17.2508 62.4525 17.2533C62.4552 17.2562 62.4572 17.2597 62.4582 17.2636C62.4593 17.2673 62.4597 17.2711 62.4593 17.2749C62.4417 17.6033 62.4803 17.9897 62.7764 18.0988V18.1147C62.4093 18.0499 62.3593 17.5465 62.4326 17.2442Z" fill="#263238"/><path d="M62.7333 16.8981C63.2685 16.8663 63.2861 17.9294 62.7975 17.9578C62.3088 17.9862 62.2895 16.9248 62.7333 16.8981ZM60.0026 16.1032C60.142 16.0638 60.2787 16.0172 60.4128 15.9634C60.5658 15.9357 60.7052 15.8577 60.8088 15.7419C60.8343 15.6994 60.845 15.6498 60.8392 15.6006C60.8335 15.5515 60.8116 15.5057 60.777 15.4703C60.6986 15.4098 60.6054 15.3715 60.5071 15.3595C60.4088 15.3475 60.3091 15.3622 60.2185 15.4021C60.0201 15.4575 59.8496 15.5852 59.7407 15.76C59.7166 15.8006 59.705 15.8474 59.7073 15.8946C59.7096 15.9417 59.7257 15.9871 59.7536 16.0252C59.7815 16.0633 59.82 16.0923 59.8643 16.1087C59.9085 16.1251 59.9566 16.1281 60.0026 16.1174V16.1032ZM63.0929 16.5714C62.9511 16.5404 62.8112 16.5018 62.6736 16.4555C62.5197 16.4353 62.3771 16.3641 62.2685 16.2532C62.2405 16.2125 62.2271 16.1636 62.2304 16.1143C62.2337 16.065 62.2535 16.0182 62.2867 15.9816C62.3617 15.9175 62.4526 15.8747 62.5499 15.8577C62.6471 15.8407 62.7472 15.8501 62.8395 15.885C63.0384 15.9277 63.2157 16.0424 63.3355 16.2072C63.3526 16.2363 63.3634 16.2688 63.367 16.3023C63.3707 16.3359 63.3671 16.3699 63.3566 16.402C63.3462 16.4342 63.329 16.4637 63.3063 16.4887C63.2835 16.5137 63.2557 16.5335 63.2247 16.547C63.1835 16.5657 63.1376 16.5716 63.0929 16.564V16.5714ZM59.8878 20.3691C59.9662 20.4475 60.0412 20.5475 60.1605 20.5634C60.2838 20.5634 60.4054 20.5384 60.5174 20.4884C60.5174 20.4884 60.5372 20.4884 60.5174 20.5066C60.4706 20.572 60.4067 20.6233 60.3328 20.6547C60.2588 20.6862 60.1776 20.6967 60.098 20.685C60.0318 20.6664 59.9723 20.629 59.9268 20.5774C59.8812 20.5258 59.8515 20.4622 59.8412 20.3941L59.8389 20.3845L59.8412 20.3748L59.8469 20.3669L59.8554 20.3617C59.8609 20.3591 59.8671 20.3583 59.873 20.3595C59.8791 20.3606 59.884 20.3638 59.8878 20.3691ZM61.7941 18.9623C61.7941 18.9623 61.8083 19.4532 61.7941 19.6765C61.3458 19.6285 60.893 19.6441 60.4492 19.7231C60.3435 19.7458 60.356 19.6623 60.4759 19.6066C60.8331 19.4418 61.2318 19.3889 61.6196 19.4549C61.6424 19.3924 61.5799 18.6958 61.6196 18.6975C61.7952 18.7345 61.9657 18.7941 62.1259 18.8759C62.1259 17.8487 61.9617 16.8322 61.9776 15.8049C61.9776 15.7968 61.9808 15.789 61.9866 15.7832C61.9923 15.7775 62.0001 15.7742 62.0083 15.7742C62.0162 15.7742 62.0232 15.7773 62.0293 15.7833C62.0322 15.7861 62.0346 15.7894 62.0362 15.7931C62.0377 15.7969 62.0385 15.8009 62.0384 15.8049C62.2458 16.8924 62.3429 17.9981 62.3293 19.1049C62.3333 19.2333 61.8816 19.0089 61.7941 18.9623Z" fill="#263238"/><path d="M61.1384 19.5078C61.0219 19.8136 60.8065 20.0716 60.5264 20.2408C60.0861 20.4192 59.9435 20.0192 60.3855 19.6561C60.6181 19.5386 60.8786 19.4873 61.1384 19.5078Z" fill="#263238"/><path d="M60.6789 20.1496C60.6329 20.1878 60.5811 20.2185 60.5255 20.2405C60.1204 20.403 59.9602 20.0797 60.2863 19.7524C60.5079 19.7706 60.7607 19.853 60.6789 20.1496Z" fill="#FFC3BD"/><path d="M56.294 17.5101C56.8287 17.6726 57.4389 16.0681 57.6139 15.4663C57.7656 14.93 57.94 13.197 57.9707 13.0771C58.0008 12.9578 60.3025 14.605 61.6457 14.2067C62.9889 13.8084 63.8878 12.5987 63.9076 12.0408C63.9275 11.484 62.2576 10.0453 60.8162 9.97373C59.3747 9.90214 57.4156 11.7249 57.4156 11.7249C57.6527 11.4559 57.8669 11.1699 58.0582 10.8669C58.0349 10.8027 57.102 11.176 56.9287 11.8408C56.9287 11.8408 57.1162 11.0027 57.0463 10.9885C56.977 10.9743 56.2508 11.6891 56.4133 12.2391C56.4133 12.2391 55.5747 12.9663 55.4986 13.7635C55.4213 14.5601 55.669 17.3169 56.294 17.5101Z" fill="#263238"/><path d="M62.5909 13.9098C62.1942 14.1261 61.7664 14.2796 61.3227 14.3649C60.8231 14.4101 60.3202 14.3299 59.8596 14.1314C59.3972 13.9534 58.968 13.6991 58.5897 13.3791C58.2545 13.1036 57.9545 12.7678 57.596 12.5229C57.5051 12.4587 57.4034 12.5911 57.4585 12.6712C58.0562 13.5174 58.9108 14.1484 59.8954 14.4706C60.3527 14.6198 60.8408 14.6476 61.3121 14.5515C61.7834 14.4554 62.2217 14.2386 62.584 13.9223C62.584 13.9223 62.5982 13.9098 62.5909 13.9098Z" fill="#263238"/><path d="M56.8536 17.3666C56.8536 17.3666 56.0257 15.6854 55.278 15.9575C54.5308 16.2286 54.9877 18.5547 55.7547 18.982C55.9398 19.1014 56.1647 19.1425 56.3801 19.0962C56.5955 19.05 56.7837 18.9202 56.9036 18.7354L56.9195 18.707L56.8536 17.3666Z" fill="#FFC3BD"/><path d="M55.4268 16.6211C55.4268 16.6211 55.4092 16.6211 55.4268 16.6438C55.962 16.9586 56.1728 17.5194 56.3012 18.104C56.277 18.0424 56.2398 17.9867 56.192 17.9408C56.1443 17.8949 56.0872 17.8599 56.0246 17.8381C55.962 17.8163 55.8955 17.8084 55.8296 17.8148C55.7637 17.8212 55.6999 17.8417 55.6427 17.8751C55.6251 17.8751 55.6427 17.9075 55.6427 17.9075C55.7467 17.8918 55.8531 17.9085 55.9473 17.9555C56.0414 18.0025 56.1188 18.0774 56.1688 18.17C56.2522 18.3268 56.3188 18.4906 56.3688 18.6614C56.3847 18.7165 56.4813 18.7023 56.4722 18.6398C56.5989 17.9126 56.1904 16.812 55.4268 16.6211Z" fill="#263238"/><path d="M51.3876 24.8778C50.804 28.1846 54.3842 39.3306 56.6785 41.4119C60.091 44.5068 66.4029 46.5414 68.6916 47.2045C69.6893 47.496 72.4472 40.4687 71.437 40.0039C69.2768 39.0198 62.5677 36.9187 61.929 36.3931C61.2898 35.8681 57.4421 30.2988 55.7279 27.1022C53.4387 22.784 51.6773 23.2039 51.3876 24.8778Z" fill="#FFC3BD"/><path d="M69.7361 39.3857C69.7361 39.3857 72.7173 40.1471 74.6372 40.7346C75.7071 41.0636 77.4997 41.7909 78.1793 42.3926C79.1929 43.2948 82.8611 47.7085 81.8117 48.6232C80.8611 49.4522 78.1901 45.2568 78.1901 45.2568C78.1901 45.2568 81.2571 49.2499 79.9406 50.0363C78.6242 50.8221 76.2122 45.6801 76.2122 45.6801C76.2122 45.6801 79.2696 49.8539 77.7747 50.4471C76.644 50.8937 74.2173 46.359 74.2173 46.359C74.2173 46.359 76.8236 49.967 75.6747 50.4778C74.4105 51.0437 71.9111 46.9971 71.9111 46.9971C68.7798 47.7977 66.9565 46.9039 65.7469 46.1198C65.469 45.9284 69.7361 39.3857 69.7361 39.3857Z" fill="#FFC3BD"/><path d="M77.2186 43.8058C77.5971 44.2615 77.9823 44.7223 78.3175 45.2098C78.6546 45.6967 78.9684 46.1994 79.2579 46.716C79.652 47.3839 79.9675 48.095 80.1982 48.8354C79.6971 47.5507 78.5232 45.7854 78.1874 45.2973C77.8522 44.8098 77.5397 44.3041 77.2079 43.8166C77.1919 43.8053 77.2101 43.7939 77.2186 43.8058ZM75.4374 44.9865C76.0851 45.5007 77.4408 47.4109 77.9618 48.6922C77.9846 48.7456 77.9618 48.7422 77.9243 48.6922C77.1057 47.4504 76.2731 46.2179 75.4266 44.995C75.4141 44.9865 75.4283 44.9791 75.4374 44.9865ZM73.726 45.7348C74.5301 46.5797 75.1652 47.5706 75.5971 48.6541C75.6169 48.7058 75.5772 48.6973 75.5454 48.6541C74.9408 47.6695 74.4749 46.9013 73.6936 45.7598C73.6811 45.7348 73.7078 45.7081 73.726 45.7348Z" fill="#263238"/><path d="M54.6388 25.1178C51.9791 21.3496 50.386 22.745 50.9195 26.8382C51.4206 30.6655 52.7604 35.236 52.7604 35.236L59.4644 31.9769C59.4644 31.9769 56.3337 27.5337 54.6388 25.1178Z" fill="#007CFF"/><path d="M56.2573 26.6019C56.3465 26.8138 56.4107 27.036 56.4482 27.2627C56.4892 27.4922 56.5034 27.7233 56.4909 27.9558C56.4056 27.7416 56.3457 27.5181 56.3124 27.2899C56.2662 27.0637 56.2477 26.8326 56.2573 26.6019ZM52.2852 28.8928C52.7132 28.7189 53.1737 28.6394 53.6352 28.6598C53.4219 28.7454 53.2013 28.8051 52.9732 28.8388C52.7471 28.8843 52.5164 28.9024 52.2852 28.8928ZM52.4573 23.0303C52.2758 23.4565 52.0065 23.8396 51.667 24.1547C51.8469 23.7279 52.1162 23.3447 52.4568 23.0308M58.138 30.6547C57.9571 31.0805 57.6886 31.4634 57.3499 31.7786C57.5293 31.3519 57.798 30.9687 58.138 30.6547ZM53.4749 32.9394C53.0487 32.759 52.666 32.4893 52.3528 32.1485C52.7789 32.3297 53.1607 32.599 53.4749 32.9394Z" fill="white"/><path d="M55.5146 26.2549C55.8714 26.7475 56.2419 27.2299 56.6095 27.7145C56.5149 27.4706 56.4334 27.2217 56.3652 26.9691M56.3834 26.9691C56.4567 27.1623 56.5402 27.3515 56.6095 27.5537L56.7152 27.8464C56.7239 27.8664 56.7315 27.8867 56.7379 27.9072C57.0288 28.2844 57.3214 28.6611 57.589 29.0452C57.9192 29.5066 58.2368 29.9748 58.5578 30.4407C58.8788 30.9066 59.2021 31.4895 59.5481 31.9594C59.5799 32.0009 58.9646 32.2526 58.8987 32.2833C58.8328 32.314 58.797 32.26 58.8612 32.2401C58.9254 32.2202 59.414 31.9185 59.4197 31.9134C59.1004 31.4395 58.7288 31.0055 58.3936 30.5464C58.0584 30.0873 57.7265 29.6299 57.4038 29.1657C56.747 28.2202 56.1459 27.2395 55.493 26.2907L56.3834 26.9691ZM51.0782 29.5464C51.1858 29.7407 51.2828 29.9401 51.3692 30.1447L51.4799 30.3986C51.3265 29.6731 51.189 28.9384 51.0288 28.2134C51.3437 29.3922 51.6456 30.5745 51.9345 31.76C52.2061 32.893 52.5362 34.0384 52.693 35.1941C52.8965 35.1052 53.1071 35.0336 53.3226 34.9799C53.1066 35.1209 52.8735 35.2335 52.6288 35.3151C52.2773 34.1397 51.9854 32.9473 51.7544 31.7424C51.6816 31.4151 51.6135 31.0884 51.5442 30.7611C51.4692 30.5628 51.3782 30.3736 51.2873 30.177C51.1964 29.9805 51.1089 29.7623 51.0339 29.5549C51.0515 29.5447 51.0714 29.5322 51.0782 29.5464Z" fill="#263238"/><path d="M52.8592 34.0995C53.7922 33.5745 57.6905 31.554 58.5677 31.1802C58.6302 31.1535 58.6626 31.1802 58.6001 31.2211C56.7515 32.297 54.8459 33.2717 52.8916 34.141C52.8558 34.1461 52.8325 34.1137 52.8592 34.0995Z" fill="#263238"/><path d="M159.323 110.856H70.5049L83.3458 37.9077H172.163L159.323 110.856Z" fill="#115CF2"/><path d="M113.744 30.353H84.6776L83.3452 37.9087H114.451L113.744 30.353Z" fill="#007CFF"/><g opacity="0.1"><path d="M159.498 110.858H70.6797L83.5212 37.9106H172.339L159.498 110.858Z" fill="black"/><path d="M145.318 30.3545H116.252L114.919 37.9107H146.026L145.318 30.3545Z" fill="#007CFF"/></g><path d="M81.5393 33.4373C81.3444 32.6021 81.3444 31.7328 81.5393 30.897C81.733 31.7322 81.733 32.6015 81.5393 33.4373ZM78.9489 34.1845C78.6528 33.8759 78.3975 33.5307 78.1893 33.1572C77.9711 32.7904 77.7997 32.3978 77.6791 31.9885C77.9762 32.2958 78.2325 32.6407 78.4404 33.014C78.658 33.3816 78.8291 33.7748 78.9489 34.1845ZM77.0916 36.1271C76.2715 35.8781 75.52 35.4431 74.8955 34.8561C75.3042 34.9759 75.6962 35.1464 76.0626 35.3634C76.4359 35.5742 76.7813 35.8311 77.0916 36.1271ZM76.4393 38.7419C75.6049 38.9371 74.7367 38.9371 73.9023 38.7419C74.7367 38.5467 75.6049 38.5467 76.4393 38.7419ZM77.1961 41.3294C76.8874 41.625 76.5429 41.8808 76.1705 42.0907C75.8055 42.3093 75.4141 42.4804 75.0058 42.5998C75.3131 42.3021 75.6579 42.0456 76.0313 41.8367C76.3958 41.6176 76.7874 41.4471 77.1961 41.3294ZM79.1262 43.1953C78.8766 44.0159 78.4426 44.7686 77.8575 45.3958C77.9753 44.9862 78.1469 44.594 78.3677 44.2294C78.5751 43.8535 78.83 43.506 79.1262 43.1953ZM81.7444 43.8396C81.8461 44.2549 81.8944 44.6822 81.8893 45.11C81.8956 45.538 81.8469 45.965 81.7444 46.3805C81.6396 45.9654 81.5905 45.5381 81.5984 45.11C81.5937 44.6821 81.6427 44.2553 81.7444 43.8396ZM84.3262 43.1072C84.6234 43.4152 84.8791 43.7606 85.0881 44.1345C85.3069 44.4998 85.4773 44.893 85.595 45.3027C85.0097 44.6753 84.5757 43.9224 84.3262 43.1015V43.1072ZM86.1893 41.1504C87.0067 41.4018 87.756 41.8363 88.3802 42.4208C87.9715 42.3031 87.5799 42.1325 87.2154 41.9134C86.8418 41.7046 86.4968 41.4481 86.1893 41.1504ZM86.8342 38.5345C87.6691 38.339 88.5379 38.339 89.3728 38.5345C88.5379 38.7299 87.6691 38.7299 86.8342 38.5345ZM86.0853 35.947C86.3946 35.6518 86.7397 35.3966 87.1126 35.1873C87.4766 34.9662 87.8682 34.7943 88.2773 34.676C87.6541 35.2625 86.904 35.6974 86.0853 35.947ZM84.1501 34.0822C84.2705 33.6731 84.441 33.2811 84.658 32.914C84.867 32.5404 85.1231 32.1951 85.42 31.8867C85.1697 32.7056 84.7357 33.4565 84.1512 34.0822H84.1501Z" fill="#263238"/><path d="M159.191 110.856H70.182L57.5405 40.5576H146.554L159.191 110.856Z" fill="#007CFF"/><path d="M88.388 83.5335C90.4317 83.5335 92.088 81.8738 92.088 79.8273C92.088 77.7807 90.4317 76.1216 88.388 76.1216C86.3442 76.1216 84.688 77.7812 84.688 79.8273C84.688 81.8733 86.3448 83.5335 88.388 83.5335ZM117.633 83.5335C119.676 83.5335 121.332 81.8738 121.332 79.8273C121.332 77.7807 119.676 76.1216 117.633 76.1216C116.651 76.1226 115.709 76.5136 115.016 77.2084C114.322 77.9033 113.932 78.8452 113.932 79.8273C113.932 81.8738 115.589 83.5335 117.633 83.5335ZM111.199 93.6511L110.887 93.054C110.86 93.0023 107.924 87.5528 102.021 87.5528C96.6494 87.5528 95.3067 92.6801 95.2709 92.9034L95.1096 93.5551L93.8056 93.2386L93.9613 92.5841C93.9613 92.5204 95.5641 86.2045 102.021 86.2045C108.735 86.2045 111.944 92.1738 112.079 92.4301L112.389 93.0131L111.199 93.6511Z" fill="white"/><path d="M153.671 79.5464L153.61 79.2526C153.799 79.2117 153.986 79.1691 154.172 79.1191L154.245 79.4106C154.055 79.4595 153.863 79.5048 153.671 79.5464ZM155.374 79.0731L155.279 78.8004C155.644 78.6739 156.006 78.5361 156.363 78.3873L156.475 78.6572C156.116 78.8089 155.747 78.9481 155.374 79.0731ZM157.545 78.1424L157.413 77.8856C157.758 77.7086 158.094 77.514 158.419 77.3026L158.571 77.5492C158.245 77.7746 157.903 77.9723 157.545 78.1424ZM159.55 76.9078L159.372 76.672C159.681 76.4441 159.982 76.2059 160.275 75.9578L160.476 76.1697C160.171 76.4345 159.879 76.6828 159.55 76.9203V76.9078ZM161.334 75.3555L161.122 75.1532C161.389 74.86 161.643 74.5691 161.876 74.2782L162.106 74.4566C161.865 74.7722 161.61 75.0762 161.34 75.3674L161.334 75.3555ZM162.777 73.4862L162.53 73.3305C162.73 73.0044 162.909 72.6663 163.065 72.3163L163.333 72.4356C163.168 72.8015 162.981 73.1553 162.772 73.497L162.777 73.4862ZM163.743 71.3191L163.45 71.2384C163.556 70.8719 163.635 70.4982 163.687 70.1203L163.978 70.1578C163.93 70.5549 163.85 70.9475 163.739 71.3316L163.743 71.3191ZM164.065 68.9839H163.775V68.8998C163.775 68.5424 163.753 68.185 163.719 67.8276L164.01 67.7992C164.049 68.1633 164.069 68.5284 164.069 68.8947L164.065 68.9839ZM163.541 66.6987C163.463 66.3218 163.366 65.9492 163.25 65.5822L163.541 65.4947C163.661 65.8693 163.758 66.2498 163.832 66.6362L163.541 66.6987ZM162.851 64.4992C162.708 64.1407 162.548 63.789 162.373 63.4453L162.635 63.3146C162.814 63.6722 162.975 64.0295 163.118 64.3867L162.851 64.4992ZM161.83 62.4174C161.652 62.0992 161.45 61.764 161.247 61.4186L161.495 61.2634C161.7 61.5944 161.894 61.9317 162.078 62.2748L161.83 62.4174ZM160.612 60.4345C160.4 60.1146 160.184 59.7896 159.961 59.4663L160.203 59.3021C160.425 59.6265 160.64 59.9498 160.853 60.2776L160.612 60.4345ZM159.299 58.5032C159.081 58.1811 158.86 57.8578 158.644 57.5367L158.885 57.372C159.101 57.6936 159.32 58.0168 159.538 58.335L159.299 58.5032ZM157.991 56.5521C157.779 56.2237 157.571 55.893 157.367 55.56L157.615 55.4083C157.82 55.7407 158.027 56.0674 158.235 56.3947L157.991 56.5521ZM156.776 54.5475C156.577 54.1896 156.394 53.843 156.227 53.5078L156.489 53.3754C156.654 53.7095 156.846 54.0538 157.024 54.4038L156.776 54.5475ZM155.739 52.443C155.589 52.0814 155.449 51.7153 155.321 51.3453L155.613 51.2509C155.735 51.6081 155.872 51.9653 156.023 52.3225L155.739 52.443ZM154.983 50.2146C154.885 49.829 154.803 49.4397 154.737 49.0475L155.03 49.0015C155.094 49.3748 155.172 49.7538 155.269 50.1265L154.983 50.2146ZM154.589 47.8862C154.559 47.5262 154.544 47.1651 154.544 46.8038V46.7072H154.837V46.8004C154.829 47.1542 154.844 47.5072 154.88 47.8595L154.589 47.8862ZM154.88 45.5509L154.589 45.5208C154.593 45.439 154.6 45.3575 154.613 45.2765C154.646 44.9634 154.691 44.6526 154.743 44.3538L155.035 44.4043C154.983 44.7042 154.941 45.0054 154.907 45.3078C154.919 45.3919 154.915 45.4782 154.894 45.56L154.88 45.5509ZM155.285 43.2782L154.994 43.1998C155.101 42.8157 155.227 42.4384 155.367 42.0793L155.638 42.185C155.506 42.5461 155.392 42.9141 155.299 43.2873L155.285 43.2782ZM169.811 41.5776C169.644 41.5735 169.477 41.5579 169.312 41.5311C169.085 41.4982 168.859 41.4602 168.634 41.4169L168.692 41.1254C168.908 41.1651 169.132 41.2044 169.354 41.2362C169.513 41.2559 169.67 41.2703 169.826 41.2793L169.811 41.5776ZM171.01 41.4867L170.948 41.1953C171.314 41.1186 171.664 40.9823 171.986 40.7918L172.138 41.0396C171.794 41.2513 171.418 41.4052 171.025 41.4953L171.01 41.4867ZM156.125 41.1401L155.865 41.0021C156.051 40.654 156.256 40.3163 156.48 39.9907L156.719 40.1589C156.506 40.4775 156.313 40.8074 156.139 41.1487L156.125 41.1401ZM167.494 41.122C167.118 41.0019 166.748 40.8623 166.386 40.7038L166.506 40.4356C166.855 40.5913 167.211 40.7265 167.576 40.8413L167.494 41.122ZM173.025 40.2106L172.782 40.0481C172.99 39.7461 173.113 39.3936 173.139 39.0276L173.431 39.0492C173.406 39.4681 173.269 39.8725 173.033 40.2197L173.025 40.2106ZM165.329 40.168C164.987 39.9688 164.657 39.7511 164.34 39.5157L164.518 39.2816C164.827 39.5104 165.147 39.7206 165.48 39.9123L165.329 40.168ZM157.443 39.2566L157.227 39.06C157.493 38.7657 157.774 38.4882 158.071 38.2276L158.263 38.447C157.976 38.7034 157.706 38.9761 157.452 39.2651L157.443 39.2566ZM163.447 38.7634C163.156 38.4953 162.88 38.2113 162.621 37.9129L162.84 37.7197C163.092 38.0102 163.359 38.2862 163.64 38.5475L163.447 38.7634ZM172.932 37.9703C172.764 37.6364 172.533 37.3389 172.25 37.0947L172.443 36.8765C172.752 37.1457 173.007 37.4721 173.192 37.8379L172.932 37.9703ZM159.167 37.7396L158.999 37.5004C159.325 37.2774 159.663 37.0717 160.01 36.8839L160.148 37.1413C159.812 37.3265 159.487 37.529 159.173 37.7487L159.167 37.7396ZM161.889 36.9805C161.777 36.8218 161.67 36.6596 161.568 36.4941L161.19 36.6447L161.079 36.3748L161.413 36.2407C161.361 36.1532 161.309 36.0623 161.259 35.9782L161.513 35.835C161.571 35.9373 161.629 36.0407 161.691 36.1282C161.853 36.0695 162.019 36.0134 162.189 35.96L162.274 36.2532C162.13 36.2958 161.982 36.3441 161.848 36.3924C161.935 36.5386 162.032 36.678 162.139 36.8106L161.889 36.9805ZM171.314 36.4782C171.032 36.3372 170.739 36.2177 170.439 36.1208L170.247 36.06L170.33 35.7691L170.529 35.8333C170.842 35.9326 171.145 36.0557 171.438 36.2026L171.314 36.4782ZM163.389 35.9549L163.33 35.6651C163.716 35.5782 164.105 35.508 164.497 35.4549L164.54 35.7475C164.155 35.8083 163.773 35.8816 163.4 35.9657L163.389 35.9549ZM169.134 35.7975C168.755 35.7307 168.373 35.6814 167.99 35.6498L168.013 35.3561C168.418 35.3919 168.813 35.4418 169.18 35.5083L169.134 35.7975ZM165.678 35.6276L165.659 35.3367C166.004 35.3154 166.349 35.3046 166.695 35.3043H166.839V35.5958H166.698C166.369 35.6043 166.026 35.6134 165.687 35.6367L165.678 35.6276ZM160.753 34.9328C160.594 34.5754 160.46 34.2055 160.33 33.8288L160.621 33.7379C160.744 34.1055 160.88 34.468 161.031 34.8095L160.753 34.9328ZM160.014 32.6907C159.925 32.3045 159.853 31.9145 159.798 31.522L160.091 31.4811C160.146 31.8669 160.215 32.2492 160.3 32.6191L160.014 32.6907ZM159.688 30.3532C159.673 30.0874 159.665 29.8212 159.664 29.5549V29.1742H159.955V29.5475C159.955 29.81 159.955 30.0839 159.976 30.3305L159.688 30.3532ZM160.036 28.018L159.743 27.9896C159.784 27.6032 159.839 27.2089 159.907 26.8214L160.198 26.8714C160.118 27.2525 160.058 27.6365 160.019 28.0237L160.036 28.018ZM160.434 25.7384L160.143 25.6686C160.235 25.2919 160.344 24.9112 160.463 24.5322L160.754 24.6231C160.628 24.9917 160.518 25.3655 160.425 25.7436L160.434 25.7384ZM161.134 23.5282L160.863 23.4225C161.006 23.0657 161.154 22.6918 161.333 22.3401L161.594 22.4686C161.42 22.8176 161.263 23.1755 161.125 23.5407L161.134 23.5282ZM162.137 21.447L161.884 21.2964C162.082 20.9612 162.294 20.6265 162.515 20.3049L162.754 20.4714C162.531 20.7888 162.322 21.1161 162.129 21.4521L162.137 21.447ZM163.452 19.5458L163.223 19.3669C163.468 19.0759 163.732 18.7646 164.003 18.4919L164.212 18.6936C163.94 18.9678 163.684 19.2555 163.443 19.5566L163.452 19.5458ZM165.038 17.864L164.841 17.6481C165.134 17.3873 165.425 17.1316 165.733 16.8833L165.911 17.114C165.6 17.3623 165.304 17.6163 165.019 17.8697L165.038 17.864ZM166.847 16.4208L166.681 16.1725C167.004 15.9493 167.333 15.7359 167.669 15.5328L167.819 15.7811C167.48 15.9884 167.166 16.2009 166.838 16.4208H166.847ZM168.827 15.2163L168.683 14.9589C169.031 14.7771 169.383 14.6028 169.739 14.4362L169.86 14.7021C169.49 14.868 169.146 15.043 168.81 15.222L168.827 15.2163ZM170.934 14.2464L170.827 13.9714C171.194 13.8282 171.564 13.6799 171.931 13.5725L172.025 13.864C171.653 13.9774 171.286 14.1067 170.925 14.2515L170.934 14.2464ZM173.182 13.5078L173.109 13.2151C173.302 13.1641 173.497 13.1193 173.693 13.0811L173.746 13.3725C173.746 13.3725 173.535 13.4163 173.182 13.5078Z" fill="#263238"/><path d="M178.334 11.6213C177.601 9.31446 172.606 10.1434 173.584 13.1594C174.562 16.1753 179.078 13.9315 178.334 11.6213Z" fill="#263238"/><path d="M170.877 10.6992C170.915 9.68617 172.32 8.91287 174.307 9.27026C176.295 9.62764 178.165 10.9725 178.165 10.9725C178.165 10.9725 176.203 12.1646 174.194 12.3828C172.185 12.6004 170.838 11.7055 170.877 10.6992Z" fill="#EBEBEB"/><path d="M178.163 10.9623C177.13 10.9623 176.103 10.9322 175.074 10.8998C174.045 10.868 173.012 10.8163 171.984 10.7339C173.016 10.7339 174.048 10.7606 175.075 10.793C176.103 10.8254 177.138 10.8771 178.163 10.9629" fill="#263238"/><path d="M172.873 9.79736C173.459 10.0779 174.011 10.4237 174.52 10.828C174.23 10.688 173.949 10.5317 173.678 10.3599C173.399 10.1879 173.131 10.0004 172.873 9.79736ZM175.576 10.8655C175.049 11.2929 174.477 11.6624 173.871 11.9678C174.401 11.5396 174.975 11.17 175.585 10.8655H175.576ZM175.876 10.2957C176.17 10.4377 176.428 10.6451 176.629 10.903C176.333 10.7635 176.075 10.5556 175.876 10.2957Z" fill="#263238"/><path d="M172.772 16.7063C173.392 17.5051 174.987 17.3142 176.39 15.8597C177.792 14.4051 178.53 12.2188 178.53 12.2188C178.53 12.2188 176.235 12.3972 174.482 13.3869C172.73 14.3767 172.147 15.8932 172.772 16.7063Z" fill="#EBEBEB"/><path d="M178.533 12.2109C177.695 12.795 176.878 13.4439 176.059 14.0706C175.241 14.698 174.439 15.3463 173.654 16.0149C174.495 15.4303 175.31 14.7854 176.127 14.1581C176.944 13.528 177.747 12.8787 178.533 12.2109Z" fill="#263238"/><path d="M174.915 16.2458C175.07 15.9617 175.21 15.6698 175.333 15.3702C175.454 15.0691 175.557 14.7617 175.641 14.4481C175.484 14.7311 175.345 15.0235 175.224 15.3236C175.104 15.6251 175.001 15.9325 174.915 16.2458ZM176.494 13.8032C175.812 13.7674 175.128 13.8043 174.453 13.9128C174.793 13.9321 175.134 13.9321 175.474 13.9128C175.816 13.8946 176.156 13.8581 176.494 13.8037M177.057 14.0941C177.135 13.9494 177.194 13.7975 177.235 13.6384C177.278 13.4812 177.304 13.321 177.312 13.1577C177.157 13.4467 177.07 13.7668 177.057 14.0941Z" fill="#263238"/><path d="M178.335 11.6214C178.171 11.6751 177.994 11.6787 177.828 11.6315C177.661 11.5844 177.513 11.4886 177.401 11.3566C177.262 11.1911 177.19 10.9804 177.197 10.7647C177.205 10.5491 177.291 10.3437 177.441 10.1881C177.59 10.0325 177.792 9.93748 178.007 9.92139C178.222 9.90529 178.436 9.96922 178.607 10.1009C178.766 10.2263 178.876 10.4029 178.92 10.6007C178.963 10.7985 178.936 11.0052 178.844 11.1856C178.739 11.3924 178.556 11.5492 178.335 11.6214Z" fill="#455A64"/><path d="M178.872 13.2889C178.749 13.3288 178.619 13.3409 178.491 13.3243C178.363 13.3077 178.24 13.2629 178.131 13.1931C178.022 13.1232 177.93 13.0301 177.862 12.9204C177.793 12.8108 177.75 12.6872 177.735 12.5588C177.715 12.387 177.746 12.2131 177.824 12.0588C177.883 11.9433 177.966 11.8423 178.069 11.763C178.171 11.6837 178.29 11.6282 178.417 11.6004C178.543 11.5726 178.674 11.5733 178.801 11.6024C178.927 11.6315 179.045 11.6882 179.147 11.7686C179.248 11.8489 179.331 11.9508 179.388 12.0669C179.446 12.183 179.477 12.3104 179.479 12.44C179.481 12.5695 179.455 12.6979 179.401 12.8159C179.348 12.934 179.269 13.0387 179.17 13.1225C179.083 13.1974 178.982 13.2541 178.872 13.2889Z" fill="#455A64"/><path d="M83.2977 12.5428C83.1585 13.8818 82.6499 15.1559 81.8289 16.2229C81.0078 17.2899 79.9065 18.1078 78.6477 18.5854C77.3901 19.0627 76.0245 19.181 74.7035 18.927C73.3825 18.6731 72.1581 18.0569 71.167 17.1473L67.5312 17.6831L69.3511 14.5371C68.9353 13.5129 68.7579 12.4076 68.8322 11.3047C68.9065 10.2018 69.2306 9.1303 69.7801 8.17117C70.3297 7.21278 71.0904 6.39221 72.0045 5.77174C73.3824 4.83634 75.04 4.40338 76.6993 4.54553C78.3585 4.68769 79.9184 5.39629 81.117 6.55242C81.9125 7.31982 82.5229 8.25819 82.902 9.29644C83.2811 10.3347 83.419 11.4456 83.3051 12.545L83.2977 12.5428Z" fill="white"/><path d="M83.299 12.5437C83.1639 13.8842 82.6592 15.1608 81.841 16.2312C81.4422 16.7738 80.9606 17.2503 80.4137 17.6432C79.8807 18.0505 79.2921 18.3796 78.666 18.6204C77.7216 18.9916 76.7091 19.1578 75.6955 19.1079C75.3581 19.0989 75.0219 19.0638 74.6899 19.0028C73.3486 18.7534 72.1036 18.1345 71.095 17.2159L71.1802 17.2426L67.5501 17.8L67.2842 17.8415L67.42 17.6074L69.2274 14.454V14.579C68.8004 13.5361 68.6187 12.4091 68.6962 11.2849C68.7737 10.1607 69.1084 9.06926 69.6745 8.09488C70.2307 7.12303 71.0021 6.29153 71.9296 5.66419C73.3282 4.71943 75.011 4.28879 76.6915 4.44565C78.3719 4.6025 79.946 5.33715 81.1455 6.52442C81.937 7.29906 82.5421 8.24343 82.9152 9.28618C83.2882 10.3289 83.4195 11.4428 83.299 12.5437ZM83.299 12.5437C83.4436 11.1738 83.1927 9.79103 82.576 8.55916C81.9594 7.32729 81.0028 6.29783 79.8194 5.5926C78.6371 4.88834 77.2777 4.53798 75.9023 4.5831C74.5269 4.62821 73.1933 5.06691 72.0597 5.84715C71.1714 6.47237 70.4295 7.28311 69.8853 8.22328C69.3477 9.16548 69.0312 10.2175 68.9598 11.3C68.8883 12.3899 69.0633 13.4819 69.4717 14.4949L69.495 14.5642L69.4609 14.625L67.6359 17.7625L67.5069 17.5693L71.1467 17.0659H71.1967L71.2308 17.0977C72.2024 18.0049 73.4074 18.6237 74.7109 18.8846C75.0363 18.9513 75.3652 18.9911 75.6978 19.004C76.0285 19.0278 76.3596 19.0278 76.691 19.004C77.3544 18.95 78.0074 18.8058 78.6319 18.5756C79.2584 18.3471 79.848 18.028 80.3819 17.6284C80.9249 17.2388 81.4061 16.7695 81.8092 16.2364C82.6389 15.1674 83.1548 13.8892 83.299 12.5437Z" fill="#263238"/><path d="M72.5273 15.1799C72.5178 14.9932 72.582 14.8102 72.7062 14.6705C72.8303 14.5307 73.0044 14.4453 73.1909 14.4327H73.2409C73.3389 14.4328 73.436 14.4522 73.5265 14.4898C73.617 14.5274 73.6992 14.5824 73.7685 14.6518C73.8377 14.7212 73.8926 14.8036 73.93 14.8942C73.9674 14.9848 73.9865 15.0819 73.9864 15.1799C73.9862 15.3776 73.9077 15.5673 73.7679 15.7072C73.6282 15.8472 73.4387 15.926 73.2409 15.9265C73.0544 15.9259 72.8754 15.8526 72.7421 15.7221C72.6088 15.5915 72.5317 15.4141 72.5273 15.2276V15.1799ZM72.8176 13.4537L72.6506 7.66113H73.8154L73.6472 13.4537H72.8176ZM75.2943 15.1799C75.2856 14.9935 75.35 14.8111 75.4739 14.6716C75.5978 14.5321 75.7713 14.4466 75.9574 14.4333H76.0023C76.1004 14.4331 76.1975 14.4522 76.2882 14.4897C76.3788 14.5271 76.4612 14.5821 76.5305 14.6515C76.5998 14.7209 76.6548 14.8033 76.6921 14.894C76.7295 14.9846 76.7486 15.0818 76.7483 15.1799C76.748 15.3777 76.6693 15.5673 76.5295 15.7073C76.3897 15.8472 76.2001 15.926 76.0023 15.9265C75.8152 15.9259 75.6357 15.8521 75.5023 15.7208C75.3689 15.5896 75.2922 15.4113 75.2887 15.2242C75.292 15.2097 75.2939 15.1948 75.2943 15.1799ZM75.5852 13.4537L75.4262 7.66113H76.5909L76.421 13.4537H75.5852ZM78.0568 15.1799C78.0481 14.9934 78.1126 14.8109 78.2366 14.6714C78.3606 14.5319 78.5343 14.4464 78.7205 14.4333H78.7705C78.8685 14.4331 78.9656 14.4524 79.0562 14.4898C79.1468 14.5273 79.2291 14.5823 79.2983 14.6517C79.3676 14.7211 79.4225 14.8034 79.4598 14.8941C79.4972 14.9847 79.5162 15.0819 79.5159 15.1799C79.5158 15.3776 79.4372 15.5673 79.2975 15.7072C79.1578 15.8472 78.9682 15.926 78.7705 15.9265C78.5836 15.927 78.404 15.8539 78.2705 15.7232C78.137 15.5924 78.0603 15.4144 78.0568 15.2276V15.1799ZM78.3495 13.4537L78.1801 7.66113H79.3466L79.1807 13.4537H78.3495Z" fill="black"/></g></g></g></g><defs><clipPath id="clip0_2_9098"><rect width="200" height="131.25" fill="white" transform="translate(0 0.984863)"/></clipPath></defs></svg>'
var userRole = $('#userRole').text().trim();
var userRoleValue = $('#userRoleValue').text().trim();
var loggedInUserId = $('#loggedInUserId').text().trim();
var monitorPermission = $("#monitorView")?.data("create-permission")?.toLowerCase();
let serviceId = "";
let PRServerStatus = '';
let DRServerStatus = '';
let pripaddressdata = "";
let dripaddressdata = "";
let infraByInterval = false
let originalInfraOrders = '';

const getBusinessServiceList = async () => {

    try {
        const result = await $.ajax({
            url: "/Dashboard/ITResiliencyView/GetBusinessServiceList",
            dataType: "json",
            traditional: true,
            type: 'GET'
        });

        globalBusinessServiceArray = result.data;
        var data = result.data;

        if (data?.length) {
            for (let i = 0; i < data.length; i++) {
                let bsstatus = data[i]?.status;

                let iconClass = bsstatus?.toLowerCase() === "major impact" ? 'cp-affecteds mb-1 pb-1 fs-6 text-danger' : bsstatus?.toLowerCase() === "available" ? 'cp-success fs-5 text-success' : 'cp-warning fs-5 text-warning';

                let businessHtml = '<button  class="businessServiceClass list-group-item list-group-item-action justify-content-between p-2 border-0 align-items-center text-truncate" aria-current="true" data-service-id="' + data[i].businessServiceId + '"  value="' + data[i]?.businessServiceName + '" id =' + data[i]?.businessServiceId + '>'
                    + '<div  class="text-truncate">'
                    + '<span class="' + iconClass + '" title="' + bsstatus?.toLowerCase() + '"></span>'
                    + '  <span class="list-title text-truncate ms-2" title="' + data[i]?.businessServiceName + '">' + data[i]?.businessServiceName + '</span ></div>'
                    + '</button>';




                $("#businessServiceParentContainer").append(businessHtml);
            }


            // For back button from monitoring module.
            let getIdValue = localStorage.getItem('idValue');

            if (getIdValue) {
                serviceId = getIdValue?.OSId;
                setTimeout(() => {
                    getIdValue = getIdValue ? JSON.parse(getIdValue) : '';
                    if (getIdValue.hasOwnProperty('OSId')) {
                        $('#businessServiceClass').removeClass('Active-Card')
                        $(`#${getIdValue?.OSId}`).addClass('Active-Card')
                        $(`#${getIdValue?.OSId}`).trigger('click')
                        setTimeout(() => {
                            $(`#${getIdValue?.OSId}`)[0]?.scrollIntoView()
                        }, 200)

                    }

                }, 300)
            } else {
                $(".businessServiceClass").first().addClass('Active-Card');
                $(".infraobjectRandom").first().addClass('Active-Card');
                serviceId = data[0]?.businessServiceId;
            }

            if (serviceId) {
                await getBusinessFunctionData(serviceId);
            }

        } else {
            solutionChart = ''
            rpoChart = ''
            rtoChart = ''
            $('#ITView-SolutionDiagram').css('display', 'flex').html(solutionDiagram_noData);
            $('#chartdata').addClass('d-none')
            $('#chartNotFound').css('text-align', 'center').css('height', '100%').removeClass('d-none')
            $('#infraobjectalldata').html(infraAll_noData);
            $('#BusinessFunction').css('text-align', 'center').html(bf_noData);
            $('#businessServiceParentContainer').html(overallBS_noData);
            hand.disabled = true;
            $("#Resilience_Health").removeClass("text-success text-info text-warning");
            $("#Resilience_infraobject").text("NA");
            $("#Resilience_Health").text("NA").addClass("text-info");
        }
    } catch (error) {
        errorNotification(error);
    }
}

let isLoading = false;

$(document).on('click', '.businessServiceClass', itviewDebounce(function () {

    if (isLoading) {
        return;
    }

    isLoading = true;
    getBusinessFunctionData(this.id);
    isLoading = false;
}, 800));

async function getBusinessFunctionData(serviceId) {

    try {

        const resultData = await $.ajax({
            url: "/Dashboard/ITResiliencyView/GetItViewByBusinessServiceId/",
            method: 'GET',
            data: {
                businessServiceId: serviceId,
            },
            dataType: 'json',
            async: true
        });

        let data = resultData.data;

        if (resultData.success) {
            $('#nodeRelationCont').empty();
            const $businessFunction = $("#BusinessFunction");
            const $chartData = $("#chartdata");
            let busFunc_Id = [];
            let infraData = [];
            $businessFunction.empty();
            $(".businessServiceClass").removeClass('Active-Card');
            $("#" + serviceId).addClass("Active-Card");

            $('#dataLagFilterValue').text('')?.attr('title', '');
            $('#dataLagFilterValue').parent().addClass('d-none');

            $('.datalag_filter')?.removeClass('active')
            $('.infraobjectRandom')?.removeClass('d-none')

            let functionsCalled = false;

            if (Array?.isArray(data)) {

                if (data.length > 0) {
                    for (const { businessFunctionId, businessFunctionName, infraObjectDataLag, status } of data) {
                        const infraObjectHtml = generateInfraObjectHtml(businessFunctionId, data, businessFunctionName, infraObjectDataLag, status);
                        let getIdValue = localStorage.getItem('idValue');
                        getIdValue = getIdValue ? JSON.parse(getIdValue) : '';

                        $businessFunction.append(infraObjectHtml);
                        if (infraObjectDataLag && infraObjectDataLag?.length > 0) {

                            busFunc_Id.push(businessFunctionId);
                            infraData.push(infraObjectDataLag);

                            if (getIdValue && getIdValue?.OFId === businessFunctionId) {
                                if (getIdValue.hasOwnProperty('OFId')) {
                                    $('.businessFunction_click').removeClass('show')
                                    $(`#collapse-${getIdValue?.OFId}`).addClass('show')

                                }
                                if (getIdValue.hasOwnProperty('InfraObjectId')) {
                                    $('.infraobjectRandom').removeClass('Active-Card')
                                    $(`#${getIdValue?.InfraObjectId}`).addClass('Active-Card')
                                    $(`#${getIdValue?.InfraObjectId}`)?.find('.particularInfra')?.trigger('click')
                                    $(`#${getIdValue?.InfraObjectId}`)[0]?.scrollIntoView()
                                }
                                localStorage.removeItem('idValue')
                            }

                            if (!functionsCalled) {
                                functionsCalled = true;

                                if (busFunc_Id?.length > 0) {

                                    if (!getIdValue) {
                                        $(`.getBusinessFunctionId[Bf_Id='${busFunc_Id[0]}']`).addClass('show');
                                        let mode = $(document).find('#nodeName').find(':selected').text();
                                        const first_infraData = infraData[0];
                                        GetInfraObjectId(first_infraData[0]?.infraObjectId, first_infraData[0]?.infraObjectName, first_infraData[0]?.monitorType);
                                        //Resiliencechart(first_infraData[0]?.infraObjectId, first_infraData[0]?.infraObjectName, first_infraData[0]?.dataLagValue, first_infraData[0]?.configuredRPO, first_infraData[0]?.monitorType);
                                        GetSolutionInfraObjectId(first_infraData[0]?.infraObjectId, first_infraData[0]?.infraObjectName, first_infraData[0]?.monitorType, mode)
                                    }
                                }

                            }

                        }

                    }


                } else {
                    solutionChart = ''
                    rpoChart = ''
                    rtoChart = ''
                    $businessFunction.append('<div class="collapsed businessFunction_click"  id="nodataBF"></div>');
                    $('#nodataBF').css('text-align', 'center').html(overallBS_noData);
                    $chartData.addClass('d-none')
                    $('#chartNotFound').css('text-align', 'center').css('height', '100%').removeClass('d-none')
                    $('#infraobjectalldata').html(infraAll_noData);
                    $('#ITView-SolutionDiagram').css('display', 'flex').html(solutionDiagram_noData);
                    hand.disabled = true;
                    $("#Resilience_Health").removeClass("text-success text-info text-warning");
                    $("#Resilience_infraobject").text("NA");
                    $("#Resilience_Health").text("NA").addClass("text-info")
                }

            }
            else {
                const idElements = ['#BusinessFunction', '#chartdata', '#infraobjectalldata', '#infrasummary'];
                idElements.forEach(element => {
                    $(element).css('text-align', 'center').html(overallBS_noData);
                });
                hand.disabled = true;
                $("#Resilience_Health").removeClass("text-success text-info text-warning");
                $("#Resilience_infraobject").text("NA");
                $("#Resilience_Health").text("NA").addClass("text-info")

            }
            if (!$('.infraobjectRandom').hasClass('Active-Card')) {
                $('.infraobjectRandom').first().addClass('Active-Card');
            }
        }
    } catch (error) {

    }
}

const getInfraSummary = async () => {
    await $.ajax({
        url: "/Dashboard/ITResiliencyView/GetAllInfraSummaries",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (res) {
            var data = res.data
            if (data?.length) {
                const ServerData = data.filter(item => item?.entityName === "Server");
                let infrasummary = '<div id="carouselExampleIndicators" class="carousel slide" data-bs-ride="carousel">' +
                    '<div class="carousel-inner">';
                const entities = [
                    { name: "Server", icon: "windows.svg", data: ServerData },
                    { name: "Database", icon: "oraclerac.svg", data: data.filter(item => item?.entityName === "Database") },
                    { name: "Replication", icon: "mysql.svg", data: data.filter(item => item?.entityName === "Replication") }
                ];

                entities.forEach((entity, index) => {

                    infrasummary += '<div class="carousel-item ' + (index === 0 ? 'active' : '') + '">' +
                        '<div class="card Card_Design_None mb-0  green-shades-color">' +
                        '<div class="card-header py-0 p-2"><span class="card-title">' + entity?.name + '</span></div>' +
                        '<div class="card-body p-0" style="height:240px; overflow-y:auto">';
                    entity.data.forEach(item => {

                        let icon = item?.logo && item?.logo != "" && item?.logo != null ? item?.logo?.replace(/[^a-zA-Z0-9]/g, '_') + '.svg' : item?.type.toLowerCase().includes("linux") ? "cp_linux.svg" : item?.type.toLowerCase() === "windows" ? "cp_windows.svg" : item?.entityName?.toLowerCase() === "server" ? 'cp_server.svg' : item?.entityName?.toLowerCase() === "database" ? 'cp_database.svg' : item?.entityName?.toLowerCase() === "replication" ? 'cp_replication_rotate.svg' : null
                        if (item?.logo === "cp-replication-on") {
                            icon = 'cp_replication_rotate.svg'
                        }
                        if (item?.logo === "cp-server" || item?.type === 'Azure_MSSQL_PaaS_Replication' || item?.type === 'AzureDatabase_MySQL_PaaS' || item?.type === 'AzureDatabase_MSSQL_PaaS' || item?.type === 'AzureDatabase_PostgreSQL_Paas') {
                            icon = 'azure.svg'
                        }
                        if (item?.type === 'RoboCopy') {
                            icon = 'cp_robocopy.svg'
                        }
                        if (item?.type?.toLowerCase()?.includes('activedirectory')) {
                            icon = 'cp_activedirectory.svg'
                        }
                        if (item?.type === 'HP3Par With Application') {
                            icon = 'HP3Par.svg'
                        }
                        if (item?.type === 'CloudAzure') {
                            icon = 'cloud-azure.svg'
                        }
                        if (item?.type === 'Azure_Storage_Replication') {
                            icon = 'azure_storage_replication.svg'
                        }
                        if (item?.type === 'RSync') {
                            icon = 'RSync.svg'
                        }
                        if (item?.type === 'MaxDB') {
                            icon = 'maxDB.svg'
                        }
                        if (item?.type === 'RedisReplication') {
                            icon = 'RedisReplication.svg'
                        }
                        if (item?.type === 'RedisDB') {
                            icon = 'Redis-database.svg'
                        }
                        if (item?.type === 'DB2HADR') {
                            icon = 'DB2.svg'
                        }
                        infrasummary +=
                            '<ul class="list-group list-group-flush">' +
                            '<li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">' +
                            (item?.entityName?.toLowerCase() === "server" ?
                                '<img src="/img/OS_Icon/' + icon + '" width="20"></img>' : item?.entityName?.toLowerCase() === "database" || item?.entityName?.toLowerCase() === "replication" ?
                                    '<img src="/img/DB-Logo/' + icon + '" width="20"></img>' : null) +
                            '<span type="button" class="flex-fill mx-2 text-truncate" onclick="summaryReport(this)" data-entity-name="' + item?.entityName + '" data-type-name="' + item?.typeId + '">' + item?.type + '</span>' +
                            '<span class="text-dark fs-6 fw-semibold">' + item?.count + '</span>' +
                            '</li>' +
                            '</li>' +
                            '</ul>';
                    });
                    infrasummary += '</div></div></div>';
                });
                infrasummary += '</div>' +
                    '<div class="carousel-indicators pb-2">' +
                    entities.map((_, index) =>
                        '<button type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide-to="' + index + '" ' +
                        'class="bg-primary' + (index === 0 ? ' active' : '') + '" ' +
                        'aria-label="Slide ' + (index + 1) + '"></button>'
                    ).join('') +
                    '</div>' +
                    '</div>';

                $("#infrasummary").html(infrasummary);

                $('#nodeRelationCont').empty();
            }
            else {
                $('#infrasummary')
                    .css('text-align', 'center')
                    .html(overallBS_noData);
            }

        },
        error: function (res) {
            errorNotification(res)
        }
    });

}

(() => {
    getBusinessServiceList();
    getInfraSummary()

})();

function summaryReport(data) {

    let type = data?.dataset?.entityName?.toLowerCase();
    if (type === 'server') {
        sessionStorage.setItem('serverDataFromITView', data?.dataset?.typeName);
        window.location.href = '/Configuration/Server/List';
    }
    else if (type === "database") {
        sessionStorage.setItem('databaseFromITView', data?.dataset?.typeName);
        window.location.href = '/Configuration/Database/List';
    }
    else if (type === "replication") {
        sessionStorage.setItem('replicationFromITView', data?.dataset?.typeName);
        window.location.href = '/Configuration/Replication/List';
    }
    //let type = data?.dataset?.typeName;
    //let entityName = data?.dataset?.entityName;
    //sessionStorage.removeItem('serverDataFromITView')
    //sessionStorage.removeItem('databaseFromITView')
    //sessionStorage.removeItem('replicationFromITView')

    //if (entityName.toLowerCase() === "server") {
    //    sessionStorage.setItem('serverDataFromITView', type);
    //    window.location.href = '/Configuration/Server/List';
    //} else if (entityName.toLowerCase() === "database") {
    //    sessionStorage.setItem('databaseFromITView', type);
    //    window.location.href = '/Configuration/Database/List';
    //}
    //else if (entityName.toLowerCase() === "replication") {
    //    sessionStorage.setItem('replicationFromITView', type);
    //    window.location.href = '/Configuration/Replication/List';
    //}
}
function itviewDebounce(func, delay = 300) {
    let timer;
    return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
            func.apply(context, args);
        }, delay);
    };
}

$('#search-bar').on('input', itviewDebounce(function () {
    let searchText = $(this).val().toLowerCase();
    let searchFlagStatus = true;

    $('.businessServiceClass').each(function () {
        let buttonValue = $(this).attr('id');
        let buttonName = $(this).attr('value')?.toLowerCase();

        if (buttonName?.includes(searchText)) {

            searchFlagStatus = false
            $(this).show();
        } else {
            $(this).hide();
        }
    });

    if (searchFlagStatus) {
        $("#search_noData").removeClass('d-none')
    }
    else {
        $("#search_noData").addClass('d-none')
    }

}, 500));

$('.itViewSearch').on('change', itviewDebounce(function (e) {
    if (e.target.checked) {
        if (this.id === 'It_OperationalFunction') {
            $('#It_InfraObject').prop('checked', false)
        } else {
            $('#It_OperationalFunction').prop('checked', false)
        }
    } else {
        if (this.id === 'It_OperationalFunction') {
            $('#It_InfraObject').prop('checked', true)
        } else {
            $('#It_OperationalFunction').prop('checked', true)
        }
    }
}, 500));

//$(document).on('input', '#It_searchBusinessName', itviewDebounce(function () {
//    let searchvalue = $(this).val()?.toLowerCase();
//    if ($('.itViewSearch').first().prop('checked')) {
//        $('.infraobjectRandom').each(function (idx, obj) {
//            if (searchvalue.length > 0) {
//                if ($(`#${this.id} .it_infraObjectName`).text()?.toLowerCase()?.includes(searchvalue)) {
//                    if ($(`#${this.id}`).hasClass('d-none')) {
//                        $(`#${this.id}`).removeClass('d-none')
//                        $(`#${this.id}`).addClass('d-flex')
//                    }

//                } else {
//                    if ($(`#${this.id}`).hasClass('d-flex')) {
//                        $(`#${this.id}`).removeClass('d-flex')
//                        $(`#${this.id}`).addClass('d-none')
//                    }
//                }
//            } else {
//                $(`#${this.id}`).removeClass('d-none')
//                $(`#${this.id}`).addClass('d-flex')
//            }

//        })
//    } else {
//        $('.It_BSData').each(function (idx, obj) {
//            if (searchvalue.length > 0) {
//                if ($(`#${this.id} .It_OperationalService`).text()?.toLowerCase()?.includes(searchvalue)) {

//                    $(`#${this.id}`).show();
//                    $(`#${this.id}`).next().show();

//                } else {

//                    $(`#${this.id}`).hide();
//                    $(`#${this.id}`).next().hide();
//                }
//            } else {

//                $(`#${this.id}`).show();
//                $(`#${this.id}`).next().show();
//            }
//        });
//    }
//}, 500));
$(document).on("click", ".businessServiceClass", function () {
    $("#It_searchBusinessName").val("");
    $("#It_searchBusinessName").trigger("input");
});
$(document).on("input", "#search-bar", function () {
    $("#It_searchBusinessName").val("");
    $("#It_searchBusinessName").trigger("input");
});

$('#It_searchBusinessName').on('input', itviewDebounce(function () {
    let searchvalue = $(this).val()?.toLowerCase();
    let hasResults = false;

    $('.infraobjectRandom').each(function () {
        if (searchvalue?.length) {
            if ($(`#${this.id} .it_infraObjectName`).text()?.toLowerCase()?.includes(searchvalue)) {
                $(this).removeClass('d-none').addClass('d-flex');
                hasResults = true;
            } else {
                $(this).removeClass('d-flex').addClass('d-none');
            }
        } else {
            $(this).removeClass('d-none').addClass('d-flex');
            hasResults = true;
        }
    });

    $('.It_BSData').each(function () {
        const $this = $(this);
        const id = $this?.attr('id');
        const collapseSelector = `#collapse-${id}`;
        let shouldShow = false;

        if (searchvalue?.length) {
            const matchText = $this?.find('.It_OperationalService')?.text()?.toLowerCase()?.includes(searchvalue);

            const anyVisibleLi = $(`${collapseSelector} .infraobjectRandom`)?.filter(function () {
                return !$(this)?.hasClass('d-none');
            }).length > 0;

            if (matchText || anyVisibleLi) {
                shouldShow = true;
            }

            if (shouldShow) {
                $this?.removeClass('d-none')?.addClass('d-flex');

                if (matchText && !anyVisibleLi) {
                    $(`${collapseSelector} .infraobjectRandom `)?.each(function () {
                        $(this)?.removeClass('d-none')?.addClass('d-flex');
                    });
                }

                hasResults = true;
            } else {
                $this?.removeClass('d-flex')?.addClass('d-none');
            }
        } else {
            $this?.removeClass('d-none')?.addClass('d-flex');
            hasResults = true;
        }
    });


    if (!hasResults) {
        if ($('#noDataFoundMessage').length === 0) {
            $('#BusinessFunction').append(`
                    <li id="noDataFoundMessage" class="list-group-item text-center mt-5">
                        <img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">
                    </li>
                `);
        }
    } else {
        $('#noDataFoundMessage').remove();
    }
}, 500));

$('.datalag_filter').on('click', function () {
    let currentDataLag = $(this)?.attr('mode')
    let currentState = $(this)?.attr('state')
    let currentType = $(this)?.attr('type')

    $('.datalag_filter')?.removeClass('active')
    $(this)?.addClass('active')
    $('#datalag_filter_form')?.removeClass('show')

    $('#dataLagFilterValue').text($(this)?.text())?.attr('title', $(this)?.text());
    $('#dataLagFilterValue').parent().removeClass('d-none');

    $('.It_BSData')?.each(function () {
        let getId = $(this)?.attr('id');
        if (currentDataLag) {
            originalInfraOrders = $('#BusinessFunction')?.html()

            const sorted = $(`#collapse-${getId}`)?.find('.infraobjectRandom')?.sort(function (a, b) {
                const aText = $(a)?.find('.particularInfra')?.data('infra-value');
                const bText = $(b)?.find('.particularInfra')?.data('infra-value');

                const aSeconds = convertToSeconds(aText);
                const bSeconds = convertToSeconds(bText);

                if (Number(currentDataLag) == 2) return aSeconds - bSeconds;
                else return bSeconds - aSeconds;

            });

            $(`#collapse-${getId}`)?.find('ul')?.empty().append(sorted);
        } else if (currentState || currentType) {
            $(`#collapse-${getId}`)?.find('.infraobjectRandom')?.each(function () {
                const $item = $(this);
                let getState = $item.data('state');
                let drOperationStatus = $item.data('droperation');

                if (getState?.toLowerCase() === currentState?.toLowerCase() || currentType?.includes(drOperationStatus)) {
                    $item.removeClass('d-none');
                } else {
                    $item.addClass('d-none');
                }
            });
        }

    });

})

$('#clearFilterValue').on('click', function () {
    $('#dataLagFilterValue').text('')?.attr('title', '');
    $('#dataLagFilterValue').parent().addClass('d-none');

    $('.datalag_filter')?.removeClass('active')
    $('.infraobjectRandom')?.removeClass('d-none')
    if (originalInfraOrders) $('#BusinessFunction')?.html(originalInfraOrders);

    originalInfraOrders = ''
})

function convertToSeconds(timeStr) {
    const parts = timeStr?.split(':')?.map(Number);
    if (parts?.length === 3) {
        return parts[0] * 3600 + parts[1] * 60 + parts[2];
    } else if (parts?.length === 2) {
        return parts[0] * 60 + parts[1];
    }
    return 0;
}


function generateInfraObjectHtml(businessFunctionId, value, businessFunctionName, infraObjectDataLag, status) {
    $('#nodeRelationCont').empty()
    let infraObjectHtml = ''
    iconbs = status?.toLowerCase() == "major impact" ? 'text-danger' : status?.toLowerCase() == "available" ? 'text-success' : 'text-warning';

    infraObjectHtml = `
        <div class="accordion-button collapsed businessFunction_click It_BSData" role="button" id="${businessFunctionId}" value="${businessFunctionName}" 
            data-bs-toggle="collapse" data-bs-target="#collapse-${businessFunctionId}" aria-expanded="false" aria-controls="collapse-${businessFunctionId}" 
            parentBusinessId="${value}">
            <i class="cp-business-function me-2 fs-5"></i><span class='It_OperationalService'>${businessFunctionName}<i class="cp-single-dot ${iconbs} ms-1 blink fs-10"></i></span>
        </div>
        <div id="collapse-${businessFunctionId}" class="accordion-collapse collapse getBusinessFunctionId" 
            data-bs-parent="#accordionFlushExample" BF_Id=${businessFunctionId} BS_Id=${value}>
            <div id="InfraObject" class="accordion-body p-1" style="overflow-y: auto;">
                <ul class="list-group list-group-flush Profile-Select">
    `;

    if (infraObjectDataLag && infraObjectDataLag?.length > 0) {

        for (let infraObject of infraObjectDataLag) {
            infraObjectHtml += generateInfraObjectItemHtml(infraObject);

        }

    } else {
        solutionChart = ''
        rpoChart = ''
        rtoChart = ''
        hand.disabled = true;
        $("#Resilience_Health").removeClass("text-success text-info text-warning");
        $("#Resilience_infraobject").text("NA");
        $("#Resilience_Health").text("NA").addClass("text-info");
        $('#ITView-SolutionDiagram').css('display', 'flex')
            .html(solutionDiagram_noData);

        $('#chartdata').addClass('d-none')
        $('#chartNotFound').css('text-align', 'center').css('height', '100%').removeClass('d-none')

        $('#infraobjectalldata')
            .html(infraAll_noData);
        //$('#Resilience_data_Health')

        //    .html(overallBS_noData);
        //infraObjectHtml += `<li class="list-group-item text-center" id="noDataFoundMessage">
        //    <img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">
        //</li>`;
    }

    infraObjectHtml += `</ul></div></div>`;

    return infraObjectHtml;

}
function generateInfraObjectItemHtml(infraObject) {

    $('#nodeRelationCont').empty()
    //if (infraObject?.hasOwnProperty('infraObjectId')) {
    //    healthChart(infraObject)
    //}
    const infraObjectStatus = infraObject?.drOperationStatus === 2
        ? '<i class="cp-off-arrow text-primary"></i>'
        : '<i class="cp-on-arrow text-primary"></i>';

    const dataLagValue = (infraObject?.dataLagValue !== undefined && infraObject?.dataLagValue !== "" &&
        infraObject?.dataLagValue !== null && infraObject?.dataLagValue !== "0")
        ? `${infraObject?.dataLagValue}`
        : 'NA';

    let result = "";
    let resultDatalag = "";
    let styleValue = "";
    if (dataLagValue === "NA") {
        resultDatalag = dataLagValue;
    }
    else {
        if (dataLagValue?.includes(".")) {
            const value = dataLagValue?.split(".");
            const hours = value[0] * 24;
            const minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const min = minutes?.split(':');
            const firstValue = parseInt(min[0]) + parseInt(hours);
            result = firstValue + ":" + min[1];
            styleValue = (parseInt(result[0]) * 60) + parseInt(result[1]);
            resultDatalag = dataLagValue
        } else if (dataLagValue?.includes("+")) {
            const value = dataLagValue?.split(" ");
            result = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            styleValue = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            resultDatalag = value[1]
        } else {
            result = dataLagValue?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')
            styleValue = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            resultDatalag = dataLagValue
        }
    }

    const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
    const dataLagStyle = styleValue < infraObject?.configuredRPO ? "bg-success" : "bg-danger";
    const typeName = infraObject?.typeName
    const typeIcon = typeName?.toLowerCase() === 'db' ? 'cp-database text-primary me-2' : typeName?.toLowerCase() === 'virtual' ? 'cp-virtual text-primary me-2' : typeName?.toLowerCase() === 'application' ? 'cp-application text-primary me-2' : ''

    return `
        <li class="list-group-item p-2 list-group-item-action d-flex justify-content-between align-items-center infraobjectRandom" id=${infraObject.infraObjectId} value=${infraObject.infraObjectName}
         data-drOperation=${infraObject?.drOperationStatus} data-state=${infraObject?.state}>
            <div class="d-flex flex-fill text-truncate w-75">
                <div><i class="${typeIcon}" title="${typeName}"></i></div>
                <div class="flex-fill text-truncate particularInfra" onclick="GetInfraObjectData(this)" role="button"
                        data-type="${infraObject?.typeName}"
                        data-moniter-type="${infraObject?.monitorType || ''}" 
                        data-infra-name="${infraObject?.infraObjectName || ''}" 
                        data-infra-id="${infraObject?.infraObjectId || ''}"
                         data-infra-value="${infraObject?.dataLagValue || ''}"
                          data-infra-rpo="${infraObject?.configuredRPO || ''}">
                    <p class="fw-semibold mb-2 text-start it_infraObjectName text-truncate" id="getInfraobject">
                        ${infraObject?.infraObjectName || ''}
                    </p>
                    <div class="d-flex align-items-center">
                    
                        <i class="cp-datalog text-light me-2"></i>
                        <small class="">DataLag</small>
                        <div class="progress w-50 mx-2" role="progressbar" 
                            aria-valuenow="${result || ''}" aria-valuemin="0" aria-valuemax="100" style="height:4px">
                            <div class="progress-bar ${dataLagStyle || ''} progress-bar-striped progress-bar-animated" 
                                style="width:${progressresult || ''}%"></div>
                        </div>
                        <small class="text-dark fw-semibold">${resultDatalag || ''}</small>
                        
                    </div>
                </div>
            </div>
            <div class="d-grid">
                <div class="btn-group-sm">
                  ${monitorPermission === 'false' ? `
                   <button type="button" class="btn btn btn-outline-secondary border-0"
                        disabled
                        data-moniter-id="${infraObject?.monitorId || ''}" 
                        data-moniter-type="${infraObject?.monitorType || ''}"
                        data-moniter-status="${infraObject?.drOperationStatus || ''}"
                        data-infraobject-id="${infraObject?.infraObjectId || ''}"
                        data-replication-type="${infraObject?.replicationType || ''}">
                        <i class="cp-Monitor" title="Monitor"></i>
                    </button>
                    `: `
                    <button type="button" class="btn btn btn-outline-secondary border-0" 
                        onclick="GetMonitorType(this)" 
                        data-moniter-id="${infraObject?.monitorId || ''}" 
                        data-moniter-type="${infraObject?.monitorType || ''}"
                        data-moniter-status="${infraObject?.drOperationStatus || ''}"
                        data-infraobject-id="${infraObject?.infraObjectId || ''}"
                        data-infraobject-name="${infraObject?.infraObjectName || ''}"
                        data-replication-type="${infraObject?.replicationType || ''}">
                        <i class="cp-Monitor" title="Monitor"></i>
                    </button>
                    `}
                   
                    ${userRoleValue === 'Manager' || userRoleValue === 'Operator' || monitorPermission === 'false' ? `
    <button type="button" class="btn btn btn-outline-secondary border-0" 
        
      disabled
        
        data-active-state="${infraObject?.state || ''}" 
        data-active-id="${infraObject?.infraObjectId || ''}" 
        data-active-name="${infraObject?.infraObjectName || ''}">
         ${infraObject?.state?.toLowerCase() === "active" ? '<i class="cp-active-inactive text-success fw-semibold" title="Active"></i>' : infraObject?.state?.toLowerCase() === "maintenance" ? '<i class="cp-maintenance" title="Maintenance"></i>' : '<i class="cp-lock" title="Lock"></i>'}
    </button>
` : `

   <button type="button" class="btn btn-outline-secondary border-0 activeMaintanenceIcon"
        ${infraObject?.state?.toLowerCase() === "locked" ? "disabled" : `onclick="GetState(this)"`} 
        data-active-state="${infraObject?.state || ''}" 
        data-active-id="${infraObject?.infraObjectId || ''}" 
        data-active-name="${infraObject?.infraObjectName || ''}">
        ${infraObject?.state?.toLowerCase() === "active"
            ? '<i class="cp-active-inactive text-success fw-semibold" title="Active"></i>'
            : infraObject?.state?.toLowerCase() === "maintenance"
                ? '<i class="cp-maintenance" title="Maintenance"></i>'
                : '<i class="cp-lock" title="Lock"></i>'
        }
    </button>
` }
             ${userRoleValue === 'Manager' || userRoleValue === 'Operator' ? `
               <button type="button" class="btn btn-outline-secondary border-0 refresh_btn" disabled
 
                        data-active-id="${infraObject?.infraObjectId || ''}" 
                        data-active-name="${infraObject?.infraObjectName || ''}">
                        <i class="cp-reload text-primary" title="Start Replication Monitor"></i>
                    </button>`: `
                    <button type="button" class="btn btn-outline-secondary border-0 refresh_btn" 
                        onclick="ReScheduleJob(this)" 
                        ${infraObject?.replicationStatus !== 1 && infraObject?.state?.toLowerCase() === "active" ? "" : "style='pointer-events:none; opacity:0.5'"}
                        data-active-id="${infraObject?.infraObjectId || ''}" 
                        data-active-name="${infraObject?.infraObjectName || ''}">
                        <i class="cp-reload text-primary" title="Start Replication Monitor"></i>
                    </button>
                    `}
                </div>
              <div class="d-flex align-items-center mt-1 justify-content-around">
                <small class="text-dark fw-semibold">PR</small>
                ${infraObject?.monitorType?.toLowerCase() !== 'openshift' ? infraObjectStatus : ''}
                ${infraObject?.monitorType?.toLowerCase() !== 'openshift' ? '<small class="text-dark fw-semibold">DR</small>' : ''}
              </div>  

            </div>
        </li>`;

}


async function GetState(data) {

    let state = data?.dataset?.activeState;
    let name = data?.dataset?.activeName
    let setstate = state?.toLowerCase() === "active" ? "Maintenance" : "Active"

    $("#stateinfraname").text("(" + name + ")");
    $("#state").text(setstate)

    if (state.toLowerCase() === "active") {

        $("#textbox").show();
    } else {
        $("#textbox").hide();
    }

    infraObjectGlobalId = data?.dataset?.activeId
    $("#ActiveMaintanenceModal").modal('show')
}

$(function () {

    $('#ActiveMaintanenceButton').on('click', itviewDebounce(async function () {
        try {
            let description = $("#textArea").val();

            let state = $("#state").text();

            let action = {
                "updateInfraObjectStates": [
                    {
                        "id": infraObjectGlobalId,
                        "state": state,
                        "Reason": description,

                    }
                ]
            };
            if (action && action?.updateInfraObjectStates && action?.updateInfraObjectStates?.length > 0) {

                let result = await $.ajax({
                    type: 'PUT',
                    url: "/Dashboard/ITResiliencyView/UpdateInfraObjectState",
                    dataType: "json",
                    headers: {
                        'RequestVerificationToken': gettoken()
                    },
                    contentType: "application/json",
                    data: JSON.stringify(action),
                    traditional: true
                });

                if (result && result?.data?.success) {

                    let datas = result?.data;
                    notificationAlert("success", datas?.message);
                    let iconStatus = datas?.message && datas.message?.toLowerCase()?.includes("active") ? '<i class="cp-active-inactive text-success fw-semibold" title="Active"></i>' : '<i class="cp-maintenance" title="Maintenance"></i>';
                    let status = datas?.message && datas?.message?.toLowerCase()?.includes("active") ? "Active" : "Maintenance";

                    let getContainerId = $('#' + infraObjectGlobalId).parents('.getBusinessFunctionId ').attr('id');

                    if (globalBusinessServiceArray && getContainerId) {
                        globalBusinessServiceArray.forEach((obj) => {
                            if (obj.businessServiceId === $('#' + getContainerId).attr('bs_id')) {
                                obj.businessFunctionDataLag.forEach((item) => {
                                    if (item.businessFunctionId === $('#' + getContainerId).attr('bf_id')) {
                                        item.infraObjectDataLag.forEach((data) => {
                                            if (data.infraObjectId === infraObjectGlobalId) {
                                                data.state = action.updateInfraObjectStates[0].state;
                                            }
                                        });
                                    }
                                });
                            }
                        });

                        $('#' + infraObjectGlobalId + ' .activeMaintanenceIcon').empty().append(iconStatus);
                        $('#' + infraObjectGlobalId + ' .activeMaintanenceIcon').attr('data-active-state', status);
                        $('#clearFilterValue')?.trigger('click')
                        $("#ActiveMaintanenceModal").modal('hide');
                    } else {
                        errorNotification(result?.data);
                        $("#ActiveMaintanenceModal").modal('hide');
                    }
                } else {
                    errorNotification(result?.data);
                    $("#ActiveMaintanenceModal").modal('hide');
                }
            }
        } catch (error) {

        }
    }, 500));
});


async function ReScheduleJob(data) {
    $('.refresh_btn').prop('disabled', true);

    const requestData = {
        infraObjectId: data?.dataset?.activeId,
        infraObjectName: data?.dataset?.activeName,

    };
    await $.ajax({
        url: "/Dashboard/ITResiliencyView/RescheduleJob",
        traditional: true,
        type: 'POST',
        dataType: "json",
        headers: {
            'RequestVerificationToken': gettoken()
        },
        contentType: 'application/json',
        data: JSON.stringify(requestData),
        success: function (res) {
            if (res.success) {
                notificationAlert("success", res?.message);
                $('.refresh_btn').prop('disabled', false);
            }
            else {
                notificationAlert("warning", res?.message);
            }
        },

        error: function (err) {
            notificationAlert("warning", err.message)
        }
    });
}
function GetMonitorType(data) {
    //debugger
    let monitortype = data?.dataset?.moniterType || ''
    let monitorid = data?.dataset?.moniterId || ''
    let monitorstatus = data?.dataset?.moniterStatus || ''
    let infraobjectid = data?.dataset?.infraobjectId || ''
    let replicationtype = data?.dataset?.replicationType || ''
    let infraobjectname = data?.dataset?.infraobjectName || ''
    sessionStorage.setItem("monitorId", monitorid)
    sessionStorage.setItem("moniterType", monitortype)
    sessionStorage.setItem("infraobjectId", infraobjectid)
    sessionStorage.setItem("moniterStatus", monitorstatus)

    if (monitorid) {
        let idValue = {
            'OSId': $('.businessServiceClass.Active-Card')[0]?.id,
            'OFId': $('.getBusinessFunctionId.show')[0]?.id?.replace('collapse-', ''),
            'InfraObjectId': infraobjectid
        }

        localStorage.setItem('idValue', JSON.stringify(idValue))
    }

    //localStorage.setItem("Business")

    // let monitordata = {}
    // monitordata.monitorId = monitorid
    // monitordata.type = monitortype

    //$.ajax({
    //    type: "GET",
    //    url: RootUrl + 'Monitor/Monitoring/GetMonitorServiceStatusByIdAndType',
    //    dataType: "json",
    //    data: monitordata,
    //    traditional: true,
    //    success: function (result) {

    //        if (result?.data?.pageProperties == "NA" || result?.data?.pageProperties == undefined || result?.data?.pageProperties == null) {
    if (monitortype?.toLowerCase() === "oracle" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/OracleDataguard/List";
    }
    else if (monitortype?.toLowerCase() === "oraclerac" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/OracleRAC/List";
    }
    else if (monitortype?.toLowerCase() === "mysql" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/Mysql/List";
    }
    else if (monitortype?.toLowerCase() === "postgres" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/PostgreSQL/List";
    }
    else if (monitortype?.toLowerCase() === "mssqlalwayson" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/MSSQLAlwaysOn/List";
    }
    else if (monitortype?.toLowerCase() === "mssqlnls" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/MSSQLNativeLogShipping/List";
    }
    else if (monitortype?.toLowerCase() === "hyperv" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/HyperV/List";
    }
    else if (monitortype?.toLowerCase() === "rsyncappreplication" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/Rsync/List";

    }
    else if (monitortype?.toLowerCase() === "mssqlalwaysonavailabilitygroup" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/MSSQLAlwaysOnAvailabilityGroup/List";
    }
    else if (monitortype?.toLowerCase() === "datasyncappreplication" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/DataSync/List";
    }
    else if (monitortype?.toLowerCase() === "windowsactivedirectory" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/WindowsActiveDirectory/List";
    }
    else if (monitortype?.toLowerCase() === "robocopy" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/WindowsRoboCopy/List";
    }
    else if (monitortype?.toLowerCase() === "mongodb" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/MongoDB/List";
    }
    else if (monitortype?.toLowerCase() === "db2hadr" && (monitorid || monitorstatus)) {

        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/DB2HADRLinux/List";
    }
    else if (monitortype?.toLowerCase() === "mssqldbmirroring" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("replicationType", replicationtype)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/MSSQLMirror/List";
    }
    else if (monitortype?.toLowerCase() === "CyberRecover" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/CyberRecovery/List";
    }
    else if (monitortype?.toLowerCase() === "as400" && (monitorid || monitorstatus)) {

        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/AS400/List";
    }
    else if (monitortype?.toLowerCase() === "srm" && (monitorid || monitorstatus)) {

        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/SRM/List";
    }
    else if (monitortype?.toLowerCase() === "svc" && monitorid) {
        sessionStorage.setItem("monitorId", monitorid)
        window.location.href = "/Monitor/SVCGlobalMirror/List";
    } else if (monitortype?.toLowerCase() === "openshift" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/OCPMonitoring/List";

    }

    else if (monitortype?.toLowerCase() === "azuremysqlpaas" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/AzureDatabaseforMySQLReplicationPaaS/List";
    }
    else if (monitortype?.toLowerCase() === "azurepostgrespaas" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/AzureDatabaseforPostgresReplicationPaaS/List";
    }
    else if (monitortype?.toLowerCase() === "azuremssqlpaas" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/AzureDatabaseforMSSQLReplicationPaaS/List";

    }
    else if (monitortype?.toLowerCase() === "azurestorageaccount" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/AzureStorageAccount/List"
    }
    else if (monitortype?.toLowerCase() === "recoverpointforvm" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/RPForVM/List";

    }
    else if (monitortype?.toLowerCase() === "sybasershadr" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/SybaseWithRSHADR/List"
    }
    else if (monitortype?.toLowerCase() === "zertovpg" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/ZertoVPG/List";
    }
    else if (monitortype?.toLowerCase() === "goldengatereplication" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/GoldenGateComponent/List";
    }
    else if (monitortype?.toLowerCase() === "netappsnapmirror" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("moniterType", monitortype)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("replicationType", replicationtype)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        window.location.href = "/Monitor/StorageNetAppSnapMirror/List";
    }
    else if (monitortype?.toLowerCase() === "hp3par" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("replicationType", replicationtype)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        sessionStorage.setItem("infraobjectName", infraobjectname)
        window.location.href = "/Monitor/HPE3PAR/List";
    }
    else if (monitortype?.toLowerCase() === "applicationnoreplication" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("replicationType", replicationtype)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        sessionStorage.setItem("infraobjectName", infraobjectname)
        window.location.href = "/Monitor/InfraObjectApplication/List";

    }
    else if (monitortype?.toLowerCase() === "powermax" && (monitorid || monitorstatus)) {
        sessionStorage.setItem("monitorId", monitorid)
        sessionStorage.setItem("infraobjectId", infraobjectid)
        sessionStorage.setItem("replicationType", replicationtype)
        sessionStorage.setItem("moniterStatus", monitorstatus)
        sessionStorage.setItem("infraobjectName", infraobjectname)
        window.location.href = "/Monitor/PowerMax/List";
    }
    else {

        notificationAlert('warning', 'Monitor Service not initiated for this infraObject')
    }


    //            }
    //            else {
    //                window.location.href = "/Monitor/MonitoringDashboard/List";
    //            }
    //        }

    //})
    //window.location.href = "/Monitor/MonitoringDashboard/List";

}

let infraObjectId;
let infraObjectName;
let moniterType;
let datavalue;
let rpo;

async function GetInfraObjectData(data) {

    infraObjectId = data?.dataset?.infraId || ''
    infraObjectName = data?.dataset?.infraName || ''
    moniterType = data?.dataset?.moniterType || ''
    datavalue = data?.dataset?.infraValue || ''
    rpo = data?.dataset?.infraRpo || ''
    let mode = '';


    if (moniterType?.toLowerCase() === 'oraclerac' || moniterType?.toLowerCase()?.includes("oracle-rac") || moniterType?.toLowerCase()?.includes("oraclerac")) {

        $('#nodeName').trigger('change', [infraObjectId, infraObjectName, moniterType]);
        mode = $(document).find('#nodeName').find(':selected').text();
    }


    $(".infraobjectRandom").removeClass('Active-Card')
    $("#" + infraObjectId).addClass('Active-Card')
    if (globalInfraObjectId !== infraObjectId || (infraByInterval && moniterType)) {
        $('#nodeRelationCont').empty()
        GetInfraObjectId(infraObjectId, infraObjectName, moniterType)
        Resiliencechart(infraObjectId, infraObjectName, datavalue, rpo, moniterType)
        GetSolutionInfraObjectId(infraObjectId, infraObjectName, moniterType, mode)
        hand.disabled = false;
    }

    infraByInterval = false
}


$(document).on('change', '#nodeName', function () {

    let mode = $('#nodeName').find(':selected').text();
    if (moniterType === undefined) {
        moniterType = "oraclerac"
    }

    GetSolutionInfraObjectId(globalInfraObjectId, infraObjectName, moniterType, mode);

});

function Resiliencechart(infraObjectId, infraObjectName, datalagValue, configuredRPO, moniterType,currentRPO,rpoThreshold) {

    //$("#Resilience_infraobject").empty()
    //$("#Resilience_Health").empty()

    $.ajax({
        url: "/Dashboard/ITResiliencyView/GetITViewLogByInfraObjectId/",
        method: 'GET',
        data: {
            infraObjectId: infraObjectId,
        },
        dataType: 'json',
        async: true,
        success: function (result) {
            let data = result.data

            if (result.success) {

                setTimeout(() => {
                    ResilienceHealthChartdata(infraObjectId, infraObjectName, datalagValue, configuredRPO, data, moniterType, currentRPO, rpoThreshold)
                }, 100)

            }
        },
        error: function (result) {
            errorNotification(result)
        }
    });

}


function GetInfraObjectId(infraObjectId, infraObjectName, moniterType) {

    $('#nodeRelationCont').empty()
    globalInfraObjectId = infraObjectId
    $.ajax({
        url: "/Dashboard/ITResiliencyView/GetITViewByInfraObjectId/",
        method: 'GET',
        data: {
            infraObjectId: infraObjectId,
        },
        dataType: 'json',
        async: true,
        success: function (result) {
            let data = result?.data
            //$('#infraobjectalldata').empty()
            //notificationAlert("success", result.message)
            updateInfraObjects(result.data, infraObjectName, moniterType, infraObjectId);
            Resiliencechart(infraObjectId, infraObjectName, data?.dataLagValue, data?.configuredRPO, moniterType, data?.currentRPO, data?.rpoThreshold);

        },
        error: function (result) {
            errorNotification(result)
        }
    });


}

async function GetSolutionInfraObjectId(infraObjectId, moniterType) {

    await $.ajax({
        url: "/Dashboard/ITResiliencyView/GetInfraObjectDetailsById/",
        method: 'GET',
        data: {
            infraObjectId: infraObjectId,
        },
        dataType: 'json',
        success: async function (res) {
            if (res.success) {

                await updateSolutionInfraObjects(res.data, moniterType);
            } else {
                errorNotification(res)
            }



        },

    });


}


async function updateInfraObjects(value, infraObjectName, moniterType, infraObjectId) {

    $('#nodeRelationCont').empty()
    //$("#infraobjectalldata").empty();
    //$('#chartdata').empty()

    let currentRPO = (value?.currentRPO !== "NA" && value?.currentRPO !== "0" && value?.currentRPO !== "" && value?.currentRPO !== null && value?.currentRPO !== undefined) ? `${value?.currentRPO}` : 'NA'
    var rporesult = "";

    var rpodays = "";

    if (currentRPO?.includes(".")) {
        var componentsRPO = currentRPO?.split('.');
        rpodays = parseInt(componentsRPO[0]);
        var remainingTime = componentsRPO[1];
        rporesult = remainingTime?.split(':');
    }
    else if (currentRPO?.includes("+")) {
        var componentsRTO = currentRPO?.split(' ');
        var day1 = componentsRTO[0];

        rpodays = parseInt(day1?.slice(1, 3));
        rporesult = componentsRTO[1]?.split(":");

    }
    else {
        rpodays = 0;
        rporesult = currentRPO?.split(":");
    }


    let currentRTO = (value?.currentRTO !== "NA" && value?.currentRTO !== "0" && value?.currentRTO !== "" && value?.currentRTO !== null && value?.currentRTO !== undefined) ? `${value?.currentRTO}` : 'NA'
    var rtoresult = "";
    var rtodays = "";

    if (currentRTO?.includes(".")) {
        var componentsRPO = currentRTO?.split('.');
        rtodays = parseInt(componentsRPO[0]);
        var remainingTime = componentsRPO[1];
        rtoresult = remainingTime.split(':');
    }
    else if (currentRTO?.includes("+")) {
        var componentsRTO = currentRTO?.split(' ');
        var day1 = componentsRTO[0];

        rtodays = parseInt(day1?.slice(1, 3));
        rtoresult = componentsRTO[1]?.split(":");

    }
    else {
        rtodays = 0;
        rtoresult = currentRTO?.split(":");
    }


    let lastComputedRPO = (value?.rpoGeneratedDate !== "0" && value?.rpoGeneratedDate !== "" && value?.rpoGeneratedDate !== null && value?.rpoGeneratedDate !== undefined) ? `${value?.rpoGeneratedDate}` : 'NA'
    let lastComputedRTO = (value?.rtoGeneratedDate !== "0" && value?.rtoGeneratedDate !== "" && value?.rtoGeneratedDate !== null && value?.rtoGeneratedDate !== undefined) ? `${value?.rtoGeneratedDate}` : 'NA'

    let rpodiv =
        '<div>' +
        '<span class="fw-semibold me-1">' + rpodays + '</span><small class="text-light fs-9">Day</small>' +
        '</div> ' +
        '<div>' +
        '<span class="fw-semibold me-1">' + rporesult[0] + '</span><small class="text-light fs-9">Hour</small>' +
        '</div> ' +
        '<div>' +
        '<span class="fw-semibold me-1">' + rporesult[1] + '</span><small class="text-light fs-9">Min</small>' +
        '</div> ' +
        '<div>' +
        '<span class="fw-semibold me-1">' + rporesult[2] + '</span><small class="text-light fs-9">Sec</small>' +
        '</div> '
    let rtodiv =
        '<div>' +
        '<span class="fw-semibold me-1">' + rtodays + '</span><small class="text-light fs-9">Day</small>' +
        '</div > ' +
        '<div>' +
        '<span class="fw-semibold me-1">' + rtoresult[0] + '</span><small class="text-light fs-9">Hour</small>' +
        '</div > ' +
        '<div>' +
        '<span class="fw-semibold me-1">' + rtoresult[1] + '</span><small class="text-light fs-9">Min</small>' +
        '</div > ' +
        '<div>' +
        '<span class="fw-semibold me-1">' + rtoresult[2] + '</span><small class="text-light fs-9">Sec</small>' +
        '</div > '
    //let chartDataDiv =
    //    '<div class="">' +
    //    '<div class="d-flex justify-content-between">' +
    //    '<div class="d-grid">' +
    //    '<div class="d-grid align-items-center text-start">' +
    //    '<h6 class="fs-7 fw-semibold mb-1"><i class="cp-active-inactive me-1 text-primary"></i>RPO</h6>' +
    //    '<div class="d-grid" style="height:fit-content;">' +
    //    '<div class="d-flex align-items-center gap-2" id="rpoformat">' +

    //    '</div> ' +
    //    '<small class="text-light border-bottom border-light-subtle mt-1">Last&nbsp;Computed&nbsp;RPO</small>' +
    //    '</div > ' +
    //    '</div > ' +
    //    '<div class="d-grid align-items-center mt-2 text-start">' +
    //    '<span id="time" class="align-middle fw-semibold" cursorshover="true">' + lastComputedRPO + '</span>' +
    //    '<small class="text-light">Last Monitoring Time</small>' +

        //'</div > ' +
        //'</div > ' +
        //'<div class="w-50" id="RPOSummary" style="height:120px;"></div>' +
        //'</div>' +
        //'</div>' +
        //'<hr class="my-2" style="border-color:var(--bs-gray-500);"/>' +
        //'<div class="">' +
        //'<div class="d-flex justify-content-between">' +
        //'<div class="d-grid">' +
        //'<div class="d-grid align-items-center text-start">' +
        //'<h6 class="fs-7 fw-semibold mb-1"><i class="cp-active-inactive me-1 text-primary"></i>RTO</h6>' +
        //'<div class="d-grid"  style="height:fit-content;">' +
        //'<div class="d-flex align-items-center gap-2" id="rtoformat">' +

        //'</div > ' +
        //'<small class="text-light border-bottom border-light-subtle pb-1">Last&nbsp;Computed&nbsp;RTO</small>' +
        //'</div > ' +
        //'</div > ' +
        //'<div class="d-grid align-items-center mt-2 text-start">' +
        //'<span id="time" class="align-middle fw-semibold" cursorshover="true">' + lastComputedRTO + '</span>' +
        //'<small class="text-light">Last DR Drill Execution</small>' +

        //'</div > ' +
        //'</div > ' +
        //'<div class="w-50" id="RTOSummary" style="height:120px;"></div>' +
        //'</div>' +
        //'</div>'

    if (value?.hasOwnProperty('id')) {
        //('#chartdata').html(chartDataDiv)
        $('#chartdata').removeClass('d-none')
        $('#chartNotFound').addClass('d-none')

        $('#timeRPO').html(lastComputedRPO)
        $('#timeRTO').html(lastComputedRTO)

        RpoSummaray(value)
        RtoSummary(value)

    }
    else {
        rpoChart = ''
        rtoChart = ''
        $('#chartdata').addClass('d-none')
        $('#chartNotFound').css('text-align', 'center').css('height', '100%').removeClass('d-none')
    }
    if (currentRPO !== "NA" && currentRPO !== "") {
        $('#rpoformat').html(rpodiv);

    } else {
        $('#rpoformat').html('<div class="fw-semibold me-1 flex-fill">' + "NA" + '</div>');
    }
    if (currentRTO !== "NA" && currentRTO !== "") {
        $('#rtoformat').html(rtodiv);
    } else {
        $('#rtoformat').html('<div class="fw-semibold me-1 flex-fill">' + "NA" + '</div>');
    }

    let parsedData;
    const prop = value?.properties;

    if (prop)
        parsedData = JSON.parse(prop);


    if (value?.properties !== "" && value?.properties !== null) {
        $("#home-tab").addClass("active")
        $("#profile-tab").removeClass("active")
        // if (!value.hasOwnProperty('id') || (moniterType == "" || !["Oracle", "MssqlAlwaysOn", "OracleRac", "Postgres", "Mysql", "DB2HADR", "MongoDB", "mssqldbmirroring", "SVC"].includes(moniterType))) {
        $('#replicationMonitorContainer').show();

        monitorTypeMysql(value, infraObjectName, moniterType, parsedData);
        monitorTypeOdg(value, infraObjectName, moniterType, parsedData);
        monitorTypeOracleRac(value, infraObjectName, moniterType, parsedData);
        monitorTypeAlwaysOn(value, infraObjectName, moniterType, parsedData);
        monitorTypePostgres(value, infraObjectName, moniterType, parsedData);
        monitorTypeMssqlDBMirroring(value, infraObjectName, moniterType, parsedData);
        monitorTypeMongoDB(value, infraObjectName, moniterType, parsedData);
        monitorTypeIBMSVCGM(infraObjectName, moniterType, parsedData);
        monitorTypeDB2HADR(value, infraObjectName, moniterType, parsedData);
        monitorTypeMssqlNLS(value, infraObjectName, moniterType, parsedData);
        monitorTypeHyperV(value, infraObjectName, moniterType, parsedData);
        monitorTypeZertoVpg(value, infraObjectName, moniterType, parsedData);
        monitorTypeOCP(value, infraObjectName, moniterType, parsedData);
        monitorTypeRPforVM(value, infraObjectName, moniterType, parsedData);
        monitorTypeSRM(value, infraObjectName, moniterType, parsedData);
    }
    else {

        monitorTypeConfigured(moniterType, infraObjectId, urls)

    }
}

async function updateSolutionInfraObjects(infraObjectData, moniterType) {
    
    itViewSolutionDiagram(infraObjectData, moniterType)
    //Start- Restrict unneccesary dashIn movment
    const stopInitialAnimation = () => {
        const group = $('.amcharts-ForceDirectedLink-group');
        if (group.length > 0) {
            group.css('animation', 'none');
        } else {
            setTimeout(stopInitialAnimation, 50);
        }
    };
    stopInitialAnimation();
    //End
    setTimeout(() => {
        if (moniterType?.toLowerCase()?.includes("srm") || moniterType?.toLowerCase() === "openshift") {
            if (moniterType === "" || moniterType === null || moniterType === undefined || infraObjectData?.state?.toLowerCase() === "maintenance" || infraObjectData?.serverDto[0]?.status?.toLowerCase() !== "up" || infraObjectData?.serverDto[1]?.status?.toLowerCase() !== "up") {

                $('.amcharts-ForceDirectedLink-group').css('animation', 'am-dashesIn 1s  linear infinite')
            }
            else if (infraObjectData?.drOperationStatus == 2) {

                $('.amcharts-ForceDirectedLink-group').css('animation', 'am-moving-dashesIn 1s reverse linear infinite')
            }
            else {
                $('.amcharts-ForceDirectedLink-group').css('animation', 'am-moving-dashesIn 1s linear infinite')
            }
        }
        else {
            if (infraObjectData?.databaseDto?.length > 0) {
                if (moniterType === "" || moniterType === null || moniterType === undefined || infraObjectData?.state?.toLowerCase() === "maintenance" || infraObjectData?.serverDto[0]?.status?.toLowerCase() !== "up" || infraObjectData?.serverDto[1]?.status?.toLowerCase() !== "up" || infraObjectData?.databaseDto[0]?.status?.toLowerCase() !== "up" || infraObjectData?.databaseDto[1]?.status?.toLowerCase() !== "up") {

                    $('.amcharts-ForceDirectedLink-group').css('animation', 'am-dashesIn 1s  linear infinite')
                }

                else if (infraObjectData?.drOperationStatus == 2) {

                    $('.amcharts-ForceDirectedLink-group').css('animation', 'am-moving-dashesIn 1s reverse linear infinite')
                }
                else {
                    $('.amcharts-ForceDirectedLink-group').css('animation', 'am-moving-dashesIn 1s linear infinite')
                }
            } else {
                if (moniterType === "" || moniterType === null || moniterType === undefined || infraObjectData?.state?.toLowerCase() === "maintenance" || infraObjectData?.serverDto[0]?.status?.toLowerCase() !== "up" || infraObjectData?.serverDto[1]?.status?.toLowerCase() !== "up") {

                    $('.amcharts-ForceDirectedLink-group').css('animation', 'am-dashesIn 1s  linear infinite')
                } else if (infraObjectData?.drOperationStatus == 2) {

                    $('.amcharts-ForceDirectedLink-group').css('animation', 'am-moving-dashesIn 1s reverse linear infinite')
                }
                else {
                    $('.amcharts-ForceDirectedLink-group').css('animation', 'am-moving-dashesIn 1s linear infinite')
                }
            }
        }

    }, 300)
}


$(document).ajaxSend(function (e, jqXHR) {
    ongoingAjax.push(jqXHR);
});


$(function () {
    function checkInfraPeriodically() {
        if ($('.infraobjectRandom.Active-Card')?.length) {
            infraByInterval = true;

            ongoingAjax?.forEach(req => {
                if (req.readyState !== 4) req.abort();
            });
            ongoingAjax = [];

            $('.infraobjectRandom.Active-Card')?.find('.particularInfra')?.trigger('click');

            setTimeout(checkInfraPeriodically, 40000);
        }
    }

    setTimeout(checkInfraPeriodically, 40000);
});


