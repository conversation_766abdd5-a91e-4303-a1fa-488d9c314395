using ContinuityPatrol.Application.Features.AlertMaster.Commands.Create;
using ContinuityPatrol.Application.Features.AlertMaster.Commands.Update;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.AlertMasterModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class AlertMasterFixture
{
    public List<AlertMasterListVm> AlertMasterListVm { get; }
    public AlertMasterDetailVm AlertMasterDetailVm { get; }
    public CreateAlertMasterCommand CreateAlertMasterCommand { get; }
    public UpdateAlertMasterCommand UpdateAlertMasterCommand { get; }

    public AlertMasterFixture()
    {
        var fixture = new Fixture();

        // Create sample AlertMaster list data
        AlertMasterListVm = new List<AlertMasterListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                AlertId = "ALERT_001",
                AlertType = 1,
                AlertMessage = "Critical database connection failure detected",
                RecoveryId = Guid.NewGuid().ToString(),
                AlertPriority = "High",
                AlertFrequency = 5,
                AlertSendTime = "09:00:00",
                IsSendEmail = true,
                IsSendSMS = true,
                AlertName = "Critical Database Alert",
                SmSMessage = "DB connection failed - immediate attention required",
                EscMatId = Guid.NewGuid().ToString(),
                IsAcknowledgement = false,
                ExceptionId = Guid.NewGuid().ToString(),
                IsAlertActive = true,
                DBType = 1,
                IsFullDB = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                AlertId = "ALERT_002",
                AlertType = 2,
                AlertMessage = "High CPU usage warning",
                RecoveryId = Guid.NewGuid().ToString(),
                AlertPriority = "Medium",
                AlertFrequency = 10,
                AlertSendTime = "10:30:00",
                IsSendEmail = true,
                IsSendSMS = false,
                AlertName = "CPU Usage Warning",
                SmSMessage = "CPU usage above threshold",
                EscMatId = Guid.NewGuid().ToString(),
                IsAcknowledgement = true,
                ExceptionId = Guid.NewGuid().ToString(),
                IsAlertActive = true,
                DBType = 2,
                IsFullDB = false
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                AlertId = "ALERT_003",
                AlertType = 3,
                AlertMessage = "Backup process completed successfully",
                RecoveryId = Guid.NewGuid().ToString(),
                AlertPriority = "Low",
                AlertFrequency = 1,
                AlertSendTime = "02:00:00",
                IsSendEmail = true,
                IsSendSMS = false,
                AlertName = "Backup Success Alert",
                SmSMessage = "Backup completed",
                EscMatId = Guid.NewGuid().ToString(),
                IsAcknowledgement = false,
                ExceptionId = Guid.NewGuid().ToString(),
                IsAlertActive = true,
                DBType = 1,
                IsFullDB = true
            }
        };

        // Create detailed AlertMaster data
        AlertMasterDetailVm = new AlertMasterDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            AlertId = "ALERT_001",
            AlertType = 1,
            AlertMessage = "Critical database connection failure detected",
            RecoveryId = Guid.NewGuid().ToString(),
            AlertPriority = "High",
            AlertFrequency = 5,
            AlertSendTime = "09:00:00",
            IsSendEmail = true,
            IsSendSMS = true,
            SmSMessage = "DB connection failed - immediate attention required",
            AlertName = "Critical Database Alert",
            EscMatId = Guid.NewGuid().ToString(),
            IsAcknowledgement = false,
            ExceptionId = Guid.NewGuid().ToString(),
            IsAlertActive = true,
            DBType = 1,
            IsFullDB = true
        };

        // Create command for creating AlertMaster
        CreateAlertMasterCommand = new CreateAlertMasterCommand
        {
            AlertId = "ALERT_004",
            AlertType = 2,
            AlertMessage = "New alert for testing purposes",
            RecoveryId = Guid.NewGuid().ToString(),
            AlertPriority = "Medium",
            AlertFrequency = 15,
            AlertSendTime = "14:00:00",
            IsSendEmail = true,
            IsSendSMS = false,
            SmSMessage = "Test alert message",
            AlertName = "Test Alert",
            EscMatId = Guid.NewGuid().ToString(),
            ExceptionId = Guid.NewGuid().ToString(),
            IsAlertActive = true,
            DBType = 2,
            IsFullDB = false
        };

        // Create command for updating AlertMaster
        UpdateAlertMasterCommand = new UpdateAlertMasterCommand
        {
            Id = Guid.NewGuid().ToString(),
            AlertId = "ALERT_005",
            AlertType = 3,
            AlertMessage = "Updated alert message for testing",
            RecoveryId = Guid.NewGuid().ToString(),
            AlertPriority = "Low",
            AlertFrequency = 30,
            AlertSendTime = "16:00:00",
            IsSendEmail = false,
            IsSendSMS = true,
            SmSMessage = "Updated SMS message",
            AlertName = "Updated Test Alert",
            EscMatId = Guid.NewGuid().ToString(),
            IsAcknowledgement = true,
            ExceptionId = Guid.NewGuid().ToString(),
            IsAlertActive = false,
            DBType = 3,
            IsFullDB = true
        };
    }
}
