﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="">
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title">
                        <i class="cp-BIA-template"></i>
                        <span>FIA Templates List</span>
                    </h6>
                </div>
                <form class="d-flex align-items-center">
                    <div class="input-group me-2 w-auto">
                        <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                        <div class="input-group-text">
                            <div class="dropdown">
                                <span data-bs-toggle="dropdown" title="Filter">
                                    <i class="cp-filter"></i>
                                </span>
                                <ul class="dropdown-menu filter-dropdown">
                                    <li>
                                        <h6 class="dropdown-header">Filter Search</h6>
                                    </li>
                                    <li class="dropdown-item">
                                        <div>
                                            <input class="form-check-input" type="checkbox" value="name=" id="Name">
                                            <label class="form-check-label" for="Name">
                                                Template Name
                                            </label>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" id="createmodal" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button>
                </form>
            </div>
        </div>
        <div class="card-body pt-0">
            <table class="table table-hover dataTable" style="width:100%" id="overallTable_List">
                <thead>
                    <tr>
                        <th>Sr.No</th>
                        <th>Template Name</th>
                        <th>Username</th>
                        <th>Template In Used</th>
                        <th>Template Used By</th>
                        <th>Created Date</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
    <!-- Modal -->
    <div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <form class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-BIA-template"></i><span>FIA Templates Configuration</span></h6>
                    <button type="button" title="Close" class="btn-close fia_template_cancel" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="min-height:25rem ">
                    <div class="form-group">
                        <div class="form-label">Template Name</div>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="cp-name"></i>
                            </span>
                            <input autocomplete="off" class="form-control" type="text" id="fia_template_name" placeholder="Enter Template Name" maxlength="100">
                        </div>
                        <span id="fia_template_name_error"></span>
                    </div>
                    <div class="d-flex gap-2 align-items-center">
                        <div class="form-group w-100">
                            <div class="form-label">Impact Category</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-Impact"></i></span>
                                <select class="form-select-modal" id="fia_impact_category" data-placeholder="Select Impact Category" multiple>
                                    <option></option>
                                </select>
                            </div>
                            <span id="fia_impact_category_error"></span>
                        </div>
                        <span type="button" data-bs-target="#ImpactCategoryModalToggle" title="Add Impact Catagory" data-bs-toggle="modal" id="ImpactTypeModal"><i class="cp-circle-plus text-primary"></i></span>
                    </div>
                    <div class="d-flex gap-2 align-items-center">
                        <div class="form-group w-100">
                            <div class="form-label">Impact Type</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-activity-type"></i></span>
                                <select class="form-select-modal mb-0" id="fia_impact_type" data-placeholder="Select Impact Type" multiple>
                                    <option></option>
                                </select>
                            </div>
                            <span id="fia_impact_type_error"></span>
                        </div>
                        <span type="button" data-bs-target="#ManageImpactCategoryModalToggle" title="Add Impact Type" id="ImpactMasterModal" data-bs-toggle="modal"><i class="cp-circle-plus text-primary"></i></span>
                    </div>
                    <div class="d-flex gap-2 align-items-center">
                        <div class="form-group w-100">
                            <div class="form-label">Interval</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-calendar"></i></span>
                                <select class="form-select-modal" id="fia_interval" data-placeholder=" Select Interval" multiple>
                                    <option></option>
                                </select>
                            </div>
                            <span id="fia_interval_error"></span>
                        </div>
                        <span type="button" data-bs-target="#TimeIntervalModalToggle1" title="Add Interval" id="TimeIntervalModal" data-bs-toggle="modal">
                            <i class="cp-circle-plus text-primary"></i>
                        </span>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm fia_template_cancel overall_cancel" data-bs-dismiss="modal" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="fia_template_save">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    @* Impact Category Modal Start *@
    <div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="ImpactCategoryModalToggle" aria-hidden="true" aria-labelledby="ImpactCategoryModalToggle" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-Impact"></i><span>Continuity Patrol :: Impact Category</span></h6>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Impact Category</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-Impact"></i>
                                    </span>
                                    <input autocomplete="off" class="form-control" maxlength="100" type="text" placeholder="Enter Impact Category" id="impactcatagory">
                                </div>
                                <span id="impactcatagory_error"></span>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Impact Category Description (Optional)</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-description"></i>
                                    </span>
                                    <input autocomplete="off" maxlength="500" class="form-control" type="text" placeholder="Enter Category Description" id="impactDescription">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <table class="table table-hover dataTable" style="width:100%;table-layout:fixed" id="TimeImpacttypetable">
                            <thead>
                                <tr>
                                    <th>Sr.No</th>
                                    <th>Impact Category Name</th>
                                    <th>Impact Category Description</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm overall_cancel" data-bs-target="#CreateModal" data-bs-toggle="modal" title="" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" title="" id="impacttype_btnsave">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @* Impact Category Modal End *@
    @*Continuity Patrol :: Manage Impact Category To Impact Type Relationship Start *@
    <div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="ManageImpactCategoryModalToggle" aria-hidden="true" aria-labelledby="ImpactCategoryModalToggle" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-activity-type"></i><span>Continuity Patrol :: Manage Impact Category To Impact Type Relationship</span></h6>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12 col-md-12">
                            <div class="form-group">
                                <div class="form-label">Impact Category</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-Impact"></i></span>
                                    <select class="form-select-modal" data-placeholder="Select Impact Category" id="Impact_catagory">
                                        <option></option>
                                    </select>
                                </div>
                                <span id="Impact_catagory_error"></span>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Impact Type</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-name"></i>
                                    </span>
                                    <input autocomplete="off" maxlength="100" class="form-control" type="text" placeholder="Enter Impact Type" id="Impact_type">
                                </div>
                                <span id="Impact_type_error"></span>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Impact Type Description (Optional)</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-description"></i>
                                    </span>
                                    <input autocomplete="off" class="form-control" maxlength="500" type="text" placeholder="Enter Impact Type Description" id="Impact_description">
                                </div>
                                <span id="Impact_description_error"></span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <table class="table table-hover dataTable" id="TimeImpactcatagorytable" style="width:100%;table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>Sr.No</th>
                                    <th>Impact Type</th>
                                    <th>Impact Type Description</th>
                                    <th>Impact Category</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>  </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm overall_cancel" data-bs-target="#CreateModal" data-bs-toggle="modal" title="" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" title="" id="ImpactMastersaveBtn">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @* Continuity Patrol :: Manage Impact Category To Impact Type Relationship End *@
    @* Continuity Patrol :: Time Interval Modal Start *@
    <div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="TimeIntervalModalToggle1" aria-hidden="true" aria-labelledby="ImpactCategoryModalToggle" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel ">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-calendar"></i><span>Continuity Patrol :: Time Interval</span></h6>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Start Time Interval</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-apply-finish-time"></i>
                                    </span>
                                    <input autocomplete="off" class="form-control" placeholder="Enter start time interval" type="number" id="MinTime">
                                </div>
                                <span id="MinTime-error"></span>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Hour or Days</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-calendar"></i></span>
                                    <select class="form-select-modal" data-placeholder="Select Time" id="MinTimeUnit">
                                        <option value=""></option>
                                        <option value="Hours">Hours</option>
                                        <option value="Days">Days</option>
                                    </select>
                                </div>
                                <span id="MinTimeUnit-error"></span>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">End Time Interval</div>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-apply-finish-time"></i>
                                    </span>
                                    <input autocomplete="off" class="form-control" placeholder="Enter end time interval" type="number" id="MaxTime">
                                </div>
                                <span id="MaxTime-error"></span>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <div class="form-label">Hour or Days</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-calendar"></i></span>
                                    <select class="form-select-modal" data-placeholder="Select Time" id="MaxTimeUnit">
                                        <option value=""></option>
                                        <option value="Hours">Hours</option>
                                        <option value="Days">Days</option>
                                    </select>
                                </div>
                                <span id="MaxTimeUnit-error"></span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <table class="table table-hover dataTable" id="timeintevaltable" style="width:100%">
                            <thead>
                                <tr>
                                    <th colspan="2">Time Interval</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm overall_cancel" id="TimeintervalcancelBtn" data-bs-target="#CreateModal" data-bs-toggle="modal" title="" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="TimeintervalsaveBtn" title="">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="ConfigurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true"></div>
    <div id="ConfigurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true"></div>
    @* Continuity Patrol :: Time Interval End *@
    <!--Modal Reject-->
    <div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">
                            You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="deleted_id"></span>
                            data?
                        </p>
                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-target="#TimeIntervalModalToggle1" data-bs-toggle="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="confirmDeleteButton" data-bs-target="#TimeIntervalModalToggle1" data-bs-toggle="modal">Yes</button>
                </div>
            </div>
        </div>
    </div>
    <!--Modal Reject1-->
    <div class="modal fade" data-bs-backdrop="static" id="impacttypeDeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">
                            You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="impacttype_deleted_id"></span>
                            data?
                        </p>
                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-target="#ImpactCategoryModalToggle" data-bs-toggle="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="impacttype_confirmDeleteButton" data-bs-target="#ImpactCategoryModalToggle" data-bs-toggle="modal">Yes</button>
                </div>
            </div>
        </div>
    </div>
    <!--Modal Reject2-->
    <div class="modal fade" data-bs-backdrop="static" id="impactmasterDeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">
                            You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="impactmaster_deleted_id"></span>
                            data?
                        </p>
                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-target="#ManageImpactCategoryModalToggle" data-bs-toggle="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="impactmaster_confirmDeleteButton" data-bs-target="#ManageImpactCategoryModalToggle" data-bs-toggle="modal">Yes</button>
                </div>
            </div>
        </div>
    </div>
    <!--Modal Reject3-->
    <div class="modal fade" data-bs-backdrop="static" id="overall_delete" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">
                            You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="overall_deleted_id"></span>
                            data?
                        </p>
                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="#overallTable_List" data-bs-toggle="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="overall_confirmDeleteButton">Yes</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Configuration/FIA-BIA/FIA Templates/fiaTemplate.js"></script>
