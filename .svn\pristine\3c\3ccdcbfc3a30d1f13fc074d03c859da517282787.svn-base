﻿using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Application.Models;

namespace ContinuityPatrol.Shared.Services.Helper;

public class WebHelper
{
    private static IHttpContextAccessor _httpContextAccessor;
    public static HttpContext Current => _httpContextAccessor?.HttpContext;

    private static UserSession _userSession;
    public static UserSession UserSession
    {
        get
        {
            _userSession = Current.Session.Get<UserSession>("SESSION");

            return _userSession ??= new UserSession();
        } 
        set
        {
            if (Current == null) return;

            if (value == null)
            {
                Current.Session.Remove("SESSION");
            }
            else
            {
                Current.Session.Set("SESSION", value);
            }
        }
    }
     
    public static void Configure(IHttpContextAccessor contextAccessor)
    {
        _httpContextAccessor = contextAccessor;
    }

    public static class CurrentSession
    {
        public static string Id => Current?.Session.Id;

        public static bool IsAuthenticated = Current?.User.Identity?.IsAuthenticated ?? false;

        [DebuggerStepThrough]
        public static void Clear()
        {
            Current?.Session.Clear();
        }

        [DebuggerStepThrough]
        public static T Get<T>(string key) where T : class
        {
            return Current.Session.Get<T>(BuildFullKey(key));
        }

        [DebuggerStepThrough]
        public static void Set(string key, object value)
        {
            if (Current == null) return;

            if (value == null)
            {
                Current.Session.Remove(BuildFullKey(key));
            }
            else
            {
                Current.Session.Set(BuildFullKey(key), value);
            }

        }

        public static Exception GetException(string key)
        {
            return Current?.Session.Get<Exception>(BuildFullKey(key));
        }

        public static void SetException(string key, Exception value)
        {
            if (Current == null) return;

            if (value == null)
            {
                Current.Session.Remove(BuildFullKey(key));
            }
            else
            {
                Current.Session.Set(BuildFullKey(key), value);
            }
        }

        [DebuggerStepThrough]
        public static void Remove(string key)
        {
            if (Current != null)
            {
                Current.Session.Remove(BuildFullKey(key));
            }
        }
        
        [DebuggerStepThrough]
        private static string BuildFullKey(string localKey)
        {
            const string sessionKey = "Web.UI.";

            return localKey.IndexOf(sessionKey, StringComparison.Ordinal) > -1 ? localKey : sessionKey + localKey;
        }

        [DebuggerStepThrough]
        public static string GetString(string key)
        {
            var fullKey = BuildFullKey(key);

            return Current != null ? Current.Session.GetString(fullKey) : string.Empty;
        }

        public static class CommonContent
        {
            public static string Message
            {
                get => GetString("UserMessage");
                set => Set("UserMessage", value);
            }
        }

        // ReSharper disable once InconsistentNaming
        public static class CPExceptionInfo
        {
            public static string Message
            {
                get => GetException("UserMessage").Message;
                set => Set("UserMessage", value);
            }
        }

        public static class Permissions
        {
            public static class Configuration
            {
                public static bool View
                {
                    get => Convert.ToBoolean(Get<string>("Configuration_View"));
                    set => Set("Configuration_View", value);
                }
                public static bool Create
                {
                    get => Convert.ToBoolean(Get<string>("Configuration_Create"));
                    set => Set("Configuration_Create", value);
                }
                public static bool Edit
                {
                    get => Convert.ToBoolean(Get<string>("Configuration_Edit"));
                    set => Set("Configuration_Edit", value);
                }

                public static bool Delete
                {
                    get => Convert.ToBoolean(Get<string>("Configuration_Delete"));
                    set => Set("Configuration_Delete", value);
                }
                public static bool CreateAndEdit
                {
                    get => Convert.ToBoolean(Get<string>("Configuration_CreateAndEdit"));
                    set => Set("Configuration_CreateAndEdit", value);
                }
            }
            public static class Admin
            {
                public static bool View
                {
                    get => Convert.ToBoolean(Get<string>("Admin_View"));
                    set => Set("Admin_View", value);
                }
                public static bool Create
                {
                    get => Convert.ToBoolean(Get<string>("Admin_Create"));
                    set => Set("Admin_Create", value);
                }
                public static bool Edit
                {
                    get => Convert.ToBoolean(Get<string>("Admin_Edit"));
                    set => Set("Admin_Edit", value);
                }

                public static bool Delete
                {
                    get => Convert.ToBoolean(Get<string>("Admin_Delete"));
                    set => Set("Admin_Delete", value);
                }
                public static bool CreateAndEdit
                {
                    get => Convert.ToBoolean(Get<string>("Admin_CreateAndEdit"));
                    set => Set("Admin_CreateAndEdit", value);
                }
            }
            public static class Orchestration
            {
                public static bool View
                {
                    get => Convert.ToBoolean(Get<string>("Orchestration_View"));
                    set => Set("Orchestration_View", value);
                }
                public static bool Create
                {
                    get => Convert.ToBoolean(Get<string>("Orchestration_Create"));
                    set => Set("Orchestration_Create", value);
                }
                public static bool Edit
                {
                    get => Convert.ToBoolean(Get<string>("Orchestration_Edit"));
                    set => Set("Orchestration_Edit", value);
                }

                public static bool Delete
                {
                    get => Convert.ToBoolean(Get<string>("Orchestration_Delete"));
                    set => Set("Orchestration_Delete", value);
                }
                public static bool Execute
                {
                    get => Convert.ToBoolean(Get<string>("Orchestration_Execute"));
                    set => Set("Orchestration_Execute", value);
                }
                public static bool CreateAndEdit
                {
                    get => Convert.ToBoolean(Get<string>("Orchestration_CreateAndEdit"));
                    set => Set("Orchestration_CreateAndEdit", value);
                }

            }
            public static class Reports
            {
                public static bool View
                {
                    get => Convert.ToBoolean(Get<string>("Reports_View"));
                    set => Set("Reports_View", value);
                }
                public static bool Create
                {
                    get => Convert.ToBoolean(Get<string>("Reports_Create"));
                    set => Set("Reports_Create", value);
                }
                public static bool Edit
                {
                    get => Convert.ToBoolean(Get<string>("Reports_Edit"));
                    set => Set("Reports_Edit", value);
                }

                public static bool Delete
                {
                    get => Convert.ToBoolean(Get<string>("Reports_Delete"));
                    set => Set("Reports_Delete", value);
                }
                public static bool CreateAndEdit
                {
                    get => Convert.ToBoolean(Get<string>("Reports_CreateAndEdit"));
                    set => Set("Reports_CreateAndEdit", value);
                }
            }
            public static class Manage
            {
                public static bool View
                {
                    get => Convert.ToBoolean(Get<string>("Manage_View"));
                    set => Set("Manage_View", value);
                }
                public static bool Create
                {
                    get => Convert.ToBoolean(Get<string>("Manage_Create"));
                    set => Set("Manage_Create", value);
                }
                public static bool Edit
                {
                    get => Convert.ToBoolean(Get<string>("Manage_Edit"));
                    set => Set("Manage_Edit", value);
                }

                public static bool Delete
                {
                    get => Convert.ToBoolean(Get<string>("Manage_Delete"));
                    set => Set("Manage_Delete", value);
                }
                public static bool CreateAndEdit
                {
                    get => Convert.ToBoolean(Get<string>("Manage_CreateAndEdit"));
                    set => Set("Manage_CreateAndEdit", value);
                }

            }
            public static class Dashboard
            {
                public static bool View
                {
                    get => Convert.ToBoolean(Get<string>("Dashboard_View"));
                    set => Set("Dashboard_View", value);
                }
                public static bool Monitor
                {
                    get => Convert.ToBoolean(Get<string>("Monitor_View"));
                    set => Set("Monitor_View", value);
                }
                public static bool Management
                {
                    get => Convert.ToBoolean(Get<string>("Dashboard_Management"));
                    set => Set("Dashboard_Management", value);
                }

            }

            public static class Alerts
            {
                public static bool View
                {
                    get => Convert.ToBoolean(Get<string>("Alerts_View"));
                    set => Set("Alerts_View", value);
                }
                public static bool Create
                {
                    get => Convert.ToBoolean(Get<string>("Alerts_Create"));
                    set => Set("Alerts_Create", value);
                }
                public static bool Edit
                {
                    get => Convert.ToBoolean(Get<string>("Alerts_Edit"));
                    set => Set("Alerts_Edit", value);
                }

                public static bool Delete
                {
                    get => Convert.ToBoolean(Get<string>("Alerts_Delete"));
                    set => Set("Alerts_Delete", value);
                }
                public static bool CreateAndEdit
                {
                    get => Convert.ToBoolean(Get<string>("Alerts_CreateAndEdit"));
                    set => Set("Alerts_CreateAndEdit", value);
                }
            }
            public static class Cyber
            {
                public static bool View
                {
                    get => Convert.ToBoolean(Get<string>("Cyber_View"));
                    set => Set("Cyber_View", value);
                }
                public static bool Create
                {
                    get => Convert.ToBoolean(Get<string>("Cyber_Create"));
                    set => Set("Cyber_Create", value);
                }
                public static bool Edit
                {
                    get => Convert.ToBoolean(Get<string>("Cyber_Edit"));
                    set => Set("Cyber_Edit", value);
                }

                public static bool Delete
                {
                    get => Convert.ToBoolean(Get<string>("Cyber_Delete"));
                    set => Set("Cyber_Delete", value);
                }
                public static bool CreateAndEdit
                {
                    get => Convert.ToBoolean(Get<string>("Cyber_CreateAndEdit"));
                    set => Set("Cyber_CreateAndEdit", value);
                }
            }

            public static class Drift
            {
                public static bool View
                {
                    get => Convert.ToBoolean(Get<string>("Drift_View"));
                    set => Set("Drift_View", value);
                }

                public static bool Create
                {
                    get => Convert.ToBoolean(Get<string>("Drift_Create"));
                    set => Set("Drift_Create", value);
                }

                public static bool Edit
                {
                    get => Convert.ToBoolean(Get<string>("Drift_Edit"));
                    set => Set("Drift_Edit", value);
                }

                public static bool Delete
                {
                    get => Convert.ToBoolean(Get<string>("Drift_Delete"));
                    set => Set("Drift_Delete", value);
                }

                public static bool CreateAndEdit
                {
                    get => Convert.ToBoolean(Get<string>("Drift_CreateAndEdit"));
                    set => Set("Drift_CreateAndEdit", value);
                }
            }
            public static class ResiliencyReadiness
            {
                public static bool View
                {
                    get => Convert.ToBoolean(Get<string>("ResiliencyReadiness_View"));
                    set => Set("ResiliencyReadiness_View", value);
                }
                public static bool Create
                {
                    get => Convert.ToBoolean(Get<string>("ResiliencyReadiness_Create"));
                    set => Set("ResiliencyReadiness_Create", value);
                }
                public static bool Edit
                {
                    get => Convert.ToBoolean(Get<string>("ResiliencyReadiness_Edit"));
                    set => Set("Cyber_Edit", value);
                }

                public static bool Delete
                {
                    get => Convert.ToBoolean(Get<string>("ResiliencyReadiness_Delete"));
                    set => Set("ResiliencyReadiness_Delete", value);
                }
                public static bool CreateAndEdit
                {
                    get => Convert.ToBoolean(Get<string>("ResiliencyReadiness_CreateAndEdit"));
                    set => Set("ResiliencyReadiness_CreateAndEdit", value);
                }
            }
            public static class CloudConnect
            {
                public static bool View
                {
                    get => Convert.ToBoolean(Get<string>("CloudConnect_View"));
                    set => Set("CloudConnect_View", value);
                }                
            }
        }
    }
}