﻿//namespace ContinuityPatrol.Application.Features.Alert.Events.HeatMapStatusEvents.Update;

//public class HeatMapStatusUpdatedEventHandler : INotificationHandler<HeatMapStatusUpdatedEvent>
//{
//    private readonly IHeatMapStatusRepository _heatMapStatusRepository;
//    private readonly ILogger<HeatMapStatusUpdatedEventHandler> _logger;

//    public HeatMapStatusUpdatedEventHandler(ILogger<HeatMapStatusUpdatedEventHandler> logger,
//        IHeatMapStatusRepository heatMapStatusRepository)
//    {
//        _heatMapStatusRepository = heatMapStatusRepository;
//        _logger = logger;
//    }

//    public async Task Handle(HeatMapStatusUpdatedEvent updatedEvent, CancellationToken cancellationToken)
//    {
//        var eventToUpdate = await _heatMapStatusRepository.GetHeatMapDetailByInfraObjectAndEntityId(
//            updatedEvent.InfraObjectId,
//            updatedEvent.EntityId);

//        eventToUpdate.InfraObjectId = updatedEvent.InfraObjectId;
//        eventToUpdate.InfraObjectName = updatedEvent.InfraObjectName;
//        eventToUpdate.EntityId = updatedEvent.EntityId;
//        eventToUpdate.HeatmapType = updatedEvent.HeatmapType;
//        eventToUpdate.ErrorMessage = updatedEvent.ErrorMessage;

//        await _heatMapStatusRepository.UpdateAsync(eventToUpdate);
//    }
//}