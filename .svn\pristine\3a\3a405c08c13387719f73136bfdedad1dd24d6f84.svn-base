﻿<div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-add"></i><span>Create Template</span></h6>
            <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label class="form-label">Template Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-name"></i></span>
                    <input type="text" class="form-control" placeholder="Enter Template Name" id="templateName" maxlength="100" />
                    <span class="input-group-text ps-1" data-bs-toggle="collapse" href="#collapseIcon" aria-expanded="false" title="Select Icon" aria-controls="collapseIcon"><i role="button" class="imageSubSelected cp-images" id="imageSubSelected"></i></span>
                </div>
                <span id="templatename-error"></span>
            </div>
            <div class="collapse mb-2" id="collapseIcon">
                <div class="form-label">Category Icon</div>
                <div class="Category_Icon">
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <td><i title="Cloud" class="cp-cloud custom-cursor-on-hover" cursorshover="true"></i></td>
                                <td><i title="Amazon" class="cp-amazon custom-cursor-on-hover"></i></td>
                                <td><i title="Softlayer" class="cp-java-soft-layers custom-cursor-on-hover"></i></td>
                                <td><i title="Database" class="cp-data custom-cursor-on-hover"></i></td>
                                <td><i title="MSSQL" class="cp-mssql custom-cursor-on-hover"></i></td>
                                <td><i title="MYSQL" class="cp-mysql custom-cursor-on-hover" cursorshover="true"></i></td>
                                <td><i title="Oracle" class="cp-oracle custom-cursor-on-hover"></i></td>
                            </tr>
                            <tr>
                                <td><i title="Postgres" class="cp-postgres custom-cursor-on-hover" cursorshover="true"></i></td>
                                <td><i title="IBM" class="cp-IBM custom-cursor-on-hover" cursorshover="true"></i></td>
                                <td><i title="Files" class="cp-folder-file custom-cursor-on-hover"></i></td>
                                <td><i title="Hypervisor" class="cp-workflow-execution custom-cursor-on-hover"></i></td>
                                <td><i title="Windows" class="cp-windows custom-cursor-on-hover" cursorshover="true"></i></td>
                                <td><i title="MailingSystem" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                                <td><i title="Network" class="cp-network custom-cursor-on-hover" cursorshover="true"></i></td>
                            </tr>
                            <tr>
                                <td><i title="Golden Gate" class="cp-goldengate custom-cursor-on-hover" cursorshover="true"></i></td>
                                <td><i title="Infoblox" class="cp-infoblox custom-cursor-on-hover" cursorshover="true"></i></td>
                                <td><i title="Router" class="cp-router custom-cursor-on-hover" cursorshover="true"></i></td>
                                <td><i title="Switch" class="cp-switch custom-cursor-on-hover"></i></td>
                                <td><i title="OS" class="cp-os-type custom-cursor-on-hover"></i></td>
                                <td><i title="Linux" class="cp-linux" cursorshover="true"></i></td>
                                <td><i title="HP" class="cp-hp custom-cursor-on-hover"></i></td>
                            </tr>
                            <tr>
                                <td><i title="Rsync" class="cp-rsync custom-cursor-on-hover"></i></td>
                                <td><i title="Veeam" class="cp-Veeam custom-cursor-on-hover"></i></td>
                                <td><i title="Storage" class="cp-stand-storage custom-cursor-on-hover"></i></td>
                                <td><i title="HDS" class="cp-hds custom-cursor-on-hover"></i></td>
                                <td><i title="NetApp" class="cp-netapp" cursorshover="true"></i></td>
                                <td><i title="System Management Tool" class="cp-system-management-tool" cursorshover="true"></i></td>
                                <td><i title="WMI" class="cp-wmi custom-cursor-on-hover"></i></td>
                            </tr>
                            <tr>
                                <td><i title="Oracle Ops Center" class="cp-microsoft custom-cursor-on-hover"></i></td>
                                <td><i title="Sun ILOM" class="cp-sun-ilom custom-cursor-on-hover"></i></td>
                                <td><i title="Veritas Cluster" class="cp-veritas-cluster custom-cursor-on-hover"></i></td>
                                <td><i title="Virtualization" class="cp-virtualization_new custom-cursor-on-hover"></i></td>
                                <td><i title="AIX" class="cp-aix custom-cursor-on-hover" cursorshover="true"></i></td>
                                <td><i title="Solaris" class="cp-oracle-solaris custom-cursor-on-hover" cursorshover="true"></i></td>
                                <td><i title="VMware" class="cp-vmware custom-cursor-on-hover"></i></td>
                            </tr>
                            <tr>
                                <td><i title="Web" class="cp-web custom-cursor-on-hover"></i></td>
                                <td><i title="Power-CLI" class="cp-power-cli custom-cursor-on-hover"></i></td>
                                <td><i title="Workflow" class="cp-workflow custom-cursor-on-hover"></i></td>
                                <td><i title="Replication" class="cp-replication-rotate custom-cursor-on-hover" cursorshover="true"></i></td>
                                <td><i title="Exchange" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                                <td><i title="String Utility" class="cp-stringutility custom-cursor-on-hover"></i></td>
                                <td><i title="Nutanix" class="cp-nutanix custom-cursor-on-hover"></i></td>
                            </tr>
                            <tr>
                                <td><i title="Solution" class="cp-solution custom-cursor-on-hover"></i></td>
                                <td><i title="Token" class="cp-token custom-cursor-on-hover"></i></td>
                                <td><i title="General" class="cp-general custom-cursor-on-hover"></i></td>
                                <td><i title="Create-Logger" class="cp-create-Logger custom-cursor-on-hover"></i></td>
                                <td><i title="Conditional" class="cp-conditional custom-cursor-on-hover"></i></td>
                                <td><i title="Loop" class="cp-loop custom-cursor-on-hover"></i></td>
                                <td><i title="Error-Handling " class="cp-error-handing custom-cursor-on-hover"></i></td>
                            </tr>
                            <tr>
                                <td><i title="System" class="cp-system custom-cursor-on-hover"></i></td>
                                <td><i title="Delay" class="cp-delay custom-cursor-on-hover"></i></td>
                                <td><i title="If" class="cp-if custom-cursor-on-hover"></i></td>
                                <td><i title="Circle-Switch" class="cp-circle-switch custom-cursor-on-hover"></i></td>
                                <td><i title="Data-Source" class="cp-data-source custom-cursor-on-hover"></i></td>
                                <td><i title="Rule" class="cp-rule custom-cursor-on-hover"></i></td>
                                <td><i title="Wait" class="cp-wait custom-cursor-on-hover"></i></td>
                            </tr>
                            <tr>
                                <td><i title="Common" class="cp-common custom-cursor-on-hover"></i></td>
                                <td><i title="Security" class="cp-security custom-cursor-on-hover"></i></td>
                                <td><i title="Powershell" class="cp-powershell custom-cursor-on-hover"></i></td>
                                <td><i title="Conversion" class="cp-conversion custom-cursor-on-hover"></i></td>
                                <td><i title="Script" class="cp-script custom-cursor-on-hover"></i></td>
                                <td><i title="SSH" class="cp-SSH custom-cursor-on-hover"></i></td>
                                <td><i title="Connect " class="cp-connect custom-cursor-on-hover"></i></td>
                            </tr>
                            <tr>
                                <td><i title="Log Files" class="cp-log-file-name custom-cursor-on-hover"></i></td>
                                <td><i title="Configure Settings" class="cp-configure-settings custom-cursor-on-hover"></i></td>
                                <td><i title="PR Site" class="cp-prsite custom-cursor-on-hover"></i></td>
                                <td><i title="Firewall" class="cp-firewall custom-cursor-on-hover"></i></td>
                                <td><i title="Server Cloud" class="cp-server-cloud custom-cursor-on-hover"></i></td>
                                <td><i title="Double Take" class="cp-double-take custom-cursor-on-hover"></i></td>
                                <td><i title="Mainframe" class="cp-main-frame custom-cursor-on-hover"></i></td>

                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>


            <div class="form-group">
                <div class="form-label">Operation Type</div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-workflow-type"></i></span>
                    <select class="form-select-modal w-100" id="templateActionType" data-placeholder="Select Operation Type" data-live-search="true">
                        @foreach (var action in Model.WorkflowActionTypeListVms)
                        {
                            <option id="@action.Value" value="@action.Text">@action.Text</option>
                        }
                    </select>
                </div>
                <span id="templateactiontype-error"></span>
            </div>
            <div class="form-group">
                <div class="form-label">Activity Type</div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-activity-type"></i></span>
                    <select class="form-select-modal w-100" id="Activetype" data-live-search="true" data-placeholder="Select Activity Type">
                        <option value=""></option>
                        <option value="1">Application</option>
                        <option value="2">Database</option>
                        <option value="3">Virtual</option>
                    </select>
                </div>
                <span id="Activetype-error"></span>
            </div>
            <div class="form-group" id="DataTypeCol">
                <div class="form-label">
                    Database Type
                </div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-database"></i></span>
                    <select id="SelectDatabaseType" class="form-select-modal" data-placeholder="Select Database Type ">
                    </select>
                </div>
                <span id="SelectDatabaseType-error"></span>
            </div>
            <div class="form-group">
                <div class="form-label">
                    Replication Category
                </div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-replication-rotate"></i></span>
                    <select id="SelectReplicationType" class="form-select-modal " data-placeholder="Select Replication Category">
                    </select>
                </div>
                <span id="SelectReplicationType-error"></span>
            </div>
            <div class="form-group">
                <div class="form-label">
                    Replication Type
                </div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-replication-type"></i></span>
                    <select id="ddlReplicationTypeNameId" class="form-select-modal" data-placeholder="Select Replication Type">
                    </select>
                </div>
                <span id="ReplicationType-error"></span>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" id="saveTemplate">Save</button>
        </div>
    </div>
</div>
