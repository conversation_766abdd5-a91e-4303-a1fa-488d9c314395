﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.RoboCopyJob.Events.Delete;

public class RoboCopyJobDeletedEventHandler : INotificationHandler<RoboCopyJobDeletedEvent>
{
    private readonly ILogger<RoboCopyJobDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public RoboCopyJobDeletedEventHandler(ILoggedInUserService userService,
        ILogger<RoboCopyJobDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(RoboCopyJobDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.RoboCopyJob}",
            Entity = Modules.RoboCopyJob.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"RoboCopy Job '{deletedEvent.ReplicationName}' deleted successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"RoboCopy Job '{deletedEvent.ReplicationName}' deleted successfully.");
    }
}