﻿using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetMongoDbMonitorStatusByInfraObjectId;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MongoDbMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class MongoDbMonitorStatusController : CommonBaseController
{
    [HttpPost]
    [Authorize(Policy = Permissions.Manage.Create)]
    public async Task<ActionResult<CreateMongoDbMonitorStatusResponse>> CreateMongoDbMonitorStatus([FromBody] CreateMongoDbMonitorStatusCommand createMongoDbMonitorStatusCommand)
    {
        Logger.LogDebug($"Create MongoDbMonitorStatus '{createMongoDbMonitorStatusCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateMongoDbMonitorStatus), await Mediator.Send(createMongoDbMonitorStatusCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Manage.Edit)]
    public async Task<ActionResult<UpdateMongoDbMonitorStatusResponse>> UpdateMongoDbMonitorStatus([FromBody] UpdateMongoDbMonitorStatusCommand updateMongoDbMonitorStatusCommand)
    {
        Logger.LogDebug($"Update MongoDbMonitorStatus '{updateMongoDbMonitorStatusCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateMongoDbMonitorStatusCommand));
    }

    [HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<MongoDbMonitorStatusListVm>>> GetAllMongoDbMonitorStatus()
    {
        Logger.LogDebug("Get All MongoDbMonitorStatus");

        return Ok(await Mediator.Send(new MongoDbMonitorStatusListQuery()));
    }

    [HttpGet("{id}")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<MongoDbMonitorStatusDetailVm>> GetMongoDbMonitorStatusById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "MongoDbMonitorStatus Detail By Id");

        Logger.LogDebug($"Get MongoDbMonitorStatus Detail by Id '{id}'");

        return Ok(await Mediator.Send(new MongoDbMonitorStatusDetailQuery { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<MongoDbMonitorStatusListVm>>> GetPaginatedMongoDbMonitorStatus([FromQuery] GetMongoDbMonitorStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in MongoDbMonitorStatus Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet("type")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<MongoDbMonitorStatusDetailByTypeVm>> GetMongoDbMonitorStatusByType(string type)
    {
        Guard.Against.NullOrEmpty(type, "MongoDbMonitorStatus Detail By Type");

        Logger.LogDebug($"Get MongoDbMonitorStatus Detail by Id '{type}'");

        return Ok(await Mediator.Send(new MongoDbMonitorStatusDetailByTypeQuery { Type = type }));
    }

    [HttpGet("by/{infraObjectId}")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<string>> GetMongoDbMonitorStatusByInfraObjectId(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "MongoDbMonitorStatus InfraObjectId");

        Logger.LogDebug($"Get MongoDbMonitorStatus Detail by InfraObjectId '{infraObjectId}'");

        return Ok(await Mediator.Send(new GetMongoDbMonitorStatusByInfraObjectIdQuery { InfraObjectId = infraObjectId }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        var cacheKeys = new string[] { ApplicationConstants.Cache.AllMongoDbMonitorStatusCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllMongoDbMonitorStatusNameCacheKey };

        ClearCache(cacheKeys);
    }
}