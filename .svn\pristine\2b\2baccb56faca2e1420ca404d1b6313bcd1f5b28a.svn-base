﻿const eMatrixURL = {
    getPagination: "/Manage/EscalationMatrix/GetPagination",
    nameExistUrl: "Manage/EscalationMatrix/IsEscalationMatrixNameExist",
    CreateOrUpdate: "Manage/EscalationMatrix/CreateOrUpdate",
    Delete: "Manage/EscalationMatrix/Delete",
    Name: "Manage/EscalationMatrix/IsEscalationMatrixNameExist",
    operationalService: "Manage/EscalationMatrix/GetOperationalService",
    userName: "Manage/EscalationMatrix/GetUserNames",
    userGroup: "Manage/EscalationMatrix/GetUserGroup",
}
let globallevelData = [];
$(function () {
    let createPermission = $("#ManageExCreate").data("create-permission").toLowerCase();
    let deletePermission = $("#ManageExDelete").data("delete-permission").toLowerCase();
    if (createPermission == 'false') {
        $("#creatematrix").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }
    let selectedValues = [];
    const $searchInput = $('#search-inp');
    const $paginationColumn = $(".pagination-column");

    const dataTable = $('#tblEmatrix').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-right-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        processing: true,
        serverSide: true,
        filter: true,
        ajax: {
            type: "GET",
            url: eMatrixURL.getPagination,
            dataType: "json",
            data(d) {
                d.PageNumber = Math.ceil(d.start / d.length) + 1;
                d.pageSize = d.length;
                d.searchString = selectedValues.length === 0 ? $searchInput.val() : selectedValues.join(';');
                selectedValues = [];
            },
            dataSrc(json) {
                json.recordsTotal = json?.totalCount || 0;
                json.recordsFiltered = json?.filteredCount || json?.recordsTotal;
                json.data.forEach(row => {
                    try {
                        let rawDesc = (row.properties || '').trim();
                        const quoteChars = ["'", '"', "`"];
                        if (quoteChars.includes(rawDesc[0]) && quoteChars.includes(rawDesc.at(-1))) {
                            rawDesc = rawDesc.slice(1, -1);
                        }
                        row._parsedNames = [];
                        if (rawDesc.startsWith('[') && rawDesc.endsWith(']')) {
                            const levels = JSON.parse(rawDesc);
                            levels.forEach(level => {
                                const unanimous = Array.isArray(level.unanimous)
                                    ? level.unanimous.map(u => u.Name).join(', ')
                                    : '';
                                if (Array.isArray(level.users)) {
                                    level.users.forEach(group => {
                                        const groupName = group.name || '';
                                        if (Array.isArray(group.list)) {
                                            group.list.forEach(user => {
                                                const parts = [user.name, groupName, unanimous].filter(Boolean);
                                                if (parts.length) row._parsedNames.push(parts.join(', '));
                                            });
                                        }
                                    });
                                }
                            });
                        }
                    } catch (err) {
                        console.warn('Failed to parse properties JSON:', err);
                        row._parsedNames = [];
                    }
                });
                $paginationColumn.toggleClass("disabled", !json.data?.length);
                return json.data || [];
            }
        },
        columns: [
            {
                data: null,
                name: "Sr. No.",
                autoWidth: true,
                render(data, type, row, meta) {
                    return type === 'display'
                        ? (meta.settings._iDisplayStart + meta.row + 1)
                        : data;
                }
            },
            {
                data: "escMatName",
                name: "Matrix Code &amp Name",
                autoWidth: true,
                render(data, type, row) {
                    return type === 'display'
                        ? `${row.escMatCode || ''} ${row.escMatName || ''}`.trim()
                        : data;
                }
            },
            {
                data: "ownerid",
                name: "OwnerName",
                autoWidth: true,
                render(data, type, row) {
                    return type === 'display' ? (row.ownerName || '') : data;
                }
            },
            {
                data: null,
                name: "Matrix Used",
                autoWidth: true,
                render(data, type, row) {
                    if (type !== 'display') return data;
                    const names = row._parsedNames || [];
                    const fullList = names.join(' , ');
                    const displayList = names.slice(0, 2).join(' , ') + (names.length > 2 ? '...' : '');
                    return `<span title="${fullList}">${displayList}</span>`;
                }
            },
            {
                data: "createdDate",
                name: "Matrix CreateDetails",
                autoWidth: true,
                render(data, type, row) {
                    return type === 'display' ? (row.createdDate || '') : data;
                }
            },
            {
                data: "createdDate",
                name: "MatrixUpdate Details",
                autoWidth: true,
                render(data, type, row) {
                    return type === 'display' ? (row.lastModifiedDate || '') : data;
                }
            },
            {
                orderable: false,
                render(data, type, row) {
                    const escalationData = JSON.stringify(row).replace(/"/g, '&quot;');
                    const editBtn = `<span role="button" title="Edit" class="edit-button" data-escalation="${escalationData}"><i class="cp-edit"></i></span>`;
                    const deleteBtn = row.isParent
                        ? `<span title="Delete disabled" class="delete-button disabled" style="opacity:0.5;"><i class="cp-Delete"></i></span>`
                        : `<span role="button" title="Delete" class="delete-button" data-escalation-id="${row.id}" data-escalation-name="${row.escMatCode}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i></span>`;

                    const iconDisabled = `<span role="button" title="Edit disabled" class="icon-disabled"><i class="cp-edit"></i></span>
                                      <span role="button" title="Delete disabled" class="icon-disabled"><i class="cp-Delete"></i></span>`;

                    if (createPermission === 'true' && deletePermission === 'true') {
                        return `<div class="d-flex align-items-center gap-2">${editBtn}${deleteBtn}</div>`;
                    }
                    if (createPermission === 'true') {
                        return `<div class="d-flex align-items-center gap-2">${editBtn}<span class="icon-disabled"><i class="cp-Delete"></i></span></div>`;
                    }
                    if (deletePermission === 'true') {
                        return `<div class="d-flex align-items-center gap-2"><span class="icon-disabled"><i class="cp-edit"></i></span>${deleteBtn}</div>`;
                    }
                    return `<div class="d-flex align-items-center gap-2">${iconDisabled}</div>`;
                }
            }
        ],
        order: [[0, 'asc']],
        columnDefs: [{ targets: 0, type: 'num' }],
        rowCallback: function (row, data) {
            row.querySelectorAll(".companyname")?.forEach(el => {
                el.style.backgroundColor = `#${Math.floor(Math.random() * 0xFFFFFF).toString(16).padStart(6, '0').toUpperCase()}`;
            });
        }
    });

    //Events  
    
    $('#operationalService').select2();  
    
    $("#previousClick").on("click", function () {
        wizardTeam();   
        wizardlevel();
    });

    $("#unanimousSave").click(function () {
        $('#unanimousId').hide();
    });

    $("#addName").click(function () {
        $('#unanimousId').show();
    });

    $("#saveLevel").click(function () {
        const isUserChecked = $('#userListContainer .userCheckList:checked').length > 0;
        const isGroupChecked = $('#userGroupListContainer .userCheckList:checked').length > 0;
        if (!isUserChecked && !isGroupChecked) {
            notificationAlert("warning","Please select at least one User or User Group");
            return false;
        }
        $("#end,start").show();
        $("#saveLevel").text("Insert");
        $("#escImage").addClass("d-none");
        const editingLevelId = $(this).data('editing-levelid');
        let userList = [];
        let userGroupList = [];
        $('#userListContainer .userCheckList:checked').toArray().forEach(obj => {
            userList.push({ id: obj.id, name: obj.name });
        });
        $('#userGroupListContainer .userCheckList:checked').toArray().forEach(obj => {
            userGroupList.push({ id: obj.id, name: obj.name });
        });
        const obj = {
            id: editingLevelId || getRandomLevelId('level'),
            name: $('#escalationLevelName').val(),
            description: $('#escalationLevelDescriptin').val(),
            Duration: {
                type: $("#escSelectDuration option:selected").val(),
                value: $('#escalationLevelTime').val()
            },
            OpearationServiceName: $("#operationalService option:selected").text(),
            OpearationServiceId: $("#operationalService option:selected").val(),
            users: [{ type: "individual", list: userList }, { type: "group", list: userGroupList }],
            unanimous: [{
                Name: $("#useraddName").val(),
                PhoneNumber: $("#phoneNumber").val(),
                Email: $("#emailId").val()
            }]
        };

        if (editingLevelId) {  
            const index = globallevelData.findIndex(x => x.id === editingLevelId);
            if (index !== -1) {
                globallevelData[index] = obj;
            }
        } else {           
            globallevelData.push(obj);
        }
        $(".Escalation_Timeline ul").empty();
        globallevelData.forEach((item, index) => {
            const html = appendMatrix(item, index + 1);
            $(".Escalation_Timeline ul").prepend(html);
        });        
       $(this).removeData('editing-levelid');
        wizardlevel();
        wizardTeam();
        $("#saveLevel").closest("li").hide();
        $("#useraddName, #phoneNumber, #emailId").val('');
        clearFields();        
    });

    $("#savebtnClick").click(async function () {
        const data = JSON.stringify(globallevelData);
        if (data === '[]') {
            return false;
        }   
        
        const id = companyData?.id ?? '';
        if (id === '') {
            formData = {
                Properties: data,
                id: '',
                EscMatDesc: "",
                EscMatCode:getRandomLevelId("ESC-MAT"),                
                __RequestVerificationToken: gettoken()
            };
        } else {            
            formData = {
                Properties: data,
                id: id,
                EscMatDesc: companyData.escMatDesc,
                EscMatCode: companyData.escMatCode,
                CompanyId:companyData.companyId,
                ApproverId:companyData.approverID,                
                OwnerId:companyData.ownerID,
                OwnerName: companyData.ownerName,
                createdDate: companyData.createdDate, 
                __RequestVerificationToken: gettoken()
            };          
        }
            const response = await $.ajax({
                url: `${RootUrl}${eMatrixURL.CreateOrUpdate}`,
                data: formData,
                type: 'POST'
            });
            if (response?.success && response?.data) {
                $("#CreateModal").modal("hide");
                notificationAlert("success", response.data);
                setTimeout(() => {
                    window.location.reload();                    
                }, 500);
            } else {
                errorNotification(response);
            }      
    });

    $('#escalationLevelName').on('keyup', async function () {        
            const value = $(this).val();
           await validatemscName(value);
    });

    $('#escalationLevelTime').on('keyup', async function () {
        const value = $(this).val();
        await validaDateTime(value);
    });   
    $('#CreateModal').on('shown.bs.modal', function () {
        $('#operationalService').select2();
    });
   
    $('#operationalService').change(async function () {
        const selectedValue = $(this).val();  
        await validaDateOperationalService(selectedValue);
    });     

    $("#nextFunction").on("click", async function () {
        const levelName = $('#escalationLevelName').val();
        const levelDuration = $("#escSelectDuration option:selected").val();
        const levelTime = $("#escalationLevelTime").val();
        const levelOpService = $("#operationalService option:selected").text();
        const isNameValid = await validatemscName(levelName);
        const isTimeValid = await validaDateTime(levelTime);        
        const isOpServiceValid = await validaDateOperationalService(levelOpService);
        const btn = document.getElementById("nextFunction");
        if (isNameValid && isTimeValid && isOpServiceValid) {           
            btn.setAttribute('data-bs-toggle', 'pill');
            btn.setAttribute('data-bs-target', '#pills-Teams');
            const tabTrigger = new bootstrap.Tab(btn);
            tabTrigger.show();
            $("#previousClick").closest("li").show();
            $("#saveLevel").closest("li").show();
            $("#nextFunction").closest("li").hide();
        } 
    });
    
    //$(document).on('click', '.btnMatleveCon', function () {
    //    let id = $(this)[0].id;
    //    sessionStorage.setItem('teamResourceId', id);
    //    var companyData = $(this).data('escalation');
    //    $('#textgroupId').val(companyData.id);
    //    // $('#levelModal').modal('show');
    //    Escmatlevdis(id);
    //});

    $(document).on('keyup input', '#search-inp', function () {
        const inputValue = $('#search-inp').val() ?? '';
        selectedValues = [];
        $('input[type="checkbox"]:checked').toArray().forEach(checkbox => {
            const checkboxValue = checkbox.value ?? '';
            selectedValues.push(checkboxValue + inputValue);
        });
        dataTable.search(inputValue).draw();
    });

    let companyData;
    $('#tablebody').on('click', '.edit-button', function () {        
      companyData = $(this).data('escalation');
        populateModalFields(companyData);
        $('#savebtnClick').text('Update')
        $('#savebtnClick').attr('title', 'Update')
        $('#CreateModal').modal('show');        
        $("#end").show();
        $("#start").show();
        individual();
        groupUser();
        $("#escImage").addClass("d-none");
        clearFields();
        wizardlevel();
        wizardTeam();
    });

    $('#Escalation_Time').on('click', '.levelEdit', function () {   
        wizardlevel();
        wizardTeam();
        let levelId = $(this).attr('data-levelid');
        $("#saveLevel").data('editing-levelid', levelId);
        $("#escImage").addClass("d-none");
        $("#enNameEerror, #escHourError, #operationError").text('').removeClass('field-validation-error');
        let filterlevelData = globallevelData.filter((filterData) => filterData.id === levelId);
        if (filterlevelData.length > 0) {
            let filterData = filterlevelData[0];
            $('#escalationLevelName').val(filterData.name);
            $('#escalationLevelDescriptin').val(filterData.description);
            $('#escalationLevelTime').val(filterData.Duration.value);            
            $('#escSelectDuration').val(filterData.Duration.type).trigger('change');           
            setDropdownValue('#operationalService', filterData.OpearationServiceName);
            $('#userListContainer').empty();
            $('#userGroupListContainer').empty();
            individual(filterData);
            groupUser(filterData);
            $("#saveLevel").text("Update");
            $('#useraddName').val(filterData.unanimous[0].Name);
            $('#emailId').val(filterData.unanimous[0].Email);
            $('#phoneNumber').val(filterData.unanimous[0].PhoneNumber);
        }
    });
    
    $('#Escalation_Time').on('click', '.levelDelete', function () {
        const levelId = $(this).closest('li').attr('id'); 
        let levelName = $(this).data('levelname');
        $("#deleteLevelData").text(levelName);
        $('#confirmDeleteLevelBtn').data('level-id', levelId);
        $('#DeleteModalLevel').modal('show');
    });

    $('#confirmDeleteLevelBtn').click(function () {       
        const levelId = $(this).data('level-id');
        globallevelData = globallevelData.filter(item => item.id !== levelId);
        $(".Escalation_Timeline ul").empty();
        globallevelData.forEach((item, index) => {
            const html = appendMatrix(item, index + 1);
            $(".Escalation_Timeline ul").prepend(html);
        });
        $('#DeleteModalLevel').modal('hide');
    });

    //LevelCreate
    $("#levelCreate").on("click", function () {
        globallevelData.length = 0;
        $("#saveLevel").removeData('editing-levelid');
        $("#CreateModal").modal("show")
        $("#escImage").removeClass("d-none");        
        individual();
        groupUser();
        $("#end").hide();
        $("#start").hide();
        $("#saveLevel").text("Insert");
        $("#savebtnClick").text("Save");
        $(".Escalation_Timeline ul").empty();
        clearFields();
        wizardlevel();
        wizardTeam();
    })

    $("#clearLevel").on("click", function () {
        clearFields();         
        wizardlevel();
        wizardTeam();
    });
    //Search
    $('#txtSearch').on('keydown input', function () {
        const term = ($(this).val() ?? '').toLowerCase().trim();
        function filterList(containerId, rowSelector, textSelector, emptyMsg) {
            const $container = $(containerId);
            const rows = $container.find(rowSelector).toArray();
            let count = 0;
            rows.forEach(row => {
                const $row = $(row);
                const text = $row.find(textSelector).text().toLowerCase();
                const isMatch = text.includes(term);
                $row.toggle(isMatch);
                if (isMatch) count++;
            });
            $container.find('.no-data-message').remove();
            if (count === 0) {
                const colspan = $container.find('table tr:first td').length || 4;
                $container.find('table tbody').append(
                    `<tr class="no-data-message">
                    <td colspan="${colspan}" class="text-center text-muted">${emptyMsg}</td>
                </tr>`
                );
            }
        }
        filterList('#userListContainer', 'tr', 'td:nth-child(2) span:last', 'No data found');
        filterList('#userGroupListContainer', 'tr', 'label.form-check-label', 'No data found');
    });

    //delete
    let deleteEscId = null;
    $('#tablebody').on('click', '.delete-button', function () {
        deleteEscId = $(this).data('escalation-id');
        const escName = $(this).data('escalation-name');
        $('#textDeleteId').val(deleteEscId);
        $('#deleteData').text(escName);
        $('#DeleteModal').modal('show');
    });

    $('#confirmDeleteBtn').on('click', function () {       
        $.ajax({
            url: RootUrl + eMatrixURL.Delete,
            type: "POST",
            dataType: "json",
            data: { id: deleteEscId },
            success: function (result) {
                if (result?.success) {
                    notificationAlert("success", result.data);                   
                    $(`[data-escalation-id="${deleteEscId}"]`).closest('tr').remove();                    
                    dataTable.ajax.reload();
                } else {
                    errorNotification(result);
                }
            },            
            complete: function () {
                $('#DeleteModal').modal('hide');
                deleteEscId = null;
            }
        });
    });

    ///Functions

    const getRandomLevelId = (value) => {
        return value + "_" + Math.floor(Math.random() * 11110 * Math.random() * 103180)
    } 

    const appendMatrix = (data, index) => {
        let borderColor = ['success', 'warning', 'primary', 'danger'][index % 4];

        let html = `
            <li class="li" id="${data.id}">
                <div class="Escalation_Timeline_Card card border-${borderColor}">
                    <div class="d-flex align-items-center">
                        <span class="Timeline_Card_Level bg-${borderColor} badge bg-${borderColor}">
                            Level ${index}
                        </span>
                        <div class="d-grid ms-3">
                            <h6 class="mb-1 text-truncate" title="${data.description}">
                                ${data.name}
                            </h6>
                            <span class="d-none">
                                <img class="rounded-circle" src="" width="20" height="20">
                            </span>
                        </div>
                        <div class="d-flex ms-auto">
                            <span class="text-primary me-2 text-truncate">
                                <i class="cp-apply-finish-time"></i>
                                &nbsp;${data.Duration.value} ${data.Duration.type}
                            </span>
                           <i class="cp-edit fs-5 me-2 levelEdit" data-levelId='${data.id}'></i>
                            <i class="cp-Delete fs-5 me-2 levelDelete" data-levelName='${data.name}'></i>
                        </div>
                    </div>
                </div>
            </li>`;

        return html
    }
    function OperationalServiceList() {
        $("#operationalService").select2();
        return $.ajax({
            type: "GET",
            url: RootUrl+ eMatrixURL.operationalService,
            dataType: "json"
        }).then(data => {
            const operationalService = $('#operationalService');
            operationalService.empty();
            operationalService.append('<option value=""></option>');
            data.forEach(item => {
                operationalService.append('<option value="' + item.id + '">' + item.name + '</option>');
            });  
            return data;
        })
    }   
    function wizardlevel() {
        const btn = document.getElementById("previousClick");
        btn.setAttribute('data-bs-toggle', 'pill');
        btn.setAttribute('data-bs-target', '#pills-Level');
        var tabTrigger = new bootstrap.Tab(btn);
        tabTrigger.show();
        $('#previousClick').closest("li").hide();
        $("#nextFunction").closest("li").show();
        $('#saveLevel').closest("li").hide();
    }
    function wizardTeam() {
        const nextBtn = document.getElementById("nextFunction");
        nextBtn.removeAttribute('data-bs-toggle', 'pill');
        nextBtn.removeAttribute('data-bs-target', '#pills-Teams');  
    }
    function individual(filterData = null) {
    const userColorPallete = [
        '#00CCFF', '#0066FF', '#CC3399', '#FF9900', '#99CC00', '#3399FF', '#993399',
        '#339966', '#993333', '#009900', '#000099', '#666633', '#FF3300', '#6600CC', '#C65F00', '#009974',
        '#BEBE00', '#CC0000', '#9100C5', '#020057', '#949C3C', '#00CB82', '#418E8A', '#0099CC', '#3D50FF'
    ];

        $.ajax({
            type: "GET",
            url: RootUrl + eMatrixURL.userName,
            dataType: "json",
            success: function (data) {
                const $container = $('#userListContainer');
                $container.empty();

                const $table = $('<table class="table-borderless table"><tbody></tbody></table>');

                (data ?? []).forEach((item, index) => {
                    const user = item?.userInfo;
                    if (!user) return false;
                    const userName = user?.userName ?? 'Unknown';
                    const userEmail = user?.email ?? 'NA';
                    const userId = user?.userId ?? `user_${index}`;
                    const displayName = userName.length > 21 ? `${userName.substring(0, 21)}...` : userName;

                    const nameSplit = userName.trim().split(' ');
                    const initials = nameSplit.length > 1
                        ? `${nameSplit[0]?.[0] ?? ''}${nameSplit[1]?.[0] ?? ''}`.toUpperCase()
                        : `${nameSplit[0]?.[0] ?? ''}`.toUpperCase();

                    const colorIndex = index % userColorPallete.length;
                    const bgColor = userColorPallete[colorIndex];

                    const avatarStyle = `
                display:inline-flex;
                align-items:center;
                justify-content:center;
                width:23px;
                height:23px;
                border-radius:50%;
                background-color:${bgColor};
                color:#fff;
                font-weight:bold;
                font-size:12px;
                text-transform:uppercase;
                margin-right:5px;
                margin-left: -18px;
            `;
                    const selectedUsers = filterData?.users?.find(u => u.type === 'individual')?.list ?? [];
                    const isChecked = selectedUsers.some(u => u.id === userId);
                    const $row = $(`
                <tr>
                    <td>
                        <div class="mt-0 align-middle form-check">
                            <input type="checkbox"
                                class="form-check-input userCheckList"
                                id="${userId}" name="${displayName}"
                                ${isChecked ? 'checked' : ''}>
                        </div>
                    </td>
                    <td>
                        <span id="username" title="${initials}" style="${avatarStyle}">${initials}</span>
                        <span title="${userName}">${displayName}</span>
                    </td>
                    <td>${userEmail}</td>
                    <td>
                        <div>
                            <input type="checkbox" name="toggle_${userId}" id="SMSNotificationtoggle_${userId}">
                            <label for="SMSNotificationtoggle_${userId}"></label>
                        </div>
                    </td>
                </tr>
            `);
                    $table.find('tbody').append($row);
                });

                $container.append($table);
            }
        });
}
    function groupUser(filterData = null) {
    const userColorPallete = [
        '#00CCFF', '#0066FF', '#CC3399', '#FF9900', '#99CC00', '#3399FF', '#993399',
        '#339966', '#993333', '#009900', '#000099', '#666633', '#FF3300', '#6600CC',
        '#C65F00', '#009974', '#BEBE00', '#CC0000', '#9100C5', '#020057', '#949C3C',
        '#00CB82', '#418E8A', '#0099CC', '#3D50FF'
    ];

        $.ajax({
            type: "GET",
            url: RootUrl + eMatrixURL.userGroup,
            dataType: "json",
            success: function (data) {
                const $container = $('#userGroupListContainer');
                $container.empty();
                const $table = $('<table class="table-borderless table"><tbody></tbody></table>');
                (data ?? []).forEach((user, index) => {
                    const userGroupName = user?.groupName ?? 'Unnamed Group';
                    const userEmail = user?.email ?? 'NA';
                    const userGroupId = user?.id ?? '';
                    const displayName = userGroupName.length > 21
                        ? `${userGroupName.substring(0, 21)}...`
                        : userGroupName;
                    const nameSplit = userGroupName.trim().split(' ');
                    const initials = nameSplit.length > 1
                        ? (nameSplit[0]?.[0] + nameSplit[1]?.[0]).toUpperCase()
                        : nameSplit[0]?.[0]?.toUpperCase() ?? '';
                    const colorIndex = index % userColorPallete.length;
                    const bgColor = userColorPallete[colorIndex];
                    const avatarStyle = `
                display:inline-flex;
                align-items:center;
                justify-content:center;
                width:23px;
                height:23px;
                border-radius:50%;
                background-color:${bgColor};
                color:#fff;
                font-weight:bold;
                font-size:12px;
                text-transform:uppercase;
                margin-right:5px;
                margin-left:5px;
            `;
                    const selectedGroups = filterData?.users?.find(u => u.type === 'group')?.list ?? [];
                    const isChecked = selectedGroups.some(g => g.id === userGroupId);
                    const $row = $(`
                <tr>
                    <td>
                        <div class="mt-0 align-middle form-check">
                            <input type="checkbox"
                                class="form-check-input userCheckList"
                                data-user-id="${userGroupId}"
                                id="${userGroupId}"
                                name="${displayName}"
                                ${isChecked ? 'checked' : ''}>
                            <span style="${avatarStyle}" title="${initials}">${initials}</span>
                            <label class="form-check-label" title="${userGroupName}">${displayName}</label>
                        </div>
                    </td>                    
                    <td>${userEmail}</td>
                    <td>
                        <div>
                            <input type="checkbox"
                                name="toggle_${userGroupId}"
                                id="SMSNotificationtoggle_${userGroupId}">
                            <label for="SMSNotificationtoggle_${userGroupId}"></label>
                        </div>
                    </td>
                </tr>
            `);
                    $table.find('tbody').append($row);
                });

                $container.append($table);
            },
            error: function () {
                $('#userGroupListContainer').html('<p class="text-danger">Failed to load user groups.</p>');
            }
        });

    }
    function setDropdownValue(selector, value) {
        const $dropdown = $(selector);
        if ($dropdown.find(`option[value="${value}"]`).length === 0) {
            $dropdown.append(new Option(value, value));
        }
        $dropdown.val(value);
    }
    function populateModalFields(escData) {    
        let levels;
        if (Array.isArray(escData.properties)) {
            levels = escData?.properties;
        } else {             
            let cleanedJson = escData?.properties;
            if (typeof cleanedJson === 'string' && cleanedJson.startsWith("'") && cleanedJson.endsWith("'")) {
                cleanedJson = cleanedJson.slice(1, -1);
            }
            levels = JSON.parse(cleanedJson);        
    }
    globallevelData = levels;
    $(".Escalation_Timeline ul").empty();
    const appendMatrix1 = (data, index) => {
        const borderColor = ['success', 'warning', 'primary', 'danger'][index % 4];
        return `
        <li class="li" id="${data.id}">
            <div class="Escalation_Timeline_Card card border-${borderColor}">
                <div class="d-flex align-items-center">
                    <span class="Timeline_Card_Level bg-${borderColor} badge bg-${borderColor}">
                        Level ${index}
                    </span>
                    <div class="d-grid ms-3">
                        <h6 class="mb-1 text-truncate" title="${data.description}">
                            ${data.name}
                        </h6>
                        <span class="d-none">
                            <img class="rounded-circle" src="" width="20" height="20">
                        </span>
                    </div>
                    <div class="d-flex ms-auto">
                        <span class="text-primary me-2 text-truncate">
                            <i class="cp-apply-finish-time"></i>
                            &nbsp;${data.Duration.value} ${data.Duration.type}
                        </span>
                        <i class="cp-edit fs-5 me-2 levelEdit" data-levelId='${data.id}'></i>
                        <i class="cp-Delete fs-5 me-2 levelDelete" data-levelName='${data.name}'></i>
                    </div>
                </div>
            </div>
        </li>`;
    };
    levels.forEach((level, index) => {
        const getDiagramHtml = appendMatrix1(level, index + 1);
        $(".Escalation_Timeline ul").prepend(getDiagramHtml);
    });
    OperationalServiceList();
    }
    function clearFields() {
        $("#escalationLevelName, #escalationLevelDescriptin, #escalationLevelTime").val('');
        $("#enNameEerror, #escHourError, #operationError").text('').removeClass('field-validation-error');
        $("#operationalService").empty();    
        $('#userGroupListContainer .userCheckList:checked').prop('checked', false);
        $('#userListContainer .userCheckList:checked').prop('checked', false);
        $('#escSelectDuration').val("Days").trigger('change');
        OperationalServiceList();
    }

///Valaidation Function

    async function validatemscName(value, url) {
        const errorElement = $('#enNameEerror');

        if (!value) {
            errorElement.text('Enter Level Name').addClass('field-validation-error');
            return false;
        }
        var url = RootUrl + eMatrixURL.Name;
        var data = {};
        data.TeamName = value;
        const validationResults = [
            await SpecialCharValidate(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await OnlyNumericsValidate(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsTeamNameExist(url, data, OnError),           
        ];
        return await CommonValidation(errorElement, validationResults);
    }

    async function validaDateTime(value) {
        const errorElement = $('#escHourError');

        if (!value) {
            errorElement.text('Select Escalation Time').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true
        }
    }  

    async function validaDateOperationalService() {
        const errorElement = $('#operationError');
        const e = document.getElementById("operationalService");
        if (!e) {
            errorElement.text('Operational Service element not found').addClass('field-validation-error');
            return false;
        }
        const selectedIndex = e.selectedIndex;
        if (selectedIndex === -1 || !e.options || !e.options[selectedIndex]) {
            errorElement.text('Select Operational Service Name').addClass('field-validation-error');
            return false;
        }
        const text = e.options[selectedIndex].text.trim();
        if (text === "") {
            errorElement.text('Select Operational Service Name').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    } 

    async function IsTeamNameExist(url, data, errorFunc) {
        const teamName = data.TeamName?.trim();
        if (!teamName) return true;
        const editingLevelId = $("#saveLevel").data('editing-levelid');
        const existsLocally = globallevelData.some(item =>
            item.name?.trim().toLowerCase() === teamName.toLowerCase() &&
            item.id !== editingLevelId
        );
        if (existsLocally) {
            return "Level Name already exists";
        }
        const existsOnServer = await GetAsync(url, data, errorFunc);
        return existsOnServer ? "Level Name already exists" : true;
    }
});
