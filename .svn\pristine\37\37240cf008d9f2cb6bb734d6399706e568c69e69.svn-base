﻿using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PostgresMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IPostgresMonitorLogsService
{
    Task<List<PostgresMonitorLogsListVm>> GetPostgresMonitorLogsList();
    Task<BaseResponse> CreateAsync(CreatePostgresMonitorLogCommand createPostgresMonitorLogCommand);
    Task<PostgresMonitorLogsDetailVm> GetByReferenceId(string id);
    Task<List<PostgresMonitorLogsDetailByTypeVm>> GetPostgresMonitorLogsDetailByTypeVm(string type);
    Task<PaginatedResult<PostgresMonitorLogsListVm>> GetPaginatedPostgresMonitorLogs(GetPostgresMonitorLogsPaginatedListQuery query);
}