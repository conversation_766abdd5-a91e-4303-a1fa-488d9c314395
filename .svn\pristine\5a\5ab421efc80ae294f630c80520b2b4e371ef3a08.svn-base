﻿using ContinuityPatrol.Application.Features.DRCalendar.Events.Create;
using ContinuityPatrol.Application.Features.DRCalendar.Events.SendEmail;

namespace ContinuityPatrol.Application.Features.DRCalendar.Commands.Create;

public class CreateDrCalendarCommandHandler : IRequestHandler<CreateDrCalendarCommand, CreateDrCalendarResponse>
{
    private readonly IDrCalenderRepository _drCalenderRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateDrCalendarCommandHandler(IMapper mapper, IDrCalenderRepository drCalenderRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _drCalenderRepository = drCalenderRepository;
        _publisher = publisher;
    }

    public async Task<CreateDrCalendarResponse> Handle(CreateDrCalendarCommand request,
        CancellationToken cancellationToken)
    {
        var drCalender = _mapper.Map<DrCalenderActivity>(request);

        drCalender = await _drCalenderRepository.AddAsync(drCalender);

        var response = new CreateDrCalendarResponse
        {
            Message = Message.Create("DR Calendar Activity", drCalender.ActivityName),
            Id = drCalender.ReferenceId
        };

        await _publisher.Publish(new DrCalendarCreatedEvent { ActivityName = drCalender.ActivityName },
            cancellationToken);

        await _publisher.Publish(new DrCalendarSendEmailEvent
        {
            Id = drCalender.ReferenceId,
            BusinessServiceId = drCalender.BusinessServiceId,
            Description = drCalender.Description,
            InvitationNo = drCalender.InvitationNo,
            ActivityName = drCalender.ActivityName,
            ScheduledStartDate = drCalender.ScheduledStartDate,
            ScheduledEndDate = drCalender.ScheduledEndDate,
            Responsibility = drCalender.Responsibility,
            RecipientTwo = drCalender.RecipientTwo,
            WorkflowProfiles = drCalender.WorkflowProfiles,
            File = request.File,
            Location = drCalender.Location,
            MailSentActivity = "REQUEST",
            SetReminder=request.SetReminders
        }, cancellationToken);

        return response;
    }
}