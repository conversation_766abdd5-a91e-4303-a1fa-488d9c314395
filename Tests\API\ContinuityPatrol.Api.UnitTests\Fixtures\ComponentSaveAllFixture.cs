using AutoFixture;
using ContinuityPatrol.Application.Features.ComponentSaveAll.Queries.GetDetail;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class ComponentSaveAllFixture : IDisposable
{
    public ComponentSaveAllDetailVm ComponentSaveAllDetailVm { get; set; }

    public ComponentSaveAllFixture()
    {
        ComponentSaveAllDetailVm = AutoComponentSaveAllFixture.Create<ComponentSaveAllDetailVm>();
    }

    public Fixture AutoComponentSaveAllFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customize<ComponentSaveAllDetailVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.EntityId, Guid.NewGuid().ToString)
                .With(b => b.EntityName, "Enterprise Database Cluster Component")
                .With(b => b.Type, "Database")
                .With(b => b.Properties, "{\"serverName\":\"DB-CLUSTER-01\",\"ipAddress\":\"*************\",\"port\":1433,\"instanceName\":\"MSSQLSERVER\",\"databaseName\":\"EnterpriseDB\",\"version\":\"SQL Server 2022\",\"edition\":\"Enterprise\",\"maxConnections\":1000,\"memoryGB\":64,\"storageGB\":2048,\"backupSchedule\":\"Daily\",\"maintenanceWindow\":\"Sunday 2:00 AM\"}"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
