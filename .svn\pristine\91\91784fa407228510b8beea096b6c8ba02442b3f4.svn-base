﻿using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowCategoryViewFixture:IDisposable
{
    public List<WorkflowCategoryView> WorkflowCategoryViewPaginationList { get; set; }
    public List<WorkflowCategoryView> WorkflowCategoryViewList { get; set; }
    public WorkflowCategoryView WorkflowCategoryViewDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowCategoryViewFixture()
    {
        var fixture = new Fixture();

        WorkflowCategoryViewList = fixture.Create<List<WorkflowCategoryView>>();

        WorkflowCategoryViewPaginationList = fixture.CreateMany<WorkflowCategoryView>(20).ToList();

        WorkflowCategoryViewDto = fixture.Create<WorkflowCategoryView>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
