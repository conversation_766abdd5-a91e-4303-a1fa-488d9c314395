﻿namespace ContinuityPatrol.Application.Features.InfraSummary.Commands.Create;

public class CreateInfraSummaryCommand : IRequest<CreateInfraSummaryResponse>
{
    public string EntityName { get; set; }
    public string Logo { get; set; }
    public string TypeId { get; set; }
    public string Type { get; set; }
    public int Count { get; set; }
    public string CompanyId { get; set; }
    public string BusinessServiceId { get; set; }

    public override string ToString()
    {
        return $"Entity Name: {EntityName};";
    }
}