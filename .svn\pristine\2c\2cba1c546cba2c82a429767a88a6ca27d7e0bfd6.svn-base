﻿using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessServiceDrReadyDetails;
using ContinuityPatrol.Web.Areas.Dashboard.Controllers;
using DevExpress.XtraReports.UI;
using Newtonsoft.Json;
using System.Drawing;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{

    [SupportedOSPlatform("windows")]
    public partial class ResiliencyReadinessReport : DevExpress.XtraReports.UI.XtraReport
    {
        private readonly ILogger<ServiceAvailabilityController> _logger;

        public static List<BusinessServiceDrReadyDetailVm> resiliencyReadinessReport = new List<BusinessServiceDrReadyDetailVm>();
        public ResiliencyReadinessReport(string data, string reportGeneratedName)
        {
            try
            {
                _logger = ServiceAvailabilityController._logger;
                resiliencyReadinessReport = JsonConvert.DeserializeObject<List<BusinessServiceDrReadyDetailVm>>(data);
                InitializeComponent();
                ClientCompanyLogo();
                var report = resiliencyReadinessReport;
                var DRReadyCount = 0;
                var DRNotReadyCount = 0;
                var DRPartialCount = 0;
                foreach (var item in report)
                {
                    bool isNotReady = item.DRReady == "0" && item.DrReadyListVm.DrReadyExecutionCount == 0;
                    bool isReady = item.DRReady == "1" && !item.DrReadyListVm.IsDataLagExceed;
                    bool isPartialReady = (item.DRReady == "0" && item.DrReadyListVm.DrReadyExecutionCount > 0) ||
                                          (item.DRReady == "1" && item.DrReadyListVm.IsDataLagExceed);

                    if (isNotReady)
                    {
                        DRNotReadyCount++;
                    }
                    else if (isReady)
                    {
                        DRReadyCount++;
                    }
                    else if (isPartialReady)
                    {
                        DRPartialCount++;
                    }
                }

                var serviceConfigured = DRReadyCount + DRNotReadyCount + DRPartialCount;
                lblConfigCount.Text = serviceConfigured.ToString("D2");
                lblReadyCount.Text = DRReadyCount.ToString("D2");
                lblNotReadyCount.Text = DRNotReadyCount.ToString("D2");
                lblPartialCount.Text = DRPartialCount.ToString("D2");

                _username.Text = "Report Generated By: " + reportGeneratedName.ToString();

                xrTable1.BeforePrint += (sender, e) =>
                {
                    XRTableRow headerRow = new XRTableRow();
                    var table = (XRTable)sender;
                    table.Rows.Clear();
                    headerRow.Cells.Add(new XRTableCell { Text = " Services Configured", WidthF = 231.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                    headerRow.Cells.Add(new XRTableCell { Text = "Readiness", WidthF = 110.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                    headerRow.Cells.Add(new XRTableCell { Text = "Remarks", WidthF = 731.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                    table.Rows.Add(headerRow);
                };

                xrTable2.BeforePrint += (sender, e) =>
                {
                    var table = (XRTable)sender;
                    table.Rows.Clear();
                    foreach (var item in report)
                    {
                        XRTableRow dataRow = new XRTableRow();
                        XRTableRow remarkRow = new XRTableRow();
                        XRTableRow remarkRow1 = new XRTableRow();
                        XRTableRow remarkRow2 = new XRTableRow();

                        XRTableRow remarkRow4 = new XRTableRow();
                        XRTableRow remarkRow5 = new XRTableRow();
                        XRTableRow remarkRow7 = new XRTableRow();
                        XRTableRow remarkRow8 = new XRTableRow();

                        XRPictureBox pictureBox = new XRPictureBox();
                        pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox.SizeF = new System.Drawing.SizeF(18F, 23F);
                        var path = " ";
                        if (item.DrReadyListVm.DrReadyInfraCount == 0) { path = "wwwroot/img/Drdrill_Icons/error.png"; }
                        else { path = "wwwroot/img/Drdrill_Icons/tick.png"; };
                        pictureBox.Image = Image.FromFile(path);

                        XRPictureBox pictureBox1 = new XRPictureBox();
                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 23F);
                        var path1 = " ";
                        if (item.DrReadyListVm.DrReadyWorkflowCount == 0) { path1 = "wwwroot/img/Drdrill_Icons/error.png"; }
                        else if (item.DrReadyListVm.DrReadyWorkflowAttaches.Count > 0) { path1 = "wwwroot/img/Drdrill_Icons/CP-Warning.png"; }
                        else { path1 = "wwwroot/img/Drdrill_Icons/tick.png"; };
                        pictureBox1.Image = Image.FromFile(path1);


                        XRPictureBox pictureBox2 = new XRPictureBox();
                        pictureBox2.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox2.SizeF = new System.Drawing.SizeF(18F, 18F);
                        var path2 = "wwwroot/img/Drdrill_Icons/abot.png";
                        pictureBox2.Image = Image.FromFile(path2);


                        XRPictureBox pictureBox3 = new XRPictureBox();
                        pictureBox3.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox3.SizeF = new System.Drawing.SizeF(110F, 23F);
                        var path3 = "";
                        if (item.DRReady == "0" && item.DrReadyListVm.DrReadyWorkflowExecutions.Count == 0) { path3 = "wwwroot/img/Drdrill_Icons/NotReady.png"; }
                        else if ((item.DRReady == "0" && item.DrReadyListVm.DrReadyWorkflowExecutions.Count > 0) || (item.DrReadyListVm.IsDataLagExceed == true && item.DRReady == "1")) { path3 = "wwwroot/img/Drdrill_Icons/Partial-shield.png"; }
                        else if (item.DRReady == "1") { path3 = "wwwroot/img/Drdrill_Icons/Ready.png"; }
                        else { path3 = "wwwroot/img/Drdrill_Icons/Partial-shield.png"; };
                        pictureBox3.Image = Image.FromFile(path3);

                        XRPictureBox pictureBox4 = new XRPictureBox();
                        pictureBox4.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox4.SizeF = new System.Drawing.SizeF(18F, 23F);
                        var path4 = " ";

                        if (item.DrReadyListVm.DrReadyExecutionCount == 0) { path4 = "wwwroot/img/Drdrill_Icons/error.png"; }
                        else if (item.DrReadyListVm.DrReadyWorkflowExecutions.Count > 0) { path4 = "wwwroot/img/Drdrill_Icons/CP-Warning.png"; }
                        else { path4 = "wwwroot/img/Drdrill_Icons/tick.png"; };
                        pictureBox4.Image = Image.FromFile(path4);

                        XRPictureBox pictureBox5 = new XRPictureBox();
                        pictureBox5.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox5.SizeF = new System.Drawing.SizeF(18F, 23F);
                        var path5 = " ";
                        if (item.DrReadyListVm.DrDataLagExceeds.Count > 0) { path5 = "wwwroot/img/Drdrill_Icons/CP-Warning.png"; }
                        else if (item.DrReadyListVm.DataLagExceedCount == "0") { path5 = "wwwroot/img/Drdrill_Icons/error.png"; }
                        else { path5 = "wwwroot/img/Drdrill_Icons/tick.png"; };
                        pictureBox5.Image = Image.FromFile(path5);
                        XRTable mainTable = new XRTable();
                        mainTable.WidthF = 341F;
                        XRTable remarkTable = new XRTable();
                        remarkTable.WidthF = 731F;
                        XRTableRow mainRemark = new XRTableRow();
                        XRTableRow rowRemark = new XRTableRow();

                        var DrReady = item.DrReadyListVm;
                        remarkRow.Cells.Add(new XRTableCell { WidthF = 5F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                        remarkRow.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(item.BusinessServiceName) ? item.BusinessServiceName.ToString() : "-", WidthF = 220.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                        remarkRow.Cells.Add(new XRTableCell { WidthF = 5F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                        remarkRow.Cells.Add(new XRTableCell { Controls = { pictureBox3 }, WidthF = 115.11F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });// Add pictureBox with if;
                        mainTable.Rows.Add(remarkRow);
                        rowRemark.Cells.Add(new XRTableCell { Controls = { pictureBox }, WidthF = 30F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                        rowRemark.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(item.DrReadyListVm.DrReadyInfraObject) ? item.DrReadyListVm.DrReadyInfraObject.ToString() : "-", WidthF = 700.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                        
                        remarkTable.Rows.Add(rowRemark);
                        remarkRow1.Cells.Add(new XRTableCell { Controls = { pictureBox1 }, WidthF = 30F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                        remarkRow1.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(item.DrReadyListVm.DrReadyWorkflow) ? item.DrReadyListVm.DrReadyWorkflow.ToString() : "-", WidthF = 700.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                        remarkTable.Rows.Add(remarkRow1);

                        if (item.DrReadyListVm.DrReadyWorkflowAttaches.Count > 0)
                        {
                            remarkRow2.Cells.Add(new XRTableCell { Text = "InfraObject Name", WidthF = 150.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow2.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow2.Cells.Add(new XRTableCell { Text = "Operational Function Name", WidthF = 250.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow2.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow2.Cells.Add(new XRTableCell { Text = "Workflow Status", WidthF = 290F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkTable.Rows.Add(remarkRow2);
                            foreach (var row in item.DrReadyListVm.DrReadyWorkflowAttaches)
                            {
                                XRTableRow remarkRow3 = new XRTableRow();
                                remarkRow3.Cells.Add(new XRTableCell { Text = (!string.IsNullOrEmpty(row.InfraObjectName) ? row.InfraObjectName.ToString() : "-"), WidthF = 150.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow3.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow3.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(row.BusinessFunctionName) ? row.BusinessFunctionName.ToString() : "-", WidthF = 250.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow3.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow3.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(row.WorkflowStatus) ? row.WorkflowStatus.ToString() : "-", WidthF = 290F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkTable.Rows.Add(remarkRow3);
                            }


                        }
                        remarkRow4.Cells.Add(new XRTableCell { Controls = { pictureBox4 }, WidthF = 30F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                        remarkRow4.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(item.DrReadyListVm.DrReadyWorkflowExecutionStatus) ? item.DrReadyListVm.DrReadyWorkflowExecutionStatus.ToString() : "-", WidthF = 700.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                        remarkTable.Rows.Add(remarkRow4);
                        if (item.DrReadyListVm.DrReadyWorkflowExecutions.Count > 0)
                        {
                            remarkRow5.Cells.Add(new XRTableCell { Text = "InfraObject Name", WidthF = 130.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow5.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow5.Cells.Add(new XRTableCell { Text = "Operational Function Name", WidthF = 130.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow5.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow5.Cells.Add(new XRTableCell { Text = "Workflow Status", WidthF = 130F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow5.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow5.Cells.Add(new XRTableCell { Text = "Failed Action Name", WidthF = 140F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow5.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow5.Cells.Add(new XRTableCell { Text = "Execution Failure Status", WidthF = 140F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkTable.Rows.Add(remarkRow5);
                            foreach (var cell in item.DrReadyListVm.DrReadyWorkflowExecutions)
                            {
                                XRTableRow remarkRow6 = new XRTableRow();
                                remarkRow6.Cells.Add(new XRTableCell { Text = (!string.IsNullOrEmpty(cell.InfraObjectName) ? cell.InfraObjectName.ToString() : "-"), WidthF = 130.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow6.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow6.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(cell.BusinessFunctionName) ? cell.BusinessFunctionName.ToString() : "-", WidthF = 130.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow6.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow6.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(cell.WorkflowStatus) ? cell.WorkflowStatus.ToString() : "-", WidthF = 130F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow6.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow6.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(cell.FailedActionName) ? cell.FailedActionName.ToString() : "-", WidthF = 140F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow6.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow6.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(cell.ErrorMessage) ? cell.FailedActionName.ToString() : "-", WidthF = 140F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkTable.Rows.Add(remarkRow6);

                            }

                        }
                        if (item.DrReadyListVm.DrDataLagExceeds.Count > 0)
                        {
                            remarkRow7.Cells.Add(new XRTableCell { Controls = { pictureBox5 }, WidthF = 30F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            remarkRow7.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(item.DrReadyListVm.DataLagExceedCount) ? item.DrReadyListVm.DataLagExceedCount.ToString() : "-", WidthF = 700.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            remarkTable.Rows.Add(remarkRow7);
                            remarkRow8.Cells.Add(new XRTableCell { Text = "InfraObject Name", WidthF = 160.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow8.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow8.Cells.Add(new XRTableCell { Text = "Operational Function Name", WidthF = 160.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow8.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow8.Cells.Add(new XRTableCell { Text = "Heatmap Status", WidthF = 130F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow8.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkRow8.Cells.Add(new XRTableCell { Text = "Failed Action Name", WidthF = 240F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), BackColor = System.Drawing.Color.FromArgb(237, 245, 255) });
                            remarkTable.Rows.Add(remarkRow8);
                            foreach (var cell in item.DrReadyListVm.DrDataLagExceeds)
                            {
                                XRTableRow remarkRow9 = new XRTableRow();
                                remarkRow9.Cells.Add(new XRTableCell { Text = (!string.IsNullOrEmpty(cell.InfraObjectName) ? cell.InfraObjectName.ToString() : "-"), WidthF = 160.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow9.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow9.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(cell.BusinessFunctionName) ? cell.BusinessFunctionName.ToString() : "-", WidthF = 160.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow9.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow9.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(cell.HeatmapStatus) ? cell.HeatmapStatus.ToString() : "-", WidthF = 130F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow9.Cells.Add(new XRTableCell { WidthF = 20F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkRow9.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(cell.ErrorMessage) ? cell.ErrorMessage.ToString() : "-", WidthF = 240F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                remarkTable.Rows.Add(remarkRow9);

                            }
                        }
                        mainRemark.Cells.Add(new XRTableCell { Controls = { mainTable}, WidthF = 341F });
                        mainRemark.Cells.Add(new XRTableCell { Controls = { remarkTable}, WidthF = 731F });
                        table.Rows.Add(mainRemark);
                        XRTableRow emptyRow = new XRTableRow();
                        emptyRow.Cells.Add(new XRTableCell { Text = " ", WidthF = 1073F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                        table.Rows.Add(emptyRow);
                    }

                };
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Resiliency Readiness Report. The error message : " + ex.Message); throw; }
        }
        public void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Resiliency Readiness Report's CP Version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(ServiceAvailabilityController.CompanyLogo) ? "NA" : ServiceAvailabilityController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
               catch (Exception ex) { _logger.LogError("Error occured while display the customer logo in Resiliency Readiness Report. The error message : " + ex.Message); throw; }
        
        }
    }
}