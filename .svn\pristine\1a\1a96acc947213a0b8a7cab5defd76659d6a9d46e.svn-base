﻿using ContinuityPatrol.Application.Features.AlertNotification.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertNotification.Commands;

public class CreateAlertNotificationTests : IClassFixture<AlertNotificationFixture>
{
    private readonly AlertNotificationFixture _alertNotificationFixture;
    private readonly Mock<IAlertNotificationRepository> _mockAlertNotificationRepository;
    private readonly CreateAlertNotificationCommandHandler _handler;

    public CreateAlertNotificationTests(AlertNotificationFixture alertNotificationFixture)
    {
        _alertNotificationFixture = alertNotificationFixture;

        _mockAlertNotificationRepository = AlertNotificationRepositoryMocks.CreateAlertNotificationRepository(_alertNotificationFixture.AlertNotifications);

        _handler = new CreateAlertNotificationCommandHandler(_alertNotificationFixture.Mapper, _mockAlertNotificationRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseAlertNotificationCount_When_AlertNotificationCreated()
    {
        await _handler.Handle(_alertNotificationFixture.CreateAlertNotificationCommand, CancellationToken.None);

        var allCategories = await _mockAlertNotificationRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_alertNotificationFixture.AlertNotifications.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateAlertNotificationResponse_When_AlertNotificationCreated()
    {
        var result = await _handler.Handle(_alertNotificationFixture.CreateAlertNotificationCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateAlertNotificationResponse));

        result.AlertNotificationId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_alertNotificationFixture.CreateAlertNotificationCommand, CancellationToken.None);

        _mockAlertNotificationRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.AlertNotification>()), Times.Once);
    }
}