using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SettingFixture : IDisposable
{
    public List<Setting> SettingPaginationList { get; set; }
    public List<Setting> SettingList { get; set; }
    public Setting SettingDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SettingFixture()
    {
        var fixture = new Fixture();

        SettingList = fixture.Create<List<Setting>>();

        SettingPaginationList = fixture.CreateMany<Setting>(20).ToList();

        SettingDto = fixture.Create<Setting>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public Setting CreateSetting(
        string sKey = "DefaultKey",
        string sValue = "DefaultValue",
        bool isActive = true,
        bool isDelete = false)
    {
        return new Setting
        {
            ReferenceId = Guid.NewGuid().ToString(),
            SKey = sKey,
            SValue = sValue,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
        };
    }

    public List<Setting> CreateMultipleSettings(int count)
    {
        var settings = new List<Setting>();
        for (int i = 1; i <= count; i++)
        {
            settings.Add(CreateSetting(
                sKey: $"Key{i}",
                sValue: $"Value{i}"
            ));
        }
        return settings;
    }

    public Setting CreateSettingWithSpecificId(string referenceId, string sKey = "TestKey", string sValue = "TestValue")
    {
        return new Setting
        {
            ReferenceId = referenceId,
            SKey = sKey,
            SValue = sValue,
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
        };
    }

    public Setting CreateJsonSetting(string sKey, object jsonObject, bool isActive = true)
    {
        return CreateSetting(
            sKey: sKey,
            sValue: Newtonsoft.Json.JsonConvert.SerializeObject(jsonObject),
            isActive: isActive
        );
    }

    public Setting CreateLogSetting(string sKey, string uiValue, bool isActive = true)
    {
        var jsonObject = new
        {
            UI = uiValue,
            Other = "Other data"
        };

        return CreateJsonSetting(sKey, jsonObject, isActive);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
