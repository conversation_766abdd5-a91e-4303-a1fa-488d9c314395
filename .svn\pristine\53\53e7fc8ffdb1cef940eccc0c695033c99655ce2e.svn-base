﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Setting.Events.Create;

public class SettingCreatedEventHandler : INotificationHandler<SettingCreatedEvent>
{
    private readonly ILogger<SettingCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public SettingCreatedEventHandler(ILoggedInUserService userService, ILogger<SettingCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(SettingCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            CompanyId = _userService.CompanyId,
            Action = $"{ActivityType.Create} {Modules.Setting}",
            Entity = Modules.Setting.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Setting '{createdEvent.SKey}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Setting '{createdEvent.SKey}' created successfully.");
    }
}