﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class InfraSummaryRepositoryMocks
{
    public static Mock<IInfraSummaryRepository> CreateInfraSummaryRepository(List<InfraSummary> infraSummaries)
    {
        var infraSummaryRepository = new Mock<IInfraSummaryRepository>();

        infraSummaryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraSummaries);

        infraSummaryRepository.Setup(repo => repo.AddAsync(It.IsAny<InfraSummary>())).ReturnsAsync(
            (InfraSummary infraSummary) =>
            {
                infraSummary.Id = new Fixture().Create<int>();
                infraSummary.ReferenceId = new Fixture().Create<Guid>().ToString();
                infraSummaries.Add(infraSummary);

                return infraSummary;
            });

        return infraSummaryRepository;
    }

    public static Mock<IInfraSummaryRepository> UpdateInfraSummaryRepository(List<InfraSummary> infraSummaries)
    {
        var infraSummaryRepository = new Mock<IInfraSummaryRepository>();

        infraSummaryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraSummaries);

        infraSummaryRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraSummaries.SingleOrDefault(x => x.ReferenceId == i));

        infraSummaryRepository.Setup(repo => repo.GetInfraSummaryByType(It.IsAny<string>())).ReturnsAsync((string i) => infraSummaries.SingleOrDefault(x => x.Type == i));

        infraSummaryRepository.Setup(repo => repo.UpdateAsync(It.IsAny<InfraSummary>())).ReturnsAsync((InfraSummary infraSummary) =>
        {
            var index = infraSummaries.FindIndex(item => item.ReferenceId == infraSummary.ReferenceId);

            infraSummaries[index] = infraSummary;

            return infraSummary;
        });

        return infraSummaryRepository;
    }

    public static Mock<IInfraSummaryRepository> DeleteInfraSummaryRepository(List<InfraSummary> infraSummaries)
    {
        var infraSummaryRepository = new Mock<IInfraSummaryRepository>();

        infraSummaryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraSummaries);

        infraSummaryRepository.Setup(repo => repo.GetInfraSummaryByType(It.IsAny<string>())).ReturnsAsync((string i) => infraSummaries.SingleOrDefault(x => x.ReferenceId == i));

        infraSummaryRepository.Setup(repo => repo.UpdateAsync(It.IsAny<InfraSummary>())).ReturnsAsync((InfraSummary infraSummary) =>
        {
            var index = infraSummaries.FindIndex(item => item.ReferenceId == infraSummary.ReferenceId);

            infraSummary.IsActive = false;
            infraSummaries[index] = infraSummary;

            return infraSummary;
        });

        return infraSummaryRepository;
    }

    public static Mock<IInfraSummaryRepository> GetInfraSummaryRepository(List<InfraSummary> infraSummaries)
    {
        var infraSummaryRepository = new Mock<IInfraSummaryRepository>();

        infraSummaryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraSummaries);

        infraSummaryRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraSummaries.SingleOrDefault(x => x.ReferenceId == i));

        return infraSummaryRepository;
    }

    public static Mock<IInfraSummaryRepository> GetInfraSummaryEmptyRepository()
    {
        var infraSummaryRepository = new Mock<IInfraSummaryRepository>();

        infraSummaryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<InfraSummary>());

        return infraSummaryRepository;
    }

    public static Mock<IInfraSummaryRepository> GetGetInfraSummaryByTypeAndBusinessServiceIdAndCompanyIdRepository(InfraSummary infraSummary)
    {
        var infraSummaryRepository = new Mock<IInfraSummaryRepository>();

        infraSummaryRepository.Setup(repo => repo.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(infraSummary);

        return infraSummaryRepository;
    }


    public static Mock<IInfraSummaryRepository> CreateDatabaseInfraSummaryEventRepository(List<InfraSummary> infraSummaries)
    {
        var infraSummaryRepository = new Mock<IInfraSummaryRepository>();

        infraSummaryRepository.Setup(repo => repo.AddAsync(It.IsAny<InfraSummary>())).ReturnsAsync(
            (InfraSummary infraSummary) =>
            {
                infraSummary.Id = new Fixture().Create<int>();
                infraSummary.ReferenceId = new Fixture().Create<Guid>().ToString();
                infraSummaries.Add(infraSummary);

                return infraSummary;
            });

        return infraSummaryRepository;
    }

    public static Mock<IInfraSummaryRepository> DeleteDatabaseInfraSummaryEventRepository(List<InfraSummary> infraSummaries)
    {
        var infraSummaryRepository = new Mock<IInfraSummaryRepository>();

        infraSummaryRepository.Setup(repo => repo.GetInfraSummaryByType(It.IsAny<string>())).ReturnsAsync((string i) => infraSummaries.SingleOrDefault(x => x.Type == i));

        infraSummaryRepository.Setup(repo => repo.UpdateAsync(It.IsAny<InfraSummary>())).ReturnsAsync((InfraSummary infraSummary) =>
        {
            var index = infraSummaries.FindIndex(item => item.ReferenceId == infraSummary.ReferenceId);

            infraSummary.IsActive = false;
            infraSummaries[index] = infraSummary;

            return infraSummary;
        });

        return infraSummaryRepository;
    }

    public static Mock<IInfraSummaryRepository> UpdateDatabaseInfraSummaryEventRepository(List<InfraSummary> infraSummaries)
    {
        var infraSummaryRepository = new Mock<IInfraSummaryRepository>();

        infraSummaryRepository.Setup(repo => repo.GetInfraSummaryByType(It.IsAny<string>())).ReturnsAsync((string i) => infraSummaries.SingleOrDefault(x => x.Type == i));

        infraSummaryRepository.Setup(repo => repo.UpdateAsync(It.IsAny<InfraSummary>())).ReturnsAsync((InfraSummary infraSummary) =>
        {
            var index = infraSummaries.FindIndex(item => item.ReferenceId == infraSummary.ReferenceId);

            infraSummaries[index] = infraSummary;

            return infraSummary;
        });

        return infraSummaryRepository;
    }
}