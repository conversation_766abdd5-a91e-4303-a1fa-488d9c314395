using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class MenuBuilderRepositoryTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly MenuBuilderRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly MenuBuilderFixture _fixture;
    private readonly Fixture _autoFixture;

    public MenuBuilderRepositoryTests()
    {
        _context = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _fixture = new MenuBuilderFixture();
        _autoFixture = new Fixture();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(MenuBuilderFixture.CompanyId);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        _repository = new MenuBuilderRepository(_context, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _context?.Dispose();
        _fixture?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _context.MenuBuilders.RemoveRange(_context.MenuBuilders);
        await _context.SaveChangesAsync();
    }

    #region IsNameExist Tests - Invalid GUID Scenarios

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalidGuid()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilder = _fixture.CreateMenuBuilderWithSpecificName("TestMenuBuilder");
        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("TestMenuBuilder", "INVALID_GUID");

        // Assert
        Assert.True(result); // Returns true when name exists and id is invalid GUID
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameNotExistsAndIdIsInvalidGuid()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsNameExist("NonExistentName", "INVALID_GUID");

        // Assert
        Assert.False(result); // Returns false when name doesn't exist and id is invalid GUID
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsNull()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilder = _fixture.CreateMenuBuilderWithSpecificName("TestMenuBuilder");
        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("TestMenuBuilder", null);

        // Assert
        Assert.True(result); // Returns true when name exists and id is null
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameNotExistsAndIdIsNull()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsNameExist("NonExistentName", null);

        // Assert
        Assert.False(result); // Returns false when name doesn't exist and id is null
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsEmptyString()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilder = _fixture.CreateMenuBuilderWithSpecificName("TestMenuBuilder");
        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("TestMenuBuilder", "");

        // Assert
        Assert.True(result); // Returns true when name exists and id is empty string
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameNotExistsAndIdIsEmptyString()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsNameExist("NonExistentName", "");

        // Assert
        Assert.False(result); // Returns false when name doesn't exist and id is empty string
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsWhitespace()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilder = _fixture.CreateMenuBuilderWithSpecificName("TestMenuBuilder");
        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("TestMenuBuilder", "   ");

        // Assert
        Assert.True(result); // Returns true when name exists and id is whitespace
    }

    #endregion

    #region IsNameExist Tests - Valid GUID Scenarios

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNoMenuBuildersWithNameAndIdIsValidGuid()
    {
        // Arrange
        await ClearDatabase();
        var validGuid = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsNameExist("NonExistentName", validGuid);

        // Assert
        Assert.False(result); // Returns false when no MenuBuilders with name exist (Unique extension returns false for empty list)
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenMultipleMenuBuildersWithSameNameAndIdIsValidGuid()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilder1 = _fixture.CreateMenuBuilderWithSpecificName("DuplicateName");
        menuBuilder1.ReferenceId = Guid.NewGuid().ToString();

        var menuBuilder2 = _fixture.CreateMenuBuilderWithSpecificName("DuplicateName");
        menuBuilder2.ReferenceId = Guid.NewGuid().ToString();

        await _context.MenuBuilders.AddRangeAsync(menuBuilder1, menuBuilder2);
        await _context.SaveChangesAsync();

        var validGuid = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsNameExist("DuplicateName", validGuid);

        // Assert
        Assert.True(result); // Returns true when multiple MenuBuilders with same name exist (Unique extension returns true for count > 1)
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenSingleMenuBuilderWithSameNameAndSameIdAsInput()
    {
        // Arrange
        await ClearDatabase();
        var referenceId = Guid.NewGuid().ToString();
        var menuBuilder = _fixture.CreateMenuBuilderWithSpecificName("TestMenuBuilder");
        menuBuilder.ReferenceId = referenceId;

        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("TestMenuBuilder", referenceId);

        // Assert
        Assert.False(result); // Returns false when single MenuBuilder with same name and same ReferenceId (Unique extension returns false)
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenSingleMenuBuilderWithSameNameButDifferentId()
    {
        // Arrange
        await ClearDatabase();
        var existingReferenceId = Guid.NewGuid().ToString();
        var differentReferenceId = Guid.NewGuid().ToString();
        
        var menuBuilder = _fixture.CreateMenuBuilderWithSpecificName("TestMenuBuilder");
        menuBuilder.ReferenceId = existingReferenceId;

        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("TestMenuBuilder", differentReferenceId);

        // Assert
        Assert.True(result); // Returns true when single MenuBuilder with same name but different ReferenceId (Unique extension returns true)
    }

    #endregion

    #region IsNameExist Tests - Edge Cases

    [Fact]
    public async Task IsNameExist_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilder = _fixture.CreateMenuBuilderWithSpecificName("TestMenuBuilder");
        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        var validGuid = Guid.NewGuid().ToString();

        // Act
        var result1 = await _repository.IsNameExist("testmenubuilder", validGuid); // lowercase
        var result2 = await _repository.IsNameExist("TESTMENUBUILDER", validGuid); // uppercase
        var result3 = await _repository.IsNameExist("TestMenuBuilder", validGuid); // exact case

        // Assert
        Assert.False(result1); // Different case - no match found
        Assert.False(result2); // Different case - no match found
        Assert.True(result3);  // Exact case - match found but different ID
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleSpecialCharactersInName()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilder = _fixture.CreateMenuBuilderWithSpecialCharacters();
        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(menuBuilder.Name, "INVALID_GUID");

        // Assert
        Assert.True(result); // Should handle special characters correctly
    }

    //[Fact]
    //public async Task IsNameExist_ShouldHandleEmptyNameString()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var menuBuilder = _fixture.CreateMenuBuilderWithEmptyName();
    //    await _context.MenuBuilders.AddAsync(menuBuilder);
    //    await _context.SaveChangesAsync();

    //    // Act
    //    var result = await _repository.IsNameExist("", "INVALID_GUID");

    //    // Assert
    //    Assert.True(result); // Should handle empty name string
    //}

    [Fact]
    public async Task IsNameExist_ShouldHandleWhitespaceInName()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilder = _fixture.CreateMenuBuilderWithWhitespace();
        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(menuBuilder.Name, "INVALID_GUID");

        // Assert
        Assert.True(result); // Should handle whitespace in name exactly
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleVeryLongNames()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilder = _fixture.CreateMenuBuilderWithLongName(1000);
        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(menuBuilder.Name, "INVALID_GUID");

        // Assert
        Assert.True(result); // Should handle very long names
    }

    #endregion

    #region IsNameExist Tests - GUID Validation Edge Cases

    [Fact]
    public async Task IsNameExist_ShouldTreatEmptyGuidAsInvalidGuid()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilder = _fixture.CreateMenuBuilderWithSpecificName("TestMenuBuilder");
        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("TestMenuBuilder", Guid.Empty.ToString());

        // Assert
        Assert.True(result); // Empty GUID should be treated as invalid GUID
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleValidGuidWithDifferentFormats()
    {
        // Arrange
        await ClearDatabase();
        var guid = Guid.NewGuid();
        var menuBuilder = _fixture.CreateMenuBuilderWithSpecificName("TestMenuBuilder");
        menuBuilder.ReferenceId = guid.ToString();
        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        // Act
        var result1 = await _repository.IsNameExist("TestMenuBuilder", guid.ToString()); // Standard format
        var result2 = await _repository.IsNameExist("TestMenuBuilder", guid.ToString("D")); // With hyphens
        var result3 = await _repository.IsNameExist("TestMenuBuilder", guid.ToString("N")); // Without hyphens

        // Assert
        Assert.False(result1); // Same GUID format - should match
        Assert.False(result2); // Same GUID with hyphens - should match
        Assert.True(result3);  // Different GUID format - should not match (string comparison)
    }

    #endregion

    #region IsNameExist Tests - Complex Business Scenarios

    [Fact]
    public async Task IsNameExist_ShouldHandleMultipleMenuBuildersWithDifferentNames()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilders = _fixture.CreateMultipleMenuBuilders(3, "MenuBuilder");
        await _context.MenuBuilders.AddRangeAsync(menuBuilders);
        await _context.SaveChangesAsync();

        var newGuid = Guid.NewGuid().ToString();

        // Act
        var result1 = await _repository.IsNameExist("MenuBuilder1", newGuid);
        var result2 = await _repository.IsNameExist("MenuBuilder4", newGuid);

        // Assert
        Assert.True(result1);  // Existing name with different ID - not unique
        Assert.False(result2); // New name - unique
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleInactiveMenuBuilders()
    {
        // Arrange
        await ClearDatabase();
        var activeMenuBuilder = _fixture.CreateMenuBuilderWithSpecificName("TestMenuBuilder");
        activeMenuBuilder.IsActive = true;

        var inactiveMenuBuilder = _fixture.CreateInactiveMenuBuilder("TestMenuBuilder");
        inactiveMenuBuilder.ReferenceId = Guid.NewGuid().ToString();

        await _context.MenuBuilders.AddRangeAsync(activeMenuBuilder, inactiveMenuBuilder);
        await _context.SaveChangesAsync();

        var newGuid = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsNameExist("TestMenuBuilder", newGuid);

        // Assert
        Assert.True(result); // Should find both active and inactive MenuBuilders (method doesn't filter by IsActive)
    }

    //[Fact]
    //public async Task IsNameExist_ShouldHandleNullNameInDatabase()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var menuBuilder = _fixture.CreateMenuBuilderWithNullName();
    //    await _context.MenuBuilders.AddAsync(menuBuilder);
    //    await _context.SaveChangesAsync();

    //    // Act
    //    var result = await _repository.IsNameExist(null, "INVALID_GUID");

    //    // Assert - This will depend on how EF Core handles null comparisons
    //    // Typically, null.Equals(null) in LINQ to Entities might not behave as expected
    //    // The test documents the actual behavior
    //    Assert.False(result); // Documenting expected behavior - may need adjustment based on EF Core behavior
    //}

    [Fact]
    public async Task IsNameExist_ShouldHandleCreateScenario()
    {
        // Arrange - Simulating creating a new MenuBuilder
        await ClearDatabase();
        var existingMenuBuilder = _fixture.CreateMenuBuilderWithSpecificName("ExistingMenuBuilder");
        await _context.MenuBuilders.AddAsync(existingMenuBuilder);
        await _context.SaveChangesAsync();

        // Act - Check if new name is unique (no ID provided, simulating create)
        var result1 = await _repository.IsNameExist("ExistingMenuBuilder", null);
        var result2 = await _repository.IsNameExist("NewMenuBuilder", null);

        // Assert
        Assert.True(result1);  // Existing name - not unique for create
        Assert.False(result2); // New name - unique for create
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleUpdateScenario()
    {
        // Arrange - Simulating updating an existing MenuBuilder
        await ClearDatabase();
        var existingMenuBuilder1 = _fixture.CreateMenuBuilderWithSpecificName("MenuBuilder1");
        existingMenuBuilder1.ReferenceId = Guid.NewGuid().ToString();

        var existingMenuBuilder2 = _fixture.CreateMenuBuilderWithSpecificName("MenuBuilder2");
        existingMenuBuilder2.ReferenceId = Guid.NewGuid().ToString();

        await _context.MenuBuilders.AddRangeAsync(existingMenuBuilder1, existingMenuBuilder2);
        await _context.SaveChangesAsync();

        // Act - Check if updated name is unique
        var result1 = await _repository.IsNameExist("MenuBuilder1", existingMenuBuilder1.ReferenceId); // Same name, same ID
        var result2 = await _repository.IsNameExist("MenuBuilder2", existingMenuBuilder1.ReferenceId); // Different name, same ID
        var result3 = await _repository.IsNameExist("NewName", existingMenuBuilder1.ReferenceId);    // New name, same ID

        // Assert
        Assert.False(result1); // Same name, same ID - unique (can keep same name)
        Assert.True(result2);  // Different existing name, same ID - not unique
        Assert.False(result3); // New name, same ID - unique
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleThreeMenuBuildersWithSameName()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilder1 = _fixture.CreateMenuBuilderWithSpecificName("SameName");
        menuBuilder1.ReferenceId = Guid.NewGuid().ToString();

        var menuBuilder2 = _fixture.CreateMenuBuilderWithSpecificName("SameName");
        menuBuilder2.ReferenceId = Guid.NewGuid().ToString();

        var menuBuilder3 = _fixture.CreateMenuBuilderWithSpecificName("SameName");
        menuBuilder3.ReferenceId = Guid.NewGuid().ToString();

        await _context.MenuBuilders.AddRangeAsync(menuBuilder1, menuBuilder2, menuBuilder3);
        await _context.SaveChangesAsync();

        var newGuid = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsNameExist("SameName", newGuid);

        // Assert
        Assert.True(result); // Multiple MenuBuilders with same name - not unique (Unique extension returns true for count > 1)
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleGuidCaseInsensitivity()
    {
        // Arrange
        await ClearDatabase();
        var guid = Guid.NewGuid();
        var menuBuilder = _fixture.CreateMenuBuilderWithSpecificName("TestMenuBuilder");
        menuBuilder.ReferenceId = guid.ToString().ToUpper(); // Store in uppercase
        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        // Act
        var result1 = await _repository.IsNameExist("TestMenuBuilder", guid.ToString().ToLower()); // Query with lowercase
        var result2 = await _repository.IsNameExist("TestMenuBuilder", guid.ToString().ToUpper()); // Query with uppercase

        // Assert
        Assert.True(result1);  // Different case GUID - should not match (string comparison is case-sensitive)
        Assert.False(result2); // Same case GUID - should match
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleUnicodeCharacters()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilder = _fixture.CreateMenuBuilderWithUnicodeCharacters();
        await _context.MenuBuilders.AddAsync(menuBuilder);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(menuBuilder.Name, "INVALID_GUID");

        // Assert
        Assert.True(result); // Should handle Unicode characters correctly
    }

    [Fact]
    public async Task IsNameExist_ShouldHandlePerformanceWithManyMenuBuilders()
    {
        // Arrange
        await ClearDatabase();
        var menuBuilders = _fixture.CreateMultipleMenuBuilders(100, "MenuBuilder");

        // Add one with duplicate name
        var duplicateMenuBuilder = _fixture.CreateMenuBuilderWithSpecificName("MenuBuilder50");
        duplicateMenuBuilder.ReferenceId = Guid.NewGuid().ToString();
        menuBuilders.Add(duplicateMenuBuilder);

        await _context.MenuBuilders.AddRangeAsync(menuBuilders);
        await _context.SaveChangesAsync();

        var newGuid = Guid.NewGuid().ToString();

        // Act
        var result1 = await _repository.IsNameExist("MenuBuilder50", newGuid); // Duplicate name
        var result2 = await _repository.IsNameExist("MenuBuilder999", newGuid); // Unique name

        // Assert
        Assert.True(result1);  // Duplicate name found - not unique
        Assert.False(result2); // Unique name - unique
    }

    #endregion

    #region IsNameExist Tests - Error Handling

    [Fact]
    public async Task IsNameExist_ShouldHandleNullNameParameter()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert - Should not throw exception
        var result1 = await _repository.IsNameExist(null, "INVALID_GUID");
        var result2 = await _repository.IsNameExist(null, Guid.NewGuid().ToString());

        // Assert - Document the behavior (may need adjustment based on actual EF Core behavior)
        Assert.False(result1); // null name with invalid GUID
        Assert.False(result2); // null name with valid GUID
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTaskSuccessfully()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var task = _repository.IsNameExist("TestName", "INVALID_GUID");

        // Assert
        Assert.NotNull(task);
        Assert.True(task.IsCompleted);
        var result = await task;
        Assert.False(result); // No MenuBuilders exist
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleCommonMenuBuilderNames()
    {
        // Arrange
        await ClearDatabase();
        var commonNames = MenuBuilderFixture.TestData.CommonMenuBuilderNames;
        var menuBuilders = new List<MenuBuilder>();

        foreach (var name in commonNames)
        {
            var menuBuilder = _fixture.CreateMenuBuilderWithSpecificName(name);
            menuBuilders.Add(menuBuilder);
        }

        await _context.MenuBuilders.AddRangeAsync(menuBuilders);
        await _context.SaveChangesAsync();

        var newGuid = Guid.NewGuid().ToString();

        // Act & Assert
        foreach (var name in commonNames)
        {
            var result = await _repository.IsNameExist(name, newGuid);
            Assert.True(result); // All common names should exist
        }

        var uniqueResult = await _repository.IsNameExist("Unique Menu Name", newGuid);
        Assert.False(uniqueResult); // Unique name should not exist
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleSpecialCharacterNames()
    {
        // Arrange
        await ClearDatabase();
        var specialNames = MenuBuilderFixture.TestData.SpecialCharacterNames;
        var menuBuilders = new List<MenuBuilder>();

        foreach (var name in specialNames)
        {
            var menuBuilder = _fixture.CreateMenuBuilderWithSpecificName(name);
            menuBuilders.Add(menuBuilder);
        }

        await _context.MenuBuilders.AddRangeAsync(menuBuilders);
        await _context.SaveChangesAsync();

        var newGuid = Guid.NewGuid().ToString();

        // Act & Assert
        foreach (var name in specialNames)
        {
            var result = await _repository.IsNameExist(name, newGuid);
            Assert.True(result); // All special character names should exist
        }
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleUnicodeNames()
    {
        // Arrange
        await ClearDatabase();
        var unicodeNames = MenuBuilderFixture.TestData.UnicodeNames;
        var menuBuilders = new List<MenuBuilder>();

        foreach (var name in unicodeNames)
        {
            var menuBuilder = _fixture.CreateMenuBuilderWithSpecificName(name);
            menuBuilders.Add(menuBuilder);
        }

        await _context.MenuBuilders.AddRangeAsync(menuBuilders);
        await _context.SaveChangesAsync();

        var newGuid = Guid.NewGuid().ToString();

        // Act & Assert
        foreach (var name in unicodeNames)
        {
            var result = await _repository.IsNameExist(name, newGuid);
            Assert.True(result); // All Unicode names should exist
        }
    }

    #endregion
}
