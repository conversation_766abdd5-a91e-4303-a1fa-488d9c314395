﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.SettingModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.Setting.Queries.GetPaginatedList;

public class
    GetSettingPaginatedListQueryHandler : IRequestHandler<GetSettingPaginatedListQuery,
        PaginatedResult<SettingListVm>>
{
    private readonly IMapper _mapper;
    private readonly ISettingRepository _settingRepository;

    public GetSettingPaginatedListQueryHandler(IMapper mapper, ISettingRepository settingRepository)
    {
        _mapper = mapper;
        _settingRepository = settingRepository;
    }

    public async Task<PaginatedResult<SettingListVm>> Handle(GetSettingPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _settingRepository.GetPaginatedQuery();

        var productFilterSpec = new SettingFilterSpecification(request.SearchString);

        var settingsList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<SettingListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return settingsList;
    }
}