using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class BackUpLogRepository : BaseRepository<BackUpLog>, IBackUpLogRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public BackUpLogRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public async Task<bool> IsNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
        {
            return await Entities.AnyAsync(e => e.DatabaseName == name);
        }

        var entities = await Entities.Where(e => e.DatabaseName == name).ToListAsync();
        return entities.Unique(id);
    }

    public override async Task<PaginatedResult<BackUpLog>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<BackUpLog> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await Entities.Specify(productFilterSpec).DescOrderById().Select(x => new BackUpLog
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            HostName = x.HostName,
            DatabaseName = x.DatabaseName,
            UserName = x.UserName,
            IsLocalServer = x.IsLocalServer,
            IsBackUpServer = x.IsBackUpServer,
            BackUpPath = x.BackUpPath,
            Type = x.Type,
            Status = x.Status,
            CreatedDate = x.CreatedDate,
            LastModifiedDate = x.LastModifiedDate,
            LastModifiedBy = x.LastModifiedBy,
            CreatedBy = x.CreatedBy
        }).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
}
