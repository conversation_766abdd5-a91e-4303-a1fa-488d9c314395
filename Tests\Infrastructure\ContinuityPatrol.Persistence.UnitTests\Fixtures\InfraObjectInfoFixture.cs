using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class InfraObjectInfoFixture : IDisposable
{
    public List<InfraObjectInfo> InfraObjectInfoPaginationList { get; set; }
    public List<InfraObjectInfo> InfraObjectInfoList { get; set; }
    public InfraObjectInfo InfraObjectInfoDto { get; set; }

    public const string InfraObjectId = "INFRA_123";
    public const string InfraObjectName = "Test Infrastructure Object";

    public ApplicationDbContext DbContext { get; private set; }

    public InfraObjectInfoFixture()
    {
        var fixture = new Fixture();

        InfraObjectInfoList = fixture.Create<List<InfraObjectInfo>>();

        InfraObjectInfoPaginationList = fixture.CreateMany<InfraObjectInfo>(20).ToList();

        // Setup proper test data for InfraObjectInfoPaginationList
        InfraObjectInfoPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraObjectInfoPaginationList.ForEach(x => x.IsActive = true);
        InfraObjectInfoPaginationList.ForEach(x => x.InfraObjectId = InfraObjectId);
        InfraObjectInfoPaginationList.ForEach(x => x.InfraObjectName = InfraObjectName);

        // Setup proper test data for InfraObjectInfoList
        InfraObjectInfoList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraObjectInfoList.ForEach(x => x.IsActive = true);
        InfraObjectInfoList.ForEach(x => x.InfraObjectId = InfraObjectId);
        InfraObjectInfoList.ForEach(x => x.InfraObjectName = InfraObjectName);

        InfraObjectInfoDto = fixture.Create<InfraObjectInfo>();
        InfraObjectInfoDto.ReferenceId = Guid.NewGuid().ToString();
        InfraObjectInfoDto.IsActive = true;
        InfraObjectInfoDto.InfraObjectId = InfraObjectId;
        InfraObjectInfoDto.InfraObjectName = InfraObjectName;
        InfraObjectInfoDto.PreviousState = "Offline";
        InfraObjectInfoDto.CurrentState = "Online";

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
