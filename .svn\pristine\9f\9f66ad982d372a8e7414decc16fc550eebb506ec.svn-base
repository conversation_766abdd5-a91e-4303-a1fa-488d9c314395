using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class AboutCPRepositoryTests : IClassFixture<AboutCpFixture>
{
    private readonly AboutCpFixture _aboutCpFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly AboutCPRepository _repository;

    public AboutCPRepositoryTests(AboutCpFixture aboutCpFixture)
    {
        _aboutCpFixture = aboutCpFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new AboutCPRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        var aboutCp = _aboutCpFixture.AboutCpDto;
        var result = await _repository.AddAsync(aboutCp);

        Assert.NotNull(result);
        Assert.Equal(aboutCp.ProductId, result.ProductId);
        Assert.Single(_dbContext.AboutCp);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        var aboutCp = _aboutCpFixture.AboutCpDto;
        await _repository.AddAsync(aboutCp);

        aboutCp.ProductVersion = "2.0";
        var result = await _repository.UpdateAsync(aboutCp);

        Assert.Equal("2.0", result.ProductVersion);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        await Assert.ThrowsAsync<System.NullReferenceException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        var aboutCp = _aboutCpFixture.AboutCpDto;
        await _repository.AddAsync(aboutCp);

        var result = await _repository.DeleteAsync(aboutCp);

        Assert.Equal(aboutCp.ProductId, result.ProductId);
        Assert.Empty(_dbContext.AboutCp);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        var aboutCps = _aboutCpFixture.AboutCpList;
        var result = await _repository.AddRangeAsync(aboutCps);

        Assert.Equal(aboutCps.Count, result.Count());
        Assert.Equal(aboutCps.Count, _dbContext.AboutCp.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        var aboutCps = _aboutCpFixture.AboutCpList;
        await _repository.AddRangeAsync(aboutCps);

        var result = await _repository.RemoveRangeAsync(aboutCps);

        Assert.Equal(aboutCps.Count, result.Count());
        Assert.Empty(_dbContext.AboutCp);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    [Fact]
    public void Get_ShouldReturnEntity_WhenExists()
    {
        var aboutCp = _aboutCpFixture.AboutCpDto;
        _dbContext.AboutCp.Add(aboutCp);
        _dbContext.SaveChanges();

        var result = _repository.GetByReferenceIdAsync(aboutCp.ReferenceId);

        Assert.NotNull(result);
        Assert.Equal(aboutCp.ReferenceId, result?.Result?.ReferenceId);
    }

    [Fact]
    public void Get_ShouldReturnNull_WhenNotExists()
    {
        var result = _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");
        Assert.Null(result?.Result);
    }

 
}