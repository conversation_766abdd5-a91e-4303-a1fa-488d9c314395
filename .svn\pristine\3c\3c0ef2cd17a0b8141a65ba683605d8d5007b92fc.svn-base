﻿using ContinuityPatrol.Application.Features.Alert.Events.HeatMapStatusEvents.Update;
using ContinuityPatrol.Application.Features.Alert.Events.UserLoginEvents.Create;

namespace ContinuityPatrol.Application.Features.Alert.Commands.Create;

public class CreateAlertCommandHandler : IRequestHandler<CreateAlertCommand, CreateAlertResponse>
{
    private readonly IAlertRepository _alertRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateAlertCommandHandler(IAlertRepository alertRepository, IMapper mapper, IPublisher publisher)
    {
        _alertRepository = alertRepository;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<CreateAlertResponse> Handle(CreateAlertCommand request, CancellationToken cancellationToken)
    {
        var alert = _mapper.Map<Domain.Entities.Alert>(request);

        await _alertRepository.AddAsync(alert);

        var response = new CreateAlertResponse
        {
            Message = Message.Create(nameof(Domain.Entities.Alert), alert.InfraObjectId),
            AlertId = alert.ReferenceId
        };

        await _publisher.Publish(
            new UserLoginCreatedEvent { InfraObjectId = alert.InfraObjectId, AlertId = alert.Id },
            cancellationToken);
        await _publisher.Publish(
            new HeatMapStatusUpdatedEvent
            {
                InfraObjectId = alert.InfraObjectId,
                InfraObjectName = alert.InfraObjectName,
                EntityId = alert.EntityId,
                HeatmapType = alert.EntityType,
                ErrorMessage = alert.SystemMessage
            }, cancellationToken);
        return response;
    }
}