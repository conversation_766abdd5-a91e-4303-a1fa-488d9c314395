﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class DataSetColumnControllerTests
    {
        private readonly DataSetColumnController _controller;

        public DataSetColumnControllerTests()
        {
            
            _controller = new DataSetColumnController();
        }

        [Fact]
        public void List_ShouldReturnViewResult()
        {
            
            var result = _controller.List() as ViewResult;

            
            Assert.NotNull(result); 
            
        }
    }
}
