﻿@using ContinuityPatrol.Domain.ViewModels.FourEyeApproversModel;
@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.FourEyeApproversModel.FourEyeApproversVm

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title" title="FourEye Approvers"><i class="cp-password-visible"></i><span>FourEye Approvers List</span></h6>
            <ul class="nav nav-pills approval_request gap-2" id="pills-tab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active px-2 py-1" id="pills-all-tab" data-bs-toggle="pill" data-bs-target="#pills-all" type="button" role="tab" aria-controls="pills-all" aria-selected="true">
                        All
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link px-2 py-1" id="pills-workflow-tab" data-bs-toggle="pill" data-bs-target="#pills-workflow" type="button" role="tab" aria-controls="pills-workflow" aria-selected="false">
                        Workflow
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link px-2 py-1" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">
                        Profile
                    </button>
                </li>
            </ul>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" >
                                <i class="cp-filter"></i>
                            </span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li>
                                    <h6 class="dropdown-header">Filter Search</h6>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                </li>

                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm" id="btnCreate" data-bs-toggle="modal" data-bs-target="#CreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="card-body pt-0">
            <div class="tab-content" id="pills-tabContent">
                <div class="tab-pane show active" id="pills-all" role="tabpanel"
                     aria-labelledby="pills-all-tab" tabindex="0">

                    <table class="table table-hover dataTable" style="width:100%">
                        <thead>
                            <tr>
                                <th title="Sr.No" class="text-center">Sr.No</th>
                                <th title="Profile/Workflow Name">Profile/Workflow Name</th>
                                <th title="Action Type">ActionType</th>
                                <th title="Approvers">Approvers</th>
                            </tr>
                        </thead>
                        <tbody id="tablebodyall">
                            @{
                                int i = 1;
                            }
                            @foreach (var approvers in Model.PaginatedApprovers.Data)
                            {
                                <tr>
                                    <td>
                                        <span>@i</span>
                                    </td>
                                    <td>

                                        @approvers.workflow_profile
                                    </td>
                                    <td>@approvers.ActionType</td>
                                    <td>@approvers.Approvers</td>
                                </tr>
                                i++;
                            }
                        </tbody>
                    </table>
                </div>
                <div class="tab-pane" id="pills-workflow" role="tabpanel"
                     aria-labelledby="pills-workflow-tab" tabindex="0">
                    <table class="datatable table table-hover  no-footer" style="width:100%">
                        <thead>
                            <tr>
                                <th title="Sr.No" class="text-center">Sr.No</th>
                                <th title="Workflow Name">Workflow Name</th>
                                <th title="Action Type">ActionType</th>
                                <th title="Approvers">Approvers</th>
                            </tr>
                        </thead>
                        <tbody id="tablebodyworkflow">
                            @{
                                int j = 1;
                            }
                            @foreach (var approvers in Model.PaginatedApprovers_Workflow.Data)
                            {
                                <tr>
                                    <td>
                                        <span>@j</span>
                                    </td>
                                    <td>

                                        @approvers.workflow_profile
                                    </td>
                                    <td>@approvers.ActionType</td>
                                    <td>@approvers.Approvers</td>
                                </tr>
                                j++;
                            }
                        </tbody>
                    </table>
                </div>
                <div class="tab-pane" id="pills-profile" role="tabpanel"
                     aria-labelledby="pills-profile-tab" tabindex="0">
                    <table class="datatable table table-hover  no-footer" style="width:100%">
                        <thead>
                            <tr>
                                <th title="Sr.No" class="text-center">Sr.No</th>
                                <th title="profile Name">profile Name</th>
                                <th title="Action Type">ActionType</th>
                                <th title="Approvers">Approvers</th>
                            </tr>
                        </thead>
                        <tbody id="tablebodyprofile">
                            @{
                                int k = 1;
                            }
                            @foreach (var approvers in Model.PaginatedApprovers_Profile.Data)
                            {
                                <tr>
                                    <td>
                                        <span>@k</span>
                                    </td>
                                    <td>
                                        @approvers.workflow_profile
                                    </td>
                                    <td>@approvers.ActionType</td>
                                    <td>@approvers.Approvers</td>
                                </tr>
                                k++;
                            }
                        </tbody>
                    </table>
                </div>

            </div>

        </div>
    </div>

</div>

<!--Modal Create-->
@* <div class="modal fade" id="CreateModals" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Configuration" model="@Model" />
</div>  *@


<!-- Modal -->

<form id="CreateApprovalform" asp-controller="FourEyeApprovers" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data">
    <div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">

        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title" title="Privilege Configuration"><i class="cp-password-visible"></i><span>FourEye-Approver Configuration</span></h6>
                    <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                    <div class="form-group">
                        <div class="form-label" title="User">User Group</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-user"></i></span>
                            <select asp-for="Approvers" class="form-select-modal" id="grpselect" data-placeholder="Select User Group" multiple>
                                <option></option>
                                @foreach (var _workflowprofilelist in Model.UserGroups)
                                {
                                    <option id="@_workflowprofilelist.Id" value="@_workflowprofilelist.GroupName">@_workflowprofilelist.GroupName</option>
                                }
                            </select>

                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label" title="Action">Action</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-Actions"></i></span>
                            <div class="form-check form-check-inline">
                                @Html.RadioButtonFor(m => m.ActionType, "Modification" , htmlAttributes: new { @class = "form-check-input" , name="inlineRadioOptions", id="Modification", value="Modification"})
                                @* <input class="form-check-input" type="radio" name="inlineRadioOptions" id="Modification" value="Modification"> *@
                                <label class="form-check-label" for="Modification">Modification</label>
                            </div>
                            <div class="form-check form-check-inline">
                                @Html.RadioButtonFor(m => m.ActionType, "Execution" , htmlAttributes: new { @class = "form-check-input" , name="inlineRadioOptions", id="Execution", value="Execution"})
                                @* <input class="form-check-input" type="radio" name="inlineRadioOptions" id="Execution" value="Execution"> *@
                                <label class="form-check-label" for="Execution">Execution</label>
                            </div>


                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label" title="Workflow List">Workflow List</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-workflow"></i></span>
                            <select asp-for="Workflows" class="form-select-modal" id="mySelect" data-placeholder="Select Workflow List" multiple>
                                <option value="all" id="data" class="select-all-option">Select All</option>
                                @foreach (var _workflowprofilelist in Model.PaginatedWorkflows.Data)
                                {
                                    <option id="@_workflowprofilelist.Id" value="@_workflowprofilelist.Name">@_workflowprofilelist.Name</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label" title="Profile List">Profile List</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-LDAP"></i></span>
                            <select asp-for="WorkflowsProfile" class="form-select-modal" id="mySelects" data-placeholder="Select Profile List" multiple>
                                <option value="all" id="data" class="select-all-option">Select All</option>
                                @foreach (var _workflowprofilelist in Model.PaginatedWorkflowsProfile.Data)
                                {
                                    <option id="@_workflowprofilelist.Id" value="@_workflowprofilelist.Name">@_workflowprofilelist.Name</option>
                                }
                            </select>
                        </div>
                    </div>

                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" title="Cancel" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" title="Save" id="btnSave_1">Save</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</form>






@section Scripts
    {
    <partial name="_ValidationScriptsPartial" />

    <script type="text/javascript">
        var RootUrl = '@Url.Content("~/")';

        $(document).ready(function () {
            $("#btnSave_1").on("click", function () {
                // alert("Handler for `click` called.");
                $('#CreateApprovalform').submit();
            });

            $('#mySelect,#mySelects', '#grpselect').select2();

            $('#mySelect,#mySelects', '#grpselect').on('select2:select', async function (e) {
                debugger
                if (e.params.data && e.params.data.id === 'all') {

                    $('#' + e.target.id).find('option').prop("selected", true);

                    let values = $('#mySelect').val();
                    let update = await values.filter((item) => item !== "all");

                    $('#' + e.target.id).val(update).trigger('change');
                    $("#data").text("unselect all")
                }
            });



            $('#mySelect,#mySelects', '#grpselect').on('select2:unselect', function (e) {
                debugger
                if (e.params.data && e.params.data.id === 'all') {

                    $('#' + e.target.id).val(null).trigger('change');
                }
            });

            // $('#btnSave').click(async function () {
            //     console.log("Btnsave clicked");

            //     $('#CreateApprovalform').submit();

            //     $("#CreateApprovalform").submit();

            // });


        });

    </script>
    @* <script src="~/js/foureyeapprovers/foureyeapprovers.js"></script> *@


}


