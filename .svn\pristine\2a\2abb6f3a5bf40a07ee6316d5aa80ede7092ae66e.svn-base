﻿let btndisabled = false
let selectedValues = [];
let DeleteId = "", reportQuery = [], dataTable = [];

$(function () {
    let Permission = {
        'Create': $("#manageAlertCreatePermission").data("create-permission").toLowerCase(),
        'Delete': $("#manageAlertDeletePermission").data("delete-permission").toLowerCase()

    }
    let alertURL = {
        nameExistUrl: "Alert/ManageAlert/IsAlertNameExist",
        idExistUrl: "Alert/ManageAlert/IsAlertIdExist",
        getPagination: "/Alert/ManageAlert/GetPagination",
        postAction: "Alert/AlertDashboard/GetEsclationMatrixOwnerBYMatrixID",
        createOrUpdate: "Alert/ManageAlert/CreateOrUpdate",
        postActUrl: "Alert/AlertDashboard/GetEsclationMatrixLevelBYMatrixID",
        postActionUrl: "Alert/AlertDashboard/GetEsclationMatrixList",
        loadReport: "/Alert/ManageAlert/LoadReport",
        updateAlertMaster: "/Alert/AlertDashboard/UpdateAlertMaster",
        list: "/Alert/ManageAlert/List"
    }
    if (Permission.Create == 'false') {
        $(".createBtn").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }

    dataTable = $('#manageAlertMasterTable').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-right-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
            },
            infoFiltered: ""
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        "deferRender": true,
        "scroller": true,
        "processing": true,
        "serverSide": true,
        "filter": true,
        "order": [],
        "ajax": {
            "type": "GET",
            "url": alertURL.getPagination,
            "dataType": "json",
            "data": function (d) {
                let sortIndex = d?.order?.[0]?.column;
                let sortValue = ["", "alertId", "alertName", "alertMessage", "alertPriority", "alertFrequency"][sortIndex] || "";
                let orderValue = d.order?.[0]?.dir || 'asc';

                d.sortColumn = sortValue;
                d.SortOrder = orderValue;
                d.PageNumber = Math.ceil(d.start / d.length) + 1;
                d.pageSize = d.length;
                d.searchString = selectedValues.length === 0 ? $('#manageAlertSearch').val() : selectedValues.join(';');
                d.alertPriority = $("#manageAlertPriorityList").val() === "All" ? '' : $("#manageAlertPriorityList").val();
                selectedValues.length = 0;
                reportQuery = d;
            },
            "dataSrc": function (json) {
                if (json?.success && json?.data?.data && Array.isArray(json.data.data)) {
                    let alertData = json.data.data;
                    $("#manageAlertPriorityList option").each(function () {
                        $(this).siblings(`[value="${this.value}"]`).remove();
                    });
                    json.recordsTotal = json.data.totalPages;
                    json.recordsFiltered = json.data.totalCount;
                    $(".pagination-column").toggleClass("disabled", alertData.length === 0);
                    $("#btnManageAlertDownload").toggleClass("disabled", alertData.length === 0);
                    return alertData;
                } else {
                    errorNotification(json);
                }
            }
        },
        columnDefs: [
            { targets: [1, 2, 3, 4, 5, 6, 7, 8, 9], className: "truncate" }
        ],
        columns: [
            {
                "data": null,
                "name": "Sr. No.",
                "orderable": false,
                "render": (data, type, row, meta) => type === 'display' ? meta.row + 1 : data
            },
            {
                "data": "alertId",
                "render": data => `<span title="${data}">${data}</span>`
            },
            {
                "data": "alertName",
                "render": data => `<span title="${data}">${data}</span>`
            },
            {
                "data": "alertMessage",
                "render": data => `<span title="${data}">${data}</span>`
            },
            {
                "data": "alertPriority",
                "render": data => {
                    const iconMap = {
                        High: "fw-bold cp-up-doublearrow text-warning",
                        Critical: "fw-bold cp-critical-level text-danger",
                        Information: "cp-warning text-primary",
                        Low: "fw-bold cp-down-doublearrow text-success"
                    };
                    return `<i class="me-1 ${iconMap[data]}"></i> ${data}`;
                }
            },
            {
                "data": "alertFrequency",
                "render": data => `<span>${data}</span>`
            },
            {
                "data": "isSendSMS",
                "orderable": false,
                "render": data => {
                    const isChecked = data === true || data === "true";
                    const iconColorClass = isChecked ? "text-success" : "text-danger";
                    return `<i class="cp-sms-gateway ${iconColorClass}"></i>`;
                }
            },
            {
                "data": "isSendEmail",
                "orderable": false,
                "render": data => {
                    const isChecked = data === true || data === "true";
                    const iconColorClass = isChecked ? "text-success" : "text-danger";
                    return `<i class="cp-email ${iconColorClass}"></i>`;
                }
            },
            //{
            //    "data": null,
            //    "orderable": false,
            //    "render": data => `<input type="checkbox" class="attachCheckbox" name="Escalationcheckbox" ${data.isAcknowledgement ? 'checked' : ''} id="${data.id}" ${Permission.Create === 'false' ? 'disabled' : ''}>`
            //},
            //{
            //    "data": null,
            //    "orderable": false,
            //    "render": data => {
            //        const disabled = !data.isAcknowledgement ? 'disabled' : '';
            //        return `<button class="btn btn-primary btn-sm btnMangeAlertESCMatrixList fs-8" style="padding:2px 5px;" ${disabled} data-bs-target="#manageAlertEscalationModal" data-bs-toggle="modal" data-esc-id="${data.id}" data-esc-ack="${data.isAcknowledgement}" data-esc-matidd="${data.escMatId}" id="attbtn${data.id}">Attach Escalation</button>`;
            //    }
            //},
            {
                "data": "isAlertActive",
                "orderable": false,
                "render": data => {
                    const isChecked = data === true || data === "true";
                    const iconColorClass = isChecked ? "text-success" : "text-danger";
                    return `<i class="cp-active-inactive ${iconColorClass}"></i>`;
                }
            },
            {
                "data": null,
                "orderable": false,
                "render": function (data, type, row) {
                    const isEditAllowed = Permission.Create === "true";
                    const isDeleteAllowed = Permission.Delete === "true";
                    const editBtn = isEditAllowed ? `<span class="btnManageAlertEdit" role="button" title="Edit" data-manageAlertEdit='${btoa(JSON.stringify(row))}'><i class="cp-edit"></i> </span>`
                        : `<span class="icon-disabled" title="Edit"><i class="cp-edit"></i></span>`;

                    const deleteBtn = isDeleteAllowed ? `<span role="button" title="Delete" class="btnManageAlertDelete" data-alertDeleteId="${row.id}" data-alertDeleteName="${row.alertName}"> <i class="cp-Delete"></i> </span>`
                        : `<span class="icon-disabled" title="Delete"><i class="cp-Delete"></i></span>`;

                    const historyBtn = `<span role="button" title="View History" class="btnManageAlertHistory" data-manageAlertHistory='${btoa(JSON.stringify(row))}'><i class="cp-time"></i></span>`;
                    return `<td class="Action-th text-truncate"><div class="d-flex align-items-center gap-2">${editBtn}${deleteBtn}${historyBtn}</div></td>`;
                }
            }
        ]
    });

    function managealertdebounce(func, timeout = 300) {
        let timer;
        return (...args) => {
            clearTimeout(timer);
            timer = setTimeout(() => { func.apply(this, args); }, timeout);
        };
    }

    $('#manageAlertSearch').on('keydown input', managealertdebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const inputValue = $('#manageAlertSearch').val();
        selectedValues.push(inputValue);
        let currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {

                if (e.target.value && json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }
    }, 500));

    $("#manageAlertPriorityList").on("change", function () {
        dataTable.ajax.reload()
    })

    async function ClearFiaTemplateErrorElements() {
        $("#alert_id_error,#message_error,#alert_name_error,#alert_message_error,#alert_Priority_error,#alert_count_error,#alert_time_error").text('').removeClass('field-validation-error');
    };

    async function downloadManageAlert(blob, fileName, contentType) {
        try {
            const link = document.createElement("a");
            link.download = fileName;
            link.href = window.URL.createObjectURL(blob);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            errorNotification(error.message)
        }
    };

    async function EnabledIDandMessage(enabled) {
        if ($("#btnSaveManageAlert").text() != "Update" && enabled) {
            $('#manageAlertId').on('keypress', function (event) {
                const alert_id = $('#manageAlertId').val();
                if (alert_id.length > 4) {
                    event.preventDefault()
                    event.stopPropagation()
                }
                ["e", "E", "+", "-", "."].includes(event.key) && event.preventDefault()
            })
            $('#manageAlertMessage').on('keypress', async function (event) {
                const value = $('#manageAlertMessage').val();
                if (value.length > 499) {
                    event.preventDefault()
                    event.stopPropagation()
                }
            })
            $('#manageAlertId').on('input', managealertdebounce(async function () {
                let value = await sanitizeInput($('#manageAlertId').val());
                $("#manageAlertId").val(value);
                const errorElement = $('#alert_id_error');
                if (value == 0 || value == "-1" || value.length > 5) {
                    $('#manageAlertId').val("")
                }
                if (!value || value.length === 0) {
                    validateID($("#manageAlertId").val(), "Enter alert ID", errorElement, alertURL.idExistUrl);
                } else {
                    validateID($("#manageAlertId").val(), "Enter 5 digits only", errorElement, alertURL.idExistUrl);
                }
            }, 400))
            $('#manageAlertMessage').on('input', async function () {
                const value = $(this).val();
                const errorElement = $('#alert_message_error');
                let sanitizedValue = value.replace(/\s{2,}/g, ' ');
                $(this).val(sanitizedValue);
                validateAlertFields(value, "Enter alert message", errorElement);
            })
        }
    }
    function validateAlertFields(value, errorMsg, errorElement) {
        const isValid = typeof value === "string" && value.trim().length > 0;

        if (!isValid) {
            errorElement.text(errorMsg).addClass('field-validation-error');
        } else {
            errorElement.text('').removeClass('field-validation-error');
        }

        return isValid;
    }

    async function validateAlertName(value, errorMsg, errorElement, url) {
        if (!value || value.length === 0) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        let URL = RootUrl + url;
        let data = {
            alertId: null,
            alertName: value
        };

        const validationResults = [
            SpecialCharValidateCustom(value),
            ShouldNotBeginWithUnderScore(value),
            OnlyNumericsValidate(value),
            minMaxlength(value),
            ShouldNotBeginWithSpace(value),
            ShouldNotBeginWithNumber(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            secondChar(value),
            await IsSameNameExist(URL, data)
        ];
        const failedValidations = validationResults.filter(result => result !== true);

        if (failedValidations.length > 0) {
            errorElement.text(failedValidations[0]).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }

    async function IsSameNameExist(url, inputValue) {
        return !inputValue.alertName.trim() || $("#btnSaveManageAlert").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Alert name already exists" : true;
    }

    async function validateID(value, errorMsg, errorElement, url) {
        if (!value || value.length === 0) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        } else if (value.length < 5) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        }
        let URL = RootUrl + url;
        let data = { alertId: value };
        const validationResults = [
            await IsSameIdExist(URL, data)
        ];

        const failedValidations = validationResults.filter(result => result !== true);

        if (failedValidations.length > 0) {
            errorElement.text(failedValidations[0]).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }

    async function IsSameIdExist(url, inputValue) {
        return !inputValue.alertId.trim() ? true : (await GetAsync(url, inputValue, OnError)) ? "Alert ID already exists" : true;
    }

    async function validatecountDropDown(value, errorMsg, errorElement) {
        if (!value || value.length === 0) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        } else if (value == 0) {
            errorElement.text("Enter value more than 0").addClass('field-validation-error');
            return false;
        } else if (value < 1 || value > 15) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }



    ////----- Escalation methods is not implemented in the controller-----

    //async function GenerateEscalationMatrixleveTree(treeData, matrixID) {
    //    let matrixOwner = "";
    //    await $.ajax({
    //        type: "POST",
    //        url: RootUrl + alertURL.postAction,
    //        data: {
    //            "EscOwnerMatID": matrixID
    //        },
    //        dataType: "json",
    //        async: false,
    //        success: function (response) {
    //            if (response) {
    //                Matrixowner = response;
    //            }
    //        },
    //        error: function (response) {
    //            errorNotification(response)
    //        }
    //    });

    //    const borderColors = ['border-primary', 'border-success', 'border-warning', 'border-danger'];
    //    const circleColors = ['bg-primary', 'bg-success', 'bg-warning', 'bg-danger'];

    //    let output = "";
    //    let j = 0;

    //    for (let i = 0; i < treeData.length; i++) {
    //        const levelData = treeData[i];
    //        if (levelData) {
    //            const timeTitle = `Will Escalate After:- ${levelData.escalationTime} ${levelData.escalationTimeUnit}`;
    //            const ownerTitle = `Matrix Owner:- ${matrixOwner}`;
    //            const fullTitle = `${timeTitle}\n${ownerTitle}`;

    //            output += `
    //            <li class="li">
    //                <div class="Escalation_Timeline_Card card ${borderColors[j]}">
    //                    <div class="d-flex align-items-center">
    //                        <span class="Timeline_Card_Level ${circleColors[j]} badge bg-primary">Level ${i + 1}</span>
    //                        <div class="d-grid ms-3">
    //                            <p class="mb-1 text-truncate" title="${fullTitle.replace(/"/g, '&quot;')}">${levelData.escLevName}</p>
    //                        </div>
    //                    </div>
    //                </div>
    //            </li>`;

    //            j = (j + 1) % borderColors.length;
    //        }
    //    }

    //    document.getElementById("Escalationlev_Time_ul").innerHTML = output;
    //}

    //async function getEsclationMatricLevelByMatrixId() {
    //    await $.ajax({
    //        type: "POST",
    //        url: RootUrl + alertURL.postActUrl,
    //        data: {
    //            "EscMatID": _esctma[0]
    //        },
    //        dataType: "json",
    //        async: false,
    //        success: function (response) {
    //            if (response && response?.data?.length) {
    //                GenerateEscalationMatrixleveTree(response.data, _esctma[0]);
    //            }
    //        },
    //        error: function (response) {
    //            errorNotification(response)
    //        }
    //    });
    //}

    //async function ackChecked(_esc_maatId){
    //   await $.ajax({
    //       type: "POST",
    //       url: RootUrl + alertURL.postActionUrl,
    //       dataType: "json",
    //       async: false,
    //       success: function (response) {
    //           if (response && response.data?.length && _esc_maatId !== "0" || _esc_maatId !== "undefined") {
    //               let _esctma = _esc_maatId.split('$');
    //               let escMatrixList = $("#manageAlertESCMatrixList");
    //               escMatrixList.empty();
    //               escMatrixList.append(`<option id="0" value="0" >Select Escalation Matrix</option>`);
    //               for (let i = 0; i < response.data.length; i++) {

    //                   let escmatname = response.data[i].escMatCode + " " + response.data[i].escMatName
    //                   if (_esctma[0] == response.data[i].id)
    //                       ddlCustomers.append(`<option id="${response.data[i].id}" value="${response.data[i].id}" selected >${escmatname}</option>`);
    //                   else
    //                       ddlCustomers.append(`<option id="${response.data[i].id}" value="${response.data[i].id}" >${escmatname}</option>`);
    //               }
    //               $("#numId").val(_esctma[1]);
    //               $("#tmdy").val(_esctma[2]);

    //                getEsclationMatricLevelByMatrixId();
    //           }
    //       },
    //       error: function (response) {
    //           errorNotification(response)
    //       }
    //   });
    //}

    //async function ackUnChecked() {
    //    await $.ajax({
    //        type: "POST",
    //        url: RootUrl + alertURL.postActionUrl,
    //        dataType: "json",
    //        async: false,
    //        success: function (response) {
    //            if (response?.data && response?.data?.length) {
    //                let matrixList = $("#manageAlertESCMatrixList");
    //                matrixList.empty();
    //                matrixList.append(`<option id="0" value="0" >Select Escalation Matrix</option>`);
    //                for (let i = 0; i < response.data.length; i++) {
    //                    let escmatname = response.data[i].escMatCode + " " + response.data[i].escMatName
    //                    matrixList.append(`<option id="${response.data[i].id}" value="${response.data[i].id}" >${escmatname}</option>`);
    //                }
    //            }
    //        },
    //        error: function (response) {
    //            errorNotification(response)
    //        }
    //    });
    //};

    //$('#manageAlertMasterTable').on('click', '.btnMangeAlertESCMatrixList', async function () {
    //    let alertId = $(this).data('esc-id');
    //    let ackchk = $(this).data('esc-ack');
    //    sessionStorage.setItem('_alertId', alertId);
    //    let _esc_maatId = $(this).data('esc-matidd');
    //    document.getElementById('Escalationlev_Time_ul').innerHTML = '';

    //    if (ackchk) {
    //        await ackChecked(_esc_maatId);
    //    } else {
    //        await ackUnChecked();
    //    }
    //});

    //$('#manageAlertESCMatrixList').on('change', async function () {
    //    let escmatID = $("#manageAlertESCMatrixList").val();
    //    await $.ajax({
    //        type: "POST",
    //        url: RootUrl + alertURL.postActUrl,
    //        data: {
    //            "EscMatID": escmatID
    //        },
    //        dataType: "json",
    //        async: false,
    //        success: function (response) {
    //            GenerateEscalationMatrixleveTree(response.data, escmatID);
    //        },
    //        error: function (response) {
    //            errorNotification(response)
    //        }
    //    });
    //});

    $('#manageAlertMasterTable').on('click', '.btnManageAlertEdit', async function () {
        const encodedData = $(this).data('managealertedit');
        let alertData = JSON.parse(decodeURIComponent(atob(encodedData)));

        $("[name=AlertId]").attr("readonly", "readonly").css({
            "opacity": 1,
        });
        $("[name=AlertMessage]").attr("readonly", "readonly").css({
            "opacity": 1,
        });
        $('#manageAlertId').attr('data-globalalertid', alertData.id ?? '');

        $('#btnSaveManageAlert').text('Update');
        $('#manageAlertCreateModal').modal('show');
        $("#manageAlertId").val(alertData.alertId);
        $("#manageAlertName").val(alertData.alertName);
        $("#manageAlertMessage").val(alertData.alertMessage);
        $("#manageAlertPriority").val(alertData.alertPriority);
        $("#manageAlertCount").val(alertData.alertFrequency);
        let alerttime = alertData.alertSendTime?.split(":");
        if (alerttime) $("#manageAlertTime").val(alerttime.length == 2 ? (alerttime[0] + ":" + alerttime[1]) : ("00" + ":" + "00"))
        let sms = (alertData.isSendSMS === true || alertData.isSendSMS === "true") ? true : false;
        let email = (alertData.isSendEmail === true || alertData.isSendEmail === "true") ? true : false;
        let isactive = (alertData.isAlertActive === true || alertData.isAlertActive === "true") ? true : false;
        let smscheck = alertData.smSMessage;

        $("#manageAlertEmail").prop("checked", email);
        $("#manageAlertActive").prop("checked", isactive);
        if (sms == true && (smscheck != null)) {
            $("#manageAlertSMSMessageDiv").show()
            $("#manageAlertSMSMessage").val(smscheck);
            $("#manageAlertSMS").prop({ checked: true, disabled: true });
        } else {
            $("#manageAlertSMSMessageDiv").hide()
            $("#manageAlertSMSMessage").val("");
            $("#manageAlertSMS").prop({ checked: false, disabled: true });
        }
    });

    $('#manageAlertMasterTable').on('click', '.btnManageAlertDelete', async function () {
        DeleteId = $(this).data('alertdeleteid');
        let AlertName = $(this).data('alertdeletename');
        $("#manageAlertDeleteModal").modal('show');
        $("#manageAlertDeteteName").text(AlertName).attr("title", AlertName)
    });

    $('#manageAlertMasterTable').on('click', '.btnManageAlertHistory', function () {
        const encodedData = $(this).data('managealerthistory');
        let alertHistoryData = JSON.parse(decodeURIComponent(atob(encodedData)));
        $("#manageAlertViewHistory").modal('show');
        $("#manageAlertHistoryTableBody").empty();

        const priorityIconMap = {
            "High": "fw-bold cp-up-doublearrow text-warning fs-7",
            "Critical": "fw-bold cp-critical-level text-danger fs-7",
            "Normal": "fw-bold cp-equal text-info fs-7",
            "Information": "cp-warning text-primary fs-7",
            "Low": "fw-bold cp-down-doublearrow text-success fs-7"
        };

        const iconClass = priorityIconMap[alertHistoryData.alertPriority];
        const alertSendTime = alertHistoryData.alertSendTime === 'null' ? 'NA' : alertHistoryData.alertSendTime;

        const getStatusIcon = (isChecked, iconClassName) => {
            const color = (isChecked === true || isChecked === "true") ? "text-success" : "text-danger";
            return `
            <td class="text-truncate">
                <i class="${iconClassName} ${color}"></i>
            </td>
        `;
        };

        const datas = `
        <tr>
            <td>${alertHistoryData.alertId}</td>
            <td title="${alertHistoryData.alertName}">
                <span class="d-inline-block text-truncate" style="max-width:60%">${alertHistoryData.alertName}</span>
            </td>
            <td title="${alertHistoryData.alertMessage}">
                <span class="d-inline-block text-truncate" style="max-width:70%">${alertHistoryData.alertMessage}</span>
            </td>
            <td>
                <span class="text-danger me-1"><i class="${iconClass}"></i></span>
                <span>${alertHistoryData.alertPriority}</span>
            </td>
            <td>${alertHistoryData.alertFrequency}</td>
            <td>${alertSendTime}</td>
            ${getStatusIcon(alertHistoryData.isSendSMS, "cp-sms-gateway")}
            ${getStatusIcon(alertHistoryData.isSendEmail, "cp-email")}
        </tr>
    `;

        $("#manageAlertHistoryTableBody").append(datas);
    });

    $('#manageAlertMasterTable').on('click', '.attachCheckbox', function () {
        if ($("#attbtn" + this.id).attr("disabled")) {
            $("#attbtn" + this.id).removeAttr("disabled");
        } else {
            $("#attbtn" + this.id).attr("disabled", "true");
        }
    });

    $('#btnSaveManageAlertEscalation').on('click', async function () {
        let escmatID = $("#manageAlertESCMatrixList").val();
        let nmID = $("#numId").val();
        let tmtID = $("#tmdy").val();
        let alertId = "";
        if (sessionStorage.getItem('_alertId') !== undefined) {
            alertId = sessionStorage.getItem('_alertId')
        }
        let escmatId = escmatID + "$" + nmID + "$" + tmtID + "#" + alertId;
        $('#textmatidlId').val(escmatId);
        const token = $('input[name="__RequestVerificationToken"]').val();
        await $.ajax({
            type: "POST",
            url: alertURL.updateAlertMaster,
            data: { escmatId, __RequestVerificationToken: token },
            dataType: "json",
            success: function (response) {
                window.location.href = alertURL.list;
            },
            error: function (response) {
                errorNotification(response)
            }
        });
    });

    $("#btnManageAlertConfirmDelete").on("click", async function () {
        await $.ajax({
            type: "GET",
            url: RootUrl + "Alert/ManageAlert/Delete_id",
            dataType: "json",
            data: { id: DeleteId },
            success: function (result) {
                if (result && result?.success) {
                    let data = result.data
                    notificationAlert("success", data.message)
                    $("#manageAlertDeleteModal").modal("hide")
                    dataTable.ajax.reload()
                } else {
                    errorNotification(result)
                }
            },
            error: function (response) {
                errorNotification(response)
            }
        })
    });

    $(".btnManageAlertCancel").on("click", function () {
        ClearFiaTemplateErrorElements()
    })

    $('#manageAlertName').on('input', managealertdebounce(async function () {
        let value = await sanitizeInput($('#manageAlertName').val());
        $("#manageAlertName").val(value);
        if (!value || value.length === 0) {
            validateAlertName($("#manageAlertName").val(), "Enter alert name", $('#alert_name_error'));
        } else {
            validateAlertName($("#manageAlertName").val(), "", $('#alert_name_error'), alertURL.nameExistUrl);
        }
    }, 400))

    $('#manageAlertPriority').on('change', function () {
        validateAlertFields($(this).val(), "Select the alert priority", $('#alert_Priority_error'));
    });

    $('#manageAlertCount').on('keypress', function (e) {
        if ($(this).val().length >= 2) {
            e.preventDefault()
        }
        ["e", "E", "+", "-", "."].includes(e.key) && e.preventDefault()
    });

    $('#manageAlertCount').on('input', function () {
        const value = $(this).val()
        if (value == "-1" || value > 15 || value == "00") {
            $('#manageAlertCount').val("")
        }
        if (!value || value.length === 0) {
            validatecountDropDown(value, "Enter alert count", $('#alert_count_error'));
        } else {
            validatecountDropDown(value, "Enter the value 1 to 15 ", $('#alert_count_error'));
        }
    });

    $('#manageAlertTime').on('change Keyup keydown', function () {
        validateAlertFields($(this).val(), "Select time", $('#alert_time_error'));
    });

    $('#btnManageAlertDownload').on('click', async function () {
        try {
            $("#btnManageAlertDownload").addClass("disabled");

            await $.ajax({
                url: alertURL.loadReport,
                type: "GET",
                xhrFields: {
                    responseType: 'blob'
                },
                data: reportQuery,
                success: function (blob) {
                    let alertClass, iconClass, message;
                    if (blob.size > 0) {
                        const DateTime = new Date().toLocaleString('en-US', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            fractionalSecondDigits: 3,
                            hour12: false
                        }).replace(/[^0-9]/g, '');

                        downloadManageAlert(blob, "ManageAlertReport_" + DateTime + ".pdf", "application/pdf");

                        alertClass = "success-toast";
                        iconClass = "cp-check toast_icon";
                        message = "Manage Alert report downloaded successfully";
                    } else {
                        alertClass = "warning-toast";
                        iconClass = "cp-exclamation toast_icon";
                        message = "Manage AlertReport Download Error";
                    }
                    $("#btnManageAlertDownload").removeClass("disabled");

                    $('#manageAlertClass').removeClass().addClass(alertClass);
                    $('#icon').removeClass().addClass(iconClass);
                    $('#notificationAlertmessage').text(message);
                    $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
                },
                error: function (xhr, status, error) {
                    $("#btnManageAlertDownload").removeClass("disabled");
                    // Show error toast
                    $('#alertClass').removeClass().addClass("warning-toast");
                    $('#icon').removeClass().addClass("cp-exclamation toast_icon");
                    $('#notificationAlertmessage').text("Manage Alert Report Download Error");
                    $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
                }
            });
        } catch (error) {
            $("#btnManageAlertDownload").removeClass("disabled");
        }
    });

    $("#btnCreateManageAlert").on("click", async function () {
        $("[name=AlertId]").removeAttr("readonly");
        $("[name=AlertMessage]").removeAttr("readonly");
        $('#btnSaveManageAlert').text('Save');
        $("#manageAlertId,#manageAlertName,#manageAlertMessage,#manageAlertPriority,#manageAlertCount,#manageAlertTime,#manageAlertSMSMessage").val("")
        $("#manageAlertSMS,#manageAlertEmail,#manageAlertActive").prop("checked", false);
        $("#manageAlertSMS").prop("disabled", false);
        $("#manageAlertSMSMessageDiv").hide()
        $('#message_error').removeClass('field-validation-error');
        $("#manageAlertCreateModal").modal('show');
        await EnabledIDandMessage(true);
        ['manageAlertName', 'manageAlertSMSMessage'].forEach(id => document.getElementById(id)?.addEventListener('keypress', preventEnterKey));
    });

    $("#manageAlertSMS").on("click", function () {
        if ($(this).prop("checked") == true) {
            $("#manageAlertSMSMessageDiv").show()
        } else {
            $("#manageAlertSMSMessageDiv").hide()
            $('#message_error').text('').removeClass('field-validation-error');
        }
    });

    $('#manageAlertSMSMessage').on('input', async function () {
        const value = $(this).val();
        const errorElement = $('#message_error');
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        validateAlertFields(value, "Enter message", errorElement);
    });

    $("#btnSaveManageAlert").on("click", async function () {
        let isalertid, isalertmessage, issmsmessage, isalertname, isalertPriority, isalertcount, isalerttime;
        const isUpdate = $("#btnSaveManageAlert").text() === "Update";
        if (!isUpdate) {
            isalertid = await validateID($("#manageAlertId").val(), $("#manageAlertId").val()?.length === 0 ? "Enter alert ID" : "Enter 5 digits only", $('#alert_id_error'), alertURL.idExistUrl);
            isalertmessage = validateAlertFields($("#manageAlertMessage").val(), $("#manageAlertMessage").val()?.length === 0 ? "Enter alert message" : "Between 3 to 500 characters", $('#alert_message_error'));
            if ($("#manageAlertSMS").prop("checked")) {
                issmsmessage = validateAlertFields($("#manageAlertSMSMessage").val(), $("#manageAlertSMSMessage").val()?.length === 0 ? "Enter message" : "Between 3 to 500 characters", $('#message_error'));
            } else {
                issmsmessage = true;
            }
        }
        isalertname = await validateAlertName($("#manageAlertName").val(), $("#manageAlertName").val()?.length === 0 ? "Enter alert name" : "Between 3 to 100 characters", $('#alert_name_error'), alertURL.nameExistUrl);

        isalertPriority = validateAlertFields($("#manageAlertPriority").val(), "Select alert priority", $('#alert_Priority_error'));

        isalertcount = validatecountDropDown($("#manageAlertCount").val(), $("#manageAlertCount").val()?.length === 0 ? "Enter alert count" : "Enter the value 1 to 15", $('#alert_count_error'));

        isalerttime = validateAlertFields($("#manageAlertTime").val(), "Select time", $('#alert_time_error'));

        const allValid = (isalertid ?? true) && (issmsmessage ?? true) && (isalertmessage ?? true) && isalertname && isalertPriority && isalertcount && isalerttime;

        if (allValid && !btndisabled) {
            btndisabled = true;
            const data = {
                AlertId: $("#manageAlertId").val(),
                AlertName: $("#manageAlertName").val(),
                AlertMessage: $("#manageAlertMessage").val(),
                AlertPriority: $("#manageAlertPriority").val(),
                AlertFrequency: $("#manageAlertCount").val(),
                AlertSendTime: $("#manageAlertTime").val(),
                IsSendEmail: $("input[name=isSendEmail]").prop("checked"),
                SmSMessage: $("#manageAlertSMSMessage").val(),
                IsSendSMS: $("input[name=isSendSMS]").prop("checked"),
                IsAlertActive: $("input[name=isAlertActive]").prop("checked"),
                __RequestVerificationToken: gettoken()
            };

            if (isUpdate) {
                data.id = $('#manageAlertId').attr('data-globalalertid');
            }

            await $.ajax({
                type: "POST",
                url: RootUrl + alertURL.createOrUpdate,
                dataType: "json",
                data,
                success: function (result) {
                    if (result?.success) {
                        notificationAlert("success", result.data.message);
                        $('#manageAlertCreateModal').modal('hide');
                        setTimeout(() => {
                            btndisabled = false;
                            dataTable.ajax.reload();
                        }, 2000);
                    } else {
                        errorNotification(result);
                        btndisabled = false;
                    }
                },
                error: function (response) {
                    errorNotification(response);
                    btndisabled = false;
                }
            });
        }
    });

})