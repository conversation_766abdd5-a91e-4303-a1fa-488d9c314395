﻿using ContinuityPatrol.Application.Features.Server.Queries.GetDetail;

namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;

public class GetByEntityIdVm
{
    public string Id { get; set; }
    public string Type { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string Properties { get; set; }
    public string PageProperties { get; set; }
    public string ConfiguredRPO { get; set; }
    public string DataLagValue { get; set; }
    public string RPOGeneratedDate { get; set; }
 
    public List<ServerStatus> ServerStatus { get; set; }
}

public class ServerStatus
{
   public string Key { get; set; }
    public ServerDetailVm ServerDetail { get; set; }
}
