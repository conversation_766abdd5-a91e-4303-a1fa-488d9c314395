﻿using ContinuityPatrol.Domain.ViewModels;
using ContinuityPatrol.Domain.ViewResults;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Infrastructure;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Controllers;
using ContinuityPatrol.Application.Features.User.Commands.ForgotPassword;
using ContinuityPatrol.Application.Features.User.Commands.UpdatePassword;
using ContinuityPatrol.Application.Features.User.Queries.GetLoginName;
using ContinuityPatrol.Application.Features.UserLogin.Commands.Update;
using ContinuityPatrol.Application.Features.UserLogin.Queries.GetDetail;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.ViewModels.UserModel.ChangePasswordModels;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Tests.Constants;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Helper;
using ContinuityPatrol.Shared.Tests.Mocks;

namespace ContinuityPatrol.Web.UnitTests.Controller;
 
public class AccountControllerShould
{
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private readonly Mock<ILogger<AccountController>> _mockLogger = new();
    private readonly Mock<IDomainService> _mockDomainService = new();
    private readonly Mock<IPublisher> _mockPublisher = new();
    private readonly Mock<IDistributedCache> _mockDistributedCache = new();
    private Mock<ILoginService> _mockLoginService;

    private AccountController _sut;

    public const string UserId = "********-072f-4ed0-9848-a469cbcfabdc";

    public AccountControllerShould(AccountController sut)
    {
        _sut = sut;
        _mockLoginService = LoginServiceMocks.PrepareLoginView(ApplicationStatus.Qualified);

        Initialize();
    }
    internal void Initialize()
    {
        _sut = new AccountController(
            _mockDataProvider.Object,
            _mockLogger.Object,
            _mockMapper.Object,
            _mockLoginService.Object,
            _mockDomainService.Object,
            _mockPublisher.Object,
            _mockDistributedCache.Object);
        _sut.ControllerContext = new ControllerContextMocks().Default();
        _sut.TempData = TempDataFakes.GeTempDataDictionary(_sut.HttpContext, "Test", "Test");
    }

    public LoginViewModel InHouseLoginViewModel => LoginFakes.InHouseLoginViewModel;
    public LoginViewModel AdLoginViewModel => LoginFakes.AdLoginViewModel;

    #region Login

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_Return_ViewResult()
    {
        var result = await _sut.Login();
        var viewResult = Assert.IsType<ViewResult>(result);
        viewResult.Model.ShouldBeOfType(typeof(LoginViewModel));
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_WhenOldSessionExists_RedirectsToPreLogin()
    {
        _sut.ControllerContext = new ControllerContextMocks().OldLogin();
        WebHelper.UserSession = new UserSession() { LoggedUserId = UserId };
        ClearDatabaseSessionMock();

        var result = await _sut.Login();

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        
        redirectToActionResult.ActionName.ShouldBe("PreLogin");
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_Call_PrepareView_OnlyOnce()
    {
        await _sut.Login();

        _mockLoginService.Verify(x => x.PrepareLoginViewAsync(), Times.Once);
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_Companies_NotEmpty_PrepareView_NeverCall()
    {
        WebHelper.CurrentSession.Set("CompanyProfiles", CompanyFakes.GetCompanyListItems());

        await _sut.Login();

        _mockLoginService.Verify(x => x.PrepareLoginViewAsync(), Times.Never);
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_ReturnsViewResult_WhenCompanyListIsAvailable()
    {
        var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
        WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
        var result = await _sut.Login();
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.NotNull(viewResult);
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_ReturnsView_WhenApplicationStatusIsUnhandledError()
    {
        
        WebHelper.CurrentSession.Remove("CompanyProfiles");

        var viewResultMock = new PreLoginViewResult { ApplicationStatus = ApplicationStatus.UnhandledError };
        _mockLoginService.Setup(ls => ls.PrepareLoginViewAsync()).ReturnsAsync(viewResultMock);

        
        var result = await _sut.Login();

        
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.NotNull(viewResult);
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_RedirectsToUserInfo_WhenApplicationStatusIsEmptyUser()
    {
        
        WebHelper.CurrentSession.Remove("CompanyProfiles");

        var viewResultMock = new PreLoginViewResult { ApplicationStatus = ApplicationStatus.EmptyUser };
        _mockLoginService.Setup(ls => ls.PrepareLoginViewAsync()).ReturnsAsync(viewResultMock);

        
        var result = await _sut.Login();

        
        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("UserInfo", redirectToActionResult.ActionName);
        Assert.Equal("Basic", redirectToActionResult.ControllerName);
    }

    [Fact]
    [Trait("Category", "AccountController_Login")]
    public async Task Login_RedirectsToConfiguration_WhenApplicationStatusIsEmptyCompanyProfile()
    {
        WebHelper.CurrentSession.Remove("CompanyProfiles");

        var viewResultMock = new PreLoginViewResult { ApplicationStatus = ApplicationStatus.EmptyCompanyProfile };
        _mockLoginService.Setup(ls => ls.PrepareLoginViewAsync()).ReturnsAsync(viewResultMock);

        var result = await _sut.Login();

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Configuration", redirectToActionResult.ActionName);
        Assert.Equal("Basic", redirectToActionResult.ControllerName);
    }

    #endregion

    #region LoginAsync

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ReturnsRedirectToActionResult_WhenModelStateIsInvalid()
    {
        _sut.ModelState.AddModelError("X", "Test Error");

        var result = await _sut.LoginAsync(InHouseLoginViewModel, "") as RedirectToActionResult;

        result!.ActionName.ShouldBe("Login");
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_Return_Login_When_NonLocalReturnUrl()
    {
        _sut.Url = UrlMocks.IsLocalUrl(false);

        var result = await _sut.LoginAsync(InHouseLoginViewModel, "https://www.google.com") as RedirectToActionResult;

        result!.ActionName.ShouldBe("Login");
    }

    [Theory]
    [Trait("Category", "AccountController_LoginAsync")]
    [InlineData(false)]
    [InlineData(true)]
    public async Task LoginAsync_Redirect_ServiceAvailabilityPage_When_Authenticate(bool isAdAuthentication)
    {
        _mockLoginService = LoginServiceMocks.Authenticate(LogInStatus.Succeeded, isAdAuthentication);

        Initialize();

        _sut.Url = UrlMocks.IsLocalUrl(true);

        var result = await _sut.LoginAsync(isAdAuthentication ? AdLoginViewModel : InHouseLoginViewModel, "") as RedirectToActionResult;

        result!.ActionName.ShouldBe("List");
        result.ControllerName.ShouldBe("ServiceAvailability");
    }

    [Theory]
    [Trait("Category", "AccountController_LoginAsync")]
    [InlineData("", "test", AccountConstant.RequiredLoginName)]
    [InlineData(" ", "test", AccountConstant.RequiredLoginName)]
    [InlineData("test", "", AccountConstant.RequiredPassword)]
    [InlineData("test", " ", AccountConstant.RequiredPassword)]
    public async Task LoginAsync_ThrowInvalidArgumentException_WhenModelInHouseModelWithEmpty(string user, string password, string errorMsg)
    {
        var model = LoginFakes.GetInHouseLoginViewModel(user, password, "********-072f-4ed0-9848-a469cbcfabdc");

        var exception = await Should.ThrowAsync<InvalidArgumentException>(() => _sut.LoginAsync(model, ""));

        exception.Message.ShouldBe(errorMsg);
    }


    [Theory]
    [Trait("Category", "AccountController_LoginAsync")]
    [MemberData(nameof(GetInvalidCompanyId))]
    public async Task LoginAsync_ThrowInvalidArgumentException_WhenInvalidCompanyId(string companyId)
    {
        var model = LoginFakes.GetInHouseLoginViewModel("test", "password", companyId);

       var exception = await Should.ThrowAsync<InvalidArgumentException>(() => _sut.LoginAsync(model, ""));

       exception.Message.ShouldBe("Input 'CompanyId' is not valid format.");
    }
    

    [Theory]
    [Trait("Category", "AccountController_LoginAsync")]
    [InlineData(null, "test", "********-072f-4ed0-9848-a469cbcfabdc", AccountConstant.NullLoginName)]
    [InlineData("test", null, "********-072f-4ed0-9848-a469cbcfabdc", AccountConstant.NullPassword)]
    [InlineData("test", "test", null, AccountConstant.NullCompanyId)]       
    public async Task LoginAsync_ThrowArgumentNullException_WhenInHouseModelIsNull(string user, string password, string companyId, string errorMsg)
    {
        var model = LoginFakes.GetInHouseLoginViewModel(user, password, companyId);

        var exception = await Should.ThrowAsync<ArgumentNullException>(() => _sut.LoginAsync(model, ""));

        exception.Message.ShouldBe(errorMsg);
    }


    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ReturnsRedirectToAction_WhenUserIsAlreadyLoggedIn()
    {
        _mockLoginService = LoginServiceMocks.Authenticate(LogInStatus.MultipleLoginSessionFound, false);

        Initialize();

        _sut.Url = UrlMocks.IsLocalUrl(true);

        var result = await _sut.LoginAsync(InHouseLoginViewModel, "") as RedirectToActionResult;

        result!.ActionName.ShouldBe("Login");
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ReturnsRedirectToAction_WhenModelStateIsInvalid()
    {
        _sut.ModelState.AddModelError("Error", "Invalid model state");
        _sut.Url = UrlMocks.IsLocalUrl(true);

        var result = await _sut.LoginAsync(InHouseLoginViewModel, "") as RedirectToActionResult;

        result!.ShouldBeOfType(typeof(RedirectToActionResult));
        result.ActionName.ShouldBe("Login");
    }

    [Fact]
    [Trait("Category", "AccountController_LoginAsync")]
    public async Task LoginAsync_ReturnsRedirectToAction_WhenReturnUrlIsInvalid()
    {  
        var returnUrl = "http://malicious-site.com";
        _sut.Url = new Mock<IUrlHelper>().Object;

        var result = await _sut.LoginAsync(InHouseLoginViewModel, returnUrl);

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Login", redirectToActionResult.ActionName);
    }
    #endregion

    #region PreLogin

    [Fact]
    [Trait("Category", "AccountController_PreLogin")]
    public async Task PreLogin_Redirect_To_Login()
    {
        var result = await _sut.PreLogin() as RedirectToActionResult;

        result!.ActionName.ShouldBe("Login");
    }

    #endregion

    #region ChangePassword
    [Fact]
    [Trait("Category", "AccountController_ChangePassword")]
    public void ChangePassword_ReturnsViewResult()
    {
        var result = _sut.ChangePassword();

        var viewResult = Assert.IsType<ViewResult>(result);

        Assert.Null(viewResult.ViewName);  
    }

    #endregion

    #region  ChangePasswordAsync
    [Fact]
    [Trait("Category", "AccountController_ChangePasswordAsync")]
    public async Task ChangePasswordAsync_ReturnsRedirectToAction_WhenPasswordChangeIsSuccessful()
    {
        var model = new ChangePasswordViewModel { UserId = UserId, LoginName = "test"};
        var command = new UpdatePasswordCommand();
        _mockMapper.Setup(m => m.Map<UpdatePasswordCommand>(model)).Returns(command);

        _mockDataProvider.Setup(dp => dp.User.UpdateUserPassword(command)).ReturnsAsync(new BaseResponse { Success = true, Message = "Password changed successfully" });
        _mockDataProvider.Setup(dp => dp.User.GetUserByLoginName(It.IsAny<string>())).ReturnsAsync(new UserLoginNameVm() { Id = UserId });
      
        ClearDatabaseSessionMock();

        var result = await _sut.ChangePasswordAsync(model);

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Login", redirectToActionResult.ActionName);
    }

    private void ClearDatabaseSessionMock()
    {
        _mockDataProvider.Setup(dp => dp.UserLogin.GetUserInfoByUserId(It.IsAny<string>()))
            .ReturnsAsync(new UserLoginDetailVm());
        _mockDataProvider.Setup(dp => dp.UserLogin.UpdateAsync(It.IsAny<UpdateUserLoginCommand>()))
            .ReturnsAsync(It.IsAny<BaseResponse>());

        _mockMapper.Setup(m => m.Map<UpdateUserLoginCommand>(It.IsAny<UserLoginDetailVm>()))
            .Returns(new UpdateUserLoginCommand()); // mapping data
    }

    [Fact]
    [Trait("Category", "AccountController_ChangePasswordAsync")]
    public async Task ChangePasswordAsync_ReturnsRedirectToAction_WhenExceptionIsThrown()
    {
        var model = new ChangePasswordViewModel { UserId = UserId };
        _mockDataProvider.Setup(dp => dp.User.UpdateUserPassword(It.IsAny<UpdatePasswordCommand>())).ThrowsAsync(new Exception("Error"));

        var result = await _sut.ChangePasswordAsync(model);

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("ChangePassword", redirectToActionResult.ActionName);
    }

    #endregion

    #region ForgotPassword
    [Fact]
    [Trait("Category", "AccountController_ForgotPassword")]
    public void ForgotPassword_ReturnsViewResult()
    {
        var result = _sut.ForgotPassword();

        var viewResult = Assert.IsType<ViewResult>(result);

        Assert.Null(viewResult.ViewName);
    }

    #endregion

    #region ForgotPasswordAsync

    [Fact]
    [Trait("Category", "AccountController_ForgotPasswordAsync")]
    public async Task ForgotPasswordAsync_ReturnsRedirectToAction_WhenModelStateIsInvalid()
    {
        _sut.ModelState.AddModelError("Error", "Invalid model state");
        var model = new ForgotPasswordViewModel();
        
        var result = await _sut.ForgotPasswordAsync(model);
        
        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("ForgotPassword", redirectToActionResult.ActionName);
    }

    [Fact]
    [Trait("Category", "AccountController_ForgotPasswordAsync")]
    public async Task ForgotPasswordAsync_ReturnsRedirectToAction_WhenForgotPasswordIsSuccessful()
    {
        var model = new ForgotPasswordViewModel();
        var command = new ForgotPasswordCommand();
        _mockMapper.Setup(m => m.Map<ForgotPasswordCommand>(model)).Returns(command);
        _mockDataProvider.Setup(dp => dp.User.ForgotPassword(command)).ReturnsAsync(new BaseResponse { Message = "Password changed successfully" });

        var result = await _sut.ForgotPasswordAsync(model);

        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Login", redirectToActionResult.ActionName);
    }

    [Fact]
    [Trait("Category", "AccountController_ForgotPasswordAsync")]
    public async Task ForgotPasswordAsync_ReturnsRedirectToAction_WhenExceptionIsThrown()
    {
        var model = new ForgotPasswordViewModel();
        _mockDataProvider.Setup(dp => dp.User.ForgotPassword(It.IsAny<ForgotPasswordCommand>())).ThrowsAsync(new Exception("Error"));

        var result = await _sut.ForgotPasswordAsync(model);
        var redirectToActionResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("ForgotPassword", redirectToActionResult.ActionName);
    }

    #endregion

    #region PatchList

    [Fact]
    [Trait("Category", "AccountController_PatchList")]
    public void PatchList_ReturnsViewResult()
    {
        var result = _sut.PatchList();

        var viewResult = Assert.IsType<ViewResult>(result);

        Assert.Null(viewResult.ViewName);
    }

    #endregion

    #region UserProfile
    [Fact]
    [Trait("Category", "AccountController_UserProfile")]
    public void UserProfile_ReturnsViewResult()
    {
        var result = _sut.UserProfile();

        var viewResult = Assert.IsType<ViewResult>(result);

        Assert.Null(viewResult.ViewName);
    }

    #endregion

    #region Logout

    [Fact]
    [Trait("Category", "AccountController_Logout")]
    public async Task Logout_ReturnsViewResult()
    {
        var result = await _sut.Logout();
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.NotNull(viewResult);
    }
    #endregion

    #region Data

    public static TheoryData<string> GetInvalidCompanyId =>
        new()
        {
            "123e4567-e89b-12d3-a456",
            "123e4567-e89b-12d3-a456-************1234",
            "123e4567-e89b-12d3-a456-42661417XXXX",
            "",
            " ",
            "123e4567-e89b-12d3-a456-************!",
            "123e4567-e89b-12d3-a456-***********",
            "123e4567-e89b 12d3-a456/************",
            "123e4567--12d3-a456-************",
            "123e4567--e89b-12d3-a456--************",
            "123e4567%2De89b%2D12d3%2Da456%2D************",
            "123e4567-e89b-12d3-a456-************; DROP TABLE users;",
            "123e4567-e89b-12d3-a456-************<script>alert('xss')</script>",
            "00000000-0000-0000-0000-000000000000"
        };

    #endregion

    #region ClearSession
    [Fact]
    public async Task ClearSession_ValidLoginName_ReturnsSuccess()
    {
        var loginName = "validUser";
        var user = new UserLoginNameVm() { Id = UserId };
        _mockDataProvider.Setup(dp => dp.User.GetUserByLoginName(loginName)).ReturnsAsync(user);
        _mockDistributedCache.Setup(c => c.RemoveAsync(It.IsAny<string>(),CancellationToken.None)).Returns(Task.CompletedTask);
        ClearDatabaseSessionMock();
        WebHelper.CurrentSession.Set("LoginViewModel" + loginName, new LoginViewModel(){LoginName = "testUser"});
        
        var result = await _sut.ClearSession(loginName) as JsonResult;
        
        result.ShouldNotBeNull();
        dynamic resultData = new JsonResultDynamicWrapper(result);
        bool success = resultData.Success;
        success.ShouldBeTrue();
        LoginViewModel loginViewModel = resultData.loginViewModel;
        loginViewModel.LoginName.ShouldBe("testUser");
    }

    //[Fact]
    //public async Task ClearSession_InvalidLoginName_ReturnsFailure()
    //{
    //    // Arrange
    //    var loginName = "invalidUser";
    //    _dataProviderMock.Setup(dp => dp.User.GetUserByLoginName(loginName)).ReturnsAsync((User)null);

    //    // Act
    //    var result = await _controller.ClearSession(loginName) as JsonResult;

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.False((bool)((dynamic)result.Value).Success);
    //}

    //[Fact]
    //public async Task ClearSession_NullOrEmptyLoginName_ThrowsException()
    //{
    //    // Arrange
    //    string loginName = null;

    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.ClearSession(loginName));
    //}

    //[Fact]
    //public async Task ClearSession_LoginInfoIdIsNullOrWhitespace_ReturnsFailure()
    //{
    //    // Arrange
    //    var loginName = "userWithWhitespaceId";
    //    var user = new User { Id = " " };
    //    _dataProviderMock.Setup(dp => dp.User.GetUserByLoginName(loginName)).ReturnsAsync(user);

    //    // Act
    //    var result = await _controller.ClearSession(loginName) as JsonResult;

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.False((bool)((dynamic)result.Value).Success);
    //}

    //[Fact]
    //public async Task ClearSession_CacheRemovedSuccessfully_ReturnsSuccess()
    //{
    //    // Arrange
    //    var loginName = "userWithCache";
    //    var user = new User { Id = "userId" };
    //    _dataProviderMock.Setup(dp => dp.User.GetUserByLoginName(loginName)).ReturnsAsync(user);
    //    _cacheMock.Setup(c => c.RemoveAsync(It.IsAny<string>())).Returns(Task.CompletedTask);

    //    // Act
    //    var result = await _controller.ClearSession(loginName) as JsonResult;

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.True((bool)((dynamic)result.Value).Success);
    //}

    //[Fact]
    //public async Task ClearSession_LoginViewModelNotFound_ReturnsSuccessWithoutLoginViewModel()
    //{
    //    // Arrange
    //    var loginName = "userWithoutLoginViewModel";
    //    var user = new User { Id = "userId" };
    //    _dataProviderMock.Setup(dp => dp.User.GetUserByLoginName(loginName)).ReturnsAsync(user);
    //    WebHelper.CurrentSession.Set("LoginViewModel" + loginName, null);

    //    // Act
    //    var result = await _controller.ClearSession(loginName) as JsonResult;

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.True((bool)((dynamic)result.Value).Success);
    //}

    //[Fact]
    //public async Task ClearSession_LoginViewModelFound_ReturnsSuccessWithLoginViewModel()
    //{
    //    // Arrange
    //    var loginName = "userWithLoginViewModel";
    //    var user = new User { Id = "userId" };
    //    var loginViewModel = new LoginViewModel { LoginName = loginName };
    //    _dataProviderMock.Setup(dp => dp.User.GetUserByLoginName(loginName)).ReturnsAsync(user);
    //    WebHelper.CurrentSession.Set("LoginViewModel" + loginName, loginViewModel);

    //    // Act
    //    var result = await _controller.ClearSession(loginName) as JsonResult;

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.True((bool)((dynamic)result.Value).Success);
    //    Assert.NotNull(((dynamic)result.Value).loginViewModel);
    //}
    #endregion

}