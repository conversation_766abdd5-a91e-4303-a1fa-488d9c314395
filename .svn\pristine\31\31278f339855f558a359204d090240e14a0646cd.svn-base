﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowCategory.Events;

public class WorkflowCategoryCreatedEventHandlerTests : IClassFixture<WorkflowCategoryFixture>
{
    private readonly WorkflowCategoryFixture _workflowCategoryFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly WorkflowCategoryCreatedEventHandler _handler;

    public WorkflowCategoryCreatedEventHandlerTests(WorkflowCategoryFixture workflowCategoryFixture)
    {
        _workflowCategoryFixture = workflowCategoryFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockWorkflowCategoryEventLogger = new Mock<ILogger<WorkflowCategoryCreatedEventHandler>>();

        _mockUserActivityRepository = WorkflowCategoryRepositoryMocks.CreateWorkflowCategoryEventRepository(_workflowCategoryFixture.UserActivities);

        _handler = new WorkflowCategoryCreatedEventHandler(mockLoggedInUserService.Object, mockWorkflowCategoryEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateWorkflowCategoryEventCreated()
    {
        _workflowCategoryFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowCategoryFixture.WorkflowCategoryCreatedEvent, CancellationToken.None);

        result.Equals(_workflowCategoryFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowCategoryFixture.WorkflowCategoryCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_CreateWorkflowCategoryEventCreated()
    {
        _workflowCategoryFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowCategoryFixture.WorkflowCategoryCreatedEvent, CancellationToken.None);

        result.Equals(_workflowCategoryFixture.UserActivities[0].Id);

        result.Equals(_workflowCategoryFixture.WorkflowCategoryCreatedEvent.Name);

        await Task.CompletedTask;
    }
}