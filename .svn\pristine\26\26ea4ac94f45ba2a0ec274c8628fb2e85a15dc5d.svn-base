﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller
{
    public class StorageNetAppSnapMirrorControllerShould
    {
        [Fact]
        public void List_Returns_ViewResult()
        {
            
            var controller = new StorageNetAppSnapMirrorController();

            
            var result = controller.List();

            
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
        }
    }
}
