using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DatabaseFixture : IDisposable
{
    public List<Database> DatabasePaginationList { get; set; }
    public List<Database> DatabaseList { get; set; }
    public Database DatabaseDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
    public const string busnessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
    

    public ApplicationDbContext DbContext { get; private set; }

    public DatabaseFixture()
    {
        var fixture = new Fixture();

        DatabaseList = fixture.Create<List<Database>>();
        DatabaseList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DatabaseList.ForEach(x => x.IsActive = true);
        DatabaseList.ForEach(x => x.CompanyId = CompanyId);
        DatabaseList.ForEach(x => x.BusinessServiceId = BusinessServiceId);

        DatabasePaginationList = fixture.CreateMany<Database>(20).ToList();
        DatabasePaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DatabasePaginationList.ForEach(x => x.IsActive = true);
        DatabasePaginationList.ForEach(x => x.CompanyId = CompanyId);
        DatabasePaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);


        DatabaseDto = fixture.Create<Database>();
        DatabaseDto.ReferenceId = "a8f5fddd-24e8-4f85-aae0-e7d188b3e6c3";
        DatabaseDto.IsActive = true;
        DatabaseDto.CompanyId = CompanyId;
        DatabaseDto.BusinessServiceId = BusinessServiceId;
        DatabaseDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
