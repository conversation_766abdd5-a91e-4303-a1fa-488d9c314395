﻿let globalId = "", globalImpactTypeId = "", globalCatagoryId = "", globalDeleteid = "", selectedValues = [], globalDriftData, dataTable 
const isNameExits = "Drift/DriftParameter/IsNameExist", isCatagoryNameExits = "Drift/DriftParameter/IsCategoryNameExist", isimapacttypeNameExits = "Drift/DriftParameter/IsImpactTypeNameExist"
let createPermission = $("#DriftCreate").data("create-permission").toLowerCase();
let deletePermission = $("#DriftDelete").data("delete-permission").toLowerCase();
if (createPermission == 'false') {
    $("#parameter_create").removeClass('#parameter_create').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
}
function driftparameterdebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
$(function () {
     dataTable = $('#drift_parameter_table').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }, infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/Drift/Driftparameter/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "driftCategoryName" : sortIndex === 3 ? "driftImpactTypeName" :  
                        sortIndex === 4 ? "severity"  : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        json.recordsTotal = json?.data?.totalPages;
                        json.recordsFiltered = json?.data?.totalCount;
                        if (json?.data?.data?.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        globalDriftData = json?.data?.data
                        return json?.data?.data;
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [2, 3, 4, 5],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                            return (page * meta.settings._iDisplayLength) + meta.row + 1;
                        }
                        return data;
                    }, "orderable": false,
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data == null ? "NA" : data}"> ${data == null ? "NA" : data}</span></td>`
                    }
                },
                {
                    "data": "driftCategoryName",
                    "name": "Category",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td>${data == null ? "NA" : data}</td>`;
                    }
                },
                {
                    "data": "driftImpactTypeName", 
                    "name": "Impact Type",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td>${data == null ? "NA" : data}</td>`;
                    }
                },
                {
                    "data": "severity", "name": "Severity", "autoWidth": true,
                    "render": function (data, type, row) {
                        let iconClass
                        if (data == 0) {
                            iconClass = "fw-bold cp-up-doublearrow text-warning";
                        } else if (data == 2) {
                            iconClass = "fw-bold cp-critical-level text-danger";
                        } else if (data == 3) {
                            iconClass = "cp-warning text-primary";
                        } else if (data == 1) {
                            iconClass = "fw-bold cp-down-doublearrow text-success";
                        }
                        const tooltip = data == 0 ? "High" : data == 1 ? "Low" : data == 2 ? "Critical" : "Information"
                        return `<span><i class="${iconClass}"></i> ${tooltip}</span>`;
                    }
                },
                {
                    "render": function (data, type, row) {
                        let propertics = JSON.parse(row.properties)
                        propertiesvalue = propertics.propertiesvalue;
                        if (createPermission == "true" && deletePermission == "true") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="edit-button" name="overallupdate" catagoryIcon="${JSON.parse(row.properties).catagoryicon}" nameicon="${JSON.parse(row.properties).Nameicon}"  data_severity="${row.severity}"  data_impactType="${row.driftImpactTypeId}" data_catagory="${row.driftCategoryId}" data_name="${row.name}" updateId="${row.id}" onclick="overall_Edit(this)" data-bs-toggle="modal" data-bs-target="#CreateModal" data_textpath='${propertiesvalue}'>
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="delete-button" delete_id="${row.id}" deletename="${row.name}" onclick="overalldeleteBtn(this)" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>`;
                        }
                        if (createPermission == "false" && deletePermission == "true") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                                        <i class="cp-edit"></i>
                                                    </span>
                                <span role="button" title="Delete" class="delete-button" delete_id="${row.id}" deletename="${row.name}" onclick="overalldeleteBtn(this)" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>`;
                        }
                        if (createPermission == "true" && deletePermission == "false") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="edit-button" name="overallupdate" catagoryIcon="${JSON.parse(row.properties).catagoryicon}" nameicon="${JSON.parse(row.properties).Nameicon}"  data_severity="${row.severity}"  data_impactType="${row.driftImpactTypeId}" data_catagory="${row.driftCategoryId}" data_name="${row.name}" updateId="${row.id}" onclick="overall_Edit(this)" data-bs-toggle="modal" data-bs-target="#CreateModal" data_textpath='${propertiesvalue}'>
                                    <i class="cp-edit"></i>
                                <span role="button" title="Delete" class="icon-disabled">
                                                        <i class="cp-Delete"></i>
                                                    </span>    
                            </div>
                        </td>`;
                        }
                        if (createPermission == "false" && deletePermission == "false") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                                        <i class="cp-edit"></i>
                                                    </span>
                               <span role="button" title="Delete" class="icon-disabled">
                                                        <i class="cp-Delete"></i>
                                                    </span>
                            </div>
                        </td>`;
                        }
                    },

                    "orderable": false,
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        }
    )
    $('#search-inp').on('keydown input', driftparameterdebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        } 
            const inputValue = $('#search-inp').val();
            selectedValues.push(inputValue);
            var currentPage = dataTable.page.info().page + 1;
            if (!isNaN(currentPage)) {
                dataTable.ajax.reload(function (json) {
                    if (e.target.value && json.recordsFiltered === 0) {
                        $('.dataTables_empty').text('No matching records found');
                    }
                }, false)
            }
    }, 500))
})
$(".parameter_cancel").on("click", function () {
    ClearErrorElements()
})
$("#parameter_create").on("click", function () { 
    clearvalue()
    getcatagory()
    getImpacttype()
    $("#Nameicon_Selected").attr("class", "cp-aborted")
    $("#catagaory_icon").attr("class", "cp-aborted")
})
function clearvalue() {
    $("#parameter_checkbox").prop("checked", false)
    $("#parameter_name,#parameter_catagory,#parameter_impact_type,#parameter_severity,#parameter_textpath").val("")
    $('#Drift_parameter_save').text("Save");
    $('#collapsecatagoryIcon').removeClass('show')
    $('#collapseIcon').removeClass('show')
}
function ClearErrorElements() {
    $("#parameter_textpath_error,#parameter_name_error,#parameter_handling_mode_error,#parameter_catagory_error,#parameter_impact_type_error, #parameter_severity_error,#parameter_find_by_error,#parameter_threshold_error").text('').removeClass('field-validation-error');
}
function validateDriftDropDown(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg);
        errorElement.addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
        return true;
    }
}
async function validateDriftNameDropDown(value, errorMsg, errorElement, url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg);
        errorElement.addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    var url = RootUrl + url;
    var data = {};
    data.id = null;
    data.name = value
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsSameNameExist(url, data)
    ];

    const failedValidations = validationResults.filter(result => result !== true);

    if (failedValidations.length > 0) {
        errorElement.text(failedValidations[0]);
        errorElement.addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
        return true;
    }
}
async function IsSameNameExist(url, inputValue) {
    return !inputValue.name.trim() || $("#Drift_parameter_save").text() == "Update" || $("#Add_profile_catagory_save").text() == "Update" || $("#Add_profile_Type_save").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Name already exists" : true;
}
var Nameicon_type = ""
var catagoryicon_type = ""
$("#collapseIcon div.Category_Icon i").on('click', function () {
    Nameicon_type = $(this).attr("title")
    if ($(this).length) {
        Nameicon_type = $(this)[0].classList[0]
    }
    $("#Nameicon_Selected").attr("class", $(this).attr("class"))
    $('#collapseIcon').removeClass('show')
})
$("#collapsecatagoryIcon div.Category_Icon i").on('click', function () {
    catagoryicon_type = $(this).attr("title")
    if ($(this).length) {
        catagoryicon_type = $(this)[0].classList[0]
    }
    $("#catagaory_icon").attr("class", $(this).attr("class"))
    $('#collapsecatagoryIcon').removeClass('show')
})
$("#parameter_name").on("input", driftparameterdebounce( async function () {
    let value = await sanitizeInput($("#parameter_name").val());
    $("#parameter_name").val(value);
    validateDriftNameDropDown(value, "Enter parameter name", $("#parameter_name_error"), isNameExits)
}, 400))

$("#parameter_catagory").on("change", function () {
        validateDriftDropDown($(this).val(), "Select category", $("#parameter_catagory_error"))
})
$("#parameter_impact_type").on("change", function () {
        validateDriftDropDown($(this).val(), "Select impact type", $("#parameter_impact_type_error"))
})
$("#parameter_severity").on("change", function () {
        validateDriftDropDown($(this).val(), "Select severity", $("#parameter_severity_error"))
})
$("#parameter_threshold").on("keypress", function (event) {
    ["e", "E", "+", "-", "."].includes(event.key) && event.preventDefault()
    if ($(this).val().length >= 6) {
        event.preventDefault()
    }
})
$("#parameter_threshold").on("input", function (e) {      
     validateDriftDropDown($(this).val(), "Enter threshold", $("#parameter_threshold_error"))
})
$("#parameter_textpath").on("input", function () {
    validateDriftDropDown($(this).val(), "Enter path", $("#parameter_textpath_error"))
})
$("#parameter_execute").on("click", async function () {
    let textpath = validateDriftDropDown($("#parameter_textpath").val(), "Enter path", $("#parameter_textpath_error"))
    if (textpath) {
        executioncommment()
    }
})
 async function executioncommment() {
    await $.ajax({
        type: "POST",
        url: RootUrl + "Drift/DriftParameter/GetCplValidation",
        dataType: 'text',
        data: { script: $("#parameter_textpath").val() },
        success: function (result) {
            //const errorElement = $("#parameter_textpath_error")
            //if (result == null || result == undefined || result == "") {
            //    errorElement.text("Comment Success");
            //    errorElement.removeClass('field-validation-error');
            //    errorElement.addClass('text-success');
            //    errorElement.addClass('text-end');
            //    errorElement.css("width", "100%");
            //    errorElement.css("position", "absolute");
            //    return true;
            //} else {
            //    errorElement.text(result);
            //    errorElement.removeClass('text-success');
            //    errorElement.addClass('field-validation-error');
            //    return false;
            //}   
        },
    })
}
$("#Drift_parameter_save").on("click", async function () {
    const form = $("#CreateModal")

    const parameter_name = await validateDriftNameDropDown($("#parameter_name").val(), "Enter parameter name", $("#parameter_name_error"), isNameExits)

    const parameter_catagory = validateDriftDropDown($("#parameter_catagory").val(), "Select category", $("#parameter_catagory_error"))

    const parameter_impact_type = validateDriftDropDown($("#parameter_impact_type").val(), "Select impact type", $("#parameter_impact_type_error"))

    const parameter_severity = validateDriftDropDown($("#parameter_severity").val(), "Select severity", $("#parameter_severity_error"))

    const parameter_path = await validateDriftDropDown($("#parameter_textpath").val(), "Enter path", $("#parameter_textpath_error"))
    let datas
    
    if (parameter_path) {
        await $.ajax({
            type: "POST",
            url: RootUrl + "Drift/DriftParameter/GetCPLValidation",
            dataType: 'text',
            data: { script: $("#parameter_textpath").val() },
            success: function (result) {
                datas = true
                //const errorElement = $("#parameter_textpath_error")
                //if (result == null || result == undefined || result == "") {
                //    datas = true
                //    errorElement.text("");
                //    errorElement.removeClass('field-validation-error');
                //    //return true;
                //} else {
                //    datas = false
                //    errorElement.text(result);
                //    errorElement.addClass('field-validation-error');
                //    //return false;
                //}
            },
        })
    }
    if (parameter_name && parameter_path && parameter_catagory && parameter_impact_type && parameter_severity && datas)  {
        form.trigger("submit")
        var propertydata = JSON.stringify({
            "propertiesvalue": $("#parameter_textpath").val(),
            "Nameicon": Nameicon_type == "" ? "cp-aborted" : Nameicon_type,
            "catagoryicon": catagoryicon_type == "" ? "cp-aborted" : catagoryicon_type
        })
        let data = {
            "Name": $("#parameter_name").val(),
            "DriftCategoryId": $("#parameter_catagory").val(),
            "DriftCategoryName": $("#parameter_catagory option:selected").text(),
            "DriftImpactTypeId": $("#parameter_impact_type").val(),
            "DriftImpactTypeName": $("#parameter_impact_type option:selected").text(),
            "Severity":Number($("#parameter_severity").val()),
            "Properties": propertydata ,
            __RequestVerificationToken: gettoken()
        }

        $('#Drift_parameter_save').text() === "Update" ? data["id"] = globalId : null

        await $.ajax({
            type: "POST",
            url: RootUrl + "Drift/DriftParameter/CreateOrUpdate",
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result.data
                if (result.success) {
                    $('#CreateModal').modal('hide');
                    notificationAlert('success', data.message)
                    setTimeout(() => {
                        dataTable.ajax.reload()
                    },1000) 
                } else {
                    errorNotification(result)
                }
            },
        })
        $('#Drift_parameter_save').text("Save");
        $("#parameter_name").val("")
        $("#parameter_catagory,#parameter_impact_type,#parameter_severity,#parameter_textpath").val("").trigger("change")
        ClearErrorElements()
    }
})
function overall_Edit(data) {
    getcatagory()
    getImpacttype()
    if ($(data).attr("name") == "overallupdate") {
        $('#Drift_parameter_save').text("Update");
        globalId = $(data).attr('updateId');
        globalDriftData.forEach((data) => {       
            if (globalId === data.id) {  
                let propertiesvalue = JSON.parse(data?.properties)?.propertiesvalue
                $("#parameter_textpath").val(propertiesvalue)
            }
        })
        $("#parameter_name").val($(data).attr('data_name'))
        setTimeout(() => {
            $("#parameter_catagory").val($(data).attr('data_catagory')).trigger("change")
            $("#parameter_impact_type").val($(data).attr('data_impactType')).trigger("change")
            $("#parameter_severity").val($(data).attr('data_severity')).trigger("change")
            //$("#parameter_textpath").val($(data).attr('data_textpath'))
            $("#Nameicon_Selected").attr("class", $(data).attr('nameicon'))
            //$("#catagaory_icon").attr("class", $(data).attr('catagoryIcon'))
        }, 800)
    }
}
function overalldeleteBtn(data){
    globalDeleteid = $(data).attr('delete_id')
    $("#overall_deleted_id").text($(data).attr('deletename')).attr("title", $(data).attr('deletename'));
}
$("#overall_confirmDeleteButton").on("click", async function () {
    await $.ajax({
        type: "POST",
        url: RootUrl + "Drift/Driftparameter/Delete",
        dataType: "json",
        data: {
            id: globalDeleteid,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let data = result.data
            if (result.success) {
                $('#DeleteModal').modal('hide');
                notificationAlert("success", data.message)
                dataTable.ajax.reload()
            } else {
                errorNotification(result)
            }
        },
    })
})
//impact type
$(".add_profile_Type_cancel").on("click", function () {
    $("#Add_profile_Type").val("")
    $("#Add_profile_type_error").text("").removeClass('field-validation-error');
})
$("#ImpactTypeModal").on("click", function () {
    $("#Add_profile_Type_save").text("Save")
    getImpacttype()
})

const getImpacttype =async () => {
    $("#impacttype_modal tbody").empty()
   await $.ajax({
        type: "GET",
        url: RootUrl + "Drift/DriftParameter/GetDriftImpactTypeList",
        dataType: "json",
        success: function (result) {
            let data = result.data
            $("#parameter_impact_type").empty()
            if (result.success) {
                data.forEach(function (item, i) {
                    let sno = i + 1
                    $('#parameter_impact_type').append('<option value=""></option>')
                    $("#parameter_impact_type").append('<option value="' + item.id + '">' + item.impactType + '</option>')
                    $("#impacttype_modal tbody ").append('<tr>' +
                        '<td>' + sno + '</td>' +
                        '<td>' + item.impactType + '</td>' +
                        '<td>' +
                        '<div class="d-flex align-items-center gap-2" >' +
                        '<span role="button" title="Edit" class=" edit-button" onclick="edit_impact_type(this)" name="editcatagory" data_id="' + item.id + '" data_impacttype="' + item.impactType + '">' +
                        '<i class="cp-edit"></i>' +
                        '</span>' +
                        '<span role="button" title="Delete" class="delete-button" data-bs-toggle="modal" onclick="impacttypedelete(this)" deletename="' + item.impactType + '" deleteId="' + item.id + '" data-bs-target="#DeleteModal2">' +
                        '<i class="cp-Delete"></i>' +
                        '</span>' +
                        '</div>' +
                        '</td>' +
                        '</tr>')
                })
            } else {
                errorNotification(result)
            }
        },
    })
}
function edit_impact_type(data) {
    globalImpactTypeId = $(data).attr('data_id');
    $("#Add_profile_Type").val($(data).attr('data_impacttype'))
    $("#Add_profile_Type_save").text("Update")
}
$("#Add_profile_Type").on("input",async function () {
    let value = await sanitizeInput($("#Add_profile_Type").val());
    $("#Add_profile_Type").val(value);
    validateDriftNameDropDown(value, "Enter impact type", $("#Add_profile_type_error"), isimapacttypeNameExits)
})
$("#Add_profile_Type_save").on("click",async function () {
    const form = $("#ImpactTypeModalToggle")
    const add_profile = await validateDriftNameDropDown($("#Add_profile_Type").val(), "Enter impact type", $("#Add_profile_type_error"), isimapacttypeNameExits)
    if (add_profile) {  
        form.trigger("submit")
            let data = {
                "ImpactType": $("#Add_profile_Type").val(),
                __RequestVerificationToken: gettoken()
            }
        $('#Add_profile_Type_save').text() === "Update" ? data["id"] = globalImpactTypeId : null
            await $.ajax({
                type: "POST",
                url: RootUrl + "Drift/DriftParameter/DriftImpactTypeCreateOrUpdate",
                dataType: "json",
                data: data,
                success: function (result) {
                    let data = result.data
                    if (result.success) {
                        notificationAlert("success", data.message)
                        getImpacttype()
                    } else {
                        errorNotification(result)
                    }
                },
            })
        $("#Add_profile_Type_save").text("Save")
        $("#Add_profile_Type").val("")
        $("#Add_profile_type_error").text("").removeClass('field-validation-error');
    }
})
function impacttypedelete(data) {
    globalDeleteid = $(data).attr('deleteId')
    $("#impacttype_deleted_id").text($(data).attr('deletename')).attr("title", $(data).attr('deletename'));
}
$("#impacttype_confirmDeleteButton_cancel").on("click", function () {
    delete_impacttype_cancel()
})
function delete_impacttype_cancel() {
    $("#Add_profile_Type").val("")
    $("#Add_profile_type_error").text("").removeClass('field-validation-error');
    $('#DeleteModal2').modal('hide');
    $('#ImpactTypeModalToggle').modal('show');
    $("#Add_profile_Type_save").text("Save")
}
$("#impacttype_confirmDeleteButton").on("click", async function () {
    await $.ajax({
        type: "POST",
        url: RootUrl + "Drift/DriftParameter/DeleteImpactType",
        dataType: "json",
        data: {
            id: globalDeleteid,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let data = result.data
            if (result.success) {
                notificationAlert("success", data.message)
                getImpacttype()
                delete_impacttype_cancel()
            } else {
                errorNotification(result)
            }
        },
    })
})
//catagory 
$("#CategoryModal").on("click", function () {
    $("#Add_profile_catagory").val("")
    $("#Add_profile_catagory_error").text("").removeClass('field-validation-error');
    $("#catagaory_icon").attr("class", "cp-aborted")
    $("#Add_profile_catagory_save").text("Save")
    getcatagory()
})
const getcatagory= async () => {
    $("#catagory_table tbody").empty()
    await $.ajax({
        type: "GET",
        url: RootUrl + "Drift/DriftParameter/GetDriftCategoryList",
        dataType: "json",
        success: function (result) {
            let data = result.data
            if (result.success) {
                $("#parameter_catagory").empty()
                data.forEach(function (item, i) { 
                    let sno = i + 1
                    $('#parameter_catagory').append('<option value=""></option>')
                    $("#parameter_catagory").append('<option value="' + item.id + '">' + item.categoryName + '</option>')

                        $("#catagory_table tbody ").append('<tr>'+
                            '<td>'+sno+'</td>'+
                            '<td>' + item.categoryName+ '</td>'+
                            '<td>'+
                                '<div class="d-flex align-items-center gap-2" >'+
                            '<span role="button" title="Edit" class=" edit-button" onclick="edit_catagory(this)" name="editcatagory" data_id="' + item.id + '" data_catagoryIcon="'+item.logo+'" data_categoryName="' + item.categoryName+'">'+
                                       '<i class="cp-edit"></i>'+
                                    '</span>'+
                            '<span role="button" title="Delete" class="delete-button" data-bs-toggle="modal" onclick="catagorydelete(this)" deletename="' + item.categoryName +'" deleteId="'+item.id+'" data-bs-target="#DeleteModal1">'+
                                        '<i class="cp-Delete"></i>'+
                                    '</span>'+
                                '</div>'+
                            '</td>'+
                            '</tr>')
                })
            } else {
                errorNotification(result)
            }
        },
    })
}
function edit_catagory(data) {
    globalCatagoryId = $(data).attr('data_id');
    $("#Add_profile_catagory").val($(data).attr('data_categoryName'))
    setTimeout(() => {
        $("#catagaory_icon").attr("class", $(data).attr('data_catagoryIcon'))
    },500)
    $("#Add_profile_catagory_save").text("Update")
}
$(".add_profile_catagory_cancel").on("click", function () {
    $("#Add_profile_catagory").val("")
    $("#Add_profile_catagory_error").text("").removeClass('field-validation-error');
})
$("#Add_profile_catagory").on("input", async function () {
    let value = await sanitizeInput($("#Add_profile_catagory").val());
    $("#Add_profile_catagory").val(value);
    validateDriftNameDropDown(value, "Enter category name", $("#Add_profile_catagory_error"), isCatagoryNameExits)
})
$("#Add_profile_catagory_save").on("click", async function () {
    const form = $("#CategoryModalToggle")
    const add_profile = await validateDriftNameDropDown($("#Add_profile_catagory").val(), "Enter category name", $("#Add_profile_catagory_error"), isCatagoryNameExits)
    if (add_profile) {
        form.trigger("submit")
        let data = {
            "CategoryName": $("#Add_profile_catagory").val(),
            "Logo": catagoryicon_type == "" ? "cp-aborted" : catagoryicon_type,
            __RequestVerificationToken: gettoken()
        }

        $('#Add_profile_catagory_save').text() === "Update" ? data["id"] = globalCatagoryId : null

        await $.ajax({
            type: "POST",
            url: RootUrl + "Drift/DriftParameter/DriftCategoryCreateOrUpdate",
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result.data
                if (result.success) {
                    notificationAlert("success", data.message)
                    getcatagory()
                } else {
                    errorNotification(result)
                }
            },
        })
        $("#Add_profile_catagory_save").text("Save")
        $("#Add_profile_catagory").val("")
        $("#catagaory_icon").attr("class", "cp-aborted")
        $("#Add_profile_catagory_error").text("").removeClass('field-validation-error');  
    }
})
function catagorydelete(data) {
    globalDeleteid = $(data).attr('deleteId')
    $("#catagory_deleted_id").text($(data).attr('deletename')).attr("title", $(data).attr('deletename'));
}
$("#catagory_confirmDeleteButton_cancel").on("click", function () {
delete_catagory_cancel()
})
function delete_catagory_cancel() {
    $("#Add_profile_catagory").val("")
    $("#Add_profile_catagory_error").text("").removeClass('field-validation-error');
    $('#DeleteModal1').modal('hide');
    $('#CategoryModalToggle').modal('show');
    $("#Add_profile_catagory_save").text("Save")
}
$("#catagory_confirmDeleteButton").on("click", async function () {
    await $.ajax({
        type: "POST",
        url: RootUrl + "Drift/DriftParameter/DeleteDriftCategory",
        dataType: "json",
        data: {
            id: globalDeleteid,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let data = result.data
            if (result.success) {
                notificationAlert("success", data.message)
                getcatagory()
                delete_catagory_cancel()
            } else {
                errorNotification(result)
            }
        },
    })
})
