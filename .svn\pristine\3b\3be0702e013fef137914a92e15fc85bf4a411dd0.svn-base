using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetDetail;
//using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationGroupModel;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IBulkImportOperationGroupService
{
    Task<List<BulkImportOperationGroupListVm>> GetBulkImportOperationGroupList();
    Task<BaseResponse> CreateAsync(CreateBulkImportOperationGroupCommand createBulkImportOperationGroupCommand);
    Task<BaseResponse> UpdateAsync(UpdateBulkImportOperationGroupCommand updateBulkImportOperationGroupCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<BulkImportOperationGroupDetailVm> GetByReferenceId(string id);
    Task<List<BulkImportOperationGroupListVm>> GetBulkImportOperationGroupByOperationId(string operationId);

    #region NameExist

    // Task<bool> IsBulkImportOperationGroupNameExist(string name, string? id);

    #endregion

    #region Paginated

    // Task<PaginatedResult<BulkImportOperationGroupListVm>> GetPaginatedBulkImportOperationGroups(GetBulkImportOperationGroupPaginatedListQuery query);

    #endregion
}
