﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class InfraObjectRepositoryMocks
{
    public static Mock<IInfraObjectRepository> CreateInfraObjectRepository(List<InfraObject> infraObjects)
    {
        var infraObjectRepository = new Mock<IInfraObjectRepository>();

        infraObjectRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjects);

        infraObjectRepository.Setup(repo => repo.AddAsync(It.IsAny<InfraObject>())).ReturnsAsync(
            (InfraObject infraObject) =>
            {
                infraObject.Id = new Fixture().Create<int>();
                infraObject.ReferenceId = new Fixture().Create<Guid>().ToString();
                infraObjects.Add(infraObject);

                return infraObject;
            });

        return infraObjectRepository;
    }

    public static Mock<IInfraObjectRepository> UpdateInfraObjectRepository(List<InfraObject> infraObjects)
    {
        var infraObjectRepository = new Mock<IInfraObjectRepository>();

        infraObjectRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjects);

        infraObjectRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraObjects.SingleOrDefault(x => x.ReferenceId == i));

        infraObjectRepository.Setup(repo => repo.UpdateAsync(It.IsAny<InfraObject>())).ReturnsAsync((InfraObject infraObject) =>
        {
            var index = infraObjects.FindIndex(item => item.ReferenceId == infraObject.ReferenceId);

            infraObjects[index] = infraObject;

            return infraObject;
        });

        return infraObjectRepository;
    }

    public static Mock<IInfraObjectRepository> DeleteInfraObjectRepository(List<InfraObject> infraObjects)
    {
        var infraObjectRepository = new Mock<IInfraObjectRepository>();

        infraObjectRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjects);

        infraObjectRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraObjects.SingleOrDefault(x => x.ReferenceId == i));

        infraObjectRepository.Setup(repo => repo.GetInfraObjectByBusinessFunctionId(It.IsAny<string>()))
            .ReturnsAsync(new List<InfraObject>());

        //infraObjectRepository.Setup(repo=>repo.GetInfraObjectByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(infraObjects);

        infraObjectRepository.Setup(repo => repo.UpdateAsync(It.IsAny<InfraObject>())).ReturnsAsync((InfraObject infraObject) =>
        {
            var index = infraObjects.FindIndex(item => item.ReferenceId == infraObject.ReferenceId);

            infraObject.IsActive = false;
            infraObjects[index] = infraObject;

            return infraObject;
        });

        return infraObjectRepository;
    }

    public static Mock<IInfraObjectRepository> GetInfraObjectRepository(List<InfraObject> infraObjects)
    {
        var infraObjectRepository = new Mock<IInfraObjectRepository>();

        infraObjectRepository.Setup(repo => repo.GetInfraObjectListByReplicationCategoryType(It.IsAny<string>())).ReturnsAsync(infraObjects);

        infraObjectRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjects);

        infraObjectRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraObjects.SingleOrDefault(x => x.ReferenceId == i));

        return infraObjectRepository;
    }

    //public static Mock<IInfraObjectRepository> GetInfraObjectList(InfraObject infraObjects)
    //{
    //    var infraObjectRepository = new Mock<IInfraObjectRepository>();

    //    infraObjectRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjects);

    //    return infraObjectRepository;
    //}

    public static Mock<IInfraObjectRepository> GetInfraObjectEmptyRepository()
    {
        var infraObjectRepository = new Mock<IInfraObjectRepository>();

        infraObjectRepository.Setup(repo => repo.GetInfraObjectListByReplicationCategoryType(It.IsAny<string>())).ReturnsAsync(new List<InfraObject>());

        infraObjectRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<InfraObject>());

        infraObjectRepository.Setup(repo => repo.GetInfraObjectByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(new List<InfraObject>());

        infraObjectRepository.Setup(repo => repo.GetInfraObjectListByReplicationTypeId(It.IsAny<string>())).ReturnsAsync(new List<InfraObject>());

        return infraObjectRepository;
    }

    public static Mock<IInfraObjectRepository> GetInfraObjectNamesRepository(List<InfraObject> infraObjects)
    {
        var infraObjectRepository = new Mock<IInfraObjectRepository>();
        infraObjectRepository.Setup(repo => repo.GetInfraObjectNames()).ReturnsAsync(infraObjects);
        infraObjectRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjects);

        return infraObjectRepository;
    }

    public static Mock<IInfraObjectRepository> GetInfraObjectNameUniqueRepository(List<InfraObject> infraObjects)
    {
        var infraObjectRepository = new Mock<IInfraObjectRepository>();

        infraObjectRepository.Setup(repo => repo.IsInfraObjectNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => infraObjects.Exists(x => x.Name == i && x.ReferenceId == j));

        return infraObjectRepository;
    }

    public static Mock<IInfraObjectRepository> GetPaginatedInfraObjectRepository(List<InfraObject> infraObjects)
    {
        var infraObjectRepository = new Mock<IInfraObjectRepository>();

        var queryableInfraObject = infraObjects.BuildMock();

        infraObjectRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableInfraObject);

        return infraObjectRepository;
    }

    public static Mock<IInfraObjectRepository> GetInfraObjectByBusinessFunctionId(List<InfraObject> infraObjects)
    {
        var infraObjectRepository = new Mock<IInfraObjectRepository>();

        infraObjectRepository.Setup(repo => repo.GetInfraObjectByBusinessFunctionId(It.IsAny<string>())).ReturnsAsync(infraObjects);

        return infraObjectRepository;
    }

    public static Mock<IInfraObjectRepository> GetInfraObjectByServerId(List<InfraObject> infraObjects)
    {
        var infraObjectRepository = new Mock<IInfraObjectRepository>();

        infraObjectRepository.Setup(repo => repo.GetInfraObjectByServerId(It.IsAny<string>())).ReturnsAsync(infraObjects);

        return infraObjectRepository;
    }

    public static Mock<IInfraObjectRepository> GetInfraObjectByNodeIdRepository(List<InfraObject> infraObjects)
    {
        var infraObjectRepository = new Mock<IInfraObjectRepository>();

        infraObjectRepository.Setup(repo => repo.GetInfraObjectByNodeId(It.IsAny<string>())).ReturnsAsync(infraObjects);

        return infraObjectRepository;
    }

    public static Mock<IInfraObjectRepository> GetInfraObjectListByReplicationTypeIdRepository(List<InfraObject> infraObjects)
    {
        var infraObjectRepository = new Mock<IInfraObjectRepository>();

        infraObjectRepository.Setup(repo => repo.GetInfraObjectListByReplicationTypeId(It.IsAny<string>())).ReturnsAsync(infraObjects);

        return infraObjectRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateInfraObjectEventRepository(List<UserActivity> userActivities)
    {
        var infraObjectEventRepository = new Mock<IUserActivityRepository>();

        infraObjectEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        infraObjectEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();
                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();
                userActivities.Add(userActivity);

                return userActivity;
            });

        return infraObjectEventRepository;
    }

}
