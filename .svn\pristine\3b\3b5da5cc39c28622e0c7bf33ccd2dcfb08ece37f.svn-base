﻿using ContinuityPatrol.Application.Features.ComponentType.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ComponentType.Events;

public class DeleteComponentTypeEventTests : IClassFixture<ComponentTypeFixture>, IClassFixture<UserActivityFixture>
{
    private readonly ComponentTypeFixture _componentTypeFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly ComponentTypeDeletedEventHandler _handler;

    public DeleteComponentTypeEventTests(ComponentTypeFixture componentTypeFixture, UserActivityFixture userActivityFixture)
    {
        _componentTypeFixture = componentTypeFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockComponentTypeEventLogger = new Mock<ILogger<ComponentTypeDeletedEventHandler>>();

        _mockUserActivityRepository = ComponentTypeRepositoryMocks.CreateComponentTypeEventRepository(_userActivityFixture.UserActivities);

        _handler = new ComponentTypeDeletedEventHandler(mockLoggedInUserService.Object, mockComponentTypeEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteComponentTypeEventDeleted()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_componentTypeFixture.ComponentTypeDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_componentTypeFixture.ComponentTypeDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

}
