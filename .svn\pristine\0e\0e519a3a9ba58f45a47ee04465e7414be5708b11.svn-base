﻿using ContinuityPatrol.Application.Features.RpForVmCGMonitorStatus.Queries.GetPagination;
using ContinuityPatrol.Application.Features.RpForVmMonitorStatus.Queries.GetDetail;
using DevExpress.XtraCharts;
using DevExpress.XtraReports.UI;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Data;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{

    [SupportedOSPlatform("windows")]
    public partial class RP4VMCGMonitoringReport : DevExpress.XtraReports.UI.XtraReport
    {
        public List<RpForVmCGMonitorStatusListVm> ReportDataList;
        public int SrNo;
        private readonly ILogger<RPForVMController> _logger;
        public RP4VMCGMonitoringReport(List<RpForVmCGMonitorStatusListVm> ReportData,string reportGeneratedName)
        {
            try
            {
                _logger = RPForVMController._logger;
                ReportDataList = ReportData;
                InitializeComponent();
                DetailReport.DataSource = ReportDataList;

                var replicationMonitorDetails = JsonConvert.DeserializeObject<ReportDataProperties>(ReportDataList.LastOrDefault().CGProperties);
                var ReplicationMonitoringPRValues = replicationMonitorDetails.VMRecoverPointMonitoringPR.ReplicationMonitoringPR;
                var ReplicationMonitorDrValues = replicationMonitorDetails.VMRecoverPointMonitoring.FirstOrDefault().ReplicationMonitoring;

                lblPluginServerIPAddress.Text = ReplicationMonitoringPRValues.PluginServerIPAddress.ToString();
                lblVCenterName.Text = ReplicationMonitoringPRValues.VCenterName.ToString();
                lblVRPAClusterName.Text = ReplicationMonitoringPRValues.VRPAClusterName.ToString();
                lblVersion.Text = ReplicationMonitoringPRValues.Version.ToString();

                lblDrPluginServerIPAddress.Text = ReplicationMonitorDrValues.PluginServerIPAddress.ToString();
                lblDrVCenterName.Text = ReplicationMonitorDrValues.VCenterName.ToString();
                lblDrVRPAClusterName.Text = ReplicationMonitorDrValues.VRPAClusterName.ToString();
                lblDrVersion.Text = ReplicationMonitorDrValues.Version.ToString();
                _username.Text = "Report Generated By: " + reportGeneratedName;
                SrNo = 0;
            }
            catch(Exception ex)
            {
                _logger.LogError($"Error occured while process the RP4VMCGMonitoringReport : { ex.Message}");
            }


        }
        private void Detail1_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                List<ReplicationMonitoringDetailsReport> ReplicationMonitoringDetailsReports = new List<ReplicationMonitoringDetailsReport>();
                
                
                var replicationMonitorDetails = JsonConvert.DeserializeObject<ReportDataProperties>(ReportDataList[SrNo].CGProperties);
                var ReplicationMonitoringPRValues = replicationMonitorDetails.VMRecoverPointMonitoringPR.ReplicationMonitoringPR;
                ReplicationMonitoringPRValues.Type = replicationMonitorDetails.VMRecoverPointMonitoringPR.Type;
                ReplicationMonitoringDetailsReports.Add(ReplicationMonitoringPRValues);
                foreach (var vmRecoveryMonitoring in replicationMonitorDetails.VMRecoverPointMonitoring)
                {
                    vmRecoveryMonitoring.ReplicationMonitoring.Type = vmRecoveryMonitoring.Type;
                    ReplicationMonitoringDetailsReports.Add(vmRecoveryMonitoring.ReplicationMonitoring);
                }
                DetailReport1.DataSource = ReplicationMonitoringDetailsReports;
                SrNo += 1;
                xrSrNo.Text = SrNo.ToString();
            }
            catch(Exception ex)
            {
                _logger.LogError("Error occured while process the Detail1_BeforePrint in RP4VMCGMonitoringReport" + ex.Message);
            }
        }
        private void XrChart_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var available = ReportDataList.Where(x => x.AvailabilityStatus.ToLower().Equals("available"));
                var newdata = ReportDataList.Where(x => x.AvailabilityStatus.ToLower().Equals("new"));
                var removed = ReportDataList.Where(x => x.AvailabilityStatus.ToLower().Equals("removed"));

                Series series1 = new Series("Series1", ViewType.Doughnut);
                DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                doughnutSeriesView.MinAllowedSizePercentage = 70D;
                series1.View = doughnutSeriesView;
                xrChart1.Series.Add(series1);
                series1.DataSource = CreateChartDataDr(available.Count(), newdata.Count(), removed.Count());
                series1.ArgumentScaleType = ScaleType.Auto;
                series1.ArgumentDataMember = "Argument";
                series1.ValueScaleType = ScaleType.Numerical;
                series1.ValueDataMembers.AddRange(new string[] { "Value" });
                series1.Label.TextPattern = "{A}\n{V}";
                series1.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            }
            catch(Exception ex)
            {
                _logger.LogError("Error occured while process the XrChart_BeforePrint method in RP4VMCGMonitoringReport" + ex.Message);
            }
        }
        private DataTable CreateChartDataDr(Int64 Available, Int64 New, Int64 Removed)
        {
            DataTable table = new DataTable("Table1");

            table.Columns.Add("Argument", typeof(string));
            table.Columns.Add("Value", typeof(Int64));

            Random rnd = new Random();

            table.Rows.Add("Available", Available);
            table.Rows.Add("New", New);
            table.Rows.Add("Removed", Removed);


            return table;
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {

                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) 
            { 
                _logger.LogError("Error occured while display the RP4VM_CG_Monitoring Report's CP Version. The error message : " + ex.Message); throw;
            }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(RPForVMController.CompanyLogo) ? "NA" : RPForVMController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the RP4VM_CG_Monitoring Report's customer logo" + ex.Message.ToString());
            }
        }

    }
    public class ReportDataProperties
    {
        public VMRecoverPointMonitoringPRReport VMRecoverPointMonitoringPR { get; set; } = new();
        public List<VMRecoverPointMonitoringReport> VMRecoverPointMonitoring { get; set; } = new();
    }
    public class VMRecoverPointMonitoringReport
    {
        public string Type { get; set; }
        public ReplicationMonitoringDetailsReport ReplicationMonitoring { get; set; } = new();

    }
    public class VMRecoverPointMonitoringPRReport
    {
        public string Type { get; set; }
        public ReplicationMonitoringDetailsReport ReplicationMonitoringPR { get; set; } = new();
    }
    public class ReplicationMonitoringDetailsReport
    {
        public string Type { get; set; }
        public string PluginServerIPAddress { get; set; }
        public string VCenterName { get; set; }
        public string VRPAClusterName { get; set; }
        public string Version { get; set; }
        public string ProtectedVMStatus { get; set; }
        public string ProtectedVMNames { get; set; }
    }
}
