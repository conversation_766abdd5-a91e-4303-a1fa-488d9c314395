﻿namespace ContinuityPatrol.Application.Features.Server.Queries.GetType;

public class GetServerTypeQueryHandler : IRequestHandler<GetServerTypeQuery, List<ServerTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly IServerViewRepository _serverViewRepository;

    public GetServerTypeQueryHandler(IMapper mapper, IServerViewRepository serverViewRepository)
    {
        _mapper = mapper;
        _serverViewRepository = serverViewRepository;
    }

    public async Task<List<ServerTypeVm>> Handle(GetServerTypeQuery request, CancellationToken cancellationToken)
    {
        var servers = request.ServerTypeId != null
            ? (await _serverViewRepository.GetType(request.ServerTypeId)).ToList()
            : await _serverViewRepository.ListAllAsync();

        return servers.Count <= 0 ? new List<ServerTypeVm>() : _mapper.Map<List<ServerTypeVm>>(servers);
    }
}