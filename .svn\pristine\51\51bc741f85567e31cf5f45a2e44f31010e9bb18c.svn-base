﻿using ContinuityPatrol.Application.Features.TemplateHistory.Queries.GetTemplateHistoryByTemplateId;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class TemplateHistoryController : CommonBaseController
{
    [HttpGet("{id}", Name = "GetTemplateHistory")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<TemplateHistoryByIdVm>> TemplateHistoryById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "TemplateHistory Id");

        Logger.LogInformation($"Get TemplateHistory Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetTemplateHistoryByTemplateIdQuery { TemplateId = id }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllTemplateHistoryCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}
