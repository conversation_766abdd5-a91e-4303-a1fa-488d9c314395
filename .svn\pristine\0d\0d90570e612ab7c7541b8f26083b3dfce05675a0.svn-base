const datasetURL = {
    getPagination: "/Admin/DataSet/GetPagination",
    getDbDetail: "Admin/DataSet/GetDbDetail",
    nameExistUrl: "Admin/DataSet/DataSetNameExist",
    runQuery: "Admin/DataSet/RunQuery",
    getColumnNames: "Admin/DataSet/GetColumnNamesBySchemaNameAndTableName",
    getTableNames: "Admin/TableAccess/GetTableNamesBySchemaName",
    datasetCreateOrUpdate: "Admin/DataSet/CreateOrUpdate",
    datasetDelete: "Admin/DataSet/Delete"
}
let dbValue = '', queryResult = '', schemaName;

$(function () {
    let Permission = {
        'Create': $("#AdminCreate").data("create-permission").toLowerCase(),
        'Delete': $("#AdminDelete").data("delete-permission").toLowerCase()
    }
    if (Permission.Create == 'false') {
        $("#btnDataSetCreate").addClass("btn-disabled").css("cursor", "not-allowed").removeAttr("data-bs-toggle data-bs-target id");
    }
    let columnEditValue = [];
    let selectedValues = [];
    let dataTable = $('#dataSetTabel').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow"></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        "processing": true,
        "serverSide": false,
        "filter": true,
        "Sortable": true,
        "order": [],
        "ajax": {
            "type": "GET",
            "url": datasetURL.getPagination,
            "dataType": "json",
            "data": function (d) {
                d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                d.pageSize = d?.length;
                d.searchString = selectedValues?.length === 0
                    ? $('#search-inp').val()
                    : selectedValues.join(';');
                selectedValues.length = 0;
            },
            "dataSrc": function (json) {
                json.recordsTotal = json?.totalPages || 0;
                json.recordsFiltered = json?.totalCount || 0;
                $(".pagination-column").toggleClass("disabled", json?.data?.length === 0);
                return json.data;
            }
        },
        "columns": [
            {
                "data": null,
                "name": "Sr. No.",
                "autoWidth": true,
                "orderable": false,
                "render": (data, type, row, meta) => type === 'display' ? meta.row + 1 : data
            },
            {
                "data": "dataSetName",
                "name": "Name",
                "autoWidth": true,
                "render": (data, type) => type === 'display' ? (data || 'NA') : data
            },
            {
                "data": "description",
                "name": "Description",
                "autoWidth": true,
               "render": (data, type) => {
                    return type === 'display'
                        ? `<span title="${data || 'NA'}" class="text-truncate" style="max-width:450px; display:inline-block">${data || 'NA'}</span>`
                        : data;
                }
            },
            {
                "data": "storedQuery",
                "name": "Stored Query",
                "autoWidth": true,
                "render": (data, type) => {
                    return type === 'display'
                        ? `<span title="${data || 'NA'}" class="text-truncate" style="max-width:600px; display:inline-block">${data || 'NA'}</span>`
                        : data;
                }
            },
            {
                "orderable": false,
                "render": function (data, type, row) {
                    const isEditAllowed = Permission.Create === "true";
                    const isDeleteAllowed = Permission.Delete === "true";

                    const editBtn = isEditAllowed
                        ? `<span role="button" title="Edit" class="btnDataSetEdit" data-dataset='${JSON.stringify(row)}'><i class="cp-edit"></i></span>`
                        : `<span role="button" title="Edit" class="btn-disabled"><i class="cp-edit"></i></span>`;

                    const deleteBtn = isDeleteAllowed
                        ? `<span role="button" title="Delete" class="btnDataSetDelete" data-dataset-id="${row.id}" data-dataset-name="${row.dataSetName}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i></span>`
                        : `<span role="button" title="Delete" class="btn-disabled"><i class="cp-Delete"></i></span>`;

                    return `<div class="d-flex align-items-center gap-2">${editBtn}${deleteBtn}</div>`;
                }
            }
        ],
        "columnDefs": [
            { targets: [1, 2], className: "truncate" }
        ],
        "rowCallback": function (row, data, index) {
            const startIndex = this.api().context[0]._iDisplayStart;
            $('td:eq(0)', row).html(startIndex + index + 1);
        },
        initComplete: function () {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
        }
    });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('.form-select-sm').select2({
        "language": {
            "noResults": function () {
                return "No Results Found";
            }
        },
    });

    $('#search-inp').on('input', commonDebounce(function () {
        const inputValue = this.value.trim();

        const selectedValues = [];
        ['#DatasetName', '#Description', '#storedQuery'].forEach(selector => {
            const checkbox = $(selector);
            if (checkbox.is(':checked')) {
                selectedValues.push(checkbox.val() + inputValue);
            }
        });
        dataTable.ajax.reload(json => {
            const message = inputValue.length === 0
                ? (json?.data?.data?.length === 0 ? 'No Data Found' : '')
                : (json?.recordsFiltered === 0 ? 'No matching records found' : '');
            if (message) $('.dataTables_empty').text(message);
        });
    }, 500));

    const getDbProvider = async () => {
        await $.ajax({
            type: "GET",
            url: RootUrl + datasetURL.getDbDetail,
            data: {},
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result.success && typeof result === "object" && result.data.length !== 0) {
                    dbValue = result.data

                } else {
                    errorNotification(result)
                }
            }
        })
    };

    getDbProvider();
     function dataSetEdit(datasetData) {
        clearPreviousData();
        $('#datasetName').val(datasetData?.dataSetName);
        $('#datasetDescription').val(datasetData?.description);
        $('#tableAccessId').val(datasetData?.tableAccessId);
        $('#id').val(datasetData?.id);
        $('#datasetStoredQuery').val(datasetData?.storedQuery);
        columnEditValue = [];
        const dbType = dbValue?.toLowerCase();
        let schemaData = "", tableData = "";
        let match;
        if (dbType === 'mysql') {
            match = datasetData?.storedQuery?.match(/from `([^`]+)`\.`([^`]+)`/i);
        } else if (dbType === 'oracle') {
            match = datasetData?.storedQuery?.match(/FROM\s+"([^"]+)"\."([^"]+)"/i);
        } else {
            match = datasetData?.storedQuery?.match(/\[([^\]]+)\]/g)?.map(v => v.slice(1, -1));
        }
        if (match) {
             schemaData = (dbType === 'oracle' || dbType === 'mysql')
                ? match[1]
                : (dbType === 'mssql' ? match[0]?.replace(/\[|\]/g, '')?.trim() : undefined);
           
            tableData = dbType === 'mysql' ? match[2]?.trim() : match[2]?.replace(/\[|\]/g, '')?.trim();
        }
        $('#datasetSchemaName').val(schemaData);
        const queryParts = datasetData?.storedQuery?.split(/from|\./);
        let columns = [];
        if (dbType === 'oracle') {
            columns = queryParts.join(' ').match(/SELECT\s+(.+)\s+FROM/i)?.[1]?.replace(/"/g, '')?.split(',').map(col => col.trim()) || [];
        } else {
            columns = queryParts[0]?.split(/Select|\,/)?.map(col => col.trim()) || [];
        }
        columns.forEach(col => {
            if (col && col !== '*') {
                const aliasMatch = col.match(/as (\w+)/i);
                columnEditValue.push(aliasMatch ? aliasMatch[1] : col);
            }
        });
        GetDataSetBySchemaName(schemaData, 'datasetTableName', datasetData?.primaryTableName ?? tableData)
            .then(() => {
                return GetDataSetByName(schemaData, datasetData?.primaryTableName ?? tableData, 'datasetColumnName');
            })
            .then(() => {
                modifiedQuery(datasetData?.storedQuery);
            });
       
        const selectors = [
            '#DataSetName-error',
            '#Description-error',
            '#selectschemaerror',
            '#selecttableerror',
            '#PrimaryTableNameerror',
            '#StoredQuery-error'
        ];

        selectors.forEach(selector => {
            $(selector).text('').removeClass('field-validation-error');
        });
    }

    async function GetDataSetBySchemaName(SchemaName, elementId, tableName) {
        let data = { SchemaName };
        let $element = $("#" + elementId).empty();
        let result = await $.getJSON(RootUrl + datasetURL.getTableNames, data);
        if (result?.success && result?.data?.length) {
            $element.append('<option></option>').append(result.data.map(item =>
                $('<option>', { value: item.tableName, tableId: item.id, text: item.tableName })
            ));
            if (tableName) $('#datasetTableName').val(tableName);
        } else {
            errorNotification(result);
        }
       
    }
    function updateQuery() {
        let leftDDL = document.getElementById("datasetColumnName");
        let rightDDL = document.getElementById("datasetColumnName_to");
        if (leftDDL.options.length == 0 && rightDDL.options.length != 0) { return; }
        if (dbValue?.toLowerCase() == 'mysql') {
            $("#datasetStoredQuery").val("Select " + (queryResult || '*') + " from `" + $('#datasetSchemaName').val() + "`.`" + $('#datasetTableName').val() + "`");
        } else if (dbValue?.toLowerCase() == 'oracle') {
            const columns = queryResult.length ? queryResult.split(',').map(column => `"${column.trim()}"`).join(', ') : '';
            $("#datasetStoredQuery").val('SELECT ' + (columns || '*') + ' FROM "' + $('#datasetSchemaName').val() + '"."' + $('#datasetTableName').val() + '"');
        } else { $("#datasetStoredQuery").val("Select " + (queryResult || '*') + " from [" + $('#datasetSchemaName').val() + "].[dbo].[" + $('#datasetTableName').val() + "]"); }
        $('#StoredQuery-error').text('').removeClass('field-validation-error')
    }
    function modifiedQuery(query) {
        let leftDDL = document.getElementById("datasetColumnName");
        let rightDDL = document.getElementById("datasetColumnName_to");
        let isSelectAll = /\bSELECT\s+\*\s+FROM\b/i.test(query);
        if (isSelectAll) {
            for (let i = leftDDL.options.length - 1; i >= 0; i--) {
                let option = leftDDL.options[i];
                let newOption = new Option(option.text, option.value);
                rightDDL.add(newOption);
                queryResult += queryResult ? ',' + newOption.text : newOption.text;
                leftDDL.remove(i);
            }
        } else {
            let match = query.match(/SELECT (.+?) FROM/i);
            let queryColumns = match ? match[1].split(',').map(col => col.trim()) : [];
            for (let i = leftDDL.options.length - 1; i >= 0; i--) {
                let option = leftDDL.options[i];
                if (queryColumns.map(col => col.replace(/"/g, '')).includes(option.text)) {                 
                    let existsInRightDDL = [...rightDDL.options].some(opt => opt.text === option.text);
                    if (!existsInRightDDL) {
                        let newOption = new Option(option.text, option.value);
                        rightDDL.add(newOption);
                        queryResult += queryResult ? ',' + newOption.text : newOption.text;
                    }
                    leftDDL.remove(i);
                }
            }
            for (let i = rightDDL.options.length - 1; i >= 0; i--) {
                let option = rightDDL.options[i];
                if(!queryColumns.map(col => col.replace(/"/g, '')).includes(option.text)) {             
                    let newOption = new Option(option.text, option.value);
                    leftDDL.add(newOption);
                    rightDDL.remove(i);
                }
            }
        }
        if ($('#datasetColumnName').children()?.length == 0) { updateQuery(); }
        checkColumnData();
    }

    async function RunQuery(query) {
        let queryForm = validateQuery(query);
        if (queryForm) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    method: "GET",
                    url: RootUrl + datasetURL.runQuery,
                    data: { runQuery: query },
                    dataType: "json",
                    success: function (response) {
                        if (response?.success && response?.data) {
                            $("#datasetWrapper").empty();
                            const tableData = $("#tableData");
                            const tableRow = $("#tablerow");
                            let tabData = typeof response?.data?.tableValue === "string" ? JSON.parse(response.data.tableValue) : response?.data?.tableValue;
                            if (tabData?.length == 0) {
                                let image = "<img src='../../img/isomatric/no_data_found.svg'  style='width: 385px;padding-top: 81px;' class='Card_NoData_Img'>";
                                $("#datasetWrapper").css('text-align', 'center').html(image);
                            } else {
                                $("#datasetWrapper").hide();
                                $("#datasetTable").show();
                                const properties = Object.keys(tabData[0]);
                                let headerRow = `<tr>${properties.map(prop => `<th>${prop}</th>`).join('')}</tr>`;
                                tableRow.append(headerRow);
                                tabData.forEach(row => {
                                    let dataRow = "<tr>" + properties.map(prop => {
                                        let value = row[prop] || '-';
                                        return `<td>${prop.includes("Date") ? value.replace("T", " ") : value}</td>`;
                                    }).join('') + "</tr>";
                                    tableData.append(dataRow);
                                });
                            }
                            resolve(true);
                        }
                        else {
                            $('#StoredQuery-error').text('Enter valid query').addClass('field-validation-error');
                            resolve(false);
                        }
                    }
                });
            });
        }
        else {           
            return false;
        }
    };

    async function GetDataSetByName(schemaName, tableName, elementId) {
        const data = { schemaName, tableName };
        const userRoleList = await getAysncWithHandler(RootUrl + datasetURL.getColumnNames, data);
        if (userRoleList?.length) {
            const $element = $("#" + elementId).empty();
            userRoleList.forEach(({ columnName }) => {
                $element.append(new Option(columnName, columnName));
            });
            if (columnEditValue.length) {
                $("#datasetColumnName").val(columnEditValue);
            }
        }
        setTimeout(() => {
            $("#datasetColumnName").multiselect();
        }, 200);
    }
    function clearPreviousData() {
        columnEditValue = [];
        queryResult = '';
        $('#datasetName, #datasetDescription, #datasetSchemaName, #datasetTableName, #datasetStoredQuery').val('');
        $('#datasetTableName, #datasetColumnName, #datasetColumnName_to, #tablerow, #tableData').empty();
        $('#btnDataSetSave').text('Save');
    }

    async function IsNameExist(url, name, data, errorFunc) {
        return !name.trim() ? true : (await getAysncWithHandler(url, data, errorFunc)) ? "Name already exists" : true;
    }
    function checkColumnData() {
        setTimeout(function () {
            $('#datasetLoader').addClass('d-none').hide();
            if ($("#datasetColumnName_to").children() && $("#datasetColumnName_to").children().length) {
                $("#btnRunQuery").show();
            } else {
                $("#btnRunQuery").hide();
            }
        }, 100);
    }
    function getAllQuery() {
        let schema = $('#datasetSchemaName').val(),
            db = dbValue?.toLowerCase(),
            tableName = $('#datasetTableName').val();

        if (tableName) $("#datasetStoredQuery").val(
            db === 'mysql' ? `Select * from \`${schema}\`.\`${tableName}\`` :
                db === 'oracle' ? `SELECT * FROM "${schema}"."${tableName}"` :
                    `Select * from [${schema}].[dbo].[${tableName}]`)
    };

    async function validateName(value, id = null, urls) {
        const errorElement = $('#DataSetName-error');
        if (!value) {
            errorElement.text('Enter dataset name').addClass('field-validation-error');
            return false;
        } else {
            if (value.includes('<')) {
                errorElement.text('Special characters not allowed').addClass('field-validation-error');
                return false;
            }
            const url = RootUrl + datasetURL.nameExistUrl;
            let data = {};
            data.dataSetName = value;
            data.id = id;
            const validationResults = [
                await SpecialCharValidateCustom(value),
                await ShouldNotBeginWithSpace(value),
                await ShouldNotBeginWithUnderScore(value),
                await OnlyNumericsValidate(value),
                await ShouldNotBeginWithNumber(value),
                await ShouldNotEndWithSpace(value),
                await ShouldNotAllowMultipleSpace(value),
                await SpaceWithUnderScore(value),
                await ShouldNotEndWithUnderScore(value),
                await MultiUnderScoreRegex(value),
                await SpaceAndUnderScoreRegex(value),
                await minMaxlength(value),
                await secondChar(value),
                await IsNameExist(url, data.dataSetName, data, OnError)
            ];
            return CommonValidation(errorElement, validationResults);
        }
    }

    async function datasetValidate(value, errorMessage, errorElementId) {
        let errorElement = $('#' + errorElementId);
        const isDescriptionError = errorElement.is('#Description-error');
        if ((isDescriptionError && value && (value?.length > 500 || value?.length < 3)) || (!isDescriptionError && !value)) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        }
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
    function validateQuery(query) {
        let queryPattern;
        switch (dbValue?.toLowerCase()) {
            case 'oracle':
                queryPattern = /^SELECT\s+.*\s+FROM\s+("[a-zA-Z0-9_.]+"|\b[a-zA-Z0-9_]+\b)(\s*\.\s*("[a-zA-Z0-9_]+"|\b[a-zA-Z0-9_]+\b))?\s*;?$/i;
                break;
            case 'mysql':
                queryPattern = /^SELECT\s+.*\s+FROM\s+(`?[a-zA-Z0-9_.]+`?)(\s*\.\s*(`?[a-zA-Z0-9_]+`?))?\s*;?$/i;
                break;
            case 'mssql':
                queryPattern = /^SELECT\s+.*\s+FROM\s+\[[a-zA-Z0-9_.]+\]\.\[dbo\]\.\[[a-zA-Z0-9_]+\]\s*;?$/i;
                break;
            default:
                $('#StoredQuery-error').text('Unsupported database type').addClass('field-validation-error');
                return false;
        }
        if (!query?.trim()) {
            $('#StoredQuery-error').text('Enter query').addClass('field-validation-error');
            return false;
        }
        else if (!queryPattern.test(query.trim())) {
            $('#StoredQuery-error').text('Enter valid query').addClass('field-validation-error');
            return false;
        }
        $('#StoredQuery-error').text('').removeClass('field-validation-error');
        return true;
    }

    document.getElementById('search-inp')?.addEventListener('keypress', preventEnterKey);

    $('#btnDataSetCreate').on('click', function () {
        const errorElements = ['#DataSetName-error', '#Description-error', '#selectschemaerror', '#selecttableerror', '#StoredQuery-error', '#PrimaryTableNameerror'];
        clearInputFields('CreateForm', errorElements);
        clearPreviousData()
        $('#datasetStoredQuery').css('height', '46px');
        $("#CreateModal").modal('show');
        checkColumnData();
    });

    $('#dataSetTabel').on('click', '.btnDataSetEdit', function () {
        const datasetData = $(this).data('dataset');
        dataSetEdit(datasetData);
        $('#btnDataSetSave').text('Update');
        $('#CreateModal').modal('show');
        checkColumnData();
    });

    $('#dataSetTabel').on('click', '.btnDataSetDelete', function () {
        let dataSetId = $(this).data('dataset-id');
        let dataSetName = $(this).data('dataset-name');
        $('#datasetDeleteId').val(dataSetId);
        $('#deleteData').text(dataSetName);
    });

    $('#datasetColumnName_rightSelected').on('click', function () {
        let leftDDL = document.getElementById("datasetColumnName");
        for (let i = 0; i < leftDDL.options.length; i++) {
            if (leftDDL?.options[i]?.selected) {
                let option = leftDDL?.options[i];
                if (queryResult.length === 0) queryResult += option?.text

                else queryResult += ',' + option?.text;
            }
        }
        setTimeout(function () {
            if ($('#datasetColumnName').children()?.length == 0) {
                getAllQuery()
            } else {
                if ($('#datasetColumnName').children()?.length || $('#datasetColumnName_to').children()?.length) updateQuery();
            }
        }, 100);
        checkColumnData();
    });

    $('#datasetColumnName_leftSelected').on('click', function () {
        let rightDDL = document.getElementById("datasetColumnName_to");
        for (let i = 0; i < rightDDL.options.length; i++) {
            let dataFound = queryResult?.split(',');
            if (rightDDL?.options[i]?.selected) {
                let option = rightDDL?.options[i];
                let particularDataIndex = dataFound.findIndex(dt => dt === option?.text)
                if (particularDataIndex !== -1) dataFound.splice(particularDataIndex, 1)
                if (dataFound.length) queryResult = dataFound.join(',')
                else queryResult = ''
            }
        }
        setTimeout(function () {
            if ($('#datasetColumnName_to').children()?.length == 0) {
                $("#datasetStoredQuery").val("");
            } else {
                if ($('#datasetColumnName').children()?.length || $('#datasetColumnName_to').children()?.length) updateQuery();
            }
        }, 100)
        checkColumnData();
    });

    $('#datasetColumnName_rightAll').on('click', function () {
        getAllQuery();
        let rightDDL = $("#datasetColumnName option");
        let newQueryResult = rightDDL.map((_, opt) => opt.text).get().join(',');
        queryResult = queryResult ? `${queryResult},${newQueryResult}` : newQueryResult;
        checkColumnData();
    });

    $('#datasetColumnName_leftAll').on('click', function () {
        queryResult = '';
        $("#datasetStoredQuery").val("");
        checkColumnData();
    });

    $('#btnRunQuery').on('click', async () => {
        $('#btnRunQuery,#datasetTableName,#datasetSchemaName').prop('disabled', true);
        $('#datasetLoader').removeClass('d-none').show();
        $("#tableData, #tablerow").empty();
        $("#datasetTable").hide();
        $("#datasetWrapper").show();
        $('#StoredQuery-error').text('').removeClass('field-validation-error');
        const result = await RunQuery($('#datasetStoredQuery').val());
        if (result) {
            $('#CreateModal').modal('hide');
            $('#datasetModal').modal('show');
            $('#datasetModal').off('shown.bs.modal').on('shown.bs.modal', function () {
                $(this).find('.modal-body > div[style*="overflow:auto"]').scrollTop(0);
            });
            $('#datasetLoader').addClass('d-none').hide();
            $('#btnRunQuery,#datasetTableName,#datasetSchemaName').prop('disabled', false);
        }
    });

    $("#confirmDeleteButton").on('click', commonDebounce(async function () {
        const datasetID = $('#datasetDeleteId').val();
        if (datasetID != "") {
            await $.ajax({
                type: "GET",
                url: RootUrl + datasetURL.datasetDelete,
                dataType: "json",
                traditional: true,
                data: {
                    id: datasetID,
                    __RequestVerificationToken: gettoken()
                },
                success: function (result) {
                    if (result?.success) {
                        notificationAlert("success", result?.data);
                        $('#DeleteModal').modal('hide');
                        setTimeout(() => dataTable.ajax.reload(), 200);
                    } else {
                        errorNotification(result)
                    }
                }
            })
        }
    }, 800));

    $("#btnDataSetSave").on('click', commonDebounce(async function () {
        const name = $("#datasetName").val();
        const storedQuery = $("#datasetStoredQuery").val();
        const description = $("#datasetDescription").val();
        const schemaName = $(".schemaName").val();
        const tableName = $("#datasetTableName").val();
        const id = $('#id').val();
        const tableId = $('#tableAccessId').val();

        const [isName, isSchemaName, isTableName, isQuery] = await Promise.all([
            validateName(name, id),
            datasetValidate(schemaName, 'Select schema name', 'selectschemaerror'),
            datasetValidate(tableName, 'Select table name', 'selecttableerror'),
            RunQuery(storedQuery)
        ]);
        if (isName && isSchemaName && isTableName && isQuery) {
            await $.ajax({
                url: RootUrl + datasetURL.datasetCreateOrUpdate,
                type: "POST",
                dataType: "json",
                data: {
                    DataSetName: name,
                    Description: description,
                    PrimaryTableName: tableName,
                    StoredQuery: storedQuery,
                    TableAccessId: tableId,
                    Id: id,
                    __RequestVerificationToken: gettoken()
                },
                success: function (result) {
                    if (result?.success) {
                        notificationAlert("success", result.data);
                        $('#CreateModal').modal('hide');
                        setTimeout(() => dataTable.ajax.reload(), 200);
                    } else {
                        errorNotification(result)
                    }
                }
            })
        }
    }, 800));

    $('#datasetName').on('input', commonDebounce(async function () {
        const value = $("#datasetName").val();
        let Id = $('#id').val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await validateName(value, Id);
    }, 500))

    $('#datasetDescription').on('input', async function () {
        let sanitizedValue = $(this).val().trimStart().replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        let errorElement = $('#Description-error');
        if (sanitizedValue) {
            const validationResults = [
                SpecialCharValidateCustom(sanitizedValue),
                minMaxlength(sanitizedValue, 500)
            ];
            return CommonValidation(errorElement, validationResults);
        } else {
            errorElement.text('').removeClass('field-validation-error')
        }
    });

    $('#datasetSchemaName').on('change', async function () {
        schemaName = $('#datasetSchemaName :selected').val();
        await datasetValidate(schemaName, 'Select schema name', 'selectschemaerror')
        GetDataSetBySchemaName(schemaName, 'datasetTableName')
    })

    $('#datasetTableName').on('change', async function () {
        let tableName = $(this).val(),
            tableId = $('#datasetTableName option:selected').attr('tableid'),
            schemaName = $('#datasetSchemaName :selected').val();
        $("#btnRunQuery").hide();
        await datasetValidate(tableName, 'Select table name', 'selecttableerror');
        queryResult = '';
        columnEditValue = [];
        $('#textPrimaryTableName').val(tableName);
        $('#tableAccessId').val(tableId);
        $('#datasetStoredQuery').val('');
        $("#tableData, #tablerow, #datasetColumnName, #datasetColumnName_to").empty();
        $('#StoredQuery-error').text('').removeClass('field-validation-error');
        if (schemaName) GetDataSetByName(schemaName, tableName, 'datasetColumnName');
    });

    $('#datasetStoredQuery').on('input', commonDebounce(async function () {
        const query = $("#datasetStoredQuery").val();
        await validateQuery(query);
        if ($('#datasetTableName').val()) { modifiedQuery(query); }
    }, 500))
});
