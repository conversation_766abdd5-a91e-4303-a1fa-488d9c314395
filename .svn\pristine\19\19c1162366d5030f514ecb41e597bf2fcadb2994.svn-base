﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetDrillAnalytics;

public class DrillAnalyticsDetailVm
{
    public int ConfiguredProfileCount { get; set; }
    public int ExecutedProfileCount { get; set; }
    public int ExecutedWorkflowCount { get; set; }
    public List<DrillAnalyticsDetailList> DrillAnalyticsDetailLists { get; set; }
}

public class DrillAnalyticsDetailList
{
    public int ProfileTotalCount { get; set; }
    public int ProfileSuccessCount { get; set; }
    public int ProfiledFailedCount { get; set; }
    public int WorkflowTotalCount { get; set; }
    public int WorkflowSuccessCount { get; set; }
    public int WorkflowFailedCount { get; set; }
    public int OutOfRtoCount { get; set; }
    public string DrillDate { get; set; }
}