using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SmtpConfigurationFixture : IDisposable
{
    public List<SmtpConfiguration> SmtpConfigurationPaginationList { get; set; }
    public List<SmtpConfiguration> SmtpConfigurationList { get; set; }
    public SmtpConfiguration SmtpConfigurationDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SmtpConfigurationFixture()
    {
        var fixture = new Fixture();

        SmtpConfigurationList = fixture.Create<List<SmtpConfiguration>>();

        SmtpConfigurationPaginationList = fixture.CreateMany<SmtpConfiguration>(20).ToList();

        SmtpConfigurationPaginationList.ForEach(x => x.CompanyId = CompanyId);

        SmtpConfigurationList.ForEach(x => x.CompanyId = CompanyId);

        SmtpConfigurationDto = fixture.Create<SmtpConfiguration>();

        SmtpConfigurationDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public SmtpConfiguration CreateSmtpConfiguration(
        string companyId = "COMPANY_123",
        string smtpHost = "smtp.default.com",
        string port = "587",
        string userName = "<EMAIL>",
        string password = "defaultpass",
        bool enableSSL = true,
        bool isBodyHTML = true,
        bool isPasswordLess = false,
        string maskFromAddress = "<EMAIL>",
        bool isMask = false,
        bool isActive = true,
        bool isDelete = false)
    {
        return new SmtpConfiguration
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = companyId,
            SmtpHost = smtpHost,
            Port = port,
            UserName = userName,
            Password = password,
            EnableSSL = enableSSL,
            IsBodyHTML = isBodyHTML,
            IsPasswordLess = isPasswordLess,
            MaskFromAddress = maskFromAddress,
            IsMask = isMask,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<SmtpConfiguration> CreateMultipleSmtpConfigurations(int count)
    {
        var configurations = new List<SmtpConfiguration>();
        for (int i = 1; i <= count; i++)
        {
            configurations.Add(CreateSmtpConfiguration(
                companyId: $"COMPANY_{i:D3}",
                smtpHost: $"smtp{i}.example.com",
                port: (587 + i).ToString(),
                userName: $"user{i}@example.com",
                password: $"pass{i}",
                enableSSL: i % 2 == 0,
                isBodyHTML: i % 3 == 0,
                isPasswordLess: i % 4 == 0,
                maskFromAddress: $"noreply{i}@example.com",
                isMask: i % 5 == 0
            ));
        }
        return configurations;
    }

    public SmtpConfiguration CreateSmtpConfigurationWithSpecificId(string referenceId, string smtpHost = "smtp.test.com")
    {
        return new SmtpConfiguration
        {
            ReferenceId = referenceId,
            CompanyId = "COMPANY_TEST",
            SmtpHost = smtpHost,
            Port = "587",
            UserName = "<EMAIL>",
            Password = "testpass",
            EnableSSL = true,
            IsBodyHTML = true,
            IsPasswordLess = false,
            MaskFromAddress = "<EMAIL>",
            IsMask = false,
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public SmtpConfiguration CreateSmtpConfigurationForCompany(string companyId, string smtpHost = null)
    {
        return CreateSmtpConfiguration(
            companyId: companyId,
            smtpHost: smtpHost ?? $"smtp.{companyId.ToLower()}.com",
            userName: $"admin@{companyId.ToLower()}.com",
            password: $"{companyId.ToLower()}pass"
        );
    }

    public List<SmtpConfiguration> CreateSmtpConfigurationsWithStatus(int activeCount, int inactiveCount)
    {
        var configurations = new List<SmtpConfiguration>();

        for (int i = 1; i <= activeCount; i++)
        {
            configurations.Add(CreateSmtpConfiguration(
                companyId: $"ACTIVE_COMPANY_{i}",
                smtpHost: $"smtp.active{i}.com",
                isActive: true
            ));
        }

        for (int i = 1; i <= inactiveCount; i++)
        {
            configurations.Add(CreateSmtpConfiguration(
                companyId: $"INACTIVE_COMPANY_{i}",
                smtpHost: $"smtp.inactive{i}.com",
                isActive: false
            ));
        }

        return configurations;
    }

    public SmtpConfiguration CreateGmailSmtpConfiguration(string companyId = "COMPANY_GMAIL")
    {
        return CreateSmtpConfiguration(
            companyId: companyId,
            smtpHost: "smtp.gmail.com",
            port: "587",
            userName: "<EMAIL>",
            password: "gmailpass",
            enableSSL: true,
            isBodyHTML: true,
            isPasswordLess: false
        );
    }

    public SmtpConfiguration CreateOutlookSmtpConfiguration(string companyId = "COMPANY_OUTLOOK")
    {
        return CreateSmtpConfiguration(
            companyId: companyId,
            smtpHost: "smtp-mail.outlook.com",
            port: "587",
            userName: "<EMAIL>",
            password: "outlookpass",
            enableSSL: true,
            isBodyHTML: true,
            isPasswordLess: false
        );
    }

    public SmtpConfiguration CreateYahooSmtpConfiguration(string companyId = "COMPANY_YAHOO")
    {
        return CreateSmtpConfiguration(
            companyId: companyId,
            smtpHost: "smtp.mail.yahoo.com",
            port: "587",
            userName: "<EMAIL>",
            password: "yahoopass",
            enableSSL: true,
            isBodyHTML: true,
            isPasswordLess: false
        );
    }

    public List<SmtpConfiguration> CreateSmtpConfigurationsForProviders()
    {
        return new List<SmtpConfiguration>
        {
            CreateGmailSmtpConfiguration(),
            CreateOutlookSmtpConfiguration(),
            CreateYahooSmtpConfiguration()
        };
    }

    public SmtpConfiguration CreateSmtpConfigurationWithSSL(bool enableSSL, string port = null)
    {
        return CreateSmtpConfiguration(
            enableSSL: enableSSL,
            port: port ?? (enableSSL ? "465" : "25")
        );
    }

    public SmtpConfiguration CreateSmtpConfigurationWithPasswordLess(bool isPasswordLess)
    {
        return CreateSmtpConfiguration(
            isPasswordLess: isPasswordLess,
            password: isPasswordLess ? null : "testpass"
        );
    }

    public SmtpConfiguration CreateSmtpConfigurationWithMask(bool isMask, string maskFromAddress = null)
    {
        return CreateSmtpConfiguration(
            isMask: isMask,
            maskFromAddress: maskFromAddress ?? (isMask ? "<EMAIL>" : null)
        );
    }

    public List<SmtpConfiguration> CreateSmtpConfigurationsForCompanies(List<string> companyIds)
    {
        var configurations = new List<SmtpConfiguration>();
        foreach (var companyId in companyIds)
        {
            configurations.Add(CreateSmtpConfigurationForCompany(companyId));
        }
        return configurations;
    }

    public SmtpConfiguration CreateMinimalSmtpConfiguration()
    {
        return new SmtpConfiguration
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "MINIMAL_COMPANY",
            SmtpHost = "smtp.minimal.com",
            Port = "25",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
