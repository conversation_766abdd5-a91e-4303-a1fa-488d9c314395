﻿.AI-Suggestion-List .list-group-item {
    border: 0px solid #ccc;
    align-items: end;
}

.AI-Suggestions-Option {
    display: flex;
    text-align: left;
}

    .AI-Suggestions-Option .AI-Profile {
        /*        margin-right: 6px;
        border-radius: 50%;*/
        width: 20px;
        height: 20px;
    }

    .AI-Suggestions-Option .AI-Suggestions-Bg {
        background-color: #fff;
        padding: 8px;
        border-radius: 8px 8px 8px 0px;
        word-wrap: break-word !important;
        word-break: break-word !important;
        max-width: 15rem;
        box-shadow: 0 1rem 3rem rgba(0,0,0,.1) !important;
    }

.CardList-Option {
    text-align: start;
    border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
    border-color: var(--bs-secondary-border-subtle) !important;
    border-radius: var(--bs-border-radius-pill) !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0.25rem !important;
}

.User-select-Option {
    display: flex;
    text-align: right;
    justify-content: end;
}

    .User-select-Option .User-Profile {
        margin-left: 6px;
        /*        border-radius: 50%;*/
        width: 28px;
        height: 28px;
    }

    .User-select-Option .User-Message-Bg {
        background-color: var(--bs-success);
        padding: 4px 8px;
        border-radius: 8px 8px 0px 8px;
        word-wrap: break-word !important;
        word-break: break-word !important;
        max-width: 15rem;
        color: var(--bs-white);
    }

.AI-Option .active {
    color: #fff;
}

    .AI-Option .active .text-primary {
        color: #fff !important;
    }

    .AI-Option .active .text-secondary {
        color: #fff !important;
    }