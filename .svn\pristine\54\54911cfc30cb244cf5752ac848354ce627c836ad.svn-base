﻿using ContinuityPatrol.Application.Features.VeritasCluster.Events.Update;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.VeritasCluster.Events
{
    public class UpdateVeritasClusterEventTests
    {
        private readonly Mock<ILogger<VeritasClusterUpdatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly VeritasClusterUpdatedEventHandler _handler;

        public UpdateVeritasClusterEventTests()
        {
            _mockLogger = new Mock<ILogger<VeritasClusterUpdatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new VeritasClusterUpdatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object
            );
        }

        [Fact]
        public async Task Handle_Creates_UserActivity_And_Logs_Info_Successfully()
        {
            var updatedEvent = new VeritasClusterUpdatedEvent
            {
                Name = "Cluster1"
            };

            _mockUserService.Setup(s => s.UserId).Returns("User123");
            _mockUserService.Setup(s => s.LoginName).Returns("johndoe");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://example.com");
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            _mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>())).ToString();

            await _handler.Handle(updatedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "User123" &&
                activity.LoginName == "johndoe" &&
                activity.RequestUrl == "http://example.com" &&
                activity.HostAddress == "***********" &&
                activity.Action == "Update VeritasCluster" &&
                activity.Entity == "VeritasCluster" &&
                activity.ActivityType == ActivityType.Update.ToString() &&
                activity.ActivityDetails == "VeritasCluster 'Cluster1' updated successfully."
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation(It.Is<string>(msg => msg.Contains("VeritasCluster 'Cluster1' updated successfully."))), Times.Once);
        }

        [Fact]
        public async Task Handle_Throws_If_AddAsync_Fails()
        {
            var updatedEvent = new VeritasClusterUpdatedEvent
            {
                Name = "Cluster1"
            };

            _mockUserService.Setup(s => s.UserId).Returns("User123");
            _mockUserService.Setup(s => s.LoginName).Returns("johndoe");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://example.com");
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            _mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .ThrowsAsync(new Exception("Database error"));

            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(updatedEvent, CancellationToken.None));
            Assert.Equal("Database error", exception.Message);

            _mockLogger.Verify(logger => logger.LogInformation(It.Is<string>(msg => msg.Contains("VeritasCluster 'Cluster1' updated successfully."))), Times.Once);
        }
    }
}
