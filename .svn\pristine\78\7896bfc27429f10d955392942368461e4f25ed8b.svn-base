using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetListByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Create;
using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Update;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessServiceDrReadyDetails;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessServiceIdByCount;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDrReadinessByBusinessServices;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DRReadyStatusModel;
using ContinuityPatrol.Domain.ViewModels.GetBusinessServiceIdByCount;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetListByBusinessServiceId;
using static ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDrReadinessByBusinessServices.GetDrReadinessByBusinessServiceVm;
using GetBusinessFunctionListByBusinessServiceIdQuery = ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessFunctionListByBusinessServiceId.GetBusinessFunctionListByBusinessServiceIdQuery;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessFunctionListByBusinessServiceId;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DrReadyStatusFixture : IDisposable
{
    public CreateDRReadyStatusCommand CreateDRReadyStatusCommand { get; }
    public CreateDRReadyStatusResponse CreateDRReadyStatusResponse { get; }
    public UpdateDRReadyStatusCommand UpdateDRReadyStatusCommand { get; }
    public UpdateDRReadyStatusResponse UpdateDRReadyStatusResponse { get; }
    public DeleteDRReadyStatusCommand DeleteDRReadyStatusCommand { get; }
    public DeleteDRReadyStatusResponse DeleteDRReadyStatusResponse { get; }
    public GetDRReadyStatusDetailQuery GetDRReadyStatusDetailQuery { get; }
    public DRReadyStatusDetailVm DRReadyStatusDetailVm { get; }
    public GetDRReadyStatusListQuery GetDRReadyStatusListQuery { get; }
    public List<DRReadyStatusListVm> DRReadyStatusListVm { get; }
    public GetDRReadyStatusPaginatedListQuery GetDRReadyStatusPaginatedListQuery { get; }
    public PaginatedResult<DRReadyStatusListVm> DRReadyStatusPaginatedResult { get; }
    public GetDRReadyStatusByBusinessServiceIdQuery GetDRReadyStatusByBusinessServiceIdQuery { get; }
    public DRReadyStatusByBusinessServiceIdVm DRReadyStatusByBusinessServiceIdVm { get; }
    public GetBusinessServiceDrReadyDetailQuery GetBusinessServiceDrReadyDetailQuery { get; }
    public List<BusinessServiceDrReadyDetailVm> BusinessServiceDrReadyDetailVm { get; }
    public GetDrReadinessByBusinessServiceQuery GetDrReadinessByBusinessServiceQuery { get; }
    public GetDrReadinessByBusinessServiceVm GetDrReadinessByBusinessServiceVm { get; }
    public GetBusinessFunctionListByBusinessServiceIdQuery GetBusinessFunctionListByBusinessServiceIdQuery { get; }
    public List<GetBusinessFunctionListByBusinessServiceIdVm> BusinessFunctionListByBusinessServiceIdVm { get; }
    public GetBusinessServiceIdByCountQuery GetBusinessServiceIdByCountQuery { get; }
    public BusinessServiceIdByCountVm BusinessServiceIdByCountVm { get; }

    public DrReadyStatusFixture()
    {
        var fixture = new Fixture();

        // Configure fixture for enterprise DR Ready Status scenarios
        fixture.Customize<CreateDRReadyStatusCommand>(c => c
            .With(x => x.UserId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise Critical Status Service")
            .With(x => x.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(x => x.BusinessFunctionName, "Enterprise Critical Status Function")
            .With(x => x.IsProtected, "Yes")
            .With(x => x.AffectedInfra, "8")
            .With(x => x.ActiveInfra, "200")
            .With(x => x.WorkflowId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise Status Workflow")
            .With(x => x.WorkflowStatus, "Active")
            .With(x => x.FailedActionName, "")
            .With(x => x.FailedActionId, "")
            .With(x => x.ActiveBusinessFunction, "40")
            .With(x => x.AffectedBusinessFunction, "3")
            .With(x => x.DRReady, "Yes")
            .With(x => x.NotReady, "No")
            .With(x => x.WorkflowAttach, "Enterprise_Status_Plan.pdf")
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, "Enterprise Status Server")
            .With(x => x.ComponentName, "Status Monitor")
            .With(x => x.Type, "DR_STATUS")
            .With(x => x.ErrorMessage, ""));

        fixture.Customize<CreateDRReadyStatusResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise DR Ready Status created successfully!")
            .With(x => x.Success, true));

        fixture.Customize<UpdateDRReadyStatusCommand>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.UserId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise Updated Status Service")
            .With(x => x.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(x => x.BusinessFunctionName, "Enterprise Updated Status Function")
            .With(x => x.IsProtected, "Yes")
            .With(x => x.AffectedInfra, "5")
            .With(x => x.ActiveInfra, "250")
            .With(x => x.WorkflowId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise Updated Status Workflow")
            .With(x => x.WorkflowStatus, "Updated")
            .With(x => x.FailedActionName, "")
            .With(x => x.FailedActionId, "")
            .With(x => x.ActiveBusinessFunction, "45")
            .With(x => x.AffectedBusinessFunction, "2")
            .With(x => x.DRReady, "Yes")
            .With(x => x.NotReady, "No")
            .With(x => x.WorkflowAttach, "Enterprise_Updated_Status_Plan.pdf")
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, "Enterprise Updated Status Server")
            .With(x => x.ComponentName, "Updated Status Monitor")
            .With(x => x.Type, "DR_STATUS_UPDATE")
            .With(x => x.ErrorMessage, ""));

        fixture.Customize<UpdateDRReadyStatusResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise DR Ready Status updated successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DeleteDRReadyStatusResponse>(c => c
            .With(x => x.Message, "Enterprise DR Ready Status deleted successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DRReadyStatusDetailVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.UserId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise Detail Status Service")
            .With(x => x.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(x => x.BusinessFunctionName, "Enterprise Detail Status Function")
            .With(x => x.IsProtected, "Yes")
            .With(x => x.AffectedInfra, "10")
            .With(x => x.ActiveInfra, "300")
            .With(x => x.WorkflowId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise Detail Status Workflow")
            .With(x => x.WorkflowStatus, "Active")
            .With(x => x.DRReady, "Yes")
            .With(x => x.NotReady, "No"));

        fixture.Customize<DRReadyStatusListVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.UserId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise List Status Service")
            .With(x => x.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(x => x.BusinessFunctionName, "Enterprise List Status Function")
            .With(x => x.IsProtected, "Yes")
            .With(x => x.WorkflowName, "Enterprise List Status Workflow")
            .With(x => x.WorkflowStatus, "Active")
            .With(x => x.DRReady, "Yes"));

        fixture.Customize<DRReadyStatusByBusinessServiceIdVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.UserId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise Status by Business Service")
            .With(x => x.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(x => x.BusinessFunctionName, "Enterprise Status Business Function")
            .With(x => x.IsProtected, "Yes")
            .With(x => x.AffectedInfra, "5")
            .With(x => x.ActiveInfra, "100")
            .With(x => x.WorkflowId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, "Enterprise Business Service Status Workflow")
            .With(x => x.WorkflowStatus, "Active")
            .With(x => x.FailedActionName, "")
            .With(x => x.FailedActionId, "")
            .With(x => x.ActiveBusinessFunction, "20")
            .With(x => x.AffectedBusinessFunction, "2")
            .With(x => x.DRReady, "Yes")
            .With(x => x.NotReady, "No")
            .With(x => x.WorkflowAttach, "Enterprise_Status_Workflow.pdf")
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, "Enterprise Status Infrastructure")
            .With(x => x.ComponentName, "Status Component")
            .With(x => x.Type, "DR_STATUS")
            .With(x => x.ErrorMessage, ""));

        fixture.Customize<BusinessServiceDrReadyDetailVm>(c => c
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise Business Service Detail")
            .With(x => x.DRReady, "Yes"));

        fixture.Customize<GetDrReadinessByBusinessServiceVm>(c => c
            .With(x => x.TotalBusinessServiceCount, 50)
            .With(x => x.TotalDrReadyInfraObjectCount, 380)
            .With(x => x.RelatedServiceCount, 25)
            .With(x => x.TotalOrchestrationCount, 15));

        fixture.Customize<Components>(c => c
            .With(x => x.ServerDownCount, 5)
            .With(x => x.DatabaseDownCount, 2)
            .With(x => x.DataLagDownCount, 3)
            .With(x => x.WorkflowNotConfiguredCount, 1));

        fixture.Customize<Orchestration>(c => c
            .With(x => x.TotalDrReadyCount, 15)
            .With(x => x.ExecutedErrorCount, 2));

        fixture.Customize<RelatedService>(c => c
            .With(x => x.RelatedServiceErrorCount, 3)
            .With(x => x.RelatedServiceNotReadyCount, 5));

        fixture.Customize<GetBusinessFunctionListByBusinessServiceIdVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(x => x.BusinessFunctionName, "Enterprise Function List")
            .With(x => x.ActiveBusinessFunction, "20")
            .With(x => x.AffectedBusinessFunction, "2")
            .With(x => x.AffectedInfra, "5")
            .With(x => x.ActiveInfra, "100"));

        fixture.Customize<BusinessServiceIdByCountVm>(c => c
            .With(x => x.ActiveInfraTotalCount, 100)
            .With(x => x.ActiveBusinessFunctionTotalCount, 50)
            .With(x => x.TrueActiveInfra, 85)
            .With(x => x.FalseActiveInfra, 15)
            .With(x => x.ActiveInfra, 85)
            .With(x => x.AffectedInfra, 15)
            .With(x => x.ActiveBusinessFunction, 45)
            .With(x => x.AffectedBusinessFunction, 5)
            .With(x => x.DRReady, 80)
            .With(x => x.NotReady, 20));

        fixture.Customize<GetDRReadyStatusPaginatedListQuery>(c => c
            .With(x => x.SearchString, "Enterprise Status")
            .With(x => x.PageNumber, 1)
            .With(x => x.PageSize, 10)
            .With(x => x.SortColumn, "BusinessServiceName")
            .With(x => x.SortOrder, "ASC"));

        // Create instances
        CreateDRReadyStatusCommand = fixture.Create<CreateDRReadyStatusCommand>();
        CreateDRReadyStatusResponse = fixture.Create<CreateDRReadyStatusResponse>();
        
        UpdateDRReadyStatusCommand = fixture.Create<UpdateDRReadyStatusCommand>();
        UpdateDRReadyStatusResponse = fixture.Create<UpdateDRReadyStatusResponse>();
        
        DeleteDRReadyStatusCommand = new DeleteDRReadyStatusCommand { Id = Guid.NewGuid().ToString() };
        DeleteDRReadyStatusResponse = fixture.Create<DeleteDRReadyStatusResponse>();
        
        GetDRReadyStatusDetailQuery = new GetDRReadyStatusDetailQuery { Id = Guid.NewGuid().ToString() };
        DRReadyStatusDetailVm = fixture.Create<DRReadyStatusDetailVm>();
        
        GetDRReadyStatusListQuery = new GetDRReadyStatusListQuery();
        DRReadyStatusListVm = fixture.CreateMany<DRReadyStatusListVm>(6).ToList();
        
        GetDRReadyStatusPaginatedListQuery = fixture.Create<GetDRReadyStatusPaginatedListQuery>();
        DRReadyStatusPaginatedResult = new PaginatedResult<DRReadyStatusListVm>
        {
            Data = fixture.CreateMany<DRReadyStatusListVm>(10).ToList(),
            TotalCount = 10,
            PageSize = 10,
            Succeeded = true
        };
        
        GetDRReadyStatusByBusinessServiceIdQuery = new GetDRReadyStatusByBusinessServiceIdQuery { BusinessServiceId = Guid.NewGuid().ToString() };
        DRReadyStatusByBusinessServiceIdVm = fixture.Create<DRReadyStatusByBusinessServiceIdVm>();
        
        GetBusinessServiceDrReadyDetailQuery = new GetBusinessServiceDrReadyDetailQuery { BusinessServiceId = Guid.NewGuid().ToString() };
        BusinessServiceDrReadyDetailVm = fixture.CreateMany<BusinessServiceDrReadyDetailVm>(4).ToList();
        
        GetDrReadinessByBusinessServiceQuery = new GetDrReadinessByBusinessServiceQuery { BusinessServiceId = Guid.NewGuid().ToString() };
        GetDrReadinessByBusinessServiceVm = fixture.Create<GetDrReadinessByBusinessServiceVm>();
        
        GetBusinessFunctionListByBusinessServiceIdQuery = new GetBusinessFunctionListByBusinessServiceIdQuery { BusinessServiceId = Guid.NewGuid().ToString() };
        BusinessFunctionListByBusinessServiceIdVm = fixture.CreateMany<GetBusinessFunctionListByBusinessServiceIdVm>(8).ToList();
        
        GetBusinessServiceIdByCountQuery = new GetBusinessServiceIdByCountQuery { BusinessServiceId = Guid.NewGuid().ToString() };
        BusinessServiceIdByCountVm = fixture.Create<BusinessServiceIdByCountVm>();
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
