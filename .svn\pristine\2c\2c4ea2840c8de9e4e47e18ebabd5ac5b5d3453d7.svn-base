﻿namespace ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetByType;

public class GetMYSQLMonitorStatusDetailByTypeQueryHandler : IRequestHandler<GetMYSQLMonitorStatusDetailByTypeQuery,
    List<MYSQLMonitorStatusDetailByTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly IMysqlMonitorStatusRepository _mysqlMonitorStatusRepository;

    public GetMYSQLMonitorStatusDetailByTypeQueryHandler(IMysqlMonitorStatusRepository mysqlMonitorStatusRepository,
        IMapper mapper)
    {
        _mysqlMonitorStatusRepository = mysqlMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<List<MYSQLMonitorStatusDetailByTypeVm>> Handle(GetMYSQLMonitorStatusDetailByTypeQuery request,
        CancellationToken cancellationToken)
    {
        var mysqlMonitorStatus = await _mysqlMonitorStatusRepository.GetDetailByType(request.Type);

        return mysqlMonitorStatus.Count <= 0
            ? new List<MYSQLMonitorStatusDetailByTypeVm>()
            : _mapper.Map<List<MYSQLMonitorStatusDetailByTypeVm>>(mysqlMonitorStatus);
    }
}