﻿namespace ContinuityPatrol.Application.Features.ServerType.Commands.Create;

public class CreateServerTypeCommandValidator : AbstractValidator<CreateServerTypeCommand>
{
    private readonly IServerTypeRepository _serverTypeRepository;

    public CreateServerTypeCommandValidator(IServerTypeRepository serverTypeRepository)
    {
        _serverTypeRepository = serverTypeRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 30).WithMessage("{PropertyName} should contain between 3 to 30 characters.")
            .NotNull();

        RuleFor(p => p)
            .MustAsync(IsServerTypeNameUnique)
            .WithMessage("A same name already exists");
    }

    public async Task<bool> IsServerTypeNameUnique(CreateServerTypeCommand createServerTypeCommand,
        CancellationToken cancellationToken)
    {
        return !await _serverTypeRepository.IsServerTypeNameUnique(createServerTypeCommand.Name);
    }
}