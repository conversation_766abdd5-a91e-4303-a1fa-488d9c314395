﻿using ContinuityPatrol.Application.Features.UserRole.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.UserRole.Commands;

public class UpdateUserRoleTests : IClassFixture<UserRoleFixture>
{
    private readonly UserRoleFixture _userRoleFixture;

    private readonly Mock<IUserRoleRepository> _mockUserRoleRepository;

    private readonly UpdateUserRoleCommandHandler _handler;

    public UpdateUserRoleTests(UserRoleFixture userRoleFixture)
    {
        _userRoleFixture = userRoleFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockUserRoleRepository = UserRoleRepositoryMocks.UpdateUserRoleRepository(_userRoleFixture.UserRoles);

        _handler = new UpdateUserRoleCommandHandler(_userRoleFixture.Mapper, _mockUserRoleRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidSite_UpdateReferenceIdAsync_ToUserRolesRepo()
    {
        _userRoleFixture.UpdateUserRoleCommand.Id = _userRoleFixture.UserRoles[0].ReferenceId;

        var result = await _handler.Handle(_userRoleFixture.UpdateUserRoleCommand, CancellationToken.None);

        var userRole = await _mockUserRoleRepository.Object.GetUserRoleById(result.Id);

        Assert.Equal(_userRoleFixture.UpdateUserRoleCommand.Role, userRole.Role);
    }

    [Fact]
    public async Task Handle_Return_ValidUserRoleResponse_WhenUpdate_UserRole()
    {
        _userRoleFixture.UpdateUserRoleCommand.Id = _userRoleFixture.UserRoles[0].ReferenceId;

        var result = await _handler.Handle(_userRoleFixture.UpdateUserRoleCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateUserRoleResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_userRoleFixture.UpdateUserRoleCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidUserRoleId()
    {
        _userRoleFixture.UpdateUserRoleCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_userRoleFixture.UpdateUserRoleCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        _userRoleFixture.UpdateUserRoleCommand.Id = _userRoleFixture.UserRoles[0].ReferenceId;

        await _handler.Handle(_userRoleFixture.UpdateUserRoleCommand, CancellationToken.None);

        _mockUserRoleRepository.Verify(x => x.GetUserRoleById(It.IsAny<string>()), Times.Once);

        _mockUserRoleRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.UserRole>()), Times.Once);
    }
}