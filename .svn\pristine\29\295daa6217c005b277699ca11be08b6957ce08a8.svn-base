﻿using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Validators;

public class CreateWorkflowActionValidatorTests
{
    private readonly Mock<IWorkflowActionRepository> _mockWorkflowActionRepository;

    public List<Domain.Entities.WorkflowAction> WorkflowActions { get; set; }

    public CreateWorkflowActionValidatorTests()
    {
        WorkflowActions = new Fixture().Create<List<Domain.Entities.WorkflowAction>>();

        _mockWorkflowActionRepository = WorkflowActionRepositoryMocks.CreateWorkflowActionRepository(WorkflowActions);
    }

    //FormTypeName

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_ActionName_InWorkflowAction_WithEmpty(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_ActionName_InWorkflowAction_IsNull(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = null;

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameNotNullRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_ActionName_InWorkflowAction_MinimumRange(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "AG";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_ActionName_InWorkflowAction_MaximumRange(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "ABCDEFGHIJKLNMOPQRSTUVWXYZ_ASDFGFDLKJHJASDF_ABCDEFGHIJKLNMOPQRSTUVWXYZ_ASDFGFDLKJHJASDF_ABCDEFGHIJKLNMOPQRSTUVWXYZ_ASDFGFDLKJHJASDF_ABCDEFGHIJKLNMOPQRSTUVWXYZ_ASDFGFDLKJHJASDF_ABCDEFGHIJKLNMOPQRSTUVWXYZ_ASDFGFDLKJHJASDF_ABCDEFGHIJKLNMOPQRSTUVWXYZ_ASDFGFDLKJHJASDF_ABCDEFGHIJKLNMOPQRSTUVWXYZ_ASDFGFDLKJHJASDF_ABCDEFGHIJKLNMOPQRSTUVWXYZ_ASDFGFDLKJHJASDF";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "  TCS Chennai  ";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction_With_SingleSpace_InFront(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = " TCS Chennai";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction_With_SingleSpace_InBack(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "TCS Chennai ";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction_With_TripleSpace_InBetween(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "TCS   Chennai";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction_With_SpecialCharacters_InFront(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "&^^%%$TCS Chennai";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction_With_SpecialCharacters_InBack(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "TCS Chennai&%%$$#";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction_With_SpecialCharacters_InBetween(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "TCS*&*&^^Chennai";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction_With_SpecialCharacters_Only(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "^%&^*&*&^^";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction_With_UnderScore_InFront(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "_TCS Chennai";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction_With_UnderScore_InBack(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "TCS Chennai_";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction_With_UnderScoreAndNumbers_InFront(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "_764TCS Chennai";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction_With_UnderScore_InFront_AndNumbers_InBack(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "_TCS Chennai877";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction_With_Numbers_InFront(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "364TCS Chennai";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_Create_Valid_ActionName_InWorkflowAction_With_Numbers_Only(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.ActionName = "864563586541";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowAction.WorkflowActionNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }


    //Properties

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_CreateWorkflowActionCommandValidator_Properties_WithEmpty(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.Properties = "";
        createWorkflowActionCommand.ActionName = "VerifyClusterState";
        createWorkflowActionCommand.Type = "Operation";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);

        Assert.Contains(validateResult.Errors, e => e.PropertyName == "Properties" && e.ErrorMessage == "Please Enter the Properties.");
    }

    [Theory]
    [AutoWorkflowActionData]
    public async Task Verify_CreateWorkflowActionCommandValidator_Properties_IsNull(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        var validator = new CreateWorkflowActionCommandValidator(_mockWorkflowActionRepository.Object);

        createWorkflowActionCommand.Properties = null;
        createWorkflowActionCommand.ActionName = "VerifyClusterStatus";
        createWorkflowActionCommand.Type = "DR_Operation";

        var validateResult = await validator.ValidateAsync(createWorkflowActionCommand, CancellationToken.None);

        Assert.Contains(validateResult.Errors, e => e.PropertyName == "Properties" && e.ErrorMessage == "Please Enter the Properties.");
    }
}