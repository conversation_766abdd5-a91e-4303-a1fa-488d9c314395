using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.BackUpLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.BackUpLog.Queries.GetPaginatedList;

public class
    GetBackUpLogPaginatedListQueryHandler : IRequestHandler<GetBackUpLogPaginatedListQuery,
        PaginatedResult<BackUpLogListVm>>
{
    private readonly IBackUpLogRepository _backUpLogRepository;
    private readonly IMapper _mapper;

    public GetBackUpLogPaginatedListQueryHandler(IMapper mapper, IBackUpLogRepository backUpLogRepository)
    {
        _mapper = mapper;
        _backUpLogRepository = backUpLogRepository;
    }

    public async Task<PaginatedResult<BackUpLogListVm>> Handle(GetBackUpLogPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new BackUpLogFilterSpecification(request.SearchString);

        var queryable =await _backUpLogRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var backUpLogList = _mapper.Map<PaginatedResult<BackUpLogListVm>>(queryable);
        
        return backUpLogList;
        //var queryable = _backUpLogRepository.GetPaginatedQuery();

        //var productFilterSpec = new BackUpLogFilterSpecification(request.SearchString);

        //var backUpLogList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<BackUpLogListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        ////await _publisher.Publish(new BackUpLogPaginatedEvent(), cancellationToken);

        //return backUpLogList;
    }
}