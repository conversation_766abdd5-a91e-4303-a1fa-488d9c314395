using ContinuityPatrol.Application.Features.BulkImportOperation.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperation.Events;

public class DeleteBulkImportOperationEventTests : IClassFixture<BulkImportOperationFixture>, IClassFixture<UserActivityFixture>
{
    private readonly BulkImportOperationFixture _bulkImportOperationFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly BulkImportOperationDeletedEventHandler _handler;

    public DeleteBulkImportOperationEventTests(BulkImportOperationFixture bulkImportOperationFixture, UserActivityFixture userActivityFixture)
    {
        _bulkImportOperationFixture = bulkImportOperationFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");
        mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/bulkimportoperation");
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        var mockBulkImportOperationEventLogger = new Mock<ILogger<BulkImportOperationDeletedEventHandler>>();

        _mockUserActivityRepository = BulkImportOperationRepositoryMocks.CreateBulkImportOperationEventRepository(_userActivityFixture.UserActivities);

        _handler = new BulkImportOperationDeletedEventHandler(
            mockLoggedInUserService.Object, 
            mockBulkImportOperationEventLogger.Object, 
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteBulkImportOperationEventCreated()
    {
        // Arrange
        _userActivityFixture.UserActivities[0].LoginName = "Tester";
        var bulkImportOperationDeletedEvent = new BulkImportOperationDeletedEvent { Name = "TestUser" };

        // Act
        var result = _handler.Handle(bulkImportOperationDeletedEvent, CancellationToken.None);

        // Assert
        Assert.True(result.IsCompleted);
        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var bulkImportOperationDeletedEvent = new BulkImportOperationDeletedEvent { Name = "TestUser" };

        // Act
        await _handler.Handle(bulkImportOperationDeletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_WithCorrectProperties()
    {
        // Arrange
        var bulkImportOperationDeletedEvent = new BulkImportOperationDeletedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
       
    }

    [Fact]
    public async Task Handle_LogCorrectInformation_When_BulkImportOperationDeleted()
    {
        // Arrange
        var bulkImportOperationDeletedEvent = new BulkImportOperationDeletedEvent { Name = "TestUser" };
        var mockLogger = new Mock<ILogger<BulkImportOperationDeletedEventHandler>>();

        var handler = new BulkImportOperationDeletedEventHandler(
            new Mock<ILoggedInUserService>().Object,
            mockLogger.Object,
            _mockUserActivityRepository.Object);

        // Act
        await handler.Handle(bulkImportOperationDeletedEvent, CancellationToken.None);

        // Assert
        //mockLogger.Verify(
        //    x => x.Log(
        //        LogLevel.Information,
        //        It.IsAny<EventId>(),
        //        It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("TestUser") && v.ToString().Contains("deleted successfully")),
        //        It.IsAny<Exception>(),
        //        It.IsAny<Func<It.IsAnyType, Exception, string>>()),
        //    Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectUserActivityProperties_When_UserServiceHasData()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var testUrl = "/api/test";
        var testIpAddress = "***********";
        var testLoginName = "TestUser";

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);
        mockLoggedInUserService.Setup(x => x.LoginName).Returns(testLoginName);
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testUrl);
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testIpAddress);

        var handler = new BulkImportOperationDeletedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationDeletedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationDeletedEvent = new BulkImportOperationDeletedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.UserId.ShouldBe(testUserId);
        capturedUserActivity.LoginName.ShouldBe(testLoginName);
        capturedUserActivity.RequestUrl.ShouldBe(testUrl);
        capturedUserActivity.HostAddress.ShouldBe(testIpAddress);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_EventHandled()
    {
        // Arrange
        var bulkImportOperationDeletedEvent = new BulkImportOperationDeletedEvent { Name = "ProductionUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.Action.ShouldBe("Delete BulkImportOperation");
        capturedUserActivity.Entity.ShouldBe("BulkImportOperation");
        capturedUserActivity.ActivityType.ShouldBe("Delete");
        capturedUserActivity.ActivityDetails.ShouldBe("BulkImportOperation 'ProductionUser' deleted successfully.");
    }

    [Fact]
    public async Task Handle_NotSetCreatedByAndLastModifiedBy_When_DeleteEvent()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);

        var handler = new BulkImportOperationDeletedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationDeletedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationDeletedEvent = new BulkImportOperationDeletedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        // Delete events don't set CreatedBy and LastModifiedBy unlike Create events
        capturedUserActivity.CreatedBy.ShouldBeNull();
        capturedUserActivity.LastModifiedBy.ShouldBeNull();
    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_EventProcessed()
    {
        // Arrange
        var bulkImportOperationDeletedEvent = new BulkImportOperationDeletedEvent { Name = null };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldContain("deleted successfully");
    }

    [Fact]
    public async Task Handle_SetCorrectRequestUrl_When_UserServiceProvided()
    {
        // Arrange
        var testRequestUrl = "/api/bulkimportoperation/delete";
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testRequestUrl);

        var handler = new BulkImportOperationDeletedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationDeletedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationDeletedEvent = new BulkImportOperationDeletedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.RequestUrl.ShouldBe(testRequestUrl);
    }

    [Fact]
    public async Task Handle_SetCorrectHostAddress_When_UserServiceProvided()
    {
        // Arrange
        var testHostAddress = "********";
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testHostAddress);

        var handler = new BulkImportOperationDeletedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationDeletedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationDeletedEvent = new BulkImportOperationDeletedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.HostAddress.ShouldBe(testHostAddress);
    }

    [Fact]
    public async Task Handle_UseUserNameAsEventName_When_BulkImportOperationDeleted()
    {
        // Arrange
        var bulkImportOperationDeletedEvent = new BulkImportOperationDeletedEvent { Name = "AdminUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldContain("AdminUser");
        capturedUserActivity.ActivityDetails.ShouldBe("BulkImportOperation 'AdminUser' deleted successfully.");
    }
}
