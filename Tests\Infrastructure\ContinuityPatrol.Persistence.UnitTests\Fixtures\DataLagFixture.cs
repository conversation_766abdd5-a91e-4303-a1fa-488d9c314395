using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DataLagFixture : IDisposable
{
    public List<DataLag> DataLagPaginationList { get; set; }
    public List<DataLag> DataLagList { get; set; }
    public DataLag DataLagDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "BS_123";

    public ApplicationDbContext DbContext { get; private set; }

    public DataLagFixture()
    {
        var fixture = new Fixture();

        DataLagList = fixture.Create<List<DataLag>>();

        DataLagPaginationList = fixture.CreateMany<DataLag>(20).ToList();

        DataLagPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DataLagPaginationList.ForEach(x => x.IsActive = true);
        DataLagPaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);

        DataLagList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DataLagList.ForEach(x => x.IsActive = true);
        DataLagList.ForEach(x => x.BusinessServiceId = BusinessServiceId);

        DataLagDto = fixture.Create<DataLag>();
        DataLagDto.ReferenceId = Guid.NewGuid().ToString();
        DataLagDto.IsActive = true;
        DataLagDto.BusinessServiceId = BusinessServiceId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
