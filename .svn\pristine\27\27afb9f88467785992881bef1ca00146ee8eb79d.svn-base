﻿namespace ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetDetail;

public class GetGlobalSettingDetailQueryHandler : IRequestHandler<GetGlobalSettingDetailQuery, GlobalSettingDetailVm>
{
    private readonly IGlobalSettingRepository _globalSettingRepository;
    private readonly IMapper _mapper;

    public GetGlobalSettingDetailQueryHandler(IMapper mapper, IGlobalSettingRepository globalSettingRepository)
    {
        _mapper = mapper;
        _globalSettingRepository = globalSettingRepository;
    }

    public async Task<GlobalSettingDetailVm> Handle(GetGlobalSettingDetailQuery request,
        CancellationToken cancellationToken)
    {
        var globalSetting = await _globalSettingRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(globalSetting, nameof(Domain.Entities.GlobalSetting),
            new NotFoundException(nameof(Domain.Entities.GlobalSetting), request.Id));

        var globalSettingDetailDto = _mapper.Map<GlobalSettingDetailVm>(globalSetting);

        return globalSettingDetailDto;
    }
}