﻿using ContinuityPatrol.Application.Features.Incident.Events.Delete;

namespace ContinuityPatrol.Application.Features.Incident.Commands.Delete;

public class DeleteIncidentCommandHandler : IRequestHandler<DeleteIncidentCommand, DeleteIncidentCommandResponse>
{
    private readonly IIncidentRepository _incidentRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public DeleteIncidentCommandHandler(IIncidentRepository incidentRepository, IMapper mapper, IPublisher publisher)
    {
        _mapper = mapper;
        _incidentRepository = incidentRepository;
        _publisher = publisher;
    }

    public async Task<DeleteIncidentCommandResponse> Handle(DeleteIncidentCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _incidentRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.Incident),
            new NotFoundException(nameof(Domain.Entities.Incident), request.Id));

        eventToDelete.IsActive = false;

        await _incidentRepository.UpdateAsync(eventToDelete);

        var response = new DeleteIncidentCommandResponse
        {
            Message = Message.Delete(nameof(Incident), eventToDelete.IncidentName),

            IsActive = eventToDelete.IsActive
        };
        await _publisher.Publish(new IncidentDeletedEvent { IncidentName = eventToDelete.IncidentName },
            cancellationToken);

        return response;
    }
}