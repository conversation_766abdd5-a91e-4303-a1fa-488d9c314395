﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.JobModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.Job.Queries.GetPaginatedList;

public class GetJobPaginatedListQueryHandler : IRequestHandler<GetJobPaginatedListQuery, PaginatedResult<JobListVm>>
{
    private readonly IJobRepository _jobRepository;
    private readonly IMapper _mapper;

    public GetJobPaginatedListQueryHandler(IMapper mapper, IJobRepository jobRepository)
    {
        _mapper = mapper;
        _jobRepository = jobRepository;
    }

    public async Task<PaginatedResult<JobListVm>> Handle(GetJobPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new JobFilterSpecification(request.SearchString);

        var queryable =await _jobRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var jobList = _mapper.Map<PaginatedResult<JobListVm>>(queryable);
        return jobList;
    }
}