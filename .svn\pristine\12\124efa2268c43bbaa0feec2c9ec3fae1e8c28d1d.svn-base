﻿function monitorTypeConfigured(moniterType, infraObjectId, urls) {
    let prServerEdition = "";
    let dRServerEdition = "";
    let prDatabaseName = "";
    let drDatabaseName = "";
    let Replication = "";
    let prIpaddress = "";
    let drIpaddress = "";
    let prrole = "";
    let drrole = "";
    let componentMonitorData = [];
    let replicationMonitorData = [];
    let prStatus = ""
    let drStatus = ""
    let additionalRow = []

    let prodWorkflowValues = [];
    let drWorkflowValues = [];
    let prodStatusValues = [];
    let drStatusValues = [];
    let hasWorkflowNames = "";
    let nonMonitorpr1 = [];
    let nonMonitordr1 = [];
    let lengthvaluepr1;
    let lengthdatapr1;
    let lengthvaluedr1;
    let lengthdatadr1;
    let lengthnamepr1;
    let lengthservicepr1;
    let lengthnamedr1;
    let lengthservicedr1;
   
    function checkValue(value) {
        return (value !== null && value !== '' && value !== undefined) ? value : "NA";
    }

    function updateTable(value) {
        
        let replicationType = value?.replicationTypeName?.toLowerCase()?.includes('rsync')
        //let uniqueServerTypes = {}; 
        
        //value?.serverDto.forEach(item => {
        //    if (item?.serverType && !uniqueServerTypes[item?.serverType]) {
        //        uniqueServerTypes[item?.serverType] = `${item?.serverType}`;
        //    }
        //});
        let uniqueServerTypes = [];

        value?.serverDto?.forEach(item => {
            if (item?.serverType && !uniqueServerTypes?.includes(item?.serverType)) {
                let serverType = item?.serverType.toLowerCase();
                if (serverType?.includes('pr')) {
                    serverType = "Production Server"
                }
                if (serverType?.includes('dr')) {
                    serverType = "DR Server"
                } 
                uniqueServerTypes.unshift(serverType);
            }
        });

      
        let infraobjectemptydata = `
        <div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">
            <table class="table table-hover mb-0 table-sm bg-white" style="table-layout:fixed">
                <thead style="position: sticky;top: 0px;">
                    <tr>
                        <th>Component Monitor</th>`;

      
        //for (let serverType in uniqueServerTypes) {
        //    infraobjectemptydata += `<th id="${serverType?.toLowerCase()}table">${uniqueServerTypes[serverType]}</th>`;
        //}
        uniqueServerTypes?.forEach(serverType => {
            infraobjectemptydata += `<th id="${serverType?.toLowerCase()}table">${serverType}</th>`;
        });

        infraobjectemptydata += `
                    </tr>
                </thead>
                <tbody>`;


        componentMonitorData?.forEach(item => {
            infraobjectemptydata += `<tr>
        <td class="text-truncate" >${item?.label}</td>`

            for (let key in item) {

                if (key !== 'label')
                    infraobjectemptydata += ` <td class="text-truncate">${getIconClass(item[key], item?.label, item?.statusClass)} ${item[key]}</td>`
            }
          
            `
        <td>${item?.statusClass}</td>
    </tr>`;
        });
       
        infraobjectemptydata += `</tbody>
                        </table>
                    </div>
                    <div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">
                        <table class="table table-hover mb-0 table-sm bg-white" style="table-layout:fixed">
                            <thead style="position: sticky;top: 0px;z-index:1;">
                                <tr>
                                    <th>Replication Monitor</th>
                                    <th>${!replicationType ? 'Production Server' : ''}</th>
                                   
                                </tr>
                            </thead>
                            <tbody>`;

        replicationMonitorData?.forEach(item => {
            infraobjectemptydata += `<tr>
                                    <td class="text-truncate">${item?.label}</td>
                                    <td class="text-truncate">${getIconClass(item?.prodValue, item?.label)} ${item?.prodValue}</td>
                                </tr>`;
        });

        infraobjectemptydata += `</tbody>
                        </table>
                    </div>`;

        setTimeout(() => {
            $("#infraobjectalldata").empty().append(infraobjectemptydata);
        }, 200);
    }

    function getIconClass(value, label, status) {

        let iconClass = '';
        if (status !== null && status !== undefined) {

            return `<i class="${status} me-1 fs-6"></i>`;
        }

        if (!iconClass) {
            switch (label?.toLowerCase()) {

                case "database sid":
                case "database name":
                    iconClass = 'text-primary cp-database';
                    break;
                case "server name":
                case "mssql server edition":
                    iconClass = 'text-primary cp-server';
                    break;
                case "replication type":
                    iconClass = 'cp-replication-type text-primary';
                    break;
                case "monitoringworkflow":
                    iconClass = 'text-primary cp-workflow-configuration';
                    break;

                case 'na':
                    iconClass = 'text-danger cp-disable';
                    break;
            }
            if (value) {
                const lowerValue = Array.isArray(value) ? value[0]?.toLowerCase() : value?.toLowerCase();

                switch (lowerValue) {
                    case 'na':
                    case 'disabled':
                    case 'no':
                    case 'not allowed':
                        iconClass = 'text-danger cp-disable';
                        break;
                    case 'up':
                        iconClass = 'cp-up-linearrow me-1 text-success';
                        break;
                    case 'down':
                        iconClass = 'cp-down-linearrow me-1 text-danger';
                        break;
                    case 'pending':
                        iconClass = 'cp-pending me-1 text-warning';
                        break;
                    case 'stopped':
                    case 'stopped -1':
                    case 'stopped -2':
                    case 'stopped -3':
                    case 'error':
                        iconClass = 'text-danger cp-fail-back';
                        break;
                    case 'running':
                    case 'running -1':
                    case 'running -2':
                    case 'running -3':
                        iconClass = 'text-success cp-reload cp-animate';
                        break;
                }
            }
        }

        return iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
    }

    $.ajax({
        url: urls,
        method: 'GET',
        data: {
            infraObjectId: infraObjectId,
        },
        dataType: 'json',
        async: true,
        success: function (res) {

            const value = res?.data;
            
            if (!value?.hasOwnProperty('id') || (moniterType == "" || moniterType == null || !["Oracle", "MssqlAlwaysOn", "Postgres", "OracleRac", "Mysql", "MssqlNLS", "DB2HADR", "MongoDB", "mssqldbmirroring", "SVC", "HyperV", "RoboCopy", "RSync", "AS400", "SRM", "OpenShift", "AzureMysqlPaas", "AzurePostgresPaas", "NetAppSnapMirror",
             "Hp3par"]?.includes(moniterType))) {
                if ((value?.replicationTypeName?.toLowerCase()?.includes("oraclerac")) || value?.replicationTypeName?.toLowerCase()?.includes("oracle-rac") || (value?.subType?.toLowerCase()?.includes("oraclerac") ||  value?.subType?.toLowerCase()?.includes("oracle_rac") || moniterType?.toLowerCase()?.includes('oraclerac'))) {
                    let PRServerArray = value?.serverDto?.filter((d) => d?.serverType === 'PRDBServer');
                    let lengthnode = PRServerArray?.length;

                    let nodes = {};
                    let databases = {};
                    let nodeName;

                  
                    let selectNode = $('<select id="nodeName" class="form-select w-100" aria-label="Default select example"></select>');

              
                    $('#nodeRelationCont').append(selectNode);

                  
                    for (let i = 1; i <= lengthnode; i++) {
                        nodeName = `Node${i}`;

                   
                        nodes[nodeName] = [];
                        databases[nodeName] = [];
                                             
                        selectNode.append(`<option value="${nodeName}">${nodeName}</option>`);
                    }

                  
                
                        let node1 = [];
                        let node2 = [];

                  
                    if ($('#nodeName option:selected')?.text()?.includes('1')) {
                            
                            value?.serverDto?.forEach((item) => {
                                if (item?.nodeName?.toLowerCase()?.includes('pr1') || item?.nodeName?.toLowerCase()?.includes('dr1')) {
                                    node1.push(item);
                                }
                            });
                            value?.databaseDto?.forEach((item) => {
                                if (item?.nodeName?.toLowerCase()?.includes('pr1') || item?.nodeName?.toLowerCase()?.includes('dr1')) {
                                    node2.push(item);
                                }
                            });
                    } else if ($('#nodeName option:selected')?.text()?.includes('2')) {
                            value?.serverDto?.forEach((item) => {
                                if (item?.nodeName?.toLowerCase()?.includes('pr2') || item?.nodeName?.toLowerCase()?.includes('dr2')) {
                                    node1.push(item);
                                }
                            });
                            value?.databaseDto?.forEach((item) => {
                                if (item?.nodeName?.toLowerCase()?.includes('pr2') || item?.nodeName?.toLowerCase()?.includes('dr2')) {
                                    node2.push(item);
                                }
                            });
                    } else if ($('#nodeName option:selected')?.text()?.includes('3')) {
                            value?.serverDto?.forEach((item) => {
                                if (item?.nodeName?.toLowerCase()?.includes('pr3') || item?.nodeName?.toLowerCase()?.includes('dr3')) {
                                    node1.push(item);
                                }
                            });
                            value?.databaseDto?.forEach((item) => {
                                if (item?.nodeName?.toLowerCase()?.includes('pr3') || item?.nodeName?.toLowerCase()?.includes('dr3')) {
                                    node2.push(item);
                                }
                            });
                       }

                      
                    value.serverDtonode = node1;
                    value.databaseDtonode = node2;
                    let PRServerArray1 = value?.serverDtonode?.filter((d) => d?.serverType?.toLowerCase()?.includes('pr'));
                    let DRServerArray1 = value?.serverDtonode?.filter((d) => d?.serverType?.toLowerCase()?.includes('dr'));
                    let PRDBArray1 = value?.databaseDtonode?.filter((d) => d?.type?.toLowerCase()?.includes('pr'));
                    let DRDBArray1 = value?.databaseDtonode?.filter((d) => d?.type?.toLowerCase()?.includes('dr'));
                    prStatus = PRServerArray1[0]?.status?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : PRServerArray1[0]?.status?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : PRServerArray1[0]?.status?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-disable me-1 text-danger";
                    let ipprvalue = PRServerArray1[0]?.connectViaHostName?.toLowerCase() === "true" ? PRServerArray1[0]?.hostName : PRServerArray1[0]?.ipAddress

                    let convertToHtmlPR1 = `<i class='${prStatus}'></i>`
                    componentMonitorData = [
                        { label: "Server Name", prodValue: checkValue(PRServerArray1[0]?.serverName) || '' },
                        { label: "IP Address/Hostname", prodValue: convertToHtmlPR1 + checkValue((ipprvalue) || '') },
                        { label: "Database Name", prodValue: checkValue(DRDBArray1[0]?.sid) || '' },
                    ].concat(additionalRow);

                    replicationMonitorData = [
                        { label: "Replication Type", prodValue: checkValue(value?.replicationTypeName) || '' },
                    ];

                    
                        value?.serverDtonode?.forEach((server, index) => {
                            let drStatus = server?.status?.toLowerCase() === "down"
                                ? "cp-down-linearrow me-1 text-danger"
                                : server?.status?.toLowerCase() === "pending"
                                    ? "cp-pending  me-1 text-warning"
                                    : server?.status?.toLowerCase() === "up"
                                        ? "cp-up-linearrow me-1 text-success"
                                        : "cp-disable me-1 text-danger";

                            let convertToHtml = `<i class='${drStatus}'></i>`;

                            let ipvalue = server?.connectViaHostName?.toLowerCase() === "true" ? server?.hostName : server?.ipAddress;
                            if (!server.serverType.toLowerCase()?.includes('pr')) {
                                componentMonitorData[0][server?.serverType + 'ServerEdition'] = checkValue(server?.serverName);
                                componentMonitorData[1][server?.serverType + 'ServerIpAddress'] = convertToHtml + checkValue(ipvalue);
                                componentMonitorData[2][server?.serverType + 'Database'] = checkValue(value?.databaseDtonode[index]?.sid) || '';
                                replicationMonitorData[0][server?.serverType + 'Replication'] = checkValue(value?.replicationTypeName);
                            }
                        });
                  

              
           
                    nonMonitorpr1 = value?.monitorServiceDto?.filter((t) => t?.serverType?.toLowerCase()?.includes("pr"));
                    nonMonitordr1 = value?.monitorServiceDto?.filter((t) => t?.serverType?.toLowerCase()?.includes("dr"));

                    nonMonitorpr1?.forEach((a) => {
                        prodWorkflowValues.push(checkValue(a?.workflowName));
                        prodStatusValues.push(checkValue(a?.isServiceUpdate));
                    });
                    nonMonitordr1?.forEach((b) => {

                        drWorkflowValues.push(checkValue(b?.workflowName));
                        drStatusValues.push(checkValue(b?.isServiceUpdate));
                    });
                    if (prodStatusValues.filter(service => service?.toLowerCase() === "running")?.length >= 1) {
                        lengthvaluepr1 = prodStatusValues?.filter(service => service?.toLowerCase() === "running")?.length;
                        lengthdatapr1 = "Running".concat(" -" + lengthvaluepr1);
                    } else if (prodStatusValues?.filter(service => service?.toLowerCase() === "stopped" || service?.toLowerCase() === "error").length >= 1) {
                        lengthvaluepr1 = prodStatusValues?.filter(service => service?.toLowerCase() === "stopped" || service?.toLowerCase() === "error").length;
                        lengthdatapr1 = "stopped".concat(" -" + lengthvaluepr1);
                    }
                    else {
                        lengthdatapr1 = prodStatusValues
                    }
                    if (prodWorkflowValues?.filter(name => name?.toLowerCase() === "na").length >= 1) {
                        lengthnamepr1 = prodWorkflowValues?.filter(name => name?.toLowerCase() === "na")?.length;
                        lengthservicepr1 = "NA".concat(" -" + lengthnamepr1 > 1 ? lengthnamepr1 : "");
                    }
                    else {
                        lengthservicepr1 = prodWorkflowValues
                    }
                    if (drStatusValues?.filter(service1 => service1?.toLowerCase() === "running")?.length >= 1) {
                        lengthvaluedr1 = drStatusValues?.filter(service1 => service1?.toLowerCase() === "running")?.length;
                        lengthdatadr1 = "Running".concat(" -" + lengthvaluedr1);
                    } else if (drStatusValues?.filter(service1 => service1?.toLowerCase() === "stopped" || service1?.toLowerCase() === "error").length >= 1) {
                        lengthvaluedr1 = drStatusValues?.filter(service1 => service1?.toLowerCase() === "stopped" || service1?.toLowerCase() === "error")?.length;
                        lengthdatadr1 = "stopped".concat(" -" + lengthvaluedr1);
                    }
                    else {
                        lengthdatadr1 = drStatusValues
                    }
                    if (drWorkflowValues?.filter(name1 => name1?.toLowerCase() === "na")?.length >= 1) {
                        lengthnamedr1 = drWorkflowValues?.filter(name1 => name1?.toLowerCase() === "na")?.length;
                        lengthservicedr1 = "NA".concat(" -" + lengthnamedr1 > 1 ? lengthnamedr1 : "");
                    }
                    else {
                        lengthservicedr1 = drWorkflowValues
                    }
                    hasWorkflowNames = value?.monitorServiceDto?.length > 0;

                    if (hasWorkflowNames) {

                        additionalRow.push({ label: "Monitoring Workflow", prodValue: lengthservicepr1, drValue: lengthservicedr1 });
                        additionalRow.push({ label: "Application Status", prodValue: lengthdatapr1, drValue: lengthdatadr1 });
                    }
                  
                    if (value?.replicationTypeName === "Application-No-Replication" || value?.typeName?.toLowerCase() === "application" || value?.replicationTypeName?.toLowerCase() === "openshift") {
                        componentMonitorData = componentMonitorData?.map(item => {

                            if (item?.label === "Database Name") {
                                return null;
                            }
                            return item;
                        }).filter(item => item !== null);
                    }

                    updateTable(value,componentMonitorData, replicationMonitorData);
                    $('#nodeName').on('change', function () {
                        let node2 = [];
                        let node3 = [];
                       
                        if ($('#nodeName option:selected').text().includes('1')) {
                          
                            value?.serverDto?.forEach((item1) => {
                                if (item1?.nodeName?.toLowerCase()?.includes('pr1') || item1?.nodeName?.toLowerCase()?.includes('dr1')) {
                                    node2.push(item1);
                                }
                            });
                            value?.databaseDto?.forEach((item1) => {
                                if (item1?.nodeName?.toLowerCase()?.includes('pr1') || item1?.nodeName?.toLowerCase()?.includes('dr1')) {
                                    node3.push(item1);
                                }
                            });
                        } else if ($('#nodeName option:selected').text().includes('2')) {
                            value?.serverDto?.forEach((item1) => {
                                if (item1?.nodeName?.toLowerCase()?.includes('pr2') || item1?.nodeName?.toLowerCase()?.includes('dr2')) {
                                    node2.push(item1);
                                }
                            });
                            value?.databaseDto?.forEach((item1) => {
                                if (item1?.nodeName?.toLowerCase()?.includes('pr2') || item1?.nodeName?.toLowerCase()?.includes('dr2')) {
                                    node3.push(item1);
                                }
                            });
                        } else if ($('#nodeName option:selected').text().includes('3')) {
                            value?.serverDto?.forEach((item1) => {
                                if (item1?.nodeName?.toLowerCase()?.includes('pr3') || item1?.nodeName?.toLowerCase()?.includes('dr3')) {
                                    node2.push(item1);
                                }
                            });
                            value?.databaseDto?.forEach((item1) => {
                                if (item1?.nodeName?.toLowerCase()?.includes('pr3') || item1?.nodeName?.toLowerCase()?.includes('dr3')) {
                                    node3.push(item1);
                                }
                            });
                        }


                        value.serverDtonode2 = node2;
                        value.databaseDtonode2 = node3;
                        let PRServerArray2 = value?.serverDtonode2?.filter((d) => d?.serverType?.toLowerCase()?.includes('pr'));
                        let DRServerArray2 = value?.serverDtonode2.filter((d) => d?.serverType?.toLowerCase()?.includes('dr'));
                        let PRDBArray2 = value?.databaseDtonode2?.filter((d) => d?.type?.toLowerCase()?.includes('pr'));
                        let DRDBArray2 = value?.databaseDtonode2?.filter((d) => d?.type?.toLowerCase()?.includes('dr'));
                        prStatus = PRServerArray2[0]?.status?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : PRServerArray2[0]?.status?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : PRServerArray2[0]?.status?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-disable me-1 text-danger";
                        let ipprvalue1 = PRServerArray2[0]?.connectViaHostName?.toLowerCase() === "true" ? PRServerArray2[0]?.hostName : PRServerArray2[0]?.ipAddress;
                        let convertToHtmlPR2 = `<i class='${prStatus}'></i>`
                        componentMonitorData = [
                            { label: "Server Name", prodValue: checkValue(PRServerArray2[0]?.serverName) || '' },
                            { label: "IP Address/Hostname", prodValue: convertToHtmlPR2 + checkValue((ipprvalue1) || '') },
                            { label: "Database Name", prodValue: checkValue(DRDBArray2[0]?.sid) || '' },
                        ].concat(additionalRow);

                        replicationMonitorData = [
                            { label: "Replication Type", prodValue: checkValue(value?.replicationTypeName) || '' },
                        ];


                        value?.serverDtonode2?.forEach((server, index) => {
                            let drStatus = server?.status?.toLowerCase() === "down"
                                ? "cp-down-linearrow me-1 text-danger"
                                : server?.status?.toLowerCase() === "pending"
                                    ? "cp-pending  me-1 text-warning"
                                    : server.status?.toLowerCase() === "up"
                                        ? "cp-up-linearrow me-1 text-success"
                                        : "cp-disable me-1 text-danger";

                            let convertToHtml = `<i class='${drStatus}'></i>`;

                            let ipvalue1 = server?.connectViaHostName?.toLowerCase() === "true" ? server?.hostName : server?.ipAddress;
                            if (!server?.serverType?.toLowerCase()?.includes('pr')) {
                                componentMonitorData[0][server?.serverType + 'ServerEdition'] = checkValue(server?.serverName);
                                componentMonitorData[1][server?.serverType + 'ServerIpAddress'] = convertToHtml + checkValue(ipvalue1);
                                componentMonitorData[2][server?.serverType + 'Database'] = checkValue(value?.databaseDtonode2[index]?.sid) || '';
                                replicationMonitorData[0][server?.serverType + 'Replication'] = checkValue(value?.replicationTypeName);
                            }
                        });


                        nonMonitorpr1 = value?.monitorServiceDto?.filter((t) => t.serverType?.toLowerCase()?.includes("pr"));
                        nonMonitordr1 = value?.monitorServiceDto?.filter((t) => t.serverType?.toLowerCase()?.includes("dr"));

                        nonMonitorpr1?.forEach((a) => {
                            prodWorkflowValues.push(checkValue(a?.workflowName));
                            prodStatusValues.push(checkValue(a?.isServiceUpdate));
                        });
                        nonMonitordr1?.forEach((b) => {

                            drWorkflowValues.push(checkValue(b?.workflowName));
                            drStatusValues.push(checkValue(b?.isServiceUpdate));
                        });

                        if (prodStatusValues?.filter(service => service?.toLowerCase() === "running")?.length >= 1) {
                            lengthvaluepr1 = prodStatusValues?.filter(service => service?.toLowerCase() === "running")?.length;
                            lengthdatapr1 = "Running".concat(" -" + lengthvaluepr1);
                        } else if (prodStatusValues?.filter(service => service?.toLowerCase() === "stopped" || service?.toLowerCase() === "error")?.length >= 1) {
                            lengthvaluepr1 = prodStatusValues?.filter(service => service?.toLowerCase() === "stopped" || service?.toLowerCase() === "error")?.length;
                            lengthdatapr1 = "stopped".concat(" -" + lengthvaluepr1);
                        }
                        else {
                            lengthdatapr1 = prodStatusValues
                        }
                        if (prodWorkflowValues?.filter(name => name?.toLowerCase() === "na")?.length >= 1) {
                            lengthnamepr1 = prodWorkflowValues?.filter(name => name?.toLowerCase() === "na")?.length;
                            lengthservicepr1 = "NA".concat(" -" + lengthnamepr1 > 1 ? lengthnamepr1 : "");
                        }
                        else {
                            lengthservicepr1 = prodWorkflowValues
                        }
                        if (drStatusValues?.filter(service1 => service1?.toLowerCase() === "running")?.length >= 1) {
                            lengthvaluedr1 = drStatusValues?.filter(service1 => service1?.toLowerCase() === "running")?.length;
                            lengthdatadr1 = "Running".concat(" -" + lengthvaluedr1);
                        } else if (drStatusValues?.filter(service1 => service1?.toLowerCase() === "stopped" || service1?.toLowerCase() === "error")?.length >= 1) {
                            lengthvaluedr1 = drStatusValues?.filter(service1 => service1?.toLowerCase() === "stopped" || service1?.toLowerCase() === "error")?.length;
                            lengthdatadr1 = "stopped".concat(" -" + lengthvaluedr1);
                        }
                        else {
                            lengthdatadr1 = drStatusValues
                        }
                        if (drWorkflowValues?.filter(name1 => name1?.toLowerCase() === "na")?.length >= 1) {
                            lengthnamedr1 = drWorkflowValues?.filter(name1 => name1?.toLowerCase() === "na")?.length;
                            lengthservicedr1 = "NA".concat(" -" + lengthnamedr1 > 1 ? lengthnamedr1 : "");
                        }
                        else {
                            lengthservicedr1 = drWorkflowValues
                        }
                        hasWorkflowNames = value?.monitorServiceDto?.length > 0;

                        if (hasWorkflowNames) {

                            additionalRow.push({ label: "Monitoring Workflow", prodValue: lengthservicepr1, drValue: lengthservicedr1 });
                            additionalRow.push({ label: "Application Status", prodValue: lengthdatapr1, drValue: lengthdatadr1 });
                        }

                        if (value?.replicationTypeName === "Application-No-Replication" || value?.typeName?.toLowerCase() === "application" ||value?.replicationTypeName?.toLowerCase() === "openshift") {
                            componentMonitorData = componentMonitorData?.map(item => {

                                if (item.label === "Database Name") {
                                    return null;
                                }
                                return item;
                            })?.filter(item => item !== null);
                        }

                        updateTable(value,componentMonitorData, replicationMonitorData);
                    });
                       
                   
                }
                const srmserver = value?.serverDto?.filter((db) => db?.type?.toLowerCase()?.includes('srm'));
                const vcenterserver = value?.serverDto?.filter((serve) => serve?.serverType?.toLowerCase()?.includes('pr'));
                prStatus = srmserver[0]?.status?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : srmserver[0]?.status?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : srmserver[0]?.status?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-disable me-1 text-danger";
                let vcenterStatus = vcenterserver[0]?.status?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : vcenterserver[0]?.status?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : vcenterserver[0]?.status?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-disable me-1 text-danger";
                let convertToHtmlSrmPR = `<i class='${prStatus}'></i>`
                let convertToHtmlVcenterPR = `<i class='${vcenterStatus}'></i>`

                if (value?.replicationTypeName?.toLowerCase()?.includes('srm')) {
                    
                    componentMonitorData = [
                        { label: "SRM Server Name", prodValue: checkValue(srmserver[0]?.serverName) },
                        { label: "SRM IP Address/Hostname", prodValue: convertToHtmlSrmPR + checkValue(srmserver[0]?.ipAddress) },
                        { label: "Vcenter Server Name", prodValue: checkValue(vcenterserver[0]?.serverName) },
                        { label: "Vcenter IP Address/Hostname", prodValue:convertToHtmlVcenterPR + checkValue(vcenterserver[0]?.ipAddress) },
                       
                    ].concat(additionalRow);
                    
                    replicationMonitorData = [
                        { label: "Replication Type", prodValue: checkValue(value?.replicationTypeName) },
                    ];

                    srmserver.forEach((serve, index) => {
                        
                        drStatus = serve?.status?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : serve?.status?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : serve?.status?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-disable me-1 text-danger";

                        let convertToHtmlsrm = `<i class='${drStatus}'></i>`
                        if (!serve?.serverType?.toLowerCase()?.includes('pr')||'NA') {

                            componentMonitorData[0][serve?.type + 'ServerEdition'] = checkValue(serve?.serverName)
                            componentMonitorData[1][serve?.type + 'ServerIpAddress'] = convertToHtmlsrm + checkValue(serve?.ipAddress)

                           
                            replicationMonitorData[0][serve?.serverType + 'Replication'] = checkValue(value?.replicationTypeName)
                        }
                    });

                    value?.serverDto.forEach((vcenter, index) => {
                       let vcenterdrStatus = vcenter?.status?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : vcenter?.status?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : vcenter?.status?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-disable me-1 text-danger";

                        let convertToHtmlvcenter = `<i class='${vcenterdrStatus}'></i>`
                        if (!vcenter?.serverType?.toLowerCase()?.includes('pr')) {

                            componentMonitorData[2][vcenter?.serverType + 'ServerEdition'] = checkValue(vcenter?.serverName)
                            componentMonitorData[3][vcenter?.serverType + 'ServerIpAddress'] = convertToHtmlvcenter + checkValue(vcenter?.ipAddress)


                           
                        }
                    });
                    updateTable(value);
                }
                else {
                    
                    const prServers = value?.serverDto?.filter((serve) => serve?.serverType?.toLowerCase()?.includes('pr'));
                    const drServers = value?.databaseDto?.filter((db) => db?.type?.toLowerCase()?.includes('dr'));

                    prStatus = prServers[0]?.status?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : prServers[0]?.status?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : prServers[0]?.status?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-disable me-1 text-danger";

                    let convertToHtmlPR = `<i class='${prStatus}'></i>`
                    let ipprvalue2 = prServers[0]?.connectViaHostName?.toLowerCase() === "true" ? prServers[0]?.hostName : prServers[0]?.ipAddress
                    componentMonitorData = [
                        { label: "Server Name", prodValue: checkValue(prServers[0]?.serverName) },
                        { label: "IP Address/Hostname", prodValue: convertToHtmlPR + checkValue(ipprvalue2) },
                        { label: "Database Name", prodValue: checkValue(drServers[0]?.sid) },
                    ]?.concat(additionalRow);

                    replicationMonitorData = [
                        { label: "Replication Type", prodValue: checkValue(value?.replicationTypeName) },
                    ];

                    value?.serverDto?.forEach((serve, index) => {

                        drStatus = serve?.status?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : serve?.status?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : serve?.status?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-disable me-1 text-danger";

                        let convertToHtml = `<i class='${drStatus}'></i>`
                        let ipvalue2 = serve?.connectViaHostName?.toLowerCase() === "true" ? serve?.hostName : serve?.ipAddress;
                        if (!serve.serverType.toLowerCase()?.includes('pr')) {

                            componentMonitorData[0][serve?.serverType + 'ServerEdition'] = checkValue(serve?.serverName)
                            componentMonitorData[1][serve?.serverType + 'ServerIpAddress'] = convertToHtml + checkValue(ipvalue2)

                            componentMonitorData[2][serve?.serverType + 'Database'] = checkValue(value?.databaseDto[index]?.sid)
                            replicationMonitorData[0][serve?.serverType + 'Replication'] = checkValue(value?.replicationTypeName)
                        }
                    });


                    nonMonitorpr1 = value?.monitorServiceDto?.filter((t) => t?.serverType?.toLowerCase()?.includes("pr"));
                    nonMonitordr1 = value?.monitorServiceDto?.filter((t) => t?.serverType?.toLowerCase()?.includes("dr"));

                    nonMonitorpr1?.forEach((a) => {
                        prodWorkflowValues.push(checkValue(a?.workflowName));
                        prodStatusValues.push(checkValue(a?.isServiceUpdate));
                    });
                    nonMonitordr1?.forEach((b) => {

                        drWorkflowValues.push(checkValue(b?.workflowName));
                        drStatusValues.push(checkValue(b?.isServiceUpdate));
                    });

                    if (prodStatusValues?.filter(service => service?.toLowerCase() === "running")?.length >= 1) {
                        lengthvaluepr1 = prodStatusValues?.filter(service => service?.toLowerCase() === "running")?.length;
                        lengthdatapr1 = "Running".concat(" -" + lengthvaluepr1);
                    } else if (prodStatusValues?.filter(service => service?.toLowerCase() === "stopped" || service?.toLowerCase() === "error")?.length >= 1) {
                        lengthvaluepr1 = prodStatusValues?.filter(service => service?.toLowerCase() === "stopped" || service?.toLowerCase() === "error")?.length;
                        lengthdatapr1 = "stopped".concat(" -" + lengthvaluepr1);
                    }
                    else {
                        lengthdatapr1 = prodStatusValues
                    }
                    if (prodWorkflowValues?.filter(name => name?.toLowerCase() === "na")?.length >= 1) {
                        lengthnamepr1 = prodWorkflowValues?.filter(name => name?.toLowerCase() === "na")?.length;
                        lengthservicepr1 = "NA".concat(" -" + lengthnamepr1 > 1 ? lengthnamepr1 : "");
                    }
                    else {
                        lengthservicepr1 = prodWorkflowValues
                    }
                    if (drStatusValues?.filter(service1 => service1?.toLowerCase() === "running")?.length >= 1) {
                        lengthvaluedr1 = drStatusValues?.filter(service1 => service1?.toLowerCase() === "running")?.length;
                        lengthdatadr1 = "Running".concat(" -" + lengthvaluedr1);
                    } else if (drStatusValues?.filter(service1 => service1?.toLowerCase() === "stopped" || service1?.toLowerCase() === "error")?.length >= 1) {
                        lengthvaluedr1 = drStatusValues?.filter(service1 => service1?.toLowerCase() === "stopped" || service1?.toLowerCase() === "error")?.length;
                        lengthdatadr1 = "stopped".concat(" -" + lengthvaluedr1);
                    }
                    else {
                        lengthdatadr1 = drStatusValues
                    }
                    if (drWorkflowValues?.filter(name1 => name1?.toLowerCase() === "na")?.length >= 1) {
                        lengthnamedr1 = drWorkflowValues?.filter(name1 => name1?.toLowerCase() === "na")?.length;
                        lengthservicedr1 = "NA".concat(" -" + lengthnamedr1 > 1 ? lengthnamedr1 : "");
                    }
                    else {
                        lengthservicedr1 = drWorkflowValues
                    }
                    hasWorkflowNames = value?.monitorServiceDto?.length > 0;

                    if (hasWorkflowNames) {

                        additionalRow.push({ label: "Monitoring Workflow", prodValue: lengthservicepr1, drValue: lengthservicedr1 });
                        additionalRow.push({ label: "Application Status", prodValue: lengthdatapr1, drValue: lengthdatadr1 });
                    }
                  
                    if (value?.replicationTypeName === "Application-No-Replication" || value?.typeName?.toLowerCase()==="application" || value?.replicationTypeName?.toLowerCase() === "openshift") {
                        componentMonitorData = componentMonitorData?.map(item => {

                            if (item.label === "Database Name") {
                                return null;
                            }
                            return item;
                        }).filter(item => item !== null);
                    }

                    updateTable(value);
                }
            }
        }
    });
}
