﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Workflow.Events.Verify;

public class UpdateWorkflowVerifyEventHandler : INotificationHandler<UpdateWorkflowVerifyEvent>
{
    private readonly ILogger<UpdateWorkflowVerifyEvent> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public UpdateWorkflowVerifyEventHandler(ILogger<UpdateWorkflowVerifyEvent> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(UpdateWorkflowVerifyEvent updatedEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Workflow '{updatedEvent.WorkflowName}' {(updatedEvent.IsVerify ? "Verify" : "UnVerify")} successfully.");

        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{(updatedEvent.IsVerify ? "Verify" : "UnVerify")} {Modules.Workflow}",
            Entity = Modules.Workflow.ToString(),
            ActivityType = updatedEvent.IsVerify ? "Verify" : "UnVerify",
            ActivityDetails = $"Workflow '{updatedEvent.WorkflowName}' {(updatedEvent.IsVerify ? "Verify" : "UnVerify")} successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);
    }
}