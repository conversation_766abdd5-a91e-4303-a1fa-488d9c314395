﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Workflow.Events.Update;

public class WorkflowUpdatedEventHandler : INotificationHandler<WorkflowUpdatedEvent>
{
    private readonly ILogger<WorkflowUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowUpdatedEventHandler(ILoggedInUserService userService, ILogger<WorkflowUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Workflow '{updatedEvent.WorkflowName}' updated successfully.");

        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress ?? "::1",
            Action = $"{ActivityType.Update} {Modules.Workflow}",
            Entity = Modules.Workflow.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Workflow '{updatedEvent.WorkflowName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);
    }
}