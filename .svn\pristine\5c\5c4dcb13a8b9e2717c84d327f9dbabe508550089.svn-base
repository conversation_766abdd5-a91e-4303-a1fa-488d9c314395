﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FiaTemplate.Events.PaginatedView;

public class FiaTemplatePaginatedeventHandler : INotificationHandler<FiaTemplatePaginatedevent>
{
    private readonly ILogger<FiaTemplatePaginatedeventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FiaTemplatePaginatedeventHandler(ILoggedInUserService userService,
        ILogger<FiaTemplatePaginatedeventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(FiaTemplatePaginatedevent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = "FiaTemplate",
            Action = $"{ActivityType.View} FiaTemplate",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "FIA Template viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("FIA Template viewed");
    }
}