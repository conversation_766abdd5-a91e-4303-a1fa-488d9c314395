﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
	  <ImplicitUsings>enable</ImplicitUsings>
	  
    <Nullable>annotations</Nullable>
  </PropertyGroup>
	<ItemGroup>
		<PackageReference Include="FluentValidation" Version="11.11.0" />
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
		<PackageReference Include="AutoMapper" Version="14.0.0" />
		<PackageReference Include="LazyCache" Version="2.4.0" />
		<PackageReference Include="LazyCache.AspNetCore" Version="2.4.0" />
		<PackageReference Include="MailKit" Version="4.11.0" />
		<PackageReference Include="MediatR" Version="12.5.0" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
		<PackageReference Include="Microsoft.Data.SqlClient.SNI.runtime" Version="6.0.2" />
		<!--<PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="7.0.9" />-->
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.4" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.2" />
		<PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.ObjectPool" Version="9.0.4" />
		<PackageReference Include="Microsoft.NETCore.Targets" Version="5.0.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
		<!--<PackageReference Include="NetEscapades.AspNetCore.SecurityHeaders.TagHelpers" Version="0.20.0" />-->
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
		<PackageReference Include="NWebsec.AspNetCore.Middleware" Version="3.0.0" />
		<PackageReference Include="Oracle.EntityFrameworkCore" Version="9.23.80" />
		<PackageReference Include="Oracle.ManagedDataAccess.Core" Version="23.8.0" />
		<PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="9.0.0-preview.3.efcore.9.0.0" />
		<PackageReference Include="Quartz" Version="3.14.0" />
		<PackageReference Include="Quartz.AspNetCore" Version="3.14.0" />
		<PackageReference Include="Quartz.Extensions.DependencyInjection" Version="3.14.0" />
		<PackageReference Include="Quartz.Extensions.Hosting" Version="3.14.0" />
		<PackageReference Include="Quartz.Serialization.Json" Version="3.14.0" />
		<PackageReference Include="RestSharp" Version="112.1.0" />

		<PackageReference Include="Seq.Api" Version="2024.3.0" />
		<PackageReference Include="Serilog" Version="4.2.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Enrichers.ClientInfo" Version="2.1.2" />
		<PackageReference Include="Serilog.Enrichers.CorrelationId" Version="3.0.1" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
		<PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
		<PackageReference Include="Serilog.Expressions" Version="5.0.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="9.0.1" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
		<PackageReference Include="Serilog.Sinks.AspNetCore.SignalR" Version="0.4.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
		<PackageReference Include="Serilog.Sinks.SignalR.Core" Version="0.1.2" />
		<!--<PackageReference Include="System.DirectoryServices" Version="7.0.1" />-->
		<PackageReference Include="System.DirectoryServices.AccountManagement" Version="9.0.4" />
		<!--<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.0.0" />-->
		<!--<PackageReference Include="Castle.Core" Version="5.1.1" />-->
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
 
  </ItemGroup>

</Project>
