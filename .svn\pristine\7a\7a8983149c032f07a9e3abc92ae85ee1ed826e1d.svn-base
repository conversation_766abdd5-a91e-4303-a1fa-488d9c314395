using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DriftEventFixture : IDisposable
{
    public List<DriftEvent> DriftEventPaginationList { get; set; }
    public List<DriftEvent> DriftEventList { get; set; }
    public DriftEvent DriftEventDto { get; set; }

    public const string CompanyId = "COMPANY_123";
   
    public ApplicationDbContext DbContext { get; private set; }

    public DriftEventFixture()
    {
        var fixture = new Fixture();

        DriftEventList = fixture.Create<List<DriftEvent>>();

        DriftEventPaginationList = fixture.CreateMany<DriftEvent>(20).ToList();

        DriftEventPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftEventPaginationList.ForEach(x => x.IsActive = true);
     
        DriftEventDto = fixture.Create<DriftEvent>();
        DriftEventDto.IsActive = true;
      

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
