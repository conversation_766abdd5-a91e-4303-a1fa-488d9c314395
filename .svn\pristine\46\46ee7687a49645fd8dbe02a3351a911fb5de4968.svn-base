﻿using ContinuityPatrol.Application.Features.ComponentType.Commands.Create;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Update;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IComponentTypeService
{
    Task<bool> IsComponentTypeExist(string componentType, string id);
    Task<ComponentTypeDetailVm> GetComponentTypeById(string serverTypeId);
    Task<BaseResponse> DeleteAsync(string serverTypeId);
    Task<BaseResponse> CreateAsync(CreateComponentTypeCommand createComponentTypeCommand);
    Task<BaseResponse> UpdateAsync(UpdateComponentTypeCommand updateComponentTypeCommand);
    Task<List<ComponentTypeListVm>> GetComponentTypeList();
    Task<List<ComponentTypeModel>> GetComponentTypeListByName(string name);
    Task<PaginatedResult<ComponentTypeListVm>> GetPaginatedComponentTypes(GetComponentTypePaginatedListQuery query);
}