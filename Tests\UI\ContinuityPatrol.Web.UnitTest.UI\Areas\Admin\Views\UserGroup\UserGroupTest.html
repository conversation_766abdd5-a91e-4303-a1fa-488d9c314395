<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>🧪 User Group Unit Test</title>

    <!-- CSS -->
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.19.4.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css" />


    <!-- JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sinon.js/15.2.0/sinon.min.js"></script>
    <script src="https://code.jquery.com/qunit/qunit-2.19.4.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>

    <!-- Mock global functions before loading UserGroup.js -->
    <script>
        // Global variables
        window.RootUrl = "/";
        window.userList = [];
        window.Userlist = [];
        window.UsersNameList = [];
        window.userGroupId = '';
        window.userGroupData = '';
        window.selectedValues = [];

        // Mock notification functions
        window.errorNotification = function (result) {
            console.log('Error notification:', result);
        };

        window.OnError = function (xhr) {
            console.log('OnError:', xhr);
        };

        // Mock validation functions
        window.SpecialCharValidate = function (val) {
            return Promise.resolve(true);
        };

        window.OnlyNumericsValidate = function (val) {
            return Promise.resolve(true);
        };

        window.ShouldNotBeginWithUnderScore = function (val) {
            return Promise.resolve(true);
        };

        window.ShouldNotBeginWithSpace = function (val) {
            return Promise.resolve(true);
        };

        window.ShouldNotBeginWithNumber = function (val) {
            return Promise.resolve(true);
        };

        window.minMaxlength = function (val) {
            return Promise.resolve(true);
        };

        window.SpaceWithUnderScore = function () { return Promise.resolve(true); };
        window.ShouldNotEndWithUnderScore = function () { return Promise.resolve(true); };
        window.ShouldNotEndWithSpace = function () { return Promise.resolve(true); };
        window.MultiUnderScoreRegex = function () { return Promise.resolve(true); };
        window.SpaceAndUnderScoreRegex = function () { return Promise.resolve(true); };
        window.secondChar = function () { return Promise.resolve(true); };

        window.CommonValidation = function (errorElement, results) {
            return Promise.resolve(true);
        };

        window.GetAsync = function (url, data) {
            return Promise.resolve(false);
        };

        window.IsGroupNameExist = function (url, data, onError) {
            return Promise.resolve(false);
        };

        window.commonDebounce = function (func, delay) {
            return func;
        };

        window.sanitizeContainer = function () { };
        window.clearInputFields = function () { };
        window.validateDropDown = function () { return true; };
        window.validateDescription = function () { return Promise.resolve(true); };
        window.validateNames = function () { return Promise.resolve(true); };
    </script>
    <script src="/js/Common/common.js"></script>

    <!-- Original UserGroup.js file -->
    <script src="/js/Admin/UserManagement/User Group/UserGroup.js"></script>
    <script src="/js/Admin/UserManagement/User Group/UserGroupTest.js"></script>
</head>
<body>
    <h1 class="text-center">🧪 User Group Unit Test</h1>
    <div id="qunit"></div>
    <div id="qunit-fixture"></div>
</body>
</html>
