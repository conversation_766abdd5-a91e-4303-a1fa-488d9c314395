using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class BiaRulesRepository : BaseRepository<BiaRules>, IBiaRulesRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public BiaRulesRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public async Task<BiaRules> GetBiaRulesByEntityIdAndType(string entityId, string type)
    {
        return await _dbContext.BiaImpacts
            .AsNoTracking()
            .Active()
            .FirstOrDefaultAsync(x => x.EntityId == entityId && x.Type == type);
    }
}
