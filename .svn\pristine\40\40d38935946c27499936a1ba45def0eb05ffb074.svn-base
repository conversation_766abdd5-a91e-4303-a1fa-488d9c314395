﻿using ContinuityPatrol.Application.Features.Workflow.Events.Draft;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Workflow.Commands.Draft;

public class UpdateWorkflowIsDraftCommandHandler:IRequestHandler<UpdateWorkflowIsDraftCommand,UpdateWorkflowIsDraftResponse>
{
    private readonly IWorkflowRepository _workflowRepository;
    private readonly ILoadBalancerRepository _loadBalancerRepository;
    private readonly IWindowsService _windowsService;
    private readonly IJobScheduler _client;
    private readonly IPublisher _publisher;
    public UpdateWorkflowIsDraftCommandHandler(IWorkflowRepository workflowRepository,IWindowsService windowsService,ILoadBalancerRepository loadBalancerRepository,
IJobScheduler client,IPublisher publisher)
    {
        _workflowRepository = workflowRepository;
        _windowsService = windowsService;
        _loadBalancerRepository = loadBalancerRepository;
        _client = client;
        _publisher = publisher;
    }

    public async Task<UpdateWorkflowIsDraftResponse> Handle(UpdateWorkflowIsDraftCommand request, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "Workflow Id");

        var eventToUpdate=await _workflowRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Workflow), request.Id);

       eventToUpdate.IsDraft = request.IsDraft;

        var nodeConfig =
                await _loadBalancerRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString())
                ?? await _loadBalancerRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(),
                    ServiceType.LoadBalancer.ToString());

        if (nodeConfig is null) throw new InvalidException("LoadBalancer not configured!.");

        var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

        var url = UrlHelper.GenerateMonitorServiceUrl(baseUrl, eventToUpdate.ReferenceId);

             var monitorResponse = await _windowsService.CheckWindowsService(url);

        if (!monitorResponse.Success) throw new WindowServiceException(monitorResponse.InActiveNodes, ServiceType.Monitor.ToString(), monitorResponse.Message);

        await _client.ScheduleJob(eventToUpdate.ReferenceId, new Dictionary<string, string> { ["url"] = url });

        await _workflowRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateWorkflowIsDraftResponse
        {
            WorkflowId = request.Id,
            Message = $"workflow {eventToUpdate.Name} IsDraft Updated successfully."
        };

        await _publisher.Publish(new UpdateWorkflowDraftEvent { WorkflowId=eventToUpdate.ReferenceId,WorkflowName=eventToUpdate.Name,IsDraft=request.IsDraft},cancellationToken);

        return response;
    }
}
