using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowProfileInfoFixture : IDisposable
{
            public List<WorkflowProfileInfo> WorkflowProfileInfoPaginationList { get; set; }
            public List<WorkflowProfileInfo> WorkflowProfileInfoList { get; set; }
            public WorkflowProfileInfo WorkflowProfileInfoDto { get; set; }

            public const string CompanyId = "COMPANY_123";

            public ApplicationDbContext DbContext { get; private set; }

            public WorkflowProfileInfoFixture()
            {
                var fixture = new Fixture();

                WorkflowProfileInfoList = fixture.Create<List<WorkflowProfileInfo>>();

                WorkflowProfileInfoPaginationList = fixture.CreateMany<WorkflowProfileInfo>(20).ToList();

                WorkflowProfileInfoPaginationList.ForEach(x => x.CompanyId = CompanyId);

                WorkflowProfileInfoPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
                WorkflowProfileInfoPaginationList.ForEach(x => x.ProfileId = Guid.NewGuid().ToString());

                WorkflowProfileInfoList.ForEach(x => x.CompanyId = CompanyId);
                WorkflowProfileInfoList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
                WorkflowProfileInfoList.ForEach(x => x.ProfileId = Guid.NewGuid().ToString());

                WorkflowProfileInfoDto = fixture.Create<WorkflowProfileInfo>();

                WorkflowProfileInfoDto.CompanyId = CompanyId;
                WorkflowProfileInfoDto.ReferenceId = Guid.NewGuid().ToString();
                WorkflowProfileInfoDto.ProfileId = Guid.NewGuid().ToString();

                DbContext = DbContextFactory.CreateInMemoryDbContext();
            }

            public void Dispose()
            {
                DbContext?.Dispose();
        }
}
