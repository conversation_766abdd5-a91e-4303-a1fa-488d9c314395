﻿using ContinuityPatrol.Application.Features.Report.Queries.BulkImportReport;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.XtraCharts;
using Newtonsoft.Json;
using System.Drawing;
using DataTable = System.Data.DataTable;
using Series = DevExpress.XtraCharts.Series;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class BulkImportReport : DevExpress.XtraReports.UI.XtraReport
    {
        private readonly ILogger<PreBuildReportController> _logger;
        public GetBulkImportReportVm bulkImportReport = new GetBulkImportReportVm();
        public BulkImportReport(string data)
        {
            try
            {
                bulkImportReport = JsonConvert.DeserializeObject<GetBulkImportReportVm>(data);
                var report = bulkImportReport.BulkImportOperationGroupListVms;
                var imagePaths = new Dictionary<string, string>
                        {
                            {"success", "wwwroot/img/Drdrill_Icons/tick.png"},
                            {"error", "wwwroot/img/Drdrill_Icons/error.png"},
                            {"running", "wwwroot/img/BulkImport_Images/running.png"},
                            {"server", "wwwroot/img/Drdrill_Icons/server.png"},
                            //{"database", "wwwroot/img/Drdrill_Icons/database.png"},
                            //{"replication", "wwwroot/img/License_Report/replication_Lic_Black.png"},
                            //{"infraobject", "wwwroot/img/BulkImport_Images/infraobject.png"},
                            //{"workflow", "wwwroot/img/BulkImport_Images/Workflow.png"},
                             {"database", "wwwroot/img/BulkImport_Images/database.png"},
                            {"replication", "wwwroot/img/BulkImport_Images/replication.png"},
                            {"infraobject", "wwwroot/img/BulkImport_Images/infraobject.png"},
                            {"workflow", "wwwroot/img/BulkImport_Images/Workflow.png"},
                        };
                var Default = "wwwroot/img/Drdrill_Icons/abot.png";
                InitializeComponent();
                xrLabel51.Text = bulkImportReport.TotalInfra.ToString();
                xrLabel5.Text = bulkImportReport.TotalInfra.ToString();
                xrLabel8.Text = bulkImportReport.SuccessInfra.ToString();
                xrLabel9.Text = bulkImportReport.RunningInfra.ToString();
                xrLabel6.Text = bulkImportReport.FailureInfra.ToString();

                xrTable2.BeforePrint += (sender, e) =>
                {
                    var table = (XRTable)sender;
                    table.Rows.Clear();
                    table.WidthF = 1071F;
                    xrTable2.WidthF = 1071F;
                    XRTableRow dataRow = new XRTableRow();
                    int serialNO = 0;

                    foreach (var infra in report)
                    {
                        serialNO++;

                        var infraName = infra.InfraObjectName;
                        // string statusImage = "Success";
                        string[] parts = infra.ProgressStatus.Split('/');

                        //if (parts.Length == 2 && int.TryParse(parts[0], out int balanceAction) && int.TryParse(parts[1], out int totalAction))
                        //{
                        //    if (balanceAction.Equals(totalAction) && (infra.Status.Contains("Success") || infra.Status.Contains("Completed")))
                        //    {
                        //        statusImage = "Success";
                        //    }
                        //    if (balanceAction != totalAction && (infra.Status.Contains("Success") || infra.Status.Contains("Next")))
                        //    {
                        //        statusImage = "Running";
                        //    }
                        //    if (infra.Status.Contains("Error"))
                        //    {
                        //        statusImage = "Error";
                        //    }
                        //}
                        XRPictureBox statusImageBox = new XRPictureBox();
                        statusImageBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        statusImageBox.SizeF = new System.Drawing.SizeF(20F, 22F);

                        XRTableRow infraRow = new XRTableRow();
                        infraRow.WidthF = 1071F;
                        XRTableRow emptyRow = new XRTableRow();
                        XRTable childTable = new XRTable();
                        childTable.WidthF = 766F;
                        infraRow.Cells.Add(new XRTableCell { Text = serialNO.ToString(), WidthF = 52F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                        infraRow.Cells.Add(new XRTableCell { Text = infraName.ToString(), WidthF = 190F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });

                        foreach (var action in infra.BulkImportActionResultListVms)
                        {
                            string imagePath = imagePaths.FirstOrDefault(pair => action.EntityType?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;
                            string EntityimagePath = imagePaths.FirstOrDefault(pair => action.Status?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;
                            XRPictureBox pictureBox = new XRPictureBox();
                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox.SizeF = new System.Drawing.SizeF(30F, 32F);
                            pictureBox.Image = Image.FromFile(imagePath);
                            XRPictureBox EntitypictureBox = new XRPictureBox();
                            EntitypictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            EntitypictureBox.SizeF = new System.Drawing.SizeF(30F, 32F);
                            EntitypictureBox.Image = Image.FromFile(EntityimagePath);
                            XRTableRow childRow = new XRTableRow();
                            childRow.WidthF = 1479F;
                            var Totaltime = GetTotalTime(action.StartTime.ToString(), action.EndTime.ToString());
                            childRow.Cells.Add(new XRTableCell { Controls = { pictureBox }, WidthF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });// Add pictureBox with if;
                            childRow.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(action.EntityType) ? action.EntityType.TrimStart().ToString() : "-", HeightF = 40F, WidthF = 90F, CanGrow = true, WordWrap = true, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childRow.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(action.EntityName) ? action.EntityName.ToString() : "-", WidthF = 150F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childRow.Cells.Add(new XRTableCell { Text = action.StartTime.ToString(), WidthF = 80F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childRow.Cells.Add(new XRTableCell { Text = action.EndTime.ToString(), WidthF = 80F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childRow.Cells.Add(new XRTableCell { Text = Totaltime.ToString(), WidthF = 80F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childRow.Cells.Add(new XRTableCell { Controls = { EntitypictureBox }, WidthF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });// Add pictureBox with if;
                            childRow.Cells.Add(new XRTableCell { Text = !string.IsNullOrEmpty(action.Status) ? action.Status.ToString() : "-", WidthF = 80F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childTable.Rows.Add(childRow);

                        }
                        if (infra.BulkImportActionResultListVms.Count == 0)
                        {
                            XRTableRow childRow = new XRTableRow();
                            childRow.WidthF = 1479F;
                            childRow.Cells.Add(new XRTableCell { Text = "  -", WidthF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childRow.Cells.Add(new XRTableCell { Text = "", HeightF = 40F, WidthF = 90F, CanGrow = true, WordWrap = true, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childRow.Cells.Add(new XRTableCell { Text = "-", WidthF = 150F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childRow.Cells.Add(new XRTableCell { Text = "-", WidthF = 80F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childRow.Cells.Add(new XRTableCell { Text = "-", WidthF = 80F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childRow.Cells.Add(new XRTableCell { Text = "-", WidthF = 80F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childRow.Cells.Add(new XRTableCell { Text = "  -", WidthF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childRow.Cells.Add(new XRTableCell { Text = "", WidthF = 80F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            childTable.Rows.Add(childRow);
                        }
                        infraRow.Cells.Add(new XRTableCell { Controls = { childTable }, WidthF = 766F, CanGrow = true, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6) });
                        table.Rows.Add(infraRow);
                    }
                    // Assuming 'dataGridView' is your DataGridView and 'table' is your DataTable
                    for (int i = 0; i < table.Rows.Count; i++)
                    {
                        if (i % 2 != 0) // Check if the row index is even
                        {
                            table.Rows[i].BackColor = System.Drawing.Color.FromArgb(243, 245, 248); // Set your desired color
                        }
                    }
                };

                // ClientCompanyLogo();
                // tableCell8.BeforePrint += tableCell_SerialNumber_BeforePrint;
            }
            catch (Exception ex)
            {
                var error = ex.Message.ToString();
                _logger.LogError("Error occured in BulkImport Report. The error message : " + error);
            }
        }
        private int serialNumber = 1;

        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + bulkImportReport.ReportGeneratedBy.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the BulkImport Report's User name. The error message : " + ex.Message); throw; }
        }

        private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;

            cell.Text = serialNumber.ToString();
            serialNumber++;
        }
        private static TimeSpan GetTotalTime(string startTime, string endTime)
        {
            var start = DateTime.Parse(startTime);
            var end = DateTime.Parse(endTime);
            var ts = end - start;
            return ts;
        }

        private DataTable CreateChartData(Int64 rowup, Int64 rowdown, Int64 rowmain, Int64 NodataFound)
        {
            // Create an empty table.
            DataTable table = new DataTable("Table1");

            // Add two columns to the table.
            table.Columns.Add("Argument", typeof(string));
            table.Columns.Add("Value", typeof(Int64));

            // Add data rows to the table.
            Random rnd = new Random();

            table.Rows.Add("Success", rowup);
            table.Rows.Add("Failed", rowdown);
            table.Rows.Add("Running", rowmain);
            table.Rows.Add("No Data Found", NodataFound);


            return table;
        }

        private void xrChart2_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                Int64 valueSuccess = bulkImportReport.SuccessInfra;
                Int64 valueError = bulkImportReport.FailureInfra;
                Int64 valueRunning = bulkImportReport.RunningInfra;
                Int64 NodataFound = 0;

                //foreach (var row in Reporter)
                //{
                //valueup = valueup + row.Up;
                //valuedown = valuedown + row.Down;
                //valueMain = valueMain + row.Maintenance;
                // }


                if (valueSuccess == 0 && valueError == 0 && valueRunning == 0)
                {
                    Series series1 = new Series("Series1", ViewType.Doughnut);
                    xrChart2.Series.Add(series1);
                    NodataFound = 1;
                    series1.DataSource = CreateChartData(valueSuccess, valueError, valueRunning, NodataFound);
                    DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                    doughnutSeriesView.MinAllowedSizePercentage = 75D;
                    series1.View = doughnutSeriesView;
                    series1.ArgumentScaleType = ScaleType.Auto;
                    series1.ArgumentDataMember = "Argument";
                    series1.ValueScaleType = ScaleType.Numerical;
                    series1.ValueDataMembers.AddRange(new string[] { "Value" });
                    series1.Label.TextPattern = "{A}";
                    series1.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                    xrChart2.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
                }
                else
                {
                    Series series = new Series("Series1", ViewType.Doughnut);
                    xrChart2.Series.Add(series);

                    series.DataSource = CreateChartData(valueSuccess, valueError, valueRunning, NodataFound);
                    DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                    doughnutSeriesView.MinAllowedSizePercentage = 75D;
                    series.View = doughnutSeriesView;
                    series.ArgumentScaleType = ScaleType.Auto;
                    series.ArgumentDataMember = "Argument";
                    series.ValueScaleType = ScaleType.Numerical;
                    series.ValueDataMembers.AddRange(new string[] { "Value" });
                    series.Label.TextPattern = "{A}\n{V}";
                    ((DoughnutSeriesLabel)series.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;
                    series.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                    xrChart2.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
                }

            }
            catch (Exception ex) { _logger.LogError("Error occured while display the BulkImport Report's chart data. The error message : " + ex.Message); throw; }
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the BulkImport Report's CP Version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the customer logo in BulkImport report" + ex.Message.ToString());
            }
        }
    }
}
