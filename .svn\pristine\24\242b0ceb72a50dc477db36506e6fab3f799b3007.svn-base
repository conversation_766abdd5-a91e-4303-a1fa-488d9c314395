using ContinuityPatrol.Domain.ViewModels.HacmpClusterModel;

namespace ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetList;

public class GetHacmpClusterListQueryHandler : IRequestHandler<GetHacmpClusterListQuery, List<HacmpClusterListVm>>
{
    private readonly IHacmpClusterRepository _hacmpClusterRepository;
    private readonly IMapper _mapper;

    public GetHacmpClusterListQueryHandler(IMapper mapper, IHacmpClusterRepository hacmpClusterRepository)
    {
        _mapper = mapper;
        _hacmpClusterRepository = hacmpClusterRepository;
    }

    public async Task<List<HacmpClusterListVm>> Handle(GetHacmpClusterListQuery request,
        CancellationToken cancellationToken)
    {
        var hacmpClusters = await _hacmpClusterRepository.ListAllAsync();

        if (hacmpClusters.Count <= 0) return new List<HacmpClusterListVm>();

        return _mapper.Map<List<HacmpClusterListVm>>(hacmpClusters);
    }
}