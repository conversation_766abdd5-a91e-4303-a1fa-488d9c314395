﻿using ContinuityPatrol.Application.Features.TableAccess.Commands.Create;
using ContinuityPatrol.Application.Features.TableAccess.Commands.Update;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetSchemaNameList;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetTableNameListBySchema;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class TableAccessService : BaseClient, ITableAccessService
{
    public TableAccessService(IConfiguration config, IAppCache cache, ILogger<TableAccessService> logger) : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateTableAccessCommand createTableAccessCommand)
    {
        var request = new RestRequest("api/v6/tableaccess", Method.Post);

        request.AddJsonBody(createTableAccessCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateTableAccessCommand updateTableAccessCommand)
    {
        var request = new RestRequest("api/v6/tableaccess", Method.Put);

        request.AddJsonBody(updateTableAccessCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string tableAccessId)
    {
        var request = new RestRequest($"api/v6/tableaccess/{tableAccessId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<PaginatedResult<TableAccessListVm>> GetTableAccessPaginatedList(GetTableAccessPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/tableaccess/paginated-list");

        return await Get<PaginatedResult<TableAccessListVm>>(request);
    }

    public async Task<bool> IsTableAccessNameExist(string tableAccessName, string? id)
    {
        var request = new RestRequest($"api/v6/tableaccess/name-exist?tableAccessName={tableAccessName}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<List<GetTableNameListBySchemaVm>> GetTableNamesBySchemaName(string schemaName)
    {
        var request = new RestRequest($"api/v6/tableaccess/tablenames?schemaName={schemaName}");

        return await Get<List<GetTableNameListBySchemaVm>>(request);
    }

    public async Task<List<SchemaNameListVm>> GetSchemaNames()
    {
        var request = new RestRequest("api/v6/tableaccess/schemanames");

        return await Get<List<SchemaNameListVm>>(request);
    }

    public async Task<List<TableAccessListVm>> GetAllTableAccesses()
    {
        var request = new RestRequest("api/v6/tableaccess");

        return await Get<List<TableAccessListVm>>(request);
    }
}