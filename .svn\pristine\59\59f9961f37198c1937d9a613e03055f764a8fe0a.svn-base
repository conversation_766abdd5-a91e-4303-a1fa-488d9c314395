﻿using ContinuityPatrol.Application.Features.FormType.Events.Delete;

namespace ContinuityPatrol.Application.Features.FormType.Commands.Delete;

public class DeleteFormTypeCommandHandler : IRequestHandler<DeleteFormTypeCommand, DeleteFormTypeResponse>
{
    private readonly IFormTypeRepository _formTypeRepository;
    private readonly IPublisher _publisher;

    public DeleteFormTypeCommandHandler(IFormTypeRepository formTypeRepository, IPublisher publisher)
    {
        _formTypeRepository = formTypeRepository;
        _publisher = publisher;
    }

    public async Task<DeleteFormTypeResponse> <PERSON>le(DeleteFormTypeCommand request, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "Form Type Id");
        var eventToDelete = await _formTypeRepository.GetFormTypeById(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.FormType),
            new NotFoundException(nameof(Domain.Entities.FormType), request.Id));

        eventToDelete.IsActive = false;

        await _formTypeRepository.UpdateAsync(eventToDelete);

        var response = new DeleteFormTypeResponse
        {
            Message = Message.Delete("Form Type", eventToDelete.FormTypeName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new FormTypeDeletedEvent { FormTypeName = eventToDelete.FormTypeName },
            cancellationToken);

        return response;
    }
}