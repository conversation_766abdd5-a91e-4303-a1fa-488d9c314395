﻿namespace ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPowerMax;

public class GetPowerMaxDetailsQueryHandler : IRequestHandler<GetPowerMaxDetailsQuery, List<PowerMaxDetailVm>>
{
    private readonly IPowerMaxMonitorStatusRepository _powerMaxMonitorStatusRepository;

    public GetPowerMaxDetailsQueryHandler(IPowerMaxMonitorStatusRepository powerMaxMonitorStatusRepository)
    {
        _powerMaxMonitorStatusRepository = powerMaxMonitorStatusRepository;
    }
    public async Task<List<PowerMaxDetailVm>> Handle(GetPowerMaxDetailsQuery request, CancellationToken cancellationToken)
    {
        var powerMaxMonitorStatus =await _powerMaxMonitorStatusRepository.GetPowerMaxMonitorStatusByName(request.Name, request.IsSnap);
        
        return powerMaxMonitorStatus;
    }
}
