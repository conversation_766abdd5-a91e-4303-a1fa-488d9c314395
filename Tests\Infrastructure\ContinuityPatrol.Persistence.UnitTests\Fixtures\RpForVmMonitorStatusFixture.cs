using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RpForVmMonitorStatusFixture : IDisposable
{
    public List<RpForVmMonitorStatus> RpForVmMonitorStatusPaginationList { get; set; }
    public List<RpForVmMonitorStatus> RpForVmMonitorStatusList { get; set; }
    public RpForVmMonitorStatus RpForVmMonitorStatusDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "INFRA_OBJ_123";
    public const string WorkflowId = "WORKFLOW_123";
    public const string Type = "RpForVm";
    public const string UserId = "USER_123";

    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public RpForVmMonitorStatusFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<RpForVmMonitorStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
        );

        RpForVmMonitorStatusList = _fixture.CreateMany<RpForVmMonitorStatus>(5).ToList();
        RpForVmMonitorStatusPaginationList = _fixture.CreateMany<RpForVmMonitorStatus>(20).ToList();
        RpForVmMonitorStatusDto = _fixture.Create<RpForVmMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public RpForVmMonitorStatus CreateRpForVmMonitorStatusWithInfraObjectId(string infraObjectId)
    {
        return _fixture.Build<RpForVmMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, Type)
            .With(x => x.InfraObjectId, infraObjectId)
            .With(x => x.InfraObjectName, _fixture.Create<string>())
            .With(x => x.WorkflowId, WorkflowId)
            .With(x => x.WorkflowName, _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, _fixture.Create<string>())
            .With(x => x.Threshold, _fixture.Create<string>())
            .With(x => x.DataLagValue, _fixture.Create<string>())
            .With(x => x.Properties, _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RpForVmMonitorStatus CreateRpForVmMonitorStatusWithType(string type)
    {
        return _fixture.Build<RpForVmMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type)
            .With(x => x.InfraObjectId, InfraObjectId)
            .With(x => x.InfraObjectName, _fixture.Create<string>())
            .With(x => x.WorkflowId, WorkflowId)
            .With(x => x.WorkflowName, _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, _fixture.Create<string>())
            .With(x => x.Threshold, _fixture.Create<string>())
            .With(x => x.DataLagValue, _fixture.Create<string>())
            .With(x => x.Properties, _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RpForVmMonitorStatus CreateRpForVmMonitorStatusWithWorkflowId(string workflowId)
    {
        return _fixture.Build<RpForVmMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, Type)
            .With(x => x.InfraObjectId, InfraObjectId)
            .With(x => x.InfraObjectName, _fixture.Create<string>())
            .With(x => x.WorkflowId, workflowId)
            .With(x => x.WorkflowName, _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, _fixture.Create<string>())
            .With(x => x.Threshold, _fixture.Create<string>())
            .With(x => x.DataLagValue, _fixture.Create<string>())
            .With(x => x.Properties, _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RpForVmMonitorStatus CreateRpForVmMonitorStatusWithProperties(
        string type = null,
        string infraObjectId = null,
        string infraObjectName = null,
        string workflowId = null,
        string workflowName = null,
        string configuredRPO = null,
        string threshold = null,
        string dataLagValue = null,
        string properties = null)
    {
        return _fixture.Build<RpForVmMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.InfraObjectName, infraObjectName ?? _fixture.Create<string>())
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.WorkflowName, workflowName ?? _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, configuredRPO ?? _fixture.Create<string>())
            .With(x => x.Threshold, threshold ?? _fixture.Create<string>())
            .With(x => x.DataLagValue, dataLagValue ?? _fixture.Create<string>())
            .With(x => x.Properties, properties ?? _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RpForVmMonitorStatus CreateRpForVmMonitorStatusWithLongProperties(int propertyLength)
    {
        var longProperty = new string('A', propertyLength);
        return CreateRpForVmMonitorStatusWithProperties(properties: longProperty);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "RpForVm", "RecoverPoint", "VMware", "VirtualMachine" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
        public static readonly string[] SpecialCharacterTypes = { "Type@#$%", "Type with spaces", "Type_with_underscores", "Type-with-dashes" };
        
        public static readonly string ValidGuid = Guid.NewGuid().ToString();
        public static readonly string InvalidGuid = "INVALID_GUID";
        public static readonly string EmptyGuid = Guid.Empty.ToString();
    }
}
