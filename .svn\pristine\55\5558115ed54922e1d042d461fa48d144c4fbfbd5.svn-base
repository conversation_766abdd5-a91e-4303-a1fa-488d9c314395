﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;
using System.Linq.Expressions;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IAlertMasterRepository : IRepository<AlertMaster>
{
    Task<List<AlertMaster>> GetAlertMasterNames();
    Task<List<AlertMaster>> GetAlertMasterByAlertId(string alertId);
    Task<List<AlertMaster>> GetAlertMasterByAlertName(string alertName);
    IQueryable<AlertMaster> GetPaginatedByAlertName(string alertName);
    IQueryable<AlertMaster> GetPaginatedByAlertPriority(string alertPriority);
    IQueryable<AlertMaster> GetPaginatedByAlertNameAndPriority(string alertName, string alertPriority);
    Task<bool> IsNameExist(string name, string id);
    Task<bool> IsAlertIdExist(string id);
    Task<PaginatedResult<AlertMaster>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<AlertMaster> specification, Expression<Func<AlertMaster, bool>> expression, string sortColumn, string sortOrder);
}