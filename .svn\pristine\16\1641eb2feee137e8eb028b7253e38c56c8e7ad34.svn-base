﻿using ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ReplicationMaster.Commands;

public class CreateReplicationMasterTests : IClassFixture<ReplicationMasterFixture>
{
    private readonly ReplicationMasterFixture _replicationMasterFixture;

    private readonly Mock<IReplicationMasterRepository> _mockReplicationMasterRepository;

    private readonly CreateReplicationMasterCommandHandler _handler;

    public CreateReplicationMasterTests(ReplicationMasterFixture replicationMasterFixture)
    {
        _replicationMasterFixture = replicationMasterFixture;

        _mockReplicationMasterRepository =
            ReplicationMasterRepositoryMocks.CreateReplicationMasterRepository(_replicationMasterFixture
                .ReplicationMasters);

        _handler = new CreateReplicationMasterCommandHandler(_mockReplicationMasterRepository.Object,
            _replicationMasterFixture.Mapper);
    }

    [Fact]

    public async Task Handle_IncreaseReplicationMasterCount_When_ReplicationMasterCreated()
    {
        await _handler.Handle(_replicationMasterFixture.CreateReplicationMasterCommand, CancellationToken.None);

        var allCategories = await _mockReplicationMasterRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_replicationMasterFixture.ReplicationMasters.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateReplicationMasterResponse_When_ReplicationMasterCreated()
    {
        var result = await _handler.Handle(_replicationMasterFixture.CreateReplicationMasterCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateReplicationMasterResponse));

        result.ReplicationMasterId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_replicationMasterFixture.CreateReplicationMasterCommand, CancellationToken.None);

        _mockReplicationMasterRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.ReplicationMaster>()), Times.Once);
    }
}