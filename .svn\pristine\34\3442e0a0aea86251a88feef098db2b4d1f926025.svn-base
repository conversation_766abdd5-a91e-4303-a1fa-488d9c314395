﻿using ContinuityPatrol.Application.Features.UserLogin.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.UserLogin.Commands;

public class CreateUserLoginTests : IClassFixture<UserLoginFixture>
{
    private readonly UserLoginFixture _userLoginFixture;

    private readonly Mock<IUserLoginRepository> _mockUserLoginRepository = new();

    private readonly Mock<IMapper> _mockmapper = new() ;

    private readonly CreateUserLoginCommandHandler _handler;
    

    public CreateUserLoginTests(UserLoginFixture userLoginFixture)
    {
        _userLoginFixture = userLoginFixture;

        _mockUserLoginRepository = UserLoginRepositoryMocks.CreateUserLoginRepository(_userLoginFixture.UserLogins);


        _handler = new CreateUserLoginCommandHandler(_mockmapper.Object , _mockUserLoginRepository.Object);
    }

    [Fact]
    public async Task Handle_Should_Increase_Count_When_AddValid_UserLogin()
    {
        var createUserloginCmd = new Fixture().Create<CreateUserLoginCommand>();
        int lstid = 12;
        createUserloginCmd.LastAlertId = lstid.ToString();

        var userlogin = new Domain.Entities.UserLogin();

        _mockmapper.Setup(x => x.Map<Domain.Entities.UserLogin>(createUserloginCmd)).Returns(userlogin);

        _mockUserLoginRepository.Setup(dp => dp.AddAsync(userlogin)).ReturnsAsync(userlogin);
        await _handler.Handle(createUserloginCmd, CancellationToken.None);

        var allCategories = await _mockUserLoginRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_userLoginFixture.UserLogins.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulUserLoginResponse_When_AddValidUserLogin()
    {
        var createUserloginCmd = new Fixture().Create<CreateUserLoginCommand>();
        int lstid = 12;
        createUserloginCmd.LastAlertId= lstid.ToString();

        var userlogin = new Domain.Entities.UserLogin();

        _mockmapper.Setup(x => x.Map<Domain.Entities.UserLogin>(createUserloginCmd)).Returns(userlogin);

        _mockUserLoginRepository.Setup(dp=>dp.AddAsync(userlogin)).ReturnsAsync(userlogin);

        var result = await _handler.Handle(createUserloginCmd, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateUserLoginResponse));

        result.UserId.ShouldBe(0);

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        var createUserloginCmd = new Fixture().Create<CreateUserLoginCommand>();
        int lstid = 12;
        createUserloginCmd.LastAlertId = lstid.ToString();

        var userlogin = new Domain.Entities.UserLogin();

        _mockmapper.Setup(x => x.Map<Domain.Entities.UserLogin>(createUserloginCmd)).Returns(userlogin);

        _mockUserLoginRepository.Setup(dp => dp.AddAsync(userlogin)).ReturnsAsync(userlogin);
        await _handler.Handle(createUserloginCmd, CancellationToken.None);

        _mockUserLoginRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserLogin>()), Times.Once);
    }
}