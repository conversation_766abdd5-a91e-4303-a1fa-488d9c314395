﻿using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;

namespace ContinuityPatrol.Shared.Tests.Infrastructure;

public static class DbContextFactory
{
    public static ApplicationDbContext CreateInMemoryDbContext(string dbName = null)
    {

        dbName ??= Guid.NewGuid().ToString();

        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: dbName)
            .Options;

        return new ApplicationDbContext(options, GetMockUserService());
    }


    public static ILoggedInUserService GetMockUserService()
    {

        var assigndinfra = System.Text.Json.JsonSerializer.Serialize(GetAssignedEntityForIsAllinfraTrue());
        var mock = new Mock<ILoggedInUserService>();
        mock.Setup(x => x.CompanyId).Returns("COMPANY_123");
        mock.Setup(x => x.UserId).Returns("USER_456");
        mock.Setup(x => x.IsParent).Returns(true);
        mock.Setup(x => x.IsAllInfra).Returns(true);
        mock.Setup(x => x.IsAuthenticated).Returns(true);
        mock.Setup(x => x.AssignedInfras).Returns(assigndinfra);

        return mock.Object;
    }
    public static ILoggedInUserService GetMockLoggedInUserIsNotParent()
    {
        var mock = new Mock<ILoggedInUserService>();

        var assigndinfra = System.Text.Json.JsonSerializer.Serialize(GetAssignedEntityForIsAllinfraFalse());
        mock.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mock.Setup(x => x.UserId).Returns("USER_Child1245");
        mock.Setup(x => x.IsParent).Returns(false);
        mock.Setup(x => x.AssignedInfras).Returns(assigndinfra);
        mock.Setup(x => x.IsAuthenticated).Returns(true);
        return mock.Object;
    }

    public static AssignedEntity GetAssignedEntityForIsAllinfraFalse()
    {
        return new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
        {
            new AssignedBusinessServices
            {
                Id = "c9b3cd51-f688-4667-be33-46f82b7086fa",
                Name = "Service 1",
                IsAll = false,
                IsPartial = false,
                AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                {
                    new AssignedBusinessFunctions
                    {
                        Id = "1acf2f57-7a9c-4a56-a3bf-26db53117e93",
                        Name = "Function 1",
                        IsAll = false,
                        IsPartial = false,
                        AssignedInfraObjects = new List<AssignedInfraObjects>
                        {
                            new AssignedInfraObjects { Id = "70bb97c9-1193-4e86-98ab-bebc88fb438c", Name = "Infra 1", IsSelected = true },
                        }
                    }
                }
            }
        }
        };
    }

    public static AssignedEntity GetAssignedEntityForIsAllinfraTrue()
    {
        return new AssignedEntity
        {
            IsAll = true,
            AssignedBusinessServices = new List<AssignedBusinessServices>
        {
            new AssignedBusinessServices
            {
                Id = "c9b3cd51-f688-4667-be33-46f82b7086fa",
                Name = "Service1",
                IsAll = true,
                IsPartial = true,
                AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                {
                    new AssignedBusinessFunctions
                    {
                        Id = "1acf2f57-7a9c-4a56-a3bf-26db53117e93",
                        Name = "Function1",
                        IsAll = true,
                        IsPartial = true,
                        AssignedInfraObjects = new List<AssignedInfraObjects>
                        {
                            new AssignedInfraObjects { Id = "70bb97c9-1193-4e86-98ab-bebc88fb438c", Name = "Infra 1", IsSelected = true },
                            new AssignedInfraObjects { Id = "70bb97c9-1193-4e86-98ab-bebc88fb439c", Name = "Infra 2", IsSelected = true }
                        }
                    }
                }
            }
        }
        };
    }



}