﻿using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReportByBusinessService;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.XtraCharts;
using Newtonsoft.Json;
using Org.BouncyCastle.Tls;
using System.Data;
using System.Drawing;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class LicenseUtilizationOperationalServiceReport : DevExpress.XtraReports.UI.XtraReport
    {

        public List<BSAvailableCountViewVm> AvailableCountVm { get; set; }
        public List<BSUsedCountViewVm> UsedCountVm { get; set; }
        private readonly ILogger<PreBuildReportController> _logger;
        public LicenseReportByBusinessServiceReport licenseReport = new LicenseReportByBusinessServiceReport();
        public class CombinedCount
        {
            public int DatabaseCount { get; set; }
            public int ServerDbCount { get; set; }
            public int ReplicationCount { get; set; }
            public int StorageCount { get; set; }
            public int VirtualizationCount { get; set; }
            public int ApplicationCount { get; set; }
            public int DnsCount { get; set; }
            public int NetworkCount { get; set; }
            public int ThirdPartyCount { get; set; }
        }

        public static Int64 totals;
        public static Int64 usedcounts;
        public static Int64 remaincounts;
        public static Int64 totalcount;

        public LicenseUtilizationOperationalServiceReport(string data)
        {
            try
            {
                var reports = new LicenseUtilizationOperationalServiceXlsReport(data);
                licenseReport = JsonConvert.DeserializeObject<LicenseReportByBusinessServiceReport>(data);
                _logger = PreBuildReportController._logger;
                InitializeComponent();
                ClientCompanyLogo();
                this.DisplayName = "LicenseUtilization_OperationalService_Report_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");
                string GetValueOrNA(string values)
                {
                    if (string.IsNullOrEmpty(values)) return "-";
                    return string.Join(", ", values.Split(',')
                                                    .Select(value => string.IsNullOrEmpty(value.Trim()) || value.Trim() == "NA" ? "-" : value.Trim()));
                }
                var Licencereport = licenseReport.LicenseReportByBusinessServiceVms;
                var report = Licencereport.SelectMany(x => x.BusinessFunctionLicenseDto).ToList();
                HashSet<string> ServerpoName = new HashSet<string>();
                HashSet<string> DBpoName = new HashSet<string>();
                HashSet<string> ApppoName = new HashSet<string>();
                HashSet<string> ReppoName = new HashSet<string>();
                var imagePaths = new Dictionary<string, string>
                        {
                            {"mysql", "wwwroot/img/Drdrill_Icons/MySql.png"},
                            {"postgres", "wwwroot/img/Drdrill_Icons/postgres.png"},
                            {"mac", "wwwroot/img/Drdrill_Icons/mac_os.png"},
                            {"openshift", "wwwroot/img/License_Report/Linux-Openshift.png"},
                            {"linux", "wwwroot/img/Drdrill_Icons/linux.png"},
                            {"oraclerac", "wwwroot/img/Drdrill_Icons/oracle_rac.png"},
                            {"oracle", "wwwroot/img/Drdrill_Icons/oracle.png"},
                            {"sqlite", "wwwroot/img/Drdrill_Icons/sqlite.png"},
                            {"windows 2016", "wwwroot/img/Drdrill_Icons/Windows 2017.png"},
                            {"windows 2019", "wwwroot/img/Drdrill_Icons/windows-2019.png"},
                            {"windows", "wwwroot/img/Drdrill_Icons/windows-2019.png"},
                            {"solaris", "wwwroot/img/Drdrill_Icons/solaris.png"},
                            {"aix", "wwwroot/img/License_Report/AIX.png"},
                            {"azurestorageserver", "wwwroot/img/License_Report/azure.png"},
                            {"cloudazure", "wwwroot/img/License_Report/CloudAzure.png"},
                            {"azure", "wwwroot/img/License_Report/AzureDatabase_MSSQL_PaaS.png"},
                            {"as400", "wwwroot/img/License_Report/as400.png"},
                            {"redisdb", "wwwroot/img/License_Report/RedisDB.png"},
                            {"maxdb", "wwwroot/img/License_Report/MaxDB.png"},
                            {"mongodb", "wwwroot/img/License_Report/mongo-db.png"},
                            {"ibm", "wwwroot/img/License_Report/IBM-DB2.png"},
                            {"sql", "wwwroot/img/Drdrill_Icons/mssql.png"}
                        };
                var Default = "wwwroot/img/Drdrill_Icons/abot.png";
                int j = 0, rowIndex = 0;

                xrTable2.BeforePrint += (sender, e) =>
                {
                    if (ServerpoName.Contains(report[j].BusinessServiceName)) { return; }
                    else { ServerpoName.Add(report[j].BusinessServiceName); }

                    var table = (XRTable)sender;
                    table.Rows.Clear();

                    var server2 = report[j].ApplicationReportVms;
                    if (server2.Count > 0)
                    {
                        //Add Label
                        XRTableRow headerRow = new XRTableRow();
                        XRLabel label = new XRLabel();
                        label.Text = "Application License " + " (" + server2.Count + ")";
                        label.WidthF = 300F;
                        label.HeightF = 50F;
                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                        // Add picture box
                        XRPictureBox pictureBox1 = new XRPictureBox();
                        pictureBox1.Image = Image.FromFile("wwwroot/img/License_Report/Application_Lic_Black.png");
                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                        table.Rows.Add(headerRow);


                        XRTableRow serverHeader = new XRTableRow();
                        serverHeader.HeightF = 32F;
                        var serverHead2 = (XRTable)sender;
                        XRLabel appName = new XRLabel(); appName.Text = "  Server Name"; appName.WidthF = 229.36F; appName.HeightF = 32F;
                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                        serverHeader.Cells.Add(new XRTableCell { Controls = { appName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Server Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "PO Number", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        table.Rows.Add(serverHeader);

                        rowIndex = 0;

                        foreach (var serverName in server2)
                        {
                            try
                            {
                                var decrypt = SecurityHelper.Decrypt(serverName.PONumber);
                                serverName.PONumber = decrypt != "" ? decrypt : serverName.PONumber;
                            }
                            catch
                            {
                                serverName.PONumber = serverName.PONumber;
                            }

                            string imagePath = imagePaths.FirstOrDefault(pair => serverName.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                            XRTableRow dataRow = new XRTableRow();
                            XRPictureBox pictureBox = new XRPictureBox();
                            pictureBox.Image = Image.FromFile(imagePath);
                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                            dataRow.Cells.AddRange(new[]
                            {
             new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(serverName.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(serverName.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(serverName.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(serverName.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(serverName.PONumber), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
             });
                            bool isOddRow = rowIndex % 2 != 0;
                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                            table.Rows.Add(dataRow);
                            rowIndex++;
                        }
                    }

                    var serverdb = report[j].DatabaseReportVms;
                    if (serverdb.Count > 0)
                    {
                        //Add Label
                        XRTableRow headerRow = new XRTableRow();
                        XRLabel label = new XRLabel();
                        label.Text = "Database License " + " (" + serverdb.Count + ")";
                        label.WidthF = 300F;
                        label.HeightF = 50F;
                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                        // Add picture box
                        XRPictureBox pictureBox1 = new XRPictureBox();
                        pictureBox1.Image = Image.FromFile("wwwroot/img/Drdrill_Icons/database.png");
                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                        table.Rows.Add(headerRow);


                        XRTableRow serverHeader = new XRTableRow();
                        serverHeader.HeightF = 32F;
                        XRLabel database = new XRLabel(); database.Text = "  Database Name"; database.WidthF = 229.36F; database.HeightF = 32F;
                        var serverHead1 = (XRTable)sender;
                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                        serverHeader.Cells.Add(new XRTableCell { Controls = { database }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Database Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Database SID", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "PO Number", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        table.Rows.Add(serverHeader);

                        rowIndex = 0;

                        foreach (var serverName in serverdb)
                        {
                            try
                            {
                                var decrypt = SecurityHelper.Decrypt(serverName.PONumber);
                                serverName.PONumber = decrypt != "" ? decrypt : serverName.PONumber;
                            }
                            catch
                            {
                                serverName.PONumber = serverName.PONumber;
                            }
                            string imagePath = imagePaths.FirstOrDefault(pair => serverName.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                            XRTableRow dataRow = new XRTableRow();
                            XRPictureBox pictureBox = new XRPictureBox();
                            pictureBox.Image = Image.FromFile(imagePath);
                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                            XRLabel serverNam = new XRLabel(); serverNam.Text = "   " + serverName.EntityName ?? "  -"; serverNam.WidthF = 229.36F; serverNam.HeightF = 32F;

                            dataRow.Cells.AddRange(new[]
                            {
             new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(serverName.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(serverName.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(serverName.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = !string.IsNullOrEmpty(serverName.EntityField)? GetValueOrNA(serverName.EntityField.Split(",").ElementAtOrDefault(2)) : "-", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(serverName.PONumber), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
             });
                            bool isOddRow = rowIndex % 2 != 0;
                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                            table.Rows.Add(dataRow);
                            rowIndex++;
                        }
                    }

                    var server3 = report[j].ReplicationReportVms;
                    if (server3.Count > 0)
                    {
                        //Add Label
                        XRTableRow headerRow = new XRTableRow();
                        XRLabel label = new XRLabel();
                        label.Text = "Replication License " + " (" + server3.Count + ")";
                        label.WidthF = 300F;
                        label.HeightF = 50F;
                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                        // Add picture box
                        XRPictureBox pictureBox1 = new XRPictureBox();
                        pictureBox1.Image = Image.FromFile("wwwroot/img/License_Report/replication_Lic_Black.png");
                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                        table.Rows.Add(headerRow);


                        XRTableRow serverHeader = new XRTableRow();
                        XRLabel repName = new XRLabel(); repName.Text = "  Replication Name"; repName.WidthF = 229.36F; repName.HeightF = 32F;
                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                        serverHeader.Cells.Add(new XRTableCell { Controls = { repName }, WidthF = 231.15F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Replication Type", WidthF = 288.61F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Site Name", WidthF = 231.32F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "PO Number", WidthF = 253.08F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });

                        table.Rows.Add(serverHeader);

                        rowIndex = 0;

                        foreach (var serverName in server3)
                        {
                            try
                            {
                                var decrypt = SecurityHelper.Decrypt(serverName.PONumber);
                                serverName.PONumber = decrypt != "" ? decrypt : serverName.PONumber;
                            }
                            catch
                            {
                                serverName.PONumber = serverName.PONumber;
                            }
                            string imagePath = imagePaths.FirstOrDefault(pair => serverName.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                            XRTableRow dataRow = new XRTableRow();
                            XRPictureBox pictureBox = new XRPictureBox();
                            pictureBox.Image = Image.FromFile(imagePath);
                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                            dataRow.Cells.AddRange(new[]
                            {
                    new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                    new XRTableCell { Text = GetValueOrNA(serverName.EntityName), WidthF = 222.15F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                    new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                    new XRTableCell { Text = GetValueOrNA(serverName.Type), WidthF = 268.61F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                    new XRTableCell { Text = GetValueOrNA(serverName.EntityField), WidthF = 231.32F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                    new XRTableCell { Text = GetValueOrNA(serverName.PONumber), WidthF = 253.08F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                    });
                            bool isOddRow = rowIndex % 2 != 0;
                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                            table.Rows.Add(dataRow);
                            rowIndex++;
                        }
                    }

                    var storage = report[j].StorageReportVms;
                    if (storage.Count > 0)
                    {
                        //Add Label
                        XRTableRow headerRow = new XRTableRow();
                        XRLabel label = new XRLabel();
                        label.Text = "Storage License " + " (" + storage.Count + ")";
                        label.WidthF = 300F;
                        label.HeightF = 50F;
                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                        // Add picture box
                        XRPictureBox pictureBox1 = new XRPictureBox();
                        pictureBox1.Image = Image.FromFile("wwwroot/img/Drdrill_Icons/Storage.png");
                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                        table.Rows.Add(headerRow);


                        XRTableRow serverHeader = new XRTableRow();
                        serverHeader.HeightF = 32F;
                        var serverHead2 = (XRTable)sender;
                        XRLabel storageName = new XRLabel(); storageName.Text = "  Server Name"; storageName.WidthF = 229.36F; storageName.HeightF = 32F;
                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                        serverHeader.Cells.Add(new XRTableCell { Controls = { storageName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Server Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "PO Number", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        table.Rows.Add(serverHeader);

                        rowIndex = 0;

                        foreach (var storageServer in storage)
                        {
                            try
                            {
                                var decrypt = SecurityHelper.Decrypt(storageServer.PONumber);
                                storageServer.PONumber = decrypt != "" ? decrypt : storageServer.PONumber;
                            }
                            catch
                            {
                                storageServer.PONumber = storageServer.PONumber;
                            }
                            string imagePath = imagePaths.FirstOrDefault(pair => storageServer.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                            XRTableRow dataRow = new XRTableRow();
                            XRPictureBox pictureBox = new XRPictureBox();
                            pictureBox.Image = Image.FromFile(imagePath);
                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                            dataRow.Cells.AddRange(new[]
                            {
             new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(storageServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(storageServer.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(storageServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(storageServer.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(storageServer.PONumber), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
             });
                            bool isOddRow = rowIndex % 2 != 0;
                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                            table.Rows.Add(dataRow);
                            rowIndex++;
                        }
                    }

                    var network = report[j].NetworkReportVms;
                    if (network.Count > 0)
                    {
                        //Add Label
                        XRTableRow headerRow = new XRTableRow();
                        XRLabel label = new XRLabel();
                        label.Text = "Network License " + " (" + network.Count + ")";
                        label.WidthF = 300F;
                        label.HeightF = 50F;
                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                        // Add picture box
                        XRPictureBox pictureBox1 = new XRPictureBox();
                        pictureBox1.Image = Image.FromFile("wwwroot/img/Drdrill_Icons/network.png");
                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                        table.Rows.Add(headerRow);


                        XRTableRow serverHeader = new XRTableRow();
                        serverHeader.HeightF = 32F;
                        var serverHead2 = (XRTable)sender;
                        XRLabel networkName = new XRLabel(); networkName.Text = "  Server Name"; networkName.WidthF = 229.36F; networkName.HeightF = 32F;
                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                        serverHeader.Cells.Add(new XRTableCell { Controls = { networkName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Server Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "PO Number", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        table.Rows.Add(serverHeader);

                        rowIndex = 0;

                        foreach (var networkServer in network)
                        {
                            try
                            {
                                var decrypt = SecurityHelper.Decrypt(networkServer.PONumber);
                                networkServer.PONumber = decrypt != "" ? decrypt : networkServer.PONumber;
                            }
                            catch
                            {
                                networkServer.PONumber = networkServer.PONumber;
                            }
                            string imagePath = imagePaths.FirstOrDefault(pair => networkServer.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                            XRTableRow dataRow = new XRTableRow();
                            XRPictureBox pictureBox = new XRPictureBox();
                            pictureBox.Image = Image.FromFile(imagePath);
                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                            dataRow.Cells.AddRange(new[]
                            {
             new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(networkServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(networkServer.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(networkServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(networkServer.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(networkServer.PONumber), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
             });
                            bool isOddRow = rowIndex % 2 != 0;
                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                            table.Rows.Add(dataRow);
                            rowIndex++;
                        }
                    }

                    var ThirdParty = report[j].ThirdPartyReportVms;
                    if (ThirdParty.Count > 0)
                    {
                        //Add Label
                        XRTableRow headerRow = new XRTableRow();
                        XRLabel label = new XRLabel();
                        label.Text = "Third Party License " + " (" + ThirdParty.Count + ")";
                        label.WidthF = 300F;
                        label.HeightF = 50F;
                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                        // Add picture box
                        XRPictureBox pictureBox1 = new XRPictureBox();
                        pictureBox1.Image = Image.FromFile("wwwroot/img/License_Report/Thirdparty_Lic_Black.png");
                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                        table.Rows.Add(headerRow);


                        XRTableRow serverHeader = new XRTableRow();
                        serverHeader.HeightF = 32F;
                        var serverHead2 = (XRTable)sender;
                        XRLabel ThirdPartyName = new XRLabel(); ThirdPartyName.Text = "  Server Name"; ThirdPartyName.WidthF = 229.36F; ThirdPartyName.HeightF = 32F;
                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                        serverHeader.Cells.Add(new XRTableCell { Controls = { ThirdPartyName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Server Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "PO Number", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        table.Rows.Add(serverHeader);

                        rowIndex = 0;

                        foreach (var thirdPartyServer in ThirdParty)
                        {
                            try
                            {
                                var decrypt = SecurityHelper.Decrypt(thirdPartyServer.PONumber);
                                thirdPartyServer.PONumber = decrypt != "" ? decrypt : thirdPartyServer.PONumber;
                            }
                            catch
                            {
                                thirdPartyServer.PONumber = thirdPartyServer.PONumber;
                            }
                            string imagePath = imagePaths.FirstOrDefault(pair => thirdPartyServer.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                            XRTableRow dataRow = new XRTableRow();
                            XRPictureBox pictureBox = new XRPictureBox();
                            pictureBox.Image = Image.FromFile(imagePath);
                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                            dataRow.Cells.AddRange(new[]
                            {
             new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(thirdPartyServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(thirdPartyServer.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(thirdPartyServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(thirdPartyServer.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(thirdPartyServer.PONumber), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
             });
                            bool isOddRow = rowIndex % 2 != 0;
                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                            table.Rows.Add(dataRow);
                            rowIndex++;
                        }
                    }

                    var vm = report[j].VirtualizationReportVms;
                    if (vm.Count > 0)
                    {
                        //Add Label
                        XRTableRow headerRow = new XRTableRow();
                        XRLabel label = new XRLabel();
                        label.Text = "Virtualization License " + " (" + vm.Count + ")";
                        label.WidthF = 300F;
                        label.HeightF = 50F;
                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                        // Add picture box
                        XRPictureBox pictureBox1 = new XRPictureBox();
                        pictureBox1.Image = Image.FromFile("wwwroot/img/License_Report/VM_Lic_Black.png");
                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                        table.Rows.Add(headerRow);


                        XRTableRow serverHeader = new XRTableRow();
                        serverHeader.HeightF = 32F;
                        var serverHead2 = (XRTable)sender;
                        XRLabel vmName = new XRLabel(); vmName.Text = "  Server Name"; vmName.WidthF = 229.36F; vmName.HeightF = 32F;
                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                        serverHeader.Cells.Add(new XRTableCell { Controls = { vmName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Server Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "PO Number", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        table.Rows.Add(serverHeader);

                        rowIndex = 0;

                        foreach (var vmServer in vm)
                        {
                            try
                            {
                                var decrypt = SecurityHelper.Decrypt(vmServer.PONumber);
                                vmServer.PONumber = decrypt != "" ? decrypt : vmServer.PONumber;
                            }
                            catch
                            {
                                vmServer.PONumber = vmServer.PONumber;
                            }
                            string imagePath = imagePaths.FirstOrDefault(pair => vmServer.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                            XRTableRow dataRow = new XRTableRow();
                            XRPictureBox pictureBox = new XRPictureBox();
                            pictureBox.Image = Image.FromFile(imagePath);
                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                            dataRow.Cells.AddRange(new[]
                            {
             new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(vmServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(vmServer.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(vmServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(vmServer.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(vmServer.PONumber), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
             });
                            bool isOddRow = rowIndex % 2 != 0;
                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                            table.Rows.Add(dataRow);
                            rowIndex++;
                        }
                    }

                    var dns = report[j].DNSReportVms;
                    if (dns.Count > 0)
                    {
                        //Add Label
                        XRTableRow headerRow = new XRTableRow();
                        XRLabel label = new XRLabel();
                        label.Text = "Dns License " + " (" + dns.Count + ")";
                        label.WidthF = 300F;
                        label.HeightF = 50F;
                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                        // Add picture box
                        XRPictureBox pictureBox1 = new XRPictureBox();
                        pictureBox1.Image = Image.FromFile("wwwroot/img/Drdrill_Icons/dns.png");
                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                        table.Rows.Add(headerRow);


                        XRTableRow serverHeader = new XRTableRow();
                        serverHeader.HeightF = 32F;
                        var serverHead2 = (XRTable)sender;
                        XRLabel dnsName = new XRLabel(); dnsName.Text = "  Server Name"; dnsName.WidthF = 229.36F; dnsName.HeightF = 32F;
                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                        serverHeader.Cells.Add(new XRTableCell { Controls = { dnsName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Server Type", WidthF = 166.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 202.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        serverHeader.Cells.Add(new XRTableCell { Text = "PO Number", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                        table.Rows.Add(serverHeader);

                        rowIndex = 0;

                        foreach (var dnsServer in dns)
                        {
                            try
                            {
                                var decrypt = SecurityHelper.Decrypt(dnsServer.PONumber);
                                dnsServer.PONumber = decrypt != "" ? decrypt : dnsServer.PONumber;
                            }
                            catch
                            {
                                dnsServer.PONumber = dnsServer.PONumber;
                            }
                            string imagePath = imagePaths.FirstOrDefault(pair => dnsServer.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                            XRTableRow dataRow = new XRTableRow();
                            XRPictureBox pictureBox = new XRPictureBox();
                            pictureBox.Image = Image.FromFile(imagePath);
                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                            dataRow.Cells.AddRange(new[]
                            {
             new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(dnsServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(dnsServer.Type), WidthF = 146.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(dnsServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(dnsServer.EntityField), WidthF = 202.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
             new XRTableCell { Text = GetValueOrNA(dnsServer.PONumber), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
             });
                            bool isOddRow = rowIndex % 2 != 0;
                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                            table.Rows.Add(dataRow);
                            rowIndex++;
                        }
                    }

                    j++;
                    return;
                    // }
                    //else{ j++; return; }

                };

                this.DataSource = report;
                //Values Count

                AvailableCountVm = Licencereport.Select(x => x.AvailableCountVm).ToList();
                UsedCountVm = Licencereport.Select(x => x.UsedCountVm).ToList();

                int Usedcount = UsedCountVm.Sum(u =>
               u.DatabaseUsedCount +
               u.ReplicationUsedCount +
               u.StorageUsedCount +
               u.VirtualizationUsedCount +
               u.ApplicationUsedCount +
                u.DnsUsedCount +
               u.NetworkUsedCount +
               u.ThirdPartyUsedCount
           );

                CombinedCount combinedCount = new CombinedCount
                {
                    DatabaseCount = AvailableCountVm.Sum(a => a.DatabaseAvailableCount) + UsedCountVm.Sum(u => u.DatabaseUsedCount),
                    ReplicationCount = AvailableCountVm.Sum(a => a.ReplicationAvailableCount) + UsedCountVm.Sum(u => u.ReplicationUsedCount),
                    StorageCount = AvailableCountVm.Sum(a => a.StorageAvailableCount) + UsedCountVm.Sum(u => u.StorageUsedCount),
                    VirtualizationCount = AvailableCountVm.Sum(a => a.VirtualizationAvailableCount) + UsedCountVm.Sum(u => u.VirtualizationUsedCount),
                    ApplicationCount = AvailableCountVm.Sum(a => a.ApplicationAvailableCount) + UsedCountVm.Sum(u => u.ApplicationUsedCount),
                    DnsCount = AvailableCountVm.Sum(a => a.DnsAvailableCount) + UsedCountVm.Sum(u => u.DnsUsedCount),
                    NetworkCount = AvailableCountVm.Sum(a => a.NetworkAvailableCount) + UsedCountVm.Sum(u => u.NetworkUsedCount),
                    ThirdPartyCount = AvailableCountVm.Sum(a => a.ThirdPartyAvailableCount) + UsedCountVm.Sum(u => u.ThirdPartyUsedCount),
                };

                totals = combinedCount.DatabaseCount + combinedCount.ReplicationCount + combinedCount.StorageCount + combinedCount.VirtualizationCount + combinedCount.ApplicationCount + combinedCount.DnsCount + combinedCount.ThirdPartyCount + combinedCount.NetworkCount;


                totalcount = Licencereport.Sum(total => total.TotalCount);
                //int remain = totalcount - Usedcount;
                xrLabel4.Text = UsedCountVm[0].DnsUsedCount.ToString();
                xrLabel41.Text = UsedCountVm[0].ReplicationUsedCount.ToString();
                xrLabel42.Text = UsedCountVm[0].DatabaseUsedCount.ToString();
                xrLabel43.Text = UsedCountVm[0].ApplicationUsedCount.ToString();
                xrLabel44.Text = UsedCountVm[0].NetworkUsedCount.ToString();
                xrLabel47.Text = UsedCountVm[0].StorageUsedCount.ToString();
                xrLabel48.Text = UsedCountVm[0].ThirdPartyUsedCount.ToString();
                xrLabel52.Text = UsedCountVm[0].VirtualizationUsedCount.ToString();
                this.xrLabel51.Text = totalcount.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the License_OperationalService_report. The error message : " + ex.Message); throw; }
        }

        //Charts
        private void xrChart1_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                Int64 DatabaseCount = 0, DNSCount = 0, ReplicationCount = 0, ApplicationCount = 0,
                       NetworkCount = 0, Storage = 0, ThirdPartyTool = 0, VM = 0/*, NodataFound = 0*/;

                if (UsedCountVm.Count != 0)
                {
                    DatabaseCount = UsedCountVm[0].DatabaseUsedCount;
                    DNSCount = UsedCountVm[0].DnsUsedCount;
                    ReplicationCount = UsedCountVm[0].ReplicationUsedCount;
                    ApplicationCount = UsedCountVm[0].ApplicationUsedCount;
                    NetworkCount = UsedCountVm[0].NetworkUsedCount;
                    Storage = UsedCountVm[0].StorageUsedCount;
                    ThirdPartyTool = UsedCountVm[0].ThirdPartyUsedCount;
                    VM = UsedCountVm[0].VirtualizationUsedCount;
                }

                Series series = new Series("Series1", ViewType.Doughnut);
                xrChart1.Series.Add(series);

                series.DataSource = CreateChartData(ApplicationCount, ReplicationCount, DNSCount, DatabaseCount, NetworkCount, Storage, ThirdPartyTool, VM);
                DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                doughnutSeriesView.MinAllowedSizePercentage = 70D;
                series.View = doughnutSeriesView;
                series.ArgumentScaleType = ScaleType.Auto;
                series.ArgumentDataMember = "Argument";
                series.ValueScaleType = ScaleType.Numerical;
                series.ValueDataMembers.AddRange(new string[] { "Value" });
                series.Label.TextPattern = "{A}\n{V}";
                ((DoughnutSeriesLabel)series.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;
                series.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the License_OperationalService_report's detail Chart. The error message : " + ex.Message); throw; }
        }
        private DataTable CreateChartData(Int64 ApplicationCount, Int64 ReplicationCount, Int64 DNSCount, Int64 DatabaseCount, Int64 NetworkCount, Int64 Storage, Int64 ThirdPartyTool, Int64 VM)
        {
            DataTable table = new DataTable("Table1");
            table.Columns.Add("Argument", typeof(string));
            table.Columns.Add("Value", typeof(Int64));
            Random rnd = new Random();
            table.Rows.Add("Network", NetworkCount);
            // table.Rows.Add("NotUsed", NotUsedCount);
            table.Rows.Add("Database", DatabaseCount);
            table.Rows.Add("DNS", DNSCount);
            table.Rows.Add("Replication", ReplicationCount);
            table.Rows.Add("Application", ApplicationCount);
            // table.Rows.Add("Hyper", Hyper);
            table.Rows.Add("Storage", Storage);
            table.Rows.Add("ThirdPartyTool", ThirdPartyTool);
            table.Rows.Add("VM", VM);
            // table.Rows.Add("Datasync", Datasync);

            return table;
        }

        private void xrChart4_BeforePrint(object sender, System.EventArgs e)
        {
            Series series = new Series("Series1", ViewType.Bar);

            series.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
            series.DataSource = CreateChartDatabar(totalcount, usedcounts, remaincounts);
            series.ArgumentScaleType = ScaleType.Auto;
            series.ArgumentDataMember = "Argument";
            series.ValueScaleType = ScaleType.Numerical;
            series.ValueDataMembers.AddRange(new string[] { "Value" });
            series.View.Colorizer = new ColorObjectColorizer();
            BarSeriesView view = (BarSeriesView)series.View;
            view.BarWidth = 0.4;
            view.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
            view.Pane.BorderVisible = false;

        }
        private DataTable CreateChartDatabar(Int64 totalCount, Int64 usedCount, Int64 remainCount)
        {
            DataTable table = new DataTable("Table1");
            table.Columns.Add("Argument", typeof(string));
            table.Columns.Add("Value", typeof(Int64));
            Random rnd = new Random();
            table.Rows.Add("Assigned", totalCount);
            table.Rows.Add("Utilized", usedCount);
            table.Rows.Add("Remaining", remainCount);
            return table;
        }
        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + licenseReport.ReportGeneratedBy.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the License_OperationalService_report's User name. The error message : " + ex.Message); throw; }
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the License_OperationalService_report's CP Version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the customer logo in License_OperationalService_report" + ex.Message.ToString());
            }
        }
    }
}
