﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.SmtpConfiguration.Events.Create;

public class SmtpConfigurationCreatedEventHandler : INotificationHandler<SmtpConfigurationCreatedEvent>
{
    private readonly ILogger<SmtpConfigurationCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;


    public SmtpConfigurationCreatedEventHandler(ILoggedInUserService userService,
        ILogger<SmtpConfigurationCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(SmtpConfigurationCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} {Modules.SmtpConfiguration}",
            Entity = Modules.SmtpConfiguration.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"SMTP Configuration '{createdEvent.UserName}' Created Successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"SmtpConfiguration '{createdEvent.UserName}' Created Successfully.");
    }
}