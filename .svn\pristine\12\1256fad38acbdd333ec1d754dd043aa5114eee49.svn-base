﻿using ContinuityPatrol.Application.Features.Server.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Queries;

public class GetServerDetailQueryHandlerTests : IClassFixture<ServerFixture>
{
    private readonly ServerFixture _serverFixture;
    private readonly Mock<IServerRepository> _mockServerRepository;
    private readonly GetServerDetailQueryHandler _handler;

    public GetServerDetailQueryHandlerTests(ServerFixture serverFixture)
    {
        _serverFixture = serverFixture;

        _mockServerRepository = ServerRepositoryMocks.GetServerRepository(_serverFixture.Servers);

        _handler = new GetServerDetailQueryHandler(_serverFixture.Mapper, _mockServerRepository.Object);

        _serverFixture.Servers[0].Properties = "{\"Name\": \"admin\", \"password\": \"5FiFj1czI96ExIcwE1t6u2ntGXcFfBNBEup6u5O6xA4=\"}";
        _serverFixture.Servers[0].LicenseKey = SecurityHelper.Encrypt(_serverFixture.Servers[0].LicenseKey);
    }

    [Fact]
    public async Task Handle_Return_ServerDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetServerDetailQuery { Id = _serverFixture.Servers[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<ServerDetailVm>();

        result.Id.ShouldBe(_serverFixture.Servers[0].ReferenceId);
        result.CompanyId.ShouldBe(_serverFixture.Servers[0].CompanyId);
        result.Name.ShouldBe(_serverFixture.Servers[0].Name);
        result.Properties.ShouldBe(_serverFixture.Servers[0].Properties);
        result.OSType.ShouldBe(_serverFixture.Servers[0].OSType);
        result.RoleType.ShouldBe(_serverFixture.Servers[0].RoleType);
        result.Status.ShouldBe(_serverFixture.Servers[0].Status);
        result.ServerType.ShouldBe(_serverFixture.Servers[0].ServerType);
        result.SiteId.ShouldBe(_serverFixture.Servers[0].SiteId);
        result.SiteName.ShouldBe(_serverFixture.Servers[0].SiteName);
        result.LicenseKey.ShouldBe(_serverFixture.Servers[0].LicenseKey);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidServerId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetServerDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_RepositoryOnlyOnce()
    {
        await _handler.Handle(new GetServerDetailQuery { Id = _serverFixture.Servers[0].ReferenceId }, CancellationToken.None);

        _mockServerRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}