﻿using System.Net;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;


namespace ContinuityPatrol.Application.Features.Server.Commands.Update;

public class UpdateServerCommandValidator : AbstractValidator<UpdateServerCommand>
{
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILicenseValidationService _licenseValidationService;
    private readonly IServerRepository _serverRepository;
    private readonly ISiteRepository _siteRepository;
    private readonly ISiteTypeRepository _siteTypeRepository;
    private readonly IDatabaseViewRepository _databaseViewRepository;
    public static string AttachedDataBase = "";

    public UpdateServerCommandValidator(IServerRepository serverRepository,
        ILicenseManagerRepository licenseManagerRepository, ISiteRepository siteRepository,
        ILicenseValidationService licenseValidationService, ISiteTypeRepository siteTypeRepository,IDatabaseViewRepository databaseViewRepository)
    {
        _serverRepository = serverRepository;
        _licenseManagerRepository = licenseManagerRepository;
        _siteRepository = siteRepository;
        _licenseValidationService = licenseValidationService;
        _siteTypeRepository = siteTypeRepository;
        _databaseViewRepository=databaseViewRepository;

        RuleFor(p => p.LicenseKey)
            .NotEmpty().WithMessage("Select {PropertyName}.");

        RuleFor(p => p)
            .MustAsync(IsLicenseExpiredAsync)
            .WithMessage("The license key has expired.");

        RuleFor(p => p)
            .MustAsync(IsLicenseActiveAsync)
            .WithMessage("License is in 'InActive' state");

        RuleFor(p => p)
            .MustAsync(ValidateLicenseCountAsync)
            .WithMessage("Server count reached maximum limit.");

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z\d]+[_\s]?)([a-zA-Z\d]+[_\s-])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.SiteName)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();

        RuleFor(p => p.ServerType)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}");

        RuleFor(p => p.OSType)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^[a-zA-Z\d]+([_\s\-\.][a-zA-Z\d]+)*$")
            .WithMessage("Please Enter Valid {PropertyName}");

        RuleFor(p => p.RoleType)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}");
        RuleFor(p=>p)
            .MustAsync(IsServerAttachedWithDataBase)
            .WithMessage(_=>$"Server is attached with '{AttachedDataBase}' database.");

        //RuleFor(p => p.Properties)
        //    .NotEmpty().WithMessage("{PropertyName} is required.")
        //    .NotNull()
        //    .Must(IsValidJson).WithMessage("{PropertyName} must be a valid JSON string.");
        RuleFor(p => p.Properties)
          .NotEmpty().WithMessage("{PropertyName} is required.")
          .NotNull()
          .Must(GetJsonProperties.IsValidJson).WithMessage("{PropertyName} must be a valid JSON string.");

        RuleFor(p => p)
            .MustAsync(ServerNameUnique)
            .WithMessage("A same name already exists.");


        RuleFor(y => y)
            .NotNull()
            .MustAsync(VerifyGuid)
            .WithMessage("Id is invalid");

        RuleFor(p => p)
            .NotNull()
            .MustAsync(VerifyIpAddress)
            .WithMessage("Please enter valid IP address.");

        //RuleFor(p => p)
        //    .NotNull()
        //    .MustAsync(VerifyHostName)
        //    .WithMessage("Please enter valid HostName.");
    }


    private Task<bool> VerifyHostName(UpdateServerCommand p, CancellationToken cancellationToken)
    {
      
        var hostName = GetJsonProperties.GetHostNameFromProperties(p.Properties);

        if (hostName == "NA") return Task.FromResult(true);

        var regex = new Regex(
            @"^[a-zA-Z0-9]+([-a-zA-Z0-9]*[a-zA-Z0-9])*(\.[a-zA-Z0-9]+([-a-zA-Z0-9]*[a-zA-Z0-9])*)*$",
            RegexOptions.Compiled | RegexOptions.IgnoreCase
        );


        return Task.FromResult(regex.IsMatch(hostName));
    }


    private Task<bool> VerifyIpAddress(UpdateServerCommand p, CancellationToken cancellationToken)
    {
       
        var ipAddress = GetJsonProperties.GetIpAddressFromProperties(p.Properties);

        if (ipAddress == "NA") return Task.FromResult(true);

        return Task.FromResult(IPAddress.TryParse(ipAddress, out _));
    }

    private Task<bool> VerifyGuid(UpdateServerCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.Id, "Server Id");
        Guard.Against.InvalidGuidOrEmpty(p.SiteId, "Site Id");
        Guard.Against.InvalidGuidOrEmpty(p.RoleTypeId, "RoleType Id");
        Guard.Against.InvalidGuidOrEmpty(p.ServerTypeId, "ServerType Id");
        Guard.Against.InvalidGuidOrEmpty(p.BusinessServiceId, "BusinessService Id");
        Guard.Against.InvalidGuidOrEmpty(p.OSTypeId, "OsType Id");
        Guard.Against.InvalidGuidOrEmpty(p.LicenseId, "License Id");

        return Task.FromResult(true);
    }

    //private bool IsValidJson(string properties)
    //{
    //    if (string.IsNullOrWhiteSpace(properties))
    //        return false;

    //    properties = properties.Trim();
    //    if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
    //        (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
    //        try
    //        {
    //            var obj = JsonConvert.DeserializeObject(properties);
    //            return obj != null;
    //        }
    //        catch (JsonException)
    //        {
    //            return false;
    //        }

    //    return false;
    //}


    public async Task<bool> IsLicenseExpiredAsync(UpdateServerCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.LicenseId, "License Id");

        var licenseManager = await _licenseManagerRepository.GetLicenseDetailByIdAsync(p.LicenseId);

        return await _licenseValidationService.IsLicenseExpired(licenseManager.ExpiryDate);
    }

    public async Task<bool> IsLicenseActiveAsync(UpdateServerCommand p, CancellationToken cancellationToken)
    {
        if (p.LicenseId.IsNullOrWhiteSpace()) return false;

        var licenseDtl = await _licenseManagerRepository.GetLicenseDetailByIdAsync(p.LicenseId) ??
                         throw new InvalidException("License GetList is null.");

        return licenseDtl.IsState;
    }

    private async Task<bool> ServerNameUnique(UpdateServerCommand p, CancellationToken cancel)
    {
        return !await _serverRepository.IsServerNameExist(p.Name, p.Id);
    }

    private async Task<bool> ValidateLicenseCountAsync(UpdateServerCommand p, CancellationToken cancel)
    {
        if (p.LicenseId.IsNullOrWhiteSpace()) return false;

        var server = await _serverRepository.GetByReferenceIdAsync(p.Id);

        var licenseManager = await _licenseManagerRepository.GetLicenseDetailByIdAsync(p.LicenseId) ??
                             throw new InvalidException("License GetList is null.");

        if (p.LicenseId.Equals(server.LicenseId) && p.RoleType.Equals(server.RoleType) &&
            p.SiteId.Equals(server.SiteId))
        {
            var isExpired = await _licenseValidationService.IsLicenseExpired(licenseManager.ExpiryDate);

            if (!isExpired) throw new InvalidOperationException("The license key has expired.");
        }
        else
        {
            var site = await _siteRepository.GetByReferenceIdAsync(p.SiteId);

            Guard.Against.NullOrDeactive(site, nameof(Domain.Entities.Site),
                new NotFoundException(nameof(Domain.Entities.Site), p.SiteId));

            var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

            Guard.Against.NullOrDeactive(siteType, nameof(Domain.Entities.SiteType),
                new NotFoundException(nameof(Domain.Entities.SiteType), site.TypeId));

            var index = await _siteTypeRepository.GetSiteTypeIndexByIdAsync(siteType.ReferenceId);

            var serverCount = await _serverRepository.GetServerCountByLicenseKey(p.LicenseId, p.RoleType, siteType.ReferenceId);

            return await _licenseValidationService.IsServerLicenseCountExitMaxLimit(licenseManager, siteType,
                p.RoleType, serverCount, index);
        }

        return true;
    }

    private async Task<bool> IsServerAttachedWithDataBase(UpdateServerCommand p, CancellationToken cancel)
    {
        var server = await _serverRepository.GetByReferenceIdAsync(p.Id);

        if (server is not null && server.RoleType.ToLower().Equals("database"))
        {
            if (!server.RoleType.Trim().ToLower().Equals(p.RoleType.Trim().ToLower()))
            {
                var database = await _databaseViewRepository.GetDatabaseByServerId(p.Id);

                AttachedDataBase = database?.FirstOrDefault()?.Name;

                return !database.Any();
            }
        }

        return true;
    }
}