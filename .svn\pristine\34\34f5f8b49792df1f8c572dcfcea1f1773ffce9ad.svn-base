﻿//using ContinuityPatrol.Application.Features.DashboardView.Queries.GetServiceTopologyList;
//using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetDCMappingBySites;
//using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessServiceDrReadyDetails;
//using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetByBusinessServiceId;
//using ContinuityPatrol.Domain.ViewModels.BusinessServiceAvailabilityModel;
//using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;
//using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;
//using ContinuityPatrol.Domain.ViewModels.ImpactActivityModel;
//using ContinuityPatrol.Domain.ViewModels.SiteModel;
//using ContinuityPatrol.Shared.Services.Provider;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.Extensions.Logging;
//using Moq;
//using Newtonsoft.Json;

//namespace ContinuityPatrol.Web.UnitTests.Areas.Dashboard.Controllers
//{
//    public class DcMappingControllerTests
//    {
//        private readonly Mock<IDataProvider> _mockDataProvider = new();
//        private readonly Mock<ILogger<DcMappingController>> _mockLogger = new();
//        private DcMappingController _controller;

//        public DcMappingControllerTests()
//        {
//            Initialize();
//        }
//        public void Initialize()
//        {
//            _controller = new DcMappingController
//                (_mockDataProvider.Object,
//                _mockLogger.Object);
//        }

//        [Fact]
//        public async Task GetBusinessServiceList_ReturnsJsonResult_WithData_WhenSiteIdIsProvided()
//        {
//            var siteId = "testSiteId";
//            var mockDcMapping = new List<GetDcMappingListVm>();

//            _mockDataProvider.Setup(p => p.DashboardView.GetDcMappingDetails(siteId))
//                             .ReturnsAsync(mockDcMapping);

//            var result = await _controller.GetBusinessServiceList(siteId);
//            var data = result?.Value as dynamic;

//            Assert.NotNull(result);
//            var json = JsonConvert.SerializeObject(result.Value);
//            Assert.Contains("\"Success\":true", json);
//            Assert.NotNull(result);
//        }

//        [Fact]
//        public async Task GetBusinessServiceList_ReturnsJsonResult_WithError_WhenExceptionIsThrown()
//        {
//            var siteId = "testSiteId";
//            _mockDataProvider.Setup(p => p.DashboardView.GetDcMappingDetails(siteId))
//                             .ThrowsAsync(new Exception("Test Exception"));

//            var result = await _controller.GetBusinessServiceList(siteId);

//            Assert.NotNull(result);
//            Assert.IsType<JsonResult>(result);
//            var data = result?.Value as dynamic;
//            Assert.False((bool)data.Success);
//            Assert.Equal("Test Exception", (string)data.Message);
//        }

//        [Fact]
//        public async Task GetImpactList_ReturnsJsonResult_WithData()
//        {

//            var mockImpactActivity = new List<ImpactActivityListVm>();

//            _mockDataProvider.Setup(p => p.ImpactActivity.GetImpactActivityList())
//                             .ReturnsAsync(mockImpactActivity);

//            var result = await _controller.GetImpactList();
//            var data = result?.Value as dynamic;

//            Assert.NotNull(result);
//            var json = JsonConvert.SerializeObject(result.Value);
//            Assert.Contains("\"Success\":true", json);
//            Assert.NotNull(result);
//        }

//        [Fact]
//        public async Task GetImpactDetailCount_ReturnsJsonResult_WithData_WhenBusinessServiceIdIsProvided()
//        {
//            var businessServiceId = "testServiceId";
//            var mockImpactDetail = new ImpactDetailVm();

//            _mockDataProvider.Setup(p => p.HeatMapStatus.GetImpactDetail(businessServiceId))
//                             .ReturnsAsync(mockImpactDetail);

//            var result = await _controller.GetImpactDetailCount(businessServiceId);
//            var data = result?.Value as dynamic;

//            Assert.NotNull(result);
//            var json = JsonConvert.SerializeObject(result.Value);
//            Assert.Contains("\"Success\":true", json);
//            Assert.NotNull(result);
//        }

//        [Fact]
//        public async Task GetBusinessServiceTopology_ReturnsJsonResult_WithData_WhenBusinessServiceIdIsProvided()
//        {

//            var businessServiceId = "testServiceId";
//            var mockTopology = new List<GetServiceTopologyListVm>();

//            _mockDataProvider.Setup(p => p.DashboardView.GetBusinessServiceTopologyByBusinessServiceId(businessServiceId))
//                             .ReturnsAsync(mockTopology);

//            var result = await _controller.GetBusinessServiceTopology(businessServiceId) as JsonResult;
//            var data = result?.Value as dynamic;

//            Assert.NotNull(result);
//            var json = JsonConvert.SerializeObject(result.Value);
//            Assert.Contains("\"Success\":true", json);
//            Assert.NotNull(result);
//        }

//        [Fact]
//        public async Task GetSites_ReturnsJsonResult_WithData()
//        {
//            var mockSites = new List<SiteListVm>();

//            _mockDataProvider.Setup(p => p.Site.GetSites())
//                             .ReturnsAsync(mockSites);

//            var result = await _controller.GetSites();
//            var data = result?.Value as dynamic;

//            Assert.NotNull(result);
//            var json = JsonConvert.SerializeObject(result.Value);
//            Assert.Contains("\"success\":true", json);
//            Assert.NotNull(result);
//        }

//        [Fact]
//        public async Task GetBusinessServiceAvailabilityList_ReturnsJsonResult_WithData()
//        {
//            var mockBusinessServiceAvailability = new List<BusinessServiceAvailabilityListVm>();

//            _mockDataProvider.Setup(p => p.BusinessServiceAvailability.GetBusinessServiceAvailabilityList())
//                             .ReturnsAsync(mockBusinessServiceAvailability);

//            var result = await _controller.GetBusinessServiceAvailabilityList();
//            var data = result?.Value as dynamic;

//            Assert.NotNull(result);
//            var json = JsonConvert.SerializeObject(result.Value);
//            Assert.Contains("\"success\":true", json);
//            Assert.NotNull(result);
//        }

//        [Fact]
//        public async Task GetDrReadyStatusByBusinessServiceId_ReturnsJsonResult_WithData_WhenBusinessServiceIdIsProvided()
//        {
//            var businessServiceId = "testServiceId";
//            var mockDrReadyStatus = new List<DRReadyStatusByBusinessServiceIdVm>();

//            _mockDataProvider.Setup(p => p.DrReadyStatus.GetDrReadyStatusByBusinessServiceId(businessServiceId))
//                             .ReturnsAsync(mockDrReadyStatus);

//            var result = await _controller.GetDrReadyStatusByBusinessServiceId(businessServiceId);
//            var data = result?.Value as dynamic;

//            Assert.NotNull(result);
//            var json = JsonConvert.SerializeObject(result.Value);
//            Assert.Contains("\"success\":true", json);
//            Assert.NotNull(result);
//        }

//        [Fact]
//        public async Task GetBusinessServiceDrReady_ReturnsJsonResult_WithData_WhenBusinessServiceIdIsProvided()
//        {
//            var businessServiceId = "testServiceId";
//            var mockBusinessServiceDrReady = new List<BusinessServiceDrReadyDetailVm>();

//            _mockDataProvider.Setup(p => p.DrReadyStatus.GetBusinessServiceDrReady(businessServiceId))
//                             .ReturnsAsync(mockBusinessServiceDrReady);

//            var result = await _controller.GetBusinessServiceDrReady(businessServiceId);
//            var data = result?.Value as dynamic;

//            Assert.NotNull(result);
//            var json = JsonConvert.SerializeObject(result.Value);
//            Assert.Contains("\"success\":true", json);
//            Assert.NotNull(result);
//        }

//        [Fact]
//        public async Task GetDcMappingSiteDetails_ReturnsJsonResult_WithData()
//        {
//            var mockDcMappingSiteDetails = new GetDcMappingSitesVm();

//            _mockDataProvider.Setup(p => p.DashboardView.GetDcMappingSiteDetails())
//                             .ReturnsAsync(mockDcMappingSiteDetails);

//            var result = await _controller.GetDcMappingSiteDetails();
//            var data = result?.Value as dynamic;

//            Assert.NotNull(result);
//            var json = JsonConvert.SerializeObject(result.Value);
//            Assert.Contains("\"success\":true", json);
//            Assert.NotNull(result);
//        }
//    }
//}

