using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperationGroup.Events;

public class DeleteBulkImportOperationGroupEventTests : IClassFixture<BulkImportOperationGroupFixture>, IClassFixture<UserActivityFixture>
{
    private readonly BulkImportOperationGroupFixture _bulkImportOperationGroupFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly BulkImportOperationGroupDeletedEventHandler _handler;

    public DeleteBulkImportOperationGroupEventTests(BulkImportOperationGroupFixture bulkImportOperationGroupFixture, UserActivityFixture userActivityFixture)
    {
        _bulkImportOperationGroupFixture = bulkImportOperationGroupFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");
        mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/bulkimportoperationgroup");
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        var mockBulkImportOperationGroupEventLogger = new Mock<ILogger<BulkImportOperationGroupDeletedEventHandler>>();

        _mockUserActivityRepository = BulkImportOperationGroupRepositoryMocks.CreateBulkImportOperationGroupEventRepository(_userActivityFixture.UserActivities);

        _handler = new BulkImportOperationGroupDeletedEventHandler(
            mockLoggedInUserService.Object, 
            mockBulkImportOperationGroupEventLogger.Object, 
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteBulkImportOperationGroupEventCreated()
    {
        // Arrange
        _userActivityFixture.UserActivities[0].LoginName = "Tester";
        var bulkImportOperationGroupDeletedEvent = new BulkImportOperationGroupDeletedEvent { Name = "TestInfraObject" };

        // Act
        var result = _handler.Handle(bulkImportOperationGroupDeletedEvent, CancellationToken.None);

        // Assert
        Assert.True(result.IsCompleted);
        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var bulkImportOperationGroupDeletedEvent = new BulkImportOperationGroupDeletedEvent { Name = "TestInfraObject" };

        // Act
        await _handler.Handle(bulkImportOperationGroupDeletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_WithCorrectProperties()
    {
        // Arrange
        var bulkImportOperationGroupDeletedEvent = new BulkImportOperationGroupDeletedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationGroupDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.LoginName.ShouldBe("Tester");
        capturedUserActivity.Action.ShouldContain("Delete BulkImportOperationGroup");
        capturedUserActivity.Entity.ShouldBe("BulkImportOperationGroup");
        capturedUserActivity.ActivityType.ShouldBe("Delete");
        capturedUserActivity.ActivityDetails.ShouldContain("TestInfraObject");
        capturedUserActivity.ActivityDetails.ShouldContain("deleted successfully");
    }

    [Fact]
    public async Task Handle_LogCorrectInformation_When_BulkImportOperationGroupDeleted()
    {
        // Arrange
        var bulkImportOperationGroupDeletedEvent = new BulkImportOperationGroupDeletedEvent { Name = "TestInfraObject" };
        var mockLogger = new Mock<ILogger<BulkImportOperationGroupDeletedEventHandler>>();

        var handler = new BulkImportOperationGroupDeletedEventHandler(
            new Mock<ILoggedInUserService>().Object,
            mockLogger.Object,
            _mockUserActivityRepository.Object);

        // Act
        await handler.Handle(bulkImportOperationGroupDeletedEvent, CancellationToken.None);

        bulkImportOperationGroupDeletedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_SetCorrectUserActivityProperties_When_UserServiceHasData()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var testUrl = "/api/test";
        var testIpAddress = "***********";
        var testLoginName = "TestUser";

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);
        mockLoggedInUserService.Setup(x => x.LoginName).Returns(testLoginName);
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testUrl);
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testIpAddress);

        var handler = new BulkImportOperationGroupDeletedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationGroupDeletedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationGroupDeletedEvent = new BulkImportOperationGroupDeletedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationGroupDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.UserId.ShouldBe(testUserId);
        capturedUserActivity.LoginName.ShouldBe(testLoginName);
        capturedUserActivity.RequestUrl.ShouldBe(testUrl);
        capturedUserActivity.HostAddress.ShouldBe(testIpAddress);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_EventHandled()
    {
        // Arrange
        var bulkImportOperationGroupDeletedEvent = new BulkImportOperationGroupDeletedEvent { Name = "ProductionInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationGroupDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.Action.ShouldBe("Delete BulkImportOperationGroup");
        capturedUserActivity.Entity.ShouldBe("BulkImportOperationGroup");
        capturedUserActivity.ActivityType.ShouldBe("Delete");
        capturedUserActivity.ActivityDetails.ShouldBe("BulkImportOperationGroup 'ProductionInfraObject' deleted successfully.");
    }

    [Fact]
    public async Task Handle_NotSetCreatedByAndLastModifiedBy_When_DeleteEvent()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);

        var handler = new BulkImportOperationGroupDeletedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationGroupDeletedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationGroupDeletedEvent = new BulkImportOperationGroupDeletedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationGroupDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        // Delete events don't set CreatedBy and LastModifiedBy unlike Create events
        capturedUserActivity.CreatedBy.ShouldBeNull();
        capturedUserActivity.LastModifiedBy.ShouldBeNull();
    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_EventProcessed()
    {
        // Arrange
        var bulkImportOperationGroupDeletedEvent = new BulkImportOperationGroupDeletedEvent { Name = null };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationGroupDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldContain("deleted successfully");
    }

    [Fact]
    public async Task Handle_SetCorrectRequestUrl_When_UserServiceProvided()
    {
        // Arrange
        var testRequestUrl = "/api/bulkimportoperationgroup/delete";
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testRequestUrl);

        var handler = new BulkImportOperationGroupDeletedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationGroupDeletedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationGroupDeletedEvent = new BulkImportOperationGroupDeletedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationGroupDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.RequestUrl.ShouldBe(testRequestUrl);
    }

    [Fact]
    public async Task Handle_SetCorrectHostAddress_When_UserServiceProvided()
    {
        // Arrange
        var testHostAddress = "********";
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testHostAddress);

        var handler = new BulkImportOperationGroupDeletedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationGroupDeletedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationGroupDeletedEvent = new BulkImportOperationGroupDeletedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationGroupDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.HostAddress.ShouldBe(testHostAddress);
    }
}
