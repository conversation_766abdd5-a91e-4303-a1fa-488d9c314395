﻿const approverPreventSpecialKeys = (selector) => {
    $(selector).on('keypress', (e) => {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
        }
    });
};

$.getJSON('/json/CountryDailCode.json', function (response) {

    setTimeout(() => {
        response.countrycode.forEach(function (value) {
            $('#mobilepre').append('<option value="' + value.dial_code + '">' + value.dial_code + ' (' + value.name + ')</option>');
        });
    }, 500);
});

const validEmail = (value) => {
    return (!(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,63}$/).test(value)) ? "Invalid email" : true;
}

function addCPUser() {
    $.ajax({
        type: "GET",
        url: RootUrl + approverURL.getUsersList,
        dataType: "json",
        success: function (result) {

            if (result?.success) {
                newApproverData = result?.data;
                approvarlists(newApproverData);
            } else {
                errorNotification(result);
            }
        }
    });
}

function searchedApproverData() {
    let searchedData = $("#approverSearchInput").val();
    const $addserApproval = $("#addserApproval");

    if (searchedData) {
        const searchedApproverData = newApproverData.filter(user => user.loginName.toLowerCase().includes(searchedData.toLowerCase()));
        let filteredData = searchedApproverData.filter(a => !addedApproverData.some(b => b.id === a.id))
        approvarlists(filteredData);

        if (filteredData?.length === 0) {
            $('#addUserApproval').append(`<div class='usersList'>${noDataFount}</div>`);
        } else {
            $(".usersList").remove();
        }

        if (addedApproverData?.length) {
            const searchedNewApproverData = addedApproverData.filter(user => user.loginName.toLowerCase().includes(searchedData.toLowerCase()));

            if (searchedNewApproverData?.length === 0) {
                $addserApproval.append(`<div class='addedUsersList'>${noDataFount}</div>`);
            } else {
                $(".addedUsersList").remove();
            }
            searchedNewApproverData.forEach(function (data, index) {
                addNewApprover($addserApproval, data?.id, data?.loginName, data?.roleName, index);
            });
        }

    } else {
        let filteredData = newApproverData.filter(a => !addedApproverData.some(b => b.id === a.id))
        approvarlists(filteredData);

        if (filteredData?.length === 0) {
            $('#addUserApproval').append(`<div class='usersList'>${noDataFount}</div>`);
        } else {
            $(".usersList").remove();
        }

        if ($addserApproval.children().length !== 0) {
            if (addedApproverData?.length === 0) {
                $addserApproval.append(`<div class='addedUsersList'>${noDataFount}</div>`);

            }
        } else {
            $(".addedUsersList").remove();

            addedApproverData.forEach(function (data, index) {
                addNewApprover($addserApproval, data?.id, data?.loginName, data?.roleName, index);
            });
        }

    }


    //if ($addserApproval.children().length === 0) {

    //    if ($('#addserApprovalTag').children('.NoDataContainer').length === 0) {
    //        $('#addserApproval').append(`<div class='addedUsersList'>
    //                                        ${noDataFount}
    //                                     </div>`);
    //    }
    //} else {
    //    $('#addserApprovalTag').children('.NoDataContainer').remove();
    //}

    //if ($("#addUserApproval").children().length === 0) {

    //    if ($('#unAddedTag').children('.NoDataContainer').length === 0) {
    //        $('#addUserApproval').append(`<div class="usersList">
    //                                        ${noDataFount}
    //                                      </div>`);
    //    }
    //} else {
    //    $('#unAddedTag').children('.NoDataContainer').remove();
    //}
}

function approvarlists(newApproverData) {
    const $addUserApproval = $("#addUserApproval");
    $addUserApproval.empty();

    const $addserApproval = $("#addserApproval");
    $addserApproval.empty();

    // Check if newApproverData is empty before running the loop
    //if (newApproverData.length === 0) {
    //    $('#addUserApproval').append(`${noDataFount}`);
    //} else {
    newApproverData.forEach(function (data, index) {
        const html = `
            <div class="border border-light-subtle rounded p-2 my-2" id="user-${data.id}" data-index="${index}">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center gap-2">
                        <span><img src="/img/profile-img/user.jpg" class="img-fluid rounded-circle" width="40" /></span>
                        <div>
                            <p class="mb-0">
                                <span class="d-inline-block text-truncate fw-semibold" style="max-width:120px;width:120px">${data.loginName}</span>
                                <span class="badge bg-success rounded-pill px-2 ms-2">${data.roleName}</span>
                            </p>
                            <span class="text-primary">All group approver</span>
                        </div>
                    </div>
                    <button type="button" data-id="${data.id}" data-name="${data.loginName}" data-role="${data.roleName}" data-index="${index}" class="btn btn-primary btn-sm ADAddUser">
                        Add
                    </button>
                </div>
            </div>`;
        $addUserApproval.append(html);
    });
    //}    
}

function addNewApprover($addserApproval, userId, userName, userRole, index) {

    //if ($("#selected-user-" + userId).length === 0) {
    $(".unAddedtitle").removeClass('d-none')
    const userHtml = `
                <div class="border border-light-subtle rounded p-2 my-2" id="selected-user-${userId}">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center gap-2">
                            <span><img src="/img/profile-img/user.jpg" class="img-fluid rounded-circle" width="40" /></span>
                            <div>
                                <p class="mb-0">
                                    <span class="d-inline-block text-truncate fw-semibold" style="max-width:120px;width:120px">${userName}</span>
                                    <span class="badge bg-success rounded-pill px-2 ms-2">${userRole}</span>
                                </p>
                                <span class="text-primary">All group approver</span>
                            </div>
                        </div>
                        <button type="button" data-id="${userId}" class="btn btn-danger btn-sm removeUser">
                            Remove
                        </button>
                    </div>
                </div>`;

    $addserApproval.append(userHtml);
    $("#user-" + userId).addClass('d-none');
    $('#addserApprovalTag').show();
    $('.dataTable').removeClass('row-cols-1').addClass('row-cols-2');
    // }
}

function removeNewApprover($addserApproval, $addUserApproval, userId, userName, userRole, index) {
    $(`#selected-user-${userId}`).remove();

    const userHtml = `
        <div class="border border-light-subtle rounded p-2 my-2" id="user-${userId}" data-index="${index}">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center gap-2">
                    <span><img src="/img/profile-img/user.jpg" class="img-fluid rounded-circle" width="40" /></span>
                    <div>
                        <p class="mb-0">
                            <span class="d-inline-block text-truncate fw-semibold" style="max-width:120px;width:120px">${userName}</span>
                            <span class="badge bg-success rounded-pill px-2 ms-2">${userRole}</span>
                        </p>
                        <span class="text-primary">All group approver</span>
                    </div>
                </div>
                <button type="button" data-id="${userId}" data-name="${userName}" data-role="${userRole}" data-index="${index}" class="btn btn-primary btn-sm ADAddUser">
                    Add
                </button>
            </div>
        </div>`;

    $(`#user-${userId}`).removeClass('d-none');
    $addUserApproval.append(userHtml);
    $(".ADAddUser").text('Add').removeClass('btn-danger').addClass('btn-primary btn-sm');

    if ($addserApproval.children().length === 0) {

        $(".unAddedtitle").addClass('d-none')
        $('#addserApprovalTag').hide();
        $('.dataTable').removeClass('row-cols-2').addClass('row-cols-1');
    }
}

function GetBusinessServiceList(UserApproval = null) {
    $.ajax({
        type: "GET",
        url: RootUrl + approverURL.getBusinessServiceList,
        data: {},
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result) {
                if (result && result.length > 0) {
                    $('#selectBusinessService').append('<option value=""></option>');
                    result.forEach(item => {
                        $('#selectBusinessService').append('<option value="' + item.id + '">' + item.name + '</option>');
                    });

                    if (UserApproval && Object.keys(UserApproval)) {
                        $('#selectBusinessService').val(UserApproval.businessServiceProperties).trigger('change');
                    }
                }
            } else {
                errorNotification(result);
            }
        }
    });    
}

async function validateUserName(value, id = null) {
    const errorElement = $('#UserName-error');

    if (!value) {
        errorElement.text('Enter username')
            .addClass('field-validation-error');
        return false;
    }

    if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    let url = RootUrl + approverURL.nameExist;
    let data = {
        id: id,
        name: value
    };
    const validationResults = [
        SpecialCharValidateCustom(value), //SpecialCharValidate(value),
        ShouldNotBeginWithSpace(value),
        ShouldNotBeginWithUnderScore(value),
        OnlyNumericsValidate(value),
        ShouldNotBeginWithNumber(value),
        ShouldNotEndWithSpace(value),
        ShouldNotAllowMultipleSpace(value),
        SpaceWithUnderScore(value),
        ShouldNotEndWithUnderScore(value),
        MultiUnderScoreRegex(value),
        SpaceAndUnderScoreRegex(value),
        minMaxlength(value),
        secondChar(value),
        await IsFormNameExist(url, data, OnError)
    ];
    return CommonValidation(errorElement, validationResults);
}

async function IsFormNameExist(url, data, errorFunc) {
    return !data.name.trim() ? true : (await GetFormAsync(url, data, errorFunc)) ? "Name already exists" : true;
};

async function GetFormAsync(url, data, errorFunc) {
    return await $.get(url, data).fail(errorFunc);
};

function validateDropDown(value, errorMessage, errorElement) {
    if (!value) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}

function validateMobilePre(value) {
    const errorElement = $('#MobilePre-error');

    if (!value) {
        errorElement.text('Select country code').addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true
    }
}

function validateMobile(value) {
    const errorElement = $('#Mobile-error');

    if (!value) {
        errorElement.text('Enter mobile number')
            .addClass('field-validation-error');
        return false;
    }
    else if (value) {
        const minLength = 7;
        if (value.length < minLength) {
            errorElement.text('Mobile number must be above 6 digits')
                .addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('')
                .removeClass('field-validation-error');
            return true;
        }
    }
    else {
        errorElement.text('')
            .removeClass('field-validation-error');
        return true;
    }
}

function updateSiteType(UserApproval) {

    if (UserApproval.userType === 'Anonymous') {
        $("#profile-tab").trigger("click");
        $('#userNameId').val(UserApproval.id);
        $('#UserName').val(UserApproval.userName);
        $('#mail').val(UserApproval.email);
        $("input[name='flexCheckDefault']").prop('checked', UserApproval.isLink);
        GetBusinessServiceList(UserApproval)
        let splitNumber = UserApproval?.mobileNumber?.split(' ');
        $('#mobilenum').val(splitNumber[1])
        $('#mobilepre').val(splitNumber[0]);
        let errorElement = ['#UserName-error', '#Email-error', '#MobilePre-error', '#Mobile-error', '#BusinessService-error']
        errorElement.forEach(element => {
            $(element).text('').removeClass('field-validation-error')
        });
    } else {
        $("#home-tab").trigger("click");
        $("#AMUserType").val('CP-User');
    }
}

async function validateEmail(value, id = null) {
    const errorElement = $('#Email-error');
    let format = /^[a-zA-Z0-9._]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

    if (!value) {
        errorElement.text('Enter email')
            .addClass('field-validation-error');
        return false;
    } else if (value.length >= 321) {
        errorElement.text('Enter the value less than 320 characters')
            .addClass('field-validation-error');
        return false;
    } else if (value.length) {
        if (format.test(value) == false) {
            errorElement.text('Invalid email')
                .addClass('field-validation-error');
            return false;
        } else if (value.charAt(0) == "." || value.charAt(0) == "_") {
            errorElement.text('Invalid email')
                .addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('')
                .removeClass('field-validation-error');
            return true;
        }
    }
    else {
        errorElement.text('')
            .removeClass('field-validation-error');
        return true;
    }
    const validationResults = [await emailRegex(value)];
    return await CommonValidation(errorElement, validationResults);
}
