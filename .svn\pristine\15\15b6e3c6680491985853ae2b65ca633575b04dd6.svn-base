﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class SmtpConfigurationRepositoryMocks
{
    public static Mock<ISmtpConfigurationRepository> CreateSmtpConfigurationRepository(List<SmtpConfiguration> smtpConfigurations)
    {
        var mockSmtpConfigurationRepository = new Mock<ISmtpConfigurationRepository>();

        mockSmtpConfigurationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<SmtpConfiguration>());
        
        mockSmtpConfigurationRepository.Setup(repo => repo.AddAsync(It.IsAny<SmtpConfiguration>())).ReturnsAsync(
            (SmtpConfiguration smtpConfiguration) =>
            {
                smtpConfiguration.Id = new Fixture().Create<int>();

                smtpConfiguration.ReferenceId = new Fixture().Create<Guid>().ToString();

                smtpConfigurations.Add(smtpConfiguration);

                return smtpConfiguration;
            });

        return mockSmtpConfigurationRepository;
    }

    public static Mock<ISmtpConfigurationRepository> UpdateSmtpConfigurationRepository(List<SmtpConfiguration> smtpConfigurations)
    {
        var mockSmtpConfigurationRepository = new Mock<ISmtpConfigurationRepository>();

        mockSmtpConfigurationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(smtpConfigurations);

        mockSmtpConfigurationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => smtpConfigurations.SingleOrDefault(x => x.ReferenceId == i));

        mockSmtpConfigurationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<SmtpConfiguration>())).ReturnsAsync((SmtpConfiguration smtpConfiguration) =>
        {
            var index = smtpConfigurations.FindIndex(item => item.Id == smtpConfiguration.Id);

            smtpConfigurations[index] = smtpConfiguration;

            return smtpConfiguration;
        });

        return mockSmtpConfigurationRepository;
    }

    public static Mock<ISmtpConfigurationRepository> DeleteSmtpConfigurationRepository(List<SmtpConfiguration> smtpConfigurations)
    {
        var mockSmtpConfigurationRepository = new Mock<ISmtpConfigurationRepository>();

        mockSmtpConfigurationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(smtpConfigurations);

        mockSmtpConfigurationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => smtpConfigurations.SingleOrDefault(x => x.ReferenceId == i));

        mockSmtpConfigurationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<SmtpConfiguration>())).ReturnsAsync((SmtpConfiguration smtpConfiguration) =>
        {
            var index = smtpConfigurations.FindIndex(item => item.Id == smtpConfiguration.Id);

            smtpConfiguration.IsActive = false;

            smtpConfigurations[index] = smtpConfiguration;

            return smtpConfiguration;
        });

        return mockSmtpConfigurationRepository;
    }

    public static Mock<ISmtpConfigurationRepository> GetSmtpConfigurationRepository(List<SmtpConfiguration> smtpConfigurations)
    {
        var mockSmtpConfigurationRepository = new Mock<ISmtpConfigurationRepository>();

        mockSmtpConfigurationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(smtpConfigurations);

        mockSmtpConfigurationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => smtpConfigurations.SingleOrDefault(x => x.ReferenceId == i));

        return mockSmtpConfigurationRepository;
    }

    public static Mock<ISmtpConfigurationRepository> GetSmtpConfigurationEmptyRepository()
    {
        var mockSmtpConfigurationRepository = new Mock<ISmtpConfigurationRepository>();

        mockSmtpConfigurationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<SmtpConfiguration>());

        return mockSmtpConfigurationRepository;
    }
}