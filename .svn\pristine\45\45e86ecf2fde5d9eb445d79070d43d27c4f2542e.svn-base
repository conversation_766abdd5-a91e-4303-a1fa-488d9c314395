﻿using ContinuityPatrol.Application.Features.Workflow.Events.Publish;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.Workflow.Commands.Publish;

public class
    UpdateWorkflowPublishCommandHandler : IRequestHandler<UpdateWorkflowPublishCommand, UpdateWorkflowPublishResponse>
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;
    private readonly IWorkflowRepository _workflowRepository;

    public UpdateWorkflowPublishCommandHandler(IMapper mapper, IWorkflowRepository workflowRepository,
        IWorkflowProfileInfoRepository workflowProfileInfoRepository, ILoggedInUserService loggedInUserService,
        IPublisher publisher)
    {
        _mapper = mapper;
        _workflowRepository = workflowRepository;
        _workflowProfileInfoRepository = workflowProfileInfoRepository;
        _loggedInUserService = loggedInUserService;
        _publisher = publisher;
    }

    public async Task<UpdateWorkflowPublishResponse> Handle(UpdateWorkflowPublishCommand request,
        CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "Workflow Id");

        var eventToWorkflowUpdate = await _workflowRepository.GetByReferenceIdAsync(request.Id);

        var eventToWorkflowProfileInfoUpdate =
            await _workflowProfileInfoRepository.GetProfileIdAttachByWorkflowId(request.Id);

        if (!eventToWorkflowUpdate.CreatedBy.Equals(_loggedInUserService.UserId))
            throw new InvalidException("User Access denied.");

        if (eventToWorkflowProfileInfoUpdate != null)
        {
            eventToWorkflowProfileInfoUpdate.IsPublish = request.IsPublish;

            await _workflowProfileInfoRepository.UpdateAsync(eventToWorkflowProfileInfoUpdate);
        }

        if (eventToWorkflowUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Workflow), request.Id);

        eventToWorkflowUpdate.IsPublish = request.IsPublish;

        _mapper.Map(request, eventToWorkflowUpdate, typeof(UpdateWorkflowPublishCommand),
            typeof(Domain.Entities.Workflow));

        await _workflowRepository.UpdateAsync(eventToWorkflowUpdate);

        await _publisher.Publish(new WorkflowPublishEvent
        {
            WorkflowName = eventToWorkflowUpdate.Name,
            IsPublished = request.IsPublish
        }, cancellationToken);

        return new UpdateWorkflowPublishResponse
        {
            Message = request.IsPublish
                ? $"Workflow '{eventToWorkflowUpdate.Name}' published successfully."
                : $"Workflow '{eventToWorkflowUpdate.Name}' unpublished successfully.",

            WorkflowId = eventToWorkflowUpdate.ReferenceId
        };
    }
}