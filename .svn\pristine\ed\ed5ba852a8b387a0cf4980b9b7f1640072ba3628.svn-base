using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberComponentGroupRepository : BaseRepository<CyberComponentGroup>, ICyberComponentGroupRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public CyberComponentGroupRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<CyberComponentGroup>> ListAllAsync()
    {
        var cyberComponentGroup = base.QueryAll(x => x.IsActive);

        var componentGroup = MapCyberComponentGroup(cyberComponentGroup);

        return await componentGroup.ToListAsync();
    }
    public override Task<CyberComponentGroup> GetByReferenceIdAsync(string id)
    {
        var cyberComponentGroup = base.GetByReferenceId(id, x =>
                  x.ReferenceId.Equals(id));

        var componentGroup = MapCyberComponentGroup(cyberComponentGroup);

        return componentGroup.FirstOrDefaultAsync();
    }
    public override async Task<PaginatedResult<CyberComponentGroup>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<CyberComponentGroup> specification, string sortColumn, string sortOrder)
    {
        var componentGroup = await MapCyberComponentGroup(Entities.Specify(specification).DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);

        return componentGroup;
    }
    public override IQueryable<CyberComponentGroup> GetPaginatedQuery()
    {
        var cyberComponentGroup = base.QueryAll(x => x.IsActive);

        var componentGroup = MapCyberComponentGroup(cyberComponentGroup);

        return componentGroup.AsNoTracking().OrderByDescending(x => x.Id);
    }

    public async Task<List<CyberComponentGroup>> GetCyberComponentGroupsBySiteId(string id)
    {
        var cyberComponentGroup = base.FilterBy(x => x.SiteId.Equals(id));

        var componentGroup = MapCyberComponentGroup(cyberComponentGroup);

        return await componentGroup.ToListAsync();
    }
    public async Task<List<CyberComponentGroup>> GetComponentGroupsByComponentId(string id)
    {
        var cyberComponentGroup = base.FilterBy(x => x.ComponentProperties.Contains(id));

        var componentGroup = MapCyberComponentGroup(cyberComponentGroup);

        return await componentGroup.ToListAsync();
    }
    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.GroupName.Equals(name))
            : Entities.Where(e => e.GroupName.Equals(name)).ToList().Unique(id));
    }
    private IQueryable<CyberComponentGroup> MapCyberComponentGroup(IQueryable<CyberComponentGroup> cyberComponentGroups)
    {
        return cyberComponentGroups.Select(x => new
        {
            Site = _dbContext.Sites.FirstOrDefault(s => s.ReferenceId.Equals(x.SiteId)),
            CyberComponentGroup = x
        })
         .Select(res => new CyberComponentGroup
         {
             Id = res.CyberComponentGroup.Id,
             ReferenceId = res.CyberComponentGroup.ReferenceId,
             GroupName = res.CyberComponentGroup.GroupName,
             ComponentProperties = res.CyberComponentGroup.ComponentProperties,
             SiteId = res.Site.ReferenceId ?? res.CyberComponentGroup.SiteId,
             SiteName = res.Site.Name ?? res.CyberComponentGroup.SiteName,
             IsActive = res.CyberComponentGroup.IsActive,
             CreatedBy = res.CyberComponentGroup.CreatedBy,
             CreatedDate = res.CyberComponentGroup.CreatedDate,
             LastModifiedBy = res.CyberComponentGroup.LastModifiedBy,
             LastModifiedDate = res.CyberComponentGroup.LastModifiedDate
         });
    }

  
}
