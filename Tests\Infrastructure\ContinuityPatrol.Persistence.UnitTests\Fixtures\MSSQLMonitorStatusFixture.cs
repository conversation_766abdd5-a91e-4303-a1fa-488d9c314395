using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MSSQLMonitorStatusFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "MSSQLMonitorStatus";

    public List<MSSQLMonitorStatus> MSSQLMonitorStatusPaginationList { get; set; }
    public List<MSSQLMonitorStatus> MSSQLMonitorStatusList { get; set; }
    public MSSQLMonitorStatus MSSQLMonitorStatusDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MSSQLMonitorStatusFixture()
    {
        _fixture = new Fixture();
        
        // Configure AutoFixture to generate valid data
        _fixture.Customize<MSSQLMonitorStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
      );

        MSSQLMonitorStatusPaginationList = _fixture.CreateMany<MSSQLMonitorStatus>(20).ToList();
        MSSQLMonitorStatusList = _fixture.CreateMany<MSSQLMonitorStatus>(5).ToList();
        MSSQLMonitorStatusDto = _fixture.Create<MSSQLMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MSSQLMonitorStatus CreateMSSQLMonitorStatusWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<MSSQLMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public MSSQLMonitorStatus CreateMSSQLMonitorStatusWithWhitespace()
    {
        return CreateMSSQLMonitorStatusWithProperties(type: "  MSSQLMonitorStatus  ");
    }

    public MSSQLMonitorStatus CreateMSSQLMonitorStatusWithLongType(int length)
    {
        var longType = new string('A', length);
        return CreateMSSQLMonitorStatusWithProperties(type: longType);
    }

    public MSSQLMonitorStatus CreateMSSQLMonitorStatusWithSpecificInfraObjectId(string infraObjectId)
    {
        return CreateMSSQLMonitorStatusWithProperties(infraObjectId: infraObjectId);
    }

    public List<MSSQLMonitorStatus> CreateMultipleMSSQLMonitorStatusWithSameType(string type, int count)
    {
        var statuses = new List<MSSQLMonitorStatus>();
        for (int i = 0; i < count; i++)
        {
            statuses.Add(CreateMSSQLMonitorStatusWithProperties(type: type, isActive: true));
        }
        return statuses;
    }

    public List<MSSQLMonitorStatus> CreateMSSQLMonitorStatusWithMixedActiveStatus(string type)
    {
        return new List<MSSQLMonitorStatus>
        {
            CreateMSSQLMonitorStatusWithProperties(type: type, isActive: true),
            CreateMSSQLMonitorStatusWithProperties(type: type, isActive: false),
            CreateMSSQLMonitorStatusWithProperties(type: type, isActive: true)
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "MSSQLMonitorStatus", "MSSQL", "SQLServer", "DatabaseMonitorStatus" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
        public static readonly string[] SpecialCharacterTypes = { "Type@#$%", "Type with spaces", "Type_with_underscores", "Type-with-dashes" };
    }
}
