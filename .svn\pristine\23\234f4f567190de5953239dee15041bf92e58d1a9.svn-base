﻿namespace ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Update;

public class
    UpdateApprovalMatrixCommandHandler : IRequestHandler<UpdateApprovalMatrixCommand, UpdateApprovalMatrixResponse>
{
    private readonly IApprovalMatrixRepository _approvalMatrixRepository;
    private readonly IMapper _mapper;

    public UpdateApprovalMatrixCommandHandler(IMapper mapper, IApprovalMatrixRepository approvalMatrixRepository)
    {
        _mapper = mapper;
        _approvalMatrixRepository = approvalMatrixRepository;
    }

    public async Task<UpdateApprovalMatrixResponse> Handle(UpdateApprovalMatrixCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _approvalMatrixRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.ApprovalMatrix), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateApprovalMatrixCommand),
            typeof(Domain.Entities.ApprovalMatrix));

        await _approvalMatrixRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateApprovalMatrixResponse
        {
            Message = Message.Update(nameof(Domain.Entities.ApprovalMatrix), eventToUpdate.Name),

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}