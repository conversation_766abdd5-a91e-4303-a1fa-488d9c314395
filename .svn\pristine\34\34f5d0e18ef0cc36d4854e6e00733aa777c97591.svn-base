﻿namespace ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetDetail;

public class GetAlertReceiverDetailQueryHandler : IRequestHandler<GetAlertReceiverDetailQuery, AlertReceiverDetailVm>
{
    private readonly IAlertReceiverRepository _alertReceiverRepository;
    private readonly IMapper _mapper;

    public GetAlertReceiverDetailQueryHandler(IMapper mapper, IAlertReceiverRepository alertReceiverRepository)
    {
        _mapper = mapper;
        _alertReceiverRepository = alertReceiverRepository;
    }

    public async Task<AlertReceiverDetailVm> Handle(GetAlertReceiverDetailQuery request,
        CancellationToken cancellationToken)
    {
        var alertReceiver = await _alertReceiverRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(alertReceiver, nameof(Domain.Entities.AlertReceiver),
            new NotFoundException(nameof(Domain.Entities.AlertReceiver), request.Id));

        var alertReceiverDto = _mapper.Map<AlertReceiverDetailVm>(alertReceiver);

        return alertReceiverDto;
    }
}