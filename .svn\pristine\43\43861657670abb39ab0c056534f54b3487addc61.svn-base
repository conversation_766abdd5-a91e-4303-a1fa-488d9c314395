﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowCategory.Events.PaginatedView;
using ContinuityPatrol.Domain.ViewModels.WorkflowCategoryModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class ActionBuilderControllerTests
    {
        private readonly Mock<IWorkflowCategoryService> _mockWorkflowCategoryService =new();
        private readonly Mock<ILogger<ActionBuilderController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private ActionBuilderController _controller;

        public ActionBuilderControllerTests()
        {
            Initialize();
        }
        internal void Initialize()
        {
            _controller = new ActionBuilderController(
                _mockWorkflowCategoryService.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockPublisher.Object);
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ShouldReturnViewWithSortedCategories()
        {
            // Arrange
            var categories = new List<WorkflowCategoryListVm>
            {
                new() { Name = "ZCategory" },
                new() { Name = "ACategory" },
                new() { Name = "MCategory" }
            };
            _mockWorkflowCategoryService.Setup(s => s.GetWorkflowCategoryList())
                .ReturnsAsync(categories);

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            var model = Assert.IsType<List<WorkflowCategoryListVm>>(result.Model);
            Assert.Equal(3, model.Count);
            Assert.Equal("ACategory", model[0].Name);
            Assert.Equal("MCategory", model[1].Name);
            Assert.Equal("ZCategory", model[2].Name);

            // Verify publisher was called
            _mockPublisher.Verify(p => p.Publish(It.IsAny<WorkflowCategoryPaginatedEvent>(), default), Times.Once);
        }

        [Fact]
        public async Task WorkflowCategoryList_ShouldReturnJsonResult()
        {
            // Arrange
            var categories = new List<WorkflowCategoryListVm>
            {
                new() { Name = "Category1" },
                new() { Name = "Category2" }
            };
            _mockWorkflowCategoryService.Setup(s => s.GetWorkflowCategoryList())
                .ReturnsAsync(categories);

            // Act
            var result = await _controller.WorkflowCategoryList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = Assert.IsType<List<WorkflowCategoryListVm>>(jsonResult.Value);
            Assert.Equal(2, resultValue.Count);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldCreateWorkflowCategory()
        {
            // Arrange
            var workflowCategory = new AutoFixture.Fixture().Create<WorkflowCategoryViewModel>();

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateWorkflowCategoryCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateWorkflowCategoryCommand>(workflowCategory))
                .Returns(createCommand);
            _mockWorkflowCategoryService.Setup(s => s.CreateAsync(createCommand))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(workflowCategory);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = Assert.IsType<BaseResponse>(jsonResult.Value);
            Assert.True(resultValue.Success);
            Assert.Equal("Created successfully", resultValue.Message);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldUpdateWorkflowCategory()
        {
            // Arrange
            var workflowCategory = new AutoFixture.Fixture().Create<WorkflowCategoryViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateWorkflowCategoryCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateWorkflowCategoryCommand>(workflowCategory))
                .Returns(updateCommand);
            _mockWorkflowCategoryService.Setup(s => s.UpdateAsync(updateCommand))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(workflowCategory);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = Assert.IsType<BaseResponse>(jsonResult.Value);
            Assert.True(resultValue.Success);
            Assert.Equal("Updated successfully", resultValue.Message);
        }

        [Fact]
        public async Task Delete_ShouldRedirectToPostView()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockWorkflowCategoryService.Setup(s => s.DeleteAsync(id))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldRedirectToList_WhenValidationExceptionOccurs()
        {
            // Arrange
            var workflowCategory = new AutoFixture.Fixture().Create<WorkflowCategoryViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateWorkflowCategoryCommand();
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Name", "Name is required"));
            var validationException = new ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<CreateWorkflowCategoryCommand>(workflowCategory))
                .Returns(createCommand);
            _mockWorkflowCategoryService.Setup(s => s.CreateAsync(createCommand))
                .ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(workflowCategory);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldRedirectToList_WhenGeneralExceptionOccurs()
        {
            // Arrange
            var workflowCategory = new AutoFixture.Fixture().Create<WorkflowCategoryViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateWorkflowCategoryCommand();
            var exception = new Exception("Database connection failed");

            _mockMapper.Setup(m => m.Map<CreateWorkflowCategoryCommand>(workflowCategory))
                .Returns(createCommand);
            _mockWorkflowCategoryService.Setup(s => s.CreateAsync(createCommand))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.CreateOrUpdate(workflowCategory);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task Delete_ShouldRedirectToList_WhenExceptionOccurs()
        {
            // Arrange
            var id = "1";
            var exception = new Exception("Database connection failed");
            _mockWorkflowCategoryService.Setup(s => s.DeleteAsync(id))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task Delete_ShouldCallRouteToPostView_WhenSuccessful()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockWorkflowCategoryService.Setup(s => s.DeleteAsync(id))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            // This indirectly tests RouteToPostView method with Success = true
        }

        [Fact]
        public async Task Delete_ShouldCallRouteToPostView_WhenResponseIndicatesFailure()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Success = false, Message = "Delete failed" };
            _mockWorkflowCategoryService.Setup(s => s.DeleteAsync(id))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            // This indirectly tests RouteToPostView method with Success = false
        }
    }
}
