﻿using ContinuityPatrol.Application.Constants;

namespace ContinuityPatrol.Application.Features.Company.Queries.GetDetail;

public class GetCompanyDetailQueryHandler : IRequestHandler<GetCompanyDetailQuery, CompanyDetailVm>
{
    private readonly ICompanyRepository _companyRepository;
    private readonly IMapper _mapper;

    public GetCompanyDetailQueryHandler(IMapper mapper, ICompanyRepository companyRepository)
    {
        _mapper = mapper;
        _companyRepository = companyRepository;
    }

    public async Task<CompanyDetailVm> Handle(GetCompanyDetailQuery request, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, nameof(request.Id), ErrorMessage.Company.CompanyIdCannotBeInvalid);

        var company = await _companyRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(company, nameof(Domain.Entities.Company), new NotFoundException(nameof(Domain.Entities.Company), request.Id));

        var companyDetailDto = _mapper.Map<CompanyDetailVm>(company);

        if (companyDetailDto.IsParent)
        {
            companyDetailDto.ParentName = "NA";

            return companyDetailDto;
        }
        Guard.Against.InvalidGuidOrEmpty(companyDetailDto.ParentId, nameof(companyDetailDto.ParentId), ErrorMessage.Company.ParentCompanyIdCannotBeEmpty);

        var parentCompany = await _companyRepository.GetByReferenceIdAsync(companyDetailDto.ParentId);
        companyDetailDto.ParentName = parentCompany?.DisplayName ?? string.Empty;

        return companyDetailDto;
    }
}