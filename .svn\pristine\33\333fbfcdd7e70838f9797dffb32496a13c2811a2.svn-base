﻿using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.Features.BusinessService.Queries.GetBusinessServiceDiagramDetail;

public class GetBusinessServiceDiagramDetailQueryHandler : IRequestHandler<GetBusinessServiceDiagramDetailQuery,
    GetBusinessServiceDiagramDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IServerViewRepository _serverViewRepository;
    private readonly IDatabaseViewRepository _databaseViewRepository;
    private readonly IReplicationViewRepository _replicationViewRepository;

    public GetBusinessServiceDiagramDetailQueryHandler(IMapper mapper,
        IBusinessServiceRepository businessServiceRepository,
        IBusinessFunctionRepository businessFunctionRepository,
        IInfraObjectRepository infraObjectRepository,
        IServerViewRepository serverViewRepository,
        IDatabaseViewRepository databaseViewRepository, IReplicationViewRepository replicationViewRepository)
    {
        _mapper = mapper;
        _businessServiceRepository = businessServiceRepository;
        _businessFunctionRepository = businessFunctionRepository;
        _infraObjectRepository = infraObjectRepository;
        _serverViewRepository = serverViewRepository;
        _databaseViewRepository = databaseViewRepository;
        _replicationViewRepository = replicationViewRepository;
    }

    public async Task<GetBusinessServiceDiagramDetailVm> Handle(GetBusinessServiceDiagramDetailQuery request,
        CancellationToken cancellationToken)
    {
        var businessService = await _businessServiceRepository.GetFilterByReferenceIdAsync(request.BusinessServiceId);

        Guard.Against.NullOrDeactive(businessService, nameof(Domain.Entities.BusinessService),
            new NotFoundException(nameof(Domain.Entities.BusinessService), request.BusinessServiceId));

        var businessServiceDto = _mapper.Map<GetBusinessServiceDiagramDetailVm>(businessService);

        var function = await _businessFunctionRepository.GetFilterByBusinessServiceId(businessService.ReferenceId);

        businessServiceDto.BusinessFunctionDataLag =
            _mapper.Map<List<BusinessFunctionDataLag>>(function);

        var businessFunctionIds = function.Where(x=>x.ReferenceId.IsNotNullOrWhiteSpace()).Select(x => x.ReferenceId).ToList();

        var infra = await _infraObjectRepository.GetByBusinessFunctionIds(businessFunctionIds);

        var infraObjectDto = _mapper.Map<List<InfraObjectDataLag>>(infra);

        var subTypes = new List<string> { "oracle", "oracle-rac" };

        var racProperties = infraObjectDto
            .Where(x => x.SubType.IsNotNullOrWhiteSpace() &&
                        subTypes.Contains(x.SubType.ToLower()) &&
                        x.ReplicationTypeName.ToLower() == "native replication-oracle-rac")
            .ToList();

        var infraProperties = infraObjectDto
            .Where(x => racProperties.All(r => r.InfraObjectId != x.InfraObjectId))
            .ToList();

        if (infraObjectDto.Count > 0)
        {
            if (racProperties.Count > 0)
            {
                await ProcessRacPropertiesAsync(racProperties);
            }

            if (infraProperties.Count > 0)
            {
                await ProcessServerPropertiesAsync(infraProperties);
                await ProcessDatabasePropertiesAsync(infraProperties);
            }

            await ProcessSrmServersAsync(infraProperties);
            await ProcessClusterServersAsync(infraProperties);
            await ProcessReplicationAsync(infraObjectDto);
        }

        businessServiceDto.BusinessFunctionDataLag.ForEach(bf =>
            bf.InfraObjectDataLag.AddRange(infraObjectDto.Where(infraObject => infraObject.BusinessFunctionId.Equals(bf.BusinessFunctionId))));

        return businessServiceDto;
    }

    private async Task ProcessReplicationAsync(List<InfraObjectDataLag> infraObjectDto)
    {
        var infraReplicationComponent = infraObjectDto
            .Where(x => x.ReplicationProperties.IsNotNullOrWhiteSpace())
            .ToDictionary(
                kvp => kvp.InfraObjectId,
                kvp => GetPropertyNames(JObject.Parse(kvp.ReplicationProperties))
                    .SelectMany(repProps => new[]
                    {
                        JObject.Parse(kvp.ReplicationProperties)
                            .SelectToken($"{repProps}.id")?.ToString()
                        ?? JObject.Parse(kvp.ReplicationProperties)
                            .SelectToken($"{repProps}.Id")?.ToString()
                    })
                    .Where(id => id != null)
                    .ToList()
            );

        var replicationIds = infraReplicationComponent.Values
            .Where(x => x != null)
            .SelectMany(x => x)
            .Distinct()
            .ToList();

        var servers = replicationIds.Count > 0
            ? await _replicationViewRepository.GetByReplicationIdsAsync(replicationIds)
            : new List<ReplicationView>();

        var replicationDto = _mapper.Map<List<ReplicationDtoVm>>(servers);

        foreach (var infra in infraObjectDto)
        {
            var infraReplicationIds = infraReplicationComponent.GetValueOrDefault(infra.InfraObjectId, new List<string>());

            var matchingReplications = replicationDto.Where(ser => infraReplicationIds.Contains(ser.Id)).ToList();

            infra.ReplicationDtoVm.AddRange(matchingReplications);
        }



        //var infraReplicationComponent = infraObjectDto
        //    .Where(x => x.ReplicationTypeId.IsNotNullOrWhiteSpace())
        //    .ToDictionary(
        //        kvp => kvp.InfraObjectId, kvp => kvp);

        //var replicationDto = _mapper.Map<List<ReplicationDataLag>>(infraReplicationComponent.Values);

        //foreach (var infra in infraObjectDto)
        //{
        //    var infraServerIds = infraReplicationComponent[infra.InfraObjectId];

        //    var matchingServers = replicationDto.FirstOrDefault(ser => infraServerIds.PRReplicationId.IsNotNullOrWhiteSpace() && infraServerIds.PRReplicationId.Equals(ser.PRReplicationId));

        //    infra.ReplicationDataLag = matchingServers;
        //}

        //return Task.CompletedTask;
    }


    private async Task ProcessRacPropertiesAsync(List<InfraObjectDataLag> infraObjectDto)
    {
        var infraRacDatabaseComponent = infraObjectDto
            .Where(x => x.DatabaseProperties.IsNotNullOrWhiteSpace())
            .ToDictionary(
                kvp => kvp.InfraObjectId,
                kvp =>
                {
                    var databaseJson = JObject.Parse(kvp.DatabaseProperties);
                    return GetPropertyNames(databaseJson)
                        .SelectMany(serverProps =>
                        {
                            var id = databaseJson.SelectToken($"{serverProps}.id")?.ToString()
                                     ?? databaseJson.SelectToken($"{serverProps}.Id")?.ToString();
                            return id.IsNotNullOrWhiteSpace() ? id!.Split(',') : Array.Empty<string>();
                        })
                        .Where(id => id != null)
                        .ToList();
                });


        var databaseIds = infraRacDatabaseComponent.Values.SelectMany(x => x).Distinct().ToList();

        var databases = databaseIds.Count > 0 
            ? await _databaseViewRepository.GetByDatabaseIdsAsync(databaseIds)
            : new List<DatabaseView>();

        var serverIdWithDatabaseId = databases.ToDictionary(x => x.ServerId, x => x);

        var databaseDto = _mapper.Map<List<DatabaseDtoVm>>(databases);

        foreach (var infra in infraObjectDto)
        {
            var infraServerIds = infraRacDatabaseComponent.GetValueOrDefault(infra.InfraObjectId, new List<string>());

            var matchingServers = databaseDto.Where(ser => infraServerIds.Contains(ser.DatabaseId)).ToList();

            infra.DatabaseDtoVm.AddRange(matchingServers);
        }

        var serverIds = serverIdWithDatabaseId.Keys.ToList();

        var servers = serverIds.Count > 0 
            ? await _serverViewRepository.GetByServerIdsAsync(serverIds)
            :new List<ServerView>();

        var serverDto = _mapper.Map<List<ServerDtoVm>>(servers);

        foreach (var infra in infraObjectDto)
        {
            var infraServerIds = infraRacDatabaseComponent.GetValueOrDefault(infra.InfraObjectId, new List<string>());

            var matchingServers = serverDto
                .Where(ser => infraServerIds.Contains(ser.ServerId))
                .Select(ser =>
                {
                    ser.NodeName = serverIdWithDatabaseId[ser.ServerId].Name;
                    return ser;
                })
                .ToList();

            infra.ServerDtoVm.AddRange(matchingServers);
        }
    }

    private async Task ProcessDatabasePropertiesAsync(List<InfraObjectDataLag> infraObjectDto)
    {
        var infraDatabaseComponent = infraObjectDto
            .Where(x => x.DatabaseProperties.IsNotNullOrWhiteSpace())
            .ToDictionary(
                kvp => kvp.InfraObjectId,
                kvp => GetPropertyNames(JObject.Parse(kvp.DatabaseProperties))
                    .SelectMany(serverProps => new[]
                    {
                        JObject.Parse(kvp.DatabaseProperties)
                            .SelectToken($"{serverProps}.id")?.ToString()
                        ?? JObject.Parse(kvp.DatabaseProperties)
                            .SelectToken($"{serverProps}.Id")?.ToString()
                    })
                    .Where(id => id != null)
                    .ToList()
            );

        var databaseIds = infraDatabaseComponent.Values.SelectMany(x => x).ToList();

        var databases = databaseIds.Count > 0
            ? await _databaseViewRepository.GetByDatabaseIdsAsync(databaseIds)
            : new List<DatabaseView>();

        var databaseDto = _mapper.Map<List<DatabaseDtoVm>>(databases);

        foreach (var infra in infraObjectDto)
        {
            var infraServerIds = infraDatabaseComponent.GetValueOrDefault(infra.InfraObjectId, new List<string>());

            var matchingServers = databaseDto.Where(ser => infraServerIds.Contains(ser.DatabaseId)).ToList();

            infra.DatabaseDtoVm.AddRange(matchingServers);
        }
    }

    private async Task ProcessServerPropertiesAsync(List<InfraObjectDataLag> infraObjectDto)
    {
        var infraServerComponent = infraObjectDto
            .Where(x => x.ServerProperties.IsNotNullOrWhiteSpace())
            .ToDictionary(
                kvp => kvp.InfraObjectId,
                kvp => GetPropertyNames(JObject.Parse(kvp.ServerProperties))
                    .SelectMany(serverProps => new[]
                    {
                        JObject.Parse(kvp.ServerProperties)
                            .SelectToken($"{serverProps}.id")?.ToString()
                        ?? JObject.Parse(kvp.ServerProperties)
                            .SelectToken($"{serverProps}.Id")?.ToString()
                    })
                    .Where(id => id != null)
                    .ToList()
            );

        var serverIds = infraServerComponent.Values.Where(x=> x !=null).SelectMany(x => x).ToList();

        var servers = serverIds.Count > 0 
            ? await _serverViewRepository.GetByServerIdsAsync(serverIds)
            : new List<ServerView>();

        var serverDto = _mapper.Map<List<ServerDtoVm>>(servers);

        foreach (var infra in infraObjectDto)
        {
            var infraServerIds = infraServerComponent.GetValueOrDefault(infra.InfraObjectId, new List<string>());

            var matchingServers = serverDto.Where(ser => infraServerIds.Contains(ser.ServerId)).ToList();

            infra.ServerDtoVm.AddRange(matchingServers);
        }
    }

    private async Task ProcessSrmServersAsync(List<InfraObjectDataLag> infraObjectDto)
    {
        var infraSrmServerComponent = infraObjectDto
            .Where(x => x.ServerProperties.IsNotNullOrWhiteSpace())
            .ToDictionary(
                kvp => kvp.InfraObjectId,
                kvp =>
                {
                    var serverJson = JObject.Parse(kvp.ServerProperties);

                    return serverJson.SelectToken("SRMServer") is JArray srmServerList
                        ? srmServerList
                            .Select(srm => srm["id"]?.ToString())
                            .Where(id => id.IsNotNullOrWhiteSpace())
                            .ToList()
                        : new List<string>();
                }
            );

        var serverIds = infraSrmServerComponent.Values.SelectMany(x => x).ToList();

        var servers = serverIds.Count > 0 
            ? await _serverViewRepository.GetByServerIdsAsync(serverIds)
            : new List<ServerView>();

        var serverDto = _mapper.Map<List<ServerDtoVm>>(servers);

        foreach (var infra in infraObjectDto)
        {
            var infraServerIds = infraSrmServerComponent.GetValueOrDefault(infra.InfraObjectId, new List<string>());

            var matchingServers = serverDto.Where(ser => infraServerIds.Contains(ser.ServerId)).ToList();

            matchingServers.ForEach(x => x.Type = "SRMServer");

            infra.ServerDtoVm.AddRange(matchingServers);
        }
    }

    private async Task ProcessClusterServersAsync(List<InfraObjectDataLag> infraObjectDto)
    {
        var infraClusterServerComponent = infraObjectDto
            .Where(x => x.ServerProperties.IsNotNullOrWhiteSpace())
            .ToDictionary(
                kvp => kvp.InfraObjectId,
                kvp =>
                {
                    var serverJson = JObject.Parse(kvp.ServerProperties);

                    var isCluster = serverJson.SelectToken("$.isCluster")?.ToString() ?? string.Empty;

                    if (!isCluster.IsNotNullOrWhiteSpace() || isCluster.ToLower() != "true")
                        return new List<string>();

                    return serverJson.SelectToken("$.clusters") is JArray clusterJArray
                        ? clusterJArray
                            .Select(cluster => cluster["id"]?.ToString())
                            .Where(id => id.IsNotNullOrWhiteSpace())
                            .ToList()
                        : new List<string>();
                }
            );

        var serverIds = infraClusterServerComponent.Values.SelectMany(x => x).ToList();

        var servers = serverIds.Count > 0
            ? await _serverViewRepository.GetByServerIdsAsync(serverIds)
            : new List<ServerView>();

        var serverDto = _mapper.Map<List<ServerDtoVm>>(servers);

        foreach (var infra in infraObjectDto)
        {
            var infraServerIds = infraClusterServerComponent.GetValueOrDefault(infra.InfraObjectId, new List<string>());

            var matchingServers = serverDto.Where(ser => infraServerIds.Contains(ser.ServerId)).ToList();

            infra.ServerDtoVm.AddRange(matchingServers);
        }
    }


    private static List<string> GetPropertyNames(JObject json)
    {
        var propertyNames = new List<string>();
        foreach (var property in json.Properties()) propertyNames.Add(property.Name);
        return propertyNames;
    }
}

#region OldCode

//if(infraProperties.Count > 0)
//{
//    await ProcessServerPropertiesAsync(infraProperties);

//    await ProcessDatabasePropertiesAsync(infraProperties);
//}

//await ProcessSrmServersAsync(infraProperties);
//await ProcessClusterServersAsync(infraProperties);






//foreach (var functionDataLag in businessServiceDto.BusinessFunctionDataLag)
//{
//    var infraObjects = await _infraObjectRepository
//        .GetInfraObjectByBusinessFunctionId(functionDataLag.BusinessFunctionId);

//    var infraObjectDataLagListVm = _mapper.Map<List<InfraObjectDataLag>>(infraObjects);

//    foreach (var infraObjectDataLag in infraObjectDataLagListVm)
//    {
//        if (infraObjectDataLag.SubType.IsNotNullOrWhiteSpace() &&
//            infraObjectDataLag.SubType.ToLower().Equals("oracle") && infraObjectDataLag.ReplicationTypeName
//                .ToLower().Equals("native replication-oracle-rac"))
//        {
//            await ProcessRacPropertiesAsync(infraObjectDataLag);
//        }
//        else
//        {
//            await ProcessServerPropertiesAsync(infraObjectDataLag);

//            await ProcessDatabasePropertiesAsync(infraObjectDataLag);
//        }

//        await ProcessSrmServersAsync(infraObjectDataLag);
//        await ProcessClusterServersAsync(infraObjectDataLag);
//        infraObjectDataLag.ReplicationDataLag = _mapper.Map<ReplicationDataLag>(infraObjectDataLag);

//        functionDataLag.InfraObjectDataLag.Add(infraObjectDataLag);
//    }
//}

#endregion
#region ProcessRacPropertiesAsync OldCode

//if (infraObjectDto.DatabaseProperties.IsNullOrWhiteSpace()) return;

//var databaseJson = JObject.Parse(infraObjectDto.DatabaseProperties);

//var serverPropertyNames = GetPropertyNames(databaseJson);

//foreach (var serverProps in serverPropertyNames)
//{
//    var id = databaseJson.SelectToken($"{serverProps}.id")?.ToString()
//             ?? databaseJson.SelectToken($"{serverProps}.Id")?.ToString();

//    if (id.IsNotNullOrWhiteSpace())
//    {
//        var databaseIds = id.IsNotNullOrWhiteSpace()
//            ? id?.Split(',')
//            : Array.Empty<string>();

//        foreach (var databaseId in databaseIds!)
//        {
//            var databaseDto = await _databaseRepository.GetByReferenceIdAsync(databaseId);

//            if (databaseDto is not null)
//            {
//                var data = _mapper.Map<DatabaseDtoVm>(databaseDto);

//                data.NodeName = databaseDto.Name;

//                data.Type = serverProps;

//                data.OracleSID = GetJsonProperties.GetJsonDatabaseSidValue(databaseDto.Properties);

//                infraObjectDto.DatabaseDtoVm.AddRangeAsync(data);

//                var server = await _serverRepository.GetByReferenceIdAsync(databaseDto.ServerId);

//                if (server is not null)
//                {
//                    var serverDto = _mapper.Map<ServerDtoVm>(server);

//                    serverDto.NodeName = databaseDto.Name;

//                    infraObjectDto.ServerDtoVm.AddRangeAsync(serverDto);
//                }
//            }
//        }
//    }
//}

#endregion
#region ProcessClusterServersAsync OldCode
//if (infraObjectDto.ServerProperties.IsNullOrWhiteSpace()) return;

//var serverJson = JObject.Parse(infraObjectDto.ServerProperties);

//var isCluster = serverJson.SelectToken("$.isCluster")?.ToString() ?? string.Empty;

//if (isCluster.IsNotNullOrWhiteSpace() && isCluster.ToLower() == "true")
//{
//    var clustersServerList = serverJson.SelectToken("$.clusters");

//    if (clustersServerList is not null)
//    {
//        var clusterJArray = clustersServerList as JArray;

//        var clusterType = serverJson.SelectToken("$.clusterType")?.ToString();

//        foreach (var cluster in clusterJArray!)
//        {
//            var serverId = cluster["id"]?.ToString();

//            var server = serverId.IsNotNullOrWhiteSpace()
//                ? await _serverRepository.GetByReferenceIdAsync(serverId)
//                : null;

//            if (server is not null)
//            {
//                var serverDtl = _mapper.Map<ServerDtoVm>(server);

//                serverDtl.Type = clusterType;

//                infraObjectDto.ServerDtoVm.AddRangeAsync(serverDtl);
//            }
//        }
//    }
//}


#endregion
#region ProcessSrmServersAsync OldCode

//if (infraObjectDto.ServerProperties.IsNullOrWhiteSpace()) return;

//var serverJson = JObject.Parse(infraObjectDto.ServerProperties);

//var srmServerList = serverJson.SelectToken("SRMServer");

//if (srmServerList is not null)
//{
//    var srmJArray = srmServerList as JArray;

//    foreach (var srm in srmJArray!)
//    {
//        var serverId = srm["id"]?.ToString();

//        var server = serverId.IsNotNullOrWhiteSpace()
//            ? await _serverRepository.GetByReferenceIdAsync(serverId)
//            : null;

//        if (server is not null)
//        {
//            var serverDtl = _mapper.Map<ServerDtoVm>(server);

//            serverDtl.Type = "SRMServer";

//            infraObjectDto.ServerDtoVm.AddRangeAsync(serverDtl);
//        }
//    }
//}

#endregion
#region ProcessServerPropertiesAsync OldCode

//if (infraObjectDto.ServerProperties.IsNullOrWhiteSpace()) return;

//var serverJson = JObject.Parse(infraObjectDto.ServerProperties);
//var serverPropertyNames = GetPropertyNames(serverJson);

//foreach (var serverProps in serverPropertyNames)
//{
//    var id = serverJson.SelectToken($"{serverProps}.id")?.ToString()
//             ?? serverJson.SelectToken($"{serverProps}.Id")?.ToString();

//    if (id.IsNotNullOrWhiteSpace())
//    {
//        var server = await _serverRepository.GetByReferenceIdAsync(id);

//        if (server is not null)
//        {
//            var serverDto = _mapper.Map<ServerDtoVm>(server);

//            infraObjectDto.ServerDtoVm.AddRangeAsync(serverDto);
//        }
//    }
//}

#endregion
#region ProcessDatabasePropertiesAsync OldCode

//if (infraObjectDto.DatabaseProperties.IsNullOrWhiteSpace()) return;

//var databaseJson = JObject.Parse(infraObjectDto.DatabaseProperties);

//var databasePropertyNames = GetPropertyNames(databaseJson);

//foreach (var databaseProps in databasePropertyNames)
//{
//    var id = databaseJson.SelectToken($"{databaseProps}.id")?.ToString()
//             ?? databaseJson.SelectToken($"{databaseProps}.Id")?.ToString();


//    if (id.IsNotNullOrWhiteSpace())
//    {
//        var databaseDto = await _databaseRepository.GetByReferenceIdAsync(id);

//        if (databaseDto is not null)
//        {
//            var database = _mapper.Map<DatabaseDtoVm>(databaseDto);

//            infraObjectDto.DatabaseDtoVm.AddRangeAsync(database);
//        }
//    }
//}

#endregion
