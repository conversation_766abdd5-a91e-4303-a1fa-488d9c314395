﻿using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetType;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;

namespace ContinuityPatrol.Application.UnitTests.Features.HeatMapStatus.Queries;

public class GetHeatMapStatusTypeQueryHandlerTests : IClassFixture<HeatMapStatusFixture>, IClassFixture<InfraObjectFixture>
{
    private readonly HeatMapStatusFixture _heatMapStatusFixture;

    private Mock<IHeatMapStatusViewRepository> _mockHeatMapStatusRepository;

    private readonly Mock<IInfraObjectRepository> _mockInfraObjectRepository;

    private readonly GetHeatMapStatusTypeQueryHandler _handler;
    
    public GetHeatMapStatusTypeQueryHandlerTests(HeatMapStatusFixture heatMapStatusFixture, InfraObjectFixture infraObjectFixture)
    {
        _heatMapStatusFixture = heatMapStatusFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

       // _mockHeatMapStatusRepository = HeatMapStatusRepositoryMocks.GetHeatMapStatusTypeRepository(_heatMapStatusFixture.HeatMapStatusList);

        _mockInfraObjectRepository = InfraObjectRepositoryMocks.GetPaginatedInfraObjectRepository(infraObjectFixture.InfraObjects);

        _handler = new GetHeatMapStatusTypeQueryHandler(_heatMapStatusFixture.Mapper, _mockHeatMapStatusRepository.Object);

        _heatMapStatusFixture.HeatMapStatusList[0].InfraObjectId =  infraObjectFixture.InfraObjects[0].ReferenceId;
    }

    [Fact]
    public async Task Handle_Return_Valid_HeatMapStatusType()
    {
        var result = await _handler.Handle(new GetHeatMapStatusTypeQuery { Type = _heatMapStatusFixture.HeatMapStatusList[0].HeatmapType }, CancellationToken.None);

        result.ShouldBeOfType<List<HeatMapStatusListVm>>();

        result[0].Id.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].ReferenceId);
        result[0].BusinessServiceId.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].BusinessServiceId);
        result[0].BusinessServiceName.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].BusinessServiceName);
        result[0].BusinessFunctionId.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].BusinessFunctionId);
        result[0].BusinessFunctionName.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].BusinessFunctionName);
        result[0].InfraObjectId.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].InfraObjectId);
        result[0].InfraObjectName.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].InfraObjectName);
        result[0].EntityId.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].EntityId);
        //result[0].HeatmapType.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].HeatmapType);
        //result[0].HeatmapStatus.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].HeatmapStatus);
        //result[0].IsAffected.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].IsAffected);
        result[0].ErrorMessage.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].ErrorMessage);
    }

    [Fact]
    public async Task Handle_ReturnEmptyType_When_NoRecords()
    {
        _mockHeatMapStatusRepository = HeatMapStatusRepositoryMocks.GetHeatMapStatusEmptyRepository();

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var handler = new GetHeatMapStatusTypeQueryHandler(_heatMapStatusFixture.Mapper, _mockHeatMapStatusRepository.Object);

        var result = await handler.Handle(new GetHeatMapStatusTypeQuery { Type = _heatMapStatusFixture.HeatMapStatusList[0].HeatmapType }, CancellationToken.None);
        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetHeatMapStatusTypeQuery { Type = _heatMapStatusFixture.HeatMapStatusList[0].HeatmapType }, CancellationToken.None);

        _mockHeatMapStatusRepository.Verify(x => x.GetHeatMapStatusType(It.IsAny<string>()), Times.Once);
    }
}