﻿namespace ContinuityPatrol.Application.Features.ServerSubType.Queries.GetNameUnique;

public class GetServerSubTypeNameUniqueQueryHandler :IRequestHandler<GetServerSubTypeNameUniqueQuery, bool>
{
    private readonly IServerSubTypeRepository _serverSubTypeRepository;

    public GetServerSubTypeNameUniqueQueryHandler(IServerSubTypeRepository serverSubTypeRepository)
    {
        _serverSubTypeRepository = serverSubTypeRepository;
    }
    public async Task<bool> Handle(GetServerSubTypeNameUniqueQuery request, CancellationToken cancellation)
    {
        return await _serverSubTypeRepository.IsServerSubTypeExist(request.SubType, request.SubTypeId);
    }
}
