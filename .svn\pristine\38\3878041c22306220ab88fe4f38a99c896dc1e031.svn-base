﻿namespace ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetNameUnique;

public class GetFormTypeCategoryNameUniqueQueryHandler : IRequestHandler<GetFormTypeCategoryNameUniqueQuery, bool>
{
    private readonly IFormTypeCategoryRepository _formTypeCategoryRepository;

    public GetFormTypeCategoryNameUniqueQueryHandler(IFormTypeCategoryRepository formTypeCategoryRepository)
    {
        _formTypeCategoryRepository = formTypeCategoryRepository;
    }

    public async Task<bool> Handle(GetFormTypeCategoryNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _formTypeCategoryRepository.IsFormTypeCategoryNameExist(request.Name, request.Id);
    }
}