﻿using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessFunction.Commands;

public class UpdateBusinessFunctionTests : IClassFixture<BusinessFunctionFixture>
{
    private readonly BusinessFunctionFixture _businessFunctionFixture;
    private readonly Mock<IBusinessFunctionRepository> _mockBusinessFunctionRepository;
    private readonly UpdateBusinessFunctionCommandHandler _handler;

    public UpdateBusinessFunctionTests(BusinessFunctionFixture businessFunctionFixture)
    {
        _businessFunctionFixture = businessFunctionFixture;

        var mockPublisher = new Mock<IPublisher>();

        var mockInfraObjectViewRepository = new Mock<IInfraObjectViewRepository>();

        mockInfraObjectViewRepository.Setup(x => x.GetInfraObjectByBusinessFunctionId(It.IsAny<string>())).ReturnsAsync(new List<Domain.Views.InfraObjectView>());

        _mockBusinessFunctionRepository = BusinessFunctionRepositoryMocks.UpdateBusinessFunctionRepository(_businessFunctionFixture.BusinessFunctions);

        _handler = new UpdateBusinessFunctionCommandHandler(_businessFunctionFixture.Mapper, mockPublisher.Object, _mockBusinessFunctionRepository.Object, mockInfraObjectViewRepository.Object);
    }

    [Fact]
    public async Task Handle_ValidBusinessFunction_UpdateToBusinessFunctionsRepo()
    {
        _businessFunctionFixture.UpdateBusinessFunctionCommand.Id = _businessFunctionFixture.BusinessFunctions[0].ReferenceId;

        var result = await _handler.Handle(_businessFunctionFixture.UpdateBusinessFunctionCommand, CancellationToken.None);

        var businessFunction = await _mockBusinessFunctionRepository.Object.GetByReferenceIdAsync(result.BusinessFunctionId);

        Assert.Equal(_businessFunctionFixture.UpdateBusinessFunctionCommand.Name, businessFunction.Name);
    }

    [Fact]
    public async Task Handle_Return_UpdateBusinessFunctionResponse_When_BusinessFunctionUpdated()
    {
        _businessFunctionFixture.UpdateBusinessFunctionCommand.Id = _businessFunctionFixture.BusinessFunctions[0].ReferenceId;

        var result = await _handler.Handle(_businessFunctionFixture.UpdateBusinessFunctionCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateBusinessFunctionResponse));

        result.BusinessFunctionId.ShouldBeGreaterThan(0.ToString());

        result.BusinessFunctionId.ShouldBe(_businessFunctionFixture.UpdateBusinessFunctionCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidBusinessFunctionId()
    {
        _businessFunctionFixture.UpdateBusinessFunctionCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_businessFunctionFixture.UpdateBusinessFunctionCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _businessFunctionFixture.UpdateBusinessFunctionCommand.Id = _businessFunctionFixture.BusinessFunctions[0].ReferenceId;

        await _handler.Handle(_businessFunctionFixture.UpdateBusinessFunctionCommand, CancellationToken.None);

        _mockBusinessFunctionRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockBusinessFunctionRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BusinessFunction>()), Times.Once);
    }
}