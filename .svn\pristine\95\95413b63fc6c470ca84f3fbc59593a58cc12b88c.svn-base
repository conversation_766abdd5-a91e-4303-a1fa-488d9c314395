using ContinuityPatrol.Application.Contexts;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Persistence.Persistence;

public partial class ApplicationDbContext : IConfigurationDbContext
{
    #region Context
	public DbSet<GlobalVariable> GlobalVariables { get; set; }

    public DbSet<AdPasswordJob> AdPasswordJobs { get; set; }
    public DbSet<AdPasswordExpire> AdPasswordExpires { get; set; }
    public DbSet<Employee> Employees { get; set; }
    public DbSet<BulkImportActionResult> BulkImportActionResults { get; set; }
    public DbSet<BulkImportOperation> BulkImportOperations { get; set; }
    public DbSet<BulkImportOperationGroup> BulkImportOperationGroups { get; set; }
    public DbSet<InfraOperationalStatus> InfraOperationalStatus { get; set; }
    public DbSet<VeritasCluster> VeritasClusters { get; set; }
    public DbSet<HacmpCluster> HacmpClusters { get; set; }
    public DbSet<RsyncOption> RsyncOptions { get; set; }
    public DbSet<RoboCopy> RoboCopys { get; set; }
    public DbSet<DataSyncOptions> DataSyncs { get; set; }
    public DbSet<SiteLocation> SiteLocations { get; set; }
    public DbSet<IncidentManagementSummary> IncidentManagementSummaries { get; set; }
    public DbSet<ServerSubType> ServerSubTypes { get; set; }
    public DbSet<Company> Companies { get; set; }
    public DbSet<Site> Sites { get; set; }
    public DbSet<TeamMaster> TeamMasters { get; set; }
    public DbSet<TeamResource> TeamResources { get; set; }
    public DbSet<SiteType> SiteTypes { get; set; }
    public DbSet<Setting> Settings { get; set; }
    public DbSet<BusinessFunction> BusinessFunctions { get; set; }
    public DbSet<BusinessService> BusinessServices { get; set; }
    public DbSet<Server> Servers { get; set; }
    public DbSet<ServerType> ServerTypes { get; set; }
    public DbSet<ComponentType> ComponentTypes { get; set; }
    public DbSet<Replication> Replications { get; set; }
    public DbSet<SingleSignOn> SingleSignOns { get; set; }
    public DbSet<Database> Databases { get; set; }
    public DbSet<CredentialProfile> CredentialProfiles { get; set; }
    public DbSet<Job> Jobs { get; set; }
    public DbSet<Node> Nodes { get; set; }
    public DbSet<InfraObject> InfraObjects { get; set; }
    public DbSet<InfraObjectScheduler> InfraObjectSchedulers { get; set; }
    public DbSet<InfraObjectSchedulerLogs> InfraObjectSchedulerLogs { get; set; }
    public DbSet<AccessManager> AccessManagers { get; set; }
    public DbSet<Alert> Alerts { get; set; }
    public DbSet<AlertMaster> AlertMasters { get; set; }
    public DbSet<AlertReceiver> AlertReceivers { get; set; }
    public DbSet<AlertNotification> AlertNotifications { get; set; }
    public DbSet<SmtpConfiguration> SmtpConfigurations { get; set; }
    public DbSet<AlertInformation> AlertInformations { get; set; }
    public DbSet<SmsConfiguration> SmsConfigurations { get; set; }
    public DbSet<Report> Reports { get; set; }
    public DbSet<ReportSchedule> ReportSchedules { get; set; }
    public DbSet<ReportScheduleExecution> ReportSchedulesExecution { get; set; }
    public DbSet<TableAccess> TableAccesses { get; set; }
    public DbSet<DataSet> DataSets { get; set; }
    public DbSet<DataSetColumns> DataSetColumns { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<UserInfo> UserInfos { get; set; }
    public DbSet<UserInfraObject> UserInfraObjects { get; set; }
    public DbSet<UserLogin> UserLogins { get; set; }
    public DbSet<UserActivity> UserActivities { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<UserCredential> UserCredentials { get; set; }
    public DbSet<UserGroup> UserGroup { get; set; }
    public DbSet<WorkflowInfraObject> WorkflowsInfraobject { get; set; }
    public DbSet<Workflow> Workflows { get; set; }
    public DbSet<ReplicationMaster> ReplicationMasters { get; set; }
    public DbSet<InfraReplicationMapping> InfraReplicationMappings { get; set; }
    public DbSet<RpoSlaDeviationReport> RpoSlaDeviationReports { get; set; }
    public DbSet<AboutCp> AboutCp { get; set; }
    public DbSet<DrCalenderActivity> DrCalenderActivity { get; set; }
    public DbSet<GlobalSetting> GlobalSettings { get; set; }
    public DbSet<IncidentManagement> IncidentManagements { get; set; }
    public DbSet<EscalationMatrix> EscalationMatrix { get; set; }
    public DbSet<EscalationMatrixLevel> EscalationMatrixLevels { get; set; }
    public DbSet<TableInformation> TableInformation { get; set; }
    public DbSet<ColumnInfo> Columns { get; set; }
    public DbSet<Incident> Incidents { get; set; }
    public DbSet<IncidentDaily> IncidentDailies { get; set; }
    public DbSet<IncidentLogs> IncidentLogs { get; set; }
    public DbSet<RoboCopyJob> RoboCopyJobs { get; set; }
    public DbSet<RsyncJob> RsyncJobs { get; set; }
    public DbSet<DataSyncJob> DataSyncJobs { get; set; }
    public DbSet<ReplicationJob> ReplicationJobs { get; set; }
    public DbSet<ServerView> ServerViews { get; set; }
    public DbSet<DatabaseView> DatabaseViews {get; set; }
    public DbSet<UserView> UserViews { get; set; }
    public DbSet<InfraObjectView> InfraObjectViews { get; set; }
    public DbSet<ReplicationView> ReplicationViews { get; set;}
    public DbSet<ComponentSaveAll> ComponentSaveAlls { get; set;}
 

    #region FiaBia
    public DbSet<FiaCost> FiaCosts { get; set; }
    public DbSet<FiaTemplate> FiaTemplates { get; set; }
    public DbSet<BiaRules> BiaImpacts { get; set; }
    public DbSet<FiaInterval> FiaIntervals { get; set; }
    public DbSet<FiaImpactType> FiaImpactTypes { get; set; }
    public DbSet<FiaImpactCategory> FiaImpactCategory { get; set; }
   
    #endregion
    #endregion
}
