﻿let fiaUrls = {
    isNameExits: "Configuration/FiaTemplates/IsTemplateNameExist",
    isimpactcatagoryNameExits: "Configuration/FiaTemplates/ImpactMasterNameExist",
    ImpactTypeMasterNameExist: "Configuration/FiaTemplates/ImpactTypeMasterNameExist",
    GetPaginatedFiaTemplatesList: "/Configuration/FiaTemplates/GetPaginatedFiaTemplatesList",
    FiaTemplateCreateOrUpdate: "Configuration/FiaTemplates/FiaTemplateCreateOrUpdate",
    GetList: "Configuration/FiaTemplates/GetList",
    FiaTemplateDelete: "Configuration/FiaTemplates/FiaTemplateDelete",
    GetTimeIntervalMasterList: "Configuration/FiaTemplates/GetTimeIntervalMasterList",
    TimeIntervalMasterCreateOrUpdate: "Configuration/FiaTemplates/TimeIntervalMasterCreateOrUpdate",
    TimeIntervalMasterDelete: "Configuration/FiaTemplates/TimeIntervalMasterDelete",
    GetImpactTypeMasterList: "Configuration/FiaTemplates/GetImpactTypeMasterList",
    ImpactTypeMasterCreateOrUpdate: "Configuration/FiaTemplates/ImpactTypeMasterCreateOrUpdate",
    ImpactTypeMasterDelete: "Configuration/FiaTemplates/ImpactTypeMasterDelete",
    GetImpactMasterList: "Configuration/FiaTemplates/GetImpactMasterList",
    ImpactMasterCreateOrUpdate: "Configuration/FiaTemplates/ImpactMasterCreateOrUpdate",
    ImpactMasterDelete: "Configuration/FiaTemplates/ImpactMasterDelete"
}
let selectedValues = [],
    globalId = "",
    globalDeleteListId = '',
    globalDeleteId = '',
    globalTimeIntervalId = '',
    globalImpactmasterId = '',
    globalImpactmasterDeleteId = '',
    globalImpacttypeId = '',
    globalImpacttypeDeleteId = '',
    overallArr = [],
    CreateDeleteId, dataTable, impactData, impactTypeData, timeIntervalData, propertyData, intervalData
let impactCatagoryArr = [],
    impactTypeArr = [],
    impactInterval = []
function fiaDebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => {
            func.apply(this, args);
        }, timeout);
    };
}
$(function () {
    let createPermission = $("#ConfigurationCreate").data("create-permission").toLowerCase();
    let deletePermission = $("#ConfigurationDelete").data("delete-permission").toLowerCase();
    if (createPermission == 'false') {
        $(".fiaPermissionCreateModal").removeClass('.fiaPermissionCreateModal').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    dataTable = $('#fiaOverallTableList').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
            },
            infoFiltered: ""
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        "processing": true,
        "serverSide": true,
        "filter": true,
        "order": [],
        "ajax": {
            "type": "GET",
            "url": fiaUrls.GetPaginatedFiaTemplatesList,
            "dataType": "json",
            "data": function (d) {
                let sortIndex = d?.order[0]?.column || '';
                let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "userName" : sortIndex === 5 ? "createdDate" : ""
                let orderValue = d.order[0]?.dir || 'asc';
                d.sortColumn = sortValue;
                d.SortOrder = orderValue;
                d.PageNumber = Math.ceil(d.start / d.length) + 1;
                d.pageSize = d.length;
                d.searchString = selectedValues.length === 0 ? $('#fiaSearchInp').val() : selectedValues.join(';');
                selectedValues.length = 0;
            },
            "dataSrc": function (json) {
                if (json.success) {
                    json.recordsTotal = json?.data?.totalPages;
                    json.recordsFiltered = json?.data?.totalCount;
                    CreateDeleteId = json?.data?.data
                    propertyData = json?.data?.data
                    if (json?.data?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    } else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data?.data;
                } else {
                    errorNotification(json)
                }
            }
        },
        "columnDefs": [{
            "targets": [1, 4, 5, 3],
            "className": "truncate"
        }],
        "columns": [{
            "data": null,
            "name": "Sr. No.",
            "autoWidth": true,
            "orderable": false,
            "render": function (data, type, row, meta) {
                if (type === 'display') {
                    var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                    return (page * meta.settings._iDisplayLength) + meta.row + 1;
                }
                return data;
            },
            "orderable": false,
        }, {
            "data": "name",
            "name": "Template Name",
            "autoWidth": true,
            "render": function (data, type, row) {
                return `<td><span title="${data ?? "NA"}">${data ?? "NA"}</span></td>`
            }
        }, {
            "data": "userName",
            "name": "Username",
            "autoWidth": true,
            "render": function (data, type, row) {
                return `<td><span>${data ?? "NA"}</span></td>`
            }
        }, {
            "data": null,
            "name": "Template In Used",
            "autoWidth": true,
            "render": function (data, type, row) {
                return `<td><i class="cp-time me-1"></i>No</td>`;
            }
        }, {
            "data": null,
            "name": "Template Used By",
            "autoWidth": true,
            "render": function (data, type, row) {
                return `<td>NA</td>`;
            }
        }, {
            "data": "createdDate",
            "name": "Created Date",
            "autoWidth": true,
            "render": function (data, type, row) {
                let datemod = data?.split("T").join(" ").split(".")[0].split(" ")
                let fia_date = datemod[0].split("-")
                return `<td><div class="d-flex align-items-center gap-2">${fia_date[2] + "-" + fia_date[1] + "-" + fia_date[0] + " " + datemod[1]}</div></td>`;
            }
        }, {
            "render": function (data, type, row) {
                if (createPermission === 'true' && deletePermission === "true") {
                    return `<td><div class="d-flex align-items-center gap-2"><span role="button" title="Edit" class="edit_businessService-button" name="overallupdate" updateId="${row.id}" temp_name='${row.name}' onclick="overallEdit(this)" data-bs-toggle="modal" data-bs-target="#fiaCreateModal"><i class="cp-edit"></i></span><span role="button" title="Delete" class="delete-bservice-button" deletename='${row.name}' deleteId='${row.id}' onclick="overallDeleteBtn(this)" data-bs-toggle="modal" data-bs-target="#fiaOverallDelete"><i class="cp-Delete"></i></span></div></td>`;
                } else if (createPermission === 'true' && deletePermission === "false") {
                    return `<td><div class="d-flex align-items-center gap-2"><span role="button" title="Edit" class="edit_businessService-button" name="overallupdate" updateId="${row.id}" temp_name='${row.name}' onclick="overallEdit(this)" data-bs-toggle="modal" data-bs-target="#fiaCreateModal"><i class="cp-edit"></i></span><span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i></span></div></td>`;
                } else if (createPermission === 'false' && deletePermission === "true") {
                    return `<td><div class="d-flex align-items-center gap-2"><span role="button" title="Edit" class="icon-disabled"><i class="cp-edit"></i></span><span role="button" title="Delete" class="delete-bservice-button" deletename='${row.templateName}' deleteId='${row.id}' onclick="overallDeleteBtn(this)" data-bs-toggle="modal" data-bs-target="#fiaOverallDelete"><i class="cp-Delete"></i></span></div></td>`;
                } else {
                    return `<td><div class="d-flex align-items-center gap-2"><span role="button" title="Edit" class="icon-disabled"><i class="cp-edit"></i></span><span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i></span></div></td>`;
                }
            },
            "orderable": false,
        }],
        "rowCallback": function (row, data, index) {
            var api = this.api();
            var startIndex = api.context[0]._iDisplayStart;
            var counter = startIndex + index + 1;
            $('td:eq(0)', row).html(counter);
        },
        initComplete: function () {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
        }
    })
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });
})
$('#fiaSearchInp').on('keydown input', fiaDebounce(function (e) {
    if (e.key === '=' || e.key === 'Enter') {
        e.preventDefault();
        return false;
    }
    const nameCheckbox = $("#fiaFilterName");
    const inputValue = $('#fiaSearchInp').val();
    if (nameCheckbox.is(':checked')) {
        selectedValues.push(nameCheckbox.val() + inputValue);
    }
    let currentPage = dataTable.page.info().page + 1;
    if (!isNaN(currentPage)) {
        dataTable.ajax.reload(function (json) {
            if ($('#fiaSearchInp').val().length === 0) {
                if (json?.data?.data?.length === 0) {
                    $('.dataTables_empty').text('No Data Found');
                }
            } else if (json?.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        }, false)
    }
}));

function validateDropDown(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text("").removeClass('field-validation-error');
        return true;
    }
}
async function validatenameDropDown(value, errorMsg, errorElement, url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else if (value.includes('<')) {
        errorElement.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    } else {
        var url = RootUrl + url;
        var data = {};
        data.id = null;
        data.name = value
        const validationResults = [SpecialCharValidate(value), value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) : OnlyNumericsValidate(value), minMaxlength(value), ShouldNotBeginWithSpace(value), ShouldNotBeginWithNumber(value), SpaceWithUnderScore(value), ShouldNotEndWithUnderScore(value), ShouldNotEndWithSpace(value), MultiUnderScoreRegex(value), SpaceAndUnderScoreRegex(value), secondChar(value), await isSameNameExist(url, data)];
        const failedValidations = validationResults.filter(result => result !== true);
        if (failedValidations?.length > 0) {
            errorElement.text(failedValidations[0]).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
}
async function isSameNameExist(url, inputValue) {
    return !inputValue.name.trim() || $("#fiaTemplateSave").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Name already exists" : true;
}
$(".fiaOverallTableList").on("click", function () {
    $('#fiaTemplateSave').text("Save");
    $("#fiaTemplateName").val("")
    $("#fiaImpactCategory,#fiaImpactType,#fiaInterval").val("").trigger("change")
    clearFiaTemplateErrorElements()
})
function clearFiaTemplateErrorElements() {
    $("#fiaTemplateNameError,#fiaImpactCategoryError,#fiaImpactTypeError,#fiaIntervalError").text('').removeClass('field-validation-error');
}
$('#fiaTemplateName').on('input', fiaDebounce(async function () {
    let value = await sanitizeInput($("#fiaTemplateName").val());
    $("#fiaTemplateName").val(value);
    validatenameDropDown(value, "Enter template name", $('#fiaTemplateNameError'), fiaUrls.isNameExits);
}))
$('#fiaImpactCategory').on('change', function () {
    var selectimpactcatagory = $(this).find('option:selected');
    impactCatagoryArr = []
    selectimpactcatagory?.each(function () {
        let option = $(this);
        let obj = {
            ImpactCatagoryId: option.val(),
            ImpactCatagoryName: option.text()
        };
        impactCatagoryArr.push(obj)
    });
    overallEdit("change_value")
    validateDropDown($(this).val(), "Select impact category", $('#fiaImpactCategoryError'));
})
$('#fiaImpactType').on('change', function () {
    var selectimpacttype = $(this).find('option:selected');
    impactTypeArr = []
    selectimpacttype?.each(function () {
        let obj = {
            ImpactTypeId: $(this).val(),
            ImpactTypeName: $(this).text(),
            catagoryid: $(this).attr("catagoryid"),
            catagoryname: $(this).attr("catagoryname")
        };
        impactTypeArr.push(obj)
    });
    validateDropDown($(this).val(), "Select impact type", $('#fiaImpactTypeError'));
})
$('#fiaInterval').on('change', function () {
    var selectimpactinterval = $(this).find('option:selected');
    impactInterval = []
    selectimpactinterval?.each(function () {
        let option = $(this);
        let obj = {
            IntervalId: option.val(),
            IntervalName: option.text()
        };
        impactInterval.push(obj)
    });
    validateDropDown($(this).val(), "Select interval", $('#fiaIntervalError'));
})
$("#fiaOverallCreateModal").on("click", function () {
    overallEdit()
})
$("#fiaTemplateSave").on("click", async function () {
    let property = {
        Fiacatagory: impactCatagoryArr,
        FiaType: impactTypeArr,
        FiaInterval: impactInterval
    }
    let form = $("#fiaCreateModal");
    let istemplatename = await validatenameDropDown($("#fiaTemplateName").val(), "Enter template name", $('#fiaTemplateNameError'), fiaUrls.isNameExits);
    let isimpactcategory = validateDropDown($("#fiaImpactCategory").val(), "Select impact category", $('#fiaImpactCategoryError'));
    let isimpact_type = validateDropDown($("#fiaImpactType").val(), "Select impact type", $('#fiaImpactTypeError'));
    let isinterval = validateDropDown($("#fiaInterval").val(), "Select interval", $('#fiaIntervalError'));
    if (istemplatename & isimpactcategory & isimpact_type & isinterval) {
        form.trigger("submit")
        let data = {
            "Name": $("#fiaTemplateName").val(),
            "Description": "",
            "Properties": JSON.stringify(property),
            "UserName": "",
            "TemplateUsedBy": "",
            "TemplateInUsed": "",
            __RequestVerificationToken: gettoken()
        }
        $('#fiaTemplateSave').text() === "Update" ? data["id"] = globalId : null
        await $.ajax({
            type: "POST",
            url: RootUrl + fiaUrls.FiaTemplateCreateOrUpdate,
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result?.data
                if (result?.success) {
                    $('#fiaCreateModal').modal('hide');
                    notificationAlert("success", data?.message)
                    dataTable.ajax.reload()
                } else {
                    errorNotification(result)
                }
            },
        })
        $('#fiaTemplateSave').text("Save");
        $("#fiaTemplateName").val("")
        $("#fiaImpactCategory,#fiaImpactTypeError,#fiaInterval").val("").trigger("change")
        clearFiaTemplateErrorElements()
    }
})
function overallEdit(data) {
    $.ajax({
        type: "GET",
        url: RootUrl + fiaUrls.GetList,
        dataType: "json",
        success: function (result) {
            impactTypeData = result?.data?.impact
            impactData = result?.data?.impactType
            timeIntervalData = result?.data?.timeInterval
            if (result?.success) {
                if ($("#fiaTemplateSave").text() != "Update") {
                    $('#fiaImpactType').empty().append('<option value=""></option>')
                    let dataVal = $('#fiaImpactCategory').val()
                    if (dataVal?.length) {
                        dataVal?.forEach((x) => {
                            impactTypeData?.forEach(function (item) {
                                if (item?.fiaImpactCategoryId === x) {
                                    $('#fiaImpactType').append('<option catagoryname="' + item.fiaImpactCategoryName + '" catagoryid="' + item.fiaImpactCategoryId + '" value="' + item?.id + '">' + item?.name + '</option>');
                                }
                            });
                        })
                    }
                    $('#fiaInterval').empty().append('<option value=""></option>')
                    if (data != "change_value") {
                        $('#fiaImpactCategory').empty().append('<option value=""></option>')
                    }
                    $('#typeImpactCatagory').empty().append('<option value=""></option>')
                    impactData?.forEach(function (item, i) {
                        $('#fiaImpactCategory').append('<option value="' + item?.id + '">' + item?.name + '</option>'); $('#typeImpactCatagory').append('<option value="' + item?.id + '">' + item?.name + '</option>')
                    })
                    timeIntervalData?.forEach(function (item, i) {
                        if (item?.minTimeUnit == 1 && item?.maxTimeUnit == 2) {
                            let one = item?.minTime + " " + "Hours" + " " + "To" + " " + item?.maxTime + ' Days'
                            $("#fiaInterval").append('<option value="' + item?.id + '">' + one + '</option>')
                        } else if (item?.minTimeUnit == 2 && item?.maxTimeUnit == 2) {
                            let two = item?.minTime + " " + "To" + " " + item?.maxTime + ' Days '
                            $("#fiaInterval").append('<option value="' + item?.id + '">' + two + '</option>')
                        } else {
                            let three = item?.minTime + " " + "To" + " " + item?.maxTime + ' Hours'
                            $("#fiaInterval").append('<option value="' + item?.id + '">' + three + '</option>')
                        }
                    })
                } else {
                    timeIntervalData?.forEach(function (item, i) {
                        if (item?.minTimeUnit == 1 && item?.maxTimeUnit == 2) {
                            let one = item?.minTime + " " + "Hours" + " " + "To" + " " + item?.maxTime + ' Days'
                            $("#fiaInterval").append('<option value="' + item?.id + '">' + one + '</option>')
                        } else if (item.minTimeUnit == 2 && item.maxTimeUnit == 2) {
                            let two = item?.minTime + " " + "To" + " " + item?.maxTime + ' Days '
                            $("#fiaInterval").append('<option value="' + item?.id + '">' + two + '</option>')
                        } else {
                            let three = item?.minTime + " " + "To" + " " + item?.maxTime + ' Hours'
                            $("#fiaInterval").append('<option value="' + item?.id + '">' + three + '</option>')
                        }
                    })
                    impactData?.forEach(function (item, i) {
                        $('#fiaImpactCategory').append('<option value="' + item?.id + '">' + item?.name + '</option>'); $('#typeImpactCatagory').append('<option value="' + item?.id + '">' + item?.name + '</option>')
                    })
                    impactTypeData?.forEach(function (item, i) {
                        $('#fiaImpactType').append('<option value="' + item?.id + '">' + item?.name + '</option>')
                    })
                }
                $("#typeImpactCatagory option,#fiaImpactCategory option,#fiaImpactType option,#fiaInterval option").each(function () {
                    $(this).siblings('[value="' + this.value + '"]').remove()
                })
            } else {
                errorNotification(result)
            }
        },
    })
    if ($(data).attr("name") == "overallupdate") {
        let catagoryArr = [],
            typeArr = [],
            intervalArr = []
        $('#fiaTemplateSave').text("Update");
        globalId = $(data).attr('updateId');
        $("#fiaTemplateName").val($(data).attr('temp_name'))
        propertyData?.forEach((x) => {
            if (x?.id == $(data).attr("updateId")) {
                if (x?.properties != null) {
                    let dat = JSON.parse(x?.properties)
                    dat?.Fiacatagory?.forEach((x) => {
                        catagoryArr.push(x.ImpactCatagoryId)
                    })
                    dat?.FiaType?.forEach((x) => {
                        typeArr.push(x.ImpactTypeId)
                    })
                    dat?.FiaInterval?.forEach((x) => {
                        intervalArr.push(x.IntervalId)
                    })
                    setTimeout(() => {
                        $('#fiaImpactCategory').val(catagoryArr).trigger('change')
                        $('#fiaImpactType').val(typeArr).trigger('change')
                        $('#fiaInterval').val(intervalArr).trigger('change')
                    }, 500)
                }
            }
        })
    }
}

function overallDeleteBtn(data) {
    globalDeleteListId = $(data).attr('deleteId')
    $("#fiaOverallDeletedId").text($(data).attr('deletename')).attr("title", $(data).attr('deletename'));
}
$("#fiaOverallConfirmDeleteButton").on("click", async function () {
    await $.ajax({
        type: "POST",
        url: RootUrl + fiaUrls.FiaTemplateDelete,
        dataType: "json",
        data: {
            id: globalDeleteListId,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let data = result?.data
            if (result?.success) {
                $('#fiaOverallDelete').modal('hide');
                notificationAlert("success", data?.message)
                dataTable.ajax.reload()
            } else {
                errorNotification(result)
            }
        },
    })
})
const getTimeInterval = () => {
    $("#fiaTimeIntevalTable tbody").empty()
    $.ajax({
        type: "GET",
        url: RootUrl + fiaUrls.GetTimeIntervalMasterList,
        dataType: "json",
        success: function (result) {
            let data = result?.data
            if (result?.success) {
                intervalData = data
                data?.forEach(function (item, i) {
                    let timeLabel = '';
                    let unit = '';

                    if (item?.minTimeUnit == 1 && item?.maxTimeUnit == 2) {
                        timeLabel = `${item?.minTime} Hours To ${item?.maxTime} Days`;
                        unit = 'Hours To Days';
                    } else if (item?.minTimeUnit == 2 && item?.maxTimeUnit == 2) {
                        timeLabel = `${item?.minTime} To ${item?.maxTime} Days`;
                        unit = 'Days';
                    } else {
                        timeLabel = `${item?.minTime} To ${item?.maxTime} Hours`;
                        unit = 'Hours';
                    }

                    $("#fiaTimeIntevalTable tbody").append(`
    <tr>
      <td class="text-truncate${unit === 'Days' ? ' d-inline-block' : ''}" title="${timeLabel}">
        ${timeLabel}
      </td>
      <td>
        <div class="d-flex align-items-center justify-content-end gap-2">
          <span
            role="button"
            onclick="TimeIntervalUpdateBtn(this)"
            maxTimeUnit="${item?.maxTimeUnit}"
            minTimeUnit="${item?.minTimeUnit}"
            maxTime="${item?.maxTime}"
            minTime="${item?.minTime}"
            updateId="${item?.id}"
            title="Edit"
            class="edit_businessService-button"
          >
            <i class="cp-edit"></i>
          </span>
          <span
            role="button"
            title="Delete"
            class="delete-bservice-button"
            data-bs-toggle="modal"
            onclick="TimeintervaldeleteBtn(this)"
            deletename="${timeLabel}"
            deleteId="${item?.id}"
            data-bs-target="#fiaDeleteModal"
          >
            <i class="cp-Delete"></i>
          </span>
        </div>
      </td>
    </tr>
  `);
                });

            } else {
                errorNotification(result)
            }
        },
    })
}

function validateMinMax(val1, val2) {
    val1 = Number(val1)
    val2 = Number(val2)
    if (!val1 && !val2) {
        $("#fiaMinTimeError").text("Enter start time interval").addClass('field-validation-error');
        $("#fiaMaxTimeError").text("Enter end time interval").addClass('field-validation-error');
        return false;
    } else if (val1 > val2 && val2 <= val1) {
        $('#fiaMinTimeError').text("Enter value lesser than end time interval").addClass('field-validation-error');
        $('#fiaMaxTimeError').text("Enter value greater than start time interval").addClass('field-validation-error');
        return false;
    } else if (val1.length >= 3) {
        $('#fiaMinTimeError').text("Enter value 2 digit").addClass('field-validation-error');
        return false;
    } else if (val2.length >= 3) {
        $('#fiaMaxTimeError').text("Enter value 2 digit").addClass('field-validation-error');
        return false;
    } else if (val1 == 0 && val2 == 0) {
        $("#fiaMaxTimeError").text("Invalid schedule").addClass('field-validation-error');
        return false;
    } else if (intervalData[0]?.maxTime >= Number(val1)) {
        if (intervalData[0]?.maxTime <= Number(val1)) {
            interval = true
        }
        if (interval == true) {
            $("#fiaMinTimeError,#fiaMaxTimeError").text('').removeClass('field-validation-error');
            return true;
        } else {
            $('#fiaMinTimeError').text("Already configured with this interval").addClass('field-validation-error');
            return false;
        }
    } else {
        $("#fiaMinTimeError,#fiaMaxTimeError").text('').removeClass('field-validation-error');
        return true;
    }
}

function validateHoursDays(val1, val2) {
    if (val1.length == 0 || val2.length == 0) {
        $("#fiaMinTimeUnitError,#fiaMaxTimeUnitError").text("Enter hours or days").addClass('field-validation-error');
        return false;
    } else if (val1 == "Days" && val2 == "Hours") {
        $('#fiaMinTimeUnitError').text("Invalid schedule").addClass('field-validation-error');
        return false;
    } else {
        $("#fiaMinTimeUnitError,#fiaMaxTimeUnitError").text('').removeClass('field-validation-error');
        return true;
    }
}
$(".fiaOverallCancel").on("click", function () {
    overallEdit()
})
$("#fiaTimeIntervalCancelBtn").on("click", function () {
    $("#fiaMinTime,#fiaMaxTime").val("")
    $("#fiaMinTimeUnit,#fiaMaxTimeUnit").val("").trigger("change")
    modalError()
})
function modalError() {
    $("#fiaMaxTimeUnitError,#fiaMinTimeUnitError,#fiaMaxTimeError,#fiaMinTimeError").text('').removeClass('field-validation-error');
}

function setModalValue() {
    let minimum = document.getElementById("fiaMinTime"),
        maximum = document.getElementById("fiaMaxTime"),
        minTimeUnit = document.getElementById("fiaMinTimeUnit").value,
        maxTimeUnit = document.getElementById("fiaMaxTimeUnit").value
    if (minTimeUnit == "Hours") {
        minimum.setAttribute("min", 0)
        minimum.setAttribute("max", 24)
    } else if (minTimeUnit == "Days") {
        minimum.setAttribute("min", 1)
        minimum.setAttribute("max", 31)
    } else if (maxTimeUnit == "Hours") {
        maximum.setAttribute("min", 0)
        maximum.setAttribute("max", 24)
    } else if (maxTimeUnit == "Days") {
        maximum.setAttribute("min", 1)
        maximum.setAttribute("max", 31)
    }
}
$('#fiaMinTimeUnit,#fiaMaxTimeUnit').on('change keyup keydown', function () {
    setModalValue()
    let minUnit = $("#fiaMinTimeUnit").val(),
        maxUnit = $("#fiaMaxTimeUnit").val()
    validateHoursDays(minUnit, maxUnit)
})
$('#fiaMinTime,#fiaMaxTime').on('input', function () {
    interval = false
    let min = $("#fiaMinTime").val(),
        max = $("#fiaMaxTime").val()
    validateMinMax(min, max)
})
$('#fiaMinTime,#fiaMaxTime').on('keypress', function (event) {
    ["e", "E", "+", "-", "."].includes(event.key) && event.preventDefault()
})
$('#fiaTimeIntervalSaveBtn').on('click', async function () {
    let form = $("#fiaTimeIntervalModalToggle1")
    let min = $("#fiaMinTime").val(),
        max = $("#fiaMaxTime").val(),
        minUnit = $("#fiaMinTimeUnit").val(),
        maxUnit = $("#fiaMaxTimeUnit").val(),
        Time = validateMinMax(min, max),
        Timeunit = validateHoursDays(minUnit, maxUnit)
    if (Time & Timeunit) {
            form.trigger("submit")
            let data = {
                "MinTime": $("#fiaMinTime").val(),
                "MaxTime": $("#fiaMaxTime").val(),
                "MinTimeUnit": $("#fiaMinTimeUnit").val() == "Hours" ? 1 : 2,
                "MaxTimeUnit": $("#fiaMaxTimeUnit").val() == "Hours" ? 1 : 2,
                __RequestVerificationToken: gettoken()
            }
        $('#fiaTimeIntervalSaveBtn').text() === "Update" ? data["id"] = globalTimeIntervalId : null
        await $.ajax({
                type: "POST",
                url: RootUrl + fiaUrls.TimeIntervalMasterCreateOrUpdate,
                dataType: "json",
                data: data,
                success: function (result) {
                    let data = result?.data
                    if (result?.success) {
                        notificationAlert("success", data?.message)
                        getTimeInterval()
                    } else {
                        errorNotification(result)
                    }
                },
            })
        $("#fiaMinTime,#fiaMaxTime").val("")
        $("#fiaMinTimeUnit,#fiaMaxTimeUnit").val("").change()
        $('#fiaTimeIntervalSaveBtn').text("Save")
        modalError()
        }
})
$("#fiaTimeIntervalModal").on("click", function () {
    $('#fiaTimeIntervalSaveBtn').text("Save");
     getTimeInterval()
})
function TimeintervaldeleteBtn(data) {
    overallArr = []
    globalDeleteId = $(data).attr('deleteId')
    $("#fiaDeletedId").text($(data).attr('deletename'))
    $("#fiaDeletedId").attr("title", $(data).attr('deletename'));
    CreateDeleteId?.forEach((item, i) => {
        if (item?.intervalId == globalDeleteId) {
            overallArr.push(item?.intervalId)
        }
    })
}
$("#fiaConfirmDeleteButton").on("click", async function () {
    if (overallArr?.length == 0) {
        await $.ajax({
            type: "POST",
            url: RootUrl + fiaUrls.TimeIntervalMasterDelete,
            dataType: "json",
            data: {
                id: globalDeleteId,
                __RequestVerificationToken: gettoken()
            },
            success: function (result) {
                let data = result?.data
                if (result?.success) {
                    notificationAlert("success", data.message)
                } else {
                    errorNotification(result)
                }
            },
        })
        getTimeInterval()
    } else {
        notificationAlert("warning", `The Interval '${$("#fiaDeletedId").text()}' is currently in use`)
    }
})
let interval = false
function TimeIntervalUpdateBtn(data) {
    $('#fiaTimeIntervalSaveBtn').text("Update");
    globalTimeIntervalId = $(data).attr('updateId');
    if ($(data).attr('updateId')) {
        interval = true
    }
    let MinTime = $(data).attr('minTime'),
        MaxTime = $(data).attr('maxTime'),
        MinTimeUnit = $(data).attr('minTimeUnit'),
        MaxTimeUnit = $(data).attr('maxTimeUnit')
    $("#fiaMinTime").val(MinTime);
    $("#fiaMaxTime").val(MaxTime);
    $("#fiaMinTimeUnit").val(MinTimeUnit == 1 ? "Hours" : "Days").trigger("change")
    $("#fiaMaxTimeUnit").val(MaxTimeUnit == 1 ? "Hours" : "Days").trigger("change")
}
$("#fiaImpactMasterModal").on("click", function () {
    $('#fiaImpactMastersaveBtn').text("Save");
    $("#typeImpactCatagory,#typeImpactType,#typeImpactDescription").val("")
    impactCatagoryModalError()
    getImpactMaster()
})
const getImpactMaster = () => {
    $("#fiaTimeImpactcatagorytable tbody ").empty()
    $.ajax({
        type: "GET",
        url: RootUrl + fiaUrls.GetImpactTypeMasterList,
        dataType: "json",
        success: function (result) {
            let data = result?.data
            if (result?.success) {
                data?.forEach(function (item, i) {
                    let sno = i + 1
                    $("#fiaTimeImpactcatagorytable tbody").append(`
  <tr>
    <td>${sno}</td>
    <td class="text-truncate" title="${item?.name}">${item?.name}</td>
    <td>${item?.description}</td>
    <td class="text-truncate" title="${item?.fiaImpactCategoryName}">${item?.fiaImpactCategoryName}</td>
    <td>
      <div class="d-flex align-items-center gap-2">
        <span
          role="button"
          title="Edit"
          onclick="impactMasterEdit(this)"
          updateId="${item?.id}"
          impactcatagoryTypeId="${item?.name}"
          impactcatagoryDescription="${item?.description}"
          fiaImpactCategoryId="${item?.fiaImpactCategoryId}"
          class="edit_businessService-button"
        >
          <i class="cp-edit"></i>
        </span>
        <span
          role="button"
          title="Delete"
          deleteId="${item?.id}"
          deleted_name="${item?.name}"
          onclick="impactMasterDeleteBtn(this)"
          class="delete-bservice-button"
          data-bs-toggle="modal"
          data-bs-target="#fiaImpactMasterDeleteModal"
        >
          <i class="cp-Delete"></i>
        </span>
      </div>
    </td>
  </tr>
`);

                })
            } else {
                errorNotification(result)
            }
        },
    })
}

function impactCatagoryModalError() {
    $("#typeImpactCatagoryError,#typeImpactTypeError,#typeImpactDescriptionError").text('').removeClass('field-validation-error');
}

function validateImpactMaster(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
async function isSameImpacttypeExist(url, inputValue) {
    return !inputValue.name.trim() || $("#fiaImpactMastersaveBtn").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Name already exists" : true;
}
async function validateImpacttypeNameMaster(value, errorMsg, errorElement, url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else if (value.includes('<')) {
        errorElement.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    } else {
        var url = RootUrl + url;
        var data = {};
        data.id = null;
        data.name = value
        const validationResults = [SpecialCharValidate(value), value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) : OnlyNumericsValidate(value), minMaxlength(value), ShouldNotBeginWithSpace(value), ShouldNotBeginWithNumber(value), SpaceWithUnderScore(value), ShouldNotEndWithUnderScore(value), ShouldNotEndWithSpace(value), MultiUnderScoreRegex(value), SpaceAndUnderScoreRegex(value), secondChar(value), await isSameImpacttypeExist(url, data)];
        const failedValidations = validationResults.filter(result => result !== true);
        if (failedValidations.length > 0) {
            errorElement.text(failedValidations[0]).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
}
$("#typeImpactCatagory").on("change", function () {
    validateImpactMaster($(this).val(), "Select impact catagory", $('#typeImpactCatagoryError'));
})
$("#typeImpactType").on("input", fiaDebounce(async function () {
    let value = await sanitizeInput($("#typeImpactType").val());
    $("#typeImpactType").val(value);
    validateImpacttypeNameMaster(value, "Enter impact type", $('#typeImpactTypeError'), fiaUrls.ImpactTypeMasterNameExist);
}))
$('#fiaImpactMastersaveBtn').on('click', async function () {
    let form = $("#fiaManageImpactCategoryModalToggle")
    let impactcatagory = $("#typeImpactCatagory option:selected").text(),
        impactcatagoryid = $("#typeImpactCatagory ").val(),
        impacttype = $("#typeImpactType").val(),
        impactdescription = $("#typeImpactDescription").val()
    let impactcatagoryvalidation = validateImpactMaster($("#typeImpactCatagory").val(), "Select impact catagory", $('#typeImpactCatagoryError')); let impacttypevalidation = await validateImpacttypeNameMaster($("#typeImpactType").val(), "Enter impact type", $('#typeImpactTypeError'), fiaUrls.ImpactTypeMasterNameExist)
    if (impactcatagoryvalidation && impacttypevalidation) {
        form.trigger("submit")
        let data = {
                "FiaImpactCategoryName": impactcatagory,
                "FiaImpactCategoryId": impactcatagoryid,
                "Name": impacttype,
                "Description": impactdescription == "" ? "NA" : impactdescription,
                __RequestVerificationToken: gettoken()
            }
        $('#fiaImpactMastersaveBtn').text() === "Update" ? data["id"] = globalImpactmasterId : null
        await $.ajax({
                type: "POST",
                url: RootUrl + fiaUrls.ImpactTypeMasterCreateOrUpdate,
                dataType: "json",
                data: data,
                success: function (result) {
                    let data = result.data
                    if (result.success) {
                        notificationAlert("success", data.message)
                            getImpactMaster()
                    } else {
                        errorNotification(result)
                    }
                },
            })
        $("#typeImpactType,#typeImpactDescription").val("")
        $("#typeImpactCatagory").val("").change()
        $('#fiaImpactMastersaveBtn').text("Save")
        impactCatagoryModalError()
        }
})
function impactMasterEdit(data) {
    $('#fiaImpactMastersaveBtn').text("Update");
    globalImpactmasterId = $(data).attr('updateId');
    let catagory_id = $(data).attr('fiaImpactCategoryId')
    $("#typeImpactCatagory").val(catagory_id).trigger("change")
    $("#typeImpactType").val($(data).attr('impactcatagoryTypeId'));
    $("#typeImpactDescription").val($(data).attr('impactcatagoryDescription'));
}

function impactMasterDeleteBtn(data) {
    overallArr = []
    globalImpactmasterDeleteId = $(data).attr('deleteId')
    $("#fiaImpactMasterDeletedId").text($(data).attr('deleted_name')).attr("title", $(data).attr('deleted_name'));
    CreateDeleteId?.forEach((item, i) => {
        if (item?.impactTypeId == globalImpactmasterDeleteId) {
            overallArr.push(item?.impactTypeId)
        }
    })
}
$("#fiaImpactMasterConfirmDeleteButton").on("click", async function () {
    if (overallArr?.length == 0) {
        await $.ajax({
            type: "POST",
            url: RootUrl + fiaUrls.ImpactTypeMasterDelete,
            dataType: "json",
            data: {
                id: globalImpactmasterDeleteId,
                __RequestVerificationToken: gettoken()
            },
            success: function (result) {
                let data = result?.data
                if (result?.success) {
                    notificationAlert("success", data?.message)
                } else {
                    errorNotification(result)
                }
            },
        })
        getImpactMaster()
    } else {
        notificationAlert("warning", `The Impact Type '${$("#fiaImpactMasterDeletedId").text()}' is currently in use`)
    }
})
function impactTypeModalError() {
    $("#fiaModalImpactCatagoryError,#fiaModalImpactDescription").text('').removeClass('field-validation-error');
}

function validateimpacttype(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        const validationResults = [SpecialCharValidate(value), value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) : OnlyNumericsValidate(value), minMaxlength(value), ShouldNotBeginWithSpace(value), ShouldNotBeginWithNumber(value), SpaceWithUnderScore(value), ShouldNotEndWithUnderScore(value), ShouldNotEndWithSpace(value), MultiUnderScoreRegex(value), SpaceAndUnderScoreRegex(value), secondChar(value),];
        return CommonValidation(errorElement, validationResults);
    }
}
$("#fiaImpactTypeModal").on("click", function () {
    $('#fiaImpacttypeBtnSave').text("Save");
    $("#fiaModalImpactCatagory,#fiaModalImpactDescription").val("");
    impactTypeModalError()
    getImpactType()
})
const getImpactType =async () => {
    $("#fiaTimeImpacttypetable tbody ").empty()
  await  $.ajax({
        type: "GET",
        url: RootUrl + fiaUrls.GetImpactMasterList,
        dataType: "json",
        success: function (result) {
            let data = result?.data
            if (result?.success) {
                data.forEach(function (item, i) {
                    let sno = i + 1
                    $("#fiaTimeImpacttypetable tbody").append(`
  <tr>
    <td>${sno}</td>
    <td class="text-truncate" title="${item?.name}">${item?.name}</td>
    <td>${item?.description}</td>
    <td>
      <div class="d-flex align-items-center gap-2">
        <span 
          role="button" 
          title="Edit" 
          onclick="impactTypeEdit(this)" 
          updateId="${item?.id}" 
          impactTypeDescription="${item?.description}" 
          impactTypeName="${item?.name}" 
          class="edit_businessService-button"
        >
          <i class="cp-edit"></i>
        </span>
        <span 
          role="button" 
          title="Delete" 
          deleteId="${item?.id}" 
          deleted_name="${item?.name}" 
          onclick="impactTypeDeleteBtn(this)" 
          class="delete-bservice-button" 
          data-bs-toggle="modal" 
          data-bs-target="#fiaImpactTypeDeleteModal"
        >
          <i class="cp-Delete"></i>
        </span>
      </div>
    </td>
  </tr>
`);

                })
            } else {
                errorNotification(result)
            }
        },
    })
}

function impactTypeEdit(data) {
    $('#fiaImpacttypeBtnSave').text("Update");
    globalImpacttypeId = $(data).attr('updateId');
    $("#fiaModalImpactCatagory").val($(data).attr('impactTypeName'));
    $("#fiaModalImpactDescription").val($(data).attr('impactTypeDescription'));
}
function validateimpacttypemaster(value, errorMsg, errorElement, url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text("").removeClass('field-validation-error');
        return true;
    }
}
async function isSameImpactCatagoryExist(url, inputValue) {
    return !inputValue.name.trim() || $("#fiaImpacttypeBtnSave").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Name already exists" : true;
}
async function validateImpactCatagoryMaster(value, errorMsg, errorElement, url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else if (value.includes('<')) {
        errorElement.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    } else {
        var url = RootUrl + url;
        var data = {};
        data.id = null;
        data.name = value
        const validationResults = [SpecialCharValidate(value), value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) : OnlyNumericsValidate(value), minMaxlength(value), ShouldNotBeginWithSpace(value), ShouldNotBeginWithNumber(value), SpaceWithUnderScore(value), ShouldNotEndWithUnderScore(value), ShouldNotEndWithSpace(value), MultiUnderScoreRegex(value), SpaceAndUnderScoreRegex(value), secondChar(value), await isSameImpactCatagoryExist(url, data)];
        const failedValidations = validationResults.filter(result => result !== true);
        if (failedValidations?.length > 0) {
            errorElement.text(failedValidations[0]).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
}
$("#fiaModalImpactCatagory").on("input", fiaDebounce(async function () {
    let value = await sanitizeInput($("#fiaModalImpactCatagory").val());
    $("#fiaModalImpactCatagory").val(value);
    validateImpactCatagoryMaster(value, "Enter impact catagory", $('#fiaModalImpactCatagoryError'), fiaUrls.isimpactcatagoryNameExits);
}))
$('#fiaImpacttypeBtnSave').on('click', async function () {
    let form = $("#fiaImpactCategoryModalToggle")
    let impactcatagory = $("#fiaModalImpactCatagory").val(),
        impactdescription = $("#fiaModalImpactDescription").val()
    let impactcatagoryvalidation = await validateImpactCatagoryMaster($("#fiaModalImpactCatagory").val(), "Enter impact catagory", $('#fiaModalImpactCatagoryError'), fiaUrls.isimpactcatagoryNameExits)
    if (impactcatagoryvalidation) {
        form.trigger("submit")
        let data = {
                "Name": impactcatagory,
                "Description": impactdescription == "" ? "NA" : impactdescription,
                __RequestVerificationToken: gettoken()
            }
        $('#fiaImpacttypeBtnSave').text() === "Update" ? data["id"] = globalImpacttypeId : null
        await $.ajax({
                type: "POST",
                url: RootUrl + fiaUrls.ImpactMasterCreateOrUpdate,
                dataType: "json",
                data: data,
                success:async function (result) {
                    let data = result?.data
                    if (result.success) {
                        notificationAlert("success", data?.message)
                       await getImpactType()
                        overallEdit()
                    } else {
                        errorNotification(result)
                    }
                },
        })
        $("#fiaModalImpactCatagory,#fiaModalImpactDescription").val("")
        $('#fiaImpacttypeBtnSave').text("Save")
        impactTypeModalError()
        }
})
function impactTypeDeleteBtn(data) {
    overallArr = []
    globalImpacttypeDeleteId = $(data).attr('deleteId')
    $("#fiaImpactTypeDeletedId").text($(data).attr('deleted_name')).attr("title", $(data).attr('deleted_name'));
    CreateDeleteId.forEach((item, i) => {
        if (item.impactCategoryId == globalImpacttypeDeleteId) {
            overallArr.push(item?.impactCategoryId)
        }
    })
}
$("#fiaImpactTypeConfirmDeleteButton").on("click", async function () {
    if (overallArr?.length == 0) {
        await $.ajax({
            type: "POST",
            url: RootUrl + fiaUrls.ImpactMasterDelete,
            dataType: "json",
            data: {
                id: globalImpacttypeDeleteId,
                __RequestVerificationToken: gettoken()
            },
            success: function (result) {
                let data = result?.data
                if (result?.success) {
                    notificationAlert("success", data?.message)
                } else {
                    errorNotification(result)
                }
            },
        })
        getImpactType()
    } else {
        notificationAlert("warning", `The Impact Category '${$("#fiaImpactTypeDeletedId").text()}' is currently in use`)
    }
})