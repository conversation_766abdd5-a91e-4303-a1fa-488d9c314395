﻿namespace ContinuityPatrol.Application.Features.DRReadyLog.Commands.Create;

public class CreateDRReadyLogCommandHandler : IRequestHandler<CreateDRReadyLogCommand, CreateDRReadyLogResponse>
{
    private readonly IDrReadyLogRepository _dRReadyLogRepository;
    private readonly IMapper _mapper;

    public CreateDRReadyLogCommandHandler(IMapper mapper, IDrReadyLogRepository dRReadyLogRepository)
    {
        _mapper = mapper;
        _dRReadyLogRepository = dRReadyLogRepository;
    }

    public async Task<CreateDRReadyLogResponse> Handle(CreateDRReadyLogCommand request,
        CancellationToken cancellationToken)
    {
        var dRReadyLog = _mapper.Map<Domain.Entities.DRReadyLog>(request);

        dRReadyLog = await _dRReadyLogRepository.AddAsync(dRReadyLog);

        var response = new CreateDRReadyLogResponse
        {
            Message = Message.Create(nameof(Domain.Entities.DRReadyLog), dRReadyLog.BusinessServiceName),

            Id = dRReadyLog.ReferenceId
        };

        return response;
    }
}