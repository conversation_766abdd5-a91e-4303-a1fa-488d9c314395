﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.HacmpClusterModel.HacmpClusterViewModel
<div class="modal-dialog modal-sm modal-dialog-centered">
    <form class="w-100">
        @Html.AntiForgeryToken()
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/delete.png" />
            </div>
            <div class="modal-body text-center pt-0">
                <h4>Are you sure?</h4>
                <p class="d-flex align-items-center justify-content-center gap-1">You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="hacmpDeleteId"></span> data?</p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" tabindex="-1" data-bs-dismiss="modal">No</button>
                <button type="button" class="btn btn-primary btn-sm" id="hacmpConfirmDeleteBtn">Yes</button>
            </div>
        </div>
    </form>
</div>