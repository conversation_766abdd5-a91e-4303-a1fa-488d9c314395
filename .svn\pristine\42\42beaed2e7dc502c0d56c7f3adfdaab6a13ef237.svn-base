using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.Create;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.Update;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.UpdateJobState;
using ContinuityPatrol.Application.Features.CyberJobManagement.Commands.UpdateJobStatus;
using ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberJobManagementModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Cyber;

public class CyberJobManagementService : BaseClient, ICyberJobManagementService
{
    public CyberJobManagementService(IConfiguration config, IAppCache cache, ILogger<CyberJobManagementService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<CyberJobManagementListVm>> GetCyberJobManagementList()
    {
        var request = new RestRequest("api/v6/cyberjobmanagements");

        return await GetFromCache<List<CyberJobManagementListVm>>(request, "GetCyberJobManagementList");
    }

    public async Task<BaseResponse> CreateAsync(CreateCyberJobManagementCommand createCyberJobManagementCommand)
    {
        var request = new RestRequest("api/v6/cyberjobmanagements", Method.Post);

        request.AddJsonBody(createCyberJobManagementCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateCyberJobManagementCommand updateCyberJobManagementCommand)
    {
        var request = new RestRequest("api/v6/cyberjobmanagements", Method.Put);

        request.AddJsonBody(updateCyberJobManagementCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/cyberjobmanagements/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<CyberJobManagementDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/cyberjobmanagements/{id}");

        return await Get<CyberJobManagementDetailVm>(request);
    }


    #region NameExist
    public async Task<bool> IsCyberJobManagementNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/cyberjobmanagements/name-exist?cyberjobmanagementName={name}&id={id}");

        return await Get<bool>(request);
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<CyberJobManagementListVm>> GetPaginatedCyberJobManagements(GetCyberJobManagementPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/cyberjobmanagements/paginated-list");

        return await Get<PaginatedResult<CyberJobManagementListVm>>(request);
    }
    #endregion

    public async  Task<List<CyberJobManagementStatusVm>> GetCyberJobManagementStatus()
    {
        var request = new RestRequest("api/v6/cyberjobmanagements/jobstatus");

        return await Get<List<CyberJobManagementStatusVm>>(request);
    }

    public async  Task<BaseResponse> UpdateStatus(UpdateCyberJobManagementStatusCommand updateCyberJobManagementStatusCommand)
    {

        var request = new RestRequest("api/v6/cyberjobmanagements/updatestatus", Method.Put);

        request.AddJsonBody(updateCyberJobManagementStatusCommand);

        return await Put<BaseResponse>(request);
    }

    public  async Task<BaseResponse> UpdateState(UpdateCyberJobManagementStateCommand updateCyberJobManagementStateCommand)
    {
        var request = new RestRequest("api/v6/cyberjobmanagements/updatestate",Method.Put);

        request.AddJsonBody(updateCyberJobManagementStateCommand);

        return await Put<BaseResponse>(request);
    }
}
