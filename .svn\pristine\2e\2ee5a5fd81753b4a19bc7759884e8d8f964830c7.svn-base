﻿using ContinuityPatrol.Application.Features.Job.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Job.Commands;


public class DeleteJobTests : IClassFixture<JobFixture>
{
    private readonly Mock<IJobRepository> _mockJobRepository;

    private readonly JobFixture _jobFixture;

    private readonly DeleteJobCommandHandler _handler;

    public DeleteJobTests(JobFixture jobFixture)
    {
        _jobFixture = jobFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockJobRepository = JobRepositoryMocks.DeleteJobRepository(_jobFixture.Jobs);

        _handler = new DeleteJobCommandHandler(_mockJobRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_Success_When_Delete_Job()
    {
        var validGuid = Guid.NewGuid();

        _jobFixture.Jobs[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteJobCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteJobResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Return_IsActive_False_When_Delete_Job()
    {
        var validGuid = Guid.NewGuid();

        _jobFixture.Jobs[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteJobCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_IsActive_False_When_GetByReferenceIdAsyncMethod_DeleteJob()
    {
        var validGuid = Guid.NewGuid();

        _jobFixture.Jobs[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteJobCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var node = await _mockJobRepository.Object.GetByReferenceIdAsync(_jobFixture.Jobs[0].ReferenceId);

        node.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidJobId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteJobCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();

        _jobFixture.Jobs[0].ReferenceId = validGuid.ToString();

        var job = _jobFixture.Jobs[0];

        var ValidGuid = _jobFixture.Jobs[0].ReferenceId.ToString();

        _jobFixture.Jobs[0].ReferenceId = Guid.NewGuid().ToString();

        var result = await _handler.Handle(new DeleteJobCommand { Id = _jobFixture.Jobs[0].ReferenceId }, CancellationToken.None);

        _mockJobRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockJobRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.Job>()), Times.Once);
    }
}