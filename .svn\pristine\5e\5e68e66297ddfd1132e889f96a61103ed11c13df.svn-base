﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.ReportModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.Report.Queries.GetPaginatedList;

public class
    GetReportPaginatedListQueryHandler : IRequestHandler<GetReportPaginatedListQuery, PaginatedResult<ReportListVm>>
{
    private readonly IMapper _mapper;
    private readonly IReportRepository _reportRepository;

    public GetReportPaginatedListQueryHandler(IMapper mapper, IReportRepository reportRepository)
    {
        _mapper = mapper;
        _reportRepository = reportRepository;
    }

    public async Task<PaginatedResult<ReportListVm>> Handle(GetReportPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _reportRepository.GetPaginatedQuery();

        var productFilterSpec = new ReportFilterSpecification(request.SearchString);

        var reportsList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<ReportListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return reportsList;
    }
}