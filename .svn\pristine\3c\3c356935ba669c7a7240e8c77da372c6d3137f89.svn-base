﻿namespace ContinuityPatrol.Application.Features.SmsConfiguration.Commands.Create;

public class CreateSmsConfigurationCommand : IRequest<CreateSmsConfigurationResponse>
{
    public string URL { get; set; }

    public string SenderId { get; set; }

    public string UserName { get; set; }

    public string Password { get; set; }

    public string RecipientNo { get; set; }
    public string Properties { get; set; }

    public override string ToString()
    {
        return $"User Name: {UserName};";
    }
}