﻿using ContinuityPatrol.Application.Features.StateMonitorStatus.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.StateMonitorStatus.Commands;

public class DeleteStateMonitorStatusTests : IClassFixture<StateMonitorStatusFixture>
{
    private readonly StateMonitorStatusFixture _stateMonitorStatusFixture;

    private Mock<IStateMonitorStatusRepository> _mockStateMonitorStatusRepository;

    private DeleteStateMonitorStatusCommandHandler _handler;

    public DeleteStateMonitorStatusTests(StateMonitorStatusFixture stateMonitorStatusFixture)
    {
        _stateMonitorStatusFixture = stateMonitorStatusFixture;

        _mockStateMonitorStatusRepository = new Mock<IStateMonitorStatusRepository>();

        _mockStateMonitorStatusRepository = StateMonitorStatusRepositoryMocks.DeleteStateMonitorStatusRepository(_stateMonitorStatusFixture.StateMonitorStatuses);

        _handler = new DeleteStateMonitorStatusCommandHandler(_mockStateMonitorStatusRepository.Object);

        _stateMonitorStatusFixture.StateMonitorStatuses[0].IsActive = true;
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_StateMonitorStatusDeleted()
    {
        var validGuid = _stateMonitorStatusFixture.StateMonitorStatuses[0].ReferenceId;

        _stateMonitorStatusFixture.StateMonitorStatuses[0].ReferenceId = validGuid;

        _mockStateMonitorStatusRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_stateMonitorStatusFixture.StateMonitorStatuses[0]);

        var result = await _handler.Handle(new DeleteStateMonitorStatusCommand{Id = validGuid }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();

        _mockStateMonitorStatusRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.StateMonitorStatus>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_DeleteStateMonitorStatusResponse_When_StateMonitorStatusDeleted()
    {
        var validGuid = _stateMonitorStatusFixture.StateMonitorStatuses[0].ReferenceId;

        _stateMonitorStatusFixture.StateMonitorStatuses[0].ReferenceId = validGuid;

        _mockStateMonitorStatusRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_stateMonitorStatusFixture.StateMonitorStatuses[0]);

        var result = await _handler.Handle(new DeleteStateMonitorStatusCommand { Id = validGuid}, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteStateMonitorStatusResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);

        _mockStateMonitorStatusRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.StateMonitorStatus>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_StateMonitorStatusDeleted()
    {
        var validGuid = _stateMonitorStatusFixture.StateMonitorStatuses[0].ReferenceId;

        _stateMonitorStatusFixture.StateMonitorStatuses[0].ReferenceId = validGuid;

        _mockStateMonitorStatusRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_stateMonitorStatusFixture.StateMonitorStatuses[0]);

        var result = await _handler.Handle(new DeleteStateMonitorStatusCommand { Id = validGuid }, CancellationToken.None);

        var stateMonitorStatus = await _mockStateMonitorStatusRepository.Object.GetByReferenceIdAsync(_stateMonitorStatusFixture.StateMonitorStatuses[0].ReferenceId);

        stateMonitorStatus.IsActive.ShouldBeFalse();

        _mockStateMonitorStatusRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.StateMonitorStatus>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidStateMonitorStatusId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteStateMonitorStatusCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = _stateMonitorStatusFixture.StateMonitorStatuses[0].ReferenceId;

        _stateMonitorStatusFixture.StateMonitorStatuses[0].ReferenceId = validGuid;

        _mockStateMonitorStatusRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_stateMonitorStatusFixture.StateMonitorStatuses[0]);

        var result = await _handler.Handle(new DeleteStateMonitorStatusCommand { Id = validGuid }, CancellationToken.None);

        _mockStateMonitorStatusRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockStateMonitorStatusRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.StateMonitorStatus>()), Times.Once);
    }
}