﻿namespace ContinuityPatrol.Application.Features.Server.Queries.GetServerByLicenseKey;

public class GetServerByLicenseKeyQueryHandler : IRequestHandler<GetServerByLicenseKeyQuery, List<ServerByLicenseKeyVm>>
{
    private readonly IMapper _mapper;
    private readonly IServerViewRepository _serverViewRepository;

    public GetServerByLicenseKeyQueryHandler(IMapper mapper, IServerViewRepository serverViewRepository)
    {
        _mapper = mapper;
        _serverViewRepository = serverViewRepository;
    }

    public async Task<List<ServerByLicenseKeyVm>> Handle(GetServerByLicenseKeyQuery request,
        CancellationToken cancellationToken)
    {
        var server = await _serverViewRepository.GetServerByLicenseKey(request.LicenseId);

        //return server.Count <= 0 ? new List<ServerByLicenseKeyVm>() : _mapper.Map<List<ServerByLicenseKeyVm>>(server);

        var databasesByLicenseDto = _mapper.Map<List<ServerByLicenseKeyVm>>(server);

        return databasesByLicenseDto;
    }
}