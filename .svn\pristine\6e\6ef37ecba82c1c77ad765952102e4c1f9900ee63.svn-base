using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetPaginatedList;

public class GetCyberAirGapLogPaginatedListQueryHandler : IRequestHandler<GetCyberAirGapLogPaginatedListQuery,
    PaginatedResult<CyberAirGapLogListVm>>
{
    private readonly ICyberAirGapLogRepository _cyberAirGapLogRepository;
    private readonly IMapper _mapper;

    public GetCyberAirGapLogPaginatedListQueryHandler(IMapper mapper,
        ICyberAirGapLogRepository cyberAirGapLogRepository)
    {
        _mapper = mapper;
        _cyberAirGapLogRepository = cyberAirGapLogRepository;
    }

    public async Task<PaginatedResult<CyberAirGapLogListVm>> Handle(GetCyberAirGapLogPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new CyberAirGapLogFilterSpecification(request.SearchString);

        var queryable =await _cyberAirGapLogRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var cyberAirGapLogList = _mapper.Map<PaginatedResult<CyberAirGapLogListVm>>(queryable);

        return cyberAirGapLogList;
        //var queryable = _cyberAirGapLogRepository.GetPaginatedQuery();

        //var productFilterSpec = new CyberAirGapLogFilterSpecification(request.SearchString);

        //var cyberAirGapLogList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<CyberAirGapLogListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return cyberAirGapLogList;
    }
}