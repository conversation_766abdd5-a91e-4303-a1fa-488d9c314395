﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class FastCopyMonitorFilterSpecification:Specification<FastCopyMonitor>
{
    public FastCopyMonitorFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.ExceptionFileNames != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("businessservicename=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.DestinationIP.Contains(stringItem.Replace("DestinationIP=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p => p.DestinationIP.Contains(searchString);
            }
        }
    }
}
