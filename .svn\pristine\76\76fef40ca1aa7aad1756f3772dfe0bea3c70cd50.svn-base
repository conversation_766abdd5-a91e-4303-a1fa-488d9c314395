using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Create;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Update;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftCategoryMasterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IDriftCategoryMasterService
{
    Task<List<DriftCategoryMasterListVm>> GetDriftCategoryMasterList();
    Task<BaseResponse> CreateAsync(CreateDriftCategoryMasterCommand createDriftCategoryMasterCommand);
    Task<BaseResponse> UpdateAsync(UpdateDriftCategoryMasterCommand updateDriftCategoryMasterCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<DriftCategoryMasterDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsDriftCategoryMasterNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<DriftCategoryMasterListVm>> GetPaginatedDriftCategoryMasters(GetDriftCategoryMasterPaginatedListQuery query);
    #endregion
}
