﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Events.PaginatedView;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.WorkflowCategoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowCategory.Queries.GetPaginatedList;

public class GetWorkflowCategoryPaginatedListQueryHandler : IRequestHandler<GetWorkflowCategoryPaginatedListQuery,
    PaginatedResult<WorkflowCategoryListVm>>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IWorkflowCategoryRepository _workflowCategoryRepository;

    public GetWorkflowCategoryPaginatedListQueryHandler(IMapper mapper,
        IWorkflowCategoryRepository workflowCategoryRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _workflowCategoryRepository = workflowCategoryRepository;
        _publisher = publisher;
    }

    public async Task<PaginatedResult<WorkflowCategoryListVm>> Handle(GetWorkflowCategoryPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _workflowCategoryRepository.GetPaginatedQuery();

        var productFilterSpec = new WorkflowCategoryFilterSpecification(request.SearchString);

        var workflowActions = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<WorkflowCategoryListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        await _publisher.Publish(new WorkflowCategoryPaginatedEvent(), cancellationToken);

        return workflowActions;
    }
}