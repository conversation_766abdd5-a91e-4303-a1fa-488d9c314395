﻿using System.Text;
using ContinuityPatrol.Api.IntegrationTests.Fixtures;
using ContinuityPatrol.Application.Features.Company.Commands.Create;
using ContinuityPatrol.Application.Features.Company.Commands.CreateDefaultCompany;
using ContinuityPatrol.Application.Features.Company.Commands.Delete;
using ContinuityPatrol.Application.Features.Company.Commands.Update;
using ContinuityPatrol.Application.Features.Company.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Company.Queries.GetDisplayNameUnique;
using ContinuityPatrol.Application.Features.Company.Queries.GetNameUnique;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Newtonsoft.Json;
using Shouldly;

namespace ContinuityPatrol.Api.IntegrationTests.Controllers;

public class CompanyControllerTests : IClassFixture<ApiWebFactory<Program>>, IDisposable
{
    private readonly HttpClient _httpClient;

    private readonly CompanyFixture _companyFixture;

    public CompanyControllerTests()
    {
        _companyFixture = new CompanyFixture();

        _httpClient = _companyFixture.CreateClient();
    }


    [Fact]
    public async Task GetNames()
    {
        _companyFixture.SetupGetNames();

        var response = await _httpClient.GetAsync(_companyFixture.GetCompanyNames);
        response.EnsureSuccessStatusCode();
        var responseString = await response.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<List<CompanyNameVm>>(responseString);

        result.ShouldBeOfType<List<CompanyNameVm>>();
        result.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task GetById()
    {
        _companyFixture.SetupCompaniesById(1);

        var response = await _httpClient.GetAsync(_companyFixture.CompanyUrl + "FF8F0377-E257-4FA0-8460-89A16D00107D");

        response.EnsureSuccessStatusCode();
        var responseString = await response.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<CompanyDetailVm>(responseString);
        result.ShouldBeOfType<CompanyDetailVm>();
    }


    [Fact]
    public async Task CreateCompany()
    {
        var rest = _companyFixture.SetupCreateCompany();
        var url = _companyFixture.CompanyUrl;

        var json = await Task.Run(() => JsonConvert.SerializeObject(rest));
        var httpContent = new StringContent(json, Encoding.UTF8, "application/json");
        var httpResponse = await _httpClient.PostAsync(url, httpContent);
        var responseContent = await httpResponse.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<CreateCompanyResponse>(responseContent);
        result.ShouldBeOfType<CreateCompanyResponse>();
    }

    [Fact]
    public async Task UpdateCompany()
    {
        var rest = _companyFixture.SetupUpdateCompany();
        var url = _companyFixture.CompanyUrl;

        var json = await Task.Run(() => JsonConvert.SerializeObject(rest));
        var httpContent = new StringContent(json, Encoding.UTF8, "application/json");
        var httpResponse = await _httpClient.PutAsync(url, httpContent);
        var responseContent = await httpResponse.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<UpdateCompanyResponse>(responseContent);
        result.ShouldBeOfType<UpdateCompanyResponse>();
    }

    [Fact]
    public async Task GetCompanyDeleteById()
    {
        _companyFixture.SetupDeleteCompaniesById(1);

        var response = await _httpClient.DeleteAsync(_companyFixture.CompanyUrl + "1");

        response.EnsureSuccessStatusCode();
        var responseString = await response.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<DeleteCompanyResponse>(responseString);
        result.ShouldBeOfType<DeleteCompanyResponse>();
    }
    [Fact]
    public async Task GetNamesExist()
    {
        _companyFixture.SetupGetNamesExists();

        var response = await _httpClient.GetAsync(_companyFixture.CompanyNamesExists);
        response.EnsureSuccessStatusCode();
        var responseString = await response.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<List<GetCompanyNameUniqueQuery>>(responseString);

        result.ShouldBeOfType<List<GetCompanyNameUniqueQuery>>();
        result.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task GetDisplayNames()
    {
        _companyFixture.SetupGetDisplayName();

        var response = await _httpClient.GetAsync(_companyFixture.DisplayNamesExists);
        response.EnsureSuccessStatusCode();
        var responseString = await response.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<List<GetDisplayNameUniqueQuery>>(responseString);

        result.ShouldBeOfType<List<GetDisplayNameUniqueQuery>>();
        result.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task CreateDefaultCompany()
    {
        var rest = _companyFixture.DefaultCompany();
        var url = _companyFixture.CreateDefaultCompany;

        var json = await Task.Run(() => JsonConvert.SerializeObject(rest));
        var httpContent = new StringContent(json, Encoding.UTF8, "application/json");
        var httpResponse = await _httpClient.PostAsync(url, httpContent);
        var responseContent = await httpResponse.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<CreateDefaultCompanyCommand>(responseContent);
        result.ShouldBeOfType<CreateDefaultCompanyCommand>();
    }

    [Fact]
    public async Task GetPaginatedList()
    {
        _companyFixture.CompanyPaginatedList();

        var response = await _httpClient.GetAsync(_companyFixture.CompanyPaginated);
        response.EnsureSuccessStatusCode();
        var responseString = await response.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<PaginatedResult<CompanyListVm>>(responseString);

        result.ShouldBeOfType<PaginatedResult<CompanyListVm>>();
        Assert.NotEmpty(result.Data);
    }


    public void Dispose()
    {
        _httpClient.Dispose();
        _companyFixture.Dispose();
    }
}