﻿using ContinuityPatrol.Application.Features.SVCGMMonitoringLogs.Commands.Create;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.SVCGMMonitoringLogs.Commands
{
    public class CreateSVCGMMonitorLogTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISVCGMMonitorLogRepository> _mockSVCGMMonitorLogRepository;
        private readonly Mock<ILogger<CreateSVCGMMonitorLogCommandHandler>> _mockLogger;
        private readonly CreateSVCGMMonitorLogCommandHandler _handler;

        public CreateSVCGMMonitorLogTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSVCGMMonitorLogRepository = new Mock<ISVCGMMonitorLogRepository>();
            _mockLogger = new Mock<ILogger<CreateSVCGMMonitorLogCommandHandler>>();
            _handler = new CreateSVCGMMonitorLogCommandHandler(_mockSVCGMMonitorLogRepository.Object, _mockMapper.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnValidResponse_WhenLogIsCreatedSuccessfully()
        {
            var command = new CreateSVCGMMonitorLogCommand
            {
                WorkflowName = "TestData1",
                Type = "TestData2"
            };

            var mockEntity = new SVCGMMonitorLog
            {
                ReferenceId = "12345",
                WorkflowName = "TestData1",
                Type = "TestData2"
            };

            _mockMapper.Setup(mapper => mapper.Map<SVCGMMonitorLog>(command))
                .Returns(mockEntity);

            _mockSVCGMMonitorLogRepository.Setup(repo => repo.AddAsync(It.IsAny<SVCGMMonitorLog>()))
                .ReturnsAsync(mockEntity);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("12345", result.Id);
            Assert.Contains(nameof(SVCGMMonitorLog), result.Message);

            _mockMapper.Verify(mapper => mapper.Map<SVCGMMonitorLog>(command), Times.Once);
            _mockSVCGMMonitorLogRepository.Verify(repo => repo.AddAsync(It.IsAny<SVCGMMonitorLog>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryAddAsyncOnce_WithCorrectEntity()
        {
            var command = new CreateSVCGMMonitorLogCommand
            {
                WorkflowName = "TestData1",
                Type = "TestData2"
            };

            var mockEntity = new SVCGMMonitorLog
            {
                ReferenceId = "12345",
                WorkflowName = "TestData1",
                Type = "TestData2"
            };

            _mockMapper.Setup(mapper => mapper.Map<SVCGMMonitorLog>(command))
                .Returns(mockEntity);

            _mockSVCGMMonitorLogRepository.Setup(repo => repo.AddAsync(It.IsAny<SVCGMMonitorLog>()))
                .ReturnsAsync(mockEntity);

            await _handler.Handle(command, CancellationToken.None);

            _mockSVCGMMonitorLogRepository.Verify(repo => repo.AddAsync(It.Is<SVCGMMonitorLog>(log =>
                log.WorkflowName == "TestData1" && log.Type == "TestData2")), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldLogInformation_WhenLogIsCreated()
        {
            var command = new CreateSVCGMMonitorLogCommand
            {
                WorkflowName = "TestData1",
                Type = "TestData2"
            };

            var mockEntity = new SVCGMMonitorLog
            {
                ReferenceId = "12345",
                WorkflowName = "TestData1",
                Type = "TestData2"
            };

            _mockMapper.Setup(mapper => mapper.Map<SVCGMMonitorLog>(command))
                .Returns(mockEntity);

            _mockSVCGMMonitorLogRepository.Setup(repo => repo.AddAsync(It.IsAny<SVCGMMonitorLog>()))
                .ReturnsAsync(mockEntity);

            await _handler.Handle(command, CancellationToken.None);

            //_mockLogger.Verify(logger => logger.Log(
            //    It.Is<LogLevel>(logLevel => logLevel == LogLevel.Information),
            //    It.IsAny<EventId>(),
            //    It.Is<It.IsAnyType>((obj, t) => obj.ToString().Contains("Creating SVCGMMonitorLog")),
            //    It.IsAny<Exception>(),
            //    It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)), Times.Once);
        }
    }
}
