﻿namespace ContinuityPatrol.Domain.Entities;

public class RoboCopyJob : AuditableEntity
{
    public string ReplicationId { get; set; }
    public string ReplicationName { get; set; }
    public string ReplicationTypeId { get; set; }
    public string ReplicationType { get; set; }
    public string SiteId { get; set; }
    public string SiteName { get; set; }
    [Column(TypeName = "NCLOB")] public string Properties { get; set; }
    [Column(TypeName = "NCLOB")] public string JobProperties { get; set; }
    [Column(TypeName = "NCLOB")] public string ScheduleProperties { get; set; }
    public string SourceDirectory { get; set; }
    public string DestinationDirectory { get; set; }
    public string ModeType { get; set; }
    public string RoboCopyOptionsId { get; set; }
    public string LastSuccessfulReplTime { get; set; }
}