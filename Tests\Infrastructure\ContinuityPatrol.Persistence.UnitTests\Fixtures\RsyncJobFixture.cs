using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RsyncJobFixture : IDisposable
{
    public List<RsyncJob> RsyncJobPaginationList { get; set; }
    public List<RsyncJob> RsyncJobList { get; set; }
    public RsyncJob RsyncJobDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string ReplicationId = "REPL_123";
    public const string ReplicationName = "Test Replication";
    public const string ReplicationTypeId = "REPL_TYPE_123";
    public const string ReplicationType = "Rsync";
    public const string SiteId = "SITE_123";
    public const string SiteName = "Test Site";
    public const string UserId = "USER_123";

    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public RsyncJobFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<RsyncJob>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.ReplicationId, () => ReplicationId)
            .With(x => x.ReplicationName, () => ReplicationName)
            .With(x => x.ReplicationTypeId, () => ReplicationTypeId)
            .With(x => x.ReplicationType, () => ReplicationType)
            .With(x => x.SiteId, () => SiteId)
            .With(x => x.SiteName, () => SiteName)
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.JobProperties, () => _fixture.Create<string>())
            .With(x => x.ScheduleProperties, () => _fixture.Create<string>())
            .With(x => x.SourceDirectory, () => "/source/path")
            .With(x => x.DestinationDirectory, () => "/destination/path")
            .With(x => x.ModeType, () => "Incremental")
            .With(x => x.RsyncOptionId, () => Guid.NewGuid().ToString())
            .With(x => x.LastSuccessfulReplTime, () => DateTime.UtcNow.AddHours(-1).ToString())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
        );

        RsyncJobList = _fixture.CreateMany<RsyncJob>(5).ToList();
        RsyncJobPaginationList = _fixture.CreateMany<RsyncJob>(20).ToList();
        RsyncJobDto = _fixture.Create<RsyncJob>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public RsyncJob CreateRsyncJobWithReplicationId(string replicationId)
    {
        return _fixture.Build<RsyncJob>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.ReplicationId, replicationId)
            .With(x => x.ReplicationName, ReplicationName)
            .With(x => x.ReplicationTypeId, ReplicationTypeId)
            .With(x => x.ReplicationType, ReplicationType)
            .With(x => x.SiteId, SiteId)
            .With(x => x.SiteName, SiteName)
            .With(x => x.Properties, _fixture.Create<string>())
            .With(x => x.JobProperties, _fixture.Create<string>())
            .With(x => x.ScheduleProperties, _fixture.Create<string>())
            .With(x => x.SourceDirectory, "/source/path")
            .With(x => x.DestinationDirectory, "/destination/path")
            .With(x => x.ModeType, "Incremental")
            .With(x => x.RsyncOptionId, Guid.NewGuid().ToString())
            .With(x => x.LastSuccessfulReplTime, DateTime.UtcNow.AddHours(-1).ToString())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RsyncJob CreateRsyncJobWithSiteId(string siteId)
    {
        return _fixture.Build<RsyncJob>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.ReplicationId, ReplicationId)
            .With(x => x.ReplicationName, ReplicationName)
            .With(x => x.ReplicationTypeId, ReplicationTypeId)
            .With(x => x.ReplicationType, ReplicationType)
            .With(x => x.SiteId, siteId)
            .With(x => x.SiteName, SiteName)
            .With(x => x.Properties, _fixture.Create<string>())
            .With(x => x.JobProperties, _fixture.Create<string>())
            .With(x => x.ScheduleProperties, _fixture.Create<string>())
            .With(x => x.SourceDirectory, "/source/path")
            .With(x => x.DestinationDirectory, "/destination/path")
            .With(x => x.ModeType, "Incremental")
            .With(x => x.RsyncOptionId, Guid.NewGuid().ToString())
            .With(x => x.LastSuccessfulReplTime, DateTime.UtcNow.AddHours(-1).ToString())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RsyncJob CreateRsyncJobWithProperties(
        string replicationId = null,
        string replicationName = null,
        string replicationTypeId = null,
        string replicationType = null,
        string siteId = null,
        string siteName = null,
        string sourceDirectory = null,
        string destinationDirectory = null,
        string modeType = null,
        string rsyncOptionId = null,
        string lastSuccessfulReplTime = null,
        string properties = null,
        string jobProperties = null,
        string scheduleProperties = null,
        bool isActive = true)
    {
        return _fixture.Build<RsyncJob>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.ReplicationId, replicationId ?? ReplicationId)
            .With(x => x.ReplicationName, replicationName ?? ReplicationName)
            .With(x => x.ReplicationTypeId, replicationTypeId ?? ReplicationTypeId)
            .With(x => x.ReplicationType, replicationType ?? ReplicationType)
            .With(x => x.SiteId, siteId ?? SiteId)
            .With(x => x.SiteName, siteName ?? SiteName)
            .With(x => x.Properties, properties ?? _fixture.Create<string>())
            .With(x => x.JobProperties, jobProperties ?? _fixture.Create<string>())
            .With(x => x.ScheduleProperties, scheduleProperties ?? _fixture.Create<string>())
            .With(x => x.SourceDirectory, sourceDirectory ?? "/source/path")
            .With(x => x.DestinationDirectory, destinationDirectory ?? "/destination/path")
            .With(x => x.ModeType, modeType ?? "Incremental")
            .With(x => x.RsyncOptionId, rsyncOptionId ?? Guid.NewGuid().ToString())
            .With(x => x.LastSuccessfulReplTime, lastSuccessfulReplTime ?? DateTime.UtcNow.AddHours(-1).ToString())
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RsyncJob CreateRsyncJobWithLongProperties(int propertyLength)
    {
        var longProperty = new string('A', propertyLength);
        return CreateRsyncJobWithProperties(
            properties: longProperty,
            jobProperties: longProperty,
            scheduleProperties: longProperty);
    }

    public RsyncJob CreateRsyncJobWithDirectories(string sourceDirectory, string destinationDirectory)
    {
        return CreateRsyncJobWithProperties(
            sourceDirectory: sourceDirectory,
            destinationDirectory: destinationDirectory);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonReplicationTypes = { "Rsync", "RoboCopy", "DataSync", "FastCopy" };
        public static readonly string[] CommonModeTypes = { "Incremental", "Full", "Differential", "Mirror" };
        public static readonly string[] CommonSourceDirectories = { "/source/path1", "/source/path2", "C:\\Source", "D:\\Source" };
        public static readonly string[] CommonDestinationDirectories = { "/dest/path1", "/dest/path2", "C:\\Destination", "D:\\Destination" };
        public static readonly string[] SpecialCharacterPaths = { "/path with spaces/", "/path@#$%/", "/path_with_underscores/", "/path-with-dashes/" };
        
        public static readonly string ValidGuid = Guid.NewGuid().ToString();
        public static readonly string InvalidGuid = "INVALID_GUID";
        public static readonly string EmptyGuid = Guid.Empty.ToString();
    }
}
