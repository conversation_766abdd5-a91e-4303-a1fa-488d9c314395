﻿namespace ContinuityPatrol.Application.Features.Form.Commands.Import;

public class ImportFormCommand : IRequest<ImportFormResponse>
{
    public List<FormListCommand> Forms { get; set; }
    public List<FormTypeCategoryListCommand> FormTypeCategories { get; set; }
    public List<ComponentTypeListCommand> ComponentTypes { get; set; }
}

public class FormListCommand
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Type { get; set; }
    public string Properties { get; set; }
    public string Version { get; set; }
    public bool IsPublish { get; set; }
    public bool IsLock { get; set; }
    public bool IsRestore { get; set; }

    public override string ToString()
    {
        return $"Name: {Name}; Id:{Id};";
    }
}


public class FormTypeCategoryListCommand
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string FormId { get; set; }
    public string FormName { get; set; }
    public string FormTypeId { get; set; }
    public string FormTypeName { get; set; }
    public string Logo { get; set; }
    public string Version { get; set; }
    public string Properties { get; set; }
    public string FormVersion { get; set; }
}

public class ComponentTypeListCommand
{
    public string Id { get; set; }
    public string ComponentName { get; set; }
    public string FormTypeId { get; set; }
    public string FormTypeName { get; set; }
    public string Properties { get; set; }
    public string Logo { get; set; }
    public string Version { get; set; }
    public bool IsDatabase { get; set; }
    public bool IsReplication { get; set; }
    public bool IsServer { get; set; }
    public bool IsCustom { get; set; }

    public override string ToString()
    {
        return $"Name: {FormTypeName}; Id:{Id};";
    }
}