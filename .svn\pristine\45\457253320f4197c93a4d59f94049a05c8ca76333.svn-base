﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.DashboardViewLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetPaginatedList;

public class GetDashboardViewLogPaginatedListQueryHandler : IRequestHandler<GetDashboardViewLogPaginatedListQuery,
    PaginatedResult<DashboardViewLogListVm>>
{
    private readonly IDashboardViewLogRepository _dataLagLogRepository;
    private readonly IMapper _mapper;

    public GetDashboardViewLogPaginatedListQueryHandler(IMapper mapper,
        IDashboardViewLogRepository dataLagLogRepository)
    {
        _mapper = mapper;
        _dataLagLogRepository = dataLagLogRepository;
    }

    public async Task<PaginatedResult<DashboardViewLogListVm>> Handle(GetDashboardViewLogPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DashboardViewLogFilterSpecification(request.SearchString);

        var queryable =await  _dataLagLogRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec,request.SortColumn,request.SortOrder);

        var dataLag = _mapper.Map<PaginatedResult<DashboardViewLogListVm>>(queryable);
           
        return dataLag;
        //var queryable = _dataLagLogRepository.PaginatedListAllAsync();

        //var productFilterSpec = new DashboardViewLogFilterSpecification(request.SearchString);

        //var dataLag = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<DashboardViewLogListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return dataLag;
    }
}