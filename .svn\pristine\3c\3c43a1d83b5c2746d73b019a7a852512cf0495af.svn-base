﻿$(async function () {   

    $('#ddlReplicationTypeID').on("change", async function () {
        const selectedReplicarion = $('#ddlReplicationTypeID option:selected');
        let replicationType = selectedReplicarion.text();

        if (replicationType?.toLowerCase()?.includes("perpetuuiti")) {
            $('#showHideLicenseKey').show();
            $('#Licensekey-error').text("").removeClass('field-validation-error');
        } else {
            $('#showHideLicenseKey').hide();
            document.getElementById('licensed').value = 'NA';
        }
        $("#replicationNameType").text(replicationType);
        replicationLogo = selectedReplicarion.attr('replicationLogo');
        $('#replicationTitleLogo').removeClass().addClass(replicationLogo);
        $("#replicaionType").val(replicationType);

        await $.ajax({
            url: RootUrl + "Admin/FormMapping/GetFormMappingByFormId",
            method: 'GET',
            data: { "formTypeId": selectedReplicarion.val(), "version": '' },
            dataType: 'json',
            success: async function (result) {
                if (result?.success) {
                    nextButtonStyle('', '');
                    let form = result?.data;
                    $('#formRenderingArea').empty();

                    try {
                        let parsedJsonData = JSON?.parse(form?.properties)
                        var renderedForm = new FormeoRenderer({
                            renderContainer: document.querySelector("#formRenderingArea")
                        });
                        renderedForm.render(parsedJsonData);

                        //Sybase Type
                        if (replicationType?.toLowerCase()?.includes("sybase-srs")) {
                            await sybaseType()
                        } else {
                            if ($('.sybaseType').length) {
                                $('.sybaseType').remove();
                            }
                        }

                        //
                        if (replicationType?.toLowerCase()?.includes("mssqlalwayson-availabilitygroup")) {
                            await MSSQLAlwaysOnAvailabilityGroup()
                        } else {
                            if ($('.availabilityGroupType').length) {
                                $('.availabilityGroupType').remove();
                            }
                        }

                        //  initalstate;
                        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                            var field = parsedJsonData.fields[fieldId];
                            if (field.conditions && field.conditions?.length > 0) {
                                field.conditions.forEach(function (condition) {
                                    condition.if.forEach(function (ifClause) {
                                        condition.then.forEach(function (thenClause) {
                                            if (thenClause.targetProperty === 'isVisible' && thenClause.assignment === 'equals' && ifClause.comparison !== "notEquals") {
                                                var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                if (targetElement && !targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none")) {
                                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                }
                                            }
                                        });
                                    });
                                });
                            }
                        });

                        setTimeout(() => {
                            let selectElements = document.querySelectorAll('.form-select-modal-dynamic');
                            selectElements.forEach(async function (selectElement) {
                                let $this = $(selectElement);
                                $this.select2({
                                    dropdownParent: this1.find('.modal-content'),
                                    placeholder: $this.attr('placeholder')
                                });
                            });

                            $('.form-select-modal-dynamic').next('.select2-container').css('width', '100%');

                            let disableSelectTagTitle = document.querySelectorAll('.select2-selection__rendered');
                            disableSelectTagTitle?.forEach(async function (selectElement) {
                                let $this = $(selectElement);
                                $this.attr('title', '');
                            });

                            onChangeFormBuilderValidation('replication');//onChangeNodeReplicationFormBuilderValidation();
                        }, 500);

                        await populateTheDynamicFields(parsedJsonData.fields);

                        for (const key in parsedJsonData?.fields) {
                            if (parsedJsonData.fields.hasOwnProperty(key)) {
                                const field = parsedJsonData.fields[key];
                                const { id, meta, attrs } = field;
                                if (meta.id === "textarea") {
                                    const textArea = $(`#f-${id}`);
                                    textArea.attr('title', '');
                                    textArea.attr('autocomplete', 'off');
                                }
                                if (meta.id === "ip-address") {
                                    const ipAddress = $(`#f-${id}`);
                                    ipAddress.attr('title', '');
                                    ipAddress.attr('maxlength', '40');
                                    ipAddress.attr('autocomplete', 'off');
                                }
                                if (meta.id === "paragraph") {
                                    const paragraph = $(`#f-${id}`);
                                    paragraph.attr('title', '');
                                    paragraph.attr('autocomplete', 'off');
                                }
                                if (meta.id === "text-input") {
                                    const textInput = $(`#f-${id}`);
                                    textInput.attr('title', '');
                                    let maxLength = textInput.attr('maxlength');
                                    if (!maxLength) {
                                        if (attrs?.name?.toLowerCase().includes("path")) {
                                            textInput.attr('maxlength', '500');
                                        } else {
                                            textInput.attr('maxlength', '100');
                                        }
                                    }
                                    textInput.attr('autocomplete', 'off');
                                }
                                if (meta.id === "password-input") {
                                    const passwordInput = $(`#f-${id}`);
                                    passwordInput.attr('title', '');
                                    passwordInput.attr('maxlength', '30');
                                    passwordInput.attr('autocomplete', 'off');
                                }
                                if (meta.id === "number") {
                                    const numberField = $(`#f-${id}`);
                                    numberField.attr('title', '');
                                    numberField.attr('autocomplete', 'off');
                                    let name = numberField.attr('name').toLowerCase();
                                    const minLength = numberField?.attr('minlength');
                                    const maxLength = numberField?.attr('maxlength');
                                    const intMinValue = parseInt(minLength, 10);
                                    const intMaxValue = parseInt(maxLength, 10);
                                    if (intMinValue && intMaxValue && intMinValue > 0 && intMaxValue > 0) {
                                        numberField.on("keyup keydown", function (event) {
                                            let eventKey = event.key;
                                            const validKeys = ["Home", "End", "Backspace", "Delete", "Left", "Right", ...Array.from({ length: 10 }, (_, i) => i.toString())];
                                            if (validKeys.includes(eventKey)) {
                                                const inputValue = $(this).val();
                                                const allowedKeys = ['Home', 'End', 'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];
                                                if (inputValue.length >= intMaxValue && !allowedKeys.includes(event.key)) {
                                                    event.preventDefault(); // Prevent further input
                                                }
                                                const numericValue = parseInt($(this).val().length, 10);
                                                if (isNaN(numericValue) || intMinValue < 1 || numericValue > intMaxValue) {
                                                    $(this).val('');
                                                }
                                            } else {
                                                event.preventDefault();
                                            }
                                        });
                                    } else {
                                        numberField.prop('min', '1');
                                        numberField.attr('max', '99999');
                                        numberField.on("keyup keydown", function (event) {
                                            let eventKey = event.key;
                                            const validKeys = ["Home", "End", "Backspace", "Delete", "Left", "Right", ...Array.from({ length: 10 }, (_, i) => i.toString())];
                                            if (validKeys.includes(eventKey)) {
                                                const inputValue = $(this).val();
                                                const allowedKeys = ['Home', 'End', 'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];
                                                if (inputValue.length >= 5 && !allowedKeys.includes(event.key)) {
                                                    event.preventDefault(); // Prevent further input
                                                }
                                                const numericValue = parseInt($(this).val(), 10);
                                                if (isNaN(numericValue) || numericValue < 1 || numericValue > 99999) {
                                                    $(this).val('');
                                                }
                                            } else {
                                                event.preventDefault();
                                            }
                                        });
                                    }
                                }
                            }
                        }
                        $('#formRenderingArea').on('change', '.formeo-render .f-field-group input , .formeo-render .f-field-group select', function (event) {
                            let selectedValue = event.target.value;
                            let selectedid = event.target.id;
                            let typ = event.target.type
                            if (typ === "radio" || typ === "checkbox") {
                                Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                    var field = parsedJsonData.fields[fieldId];
                                    if (field.conditions && field.conditions?.length > 0) {
                                        var isVisible = false;
                                        field.conditions.forEach(function (condition) {
                                            var isMatchingCondition = condition.if.some(function (ifClause) {
                                                sourceField = parsedJsonData.fields[ifClause.source.substring(7)];
                                                return ifClause.target === selectedValue;
                                            });
                                            if (isMatchingCondition) {
                                                isVisible = true;
                                            }
                                        });
                                        field.conditions.forEach(function (condition) {
                                            condition.if.forEach(function (ifClause) {
                                                condition.then.forEach(function (thenClause) {
                                                    var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                    var srcElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                                    if (targetElement && thenClause.targetProperty === 'isVisible') {
                                                        if (isVisible) {
                                                            targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                        }
                                                        if (!event.target.checked && (selectedid.substring(0, selectedid?.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                            targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                            let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                                            let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                                            if (textField?.length > 0) {
                                                                removeValidationWhenUncheck(textField);
                                                            }
                                                            if (selectField?.length > 0) {
                                                                removeValidationWhenUncheck(selectField);
                                                            }
                                                        }
                                                    }
                                                });
                                            });
                                        });
                                    }
                                });
                            };
                            if (typ === "select-one") {
                                Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                    var field = parsedJsonData.fields[fieldId];
                                    if (field.conditions && field.conditions?.length > 0) {
                                        var isMatchingCondition = field.conditions.some(function (condition) {
                                            return condition.if.some(function (ifClause) {
                                                if (ifClause.source === `fields.${fieldId}` && ifClause.comparison === 'equals' && ifClause.target === selectedValue) {
                                                    return true;
                                                }
                                            });
                                        });
                                        if (isMatchingCondition) {
                                            field.conditions.forEach(function (condition) {
                                                condition.then.forEach(function (thenClause) {
                                                    condition.if.forEach(function (ifClause) {
                                                        var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                        if (targetElement && thenClause.targetProperty === 'isVisible') {
                                                            if (ifClause.target === selectedValue && thenClause.assignment === 'equals' && targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none")) {
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                                correctElementId = targetElement.id
                                                            } else if (ifClause.target !== selectedValue && thenClause.assignment === 'equals') {
                                                                targetElement.value = ""
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                                let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                                                let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                                                if (textField?.length > 0) {
                                                                    removeValidationWhenUncheck(textField);
                                                                }
                                                                if (selectField?.length > 0) {
                                                                    removeValidationWhenUncheck(selectField);
                                                                }
                                                            }
                                                        }
                                                    });
                                                });
                                            });
                                        }
                                    } else {
                                        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                            var field = parsedJsonData.fields[fieldId];
                                            if (field.conditions && field.conditions?.length > 0) {
                                                field.conditions.forEach(function (condition) {
                                                    condition.then.forEach(function (thenClause) {
                                                        condition.if.forEach(function (ifClause) {
                                                            var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                            var sourceElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                                            var sourceName = document.getElementsByName(`f-${ifClause.source.substring(7)}`);
                                                            //if (targetElement === null) {
                                                            //    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                            //}
                                                            if (targetElement && selectedValue !== ifClause.target && ifClause.comparison !== 'notEquals' && (selectedid === sourceElement || selectedid == sourceName)) {
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                                let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                                                let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                                                if (textField?.length > 0) {
                                                                    removeValidationWhenUncheck(textField);
                                                                }
                                                                if (selectField?.length > 0) {
                                                                    removeValidationWhenUncheck(selectField);
                                                                }
                                                            }
                                                        });
                                                    });
                                                });
                                            }
                                        });
                                    }

                                });
                            }
                        });

                        ///onsetconditionals
                        $('#formRenderingArea').on('change input', '.formeo-render .f-field-group input , .formeo-render .f-field-group select', function (event) {
                            let selectedValue = event.target.value;
                            let selectedid = event.target.id;
                            let typ = event.target.type
                            if (typ === "radio" || typ === "checkbox") {

                                // Loop through all fields and their conditions id radio
                                Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                    var field = parsedJsonData.fields[fieldId];
                                    if (field.conditions && field.conditions?.length > 0) {
                                        var isVisible = false;
                                        field.conditions.forEach(function (condition) {
                                            var isMatchingCondition = condition.if.some(function (ifClause) {
                                                sourceField = parsedJsonData.fields[ifClause.source.substring(7)];
                                                return ifClause.target === selectedValue;
                                            });
                                            if (isMatchingCondition) {
                                                isVisible = true;
                                            }
                                        });

                                        field.conditions.forEach(function (condition) {
                                            condition.then.forEach(function (thenClause) {
                                                condition.if.forEach(function (ifClause) {
                                                    var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                    var srcElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                                    if (targetElement) {
                                                        if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                                                            if (isVisible) {
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                            }
                                                            if (!event.target.checked && (selectedid.substring(0, selectedid?.length - 2) === `f-${ifClause.source.substring(7)}`)) {

                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                            }
                                                        }
                                                        else if (ifClause.comparison === "notEquals") {
                                                            if (targetElement && event.target.checked) {
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none')
                                                            }
                                                            else if (!event.target.checked && (selectedid.substring(0, selectedid?.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                                                            }
                                                        }
                                                    }
                                                });
                                            });
                                        });
                                    }
                                });
                            };
                            if (typ === "select-one") {
                                Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                    var field = parsedJsonData.fields[fieldId];

                                    if (field.conditions && field.conditions?.length > 0) {
                                        var isMatchingCondition = field.conditions.some(function (condition) {
                                            return condition.if.some(function (ifClause) {
                                                if (ifClause.source === `fields.${fieldId}` && ifClause.target === selectedValue) {
                                                    return true;
                                                }
                                            });
                                        });

                                        if (isMatchingCondition) {
                                            field.conditions.forEach(function (condition) {
                                                condition.then.forEach(function (thenClause) {
                                                    condition.if.forEach(function (ifClause) {
                                                        var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                        if (targetElement && thenClause.targetProperty === 'isVisible') {
                                                            if (ifClause.target === selectedValue && thenClause.assignment === 'equals' && targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none")) {
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                                correctElementId = targetElement.id
                                                            } else if (ifClause.target !== selectedValue) {
                                                                targetElement.value = ""
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                            }
                                                        }
                                                    });
                                                });
                                            });
                                        } else {
                                            Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                                var field = parsedJsonData.fields[fieldId];
                                                if (field.conditions && field.conditions?.length > 0) {
                                                    field.conditions.forEach(function (condition) {
                                                        condition.then.forEach(function (thenClause) {
                                                            condition.if.forEach(function (ifClause) {
                                                                var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                                var sourceElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                                                var sourceName = document.getElementsByName(`f-${ifClause.source.substring(7)}`);
                                                                if (targetElement && selectedValue !== ifClause.target && ifClause.comparison !== 'notEquals' && (selectedid === sourceElement || selectedid == sourceName)) {
                                                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                                }
                                                            });
                                                        });
                                                    });
                                                }
                                            });
                                        }
                                    }
                                });
                            }
                        });

                        if (isEdit && propsReplication) {
                            setTimeout(() => {
                                populateReplicationDynamicFields(propsReplication, rSyncDataSyncRoboCopy)
                            }, 900)
                        }

                    } catch (error) {
                        notificationAlert("warning", "Form property is not valid format.");
                        nextButtonStyle('0.5', 'none');
                    }
                }
                else {
                    errorNotification(result);
                    nextButtonStyle('0.5', 'none');
                }
            }
        });
    });


    $("#saveButton").on("click", debounce(async function () {
        let res = await inputFormValidation('replication', $('#ddlReplicationTypeID option:selected').text().toLowerCase());
        $('.sourceDirectory, .destinationDirectory, [class^="replicationProperties"]').each(async function () {
            const $this = $(this);
            const isVisible = $this.is(":visible");

            if (isVisible) {
                let source = sourceDirInput($('.sourceDirectory'), $('#ddlReplicationTypeID option:selected').text().toLowerCase(), true);
                let destination = destinationDirInput($('.destinationDirectory'), $('#ddlReplicationTypeID option:selected').text().toLowerCase(), true);
                let properties = rSyncRoboDataSyncproperties($('select[class^="replicationProperties"]'), true);
                let sourceResult = source.every(value => value === true);
                let destinationResult = destination.every(value => value === true);
                let propertiesResult = properties.every(value => value === true);
                if (!sourceResult && !destinationResult && !propertiesResult) {
                    res = false;
                }
            } else {
                res = true;
            }
        });

        setTimeout(async () => {

            if (res) {
                let fd = saveReplicationFormFields();
                fd.icon = replicationLogo ? replicationLogo : "cp-images"
                const keys = Object.keys(fd);
                keys.forEach(key => {
                    if (key.startsWith('f-')) {
                        delete fd[key];
                    }
                });
                let hiddenInputProperties = document.getElementById('Props');
                let encryption = await propertyEncryption(fd);
                hiddenInputProperties.value = encryption;

                if (!btnDisableReplication) {
                    btnDisableReplication = true;
                    let replRSyncRCopyData = {
                        Id: $('#replicationID').val(),
                        Name: $('#textName').val(),
                        Type: $('#replicaionType').val(),
                        TypeId: $('#ddlReplicationTypeID').val(),
                        CompanyId: $('#replicationID').val(),
                        SiteId: $('#siteNames').val(),
                        SiteName: $('#names').val(),
                        Properties: encryption,
                        Logo: $('#replicationLogo').val(),
                        LicenseId: $('#licensed').val() ? $('#licensed').val() : "NA",
                        LicenseKey: $('#replicationLicenseKey').val(),
                        BusinessServiceId: $('#businessServiceID').val(),
                        BusinessServiceName: $('#businessServiceName').val(),
                    }
                    if ($('#ddlReplicationTypeID option:selected').text().trim().toLowerCase().includes("rsync")) {
                        replRSyncRCopyData.RsyncJobViewModels = [];
                        fd.RsyncTable.forEach(function (value, index) {
                            let rsyncObj = {
                                Id: value?.tableID !== "null" ? value?.tableID : "",
                                ReplicationId: $('#replicationID').val(),
                                ReplicationName: $('#textName').val(),
                                ReplicationTypeId: $('#ddlReplicationTypeID').val(),
                                ReplicationType: $('#replicaionType').val(),
                                SiteId: $('#siteNames').val(),
                                SiteName: $('#names').val(),
                                Properties: encryption,
                                SourceDirectory: value.sourceDirectory,
                                DestinationDirectory: value.destinationDirectory,
                                ModeType: "Pending",
                                RsyncOptionId: value.properties,
                                LastSuccessfullReplTime: null,
                            };
                            replRSyncRCopyData.RsyncJobViewModels.push(rsyncObj);
                        });
                    }

                    if ($('#ddlReplicationTypeID option:selected').text().trim().toLowerCase().includes("robocopy")) {
                        replRSyncRCopyData.RoboCopyJobViewModels = [];
                        fd.RobocopyTable.forEach(function (value, index) {
                            let roboCopyObj = {
                                Id: value?.tableID !== "null" ? value?.tableID : "",
                                ReplicationId: $('#replicationID').val(),
                                ReplicationName: $('#textName').val(),
                                ReplicationTypeId: $('#ddlReplicationTypeID').val(),
                                ReplicationType: $('#replicaionType').val(),
                                SiteId: $('#siteNames').val(),
                                SiteName: $('#names').val(),
                                Properties: encryption,
                                SourceDirectory: value.sourceDirectory,
                                DestinationDirectory: value.destinationDirectory,
                                ModeType: "Pending",
                                JobProperties: "NULL",
                                ScheduleProperties: "NULL",
                                RoboCopyOptionsId: value.properties,
                                LastSuccessfullReplTime: null,
                            };
                            replRSyncRCopyData.RoboCopyJobViewModels.push(roboCopyObj);
                        });
                    }

                    if ($('#ddlReplicationTypeID option:selected').text().trim().toLowerCase().includes("datasync")) {
                        replRSyncRCopyData.DataSyncJobViewModels = [];
                        fd.DataSyncTable.forEach(function (value, index) {
                            let dataSyncObj = {
                                Id: value?.tableID !== "null" ? value?.tableID : "",
                                ReplicationId: $('#replicationID').val(),
                                ReplicationName: $('#textName').val(),
                                ReplicationTypeId: $('#ddlReplicationTypeID').val(),
                                ReplicationType: $('#replicaionType').val(),
                                SiteId: $('#siteNames').val(),
                                SiteName: $('#names').val(),
                                Properties: encryption,
                                SourceDirectory: value.sourceDirectory,
                                DestinationDirectory: value.destinationDirectory,
                                ModeType: "Pending",
                                JobProperties: "NULL",
                                ScheduleProperties: "NULL",
                                DataSyncOptionId: value.properties,
                                //LastSuccessfullReplTime: null,
                            };
                            replRSyncRCopyData.DataSyncJobViewModels.push(dataSyncObj);
                        });
                    }

                    let response = await $.ajax({
                        type: "POST",
                        url: RootUrl + replicationURL.createOrUpdate,
                        dataType: "json",
                        headers: {
                            'RequestVerificationToken': await gettoken()
                        },
                        contentType: 'application/json',
                        data: JSON.stringify(replRSyncRCopyData),
                        traditional: true
                    });

                    $("#CreateModal").modal('hide');
                    btnDisableReplication = false;

                    if (response.success) {
                        notificationAlert("success", response.data.message);
                        getReplicationLists();
                        setTimeout(() => {
                            let selectedReplType = $("#ddlReplicationTypeID :selected").val();
                            //window.location.reload(); //Incase if change this var value won't change.
                            dataTableCreateAndUpdate($("#saveButton"), dataTable, $("#search-in-type"), selectedReplType);
                        }, 2000);
                    } else {
                        errorNotification(response);
                        //setTimeout(() => {
                        //    //window.location.reload(); //Incase if change this var value won't change.                               
                        //}, 2000);
                    }
                }
            }
        }, 50)
    }, 200));
})