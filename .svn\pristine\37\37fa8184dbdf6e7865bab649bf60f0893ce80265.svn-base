﻿using ContinuityPatrol.Application.Features.TeamMaster.Events.Delete;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamMaster.Events
{
    public class DeleteTeamMasterEventTests
    {
        private readonly Mock<ILogger<TeamMasterDeletedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly TeamMasterDeletedEventHandler _handler;

        public DeleteTeamMasterEventTests()
        {
            _mockLogger = new Mock<ILogger<TeamMasterDeletedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new TeamMasterDeletedEventHandler(
                _mockUserService.Object,
                _mockUserActivityRepository.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_AddsUserActivityAndLogsInformation_WhenCalled()
        {
            var deletedEvent = new TeamMasterDeletedEvent
            {
                GroupName = "Team Alpha"
            };
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns("user-id");
            _mockUserService.Setup(s => s.LoginName).Returns("user-login");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("/api/team-master");
            _mockUserService.Setup(s => s.CompanyId).Returns("company-id");
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            await _handler.Handle(deletedEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "user-id" &&
                activity.LoginName == "user-login" &&
                activity.RequestUrl == "/api/team-master" &&
                activity.CompanyId == "company-id" &&
                activity.HostAddress == "***********" &&
                activity.Entity == Modules.TeamMaster.ToString() &&
                activity.Action == $"{ActivityType.Delete} {Modules.TeamMaster}" &&
                activity.ActivityType == ActivityType.Delete.ToString() &&
                activity.ActivityDetails == " Team master 'Team Alpha' deleted successfully."
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation("Team master 'Team Alpha' deleted successfully."), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenAddAsyncFails()
        {
            var deletedEvent = new TeamMasterDeletedEvent
            {
                GroupName = "Team Beta"
            };
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns("user-id");
            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .ThrowsAsync(new System.Exception("Database error"));

            var exception = await Assert.ThrowsAsync<System.Exception>(() => _handler.Handle(deletedEvent, cancellationToken));
            Assert.Equal("Database error", exception.Message);

            _mockLogger.Verify(logger => logger.LogInformation(It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task Handle_UsesDefaultValues_WhenUserIdIsNullOrEmpty()
        {
            var deletedEvent = new TeamMasterDeletedEvent
            {
                GroupName = "Team Gamma"
            };
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns((string)null);
            _mockUserService.Setup(s => s.LoginName).Returns("user-login");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("/api/team-master");
            _mockUserService.Setup(s => s.CompanyId).Returns("company-id");
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            await _handler.Handle(deletedEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                !string.IsNullOrEmpty(activity.UserId)
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation("Team master 'Team Gamma' deleted successfully."), Times.Once);
        }
    }
}
