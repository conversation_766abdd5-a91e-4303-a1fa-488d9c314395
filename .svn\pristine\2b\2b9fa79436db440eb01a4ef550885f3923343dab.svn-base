﻿using ContinuityPatrol.Application.Features.AlertMaster.Commands.Create;
using ContinuityPatrol.Application.Features.AlertMaster.Commands.Update;
using ContinuityPatrol.Application.Features.AlertMaster.Events.PaginatedView;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertMasterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using DevExpress.XtraReports.UI;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.Areas.Alert.Controllers;

[Area("Alert")]
public class ManageAlertController : BaseController
{
    public readonly IPublisher _publisher;
    //private readonly IAlertMasterService _alertMasterService;
    public static ILogger<ManageAlertController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    public ManageAlertController(IPublisher publisher,ILogger<ManageAlertController> logger, IMapper mapper, IDataProvider dataProvider)
    {
        _publisher = publisher;
        //_alertMasterService = alertMasterService;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }
    public async Task<IActionResult> List()
    {
        await _publisher.Publish(new AlertMasterPaginatedEvent());
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(AlertMasterListVm masterAlertModel)
    {
        var masterAlertId = Request.Form["id"];

        try
        {
            BaseResponse result;
            if (string.IsNullOrEmpty(masterAlertId))
            {
                var alertMaster = _mapper.Map<CreateAlertMasterCommand>(masterAlertModel);
                result = await _dataProvider.AlertMasterService.CreateAsync(alertMaster);
            }
            else
            {
                var alertMaster = _mapper.Map<UpdateAlertMasterCommand>(masterAlertModel);
                result = await _dataProvider.AlertMasterService.UpdateAsync(alertMaster);
            }

            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(message: $"An error occurred on manage alert page while processing the request. {ex.GetMessage()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetAlertMasterPaginatedListQuery query)
    {
        try
        {
            var paginatedResult = await _dataProvider.AlertMasterService.GetPaginatedAlertMasters(query);
            paginatedResult.Data.OrderBy(alert => int.Parse(alert.AlertId)).ToList();
            return Json(new { Success = true, data = paginatedResult });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on manage alert page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }
    //Manage Alert Report Download
    [HttpGet]
    public async Task<IActionResult> LoadReport(GetAlertMasterPaginatedListQuery query)
    {
        var reportsDirectory = "";
        try
        {
            string reportGeneratedName = WebHelper.UserSession.LoginName.ToString();
            query.PageSize = 10000;
            query.PageNumber = 1;
            var paginatedResult = await _dataProvider.AlertMasterService.GetPaginatedAlertMasters(query);
            paginatedResult.Data.OrderBy(alert => int.Parse(alert.AlertId)).ToList();
            string reportf = JsonConvert.SerializeObject(paginatedResult);
            XtraReport report = new Report.ReportTemplate.ManageAlerts(reportf, reportGeneratedName);
            var filenameSuffix = DateTime.Now.ToString("ddMMyyyyhhmmsstt");
            var fileName = "ManageAlertReport_" + filenameSuffix + ".pdf";
            reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);
            report.ExportToPdf(reportsDirectory);
            byte[] fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);
            return File(fileBytes, "application/pdf", fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on manage alert page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
        finally
        {

            if (System.IO.File.Exists(reportsDirectory))
            {
                System.IO.File.Delete(reportsDirectory);
            }
        }
    }
    public async Task<IActionResult> Delete_id(string id)
    {
        try
        {
            var delete = await _dataProvider.AlertMasterService.DeleteAsync(id);
            return Json(new { Success = true, data = delete });
        }
        catch (Exception ex)
        {
            _logger.LogError(message: $"An error occurred on manage alert page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }

    }
    [HttpGet]
    public async Task<bool> IsAlertIdExist(string alertId)
    {
        try
        {
            var isAlertExist = await _dataProvider.AlertMasterService.IsAlertMasterIdExist(alertId);
            return isAlertExist;
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on manage alert page while processing the request. {ex.GetJsonException()}");
            return false;
        }
    }

    public async Task<bool> IsAlertNameExist(string alertName, string alertId)
    {
        try
        {
            var isAlertnameExits = await _dataProvider.AlertMasterService.IsAlertMasterNameExist(alertName,alertId);
            return isAlertnameExits;
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on manage alert page while processing the request. {ex.GetJsonException()}");
            return false;
        }
    }
}