﻿using ContinuityPatrol.Application.Features.PluginManagerHistory.Events.Delete;

namespace ContinuityPatrol.Application.Features.PluginManagerHistory.Commands.Delete;

public class
    DeletePluginManagerHistoryCommandHandler : IRequestHandler<DeletePluginManagerHistoryCommand,
        DeletePluginManagerHistoryResponse>
{
    private readonly IPluginManagerHistoryRepository _pluginManagerHistoryRepository;
    private readonly IPublisher _publisher;

    public DeletePluginManagerHistoryCommandHandler(IPluginManagerHistoryRepository pluginManagerHistoryRepository,
        IPublisher publisher)
    {
        _pluginManagerHistoryRepository = pluginManagerHistoryRepository;
        _publisher = publisher;
    }

    public async Task<DeletePluginManagerHistoryResponse> Handle(DeletePluginManagerHistoryCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _pluginManagerHistoryRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.PluginManagerHistory),
            new NotFoundException(nameof(Domain.Entities.PluginManagerHistory), request.Id));

        eventToDelete.IsActive = false;

        await _pluginManagerHistoryRepository.UpdateAsync(eventToDelete);

        var response = new DeletePluginManagerHistoryResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.PluginManagerHistory), eventToDelete.ReferenceId),

            IsActive = eventToDelete.IsActive
        };
        await _publisher.Publish(
            new PluginManagerHistoryDeletedEvent { PluginManagerName = eventToDelete.PluginManagerName },
            cancellationToken);

        return response;
    }
}