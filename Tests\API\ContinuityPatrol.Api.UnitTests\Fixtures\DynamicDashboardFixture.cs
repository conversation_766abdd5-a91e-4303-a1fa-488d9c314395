using AutoFixture;
using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DynamicDashboardFixture
{
    public CreateDynamicDashboardCommand CreateDynamicDashboardCommand { get; set; }
    public CreateDynamicDashboardResponse CreateDynamicDashboardResponse { get; set; }
    public UpdateDynamicDashboardCommand UpdateDynamicDashboardCommand { get; set; }
    public UpdateDynamicDashboardResponse UpdateDynamicDashboardResponse { get; set; }
    public DeleteDynamicDashboardCommand DeleteDynamicDashboardCommand { get; set; }
    public DeleteDynamicDashboardResponse DeleteDynamicDashboardResponse { get; set; }
    public GetDynamicDashboardDetailQuery GetDynamicDashboardDetailQuery { get; set; }
    public DynamicDashboardDetailVm DynamicDashboardDetailVm { get; set; }
    public GetDynamicDashboardListQuery GetDynamicDashboardListQuery { get; set; }
    public List<DynamicDashboardListVm> DynamicDashboardListVm { get; set; }
    public GetDynamicDashboardNameUniqueQuery GetDynamicDashboardNameUniqueQuery { get; set; }
    public GetDynamicDashboardPaginatedListQuery GetDynamicDashboardPaginatedListQuery { get; set; }
    public PaginatedResult<DynamicDashboardListVm> DynamicDashboardPaginatedResult { get; set; }

    public DynamicDashboardFixture()
    {
        var fixture = new Fixture();

        // Configure fixture to handle circular references
        fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
            .ForEach(b => fixture.Behaviors.Remove(b));
        fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        // Create Commands
        CreateDynamicDashboardCommand = fixture.Create<CreateDynamicDashboardCommand>();
        UpdateDynamicDashboardCommand = fixture.Create<UpdateDynamicDashboardCommand>();
        DeleteDynamicDashboardCommand = fixture.Create<DeleteDynamicDashboardCommand>();

        // Create Responses
        fixture.Customize<CreateDynamicDashboardResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Success, true)
            .With(b => b.Message, "DynamicDashboard created successfully"));
        CreateDynamicDashboardResponse = fixture.Create<CreateDynamicDashboardResponse>();

        fixture.Customize<UpdateDynamicDashboardResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Success, true)
            .With(b => b.Message, "DynamicDashboard updated successfully"));
        UpdateDynamicDashboardResponse = fixture.Create<UpdateDynamicDashboardResponse>();

        fixture.Customize<DeleteDynamicDashboardResponse>(c => c
            .With(b => b.Success, true)
            .With(b => b.IsActive, false)
            .With(b => b.Message, "DynamicDashboard deleted successfully"));
        DeleteDynamicDashboardResponse = fixture.Create<DeleteDynamicDashboardResponse>();

        // Create Queries
        GetDynamicDashboardDetailQuery = fixture.Create<GetDynamicDashboardDetailQuery>();
        GetDynamicDashboardListQuery = fixture.Create<GetDynamicDashboardListQuery>();
        GetDynamicDashboardNameUniqueQuery = fixture.Create<GetDynamicDashboardNameUniqueQuery>();
        GetDynamicDashboardPaginatedListQuery = fixture.Create<GetDynamicDashboardPaginatedListQuery>();

        // Create ViewModels
        fixture.Customize<DynamicDashboardDetailVm>(c => c
            .With(b => b.ReferenceId, Guid.NewGuid().ToString)
            .With(b => b.Name, "Enterprise Dynamic Dashboard")
            .With(b => b.Url, "/dashboard/enterprise")
            .With(b => b.IsDelete, false));
        DynamicDashboardDetailVm = fixture.Create<DynamicDashboardDetailVm>();

        fixture.Customize<DynamicDashboardListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Name, "Enterprise Dashboard")
            .With(b => b.Url, "/dashboard/enterprise")
            .With(b => b.IsDelete, false));
        DynamicDashboardListVm = fixture.CreateMany<DynamicDashboardListVm>(5).ToList();

        // Create PaginatedResult using the Success factory method
        DynamicDashboardPaginatedResult = PaginatedResult<DynamicDashboardListVm>.Success(
            data: DynamicDashboardListVm,
            count: DynamicDashboardListVm.Count,
            page: 1,
            pageSize: 10
        );
    }
}
