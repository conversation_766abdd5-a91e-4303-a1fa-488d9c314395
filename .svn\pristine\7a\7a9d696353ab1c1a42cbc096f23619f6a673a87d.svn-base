﻿using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Update;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetByFormTypeId;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetFormTypeCategoryByName;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class FormMappingControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<IFormMappingService> _mockFormMappingService = new();
        private readonly Mock<ILogger<FormMappingController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private FormMappingController _controller;

        public FormMappingControllerShould()
        {

            Initialize();
        }
        internal void Initialize()
        {
            _controller = new FormMappingController(
               _mockPublisher.Object,
               //_mockFormMappingService.Object,
               _mockDataProvider.Object,
               _mockLogger.Object,
               _mockMapper.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public async Task List_ReturnsViewResult()
        {
            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task Mapped_ReturnsJsonResultWithSuccess()
        {
            
            var id = "someId";
            var formMapping = new List<FormTypeCategoryListVm>();
            
            _mockFormMappingService.Setup(service => service.GetFormMappingList()).ReturnsAsync(formMapping);
			_mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ReturnsAsync(new List<FormTypeCategoryListVm>());
			_mockDataProvider.Setup(x => x.FormMapping.GetPaginatedFormTypeCategory(new GetFormTypeCategoryPaginatedListQuery())).ReturnsAsync(new PaginatedResult<FormTypeCategoryListVm>());

			var result = await _controller.Mapped(id) as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task CreateOrUpdate_CreatesFormMappingSuccessfully()
        {
            // Arrange
            var formMappingViewModel = new FormMappingViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateFormTypeCategoryCommand();
            _mockMapper.Setup(mapper => mapper.Map<CreateFormTypeCategoryCommand>(formMappingViewModel)).Returns(command);
            _mockDataProvider.Setup(provider => provider.FormMapping.CreateAsync(command)).ReturnsAsync(new BaseResponse { Success = true, Message = "Created" });

            // Act
            var result = await _controller.CreateOrUpdate(formMappingViewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesFormMappingSuccessfully()
        {
            // Arrange
            var formMappingViewModel = new FormMappingViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateFormTypeCategoryCommand();
            _mockMapper.Setup(mapper => mapper.Map<UpdateFormTypeCategoryCommand>(formMappingViewModel)).Returns(command);
            _mockDataProvider.Setup(provider => provider.FormMapping.UpdateAsync(command)).ReturnsAsync(new BaseResponse { Success = true, Message = "Updated" });

            // Act
            var result = await _controller.CreateOrUpdate(formMappingViewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task ReplicationList_ReturnsJsonResultWithReplicationData()
        {
            
            var replicationList = new List<FormTypeCategoryListVm>();
            
            _mockFormMappingService.Setup(service => service.GetFormMappingList()).ReturnsAsync(replicationList);
			_mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ReturnsAsync(new List<FormTypeCategoryListVm>());
			_mockDataProvider.Setup(x => x.FormMapping.GetPaginatedFormTypeCategory(new GetFormTypeCategoryPaginatedListQuery())).ReturnsAsync(new PaginatedResult<FormTypeCategoryListVm>());

			var result = await _controller.ReplicationList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task ServerList_ReturnsJsonResultWithServerOSTypeData()
        {
            
            var formMappingList = new List<FormTypeCategoryListVm>();
           
            _mockDataProvider.Setup(provider => provider.FormMapping.GetFormMappingList()).ReturnsAsync(formMappingList);

            
            var result = await _controller.ServerList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task DatabaseList_ReturnsJsonResultWithDatabaseData()
        {
            // Arrange
            var databaseList = new List<FormTypeCategoryListVm>();
            _mockDataProvider.Setup(provider => provider.FormMapping.GetFormMappingList()).ReturnsAsync(databaseList);

            // Act
            var result = await _controller.DatabaseList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task SingleSignOnList_ReturnsJsonResultWithSingleSignOnData()
        {
            // Arrange
            var singleSignOnList = new List<FormTypeCategoryListVm>();
            
            _mockFormMappingService.Setup(service => service.GetFormMappingList()).ReturnsAsync(singleSignOnList);
			_mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ReturnsAsync(new List<FormTypeCategoryListVm>());
			_mockDataProvider.Setup(x => x.FormMapping.GetPaginatedFormTypeCategory(new GetFormTypeCategoryPaginatedListQuery())).ReturnsAsync(new PaginatedResult<FormTypeCategoryListVm>());
			// Act
			var result = await _controller.SingleSignOnList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
          
        }

        [Fact]
        public async Task NodeList_ReturnsJsonResultWithNodeData()
        {
            // Arrange
            var nodeList = new List<FormTypeCategoryListVm>();
            
            _mockFormMappingService.Setup(service => service.GetFormMappingList()).ReturnsAsync(nodeList);
			_mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ReturnsAsync(new List<FormTypeCategoryListVm>());
			_mockDataProvider.Setup(x => x.FormMapping.GetPaginatedFormTypeCategory(new GetFormTypeCategoryPaginatedListQuery())).ReturnsAsync(new PaginatedResult<FormTypeCategoryListVm>());
			// Act
			var result = await _controller.NodeList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task Delete_DeletesFormMappingSuccessfully()
        {
            // Arrange
            var id = "someId";
            _mockDataProvider.Setup(provider => provider.FormMapping.DeleteAsync(id)).ReturnsAsync(new BaseResponse { Success = true, Message = "Deleted" });

            // Act
            var result = await _controller.Delete(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task GetFormMappingByFormId_ReturnsJsonResultWithFormMapping()
        {
            // Arrange
            var formTypeId = "someFormTypeId";
            var version = "1.0";
            var formMapping = new FormTypeCategoryByFormTypeIdVm ();
            _mockDataProvider.Setup(provider => provider.FormMapping.GetFormMappingByFormTypeId(formTypeId, version)).ReturnsAsync(formMapping);

            // Act
            var result = await _controller.GetFormMappingByFormId(formTypeId, version) as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResultWithPaginatedFormTypeCategory()
        {
            // Arrange
            var query = new GetFormTypeCategoryPaginatedListQuery();
            var paginatedList = new PaginatedResult<FormTypeCategoryListVm>();
            _mockDataProvider.Setup(provider => provider.FormMapping.GetPaginatedFormTypeCategory(query)).ReturnsAsync(paginatedList);

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task FormMappingNameExist_ReturnsTrueIfNameExists()
        {
            // Arrange
            var formTypeName = "someName";
            var formTypeId = "someId";
            _mockDataProvider.Setup(provider => provider.FormMapping.IsFormMappingExist(formTypeName, formTypeId)).ReturnsAsync(true);

            // Act
            var result = await _controller.FormMappingNameExist(formTypeName, formTypeId);

            // Assert
            Assert.True(result);
        }

        // Exception handling tests
        [Fact]
        public async Task Mapped_Returns_Json_With_Error_When_ValidationException_Occurs()
        {
            // Arrange
            var id = "testId";
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ThrowsAsync(validationException);

            // Act
            var result = await _controller.Mapped(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":false", json);
            Assert.Contains("\"message\":", json);
        }

        [Fact]
        public async Task Mapped_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            var id = "testId";
            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Mapped(id);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Value);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("\"Message\":", json);
            Assert.Contains("\"ErrorCode\":", json);
        }

        [Fact]
        public async Task CreateOrUpdate_Returns_Json_With_Error_When_ValidationException_Occurs_Create()
        {
            // Arrange
            var formMappingViewModel = new FormMappingViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty id for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreateFormTypeCategoryCommand();
            _mockMapper.Setup(mapper => mapper.Map<CreateFormTypeCategoryCommand>(formMappingViewModel)).Returns(command);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockDataProvider.Setup(provider => provider.FormMapping.CreateAsync(command)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(formMappingViewModel);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task CreateOrUpdate_Returns_Json_With_Error_When_Exception_Occurs_Create()
        {
            // Arrange
            var formMappingViewModel = new FormMappingViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty id for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreateFormTypeCategoryCommand();
            _mockMapper.Setup(mapper => mapper.Map<CreateFormTypeCategoryCommand>(formMappingViewModel)).Returns(command);
            _mockDataProvider.Setup(provider => provider.FormMapping.CreateAsync(command)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(formMappingViewModel);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task CreateOrUpdate_Returns_Json_With_Error_When_ValidationException_Occurs_Update()
        {
            // Arrange
            var formMappingViewModel = new FormMappingViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new UpdateFormTypeCategoryCommand();
            _mockMapper.Setup(mapper => mapper.Map<UpdateFormTypeCategoryCommand>(formMappingViewModel)).Returns(command);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockDataProvider.Setup(provider => provider.FormMapping.UpdateAsync(command)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(formMappingViewModel);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task CreateOrUpdate_Returns_Json_With_Error_When_Exception_Occurs_Update()
        {
            // Arrange
            var formMappingViewModel = new FormMappingViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new UpdateFormTypeCategoryCommand();
            _mockMapper.Setup(mapper => mapper.Map<UpdateFormTypeCategoryCommand>(formMappingViewModel)).Returns(command);
            _mockDataProvider.Setup(provider => provider.FormMapping.UpdateAsync(command)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(formMappingViewModel);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task Delete_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            var id = "testId";
            _mockDataProvider.Setup(provider => provider.FormMapping.DeleteAsync(id)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetFormMappingListByName_ReturnsJsonResultWithSuccess()
        {
            // Arrange
            var name = "testName";
            var formTypeCategory = new List<FormTypeCategoryByNameVm>();
            _mockDataProvider.Setup(provider => provider.FormMapping.GetFormMappingListByName(name)).ReturnsAsync(formTypeCategory);

            // Act
            var result = await _controller.GetFormMappingListByName(name) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task GetFormMappingListByName_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            var name = "testName";
            _mockDataProvider.Setup(provider => provider.FormMapping.GetFormMappingListByName(name)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetFormMappingListByName(name);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task ReplicationList_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.ReplicationList();

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task ServerList_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.ServerList();

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task DatabaseList_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DatabaseList();

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task SingleSignOnList_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.SingleSignOnList();

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task NodeList_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.NodeList();

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetFormMappingByFormId_Returns_Json_With_Invalid_FormTypeId()
        {
            // Arrange
            string formTypeId = null; // Invalid formTypeId
            string version = "1.0";

            // Act
            var result = await _controller.GetFormMappingByFormId(formTypeId, version) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("\"Message\":\"formTypeId is not valid format\"", json);
            Assert.Contains("\"ErrorCode\":0", json);
        }

        [Fact]
        public async Task GetFormMappingByFormId_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            var formTypeId = "validId";
            var version = "1.0";
            _mockDataProvider.Setup(provider => provider.FormMapping.GetFormMappingByFormTypeId(formTypeId, version)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetFormMappingByFormId(formTypeId, version);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetPagination_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            var query = new GetFormTypeCategoryPaginatedListQuery();
            _mockDataProvider.Setup(provider => provider.FormMapping.GetPaginatedFormTypeCategory(query)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task FormMappingNameExist_Returns_False_When_Exception_Occurs()
        {
            // Arrange
            var formTypeName = "testName";
            var formTypeId = "testId";
            _mockDataProvider.Setup(provider => provider.FormMapping.IsFormMappingExist(formTypeName, formTypeId)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.FormMappingNameExist(formTypeName, formTypeId);

            // Assert
            Assert.False(result);
        }

        // ===== ADDITIONAL TESTS TO COVER UNCOVERED LINES =====

        [Fact]
        public async Task Mapped_FiltersFormMappingByFormId()
        {
            // Arrange
            var id = "testFormId";
            var formMapping = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { FormId = "testFormId", Name = "Test Form 1" },
                new FormTypeCategoryListVm { FormId = "otherFormId", Name = "Test Form 2" },
                new FormTypeCategoryListVm { FormId = "testFormId", Name = "Test Form 3" }
            };

            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ReturnsAsync(formMapping);

            // Act
            var result = await _controller.Mapped(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task ReplicationList_FiltersReplicationData()
        {
            // Arrange
            var replicationList = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { Name = "Replication", Id = "1" },
                new FormTypeCategoryListVm { Name = "REPLICATION", Id = "2" },
                new FormTypeCategoryListVm { Name = "Server", Id = "3" },
                new FormTypeCategoryListVm { Name = "replication", Id = "4" }
            };

            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ReturnsAsync(replicationList);

            // Act
            var result = await _controller.ReplicationList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task ServerList_FiltersServerData()
        {
            // Arrange
            var formMappingList = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { Name = "Server", Id = "1" },
                new FormTypeCategoryListVm { Name = "SERVER", Id = "2" },
                new FormTypeCategoryListVm { Name = "Database", Id = "3" },
                new FormTypeCategoryListVm { Name = "server", Id = "4" }
            };

            _mockDataProvider.Setup(provider => provider.FormMapping.GetFormMappingList()).ReturnsAsync(formMappingList);

            // Act
            var result = await _controller.ServerList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task DatabaseList_FiltersDatabaseData()
        {
            // Arrange
            var databaseList = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { Name = "Database", Id = "1" },
                new FormTypeCategoryListVm { Name = "DATABASE", Id = "2" },
                new FormTypeCategoryListVm { Name = "Server", Id = "3" },
                new FormTypeCategoryListVm { Name = "database", Id = "4" }
            };

            _mockDataProvider.Setup(provider => provider.FormMapping.GetFormMappingList()).ReturnsAsync(databaseList);

            // Act
            var result = await _controller.DatabaseList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task SingleSignOnList_FiltersSingleSignOnData()
        {
            // Arrange
            var singleSignOnList = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { Name = "Single SignOn", Id = "1" },
                new FormTypeCategoryListVm { Name = "SINGLE SIGNON", Id = "2" },
                new FormTypeCategoryListVm { Name = "Database", Id = "3" },
                new FormTypeCategoryListVm { Name = "single signon", Id = "4" }
            };

            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ReturnsAsync(singleSignOnList);

            // Act
            var result = await _controller.SingleSignOnList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task NodeList_FiltersNodeData()
        {
            // Arrange
            var nodeList = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { Name = "Node", Id = "1" },
                new FormTypeCategoryListVm { Name = "NODE", Id = "2" },
                new FormTypeCategoryListVm { Name = "Server", Id = "3" },
                new FormTypeCategoryListVm { Name = "node", Id = "4" }
            };

            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ReturnsAsync(nodeList);

            // Act
            var result = await _controller.NodeList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        // ===== ADDITIONAL EDGE CASE TESTS FOR COMPLETE COVERAGE =====

        [Fact]
        public async Task Mapped_HandlesNullFormIdInList()
        {
            // Arrange
            var id = "testFormId";
            var formMapping = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { FormId = null, Name = "Test Form 1" },
                new FormTypeCategoryListVm { FormId = "testFormId", Name = "Test Form 2" },
                new FormTypeCategoryListVm { FormId = "", Name = "Test Form 3" }
            };

            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ReturnsAsync(formMapping);

            // Act
            var result = await _controller.Mapped(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task ReplicationList_HandlesNullNamesInList()
        {
            // Arrange
            var replicationList = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { Name = null, Id = "1" },
                new FormTypeCategoryListVm { Name = "Replication", Id = "2" },
                new FormTypeCategoryListVm { Name = "", Id = "3" },
                new FormTypeCategoryListVm { Name = "replication", Id = "4" }
            };

            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ReturnsAsync(replicationList);

            // Act
            var result = await _controller.ReplicationList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task ServerList_HandlesNullNamesInList()
        {
            // Arrange
            var formMappingList = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { Name = null, Id = "1" },
                new FormTypeCategoryListVm { Name = "Server", Id = "2" },
                new FormTypeCategoryListVm { Name = "", Id = "3" },
                new FormTypeCategoryListVm { Name = "server", Id = "4" }
            };

            _mockDataProvider.Setup(provider => provider.FormMapping.GetFormMappingList()).ReturnsAsync(formMappingList);

            // Act
            var result = await _controller.ServerList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task DatabaseList_HandlesNullNamesInList()
        {
            // Arrange
            var databaseList = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { Name = null, Id = "1" },
                new FormTypeCategoryListVm { Name = "Database", Id = "2" },
                new FormTypeCategoryListVm { Name = "", Id = "3" },
                new FormTypeCategoryListVm { Name = "database", Id = "4" }
            };

            _mockDataProvider.Setup(provider => provider.FormMapping.GetFormMappingList()).ReturnsAsync(databaseList);

            // Act
            var result = await _controller.DatabaseList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task SingleSignOnList_HandlesNullNamesInList()
        {
            // Arrange
            var singleSignOnList = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { Name = null, Id = "1" },
                new FormTypeCategoryListVm { Name = "Single SignOn", Id = "2" },
                new FormTypeCategoryListVm { Name = "", Id = "3" },
                new FormTypeCategoryListVm { Name = "single signon", Id = "4" }
            };

            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ReturnsAsync(singleSignOnList);

            // Act
            var result = await _controller.SingleSignOnList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task NodeList_HandlesNullNamesInList()
        {
            // Arrange
            var nodeList = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { Name = null, Id = "1" },
                new FormTypeCategoryListVm { Name = "Node", Id = "2" },
                new FormTypeCategoryListVm { Name = "", Id = "3" },
                new FormTypeCategoryListVm { Name = "node", Id = "4" }
            };

            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ReturnsAsync(nodeList);

            // Act
            var result = await _controller.NodeList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        // ===== TESTS FOR CASE SENSITIVITY AND EXACT MATCHING =====

        [Fact]
        public async Task ReplicationList_FiltersExactCaseInsensitiveMatch()
        {
            // Arrange
            var replicationList = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { Name = "Replication Test", Id = "1" }, // Should not match
                new FormTypeCategoryListVm { Name = "Replication", Id = "2" }, // Should match
                new FormTypeCategoryListVm { Name = "Test Replication", Id = "3" }, // Should not match
                new FormTypeCategoryListVm { Name = "REPLICATION", Id = "4" } // Should match
            };

            _mockDataProvider.Setup(x => x.FormMapping.GetFormMappingList()).ReturnsAsync(replicationList);

            // Act
            var result = await _controller.ReplicationList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task ServerList_FiltersExactCaseInsensitiveMatch()
        {
            // Arrange
            var formMappingList = new List<FormTypeCategoryListVm>
            {
                new FormTypeCategoryListVm { Name = "Server Test", Id = "1" }, // Should not match
                new FormTypeCategoryListVm { Name = "Server", Id = "2" }, // Should match
                new FormTypeCategoryListVm { Name = "Test Server", Id = "3" }, // Should not match
                new FormTypeCategoryListVm { Name = "SERVER", Id = "4" } // Should match
            };

            _mockDataProvider.Setup(provider => provider.FormMapping.GetFormMappingList()).ReturnsAsync(formMappingList);

            // Act
            var result = await _controller.ServerList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }
    }
}
