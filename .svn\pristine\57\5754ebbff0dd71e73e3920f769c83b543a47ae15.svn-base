﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ServerType.Events.Create;

public class ServerTypeCreatedEventHandler : INotificationHandler<ServerTypeCreatedEvent>
{
    private readonly ILogger<ServerTypeCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ServerTypeCreatedEventHandler(ILoggedInUserService userService,
        ILogger<ServerTypeCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ServerTypeCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Entity = Modules.ServerType.ToString(),
            Action = $"{ActivityType.Create} {Modules.ServerType}",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"ServerType '{createdEvent.Name}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"ServerType '{createdEvent.Name}' created successfully.");
    }
}