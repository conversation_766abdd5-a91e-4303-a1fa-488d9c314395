﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Helper;
namespace ContinuityPatrol.Persistence.Repositories;

public class DRReadyLogRepository : BaseRepository<DRReadyLog>, IDrReadyLogRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public DRReadyLogRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<DRReadyLog>> GetDrReadyLogListByStartTimeAndEndTime(string startTime, string endTime, string businessServiceId)
    {
        var result = await _dbContext.DrReadyLog
         .Where(x => x.CreatedDate.Date >= startTime.ToDateTime() &&
                     x.CreatedDate.Date <= endTime.ToDateTime() && x.BusinessServiceId.Equals(businessServiceId)).ToListAsync();


        return result;
    }

    public override async Task<IReadOnlyList<DRReadyLog>> ListAllAsync()
    {
        var dRReadyLogs = base.QueryAll(businessService => businessService.IsActive);

        return _loggedInUserService.IsAllInfra
            ? await dRReadyLogs.ToListAsync()
            : AssignedBusinessServices(dRReadyLogs);
    }

    public override Task<DRReadyLog> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilterAsync(dataLags => dataLags.ReferenceId.Equals(id)).Result.SingleOrDefault());
    }

    public async Task<List<DRReadyLog>> GetDrReadyLogForDrReadyReportByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "BusinessServiceId", "BusinessServiceId cannot be invalid");

        var dRReadyLogs = base.QueryAll(businessService => businessService.IsActive);

        return (List<DRReadyLog>)(_loggedInUserService.IsAllInfra
            ? await dRReadyLogs.Where(x => x.BusinessServiceId.Equals(businessServiceId)).ToListAsync()
            : AssignedBusinessServices(dRReadyLogs));
    }

    public async Task<DRReadyLog> GetDrReadyLogByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "BusinessServiceId", "BusinessServiceId cannot be invalid");

        var dRReadyLogs = GetByBusinessServiceIdAsync(businessServiceId,
            businessService => businessService.ReferenceId.Equals(businessServiceId));

        return _loggedInUserService.IsAllInfra
            ? await dRReadyLogs.FirstOrDefaultAsync()
            : GetByBusinessServiceId(dRReadyLogs.FirstOrDefault());
    }

    public Task<List<DRReadyLog>> GetDrReadyLogByLast7Days()
    {
        var matches = _dbContext.DrReadyLog.Active()
            .Where(x => x.LastModifiedDate.Date >= DateTime.Now.Date.AddDays(-7))
            .ToListAsync();

        return matches;
    }

    //Filters
    public IReadOnlyList<DRReadyLog> AssignedBusinessServices(IQueryable<DRReadyLog> businessServices)
    {
        var services = new List<DRReadyLog>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                services.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                    where businessService.BusinessServiceId == assignedBusinessService.Id
                    select businessService);
        return services;
    }

    public DRReadyLog GetByBusinessServiceId(DRReadyLog businessService)
    {
        var services = AssignedEntity.AssignedBusinessServices
            .Where(assignedBusinessService => businessService.BusinessServiceId == assignedBusinessService.Id)
            .Select(_ => businessService).SingleOrDefault();

        return services;
    }

    public IQueryable<DRReadyLog> GetByBusinessServiceIdAsync(string id,
        Expression<Func<DRReadyLog, bool>> expression = null)
    {
        return _loggedInUserService.IsParent
            ? Entities.Where(x => x.BusinessServiceId.Equals(id))
            : FilterBy(expression);
    }
}