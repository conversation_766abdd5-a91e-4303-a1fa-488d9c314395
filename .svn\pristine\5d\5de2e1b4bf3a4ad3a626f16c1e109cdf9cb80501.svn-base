﻿using ContinuityPatrol.Application.Features.ServerLog.Commands.Create;
using ContinuityPatrol.Application.Features.ServerLog.Commands.Update;
using ContinuityPatrol.Application.Features.ServerLog.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ServerLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;
////

namespace ContinuityPatrol.Application.Mappings;

public class ServerLogProfile : Profile
{
    public ServerLogProfile()
    {
        CreateMap<ServerLog, CreateServerLogCommand>().ReverseMap();
        CreateMap<ServerLog, ServerLogViewModel>().ReverseMap();

        CreateMap<CreateServerLogCommand, ServerLogViewModel>().ReverseMap();
        CreateMap<UpdateServerLogCommand, ServerLogViewModel>().ReverseMap();

        CreateMap<UpdateServerLogCommand, ServerLog>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<ServerLog, ServerLogListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<ServerLog, GetServerLogDetailVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PaginatedResult<ServerLog>, PaginatedResult<ServerLogListVm>>()
   .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}