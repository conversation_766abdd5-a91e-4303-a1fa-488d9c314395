using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.DriftEventModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DriftEvent.Queries.GetPaginatedList;

public class
    GetDriftEventPaginatedListQueryHandler : IRequestHandler<GetDriftEventPaginatedListQuery,
        PaginatedResult<DriftEventListVm>>
{
    private readonly IDriftEventRepository _driftEventRepository;
    private readonly IMapper _mapper;

    public GetDriftEventPaginatedListQueryHandler(IMapper mapper, IDriftEventRepository driftEventRepository)
    {
        _mapper = mapper;
        _driftEventRepository = driftEventRepository;
    }

    public async Task<PaginatedResult<DriftEventListVm>> Handle(GetDriftEventPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DriftEventFilterSpecification(request.SearchString);

        var queryable =await  _driftEventRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var driftEventList = _mapper.Map<PaginatedResult<DriftEventListVm>>(queryable);
         
        return driftEventList;
        //var queryable = _driftEventRepository.GetPaginatedQuery();

        //var productFilterSpec = new DriftEventFilterSpecification(request.SearchString);

        //var driftEventList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<DriftEventListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return driftEventList;
    }
}