﻿function onChangeTextFieldValidation(modulename) {
    let requiredInputsMinLengthRestrict = $(".formeo-render .f-field-group input[required][input-type='text'][minlength][restrict]")
        .not("[name='VirtualIPAddress'], [readonly='readonly']");

    if (requiredInputsMinLengthRestrict?.length) {
        requiredInputsMinLengthRestrict?.each(function () {
            let $this = $(this);
            let id = $this?.attr("id");
            document.getElementById(id)?.addEventListener("keyup", async function () {

                if ($this.is(':visible')) {
                    $this.next(".dynamic-input-field").remove();
                    let inputVal = this.value;
                    let minLength = $(this)?.attr("minLength");
                    let maxLength = $(this)?.attr("maxLength");
                    let res = await formBuilderTextValidation($this, modulename);
                    if (res) {
                        if (inputVal.length > 0) {
                            if (!pattern.test(inputVal)) {
                                $this.after(filedValidationError("Only & _ @ . - special characters allowed"));
                            } else if ((!minLength && !maxLength) || (minLength > 0 && maxLength > 0)) {

                                if (inputVal.length < minLength || inputVal.length > maxLength) {
                                    $this.after(filedValidationError(`Between ${minLength} to ${maxLength} characters`));
                                } else {
                                    $this.next(".dynamic-input-field").remove();
                                }
                            }
                        }
                    }
                }
            });

            document.getElementById(id)?.addEventListener("keydown", async function (event) {
                if ($this.is(':visible')) {
                    if (event.key === ' ') {
                        const value = this.value;
                        const selectionStart = this.selectionStart;
                        const len = value.trim().length;

                        if (len < selectionStart) {
                            event.preventDefault(); // Prevent adding more than two consecutive spaces
                        }
                    }
                }
            });
        });
    }

    let requiredInputsMinLength = $(".formeo-render .f-field-group input[required][input-type='text'][minlength]")
        .not("[restrict], [name='VirtualIPAddress'], [readonly='readonly']");

    if (requiredInputsMinLength?.length) {
        requiredInputsMinLength?.each(function () {
            let $this = $(this);
            let id = $this?.attr("id");
            document.getElementById(id)?.addEventListener("keyup", async function () {

                if ($this.is(':visible')) {
                    $this.next(".dynamic-input-field").remove();
                    let inputVal = this.value;
                    let minLength = $(this)?.attr("minLength");
                    let maxLength = $(this)?.attr("maxLength");
                    const intMinValue = parseInt(minLength, 10);
                    const intMaxValue = parseInt(maxLength, 10);
                    let type = $(this)?.attr("type");
                    let errorMessage = type === 'number' ? 'digits' : 'characters'
                    $this.next(".dynamic-input-field").remove();
                    let res = await formBuilderTextValidation($this, modulename);

                    if (res) {
                        if ((!minLength && !maxLength) || (intMinValue >= 0 && intMaxValue > 0)) {
                            if (inputVal.length < intMinValue || inputVal.length > intMaxValue) {
                                $this.after(filedValidationError(`Between ${minLength} to ${maxLength} characters`));
                            } else {
                                $this.next(".dynamic-input-field").remove();
                            }
                        }
                    }
                }
            });

            document.getElementById(id)?.addEventListener("keydown", async function (event) {

                if ($this.is(':visible')) {
                    if (event.key === ' ') {
                        const value = this.value;
                        const selectionStart = this.selectionStart;
                        const len = value.trim().length;

                        if (len < selectionStart) {
                            event.preventDefault(); // Prevent adding more than two consecutive spaces
                        }
                    }
                }
            });
        });
    }

    let inputsMinLength = $(".formeo-render .f-field-group input[input-type='text'][minlength]")
        .not("[restrict], [required], [name='VirtualIPAddress'], [readonly='readonly']");

    if (inputsMinLength?.length) {
        inputsMinLength?.each(function () {
            let $this = $(this);
            document.getElementById($(this)?.attr("id"))?.addEventListener("keyup", function (event) {

                if ($this.is(':visible')) {
                    let inputVal = this.value;
                    let minLength = $(this)?.attr("minLength");
                    let maxLength = $(this)?.attr("maxLength");
                    let type = $(this)?.attr("type");
                    let errorMessage = type === 'number' ? 'digits' : 'characters';
                    $this.next(".dynamic-input-field").remove();

                    if (inputVal.includes('<')) {
                        $this.after(filedValidationError("Special characters not allowed"));
                        return;
                    }
                    if (inputVal.length > 0) {
                        if (inputVal.length >= minLength && inputVal.length <= maxLength) {
                            $this.next(".dynamic-input-field").remove();

                            if ((!minLength && !maxLength) || (minLength > 0 && maxLength > 0)) {
                                if (inputVal.length < minLength || inputVal.length > maxLength) {
                                    $this.after(filedValidationError(`Between ${minLength} to ${maxLength} ${errorMessage}`));
                                } else {
                                    $this.next(".dynamic-input-field").remove();
                                }
                            }
                        } else {
                            event.preventDefault();
                        }
                    } else {
                        $this.next(".dynamic-input-field").remove();
                    }
                }
            });
        });
    }

    let requiredInputsText = $(".formeo-render .f-field-group input[required][input-type='text']")
        .not("[minlength], [restrict], [name='VirtualIPAddress'], [readonly='readonly']");

    if (requiredInputsText?.length) {
        requiredInputsText?.each(function () {
            let $this = $(this);
            document.getElementById($this?.attr("id"))?.addEventListener("keyup", async function () {

                if ($this.is(':visible')) {
                    let value = $(this).val();
                    $(this).val(value.replace(/\s{2,}/g, ' ')); //Restrict Multiple spaces
                    await formBuilderTextValidation($this, modulename);
                }
            });
        });
    }

    let requiredURLInput = $(".formeo-render .f-field-group input[required][attrid='URLField']");

    if (requiredURLInput?.length) {
        requiredURLInput?.each(function () {
            let $this = $(this);
            document.getElementById($this?.attr("id"))?.addEventListener("keyup", async function () {

                if ($this.is(':visible')) {
                    let value = $(this).val();
                    $(this).val(value.replace(/\s{2,}/g, ' '));  //Restrict Multiple spaces
                    await formBuilderRequiredURLValidation($this)
                }
            });
        });
    }
}

function onChangeFormBuilderValidation(modulename) {
    if (modulename === 'server') {
        let requiredSudoSuTable = $("table[required]");

        if (requiredSudoSuTable?.length > 0) {
            requiredSudoSuTable?.each(function () {
                let $this = $(this);
                let className = $this.attr("class");
                var elements = document.getElementsByClassName(className);

                if (elements.length > 0) {
                    elements[0].addEventListener("keyup", async function () {
                        if ($(this).is(':visible')) { 
                            await SubstituteAuthenticationType();
                            await SubstituteAuthenticationUser();
                        }
                    });
                }
            });
        }
    }

    if (modulename === "node") {
        //For Node.js source, target archieve log path
        let requiredsourcetargetPath = $(".formeo-render .f-field-group input[input-type='path']")
            .not("[required]"); //[type='text']" it's optional in 4.5

        requiredsourcetargetPath?.each(function () {
            let $this = $(this);
            let name = $this.attr("name").toLowerCase();

            if (name === "sourcearchivelogpath" || name === "targetarchivelogpath") {
                document.getElementById($this.attr("id"))?.addEventListener("keyup", async function () {

                    if ($this.is(':visible')) {
                        $this.next(".dynamic-input-field").remove();
                        let value = $this.val();

                        if (value) {
                            const regex = /^((?!.*\/\/.*)(?!.*\/ .*)\/{1}([^\\(){}:\*\?<>\|'])+.)$|[a-zA-Z]:(\\\w+)*([\\]|[.])?$/; //in CP-4.5

                            if (!regex.test(value)) {
                                $this.next(".dynamic-input-field").remove();
                                $this.after(filedValidationError("Enter valid path"));
                            }
                        }
                    }
                });
            }
        });
    }

    //Text field req, res, min, max.
    onChangeTextFieldValidation(modulename);

    let requiredpassword = $(".formeo-render .f-field-group input[required][type='password']");

    requiredpassword?.each(function () {
        let $this = $(this);
        document.getElementById($this.attr("id"))?.addEventListener("keyup", async function () {

            if ($this.is(':visible')) {
                PasswordValidation($($this)?.attr('placeholder'), $this, 'onchange');
            }
        });
    });

    let EncyDecrpassword = $(".formeo-render .f-field-group input[type='password']");//when it's optional also

    EncyDecrpassword?.each(function () {
        let $this = $(this);
        let id = $this.attr("id");
        document.getElementById(id)?.addEventListener("focus", async function () {

            if ($this.is(':visible')) {
                let $thisval = $(this).val();

                if ($thisval) {
                    let pwd = await DecryptPassword($thisval)//Common.js.
                    $(`input[name=${$(this).attr('name') }]`).val(pwd);
                }
            }
        });

        document.getElementById(id)?.addEventListener('blur', async function () {

            if ($this.is(':visible')) {
                let $thisval = $(this).val();

                if ($thisval) {
                    let pwd = await EncryptPassword($thisval)//Common.js.
                    $(`input[name=${$(this).attr('name') }]`).val(pwd);
                }
            }
        });
    });

    let requiredPath = $(".formeo-render .f-field-group input[required][input-type='path']")
        .not("[restrict], [minlength], [maxlength]");

    if (requiredPath?.length > 0) {
        requiredPath?.each(function () {
            let $this = $(this);
            document.getElementById($this.attr("id"))?.addEventListener("keyup", async function () {

                if ($this.is(':visible')) {
                    await formBuilderRequiredPathValidation($this, modulename);
                }
            });
        });
    }

    let requiredInputsMinLength = $(".formeo-render .f-field-group input[required][input-type='path'][maxlength]")
        .not("[restrict]");

    if (requiredInputsMinLength?.length) {
        requiredInputsMinLength?.each(function () {
            let $this = $(this);
            let id = $this?.attr("id");
            document.getElementById(id)?.addEventListener("keyup", async function () {

                if ($this.is(':visible')) {
                    $this.next(".dynamic-input-field").remove();
                    let inputVal = this.value;
                    let minLength = $(this)?.attr("minLength");
                    let maxLength = $(this)?.attr("maxLength");
                    const intMinValue = parseInt(minLength, 10);
                    const intMaxValue = parseInt(maxLength, 10);
                    let type = $(this)?.attr("type");
                    let errorMessage = type === 'number' ? 'digits' : 'characters'
                    $this.next(".dynamic-input-field").remove();
                    let pathRes = await formBuilderRequiredPathValidation($this, modulename);

                    if (pathRes) {
                        let res = await formBuilderTextValidation($this, modulename);
                        if (res) {
                            if ((!minLength && !maxLength) || (intMinValue >= 0 && intMaxValue > 0)) {
                                if (inputVal.length < intMinValue || inputVal.length > intMaxValue) {
                                    $this.after(filedValidationError(`Between ${minLength} to ${maxLength} ${errorMessage}`));
                                } else {
                                    $this.next(".dynamic-input-field").remove();
                                }
                            }
                        }
                    }
                }
            });

            document.getElementById(id)?.addEventListener("keydown", async function (event) {

                if ($this.is(':visible')) {
                    if (event.key === ' ') {
                        const value = this.value;
                        const selectionStart = this.selectionStart;
                        const len = value.trim().length;

                        if (len < selectionStart) {
                            event.preventDefault(); // Prevent adding more than two consecutive spaces
                        }
                    }
                }
            });
        });
    }

    let requiredTextarea = $(".formeo-render .f-field-group textarea[required]");

    if (requiredTextarea?.length > 0) {
        requiredTextarea?.each(function () {
            let $this = $(this);
            document.getElementById($(this)?.attr("id"))?.addEventListener("keyup", function () {

                if ($this.is(':visible')) {
                    textAreaFieldValidation($this, 'onchange');
                }
            });
        });
    }

    let restrictRequiredDate = $(".formeo-render .f-field-group input[required][type='date']");

    if (restrictRequiredDate?.length > 0) {
        restrictRequiredDate?.each(function () {
            let $this = $(this);
            document.getElementById($(this)?.attr("id"))?.addEventListener("change", function () {

                if ($this.is(':visible')) {
                    dateFieldValidation(this.value, $this, 'onchange');
                }
            });
        });
    }

    let restrictRequiredInputs = $(".formeo-render .f-field-group input[restrict]")
        .not("[required], [minlength]");

    restrictRequiredInputs?.each(function () {
        let $this = $(this);
        document.getElementById($(this)?.attr("id"))?.addEventListener("keyup", function () {

            if ($this.is(':visible')) {
                $this.next(".dynamic-input-field").remove();
                let inputValue = this.value;

                if (inputValue) {
                    if (!pattern.test(inputValue)) {
                        $this.after(filedValidationError("Only & _ @ . - special characters allowed"));
                    }
                }
            }
        });
    });


    //[type='number'] //Shoule be seperate. if give input char e cleared input field.
    let requiredInputsNumber = $(".formeo-render .f-field-group input[required][type='number']")
        .not("[minlength][restrict], [name='VirtualIPAddress'][readonly='readonly']");

    requiredInputsNumber?.each(function () {
        let $this = $(this);
        let id = $this?.attr("id");
        function handleEvent(event) {

            if ($this.is(':visible')) {
                if (event.key === 'e' || event.key === 'E' || event.key === '-') {
                    event.preventDefault();
                } else {
                    formBuilderTextValidation($this, modulename);
                }
            }
        }

        let element = document.getElementById(id);
        element?.addEventListener("keyup", handleEvent);
        element?.addEventListener("keydown", handleEvent);
    });

    let restrictRequiredNotMinlengthInputs = $(".formeo-render .f-field-group input[restrict][required]")
        .not("[minlength]");

    restrictRequiredNotMinlengthInputs?.each(function () {
        let $this = $(this);
        let id = $this?.attr("id");
        document.getElementById(id)?.addEventListener("keyup", async function () {

            if ($this.is(':visible')) {
                let inputVal = this.value;

                if (!inputVal) {
                    await formBuilderTextValidation($this, modulename);
                } else {
                    $this.next(".dynamic-input-field").remove();

                    if (!pattern.test(inputVal)) {
                        $this.after(filedValidationError("Only & _ @ . - special characters allowed"));
                    } else {
                        $this.next(".dynamic-input-field").remove();
                    }
                }
            }
        });

        document.getElementById(id)?.addEventListener("keydown", async function (event) {

            if ($this.is(':visible')) {
                if (event.key === ' ') {
                    const value = this.value;
                    const selectionStart = this.selectionStart;
                    const len = value.trim().length;

                    if (len < selectionStart) {
                        event.preventDefault(); // Prevent adding more than two consecutive spaces
                    }
                }
            }
        });
    });

    let restrictMinLengthNotRequiredInputs = $(".formeo-render .f-field-group input[restrict][minlength]")
        .not("[required]");

    restrictMinLengthNotRequiredInputs?.each(function () {
        let $this = $(this);
        document.getElementById($this?.attr("id"))?.addEventListener("keyup", async function () {

            if ($this.is(':visible')) {
                $this.next(".dynamic-input-field").remove();
                let inputVal = this.value;
                let minLength = $(this)?.attr("minLength");
                let maxLength = $(this)?.attr("maxLength");

                if (inputVal) {
                    if (!pattern.test(inputVal)) {
                        $this.after(filedValidationError("Only & _ @ . - special characters allowed"));
                    } else {
                        if (inputVal.length > 0) {
                            if (!minLength && !maxLength || minLength > 0 && maxLength > 0) {
                                if (inputVal.length < minLength || inputVal.length > maxLength) {
                                    $this.after(filedValidationError(`Between ${minLength} to ${maxLength} characters`));
                                } else {
                                    $this.next(".dynamic-input-field").remove();
                                }
                            }
                        }
                    }
                }
            }
        });
    });

    //['ipAddressField']
    let requiredInputsIpAddress = $(".formeo-render .f-field-group input[required][attrid='ipAddressField']");
    requiredInputsIpAddress?.each(function () {
        let $this = $(this);
        document.getElementById($this?.attr("id"))?.addEventListener("keyup", function () {

            if ($this.is(':visible')) {
                formBuilderIPAddressValidation($this);
            }
        });
    });

    function readonly(ipAddressHostName) {
        let inputs = document.querySelectorAll(ipAddressHostName);

        if (inputs.length > 0) {
            inputs?.forEach(function (input) {
                document.getElementById(input?.id)?.addEventListener("keydown", async function (event) {
                    event.preventDefault();
                    input.setAttribute("readonly", "readonly");
                });
            });
        }
    }
    readonly("input[name='IpAddress'][readonly='readonly']");
    readonly("input[name='HostName'][readonly='readonly']");
    readonly("input[name='VirtualIPAddress'][readonly='readonly']");

    let requiredSelects = $(".formeo-render .f-field-group select[required]")
        .not("[multiple]");

    requiredSelects?.each(function () {
        let $this = $(this);
        $("#" + $this?.attr("id")).on("change", async function () {

            if ($this.is(':visible')) {
                restrictSelectEmptyFields = true;
                let disableSelectTagTitle = document.querySelectorAll('.select2-selection__rendered');
                disableSelectTagTitle.forEach(async function (selectElement) {
                    let $this = $(selectElement);
                    $this.attr('title', '');
                });

                restrictSelectEmptyFields = await formBuilderSelectFieldValidation($this, modulename);

                //Remove SSH Password Validation
                let SSHUser = $("input[name='SSHUser']");
                SSHUser?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });
                let SSHPassword = $("input[name='SSHPassword']");
                SSHPassword?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });

                //Remove SSH Key Validation
                let SSHKeyUser = $("input[name='SSHKeyUser']");
                SSHKeyUser?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });
                let SSHKeyPath = $("input[name='SSHKeyPath']");
                SSHKeyPath?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });

                //Remove WMI Validation
                let WMIUser = $("input[name='WMIUser']");
                WMIUser?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });
                let WMIPassword = $("input[name='WMIPassword']");
                WMIPassword?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });

                //Remove PowerShell Validation
                let PowerShellUser = $("input[name='PowerShellUser']");
                PowerShellUser?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });
                let PowerShellPassword = $("input[name='PowerShellPassword']");
                PowerShellPassword?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });
            }
        });
    });

    let requiredSelects2 = $(".formeo-render .f-field-group select[multiple][required]");

    requiredSelects2?.each(function () {
        let $this = $(this);
        $("#" + $this?.attr("id")).on("change", async function () {

            if ($this.is(':visible')) {
                restrictSelectEmptyFields = true;
                let disableSelectTagTitle = document.querySelectorAll('.select2-selection__rendered');
                disableSelectTagTitle.forEach(async function (selectElement) {
                    let $this = $(selectElement);
                    $this.attr('title', '');
                });

                let placeholderValue = $($this)?.attr('placeholder');
                let result;

                if (placeholderValue) {
                    result = placeholderValue?.charAt(0)?.toUpperCase() + placeholderValue?.slice(1)?.toLowerCase();
                } else {
                    let labelText2 = "nodes";
                    result = labelText2?.toLowerCase();
                }
                let inputValue2 = $this?.val();
                inputValue2 = inputValue2?.filter(item => item !== '');
                let value;

                if (Array?.isArray(inputValue2)) {
                    if (inputValue2?.length > 0) {
                        value = 'Not empty';
                    } else {
                        value = "";
                    }
                }
                if (typeof inputValue2 === 'string') {
                    value = $this?.val();
                    if (value && value?.toLowerCase()?.replace(/\*/g, '')?.includes('select')) value = ""
                }
                $this.next(".dynamic-select-tag").remove();
                $this.next(".dynamic-input-field").remove();

                if (value?.length === 0 || value === "") {
                    $this.after(`<div class="dynamic-select-tag field-validation-error-selecttag2">
                                    <span class="required-field-msg">${result}</span>
                                  </div>`);
                    restrictSelectEmptyFields = false;
                }
            }
        });
    });
}