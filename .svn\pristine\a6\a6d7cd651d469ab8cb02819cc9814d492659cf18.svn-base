using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class IncidentLogsFixture : IDisposable
{
    public List<IncidentLogs> IncidentLogsPaginationList { get; set; }
    public List<IncidentLogs> IncidentLogsList { get; set; }
    public IncidentLogs IncidentLogsDto { get; set; }

    public const int IncidentNumber = 12345;
    public const string InfraObjectId = "INFRA_123";

    public ApplicationDbContext DbContext { get; private set; }

    public IncidentLogsFixture()
    {
        var fixture = new Fixture();

        IncidentLogsList = fixture.Create<List<IncidentLogs>>();

        IncidentLogsPaginationList = fixture.CreateMany<IncidentLogs>(20).ToList();

        // Setup proper test data for IncidentLogsPaginationList
        IncidentLogsPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        IncidentLogsPaginationList.ForEach(x => x.IsActive = true);
        IncidentLogsPaginationList.ForEach(x => x.IncidentNumber = IncidentNumber);
        IncidentLogsPaginationList.ForEach(x => x.InfraObjectId = InfraObjectId);

        // Setup proper test data for IncidentLogsList
        IncidentLogsList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        IncidentLogsList.ForEach(x => x.IsActive = true);
        IncidentLogsList.ForEach(x => x.IncidentNumber = IncidentNumber);
        IncidentLogsList.ForEach(x => x.InfraObjectId = InfraObjectId);

        IncidentLogsDto = fixture.Create<IncidentLogs>();
        IncidentLogsDto.ReferenceId = Guid.NewGuid().ToString();
        IncidentLogsDto.IsActive = true;
        IncidentLogsDto.IncidentNumber = IncidentNumber;
        IncidentLogsDto.Severity = "High";
        IncidentLogsDto.Message = "Test incident log message";
        IncidentLogsDto.InfraObjectId = InfraObjectId;
        IncidentLogsDto.InfraObjectName = "Test Infrastructure Object";

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
