using ContinuityPatrol.Application.Features.Report.Queries.DriftReport;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IDriftEventRepository : IRepository<DriftEvent>
{
    Task<bool> IsNameExist(string name, string id);
    Task<List<DriftEvent>> GetStartDateAndEndDate(string startDate, string endDate);
    Task<List<DriftEvent>> GetInfraObjectIdByStatus(string startDate, string endDate, string infraObjectId);
    Task<List<DriftEvent>> GetStartDateEndDateAndInfraObjectIdAndStatus(string startDate, string endDate, string infraObjectId, string statusId);
}