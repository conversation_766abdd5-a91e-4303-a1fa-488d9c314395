﻿using ContinuityPatrol.Application.Features.ReplicationJob.Events.UpdateState;

namespace ContinuityPatrol.Application.Features.ReplicationJob.Commands.UpdateReplicationJobState;

public class
    UpdateReplicationJobStateCommandHandler : IRequestHandler<UpdateReplicationJobStateCommand,
        UpdateReplicationJobStateResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IReplicationJobRepository _replicationJobRepository;

    public UpdateReplicationJobStateCommandHandler(IReplicationJobRepository replicationJobRepository, IMapper mapper,
        IPublisher publisher)
    {
        _replicationJobRepository = replicationJobRepository;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<UpdateReplicationJobStateResponse> Handle(UpdateReplicationJobStateCommand request,
        CancellationToken cancellationToken)
    {
        if (request.UpdateReplicationJobStates.Count == 0)
            throw new InvalidDataException("Update Job States List Can't be empty.");

        foreach (var replicationJob in request.UpdateReplicationJobStates)
        {
            var eventToUpdate = await _replicationJobRepository.GetByReferenceIdAsync(replicationJob.Id);

            if (eventToUpdate == null)
                throw new NotFoundException(nameof(Domain.Entities.ReplicationJob), replicationJob.Id);

            eventToUpdate.State = replicationJob.State;

            _mapper.Map(replicationJob, eventToUpdate, typeof(UpdateReplicationJobState),
                typeof(Domain.Entities.ReplicationJob));

            await _replicationJobRepository.UpdateAsync(eventToUpdate);

            await _publisher.Publish(
                new ReplicationJobStateUpdatedEvent { State = eventToUpdate.State, Name = eventToUpdate.Name },
                cancellationToken);
        }

        var response = new UpdateReplicationJobStateResponse
        {
            Message = request.UpdateReplicationJobStates.Count == 1
                ? $"Replication Job state has been updated to '{request.UpdateReplicationJobStates[0].State}' state successfully"
                : $"Replication Job states has been updated to '{request.UpdateReplicationJobStates[0].State}' state successfully"
        };
        return response;
    }
}