﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.InfraObject.Events.Update;

public class InfraObjectUpdatedEventHandler : INotificationHandler<InfraObjectUpdatedEvent>
{
    private readonly ILogger<InfraObjectUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public InfraObjectUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<InfraObjectUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(InfraObjectUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress ?? "::1",
            Entity = Modules.InfraObject.ToString(),
            Action = $"{ActivityType.Update} {Modules.InfraObject}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $" InfraObject '{updatedEvent.InfraObjectName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"InfraObject '{updatedEvent.InfraObjectName}' updated successfully.");
    }
}