using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetPaginatedList;

public class GetAdPasswordJobPaginatedListQueryHandler : IRequestHandler<GetAdPasswordJobPaginatedListQuery, PaginatedResult<AdPasswordJobListVm>>
{
    private readonly IAdPasswordJobRepository _adPasswordJobRepository;
    private readonly IMapper _mapper;

    public GetAdPasswordJobPaginatedListQueryHandler(IMapper mapper, IAdPasswordJobRepository adPasswordJobRepository)
    {
        _mapper = mapper;
        _adPasswordJobRepository = adPasswordJobRepository;
    }

    public async Task<PaginatedResult<AdPasswordJobListVm>> Handle(GetAdPasswordJobPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _adPasswordJobRepository.GetPaginatedQuery();

        var productFilterSpec = new AdPasswordJobFilterSpecification(request.SearchString);

        var adPasswordJobList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<AdPasswordJobListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return adPasswordJobList;
    }
}
