using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.Contexts;

public interface IDashboardDbContext
{
    #region ContinuityPatrol
    public DbSet<DrReady> DrReadys { get; set; }
    public DbSet<ImpactActivity> ImpactActivities { get; set; }
    public DbSet<Rto> Rtos { get; set; }
    public DbSet<DataLag> DataLags { get; set; }
    public DbSet<DashboardViewLog> DashboardViewLogs { get; set; }
    public DbSet<DRReadyLog> DrReadyLog { get; set; }
    public DbSet<BusinessServiceHealthStatus> BusinessServiceHealthStatuses { get; set; }
    public DbSet<BusinessServiceHealthLog> BusinessServiceHealthLogs { get; set; }
   /// public DbSet<HeatMapLog> HeatMapLogs { get; set; }
   // public DbSet<HeatMapStatus> HeatMapStatus { get; set; }
    public DbSet<InfraObjectInfo> InfraObjectInfos { get; set; }
    public DbSet<InfraSummary> InfraSummaries { get; set; }
    public DbSet<ImpactAvailability> ImpactAvailabilities { get; set; }
    public DbSet<DRReadyStatus> DrReadyStatuses { get; set; }
    public DbSet<RiskMitigation> RiskMitigations { get; set; }
    public DbSet<BusinessServiceAvailability> BusinessServiceAvailabilities { get; set; }
    public DbSet<BusinessServiceEvaluation> BusinessServiceEvaluations { get; set; }
    public DbSet<DashboardView> DashboardViews { get; set; }
    public DbSet<InfraDashboardView> InfraDashboardViews { get; set; }

    public DbSet<OneViewEntitiesEventView> OneViewEntitiesEventViews { get; set; }  

    public DbSet<OneViewRiskMitigationFailedDrillView> OneViewRiskMitigationFailedDrills { get; set; }
    public DbSet<OneViewRiskMitigationCyberSecurityView> OneViewRiskMitigationCyberSecurityViews { get; set; }
    public DbSet<DatalagImpactAvailabilityView> DatalagImpactAvailabilityViews { get; set; }
    #endregion
}