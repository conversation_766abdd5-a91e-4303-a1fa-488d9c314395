﻿using ContinuityPatrol.Application.Features.AlertMaster.Events.Update;

namespace ContinuityPatrol.Application.Features.AlertMaster.Commands.Update;

public class UpdateAlertMasterCommandHandler : IRequestHandler<UpdateAlertMasterCommand, UpdateAlertMasterResponse>
{
    private readonly IAlertMasterRepository _alertMasterRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateAlertMasterCommandHandler(IAlertMasterRepository alertMasterRepository, IMapper mapper,
        IPublisher publisher)
    {
        _alertMasterRepository = alertMasterRepository;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<UpdateAlertMasterResponse> Handle(UpdateAlertMasterCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _alertMasterRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.AlertMaster), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateAlertMasterCommand), typeof(Domain.Entities.AlertMaster));

        await _alertMasterRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateAlertMasterResponse
        {
            Message = Message.Update("Manage Alert", eventToUpdate.AlertName),

            AlertMasterId = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new AlertMasterUpdatedEvent { AlertName=eventToUpdate.AlertName }, cancellationToken);
        return response;
    }
}