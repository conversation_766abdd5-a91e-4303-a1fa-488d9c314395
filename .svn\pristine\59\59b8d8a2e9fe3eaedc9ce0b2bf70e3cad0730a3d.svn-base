﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Server.Events.Delete;

public class ServerDeletedEventHandler : INotificationHandler<ServerDeletedEvent>
{
    private readonly ILogger<ServerDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ServerDeletedEventHandler(ILoggedInUserService userService, ILogger<ServerDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        
    }

    public async Task Handle(ServerDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Entity = Modules.Server.ToString(),
            Action = $"{ActivityType.Delete} {Modules.Server}",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Server '{deletedEvent.ServerName}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Server '{deletedEvent.ServerName}' deleted successfully.");
    }
}