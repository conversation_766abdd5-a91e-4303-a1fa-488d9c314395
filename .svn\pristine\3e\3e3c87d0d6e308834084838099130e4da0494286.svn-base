﻿namespace ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessFunctionListByBusinessServiceId;

public class GetBusinessFunctionListByBusinessServiceIdQueryHandler : IRequestHandler<
    GetBusinessFunctionListByBusinessServiceIdQuery, List<GetBusinessFunctionListByBusinessServiceIdVm>>
{
    private readonly IDrReadyStatusRepository _drReadyStatusRepository;
    private readonly IHeatMapStatusViewRepository _heatMapStatusViewRepository;
    private readonly IMapper _mapper;

    public GetBusinessFunctionListByBusinessServiceIdQueryHandler(IMapper mapper,
        IDrReadyStatusRepository drReadyStatusRepository,
        IHeatMapStatusViewRepository heatMapStatusViewRepository)
    {
        _mapper = mapper;
        _drReadyStatusRepository = drReadyStatusRepository;
        _heatMapStatusViewRepository = heatMapStatusViewRepository;
    }

    public async Task<List<GetBusinessFunctionListByBusinessServiceIdVm>> Handle(
        GetBusinessFunctionListByBusinessServiceIdQuery request, CancellationToken cancellationToken)
    {
        var drReadyStatus =
            await _drReadyStatusRepository.GetDrReadyStatusByBusinessServiceId(request.BusinessServiceId);

        var businessServices = drReadyStatus.DistinctBy(x => x.BusinessFunctionId).ToList();

        var businessServiceDto = _mapper.Map<List<GetBusinessFunctionListByBusinessServiceIdVm>>(businessServices);

       foreach(var businessFunction in businessServiceDto)
        {
            //var businessFunctions = _drReadyStatusRepository
            //    .GetDrReadyStatusListByBusinessFunctionId(businessFunction.BusinessFunctionId);

            var heatMapStatus =await _heatMapStatusViewRepository
                .GetHeatMapListByBusinessFunctionId(businessFunction.BusinessFunctionId);

            businessFunction.GetInfraObjectList = _mapper.Map<List<GetInfraObjectList>>(heatMapStatus);
        };

        return businessServiceDto;
    }
}