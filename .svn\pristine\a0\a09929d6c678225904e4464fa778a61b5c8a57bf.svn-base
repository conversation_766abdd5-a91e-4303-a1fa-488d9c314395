﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class ReportRepositoryMocks
{
    public static Mock<IReportRepository> CreateReportRepository(List<Report> reports)
    {
        var mockReportRepository = new Mock<IReportRepository>();

        mockReportRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(reports);

        mockReportRepository.Setup(repo => repo.AddAsync(It.IsAny<Report>())).ReturnsAsync(
            (Report report) =>
            {
                report.Id = new Fixture().Create<int>();

                report.ReferenceId = new Fixture().Create<Guid>().ToString();

                reports.Add(report);

                return report;
            });

        return mockReportRepository;
    }

    public static Mock<IReportRepository> UpdateReportRepository(List<Report> reports)
    {
        var mockReportRepository = new Mock<IReportRepository>();

        mockReportRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(reports);

        mockReportRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => reports.SingleOrDefault(x => x.ReferenceId == i));

        mockReportRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Report>())).ReturnsAsync((Report report) =>
        {
            var index = reports.FindIndex(item => item.Id == report.Id);

            reports[index] = report;

            return report;
        });

        return mockReportRepository;
    }

    public static Mock<IReportRepository> DeleteReportRepository(List<Report> reports)
    {
        var mockReportRepository = new Mock<IReportRepository>();

        mockReportRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(reports);

        mockReportRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => reports.SingleOrDefault(x => x.ReferenceId == i));

        mockReportRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Report>())).ReturnsAsync((Report report) =>
        {
            var index = reports.FindIndex(item => item.Id == report.Id);

            report.IsActive = false;

            reports[index] = report;

            return report;
        });

        return mockReportRepository;
    }

    public static Mock<IReportRepository> GetReportRepository(List<Report> reports)
    {
        var mockReportRepository = new Mock<IReportRepository>();

        mockReportRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(reports);

        mockReportRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => reports.SingleOrDefault(x => x.ReferenceId == i));

        return mockReportRepository;
    }

    public static Mock<IReportRepository> GetReportNamesRepository(List<Report> reports)
    {
        var mockReportRepository = new Mock<IReportRepository>();

        mockReportRepository.Setup(repo => repo.GetReportNames()).ReturnsAsync(reports);

        return mockReportRepository;
    }

    public static Mock<IReportRepository> GetReportNameUniqueRepository(List<Report> reports)
    {
        var mockReportRepository = new Mock<IReportRepository>();

        mockReportRepository.Setup(repo => repo.IsReportNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => reports.Exists(x => x.Name == i && x.ReferenceId == j));

        return mockReportRepository;
    }

    public static Mock<IReportRepository> GetReportEmptyRepository()
    {
        var mockReportRepository = new Mock<IReportRepository>();

        mockReportRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Report>());

        return mockReportRepository;
    }

    public static Mock<IReportRepository> GetPaginatedReportRepository(List<Report> reports)
    {
        var mockReportRepository = new Mock<IReportRepository>();

        var queryableReport = reports.BuildMock();

        mockReportRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableReport);

        return mockReportRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateReportEventRepository(List<UserActivity> userActivities)
    {
        var reportEventRepository = new Mock<IUserActivityRepository>();

        reportEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return reportEventRepository;
    }
}