﻿namespace ContinuityPatrol.Application.Features.UserGroup.Queries.GetAssignedUserGroups;

public class UsersWithUserGroup
{
    public bool IsAll { get; set; } = false;
    public List<AssignedUsers> AssignedUsersWithGroup { get; set; }
}

public class AssignedUsers
{
    public string Id { get; set; }
    public string Name { get; set; }
    public bool IsAll { get; set; } = false;
    public bool IsPartial { get; set; } = false;
    public string Properties { get; set; }
}