﻿using ContinuityPatrol.Application.Features.Site.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Site.Commands;

public class CreateSiteTests : IClassFixture<SiteFixture>
{
    private readonly SiteFixture _siteFixture;

    private readonly Mock<ISiteRepository> _mockSiteRepository;

    private readonly CreateSiteCommandHandler _handler;

    public CreateSiteTests(SiteFixture siteFixture)
    {
        _siteFixture = siteFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockSiteRepository = SiteRepositoryMocks.CreateSiteRepository(_siteFixture.Sites);

        _handler = new CreateSiteCommandHandler(_siteFixture.Mapper, _mockSiteRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Should_Increase_Count_When_AddValid_Site()
    {
        await _handler.Handle(_siteFixture.CreateSiteCommand, CancellationToken.None);

        var allCategories = await _mockSiteRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_siteFixture.Sites.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulSiteResponse_When_AddValidSite()
    {
        var result = await _handler.Handle(_siteFixture.CreateSiteCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateSiteResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_siteFixture.CreateSiteCommand, CancellationToken.None);

        _mockSiteRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.Site>()), Times.Once);
    }
}