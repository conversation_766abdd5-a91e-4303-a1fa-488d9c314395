using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class TemplateHistoryFixture : IDisposable
{
    public List<TemplateHistory> TemplateHistoryPaginationList { get; set; }
    public List<TemplateHistory> TemplateHistoryList { get; set; }
    public TemplateHistory TemplateHistoryDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public TemplateHistoryFixture()
    {
        var fixture = new Fixture();

        TemplateHistoryList = fixture.Create<List<TemplateHistory>>();

        TemplateHistoryPaginationList = fixture.CreateMany<TemplateHistory>(20).ToList();

        TemplateHistoryPaginationList.ForEach(x => x.CompanyId = CompanyId);

        TemplateHistoryList.ForEach(x => x.CompanyId = CompanyId);

        TemplateHistoryDto = fixture.Create<TemplateHistory>();

        TemplateHistoryDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
