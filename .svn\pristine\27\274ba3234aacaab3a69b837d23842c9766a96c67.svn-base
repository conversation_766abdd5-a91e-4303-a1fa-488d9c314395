﻿namespace ContinuityPatrol.Application.Features.Incident.Commands.Create;

public class CreateIncidentCommandValidator : AbstractValidator<CreateIncidentCommand>
{
    private readonly IIncidentRepository _incidentRepository;

    public CreateIncidentCommandValidator(IIncidentRepository incidentRepository)
    {
        _incidentRepository = incidentRepository;

        RuleFor(p => p.IncidentName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 30).WithMessage("{PropertyName} should contain between 3 to 30 characters.");

        RuleFor(e => e)
            .MustAsync(IncidentNameUnique)
            .WithMessage("A same name already exists.");

        RuleFor(p => p.BusinessFunctionName)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.");

        RuleFor(p => p.BusinessServiceName)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.");
    }

    public async Task<bool> IncidentNameUnique(CreateIncidentCommand e, CancellationToken token)
    {
        return !await _incidentRepository.IsIncidentNameUnique(e.IncidentName);
    }
}