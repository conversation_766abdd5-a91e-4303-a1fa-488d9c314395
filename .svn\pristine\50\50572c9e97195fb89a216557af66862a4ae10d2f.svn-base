﻿using AutoFixture;
using ContinuityPatrol.Application.Features.FiaTemplate.Commands.Create;
using ContinuityPatrol.Application.Features.FiaTemplate.Commands.Update;
using ContinuityPatrol.Application.Features.FiaTemplate.Events.PaginatedView;
using ContinuityPatrol.Application.Features.FiaTemplate.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FiaImpactCategoryModel;
using ContinuityPatrol.Domain.ViewModels.FiaImpactTypeModel;
using ContinuityPatrol.Domain.ViewModels.FiaIntervalModel;
using ContinuityPatrol.Domain.ViewModels.FiaTemplateModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using FluentValidation.Results;

using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller;

public class FIATemplatesControllerShould
{
    private readonly Mock<IPublisher> _publisherMock = new();
    private readonly Mock<IDataProvider> _dataProviderMock = new();
    private readonly Mock<IMapper> _mapperMock = new();
    private readonly Mock<ILogger<FiaTemplatesController>> _loggerMock = new();
    private FiaTemplatesController _controller = null!;

    public FIATemplatesControllerShould()
    {
        Initialize();
    }

    internal void Initialize()
    {
        _controller = new FiaTemplatesController(
            _publisherMock.Object,
            _loggerMock.Object,
            _dataProviderMock.Object,
            _mapperMock.Object
        );
        _controller.ControllerContext.HttpContext = new DefaultHttpContext();
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
    }

    // ===== LIST METHOD TESTS =====

    [Fact]
    public async Task List_ShouldReturnViewResult()
    {
        // Act
        var result = await _controller.List() as ViewResult;

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task List_ShouldPublishFiaTemplatePaginatedEvent()
    {
        // Act
        await _controller.List();

        // Assert
        _publisherMock.Verify(p => p.Publish(It.IsAny<FiaTemplatePaginatedevent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    // ===== GETPAGINATEDFIATEMPLATESLIST METHOD TESTS =====

    [Fact]
    public async Task GetPaginatedFiaTemplatesList_ShouldReturnJsonResult_WithSuccess()
    {
        // Arrange
        var query = new GetFiaTemplatePaginatedListQuery();
        var timeList = new PaginatedResult<FiaTemplateListVm>();
        _dataProviderMock.Setup(x => x.FiaTemplate.GetPaginatedFiaTemplates(query))
            .ReturnsAsync(timeList);

        // Act
        var result = await _controller.GetPaginatedFiaTemplatesList(query);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.NotNull(result);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetPaginatedFiaTemplatesList_ShouldReturnJsonResult_WithException()
    {
        // Arrange
        var query = new GetFiaTemplatePaginatedListQuery();
        _dataProviderMock.Setup(x => x.FiaTemplate.GetPaginatedFiaTemplates(query))
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.GetPaginatedFiaTemplatesList(query);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetPaginatedFiaTemplatesList_ShouldCallDataProviderWithCorrectQuery()
    {
        // Arrange
        var query = new GetFiaTemplatePaginatedListQuery();
        var timeList = new PaginatedResult<FiaTemplateListVm>();
        _dataProviderMock.Setup(x => x.FiaTemplate.GetPaginatedFiaTemplates(query))
            .ReturnsAsync(timeList);

        // Act
        await _controller.GetPaginatedFiaTemplatesList(query);

        // Assert
        _dataProviderMock.Verify(x => x.FiaTemplate.GetPaginatedFiaTemplates(query), Times.Once);
    }

    // ===== GETTIMEINTERVALMASTERLIST METHOD TESTS =====

    [Fact]
    public async Task GetTimeIntervalMasterList_ShouldReturnJsonResult_WithSuccess()
    {
        // Arrange
        var timeIntervalList = new List<FiaIntervalListVm> { new() { Id = "1", MinTime = 1, MaxTime = 10, MinTimeUnit = 1, MaxTimeUnit = 2 } };
        _dataProviderMock.Setup(x => x.FiaInterval.GetFiaIntervalList())
            .ReturnsAsync(timeIntervalList);

        // Act
        var result = await _controller.GetTimeIntervalMasterList();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.NotNull(result);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetTimeIntervalMasterList_ShouldReturnJsonResult_WithException()
    {
        // Arrange
        _dataProviderMock.Setup(x => x.FiaInterval.GetFiaIntervalList())
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.GetTimeIntervalMasterList();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetTimeIntervalMasterList_ShouldCallDataProvider()
    {
        // Arrange
        var timeIntervalList = new List<FiaIntervalListVm>();
        _dataProviderMock.Setup(x => x.FiaInterval.GetFiaIntervalList())
            .ReturnsAsync(timeIntervalList);

        // Act
        await _controller.GetTimeIntervalMasterList();

        // Assert
        _dataProviderMock.Verify(x => x.FiaInterval.GetFiaIntervalList(), Times.Once);
    }

    // ===== GETIMPACTMASTERLIST METHOD TESTS =====

    [Fact]
    public async Task GetImpactMasterList_ShouldReturnJsonResult_WithSuccess()
    {
        // Arrange
        var impactList = new List<FiaImpactCategoryListVm> { new() { Id = "1", Name = "Test" } };
        _dataProviderMock.Setup(x => x.FiaImpactCategory.GetFiaImpactCategoryList())
            .ReturnsAsync(impactList);

        // Act
        var result = await _controller.GetImpactMasterList();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.NotNull(result);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetImpactMasterList_ShouldReturnJsonResult_WithException()
    {
        // Arrange
        _dataProviderMock.Setup(x => x.FiaImpactCategory.GetFiaImpactCategoryList())
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.GetImpactMasterList();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetImpactMasterList_ShouldCallDataProvider()
    {
        // Arrange
        var impactList = new List<FiaImpactCategoryListVm>();
        _dataProviderMock.Setup(x => x.FiaImpactCategory.GetFiaImpactCategoryList())
            .ReturnsAsync(impactList);

        // Act
        await _controller.GetImpactMasterList();

        // Assert
        _dataProviderMock.Verify(x => x.FiaImpactCategory.GetFiaImpactCategoryList(), Times.Once);
    }

    // ===== GETIMPACTTYPEMASTERLIST METHOD TESTS =====

    [Fact]
    public async Task GetImpactTypeMasterList_ShouldReturnJsonResult_WithSuccess()
    {
        // Arrange
        var impactTypeList = new List<FiaImpactTypeListVm> { new() { Id = "1", Name = "Test" } };
        _dataProviderMock.Setup(x => x.FiaImpactType.GetFiaImpactTypeList())
            .ReturnsAsync(impactTypeList);

        // Act
        var result = await _controller.GetImpactTypeMasterList();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.NotNull(result);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetImpactTypeMasterList_ShouldReturnJsonResult_WithException()
    {
        // Arrange
        _dataProviderMock.Setup(x => x.FiaImpactType.GetFiaImpactTypeList())
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.GetImpactTypeMasterList();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetImpactTypeMasterList_ShouldCallDataProvider()
    {
        // Arrange
        var impactTypeList = new List<FiaImpactTypeListVm>();
        _dataProviderMock.Setup(x => x.FiaImpactType.GetFiaImpactTypeList())
            .ReturnsAsync(impactTypeList);

        // Act
        await _controller.GetImpactTypeMasterList();

        // Assert
        _dataProviderMock.Verify(x => x.FiaImpactType.GetFiaImpactTypeList(), Times.Once);
    }

    // ===== GETLIST METHOD TESTS =====

    [Fact]
    public async Task GetList_ShouldReturnJsonResult_WithSuccess()
    {
        // Arrange
        var timeIntervalList = new List<FiaIntervalListVm> { new() { Id = "1", MinTime = 1, MaxTime = 10, MinTimeUnit = 1, MaxTimeUnit = 2 } };
        var impactList = new List<FiaImpactCategoryListVm> { new() { Id = "1", Name = "Impact" } };
        var impactTypeList = new List<FiaImpactTypeListVm> { new() { Id = "1", Name = "ImpactType" } };

        _dataProviderMock.Setup(x => x.FiaInterval.GetFiaIntervalList()).ReturnsAsync(timeIntervalList);
        _dataProviderMock.Setup(x => x.FiaImpactCategory.GetFiaImpactCategoryList()).ReturnsAsync(impactList);
        _dataProviderMock.Setup(x => x.FiaImpactType.GetFiaImpactTypeList()).ReturnsAsync(impactTypeList);

        // Act
        var result = await _controller.GetList();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.NotNull(result);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetList_ShouldReturnJsonResult_WithException()
    {
        // Arrange
        _dataProviderMock.Setup(x => x.FiaInterval.GetFiaIntervalList())
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.GetList();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetList_ShouldCallAllDataProviders()
    {
        // Arrange
        var timeIntervalList = new List<FiaIntervalListVm>();
        var impactList = new List<FiaImpactCategoryListVm>();
        var impactTypeList = new List<FiaImpactTypeListVm>();

        _dataProviderMock.Setup(x => x.FiaInterval.GetFiaIntervalList()).ReturnsAsync(timeIntervalList);
        _dataProviderMock.Setup(x => x.FiaImpactCategory.GetFiaImpactCategoryList()).ReturnsAsync(impactList);
        _dataProviderMock.Setup(x => x.FiaImpactType.GetFiaImpactTypeList()).ReturnsAsync(impactTypeList);

        // Act
        await _controller.GetList();

        // Assert
        _dataProviderMock.Verify(x => x.FiaInterval.GetFiaIntervalList(), Times.Once);
        _dataProviderMock.Verify(x => x.FiaImpactCategory.GetFiaImpactCategoryList(), Times.Once);
        _dataProviderMock.Verify(x => x.FiaImpactType.GetFiaImpactTypeList(), Times.Once);
    }

    // ===== FIATEMPLATECREATEORUPDATE METHOD TESTS =====

    [Fact]
    public async Task FiaTemplateCreateOrUpdate_WithNewTemplate_ShouldReturnJsonResult_WithSuccess()
    {
        // Arrange
        var model = new Fixture().Create<FiaTemplateViewModel>();
        var dic = new Dictionary<string, StringValues>();
        dic.Add("id", "");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;

        var command = new CreateFiaTemplateCommand();
        var response = new BaseResponse { Success = true, Message = "Created successfully" };

        _mapperMock.Setup(m => m.Map<CreateFiaTemplateCommand>(model)).Returns(command);
        _dataProviderMock.Setup(x => x.FiaTemplate.CreateAsync(command)).ReturnsAsync(response);

        // Act
        var result = await _controller.FiaTemplateCreateOrUpdate(model);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.NotNull(result);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task FiaTemplateCreateOrUpdate_WithExistingTemplate_ShouldReturnJsonResult_WithSuccess()
    {
        // Arrange
        var model = new Fixture().Create<FiaTemplateViewModel>();
        var dic = new Dictionary<string, StringValues>();
        dic.Add("id", "existing-id");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;

        var command = new UpdateFiaTemplateCommand();
        var response = new BaseResponse { Success = true, Message = "Updated successfully" };

        _mapperMock.Setup(m => m.Map<UpdateFiaTemplateCommand>(model)).Returns(command);
        _dataProviderMock.Setup(x => x.FiaTemplate.UpdateAsync(command)).ReturnsAsync(response);

        // Act
        var result = await _controller.FiaTemplateCreateOrUpdate(model);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.NotNull(result);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task FiaTemplateCreateOrUpdate_WithValidationException_ShouldRedirectToList()
    {
        // Arrange
        var model = new Fixture().Create<FiaTemplateViewModel>();
        var dic = new Dictionary<string, StringValues>();
        dic.Add("id", "");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;

        var validationResult = new ValidationResult();
        validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Property", "Validation error"));
        var validationException = new ValidationException(validationResult);
        _mapperMock.Setup(m => m.Map<CreateFiaTemplateCommand>(model)).Throws(validationException);

        // Act
        var result = await _controller.FiaTemplateCreateOrUpdate(model);

        // Assert
        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
    }

    [Fact]
    public async Task FiaTemplateCreateOrUpdate_WithException_ShouldReturnJsonException()
    {
        // Arrange
        var model = new Fixture().Create<FiaTemplateViewModel>();
        var dic = new Dictionary<string, StringValues>();
        dic.Add("id", "");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;

        var exception = new Exception("Database error");
        _mapperMock.Setup(m => m.Map<CreateFiaTemplateCommand>(model)).Throws(exception);

        // Act
        var result = await _controller.FiaTemplateCreateOrUpdate(model);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(result);
    }

    // ===== CONSTRUCTOR TESTS =====

    [Fact]
    public void Constructor_ShouldInitializeAllDependencies()
    {
        // Arrange & Act
        var controller = new FiaTemplatesController(
            _publisherMock.Object,
            _loggerMock.Object,
            _dataProviderMock.Object,
            _mapperMock.Object
        );

        // Assert
        Assert.NotNull(controller);
    }

    [Fact]
    public void Constructor_ShouldAcceptNullPublisher_WithoutThrowingException()
    {
        // Act & Assert - Production code doesn't validate null, so it should not throw
        var exception = Record.Exception(() => new FiaTemplatesController(
            null!,
            _loggerMock.Object,
            _dataProviderMock.Object,
            _mapperMock.Object
        ));
        Assert.Null(exception);
    }

    [Fact]
    public void Constructor_ShouldAcceptNullLogger_WithoutThrowingException()
    {
        // Act & Assert - Production code doesn't validate null, so it should not throw
        var exception = Record.Exception(() => new FiaTemplatesController(
            _publisherMock.Object,
            null!,
            _dataProviderMock.Object,
            _mapperMock.Object
        ));
        Assert.Null(exception);
    }

    [Fact]
    public void Constructor_ShouldAcceptNullDataProvider_WithoutThrowingException()
    {
        // Act & Assert - Production code doesn't validate null, so it should not throw
        var exception = Record.Exception(() => new FiaTemplatesController(
            _publisherMock.Object,
            _loggerMock.Object,
            null!,
            _mapperMock.Object
        ));
        Assert.Null(exception);
    }

    [Fact]
    public void Constructor_ShouldAcceptNullMapper_WithoutThrowingException()
    {
        // Act & Assert - Production code doesn't validate null, so it should not throw
        var exception = Record.Exception(() => new FiaTemplatesController(
            _publisherMock.Object,
            _loggerMock.Object,
            _dataProviderMock.Object,
            null!
        ));
        Assert.Null(exception);
    }

    // ===== ISTEMPLATENAMEEXIST METHOD TESTS =====

    [Fact]
    public async Task IsTemplateNameExist_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var name = "test-name";
        var id = "test-id";
        _dataProviderMock.Setup(dp => dp.FiaTemplate.IsFiaTemplateNameExist(name, id)).ReturnsAsync(true);

        // Act
        var result = await _controller.IsTemplateNameExist(name, id);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTemplateNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var name = "test-name";
        var id = "test-id";
        _dataProviderMock.Setup(dp => dp.FiaTemplate.IsFiaTemplateNameExist(name, id)).ReturnsAsync(false);

        // Act
        var result = await _controller.IsTemplateNameExist(name, id);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTemplateNameExist_ShouldReturnFalseOnException()
    {
        // Arrange
        var name = "test-name";
        var id = "test-id";
        var exception = new Exception("Test exception");
        _dataProviderMock.Setup(dp => dp.FiaTemplate.IsFiaTemplateNameExist(name, id)).ThrowsAsync(exception);

        // Act
        var result = await _controller.IsTemplateNameExist(name, id);

        // Assert
        Assert.False(result);
    }

    // ===== FIATEMPLATEDELTE METHOD TESTS =====

    [Fact]
    public async Task FiaTemplateDelete_ShouldReturnJsonResult_WithSuccess()
    {
        // Arrange
        var id = "test-id";
        var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
        _dataProviderMock.Setup(x => x.FiaTemplate.DeleteAsync(id)).ReturnsAsync(response);

        // Act
        var result = await _controller.FiaTemplateDelete(id);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.NotNull(result);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task FiaTemplateDelete_ShouldReturnJsonResult_WithException()
    {
        // Arrange
        var id = "test-id";
        var exception = new Exception("Database error");
        _dataProviderMock.Setup(x => x.FiaTemplate.DeleteAsync(id)).ThrowsAsync(exception);

        // Act
        var result = await _controller.FiaTemplateDelete(id);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task FiaTemplateDelete_ShouldCallDataProviderWithCorrectId()
    {
        // Arrange
        var id = "test-id";
        var response = new BaseResponse { Success = true };
        _dataProviderMock.Setup(x => x.FiaTemplate.DeleteAsync(id)).ReturnsAsync(response);

        // Act
        await _controller.FiaTemplateDelete(id);

        // Assert
        _dataProviderMock.Verify(x => x.FiaTemplate.DeleteAsync(id), Times.Once);
    }

    // ===== TIMEINTERVALMASTERDELETE METHOD TESTS =====

    [Fact]
    public async Task TimeIntervalMasterDelete_ShouldReturnJsonResult_WithSuccess()
    {
        // Arrange
        var id = "test-id";
        var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
        _dataProviderMock.Setup(x => x.FiaInterval.DeleteAsync(id)).ReturnsAsync(response);

        // Act
        var result = await _controller.TimeIntervalMasterDelete(id);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.NotNull(result);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task TimeIntervalMasterDelete_ShouldReturnJsonResult_WithException()
    {
        // Arrange
        var id = "test-id";
        var exception = new Exception("Database error");
        _dataProviderMock.Setup(x => x.FiaInterval.DeleteAsync(id)).ThrowsAsync(exception);

        // Act
        var result = await _controller.TimeIntervalMasterDelete(id);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(result);
    }
}