<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Site Type QUnit Tests</title>
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.20.1.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://code.jquery.com/qunit/qunit-2.19.4.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sinon.js/15.2.0/sinon.min.js"></script>
    <script src="/js/Common/common.js"></script>
    <script src="/js/Configuration/Sites/Site/site.js"></script>
    <script src="/js/Configuration/Sites/Site/siteTest.js"></script>
    <style>
        #qunit-fixture {
            display: none;
        }
    </style>
</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture">
        <div id="siteConfigCreate" data-create-permission="true"></div>
        <div id="siteConfigDelete" data-delete-permission="true"></div>

        <div id="qunit-fixture">
            <button id="createSiteBtn"></button>
            <button id="siteSaveButton"></button>
            <input type="text" id="siteName">
            <select id="selectLocation"><option value="City" lat="0" lng="0" id="1">City</option></select>
            <select id="platformSelect"><option value="Physical">Physical</option></select>
            <select id="companyNameDropdown"><option id="1" value="Company">Company</option></select>
            <div id="siteTypeCustom">
                <input type="radio" class="siteTypeRadio" name="type" value="prsite" data-typeName="1">
            </div>
            <select id="drSiteTypeSelect"><option value="Hot">Hot</option></select>
            <div id="siteDeleteId"></div>
            <div id="siteCreateModal"></div>
            <div id="siteDeleteModal"></div>
            <span id="siteNameError"></span>
            <span id="sitePlatformError"></span>
            <span id="siteLocError"></span>
            <span id="siteCompError"></span>
            <span id="sitetypeError"></span>
            <span id="drSiteTypeError"></span>
            <table id="siteTable"></table>
        </div>
    </div>
</body>
</html>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>