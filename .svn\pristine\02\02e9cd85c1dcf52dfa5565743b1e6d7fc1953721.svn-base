﻿using ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetWorkflowActionResultByOperationGroupId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionResult.Queries;

public class GetWorkflowActionResultByOperationGroupIdQueryHandlerTests : IClassFixture<WorkflowActionResultFixture>
{
    private readonly WorkflowActionResultFixture _workflowActionResultFixture;

    private Mock<IWorkflowActionResultRepository> _mockWorkflowActionResultRepository;

    private readonly GetWorkflowActionResultByOperationGroupIdQueryHandler _handler;

    public GetWorkflowActionResultByOperationGroupIdQueryHandlerTests(WorkflowActionResultFixture workflowActionResultFixture)
    {
        _workflowActionResultFixture = workflowActionResultFixture;

        _mockWorkflowActionResultRepository = WorkflowActionResultRepositoryMocks.GetWorkflowActionResultFromOperationGroupIdRepository(_workflowActionResultFixture.WorkflowActionResults);

        _handler = new GetWorkflowActionResultByOperationGroupIdQueryHandler(_workflowActionResultFixture.Mapper, _mockWorkflowActionResultRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_WorkflowActionResult_When_ValidOperationGroupId()
    {
        var result = await _handler.Handle(new GetWorkflowActionResultByOperationGroupIdQuery { WorkflowOperationGroupId = _workflowActionResultFixture.WorkflowActionResults[0].WorkflowOperationGroupId }, CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowActionResultByOperationGroupIdVm>>();
        result.Count.ShouldBe(1);
        result[0].Id.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].ReferenceId);
        result[0].WorkflowActionName.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].WorkflowActionName);
        result[0].StartTime.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].StartTime);
        result[0].EndTime.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].EndTime);
        result[0].Status.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].Status);
        result[0].Message.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].Message);
        result[0].WorkflowOperationId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].WorkflowOperationId);
        result[0].WorkflowOperationGroupId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].WorkflowOperationGroupId);
        result[0].InfraObjectId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].InfraObjectId);
        result[0].ActionId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].ActionId);
        result[0].ConditionActionId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].ConditionActionId);
        result[0].SkipStep.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].SkipStep);
        result[0].IsReload.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].IsReload);
        result[0].Direction.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].Direction);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockWorkflowActionResultRepository = WorkflowActionResultRepositoryMocks.GetWorkflowActionResultEmptyRepository();

        var handler = new GetWorkflowActionResultByOperationGroupIdQueryHandler(_workflowActionResultFixture.Mapper, _mockWorkflowActionResultRepository.Object);

        var result = await handler.Handle(new GetWorkflowActionResultByOperationGroupIdQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowActionResultByOperationResultIdMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowActionResultByOperationGroupIdQuery { WorkflowOperationGroupId = _workflowActionResultFixture.WorkflowActionResultByOperationGroupIdVms[0].Id }, CancellationToken.None);

        _mockWorkflowActionResultRepository.Verify(x => x.GetWorkflowActionResultByWorkflowOperationGroupId(It.IsAny<string>()), Times.Once);
    }
}