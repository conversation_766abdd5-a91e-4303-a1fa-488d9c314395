﻿using ContinuityPatrol.Application.Features.Server.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Domain.Views;


namespace ContinuityPatrol.Application.UnitTests.Features.Server.Queries;

public class GetServerPaginatedListQueryHandlerTests : IClassFixture<ServerFixture>, IClassFixture<InfraObjectFixture>
{
    private readonly ServerFixture _serverFixture;
    private readonly InfraObjectFixture _infraObjectFixture;
    private readonly Mock<IServerRepository> _mockServerRepository;
    private readonly Mock<IServerViewRepository> _mockServerViewRepository=new();
    private readonly GetServerPaginatedListQueryHandler _handler;
    private Mock<ISpecification<Domain.Entities.Server>> _mockFilterSpecification=new();
    private readonly Mock<IMapper> _mockMapper=new();
    
    public GetServerPaginatedListQueryHandlerTests(ServerFixture serverFixture, InfraObjectFixture infraObjectFixture)
    {
        _serverFixture = serverFixture;

        _infraObjectFixture = infraObjectFixture;

        var mockILoggedInUserService = new Mock<ILoggedInUserService>();

        _serverFixture.Servers[0].Name = "Test123";
        _serverFixture.Servers[0].ServerType = "Server_DR";
        _serverFixture.Servers[0].RoleType = "Application";
        _serverFixture.Servers[0].OSType = "Windows";
        _serverFixture.Servers[0].SiteName = "Site";
        _serverFixture.Servers[0].Properties = "Sample_Properties";
        _serverFixture.Servers[0].Status = "Pending";

        _serverFixture.Servers[1].Name = "TestDR";
        _serverFixture.Servers[1].ServerType = "Server_PR";
        _serverFixture.Servers[1].RoleType = "DataBase";
        _serverFixture.Servers[1].OSType = "Linux";
        _serverFixture.Servers[1].SiteName = "TestSite";
        _serverFixture.Servers[1].Properties = "Sample1_Properties";
        _serverFixture.Servers[1].Status = "Updated";

        
        
        _mockServerRepository = ServerRepositoryMocks.GetPaginatedServerRepository(_serverFixture.Servers);
        
        var mockInfraObjectRepository = InfraObjectRepositoryMocks.GetPaginatedInfraObjectRepository(_infraObjectFixture.InfraObjects);

        _handler = new GetServerPaginatedListQueryHandler(_serverFixture.Mapper, _mockServerViewRepository.Object);

        _serverFixture.Servers[0].LicenseKey = "7OiVZ+aFxmcVHBrweWh3ZTx2e15l0jdG6ulLqnKi9uI=$XMfsb/bBKGBHBlIJN3ADJt8vTJr7cQAxLro7hyxPCBA=";
        _serverFixture.Servers[1].LicenseKey = "7OiVZ+aFxmcVHBrweWh3ZTx2e15l0jdG6ulLqnKi9uI=$XMfsb/bBKGBHBlIJN3ADJt8vTJr7cQAxLro7hyxPCBA=";
        
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var query = new Fixture().Create<GetServerPaginatedListQuery>();
        var servers = new Fixture().Create<List<ServerView>>();
        var queryableServer = servers.BuildMock();
        var request = new GetServerPaginatedListQuery
        {
            SearchString = "",
            PageNumber = 1,
            PageSize = 10,
            OSTypeId = servers[0].OSTypeId
        };

        //_mockServerViewRepository.Setup(repo => repo.GetServerByOsType(servers[0].OSTypeId)).Returns(queryableServer);
        _mockServerViewRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableServer);
        var result = await _handler.Handle(request, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ServerViewListVm>>();

        result.TotalCount.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_PaginatedServers_When_QueryStringMatch_WithType()
    {
       // _serverFixture.Servers[0].ReferenceId = _infraObjectFixture.InfraObjects[0].DRServerId = "7c89d072-24fa-4550-8493-08b088b310ba";

        var query = new Fixture().Create<GetServerPaginatedListQuery>();
        var servers = new Fixture().Create<List<ServerView>>();
        var queryableServer = servers.BuildMock();
        var request = new GetServerPaginatedListQuery
        {
            SearchString = "",
            PageNumber = 1,
            PageSize = 10,
            OSTypeId = servers[0].OSTypeId
        };

       // _mockServerViewRepository.Setup(repo => repo.GetServerByOsType(servers[0].OSTypeId)).Returns(queryableServer);
        _mockServerViewRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableServer);
        var result = await _handler.Handle(request, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ServerViewListVm>>();

        result.TotalCount.ShouldBe(3);

        result.Data[0].ShouldBeOfType<ServerViewListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].ServerType.ShouldBe(servers[0].ServerType);

        result.Data[0].Name.ShouldBe(servers[0].Name);
    }

    [Fact]
    public async Task Handle_Return_PaginatedServers_When_QueryStringMatch()
    {
        var query = new Fixture().Create<GetServerPaginatedListQuery>();
        var servers = new Fixture().Create<List<ServerView>>();

       // servers[0].ReferenceId = _infraObjectFixture.InfraObjects[1].DRServerId = "7c89d072-24fa-4550-8493-08b088b310ba";
        var queryableServer = servers.BuildMock();
       

        var request = new GetServerPaginatedListQuery
        {
            SearchString = "",
            PageNumber = 1,
            PageSize = 10,
            OSTypeId = servers[0].OSTypeId,
            
        };

        //_mockServerViewRepository.Setup(repo => repo.GetServerByOsType(servers[0].OSTypeId)).Returns(queryableServer);
        _mockServerViewRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableServer);


        var result = await _handler.Handle(request, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ServerViewListVm>>();

        result.TotalCount.ShouldBe(3);

        result.Data[0].ShouldBeOfType<ServerViewListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].ServerType.ShouldBe(servers[0].ServerType);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
       // _serverFixture.Servers[1].ReferenceId = _infraObjectFixture.InfraObjects[1].DRServerId = "7c89d072-24fa-4550-8493-08b088b310ba";
        var query = new Fixture().Create<GetServerPaginatedListQuery>();
        var servers = new Fixture().Create<List<ServerView>>();
        var queryableServer = servers.BuildMock();
        var request = new GetServerPaginatedListQuery
        {
            SearchString = "",
            PageNumber = 1,
            PageSize = 10,
            OSTypeId = servers[0].OSTypeId
        };

        //_mockServerViewRepository.Setup(repo => repo.GetServerByOsType(servers[0].OSTypeId)).Returns(queryableServer);
        _mockServerViewRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableServer);
        var result = await _handler.Handle(request, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ServerViewListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_Servers_With_MultipleQueryStringParameter()
    {
      //  _serverFixture.Servers[1].ReferenceId = _infraObjectFixture.InfraObjects[1].DRServerId = "7c89d072-24fa-4550-8493-08b088b310ba";
        var query = new Fixture().Create<GetServerPaginatedListQuery>();
        var servers = new Fixture().Create<List<ServerView>>();
        var queryableServer = servers.BuildMock();
        var request = new GetServerPaginatedListQuery
        {
            SearchString = "",
            PageNumber = 1,
            PageSize = 10,
            OSTypeId = servers[0].OSTypeId
        };

       // _mockServerViewRepository.Setup(repo => repo.GetServerByOsType(servers[0].OSTypeId)).Returns(queryableServer);
        _mockServerViewRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableServer);
        var result = await _handler.Handle(request, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ServerViewListVm>>();

        result.TotalCount.ShouldBe(3);

        result.Data[0].Name.ShouldBe(servers[0].Name);

        result.Data[0].ServerType.ShouldBe(servers[0].ServerType);

        result.Data[0].OSType.ShouldBe(servers[0].OSType);

        result.Data[0].SiteName.ShouldBe(servers[0].SiteName);

        result.Data[0].Properties.ShouldBe(servers[0].Properties);

        result.Data[0].Status.ShouldBe(servers[0].Status);

        result.Data[0].SiteId.ShouldBe(servers[0].SiteId);

        result.Data[0].LicenseKey.ShouldBe(servers[0].LicenseKey);
    }
    
    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        var query = new Fixture().Create<GetServerPaginatedListQuery>();
        var servers = new Fixture().Create<List<ServerView>>();
        var queryableServer = servers.BuildMock();
        var request = new GetServerPaginatedListQuery
        {
            SearchString = "name=Test",
            PageNumber = 1,
            PageSize = 10,
            OSTypeId= servers[0].OSTypeId
        };
        
       // _mockServerViewRepository.Setup(repo => repo.GetServerByOsType(servers[0].OSTypeId)).Returns(queryableServer);
        _mockServerViewRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableServer);
        var result = await _handler.Handle(request, CancellationToken.None);
        Assert.NotNull(result);
        _mockServerViewRepository.Verify(x => x.GetServerByOsType(servers[0].OSTypeId), Times.Once());
    }
}
