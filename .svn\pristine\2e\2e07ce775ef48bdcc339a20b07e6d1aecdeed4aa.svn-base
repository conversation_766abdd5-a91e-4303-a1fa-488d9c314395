﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.Company.Commands.Create;
using ContinuityPatrol.Application.Features.Company.Commands.CreateDefaultCompany;
using ContinuityPatrol.Application.Features.Company.Commands.Delete;
using ContinuityPatrol.Application.Features.Company.Commands.Update;
using ContinuityPatrol.Application.Features.Company.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Company.Queries.GetDisplayNameUnique;
using ContinuityPatrol.Application.Features.Company.Queries.GetList;
using ContinuityPatrol.Application.Features.Company.Queries.GetNames;
using ContinuityPatrol.Application.Features.Company.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Company.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class CompaniesController : CommonBaseController
{
    private readonly ICompanyRepository _companyRepository;
    public CompaniesController(ICompanyRepository companyRepository)
    {
        _companyRepository = companyRepository;
    }

    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<CompanyListVm>>> GetCompanies()
    {
        Logger.LogDebug("Get All Companies");

        return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllCompaniesCacheKey + LoggedInUserService.CompanyId, () => Mediator.Send(new GetCompanyListQuery()), CacheExpiry));
    }

    [Authorize(Policy = Permissions.Configuration.View)]
    [HttpGet("{id}", Name = "GetCompany")]
    public async Task<ActionResult<CompanyDetailVm>> GetCompanyById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Company Id");

        Logger.LogDebug($"Get Company Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetCompanyDetailQuery { Id = id }));
    }

    [AllowAnonymous]
    [Route("names"), HttpGet]
    public async Task<ActionResult<List<CompanyNameVm>>> GetCompanyNames()
    {
        Logger.LogDebug("Get All Company Names");

        ClearDataCache();

        return Ok(await Mediator.Send(new GetCompanyNameQuery()));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateCompanyResponse>> CreateCompany([FromBody] CreateCompanyCommand createCompanyCommand)
    {
        Logger.LogDebug($"Creating Company '{createCompanyCommand.Name}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateCompany), await Mediator.Send(createCompanyCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateCompanyResponse>> UpdateCompany([FromBody] UpdateCompanyCommand updateCompanyCommand)
    {
        Logger.LogDebug($"Updating Company '{updateCompanyCommand.Name}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateCompanyCommand));
    }

    [HttpDelete("{id}")]

    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteCompanyResponse>> DeleteCompany(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Company Id");

        Logger.LogDebug($"Deleting Company Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteCompanyCommand { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<CompanyListVm>>> GetPaginatedCompanies([FromQuery] GetCompanyPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Company Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsCompanyNameExist(string companyName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(companyName, "Company Name");

        Logger.LogDebug($"Check Name Exists Detail by Company Name '{companyName}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetCompanyNameUniqueQuery { CompanyName = companyName, CompanyId = id }));
    }

    [Route("displayname-exist"), HttpGet]
    public async Task<ActionResult> IsDisplayNameExist(string displayName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(displayName, "Display Name");

        Logger.LogDebug($"Check Name Exists Detail by Display Name '{displayName}'and Id '{id}'");

        return Ok(await Mediator.Send(new GetDisplayNameUniqueQuery { DisplayName = displayName, CompanyId = id }));
    }

    [HttpPost("default-company")]
    [AllowAnonymous]
    public async Task<ActionResult<CreateCompanyResponse>> CreateDefaultCompany([FromBody] CreateDefaultCompanyCommand createDefaultCompanyCommand)
    {
        var companies = await _companyRepository.GetAllCompanyNames();

        if (companies.Count > 0) throw new InvalidException("A default company cannot be created after one time!");

        Logger.LogDebug($"Create DefaultCompany '{createDefaultCompanyCommand.Name}'");

        var request = Mapper.Map<CreateCompanyCommand>(createDefaultCompanyCommand);

        request = CompanyDataHelper.GenerateDefaultCompany(request);

        ClearDataCache();

        return await Mediator.Send(request);
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllCompaniesCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllCompaniesNameCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}