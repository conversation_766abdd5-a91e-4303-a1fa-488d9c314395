﻿using ContinuityPatrol.Application.Features.Database.Events.DatabaseTestConnection;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Database.Commands.TestConnection;

public class
    DatabaseTestConnectionCommandHandler : IRequestHandler<DatabaseTestConnectionCommand,
        DatabaseTestConnectionResponse>
{
    private readonly IJobScheduler _client;
    private readonly IDatabaseRepository _databaseRepository;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;
    private readonly IPublisher _publisher;
    private readonly IWindowsService _windowsService;
    private readonly IDatabaseViewRepository _databaseViewRepository;
    private readonly IMapper _mapper;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILicenseValidationService _licenseValidationService;
    private readonly ILogger<DatabaseTestConnectionCommandHandler> _logger;

    public DatabaseTestConnectionCommandHandler(IDatabaseRepository databaseRepository, IDatabaseViewRepository databaseViewRepository,IMapper mapper,
        ILoadBalancerRepository nodeConfigurationRepository, IWindowsService windowsService, IPublisher publisher, IJobScheduler client, ILicenseManagerRepository licenseManagerRepository, ILicenseValidationService licenseValidationService, ILogger<DatabaseTestConnectionCommandHandler> logger)
    {
        _databaseRepository = databaseRepository;
        _databaseViewRepository = databaseViewRepository;
        _windowsService = windowsService;
        _nodeConfigurationRepository = nodeConfigurationRepository;
        _publisher = publisher;
        _client = client;
        _licenseManagerRepository = licenseManagerRepository;
        _licenseValidationService = licenseValidationService;
        _logger = logger;
        _mapper = mapper;
    }

    public async Task<DatabaseTestConnectionResponse> Handle(DatabaseTestConnectionCommand request,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("Starting test connection process for database IDs: {DatabaseIds}", request.Id);

        var eventToUpdate = await _databaseViewRepository.GetAllByDatabaseIdsAsync(request.Id);

        _logger.LogDebug("Retrieved {Count} databases for processing.", eventToUpdate.Count);
        _logger.LogDebug("Starting license validation for the retrieved databases.");

        await LicenseValidation(eventToUpdate);

        if (eventToUpdate.Count == 0) return new DatabaseTestConnectionResponse { Success = false, Message = "All databases are inactive or expired." };

        _logger.LogDebug("Fetching node configuration for the load balancer.");

        var nodeConfig =
            await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString())
            ?? await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(),
                ServiceType.LoadBalancer.ToString());

        if (nodeConfig is null)
        {
            _logger.LogError("Load Balancer is not configured for monitor service.");
            throw new InvalidException("Load Balancer is not configured for Monitor Service.");
        }
        var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";
        _logger.LogDebug("Node configuration fetched successfully. Base URL: {BaseUrl}", baseUrl);

        var monitorUrl = UrlHelper.GenerateMonitorCheckUrl(nodeConfig.TypeCategory, baseUrl);

        _logger.LogDebug("Generated monitor URL: {MonitorUrl}", monitorUrl);

        var monitorResponse = await _windowsService.CheckWindowsService(monitorUrl);

        if (!monitorResponse.Success)
        {
            _logger.LogError("Monitor service check failed. Inactive nodes: {InactiveNodes}, Message: {Message}", monitorResponse.InActiveNodes, monitorResponse.Message);
            throw new WindowServiceException(monitorResponse.InActiveNodes, ServiceType.Monitor.ToString(), monitorResponse.Message);
        }
        _logger.LogDebug("Monitor service check succeeded.");

        eventToUpdate.ForEach(x =>
        {
            x.LicenseKey = SecurityHelper.Encrypt(x.LicenseKey);
            x.ModeType = "Pending";
            x.IsConnection = true;
        });

        _logger.LogDebug("Updated database details: Encrypted license keys, set mode to 'Pending' and connection to true.");

        var databaseDto = _mapper.Map<List<Domain.Entities.Database>>(eventToUpdate);
        _logger.LogDebug("Mapped database details to DTO objects.");

        _ = await _databaseRepository.UpdateRangeAsync(databaseDto) as List<Domain.Entities.Database>;

        _logger.LogDebug("Updated database details in the repository.");

        await _publisher.Publish(new DatabaseTestConnectionEvent { Name = eventToUpdate.Select(x => x.Name).ToList()}, cancellationToken);

        _logger.LogDebug("Published DatabaseTestConnectionEvent for databases: {DatabaseNames}", string.Join(", ", eventToUpdate.Select(x => x.Name)));

        var url = UrlHelper.GenerateDatabaseTestConnectionUrl(nodeConfig.TypeCategory, baseUrl);

        _logger.LogDebug("Generated database test connection URL: {TestConnectionUrl}", url);

        await _client.ScheduleJob(Guid.NewGuid().ToString(), new Dictionary<string, string> { ["url"] = url });

         _logger.LogDebug("Scheduled job with URL: {JobUrl}", url);
        var response = new DatabaseTestConnectionResponse
        {
            Message = eventToUpdate.Count == 1
                ? $"Database '{string.Join(", ", eventToUpdate.Select(x => x.Name))}' test connection request sent successfully."
                : $"Database(s) '{string.Join(", ", eventToUpdate.Select(x => x.Name))}' test connection request sent successfully."

        };

        _logger.LogDebug("Test connection request completed. Response message: {ResponseMessage}", response.Message);

        return response;
    }

    private async Task LicenseValidation(List<DatabaseView> databaseViews)
    {
        var licenseIds = databaseViews.Where(x=>x.LicenseId.IsNotNullOrWhiteSpace()).Select(x => x.LicenseId).Distinct().ToList();

        _logger.LogDebug("Extracted license IDs: {@LicenseIds}", licenseIds);

        var licenseManager = await _licenseManagerRepository.GetLicenseExpiryDateByIds(licenseIds);

        _logger.LogDebug("Retrieved license manager details: {@LicenseManager}", licenseManager);

        foreach (var license in licenseManager)
        {
            var databases = databaseViews.Where(x => x.LicenseId == license.ReferenceId).ToList();

            if (!license.IsState || !await _licenseValidationService.IsLicenseExpired(license.ExpiryDate))
            {
                var stateMessage = !license.IsState ? "is in an inactive state" : "is expired";
                _logger.LogWarning("License '{PoNumber}' {StateMessage}, related databases: {AffectedDatabases}.",
                    license.PoNumber,
                    stateMessage,
                    string.Join(", ", databases.Select(x => x.Name)));

                var serverMap = _mapper.Map<List<Domain.Entities.Database>>(databases);
                serverMap.ForEach(x => x.ExceptionMessage = $"License '{license.PoNumber}' {stateMessage}.");

                await _databaseRepository.UpdateRangeAsync(serverMap);
                databaseViews.RemoveAll(x => x.LicenseId == license.ReferenceId);
            }
        }
    }
}