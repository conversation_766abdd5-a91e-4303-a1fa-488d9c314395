﻿const ClusterExistUrl = 'Configuration/HACMPCluster/IsClusterNameExist';
let createPermission = $("#configurationCreate").data("create-permission").toLowerCase();
let deletePermission = $("#configurationDelete").data("delete-permission").toLowerCase();

if (createPermission == 'false') {
    $("#create").removeClass('#create').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
}
$(function () {
    //Pagination
    btnCrudEnable('SaveFunction');
    btnCrudEnable('confirmDeleteButton');
    let selectedValues = [];
    let dataTable = $('#tblHacmpCluster').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/Configuration/HACMPCluster/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {

                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1,3,4,5],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    orderable: false,
                    "render": function (data, type, row, meta) {
                        
                        return data;
                    },
                    orderable: false
                },

                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                       
                            return '<span title="' + data + '">' + data + '</span>';
                           // return data;
                    }
                },
                {
                    "data": "serverName", "name": "Server Name", "autoWidth": true,
                    "render": function (data, type, row) {

                        return data;
                    }
                },
                {
                    "data": "lssrcPath", "name": "LSSRC Path", "autoWidth": true,
                    "render": function (data, type, row) {
                        
                            return '<span title="' + data + '">' + data + '</span>';
                            //return data;
                    }
                }, {
                    "data": "clrgInfoPath", "name": "CLRG Path", "autoWidth": true,
                    "render": function (data, type, row) {                       
                            return '<span title="' + data + '">' + data + '</span>';
                            // return data;
                    }
                }, {
                    "data": "resourceGroupName", "name": "Group Name", "autoWidth": true,
                    "render": function (data, type, row) {                       
                            return '<span title="' + data + '">' + data + '</span>';
                           // return data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class=" edit-button" data-cluster='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="delete-button" data-cluster-id="${row.id}" data-cluster-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class=" edit-button" data-cluster='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="icon-disabled">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="delete-button" data-cluster-id="${row.id}" data-cluster-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                  
                        </div>`;
                        }
                        else {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled">
                                    <i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }

                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    //Search Filter    
    $('#search-inp').on('keypress input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const NameValue = $('#Name')
        const ServerValue = $('#serverName')
        const PathValue1 = $('#Lssrcpath')
        const PathValue2 = $('#Clrgpath')
        const GroupValue = $('#groupValue')
        const inputValue = $('#search-inp').val();
        if (NameValue.is(':checked')) {
            selectedValues.push(NameValue.val() + inputValue);
        }
        if (ServerValue.is(':checked')) {
            selectedValues.push(ServerValue.val() + inputValue);
        }
        if (PathValue1.is(':checked')) {
            selectedValues.push(PathValue1.val() + inputValue);
        }
        if (PathValue2.is(':checked')) {
            selectedValues.push(PathValue2.val() + inputValue);
        }
        if (GroupValue.is(':checked')) {
            selectedValues.push(GroupValue.val() + inputValue);
        }
        let currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {                
                if (e.target.value && json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }
    },500));
    $('#txtName').on('keyup', commonDebounce(async function () {
        const value = $(this).val();
        let nameId = $('#clusterNameId').val() 
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);       
        await validateName(value, nameId, IsNameExist);
    },400))

    $('#ddlServer').on('change', function () {
        const value = $(this).val();
        let id = $(this).children(":selected").attr("id");
        $('#txtServerId').val(id);        
        validateDropDown(value, 'Select server', 'Server-error');        
    });
    $('#txtLSSRCPath').on('keyup', async function () {
        const value = $(this).val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        let errorElement = $('#LSSRCPath-error');
        await validatePath(value, 'Enter LSSRC path', errorElement)
    })
    $('#txtCLRGPath').on('keyup', async function () {
        const value = $(this).val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        let errorElement = $('#CLRGPath-error');
        await validatePath(value, 'Enter CLRG info path', errorElement)
    })
    $('#txtGroupName').on('keyup', async function () {
        const value = $(this).val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        let errorElement = $('#GroupName-error');
        await validateGroupName(value, 'Enter resource group name', errorElement)
    })
    //Save
    $('#SaveFunction').on('click', async function () {
        let form = $('#CreateForm')
        let name = $('#txtName').val();
        let nameId = $('#clusterNameId').val();
        let serverName = $('#ddlServer').val();
        let lssrcPath = $('#txtLSSRCPath').val();
        let clrgPath = $('#txtCLRGPath').val();
        let groupName = $('#txtGroupName').val();
        let isName = await validateName(name, nameId, IsNameExist);
        let isServerName = await validateDropDown(serverName, 'Select server', 'Server-error');
        let errorElement = $('#LSSRCPath-error');
        let islssrcPath = await validatePath(lssrcPath, 'Enter LSSRC path', errorElement);
        let errorElement1 = $('#CLRGPath-error');
        let isclrgPath = await validatePath(clrgPath, 'Enter CLRG info path', errorElement1);
        let errorElement2 = $('#GroupName-error');
        let isgroupName = await validateGroupName(groupName, 'Enter resource group name', errorElement2);
        //if (isName && isServerName && islssrcPath && isclrgPath && isgroupName) {
        //    btnCrudDiasable('SaveFunction');
        //    form.trigger('submit');
        //}
        let sanitizeArray = ['txtName', 'clusterNameId', 'ddlServer', 'txtLSSRCPath', 'txtCLRGPath', 'txtGroupName']
        sanitizeContainer(sanitizeArray)
        setTimeout(() => {
            if (isName && isServerName && islssrcPath && isclrgPath && isgroupName) {
                form.trigger('submit');
            }
        }, 200)       
    })
    let ClusterData = '';
    const errorElements = ['#Name-error', '#LSSRCPath-error', '#CLRGPath-error', '#GroupName-error', '#Server-error'];
    //Update
    $('#tblHacmpCluster').on('click', '.edit-button', function () {
        ClusterData = $(this).data("cluster");
        ClearErrorElements(errorElements);
        populateModalFields(ClusterData);
        $('#SaveFunction').text('Update');        
        $('#CreateModal').modal('show');
    });
    //Delete
    $('#tblHacmpCluster').on('click', '.delete-button', function () {
        let clusterId = $(this).data('cluster-id');
        let clusterName = $(this).data('cluster-name');
        $('#deleteData').text(clusterName);
        $('#deleteData').attr('title', clusterName)
        $('#textDeleteId').val(clusterId);
    });

    $('#confirmDeleteButton').on('click', function () {
        btnCrudDiasable('confirmDeleteButton');        
    });
    //Edit
    function populateModalFields(ClusterData) {         
        $('#txtName').val(ClusterData.name);
        $('#clusterNameId').val(ClusterData.id);
        $('#ddlServer').val(ClusterData.serverName);
        $('#txtServerId').val(ClusterData.serverId);
        $('#txtLSSRCPath').val(ClusterData.lssrcPath);
        $('#txtCLRGPath').val(ClusterData.clrgInfoPath);
        $('#txtGroupName').val(ClusterData.resourceGroupName);
    }
    //Clear previous data
    $('[data-bs-target="#CreateModal"]').on('click', function () {
        const errorElements = ['#Name-error', '#LSSRCPath-error', '#CLRGPath-error', '#GroupName-error', '#Server-error'];
        clearInputFields('CreateForm', errorElements);          
        $('#SaveFunction').text('Save');
       
    });
    function ClearErrorElements(errorElements) {
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    }
    //Validation
    async function validateName(value, id = null) {

        const errorElement = $('#Name-error');
         if (!value) {            
            errorElement.text('Enter HACMP cluster name')
                .addClass('field-validation-error');
            return false;
        }

        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }

        let url = RootUrl + ClusterExistUrl;
        let data = {};
        data.name = value;
        data.id = id;

        const validationResults = [
            await SpecialCharValidate(value),
            await ShouldNotBeginWithSpace(value),
            await ShouldNotBeginWithUnderScore(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await ShouldNotEndWithSpace(value),
            await ShouldNotAllowMultipleSpace(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsNameExist(url, data, OnError)
        ];

        return await CommonValidation(errorElement, validationResults);
    }
    async function IsNameExist(url, data, errorFunc) {
        return !data.name.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
    }
    function validateDropDown(value, errorMessage, errorElement) {
        if (!value) {
            $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            $('#' + errorElement).text('').removeClass('field-validation-error');
            return true;
        }
    }
    async function validatePath(value, errorMsg, errorElement) {
        if (!value) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        }
        const validationResults = [
            await InvalidPathRegex(value)
        ];
        return await CommonValidation(errorElement, validationResults);
    }
    async function validateGroupName(value, errorMsg, errorElement) {
        if (!value) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        }
        const validationResults = [
            await SpecialCharValidate(value),
            await ShouldNotBeginWithSpace(value),
            await ShouldNotBeginWithUnderScore(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await ShouldNotEndWithSpace(value),
            await ShouldNotAllowMultipleSpace(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
        ];
        return await CommonValidation(errorElement, validationResults);
    }
})