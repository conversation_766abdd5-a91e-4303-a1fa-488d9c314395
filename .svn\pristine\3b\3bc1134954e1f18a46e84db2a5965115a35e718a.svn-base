﻿using ContinuityPatrol.Application.Features.TeamMaster.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamMaster.Commands;

public class DeleteTeamMasterTests : IClassFixture<TeamMasterFixture>
{
    private readonly TeamMasterFixture _teamMasterFixture;

    private readonly Mock<ITeamMasterRepository> _mockTeamMasterRepository;

    private readonly DeleteTeamMasterCommandHandler _handler;

    public DeleteTeamMasterTests(TeamMasterFixture teamMasterFixture)
    {
        _teamMasterFixture = teamMasterFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockTeamMasterRepository = TeamMasterRepositoryMocks.DeleteTeamMasterRepository(_teamMasterFixture.TeamMasters);

        _handler = new DeleteTeamMasterCommandHandler(_mockTeamMasterRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_Success_When_Delete_TeamMaster()
    {
        var result = await _handler.Handle(new DeleteTeamMasterCommand { Id = _teamMasterFixture.TeamMasters[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteTeamMasterResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Return_IsActive_False_When_Delete_TeamMaster()
    {
        var result = await _handler.Handle(new DeleteTeamMasterCommand { Id = _teamMasterFixture.TeamMasters[0].ReferenceId }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_IsActive_False_When_GetByReferenceIdAsyncMethod_DeleteTeamMaster()
    {
        await _handler.Handle(new DeleteTeamMasterCommand { Id = _teamMasterFixture.TeamMasters[0].ReferenceId }, CancellationToken.None);

        var teamMaster = await _mockTeamMasterRepository.Object.GetByReferenceIdAsync(_teamMasterFixture.TeamMasters[0].ReferenceId);

        teamMaster.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidTeamMasterId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteTeamMasterCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteTeamMasterCommand { Id = _teamMasterFixture.TeamMasters[0].ReferenceId }, CancellationToken.None);

        _mockTeamMasterRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockTeamMasterRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.TeamMaster>()), Times.Once);
    }
}
