﻿using ContinuityPatrol.Application.Features.Setting.Events.Update;
using ContinuityPatrol.Shared.Infrastructure.Extensions;

namespace ContinuityPatrol.Application.Features.Setting.Commands.Update;

public class UpdateSettingCommandHandler : IRequestHandler<UpdateSettingCommand, UpdateSettingResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly ISettingRepository _settingRepository;
    private readonly ILogger<UpdateSettingCommandHandler> _logger;


    public UpdateSettingCommandHandler(IMapper mapper, IPublisher publisher, ISettingRepository settingRepository, ILogger<UpdateSettingCommandHandler> logger)
    {
        _mapper = mapper;
        _publisher = publisher;
        _settingRepository = settingRepository;
        _logger = logger;
    }

    public async Task<UpdateSettingResponse> Handle(UpdateSettingCommand request, CancellationToken cancellationToken)
    {
        var hasAccess = false;
        var message = string.Empty;
        var driveExists = false;
        var driveLetter = string.Empty;

        var eventToUpdate = await _settingRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Setting), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateSettingCommand), typeof(Domain.Entities.Setting));

        if (eventToUpdate.SKey.Trim().ToLower().Equals("log"))
        {
            var path = JObject.Parse(eventToUpdate.SValue).SelectToken("UI")?.ToString();

            var newPath = Path.Combine(path ?? "C:\\CP\\Logs\\Web", "CP_Web_log-.txt");

             driveLetter = Path.GetPathRoot(path)?.TrimEnd('\\');

            driveExists = DriveInfo.GetDrives().Any(d => d.Name.TrimEnd('\\').Equals(driveLetter, StringComparison.OrdinalIgnoreCase));

            // Read the appsettings.json file
            var jsonFilePath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");

            (hasAccess, message) = await HasWriteAccess(jsonFilePath);

            if (hasAccess)
            {
                var json = await File.ReadAllTextAsync(jsonFilePath, cancellationToken);
                var jsonObj = JObject.Parse(json);

                // Update the path in the JSON object
                jsonObj["Serilog"]!["WriteTo"]![0]!["Args"]!["path"] = newPath;

                // Save the updated JSON back to the file
                await File.WriteAllTextAsync(jsonFilePath, jsonObj.ToString(), cancellationToken);
            }

            if(driveExists)
              await _settingRepository.UpdateAsync(eventToUpdate);
        }
        else
        {
            await _settingRepository.UpdateAsync(eventToUpdate);
        }

        var response = new UpdateSettingResponse
        {
            Message = eventToUpdate.SKey.Trim().ToLower().Equals("log")
                ? driveExists == false ? $"The drive {driveLetter}:\\ is not available" : hasAccess ? "After restarting the IIS server, the file path should be changed." : message
                : Message.Update("Settings", eventToUpdate.SKey),

            Success = !eventToUpdate.SKey.Trim().ToLower().Equals("log"),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new SettingUpdatedEvent { SKey = eventToUpdate.SKey }, cancellationToken);

        return response;
    }
    private Task<(bool hasAccess, string message)> HasWriteAccess(string filePath)
    {
        try
        {
            var directoryPath = Path.GetDirectoryName(filePath);

            // Check if the directory exists
            if (!Directory.Exists(directoryPath))
            {
                _logger.LogWarning("Directory does not exist.");
                return Task.FromResult((false, "Directory does not exist. Please check the path."));
            }


            using FileStream fs = new FileStream(filePath, FileMode.OpenOrCreate, FileAccess.Write, FileShare.None);

            _logger.LogInformation("File is writable.");

            return Task.FromResult((true, "File is writable."));
        }
        catch (UnauthorizedAccessException)
        {
            _logger.LogError("Access denied to the file or directory.");
            return Task.FromResult((false, "Access denied. You do not have permission to write to this file."));
        }
        catch (DirectoryNotFoundException)
        {
            _logger.LogError("The specified directory was not found.");
            return Task.FromResult((false, "The specified directory was not found."));
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred: {ex.GetMessage()}");
            return Task.FromResult((false, $"An error occurred: {ex.GetMessage()}"));
        }
    }

}