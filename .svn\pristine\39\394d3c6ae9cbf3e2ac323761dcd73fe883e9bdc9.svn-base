using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class AlertNotificationRepositoryTests : IClassFixture<AlertNotificationFixture>
{
    private readonly AlertNotificationFixture _alertNotificationFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly AlertNotificationRepository _repository;

    public AlertNotificationRepositoryTests(AlertNotificationFixture alertNotificationFixture)
    {
        _alertNotificationFixture = alertNotificationFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new AlertNotificationRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var alertNotification = _alertNotificationFixture.AlertNotificationDto;

        // Act
        var result = await _repository.AddAsync(alertNotification);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(alertNotification.AlertCode, result.AlertCode);
        Assert.Equal(alertNotification.InfraObjectId, result.InfraObjectId);
        Assert.Single(_dbContext.AlertNotifications);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var alertNotification = _alertNotificationFixture.AlertNotificationDto;
        await _repository.AddAsync(alertNotification);

        alertNotification.AlertCode = "UPDATED_CODE";
        alertNotification.AlertSentCount = 5;

        // Act
        var result = await _repository.UpdateAsync(alertNotification);

        // Assert
        Assert.Equal("UPDATED_CODE", result.AlertCode);
        Assert.Equal(5, result.AlertSentCount);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.NullReferenceException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var alertNotification = _alertNotificationFixture.AlertNotificationDto;
        await _repository.AddAsync(alertNotification);

        // Act
        var result = await _repository.DeleteAsync(alertNotification);

        // Assert
        Assert.Equal(alertNotification.AlertCode, result.AlertCode);
        Assert.Empty(_dbContext.AlertNotifications);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var alertNotification = _alertNotificationFixture.AlertNotificationDto;
        var addedEntity = await _repository.AddAsync(alertNotification);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.AlertCode, result.AlertCode);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var alertNotification = _alertNotificationFixture.AlertNotificationDto;
        await _repository.AddAsync(alertNotification);

        // Act
        var result = await _repository.GetByReferenceIdAsync(alertNotification.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(alertNotification.ReferenceId, result.ReferenceId);
        Assert.Equal(alertNotification.AlertCode, result.AlertCode);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var alertNotifications = _alertNotificationFixture.AlertNotificationList;
        await _repository.AddRange(alertNotifications);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(alertNotifications.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var alertNotifications = _alertNotificationFixture.AlertNotificationList;

        // Act
        var result = await _repository.AddRange(alertNotifications);

        // Assert
        Assert.Equal(alertNotifications.Count, result.Count());
        Assert.Equal(alertNotifications.Count, _dbContext.AlertNotifications.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRange(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var alertNotifications = _alertNotificationFixture.AlertNotificationList;
        await _repository.AddRange(alertNotifications);

        // Act
        var result = await _repository.RemoveRange(alertNotifications);

        // Assert
        Assert.Equal(alertNotifications.Count, result.Count());
        Assert.Empty(_dbContext.AlertNotifications);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRange(null));
    }

    #endregion

    #region FindByFilter Tests

    [Fact]
    public async Task FindByFilter_ShouldReturnFilteredEntities()
    {
        // Arrange
        var alertNotifications = _alertNotificationFixture.AlertNotificationList;
        var targetAlertCode = "TEST_CODE";
        alertNotifications.First().AlertCode = targetAlertCode;
        await _repository.AddRange(alertNotifications);

        // Act
        var result = await _repository.FindByFilter(x => x.AlertCode == targetAlertCode);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(targetAlertCode, result.First().AlertCode);
    }

    [Fact]
    public async Task FindByFilter_ShouldReturnEmptyList_WhenNoMatch()
    {
        // Arrange
        var alertNotifications = _alertNotificationFixture.AlertNotificationList;
        await _repository.AddRange(alertNotifications);

        // Act
        var result = await _repository.FindByFilter(x => x.AlertCode == "NON_EXISTENT_CODE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region FilterBy Tests

    [Fact]
    public void FilterBy_ShouldReturnFilteredQueryable()
    {
        // Arrange
        var alertNotifications = _alertNotificationFixture.AlertNotificationList;
        var targetAlertCode = "FILTER_CODE";
        alertNotifications.First().AlertCode = targetAlertCode;
        _dbContext.AlertNotifications.AddRange(alertNotifications);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.FilterBy(x => x.AlertCode == targetAlertCode);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.ToList());
        Assert.Equal(targetAlertCode, result.First().AlertCode);
    }

    [Fact]
    public void FilterBy_ShouldReturnEmptyQueryable_WhenNoMatch()
    {
        // Arrange
        var alertNotifications = _alertNotificationFixture.AlertNotificationList;
        _dbContext.AlertNotifications.AddRange(alertNotifications);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.FilterBy(x => x.AlertCode == "NON_EXISTENT_CODE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.ToList());
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public void PaginatedListAllAsync_ShouldReturnQueryable()
    {
        // Arrange
        var alertNotifications = _alertNotificationFixture.AlertNotificationPaginationList;
        _dbContext.AlertNotifications.AddRange(alertNotifications);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.PaginatedListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(alertNotifications.Count, result.Count());
    }

    [Fact]
    public void PaginatedListAllAsync_ShouldReturnEmptyQueryable_WhenNoEntities()
    {
        // Act
        var result = _repository.PaginatedListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.ToList());
    }

    #endregion

    #region GetAlertNotificationByInfraObjectIdAndAlertCode Tests

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_ShouldReturnMatchingEntities()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var alertCode = "ALERT_001";

        var alertNotifications = new List<AlertNotification>
        {
            new AlertNotification
            {
                InfraObjectId = infraObjectId,
                AlertCode = alertCode,
                IsActive = true,
                ReferenceId = Guid.NewGuid().ToString(),
                AlertType = "Type1"
            },
            new AlertNotification
            {
                InfraObjectId = infraObjectId,
                AlertCode = alertCode,
                IsActive = true,
                ReferenceId = Guid.NewGuid().ToString(),
                AlertType = "Type2"
            },
            new AlertNotification
            {
                InfraObjectId = "INFRA_002",
                AlertCode = alertCode,
                IsActive = true,
                ReferenceId = Guid.NewGuid().ToString(),
                AlertType = "Type3"
            },
            new AlertNotification
            {
                InfraObjectId = infraObjectId,
                AlertCode = "ALERT_002",
                IsActive = true,
                ReferenceId = Guid.NewGuid().ToString(),
                AlertType = "Type4"
            }
        };

        await _repository.AddRange(alertNotifications);

        // Act
        var result = await _repository.GetAlertNotificationByInfraObjectIdAndAlertCode(infraObjectId, alertCode);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(infraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.Equal(alertCode, x.AlertCode));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_ShouldReturnEmpty_WhenNoMatch()
    {
        // Arrange
        var alertNotifications = _alertNotificationFixture.AlertNotificationList;
        await _repository.AddRange(alertNotifications);

        // Act
        var result = await _repository.GetAlertNotificationByInfraObjectIdAndAlertCode("NON_EXISTENT_INFRA", "NON_EXISTENT_CODE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_ShouldNotReturnInactiveEntities()
    {
        // Arrange
   
        var alertNotifications = _alertNotificationFixture.AlertNotificationList;

        await _repository.AddRange(alertNotifications);

        // Act
        var result = await _repository.GetAlertNotificationByInfraObjectIdAndAlertCode(alertNotifications[0].InfraObjectId, alertNotifications[0].AlertCode);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
        Assert.Equal(alertNotifications[0].AlertType, result.First().AlertType);
    }

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_ShouldHandleNullParameters()
    {
        // Act & Assert
        var result1 = await _repository.GetAlertNotificationByInfraObjectIdAndAlertCode(null, "ALERT_001");
        var result2 = await _repository.GetAlertNotificationByInfraObjectIdAndAlertCode("INFRA_001", null);
        var result3 = await _repository.GetAlertNotificationByInfraObjectIdAndAlertCode(null, null);

        Assert.NotNull(result1);
        Assert.Empty(result1);
        Assert.NotNull(result2);
        Assert.Empty(result2);
        Assert.NotNull(result3);
        Assert.Empty(result3);
    }

    #endregion

    #region UpdateRange Tests

    [Fact]
    public async Task UpdateRange_ShouldUpdateEntities()
    {
        // Arrange
        var alertNotifications = _alertNotificationFixture.AlertNotificationList.Take(3).ToList();
        await _repository.AddRange(alertNotifications);

        // Modify entities
        foreach (var notification in alertNotifications)
        {
            notification.AlertSentCount = 10;
            notification.AlertCode = "UPDATED_CODE";
        }

        // Act
        var result = await _repository.UpdateRange(alertNotifications);

        // Assert
        Assert.Equal(alertNotifications.Count, result.Count());
        Assert.All(result, x => Assert.Equal(10, x.AlertSentCount));
        Assert.All(result, x => Assert.Equal("UPDATED_CODE", x.AlertCode));
    }

    [Fact]
    public async Task UpdateRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateRange(null));
    }

    #endregion

    #region Edge Cases and Negative Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        var alertNotifyList= _alertNotificationFixture.AlertNotificationList.Take(2).ToList();
        var alertNotification1 = alertNotifyList[0];
        var alertNotification2 = alertNotifyList[1];

        var task1 = _repository.AddAsync(alertNotification1);
        var task2 = _repository.AddAsync(alertNotification2);

        var results = await Task.WhenAll(task1, task2);

        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.AlertNotifications.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var alertNotifications = _alertNotificationFixture.AlertNotificationList;

        // Act - Add, then update some, then delete some
        await _repository.AddRange(alertNotifications);
        var initialCount = alertNotifications.Count;

        var toUpdate = alertNotifications.Take(2).ToList();
        toUpdate.ForEach(x => x.AlertSentCount = 99);
        await _repository.UpdateRange(toUpdate);

        var toDelete = alertNotifications.Skip(2).Take(1).ToList();
        await _repository.RemoveRange(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.AlertSentCount == 99).ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_ShouldHandleEmptyStrings()
    {
        // Arrange
        var alertNotifications = _alertNotificationFixture.AlertNotificationList;
        await _repository.AddRange(alertNotifications);

        // Act & Assert
        var result1 = await _repository.GetAlertNotificationByInfraObjectIdAndAlertCode("", "ALERT_001");
        var result2 = await _repository.GetAlertNotificationByInfraObjectIdAndAlertCode("INFRA_001", "");
        var result3 = await _repository.GetAlertNotificationByInfraObjectIdAndAlertCode("", "");

        Assert.NotNull(result1);
        Assert.Empty(result1);
        Assert.NotNull(result2);
        Assert.Empty(result2);
        Assert.NotNull(result3);
        Assert.Empty(result3);
    }

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_ShouldBeCaseSensitive()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var alertCode = "ALERT_001";

        var alertNotification = new AlertNotification
        {
            InfraObjectId = infraObjectId,
            AlertCode = alertCode,
            IsActive = true,
            ReferenceId = Guid.NewGuid().ToString(),
            AlertType = "TestType"
        };

        await _repository.AddAsync(alertNotification);

        // Act
        var result1 = await _repository.GetAlertNotificationByInfraObjectIdAndAlertCode(infraObjectId.ToLower(), alertCode);
        var result2 = await _repository.GetAlertNotificationByInfraObjectIdAndAlertCode(infraObjectId, alertCode.ToLower());

        // Assert
        Assert.NotNull(result1);
        Assert.Empty(result1);
        Assert.NotNull(result2);
        Assert.Empty(result2);
    }

    #endregion
}
