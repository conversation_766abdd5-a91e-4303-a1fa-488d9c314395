using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Create;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Delete;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Update;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetList;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.HacmpClusterModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class HacmpClusterService : BaseService,IHacmpClusterService
{
    public HacmpClusterService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<HacmpClusterListVm>> GetHacmpClusterList()
    {
        Logger.LogDebug("Get All HacmpClusters");

        return await Mediator.Send(new GetHacmpClusterListQuery());
    }

    public async Task<HacmpClusterDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "HacmpCluster Id");

        Logger.LogDebug($"Get HacmpCluster Detail by Id '{id}'");

        return await Mediator.Send(new GetHacmpClusterDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateHacmpClusterCommand createHacmpClusterCommand)
    {
        Logger.LogDebug($"Create HacmpCluster '{createHacmpClusterCommand}'");

        return await Mediator.Send(createHacmpClusterCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateHacmpClusterCommand updateHacmpClusterCommand)
    {
        Logger.LogDebug($"Update HacmpCluster '{updateHacmpClusterCommand}'");

        return await Mediator.Send(updateHacmpClusterCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "HacmpCluster Id");

        Logger.LogDebug($"Delete HacmpCluster Details by Id '{id}'");

        return await Mediator.Send(new DeleteHacmpClusterCommand { Id = id });
    }
     #region NameExist
 public async Task<bool> IsHacmpClusterNameExist(string name, string? id)
 {
     Guard.Against.NullOrWhiteSpace(name, "HacmpCluster Name");

     Logger.LogDebug($"Check Name Exists Detail by HacmpCluster Name '{name}' and Id '{id}'");

     return await Mediator.Send(new GetHacmpClusterNameUniqueQuery { Name = name, Id = id });
 }
    #endregion

     #region Paginated
public async Task<PaginatedResult<HacmpClusterListVm>> GetPaginatedHacmpClusters(GetHacmpClusterPaginatedListQuery query)
{
    Logger.LogDebug("Get Searching Details in HacmpCluster Paginated List");

    return await Mediator.Send(query);
}
     #endregion
}
