﻿using ContinuityPatrol.Application.Features.Alert.Queries.GetInfraObjectId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Alert.Queries;

public class GetAlertByInfraObjectIdQueryHandlerTests : IClassFixture<AlertFixture>
{
    private readonly AlertFixture _alertFixture;

    private readonly Mock<IAlertRepository> _mockAlertRepository;

    private readonly GetAlertByInfraObjectIdQueryHandler _handler;

    public GetAlertByInfraObjectIdQueryHandlerTests(AlertFixture alertFixture)
    {
        _alertFixture = alertFixture;
        
        _mockAlertRepository = AlertRepositoryMocks.GetAlertByInfraObjectIdRepository(_alertFixture.Alerts);
        
        _handler = new GetAlertByInfraObjectIdQueryHandler(_mockAlertRepository.Object, _alertFixture.Mapper);
    }

    [Fact]
    public async Task Handle_ReturnAlert_When_ValidClientId()
    {
        var result = await _handler.Handle(new GetAlertByInfraObjectIdQuery 
            { InfraObjectId = _alertFixture.Alerts[0].InfraObjectId, EntityId = _alertFixture.Alerts[0].EntityId }, CancellationToken.None);

        result.ShouldBeOfType<List<AlertByInfraObjectIdVm>>();
        result.Count.ShouldBe(1);

        result[0].Id.ShouldBe(_alertFixture.Alerts[0].ReferenceId);
        result[0].Type.ShouldBe(_alertFixture.Alerts[0].Type);
        result[0].Severity.ShouldBe(_alertFixture.Alerts[0].Severity);
        result[0].SystemMessage.ShouldBe(_alertFixture.Alerts[0].SystemMessage);
        result[0].UserMessage.ShouldBe(_alertFixture.Alerts[0].UserMessage);
        result[0].JobName.ShouldBe(_alertFixture.Alerts[0].JobName);
        result[0].InfraObjectId.ShouldBe(_alertFixture.Alerts[0].InfraObjectId);
        result[0].InfraObjectName.ShouldBe(_alertFixture.Alerts[0].InfraObjectName);
        result[0].ClientAlertId.ShouldBe(_alertFixture.Alerts[0].ClientAlertId);
        result[0].IsResolve.ShouldBe(_alertFixture.Alerts[0].IsResolve);
        result[0].IsAcknowledgement.ShouldBe(_alertFixture.Alerts[0].IsAcknowledgement);
        result[0].EntityId.ShouldBe(_alertFixture.Alerts[0].EntityId);
        result[0].EntityType.ShouldBe(_alertFixture.Alerts[0].EntityType);
        result[0].AlertCategoryId.ShouldBe(_alertFixture.Alerts[0].AlertCategoryId);
    }

    [Fact]
    public async Task Handle_Call_GetAlertByInfraObjectIdMethod_OneTime()
    {
        await _handler.Handle(new GetAlertByInfraObjectIdQuery { InfraObjectId = _alertFixture.Alerts[0].InfraObjectId, EntityId = _alertFixture.Alerts[0].EntityId }, CancellationToken.None);

        _mockAlertRepository.Verify(x => x.GetAlertByInfraObjectId(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }
}