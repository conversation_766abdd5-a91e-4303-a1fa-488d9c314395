﻿function monitorTypeMysql(value, infraObjectName, moniterType, parsedData) {
    let prWfName = [], drWfName = [];
    let prStatusArr = [], drStatusArr = [];
    let prWfDisplay = '--', drWfDisplay = '--';
    let prStatusDisplay = '--', drStatusDisplay = '--';
    let iconWF = '', iconStatus = '';
    let monitor = value?.monitorServiceDetails;

    if (value?.monitorServiceDetails?.length > 0) {
        value?.monitorServiceDetails?.forEach(list => {
            let parsed = [];
            const isValidJson = list?.isServiceUpdate && list?.isServiceUpdate.trim().startsWith('[');

            if (isValidJson) {
                try {
                    parsed = JSON?.parse(list?.isServiceUpdate);
                } catch (err) {
                    console.warn('Invalid JSON in isServiceUpdate:', list?.isServiceUpdate);
                    parsed = [];
                }
            }
            parsed?.forEach(entry => {
                entry?.Services?.forEach(service => {
                    if (entry?.Type?.toLowerCase() === 'pr') {
                        if (service?.ServiceName && service?.ServiceName !== 'NA') {
                            prWfName.push(service?.ServiceName);
                        }
                        if (service?.Status && service?.Status !== 'NA') {
                            prStatusArr.push(service?.Status);
                        }
                    } else if (entry?.Type?.toLowerCase() === 'dr') {
                        if (service?.ServiceName && service?.ServiceName !== 'NA') {
                            drWfName.push(service?.ServiceName);
                        }
                        if (service?.Status && service?.Status !== 'NA') {
                            drStatusArr.push(service?.Status);
                        }
                    }
                });
            });
        });

        // Unique workflow names
        prWfDisplay = prWfName?.length > 0 ? [...new Set(prWfName)].join(', ') : '--';
        drWfDisplay = drWfName?.length > 0 ? [...new Set(drWfName)].join(', ') : '--';

        // Status summary
        function getStatusSummary(arr) {
            let countMap = {};
            arr?.forEach(status => {
                countMap[status] = (countMap[status] || 0) + 1;
            });
            let total = arr?.length;
            let statusSummary = Object.entries(countMap)
                .map(([status, count]) => `${count} ${status}`)
                .join(', ');
            return statusSummary ? `${statusSummary} / ${total}` : '--';
        }

        prStatusDisplay = getStatusSummary(prStatusArr);
        drStatusDisplay = getStatusSummary(drStatusArr);

        iconWF = (prWfDisplay !== '--' || drWfDisplay !== '--') ? '<i class="text-primary cp-monitoring-services me-1 fs-6"></i>' : '';

        iconStatus = (prStatusDisplay !== '--' || drStatusDisplay !== '--') ? '<i class="text-primary cp-Job-status me-1 fs-6"></i>' : '';

    }
    const getDRDetails = (data, value, obj = null) => {

        let tdHtml = '';
        data.forEach((item, i) => {
            let iconClass = getIconClass(value, item);
            let tableData = obj ? item?.MonitoringModel[obj][value] : item?.MonitoringModel[value];

            tdHtml += `<td class="text-truncate"><i class="${iconClass} me-1"></i>${tableData || 'NA'}</td>`
        })
        return tdHtml
    }
    const getIconClass = (value, monitoringData) => {
        let iconClass = '';

        if (value == 'Server_Name') {
            iconClass = 'cp-stand-server text-primary'

        } else if (value === 'Server_IpAddress' || value === 'Server_HostName') {
            let text = monitoringData?.MonitoringModel?.Server_Status?.toLowerCase()
            iconClass = text === 'down' ? "cp-down-linearrow me-1 text-danger" : text === 'pending' ? 'cp-pending me-1 text-warning' : text === 'up' ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";

        } else if (value === 'Database') {

            iconClass = monitoringData?.MonitoringModel?.Database ? 'cp-database me-1 text-primary' : "cp-disable me-1 text-danger";

        } else if (value === 'Master_Log_File') {
            iconClass = monitoringData?.MonitoringModel?.Master_Log_File?.includes("NA") ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.Master_Log_File ? "cp-log-file-name me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'Relay_Master_Log_File') {
            iconClass = monitoringData?.MonitoringModel?.Relay_Master_Log_File?.includes("NA") ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.Relay_Master_Log_File ? "cp-log-file-name me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'Master_Log_Position') {
            iconClass = monitoringData?.MonitoringModel?.Master_Log_Position?.includes("NA") ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.Master_Log_Position ? "cp-log-file-name me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'Exec_Master_Log_Position') {
            iconClass = monitoringData?.MonitoringModel?.Exec_Master_Log_Position?.includes("NA") ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.Exec_Master_Log_Position ? "cp-log-file-name me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'Replication_Connect_State') {
            iconClass = monitoringData?.MonitoringModel?.Replication_Connect_State?.includes("NA") ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.Replication_Connect_State ? "cp-connected me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'PR_Datalag') {
            iconClass = monitoringData?.MonitoringModel?.PR_Datalag?.includes("NA") ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.PR_Datalag ? "cp-time text-primary mt-2" : "cp-disable me-1 text-danger";

        }

        return iconClass;
    }

    const getDynamicHeader = (MySqlMonitoringModels) => {

        let dynamicHeader = '';

        MySqlMonitoringModels?.length && MySqlMonitoringModels?.map((data) => {
            dynamicHeader += `<th>${data?.Type}</th>`
        })

        return dynamicHeader;
    }
    if (moniterType === "Mysql") {        
        let prStatus = parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Server_Status 
        let ipOrHostName;
        let application = value?.monitorServiceDetails[0]?.isServiceUpdate?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.isServiceUpdate?.toLowerCase()?.includes("running") ? "text-success cp-reload cp-animate" : "text-danger cp-fail-back";
        let workflow = value?.monitorServiceDetails[0]?.workflowName?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.workflowName ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
        let pripaddress = prStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : prStatus?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : prStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
        let dripaddress = value?.drServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : value?.drServerStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
        let prlogfile = parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Master_Log_File?.includes('NA') ? "cp-disable me-1 text-danger" : parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Master_Log_File ? "cp-log-file-name me-1 text-primary" : "cp-disable me-1 text-danger";
        let prmasterfile = parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Relay_Master_Log_File?.includes('NA') ? "cp-disable me-1 text-danger" : parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Relay_Master_Log_File ? "cp-log-file-name me-1 text-primary" : "cp-disable me-1 text-danger";
        let prpositionfile = parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Master_Log_Position?.includes('NA') ? "cp-disable me-1 text-danger" : parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Master_Log_Position ? "cp-log-file-name me-1 text-primary" : "cp-disable me-1 text-danger";
        let prapplyfile = parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Exec_Master_Log_Position?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Exec_Master_Log_Position ? "cp-log-file-name me-1 text-primary" : "cp-disable me-1 text-danger";
        let prconnectedfile = parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PRReplication_Connect_State?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PRReplication_Connect_State ? "cp-connected me-1 text-primary" : "cp-disable me-1 text-danger";
        let ipprdata = parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.Pr_ConnectViaHostName.toLowerCase() === "true" ? parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Server_HostName : parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Server_IpAddress
        let drdata = parsedData?.MySqlMonitoringModels.map((ip) => ip.MonitoringModel?.connectViaHostName);
        parsedData?.MySqlMonitoringModels.forEach((ip, index) => {

            let isHostName = drdata[index]?.toLowerCase() === "true";
            value = isHostName ? 'Server_HostName' : 'Server_IpAddress';
            ipOrHostName = isHostName
                ? getDRDetails(parsedData?.MySqlMonitoringModels, 'Server_HostName')
                : getDRDetails(parsedData?.MySqlMonitoringModels, 'Server_IpAddress');
        });

        let infraobjectdata = 
            '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
            ' <table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
            '<thead style="position: sticky;top: 0px;">' +
            '<tr>' +
            '<th>Component Monitor</th>' +
            ' <th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.MySqlMonitoringModels)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + 'Server Name' + '</td>' +
            '<td>' + '<i class="cp-stand-server me-1 text-primary"></i>' + (parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Server_Name !== undefined && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Server_Name !== null && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Server_Name !== "" ? parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Server_Name : "NA") + '</td>' +
            `${getDRDetails(parsedData?.MySqlMonitoringModels, 'Server_Name')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'IP Address/HostName' + '</td>' +
            '<td>' + '<i class="' + pripaddress + '"></i>' + (ipprdata|| "NA") + '</td>' +
            `${ipOrHostName}` +  
            '</tr>' +           
            '<tr>' +
            '<td>' + 'Database Name' + '</td>' +
            '<td>' + '<i class="cp-database me-1 text-primary"></i>' + (parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Database !== undefined && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Database !== null && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Database !== "" ? parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Database : "NA") + '</td>' +
            `${getDRDetails(parsedData?.MySqlMonitoringModels, 'Database')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'Master Log File' + '</td>' +
            '<td>' + '<i class="' + prlogfile + '"></i>' + (parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Master_Log_File !== undefined && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Master_Log_File !== null && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Master_Log_File !== "" ? parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Master_Log_File : "NA") + '</td>' +
            `${getDRDetails(parsedData?.MySqlMonitoringModels, 'Master_Log_File')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'Relay Master Log File' + '</td>' +
            '<td>' + '<i class="' + prmasterfile + '"></i>' + (parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Relay_Master_Log_File !== undefined && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Relay_Master_Log_File !== null && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Relay_Master_Log_File !== "" ? parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Relay_Master_Log_File : "NA") + '</td>' +
            `${getDRDetails(parsedData?.MySqlMonitoringModels, 'Relay_Master_Log_File')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'Master Log Position' + '</td>' +
            '<td>' + '<i class="' + prpositionfile + '"></i>' + (parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Master_Log_Position !== undefined && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Master_Log_Position !== null && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Master_Log_Position !== "" ? parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Master_Log_Position : "NA") + '</td>' +
            `${getDRDetails(parsedData?.MySqlMonitoringModels, 'Master_Log_Position')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'Exec Master Log Position' + '</td>' +
            '<td>' + '<i class="' + prapplyfile + '"></i>' + (parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Exec_Master_Log_Position !== undefined && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Exec_Master_Log_Position !== null && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Exec_Master_Log_Position !== "" ? parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PR_Exec_Master_Log_Position : "NA") + '</td>' +
            `${getDRDetails(parsedData?.MySqlMonitoringModels, 'Exec_Master_Log_Position')}` +
            '</tr>';
       
            infraobjectdata += generateMonitorServiceDetailsRow(workflow, application, monitor);

        infraobjectdata += '</tbody>' +
            '</table>' +
            '</div>' +
            '<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
            '<thead style="position: sticky;top: 0px;z-index: 1;">' +
            '<tr>' +
            '<th>Replication Monitor</th>' +
            '<th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.MySqlMonitoringModels)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + "Replication Connect State" + '</td>' +
            '<td>' + '<i class="' + prconnectedfile + '"></i>' + (parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PRReplication_Connect_State !== undefined && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PRReplication_Connect_State !== null && parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PRReplication_Connect_State !== "" ? parsedData?.PrMySqlMonitoringModel?.MonitoringModel?.PRReplication_Connect_State : "NA") + '</td>' +
            `${getDRDetails(parsedData?.MySqlMonitoringModels, 'Replication_Connect_State')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "DataLag" + '</td>' +
          //  '<td>' + (parsedData?.MySqlMonitoringModels?.MonitoringModel?.PR_Datalag !== undefined && parsedData?.MySqlMonitoringModels?.MonitoringModel?.PR_Datalag !== null && parsedData?.MySqlMonitoringModels?.MonitoringModel?.PR_Datalag !== "" ? parsedData?.MySqlMonitoringModels?.MonitoringModel?.PR_Datalag : "NA") + '</td>' +
            `${getDRDetails(parsedData?.MySqlMonitoringModels, 'PR_Datalag')}` +
            '</tr>'+
            '</tbody>' +
            '</table>' +
            '</div>' 
            

        setTimeout(() => {
            $("#infraobjectalldata").append(infraobjectdata);
        }, 200)


    }
}