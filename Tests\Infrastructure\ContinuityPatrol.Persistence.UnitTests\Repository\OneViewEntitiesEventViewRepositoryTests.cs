using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class OneViewEntitiesEventViewRepositoryTests : IClassFixture<OneViewEntitiesEventViewFixture>
{
    private readonly OneViewEntitiesEventViewFixture _oneViewEntitiesEventViewFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly OneViewEntitiesEventViewRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public OneViewEntitiesEventViewRepositoryTests(OneViewEntitiesEventViewFixture oneViewEntitiesEventViewFixture)
    {
        _oneViewEntitiesEventViewFixture = oneViewEntitiesEventViewFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(OneViewEntitiesEventViewFixture.UserId);

        _repository = new OneViewEntitiesEventViewRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    private async Task ClearDatabase()
    {
        // Note: For views, we typically can't directly manipulate data
        // This method is kept for consistency but may not be used in view tests
        try
        {
            _dbContext.OneViewEntitiesEventViews.RemoveRange(_dbContext.OneViewEntitiesEventViews);
            await _dbContext.SaveChangesAsync();
        }
        catch
        {
            // Views may not support direct manipulation, which is expected
        }
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEvents()
    {
        // Arrange
        await ClearDatabase();
        var events = new List<OneViewEntitiesEventView>
        {
            _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithProperties(),
            _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithProperties(),
            _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithProperties()
        };

        // For views, we simulate data being present (since views are read-only)
        // In real scenarios, the view would be populated by the underlying database view
        await _dbContext.OneViewEntitiesEventViews.AddRangeAsync(events);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, eventView => Assert.NotNull(eventView.Entity));
        Assert.All(result, eventView => Assert.NotNull(eventView.Message));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmpty_WhenNoEvents()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldUseAsNoTracking()
    {
        // Arrange
        await ClearDatabase();
        var events = new List<OneViewEntitiesEventView>
        {
            _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithProperties()
        };

        await _dbContext.OneViewEntitiesEventViews.AddRangeAsync(events);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        // Verify that entities are not tracked (characteristic of AsNoTracking)
        var trackedEntities = _dbContext.ChangeTracker.Entries<OneViewEntitiesEventView>().ToList();
        Assert.Empty(trackedEntities.Where(e => e.State != Microsoft.EntityFrameworkCore.EntityState.Detached));
    }

    [Fact]
    public async Task ListAllAsync_ShouldHandleMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var events = _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithDifferentEntities();

        await _dbContext.OneViewEntitiesEventViews.AddRangeAsync(events);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(4, result.Count);

        var entities = result.Select(e => e.Entity).ToList();
        Assert.Contains("Database", entities);
        Assert.Contains("Server", entities);
        Assert.Contains("Application", entities);
        Assert.Contains("Network", entities);
    }

    [Fact]
    public async Task ListAllAsync_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();
        var specialCharacterEntities = OneViewEntitiesEventViewFixture.TestData.SpecialCharacterEntities;
        var events = new List<OneViewEntitiesEventView>();

        foreach (var entity in specialCharacterEntities)
        {
            var eventView = _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithProperties(entity: entity);
            events.Add(eventView);
        }

        await _dbContext.OneViewEntitiesEventViews.AddRangeAsync(events);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(specialCharacterEntities.Length, result.Count);

        foreach (var entity in specialCharacterEntities)
        {
            Assert.Contains(result, e => e.Entity == entity);
        }
    }

    [Fact]
    public async Task ListAllAsync_ShouldHandleUnicodeCharacters()
    {
        // Arrange
        await ClearDatabase();
        var unicodeEntity = "测试实体_тест_テスト";
        var unicodeMessage = "测试消息_тест_テスト";

        var eventView = _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithProperties(
            entity: unicodeEntity,
            message: unicodeMessage);

        await _dbContext.OneViewEntitiesEventViews.AddAsync(eventView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(unicodeEntity, result[0].Entity);
        Assert.Equal(unicodeMessage, result[0].Message);
    }

    [Fact]
    public async Task ListAllAsync_ShouldHandleLongMessages()
    {
        // Arrange
        await ClearDatabase();
        var longMessageEvent = _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithLongMessage(1000);

        await _dbContext.OneViewEntitiesEventViews.AddAsync(longMessageEvent);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(1000, result[0].Message.Length);
    }

    [Fact]
    public async Task ListAllAsync_ShouldHandleWhitespaceInFields()
    {
        // Arrange
        await ClearDatabase();
        var whitespaceEvent = _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithWhitespace();

        await _dbContext.OneViewEntitiesEventViews.AddAsync(whitespaceEvent);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("  Database  ", result[0].Entity);
        Assert.Equal("  Test Message  ", result[0].Message);
    }

    [Fact]
    public async Task ListAllAsync_ShouldOrderByLastModifiedDate()
    {
        // Arrange
        await ClearDatabase();
        var oldDate = DateTime.UtcNow.AddDays(-2);
        var newDate = DateTime.UtcNow.AddDays(-1);

        var events = new List<OneViewEntitiesEventView>
        {
            _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithProperties(lastModifiedDate: newDate),
            _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithProperties(lastModifiedDate: oldDate)
        };

        await _dbContext.OneViewEntitiesEventViews.AddRangeAsync(events);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        // Note: The actual ordering depends on the view definition, not the repository
        // This test verifies that all data is returned correctly
    }

    [Fact]
    public async Task ListAllAsync_ShouldHandleConcurrentAccess()
    {
        // Arrange
        await ClearDatabase();
        var events = new List<OneViewEntitiesEventView>
        {
            _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithProperties(),
            _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithProperties(),
            _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithProperties()
        };

        await _dbContext.OneViewEntitiesEventViews.AddRangeAsync(events);
        await _dbContext.SaveChangesAsync();

        // Act - Perform concurrent read operations
        var tasks = new List<Task<List<OneViewEntitiesEventView>>>();
        for (int i = 0; i < 5; i++)
        {
            tasks.Add(_repository.ListAllAsync());
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        Assert.All(results, result =>
        {
            Assert.NotNull(result);
            Assert.Equal(3, result.Count);
        });
    }

    #endregion

    #region Repository Characteristics Tests

    [Fact]
    public void Repository_ShouldHaveCorrectDependencies()
    {
        // Assert
        Assert.NotNull(_repository);
        Assert.NotNull(_mockLoggedInUserService.Object);
    }

    [Fact]
    public async Task Repository_ShouldBeReadOnly()
    {
        // This test verifies that the repository is designed for read-only operations
        // which is appropriate for a view repository

        // Arrange
        var eventView = _oneViewEntitiesEventViewFixture.CreateOneViewEntitiesEventViewWithProperties();

        // Act & Assert - The repository should only have ListAllAsync method
        // No Add, Update, Delete methods should be available for view repositories
        var repositoryType = typeof(OneViewEntitiesEventViewRepository);
        var methods = repositoryType.GetMethods().Where(m => m.DeclaringType == repositoryType).ToList();

        Assert.Contains(methods, m => m.Name == "ListAllAsync");
        Assert.DoesNotContain(methods, m => m.Name == "AddAsync");
        Assert.DoesNotContain(methods, m => m.Name == "UpdateAsync");
        Assert.DoesNotContain(methods, m => m.Name == "DeleteAsync");
    }

    [Fact]
    public async Task Repository_ShouldHandleEmptyView()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
        Assert.IsType<List<OneViewEntitiesEventView>>(result);
    }

    #endregion
}
