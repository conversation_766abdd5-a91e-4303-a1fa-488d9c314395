﻿using ContinuityPatrol.Application.Features.LicenseManager.Events.BaseLicenseEvent.Update;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;

public class UpdateBaseLicenseCommandHandler : IRequestHandler<UpdateBaseLicenseCommand, UpdateBaseLicenseResponse>
{
    private readonly IInfraObjectInfoRepository _infraObjectInfoRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly ILicenseHistoryRepository _licenseHistoryRepository;
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILicenseValidationService _licenseValidationService;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateBaseLicenseCommandHandler(IMapper mapper, ILicenseManagerRepository licenseManagerRepository,
        IPublisher publisher, ILicenseHistoryRepository licenseHistoryRepository,
        ILoggedInUserService loggedInUserService, ILicenseValidationService licenseValidationService,
        IInfraObjectInfoRepository infraObjectInfoRepository, ILicenseInfoRepository licenseInfoRepository,
        IInfraObjectRepository infraObjectRepository)
    {
        _licenseManagerRepository = licenseManagerRepository;
        _mapper = mapper;
        _publisher = publisher;
        _licenseHistoryRepository = licenseHistoryRepository;
        _loggedInUserService = loggedInUserService;
        _licenseValidationService = licenseValidationService;
        _infraObjectInfoRepository = infraObjectInfoRepository;
        _licenseInfoRepository = licenseInfoRepository;
        _infraObjectRepository = infraObjectRepository;
    }

    public async Task<UpdateBaseLicenseResponse> Handle(UpdateBaseLicenseCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _licenseManagerRepository.GetByReferenceIdAsync(request.Id);

        var (newLicenseKey, _) = await SplitLicenseKey(request.LicenseKey);

        if (newLicenseKey.LicenseActionType.Trim().ToLower().Contains("renewal"))
        {
            if (IsEnterpriseOrSubscriptionOrPocOrUit(newLicenseKey.LicenseType))
                switch (newLicenseKey.LicenseAction.Trim().ToLower())
                {
                    case var action when action.Contains("countonly"):
                        await UpdateLicense(eventToUpdate, request, updateLicenseCount: true);
                        break;
                    case var action when action.Contains("expirydateonly"):
                        await UpdateLicense(eventToUpdate, request, true);
                        break;
                    case var action when action.Contains("both"):
                        await UpdateLicense(eventToUpdate, request, true, true);
                        break;
                    case var action when action.Contains("upgrade"):
                        await UpdateLicense(eventToUpdate, request, true, true);
                        break;
                }
        }
        else if (newLicenseKey.LicenseActionType.Trim().ToLower().Contains("amc"))
        {
            switch (newLicenseKey.LicenseAction.Trim().ToLower())
            {
                case var action when action.Contains("amcnew"):
                    await UpdateLicense(eventToUpdate, request, newAmc: true);
                    break;
                case var action when action.Contains("amcrenewal"):
                    await UpdateLicense(eventToUpdate, request, renewalAmc: true);
                    break;
            }
        }

        var response = new UpdateBaseLicenseResponse
        {
            Id = eventToUpdate.ReferenceId,

            Message = $"Base License  '{newLicenseKey.PoNumber}' upgraded successfully."
        };

        await _publisher.Publish(new BaseLicenseUpdatedEvent { PONumber = newLicenseKey.PoNumber },
            cancellationToken);

        return response;
    }

    private Task<(LicenseDto licenseDto, string decryptLicenseKey)> SplitLicenseKey(string licenseKey)
    {
        var decryptLicenseKey = SecurityHelper.Decrypt(licenseKey);

        var splitLicenseKey = decryptLicenseKey.Split('*');

        var licenseDto = _mapper.Map<LicenseDto>(splitLicenseKey);

        return Task.FromResult((licenseDto, decryptLicenseKey));
    }

    private bool IsEnterpriseOrSubscriptionOrPocOrUit(string licenseType)
    {
        var lowerLicenseType = licenseType.Trim().ToLower();
        return lowerLicenseType.Contains("enterprise") ||
               lowerLicenseType.Contains("subscription") ||
               lowerLicenseType.Contains("poc") ||
               lowerLicenseType.Contains("uat");
    }

    private async Task UpdateLicense(Domain.Entities.LicenseManager eventToUpdate, UpdateBaseLicenseCommand request,
        bool updateExpiryDate = false, bool updateLicenseCount = false, bool newAmc = false, bool renewalAmc = false)
    {
        var (newLicenseKey, newDecryptLicenseKey) = await SplitLicenseKey(request.LicenseKey);
        var (existLicenseKey, _) = await SplitLicenseKey(eventToUpdate.LicenseKey);

        var existingLicenseCount = SecurityHelper.Decrypt(eventToUpdate.Properties);
        var expiryDate = SecurityHelper.Decrypt(eventToUpdate.ExpiryDate);
        var updatedLicenseKey = SecurityHelper.Decrypt(request.LicenseKey);
        var isAmc = eventToUpdate.IsAmc;
        var serviceStartDate = SecurityHelper.Decrypt(eventToUpdate.AmcStartDate);
        var serviceEndDate = SecurityHelper.Decrypt(eventToUpdate.AmcEndDate);

        if (updateExpiryDate)
        {
            if (eventToUpdate.IsExpired)
            {
                await UpdateInfraObjectState(eventToUpdate.ReferenceId);
            }
            else
            {
                await UpdateInfraObjectState(eventToUpdate.ReferenceId);
            }
            
            expiryDate = await _licenseValidationService.UpdateExpiryDate(newLicenseKey, eventToUpdate);
        }

        if (updateLicenseCount || !DateTime.Parse(existLicenseKey.LicenseGeneratorDate)
                .Equals(DateTime.Parse(newLicenseKey.LicenseGeneratorDate)))
        {
            existingLicenseCount = newLicenseKey.LicenseCount;
            updatedLicenseKey = newDecryptLicenseKey;
        }

        if (newAmc)
        {
            isAmc = await _licenseValidationService.IsAmc(newLicenseKey.SupportPlan);
            serviceStartDate = await _licenseValidationService.AmcStartDate(newLicenseKey.SupportPlan);
            serviceEndDate = await _licenseValidationService.AmcEndDate(newLicenseKey.SupportPlan);
        }

        if (renewalAmc)
        {
            isAmc = await _licenseValidationService.IsAmc(newLicenseKey.SupportPlan);
            serviceEndDate = await _licenseValidationService.UpdateAmcDate(newLicenseKey, eventToUpdate);
            serviceStartDate = await _licenseValidationService.AmcStartDate(newLicenseKey.SupportPlan);
        }

        var newRequest = UpdateBaseLicenseCommand(request.Id, newLicenseKey, existingLicenseCount, expiryDate,
            updatedLicenseKey, isAmc, serviceStartDate, serviceEndDate);
        newRequest.IsState = eventToUpdate.IsState;
        await UpdateLicenseManager(eventToUpdate, newRequest);
        await UpdateDerivedLicenses(eventToUpdate, newLicenseKey, expiryDate);
        await AddLicenseHistory(newRequest);
    }


    private UpdateBaseLicenseCommand UpdateBaseLicenseCommand(string id, LicenseDto licenseKey, string properties,
        string expiryDate, string updatedLicenseKey, bool isAmc, string amcStartDate, string amcEndDate)
    {
        return new UpdateBaseLicenseCommand
        {
            Id = id,
            PoNumber = SecurityHelper.Encrypt(licenseKey.PoNumber),
            CompanyId = _loggedInUserService.CompanyId,
            CompanyName = _loggedInUserService.CompanyName,
            HostName = SecurityHelper.Encrypt(licenseKey.CpHostName),
            IpAddress = SecurityHelper.Encrypt(licenseKey.IpAddress),
            MacAddress = SecurityHelper.Encrypt(licenseKey.MacAddress),
            Properties = SecurityHelper.Encrypt(properties),
            Validity = SecurityHelper.Encrypt(licenseKey.LicenseType),
            ExpiryDate = SecurityHelper.Encrypt(expiryDate),
            ParentId = string.Empty,
            IsParent = _loggedInUserService.IsParent,
            LicenseKey = SecurityHelper.Encrypt(updatedLicenseKey),
            ParentPoNumber = SecurityHelper.Encrypt("NA"),
            IsAmc = isAmc,
            AmcPlan = SecurityHelper.Encrypt(licenseKey.SupportPlan),
            AmcStartDate = SecurityHelper.Encrypt(amcStartDate),
            AmcEndDate = SecurityHelper.Encrypt(amcEndDate)
        };
    }

    private async Task UpdateLicenseManager(Domain.Entities.LicenseManager eventToUpdate,
        UpdateBaseLicenseCommand newRequest)
    {
        _mapper.Map(newRequest, eventToUpdate, typeof(UpdateBaseLicenseCommand),
            typeof(Domain.Entities.LicenseManager));
        await _licenseManagerRepository.UpdateAsync(eventToUpdate);
    }

    private async Task UpdateDerivedLicenses(Domain.Entities.LicenseManager eventToUpdate, LicenseDto newLicenseKey,
        string expiryDate)
    {
        var parentPoNumber = SecurityHelper.Decrypt(eventToUpdate.PoNumber);

        var derivedLicenses =
            await _licenseManagerRepository.GetDerivedLicenseDetailByBaseLicenseDetailAsync(eventToUpdate.CompanyId,
                parentPoNumber);

        foreach (var license in derivedLicenses) UpdateDerivedLicense(license, newLicenseKey, expiryDate);

        await _licenseManagerRepository.UpdateRange(derivedLicenses);
    }

    private void UpdateDerivedLicense(Domain.Entities.LicenseManager license, LicenseDto newLicenseKey,
        string expiryDate)
    {
        var derivedLicenseKey = SecurityHelper.Decrypt(license.LicenseKey);
        var updatedLicenseKey = new StringBuilder(derivedLicenseKey)
            .Replace(derivedLicenseKey.Split('*')[5], newLicenseKey.LicenseType)
            .ToString();

        license.PoNumber = SecurityHelper.Encrypt(license.PoNumber);
        license.HostName = SecurityHelper.Encrypt(license.HostName);
        license.Properties = SecurityHelper.Encrypt(license.Properties);
        license.IpAddress = SecurityHelper.Encrypt(license.IpAddress);
        license.MacAddress = SecurityHelper.Encrypt(license.MacAddress);
        license.ParentPoNumber = SecurityHelper.Encrypt(license.ParentPoNumber);
        license.Validity = SecurityHelper.Encrypt(newLicenseKey.LicenseType);
        license.ExpiryDate = SecurityHelper.Encrypt(expiryDate);
        license.LicenseKey = SecurityHelper.Encrypt(updatedLicenseKey);
    }

    private async Task AddLicenseHistory(UpdateBaseLicenseCommand newRequest)
    {
        await _licenseHistoryRepository.AddAsync(new Domain.Entities.LicenseHistory
        {
            LicenseId = newRequest.Id,
            PONumber = newRequest.PoNumber,
            CompanyName = newRequest.CompanyName,
            CompanyId = newRequest.CompanyId,
            CPHostName = newRequest.HostName,
            IPAddress = newRequest.IpAddress,
            MACAddress = newRequest.MacAddress,
            Properties = newRequest.Properties,
            Validity = newRequest.Validity,
            ExpiryDate = newRequest.ExpiryDate,
            ParentId = string.Empty,
            IsParent = _loggedInUserService.IsParent,
            LicenseKey = newRequest.LicenseKey,
            UpdaterId = _loggedInUserService.UserId,
            ParentPONumber = SecurityHelper.Encrypt("NA"),
            IsState = newRequest.IsState
        });
    }

    private async Task UpdateInfraObjectState(string licenseId)
    {
        var license = await _licenseManagerRepository.GetByReferenceIdAsync(licenseId);

        license.IsExpired = false;

        await _licenseManagerRepository.UpdateAsync(license);

        var licenseInfos = await _licenseInfoRepository.GetLicenseInfoDetailByLicenseId(licenseId);

        var groupByEntity = licenseInfos.GroupBy(x => x.Entity)
            .Select(x => new
            {
                x.Key,
                LicenseInfos = x.ToList()
            }).ToList();


        var infraObjectList = new List<Domain.Entities.InfraObject>();

        foreach (var licenseInfo in groupByEntity)
        foreach (var server in licenseInfo.LicenseInfos)
        {
            var infraObjects = licenseInfo.Key.Trim().ToLower() switch
            {
                var key when key.Contains("server") => await _infraObjectRepository.GetInfraObjectByServerId(
                    server.EntityId),
                var key when key.Contains("database") => await _infraObjectRepository.GetInfraObjectByDatabaseId(
                    server.EntityId),
                var key when key.Contains("replication") => await _infraObjectRepository.GetInfraObjectByReplicationId(
                    server.EntityId),
                _ => Enumerable.Empty<Domain.Entities.InfraObject>()
            };

            infraObjectList.AddRange(infraObjects);
        }

        foreach (var infraObject in infraObjectList.DistinctBy(x => x.ReferenceId))
        {
            var infraObjectInfo =
                await _infraObjectInfoRepository.GetInfraObjectInfoByInfraObjectId(infraObject.ReferenceId);

            var eventToUpdate = await _infraObjectRepository.GetByReferenceIdAsync(infraObject.ReferenceId);

            eventToUpdate.State = (infraObjectInfo?.PreviousState == "Locked") ? "Active" : (infraObjectInfo?.PreviousState ?? "Active");

            await _infraObjectRepository.UpdateAsync(eventToUpdate);
        }
    }
}