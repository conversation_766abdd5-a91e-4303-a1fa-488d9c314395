﻿using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Create;
using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Update;
using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.CredentialProfileModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class CredentialProfileProfile : Profile
{
    public CredentialProfileProfile()
    {
        CreateMap<CredentialProfile, CreateCredentialProfileCommand>().ReverseMap();
        CreateMap<UpdateCredentialProfileCommand, CredentialProfile>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<UpdateCredentialProfileCommand, CredentialProfileViewModel>().ReverseMap();
        CreateMap<CreateCredentialProfileCommand, CredentialProfileListVm>().ReverseMap();

        CreateMap<CredentialProfile, CredentialProfileDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<CredentialProfile, CredentialProfileListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<CredentialProfile, CredentialProfileNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<CredentialProfile, CredentialProfileListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<CredentialProfile, CredentialProfileTypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId)); 
        CreateMap<PaginatedResult<CredentialProfile>, PaginatedResult<CredentialProfileListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}