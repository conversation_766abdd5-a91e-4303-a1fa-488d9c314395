using System.Runtime.Intrinsics.X86;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SingleSignOnRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly SingleSignOnRepository _repository;
    private readonly SingleSignOnFixture _fixture;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public SingleSignOnRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        
        _repository = new SingleSignOnRepository(_dbContext, _mockLoggedInUserService.Object);
        _fixture = new SingleSignOnFixture();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnSingleSignOnsForCompany()
    {
        // Arrange
        await ClearDatabase();
        await SetupComponentTypes();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO1", companyId: "COMPANY_123", signOnTypeId: "TYPE_001");
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO2", companyId: "COMPANY_123", signOnTypeId: "TYPE_002");
        var sso3 = _fixture.CreateSingleSignOn(profileName: "SSO3", companyId: "COMPANY_456", signOnTypeId: "TYPE_001");

        await _repository.AddAsync(sso1);
        await _repository.AddAsync(sso2);
        await _repository.AddAsync(sso3);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.ProfileName == "SSO1");
        Assert.Contains(result, s => s.ProfileName == "SSO2");
        Assert.DoesNotContain(result, s => s.ProfileName == "SSO3");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnMappedProperties()
    {
        // Arrange
        await ClearDatabase();
        await SetupComponentTypes();

        var sso = _fixture.CreateSingleSignOn(
            profileName: "TestSSO",
            companyId: "COMPANY_123",
            signOnTypeId: "TYPE_001",
            properties: "{\"test\":\"value\"}",
            formVersion: "1.0"
        );

        await _repository.AddAsync(sso);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var ssoResult = result.First();
        Assert.Equal("TestSSO", ssoResult.ProfileName);
        Assert.Equal("COMPANY_123", ssoResult.CompanyId);
        Assert.Equal("TYPE_001", ssoResult.SignOnTypeId);
        Assert.Equal("{\"test\":\"value\"}", ssoResult.Properties);
        Assert.Equal("1.0", ssoResult.FormVersion);
    }

    #endregion

    #region GetType Tests

    [Fact]
    public async Task GetType_ShouldReturnSingleSignOnsWithMatchingTypeId_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupComponentTypes();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO1", signOnTypeId: "TYPE_001");
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO2", signOnTypeId: "TYPE_001");
        var sso3 = _fixture.CreateSingleSignOn(profileName: "SSO3", signOnTypeId: "TYPE_002");

        await _repository.AddAsync(sso1);
        await _repository.AddAsync(sso2);
        await _repository.AddAsync(sso3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetType("TYPE_001");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.ProfileName == "SSO1");
        Assert.Contains(result, s => s.ProfileName == "SSO2");
        Assert.DoesNotContain(result, s => s.ProfileName == "SSO3");
    }

    [Fact]
    public async Task GetType_ShouldReturnSingleSignOnsForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupComponentTypes();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO1", companyId: "COMPANY_123", signOnTypeId: "TYPE_001");
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO2", companyId: "COMPANY_456", signOnTypeId: "TYPE_001");

        await _repository.AddAsync(sso1);
        await _repository.AddAsync(sso2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetType("TYPE_001");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("SSO1", result.First().ProfileName);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnSingleSignOn_WhenIdExistsForCompany()
    {
        // Arrange
        await ClearDatabase();
        await SetupComponentTypes();

        var sso = _fixture.CreateSingleSignOn(profileName: "TestSSO", companyId: "COMPANY_123");
        await _repository.AddAsync(sso);

        // Act
        var result = await _repository.GetByReferenceIdAsync(sso.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("TestSSO", result.ProfileName);
        Assert.Equal("COMPANY_123", result.CompanyId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIdExistsButDifferentCompany()
    {
        // Arrange
        await ClearDatabase();
        await SetupComponentTypes();

        var sso = _fixture.CreateSingleSignOn(profileName: "TestSSO", companyId: "COMPANY_456");
        await _repository.AddAsync(sso);

        // Act
        var result = await _repository.GetByReferenceIdAsync(sso.ReferenceId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetSingleSignOnNames Tests

    [Fact]
    public async Task GetSingleSignOnNames_ShouldReturnActiveNamesForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO_B", companyId: "COMPANY_123", isActive: true);
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO_A", companyId: "COMPANY_123", isActive: true);
        var sso3 = _fixture.CreateSingleSignOn(profileName: "SSO_C", companyId: "COMPANY_456", isActive: true);
        var sso4 = _fixture.CreateSingleSignOn(profileName: "SSO_D", companyId: "COMPANY_123", isActive: false);

        await _dbContext.SingleSignOns.AddRangeAsync(sso1, sso2, sso3, sso4);
        _dbContext.SaveChanges();
        //await _repository.AddAsync(sso1);
        //await _repository.AddAsync(sso2);
        //await _repository.AddAsync(sso3);
        //await _repository.AddAsync(sso4);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetSingleSignOnNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Equal("SSO_A", result[0].ProfileName); // Should be ordered by ProfileName
        Assert.Equal("SSO_B", result[1].ProfileName);
        Assert.All(result, sso => Assert.NotNull(sso.ReferenceId));
    }

    [Fact]
    public async Task GetSingleSignOnNames_ShouldReturnAllActiveNames_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO_B", companyId: "COMPANY_123", isActive: true);
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO_A", companyId: "COMPANY_456", isActive: true);
        var sso3 = _fixture.CreateSingleSignOn(profileName: "SSO_C", companyId: "COMPANY_123", isActive: false);


        await _dbContext.SingleSignOns.AddRangeAsync(sso1, sso2, sso3);
        _dbContext.SaveChanges();

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetSingleSignOnNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Equal("SSO_A", result[0].ProfileName); // Should be ordered by ProfileName
        Assert.Equal("SSO_B", result[1].ProfileName);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public async Task GetPaginatedQuery_ShouldReturnQueryableForCompany()
    {
        // Arrange
        await ClearDatabase();
        await SetupComponentTypes();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO1", companyId: "COMPANY_123");
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO2", companyId: "COMPANY_123");
        var sso3 = _fixture.CreateSingleSignOn(profileName: "SSO3", companyId: "COMPANY_456");

        await _repository.AddAsync(sso1);
        await _repository.AddAsync(sso2);
        await _repository.AddAsync(sso3);

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = await result.ToListAsync();
        Assert.Equal(2, resultList.Count);
        Assert.Contains(resultList, s => s.ProfileName == "SSO1");
        Assert.Contains(resultList, s => s.ProfileName == "SSO2");
        Assert.DoesNotContain(resultList, s => s.ProfileName == "SSO3");
    }

    #endregion

    #region IsProfileNameExist Tests

    [Fact]
    public async Task IsProfileNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsNotValidGuid()
    {
        // Arrange
        await ClearDatabase();

        var sso = _fixture.CreateSingleSignOn(profileName: "ExistingProfile");
        await _repository.AddAsync(sso);

        // Act
        var result = await _repository.IsProfileNameExist("ExistingProfile", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsProfileNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsNotValidGuid()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsProfileNameExist("NonExistentProfile", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsProfileNameExist_ShouldReturnFalse_WhenNameExistsButIdMatches()
    {
        // Arrange
        await ClearDatabase();

        var sso = _fixture.CreateSingleSignOn(profileName: "TestProfile");
        await _repository.AddAsync(sso);

        // Act
        var result = await _repository.IsProfileNameExist("TestProfile", sso.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsProfileNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsDifferent()
    {
        // Arrange
        await ClearDatabase();

        var sso = _fixture.CreateSingleSignOn(profileName: "TestProfile");
        await _repository.AddAsync(sso);

        // Act
        var result = await _repository.IsProfileNameExist("TestProfile", Guid.NewGuid().ToString());

        // Assert
        Assert.True(result);
    }

    #endregion

    #region IsProfileNameUnique Tests

    [Fact]
    public async Task IsProfileNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();

        var sso = _fixture.CreateSingleSignOn(profileName: "ExistingProfile");
        await _repository.AddAsync(sso);

        // Act
        var result = await _repository.IsProfileNameUnique("ExistingProfile");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsProfileNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsProfileNameUnique("NonExistentProfile");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsProfileNameUnique_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();

        var sso = _fixture.CreateSingleSignOn(profileName: "TestProfile");
        await _repository.AddAsync(sso);

        // Act
        var result = await _repository.IsProfileNameUnique("testprofile");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupComponentTypes();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO1", signOnTypeId: "TYPE_001");
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO2", signOnTypeId: "TYPE_002");
        var sso3 = _fixture.CreateSingleSignOn(profileName: "SSO3", signOnTypeId: "TYPE_001");

        await _repository.AddAsync(sso1);
        await _repository.AddAsync(sso2);
        await _repository.AddAsync(sso3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        string? searchString = null;

        var specification = new SingleSignOnFilterSpecification(searchString);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 2, specification, "ProfileName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Data.Count);
        Assert.Equal(3, result.TotalCount);
        Assert.Equal(2, result.PageSize);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupComponentTypes();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO1", companyId: "COMPANY_123", signOnTypeId: "TYPE_001");
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO2", companyId: "COMPANY_456", signOnTypeId: "TYPE_002");


        await _dbContext.SingleSignOns.AddRangeAsync(sso1, sso2);
        _dbContext.SaveChanges();

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        string? searchString = null;

        var specification = new SingleSignOnFilterSpecification(searchString);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "ProfileName", "asc");

        // Assert
        Assert.NotNull(result);

    }

    #endregion

    #region GetSingleSignOnByType Tests

    [Fact]
    public async Task GetSingleSignOnByType_Paginated_ShouldReturnFilteredResults_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupComponentTypes();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO1", signOnTypeId: "TYPE_001");
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO2", signOnTypeId: "TYPE_001");
        var sso3 = _fixture.CreateSingleSignOn(profileName: "SSO3", signOnTypeId: "TYPE_002");

        await _repository.AddAsync(sso1);
        await _repository.AddAsync(sso2);
        await _repository.AddAsync(sso3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        string? searchString = null;

        var specification = new SingleSignOnFilterSpecification(searchString);

        // Act
        var result = await _repository.GetSingleSignOnByType("TYPE_001", 1, 10, specification, "ProfileName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Data.Count);
        Assert.Contains(result.Data, s => s.ProfileName == "SSO1");
        Assert.Contains(result.Data, s => s.ProfileName == "SSO2");
        Assert.DoesNotContain(result.Data, s => s.ProfileName == "SSO3");
    }

    [Fact]
    public async Task GetSingleSignOnByType_Paginated_ShouldReturnFilteredResultsForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupComponentTypes();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO1", companyId: "COMPANY_123", signOnTypeId: "TYPE_001");
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO2", companyId: "COMPANY_456", signOnTypeId: "TYPE_001");

        await _repository.AddAsync(sso1);
        await _repository.AddAsync(sso2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        string? searchString = null;

        var specification = new SingleSignOnFilterSpecification(searchString);

        // Act
        var result = await _repository.GetSingleSignOnByType("TYPE_001", 1, 10, specification, "ProfileName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Data);
        Assert.Equal("SSO1", result.Data.First().ProfileName);
    }

    [Fact]
    public async Task GetSingleSignOnByType_Queryable_ShouldReturnFilteredQueryable_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupComponentTypes();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO1", signOnTypeId: "TYPE_001");
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO2", signOnTypeId: "TYPE_002");

        await _repository.AddAsync(sso1);
        await _repository.AddAsync(sso2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = _repository.GetSingleSignOnByType("TYPE_001");

        // Assert
        Assert.NotNull(result);
        var resultList = await result.ToListAsync();
        Assert.Single(resultList);
        Assert.Equal("SSO1", resultList.First().ProfileName);
    }

    [Fact]
    public async Task GetSingleSignOnByType_Queryable_ShouldReturnFilteredQueryableForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();
        await SetupComponentTypes();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO1", companyId: "COMPANY_123", signOnTypeId: "TYPE_001");
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO2", companyId: "COMPANY_456", signOnTypeId: "TYPE_001");

        await _repository.AddAsync(sso1);
        await _repository.AddAsync(sso2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = _repository.GetSingleSignOnByType("TYPE_001");

        // Assert
        Assert.NotNull(result);
        var resultList = await result.ToListAsync();
        Assert.Single(resultList);
        Assert.Equal("SSO1", resultList.First().ProfileName);
    }

    #endregion

    #region GetSingleSignOnByTypeIds Tests

    [Fact]
    public async Task GetSingleSignOnByTypeIds_ShouldReturnMatchingTypeIds_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO1", signOnTypeId: "TYPE_001");
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO2", signOnTypeId: "TYPE_002");
        var sso3 = _fixture.CreateSingleSignOn(profileName: "SSO3", signOnTypeId: "TYPE_003");

        await _repository.AddAsync(sso1);
        await _repository.AddAsync(sso2);
        await _repository.AddAsync(sso3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var typeIds = new List<string> { "TYPE_001", "TYPE_002" };

        // Act
        var result = await _repository.GetSingleSignOnByTypeIds(typeIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.ProfileName == "SSO1");
        Assert.Contains(result, s => s.ProfileName == "SSO2");
        Assert.DoesNotContain(result, s => s.ProfileName == "SSO3");
    }

    [Fact]
    public async Task GetSingleSignOnByTypeIds_ShouldReturnMatchingTypeIdsForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var sso1 = _fixture.CreateSingleSignOn(profileName: "SSO1", companyId: "COMPANY_123", signOnTypeId: "TYPE_001");
        var sso2 = _fixture.CreateSingleSignOn(profileName: "SSO2", companyId: "COMPANY_456", signOnTypeId: "TYPE_001");
        var sso3 = _fixture.CreateSingleSignOn(profileName: "SSO3", companyId: "COMPANY_123", signOnTypeId: "TYPE_002");

        await _repository.AddAsync(sso1);
        await _repository.AddAsync(sso2);
        await _repository.AddAsync(sso3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var typeIds = new List<string> { "TYPE_001", "TYPE_002" };

        // Act
        var result = await _repository.GetSingleSignOnByTypeIds(typeIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.ProfileName == "SSO1");
        Assert.Contains(result, s => s.ProfileName == "SSO3");
        Assert.DoesNotContain(result, s => s.ProfileName == "SSO2"); // Different company
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnSingleSignOn_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var sso = _fixture.CreateSingleSignOn(profileName: "TestSSO");
        await _repository.AddAsync(sso);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetByIdAsync(sso.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("TestSSO", result.ProfileName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnSingleSignOn_WhenUserIsNotParentAndSameCompany()
    {
        // Arrange
        await ClearDatabase();

        var sso = _fixture.CreateSingleSignOn(profileName: "TestSSO", companyId: "COMPANY_123");
        await _repository.AddAsync(sso);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetByIdAsync(sso.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("TestSSO", result.ProfileName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenUserIsNotParentAndDifferentCompany()
    {
        // Arrange
        await ClearDatabase();

        var sso = _fixture.CreateSingleSignOn(profileName: "TestSSO", companyId: "COMPANY_456");
        await _repository.AddAsync(sso);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetByIdAsync(sso.Id);

        // Assert
        Assert.Null(result);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.SingleSignOns.RemoveRange(_dbContext.SingleSignOns);
        _dbContext.ComponentTypes.RemoveRange(_dbContext.ComponentTypes);
        await _dbContext.SaveChangesAsync();
    }

    private async Task SetupComponentTypes()
    {
        var componentType1 = new ComponentType
        {
            ReferenceId = "TYPE_001",
            ComponentName = "Type 1",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };

        var componentType2 = new ComponentType
        {
            ReferenceId = "TYPE_002",
            ComponentName = "Type 2",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };

        _dbContext.ComponentTypes.AddRange(componentType1, componentType2);
        await _dbContext.SaveChangesAsync();
    }
}
