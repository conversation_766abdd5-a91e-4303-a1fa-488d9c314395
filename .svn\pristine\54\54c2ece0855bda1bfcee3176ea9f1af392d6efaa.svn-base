﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfile.Commands;

public class UpdateWorkflowProfileTests : IClassFixture<WorkflowProfileFixture>
{
    private readonly WorkflowProfileFixture _workflowProfileFixture;

    private readonly Mock<IWorkflowProfileRepository> _mockWorkflowProfileRepository;

    private readonly UpdateWorkflowProfileCommandHandler _handler;

    public UpdateWorkflowProfileTests(WorkflowProfileFixture workflowProfileFixture)
    {
        _workflowProfileFixture = workflowProfileFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockWorkflowProfileRepository = WorkflowProfileRepositoryMocks.UpdateWorkflowProfileRepository(_workflowProfileFixture.WorkflowProfiles);

        _handler = new UpdateWorkflowProfileCommandHandler(_workflowProfileFixture.Mapper, _mockWorkflowProfileRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidWorkflowProfile_UpdateReferenceIdAsync_ToWorkflowProfilesRepo()
    {
        _workflowProfileFixture.UpdateWorkflowProfileCommand.Id = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId;

        var result = await _handler.Handle(_workflowProfileFixture.UpdateWorkflowProfileCommand, CancellationToken.None);

        var workflowProfile = await _mockWorkflowProfileRepository.Object.GetByReferenceIdAsync(result.WorkflowProfileId);

        Assert.Equal(_workflowProfileFixture.UpdateWorkflowProfileCommand.Name, workflowProfile.Name);
    }

    [Fact]
    public async Task Handle_Return_ValidWorkflowProfileResponse_WhenUpdate_WorkflowProfile()
    {
        _workflowProfileFixture.UpdateWorkflowProfileCommand.Id = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId;

        var result = await _handler.Handle(_workflowProfileFixture.UpdateWorkflowProfileCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateWorkflowProfileResponse));

        result.WorkflowProfileId.ShouldBeGreaterThan(0.ToString());

        result.WorkflowProfileId.ShouldBe(_workflowProfileFixture.UpdateWorkflowProfileCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidWorkflowProfileId()
    {
        _workflowProfileFixture.UpdateWorkflowProfileCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_workflowProfileFixture.UpdateWorkflowProfileCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        _workflowProfileFixture.UpdateWorkflowProfileCommand.Id = _workflowProfileFixture.WorkflowProfiles[0].ReferenceId;

        await _handler.Handle(_workflowProfileFixture.UpdateWorkflowProfileCommand, CancellationToken.None);

        _mockWorkflowProfileRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockWorkflowProfileRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.WorkflowProfile>()), Times.Once);
    }
}