﻿namespace ContinuityPatrol.Application.Features.InfraObject.Events.DashboardViewEvent.Update;

public class InfraObjectDashboardViewUpdatedEventHandler : INotificationHandler<InfraObjectDashboardViewUpdatedEvent>
{
    private readonly IDashboardViewRepository _dashboardViewRepository;

    private readonly ILogger<InfraObjectDashboardViewUpdatedEventHandler> _logger;

    public InfraObjectDashboardViewUpdatedEventHandler(IDashboardViewRepository dashboardViewRepository,
        ILogger<InfraObjectDashboardViewUpdatedEventHandler> logger)
    {
        _dashboardViewRepository = dashboardViewRepository;
        _logger = logger;
    }

    public async Task Handle(InfraObjectDashboardViewUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var dashboardView = await _dashboardViewRepository.GetBusinessViewByInfraObjectId(updatedEvent.InfraObjectId);

        if (dashboardView is not null)
        {
            dashboardView.InfraObjectId = updatedEvent.InfraObjectId;
            dashboardView.InfraObjectName = updatedEvent.InfraObjectName;
            dashboardView.BusinessServiceId = updatedEvent.BusinessServiceId;
            dashboardView.BusinessServiceName = updatedEvent.BusinessServiceName;
            dashboardView.BusinessFunctionId = updatedEvent.BusinessFunctionId;
            dashboardView.BusinessFunctionName = updatedEvent.BusinessFunctionName;
            dashboardView.DROperationStatus = updatedEvent.DROperationStatus;
            dashboardView.ReplicationStatus = updatedEvent.ReplicationStatus;
            dashboardView.Type = updatedEvent.Type;
            dashboardView.State = updatedEvent.State;

            await _dashboardViewRepository.UpdateAsync(dashboardView);

            _logger.LogInformation(
                $"InfraObject :: DashboardViewUpdatedEvent '{updatedEvent.InfraObjectName}' updated successfully.");
        }
    }
}