using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowTempFixture : IDisposable
{
    public List<WorkflowTemp> WorkflowTempPaginationList { get; set; }
    public List<WorkflowTemp> WorkflowTempList { get; set; }
    public WorkflowTemp WorkflowTempDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowTempFixture()
    {
        var fixture = new Fixture();

        WorkflowTempList = fixture.Create<List<WorkflowTemp>>();

        WorkflowTempPaginationList = fixture.CreateMany<WorkflowTemp>(20).ToList();

        WorkflowTempDto = fixture.Create<WorkflowTemp>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
