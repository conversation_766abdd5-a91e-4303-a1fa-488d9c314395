using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class IncidentDailyFixture : IDisposable
{
    public List<IncidentDaily> IncidentDailyPaginationList { get; set; }
    public List<IncidentDaily> IncidentDailyList { get; set; }
    public IncidentDaily IncidentDailyDto { get; set; }

    public const string ParentBusinessServiceId = "BS_123";
    public const string InfraObjectId = "INFRA_123";

    public ApplicationDbContext DbContext { get; private set; }

    public IncidentDailyFixture()
    {
        var fixture = new Fixture();

        IncidentDailyList = fixture.Create<List<IncidentDaily>>();

        IncidentDailyPaginationList = fixture.CreateMany<IncidentDaily>(20).ToList();

        // Setup proper test data for IncidentDailyPaginationList
        IncidentDailyPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        IncidentDailyPaginationList.ForEach(x => x.IsActive = true);
        IncidentDailyPaginationList.ForEach(x => x.ParentBusinessServiceId = ParentBusinessServiceId);
        IncidentDailyPaginationList.ForEach(x => x.InfraObjectId = InfraObjectId);
        IncidentDailyPaginationList.ForEach(x => x.IncidentDate = DateTime.Today);

        // Setup proper test data for IncidentDailyList
        IncidentDailyList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        IncidentDailyList.ForEach(x => x.IsActive = true);
        IncidentDailyList.ForEach(x => x.ParentBusinessServiceId = ParentBusinessServiceId);
        IncidentDailyList.ForEach(x => x.InfraObjectId = InfraObjectId);
        IncidentDailyList.ForEach(x => x.IncidentDate = DateTime.Today);

        IncidentDailyDto = fixture.Create<IncidentDaily>();
        IncidentDailyDto.ReferenceId = Guid.NewGuid().ToString();
        IncidentDailyDto.IsActive = true;
        IncidentDailyDto.ParentBusinessServiceId = ParentBusinessServiceId;
        IncidentDailyDto.ParentBusinessServiceName = "Test Business Service";
        IncidentDailyDto.InfraObjectId = InfraObjectId;
        IncidentDailyDto.InfraObjectName = "Test Infrastructure Object";
        IncidentDailyDto.Open = 5;
        IncidentDailyDto.Close = 3;
        IncidentDailyDto.Total = 8;
        IncidentDailyDto.IncidentDate = DateTime.Today;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
