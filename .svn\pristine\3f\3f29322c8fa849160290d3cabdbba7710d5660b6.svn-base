﻿using ContinuityPatrol.Application.Features.AccessManager.Commands.Create;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Delete;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Update;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetByRole;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetList;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AccessManagerModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class AccessManagerService : BaseService, IAccessManagerService
{
    public AccessManagerService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateAccessManagerCommand createAccessManagerCommand)
    {
        Logger.LogDebug($"Creating Access Manager '{createAccessManagerCommand.RoleName}'");

        return await Mediator.Send(createAccessManagerCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string accessManagerId)
    {
        Guard.Against.InvalidGuidOrEmpty(accessManagerId, "Access Manager Id");

        Logger.LogDebug($"Deleting AccessManagerId Details by Id '{accessManagerId}'");

        return await Mediator.Send(new DeleteAccessManagerCommand { Id = accessManagerId });
    }

    public async Task<PaginatedResult<AccessManagerListVm>> GetAccessManagerPaginatedList(
        GetAccessManagerPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in AccessManager Paginated List");

        return await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllAccessManagersCacheKey,
            () => Mediator.Send(query));
    }

    public async Task<List<AccessManagerListVm>> GetAccessMangerList()
    {
        Logger.LogDebug("Get in AccessManager List");

        return await Mediator.Send(new GetAccessManagerListQuery());
    }

    //public async Task<List<GetAccessManagerByUserIdVm>> GetByUserId(string userId)
    //{
    //    Logger.LogDebug($"Get Access Manager Detail by User Id '{userId}'");

    //    return await Mediator.Send(new GetAccessManagerByUserIdQuery { Id = userId });
    //}

    public async Task<BaseResponse> UpdateAsync(UpdateAccessManagerCommand updateAccessManagerCommand)
    {
        Logger.LogDebug($"Updating Access manager '{updateAccessManagerCommand.RoleName}'");

        return await Mediator.Send(updateAccessManagerCommand);
    }

    public async Task<GetByRoleIdVm> GetByUserRole(string roleId)
    {
        //Guard.Against.InvalidGuidOrEmpty(role, "Role");

        Logger.LogDebug($"Get User Role Detail by Id '{roleId}'");

        return await Mediator.Send(new GetByRoleQuery { Role = roleId });
    }
}