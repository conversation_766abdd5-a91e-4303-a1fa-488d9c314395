using ContinuityPatrol.Application.Features.Archive.Commands.Create;
using ContinuityPatrol.Application.Features.Archive.Commands.Delete;
using ContinuityPatrol.Application.Features.Archive.Commands.Update;
using ContinuityPatrol.Application.Features.Archive.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Archive.Queries.GetList;
using ContinuityPatrol.Application.Features.Archive.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Archive.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Archive.Queries.GetTableNameUnique;
using ContinuityPatrol.Domain.ViewModels.ArchiveModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class ArchiveService : BaseService, IArchiveService
{
    public ArchiveService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<ArchiveListVm>> GetArchiveList()
    {
        Logger.LogDebug("Get All Archives");

        return await Mediator.Send(new GetArchiveListQuery());
    }

    public async Task<ArchiveDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Archive Id");

        Logger.LogDebug($"Get Archive Detail by Id '{id}'");

        return await Mediator.Send(new GetArchiveDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateArchiveCommand createArchiveCommand)
    {
        Logger.LogDebug($"Create Archive '{createArchiveCommand}'");

        return await Mediator.Send(createArchiveCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateArchiveCommand updateArchiveCommand)
    {
        Logger.LogDebug($"Update Archive '{updateArchiveCommand}'");

        return await Mediator.Send(updateArchiveCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Archive Id");

        Logger.LogDebug($"Delete Archive Details by Id '{id}'");

        return await Mediator.Send(new DeleteArchiveCommand { Id = id });
    }

    #region NameExist

    public async Task<bool> IsArchiveNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "Archive Name");

        Logger.LogDebug($"Check Name Exists Detail by Archive Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetArchiveNameUniqueQuery { Name = name, Id = id });
    }
    public async Task<bool> IsTableNameExist(string tableName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(tableName, "Table Name");

        Logger.LogDebug($"Check Name Exists Detail by Table Name '{tableName}' and Id '{id}'");

        return await Mediator.Send(new GetTableNameUniqueQuery { TableName = tableName, Id = id });
    }
    #endregion

    #region Paginated

    public async Task<PaginatedResult<ArchiveListVm>> GetPaginatedArchives(GetArchivePaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Archive Paginated List");

        return await Mediator.Send(query);
    }

    #endregion
}