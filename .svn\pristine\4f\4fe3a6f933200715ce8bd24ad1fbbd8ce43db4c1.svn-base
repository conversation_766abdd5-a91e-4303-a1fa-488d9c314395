﻿using ContinuityPatrol.Domain.ViewModels.ServerTypeModel;

namespace ContinuityPatrol.Application.Features.ServerType.Queries.GetList;

public class GetServerTypeListQueryHandler : IRequestHandler<GetServerTypeListQuery, List<ServerTypeListVm>>
{
    private readonly IMapper _mapper;
    private readonly IServerTypeRepository _serverTypeRepository;

    public GetServerTypeListQueryHandler(IMapper mapper, IServerTypeRepository serverTypeRepository)
    {
        _mapper = mapper;
        _serverTypeRepository = serverTypeRepository;
    }

    public async Task<List<ServerTypeListVm>> Handle(GetServerTypeListQuery request,
        CancellationToken cancellationToken)
    {
        var serverTypeList = await _serverTypeRepository.ListAllAsync();

        return serverTypeList.Count == 0
            ? new List<ServerTypeListVm>()
            : _mapper.Map<List<ServerTypeListVm>>(serverTypeList);
    }
}