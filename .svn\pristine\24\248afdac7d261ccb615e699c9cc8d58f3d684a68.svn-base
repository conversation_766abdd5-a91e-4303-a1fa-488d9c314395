﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class PluginManagerFilterSpecification : Specification<PluginManager>
{
    public PluginManagerFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.Name != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("version=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Version.Contains(stringItem.Replace("version=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("description=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Description.Contains(stringItem.Replace("description=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("properties=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Properties.Contains(stringItem.Replace("properties=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("name=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.Name.Contains(searchString) || p.Version.Contains(searchString) ||
                    p.Description.Contains(searchString) || p.Properties.Contains(searchString);
            }
        }
    }
}