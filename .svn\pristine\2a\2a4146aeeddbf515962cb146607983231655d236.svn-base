﻿using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Web.Attributes;


namespace ContinuityPatrol.Web.Areas.Monitor.Controllers;

[Area("Monitor")]
public class MSSQLAlwaysOnController : Controller
{
    private readonly IDashboardViewService _dashboardViewService;
    private readonly ILogger<MSSQLAlwaysOnController> _logger;
    public MSSQLAlwaysOnController(IDashboardViewService dashboardViewService,ILogger<MSSQLAlwaysOnController> logger)
    {
        _dashboardViewService = dashboardViewService;
        _logger = logger;
    }
   
    [AntiXss]
    public IActionResult List()
    {
        return View();
    }

    [HttpGet]
    public async Task<GetByEntityIdVm> GetMonitorServiceStatusByIdAndType(string monitorId, string type)
    {

        try
        {
            return await _dashboardViewService.GetMonitorServiceStatusByIdAndType(monitorId, type);
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred while processing the request. {ex.GetMessage()}");
            return null;
        }  
    }
}

