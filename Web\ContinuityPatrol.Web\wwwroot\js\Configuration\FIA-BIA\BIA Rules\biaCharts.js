﻿function newJsonCreate(data, list, type, datas) {
    treeInfraBf = data
    let result = {}
    let businessServicsSize = "businessServicsSize", businessFunctionSize = "businessFunctionSize", infraObjectSize = "infraObjectSize", summarySize = "summarySize", detailsSize = "detailsSize"
    // Iterate through the nestedArray
    if (list == undefined || list == "") {
        result = {
            name: data?.businessServiceName,
            index: 0,
            children: [],
            size: businessServicsSize,
        }
        if (data != "") {
            if (data?.businessFunctionDataLag?.length != 0) {
                data?.businessFunctionDataLag?.forEach(function (parentItem, index) {
                    let arrayInfraStatusfunc = []
                    let flagStatusfunc = ""
                    if (parentItem.infraObjectDataLag.length != 0) {
                        parentItem?.infraObjectDataLag?.forEach(function (childItem, index) {
                            arrayInfraStatusfunc = []
                            flagStatusfunc = "Up"
                            childItem?.serverDtoVm?.forEach((Serveritem) => {
                                arrayInfraStatusfunc?.push(Serveritem)
                            })
                            childItem?.databaseDtoVm?.forEach((database) => {
                                arrayInfraStatusfunc?.push(database)
                            })
                        })
                        arrayInfraStatusfunc?.forEach((item) => {
                            if (item?.status == "Down" || item?.modeType == 'Down') {
                                flagStatusfunc = 'Down'
                            }
                        })
                        var parent = {
                            name: parentItem?.businessFunctionName,
                            alert: "true",
                            index: index,
                            size: businessFunctionSize,
                            status: flagStatusfunc,
                            children: []
                        };
                    }
                    else {
                        var parent = {
                            name: parentItem?.businessFunctionName,
                            alert: "true",
                            index: index,
                            size: businessFunctionSize,
                            status: flagStatusfunc,
                            children: []
                        };
                    }
                    if (parentItem?.infraObjectDataLag?.length != 0) {
                        let arrayInfraStatus = []
                        let flagStatus = "Up"
                        // Iterate through the children of each parent
                        parentItem?.infraObjectDataLag?.forEach(function (childItem, index) {
                            arrayInfraStatus = []
                            flagStatus = "Up"
                            childItem?.serverDtoVm?.forEach((Serveritem) => {
                                arrayInfraStatus.push(Serveritem)
                            })
                            childItem?.databaseDtoVm?.forEach((database) => {
                                arrayInfraStatus.push(database)
                            })
                            arrayInfraStatus?.forEach((item) => {
                                if (item?.status == "Down" || item?.modeType == 'Down') {
                                    flagStatus = 'Down'
                                }
                            })
                            if (childItem?.infraObjectName !== "NA") {
                                let id = ""
                                impactInfraBfId?.forEach((x, i) => {

                                    if (x == childItem?.infraObjectId) {
                                        id = impactInfraBfStatus
                                    }
                                })
                                var child = {
                                    name: childItem?.infraObjectName,
                                    alert: "true",
                                    index: index,
                                    size: infraObjectSize,
                                    status: id,
                                    children: []
                                };
                                for (var prop in childItem) {
                                    if (childItem.hasOwnProperty(prop)) {
                                        if (prop == "databaseDtoVm") {
                                            if (childItem[prop]) {
                                                childItem?.databaseDtoVm?.forEach(function (databasedtovm, index) {
                                                    if (databasedtovm?.databaseName !== "NA" && databasedtovm?.databaseName !== null && databasedtovm?.databaseName !== undefined) {
                                                        let databaseData = []
                                                        if (databasedtovm?.databaseType != "NA") {
                                                            databaseData.push({
                                                                name: databasedtovm?.databaseType,
                                                                value: databasedtovm?.databaseType,
                                                                status: databasedtovm?.modeType,
                                                                size: detailsSize,
                                                            })
                                                        }
                                                        if (databasedtovm?.oracleSID != "NA") {
                                                            databaseData.push({
                                                                name: databasedtovm?.oracleSID,
                                                                value: databasedtovm?.oracleSID,
                                                                status: databasedtovm?.modeType,
                                                                size: detailsSize,
                                                            })
                                                        }
                                                        child?.children?.push({
                                                            name: databasedtovm?.databaseName,
                                                            alert: "true",
                                                            size: summarySize,
                                                            status: databasedtovm?.modeType,
                                                            children: databaseData
                                                        })
                                                    }
                                                });
                                            }
                                        }
                                        else if (prop == "serverDtoVm") {
                                            if (childItem[prop]) {
                                                childItem?.serverDtoVm?.forEach(function (serverdtovm, index) {
                                                    if (serverdtovm?.serverName !== "NA" && serverdtovm?.serverName !== null && serverdtovm?.serverName !== undefined) {
                                                        let serverData = []
                                                        if (serverdtovm?.osType != "NA") {
                                                            serverData.push({
                                                                name: serverdtovm?.osType,
                                                                value: serverdtovm?.osType,
                                                                status: serverdtovm?.status,
                                                                size: detailsSize,
                                                            })
                                                        }
                                                        if (serverdtovm?.connectViaHostName == "False" || serverdtovm?.connectViaHostName == "NA") {
                                                            if (serverdtovm?.ipAddress != "NA") {
                                                                serverData.push({
                                                                    name: serverdtovm?.ipAddress,
                                                                    value: serverdtovm?.ipAddress,
                                                                    status: serverdtovm?.status,
                                                                    size: detailsSize,
                                                                })
                                                            }
                                                        }
                                                        else {
                                                            if (serverdtovm?.hostName != "NA") {
                                                                serverData.push({
                                                                    name: serverdtovm?.hostName,
                                                                    value: serverdtovm?.hostName,
                                                                    status: serverdtovm?.status,
                                                                    size: detailsSize,
                                                                })
                                                            }
                                                        }
                                                        child?.children?.push({
                                                            name: serverdtovm?.serverName,
                                                            alert: "false",
                                                            status: serverdtovm?.status,
                                                            size: summarySize,
                                                            children: serverData
                                                        })
                                                    }
                                                });
                                            }
                                        }
                                        else if (prop == "replicationDataLag") {

                                            if (childItem[prop]) {
                                                if (childItem[prop]?.prReplicationName != null && childItem[prop]?.prReplicationName != "NA" && childItem[prop]?.prReplicationName != undefined) {
                                                    child?.children.push({
                                                        name: childItem[prop]?.prReplicationName,
                                                        alert: "true",
                                                        status: childItem[prop]?.status,
                                                        size: summarySize,
                                                        children: [
                                                            {
                                                                name: childItem[prop]?.replicationTypeName,
                                                                value: childItem[prop]?.replicationTypeName,
                                                                status: childItem[prop]?.status,
                                                                size: detailsSize,
                                                            }
                                                        ]
                                                    })
                                                }
                                            }
                                        }

                                    }
                                }
                                parent.children.push(child);
                            }
                        });
                    }
                    result.children.push(parent);
                });
            }
        }
    } else {
        if (type == "1") {
            data?.forEach((x, i) => {
                result = {
                    name: x?.BusinessServiceName,
                    id: x?.BusinessServiceId,
                    index: 0,
                    children: [],
                    size: businessServicsSize,
                    status: x.ImpactStatus,
                }
                if (x?.propertytreedata
                    ?.businessFunctionDataLag?.length != 0) {
                    x?.propertytreedata?.businessFunctionDataLag?.forEach(function (parentItem, index) {
                        let arrayInfraStatusfunc = [], flagStatusfunc = ""
                        if (parentItem?.infraObjectDataLag?.length != 0) {
                            parentItem?.infraObjectDataLag?.forEach(function (childItem, index) {
                                arrayInfraStatusfunc = []
                                flagStatusfunc = x?.IsNotAvailableThen?.BusinessFunctionImpact
                                childItem?.serverDtoVm?.forEach((Serveritem) => {
                                    arrayInfraStatusfunc.push(Serveritem)
                                })
                                childItem?.databaseDtoVm?.forEach((database) => {
                                    arrayInfraStatusfunc.push(database)
                                })
                            })
                            var parent = {
                                name: parentItem?.businessFunctionName,
                                alert: "true",
                                index: index,
                                size: businessFunctionSize,
                                status: flagStatusfunc,
                                children: []
                            };
                        }
                        else {
                            var parent = {
                                name: parentItem?.businessFunctionName,
                                alert: "true",
                                index: index,
                                size: businessFunctionSize,
                                status: flagStatusfunc,
                                children: []
                            };
                        }
                        if (parentItem?.infraObjectDataLag?.length != 0) {
                            let arrayInfraStatus = []
                            // Iterate through the children of each parent
                            parentItem?.infraObjectDataLag?.forEach(function (childItem, index) {
                                arrayInfraStatus = []
                                flagStatus = "Up"
                                childItem?.serverDtoVm?.forEach((Serveritem) => {
                                    arrayInfraStatus.push(Serveritem)
                                })
                                childItem?.databaseDtoVm?.forEach((database) => {
                                    arrayInfraStatus.push(database)
                                })
                                arrayInfraStatus?.forEach((item) => {
                                    if (item?.status == "Down" || item?.modeType == 'Down') {
                                        flagStatus = 'Down'
                                    }
                                })
                                if (childItem?.infraObjectName !== "NA") {
                                    let id = ""
                                    impactInfraBfId?.forEach((x, i) => {

                                        if (x == childItem?.infraObjectId) {
                                            id = impactInfraBfStatus
                                        }
                                    })
                                    var child = {
                                        name: childItem?.infraObjectName,
                                        alert: "true",
                                        index: index,
                                        size: infraObjectSize,
                                        status: id,
                                        children: []
                                    };
                                    for (var prop in childItem) {
                                        if (childItem.hasOwnProperty(prop)) {
                                            if (prop == "databaseDtoVm") {
                                                if (childItem[prop]) {
                                                    childItem?.databaseDtoVm?.forEach(function (databasedtovm, index) {
                                                        if (databasedtovm?.databaseName !== "NA" && databasedtovm?.databaseName !== null && databasedtovm?.databaseName !== undefined) {
                                                            let databaseData = []
                                                            if (databasedtovm?.databaseType != "NA") {
                                                                databaseData.push({
                                                                    name: databasedtovm?.databaseType,
                                                                    value: databasedtovm?.databaseType,
                                                                    status: databasedtovm?.modeType,
                                                                    size: detailsSize,
                                                                })
                                                            }
                                                            if (databasedtovm?.oracleSID != "NA") {
                                                                databaseData.push({
                                                                    name: databasedtovm?.oracleSID,
                                                                    value: databasedtovm?.oracleSID,
                                                                    status: databasedtovm?.modeType,
                                                                    size: detailsSize,
                                                                })
                                                            }
                                                            child?.children.push({
                                                                name: databasedtovm?.databaseName,
                                                                alert: "true",
                                                                size: summarySize,
                                                                status: databasedtovm?.modeType,
                                                                children: databaseData
                                                            })
                                                        }

                                                    });
                                                }
                                            }
                                            else if (prop == "serverDtoVm") {
                                                if (childItem[prop]) {
                                                    childItem?.serverDtoVm?.forEach(function (serverdtovm, index) {

                                                        if (serverdtovm?.serverName !== "NA" && serverdtovm?.serverName !== null && serverdtovm?.serverName !== undefined) {
                                                            let serverData = []
                                                            if (serverdtovm.osType != "NA") {
                                                                serverData.push({
                                                                    name: serverdtovm?.osType,
                                                                    value: serverdtovm?.osType,
                                                                    status: serverdtovm?.status,
                                                                    size: detailsSize,
                                                                })
                                                            }
                                                            if (serverdtovm?.connectViaHostName == "False" || serverdtovm?.connectViaHostName == "NA") {
                                                                if (serverdtovm?.ipAddress != "NA") {
                                                                    serverData.push({
                                                                        name: serverdtovm?.ipAddress,
                                                                        value: serverdtovm?.ipAddress,
                                                                        status: serverdtovm?.status,
                                                                        size: detailsSize,
                                                                    })
                                                                }
                                                            }
                                                            else {
                                                                if (serverdtovm?.hostName != "NA") {
                                                                    serverData.push({
                                                                        name: serverdtovm?.hostName,
                                                                        value: serverdtovm?.hostName,
                                                                        status: serverdtovm?.status,
                                                                        size: detailsSize,
                                                                    })
                                                                }
                                                            }
                                                            child?.children.push({
                                                                name: serverdtovm?.serverName,
                                                                alert: "false",
                                                                status: serverdtovm?.status,
                                                                size: summarySize,
                                                                children: serverData
                                                            })
                                                        }
                                                    });
                                                }
                                            }
                                            else if (prop == "replicationDataLag") {
                                                if (childItem[prop]) {
                                                    if (childItem[prop]?.prReplicationName != null && childItem[prop]?.prReplicationName != "NA" && childItem[prop]?.prReplicationName != undefined) {
                                                        child?.children.push({
                                                            name: childItem[prop]?.prReplicationName,
                                                            alert: "true",
                                                            status: childItem[prop]?.status,
                                                            size: summarySize,
                                                            children: [
                                                                {
                                                                    name: childItem[prop]?.replicationTypeName,
                                                                    value: childItem[prop]?.replicationTypeName,
                                                                    status: childItem[prop]?.status,
                                                                    size: detailsSize,
                                                                }
                                                            ]
                                                        })
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    parent?.children.push(child);
                                }
                            });
                        }
                        result?.children.push(parent);
                    });
                }
            })
        } else if (type == "2" && Array.isArray(datas)) {
            let bS1 = "", bS2 = "", colorModifyData = "", arrBs1 = [], arrBs2 = []

            datas?.forEach((x, i) => {
                data?.forEach((y, i) => {
                    colorModifyData = y
                    if (y?.if?.BusinessFunctionId == x?.id) {
                        bS1 = datas?.filter(item => item?.businessServiceId === x?.businessServiceId);
                    }
                    if (y?.then?.BusinessFunctionId == x.id) {
                        bS2 = datas?.filter(item => item?.businessServiceId === x?.businessServiceId);
                    }
                })
            })
            arrBs1 = []
            arrBs2 = []
            let fixcolor = colorModifyData?.if?.BusinessFunctionImpact == "Total" ? "Total" : colorModifyData?.then?.BusinessFunctionImpact == "Total" ? "Total" : colorModifyData?.if?.BusinessFunctionImpact == "partial" ? "partial" : colorModifyData?.then?.BusinessFunctionImpact == "partial" ? "partial" : "major"
            bS1?.forEach((b1, i) => {
                bS2?.forEach((b2, i) => {
                    if (b1?.businessServiceId == b2?.businessServiceId) {
                        result = {
                            name: "",
                            index: 0,
                            size: "",
                            status: fixcolor,
                            children: [{
                                name: b1?.businessServiceName,
                                index: 0,
                                size: businessServicsSize,
                                status: fixcolor,
                                children: [{
                                    name: colorModifyData?.if?.BusinessFunctionName,
                                    index: 0,
                                    size: businessFunctionSize,
                                    status: colorModifyData?.if?.BusinessFunctionImpact,
                                }, {
                                    name: colorModifyData?.then?.BusinessFunctionName,
                                    index: 0,
                                    size: businessFunctionSize,
                                    status: colorModifyData?.then?.BusinessFunctionImpact,
                                }]
                            }]
                        }
                    } else {
                        arrBs1.push(b1)
                        arrBs2.push(b2)
                        let bs1new = [...new Set(arrBs1)]
                        let bs2new = [...new Set(arrBs2)]
                        result = {
                            name: "",
                            index: 0,
                            size: "",
                            status: fixcolor,
                            children: [{
                                name: b1?.businessServiceName,
                                index: 0,
                                size: businessServicsSize,
                                status: colorModifyData?.if?.BusinessFunctionImpact,
                                children: []
                            }, {
                                name: b2?.businessServiceName,
                                index: 0,
                                size: businessServicsSize,
                                status: colorModifyData?.then?.BusinessFunctionImpact,
                                children: []
                            }]
                        }
                        bs1new?.forEach((x, i) => {
                            result?.children[0].children.push({
                                name: x?.name,
                                index: 0,
                                size: businessFunctionSize,
                                status: colorModifyData?.if?.BusinessFunctionId == x?.id ? colorModifyData?.if?.BusinessFunctionImpact : "up",
                            })
                        })
                        bs2new?.forEach((x, i) => {
                            result?.children[1].children.push({
                                name: x?.name,
                                index: 0,
                                size: businessFunctionSize,
                                status: colorModifyData?.then?.BusinessFunctionId == x?.id ? colorModifyData?.then?.BusinessFunctionImpact : "up",
                            })
                        })
                    }
                })
            })
        } else {
            data[0]?.Is?.ByBusinessserviceProperties?.forEach((y, i) => {
                y.Impact = data[0]?.Impact
            })
            result = {
                name: data[0]?.BusinessServiceName,
                index: 0,
                size: "",
                status: data[0]?.Impact,
                children: []
            }
            if (Array.isArray(datas)) {
                datas?.forEach((x, i) => {
                    let flag = true;
                    data[0]?.Is?.ByBusinessserviceProperties?.forEach((y, i) => {
                        if (x?.id == y?.Id) {
                            result?.children.push({
                                name: x?.name,
                                index: 0,
                                size: businessServicsSize,
                                status: y?.Impact,
                            })
                            flag = false
                        }
                    })
                    if (flag) {
                        result?.children.push({
                            name: x?.name,
                            index: 0,
                            size: businessServicsSize,
                            status: "up",
                        })
                    }

                })
            }

        }
    }
    drawTree(result)
}
async function drawTree(treeData) {

    $("#biaTreeWrapper,#biaTreeWrapper1,#treewrapper2").empty()
    let treeid = "treewrapper"
    if ($("#biaNavHome").attr("class").includes("active") == true) {
        treeid = "biaTreeWrapper"
    } else if ($("#biaNavProfile").attr("class").includes("active") == true) {
        treeid = "biaTreeWrapper1"
    } else {
        treeid = "treewrapper2"
    }
    let data = {}
    data = await treeData
    // 2. Create chart dimensions
    const dimension = {
        width: 1000,
        margin: { top: 20, right: 0, bottom: 30, left: 50 },
        height: 600
    }
    if (data) {
        const root = d3.hierarchy(data);
        const dx = 50;
        const dy = (dimension.width - dimension.margin.right - dimension.margin.left) / (1 + root.height);
        // Define the tree layout and the shape for links.
        const tree = d3.tree()
            .nodeSize([dx, dy]);
        const diagonal = d3.linkHorizontal()
            .x(d => d.y)
            .y(d => d.x);
        // Define the zoom function for the zoomable tree
        const handleZoom = (e) => bounds.attr('transform', e.transform);
        const zoom = d3.zoom().on('zoom', handleZoom).scaleExtent([.5, 20])
        // Create the SVG container, a layer for the links and a layer for the nodes.
        const svg = d3.selectAll("#" + treeid + "")
            .append("svg")
            .attr("class", "D3overview")
            .attr("width", dimension.width)
            .attr("height", dx)
            .style("overflow-y", "scroll")
            .attr("viewBox", [-dimension.margin.left, -dimension.margin.top, dimension.width, dx])
            .call(zoom);

        d3.select("#" + treeid + "").select("svg")
            .attr("width", 1980)
            .attr("height", 450)
        // Add tooltips using d3-tip
        //const tip = d3.tip()
        //	.attr('class', 'd3-tip')
        //	.offset([-10, 0])

        //	.html(d => d.data.tooltip); // Set the tooltip content based on JSON data

        //svg.call(tip);
        var tooltip = d3.select("body").append("div")
            .attr("class", "tooltip")
            .style("opacity", 0);

        // Attach the tooltip to tree nodes
        var nodes = d3.selectAll(".node"); // Select your tree nodes
        nodes.on("mouseover", function (d) {
            tooltip.transition()
                .duration(200)
                .style("opacity", 0.9);
            tooltip.html(d.data.name) // Customize the content of the tooltip
                .style("left", (d3.event.pageX) + "px")
                .style("top", (d3.event.pageY - 28) + "px");
        });
        nodes.on("mouseout", function (d) {
            tooltip.transition()
                .duration(500)
                .style("opacity", 0);
        });
        //nodeSelection.on('mouseover', tip.show)
        //	.on('mouseout', tip.hide);
        //
        // Create a intrim gorup for zoom & pan
        const bounds = svg.append("g");
        const gLink = bounds.append("g")
            .attr("class", "link")
        const gNode = bounds.append("g")
            .attr("class", "node")
            .attr("pointer-events", "all");
        function update(event, source) {
            const duration = event?.altKey ? 2500 : 250; // hold the alt key to slow down the transition
            //const nodes = root.descendants().reverse();
            //const links = root.links();
            var nodes = root.descendants().filter(function (d) { return !d.hidden; }).reverse();
            const links = root.links();
            // Compute the new tree layout.
            tree(root);
            let left = root;
            let right = root;
            root.eachBefore(node => {
                if (node.x < left.x) left = node;
                if (node.x > right.x) right = node;
            });
            //links.style("stroke", "red");
            const height = right.x - left.x + dimension.margin.top + dimension.margin.bottom;
            const transition = svg.transition()
                .duration(duration)
                .attr("height", height)
                .attr("viewBox", [-dimension.margin.left, left.x - dimension.margin.top, dimension.width, height])
                .tween("resize", window.ResizeObserver ? null : () => () => svg.dispatch("toggle"));

            // Update the nodes…
            const node = gNode.selectAll("g")
                .data(nodes, d => d.id);
            // Enter any new nodes at the parent's previous position.
            const nodeEnter = node
                .enter()
                .append("g")
                .attr("transform", d => `translate(${source.y0},${source.x0})`)
                .attr("fill-opacity", 0)
                .attr("stroke-opacity", 0)
                .attr("id", d => d?.data?.size)
                .attr("class", d => d?.data?.size)
                .on("click", (event, d) => {

                    if (d.children) {
                        d._children = d.children;
                        d.children = null;
                    } else {
                        d.children = d._children;
                        d._children = null;
                    }
                    // If the node has a parent, then collapse its child nodes
                    // except for this clicked node.
                    if (d.parent) {
                        d?.parent?.children.forEach(function (element) {
                            if (d !== element) {
                                collapse(element);
                            }
                        });
                    }
                    update(null, d);
                });
            // Create links (edges)
            nodeEnter.append("circle")
                .attr('class', 'nodecircle')
                .attr("r", 0)
            // Show the clicked node
            //d3.select(this.parentNode).classed("active", true);
            nodeEnter.append("text")
                .attr("dy", d => d?.data?.size == "businessServicsSize" ? "-1em" : d?.data?.size == "businessFunctionSize" ? "2em" : d?.data?.size == "infraObjectSize" ? "-1em" : d?.data?.size == "summarySize" ? "-1em" : "-1em")
                .attr("x", d => d?.data?.size == "businessServicsSize" ? 6 : d?.data?.size == "businessFunctionSize" ? 6 : d?.data?.size == "infraObjectSize" ? 6 : d?.data?.size == "summarySize" ? 6 : -6)
                .attr("text-anchor", d => d._children ? "middle" : "middle")
                .text(d => {
                    if (d?.data?.name?.length <= 15) {
                        return d.data.name
                    }
                    else {
                        return d?.data?.name?.slice(0, 15) + "..."
                    }
                })
            // Change the circle fill depending on whether it has children and is collapsed
            nodeEnter.select("circle")
                .attr("r", 5)
                .attr('class', 'nodeCircle')
                .style("fill", function (d) {
                    if (d?.data?.status?.toLowerCase() == "up") {
                        return "#00FF00"; // Green color
                    } else if (d?.data?.status?.toLowerCase() == "total") {
                        return "#FF0000"; // Red color
                    } else if (d?.data?.status?.toLowerCase() == "major") {
                        return "#E76802"
                    } else if (d?.data?.status?.toLowerCase() == "partial") {
                        return "#ffc107"
                    } else {
                        return "#FF0000";
                    }
                });
            // Transition nodes to their new position.
            const nodeUpdate = node.merge(nodeEnter)
                .transition(transition)
                .attr("transform", d => `translate(${d.y},${d.x})`)
                .attr("fill-opacity", 1)
                .attr("stroke-opacity", 1);
            nodeUpdate.each(function (d) {
                if (d.data.ipaddress !== undefined) {
                    let titleText = `IP Address: ${d.data.ipaddress}`;
                    if (d.data.ostype !== undefined) {
                        titleText += `\nOS Type: ${d.data.ostype}`;
                    }
                    d3.select(this).append("title")
                        .text(titleText);
                }
            });
            nodeEnter.append("title")
                .text(d => d.data.name);
            // Transition exiting nodes to the parent's new position.
            const nodeExit = node.exit()
                .transition(transition)
                .remove()
                .attr("transform", d => `translate(${source.y},${source.x})`)
                .attr("fill-opacity", 0)
                .attr("stroke-opacity", 0);
            // Update the links…
            const link = gLink.selectAll("path")
                .data(links, d => d.target.id)
            // Enter any new links at the parent's previous position.
            const linkEnter = link.enter()
                .append("path")
                .attr("id", d => d?.data?.size)
                .attr("class", d => d?.data?.size)
                .attr("d", d => {
                    const o = {
                        x: source.x0,
                        y: source.y0
                    };
                    return diagonal({
                        source: o,
                        target: o
                    });
                })

            // Transition links to their new position.
            link.merge(linkEnter)
                .transition(transition)
                .attr("d", diagonal)
                .attr("stroke", function (d) {
                    if (d?.target?.data?.status?.toLowerCase() === "up") {
                        return "#00FF00"; // Green color 
                    } else if (d?.target?.data?.status?.toLowerCase() === "total") {
                        return "#FF0000"; // Red color 
                    } else if (d?.target?.data?.status?.toLowerCase() === "major") {
                        return "#E76802"
                    } else if (d?.target?.data?.status?.toLowerCase() === "partial") {
                        return "#ffc107"
                    } else {
                        return "#FF0000";
                    }
                })
                .attr("id", d => { return d?.data?.size })
                .attr("class", d => { return d?.data?.size })
            // Transition exiting nodes to the parent's new position.
            link.exit()
                .transition(transition)
                .remove()
                .attr("d", d => {
                    const o = {
                        x: source.x,
                        y: source.y
                    };
                    return diagonal({
                        source: o,
                        target: o
                    });
                });
            // Stash the old positions for transition.
            root.eachBefore(d => {
                d.x0 = d.x;
                d.y0 = d.y;
            });
        }
        root.x0 = dy / 2;
        root.y0 = 0;
        root.descendants().forEach((d, i) => {
            d.id = i;
            d._children = d.children;
            if (d.data?.index != 0) {
                d.children = null;
            }
        });
        update(null, root);
    }
}
function collapse(d) {
    if (d.children) {
        d._children = d.children;
        d._children.forEach(collapse);
        d.children = null;
    }
}