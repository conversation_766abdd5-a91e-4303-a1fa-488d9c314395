﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class AlertRepositoryMocks
{
    public static Mock<IAlertRepository> CreateAlertRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        alertRepository.Setup(repo => repo.AddAsync(It.IsAny<Alert>())).ReturnsAsync(
            (Alert alert) =>
            {
                alert.Id = new Fixture().Create<int>();

                alert.ReferenceId = new Fixture().Create<Guid>().ToString();

                alerts.Add(alert);

                return alert;
            });
        return alertRepository;
    }

    public static Mock<IAlertRepository> UpdateAlertRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        alertRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alerts.SingleOrDefault(x => x.ReferenceId == i));

        alertRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Alert>())).ReturnsAsync((Alert alert) =>
        {
            var index = alerts.FindIndex(item => item.ReferenceId == alert.ReferenceId);

            alerts[index] = alert;

            return alert;

        });
        return alertRepository;
    }

    public static Mock<IAlertRepository> DeleteAlertRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        alertRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alerts.SingleOrDefault(x => x.ReferenceId == i));

        alertRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Alert>())).ReturnsAsync(
            (Alert alert) =>
            {
                var index = alerts.FindIndex(item => item.ReferenceId == alert.ReferenceId);

                alert.IsActive = false;

                alerts[index] = alert;

                return alert;
            });
        return alertRepository;
    }

    public static Mock<IAlertRepository> GetAlertRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        alertRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alerts.SingleOrDefault(x => x.ReferenceId == i));

        alertRepository.Setup(repo=>repo.GetAlertByUserLastAlertId(It.IsAny<int>() )).ReturnsAsync((int i) => alerts.Where(x=>x.Id == i).ToList());

        return alertRepository;
    }

    public static Mock<IAlertRepository> GetAlertListByStartOfWeekRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        return alertRepository;
    }

    public static Mock<IAlertRepository> GetAlertListFilterByDateRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.GetAlertListFilterByDate(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(alerts);

        return alertRepository;
    }

    public static Mock<IAlertRepository> GetAlertByInfraObjectIdRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.GetAlertByInfraObjectId(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => alerts.Where(x => x.InfraObjectId == i && x.EntityId == j).ToList());

        return alertRepository;
    }

    public static Mock<IAlertRepository> GetLastAlertDetailRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alerts.SingleOrDefault(x => x.ReferenceId == i));

        alertRepository.Setup(repo => repo.GetAlertByUserLastAlertId( It.IsAny<int>())).ReturnsAsync((int i) => alerts.Where(x => x.Id == i).ToList());

        alertRepository.Setup(repo => repo.GetAlertByUserLastInfraObjectId(It.IsAny<string>(), It.IsAny<DateTime>())).ReturnsAsync((string i, DateTime j) => alerts.Where(x => x.InfraObjectId == i && x.CreatedDate == j).ToList());

        return alertRepository;
    }

    public static Mock<IAlertRepository> GetAlertEmptyRepository()
    {
        var alertEmptyRepository = new Mock<IAlertRepository>();

        alertEmptyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Alert>());

        return alertEmptyRepository;
    }

    public static Mock<IAlertRepository> GetPaginatedAlertRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        var queryableAccessManager = alerts.BuildMock();

        alertRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableAccessManager);

        return alertRepository;
    }

    public static Mock<IAlertRepository> GetAlertByClientAlertIdRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.GetAlertByClientAlertId(It.IsAny<string>())).ReturnsAsync(alerts);

        return alertRepository;
    }


}