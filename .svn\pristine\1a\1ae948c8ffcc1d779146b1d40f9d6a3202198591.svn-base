﻿namespace ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterByAlertName;

public class AlertMasterByAlertNameVm
{
    public string Id { get; set; }
    public string AlertId { get; set; }
    public int AlertType { get; set; }
    public string AlertMessage { get; set; }
    public string RecoveryId { get; set; }
    public string AlertPriority { get; set; }
    public int AlertFrequency { get; set; }
    public string AlertSendTime { get; set; }
    public bool IsSendEmail { get; set; }
    public bool IsSendSMS { get; set; }
    public string SmSMessage { get; set; }
    public string AlertName { get; set; }
    public string EscMatId { get; set; }
    public bool IsAcknowledgement { get; set; }
    public string ExceptionId { get; set; }
    public bool IsAlertActive { get; set; }
    public int DBType { get; set; }
    public bool IsFullDB { get; set; }
}