﻿@model ContinuityPatrol.Domain.ViewModels.GlobalSetting.GlobalSettingModel
@using ContinuityPatrol.Shared.Services.Helper
@using ContinuityPatrol.Shared.Core.Helper;
@Html.AntiForgeryToken()

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-configure-settings"></i><span>Global Settings</span></h6>
        </div>
        <div class="card-body card_ScrollBody">
            <div class="row row-cols-3 g-3">
                <div class="col d-grid">
                    <div class="card border mb-0">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <i class="cp-lock me-2 fs-5"></i>
                                <span class="card-title flex-fill" data-checkbox-id="chk-authentication">Two Factor Authentication</span>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="Two Factor Authentication" id="chk-authentication" onchange="handleChange(event, 'txtId')" style="width:2.3rem; height:1.2rem;">
                                </div>
                            </div>
                            <input type="hidden" id="txtId" class="form-control" />
                            <span class="text-light" title="Two-factor authentication (2FA) is a security process in which users provide two different authentication factors to verify themselves. SMS-based 2FA: A code is sent to the user's phone via SMS.">
                                Two-factor authentication (2FA) is a security process in which users provide two different authentication factors to verify themselves.
                                SMS-based 2FA: A code is sent to the user's phone via SMS.
                            </span>
                        </div>
                    </div>
                </div>

                <div class="col d-grid">
                    <div class="card border mb-0">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <i class="cp-dr-calendar me-2 fs-5"></i>
                                <span class="card-title flex-fill" data-checkbox-id="chk-dr">DR Calendar</span>
                                <div class="form-check form-switch">
                                    <input name="DR Calendar" class="form-check-input" type="checkbox" id="chk-dr" onchange="handleChange(event, 'txtDRId')" style="width:2.3rem; height:1.2rem;">
                                </div>
                            </div>
                            <input type="hidden" id="txtDRId" class="form-control" />
                            <span class="text-light" title="TA DR drill activity can be marked in the DR calendar, and it will remind you and your stakeholders.">
                                A DR drill activity can be marked in the DR calendar, and it will remind you and your stakeholders.
                            </span>
                        </div>
                    </div>
                </div>

                @* <div class="col">
                <div class="card border mb-0">
                <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                <i class="cp-notification me-2 fs-5"></i>
                <span class="card-title flex-fill">Notification</span>
                <div class="form-check form-switch">
                <input name="Notification" class="form-check-input" type="checkbox" id="chk-notification" checked onchange="handleChange(event, 'txtNotificationId')" style="width:2.3rem; height:1.2rem;">
                </div>
                </div>
                <input type="hidden" id="txtNotificationId" class="form-control" />
                <span class="text-light" title="The standard Lorem Ipsum passage is sit amet
                adipiscing passage">
                The standard Lorem Ipsum passage is sit amet
                adipiscing passage.
                </span>
                </div>
                </div>
                </div> *@

                <div class="col d-grid">
                    <div class="card border mb-0">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <i class="cp-chat me-2 fs-5"></i>
                                <span class="card-title flex-fill">SMS Notification</span>
                                <div class="form-check form-switch">
                                    <input name="SMS Notification" class="form-check-input" type="checkbox" id="chk-sms"
                                           style="width:2.3rem; height:1.2rem;" onchange="handleChange(event, 'txtSMSId')">
                                </div>
                            </div>
                            <input type="hidden" id="txtSMSId" class="form-control" />
                            <span class="text-light" title="To send SMS notification to the configured stakeholders.">
                                To send SMS notification to the configured stakeholders.
                            </span>
                        </div>
                    </div>
                </div>

                <div class="col d-grid">
                    <div class="card border mb-0">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <i class="cp-email me-2 fs-5"></i>
                                <span class="card-title flex-fill">Email Notification</span>
                                <div class="form-check form-switch">
                                    <input name="Email Notification" class="form-check-input" type="checkbox" id="chk-email" onchange="handleChange(event, 'txtEmailId')" style="width:2.3rem; height:1.2rem;">
                                </div>
                            </div>
                            <input type="hidden" id="txtEmailId" class="form-control" />
                            <span class="text-light" title="To send an email notification to the configured stakeholders.">
                                To send an email notification to the configured stakeholders.
                            </span>
                        </div>
                    </div>
                </div>

                <div class="col d-grid">

                    <div class="card border mb-0">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <i class="cp-bulk-import me-2 fs-5"></i>
                                <span class="card-title flex-fill">Bulk Import</span>
                                <div class="form-check form-switch">
                                    <input name="Bulk Import" class="form-check-input" type="checkbox" id="chk-import" checked onchange="handleChange(event, 'txtImportId')" style="width:2.3rem; height:1.2rem;">
                                </div>
                            </div>
                            <span class="text-light">
                                To enable the bulk import functionality in Continuity Patrol
                            </span>
                            <input type="hidden" id="txtImportId" class="form-control" />
                        </div>
                    </div>

                </div>
                <div class="col d-grid">
                    <div class="card border mb-0">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <i class="cp-approval-matrix me-2 fs-5"></i>
                                <span class="card-title flex-fill">Approval Matrix</span>
                                <div class="form-check form-switch">
                                    <input name="Approval Matrix" class="form-check-input" type="checkbox" id="chk-matrix" checked onchange="handleChange(event, 'txtMatrixId')" style="width:2.3rem; height:1.2rem;">
                                </div>
                            </div>
                            <span class="text-light">
                                To enable the approval matrix is to establish a process for obtaining approval to modify workflow and profile execution according to the organization's hierarchy.
                            </span>
                            <input type="hidden" id="txtMatrixId" class="form-control" />
                        </div>
                    </div>

                </div>
                <div class="col d-grid">
                    <div class="card border mb-0">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <i class="cp-escalation_matrix_header-icon-3 me-2 fs-5"></i>
                                <span class="card-title flex-fill">Escalation Matrix</span>
                                <div class="form-check form-switch">
                                    <input name="Escalation Matrix" class="form-check-input" type="checkbox" id="chk-escalation" checked onchange="handleChange(event, 'txtEscalationId')" style="width:2.3rem; height:1.2rem;">
                                </div>
                            </div>
                            <span class="text-light">
                                To activate the escalation matrix process for notifying and escalating according to the organization's hierarchy.
                            </span>
                            <input type="hidden" id="txtEscalationId" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="col d-grid" >
                    <div class="card border mb-0" id="secretKeyDiv">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <i class="cp-key me-2 fs-5"></i>
                                <span class="card-title flex-fill">Secret Key</span>
                                @* <div class="form-check form-switch">
                                <input name="SecurityKey" class="form-check-input" type="checkbox" id="chk-password" checked onchange="handleChange(event, 'txtPasswordId')" style="width:2.3rem; height:1.2rem;">
                                </div> *@
                            </div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-lock" title="Lock"></i></span>
                                <input type="password" autocomplete="off" class="form-control" placeholder="Enter Secret Key   " id="txtPassword" />
                                @* <span role="button" class="input-group-text toggle-password"><i class="cp-password-visible fs-6"></i></span> *@
                                <span class="input-group-text copy-password"><i class="cp-copy" title="Copy"></i></span>
                            </div>
                            <input type="hidden" id="txtPasswordId" class="form-control" />
                        </div>
                    </div>
                </div>
                <input type="hidden" id="txtUserRole" data-role="@WebHelper.UserSession.RoleName" class="form-control" />
                <input type="hidden" id="txtLoginUser" data-login="@WebHelper.UserSession.LoggedUserId" class="form-control" />
            </div>
        </div>
    </div>
</div>
<div id="adminCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.CreateAndEdit" aria-hidden="true"></div>
@*alertNotification*@
<div class='Notification'>
    <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
        <div class='d-flex'>
            <div class='toast-body'>
                <span id="alertClass" class='{ClassName}-toast'>
                    <i id="icon_Detail" class=''></i>
                </span>
                <span id="notificationAlertmessage">

                </span>
            </div>
            <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
        </div>
    </div>
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Admin/GlobalSettings/GlobalSetting.js"></script>


