using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MYSQLMonitorStatusFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "MYSQLMonitorStatus";

    public List<MYSQLMonitorStatus> MYSQLMonitorStatusPaginationList { get; set; }
    public List<MYSQLMonitorStatus> MYSQLMonitorStatusList { get; set; }
    public MYSQLMonitorStatus MYSQLMonitorStatusDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MYSQLMonitorStatusFixture()
    {
        _fixture = new Fixture();
        
        // Configure AutoFixture to generate valid data
        _fixture.Customize<MYSQLMonitorStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            );

        MYSQLMonitorStatusPaginationList = _fixture.CreateMany<MYSQLMonitorStatus>(20).ToList();
        MYSQLMonitorStatusList = _fixture.CreateMany<MYSQLMonitorStatus>(5).ToList();
        MYSQLMonitorStatusDto = _fixture.Create<MYSQLMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MYSQLMonitorStatus CreateMYSQLMonitorStatusWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<MYSQLMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
     
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public MYSQLMonitorStatus CreateMYSQLMonitorStatusWithWhitespace()
    {
        return CreateMYSQLMonitorStatusWithProperties(type: "  MYSQLMonitorStatus  ");
    }

    public MYSQLMonitorStatus CreateMYSQLMonitorStatusWithLongType(int length)
    {
        var longType = new string('A', length);
        return CreateMYSQLMonitorStatusWithProperties(type: longType);
    }

    public MYSQLMonitorStatus CreateMYSQLMonitorStatusWithSpecificInfraObjectId(string infraObjectId)
    {
        return CreateMYSQLMonitorStatusWithProperties(infraObjectId: infraObjectId);
    }

    public List<MYSQLMonitorStatus> CreateMultipleMYSQLMonitorStatusWithSameType(string type, int count)
    {
        var statuses = new List<MYSQLMonitorStatus>();
        for (int i = 0; i < count; i++)
        {
            statuses.Add(CreateMYSQLMonitorStatusWithProperties(type: type, isActive: true));
        }
        return statuses;
    }

    public List<MYSQLMonitorStatus> CreateMYSQLMonitorStatusWithMixedActiveStatus(string type)
    {
        return new List<MYSQLMonitorStatus>
        {
            CreateMYSQLMonitorStatusWithProperties(type: type, isActive: true),
            CreateMYSQLMonitorStatusWithProperties(type: type, isActive: false),
            CreateMYSQLMonitorStatusWithProperties(type: type, isActive: true)
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "MYSQLMonitorStatus", "MySQL", "MySQLServer", "DatabaseMonitorStatus" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
        public static readonly string[] SpecialCharacterTypes = { "Type@#$%", "Type with spaces", "Type_with_underscores", "Type-with-dashes" };
    }
}
