using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BiaRules.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.BiaRules.Events;

public class BiaRulesDeletedEventTests : IClassFixture<BiaRulesFixture>
{
    private readonly BiaRulesFixture _biaRulesFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Mock<ILogger<BiaRulesDeletedEventHandler>> _mockLogger;
    private readonly BiaRulesDeletedEventHandler _handler;

    public BiaRulesDeletedEventTests(BiaRulesFixture biaRulesFixture)
    {
        _biaRulesFixture = biaRulesFixture;
        _mockUserActivityRepository = BiaRulesRepositoryMocks.CreateUserActivityRepository(_biaRulesFixture.UserActivities);
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<BiaRulesDeletedEventHandler>>();

        // Setup logged in user service
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/biarules");
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        _handler = new BiaRulesDeletedEventHandler(
            _mockLoggedInUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_When_BiaRulesDeletedEventReceived()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCorrectUserActivity_When_RTOBiaRulesDeleted()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == _mockLoggedInUserService.Object.UserId &&
            ua.LoginName == _mockLoggedInUserService.Object.LoginName &&
            ua.RequestUrl == _mockLoggedInUserService.Object.RequestedUrl &&
            ua.HostAddress == _mockLoggedInUserService.Object.IpAddress &&
            ua.Action == $"{ActivityType.Delete} {Modules.BiaRules}" &&
            ua.Entity == Modules.BiaRules.ToString() &&
            ua.ActivityType == ActivityType.Delete.ToString() &&
            ua.ActivityDetails == "BiaRules 'RTO' deleted successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCorrectUserActivity_When_RPOBiaRulesDeleted()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = "RPO" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BiaRules 'RPO' deleted successfully." &&
            ua.Action == $"{ActivityType.Delete} {Modules.BiaRules}" &&
            ua.ActivityType == ActivityType.Delete.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectUserInfo_When_BiaRulesDeleted()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var loginName = "DeleteTestUser";
        var requestUrl = "/api/v6/biarules/delete";
        var ipAddress = "192.168.1.300";

        _mockLoggedInUserService.Setup(x => x.UserId).Returns(userId);
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns(loginName);
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(requestUrl);
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns(ipAddress);

        var deletedEvent = new BiaRulesDeletedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == userId &&
            ua.LoginName == loginName &&
            ua.RequestUrl == requestUrl &&
            ua.HostAddress == ipAddress)), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_BiaRulesDeleted()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = "Custom Deleted Rule" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BiaRules 'Custom Deleted Rule' deleted successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectEntityAndModule_When_BiaRulesDeleted()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.Entity == Modules.BiaRules.ToString() &&
            ua.Action.Contains(Modules.BiaRules.ToString()))), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityType_When_BiaRulesDeleted()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityType == ActivityType.Delete.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_LogInformation_When_BiaRulesDeleted()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

      
       
    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_BiaRulesDeletedWithNullName()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = null };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BiaRules '' deleted successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleEmptyEventName_When_BiaRulesDeletedWithEmptyName()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = string.Empty };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BiaRules '' deleted successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_CallRepositoryOnce_When_EventHandled()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        _mockUserActivityRepository.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_CreateEventWithComplexRuleName_When_BiaRulesDeleted()
    {
        // Arrange
        var complexRuleName = "Deleted Complex RTO Rule with Special Characters & Numbers 789";
        var deletedEvent = new BiaRulesDeletedEvent { Name = complexRuleName };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == $"BiaRules '{complexRuleName}' deleted successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_VerifyEventType_When_BiaRulesDeleted()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        deletedEvent.ShouldBeOfType<BiaRulesDeletedEvent>();
        deletedEvent.ShouldBeAssignableTo<INotification>();
    }

    [Fact]
    public async Task Handle_DifferentiateFromOtherEvents_When_BiaRulesDeleted()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityType == ActivityType.Delete.ToString() &&
            ua.ActivityDetails.Contains("deleted") &&
            !ua.ActivityDetails.Contains("created") &&
            !ua.ActivityDetails.Contains("updated"))), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActionFormat_When_BiaRulesDeleted()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.Action == $"{ActivityType.Delete} {Modules.BiaRules}")), Times.Once);
    }

    [Fact]
    public async Task Handle_PerformSoftDelete_When_BiaRulesDeleted()
    {
        // Arrange
        var deletedEvent = new BiaRulesDeletedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        // Verify that the event handler logs the soft delete operation
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails.Contains("deleted successfully") &&
            ua.ActivityType == ActivityType.Delete.ToString())), Times.Once);
    }
}
