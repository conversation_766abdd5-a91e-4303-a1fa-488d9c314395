﻿using ContinuityPatrol.Application.Features.SiteLocation.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SiteLocationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.SiteLocation.Queries
{
    public class GetSiteLocationPaginatedListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISiteLocationRepository> _mockSiteLocationRepository;
        private readonly GetSiteLocationPaginatedListQueryHandler _handler;

        public GetSiteLocationPaginatedListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSiteLocationRepository = new Mock<ISiteLocationRepository>();
            _handler = new GetSiteLocationPaginatedListQueryHandler(_mockMapper.Object, _mockSiteLocationRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedResult_WhenDataExists()
        {
            var query = new GetSiteLocationPaginatedListQuery
            {
                SearchString = "Test",
                PageNumber = 1,
                PageSize = 2
            };

            var siteLocations = new List<Domain.Entities.SiteLocation>
            {
                new Domain.Entities.SiteLocation { Id = 1, City = "Chennai" },
                new Domain.Entities.SiteLocation { Id = 2, Country = "India" }
            }.AsQueryable();

            var paginatedResult = new PaginatedResult<Domain.Entities.SiteLocation>
            {
                Data = siteLocations.ToList(),
                TotalPages = query.PageNumber,
                PageSize = query.PageSize,
                TotalCount = siteLocations.Count()
            };

            var mappedResult = siteLocations.Select(x => new SiteLocationListVm { Id = x.Lat, City = x.Country }).ToList();

            _mockSiteLocationRepository.Setup(repo => repo.PaginatedListAllAsync())
                .Returns(siteLocations);

            _mockMapper.Setup(mapper => mapper.Map<SiteLocationListVm>(It.IsAny<Domain.Entities.SiteLocation>()))
                .Returns<Domain.Entities.SiteLocation>(site => new SiteLocationListVm { City = site.Country, CityAscii = site.Lat });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(query.PageSize, result.PageSize);
            Assert.Equal(query.PageNumber, result.TotalPages);
            Assert.Equal(siteLocations.Count(), result.Data.Count);
            _mockSiteLocationRepository.Verify(repo => repo.PaginatedListAllAsync(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyPaginatedResult_WhenNoDataExists()
        {
            var query = new GetSiteLocationPaginatedListQuery
            {
                SearchString = "NonExisting",
                PageNumber = 1,
                PageSize = 10
            };

            var siteLocations = new List<Domain.Entities.SiteLocation>().AsQueryable();

            _mockSiteLocationRepository.Setup(repo => repo.PaginatedListAllAsync())
                .Returns(siteLocations);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);
            Assert.Equal(query.PageNumber, result.TotalPages);
            Assert.Equal(query.PageSize, result.PageSize);
            _mockSiteLocationRepository.Verify(repo => repo.PaginatedListAllAsync(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldFilterResults_WhenSearchStringIsProvided()
        {
            var query = new GetSiteLocationPaginatedListQuery
            {
                SearchString = "Chennai",
                PageNumber = 1,
                PageSize = 5
            };

            var siteLocations = new List<Domain.Entities.SiteLocation>
            {
                 new Domain.Entities.SiteLocation { Id = 1, City = "Chennai" },
                 new Domain.Entities.SiteLocation { Id = 2, Country = "Country" }
            }.AsQueryable();

            _mockSiteLocationRepository.Setup(repo => repo.PaginatedListAllAsync())
                .Returns(siteLocations);

            var filteredLocations = siteLocations.Where(x => x.City.Contains(query.SearchString)).ToList();

            _mockMapper.Setup(mapper => mapper.Map<SiteLocationListVm>(It.IsAny<Domain.Entities.SiteLocation>()))
                .Returns<Domain.Entities.SiteLocation>(site => new SiteLocationListVm { Id = site.Lat, City = site.Country });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result.Data);
            Assert.Equal("Chennai", result.Data.First().City);
            _mockSiteLocationRepository.Verify(repo => repo.PaginatedListAllAsync(), Times.Once);
        }
    }
}
