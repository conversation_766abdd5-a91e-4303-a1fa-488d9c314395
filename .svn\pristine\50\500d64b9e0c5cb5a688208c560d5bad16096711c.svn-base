using ContinuityPatrol.Application.Features.RsyncOption.Commands.Create;
using ContinuityPatrol.Application.Features.RsyncOption.Commands.Update;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.RsyncOptionModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class RsyncOptionProfile : Profile
{
    public RsyncOptionProfile()
    {
        CreateMap<RsyncOption, RsyncOptionListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<RsyncOption, RsyncOptionDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<RsyncOption, CreateRsyncOptionCommand>().ReverseMap();
        CreateMap<RsyncOption, RsyncOptionViewModel>().ReverseMap();

        CreateMap<CreateRsyncOptionCommand, RsyncOptionViewModel>().ReverseMap();
        CreateMap<UpdateRsyncOptionCommand, RsyncOptionViewModel>().ReverseMap();

        CreateMap<UpdateRsyncOptionCommand, RsyncOption>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<PaginatedResult<RsyncOption>,PaginatedResult<RsyncOptionListVm>>()
             .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));

    }
}