﻿using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyExecutionReport;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Queries
{
    public class GetDRReadyExecutionReportQueryHandlerTests
    {
        private readonly Mock<IDrReadyLogRepository> _mockDrReadyLogRepository;
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly GetDRReadyExecutionReportQueryHandler _handler;

        public GetDRReadyExecutionReportQueryHandlerTests()
        {
            _mockDrReadyLogRepository = new Mock<IDrReadyLogRepository>();
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();
            _mockMapper = new Mock<IMapper>();
            _mockPublisher = new Mock<IPublisher>();

            _handler = new GetDRReadyExecutionReportQueryHandler(
                _mockMapper.Object,
                _mockDrReadyLogRepository.Object,
                _mockLoggedInUserService.Object,
                _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_WithValidBusinessServiceId_ReturnsCorrectReport()
        {
            var request = new GetDRReadyExecutionReportQuery
            {
                BusinessServiceId = "123",
                StartTime = "2024-12-01",
                EndTime = "2024-12-10"
            };

            var drReadyLogs = new List<Domain.Entities.DRReadyLog>
            {
                new Domain.Entities.DRReadyLog { CreatedDate = DateTime.Parse("2024-12-05") },
                new Domain.Entities.DRReadyLog { CreatedDate = DateTime.Parse("2024-12-07") }
            };

            var drReadyReportVms = new List<DRReadyExecutionReportVm>
            {
                new DRReadyExecutionReportVm {  },
                new DRReadyExecutionReportVm {  }
            };

            _mockDrReadyLogRepository
                .Setup(repo => repo.GetDrReadyLogListByStartTimeAndEndTime(
                    request.StartTime, request.EndTime, request.BusinessServiceId))
                .ReturnsAsync(drReadyLogs);

            _mockMapper
                .Setup(mapper => mapper.Map<List<DRReadyExecutionReportVm>>(drReadyLogs))
                .Returns(drReadyReportVms);

            _mockLoggedInUserService
                .Setup(service => service.LoginName)
                .Returns("TestUser");

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("TestUser", result.ReportGeneratedBy);
            Assert.NotNull(result.DRReadyExecutionReportVm);
            Assert.Equal(2, result.DRReadyExecutionReportVm.Count);

            _mockPublisher.Verify(
                pub => pub.Publish(It.IsAny<ReportViewedEvent>(), CancellationToken.None),
                Times.Once);
        }

        [Fact]
        public async Task Handle_WithoutBusinessServiceId_ReturnsFilteredReport()
        {
            var request = new GetDRReadyExecutionReportQuery
            {
                BusinessServiceId = null,
                StartTime = "2024-12-01",
                EndTime = "2024-12-10"
            };

            var allDrReadyLogs = new List<Domain.Entities.DRReadyLog>
            {
                new Domain.Entities.DRReadyLog { CreatedDate = DateTime.Parse("2024-12-05") },
                new Domain.Entities.DRReadyLog { CreatedDate = DateTime.Parse("2024-12-15") }
            };

            var filteredLogs = allDrReadyLogs.Where(x =>
                x.CreatedDate.Date >= DateTime.Parse(request.StartTime) &&
                x.CreatedDate.Date <= DateTime.Parse(request.EndTime)).ToList();

            var drReadyReportVms = new List<DRReadyExecutionReportVm>
            {
                new DRReadyExecutionReportVm {  }
            };

            _mockDrReadyLogRepository
                .Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(allDrReadyLogs);

            _mockMapper
                .Setup(mapper => mapper.Map<List<DRReadyExecutionReportVm>>(filteredLogs))
                .Returns(drReadyReportVms);

            _mockLoggedInUserService
                .Setup(service => service.LoginName)
                .Returns("TestUser");

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("TestUser", result.ReportGeneratedBy);
            Assert.NotNull(result.DRReadyExecutionReportVm);
            Assert.Single(result.DRReadyExecutionReportVm);

            _mockPublisher.Verify(
                pub => pub.Publish(It.IsAny<ReportViewedEvent>(), CancellationToken.None),
                Times.Once);
        }
    }
}
