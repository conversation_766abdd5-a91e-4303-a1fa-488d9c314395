﻿using ContinuityPatrol.Application.Features.DRCalendar.Commands.Create;
using ContinuityPatrol.Application.Features.TeamResource.Commands.Create;
using ContinuityPatrol.Application.Features.TeamResource.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.TeamResourceModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.Api.Impl.Configuration
{
    public class TeamResourceService : BaseClient, ITeamResourceService
    {
        public TeamResourceService(IConfiguration config, IAppCache cache, ILogger<TeamResourceService> logger)
        : base(config, cache, logger)
        {
        }

       public async Task<BaseResponse>  CreateAsync(CreateTeamResourceCommand team)
        {
            var request = new RestRequest("api/v6/TeamResourceService", Method.Post);

            request.AddJsonBody(team);

            return await Post<BaseResponse>(request);
        }

       public async Task<BaseResponse>  DeleteAsync(string TeamId)
        {
            var request = new RestRequest($"api/v6/TeamResourceService/{TeamId}", Method.Delete);

            return await Delete<BaseResponse>(request);
        }

        Task<List<TeamResourceListVm>> ITeamResourceService.GetTeamMasterIdByTeamMember(string teamMasterId)
        {
            throw new NotImplementedException();
        }

        Task<PaginatedResult<TeamResourceListVm>> ITeamResourceService.GetTeamMemberList(GetTeamResourcePaginatedListQuery query)
        {
            throw new NotImplementedException();
        }

        Task<PaginatedResult<TeamResourceListVm>> ITeamResourceService.GetTeamMemberListAll(string query)
        {
            throw new NotImplementedException();
        }

        Task<List<TeamResourceListVm>> ITeamResourceService.GetTeamMemberNames()
        {
            throw new NotImplementedException();
        }

        Task<bool> ITeamResourceService.IsTeamMemberNameExist(string teamMasterName, string id)
        {
            throw new NotImplementedException();
        }

        Task<bool> ITeamResourceService.IsTeamMemberNameUnique(string teamMasterName)
        {
            throw new NotImplementedException();
        }
    }
}
