using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class GlobalVariableRepository : BaseRepository<GlobalVariable>, IGlobalVariableRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public GlobalVariableRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
public Task<bool> IsNameExist(string name, string id)
{
    return Task.FromResult(!id.IsValidGuid()
        ? Entities.Any(e => e.VariableName.Equals(name))
        : Entities.Where(e => e.VariableName.Equals(name)).ToList().Unique(id));
}

    public async Task<List<GlobalVariable>> GetByVariableName(string name)
    {
        return await _dbContext.GlobalVariables.Active().AsNoTracking().Where(x => x.VariableName.Trim().ToLower().Equals(name)).ToListAsync();
    }
}
