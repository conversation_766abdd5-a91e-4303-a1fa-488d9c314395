﻿@model ContinuityPatrol.Domain.ViewModels.MonitorServicesListModel.MonitorServiceListViewModel
@Html.AntiForgeryToken()

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<style>
    .dataTables_length label {
        display: flex !important;
        align-items: center !important;
    }
</style>
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-monitoring"></i><span>Monitoring Services</span></h6>
            <form class="d-flex">
                <div class="row g-3 align-items-center">
                    <div class="col-auto">
                        <label class="form-label mb-0">Operational Service : </label>
                    </div>
                    <div class="col-auto">
                        <div class="input-group me-2 " style="width:200px">
                            <span class="input-group-text"><i class="cp-business-service"></i></span>
                            <select asp-for="BusinessServiceName" data-placeholder="Select Operational Service" class="form-select" aria-label="Default select example" id="listMSOperationService">
                                <option>All</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-auto">
                        <label class="form-label mb-0">InfraObject : </label>
                    </div>
                    <div class="col-auto">
                        <div class="input-group me-2" style="width:200px">
                            <span class="input-group-text"><i class="cp-infra-object"></i></span>
                            <select class="form-select" aria-label="Default select example" id="listMSInfra" data-placeholder="Select InfraObject">
                            </select>
                        </div>
                    </div>
                </div>
                <div class="input-group me-2 w-auto">
                    <input type="search" id="msSearch" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="businessservicename=" id="filterBusinessServiceName">
                                        <label class="form-check-label" for="BusinessServiceName">
                                            Operational Service Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="infraobjectname=" id="filterInfraObjectName">
                                        <label class="form-check-label" for="InfraObjectName">
                                            InfraObject Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="servername=" id="filterServerName">
                                        <label class="form-check-label" for="ServerName">
                                            Server Name
                                        </label>
                                    </div>
                                </li>

                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm" id="btnMonitoringServiceCreate"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <div id="collapetable">
                <table id="tblMoniterService" class="datatable table table-hover dataTable no-footer" style="width:100%">
                    <thead>
                        <tr>
                            <th class="Sr_No">Sr.No</th>
                            <th>Operational Service</th>
                            <th>InfraObject</th>
                            <th>Server</th>
                            <th>Authentication Type</th>
                            <th title="Service / Process / Workflow">Service / Process / Workflow </th>
                            <th>Last Monitored Time</th>
                            @*         <th>Service Status</th> *@
                            <th class="">Job Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div id="manageMSCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.CreateAndEdit" aria-hidden="true"></div>
<div id="manageMSDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.Delete" aria-hidden="true"></div>

<!--Modal Create-->
<div class="modal fade" id="createMSModal" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>

<!--Modal Delete-->
<div class="modal fade" id="deleteMSModel" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>
@* Duplicate Actions *@
<div id="msControlModal" class="modal fade" data-bs-backdrop="static" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <img class="w-100" src="~/img/isomatric/confirmation.svg" alt="Duplicate Actions" />
            </div>
            <div class="modal-body text-center pt-5">
                <h6 class="fw-bold">Are you sure?</h6>Do you want  <span class="font-weight-bolder text-primary" id="statusData"></span> the <span class="font-weight-bolder text-primary" id="monitorData"></span> infraObject service ?
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="button" class="btn btn-primary btn-block" style="width:80px;" id="btnMSControl">
                    Yes
                    <div id="MSLoader" class="spinner-border text-white ms-2 mt-1 p-1 d-none" style="width: 0.8rem; height: 0.8rem;">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </button>
            </div>

        </div>
    </div>
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Manage/MonitoringServices/monitoringServicesListFunctions.js"></script>
<script src="~/js/Manage/MonitoringServices/monitoringServicesList.js"></script>