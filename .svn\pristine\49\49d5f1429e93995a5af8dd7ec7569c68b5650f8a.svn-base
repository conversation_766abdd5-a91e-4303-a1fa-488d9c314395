﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowCategory.Validators;

public class CreateWorkflowCategoryValidatorTests
{
    private readonly Mock<IWorkflowCategoryRepository> _mockWorkflowCategoryRepository;

    public List<Domain.Entities.WorkflowCategory> WorkflowCategories { get; set; }

    public CreateWorkflowCategoryValidatorTests()
    {
        WorkflowCategories = new Fixture().Create<List<Domain.Entities.WorkflowCategory>>();

        _mockWorkflowCategoryRepository = WorkflowCategoryRepositoryMocks.CreateWorkflowCategoryRepository(WorkflowCategories);
    }

    //Name

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Name_InWorkflowCategory_WithEmpty(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Name_InWorkflowCategory_IsNull(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = null;

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameNotNullRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Name_InWorkflowCategory_MiniMumRange(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "NR";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Name_InWorkflowCategory_MaximumRange(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXY_ABCDEFGHIJKLMNOPQRSTUVWXY_ABCDEFGHIJKLMNOPQRSTUVWXY_ABCDEFGHIJKLMNOPQRSTUVWXY";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "  PTS  ";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory_With_TripleSpace_InBetween(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "PTS   India";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory_With_DoubleSpace_InFront(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "  PTS India";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory_With_DoubleSpace_InBack(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "PTS India  ";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory_With_SpecialCharacters_InBetween(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "PTS@#India";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory_With_SpecialCharacters_Only(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "@#$%^%*^(()><";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory_With_SpecialCharacters_InFront(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "@#PTS";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory_With_SpecialCharacters_InBack(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "PTS#$%";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory_With_Numbers_Only(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "12345678990";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }
    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory_With_UnderScore_InFront(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "_PTS";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory_With_UnderScore_InFront_AndBack(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "_PTS_";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory_With_Numbers_InFront(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "123PTS";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "_123PTS_";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Valid_Name_InWorkflowCategory_With_UnderScore_InFront_AndNumbers_InBack(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Name = "_PTS123";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    //Properties

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Properties_InWorkflowCategory_WithEmpty(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Properties = "";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryPropertiesRequired, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_Create_Properties_InWorkflowCategory_IsNull(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Properties = null;

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.WorkflowCategory.WorkflowCategoryPropertiesNotNullRequired, (string)validateResult.Errors[3].ErrorMessage);
    }

    //Version

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_CreateWorkflowCategoryCommandValidator_Version_WithEmpty(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Version = "";
        createWorkflowCategoryCommand.Name = "PTS";
        createWorkflowCategoryCommand.Properties = "{\"key\": \"value\"}";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);

        Assert.Contains(validateResult.Errors, e => e.PropertyName == "Version" && e.ErrorMessage == "Version is required.");
    }

    [Theory]
    [AutoWorkflowCategoryData]
    public async Task Verify_CreateWorkflowCategoryCommandValidator_Version_IsNull(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var validator = new CreateWorkflowCategoryCommandValidator(_mockWorkflowCategoryRepository.Object);

        createWorkflowCategoryCommand.Version = null;
        createWorkflowCategoryCommand.Name = "Chennai";
        createWorkflowCategoryCommand.Properties = "{\"key\": \"value\"}";

        var validateResult = await validator.ValidateAsync(createWorkflowCategoryCommand, CancellationToken.None);

        Assert.Contains(validateResult.Errors, e => e.PropertyName == "Version" && e.ErrorMessage == "Version is required.");
    }
}