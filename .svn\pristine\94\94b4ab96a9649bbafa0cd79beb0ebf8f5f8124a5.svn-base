﻿using ContinuityPatrol.Application.Features.Report.Queries.GetRunBookReport;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Identity;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using ContinuityPatrol.Web.Areas.ITAutomation.Controllers;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.ClipboardSource.SpreadsheetML;
using DevExpress.Utils.Svg;
using DevExpress.XtraCharts;
using DevExpress.XtraPivotGrid.Data;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using DevExpress.XtraRichEdit.Import.OpenDocument;
using DevExpress.XtraRichEdit.Model;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Npgsql.Replication.PgOutput;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    [SupportedOSPlatform("windows")]
    public partial class BulkImport : DevExpress.XtraReports.UI.XtraReport
    {
     
        public BulkImport()
        {         
            InitializeComponent();


            //Clinet logo
            //try
            //{
            //    string imgbase64String = string.IsNullOrEmpty(WorkflowConfigurationController.CompanyLogo.ToString()) ? "NA" : WorkflowConfigurationController.CompanyLogo.ToString();
            //    if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
            //    {
            //        xrPictureBox1.Visible = false;
            //        if (imgbase64String.Contains(","))
            //        {
            //            imgbase64String = imgbase64String.Split(',')[1];
            //        }
            //        byte[] imageBytes = Convert.FromBase64String(imgbase64String);
            //        using (MemoryStream ms = new MemoryStream(imageBytes))
            //        {
            //            Image image = Image.FromStream(ms);
            //            xrPictureBox5.Image = image;
            //        }
            //    }
            //    else
            //    {
            //        xrPictureBox5.Visible = false;
            //        xrPictureBox1.Visible = true;
            //    }
            //}
            //catch (Exception ex)
            //{
            //    _logger.LogError("Error occured while display the customer logo" + ex.Message.ToString());
            //}
           // xrLabel3.Text = BulkImportInputController.TemplateName.ToString();
            xrTable2.BeforePrint += (sender, e) =>
            {
                var table = (XRTable)sender;
                table.Rows.Clear();
                int rowIndex = 0;
                //header             
                var propertyCountValues = BulkImportInputController.propertyCount;
                XRTableRow dataRow0 = new XRTableRow();
                foreach (var item in propertyCountValues)
                {
                    var parts = item.Split(':');
                    var text = parts[0]; 
                    var spanCount = int.Parse(parts[1]);
                    XRTableCell cell = new XRTableCell
                    {
                        Text = text,
                        Multiline = true,
                        Borders = DevExpress.XtraPrinting.BorderSide.Top | BorderSide.Left,
                        BorderWidth = 1,
                        BorderColor = System.Drawing.Color.Black,
                        WordWrap = true,
                        WidthF = 830.11F,
                        HeightF = 50F,
                        Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6),
                        TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft,
                        Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F,DXFontStyle.Bold)
                    };

                    dataRow0.Cells.Add(cell);

                    for (int i = 1; i < spanCount; i++)
                    {
                        if (item.Contains("Workflow Template") && i == spanCount - 1)
                        {
                            dataRow0.Cells.Add(new XRTableCell
                            {
                                Text = "",
                                Multiline = true,
                                Borders = DevExpress.XtraPrinting.BorderSide.Top|BorderSide.Right,
                                BorderWidth = 1,
                                BorderColor = System.Drawing.Color.Black,
                                WordWrap = true,
                                WidthF = 830.11F,
                                HeightF = 50F,
                                Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6),
                                TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft,
                                Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F)
                            });
                        }
                        else
                        {

                            dataRow0.Cells.Add(new XRTableCell
                            {
                                Text = "",
                                Multiline = true,
                                Borders = DevExpress.XtraPrinting.BorderSide.Top,
                                BorderWidth = 1,
                                BorderColor = System.Drawing.Color.Black,
                                WordWrap = true,
                                WidthF = 830.11F,
                                HeightF = 50F,
                                Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6),
                                TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft,
                                Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F)
                            });
                        }

                    }

                }
                table.Rows.Add(dataRow0);

                //header
                XRTableRow dataRow = new XRTableRow();                           
                foreach (var item in BulkImportInputController.propertyNames)
                {
                    dataRow.Cells.Add(new XRTableCell { Text = item, Multiline = true, Borders = DevExpress.XtraPrinting.BorderSide.All, BorderWidth = 1, BorderColor = System.Drawing.Color.Black, WordWrap = true, WidthF = 830.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                }

                table.Rows.Add(dataRow);
                rowIndex++;
                for (int i = 0; i < 100; i++)
                {
                    XRTableRow dataRow1 = new XRTableRow();
                    for (int j = 0; j < BulkImportInputController.propertyNames.Count; j++) { dataRow1.Cells.Add(new XRTableCell { Text = "", Multiline = true, Borders = DevExpress.XtraPrinting.BorderSide.All, BorderWidth = 1, BorderColor = System.Drawing.Color.Black, WordWrap = true, WidthF = 830.11F, HeightF = 50F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }); }
                    table.Rows.Add(dataRow1);
                }

                xrTable2.WidthF = 200 * table.Rows[0].Cells.Count;
                XRTableCell lastCell = table.Rows[table.Rows.Count - 1].Cells[table.Rows[table.Rows.Count - 1].Cells.Count - 1];
                PointF lastCellLocation = lastCell.BoundsF.Location;
              //  xrPictureBox2.LocationF = new PointF(lastCellLocation.X + 100, lastCellLocation.Y + lastCell.BoundsF.Height);
                ////Temp gen time
                //xrLabel12.LocationF = new PointF(lastCellLocation.X+50 , lastCellLocation.Y + 88 + lastCell.BoundsF.Height);
                //xrLabel3.LocationF = new PointF(lastCellLocation.X + 250, lastCellLocation.Y + 88 + lastCell.BoundsF.Height);
                ////temp gen by
                //xrLabel2.LocationF = new PointF(lastCellLocation.X+50 , lastCellLocation.Y + 130 + lastCell.BoundsF.Height);
                //_username.LocationF = new PointF(lastCellLocation.X + 250, lastCellLocation.Y + 130 + lastCell.BoundsF.Height);
                ////temp name
                //xrLabel1.LocationF = new PointF(lastCellLocation.X +50, lastCellLocation.Y + 170 + lastCell.BoundsF.Height);
                //xrPageInfo2.LocationF = new PointF(lastCellLocation.X + 250, lastCellLocation.Y + 170 + lastCell.BoundsF.Height);

            };
            
        }

        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
           // _username.Text = BulkImportInputController.ReportGeneratedName.ToString();
        }

  
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {

            var builder = new ConfigurationBuilder()
                 .SetBasePath(Directory.GetCurrentDirectory())
                 .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            IConfigurationRoot configuration = builder.Build();
            var version = configuration["CP:Version"];
          //  xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2024 - 2025 Perpetuuiti - All Rights Reserved.";
        }

    }
}