﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.VeritasClusterModel.VeritasClusterViewModel
@using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel
@using ContinuityPatrol.Shared.Services.Helper
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-veritas-cluster"></i><span>Veritas Cluster</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="veritasSearchInp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="clusterprofilename=" id="profilename">
                                        <label class="form-check-label" for="profilename">
                                            Cluster Profile Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="clusterservername=" id="clusterserver">
                                        <label class="form-check-label" for="clusterserver">
                                            Cluster Server
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="clustername=" id="clustername">
                                        <label class="form-check-label" for="clustername">
                                            Cluster Name
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" id="veritasCreateBtn" class="btn btn-primary btn-sm"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="pt-1 card-body" style="height: calc(100vh - 141px);">
            <div>
                <table id="veritasClusterTable" class="table">
                    <thead>
                        <tr>
                            <th>Sr No</th>
                            <th>Cluster Profile Name </th>
                            <th>Cluster Server</th>
                            <th>Cluster Name</th>
                            <th>Cluster Bin Path</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div id="veritasConfigCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<div id="veritasConfigDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<!--Modal create-->
<div class="modal fade" id="veritasCreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>
<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="veritasDeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Configuration//Infra Components/Veritas Cluster/VeritasCluster.js"></script>