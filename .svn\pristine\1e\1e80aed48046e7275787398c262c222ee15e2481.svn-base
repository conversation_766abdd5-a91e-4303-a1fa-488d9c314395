﻿let createPermission = $("#AdminBkCreate").data("create-permission")?.toLowerCase();
if (createPermission == 'false') {
    $(".bkclass").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-dismiss').removeAttr('id');

}
let BackupURL = {
    "CheckWindowServiceurl": RootUrl + 'Admin/BackupData/CheckWindowsService',
}

$(function () {
    dataVl();
    getBackupDetails();
    $('#btnSave').prop('disabled', true);
    setTimeout(() => {
        getBackupConfigDetails()
    }, 500)
    let selectedValues = [];

    let dataTable = $('#appendbackuptable').DataTable(

        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/Admin/BackupData/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json.data.length === 0) {
                        $(".pagination-column").addClass("disabled")


                    }
                    else {
                        $(".pagination-column").removeClass("disabled")

                    }
                    return json?.data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1, 2, 3, 4],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false
                },
                {
                    "data": "backUpPath", "name": "Backup Path", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "type", "name": "Backup Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span>' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "createdDate", "name": "Created Date", "autoWidth": true,
                    "render": function (data, type, row) {
                        const formattedDatetimeCreate = formatDateTime(row.createdDate);
                       
                        if (type === 'display') {
                            return '<span>' + formattedDatetimeCreate + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "lastModifiedDate", "name": "Last Executed", "autoWidth": true,
                    "render": function (data, type, row) {
                     const formattedDatetimeExecute = formatDateTime(row.lastModifiedDate);
                        if (type === 'display') {
                            return '<span>' + formattedDatetimeExecute + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "status", "name": "Status", "autoWidth": true,
                    "render": function (data, type, row) {
                        
                        const statusBadge = (row.status.toLowerCase() === "error") ? '<span class="badge text-bg-danger">' + row.status + '</span>' : row.status.toLowerCase() === "pending" ? '<span class="badge text-bg-warning">' + row.status + '</span>' : '<span class="badge text-bg-success">' + backup.status + '</span>';
                        if (type === 'display') {
                            return '<span>' + statusBadge + '</span>';
                        }
                        return data;
                    }

                },
               
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const NameCheckbox = $("#Name");

        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }

        dataTable.ajax.reload(function (json) {

            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500))
})

async function dataVl() {
    let html = '';

    try {       
        const result = await $.ajax({
            type: "POST",
            url: BackupURL.CheckWindowServiceurl,
            data: { type: 'monitoring', __RequestVerificationToken: gettoken() },
            dataType: "json",
            traditional: true,
        });

        // Check if the response is successful
        if (result?.success) {
            if (result?.success) {
               // $('#ProfileList').prop('disabled', false)
                //let message = result.activeNodes[0] || result.inActiveNodes[0]
                // notificationAlert("success", message)
                if (result?.activeNodes && Array.isArray(result?.activeNodes) && result?.activeNodes?.length) {
                    for (let i = 0; i < result?.activeNodes?.length; i++) {
                        html += `<div class='mb-1'><i class="cp-network fs-4 text-success" ></i> '${result.activeNodes[i]}'</div>`;
                    }
                }

                if (result?.inActiveNodes && Array.isArray(result?.activeNodes) && result?.inActiveNodes?.length) {
                    for (let i = 0; i < result?.inActiveNodes?.length; i++) {
                        html += `<div class='mb-1'><i class="cp-network fs-4 text-danger" ></i> '${result.inActiveNodes[i]}'</div>`;
                    }
                }

                notificationAlert("success", html, 'execution')
            } else {
                errorNotification(result)
            }
            notificationAlert("success", html, 'execution');
        } else {
           
            errorNotification(result);
        }
    } catch (error) {
        
        console.error("Error during AJAX request:", error);
      //  errorNotification({ message: "An error occurred while fetching the data." });
    }
}

function getBackupConfigDetails() {
    $.ajax({
        type: "GET",
        url: "/Admin/BackupData/GetBackupConfig",
        async: true,
        success: function (result) {
            
            $('#txthostname').val(result.serverName);
            $('#txtdatabasename').val(result.databaseName);
            $('#txtusername').val(result.userName);
            $('#txtpassword').val(result.password);

        }

    });

}

const exceptThisSymbols = ["e", "E", "+", "-", "."];
$('#everyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#everyHours').val('');
    }
    const value = $(this).val();
    errorElement = $('#CroneveryHour-error');
    validateHourNumber(value, "Select hours", errorElement);
});

$('#MonthlyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#MonthlyHours').val('');
    }
    const value = $(this).val()
    errorElement = $('#CronMonthHrs-error');
    validateHourNumber(value, "Select hours", errorElement);
});

$('#txtbackuppath').on('keydown keyup', async function () {

    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateBackuppath(value);
});

$('#selectbackup').on('change', function (event) {

    let value = $(this).val();
    const errorElement = $('#Keepbackup-error');
    if (value === "Select keep last backup") {
        value = ""
    }
    validateDropDown(value, "Select keep last backup", errorElement);
});


$('#bd_lblMonth').on('change', function () {

    const value = $(this).val();
    errorElement = $('#CronMonth-error');
    validateDayNumber(value, "Select month and year", errorElement);
    if (value) {
        $('input[name=Days]').prop("checked", false);
        const [year, month] = value.split("-");
        const lastDayOfMonth = new Date(year, month, 0).getDate();
        const today = new Date();
        let currentdate = today.getDate();
        let currentMonth = today.getFullYear() + "-" + (today.getMonth() + 1);
        for (let i = 1; i <= 31; i++) {
            const checkboxElement = document.getElementById(`inlineCheckbox${i}`);

            currentMonth == value && currentdate > i ? checkboxElement.disabled = true : checkboxElement.disabled = !(i <= lastDayOfMonth);
        }
    } else {
        for (let i = 1; i <= 31; i++) {
            const checkboxElement = document.getElementById(`inlineCheckbox${i}`);
            checkboxElement.checked = false;
            checkboxElement.disabled = false;
        }
        updateCheckboxes()
    }
});

let monthcheck = ''
let checkCurrentYear = ''
var currentDate = new Date();
var currentMonths = (currentDate.getMonth() + 1).toString().padStart(2, '0');
var currentDay = currentDate.getDate();
let currentYear = currentDate.getFullYear();

let displayValidDates = {
    '02': ['29', '30', '31'],
    '04': ['31'],
    '06': ['31'],
    '09': ['31'],
    '11': ['31'],
}

function updateCheckboxes() {

    $('input[name=Days]').each(function () {
        var checkboxValue = parseInt($(this).val());
        if (monthcheck === currentMonths && checkboxValue >= currentDay) {
            $(this).prop('disabled', false);
        } else if (monthcheck > currentMonths || (monthcheck === currentMonths && checkboxValue > currentDay)) {
            $(this).prop('disabled', false);
        } else {
            $(this).prop('disabled', true);
        }
    });

    if (monthcheck || currentMonths) {
        let getValue = displayValidDates[monthcheck ?? currentMonths] ?? '';
        let findLeapYear = (checkCurrentYear ?? currentYear);

        let isLeapYearFound = (findLeapYear % 4 === 0 && findLeapYear % 100 !== 0) || (findLeapYear % 400 === 0);

        if (isLeapYearFound && monthcheck === '02') {
            displayValidDates['02'].splice(0, 1);
        }

        if (getValue.length) {
            getValue.forEach((val) => {
                $(`input[name="Days"][value="${val}"]`)?.parent()?.hide();
            })
        } else {
            let showDateElement = ['29', '30', '31']

            showDateElement.forEach((val) => {
                $(`input[name="Days"][value="${val}"]`)?.parent()?.show();
            })
        }
    }
}

updateCheckboxes();


$('#bd_txtMins').on('input', function (event) {

    const value = $(this).val();
    let numericValue = value.replace(/[^0-9]/g, '');

    if (numericValue.length > 2) {
        numericValue = numericValue.substring(0, 2);
    }

    // Ensure numericValue is not empty and not 0
    if (numericValue === '' || parseInt(numericValue, 10) <= 0 || parseInt(numericValue, 10) > 59) {
        $(this).val('');
    } else {
        $(this).val(numericValue);
    }

    errorElement = $('#CronMin-error');
    validateMinNumber(numericValue, "Enter minutes", errorElement);
});



// Hourly Button
$('#bd_txtHours').on('input', function (event) {
    const value = $(this).val();
    let numericValue = value.replace(/[^0-9]/g, '');
    if (numericValue.length > 2) {
        numericValue = numericValue.substring(0, 2);
    }
    if (numericValue === '' || parseInt(numericValue, 10) < 0 || parseInt(numericValue, 10) > 23) {
        $(this).val('');
    } else {
        $(this).val(numericValue);
    }

    errorElement = $('#CronHourly-error');
    validateHourNumber(numericValue, "Enter hours", errorElement);
});

$('#bd_txtMinutes').on('input', function (event) {
    const value = $(this).val();
    let numericValue = value.replace(/[^0-9]/g, '');
    if (numericValue.length > 2) {
        numericValue = numericValue.substring(0, 2);
    }
    if (numericValue === '' || parseInt(numericValue, 10) < 0 || parseInt(numericValue, 10) > 59) {
        $(this).val('');
    } else {
        $(this).val(numericValue);
    }

    errorElement = $('#CronHourMin-error');
    validateMinNumber(numericValue, "Enter minutes", errorElement);
});

// Daily Button
$('input[name=daysevery1]').on('click', function (event) {
    errorElement = $('#Crondaysevery-error');
    ValidateCronRadioButton(errorElement);
});



// weekly Button 
$('input[name=weekDays]').on('click', function (event) {
    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var Dayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    errorElement = $('#CronDay-error');
    validateDayNumber(Dayvalue, "Select day", errorElement);
});


$('#bd_ddlHours').on('input', function (event) {
    const value = $(this).val();
    const numericValue = parseInt($(this).val(), 10);
    if (isNaN(numericValue) || numericValue < 0) {
        $(this).val('');
    }
    errorElement = $('#CronddlHour-error');
    validationhourweek(value, "Enter hours", errorElement);
});

$('input[name=Days]').on('click', function (event) {
    var checkedCheckboxes = document.querySelectorAll('[name="Days"]:checked');
    var Dayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    errorElement = $('#CronMonthlyDay-error');
    validateDayNumber(Dayvalue, "Select date(s)", errorElement);
});

$('#bd_txtHourss').on('input', function (event) {
    const value = $(this).val();
    let numericValue = value.replace(/[^0-9]/g, '');
    if (numericValue.length > 2) {
        numericValue = numericValue.substring(0, 2);
    }
    if (numericValue === '' || parseInt(numericValue, 10) < 1 || parseInt(numericValue, 10) > 23) {
        $(this).val('');
    } else {
        $(this).val(numericValue);
    }

    errorElement = $('#CronMonthHrs-error');
    validateHourNumber(numericValue, "Enter hours", errorElement);
});

$('#bd_txtMinss').on('input', function (event) {
    const value = $(this).val();
    let numericValue = value.replace(/[^0-9]/g, '');
    if (numericValue.length > 2) {
        numericValue = numericValue.substring(0, 2);
    }
    if (numericValue === '' || parseInt(numericValue, 10) < 1 || parseInt(numericValue, 10) > 59) {
        $(this).val('');
    } else {
        $(this).val(numericValue);
    }

    errorElement = $('#CronMonthMins-error');
    validateMinNumber(numericValue, "Enter minutes", errorElement);
});

$('#datetimeCron').on('change', function () {
    const value = $(this).val();
    errorElement = $('#CronExpression-error');
    validateDayNumber(value, "Select the schedule time", errorElement);
});

$('#ftphostname').on('keydown keyup', async function () {

    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateHostName(value);
});


$('#ftpurl').on('keydown keyup', async function () {

    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateTargetPath(value);
});

$('#cpurl').on('keydown keyup', async function () {

    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateSourcePath(value);
});
$('#ftppassword').on('keydown keyup', async function () {

    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validatePassword(value);
});

$('#ftpusername').on('keydown keyup', async function () {

    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateUserName(value);
});

$('#ftpport').on('input', async function (event) {
    let value = $(this).val();

    var sanitizedValue = value.replace(/\D/g, '').substring(0, 5);

    $(this).val(sanitizedValue);

    if (event.originalEvent && event.originalEvent.inputType === 'insertText') {
        let key = event.originalEvent.data;
        if (isNaN(key) || sanitizedValue.length >= 5) {
            event.preventDefault();
        }
    }

    if (sanitizedValue == 0) {
        $(this).val("");
    }

    await validatePort(sanitizedValue);
});


$('#btnExecute').on('click', backupDebounce(async function () {

    let backupserver = $('#txtserverid').val().trim();
    var backuppath1 = $('#txtbackuppath').val();
    var ftphostName1 = $('#ftphostname').val();
    var ftpuserName1 = $('#ftpusername').val();
    var ftphostPassword1 = $('#ftppassword').val();
    var ftpport1 = $('#ftpport').val();
    var ftpcpUrl1 = $('#cpurl').val();
    var ftpserverUrl1 = $('#ftpurl').val();
    if (backupserver && backuppath1 || (ftphostName1 && ftpuserName1 && ftphostPassword1 && ftpport1 && ftpcpUrl1 && ftpserverUrl1)) {
        try {
            const response = await $.ajax({
                url: "/Admin/BackupData/ExecuteBackUpcommand",
                type: 'POST',
                dataType: "json",
                contentType: 'application/json',
                data: JSON.stringify({ Id: backupserver }),
            });

            if (response && response.success) {
                notificationAlert("success", response.message);
                setTimeout(() => {
                    window.location.reload();
                }, 300)
            } else {
                notificationAlert("warning", response.message);
            }
        } catch (error) {
            //notificationAlert("error", "An error occurred: " + error.statusText);
        }
    } else {
        // notificationAlert("error", "Please provide a valid server ID.");
    }
}, 500));


$('#btnSave').on('click', async function () {

    GetIsSchedule();

    var backupType = $("input[name='switchPlan']:checked").val();
    if (backupType === 'Cycle') {
        $("#switchMonthly").prop("checked", true);
        $('#btnExecute').prop('disabled', true);
        $('#btnSave').prop('disabled', false);
    } else if (backupType === 'Once') {
        $("#switchYearly").prop("checked", true);
        $('#btnExecute').prop('disabled', false);
        $('#btnSave').prop('disabled', true);
    }

    $("#ChkValue").val(backupType);
    errorElement = $('#Keepbackup-error');
    var keepBackupLast = $("#selectbackup").find(':selected').val()

    var serverName = $("#txthostname").val();
    var databaseName = $("#txtdatabasename").val();
    var userName = $("#txtusername").val();
    var backuppath = $('#txtbackuppath').val();
    var ftphostName = $('#ftphostname').val();
    var ftpuserName = $('#ftpusername').val();
    var ftphostPassword = $('#ftppassword').val();
    var ftpport = $('#ftpport').val();
    var ftpcpUrl = $('#cpurl').val();
    var ftpserverUrl = $('#ftpurl').val();
    var isFtphostname = await validateHostName(ftphostName);
    var isFtpusername = await validateUserName(ftpuserName);
    var isFtppassword = await validatePassword(ftphostPassword)
    var isFtpport = await validatePort(ftpport)
    var isFtpsourcepath = await validateSourcePath(ftpcpUrl)
    var isFtptargetpath = await validateTargetPath(ftpserverUrl)
    var isBackuppath = await validateBackuppath(backuppath)
    var iskeepbackup = await validateDropDown(keepBackupLast, "Select keep last backup ", errorElement)
    let baseProperties = {
        ftphostName: ftphostName,
        ftpuserName: ftpuserName,
        ftphostPassword: ftphostPassword,
        ftpport: ftpport,
        sourcepath: ftpcpUrl,
        targetpath: ftpserverUrl
    };

    let jsonString = JSON.stringify(baseProperties);

    $('#textProperties').val(jsonString);


    var isLocal = $('#inlineRadio1').prop('checked');
    var isBackup = $('#inlineRadio2').prop('checked');

    $('#txtLocalserver').val(isLocal);
    $('#txtBackupserver').val(isBackup);
    if (backupType === "Cycle") {
        var isScheduler = CronValidation();
        var { cronExpression, cronListView } = GetCronExpression();
        Get_ScheduleTypes();
        $('#textCronExpression').val(cronExpression);
        $('#txtCronViewList').val(cronListView);

        let sanitizebackupArray = ['txthostname', 'txtdatabasename', 'txtusername', 'txtbackuppath', 'ftphostname', 'ftpusername', 'ftppassword', 'ftpport',
            'cpurl', 'ftpurl', 'txtLocalserver', 'txtBackupserver']
        sanitizeContainer(sanitizebackupArray)
        
        if (isLocal) {
            if (serverName && databaseName && userName && isBackuppath && isScheduler && iskeepbackup && backupType) {
                $("#backpth").val(backuppath)
                $("#KPbackLast").val(keepBackupLast)
                $('#BackupForm').trigger('submit');
            }
        } else {
            if (serverName && databaseName && userName && isScheduler && iskeepbackup && isBackup && isFtphostname && isFtpusername && isFtppassword && isFtpport && isFtpsourcepath && isFtptargetpath && backupType) {
                $("#backpth").val(backuppath)
                $("#KPbackLast").val(keepBackupLast)
                $('#BackupForm').trigger('submit');
            }
        }
        let backupserver = $('#txtserverid').val().trim();

        if (backupserver && isScheduler && isFtphostname && isFtpusername && isFtppassword && isFtpport && isFtpsourcepath && isFtptargetpath) {

            const response = await $.ajax({
                url: "/Admin/BackupData/ExecuteBackUpcommand",
                type: 'POST',
                dataType: "json",
                headers: {
                    'RequestVerificationToken': gettoken()
                },
                contentType: 'application/json',
                data: JSON.stringify({
                    Id: backupserver,

                }),
            });

            if (response && response.success) {
                notificationAlert("success", response.message);
                  setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                notificationAlert("warning", response.message);
            }

        }
    } else {
        
        if (isLocal) {
            if (serverName && databaseName && userName && iskeepbackup && isBackuppath && backupType) {
                $('#BackupForm').trigger('submit');
            }
        } else {
            if (serverName && databaseName && userName && backupType && iskeepbackup && isScheduler && isBackup && isFtphostname && isFtpusername && isFtppassword && isFtpport && isFtpsourcepath && isFtptargetpath && backupType) {
                $('#BackupForm').trigger('submit');
            }
        }


    }

});
function validateMinNumber(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg);
        errorElement.addClass('field-validation-error');
        return false;
    }
    else if (value.length > 3) {
        errorElement.text("Value should be 2 digit");
        errorElement.addClass('field-validation-error');
        return false;
    }

    else if ((Number(value) < 0) || (Number(value) >= 60)) {
        errorElement.text("Enter value between 0 to 59");
        errorElement.addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
        return true;
    }
}
function validateHourNumber(value, errorMsg, errorElement) {

    if (!value) {
        errorElement.text(errorMsg);
        errorElement.addClass('field-validation-error');
        return false;
    }
    //else if (value.length > 2) {
    //    errorElement.text("Value should be 2 digit");
    //    errorElement.addClass('field-validation-error');
    //    return false;
    //}
    else if ((Number(value) < 0) || (Number(value) >= 24)) {
        errorElement.text("Enter value between 0 to 23");
        errorElement.addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
        return true;
    }
}
function ValidateCronRadioButton(errorElement) {
    if ($('input[name=daysevery1]:checked').length > 0) {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
    else {
        errorElement.text("Select day type").addClass('field-validation-error');
        return false;
    }
}
function validateDayNumber(value, errorMsg, errorElement) {
    if (!value || value == 0) {
        errorElement.text(errorMsg);
        errorElement.addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
        return true;
    }
}

function validationhourweek(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg);
        errorElement.addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
        return true;
    }
}

function validateDropDown(value, errorMsg, errorElement) {

    if (!value) {
        errorElement.text(errorMsg);
        errorElement.addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
        return true;
    }
}


async function validateBackuppath(value) {
    const errorElement = $('#Backup-error');

    if (!value) {
        errorElement.text('Enter backup path').addClass('field-validation-error');

        return false;

    }
    const validationResults = [
        await Backupfilepath(value),

    ];

    return await CommonValidation(errorElement, validationResults);

    return true;
}

async function validateHostName(value) {
    const errorElement = $('#Ftphostname-error');

    if (!value) {
        errorElement.text('Enter FTP host name').addClass('field-validation-error');

        return false;

    } const validationResults = [
        await SpecialCharValidate(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await ShouldNotBeginWithNumber(value),
    ];

    return await CommonValidation(errorElement, validationResults);

    return true;
}

function CronValidation() {
    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    var checkedDaysCheckboxes = document.querySelectorAll('[name="Days"]:checked');
    var txtDays = Array.from(checkedDaysCheckboxes).map(checkbox => checkbox.value);
    var Minutes = $('#bd_txtMins').val();
    var txtHours = $('#bd_txtHours').val();
    var txtHourMinutes = $('#bd_txtMinutes').val();
    var ddlHours = $('#bd_ddlHours').val();
    //  var everyHours = $('#bd_everyHours').val();
    var Daily = $('#everyHours').val().split(":")
    var everyHours = Daily[0]
    var everyMinutes = Daily[1]
    //var everyMinutes = $('#bd_everyMinutes').val();
    var month = $('#bd_lblMonth').val();
    var monthHrs = $('#bd_txtHourss').val();
    var monthMints = $('#bd_txtMinss').val();
    var isScheduler = '';
    var months = $('#MonthlyHours').val().split(":")
    var monthHrs = months[0]
    var monthMints = months[1]
    $('#datetimeCron').val('');
    let Scheduler_types = $('.nav-tabs .active').text().trim();

    switch (Scheduler_types) {
        case "Minutes":
            $('#bd_txtHours').val('');
            $('#bd_txtMinutes').val('');
            $('#bd_ddlHours').val('');
            $('#ddlMinutes').val('');
            $('#everyHours').val('');
            // $('#bd_everyMinutes').val('');
            $('#datetimeCron').val('');
            $('#bd_lblMonth').val();
            $('#bd_txtHourss').val();
            $('#MonthlyHours').val('');
            $('#bd_txtMinss').val();
            $('input[name=weekDays]').prop("checked", false)
            $('input[name=Days]').prop("checked", false)
            $("#defaultCheck-everyday").prop("checked", false)
            $("#defaultCheck-MON-FRI").prop("checked", false)
            errorElement = $('#CronMin-error');
            value = Minutes;
            if (!value) {
                isScheduler = validateMinNumber(Minutes, "Enter minutes", errorElement);
            }
            else if (value.length > 2) {
                isScheduler = validateMinNumber(Minutes, "Value should be 2 digit", errorElement);
            }

            else if ((Number(value) < 0) || (Number(value) >= 60)) {
                isScheduler = validateMinNumber(Minutes, "Enter value between 0 to 59", errorElement);
            }
            else {

                errorElement.text('');
                errorElement.removeClass('field-validation-error');
                isScheduler = true;
            }
            break;
        case "Hourly":
            $('#bd_ddlHours').val('');
            $('#ddlMinutes').val('');
            $('#everyHours').val('');
            //$('#bd_everyMinutes').val('');
            $('#bd_txtMins').val('');
            $('#datetimeCron').val('');
            $("#defaultCheck-everyday").prop("checked", false)
            $("#defaultCheck-MON-FRI").prop("checked", false)
            $('#bd_lblMonth').val();
            $('#bd_txtHourss').val();
            $('#bd_txtMinss').val();
            $('#MonthlyHours').val('');
            $('input[name=weekDays]').prop("checked", false);
            $('input[name=Days]').prop("checked", false);
            errorElement = $('#CronHourly-error');
            isSchedulertxthr = validateHourNumber(txtHours, "Enter hours", errorElement);
            if (isSchedulertxthr || txtHours.length > 2) {
                isSchedulertxthr = validateHourNumber(txtHours, "Value should be 2 digits", errorElement);
            }
            if (isSchedulertxthr || (Number(txtHours) < 0 || Number(txtHours) >= 24)) {
                isSchedulertxthr = validateHourNumber(txtHours, "Enter value between 0 to 23", errorElement);
            }
            errorElement = $('#CronHourMin-error');
            isSchedulertxtmin = isScheduler || validateMinNumber(txtHourMinutes, "Enter minutes", errorElement);
            if (isSchedulertxtmin && txtHourMinutes.length > 2) {
                isSchedulertxtmin = validateMinNumber(txtHourMinutes, "Value should be 2 digits", errorElement);
            }
            if (isSchedulertxtmin && (Number(txtHourMinutes) < 0 || Number(txtHourMinutes) >= 60)) {
                isSchedulertxtmin = validateMinNumber(txtHourMinutes, "Enter value between 0 to 59", errorElement);
            }
            if (isSchedulertxthr && isSchedulertxtmin) {
                isScheduler = true
            }
            break;
        case "Daily":
            $('#bd_ddlHours').val('');
            $('#ddlMinutes').val('');
            $('#bd_txtHours').val('');
            $('#bd_txtMinutes').val('');
            $('#bd_txtMins').val('');
            $('#datetimeCron').val('');
            $('#bd_lblMonth').val();
            $('#bd_txtHourss').val();
            $('#bd_txtMinss').val();
            $('#MonthlyHours').val('');
            $('input[name=weekDays]').prop("checked", false)
            $('input[name=Days]').prop("checked", false)
            errorElement = $('#Crondaysevery-error');
            isSchedulerRadioButton = ValidateCronRadioButton(errorElement);

            // Validation for everyHours
            errorElement = $('#CroneveryHour-error');
            isSchedulerhr = isScheduler || validateHourNumber(everyHours, "Select hours", errorElement);
            if (isScheduler || everyHours.length > 2) {
                isScheduler = validateHourNumber(everyHours, "Value should be 2 digits", errorElement);
            }
            if (isScheduler || (Number(everyHours) < 0 || Number(everyHours) >= 24)) {
                isScheduler = validateHourNumber(everyHours, "Enter value between 0 to 23", errorElement);
            }

            // Validation for everyMinutes
            errorElement = $('#CroneveryMin-error');
            isSchedulermint = isScheduler || validateMinNumber(everyMinutes, "Enter minutes", errorElement);
            if (isScheduler && everyMinutes.length > 2) {
                isScheduler = validateMinNumber(everyMinutes, "Value should be 2 digits", errorElement);
            }
            if (isScheduler || (Number(everyMinutes) < 0 || Number(everyMinutes) >= 60)) {
                isScheduler = validateMinNumber(everyMinutes, "Enter value between 0 to 59", errorElement);
            }
            if (isSchedulermint && isSchedulerhr && isSchedulerRadioButton) {
                isScheduler = true
            }
            break;

        case "Weekly":

            $('#everyHours').val('');
            $('#bd_everyMinutes').val('');
            $('#bd_txtHours').val('');
            $('#bd_txtMinutes').val('');
            $('#bd_txtMins').val('');
            $('#datetimeCron').val('');
            $("#defaultCheck-everyday").prop("checked", false)
            $("#defaultCheck-MON-FRI").prop("checked", false)
            $('#bd_lblMonth').val();
            $('#bd_txtHourss').val();
            $('#bd_txtMinss').val();
            $('#MonthlyHours').val('');
            $('input[name=Days]').prop("checked", false)
            errorElement = $('#CronddlHour-error');
            isSchedulerhr = validationhourweek(ddlHours, "Select hours", errorElement);

            errorElement = $('#CronDay-error');
            isSchedulerday = isScheduler || validateDayNumber(txtDay, "Select day", errorElement);
            if (isSchedulerhr && isSchedulerday) {
                isScheduler = true
            }
            break;

        case "Monthly":
            $('#everyHours').val('');
            $('#bd_everyMinutes').val('');
            $('#bd_txtHours').val('');
            $('#bd_txtMinutes').val('');
            $('#bd_txtMins').val('');
            $('#datetimeCron').val('');
            $('#ddlMinutes').val('');
            $('#bd_ddlHours').val('');
            $("#defaultCheck-everyday").prop("checked", false)
            $("#defaultCheck-MON-FRI").prop("checked", false)
            $('input[name=weekDays]').prop("checked", false)
            errorElement = $('#CronMonth-error');
            isSchedulermonth = validateDayNumber(month, "Select month and year", errorElement);
            errorElement = $('#CronMonthlyDay-error');
            let mt = $("#bd_lblMonth").val()
            if (mt) {
                // If mt has a value, then trigger the validation function
                isSchedulerday = validateDayNumber(txtDays, "Select date(s)", errorElement);
            }
            errorElement = $('#CronMonthHrs-error');
            isSchedulerhours = validateHourNumber(monthHrs, "Select hours", errorElement);
            errorElement = $('#CronMonthMins-error');
            isSchedulermin = validateMinNumber(monthMints, "Enter minutes", errorElement);
            if (isSchedulermonth && isSchedulerday && isSchedulerhours && isSchedulermin) {
                isScheduler = true
            }
            break;
    }
    return isScheduler;
}

$('.navChange button').on('click', function (event) {

    let navName = event.target.name;
    if (navName === 'minutes') {
        $('#bd_txtMinutes,#bd_txtHours,#everyHours,#bd_everyMinutes,#bd_ddlHours,#ddlMinutes,#MonthlyHours').val('')
        $("#bd_ddlHours,#bd_lblMonth").val("");
        $('#defaultCheck-everyday,#defaultCheck-MON-FRI').prop('checked', false);
        $('.defaultCheck,.monthday').prop('checked', false);
        $('#CronHourly-error,#CronHourMin-error,#Crondaysevery-error,#CroneveryHour-error,#CroneveryMin-error,#CronDay-error,#CronddlHour-error,#CronMonth-error,#CronMonthlyDay-error,#CronMonthHrs-error,#CronMonthMins-error').text('').removeClass('field-validation-error');
    } else if (navName === 'hourly') {
        $('#bd_txtMinutes,#bd_txtHours,#bd_everyMinutes,#everyHours,#ddlMinutes,#MonthlyHours').val('')
        $("#bd_ddlHours,#bd_lblMonth").val("");
        $('#defaultCheck-everyday,#defaultCheck-MON-FRI').prop('checked', false);
        $('.defaultCheck,.monthday').prop('checked', false);
        $('#CronMin-error,#Crondaysevery-error,#CroneveryHour-error,#CroneveryMin-error,#CronDay-error,#CronddlHour-error,#CronMonth-error,#CronMonthlyDay-error,#CronMonthHrs-error,#CronMonthMins-error').text('').removeClass('field-validation-error');
    } else if (navName === 'daily') {
        $('#bd_txtMinutes,#bd_everyMinutes,#bd_txtHours,#everyHours,#ddlMinutes,#MonthlyHours').val('')
        $("#bd_ddlHours,#bd_lblMonth").val("");
        $('.defaultCheck,.monthday').prop('checked', false);
        $('#CronHourly-error,#CronHourMin-error,#CronMin-error,#CroneveryHour-error,#CroneveryMin-error,#CronDay-error,#CronddlHour-error,#CronMonth-error,#CronMonthlyDay-error,#CronMonthHrs-error,#CronMonthMins-error').text('').removeClass('field-validation-error');
    } else if (navName === 'weekly') {
        $('#defaultCheck-everyday,#defaultCheck-MON-FRI').prop('checked', false);
        $('.monthday').prop('checked', false);
        $("#bd_lblMonth").val("");
        $('#bd_txtMinutes,#bd_everyMinutes,#bd_txtHours,#everyHours,#ddlMinutes,#MonthlyHours').val('')
        $('#CronHourly-error,#CronHourMin-error,#CronMin-error,#CronDay-error,#Crondaysevery-error,#CronddlHour-error,#CronMonth-error,#CronMonthlyDay-error,#CronMonthHrs-error,#CronMonthMins-error').text('').removeClass('field-validation-error');
    } else if (navName === 'monthly') {
        $('#defaultCheck-everyday,#defaultCheck-MON-FRI').prop('checked', false);

        $("#bd_ddlHours").val("");
        $('.defaultCheck').prop('checked', false);
        $('#bd_txtMinutes,#bd_everyMinutes,#bd_txtHours,#everyHours,#ddlMinutes').val('')
        monthcheck = ''
        updateCheckboxes();
        $('#CronMin-error,#CronHourly-error,#CronHourMin-error,#Crondaysevery-error,#CroneveryHour-error,#CroneveryMin-error,#CronDay-error,#CronddlHour-erro').text('').removeClass('field-validation-error');
    }

});
function GetCronExpression() {

    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    var checkedDaysCheckboxes = document.querySelectorAll('[name="Days"]:checked');
    var txtDays = Array.from(checkedDaysCheckboxes).map(checkbox => checkbox.value);
    var cronExpression = "";
    var Minutes = $('#bd_txtMins').val();
    var txtHours = $('#bd_txtHours').val();
    var txtHourMinutes = $('#bd_txtMinutes').val();
    var weekhm = $('#bd_ddlHours').val().split(":")
    var ddlHours = weekhm[0]
    var ddlMinutes = weekhm[1]
    //var everyHours = $('#bd_everyHours').val();
    //var everyMinutes = $('#bd_everyMinutes').val();
    var Daily = $('#everyHours').val().split(":")
    var everyHours = Daily[0]
    var everyMinutes = Daily[1]
    var weekDay = $('#defaultCheck-MON-FRI').val();
    var datetime = $('#datetimeCron').val();
    var month = $('#bd_lblMonth').val();
    var monthYear = month.split("-");
    var months = $('#MonthlyHours').val().split(":")
    var monthHrs = months[0]
    var monthMints = months[1]
    var schedule_model = document.querySelector('input[name="daysevery1"]:checked');
    var cronListView = "";

    if (Minutes != '') {
        cronExpression = "0" + " 0/" + Minutes + " * * * ?";
        cronListView = Minutes + " Mins";
    }
    else if (txtHours != '') {
        cronExpression = "0 " + txtHourMinutes + " 0/" + txtHours + " * * ?";
        cronListView = txtHours + " Hrs " + txtHourMinutes + " Mins";
    }
    else if (txtDay != '') {
        cronExpression = "0 " + ddlMinutes + " 0/" + ddlHours + " ? * " + txtDay + " *";
        cronListView = "Every " + ddlHours + " Hrs " + ddlMinutes + " Mins for " + txtDay;
    }
    //else if (txtDays != '') {
    //    cronExpression = "0 " + monthMints + " " + monthHrs + " " + txtDays + " " + monthYear[1] + " ? " + monthYear[0];
    //    cronListView = "At " + monthHrs + " Hrs " + monthMints + " Mins on the " + txtDays + "day(s), in " + monthYear[1] + "-" + monthYear[0];
        //}
    else if (txtDays != '') {
        cronExpression = "0 " + monthMints + " " + monthHrs + " " + txtDays + " " + monthYear[1] + " ? " + monthYear[0];
        cronListView = "At " + monthHrs + " Hrs " + monthMints + " Mins on the " + txtDays + "day(s), in " + monthYear[1] + "-" + monthYear[0];
    }
    else if (schedule_model?.value == "everyday") {
        cronExpression = "0 " + everyMinutes + " 0/" + everyHours + " * * ?"
        cronListView = everyHours + " Hrs " + everyMinutes + " Mins on Every Day";
    }
    else if (schedule_model?.value == "MON-FRI") {
        cronExpression = "0 " + everyMinutes + " 0/" + everyHours + " ? * " + weekDay
        cronListView = everyHours + " Hrs " + everyMinutes + " Mins on Every " + weekDay;

    }
    return { cronExpression, cronListView };
}
function parseMinCronExpression(expression) {
    const parts = expression.split(' ');
    const minutes = parseInt(parts[1].substring(2));
    const hours = parseInt(parts[2].substring(2));
    const day = parseInt(parts[3].substring(2));
    return { hours, minutes, day };
}

function dayconventor(day) {
    const daysMap = {
        MON: 1,
        TUE: 2,
        WED: 3,
        THU: 4,
        FRI: 5,
        SAT: 6,
        SUN: 0
    };

    const days = day.split(',');
    days.forEach(day => {
        const checkboxId = `#defaultCheck-${daysMap[day]}`;
        $(checkboxId).prop("checked", true);
    });
}
function monthDayconventor(days) {
    let day = days && days.split(',');
    day = Array.from(day)

    day.forEach((day) => {
        $(`input[name="Days"][value="${day}"]`).prop('checked', true);
    });

};


function Get_ScheduleTypes() {

    var Scheduler_types = $('.nav-tabs .active').text().trim();
    switch (Scheduler_types) {
        case "Minutes":
            $('#textScheduleType').val(1);
            break;
        case "Hourly":
            $('#textScheduleType').val(2);
            break;
        case "Daily":
            $('#textScheduleType').val(3);
            break;
        case "Weekly":
            $('#textScheduleType').val(4);
            break;
        case "Monthly":
            $('#textScheduleType').val(5);
            break;
    }
}



function parseCronExpression(expression) {
    const parts = expression.split(' ');
    const minutes = parseInt(parts[1]);
    const hours = parseInt(parts[2].substring(2));
    const day = parts[5];
    return { hours, minutes, day };
}
function parseCronEveryExpression(expression) {
    const parts = expression.split(' ');
    const minutes = parseInt(parts[1]);
    const hours = parseInt(parts[2].substring(2));
    const day = parts2[5];
    return { hours, minutes, day };
}
function parseCronMonthExpression(expression) {
    const parts = expression.split(' ');
    const minutes = parseInt(parts[1]);
    const hours = parseInt(parts[2]);
    const month = parts[6] + "-" + parts[4];
    const days = parts[3];
    return { minutes, hours, month, days };
}

var monthInput = document.getElementById("bd_lblMonth");
var today = new Date();
// var currentYear = today.getFullYear();
var currentMonth = today.getMonth() + 1;
var minMonth = currentYear + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
var maxMonth = (currentYear + 77) + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
monthInput.setAttribute("min", minMonth);
monthInput.setAttribute("max", maxMonth);

var today = new Date().toLocaleString('sv-SE', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' }).replace(' ', 'T');
document.getElementsByName("datetime_currentdate")[0].min = today;


var datetimeInput = $('#datetimeCron');


var minYear = new Date().getFullYear();
var maxYear = new Date().getFullYear() + 76;

var minDate = new Date(minYear, 0, 1, 0, 0).toLocaleString('sv-SE', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' }).replace(' ', 'T');
var maxDate = new Date(maxYear, 11, 31, 23, 59).toLocaleString('sv-SE', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' }).replace(' ', 'T');
datetimeInput.attr('min', minDate);
datetimeInput.attr('max', maxDate);

function populateModalFields(Backupdata) {
    
    var scheduleTime = Backupdata?.scheduleTime?.split(" ")
    if (Backupdata?.scheduleTime?.includes("MON") == true) {
        $("#defaultCheck-1").prop("checked", true)
    }
    if (Backupdata?.scheduleTime?.includes("TUE") == true) {
        $("#defaultCheck-2").prop("checked", true)
    }
    if (Backupdata?.scheduleTime?.includes("WED") == true) {
        $("#defaultCheck-3").prop("checked", true)
    }
    if (Backupdata?.scheduleTime?.includes("THU") == true) {
        $("#defaultCheck-4").prop("checked", true)
    }
    if (Backupdata?.scheduleTime?.includes("FRI") == true) {
        $("#defaultCheck-5").prop("checked", true)
    }
    if (Backupdata?.scheduleTime?.includes("SAT") == true) {
        $("#defaultCheck-6").prop("checked", true)
    }
    if (Backupdata?.scheduleTime?.includes("SUN") == true) {
        $("#defaultCheck-0").prop("checked", true)
    }

    $("#ddlHours").val(scheduleTime[0] + ":" + scheduleTime[2])

    var backUpTypedata = Backupdata.backUpType;

    if (backUpTypedata === 'One Week') {
        $("#option1").prop("checked", true);
    } else if (backUpTypedata === 'One Month') {
        $("#option2").prop("checked", true);
    } else if (backUpTypedata === 'Three Month') {
        $("#option3").prop("checked", true);
    } else if (backUpTypedata === 'Six Month') {
        $("#option4").prop("checked", true);
    } else if (backUpTypedata === 'One Year') {
        $("#option5").prop("checked", true);
    }

}
function ClearErrorElements(errorElements) {
    errorElements.forEach(element => {
        $(element).text('').removeClass('field-validation-error');
    });

}
const errorElements = ['#Ftpusername-error,#Ftphostname-error,#Ftppassword-error,#Ftpport-error,#Ftpsource-error,#Ftptarget-error,#CronMin-error,#CronHourly-error,#CronHourMin-error,#CronExpression-error,#Crondaysevery-error,#CroneveryHour-error,#CroneveryMin-error,#CronDay-error,#CronMonthMins-error,#CronddlHour-error,#CronMonthHrs-error,#CronMonth-error,#CronMonthlyDay-error,#Backup-error'];
const errorElementt = ['#CronMin-error,#CronHourly-error,#CronHourMin-error,#CronExpression-error,#Crondaysevery-error,#CroneveryHour-error,#CroneveryMin-error,#CronDay-error,#CronMonthMins-error,#CronddlHour-error,#CronMonthHrs-error,#CronMonth-error,#CronMonthlyDay-error'];



$('.nav-link').on('click', function () {
    
    $('#btnsave').text("save");
    clearCronExpressionData();
    ClearErrorElements(errorElementt);
});

const clearCronExpressionData = () => {
    $('#bd_txtMins').val('');
    $('#bd_txtHours').val('');
    $('#bd_txtMinutes').val('');
    $('#bd_ddlHours').val('');
    $('#ddlMinutes').val('');
    //$('#bd_everyHours').val('');
    //$('#bd_everyMinutes').val('');
    $('#everyHours').val('') 
    $('input[name=weekDays]').prop("checked", false)
    $('input[name=Days]').prop("checked", false)
    $('input[name=daysevery1]').prop("checked", false)
    $("#option1").prop("checked", true);
    $("#option3,#option2,#option4,#option5").prop("checked", false);
    $('#datetimeCron').val('');
    $('#textCronExpression').val('');
    $('#bd_lblMonth').val('');
    $('#bd_txtHourss').val('');
    $('#bd_txtMinss').val('');
    $('#MonthlyHours').val('');
};

$('#btnCreate').on('click', function () {   
    getBackupConfigDetails()
})

$('#ScheduleId').on('click', function () {
   
    clearCronExpressionData();
    ClearErrorElements(errorElementt);
  
    //getBackupConfigDetails()
})


async function validateUserName(value) {
    const errorElement = $('#Ftpusername-error');

    if (!value) {
        errorElement.text('Enter FTP username').addClass('field-validation-error');

        return false;

    } const validationResults = [
        await SpecialCharValidate(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await ShouldNotBeginWithNumber(value),
    ];

    return await CommonValidation(errorElement, validationResults);
    return true;
}

async function validatePassword(value) {
    const errorElement = $('#Ftppassword-error');

    if (!value) {
        errorElement.text('Enter FTP password').addClass('field-validation-error');

        return false;

    } else {
        errorElement.text('').removeClass('field-validation-error');

    }

    return true;
}


async function validatePort(value) {
    const errorElement = $('#Ftpport-error');

    if (!value) {
        errorElement.text('Enter FTP port').addClass('field-validation-error');

        return false;

    }
    const validationResults = [
        await PortReg(value),

    ];

    return await CommonValidation(errorElement, validationResults);

    return true;
}


async function validateSourcePath(value) {
    const errorElement = $('#Ftpsource-error');

    if (!value) {
        errorElement.text('Enter source path').addClass('field-validation-error');

        return false;

    } const validationResults = [
        await BackupSourcefilepath(value),

    ];

    return await CommonValidation(errorElement, validationResults);

    return true;
}


async function validateTargetPath(value) {
    const errorElement = $('#Ftptarget-error');

    if (!value) {
        errorElement.text('Enter target path').addClass('field-validation-error');

        return false;

    }

    const validationResults = [
        await BackupTargetfilepath(value),

    ];

    return await CommonValidation(errorElement, validationResults);

    return true;
}


//$(document).ready(function () {
$("input[name='switchPlan']").on('change', function () {
    
    let backuptextpath = $('#txtbackuppath').val();
    let ftpchange = $('#ftphostname').val();

    var backupschedule = $(this).val();
    if (backupschedule.toLowerCase() === "once" && (backuptextpath !== "" || ftpchange !== "")) {
        $('#btnExecute').prop('disabled', false);
        $('#btnSave').prop('disabled', false);
        $('#pills-profile').hide();
    } else {
        $('#btnExecute').prop('disabled', true);
       // $('#btnSave').prop('disabled', true);
        $('#pills-profile').show();
    }
});

function switchPlanHandler() {
    
    if ($('#switchMonthly').is(':checked')) {
        $('#pills-profile').show();
        //$('#ftphostname').val('');
        //$('#ftpusername').val('');
        //$('#ftppassword').val('');
        //$('#ftpport').val('');
        //$('#cpurl').val('');
        //$('#ftpurl').val('');
    } else {
        $('#pills-profile').hide();
        //  $('#txtbackuppath').val('');
        $('#btnExecute').prop('disabled', true);
       // $('#btnSave').prop('disabled', true);
    }
}

function inlineRadioOptionsHandler() {

    if ($('#inlineRadio2').prop('checked')) {
        $('#pills-Local').show();
        $('#backuphidepath').hide();
        // $('#txtbackuppath').val('');
        $('#btnExecute').prop('disabled', true);
       // $('#btnSave').prop('disabled', false);
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    }

    else if ($('#inlineRadio1').prop('checked')) {
        //$('#ftphostname').val('');
        //$('#ftpusername').val('');
        //$('#ftppassword').val('');
        //$('#ftpport').val('');
        //$('#cpurl').val('');
        //$('#ftpurl').val('');
        $('#pills-Local').hide();
        $('#backuphidepath').show();
        $('#btnExecute').prop('disabled', true);
       // $('#btnSave').prop('disabled', false);
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    }


}

switchPlanHandler();
inlineRadioOptionsHandler();

$('input[name="switchPlan"]').on('change', switchPlanHandler);
$('input[name="inlineRadioOptions"]').on('change', inlineRadioOptionsHandler);
$('input[name="inlineRadioOptions"]:checked').trigger('change');

//});
function GetIsSchedule() {
    var schedule_type = document.querySelector('input[name = "switchPlan"]:checked');
    if (schedule_type.value === "Once") {
        $('#ChkValue').val(1);
    } else {
        $('#ChkValue').val(2);
    }
}
function formatDateTime(datetime) {
    const dateObj = new Date(datetime);
    const formattedDate = `${dateObj.getDate().toString().padStart(2, '0')}-${(dateObj.getMonth() + 1).toString().padStart(2, '0')}-${dateObj.getFullYear()}`;
    const formattedTime = `${dateObj.getHours().toString().padStart(2, '0')}:${dateObj.getMinutes().toString().padStart(2, '0')}:${dateObj.getSeconds().toString().padStart(2, '0')}`;
    return `${formattedDate} ${formattedTime}`;
}

function getBackupDetails() {

    $('#btnSave').prop('disabled', true);
    
    $.ajax({
        type: "GET",
        url: "/Admin/BackupData/GetList",
        async: true,
        success: function (response) {

            if (response.length > 0) {

                if ($("#ChkValue")?.val(backupType) === "Cycle") {
                    populateModalFields(response[0]);
                }

                $('#btnSave').text('Update');
                $('#btnSave').prop('disabled', false);
                $('#txthostname').val(response[0].hostName);
                $('#txtdatabasename').val(response[0].databaseName);
                $('#txtusername').val(response[0].userName);
                $('#txtpassword').val(response[0].password);
                $('#inlineRadio1').prop('checked', response[0].isLocalServer);
                $('#inlineRadio2').prop('checked', response[0].isBackUpServer);
                $('#txtbackuppath').val(response[0].backUpPath);
                var backupType = response[0].backUpType;
                var keepBackupLast = response[0].keepBackUpLast;
                $("#ChkValue").val(backupType)
                $("input[name='switchPlan'][value='" + backupType + "']").prop('checked', true);
                var selectedRadioValue = $("input[name='inlineRadioOptions']:checked").val();

                $("#selectbackup").val(keepBackupLast).trigger('change');

                let ftpProperties = JSON.parse(response[0].properties);
                setTimeout(() => {
                    $('#ftphostname').val(ftpProperties.ftphostName);
                    $('#ftpusername').val(ftpProperties.ftpuserName);
                    $('#ftppassword').val(ftpProperties.ftphostPassword);
                    $('#ftpport').val(ftpProperties.ftpport);
                    $('#cpurl').val(ftpProperties.sourcepath);
                    $('#ftpurl').val(ftpProperties.targetpath);
                    $('#txtserverid').val(response[0].id);
                }, 3000)
               

                switch (response[0].scheduleType) {
                    case "1":
                        $('#nav-Minutes-tab').click();
                        const { minutes } = parseMinCronExpression(response[0].cronExpression);
                        $('#bd_txtMins').val(minutes);
                        break;
                    case "2":
                        $('#nav-Hourly-tab').click();
                        const { hours: bdhours, minutes: bdminutes } = parseCronExpression(response[0].cronExpression);
                        $('#bd_txtHours').val(bdhours);
                        $('#bd_txtMinutes').val(bdminutes);
                        break;
                    case "3":
                        $('#nav-Daily-tab').click();
                        //const { hours: ehours, minutes: eminutes, day } = parseCronExpression(response[0].cronExpression);
                        //$('#bd_everyHours').val(ehours);
                        //$('#bd_everyMinutes').val(eminutes);
                        //if (day == "?") {
                        //    $("#defaultCheck-everyday").prop("checked", true);
                        //}
                        //else {
                        //    $("#defaultCheck-MON-FRI").prop("checked", true);
                        //}
                        //break;
                        setTimeout(() => {

                            //clickedLink = document.getElementById(linkId);
                            //clickedLink.click();
                            let { hours, minutes, day } = parseCronExpression(response[0].cronExpression);
                            hours = String(hours)
                            minutes = String(minutes)
                            let formattedTime = `${hours.length === 1 ? `0${hours}` : hours}:${minutes.length === 1 ? `0${minutes}` : minutes}`;

                            const everyHoursInput = document.getElementById("everyHours");

                            if (everyHoursInput) {
                                everyHoursInput.value = formattedTime;
                            }

                            if (day == "?") {
                                $("#defaultCheck-everyday").prop("checked", true);
                            }
                            else {
                                $("#defaultCheck-MON-FRI").prop("checked", true);
                            }
                        }, 150)
                        break;
                    case "4":
                        $('#nav-Weekly-tab').click();
                        const { hours: dhours, minutes: dminutes, day: dday } = parseCronExpression(response[0].cronExpression);
                        $('#bd_ddlHours').val(`${dhours}:${dminutes}`);
                        dayconventor(dday);
                        break;
                    case "5":
                        $('#nav-Monthly-tab').click();

                        //const { hours: mdhours, minutes: mdminutes, month, days } = parseCronMonthExpression(response[0].cronExpression);
                        //$('#bd_txtHourss').val(mdhours);
                        //$('#bd_txtMinss').val(mdminutes);
                        //$('#bd_lblMonth').val(month);
                        //monthDayconventor(days);
                        //let monthDay = month.split('-')
                        //monthcheck = monthDay.length && monthDay[1]
                        //updateCheckboxes()
                        setTimeout(() => {
                            //clickedLink = document.getElementById(linkId);
                            //clickedLink.click();

                            let { hours, minutes, month, days } = parseCronMonthExpression(response[0].cronExpression);
                            hours = String(hours)
                            minutes = String(minutes)
                            let getHrsAndMins = `${hours.length === 1 ? `0${hours}` : hours}:${minutes.length === 1 ? `0${minutes}` : minutes}`
                            document.getElementById("MonthlyHours").value = `${getHrsAndMins}`;
                            document.getElementById("bd_lblMonth").value = month;
                            monthDayconventor(days);
                            let monthDay = month.split('-')
                            monthcheck = monthDay.length && monthDay[1]
                            updateCheckboxes()
                        }, 150)
                        break;
                
                }


                $('input[name="inlineRadioOptions"]:checked').trigger('change');

                if (selectedRadioValue === 'isremote' && response[0].properties.ftphostName === "") {


                    $('#btnExecute').prop('disabled', true);
                    $('#btnSave').prop('disabled', false);
                } else {

                    $('#btnExecute').prop('disabled', false);
                    $('#btnSave').prop('disabled', true);
                }
                if (selectedRadioValue === 'islocal' && response[0].backUpPath === "") {
                    $('#btnExecute').prop('disabled', true);
                    $('#btnSave').prop('disabled', false);
                } else {

                    $('#btnExecute').prop('disabled', false);
                    $('#btnSave').prop('disabled', true);
                }
                if ((backupType?.toLowerCase() === "once")) {

                    $('#pills-profile').hide();
                    $('#btnExecute').prop('disabled', false);
                    $('#btnSave').prop('disabled', true);
                } else {
                    $('#pills-profile').show();
                    $('#btnExecute').prop('disabled', true);
                    $('#btnSave').prop('disabled', false);
                }
            } else {
                $('#btnSave').text('Save');
                $('#btnSave').prop('disabled', false);
            }

        },
        error: function (xhr, status, error) {

        }
    });
}

function backupDebounce(func, delay = 300) {
    let timer;
    return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
            func.apply(context, args);
        }, delay);
    };
}


