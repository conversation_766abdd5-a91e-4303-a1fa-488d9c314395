﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;

namespace ContinuityPatrol.Persistence.Repositories;

public class ServerTypeRepository : BaseRepository<ServerType>, IServerTypeRepository
{
    private readonly ApplicationDbContext _dbContext;

    public ServerTypeRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
        _dbContext = dbContext;
    }
    public override async Task<IReadOnlyList<ServerType>> ListAllAsync()
    {
        return await SelectServerType(_dbContext.ServerTypes.Active().AsNoTracking())
            .ToListAsync();
    }
    public async Task<ServerType> GetServerTypeById(string id)
    {
        return await _dbContext.ServerTypes
            .Active()
            .Where(s => s.ReferenceId.Equals(id))
            .FirstOrDefaultAsync();
    }

    public Task<bool> IsServerTypeNameExist(string serverTypeName, string serverTypeId)
    {
        return !serverTypeId.IsValidGuid()
            ? Task.FromResult(_dbContext.ServerTypes.Any(e => e.Name.Equals(serverTypeName)))
            : Task.FromResult(_dbContext.ServerTypes.Where(e => e.Name.Equals(serverTypeName)).ToList()
                .Unique(serverTypeId));
    }

    public async Task<bool> IsServerTypeNameUnique(string name)
    {
        return await _dbContext.ServerTypes.AnyAsync(e => e.Name.Equals(name));
    }

    public async Task<List<ServerType>> GetServerTypeListByName(string name)
    {
        var result = await _dbContext.ServerTypes.Active()
            .Where(x => x.Name.Trim().ToLower().Equals(name.Trim().ToLower()))
            .ToListAsync();

        return result;
    }
    private static IQueryable<ServerType> SelectServerType(IQueryable<ServerType> query)
    {
        return query.Select(x => new ServerType
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Logo = x.Logo,
            Name = x.Name
        });
    }
}