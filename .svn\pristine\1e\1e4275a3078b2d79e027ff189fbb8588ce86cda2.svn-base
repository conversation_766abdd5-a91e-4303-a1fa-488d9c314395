namespace ContinuityPatrol.Domain.Entities;

public class DriftResourceSummary : AuditableEntity
{
    public string CompanyId { get; set; }
	public string InfraObjectId { get; set; }
    [Column(TypeName = "NCLOB")] public string ParameterName { get; set; }
    public string EntityName { get; set; }
	public string Type { get; set; }
	public string TypeId { get; set; }
	public int TotalCount { get; set; }
	public int ConflictCount { get; set; }
	public int NonConflictCount { get; set; }
    public bool IsConflict { get; set; }
    [Column(TypeName = "NCLOB")] public string Logo { get; set; }
		
}
