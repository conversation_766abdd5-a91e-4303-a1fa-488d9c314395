﻿namespace ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.WithDrawRequest;

public class WithDrawApprovalRequestQueryHandler : IRequestHandler<WithDrawApprovalRequestQuery, string>
{
    private readonly IApprovalMatrixRepository _approvalMatrixRepository;
    private readonly IMapper _mapper;

    public WithDrawApprovalRequestQueryHandler(IMapper mapper, IApprovalMatrixRepository approvalMatrixRepository)
    {
        _mapper = mapper;
        _approvalMatrixRepository = approvalMatrixRepository;
    }

    public async Task<string> Handle(WithDrawApprovalRequestQuery request, CancellationToken cancellationToken)
    {
        var entity = await _approvalMatrixRepository.GetByReferenceIdAsync(request.MatrixId);
        var status = await _approvalMatrixRepository.DeleteAsync(entity);
        return "Success";
    }
}