﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using Microsoft.Extensions.Configuration;
using System.Data;
using System.Reflection;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ActiveDirectoryMonitorLogRepositoryTests : IClassFixture<ActiveDirectoryMonitorLogFixture>
{
    private readonly ActiveDirectoryMonitorLogFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly Mock<IConfiguration> _configMock= new();
    private readonly ActiveDirectoryMonitorLogRepository _repository;

    public ActiveDirectoryMonitorLogRepositoryTests(ActiveDirectoryMonitorLogFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _configMock = ConfigurationRepositoryMocks.GetConnectionString();
        _repository = new ActiveDirectoryMonitorLogRepository(_dbContext, _configMock.Object);
    }

    [Fact]
    public async Task GetDetailByType_ReturnsLogs_WhenTypeExists()
    {
        var log = _fixture.ActiveDirectoryMonitorLogDto;
        log.Type = "TestType";
        _dbContext.ActiveDirectoryMonitorLogs.Add(log);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetDetailByType("TestType");

        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("TestType", result[0].Type);
    }

    [Fact]
    public async Task GetDetailByType_ReturnsEmpty_WhenTypeDoesNotExist()
    {
        var result = await _repository.GetDetailByType("NonExistentType");
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ReturnEmpty_While_Pass_Null()
    {
        var result = await _repository.GetDetailByType(null);

        Assert.NotNull(result);
        Assert.Empty(result);
    }



    [Fact]
    public async Task GetByInfraObjectId_ReturnsLogs_WhenDataExists()
    {
        var log = _fixture.ActiveDirectoryMonitorLogDto;
        log.InfraObjectId = "infra1";
        log.CreatedDate = DateTime.UtcNow.Date;
        _dbContext.ActiveDirectoryMonitorLogs.Add(log);
        await _dbContext.SaveChangesAsync();

        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        var repo = new TestableActiveDirectoryMonitorLogRepository(_dbContext, _configMock.Object, false);

        var result = await repo.GetByInfraObjectId("infra1", startDate, endDate);

        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("infra1", result[0].InfraObjectId);
    }

    [Fact]
    public async Task GetByInfraObjectId_ReturnsEmpty_WhenNoData()
    {
        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");
        var repo = new TestableActiveDirectoryMonitorLogRepository(_dbContext, _configMock.Object, false);

        var result = await repo.GetByInfraObjectId("infra1", startDate, endDate);

        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_Return_Empty_Rows_WhenInfraObjectIdIsNull()
    {
        var log = _fixture.ActiveDirectoryMonitorLogList;

        _dbContext.ActiveDirectoryMonitorLogs.AddRange(log);
        _dbContext.SaveChanges();
        var res = await _repository.ListAllAsync();

        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        var repo = new TestableActiveDirectoryMonitorLogRepository(_dbContext, _configMock.Object, false);

        var result = await repo.GetByInfraObjectId(null, startDate, endDate);


        Assert.NotNull(result);
        Assert.Equal(0, result?.Count);
    }
    private string? InvokeGetDatabaseNameFromConnectionString(string connectionString, string provider)
    {
        var method = typeof(ActiveDirectoryMonitorLogRepository)
            .GetMethod("GetDatabaseNameFromConnectionString", BindingFlags.NonPublic | BindingFlags.Instance);

        if (method == null)
        {
            throw new InvalidOperationException("The method 'GetDatabaseNameFromConnectionString' could not be found.");
        }

        var result = method.Invoke(_repository, new object[] { connectionString, provider });
        return result as string;
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ReturnsDatabase_ForMySql()
    {
        var conn = "Server=localhost;Database=testdb;Uid=root;Pwd=****;";
        var result = InvokeGetDatabaseNameFromConnectionString(conn, "mysql");
        Assert.NotNull(result);
        Assert.Equal("testdb", result);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ReturnsDatabase_ForNpgsql()
    {
        var conn = "Host=localhost;Database=pgdb;Username=postgres;Password=****;";
        var result = InvokeGetDatabaseNameFromConnectionString(conn, "npgsql");
        Assert.NotNull(result);
        Assert.Equal("pgdb", result);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ReturnsDatabase_ForMssql()
    {
        var conn = "Server=localhost;Database=sqlserverdb;User Id=sa;Password=****;";
        var result = InvokeGetDatabaseNameFromConnectionString(conn, "mssql");
        Assert.NotNull(result);
        Assert.Equal("sqlserverdb", result);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ReturnsUserId_ForOracle()
    {
        var conn = "Data Source=localhost:1521/ORCL;User Id=ORACLEUSER;Password=****;";
        var result = InvokeGetDatabaseNameFromConnectionString(conn, "oracle");
        Assert.NotNull(result);
        Assert.Equal("ORACLEUSER", result);
    }
    [Fact]
    public void GetTableName_ReturnsCorrectTableName_ForActiveDirectoryMonitorLog()
    {
        // Act
        var tableName = _repository.GetTableName<ActiveDirectoryMonitorLog>();

        // Assert
        Assert.False(string.IsNullOrWhiteSpace(tableName));
        Assert.Equal("ACTIVEDIRECTORY_MONITOR_LOGS", tableName);
    }

    [Fact]
    public void GetTableName_ReturnsNull_ForUnmappedType()
    {
       
        var tableName = _repository.GetTableName<UnmappedEntity>();

        Assert.Null(tableName);
    }
    private class UnmappedEntity { }

    //[Fact]
    //public async Task IsTableExistAsync_ReturnsTrue_WhenTableExists()
    //{

    //    var repo = new TestableActiveDirectoryMonitorLogRepository(_dbContext, _configMock.Object, true);

    //    var result = await _repository.IsTableExistAsync("TestTable", "TestSchema", "mssql");

    //    // Assert
    //    Assert.True(result);
    //}

    //[Fact]
    //public async Task IsTableExistAsync_ReturnsFalse_WhenTableDoesNotExist()
    //{

    //    var repo = new TestableActiveDirectoryMonitorLogRepository(_dbContext, _configMock.Object, false);

    //    var result = await _repository.IsTableExistAsync("MissingTable", "TestSchema", "mssql");

    //    // Assert
    //    Assert.False(result);
    //}



    // Helper class to override IsTableExistAsync for testing
    private class TestableActiveDirectoryMonitorLogRepository : ActiveDirectoryMonitorLogRepository
    {
        private readonly bool _tableExists;
        public TestableActiveDirectoryMonitorLogRepository(ApplicationDbContext dbContext, IConfiguration config, bool tableExists)
            : base(dbContext, config)
        {
            _tableExists = tableExists;
        }

    }
}