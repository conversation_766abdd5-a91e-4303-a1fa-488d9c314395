﻿using ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowActionType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Orchestration;

public class WorkflowActionTypeService : BaseClient, IWorkflowActionTypeService
{
    public WorkflowActionTypeService(IConfiguration config, IAppCache cache, ILogger<WorkflowActionTypeService> logger) : base(config, cache, logger)
    {

    }

    public async Task<List<WorkflowActionTypeListVm>> GetWorkflowActionList()
    {
        var request = new RestRequest("api/v6/workflowactiontype");

        return await Get<List<WorkflowActionTypeListVm>>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowActionTypeCommand createWorkflowActionTypesCommand)
    {
        var request = new RestRequest("api/v6/workflowactiontype", Method.Post);

        request.AddJsonBody(createWorkflowActionTypesCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowActionTypeCommand updateWorkflowActionTypeCommand)
    {
        var request = new RestRequest("api/v6/workflowactiontype", Method.Put);

        request.AddJsonBody(updateWorkflowActionTypeCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/workflowactiontype/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<PaginatedResult<WorkflowActionTypeListVm>> GetWorkflowActionTypePaginatedList(GetWorkflowActionTypePaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/workflowactiontype/paginated-list{query}");

        return await Get<PaginatedResult<WorkflowActionTypeListVm>>(request);
    }

    public async Task<bool> IsWorkflowActionTypeExist(string actionType, string? id)
    {
        var request = new RestRequest($"api/v6/workflowactiontype/name-exist?loginName={actionType}&id={id}");

        return await Get<bool>(request);
    }
}
