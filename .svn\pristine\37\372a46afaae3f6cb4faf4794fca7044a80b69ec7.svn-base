﻿using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Domain.ViewModels.UserGroupModel;
using ContinuityPatrol.Domain.ViewModels.UserModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Domain.ViewModels.ApprovalMatrixModel;

public class FourEyeModel
{
    public string Id { get; set; }

    public string UserId { get; set; }
    public string UserName { get; set; }
    public string UserNames { get; set; }
    public string GroupName { get; set; }
    public string GroupNames { get; set; }

    public string Name { get; set; }
    public string Description { get; set; }
    public DateTime Hours { get; set; }
    public DateTime Mins { get; set; }

    public string NotificationType { get; set; }

    //public string Properties { get; set; }
    public string Emails { get; set; }
    public string Rule { get; set; }
    public bool WorkflowModification { get; set; }
    public bool WorkflowProfileExecution { get; set; }
    public bool ProfileModification { get; set; }

    public string WorkflowApprovers { get; set; }
    public string WorkflowApproversGroup { get; set; }
    public string ProfileModificationApprovers { get; set; }
    public string ProfileModificationApproversGroup { get; set; }
    public string ProfileExecutionApproversGroup { get; set; }
    public string ProfileExecutionApprovers { get; set; }
    public List<UserGroupListVm> UserGroup { get; set; }

    public List<WorkflowNameVm> Workflows { get; set; }
    public List<BusinessServiceListVm> BusinessServiceList { get; set; }

    public PaginatedResult<WorkflowProfileListVm> WorkflowProfiles { get; set; }
    public List<UserNameVm> UsersList { get; set; }

    public override string ToString()
    {
        return $"Name: {Name};";
    }
}