﻿using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Create;
using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Delete;
using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Update;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetList;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetNames;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.SingleSignOnModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class SingleSignOnsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<SingleSignOnListVm>>> GetSingleSignOns()
    {
        Logger.LogDebug("Get All SingleSignOns ");

        //return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllSingleSignOnCacheKey + LoggedInUserService.CompanyId, () => Mediator.Send(new GetSingleSignOnListQuery()), CacheExpiry));

        return Ok(await  Mediator.Send(new GetSingleSignOnListQuery()));
    }

    [HttpGet, Route("by/{typeId}")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<SingleSignOnTypeVm>>> GetSingleSignOnByType(string typeId)
    {
        //Guard.Against.NullOrWhiteSpace(type, "SingleSignOn Type");

        Logger.LogDebug($"Get SingleSignOn Details by Type Id '{typeId}'");

        return Ok(await Mediator.Send(new GetSingleSignOnTypeQuery { SignOnTypeId = typeId }));
    }

    [HttpGet("{id}", Name = "GetSingleSignOn")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<SingleSignOnDetailVm>> GetSingleSignOnById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "SingleSignOn Id");

        Logger.LogDebug($"Get SingleSignOn Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetSingleSignOnDetailQuery { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<SingleSignOnListVm>>> GetPaginatedSingleSignOns([FromQuery] GetSingleSignOnPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in SingleSignOn Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateSingleSignOnResponse>> CreateSingleSignOn([FromBody] CreateSingleSignOnCommand createSingleSignOnCommand)
    {
        Logger.LogDebug($"Create SingleSignOn '{createSingleSignOnCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateSingleSignOn), await Mediator.Send(createSingleSignOnCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateSingleSignOnResponse>> UpdateSingleSignOn([FromBody] UpdateSingleSignOnCommand updateSingleSignOnCommand)
    {
        Logger.LogDebug($"Update SingleSignOn '{updateSingleSignOnCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateSingleSignOnCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteSingleSignOnResponse>> DeleteSingleSignOn(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "SingleSignOn Id");

        Logger.LogDebug($"Delete SingleSignOn Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteSingleSignOnCommand { Id = id }));
    }

    [HttpGet, Route("names")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<SingleSignOnNameVm>>> GetSingleSignOnNames()
    {
        Logger.LogDebug("Get All SingleSignOn Names");

        //return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllReplicationNameCacheKey,
        //    () => Mediator.Send(new GetSingleSignOnNameQuery()), CacheExpiry));

        return Ok(await Mediator.Send(new GetSingleSignOnNameQuery()));
    }

    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsSingleSignOnNameExist(string profileName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(profileName, "Profile Name");

        Logger.LogDebug($"Check ProfileName Exists Detail by SingleSignOn Profile Name'{profileName}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetSingleSignOnNameUniqueQuery { ProfileName = profileName, SingleSignOnId = id }));
    }
    [NonAction]
    public override void ClearDataCache()
    {
        var cacheKeys = new[] { ApplicationConstants.Cache.AllSingleSignOnCacheKey + LoggedInUserService.CompanyId,
            ApplicationConstants.Cache.AllSingleSignOnNameCacheKey};

        ClearCache(cacheKeys);
    }
}