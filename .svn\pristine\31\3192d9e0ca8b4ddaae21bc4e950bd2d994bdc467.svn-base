﻿using ContinuityPatrol.Application.Features.ManageWorkflow.Queries.GetManagedWorkflow;
using ContinuityPatrol.Application.Features.Workflow.Commands.Create;
using ContinuityPatrol.Application.Features.Workflow.Commands.Delete;
using ContinuityPatrol.Application.Features.Workflow.Commands.Lock;
using ContinuityPatrol.Application.Features.Workflow.Commands.Publish;
using ContinuityPatrol.Application.Features.Workflow.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Workflow.Commands.Update;
using ContinuityPatrol.Application.Features.Workflow.Commands.Verify;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetCpslScript;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetDetailByActionName;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetList;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetManagedWorkflowList;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetNames;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetWorkflowActionById;
using ContinuityPatrol.Domain.ViewModels.ManageWorkflow;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Orchestration;

public class WorkflowService : BaseService, IWorkflowService
{
    public WorkflowService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<CreateWorkflowResponse> CreateAsync(CreateWorkflowCommand createWorkflow)
    {
        Logger.LogDebug($"Create Workflow '{createWorkflow.Name}'");

        return await Mediator.Send(createWorkflow);
    }

    public async Task<BaseResponse> DeleteAsync(string workflowId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowId, "Workflow Id");

        Logger.LogDebug($"Delete Workflow Details by Id '{workflowId}'");

        return await Mediator.Send(new DeleteWorkflowCommand { Id = workflowId });
    }

    public async Task<UpdateWorkflowResponse> UpdateAsync(UpdateWorkflowCommand updateWorkflow)
    {
        Logger.LogDebug($"Update Workflow '{updateWorkflow.Name}'");

        return await Mediator.Send(updateWorkflow);
    }

    public async Task<BaseResponse> UpdateWorkflowLock(UpdateWorkflowLockCommand workflowLock)
    {
        Logger.LogDebug($"Update Workflow lock '{workflowLock.Id}'");

        return await Mediator.Send(workflowLock);
    }

    public async Task<BaseResponse> UpdateWorkflowPublish(UpdateWorkflowPublishCommand workflowPublish)
    {
        Logger.LogDebug($"Update Workflow publish '{workflowPublish.Id}'");

        return await Mediator.Send(workflowPublish);
    }

    public async Task<BaseResponse> UpdateIsVerify(UpdateWorkflowVerifyCommand command)
    {
        Logger.LogDebug($"update Workflow Freeze'{command.Name}'");

        return await Mediator.Send(command);
    }

    public async Task<WorkflowDetailVm> GetByReferenceId(string workflowId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowId, "Workflow Id");

        Logger.LogDebug($"Get Workflow Detail by Id '{workflowId}'");

        return await Mediator.Send(new GetWorkflowDetailQuery { Id = workflowId });
    }

    public async Task<List<GetWorkflowActionByIdVm>> GetWorkflowActionByWorkflowIdAndGroupId(string workflowId,
        string? workflowOperationGroupId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowId, "Workflow Id");

        Logger.LogDebug($"Get Workflow Detail by Id '{workflowId}'");

        return await Mediator.Send(new GetWorkflowActionByIdQuery
            { WorkflowId = workflowId, WorkflowOperationGroupId = workflowOperationGroupId });
    }

    public async Task<List<WorkflowListVm>> GetWorkflowList()
    {
        Logger.LogDebug("Get All Workflow");

        return await Mediator.Send(new GetWorkflowListQuery());
    }

    public async Task<List<WorkflowNameVm>> GetWorkflowNames()
    {
        Logger.LogDebug("Get All Workflow Names");

        return await Mediator.Send(new GetWorkflowNameQuery());
    }

    public async Task<PaginatedResult<WorkflowListVm>> GetPaginatedWorkflow(GetWorkflowPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Workflow Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<bool> IsWorkflowNameExist(string workflowName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(workflowName, "Workflow Name");

        Logger.LogDebug($"Check Name Exists Detail by Workflow Name '{workflowName}' and id '{id}'");

        return await Mediator.Send(new GetWorkflowNameUniqueQuery { WorkflowName = workflowName, WorkflowId = id });
    }

    public async Task<SaveAsWorkflowResponse> SaveAsWorkflow(SaveAsWorkflowCommand saveAsWorkflowCommand)
    {
        Logger.LogDebug($"SaveAs Workflow '{saveAsWorkflowCommand.Name}'");

        return await Mediator.Send(saveAsWorkflowCommand);
    }

    public async Task<PaginatedResult<Manageworkflowlist>> GetManagedWorkflow(GetManagedWorkflowListQuery query)
    {
        Logger.LogDebug("==== Getting workflow details for Four Eye ====");

        return await Mediator.Send(query);
    }

    public async Task<List<ManageWorkflowModel>> GetManageWorkflows()
    {
        Logger.LogDebug("Get All Workflow Names");

        return await Mediator.Send(new GetDetailsManagedWorkflowListQuery());
    }

    public async Task<GetDetailByActionNameVm> GetDetailByActionName(GetDetailByActionNameQuery query)
    {
        Guard.Against.InvalidGuidOrEmpty(query.WorkflowId, "Workflow Id");

        Logger.LogDebug($"Get Workflow Detail by Id '{query.WorkflowId}'");

        return await Mediator.Send(query);
    }

    public async Task<GetCpslScriptDetailVm> GetCpslScript(GetCpslScriptDetailQuery query)
    {
        Logger.LogDebug($"Get Cpsl Script '{query.Script}' and  isSubstituteAuth :'{query.IsSubstituteAuth}'");

        return await Mediator.Send(query);
    }
}