﻿namespace ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterIdExist;

public class GetAlertMasterIdExistQueryHandler : IRequestHandler<GetAlertMasterIdExistQuery, bool>
{
    private readonly IAlertMasterRepository _alertMasterRepository;

    public GetAlertMasterIdExistQueryHandler(IAlertMasterRepository alertMasterRepository)
    {
        _alertMasterRepository = alertMasterRepository;
    }

    public async Task<bool> Handle(GetAlertMasterIdExistQuery request, CancellationToken cancellationToken)
    {
        var isExist = await _alertMasterRepository.IsAlertIdExist(request.AlertId);
        return isExist;
    }
}