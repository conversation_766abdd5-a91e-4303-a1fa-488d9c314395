﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title">
            <i class="cp-monitoring"></i>
            <span>
                EMC–ISILON : <span>123</span>
            </span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time :"><i class="cp-"></i>Last Monitored Time : <span></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title=""><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
    </div>
    <div class="monitor_pages">
        <div class="row g-2">
            <div class="col-12 col-lg-7">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        Server Details
                    </div>
                    <div class="card-body pt-1">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Component">Component</th>
                                    <th title="Primary" class="text-primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-server me-1"></i>Server Name</td>
                                    <td class="text-truncate"><span><i class="cp-fal-server me-1 text-success"></i>AODG 19C Ser Prod_1</span></td>
                                    <td class="text-truncate"><span><i class="cp-fal-server me-1 text-success"></i>AODG 19C Ser Prod_1</span></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-ip-address me-1"></i>IP Address/Hostname</td>
                                    <td class="text-truncate"><span>*************</span></td>
                                    <td class="text-truncate"><span>*************</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-12 col-lg-5">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center">
                        <img src="/img/charts_img/PageBuilder-Solution.svg" />
                    </div>
                </div>
            </div>
            <div class="col-12 mt-0">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        Replication Details
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Component">Parameter Name</th>
                                    <th title="Primary" class="text-primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-server me-1"></i>Isilon Cluster IP</td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-product-version-icon me-1"></i>Version</td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-health me-1"></i>Health</td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-veritas-cluster me-1"></i>Cluster Name</td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                </tr> 
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-group-policy me-1"></i>Policy Name</td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-ssh-private-key-path me-1"></i>Path</td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-mysql-data me-1"></i>State</td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                    <td class="text-truncate"><span><i class="cp-disable me-1 text-danger"></i>NA</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>