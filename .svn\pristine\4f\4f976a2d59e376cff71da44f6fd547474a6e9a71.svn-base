using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Archive.Commands.Create;

public class CreateArchiveCommandValidator : AbstractValidator<CreateArchiveCommand>
{
    private readonly IArchiveRepository _archiveRepository;

    public CreateArchiveCommandValidator(IArchiveRepository archiveRepository)
    {
        _archiveRepository = archiveRepository;

        RuleFor(p => p.ArchiveProfileName)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.TableNameProperties)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Must(prop => IsValidJsonObjcet(prop))
            .WithMessage("{PropertyName} must be a valid json string.");

        RuleFor(p => p.CronExpression)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull();

        RuleFor(p => p.ScheduleTime)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull();

        RuleFor(p => p.Type)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Must(type => type.Equals("count", StringComparison.OrdinalIgnoreCase) ||
                          type.Equals("period", StringComparison.OrdinalIgnoreCase))
            .WithMessage("select  {PropertyName}");


        RuleFor(p => p.ScheduleType).InclusiveBetween(1, 5).WithMessage("Select the {PropertyName}.")
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull();


        RuleFor(p => p.BackUpType)
            .NotEmpty().WithMessage("PropertyName} is Required.")
            .NotNull();

        RuleFor(p => p.ClearBackup)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Must(type => type.Equals("one month", StringComparison.OrdinalIgnoreCase) ||
                          type.Equals("one week", StringComparison.OrdinalIgnoreCase) ||
                          type.Equals("three month", StringComparison.OrdinalIgnoreCase) ||
                          type.Equals("six month", StringComparison.OrdinalIgnoreCase) ||
                          type.Equals("one year", StringComparison.OrdinalIgnoreCase))
            .WithMessage("select the {PropertyName}");

        RuleFor(p => p)
            .NotEmpty().NotNull().MustAsync(IsNameUnique).WithMessage("A same profile name already exist.");
    }

    private bool IsValidJsonObjcet(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                var obj = JsonConvert.DeserializeObject(properties);
                return true;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }

    private async Task<bool> IsNameUnique(CreateArchiveCommand p, CancellationToken cancellationToken)
    {
        return !await _archiveRepository.IsNameExist(p.ArchiveProfileName, string.Empty);
    }
}