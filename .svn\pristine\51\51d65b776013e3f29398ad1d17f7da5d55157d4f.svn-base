﻿namespace ContinuityPatrol.Application.Features.User.Queries.GetDomainUser;

public class GetDomainUserListQueryHandler : IRequestHandler<GetDomainUserListQuery, List<string>>
{
    private readonly IMapper _mapper;
    private readonly IUserRepository _userRepository;

    public GetDomainUserListQueryHandler(IMapper mapper, IUserRepository userRepository)
    {
        _mapper = mapper;
        _userRepository = userRepository;
    }

    public async Task<List<string>> Handle(GetDomainUserListQuery request, CancellationToken cancellationToken)
    {
        var userLoginName = request.DomainUserName.IsNotNullOrWhiteSpace()
            ? await _userRepository.GetDomainUsersByUserName(request.Name, request.DomainUserName)
            : await _userRepository.GetDomainUsers(request.Name);

        Guard.Against.NullOrDeactive(userLoginName, nameof(Domain.Entities.User),
            new NotFoundException(nameof(Domain.Entities.User), request.Name));

        return userLoginName;
    }
}