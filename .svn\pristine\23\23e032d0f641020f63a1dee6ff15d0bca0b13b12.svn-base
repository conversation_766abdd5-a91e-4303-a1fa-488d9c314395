using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.IsAttached;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetAirGapsStatus;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ICyberAirGapService
{
    Task<List<CyberAirGapListVm>> GetCyberAirGapList();
    Task<BaseResponse> CreateAsync(CreateCyberAirGapCommand createCyberAirGapCommand);
    Task<BaseResponse> UpdateAsync(UpdateCyberAirGapCommand updateCyberAirGapCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<CyberAirGapDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsCyberAirGapNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<CyberAirGapListVm>> GetPaginatedCyberAirGaps(GetCyberAirGapPaginatedListQuery query);
    #endregion
    Task<List<GetAirGapsStatusListVm>> GetAirGapsStatus();
    Task<BaseResponse> UpdateStatus(AirGapStatusUpdateCommand airGapStatusCommand);
    Task<BaseResponse> AirGapIsAttached(AirGapAttachedCommand airGapAttachedCommand);
}
