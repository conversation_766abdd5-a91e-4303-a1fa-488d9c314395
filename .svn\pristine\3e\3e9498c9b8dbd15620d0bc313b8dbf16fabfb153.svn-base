﻿using ContinuityPatrol.Application.Features.Replication.Queries.GetReplicationByLicensekey;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using System.ComponentModel;
using System.Text.Json;

namespace ContinuityPatrol.Application.UnitTests.Features.Replication.Queries;

public class GetReplicationByLicenseKeyQueryHandlerTests : IClassFixture<ReplicationFixture>
{
    private readonly ReplicationFixture _replicationFixture;
    private Mock<IReplicationRepository> _mockReplicationRepository;
    private Mock<IReplicationViewRepository> _mockReplicationViewRepository;
    private readonly GetReplicationByLicenseKeyQueryHandler _handler;

    public GetReplicationByLicenseKeyQueryHandlerTests(ReplicationFixture replicationFixture)
    {
        _replicationFixture = replicationFixture;

        _mockReplicationViewRepository = new Mock<IReplicationViewRepository>();

        _mockReplicationRepository = ReplicationRepositoryMocks.GetReplicationListByLicenseKey(_replicationFixture.Replications);

        _handler = new GetReplicationByLicenseKeyQueryHandler(_replicationFixture.Mapper, _mockReplicationViewRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Valid_ReplicationsByLicenseKey()
    {
        //var licenseqry = new Fixture().Create<GetReplicationByLicenseKeyQuery>();

        _replicationFixture.Replications[0].LicenseId = "f2e2cf3c-0650-45e7-b20f-2ba9eb409d03";


        var replication = new List<Domain.Entities.Replication>
        {


        };

        var result = await _handler.Handle(new GetReplicationByLicenseKeyQuery { LicenseId = _replicationFixture.Replications[0].LicenseId }, CancellationToken.None);

        result.ShouldBeOfType<List<GetReplicationByLicenseKeyListVm>>();

        result[0].Id.ShouldBe(_replicationFixture.Replications[0].ReferenceId);
        result[0].Name.ShouldBe(_replicationFixture.Replications[0].Name);
        result[0].CompanyId.ShouldBe(_replicationFixture.Replications[0].CompanyId);
        result[0].SiteId.ShouldBe(_replicationFixture.Replications[0].SiteId);
        result[0].Type.ShouldBe(_replicationFixture.Replications[0].Type);
        result[0].SiteName.ShouldBe(_replicationFixture.Replications[0].SiteName);
        result[0].Properties.ShouldBe(_replicationFixture.Replications[0].Properties);
        result[0].LicenseKey.ShouldBe(_replicationFixture.Replications[0].LicenseKey);
    }

    [Fact]
    public async Task Handle_Return_Active_ReplicationsCount()
    {

        var licenseqry = new Fixture().Create<GetReplicationByLicenseKeyQuery>();

        licenseqry.LicenseId = "f2e2cf3c-0650-45e7-b20f-2ba9eb409d03";


        var replication = new List<Domain.Entities.Replication>
        {


        };

        var result = await _handler.Handle(licenseqry, CancellationToken.None);

        result.ShouldBeOfType<List<GetReplicationByLicenseKeyListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {


        var licenseqry = new Fixture().Create<GetReplicationByLicenseKeyQuery>();

        licenseqry.LicenseId = "f2e2cf3c-0650-45e7-b20f-2ba9eb409d03";


        var replication = new List<Domain.Entities.Replication>
        {


        };

        //_mockReplicationRepository = ReplicationRepositoryMocks.GetReplicationEmptyRepository();

        var handler = new GetReplicationByLicenseKeyQueryHandler(_replicationFixture.Mapper, _mockReplicationViewRepository.Object);

        var result = await handler.Handle(licenseqry, CancellationToken.None);

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        var licenseqry = new Fixture().Create<GetReplicationByLicenseKeyQuery>();

        licenseqry.LicenseId = "f2e2cf3c-0650-45e7-b20f-2ba9eb409d03";


        var replication = new List<Domain.Entities.Replication> { 
           
            
        };

        _mockReplicationRepository.Setup(dp=>dp.GetReplicationListByLicenseKey(licenseqry.LicenseId)).ReturnsAsync(replication);

        await _handler.Handle(licenseqry, CancellationToken.None);

        _mockReplicationRepository.Verify(x => x.GetReplicationListByLicenseKey(It.IsAny<string>()), Times.Once);
    }
}