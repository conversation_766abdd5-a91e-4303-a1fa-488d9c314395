using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberAirGapStatus.Events.Create;

public class CyberAirGapStatusCreatedEventHandler : INotificationHandler<CyberAirGapStatusCreatedEvent>
{
    private readonly ILogger<CyberAirGapStatusCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberAirGapStatusCreatedEventHandler(ILoggedInUserService userService,
        ILogger<CyberAirGapStatusCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(CyberAirGapStatusCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} CyberAirGapStatus",
            Entity = "CyberAirGapStatus",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"CyberAirGapStatus '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"CyberAirGapStatus '{createdEvent.Name}' created successfully.");
    }
}