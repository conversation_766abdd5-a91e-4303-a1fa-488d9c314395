﻿namespace ContinuityPatrol.Application.Features.OracleMonitorStatus.Commands.Update;

public class
    UpdateOracleMonitorStatusCommandHandler : IRequestHandler<UpdateOracleMonitorStatusCommand,
        UpdateOracleMonitorStatusResponse>
{
    private readonly IMapper _mapper;
    private readonly IOracleMonitorStatusRepository _oracleMonitorStatusRepository;

    public UpdateOracleMonitorStatusCommandHandler(IMapper mapper,
        IOracleMonitorStatusRepository oracleMonitorStatusRepository)
    {
        _mapper = mapper;
        _oracleMonitorStatusRepository = oracleMonitorStatusRepository;
    }

    public async Task<UpdateOracleMonitorStatusResponse> Handle(UpdateOracleMonitorStatusCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _oracleMonitorStatusRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.OracleMonitorStatus), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateOracleMonitorStatusCommand),
            typeof(Domain.Entities.OracleMonitorStatus));

        await _oracleMonitorStatusRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateOracleMonitorStatusResponse
        {
            Message = Message.Update(nameof(Domain.Entities.OracleMonitorStatus), eventToUpdate.ReferenceId),
            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}