﻿using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.UpdateConditionActionId;
using ContinuityPatrol.Application.Features.CGExecutionReport.Queries.GetCgExecutionReportPaginatedList;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ICGExecutionReportService
{
    Task<PaginatedResult<CgExecutionPaginatedListVm>> GetCgExecutionPaginatedList(
        GetCgExecutionPaginatedListQuery query);
    Task<BaseResponse> Update(ResiliencyReadinessWorkflowScheduleLogCommand workflowScheduleLogCommand);
}
