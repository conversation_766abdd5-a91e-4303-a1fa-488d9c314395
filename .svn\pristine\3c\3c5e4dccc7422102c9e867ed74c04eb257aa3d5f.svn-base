﻿.carousel-indicators [data-bs-target] {
    width: 14px;
    height: 2px;
    border-top: none;
    border-bottom: none;
}

.carousel-indicators {
    position: relative;
    margin-bottom: 0;
    margin-top: 8px;
}

.BusinessCard_List .card-body {
    height: calc(100vh - 171px);
    overflow: auto;
}

.BusinessCard_List .list-group-item-action:active {
    border: 1px solid #0d6efd;
    border-radius: 0.3rem !important;
}

.BusinessCard_List .Active-Card {
    border-left: 3px solid #0d6efd !important;
    border-radius: 0px !important;
    color: var(--bs-list-group-action-active-color);
    background-color: var(--bs-primary-bg-subtle);
}

.filter-accordion .accordion-button {
    padding: 6px 10px;
    font-size: var(--bs-body-font-size);
}

.filter-accordion .accordion-button {
    padding: 6px 10px;
    font-size: var(--bs-body-font-size);
}

.accordion {
    --bs-accordion-btn-icon-width: 1rem !important;
}

.filter-accordion .accordion-button::after {
    width: var(--bs-accordion-btn-icon-width);
    height: var(--bs-accordion-btn-icon-width);
}

.filter-accordion .list-group-item {
    padding: 0.4rem;
    font-size: var(--bs-body-font-size);
    font-weight: var(--bs-body-font-weight);
}

.ITView-Infraobject-Tab .nav-link {
    font-size: var(--bs-nav-menu-font-size);
}

/*List group Style*/
.Workflow-Execution {
    height: calc(100vh - 171px);
    overflow-y: auto;
}

.accordion-button {
    padding: 6px;
    font-size: var(--bs-nav-menu-font-size);
    font-weight: var(--bs-menu-font-weight);
    background-color: transparent !important;
    box-shadow: none !important;
}

.accordion-body {
    padding: 3px 6px;
}

.btn-outline-secondary:hover {
    background-color: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
}

/*End List group Style*/

/*Charts Style*/
#DCMap-Chart {
    width: 100%;
    height: calc(100vh - 340px);
}

.D3overview {
    max-width: 100%;
    height: 100%;
    user-select: none;
    padding: 8px;
}

.link {
    fill: none;
    stroke: #41c200;
    stroke-opacity: 0.4;
    stroke-width: 1.5;
}

.node {
    cursor: pointer;
    stroke-linejoin: round;
    stroke-width: 3;
}

    .node circle {
        fill: #41c200;
        stroke: #41c200;
        stroke-width: 0px;
    }


.templink {
    fill: none;
    stroke: red;
    stroke-width: 3px;
}

.ghostCircle.show {
    display: block;
}

.ghostCircle, .activeDrag .ghostCircle {
    display: none;
}

.amcharts-ForceDirectedLink-group {
    stroke-dasharray: 10px 5px;
    stroke-linejoin: round;
    stroke-linecap: round;
    -webkit-animation: am-moving-dashesIn 2s linear infinite;
    animation: am-moving-dashesIn 1s linear infinite;
    /*stroke: #0d6efd !important;*/
    stroke-opacity: 1;
    stroke-width: 1.5px;
}

@-webkit-keyframes am-moving-dashesIn {
    100% {
        stroke-dashoffset: -31px;
    }
}

@keyframes am-moving-dashesIn {
    100% {
        stroke-dashoffset: -31px;
    }
}

.amcharts-ForceDirectedLink-group-Out {
    stroke-dasharray: 10px 5px;
    stroke-linejoin: round;
    stroke-linecap: round;
    -webkit-animation: am-moving-dashesIn 2s linear infinite;
    animation: am-moving-dashesIn 1s linear infinite;
    stroke: #0d6efd !important;
    stroke-opacity: 1;
    stroke-width: 1.5px;
}


@-webkit-keyframes am-moving-dashesOut {
    100% {
        stroke-dashoffset: -31px;
    }
}

@keyframes am-moving-dashesOut {
    100% {
        stroke-dashoffset: -31px;
    }
}


#DataCenter .amcharts-ForceDirectedLink-group {
    stroke-dasharray: 10px 5px;
    stroke-linejoin: round;
    stroke-linecap: round;
    -webkit-animation: am-moving-dashesIn 2s linear infinite;
    animation: am-moving-dashesIn 1s linear infinite;
    stroke: #0d6efd !important;
    stroke-opacity: 1;
    stroke-width: 0.5px;
}

@-webkit-keyframes am-moving-dashesIn {
    100% {
        stroke-dashoffset: -31px;
    }
}

@keyframes am-moving-dashesIn {
    100% {
        stroke-dashoffset: -31px;
    }
}

#DataCenter .amcharts-ForceDirectedLink-group-Out {
    stroke-dasharray: 10px 5px;
    stroke-linejoin: round;
    stroke-linecap: round;
    -webkit-animation: am-moving-dashesIn 2s linear infinite;
    animation: am-moving-dashesIn 1s linear infinite;
    stroke: #0d6efd !important;
    stroke-opacity: 1;
    stroke-width: 0.5px;
}


@-webkit-keyframes am-moving-dashesOut {
    100% {
        stroke-dashoffset: -31px;
    }
}

@keyframes am-moving-dashesOut {
    100% {
        stroke-dashoffset: -31px;
    }
}



/*End Charts Style*/

.BusinessView-NavTab .nav-link {
    color: var(--bs-dark);
    background-color: #fff;
}

.range-slider {
    width: 100%;
    margin: 0 auto;
    position: relative;
    margin-top: -20px;
    margin-bottom: 0rem;
}

#range {
    -webkit-appearance: none;
    width: 100%;
    height: 1px !important;
}

    #range:focus {
        outline: none;
    }

    #range::-webkit-slider-runnable-track {
        width: 100%;
        height: 0.15rem;
        cursor: pointer;
        animate: 0.2s;
        border-radius: 0rem;
    }

    #range::-moz-range-track {
        width: 100%;
        height: 0.15rem;
        cursor: pointer;
        animate: 0.2s;
        border-radius: 0rem;
    }

    #range::-webkit-slider-thumb {
        -webkit-appearance: none;
        border-radius: 50%;
        background: #dcdcdc;
        cursor: pointer;
        height: 10px;
        width: 1.5px;
        transform: translatey(calc(-50% + -3px));
    }

    /* For Firefox */
    #range::-moz-range-thumb {
        -webkit-appearance: none;
        background: #dcdcdc;
        cursor: pointer;
        height: 10px;
        width: 0.6px;
        border: none;
    }


#tooltip {
    position: absolute;
    top: -1rem;
}

    #tooltip .tooltip-bg {
        position: absolute;
        text-align: center;
        display: block;
        left: 50%;
        transform: translate(-50%, 0);
    }

    #tooltip .tooltip-value {
        color: #0479ff;
        width: 100px;
        display: flex;
        justify-content: center
    }

.text-custom-color {
    color: #0479FF;
}

.text-rto-color {
    color: #00C2FF;
}

text-rto-color


.DC-Accordion .accordion-button {
    background-color: transparent;
    color: var(--bs-accordion-color);
}

.DC-Accordion .Active-Card {
    color: var(--bs-primary);
}

.DC-Accordion .Card_NoData_Img {
    display: flex;
    padding: 20px;
}


.DC-Map-Timeline .user-profile-timeline-container ul.tl li .item-icon {
    top: 0px;
}

.DC-Map-Timeline .user-profile-timeline-container ul.tl li {
    border-left: 1px solid #f1f1f1;
    padding: 0 0 10px 20px;
}

.DC-Map-Timeline .user-profile-timeline-container ul.tl {
    margin: 0px;
}

.Resiliency-TotalSite-Tab .nav-pills {
    --bs-nav-pills-border-radius: 0px;
    --bs-nav-pills-link-active-color: #1a1a1a;
    --bs-nav-pills-link-active-bg: transparent;
}

    .Resiliency-TotalSite-Tab .nav-pills .nav-link {
        color: #1a1a1a;
        padding: 0px;
        width: 130px;
    }

    .Resiliency-TotalSite-Tab .nav-pills .active {
        border-right: 3px solid #0479ff
    }


/*background Gradient Color*/
.Blue_Gradient {
    background: rgb(29,159,252);
    background: linear-gradient(90deg, rgba(29,159,252,1) 0%, rgba(93,200,255,1) 46%, rgba(22,185,250,1) 100%);
    height: 80px;
    background-size: cover;
    clip-path: circle(100vh at 50% -88vh);
}

.Pink_Gradient {
    background: rgb(129,154,246);
    background: linear-gradient(144deg, rgba(129,154,246,1) 0%, rgba(177,147,232,1) 46%, rgba(210,142,221,1) 100%);
    height: 80px;
    background-size: cover;
    clip-path: circle(100vh at 50% -88vh);
}

.Orange_Gradient {
    background: rgb(254,110,109);
    background: linear-gradient(144deg, rgba(254,110,109,1) 0%, rgba(255,151,111,1) 46%, rgba(255,183,113,1) 100%);
    height: 80px;
    background-size: cover;
    clip-path: circle(100vh at 50% -88vh);
}

/*background Gradient Color*/
.Drift_Blue_Gradient {
    background: rgb(241,247,255);
    background: linear-gradient(97deg, rgba(241,247,255,1) 0%, rgba(215,220,255,1) 100%);
    border-radius: 10px;
    border: 1px solid #e2eafe;
}

.Drift_Green_Gradient {
    background: rgb(204, 255, 231);
    background: linear-gradient(97deg, rgba(204, 255, 231, 1) 0%, rgba(230, 255, 205, 1) 17%, rgba(227, 255, 205, 1) 33%, rgba(211, 253, 245, 1) 53%, rgba(224, 249, 249, 1) 67%, rgba(248, 241, 224, 1) 87%, rgba(246, 245, 224, 1) 100%);
    border-radius: 10px;
    border: 1px solid #e4f2ee;
}

.Drift_Pink_Gradient {
    background: #FFEDED;
    background: linear-gradient(97.9deg, #F7E1DA 0%, #EFDFE6 28.53%, #E6E1F6 100.33%);
    border-radius: 10px;
    border: 1px solid #fee9e9;
}

.Drift_Orange_Gradient {
    background: #FFEDED;
    background: linear-gradient(97.9deg, #FFEDED 0%, #FFEAE5 28.53%, #FFF8D6 100.33%);
    border-radius: 10px;
    border: 1px solid #FFF8D6;
}
