using ContinuityPatrol.Application.Features.CyberAlert.Events.Create;

namespace ContinuityPatrol.Application.Features.CyberAlert.Commands.Create;

public class CreateCyberAlertCommandHandler : IRequestHandler<CreateCyberAlertCommand, CreateCyberAlertResponse>
{
    private readonly ICyberAlertRepository _cyberAlertRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateCyberAlertCommandHandler(IMapper mapper, ICyberAlertRepository cyberAlertRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _cyberAlertRepository = cyberAlertRepository;
    }

    public async Task<CreateCyberAlertResponse> Handle(CreateCyberAlertCommand request,
        CancellationToken cancellationToken)
    {
        var cyberAlert = _mapper.Map<Domain.Entities.CyberAlert>(request);

        cyberAlert = await _cyberAlertRepository.AddAsync(cyberAlert);

        var response = new CreateCyberAlertResponse
        {
            Message = Message.Create(nameof(Domain.Entities.CyberAlert), cyberAlert.JobName),

            Id = cyberAlert.ReferenceId
        };

        await _publisher.Publish(new CyberAlertCreatedEvent { Name = cyberAlert.JobName }, cancellationToken);

        return response;
    }
}