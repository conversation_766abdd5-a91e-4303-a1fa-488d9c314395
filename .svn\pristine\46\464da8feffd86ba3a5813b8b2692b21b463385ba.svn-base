﻿using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class HeatMapStatusFilterSpecification : Specification<HeatMapStatusView>
{
    public HeatMapStatusFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("businessServiceName=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.BusinessServiceName.Contains(stringItem.Replace("businessServiceName=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("businessFunctionName=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.BusinessFunctionName.Contains(stringItem.Replace("businessFunctionName=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("infraObjectName=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.InfraObjectName.Contains(stringItem.Replace("infraObjectName=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("HeatmapType=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.HeatmapType.Contains(stringItem.Replace("HeatmapType=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }

            else
            {
                Criteria = p =>
                    p.BusinessServiceName.Contains(searchString) || p.InfraObjectName.Contains(searchString) ||
                    p.HeatmapType.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.BusinessServiceName != null;
        }
    }
}