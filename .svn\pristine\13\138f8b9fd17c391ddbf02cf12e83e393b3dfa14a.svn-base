﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class GlobalSettingFilterSpecification : Specification<GlobalSetting>
{
    public GlobalSettingFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.GlobalSettingKey != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)

                    if (stringItem.Contains("globalsettingkey=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.GlobalSettingKey.Contains(stringItem.Replace("globalsettingkey=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("globalsettingvalue=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.GlobalSettingValue.Contains(stringItem.Replace("globalsettingvalue=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.GlobalSettingKey.Contains(searchString) || p.GlobalSettingValue.Contains(searchString);
            }
        }
    }
}