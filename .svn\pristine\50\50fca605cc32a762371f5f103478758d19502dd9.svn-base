using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SmtpConfigurationRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly SmtpConfigurationRepository _repository;
    private readonly SmtpConfigurationFixture _fixture;

    public SmtpConfigurationRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _repository = new SmtpConfigurationRepository(_dbContext);
        _fixture = new SmtpConfigurationFixture();
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllSmtpConfigurations()
    {
        // Arrange
        await ClearDatabase();

        var smtpConfig1 = _fixture.CreateSmtpConfiguration(
            companyId: "COMPANY_123",
            smtpHost: "smtp1.example.com",
            port: "587",
            userName: "<EMAIL>"
        );
        var smtpConfig2 = _fixture.CreateSmtpConfiguration(
            companyId: "COMPANY_456",
            smtpHost: "smtp2.example.com",
            port: "465",
            userName: "<EMAIL>"
        );

        await _repository.AddAsync(smtpConfig1);
        await _repository.AddAsync(smtpConfig2);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.SmtpHost == "smtp1.example.com");
        Assert.Contains(result, s => s.SmtpHost == "smtp2.example.com");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoConfigurations()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnConfiguration_WhenIdExists()
    {
        // Arrange
        await ClearDatabase();

        var smtpConfig = _fixture.CreateSmtpConfiguration(
            companyId: "COMPANY_123",
            smtpHost: "smtp.test.com",
            port: "587",
            userName: "<EMAIL>",
            password: "testpass",
            enableSSL: true,
            isBodyHTML: true,
            isPasswordLess: false,
            maskFromAddress: "<EMAIL>",
            isMask: true
        );

        await _repository.AddAsync(smtpConfig);

        // Act
        var result = await _repository.GetByReferenceIdAsync(smtpConfig.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(smtpConfig.ReferenceId, result.ReferenceId);
        Assert.Equal("COMPANY_123", result.CompanyId);
        Assert.Equal("smtp.test.com", result.SmtpHost);
        Assert.Equal("587", result.Port);
        Assert.Equal("<EMAIL>", result.UserName);
        Assert.Equal("testpass", result.Password);
        Assert.True(result.EnableSSL);
        Assert.True(result.IsBodyHTML);
        Assert.False(result.IsPasswordLess);
        Assert.Equal("<EMAIL>", result.MaskFromAddress);
        Assert.True(result.IsMask);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

  
    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnConfiguration_WhenIdExists()
    {
        // Arrange
        await ClearDatabase();

        var smtpConfig = _fixture.CreateSmtpConfiguration(smtpHost: "smtp.test.com");
        await _repository.AddAsync(smtpConfig);

        // Act
        var result = await _repository.GetByIdAsync(smtpConfig.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(smtpConfig.Id, result.Id);
        Assert.Equal("smtp.test.com", result.SmtpHost);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddConfiguration_WhenValidEntity()
    {
        // Arrange
        await ClearDatabase();

        var smtpConfig = _fixture.CreateSmtpConfiguration(
            companyId: "COMPANY_NEW",
            smtpHost: "smtp.new.com",
            port: "25",
            userName: "<EMAIL>",
            password: "newpass",
            enableSSL: false,
            isBodyHTML: false,
            isPasswordLess: true,
            maskFromAddress: "<EMAIL>",
            isMask: false
        );

        // Act
        var result = await _repository.AddAsync(smtpConfig);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Id > 0);
        Assert.Equal("COMPANY_NEW", result.CompanyId);
        Assert.Equal("smtp.new.com", result.SmtpHost);
        Assert.Equal("25", result.Port);
        Assert.Equal("<EMAIL>", result.UserName);
        Assert.Equal("newpass", result.Password);
        Assert.False(result.EnableSSL);
        Assert.False(result.IsBodyHTML);
        Assert.True(result.IsPasswordLess);
        Assert.Equal("<EMAIL>", result.MaskFromAddress);
        Assert.False(result.IsMask);

        // Verify it was saved to database
        var savedConfig = await _repository.GetByReferenceIdAsync(result.ReferenceId);
        Assert.NotNull(savedConfig);
        Assert.Equal(result.SmtpHost, savedConfig.SmtpHost);
    }

    [Fact]
    public async Task AddAsync_ShouldThrowException_WhenEntityIsNull()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateConfiguration_WhenValidEntity()
    {
        // Arrange
        await ClearDatabase();

        var smtpConfig = _fixture.CreateSmtpConfiguration(
            smtpHost: "smtp.original.com",
            port: "587",
            enableSSL: true
        );
        await _repository.AddAsync(smtpConfig);

        // Modify the entity
        smtpConfig.SmtpHost = "smtp.updated.com";
        smtpConfig.Port = "465";
        smtpConfig.EnableSSL = false;
        smtpConfig.UserName = "<EMAIL>";
        smtpConfig.Password = "updatedpass";

        // Act
        var result = await _repository.UpdateAsync(smtpConfig);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("smtp.updated.com", result.SmtpHost);
        Assert.Equal("465", result.Port);
        Assert.False(result.EnableSSL);
        Assert.Equal("<EMAIL>", result.UserName);
        Assert.Equal("updatedpass", result.Password);

        // Verify it was updated in database
        var updatedConfig = await _repository.GetByReferenceIdAsync(result.ReferenceId);
        Assert.NotNull(updatedConfig);
        Assert.Equal("smtp.updated.com", updatedConfig.SmtpHost);
        Assert.Equal("465", updatedConfig.Port);
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveConfiguration_WhenValidEntity()
    {
        // Arrange
        await ClearDatabase();

        var smtpConfig = _fixture.CreateSmtpConfiguration(smtpHost: "smtp.todelete.com");
        await _repository.AddAsync(smtpConfig);

        // Act
        var result = await _repository.DeleteAsync(smtpConfig);

        // Assert
        Assert.NotNull(result);

        // Verify it was removed from database
        var deletedConfig = await _repository.GetByReferenceIdAsync(smtpConfig.ReferenceId);
        Assert.Null(deletedConfig);
    }

    #endregion

    #region FindByFilterAsync Tests

    [Fact]
    public async Task FindByFilterAsync_ShouldReturnMatchingConfigurations()
    {
        // Arrange
        await ClearDatabase();

        var smtpConfig1 = _fixture.CreateSmtpConfiguration(companyId: "COMPANY_123", smtpHost: "smtp1.example.com");
        var smtpConfig2 = _fixture.CreateSmtpConfiguration(companyId: "COMPANY_456", smtpHost: "smtp2.example.com");
        var smtpConfig3 = _fixture.CreateSmtpConfiguration(companyId: "COMPANY_123", smtpHost: "smtp3.example.com");

        await _repository.AddAsync(smtpConfig1);
        await _repository.AddAsync(smtpConfig2);
        await _repository.AddAsync(smtpConfig3);

        // Act
        var result = await _repository.FindByFilterAsync(s => s.CompanyId == "COMPANY_123");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, s => Assert.Equal("COMPANY_123", s.CompanyId));
    }

    [Fact]
    public async Task FindByFilterAsync_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();

        var smtpConfig = _fixture.CreateSmtpConfiguration(companyId: "COMPANY_123");
        await _repository.AddAsync(smtpConfig);

        // Act
        var result = await _repository.FindByFilterAsync(s => s.CompanyId == "NONEXISTENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        await ClearDatabase();

        var configs = _fixture.CreateMultipleSmtpConfigurations(5);
        foreach (var config in configs)
        {
            await _repository.AddAsync(config);
        }

        string? searchString = null;

        var specification = new SmtpConfigurationFilterSpecification(searchString);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 3, specification, "Id", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Data.Count);
        Assert.Equal(5, result.TotalCount);
        Assert.Equal(3, result.PageSize);
        Assert.Equal(2, result.TotalPages);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.SmtpConfigurations.RemoveRange(_dbContext.SmtpConfigurations);
        await _dbContext.SaveChangesAsync();
    }
}
