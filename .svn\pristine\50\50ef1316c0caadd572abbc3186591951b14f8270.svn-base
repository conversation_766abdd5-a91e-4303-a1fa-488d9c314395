﻿using ContinuityPatrol.Domain.ViewModels.ReplicationJobModel;

namespace ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetList;

public class GetReplicationJobListQueryHandler : IRequestHandler<GetReplicationJobListQuery, List<ReplicationJobListVm>>
{
    private readonly IMapper _mapper;
    private readonly IReplicationJobRepository _replicationJobRepository;

    public GetReplicationJobListQueryHandler(IMapper mapper, IReplicationJobRepository replicationJobRepository)
    {
        _mapper = mapper;
        _replicationJobRepository = replicationJobRepository;
    }

    public async Task<List<ReplicationJobListVm>> Handle(GetReplicationJobListQuery request,
        CancellationToken cancellationToken)
    {
        var replications = (await _replicationJobRepository.ListAllAsync()).ToList();

        return replications.Count <= 0
            ? new List<ReplicationJobListVm>()
            : _mapper.Map<List<ReplicationJobListVm>>(replications);
    }
}