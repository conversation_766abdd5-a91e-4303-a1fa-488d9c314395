﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Server.Events.InfraSummaryEvents.Update;

public class ServerInfraSummaryUpdatedEventHandler : INotificationHandler<ServerInfraSummaryUpdatedEvent>
{
    private readonly IInfraSummaryRepository _infraSummaryRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ILogger<ServerInfraSummaryUpdatedEventHandler> _logger;

    public ServerInfraSummaryUpdatedEventHandler(ILogger<ServerInfraSummaryUpdatedEventHandler> logger,
        IInfraSummaryRepository infraSummaryRepository, ILoggedInUserService loggedInUserService)
    {
        _logger = logger;
        _infraSummaryRepository = infraSummaryRepository;
        _loggedInUserService = loggedInUserService;
    }

    public async Task Handle(ServerInfraSummaryUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var infraSummaryPreviousOSType =
            await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                updatedEvent.PreviousOsType, updatedEvent.PreviousBusinessServiceId, updatedEvent.CompanyId);

        //var infraSummaryCurrentOSType = await _infraSummaryRepository.GetInfraSummaryByType(updatedEvent.CurrentOsType);
        var infraSummaryCurrentOSType =
            await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                updatedEvent.CurrentOsType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);

        if (infraSummaryCurrentOSType is null && !infraSummaryPreviousOSType.Equals(null))
        {
            if (infraSummaryPreviousOSType.Count == 1)
            {
                await _infraSummaryRepository.DeleteAsync(infraSummaryPreviousOSType);

                await _infraSummaryRepository.AddAsync(new Domain.Entities.InfraSummary
                {
                    EntityName = Modules.Server.ToString(),
                    TypeId = updatedEvent.CurrentOsTypeId,
                    Type = updatedEvent.CurrentOsType,
                    Logo = updatedEvent.Logo,
                    Count = 1,
                    BusinessServiceId = updatedEvent.BusinessServiceId,
                    CompanyId = updatedEvent.CompanyId
                });
            }
            else
            {
                infraSummaryPreviousOSType.Count -= 1;
                await _infraSummaryRepository.UpdateAsync(infraSummaryPreviousOSType);

                await _infraSummaryRepository.AddAsync(new Domain.Entities.InfraSummary
                {
                    EntityName = Modules.Server.ToString(),
                    Type = updatedEvent.CurrentOsType,
                    TypeId = updatedEvent.CurrentOsTypeId,
                    Logo = updatedEvent.Logo,
                    Count = 1,
                    BusinessServiceId = updatedEvent.BusinessServiceId,
                    CompanyId = updatedEvent.CompanyId
                });
            }
        }
        else if (!infraSummaryPreviousOSType.Type.Equals(updatedEvent.CurrentOsType) &&
                 infraSummaryCurrentOSType.BusinessServiceId.Equals(updatedEvent.PreviousBusinessServiceId) &&
                 infraSummaryCurrentOSType.CompanyId.Equals(updatedEvent.CompanyId))
        {
            if (infraSummaryPreviousOSType.Count == 1)
            {
                await _infraSummaryRepository.DeleteAsync(infraSummaryPreviousOSType);

                var infraSummary =
                    await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                        updatedEvent.CurrentOsType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);

                if (infraSummary is null)
                {
                    await _infraSummaryRepository.AddAsync(new Domain.Entities.InfraSummary
                    {
                        EntityName = Modules.Server.ToString(),
                        Type = updatedEvent.CurrentOsType,
                        TypeId = updatedEvent.CurrentOsTypeId,
                        Logo = updatedEvent.Logo,
                        Count = 1,
                        BusinessServiceId = updatedEvent.BusinessServiceId,
                        CompanyId = updatedEvent.CompanyId
                    });
                }
                else
                {
                    infraSummary.EntityName = Modules.Server.ToString();
                    infraSummary.Logo = updatedEvent.Logo;
                    infraSummary.Count += 1;
                    infraSummary.BusinessServiceId = updatedEvent.BusinessServiceId;
                    infraSummary.CompanyId = updatedEvent.CompanyId;
                    await _infraSummaryRepository.UpdateAsync(infraSummary);
                }
            }
            else
            {
                infraSummaryPreviousOSType.Count -= 1;
                await _infraSummaryRepository.UpdateAsync(infraSummaryPreviousOSType);

                var infraSummary =
                    await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                        updatedEvent.CurrentOsType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);
                infraSummary.EntityName = Modules.Server.ToString();
                infraSummary.Logo = updatedEvent.Logo;
                infraSummary.Count += 1;
                infraSummary.BusinessServiceId = updatedEvent.BusinessServiceId;
                infraSummary.CompanyId = updatedEvent.CompanyId;
                await _infraSummaryRepository.UpdateAsync(infraSummary);
            }
        }
        else if (infraSummaryPreviousOSType.Type.Equals(updatedEvent.CurrentOsType) &&
                 !infraSummaryCurrentOSType.BusinessServiceId.Equals(updatedEvent.PreviousBusinessServiceId) &&
                 infraSummaryCurrentOSType.CompanyId.Equals(updatedEvent.CompanyId))
        {
            if (infraSummaryPreviousOSType.Count == 1)
            {
                await _infraSummaryRepository.DeleteAsync(infraSummaryPreviousOSType);

                var infraSummary =
                    await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                        updatedEvent.CurrentOsType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);

                if (infraSummary is null)
                {
                    await _infraSummaryRepository.AddAsync(new Domain.Entities.InfraSummary
                    {
                        EntityName = Modules.Server.ToString(),
                        Type = updatedEvent.CurrentOsType,
                        TypeId = updatedEvent.CurrentOsTypeId,
                        Logo = updatedEvent.Logo,
                        Count = 1,
                        BusinessServiceId = updatedEvent.BusinessServiceId,
                        CompanyId = updatedEvent.CompanyId
                    });
                }
                else
                {
                    infraSummary.EntityName = Modules.Server.ToString();
                    infraSummary.Logo = updatedEvent.Logo;
                    infraSummary.Count += 1;
                    infraSummary.BusinessServiceId = updatedEvent.BusinessServiceId;
                    infraSummary.CompanyId = updatedEvent.CompanyId;
                    await _infraSummaryRepository.UpdateAsync(infraSummary);
                }
            }
            else
            {
                infraSummaryPreviousOSType.Count -= 1;
                await _infraSummaryRepository.UpdateAsync(infraSummaryPreviousOSType);

                var infraSummary =
                    await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                        updatedEvent.CurrentOsType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);
                infraSummary.EntityName = Modules.Server.ToString();
                infraSummary.Logo = updatedEvent.Logo;
                infraSummary.Count += 1;
                infraSummary.BusinessServiceId = updatedEvent.BusinessServiceId;
                infraSummary.CompanyId = updatedEvent.CompanyId;
                await _infraSummaryRepository.UpdateAsync(infraSummary);
            }
        }
        else if (infraSummaryPreviousOSType.Type.Equals(updatedEvent.CurrentOsType) &&
                 infraSummaryCurrentOSType.BusinessServiceId.Equals(updatedEvent.PreviousBusinessServiceId) &&
                 infraSummaryCurrentOSType.CompanyId.Equals(updatedEvent.CompanyId))
        {
            if (infraSummaryPreviousOSType.Count == 1)
            {
                await _infraSummaryRepository.DeleteAsync(infraSummaryPreviousOSType);

                var infraSummary =
                    await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                        updatedEvent.CurrentOsType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);

                if (infraSummary is null)
                {
                    await _infraSummaryRepository.AddAsync(new Domain.Entities.InfraSummary
                    {
                        EntityName = Modules.Server.ToString(),
                        Type = updatedEvent.CurrentOsType,
                        TypeId = updatedEvent.CurrentOsTypeId,
                        Logo = updatedEvent.Logo,
                        Count = 1,
                        BusinessServiceId = updatedEvent.BusinessServiceId,
                        CompanyId = updatedEvent.CompanyId
                    });
                }
                else
                {
                    infraSummary.EntityName = Modules.Server.ToString();
                    infraSummary.Logo = updatedEvent.Logo;
                    infraSummary.Count += 1;
                    infraSummary.BusinessServiceId = updatedEvent.BusinessServiceId;
                    infraSummary.CompanyId = updatedEvent.CompanyId;
                    await _infraSummaryRepository.UpdateAsync(infraSummary);
                }
            }
            else
            {
                infraSummaryPreviousOSType.Count -= 1;
                await _infraSummaryRepository.UpdateAsync(infraSummaryPreviousOSType);

                var infraSummary =
                    await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                        updatedEvent.CurrentOsType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);
                infraSummary.EntityName = Modules.Server.ToString();
                infraSummary.Logo = updatedEvent.Logo;
                infraSummary.Count += 1;
                infraSummary.BusinessServiceId = updatedEvent.BusinessServiceId;
                infraSummary.CompanyId = updatedEvent.CompanyId;
                await _infraSummaryRepository.UpdateAsync(infraSummary);
            }
        }

        _logger.LogInformation($"InfraSummary '{updatedEvent.CurrentOsType}' updated successfully.");
    }
}