﻿using ContinuityPatrol.Application.Features.StateMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.StateMonitorStatusModel;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Impl;

namespace ContinuityPatrol.Application.UnitTests.Features.StateMonitorStatus.Queries;

public class GetStateMonitorStatusListQueryHandlerTests : IClassFixture<StateMonitorStatusFixture>, IClassFixture<NodeConfigurationFixture>
{
    private readonly StateMonitorStatusFixture _stateMonitorStatusFixture;

    private readonly NodeConfigurationFixture _nodeConfigurationFixture;

    private Mock<IStateMonitorStatusRepository> _mockStateMonitorStatusRepository;

    private readonly Mock<ILoadBalancerRepository> _mockNodeConfigurationRepository;

    private readonly IWindowsService _windowsService;

    private readonly GetStateMonitorStatusListQueryHandler _handler;

    public GetStateMonitorStatusListQueryHandlerTests(StateMonitorStatusFixture stateMonitorStatusFixture, NodeConfigurationFixture nodeConfigurationFixture)
    {

        _windowsService= Mock.Of<IWindowsService>();

        _stateMonitorStatusFixture = stateMonitorStatusFixture;

        _nodeConfigurationFixture = nodeConfigurationFixture;

        _mockStateMonitorStatusRepository = StateMonitorStatusRepositoryMocks.GetStateMonitorStatusRepository(_stateMonitorStatusFixture.StateMonitorStatuses);

        _mockNodeConfigurationRepository = NodeConfigurationRepositoryMocks.GetNodeConfigurationRepository(_nodeConfigurationFixture.NodeConfigurations);

        _handler = new GetStateMonitorStatusListQueryHandler(_mockStateMonitorStatusRepository.Object, _stateMonitorStatusFixture.Mapper, _windowsService, _mockNodeConfigurationRepository.Object);

    }

    [Fact]
    public async Task Handle_Return_Valid_StateMonitorStatusesList()
    {
        var loadbalencer = new Domain.Entities.LoadBalancer
        {
            ConnectionType = "Node",
            IPAddress = "10:11:92:106",
            Port = 5000
        };
        _mockNodeConfigurationRepository.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "Load Balancer")).ReturnsAsync(loadbalencer);
        var result = await _handler.Handle(new GetStateMonitorStatusListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<StateMonitorStatusListVm>>();

        result[0].Id.ShouldBe(_stateMonitorStatusFixture.StateMonitorStatuses[0].ReferenceId);
        result[0].Properties.ShouldBe(_stateMonitorStatusFixture.StateMonitorStatuses[0].Properties);

    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        var loadbalencer = new Domain.Entities.LoadBalancer
        {
            ConnectionType = "Node",
            IPAddress = "10:11:92:106",
            Port = 5000
        };
        _mockNodeConfigurationRepository.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "Load Balancer")).ReturnsAsync(loadbalencer);
        _mockStateMonitorStatusRepository = StateMonitorStatusRepositoryMocks.GetStateMonitorStatusEmptyRepository();

        var handler = new GetStateMonitorStatusListQueryHandler(_mockStateMonitorStatusRepository.Object, _stateMonitorStatusFixture.Mapper, _windowsService, _mockNodeConfigurationRepository.Object);

        var result = await handler.Handle(new GetStateMonitorStatusListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }
    
    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        var loadbalencer = new Domain.Entities.LoadBalancer {
            ConnectionType="Node",
            IPAddress="10:11:92:106",
            Port=5000
        };
        _mockNodeConfigurationRepository.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "Load Balancer")).ReturnsAsync(loadbalencer);
        
        await _handler.Handle(new GetStateMonitorStatusListQuery(), CancellationToken.None);

        _mockStateMonitorStatusRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}