﻿using ContinuityPatrol.Application.Features.Database.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Database.Events;

public class UpdateDatabaseEventTests : IClassFixture<DatabaseFixture>, IClassFixture<UserActivityFixture>
{
    private readonly DatabaseFixture _databaseFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly DatabaseUpdatedEventHandler _handler;

    public UpdateDatabaseEventTests(DatabaseFixture databaseFixture, UserActivityFixture userActivityFixture)
    {
        _databaseFixture = databaseFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockDatabaseEventLogger = new Mock<ILogger<DatabaseUpdatedEventHandler>>();

        _mockUserActivityRepository = DatabaseRepositoryMocks.CreateDatabaseEventRepository(_userActivityFixture.UserActivities);

        _handler = new DatabaseUpdatedEventHandler(mockLoggedInUserService.Object, mockDatabaseEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateDatabaseEventUpdated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_databaseFixture.DatabaseUpdatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_databaseFixture.DatabaseUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}