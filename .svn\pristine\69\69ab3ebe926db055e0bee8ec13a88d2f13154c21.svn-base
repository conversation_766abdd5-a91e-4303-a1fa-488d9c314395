﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfile.Queries;

public class GetWorkflowProfilePaginatedListQueryHandlerTests : IClassFixture<WorkflowProfileFixture>
{
    private readonly WorkflowProfileFixture _workflowProfileFixture;

    private readonly Mock<IWorkflowProfileRepository> _mockWorkflowProfileRepository;

    private readonly GetWorkflowProfilePaginatedListQueryHandler _handler;

    public GetWorkflowProfilePaginatedListQueryHandlerTests(WorkflowProfileFixture workflowProfileFixture)
    {
        _workflowProfileFixture = workflowProfileFixture;

        _workflowProfileFixture.WorkflowProfiles[0].Name = "Pratheesh_Profile";
        _workflowProfileFixture.WorkflowProfiles[0].Status = "Pending";
        _workflowProfileFixture.WorkflowProfiles[0].Password = "Admin@123";

        _workflowProfileFixture.WorkflowProfiles[1].Name = "Pratheesh_Profile123";
        _workflowProfileFixture.WorkflowProfiles[1].Status = "Pending123";
        _workflowProfileFixture.WorkflowProfiles[1].Password = "Admin@1234";

        _mockWorkflowProfileRepository = WorkflowProfileRepositoryMocks.GetPaginatedWorkflowProfileRepository(_workflowProfileFixture.WorkflowProfiles);
        _handler = new GetWorkflowProfilePaginatedListQueryHandler(_workflowProfileFixture.Mapper, _mockWorkflowProfileRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetWorkflowProfilePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowProfileListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_WorkflowProfile_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetWorkflowProfilePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=Pratheesh_Profile;Status=Pending;Password=Admin@123" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowProfileListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Name.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Name);
        result.Data[0].Status.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Status);
        result.Data[0].Password.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Password);


        result.Data[1].Name.ShouldBe(_workflowProfileFixture.WorkflowProfiles[1].Name);
        result.Data[1].Status.ShouldBe(_workflowProfileFixture.WorkflowProfiles[1].Status);
        result.Data[1].Password.ShouldBe(_workflowProfileFixture.WorkflowProfiles[1].Password);
    }

    [Fact]
    public async Task Handle_Return_PaginatedWorkflowProfile_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetWorkflowProfilePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Pratheesh_Profile" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowProfileListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());
        result.Data[0].Name.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Name);
        result.Data[0].Status.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Status);
        result.Data[0].Password.ShouldBe(_workflowProfileFixture.WorkflowProfiles[0].Password);


        result.Data[1].Name.ShouldBe(_workflowProfileFixture.WorkflowProfiles[1].Name);
        result.Data[1].Status.ShouldBe(_workflowProfileFixture.WorkflowProfiles[1].Status);
        result.Data[1].Password.ShouldBe(_workflowProfileFixture.WorkflowProfiles[1].Password);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowProfilePaginatedListQuery() { PageNumber = 1, PageSize = 10, SearchString = "Admin@123" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowProfileListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetWorkflowProfilePaginatedListQuery(), CancellationToken.None);

        _mockWorkflowProfileRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}