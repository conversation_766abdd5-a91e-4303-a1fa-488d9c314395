﻿using ContinuityPatrol.Application.Features.WorkflowActionResult.Commands.Create;
using ContinuityPatrol.Application.Hubs;
using Microsoft.AspNetCore.SignalR;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionResult.Commands
{
    public class CreateWorkflowActionResultTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IWorkflowActionResultRepository> _mockWorkflowActionResultRepository;
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly Mock<IHubContext<WorkflowActionResultHub>> _mockHubContext;
        private readonly Mock<IClientProxy> _mockClientProxy;
        private readonly CreateWorkflowActionResultCommandHandler _handler;

        public CreateWorkflowActionResultTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();
            _mockHubContext = new Mock<IHubContext<WorkflowActionResultHub>>();
            _mockClientProxy = new Mock<IClientProxy>();

            _handler = new CreateWorkflowActionResultCommandHandler(
                _mockMapper.Object,
                _mockWorkflowActionResultRepository.Object,
                _mockLoggedInUserService.Object,
                _mockHubContext.Object
            );

            var mockClients = new Mock<IHubClients>();
            mockClients.Setup(clients => clients.All).Returns(_mockClientProxy.Object);
            _mockHubContext.Setup(hub => hub.Clients).Returns(mockClients.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnResponseAndSendMessage_WhenCommandIsValid()
        {
            var request = new CreateWorkflowActionResultCommand
            {
                WorkflowOperationId = Guid.NewGuid().ToString(),
                WorkflowOperationGroupId = Guid.NewGuid().ToString(),
                WorkflowOperationGroupName = "TestGroup",
                ActionId = Guid.NewGuid().ToString(),
                StepId = Guid.NewGuid().ToString(),
                WorkflowActionName = "TestAction",
                StartTime = DateTime.UtcNow,
                EndTime = DateTime.UtcNow.AddMinutes(5),
                Status = "success",
                Message = "Test message"
            };

            var workflowActionResult = new Domain.Entities.WorkflowActionResult
            {
                WorkflowOperationId = request.WorkflowOperationId,
                WorkflowActionName = request.WorkflowActionName,
                ReferenceId = Guid.NewGuid().ToString()
            };

            var workflowActionResultList = new List<Domain.Entities.WorkflowActionResult>
            {
                new Domain.Entities.WorkflowActionResult { Status = "success" },
                new Domain.Entities.WorkflowActionResult { Status = "bypassed" },
                new Domain.Entities.WorkflowActionResult { Status = "error" },
                new Domain.Entities.WorkflowActionResult { Status = "running" }
            };

            _mockLoggedInUserService.Setup(service => service.CompanyId).Returns(Guid.NewGuid().ToString());
            _mockMapper.Setup(mapper => mapper.Map<Domain.Entities.WorkflowActionResult>(request)).Returns(workflowActionResult);
            _mockWorkflowActionResultRepository.Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.WorkflowActionResult>()))
                .ReturnsAsync(workflowActionResult);
            _mockWorkflowActionResultRepository.Setup(repo => repo.GetWorkflowActionResultByWorkflowOperationId(request.WorkflowOperationId))
                .ReturnsAsync(workflowActionResultList);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(workflowActionResult.ReferenceId, result.WorkflowActionResultId);
            Assert.NotEmpty(result.Message);

            _mockWorkflowActionResultRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.WorkflowActionResult>()), Times.Once);
            _mockWorkflowActionResultRepository.Verify(repo => repo.GetWorkflowActionResultByWorkflowOperationId(request.WorkflowOperationId), Times.Once);

            object[] expectedMessage = new object[]
            {
                new
                {
                    Id = workflowActionResult.ReferenceId,
                    SkipCount = 1,
                    SuccessCount = 1,
                    ErrorCount = 1,
                    RunningCount = 1,
                    BypassedCount = 1
                }
            };

            _mockClientProxy.Verify(client => client.SendCoreAsync(
                "workflowActionResult Message",
                It.Is<object[]>(o => CheckMessageContent(o, expectedMessage)),
                CancellationToken.None),
                Times.Once);
        }

        private bool CheckMessageContent(object[] actual, object[] expected)
        {
            if (actual.Length != expected.Length)
                return false;

            for (int i = 0; i < actual.Length; i++)
            {
                var actualItem = actual[i];
                var expectedItem = expected[i];

                if (!ArePropertiesEqual(actualItem, expectedItem, "Id") ||
                    !ArePropertiesEqual(actualItem, expectedItem, "SkipCount") ||
                    !ArePropertiesEqual(actualItem, expectedItem, "SuccessCount") ||
                    !ArePropertiesEqual(actualItem, expectedItem, "ErrorCount") ||
                    !ArePropertiesEqual(actualItem, expectedItem, "RunningCount") ||
                    !ArePropertiesEqual(actualItem, expectedItem, "BypassedCount"))
                {
                    return false;
                }
            }
            return true;
        }

        private bool ArePropertiesEqual(object actual, object expected, string propertyName)
        {
            var actualValue = actual.GetType().GetProperty(propertyName)?.GetValue(actual);
            var expectedValue = expected.GetType().GetProperty(propertyName)?.GetValue(expected);
            return Equals(actualValue, expectedValue);
        }
    }
}
