﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ServerViewRepository : BaseRepository<ServerView>, IServerViewRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly ILoggedInUserService _loggedInUserService;

    public ServerViewRepository(ApplicationDbContext applicationDbContext, IInfraObjectRepository infraObjectRepository, ILoggedInUserService loggedInUserService) : base(applicationDbContext, loggedInUserService)
    {

        _dbContext = applicationDbContext;

        _infraObjectRepository = infraObjectRepository;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<ServerView>> ListAllAsync()
    {
        var servers = SelectServer(base.QueryAll(server =>
            server.CompanyId.Equals(_loggedInUserService.CompanyId)));       

        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetAssignedBusinessServicesByServers(servers);
    }

    public async Task<int> ServerCountAsync()
    {
        var servers = SelectServer(base.QueryAll(server =>
            server.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await servers.CountAsync()
            : GetAssignedBusinessServicesByServers(servers).Count();
    }


    public async Task<IReadOnlyList<ServerView>> GetServerList()
    {
        var servers = base.QueryAll(server =>
            server.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new ServerView { OSType=x.OSType,OSTypeId=x.OSTypeId,Properties=x.Properties, BusinessServiceId = x.BusinessServiceId });

        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetAssignedBusinessServicesByServers(servers);
    }
    public async Task<List<ServerView>> GetServerNames()
    {
        return _loggedInUserService.IsAllInfra
            ? await base.QueryAll(server =>
                server.CompanyId.Equals(_loggedInUserService.CompanyId) && server.IsActive)
                .Select(x => new ServerView
                {
                    ReferenceId = x.ReferenceId,
                    Name = x.Name,
                    RoleTypeId = x.RoleTypeId,
                    RoleType = x.RoleType
                }).ToListAsync()
            : GetAssignedBusinessServicesByServers(base.QueryAll(server =>
                server.CompanyId.Equals(_loggedInUserService.CompanyId) && server.IsActive)).Select(x => new ServerView
                {
                    ReferenceId = x.ReferenceId,
                    Name = x.Name,
                    RoleTypeId = x.RoleTypeId,
                    RoleType = x.RoleType
                }).ToList();
    }
    public async Task<List<ServerView>> GetType(string serverTypeId)
    {
        var servers = SelectServer(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ServerTypeId.Equals(serverTypeId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ServerTypeId.Equals(serverTypeId)));

        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetAssignedBusinessServicesByServers(servers).ToList();
    }
    public async Task<List<ServerView>> GetTypeName(string serverType)
    {
        return await SelectServer(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ServerType.Trim().ToLower().Equals(serverType.Trim().ToLower()))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                x.ServerType.Trim().ToLower().Equals(serverType.Trim().ToLower()))).ToListAsync();        
    }
    public async Task<List<ServerView>> GetRoleType(string roleTypeId)
    {
        var servers = SelectServer(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.RoleTypeId.Equals(roleTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.RoleTypeId.Equals(roleTypeId)));
       
        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetAssignedBusinessServicesByServers(servers).ToList();
    }
    public async Task<List<ServerView>> GetServerBySiteIds(List<string> siteIds)
    {
        var servers = SelectServer(_loggedInUserService.IsParent
           ? base.FilterBy(x => siteIds.Equals(x.SiteId))
           : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && siteIds.Equals(x.SiteId)));

        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetAssignedBusinessServicesByServers(servers).ToList();
    }
    public async Task<List<ServerView>> GetServerBySiteId(string siteId)
    {
        var servers = SelectServer(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.SiteId.Equals(siteId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.SiteId.Equals(siteId)));
       
        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetAssignedBusinessServicesByServers(servers).ToList();
    }
    public async Task<List<ServerView>> GetByServerIdsAsync(List<string> ids)
    {
        var servers = SelectServer(_loggedInUserService.IsParent
            ? base.FilterBy(x => ids.Contains(x.ReferenceId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && ids.Contains(x.ReferenceId)));
       
        return await servers.ToListAsync();
    }

    public async Task<List<ServerView>> GetAllByServerIdsAsync(List<string> ids)
    {
        var servers = _loggedInUserService.IsParent
            ? base.FilterBy(x => ids.Contains(x.ReferenceId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && ids.Contains(x.ReferenceId));

        return await servers.ToListAsync();
    }
    public async Task<List<ServerView>> GetServerTypeByIds(List<string> serverIds)
    {
        var servers = (_loggedInUserService.IsParent
            ? base.FilterBy(x => serverIds.Contains(x.ReferenceId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && serverIds.Contains(x.ReferenceId)))
            .Select(x => new ServerView { ReferenceId=x.ReferenceId,Name=x.Name,ServerType=x.ServerType,IpAddress=x.IpAddress,Status=x.Status});

        return await servers.ToListAsync();
    }
    public async Task<List<ServerView>> GetServerByLicenseKey(string licenseId)
    {
        var servers = SelectServer(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.LicenseId.Equals(licenseId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.LicenseId.Equals(licenseId)));       

        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetAssignedBusinessServicesByServers(servers).ToList();
    }
    public async Task<List<ServerView>> GetServerType(string serverTypeId)
    {
        var servers = SelectServer(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ServerTypeId.Equals(serverTypeId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ServerTypeId.Equals(serverTypeId)));

     
        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetAssignedBusinessServicesByServers(servers).ToList();
    }
    public async Task<List<ServerView>> GetServerByOsType(string osTypeId)
    {
        var servers = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.OSTypeId.Equals(osTypeId)).Select(x => new ServerView
            {
                ReferenceId = x.ReferenceId,
                OSTypeId = x.OSTypeId,
                OSType = x.OSType,
                BusinessServiceId = x.BusinessServiceId
            })
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.OSTypeId.Equals(osTypeId)).Select(x => new ServerView
            {
                ReferenceId = x.ReferenceId,
                OSTypeId = x.OSTypeId,
                OSType = x.OSType,
                BusinessServiceId = x.BusinessServiceId
            });

        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetPaginatedAssignedBusinessServicesByServers(servers).ToList();
    }

    public async Task<List<ServerView>> GetServerOsTypeList()
    {
        var servers = _loggedInUserService.IsParent
            ? base.QueryAll(x=>x.IsActive).Select(x=> new ServerView
            {
                ReferenceId = x.ReferenceId,
                OSTypeId = x.OSTypeId,
                OSType = x.OSType,
                BusinessServiceId = x.BusinessServiceId
            })
            : base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).Select(x => new ServerView
            {
                ReferenceId = x.ReferenceId,
                OSTypeId = x.OSTypeId,
                OSType = x.OSType,
                BusinessServiceId = x.BusinessServiceId
            });

        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetPaginatedAssignedBusinessServicesByServers(servers).ToList();
    }

    public async Task<List<ServerView>> GetByUserName(string userName)
    {
        var servers = SelectServer(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.UserName.ToLower().Equals(userName))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.UserName.Equals(userName)));

        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetAssignedBusinessServicesByServers(servers).ToList();
    }

    public async Task<List<ServerView>> GetByUserNameAndOsTypeId(string userName, string osTypeId)
    {
        var splitOsTypeId = osTypeId.Split(',');

        var servers = SelectServer(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.UserName.ToLower().Equals(userName) && splitOsTypeId.Contains(x.OSTypeId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.UserName.ToLower().Equals(userName) && splitOsTypeId.Contains(x.OSTypeId)));

        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetAssignedBusinessServicesByServers(servers).ToList();
    }


    public async Task<List<ServerView>> GetServerByBusinessServiceId(string businessServiceId)
    {
        var servers = SelectServer(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId))
            : base.FilterBy(x =>
                x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.BusinessServiceId.Equals(businessServiceId)));

        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetAssignedBusinessServicesByServers(servers).ToList();
    }
    public async Task<List<ServerView>> GetByIpAddress(string ipAddress)
    {
       var servers = SelectServer(base.QueryAll(x => x.IpAddress.Equals(ipAddress)));
       
        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetAssignedBusinessServicesByServers(servers).ToList();
    }
    public async Task<List<ServerView>> GetServerByOsTypeIdAndFormVersion(string osTypeId, string formVersion)
    {
        var servers = SelectServer(base.FilterBy(x => x.OSTypeId.Equals(osTypeId) && x.FormVersion.Equals(formVersion)));

        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetAssignedBusinessServicesByServers(servers).ToList();
    }
    public override async Task<PaginatedResult<ServerView>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<ServerView> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await SelectServer(_loggedInUserService.IsAllInfra
                     ? Entities.Specify(productFilterSpec).DescOrderById()
                     : GetPaginatedAssignedBusinessServicesByServers(Entities.Specify(productFilterSpec))).DescOrderById()
                     .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        }

        return await SelectServer(_loggedInUserService.IsAllInfra
                ? Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()
                : GetPaginatedAssignedBusinessServicesByServers(Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()))
                .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public async Task<PaginatedResult<ServerView>> GetServerByOsType(string osTypeId, int pageNumber, int pageSize, Specification<ServerView> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await SelectServer(_loggedInUserService.IsAllInfra
                    ?Entities.Specify(productFilterSpec).Where(x => x.OSTypeId == osTypeId).DescOrderById()
                    :GetPaginatedAssignedBusinessServicesByServers(Entities.Specify(productFilterSpec).Where(x => x.OSTypeId == osTypeId).DescOrderById()))
                .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        }

        return await SelectServer(_loggedInUserService.IsAllInfra
                ? Entities.Specify(productFilterSpec).Where(x => x.OSTypeId == osTypeId && x.CompanyId == _loggedInUserService.CompanyId).DescOrderById()
                : GetPaginatedAssignedBusinessServicesByServers(Entities.Specify(productFilterSpec).Where(x => x.OSTypeId == osTypeId && x.CompanyId == _loggedInUserService.CompanyId).DescOrderById()))
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    
    private IQueryable<ServerView> GetPaginatedAssignedBusinessServicesByServers(IQueryable<ServerView> businessServices)
    {
        var assignedServiceIds = AssignedEntity.AssignedBusinessServices.Select(s => s.Id);

        businessServices = businessServices.Where(s => assignedServiceIds.Contains(s.BusinessServiceId));

        var infraObjects = _infraObjectRepository.GetPaginatedQuery();

        businessServices = businessServices.Where(server => infraObjects.Any(x =>x.ServerProperties.Contains(server.ReferenceId)));
        
        return businessServices;
    }

    public async Task<List<ServerView>> GetServersByOsTypeIds(List<string> osTypeIds)
    {
        var servers = _loggedInUserService.IsParent
                ? base.FilterBy(x => osTypeIds.Contains(x.OSTypeId)).Select(x => new ServerView
                {
                    ReferenceId = x.ReferenceId,
                    OSTypeId = x.OSTypeId,
                    OSType = x.OSType,
                    BusinessServiceId = x.BusinessServiceId
                })
                : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && osTypeIds.Contains(x.OSTypeId)).Select(x => new ServerView
                {
                    ReferenceId = x.ReferenceId,
                    OSTypeId = x.OSTypeId,
                    OSType = x.OSType,
                    BusinessServiceId = x.BusinessServiceId
                });

        return _loggedInUserService.IsAllInfra
            ? await servers.ToListAsync()
            : GetPaginatedAssignedBusinessServicesByServers(servers).ToList();

    }
    private IReadOnlyList<ServerView> GetAssignedBusinessServicesByServers(IQueryable<ServerView> businessServices)
    {
        var services = new List<ServerView>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                services.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                                  where businessService.BusinessServiceId == assignedBusinessService.Id
                                  select businessService);

        var infraObjects = _infraObjectRepository.GetPaginatedQuery();

        services = services.Where(server => infraObjects.Any(x =>x.ServerProperties.Contains(server.ReferenceId))).ToList();

        return services;
    }

    private IQueryable<ServerView> SelectServer(IQueryable<ServerView> query)
    {
        return query.Select(x => new ServerView
        {
            Id=x.Id,
            ReferenceId = x.ReferenceId,
            CompanyId = x.CompanyId,
            Name = x.Name,
            BusinessServiceId = x.BusinessServiceId,
            BusinessServiceName = x.BusinessServiceName,
            SiteId = x.SiteId,
            SiteName = x.SiteName,
            RoleTypeId = x.RoleTypeId,
            RoleType = x.RoleType,
            ServerTypeId = x.ServerTypeId,
            ServerType = x.ServerType,
            OSTypeId = x.OSTypeId,
            OSType = x.OSType,
            Status = x.Status,
            UserName = x.UserName,
            Properties = x.Properties,
            LicenseId = x.LicenseId,
            LicenseKey = SecurityHelper.Decrypt(x.LicenseKey),
            Version = x.Version,
            FormVersion = x.FormVersion,
            ExceptionMessage = x.ExceptionMessage,
            IsAttached = x.IsAttached,
            IpAddress = x.IpAddress,
            HostName = x.HostName,
            SubstituteAuthentication = x.SubstituteAuthentication,
            SubAuthenticationUsers = x.SubAuthenticationUsers,
            ConnectViaHostName =x.ConnectViaHostName,
            IsActive =x.IsActive            
        });
    }
}
