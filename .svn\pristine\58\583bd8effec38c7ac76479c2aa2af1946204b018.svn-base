﻿using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowHistoryModel;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowHistory.Queries;

public class GetWorkflowHistoryListQueryHandlerTests : IClassFixture<WorkflowHistoryFixture>
{
    private readonly WorkflowHistoryFixture _workflowHistoryFixture;
    private Mock<IWorkflowHistoryRepository> _mockWorkflowHistoryRepository;
    private readonly GetWorkflowHistoryListQueryHandler _handler;

    public GetWorkflowHistoryListQueryHandlerTests(WorkflowHistoryFixture workflowHistoryFixture)
    {
        _workflowHistoryFixture = workflowHistoryFixture;
        _mockWorkflowHistoryRepository = WorkflowHistoryRepositoryMocks.GetWorkflowHistoryRepository(_workflowHistoryFixture.WorkflowHistories);
        _handler = new GetWorkflowHistoryListQueryHandler(_workflowHistoryFixture.Mapper, _mockWorkflowHistoryRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowHistoryCount()
    {
        var result = await _handler.Handle(new GetWorkflowHistoryListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowHistoryListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowHistoryListQuery(), CancellationToken.None);

        _mockWorkflowHistoryRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_Valid_WorkflowHistoryDetail()
    {
        var result = await _handler.Handle(new GetWorkflowHistoryListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowHistoryListVm>>();

        result[0].Id.ShouldBe(_workflowHistoryFixture.WorkflowHistories[0].ReferenceId);
        result[0].WorkflowId.ShouldBe(_workflowHistoryFixture.WorkflowHistories[0].WorkflowId);
        result[0].WorkflowName.ShouldBe(_workflowHistoryFixture.WorkflowHistories[0].WorkflowName);
        result[0].Version.ShouldBe(_workflowHistoryFixture.WorkflowHistories[0].Version);
        result[0].Description.ShouldBe(_workflowHistoryFixture.WorkflowHistories[0].Description);
        result[0].LoginName.ShouldBe(_workflowHistoryFixture.WorkflowHistories[0].LoginName);
        result[0].Comments.ShouldBe(_workflowHistoryFixture.WorkflowHistories[0].Comments);
        result[0].CompanyId.ShouldBe(_workflowHistoryFixture.WorkflowHistories[0].CompanyId);
        result[0].Properties.ShouldBe(_workflowHistoryFixture.WorkflowHistories[0].Properties);
        result[0].UpdaterId.ShouldBe(_workflowHistoryFixture.WorkflowHistories[0].UpdaterId);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockWorkflowHistoryRepository = WorkflowHistoryRepositoryMocks.GetWorkflowHistoryEmptyRepository();

        var handler = new GetWorkflowHistoryListQueryHandler(_workflowHistoryFixture.Mapper, _mockWorkflowHistoryRepository.Object);

        var result = await handler.Handle(new GetWorkflowHistoryListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }
}