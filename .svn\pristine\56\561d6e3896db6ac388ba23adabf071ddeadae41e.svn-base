﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Update;

public class UpdateDerivedLicenseCommandValidator : AbstractValidator<UpdateDerivedLicenseCommand>
{
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILoggedInUserService _loggedInUserService;

    public UpdateDerivedLicenseCommandValidator(ILicenseManagerRepository licenseManagerRepository,
        ILoggedInUserService loggedInUserService)
    {
        _licenseManagerRepository = licenseManagerRepository;
        _loggedInUserService = loggedInUserService;

        RuleFor(p => p)
            .MustAsync(IsParentId)
            .WithMessage("Parent companies can update derived licenses for this activity.");

        RuleFor(p => p)
            .MustAsync(IsAuthorizedUser)
            .WithMessage("Derived license update is restricted to SiteAdmin and SuperAdmin users only.");

        RuleFor(p => p)
            .MustAsync(IsLicenseKeyPoNumberExist)
            .WithMessage("A derived licenses with the same po-number already exists.");
    }

    private Task<bool> IsParentId(UpdateDerivedLicenseCommand updateDerivedLicenseCommand,
        CancellationToken cancellation)
    {
        return Task.FromResult(_loggedInUserService.IsParent);
    }

    private Task<bool> IsAuthorizedUser(UpdateDerivedLicenseCommand updateDerivedLicenseCommand,
        CancellationToken cancellation)
    {
        return Task.FromResult(_loggedInUserService.IsSiteAdmin || _loggedInUserService.IsSuperAdmin);
    }

    private async Task<bool> IsLicenseKeyPoNumberExist(UpdateDerivedLicenseCommand e, CancellationToken token)
    {
        return !await _licenseManagerRepository.IsLicenseKeyPoNumberExist(e.PONumber, e.Id);
    }
}