﻿using ContinuityPatrol.Web.Areas.Admin.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller;

public class MenuBuilderControllerShould
{
    private readonly MenuBuilderController _controller;

    public MenuBuilderControllerShould()
    {
        _controller = new MenuBuilderController();
    }

    // ===== LIST METHOD TESTS =====

    [Fact]
    public void List_ReturnsViewResult()
    {
        // Act
        var result = _controller.List();

        // Assert
        Assert.NotNull(result);
        var viewResult = Assert.IsType<ViewResult>(result);
    }

    [Fact]
    public void List_HasDefaultViewName()
    {
        // Act
        var result = _controller.List() as ViewResult;

        // Assert
        Assert.NotNull(result);
        Assert.Null(result.ViewName); // Default view name should be null (uses action name)
    }

    [Fact]
    public void List_HasNoModel()
    {
        // Act
        var result = _controller.List() as ViewResult;

        // Assert
        Assert.NotNull(result);
        Assert.Null(result.Model); // No model is passed to the view
    }

    [Fact]
    public void List_ReturnsIActionResult()
    {
        // Act
        var result = _controller.List();

        // Assert
        Assert.NotNull(result);
        Assert.IsAssignableFrom<IActionResult>(result);
    }

    [Fact]
    public void List_ShouldBeConsistentOnMultipleCalls()
    {
        // Act
        var result1 = _controller.List() as ViewResult;
        var result2 = _controller.List() as ViewResult;

        // Assert
        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.Equal(result1.ViewName, result2.ViewName);
        Assert.Equal(result1.Model, result2.Model);
    }

    [Fact]
    public void List_ShouldReturnViewWithDefaultViewName()
    {
        // Act
        var result = _controller.List() as ViewResult;

        // Assert
        Assert.NotNull(result);
        Assert.Null(result.ViewName); // Default view name should be null (uses action name)
    }

    [Fact]
    public void List_ShouldReturnViewWithNullModel()
    {
        // Act
        var result = _controller.List() as ViewResult;

        // Assert
        Assert.NotNull(result);
        Assert.Null(result.Model); // No model is passed to the view
    }

    // ===== CONTROLLER INSTANTIATION TESTS =====

    [Fact]
    public void Constructor_ShouldCreateValidInstance()
    {
        // Act
        var controller = new MenuBuilderController();

        // Assert
        Assert.NotNull(controller);
        Assert.IsType<MenuBuilderController>(controller);
    }

    [Fact]
    public void Controller_ShouldInheritFromController()
    {
        // Act
        var controller = new MenuBuilderController();

        // Assert
        Assert.NotNull(controller);
        Assert.IsAssignableFrom<Microsoft.AspNetCore.Mvc.Controller>(controller);
    }

    [Fact]
    public void Controller_ShouldHaveAreaAttribute()
    {
        // Act
        var controller = new MenuBuilderController();
        var areaAttribute = controller.GetType().GetCustomAttributes(typeof(AreaAttribute), false).FirstOrDefault() as AreaAttribute;

        // Assert
        Assert.NotNull(areaAttribute);
        Assert.Equal("Admin", areaAttribute.RouteValue);
    }

    // ===== METHOD SIGNATURE TESTS =====

    [Fact]
    public void List_ShouldHaveCorrectMethodSignature()
    {
        // Arrange
        var methodInfo = typeof(MenuBuilderController).GetMethod("List");

        // Assert
        Assert.NotNull(methodInfo);
        Assert.Equal(typeof(IActionResult), methodInfo.ReturnType);
        Assert.Empty(methodInfo.GetParameters()); // No parameters
        Assert.True(methodInfo.IsPublic);
    }

    [Fact]
    public void List_ShouldNotBeAsync()
    {
        // Arrange
        var methodInfo = typeof(MenuBuilderController).GetMethod("List");

        // Assert
        Assert.NotNull(methodInfo);
        Assert.False(methodInfo.ReturnType == typeof(Task<IActionResult>));
        Assert.False(methodInfo.ReturnType.IsGenericType && methodInfo.ReturnType.GetGenericTypeDefinition() == typeof(Task<>));
    }

    // ===== CONTROLLER BEHAVIOR TESTS =====

    [Fact]
    public void Controller_ShouldNotRequireParameters()
    {
        // Act & Assert - Should not throw
        var controller = new MenuBuilderController();
        Assert.NotNull(controller);
    }

    [Fact]
    public void List_ShouldNotThrowException()
    {
        // Act & Assert - Should not throw
        var result = _controller.List();
        Assert.NotNull(result);
    }

    [Fact]
    public void List_ShouldReturnSameTypeOnMultipleCalls()
    {
        // Act
        var result1 = _controller.List();
        var result2 = _controller.List();

        // Assert
        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.Equal(result1.GetType(), result2.GetType());
    }
}
