﻿using ContinuityPatrol.Application.Features.LogViewer.Commands.Create;
using ContinuityPatrol.Application.Features.LogViewer.Commands.Update;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.LogViewerModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage;

public class LogViewerService : BaseClient, ILogViewerService
{
    public LogViewerService(IConfiguration config, IAppCache cacheService, ILogger<LogViewerService> logger) : base(config, cacheService, logger)
    {

    }
    public async Task<PaginatedResult<LogViewerListVm>> GetPaginatedLogViewerList(GetLogViewerPaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/logviewer/paginated-list{query}");

        return await Get<PaginatedResult<LogViewerListVm>>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateLogViewerCommand createLogViewer)
    {
        var request = new RestRequest("api/v6/logviewer", Method.Post);

        request.AddJsonBody(createLogViewer);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/logviewer/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateLogViewerCommand updateLogViewer)
    {
        var request = new RestRequest("api/v6/logviewer", Method.Put);

        request.AddJsonBody(updateLogViewer);

        return await Put<BaseResponse>(request);
    }
    public async Task<List<LogViewerListVm>> GetLogViewerList()
    {
        var request = new RestRequest($"api/v6/logviewer");

        return await Get<List<LogViewerListVm>>(request);
    }
    public async Task<GetLogViewerDetailVm> GetLogViewerDetail(string id)
    {
        var request = new RestRequest($"api/v6/logviewer/{id}");

        return await Get<GetLogViewerDetailVm>(request);
    }
    public async Task<bool> IsLogViewerNameUnique(string name, string? id)
    {
        var request = new RestRequest($"api/v6/logviewer/name-unique?name={name}&id={id}");

        return await Get<bool>(request);
    }
}