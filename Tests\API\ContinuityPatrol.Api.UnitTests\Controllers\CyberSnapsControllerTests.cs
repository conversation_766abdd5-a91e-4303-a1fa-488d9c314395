using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.CyberSnaps.Commands.Create;
using ContinuityPatrol.Application.Features.CyberSnaps.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberSnaps.Commands.Update;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetByGroupNameAndLinkedStatus;
using ContinuityPatrol.Domain.ViewModels.CyberSnapsModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPowerMax;


namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CyberSnapsControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CyberSnapsController _controller;
    private readonly CyberSnapsFixture _cyberSnapsFixture;

    public CyberSnapsControllerTests()
    {
        _cyberSnapsFixture = new CyberSnapsFixture();

        var testBuilder = new ControllerTestBuilder<CyberSnapsController>();
        _controller = testBuilder.CreateController(
            _ => new CyberSnapsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetCyberSnapss_ReturnsExpectedList()
    {
        // Arrange
        var expectedCyberSnapss = new List<CyberSnapsListVm>
        {
            _cyberSnapsFixture.CyberSnapsListVm,
            _cyberSnapsFixture.CyberSnapsListVm,
            _cyberSnapsFixture.CyberSnapsListVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberSnapsListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCyberSnapss);

        // Act
        var result = await _controller.GetCyberSnapss();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberSnapss = Assert.IsAssignableFrom<List<CyberSnapsListVm>>(okResult.Value);
        Assert.Equal(3, cyberSnapss.Count);
    }

    [Fact]
    public async Task GetCyberSnapsById_ReturnsExpectedDetail()
    {
        // Arrange
        var cyberSnapsId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberSnapsDetailQuery>(q => q.Id == cyberSnapsId), default))
            .ReturnsAsync(_cyberSnapsFixture.CyberSnapsDetailVm);

        // Act
        var result = await _controller.GetCyberSnapsById(cyberSnapsId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberSnaps = Assert.IsType<CyberSnapsDetailVm>(okResult.Value);
        Assert.Equal(_cyberSnapsFixture.CyberSnapsDetailVm.Name, cyberSnaps.Name);
    }

    [Fact]
    public async Task GetPaginatedCyberSnapss_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetCyberSnapsPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = new List<CyberSnapsListVm>
        {
            _cyberSnapsFixture.CyberSnapsListVm,
            _cyberSnapsFixture.CyberSnapsListVm
        };
        var expectedResults = PaginatedResult<CyberSnapsListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberSnapsPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberSnapss(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberSnapsListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task CreateCyberSnaps_ReturnsCreatedAtAction()
    {
        // Arrange
        var command = _cyberSnapsFixture.CreateCyberSnapsCommand;
        var expectedMessage = "CyberSnaps has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberSnapsResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberSnaps(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberSnapsResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberSnaps_ReturnsOk()
    {
        // Arrange
        var command = _cyberSnapsFixture.UpdateCyberSnapsCommand;
        var expectedMessage = "CyberSnaps has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberSnapsResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberSnaps(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberSnapsResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteCyberSnaps_ReturnsOk()
    {
        // Arrange
        var cyberSnapsId = Guid.NewGuid().ToString();
        var expectedMessage = "CyberSnaps has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCyberSnapsCommand>(c => c.Id == cyberSnapsId), default))
            .ReturnsAsync(new DeleteCyberSnapsResponse
            {
                Message = expectedMessage,
                IsActive = false
            });

        // Act
        var result = await _controller.DeleteCyberSnaps(cyberSnapsId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteCyberSnapsResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public async Task IsCyberSnapsNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var snapsName = "Existing Snapshot";
        var snapsId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberSnapsNameUniqueQuery>(q => 
                q.Name == snapsName && q.Id == snapsId), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsCyberSnapsNameExist(snapsName, snapsId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.True(exists);
    }

    [Fact]
    public async Task IsCyberSnapsNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var snapsName = "Unique Snapshot Name";
        var snapsId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberSnapsNameUniqueQuery>(q => 
                q.Name == snapsName && q.Id == snapsId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsCyberSnapsNameExist(snapsName, snapsId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }

    [Fact]
    public async Task GetCyberSnapss_HandlesEmptyList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberSnapsListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<CyberSnapsListVm>());

        // Act
        var result = await _controller.GetCyberSnapss();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberSnapss = Assert.IsAssignableFrom<List<CyberSnapsListVm>>(okResult.Value);
        Assert.Empty(cyberSnapss);
    }

    [Fact]
    public async Task GetCyberSnapsById_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetCyberSnapsById(invalidId));
    }

    [Fact]
    public async Task GetCyberSnapsById_HandlesNotFound()
    {
        // Arrange
        var cyberSnapsId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberSnapsDetailQuery>(q => q.Id == cyberSnapsId), default))
            .ThrowsAsync(new NotFoundException("CyberSnaps", cyberSnapsId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetCyberSnapsById(cyberSnapsId));
    }

    [Fact]
    public async Task DeleteCyberSnaps_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteCyberSnaps(invalidId));
    }

    [Fact]
    public async Task CreateCyberSnaps_HandlesEnterpriseSnapshot()
    {
        // Arrange
        var command = new CreateCyberSnapsCommand
        {
            Name = "Enterprise Mission-Critical Database Snapshot",
            Description = "Comprehensive snapshot of the enterprise mission-critical database system including all transaction logs, indexes, stored procedures, and metadata for complete disaster recovery capability with point-in-time recovery options",
            Tag = "ENTERPRISE-MISSION-CRITICAL-DB-SNAP-001",
            Type = "Enterprise Database",
            Location = "/enterprise/snapshots/mission-critical/database/production/",
            Age = 0,
            Size = 102400
        };

        var expectedMessage = "CyberSnaps has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberSnapsResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberSnaps(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberSnapsResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberSnaps_HandlesSnapshotAging()
    {
        // Arrange
        var command = new UpdateCyberSnapsCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Aged Enterprise Database Snapshot",
            Description = "Enterprise database snapshot that has been aged for retention policy compliance",
            Tag = "AGED-ENTERPRISE-DB-SNAP-001",
            Type = "Aged Database",
            Location = "/enterprise/snapshots/aged/database/",
            Age = 90,
            Size = 1024
        };

        var expectedMessage = "CyberSnaps has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberSnapsResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberSnaps(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberSnapsResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task GetPaginatedCyberSnapss_HandlesSnapshotTypeFiltering()
    {
        // Arrange
        var query = new GetCyberSnapsPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Database"
        };

        var expectedData = new List<CyberSnapsListVm>
        {
            new CyberSnapsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Database Snapshot 1",
                Type = "Database",
                Size = 2048,
                LinkedStatus = "Linked",
                AvailableStatus = "Available"
            },
            new CyberSnapsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Database Snapshot 2",
                Type = "Database",
                Size = 4096,
                LinkedStatus = "Linked",
                AvailableStatus = "Available"
            }
        };
        var expectedResults = PaginatedResult<CyberSnapsListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberSnapsPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && 
                q.PageSize == query.PageSize && 
                q.SearchString == query.SearchString), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberSnapss(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberSnapsListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, snap => Assert.Equal("Database", snap.Type));
    }

    [Fact]
    public async Task CreateCyberSnaps_HandlesFileSystemSnapshot()
    {
        // Arrange
        var command = new CreateCyberSnapsCommand
        {
            Name = "Enterprise File System Snapshot",
            Description = "Comprehensive file system snapshot including application data, configuration files, logs, and user data with deduplication and compression",
            Tag = "ENTERPRISE-FS-SNAP-001",
            Type = "File System",
            Location = "/enterprise/snapshots/filesystem/production/",
            Age = 0,
            Size = 51200
        };

        var expectedMessage = "File System CyberSnaps created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberSnapsResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberSnaps(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberSnapsResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("File System", response.Message);
    }

    [Fact]
    public async Task GetCyberSnapsById_HandlesDetailedSnapshotInformation()
    {
        // Arrange
        var snapshotId = Guid.NewGuid().ToString();
        var detailedSnapshot = new CyberSnapsDetailVm
        {
            Id = snapshotId,
            Name = "Enterprise Comprehensive Snapshot",
            Description = "Multi-tier enterprise snapshot with complete application stack, database, and infrastructure configuration",
            Tag = "ENTERPRISE-COMPREHENSIVE-SNAP-001",
            Type = "Multi-Tier Enterprise",
            Location = "/enterprise/snapshots/comprehensive/tier1/",
            Age = 2,
            Size = 204800,
            SID = "ENT-COMP-SID-001",
            TimeStamp = DateTime.Now.AddHours(-2).ToString("yyyy-MM-dd HH:mm:ss"),
            StorageGroupName = "ENTERPRISE-TIER1-SG",
            SnapshotName = "ENT-COMP-SNAP-20240115",
            LinkSG = "ENT-COMP-LINK-SG",
            LinkedSGTime = DateTime.Now.AddHours(-1).ToString("yyyy-MM-dd HH:mm:ss"),
            UnlinkSGTime = null,
            Remark = "Comprehensive enterprise snapshot with full application stack including web tier (8 servers), application tier (12 servers), database tier (4 servers), and supporting infrastructure. Includes automated consistency checks, encryption validation, and recovery testing.",
            LinkedStatus = "Linked",
            AvailableStatus = "Available",
            SecureStatus = "Secure",
            Gen = "Gen-1"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberSnapsDetailQuery>(q => q.Id == snapshotId), default))
            .ReturnsAsync(detailedSnapshot);

        // Act
        var result = await _controller.GetCyberSnapsById(snapshotId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var snapshot = Assert.IsType<CyberSnapsDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Comprehensive Snapshot", snapshot.Name);
        Assert.Equal("Multi-Tier Enterprise", snapshot.Type);
        Assert.Equal(204800, snapshot.Size);
        Assert.Equal("Secure", snapshot.SecureStatus);
        Assert.Contains("web tier", snapshot.Remark);
    }

    [Fact]
    public async Task UpdateCyberSnaps_HandlesSnapshotLinking()
    {
        // Arrange
        var command = new UpdateCyberSnapsCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Linked Enterprise Snapshot",
            Description = "Enterprise snapshot linked to storage group for active recovery operations",
            Tag = "LINKED-ENTERPRISE-SNAP-001",
            Type = "Linked Database",
            Location = "/enterprise/snapshots/linked/database/",
            Age = 1,
            Size = 25600
        };

        var expectedMessage = "CyberSnaps linked successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberSnapsResponse
            {
                Message = expectedMessage,
                Id = command.Id,
               
            });

        // Act
        var result = await _controller.UpdateCyberSnaps(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberSnapsResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
       
    }

    [Fact]
    public async Task GetPaginatedCyberSnapss_HandlesAdvancedFiltering()
    {
        // Arrange
        var query = new GetCyberSnapsPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 15,
            SearchString = "Enterprise",
           
        };

        var expectedData = new List<CyberSnapsListVm>
        {
            new CyberSnapsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Database Snapshot",
                Type = "Database",
                Age = 3,
                Size = 10240,
                LinkedStatus = "Linked",
                AvailableStatus = "Available",
                SecureStatus = "Secure"
            }
        };
        var expectedResults = PaginatedResult<CyberSnapsListVm>.Success(expectedData, 1, 1, 15);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberSnapsPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber &&
                q.PageSize == query.PageSize &&
                q.SearchString == query.SearchString 
               ), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberSnapss(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberSnapsListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        Assert.Equal("Database", paginatedResult.Data.First().Type);
        Assert.Equal("Available", paginatedResult.Data.First().AvailableStatus);
        Assert.True(paginatedResult.Data.First().Age < 7);
    }

    [Fact]
    public async Task IsCyberSnapsNameExist_HandlesComplexNamingConventions()
    {
        // Arrange
        var complexSnapshotName = "Enterprise-DB-PROD-Snapshot_v2.1.0_(Daily-Backup)_[2024-01-15]";
        var snapshotId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberSnapsNameUniqueQuery>(q =>
                q.Name == complexSnapshotName && q.Id == snapshotId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsCyberSnapsNameExist(complexSnapshotName, snapshotId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }

    [Fact]
    public async Task GetCyberSnapss_HandlesMultipleSnapshotTypes()
    {
        // Arrange
        var multiTypeSnapshots = new List<CyberSnapsListVm>
        {
            new CyberSnapsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Database Snapshot",
                Type = "Database",
                Size = 5120,
                LinkedStatus = "Linked",
                AvailableStatus = "Available"
            },
            new CyberSnapsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "File System Snapshot",
                Type = "FileSystem",
                Size = 10240,
                LinkedStatus = "Unlinked",
                AvailableStatus = "Available"
            },
            new CyberSnapsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Application Snapshot",
                Type = "Application",
                Size = 2048,
                LinkedStatus = "Linked",
                AvailableStatus = "Unavailable"
            },
            new CyberSnapsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "VM Snapshot",
                Type = "VirtualMachine",
                Size = 20480,
                LinkedStatus = "Linked",
                AvailableStatus = "Available"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberSnapsListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(multiTypeSnapshots);

        // Act
        var result = await _controller.GetCyberSnapss();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var snapshots = Assert.IsAssignableFrom<List<CyberSnapsListVm>>(okResult.Value);
        Assert.Equal(4, snapshots.Count);
        Assert.Contains(snapshots, s => s.Type == "Database");
        Assert.Contains(snapshots, s => s.Type == "FileSystem");
        Assert.Contains(snapshots, s => s.Type == "Application");
        Assert.Contains(snapshots, s => s.Type == "VirtualMachine");
    }

    [Fact]
    public async Task CreateCyberSnaps_HandlesVirtualMachineSnapshot()
    {
        // Arrange
        var command = new CreateCyberSnapsCommand
        {
            Name = "Enterprise VM Infrastructure Snapshot",
            Description = "Complete virtual machine infrastructure snapshot including all VMs, configurations, network settings, and storage allocations for disaster recovery",
            Tag = "ENTERPRISE-VM-INFRA-SNAP-001",
            Type = "Virtual Machine Infrastructure",
            Location = "/enterprise/snapshots/vm-infrastructure/production/",
            Age = 0,
            Size = 512000
        };

        var expectedMessage = "VM Infrastructure CyberSnaps created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberSnapsResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberSnaps(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberSnapsResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("VM Infrastructure", response.Message);
    }

    [Fact]
    public async Task GetCyberSnapsByStorageGroupNameAndLinkedStatus_ReturnsExpectedSnapshots()
    {
        // Arrange
        var storageGroupName = "ENTERPRISE-PROD-SG-001";
        var linkedStatus = "Linked";
        var snapShotName = "ENT-DB-SNAP-MASTER-20240115";

        var expectedSnapshots = new List<CyberSnapsListVm>
        {
            new CyberSnapsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Production Database Snapshot",
                Type = "Database",
                Tag = "PROD-DB-SNAP-001",
                Location = "/enterprise/storage/prod/db/snapshots/",
                Age = 1,
                Size = 51200,
                LinkedStatus = "Linked",
                AvailableStatus = "Available",
                SecureStatus = "Secure"
            },
            new CyberSnapsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Production Application Snapshot",
                Type = "Application",
                Tag = "PROD-APP-SNAP-001",
                Location = "/enterprise/storage/prod/app/snapshots/",
                Age = 2,
                Size = 25600,
                LinkedStatus = "Linked",
                AvailableStatus = "Available",
                SecureStatus = "Secure"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberSnapsByGroupNameAndLinkedStatusQuery>(q =>
                q.StorageGroupName == storageGroupName && q.LinkedStatus == linkedStatus&&q.SnapshotName== snapShotName), default))
            .ReturnsAsync(expectedSnapshots);

        // Act
        var result = await _controller.GetCyberSnapsByStorageGroupNameAndLinkedStatus(storageGroupName, linkedStatus, snapShotName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var snapshots = Assert.IsAssignableFrom<List<CyberSnapsListVm>>(okResult.Value);
        Assert.Equal(2, snapshots.Count);
        Assert.All(snapshots, snap => Assert.Equal("Linked", snap.LinkedStatus));
        Assert.All(snapshots, snap => Assert.Equal("Available", snap.AvailableStatus));
        Assert.Contains(snapshots, snap => snap.Type == "Database");
        Assert.Contains(snapshots, snap => snap.Type == "Application");
    }

    [Fact]
    public async Task GetPowerMaxMonitorStatus_ReturnsExpectedStatus()
    {
        // Arrange
        var expectedPowerMaxDetails = new List<PowerMaxDetailVm>
        {
            _cyberSnapsFixture.PowerMaxDetailVm,
            new PowerMaxDetailVm
            {
                IpAddress = "***********",
                Version = "*********",
                Array = "000297900002",
                ModelName = "PowerMax 2000",
                StorageGroupMonitoring = new List<StorageGroupMonitoring>
                {
                    new StorageGroupMonitoring
                    {
                        StorageGroupName = "ENTERPRISE-SG-PROD-02",
                        Compliance = "Compliant",
                        SRP = "SRP-PROD",
                        ServiceLevel = "Gold",
                        Capacity = "5000GB",
                        Emulation = "FBA",
                        SRDFReplicationstatus = "Synchronized",
                        SnapshotCount = "15",
                        SnapshotDetails = new List<SnapshotDetail>
                        {
                            new SnapshotDetail
                            {
                                SnapshotId = "SNAP-PROD-002",
                                SnapshotName = "Enterprise-Snapshot-PROD-02",
                                CreationTime = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd HH:mm:ss"),
                                LinkedStatus = "Linked",
                                Restored = "No",
                                Expired = "No",
                                ExpiryTime = DateTime.Now.AddDays(30).ToString("yyyy-MM-dd HH:mm:ss"),
                                Secured = "Yes"
                            }
                        }
                    }
                }
            }
        };

        var name = "PowerMax-Array-001";
        var isSnap = true;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPowerMaxDetailsQuery>(q =>
                q.Name == name && q.IsSnap == isSnap), default))
            .ReturnsAsync(expectedPowerMaxDetails);

        // Act
        var result = await _controller.GetPowerMaxMonitorStatus(name, isSnap);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var powerMaxDetails = Assert.IsAssignableFrom<List<PowerMaxDetailVm>>(okResult.Value);
        Assert.Equal(2, powerMaxDetails.Count);

        var powerMax8000 = powerMaxDetails.First(p => p.ModelName == "PowerMax 8000");
        Assert.Equal("000297900001", powerMax8000.Array);
        Assert.Equal("***********", powerMax8000.IpAddress);
        Assert.Equal("*********", powerMax8000.Version);
        Assert.NotEmpty(powerMax8000.StorageGroupMonitoring);

        var powerMax2000 = powerMaxDetails.First(p => p.ModelName == "PowerMax 2000");
        Assert.Equal("000297900002", powerMax2000.Array);
        Assert.Equal("***********", powerMax2000.IpAddress);
        Assert.Equal("*********", powerMax2000.Version);
        Assert.Single(powerMax2000.StorageGroupMonitoring);

        // Validate StorageGroupMonitoring details
        var storageGroup = powerMax2000.StorageGroupMonitoring.First();
        Assert.Equal("ENTERPRISE-SG-PROD-02", storageGroup.StorageGroupName);
        Assert.Equal("Compliant", storageGroup.Compliance);
        Assert.Equal("15", storageGroup.SnapshotCount);
        Assert.Single(storageGroup.SnapshotDetails);

        // Validate SnapshotDetail
        var snapshotDetail = storageGroup.SnapshotDetails.First();
        Assert.Equal("SNAP-PROD-002", snapshotDetail.SnapshotId);
        Assert.Equal("Linked", snapshotDetail.LinkedStatus);
        Assert.Equal("Yes", snapshotDetail.Secured);
    }

    [Fact]
    public async Task GetCyberSnapsByStorageGroupNameAndLinkedStatus_HandlesUnlinkedSnapshots()
    {
        // Arrange
        var storageGroupName = "ENTERPRISE-DEV-SG-001";
        var linkedStatus = "Unlinked";
        var snapshotName = "ENT-DB-SNAP-MASTER-20240115";

        var expectedSnapshots = new List<CyberSnapsListVm>
        {
            new CyberSnapsListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Enterprise Development Snapshot",
                Type = "Development",
                Tag = "DEV-SNAP-001",
                Location = "/enterprise/storage/dev/snapshots/",
                Age = 7,
                Size = 10240,
                LinkedStatus = "Unlinked",
                AvailableStatus = "Available",
                SecureStatus = "Secure"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberSnapsByGroupNameAndLinkedStatusQuery>(q =>
                q.StorageGroupName == storageGroupName && q.LinkedStatus == linkedStatus&&q.SnapshotName== snapshotName), default))
            .ReturnsAsync(expectedSnapshots);

        // Act
        var result = await _controller.GetCyberSnapsByStorageGroupNameAndLinkedStatus(storageGroupName, linkedStatus,snapshotName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var snapshots = Assert.IsAssignableFrom<List<CyberSnapsListVm>>(okResult.Value);
        Assert.Single(snapshots);
        Assert.Equal("Unlinked", snapshots.First().LinkedStatus);
        Assert.Equal("Development", snapshots.First().Type);
    }

    [Fact]
    public async Task GetPowerMaxMonitorStatus_HandlesEmptyArrays()
    {
        // Arrange
        var name = "NonExistent-Array";
        var isSnap = false;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPowerMaxDetailsQuery>(q =>
                q.Name == name && q.IsSnap == isSnap), default))
            .ReturnsAsync(new List<PowerMaxDetailVm>());

        // Act
        var result = await _controller.GetPowerMaxMonitorStatus(name, isSnap);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var powerMaxDetails = Assert.IsAssignableFrom<List<PowerMaxDetailVm>>(okResult.Value);
        Assert.Empty(powerMaxDetails);
    }

    [Fact]
    public async Task GetCyberSnapsByStorageGroupNameAndLinkedStatus_HandlesEmptyResults()
    {
        // Arrange
        var storageGroupName = "NON-EXISTENT-SG";
        var linkedStatus = "Linked";
        var snapShotName = "ENT-DB-SNAP-MASTER-20240115";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberSnapsByGroupNameAndLinkedStatusQuery>(q =>
                q.StorageGroupName == storageGroupName && q.LinkedStatus == linkedStatus&&q.SnapshotName==snapShotName), default))
            .ReturnsAsync(new List<CyberSnapsListVm>());

        // Act
        var result = await _controller.GetCyberSnapsByStorageGroupNameAndLinkedStatus(storageGroupName, linkedStatus,snapShotName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var snapshots = Assert.IsAssignableFrom<List<CyberSnapsListVm>>(okResult.Value);
        Assert.Empty(snapshots);
    }

    [Fact]
    public async Task GetPowerMaxMonitorStatus_HandlesComplexStorageConfiguration()
    {
        // Arrange
        var complexPowerMaxDetails = new List<PowerMaxDetailVm>
        {
            new PowerMaxDetailVm
            {
                IpAddress = "************",
                Version = "*********",
                Array = "000297900100",
                ModelName = "PowerMax 8000 Enterprise",
                StorageGroupMonitoring = new List<StorageGroupMonitoring>
                {
                    new StorageGroupMonitoring
                    {
                        StorageGroupName = "ENTERPRISE-TIER1-SG-001",
                        Compliance = "Compliant",
                        SRP = "SRP-TIER1",
                        ServiceLevel = "Diamond",
                        Capacity = "50000GB",
                        Emulation = "FBA",
                        SRDFReplicationstatus = "Synchronized",
                        SnapshotCount = "100",
                        SnapshotDetails = new List<SnapshotDetail>
                        {
                            new SnapshotDetail
                            {
                                SnapshotId = "SNAP-TIER1-001",
                                SnapshotName = "Enterprise-Critical-DB-Snapshot",
                                CreationTime = DateTime.Now.AddHours(-2).ToString("yyyy-MM-dd HH:mm:ss"),
                                LinkedStatus = "Linked",
                                Restored = "No",
                                Expired = "No",
                                ExpiryTime = DateTime.Now.AddDays(90).ToString("yyyy-MM-dd HH:mm:ss"),
                                Secured = "Yes"
                            },
                            new SnapshotDetail
                            {
                                SnapshotId = "SNAP-TIER1-002",
                                SnapshotName = "Enterprise-App-Data-Snapshot",
                                CreationTime = DateTime.Now.AddHours(-4).ToString("yyyy-MM-dd HH:mm:ss"),
                                LinkedStatus = "Unlinked",
                                Restored = "Yes",
                                Expired = "No",
                                ExpiryTime = DateTime.Now.AddDays(60).ToString("yyyy-MM-dd HH:mm:ss"),
                                Secured = "Yes"
                            }
                        }
                    },
                    new StorageGroupMonitoring
                    {
                        StorageGroupName = "ENTERPRISE-TIER2-SG-002",
                        Compliance = "Compliant",
                        SRP = "SRP-TIER2",
                        ServiceLevel = "Gold",
                        Capacity = "25000GB",
                        Emulation = "CKD",
                        SRDFReplicationstatus = "Not Synchronized",
                        SnapshotCount = "50",
                        SnapshotDetails = new List<SnapshotDetail>
                        {
                            new SnapshotDetail
                            {
                                SnapshotId = "SNAP-TIER2-001",
                                SnapshotName = "Enterprise-Archive-Snapshot",
                                CreationTime = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd HH:mm:ss"),
                                LinkedStatus = "Linked",
                                Restored = "No",
                                Expired = "No",
                                ExpiryTime = DateTime.Now.AddDays(365).ToString("yyyy-MM-dd HH:mm:ss"),
                                Secured = "Yes"
                            }
                        }
                    }
                }
            }
        };

        var name = "Enterprise-PowerMax-Complex";
        var isSnap = true;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPowerMaxDetailsQuery>(q =>
                q.Name == name && q.IsSnap == isSnap), default))
            .ReturnsAsync(complexPowerMaxDetails);

        // Act
        var result = await _controller.GetPowerMaxMonitorStatus(name, isSnap);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var powerMaxDetails = Assert.IsAssignableFrom<List<PowerMaxDetailVm>>(okResult.Value);
        Assert.Single(powerMaxDetails);

        var powerMaxArray = powerMaxDetails.First();
        Assert.Equal("000297900100", powerMaxArray.Array);
        Assert.Equal("PowerMax 8000 Enterprise", powerMaxArray.ModelName);
        Assert.Equal(2, powerMaxArray.StorageGroupMonitoring.Count);

        // Validate Tier 1 Storage Group
        var tier1SG = powerMaxArray.StorageGroupMonitoring.First(sg => sg.StorageGroupName == "ENTERPRISE-TIER1-SG-001");
        Assert.Equal("Diamond", tier1SG.ServiceLevel);
        Assert.Equal("100", tier1SG.SnapshotCount);
        Assert.Equal(2, tier1SG.SnapshotDetails.Count);
        Assert.Contains(tier1SG.SnapshotDetails, sd => sd.SnapshotName == "Enterprise-Critical-DB-Snapshot");
        Assert.Contains(tier1SG.SnapshotDetails, sd => sd.SnapshotName == "Enterprise-App-Data-Snapshot");

        // Validate Tier 2 Storage Group
        var tier2SG = powerMaxArray.StorageGroupMonitoring.First(sg => sg.StorageGroupName == "ENTERPRISE-TIER2-SG-002");
        Assert.Equal("Gold", tier2SG.ServiceLevel);
        Assert.Equal("50", tier2SG.SnapshotCount);
        Assert.Single(tier2SG.SnapshotDetails);
        Assert.Equal("Enterprise-Archive-Snapshot", tier2SG.SnapshotDetails.First().SnapshotName);
    }

    [Fact]
    public void PowerMaxDetailVm_FixtureCreatesValidData()
    {
        // Arrange & Act
        var powerMaxDetail = _cyberSnapsFixture.PowerMaxDetailVm;

        // Assert
        Assert.NotNull(powerMaxDetail);
        Assert.Equal("***********", powerMaxDetail.IpAddress);
        Assert.Equal("*********", powerMaxDetail.Version);
        Assert.Equal("000297900001", powerMaxDetail.Array);
        Assert.Equal("PowerMax 8000", powerMaxDetail.ModelName);
        Assert.NotNull(powerMaxDetail.StorageGroupMonitoring);
        Assert.NotEmpty(powerMaxDetail.StorageGroupMonitoring);

        // Validate StorageGroupMonitoring structure
        var storageGroup = powerMaxDetail.StorageGroupMonitoring.First();
        Assert.NotNull(storageGroup.StorageGroupName);
        Assert.NotNull(storageGroup.Compliance);
        Assert.NotNull(storageGroup.SnapshotDetails);
        Assert.NotEmpty(storageGroup.SnapshotDetails);

        // Validate SnapshotDetail structure
        var snapshotDetail = storageGroup.SnapshotDetails.First();
        Assert.NotNull(snapshotDetail.SnapshotId);
        Assert.NotNull(snapshotDetail.SnapshotName);
        Assert.NotNull(snapshotDetail.LinkedStatus);
        Assert.NotNull(snapshotDetail.Secured);
    }
}
