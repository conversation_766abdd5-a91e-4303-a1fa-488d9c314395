﻿using ContinuityPatrol.Application.Features.Report.Queries.BusinessServiceSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Report.Controllers
{
    [Area("Report")]
    public class ViewReport : Controller
    {
      
        public static List<BusinessServiceSummaryReportVm> Reporter = new List<BusinessServiceSummaryReportVm>();         
        public static List<WorkflowOperationDrDrillReportVm>DrDrillReporters = new List<WorkflowOperationDrDrillReportVm>();
        

        private readonly IDataProvider _provider;
        private readonly ILogger<ViewReport> _logger;
        public ViewReport(IDataProvider provider, ILogger<ViewReport> logger)
        {

            _provider = provider;
            _logger = logger;
        }

 
        [AntiXss]
        public IActionResult List()
        {

            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AntiXss]
        public async Task<IActionResult> List(string report)
        {
            try
            {
                if (report == "1")
                {
                    Reporter.Clear();
                    var value = await _provider.Report.GetBusinessServiceSummaryReport();
                    foreach (var item in value.BusinessServiceSummaryReportVms)
                    {
                        Reporter.Add(item);
                    }
                }
                else if (report == "2")
                {
                   // var drillreportee = await _provider.Report.GetDrDrillReportByWorkflowOperationId("a721ddea-8a47-46f6-9d45-3f2e565161ce");

                   // DrDrillReporters.Add(drillreportee);

                }
                if (Convert.ToString(Request.Form["Report"]) != null || Convert.ToString(Request.Form["Report"]) != "")
                {
                    string selectedValue = Convert.ToString(Request.Form["Report"]);
                    TempData["selectedValue"] = selectedValue.ToString();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError( $"An error occured on while processing the request for view report list.{ex.GetMessage()}");
            }


            return RedirectToAction("List");
        }
    }
}
