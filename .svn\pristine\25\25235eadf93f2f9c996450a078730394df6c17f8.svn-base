﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IHeatMapLogRepository : IRepository<HeatMapLog>
{
    IQueryable<HeatMapLog> GetHeatMapLogType(string type);
    Task<PaginatedResult<HeatMapLog>> GetHeatMapLogType(string type, int pageNumber, int pageSize, Specification<HeatMapLog> productFilterSpec,string sortColumn,string sortOrder);
}