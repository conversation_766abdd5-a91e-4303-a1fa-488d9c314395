using ContinuityPatrol.Application.Features.AdPasswordExpire.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordExpire.Events;

public class DeleteAdPasswordExpireEventTests : IClassFixture<AdPasswordExpireFixture>, IClassFixture<UserActivityFixture>
{
    private readonly AdPasswordExpireFixture _adPasswordExpireFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly AdPasswordExpireDeletedEventHandler _handler;

    public DeleteAdPasswordExpireEventTests(AdPasswordExpireFixture adPasswordExpireFixture, UserActivityFixture userActivityFixture)
    {
        _adPasswordExpireFixture = adPasswordExpireFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");
        mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/adpasswordexpire");
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        var mockAdPasswordExpireEventLogger = new Mock<ILogger<AdPasswordExpireDeletedEventHandler>>();

        _mockUserActivityRepository = CompanyRepositoryMocks.CreateCompanyEventRepository(_userActivityFixture.UserActivities);

        _handler = new AdPasswordExpireDeletedEventHandler(
            mockLoggedInUserService.Object, 
            mockAdPasswordExpireEventLogger.Object, 
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteAdPasswordExpireEventCreated()
    {
        // Arrange
        _userActivityFixture.UserActivities[0].LoginName = "Tester";
        var adPasswordExpireDeletedEvent = new AdPasswordExpireDeletedEvent { Name = "TestUser" };

        // Act
        var result = _handler.Handle(adPasswordExpireDeletedEvent, CancellationToken.None);

        // Assert
        Assert.True(result.IsCompleted);
        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var adPasswordExpireDeletedEvent = new AdPasswordExpireDeletedEvent { Name = "TestUser" };

        // Act
        await _handler.Handle(adPasswordExpireDeletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_WithCorrectProperties()
    {
        // Arrange
        var adPasswordExpireDeletedEvent = new AdPasswordExpireDeletedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(adPasswordExpireDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.LoginName.ShouldBe("Tester");
        capturedUserActivity.Action.ShouldContain("Delete ADPasswordExpire");
        capturedUserActivity.Entity.ShouldBe("ADPasswordExpire");
        capturedUserActivity.ActivityType.ShouldBe("Delete");
        capturedUserActivity.ActivityDetails.ShouldContain("TestUser");
        capturedUserActivity.ActivityDetails.ShouldContain("deleted successfully");
    }

    [Fact]
    public async Task Handle_LogCorrectInformation_When_AdPasswordExpireDeleted()
    {
        // Arrange
        var adPasswordExpireDeletedEvent = new AdPasswordExpireDeletedEvent { Name = "TestUser" };
        var mockLogger = new Mock<ILogger<AdPasswordExpireDeletedEventHandler>>();

        var handler = new AdPasswordExpireDeletedEventHandler(
            new Mock<ILoggedInUserService>().Object,
            mockLogger.Object,
            _mockUserActivityRepository.Object);

        // Act
        await handler.Handle(adPasswordExpireDeletedEvent, CancellationToken.None);

       
    }

    [Fact]
    public async Task Handle_SetCorrectUserActivityProperties_When_UserServiceHasData()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var testUrl = "/api/test";
        var testIpAddress = "***********";
        var testLoginName = "TestUser";

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);
        mockLoggedInUserService.Setup(x => x.LoginName).Returns(testLoginName);
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testUrl);
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testIpAddress);

        var handler = new AdPasswordExpireDeletedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<AdPasswordExpireDeletedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var adPasswordExpireDeletedEvent = new AdPasswordExpireDeletedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(adPasswordExpireDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.UserId.ShouldBe(testUserId);
        capturedUserActivity.LoginName.ShouldBe(testLoginName);
        capturedUserActivity.RequestUrl.ShouldBe(testUrl);
        capturedUserActivity.HostAddress.ShouldBe(testIpAddress);
    }
}
