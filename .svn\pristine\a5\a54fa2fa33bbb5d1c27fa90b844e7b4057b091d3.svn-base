﻿using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Impl;
using ContinuityPatrol.Infrastructure.Job;
using ContinuityPatrol.Infrastructure.Services;

namespace ContinuityPatrol.Infrastructure.Extension;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration config)
    {
        services.AddQuartz();


        services.AddQuartz(q =>
        {
            // Add dependency injection.
            q.UseMicrosoftDependencyInjectionJobFactory(options =>
            {
                // if we don't have the job in DI, allow fallback
                // to configure via default constructor
                options.AllowDefaultConstructor = true;
            });
        });


        // Add Quartz Hosted Service
        services.AddQuartzHostedService();
        services.AddSingleton<IJobScheduler, JobScheduler>();
        services.AddTransient<IWindowsService, WindowsService>();
        
        services.AddTransient<ILicenseValidationService, LicenseValidationService>();
        services.AddTransient<IEmailService, EmailService>();
        services.AddTransient<ISeqService, SeqService>();
        services.AddTransient<ISymmetrixService, SymmetrixService>();
        services.AddHttpClient<ISmsService, SmsService>();

       // services.AddTransient<ISmtpClientWrapper, SmtpClientWrapper>();
        services.AddTransient<ISmtpClientFactory, SmtpClientFactory>();

        return services;
    }
}