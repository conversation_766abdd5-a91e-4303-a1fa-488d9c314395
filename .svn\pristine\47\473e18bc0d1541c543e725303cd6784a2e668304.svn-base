﻿namespace ContinuityPatrol.Domain.ViewModels.UserActivityModel;

public class UserActivityViewModel
{
    public string Id { get; set; }
    public string Action { get; set; }
    public string LoginName { get; set; }
    public string Entity { get; set; }
    public string ActivityType { get; set; }
    public string RequestUrl { get; set; }
    public string HostAddress { get; set; }
    public string ActivityDetails { get; set; }
    public string CreatedDate { get; set; }

    public List<UserActivityListVm> UserActivityListVms { get; set; }
}