﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class UserRoleRepositoryMocks
{
    public static Mock<IUserRoleRepository> CreateUserRoleRepository(List<UserRole> userRoles)
    {
        var userRoleRepository = new Mock<IUserRoleRepository>();

        userRoleRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userRoles);

        userRoleRepository.Setup(repo => repo.AddAsync(It.IsAny<UserRole>())).ReturnsAsync(
            (UserRole userRole) =>
            {
                userRole.Id = new Fixture().Create<int>();

                userRole.ReferenceId = new Fixture().Create<Guid>().ToString();

                userRoles.Add(userRole);

                return userRole;
            });

        return userRoleRepository;
    }


    public static Mock<IUserRoleRepository> UpdateUserRoleRepository(List<UserRole> userRoles)
    {
        var mockUserRoleRepository = new Mock<IUserRoleRepository>();

        mockUserRoleRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userRoles);

        mockUserRoleRepository.Setup(repo => repo.GetUserRoleById(It.IsAny<string>())).ReturnsAsync((string i) => userRoles.SingleOrDefault(x => x.ReferenceId == i));

        mockUserRoleRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserRole>())).ReturnsAsync((UserRole userRole) =>
        {
            var index = userRoles.FindIndex(item => item.ReferenceId == userRole.ReferenceId);

            userRoles[index] = userRole;

            return userRole;
        });

        return mockUserRoleRepository;
    }

    public static Mock<IUserRoleRepository> DeleteUserRoleRepository(List<UserRole> userRoles)
    {
        var mockUserRoleRepository = new Mock<IUserRoleRepository>();

        mockUserRoleRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userRoles);

        mockUserRoleRepository.Setup(repo => repo.GetUserRoleById(It.IsAny<string>())).ReturnsAsync((string i) => userRoles.SingleOrDefault(x => x.ReferenceId == i));

        mockUserRoleRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserRole>())).ReturnsAsync((UserRole userRole) =>
        {
            var index = userRoles.FindIndex(item => item.ReferenceId == userRole.ReferenceId);

            userRole.IsActive = false;

            userRoles[index] = userRole;

            return userRole;
        });

        return mockUserRoleRepository;
    }

    public static Mock<IUserRoleRepository> GetUserRoleRepository(List<UserRole> userRoles)
    {
        var userRoleRepository = new Mock<IUserRoleRepository>();

        userRoleRepository.Setup(repo => repo.ListAllUserRoles()).ReturnsAsync(userRoles);

        userRoleRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => userRoles.SingleOrDefault(x => x.ReferenceId == i));

        return userRoleRepository;
    }

    public static Mock<IUserRoleRepository> GetUserRoleNameUniqueRepository(List<UserRole> userRoles)
    {
        var userRoleNameUniqueRepository = new Mock<IUserRoleRepository>();

        userRoleNameUniqueRepository.Setup(repo => repo.IsUserRoleNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => userRoles.Exists(x => x.Role == i && x.ReferenceId == j));

        return userRoleNameUniqueRepository;
    }

    public static Mock<IUserRoleRepository> GetUserRoleEmptyRepository()
    {
        var mockUserRoleRepository = new Mock<IUserRoleRepository>();

        mockUserRoleRepository.Setup(repo => repo.ListAllUserRoles()).ReturnsAsync(new List<UserRole>());

        return mockUserRoleRepository;
    }

    public static Mock<IUserRoleRepository> GetPaginatedUserRoleRepository(List<UserRole> userRoles)
    {
        var mockUserRoleRepository = new Mock<IUserRoleRepository>();

        var queryableUserRoles = userRoles.BuildMock();

        mockUserRoleRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableUserRoles);

        return mockUserRoleRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateUserRoleEventRepository(List<UserActivity> userActivities)
    {
        var userRoleEventRepository = new Mock<IUserActivityRepository>();

        userRoleEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        userRoleEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return userRoleEventRepository;
    }
}