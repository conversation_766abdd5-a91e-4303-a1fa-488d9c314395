﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller
{
    public class StorageAppEMCSRDFStarControllerShould
    {
        [Fact]
        public void List_Returns_ViewResult()
        {
            // Arrange
            var controller = new StorageAppEMCSRDFStarController();

            // Act
            var result = controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
        }
    }
}
