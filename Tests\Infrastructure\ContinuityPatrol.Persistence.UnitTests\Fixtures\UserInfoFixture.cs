using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class UserInfoFixture : IDisposable
{
    public List<UserInfo> UserInfoPaginationList { get; set; }
    public List<UserInfo> UserInfoList { get; set; }
    public UserInfo UserInfoDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public UserInfoFixture()
    {
        var fixture = new Fixture();

        UserInfoList = fixture.Create<List<UserInfo>>();

        UserInfoPaginationList = fixture.CreateMany<UserInfo>(20).ToList();

        UserInfoDto = fixture.Create<UserInfo>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
