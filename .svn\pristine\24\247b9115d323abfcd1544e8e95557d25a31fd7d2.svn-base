using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FiaTemplate.Events.Update;

public class FiaTemplateUpdatedEventHandler : INotificationHandler<FiaTemplateUpdatedEvent>
{
    private readonly ILogger<FiaTemplateUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FiaTemplateUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<FiaTemplateUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(FiaTemplateUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} FiaTemplate",
            Entity = "FiaTemplate",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"FiaTemplate '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"FiaTemplate '{updatedEvent.Name}' updated successfully.");
    }
}