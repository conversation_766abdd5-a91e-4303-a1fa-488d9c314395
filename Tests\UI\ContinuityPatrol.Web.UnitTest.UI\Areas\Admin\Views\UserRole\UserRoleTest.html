<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>🧪 User Role Unit Tests</title>
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.19.4.css">
    <link rel="stylesheet" href="/css/color_pallete.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://code.jquery.com/qunit/qunit-2.19.4.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sinon@15.2.0/pkg/sinon.min.js"></script>
    <!-- The app's JS (original code) -->
    <!-- The QUnit test file -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/Common/common.js"></script>
    <script src="/js/Admin/UserManagement/User Role/userRole.js"></script>
    <script src="/js/Admin/UserManagement/User Role/UserRoleTest.js"></script>

</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture"></div>
    <!-- Minimal markup required for events and functions -->
    <div id="AdminCreate" data-create-permission="true"></div>
    <div id="AdminDelete" data-delete-permission="true"></div>
    <button class="btn-userrole-Create" id="btnCreateUserRole"></button>
    <button id="btnURSave"></button>
    <button id="confirmDeleteButton"></button>
    <input type="text" id="cpRoleName" />
    <input type="hidden" id="textDeleteId" />
    <span id="nameError"></span>
    <div id="colorTable">
        <input type="radio" id="red" name="color" />
        <label for="red"><span class="dynamicColor red" style="background-color: rgb(255,0,0)"></span></label>
        <input type="radio" id="green" name="color" />
        <label for="green"><span class="dynamicColor green" style="background-color: rgb(0,255,0)"></span></label>
    </div>
    <span class="dynamicColor"></span>
    <input type="hidden" id="textUpdateId" value="1">
    <div id="multiCollapseExample1"></div>
    <div id="multiCollapseExample1Color"></div>
    <table id="UserRoleList"></table>
    <div class="roleData"></div>
    <div id="deleteData"></div>
    <input type="hidden" id="textLogo" />
    <a class="edit-button" data-role="{}"></a>
    <a class="delete-button" data-user-id="1" data-user-role="Role1"></a>
    <input type="text" id="search-inp">
    <span id="totalListCount"></span>
    <a class="accessMGLink-button" data-user-id="1"></a>

    <!-- Modal for testing -->
    <div class="modal" id="CreateModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">User Role</h5>
                </div>
                <div class="modal-body">
                    <!-- Modal content for testing -->
                </div>
            </div>
        </div>
    </div>
</body>
</html>