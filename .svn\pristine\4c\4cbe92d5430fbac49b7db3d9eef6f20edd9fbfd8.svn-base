namespace ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Create;

public class CreateBulkImportOperationGroupCommandHandler : IRequestHandler<CreateBulkImportOperationGroupCommand,
    CreateBulkImportOperationGroupResponse>
{
    private readonly IBulkImportOperationGroupRepository _bulkImportOperationGroupRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateBulkImportOperationGroupCommandHandler(IMapper mapper,
        IBulkImportOperationGroupRepository bulkImportOperationGroupRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _bulkImportOperationGroupRepository = bulkImportOperationGroupRepository;
    }

    public async Task<CreateBulkImportOperationGroupResponse> Handle(CreateBulkImportOperationGroupCommand request,
        CancellationToken cancellationToken)
    {
        var bulkImportOperationGroup = _mapper.Map<Domain.Entities.BulkImportOperationGroup>(request);

        bulkImportOperationGroup = await _bulkImportOperationGroupRepository.AddAsync(bulkImportOperationGroup);

        var response = new CreateBulkImportOperationGroupResponse
        {
            // Message = Message.Create(nameof(Domain.Entities.BulkImportOperationGroup), bulkImportOperationGroup.Name),

            Id = bulkImportOperationGroup.ReferenceId
        };

        // await _publisher.Publish(new BulkImportOperationGroupCreatedEvent { Name = bulkImportOperationGroup.Name }, cancellationToken);

        return response;
    }
}