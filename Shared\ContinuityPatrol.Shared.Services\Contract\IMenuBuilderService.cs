using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Create;
using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MenuBuilderModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IMenuBuilderService
{
    Task<List<MenuBuilderListVm>> GetMenuBuilderList();
    Task<BaseResponse> CreateAsync(CreateMenuBuilderCommand createMenuBuilderCommand);
    Task<BaseResponse> UpdateAsync(UpdateMenuBuilderCommand updateMenuBuilderCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<MenuBuilderDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsMenuBuilderNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<MenuBuilderListVm>> GetPaginatedMenuBuilders(GetMenuBuilderPaginatedListQuery query);
    #endregion
}
