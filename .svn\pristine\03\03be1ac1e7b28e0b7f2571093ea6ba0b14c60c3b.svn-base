﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.InfraObjectModel.InfraObjectViewModel
@Html.AntiForgeryToken()
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/lib/jquery.steps/jquery.steps.css" rel="stylesheet" />
<link href="~/css/wizard.css" rel="stylesheet" />
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-infra-object"></i><span>InfraObject</span></h6>
            <form class="d-flex align-items-center">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input searchCheckbox" type="checkbox" value="name=" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input searchCheckbox" type="checkbox" value="businessservice=" id="BusinessService">
                                        <label class="form-check-label" for="  BusinessService">
                                            Operational Service
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input searchCheckbox" type="checkbox" value="businessfunction=" id="BusinessFunction">
                                        <label class="form-check-label" for=" BusinessFunction">
                                            Operational Function
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input searchCheckbox" type="checkbox" value="activity=" id="ActivityType">
                                        <label class="form-check-label" for="ActivityType">
                                            Activity Type
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input searchCheckbox" type="checkbox" value="replication=" id="ReplicationName">
                                        <label class="form-check-label" for="ReplicationName">
                                            Replication Category
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm me-1" data-bs-toggle="modal" id="infraObject-createbutton"
                        data-bs-target="#CreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
                @*  <div class="btn-group me-2">
                <button type="button" class="btn btn-primary btn-sm rounded-end dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="cp-down-arrow fs-7"></i>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#saveasModal">Save As</a></li>
                    <li><a class="dropdown-item" href="#">Clone</a></li>
                    <li><a class="dropdown-item" href="#">Test Connection</a></li>
                    <li><a class="dropdown-item" href="#">Refresh</a></li>
                </ul>
                </div> *@
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="InfraObjectList" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Name</th>
                        <th>Operational Service Name</th>
                        <th>Operational Function Name</th>
                        <th>Activity Type </th>
                        <th>Replication Category</th>
                        <th>Status</th>
                        <th class="Action-th">Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>
<div id="configurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<div id="configurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<!-- Create -->
<div class="modal fade wizard-sticky-header" id="CreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>
<!-- Delete -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="Delete" />
</div>
@* Relate Nodes Modal *@
<div class="modal fade" id="relateModalToggle" aria-hidden="true" aria-labelledby="relateModalToggle" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title" title="Related Nodes"><i class="cp-network"></i><span>Related Nodes</span></h6>
                <button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Sr.No.</th>
                                <th>Production Node</th>
                                <th>DR Node</th>
                                <th class="Action-th">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>#</td>
                                <td>
                                    <div class="form-group">
                                        <div class="input-group">
                                            <select id="selectPrNode" class="form-select-modal" aria-hidden="true" style="width:200px" aria-label="Default select example" data-placeholder="Select Production Node ">
                                                <option value="" disabled selected="selected">Select Production Node </option>
                                            </select>
                                        </div>
                                        @* <span asp-validation-for="PRNodeName" id="SelectPRNodeName-error"></span> *@
                                    </div>
                                </td>
                                <td>
                                    <div class="form-group">
                                        <div class="input-group">
                                            <select id="selectDrNode" class="form-select-modal" aria-hidden="true" style="width:200px" data-placeholder="Select DR Node">
                                                <option value="" disabled="" selected="">Select DR Node</option>
                                            </select>
                                        </div>
                                        @* <span asp-validation-for="DRNodeName" id="SelectDRNodeName-error"></span> *@
                                    </div>
                                </td>
                                <td class="Action-th"><i id="btnSaveNode" title="Add" role="button" class="cp-circle-plus fs-5 text-primary" cursorshover="true"></i></td>
                            </tr>
                        </tbody>
                        <tbody id="tbody1"></tbody>
                    </table>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" id="cancelNode">Cancel</button>
                <button class="btn btn-primary btn-sm" id="saveNode">Save</button>
            </div>
        </div>
    </div>
</div>

<!--Save As Modal -->
<div class="modal fade" id="saveasModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="saveasModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-save"></i><span>Save As</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div>
                    <div class="form-group w-50">
                        <div class="form-label">Clone Name</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-copy-option"></i></span>
                            <select data-placeholder="Select Clone Name"
                                    class="form-select-modal">
                                <option></option>
                                <option>Clone Name 1</option>
                                <option>Clone Name 2</option>
                            </select>
                        </div>
                    </div>
                    <div class="row row-cols-5">

                        <div class="form-group">
                            <div class="form-label">Server Name</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-server"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Server Name" />
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-label">Server Type</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-production-server-name"></i></span>
                                <select data-placeholder="Select Server Type"
                                        class="form-select-modal">
                                    <option></option>
                                    <option>Server Type 1</option>
                                    <option>Server Type 2</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-label">Site Name</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-web"></i></span>
                                <select data-placeholder="Select Site Name"
                                        class="form-select-modal">
                                    <option></option>
                                    <option>Site Name 1</option>
                                    <option>Site Name 2</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-label">IP Address</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-ip-address"></i></span>
                                <input type="text" class="form-control" placeholder="Enter IP Address" />
                            </div>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <div class="form-group">

                                <div class="form-label">Host Name</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-host-name"></i></span>
                                    <input type="text" class="form-control" placeholder="Enter Host Name" />
                                </div>
                            </div>
                            <div>
                                <i role="button" title="Add" class="cp-circle-plus fs-5 text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="height:300px;overflow:auto">
                    <table class="table table-hover" style="width:100% !important">
                        <thead class="position-sticky top-0 z-3">
                            <tr>
                                <th class="SrNo_th">Sr.No</th>
                                <th>Server Name</th>
                                <th>Server Type</th>
                                <th>Site Name</th>
                                <th>IP Address</th>
                                <th>Host Name</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>
                                    PR Server
                                </td>
                                <td class="text-truncate">
                                    DRDBServer
                                </td>
                                <td>
                                    OS_DR_Site
                                </td>
                                <td>***********</td>
                                <td>PTS</td>
                                <td><span class="text-success"><i class="cp-success"></i></span></td>
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <span role="button" title="Edit" class="edit-button">
                                            <i class="cp-edit"></i>
                                        </span>
                                        <span role="button" title="Delete" class="delete-button" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                            <i class="cp-Delete"></i>
                                        </span>

                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>
                                    DR Server
                                </td>
                                <td class="text-truncate">
                                    DRDBServer
                                </td>
                                <td>
                                    OS_DR_Site
                                </td>
                                <td>10.12.20.11</td>
                                <td>PTS</td>
                                <td><span class="text-danger"><i class="cp-error"></i></span></td>
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <span role="button" title="Edit" class="edit-button">
                                            <i class="cp-edit"></i>
                                        </span>
                                        <span role="button" title="Delete" class="delete-button" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                            <i class="cp-Delete"></i>
                                        </span>

                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <div class="d-flex align-items-center gap-3">
                    <span class="fw-semibold fs-7">Total<span class="mx-2 py-1 px-2 rounded-circle bg-primary-subtle text-primary">2</span></span>
                    <span class="fw-semibold fs-7">Completed<span class="mx-2 py-1 px-2 rounded-circle bg-success-subtle text-success">1</span></span>
                    <span class="fw-semibold fs-7">Error<span class="mx-2 py-1 px-2 rounded-circle bg-danger-subtle text-danger">1</span></span>
                </div>
                <div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary">Save All</button>
                </div>

            </div>
        </div>
    </div>
</div>


<!-- Modal Create Custom Type-->
<div class="modal fade" id="NewModel" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form class="modal-content" id="createModel" method="post">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-mapping"></i><span>Node Mapping</span></h6>
                <button type="button" title="Close" class="btn-close" id='Modelcls' data-bs-dismiss="modal" data-bs-toggle="modal"></button>
            </div>
            <div class="modal-body" style="max-height:250px;overflow:auto">

                <table class="table table-hover" style="table-layout:fixed">
                    <thead class="position-sticky top-0 z-3">
                        <tr>
                            <th>Primary</th>
                            <th>DR</th>
                        </tr>
                    </thead>
                    <tbody id="addUserApproval">
                        @* <tr>
                            <td>Primary 1</td>
                            <td>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-city-name"></i>
                                    </span>
                                    <select class="form-select-modal" data-placeholder="Select Operational Function">
                                    </select>
                                </div>

                            </td>
                        </tr>  *@
                    </tbody>
                </table>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary ModalFooter-Note-Text"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" id="Modelcancel" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-sm btn-primary" id="ModelSave">Save</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script src="~/js/common/wizard.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Configuration/InfraObject/InfraObject.js"></script>