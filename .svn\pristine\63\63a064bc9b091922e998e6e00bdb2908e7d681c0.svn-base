using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class BulkImportOperationRepository : BaseRepository<BulkImportOperation>, IBulkImportOperationRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;
    private static readonly string[] RunningStatuses = { "running", "pending" };

    public BulkImportOperationRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<BulkImportOperation>> GetDescriptionBulkImportStartAndEndTime(string startDate, string endDate)
    {
        var start = startDate.ToDateTime();
        var end = endDate.ToDateTime();

        var query = _dbContext.BulkImportOperations
            .AsNoTracking()
            .Active()
            .Where(x => x.CreatedDate.Date >= start && x.CreatedDate.Date <= end);

        if (!IsParent)
        {
            query = query.Where(x => x.CompanyId == _loggedInUserService.CompanyId);
        }

        return await query.Select(x => new BulkImportOperation
        {
            ReferenceId = x.ReferenceId,
            Description = x.Description,
            StartTime = x.StartTime
        }).ToListAsync();

    }

    public async Task<bool> GetOperationRunningByInfraObjectName(string infraObjectName)
    {
        return await _dbContext.BulkImportOperations
            .AsNoTracking()
            .AnyAsync(x => x.InfraObjectName == infraObjectName && RunningStatuses.Contains(x.Status.Trim().ToLower())); 
    }

    public async Task<List<BulkImportOperation>> GetRunningStatus()
    { 
        return await _dbContext.BulkImportOperations
            .AsNoTracking()
            .Active()
            .Where(x => RunningStatuses.Contains(x.Status.Trim().ToLower()))
            .Select(x=> new BulkImportOperation
            {
                Id=x.Id,
                ReferenceId=x.ReferenceId,
                CompanyId=x.CompanyId,
                UserName=x.UserName,
                InfraObjectName=x.InfraObjectName,
                Description=x.Description,
                Status=x.Status,
                StartTime=x.StartTime,
                EndTime=x.EndTime
            })
            .ToListAsync();
    }
}