using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.DriftParameterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DriftParameter.Queries.GetPaginatedList;

public class GetDriftParameterPaginatedListQueryHandler : IRequestHandler<GetDriftParameterPaginatedListQuery,
    PaginatedResult<DriftParameterListVm>>
{
    private readonly IDriftParameterRepository _driftParameterRepository;
    private readonly IMapper _mapper;

    public GetDriftParameterPaginatedListQueryHandler(IMapper mapper,
        IDriftParameterRepository driftParameterRepository)
    {
        _mapper = mapper;
        _driftParameterRepository = driftParameterRepository;
    }

    public async Task<PaginatedResult<DriftParameterListVm>> Handle(GetDriftParameterPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DriftParameterFilterSpecification(request.SearchString);

        var queryable =await  _driftParameterRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var driftParameterList = _mapper.Map<PaginatedResult<DriftParameterListVm>>(queryable);

        return driftParameterList;
        //var queryable = _driftParameterRepository.GetPaginatedQuery();

        //var productFilterSpec = new DriftParameterFilterSpecification(request.SearchString);

        //var driftParameterList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<DriftParameterListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return driftParameterList;
    }
}