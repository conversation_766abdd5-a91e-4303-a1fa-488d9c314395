using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.AdPasswordExpireModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetPaginatedList;

public class GetAdPasswordExpirePaginatedListQueryHandler : IRequestHandler<GetAdPasswordExpirePaginatedListQuery, PaginatedResult<AdPasswordExpireListVm>>
{
    private readonly IAdPasswordExpireRepository _adPasswordExpireRepository;
    private readonly IMapper _mapper;

    public GetAdPasswordExpirePaginatedListQueryHandler(IMapper mapper, IAdPasswordExpireRepository adPasswordExpireRepository)
    {
        _mapper = mapper;
        _adPasswordExpireRepository = adPasswordExpireRepository;
    }

    public async Task<PaginatedResult<AdPasswordExpireListVm>> Handle(GetAdPasswordExpirePaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new AdPasswordExpireFilterSpecification(request.SearchString);

        var queryable =await _adPasswordExpireRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var adPasswordExpireList= _mapper.Map<PaginatedResult<AdPasswordExpireListVm>>(queryable);

        return adPasswordExpireList;
        //var queryable = _adPasswordExpireRepository.GetPaginatedQuery();

        //var productFilterSpec = new AdPasswordExpireFilterSpecification(request.SearchString);

        //var adPasswordExpireList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<AdPasswordExpireListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return adPasswordExpireList;
    }
}
