using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftCategoryList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftOperationSummary;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetConflictList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetDriftTreeView;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftManagementMonitorStatusModel;
using ContinuityPatrol.Domain.ViewModels.DriftResourceSummaryModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IDriftManagementMonitorStatusService
{
    Task<List<DriftManagementMonitorStatusListVm>> GetDriftManagementMonitorStatusList();
    Task<BaseResponse> CreateAsync(CreateDriftManagementMonitorStatusCommand createDriftManagementMonitorStatusCommand);
    Task<BaseResponse> UpdateAsync(UpdateDriftManagementMonitorStatusCommand updateDriftManagementMonitorStatusCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<DriftManagementMonitorStatusDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsDriftManagementMonitorStatusNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<DriftManagementMonitorStatusListVm>> GetPaginatedDriftManagementMonitorStatuss(GetDriftManagementMonitorStatusPaginatedListQuery query);
    #endregion

    Task<List<DriftManagementMonitorStatusListVm>> GetDriftManagementStatusByInfraObjectId(string infraObjectId);
    Task<List<GetDriftTreeListVm>> GetDriftTreeList();
    Task<DriftOperationSummaryVm> GetDriftOperationSummary();
    Task<List<DriftResourceSummaryListVm>> GetDriftResourceSummary();
    Task<DriftCategoryListVm> GetDriftCategory();
    Task<DriftDashboardResourceStatusVm> GetDriftDashboardResourceStatus();
    Task<List<ConflictListVm>> GetConflictOverView();
}
