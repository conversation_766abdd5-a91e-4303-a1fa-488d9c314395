﻿@model ContinuityPatrol.Domain.ViewModels.BusinessServiceModel.BusinessServiceViewModel
@using ContinuityPatrol.Shared.Services.Helper;
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title" ><i class="cp-business-service"></i><span>Operational Service </span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown"  title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="companyname=" id="CompanyName">
                                        <label class="form-check-label" for="CompanyName">
                                            Company Name
                                        </label>
                                    </div>
                                </li>                              
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm" id="business-service-createbutton" data-bs-toggle="modal"
                        data-bs-target="#CreateModal" >
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="tblBusinessService" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th >Name</th>
                        <th >Company Name</th>
                        <th>Critical Level</th>
                        <th class="Action-th " >Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>
<div id="configurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<div id="configurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<!--model create -->
<div class="modal fade" id="CreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="configureModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>
<!--Modal Delete-->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="Delete" />
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Configuration/Operational Service/BusinessService.js"></script>
