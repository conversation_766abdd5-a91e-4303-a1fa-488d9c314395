﻿using ContinuityPatrol.Application.Features.AlertInformation.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertInformation.Queries;

public class GetAlertInformationDetailQueryHandlerTests : IClassFixture<AlertInformationFixture>
{
    private readonly AlertInformationFixture _alertInformationFixture;
    private readonly Mock<IAlertInformationRepository> _mockAlertInformationRepository;
    private readonly GetAlertInformationDetailQueryHandler _handler;

    public GetAlertInformationDetailQueryHandlerTests(AlertInformationFixture alertInformationFixture)
    {
        _alertInformationFixture = alertInformationFixture;

        _mockAlertInformationRepository = AlertInformationRepositoryMocks.GetAlertInformationRepository(_alertInformationFixture.AlertInformations);

        _handler = new GetAlertInformationDetailQueryHandler(_mockAlertInformationRepository.Object, _alertInformationFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_AlertInformationDetails_When_ValidAlertInformationId()
    {
        var result = await _handler.Handle(new GetAlertInformationDetailQuery { Id = _alertInformationFixture.AlertInformations[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<AlertInformationDetailVm>();

        result.Id.ShouldBe(_alertInformationFixture.AlertInformations[0].ReferenceId);
        result.AlertFrequency.ShouldBe(_alertInformationFixture.AlertInformations[0].AlertFrequency);
        result.Code.ShouldBe(_alertInformationFixture.AlertInformations[0].Code);
        result.Severity.ShouldBe(_alertInformationFixture.AlertInformations[0].Severity);
        result.Type.ShouldBe(_alertInformationFixture.AlertInformations[0].Type);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidAlertInformationId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetAlertInformationDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("Not Found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetAlertInformationDetailQuery { Id = _alertInformationFixture.AlertInformations[0].ReferenceId }, CancellationToken.None);

        _mockAlertInformationRepository.Verify(x=>x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}