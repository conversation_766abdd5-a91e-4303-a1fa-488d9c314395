﻿using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Create;
using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Update;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetAvailableCount;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetDetailView;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ILicenseInfoService
{
    Task<BaseResponse> CreateAsync(CreateLicenseInfoCommand createLicenseInfoCommand);
    Task<BaseResponse> UpdateAsync(UpdateLicenseInfoCommand updateLicenseInfoCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<PaginatedResult<LicenseInfoListVm>> GetPaginatedLicenseInfo(GetLicenseInfoPaginatedListQuery query);
    Task<List<LicenseInfoListVm>> GetLicenseInfo();
    Task<List<LicenseInfoDetailVm>> GetLicenseInfoByLicenseId(string id);
    Task<LicenseInfoDetailViewVm> GetLicenseInfoById(string licenseId);
    Task<AvailableCountVm> GetAvailableCountByLicenseId(string licenseId);
    Task<List<LicenseInfoByBusinessServiceIdListVm>> GetLicenseByBusinessServiceId(string businessServiceId);
    Task<List<LicenseInfoByEntityListVm>> GetLicenseInfoByEntity(string licenseId, string entity,string entityType);
    Task<List<LicenseInfoTypeListVm>> GetLicenseInfoByType(string licenseId, string type, string entityType);
}