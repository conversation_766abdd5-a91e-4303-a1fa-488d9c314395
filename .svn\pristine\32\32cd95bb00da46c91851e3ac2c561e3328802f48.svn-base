﻿using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;

namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetList;

public class
    GetWorkflowOperationGroupListQueryHandler : IRequestHandler<GetWorkflowOperationGroupListQuery,
        List<WorkflowOperationGroupListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;

    public GetWorkflowOperationGroupListQueryHandler(IMapper mapper,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository)
    {
        _mapper = mapper;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
    }

    public async Task<List<WorkflowOperationGroupListVm>> Handle(GetWorkflowOperationGroupListQuery request,
        CancellationToken cancellationToken)
    {
        var workflowOperationGroup = (await _workflowOperationGroupRepository.ListAllAsync()).ToList();

        return workflowOperationGroup.Count == 0
            ? new List<WorkflowOperationGroupListVm>()
            : _mapper.Map<List<WorkflowOperationGroupListVm>>(workflowOperationGroup);
    }
}