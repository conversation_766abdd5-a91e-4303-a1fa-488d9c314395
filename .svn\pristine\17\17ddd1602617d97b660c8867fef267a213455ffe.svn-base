﻿using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSummaryReport;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Queries
{
    public class GetInfraObjectSummaryReportQueryHandlerTests
    {
        private readonly Mock<IDashboardViewRepository> _mockDashboardViewRepository;
        private readonly Mock<IInfraObjectRepository> _mockInfraObjectRepository;
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly GetInfraObjectSummaryReportQueryHandler _handler;

        public GetInfraObjectSummaryReportQueryHandlerTests()
        {
            _mockDashboardViewRepository = new Mock<IDashboardViewRepository>();
            _mockInfraObjectRepository = new Mock<IInfraObjectRepository>();
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();
            _mockPublisher = new Mock<IPublisher>();

            _handler = new GetInfraObjectSummaryReportQueryHandler(
                _mockDashboardViewRepository.Object,
                _mockInfraObjectRepository.Object,
                _mockLoggedInUserService.Object,
                _mockPublisher.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldReturnReport_WhenBusinessServiceIdIsNull()
        {
            var request = new GetInfraObjectSummaryReportQuery
            {
                BusinessServiceId = null
            };

            var infraObjectReports = new List<InfraObjectSummaryReportVm>
            {
                new InfraObjectSummaryReportVm
                {
                    SrNo = 123,
                    InfraObjectName = "TestInfra",
                    ReplicationType = "mysql",
                    DataLag = "Test"
                }
            };

            _mockDashboardViewRepository
                .Setup(repo => repo.GetInfraObjectSummaryReport())
                .Returns(ToString);

            _mockInfraObjectRepository
                .Setup(repo => repo.GetByReferenceIdAsync("123"))
                .ReturnsAsync(new Domain.Entities.InfraObject
                {
                    ReplicationCategoryType = "TestReplication"
                });

            _mockLoggedInUserService
                .Setup(service => service.LoginName)
                .Returns("TestUser");

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("TestUser", result.ReportGeneratedBy);
            Assert.NotEmpty(result.InfraObjectSummaryReportVms);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<ReportViewedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnReport_WhenBusinessServiceIdIsProvided()
        {
            var request = new GetInfraObjectSummaryReportQuery
            {
                BusinessServiceId = "456"
            };

            var infraObjectReports = new List<InfraObjectSummaryReportVm>
            {
                new InfraObjectSummaryReportVm
                {
                    SrNo = 789,
                    InfraObjectName = "TestInfra2",
                    ReplicationType = "oracle",
                    DRHealth = "Demo"
                }
            };

            _mockDashboardViewRepository
                .Setup(repo => repo.GetBusinessViewListByBusinessServiceId("456"))
                .Returns(ToString);

            _mockInfraObjectRepository
                .Setup(repo => repo.GetByReferenceIdAsync("789"))
                .ReturnsAsync(new Domain.Entities.InfraObject
                {
                    ReplicationCategoryType = "ReplicationType2"
                });

            _mockLoggedInUserService
                .Setup(service => service.LoginName)
                .Returns("TestUser2");

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("TestUser2", result.ReportGeneratedBy);
            Assert.NotEmpty(result.InfraObjectSummaryReportVms);
            var report = result.InfraObjectSummaryReportVms[0];
            Assert.Equal("TestInfra2", report.InfraObjectName);
            Assert.Equal("123", report.LastArchiveLogAppliedDR);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<ReportViewedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnNA_WhenPropertiesAreInvalid()
        {
            var request = new GetInfraObjectSummaryReportQuery
            {
                BusinessServiceId = null
            };

            var infraObjectReports = new List<InfraObjectSummaryReportVm>
            {
                new InfraObjectSummaryReportVm
                {
                    SrNo = 123,
                    InfraObjectName = "TestInfra",
                    ReplicationType = "oracle",
                    ConfiguredRpo = "InvalidJson"
                }
            };

            _mockDashboardViewRepository
                .Setup(repo => repo.GetInfraObjectSummaryReport())
                .Returns(ToString);

            _mockInfraObjectRepository
                .Setup(repo => repo.GetByReferenceIdAsync("123"))
                .ReturnsAsync(new Domain.Entities.InfraObject
                {
                    ReplicationCategoryType = "TestReplication"
                });

            _mockLoggedInUserService
                .Setup(service => service.LoginName)
                .Returns("TestUser");

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("TestUser", result.ReportGeneratedBy);
            Assert.NotEmpty(result.InfraObjectSummaryReportVms);
            Assert.Equal("NA", result.InfraObjectSummaryReportVms[0].LastArchiveLogAppliedDR);
        }
    }
}
