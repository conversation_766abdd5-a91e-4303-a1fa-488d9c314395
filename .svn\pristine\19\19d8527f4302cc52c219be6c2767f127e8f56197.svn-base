﻿using ContinuityPatrol.Application.Features.IncidentDaily.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentDaily.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentDaily.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.IncidentDailyModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IIncidentDailyService
{
    Task<List<IncidentDailyListVm>> GetList();
    Task<IncidentDailyDetailVm> GetIncidentDailyById(string id);
    Task<BaseResponse> CreateIncidentDaily(CreateIncidentDailyCommand createIncidentDailyCommand);
    Task<BaseResponse> UpdateIncidentLogs(UpdateIncidentDailyCommand updateIncidentDailyCommand);
    Task<BaseResponse> DeleteIncidentDaily(string id);
    Task<PaginatedResult<IncidentDailyListVm>> GetPaginatedIncidentDaily(GetIncidentDailyPaginatedQuery query);
    
}
