using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SolutionTypeTablesRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly SolutionTypeTablesRepository _repository;
    private readonly SolutionTypeTablesFixture _fixture;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public SolutionTypeTablesRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        
        _repository = new SolutionTypeTablesRepository(_dbContext, _mockLoggedInUserService.Object);
        _fixture = new SolutionTypeTablesFixture();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
    }

    #region GetBySolutionType Tests

    [Fact]
    public async Task GetBySolutionType_ShouldReturnMatchingSolutionTypes_WhenExactMatch()
    {
        // Arrange
        await ClearDatabase();

        var solutionTable1 = _fixture.CreateSolutionTypeTables(
            monitoringType: "Database",
            solutionType: "SQL Server",
            monitorTableNames: "SqlServerMonitor",
            isActive: true
        );
        var solutionTable2 = _fixture.CreateSolutionTypeTables(
            monitoringType: "Database",
            solutionType: "MySQL",
            monitorTableNames: "MySqlMonitor",
            isActive: true
        );
        var solutionTable3 = _fixture.CreateSolutionTypeTables(
            monitoringType: "Application",
            solutionType: "Web Server",
            monitorTableNames: "WebServerMonitor",
            isActive: true
        );

        await _repository.AddAsync(solutionTable1);
        await _repository.AddAsync(solutionTable2);
        await _repository.AddAsync(solutionTable3);

        // Act
        var result = await _repository.GetBySolutionType("sql server");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("SQL Server", result[0].SolutionType);
        Assert.Equal("Database", result[0].MonitoringType);
        Assert.Equal("SqlServerMonitor", result[0].MonitorTableNames);
    }

    [Fact]
    public async Task GetBySolutionType_ShouldReturnMatchingSolutionTypes_WhenPartialMatch()
    {
        // Arrange
        await ClearDatabase();

        var solutionTable1 = _fixture.CreateSolutionTypeTables(
            monitoringType: "Database",
            solutionType: "SQL Server 2019",
            monitorTableNames: "SqlServer2019Monitor",
            isActive: true
        );
        var solutionTable2 = _fixture.CreateSolutionTypeTables(
            monitoringType: "Database",
            solutionType: "SQL Server 2022",
            monitorTableNames: "SqlServer2022Monitor",
            isActive: true
        );
        var solutionTable3 = _fixture.CreateSolutionTypeTables(
            monitoringType: "Database",
            solutionType: "MySQL Server",
            monitorTableNames: "MySqlServerMonitor",
            isActive: true
        );
        var solutionTable4 = _fixture.CreateSolutionTypeTables(
            monitoringType: "Application",
            solutionType: "Oracle Database",
            monitorTableNames: "OracleMonitor",
            isActive: true
        );

        await _dbContext.SolutionTypeTables.AddRangeAsync(solutionTable1, solutionTable2, solutionTable3, solutionTable4);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetBySolutionType("sql server");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);

    }

    [Fact]
    public async Task GetBySolutionType_ShouldBeCaseInsensitive()
    {
        // Arrange
        await ClearDatabase();

        var solutionTable1 = _fixture.CreateSolutionTypeTables(
            solutionType: "SQL Server",
            isActive: true
        );
        var solutionTable2 = _fixture.CreateSolutionTypeTables(
            solutionType: "mysql server",
            isActive: true
        );
        var solutionTable3 = _fixture.CreateSolutionTypeTables(
            solutionType: "Oracle DATABASE",
            isActive: true
        );
        await _dbContext.SolutionTypeTables.AddRangeAsync(solutionTable1, solutionTable2, solutionTable3);
        _dbContext.SaveChanges();

        // Act - Test various case combinations
        var result1 = await _repository.GetBySolutionType("SQL");
        var result2 = await _repository.GetBySolutionType("sql");
        var result3 = await _repository.GetBySolutionType("SERVER");
        var result4 = await _repository.GetBySolutionType("server");
        var result5 = await _repository.GetBySolutionType("DATABASE");
        var result6 = await _repository.GetBySolutionType("database");

        // Assert
        Assert.NotNull(result1);
        Assert.Equal(2, result1.Count); // SQL Server and mysql server
        
        Assert.NotNull(result2);
        Assert.Equal(2, result2.Count); // SQL Server and mysql server
        
        Assert.NotNull(result3);
        Assert.Equal(2, result3.Count); // SQL Server and mysql server
        
        Assert.NotNull(result4);
        Assert.Equal(2, result4.Count); // SQL Server and mysql server
        
        Assert.NotNull(result5);
        Assert.Single(result5); // Oracle DATABASE
        
        Assert.NotNull(result6);
        Assert.Single(result6); // Oracle DATABASE
    }

    [Fact]
    public async Task GetBySolutionType_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();

        var solutionTable1 = _fixture.CreateSolutionTypeTables(
            solutionType: "SQL Server",
            isActive: true
        );
        var solutionTable2 = _fixture.CreateSolutionTypeTables(
            solutionType: "MySQL",
            isActive: true
        );

        await _repository.AddAsync(solutionTable1);
        await _repository.AddAsync(solutionTable2);

        // Act
        var result = await _repository.GetBySolutionType("oracle");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetBySolutionType_ShouldReturnEmpty_WhenSearchStringIsEmpty()
    {
        // Arrange
        await ClearDatabase();

        var solutionTable = _fixture.CreateSolutionTypeTables(
            solutionType: "SQL Server",
            isActive: true
        );

        await _repository.AddAsync(solutionTable);

        // Act
        var result = await _repository.GetBySolutionType("");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Empty string should match all (contains empty string)
    }

    [Fact]
    public async Task GetBySolutionType_ShouldOnlyReturnActiveSolutionTypes()
    {
        // Arrange
        await ClearDatabase();

        var activeSolutionTable = _fixture.CreateSolutionTypeTables(
            solutionType: "SQL Server Active",
            isActive: true
        );
        var inactiveSolutionTable = _fixture.CreateSolutionTypeTables(
            solutionType: "SQL Server Inactive",
            isActive: false
        );

        await _dbContext.SolutionTypeTables.AddRangeAsync(activeSolutionTable, inactiveSolutionTable);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetBySolutionType("sql server");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("SQL Server Active", result[0].SolutionType);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetBySolutionType_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();

        var solutionTable1 = _fixture.CreateSolutionTypeTables(
            solutionType: "SQL Server 2019 (Express)",
            isActive: true
        );
        var solutionTable2 = _fixture.CreateSolutionTypeTables(
            solutionType: "MySQL 8.0",
            isActive: true
        );
        var solutionTable3 = _fixture.CreateSolutionTypeTables(
            solutionType: "Oracle DB-19c",
            isActive: true
        );

        await _repository.AddAsync(solutionTable1);
        await _repository.AddAsync(solutionTable2);
        await _repository.AddAsync(solutionTable3);

        // Act
        var result1 = await _repository.GetBySolutionType("2019");
        var result2 = await _repository.GetBySolutionType("8.0");
        var result3 = await _repository.GetBySolutionType("db-19c");

        // Assert
        Assert.NotNull(result1);
        Assert.Single(result1);
        Assert.Equal("SQL Server 2019 (Express)", result1[0].SolutionType);

        Assert.NotNull(result2);
        Assert.Single(result2);
        Assert.Equal("MySQL 8.0", result2[0].SolutionType);

        Assert.NotNull(result3);
        Assert.Single(result3);
        Assert.Equal("Oracle DB-19c", result3[0].SolutionType);
    }

    [Fact]
    public async Task GetBySolutionType_ShouldReturnAllProperties()
    {
        // Arrange
        await ClearDatabase();

        var solutionTable = _fixture.CreateSolutionTypeTables(
            monitoringType: "Database Monitoring",
            solutionType: "SQL Server Enterprise",
            monitorTableNames: "SqlServerEnterpriseMonitor,SqlServerPerformanceMonitor",
            isActive: true
        );

        await _repository.AddAsync(solutionTable);

        // Act
        var result = await _repository.GetBySolutionType("sql server");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var resultItem = result[0];
        
        Assert.Equal("Database Monitoring", resultItem.MonitoringType);
        Assert.Equal("SQL Server Enterprise", resultItem.SolutionType);
        Assert.Equal("SqlServerEnterpriseMonitor,SqlServerPerformanceMonitor", resultItem.MonitorTableNames);
        Assert.True(resultItem.IsActive);
        Assert.NotNull(resultItem.ReferenceId);
        Assert.True(resultItem.Id > 0);
    }

    [Fact]
    public async Task GetBySolutionType_ShouldHandleNullSolutionType()
    {
        // Arrange
        await ClearDatabase();

        var solutionTable1 = _fixture.CreateSolutionTypeTables(
            solutionType: "SQL Server",
            isActive: true
        );
        var solutionTable2 = _fixture.CreateSolutionTypeTables(
            solutionType: null, // Null solution type
            isActive: true
        );

        await _repository.AddAsync(solutionTable1);
        await _repository.AddAsync(solutionTable2);

        // Act
        var result = await _repository.GetBySolutionType("sql");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Should only return the non-null one
        Assert.Equal("SQL Server", result[0].SolutionType);
    }

    [Fact]
    public async Task GetBySolutionType_ShouldReturnMultipleMatchesOrderedCorrectly()
    {
        // Arrange
        await ClearDatabase();

        var solutionTable1 = _fixture.CreateSolutionTypeTables(
            solutionType: "SQL Server 2016",
            monitoringType: "Database",
            isActive: true
        );
        var solutionTable2 = _fixture.CreateSolutionTypeTables(
            solutionType: "SQL Server 2019",
            monitoringType: "Database",
            isActive: true
        );
        var solutionTable3 = _fixture.CreateSolutionTypeTables(
            solutionType: "SQL Server 2022",
            monitoringType: "Database",
            isActive: true
        );

        await _repository.AddAsync(solutionTable1);
        await _repository.AddAsync(solutionTable2);
        await _repository.AddAsync(solutionTable3);

        // Act
        var result = await _repository.GetBySolutionType("sql server");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, s => Assert.Contains("SQL Server", s.SolutionType));
        Assert.All(result, s => Assert.Equal("Database", s.MonitoringType));
        Assert.All(result, s => Assert.True(s.IsActive));
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.SolutionTypeTables.RemoveRange(_dbContext.SolutionTypeTables);
        await _dbContext.SaveChangesAsync();
    }
}
