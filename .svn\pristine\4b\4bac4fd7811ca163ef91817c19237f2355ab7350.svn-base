﻿using ContinuityPatrol.Application.Features.Company.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;

namespace ContinuityPatrol.Application.UnitTests.Features.Company.Queries;

public class GetCompanyListQueryHandlerTests : IClassFixture<CompanyFixture>
{
    private readonly CompanyFixture _companyFixture;
    private Mock<ICompanyRepository> _mockCompanyRepository;
    private readonly GetCompanyListQueryHandler _handler;

    public GetCompanyListQueryHandlerTests(CompanyFixture companyFixture)
    {
        _companyFixture = companyFixture;

        _mockCompanyRepository = CompanyRepositoryMocks.GetCompanyRepository(_companyFixture.Companies);

        _handler = new GetCompanyListQueryHandler(_companyFixture.Mapper, _mockCompanyRepository.Object);

        _companyFixture.Companies[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
        _companyFixture.Companies[1].ParentId = _companyFixture.Companies[0].ReferenceId;
    }

    [Fact]
    public async Task Handle_Return_ParentCompanies_ParentName_ShouldBeNA()
    {
        var result = await _handler.Handle(new GetCompanyListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<CompanyListVm>>();

        var parentCompany = result.FirstOrDefault(x => x.IsParent);

        if (parentCompany != null)
        {
            parentCompany.ParentName.ShouldBe("NA");

            parentCompany.ParentId.ShouldBe(0.ToString());
        }
    }

    [Fact]
    public async Task Handle_Return_ChildCompanies_ParentName_ShouldNotEmptyOrNA()
    {
        var result = await _handler.Handle(new GetCompanyListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<CompanyListVm>>();

        var childCompany = result.FirstOrDefault(x => x.IsParent == false);

        if (childCompany != null)
        {
            childCompany.ParentName.ShouldNotBeNullOrEmpty();

            childCompany.ParentId.ShouldBeGreaterThan(0.ToString());
        }
    }

    [Fact]
    public async Task Handle_Return_Valid_CompaniesDetail()
    {
        var result = await _handler.Handle(new GetCompanyListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<CompanyListVm>>();

        result[0].Id.ShouldBe(_companyFixture.Companies[0].ReferenceId);
        result[0].Name.ShouldBe(_companyFixture.Companies[0].Name);
        result[0].DisplayName.ShouldBe(_companyFixture.Companies[0].DisplayName);
        result[0].WebAddress.ShouldBe(_companyFixture.Companies[0].WebAddress);
        result[0].CompanyLogo.ShouldBe(_companyFixture.Companies[0].CompanyLogo);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockCompanyRepository = CompanyRepositoryMocks.GetCompanyEmptyRepository();
        var handler = new GetCompanyListQueryHandler(_companyFixture.Mapper, _mockCompanyRepository.Object);
        var result = await handler.Handle(new GetCompanyListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetCompanyListQuery(), CancellationToken.None);
        _mockCompanyRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}