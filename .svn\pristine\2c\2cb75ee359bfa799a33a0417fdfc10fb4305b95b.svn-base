using ContinuityPatrol.Application.Features.PageWidget.Commands.Create;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Update;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageWidgetModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class PageWidgetService : BaseClient, IPageWidgetService
{
    public PageWidgetService(IConfiguration config, IAppCache cache, ILogger<PageWidgetService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<PageWidgetListVm>> GetPageWidgetList()
    {
        var request = new RestRequest("api/v6/pagewidgets");

        return await GetFromCache<List<PageWidgetListVm>>(request, "GetPageWidgetList");
    }

    public async Task<BaseResponse> CreateAsync(CreatePageWidgetCommand createPageWidgetCommand)
    {
        var request = new RestRequest("api/v6/pagewidgets", Method.Post);

        request.AddJsonBody(createPageWidgetCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdatePageWidgetCommand updatePageWidgetCommand)
    {
        var request = new RestRequest("api/v6/pagewidgets", Method.Put);

        request.AddJsonBody(updatePageWidgetCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/pagewidgets/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<PageWidgetDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/pagewidgets/{id}");

        return await Get<PageWidgetDetailVm>(request);
    }

    #region NameExist
    public async Task<bool> IsPageWidgetNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/pagewidgets/name-exist?pagewidgetName={name}&id={id}");

        return await Get<bool>(request);
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<PageWidgetListVm>> GetPaginatedPageWidgets(GetPageWidgetPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/pagewidgets/paginated-list");

        return await Get<PaginatedResult<PageWidgetListVm>>(request);
    }
    #endregion
}
