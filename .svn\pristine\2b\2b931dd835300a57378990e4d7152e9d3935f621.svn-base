﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.UserGroupModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.UserGroup.Queries.GetPaginatedList;

public class GetUserGroupPaginatedListQueryHandler : IRequestHandler<GetUserGroupPaginatedListQuery,
    PaginatedResult<UserGroupListVm>>
{
    private readonly IMapper _mapper;
    private readonly IUserGroupRepository _userGroupRepository;

    public GetUserGroupPaginatedListQueryHandler(IMapper mapper, IUserGroupRepository userGroupRepository)
    {
        _mapper = mapper;
        _userGroupRepository = userGroupRepository;
    }

    public async Task<PaginatedResult<UserGroupListVm>> Handle(GetUserGroupPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var specification = new UserGroupSpecification(request.SearchString);

        var queryable = await _userGroupRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize, specification, request.SortColumn, request.SortOrder);

        var userGroupList = _mapper.Map<PaginatedResult<UserGroupListVm>>(queryable);

        return userGroupList;
    }
}