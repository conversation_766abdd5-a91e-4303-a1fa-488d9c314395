﻿using ContinuityPatrol.Application.Features.TeamMaster.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamMaster.Events
{
    public class PaginatedTeamMasterEventTests
    {
        private readonly Mock<ILogger<TeamMasterPaginatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly TeamMasterPaginatedEventHandler _handler;

        public PaginatedTeamMasterEventTests()
        {
            _mockLogger = new Mock<ILogger<TeamMasterPaginatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new TeamMasterPaginatedEventHandler(
                _mockUserService.Object,
                _mockUserActivityRepository.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_AddsUserActivityAndLogsInformation_WhenCalled()
        {
            var paginatedEvent = new TeamMasterPaginatedEvent();
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns("user-id");
            _mockUserService.Setup(s => s.LoginName).Returns("user-login");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("/api/team-master");
            _mockUserService.Setup(s => s.CompanyId).Returns("company-id");
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            await _handler.Handle(paginatedEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "user-id" &&
                activity.LoginName == "user-login" &&
                activity.RequestUrl == "/api/team-master" &&
                activity.CompanyId == "company-id" &&
                activity.HostAddress == "***********" &&
                activity.Entity == Modules.TeamMaster.ToString() &&
                activity.Action == $"{ActivityType.View} {Modules.TeamMaster}" &&
                activity.ActivityType == ActivityType.View.ToString() &&
                activity.ActivityDetails == "Manage Team viewed"
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation("Manage Team viewed"), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenAddAsyncFails()
        {
            var paginatedEvent = new TeamMasterPaginatedEvent();
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns("user-id");
            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .ThrowsAsync(new System.Exception("Database error"));

            var exception = await Assert.ThrowsAsync<System.Exception>(() => _handler.Handle(paginatedEvent, cancellationToken));
            Assert.Equal("Database error", exception.Message);

            _mockLogger.Verify(logger => logger.LogInformation(It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task Handle_UsesDefaultValues_WhenUserIdIsNullOrEmpty()
        {
            var paginatedEvent = new TeamMasterPaginatedEvent();
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns((string)null);
            _mockUserService.Setup(s => s.LoginName).Returns("user-login");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("/api/team-master");
            _mockUserService.Setup(s => s.CompanyId).Returns("company-id");
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            await _handler.Handle(paginatedEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                !string.IsNullOrEmpty(activity.UserId)
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation("Manage Team viewed"), Times.Once);
        }
    }
}
