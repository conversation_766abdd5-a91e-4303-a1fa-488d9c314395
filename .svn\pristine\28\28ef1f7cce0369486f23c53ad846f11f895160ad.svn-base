﻿using ContinuityPatrol.Application.Features.ServerType.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ServerType.Queries;

public class GetServerTypeNameUniqueQueryHandlerTests : IClassFixture<ServerTypeFixture>
{
    private readonly ServerTypeFixture _serverTypeFixture;

    private Mock<IServerTypeRepository> _mockServerTypeRepository;

    private readonly GetServerTypeNameUniqueQueryHandler _handler;

    public GetServerTypeNameUniqueQueryHandlerTests(ServerTypeFixture serverTypeFixture)
    {
        _serverTypeFixture = serverTypeFixture;

        _mockServerTypeRepository = ServerTypeRepositoryMocks.GetServerTypeNameUniqueRepository(_serverTypeFixture.ServerTypes);

        _handler = new GetServerTypeNameUniqueQueryHandler(_mockServerTypeRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_ServerTypeName_Exist()
    {
        _serverTypeFixture.ServerTypes[0].Name = "DR_Site";

        var result = await _handler.Handle(new GetServerTypeNameUniqueQuery { Type = _serverTypeFixture.ServerTypes[0].Name, Id = _serverTypeFixture.ServerTypes[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_ServerTypeNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetServerTypeNameUniqueQuery { Type = "Server", Id = _serverTypeFixture.ServerTypes[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsServerTypeNameExist_OneTime()
    {
        var handler = new GetServerTypeNameUniqueQueryHandler(_mockServerTypeRepository.Object);

        await handler.Handle(new GetServerTypeNameUniqueQuery { Id = _serverTypeFixture.ServerTypes[0].ReferenceId, Type = _serverTypeFixture.ServerTypes[0].Name }, CancellationToken.None);

        _mockServerTypeRepository.Verify(x => x.IsServerTypeNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_False_ServerTypeName_NotMatch()
    {
        var result = await _handler.Handle(new GetServerTypeNameUniqueQuery { Type = "DR_Pro", Id = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockServerTypeRepository = ServerTypeRepositoryMocks.GetServerTypeEmptyRepository();

        var result = await _handler.Handle(new GetServerTypeNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}
