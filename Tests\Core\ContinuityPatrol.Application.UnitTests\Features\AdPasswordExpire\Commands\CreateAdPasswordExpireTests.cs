using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordExpire.Commands;

public class CreateAdPasswordExpireTests : IClassFixture<AdPasswordExpireFixture>
{
    private readonly AdPasswordExpireFixture _adPasswordExpireFixture;
    private readonly Mock<IAdPasswordExpireRepository> _mockAdPasswordExpireRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly CreateAdPasswordExpireCommandHandler _handler;

    public CreateAdPasswordExpireTests(AdPasswordExpireFixture adPasswordExpireFixture)
    {
        _adPasswordExpireFixture = adPasswordExpireFixture;

        _mockAdPasswordExpireRepository = AdPasswordExpireRepositoryMocks.CreateAdPasswordExpireRepository(_adPasswordExpireFixture.AdPasswordExpires);
        _mockMapper = new Mock<IMapper>();
        _mockPublisher = new Mock<IPublisher>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<Domain.Entities.AdPasswordExpire>(It.IsAny<CreateAdPasswordExpireCommand>()))
            .Returns((CreateAdPasswordExpireCommand cmd) => new Domain.Entities.AdPasswordExpire
            {
                DomainServerId = cmd.DomainServerId,
                DomainServer = cmd.DomainServer,
                UserName = cmd.UserName,
                Email = cmd.Email,
                ServerList = cmd.ServerList,
                NotificationDays = cmd.NotificationDays,
                IsPassword = cmd.IsPassword
            });

        _handler = new CreateAdPasswordExpireCommandHandler(
            _mockMapper.Object,
            _mockAdPasswordExpireRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_CreateAdPasswordExpireResponse_When_AdPasswordExpireCreated()
    {
        // Arrange
        var command = _adPasswordExpireFixture.CreateAdPasswordExpireCommand;

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(CreateAdPasswordExpireResponse));
        result.Id.ShouldNotBeNullOrEmpty();
        result.Message.ShouldContain("AdPasswordExpire");
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var command = _adPasswordExpireFixture.CreateAdPasswordExpireCommand;

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockAdPasswordExpireRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.AdPasswordExpire>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_PublishEvent_OnlyOnce()
    {
        // Arrange
        var command = _adPasswordExpireFixture.CreateAdPasswordExpireCommand;

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(It.IsAny<AdPasswordExpireCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var command = _adPasswordExpireFixture.CreateAdPasswordExpireCommand;

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<Domain.Entities.AdPasswordExpire>(It.IsAny<CreateAdPasswordExpireCommand>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PublishEventWithCorrectName_When_AdPasswordExpireCreated()
    {
        // Arrange
        var command = _adPasswordExpireFixture.CreateAdPasswordExpireCommand;
        command.UserName = "TestUser";

        AdPasswordExpireCreatedEvent capturedEvent = null;
        _mockPublisher.Setup(x => x.Publish(It.IsAny<AdPasswordExpireCreatedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<AdPasswordExpireCreatedEvent, CancellationToken>((evt, ct) => capturedEvent = evt)
            .Returns(Task.CompletedTask);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEvent.ShouldNotBeNull();
        capturedEvent.Name.ShouldBe("TestUser");
    }

    [Fact]
    public async Task Handle_CreateResponseWithCorrectProperties_When_AdPasswordExpireCreated()
    {
        // Arrange
        var command = _adPasswordExpireFixture.CreateAdPasswordExpireCommand;
        command.UserName = "TestUser";

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldContain("AdPasswordExpire");
        result.Message.ShouldContain("TestUser");
        result.Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_MapCommandToEntity_WithCorrectProperties()
    {
        // Arrange
        var command = new CreateAdPasswordExpireCommand
        {
            DomainServerId = "DS001",
            DomainServer = "TestDomain.com",
            UserName = "TestUser",
            Email = "<EMAIL>",
            ServerList = "Server1,Server2",
            NotificationDays = "7,14,30",
            IsPassword = true
        };

        Domain.Entities.AdPasswordExpire capturedEntity = null;
        _mockAdPasswordExpireRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.AdPasswordExpire>()))
            .Callback<Domain.Entities.AdPasswordExpire>(entity => capturedEntity = entity)
            .ReturnsAsync((Domain.Entities.AdPasswordExpire entity) => 
            {
                entity.ReferenceId = Guid.NewGuid().ToString();
                return entity;
            });

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEntity.ShouldNotBeNull();
        capturedEntity.DomainServerId.ShouldBe("DS001");
        capturedEntity.DomainServer.ShouldBe("TestDomain.com");
        capturedEntity.UserName.ShouldBe("TestUser");
        capturedEntity.Email.ShouldBe("<EMAIL>");
        capturedEntity.ServerList.ShouldBe("Server1,Server2");
        capturedEntity.NotificationDays.ShouldBe("7,14,30");
        capturedEntity.IsPassword.ShouldBeTrue();
    }
}
