﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = 'Postgres';
let infraObjectId = sessionStorage.getItem("infraobjectId");

setTimeout(() => { postgresmonitorstatus(mId, monitortype) }, 250)
setTimeout(() => { postgresServer(infraObjectId) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
$('#mssqlserver').hide();
async function postgresServer(id) {

    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
    let data = {}
    data.infraObjectId = id;
    let mssqlServerData = await getAysncWithHandler(url, data);
   
    if (mssqlServerData != null && mssqlServerData?.length > 0) {
        mssqlServerData?.forEach(data => {
            let value = data?.isServiceUpdate
            let parsed = []
            if (value && value !== 'NA') parsed = JSON?.parse(value)
            if (parsed) {
                $('#mssqlserver').show();
                bindPostgresServer(mssqlServerData)
            }
        })
        
    } else {
        $('#mssqlserver').hide();
    }

}
$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})

function bindPostgresServer(mssqlServerData) {

    let prType = { IpAddress: '--', Services: [] };
    let drType = { IpAddress: '--', Services: [] };

    // Loop through each item to find PR and DR entries
    mssqlServerData?.forEach(item => {
        let parsedServices = [];
        try {
            const value = item?.isServiceUpdate
            if (value && value !== 'NA') {
                parsedServices = JSON.parse(item?.isServiceUpdate)
            }
        } catch (e) {
            console.error("Invalid JSON in isServiceUpdate:", item?.isServiceUpdate);
        }
       
        parsedServices?.forEach(serviceGroup => {
            if (serviceGroup?.Type === 'PR') {
                prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
                prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];
            } else if (serviceGroup?.Type === 'DR') {
                prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
                prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];
            }
        });
    });

    // Set header IPs
    $('#prIp').text('Primary (' + prType?.IpAddress + ')');
    $('#drIp').text('DR (' + drType?.IpAddress + ')');
    
    const prStatusSummary = getStatusSummary(prType.Services.map(s => s.Status).filter(Boolean));
    const drStatusSummary = getStatusSummary(drType.Services.map(s => s.Status).filter(Boolean));
    $('#prIp').html(`Primary (${prType.IpAddress}) <br ><span class="status-summary text-muted small">${prStatusSummary}</span>`);
    $('#drIp').html(`DR (${drType.IpAddress}) <br ><span class="status-summary text-muted small">${drStatusSummary}</span>`);

    // Unique list of all service names from both PR and DR
    let allServiceNames = [...new Set([
        ...prType?.Services?.map(s => s?.ServiceName),
        ...drType?.Services?.map(s => s?.ServiceName)
    ])];

    // Build table rows
    let tbody = $('#mssqlserverbody');
    tbody.empty();

    allServiceNames?.forEach(serviceName => {
        let prService = prType?.Services?.find(s => s?.ServiceName === serviceName);
        let drService = drType?.Services?.find(s => s?.ServiceName === serviceName);

        let prStatus = prService ? prService?.Status : '--';
        let drStatus = drService ? drService?.Status : '--';
        let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
        let drIcon = drStatus !== '--' ? `<i class="${getStatusIconClass(drStatus)} me-2 fs-6"></i>` : '';
        const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";

        let row = `
            <tr>
                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
                <td>${prIcon}${prStatus}</td>
                <td>${drIcon}${drStatus}</td>
            </tr>
        `;
        tbody.append(row);
    });
}
function getStatusSummary(arr) {
    let countMap = {};
    arr?.forEach(status => {
        countMap[status] = (countMap[status] || 0) + 1;
    });
    let total = arr?.length;
    let statusSummary = Object.entries(countMap)
        .map(([status, count]) => `${count} ${status}`)
        .join(', ');
    return statusSummary ? `${statusSummary} out of ${total} services` : '--';
}
function getStatusIconClass(status) {
    if (!status) return "text-danger cp-disable";

    const lowerStatus = status.toLowerCase();
    if (lowerStatus === "running") {
        return "text-success cp-reload cp-animate";
    } else if (lowerStatus === "error" || lowerStatus === "stopped") {
        return "text-danger cp-fail-back";
    } else {
        return "text-danger cp-disable";
    }
}
async function postgresmonitorstatus(id, type) {

    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
}

let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'

function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}

function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}

function propertiesData(value) {
    let ipprdata;
    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    else {
        let data = JSON?.parse(value?.properties);
        let customSite = data?.PostgresMonitoringModels?.length > 1;
        if (customSite) {
            $("#Sitediv").show();
        } else {
            $("#Sitediv").hide();
        }


        $(".siteContainer").empty();


        data?.PostgresMonitoringModels?.forEach((a, index) => {
            let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        });


        if (data?.PostgresMonitoringModels?.length > 0) {
            $("#siteName0 .nav-link").addClass("active");
            displaySiteData(data?.PostgresMonitoringModels[0]);
        }



        let defaultSite = data?.PostgresMonitoringModels?.find(d => d?.Type === 'DR') || data?.PostgresMonitoringModels[0];
        if (defaultSite) {
            displaySiteData(defaultSite);
        }

        $(document).on('click', '.siteListChange', function () {
            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0]?.id
            let getSiteName = $(`#${siteId} .siteName`).text()

            let MonitoringModel = data?.PostgresMonitoringModels?.find(d => d?.Type === getSiteName);
            if (MonitoringModel) {
                $('#PR_Server_IpAddress').find('i').remove();
                $('#DR_Server_IpAddress').find('i').remove();
                displaySiteData(MonitoringModel);
            }
        });

        function displaySiteData(siteData) {
            let obj = {};
            $('.dynamicSite-header').text(siteData?.Type).attr('title', siteData?.Type);

            for (let key in siteData?.MonitoringModel) {
                obj[`DR_` + key] = siteData?.MonitoringModel[key];
            }
             ipprdata = obj?.DR_connectViaHostName?.toLowerCase() === "true" ? obj?.DR_Server_HostName : obj?.DR_Server_IpAddress

            $("#DR_Server_IpAddress").text(ipprdata)
            let MonitoringModelMysql = [
                  "DR_Server_HostName","DR_Database",
                "DR_Database_Version", "DR_DatabaseServiceStatus", "DR_DatabaseClusterStatus",  "DR_ReplicationStatus", "DR_RecoveryStatus", 
                "DR_DataDirectoryPath","DR_CurrentWalLsn", "DR_CurrentWalLsnFileName", 
                "DR_LastWalReceiveLsnFileName","DR_LastWalReceiveLsn", "DR_LastWalReplayLsnFileNameDR",
                "DR_LastWalReplayLsnDR", 
            ];

            if (Object.keys(obj)?.length > 0) {
                bindProperties(obj, MonitoringModelMysql, value, ipprdata);
            }
        }
        let dbpostgresDetail = data?.PrPostgresMonitoringModel?.MonitoringModel
         ipprdata = dbpostgresDetail?.Pr_ConnectViaHostName?.toLowerCase() === "true" ? dbpostgresDetail?.PR_Server_HostName : dbpostgresDetail?.PR_Server_IpAddress

        $('#PR_Server_IpAddress').text(ipprdata)
        const dbpostDetailsProp = [
           "PR_Server_HostName", "PR_Database", "PR_DataDirectoryPath",
            "PR_Database_Version", "PR_DatabaseServiceStatus", "PR_DatabaseClusterStatus", "PR_ReplicationStatus",
            "PR_RecoveryStatus", "CurrentWalLsnPR", "PR_CurrentWalLsnFileName", "PR_LastWalReceiveLsnFileName",
            "PR_LastWalReceiveLsn", "PR_LastWalReplayLsnFileName", "PR_LastWalReplayLsnPR", "PR_DataLagInSize", "PR_Datalag"
        ];

        bindProperties(dbpostgresDetail, dbpostDetailsProp, value, ipprdata);
       

        //Datalag
        const datalag = checkAndReplace(dbpostgresDetail?.PR_Datalag);
        
        let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0") ? `${(datalag)}` : 'NA';

        var result = "";
        var iconClass = "text-danger cp-disable";

        if (dataLagValue !== "NA") {
            if (dataLagValue.includes(".")) {
                const values = dataLagValue.split(".");
                const hours = values[0] * 24;
                const minutes = values[1]?.split(':')?.slice(0, 2)?.join(':');
                const min = minutes?.split(':');
                const firstValue = parseInt(min[0]) + parseInt(hours);
                result = firstValue + ":" + min[1];
            }
            else if (dataLagValue.includes("+")) {
                const value = dataLagValue.split(" ");
                result = value[1]?.split(':')?.slice(0, 2)?.join(':');
            }
            else {
                result = dataLagValue.split(':')?.slice(0, 2)?.join(':');
            }

            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);

            if (minute > value?.configuredRPO) {
                $('#PR_Datalag').text(result).attr('title', result).css('color', 'red');
            } else {
                $('#PR_Datalag').text(result).attr('title', result).css('color', '');
            }

            iconClass = "text-primary cp-time";
        } else {
            dataLagValue !== 'NA' ? $('#PR_Datalag').text(dataLagValue).attr('title', dataLagValue).css('color', 'red') : $('#PR_Datalag').text(dataLagValue).attr('title', dataLagValue).css('color', '')
        }

        $('#PR_Datalag').prepend(`<i class="${iconClass} me-1 fs-6"></i>`);

    }
}

function setPropData(data, propSets,value) {
    propSets.forEach(properties => {
        bindProperties(data, properties,value);
    });
}

function bindProperties(data, properties, value) {    
   // let prStatus = value?.prServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value.prServerStatus.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.prServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";
    // let drStatus = value?.drServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value.drServerStatus.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";    
    let prdatabaseicon = data?.PR_Database ? "cp-database text-primary" : "text-danger cp-disable"
    let drdatabaseicon = data?.DR_Database ? "cp-database text-primary" : "text-danger cp-disable"
    let prdatabasefile = data?.PR_DataDirectoryPath ? "cp-control-file-type text-success" : "text-danger cp-disable"
    let drdatabasefile = data?.DR_DataDirectoryPath ? "cp-control-file-type text-success" : "text-danger cp-disable"
    let prWalLsnFileName = data?.PR_CurrentWalLsnFileName ? "cp-control-file-type text-success" : "text-danger cp-disable"
    let drWalLsnFileName = data?.DR_CurrentWalLsnFileName ? "cp-control-file-type text-success" : "text-danger cp-disable"
    let prDatabase_Version = data?.PR_Database_Version ? "cp-version text-success" : "text-danger cp-disable"
    let drDatabase_Version = data?.DR_Database_Version ? "cp-version text-success" : "text-danger cp-disable"
    let prWalReplayLsnPR = data?.PR_LastWalReplayLsnPR ? "cp-location text-success" : "text-danger cp-disable"
    let drWalReplayLsnPR = data?.DR_LastWalReplayLsnDR ? "cp-location text-success" : "text-danger cp-disable"
    let prDataLagInSize = data?.PR_DataLagInSize ? "cp-datalog text-success" : "text-danger cp-disable"
    let datalag = data?.PR_Datalag ? "cp-time text-primary" : "text-danger cp-disable"    
    const iconMapping = {
       // 'PR_Server_IpAddress': prStatus,
       // 'DR_Server_IpAddress': drStatus,
        'PR_Database': prdatabaseicon,
        'DR_Database': drdatabaseicon,
        'PR_DataDirectoryPath': prdatabasefile,
        'DR_DataDirectoryPath': drdatabasefile,
        'PR_CurrentWalLsnFileName': prWalLsnFileName,
        'DR_CurrentWalLsnFileName': drWalLsnFileName,
        'PR_Database_Version': prDatabase_Version,
        'DR_Database_Version': drDatabase_Version,
        'PR_LastWalReplayLsnPR': prWalReplayLsnPR,
        'DR_LastWalReplayLsnDR': drWalReplayLsnPR,
        'PR_DataLagInSize': prDataLagInSize,
        'PR_Datalag': datalag
    };   
    $('#PR_Server_IpAddress').find('i').remove();
    $('#DR_Server_IpAddress').find('i').remove();
    
    const prIconHtml = value?.prServerStatus?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success me-1 fs-6"></i>' :
        value?.prServerStatus?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger me-1 fs-6"></i>' :
            value?.prServerStatus?.toLowerCase() === "pending" ? '<i class="cp-pending text-warning me-1 fs-6"></i>' : '';
    $('#PR_Server_IpAddress').prepend(prIconHtml);

    const drIconHtml = value?.drServerStatus?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success me-1 fs-6"></i>' :
        value?.drServerStatus?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger me-1 fs-6"></i>' :
            value?.drServerStatus?.toLowerCase() === "pending" ? '<i class="cp-pending text-warning me-1 fs-6"></i>' : '';
    $('#DR_Server_IpAddress').prepend(drIconHtml);
    properties.forEach(property => {
        const value = data[property];
        let displayedValue = value !== undefined ? checkAndReplace(value) : 'NA';
        let iconClass = iconMapping[property] || '';
        
        // Add icons based on conditions
        switch (displayedValue?.toLowerCase()?.trim()) {
            case 'na':
                iconClass = 'text-danger cp-disable';
                break;
            case 'not allowed':
            case 'no':         
                iconClass = 'text-danger cp-disagree';
                break;
            case 'disabled':
            case 'disable':
                iconClass = 'text-danger cp-disables';
                break;  
            case 'enabled':
            case 'enable':
                iconClass = 'text-success cp-enables';
                break;  
            case 'streaming':
                iconClass = 'text-success cp-refresh';
                break;
            case 'running':
            case 'run':
                iconClass = 'text-success cp-reload cp-animate';
                break;
            case 'stopped':
            case 'stop':
                iconClass = 'text-danger cp-Stopped';
                break;
            case 'f':
            case 'false':
            case 'defer':
            case 'deferred':
                iconClass = 'text-danger cp-error';
                break;
            case 't':
            case 'true':
            case 'yes':
                iconClass = 'text-success  cp-agree';
                break;          
            case 'valid':
                iconClass = 'text-success cp-success';
                break;
            case 'pending':
                iconClass = 'text-warning cp-pending';
                break;
            case 'pause':
            case 'paused':
                iconClass = 'text-warning cp-circle-pause';
                break;
            case 'manual':
                iconClass = 'text-warning cp-settings';
                break;
            case 'synchronous_commit':
            case 'synchronized':
            case 'synchronizing':
            case 'sync':
                iconClass = 'text-success cp-refresh';
                break;
            case 'asynchronous_commit':
            case 'asynchronizing':
            case 'asynchronized':
            case 'async':
                iconClass = 'text-danger cp-refresh';
                break;
            case 'online':
                iconClass = 'text-success cp-online';
                break;
            case 'offline':
                iconClass = 'text-danger cp-offline';
                break;
            case 'enabled':          
            case 'connected':
            case 'connect':
                iconClass = 'text-success cp-connected';
                break;
            case 'disconnected':
            case 'disconnect':
                iconClass = 'text-danger cp-disconnecteds';
                break;
            case 'standby':
            case 'to standby':
            case 'mounted':
                iconClass = 'text-warning cp-control-file-type';
                break;
            case 'required':
            case 'require':
                iconClass = 'text-warning cp-warning';
                break;
            case 'healthy':
                iconClass = 'text-success cp-health-success';
                break;
            case 'nothealthy':
            case 'not_healthy':
            case 'unhealthy':
                iconClass = 'text-danger cp-health-error';
                break;
            case 'error':
                iconClass = 'text-danger cp-fail-back';
                break;
            case 'on':
                iconClass = 'text-success cp-end';
                break;
            case 'off':
                iconClass = 'text-danger cp-end';
                break;
            case 'current':
            case 'read write':
                iconClass = 'text-success cp-file-edits';
                break;
            case 'primary':
                iconClass = 'text-primary cp-list-prsite';
                break;
            case 'secondary':
                iconClass = 'text-info cp-dr';
                break;
            case 'physical standby':
                iconClass = 'text-info cp-physical-drsite';
                break;
          
            default:
               
                if (displayedValue?.includes('running')) {
                    iconClass = 'text-success cp-reload cp-animate';
                } else if (displayedValue?.includes('production') || displayedValue?.includes('archive recovery')) {
                    iconClass = 'text-warning cp-log-archive-config';
                } 
                break;
        }
        // Displayed value with icon
        const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(mergeValue).attr('title', displayedValue);
      
    });
}
