﻿@model ContinuityPatrol.Application.Features.User.Commands.UserLock.UserLockCommand
@using Microsoft.Extensions.Configuration
@using ContinuityPatrol.Shared.Services.Helper
@using ContinuityPatrol.Shared.Core.Helper;
@inject IConfiguration Configuration
@{
    ViewData["Title"] = "Lock";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
<body class="align-items-center d-grid vh-100">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <div class="card login_card">
                    <div class="card-header">
                        <img src="~/img/logo/cplogo.svg" alt="Logo" />
                    </div>
                    <form>
                        <div class="card-body">
                            <h4 class="text-primary">Hello @Model.UserName</h4>
                            <h6>Your screen has been locked</h6>
                            <span class="text-light">Enter your password to retrieve your session</span>
                            <div class="form-group mt-3">
                                <label class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-lock"></i>
                                    </span>
                                    <input asp-for="UserName" type="hidden" id="txtLoginName" data-loginname="@WebHelper.UserSession.LoginName">
                                    <input asp-for="Password" type="password" class="form-control" autocomplete="off" id="txtPassword" placeholder="Enter Password">
                                    <input asp-for="Url" type="hidden" id="txtUrl">
                                    <input asp-for="AuthenticationType" type="hidden" value="@Model.AuthenticationType" id="txtAuthenticationType">
                                    <i class="fs-6 cp-password-visible toggle-password me-2 "></i>
                                </div>
                                <span id="password_error"></span>
                            </div>
                        </div>
                        <form method="post" class="mb-3 mt-4 gap-2 d-flex">
                            <div class="form-group">
                                <div class="card-footer">
                                    <button type="button" class="btn btn-primary px-5" name="Login" id="Login" asp-action="LockLogin">Log in</button>
                                    <div class="mt-3">
                                        <div type="submit" class="text-secondary">
                                            <a asp-action="PreLogin" asp-controller="Account" asp-area="" class="text-primary text-decoration-underline">
                                                Click here
                                            </a> to sign in as a different user.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </form>
                </div>
            </div>
            <div class="col-8 align-content-between d-grid">
                <div class="text-center">
                    @* <img src="~/img/isomatric/lock-illustration.svg" alt="lock-illustration.svg" /> *@

                    <svg xmlns="http://www.w3.org/2000/svg" width="608" height="453" fill="none"><g clip-path="url(#a)"><mask id="b" width="608" height="453" x="0" y="0" maskUnits="userSpaceOnUse" style="mask-type:luminance"><path fill="#fff" d="M607.312.42h-607v452h607z" /></mask><g mask="url(#b)"><path fill="#EBEBEB" d="M606.852 397.635H.312v.303h606.54zM546.078 417.162h-40.18v.304h40.18zM402.108 420.451h-10.546v.304h10.546zM504.684 405.896h-23.282v.303h23.282zM116.346 407.934H63.949v.303h52.397zM134.828 407.934h-7.682v.303h7.682zM273.433 413.057H159.797v.303h113.636z" /><path fill="#F5F5F5" d="M384.303 276.396h145.741a10 10 0 0 1-9.557 9.984c-15.662.706-39.756 3.186-47.396 11.852-14.244 16.154-12.863 72.9 4.196 86.043 5.35 4.12 20.944 12.302 20.944 12.302h-82.113s14.572-8.347 19.635-12.302c16.964-13.251 17.457-69.962 3.148-86.043-7.343-8.25-29.926-10.916-45.079-11.757a10.083 10.083 0 0 1-9.519-10.079" /><path fill="#E6E6E6" d="M511.992 286.91c-14.803 1.187-32.544 4.112-38.9 11.322a28.7 28.7 0 0 0-4.818 8.346h-24.479a29.2 29.2 0 0 0-4.893-8.346c-6.381-7.169-24.269-10.119-38.791-11.322z" /><path fill="#EBEBEB" d="m446.385 276.469 1.979-22.653h-28.725l-1.979 22.653z" /><path fill="#E0E0E0" d="m446.385 276.469 1.979-22.653h1.548l-1.979 22.653z" /><path fill="#F0F0F0" d="m443.659 274.161 1.578-18.034h-22.871l-1.577 18.034z" /><path fill="#FAFAFA" d="m441.906 272.676 1.32-15.065h-19.107l-1.32 15.065z" /><path fill="#E6E6E6" d="M455.707 276.399h28.859l-3.205-36.598h-28.859z" /><path fill="#E0E0E0" d="M455.705 276.399h-1.378l-3.204-36.598h1.377z" /><path fill="#F0F0F0" d="M458.318 272.666h22.975l-2.545-29.135H455.77z" /><path fill="#FAFAFA" d="M460.002 270.269h19.192l-2.13-24.339h-19.191z" /><path fill="#EBEBEB" d="M469.694 256.88a3.337 3.337 0 1 1-4.719-4.72 3.337 3.337 0 0 1 4.719 4.72" /><path fill="#F0F0F0" d="M468.282 260.964a2.445 2.445 0 1 1 3.457 3.458 2.445 2.445 0 0 1-3.457-3.458" /><path fill="#E0E0E0" d="M99.453 234.667h136.094V84.152H99.453z" /><path fill="#F0F0F0" d="M95.107 234.667H236.26V84.152H95.107z" /><path fill="#fff" d="M101.08 227.754h129.436V88.955H101.08z" /><path fill="#E0E0E0" d="M101.08 227.754h3.743V88.955h-3.743z" /><path fill="#E0E0E0" d="M104.422 188.766v3.743h126.425v-3.743z" /><path fill="#F0F0F0" d="M101.08 188.766h131.444v-6.284H101.08z" /><path fill="#E6E6E6" d="M459.323 109.922s1.214-8.004-4.624-13.137-5.325-8.185-3.658-8.584 3.205 4.733 5.775 6.145c0 0 .081-7.314-2.526-10.01s-3.975-9.634-1.495-9.757 2.733 4.124 3.251 7.133 3.276 7.245 3.276 7.245.54-6.544 2.225-9.236-2.807-8.853-1.074-11.548c1.732-2.694 4.812 2.437 4.555 8.211s-4.107 11.933-3.205 17.965c0 0 1.697-4.235 3.223-4.618 1.527-.384 3.064-3.186 4.091-4.223 1.026-1.036 2.31 2.555 0 5.89-2.31 3.336-8.213 12.263-7.314 19.476z" /><path fill="#F5F5F5" d="m455.834 106.279-1.912 11.997a6.1 6.1 0 0 0 1.384 4.92 6.093 6.093 0 0 0 4.641 2.14h2.039a6.097 6.097 0 0 0 6.025-7.06l-1.912-11.997z" /><path fill="#E6E6E6" d="M484.749 120.908h-135.14v6.944h135.14z" /><path fill="#F0F0F0" d="M361.783 95.316h-5.953v25.595h5.953zM390.572 95.316h-5.953v25.595h5.953zM423.252 94.676l-5.865 1.012 4.351 25.21 5.865-1.012zM369.16 98.688h-5.953v22.222h5.953zM382.847 98.688h-5.954v22.222h5.954zM417.386 98.688h-5.954v22.222h5.954zM398.392 97.928l-5.812 1.291 4.82 21.693 5.811-1.292zM374.308 94.047h-2.976v26.866h2.976zM409.723 94.047h-2.977v26.866h2.977z" /><path fill="#E6E6E6" d="M349.609 179.688H484.75v-6.944H349.609z" /><path fill="#F0F0F0" d="M472.576 172.744h5.954V147.15h-5.954zM443.789 172.744h5.954V147.15h-5.954zM406.758 171.734l5.867 1.011 4.347-25.222-5.867-1.011zM465.199 172.743h5.954v-22.222h-5.954zM451.516 172.743h5.953v-22.222h-5.953zM416.973 172.743h5.953v-22.222h-5.953zM431.148 171.453l5.812 1.291 4.82-21.693-5.812-1.291zM460.051 172.744h2.977v-26.865h-2.977zM424.637 172.744h2.978v-26.865h-2.978z" /><path fill="#F5F5F5" d="M362.576 172.516h29.009v-20.028h-29.009zM303.582 452.421c129.899 0 235.202-6.15 235.202-13.736s-105.303-13.736-235.202-13.736-235.203 6.15-235.203 13.736 105.304 13.736 235.203 13.736" /><path fill="#407BFF" d="M403.62 408.347s-6.007-13.108.858-26.031c6.865-12.924 8.855-15.967 4.478-18.615-4.377-2.649-6.384 10.338-6.384 10.338s-3.689-8.933-.643-18.284c3.047-9.352 4.956-24.576.796-26.21s-.794 18.997-4.402 26.028a60.5 60.5 0 0 1-3.53-15.337c-.5-7.509.283-19.5-3.005-22.576-3.287-3.075-5.388-.947-2.16 15.624a173.3 173.3 0 0 1 3.205 30.042s-7.129-21.738-11.915-22.372-4.779 6.409-.684 12.511c4.095 6.103 11.995 17.045 13.289 22.674 0 0-7.822-16.36-14.864-11.346s6.126 14.891 11.995 18.465 9.943 25.952 9.943 25.952z" /><path fill="#0479FF" d="M403.62 408.347s-6.007-13.108.858-26.031c6.865-12.924 8.855-15.967 4.478-18.615-4.377-2.649-6.384 10.338-6.384 10.338s-3.689-8.933-.643-18.284c3.047-9.352 4.956-24.576.796-26.21s-.794 18.997-4.402 26.028a60.5 60.5 0 0 1-3.53-15.337c-.5-7.509.283-19.5-3.005-22.576-3.287-3.075-5.388-.947-2.16 15.624a173.3 173.3 0 0 1 3.205 30.042s-7.129-21.738-11.915-22.372-4.779 6.409-.684 12.511c4.095 6.103 11.995 17.045 13.289 22.674 0 0-7.822-16.36-14.864-11.346s6.126 14.891 11.995 18.465 9.943 25.952 9.943 25.952z" opacity=".2" /><path fill="#0479FF" d="M398.879 413.723s5.849-13.194-1.17-26.012-9.048-15.834-4.701-18.546 6.503 10.242 6.503 10.242 3.581-8.987.423-18.29c-3.158-9.304-5.25-24.505-1.111-26.196s1.023 18.983 4.712 25.96a60.7 60.7 0 0 0 3.346-15.389c.409-7.516-.513-19.494 2.733-22.62 3.246-3.125 5.373-1.028 2.348 15.59a173.4 173.4 0 0 0-2.843 30.088s6.869-21.843 11.645-22.547c4.777-.704 4.855 6.333.832 12.499-4.022 6.166-11.788 17.222-13.016 22.871 0 0 7.634-16.475 14.728-11.568s-5.948 14.986-11.773 18.643-9.632 26.096-9.632 26.096z" /><path fill="#263238" d="M425.863 404.314h-48.33l3.911 34.589h40.506z" /><path fill="#000" d="M521.455 60.4H264.688a2.336 2.336 0 0 0-2.336 2.336v228.219a2.336 2.336 0 0 0 2.336 2.336h256.767a2.335 2.335 0 0 0 2.335-2.336V62.735a2.335 2.335 0 0 0-2.335-2.335" opacity=".2" /><path fill="#0479FF" d="M254.799 56.1h256.767a2.334 2.334 0 0 1 2.336 2.335v228.22a2.34 2.34 0 0 1-1.442 2.157 2.3 2.3 0 0 1-.894.178H254.799a2.333 2.333 0 0 1-2.336-2.335V58.435a2.336 2.336 0 0 1 2.336-2.335" /><path fill="#fff" d="M252.463 76.83v209.153a2.79 2.79 0 0 0 2.493 3.006h256.447a2.784 2.784 0 0 0 2.493-3.006V76.83z" opacity=".6" /><path fill="#fff" d="M510.802 89.162h-255.24v184.713h255.24z" /><path fill="#fff" d="M505.365 70.475c2.115 0 3.83-2.068 3.83-4.62 0-2.55-1.715-4.619-3.83-4.619s-3.83 2.068-3.83 4.62c0 2.55 1.715 4.619 3.83 4.619M492.805 70.475c2.116 0 3.831-2.068 3.831-4.62 0-2.55-1.715-4.619-3.831-4.619s-3.83 2.068-3.83 4.62c0 2.55 1.715 4.619 3.83 4.619M484.079 65.855c0 2.549-1.715 4.618-3.831 4.618s-3.832-2.07-3.832-4.618c0-2.55 1.716-4.619 3.832-4.619s3.831 2.068 3.831 4.619" opacity=".6" /><path fill="#407BFF" d="M309.944 110.725v47.645l-12.253 14.778h-27.134v-62.423z" /><path fill="#fff" d="M309.944 110.725v47.645l-12.253 14.778h-27.134v-62.423z" opacity=".7" /><path fill="#407BFF" d="m309.943 158.369-12.252 14.777v-14.777zM303.838 119.725h-27.176v1.789h27.176zM303.838 126.504h-27.176v1.789h27.176zM303.838 133.283h-27.176v1.789h27.176zM303.838 140.066h-27.176v1.789h27.176zM296.81 146.846h-20.148v1.789h20.148z" opacity=".3" /><path fill="#407BFF" d="M372.415 110.725v47.645l-12.252 14.778h-27.134v-62.423z" /><path fill="#fff" d="M372.415 110.725v47.645l-12.252 14.778h-27.134v-62.423z" opacity=".7" /><path fill="#407BFF" d="m372.414 158.369-12.252 14.777v-14.777zM366.308 119.725h-27.177v1.789h27.177zM366.308 126.504h-27.177v1.789h27.177zM366.308 133.283h-27.177v1.789h27.177zM366.308 140.066h-27.177v1.789h27.177zM359.28 146.846h-20.149v1.789h20.149z" opacity=".3" /><path fill="#407BFF" d="M434.886 110.725v47.645l-12.252 14.778H395.5v-62.423z" /><path fill="#fff" d="M434.886 110.725v47.645l-12.252 14.778H395.5v-62.423z" opacity=".7" /><path fill="#407BFF" d="m434.886 158.369-12.253 14.777v-14.777zM428.781 119.725h-27.177v1.789h27.177zM428.781 126.504h-27.177v1.789h27.177zM428.781 133.283h-27.177v1.789h27.177zM428.781 140.066h-27.177v1.789h27.177zM421.753 146.846h-20.149v1.789h20.149z" opacity=".3" /><path fill="#407BFF" d="M497.357 110.725v47.645l-12.253 14.778h-27.133v-62.423z" /><path fill="#fff" d="M497.357 110.725v47.645l-12.253 14.778h-27.133v-62.423z" opacity=".7" /><path fill="#407BFF" d="m497.357 158.369-12.252 14.777v-14.777zM491.25 119.725h-27.176v1.789h27.176zM491.25 126.504h-27.176v1.789h27.176zM491.25 133.283h-27.176v1.789h27.176zM491.25 140.066h-27.176v1.789h27.176zM484.222 146.846h-20.148v1.789h20.148z" opacity=".3" /><path fill="#407BFF" d="M309.944 193.217v47.645l-12.253 14.778h-27.134v-62.423z" /><path fill="#fff" d="M309.944 193.217v47.645l-12.253 14.778h-27.134v-62.423z" opacity=".7" /><path fill="#407BFF" d="m309.943 240.861-12.252 14.778v-14.778zM303.838 202.217h-27.176v1.789h27.176zM303.838 208.996h-27.176v1.789h27.176zM303.838 215.775h-27.176v1.789h27.176zM303.838 222.559h-27.176v1.789h27.176zM296.81 229.338h-20.148v1.789h20.148z" opacity=".3" /><path fill="#407BFF" d="M372.415 193.217v47.645l-12.252 14.778h-27.134v-62.423z" /><path fill="#fff" d="M372.415 193.217v47.645l-12.252 14.778h-27.134v-62.423z" opacity=".7" /><path fill="#407BFF" d="m372.414 240.861-12.252 14.778v-14.778zM366.308 202.217h-27.177v1.789h27.177zM366.308 208.996h-27.177v1.789h27.177zM366.308 215.775h-27.177v1.789h27.177zM366.308 222.559h-27.177v1.789h27.177zM359.28 229.338h-20.149v1.789h20.149z" opacity=".3" /><path fill="#407BFF" d="M434.886 193.217v47.645l-12.252 14.778H395.5v-62.423z" /><path fill="#fff" d="M434.886 193.217v47.645l-12.252 14.778H395.5v-62.423z" opacity=".7" /><path fill="#407BFF" d="m434.886 240.861-12.253 14.778v-14.778zM428.781 202.217h-27.177v1.789h27.177zM428.781 208.996h-27.177v1.789h27.177zM428.781 215.775h-27.177v1.789h27.177zM428.781 222.559h-27.177v1.789h27.177zM421.753 229.338h-20.149v1.789h20.149z" opacity=".3" /><path fill="#407BFF" d="M497.357 193.217v47.645l-12.253 14.778h-27.133v-62.423z" /><path fill="#fff" d="M497.357 193.217v47.645l-12.253 14.778h-27.133v-62.423z" opacity=".7" /><path fill="#407BFF" d="m497.357 240.861-12.252 14.778v-14.778zM491.25 202.217h-27.176v1.789h27.176zM491.25 208.996h-27.176v1.789h27.176zM491.25 215.775h-27.176v1.789h27.176zM491.25 222.559h-27.176v1.789h27.176zM484.222 229.338h-20.148v1.789h20.148z" opacity=".3" /><path fill="#000" d="M401.753 153.75h-65.221a4.1 4.1 0 0 0-4.102 4.102v49.47a4.103 4.103 0 0 0 4.102 4.102h65.221a4.103 4.103 0 0 0 4.102-4.102v-49.47a4.1 4.1 0 0 0-4.102-4.102M396.081 151.063h-7.279v-7.872a18.754 18.754 0 1 0-37.511 0v7.872h-7.279v-7.872a26.034 26.034 0 1 1 52.069 0z" opacity=".1" /><path fill="#263238" d="M371.425 151.324h-65.221a4.1 4.1 0 0 0-4.102 4.102v49.47a4.1 4.1 0 0 0 4.102 4.102h65.221a4.1 4.1 0 0 0 4.102-4.102v-49.47a4.103 4.103 0 0 0-4.102-4.102" /><path fill="#fff" d="M358.148 151.324h-51.944a4.1 4.1 0 0 0-4.102 4.102v49.47a4.1 4.1 0 0 0 4.102 4.102h51.944a4.1 4.1 0 0 0 4.102-4.102v-49.47a4.103 4.103 0 0 0-4.102-4.102" opacity=".2" /><path fill="#263238" d="M335.862 172.959c0-3.975-2.421-7.199-5.406-7.199s-5.407 3.224-5.407 7.199c0 3.025 1.402 5.61 3.386 6.676l-1 15.038h6.041l-1.001-15.038c1.985-1.067 3.387-3.652 3.387-6.676M365.757 148.636h-7.279v-7.872c0-4.974-1.977-9.745-5.495-13.262a18.75 18.75 0 0 0-26.523 0 18.76 18.76 0 0 0-5.494 13.262v7.872h-7.278v-7.872a26.02 26.02 0 0 1 7.624-18.409 26.037 26.037 0 0 1 44.445 18.409z" /><path fill="#fff" d="M365.757 148.636h-7.279v-7.872c0-4.974-1.977-9.745-5.495-13.262a18.75 18.75 0 0 0-26.523 0 18.76 18.76 0 0 0-5.494 13.262v7.872h-7.278v-7.872a26.02 26.02 0 0 1 7.624-18.409 26.037 26.037 0 0 1 44.445 18.409z" opacity=".4" /><path fill="#fff" d="M365.75 140.768v7.873h-4.937v-8.771a23.77 23.77 0 0 0-6.968-16.815 23.78 23.78 0 0 0-16.818-6.961 23.14 23.14 0 0 0-13.334 4.185 26.01 26.01 0 0 1 38.108 6.688 26 26 0 0 1 3.949 13.801" opacity=".2" /><path fill="#407BFF" d="M262.891 280.249h-29.405v-1.131h6.642a1.9 1.9 0 0 0 1.866-1.576l2.969-17.687.583-3.482h10.072l9.01 21.261a1.88 1.88 0 0 1-1.737 2.615" /><path fill="#fff" d="M262.891 280.249h-29.405v-1.131h6.642a1.9 1.9 0 0 0 1.866-1.576l2.969-17.687.583-3.482h10.072l9.01 21.261a1.88 1.88 0 0 1-1.737 2.615" opacity=".4" /><path fill="#000" d="M262.931 279.115h-22.808a1.9 1.9 0 0 0 1.866-1.576l2.969-17.687h10.556l8.027 18.329a.66.66 0 0 1-.055.631.67.67 0 0 1-.555.303" opacity=".2" /><path fill="#407BFF" d="M278.706 262.888h-58.819a.855.855 0 0 1-.869-.67.86.86 0 0 1 .005-.394l8.357-44.195a1.345 1.345 0 0 1 1.265-1.064h58.82a.85.85 0 0 1 .696.316.86.86 0 0 1 .166.748l-8.357 44.195a1.35 1.35 0 0 1-1.264 1.064" /><path fill="#fff" d="M278.706 262.888h-58.819a.855.855 0 0 1-.869-.67.86.86 0 0 1 .005-.394l8.357-44.195a1.345 1.345 0 0 1 1.265-1.064h58.82a.85.85 0 0 1 .696.316.86.86 0 0 1 .166.748l-8.357 44.195a1.35 1.35 0 0 1-1.264 1.064" opacity=".4" /><path fill="#263238" d="M277.493 262.888h-58.819a.855.855 0 0 1-.863-1.064l8.356-44.195a1.346 1.346 0 0 1 1.265-1.064h58.82a.855.855 0 0 1 .862 1.064l-8.356 44.195a1.35 1.35 0 0 1-1.265 1.064" /><path fill="#407BFF" d="M276.711 260.179h-56.234l7.733-40.904h56.234zM178.693 280.249v-1.379l22.643-4.575h44.281a2.975 2.975 0 1 1 .001 5.95z" /><path fill="#fff" d="M248.589 277.272a2.97 2.97 0 0 1-.862 2.099 3.02 3.02 0 0 1-2.111.874h-66.925v-1.371l22.648-4.585h44.277a2.973 2.973 0 0 1 2.973 2.983" opacity=".4" /><path fill="#fff" d="M248.589 277.272a2.97 2.97 0 0 1-.862 2.099 3.02 3.02 0 0 1-2.111.874h-26.893v-1.371l22.648-4.585h4.241a2.98 2.98 0 0 1 2.109.873 2.98 2.98 0 0 1 .868 2.11" opacity=".4" /><path fill="#263238" d="M264.779 438.902h6.058l10.886-151.564h-8.039z" /><path fill="#fff" d="M264.775 438.902h6.063l10.355-144.035.534-7.285h-8.04l-.437 7.285z" opacity=".1" /><path fill="#000" d="M273.25 294.505h7.942l.534-6.923h-8.04z" opacity=".2" /><path fill="#263238" d="M220.313 438.902h6.06l.989-151.564h-8.038z" /><path fill="#fff" d="m219.32 287.582.158 24.454.831 126.866h6.063l.83-126.866.158-24.454z" opacity=".1" /><path fill="#000" d="m219.32 287.582.158 24.725h7.729l.158-24.725z" opacity=".2" /><path fill="#263238" d="M155.986 438.902h6.058l10.886-151.564h-8.039z" /><path fill="#fff" d="M155.982 438.902h6.063L172.4 294.867l.534-7.285h-8.041l-.435 7.285z" opacity=".1" /><path fill="#000" d="M164.457 294.505h7.942l.534-6.923h-8.041z" opacity=".2" /><path fill="#263238" d="M334.09 438.901h6.059l-8.91-151.563h-8.038z" /><path fill="#fff" d="m323.205 287.582.534 7.285 10.355 144.035h6.053l-8.467-144.035-.435-7.285z" opacity=".1" /><path fill="#000" d="M331.246 287.582h-8.041l.534 6.923h7.941z" opacity=".2" /><path fill="#407BFF" d="M335.575 291.137h-175.02a2.037 2.037 0 0 1-2.037-2.038v-5.554h179.095v5.554a2.04 2.04 0 0 1-2.038 2.038" /><path fill="#fff" d="M335.575 291.137h-175.02a2.037 2.037 0 0 1-2.037-2.038v-5.554h179.095v5.554a2.04 2.04 0 0 1-2.038 2.038" opacity=".4" /><path fill="#000" d="M158.521 287.338h179.093v-3.795H158.521z" opacity=".2" /><path fill="#407BFF" d="M154.766 284.533H341.37a2.138 2.138 0 1 0 0-4.277H154.766a2.138 2.138 0 1 0 0 4.277" /><path fill="#fff" d="M154.766 284.533H341.37a2.138 2.138 0 1 0 0-4.277H154.766a2.138 2.138 0 1 0 0 4.277" opacity=".4" /><path fill="#fff" d="M231.336 242.185a1.81 1.81 0 0 1-1.775-2.155l.754-3.874a2.32 2.32 0 0 1 2.271-1.871h31.547a1.81 1.81 0 0 1 1.774 2.156l-.754 3.873a2.32 2.32 0 0 1-2.269 1.871z" opacity=".3" /><path fill="#fff" d="M262.882 241.577h-31.546a1.2 1.2 0 0 1-.928-.437 1.22 1.22 0 0 1-.252-.994l.754-3.874a1.706 1.706 0 0 1 1.675-1.379h31.547c.177 0 .352.039.513.114a1.19 1.19 0 0 1 .651.791c.044.171.049.351.015.524l-.754 3.875a1.705 1.705 0 0 1-1.675 1.38" /><path fill="#263238" d="M236.683 239.125c.619-.884.549-2-.155-2.493s-1.777-.175-2.395.709c-.619.885-.549 2.001.155 2.493.705.493 1.777.175 2.395-.709M241.999 238.234a2.24 2.24 0 0 1-2.017 1.833 1.41 1.41 0 0 1-1.109-.591 1.4 1.4 0 0 1-.192-1.242c.087-.485.332-.928.697-1.259a2.23 2.23 0 0 1 1.319-.575 1.4 1.4 0 0 1 1.108.592 1.405 1.405 0 0 1 .194 1.242M246.929 238.234a2.23 2.23 0 0 1-.697 1.258 2.23 2.23 0 0 1-1.319.575 1.4 1.4 0 0 1-1.109-.591 1.4 1.4 0 0 1-.193-1.242 2.25 2.25 0 0 1 .697-1.26 2.24 2.24 0 0 1 1.32-.574 1.42 1.42 0 0 1 1.109.592 1.406 1.406 0 0 1 .192 1.242M251.474 239.125c.619-.884.549-2-.155-2.493s-1.777-.175-2.395.709c-.618.885-.549 2.001.155 2.493.705.493 1.777.175 2.395-.709M256.789 238.234a2.23 2.23 0 0 1-.697 1.258 2.23 2.23 0 0 1-1.319.575 1.4 1.4 0 0 1-1.109-.591 1.401 1.401 0 0 1-.192-1.242 2.24 2.24 0 0 1 2.017-1.834 1.406 1.406 0 0 1 1.3 1.834M261.338 239.125c.618-.884.548-2-.156-2.493s-1.776-.175-2.395.709c-.618.885-.549 2.001.156 2.493.704.493 1.776.175 2.395-.709" /><path fill="#fff" d="M267.292 242.184a1.38 1.38 0 0 1-1.075-.491 1.406 1.406 0 0 1-.274-1.175l.955-4.807a1.815 1.815 0 0 1 1.724-1.43h4.806a1.37 1.37 0 0 1 1.074.492 1.406 1.406 0 0 1 .274 1.175l-.955 4.806a1.81 1.81 0 0 1-1.724 1.43z" opacity=".3" /><path fill="#263238" d="M267.292 241.575h4.801a1.206 1.206 0 0 0 1.131-.942l.955-4.801a.754.754 0 0 0-.754-.943h-4.807a1.2 1.2 0 0 0-1.131.943l-.955 4.801a.76.76 0 0 0 .418.875.75.75 0 0 0 .342.067" /><path fill="#407BFF" d="M269.757 240.359a.249.249 0 0 1-.197-.088c-.044-.069-.06-.152-.044-.232a.32.32 0 0 1 .129-.198l1.923-1.602-1.284-1.603a.318.318 0 0 1 .084-.429.3.3 0 0 1 .098-.066.29.29 0 0 1 .33.066l1.458 1.82a.316.316 0 0 1-.085.429l-2.181 1.82a.37.37 0 0 1-.231.083" /><path fill="#407BFF" d="M271.938 238.541h-3.279a.244.244 0 0 1-.244-.304.384.384 0 0 1 .363-.303h3.279a.24.24 0 0 1 .247.191.24.24 0 0 1-.003.112.392.392 0 0 1-.363.304" /><path fill="#0479FF" d="M164.505 164.478h28.38a3.886 3.886 0 0 0 3.886-3.886v-28.38a3.886 3.886 0 0 0-3.886-3.886h-28.38a3.886 3.886 0 0 0-3.886 3.886v28.38a3.886 3.886 0 0 0 3.886 3.886" /><path fill="#407BFF" d="m175.867 163.127 2.827 12.158 2.828-12.158z" /><path fill="#fff" d="M178.378 157.248a21.45 21.45 0 0 1-10.452-17.643l.013-.389 10.756-4.581 10.757 4.581.012.389a21.448 21.448 0 0 1-10.452 17.643l-.317.188z" opacity=".3" /><path fill="#fff" d="m178.695 135.293 10.162 4.327a20.65 20.65 0 0 1-10.162 17.108z" opacity=".6" /><path fill="#fff" d="m178.695 135.293-10.162 4.327a20.64 20.64 0 0 0 10.162 17.108z" opacity=".4" /><path fill="#E4897B" d="m168.93 272.344 8.483 1.27a32.3 32.3 0 0 1 5.918-1.27 39.5 39.5 0 0 1 6.559 0s3.838 1.439 3.838 5.117c0 0-8.954 2.4-12.397.961l-12.401-.961z" /><path fill="#000" d="m168.93 272.344 8.483 1.27a32.3 32.3 0 0 1 5.918-1.27 39.5 39.5 0 0 1 6.559 0s3.838 1.439 3.838 5.117c0 0-8.954 2.4-12.397.961l-12.401-.961z" opacity=".2" /><path fill="#AA473D" d="M189.965 214.988a22 22 0 0 0 1.017 5.14 2.84 2.84 0 0 1-2.966-.033z" /><path fill="#263238" d="M193.273 212.575c-.326.785-.908 1.289-1.301 1.131-.393-.159-.446-.931-.121-1.716s.907-1.289 1.298-1.131c.392.159.451.93.124 1.716" /><path stroke="#263238" stroke-linecap="round" stroke-linejoin="round" stroke-width=".725" d="M195.274 209.415s-2.505-1.52-4.735-.384" /><path fill="#E4897B" d="M187.595 221.591a12.266 12.266 0 0 1-8.987 4.666 11 11 0 0 1-8.78-3.512 11.1 11.1 0 0 1-2.262-3.759c-2.162-5.821.042-10.413 3.148-18.473a11.83 11.83 0 0 1 14.689-7.201 11.8 11.8 0 0 1 5.039 3.178c6.285 6.633 2.055 18.803-2.847 25.101" /><path fill="#000" d="M180.447 226.023q-.912.19-1.841.24a10.98 10.98 0 0 1-8.78-3.512 12.9 12.9 0 0 1 2.61-2.032 17.03 17.03 0 0 1 8.011 5.304" opacity=".2" /><path fill="#E4897B" d="M154.295 226.327c.795 5.684 13.214 7.805 13.214 7.805s6.126 2.791 6.381.359c-2.536-2.936-2.817-5.493-1.943-7.738a11.1 11.1 0 0 1 2.498-3.607 23 23 0 0 1 1.808-1.669l-3.788-15.551c-2.728 5.907-12.056 19.559-18.17 20.401" /><path fill="#263238" d="M189.738 192.096s2.653-.33 4.686 4.618c1.715 4.169-2.32 5.07-2.146 9.504s-3.872 6.89-3.872 6.89-3.405 2.934-6.299 1.343c0 0-3.787 1.858-4.784 5.099-.998 3.242-6.855-2.614-12.135-1.726 0 0-3.57-3.339-3.305-11.442a28.1 28.1 0 0 1 2.544-10.162s1.541-4.827 7.576-7.522 16.598-.648 17.735 3.398" /><path fill="#E4897B" d="M181.38 212.061a5.6 5.6 0 0 0 1.036 4.673c1.295 1.403 3.364-.377 4.201-2.844.728-2.192.977-5.629-.912-6.016-1.889-.386-3.928 1.821-4.325 4.187" /><path fill="#263238" d="M182.706 231.02a310 310 0 0 0-4.657 13.948 282 282 0 0 0-4.137 14.46c-.254.994-.51 1.979-.754 2.948a303 303 0 0 0-3.263 14.521 192 192 0 0 0-3.142 20.464l-47.608-5.954c1.649-8.176 2.936-15.321 4.731-23.024.995-4.222 2.123-8.613 3.582-13.429a222 222 0 0 1 3.615-11.121c4.78-13.708 9.183-17.505 9.183-17.505 18.314-2.939 42.45 4.692 42.45 4.692" /><path fill="#E4897B" d="m198.543 273.225 8.483 1.27a32.4 32.4 0 0 1 5.92-1.27 39 39 0 0 1 6.557 0s3.84 1.44 3.84 5.119c0 0-8.958 2.4-12.397.96l-12.403-.965z" /><path fill="#EBB376" d="m145.986 430.04 8.866.161 4.415-20.452-8.859-.149z" /><path fill="#000" d="m150.411 409.602 8.86.159-2.527 11.687-8.777-.588z" opacity=".2" /><path fill="#407BFF" d="M174.477 379.989a278 278 0 0 1-12.912 38.137l-16.18-1.234s6.639-25.93 9.048-49.675c1.49-14.658 1.357-26.788-2.933-27.726-2.703-.596-9.022-.574-17.117-.386V308.93c22.72 4.665 45.328 13.466 47.049 20.525 2.396 9.936-1.805 30.968-6.955 50.534" /><path fill="#407BFF" d="m143.666 417.65 19.263 1.279 2.835-8.327-21.224-.995z" /><path fill="#fff" d="m143.666 417.65 19.263 1.279 2.835-8.327-21.224-.995z" opacity=".2" /><path fill="#407BFF" d="M156.182 429.76c0 .009.183.013.189.022.011.033.198.06.223.084s.016.047.038.062a.13.13 0 0 0 .075-.005c.751.235 1.525.388 2.308.456a2.36 2.36 0 0 0 1.686-.331 1.1 1.1 0 0 0 .322-1.131.78.78 0 0 0-.445-.615 3.63 3.63 0 0 0-2.451.451 3.15 3.15 0 0 0 .874-2.024.85.85 0 0 0-.754-.672 1.3 1.3 0 0 0-.985.232c-.922.798-1.072 3.299-1.072 3.456v.018zm.952-.513-.182.102c.034-.84.189-2.24.748-2.71a.6.6 0 0 1 .451-.16l.101.013c.292.054.36.203.371.322a4.18 4.18 0 0 1-1.489 2.433m.127.439c1.068-.588 2.585-1.237 3.149-.975q.09.042.147.123a.37.37 0 0 1 .067.18.76.76 0 0 1-.188.724c-.448.377-1.614.358-3.175-.052" /><path fill="#263238" d="m155.97 428.679-9.926-.619a.63.63 0 0 0-.692.558v.01l-.974 7.83a1.565 1.565 0 0 0 1.447 1.66c3.468.16 5.13.063 9.5.3 2.68.161 7.965.731 11.665.377 3.7-.353 3.246-4.005 1.648-4.18-3.789-.43-8.806-3.214-11.177-5.328a2.5 2.5 0 0 0-1.491-.608" /><path fill="#000" d="M174.476 379.989a4759 4759 0 0 1-20.05-12.772c1.49-14.658 1.357-26.788-2.933-27.726-2.703-.596-9.022-.574-17.118-.386V308.93c22.72 4.665 45.328 13.466 47.049 20.525 2.404 9.936-1.797 30.968-6.948 50.534" opacity=".2" /><path fill="#EBB376" d="m197.82 430.04 8.866.161 4.415-20.452-8.859-.149z" /><path fill="#000" d="m202.247 409.602 8.859.159-2.526 11.687-8.777-.588z" opacity=".2" /><path fill="#407BFF" d="m165.624 305.964 1.427-11.456-46.307-7.625s-15.144 30.005-7.729 44.711c6.8 13.49 79.169 5.454 90.314 7.889 11.256 2.46-6.109 77.401-6.109 77.401l16.179 1.235s24.999-67.448 19.859-88.663c-2.245-9.256-40.397-21.512-67.634-23.492" /><path fill="#407BFF" d="m195.5 417.65 19.263 1.279 2.835-8.327-21.224-.993z" /><path fill="#fff" d="m195.5 417.65 19.263 1.279 2.835-8.327-21.224-.993z" opacity=".2" /><path fill="#407BFF" d="M208.018 429.759c0 .01.189.014.189.023.009.033.196.06.222.084.026.023.017.047.038.062a.12.12 0 0 0 .075-.006c.751.236 1.525.389 2.308.457a2.36 2.36 0 0 0 1.686-.331 1.1 1.1 0 0 0 .322-1.131.8.8 0 0 0-.444-.616 3.63 3.63 0 0 0-2.451.452 3.16 3.16 0 0 0 .872-2.025.84.84 0 0 0-.754-.671 1.3 1.3 0 0 0-.985.232c-.922.798-1.072 3.299-1.072 3.456v.018zm.952-.512-.182.101c.034-.839.189-2.239.748-2.709a.6.6 0 0 1 .452-.16l.101.013c.292.054.36.202.37.322a4.18 4.18 0 0 1-1.489 2.433m.127.439c1.069-.588 2.585-1.238 3.149-.976a.38.38 0 0 1 .214.304.76.76 0 0 1-.188.724c-.447.377-1.613.358-3.175-.052" /><path fill="#263238" d="m207.806 428.679-9.925-.619a.64.64 0 0 0-.62.324.7.7 0 0 0-.073.234v.01l-.974 7.83a1.565 1.565 0 0 0 1.447 1.66c3.468.16 5.13.063 9.501.3 2.681.161 7.964.731 11.664.377 3.699-.353 3.246-4.005 1.648-4.18-3.789-.43-8.806-3.214-11.177-5.328a2.48 2.48 0 0 0-1.491-.608" /><path fill="#000" d="m119.045 292.1 21.457 2.813.694-3.163-21.489-3.127zM142.979 291.77l-.26 3.145 19.829 2.726v-3.11zM163.781 294.705v2.936l2.877.385v-2.839z" /><path fill="#000" d="M178.049 244.97a280 280 0 0 0-4.137 14.46c-1.492-8.128 1.213-17.832 1.213-17.832z" opacity=".2" /><path fill="#E4897B" d="M182.707 231.016c3.873.783 5.151 30.857 5.311 34.056.161 3.2 19.002 9.425 19.002 9.425v4.386s-20.191-1.414-24.902-3.341-5.866-29.823-5.866-29.823z" /><path fill="#263238" d="M182.708 231.016c3.553 1.103 5.312 20.939 5.312 20.939l-10.987 4.43-2.727-11.027s-.78-2.24.754-6.975c2.223-6.842 7.648-7.367 7.648-7.367" /><path fill="#407BFF" d="M98.59 329.852h91.922a7.455 7.455 0 0 1 7.457 7.457v7.273H91.133v-7.273a7.456 7.456 0 0 1 7.457-7.457" /><path fill="#263238" d="M129.232 293.379h58.82a4.7 4.7 0 0 1 3.681 1.776 4.71 4.71 0 0 1 .9 3.987l-10.515 45.441h-2.43l8.96-39.823a4.7 4.7 0 0 0-.916-3.971 4.697 4.697 0 0 0-3.673-1.764h-53.627zM151.494 346.172v38.394a2.24 2.24 0 0 1-.654 1.578 2.24 2.24 0 0 1-1.578.654h-9.413a2.24 2.24 0 0 1-1.58-.652 2.23 2.23 0 0 1-.652-1.58v-38.394zM99.535 434.826a4.079 4.079 0 0 0-6.965-2.887 4.08 4.08 0 0 0-.884 4.447 4.08 4.08 0 0 0 6.654 1.324 4.08 4.08 0 0 0 1.195-2.884" /><path fill="#fff" d="M97.493 434.827a2.04 2.04 0 0 0-1.259-1.885 2.038 2.038 0 0 0-2.78 1.487 2.038 2.038 0 0 0 2 2.437 2.04 2.04 0 0 0 2.04-2.039" opacity=".2" /><path fill="#263238" d="M197.728 434.828a4.08 4.08 0 1 0-8.16 0 4.08 4.08 0 0 0 8.16 0" /><path fill="#fff" d="M195.689 434.829a2.04 2.04 0 0 0-1.259-1.885 2.044 2.044 0 0 0-2.781 1.487 2.04 2.04 0 1 0 4.04.398" opacity=".2" /><path fill="#263238" d="M147.926 416.927v-43.884h-6.749v43.884c-26.282.673-42.251 9.861-45.447 11.875a1.28 1.28 0 0 0-.595 1.087v3.647a1.29 1.29 0 0 0 .377.912 1.29 1.29 0 0 0 2.033-.272l1.559-2.734a147.8 147.8 0 0 1 42.073-6.966v7.212a1.66 1.66 0 0 0 1.03 1.542c.203.084.42.128.639.128h3.411c.219 0 .436-.044.638-.128a1.65 1.65 0 0 0 .904-.903c.084-.202.127-.419.127-.639v-7.209a147.8 147.8 0 0 1 42.072 6.967l1.558 2.733a1.3 1.3 0 0 0 1.121.652 1.29 1.29 0 0 0 1.192-.798c.065-.156.098-.324.098-.494v-3.65a1.28 1.28 0 0 0-.595-1.087c-3.195-2.014-19.165-11.203-45.446-11.875" /><path fill="#263238" d="M146.764 430.746h-4.428v8.158h4.428z" /><path fill="#000" d="M151.494 346.172h-13.877v15.952h13.877z" opacity=".2" /><path fill="#263238" d="M200.889 342.218v4.585a2.35 2.35 0 0 1-1.456 2.167 2.4 2.4 0 0 1-.9.175H90.56a2.338 2.338 0 0 1-2.341-2.342v-4.585a2.343 2.343 0 0 1 2.34-2.341h107.976a2.353 2.353 0 0 1 2.357 2.341z" /><path fill="#263238" d="M151.214 344.588H88.216s-20.013-33.289-20.013-88.29V236.1a15.04 15.04 0 0 1 15.042-15.03h26.713a15.03 15.03 0 0 1 15.03 14.609c.776 28.334 4.949 89.704 26.226 108.909" /><path fill="#fff" d="M137.357 344.586H91.066a4.79 4.79 0 0 1-4.254-2.576c-4.556-8.787-18.609-39.627-18.609-85.712V236.1a15.03 15.03 0 0 1 15.03-15.03h23.086a15.03 15.03 0 0 1 15.03 14.609c.692 25.317 4.098 77.001 19.989 101.423a4.814 4.814 0 0 1-1.515 6.792 4.8 4.8 0 0 1-2.466.692" opacity=".2" /><path fill="#000" d="M200.889 342.218v4.585a2.343 2.343 0 0 1-2.341 2.342h-48.389a3.166 3.166 0 0 1-3.165-3.166v-2.937a3.164 3.164 0 0 1 3.165-3.165h48.374c.622 0 1.219.246 1.66.685.442.438.692 1.034.696 1.656" opacity=".2" /><path fill="#407BFF" d="m88.201 219.284 4-5.243s17.102.778 17.509 7.027-21.509-1.784-21.509-1.784M76.967 234.965s1.043 27.772 0 99.479c-.037 2.418-9.048 2.337-9.675 0-2.173-8.158 0-73.354 0-73.354" /><path fill="#407BFF" d="m67.469 334.801 9.208.313v4.598l-9.208-.471z" /><path fill="#fff" d="M76.967 234.965s1.043 27.772 0 99.479c-.037 2.418-9.048 2.337-9.675 0-2.173-8.158 0-73.354 0-73.354" opacity=".2" /><path fill="#407BFF" d="M62.923 319.779c-.495-13.217-2.308-73.986 5.278-94.128 0 0 7.147-9.075 31.329-9.075s31.456 17.162 31.456 17.162 1.043 27.772 0 99.479c-.035 2.418-9.048 2.337-9.675 0-2.173-8.159 0-73.354 0-73.354l-4.836 59.311a1.45 1.45 0 0 1-1.08 1.288 1.5 1.5 0 0 1-.592.032c-7.626-1.175-41.275-5.931-50.136.136a1.11 1.11 0 0 1-1.124.095 1.1 1.1 0 0 1-.62-.944z" /><path fill="#407BFF" d="m121.492 333.574 9.208.313v4.598l-9.208-.472zM115.252 320.561l-.728 4.842s-37.519-8.185-49.676.532l-.721-5.373s9.388-8.655 51.125-.001" /><path fill="#fff" d="M62.923 319.779c-.495-13.217-2.308-73.986 5.278-94.128 0 0 7.147-9.075 31.329-9.075s31.456 17.162 31.456 17.162 1.043 27.772 0 99.479c-.035 2.418-9.048 2.337-9.675 0-2.173-8.159 0-73.354 0-73.354l-4.836 59.311a1.45 1.45 0 0 1-1.08 1.288 1.5 1.5 0 0 1-.592.032c-7.626-1.175-41.275-5.931-50.136.136a1.11 1.11 0 0 1-1.124.095 1.1 1.1 0 0 1-.62-.944z" opacity=".4" /><path fill="#000" d="M121.313 259.861s.534-18.831 9.675-26.125c0 0-11.434 4.919-9.675 26.125M93.798 218.744S82.76 224.758 81 307.461c0 0-4.798-83.372 12.798-88.717" opacity=".2" /></g></g><defs><clipPath id="a"><path fill="#fff" d="M.313.42h607v452h-607z" /></clipPath></defs></svg>
                </div>
                <div class="text-center">
                    <p class="fs-4 mt-3">Business Continuity & Disaster Management</p>
                    <h6 class="text-secondary fw-normal">
                        Simple workflows to perform actions & failovers in few clicks.
                    </h6>
                </div>
            </div>
            <footer class="text-center fixed-bottom p-1">
                @{
                    var version = Configuration.GetValue<string>("CP:Version");
                    var isCOE = Configuration.GetValue<string>("Release:isCOE");
                }
                @if (@isCOE != null)
                {
                    <small>Continuity Patrol Version <span>@version</span> | <span class="fw-bold">CoE Release</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
                }
                else
                {
                    <small>Continuity Patrol Version <span>@version</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
                }
            </footer>
        </div>
    </div>
    <div class='Notification'>
        <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
            <div class='d-flex'>
                <div class='toast-body'>
                    <span id="alertClass" class='success-toast'>
                        <i id="icon_Detail" class='cp-check toast_icon'></i>
                    </span>
                    <span id="notificationAlertmessage">

                    </span>
                </div>
                <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
            </div>
        </div>
    </div>
    @section Scripts
    {
        <partial name="_ValidationScriptsPartial" />
    }    
    <script src="~/js/lock.js"></script>
    <script src="~/js/show_hide_password.js"> </script>
    <script type="text/javascript">
        var RootUrl = '@Url.Content("~/")';
    </script>
</body>