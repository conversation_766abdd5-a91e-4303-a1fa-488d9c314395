namespace ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Update;

public class UpdateBulkImportActionResultCommandHandler : IRequestHandler<UpdateBulkImportActionResultCommand,
    UpdateBulkImportActionResultResponse>
{
    private readonly IBulkImportActionResultRepository _bulkImportActionResultRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateBulkImportActionResultCommandHandler(IMapper mapper,
        IBulkImportActionResultRepository bulkImportActionResultRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _bulkImportActionResultRepository = bulkImportActionResultRepository;
        _publisher = publisher;
    }

    public async Task<UpdateBulkImportActionResultResponse> Handle(UpdateBulkImportActionResultCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _bulkImportActionResultRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.BulkImportActionResult), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateBulkImportActionResultCommand),
            typeof(Domain.Entities.BulkImportActionResult));

        await _bulkImportActionResultRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateBulkImportActionResultResponse
        {
            //Message = Message.Update(nameof(Domain.Entities.BulkImportActionResult), eventToUpdate.Name),

            Id = eventToUpdate.ReferenceId
        };

        //await _publisher.Publish(new BulkImportActionResultUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}