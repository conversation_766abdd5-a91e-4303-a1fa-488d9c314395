﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.LoadBalancer.Events.PaginatedView;

public class LoadBalancerPaginatedEventHandler : INotificationHandler<LoadBalancerPaginatedEvent>
{
    private readonly ILogger<LoadBalancerPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public LoadBalancerPaginatedEventHandler(ILoggedInUserService userService,
        ILogger<LoadBalancerPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(LoadBalancerPaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.LoadBalancer.ToString(),
            Action = $"{ActivityType.View} {Modules.LoadBalancer}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Load Balancer viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Load Balancer viewed");
    }
}