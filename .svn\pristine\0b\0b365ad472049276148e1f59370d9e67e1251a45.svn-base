﻿using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetRunningUserList;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperation.Queries
{
    public class GetWorkflowOperationRunningUserListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IWorkflowOperationRepository> _mockWorkflowOperationRepository;
        private readonly GetWorkflowOperationRunningUserListQueryHandler _handler;

        public GetWorkflowOperationRunningUserListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockWorkflowOperationRepository = new Mock<IWorkflowOperationRepository>();
            _handler = new GetWorkflowOperationRunningUserListQueryHandler(_mockMapper.Object, _mockWorkflowOperationRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoRunningUsers()
        {
            _mockWorkflowOperationRepository
                .Setup(repo => repo.GetWorkflowOperationByRunningUsers())
                .ReturnsAsync(new List<Domain.Entities.WorkflowOperation>());

            var result = await _handler.Handle(new GetWorkflowOperationRunningUserListQuery(), CancellationToken.None);

            Assert.Empty(result);
        }

        [Fact]
        public async Task Handle_ShouldReturnDistinctRunningUsers_WhenDataIsAvailable()
        {
            var users = new List<Domain.Entities.WorkflowOperation>
            {
                new Domain.Entities.WorkflowOperation { UserName = "user1" },
                new Domain.Entities.WorkflowOperation { UserName = "user2" },
                new Domain.Entities.WorkflowOperation { UserName = "user1" },
            };
            var distinctUsers = users.DistinctBy(x => x.UserName).ToList();

            _mockWorkflowOperationRepository
                .Setup(repo => repo.GetWorkflowOperationByRunningUsers())
                .ReturnsAsync(users);

            var userVmList = new List<WorkflowOperationRunningUserListVm>
            {
                new WorkflowOperationRunningUserListVm { UserName = "user1" },
                new WorkflowOperationRunningUserListVm { UserName = "user2" },
            };
            _mockMapper
                .Setup(mapper => mapper.Map<List<WorkflowOperationRunningUserListVm>>(distinctUsers))
                .Returns(userVmList);

            var result = await _handler.Handle(new GetWorkflowOperationRunningUserListQuery(), CancellationToken.None);

            Assert.NotEmpty(result);
            Assert.Equal(2, result.Count);
            Assert.Contains(result, r => r.UserName == "user1");
            Assert.Contains(result, r => r.UserName == "user2");
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedList_WhenDataIsAvailable()
        {
            var users = new List<Domain.Entities.WorkflowOperation>
            {
                new Domain.Entities.WorkflowOperation { UserName = "user1" },
                new Domain.Entities.WorkflowOperation { UserName = "user2" }
            };

            _mockWorkflowOperationRepository
                .Setup(repo => repo.GetWorkflowOperationByRunningUsers())
                .ReturnsAsync(users);

            var userVmList = new List<WorkflowOperationRunningUserListVm>
            {
                new WorkflowOperationRunningUserListVm { UserName = "user1" },
                new WorkflowOperationRunningUserListVm { UserName = "user2" }
            };
            _mockMapper
                .Setup(mapper => mapper.Map<List<WorkflowOperationRunningUserListVm>>(users))
                .Returns(userVmList);

            var result = await _handler.Handle(new GetWorkflowOperationRunningUserListQuery(), CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("user1", result[0].UserName);
            Assert.Equal("user2", result[1].UserName);
        }
    }
}
