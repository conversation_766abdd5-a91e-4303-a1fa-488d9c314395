﻿@model ContinuityPatrol.Domain.ViewModels.ComponentTypeModel.ComponentTypeViewModel

@Html.AntiForgeryToken()
<div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel ">
    <form class="modal-content" id="createComponentForm">
        @* asp-controller="ComponentType" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data" *@
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-server-role"></i><span>Component Type Configuration</span></h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
        </div>
        <div class="modal-body mb-5">
            <div class="mb-3 ">
                <div class="form-group">
                    <label class="form-label" for="selectComponentType">Type</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-form-mapping"></i></span>
                        <select asp-for="FormTypeName" autocomplete="off" class="form-select-modal" id="selectComponentType" data-live-search="true"
                                 data-placeholder="Select Type">
                            @*  <option value="">Select</option>
                            <option value="Server">Server</option>
                            <option value="Database">Database</option>
                            <option value="Replication">Replication</option>
                            <option value="Single SignOn">Single Sign-On</option>
                            <option value="Node">Node</option> *@
                        </select>
                    </div>
                    <span asp-validation-for="FormTypeName" id="componentTypeError"></span>
                </div>
            </div>

            <div class="mb-3 ">
                <div class="form-group">
                    <label class="form-label" for="componentTypeName">Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-name"></i></span>
                        <input type="text" id="componentTypeName" class="form-control" maxlength="100"
                               placeholder="Enter Name" autocomplete="off"/>
                        @* don't chnage maxLength *@
                        <span class="input-group-text" title="Select Component Type Icon" role="button" data-bs-toggle="collapse" href="#collapseExample"
                              aria-expanded="false" aria-controls="collapseExample" id="iconContainer">
                            <i class="cp-images" id="componentTypelogo" style="color:black"></i>
                            <input type="hidden" id="componentTypeIcon" />
                            <input type="hidden" asp-for="ComponentName" id="componentName" />
                            <input type="hidden" asp-for="Logo" id="iconName" />
                            <input type="hidden" asp-for="FormTypeId" id="formTypeId" />
                        </span>

                    </div>
                    <span id="componentNameError"></span>
                </div>
                <div class="collapse mb-2" id="collapseExample">
                    <table class="table table-bordered" id="iconchange">
                    </table>
                </div>

            </div>
            <div class="form-group hideInfraComponent">
                <span class="form-label">Enable Infra Components</span>
                <div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="isServerChecked" checked disabled="disabled">
                        <label class="form-check-label" for="isServerChecked">Server </label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="isDatabaseChecked">
                        <label class="form-check-label" for="isDatabaseChecked">Database</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="isReplicationChecked">
                        <label class="form-check-label" for="isReplicationChecked">Replication</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="isClusterChecked">
                        <label class="form-check-label" for="isClusterChecked">isCluster</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="isNodeChecked">
                        <label class="form-check-label" for="isNodeChecked">isNode</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="isRacChecked">
                        <label class="form-check-label" for="isRacChecked">isRAC</label>
                    </div>                   
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="isMultiPRChecked">
                        <label class="form-check-label" for="isMultiPRChecked">multi PR</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="isMultiDRChecked">
                        <label class="form-check-label" for="isMultiDRChecked">multi DR</label>   
                    </div>
                </div>
            </div>
            <div class="mb-3 hideForReplication">
                <div class="form-group">
                    <label class="form-label" for="componentTypeVersion">Version</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-insert-left"></i></span>
                        <input type="text" id="componentTypeVersion" class="form-control" placeholder="Add Version"
                               oninput="validateComponentVersion()" autocomplete="off" onkeypress="handleKeyPress(event)" maxlength="30" />
                        <span class="input-group-text pe-0" title="Add" role="button">
                            <i id="btnSaveProfile" role="button" class="cp-circle-plus fs-5 text-primary ms-2" onclick="addVersionData()"></i>
                        </span>
                    </div>
                    <span id="componentTypeVersionError"></span>
                    <input type="hidden" id="propertiesData" asp-for="Properties" />
                    <input type="hidden" asp-for="Version" id="versionDatas" />
                </div>
            </div>

            <div class="mb-3 hideForSelectVersion">
                <div class="form-group">
                    <span class="form-label">Added Versions</span>
                    <div class="input-group" style="height: 100px;overflow-y: auto; overflow-x:hidden;">
                        <div id="selectedVersionDetails"></div>
                    </div>
                </div>
            </div>

        </div>
        <input asp-for="Id" type="hidden" id="textServerTypeId" />
        <input type="hidden" id="enableInfraServer" asp-for="IsServer" />
        <input type="hidden" id="enableInfraDatabase" asp-for="IsDatabase" />
        <input type="hidden" id="enableInfrareplication" asp-for="IsReplication" />
        <input type="hidden" id="enableInfraIsCluster" asp-for="IsCustom" />
        <input type="hidden" id="componentProperties" asp-for="ComponentProperties" />
        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" id="componentTypeSaveButton">Save</button>
            </div>
        </div>
    </form>
</div>
