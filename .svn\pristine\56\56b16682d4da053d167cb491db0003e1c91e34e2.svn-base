//functions

const resetFileInput = () => {
    fileInput.value = '';
    dataTable.ajax.reload();
};

function disableExportButton() {
    $("#exportObjects").addClass("d-none");
}

const formeoOptions = {
    editorContainer: '#formeo-editor',
    controls: controlOptions,
    events: {
        onSave: (formDta) => console.log("FORMdATA", formDta),
        onUpdate: (formData) => handleFormUpdate(formData),
    },
    disableActionButtons: true,
}

const dataEncryptDecrypt = async (data, mode) => {
    let url = mode === 'encrypt' ? formURL.EncryptForm : formURL.DecryptForm;
    let resultData = null;

    await $.ajax({
        type: "POST",
        url: RootUrl + url,
        data: { data: data, __RequestVerificationToken: gettoken() },
        dataType: 'text',
        success: function (response) {
            let parse = JSON.parse(response);
            if (parse.success) {
                resultData = parse?.data;
            } else {
                errorNotification(response);
            }
        }
    });
    return resultData;
}

function createUpdate() {
    $('#formNameError, #dynamicWidth').removeClass('w-100').addClass('w-25');
    $('#prevBtn, #formeo-editor').show();
    $('#dynamicInputName').text('Form Name').removeClass("mt-2");
    $('#dynamicHeader').text('Create Form')
    $('#ModalCreation').removeClass('modal-m').addClass('modal-fullscreen');
}

function versionValidation(value, errorMessage, errorElement) {
    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-versionError');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-versionError');
        return true;
    }
}

async function versionControlChange() {
    let selectedVersion = $('#formBuilderVersionControl option:selected').text();
    $("#formBuilderVersion").val(selectedVersion);
    let result = versionValidation($('#formBuilderVersionControl').val(), " Select alternative version", "versionRestoreError");

    if (result) {
        let data = { formId: formID, version: selectedVersion, __RequestVerificationToken: gettoken() };
        let versionChange = await postRequestWithData(RootUrl + formURL.GetFormHistrotyByID, data); //commonfunctions.js

        if (versionChange?.length) {
            let formProps = JSON.parse(versionChange[0]?.properties);
            let secondaryFormeo = new FormeoEditor({
                editorContainer: '',
                controls: controlOptions,
                formData: formProps,
                events: {
                    onUpdate: (formData) => {
                        handleFormUpdate(formData);
                        renderForm(secondaryFormeo.formData);
                    }
                },
            });
            function renderForm(formData) {
                let renderedForm = new FormeoRenderer({
                    renderContainer: document.querySelector("#primaryVersionContainer")
                });
                renderedForm.render(secondaryFormeo.formData);

                setTimeout(() => {
                    var selectElements = document.querySelectorAll('.form-select-modal-dynamic');
                    selectElements.forEach(async function (selectElement) {
                        let $this = $(selectElement);
                        $this.attr('title', '');
                        $this.select2({
                            dropdownParent: this1.find('.modal-content'), placeholder: $this.attr('placeholder')
                        });
                    });
                }, 100);
            }
        }
    }
}

async function serverType(element, type) {
    let serverTypeID = element.attr('id');
    $(`#${serverTypeID}`).empty();

    if (type) {
        let serverTypeData = await getRequestWithData(RootUrl + formURL.GetServerType, { id: type });

        if (serverTypeData?.length) {
            $(`#${serverTypeID}`).empty();
            $(`<option>`).val('').text('Select Server Type').appendTo(`#${serverTypeID}`);
            serverTypeData.forEach(option => {
                $('<option>').val(option?.name).text(option?.name).attr('idServerType', option?.id).appendTo(`#${serverTypeID}`);
            });
        }
    } else {
        $(`#${serverTypeID}`).empty().append(
            $('<option>', { value: '', text: 'Select Server Type' }),
        );
    }
}

function populateSecondDropdown(selectedRole, changedDropdown) {
    let modifiedString = changedDropdown.name.replace("ServerRole", "ServerType");
    let siblingElement = $(`select[name=${modifiedString}]`);

    if (siblingElement) {
        serverType(siblingElement, selectedRole);
    }
}

function handleIconChange(event) {
    let classList = event?.target?.classList?.[0];
    $('#componentTypelogo').removeClass().addClass(classList);
    $('.dynamicIcon').val(classList).text(classList).attr('value', classList);
}

function handleDeleteColumn(newRow, event) {
    const actionThIndex = Array.from(newRow.children).findIndex(th => th.textContent.trim() === 'Action');
    let indexx = actionThIndex - 1;
    $(newRow).find('th:eq(' + indexx + ')').remove();
    $('.data_row5').each(function () {
        $(this).find('td:eq(' + indexx + ')').remove();
    });

    if (actionThIndex === 2) {
        $('#tableDeleteButton').remove();
    }
}

function addColumn(newRow, event) {
    dynamicUpdateCondition = false;
    event.preventDefault();
    const actionThIndex = Array.from(newRow.children).findIndex(th => th.textContent.trim() === 'Action');
    const newHeaderCell = document.createElement('th');
    newHeaderCell.innerHTML = `<input type="text" style="font-weight: bold; "id="dynamicHeader${actionThIndex + 1}" value="Dynamic Header${actionThIndex + 1}" />`; // Set header text
    newRow.insertBefore(newHeaderCell, newRow.children[actionThIndex]);
    let dataRow = document.querySelector('tr.data_row5');

    if (dataRow) {
        let lastTd = dataRow.querySelector('td:last-child');

        if (lastTd) {
            let newTd = document.createElement('td');
            newTd.innerHTML = `<td><input disabled type="text" id="dynamicData${actionThIndex + 1}" placeholder="Enter ${$(`#dynamicHeader${actionThIndex + 1}`).val()}" /></td>`;
            dataRow.insertBefore(newTd, lastTd);
        }
    }

    if (actionThIndex + 1 === 2) {
        const lastTd = newRow.querySelector('th:last-child');
        lastTd.innerHTML += '<span role="button" id="tableDeleteButton" title="Delete Column" onclick="handleDeleteColumn(this.parentElement.parentElement, event)"> <i class="cp-Delete"></i> </span>';
    }
    setTimeout(() => {
        let headerRow = document?.querySelector('tr.header_row5');
        let thElements = headerRow?.querySelectorAll('th');
        thIds = [];
        thElements?.forEach(function (th) {
            let inputId = th?.querySelector('input');

            if (inputId) {
                let inputI = inputId?.id;
                thIds.push(inputI);
            }
        });
        addPlaceholderListener(`dynamicHeader${actionThIndex + 1}`, `dynamicData${actionThIndex + 1}`);
        headerRowData = document.querySelector('.header_row5');
        dataRowData = document.querySelector('.data_row5');
    }, 300)
}

function addPlaceholderListener(headerId, descriptionId) {
    const $header = $('#' + headerId);
    const $desc = $('#' + descriptionId);

    if ($header.length && $desc.length) {
        $header.on('keyup', function () {
            $desc.attr('placeholder', 'Enter ' + this.value);
        });
    }
}

function renderFormPreview(formData) {
    var renderedForm = new FormeoRende - rer({ renderContainer: document.querySelector('#form-preview-container') });
    renderedForm.render(formData[0])
}

async function forEditAndSaveAs(savebuttontext, id) {
    $("#formNameError").text("").removeClass('field-validation-error');
    document.getElementById("formeo-editor").innerHTML = "";
    let formId = { id: id, __RequestVerificationToken: gettoken() };
    let getFormById = await postRequestWithData(RootUrl + "Admin/FormBuilder/GetFormById", formId); //commonfunctions.js

    if (getFormById?.id) {
        populateForm(getFormById, savebuttontext)
    }    
    $(".save-button").text(savebuttontext);
    $("#NextModal").on("click", function () {
        $(".steps").css("display", "none")
    });
    $("#prev_btn").on("click", function () {
        $(".steps").css("display", "block")
    })
}

function populateForm(res, savebuttontext) {
    editedFormName = res.name;
    compareJSONPrimary = res.properties;
    $('#formBuilderVersion').val(res.version)

    if (savebuttontext === "Save As") {
        compareJSONPrimary = "";
        $("#formName").val("")
    }
    if (savebuttontext === "Update" || savebuttontext === "Restore") {
        $("#formName").val(res.name);
    }
    formNameEdit = res.name;
    $("#formBuilderIsPublish").val(res.isPublish);
    res.isPublish ? $('#formName').prop('disabled', true) : $('#formName').prop('disabled', false);
    res.isPublish ? $('#formName').prop('readonly', true) : $('#formName').prop('readonly', false);

    if (saveAs === "SaveAsClicked") {
        $('#formName').prop('disabled', false);
        $('#formName').prop('readonly', false);
    }
    let parsedJsonData = JSON.parse(res?.properties);
    Object.keys(parsedJsonData?.fields).forEach(function (fieldId, index) {
        let field = parsedJsonData?.fields[fieldId];

        if (field?.attrs?.name === "replication-table") {
            sourceDirectoryValue = field.attrs.sourcePath;
            destinationDirectoryValue = field.attrs.destinationPath;
        }
        if (field?.attrs?.className === "luns-custom-table") {
            lunHeaderOne = field?.attrs?.lunsHeader1;
            lunHeaderTwo = field?.attrs?.lunsHeader2;
            lunHeaderThree = field?.attrs?.lunsHeader3;
            lunHeaderFour = field?.attrs?.lunsHeader4;
            lunHeaderFive = field?.attrs?.lunsHeader5;
            lunHeaderSix = field?.attrs?.lunsHeader6;
        }
        setTimeout(() => {
            if (field?.attrs?.className === "dynamic-custom-table") {

                if (field?.attrs?.dynamicHeader) {
                    let keys = Object.keys(field?.attrs?.dynamicHeader);
                    let length = keys.length;
                    let dynamicvalues = [];

                    for (let len = 1; length > len; len++) {
                        let addColumnButton = document.getElementById('addColumnToTable');
                        if (addColumnButton) {
                            addColumnButton.click();
                        }
                    }
                    setTimeout(() => {
                        //let dynamicTable = $('.dynamic-custom-table');
                        //dynamicTable?.each(function (childindex, element) {
                        //    if (index === childindex) {
                        //        $(element).find('input').each(function (inputIndex, inputElement) {
                        //            const prefix = 'dynamicHeader';
                        //            let inputId = $(inputElement).attr('id');
                        //            if (inputId && inputId.startsWith('dynamicHeader')) {
                        //                const dynamicAttributes = Object.keys(field?.attrs?.dynamicHeader || {}).filter(key => key.startsWith(prefix));
                        //                $(inputElement).attr('id', dynamicAttributes[0]);
                        //                let value = field?.attrs?.dynamicHeader[dynamicAttributes[0]];
                        //                $(`#${dynamicAttributes[0]}`).val(value);
                        //                dynamicvalues.push(value);
                        //            }
                        //            if (inputId && inputId.startsWith('dynamicData')) {
                        //                document.getElementById(`${inputId}`).placeholder = "Enter " + dynamicvalues[0];
                        //            }
                        //        });  
                        //    }                          
                        //});                       

                        thIds.forEach(function (ids) {
                            let value = field?.attrs?.dynamicHeader[ids];
                            $(`#${ids}`).val(value);
                            dynamicvalues.push(value)
                        });
                        setTimeout(() => {
                            for (let i = 1; i <= length; i++) {
                                const $input = $(`#dynamicData${i}`);
                                if ($input.length) {
                                    $input.attr('placeholder', 'Enter ' + dynamicvalues[i - 1]);
                                }
                            }
                        }, 200);
                    }, 500)
                }
            }

            let selectTags = document.querySelectorAll('select.form-select-modal-dynamic');
            selectTags.forEach(selectTag => {
                let replacedID = selectTag?.getAttribute('id')?.replace('prev-', '');

                if (field?.id === replacedID) {
                    $(`#${field?.id}-attrs-ServerRole-ServerRole`).val(field.attrs.ServerRole);
                    let serverRoleId = $(`#${field?.id}-attrs-ServerRole-ServerRole option:selected`).attr('data-ServerRoleID');

                    if (serverRoleId) {
                        serverType($(`#${field?.id}-attrs-ServerType-ServerType`), serverRoleId);
                    }
                    setTimeout(() => {
                        $(`#${field?.id}-attrs-ServerType-ServerType`).val(field.attrs.ServerType);
                    }, 500);
                    //if (field.attrs.ServerRole) {
                    //    $(`#${field?.id}-attrs-ServerRole-ServerRole`).val(field.attrs.ServerRole);
                    //    let serverRoleId = $(`#${field?.id}-attrs-ServerRole-ServerRole option:selected`).attr('data-ServerRoleID');
                    //    if (serverRoleId) {
                    //        serverType($(`#${field?.id}-attrs-ServerType-ServerType`), serverRoleId);
                    //    }
                    //    setTimeout(() => {
                    //        $(`#${field?.id}-attrs-ServerType-ServerType`).val(field.attrs.ServerType);
                    //    }, 500);
                    //}                   
                }
            });

            let selectTagInputType = document.querySelectorAll('select.inputType');
            selectTagInputType?.forEach(selectTag => {
                let replacedID = selectTag.getAttribute('id').replace('-attrs-inputType-inputType', '');

                if (field?.id === replacedID) {
                    $(`#${replacedID}-attrs-inputType-inputType`).val(field.attrs.inputType);
                    $(`#${replacedID}-attrs-inputType-inputType`).trigger('change');
                }
            });

            //let selectedIcon = document.querySelectorAll('#dynamicIcons');
            //console.log(selectedIcon)
            //selectedIcon?.forEach(selectIcn => {
            //    let replacedID = selectIcn.getAttribute('id'); //.replace('-attrs-formDynamicIcon-formDynamicIcon', '');
            //    console.log(replacedID)
            //    //if (field?.id === replacedID) {
            //    //   
            //    //}
            //});
            let databaseSelectTags = document.querySelectorAll('select.form-select-modal-dynamic');
            databaseSelectTags.forEach(selectTag => {
                let replacedID = selectTag?.getAttribute('id')?.replace('prev-', '');

                if (field?.id === replacedID) {
                    setTimeout(() => {
                        $(`#${field?.id}-attrs-DatabaseType-DatabaseType`).val(field.attrs.DatabaseTypeID);
                    }, 1000);
                    //if (field.attrs.DatabaseTypeID) {
                    //    setTimeout(() => {
                    //        $(`#${field?.id}-attrs-DatabaseType-DatabaseType`).val(field.attrs.DatabaseTypeID);
                    //    }, 1000);
                    //}                
                }
            });
            //if (field?.attrs?.name === "@@PRdatabase") {
            //    $('.DatabaseType').val(field.attrs.DatabaseType);
            //}
        }, 1400)
    });

    let keyValuePair = Object.values(parsedJsonData.fields);

    if (keyValuePair?.length > 0) {
        keyValuePair.forEach(function (data, index) {
            if (data.meta.id === "text-input") {
                delete data.config["disabledAttrs"];
                delete data.config["lockedAttrs"];
                data.config["disabledAttrs"] = ['type', "class", "attrid"];
                data.config["lockedAttrs"] = ['name', 'required', 'placeholder', 'minlength', 'maxlength', 'encryption', 'restrict', 'disabled', 'inputType'];
                data.attrs["name"] = data.attrs.name ? data.attrs.name : "";
                //data.attrs["formDynamicIcon"] = data.attrs.formDynamicIcon ? data.attrs.formDynamicIcon : "";
                data.attrs["inputType"] = data.attrs.inputType ? data.attrs.inputType : "";
                data.attrs["required"] = data.attrs.required ? data.attrs.required : false;
                data.attrs["placeholder"] = data.attrs.placeholder ? data.attrs.placeholder : "Enter Text";
                data.attrs["minlength"] = data.attrs.minlength ? data.attrs.minlength : "";
                data.attrs["maxlength"] = data.attrs.maxlength ? data.attrs.maxlength : "";
                data.attrs["encryption"] = data.attrs.encryption ? data.attrs.encryption : false;
                //data.attrs["restrict"] = data.attrs.restrict ? data.attrs.restrict : false;
                data.attrs["disabled"] = data.attrs.disabled ? data.attrs.disabled : false;
                data.attrs["attrid"] = "textField";
                delete data.attrs["restrict"];
                delete data.attrs["customvalidation"];
                delete data.attrs["pattern"];
            }
            if (data.meta.id === "checkbox") {
                delete data.config["disabledAttrs"];
                delete data.config["lockedAttrs"];
                delete data.attrs["required"];
                data.config["disabledAttrs"] = ['type'];
                data.config["lockedAttrs"] = ['name', 'required'];
                data.attrs["name"] = data.attrs.name ? data.attrs.name : "";
            }
            if (data.meta.id === "number") {
                delete data.config["disabledAttrs"];
                delete data.config["lockedAttrs"];
                data.config["disabledAttrs"] = ['type', 'attrid'];
                data.config["lockedAttrs"] = ['name', 'placeholder', 'required', 'minlength', 'maxlength', 'disabled'];
                data.attrs["name"] = data.attrs.name ? data.attrs.name : "";
                data.attrs["placeholder"] = data.attrs.placeholder ? data.attrs.placeholder : "Enter Number";
                data.attrs["required"] = data.attrs.required ? data.attrs.required : false;
                data.attrs["minlength"] = data.attrs.minlength ? data.attrs.minlength : "";
                data.attrs["maxlength"] = data.attrs.maxlength ? data.attrs.maxlength : "";
                data.attrs["disabled"] = data.attrs.disabled ? data.attrs.disabled : false;
                data.attrs["attrid"] = "numberInput";
            }
            //if (data.meta.id === "select") {
            //    console.log(data);
            //}
            //if (data.meta.id === "radio") {
            //    console.log(data);
            //}
            if (data.meta.id === "password-input") {
                delete data.config["disabledAttrs"];
                delete data.config["lockedAttrs"];
                data.config["disabledAttrs"] = ["type", "attrid", "encryption"];
                data.config["lockedAttrs"] = ['name', 'required', 'placeholder'];
                data.attrs["attrid"] = "passwordField";
                delete data.attrs["minlength"];
                delete data.attrs["maxlength"];
            }
            if (data.meta.id === "ip-address") {
                delete data.config["disabledAttrs"];
                delete data.config["lockedAttrs"];
                data.config["disabledAttrs"] = ["type", "attrid"];
                data.config["lockedAttrs"] = ['name', 'placeholder', 'required'];
                data.attrs["attrid"] = "ipAddressField";
            }
            if (data.meta.id === "database") {
                data.attrs["name"] = data.attrs.name ? data.attrs.name : "";
                data.attrs["placeholder"] = data.attrs.placeholder ? data.attrs.placeholder : "Select Option";
                data.attrs["DatabaseType"] = data.attrs.DatabaseType ? data.attrs.DatabaseType : 'All';
                data.attrs["DatabaseTypeID"] = data.attrs.DatabaseTypeID ? data.attrs.DatabaseTypeID : '';
            }
            if (data.meta.id === "server") {
                data.attrs["name"] = data.attrs.name ? data.attrs.name : "";
                data.attrs["placeholder"] = data.attrs.placeholder ? data.attrs.placeholder : "Select Server";
                data.attrs["ServerRole"] = data.attrs.ServerRole ? data.attrs.ServerRole : "";
                data.attrs["ServerType"] = data.attrs.ServerType ? data.attrs.ServerType : "";
                data.attrs["ServerRoleID"] = data.attrs.ServerRoleID ? data.attrs.ServerRoleID : "";
                data.attrs["ServerTypeID"] = data.attrs.ServerTypeID ? data.attrs.ServerTypeID : "";
            }
            //if (data.meta.id === "replication") {
            //    console.log(data);
            //}
            //if (data.meta.id === "workflow") {
            //    console.log(data);
            //}
            //if (data.meta.id === "singlesignon") {
            //    console.log(data);
            //}
            //if (data.meta.id === "nodes") {
            //    console.log(data);
            //}
            if (data.meta.id === "lunsTable") {
                delete data.config["disabledAttrs"];
                delete data.config["lockedAttrs"];
                data.attrs["type"] = "lunstable";
                data.attrs["name"] = data.attrs.name ? data.attrs.name : 'Luns-Table';
                data.attrs["className"] = 'luns-custom-table';
                data.attrs["hideLunsTable"] = data.attrs.hideLunsTable ? data.attrs.hideLunsTable : false;
                data.config["disabledAttrs"] = ['type', 'className', 'rows', 'columns', "lunsHeader1", "lunsHeader2", "lunsHeader3", "lunsHeader4", "lunsHeader5", "lunsHeader6"];
                data.config["lockedAttrs"] = ['required', 'name', "hideLunsTable"];
            }
        });
    }

    setTimeout(() => {
        formeo = new FormeoEditor({
            editorContainer: '#formeo-editor',
            controls: controlOptions,
            formData: parsedJsonData,
            events: {
                onUpdate: (formData) => {
                    handleFormUpdate(formData);
                    if (savebuttontext === "Restore") {
                        renderForm(formeo.formData);
                    }
                }
            },
        });

        document.querySelector('#prevBtn').addEventListener('click', function () {
            $("#prevModal").modal("show")
            $('#form-preview-container').empty();
            var renderedForm = new FormeoRenderer({
                renderContainer: document.querySelector("#form-preview-container")
            });
            renderedForm.render(formeo.formData);
            populateFormbuilderDynamicFields(formeo.formData.fields)
        });
        dynamicIconChange();
    }, 250);
};

function renderForm(formData) {
    let renderedForm = new FormeoRenderer({
        renderContainer: document.querySelector("#secondaryVersionContainer")
    });
    renderedForm.render(formData);

    setTimeout(() => {
        var selectElements = document.querySelectorAll('.form-select-modal-dynamic');
        selectElements.forEach(async function (selectElement) {
            let $this = $(selectElement);
            $this.attr('title', '');
            $this.select2({
                dropdownParent: this1.find('.modal-content'), placeholder: $this.attr('placeholder')
            });
        });
    }, 100);
}

async function populateFormbuilderDynamicFields(fields) {
    for (const key in fields) {

        if (fields.hasOwnProperty(key)) {
            const field = fields[key];
            const { id, meta, config, attrs } = field;

            if (meta.id === "table") {
                let customTable = $('.custom-table');
                customTable?.each(function (index, element) {
                    if ($(element) && $(element).children().length === 0) {
                        formCustomTable(element);
                    }
                });
            }
            if (meta.id === "deploymentTable") {
                let deploymentTable = $('.deployments-table');
                deploymentTable?.each(function (index, element) {
                    if ($(element) && $(element).children().length === 0) {
                        formDeploymentsTable(element);
                    }
                });
            }
            if (meta.id === "replicationConfigTable") {
                let replicationCustomTable = $('.replication-custom-table');
                replicationCustomTable?.each(function (index, element) {
                    if ($(element) && $(element).children().length === 0) {
                        formReplicationCustomTable(element);
                    }
                });
            }
            if (meta.id === "VmPathTable") {
                let vmPathCustomTable = $('.vmPath-custom-table');
                vmPathCustomTable?.each(function (index, element) {
                    if ($(element) && $(element).children().length === 0) {
                        formVMPathCustomTable(element);
                    }
                });
            }
            if (meta.id === "lunsTable") {
                let lunsCustomTable = $('.luns-custom-table');
                lunsCustomTable?.each(function (index, element) {
                    if ($(element) && $(element).children().length === 0) {
                        formLunsCustomTable(element);
                    }
                });
            }
            if (meta.id === "dynamicTable") {
                let dynamicCustomTable = $('.dynamic-custom-table');
                dynamicCustomTable?.each(function (index, element) {
                    if ($(element) && $(element).children().length === 0) {
                        formDynamicCustomTable(element);
                    }
                });
            }
            if (meta.id === "replicationZFSTable") {
                let replicationZFSTable = $('.replicationZFSTable',);
                replicationZFSTable?.each(function (index, element) {
                    if ($(element) && $(element).children().length === 0) {
                        formReplicationZFSTable(element);
                    }
                });
            }
        }
    }
}