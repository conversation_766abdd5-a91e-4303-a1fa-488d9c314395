using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class AdPasswordJobFixture
{
    public List<AdPasswordJobListVm> AdPasswordJobListVm { get; }
    public AdPasswordJobDetailVm AdPasswordJobDetailVm { get; }
    public CreateAdPasswordJobCommand CreateAdPasswordJobCommand { get; }
    public UpdateAdPasswordJobCommand UpdateAdPasswordJobCommand { get; }

    public AdPasswordJobFixture()
    {
        var fixture = new Fixture();

        // Create sample AdPasswordJob list data
        AdPasswordJobListVm = new List<AdPasswordJobListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DomainServerId = Guid.NewGuid().ToString(),
                DomainServer = "DC01.contoso.com",
                State = "Running",
                IsSchedule = 1,
                ScheduleType = 1,
                CronExpression = "0 0 2 * * ?",
                ScheduleTime = "02:00:00",
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Node01",
                ExceptionMessage = ""
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DomainServerId = Guid.NewGuid().ToString(),
                DomainServer = "DC02.contoso.com",
                State = "Stopped",
                IsSchedule = 0,
                ScheduleType = 0,
                CronExpression = "",
                ScheduleTime = "",
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Node02",
                ExceptionMessage = "Connection timeout"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DomainServerId = Guid.NewGuid().ToString(),
                DomainServer = "DC03.contoso.com",
                State = "Scheduled",
                IsSchedule = 1,
                ScheduleType = 2,
                CronExpression = "0 0 */6 * * ?",
                ScheduleTime = "Every 6 hours",
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Node03",
                ExceptionMessage = ""
            }
        };

        // Create detailed AdPasswordJob data
        AdPasswordJobDetailVm = new AdPasswordJobDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            DomainServerId = Guid.NewGuid().ToString(),
            DomainServer = "DC01.contoso.com",
            State = "Running",
            IsSchedule = 1,
            ScheduleType = 1,
            CronExpression = "0 0 2 * * ?",
            ScheduleTime = "02:00:00",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "Node01",
            ExceptionMessage = ""
        };

        // Create command for creating AdPasswordJob
        CreateAdPasswordJobCommand = new CreateAdPasswordJobCommand
        {
            DomainServerId = Guid.NewGuid().ToString(),
            DomainServer = "DC04.contoso.com",
            State = "Scheduled",
            IsSchedule = 1,
            ScheduleType = 1,
            CronExpression = "0 0 3 * * ?",
            ScheduleTime = "03:00:00",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "Node04"
        };

        // Create command for updating AdPasswordJob
        UpdateAdPasswordJobCommand = new UpdateAdPasswordJobCommand
        {
            Id = Guid.NewGuid().ToString(),
            DomainServerId = Guid.NewGuid().ToString(),
            DomainServer = "DC05.contoso.com",
            State = "Running",
            IsSchedule = 1,
            ScheduleType = 2,
            CronExpression = "0 0 */4 * * ?",
            ScheduleTime = "Every 4 hours",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "Node05"
        };
    }
}
