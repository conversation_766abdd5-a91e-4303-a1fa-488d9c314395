﻿using ContinuityPatrol.Application.Features.Workflow.Events.CreateWorkflowExecutionEvent;

namespace ContinuityPatrol.Application.UnitTests.Features.Workflow.Events
{
    public class CreateWorkflowExecutionEventTests
    {
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly Mock<ILogger<WorkflowExecutionCreatedEventHandler>> _mockLogger;
        private readonly Mock<IWorkflowExecutionEventLogRepository> _mockWorkflowExecutionEventLogRepository;
        private readonly Mock<IWorkflowOperationGroupRepository> _mockWorkflowOperationGroupRepository;
        private readonly Mock<IWorkflowOperationRepository> _mockWorkflowOperationRepository;
        private readonly WorkflowExecutionCreatedEventHandler _handler;

        public CreateWorkflowExecutionEventTests()
        {
            _mockUserService = new Mock<ILoggedInUserService>();
            _mockLogger = new Mock<ILogger<WorkflowExecutionCreatedEventHandler>>();
            _mockWorkflowExecutionEventLogRepository = new Mock<IWorkflowExecutionEventLogRepository>();
            _mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();
            _mockWorkflowOperationRepository = new Mock<IWorkflowOperationRepository>();

            _handler = new WorkflowExecutionCreatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockWorkflowOperationGroupRepository.Object,
                _mockWorkflowExecutionEventLogRepository.Object,
                _mockWorkflowOperationRepository.Object
            );
        }

        [Fact]
        public async Task Handle_Should_Create_WorkflowExecutionEventLog_Successfully()
        {
            var eventData = new WorkflowExecutionCreatedEvent
            {
                WorkflowId = Guid.NewGuid().ToString()
            };

            var workflowOperationRunningStatus = new List<Domain.Entities.WorkflowOperation>
            {
                new Domain.Entities.WorkflowOperation
                {
                    ReferenceId = Guid.NewGuid().ToString()
                }
            };

            var workflowOperationGroup = new List<Domain.Entities.WorkflowOperationGroup>
            {
                new Domain.Entities.WorkflowOperationGroup
                {
                    ReferenceId = Guid.NewGuid().ToString(),
                    WorkflowId = eventData.WorkflowId,
                    WorkflowOperationId = workflowOperationRunningStatus[0].ReferenceId,
                    InfraObjectId = "InfraObj123"
                }
            };

            _mockWorkflowOperationRepository.Setup(repo => repo.GetWorkflowOperationByRunningStatus())
                .ReturnsAsync(workflowOperationRunningStatus);

            _mockWorkflowOperationGroupRepository.Setup(repo => repo.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>()))
                .ReturnsAsync(workflowOperationGroup);

            _mockUserService.Setup(s => s.LoginName).Returns("TestUser");
            _mockUserService.Setup(s => s.CompanyId).Returns(Guid.NewGuid().ToString());

            await _handler.Handle(eventData, CancellationToken.None);

            _mockWorkflowExecutionEventLogRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.WorkflowExecutionEventLog>()), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation(It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Not_Create_EventLog_If_No_WorkflowOperationGroup_Found()
        {
            var eventData = new WorkflowExecutionCreatedEvent
            {
                WorkflowId = Guid.NewGuid().ToString()
            };

            var workflowOperationRunningStatus = new List<Domain.Entities.WorkflowOperation>
            {
                new Domain.Entities.WorkflowOperation
                {
                    ReferenceId = Guid.NewGuid().ToString()
                }
            };

            _mockWorkflowOperationRepository.Setup(repo => repo.GetWorkflowOperationByRunningStatus())
                .ReturnsAsync(workflowOperationRunningStatus);

            _mockWorkflowOperationGroupRepository.Setup(repo => repo.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>()))
                .ReturnsAsync(new List<Domain.Entities.WorkflowOperationGroup>());

            await _handler.Handle(eventData, CancellationToken.None);

            _mockWorkflowExecutionEventLogRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.WorkflowExecutionEventLog>()), Times.Never);
        }

        [Fact]
        public async Task Handle_Should_Log_Information_When_Workflow_Is_Updated()
        {
            var eventData = new WorkflowExecutionCreatedEvent
            {
                WorkflowId = Guid.NewGuid().ToString()
            };

            var workflowOperationRunningStatus = new List<Domain.Entities.WorkflowOperation>
            {
                new Domain.Entities.WorkflowOperation
                {
                    ReferenceId = Guid.NewGuid().ToString()
                }
            };

            var workflowOperationGroup = new List<Domain.Entities.WorkflowOperationGroup>
            {
                new Domain.Entities.WorkflowOperationGroup
                {
                    ReferenceId = Guid.NewGuid().ToString(),
                    WorkflowId = eventData.WorkflowId,
                    WorkflowOperationId = workflowOperationRunningStatus[0].ReferenceId,
                    InfraObjectId = "InfraObj123"
                }
            };

            _mockWorkflowOperationRepository.Setup(repo => repo.GetWorkflowOperationByRunningStatus())
                .ReturnsAsync(workflowOperationRunningStatus);

            _mockWorkflowOperationGroupRepository.Setup(repo => repo.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>()))
                .ReturnsAsync(workflowOperationGroup);

            _mockUserService.Setup(s => s.LoginName).Returns("TestUser");
            _mockUserService.Setup(s => s.CompanyId).Returns(Guid.NewGuid().ToString());

            await _handler.Handle(eventData, CancellationToken.None);

            _mockLogger.Verify(logger => logger.LogInformation(It.Is<string>(s => s.Contains(eventData.WorkflowId))), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Log_Information_When_WorkflowExecutionEventLog_Is_Created()
        {
            var eventData = new WorkflowExecutionCreatedEvent
            {
                WorkflowId = Guid.NewGuid().ToString()
            };

            var workflowOperationRunningStatus = new List<Domain.Entities.WorkflowOperation>
            {
                new Domain.Entities.WorkflowOperation
                {
                    ReferenceId = Guid.NewGuid().ToString()
                }
            };

            var workflowOperationGroup = new List<Domain.Entities.WorkflowOperationGroup>
            {
                new Domain.Entities.WorkflowOperationGroup
                {
                   ReferenceId = Guid.NewGuid().ToString(),
                   WorkflowId = eventData.WorkflowId,
                   WorkflowOperationId = workflowOperationRunningStatus[0].ReferenceId,
                   InfraObjectId = "InfraObj123"
                }
            };

            _mockWorkflowOperationRepository.Setup(repo => repo.GetWorkflowOperationByRunningStatus())
                .ReturnsAsync(workflowOperationRunningStatus);

            _mockWorkflowOperationGroupRepository.Setup(repo => repo.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>()))
                .ReturnsAsync(workflowOperationGroup);

            _mockUserService.Setup(s => s.LoginName).Returns("TestUser");
            _mockUserService.Setup(s => s.CompanyId).Returns(Guid.NewGuid().ToString());

            await _handler.Handle(eventData, CancellationToken.None);

            _mockWorkflowExecutionEventLogRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.WorkflowExecutionEventLog>()), Times.Once);
        }
    }
}
