﻿using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.BusinessService.Commands.Create;

public class CreateBusinessServiceCommandValidator : AbstractValidator<CreateBusinessServiceCommand>
{
    private readonly IBusinessServiceRepository _businessServiceRepository;

    public CreateBusinessServiceCommandValidator(IBusinessServiceRepository businessServiceRepository)
    {
        _businessServiceRepository = businessServiceRepository;
        RuleFor(e => e)
            .MustAsync(IsValidGuid)
            .WithMessage("Invalid company id");

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s\-]?)([a-zA-Z\d]+[_\s\-])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.Description)
            .MaximumLength(250).WithMessage("Operational Service {PropertyName} Maximum 250 characters.")
            .Matches(@"^\s*([a-zA-Z]+[\s_]?)([a-zA-Z\d]+[\s_])*[a-zA-Z\d]+\s*$")
            .WithMessage("Operational Service {PropertyName} contains invalid characters.")
            .When(p => p.Description.IsNotNullOrWhiteSpace());

        RuleFor(p => p.CompanyId)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull();

        RuleFor(p => p.CompanyName)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .Matches(@"^([1-9][a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]|[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d])$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();


        RuleFor(p => p.Priority).NotEmpty().WithMessage("select {PropertyName}").NotNull()
            .InclusiveBetween(1, 3).WithMessage("Please enter number only.");


        RuleFor(p => p.SiteProperties)
            .NotEmpty().WithMessage("Operational Service {PropertyName} is required")
            .NotNull().WithMessage("{PropertyName} should not be null")
            .Must(prop => IsValidJsonObject(prop))
            .WithMessage("{PropertyName} must be a valid json string.");

        RuleFor(e => e)
            .MustAsync(BusinessServiceNameUnique)
            .WithMessage("A same name already exists.");
    }

    private async Task<bool> BusinessServiceNameUnique(CreateBusinessServiceCommand e, CancellationToken token)
    {
        return !await _businessServiceRepository.IsBusinessServiceNameUnique(e.Name);
    }

    private Task<bool> IsValidGuid(CreateBusinessServiceCommand p, CancellationToken token)
    {
        Guard.Against.InvalidGuidOrEmpty(p.CompanyId, "company id");
        return Task.FromResult(true);
    }

    private static bool IsValidJsonObject(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                var obj = JsonConvert.DeserializeObject(properties);
                return true;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }
}