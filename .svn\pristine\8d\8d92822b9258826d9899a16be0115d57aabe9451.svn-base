using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SmtpConfigurationFixture : IDisposable
{
    public List<SmtpConfiguration> SmtpConfigurationPaginationList { get; set; }
    public List<SmtpConfiguration> SmtpConfigurationList { get; set; }
    public SmtpConfiguration SmtpConfigurationDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SmtpConfigurationFixture()
    {
        var fixture = new Fixture();

        SmtpConfigurationList = fixture.Create<List<SmtpConfiguration>>();

        SmtpConfigurationPaginationList = fixture.CreateMany<SmtpConfiguration>(20).ToList();

        SmtpConfigurationPaginationList.ForEach(x => x.CompanyId = CompanyId);

        SmtpConfigurationList.ForEach(x => x.CompanyId = CompanyId);

        SmtpConfigurationDto = fixture.Create<SmtpConfiguration>();

        SmtpConfigurationDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
