﻿using ContinuityPatrol.Application.Features.MssqlNativeLogShippingMonitorLog.Commands.Create;
using ContinuityPatrol.Application.Features.MssqlNativeLogShippingMonitorLog.Queries.GetByType;
using ContinuityPatrol.Application.Features.MssqlNativeLogShippingMonitorLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MssqlNativeLogShippingMonitorLog.Queries.GetList;
using ContinuityPatrol.Application.Features.MssqlNativeLogShippingMonitorLog.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IMssqlNativeLogShippingMonitorLogService
{
    Task<BaseResponse> CreateMssqlNativeLogShippingMonitorLog(CreateMssqlNativeLogShippingMonitorLogCommand createMssqlNativeLogShippingMonitorLogCommand);
    Task<List<MssqlNativeLogShippingMonitorLogListVm>> GetAllMssqlNativeLogShippingMonitorLog();
    Task<MssqlNativeLogShippingMonitorLogDetailVm> GetMssqlNativeLogShippingMonitorLogById(string id);
    Task<PaginatedResult<MssqlNativeLogShippingMonitorLogPaginatedListVm>> GetPaginatedMssqlNativeLogShippingMonitorLogMonitorLogs(GetMssqlNativeLogShippingMonitorLogPaginatedListQuery query);
    Task<List<MssqlNativeLogShippingMonitorLogDetailByTypeVm>> GetMssqlNativeLogShippingMonitorLogByType(string type);
}