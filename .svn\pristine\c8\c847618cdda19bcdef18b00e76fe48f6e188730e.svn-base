using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RsyncMonitorStatusRepositoryTests : IClassFixture<RsyncMonitorStatusFixture>
{
    private readonly RsyncMonitorStatusFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly RsyncMonitorStatusRepository _repository;

    public RsyncMonitorStatusRepositoryTests(RsyncMonitorStatusFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new RsyncMonitorStatusRepository(_dbContext);
    }

    #region GetRsyncStatusByInfraObjectIdAsync Tests

    [Fact]
    public async Task GetRsyncStatusByInfraObjectIdAsync_ShouldReturnMatchingRecord_WhenRecordExists()
    {
        // Arrange
        var infraObjectId = "abea4151-6ee0-4f6b-9fc0-2fa5c961d956";
        var matchingStatus = _fixture.CreateRsyncMonitorStatusWithInfraObjectId(infraObjectId);
        var nonMatchingStatus = _fixture.CreateRsyncMonitorStatusWithInfraObjectId("vbea4151-6ee0-4f6b-9fc0-2fa5c961d956");

        _dbContext.RsyncMonitorStatus.Add(matchingStatus);
        _dbContext.RsyncMonitorStatus.Add(nonMatchingStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRsyncStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal(matchingStatus.Id, result.Id);
        Assert.Equal(matchingStatus.Type, result.Type);
        Assert.Equal(matchingStatus.WorkflowId, result.WorkflowId);
    }

    [Fact]
    public async Task GetRsyncStatusByInfraObjectIdAsync_ShouldReturnNull_WhenNoMatchingRecord()
    {
        // Arrange
        var status = _fixture.CreateRsyncMonitorStatusWithInfraObjectId("abea4151-6ee0-4f6b-9fc0-2fa5c961d956");
        _dbContext.RsyncMonitorStatus.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRsyncStatusByInfraObjectIdAsync("44245100-3539-4d73-a29c-9fa4311527a6");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetRsyncStatusByInfraObjectIdAsync_ShouldReturnFirstMatch_WhenMultipleRecordsExist()
    {
        // Arrange
        var infraObjectId = "abea4151-6ee0-4f6b-9fc0-2fa5c961d956";
        var firstStatus = _fixture.CreateRsyncMonitorStatusWithInfraObjectId(infraObjectId);
        var secondStatus = _fixture.CreateRsyncMonitorStatusWithInfraObjectId(infraObjectId);

        _dbContext.RsyncMonitorStatus.Add(firstStatus);
        _dbContext.RsyncMonitorStatus.Add(secondStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRsyncStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should return one of the matching records
        Assert.True(result.Id == firstStatus.Id || result.Id == secondStatus.Id);
    }

    [Fact]
    public async Task GetRsyncStatusByInfraObjectIdAsync_ShouldThrowArgumentNullException_WhenInfraObjectIdIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.GetRsyncStatusByInfraObjectIdAsync(null));
    }

    

    [Fact]
    public async Task GetRsyncStatusByInfraObjectIdAsync_ShouldReturnNull_WhenValidGuidButNoMatch()
    {
        // Arrange
        var validGuid = Guid.NewGuid().ToString();
        var status = _fixture.CreateRsyncMonitorStatusWithInfraObjectId("DIFFERENT_GUID");
        _dbContext.RsyncMonitorStatus.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRsyncStatusByInfraObjectIdAsync(validGuid);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddRsyncMonitorStatus_WhenValidEntity()
    {
        // Arrange
        var monitorStatus = _fixture.RsyncMonitorStatusDto;
        monitorStatus.Type = "Rsync";
        monitorStatus.InfraObjectId = "INFRA_TEST_001";
        monitorStatus.InfraObjectName = "Test Infrastructure Object";
        monitorStatus.WorkflowId = "WORKFLOW_TEST_001";
        monitorStatus.WorkflowName = "Test Workflow";
        monitorStatus.ConfiguredRPO = "4 hours";
        monitorStatus.DataLagValue = "30 minutes";
        monitorStatus.Threshold = "2 hours";
        monitorStatus.Properties = "{\"property1\":\"value1\",\"property2\":\"value2\"}";

        // Act
        var result = await _repository.AddAsync(monitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorStatus.Type, result.Type);
        Assert.Equal(monitorStatus.InfraObjectId, result.InfraObjectId);
        Assert.Equal(monitorStatus.InfraObjectName, result.InfraObjectName);
        Assert.Equal(monitorStatus.WorkflowId, result.WorkflowId);
        Assert.Equal(monitorStatus.WorkflowName, result.WorkflowName);
        Assert.Equal(monitorStatus.ConfiguredRPO, result.ConfiguredRPO);
        Assert.Equal(monitorStatus.DataLagValue, result.DataLagValue);
        Assert.Equal(monitorStatus.Threshold, result.Threshold);
        Assert.Equal(monitorStatus.Properties, result.Properties);
        Assert.Single(_dbContext.RsyncMonitorStatus);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var monitorStatus = _fixture.RsyncMonitorStatusDto;
        _dbContext.RsyncMonitorStatus.Add(monitorStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(monitorStatus.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorStatus.Id, result.Id);
        Assert.Equal(monitorStatus.InfraObjectId, result.InfraObjectId);
        Assert.Equal(monitorStatus.Type, result.Type);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var monitorStatus = _fixture.RsyncMonitorStatusDto;
        _dbContext.RsyncMonitorStatus.Add(monitorStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(monitorStatus.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorStatus.ReferenceId, result.ReferenceId);
        Assert.Equal(monitorStatus.InfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var monitorStatuses = _fixture.RsyncMonitorStatusList;
        _dbContext.RsyncMonitorStatus.AddRange(monitorStatuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorStatuses.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntitiesExist()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_WhenExists()
    {
        // Arrange
        var monitorStatus = _fixture.RsyncMonitorStatusDto;
        _dbContext.RsyncMonitorStatus.Add(monitorStatus);
        await _dbContext.SaveChangesAsync();

        var updatedType = "Updated_Rsync";
        var updatedDataLagValue = "1 hour";
        monitorStatus.Type = updatedType;
        monitorStatus.DataLagValue = updatedDataLagValue;

        // Act
        var result = await _repository.UpdateAsync(monitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(updatedType, result.Type);
        Assert.Equal(updatedDataLagValue, result.DataLagValue);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity_WhenExists()
    {
        // Arrange
        var monitorStatus = _fixture.RsyncMonitorStatusDto;
        _dbContext.RsyncMonitorStatus.Add(monitorStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(monitorStatus);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(monitorStatus.Id);
        Assert.Null(deletedEntity);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleEntities()
    {
        // Arrange
        var monitorStatuses = new List<RsyncMonitorStatus>
        {
            _fixture.CreateRsyncMonitorStatusWithInfraObjectId("INFRA_001"),
            _fixture.CreateRsyncMonitorStatusWithInfraObjectId("INFRA_002"),
            _fixture.CreateRsyncMonitorStatusWithInfraObjectId("INFRA_003")
        };

        // Act
        var result = await _repository.AddRangeAsync(monitorStatuses);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count());

        var savedEntities = await _repository.ListAllAsync();
        Assert.Equal(3, savedEntities.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    [Fact]
    public async Task Repository_ShouldHandleComplexProperties()
    {
        // Arrange
        var monitorStatus = _fixture.CreateRsyncMonitorStatusWithProperties(
            type: "Complex_Rsync_Type",
            infraObjectId: "COMPLEX_INFRA_001",
            infraObjectName: "Complex Infrastructure Object with Special Characters @#$%",
            workflowId: "COMPLEX_WORKFLOW_001",
            workflowName: "Complex Workflow with Special Characters @#$%",
            configuredRPO: "8 hours",
            dataLagValue: "45 minutes",
            threshold: "3 hours",
            properties: "{\"complexProperty1\":\"complexValue1\",\"complexProperty2\":\"complexValue2\",\"nestedObject\":{\"key\":\"value\"}}"
        );

        // Act
        var result = await _repository.AddAsync(monitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorStatus.Type, result.Type);
        Assert.Equal(monitorStatus.InfraObjectId, result.InfraObjectId);
        Assert.Equal(monitorStatus.InfraObjectName, result.InfraObjectName);
        Assert.Equal(monitorStatus.WorkflowId, result.WorkflowId);
        Assert.Equal(monitorStatus.WorkflowName, result.WorkflowName);
        Assert.Equal(monitorStatus.ConfiguredRPO, result.ConfiguredRPO);
        Assert.Equal(monitorStatus.DataLagValue, result.DataLagValue);
        Assert.Equal(monitorStatus.Threshold, result.Threshold);
        Assert.Equal(monitorStatus.Properties, result.Properties);
    }

    [Theory]
    [InlineData("Rsync")]
    [InlineData("RoboCopy")]
    [InlineData("DataSync")]
    [InlineData("FastCopy")]
    public async Task Repository_ShouldHandleDifferentTypes(string type)
    {
        // Arrange
        var monitorStatus = _fixture.CreateRsyncMonitorStatusWithType(type);

        // Act
        var result = await _repository.AddAsync(monitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(type, result.Type);
    }

    [Theory]
    [InlineData("1 hour")]
    [InlineData("2 hours")]
    [InlineData("4 hours")]
    [InlineData("8 hours")]
    [InlineData("24 hours")]
    public async Task Repository_ShouldHandleDifferentConfiguredRPOs(string configuredRPO)
    {
        // Arrange
        var monitorStatus = _fixture.CreateRsyncMonitorStatusWithProperties(configuredRPO: configuredRPO);

        // Act
        var result = await _repository.AddAsync(monitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(configuredRPO, result.ConfiguredRPO);
    }

    [Theory]
    [InlineData("5 minutes")]
    [InlineData("15 minutes")]
    [InlineData("30 minutes")]
    [InlineData("1 hour")]
    [InlineData("2 hours")]
    public async Task Repository_ShouldHandleDifferentDataLagValues(string dataLagValue)
    {
        // Arrange
        var monitorStatus = _fixture.CreateRsyncMonitorStatusWithProperties(dataLagValue: dataLagValue);

        // Act
        var result = await _repository.AddAsync(monitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataLagValue, result.DataLagValue);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.RsyncMonitorStatus.RemoveRange(_dbContext.RsyncMonitorStatus);
        await _dbContext.SaveChangesAsync();
    }
}
