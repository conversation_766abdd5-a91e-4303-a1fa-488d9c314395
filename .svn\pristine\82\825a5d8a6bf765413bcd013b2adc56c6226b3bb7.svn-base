using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AlertNotificationFixture : IDisposable
{
    public List<AlertNotification> AlertNotificationPaginationList { get; set; }
    public List<AlertNotification> AlertNotificationList { get; set; }
    public AlertNotification AlertNotificationDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public AlertNotificationFixture()
    {
        var fixture = new Fixture();

        AlertNotificationList = fixture.Create<List<AlertNotification>>();

        AlertNotificationPaginationList = fixture.CreateMany<AlertNotification>(20).ToList();

        AlertNotificationDto = fixture.Create<AlertNotification>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
