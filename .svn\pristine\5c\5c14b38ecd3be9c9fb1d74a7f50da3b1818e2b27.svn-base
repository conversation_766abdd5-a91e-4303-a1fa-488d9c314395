﻿using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceHealthStatus.Commands;

public class CreateBusinessServiceHealthStatusTests : IClassFixture<BusinessServiceHealthStatusFixture>
{
    private readonly BusinessServiceHealthStatusFixture _businessServiceHealthStatusFixture;

    private readonly Mock<IBusinessServiceHealthStatusRepository> _businessServiceHealthStatusRepositoryMock;

    private readonly CreateBusinessServiceHealthStatusCommandHandler _handler;

    public CreateBusinessServiceHealthStatusTests(BusinessServiceHealthStatusFixture businessServiceHealthStatusFixture)
    {
        _businessServiceHealthStatusFixture = businessServiceHealthStatusFixture;
    
        _businessServiceHealthStatusRepositoryMock = BusinessServiceHealthStatusRepositoryMocks.CreateBusinessServiceHealthStatusRepository(_businessServiceHealthStatusFixture.BusinessServiceHealthStatusList);
        
        _handler = new CreateBusinessServiceHealthStatusCommandHandler(_businessServiceHealthStatusRepositoryMock.Object, _businessServiceHealthStatusFixture.Mapper);
    }

    [Fact]
    public async Task Handle_IncreaseBusinessServiceHealthStatusCount_When_BusinessServiceHealthStatus_Created()
    {
        await _handler.Handle(_businessServiceHealthStatusFixture.CreateBusinessServiceHealthStatusCommand, CancellationToken.None);

        var result = await _businessServiceHealthStatusRepositoryMock.Object.ListAllAsync();

        result.Count.ShouldBe(_businessServiceHealthStatusFixture.BusinessServiceHealthStatusList.Count);
    }

    [Fact]
    public async Task Handle_Return_BusinessServiceHealthStatusResponse_When_BusinessServiceHealthStatus_Created()
    {
        var result = await _handler.Handle(_businessServiceHealthStatusFixture.CreateBusinessServiceHealthStatusCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateBusinessServiceHealthStatusResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_businessServiceHealthStatusFixture.CreateBusinessServiceHealthStatusCommand, CancellationToken.None);

        _businessServiceHealthStatusRepositoryMock.Verify(x=>x.AddAsync(It.IsAny<Domain.Entities.BusinessServiceHealthStatus>()), Times.Once);
    }
}