﻿
@{
    ViewData["Title"] = "UserActivityReport";
   
}
@using DevExpress.AspNetCore
@using ContinuityPatrol.Web.Areas.Report.ReportTemplate

<style>
    .form-group {
        margin-left: 550px;
        margin-top: 60px;
    }
</style>
@section Styles
    {
    <link href="~/css/viewer.part.bundle.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/thirdparty.bundle.css" rel="stylesheet" asp-append-version="true" />
    }
@section HeaderScripts
    {
    <script src="~/js/common/thirdparty.bundle.js" asp-append-version="true"></script>
    <script src="~/js/common/viewer.part.bundle.js" asp-append-version="true"></script>
   }


<div class="card card-custom gutter-b">
    <div class="card-body p-0">
        <div id="reportContainer">
            @Html.DevExpress().WebDocumentViewer("UserActivityDocumentViewer").Height("1150px").Bind(new UserActivityReport(ViewData["UserActivityReportData"].ToString()))

        </div>
    </div>
</div>

@*<div class="row mt-3">
    <div class="col">
        <img src="/img/logo/cplogo.svg" height="35" />
    </div>
    <div class="col text-end">
        <img src="/img/logo/pts_logo.png" height="35" />
    </div>
    <div class="col-12">
        <div class="bg-secondary rounded-0 text-light mt-1">
            <h6 class="Report-Header text-center">
                User Activity Report
            </h6>
        </div>
    </div>
</div>
<div class="card">
    <div>
        <div class="mt-3">
            <div class="rounded card bg-light">
                <div class="card-header text-primary fw-bold border-bottom">
                   Report Details
                </div>
                <div class="p-0 card-body">
                    <div class="rounded-0 py-2">
                        <div class="row">
                            <div class="col border-end border-secondary-subtle">
                                <div class="d-flex justify-content-between px-3 py-2 border-0 list-group-item">
                                    <div class="me-auto d-flex align-items-center">
                                        <i class="cp-business-service"></i>
                                        <span class="ms-1 align-middle">From Date</span>
                                    </div>
                                    01-02-2022
                                </div>
                            </div>
                            <div class="col">
                                <div class="d-flex justify-content-between px-3 py-2 border-0 list-group-item">
                                    <div class="me-auto d-flex align-items-center">
                                        <i class="cp-business-service"></i>
                                        <span class="ms-1 align-middle">To Date</span>
                                    </div>
                                    01-02-2022
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
       
        <div class="row">
            <div class="col">
                <div class="rounded card bg-light h-100">
                    <div class="card-header text-primary fw-bold border-bottom">
                        User Details
                    </div>
                    <div class="p-0 card-body">
                        <div class="rounded-0 list-group">
                            <li class="d-flex justify-content-between px-3 py-2 border-top-0 list-group-item">
                                <div class="me-auto">
                                    <span class="ms-1 ">
                                        Username
                                    </span>
                                </div>
                                Login Duration (Hrs)
                            </li>
                            <li class="d-flex justify-content-between px-3 py-2  list-group-item">
                                <div class="me-auto">
                                    <span class="ms-1 ">
                                        Srivignesh
                                    </span>
                                </div>
                                120
                            </li>
                            <li class="d-flex justify-content-between px-3 py-2  list-group-item">
                                <div class="me-auto">
                                    <span class="ms-1 ">
                                        Srivignesh
                                    </span>
                                </div>
                                120
                            </li>
                            <li class="d-flex justify-content-between px-3 py-2  list-group-item">
                                <div class="me-auto">
                                    <span class="ms-1 ">
                                        Srivignesh
                                    </span>
                                </div>
                                120
                            </li>
                            <li class="d-flex justify-content-between px-3 py-2 border-0 list-group-item">
                                <div class="me-auto">
                                    <span class="ms-1 ">
                                        Srivignesh
                                    </span>
                                </div>
                                120
                            </li>


                        </div>
                    </div>
                </div>


            </div>
            <div class="col">
                <div class="rounded card h-100">
                    <div class="card-body">                
                            <div id="chartdiv" class="w-100 h-100"></div>              
                    </div>
                </div>

            </div>
           
            
        </div>
        <div class="mt-3 prebuildreportcustom">
            <div>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="bg-light">
                            <tr>
                                <th>Sr.No</th>
                                <th>Login Name</th>
                                <th>Action Type</th>
                                <th>Userhost Address</th>
                                <th>Activity Details</th>
                                <th>Activity Date</th>
                            </tr>
                        </thead>
                        <tbody id="tableDataone">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="~/js/report-charts/user_activity_report.js"></script>*@