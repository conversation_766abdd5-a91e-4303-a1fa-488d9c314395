using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixUsersModel;
using ContinuityPatrol.Shared.Core.Wrapper;


namespace ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetPaginatedList;

public class GetApprovalMatrixUsersPaginatedListQueryHandler : IRequestHandler<GetApprovalMatrixUsersPaginatedListQuery, PaginatedResult<ApprovalMatrixUsersListVm>>
{
    private readonly IApprovalMatrixUsersRepository _approvalMatrixUsersRepository;
    private readonly IMapper _mapper;

    public GetApprovalMatrixUsersPaginatedListQueryHandler(IMapper mapper, IApprovalMatrixUsersRepository approvalMatrixUsersRepository)
    {
        _mapper = mapper;
        _approvalMatrixUsersRepository = approvalMatrixUsersRepository;
    }

    public async Task<PaginatedResult<ApprovalMatrixUsersListVm>> Handle(GetApprovalMatrixUsersPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new ApprovalMatrixUsersFilterSpecification(request.SearchString);

        var queryable = request.Type.IsNotNullOrWhiteSpace()
            ? await _approvalMatrixUsersRepository
            .GetApprovalMatrixUserByType(request.Type, request.PageNumber,request.PageSize, productFilterSpec,request.SortColumn,request.SortOrder)
            : await _approvalMatrixUsersRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var approvalMatrixUsersList = _mapper.Map<PaginatedResult<ApprovalMatrixUsersListVm>>(queryable);

        return approvalMatrixUsersList;
    }
}
