﻿using ContinuityPatrol.Application.Features.PluginManager.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.PluginManagerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.PluginManager.Queries;

public class GetPluginManagerPaginatedListQueryHandlerTests : IClassFixture<PluginManagerFixture>
{
    private readonly GetPluginManagerPaginatedListQueryHandler _handler;

    private readonly Mock<IPluginManagerRepository> _mockPluginManagerRepository;

    public GetPluginManagerPaginatedListQueryHandlerTests(PluginManagerFixture pluginManagerFixture)
    {
        var pluginManagerFixture1 = pluginManagerFixture;

        pluginManagerFixture1.PluginManagers[0].Name = "BMW";
        pluginManagerFixture1.PluginManagers[0].Properties = "{\"Name\": \"admin\", \"password\": \"Cpadmin@1234\"}";
        pluginManagerFixture1.PluginManagers[0].Description = "Status";
        pluginManagerFixture1.PluginManagers[0].Version = "*******";

        pluginManagerFixture1.PluginManagers[1].Name = "BMW123";
        pluginManagerFixture1.PluginManagers[1].Properties = "{\"Test\": \"Common\", \"password\": \"Admin@321\"}";
        pluginManagerFixture1.PluginManagers[1].Description = "Pending";
        pluginManagerFixture1.PluginManagers[1].Version = "6.0.2";

        _mockPluginManagerRepository = PluginManagerRepositoryMocks.GetPaginatedPluginManagerRepository(pluginManagerFixture1.PluginManagers);

        _handler = new GetPluginManagerPaginatedListQueryHandler(pluginManagerFixture1.Mapper, _mockPluginManagerRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetPluginManagerPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "BMW" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<PluginManagerListVm>>();

        result.TotalCount.ShouldBe(2);
    }

    [Fact]
    public async Task Handle_Return_PaginatedPluginManagers_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetPluginManagerPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "BMW" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<PluginManagerListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<PluginManagerListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("BMW");

        result.Data[0].Description.ShouldBe("Status");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetPluginManagerPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<PluginManagerListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_PluginManagers_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetPluginManagerPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "pluginname=BMW;version=*******;description=Status;properties={\"Name\": \"admin\", \"password\": \"Cpadn=min@1234\"}" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<PluginManagerListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("BMW");

        result.Data[0].Properties.ShouldBe("{\"Name\": \"admin\", \"password\": \"Cpadmin@1234\"}");

        result.Data[0].Description.ShouldBe("Status");

        result.Data[0].Version.ShouldBe("*******");

    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetPluginManagerPaginatedListQuery(), CancellationToken.None);

        _mockPluginManagerRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}