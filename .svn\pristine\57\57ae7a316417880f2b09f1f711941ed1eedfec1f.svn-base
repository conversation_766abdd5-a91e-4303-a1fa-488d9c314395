﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class InfraReplicationMappingRepository : BaseRepository<InfraReplicationMapping>,
    IInfraReplicationMappingRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public InfraReplicationMappingRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<List<InfraReplicationMapping>> GetInfraReplicationMappingByDatabaseId(string databaseId,
        string replicationMasterId)
    {
        return _dbContext.InfraReplicationMappings.Active()
            .Where(e => e.DatabaseId.Equals(databaseId) && e.ReplicationMasterId.Equals(replicationMasterId) &&
                        e.IsActive)
            .ToListAsync();
    }

    public async Task<List<InfraReplicationMapping>> GetInfraReplicationMappingByType(string type)
    {
        var result = await _dbContext.InfraReplicationMappings.Active()
            .Where(x => x.Type.Equals(type) && x.IsActive).ToListAsync();

        return result;
    }

    public async Task<List<InfraReplicationMapping>> GetTypeByDatabaseIdAndReplicationMasterId(string databaseId,
        string replicationMasterId, string type)
    {
        var result = await _dbContext.InfraReplicationMappings.Active()
            .Where(x => x.DatabaseId.Equals(databaseId) && x.ReplicationMasterId.Equals(replicationMasterId) &&
                        x.Type.Equals(type) && x.IsActive).ToListAsync();

        return result;
    }

    public override Task<InfraReplicationMapping> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilter(company =>
                    company.ReferenceId.Equals(id) && company.ReferenceId.Equals(_loggedInUserService.CompanyId)).Result
                .SingleOrDefault());
    }
    public override async Task<PaginatedResult<InfraReplicationMapping>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<InfraReplicationMapping> specification, string sortColumn, string sortOrder)
    {
        var result = await FilterRequiredField(Entities.Specify(specification).DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
 
        return result;
    }
    public async Task<List<InfraReplicationMapping>> GetInfraReplicationMappingByComponentId(string componentId)
    {
        var result = await FilterRequiredField(base.FilterBy(x => x.DatabaseId.Equals(componentId) 
                    || x.Properties.Contains(componentId))).ToListAsync();
        return result;
    }
    private IQueryable<InfraReplicationMapping> FilterRequiredField(IQueryable<InfraReplicationMapping> infraReplicationMappings)
    { 
        return infraReplicationMappings.Select(x => new InfraReplicationMapping
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            DatabaseId = x.DatabaseId,
            ReplicationMasterId = x.ReplicationMasterId,
            ReplicationMasterName= x.ReplicationMasterName,
            DatabaseName = x.DatabaseName,
            Properties=x.Properties,
            Type = x.Type,
            IsActive = x.IsActive
        });
    }

   
}