﻿namespace ContinuityPatrol.Application.Features.WorkflowAction.Commands.Import;

public class ImportWorkflowActionListCommand
{
    public string Id { get; set; }
    public string NodeId { get; set; }
    public string ActionName { get; set; }
    public string Properties { get; set; }
    public string Version { get; set; }
    public string Script { get; set; }
    public string Type { get; set; }
    public override string ToString()
    {
        return $"Action Name: {ActionName};";
    }
}
