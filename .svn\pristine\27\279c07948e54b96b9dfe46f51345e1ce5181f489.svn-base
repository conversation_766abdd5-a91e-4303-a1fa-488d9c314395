﻿using ContinuityPatrol.Application.Features.SingleSignOn.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.SingleSignOn.Events;

public class UpdateSingleSignOnEventTests : IClassFixture<SingleSignOnFixture>, IClassFixture<UserActivityFixture>
{
    private readonly SingleSignOnFixture _singleSignOnFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly SingleSignOnUpdatedEventHandler _handler;

    public UpdateSingleSignOnEventTests(SingleSignOnFixture singleSignOnFixture, UserActivityFixture userActivityFixture)
    {
        _singleSignOnFixture = singleSignOnFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockSingleSignOnEventLogger = new Mock<ILogger<SingleSignOnUpdatedEventHandler>>();

        _mockUserActivityRepository = SingleSignOnRepositoryMocks.CreateSingleSignOnEventRepository(_userActivityFixture.UserActivities);

        _handler = new SingleSignOnUpdatedEventHandler(mockLoggedInUserService.Object, mockSingleSignOnEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateSingleSignOnEventUpdated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_singleSignOnFixture.SingleSignOnUpdatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_singleSignOnFixture.SingleSignOnUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}