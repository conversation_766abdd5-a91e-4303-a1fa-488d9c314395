﻿namespace ContinuityPatrol.Application.Features.BusinessFunction.Events.DashboardViewEvent.Update;

public class
    BusinessFunctionDashboardViewUpdatedEventHandler : INotificationHandler<BusinessFunctionDashboardViewUpdatedEvent>
{
    private readonly IDashboardViewRepository _dashboardViewRepository;

    private readonly ILogger<BusinessFunctionDashboardViewUpdatedEventHandler> _logger;

    public BusinessFunctionDashboardViewUpdatedEventHandler(IDashboardViewRepository dashboardViewRepository,
        ILogger<BusinessFunctionDashboardViewUpdatedEventHandler> logger)
    {
        _dashboardViewRepository = dashboardViewRepository;
        _logger = logger;
    }

    public async Task Handle(BusinessFunctionDashboardViewUpdatedEvent updatedEvent,
        CancellationToken cancellationToken)
    {
        var dashboardViewList =
            await _dashboardViewRepository.GetBusinessViewListByBusinessFunctionId(updatedEvent.BusinessFunctionId);

        dashboardViewList.ForEach(dashboardView =>
        {
            dashboardView.BusinessFunctionId = updatedEvent?.BusinessFunctionId;
            dashboardView.BusinessFunctionName = updatedEvent?.BusinessFunctionName;
            dashboardView.ConfiguredRPO = updatedEvent?.ConfiguredRPO;
            dashboardView.ConfiguredRTO = updatedEvent?.ConfiguredRTO;
            dashboardView.RPOThreshold = updatedEvent?.RPOThreshold;            
        });

        await _dashboardViewRepository.UpdateRangeAsync(dashboardViewList);

        _logger.LogInformation(
            $"OperationalFunction :: DashboardViewUpdatedEvent '{updatedEvent.BusinessFunctionName}' updated successfully.");
    }
}