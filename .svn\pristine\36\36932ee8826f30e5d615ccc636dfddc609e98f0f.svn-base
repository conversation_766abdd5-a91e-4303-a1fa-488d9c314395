﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title"><i class="cp-monitoring"></i><span>Infra Object Application Monitor</span></h6>
        <span><i class="cp-time me-2"></i>Last Monitored Time : 12/9/2022 1:39:31 PM</span>
    </div>
    <div class="monitor_pages">
        <div class="row g-2 mt-0">
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        Cyber Recovery
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                          @*   <thead>
                                <tr>
                                    <th>Replication Monitoring</th>
                                    <th class="text-primary">Primary</th>
                                    <th>DR</th>
                                </tr>
                            </thead> *@
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-incident-summery me-1"></i>Policy Name</td>
                                    <td class="text-truncate">ppdm</td>
                             
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-estimated-time me-1"></i>Last Job Successful Job (Completed Time)</td>
                                    <td class="text-truncate"><i class="text-primary cp-calendar me-1"></i>19 Jan, 4:05PM</td>
                                
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-user-configuration me-1"></i>Last Successful (Secure/Copy) Job Name
                                    </td>
                                    <td class="text-truncate">JobFBxxx</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-replication-connect me-1"></i>Last Successful (Secure/Copy) Copy Name
                                    </td>
                                    <td class="text-truncate">cr-copy-ppdm-per-20230119160518</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center pt-0">
                        @* <img src="~/img/isomatric/solutiondiagram.svg" height="159px;" /> *@
                        <div id="Solution_Diagram" class="w-50"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-2">
            <div class="col-8 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title">
                        Detailed Job Monitoring
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>
                                        job name
                                    </th>
                                    <th>job Status</th>
                                    <th>job Progress</th>
                                    <th>Created Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate ">Ptech_Prod_1</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>disable</td>
                                    <td class="text-truncate">94%</td>
                                    <td class="text-truncate"><i class="text-primary cp-calendar me-1"></i>19 Jan, 4:05PM</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">Ptech_Prod_2</td>
                                    <td class="text-truncate"><i class="text-success cp-circle-switch me-1"></i>ENABLED</td>
                                    <td class="text-truncate">100%</td>
                                    <td class="text-truncate"><i class="text-primary cp-calendar me-1"></i>19 Jan, 4:05PM</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">Ptech_Prod_3</td>
                                    <td class="text-truncate"><i class="text-success cp-circle-switch me-1"></i>ENABLED</td>
                                    <td class="text-truncate">100%</td>
                                    <td class="text-truncate"><i class="text-primary cp-calendar me-1"></i>19 Jan, 7:05PM</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">Ptech_Prod_4</td>
                                    <td class="text-truncate"><i class="text-danger cp-disable me-1"></i>disable</td>
                                    <td class="text-truncate">56%</td>
                                    <td class="text-truncate"><i class="text-primary cp-calendar me-1"></i>19 Jan, 7:05PM</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card Card_Design_None mb-2 shadow">
                    <div class="card-header card-title">
                        Cyber Recover Vault monitoring
                    </div>
                    <div class="card-body">
                  <div class="d-flex align-items-center gap-3">
                      <div>
                                <i class="cp-cyber-recovery fs-1 text-primary"></i>
                      </div>
                      <div class="vr"></div>
                      <div>
                                <p >Cyber Recover Vault State</p>
                                <span><i class="cp-lock text-primary me-1"></i>Lock</span>
                      </div>
                  </div>
                    </div>
                </div>
                <div class="card Card_Design_None mb-0 shadow">
                    <div class="card-header card-title">
                        RPO
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center gap-3">
                            <div>
                                <i class="cp-datalog fs-1 text-primary"></i>
                            </div>
                            <div class="vr"></div>
                            <div>
                                <p >(DataLag)</p>
                                <span><i class="cp-calendar text-primary me-1"></i>11 Days</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        Services
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>
                                        Service Name
                                    </th>
                                    <th class="text-primary">Server IP/HostName</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-database-warning me-1"></i>MSSQL Server</td>
                                    <td class="text-truncate"><i class="text-success cp-fal-server me-1"></i>**************</td>
                                    <td class="text-truncate"><i class="text-primary cp-reload me-1 cp-animate"></i>Running</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-log-file-name me-1"></i>MSSQL Server</td>
                                    <td class="text-truncate"><i class="text-success cp-fal-server me-1"></i>**************</td>
                                    <td class="text-truncate"><i class="text-primary cp-reload me-1 cp-animate"></i>Running</td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>