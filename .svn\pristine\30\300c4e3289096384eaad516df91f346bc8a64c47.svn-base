﻿namespace ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetListById;

public class
    GetLoadBalancerListByIdQueryHandler : IRequestHandler<GetLoadBalancerListByIdQuery,
        List<GetLoadBalancerListByIdVm>>
{
    private readonly IMapper _mapper;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;

    public GetLoadBalancerListByIdQueryHandler(ILoadBalancerRepository nodeConfigurationRepository,
        IMapper mapper)
    {
        _nodeConfigurationRepository = nodeConfigurationRepository;
        _mapper = mapper;
    }

    public async Task<List<GetLoadBalancerListByIdVm>> Handle(GetLoadBalancerListByIdQuery request,
        CancellationToken cancellationToken)
    {
        var loadBalancerIds = request.NodeConfigurationId
            .Split(",")
            .Where(x=>x.IsNotNullOrWhiteSpace())
            .ToList();

        var nodeConfiguration = await _nodeConfigurationRepository.GetNodeConfigurationListById(loadBalancerIds);

        return nodeConfiguration.Count > 0 ?
            _mapper.Map<List<GetLoadBalancerListByIdVm>>(nodeConfiguration) 
            : new List<GetLoadBalancerListByIdVm>();
    }
}