
//Server Deployment table starts here.
let deploymentsReplicaNameTD = (value, i = 0, arraylength = 0) =>
    `<div class="form-group">
       <div class="">
         <input value="${value}" ${value ? (i < arraylength - 1 ? "disabled" : "") : ""} class="form-control deploymentsReplicaName" placeholder="Deployments ReplicaSet Name" type="text" name="DeploymentsReplicaSetName"/>
       </div>
       <span class="replicaNamevalidation-error"></span>
    </div>`;

let deploymentsNameTD = (value, i = 0, arraylength = 0) =>
    `<div class="form-group">
        <div class="">
           <input value="${value}" ${value ? (i < arraylength - 1 ? "disabled" : "") : ""} class="form-control deploymentsName" placeholder="Deployments Name" type="text" name="DeploymentsName" />
        </div>
        <span class="namevalidation-error"></span>
    </div>`;

function deploymentTableWhileCreate(id) {
    const $table = document.querySelector(`#f-${id}`);
    //let tableField = $($table).find('.custom-table');
    if ($table.children.length == 0) {
        let $tHead = $(' <thead> </thead>')
        let $headerRow = $('<tr class=".deployment_row" id="deploymentTable"></tr>');
        $headerRow.append('<th>Deployments ReplicaSet Name</th>');
        $headerRow.append('<th>Deployments Name</th>');
        $headerRow.append('<th>Action</th>');

        $tHead.append($headerRow[0])
        $table.append($tHead[0]);

        let $tBody = $('<tbody></tbody>');
        let $dataRow = $('<tr class="deploymentRow"></tr>');
        $dataRow.append(`<td>${deploymentsReplicaNameTD('')}</td>`);
        $dataRow.append(`<td>${deploymentsNameTD('')}</td>`);

        let $actionCell = $('<td style="height:64px" class="d-flex align-items-center justify-content-center"> </td>');
        $actionCell.append(`<span role="button" title="Add"  class="deployAddButton" onclick="event.preventDefault(); handleAddDeployment('${id}', this.parentElement.parentElement, event)"> <i  class="cp-add"></i></span>`);
        $actionCell.append(`<span role="button" title="Edit"  style="display: none;"  class="deployEditButton"  onclick="event.preventDefault(); handleEditDeployment('${id}', this.parentElement.parentElement, event)"> <i  class="cp-edit"></i></span>`);
        $actionCell.append(`<span class="deployUpdateButton" style="display: none;" role="button" onclick="handleUpdateDeploy('${id}',this.parentElement.parentElement, event)" title="Update" class="updateButton"><i class="cp-up-linearrow"></i></span>`);
        $actionCell.append(`<span role="button" title="Delete"  style="display: none;"  class="deployDeleteButton" onclick="event.preventDefault(); handleDelete(event)"> <i  class="cp-Delete"></i></span>`);

        $dataRow.append($actionCell);

        $tBody.append($dataRow[0])
        $table.append($tBody[0]);

        const $wrapper = document.createElement('div');
        $wrapper.classList.add('table-container');
        $table.parentNode.insertBefore($wrapper, $table);
        $wrapper.appendChild($table);

        //When create there is a space between table and other inputs.
        //$wrapper.style.height = '300px';
        //$wrapper.style.overflow = 'auto';
    }
}

function deploymentTableWhileEdit(formData) {
    let tableIDCollection = document.getElementsByClassName('deployments-table');
    let tableID = tableIDCollection[0];
    let $tableId

    if (tableID) {
        $tableId = $(tableID).attr('id');
        //let $tableId = $(element).find('table').attr('id');
        let $table = document.querySelector(`#${$tableId}`)
        let $tHead = $('<thead></thead>')
        let $headerRow = $('<tr class=".deployment_row" id="deploymentTable"></tr>');
        $headerRow.append('<th>Deployments ReplicaSet Name</th>');
        $headerRow.append('<th>Deployments Name</th>');
        $headerRow.append('<th>Action</th>');
        $tHead.append($headerRow[0])
        $table.append($tHead[0]);
        let arraylength = formData.ConfigureDeployments.length;
        let $tBody = $('<tbody></tbody>')
        for (let i = 0; i < arraylength; i++) {
            let $dataRow = $('<tr></tr>');
            $dataRow.append(`<td>${deploymentsReplicaNameTD(formData.ConfigureDeployments[i].DeploymentsReplicaSetName, i, arraylength)}</td>`);
            $dataRow.append(`<td>${deploymentsNameTD(formData.ConfigureDeployments[i].DeploymentsName, i, arraylength)}</td>`);

            let $actionCell = $('<td style="height:64px" class="d-flex align-items-center justify-content-center"> </td>');
            $actionCell.append(`<span role="button" title="Add" style="display: ${arraylength - 1 === i ? '' : 'none'};" class="deployAddButton" onclick="event.preventDefault(); handleAddDeployment('${$tableId}', this.parentElement.parentElement, event)"> <i  class="cp-add"></i></span>`);
            $actionCell.append(`<span role="button" title="Edit" style="display: ${arraylength - 1 !== i ? '' : 'none'};" class="deployEditButton" onclick="event.preventDefault(); handleEditDeployment('${$tableId}', this.parentElement.parentElement, event)"> <i  class="cp-edit"></i></span>`);
            $actionCell.append(`<span class="deployUpdateButton" style="display: none;" role="button" onclick="handleUpdateDeploy('${$tableId}',this.parentElement.parentElement, event)" title="Update" class="updateButton"><i class="cp-up-linearrow"></i></span>`);
            $actionCell.append(`<span role="button" title="Delete" style="display: ${arraylength - 1 !== i ? '' : 'none'};" class="deployDeleteButton" onclick="event.preventDefault(); handleDelete(event)"> <i  class="cp-Delete"></i></span>`);

            $dataRow.append($actionCell);

            $tBody.append($dataRow[0])
            $table.append($tBody[0]);
        }
        const $wrapper = document.createElement('div');
        $wrapper.classList.add('table-container');
        $table.parentNode.insertBefore($wrapper, $table);
        $wrapper.appendChild($table);

        //When edit there is a space between table and other inputs.
        //$wrapper.style.height = '300px';
        //$wrapper.style.overflow = 'auto';
    }
}

async function tableFieldDeploymentsValidation(requiredInputs, actiontype = null) {
    const validationResults = await Promise.all(requiredInputs.map(async function (index, _) {
        if (actiontype === "save") {
            if (index === requiredInputs.length - 1 && requiredInputs.length !== 1) {
                return true;
            }
        }
        let $this = $(this);
        if ($this.is(':visible')) {
            let associatedLabel = $this.attr('placeholder');
            let toLowerCase = associatedLabel.toLowerCase()
            let $deploy1 = $this.closest('.form-group').find('.replicaNamevalidation-error');
            let $deploy2 = $this.closest('.form-group').find('.namevalidation-error');
            let inputValue2 = $this?.val();

            $deploy1.text("").removeClass("field-validation-error");
            $deploy2.text("").removeClass("field-validation-error");

            if (inputValue2 === "") {
                if (associatedLabel.toLowerCase() === "deployments name") {
                    $deploy2.text("Enter " + toLowerCase).addClass("field-validation-error");
                }
                if (associatedLabel.toLowerCase() === "deployments replicaset name") {
                    $deploy1.text("Enter " + toLowerCase).addClass("field-validation-error");
                }
                return false;
            }
            else {
                return true;
            }
        } else {
            return true
        }
    }));

    return validationResults.every(result => result);
}

$(document).on('input', '.deploymentsReplicaName', function () {
    tableFieldDeploymentsValidation($('.deploymentsReplicaName'));
});

$(document).on('input', '.deploymentsName', function () {
    tableFieldDeploymentsValidation($('.deploymentsName'));
});

async function deploymentsReplicaSetName(actiontype = null) {
    let replicaSetName = $("input[name='DeploymentsReplicaSetName']");
    if (replicaSetName?.length > 0) {
        return await tableFieldDeploymentsValidation(replicaSetName, actiontype);
    }
    return true;
}

async function deploymentsName(actiontype = null) {
    let deploymentsName = $("input[name='DeploymentsName']");
    if (deploymentsName?.length > 0) {
        return await tableFieldDeploymentsValidation(deploymentsName, actiontype);
    }
    return true;
}

async function handleAddDeployment(id, newRow, event) {
    event.preventDefault();
    let replicaName = await deploymentsReplicaSetName("add");
    let name = await deploymentsName("add");
    if (replicaName && name) {
        const table = document.querySelector(`#f-${id}`) ? document.querySelector(`#f-${id}`) : document.querySelector(`#${id}`);
        const clonedRow = newRow.cloneNode(true);
        const rows = document.querySelectorAll('.deploymentRow');
        const deployAddButton = newRow.querySelector(".deployAddButton");
        const deployEditButton = newRow.querySelector(".deployEditButton");
        const deployUpdateButton = newRow.querySelector(".deployUpdateButton");
        const deployDeleteButton = newRow.querySelector(".deployDeleteButton");

        deployAddButton.style.display = "none";
        deployEditButton.style.display = "";
        deployDeleteButton.style.display = "";

        const inputElements = clonedRow.querySelectorAll('input');
        const selectElements = clonedRow.querySelectorAll('select');
        inputElements.forEach(input => { input.value = ''; });
        selectElements.forEach(select => { select.value = ''; });

        newRow.querySelector(".deploymentsReplicaName").setAttribute("disabled", true);
        newRow.querySelector(".deploymentsName").setAttribute("disabled", true);

        table.appendChild(clonedRow);
    }
}

function handleEditDeployment(id, newRow, event) {
    newRow.querySelector(".deploymentsReplicaName").removeAttribute("disabled");
    newRow.querySelector(".deploymentsName").removeAttribute("disabled");

    newRow.querySelector('.deploymentsReplicaName').focus();

    const cpEditElement = newRow.querySelector(".deployEditButton");
    const deployUpdateButton = newRow.querySelector(".deployUpdateButton");

    if (cpEditElement) {
        cpEditElement.style.display = "none";
        deployUpdateButton.style.display = "";
    }
}

function handleUpdateDeploy(id, newRow, event) {
    newRow.querySelector(".deploymentsReplicaName").setAttribute("disabled", true);
    newRow.querySelector(".deploymentsName").setAttribute("disabled", true);
    const cpEditElement = newRow.querySelector(".deployEditButton");
    const deployUpdateButton = newRow.querySelector(".deployUpdateButton");

    if (cpEditElement) {
        cpEditElement.style.display = "";
        deployUpdateButton.style.display = "none";
    }
}
//Server Deployment table ends here.


//Server Sudosu table starts here.
function sudosuTableWhileCreate(id) {
    const $table = document.querySelector(`#f-${id}`);

    if ($table?.children?.length == 0) {
        let $tHead = $(' <thead> </thead>')
        let $headerRow = $('<tr class=".header_row" id="substituteAuthentication"></tr>');
        $headerRow.append('<th>Authenticate Type</th>');
        $headerRow.append('<th>Path</th>');
        $headerRow.append('<th>User</th>');
        $headerRow.append('<th>Password</th>');
        $headerRow.append('<th>Action</th>');

        $tHead.append($headerRow[0])
        $table.append($tHead[0]);

        let $tBody = $('<tbody></tbody>');
        let $dataRow = $('<tr></tr>');
        $dataRow.append('<td><select class="substituteAuthenticationType" data-placeholder="Select Auth Type" name="SubstituteAuthenticationType"><option value="" hidden>Select Auth Type</option><option value="sudo su">sudo su</option><option value="su">su</option><option value="asu">asu</option><option value="sudo">sudo</option><option value="privrun">privrun</option><option value="IsPasswordless">ispasswordless</option><option value="other">other</option></select></td>');
        $dataRow.append('<td><input class="substitutePath" placeholder="Enter Path" autocomplete="off" type="text" name="SubstituteAuthenticationPath"/></td>');
        $dataRow.append('<td><input class="substituteUserName" autocomplete="off" placeholder="Enter User" type="text" name="SubstituteAuthenticationUser" /> </td>');
        $dataRow.append('<td><input class="substitutePassword" placeholder="Enter Password" type="password" name="SubstituteAuthenticationPassword" /></td>');

        let $actionCell = $('<td style="height:62px" class="d-flex align-items-center justify-content-center"> </td>');
        $actionCell.append(`<span role="button" title="Add" class="sudosuAddButton" onclick="event.preventDefault(); handleAddSudosu('${id}', this.parentElement.parentElement, event)"> <i  class="cp-add "></i></span>`);

        $actionCell.append(`<span role="button" title="Edit"  style="display: none;"  class="sudosuEditButton"  onclick="event.preventDefault(); handleEditSudosu('${id}', this.parentElement.parentElement, event)"> <i  class="cp-edit"></i></span>`);
        $actionCell.append(`<span class="sudosuUpdateButton" style="display: none;" role="button" onclick="handleUpdateSudosu('${id}',this.parentElement.parentElement, event)" title="Update" class="updateButton"><i class="cp-up-linearrow"></i></span>`);
        $actionCell.append(`<span role="button" title="Delete"  style="display: none;"  class="sudosuDeleteButton" onclick="event.preventDefault(); handleDelete(event)"> <i  class="cp-Delete"></i></span>`);

        $dataRow.append($actionCell);
        $tBody.append($dataRow[0])
        $table.append($tBody[0]);
    }
}

async function sudosuTableWhileEdit(formData) {
    let tableIDCollection = document.getElementsByClassName('custom-table');
    let tableID = tableIDCollection[0];
    let $tableId
    if (tableID) {
        $tableId = $(tableID).attr('id');
        //let $tableId = $(element).find('table').attr('id');
        let $table = document.querySelector(`#${$tableId}`)
        let $tHead = $('<thead></thead>')
        let $headerRow = $('<tr class=".header_row" id="substituteAuthentication"></tr>');
        $headerRow.append('<th>Authenticate Type</th>');
        $headerRow.append('<th>Path</th>');
        $headerRow.append('<th>User</th>');
        $headerRow.append('<th>Password</th>');
        $headerRow.append('<th>Action</th>');
        $tHead.append($headerRow[0])
        $table.append($tHead[0]);

        let $tBody = $('<tbody></tbody>')
        for (let i = 0; i < formData.ConfigureSubstituteAuthentication.length; i++) {
            let $dataRow = $('<tr></tr>');
            let res = '';

            $dataRow.append(`<td><select disabled class="substituteAuthenticationType" data-placeholder="Select Auth Type" name="SubstituteAuthenticationType">
                        <option value="" selected hidden>Select Auth Type</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'sudo su' ? 'selected' : ''}>sudo su</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'su' ? 'selected' : ''}>su</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'asu' ? 'selected' : ''}>asu</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'sudo' ? 'selected' : ''}>sudo</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'privrun' ? 'selected' : ''}>privrun</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'IsPasswordless' ? 'selected' : ''}>ispasswordless</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'other' ? 'selected' : ''}>other</option>
                        </select></td>`);

            if (formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationPassword !== '') {
                await $.ajax({
                    type: "POST",
                    url: RootUrl + 'Configuration/Server/ServerDataDecrypt',
                    data: { data: formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationPassword, __RequestVerificationToken: gettoken() },
                    dataType: 'text',
                    success: function (decryptedValue) {
                        res = decryptedValue;
                    }
                });
            }

            $dataRow.append(`<td><input class="substitutePath"  disabled placeholder="Enter Path" autocomplete="off" name="SubstituteAuthenticationPath" value="${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationPath}" type="text"/></td>`);
            $dataRow.append(`<td><input class="substituteUserName" disabled autocomplete="off" placeholder="Enter User" name="SubstituteAuthenticationUser" value="${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationUser}" type="text" /> </td>`);
            $dataRow.append(`<td><input class="substitutePassword" disabled placeholder="Enter Password" name="SubstituteAuthenticationPassword" value="${res}" type="password" /></td>`);

            let $actionCell = $('<td style="height:62px" class="d-flex align-items-center justify-content-center"></td>');

            $actionCell.append(`<span role="button" title="Add" style="display: ${formData.ConfigureSubstituteAuthentication.length - 1 === i ? '' : 'none'};" class="sudosuAddButton" onclick="event.preventDefault(); handleAddSudosu('${$tableId}', this.parentElement.parentElement, event)"> <i  class="cp-add "></i></span>`);

            $actionCell.append(`<span role="button" title="Edit"  style="display: ${formData.ConfigureSubstituteAuthentication.length - 1 !== i ? '' : 'none'};"  class="sudosuEditButton"  onclick="event.preventDefault(); handleEditSudosu('${$tableId}', this.parentElement.parentElement, event)"> <i  class="cp-edit"></i></span>`);
            $actionCell.append(`<span class="sudosuUpdateButton" style="display: none;" role="button" onclick="handleUpdateSudosu('${$tableId}',this.parentElement.parentElement, event)" title="Update" class="updateButton"><i class="cp-up-linearrow"></i></span>`);
            $actionCell.append(`<span role="button" title="Delete"  style="display: ${formData.ConfigureSubstituteAuthentication.length - 1 !== i ? '' : 'none'};"  class="sudosuDeleteButton" onclick="event.preventDefault(); handleDelete(event)"> <i  class="cp-Delete"></i></span>`);

            $dataRow.append($actionCell);
            $tBody.append($dataRow[0])
            $table.append($tBody[0]);

            setTimeout(() => {
                let lastRow = $('.custom-table tr:last').get(0);
                if (lastRow) {
                    const inputs = lastRow.querySelectorAll('input[type="text"]');
                    const inputPassword = lastRow.querySelectorAll('input[type="password"]');
                    const selects = lastRow.querySelectorAll('select');

                    // Disable the first two input elements, if available
                    if (inputs.length >= 2) {
                        inputs[0].removeAttribute('disabled');
                        inputs[1].removeAttribute('disabled');
                    }

                    if (inputPassword.length >= 1) {
                        inputPassword[0].removeAttribute('disabled');
                    }

                    // Disable the first select element, if available
                    if (selects.length >= 1) {
                        selects[0].removeAttribute('disabled');
                    }

                    // Trigger focus on the input with class "sourceDirectory"
                    $(lastRow).find('select.substituteAuthenticationType').trigger('focus');
                }
            }, 100)
        }
    }
}

function SubstituteAuthType() {

    return new Promise((resolve, reject) => {
        let selectInput = $("select[name='SubstituteAuthenticationType']");
        if (selectInput?.length > 0) {
            selectInput.each(function () {
                let $this = $(this);
                if ($this.is(':visible')) {
                    let associatedLabel = $this.attr('data-placeholder');
                    let toLowerCase = associatedLabel.charAt(0).toUpperCase() + associatedLabel.slice(1).toLowerCase();
                    $this.next(".dynamic-input-field").remove();
                    let value = $this.val().replace(/\s/g, "");
                    if (value) {
                        resolve(true);
                    } else {
                        $this.after("<div class='dynamic-input-field table-select-field-validation-error'><span class='required-field-msg'> " + toLowerCase + "</span></div>");
                        resolve(false);
                    }
                } else {
                    resolve(true);
                }
            });
        }
    });
}

async function tableFieldValidation(requiredInputs) {
    const validationResults = await Promise.all(requiredInputs.map(async function () {
        let $this = $(this);
        if ($this.is(':visible')) {
            let associatedLabel = $this.attr('placeholder');
            let toLowerCase = associatedLabel.charAt(0).toUpperCase() + associatedLabel.slice(1).toLowerCase();
            let inputValue2 = $this?.val();
            $this.next(".dynamic-input-field").remove();

            if (inputValue2 === "") {
                $this.after("<div class='dynamic-input-field table-field-validation-error'><span class='required-field-msg'> " + toLowerCase + "</span></div>");
                return false;
            }
            else {
                return true;
            }
        } else {
            return true
        }
    }));

    return validationResults.every(result => result);

    //requiredInputs?.each(async function () {
    //    let $this = $(this);
    //    if ($this.is(':visible')) {
    //        let associatedLabel = $this.attr('placeholder');
    //        let toLowerCase = associatedLabel.charAt(0).toUpperCase() + associatedLabel.slice(1).toLowerCase();
    //        let inputValue2 = $this?.val();
    //        $this.next(".dynamic-input-field").remove();

    //        if (inputValue2 === "") {
    //            // Show error message for each empty required field                               
    //            $this.after("<div class='dynamic-input-field table-field-validation-error'><span class='required-field-msg'> " + toLowerCase + "</span></div>");
    //            inputRqrd = false;
    //            return false;
    //        }
    //        else {
    //            return true;
    //        }
    //    }
    //});
}

async function SubstituteAuthPath() {
    let requiredInputs = $("input[name='SubstituteAuthenticationPath']");
    if (requiredInputs?.length > 0) {
        return await tableFieldValidation(requiredInputs);
    }
    return true;
}

async function SubstituteAuthUser() {
    let requiredInputs = $("input[name='SubstituteAuthenticationUser']");
    if (requiredInputs?.length > 0) {
        return await tableFieldValidation(requiredInputs);
    }
    return true;
}

async function SubstituteAuthPassword() {
    let requiredInputs = $("input[name='SubstituteAuthenticationPassword']");
    if (requiredInputs?.length > 0) {
        return await tableFieldValidation(requiredInputs);
    }
    return true;
}

$(document).on("change", ".substituteAuthenticationType", function () {
    let $this = $(this);
    let value = $this.val();
    if (value) {
        if ($this.next(".dynamic-input-field").length > 0) {
            $this.next(".dynamic-input-field").remove();
        }
    }
});

$(document).on("input", ".substituteUserName", function () {
    let $this = $(this);
    let value = $this.val();
    if (value) {
        if ($this.next(".dynamic-input-field").length > 0) {
            $this.next(".dynamic-input-field").remove();
        }
    }
});

async function handleAddSudosu(id, newRow, event) {
    enableValidation = false;
    let type = await SubstituteAuthType();
    //let path = await SubstituteAuthPath();
    let user = await SubstituteAuthUser();
    //let pwd = await SubstituteAuthPassword();

    if (type && user) {
        event.preventDefault();
        const table = document.querySelector(`#f-${id}`) ? document.querySelector(`#f-${id}`) : document.querySelector(`#${id}`);

        const clonedRow = newRow.cloneNode(true);
        const sudosuAddButton = newRow.querySelector(".sudosuAddButton");
        const sudosuEditButton = newRow.querySelector(".sudosuEditButton");
        const sudosuUpdateButton = newRow.querySelector(".sudosuUpdateButton");
        const sudosuDeleteButton = newRow.querySelector(".sudosuDeleteButton");

        sudosuAddButton.style.display = "none";
        sudosuEditButton.style.display = "";
        sudosuDeleteButton.style.display = "";

        const clonedRowDeleteButton = clonedRow.querySelector(".sudosuDeleteButton");
        clonedRowDeleteButton.style.display = "";

        const inputElements = clonedRow.querySelectorAll('input');
        const selectElements = clonedRow.querySelectorAll('select');
        inputElements.forEach(input => { input.value = ''; });
        selectElements.forEach(select => { select.value = ''; });

        newRow.querySelector(".substituteAuthenticationType").setAttribute("disabled", true);
        newRow.querySelector(".substitutePath").setAttribute("disabled", true);
        newRow.querySelector(".substituteUserName").setAttribute("disabled", true);
        newRow.querySelector(".substitutePassword").setAttribute("disabled", true);

        table.appendChild(clonedRow);
    }
}

function handleEditSudosu(id, newRow, event) {
    newRow.querySelector(".substituteAuthenticationType").removeAttribute("disabled");
    newRow.querySelector(".substitutePath").removeAttribute("disabled");
    newRow.querySelector(".substituteUserName").removeAttribute("disabled");
    newRow.querySelector(".substitutePassword").removeAttribute("disabled");

    newRow.querySelector('.substituteAuthenticationType').focus();

    const sudosuEditButton = newRow.querySelector(".sudosuEditButton");
    const sudosuUpdateButton = newRow.querySelector(".sudosuUpdateButton");

    if (sudosuEditButton) {
        sudosuEditButton.style.display = "none";
        sudosuUpdateButton.style.display = "";
    }
}

function handleUpdateSudosu(id, newRow, event) {
    newRow.querySelector(".substituteAuthenticationType").setAttribute("disabled", true);
    newRow.querySelector(".substitutePath").setAttribute("disabled", true);
    newRow.querySelector(".substituteUserName").setAttribute("disabled", true);
    newRow.querySelector(".substitutePassword").setAttribute("disabled", true);

    const sudosuEditButton = newRow.querySelector(".sudosuEditButton");
    const sudosuUpdateButton = newRow.querySelector(".sudosuUpdateButton");

    if (sudosuEditButton) {
        sudosuEditButton.style.display = "";
        sudosuUpdateButton.style.display = "none";
    }
}

function handleDelete(event) {
    event.preventDefault();
    $("#deleteModalSudoSuTable").modal("show");
    document.getElementById("deleteSudoSuTableRow").addEventListener('click', async function () {
        let rowToDelete = $(event.target).closest('tr');
        //var previousRow = rowToDelete.prev();
        rowToDelete.remove();
        $("#deleteModalSudoSuTable").modal("hide");

        let customTable = $('.custom-table').find('tr').length;

        if (customTable === 2) {
            const table = document.querySelector('.custom-table');
            const addButton = table.querySelector('.sudosuAddButton');
            const editButton = table.querySelector('.sudosuEditButton');
            const deleteButton = table.querySelector('.sudosuDeleteButton');

            let lastRow = $('.custom-table tr:last').get(0);
            if (lastRow) {
                const inputs = lastRow.querySelectorAll('input[type="text"]');
                const inputPassword = lastRow.querySelectorAll('input[type="password"]');
                const selects = lastRow.querySelectorAll('select');

                // Disable the first two input elements, if available
                if (inputs.length >= 2) {
                    inputs[0].removeAttribute('disabled');
                    inputs[1].removeAttribute('disabled');
                }

                if (inputPassword.length >= 1) {
                    inputPassword[0].removeAttribute('disabled');
                }

                // Disable the first select element, if available
                if (selects.length >= 1) {
                    selects[0].removeAttribute('disabled');
                }

                // Trigger focus on the input with class "sourceDirectory"
                $(lastRow).find('select.substituteAuthenticationType').trigger('focus');
            }
            if (editButton) {
                editButton.style.display = 'none';
            }
            if (addButton) {
                addButton.style.display = '';
            }
            if (deleteButton) {
                deleteButton.style.display = 'none';
            }
        } else {
            let lastRow = $('.custom-table tr:last').get(0);

            if (lastRow) {
                const hasAddButton = lastRow.querySelector('.sudosuAddButton');
                const hasDeleteButton = lastRow.querySelector('.sudosuDeleteButton');
                const hasEdit = lastRow.querySelector('.sudosuEditButton');

                const inputs = lastRow.querySelectorAll('input[type="text"]');
                const inputPassword = lastRow.querySelectorAll('input[type="password"]');
                const selects = lastRow.querySelectorAll('select');

                // Disable the first two input elements, if available
                if (inputs.length >= 3) {
                    inputs[0].removeAttribute('disabled');
                    inputs[1].removeAttribute('disabled');
                }

                if (inputPassword.length >= 1) {
                    inputPassword[0].removeAttribute('disabled');
                }

                // Disable the first select element, if available
                if (selects.length >= 1) {
                    selects[0].removeAttribute('disabled');
                }

                // Trigger focus on the input with class "sourceDirectory"
                $(lastRow).find('select.substituteAuthenticationType').trigger('focus');

                // Hide the edit button and show the add button, if they exist
                if (hasEdit.style.display !== "none") {
                    hasEdit.style.display = 'none';
                }
                if (hasAddButton.style.display === "none") {
                    hasAddButton.style.display = '';
                }
            }
        }


        //const hasAddButton = previousRow.find('.sudosuAddButton').get(0);
        //const hasDeleteButton = previousRow.find('.sudosuDeleteButton').get(0);
        //const hasEdit = previousRow.find('.sudosuEditButton').get(0);

        //if (hasEdit.style.display !== "none" && hasDeleteButton.style.display !== "none" && hasAddButton.style.display === "none") {
        //    hasEdit.style.display = 'none';
        //    hasDeleteButton.style.display = 'none';
        //    hasAddButton.style.display = '';
        //}       
    });
}
//Server Sudosu table ends here.


function encrypt(value) {
    const encryptedValue = btoa(JSON.stringify(value));
    return encryptedValue;
}

// Decryption function
function decrypt(encryptedValue) {
    const decryptedValue = JSON.parse(atob(encryptedValue));
    return decryptedValue;
}

function setFieldAttrValues(field) {
    const { id, meta, attrs } = field;
    if (meta?.id === "textarea") {
        const textArea = $(`#f-${id}`);
        textArea.attr('title', '');
        textArea.attr('autocomplete', 'off');
    }
    if (meta?.id === "ip-address") {
        const ipAddress = $(`#f-${id}`);
        ipAddress.attr('title', '');
        ipAddress.attr('maxlength', '40');
        ipAddress.attr('autocomplete', 'off');
    }
    if (meta?.id === "paragraph") {
        const paragraph = $(`#f-${id}`);
        paragraph.attr('title', '');
        paragraph.attr('autocomplete', 'off');
    }
    if (meta?.id === "text-input") {
        const textInput = $(`#f-${id}`);
        textInput.attr('title', '');
        let maxLength = textInput.attr('maxlength');
        if (!maxLength) {
            if (attrs?.name?.toLowerCase().includes("path")) {
                textInput.attr('maxlength', '500');
            } else {
                textInput.attr('maxlength', '100');
            }
        }
        textInput.attr('autocomplete', 'off');
    }
    if (meta?.id === "password-input") {
        const passwordInput = $(`#f-${id}`);
        passwordInput.attr('title', '');
        passwordInput.attr('maxlength', '40');
        passwordInput.attr('autocomplete', 'off');
    }
    if (meta?.id === "number") {
        const numberField = $(`#f-${id}`);
        numberField.attr('title', '');
        numberField.attr('autocomplete', 'off');
        const minLength = numberField?.attr('minlength');
        const maxLength = numberField?.attr('maxlength');
        const intMinValue = parseInt(minLength, 10);
        const intMaxValue = parseInt(maxLength, 10);
        if (intMinValue && intMaxValue && intMinValue > 0 && intMaxValue > 0) {
            numberField.on("keyup keydown", function (event) {
                let eventKey = event.key;
                const validKeys = ["Home", "End", "Backspace", "Delete", "ArrowLeft", "ArrowRight", ...Array.from({ length: 10 }, (_, i) => i.toString())];
                if (validKeys.includes(eventKey)) {
                    const inputValue = $(this).val();
                    const allowedKeys = ['Home', 'End', 'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];

                    if (inputValue.length >= intMaxValue && !allowedKeys.includes(event.key)) {
                        event.preventDefault();
                    }
                    const numericValue = parseInt($(this).val().length, 10);

                    if (isNaN(numericValue) || intMinValue < 1 || numericValue > intMaxValue) {
                        $(this).val('');
                    }
                } else {
                    event.preventDefault();
                }
            });
        } else {
            numberField.prop('min', '1');
            numberField.attr('max', '99999');
            numberField.on("keyup keydown", function (event) {
                let eventKey = event.key;
                const validKeys = ["Backspace", "Delete", "ArrowLeft", "ArrowRight", ...Array.from({ length: 10 }, (_, i) => i.toString())];
                if (validKeys.includes(eventKey)) {
                    const inputValue = $(this).val();
                    const allowedKeys = ['Home', 'End', 'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];

                    if (inputValue.length >= 5 && !allowedKeys.includes(event.key)) {
                        event.preventDefault();
                    }
                    const numericValue = parseInt($(this).val(), 10);

                    if (isNaN(numericValue) || numericValue < 1 || numericValue > 99999) {
                        $(this).val('');
                    }
                } else {
                    event.preventDefault();
                }
            });
        }
    }
}

async function getDBOptionsFromServer(type) {
    let dbType = type;

    if (type.toLowerCase() === "all" || type.toLowerCase() === "select database type") {
        dbType = "";
    }
    const result = await $.ajax({
        url: RootUrl + "Configuration/Database/GetDatabaseNames",
        method: 'GET',
        dataType: 'json',
        data: { type: dbType }
    });

    if (result.success) {
        const options = [];
        options.push({ value: '', text: '' });
        $.each(result?.data, function (index, item) {
            options.push({ value: item.id, text: item.name });
        });
        return options;
    } else {
        errorNotification(result);
    }
}

async function getServOptionsFromServer(ServerRoleID, ServerTypeID) {
    const result = await $.ajax({
        url: RootUrl + "Configuration/Server/GetServerNames",
        method: 'GET',
        dataType: 'json',
        data: { 'roleType': ServerRoleID, 'serverType': ServerTypeID }
    });

    if (result.success) {
        const options = [];
        options.push({ value: '', text: '' });
        $.each(result?.data, function (index, item) {
            options.push({ value: item.id, text: item.name });
        });
        return options;
    } else {
        errorNotification(result);
    }
}

async function getSSOOptionsFromServer() {
    const result = await $.ajax({
        url: RootUrl + "Admin/ComponentType/SingleSignOnList",
        method: 'GET',
        dataType: 'json'
    });

    if (result.success) {
        const options = [];
        options.push({ value: '', text: '' });
        const uniqueValues = new Set();
        $.each(result?.data, function (index, item) {
            if (!uniqueValues.has(item?.id?.toLowerCase())) {
                options.push({ value: item.id, text: item.componentName });
                uniqueValues.add(item?.id?.toLowerCase());
            }
        });
        return options;
    } else {
        errorNotification(result);
    }
}

async function getSSOProfileNameOptionsFromServer(value) {
    if (value) {
        const result = await $.ajax({
            url: RootUrl + "Configuration/SingleSignOn/GetSingleSignOnByType",
            type: 'POST',
            data: { type: value, __RequestVerificationToken: gettoken() },
            dataType: 'json'
        });

        if (result.success) {
            const options = [];
            options.push({ value: "", text: "Select Profile" });
            $.each(result?.data, function (index, item) {
                options.push({ value: item.id, text: item.profileName });
            });
            return options;
        } else {
            errorNotification(result);
        }
    } else {
        const options = [];
        options.push({ value: "", text: "Select Profile" });
        return options;
    }
}

async function getRepOptionsFromServer() {
    const result = await $.ajax({
        url: RootUrl + "Configuration/Replication/GetReplicationNames",
        method: 'GET',
        dataType: 'json'
    });

    if (result.success) {
        const options = [];
        options.push({ value: '', text: '' });
        $.each(result?.data, function (index, item) {
            options.push({ value: item.id, text: item.name });
        });
        return options;
    } else {
        errorNotification(result);
    }
}

async function getNodesFromServer() {
    const result = await $.ajax({
        url: RootUrl + "Configuration/Node/GetNodeNames",
        method: 'GET',
        dataType: 'json'
    });

    if (result.success) {
        const options = [];
        options.push({ value: '', text: '' });
        $.each(result?.data, function (index, item) {
            options.push({ value: item.id, text: item.name });
        });
        return options;
    } else {
        errorNotification(result);
    }
}

async function getWorkflowsActionsFromServer(id) {
    if (id) {
        const result = await $.ajax({
            url: RootUrl + "ITAutomation/WorkflowList/GetWorkflowById",
            method: 'GET',
            dataType: 'json',
            data: { workflowId: id }
        });

        if (result.success) {
            const options = [];
            let Properties = JSON.parse(result?.data.properties);
            let propLength = Properties.nodes.length;

            for (let i = 0; i < propLength; i++) {
                if (Properties?.nodes[i]?.hasOwnProperty('children')) {
                    Properties?.nodes[i]?.children?.forEach(function (obj) {
                        let Obj = { 'id': obj.actionInfo.uniqueId, value: obj.actionInfo.actionName };
                        options.push(Obj);
                    });
                } else if (!Properties.nodes[i].hasOwnProperty('groupName')) {
                    let obj = { 'id': Properties.nodes[i].actionInfo.uniqueId, value: Properties.nodes[i].actionInfo.actionName };
                    options.push(obj);
                }
            }

            return options;
        } else {
            errorNotification(result);
        }
    }
}

async function getWorkflowsFromServer(isAction, id) {
    const result = await $.ajax({
        url: RootUrl + "ITAutomation/WorkflowList/GetWorkflowNames",
        method: 'GET',
        dataType: 'json'
    });

    if (result.success) {
        const options = [];
        options.push({ value: '', text: '' });
        $.each(result?.data, function (index, item) {
            options.push({ value: item.id, text: item.name });
        });
        return options;
    } else {
        errorNotification(result);
    }
}

//$(document).on("input", ".deploymentsReplicaName", function () {
//    let $this = $(this);
//    let value = $this.val();
//    let associatedLabel = $this.attr('placeholder');
//    let toLowerCase = associatedLabel.charAt(0).toUpperCase() + associatedLabel.slice(1).toLowerCase();
//    if (value) {
//        if ($this.next(".dynamic-input-field").length > 0) {
//            $this.next(".dynamic-input-field").remove();
//        }
//    } else {
//        $this.after("<div class='dynamic-input-field table-field-deploymentsReplicaNamevalidation-error'><span class='required-field-msg'> " + toLowerCase + "</span></div>");
//    }
//});

//$(document).on("input", ".deploymentsName", function () {
//    let $this = $(this);
//    let value = $this.val();
//    let associatedLabel = $this.attr('placeholder');
//    let toLowerCase = associatedLabel.charAt(0).toUpperCase() + associatedLabel.slice(1).toLowerCase();
//    if (value) {
//        if ($this.next(".dynamic-input-field").length > 0) {
//            $this.next(".dynamic-input-field").remove();
//        }
//    } else {
//        $this.after("<div class='dynamic-input-field table-field-deploymentsNamevalidation-error'><span class='required-field-msg'> " + toLowerCase + "</span></div>");
//    }
//});

//async function handleAddDynamicRow(id, newRow, event) {
//    event.preventDefault();
//    const table = document.querySelector(`#f-${id}`) ? document.querySelector(`#f-${id}`) : document.querySelector(`#${id}`);
//    const clonedRow = newRow.cloneNode(true);

//    // Clear input and select values in the cloned row
//    const inputElements = clonedRow.querySelectorAll('input');
//    inputElements.forEach(input => { input.value = ''; });

//    // Check if the cloned row already has a delete button
//    const hasDeleteButton = clonedRow.querySelector('.delete-button');
//    if (!hasDeleteButton) {
//        const lastTd = clonedRow.querySelector('td:last-child');
//        lastTd.innerHTML += '<span role="button" title="Delete" class="delete-button"><i onclick="handleDelete(event)" class="cp-Delete"></i></span>';
//    }

//    // Append the cloned row to the table
//    table.appendChild(clonedRow);
//}

async function populateFormbuilderDynamicFields(fields) {
    for (const key in fields) {
        if (fields.hasOwnProperty(key)) {
            const field = fields[key];
            const { id, meta, config, attrs } = field;
            if (meta.id === "password-input") {
                //setTimeout(() => {
                //    const fieldElement = document.querySelector(`#f-${id}`);; // Assuming id is the ID of the password input field
                //    if (fieldElement) {
                //        const fieldGroup = fieldElement.closest('.f-field-group'); // Assuming the input field is within a container with class 'f-field-group'
                //        if (fieldGroup) {
                //            // Create an icon element (for Font Awesome, use <i> tag)
                //            const icon = document.createElement('span');
                //            icon.classList.add('cp-password-hide'); // Example classes for Font Awesome icon
                //            // Append the icon to the field container
                //            fieldGroup.appendChild(icon);
                //        }
                //    }
                //}, 1000)
            }
            if (meta.id === "database") {
                const type = attrs.DatabaseTypeID;
                const _options = await getDBOptionsFromServer(type);
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    nextButtonStyle(' ', ' ');
                    _options?.forEach(option => {
                        const optionElement = document.createElement("option");
                        optionElement.value = option.value;
                        optionElement.text = option.text;
                        selectField.appendChild(optionElement);
                    });
                } else {
                    let error = { message: "Check the database condition field in the form builder module." }
                    errorNotification(error);
                    nextButtonStyle('0.5', 'none');
                }
            }
            if (meta.id === "server") {
                const role = attrs.ServerRole;
                const type = attrs.ServerType;
                const ServerRoleID = attrs.ServerRoleID;
                const ServerTypeID = attrs.ServerTypeID;
                const _options = await getServOptionsFromServer(ServerRoleID, ServerTypeID);
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    nextButtonStyle(' ', ' ');
                    _options?.forEach(option => {
                        const optionElement = document.createElement("option");
                        optionElement.value = option.value;
                        optionElement.text = option.text;
                        selectField.appendChild(optionElement);
                    });
                } else {
                    let error = { message: "Check the server condition field in the form builder module." }
                    errorNotification(error);
                    nextButtonStyle('0.5', 'none');
                }
            }
            if (meta.id === "replication") {
                const _options = await getRepOptionsFromServer();
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    nextButtonStyle(' ', ' ');
                    _options?.forEach(option => {
                        const optionElement = document.createElement("option");
                        optionElement.value = option.value;
                        optionElement.text = option.text;
                        selectField.appendChild(optionElement);
                    });
                } else {
                    let error = { message: "Check the replication condition field in the form builder module." }
                    errorNotification(error);
                    nextButtonStyle('0.5', 'none');
                }
            }
            if (meta.id === "workflow") {
                let isAction = attrs.dependentAction
                const _options = await getWorkflowsFromServer(isAction);
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    nextButtonStyle(' ', ' ');
                    if (isAction) {
                        const duplicatedSelectField = selectField.parentNode.parentNode.parentNode.parentNode.cloneNode(true);
                        const labelElement = duplicatedSelectField.querySelector('label');
                        labelElement.textContent = 'Workflows Actions';
                        labelElement.htmlFor = 'f-new-select-id8actions0';
                        const selectElement = duplicatedSelectField.querySelector('select');
                        selectElement.name = '@@workflow_actions';
                        selectElement.multiple = true
                        selectElement.id = 'f-new-select-id8actions0';
                        selectElement.setAttribute('placeholder', 'Select Workflow Actions');
                        selectField.parentNode.parentNode.parentNode.parentNode.appendChild(duplicatedSelectField);

                        _options?.forEach(option => {
                            const optionElement = document.createElement("option");
                            optionElement.value = option.value;
                            optionElement.text = option.text;
                            selectField.appendChild(optionElement);
                        });

                        selectElement.innerHTML = '';
                        const optionElement2 = document.createElement("option");
                        optionElement2.value = '';
                        optionElement2.text = "Select Workflow Actions";
                        selectElement.appendChild(optionElement2);

                        $(`#f-${id}`).on("change", async function () {
                            const _options2 = await getWorkflowsActionsFromServer(this.value);
                            selectElement.innerHTML = '';
                            _options2?.forEach(option => {
                                const optionElement = document.createElement("option");
                                optionElement.value = option.id;
                                optionElement.text = option.value;
                                selectElement.appendChild(optionElement);
                            });
                        })
                    }
                    else {
                        _options?.forEach(option => {
                            const optionElement = document.createElement("option");
                            optionElement.value = option.value;
                            optionElement.text = option.text;
                            selectField.appendChild(optionElement);
                        });
                    }

                } else {
                    let error = { message: "Check the workflow condition field in the form builder module." }
                    errorNotification(error);
                    nextButtonStyle('0.5', 'none');
                }
            }
            if (meta.id === "singlesignon") {
                const _options = await getSSOOptionsFromServer();
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    nextButtonStyle('', '');
                    const profileField = selectField.parentNode.cloneNode(true);
                    const labelElement = profileField.querySelector('label');
                    labelElement.textContent = 'Profile';
                    labelElement.htmlFor = 'f-new-select-id8rhdgry0';
                    const selectElement = profileField.querySelector('select');
                    selectElement.name = 'signonprofile';
                    selectElement.id = 'f-new-select-id8rhdgry0';
                    selectElement.setAttribute('placeholder', 'Select Profile');
                    selectField.parentNode.parentNode.appendChild(profileField);

                    _options?.forEach(option => {
                        const optionElement = document.createElement("option");
                        optionElement.value = option.value;
                        optionElement.text = option.text;
                        selectField.appendChild(optionElement);
                    });

                    selectElement.innerHTML = '';
                    const optionElement2 = document.createElement("option");
                    optionElement2.value = '';
                    optionElement2.text = "Select Profile";
                    selectElement.appendChild(optionElement2);

                    $(`#f-${id}`).on("change", async function () {
                        const _options2 = await getSSOProfileNameOptionsFromServer(this.value);
                        selectElement.innerHTML = '';
                        _options2?.forEach(option => {
                            const optionElement = document.createElement("option");
                            optionElement.value = option.value;
                            optionElement.text = option.text;
                            selectElement.appendChild(optionElement);
                        });
                    });
                } else {
                    let error = { message: "Check the single sign-on condition field in the form builder module." }
                    errorNotification(error);
                    nextButtonStyle('0.5', 'none');
                }
            }
            if (meta.id === "nodes") {
                const _options = await getNodesFromServer();
                const selectNodeField = document.querySelector(`#f-${id}`);

                if (selectNodeField) {
                    nextButtonStyle('', '');
                    _options?.forEach(option => {
                        const optionElement = document.createElement("option");
                        optionElement.value = option.value;
                        optionElement.text = option.text;
                        selectNodeField?.appendChild(optionElement);
                    });
                } else {
                    let error = { message: "Check the nodes condition field in the form builder module." }
                    errorNotification(error);
                    nextButtonStyle('0.5', 'none');
                }
            }
            if (meta.id === "table") {
                setTimeout(() => {
                    sudosuTableWhileCreate(id);
                }, 1000)
            }

            if (meta.id === "deploymentTable") {
                setTimeout(() => {
                    deploymentTableWhileCreate(id);
                }, 1000)
            }

            //    if (meta.id === "dynamicTable") {
            //        setTimeout(() => {
            //            const $table = document.querySelector(`#f-${id}`);
            //            //$table.innerHTML = '';
            //            //console.log(id, attrs, $table);
            //            let keys = Object.keys(attrs?.dynamicHeader);
            //            let length = keys.length;
            //            if ($table?.children?.length == 0) {
            //                let $tHead = $(' <thead> </thead>')
            //                let $headerRow = $('<tr class="header_row5"></tr>');
            //                for (let i = 0; i < length; i++) {
            //                    $headerRow.append(`<th id="${keys[i]}">${attrs?.dynamicHeader[keys[i]]}</th>`);
            //                }
            //                $headerRow.append('<th>Action</th>');
            //                // Append the header row to the table
            //                $tHead.append($headerRow[0])
            //                $table.append($tHead[0]);

            //                let $tBody = $('<tbody></tbody>');
            //                let $dataRow = $('<tr></tr>');
            //                for (let i = 0; i < length; i++) {
            //                    let dynamicID = Math.floor((Math.random() * 10000) + 1);
            //                    $dataRow.append(`<td><input type="text" name="dynamicData${dynamicID}" placeholder="Enter ${attrs?.dynamicHeader[keys[i]]}" /></td>`);
            //                }
            //                // Add action buttons to the data row
            //                let $actionCell = $('<td style="height:62px" class="d-flex align-items-center justify-content-center"> </td>');
            //                $actionCell.append(`<span role="button" title="Add"  onclick="event.preventDefault(); handleAddDynamicRow('${id}', this.parentElement.parentElement, event)"> <i  class="cp-add "></i></span>`);
            //                //$actionCell.append('<span role="button" title="Delete" class="delete-button" ><i onclick="handleDelete(event)" class="cp-Delete"></i></span>');

            //                $dataRow.append($actionCell);
            //                // Append the data row to the table
            //                $tBody.append($dataRow[0])
            //                $table.append($tBody[0]);
            //            }
            //        }, 1000);
            //    }
        }
    }
}

function nextButtonStyle(opacity, pointerEvents) {
    $('.next_btn').css({
        'opacity': opacity,
        'pointer-events': pointerEvents,
    });
}

$('.btn-cancel').on('click', function () {
    nextButtonStyle('', '');
});

function formBuilderTextConditions(event, parsedJsonData) {
    let selectedValue = event.target.value;
    let selectedid = event.target.id;
    let typ = event.target.type;
    let getId = selectedid.replace('f-', '');

    if (typ === "text") {
        let replacedId = getId.replace(/-0$/, '');
        let field = parsedJsonData?.fields && parsedJsonData?.fields[replacedId];

        if (field?.conditions && field?.conditions.length > 0) {
            let isVisible = false;
            field.conditions.forEach(function (condition) {
                let isMatchingCondition = condition.if.some(function (ifClause) {
                    sourceField = parsedJsonData?.fields[ifClause.source.substring(7)];
                    return ifClause.target === selectedValue;
                });

                if (isMatchingCondition) {
                    isVisible = true;
                }
            });

            field.conditions.forEach(function (condition) {
                condition.then.forEach(function (thenClause) {
                    condition.if.forEach(function (ifClause) {
                        let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);

                        if (targetElement) {

                            if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                                if (isVisible) {
                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                } else {
                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add("d-none")
                                }
                            }
                        }
                    });
                });
            });
        }
    }
}

function removeValidationWhenUncheck(targets) {
    targets.forEach(function (target) {
        target.parentNode.removeChild(target);
    });
}
