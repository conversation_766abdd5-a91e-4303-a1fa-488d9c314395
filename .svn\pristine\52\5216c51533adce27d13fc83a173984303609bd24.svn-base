﻿using ContinuityPatrol.Application.Features.ApprovalMatrix.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ApprovalMatrix.Events;

public class DeleteApprovalMatrixEventTests : IClassFixture<ApprovalMatrixFixture>, IClassFixture<UserActivityFixture>
{
    private readonly ApprovalMatrixFixture _approvalMatrixFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly ApprovalMatrixDeletedEventHandler _handler;

    public DeleteApprovalMatrixEventTests(ApprovalMatrixFixture approvalMatrixFixture, UserActivityFixture userActivityFixture)
    {
        _approvalMatrixFixture = approvalMatrixFixture;
        _userActivityFixture = userActivityFixture;
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var mockApprovalMatrixEventLogger = new Mock<ILogger<ApprovalMatrixDeletedEventHandler>>();

        _mockUserActivityRepository = ApprovalMatrixRepositoryMocks.CreateApprovalMatrixEventRepository(_approvalMatrixFixture.UserActivities);
        _handler = new ApprovalMatrixDeletedEventHandler(mockLoggedInUserService.Object, mockApprovalMatrixEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteApprovalMatrixEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_approvalMatrixFixture.ApprovalMatrixDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_approvalMatrixFixture.ApprovalMatrixDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}