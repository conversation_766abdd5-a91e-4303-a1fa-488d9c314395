using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FastCopyMonitorLogFixture : IDisposable
{
    public List<FastCopyMonitorLog> FastCopyMonitorLogPaginationList { get; set; }
    public List<FastCopyMonitorLog> FastCopyMonitorLogList { get; set; }
    public FastCopyMonitorLog FastCopyMonitorLogDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public FastCopyMonitorLogFixture()
    {
        var fixture = new Fixture();

        FastCopyMonitorLogList = fixture.Create<List<FastCopyMonitorLog>>();

        FastCopyMonitorLogPaginationList = fixture.CreateMany<FastCopyMonitorLog>(20).ToList();

        FastCopyMonitorLogDto = fixture.Create<FastCopyMonitorLog>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
