﻿using ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel;
using ContinuityPatrol.Domain.ViewModels.FormTypeModel;

namespace ContinuityPatrol.Application.Features.FormType.Queries.GetList;

public class GetFormTypeListQueryHandler : IRequestHandler<GetFormTypeListQuery, List<FormTypeListVm>>
{
    private readonly IFormTypeCategoryRepository _formTypeCategoryRepository;
    private readonly IFormTypeRepository _formTypeRepository;
    private readonly IMapper _mapper;

    public GetFormTypeListQueryHandler(IMapper mapper, IFormTypeRepository formTypeRepository,
        IFormTypeCategoryRepository formTypeCategoryRepository)
    {
        _mapper = mapper;
        _formTypeRepository = formTypeRepository;
        _formTypeCategoryRepository = formTypeCategoryRepository;
    }

    public async Task<List<FormTypeListVm>> Handle(GetFormTypeListQuery request, CancellationToken cancellationToken)
    {
        var formType = (await _formTypeRepository.ListAllAsync()).OrderBy(x => x.Id).ToList();

        var formTypes = _mapper.Map<List<FormTypeListVm>>(formType);

        foreach(var x in formTypes)
        {
            var formCategory =await _formTypeCategoryRepository.GetFormTypeCategoryByFormTypeId(x.Id);

            var formTypeListVm = _mapper.Map<List<FormTypeCategoryListVm>>(formCategory);

            x.FormTypeCategory = formTypeListVm;
        };

        return formTypes;
    }
}