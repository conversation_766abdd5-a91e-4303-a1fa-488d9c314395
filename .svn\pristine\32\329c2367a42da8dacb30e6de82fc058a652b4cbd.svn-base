﻿using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ApprovalMatrix.Validators;

public class CreateApprovalMatrixValidatorTests
{
    private readonly Mock<IApprovalMatrixRepository> _mockApprovalMatrixRepository;

    public CreateApprovalMatrixValidatorTests()
    {
        var approvalMatrices = new Fixture().Create<List<Domain.Entities.ApprovalMatrix>>();

        _mockApprovalMatrixRepository = ApprovalMatrixRepositoryMocks.CreateApprovalMatrixRepository(approvalMatrices);
    }

    //Name

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Name_InApprovalMatrix_WithEmpty(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Name_InApprovalMatrix_IsNull(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = null;

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameNotNullRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Name_InApprovalMatrix_MinimumRange(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "PS";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Name_InApprovalMatrix_MaximumRange(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = " PST ";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_DoubleSpace_InFront(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "   PST";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_DoubleSpace_InBack(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "PST  ";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_TripleSpace_InBetween(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "PTS   Chennai";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_SpecialCharacters_InFront(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "&^%$PTS Chennai";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_SpecialCharacters_InBack(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "PTS Chennai&^^%$";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_SpecialCharacters_InBetween(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "PTS*&&^^%$Chennai";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_SpecialCharacters_Only(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "*&*&^%%$#";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_With_UnderScore_InFront(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "_PTS Chennai";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_With_UnderScore_InBack(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "PTS Chennai_";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_With_UnderScore_InFront_AndBack(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "_PTS Chennai_";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_With_Numbers_InFront(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "456PTS Chennai";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_With_UnderScore_InFront_AndNumbers_InBack(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "_PTS Chennai566";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "_456PTS Chennai_";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Valid_Name_InApprovalMatrix_With_Numbers_Only(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Name = "89561287132";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    //Description

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_CreateApprovalMatrixCommandValidator_Description_IsNull(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Description = null;

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createApprovalMatrixCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, validateResult.Errors[0].ErrorMessage);

    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_Create_Description_InApprovalMatrix_MaximumRange(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.Description = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixDescriptionRangeRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    ////UserName

    //[Theory]
    //[AutoApprovalMatrixData]
    //public async Task Verify_Create_UserName_InApprovalMatrix_WithEmpty(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    //{
    //    var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

    //    createApprovalMatrixCommand.UserName = "";

    //    var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixUserNameRequired, (string)validateResult.Errors[1].ErrorMessage);
    //}

    //[Theory]
    //[AutoApprovalMatrixData]
    //public async Task Verify_Create_UserName_InApprovalMatrix_IsNull(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    //{
    //    var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

    //    createApprovalMatrixCommand.UserName = null;

    //    var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixUserNameNotNullRequired, (string)validateResult.Errors[2].ErrorMessage);
    //}

    //BusinessServiceName

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_CreateApprovalMatrixCommandValidator_BusinessServiceName_WithEmpty(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.BusinessServiceName = "";

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createApprovalMatrixCommand);

        Assert.Contains("Description contains invalid characters.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixDescriptionContainsRequired, validateResult.Errors[1].ErrorMessage);

    }

    [Theory]
    [AutoApprovalMatrixData]
    public async Task Verify_CreateApprovalMatrixCommandValidator_BusinessServiceName_IsNull(CreateApprovalMatrixCommand createApprovalMatrixCommand)
    {
        var validator = new CreateApprovalMatrixCommandValidator(_mockApprovalMatrixRepository.Object);

        createApprovalMatrixCommand.BusinessServiceName = null;

        var validateResult = await validator.ValidateAsync(createApprovalMatrixCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createApprovalMatrixCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.ApprovalMatrix.ApprovalMatrixNameValidRequired, validateResult.Errors[0].ErrorMessage);

    }
}