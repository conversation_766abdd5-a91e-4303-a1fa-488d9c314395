﻿using ContinuityPatrol.Application.Features.WorkflowActionType.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionType.Events;

public class WorkflowActionTypeUpdatedEventHandlerTests : IClassFixture<WorkflowActionTypeFixture>
{
    private readonly WorkflowActionTypeFixture _workflowActionTypeFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly WorkflowActionTypeUpdatedEventHandler _handler;

    public WorkflowActionTypeUpdatedEventHandlerTests(WorkflowActionTypeFixture workflowActionTypeFixture)
    {
        _workflowActionTypeFixture = workflowActionTypeFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockWorkflowActionTypeEventLogger = new Mock<ILogger<WorkflowActionTypeUpdatedEventHandler>>();

        _mockUserActivityRepository = WorkflowActionTypeRepositoryMocks.CreateWorkflowActionTypeEventRepository(_workflowActionTypeFixture.UserActivities);

        _handler = new WorkflowActionTypeUpdatedEventHandler(mockLoggedInUserService.Object, mockWorkflowActionTypeEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateWorkflowActionTypeEventUpdated()
    {
        _workflowActionTypeFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowActionTypeFixture.WorkflowActionTypeUpdatedEvent, CancellationToken.None);

        result.Equals(_workflowActionTypeFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowActionTypeFixture.WorkflowActionTypeUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_UpdateWorkflowActionTypeEventUpdated()
    {
        _workflowActionTypeFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowActionTypeFixture.WorkflowActionTypeUpdatedEvent, CancellationToken.None);

        result.Equals(_workflowActionTypeFixture.UserActivities[0].Id);

        result.Equals(_workflowActionTypeFixture.WorkflowActionTypeUpdatedEvent.ActionType);

        await Task.CompletedTask;
    }
}