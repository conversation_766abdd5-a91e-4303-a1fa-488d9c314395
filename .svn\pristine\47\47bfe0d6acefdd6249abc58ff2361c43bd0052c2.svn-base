﻿using ContinuityPatrol.Application.Features.TableAccess.Commands.Create;
using ContinuityPatrol.Application.Features.TableAccess.Commands.Delete;
using ContinuityPatrol.Application.Features.TableAccess.Commands.Update;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetList;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetSchemaNameList;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetTableNameListBySchema;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class TableAccessService : BaseService, ITableAccessService
{
    public TableAccessService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateTableAccessCommand createTableAccessCommand)
    {
        Logger.LogDebug($"Create Table Access '{createTableAccessCommand.TableName}'");

        return await Mediator.Send(createTableAccessCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateTableAccessCommand updateTableAccessCommand)
    {
        Logger.LogDebug($"Update TableAccess '{updateTableAccessCommand.UpdateTableAccess.FirstOrDefault()?.TableName}'");

        return await Mediator.Send(updateTableAccessCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string tableAccessId)
    {
        Guard.Against.InvalidGuidOrEmpty(tableAccessId, "TableAccess Id");

        Logger.LogDebug($"Delete TableAccess Details by Id '{tableAccessId}'");

        return await Mediator.Send(new DeleteTableAccessCommand { Id = tableAccessId });
    }

    public async Task<PaginatedResult<TableAccessListVm>> GetTableAccessPaginatedList(
        GetTableAccessPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in TableAccess Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<bool> IsTableAccessNameExist(string tableAccessName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(tableAccessName, "TableAccess Name");

        Logger.LogDebug($"Check Name Exists Detail by TableAccess Name '{tableAccessName}' and Id '{id}'");

        return await Mediator.Send(new GetTableAccessNameUniqueQuery
            { TableAccessName = tableAccessName, TableAccessId = id });
    }

    public async Task<List<GetTableNameListBySchemaVm>> GetTableNamesBySchemaName(string schemaName)
    {
        Logger.LogDebug($"Get All TableName List by Schema '{schemaName}'");

        return await Mediator.Send(new GetTableNameListBySchemaQuery { SchemaName = schemaName });
    }

    public async Task<List<SchemaNameListVm>> GetSchemaNames()
    {
        Logger.LogDebug("Get All SchemaName List");

        return await Mediator.Send(new GetSchemaNameListQuery());
    }

    public async Task<List<TableAccessListVm>> GetAllTableAccesses()
    {
        Logger.LogDebug("Get All TableAccess List");

        return await Mediator.Send(new GetTableAccessListQuery());
    }
}