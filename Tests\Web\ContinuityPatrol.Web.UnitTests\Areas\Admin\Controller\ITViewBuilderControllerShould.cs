﻿using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByInfraObjectId;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class ITViewBuilderControllerShould
    {
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private ITViewBuilderController _controller;

        public ITViewBuilderControllerShould()
        {
            Initialize();
        }

        internal void Initialize()
        {
            _controller = new ITViewBuilderController(
                _mockLoggedInUserService.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public void List_ReturnsViewResult()
        {
            // Act
            var result = _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
        }

        [Fact]
        public async Task GetDashboardViewListByInfraObjectId_ReturnsJsonResultWithSuccess()
        {
            // Arrange
            var infraId = "testInfraId";
            var dashboardViewData = new GetDashboardViewByInfraObjectIdVm
            {
                InfraObjectId = infraId,
                InfraObjectName = "Test Infrastructure"
            };
            _mockDataProvider.Setup(dp => dp.DashboardView.GetDashboardViewListByInfraObjectId(infraId))
                            .ReturnsAsync(dashboardViewData);

            // Act
            var result = await _controller.GetDashboardViewListByInfraObjectId(infraId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task GetDashboardViewListByInfraObjectId_ReturnsJsonException_WhenExceptionOccurs()
        {
            // Arrange
            var infraId = "testInfraId";
            _mockDataProvider.Setup(dp => dp.DashboardView.GetDashboardViewListByInfraObjectId(infraId))
                            .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetDashboardViewListByInfraObjectId(infraId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task GetDashboardViewListByInfraObjectId_ReturnsJsonResultWithSuccess_WhenInfraIdIsNull()
        {
            // Arrange
            string infraId = null;
            var dashboardViewData = new GetDashboardViewByInfraObjectIdVm();
            _mockDataProvider.Setup(dp => dp.DashboardView.GetDashboardViewListByInfraObjectId(infraId))
                            .ReturnsAsync(dashboardViewData);

            // Act
            var result = await _controller.GetDashboardViewListByInfraObjectId(infraId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task GetDashboardViewListByInfraObjectId_ReturnsJsonResultWithSuccess_WhenInfraIdIsEmpty()
        {
            // Arrange
            var infraId = "";
            var dashboardViewData = new GetDashboardViewByInfraObjectIdVm();
            _mockDataProvider.Setup(dp => dp.DashboardView.GetDashboardViewListByInfraObjectId(infraId))
                            .ReturnsAsync(dashboardViewData);

            // Act
            var result = await _controller.GetDashboardViewListByInfraObjectId(infraId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task GetDashboardViewListByInfraObjectId_ReturnsJsonResultWithEmptyData_WhenNoDataFound()
        {
            // Arrange
            var infraId = "nonExistentId";
            var emptyDashboardViewData = new GetDashboardViewByInfraObjectIdVm();
            _mockDataProvider.Setup(dp => dp.DashboardView.GetDashboardViewListByInfraObjectId(infraId))
                            .ReturnsAsync(emptyDashboardViewData);

            // Act
            var result = await _controller.GetDashboardViewListByInfraObjectId(infraId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);
            Assert.NotNull(jsonResult);
        }

        [Fact]
        public async Task GetDashboardViewListByInfraObjectId_CallsDataProviderWithCorrectParameter()
        {
            // Arrange
            var infraId = "specificInfraId";
            var dashboardViewData = new GetDashboardViewByInfraObjectIdVm();
            _mockDataProvider.Setup(dp => dp.DashboardView.GetDashboardViewListByInfraObjectId(infraId))
                            .ReturnsAsync(dashboardViewData);

            // Act
            await _controller.GetDashboardViewListByInfraObjectId(infraId);

            // Assert
            _mockDataProvider.Verify(dp => dp.DashboardView.GetDashboardViewListByInfraObjectId(infraId), Times.Once);
        }
    }
}
