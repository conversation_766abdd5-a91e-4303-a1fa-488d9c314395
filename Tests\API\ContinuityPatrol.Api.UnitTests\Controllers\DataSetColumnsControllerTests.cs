using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DataSetColumns.Commands.Create;
using ContinuityPatrol.Application.Features.DataSetColumns.Queries.GetColumnNames;
using ContinuityPatrol.Application.Features.DataSetColumns.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.DataSetColumnsModel;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DataSetColumnsControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DataSetColumnsController _controller;
    private readonly DataSetColumnsFixture _dataSetColumnsFixture;

    public DataSetColumnsControllerTests()
    {
        _dataSetColumnsFixture = new DataSetColumnsFixture();

        var testBuilder = new ControllerTestBuilder<DataSetColumnsController>();
        _controller = testBuilder.CreateController(
            _ => new DataSetColumnsController(),
            out _mediatorMock);
    }

    #region Core CRUD Operations

    [Fact]
    public async Task CreateDataSetColumns_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _dataSetColumnsFixture.CreateDataSetColumnsCommand;
        var expectedResponse = _dataSetColumnsFixture.CreateDataSetColumnsResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataSetColumns(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataSetColumnsResponse>(createdResult.Value);
        Assert.Equal("Enterprise DataSet Column created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.DataSetColumnsId);
    }

    [Fact]
    public async Task GetDataSetColumns_ReturnsOkResult()
    {
        // Arrange
        var dataSetColumnsList = new List<DataSetColumnsListVm> { _dataSetColumnsFixture.DataSetColumnsListVm };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataSetColumnsListQuery>(), default))
            .ReturnsAsync(dataSetColumnsList);

        // Act
        var result = await _controller.GetDataSetColumns();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataSetColumnsListVm>>(okResult.Value);
        Assert.Single(returnedList);
        Assert.Equal("Enterprise_Business_Service", returnedList.First().TableName);
        Assert.Equal("business_service_id", returnedList.First().ColumnName);
      
    }

    [Fact]
    public async Task GetColumnNamesByDbAndTableName_WithValidParameters_ReturnsOkResult()
    {
        // Arrange
        var dbName = "ContinuityPatrol_Enterprise";
        var tableName = "Enterprise_Configuration_Table";
        var expectedColumn = new DataSetColumnsColumnNameVm
        {
            ColumnName = "enterprise_data_column",
            ColumnKey = "Column key value",
            IsPrimaryKey = false
        };
        var columnNamesList = new List<DataSetColumnsColumnNameVm> { expectedColumn };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSetColumnsColumnNameQuery>(q =>
                q.DBName == dbName && q.TableName == tableName), default))
            .ReturnsAsync(columnNamesList);

        // Act
        var result = await _controller.GetColumnNamesByDbAndTableName(dbName, tableName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataSetColumnsColumnNameVm>>(okResult.Value);
        Assert.Single(returnedList);
        Assert.Equal("enterprise_data_column", returnedList.First().ColumnName);
        Assert.Equal("Column key value", returnedList.First().ColumnKey);
        Assert.False(returnedList.First().IsPrimaryKey);
    }

    #endregion

    #region Error Handling

    [Fact]
    public async Task CreateDataSetColumns_WhenMediatorThrowsException_PropagatesException()
    {
        // Arrange
        var command = _dataSetColumnsFixture.CreateDataSetColumnsCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("Database schema validation failed"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.CreateDataSetColumns(command));
        Assert.Contains("Database schema validation failed", exception.Message);
    }

    [Fact]
    public async Task GetColumnNamesByDbAndTableName_WhenMediatorThrowsException_PropagatesException()
    {
        // Arrange
        var dbName = "InvalidDB";
        var tableName = "InvalidTable";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataSetColumnsColumnNameQuery>(), default))
            .ThrowsAsync(new ArgumentException("Table not found in specified database"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(() => 
            _controller.GetColumnNamesByDbAndTableName(dbName, tableName));
        Assert.Contains("Table not found in specified database", exception.Message);
    }

    #endregion

    #region ClearDataCache

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act
        _controller.ClearDataCache();

        // Assert - This test verifies the method executes without throwing exceptions
        // The actual cache clearing logic is tested in integration tests
        Assert.True(true);
    }

    #endregion

    

    [Fact]
    public async Task GetDataSetColumns_HandlesEmptyList()
    {
        // Arrange
        var emptyList = new List<DataSetColumnsListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataSetColumnsListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDataSetColumns();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataSetColumnsListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task CreateDataSetColumns_HandlesComplexEnterpriseColumn()
    {
        // Arrange
        var complexCommand = new CreateDataSetColumnsCommand
        {
            TableName = "Enterprise_Complex_Business_Analytics",
            ColumnName = "aggregated_performance_metrics_json",
            DataSetId=Guid.NewGuid().ToString()
        };

        var expectedResponse = new CreateDataSetColumnsResponse
        {
            DataSetColumnsId = Guid.NewGuid().ToString(),
            Message = "Enterprise Complex Business Analytics column created successfully!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(complexCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataSetColumns(complexCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataSetColumnsResponse>(createdResult.Value);
        
        Assert.Equal("Enterprise Complex Business Analytics column created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);

        // Validate complex column properties
        Assert.Equal("Enterprise_Complex_Business_Analytics", complexCommand.TableName);
        Assert.Equal("aggregated_performance_metrics_json", complexCommand.ColumnName);
        Assert.NotNull(complexCommand.DataSetId);
      
    }

    [Fact]
    public async Task GetColumnNamesByDbAndTableName_HandlesMultipleColumnsWithDifferentTypes()
    {
        // Arrange
        var dbName = "ContinuityPatrol_Production";
        var tableName = "Enterprise_Multi_Column_Table";
        var multipleColumnsList = new List<DataSetColumnsColumnNameVm>
        {
            new DataSetColumnsColumnNameVm
            {
                ColumnName = "id",
                ColumnKey = "uniqueidentifier",
                IsPrimaryKey = true,
               
            },
            new DataSetColumnsColumnNameVm
            {
                ColumnName = "name",
                ColumnKey="colunkey",
                IsPrimaryKey = false,
               
            },
            new DataSetColumnsColumnNameVm
            {
                ColumnName = "created_date",
                IsPrimaryKey = false,
                ColumnKey = "getutcdate()",
            },
            new DataSetColumnsColumnNameVm
            {
                ColumnName = "configuration_json",
                ColumnKey = "nvarchar",
                IsPrimaryKey = false,
                },
               
            new DataSetColumnsColumnNameVm
            {
                ColumnName = "is_active",
                ColumnKey = "bit",
                IsPrimaryKey = false,
                
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSetColumnsColumnNameQuery>(q => 
                q.DBName == dbName && q.TableName == tableName), default))
            .ReturnsAsync(multipleColumnsList);

        // Act
        var result = await _controller.GetColumnNamesByDbAndTableName(dbName, tableName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataSetColumnsColumnNameVm>>(okResult.Value);
        
        Assert.Equal(5, returnedList.Count);
        
        // Validate primary key column
        var primaryKeyColumn = returnedList.First(c => c.IsPrimaryKey);
        Assert.Equal("id", primaryKeyColumn.ColumnName);
        Assert.Equal("uniqueidentifier", primaryKeyColumn.ColumnKey);
      
        
        // Validate string column
        var nameColumn = returnedList.First(c => c.ColumnName == "name");
        Assert.Equal("colunkey", nameColumn.ColumnKey);
      
        
        // Validate datetime column
        var dateColumn = returnedList.First(c => c.ColumnName == "created_date");
       
        Assert.Equal("getutcdate()", dateColumn.ColumnKey);
        
        // Validate JSON column (nvarchar(MAX))
        var jsonColumn = returnedList.First(c => c.ColumnName == "configuration_json");
        Assert.Equal("nvarchar", jsonColumn.ColumnKey);
      
        // Validate boolean column
        var boolColumn = returnedList.First(c => c.ColumnName == "is_active");
        Assert.Equal("bit", boolColumn.ColumnKey);

      
    }



    [Fact]
    public async Task CreateDataSetColumns_HandlesLargeScaleDataWarehouse()
    {
        // Arrange
        var warehouseCommand = _dataSetColumnsFixture.CreateDataSetColumnsCommand;
        warehouseCommand.TableName = "Enterprise_Data_Warehouse_Fact_Table";
        warehouseCommand.ColumnName = "fact_sales_aggregated_metrics";

        var expectedResponse = _dataSetColumnsFixture.CreateDataSetColumnsResponse;

        _mediatorMock
            .Setup(m => m.Send(warehouseCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataSetColumns(warehouseCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataSetColumnsResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise_Data_Warehouse_Fact_Table", warehouseCommand.TableName);
        Assert.Equal("fact_sales_aggregated_metrics", warehouseCommand.ColumnName);
    }

    [Fact]
    public async Task GetDataSetColumns_HandlesEmptyColumnSet()
    {
        // Arrange
        var emptyColumnsList = new List<DataSetColumnsListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataSetColumnsListQuery>(), default))
            .ReturnsAsync(emptyColumnsList);

        // Act
        var result = await _controller.GetDataSetColumns();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataSetColumnsListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetColumnNamesByDbAndTableName_HandlesComplexJoinTables()
    {
        // Arrange
        var dbName = "ContinuityPatrol_Analytics";
        var tableName = "Enterprise_Complex_Join_View";
        var complexColumns = new List<DataSetColumnsColumnNameVm>
        {
            new DataSetColumnsColumnNameVm
            {
                ColumnName = "business_service_id",
                ColumnKey = "uniqueidentifier",
                IsPrimaryKey = true
            },
            new DataSetColumnsColumnNameVm
            {
                ColumnName = "aggregated_metrics_json",
                ColumnKey = "nvarchar(max)",
                IsPrimaryKey = false
            },
            new DataSetColumnsColumnNameVm
            {
                ColumnName = "calculated_performance_score",
                ColumnKey = "decimal(18,4)",
                IsPrimaryKey = false
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSetColumnsColumnNameQuery>(q =>
                q.DBName == dbName && q.TableName == tableName), default))
            .ReturnsAsync(complexColumns);

        // Act
        var result = await _controller.GetColumnNamesByDbAndTableName(dbName, tableName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataSetColumnsColumnNameVm>>(okResult.Value);

        Assert.Equal(3, returnedList.Count);
        Assert.Contains(returnedList, c => c.ColumnName == "business_service_id" && c.IsPrimaryKey);
        Assert.Contains(returnedList, c => c.ColumnName == "aggregated_metrics_json" && c.ColumnKey == "nvarchar(max)");
        Assert.Contains(returnedList, c => c.ColumnName == "calculated_performance_score" && c.ColumnKey == "decimal(18,4)");
    }

    [Fact]
    public async Task CreateDataSetColumns_HandlesTemporalDataColumns()
    {
        // Arrange
        var temporalCommand = new CreateDataSetColumnsCommand
        {
            TableName = "Enterprise_Temporal_Analytics",
            ColumnName = "system_time_period_start",
            DataSetId = Guid.NewGuid().ToString()
        };

        var expectedResponse = new CreateDataSetColumnsResponse
        {
            DataSetColumnsId = Guid.NewGuid().ToString(),
            Message = "Temporal column created successfully for enterprise analytics!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(temporalCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataSetColumns(temporalCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataSetColumnsResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Contains("Temporal column created successfully", returnedResponse.Message);
        Assert.Equal("Enterprise_Temporal_Analytics", temporalCommand.TableName);
        Assert.Equal("system_time_period_start", temporalCommand.ColumnName);
    }

    [Fact]
    public async Task GetColumnNamesByDbAndTableName_HandlesEncryptedColumns()
    {
        // Arrange
        var dbName = "ContinuityPatrol_Secure";
        var tableName = "Enterprise_Encrypted_Data";
        var encryptedColumns = new List<DataSetColumnsColumnNameVm>
        {
            new DataSetColumnsColumnNameVm
            {
                ColumnName = "encrypted_customer_data",
                ColumnKey = "varbinary(max)",
                IsPrimaryKey = false
            },
            new DataSetColumnsColumnNameVm
            {
                ColumnName = "encryption_key_id",
                ColumnKey = "uniqueidentifier",
                IsPrimaryKey = false
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSetColumnsColumnNameQuery>(q =>
                q.DBName == dbName && q.TableName == tableName), default))
            .ReturnsAsync(encryptedColumns);

        // Act
        var result = await _controller.GetColumnNamesByDbAndTableName(dbName, tableName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataSetColumnsColumnNameVm>>(okResult.Value);

        Assert.Equal(2, returnedList.Count);
        Assert.Contains(returnedList, c => c.ColumnName == "encrypted_customer_data" && c.ColumnKey == "varbinary(max)");
        Assert.Contains(returnedList, c => c.ColumnName == "encryption_key_id" && c.ColumnKey == "uniqueidentifier");
    }

    
}
