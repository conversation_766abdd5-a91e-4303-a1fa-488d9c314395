﻿using ContinuityPatrol.Application.Features.RsyncJob.Commands.Update;
using ContinuityPatrol.Application.Features.RsyncJob.Events.Update;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.RsyncJob.Commands
{
    public class UpdateRsyncJobTests
    {
        private readonly Mock<IRsyncJobRepository> _mockRsyncJobRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly UpdateRsyncJobCommandHanlder _handler;

        public UpdateRsyncJobTests()
        {
            _mockRsyncJobRepository = new Mock<IRsyncJobRepository>();
            _mockMapper = new Mock<IMapper>();
            _mockPublisher = new Mock<IPublisher>();
            _handler = new UpdateRsyncJobCommandHanlder(
                _mockRsyncJobRepository.Object,
                _mockMapper.Object,
                _mockPublisher.Object
            );
        }

        [Fact]
        public async Task Handle_UpdatesRsyncJob_Successfully()
        {
            var command = new UpdateRsyncJobCommand
            {
                Id = Guid.NewGuid().ToString(),
                ReplicationName = "UpdatedJob"
            };

            var existingJob = new Domain.Entities.RsyncJob
            {
                ReferenceId = command.Id,
                ReplicationName = "OriginalJob"
            };

            _mockRsyncJobRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(existingJob);
            _mockRsyncJobRepository.Setup(repo => repo.UpdateAsync(existingJob)).Returns(ToString);
            _mockMapper.Setup(mapper => mapper.Map(command, existingJob, typeof(UpdateRsyncJobCommand), typeof(Domain.Entities.RsyncJob)))
                .Callback(() =>
                {
                    existingJob.ReplicationName = command.ReplicationName;
                });
            _mockPublisher.Setup(pub => pub.Publish(It.IsAny<RsyncJobUpdatedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(command.Id, result.Id);
            Assert.Contains(command.ReplicationName, result.Message);
            _mockRsyncJobRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRsyncJobRepository.Verify(repo => repo.UpdateAsync(existingJob), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RsyncJobUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenJobDoesNotExist()
        {
            var command = new UpdateRsyncJobCommand
            {
                Id = Guid.NewGuid().ToString(),
            };

            _mockRsyncJobRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync((Domain.Entities.RsyncJob)null);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Contains(nameof(Domain.Entities.RsyncJob), exception.Message);
            _mockRsyncJobRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRsyncJobRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RsyncJob>()), Times.Never);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RsyncJobUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenRepositoryUpdateFails()
        {
            var command = new UpdateRsyncJobCommand
            {
                Id = Guid.NewGuid().ToString(),
                ReplicationName = "UpdatedJob"
            };

            var existingJob = new Domain.Entities.RsyncJob
            {
                ReferenceId = command.Id,
                ReplicationName = "OriginalJob"
            };

            _mockRsyncJobRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(existingJob);
            _mockRsyncJobRepository.Setup(repo => repo.UpdateAsync(existingJob))
                .Throws(new Exception("Update failed"));

            _mockMapper.Setup(mapper => mapper.Map(command, existingJob, typeof(UpdateRsyncJobCommand), typeof(Domain.Entities.RsyncJob)))
                .Callback(() =>
                {
                    existingJob.ReplicationName = command.ReplicationName;
                });

            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Equal("Update failed", exception.Message);
            _mockRsyncJobRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRsyncJobRepository.Verify(repo => repo.UpdateAsync(existingJob), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RsyncJobUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenPublisherFails()
        {
            var command = new UpdateRsyncJobCommand
            {
                Id = Guid.NewGuid().ToString(),
                ReplicationName = "UpdatedJob"
            };

            var existingJob = new Domain.Entities.RsyncJob
            {
                ReferenceId = command.Id,
                ReplicationName = "OriginalJob"
            };

            _mockRsyncJobRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(existingJob);
            _mockRsyncJobRepository.Setup(repo => repo.UpdateAsync(existingJob)).Returns(ToString);
            _mockMapper.Setup(mapper => mapper.Map(command, existingJob, typeof(UpdateRsyncJobCommand), typeof(Domain.Entities.RsyncJob)))
                .Callback(() =>
                {
                    existingJob.ReplicationName = command.ReplicationName;
                });
            _mockPublisher.Setup(pub => pub.Publish(It.IsAny<RsyncJobUpdatedEvent>(), It.IsAny<CancellationToken>()))
                .Throws(new Exception("Publisher failed"));

            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Equal("Publisher failed", exception.Message);
            _mockRsyncJobRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRsyncJobRepository.Verify(repo => repo.UpdateAsync(existingJob), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RsyncJobUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}
