﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CredentialProfile.Events.Delete;

public class CredentialProfileDeletedEventHandler : INotificationHandler<CredentialProfileDeletedEvent>
{
    private readonly ILogger<CredentialProfileDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CredentialProfileDeletedEventHandler(ILoggedInUserService userService,
        ILogger<CredentialProfileDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(CredentialProfileDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.CredentialProfile.ToString(),
            Action = $"{ActivityType.Delete} {Modules.CredentialProfile}",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $" CredentialProfile '{deletedEvent.CredentialProfileName}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"CredentialProfile '{deletedEvent.CredentialProfileName}' deleted successfully.");
    }
}