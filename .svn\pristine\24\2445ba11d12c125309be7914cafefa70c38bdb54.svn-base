using ContinuityPatrol.Domain.ViewModels.CyberSnapsModel;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;
using System.Linq.Expressions;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface ICyberSnapsRepository : IRepository<CyberSnaps>
{
    Task<bool> IsNameExist(string name, string id);
    Task<List<CyberSnaps>> GetCyberSnapsBySnapTagName(string cyberSnapTagName, string startDate, string endDate);
    Task<List<CyberSnaps>> GetCyberSnapsListByDate(string startDate, string endDate);
    Task<List<CyberSnaps>> GetCyberSnapsByStorageGroupNameAndLinkedStatus(Expression<Func<CyberSnaps, bool>> expression);
    Task<PaginatedResult<CyberSnapsListVm>> GetCyberSnapsByDateTime(int pageNumber, int pageSize, Specification<CyberSnaps> specification, string sortColumn, string sortOrder,DateTime startDate,DateTime endDate);
}