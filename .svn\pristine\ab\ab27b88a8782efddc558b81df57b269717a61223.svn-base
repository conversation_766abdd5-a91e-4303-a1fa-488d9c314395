using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPowerMax;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class PowerMaxMonitorStatusRepositoryTests : IClassFixture<PowerMaxMonitorStatusFixture>, IDisposable
{
    private readonly PowerMaxMonitorStatusFixture _powerMaxMonitorStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly PowerMaxMonitorStatusRepository _repository;

    public PowerMaxMonitorStatusRepositoryTests(PowerMaxMonitorStatusFixture powerMaxMonitorStatusFixture)
    {
        _powerMaxMonitorStatusFixture = powerMaxMonitorStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new PowerMaxMonitorStatusRepository(_dbContext);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.PowerMaxMonitorStatus.RemoveRange(_dbContext.PowerMaxMonitorStatus);
        await _dbContext.SaveChangesAsync();
    }

    #region Constructor Tests

    [Fact]
    public void Constructor_ShouldCreateInstance_WhenValidParametersProvided()
    {
        // Arrange & Act
        var repository = new PowerMaxMonitorStatusRepository(_dbContext);

        // Assert
        Assert.NotNull(repository);
    }

    #endregion

    #region GetPowerMaxMonitorStatusByName Tests

    [Fact]
    public async Task GetPowerMaxMonitorStatusByName_ShouldReturnAllStatuses_WhenNameIsNull()
    {
        // Arrange
        await ClearDatabase();
        var statuses = _powerMaxMonitorStatusFixture.PowerMaxMonitorStatusList.Take(3).ToList();
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetPowerMaxMonitorStatusByName(null, false);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task GetPowerMaxMonitorStatusByName_ShouldReturnAllStatuses_WhenNameIsEmpty()
    {
        // Arrange
        await ClearDatabase();
        var statuses = _powerMaxMonitorStatusFixture.PowerMaxMonitorStatusList.Take(3).ToList();
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetPowerMaxMonitorStatusByName("", false);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task GetPowerMaxMonitorStatusByName_ShouldReturnAllStatuses_WhenNameIsWhitespace()
    {
        // Arrange
        await ClearDatabase();
        var statuses = _powerMaxMonitorStatusFixture.PowerMaxMonitorStatusList.Take(3).ToList();
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetPowerMaxMonitorStatusByName("   ", false);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
    }

    //[Fact]
    //public async Task GetPowerMaxMonitorStatusByName_ShouldReturnEmptyList_WhenNoActiveStatuses()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var inactiveStatus = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithProperties(isActive: false);
    //    await _repository.AddAsync(inactiveStatus);

    //    // Act
    //    var result = await _repository.GetPowerMaxMonitorStatusByName(null, false);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Empty(result);
    //}

    [Fact]
    public async Task GetPowerMaxMonitorStatusByName_ShouldFilterByStorageGroupName_WhenIsSnapIsFalse()
    {
        // Arrange
        await ClearDatabase();
        var targetStorageGroupName = "SG_Target";
        var targetStatus = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithCustomProperties(targetStorageGroupName);
        var otherStatus = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithCustomProperties("SG_Other");
        
        await _repository.AddAsync(targetStatus);
        await _repository.AddAsync(otherStatus);

        // Act
        var result = await _repository.GetPowerMaxMonitorStatusByName(targetStorageGroupName, false);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Single(result.First().StorageGroupMonitoring);
        Assert.Equal(targetStorageGroupName, result.First().StorageGroupMonitoring.First().StorageGroupName);
    }

    [Fact]
    public async Task GetPowerMaxMonitorStatusByName_ShouldFilterBySnapshotName_WhenIsSnapIsTrue()
    {
        // Arrange
        await ClearDatabase();
        var targetSnapshotName = "Snap_Target";
        var targetStatus = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithCustomProperties("SG_Test", targetSnapshotName);
        var otherStatus = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithCustomProperties("SG_Test", "Snap_Other");
        
        await _repository.AddAsync(targetStatus);
        await _repository.AddAsync(otherStatus);

        // Act
        var result = await _repository.GetPowerMaxMonitorStatusByName(targetSnapshotName, true);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Single(result.First().StorageGroupMonitoring);
        Assert.Single(result.First().StorageGroupMonitoring.First().SnapshotDetails);
        Assert.Equal(targetSnapshotName, result.First().StorageGroupMonitoring.First().SnapshotDetails.First().SnapshotName);
    }

    [Fact]
    public async Task GetPowerMaxMonitorStatusByName_ShouldHandlePartialSnapshotNameMatch_WhenIsSnapIsTrue()
    {
        // Arrange
        await ClearDatabase();
        var partialSnapshotName = "Target";
        var fullSnapshotName = "Snap_Target_01";
        var targetStatus = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithCustomProperties("SG_Test", fullSnapshotName);
        
        await _repository.AddAsync(targetStatus);

        // Act
        var result = await _repository.GetPowerMaxMonitorStatusByName(partialSnapshotName, true);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Single(result.First().StorageGroupMonitoring);
        Assert.Single(result.First().StorageGroupMonitoring.First().SnapshotDetails);
        Assert.Equal(fullSnapshotName, result.First().StorageGroupMonitoring.First().SnapshotDetails.First().SnapshotName);
    }

    [Fact]
    public async Task GetPowerMaxMonitorStatusByName_ShouldReturnEmptyList_WhenStorageGroupNotFound()
    {
        // Arrange
        await ClearDatabase();
        var status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithCustomProperties("SG_Existing");
        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetPowerMaxMonitorStatusByName("SG_NonExistent", false);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetPowerMaxMonitorStatusByName_ShouldReturnEmptyList_WhenSnapshotNotFound()
    {
        // Arrange
        await ClearDatabase();
        var status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithCustomProperties("SG_Test", "Snap_Existing");
        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetPowerMaxMonitorStatusByName("Snap_NonExistent", true);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetPowerMaxMonitorStatusByName_ShouldHandleLargeDataset()
    {
        // Arrange
        await ClearDatabase();
        var statuses = new List<PowerMaxMonitorStatus>();
        for (int i = 0; i < 50; i++)
        {
            statuses.Add(_powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithCustomProperties($"SG_Test_{i}"));
        }
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetPowerMaxMonitorStatusByName(null, false);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(50, result.Count);
    }

    [Fact]
    public async Task GetPowerMaxMonitorStatusByName_ShouldReturnCompleteEntityStructure()
    {
        // Arrange
        await ClearDatabase();
        var status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithCustomProperties("SG_Complete", "Snap_Complete");
        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetPowerMaxMonitorStatusByName(null, false);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        
        var powerMaxDetail = result.First();
        Assert.NotNull(powerMaxDetail.IpAddress);
        Assert.NotNull(powerMaxDetail.Version);
        Assert.NotNull(powerMaxDetail.Array);
        Assert.NotNull(powerMaxDetail.ModelName);
        Assert.NotNull(powerMaxDetail.StorageGroupMonitoring);
        Assert.Single(powerMaxDetail.StorageGroupMonitoring);
        
        var storageGroup = powerMaxDetail.StorageGroupMonitoring.First();
        Assert.Equal("SG_Complete", storageGroup.StorageGroupName);
        Assert.NotNull(storageGroup.Compliance);
        Assert.NotNull(storageGroup.SRP);
        Assert.NotNull(storageGroup.ServiceLevel);
        Assert.NotNull(storageGroup.Capacity);
        Assert.NotNull(storageGroup.Emulation);
        Assert.NotNull(storageGroup.SRDFReplicationstatus);
        Assert.NotNull(storageGroup.SnapshotCount);
        Assert.NotNull(storageGroup.SnapshotDetails);
        Assert.Single(storageGroup.SnapshotDetails);
        
        var snapshot = storageGroup.SnapshotDetails.First();
        Assert.Equal("Snap_Complete", snapshot.SnapshotName);
        Assert.NotNull(snapshot.SnapshotId);
        Assert.NotNull(snapshot.CreationTime);
        Assert.NotNull(snapshot.LinkedStatus);
        Assert.NotNull(snapshot.Restored);
        Assert.NotNull(snapshot.Expired);
        Assert.NotNull(snapshot.ExpiryTime);
        Assert.NotNull(snapshot.Secured);
    }

    #endregion

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddPowerMaxMonitorStatus_WhenValidEntityProvided()
    {
        // Arrange
        await ClearDatabase();
        var status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithProperties();

        // Act
        var result = await _repository.AddAsync(status);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(status.Type, result.Type);
        Assert.Equal(status.InfraObjectId, result.InfraObjectId);
        
        var savedEntity = await _repository.GetByReferenceIdAsync(result.ReferenceId);
        Assert.NotNull(savedEntity);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdatePowerMaxMonitorStatus_WhenValidEntityProvided()
    {
        // Arrange
        await ClearDatabase();
        var status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithProperties();
        await _repository.AddAsync(status);

        // Modify the entity
        status.Type = "UpdatedType";
        status.InfraObjectName = "UpdatedInfraObjectName";

        // Act
        var result = await _repository.UpdateAsync(status);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedType", result.Type);
        Assert.Equal("UpdatedInfraObjectName", result.InfraObjectName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldDeletePowerMaxMonitorStatus_WhenValidEntityProvided()
    {
        // Arrange
        await ClearDatabase();
        var status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithProperties();
        await _repository.AddAsync(status);

        // Act
        var result = await _repository.DeleteAsync(status);

        // Assert
        Assert.NotNull(result);
        
        // Verify entity is deleted
        var deletedEntity = await _dbContext.PowerMaxMonitorStatus.FindAsync(status.Id);
        Assert.Null(deletedEntity);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region AddRangeAsync Tests

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultiplePowerMaxMonitorStatuses()
    {
        // Arrange
        await ClearDatabase();
        var statuses = _powerMaxMonitorStatusFixture.PowerMaxMonitorStatusList.Take(3).ToList();

        // Act
        var result = await _repository.AddRangeAsync(statuses);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count());
        
        var allStatuses = await _repository.ListAllAsync();
        Assert.Equal(3, allStatuses.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllPowerMaxMonitorStatuses()
    {
        // Arrange
        await ClearDatabase();
        var statuses = _powerMaxMonitorStatusFixture.PowerMaxMonitorStatusList.Take(4).ToList();
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(4, result.Count);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnPowerMaxMonitorStatus_WhenIdExists()
    {
        // Arrange
        await ClearDatabase();
        var status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithProperties();
        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetByReferenceIdAsync(status.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(status.ReferenceId, result.ReferenceId);
        Assert.Equal(status.Type, result.Type);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(
            () => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    //[Fact]
    //public async Task GetByReferenceIdAsync_ShouldThrow_WhenIdIsNull()
    //{
    //    // Act & Assert
    //    await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(
    //        () => _repository.GetByReferenceIdAsync(null));
    //}

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenIdIsEmpty()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(
            () => _repository.GetByReferenceIdAsync(string.Empty));
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var tasks = new List<Task>();

        // Act
        for (int i = 0; i < 10; i++)
        {
            var status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithProperties();
            tasks.Add(_repository.AddAsync(status));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allStatuses = await _repository.ListAllAsync();
        Assert.Equal(10, allStatuses.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleComplexFilteringScenario()
    {
        // Arrange
        await ClearDatabase();

        // Create statuses with different storage groups and snapshots
        var sg1Status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithCustomProperties("SG_Database", "DB_Snap_01");
        var sg2Status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithCustomProperties("SG_Application", "App_Snap_01");
        var sg3Status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithCustomProperties("SG_Database", "DB_Snap_02");

        await _repository.AddAsync(sg1Status);
        await _repository.AddAsync(sg2Status);
        await _repository.AddAsync(sg3Status);

        // Act & Assert
        // Test storage group filtering
        var databaseResults = await _repository.GetPowerMaxMonitorStatusByName("SG_Database", false);
        Assert.Equal(2, databaseResults.Count);
        Assert.All(databaseResults, result =>
            Assert.Equal("SG_Database", result.StorageGroupMonitoring.First().StorageGroupName));

        var applicationResults = await _repository.GetPowerMaxMonitorStatusByName("SG_Application", false);
        Assert.Single(applicationResults);
        Assert.Equal("SG_Application", applicationResults.First().StorageGroupMonitoring.First().StorageGroupName);

        // Test snapshot filtering
        var dbSnapResults = await _repository.GetPowerMaxMonitorStatusByName("DB_Snap", true);
        Assert.Equal(2, dbSnapResults.Count);
        Assert.All(dbSnapResults, result =>
            Assert.Contains("DB_Snap", result.StorageGroupMonitoring.First().SnapshotDetails.First().SnapshotName));

        var appSnapResults = await _repository.GetPowerMaxMonitorStatusByName("App_Snap_01", true);
        Assert.Single(appSnapResults);
        Assert.Equal("App_Snap_01", appSnapResults.First().StorageGroupMonitoring.First().SnapshotDetails.First().SnapshotName);
    }

    [Fact]
    public async Task Repository_ShouldHandleEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        var allStatuses = await _repository.ListAllAsync();
        Assert.Empty(allStatuses);

        var nameResults = await _repository.GetPowerMaxMonitorStatusByName("NonExistent", false);
        Assert.Empty(nameResults);

        var snapResults = await _repository.GetPowerMaxMonitorStatusByName("NonExistent", true);
        Assert.Empty(snapResults);
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity()
    {
        // Arrange
        await ClearDatabase();
        var status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithProperties();

        // Act
        var addedStatus = await _repository.AddAsync(status);
        var retrievedStatus = await _repository.GetByReferenceIdAsync(addedStatus.ReferenceId);

        // Assert
        Assert.NotNull(retrievedStatus);
        Assert.Equal(status.Type, retrievedStatus.Type);
        Assert.Equal(status.InfraObjectId, retrievedStatus.InfraObjectId);
        Assert.Equal(status.InfraObjectName, retrievedStatus.InfraObjectName);
        Assert.Equal(status.WorkflowId, retrievedStatus.WorkflowId);
        Assert.Equal(status.WorkflowName, retrievedStatus.WorkflowName);
        Assert.Equal(status.Properties, retrievedStatus.Properties);
        Assert.Equal(status.ConfiguredRPO, retrievedStatus.ConfiguredRPO);
        Assert.Equal(status.DataLagValue, retrievedStatus.DataLagValue);
        Assert.Equal(status.Threshold, retrievedStatus.Threshold);
    }

    [Fact]
    public async Task Repository_ShouldHandleInvalidJsonInProperties()
    {
        // Arrange
        await ClearDatabase();
        var status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithProperties();
        status.Properties = "invalid json content";
        await _repository.AddAsync(status);

        // Act & Assert
        // The method should handle invalid JSON gracefully and return empty list
        var result = await _repository.GetPowerMaxMonitorStatusByName(null, false);
        Assert.NotNull(result);
        // The result might be empty or contain items depending on how the method handles invalid JSON
    }

    //[Fact]
    //public async Task Repository_ShouldHandleNullProperties()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var status = new PowerMaxMonitorStatus
    //    {
    //        ReferenceId = Guid.NewGuid().ToString(),
    //        Type = "NullPropsType",
    //        InfraObjectId = Guid.NewGuid().ToString(),
    //        InfraObjectName = "TestInfraObject",
    //        WorkflowId = Guid.NewGuid().ToString(),
    //        WorkflowName = "TestWorkflow",
    //        Properties = null,
    //        ConfiguredRPO = "24h",
    //        DataLagValue = "0",
    //        Threshold = "1h",
    //        IsActive = true,
    //        CreatedDate = DateTime.UtcNow,
    //        LastModifiedDate = DateTime.UtcNow,
    //        CreatedBy = "TestUser",
    //        LastModifiedBy = "TestUser"
    //    };

    //    // Act
    //    var addedStatus = await _repository.AddAsync(status);

    //    // Assert
    //    Assert.NotNull(addedStatus);
    //    var result = await _repository.GetPowerMaxMonitorStatusByName(null, false);
    //    Assert.NotNull(result);
    //    Assert.Empty(result); // Should return empty list when Properties is null
    //}

    [Fact]
    public async Task Repository_ShouldHandleMultipleStorageGroupsInSingleStatus()
    {
        // Arrange
        await ClearDatabase();

        // Create a PowerMax detail with multiple storage groups
        var powerMaxDetail = new PowerMaxDetailVm
        {
            IpAddress = "*************",
            Version = "********",
            Array = "000197800123",
            ModelName = "PowerMax 8000",
            StorageGroupMonitoring = new List<StorageGroupMonitoring>
            {
                new StorageGroupMonitoring
                {
                    StorageGroupName = "SG_Multi_01",
                    Compliance = "Green",
                    SRP = "SRP_1",
                    ServiceLevel = "Diamond",
                    Capacity = "1000GB",
                    Emulation = "FBA",
                    SRDFReplicationstatus = "Synchronized",
                    SnapshotCount = "1",
                    SnapshotDetails = new List<SnapshotDetail>
                    {
                        new SnapshotDetail { SnapshotId = "1", SnapshotName = "Snap_Multi_01", CreationTime = DateTime.UtcNow.ToString() }
                    }
                },
                new StorageGroupMonitoring
                {
                    StorageGroupName = "SG_Multi_02",
                    Compliance = "Green",
                    SRP = "SRP_1",
                    ServiceLevel = "Diamond",
                    Capacity = "2000GB",
                    Emulation = "FBA",
                    SRDFReplicationstatus = "Synchronized",
                    SnapshotCount = "1",
                    SnapshotDetails = new List<SnapshotDetail>
                    {
                        new SnapshotDetail { SnapshotId = "2", SnapshotName = "Snap_Multi_02", CreationTime = DateTime.UtcNow.ToString() }
                    }
                }
            }
        };

        var status = _powerMaxMonitorStatusFixture.CreatePowerMaxMonitorStatusWithProperties(
            properties: JsonConvert.SerializeObject(powerMaxDetail));
        await _repository.AddAsync(status);

        // Act & Assert
        // Test filtering by first storage group
        var sg1Results = await _repository.GetPowerMaxMonitorStatusByName("SG_Multi_01", false);
        Assert.Single(sg1Results);
        Assert.Single(sg1Results.First().StorageGroupMonitoring);
        Assert.Equal("SG_Multi_01", sg1Results.First().StorageGroupMonitoring.First().StorageGroupName);

        // Test filtering by second storage group
        var sg2Results = await _repository.GetPowerMaxMonitorStatusByName("SG_Multi_02", false);
        Assert.Single(sg2Results);
        Assert.Single(sg2Results.First().StorageGroupMonitoring);
        Assert.Equal("SG_Multi_02", sg2Results.First().StorageGroupMonitoring.First().StorageGroupName);

        // Test getting all storage groups
        var allResults = await _repository.GetPowerMaxMonitorStatusByName(null, false);
        Assert.Single(allResults);
        Assert.Equal(2, allResults.First().StorageGroupMonitoring.Count);
    }

    #endregion
}
