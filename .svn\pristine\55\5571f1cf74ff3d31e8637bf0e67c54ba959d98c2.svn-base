﻿using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.RoboCopyModel;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopy.Queries
{
    public class GetRoboCopyListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IRoboCopyRepository> _mockRoboCopyRepository;
        private readonly GetRoboCopyListQueryHandler _handler;

        public GetRoboCopyListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockRoboCopyRepository = new Mock<IRoboCopyRepository>();
            _handler = new GetRoboCopyListQueryHandler(_mockMapper.Object, _mockRoboCopyRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedList_WhenRoboCopysExist()
        {
            var roboCopys = new List<Domain.Entities.RoboCopy>
            {
                new Domain.Entities.RoboCopy { Id = 1, Name = "RoboCopy1", IsActive = true },
                new Domain.Entities.RoboCopy { Id = 2, Name = "RoboCopy2", IsActive = true }
            };
            var expectedVmList = new List<RoboCopyListVm>
            {
                new RoboCopyListVm { Id = Guid.NewGuid().ToString(), Name = "RoboCopy1" },
                new RoboCopyListVm { Id = Guid.NewGuid().ToString(), Name = "RoboCopy2" }
            };

            _mockRoboCopyRepository
                .Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(roboCopys);

            _mockMapper
                .Setup(mapper => mapper.Map<List<RoboCopyListVm>>(roboCopys))
                .Returns(expectedVmList);

            var query = new GetRoboCopyListQuery();

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedVmList.Count, result.Count);
            Assert.Equal(expectedVmList[0].Name, result[0].Name);

            _mockRoboCopyRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<RoboCopyListVm>>(roboCopys), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoRoboCopysExist()
        {
            _mockRoboCopyRepository
                .Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(new List<Domain.Entities.RoboCopy>());

            var query = new GetRoboCopyListQuery();

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);

            _mockRoboCopyRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<RoboCopyListVm>>(It.IsAny<List<Domain.Entities.RoboCopy>>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowArgumentNullException_WhenRepositoryReturnsNull()
        {
            _mockRoboCopyRepository
                .Setup(repo => repo.ListAllAsync())
                .ReturnsAsync((List<Domain.Entities.RoboCopy>)null);

            var query = new GetRoboCopyListQuery();

            await Assert.ThrowsAsync<ArgumentNullException>(() =>
                _handler.Handle(query, CancellationToken.None));

            _mockRoboCopyRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<RoboCopyListVm>>(It.IsAny<List<Domain.Entities.RoboCopy>>()), Times.Never);
        }
    }
}
