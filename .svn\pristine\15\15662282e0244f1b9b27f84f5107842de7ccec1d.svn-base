﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Replication.Events.LicenseInfoEvents.Update;

public class ReplicationLicenseInfoUpdatedEventHandler : INotificationHandler<ReplicationLicenseInfoUpdatedEvent>
{
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ILogger<ReplicationLicenseInfoUpdatedEventHandler> _logger;

    public ReplicationLicenseInfoUpdatedEventHandler(ILicenseInfoRepository licenseInfoRepository,
        ILogger<ReplicationLicenseInfoUpdatedEventHandler> logger, ILoggedInUserService loggedInUserService)
    {
        _licenseInfoRepository = licenseInfoRepository;
        _logger = logger;
        _loggedInUserService = loggedInUserService;
    }

    public async Task Handle(ReplicationLicenseInfoUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _licenseInfoRepository.GetByEntityId(updatedEvent.EntityId);

        if (eventToUpdate is null)
        {
            await _licenseInfoRepository.AddAsync(new Domain.Entities.LicenseInfo
            {
                LicenseId = updatedEvent.LicenseId,
                PONumber = updatedEvent.PONumber,
                CompanyId = _loggedInUserService.CompanyId,
                EntityName = updatedEvent.EntityName,
                EntityId = updatedEvent.EntityId,
                Entity = Modules.Replication.ToString(),
                Type = updatedEvent.Type,
                BusinessServiceId = updatedEvent.BusinessServiceId,
                BusinessServiceName = updatedEvent.BusinessServiceName,
                Category = updatedEvent.Category,
                Logo = updatedEvent.Logo
            });
        }
        else
        {
            eventToUpdate.LicenseId = updatedEvent.LicenseId;
            eventToUpdate.PONumber = updatedEvent.PONumber;
            eventToUpdate.CompanyId = _loggedInUserService.CompanyId;
            eventToUpdate.EntityName = updatedEvent.EntityName;
            eventToUpdate.EntityId = updatedEvent.EntityId;
            eventToUpdate.Entity = Modules.Replication.ToString();
            eventToUpdate.Type = updatedEvent.Type;
            eventToUpdate.EntityType = string.Empty;
            eventToUpdate.EntityField = updatedEvent.EntityField;
            eventToUpdate.BusinessServiceId = updatedEvent.BusinessServiceId;
            eventToUpdate.BusinessServiceName = updatedEvent.BusinessServiceName;
            eventToUpdate.Category = updatedEvent.Category;
            eventToUpdate.Logo = updatedEvent.Logo;
            await _licenseInfoRepository.UpdateAsync(eventToUpdate);
        }

        _logger.LogDebug($"'{updatedEvent.EntityName}' updated successfully in license Info.");
    }
}