﻿.Animated-Background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

    .Animated-Background li {
        position: absolute;
        display: block;
        list-style: none;
        width: 20px;
        height: 20px;
        background: rgba(255, 255, 255, 0.2);
        animation: animate 15s linear infinite;
        bottom: -150px;
    }

        .Animated-Background li:nth-child(1) {
            left: 25%;
            width: 80px;
            height: 80px;
            animation-delay: 0s;
        }


        .Animated-Background li:nth-child(2) {
            left: 10%;
            width: 20px;
            height: 20px;
            animation-delay: 2s;
            animation-duration: 12s;
        }

        .Animated-Background li:nth-child(3) {
            left: 70%;
            width: 20px;
            height: 20px;
            animation-delay: 4s;
        }

        .Animated-Background li:nth-child(4) {
            left: 40%;
            width: 60px;
            height: 60px;
            animation-delay: 0s;
            animation-duration: 18s;
        }

        .Animated-Background li:nth-child(5) {
            left: 65%;
            width: 20px;
            height: 20px;
            animation-delay: 0s;
        }

.circles li:nth-child(6) {
    left: 75%;
    width: 110px;
    height: 110px;
    animation-delay: 3s;
}

.Animated-Background li:nth-child(7) {
    left: 35%;
    width: 150px;
    height: 150px;
    animation-delay: 7s;
}

.Animated-Background li:nth-child(8) {
    left: 50%;
    width: 25px;
    height: 25px;
    animation-delay: 15s;
    animation-duration: 45s;
}

.Animated-Background li:nth-child(9) {
    left: 20%;
    width: 15px;
    height: 15px;
    animation-delay: 2s;
    animation-duration: 35s;
}

.Animated-Background li:nth-child(10) {
    left: 85%;
    width: 150px;
    height: 150px;
    animation-delay: 0s;
    animation-duration: 11s;
}



@keyframes animate {

    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
        border-radius: 0;
    }

    100% {
        transform: translateY(-1000px) rotate(720deg);
        opacity: 0;
        border-radius: 50%;
    }
}



@-webkit-keyframes dot-keyframes {
    0% {
        opacity: 0.4;
        transform: scale(1, 1);
    }

    50% {
        opacity: 1;
        transform: scale(1.2, 1.2);
    }

    100% {
        opacity: 0.4;
        transform: scale(1, 1);
    }
}

@keyframes dot-keyframes {
    0% {
        opacity: 0.4;
        transform: scale(1, 1);
    }

    50% {
        opacity: 1;
        transform: scale(1.2, 1.2);
    }

    100% {
        opacity: 0.4;
        transform: scale(1, 1);
    }
}

.loading-dots {
    text-align: right;
    width: 100%;
}

.loading-dots--dot {
    -webkit-animation: dot-keyframes 1.5s infinite ease-in-out;
    animation: dot-keyframes 1.5s infinite ease-in-out;
    border-radius: 10px;
    display: inline-block;
    height: 6px;
    width: 6px;
    margin: 0 2px;
}

    .loading-dots--dot:nth-child(2) {
        -webkit-animation-delay: 0.5s;
        animation-delay: 0.5s;
    }

    .loading-dots--dot:nth-child(3) {
        -webkit-animation-delay: 1s;
        animation-delay: 1s;
    }