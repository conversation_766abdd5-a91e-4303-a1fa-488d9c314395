using ContinuityPatrol.Application.Features.BiaRules.Events.Update;

namespace ContinuityPatrol.Application.Features.BiaRules.Commands.Update;

public class UpdateBiaRulesCommandHandler : IRequestHandler<UpdateBiaRulesCommand, UpdateBiaRulesResponse>
{
    private readonly IBiaRulesRepository _biaImpactRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateBiaRulesCommandHandler(IMapper mapper, IBiaRulesRepository biaImpactRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _biaImpactRepository = biaImpactRepository;
        _publisher = publisher;
    }

    public async Task<UpdateBiaRulesResponse> Handle(UpdateBiaRulesCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _biaImpactRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.BiaRules), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateBiaRulesCommand), typeof(Domain.Entities.BiaRules));

        await _biaImpactRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateBiaRulesResponse
        {
            //Message = Message.Update(nameof(Domain.Entities.BiaRules), eventToUpdate.Type),
            Message = "BIA Rules updated successfully",
            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new BiaRulesUpdatedEvent { Name = eventToUpdate.Type }, cancellationToken);

        return response;
    }
}