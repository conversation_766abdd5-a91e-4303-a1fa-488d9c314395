using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DRReadyLog.Commands.Create;
using ContinuityPatrol.Application.Features.DRReadyLog.Commands.Delete;
using ContinuityPatrol.Application.Features.DRReadyLog.Commands.Update;
using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetByLast7Days;
using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.DRReadyLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DrReadyLogControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DrReadyLogController _controller;
    private readonly DrReadyLogFixture _drReadyLogFixture;

    public DrReadyLogControllerTests()
    {
        _drReadyLogFixture = new DrReadyLogFixture();

        var testBuilder = new ControllerTestBuilder<DrReadyLogController>();
        _controller = testBuilder.CreateController(
            _ => new DrReadyLogController(),
            out _mediatorMock);
    }

    #region Core CRUD Operations

    [Fact]
    public async Task CreateDrReadyLog_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _drReadyLogFixture.CreateDRReadyLogCommand;
        var expectedResponse = _drReadyLogFixture.CreateDRReadyLogResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrReadyLog(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDRReadyLogResponse>(createdResult.Value);
        Assert.Equal("Enterprise DR Ready Log created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDrReadyLog_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _drReadyLogFixture.UpdateDRReadyLogCommand;
        var expectedResponse = _drReadyLogFixture.UpdateDRReadyLogResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDrReadyLog(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDRReadyLogResponse>(okResult.Value);
        Assert.Equal("Enterprise DR Ready Log updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task DeleteDrReadyLog_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var logId = Guid.NewGuid().ToString();
        var expectedResponse = _drReadyLogFixture.DeleteDRReadyLogResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDRReadyLogCommand>(c => c.Id == logId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDrReadyLog(logId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDRReadyLogResponse>(okResult.Value);
        Assert.Equal("Enterprise DR Ready Log deleted successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
    }

    [Fact]
    public async Task GetDrReadyLogById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var logId = Guid.NewGuid().ToString();
        var expectedDetail = _drReadyLogFixture.DRReadyLogDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDRReadyLogDetailQuery>(q => q.Id == logId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDrReadyLogById(logId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DRReadyLogDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Detail Business Service", returnedDetail.BusinessServiceName);
        Assert.Equal("Enterprise Detail Function", returnedDetail.BusinessFunctionName);
        Assert.Equal("Completed", returnedDetail.WorkflowStatus);
        Assert.Equal("Yes", returnedDetail.DRReady);
    }

    [Fact]
    public async Task GetDrReadyLog_ReturnsOkResult()
    {
        // Arrange
        var logList = _drReadyLogFixture.DRReadyLogListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDRReadyLogListQuery>(), default))
            .ReturnsAsync(logList);

        // Act
        var result = await _controller.GetDrReadyLog();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DRReadyLogListVm>>(okResult.Value);
        Assert.Equal(5, returnedList.Count);
        Assert.All(returnedList, log => Assert.Contains("Enterprise", log.BusinessServiceName));
    }

    [Fact]
    public async Task GetPaginatedDrReadyLog_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _drReadyLogFixture.GetDRReadyLogPaginatedListQuery;
        var paginatedResult = _drReadyLogFixture.DRReadyLogPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedDrReadyLog(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DRReadyLogListVm>>(okResult.Value);
        Assert.Equal(8, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, log => Assert.Contains("Enterprise", log.BusinessServiceName));
    }

    [Fact]
    public async Task GetDrReadyLogByBusinessServiceId_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var expectedDetail = _drReadyLogFixture.DRReadyLogByBusinessServiceIdVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDRReadyLogByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDrReadyLogByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DRReadyLogByBusinessServiceIdVm>(okResult.Value);
        Assert.Equal("Enterprise Business Service by ID", returnedDetail.BusinessServiceName);
        Assert.Equal("Completed", returnedDetail.WorkflowStatus);
        Assert.Equal("Yes", returnedDetail.DRReady);
    }

    [Fact]
    public async Task GetDataLagByLast7DaysList_ReturnsOkResult()
    {
        // Arrange
        var last7DaysList = _drReadyLogFixture.DRReadyLogByLast7DaysVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDRReadyLogByLast7DaysQuery>(), default))
            .ReturnsAsync(last7DaysList);

        // Act
        var result = await _controller.GetDataLagByLast7DaysList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DRReadyLogByLast7DaysVm>>(okResult.Value);
        Assert.Equal(7, returnedList.Count);
        Assert.All(returnedList, log => Assert.Contains("Enterprise", log.InfraObjectName));
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public async Task CreateDrReadyLog_CallsClearDataCache()
    {
        // Arrange
        var command = _drReadyLogFixture.CreateDRReadyLogCommand;
        var expectedResponse = _drReadyLogFixture.CreateDRReadyLogResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.CreateDrReadyLog(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task UpdateDrReadyLog_CallsClearDataCache()
    {
        // Arrange
        var command = _drReadyLogFixture.UpdateDRReadyLogCommand;
        var expectedResponse = _drReadyLogFixture.UpdateDRReadyLogResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.UpdateDrReadyLog(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task DeleteDrReadyLog_CallsClearDataCache()
    {
        // Arrange
        var logId = Guid.NewGuid().ToString();
        var expectedResponse = _drReadyLogFixture.DeleteDRReadyLogResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDRReadyLogCommand>(c => c.Id == logId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.DeleteDrReadyLog(logId);

        // Assert
        _mediatorMock.Verify(m => m.Send(It.Is<DeleteDRReadyLogCommand>(c => c.Id == logId), default), Times.Once);
    }

    #endregion

  

    [Fact]
    public async Task CreateDrReadyLog_HandlesFailedWorkflowScenario()
    {
        // Arrange
        var failedCommand = _drReadyLogFixture.CreateDRReadyLogCommand;
        failedCommand.BusinessServiceName = "Enterprise Failed Workflow Service";
        failedCommand.WorkflowStatus = "Failed";
        failedCommand.FailedActionName = "Database Failover";
        failedCommand.FailedActionId = Guid.NewGuid().ToString();
        failedCommand.DRReady = "No";
        failedCommand.NotReady = "Yes";
        failedCommand.ErrorMessage = "Database connection timeout during failover";
        failedCommand.Type = "DR_FAILURE";

        var expectedResponse = _drReadyLogFixture.CreateDRReadyLogResponse;

        _mediatorMock
            .Setup(m => m.Send(failedCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrReadyLog(failedCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDRReadyLogResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Failed Workflow Service", failedCommand.BusinessServiceName);
        Assert.Equal("Failed", failedCommand.WorkflowStatus);
        Assert.Equal("Database Failover", failedCommand.FailedActionName);
        Assert.Equal("No", failedCommand.DRReady);
        Assert.Contains("timeout", failedCommand.ErrorMessage);
    }

    [Fact]
    public async Task UpdateDrReadyLog_HandlesWorkflowRecovery()
    {
        // Arrange
        var recoveryCommand = _drReadyLogFixture.UpdateDRReadyLogCommand;
        recoveryCommand.BusinessServiceName = "Enterprise Recovery Service";
        recoveryCommand.WorkflowStatus = "Recovered";
        recoveryCommand.FailedActionName = "";
        recoveryCommand.FailedActionId = "";
        recoveryCommand.DRReady = "Yes";
        recoveryCommand.NotReady = "No";
        recoveryCommand.ErrorMessage = "";
        recoveryCommand.Type = "DR_RECOVERY";

        var expectedResponse = _drReadyLogFixture.UpdateDRReadyLogResponse;

        _mediatorMock
            .Setup(m => m.Send(recoveryCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDrReadyLog(recoveryCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDRReadyLogResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Recovery Service", recoveryCommand.BusinessServiceName);
        Assert.Equal("Recovered", recoveryCommand.WorkflowStatus);
        Assert.Equal("Yes", recoveryCommand.DRReady);
        Assert.Equal("", recoveryCommand.ErrorMessage);
    }

    [Fact]
    public async Task GetPaginatedDrReadyLog_HandlesLargeDataset()
    {
        // Arrange
        var query = _drReadyLogFixture.GetDRReadyLogPaginatedListQuery;
        query.PageSize = 50;
        query.SearchString = "Enterprise Production";

        var largePaginatedResult = new PaginatedResult<DRReadyLogListVm>
        {
            Data = Enumerable.Range(1, 50).Select(i => new DRReadyLogListVm
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceName = $"Enterprise Production Service {i:D2}",
                BusinessFunctionName = $"Enterprise Production Function {i:D2}",
                WorkflowName = $"Enterprise Production Workflow {i:D2}",
                WorkflowStatus = i % 5 == 0 ? "Failed" : "Completed",
                DRReady = i % 5 == 0 ? "No" : "Yes",
                IsProtected = "Yes",
                ActiveInfra = (100 + i).ToString(),
                AffectedInfra = (i % 10).ToString()
            }).ToList(),
            TotalCount = 50,
            PageSize = 50,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(largePaginatedResult);

        // Act
        var result = await _controller.GetPaginatedDrReadyLog(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DRReadyLogListVm>>(okResult.Value);

        Assert.Equal(50, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, log => Assert.Contains("Enterprise Production", log.BusinessServiceName));
        Assert.Contains(returnedResult.Data, log => log.WorkflowStatus == "Failed");
        Assert.Contains(returnedResult.Data, log => log.WorkflowStatus == "Completed");
        Assert.Contains(returnedResult.Data, log => log.DRReady == "No");
        Assert.Contains(returnedResult.Data, log => log.DRReady == "Yes");
    }

    [Fact]
    public async Task GetDataLagByLast7DaysList_HandlesTimeRangeAnalysis()
    {
        // Arrange
        var timeRangeList = Enumerable.Range(1, 7).Select(i => new DRReadyLogByLast7DaysVm
        {
            Id = Guid.NewGuid().ToString(),
            InfraObjectName = $"Enterprise Day {i} Service",
            WorkflowName = $"Enterprise Day {i} Workflow",
            IsDRReady = i % 3 == 0 ? true : false,
            Reason = i % 4 == 0 ? "No" : "Yes",
            StartTime = DateTime.UtcNow.AddDays(-i),
            EndTime = DateTime.UtcNow.AddDays(-i).AddHours(2)
        }).ToList();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDRReadyLogByLast7DaysQuery>(), default))
            .ReturnsAsync(timeRangeList);

        // Act
        var result = await _controller.GetDataLagByLast7DaysList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DRReadyLogByLast7DaysVm>>(okResult.Value);

        Assert.Equal(7, returnedList.Count);
        Assert.All(returnedList, log => Assert.Contains("Enterprise Day", log.InfraObjectName));
        Assert.Contains(returnedList, log => log.IsDRReady == true);
        Assert.Contains(returnedList, log => log.IsDRReady == false);
        Assert.Contains(returnedList, log => log.Reason == "No");
        Assert.Contains(returnedList, log => log.Reason == "Yes");
        Assert.All(returnedList, log => Assert.True(log.StartTime <= log.EndTime));
    }

    

  

    [Fact]
    public async Task CreateDrReadyLog_WithCloudMigrationScenario_ReturnsCreatedResult()
    {
        // Arrange
        var cloudMigrationCommand = _drReadyLogFixture.CreateDRReadyLogCommand;
        cloudMigrationCommand.BusinessServiceName = "Enterprise Cloud Migration Service";
        cloudMigrationCommand.BusinessFunctionName = "Enterprise Cloud Migration Function";
        cloudMigrationCommand.WorkflowName = "Enterprise Cloud Migration DR Workflow";
        cloudMigrationCommand.WorkflowStatus = "Migration_In_Progress";
        cloudMigrationCommand.Type = "CLOUD_MIGRATION_DR";
        cloudMigrationCommand.InfraObjectName = "Enterprise Cloud Migration Server";
        cloudMigrationCommand.ComponentName = "Cloud Migration Database";
        cloudMigrationCommand.AffectedInfra = "25";
        cloudMigrationCommand.ActiveInfra = "200";
        cloudMigrationCommand.ActiveBusinessFunction = "40";
        cloudMigrationCommand.AffectedBusinessFunction = "5";
        cloudMigrationCommand.DRReady = "Partial";
        cloudMigrationCommand.NotReady = "Migration_Pending";
        cloudMigrationCommand.ErrorMessage = "";

        var expectedResponse = _drReadyLogFixture.CreateDRReadyLogResponse;
        expectedResponse.Message = "Enterprise Cloud Migration DR Ready Log created successfully!";

        _mediatorMock
            .Setup(m => m.Send(cloudMigrationCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrReadyLog(cloudMigrationCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDRReadyLogResponse>(createdResult.Value);
        Assert.Equal("Enterprise Cloud Migration DR Ready Log created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDrReadyLog_WithMultiRegionFailover_ReturnsOkResult()
    {
        // Arrange
        var multiRegionCommand = _drReadyLogFixture.UpdateDRReadyLogCommand;
        multiRegionCommand.BusinessServiceName = "Enterprise Multi-Region Service";
        multiRegionCommand.BusinessFunctionName = "Enterprise Multi-Region Function";
        multiRegionCommand.WorkflowName = "Enterprise Multi-Region Failover Workflow";
        multiRegionCommand.WorkflowStatus = "Cross_Region_Failover_Complete";
        multiRegionCommand.Type = "MULTI_REGION_FAILOVER";
        multiRegionCommand.InfraObjectName = "Enterprise Multi-Region Primary";
        multiRegionCommand.ComponentName = "Multi-Region Load Balancer";
        multiRegionCommand.AffectedInfra = "0";
        multiRegionCommand.ActiveInfra = "500";
        multiRegionCommand.ActiveBusinessFunction = "100";
        multiRegionCommand.AffectedBusinessFunction = "0";
        multiRegionCommand.DRReady = "Yes";
        multiRegionCommand.NotReady = "No";
        multiRegionCommand.ErrorMessage = "";

        var expectedResponse = _drReadyLogFixture.UpdateDRReadyLogResponse;
        expectedResponse.Message = "Enterprise Multi-Region DR Ready Log updated successfully!";

        _mediatorMock
            .Setup(m => m.Send(multiRegionCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDrReadyLog(multiRegionCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDRReadyLogResponse>(okResult.Value);
        Assert.Equal("Enterprise Multi-Region DR Ready Log updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task GetDrReadyLogById_WithContainerOrchestration_ReturnsOkResult()
    {
        // Arrange
        var logId = Guid.NewGuid().ToString();
        var containerLog = _drReadyLogFixture.DRReadyLogDetailVm;
        containerLog.Id = logId;
        containerLog.BusinessServiceName = "Enterprise Container Orchestration Service";
        containerLog.BusinessFunctionName = "Enterprise Container Function";
        containerLog.WorkflowName = "Enterprise Container DR Workflow";
        containerLog.WorkflowStatus = "Container_Orchestration_Active";
        containerLog.Type = "CONTAINER_ORCHESTRATION_DR";
        containerLog.InfraObjectName = "Enterprise Kubernetes Cluster";
        containerLog.ComponentName = "Container Registry";
        containerLog.AffectedInfra = "10";
        containerLog.ActiveInfra = "300";
        containerLog.ActiveBusinessFunction = "60";
        containerLog.AffectedBusinessFunction = "3";
        containerLog.DRReady = "Yes";
        containerLog.NotReady = "No";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDRReadyLogDetailQuery>(q => q.Id == logId), default))
            .ReturnsAsync(containerLog);

        // Act
        var result = await _controller.GetDrReadyLogById(logId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedLog = Assert.IsType<DRReadyLogDetailVm>(okResult.Value);
        Assert.Equal(logId, returnedLog.Id);
        Assert.Equal("Enterprise Container Orchestration Service", returnedLog.BusinessServiceName);
        Assert.Equal("CONTAINER_ORCHESTRATION_DR", returnedLog.Type);
        Assert.Equal("Enterprise Kubernetes Cluster", returnedLog.InfraObjectName);
        Assert.Equal("Yes", returnedLog.DRReady);
        Assert.Equal("Container_Orchestration_Active", returnedLog.WorkflowStatus);
    }

   
}
