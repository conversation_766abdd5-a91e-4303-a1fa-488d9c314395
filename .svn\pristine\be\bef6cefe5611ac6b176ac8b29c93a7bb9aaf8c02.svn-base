﻿using ContinuityPatrol.Application.Features.UserLogin.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.UserLoginModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.UserLogin.Queries
{
    public class GetUserLoginPaginatedListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IMediator> _mockMediator;
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly GetUserLoginPaginatedListQueryHandler _handler;

        public GetUserLoginPaginatedListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockMediator = new Mock<IMediator>();
            _mockUserRepository = new Mock<IUserRepository>();
            _handler = new GetUserLoginPaginatedListQueryHandler(_mockMapper.Object, _mockMediator.Object, _mockUserRepository.Object);
        }

        [Fact]
        public async Task Handle_ReturnsPaginatedUserLoginListVm_WhenDataExists()
        {
            var query = new GetUserLoginPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "User" };
            var cancellationToken = CancellationToken.None;

            var userEntities = new List<Domain.Entities.User>
            {
                new Domain.Entities.User { Id = 1, LoginName = "User1" },
                new Domain.Entities.User { Id = 2, LoginName = "User2" }
            }.AsQueryable();

            var paginatedUsers = new PaginatedResult<UserLoginListVm>
            {
                Data = new List<UserLoginListVm>
            {
                new UserLoginListVm { UserId = "1", LastLoginIp = "************" },
                new UserLoginListVm { UserId = "2", LastLoginIp = "***********" }
            },
                TotalCount = 2,
                PageSize = 10
            };

            _mockUserRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(userEntities);

            _mockMapper
                .Setup(mapper => mapper.Map<UserLoginListVm>(It.IsAny<Domain.Entities.User>()))
                .Returns((Domain.Entities.User user) => new UserLoginListVm { UserId = "1", LastLoginIp = "************" });

            var result = await _handler.Handle(query, cancellationToken);

            Assert.NotNull(result);
            Assert.Equal(paginatedUsers.TotalCount, result.TotalCount);
            Assert.Equal(paginatedUsers.Data.Count, result.Data.Count);

            _mockUserRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<UserLoginListVm>(It.IsAny<Domain.Entities.User>()), Times.AtLeastOnce);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenUsersListIsNull()
        {
            var query = new GetUserLoginPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "User" };
            var cancellationToken = CancellationToken.None;

            IQueryable<Domain.Entities.User> nullQueryable = null;

            _mockUserRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(nullQueryable);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, cancellationToken));

            _mockUserRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<UserLoginListVm>(It.IsAny<Domain.Entities.User>()), Times.Never);
        }
    }
}
