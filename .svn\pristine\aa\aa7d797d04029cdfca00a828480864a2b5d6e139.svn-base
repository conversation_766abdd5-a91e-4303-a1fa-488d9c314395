using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;
using static StackExchange.Redis.Role;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ReportRepositoryTests : IClassFixture<ReportFixture>, IDisposable
{
    private readonly ReportFixture _reportFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ReportRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public ReportRepositoryTests(ReportFixture reportFixture)
    {
        _reportFixture = reportFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _repository = new ReportRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.Reports.RemoveRange(_dbContext.Reports);
        await _dbContext.SaveChangesAsync();
    }

    #region IsReportNameExist Tests

    [Fact]
    public async Task IsReportNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var report = new Report
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingReport",
            Description = "Test Description",
            IsActive = true
        };
        await _repository.AddAsync(report);

        // Act
        var result = await _repository.IsReportNameExist("ExistingReport", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsReportNameExist_ShouldReturnFalse_WhenNameExistsAndIdMatchesExistingEntity()
    {
        // Arrange
        await ClearDatabase();
        var report = new Report
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingReport",
            Description = "Test Description",
            IsActive = true
        };
        await _repository.AddAsync(report);

        // Act
        var result = await _repository.IsReportNameExist("ExistingReport", report.ReferenceId);

        // Assert
        Assert.False(result); // Should return false because it's the same entity
    }

    [Fact]
    public async Task IsReportNameExist_ShouldReturnTrue_WhenNameExistsAndIdDoesNotMatchExistingEntity()
    {
        // Arrange
        await ClearDatabase();
        var report = new Report
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingReport",
            Description = "Test Description",
            IsActive = true
        };
        await _repository.AddAsync(report);

        // Act
        var differentId = Guid.NewGuid().ToString();
        var result = await _repository.IsReportNameExist("ExistingReport", differentId);

        // Assert
        Assert.True(result); // Should return true because it's a different entity with same name
    }

    [Fact]
    public async Task IsReportNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var report = new Report
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingReport",
            Description = "Test Description",
            IsActive = true
        };
        await _repository.AddAsync(report);

        // Act
        var result = await _repository.IsReportNameExist("NonExistentReport", "any-id");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsReportNameExist_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var report = new Report
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "CaseSensitiveReport",
            Description = "Test Description",
            IsActive = true
        };
        await _repository.AddAsync(report);

        // Act
        var result1 = await _repository.IsReportNameExist("CaseSensitiveReport", "invalid-guid");
        var result2 = await _repository.IsReportNameExist("casesensitivereport", "invalid-guid");
        var result3 = await _repository.IsReportNameExist("CASESENSITIVEREPORT", "invalid-guid");

        // Assert
        Assert.True(result1);   // Exact match should return true
        Assert.False(result2);  // Different case should return false
        Assert.False(result3);  // Different case should return false
    }

    [Fact]
    public async Task IsReportNameExist_ShouldHandleNullName()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReportNameExist(null, "any-id");

        // Assert
        Assert.False(result); // Should return false when searching for null name
    }

    [Fact]
    public async Task IsReportNameExist_ShouldHandleEmptyName()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReportNameExist("", "any-id");

        // Assert
        Assert.False(result); // Should return false when searching for empty name
    }

    [Fact]
    public async Task IsReportNameExist_ShouldHandleMultipleEntitiesWithSameName()
    {
        // Arrange
        await ClearDatabase();
        var reports = new List<Report>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "DuplicateName", 
                Description = "Description1",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "DuplicateName", 
                Description = "Description2",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "DuplicateName", 
                Description = "Description3",
                IsActive = true 
            }
        };

        foreach (var report in reports)
        {
            await _repository.AddAsync(report);
        }

        // Act - Using a different valid GUID
        var differentId = Guid.NewGuid().ToString();
        var result = await _repository.IsReportNameExist("DuplicateName", differentId);

        // Assert
        Assert.True(result); // Should return true because multiple entities exist (count > 1)
    }

    #endregion

    #region IsReportNameUnique Tests

    [Fact]
    public async Task IsReportNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();
        var report = new Report
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingReport",
            Description = "Test Description",
            IsActive = true
        };
        await _repository.AddAsync(report);

        // Act
        var result = await _repository.IsReportNameUnique("ExistingReport");

        // Assert
        Assert.True(result); // Method returns true when name exists (opposite of unique)
    }

    [Fact]
    public async Task IsReportNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var report = new Report
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingReport",
            Description = "Test Description",
            IsActive = true
        };
        await _repository.AddAsync(report);

        // Act
        var result = await _repository.IsReportNameUnique("NonExistentReport");

        // Assert
        Assert.False(result); // Method returns false when name doesn't exist (is unique)
    }

    [Fact]
    public async Task IsReportNameUnique_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var report = new Report
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "CaseSensitiveReport",
            Description = "Test Description",
            IsActive = true
        };
        await _repository.AddAsync(report);

        // Act
        var result1 = await _repository.IsReportNameUnique("CaseSensitiveReport");
        var result2 = await _repository.IsReportNameUnique("casesensitivereport");
        var result3 = await _repository.IsReportNameUnique("CASESENSITIVEREPORT");

        // Assert
        Assert.True(result1);   // Exact match should return true
        Assert.False(result2);  // Different case should return false
        Assert.False(result3);  // Different case should return false
    }

    [Fact]
    public async Task IsReportNameUnique_ShouldHandleNullName()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReportNameUnique(null);

        // Assert
        Assert.False(result); // Should return false when searching for null name
    }

    [Fact]
    public async Task IsReportNameUnique_ShouldHandleEmptyName()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReportNameUnique("");

        // Assert
        Assert.False(result); // Should return false when searching for empty name
    }

    #endregion

    #region GetReportNames Tests

    [Fact]
    public async Task GetReportNames_ShouldReturnActiveReports_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var reports = new List<Report>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "ZReport", 
                Description = "Description1",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "AReport", 
                Description = "Description2",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "InactiveReport", 
                Description = "Description3",
                IsActive = false 
            }
        };

        foreach (var report in reports)
        {
            await _dbContext.Reports.AddAsync(report);
            _dbContext.SaveChanges();
        }

        // Act
        var result = await _repository.GetReportNames();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.True(r.IsActive));
        // Should be ordered by name
        Assert.Equal("AReport", result[0].Name);
        Assert.Equal("ZReport", result[1].Name);
    }

    [Fact]
    public async Task GetReportNames_ShouldReturnActiveReports_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var reports = new List<Report>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "ActiveReport1", 
                Description = "Description1",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "ActiveReport2", 
                Description = "Description2",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "InactiveReport", 
                Description = "Description3",
                IsActive = false 
            }
        };

        foreach (var report in reports)
        {
            await _dbContext.Reports.AddAsync(report);
            _dbContext.SaveChanges();
        }

        // Act
        var result = await _repository.GetReportNames();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.True(r.IsActive));
        Assert.Contains(result, r => r.Name == "ActiveReport1");
        Assert.Contains(result, r => r.Name == "ActiveReport2");
        Assert.DoesNotContain(result, r => r.Name == "InactiveReport");
    }

    [Fact]
    public async Task GetReportNames_ShouldReturnOnlyReferenceIdAndName()
    {
        // Arrange
        await ClearDatabase();

        var report = new Report
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReport",
            Description = "Test Description",
            FilterColumn = "TestFilter",
            HeaderColumn = "TestHeader",
            Design = "TestDesign",
            DataSet = "TestDataSet",
            IsActive = true
        };

        await _repository.AddAsync(report);

        // Act
        var result = await _repository.GetReportNames();

        // Assert
        Assert.Single(result);
        var returnedReport = result[0];
        Assert.Equal(report.ReferenceId, returnedReport.ReferenceId);
        Assert.Equal(report.Name, returnedReport.Name);
        // Other properties should be null/default as they're not selected
        Assert.Null(returnedReport.Description);
        Assert.Null(returnedReport.FilterColumn);
        Assert.Null(returnedReport.HeaderColumn);
        Assert.Null(returnedReport.Design);
        Assert.Null(returnedReport.DataSet);
    }

    [Fact]
    public async Task GetReportNames_ShouldReturnEmptyList_WhenNoActiveReports()
    {
        // Arrange
        await ClearDatabase();

        var inactiveReports = new List<Report>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "InactiveReport1", 
                Description = "Description1",
                IsActive = false 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "InactiveReport2", 
                Description = "Description2",
                IsActive = false 
            }
        };

        foreach (var report in inactiveReports)
        {
            await _dbContext.Reports.AddAsync(report);
            _dbContext.SaveChanges();
        }

        // Act
        var result = await _repository.GetReportNames();

        // Assert
        Assert.Empty(result);
    }

    #endregion
}
