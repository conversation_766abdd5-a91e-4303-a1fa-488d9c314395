﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IWorkflowActionTypeRepository : IRepository<WorkflowActionType>
{
    Task<WorkflowActionType> GetWorkflowActionTypeById(string id);
    Task<bool> IsWorkflowActionTypeExist(string actionType, string id);
    Task<bool> IsWorkflowActionTypeUnique(string actionType);
}