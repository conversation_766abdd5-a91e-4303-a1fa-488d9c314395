﻿namespace ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByDrReady;

public class GetInfraObjectByDrReadyListQueryHandler : IRequestHandler<GetInfraObjectByDrReadyListQuery, DrReadyCount>
{
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IMapper _mapper;

    public GetInfraObjectByDrReadyListQueryHandler(IMapper mapper, IInfraObjectRepository infraObjectRepository)
    {
        _mapper = mapper;
        _infraObjectRepository = infraObjectRepository;
    }

    public async Task<DrReadyCount> Handle(GetInfraObjectByDrReadyListQuery request,
        CancellationToken cancellationToken)
    {
        var infraObjects = await _infraObjectRepository.ListAllAsync();

        var drReady = _mapper.Map<DrReadyCount>(infraObjects);

        return drReady;
    }
}