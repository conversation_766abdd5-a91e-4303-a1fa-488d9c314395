using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.DriftManagementMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetPaginatedList;

public class GetDriftManagementMonitorStatusPaginatedListQueryHandler : IRequestHandler<
    GetDriftManagementMonitorStatusPaginatedListQuery, PaginatedResult<DriftManagementMonitorStatusListVm>>
{
    private readonly IDriftManagementMonitorStatusRepository _driftManagementMonitorStatusRepository;
    private readonly IMapper _mapper;

    public GetDriftManagementMonitorStatusPaginatedListQueryHandler(IMapper mapper,
        IDriftManagementMonitorStatusRepository driftManagementMonitorStatusRepository)
    {
        _mapper = mapper;
        _driftManagementMonitorStatusRepository = driftManagementMonitorStatusRepository;
    }

    public async Task<PaginatedResult<DriftManagementMonitorStatusListVm>> Handle(
        GetDriftManagementMonitorStatusPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DriftManagementMonitorStatusFilterSpecification(request.SearchString);

        var queryable =await _driftManagementMonitorStatusRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var driftManagementMonitorStatusList = _mapper.Map<PaginatedResult<DriftManagementMonitorStatusListVm>>(queryable);
           
        return driftManagementMonitorStatusList;

        //var queryable = _driftManagementMonitorStatusRepository.GetPaginatedQuery();

        //var productFilterSpec = new DriftManagementMonitorStatusFilterSpecification(request.SearchString);

        //var driftManagementMonitorStatusList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<DriftManagementMonitorStatusListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return driftManagementMonitorStatusList;
    }
}