﻿using ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetRunningActionCountByOperationId;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionResult.Queries
{
    public class GetRunningActionCountByOperationIdQueryHandlerTests
    {
        private readonly Mock<IWorkflowOperationRepository> _mockWorkflowOperationRepository;
        private readonly Mock<IWorkflowActionResultRepository> _mockWorkflowActionResultRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetRunningActionCountByOperationIdQueryHandler _handler;

        public GetRunningActionCountByOperationIdQueryHandlerTests()
        {
            _mockWorkflowOperationRepository = new Mock<IWorkflowOperationRepository>();
            _mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();
            _mockMapper = new Mock<IMapper>();

            _handler = new GetRunningActionCountByOperationIdQueryHandler(
                _mockWorkflowOperationRepository.Object,
                _mockWorkflowActionResultRepository.Object,
                _mockMapper.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldReturnActionRunningCountList_WhenOperationIdIsProvided()
        {
            var operationId = "test-operation-id";
            var query = new GetRunningActionCountByOperationIdQuery { OperationId = operationId };

            var workflowOperations = new List<Domain.Entities.WorkflowOperation>
            {
                new Domain.Entities.WorkflowOperation { ReferenceId = operationId }
            };
            _mockWorkflowOperationRepository
                .Setup(repo => repo.GetWorkflowOperationByOperationId(operationId))
                .ReturnsAsync(workflowOperations);

            var workflowActionResults = new List<Domain.Entities.WorkflowActionResult>
            {
                new Domain.Entities.WorkflowActionResult { WorkflowOperationId = operationId }
            };
            _mockWorkflowActionResultRepository
                .Setup(repo => repo.GetWorkflowActionResultByWorkflowOperationId(operationId))
                .ReturnsAsync(workflowActionResults);

            var actionRunningCountVm = new ActionRunningCountListVm
            {
                WorkflowOperationId = operationId
            };
            _mockMapper
                .Setup(mapper => mapper.Map<ActionRunningCountListVm>(workflowActionResults))
                .Returns(actionRunningCountVm);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(operationId, result[0].WorkflowOperationId);

            _mockWorkflowOperationRepository.Verify(repo => repo.GetWorkflowOperationByOperationId(operationId), Times.Once);
            _mockWorkflowActionResultRepository.Verify(repo => repo.GetWorkflowActionResultByWorkflowOperationId(operationId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<ActionRunningCountListVm>(workflowActionResults), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenOperationIdIsNullOrEmpty()
        {
            var query = new GetRunningActionCountByOperationIdQuery { OperationId = null };

            _mockWorkflowOperationRepository
                .Setup(repo => repo.GetWorkflowOperationByRunningStatus())
                .ReturnsAsync(new List<Domain.Entities.WorkflowOperation>());

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);

            _mockWorkflowOperationRepository.Verify(repo => repo.GetWorkflowOperationByRunningStatus(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldMapAndReturnCorrectActionCountList()
        {
            var operationId = "test-operation-id";
            var query = new GetRunningActionCountByOperationIdQuery { OperationId = operationId };

            var workflowOperations = new List<Domain.Entities.WorkflowOperation>
            {
                new Domain.Entities.WorkflowOperation { ReferenceId = operationId }
            };
            _mockWorkflowOperationRepository
                .Setup(repo => repo.GetWorkflowOperationByOperationId(operationId))
                .ReturnsAsync(workflowOperations);

            var workflowActionResults = new List<Domain.Entities.WorkflowActionResult>
            {
                new Domain.Entities.WorkflowActionResult { WorkflowOperationId = operationId }
            };
            _mockWorkflowActionResultRepository
                .Setup(repo => repo.GetWorkflowActionResultByWorkflowOperationId(operationId))
                .ReturnsAsync(workflowActionResults);

            var actionRunningCountVm = new ActionRunningCountListVm
            {
                WorkflowOperationId = operationId
            };
            _mockMapper
                .Setup(mapper => mapper.Map<ActionRunningCountListVm>(workflowActionResults))
                .Returns(actionRunningCountVm);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(operationId, result[0].WorkflowOperationId);

            _mockWorkflowOperationRepository.Verify(repo => repo.GetWorkflowOperationByOperationId(operationId), Times.Once);
            _mockWorkflowActionResultRepository.Verify(repo => repo.GetWorkflowActionResultByWorkflowOperationId(operationId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<ActionRunningCountListVm>(workflowActionResults), Times.Once);
        }
    }
}
