using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class BulkImportOperationRepositoryTests : IClassFixture<BulkImportOperationFixture>
{
    private readonly BulkImportOperationFixture _bulkImportOperationFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BulkImportOperationRepository _repository;

    public BulkImportOperationRepositoryTests(BulkImportOperationFixture bulkImportOperationFixture)
    {
        _bulkImportOperationFixture = bulkImportOperationFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BulkImportOperationRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var bulkImportOperation = _bulkImportOperationFixture.BulkImportOperationDto;

        // Act
        var result = await _repository.AddAsync(bulkImportOperation);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(bulkImportOperation.Description, result.Description);
        Assert.Equal(bulkImportOperation.InfraObjectName, result.InfraObjectName);
        Assert.Single(_dbContext.BulkImportOperations);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var bulkImportOperation = _bulkImportOperationFixture.BulkImportOperationDto;
        await _repository.AddAsync(bulkImportOperation);

        bulkImportOperation.Description = "UpdatedDescription";
        bulkImportOperation.Status = "UpdatedStatus";
        bulkImportOperation.InfraObjectName = "UpdatedInfraObjectName";

        // Act
        var result = await _repository.UpdateAsync(bulkImportOperation);

        // Assert
        Assert.Equal("UpdatedDescription", result.Description);
        Assert.Equal("UpdatedStatus", result.Status);
        Assert.Equal("UpdatedInfraObjectName", result.InfraObjectName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.NullReferenceException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var bulkImportOperation = _bulkImportOperationFixture.BulkImportOperationDto;
        await _repository.AddAsync(bulkImportOperation);

        // Act
        var result = await _repository.DeleteAsync(bulkImportOperation);

        // Assert
        Assert.Equal(bulkImportOperation.Description, result.Description);
        Assert.Empty(_dbContext.BulkImportOperations);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var bulkImportOperation = _bulkImportOperationFixture.BulkImportOperationDto;
        var addedEntity = await _repository.AddAsync(bulkImportOperation);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Description, result.Description);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var bulkImportOperation = _bulkImportOperationFixture.BulkImportOperationDto;
        await _repository.AddAsync(bulkImportOperation);

        // Act
        var result = await _repository.GetByReferenceIdAsync(bulkImportOperation.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(bulkImportOperation.ReferenceId, result.ReferenceId);
        Assert.Equal(bulkImportOperation.Description, result.Description);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var bulkImportOperations = _bulkImportOperationFixture.BulkImportOperationList;
        await _repository.AddRange(bulkImportOperations);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(bulkImportOperations.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDescriptionBulkImportStartAndEndTime Tests

    [Fact]
    public async Task GetDescriptionBulkImportStartAndEndTime_ShouldReturnOperationsInDateRange()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = baseDate.AddDays(5).ToString("yyyy-MM-dd");
        
        var operations = _bulkImportOperationFixture.BulkImportOperationList;
        operations[0].CreatedDate = baseDate.AddDays(-3);
        operations[1].CreatedDate = baseDate.AddDays(-1);
        operations[2].CreatedDate = baseDate.AddDays(-10);

        _dbContext.BulkImportOperations.AddRange(operations);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDescriptionBulkImportStartAndEndTime(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= DateTime.Parse(startDate)));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date <= DateTime.Parse(endDate)));
    }

    [Fact]
    public async Task GetDescriptionBulkImportStartAndEndTime_ShouldReturnEmpty_WhenNoOperationsInDateRange()
    {
        // Arrange
        var operations = _bulkImportOperationFixture.BulkImportOperationList;
        await _repository.AddRange(operations);

        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(10).ToString("yyyy-MM-dd");
        var endDate = baseDate.AddDays(15).ToString("yyyy-MM-dd");

        // Act
        var result = await _repository.GetDescriptionBulkImportStartAndEndTime(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var operation = _bulkImportOperationFixture.BulkImportOperationList;
        var operation1 = operation[0];
        var operation2 = operation[1];
        operation2.ReferenceId = Guid.NewGuid().ToString();
        operation2.Description = "DifferentDescription";

        // Act
        var task1 = _repository.AddAsync(operation1);
        var task2 = _repository.AddAsync(operation2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.BulkImportOperations.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var operations = _bulkImportOperationFixture.BulkImportOperationList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRange(operations);
        var initialCount = operations.Count;

        var toUpdate = operations.Take(2).ToList();
        toUpdate.ForEach(x => x.Status = "Updated");
        await _repository.UpdateRange(toUpdate);

        var toDelete = operations.Skip(2).Take(1).ToList();
        await _repository.RemoveRange(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Status == "Updated").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleStatusFiltering()
    {
        // Arrange
        var operations = new List<BulkImportOperation>
        {
            new BulkImportOperation
            {
                Description = "Operation1",
                Status = "Running",
                InfraObjectName = "Infra1",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportOperationFixture.CompanyId,
                IsActive = true
            },
            new BulkImportOperation
            {
                Description = "Operation2",
                Status = "Completed",
                InfraObjectName = "Infra2",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportOperationFixture.CompanyId,
                IsActive = true
            },
            new BulkImportOperation
            {
                Description = "Operation3",
                Status = "Failed",
                InfraObjectName = "Infra3",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportOperationFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRange(operations);

        // Act
        var runningOperations = await _repository.FindByFilter(x => x.Status == "Running");
        var completedOperations = await _repository.FindByFilter(x => x.Status == "Completed");
        var failedOperations = await _repository.FindByFilter(x => x.Status == "Failed");

        // Assert
        Assert.Single(runningOperations);
        Assert.Single(completedOperations);
        Assert.Single(failedOperations);
        Assert.Equal("Running", runningOperations.First().Status);
        Assert.Equal("Completed", completedOperations.First().Status);
        Assert.Equal("Failed", failedOperations.First().Status);
    }

    #endregion
}
