namespace ContinuityPatrol.Application.Features.BackUpLog.Queries.GetDetail;

public class GetBackUpLogDetailsQueryHandler : IRequestHandler<GetBackUpLogDetailQuery, BackUpLogDetailVm>
{
    private readonly IBackUpLogRepository _backUpLogRepository;
    private readonly IMapper _mapper;

    public GetBackUpLogDetailsQueryHandler(IMapper mapper, IBackUpLogRepository backUpLogRepository)
    {
        _mapper = mapper;
        _backUpLogRepository = backUpLogRepository;
    }

    public async Task<BackUpLogDetailVm> Handle(GetBackUpLogDetailQuery request, CancellationToken cancellationToken)
    {
        var backUpLog = await _backUpLogRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(backUpLog, nameof(Domain.Entities.BackUpLog),
            new NotFoundException(nameof(Domain.Entities.BackUpLog), request.Id));

        var backUpLogDetailDto = _mapper.Map<BackUpLogDetailVm>(backUpLog);

        return backUpLogDetailDto;
    }
}