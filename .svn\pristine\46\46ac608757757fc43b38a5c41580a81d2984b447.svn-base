﻿using ContinuityPatrol.Application.Features.Form.Commands.Create;
using ContinuityPatrol.Application.Features.Form.Commands.Update;
using ContinuityPatrol.Application.Features.Form.Events.Create;
using ContinuityPatrol.Application.Features.Form.Events.Delete;
using ContinuityPatrol.Application.Features.Form.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class FormFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<Form> Forms { get; set; }

    public List<UserActivity> UserActivities { get; set; }
    
    public CreateFormCommand CreateFormCommand { get; set; }
    public UpdateFormCommand UpdateFormCommand { get; set; }

    public FormCreatedEvent FormCreatedEvent { get; set; }
    public FormDeletedEvent FormDeletedEvent { get; set; }
    public FormUpdatedEvent FormUpdatedEvent { get; set; }


    public Fixture AutoFormFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateFormCommand>(p => p.Name, 10));
            fixture.Customize<CreateFormCommand>(c => c.With(b => b.Name, 10.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateFormCommand>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserActivity>(p => p.LoginName, 10));
            fixture.Customize<UpdateFormCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<Form>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<FormCreatedEvent>(p => p.FormName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<FormDeletedEvent>(p => p.FormName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<FormUpdatedEvent>(p => p.FormName, 10));

            return fixture;
        }
    }

    public FormFixture()
    {
        Forms = new List<Domain.Entities.Form>
        {
            new Domain.Entities.Form
            {
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
        };
        Forms = AutoFormFixture.Create<List<Form>>();

        UserActivities = AutoFormFixture.Create<List<UserActivity>>();

        CreateFormCommand = AutoFormFixture.Create<CreateFormCommand>();

        UpdateFormCommand = AutoFormFixture.Create<UpdateFormCommand>();

        FormCreatedEvent = AutoFormFixture.Create<FormCreatedEvent>();

        FormDeletedEvent = AutoFormFixture.Create<FormDeletedEvent>();

        FormUpdatedEvent = AutoFormFixture.Create<FormUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<FormProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public void Dispose()
    {

    }
}