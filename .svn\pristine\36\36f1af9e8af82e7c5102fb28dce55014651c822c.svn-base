﻿using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.DRReadyLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.DRReadyLog.Queries;

public class GetDrReadyLogPaginatedListQueryHandlerTests : IClassFixture<DrReadyLogFixture>
{
    private readonly DrReadyLogFixture _drReadyLogFixture;

    private readonly Mock<IDrReadyLogRepository> _drReadyLogRepositoryMock;

    private readonly GetDRReadyLogPaginatedListQueryHandler _handler;

    public GetDrReadyLogPaginatedListQueryHandlerTests(DrReadyLogFixture drReadyLogFixture)
    {
        _drReadyLogFixture = drReadyLogFixture;
    
        _drReadyLogRepositoryMock = DrReadyLogRepositoryMocks.GetPaginatedDrReadyLogRepository(_drReadyLogFixture.DrReadyLogs);
        
        _handler = new GetDRReadyLogPaginatedListQueryHandler(_drReadyLogFixture.Mapper, _drReadyLogRepositoryMock.Object);

        _drReadyLogFixture.DrReadyLogs[0].BusinessServiceName = "BS_Test_01";

        _drReadyLogFixture.DrReadyLogs[1].BusinessServiceName = "BS_Test_02";
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetDRReadyLogPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DRReadyLogListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_DrReadyLogs_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetDRReadyLogPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "BusinessServiceName=BS_Test_01" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DRReadyLogListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());
        result.Data[0].UserId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].UserId);
        result.Data[0].BusinessServiceId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].BusinessServiceId);
        result.Data[0].BusinessServiceName.ShouldBe("BS_Test_01");
        result.Data[0].BusinessFunctionId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].BusinessFunctionId);
        result.Data[0].BusinessFunctionName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].BusinessFunctionName);
        result.Data[0].IsProtected.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].IsProtected);
        result.Data[0].AffectedInfra.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].AffectedInfra);
        result.Data[0].ActiveInfra.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ActiveInfra);
        result.Data[0].WorkflowId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowId);
        result.Data[0].WorkflowName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowName);
        result.Data[0].WorkflowStatus.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowStatus);
        result.Data[0].WorkflowAttach.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowAttach);
        result.Data[0].FailedActionName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].FailedActionName);
        result.Data[0].FailedActionId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].FailedActionId);
        result.Data[0].ActiveBusinessFunction.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ActiveBusinessFunction);
        result.Data[0].AffectedBusinessFunction.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].AffectedBusinessFunction);
        result.Data[0].DRReady.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].DRReady);
        result.Data[0].NotReady.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].NotReady);
        result.Data[0].InfraObjectId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].InfraObjectId);
        result.Data[0].InfraObjectName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].InfraObjectName);
        result.Data[0].ComponentName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ComponentName);
        result.Data[0].Type.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].Type);
        result.Data[0].ErrorMessage.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ErrorMessage);

    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetDRReadyLogPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABC" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DRReadyLogListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetDRReadyLogPaginatedListQuery(), CancellationToken.None);

        _drReadyLogRepositoryMock.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}