
/*drag and drop action type to open create modal*/
const dataDisplay = $("#workflowActions");
const $nextButton = $("#next");
const $saveButton = $("#save");
const $filterProperty = $("#filter_property ul");
const $createModal = $('#CreateModal');
let workflowActionArray = [];
let modal = document.getElementById('CreateModal');
let modal2 = document.getElementById('DuplicateActionsModal');
let independentActionContainer = ['81a85aac-3ea9-45b5-8095-72bb070a091d', 'cd75fcd0-53dc-4c8c-839c-bc1a99148347', 'd0af6050-383a-4140-8f45-871fc2490494']

let actionNodeId = ''
let parentActionIcon = ''
let parentActionId = ''
let parentActionColor = ''
let parentActionType = ''
let formFields = {};
let serverDataList = [];
let isEdit = false;
let updateActionObject = {
    actionInfo: {
        properties: {}, propertyData: { propertiesInfo: [] }, formInput: []
    }
};

let newActionObj = {
    actionInfo: {
        properties: {}, propertyData: { propertiesInfo: [] }, formInput: []
    }
};

let actionList = []
let workFlowData = []
let serverListCount = {}
let databaseListCount = {};
let propertyData = {}
let checkComponents = []
let globalInsertIndex = 0;
let globalSelectedNodeId = '';
let currentActionIcon = '';

const getServerRole = async () => {

    await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Configuration/Server/GetServerRole",
        dataType: "json",
        data: {},
        success: function (result) {
            if (result.success) {

                if (result?.data && Array.isArray(result.data)) {
                    serverDataList = result?.data
                }

            } else {
                errorNotification(result);
            }
        },
    });
}

getServerRole();

const getServerOsType = (id) => {
    let data = []
    if (id) {
        $.ajax({
            type: "GET",
            async: false,
            url: RootUrl + "Configuration/Server/GetServerType",
            dataType: "json",
            data: { id: id },
            success: function (result) {
                if (result.success) {

                    if (result?.data && Array.isArray(result.data)) {
                        data = result.data
                    }

                } else {
                    errorNotification(result);
                }
            },
        });
    }

    return data
}

//const getUserList = (id) => {

//    if(id)
//        $.ajax({
//            type: "GET",
//            async: false,
//            url: RootUrl + Urls?.GetUserList,
//            dataType: "json",
//            data: { id: id },
//            success: function (result) {
//                if (result.success) {


//                } else {
//                    errorNotification(result);
//                }
//            },
//        });
//}

$(document).on('click', '.actiontype', function (e) {

    if (e?.target?.classList?.contains('childWorkflowTemplate')) {
        let getWorkflowData = e?.target?.getAttribute('data-template') || ''
        const parsedData = tryParseJSON(getWorkflowData);

        restoreUniqueObj = []
        restoreFieldObj = []
        restoreFormInput = []
        newActionObj = {
            actionInfo: {
                properties: {},
                propertyData: { propertiesInfo: [] },
                formInput: []
            }
        };

        $('.templateActionData').empty()
        $('#templateComponentModal').modal('show')
        if (parsedData) getUniqueFormInput(parsedData, 'templateActionData', 'restore')
    } else {
        let actionId = $(this).attr("id");
        actionNodeId = $(this).attr("nodeId");
        parentActionId = $(this).attr("parentId")
        parentActionIcon = $(this).attr("parentIcon")
        parentActionColor = $(this).attr("parentColor")
        parentActionType = $(this).attr("actionType")

        clearInputWFFields();
        GetActionListByNodeId(actionNodeId, actionId)
    }
});


function allowDrop(e) {
    e.preventDefault();
}
function drag(e) {

    if (e?.target?.classList?.contains('childWorkflowTemplate')) {
        let getWorkflowData = e?.target?.getAttribute('data-template') || ''
        const parsedData = tryParseJSON(getWorkflowData);

        restoreUniqueObj = []
        restoreFieldObj = []
        restoreFormInput = []
        newActionObj = {
            actionInfo: {
                properties: {},
                propertyData: { propertiesInfo: [] },
                formInput: []
            }
        };

        $('.templateActionData').empty()
        $('#templateComponentModal').modal('show')
        if (parsedData) getUniqueFormInput(parsedData, 'templateActionData', 'restore')

    } else {
        e.dataTransfer.setData("actionId", e.target.getAttribute('id'));
        e.dataTransfer.setData("nodeId", e.target.getAttribute('nodeId'));
        e.dataTransfer.setData("parentId", e.target.getAttribute('parentId'));
        e.dataTransfer.setData("parentIcon", e.target.getAttribute('parentIcon'));
        e.dataTransfer.setData("parentColor", e.target.getAttribute('parentColor'));
        e.dataTransfer.setData("actionType", e.target.getAttribute('actionType'));
    }
}
function drop(e) {
    e.preventDefault()
    let actionId = e.dataTransfer.getData("actionId");
    actionNodeId = e.dataTransfer.getData("nodeId");
    parentActionId = e.dataTransfer.getData("parentId");
    parentActionIcon = $(this).attr("parentIcon");
    parentActionColor = $(this).attr("parentColor");
    parentActionType = e.dataTransfer.getData('actionType');

    clearInputWFFields();
    GetActionListByNodeId(actionNodeId, actionId)
}

function tryParseJSON(jsonString) {
    try {
        const decoded = atob(jsonString);
        const parsed = JSON.parse(decoded);
        return parsed;
    } catch (e) {
        return null;
    }
}

/*Create action*/

$nextButton.on("click", async function (e) {
    e.preventDefault();
    $(".Common-input").each(async function () {
        let inputValue = $(this).val();
        if ($(this).attr("type") === 'checkbox') inputValue = $(this).is(':checked')
        let inputName = $(this).attr("name");
        newActionObj['actionInfo'][inputName] = inputValue
        if (inputName === 'actionType') {
            if (newActionObj.actionInfo.hasOwnProperty('propertyData')) {
                newActionObj['actionInfo']['propertyData'][inputName] = $(this).find(":selected").text()
            }
        }
    });

    if (!newActionObj.actionInfo.hasOwnProperty('nodeId')) newActionObj['actionInfo']['nodeId'] = actionNodeId

    if (formValidation(newActionObj.actionInfo)) {
        $nextButton.addClass('next-disabled');
        await loadFormData(newActionObj?.actionInfo?.actionType, 'sectionData')
        //if (GlobalIsLock) {
        //    $(".checkSaveWorkflow").show();
        //}      
    }
    if (GlobalIsLock) {
        $saveButton.addClass('next-disabled')
    } else {
        $saveButton.removeClass('next-disabled')
    }
    // $(".checkSaveWorkflow").show()
});

function populateFormWithData(data) {
    for (var key in data) {
        if (data.hasOwnProperty(key)) {
            if (key !== 'properties' && key !== 'formInput') {
                (key == 'email' || key == 'sms') ? $('#' + key).prop('checked', data[key]) : $('#' + key).val(data[key]);
            }
        }
    }
}
function addDataAndNotify(newData) {
    if (isEdit) {
        $('#' + newData.actionInfo.uniqueId).attr("details", btoa(JSON.stringify(newData)))
            .find('.actionSpan').text(newData?.actionInfo?.actionName).attr('title', newData?.actionInfo?.actionName);
        updateIndexValues();
        loadActionsCount(newData, 'edit');
    } else {
        let selectedNodeId = $("#workflowActions .selectedWfContainer").last().attr('id')
        const insertIndex = $(".workflowActions").index($("#" + selectedNodeId));
        appendData(newData, selectedNodeId, insertIndex);
    }
    $createModal.modal('hide');
}

const actionEncryptDecrypt = async (text) => {
    return await EncryptPassword(text)
}

function appendData(newData, selectedNodeId, insertIndex) {
    let actionType = $('.nodeSummaryData[id=' + newData.actionInfo.actionType + ']').attr('actionType')
    if (!newData.actionInfo.hasOwnProperty('type')) newData.actionInfo.type = actionType || ''
    let encodedData = btoa(JSON.stringify(newData))

    let ColorData = $('.nodeSummaryData[nodeId=' + newData.actionInfo.nodeId + ']').attr('parentColor')
    let iconData = `${$('.nodeSummaryData[nodeId=' + newData.actionInfo.nodeId + ']').attr('parentIcon') || 'cp-flow'} workflowTextClass circle fs-7`

    ColorData = ColorData === 'rgb(255,255,255)' ? '#3562AA' : ColorData;

    let backGroundColor = '';
    if (actionType) {
        backGroundColor = actionType.toLowerCase() === 'common' ? '#0e97ff' : actionType.toLowerCase() === 'operation' ? '#f30' : actionType.toLowerCase() === 'monitoring' ? '#009' : actionType.toLowerCase() === 'monitoring' ? '#f90' : '#08a200'
    }

    let isIndependent = independentActionContainer.includes(newData?.actionInfo?.actionType)

    const newItemElement = `
        <div class='ui-sortable-handle'>
            <i class='cp-workflow-line fs-7'></i>
            <div class="workflowActions justify-content-between ${newData.actionInfo.hasOwnProperty('isVerifiedAction') && !newData.actionInfo.isVerifiedAction ? 'exclamation-icon exclamationContainer' : ''
} " role="button" parentId="${ newData?.actionInfo?.parentActionId || '' }" id=${newData?.actionInfo?.uniqueId} details='${encodedData}' ${isIndependent ? 'isIndependent=' + isIndependent : ''}>
                <i class='${iconData} workflowIcon' style='color:white;background:${backGroundColor}'></i>
                <div class='flex-fill text-truncate mx-2'>
                    <span class='workflowIndex'></span>
                    . 
                    <span class='actionSpan' title=${newData?.actionInfo?.actionName}>${newData?.actionInfo?.actionName}</span>
                </div>
                <input type='checkbox' class='actionCheckBox' name='actionCheck' />
            </div>
        </div>`;
    $('#AISuggestion').remove()

    if (selectedNodeId && insertIndex !== -1) {
        globalInsertIndex = insertIndex
        globalSelectedNodeId = selectedNodeId
        const parentElement = $("#workflowActions").children();
        parentElement.eq(insertIndex).after(newItemElement);
        parentElement.children().removeClass("selectedWfContainer")
    } else {
        dataDisplay.append(newItemElement);
    }

    updateFilterProperties(newData)
    updateIndexValues();
    loadActionsCount(newData);
    $('.actionCheckBox').hide();
}

$saveButton.on("click", async function (e) {
    e.preventDefault();

    newActionObj.actionInfo.properties = {}
    let checkCPLCommand = true
    let elements = $(".my-i")?.toArray();

    for (let el of elements) {
        let $select = $(el);

        let inputName = $select.attr("name");
        let id = $select.attr("id");
        let typeId = $select.data("typeid");
        let inputValue = '';
        let inputText = '';
        let parentDisplay = $('#' + id).parent().parent().hasClass('d-none');
        let isEncryption = $select.data("encryption") ?? false;
        let scriptTypeValue = $('select[name="scriptType"]').val();

        if (!parentDisplay) {
            if ($select.is('select')) {

                let selectedOption = $select[0].selectize.options[$select.val()];
                let selectedItems = $select[0].selectize.items;
                if (selectedOption || selectedItems.length) {
                    inputValue = selectedOption ? selectedOption.value : selectedItems;
                    inputText = selectedOption ? selectedOption.text : selectedItems;

                    let inputServerType = selectedOption ? selectedOption['servertype'] : '';
                    let inputRoleType = selectedOption ? selectedOption['roletype'] : '';

                    if (inputName !== undefined) {
                        newActionObj?.actionInfo?.formInput.filter((action) => {
                            if (action?.name == inputName) {
                                action.optionType = inputServerType
                                action.optionRoleType = inputRoleType
                                action.id = inputValue

                            }
                        })
                    }
                }
            } else if ($select.attr("type") === 'checkbox' || $select.attr("type") == 'radio') {
                inputValue = $select.is(':checked');
            } else if ($select.attr("type") === 'textarea' || typeId?.toLowerCase() == 'command' || isEncryption) {
                let filteredAction = actionList.find((action) => action.id === newActionObj?.actionInfo?.actionType)

                if (filteredAction.actionName.toLowerCase() === 'executecpl' || typeId?.toLowerCase() == 'command' || isEncryption) {
                    let findTextArea = $('#scriptParent')?.parent()?.parent()?.find('textarea');
                    let textVal = findTextArea?.eq(0)?.val();
                    let textName = findTextArea?.eq(0)?.attr('name');
                    let isCPButton = $('#scriptCheck')?.text()?.replace(/\s+/g, '')?.toLowerCase() == 'validatecp#'

                    let value = $select?.val();

                    let isEncryptAllowed = scriptTypeValue?.toLowerCase() == 'CP#' || (isCPButton && inputName !== '@@cpslscript')

                    if (typeId?.toLowerCase() !== 'command') {
                        if (isCPButton && textName == '@@cpslscript') {
                            textName = findTextArea?.eq(1)?.attr('name')
                        } else if (!scriptTypeValue) {
                            inputName = findTextArea?.eq(1)?.attr('name')
                        }

                        if (scriptTypeValue?.toLowerCase() == 'cpsl' && inputName !== '@@cpslscript') textVal = $('#cpslTextArea').val();
                    }

                    checkCPLCommand = isEncryption ? true : (typeId?.toLowerCase() == 'command') ? true :
                        (isEncryptAllowed ? await checkValidCPSharp(textVal, `${textName}-error`, 'script') : true)

                    let isValidate = commandValidate(value, `${inputName}-error`)

                    if (isValidate) return false;

                    if (checkCPLCommand) {

                        try {
                            let encryptedValue = await $.ajax({
                                type: "POST",
                                url: RootUrl + "ITAutomation/WorkflowConfiguration/WorkFlowDataEncrypt",
                                data: { data: value, __RequestVerificationToken: gettoken() },
                                dataType: "text",
                            });

                            let encryptedData = JSON.parse(encryptedValue);
                            inputValue = encryptedData?.data ? encryptedData.data + "_encryptedcpl" : inputValue;

                            newActionObj.actionInfo.properties[inputName] = inputValue;
                        } catch (error) {
                            console.error("Error occurred during encryption:", error);
                        }
                    }
                } else {
                    inputValue = $select.val();
                }

            } else {
                inputValue = $select.val();
            }

            if (inputName !== undefined) {
                let inputGroupDiv = $select.closest('.input-group');
                let associatedLabel = '';

                if (inputGroupDiv.parent()) {
                    associatedLabel = ($select.attr("type") === 'checkbox') ? inputGroupDiv.parent().find('.form-label-label').text() : inputGroupDiv.parent().find('.form-label').text();
                }

                let labelText = associatedLabel.replace(/\*/g, '');

                newActionObj.actionInfo.properties[inputName] = inputValue;

                if (newActionObj.actionInfo.hasOwnProperty('propertyData') && actionFormValidation(newActionObj)) {
                    let foundIdIndex = newActionObj.actionInfo.propertyData.propertiesInfo.findIndex(data => data.id == id);

                    if (foundIdIndex !== -1) {
                        newActionObj.actionInfo.propertyData.propertiesInfo.splice(foundIdIndex, 1);
                    }

                    let obj = {
                        id: inputValue,
                    };
                    obj['label'] = labelText ? labelText : '';
                    obj['value'] = inputText ? inputText : inputValue;
                    newActionObj.actionInfo.propertyData.propertiesInfo.push(obj);
                }
            }
        }
    }

    if (!newActionObj.actionInfo.hasOwnProperty('uniqueId')) newActionObj.actionInfo.uniqueId = getRandomId('node')
    if (!newActionObj.actionInfo.hasOwnProperty('IsParallel')) newActionObj.actionInfo.IsParallel = false
    if (!newActionObj.actionInfo.hasOwnProperty('parentActionId')) newActionObj.actionInfo.parentActionId = parentActionId
    if (!newActionObj.actionInfo.hasOwnProperty('icon')) newActionObj.actionInfo.icon = parentActionIcon
    if (!newActionObj.actionInfo.hasOwnProperty('color')) newActionObj.actionInfo.color = parentActionColor ? parentActionColor : '#ADD8E6'
    if (!newActionObj.actionInfo.hasOwnProperty('type')) newActionObj.actionInfo.type = parentActionType || ''
    if (!newActionObj.hasOwnProperty("stepId")) newActionObj.stepId = generateStepId();

    if (isEdit) {
        workflowActionUniqueCheck(newActionObj)
        if ($(`#${newActionObj?.actionInfo.uniqueId}`).hasClass('exclamationContainer')) {
            $(`#${newActionObj?.actionInfo.uniqueId}`).removeClass('exclamationContainer exclamation-icon')
        }
        if (workflowActionArray.length > 0) {
            let a = updateActionObject.actionInfo.properties
            let b = newActionObj.actionInfo.properties
            if (JSON.stringify(a) !== JSON.stringify(b)) {
                if (actionFormValidation(newActionObj) && checkCPLCommand) {
                    $('#textDuplicate1').html("The " + "<span class='text-primary'>" + newActionObj.actionInfo.actionName + "</span> action already exists in the workflow. Are you sure you want to override all the existing actions?")
                    $('#CreateModal').modal('hide')
                    $('#DuplicateActionsModal').modal('show')
                }

                return false
            } else {
                if (actionFormValidation(newActionObj) && checkCPLCommand) {
                    updateActionProperties();
                }

            }
        } else {
            if (actionFormValidation(newActionObj) && checkCPLCommand) {
                updateActionProperties();
            }

        }
    } else {
        setTimeout(() => {
            if (actionFormValidation(newActionObj) && checkCPLCommand) {
                updateActionProperties();
                workflowAISuggestion(newActionObj);
            }
        }, 200)

    }

    $('#mismatchAISuggestion').hide()
    $('#AIIngnoreButton').hide();
    $(".checkSaveWorkflow").show()
});

const commandValidate = (value, id) => {
    const errorText = $(`[data-id="${id}"]`).data('error');

    if (!value) {
        $(`[data-id='${id}']`)?.text(errorText ? errorText : '')?.addClass('field-validation-error')
        return true
    } else {
        return false
    }
}

const submitFormIfReady = () => {
    if (actionFormValidation(newActionObj)) {
        updateActionProperties()
        !isEdit && workflowAISuggestion(newActionObj)
    }
}

$(document).on('click', '#scriptCheck', async function (e) {
    e.preventDefault();

    let findTextArea = $(this).parent().parent().find('textarea');
    let textVal = findTextArea?.eq(0)?.val();
    let textName = findTextArea?.eq(0)?.attr('name');
    let scriptTypeValue = $('select[name="scriptType"]').val();

    if (textName == '@@cpslscript') {
        textName = findTextArea?.eq(1)?.attr('name')
    }

    const errorElement = $(`[data-id='${textName}-error']`);

    if (textVal && $(this)?.text()?.replace(/\s+/g, '')?.toLowerCase() == 'validatescript') {
        await checkValidCpl(textVal, `${textName}-error`, 'script', scriptTypeValue);
    } else if (textVal && $(this)?.text()?.replace(/\s+/g, '')?.toLowerCase() == 'validatecp#') {

        if (scriptTypeValue?.toLowerCase() == 'cpsl') textVal = findTextArea?.eq(1)?.val();
        await checkValidCPSharp(textVal, `${textName}-error`, 'script');
    }
    else errorElement.html('<p style="width: 85%; text-align: start; font-size: 12px">Enter Script</p>')
        .addClass('field-validation-error');

});


const workflowAISuggestion = (newActionObj) => {
    //if ($('.selected').length === 0) {
    setTimeout(() => {
        if ($('#workflowActions').children().length < 2) {
            getPrediction(newActionObj.actionInfo.actionType, '')

        } else {
            let gerPreviousNodeId = dataDisplay.children().last().prev().children()[1].id
            getPrediction(newActionObj.actionInfo.actionType, JSON.parse(atob($('#' + gerPreviousNodeId).attr('details'))).actionInfo.actionType)
            //lastConfiguredAction(newActionObj, '#lastConfiguredAISuggestion')
        }
        removeErrorSugg();
    }, 1000)
    //} else {
    //    let selectedNodeId = $("#workflowActions .selected").last().attr('id')
    //    const insertIndex = $(".workflowActions").index($("#" + selectedNodeId));
    //}

}

const updateActionProperties = () => {
    addDataAndNotify(newActionObj);
    $('#btnSaveModalOpen').prop('disabled', false);
    if ($("#workflowActions").children().length === 1 && workflowSaveMode === "Save") {
        $('#workflowTitle').text('Untitled Workflow');
    }

}

$('#duplicateConfirmation').on('click', function () {
    if (workflowActionArray.length > 0) {
        workflowActionArray.forEach((action) => {
            let getActionDetails = JSON.parse(atob($('#' + action.actionInfo.uniqueId).attr('details')))
            getActionDetails.actionInfo.properties = newActionObj.actionInfo.properties
            getActionDetails.actionInfo.formInput = newActionObj.actionInfo.formInput
            getActionDetails.actionInfo.actionType = newActionObj.actionInfo.actionType
            $('#' + action.actionInfo.uniqueId).attr("details", btoa(JSON.stringify(getActionDetails))).find('.actionSpan').text(getActionDetails?.actionInfo?.actionName).attr('title', getActionDetails?.actionInfo?.actionName);
            updateIndexValues();
            loadActionsCount(getActionDetails);
        })
    }
    updateActionProperties();
    $('#DuplicateActionsModal').modal('hide')
    $(".checkSaveWorkflow").show();
    $createModal.modal('hide');
})

const generateStepId = () => {
    return Math.floor(Math.random() * 100000) + "-" + Date.now() + "-" + Math.floor(Math.random() * 100000)
}

const clearInputWFFields = () => {

    if (!GlobalIsLock) {
        let objKeys = Object.keys(newActionObj.actionInfo)
        objKeys.forEach((key) => {
            if (key !== 'properties' && key !== 'formInput' && key !== 'propertyData') {
                (key == 'email' || key == 'sms') ? $('#' + key).prop('checked', false) : $('#' + key).val('');
                $('#' + key + '-error').text('').removeClass('field-validation-error');
            }
        })

        newActionObj = {
            actionInfo: {
                properties: {},
                propertyData: { propertiesInfo: [] },
                formInput: []
            }
        };
        infraRoleReverseDetails = [];
        form.steps('previous');
        $nextButton.removeClass('next-disabled');

        isEdit = false
        $createModal.modal('show');
        $(".sectionData").empty();
    }
};
async function GetActionListByNodeId(actionNodeId, actionId = '') {
    let data = {};
    data.nodeId = actionNodeId;

    $('#actionType').empty()

    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.GetWorkflowAction,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {

                $('#actionType').append(`<option disabled selected>Select Action Category</option>`);

                if (Array.isArray(result.data) && result.data.length) {
                    actionList = result.data

                    actionList.forEach((action) => {
                        $('#actionType').append(`<option value="${action.id}"> ${action.actionName} </option>`);
                    });

                } else {
                    $('#actionType').append(`<option disabled> No Data Found </option>`);
                }

                if (newActionObj.actionInfo.hasOwnProperty('actionType')) {
                    $('#actionType').val(newActionObj.actionInfo.actionType);
                } else if (actionId) {
                    $('#actionType').val(actionId);

                }
                $('#next').removeClass('disabled')

            } else {
                errorNotification(result)
            }
        }
    })
}
async function loadFormData(actionType, parentId) {
    let particularActionList = actionList.filter((s) => s?.id === actionType)
    let selectedActionText = particularActionList?.length ? $(`#${particularActionList[0]?.nodeId}`)?.text() : '';

    let actionss = particularActionList?.length ? JSON.parse(particularActionList[0]?.properties) : []

    currentActionIcon = actionss?.color;

    let forms = actionss?.formInput
    formFields = forms?.fields ? forms?.fields : {};

    let fieldKeys = Object.keys(formFields)
    let formFieldsArray = Object.entries(formFields);

    formFieldsArray?.length && formFieldsArray.sort((a, b) => {
        return a[1]?.index - b[1]?.index;
    });

    fieldKeys = formFieldsArray?.length && formFieldsArray.map(entry => entry[0]);

    let uniqueFieldName = []

    //if (isTemplateWorkFlow && isEdit) {
    //    let templateFilterFields = {}
    //    let templateFilterFieldKeys = []

    //    fieldKeys.forEach((field) => {
    //        if (newActionObj?.actionInfo?.properties?.hasOwnProperty(formFields[field]?.attrs?.name)) {
    //            templateFilterFields[field] = formFields[field]
    //            templateFilterFieldKeys.push(field)
    //        }
    //    })

    //    formFields = templateFilterFields
    //    fieldKeys = templateFilterFieldKeys
    //}

    if (fieldKeys?.length) {
        var html = ""
        var data = {}

        newActionObj.actionInfo.formInput = []
        if (newActionObj?.actionInfo?.hasOwnProperty('propertyData')) newActionObj.actionInfo.propertyData.propertiesInfo = []

        for (let i = 0; i < fieldKeys?.length; i++) {
            let fieldObj = formFields[fieldKeys[i]]
            let fieldValue = fieldObj?.attrs?.textInputValue ?? ''
            let fieldDisable = fieldObj?.attrs?.readonly ?? false
            let fieldName = fieldObj?.attrs?.name
            let isFieldRequired = fieldObj?.attrs?.required ?? true;
            let isEncryption = fieldObj?.attrs?.encryption ?? false;

            var tempElement = document.createElement('div');

            tempElement.innerHTML = fieldObj?.config?.label;

            var textContent = tempElement?.textContent || tempElement?.innerText;

            if (textContent) fieldObj.config.label = textContent

            uniqueFieldName.push(fieldName)

            if (fieldObj?.attrs?.type === 'checkbox') {

                for (let k = 0; k < fieldObj?.options?.length; k++) {

                    html += "<div class='mb-3 form-check'>";
                    html += `<input type='${fieldObj?.attrs?.type}' id='${fieldKeys[i]}' class='form-check-input my-i' name='${fieldName}' placeholder='' 
                    oninput='handleFormDataChange(event, "")' ${fieldDisable ? 'disabled' : ''} ${fieldObj?.options[k]?.checked ? 'checked' : ''} />`;

                    html += "<div class='form-check-label'>" + fieldObj?.options[k]?.label + ' ' + (!isFieldRequired ? '(Optional)' : '') + "</div>";
                    html += "</div>";
                }

            } else if (fieldObj?.meta?.id === 'html.header') {
                html += `<div class="fw-semibold fs-7 mb-2">${fieldObj?.content}</div>`
            } else if (fieldObj?.meta?.id === 'divider') {
                html += `<hr></hr>`
            }
            else {
                let selectIcon = fieldObj?.meta?.id === 'server' ? 'cp-server' : fieldObj?.meta?.id === 'database' ? 'cp-database' : fieldObj?.meta?.id === 'replication' ? 'cp-replication-on' : 'cp-text'
                html += "<div class='mb-3 form-group'>";
                html += "<div class='form-label'>" + fieldObj?.config?.label + ' ' + (!isFieldRequired ? '(Optional)' : '') + "</div>";

                if (fieldObj?.attrs.type === 'textarea' && particularActionList[0].actionName.toLowerCase() === 'executecpl') html += `<div class='d-flex gap-3'><div class="w-100"><label class='form-label d-none' id='cpslLabel'>CPSL Script</label>`

                html += `<div class="input-group ${fieldObj?.attrs?.type === 'textarea' ? 'align-items-start' : ''}" style="${fieldObj?.attrs?.type === 'textarea' && 'height: auto !important'}">`;
                html += "<span class='input-group-text'><i class=" + selectIcon + "></i></span>";

                if (fieldObj?.attrs?.type === 'text' || fieldObj?.attrs?.type === 'number' || fieldObj?.attrs?.type === 'password' || fieldObj?.attrs?.type === 'date') {
                    html += `<input type='${fieldObj?.attrs?.type}' id='${fieldKeys[i]}' data-typeId='${fieldObj?.meta?.id}' class='form-control my-i' value='${fieldValue}' name='${fieldName}' placeholder='${fieldObj?.config?.label}' 
                    data-encryption='${isEncryption}' isIPAddress='${fieldObj?.attrs?.attrid}' oninput='handleFormDataChange(event, "${fieldName}-error")' ${fieldDisable ? 'disabled' : ''} />`;

                } else if (fieldObj?.attrs?.type === 'textarea') {
                    html += `<textarea type='${fieldObj?.attrs?.type}' id='${fieldKeys[i]}' data-typeId='${fieldObj?.meta?.id}' class='form-control my-i' name='${particularActionList[0]?.actionName?.toLowerCase() === 'executecpl' ? '@@cpslscript' : fieldName}' placeholder='${fieldObj?.config?.label}' 
                    data-encryption='${isEncryption}' oninput='handleFormDataChange(event, "${fieldName}-error")' style='${selectedActionText?.trim()?.toLowerCase() == "as400" ? "font-weight: 700;height: 170px" : ""}'></textarea>`;

                } else {
                    let selectObj = {
                        label: fieldObj?.config?.label,
                        type: fieldObj?.meta?.id,
                        name: fieldName,
                    }

                    html += `<select  class='my-i select_actions forSelectLoad' componentType=${fieldObj?.meta?.id === 'server' ? fieldObj?.meta?.id : 'nonServer'}  id='${fieldKeys[i]}' data-live-search='${true}' name='${fieldName}'
                   data-placeholder='${fieldObj?.attrs?.placeholder || ''}' oninput='handleFormDataChange(event, "${fieldName}-error")' ${fieldObj?.attrs?.multiple && 'multiple'}>`;

                    if (fieldObj?.meta?.id === 'server') {

                        const { filteredServer, getServerSubType } = getServerById(fieldObj?.attrs?.ServerRole, fieldObj?.attrs?.ServerType)

                        data = {}
                        data.roleType = fieldObj?.attrs?.ServerRole && (fieldObj?.attrs?.ServerRole.toLowerCase() !== 'all' || !fieldObj?.attrs?.ServerRole) ? filteredServer?.id : '';
                        data.serverType = fieldObj?.attrs?.ServerType && (fieldObj?.attrs?.ServerType.toLowerCase() !== 'all' || !fieldObj?.attrs?.ServerType) ? getServerSubType?.id ? getServerSubType?.id : '' : '';

                        selectObj.ServerRole = data?.roleType
                        selectObj.ServerType = data?.serverType

                    } else if (fieldObj?.meta?.id === 'database') {
                        data = {}
                        data.type = fieldObj?.attrs?.DatabaseType && fieldObj?.attrs?.DatabaseType?.toLowerCase() !== 'all' ? fieldObj?.attrs?.DatabaseType : '';

                        selectObj.DatabaseType = data?.type
                    }
                    newActionObj.actionInfo.formInput.push(selectObj)

                    let url = 'ITAutomation/WorkflowConfiguration/GetServerByRoleTypeAndServerType'

                    if (fieldObj?.meta?.id === 'database') {
                        url = 'Configuration/Database/GetDataBaseList'

                    } else if (fieldObj?.meta?.id === 'replication') {
                        data = {}
                        url = 'ITAutomation/WorkflowConfiguration/GetReplicationNames'
                    }
                    else if (fieldObj?.meta?.id === 'workflow') {
                        data = {}
                        url = 'ITAutomation/WorkflowConfiguration/GetWorkflowList'
                    }
                    else if (fieldObj?.meta?.id === 'infraobject') {
                        data = {}
                        url = 'ITAutomation/WorkflowConfiguration/GetInfraObjectList'
                    }
                    else if (fieldObj?.attrs?.name === '@@AirGapName') {
                        data = {}
                        url = 'ITAutomation/WorkflowConfiguration/GetAirGapList'
                    }
                    //else if (fieldObj?.meta.id === 'ip-port') {                        
                    //    var IPPort = `https://${fieldObj.attrs.ipaddress}:${fieldObj.attrs.port}/api/v2/login`;
                    //    let data2 = {
                    //        'Username': fieldObj.attrs.username,
                    //        'Password': fieldObj.attrs.password
                    //    };
                    //    var IPPortData = await GetAsync(IPPort, data2, OnError);
                    //    console.log(IPPortData);
                    //}

                    if (fieldObj?.meta?.id !== 'select' || fieldObj?.attrs?.name === '@@AirGapName') {

                        if (fieldObj?.attrs?.name === '@@AirGapName') {
                            data = {}
                        }

                        var serverTypes = await GetAsync(RootUrl + url, data, OnError)

                        serverTypes = serverTypes.hasOwnProperty('data') ? serverTypes?.data : serverTypes

                        html += "<option value=''>Select " + fieldObj?.config?.label + "</option>";

                        for (var k = 0; k < serverTypes?.length; k++) {
                            let servertype = (fieldObj?.meta?.id === 'server') ? serverTypes[k]?.serverType : serverTypes[k]?.type ? serverTypes[k].type : (fieldObj?.meta?.id === 'infraobject') ? 'infraobject' : (fieldObj?.attrs?.name === '@@AirGapName') ? 'airgap' : 'workflow'
                            let roletype = (fieldObj?.meta?.id === 'server') ? serverTypes[k]?.roleType : ''

                            html += "<option value='" + serverTypes[k]?.id + "' data-servertype='" + servertype + "' data-roletype='" + roletype + "'>" + serverTypes[k]?.name + "</option>";

                        }
                    } else {
                        for (var k = 0; k < fieldObj?.options?.length; k++) {

                            html += "<option value='" + fieldObj?.options[k]?.value + "' data-servertype='' data-roletype=''>" + fieldObj?.options[k]?.label + "</option>";

                        }
                    }

                    html += "</select>"

                }


                html += "</div>";
                if (fieldObj?.attrs.type === 'textarea' && particularActionList[0].actionName.toLowerCase() === 'executecpl') {
                    html += `</div><div class="w-100 d-none"><label class='form-label'>CP# Script</label><div class="input-group align-items-start" style="height: auto !important">
                    <span class="input-group-text"><i class="cp-text"></i></span>
                    <textarea type="textarea"  name='${fieldName}' class="form-control my-i" id="cpslTextArea" placeholder="Script Block" style="height: 228px;" oninput='handleFormDataChange(event, "")' ></textarea></div></div>`;
                }

                if (fieldObj?.attrs.type === 'textarea' && particularActionList[0].actionName.toLowerCase() === 'executecpl') html += `</div>`

                html += `<span data-id="${fieldName}-error" data-error="${fieldObj?.attrs.type === 'select' ? 'Select' : 'Enter'} ${fieldObj?.config?.label.toLowerCase()}"
                data-required = '${fieldObj?.attrs?.required}'></span>`;

                if (fieldObj?.attrs.type === 'textarea' && particularActionList[0].actionName.toLowerCase() === 'executecpl') {
                    html += `<div style="position: absolute;z-index: 99999;right: 0;bottom: -45px;" id="scriptParent"><button class="btn btn-primary" id="scriptCheck">Validate Script</button><button class="btn btn-primary ms-1 d-none" id="cpslConvert">Convert To CP#</button></div>`;
                }

                html += "</div>";


                html += "</div>";

                if (fieldObj?.meta.id === 'workflow' && fieldObj?.attrs?.dependentAction) {
                    uniqueFieldName.push('@@DependentWorkflowAction', '@@workflow_action')
                    html = await loadSpecificWorkflow(selectIcon, fieldObj, html);
                }

            }
        }

        isEdit ? $saveButton.text('Update') : $saveButton.text('Save')

        setTimeout(() => {
            $("." + parentId).append(html);

            if (!isEdit) {
                fieldKeys.forEach(function (fieldId) {
                    var field = formFields[fieldId];

                    if (field.conditions && field.conditions.length) {
                        field.conditions.forEach(function (condition) {
                            condition.if.forEach(function (ifClause) {
                                condition.then.forEach(function (thenClause) {
                                    if ((thenClause.targetProperty === 'isVisible') && thenClause.assignment === 'equals' && ifClause.comparison !== "notEquals") {

                                        let getId = thenClause.target.replace('fields.', '');
                                        var targetElement = document.getElementById(`${getId}`);

                                        if (targetElement && !targetElement.parentNode.parentNode.classList.contains("d-none") && !targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) {
                                            targetElement.parentNode.parentNode.classList.add('d-none');
                                        }
                                    }
                                });
                            });
                        });
                    }
                });
            }

            $('.forSelectLoad').selectize({
                normalize: true,
                openOnFocus: false,
                create: false,
                createOnBlur: true,
                closeAfterSelect: true,
                plugins: ["remove_button"],
                score: function (search) {
                    let score = this.getScoreFunction(search);
                    return function (item) {
                        return score(item) + (item.text.toLowerCase().indexOf(search.toLowerCase()) + 1) * 1000;
                    };
                },

                onDropdownOpen: function ($dropdown) {
                    let currrentDrop = $dropdown?.parent()?.siblings('select')[0]?.selectize
                    $('.selectize-dropdown').each(function () {
                        let selectizeInstance = $(this).parent().siblings('select')[0]?.selectize;
                        if (selectizeInstance && selectizeInstance.isOpen && selectizeInstance !== currrentDrop) {
                            selectizeInstance.close();
                        }
                    });
                },

                onType: function (input) {
                    const maxLength = 100;
                    if (input?.length > maxLength) {
                        this?.setTextboxValue(input?.slice(0, maxLength));
                    }
                }

            });

            if (newActionObj?.actionInfo?.properties && newActionObj?.actionInfo?.uniqueId) {
                if (newActionObj.actionInfo.properties.hasOwnProperty('@@workflow_name')) uniqueFieldName.push('@@workflow_name')
                if (newActionObj.actionInfo.properties.hasOwnProperty('@@cpslscript')) uniqueFieldName.push('@@cpslscript')

                let propKeys = Object.keys(newActionObj?.actionInfo?.properties)

                propKeys.forEach((pKey) => {
                    if (!uniqueFieldName.includes(pKey)) {
                        delete newActionObj?.actionInfo?.properties[pKey]
                    }
                })

                if (!uniqueFieldName.includes('@@cpslscript')) {
                    let getName = $('#cpslTextArea')?.attr('name')
                    $('textarea[name="@@cpslscript"]')?.attr('name', getName)
                    $('#scriptCheck').text('Validate CP#')
                }

                $(".my-i").each(async function () {
                    let inputname = $(this).attr("name");
                    let typeId = $(this).data('typeid')
                    let isEncryption = $(this).data("encryption") ?? false;

                    if (inputname && inputname.toLowerCase() == '@@dependentworkflowaction') {
                        if (!newActionObj.actionInfo.properties.hasOwnProperty(inputname)) inputname = '@@workflow_action'
                    } else if (inputname && inputname.toLowerCase() == '@@dependentworkflowname') {
                        if (!newActionObj.actionInfo.properties.hasOwnProperty(inputname)) inputname = '@@workflow_name'
                    }

                    if (newActionObj.actionInfo.properties.hasOwnProperty(inputname)) {
                        if ($(this).is(":checkbox")) {
                            $(this).prop("checked", newActionObj.actionInfo.properties[inputname]);
                            $(this).trigger('input');
                        } else if ($(this).attr('type') === 'textarea' || typeId?.toLowerCase() === 'command' || isEncryption) {
                            let filteredAction = actionList.find((action) => action.id === newActionObj?.actionInfo?.actionType)

                            if (filteredAction?.actionName.toLowerCase() === 'executecpl' || typeId?.toLowerCase() === 'command' || isEncryption) {
                                let modifiedValue = newActionObj.actionInfo.properties[inputname] &&
                                    newActionObj.actionInfo.properties[inputname].split('_encryptedcpl');
                                let $textarea = $(this);

                                $.ajax({
                                    type: "POST",
                                    url: RootUrl + 'ITAutomation/WorkflowConfiguration/WorkFlowDataDecrypt',
                                    data: { data: modifiedValue?.length ? modifiedValue[0] : '', __RequestVerificationToken: gettoken() },
                                    dataType: 'text',
                                    success: function (data) {
                                        let decryptedData = JSON.parse(data);

                                        if ($textarea?.attr('id') == 'cpslTextArea' && uniqueFieldName.includes('@@cpslscript')) {
                                            if ($textarea?.parent()?.parent()?.hasClass('d-none')) {
                                                $textarea?.parent()?.parent()?.removeClass('d-none')
                                                $('#cpslLabel').removeClass('d-none')
                                                $('#scriptCheck').text('Validate CP#')
                                            }
                                        }

                                        $textarea.val(decryptedData.data);
                                        $textarea.trigger('input');
                                    }
                                });

                            } else {
                                $(this).val(newActionObj.actionInfo.properties[inputname])
                            }

                        } else {
                            if (inputname == '@@DependentWorkflowAction' || inputname == '@@workflow_action') {
                                let keyName = Object.keys(newActionObj.actionInfo.properties)
                                    .find((key) => key.toLowerCase().includes('@@dependentworkflowname') || key.toLowerCase().includes('@@workflowname'));

                                let workflowValue = newActionObj.actionInfo.properties[keyName] ?? newActionObj.actionInfo.properties['@@workflow_name']
                                loadWorflowAction(workflowValue, newActionObj.actionInfo.properties[inputname])
                            } else {

                                if ($(this)[0] && $(this).is('select')) {
                                    $(this)[0].selectize.setValue(newActionObj.actionInfo.properties[inputname]);
                                } else {
                                    $(this).val(newActionObj.actionInfo.properties[inputname])
                                }
                            }
                            $(this).trigger('input');
                            $(`[data-id="${inputname}-error"]`).removeClass('field-validation-error').text('');

                        }
                    }
                });
            }

            $nextButton.removeClass('next-disabled');

            setTimeout(() => {
                const passwordInputs = document.querySelectorAll('input[type="password"]');

                passwordInputs?.forEach(inputs => {
                    let id = inputs?.getAttribute("id");

                    document.getElementById(id)?.addEventListener("focus", async function (e) {
                        if (e.target.value.length > 64) {
                            let pwd = await DecryptPassword(e.target.value)//Common.js.
                            e.target.value = pwd;
                        }

                    });

                    document.getElementById(id)?.addEventListener('blur', async function (e) {
                        if (e.target.value) {
                            let pwd = await EncryptPassword(e.target.value)//Common.js.
                            e.target.value = pwd;
                        }
                    });
                })
            });

        }, 200)

        form.steps('next')
    } else {
        notificationAlert("warning", 'No forms configuration for this action')
        $nextButton.removeClass('next-disabled')
    }
}

const getServerById = (roleType, serverType) => {

    let role = roleType?.toLowerCase()
    let filteredServer = serverDataList.length && serverDataList.find(data => data?.name?.toLowerCase() === role)
    let getServerType = getServerOsType(filteredServer?.id);
    let getServerSubType = getServerType.length && getServerType.find(data => data?.name?.toLowerCase() === serverType?.toLowerCase())
    return { filteredServer, getServerSubType }
}

const getConfigData = async (url, data) => {
    let detailArray = [];

    await $.ajax({
        type: "GET",
        url: url,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (Array.isArray(result.data) && result.data.length > 0) {
                    detailArray = result.data
                }
            } else {
                errorNotification(result)
            }
        }
    })
    return detailArray;
}

async function loadSpecificWorkflow(selectIcon, fieldObj, html) {
    let checkParallel = $('#actionType option:selected').text().toLowerCase().includes('waitforparallelaction')

    html += "<div class='mb-3 form-group'>";
    html += "<div class='form-label'>WorkFlow Action</div>";
    html += "<div class='input-group'>";
    html += "<span class='input-group-text'><i class=" + selectIcon + "></i></span>";

    let selectObj = {
        label: 'WorkFlow Action',
        type: 'workflowAction',
        name: '@@DependentWorkflowAction',
    }
    newActionObj.actionInfo.formInput.push(selectObj)

    html += `<select  class='my-i select_actions forSelectLoad' data-live-search='${true}' name='@@DependentWorkflowAction' ${checkParallel && 'multiple'} data-placeholder="Select WorkFlow Action" onchange='handleFormDataChange(event, "@@workflow_action-error")'>`;

    //html += "<option value='default'>Select WorkFlow Action</option>";

    html += "</select>"

    html += "</div>";
    html += `<span data-id="@@DependentWorkflowAction-error" data-error="Select WorkFlow Action"></span>`;
    html += "</div>";

    return html
}

$("#btnPrevious").on("click", function () {
    $(".sectionData").empty();
    newActionObj.actionInfo.formInput = []
    form.steps('previous')
});

$(".btnCancel").on("click", function () {
    $(".Common-input").each(function () {
        ($(this).attr("type") === 'checkbox') ? $(this).prop('checked', false) : $(this).val('');
    });

    $createModal.modal('hide');
});

const loadActionsCount = (newData, mode = '') => {
    let actionCount = $('.workflowActions').length;
    let serverKeys = Object.keys(serverListCount);
    let databaseKeys = Object.keys(databaseListCount);

    if (newData) {
        let propertiesValues = newData.actionInfo?.properties
        let updateProperties = updateActionObject.actionInfo?.properties

        if (newData.actionInfo.formInput) {
            newData.actionInfo.formInput.forEach((form) => {
                if (form.type == 'server' || form.type == 'database') {
                    const keys = form.type == 'server' ? serverKeys : databaseKeys;

                    if (!keys.includes(propertiesValues[form.name])) {
                        keys.push(propertiesValues[form.name])
                        if (form.type == 'server') {
                            serverListCount[propertiesValues[form.name]] = 1;
                        } else {
                            databaseListCount[propertiesValues[form.name]] = 1;
                        }

                        if (mode == 'edit') {
                            if (form.type == 'server') {
                                reduceServerCount(serverKeys, updateProperties, form)

                            } else {
                                reduceDatabaseCount(databaseKeys, updateProperties, form)
                            }
                        }

                    } else {
                        if (mode == 'delete') {
                            if (form.type == 'server') {
                                reduceServerCount(serverKeys, propertiesValues, form)
                            } else {
                                reduceDatabaseCount(databaseKeys, propertiesValues, form)
                            }
                        } else {

                            if (mode == 'edit') {
                                if (form.type == 'server' && updateProperties[form.name] !== propertiesValues[form.name] && keys.includes(propertiesValues[form.name])) {
                                    reduceServerCount(serverKeys, updateProperties, form)
                                    serverListCount[propertiesValues[form.name]]++;
                                } else {
                                    if (updateProperties[form.name] !== propertiesValues[form.name] && keys.includes(propertiesValues[form.name])) {
                                        reduceDatabaseCount(databaseKeys, updateProperties, form)
                                        databaseListCount[propertiesValues[form.name]]++;
                                    }
                                }
                            } else {
                                if (form.type == 'server') {
                                    serverListCount[propertiesValues[form.name]]++;
                                } else {
                                    databaseListCount[propertiesValues[form.name]]++;
                                }
                            }
                        }
                    }
                }
            });
        }
    }

    $("#actionsCount").text(actionCount);
    $("#serverCount").text(serverKeys.length);
    $("#databaseCount").text(databaseKeys.length);
}

const reduceServerCount = (serverKeys, properties, form) => {

    serverListCount[properties[form.name]]--;
    if (serverListCount[properties[form.name]] === 0) {
        delete serverListCount[properties[form.name]];
        serverKeys.splice(serverKeys.indexOf(properties[form.name]), 1);
    }

}

const reduceDatabaseCount = (databaseKeys, properties, form) => {

    databaseListCount[properties[form.name]]--;
    if (databaseListCount[properties[form.name]] === 0) {
        delete databaseListCount[properties[form.name]];
        databaseKeys.splice(databaseKeys.indexOf(properties[form.name]), 1);
    }

}



