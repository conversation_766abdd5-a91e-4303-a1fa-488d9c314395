using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetPaginatedList;

public class
    GetDataSyncOptionsPaginatedListQueryHandler : IRequestHandler<GetDataSyncOptionsPaginatedListQuery,
        PaginatedResult<DataSyncOptionsListVm>>
{
    private readonly IDataSyncOptionsRepository _dataSyncOptionsRepository;
    private readonly IMapper _mapper;

    public GetDataSyncOptionsPaginatedListQueryHandler(IMapper mapper, IDataSyncOptionsRepository dataSyncOptionsRepository)
    {
        _mapper = mapper;
        _dataSyncOptionsRepository = dataSyncOptionsRepository;
    }

    public async Task<PaginatedResult<DataSyncOptionsListVm>> Handle(GetDataSyncOptionsPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DataSyncOptionsFilterSpecification(request.SearchString);

        var queryable =await _dataSyncOptionsRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var dataSyncList = _mapper.Map<PaginatedResult<DataSyncOptionsListVm>>(queryable);

        return dataSyncList;

        //var queryable = _dataSyncRepository.GetPaginatedQuery();

        //var productFilterSpec = new DataSyncFilterSpecification(request.SearchString);

        //var dataSyncList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<DataSyncListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return dataSyncList;
    }
}