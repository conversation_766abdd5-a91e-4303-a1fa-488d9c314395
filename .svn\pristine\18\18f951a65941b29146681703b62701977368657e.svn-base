﻿using ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerWorkflowDetailModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObjectSchedulerWorkflowDetail.Queries;

public class GetInfraObjectSchedulerWorkflowDetailPaginatedListQueryHandlerTests : IClassFixture<InfraObjectSchedulerWorkflowDetailFixture>
{
    private readonly GetInfraObjectSchedulerWorkflowDetailPaginatedListQueryHandler _handler;
    private readonly Mock<IInfraObjectSchedulerWorkflowDetailRepository> _mockInfraObjectSchedulerWorkflowDetailRepository;
    private readonly InfraObjectSchedulerWorkflowDetailFixture _infraObjectSchedulerWorkflowDetailFixture;

    public GetInfraObjectSchedulerWorkflowDetailPaginatedListQueryHandlerTests(InfraObjectSchedulerWorkflowDetailFixture infraObjectSchedulerWorkflowDetailFixture)
    {
        _infraObjectSchedulerWorkflowDetailFixture = infraObjectSchedulerWorkflowDetailFixture;

        _mockInfraObjectSchedulerWorkflowDetailRepository = InfraObjectSchedulerWorkflowDetailRepositoryMocks.GetPaginatedInfraObjectSchedulerWorkflowDetailRepository(_infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails);

        _handler = new GetInfraObjectSchedulerWorkflowDetailPaginatedListQueryHandler(_infraObjectSchedulerWorkflowDetailFixture.Mapper, _mockInfraObjectSchedulerWorkflowDetailRepository.Object);

        _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[0].InfraObjectName = "Infra_Test";
        _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[0].WorkflowName = "WF_Test";
        _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[0].CurrentActionName = "Current_Base";
        _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[0].Status = "Pending";


        _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[1].InfraObjectName = "Object_Reference";
        _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[1].WorkflowName = "Workflow_Name";
        _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[1].CurrentActionName = "Action_Build";
        _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[1].Status = "Update";
           
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetInfraObjectSchedulerWorkflowDetailPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraObjectSchedulerWorkflowDetailListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedInfraObjectSchedulerWorkflowDetails_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetInfraObjectSchedulerWorkflowDetailPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Update" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraObjectSchedulerWorkflowDetailListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<InfraObjectSchedulerWorkflowDetailListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].InfraObjectName.ShouldBe("Object_Reference");

        result.Data[0].WorkflowName.ShouldBe("Workflow_Name");

        result.Data[0].CurrentActionName.ShouldBe("Action_Build");

        result.Data[0].Status.ShouldBe("Update");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetInfraObjectSchedulerWorkflowDetailPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraObjectSchedulerWorkflowDetailListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_InfraObjectSchedulerWorkflowDetails_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetInfraObjectSchedulerWorkflowDetailPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "infraobjectname=Infra_Test;workflowname=WF_Test;currentactionname=Current_Base;Status=Pending;" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraObjectSchedulerWorkflowDetailListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].InfraObjectName.ShouldBe("Infra_Test");

        result.Data[0].WorkflowName.ShouldBe("WF_Test");

        result.Data[0].CurrentActionName.ShouldBe("Current_Base");

        result.Data[0].Status.ShouldBe("Pending");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetInfraObjectSchedulerWorkflowDetailPaginatedListQuery(), CancellationToken.None);

        _mockInfraObjectSchedulerWorkflowDetailRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}