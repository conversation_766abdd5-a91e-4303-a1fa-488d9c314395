﻿using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionFieldMasterModel;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionFieldMaster.Queries
{
    public class GetWorkflowActionFieldMasterPaginatedListQueryHandlerTests
    {
        private readonly Mock<IWorkflowActionFieldMasterRepository> _mockWorkflowActionFieldMasterRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetWorkflowActionFieldMasterPaginatedListQueryHandler _handler;

        public GetWorkflowActionFieldMasterPaginatedListQueryHandlerTests()
        {
            _mockWorkflowActionFieldMasterRepository = new Mock<IWorkflowActionFieldMasterRepository>();
            _mockMapper = new Mock<IMapper>();

            _handler = new GetWorkflowActionFieldMasterPaginatedListQueryHandler(
                _mockMapper.Object,
                _mockWorkflowActionFieldMasterRepository.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedResult_WhenValidQueryProvided()
        {
            var request = new GetWorkflowActionFieldMasterPaginatedListQuery
            {
                SearchString = "test",
                PageNumber = 1,
                PageSize = 10
            };

            var mockQueryable = new List<Domain.Entities.WorkflowActionFieldMaster>
            {
                new Domain.Entities.WorkflowActionFieldMaster { Id = 1, Name = "Test 1" },
                new Domain.Entities.WorkflowActionFieldMaster { Id = 2, Name = "Test 2" }
            }.AsQueryable().BuildMock();

            _mockWorkflowActionFieldMasterRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(mockQueryable);

            var mappedList = new List<WorkflowActionFieldMasterListVm>
            {
                new WorkflowActionFieldMasterListVm { Id = Guid.NewGuid().ToString(), Name = "Test 1" },
                new WorkflowActionFieldMasterListVm { Id = Guid.NewGuid().ToString(), Name = "Test 2" }
            };

            _mockMapper
                .Setup(mapper => mapper.Map<WorkflowActionFieldMasterListVm>(It.IsAny<Domain.Entities.WorkflowActionFieldMaster>()))
                .Returns((Domain.Entities.WorkflowActionFieldMaster src) => new WorkflowActionFieldMasterListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = src.Name
                });

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal("Test 1", result.Data[0].Name);

            _mockWorkflowActionFieldMasterRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyPaginatedResult_WhenNoResultsMatch()
        {
            var request = new GetWorkflowActionFieldMasterPaginatedListQuery
            {
                SearchString = "nonexistent",
                PageNumber = 1,
                PageSize = 10
            };

            var emptyQueryable = new List<Domain.Entities.WorkflowActionFieldMaster>().AsQueryable().BuildMock();

            _mockWorkflowActionFieldMasterRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(emptyQueryable);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);

            _mockWorkflowActionFieldMasterRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldApplyPaginationCorrectly()
        {
            var request = new GetWorkflowActionFieldMasterPaginatedListQuery
            {
                SearchString = "",
                PageNumber = 2,
                PageSize = 1
            };

            var mockQueryable = new List<Domain.Entities.WorkflowActionFieldMaster>
            {
                new Domain.Entities.WorkflowActionFieldMaster { Id = 1, Name = "Test 1" },
                new Domain.Entities.WorkflowActionFieldMaster { Id = 2, Name = "Test 2" }
            }.AsQueryable().BuildMock();

            _mockWorkflowActionFieldMasterRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(mockQueryable);

            var mappedList = new List<WorkflowActionFieldMasterListVm>
            {
                new WorkflowActionFieldMasterListVm { Id = Guid.NewGuid().ToString(), Name = "Test 2" }
            };

            _mockMapper
                .Setup(mapper => mapper.Map<WorkflowActionFieldMasterListVm>(It.IsAny<Domain.Entities.WorkflowActionFieldMaster>()))
                .Returns((Domain.Entities.WorkflowActionFieldMaster src) => new WorkflowActionFieldMasterListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = src.Name
                });

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result.Data);
            Assert.Equal("Test 2", result.Data[0].Name);

            _mockWorkflowActionFieldMasterRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }
    }
}
