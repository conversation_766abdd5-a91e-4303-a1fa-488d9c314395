﻿namespace ContinuityPatrol.Shared.Infrastructure.Middlewares;

public class ApiKeyMiddleware
{
    private const string APIKEY = "x-api-key";
    private readonly RequestDelegate _next;

    public ApiKeyMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (context.Request.Headers.TryGetValue(APIKEY, out var extractedApiKey))
        {
            var appSettings = context.RequestServices.GetRequiredService<IConfiguration>();
            var apiKey = appSettings.GetValue<string>(APIKEY);
            if (!apiKey.Equals(extractedApiKey))
            {
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Unauthorized client");
                return;
            }

            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, "CP_WINDOWSERVICE"),
                new Claim(ClaimTypes.Name, "CP_WINDOWSERVICE"),
                new Claim("permissions", "[\"Permission.WindowsService.All\"]"),
                new Claim("isParent", "true"),
                new Claim("isAllInfra", "true")
            };

            var appIdentity = new ClaimsIdentity(claims, "Basic");
            var principal = new ClaimsPrincipal(appIdentity);
            context.User = principal;
        }

        await _next(context);
    }
}