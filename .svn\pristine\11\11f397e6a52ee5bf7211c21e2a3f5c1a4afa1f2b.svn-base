﻿using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetReplicationMasterByInfraMasterName;

namespace ContinuityPatrol.Application.UnitTests.Features.ReplicationMaster.Queries
{
    public class GetByInfraMasterNameQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IReplicationMasterRepository> _mockReplicationMasterRepository;
        private readonly GetByInfraMasterNameQueryHandler _handler;

        public GetByInfraMasterNameQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockReplicationMasterRepository = new Mock<IReplicationMasterRepository>();

            _handler = new GetByInfraMasterNameQueryHandler(
                _mockMapper.Object,
                _mockReplicationMasterRepository.Object
            );
        }

        [Fact]
        public async Task Handle_ReturnsMappedReplicationMasterList_WhenDataExists()
        {
            var query = new GetByInfraMasterNameQuery { InfraMasterName = "Master1" };

            var replicationMasterList = new List<Domain.Entities.ReplicationMaster>
            {
                new Domain.Entities.ReplicationMaster { Id = 1, InfraMasterName = "Master1" },
                new Domain.Entities.ReplicationMaster { Id = 2, InfraMasterName = "Master1" }
            };

            var mappedList = new List<GetByInfraMasterNameVm>
            {
                new GetByInfraMasterNameVm { Id = Guid.NewGuid().ToString(), InfraMasterName = "Master1" },
                new GetByInfraMasterNameVm { Id = Guid.NewGuid().ToString(), InfraMasterName = "Master1" }
            };

            _mockReplicationMasterRepository
                .Setup(repo => repo.GetReplicationMasterByInfraMasterName(query.InfraMasterName))
                .ReturnsAsync(replicationMasterList);

            _mockMapper
                .Setup(mapper => mapper.Map<List<GetByInfraMasterNameVm>>(replicationMasterList))
                .Returns(mappedList);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.NotEmpty(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("Master1", result[0].InfraMasterName);
        }

        [Fact]
        public async Task Handle_ReturnsEmptyList_WhenNoDataExists()
        {
            var query = new GetByInfraMasterNameQuery { InfraMasterName = "NonExistentMaster" };

            var emptyReplicationMasterList = new List<Domain.Entities.ReplicationMaster>();

            _mockReplicationMasterRepository
                .Setup(repo => repo.GetReplicationMasterByInfraMasterName(query.InfraMasterName))
                .ReturnsAsync(emptyReplicationMasterList);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);

            _mockMapper.Verify(mapper => mapper.Map<List<GetByInfraMasterNameVm>>(It.IsAny<List<Domain.Entities.ReplicationMaster>>()), Times.Never);
        }
    }
}
