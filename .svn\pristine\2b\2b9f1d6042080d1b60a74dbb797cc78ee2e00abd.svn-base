namespace ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetNameUnique;

public class
    GetDriftManagementMonitorStatusNameUniqueQueryHandler : IRequestHandler<
        GetDriftManagementMonitorStatusNameUniqueQuery, bool>
{
    private readonly IDriftManagementMonitorStatusRepository _driftManagementMonitorStatusRepository;

    public GetDriftManagementMonitorStatusNameUniqueQueryHandler(
        IDriftManagementMonitorStatusRepository driftManagementMonitorStatusRepository)
    {
        _driftManagementMonitorStatusRepository = driftManagementMonitorStatusRepository;
    }

    public async Task<bool> Handle(GetDriftManagementMonitorStatusNameUniqueQuery request,
        CancellationToken cancellationToken)
    {
        return await _driftManagementMonitorStatusRepository.IsNameExist(request.Name, request.Id);
    }
}