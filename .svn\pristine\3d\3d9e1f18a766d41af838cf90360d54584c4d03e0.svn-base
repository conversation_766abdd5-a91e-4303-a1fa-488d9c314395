using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftCategoryList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftOperationSummary;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftResourceSummary;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetConflictList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetDriftTreeView;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetResourceStatus;
using ContinuityPatrol.Domain.ViewModels.DriftManagementMonitorStatusModel;
using ContinuityPatrol.Domain.ViewModels.DriftResourceSummaryModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Drift;

public class DriftManagementMonitorStatusService : BaseService, IDriftManagementMonitorStatusService
{
    public DriftManagementMonitorStatusService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<DriftManagementMonitorStatusListVm>> GetDriftManagementMonitorStatusList()
    {
        Logger.LogDebug("Get All DriftManagementMonitorStatuss");

        return await Mediator.Send(new GetDriftManagementMonitorStatusListQuery());
    }

    public async Task<DriftManagementMonitorStatusDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftManagementMonitorStatus Id");

        Logger.LogDebug($"Get DriftManagementMonitorStatus Detail by Id '{id}'");

        return await Mediator.Send(new GetDriftManagementMonitorStatusDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateDriftManagementMonitorStatusCommand createDriftManagementMonitorStatusCommand)
    {
        Logger.LogDebug($"Create DriftManagementMonitorStatus '{createDriftManagementMonitorStatusCommand}'");

        return await Mediator.Send(createDriftManagementMonitorStatusCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDriftManagementMonitorStatusCommand updateDriftManagementMonitorStatusCommand)
    {
        Logger.LogDebug($"Update DriftManagementMonitorStatus '{updateDriftManagementMonitorStatusCommand}'");

        return await Mediator.Send(updateDriftManagementMonitorStatusCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftManagementMonitorStatus Id");

        Logger.LogDebug($"Delete DriftManagementMonitorStatus Details by Id '{id}'");

        return await Mediator.Send(new DeleteDriftManagementMonitorStatusCommand { Id = id });
    }
    #region NameExist
    public async Task<bool> IsDriftManagementMonitorStatusNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "DriftManagementMonitorStatus Name");

        Logger.LogDebug($"Check Name Exists Detail by DriftManagementMonitorStatus Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetDriftManagementMonitorStatusNameUniqueQuery { Name = name, Id = id });
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<DriftManagementMonitorStatusListVm>> GetPaginatedDriftManagementMonitorStatuss(GetDriftManagementMonitorStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in DriftManagementMonitorStatus Paginated List");

        return await Mediator.Send(query);
    }
    #endregion

    public async Task<List<DriftManagementMonitorStatusListVm>> GetDriftManagementStatusByInfraObjectId(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObject Id");

        Logger.LogDebug($"Get DriftManagementMonitorStatus Detail by infraObjectId '{infraObjectId}'");

        return await Mediator.Send(new GetByInfraObjectIdQuery { InfraObjectId = infraObjectId });
    }

    public async Task<List<GetDriftTreeListVm>> GetDriftTreeList()
    {
        Logger.LogDebug("Get DriftManagementMonitorStatus tree List");

        return await Mediator.Send(new GetDriftTreeListQuery());
    }

    public async Task<List<DriftResourceSummaryListVm>> GetDriftResourceSummary()
    {

        Logger.LogDebug("Get DriftManagementMonitorStatus resource summary List");

        return await Mediator.Send(new GetDriftResourceSummaryQuery());
    }

    public async Task<DriftOperationSummaryVm> GetDriftOperationSummary()
    {

        Logger.LogDebug("Get DriftManagementMonitorStatus operation summary List");

        return await Mediator.Send(new DriftOperationSummaryQuery());
    }
    public async Task<DriftCategoryListVm> GetDriftCategory()
    {
        Logger.LogDebug("GetDriftCategory List");

        return await Mediator.Send(new DriftCategoryListQuery());
    }

    public async Task<DriftDashboardResourceStatusVm> GetDriftDashboardResourceStatus()
    {
        Logger.LogDebug("Getting Drift Dashboard Resource Status.");

        return await Mediator.Send(new DriftDashboardResourceStatusQuery());
    }

    public async Task<List<ConflictListVm>> GetConflictOverView()
    {
        Logger.LogDebug("Get Conflict OverView ");

        return await Mediator.Send(new GetConflictListQuery());
    }
}
