﻿namespace ContinuityPatrol.Application.Constants;

public sealed class ErrorMessage
{
    public static class Company
    {
        public const string CompanyIdCannotBeZero = "Company Id cannot be Zero or Negative value.";

        public const string CompanyIdCannotBeInvalid = "Company Id cannot be Invalid.";

        public const string ParentCompanyIdCannotBeZero = "Parent Company Id cannot be Zero or Negative value for child company.";

        public const string ParentCompanyIdCannotBeEmpty = "Parent Company Id cannot be Invalid for child company.";
    }

    public static class BusinessFunction
    {
        public const string BusinessFunctionIdCannotBeZero = "BusinessFunction Id cannot be Zero or Negative value.";
    }

    public static class Server
    {
        public const string ServerIdCannotBeZero = "Server Id cannot be Zero or Negative value.";
    }

    public static class Replication
    {
        public const string ReplicationIdCannotBeZero = "Replication Id cannot be Zero or Negative value.";
    }

    public static class Database
    {
        public const string DatabaseIdCannotBeZero = "Database Id cannot be Zero or Negative value.";
    }

    public static class BusinessService
    {
        public const string BusinessServiceIdCannotBeZero = "BusinessService Id cannot be Zero or Negative value.";
    }

    public static class CredentialProfile
    {
        public const string CredentialProfileIdCannotBeZero = "CredentialProfile Id cannot be Zero or Negative value.";
    }

    public static class InfraObject
    {
        public const string InfraObjectIdCannotBeZero = "InfraObject Id cannot be Zero or Negative value.";
    }

    public static class Node
    {
        public const string NodeIdCannotBeZero = "Node Id cannot be Zero or Negative value.";
    }

    public static class SingleSignOn
    {
        public const string SingleSignOnIdCannotBeZero = "SingleSignOn Id cannot be Zero or Negative value.";
    }

    public static class Site
    {
        public const string SiteIdCannotBeZero = "Site Id cannot be Zero or Negative value.";
    }

    public static class Setting
    {
        public const string SettingIdCannotBeZero = "Setting Id Cannot be Zero or Negative value.";
    }

    public static class User
    {
        public const string UserIdCannotBeZero = "User Id cannot be Zero or Negative value.";
    }

    public static class AccessManager
    {
        public const string AccessManagerIdCannotBeZero = "AccessManager Id cannot be Zero or Negative value.";
    }

    public static class SiteType
    {
        public const string SiteTypeIdCannotBeZero = "SiteType Id cannot be Zero or Negative value.";
    }

    public static class UserRole
    {
        public const string UserRoleIdCannotBeZero = "UserRole Id cannot be Zero or Negative value.";
    }

    public static class Workflow
    {
        public const string WorkflowIdCannotBeZero = "Workflow Id cannot be Zero or Negative value.";
    }

    public static class WorkflowProfile
    {
        public const string WorkflowProfileIdCannotBeZero = "WorkflowProfile Id cannot be Zero or Negative value.";

        public const string ProfileAlreadyExists = "This workflow already attached with profile :'{0}'";
    }

    public static class WorkflowHistory
    {
        public const string WorkflowHistoryIdCannotBeZero = "WorkflowHistory Id cannot be Zero or Negative value.";
    }

    public static class WorkflowInfraObject
    {
        public const string DeAttachedInfraObject = "Already InfraObject is De-Attached.";
    }

    public static class InfraObjectSchedulerWorkflowDetail
    {
        public const string InfraObjectSchedulerWorkflowDetailIdCannotBeZero =
            "InfraObjectSchedulerWorkflowDetail Id cannot be Zero or Negative value.";
    }

    public static class Form
    {
        public const string FormIdCannotBeZero = "Form Id Cannot Be Zero or Negative Value.";
    }

    public static class LicenseManager
    {
        public const string LicenseManagerIdCannotBeZero = "LicenseManager Id Cannot Be Zero or Negative Value.";
    }

    public static class WorkflowCategory
    {
        public const string WorkflowCategoryIdCannotBeZero = "WorkflowCategory Id Cannot Be Zero or Negative Value.";
    }

    public static class WorkflowAction
    {
        public const string WorkflowActionIdCannotBeZero = "WorkflowAction Id Cannot Be Zero or Negative Value.";
    }
}