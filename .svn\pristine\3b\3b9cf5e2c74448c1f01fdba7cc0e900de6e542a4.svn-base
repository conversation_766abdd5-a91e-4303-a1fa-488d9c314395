﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DashboardView.Event.ITResiliencyView;

public class ITResiliencyEventHandler : INotificationHandler<ITResiliencyEvent>
{
    private readonly ILogger<ITResiliencyEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ITResiliencyEventHandler(ILogger<ITResiliencyEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(ITResiliencyEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.View} ITResiliency",
            Entity = "ITResiliency",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "IT Resiliency viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("IT Resiliency viewed");
    }
}