﻿using ContinuityPatrol.Application.Features.CyberAirGap.Events.IsAttached;

namespace ContinuityPatrol.Application.Features.CyberAirGap.Commands.IsAttached;

public class AirGapAttachedCommandHandler : IRequestHandler<AirGapAttachedCommand, AirGapAttachedResponse>
{
    private readonly ICyberAirGapRepository _airGapRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public AirGapAttachedCommandHandler(ICyberAirGapRepository airGapRepository, IMapper mapper, IPublisher publisher)
    {
        _airGapRepository = airGapRepository;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<AirGapAttachedResponse> Handle(AirGapAttachedCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _airGapRepository.GetByReferenceIdAsync(request.Id)
                            ?? throw new NotFoundException(nameof(Domain.Entities.CyberAirGap), request.Id);

        eventToUpdate.IsAttached = request.IsAttached;

        _mapper.Map(request, eventToUpdate, typeof(AirGapAttachedCommand), typeof(Domain.Entities.CyberAirGap));

        await _airGapRepository.UpdateAsync(eventToUpdate);

        var response = new AirGapAttachedResponse
        {
            Message =
                $"AirGap '{(eventToUpdate.IsAttached ? "Attached" : "Detached")}' successfully {eventToUpdate.Name}",

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(
            new AirGapAttachedEvent { Name = eventToUpdate.Name, IsAttached = eventToUpdate.IsAttached },
            cancellationToken);

        return response;
    }
}