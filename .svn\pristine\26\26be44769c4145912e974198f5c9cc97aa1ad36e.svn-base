using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Delete;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Update;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetList;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class DataSyncOptionsService : BaseService, IDataSyncOptionsService
{
    public DataSyncOptionsService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<DataSyncOptionsListVm>> GetDataSyncList()
    {
        Logger.LogDebug("Get All DataSyncs");

        return await Mediator.Send(new GetDataSyncOptionsListQuery());
    }

    public async Task<DataSyncOptionsDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DataSync Id");

        Logger.LogDebug($"Get DataSync Detail by Id '{id}'");

        return await Mediator.Send(new GetDataSyncOptionsDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateDataSyncOptionsCommand createDataSyncCommand)
    {
        Logger.LogDebug($"Create DataSync '{createDataSyncCommand}'");

        return await Mediator.Send(createDataSyncCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDataSyncOptionsCommand updateDataSyncCommand)
    {
        Logger.LogDebug($"Update DataSync '{updateDataSyncCommand}'");

        return await Mediator.Send(updateDataSyncCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DataSync Id");

        Logger.LogDebug($"Delete DataSync Details by Id '{id}'");

        return await Mediator.Send(new DeleteDataSyncOptionsCommand { Id = id });
    }

    #region NameExist

    public async Task<bool> IsDataSyncNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "DataSync Name");

        Logger.LogDebug($"Check Name Exists Detail by DataSync Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetDataSyncOptionsNameUniqueQuery { Name = name, Id = id });
    }

    #endregion

    #region Paginated

    public async Task<PaginatedResult<DataSyncOptionsListVm>> GetPaginatedDataSyncs(GetDataSyncOptionsPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in DataSync  Paginated List");

        return await Mediator.Send(query);
    }

    #endregion
}