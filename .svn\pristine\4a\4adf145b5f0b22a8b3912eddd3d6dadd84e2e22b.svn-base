﻿using ContinuityPatrol.Domain.ViewModels.EscalationMatrix;

namespace ContinuityPatrol.Application.Features.EscalationMatrix.Queries.GetDetail;

public class
    GetEscalationMatrixDetailQueryHandler : IRequestHandler<GetEscalationMatrixDetailQuery, EscalationMatrixListVm>
{
    private readonly IEscalationMatrixRepository _escalationMatrixRepository;
    private readonly IMapper _mapper;

    public GetEscalationMatrixDetailQueryHandler(IMapper mapper, IEscalationMatrixRepository escalationMatrixRepository)
    {
        _mapper = mapper;
        _escalationMatrixRepository = escalationMatrixRepository;
    }

    public async Task<EscalationMatrixListVm> Handle(GetEscalationMatrixDetailQuery request,
        CancellationToken cancellationToken)
    {
        var escalationMatrixDto = await _escalationMatrixRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(escalationMatrixDto, nameof(Domain.Entities.EscalationMatrix),
            new NotFoundException(nameof(Domain.Entities.EscalationMatrix), request.Id));

        return _mapper.Map<EscalationMatrixListVm>(escalationMatrixDto);
    }
}