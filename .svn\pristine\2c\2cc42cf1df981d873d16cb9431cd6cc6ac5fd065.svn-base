﻿using ContinuityPatrol.Application.Features.Job.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Job.Queries;


public class GetJobDetailQueryHandlerTests : IClassFixture<JobFixture>
{
    private readonly JobFixture _jobFixture;

    private readonly Mock<IJobRepository> _mockJobRepository;

    private readonly GetJobDetailQueryHandler _handler;

    public GetJobDetailQueryHandlerTests(JobFixture jobFixture)
    {
        _jobFixture = jobFixture;

        _mockJobRepository = JobRepositoryMocks.GetJobRepository(_jobFixture.Jobs);

        _handler = new GetJobDetailQueryHandler(_jobFixture.Mapper, _mockJobRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_JobDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetJobDetailQuery { Id = _jobFixture.Jobs[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<JobDetailVm>();
        result.Id.ShouldBe(_jobFixture.Jobs[0].ReferenceId);
        result.Name.ShouldBe(_jobFixture.Jobs[0].Name);
        result.InfraObjectProperties.ShouldBe(_jobFixture.Jobs[0].InfraObjectProperties);
        result.TemplateId.ShouldBe(_jobFixture.Jobs[0].TemplateId);
        result.TemplateName.ShouldBe(_jobFixture.Jobs[0].TemplateName);
        result.NodeId.ShouldBe(_jobFixture.Jobs[0].NodeId);
        result.NodeName.ShouldBe(_jobFixture.Jobs[0].NodeName);
        result.Status.ShouldBe(_jobFixture.Jobs[0].Status);
        result.CronExpression.ShouldBe(_jobFixture.Jobs[0].CronExpression);
        result.State.ShouldBe(_jobFixture.Jobs[0].State);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidSiteId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetJobDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetJobDetailQuery { Id = _jobFixture.Jobs[0].ReferenceId }, CancellationToken.None);

        _mockJobRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}