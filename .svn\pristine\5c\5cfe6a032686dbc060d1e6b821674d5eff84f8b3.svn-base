﻿namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.
    GetWorkflowOperationGroupByInfraObjectId;

public class WorkflowOperationGroupByInfraObjectIdVm
{
    public string Id { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string NodeId { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string CurrentActionId { get; set; }
    public string CurrentActionName { get; set; }
    public string Status { get; set; }
    public string Message { get; set; }
    public string WorkflowOperationId { get; set; }
    public int ConditionalOperation { get; set; }
    public string ProgressStatus { get; set; }
    public bool IsCustom { get; set; }
    public int JobName { get; set; }
    public int IsResume { get; set; }
    public int IsReExecute { get; set; }
    public int IsPause { get; set; }
    public int IsAbort { get; set; }
    public int WaitToNext { get; set; }
    public string WorkflowVersion { get; set; }
    public string ActionMode { get; set; }
    public string WorkflowExecutionTempId { get; set; }
}