﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Newtonsoft.Json
@model ContinuityPatrol.Domain.ViewModels.WorkflowModel.WorkflowViewModel

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}


<link href="~/css/WorkflowConfiguration.css" rel="stylesheet" />
<link href="~/css/wizard.css" rel="stylesheet" />
<link href="~/css/switch_slide_toggle.css" rel="stylesheet" />
<link href="~/css/AISuggestionsBox.css" rel="stylesheet" />
<style>
    .exclamationContainer {
        position: relative;
    }
    .exclamation-icon::after {
        content: "\e945";
        font-family: 'cp-icon' !important;
        color: #ff9632;
        position: absolute;
        right: -7px;
        top: -11px;
        font-size: var(--bs-input-icon-font-size);
        width: 14px;
        height: 14px;
        border-radius: 1rem;
        line-height: 1.7;
    }

    .nav-underline .nav-link.active, .nav-underline .show > .nav-link {
        color: #0d6efd;
        border-bottom-color: currentcolor;
    }

    .nav-link {
        color: #000000;
    }
</style>
@Html.AntiForgeryToken()

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-body p-0">
            <div class="row g-2">
                <div class="col-xl-2 col-md-2 col-xxl-2">
                    <div class="card-header px-0 ps-1">
                        <h6 class="page_title"><i class="cp-workflow-configuration"></i><span>Workflow&nbsp;Configuration</span></h6>
                    </div>
                    <div class="px-2">
                        <div class="input-group">
                            <input type="search" id="search_category" class="form-control px-1" placeholder="Search Action Type" />
                            <span role="button" class="input-group-text"><i class="cp-search fs-7 bg-body-tertiary p-1" id="btn_search"></i></span>
                            <span class="input-group-text px-0">
                                <i class="cp-circle-rightarrow text-primary ms-2" id="expandAction" role="button" title="Collapse & Expand"></i>
                            </span>
                        </div>
                    </div>
                    <div class="card-body p-0 Workflow-Tree">
                        <ul class="nav nav-underline nav-justified px-2 position-sticky top-0 bg-white" id="pills-tab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <a class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">Action Type</a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">Template</a>
                            </li>
                        </ul>
                        <div class="tab-content" id="pills-tabContent">
                            <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab" tabindex="0">
                                @foreach (var workflow in Model.workflowCategoryViewListVms)
                                {
                                    <details>
                                        @{
                                            var title = (string)workflow?.CategoryName ?? string.Empty;
                                            var id = (string)workflow?.CategoryId ?? string.Empty;
                                            var properties = workflow?.WorkflowCategoryBaseChildViewListVms;
                                        }
                                        <summary parentId="" id="@id" class="filterTitle categorysummary text-truncate" title="@title">
                                            <i class="@workflow?.Icon"></i>  @title.Replace(" ", "")
                                        </summary>
                                        @{
                                            var sortedChildren = (properties as IEnumerable<dynamic>)?.Cast<dynamic>()?.OrderBy(p => (string)p?.Name)?.ToList();
                                        }
                                        @if (sortedChildren != null)
                                        {
                                            @foreach (var propertiesData in sortedChildren)
                                            {
                                                <details>

                                                    @{
                                                        var childTitle = (string)propertiesData?.Name ?? string.Empty;
                                                        var childId = (string)propertiesData?.Id ?? string.Empty;
                                                        var childProperties = propertiesData?.WorkflowCategoryChildViewListVms;
                                                    }

                                                    <summary id="@childId" class="categorysummary secondChild text-truncate" title="@childTitle">
                                                        <i class="@propertiesData?.Icon"></i> @childTitle.Replace(" ", "")
                                                    </summary>

                                                    @{
                                                        var sortedChildProperties = (childProperties as IEnumerable<dynamic>)?.Cast<dynamic>()?.OrderBy(p => (string)p?.Name)?.ToList();
                                                    }

                                                    @if (sortedChildProperties != null)
                                                    {
                                                        @foreach (var childPropertiesData in sortedChildProperties)
                                                        {
                                                            <details>

                                                                @{
                                                                    var secondChildTitle = (string)childPropertiesData?.Name ?? string.Empty;
                                                                    var secondChildId = (string)childPropertiesData?.Id ?? string.Empty;
                                                                    var actionProperties = childPropertiesData?.ActionLists as IEnumerable<dynamic> ?? Enumerable.Empty<dynamic>();
                                                                }

                                                                <summary class="categorysummary thirdChild text-truncate" id="@secondChildId" title="@(secondChildTitle ?? string.Empty)">
                                                                    <i class="@childPropertiesData.Icon"></i> @(secondChildTitle ?? string.Empty)
                                                                </summary>

                                                                @{
                                                                    var sortedActionProperties = (actionProperties as IEnumerable<dynamic>)?.Cast<dynamic>()?.OrderBy(p => (string)p?.ActionName)?.ToList();
                                                                }
                                                                @if (sortedActionProperties != null && sortedActionProperties.Any())
                                                                {
                                                                    @foreach (var actionData in sortedActionProperties)
                                                                    {

                                                                        <div class="d-grid ms-3">

                                                                            @{
                                                                                var actionId = (string)actionData?.ActionId ?? string.Empty;
                                                                                var actionName = (string)actionData?.ActionName ?? string.Empty;
                                                                                var actionType = (string)actionData?.Type ?? string.Empty;
                                                                            }

                                                                            <span role="button" id="@actionId" class="actiontype categorysummary nodeSummaryData text-truncate" draggable="true" ondragstart="drag(event)" actionType="@(actionType ?? string.Empty)" title="@(actionName ?? string.Empty)"
                                                                                  nodeId="@secondChildId" parentId="@id" parentIcon="@workflow?.Icon" parentColor="@workflow?.Color">
                                                                                @(actionName ?? string.Empty)
                                                                            </span>
                                                                        </div>
                                                                    }
                                                                }

                                                            </details>
                                                        }
                                                    }

                                                </details>
                                            }
                                        }

                                    </details>
                                }

                                <div id="workflowTreeNoData"></div>
                            </div>
                            <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab" tabindex="0">

                            </div>
                        </div>






                       

                    </div>
                </div>
                <div class="col-md-10 col-lg-10 col-xl-10" align="center" id="dropbox" ondrop="drop(event)" ondragover="allowDrop(event)">
                    <div class="">
                        <div class="header WF_headerTag">
                            <form class="w-100">
                                <div class="d-flex">
                                    <div class="row workflow_toolbar justify-content-end g-0 flex-fill collapse show" id="collapseWidthExample">
                                        <div class="col-auto card rounded-0 shadow-none mb-0 flex-fill">
                                            <div class="p-1 d-flex gap-1 card-body text-start pb-0">
                                                <div class="list-group">
                                                    <button type="button" id="btnNewWorkflow" class="list-group-item list-group-item-action"><i class="cp-add-new me-1"></i>New</button>
                                                    <button type="button" id="btnLoadWorkFlow" class="list-group-item list-group-item-action"><i class="cp-open me-1"></i>Open</button>
                                                </div>
                                                <div class="list-group">
                                                    <button type="button" id="btnWorkFlowDelete" class="list-group-item list-group-item-action"><i class="cp-Delete me-1 text-secondary"></i>Delete</button>
                                                    <button type="button" id="btnWorkflowRename" class="list-group-item list-group-item-action"><i class="cp-rename me-1"></i>Rename</button>
                                                </div>
                                                <div class="list-group">
                                                    <button type="button" id="btnSaveModalOpen" class="list-group-item list-group-item-action">
                                                        <i class="cp-save me-1"></i>Save
                                                    </button>
                                                    <button type="button" id="btnSaveAsModal" class="list-group-item list-group-item-action"><i class="cp-save me-1"></i>Save As</button>
                                                </div>
                                            </div>
                                            <div class="text-secondary py-1 bg-light">File</div>
                                        </div>
                                        <div class="col-auto card rounded-0 shadow-none mb-0 flex-fill">
                                            <div class="p-1 d-flex gap-2 card-body text-start pb-0 border-start">
                                                <div class="list-group">
                                                    <button type="button" id="btnWorkflowLock" class="list-group-item list-group-item-action rounded-0"><i class="cp-lock me-1"></i>Lock</button>
                                                    <button type="button" id="btnWorkflowVerify" class="list-group-item list-group-item-action rounded-0"><i class="cp-workflow-verify me-1"></i>Verify</button>
                                                </div>
                                                <div class="list-group">
                                                    <button type="button" id="workflowAttach" class="list-group-item list-group-item-action rounded-0"><i class="cp-url me-1"></i>Attach</button>
                                                    <button type="button" id="btnWorkflowPublish" class="list-group-item list-group-item-action rounded-0"><i class="cp-publish me-1"></i>Publish</button>
                                                </div>
                                            </div>
                                            <div class="text-secondary py-1 bg-light">Manage</div>
                                        </div>
                                        <div class="col-auto card rounded-0 shadow-none mb-0 flex-fill">
                                            <div class="p-1 d-flex gap-2 card-body text-start pb-0 border-start">
                                                <div class="list-group">
                                                    <button id='btnMarkAction' type="button" class="list-group-item list-group-item-action rounded-0"><i class="cp-mark-action me-1"></i>Mark Action</button>
                                                    <button type="button" class="list-group-item list-group-item-action rounded-0" id="btnExtract"><i class="cp-zip me-1"></i>Extract</button>

                                                </div>
                                            </div>
                                            <div class="text-secondary py-1 bg-light">Extraction</div>
                                        </div>
                                        <div class="col-auto card rounded-0 shadow-none mb-0 flex-fill">
                                            <div class="p-1 d-flex gap-2 card-body text-start pb-0 border-start">
                                                <div class="list-group">
                                                    <button type="button" class="list-group-item list-group-item-action rounded-0" id="btnGenerateTemplate">
                                                        <i class="cp-add me-1"></i>Create Template
                                                    </button>
                                                    <button type="button" id="btnRestoreTemplate" class="list-group-item list-group-item-action rounded-0"><i class="cp-generate me-1"></i>Generate Workflow</button>

                                                </div>
                                            </div>
                                            <div class="text-secondary py-1 bg-light">Template</div>
                                        </div>
                                        <div class="col-auto card rounded-0 shadow-none mb-0 flex-fill">
                                            <div class="p-1 d-flex gap-2 card-body text-start pb-0 border-start">
                                                <div class="list-group">
                                                    <button type="button" class="list-group-item" id="btnImportWorkFlow"><i class="cp-import me-1"></i>Import</button>
                                                    <button type="button" class="list-group-item" id="btnExportWorkFlow"><i class="cp-export me-1"></i>Export</button>
                                                </div>
                                            </div>
                                            <div class="text-secondary py-1 bg-light">Workflow</div>
                                        </div>
                                        <div class="col-auto card rounded-0 shadow-none mb-0 flex-fill">
                                            <div class="p-2 d-flex gap-2 card-body text-start pb-0 border-start">
                                                <div class="list-group justify-content-center">
                                                    @* <button type="button" class="list-group-item list-group-item-action rounded-0" id="btnUploadRunbook"><i class="cp-upload-up me-1"></i>Upload</button> *@
                                                    <button type="button" id="btnVersionManage" class="list-group-item" data-bs-target="#CompareModal"><i class="cp-compare me-1"></i>Compare</button>
                                                    <button type="button" class="list-group-item list-group-item-action rounded-0" title="Report (Ctrl + D)" id="btnWorkFlowReport"><i class="cp-custom-reports me-1"></i>Download</button>


                                                </div>

                                            </div>
                                            <div class="text-secondary py-1 bg-light">Runbook</div>
                                        </div>
                                    </div>
                                    <i class="cp-down-arrow text-primary ms-auto p-1" type="button" title="Workflow Tools" data-bs-toggle="collapse" data-bs-target="#collapseWidthExample" aria-expanded="false" aria-controls="collapseWidthExample"></i>
                                </div>
                            </form>
                        </div>
                        <div class="card-body p-0" align="center" id="parentAction">
                            <div id="WFLoader" class="spinner-border text-primary position-absolute" style="width: 2rem; height: 2rem; top:45%;left:47%;" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div class="d-flex justify-content-between position-sticky top-0 bg-white px-2 shadow-sm mb-2" style="z-index:999;">
                                <div class="d-flex align-items-center text-truncate w-50">
                                    <div class="d-flex"><h5 class="page_title text-primary text-truncate d-inline-block"><span id="workflowTitle"></span></h5></div><i class="cp-round-star-fill checkSaveWorkflow mb-2" style="font-size: 8px;color: #5c5c5c;display:none;"></i>
                                    <small class="text-secondary mx-1 fs-10 mt-2" id="versionText" style="display:none;">v<span id="workflowVersion"></span></small>
                                    <i class="cp-lock fs-7 me-1 text-danger" id="workflowLockStatus" type="button" style="display:none"></i><i class="cp-workflow-verify fs-7 me-1 text-primary" id="workflowVerifyStatus" type="button" style="display:none"></i>
                                    <i class="cp-reload cp-animate fs-6 ms-1 text-primary fw-semibold d-none" id="workflowRunningStatus" title="Currently Executing"></i>
                                </div>
                                <div class="d-flex align-items-center me-2 w-50 justify-content-end gap-4">
                                    <div class="fs-8" style="max-width:100px">
                                        <p class="mb-0 text-truncate"><i class="cp-workflow-type me-1 align-middle" title="Operation Type"></i><span id="attachedInfraObjectType"></span></p>
                                    </div>

                                    <div class="fs-8 " style="max-width:140px">
                                        <p class="mb-0 text-truncate"><i class="cp-workflow-profile me-1 align-middle" title="Workflow Profile"></i><span id="attachedProfileName"></span></p>
                                    </div>

                                    <div class="fs-8" style="max-width:130px">
                                        <p class="mb-0 text-truncate"><i class="cp-infra-object me-1 align-middle" title="InfraObject Name"></i><span id="attachedInfraObjectName"></span></p>
                                    </div>
                                    <div class="d-none justify-content-around me-3" id="markAllContainer">
                                        <input type="checkbox" class="markAllCheck" />
                                        <label class="ms-2 markAllLabel" style="max-width: 100px;width: 100px;">Mark&nbsp;All&nbsp;Actions</label>
                                    </div>
                                    <i type="button" id="collapseStatus" class="me-2 fs-5"></i>
                                    <div class="input-group w-auto border-dark-subtle">
                                        <input type="text" id="searchWorkFlow" class="form-control" placeholder="Search" style="width:130px" />
                                        <div class="input-group-text gap-2 p-0" id="highlight_arrow">
                                            <i class="cp-up-arrow fs-7" id="up-arrow-icon" role="button" title="previous"></i>
                                            <i class="cp-down-arrow fs-7" id="down-arrow-icon" role="button" title="Next"></i>
                                            <span class="fs-8 me-1" id="highlightCount"></span>
                                        </div>
                                        <div class="input-group-text">
                                            <div class="dropdown" title="Filter" id="filter_property">
                                                <span data-bs-toggle="dropdown" aria-expanded="false"><i class="cp-filter"></i></span>
                                                <ul class="dropdown-menu filter-dropdown">
                                                    <li><h6 class="dropdown-header">Filter Search</h6></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="workflow_body_scroll" id="workflowContainer">
                                <div>
                                    <img title="Start" src="/img/input_Icons/Start.svg" width="30" height="30" draggable="false" loading="lazy" alt="Start image" />
                                </div>
                                <div class="" id="workflowActions"></div>
                                <div class="row mx-0" id="EndWorkflowButton" style="display:none;">
                                    <i class="cp-workflow-line fs-7"></i>
                                    <img title="End" src="/img/input_Icons/End.svg" width="30" height="30" draggable="false" loading="lazy" alt="End image" />
                                </div>
                            </div>
                            <div class="contextMenu dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <ul class="UlContextBtn dropdown-menu dropdown-menu-lg-end fs-8" style="display:block;">
                                    <li id="btnSelectAll">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-uncheck me-1 fs-7"></i>Select All</span>
                                            <small class="font-monospace">ctrl + a</small>
                                        </a>
                                    </li>
                                    <li id="btnUnselect">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-checks me-1  fs-7"></i>Unselect All</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>
                                    <li id="btnActionEdit">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-edit me-1 text-dark fs-7"></i>Edit</span>
                                            <small class="font-monospace">ctrl + E</small>
                                        </a>
                                    </li>
                                    <li id="btnCut">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-cut me-1  fs-7"></i>Cut</span>
                                            <small class="font-monospace">ctrl + x</small>
                                        </a>
                                    </li>
                                    <li id="btnCopy">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-copy me-1  fs-7"></i>Copy</span>
                                            <small class="font-monospace">ctrl + c</small>
                                        </a>
                                    </li>
                                    <li id="btnPaste">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-paste me-1  fs-7"></i>Paste</span>
                                            <small class="font-monospace">ctrl + v</small>
                                        </a>
                                    </li>
                                    <li id="btndelete">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-Delete me-1 text-dark  fs-7"></i>Delete</span>
                                            <small class="font-monospace">Del</small>
                                        </a>
                                    </li>
                                    <li id="btnConditional">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-conditional me-1 text-dark fs-7"></i>Add Condition</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>
                                    <li id="btnGoToConditional">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-left-right me-1 text-dark fs-7"></i>Go To</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>
                                    <li id="removeConditional">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-Delete me-1 text-dark fs-7"></i>Delete Condition</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>
                                    <li id="removeIfConditional">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-link-off text-dark me-1 fs-7"></i>Remove Success</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>
                                    <li id="removeElseConditional">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-link-off text-dark me-1 fs-7"></i>Remove Failure</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>

                                    <li id="btnParallel">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-Parallel me-1 fs-7"></i>Parallel</span>
                                            <small class="font-monospace">ctrl + p</small>
                                        </a>
                                    </li>
                                    <li id="btnUnParallel">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-Unparallel me-1  fs-7"></i>Unparallel</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>
                                    <li id="btnGroup">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-park-solid-group me-1  fs-7"></i>Group</span>
                                            <small class="font-monospace">ctrl + g</small>
                                        </a>
                                    </li>
                                    <li id="btnEditGroup">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-edit me-1 text-dark  fs-7"></i>Edit Group</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>
                                    <li id="btnUnGroup">
                                        <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                            <span><i class="cp-ungroup me-1  fs-7"></i>Ungroup</span>
                                            <small class="font-monospace"></small>
                                        </a>
                                    </li>
                                </ul>
                            </div>

                            <div class="position-absolute bottom-0 text-start">
                                <div class="card bg-primary-subtle d-none" style="width:10rem;" id="actionTypeLegend">
                                    <div class="card-body text-start d-grid gap-2">
                                        <div class="d-flex align-items-center">
                                            <span class="rounded-pill me-2" style="height:10px; width: 10px; background-color:#0e97ff;"></span>
                                            <small>Common</small>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="rounded-pill me-2" style="height:10px; width: 10px; background-color:#db2828;"></span>
                                            <small>DR Operation</small>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="rounded-pill me-2" style="height:10px; width: 10px; background-color:#000099;"></span>
                                            <small>Monitoring</small>
                                        </div>
                                       
                                    </div>
                                </div>
                                <div class="collapse collapse-horizontal" id="Configuration_Shortcut_Collapse">
                                    <div class="card bg-primary-subtle" style="width:10rem;">
                                        <div class="card-body text-start">
                                            <div class="d-flex">
                                                <div class="card me-2">
                                                    <div class="card-body p-2">
                                                        <i class="cp-application fs-5 text-primary"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0" id="serverCount">0</h6>
                                                    <small>Servers</small>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="card me-2">
                                                    <div class="card-body p-2">
                                                        <i class="cp-database fs-5 text-primary"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0" id="databaseCount">0</h6>
                                                    <small>Databases</small>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="card me-2 mb-0">
                                                    <div class="card-body p-2">
                                                        <i class="cp-Actions fs-5 text-primary"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0" id="actionsCount">0</h6>
                                                    <small>Actions</small>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="card bg-primary-subtle d-none" style="width:10rem;">
                                        <div class="card-body text-start d-grid gap-2">
                                            <div class="d-flex align-items-center">
                                                <span class="bg-success rounded-pill me-2" style="height:8px; width: 18px;"></span>
                                                <small>PR Site Group</small>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="bg-primary rounded-pill me-2" style="height:8px; width: 18px;"></span>
                                                <small>DR Group</small>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="bg-warning rounded-pill me-2" style="height:8px; width: 18px;"></span>
                                                <small>Database</small>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="rounded-pill me-2" style="height:8px; width: 18px; background-color:var(--bs-cyan);"></span>
                                                <small>Server</small>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="rounded-pill me-2" style="height:8px; width: 18px; background-color:var(--bs-purple);"></span>
                                                <small>OS</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-secondary start-0 mb-2" title="Details" type="button" data-bs-toggle="collapse" data-bs-target="#Configuration_Shortcut_Collapse" aria-expanded="false" aria-controls="collapseWidthExample">
                                    <i class="cp-user-details fs-5"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@* AI Suggestions *@
<div class="offcanvas offcanvas-end rounded-3 shadow-lg border-0" data-bs-scroll="true" tabindex="-1" data-bs-backdrop="false" id="offcanvasScrolling" aria-labelledby="offcanvasScrollingLabel"
     style="width:300px; height:calc(100vh - 171px); margin:auto 8px 29px 0px;">
    <div class="offcanvas-header border-bottom align-items-start p-2">
        <div class="d-grid w-100 mx-2">
            <img src="~/img/genie_bot/logo.svg" loading="lazy" alt="AI Profile image" />
            <span class="offcanvas-title" id="offcanvasScrollingLabel">Your Continuity Patrol Autopilot</span>
        </div>
        <button type="button" class="btn" id="AI_SuggestionReset" title="Reset"><i class="cp-refresh"></i></button>
        <button type="button" class="btn-close mt-1 me-1" id="AI_SuggestionClose" data-bs-dismiss="offcanvas" aria-label="Close" title="Close"></button>
    </div>
    <div class="offcanvas-body AI-Suggestion-List mb-1 p-0">
        <ul class="list-group list-group-flush " id="genieChatContainer">
        </ul>
        <ul class="list-group list-group-flush " id="genieChatContainerSugg">
            <li class="list-group-item AI-Suggestions-Option" id="ActionSuggestionContainerAI">
                <img class="AI-Profile" src="~/img/genie_bot/LOGO-SMALL.svg" loading="lazy" alt="AI Profile image" />
                <div class="d-grid AI-Suggestions-Bg">
                    <div class="">
                        <p class="mb-1">
                            Hi, This is<span class="text-primary fw-bold"> Dave. </span>I'am here to help you for creating Workflow's <span class="text-primary">Manually</span> or <span class="text-primary">Autonomously</span> in a minimal time.
                        </p>
                        <p> As per your suggestion lets start with <span class="text-primary">Supervising mode.</span></p>
                    </div>
                </div>
            </li>
            <li class="list-group-item AI-Suggestions-Option" id="suggestionChatContainerParent">
                <img class="AI-Profile" src="~/img/genie_bot/LOGO-SMALL.svg" loading="lazy" alt="AI Profile image" />
                <div class="d-grid AI-Suggestions-Bg">
                    <div class="" id="suggestionChatContainer">
                        <p>You have added the <span class="text-primary"> Action</span> form <span id="parentActionCatagoryName"></span>.</p>
                        <div id="lastConfiguredAISuggestion" class="mb-2"></div>
                        <p class="textBeforeAppendSuggestion">Here are some secondary <span class="text-primary"> suggestion actions</span> to add next.</p>
                        <div id="AISuggestionsList"></div>
                    </div>
                </div>
            </li>

            <li class="list-group-item AI-Suggestions-Option" id="mismatchAISuggestion">
                <img class="AI-Profile" src="~/img/genie_bot/LOGO-SMALL.svg" loading="lazy" alt="AI Profile image" />
                <div class="d-grid AI-Suggestions-Bg">
                    <div class="bg-danger text-white text-center p-2 rounded-top mb-2 d-grid">
                        <i class="cp-warning fs-1"></i>
                        <span class="fs-6 mt-2">Action Incompatible</span>
                    </div>
                    <div class="">
                        <p>You have added the <span class="text-danger">Mismatches </span>action in the</p>
                        <div id="mismatchActionCheck" class="mb-3">
                        </div>
                        <p>Here are some <span class="text-primary"> suggestion action</span> to add next.</p>
                        <div id="mismatchAISuggestionList">
                        </div>
                    </div>
                </div>
            </li>
            <li id="AIIngnoreButton">
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button class="btn btn-danger me-md-3" type="button">Ignore</button>
                </div>
            </li>
        </ul>
    </div>
</div>

<!-- AI Configuration Modal -->
<div class="modal fade" id="AI-ConfigurationModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-workflow-configuration"></i><span>Workflow Configuration</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="mb-3">
                    <img src="/img/genie_bot/logo.svg" loading="lazy" alt="AI Profile image">
                </div>
                <div class="form-group">
                    <div class="row row-cols-3 d-flex justify-content-center">
                        <div class="position-relative col d-none">
                            <input class="btn-check AIModeCheck" type="radio" value="0" id="option" name="genie" data-toggle="button" autocomplete="off">
                            <label class="site_type btn border-secondary-subtle d-grid" for="option">
                                <i class="cp-infra-object fs-2 text-primary"></i>
                                <span class="text-center mt-3 fw-bold">
                                    Manual<br /> Mode
                                </span>
                            </label>
                        </div>
                        <div class="position-relative col">
                            <input class="btn-check AIModeCheck" type="radio" value="0" id="option2" name="genie" data-toggle="button" autocomplete="off">
                            <label class="site_type btn border-secondary-subtle d-grid" for="option2">
                                <i class="cp-supervising-mode fs-2 text-primary"></i>
                                <span class="text-center mt-3 fw-bold">
                                    Supervising Mode
                                </span>
                            </label>
                        </div>
                        <div class="position-relative col">
                            <input class="btn-check AIModeCheck" type="radio" value="1" id="option1" name="genie" data-toggle="button" autocomplete="off">
                            <label class="site_type btn border-secondary-subtle d-grid" for="option1">
                                <i class="cp-autonomous-mode fs-2 text-primary"></i>
                                <span class="text-center mt-3 fw-bold">
                                    Autonomous Mode
                                </span>
                            </label>
                        </div>
                    </div>
                    <div class="row d-flex justify-content-center mt-3">
                        <div class="col-10 text-center d-grid">
                            <i class="cp-note text-primary fs-5 my-2"></i>
                            <span class="text-secondary spanTextAIConfig">
                                Deals with interpreting different commands and is capable of executing privileged instructions.
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="AISavemodal" class="btn btn-primary">OK</button>
            </div>
        </div>
    </div>
</div>

@* Confirmation *@
<div class="modal" role="dialog" id="confirmationModal">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmation</h5>
            </div>
            <div class="modal-body">
                <p id="confirmationModalText"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="confirmationDiscard">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmationSave">Save</button>
            </div>
        </div>
    </div>
</div>

@* WorkflowSaveConfirmation *@
<div class="modal" role="dialog" id="confirmationWFModal">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmation</h5>
            </div>
            <div class="modal-body">
                <p id="confirmationModalWFText" class="fs-7 ms-3"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="confirmationWFDiscard">No</button>
                <button type="button" class="btn btn-primary" id="confirmationWFSave">Yes</button>
            </div>
        </div>
    </div>
</div>


@* templateConfirmation *@
<div class="modal" role="dialog" id="templateConfirmationModal">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmation</h5>
            </div>
            <div class="modal-body">
                <p id="confirmationTempModalText"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="confirmationtemplateDiscard">No</button>
                <button type="button" class="btn btn-primary" id="templateConfirmation">Yes</button>
            </div>
        </div>
    </div>
</div>

@* template find confirmation *@

<div class="modal" role="dialog" id="templateFindConfirmationModal">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmation</h5>
            </div>
            <div class="modal-body">
                <p>
                    Some of the action names do not match the provided find value. Are you sure you want to proceed?
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="confirmationtemplateFindDiscard">No</button>
                <button type="button" class="btn btn-primary" id="templateFindConfirmation">Yes</button>
            </div>
        </div>
    </div>
</div>


@* Confirmation *@
<div class="modal" role="dialog" id="roleReverseConfirmationModal">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body roleReverseContainer">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="confirmationRRDiscard">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmationRRSave">Save</button>
            </div>
        </div>
    </div>
</div>

@* saveConfirmation *@
<div class="modal" role="dialog" id="saveConfirmationModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmation</h5>
            </div>
            <div class="modal-body">
                <p id="confirmationModalText">you have made some change on <span id="workflowNameConfirmation"></span> workflow. Do you want to save it ?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="saveConfirmationDiscard">No</button>
                <button type="button" class="btn btn-primary" id="saveConfirmationmodal">Yes</button>
            </div>
        </div>
    </div>
</div>

@* Delete Confirmation *@
<div class="modal" role="dialog" id="confirmationDeleteModal">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmation</h5>
            </div>
            <div class="modal-body">
                <p id="confirmationDeleteText"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal" id="confirmationDeleteSave">OK</button>
            </div>
        </div>
    </div>
</div>


@* Delete Actions *@
<div class="modal fade" id="deleteActionsModal">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">

            <div class="modal-header p-0">
                <img class="delete-img" src="~/img/isomatric/delete.png" alt="Delete Img" loading="lazy" />
            </div>
            <div class="modal-body text-center pt-0">
                <h5 class="fw-bold">Are you sure?</h5>
                <p id="ActionDeleteText"></p>

            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="btnDeleteActions" onclick="deleteAction()">Yes</button>
            </div>

        </div>
    </div>
</div>

@* Duplicate Actions *@
<div class="modal" id="DuplicateActionsModal" tabindex="-1" area-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <img class="w-100" src="~/img/isomatric/confirmation.svg" alt="Duplicate Actions" loading="lazy" />
            </div>
            <div class="modal-body text-center pt-5">
                <h5 class="fw-bold">Confirmation</h5>
                <p id="textDuplicate1"></p>
                <p id="textDuplicate2"></p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-target="#CreateModal" data-bs-toggle="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="duplicateConfirmation">Yes</button>
            </div>

        </div>
    </div>
</div>

<!--Action Create-->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="configureModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/ActionCreate" />
</div>

<!--Group name create-->
<div class="modal fade" id="CreateGroupNameModal" tabindex="-1" aria-labelledby="configureModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/GroupNameCreate" />
</div>

<!--Save workflow-->
<div class="modal fade" tabindex="-1" id="SaveWorkflowModal" data-bs-backdrop="static" aria-labelledby="configureModalLabel" aria-hidden="true">
    <partial name="WorkflowModals/WorkflowSave" />
</div>

<!--Generate Template Modal -->
<div class="modal fade" id="GenerateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/GenrateTemplate" />
</div>

<!--Restore Template Modal -->
<div class="modal fade" id="RestoreModal" tabindex="-1" aria-labelledby="exampleModalLabelfas" data-bs-backdrop="static" aria-hidden="true">
    <partial name="WorkflowModals/RestoreTemplate" />
</div>



@* Infra Attach *@
<div class="modal fade" id="infraAttachModal" tabindex="-1" aria-labelledby="configureModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/InfraAttach" />
</div>

@* de-Attach workflow *@
<div class="modal fade" id="detachWorkflow" tabindex="-1" aria-labelledby="configureModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/DeAttach" />
</div>

<!--Load Saved WorkFlow-->
<div class="modal fade" tabindex="-1" id="LoadWorkflowListModal" aria-labelledby="configureModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/WorkflowLoad" />
</div>

<!--Save As WorkFlow Modal-->
<div class="modal fade" tabindex="-1" id="SaveAsWorkflowListModal" aria-labelledby="configureModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/WorkflowSaveAs" />
</div>

<!--Conformation Lock WorkFlow-->
<div class="modal fade" id="lockWorkFlow" tabindex="-1" aria-labelledby="configureModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/WorkflowLock" />
</div>

<!--Conformation Publish WorkFlow-->
<div class="modal fade" id="publishWorkFlow" tabindex="-1" aria-labelledby="configureModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/WorkflowPublish" />
</div>

<!--Modal WorkFlow Delete-->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/Delete" />
</div>

<!--Import Workflow-->
<div class="modal fade" tabindex="-1" id="ImportWorkflowModal" aria-labelledby="configureModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/ImportWorkflow" />
</div>

<!--Export Workflow-->
<div class="modal fade" tabindex="-1" id="ExportWorkflowModal" aria-labelledby="configureModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/ExportWorkflow" />
</div>

@* GoToActionConditional *@
<div class="modal fade" tabindex="-1" id="GoToConditionalModal" aria-labelledby="conditionalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/GoToConditional" />
</div>

<!--Code Compare Modal -->
<div class="modal fade" id="CompareVersionModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="WorkflowModals/VersionCompare" />
</div>

<!--Conformation Verify WorkFlow-->
<div class="modal fade" id="verifyWorkFlowModal" tabindex="-1" aria-labelledby="configureModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="~/img/isomatric/Lock-Unlock.svg" alt="Lock & Unlock Img" loading="lazy" />
            </div>

            <div class="modal-body text-center pt-0">
                <h5 class="fw-bold">Are you sure?</h5>
                <p class="d-flex align-items-center justify-content-center gap-1">You want to <span class="updateVerifyText"></span> the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:80px" id="verifyWorkflowText"></span>workflow?</p>
            </div>

            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
                <button type="button" class="btn btn-primary" id="btnVerifyWorkflow">Yes</button>
            </div>
        </div>
    </div>
</div>


<div id="pop-up">
    <table id="ServiceOverviewtable" class="table table-hover datatable d-none">
        <thead>
            <tr>
                <th class="text-center" style="width:5%;">Action Name</th>
                <th class="text-center" style="width:5%;">Description</th>
                <th class="text-center" style="width:5%;">RTO</th>
                <th class="text-center" style="width:5%;">Action Category</th>
                <th class="text-center" style="width:5%;">Properties</th>
            </tr>
        </thead>
        <tbody id="ServiceOverviewtableBody"></tbody>
    </table>

</div>


@* campareConfirmationModal *@
<div class="modal fade" id="WorkflowComapareModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="~/img/isomatric/Lock-Unlock.svg" alt="Lock & Unlock Img" loading="lazy" />
            </div>
            <div class="modal-body text-center pt-0">
                <h5 class="fw-bold">Are you sure?</h5>
                <p>You want to <span>restore</span> <span class="font-weight-bolder text-primary" id="workflowRestoreData"></span> version?</p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" id="btnVersionRestoreCancel" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
                <button type="button" class="btn btn-primary" id="btnWFVersionRetore">Yes</button>
            </div>
        </div>
    </div>
</div>


<!--Restore Animation Modal -->
<div class="modal fade" id="RestoreAnimationModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-scrollabel modal-dialog-centered modal-lg ">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-workflow-configuration"></i><span>Generate Workflow</span></h6>
            </div>
            <div class="modal-body d-flex align-items-center justify-content-center">
                <div>
                    <img src="~/img/restore_GIF/restore.gif" height="280px" loading="lazy" alt="restore image" />
                    <p class="text-end me-2 fw-bold mb-0 progressPercentage">0%</p>
                    <div class="flex-fill p-2 border-0 list-group-item">
                        <div class="progress" style="height:13px">
                            <div role="progressbar" class="progress-bar restoreProgressBar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    <p class="text-center fw-bold progressText">Initializing template</p>
                </div>
            </div>
            <div class="modal-footer d-block">
            </div>
        </div>
    </div>
</div>


<!--Conditional Action Modal -->
<div class="modal fade" id="ConditionalActionModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-scrollabel modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-conditional"></i><span>Conditional Action</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="min-height: 27em;">

                <div class="form-group">
                    <div class="form-label">Success Action</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-action-builder"></i></span>
                        <input type="text" class="form-control" placeholder="Success Action" />
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label"> Failure Action</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-action-builder"></i></span>
                        <select class="form-select-modal" data-live-search="true" data-placeholder="Select Failure Action">
                            <option></option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">Failure Trial Count</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-settings"></i></span>
                        <input type="text" class="form-control" placeholder="Failure Trial Count" />
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Connect</button>
            </div>
        </div>
    </div>
</div>

@* Upload workflow runbook *@
<div class="modal fade" tabindex="-1" id="UploadRunbookWorkflowModal" aria-labelledby="configureModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-scrollabel modal-dialog-centered">

        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title"><i class="cp-upload-up"></i><span class="mt-1">Upload Runbook</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <form id="example-form">
                    <div class="form-group">
                        <div class="form-label">Choose Runbook</div>
                        <div class="input-group">
                            <input type="file" class="form-control" name="uploadRunbookWorkflow" id="uploadWorkflow" accept=".xls, .xlsx, application /vnd.ms-excel, application /vnd.openxmlformats-officedocument.spreadsheetml.sheet" placeholder="Upload Workflow" />
                        </div>
                        <span id="uploadWorkflow-error"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="uploadRunbookWorkflow">
                    Upload<div id="WFUploadRunbookLoader" class="spinner-border text-white ms-2 mt-1 p-1 d-none" style="width: 0.8rem; height: 0.8rem;" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>

<div id="orchestrationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Orchestration.CreateAndEdit" aria-hidden="true"></div>
<div id="orchestrationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Orchestration.Delete" aria-hidden="true"></div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>


@* <environment include="Development"> *@
<script src="~/js/wizard.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>

<script src="~/js/itautomation/workflowconfiguration/workflowdiagram.js"></script>
<script src="~/js/itautomation/workflowconfiguration/conditionalworkflow.js"></script>
<script src="~/js/itautomation/workflowconfiguration/infraobjectattach.js"></script>
<script src="~/js/itautomation/workflowconfiguration/jsondiff.js"></script>
<script src="~/js/itautomation/workflowconfiguration/loadworkflow.js"></script>
<script src="~/js/itautomation/workflowconfiguration/templategenerate.js"></script>
<script src="~/js/itautomation/workflowconfiguration/validations.js"></script>
<script src="~/js/itautomation/workflowconfiguration/workflowconditional.js"></script>
<script src="~/js/itautomation/workflowconfiguration/workflowconfiguration.js"></script>
<script src="~/js/itautomation/workflowconfiguration/workflowvalidations.js"></script>
<script src="~/js/itautomation/workflowconfiguration/compareworkflow.js"></script>
<script src="~/js/itautomation/workflowconfiguration/jsondiff.js"></script>
<script src="~/js/slide_toggle.js"></script>
@* <script src="~/lib/selectize.js/js/selectize.min.js/"></script> *@
<script src="~/lib/exceltojson/exceltojson.js"></script>

