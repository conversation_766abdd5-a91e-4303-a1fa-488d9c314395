﻿using ContinuityPatrol.Application.Features.MSSQLDBMirroingLogs.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLDBMirroingLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLDBMirroingLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLDBMirroingLogsModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract
{
    public interface IMSSQLDbMirroringMonitorLogsService
    {
        //    Task<BaseResponse> CreateMssqlDBmirroringMonitorLog(CreateSQLDBMirroringLogsCommand createmssqldbmirrroringLogCommand);

        //    Task<List<MSSQLDBMirroingLogsVM>> GetAllDBmirrorMonitorLog();

        //    Task<MSSQLDBMirroingLogsVM> GetDB2HADRMonitorLogById(string id);

        //    Task<PaginatedResult<MSSQLDBMirroingLogsVM>> GetPaginatedDBmirroringMonitorLog(GetMSSQLDbMonitorLogsPaginatedListQuery query);

        //    Task<List<DB2HADRMonitorLogDetailByTypeVm>> GetMssqlDbMirroringMonitorLogByType(string type);
        Task<BaseResponse> CreateAsync(CreateSQLDBMirroringLogsCommand createSqldbMirroringLogCommand);
      
         Task<List<MSSQLDBMirroringLogListVm>> GetAllSqlDbMirroringLogs();
       // Task<MSSQLDBMirroingLogsVM> GetByReferenceId(string id);
        Task<List<SQLDBMirroringMonitorLogsDetailByTypeVm>> GetSqlDbMirroringLogsByType(string type);

        Task<PaginatedResult<MSSQLDBMirroringLogListVm>> GetPaginatedMSSQLDBMirroringMonitorLogs(GetMSSQLDbMonitorLogsPaginatedListQuery query);

    }
}
