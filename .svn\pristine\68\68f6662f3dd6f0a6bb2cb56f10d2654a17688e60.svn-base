using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SiteLocationFixture : IDisposable
{
    public List<SiteLocation> SiteLocationPaginationList { get; set; }
    public List<SiteLocation> SiteLocationList { get; set; }
    public SiteLocation SiteLocationDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SiteLocationFixture()
    {
        var fixture = new Fixture();

        SiteLocationList = fixture.Create<List<SiteLocation>>();

        SiteLocationPaginationList = fixture.CreateMany<SiteLocation>(20).ToList();

        SiteLocationDto = fixture.Create<SiteLocation>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public SiteLocation CreateSiteLocation(
        string city = "Default City",
        string cityAscii = "Default City ASCII",
        string country = "Default Country",
        double lat = 0.0,
        double lng = 0.0,
        string iso2 = "XX",
        string iso3 = "XXX",
        bool isActive = true,
        bool isDelete = false)
    {
        return new SiteLocation
        {
            ReferenceId = Guid.NewGuid().ToString(),
            City = city,
            CityAscii = cityAscii,
            Country = country,
            Lat = lat.ToString(),
            Lng = lng.ToString(),
            Iso2 = iso2,
            Iso3 = iso3,
            IsActive = isActive,
            IsDelete = isDelete,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public List<SiteLocation> CreateMultipleSiteLocations(int count)
    {
        var locations = new List<SiteLocation>();
        for (int i = 1; i <= count; i++)
        {
            locations.Add(CreateSiteLocation(
                city: $"City {i}",
                cityAscii: $"City {i} ASCII",
                country: $"Country {i}",
                lat: i * 10.0,
                lng: i * -10.0,
                iso2: $"C{i}",
                iso3: $"C{i:D2}X"
            ));
        }
        return locations;
    }

    public SiteLocation CreateSiteLocationWithSpecificId(string referenceId, string city = "Test City")
    {
        return new SiteLocation
        {
            ReferenceId = referenceId,
            City = city,
            CityAscii = $"{city} ASCII",
            Country = "Test Country",
            Lat = "40.7128",
            Lng = "-74.0060",
            Iso2 = "TC",
            Iso3 = "TCT",
            IsActive = true,
            IsDelete = false,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public SiteLocation CreateSiteLocationForCountry(string country, string city = null)
    {
        return CreateSiteLocation(
            city: city ?? $"City in {country}",
            cityAscii: $"{city ?? $"City in {country}"} ASCII",
            country: country,
            iso2: country.Length >= 2 ? country.Substring(0, 2).ToUpper() : "XX",
            iso3: country.Length >= 3 ? country.Substring(0, 3).ToUpper() : "XXX"
        );
    }

    public SiteLocation CreateSiteLocationWithCoordinates(double lat, double lng, string city = "Coordinate City")
    {
        return CreateSiteLocation(
            city: city,
            cityAscii: $"{city} ASCII",
            lat: lat,
            lng: lng
        );
    }

    public SiteLocation CreateSiteLocationWithIso(string iso2, string iso3, string city = "ISO City")
    {
        return CreateSiteLocation(
            city: city,
            cityAscii: $"{city} ASCII",
            iso2: iso2,
            iso3: iso3
        );
    }

    public List<SiteLocation> CreateSiteLocationsForCountries(List<string> countries)
    {
        var locations = new List<SiteLocation>();
        foreach (var country in countries)
        {
            locations.Add(CreateSiteLocationForCountry(country));
        }
        return locations;
    }

    public List<SiteLocation> CreateSiteLocationsWithStatus(int activeCount, int inactiveCount)
    {
        var locations = new List<SiteLocation>();

        for (int i = 1; i <= activeCount; i++)
        {
            locations.Add(CreateSiteLocation(
                city: $"Active City {i}",
                isActive: true
            ));
        }

        for (int i = 1; i <= inactiveCount; i++)
        {
            locations.Add(CreateSiteLocation(
                city: $"Inactive City {i}",
                isActive: false
            ));
        }

        return locations;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
