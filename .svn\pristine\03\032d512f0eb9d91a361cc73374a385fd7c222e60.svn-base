using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class RoboCopyRepository : BaseRepository<RoboCopy>, IRoboCopyRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public RoboCopyRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsRoboCopyNameUnique(string name)
    {
        var matches = _dbContext.RoboCopys.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }
    public override async Task<PaginatedResult<RoboCopy>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<RoboCopy> specification, string sortColumn, string sortOrder)
    {
        return await Entities.Specify(specification).DescOrderById()
            .Select(x => new RoboCopy
            {
                Id=x.Id,
                ReferenceId=x.ReferenceId,
                Name=x.Name,
                ReplicationType=x.ReplicationType,
                Properties=x.Properties

            }).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
}