﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class WorkflowPermissionFilterSpecification : Specification<WorkflowPermission>
{
    public WorkflowPermissionFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.AccessProperties != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("accessproperties=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.AccessProperties.Contains(stringItem.Replace("accessproperties=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("accesstype=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.AccessType.Contains(stringItem.Replace("accesstype=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("userproperties=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.UserProperties.Contains(stringItem.Replace("userproperties=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.AccessProperties.Contains(searchString) || p.AccessType.Contains(searchString) ||
                    p.UserProperties.Contains(searchString);
            }
        }
    }
}