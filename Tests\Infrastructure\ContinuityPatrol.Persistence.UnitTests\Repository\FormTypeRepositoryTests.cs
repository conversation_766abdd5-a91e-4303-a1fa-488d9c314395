using AutoFixture;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class FormTypeRepositoryTests : IClassFixture<FormTypeFixture>, IDisposable
{
    private readonly FormTypeFixture _formTypeFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly FormTypeRepository _repository;

    public FormTypeRepositoryTests(FormTypeFixture formTypeFixture)
    {
        _formTypeFixture = formTypeFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new FormTypeRepository(_dbContext);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.FormTypes.RemoveRange(_dbContext.FormTypes);
        await _dbContext.SaveChangesAsync();
    }

    #region GetFormTypeById Tests

    [Fact]
    public async Task GetFormTypeById_ReturnsFormType_WhenExistsAndIsDeleteTrue()
    {
        // Arrange
        await ClearDatabase();
        var formTypeId = Guid.NewGuid().ToString();
        var formType = new FormType
        {
            ReferenceId = formTypeId,
            IsActive = true,
            IsDelete = true,
            FormTypeName = "Test Form Type"
        };

        await _dbContext.FormTypes.AddAsync(formType);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeById(formTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(formTypeId, result.ReferenceId);
        Assert.True(result.IsDelete);
    }

    [Fact]
    public async Task GetFormTypeById_ReturnsNull_WhenExistsButIsDeleteFalse()
    {
        // Arrange
        await ClearDatabase();
        var formTypeId = Guid.NewGuid().ToString();
        var formType = new FormType
        {
            ReferenceId = formTypeId,
            IsActive = true,
            IsDelete = false,
            FormTypeName = "Test Form Type"
        };

        await _dbContext.FormTypes.AddAsync(formType);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeById(formTypeId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetFormTypeById_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetFormTypeById(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetFormTypeById_ReturnsNull_WhenInactive()
    {
        // Arrange
        await ClearDatabase();
        var formTypeId = Guid.NewGuid().ToString();
        var formType = new FormType
        {
            ReferenceId = formTypeId,
            IsActive = true,
            IsDelete = true,
            FormTypeName = "Inactive Form Type"
        };

        await _dbContext.FormTypes.AddAsync(formType);
        await _dbContext.SaveChangesAsync();
        formType.IsActive = false;
         _dbContext.FormTypes.Update(formType);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GetFormTypeById(formTypeId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetFormTypeNames Tests

    [Fact]
    public async Task GetFormTypeNames_ReturnsActiveFormTypes_WhenDataExists()
    {
        // Arrange
        await ClearDatabase();
        var formTypes = new List<FormType>
        {
            new FormType { ReferenceId = Guid.NewGuid().ToString(), FormTypeName = "Survey", IsActive = true },
            new FormType { ReferenceId = Guid.NewGuid().ToString(), FormTypeName = "Assessment", IsActive = true },
            new FormType { ReferenceId = Guid.NewGuid().ToString(), FormTypeName = "Inactive Form" }
        };

        await _dbContext.FormTypes.AddRangeAsync(formTypes);
        await _dbContext.SaveChangesAsync();
        formTypes[2].IsActive = false;
         _dbContext.FormTypes.UpdateRange(formTypes);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GetFormTypeNames();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.FormTypeName == "Survey");
        Assert.Contains(result, x => x.FormTypeName == "Assessment");
        Assert.DoesNotContain(result, x => x.FormTypeName == "Inactive Form");
    }

    [Fact]
    public async Task GetFormTypeNames_ReturnsEmpty_WhenNoActiveFormTypes()
    {
        // Arrange
        await ClearDatabase();
        var formTypes = new List<FormType>
        {
            new FormType { ReferenceId = Guid.NewGuid().ToString(), FormTypeName = "Inactive Form 1", IsActive = false },
            new FormType { ReferenceId = Guid.NewGuid().ToString(), FormTypeName = "Inactive Form 2", IsActive = false }
        };

        await _dbContext.FormTypes.AddRangeAsync(formTypes);
        await _dbContext.SaveChangesAsync();
        formTypes.ForEach(x => x.IsActive = false);
        _dbContext.FormTypes.UpdateRange(formTypes);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeNames();

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetFormTypeNames_ReturnsEmpty_WhenNoData()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetFormTypeNames();

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetFormTypeNames_ReturnsOnlyReferenceIdAndFormTypeName()
    {
        // Arrange
        await ClearDatabase();
        var formType = new FormType
        {
            ReferenceId = Guid.NewGuid().ToString(),
            FormTypeName = "Test Form Type",
            FormTypeLogo = "test-logo.png",
            IsActive = true,
            IsDelete = false
        };

        await _dbContext.FormTypes.AddAsync(formType);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeNames();

        // Assert
        Assert.Single(result);
        var returnedFormType = result.First();
        Assert.Equal(formType.ReferenceId, returnedFormType.ReferenceId);
        Assert.Equal(formType.FormTypeName, returnedFormType.FormTypeName);
        // These should be null/default as they're not selected
        Assert.Null(returnedFormType.FormTypeLogo);
        Assert.Equal(0, returnedFormType.Id);
    }

    #endregion

    #region IsFormTypeNameExist Tests

    [Fact]
    public async Task IsFormTypeNameExist_ReturnsTrue_WhenNameExists_WithoutId()
    {
        // Arrange
        var formTypeName = "Survey Form Type";
        _formTypeFixture.FormTypeDto.FormTypeName = formTypeName;

        await _dbContext.FormTypes.AddAsync(_formTypeFixture.FormTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsFormTypeNameExist(formTypeName, null);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsFormTypeNameExist_ReturnsFalse_WhenNameDoesNotExist_WithoutId()
    {
        // Arrange
        var nonExistentName = "Non Existent Form Type";

        // Act
        var result = await _repository.IsFormTypeNameExist(nonExistentName, null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsFormTypeNameExist_ReturnsTrue_WhenNameExists_WithDifferentId()
    {
        // Arrange
        var formTypeName = "Survey Form Type";
        var existingId = Guid.NewGuid().ToString();
        var differentId = Guid.NewGuid().ToString();

        _formTypeFixture.FormTypeDto.FormTypeName = formTypeName;
        _formTypeFixture.FormTypeDto.ReferenceId = existingId;

        await _dbContext.FormTypes.AddAsync(_formTypeFixture.FormTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsFormTypeNameExist(formTypeName, differentId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsFormTypeNameExist_ReturnsFalse_WhenNameExists_WithSameId()
    {
        // Arrange
        var formTypeName = "Survey Form Type";
        var sameId = Guid.NewGuid().ToString();

        _formTypeFixture.FormTypeDto.FormTypeName = formTypeName;
        _formTypeFixture.FormTypeDto.ReferenceId = sameId;

        await _dbContext.FormTypes.AddAsync(_formTypeFixture.FormTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsFormTypeNameExist(formTypeName, sameId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsFormTypeNameExist_ReturnsFalse_WhenInvalidGuid()
    {
        // Arrange
        var formTypeName = "Survey Form Type";
        var invalidGuid = "invalid-guid";

        _formTypeFixture.FormTypeDto.FormTypeName = formTypeName;

        await _dbContext.FormTypes.AddAsync(_formTypeFixture.FormTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsFormTypeNameExist(formTypeName, invalidGuid);

        // Assert
        Assert.True(result); // Should behave like no ID provided
    }

    #endregion

    #region IsFormTypeNameUnique Tests

    [Fact]
    public async Task IsFormTypeNameUnique_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var formTypeName = "Unique Form Type";
        _formTypeFixture.FormTypeDto.FormTypeName = formTypeName;

        await _dbContext.FormTypes.AddAsync(_formTypeFixture.FormTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsFormTypeNameUnique(formTypeName);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsFormTypeNameUnique_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var nonExistentName = "Non Existent Form Type";

        // Act
        var result = await _repository.IsFormTypeNameUnique(nonExistentName);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsFormTypeNameUnique_IsCaseSensitive()
    {
        // Arrange
        var formTypeName = "Survey Form Type";
        _formTypeFixture.FormTypeDto.FormTypeName = formTypeName;

        await _dbContext.FormTypes.AddAsync(_formTypeFixture.FormTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsFormTypeNameUnique("SURVEY FORM TYPE");

        // Assert
        Assert.False(result); // Should not match due to case sensitivity
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsCorrectData_WhenDataExists()
    {
        // Arrange
        var formTypes = _formTypeFixture.FormTypePaginationList;
        foreach (var formType in formTypes)
        {
            formType.IsActive = true;
            formType.IsDelete = false;
        }

        await _dbContext.FormTypes.AddRangeAsync(formTypes);
        await _dbContext.SaveChangesAsync();

        var specification = new FormTypeFilterSpecification(null);

        // Act
        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 10,
            specification: specification,
            sortColumn: "Id",
            sortOrder: "asc"
        );

        // Assert
        Assert.NotNull(result);
        Assert.Equal(10, result.Data.Count);
        Assert.Equal(20, result.TotalCount);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(2, result.TotalPages);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsEmpty_WhenNoData()
    {
        // Arrange
        var specification = new FormTypeFilterSpecification(null);

        // Act
        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 10,
            specification: specification,
            sortColumn: "Id",
            sortOrder: "asc"
        );

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.Data);
        Assert.Equal(0, result.TotalCount);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(0, result.TotalPages);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsOnlySelectedFields()
    {
        // Arrange
        var formType = new FormType
        {
            ReferenceId = Guid.NewGuid().ToString(),
            FormTypeName = "Test Form Type",
            FormTypeLogo = "test-logo.png",
            IsActive = true,
            IsDelete = false
        };

        await _dbContext.FormTypes.AddAsync(formType);
        await _dbContext.SaveChangesAsync();

        var specification = new FormTypeFilterSpecification(null);

        // Act
        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 10,
            specification: specification,
            sortColumn: "Id",
            sortOrder: "asc"
        );

        // Assert
        Assert.Single(result.Data);
        var returnedFormType = result.Data.First();
        Assert.Equal(formType.Id, returnedFormType.Id);
        Assert.Equal(formType.ReferenceId, returnedFormType.ReferenceId);
        Assert.Equal(formType.FormTypeName, returnedFormType.FormTypeName);
        Assert.Equal(formType.FormTypeLogo, returnedFormType.FormTypeLogo);
        Assert.Equal(formType.IsDelete, returnedFormType.IsDelete);
    }

    [Fact]
    public async Task PaginatedListAllAsync_RespectsPageSize()
    {
        // Arrange
        var formTypes = _formTypeFixture.FormTypePaginationList.Take(15).ToList();
        foreach (var formType in formTypes)
        {
            formType.IsActive = true;
            formType.IsDelete = false;
        }

        await _dbContext.FormTypes.AddRangeAsync(formTypes);
        await _dbContext.SaveChangesAsync();

        var specification = new FormTypeFilterSpecification(null);

        // Act
        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 5,
            specification: specification,
            sortColumn: "Id",
            sortOrder: "asc"
        );

        // Assert
        Assert.Equal(5, result.Data.Count);
        Assert.Equal(15, result.TotalCount);
        Assert.Equal(3, result.TotalPages);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddFormType_WhenValidFormType()
    {
        // Arrange
        var formType = _formTypeFixture.FormTypeDto;
        formType.FormTypeName = "Test Form Type";
        formType.FormTypeLogo = "test-logo.png";
        formType.IsDelete = false;

        // Act
        var result = await _repository.AddAsync(formType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(formType.FormTypeName, result.FormTypeName);
        Assert.Equal(formType.FormTypeLogo, result.FormTypeLogo);
        Assert.Equal(formType.IsDelete, result.IsDelete);
        Assert.Single(_dbContext.FormTypes);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenFormTypeIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsFormType_WhenExists()
    {
        // Arrange
        _formTypeFixture.FormTypeDto.Id = 1;
        await _dbContext.FormTypes.AddAsync(_formTypeFixture.FormTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_formTypeFixture.FormTypeDto.Id, result.Id);
        Assert.Equal(_formTypeFixture.FormTypeDto.FormTypeName, result.FormTypeName);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsFormType_WhenExists()
    {
        // Arrange
        var referenceId = Guid.NewGuid().ToString();
        _formTypeFixture.FormTypeDto.ReferenceId = referenceId;
        await _dbContext.FormTypes.AddAsync(_formTypeFixture.FormTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal(_formTypeFixture.FormTypeDto.FormTypeName, result.FormTypeName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateFormType_WhenValidFormType()
    {
        // Arrange
        _dbContext.FormTypes.Add(_formTypeFixture.FormTypeDto);
        await _dbContext.SaveChangesAsync();

        _formTypeFixture.FormTypeDto.FormTypeName = "Updated Form Type Name";
        _formTypeFixture.FormTypeDto.FormTypeLogo = "updated-logo.png";
        _formTypeFixture.FormTypeDto.IsDelete = true;

        // Act
        var result = await _repository.UpdateAsync(_formTypeFixture.FormTypeDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Form Type Name", result.FormTypeName);
        Assert.Equal("updated-logo.png", result.FormTypeLogo);
        Assert.True(result.IsDelete);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenFormTypeIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveFormType_WhenExists()
    {
        // Arrange
        await _dbContext.FormTypes.AddAsync(_formTypeFixture.FormTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(_formTypeFixture.FormTypeDto);

        // Assert
        var deletedFormType = await _dbContext.FormTypes.FindAsync(_formTypeFixture.FormTypeDto.Id);
        Assert.Null(deletedFormType);
    }


    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoData()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    #endregion
}
