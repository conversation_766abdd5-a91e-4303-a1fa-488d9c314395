using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowActionResultFixture : IDisposable
{
    public List<WorkflowActionResult> WorkflowActionResultPaginationList { get; set; }
    public List<WorkflowActionResult> WorkflowActionResultList { get; set; }
    public WorkflowActionResult WorkflowActionResultDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowActionResultFixture()
    {
        var fixture = new Fixture();

        WorkflowActionResultList = fixture.Create<List<WorkflowActionResult>>();

        WorkflowActionResultPaginationList = fixture.CreateMany<WorkflowActionResult>(20).ToList();

        WorkflowActionResultPaginationList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowActionResultList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowActionResultDto = fixture.Create<WorkflowActionResult>();

        WorkflowActionResultDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
