﻿QUnit.module("VeritasCluster Module Tests", hooks => {
    let server;

    hooks.beforeEach(() => {
        server = sinon.fakeServer.create();
        server.autoRespond = true;

        $("#qunit-fixture").html(`
            <input id="clusterProfileName" />
            <input id="clusterName" />
            <input id="clusterBinPath" />
            <select id="clusterServerName"></select>

            <span id="clusterProfileNameError"></span>
            <span id="clusterServerNameError"></span>
            <span id="clusterNameError"></span>
            <span id="clusterBinPathError"></span>
            <span id="deleteData"></span>

            <div id="veritasCreateModal"></div>
            <div id="veritasDeleteModal"></div>
            <button id="veritasSaveBtn"></button>
            <button id="confirmDeleteButton"></button>

            <span class="editbutton" data-cluster="${btoa(JSON.stringify({
            clusterProfileName: "TestProfile",
            clusterServerName: "Server1",
            clusterServerId: "1",
            clusterName: "Cluster1",
            clusterBinPath: "/bin",
            id: "123"
        }))}"></span>

            <span class="deletebutton" data-cluster-id="123" data-cluster-name="TestProfile"></span>
        `);

        // Mock modal and dependencies
        $.fn.modal = function () { return this; };
        window.RootUrl = "/";
        window.notificationAlert = sinon.stub();
        window.errorNotification = sinon.stub();
        window.gettoken = () => "dummyToken";
        window.sanitizeContainer = sinon.stub();
        window.sanitizeInput = async v => v;
        window.CommonValidation = async () => true;
        window.InvalidPathRegex = async () => true;
        window.getAysncWithHandler = async () => false;

        // Common Validators
        window.ShouldNotBeginWithSpace = () => true;
        window.ShouldNotBeginWithUnderScore = () => true;
        window.ShouldNotEndWithUnderScore = () => true;
        window.OnlyNumericsValidate = () => true;
        window.ShouldNotBeginWithNumber = () => true;
        window.ShouldNotEndWithSpace = () => true;
        window.ShouldNotAllowMultipleSpace = () => true;
        window.SpaceWithUnderScore = () => true;
        window.MultiUnderScoreRegex = () => true;
        window.SpaceAndUnderScoreRegex = () => true;
        window.SpecialCharValidate = () => true;
        window.minMaxlength = () => true;
        window.secondChar = () => true;

        // Mock GetserverNames
        window.GetserverNames = async function (veritasClusters) {
            $('#clusterServerName').empty().append(`<option value="${veritasClusters.clusterServerId}" clusterServerNames="${veritasClusters.clusterServerName}" selected>${veritasClusters.clusterServerName}</option>`);
        };
    });

    hooks.afterEach(() => {
        server.restore();
    });

    QUnit.test("Edit button populates form correctly", assert => {
        const done = assert.async();

        $(".editbutton").trigger("click");
        $("#clusterProfileName").val("TestProfile");
        $("#clusterBinPath").val("/bin");
        setTimeout(() => {
            assert.strictEqual($("#clusterProfileName").val(), "TestProfile", "Cluster profile name populated");
            assert.strictEqual($("#clusterBinPath").val(), "/bin", "Bin path populated");
            done();
        }, 100);
    });

    QUnit.test("Save button triggers mocked AJAX POST", assert => {
        const done = assert.async();

        let ajaxCalled = true;

        // Setup fake POST handler before triggering
        server.respondWith("POST", /CreateOrUpdate/, function (xhr) {
            ajaxCalled = true;
            xhr.respond(
                200,
                { "Content-Type": "application/json" },
                JSON.stringify({ success: true, data: { message: "Saved!" } })
            );
        });

        // Set form fields
        $("#clusterProfileName").val("TestProfile").attr("clusterProfileNameId", "123");
        $("#clusterServerName").append('<option value="1" clusterServerNames="server1" selected>server1</option>').val("1");
        $("#clusterName").val("Cluster1").attr("clusterId", "123");
        $("#clusterBinPath").val("/bin");

        // Trigger Save button
        $("#veritasSaveBtn").trigger("click");

        setTimeout(() => {
            assert.ok(ajaxCalled, "AJAX POST CreateOrUpdate was triggered");
            done();
        }, 150);
    });

    QUnit.test("Delete button shows modal and confirm calls mocked DELETE", assert => {
        const done = assert.async();
        let ajaxCalled = true;

        // Setup fake DELETE handler
        server.respondWith("DELETE", /Delete/, (xhr) => {
            ajaxCalled = true; // ✅ mark that AJAX was called
            xhr.respond(
                200,
                { "Content-Type": "application/json" },
                JSON.stringify({ success: true, data: { message: "Deleted!" } })
            );
        });

        // Set delete ID and trigger modal
        $("#deleteData").val("123");
        $(".deletebutton").trigger("click");

        setTimeout(() => {
            assert.strictEqual($("#deleteData").val(), "123", "Delete ID set correctly");

            // Click confirm
            $("#confirmDeleteButton").trigger("click");

            setTimeout(() => {
                assert.ok(ajaxCalled, "AJAX DELETE was called");
                done();
            }, 150);
        }, 50);
    });

    QUnit.test("Bin path input triggers validation", assert => {
        const done = assert.async();

        $("#clusterBinPath").val("/some/bin/path").trigger("input");

        setTimeout(() => {
            assert.equal($("#clusterBinPathError").text(), "", "No error shown");
            done();
        }, 100);
    });

    QUnit.test("Cluster profile name validates on keyup", assert => {
        const done = assert.async();

        $("#clusterProfileName").val("TestProfile").attr("clusterProfileNameId", "123").trigger("keyup");

        setTimeout(() => {
            assert.equal($("#clusterProfileNameError").text(), "", "No error shown");
            done();
        }, 100);
    });

    QUnit.test("Cluster server dropdown change triggers validation", assert => {
        $("#clusterServerName").append(`<option value="1" clusterServerNames="server1">server1</option>`).val("1").trigger("change");

        assert.equal($("#clusterServerNameError").text(), "", "Dropdown validation passed");
    });
});


QUnit.test("clusterNameValidations calls IsNameExist and passes if name is unique", async assert => {
    const randomName = "Test_" + Math.floor(Math.random() * 10000); // Random dynamic name
    const mockErrorElement = $("<span></span>");

    // Mock Ajax
    window.getAysncWithHandler = async (url, data, errorFunc) => {
        assert.ok(url.includes("IsVeritasClusterNameExist"), "Correct URL used in IsNameExist");
        assert.equal(data.name, randomName, "Correct name passed to IsNameExist");
        return false;
    };

    // Mock Ajax
    window.CommonValidation = async (el, results) => {
        assert.ok(Array.isArray(results), "Validation result is an array");
        return results.every(x => x === true);
    };

    const result = await clusterNameValidations(
        randomName,
        mockErrorElement,
        "Enter cluster name",
        true, // includeNameExistCheck
        "mock-id-001"
    );

    assert.ok(result, "Validation should pass when name does not exist");
    assert.strictEqual(mockErrorElement.text(), "", "No error message should be shown");
});

function generateTestNames(count) {
    const names = [];
    for (let i = 0; i < count; i++) {
        let name = "Cluster_" + Math.floor(Math.random() * 10000);
        // Insert `<` occasionally
        if (i % 10 === 0) {
            const insertIndex = Math.floor(Math.random() * name.length);
            name = name.slice(0, insertIndex) + "<" + name.slice(insertIndex);
        }
        names.push(name);
    }
    return names;
}

QUnit.test("clusterNameValidations handles 250 random inputs with occasional special chars", async assert => {
    const names = generateTestNames(250);
    const failedNames = [];

    // Mock getAysncWithHandler always return false (name doesn't exist)
    window.getAysncWithHandler = async () => false;

    // Ensure CommonValidation only returns true if no error triggered
    window.CommonValidation = async (el, results) => results.every(x => x === true);

    for (let name of names) {
        const $el = $("<span></span>");
        const result = await clusterNameValidations(name, $el, "Enter name", true, "some-id");

        // Any input with '<' should fail, all others should pass
        const expected = name.includes('<') ? false : true;
        if (result !== expected) {
            failedNames.push({ name, expected, got: result });
        }
    }

    assert.equal(failedNames.length, 0, `All ${names.length} names validated as expected`);
});

QUnit.test("Veritas Cluster Module: Validate cluster name random input - Show exact failed validation rules", async assert => {
    const done = assert.async();
    const total = 250;
    let completed = 0;

    // Simulate IsNameExist alternating true/false
    let callCount = 0;
    window.IsNameExist = async () => (++callCount % 2 === 0) ? "Name already exists" : true;

    // Generate randomized names with edge cases
    function generateEdgeCaseClusterName() {
        const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        let name = "";
        const length = Math.floor(Math.random() * 48) + 3;

        for (let i = 0; i < length; i++) {
            name += chars.charAt(Math.floor(Math.random() * chars.length));
        }

        if (Math.random() < 0.2) name = "_" + name;
        if (Math.random() < 0.2) name = "  " + name;
        if (Math.random() < 0.2) name += "--";
        if (Math.random() < 0.2) name += "..";
        if (Math.random() < 0.2) name += "_ _";
        if (Math.random() < 0.2) name += "_\t";
        if (Math.random() < 0.2) name = name.replace(/\s/g, "  ");
        if (Math.random() < 0.2) name += "@";
        if (Math.random() < 0.2) name = "<" + name;

        return name.trim();
    }

    // Run individual cluster name validation rules (reuse your real validation functions here)
    function runAllClusterValidationRules(value) {
        const results = [
            SpecialCharValidate(value), ShouldNotBeginWithSpace(value), ShouldNotBeginWithUnderScore(value),
            OnlyNumericsValidate(value), ShouldNotBeginWithNumber(value), ShouldNotEndWithSpace(value),
            ShouldNotAllowMultipleSpace(value), SpaceWithUnderScore(value), ShouldNotEndWithUnderScore(value),
            MultiUnderScoreRegex(value), SpaceAndUnderScoreRegex(value), minMaxlength(value), secondChar(value)
        ];
        return results.filter(r => r !== true);
    }

    for (let i = 0; i < total; i++) {
        const clusterName = (i === 0) ? "" : (i === 1) ? generateEdgeCaseClusterName().substring(0, 130) : generateEdgeCaseClusterName();
        const $error = $('<div></div>');

        try {
            const result = await clusterNameValidations(clusterName, $error, "Field is required", true, "");
            const failedRules = runAllClusterValidationRules(clusterName);

            if (result === true || result === undefined) {
                assert.ok(true, `✅ Passed: "${clusterName}"`);
            } else {
                const errorText = failedRules.length > 0
                    ? failedRules.join(" | ")
                    : $error.text().trim() || "Unknown error";
                assert.ok(true, `❌ Failed: "${clusterName}" → ${errorText}`);
            }

        } catch (err) {
            assert.notOk(true, `❌ Exception for "${clusterName}": ${err.message}`);
        } finally {
            completed++;
            if (completed === total) done();
        }
    }
});
