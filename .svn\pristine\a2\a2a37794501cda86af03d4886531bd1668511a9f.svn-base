﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class SingleSignOnRepository : BaseRepository<SingleSignOn>, ISingleSignOnRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public SingleSignOnRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;

        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<SingleSignOn>> ListAllAsync()
    {
        var singleSignOn = base.QueryAll(sso =>
            sso.CompanyId.Equals(_loggedInUserService.CompanyId));

          var singleSignOns =  MapSingleSignOns(singleSignOn);
           
        return await singleSignOns.ToListAsync();

    }

    public async Task<List<SingleSignOn>> GetType(string typeId)
    {
        var singleSignOn = SelectSingleSignOn(_loggedInUserService.IsParent
        ? base.FilterBy(x => x.SignOnTypeId.Equals(typeId)) 
        : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.SignOnTypeId.Equals(typeId)));

        var singleSignOns = MapSingleSignOns(singleSignOn);

        return await singleSignOns.ToListAsync();
    }

   
    public override async Task<SingleSignOn> GetByReferenceIdAsync(string id)
    {
        var singleSignOn = base.GetByReferenceId(id,
            sso => sso.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                    sso.ReferenceId.Equals(id));

        var singleSignOns = MapSingleSignOns(singleSignOn);

        return await singleSignOns.FirstOrDefaultAsync();
    }

    public async Task<List<SingleSignOn>> GetSingleSignOnNames()
    {
        if (!_loggedInUserService.IsParent)
        {
            return await _dbContext.SingleSignOns
                .Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new SingleSignOn
                {
                    ReferenceId = x.ReferenceId,
                    ProfileName = x.ProfileName
                })
                .OrderBy(x => x.ProfileName)
                .ToListAsync();
        }

        return await _dbContext.SingleSignOns
            .Active()
            .Select(x => new SingleSignOn
            {
                ReferenceId = x.ReferenceId,
                ProfileName = x.ProfileName
            })
            .OrderBy(x => x.ProfileName)
            .ToListAsync();
    }

    public override async Task<PaginatedResult<SingleSignOn>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<SingleSignOn> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await SelectSingleSignOn(_loggedInUserService.IsParent
           ? MapSingleSignOns(Entities.Specify(productFilterSpec).DescOrderById())
           : MapSingleSignOns(Entities.Specify(productFilterSpec).DescOrderById()))
          .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<SingleSignOn> GetPaginatedQuery()
    {
        var singleSignOn = base.QueryAll(sso =>
            sso.CompanyId.Equals(_loggedInUserService.CompanyId));

        var singleSignOns = MapSingleSignOns(singleSignOn);

        return singleSignOns.AsNoTracking().OrderByDescending(x => x.Id);
    }

    public async Task<bool> IsProfileNameExist(string profileName, string id)
    {
        return !id.IsValidGuid()
            ? await _dbContext.SingleSignOns.AnyAsync(e => e.ProfileName.Equals(profileName))
            : (await _dbContext.SingleSignOns.Where(e => e.ProfileName.Equals(profileName)).ToListAsync()).Unique(id);
    }

    public async Task<bool> IsProfileNameUnique(string profileName)
    {
        return await _dbContext.SingleSignOns.AnyAsync(e => e.ProfileName.Equals(profileName));
    }

    public async Task<PaginatedResult<SingleSignOn>> GetSingleSignOnByType(string typeId, int pageNumber, int pageSize, Specification<SingleSignOn> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await SelectSingleSignOn(_loggedInUserService.IsParent
            ? MapSingleSignOns(Entities.Specify(productFilterSpec).Where(x => x.SignOnTypeId.Equals(typeId)).DescOrderById())
            : MapSingleSignOns(Entities.Specify(productFilterSpec).Where(x=>x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.SignOnTypeId.Equals(typeId)).DescOrderById()))
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public IQueryable<SingleSignOn> GetSingleSignOnByType(string typeId)
    {
        var singleSignOn = SelectSingleSignOn(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.SignOnTypeId.Equals(typeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.SignOnTypeId.Equals(typeId)));

       var singleSignOns =  MapSingleSignOns(singleSignOn);

        return singleSignOns.AsNoTracking().OrderByDescending(x => x.Id);
    }

    //public async Task<List<SingleSignOn>> GetSingleSignOnByBusinessServiceId(string businessServiceId)
    //{
    //    var singleSignOn = _loggedInUserService.IsParent
    //        ? base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId))
    //        : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.BusinessServiceId.Equals(businessServiceId));

    //    var singleSignOns = MapSingleSignOns(singleSignOn);

    //   return await singleSignOns.ToListAsync();    
    //}

    public async Task<List<SingleSignOn>> GetSingleSignOnByTypeIds(List<string> typeIds)
    {
        return await (_loggedInUserService.IsParent
            ? Entities.Where(x => typeIds.Contains(x.SignOnTypeId))
            : Entities.Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && typeIds.Contains(x.SignOnTypeId))).DescOrderById()
            .Select(x => new SingleSignOn
            {
                Id = x.Id,
                ReferenceId = x.ReferenceId,
                CompanyId = x.CompanyId,
                SignOnType = x.SignOnType,
                SignOnTypeId = x.SignOnTypeId,
                ProfileName = x.ProfileName,

            }).ToListAsync();
    }

  
    public override async Task<SingleSignOn> GetByIdAsync(int id)
    {
        return _loggedInUserService.IsParent
            ? await base.GetByIdAsync(id)
            : (await FindByFilterAsync(singleSignOn =>
                    singleSignOn.Id.Equals(id) && singleSignOn.CompanyId.Equals(_loggedInUserService.CompanyId)))
                .SingleOrDefault();
    }


    private IQueryable<SingleSignOn> MapSingleSignOns(IQueryable<SingleSignOn> singleSignOns)
    {
        return singleSignOns
        .Select(sso => new
        {
            SingleSignOn = sso,
            ComponentType = _dbContext.ComponentTypes.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId == sso.SignOnTypeId)

        })
        .Select(result => new SingleSignOn
        {
            Id = result!.SingleSignOn!.Id,
            ReferenceId = result!.SingleSignOn!.ReferenceId,
            CompanyId = result!.SingleSignOn!.CompanyId,
            ProfileName = result!.SingleSignOn!.ProfileName,
            Properties = result!.SingleSignOn!.Properties,
            SignOnTypeId = result!.SingleSignOn!.SignOnTypeId,
            SignOnType = result!.SingleSignOn!.SignOnType,
            FormVersion = result!.SingleSignOn!.FormVersion,
            //SignOnType = result.ComponentType.Properties != null ? GetJsonProperties.GetJsonValue(result.ComponentType.Properties, "name") : null,
            IsActive = result!.SingleSignOn!.IsActive,
            CreatedBy = result!.SingleSignOn!.CreatedBy,
            CreatedDate = result!.SingleSignOn!.CreatedDate,
            LastModifiedBy = result.SingleSignOn!.LastModifiedBy,
            LastModifiedDate = result!.SingleSignOn!.LastModifiedDate
        });
    }
    private static IQueryable<SingleSignOn> SelectSingleSignOn(IQueryable<SingleSignOn> query)
    {
        return query.Select(x => new SingleSignOn
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            CompanyId = x.CompanyId,
            SignOnType = x.SignOnType,
            SignOnTypeId = x.SignOnTypeId,
            ProfileName = x.ProfileName,
            Properties = x.Properties,
            FormVersion = x.FormVersion
        });
    }
}