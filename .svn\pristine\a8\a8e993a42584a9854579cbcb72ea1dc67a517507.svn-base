using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixUsersModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class ApprovalMatrixUsersFixture
{
    public List<ApprovalMatrixUsersListVm> ApprovalMatrixUsersListVm { get; }
    public ApprovalMatrixUsersDetailVm ApprovalMatrixUsersDetailVm { get; }
    public CreateApprovalMatrixUsersCommand CreateApprovalMatrixUsersCommand { get; }
    public UpdateApprovalMatrixUsersCommand UpdateApprovalMatrixUsersCommand { get; }

    public ApprovalMatrixUsersFixture()
    {
        var fixture = new Fixture();

        // Create sample ApprovalMatrixUsers list data
        ApprovalMatrixUsersListVm = new List<ApprovalMatrixUsersListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UserId = Guid.NewGuid().ToString(),
                UserName = "john.doe",
                Email = "<EMAIL>",
                MobileNumber = "******-0123",
                BusinessServiceProperties = "{\"department\":\"Finance\",\"role\":\"Manager\",\"clearanceLevel\":\"Level3\"}",
                UserType = "Manager",
                AcceptType = "Approver",
                IsLink = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UserId = Guid.NewGuid().ToString(),
                UserName = "jane.smith",
                Email = "<EMAIL>",
                MobileNumber = "******-0456",
                BusinessServiceProperties = "{\"department\":\"Security\",\"role\":\"Director\",\"clearanceLevel\":\"Level4\"}",
                UserType = "Director",
                AcceptType = "Final Approver",
                IsLink = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UserId = Guid.NewGuid().ToString(),
                UserName = "bob.wilson",
                Email = "<EMAIL>",
                MobileNumber = "******-0789",
                BusinessServiceProperties = "{\"department\":\"HR\",\"role\":\"Specialist\",\"clearanceLevel\":\"Level2\"}",
                UserType = "Specialist",
                AcceptType = "Reviewer",
                IsLink = false
            }
        };

        // Create detailed ApprovalMatrixUsers data
        ApprovalMatrixUsersDetailVm = new ApprovalMatrixUsersDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            UserId = Guid.NewGuid().ToString(),
            UserName = "admin.user",
            Email = "<EMAIL>",
            MobileNumber = "******-0001",
            BusinessServiceProperties = "{\"department\":\"Administration\",\"role\":\"Administrator\",\"clearanceLevel\":\"Level5\",\"permissions\":[\"approve_all\",\"manage_users\",\"system_admin\"]}",
            UserType = "Administrator",
            AcceptType = "Super Approver",
            IsLink = true
        };

        // Create command for creating ApprovalMatrixUsers
        CreateApprovalMatrixUsersCommand = new CreateApprovalMatrixUsersCommand
        {
            ApprovalMatrixUsers = new List<CreateApprovalMatrixUsersCommandList>
            {
                new()
                {
                    UserId = Guid.NewGuid().ToString(),
                    UserName = "new.user1",
                    Email = "<EMAIL>",
                    MobileNumber = "******-1001",
                    BusinessServiceProperties = "{\"department\":\"IT\",\"role\":\"Analyst\",\"clearanceLevel\":\"Level2\"}",
                    UserType = "Analyst",
                    AcceptType = "Reviewer",
                    IsLink = true
                },
                new()
                {
                    UserId = Guid.NewGuid().ToString(),
                    UserName = "new.user2",
                    Email = "<EMAIL>",
                    MobileNumber = "******-1002",
                    BusinessServiceProperties = "{\"department\":\"Operations\",\"role\":\"Manager\",\"clearanceLevel\":\"Level3\"}",
                    UserType = "Manager",
                    AcceptType = "Approver",
                    IsLink = true
                }
            }
        };

        // Create command for updating ApprovalMatrixUsers
        UpdateApprovalMatrixUsersCommand = new UpdateApprovalMatrixUsersCommand
        {
            Id = Guid.NewGuid().ToString(),
            UserId = Guid.NewGuid().ToString(),
            UserName = "updated.user",
            Email = "<EMAIL>",
            MobileNumber = "******-9999",
            BusinessServiceProperties = "{\"department\":\"Updated Department\",\"role\":\"Updated Role\",\"clearanceLevel\":\"Level4\"}",
            UserType = "Updated Type",
            AcceptType = "Updated Approver",
            IsLink = false
        };
    }
}
