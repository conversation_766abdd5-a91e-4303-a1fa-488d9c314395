using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ServerTypeRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ServerTypeRepository _repository;
    private readonly ServerTypeFixture _fixture;

    public ServerTypeRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new ServerTypeRepository(_dbContext);
        _fixture = new ServerTypeFixture();
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnActiveServerTypes()
    {
        // Arrange
        await ClearDatabase();

        var serverType1 = _fixture.CreateServerType(name: "Web Server", isActive: true);
        var serverType2 = _fixture.CreateServerType(name: "Database Server", isActive: true);
        var serverType3 = _fixture.CreateServerType(name: "Inactive Server", isActive: false);

        await _dbContext.ServerTypes.AddAsync(serverType1);
        _dbContext.SaveChanges();
        await _dbContext.ServerTypes.AddAsync(serverType2);
        _dbContext.SaveChanges();
        await _dbContext.ServerTypes.AddAsync(serverType3);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.Name == "Web Server");
        Assert.Contains(result, s => s.Name == "Database Server");
        Assert.DoesNotContain(result, s => s.Name == "Inactive Server");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoActiveServerTypes()
    {
        // Arrange
        await ClearDatabase();

        var serverType = _fixture.CreateServerType(name: "Inactive Server", isActive: false);
        await _dbContext.ServerTypes.AddAsync(serverType);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnProjectedFields()
    {
        // Arrange
        await ClearDatabase();

        var serverType = _fixture.CreateServerType(name: "Test Server", isActive: true);
        await _repository.AddAsync(serverType);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var serverTypeResult = result.First();
        Assert.NotNull(serverTypeResult.ReferenceId);
        Assert.Equal("Test Server", serverTypeResult.Name);
        Assert.NotNull(serverTypeResult.Logo);
        Assert.True(serverTypeResult.Id > 0);
    }

    #endregion

    #region GetServerTypeById Tests

    [Fact]
    public async Task GetServerTypeById_ShouldReturnServerType_WhenIdExists()
    {
        // Arrange
        await ClearDatabase();

        var serverType = _fixture.CreateServerType(name: "Test Server", isActive: true);
        await _repository.AddAsync(serverType);

        // Act
        var result = await _repository.GetServerTypeById(serverType.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(serverType.ReferenceId, result.ReferenceId);
        Assert.Equal("Test Server", result.Name);
    }

    [Fact]
    public async Task GetServerTypeById_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetServerTypeById(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetServerTypeById_ShouldReturnNull_WhenServerTypeIsInactive()
    {
        // Arrange
        await ClearDatabase();

        var serverType = _fixture.CreateServerType(name: "Inactive Server", isActive: false);

        await _dbContext.ServerTypes.AddAsync(serverType);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetServerTypeById(serverType.ReferenceId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region IsServerTypeNameExist Tests

    [Fact]
    public async Task IsServerTypeNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsNotValidGuid()
    {
        // Arrange
        await ClearDatabase();

        var serverType = _fixture.CreateServerType(name: "Existing Server");
        await _repository.AddAsync(serverType);

        // Act
        var result = await _repository.IsServerTypeNameExist("Existing Server", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsServerTypeNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsNotValidGuid()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsServerTypeNameExist("Non-existing Server", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsServerTypeNameExist_ShouldReturnFalse_WhenNameExistsButIdMatches()
    {
        // Arrange
        await ClearDatabase();

        var serverType = _fixture.CreateServerType(name: "Test Server");
        await _repository.AddAsync(serverType);

        // Act
        var result = await _repository.IsServerTypeNameExist("Test Server", serverType.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsServerTypeNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsDifferent()
    {
        // Arrange
        await ClearDatabase();

        var serverType = _fixture.CreateServerType(name: "Test Server");
        await _repository.AddAsync(serverType);

        // Act
        var result = await _repository.IsServerTypeNameExist("Test Server", Guid.NewGuid().ToString());

        // Assert
        Assert.True(result);
    }

    #endregion

    #region IsServerTypeNameUnique Tests

    [Fact]
    public async Task IsServerTypeNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();

        var serverType = _fixture.CreateServerType(name: "Existing Server");
        await _repository.AddAsync(serverType);

        // Act
        var result = await _repository.IsServerTypeNameUnique("Existing Server");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsServerTypeNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsServerTypeNameUnique("Non-existing Server");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsServerTypeNameUnique_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();

        var serverType = _fixture.CreateServerType(name: "Test Server");
        await _repository.AddAsync(serverType);

        // Act
        var result = await _repository.IsServerTypeNameUnique("test server");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetServerTypeListByName Tests

    [Fact]
    public async Task GetServerTypeListByName_ShouldReturnMatchingServerTypes_CaseInsensitive()
    {
        // Arrange
        await ClearDatabase();

        var serverType1 = _fixture.CreateServerType(name: "Web Server", isActive: true);
        var serverType2 = _fixture.CreateServerType(name: "WEB SERVER", isActive: true);
        var serverType3 = _fixture.CreateServerType(name: "Database Server", isActive: true);

        await _repository.AddAsync(serverType1);
        await _repository.AddAsync(serverType2);
        await _repository.AddAsync(serverType3);

        // Act
        var result = await _repository.GetServerTypeListByName("web server");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.Name == "Web Server");
        Assert.Contains(result, s => s.Name == "WEB SERVER");
        Assert.DoesNotContain(result, s => s.Name == "Database Server");
    }

    [Fact]
    public async Task GetServerTypeListByName_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();

        var serverType = _fixture.CreateServerType(name: "Web Server", isActive: true);
        await _repository.AddAsync(serverType);

        // Act
        var result = await _repository.GetServerTypeListByName("Database Server");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetServerTypeListByName_ShouldOnlyReturnActiveServerTypes()
    {
        // Arrange
        await ClearDatabase();

        var serverType1 = _fixture.CreateServerType(name: "Web Server", isActive: true);
        var serverType2 = _fixture.CreateServerType(name: "Web Server", isActive: false);
        await _dbContext.ServerTypes.AddAsync(serverType1);
        _dbContext.SaveChanges();
        await _dbContext.ServerTypes.AddAsync(serverType2);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetServerTypeListByName("Web Server");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(serverType1.ReferenceId, result[0].ReferenceId);
    }

    [Fact]
    public async Task GetServerTypeListByName_ShouldTrimWhitespace()
    {
        // Arrange
        await ClearDatabase();

        var serverType = _fixture.CreateServerType(name: "  Web Server  ", isActive: true);
        await _repository.AddAsync(serverType);

        // Act
        var result = await _repository.GetServerTypeListByName("  web server  ");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(serverType.ReferenceId, result[0].ReferenceId);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.ServerTypes.RemoveRange(_dbContext.ServerTypes);
        await _dbContext.SaveChangesAsync();
    }
}
