using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SolutionTypeTablesFixture : IDisposable
{
    public List<SolutionTypeTables> SolutionTypeTablesPaginationList { get; set; }
    public List<SolutionTypeTables> SolutionTypeTablesList { get; set; }
    public SolutionTypeTables SolutionTypeTablesDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SolutionTypeTablesFixture()
    {
        var fixture = new Fixture();

        SolutionTypeTablesList = fixture.Create<List<SolutionTypeTables>>();

        SolutionTypeTablesPaginationList = fixture.CreateMany<SolutionTypeTables>(20).ToList();

        SolutionTypeTablesDto = fixture.Create<SolutionTypeTables>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public SolutionTypeTables CreateSolutionTypeTables(
        string monitoringType = "Default Monitoring",
        string solutionType = "Default Solution",
        string monitorTableNames = "DefaultMonitorTable",
        bool isActive = true,
        bool isDelete = false)
    {
        return new SolutionTypeTables
        {
            ReferenceId = Guid.NewGuid().ToString(),
            MonitoringType = monitoringType,
            SolutionType = solutionType,
            MonitorTableNames = monitorTableNames,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<SolutionTypeTables> CreateMultipleSolutionTypeTables(int count)
    {
        var solutionTypeTables = new List<SolutionTypeTables>();
        for (int i = 1; i <= count; i++)
        {
            solutionTypeTables.Add(CreateSolutionTypeTables(
                monitoringType: $"Monitoring Type {i}",
                solutionType: $"Solution Type {i}",
                monitorTableNames: $"MonitorTable{i},PerformanceTable{i}"
            ));
        }
        return solutionTypeTables;
    }

    public SolutionTypeTables CreateSolutionTypeTablesWithSpecificId(string referenceId, string solutionType = "Test Solution")
    {
        return new SolutionTypeTables
        {
            ReferenceId = referenceId,
            MonitoringType = "Test Monitoring",
            SolutionType = solutionType,
            MonitorTableNames = "TestMonitorTable",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public SolutionTypeTables CreateSolutionTypeTablesForMonitoringType(string monitoringType, string solutionType = null)
    {
        return CreateSolutionTypeTables(
            monitoringType: monitoringType,
            solutionType: solutionType ?? $"Solution for {monitoringType}",
            monitorTableNames: $"{monitoringType}MonitorTable"
        );
    }

    public List<SolutionTypeTables> CreateSolutionTypeTablesWithStatus(int activeCount, int inactiveCount)
    {
        var solutionTypeTables = new List<SolutionTypeTables>();

        for (int i = 1; i <= activeCount; i++)
        {
            solutionTypeTables.Add(CreateSolutionTypeTables(
                monitoringType: $"Active Monitoring {i}",
                solutionType: $"Active Solution {i}",
                monitorTableNames: $"ActiveMonitorTable{i}",
                isActive: true
            ));
        }

        for (int i = 1; i <= inactiveCount; i++)
        {
            solutionTypeTables.Add(CreateSolutionTypeTables(
                monitoringType: $"Inactive Monitoring {i}",
                solutionType: $"Inactive Solution {i}",
                monitorTableNames: $"InactiveMonitorTable{i}",
                isActive: false
            ));
        }

        return solutionTypeTables;
    }

    public SolutionTypeTables CreateDatabaseSolutionTypeTables(string databaseType = "SQL Server")
    {
        return CreateSolutionTypeTables(
            monitoringType: "Database",
            solutionType: databaseType,
            monitorTableNames: $"{databaseType.Replace(" ", "")}Monitor,{databaseType.Replace(" ", "")}Performance"
        );
    }

    public SolutionTypeTables CreateApplicationSolutionTypeTables(string applicationType = "Web Server")
    {
        return CreateSolutionTypeTables(
            monitoringType: "Application",
            solutionType: applicationType,
            monitorTableNames: $"{applicationType.Replace(" ", "")}Monitor,{applicationType.Replace(" ", "")}Health"
        );
    }

    public SolutionTypeTables CreateInfrastructureSolutionTypeTables(string infrastructureType = "Windows Server")
    {
        return CreateSolutionTypeTables(
            monitoringType: "Infrastructure",
            solutionType: infrastructureType,
            monitorTableNames: $"{infrastructureType.Replace(" ", "")}Monitor,{infrastructureType.Replace(" ", "")}Resources"
        );
    }

    public List<SolutionTypeTables> CreateStandardSolutionTypeTables()
    {
        return new List<SolutionTypeTables>
        {
            CreateDatabaseSolutionTypeTables("SQL Server"),
            CreateDatabaseSolutionTypeTables("MySQL"),
            CreateDatabaseSolutionTypeTables("Oracle"),
            CreateApplicationSolutionTypeTables("Web Server"),
            CreateApplicationSolutionTypeTables("Application Server"),
            CreateInfrastructureSolutionTypeTables("Windows Server"),
            CreateInfrastructureSolutionTypeTables("Linux Server")
        };
    }

    public SolutionTypeTables CreateMinimalSolutionTypeTables()
    {
        return new SolutionTypeTables
        {
            ReferenceId = Guid.NewGuid().ToString(),
            SolutionType = "Minimal Solution",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public SolutionTypeTables CreateSolutionTypeTablesForTesting(
        string testName,
        string solutionType = null,
        string monitoringType = null)
    {
        return CreateSolutionTypeTables(
            monitoringType: monitoringType ?? $"Test Monitoring for {testName}",
            solutionType: solutionType ?? $"Test Solution for {testName}",
            monitorTableNames: $"{testName}MonitorTable,{testName}PerformanceTable"
        );
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
