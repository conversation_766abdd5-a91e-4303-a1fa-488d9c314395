﻿using ContinuityPatrol.Application.Features.ReportSchedule.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.ReportScheduleModel;

namespace ContinuityPatrol.Application.UnitTests.Features.ReportSchedule.Queries
{
    public class GetReportScheduleListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IReportScheduleRepository> _mockReportScheduleRepository;
        private readonly GetReportScheduleListQueryHandler _handler;

        public GetReportScheduleListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockReportScheduleRepository = new Mock<IReportScheduleRepository>();
            _handler = new GetReportScheduleListQueryHandler(
                _mockMapper.Object,
                _mockReportScheduleRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnListOfReportScheduleListVm_WhenReportSchedulesExist()
        {
            var query = new GetReportScheduleListQuery();
            var reportSchedules = new List<Domain.Entities.ReportSchedule>
            {
                new Domain.Entities.ReportSchedule
                {
                    Id = 1,
                    ReportName = "Daily Report",
                },
                new Domain.Entities.ReportSchedule
                {
                    Id = 2,
                    ReportName = "Weekly Report",
                }
            };

            var reportScheduleListVm = new List<ReportScheduleListVm>
            {
                new ReportScheduleListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportName = "Daily Report"
                },
                new ReportScheduleListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportName = "Weekly Report"
                }
            };

            _mockReportScheduleRepository.Setup(r => r.ListAllAsync())
                .ReturnsAsync(reportSchedules);

            _mockMapper.Setup(m => m.Map<List<ReportScheduleListVm>>(reportSchedules))
                .Returns(reportScheduleListVm);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal(reportScheduleListVm[0].Id, result[0].Id);
            Assert.Equal(reportScheduleListVm[1].Id, result[1].Id);

            _mockReportScheduleRepository.Verify(r => r.ListAllAsync(), Times.Once);

            _mockMapper.Verify(m => m.Map<List<ReportScheduleListVm>>(reportSchedules), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoReportSchedulesExist()
        {
            var query = new GetReportScheduleListQuery();
            var reportSchedules = new List<Domain.Entities.ReportSchedule>();

            _mockReportScheduleRepository.Setup(r => r.ListAllAsync())
                .ReturnsAsync(reportSchedules);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);

            _mockReportScheduleRepository.Verify(r => r.ListAllAsync(), Times.Once);

            _mockMapper.Verify(m => m.Map<List<ReportScheduleListVm>>(It.IsAny<List<Domain.Entities.ReportSchedule>>()), Times.Never);
        }
    }
}
