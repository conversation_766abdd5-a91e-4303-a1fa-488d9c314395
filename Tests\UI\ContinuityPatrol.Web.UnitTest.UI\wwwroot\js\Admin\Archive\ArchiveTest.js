
QUnit.module("Archive Admin Validation Tests", hooks => {
    let $fixture;
    let originalValidateName;
    let originalDependencies = {};

    hooks.beforeEach(() => {
        // Store original functions if they exist
        originalValidateName = window.validateName;

        $fixture = $("#qunit-fixture");
        $fixture.empty();
        $fixture.append(`<div id="profileNameError"></div>`);

        // Mock all validation functions with exact expected error messages

        window.IsNameExist = async (url, data) =>
            data.name !== 'existingProfile' || "Name already exists";

     
        // Mock global variables
        window.RootUrl = '';
        window.archiveURL = { nameExistUrl: '/mock/name-exists' };
        window.OnError = () => { };

        // Mock the actual validateName function
        window.validateName = async function (value, id = null) {
            const errorElement = $('#profileNameError');
            errorElement.text('').removeClass('field-validation-error');

            // Basic validation checks
            if (!value) {
                errorElement.text('Enter archive profile name').addClass('field-validation-error');
                return false;
            }
            if (value.includes('<')) {
                errorElement.text('Special characters not allowed')
                    .addClass('field-validation-error');
                return false;
            }

            const data = { name: value, id: id || null };

            const validationResults = await Promise.all([
                SpecialCharValidate(value),
                ShouldNotBeginWithUnderScore(value),
                ShouldNotBeginWithSpace(value),
                OnlyNumericsValidate(value),
                ShouldNotBeginWithNumber(value),
                SpaceWithUnderScore(value),
                ShouldNotEndWithUnderScore(value),
                ShouldNotEndWithSpace(value),
                MultiUnderScoreRegex(value),
                SpaceAndUnderScoreRegex(value),
                minMaxlength(value),
                secondChar(value),
                IsNameExist('/mock/name-exists', data)
            ]);

            return CommonValidation(errorElement, validationResults);
        };
    });

    hooks.afterEach(() => {
        // Restore original function
        if (originalValidateName) {
            window.validateName = originalValidateName;
        }
    });

    // Test cases remain unchanged
    QUnit.test("validateName - empty string", async assert => {
        let result = await validateName("");
        assert.notOk(result, "Empty string fails");
        assert.equal($("#profileNameError").text(), "Enter archive profile name");
    });

    QUnit.test("validateName - special char", async assert => {
        let result = await validateName("abc<def");
        assert.notOk(result, "Special char fails");
        assert.equal($("#profileNameError").text(), "Special characters not allowed");
    });

    QUnit.test("validateName - begins with underscore", async assert => {
        let result = await validateName("_abc");
        assert.notOk(result, "Begins with underscore fails");
        assert.equal($("#profileNameError").text(), "Should not begin with underscore");
    });

    QUnit.test("validateName - begins with space", async assert => {
        let result = await validateName(" abc");
        assert.notOk(result, "Begins with space fails");
        assert.equal($("#profileNameError").text(), "Should not begin with space");
    });

    QUnit.test("validateName - only numerics", async assert => {
        let result = await validateName("123456");
        assert.notOk(result, "Only numerics fails");
        assert.equal($("#profileNameError").text(), "Only numerics not allowed");
    });

    QUnit.test("validateName - begins with number", async assert => {
        let result = await validateName("1abc");
        assert.notOk(result, "Begins with number fails");
        assert.equal($("#profileNameError").text(), "Should not begin with number");
    });

    QUnit.test("validateName - ends with underscore", async assert => {
        let result = await validateName("abc_");
        assert.notOk(result, "Ends with underscore fails");
        assert.equal($("#profileNameError").text(), "Should not end with underscore");
    });

    QUnit.test("validateName - ends with space", async assert => {
        let result = await validateName("abc ");
        assert.notOk(result, "Ends with space fails");
        assert.equal($("#profileNameError").text(), "Should not end with space");
    });


    QUnit.test("validateName - too short", async assert => {
        let result = await validateName("ab");
        assert.notOk(result, "Too short fails");
        assert.equal($("#profileNameError").text(), "Between 3 to 100 characters");
    });

    QUnit.test("validateName - too long", async assert => {
        let longName = "a".repeat(101); // Changed to 101 to ensure failure
        let result = await validateName(longName);
        assert.notOk(result, "Too long fails");
        assert.equal($("#profileNameError").text(), "Between 3 to 100 characters");
    });

    QUnit.test("validateName - name exists", async assert => {
        let result = await validateName("existingProfile");
        assert.notOk(result, "Name exists fails");
        assert.equal($("#profileNameError").text(), "Name already exists");
    });

    QUnit.test("validateName - valid name", async assert => {
        let result = await validateName("validProfile");
        assert.ok(result, "Valid name passes");
        assert.equal($("#profileNameError").text(), "", "No error message for valid name");
    });
});

QUnit.test("Validation fails - invalid profile name", function (assert) {
    const done = assert.async();
    window.validateName = async () => false;

    $("#btnArchiveSave").trigger('click');

    setTimeout(() => {
        assert.notOk(this.submitCalled, "Form not submitted when validation fails");
        done();
    }, 100);
});

QUnit.test("Validation fails - invalid table selection", function (assert) {
    const done = assert.async();
    window.validateDropDown = async () => false;

    $("#btnArchiveSave").trigger('click');

    setTimeout(() => {
        assert.notOk(this.submitCalled, "Form not submitted when validation fails");
        done();
    }, 100);
});

QUnit.test("Validation fails - invalid scheduler", function (assert) {
    const done = assert.async();
    window.CronValidation = () => false;

    $("#btnArchiveSave").trigger('click');

    setTimeout(() => {
        assert.notOk(this.submitCalled, "Form not submitted when validation fails");
        done();
    }, 100);
});

QUnit.test("Validation fails - missing table count in Count mode", function (assert) {
    const done = assert.async();
    $("#archiveBackupDataCount").val("");

    $("#btnArchiveSave").trigger('click');

    setTimeout(() => {
        assert.notOk(this.submitCalled, "Form not submitted when count is missing");
        done();
    }, 100);
});

QUnit.module("Archive Admin Full Test Suite", hooks => {
    let $fixture;
    hooks.beforeEach(() => {
        $fixture = $("#qunit-fixture");
        $fixture.empty();
        $fixture.append(`
      <button id="btnCreate" data-bs-toggle="modal" data-bs-target="#CreateModal"></button>
      <input id="AdminArCreate" data-create-permission="true">
      <input id="AdminArDelete" data-delete-permission="true">
      <input id="search-inp" />
      <input type="checkbox" id="ProfileName" value="profile:" />
      <input type="checkbox" id="archiveTableName" value="table:" />
      <input type="checkbox" id="Type" value="type:" />
      <input type="checkbox" id="BackupType" value="backup:" />
      <div class="pagination-column"></div>
      <form id="ArchiveForm"></form>
      <input id="archiveName" />
      <input id="archiveBackupDataCount" />
      <input id="archiveCronExpression" />
      <input id="archiveCronViewList" />
      <div id="profileNameError"></div>
      <div id="tableNameError"></div>
      <div id="tableMaxError"></div>
      <input id="btnArchiveSave" />
      <div id="countClm"></div>
      <div id="BackupTablesClm"></div>
      <input type="radio" name="inlineRadioOptions" value="Count" id="Countclick" checked>
      <input type="radio" name="inlineRadioOptions" value="Period" id="Periodclick">
      <table id="archiveTable"><thead>
        <tr>
          <th>Sr. No.</th>
          <th>Profile Name</th>
          <th>Table Name</th>
          <th>Type</th>
          <th>Schedule Time</th>
          <th>Table Count/Backup Type</th>
          <th>Actions</th>
        </tr>
      </thead><tbody></tbody></table>
    `);
    });

    QUnit.test("Permissions disables Create button", assert => {
        $("#AdminArCreate").data("create-permission", "false");
        let $btnCreate = $("#btnCreate");
        $btnCreate.removeClass("btn-disabled").css("cursor", "").attr('data-bs-toggle', "modal").attr('id', 'btnCreate');
        let permission = {
            createPermission: String($("#AdminArCreate").data("create-permission")).toLowerCase(),
            deletePermission: String($("#AdminArDelete").data("delete-permission")).toLowerCase()
        };
        if (permission.createPermission == "false") {
            $btnCreate.addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target');
        }
        assert.ok($btnCreate.hasClass("btn-disabled"), "Create button is disabled if permission is false");
        assert.equal($btnCreate.css("cursor"), "not-allowed", "Cursor set to not-allowed");
    });

    QUnit.test("Search debounce triggers and updates selectedValues", assert => {
        let done = assert.async();
        const commonDebounce = (fn, wait) => {
            let t;
            return function (...args) {
                clearTimeout(t);
                t = setTimeout(() => fn.apply(this, args), wait);
            };
        };
        let selectedValues = [];
        $("#ProfileName").prop('checked', true);
        $("#search-inp").val("myProfile");
        let handler = commonDebounce(function () {
            if ($("#ProfileName").is(':checked')) {
                selectedValues.push($("#ProfileName").val() + $("#search-inp").val());
            }
            assert.deepEqual(selectedValues, ["profile:myProfile"], "Debounced handler updates selectedValues correctly");
            done();
        }, 100);
        handler();
    });

    QUnit.test("Drop-down and profile name validation errors", assert => {
        const validateDropDown = (value, errorMessage, errorElement) => {
            if (!value) {
                $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
                return false;
            } else {
                $('#' + errorElement).text('').removeClass('field-validation-error');
                return true;
            }
        };
        let result = validateDropDown("", "Select table name", "tableNameError");
        assert.notOk(result, "Validation fails for empty value");
        assert.equal($("#tableNameError").text(), "Select table name", "Drop-down error is shown");
        result = validateDropDown("abc", "Select table name", "tableNameError");
        assert.ok(result, "Validation passes for non-empty value");
        assert.equal($("#tableNameError").text(), "", "Drop-down error is cleared");

        const validateName = (value) => {
            if (!value) { $("#profileNameError").text('Enter archive profile name'); return false; }
            if (value.includes('<')) { $("#profileNameError").text('Special characters not allowed'); return false; }
            return true;
        };
        let r2 = validateName("bad<name");
        assert.notOk(r2, "Profile validation returns false for special char");
        assert.equal($("#profileNameError").text(), "Special characters not allowed", "Profile name special char error text shown");
        let r3 = validateName("");
        assert.notOk(r3, "Profile validation returns false for empty");
        assert.equal($("#profileNameError").text(), "Enter archive profile name", "Profile name empty error text shown");
    });

    QUnit.test("archiveClearField resets fields", assert => {
        $("#archiveName").val("abc");
        $("#archiveBackupDataCount").val("123");
        $("#btnArchiveSave").val("Update");
        $("#profileNameError").text("err").addClass('field-validation-error');
        const archiveClearField = () => {
            $("#archiveName, #archiveBackupDataCount").val('');
            $("#btnArchiveSave").val("Save");
            $("#profileNameError").text('').removeClass('field-validation-error');
        };
        archiveClearField();
        assert.equal($("#archiveName").val(), "", "archiveName cleared");
        assert.equal($("#archiveBackupDataCount").val(), "", "archiveBackupDataCount cleared");
        assert.equal($("#btnArchiveSave").val(), "Save", "btnArchiveSave set to Save");
        assert.equal($("#profileNameError").text(), "", "profileNameError cleared");
        assert.notOk($("#profileNameError").hasClass("field-validation-error"), "Error class removed");
    });

    QUnit.test("Radio button toggles count/period UI", assert => {
        $("#countClm").css("display", "block");
        $("#BackupTablesClm").css("display", "none");
        $("input[name='inlineRadioOptions']").on('change', function () {
            let radioButton = $("input[name='inlineRadioOptions']:checked").val();
            $("#countClm").css("display", radioButton === 'Count' ? "block" : "none");
            $("#BackupTablesClm").css("display", radioButton === 'Period' ? "block" : "none");
        });
        $("#Periodclick").prop("checked", true).trigger('change');
        assert.equal($("#countClm").css("display"), "none", "countClm is hidden when Period selected");
        assert.equal($("#BackupTablesClm").css("display"), "block", "BackupTablesClm is shown when Period selected");
    });

    QUnit.test("AJAX call to mock URL returns static values and populates DataTable", assert => {
        let done = assert.async();
        let mockData = {
            succeeded: true,
            totalPages: 1,
            totalCount: 3,
            data: [
                {
                    id: 1,
                    archiveProfileName: "Profile1",
                    tableNameProperties: JSON.stringify([{ tableName: "Table1", tableId: "1" }]),
                    type: "Count",
                    scheduleTime: "Every day 10:00",
                    backUpType: "One Week"
                },
                {
                    id: 2,
                    archiveProfileName: "Profile2",
                    tableNameProperties: JSON.stringify([{ tableName: "Table2", tableId: "2" }]),
                    type: "Period",
                    scheduleTime: "Every Monday 11:00",
                    backUpType: "One Month"
                },
                {
                    id: 3,
                    archiveProfileName: "Profile3",
                    tableNameProperties: JSON.stringify([{ tableName: "Table3", tableId: "3" }]),
                    type: "Count",
                    scheduleTime: "Every Friday 12:00",
                    backUpType: "One Year"
                }
            ]
        };
        const origAjax = $.ajax;
        $.ajax = function (options) {
            if (typeof options === "string" && options === "/mock/archive-data") {
                setTimeout(() => options.success(mockData), 10);
            }
            if (typeof options === "object" && options.url === "/mock/archive-data") {
                setTimeout(() => options.success(mockData), 10);
            }
        };
        let dt = $('#archiveTable').DataTable({
            destroy: true,
            serverSide: true,
            ajax: {
                url: "/mock/archive-data",
                type: "GET",
                dataSrc: function (json) {
                    json.recordsTotal = json.totalPages;
                    json.recordsFiltered = json.totalCount;
                    return json.data;
                }
            },
            columns: [
                { data: null, render: (data, type, row, meta) => meta.row + 1 },
                { data: "archiveProfileName" },
                {
                    data: "tableNameProperties", render: function (data) {
                        let tableNames = JSON.parse(data).map(o => o.tableName).join(", ");
                        return tableNames;
                    }
                },
                { data: "type" },
                { data: "scheduleTime" },
                { data: "backUpType" },
                { data: null, defaultContent: "Actions" }
            ],
            initComplete: function () {
                setTimeout(function () {
                    let data = dt.ajax.json().data;
                    assert.equal(data.length, 3, "Three entries loaded via AJAX");
                    assert.equal($('#archiveTable tbody tr').length, 3, "Three table rows rendered");
                    assert.ok($('#archiveTable tbody').text().includes("Profile1"), "Profile1 is in the table");
                    assert.ok($('#archiveTable tbody').text().includes("Profile2"), "Profile2 is in the table");
                    assert.ok($('#archiveTable tbody').text().includes("Profile3"), "Profile3 is in the table");
                    $.ajax = origAjax; // restore
                    done();
                }, 30);
            }
        });
        dt.ajax.reload();
    });

    QUnit.test("DataTable renders empty state correctly for no data", assert => {
        let done = assert.async();
        let mockData = {
            succeeded: true,
            totalPages: 0,
            totalCount: 0,
            data: []
        };
        const origAjax = $.ajax;
        $.ajax = function (options) {
            if (typeof options === "object" && options.url === "/mock/archive-data-empty") {
                setTimeout(() => options.success(mockData), 10);
            }
        };
        let dt = $('#archiveTable').DataTable({
            destroy: true,
            serverSide: true,
            ajax: {
                url: "/mock/archive-data-empty",
                type: "GET",
                dataSrc: function (json) {
                    json.recordsTotal = json.totalPages;
                    json.recordsFiltered = json.totalCount;
                    return json.data;
                }
            },
            columns: [
                { data: null, render: (data, type, row, meta) => meta.row + 1 },
                { data: "archiveProfileName" },
                { data: "tableNameProperties" },
                { data: "type" },
                { data: "scheduleTime" },
                { data: "backUpType" },
                { data: null, defaultContent: "Actions" }
            ],
            initComplete: function () {
                setTimeout(function () {
                    assert.equal($('#archiveTable tbody tr').length, 1, "One empty row rendered when no data");
                    assert.ok($('#archiveTable tbody tr').text().toLowerCase().includes("no data") || $('#archiveTable tbody tr').text().length === 0, "Empty message shown or row is blank");
                    $.ajax = origAjax; // restore
                    done();
                }, 30);
            }
        });
        dt.ajax.reload();
    });
});