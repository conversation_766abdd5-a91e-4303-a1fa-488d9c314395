﻿using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetSolutionHistoryByActionId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.SolutionHistory.Queries;

public class GetSolutionHistoryByActionIdQueryHandlerTests : IClassFixture<SolutionHistoryFixture> 
{
    private readonly SolutionHistoryFixture _solutionHistoryFixture;

    private Mock<ISolutionHistoryRepository> _mockSolutionHistoryRepository;

    private readonly GetSolutionHistoryByActionIdQueryHandler _handler; 

    public GetSolutionHistoryByActionIdQueryHandlerTests(SolutionHistoryFixture solutionHistoryFixture)
    {
        _solutionHistoryFixture = solutionHistoryFixture;

        _mockSolutionHistoryRepository = SolutionHistoryRepositoryMocks.GetSolutionHistoryByActionIdRepository(_solutionHistoryFixture.SolutionHistories);

        _handler = new GetSolutionHistoryByActionIdQueryHandler(_solutionHistoryFixture.Mapper, _mockSolutionHistoryRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_SolutionHistoriesCount()
    {
        var result = await _handler.Handle(new GetSolutionHistoryByActionIdQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<SolutionHistoryByActionIdQueryVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_Valid_SolutionHistoriesList()
    {
        var result = await _handler.Handle(new GetSolutionHistoryByActionIdQuery(), CancellationToken.None);
        result.ShouldBeOfType<List<SolutionHistoryByActionIdQueryVm>>();
        result[0].Id.ShouldBe(_solutionHistoryFixture.SolutionHistories[0].ReferenceId);
        result[0].CompanyId.ShouldBe(_solutionHistoryFixture.SolutionHistories[0].CompanyId);
        result[0].LoginName.ShouldBe(_solutionHistoryFixture.SolutionHistories[0].LoginName);
        result[0].NodeId.ShouldBe(_solutionHistoryFixture.SolutionHistories[0].NodeId);
        result[0].ActionId.ShouldBe(_solutionHistoryFixture.SolutionHistories[0].ActionId);
        result[0].ActionName.ShouldBe(_solutionHistoryFixture.SolutionHistories[0].ActionName);
        result[0].Properties.ShouldBe(_solutionHistoryFixture.SolutionHistories[0].Properties);
        result[0].Version.ShouldBe(_solutionHistoryFixture.SolutionHistories[0].Version);
        result[0].UpdaterId.ShouldBe(_solutionHistoryFixture.SolutionHistories[0].UpdaterId);
        result[0].Description.ShouldBe(_solutionHistoryFixture.SolutionHistories[0].Description);
        result[0].Comments.ShouldBe(_solutionHistoryFixture.SolutionHistories[0].Comments);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockSolutionHistoryRepository = SolutionHistoryRepositoryMocks.GetSolutionHistoryEmptyRepository();

        var handler = new GetSolutionHistoryByActionIdQueryHandler(_solutionHistoryFixture.Mapper, _mockSolutionHistoryRepository.Object);

        var result = await handler.Handle(new GetSolutionHistoryByActionIdQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetSolutionHistoryByActionIdQuery { ActionId = _solutionHistoryFixture.SolutionHistories[0].ActionId}, CancellationToken.None);

        _mockSolutionHistoryRepository.Verify(x => x.GetSolutionHistoryByActionId(It.IsAny<string>()), Times.Once);
    }
}