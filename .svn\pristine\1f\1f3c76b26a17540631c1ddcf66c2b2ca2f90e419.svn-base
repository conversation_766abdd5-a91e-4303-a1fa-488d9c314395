﻿namespace ContinuityPatrol.Application.Features.SVCMssqlMonitorLog.Queries.GetList;

public class
    GetSVCMssqlMonitorLogListQueryHandler : IRequestHandler<GetSVCMssqlMonitorLogListQuery,
        List<SVCMssqlMonitorLogListVm>>
{
    private readonly IMapper _mapper;
    private readonly ISVCMssqlMonitorLogRepository _svcMssqlMonitorLogRepository;

    public GetSVCMssqlMonitorLogListQueryHandler(ISVCMssqlMonitorLogRepository svcMssqlMonitorLogRepository,
        IMapper mapper)
    {
        _svcMssqlMonitorLogRepository = svcMssqlMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<List<SVCMssqlMonitorLogListVm>> Handle(GetSVCMssqlMonitorLogListQuery request,
        CancellationToken cancellationToken)
    {
        var svcMssqlMonitorLogList = await _svcMssqlMonitorLogRepository.ListAllAsync();

        return svcMssqlMonitorLogList.Count <= 0
            ? new List<SVCMssqlMonitorLogListVm>()
            : _mapper.Map<List<SVCMssqlMonitorLogListVm>>(svcMssqlMonitorLogList);
    }
}