﻿using ContinuityPatrol.Application.Features.ServerType.Commands.Create;
using ContinuityPatrol.Application.Features.ServerType.Commands.Update;
using ContinuityPatrol.Application.Features.ServerType.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ServerTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class ServerTypeProfile : Profile
{
    public ServerTypeProfile()
    {
        CreateMap<CreateServerTypeCommand, ServerTypeViewModel>().ReverseMap();
        CreateMap<UpdateServerTypeCommand, ServerTypeViewModel>().ReverseMap();
        CreateMap<CreateServerTypeCommand, ServerTypeListVm>().ReverseMap();
        CreateMap<UpdateServerTypeCommand, ServerTypeListVm>().ReverseMap();

        CreateMap<ServerType, CreateServerTypeCommand>().ReverseMap();
        CreateMap<UpdateServerTypeCommand, ServerType>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<ServerType, ServerTypeDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<ServerType, ServerTypeListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<ServerType, ServerTypeModel>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PaginatedResult<ServerType>,PaginatedResult<ServerTypeListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}