﻿using ContinuityPatrol.Application.Features.FormHistory.Events.Delete;

namespace ContinuityPatrol.Application.Features.FormHistory.Commands.Delete;

public class DeleteFormHistoryCommandHandler : IRequestHandler<DeleteFormHistoryCommand, DeleteFormHistoryResponse>
{
    private readonly IFormHistoryRepository _formHistoryRepository;
    private readonly IPublisher _publisher;

    public DeleteFormHistoryCommandHandler(IFormHistoryRepository formHistoryRepository, IPublisher publisher)
    {
        _formHistoryRepository = formHistoryRepository;
        _publisher = publisher;
    }

    public async Task<DeleteFormHistoryResponse> Handle(DeleteFormHistoryCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _formHistoryRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.FormHistory),
            new NotFoundException(nameof(Domain.Entities.FormHistory), request.Id));

        eventToDelete.IsActive = false;

        await _formHistoryRepository.UpdateAsync(eventToDelete);

        var response = new DeleteFormHistoryResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.FormHistory), eventToDelete.FormName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new FormHistoryDeletedEvent { FormName = eventToDelete.FormName }, cancellationToken);

        return response;
    }
}