﻿namespace ContinuityPatrol.Application.Features.Server.Queries.GetRoleType;

public class ServerRoleTypeVm
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string SiteId { get; set; }
    public string SiteName { get; set; }
    public string RoleTypeId { get; set; }
    public string RoleType { get; set; }
    public string ServerTypeId { get; set; }
    public string ServerType { get; set; }
    public string OSTypeId { get; set; }
    public string OSType { get; set; }
    public string Status { get; set; }
    public string Properties { get; set; }
    public string LicenseId { get; set; }
    public string LicenseKey { get; set; }
    public string Version { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string ExceptionMessage { get; set; }
    public string FormVersion { get; set; }
    public bool IsAttached { get; set; }
}