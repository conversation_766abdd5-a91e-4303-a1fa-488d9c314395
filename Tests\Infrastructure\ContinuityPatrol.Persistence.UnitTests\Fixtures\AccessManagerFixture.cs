﻿using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AccessManagerFixture : IDisposable
{
    public List<AccessManager> AccessManagerPaginationList { get; set; }
    public List<AccessManager> AccessManagerList { get; set; }
    public AccessManager AccessManagerDto { get; set; }
    public UserRole UserRoleDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public AccessManagerFixture()
    {
        var fixture = new Fixture();

        AccessManagerList = fixture.Create<List<AccessManager>>();

        AccessManagerPaginationList = fixture.CreateMany<AccessManager>(20).ToList();

        AccessManagerPaginationList.ForEach(x=>x.CompanyId = CompanyId);
        AccessManagerPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AccessManagerPaginationList.ForEach(x => x.IsActive = true);

        AccessManagerList.ForEach(x=>x.CompanyId = CompanyId);
        AccessManagerList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AccessManagerList.ForEach(x => x.IsActive = true);

        AccessManagerDto = fixture.Create<AccessManager>();
        AccessManagerDto.CompanyId = CompanyId;
        AccessManagerDto.ReferenceId = Guid.NewGuid().ToString();
        AccessManagerDto.IsActive = true;

        UserRoleDto = fixture.Create<UserRole>();
        UserRoleDto.ReferenceId = Guid.NewGuid().ToString();
        UserRoleDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }


    public void Dispose()
    {
        DbContext?.Dispose();
    }
}