﻿//using AutoMapper;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.Extensions.Logging;
//using Moq;
//using ContinuityPatrol.Application.Features.FiaTemplate.Commands.Create;
//using ContinuityPatrol.Application.Features.FiaTemplate.Commands.Update;
//using ContinuityPatrol.Application.Features.FiaTemplate.Queries.GetPaginatedList;
//using ContinuityPatrol.Domain.ViewModels.FiaTemplateModel;
//using ContinuityPatrol.Shared.Core.Responses;
//using ContinuityPatrol.Shared.Services.Provider;
//using ContinuityPatrol.Web.Areas.Configuration.Controllers;
//using ContinuityPatrol.Domain.Wrapper;
//using MediatR;
//using ContinuityPatrol.Shared.Tests.Fakes;
//using Microsoft.AspNetCore.Http;
//using AutoFixture;
//using Newtonsoft.Json;

//namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
//{
//    public class FIATemplatesController_Should
//    {
//        private readonly Mock<IPublisher> _publisherMock=new();
//        private readonly Mock<IDataProvider> _dataProviderMock = new();
//        private readonly Mock<IMapper> _mapperMock = new();
//        private readonly Mock<ILogger<FIATemplatesController>> _loggerMock = new();
//        private  FIATemplatesController _controller;

//        public FIATemplatesController_Should()
//        {
            
//            _controller = new FIATemplatesController(
//                _publisherMock.Object,
//                _loggerMock.Object,
//                _dataProviderMock.Object,
//                _mapperMock.Object
//            );
//            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
//            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
//        }

//        [Fact]
//        public void List_ReturnsViewResult()
//        {
//            // Act
//            var result = _controller.List();

//            // Assert
//            var viewResult = Assert.IsType<ViewResult>(result);
//        }

//        [Fact]
//        public async Task OverAllList_ReturnsJsonResult_WithSuccess()
//        {
            
//            var query = new GetFiaTemplatePaginatedListQuery();
//            var timeList = new PaginatedResult<FiaTemplateListVm>();
//            _dataProviderMock.Setup(x => x.FiaTemplate.GetPaginatedFiaTemplates(query))
//                .ReturnsAsync(timeList);

//            // Act
//            var result = await _controller.GetPaginatedFiaTemplatesList(query);

//            // Assert
//            var jsonResult = Assert.IsType<JsonResult>(result);
//            var json = JsonConvert.SerializeObject(jsonResult.Value);
//            Assert.Contains("\"Success\":true", json);
//        }

//        [Fact]
//        public async Task OverAllList_ReturnsJsonResult_WithException()
//        {
            
//            var query = new GetFiaTemplatePaginatedListQuery();
//            _dataProviderMock.Setup(x => x.FiaTemplate.GetPaginatedFiaTemplates(query))
//                .ThrowsAsync(new Exception("Database error"));

            
//            var result = await _controller.GetPaginatedFiaTemplatesList(query);

            
//            var jsonResult = Assert.IsType<JsonResult>(result);
//            var json = JsonConvert.SerializeObject(jsonResult.Value);
//            Assert.Contains("\"Success\":false", json);
//        }

//        [Fact]
//        public async Task CreateOrUpdate_ReturnsJsonResult_WithSuccess()
//        {
//            var masterListVm = new AutoFixture.Fixture().Create<FiaTemplateListVm>();
//            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
//            dic.Add("id", "22");
//            var collection = new FormCollection(dic);
//            _controller.Request.Form = collection;
//            var command = new CreateFiaTemplateCommand();
//            var updateCommand = new UpdateFiaTemplateCommand();
//            var response = new BaseResponse();
//            _mapperMock.Setup(m => m.Map<CreateFiaTemplateCommand>(masterListVm))
//                .Returns(command);
//            _mapperMock.Setup(m => m.Map<UpdateFiaTemplateCommand>(masterListVm))
//                .Returns(updateCommand);
//            _dataProviderMock.Setup(x => x.FiaTemplate.CreateAsync(command))
//                .ReturnsAsync(response);
//            _dataProviderMock.Setup(x => x.FiaTemplate.UpdateAsync(updateCommand))
//               .ReturnsAsync(response);


//            var result = await _controller.FiaTemplateCreateOrUpdate(masterListVm);


//            var jsonResult = Assert.IsType<JsonResult>(result);
//            var json = JsonConvert.SerializeObject(jsonResult);
//            Assert.Contains("\"Success\":true", json);
//        }


//        [Fact]
//        public async Task Delete_data_ReturnsJsonResult_WithSuccess()
//        {
//            // Arrange
//            var id = "1";
//            var response =new BaseResponse();
//            _dataProviderMock.Setup(x => x.TimeIntervalMaster.DeleteAsync(id))
//                .ReturnsAsync(response);

//            // Act
//            var result = await _controller.TimeIntervalMasterDelete(id);

//            // Assert
//            var jsonResult = Assert.IsType<JsonResult>(result);
//            var json = JsonConvert.SerializeObject(jsonResult.Value);
//            Assert.Contains("\"Success\":true", json);
//            Assert.NotNull(result);
//        }

//        [Fact]
//        public async Task Delete_data_ReturnsJsonResult_WithException()
//        {
//            // Arrange
//            var id = "1";
//            _dataProviderMock.Setup(x => x.TimeIntervalMaster.DeleteAsync(id))
//                .ThrowsAsync(new Exception("Database error"));

//            // Act
//            var result = await _controller.TimeIntervalMasterDelete(id);

//            // Assert
//            var jsonResult = Assert.IsType<JsonResult>(result);
          
//            var json = JsonConvert.SerializeObject(jsonResult.Value);
//            Assert.Contains("\"Success\":false", json);
//        }

       
//    }
//}
