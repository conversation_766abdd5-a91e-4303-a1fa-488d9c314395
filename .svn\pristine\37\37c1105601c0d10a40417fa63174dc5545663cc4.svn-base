using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IDynamicDashboardMapRepository : IRepository<DynamicDashboardMap>
{
    Task<bool> IsNameExist(string name, string id);
    Task<DynamicDashboardMap> IsDefaultDashboardByUserId(string userId);
    Task<DynamicDashboardMap> IsDefaultDashboardByRoleId(string roleId);
}