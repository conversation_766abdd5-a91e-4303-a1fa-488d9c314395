﻿namespace ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowActionType.Events.PaginatedEvent;
using ContinuityPatrol.Application.Features.WorkflowActionType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionTypeModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

[Area("Admin")]
public class OperationTypeController : Controller
{
    private readonly ILogger<OperationTypeController> _logger;
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public OperationTypeController(ILogger<OperationTypeController> logger, IDataProvider dataProvider, IPublisher publisher, IMapper mapper)
    {
        _logger = logger;
        _publisher = publisher;
        _dataProvider = dataProvider;
        _mapper = mapper;
    }


    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in Operation Type");
        await _publisher.Publish(new WorkflowActionTypePaginatedEvent());
        return View();
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    //[Authorize(Policy = Permissions.Configuration.CreateAndEdit)]
    public async Task<IActionResult> CreateOrUpdate(WorkflowActionTypeViewModel WorkflowActionTypeViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in Operation Type");

        try
        {
            var OperationTypeId = Request.Form["id"].ToString();

            if (OperationTypeId.IsNullOrWhiteSpace())
            {
                var OperationMapping = _mapper.Map<CreateWorkflowActionTypeCommand>(WorkflowActionTypeViewModel);
                OperationMapping.IsDelete = true;

                var result = await _dataProvider.WorkflowActionTypes.CreateAsync(OperationMapping);

                _logger.LogDebug($"Creating Operation Type '{OperationMapping.ActionType}'");

                return Json(new { Success = true, data = result });
            }
            else
            {
                var OperationMapping = _mapper.Map<UpdateWorkflowActionTypeCommand>(WorkflowActionTypeViewModel);


                var result = await _dataProvider.WorkflowActionTypes.UpdateAsync(OperationMapping);

                _logger.LogDebug($"Updating Operation Type '{OperationMapping.ActionType}'");

                return Json(new { Success = true, data = result });
            }


        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on Operation Type page: {ex.ValidationErrors.FirstOrDefault()}");

            //TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return ex.GetJsonException();
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Operation Type page while processing the request for create or update.", ex);

            //TempData.NotifyWarning(ex.Message);

            return ex.GetJsonException();
        }

    }
    [HttpGet]
    public async Task<JsonResult> GetPagination(GetWorkflowActionTypePaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in OperationType");
        try
        {
            _logger.LogDebug("Successfully retrieved Operation Type paginated list on Operation Type page");

            return Json(await _dataProvider.WorkflowActionTypes.GetWorkflowActionTypePaginatedList(query));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on  page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }
    }

    //[HttpGet]
    //public async Task<JsonResult> GetPagination(GetWorkflowActionResultPaginatedListQuery query)
    //{
    //    _logger.LogDebug("Entering GetPagination method in Cyber Component");
    //    try
    //    {
    //        _logger.LogDebug("Successfully retrieved cyber component paginated list on cyber component page");

    //        return Json(await _dataProvider.WorkflowActionResult.GetPaginatedWorkflowActionResult(query));
    //    }
    //    catch (Exception ex)
    //    {
    //        _logger.Exception("An error occurred on cyber component page while processing the pagination request.", ex);

    //        return ex.GetJsonException();
    //    }
    //}

    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in Operation Type");

        try
        {
            _logger.LogDebug($"Deleting Operation Type Detail by Id '{id}'");

            var result = await _dataProvider.WorkflowActionTypes.DeleteAsync(id);

            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on Operation Type.", ex);

            return ex.GetJsonException();
        }
    }
    public async Task<bool> IsWorkflowActionTypeExist(string name, string id)
    {
        _logger.LogDebug("Entering IsWorkflowActionTypeExist method in Operation Type");
        try
        {
            _logger.LogDebug("Returning result for IsWorkflowActionTypeExist on Operation Type");

            return await _dataProvider.WorkflowActionTypes.IsWorkflowActionTypeExist(name, id);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on Operation Type while checking if Operation Type name exists for : {name}.", ex);

            return false;
        }

    }
}

