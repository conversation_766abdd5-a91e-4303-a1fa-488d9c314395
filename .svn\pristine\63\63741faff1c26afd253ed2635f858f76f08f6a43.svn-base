using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class BackUpLogRepositoryTests : IClassFixture<BackUpLogFixture>
{
    private readonly BackUpLogFixture _backUpLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BackUpLogRepository _repository;

    public BackUpLogRepositoryTests(BackUpLogFixture backUpLogFixture)
    {
        _backUpLogFixture = backUpLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BackUpLogRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var backUpLog = _backUpLogFixture.BackUpLogDto;

        // Act
        await _dbContext.BackUpLogs.AddAsync(backUpLog);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.GetByReferenceIdAsync(backUpLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(backUpLog.HostName, result.HostName);
        Assert.Equal(backUpLog.DatabaseName, result.DatabaseName);
        Assert.Single(_dbContext.BackUpLogs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var backUpLog = _backUpLogFixture.BackUpLogDto;
        await _dbContext.BackUpLogs.AddAsync(backUpLog);
        await _dbContext.SaveChangesAsync();

        backUpLog.HostName = "UpdatedHostName";
        backUpLog.DatabaseName = "UpdatedDatabaseName";
        backUpLog.Status = "UpdatedStatus";

        // Act
         _dbContext.BackUpLogs.Update(backUpLog);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(backUpLog.ReferenceId);   

        // Assert
        Assert.Equal("UpdatedHostName", result.HostName);
        Assert.Equal("UpdatedDatabaseName", result.DatabaseName);
        Assert.Equal("UpdatedStatus", result.Status);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var backUpLog = _backUpLogFixture.BackUpLogDto;
        await _dbContext.BackUpLogs.AddAsync(backUpLog);
        await _dbContext.SaveChangesAsync();

        // Act
        backUpLog.IsActive = false;
         _dbContext.BackUpLogs.Update(backUpLog);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var backUpLog = _backUpLogFixture.BackUpLogDto;
        await _dbContext.BackUpLogs.AddAsync(backUpLog);
        await _dbContext.SaveChangesAsync();
        var addedEntity = await _repository.GetByReferenceIdAsync(backUpLog.ReferenceId);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.HostName, result.HostName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var backUpLog = _backUpLogFixture.BackUpLogDto;
        await _dbContext.BackUpLogs.AddAsync(backUpLog);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GetByReferenceIdAsync(backUpLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(backUpLog.ReferenceId, result.ReferenceId);
        Assert.Equal(backUpLog.HostName, result.HostName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var backUpLogs = _backUpLogFixture.BackUpLogList;
        await _repository.AddRangeAsync(backUpLogs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(backUpLogs.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var backUpLogs = _backUpLogFixture.BackUpLogList;

        // Act
        var result = await _repository.AddRangeAsync(backUpLogs);

        // Assert
        Assert.Equal(backUpLogs.Count, result.Count());
        Assert.Equal(backUpLogs.Count, _dbContext.BackUpLogs.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var backUpLogs = _backUpLogFixture.BackUpLogList;
        await _repository.AddRangeAsync(backUpLogs);

        // Act
        var result = await _repository.RemoveRangeAsync(backUpLogs);

        // Assert
        Assert.Equal(backUpLogs.Count, result.Count());
        Assert.Empty(_dbContext.BackUpLogs);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region FindByFilterAsync Tests

    [Fact]
    public async Task FindByFilter_ShouldReturnFilteredEntities()
    {
        // Arrange
        var backUpLogs = _backUpLogFixture.BackUpLogList;
        var targetHostName = "TEST_HOST";
        backUpLogs.First().HostName = targetHostName;
        await _repository.AddRangeAsync(backUpLogs);

        // Act
        var result = await _repository.FindByFilterAsync(x => x.HostName == targetHostName);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(targetHostName, result.First().HostName);
    }

    [Fact]
    public async Task FindByFilter_ShouldReturnEmptyList_WhenNoMatch()
    {
        // Arrange
        var backUpLogs = _backUpLogFixture.BackUpLogList;
        await _repository.AddRangeAsync(backUpLogs);

        // Act
        var result = await _repository.FindByFilterAsync(x => x.HostName == "NON_EXISTENT_HOST");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenDatabaseNameExistsAndIdIsInvalid()
    {
        // Arrange
        var backUpLog = _backUpLogFixture.BackUpLogDto;
        backUpLog.DatabaseName = "ExistingDatabaseName";
        await _dbContext.BackUpLogs.AddAsync(backUpLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("ExistingDatabaseName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenDatabaseNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var backUpLogs = _backUpLogFixture.BackUpLogList;
        await _repository.AddRangeAsync(backUpLogs);

        // Act
        var result = await _repository.IsNameExist("NonExistentDatabaseName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenDatabaseNameExistsForSameEntity()
    {
        // Arrange
        var backUpLog = _backUpLogFixture.BackUpLogDto;
        backUpLog.DatabaseName = "SameDatabaseName";
        await _dbContext.BackUpLogs.AddAsync(backUpLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("SameDatabaseName", backUpLog.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleNullParameters()
    {
        // Act & Assert
        var result1 = await _repository.IsNameExist(null, "valid-guid");
        var result2 = await _repository.IsNameExist("TestDatabaseName", null);
        var result3 = await _repository.IsNameExist(null, null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        var backUpLogs = _backUpLogFixture.BackUpLogList;
        var backUpLog1 = backUpLogs[0];
        var backUpLog2 = backUpLogs[1];

        await _dbContext.BackUpLogs.AddAsync(backUpLog1);
        await _dbContext.BackUpLogs.AddAsync(backUpLog2);
        await _dbContext.SaveChangesAsync();
       
        var results = await _repository.ListAllAsync();
        Assert.Equal(2, results.Count);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.BackUpLogs.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var backUpLogs = _backUpLogFixture.BackUpLogList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _dbContext.BackUpLogs.AddRangeAsync(backUpLogs);
        await _dbContext.SaveChangesAsync();
        var initialCount = backUpLogs.Count;

        var toUpdate = backUpLogs.Take(2).ToList();
        toUpdate.ForEach(x => x.Status = "Updated");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = backUpLogs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Status == "Updated").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleBackupServerFlags()
    {
        // Arrange
        var backUpLogs = new List<BackUpLog>
        {
            new BackUpLog
            {
                HostName = "Host1",
                DatabaseName = "DB1",
                IsLocalServer = true,
                IsBackUpServer = false,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new BackUpLog
            {
                HostName = "Host2",
                DatabaseName = "DB2",
                IsLocalServer = false,
                IsBackUpServer = true,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new BackUpLog
            {
                HostName = "Host3",
                DatabaseName = "DB3",
                IsLocalServer = true,
                IsBackUpServer = true,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(backUpLogs);

        // Act
        var localServers = await _repository.FindByFilterAsync(x => x.IsLocalServer);
        var backupServers = await _repository.FindByFilterAsync(x => x.IsBackUpServer);

        // Assert
        Assert.Equal(2, localServers.Count);
        Assert.Equal(2, backupServers.Count);
        Assert.All(localServers, x => Assert.True(x.IsLocalServer));
        Assert.All(backupServers, x => Assert.True(x.IsBackUpServer));
    }

    #endregion
}
