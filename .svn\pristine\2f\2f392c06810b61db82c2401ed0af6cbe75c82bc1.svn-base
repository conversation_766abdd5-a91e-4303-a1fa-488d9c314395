﻿using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Events.PaginatedView;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixModel;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Permissions;

namespace ContinuityPatrol.Web.Areas.Manage.Controllers;
[Area("Manage")]

public class TemplateController : Controller
{
    private readonly IPublisher _publisher;
    private readonly ILogger<TemplateController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;

    public TemplateController(IPublisher publisher, ILogger<TemplateController> logger, IMapper mapper, IDataProvider dataProvider)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }

    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in  ApprovalMatrixRequested");
        await _publisher.Publish(new ApprovalMatrixRequestPaginatedEvent());
        return View();
    }

    [HttpPost]
    //[ValidateAntiForgeryToken]
    //[AntiXss]
    public async Task<IActionResult> CreateOrUpdate(ApprovalMatrixModel approvalMatrixTemplate)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in  ApprovalMatrix");
        var userId = Request.Form["id"].ToString();
        try
        {
            if (userId.IsNullOrWhiteSpace())
            {
                _logger.LogDebug($"Creating  ApprovalMatrixTemplate");
                var createCommand = _mapper.Map<CreateApprovalMatrixCommand>(approvalMatrixTemplate);
                var response = await _dataProvider.approvalMatrixService.CreateAsync(createCommand);
                return Json(new { success = true, data = response });
            }
            else
            {
                _logger.LogDebug($"Updating  ApprovalMatrixTemplate");
                var updateCommand = _mapper.Map<UpdateApprovalMatrixCommand>(approvalMatrixTemplate);
                var response = await _dataProvider.approvalMatrixService.UpdateAsync(updateCommand);
                return Json(new { success = true, data = response });
            }
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on  ApprovalMatrixRequested page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPaginatedlist(GetApprovalMatrixPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPaginated method in ApprovalMatrix");
        try
        {
            _logger.LogDebug("Successfully retrieved paginated list for ApprovalMatrix");
            return Json(await _dataProvider.approvalMatrixService.GetPaginatedApprovalMatrices(query));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on ApprovalMatrix page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> ApprovalMatrixUsersList()
    {
        _logger.LogDebug("Entering ApprovalMatrix Users method in ApprovalMatrix Users");
        try
        {
            var result = await _dataProvider.ApprovalMatrixUsers.GetApprovalMatrixUsersList();
            _logger.LogDebug("Successfully retrieved ApprovalMatrix Usernames in ApprovalMatrix Users");
            return Json(result);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on ApprovalMatrix Users page while retrieving ApprovalMatrix Users List.", ex);
            return ex.GetJsonException();
        }
    }

    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        try
        {
            var response = await _dataProvider.approvalMatrixService.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted record in ApprovalMatrix");
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on ApprovalMatrix.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<bool> IsApprovalMatrixNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsApprovalMatrixName method in ApprovalMatrix.");
        try
        {
            var isNameExits = await _dataProvider.approvalMatrixService.IsApprovalMatrixNameExist(name, id);
            _logger.LogDebug($"Successfully retrieved name exist detail in ApprovalMatrix.");
            return isNameExits;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on ApprovalMatrix page while retrieving the name exist detail.", ex);
            return false;
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetBusinessFunctions(string id)
    {
        _logger.LogDebug("Entering GetBusinessFunctions method in Template");

        if (id.IsNullOrWhiteSpace())
        {
            _logger.LogDebug("Id is not valid format in GetBusinessFunctions method on template page");
            return Json(new { Success = false, Message = "Id is not valid format", ErrorCode = 0 });
        }
        else
        {
            try
            {
                var businessFunctionNames = await _dataProvider.BusinessFunction.GetBusinessFunctionNamesByBusinessServiceId(id);
                _logger.LogDebug("Successfully retrieved business function names by businessServiceId on template");
                return Json(new { Success = true, data = businessFunctionNames });
            }
            catch (Exception ex)
            {
                _logger.Exception("An error occurred on template page while retrieving the businessFunction names by businessServiceId.", ex);
                return ex.GetJsonException();
            }
        }
    }
}

