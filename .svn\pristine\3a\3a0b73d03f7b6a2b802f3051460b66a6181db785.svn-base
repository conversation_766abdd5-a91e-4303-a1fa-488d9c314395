let getreplicationslists = "Configuration/Replication/GetReplicationList";
let SavePath = "Configuration/Server/CreateOrUpdate";
let ReplicationType = $('#ddlReplicationTypeID');
const siteList = 'Configuration/Server/GetSiteNames'
const licenseList = 'Admin/LicenseManager/GetLicensesNames'
const rpList = 'Admin/FormBuilder/GetForms';
const ReplicationTypes = 'Admin/FormMapping/GetFormMappingListByName';

let replicationList = "";
let dataTable = "";
let isEdit = false;
let editClicked = false;
let propsReplication;
let this1 = '';
let propertyType;
let lunHeaderOne, lunHeaderTwo, lunHeaderThree, lunHeaderFour, lunHeaderFive, lunHeaderSix;
let replicationLogo = "";
let sourcePath, destinationPath;
let rSyncDataSyncRoboCopy;
let btnDisableReplication = false;
let lssidState = false;
let lssidrangeState = false;
let sessionState = false;
let getreplication = '';
let licenseIdForCountError = "";
let clonedReplicationRowData = "";
let cloneReplicationSlNo = 0;
let clonedReplicationLists = {
    "ReplicationId": "",
    "ReplicationList": [
    ]
};
let deleteSaveAsReplRow = "";
let createPermission = $("#configurationCreate").data("create-permission").toLowerCase();
let deletePermission = $("#configurationDelete").data("delete-permission").toLowerCase();

//CheckSession
let isSession = false;

$(async function () {
    $('#showHideLicenseKey').hide();
    $('#licensed').val("NA");

    $('#search-inp, #textName').on('keypress', replicationPreventEnterKey);

    (function () {
        let sessionData = sessionStorage.getItem('replicationFromITView')
        if (sessionData !== undefined && sessionData !== null && sessionData !== '') {
            getreplication = sessionData;
            isSession = true;
        }
    })();   

    $("#createBtn").toggleClass('btn-disabled', createPermission === 'false').css('pointer-events', createPermission === 'false' ? 'none' : '');
    let selectedValues = [];
    dataTable = $('#datatablelist').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/Configuration/Replication/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "type" : sortIndex === 3 ? "siteName" : "";
                    let orderValue = d?.order[0]?.dir || 'asc';
                    let selectedType = $('#search-in-type').val();
                    if (getreplication?.length > 0) {
                        selectedType = getreplication;
                    } else if (selectedType === "all") {
                        selectedType = "";
                    }
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.TypeId = selectedType;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    const { data, totalPages, totalCount } = json;
                    json.recordsTotal = totalPages;
                    json.recordsFiltered = totalCount;
                    let hasData = data.length === 0
                    $(".pagination-column").toggleClass("disabled", hasData);
                    return data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        return type === 'display' ? meta.row + 1 : data;
                    },
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title='${data || "NA"}'>${data || "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "type", "type": "Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            let iconList = JSON.parse(row?.properties) || {};
                            const iconName = iconList?.icon || "cp-images";
                            return `<span title='${data || "NA"}'><i class='${iconName} me-1'></i>${data || "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "siteName", "name": "Site", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>${data || "NA"}</span>` : data;
                    }
                },
                {
                    "orderable": false, "width": '100px',
                    "render": function (data, type, row) {
                        const rowID = row?.id;
                        const rowName = row?.name;
                        const rowType = row?.type;

                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="edit-button" data-replication='${rowID}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete" id="replication-delete-button" data-replication-id="${rowID}" 
                                                 data-replication="${rowType}" data-replication-name="${rowName}" data-bs-toggle="modal" 
                                                  data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>                                  
                                        </div>`;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="edit-button" data-replication='${rowID}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="icon-disabled">
                                                <i class="cp-Delete"></i>
                                            </span>                                  
                                        </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete" id="replication-delete-button" data-replication-id="${rowID}" 
                                              data-replication="${rowType}" data-replication-name="${rowName}" data-bs-toggle="modal" 
                                                data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>                                  
                                        </div>`;
                        }
                        else {
                            return `
                                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete" class="icon-disabled">
                                                <i class="cp-Delete"></i>
                                            </span>
                                        </div>`;
                        }
                    },
                    "orderable": false
                },
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart; // Get the start index of displayed data
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-in-type').on("change", function () {
        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    });

    $('#search-inp').on('input', commonDebounce(async function (e) {
        const checkboxes = ["Name", "Type", "Site"];
        let sanitizedValue = $(this).val().replace(/^\s/, '').replace(/\s+/g, ' ').replace(/\s$/, ' ');

        if (sanitizedValue?.trim() === "") {
            $(this).val("");
            sanitizedValue = "";
        } else {
            $(this).val(sanitizedValue);
        }

        checkboxes.forEach(id => {
            const checkbox = $(`#${id}`);

            if (checkbox.is(':checked')) {
                selectedValues.push(checkbox.val() + sanitizedValue);
            }
        });
        dataTable.ajax.reload(function (json) {
            let $dataTables_empty = $('.dataTables_empty');

            if (sanitizedValue.length === 0 && json?.data?.data?.length === 0) {
                $dataTables_empty.text('No Data Found');
            } else if (json?.recordsFiltered === 0) {
                $dataTables_empty.text('No matching records found');
            }
        })
    }))

    getReplicationType();

    $('.form-select-sm').select2({
        "language": {
            "noResults": function () {
                return "No results found";
            }
        },
    });

    $('#previousButton').on('click', function () {
        $('#forModallgtoxl').removeClass("modal-xl").addClass("modal-lg");
        $("#nextButton").show();
        $("#previousButton, #saveButton").hide();

        const removeElements = (selector) => {
            document.querySelectorAll(selector)?.forEach(element => element.remove());
        };
        removeElements('.dynamic-select-tag');

        const inputValues = $(".formeo-render .f-field-group input[type='text']:visible, .formeo-render .f-field-group input[type='number']:visible, .formeo-render .f-field-group input[type='password']:visible");
        //const inputValues = $(".formeo-render .f-field-group input[type='text']:visible");
        if (inputValues?.length) {
            inputValues?.each(function () {
                let $this = $(this);
                let res = $this.val();
                if (!res) {
                    $this.siblings('.dynamic-input-field').remove("");
                    $('.sourceFieldError').removeClass("field-validation-error").text("");
                    $('.destinationFieldError').removeClass("field-validation-error").text("");
                    $('.propertyError').removeClass("field-validation-error").text("");
                }
            })
        }

        //document.querySelectorAll('.field-validation-error').forEach(element => {
        //    element.classList.remove('field-validation-error');
        //    element.textContent = '';
        //});           
    });

    getReplicationLists();

    clearSessionReplica();

    $('#datatablelist').on('click', '.edit-button', async function () {
        clearInputFields('replication-form', ['#Name-error', '#Site-error', '#BusinessServiceIdError', '#Type-error', '#Licensekey-error']);
        editClicked = true;
        let replicationID = $(this).data("replication");
        $('#forModallgtoxl').removeClass("modal-xl").addClass("modal-lg");
        $("#nextButton").css("display", "");
        $("#previousButton").css("display", "none");
        isEdit = true;
        form.steps('previous');
        $("#saveButton").css("display", "none").text('Update');
        $('#CreateModal').modal('show');

        await $.ajax({
            url: RootUrl + "Configuration/Replication/GetReplicationById",
            method: 'GET',
            dataType: 'json',
            data: { id: replicationID },
            success: function (result) {
                if (result.success) {
                    populateModalFields(result?.data?.response2);
                    rSyncDataSyncRoboCopy = result?.data?.response1;
                } else {
                    errorNotification(result);
                }
            }
        })
    });

    $('#datatablelist').on('click', '#replication-delete-button', function () {
        let replName = $(this).data('replication-name');
        let ReplicationId = $(this).data('replication-id');
        $('#textDeleteId').val(ReplicationId);
        $('#deleteData').text(replName);
        $("#deleteData").attr("title", replName);
    });

    $("#confirmDeleteButton").on("click", async function () {
        const form = $('#replicationDelete')[0];
        const formData = new FormData(form);

        if (!btnDisableReplication) {
            btnDisableReplication = true;
            let response = await $.ajax({
                type: "POST",
                url: RootUrl + "Configuration/Replication/Delete",
                headers: {
                    'RequestVerificationToken': await gettoken()
                },
                data: formData,
                contentType: false,
                processData: false,
            });

            if (response?.success) {
                $("#DeleteModal").modal("hide");
                notificationAlert("success", response?.data?.message);
                btnDisableReplication = false;
                setTimeout(() => {
                    //dataTable.ajax.reload();
                    dataTableDelete(dataTable);
                }, 2000)
            } else {
                $("#DeleteModal").modal("hide");
                errorNotification(response);
                btnDisableReplication = false;
            }
        }
    });

    $("#createBtn").on("click", function () {
        $("#information").html("");
        licenseIdForCountError = "";
        rSyncDataSyncRoboCopy;
        editClicked = false;
        $('#showHideLicenseKey').hide();
        $('#forModallgtoxl').removeClass("modal-xl").addClass("modal-lg");
        $("#nextButton").css("display", "");
        $("#saveButton").css("display", "none").text('Save');
        $("#previousButton").css("display", "none");
        $('#replicationTitleLogo').removeClass().addClass("cp-replication-on");
        isEdit = false;
        clearInputFields('replication-form', ['#Name-error', '#Site-error', '#BusinessServiceIdError', '#Type-error', '#Licensekey-error']);
        $('#formRenderingArea, #licensed').empty();
        $("#replicationNameType").text("Replication");
        $('#textName, #siteNames, #licensed, #replicationID').val("");
        $("#replicationNameType, #ddlReplicationTypeID, #businessServiceID").val("");
        form.steps('previous');
    })

    $('#siteNames').on("change", function (e, val) {
        $("#names").val($(this).find(":selected").text())
    });

    $('#ddlReplicationTypeID').on("change", async function () {
        const selectedReplicarion = $('#ddlReplicationTypeID option:selected');
        let replicationType = selectedReplicarion.text();

        if (replicationType?.toLowerCase()?.includes("perpetuuiti")) {
            $('#showHideLicenseKey').show();
            $('#Licensekey-error').text("").removeClass('field-validation-error');
        } else {
            $('#showHideLicenseKey').hide();
            document.getElementById('licensed').value = 'NA';
        }
        $("#replicationNameType").text(replicationType);
        replicationLogo = selectedReplicarion.attr('replicationLogo');
        $('#replicationTitleLogo').removeClass().addClass(replicationLogo);
        $("#replicaionType").val(replicationType);

        await $.ajax({
            url: RootUrl + "Admin/FormMapping/GetFormMappingByFormId",
            method: 'GET',
            data: { "formTypeId": selectedReplicarion.val(), "version": '' },
            dataType: 'json',
            success: async function (result) {
                if (result?.success) {
                    nextButtonStyle('', '');
                    let form = result?.data;
                    $('#formRenderingArea').empty();

                    try {
                        let parsedJsonData = JSON?.parse(form?.properties)
                        var renderedForm = new FormeoRenderer({
                            renderContainer: document.querySelector("#formRenderingArea")
                        });
                        renderedForm.render(parsedJsonData);

                        //Sybase Type
                        if (replicationType?.toLowerCase()?.includes("sybase-srs")) {
                            await sybaseType()
                        } else {
                            if ($('.sybaseType').length) {
                                $('.sybaseType').remove();
                            }
                        }

                        //
                        if (replicationType?.toLowerCase()?.includes("mssqlalwayson-availabilitygroup")) {
                            await MSSQLAlwaysOnAvailabilityGroup()
                        } else {
                            if ($('.availabilityGroupType').length) {
                                $('.availabilityGroupType').remove();
                            }
                        }

                        //  initalstate;
                        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                            var field = parsedJsonData.fields[fieldId];
                            if (field.conditions && field.conditions?.length > 0) {
                                field.conditions.forEach(function (condition) {
                                    condition.if.forEach(function (ifClause) {
                                        condition.then.forEach(function (thenClause) {
                                            if (thenClause.targetProperty === 'isVisible' && thenClause.assignment === 'equals' && ifClause.comparison !== "notEquals") {
                                                var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                if (targetElement && !targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none")) {
                                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                }
                                            }
                                        });
                                    });
                                });
                            }
                        });

                        setTimeout(() => {
                            let selectElements = document.querySelectorAll('.form-select-modal-dynamic');
                            selectElements.forEach(async function (selectElement) {
                                let $this = $(selectElement);
                                $this.select2({
                                    dropdownParent: this1.find('.modal-content'),
                                    placeholder: $this.attr('placeholder')
                                });
                            });

                            $('.form-select-modal-dynamic').next('.select2-container').css('width', '100%');

                            let disableSelectTagTitle = document.querySelectorAll('.select2-selection__rendered');
                            disableSelectTagTitle?.forEach(async function (selectElement) {
                                let $this = $(selectElement);
                                $this.attr('title', '');
                            });

                            onChangeFormBuilderValidation('replication');//onChangeNodeReplicationFormBuilderValidation();
                        }, 500);

                        await populateTheDynamicFields(parsedJsonData.fields);

                        for (const key in parsedJsonData?.fields) {
                            if (parsedJsonData.fields.hasOwnProperty(key)) {
                                const field = parsedJsonData.fields[key];
                                const { id, meta, attrs } = field;
                                if (meta.id === "textarea") {
                                    const textArea = $(`#f-${id}`);
                                    textArea.attr('title', '');
                                    textArea.attr('autocomplete', 'off');
                                }
                                if (meta.id === "ip-address") {
                                    const ipAddress = $(`#f-${id}`);
                                    ipAddress.attr('title', '');
                                    ipAddress.attr('maxlength', '40');
                                    ipAddress.attr('autocomplete', 'off');
                                }
                                if (meta.id === "paragraph") {
                                    const paragraph = $(`#f-${id}`);
                                    paragraph.attr('title', '');
                                    paragraph.attr('autocomplete', 'off');
                                }
                                if (meta.id === "text-input") {
                                    const textInput = $(`#f-${id}`);
                                    textInput.attr('title', '');
                                    let maxLength = textInput.attr('maxlength');
                                    if (!maxLength) {
                                        if (attrs?.name?.toLowerCase().includes("path")) {
                                            textInput.attr('maxlength', '500');
                                        } else {
                                            textInput.attr('maxlength', '100');
                                        }
                                    }
                                    textInput.attr('autocomplete', 'off');
                                }
                                if (meta.id === "password-input") {
                                    const passwordInput = $(`#f-${id}`);
                                    passwordInput.attr('title', '');
                                    passwordInput.attr('maxlength', '30');
                                    passwordInput.attr('autocomplete', 'off');
                                }
                                if (meta.id === "number") {
                                    const numberField = $(`#f-${id}`);
                                    numberField.attr('title', '');
                                    numberField.attr('autocomplete', 'off');
                                    let name = numberField.attr('name').toLowerCase();
                                    const minLength = numberField?.attr('minlength');
                                    const maxLength = numberField?.attr('maxlength');
                                    const intMinValue = parseInt(minLength, 10);
                                    const intMaxValue = parseInt(maxLength, 10);
                                    if (intMinValue && intMaxValue && intMinValue > 0 && intMaxValue > 0) {
                                        numberField.on("keyup keydown", function (event) {
                                            let eventKey = event.key;
                                            const validKeys = ["Home", "End", "Backspace", "Delete", "Left", "Right", ...Array.from({ length: 10 }, (_, i) => i.toString())];
                                            if (validKeys.includes(eventKey)) {
                                                const inputValue = $(this).val();
                                                const allowedKeys = ['Home', 'End', 'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];
                                                if (inputValue.length >= intMaxValue && !allowedKeys.includes(event.key)) {
                                                    event.preventDefault(); // Prevent further input
                                                }
                                                const numericValue = parseInt($(this).val().length, 10);
                                                if (isNaN(numericValue) || intMinValue < 1 || numericValue > intMaxValue) {
                                                    $(this).val('');
                                                }
                                            } else {
                                                event.preventDefault();
                                            }
                                        });
                                    } else {
                                        numberField.prop('min', '1');
                                        numberField.attr('max', '99999');
                                        numberField.on("keyup keydown", function (event) {
                                            let eventKey = event.key;
                                            const validKeys = ["Home", "End", "Backspace", "Delete", "Left", "Right", ...Array.from({ length: 10 }, (_, i) => i.toString())];
                                            if (validKeys.includes(eventKey)) {
                                                const inputValue = $(this).val();
                                                const allowedKeys = ['Home', 'End', 'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];
                                                if (inputValue.length >= 5 && !allowedKeys.includes(event.key)) {
                                                    event.preventDefault(); // Prevent further input
                                                }
                                                const numericValue = parseInt($(this).val(), 10);
                                                if (isNaN(numericValue) || numericValue < 1 || numericValue > 99999) {
                                                    $(this).val('');
                                                }
                                            } else {
                                                event.preventDefault();
                                            }
                                        });
                                    }
                                }
                            }
                        }
                        $('#formRenderingArea').on('change', '.formeo-render .f-field-group input , .formeo-render .f-field-group select', function (event) {
                            let selectedValue = event.target.value;
                            let selectedid = event.target.id;
                            let typ = event.target.type
                            if (typ === "radio" || typ === "checkbox") {
                                Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                    var field = parsedJsonData.fields[fieldId];
                                    if (field.conditions && field.conditions?.length > 0) {
                                        var isVisible = false;
                                        field.conditions.forEach(function (condition) {
                                            var isMatchingCondition = condition.if.some(function (ifClause) {
                                                sourceField = parsedJsonData.fields[ifClause.source.substring(7)];
                                                return ifClause.target === selectedValue;
                                            });
                                            if (isMatchingCondition) {
                                                isVisible = true;
                                            }
                                        });
                                        field.conditions.forEach(function (condition) {
                                            condition.if.forEach(function (ifClause) {
                                                condition.then.forEach(function (thenClause) {
                                                    var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                    var srcElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                                    if (targetElement && thenClause.targetProperty === 'isVisible') {
                                                        if (isVisible) {
                                                            targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                        }
                                                        if (!event.target.checked && (selectedid.substring(0, selectedid?.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                            targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                            let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                                            let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                                            if (textField?.length > 0) {
                                                                removeValidationWhenUncheck(textField);
                                                            }
                                                            if (selectField?.length > 0) {
                                                                removeValidationWhenUncheck(selectField);
                                                            }
                                                        }
                                                    }
                                                });
                                            });
                                        });
                                    }
                                });
                            };
                            if (typ === "select-one") {
                                Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                    var field = parsedJsonData.fields[fieldId];
                                    if (field.conditions && field.conditions?.length > 0) {
                                        var isMatchingCondition = field.conditions.some(function (condition) {
                                            return condition.if.some(function (ifClause) {
                                                if (ifClause.source === `fields.${fieldId}` && ifClause.comparison === 'equals' && ifClause.target === selectedValue) {
                                                    return true;
                                                }
                                            });
                                        });
                                        if (isMatchingCondition) {
                                            field.conditions.forEach(function (condition) {
                                                condition.then.forEach(function (thenClause) {
                                                    condition.if.forEach(function (ifClause) {
                                                        var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                        if (targetElement && thenClause.targetProperty === 'isVisible') {
                                                            if (ifClause.target === selectedValue && thenClause.assignment === 'equals' && targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none")) {
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                                correctElementId = targetElement.id
                                                            } else if (ifClause.target !== selectedValue && thenClause.assignment === 'equals') {
                                                                targetElement.value = ""
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                                let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                                                let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                                                if (textField?.length > 0) {
                                                                    removeValidationWhenUncheck(textField);
                                                                }
                                                                if (selectField?.length > 0) {
                                                                    removeValidationWhenUncheck(selectField);
                                                                }
                                                            }
                                                        }
                                                    });
                                                });
                                            });
                                        }
                                    } else {
                                        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                            var field = parsedJsonData.fields[fieldId];
                                            if (field.conditions && field.conditions?.length > 0) {
                                                field.conditions.forEach(function (condition) {
                                                    condition.then.forEach(function (thenClause) {
                                                        condition.if.forEach(function (ifClause) {
                                                            var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                            var sourceElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                                            var sourceName = document.getElementsByName(`f-${ifClause.source.substring(7)}`);
                                                            //if (targetElement === null) {
                                                            //    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                            //}
                                                            if (targetElement && selectedValue !== ifClause.target && ifClause.comparison !== 'notEquals' && (selectedid === sourceElement || selectedid == sourceName)) {
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                                let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                                                let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                                                if (textField?.length > 0) {
                                                                    removeValidationWhenUncheck(textField);
                                                                }
                                                                if (selectField?.length > 0) {
                                                                    removeValidationWhenUncheck(selectField);
                                                                }
                                                            }
                                                        });
                                                    });
                                                });
                                            }
                                        });
                                    }

                                });
                            }
                        });

                        ///onsetconditionals
                        $('#formRenderingArea').on('change input', '.formeo-render .f-field-group input , .formeo-render .f-field-group select', function (event) {
                            let selectedValue = event.target.value;
                            let selectedid = event.target.id;
                            let typ = event.target.type
                            if (typ === "radio" || typ === "checkbox") {

                                // Loop through all fields and their conditions id radio
                                Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                    var field = parsedJsonData.fields[fieldId];
                                    if (field.conditions && field.conditions?.length > 0) {
                                        var isVisible = false;
                                        field.conditions.forEach(function (condition) {
                                            var isMatchingCondition = condition.if.some(function (ifClause) {
                                                sourceField = parsedJsonData.fields[ifClause.source.substring(7)];
                                                return ifClause.target === selectedValue;
                                            });
                                            if (isMatchingCondition) {
                                                isVisible = true;
                                            }
                                        });

                                        field.conditions.forEach(function (condition) {
                                            condition.then.forEach(function (thenClause) {
                                                condition.if.forEach(function (ifClause) {
                                                    var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                    var srcElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                                    if (targetElement) {
                                                        if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                                                            if (isVisible) {
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                            }
                                                            if (!event.target.checked && (selectedid.substring(0, selectedid?.length - 2) === `f-${ifClause.source.substring(7)}`)) {

                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                            }
                                                        }
                                                        else if (ifClause.comparison === "notEquals") {
                                                            if (targetElement && event.target.checked) {
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none')
                                                            }
                                                            else if (!event.target.checked && (selectedid.substring(0, selectedid?.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                                                            }
                                                        }
                                                    }
                                                });
                                            });
                                        });
                                    }
                                });
                            };
                            if (typ === "select-one") {
                                Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                    var field = parsedJsonData.fields[fieldId];

                                    if (field.conditions && field.conditions?.length > 0) {
                                        var isMatchingCondition = field.conditions.some(function (condition) {
                                            return condition.if.some(function (ifClause) {
                                                if (ifClause.source === `fields.${fieldId}` && ifClause.target === selectedValue) {
                                                    return true;
                                                }
                                            });
                                        });

                                        if (isMatchingCondition) {
                                            field.conditions.forEach(function (condition) {
                                                condition.then.forEach(function (thenClause) {
                                                    condition.if.forEach(function (ifClause) {
                                                        var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                        if (targetElement && thenClause.targetProperty === 'isVisible') {
                                                            if (ifClause.target === selectedValue && thenClause.assignment === 'equals' && targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none")) {
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                                correctElementId = targetElement.id
                                                            } else if (ifClause.target !== selectedValue) {
                                                                targetElement.value = ""
                                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                            }
                                                        }
                                                    });
                                                });
                                            });
                                        } else {
                                            Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                                var field = parsedJsonData.fields[fieldId];
                                                if (field.conditions && field.conditions?.length > 0) {
                                                    field.conditions.forEach(function (condition) {
                                                        condition.then.forEach(function (thenClause) {
                                                            condition.if.forEach(function (ifClause) {
                                                                var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                                var sourceElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                                                var sourceName = document.getElementsByName(`f-${ifClause.source.substring(7)}`);
                                                                if (targetElement && selectedValue !== ifClause.target && ifClause.comparison !== 'notEquals' && (selectedid === sourceElement || selectedid == sourceName)) {
                                                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                                }
                                                            });
                                                        });
                                                    });
                                                }
                                            });
                                        }
                                    }
                                });
                            }
                        });

                        if (isEdit && propsReplication) {
                            setTimeout(() => {
                                populateReplicationDynamicFields(propsReplication, rSyncDataSyncRoboCopy)
                            }, 900)
                        }

                    } catch (error) {
                        notificationAlert("warning", "Form property is not valid format.");
                        nextButtonStyle('0.5', 'none');
                    }
                }
                else {
                    errorNotification(result);
                    nextButtonStyle('0.5', 'none');
                }
            }
        });
    });

    $("#saveButton").on("click", debounce(async function () {
        let res = await inputFormValidation('replication', $('#ddlReplicationTypeID option:selected').text().toLowerCase());
        $('.sourceDirectory, .destinationDirectory, [class^="replicationProperties"]').each(async function () {
            const $this = $(this);
            const isVisible = $this.is(":visible");

            if (isVisible) {
                let source = sourceDirInput($('.sourceDirectory'), $('#ddlReplicationTypeID option:selected').text().toLowerCase(), true);
                let destination = destinationDirInput($('.destinationDirectory'), $('#ddlReplicationTypeID option:selected').text().toLowerCase(), true);
                let properties = rSyncRoboDataSyncproperties($('select[class^="replicationProperties"]'), true);
                let sourceResult = source.every(value => value === true);
                let destinationResult = destination.every(value => value === true);
                let propertiesResult = properties.every(value => value === true);
                if (!sourceResult && !destinationResult && !propertiesResult) {
                    res = false;
                }
            } else {
                res = true;
            }
        });

        setTimeout(async () => {

            if (res) {
                let fd = saveReplicationFormFields();
                fd.icon = replicationLogo ? replicationLogo : "cp-images"
                const keys = Object.keys(fd);
                keys.forEach(key => {
                    if (key.startsWith('f-')) {
                        delete fd[key];
                    }
                });
                let hiddenInputProperties = document.getElementById('Props');
                let encryption = "";

                async function encrypt() {
                    encryption = await propertyEncryption(fd);
                    hiddenInputProperties.value = encryption;
                }
                await encrypt();

                if (!btnDisableReplication) {
                    btnDisableReplication = true;
                    let replRSyncRCopyData = {
                        Id: $('#replicationID').val(),
                        Name: $('#textName').val(),
                        Type: $('#replicaionType').val(),
                        TypeId: $('#ddlReplicationTypeID').val(),
                        CompanyId: $('#replicationID').val(),
                        SiteId: $('#siteNames').val(),
                        SiteName: $('#names').val(),
                        Properties: encryption,
                        Logo: $('#replicationLogo').val(),
                        LicenseId: $('#licensed').val() ? $('#licensed').val() : "NA",
                        LicenseKey: $('#replicationLicenseKey').val(),
                        BusinessServiceId: $('#businessServiceID').val(),
                        BusinessServiceName: $('#businessServiceName').val(),
                    }
                    if ($('#ddlReplicationTypeID option:selected').text().trim().toLowerCase().includes("rsync")) {
                        replRSyncRCopyData.RsyncJobViewModels = [];
                        fd.RsyncTable.forEach(function (value, index) {
                            let rsyncObj = {
                                Id: value?.tableID !== "null" ? value?.tableID : "",
                                ReplicationId: $('#replicationID').val(),
                                ReplicationName: $('#textName').val(),
                                ReplicationTypeId: $('#ddlReplicationTypeID').val(),
                                ReplicationType: $('#replicaionType').val(),
                                SiteId: $('#siteNames').val(),
                                SiteName: $('#names').val(),
                                Properties: encryption,
                                SourceDirectory: value.sourceDirectory,
                                DestinationDirectory: value.destinationDirectory,
                                ModeType: "Pending",
                                RsyncOptionId: value.properties,
                                LastSuccessfullReplTime: null,
                            };
                            replRSyncRCopyData.RsyncJobViewModels.push(rsyncObj);
                        });
                    }

                    if ($('#ddlReplicationTypeID option:selected').text().trim().toLowerCase().includes("robocopy")) {
                        replRSyncRCopyData.RoboCopyJobViewModels = [];
                        fd.RobocopyTable.forEach(function (value, index) {
                            let roboCopyObj = {
                                Id: value?.tableID !== "null" ? value?.tableID : "",
                                ReplicationId: $('#replicationID').val(),
                                ReplicationName: $('#textName').val(),
                                ReplicationTypeId: $('#ddlReplicationTypeID').val(),
                                ReplicationType: $('#replicaionType').val(),
                                SiteId: $('#siteNames').val(),
                                SiteName: $('#names').val(),
                                Properties: encryption,
                                SourceDirectory: value.sourceDirectory,
                                DestinationDirectory: value.destinationDirectory,
                                ModeType: "Pending",
                                JobProperties: "NULL",
                                ScheduleProperties: "NULL",
                                RoboCopyOptionsId: value.properties,
                                LastSuccessfullReplTime: null,
                            };
                            replRSyncRCopyData.RoboCopyJobViewModels.push(roboCopyObj);
                        });
                    }

                    if ($('#ddlReplicationTypeID option:selected').text().trim().toLowerCase().includes("datasync")) {
                        replRSyncRCopyData.DataSyncJobViewModels = [];
                        fd.DataSyncTable.forEach(function (value, index) {
                            let dataSyncObj = {
                                Id: value?.tableID !== "null" ? value?.tableID : "",
                                ReplicationId: $('#replicationID').val(),
                                ReplicationName: $('#textName').val(),
                                ReplicationTypeId: $('#ddlReplicationTypeID').val(),
                                ReplicationType: $('#replicaionType').val(),
                                SiteId: $('#siteNames').val(),
                                SiteName: $('#names').val(),
                                Properties: encryption,
                                SourceDirectory: value.sourceDirectory,
                                DestinationDirectory: value.destinationDirectory,
                                ModeType: "Pending",
                                JobProperties: "NULL",
                                ScheduleProperties: "NULL",
                                DataSyncOptionId: value.properties,
                                //LastSuccessfullReplTime: null,
                            };
                            replRSyncRCopyData.DataSyncJobViewModels.push(dataSyncObj);
                        });
                    }

                    async function createOrUpdateReplication() {
                        let response = await $.ajax({
                            type: "POST",
                            url: RootUrl + "Configuration/Replication/CreateOrUpdate",
                            dataType: "json",
                            headers: {
                                'RequestVerificationToken': await gettoken()
                            },
                            contentType: 'application/json',
                            data: JSON.stringify(replRSyncRCopyData),
                            traditional: true
                        });

                        if (response.success) {
                            notificationAlert("success", response.data.message);
                            $("#CreateModal").modal('hide');
                            btnDisableReplication = false;
                            setTimeout(() => {
                                //window.location.reload(); //Incase if change this var value won't change.
                                //dataTable.ajax.reload(); 
                                dataTableCreateAndUpdate($("#saveButton"), dataTable);
                            }, 2000);
                        } else {
                            $("#CreateModal").modal('hide');
                            errorNotification(response);
                            btnDisableReplication = false;
                            //setTimeout(() => {
                            //    //window.location.reload(); //Incase if change this var value won't change.                               
                            //}, 2000);
                        }
                    }
                    createOrUpdateReplication();
                }
            }
        }, 50)
    }, 200));

    $('.modal').on('hidden.bs.modal', function () {
        isEdit = false;
    });

    $('.btn-cancel').on('click', function () {
        nextButtonStyle('', '');
    });

    $('.replication').on('shown.bs.modal', function async() {
        this1 = $(this)
    });

    $('#textName').on('keyup', commonDebounce(async function () {
        let replName = $(".infraComponentsReplicationName");
        let sanitizedValue = replName.val().replace(/\s{2,}/g, ' ');
        replName.val(sanitizedValue);

        //InfraCommonFunctions.js InfraNameValidation
        await InfraNameValidation(sanitizedValue, $('#replicationID').val(), "Configuration/Replication/IsReplicationNameExist",
            $("#Name-error"), "Enter replication name", 'Special characters not allowed', 'ReplicationName');
    }));

    $(".next_btn").on("click", async function () {
        let validateLicenseKey = true;
        let businessServiceName = commonValidationReplication($("#businessServiceID").val(), " Select operational service", "BusinessServiceIdError");

        //InfraCommonFunctions.js InfraNameValidation
        let validateName = await InfraNameValidation($(".infraComponentsReplicationName").val(), $('#replicationID').val(),
            "Configuration/Replication/IsReplicationNameExist", $("#Name-error"), "Enter replication name",
            'Special characters not allowed', 'ReplicationName');
        let validateSiteName = commonValidationReplication($(".infraComponentsSiteName").val(), " Select site name", "Site-error");
        let validateReplicatoionType = serverTypeValidation($("#ddlReplicationTypeID option:selected").text(), " Select replication type", "Type-error");

        if ($("#ddlReplicationTypeID option:selected").text().toLowerCase().includes("perpetuuiti")) {
            validateLicenseKey = commonValidationReplication($("#licensed option:selected").val(), " Select license key", "Licensekey-error");
            validateLicenseKey = licenseCountValidation();
        } else {
            validateLicenseKey = true;
            $("#licensed").val("NA")
        }

        if (validateName && validateLicenseKey && validateSiteName && validateReplicatoionType && businessServiceName) {
            form.steps('next');
            setTimeout(() => {
                formDynamicButton()
            }, 300);
        }
    });

    $("#licensed").on("change", async function () {
        let licenseKey = $("#licensed :selected");
        $('#replicationLicenseKey').val(licenseKey.text());
        let count = licenseKey.attr('remainingcount');

        if (count !== null && count !== undefined) {
            $("#information").html(`<i class="cp-note me-1 fs-8"></i><span>Remaining count ${count} </span>`);
        }
        await commonValidationReplication(licenseKey.text(), " Select license key", "Licensekey-error");
        licenseCountValidation();
    });

    await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + 'Configuration/OperationalService/GetBusinessServiceNames',
        dataType: "json",
        success: function (result) {
            if (result?.success && (Array.isArray(result?.data) && result?.data.length > 0)) {
                const sortedData = result?.data?.sort((a, b) => a?.name.toLowerCase().localeCompare(b?.name.toLowerCase()));
                let businessService = $('#businessServiceID');
                let options = [];
                businessService.empty().append($('<option>').val("").text("Select Operational Service"));
                sortedData.forEach(function (item) {
                    options.push($('<option>').val(item?.id).text(item?.name).attr('businessServiceName', item?.name));
                });
                businessService.append(options);
            } else {
                errorNotification(result)
            }
        },
    });

    await $.ajax({
        url: RootUrl + rpList,
        method: 'GET',
        data: { "type": "replication" },
        dataType: 'json',
        success: function (result) {
            if (result.success) {
                replicationList = result?.data;
            } else {
                errorNotification(result)
            }
        }
    });

    await $.ajax({
        url: RootUrl + siteList,
        method: 'GET',
        dataType: 'json',
        success: function (result) {
            if (result.success && (Array.isArray(result?.data) && result?.data.length > 0)) {
                const sortedData = result?.data?.sort((a, b) => a?.name.toLowerCase().localeCompare(b?.name.toLowerCase()));
                let siteNames = $('#siteNames');
                let options = [];
                let $name = $('#names');
                if ($name.val()?.length != 0) {
                    siteNames.empty().append($('<option>').val("").text("Select Site Name"));
                    let value = $name.val();
                    sortedData?.forEach(function (item) {
                        options.push($('<option>').val(item?.id).text(item?.name));
                    });
                    siteNames.append(options);
                    $name.val(value);
                    siteNames.val(siteNames.val());
                }
                else {
                    siteNames.empty().append($('<option>').val('').text('Select Site'));
                    sortedData?.forEach(function (item) {
                        options.push($('<option>').val(item?.id).text(item?.name))
                    });
                    siteNames.append(options);
                }
            } else {
                errorNotification(result);
            }
        }
    });
});
