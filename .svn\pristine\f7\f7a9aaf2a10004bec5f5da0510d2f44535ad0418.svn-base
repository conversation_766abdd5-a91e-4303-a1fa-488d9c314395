﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Helper;
using MySqlConnector;
using Npgsql;
using System.Data;
using System.Data.Common;

namespace ContinuityPatrol.Persistence.Repositories
{
    public class AzureStorageAccountMonitorLogsRepository : BaseRepository<AzureStorageAccountMonitorlogs>, IAzureStorageAccountMonitorLogsRepository
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly IConfiguration _config;
        public AzureStorageAccountMonitorLogsRepository(ApplicationDbContext dbContext, IConfiguration config) : base(dbContext)
        {
            _dbContext = dbContext;
            _config = config;
        }
        public async Task<List<AzureStorageAccountMonitorlogs>> GetByInfraObjectId(string infraObjectId, string startDate,string endDate)
        {
            var tableName = GetTableName<AzureStorageAccountMonitorlogs>();
            var config = _config.GetConnectionString("Default");
            var dbProvider = _config.GetConnectionString("DBProvider");

            var decryptString = CryptographyHelper.Decrypt(config);
            var dbProviderString = CryptographyHelper.Decrypt(dbProvider);
            var schema = GetDatabaseNameFromConnectionString(decryptString, dbProviderString);

            var startDateTime = startDate.ToDateTime();
            var endDateTime = endDate.ToDateTime();

            var logTable = await _dbContext.AzureStorageAccountMonitorlogs
                .AsNoTracking()
                .Active()
                .Where(x => x.InfraObjectId == infraObjectId &&
                            x.CreatedDate.Date >= startDateTime &&
                            x.CreatedDate.Date <= endDateTime)
                .OrderBy(x => x.CreatedDate)
                .ToListAsync();


            var tableExist = await IsTableExistAsync($"{tableName}_bkp", schema, dbProviderString);
            if (!tableExist)
                return logTable;
            // Construct SQL query based on database provider
            string sqlQuery;
            if (dbProviderString.ToLower().Equals("oracle"))
            {
                var formattedStartDate = startDateTime.ToString("dd-MM-yyyy");
                var formattedEndDate = endDateTime.ToString("dd-MM-yyyy");
                sqlQuery = $"SELECT * FROM \"{schema}\".\"{tableName}_bkp\" WHERE TRUNC(\"CreatedDate\") >= TO_DATE('{formattedStartDate}', 'DD-MM-YYYY') AND TRUNC(\"CreatedDate\") <= TO_DATE('{formattedEndDate}', 'DD-MM-YYYY')";
            }
            else
            {
                sqlQuery = $"SELECT * FROM {tableName}_bkp WHERE CreatedDate >= {startDate} AND CreatedDate <= {endDate}";
            }

            try
            {
                // Get logs from backup table
                var logBackupTable = await _dbContext.AzureStorageAccountMonitorlogs
                    .FromSqlRaw(sqlQuery)
                    .AsNoTracking()
                    .ToListAsync();

                // Combine and return results
                return logTable.Concat(logBackupTable).ToList();
            }
            catch (Exception)
            {
                // If query fails, return just the active table results
                return logTable;
            }
        }
        public async Task<List<AzureStorageAccountMonitorlogs>> GetDetailByType(string type)
        {
            return await _dbContext.AzureStorageAccountMonitorlogs.AsNoTracking().Active().Where(x => x.Type.Equals(type)).ToListAsync();
        }
        private string GetDatabaseNameFromConnectionString(string connectionString, string provider)
        {
            DbConnectionStringBuilder builder = provider.ToLower() switch
            {
                "mysql" => new MySqlConnectionStringBuilder(connectionString),
                "oracle" => new OracleConnectionStringBuilder(connectionString),
                "mssql" => new SqlConnectionStringBuilder(connectionString),
                "npgsql" => new NpgsqlConnectionStringBuilder(connectionString),
                _ => throw new ArgumentException($"Unsupported provider name: {provider}")
            };

            if (builder.TryGetValue("Database", out var databaseName))
                return databaseName.ToString();

            if (builder.TryGetValue("User Id", out var dbName))
                return dbName.ToString()?.ToUpper();

            throw new ArgumentException("Unable to extract database name from connection string.");
        }
        public virtual string GetTableName<T>()
        {
            var entityType = DbContext.Model.FindEntityType(typeof(T));

            return entityType?.GetTableName() ?? throw new ArgumentException($"Table name not found for type {typeof(T).Name}");
        }
        public virtual async Task<bool> IsTableExistAsync(string tableName, string schemaName, string providerName)
        {
            var connection = DbContext.Database.GetDbConnection();
            bool wasOpen = connection.State == ConnectionState.Open;

            try
            {
                if (!wasOpen)
                    await connection.OpenAsync();

                var isOracle = providerName.ToLower().Equals("oracle");
                var schemaTable = isOracle
                    ? await connection.GetSchemaAsync("Tables", new[] { schemaName, tableName })
                    : await connection.GetSchemaAsync("Tables", new[] { null, schemaName, tableName });

                var schema = isOracle ? "OWNER" : "TABLE_SCHEMA";
                return schemaTable.Rows.OfType<DataRow>().Any(row =>
                    row[schema].ToString() == schemaName &&
                    row["TABLE_NAME"].ToString() == tableName);
            }
            finally
            {
                if (!wasOpen && connection.State == ConnectionState.Open)
                    await connection.CloseAsync();
            }
        }
    }
}
