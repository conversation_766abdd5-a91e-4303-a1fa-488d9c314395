﻿using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Web.Attributes;

[AttributeUsage(AttributeTargets.Method, Inherited = false, AllowMultiple = false)]
public class AntiXssAttribute : Attribute, IActionFilter
{

    private bool ContainsScript(string input)
    {
        string scriptPattern = @"<script\b[^>]*>(.*?)<\/script>";
        return Regex.IsMatch(input, scriptPattern, RegexOptions.IgnoreCase);
    }

    public void OnActionExecuting(ActionExecutingContext context)
    {
        foreach (var parameter in context.ActionArguments)
        {
            if (parameter.Value != null)
            {
                Type parameterType = parameter.Value.GetType();

                if (parameterType.IsClass && parameterType != typeof(string))
                {
                    // If the parameter is a custom class object, iterate through its properties.
                    foreach (var property in parameterType.GetProperties())
                    {
                        if (property.PropertyType == typeof(string))
                        {
                            string propertyValue = property.GetValue(parameter.Value) as string;

                            if (propertyValue != null)
                            {
                                if (ContainsScript(propertyValue))
                                {
                                    throw new AntiXssException("A Potentially dangerous request was detected.", 5005);
                                }
                            }

                        }
                    }
                }
                else if (parameter.Value is string stringValue)
                {
                    if (ContainsScript(stringValue))
                    {
                        throw new AntiXssException("A Potentially dangerous request was detected.", 5005);
                    }

                }
            }

        }
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {

    }
}