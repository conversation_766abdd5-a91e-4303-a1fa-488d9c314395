﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceHealthLogModel;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceHealthStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Queries.GetPaginatedList;

public class GetBusinessServiceHealthLogPaginatedListQueryHandler : IRequestHandler<
    GetBusinessServiceHealthLogPaginatedListQuery, PaginatedResult<BusinessServiceHealthLogListVm>>
{
    private readonly IBusinessServiceHealthLogRepository _drReadyLogRepository;
    private readonly IMapper _mapper;

    public GetBusinessServiceHealthLogPaginatedListQueryHandler(
        IBusinessServiceHealthLogRepository drReadyLogRepository, IMapper mapper)
    {
        _drReadyLogRepository = drReadyLogRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<BusinessServiceHealthLogListVm>> Handle(
        GetBusinessServiceHealthLogPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var productFilterSpec = new BusinessServiceHealthLogFilterSpecification(request.SearchString);

        var queryable = await _drReadyLogRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var businessServiceLog = _mapper.Map<PaginatedResult<BusinessServiceHealthLogListVm>>(queryable);

        return businessServiceLog;
        //var queryable = _drReadyLogRepository.GetPaginatedQuery();

        //var productFilterSpec = new BusinessServiceHealthLogFilterSpecification(request.SearchString);

        //var businessServiceLog = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<BusinessServiceHealthLogListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return businessServiceLog;
    }
}