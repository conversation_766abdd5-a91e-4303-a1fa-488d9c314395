﻿namespace ContinuityPatrol.Application.Features.FormHistory.Commands.Update;

public class UpdateFormHistoryCommandValidator : AbstractValidator<UpdateFormHistoryCommand>
{
    private readonly IFormHistoryRepository _formHistoryRepository;

    public UpdateFormHistoryCommandValidator(IFormHistoryRepository formHistoryRepository)
    {
        _formHistoryRepository = formHistoryRepository;

        //RuleFor(p => p.FormName)
        //    .NotEmpty().WithMessage("{PropertyName} is Required.")
        //    .NotNull()
        //    .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
        //    .WithMessage("Please Enter Valid {PropertyName}.")
        //    .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.LoginName)
            .NotEmpty().WithMessage("Please Enter the {PropertyName}.")
            .NotNull();

        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("Please Enter the {PropertyName}.")
            .NotNull();

        RuleFor(p => p.Type)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .Matches(@"^[a-zA-Z]*$").WithMessage("Please Enter Valid {PropertyName}.")
            .NotNull();

        RuleFor(p => p.Description)
            .NotEmpty().WithMessage("Please Enter the {PropertyName}.")
            .NotNull();

        RuleFor(p => p.Comments)
            .NotEmpty().WithMessage("Please Enter the {PropertyName}.")
            .NotNull();
    }
}