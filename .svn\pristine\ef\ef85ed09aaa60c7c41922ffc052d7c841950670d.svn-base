using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowHistoryFixture : IDisposable
{
    public List<WorkflowHistory> WorkflowHistoryPaginationList { get; set; }
    public List<WorkflowHistory> WorkflowHistoryList { get; set; }
    public WorkflowHistory WorkflowHistoryDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowHistoryFixture()
    {
        var fixture = new Fixture();

        WorkflowHistoryList = fixture.Create<List<WorkflowHistory>>();

        WorkflowHistoryPaginationList = fixture.CreateMany<WorkflowHistory>(20).ToList();

        WorkflowHistoryPaginationList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowHistoryList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowHistoryDto = fixture.Create<WorkflowHistory>();

        WorkflowHistoryDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
