﻿@* @model ContinuityPatrol.Domain.ViewModels.PRDRCompareModel.PRDRCompareViewModel *@

@*@using Microsoft.AspNetCore.Mvc.TagHelpers
@using ContinuityPatrol.Domain.ViewModels.PRDRCompareModel*@


@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link rel="stylesheet" type="text/css" href="~/css/Icon.css">

<link href="~/lib/lightbox/css/glightbox.css" rel="stylesheet" />

<style>

    .red-text {
        color: red;
    }


    .all-infra {
        display: inline-block;
        background-image: url(../img/icons/total.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 36px;
        height: 36px;
        position: absolute;
        right: 150px;
    }

    .impacted {
        display: inline-block;
        background-image: url(../img/icons/impacted.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 36px;
        height: 36px;
        position: absolute;
        right: 170px;
    }

    .non-impacted {
        display: inline-block;
        background-image: url(../img/icons/non-impacted.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 36px;
        height: 36px;
        position: absolute;
        right: 200px;
    }

</style>

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title" title="PRDRCompare List"><i class="cp-company"></i><span>PRDRCompare List</span></h6>
            <form class="d-flex gap-2 align-items-center">
                @*       <a type="button" title="Play Vieo" href="~/img/video/company.mp4" class="glightbox4"><i class="cp-circle-play fs-5"></i></a>*@
                <div class="input-group w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown" title="Filter">
                            <span data-bs-toggle="dropdown" ><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="Name">
                                        <label class="form-check-label" for="BusinessServiceName">
                                            Business Service Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="location=" id="Location">
                                        <label class="form-check-label" for="Name">
                                            InfraObject Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="platformtype=" id="PlatformType">
                                        <label class="form-check-label" for="PRServerName">
                                            Prod Server Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="companyname=" id="CompanyName">
                                        <label class="form-check-label" for="DRServerName">
                                            DR Server Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="type=" id="Type">
                                        <label class="form-check-label" for="PRServerId">
                                            Prod IP Address
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="type=" id="Type">
                                        <label class="form-check-label" for="DRServerId">
                                            DR IP Address
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>

                </div>
            </form>
        </div>

        <br />


       


         <div class="row">


            <div class="col-md-2" style="border-right: solid 2px #4a8bc2; width: 16%;">
                <span class="all-infra"></span>
                <div class="text-right text-ServerColor  text-medium" id="divBFAvail" title="Click here" style="line-height: 34px; cursor: pointer">
                    <button id="All" name="All Infra" style="margin-left: 10px; text-decoration: none; font-size: 20px; border: none; background-color: #ffffff; vertical-align: top;">All infra</button>&nbsp;&nbsp;

                    <span id="drTotalBusiness" class="align-middle bg-primary-subtle text-primary badge bg-primary fs-5" role="button">
                        @if (@Model.PRDRServerImpactList != null)
                        {
                            @Model.PRDRServerImpactList.Count()
                        }
                    </span>



                </div>

            </div>
          
            <div class="col-md-2" style="border-right: solid 2px #4a8bc2; width: 16%;">
                <span class="nonimpacted"></span>
                <div class="text-right text-ServerColor  text-medium" id="divBFAvail" title="Click here" style="line-height: 34px; cursor: pointer">
                    <button id="non_impact" name="All Infra" style="margin-left: 10px;color:forestgreen; border: none; background-color: #ffffff; vertical-align: top; font-size: 20px;">Non-Impact Infra  </button>&nbsp;&nbsp;
                    <span id="drReadyBusiness" class="align-middle bg-success-subtle text-success badge bg-primary fs-5" role="button">
                        @* @if (@Model.PRDRServerImpactList != null)
                        {
                            @Model.PRDRServerImpactList.Where(v => v.IsImpact == 0).Count()
                        } *@
                    </span>

                </div>
            </div>

              <div class="col-md-2" style="width: 17%;">
                <span class="impacted"></span>
                <div class="text-right text-ServerColor  text-medium" id="divBFAvail1" title="Click here" style="line-height: 34px; cursor: pointer">
                    <button ID="impact" runat="server" Style="margin-left: 10px; border: none; background-color: #ffffff; vertical-align: top; color: red; font-size: 20px;" >
                        Impact Infra
                    </button>&nbsp;&nbsp;
                    <span id="drNotReadyBusiness" class="align-middle bg-danger-subtle text-danger badge bg-primary fs-5" role="button">
                        @* @if (@Model.PRDRServerImpactList != null)
                        {
                            @Model.PRDRServerImpactList.Where(v => v.IsImpact == 1).Count()
                        } *@
                    </span>

                </div>
            </div>
        </div>




        <br /><br />
        <div class="card-body pt-0">
            <table id="tblPRDRCompare" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead class="TableThead">
                    <tr>
                        <th class="SrNo_th" title="Sr. No">Sr. No.</th>
                        <th title="Business Service Name">Business Service Name</th>
                        <th title="InfraObject Namee">InfraObject Name</th>
                        <th title="Prod Server Name">Prod Server Name</th>
                        <th class="DR Server Name">DR Server Name</th>
                        <th class="Prod IP Address">Prod IP Address</th>
                        <th class="DR IP Address">DR IP Address</th>
                        <th class="View Prod-DR Information" title="Action">View Prod-DR Information</th>
                    </tr>
                </thead>
                <tbody id="table-body_01">
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="modal fade" id="myModal" role="dialog">
    <div class="modal-dialog modal-lg modal-dialog-scrollable modal-dialog-centered Organization_modal">
        <from class="modal-content" asp-controller="PRDRCompare" asp-antiforgery="true" id="ref" asp-action="Details" method="get" class="tab-wizard wizard-circle wizard clearfix">
            <div class="modal-content">
                <div class="modal-header">

                    <h6 class="page_title" title="Server List"><i class="cp-server"></i><span>  Compare Prod DR Server Details </span></h6>
                    <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="row modal-body py-0" style="width:800px;padding: 25px 25px 41px 80px !important;">
                    @*<from asp-action="Details" >*@

                    <div class="col-md-6" style="margin-top: 20px; margin-bottom:10px">
                        <h4 class="text-primary" style="font-weight: bold; padding-left: 40px;">Prod Site</h4>
                    </div>
                    @*</div>*@
                    <div class="col-md-6" style="margin-top: 20px; margin-bottom:10px;">
                        <h4 class="text-primary" style="font-weight: bold; padding-left: 24px;">DR Site</h4>
                    </div>
                    <table class="border-2" style="margin-bottom: 20px !important;">
                        <thead class="TableThead" style="padding:7px !important;">
                            <tr>
                                <th class="SrNo_th" title="Sr. No">Sr. No.</th>
                                <th title="PR Configuration Name">Configuration Details</th>
                                <th title="DR ConfigurationName">Configuration Details</th>
                            </tr>
                        </thead>
                        <tbody id="table-body" style="padding:7px !important;">
                        </tbody>
                    </table>

                </div>
            </div>


        </from>
    </div>
</div>



@*</div>*@


@*@section Scripts
    {
    <partial name="_ValidationScriptsPartial" />
}
*@
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
@*<script src="~/js/PRDRCompare.js"></script>*@
<script src="~/js/common/wizard.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>

<script>

//$(document).ready(function () {
         $(window).on('load', function() {
       
        console.log("Document ready!");
        var selectedtype;
        var selectedValues = [];
        var useSelectedType = false;
        var dataTable = $('#tblPRDRCompare').DataTable({
            // ... Existing DataTable configuration ...
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "ajax": {
                // ... Existing ajax configuration ...
                "type": "GET",
                "url": "/Configuration/PRDRCompare/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    console.log("Ajax data function:", d);
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    //d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    //d.searchString = selectedtype;
              if (useSelectedType) {
                    d.searchString = selectedtype;  // Use selectedtype
                } else 
                {
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');  // Use search-inp
                }


                    selectedValues.length = 0;
                },
                //success: function (data, status, xhr) {
                //    alert(data);
                //},

                //var json = JSON.parse(json);
                "dataSrc": function (json) {
                    //var test = JSON.parse(json);
                    console.log("Ajax dataSrc function:", json);
                    json.recordsTotal = json.totalPages;
                    json.recordsFiltered = json.totalCount;
                    debugger;
                    if (json.data.length === 0) {
                        $(".pagination-column").addClass("disabled")
                        /*  $(".TableThead").addClass("d-none")*/

                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                        //$(".TableThead").removeClass("d-none")
                    }
                    return json.data;
                }
            },

            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            /*return meta.row + 1;*/
                            var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                            return (page * meta.settings._iDisplayLength) + meta.row + 1;
                        }
                        return data;
                    }
                },
                {
                    "data": "businessServiceName", "name": "Business Service Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                //{
                //    "data": "type", "name": "Type", "autoWidth": true,
                //    "render": function (data, type, row) {
                //        var icon;
                //        switch (data.toLowerCase()) {
                //            case "prsite":
                //                icon = "cp-prsites";
                //                break;
                //            case "drsite":
                //                icon = "cp-physical-drsite";
                //                break;
                //            case "neardrsite":
                //                icon = "cp-physical-neardrsite";
                //                break;
                //            default:
                //                icon = "cp-custom-server-4"
                //                break;
                //        }

                //        //if (icon) {
                //        //    return `
                //        //    <i class="${icon} me-1 fs-5"></i>  ${data} `;
                //        //}
                //        if (icon) {
                //            if (type === 'display' && data) {
                //                return `<span title="${data}"><i class="${icon} me-1 fs-5"></i>${data}</span>`;
                //            }
                //        }

                //        if (type === 'display') {
                //            return '<span title="' + data + '">' + data + '</span>';
                //        }
                //        return data;
                //    }
                //},
                {
                    "data": "infraObjectName", "name": "InfraObject Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "prServerName", "name": "Prod Server Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "drServerName", "name": "DR Server Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "pripAddress", "name": "Prod IP Address", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "dripAddress", "name": "DR IP Address", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                //{
                //    "data": "businessServiceName", "name": "View Prod-DR Information", "autoWidth": true,
                //    "render": function (data, type, row) {
                //        if (type === 'display') {
                //            return '<span title="' + data + '">' + data + '</span>';
                //        }
                //        return data;
                //    }
                //}
                {
                    "data": "isImpact", "name": "View Prod-DR Information", "autoWidth": true,
                    "render": function (data, type, row) {
                        var icon;
                        switch (data) {
                            case 1:
                                icon = "cp-success me-2 text-success fw-bold";
                                break;
                            case 0:
                                icon = "cp-error me-2 text-danger fw-bold";
                                break;
                            case "":
                                icon = "cp-error me-2 text-danger fw-bold";
                                break;

                        }
                        if (icon) {
                            //if (type === 'display' && data)
                            if (type === 'display' && (data === 1 || data === 0)) {
                                //const functionName = 'SaveFunction('+ row.id.trim() + ')';
                                //const functionName = 'SaveFunction(' + '"' + row.id.starttrim() + '"' + ')';
                                //const functionName = `SaveFunction("${row.id.trim()}")`;
                                //console.log(`Row ID:"${row.id}"`);
                                //const siteId = row.id;
                                //onClick="gotoNode(' + result.name + ')" />'

                                return `<span title="${data}" role="button" data-site-id="${row.infraObjectId}"  onclick="SaveFunction('${row.infraObjectId}')" <i class="${icon} me-1 fs-5"></i></span>`;
                            }
                        }

                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                }


                // ... Existing columns configuration ...
            ]
        });

         //function FilterData(btn) {
           
         $('#All').on('click', function () {

             
         
               selectedtype = "All";
               useSelectedType = true;
                 dataTable.search(selectedtype).draw();
             
           
           });
             $('#impact').on('click', function () {

             
         
               selectedtype = "impact";
               useSelectedType = true;
             dataTable.search(selectedtype).draw();
             
           
           });
            $('#non_impact').on('click', function () {

             
         
               selectedtype = "nonimpact";
               useSelectedType = true;
             dataTable.search(selectedtype).draw();
             
           
           });
 

        $(document).on('keyup', '#search-inp', function () {
            console.log("Keyup event triggered!");
                 var useSelectedType = false;
                 selectedtype = "";
            $('input[type="checkbox"]').each(function () {
                if ($(this).is(':checked')) {
                    var checkboxValue = $(this).val();
                    var inputValue = $('#search-inp').val();
                    selectedValues.push(checkboxValue + inputValue);
                }
            });
            dataTable.search($(this).val()).draw();
        });

    



    
    
    });
    //$('#tblPRDRCompare').on('click', '.recordRow', function () {

    //        $("#SaveFunction").click(async function () {
    //            debugger;
    //        console.log("Row click event triggered!");
    //        var selectedId = $(this).data('id');
    //        // ... Existing click event logic ...
    //        $.ajax({
    //            url: '/Configuration/PRDRCompare/GetDetails',
    //            type: 'GET',
    //            dataType: 'json',
    //            data: { id: selectedId },
    //            success: function (response) {
    //                // Populate the details table with the received data
    //                var parsedResponse = JSON.parse(response);
    //                var detailsTableContent = '';
    //                //parsedResponse.forEach(function (detail) {
    //                //detailsTableContent += '<tr>' +
    //                //    '<td>' + parsedResponse.DRIPAddress + '</td>' +
    //                //    '<td>' + parsedResponse.PRIPAddress + '</td>' +
    //                //    // Add other detail columns as needed
    //                //    '</tr>';
    //                debugger;
    //                detailsTableContent +=

    //                    '<tr>' +

    //                    '<td>' + "1" + '</td>' +

    //                    '<td>' + "Prod IP Address: " + parsedResponse.PRIPAddress + '</td>' +

    //                    '<td>' + "DR IP Address: " + parsedResponse.DRIPAddress + '</td>' +
    //                    '</tr>' +

    //                    '<tr>' +
    //                    '<td>' + "2" + '</td>' +

    //                    '<td>' + "Prod System Name: " + parsedResponse.PRSystemName + '</td>' +

    //                    '<td>' + "DR System Name: " + parsedResponse.DRSystemName + '</td>' +

    //                    '</tr>' +

    //                    '<td>' + "3" + '</td>' +
    //                    '<td>' + "Prod OS Name: " + parsedResponse.PROSName + '</td>' +
    //                    '<td class="' + (parsedResponse.PROSName === parsedResponse.DROSName ? '' : 'red-text') + '">' +
    //                    "DR OS Name: " + parsedResponse.DROSName +
    //                    '</td>' +

    //                    '</tr>' +







    //                    '<tr>' +
    //                    '<td>' + "4" + '</td>' +

    //                    '<td>' + "Prod Build Version: " + parsedResponse.PRBuildVersion + '</td>' +

    //                    '<td class="' + (parsedResponse.PRBuildVersion === parsedResponse.DRBuildVersion ? '' : 'red-text') + '">' +
    //                    "DR Build Version: " + parsedResponse.DRBuildVersion +
    //                    '</td>' +

    //                    '</tr>' +

    //                    '<tr>' +
    //                    '<td>' + "5" + '</td>' +

    //                    '<td>' + "Prod Processor: " + parsedResponse.PRProcessor + '</td>' +

    //                    '<td class="' + (parsedResponse.PRProcessor === parsedResponse.DRProcessor ? '' : 'red-text') + '">' +
    //                    "DR Processor: " + parsedResponse.DRProcessor +
    //                    '</td>' +

    //                    '</tr>' +



    //                    '<tr>' +
    //                    '<td>' + "6" + '</td>' +

    //                    '<td>' + "Prod Memory: " + parsedResponse.PRMemory + '</td>' +

    //                    '<td class="' + (parsedResponse.PRMemory === parsedResponse.DRMemory ? '' : 'red-text') + '">' +
    //                    "DR Processor: " + parsedResponse.DRMemory +
    //                    '</td>' +


    //                    '</tr>' +

    //                    '<tr>' +
    //                    '<td>' + "7" + '</td>' +

    //                    '<td>' + "Prod Local Drive Name: " + parsedResponse.PRLocalDrive + '</td>' +


    //                    '<td class="' + (parsedResponse.PRLocalDrive === parsedResponse.DRLocalDrive ? '' : 'red-text') + '">' +
    //                    "DR Local Drive Name: " + parsedResponse.DRLocalDrive +
    //                    '</td>' +

    //                    '</tr>' +

    //                    '<tr>' +
    //                    '<td>' + "8" + '</td>' +

    //                    '<td>' + "Prod Shared Drive:" + parsedResponse.PRSharedDrive + '</td>' +

    //                    '<td>' + "DR Shared Drive:" + parsedResponse.DRSharedDrive + '</td>' +

    //                    '</tr>';

    //                //});
    //                $('#table-body').html(detailsTableContent);
    //                $('#myModal').modal('show');
    //            },
    //            error: function (xhr, status, error) {
    //                console.error(error);
    //            }
    //        });

    //    });
    //});

</script>

<style>

    .blockval {
        display: inline-block;
    }

    .keyval {
        width: 40px;
    }

    .valueval {
        width: 40px;
    }

</style>

<script>
    function SaveFunction(rowid) {
        debugger;
        console.log("Row click event triggered!");
        var selectedId = rowid;

        // ... Existing click event logic ...
        $.ajax({
            url: '/Configuration/PRDRCompare/GetDetails',
            type: 'GET',
            dataType: 'json',
            data: { id: selectedId },
            success: function (response) {
                var parsedResponse = JSON.parse(response);
                var detailsTableContent = '';

                debugger;



                detailsTableContent +=

                    '<tr>' +

                    '<td>' + "1" + '</td>' +

                    '<td>' + " Prod IP Address &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : " + parsedResponse.PRIPAddress + '</td>' +

                    '<td>' + "DR IP Address &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;: " + parsedResponse.DRIPAddress + '</td>' +
                    '</tr>' +

                    '<tr>' +
                    '<td>' + "2" + '</td>' +

                    '<td>' + "Prod System Name &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;   : " + parsedResponse.PRSystemName + '</td>' +

                    '<td>' + "DR System Name    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  : " + parsedResponse.DRSystemName + '</td>' +

                    '</tr>' +

                    '<td>' + "3" + '</td>' +
                    '<td>' + "Prod OS Name    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;   &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;    : " + parsedResponse.PROSName + '</td>' +
                    '<td class="' + (parsedResponse.PROSName === parsedResponse.DROSName ? '' : 'red-text') + '">' +
                    "DR OS Name &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : " + parsedResponse.DROSName +
                    '</td>' +

                    '</tr>' +







                    '<tr>' +
                    '<td>' + "4" + '</td>' +

                    '<td>' + "Prod Build Version  &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : " + parsedResponse.PRBuildVersion + '</td>' +

                    '<td class="' + (parsedResponse.PRBuildVersion === parsedResponse.DRBuildVersion ? '' : 'red-text') + '">' +
                    "DR Build Version &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : " + parsedResponse.DRBuildVersion +
                    '</td>' +

                    '</tr>' +

                    '<tr>' +
                    '<td>' + "5" + '</td>' +

                    '<td>' + "Prod Processor &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : " + parsedResponse.PRProcessor + '</td>' +

                    '<td class="' + (parsedResponse.PRProcessor === parsedResponse.DRProcessor ? '' : 'red-text') + '">' +
                    "DR Processor &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : " + parsedResponse.DRProcessor +
                    '</td>' +

                    '</tr>' +



                    '<tr>' +
                    '<td>' + "6" + '</td>' +

                    '<td>' + "Prod Memory &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : " + parsedResponse.PRMemory + '</td>' +

                    '<td class="' + (parsedResponse.PRMemory === parsedResponse.DRMemory ? '' : 'red-text') + '">' +
                    "DR Memory &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : " + parsedResponse.DRMemory +
                    '</td>' +


                    '</tr>' +

                    '<tr>' +
                    '<td>' + "7" + '</td>' +

                    '<td>' + "Prod Local Drive Name &nbsp;&nbsp;&nbsp;&nbsp; : " + parsedResponse.PRLocalDrive + '</td>' +


                    '<td class="' + (parsedResponse.PRLocalDrive === parsedResponse.DRLocalDrive ? '' : 'red-text') + '">' +
                    "DR Local Drive Name  &nbsp;&nbsp;&nbsp;&nbsp; : " + parsedResponse.DRLocalDrive +
                    '</td>' +

                    '</tr>' +

                    '<tr>' +
                    '<td>' + "8" + '</td>' +

                    '<td>' + "Prod Shared Drive &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : " + parsedResponse.PRSharedDrive + '</td>' +

                    '<td>' + "DR Shared Drive &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : " + parsedResponse.DRSharedDrive + '</td>' +

                    '</tr>';

                //detailsTableContent +=
                //    "<tr>"
                //    + "<td> 1 </td>"
                //    + "<td><span class='blockval keyval'>Prod IP Address</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.PRIPAddress + "</span></td>"
                //    + "<td><span class='blockval keyval'>DR IP Address</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.DRIPAddress + "</span></td>"
                //    + "</tr><tr>";
                //    + "<td> 2 </td>"
                //    + "<td><span class='blockval keyval'>Prod System Name</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.PRSystemName + "</span></td>"
                //    + "<td><span class='blockval keyval'>DR System Name</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.DRSystemName + "</span></td>"
                //    + "</tr><tr>";
                //    + "<td> 3 </td>"
                //    + "<td><span class='blockval keyval'>Prod OS Name</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.PROSName + "</span></td>"
                //    + "<td><span class='blockval keyval'>DR OS Name</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.DROSName + "</span></td>"
                //    + "</tr><tr>";
                //    + "<td> 4 </td>"
                //    + "<td><span class='blockval keyval'>Prod Build Version</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.PRBuildVersion + "</span></td>"
                //    + "<td><span class='blockval keyval'>DR Build Version</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.DRBuildVersion + "</span></td>"
                //    + "</tr><tr>";
                //    + "<td> 5 </td>"
                //    + "<td><span class='blockval keyval'>Prod Processor</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.PRProcessor + "</span></td>"
                //    + "<td><span class='blockval keyval'>DR Processor</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.DRProcessor + "</span></td>"
                //    + "</tr><tr>";
                //    + "<td> 6 </td>"
                //    + "<td><span class='blockval keyval'>Prod Memory</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.PRMemory + "</span></td>"
                //    + "<td><span class='blockval keyval'>DR Memory</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.DRMemory + "</span></td>"
                //    + "</tr><tr>";
                //    + "<td> 7 </td>"
                //    + "<td><span class='blockval keyval'>Prod Local Drive Name</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.PRLocalDrive + "</span></td>"
                //    + "<td><span class='blockval keyval'>DR Local Drive Name</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.DRLocalDrive + "</span></td>"
                //    + "</tr><tr>";
                //    + "<td> 8 </td>"
                //    + "<td><span class='blockval keyval'>Prod Shared Drive</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.PRSharedDrive + "</span></td>"
                //    + "<td><span class='blockval keyval'>DR Shared Drive</span><span class='blockval'> : </span><span class='blockval valueval'>" + parsedResponse.DRSharedDrive + "</span></td>"
                //    + "</tr>";





                $('#table-body').html(detailsTableContent);
                $('#myModal').modal('show');
            },
            error: function (xhr, status, error) {
                console.error(error);
            }
        });
    }




        //detailsTableContent +=

                //    '<tr>' +

                //    '<td>' + "1" + '</td>' +

                //    '<td>' + '"<span>style="width: 124px; display: inline - block;" Prod IP Address</span> : ' + parsedResponse.PRIPAddress + '</td>' +

                //    '<td>' + "DR IP Address &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;: " + parsedResponse.DRIPAddress + '</td>' +
                //    '</tr>' +

                //    '<tr>' +
                //    '<td>' + "2" + '</td>' +

                //    '<td>' + "Prod System Name &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;   : " + parsedResponse.PRSystemName + '</td>' +

                //    '<td>' + "DR System Name    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;  : " + parsedResponse.DRSystemName + '</td>' +

                //    '</tr>' +

                //    '<td>' + "3" + '</td>' +
                //    '<td>' + "Prod OS Name    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;   &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;    : " + parsedResponse.PROSName + '</td>' +
                //    '<td class="' + (parsedResponse.PROSName === parsedResponse.DROSName ? '' : 'red-text') + '">' +
                //    "DR OS Name &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;: " + parsedResponse.DROSName +
                //    '</td>' +

                //    '</tr>' +







                //    '<tr>' +
                //    '<td>' + "4" + '</td>' +

                //    '<td>' + "Prod Build Version  &nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :" + parsedResponse.PRBuildVersion + '</td>' +

                //    '<td class="' + (parsedResponse.PRBuildVersion === parsedResponse.DRBuildVersion ? '' : 'red-text') + '">' +
                //    "DR Build Version &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;: " + parsedResponse.DRBuildVersion +
                //    '</td>' +

                //    '</tr>' +

                //    '<tr>' +
                //    '<td>' + "5" + '</td>' +

                //    '<td>' + "Prod Processor &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:" + parsedResponse.PRProcessor + '</td>' +

                //    '<td class="' + (parsedResponse.PRProcessor === parsedResponse.DRProcessor ? '' : 'red-text') + '">' +
                //    "DR Processor: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; " + parsedResponse.DRProcessor +
                //    '</td>' +

                //    '</tr>' +



                //    '<tr>' +
                //    '<td>' + "6" + '</td>' +

                //    '<td>' + "Prod Memory &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:" + parsedResponse.PRMemory + '</td>' +

                //    '<td class="' + (parsedResponse.PRMemory === parsedResponse.DRMemory ? '' : 'red-text') + '">' +
                //    "DR Processor &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:" + parsedResponse.DRMemory +
                //    '</td>' +


                //    '</tr>' +

                //    '<tr>' +
                //    '<td>' + "7" + '</td>' +

                //    '<td>' + "Prod Local Drive Name &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;: " + parsedResponse.PRLocalDrive + '</td>' +


                //    '<td class="' + (parsedResponse.PRLocalDrive === parsedResponse.DRLocalDrive ? '' : 'red-text') + '">' +
                //    "DR Local Drive Name  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:" + parsedResponse.DRLocalDrive +
                //    '</td>' +

                //    '</tr>' +

                //    '<tr>' +
                //    '<td>' + "8" + '</td>' +

                //    '<td>' + "Prod Shared Drive &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:" + parsedResponse.PRSharedDrive + '</td>' +

                //    '<td>' + "DR Shared Drive &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:" + parsedResponse.DRSharedDrive + '</td>' +

                //    '</tr>';
</script>


