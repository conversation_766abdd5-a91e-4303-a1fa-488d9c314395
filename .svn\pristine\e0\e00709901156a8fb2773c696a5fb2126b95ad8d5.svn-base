﻿using ContinuityPatrol.Application.Features.LogViewer.Commands.Create;
using ContinuityPatrol.Application.Features.LogViewer.Commands.Update;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.LogViewerModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ILogViewerService
{
    Task<PaginatedResult<LogViewerListVm>> GetPaginatedLogViewerList(GetLogViewerPaginatedListQuery query);
    Task<BaseResponse> CreateAsync(CreateLogViewerCommand createLogViewer);
    Task<BaseResponse> UpdateAsync(UpdateLogViewerCommand updateLogViewer);
    Task<BaseResponse> DeleteAsync(string id);
    Task<List<LogViewerListVm>> GetLogViewerList();
    Task<GetLogViewerDetailVm> GetLogViewerDetail(string id);
    Task<bool> IsLogViewerNameUnique(string name, string id);
}