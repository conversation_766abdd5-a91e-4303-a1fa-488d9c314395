﻿using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Infrastructure.Contract;

namespace ContinuityPatrol.Application.UnitTests.Features.LoadBalancer.Commands;

public class CreateLoadBalancerTests : IClassFixture<LoadBalancerFixture>
{
    private readonly LoadBalancerFixture _loadBalancerFixture;

    private readonly Mock<ILoadBalancerRepository> _mockLoadBalancerRepository;

    private readonly CreateLoadBalancerCommandHandler _handler;

    public CreateLoadBalancerTests(LoadBalancerFixture loadBalancerFixture)
    {
        _loadBalancerFixture = loadBalancerFixture;

        Mock<IPublisher> mockPublisher = new();

        //var backgroundJobClient = new Mock<IBackgroundJobClient>();

        //var mockWindowsService = new Mock<IWindowsService>();

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        _mockLoadBalancerRepository = LoadBalancerRepositoryMocks.CreateLoadBalancerRepository(_loadBalancerFixture.LoadBalancers);

        _handler = new CreateLoadBalancerCommandHandler(_loadBalancerFixture.Mapper, _mockLoadBalancerRepository.Object, mockLoggedInUserService.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Should_Increase_Count_When_AddValid_LoadBalancer()
    {
        await _handler.Handle(_loadBalancerFixture.CreateLoadBalancerCommand, CancellationToken.None);

        var allCategories = await _mockLoadBalancerRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_loadBalancerFixture.LoadBalancers.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulLoadBalancerResponse_When_AddValidLoadBalancer()
    {
        var result = await _handler.Handle(_loadBalancerFixture.CreateLoadBalancerCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateLoadBalancerResponse));

        //result.LoadBalancerId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulLoadBalancerResponse_When_AddValidLoadBalancer_InActiveState()
    {
        var loadbalencer = new Fixture().Create<Domain.Entities.LoadBalancer>();
        var loadbalence = new Domain.Entities.LoadBalancer();
        _mockLoadBalancerRepository.Setup(dp => dp.AddAsync(loadbalencer)).ReturnsAsync(loadbalence);
        var result = await _handler.Handle(_loadBalancerFixture.CreateLoadBalancerCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateLoadBalancerResponse));

        //result.LoadBalancerId.ShouldBeGreaterThan(0.ToString());

        Assert.True(result.Success);
    }

    //[Fact]
    //public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    //{
    //    await _handler.Handle(_loadBalancerFixture.CreateLoadBalancerCommand, CancellationToken.None);

    //    _mockLoadBalancerRepository.Verify(x => x.AddAsync(It.IsAny<Entities.LoadBalancer>()), Times.Once);
    //}

}