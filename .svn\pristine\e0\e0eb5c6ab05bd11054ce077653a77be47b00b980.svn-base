using AutoFixture;
using ContinuityPatrol.Application.Features.DriftProfile.Commands.Create;
using ContinuityPatrol.Application.Features.DriftProfile.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftProfile.Commands.Update;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.DriftProfileModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DriftProfileFixture : IDisposable
{
    public List<DriftProfileListVm> DriftProfileListVm { get; set; }
    public List<DriftProfile> DriftProfiles { get; set; }
    public DriftProfileDetailVm DriftProfileDetailVm { get; set; }
    public CreateDriftProfileCommand CreateDriftProfileCommand { get; set; }
    public CreateDriftProfileResponse CreateDriftProfileResponse { get; set; }
    public UpdateDriftProfileCommand UpdateDriftProfileCommand { get; set; }
    public UpdateDriftProfileResponse UpdateDriftProfileResponse { get; set; }
    public DeleteDriftProfileCommand DeleteDriftProfileCommand { get; set; }
    public DeleteDriftProfileResponse DeleteDriftProfileResponse { get; set; }
    public GetDriftProfilePaginatedListQuery GetDriftProfilePaginatedListQuery { get; set; }
    public PaginatedResult<DriftProfileListVm> DriftProfilePaginatedResult { get; set; }
    public GetDriftProfileNameUniqueQuery GetDriftProfileNameUniqueQuery { get; set; }

    public DriftProfileFixture()
    {
        DriftProfileListVm = AutoDriftProfileFixture.Create<List<DriftProfileListVm>>();
        DriftProfileDetailVm = AutoDriftProfileFixture.Create<DriftProfileDetailVm>();
        CreateDriftProfileCommand = AutoDriftProfileFixture.Create<CreateDriftProfileCommand>();
        CreateDriftProfileResponse = AutoDriftProfileFixture.Create<CreateDriftProfileResponse>();
        UpdateDriftProfileCommand = AutoDriftProfileFixture.Create<UpdateDriftProfileCommand>();
        UpdateDriftProfileResponse = AutoDriftProfileFixture.Create<UpdateDriftProfileResponse>();
        DeleteDriftProfileCommand = AutoDriftProfileFixture.Create<DeleteDriftProfileCommand>();
        DeleteDriftProfileResponse = AutoDriftProfileFixture.Create<DeleteDriftProfileResponse>();
        DriftProfiles = AutoDriftProfileFixture.Create<List<DriftProfile>>();
        GetDriftProfilePaginatedListQuery = AutoDriftProfileFixture.Create<GetDriftProfilePaginatedListQuery>();
        DriftProfilePaginatedResult = AutoDriftProfileFixture.Create<PaginatedResult<DriftProfileListVm>>();
        GetDriftProfileNameUniqueQuery = AutoDriftProfileFixture.Create<GetDriftProfileNameUniqueQuery>();
    }

    public Fixture AutoDriftProfileFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customize<CreateDriftProfileCommand>(c => c
                .With(b => b.Name, "Enterprise Drift Profile")
                .With(b => b.Properties, "SOX Compliance Configuration Properties")
                .With(b => b.ComponentTypeId, Guid.NewGuid().ToString));

            fixture.Customize<UpdateDriftProfileCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.Name, "Updated Enterprise Drift Profile")
                .With(b => b.Properties, "Updated SOX Compliance Configuration Properties")
                .With(b => b.ComponentTypeId, Guid.NewGuid().ToString));

            fixture.Customize<DeleteDriftProfileCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString));

            fixture.Customize<CreateDriftProfileResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.Success, true));

            fixture.Customize<UpdateDriftProfileResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.Success, true));

            fixture.Customize<DeleteDriftProfileResponse>(c => c
                .With(b => b.Success, true)
                .With(b => b.Message, "Drift Profile deleted successfully")
                .With(b => b.IsActive, false));

            // View Models - Combine all DriftProfileDetailVm customizations to avoid overriding
            fixture.Customize<DriftProfileDetailVm>(c => c
                .With(b => b.Name, "Enterprise Drift Profile Detail")
                .With(b => b.Properties, "SOX_GDPR_Compliance_Properties")
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.ComponentTypeId, Guid.NewGuid().ToString));

            fixture.Customize<DriftProfile>(c => c.With(b => b.IsActive, true));
            fixture.Customize<DriftProfile>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString));

            // DriftProfileListVm customizations
            fixture.Customize<DriftProfileListVm>(c => c
                .With(b => b.Name, "Enterprise Drift Profile List")
                .With(b => b.Properties, "SOX Compliance Configuration Properties")
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.ComponentTypeId, Guid.NewGuid().ToString));

            fixture.Customize<GetDriftProfilePaginatedListQuery>(c => c.With(b => b.PageNumber, 1));
            fixture.Customize<GetDriftProfilePaginatedListQuery>(c => c.With(b => b.PageSize, 10));

            fixture.Customize<GetDriftProfileNameUniqueQuery>(c => c.With(b => b.Id, Guid.NewGuid().ToString));

            fixture.Customize<PaginatedResult<DriftProfileListVm>>(c => c.With(b => b.Succeeded, true));
            fixture.Customize<PaginatedResult<DriftProfileListVm>>(c => c.With(b => b.PageSize, 10));
            fixture.Customize<PaginatedResult<DriftProfileListVm>>(c => c.With(b => b.CurrentPage, 1));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}
