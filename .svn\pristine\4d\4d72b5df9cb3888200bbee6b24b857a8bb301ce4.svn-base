﻿// QUnit test file: test-company.js
QUnit.module("Company Module Tests", hooks => {
    hooks.before(() => {
        sinon.stub($, "get").callsFake((url, data) => {
            const isDuplicate = data.name.toLowerCase().includes("duplicate");
            return {
                done: function (callback) {
                    callback({
                        success: true,
                        data: isDuplicate ? true : false
                    });
                    return this;
                }
            };
        });

        sinon.stub(window, 'FileReader').callsFake(function () {
            this.readAsDataURL = function () {
                this.onload({ target: { result: 'data:image/png;base64,fakebase64' } });
            };
        });
    });

    hooks.after(() => {
        $.get.restore();
        window.FileReader.restore();
    });

    // QUnit test for validateName with random dynamic inputs covering all validations

    QUnit.module("validateName - Full Validation Coverage", hooks => {
        hooks.beforeEach(() => {
            $('#qunit-fixture').append('<div id="companyNameError"></div>');
        });

        const generateRandomName = () => {
            const parts = [
                "Alpha", "Beta_1", "123Start", "--dash", "Dot..Dot", "ValidName",
                "Name_End_", "_Start", " NameSpace", "TooLong" + "a".repeat(100),
                "", "<script>", "Middle Space", "Two__Underscore", "Under Score",
                "Ab", "A B", "Middle--Hyphen", "Middle..Dot"
            ];
            return parts[Math.floor(Math.random() * parts.length)];
        };

        QUnit.test("validateName - dynamic tests", async assert => {
            const errorElement = $('#companyNameError');

            for (let i = 0; i < 250; i++) {
                const testName = generateRandomName();
                errorElement.text('');

                const result = await validateName(testName, null, '/fake-url',errorElement,'Enter company name','Name already exists');

                const msg = `${result === true ? '✅' : '❌'} Test ${i + 1}: '${testName}' → ${result === true ? 'Passed' : 'Failed'} | Message: ${errorElement.text()} | returns ${result}`;

                assert.strictEqual(typeof result === 'boolean' || typeof result === 'string', true, msg);
            }
        });
    });


    QUnit.module("Dynamic validateDisplayName Tests", hooks => {
        const url = "/mock-url";
        const mockErrorDisplayText = "Name already exists";
        // Backup original $.get
        let originalAjax;

        hooks.before(() => {
            // Save the original jQuery $.get method
            originalAjax = $.get;

            // Mock $.get to simulate backend response
            $.get = (url, data) => {
                return {
                    done(callback) {
                        const mockExists = Math.random() < 0.5;
                        callback({
                            success: true,
                            data: false
                        });
                        return this;
                    }
                };
            };
        });

        hooks.after(() => {
            $.get = originalAjax;
        });

        function generateRandomInput() {
            const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-. ";
            let name = "";
            const length = Math.floor(Math.random() * 20) + 3;

            for (let i = 0; i < length; i++) {
                name += chars.charAt(Math.floor(Math.random() * chars.length));
            }

            if (Math.random() < 0.2) name = "_" + name;
            if (Math.random() < 0.2) name = "<" + name;
            if (Math.random() < 0.2) name = name + ">" ;
            if (Math.random() < 0.2) name = "  " + name;
            if (Math.random() < 0.2) name += "--";
            if (Math.random() < 0.2) name += "__";
            if (Math.random() < 0.2) name = name.replace(" ", " _");

            return name.trim();
        }

        QUnit.test("validateDisplayName - with dynamic inputs", async assert => {
            const totalTests = 100;
            const $mockError = $("<div>");

            for (let i = 0; i < totalTests; i++) {
                const input = generateRandomInput();
                const result = await validateDisplayName(input, null, url, $mockError, mockErrorDisplayText, mockErrorDisplayText);
                const msg = result === true
                    ? `✔️ [${i + 1}] PASSED: '${input}'`
                    : `❌ [${i + 1}] FAILED: '${input}' => ${$mockError.text()}`;
                assert.ok(true, msg);
            }
        });
    });


    QUnit.module("WebAddressValidate — dynamic regex tests", hooks => {
        // Helper: call WebAddressValidate directly
        const validate = WebAddressValidate;

        // Generate random valid hostnames with optional scheme, www., port, path, query, fragment
        function generateValidWeb() {
            const schemes = ["", "http://", "https://", "ftp://"];
            const prefixes = ["", "www."];
            const names = ["example", "my-site", "test123", "sub.domain"];
            const tlds = [".com", ".org", ".net", ".io"];
            const ports = ["", `:${Math.floor(Math.random() * 9000) + 1000}`];
            const paths = ["", "/path", "/a/b"];
            const queries = ["", "?q=1", "?a=b&c=d"];
            const fragments = ["", "#top", "#section-2"];

            return [
                schemes[Math.floor(Math.random() * schemes.length)],
                prefixes[Math.floor(Math.random() * prefixes.length)],
                names[Math.floor(Math.random() * names.length)],
                tlds[Math.floor(Math.random() * tlds.length)],
                ports[Math.floor(Math.random() * ports.length)],
                paths[Math.floor(Math.random() * paths.length)],
                queries[Math.floor(Math.random() * queries.length)],
                fragments[Math.floor(Math.random() * fragments.length)]
            ].join("");
        }

        // Generate random invalid web addresses by injecting spaces or invalid chars
        function generateInvalidWeb() {
            const chars = " !@#$%^&*()+={}[]|;:'\",<>`~";
            const len = Math.floor(Math.random() * 5) + 3;
            let s = "";
            for (let i = 0; i < len; i++) {
                s += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            // occasionally produce a hostname without dot
            if (Math.random() < 0.5) {
                s = Math.random().toString(36).substring(2, 8);
            }
            return s;
        }

        QUnit.test("150 randomized valid inputs should pass", assert => {
            for (let i = 0; i < 150; i++) {
                const url = generateValidWeb();
                const result = validate(url);
                assert.strictEqual(result, true, `✅ [${i + 1}] '${url}' matches regex`);
            }
        });

        QUnit.test("150 randomized invalid inputs should fail", assert => {
            for (let i = 0; i < 150; i++) {
                const url = generateInvalidWeb();
                const result = validate(url);
                assert.notStrictEqual(result, true, `❌ [${i + 1}] '${url}' did not match regex → ${result}`);
            }
        });
    });


    QUnit.test("FileValidation - valid PNG over 1KB passes", async assert => {
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        const largeContent = 'a'.repeat(2048);
        const file = new File([largeContent], "logo.png", { type: "image/png" });
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        fileInput.files = dataTransfer.files;
        const result = await FileValidation(fileInput);
        assert.strictEqual(result, true, " Valid PNG passed FileValidation.");
    });


    QUnit.test("FileValidation - invalid file type fails", async assert => {
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        const largeContent = 'a'.repeat(2048);
        const file = new File([largeContent], "logo.jpg", { type: "image/jpeg" });
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        fileInput.files = dataTransfer.files;
        const result = await FileValidation(fileInput);
        assert.strictEqual(result, false, "Non-PNG file failed FileValidation.");
    });
});
