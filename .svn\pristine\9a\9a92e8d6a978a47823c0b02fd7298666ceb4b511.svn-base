﻿using ContinuityPatrol.Application.Features.Server.Events.TestConnection;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Server.Commands.TestConnection;

public class
    ServerTestConnectionCommandHandler : IRequestHandler<ServerTestConnectionCommand, ServerTestConnectionResponse>
{
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;
    private readonly IPublisher _publisher;
    private readonly IServerRepository _serverRepository;
    private readonly IServerViewRepository _serverViewRepository;
    private readonly IWindowsService _windowsService;
    private readonly IJobScheduler _client;
    private readonly IMapper _mapper;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILicenseValidationService _licenseValidationService;
    private readonly ILogger<ServerTestConnectionCommandHandler> _logger;

    public ServerTestConnectionCommandHandler(IServerRepository serverRepository, IServerViewRepository serverViewRepository, IMapper mapper,
        ILoadBalancerRepository nodeConfigurationRepository, IWindowsService windowsService, IPublisher publisher, IJobScheduler client, ILicenseManagerRepository licenseManagerRepository, ILicenseValidationService licenseValidationService, ILogger<ServerTestConnectionCommandHandler> logger)
    {
        _mapper = mapper;
        _serverRepository = serverRepository;
        _serverViewRepository = serverViewRepository;
        _windowsService = windowsService;
        _nodeConfigurationRepository = nodeConfigurationRepository;
        _publisher = publisher;
        _client = client;
        _licenseManagerRepository = licenseManagerRepository;
        _licenseValidationService = licenseValidationService;
        _logger = logger;
    }

    public async Task<ServerTestConnectionResponse> Handle(ServerTestConnectionCommand request,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("Starting ServerTestConnectionCommand handler for request: {@Request}", request);

        var serverDtl = await _serverViewRepository.GetAllByServerIdsAsync(request.Id);

        _logger.LogDebug("Retrieved server details: {@ServerDetails}", serverDtl);

        await LicenseValidation(serverDtl);

        if(serverDtl.Count == 0) return new ServerTestConnectionResponse { Success = false, Message = "All servers are inactive or expired." };

        var nodeConfig =
            await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString())
            ?? await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(),
                ServiceType.LoadBalancer.ToString());

        if (nodeConfig is null)
        {
            _logger.LogError("LoadBalancer is not configured for monitor service.");
            throw new InvalidException("Load Balancer is not configured for Monitor Service");
        }

        _logger.LogDebug("Retrieved node configuration: {@NodeConfig}", nodeConfig);

        var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

        _logger.LogDebug("Generated base URL: {BaseUrl}", baseUrl);

        var monitorUrl = UrlHelper.GenerateMonitorCheckUrl(nodeConfig.TypeCategory, baseUrl);

        _logger.LogDebug("Generated monitor URL: {MonitorUrl}", monitorUrl);

        var monitorResponse = await _windowsService.CheckWindowsService(monitorUrl);

        _logger.LogDebug("Monitor service response: {@MonitorResponse}", monitorResponse);

        if (!monitorResponse.Success)
        {
            _logger.LogError("Monitor service check failed. Inactive Nodes: {@InactiveNodes}, Message: {Message}", monitorResponse.InActiveNodes, monitorResponse.Message);
            throw new WindowServiceException(monitorResponse.InActiveNodes, ServiceType.Monitor.ToString(), monitorResponse.Message);
        }

        serverDtl.ForEach(x =>
        {
            x.LicenseKey = SecurityHelper.Encrypt(x.LicenseKey);
            x.Status = "Pending";
            x.IsConnection = true;
        });
        _logger.LogDebug("Updated server details with encrypted license keys and status.");

        var serverDto = _mapper.Map<List<Domain.Entities.Server>>(serverDtl);

        _logger.LogDebug("Mapped server details to DTO: {@ServerDto}", serverDto);

        _ = await _serverRepository.UpdateRangeAsync(serverDto) as List<Domain.Entities.Server>;

        _logger.LogDebug("Updated server repository with new details: {@UpdatedServers}", serverDto);

        await _publisher.Publish(new ServerTestConnectionEvent { ServerName = serverDtl.Select(x=>x.Name).ToList() }, cancellationToken);

        _logger.LogDebug("Published ServerTestConnectionEvent for servers: {@ServerNames}", serverDtl.Select(x => x.Name));

        var url = UrlHelper.GenerateServerTestConnectionUrl(nodeConfig.TypeCategory, baseUrl);

        _logger.LogDebug("Generated server test connection URL: {TestConnectionUrl}", url);

        var jobData = new Dictionary<string, string> { ["url"] = url };
        await _client.ScheduleJob(Guid.NewGuid().ToString(), jobData);

        _logger.LogDebug("Scheduled job with URL: {Url}", url);

        var response = new ServerTestConnectionResponse
        {
            Message = serverDtl.Count == 1
                    ? $"Server '{string.Join(", ", serverDtl.Select(x => x.Name))}' test connection request sent successfully."
                    : $"Server(s) '{string.Join(", ", serverDtl.Select(x => x.Name))}' test connection request sent successfully."
        };

        _logger.LogDebug("Test connection request completed. Response message: {ResponseMessage}", response.Message);

        return response;
    }

    private async Task LicenseValidation(List<ServerView> serverDtl)
    {
        var licenseIds = serverDtl.Where(x=>x.LicenseId.IsNotNullOrWhiteSpace()).Select(x => x.LicenseId).Distinct().ToList();

        _logger.LogDebug("Extracted license IDs: {@LicenseIds}", licenseIds);

        var licenseManager = await _licenseManagerRepository.GetLicenseExpiryDateByIds(licenseIds);

        _logger.LogDebug("Retrieved license manager details: {@LicenseManager}", licenseManager);

        foreach (var license in licenseManager)
        {
            var servers = serverDtl.Where(x => x.LicenseId == license.ReferenceId).ToList();

            if (!license.IsState || !await _licenseValidationService.IsLicenseExpired(license.ExpiryDate))
            {
                var stateMessage = !license.IsState ? "is in an inactive state" : "is expired";
                _logger.LogWarning("License '{PoNumber}' {StateMessage}, related servers: {AffectedServers}.",
                    license.PoNumber,
                    stateMessage,
                    string.Join(", ", servers.Select(x => x.Name)));

                var serverMap = _mapper.Map<List<Domain.Entities.Server>>(servers);
                serverMap.ForEach(x => x.ExceptionMessage = $"License '{license.PoNumber}' {stateMessage}.");

                await _serverRepository.UpdateRangeAsync(serverMap);
                serverDtl.RemoveAll(x => x.LicenseId == license.ReferenceId);
            }
        }
    }
}