﻿using ContinuityPatrol.Application.Features.FormHistory.Events.Create;

namespace ContinuityPatrol.Application.Features.FormHistory.Commands.Create;

public class CreateFormHistoryCommandHandler : IRequestHandler<CreateFormHistoryCommand, CreateFormHistoryResponse>
{
    private readonly IFormHistoryRepository _formHistoryRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateFormHistoryCommandHandler(IMapper mapper, IFormHistoryRepository formHistoryRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _formHistoryRepository = formHistoryRepository;
        _publisher = publisher;
    }

    public async Task<CreateFormHistoryResponse> Handle(CreateFormHistoryCommand request,
        CancellationToken cancellationToken)
    {
        var formHistory = _mapper.Map<Domain.Entities.FormHistory>(request);

        formHistory = await _formHistoryRepository.AddAsync(formHistory);

        var response = new CreateFormHistoryResponse
        {
            Message = Message.Create(nameof(Domain.Entities.FormHistory), formHistory.FormName),

            FormHistoryId = formHistory.ReferenceId
        };

        await _publisher.Publish(new FormHistoryCreatedEvent { FormName = formHistory.FormName }, cancellationToken);

        return response;
    }
}