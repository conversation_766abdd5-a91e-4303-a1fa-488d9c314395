﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Job.Events.PaginatedView;

public class JobPaginatedEventHandler : INotificationHandler<JobPaginatedEvent>
{
    private readonly ILogger<JobPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public JobPaginatedEventHandler(ILoggedInUserService userService, ILogger<JobPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(JobPaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.MonitoringJob.ToString(),
            Action = $"{ActivityType.View} {Modules.MonitoringJob}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Monitoring Job viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Monitoring Job viewed");
    }
}