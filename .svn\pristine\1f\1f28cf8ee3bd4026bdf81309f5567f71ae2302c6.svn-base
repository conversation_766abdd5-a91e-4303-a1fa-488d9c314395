﻿namespace ContinuityPatrol.Application.Features.AlertInformation.Queries.GetDetail;

public class
    GetAlertInformationDetailQueryHandler : IRequestHandler<GetAlertInformationDetailQuery, AlertInformationDetailVm>
{
    private readonly IAlertInformationRepository _alertInformationRepository;
    private readonly IMapper _mapper;

    public GetAlertInformationDetailQueryHandler(IAlertInformationRepository alertInformationRepository, IMapper mapper)
    {
        _alertInformationRepository = alertInformationRepository;
        _mapper = mapper;
    }

    public async Task<AlertInformationDetailVm> Handle(GetAlertInformationDetailQuery request,
        CancellationToken cancellationToken)
    {
        var alertInformation = await _alertInformationRepository.GetByReferenceIdAsync(request.Id);
        Guard.Against.NullOrDeactive(alertInformation, nameof(Domain.Entities.AlertInformation),
            new NotFoundException(nameof(Domain.Entities.AlertInformation), request.Id));

        var alertInformationDto = _mapper.Map<AlertInformationDetailVm>(alertInformation);

        return alertInformationDto;
    }
}