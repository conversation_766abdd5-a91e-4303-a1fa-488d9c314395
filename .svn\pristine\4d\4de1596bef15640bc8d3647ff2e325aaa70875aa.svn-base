namespace ContinuityPatrol.Domain.Entities;

public class ApprovalMatrixRequest : AuditableEntity
{
    public string RequestId { get; set; }
    public string ApprovalMatrixId { get; set; }
    public string ProcessName { get; set; }
	public string Description { get; set; }
	public string UserName { get; set; }
	public string Status { get; set; }
	public string Approvers { get; set; }
	public string Message { get; set; }
    public DateTime StartDateTime { get; set; }
	public DateTime EndDateTime { get; set; }
	public bool IsRequest { get; set; }
		
}
