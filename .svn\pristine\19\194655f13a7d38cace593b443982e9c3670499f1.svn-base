﻿//If change any attr or someother property update same While edit also. find.. keyValuePair and update it
const controlOptions = {
    groups: [
        {
            id: 'custom',
            label: 'CP Custom Elements',
            elementOrder: ['database,server,replication'],
        },
        {
            id: 'common',
            label: 'Form Fields',
            elementOrder: [
                'text-input',
                'number',
                'select',
                'checkbox',
                'radio',
                'textarea',
                'date-input',
                'hidden',
                'upload',
                'button',
                'email',
            ],
        },
        {
            id: 'html',
            label: 'HTML Elements',
            elementOrder: ['header', 'paragraph', 'divider'],
        },
        {
            id: 'layout',
            label: 'Layout',
            elementOrder: ['header', 'paragraph', 'divider'],
        },
        {
            id: 'replicationTable',
            label: 'Replication Table',
            elementOrder: ['', '', ''],
        },
    ],
    groupOrder: ['common', 'custom', 'replicationTable', 'html', 'layout'],
    sortable: false,
    disable: {
        elements: ["upload", "radio", "hidden"]
    },
    elements: [
        {
            tag: 'select',
            config: {
                label: 'Database',
                disabledAttrs: ['type', 'className', 'DatabaseTypeID'],//Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder', 'DatabaseType'],// disable delete button.
            },
            meta: {
                group: 'custom',
                id: 'database',
                icon: '', //⌨
            },
            attrs: {
                name: "@@PRdatabase",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
                DatabaseType: "all",
                DatabaseTypeID: " "
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Server',
                disabledAttrs: ['type', 'className', 'ServerRoleID', 'ServerTypeID'], //Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder', 'ServerRole', 'ServerType'], // disable delete button.
            },
            meta: {
                group: 'custom',
                id: 'server',
                icon: '', //⌨
            },
            attrs: {
                name: "@@PRServer",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
                ServerRole: "",
                ServerType: "",
                ServerRoleID: "",
                ServerTypeID: ""
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Replication',
                disabledAttrs: ['type', 'className'], //Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder'], // disable delete button.
            },
            meta: {
                group: 'custom',
                id: 'replication',
                icon: '', //⌨
            },
            attrs: {
                name: "@@replication",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Workflows',
                disabledAttrs: ['type', 'className'], //Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder', 'dependentAction'], // disable delete button.
            },
            meta: {
                group: 'custom',
                id: 'workflow',
                icon: '', //⌨
            },
            attrs: {
                name: "@@workflow_name",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
                dependentAction: false
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Single Sign-On',
                disabledAttrs: ['type', 'className'], //Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder'], // disable delete button.
            },
            meta: {
                group: 'custom',
                id: 'singlesignon',
                icon: '', //⌨
            },
            attrs: {
                name: "singlesignon_name",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true
            },
        },
        //{
        //    tag: 'select',
        //    config: {
        //        label: 'Node',
        //        disabledAttrs: ['type', 'className', 'id'], //Hide from user.
        //        lockedAttrs: ['required', 'name', 'placeholder', 'multiple'], // disable delete button.
        //    },
        //    meta: {
        //        group: 'custom',
        //        id: 'nodes',
        //        icon: '', //⌨
        //    },
        //    attrs: {
        //        name: "nodes",
        //        id: "assigned_nodes",
        //        className: 'form-select-modal-dynamic',
        //        type: 'select',
        //        placeholder: "Select Option",
        //        required: true,
        //        multiple: true
        //    },
        //},
        {
            tag: 'table',
            config: {
                label: 'Sudo/Su Table',
                disabledAttrs: ['type', 'className', 'rows', 'columns'],
                lockedAttrs: ['required', 'name'],
            },
            meta: {
                group: 'custom',
                id: 'table',
                icon: '⌨',
            },
            attrs: {
                name: 'Sudo/Su Table',
                className: 'custom-table',
                required: true,
                rows: 1, // Initial number of rows
                columns: 4, // Initial number of columns
            },
        },
        {
            tag: 'table',
            config: {
                label: 'Deployments Table',
                disabledAttrs: ['type', 'className', 'id'], //Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder', 'multiple'], // disable delete button.
            },
            meta: {
                group: 'custom',
                id: 'deploymentTable',
                icon: '⌨'
            },
            attrs: {
                name: "Deployments Table",
                id: "deployments-table",
                className: 'deployments-table',
            },
        },
        {
            tag: 'table',
            config: {
                label: 'RSync',
                disabledAttrs: ['type', 'className', 'rows', 'columns'],
                lockedAttrs: ['required', 'name'],
            },
            meta: {
                group: 'replicationTable',
                id: 'replicationConfigTable',
                icon: '⌨',
            },
            attrs: {
                type: 'replicationtable', //Important
                name: 'replication-table',
                className: 'replication-custom-table',
                //required: true,
                //rows: 1, // Initial number of rows
                //columns: 4, // Initial number of columns
            },
        },
        {
            tag: 'table',
            config: {
                label: 'VmPath Table',
                disabledAttrs: ['type', 'className', 'rows', 'columns'],
                lockedAttrs: ['required', 'name'],
            },
            meta: {
                group: 'replicationTable',
                id: 'VmPathTable',
                icon: '⌨',
            },
            attrs: {
                type: 'vmpathtable', //Important
                name: 'vmPath-table',
                className: 'vmPath-custom-table',
                //required: true,
                //rows: 1, // Initial number of rows
                //columns: 4, // Initial number of columns
            },
        },
        {
            tag: 'table',
            config: {
                label: 'Luns Table',
                disabledAttrs: ['type', 'className', 'rows', 'columns'],
                lockedAttrs: ['required', 'name', "hideLunsTable"],
            },
            meta: {
                group: 'replicationTable',
                id: 'lunsTable',
                icon: '⌨',
            },
            attrs: {
                type: 'lunstable', //Important
                name: 'Luns-Table',
                className: 'luns-custom-table',
                //required: true,
                hideLunsTable: false,
                //rows: 1, // Initial number of rows
                //columns: 4, // Initial number of columns
            },
        },
        {
            tag: 'table',
            config: {
                label: 'Dynamic Table',
                disabledAttrs: ['type', 'className', 'rows', 'columns'],
                lockedAttrs: ['required', 'name'],
            },
            meta: {
                group: 'replicationTable',
                id: 'dynamicTable',
                icon: '⌨',
            },
            attrs: {
                // type: 'dynamictable', //Important
                name: 'Dynamic Table',
                className: 'dynamic-custom-table',
                //required: true,
                //rows: 1, // Initial number of rows
                //columns: 4, // Initial number of columns
            },
        },
        {
            tag: 'table',
            config: {
                label: 'ZFS Table',
                disabledAttrs: ['className', 'rows', 'columns'],
                lockedAttrs: ['required', 'name'],
            },
            meta: {
                group: 'replicationTable',
                id: 'replicationZFSTable',
                icon: '⌨',
            },
            attrs: {
                // type: 'dynamictable', //Important
                name: 'Replication ZFS Table',
                className: 'replicationZFSTable',
                //required: true,
                //rows: 1, // Initial number of rows
                //columns: 4, // Initial number of columns
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Select',
                disabledAttrs: ['type', 'className'],
                lockedAttrs: ['required', 'name', 'placeholder', 'multiple'],
            },
            meta: {
                group: 'common',
                id: 'select',
                icon: 'select',
            },
            options: [
                {
                    label: '',
                    value: '',
                },
                {
                    label: 'Option-1',
                    value: 'option-1',
                }
            ],
            attrs: {
                name: "select_field",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
                multiple: false
            },
        },
        {
            tag: 'input',
            attrs: {
                name: "radio_field",
                type: 'radio',

            },
            config: {
                label: 'Radio Group',
                disabledAttrs: ['type'],
                lockedAttrs: ['name'],
            },
            meta: {
                group: 'common',
                icon: 'radio-group',
                id: 'radio'
            },
            options: (() => {
                let options = [1, 2, 3].map(i => {
                    return {
                        label: 'Radio ' + i,
                        value: 'radio-' + i,
                        selected: false,
                    };
                });
                return options;
            })(),
        },
        {
            tag: 'input',
            attrs: {
                name: "checkbox_field",
                type: 'checkbox',
            },
            config: {
                label: 'Checkbox',
                disabledAttrs: ['type'],
                lockedAttrs: ['name', 'required'],
                hideLabel: true
            },
            meta: {
                group: 'common',
                icon: 'checkbox',
                id: 'checkbox'
            },
            options: [{
                label: 'Option 1',
                value: 'option-1',
                checked: false,
            },],
        },
        //{
        //    tag: 'input',
        //    attrs: {
        //        name: "checkbox_group",
        //        type: 'checkbox',
        //        //required: true,
        //    },
        //    config: {
        //        label: 'Checkbox Group',
        //        disabledAttrs: ['type'],
        //        lockedAttrs: ['name', 'required'],
        //        hideLabel: false
        //    },
        //    meta: {
        //        group: 'common',
        //        icon: 'checkbox-group',
        //        id: 'checkbox-group'
        //    },
        //    options: (() => {
        //        let options = [1, 2, 3].map(i => {
        //            return {
        //                label: 'Option ' + i,
        //                value: 'option-' + i,
        //                checked: false,
        //            };
        //        });
        //        return options;
        //    })(),
        //},
        {
            tag: 'textarea',
            attrs: {
                name: "text_area",
                type: 'textarea',
                required: false,
                placeholder: "Enter Text Area",
            },
            config: {
                label: 'Text Area',
                disabledAttrs: ['type'],
                lockedAttrs: ['name', 'required', 'maxlength', 'minlength'],
            },
            meta: {
                group: 'common',
                icon: 'textarea',
                id: 'textarea'
            },
        },
        {
            tag: "input",
            config: {
                label: 'Text Input',
                disabledAttrs: ['type', "class", "attrid"],
                lockedAttrs: ['name', 'required', 'placeholder', 'minlength', 'maxlength', 'encryption', 'restrict', 'disabled', 'inputType'],
            },
            meta: {
                group: 'common',
                icon: 'text-input',
                id: 'text-input'
            },
            attrs: {
                attrid: "textField",
                name: "text_field",
                required: true,
                placeholder: "Enter Text",
                type: "text",
                minlength: "",
                maxlength: "",
                encryption: false,
                /* customvalidation: false,*/
                /*restrict: false,*/
                disabled: false,
                inputType: "",
                //formDynamicIcon: '',
            },
        },
        {
            tag: "input",
            config: {
                label: "Number Input",
                disabledAttrs: ["type", "attrid"],
                lockedAttrs: ['name', 'placeholder', 'required', 'minlength', 'maxlength', 'disabled'],
            },
            meta: {
                group: "common",
                icon: "hash",
                id: "number"
            },
            attrs: {
                attrid: "numberInput",
                name: "number_field",
                placeholder: "Enter Number",
                type: "number",
                required: true,
                minlength: "",
                maxlength: "",
                disabled: false,
            }
        },
        {
            tag: "input",
            config: {
                label: "Password Input",
                disabledAttrs: ["type", "attrid", "encryption"],
                lockedAttrs: ['name', 'required', 'placeholder'],
            },
            meta: {
                group: "common",
                icon: "", //menu
                id: "password-input"
            },
            attrs: {
                attrid: "passwordField",
                name: "password_field",
                type: "password",
                placeholder: "Enter Password",
                required: true,
                encryption: true,
            }
        },
        {
            tag: "input",
            config: {
                label: "IP Address",
                disabledAttrs: ["type", "attrid"],
                lockedAttrs: ['name', 'placeholder', 'required'],
            },
            meta: {
                group: "common",
                icon: "", //text-input
                id: "ip-address"
            },
            attrs: {
                name: "IpAddress",
                type: "text",
                placeholder: "Enter IP Address",
                required: true,
                attrid: "ipAddressField"
            }
        },
        {
            tag: "input",
            config: {
                label: 'URL Input',
                disabledAttrs: ['type', "class", "attrid"],
                lockedAttrs: ['name', 'required', 'placeholder'],
            },
            meta: {
                group: 'common',
                icon: 'text-input',
                id: 'URL-Field'
            },
            attrs: {
                attrid: "URLField",
                name: "URL-Field",
                required: true,
                placeholder: "Enter Text",
                type: "text",
            },
        },
    ],
}