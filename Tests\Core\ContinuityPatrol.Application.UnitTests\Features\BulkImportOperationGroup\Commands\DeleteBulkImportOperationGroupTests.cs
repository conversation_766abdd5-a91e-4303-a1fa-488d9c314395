using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperationGroup.Commands;

public class DeleteBulkImportOperationGroupTests : IClassFixture<BulkImportOperationGroupFixture>
{
    private readonly BulkImportOperationGroupFixture _bulkImportOperationGroupFixture;
    private readonly Mock<IBulkImportOperationGroupRepository> _mockBulkImportOperationGroupRepository;
    private readonly Mock<IPublisher> _publisher;
    private readonly DeleteBulkImportOperationGroupCommandHandler _handler;

    public DeleteBulkImportOperationGroupTests(BulkImportOperationGroupFixture bulkImportOperationGroupFixture)
    {
        _bulkImportOperationGroupFixture = bulkImportOperationGroupFixture;

        _mockBulkImportOperationGroupRepository = BulkImportOperationGroupRepositoryMocks.CreateDeleteBulkImportOperationGroupRepository(_bulkImportOperationGroupFixture.BulkImportOperationGroups);

        _mockBulkImportOperationGroupRepository = new Mock<IBulkImportOperationGroupRepository>();
        _publisher = new Mock<IPublisher>();

        _handler = new DeleteBulkImportOperationGroupCommandHandler(
            _mockBulkImportOperationGroupRepository.Object,
            _publisher.Object);
    }

    [Fact]
    public async Task Handle_Return_DeleteBulkImportOperationGroupResponse_When_BulkImportOperationGroupDeleted()
    {
       
            var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
            var command = new DeleteBulkImportOperationGroupCommand { Id = existingGroup.ReferenceId };

            _mockBulkImportOperationGroupRepository
                .Setup(repo => repo.GetByReferenceIdAsync(existingGroup.ReferenceId))
                .ReturnsAsync(existingGroup);

            _mockBulkImportOperationGroupRepository
               .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
               .ReturnsAsync(existingGroup);

            var result = await _handler.Handle(command, CancellationToken.None);

            result.ShouldBeOfType<DeleteBulkImportOperationGroupResponse>();
            result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var command = new DeleteBulkImportOperationGroupCommand { Id = existingGroup.ReferenceId };
        _mockBulkImportOperationGroupRepository
               .Setup(repo => repo.GetByReferenceIdAsync(existingGroup.ReferenceId))
               .ReturnsAsync(existingGroup);

        _mockBulkImportOperationGroupRepository
           .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
           .ReturnsAsync(existingGroup);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsync_OnlyOnce()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var command = new DeleteBulkImportOperationGroupCommand { Id = existingGroup.ReferenceId };

        _mockBulkImportOperationGroupRepository
               .Setup(repo => repo.GetByReferenceIdAsync(existingGroup.ReferenceId))
               .ReturnsAsync(existingGroup);

        _mockBulkImportOperationGroupRepository
           .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
           .ReturnsAsync(existingGroup);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()), Times.Once);
    }

    [Fact]
    public async Task Handle_SetIsActiveToFalse_When_BulkImportOperationGroupDeleted()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        existingGroup.IsActive = true; // Ensure it starts as active
        var command = new DeleteBulkImportOperationGroupCommand { Id = existingGroup.ReferenceId };

        Domain.Entities.BulkImportOperationGroup capturedGroup = null;

     
        _mockBulkImportOperationGroupRepository
                .Setup(repo => repo.GetByReferenceIdAsync(existingGroup.ReferenceId))
                .ReturnsAsync(existingGroup);

        _mockBulkImportOperationGroupRepository
           .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
           .ReturnsAsync(existingGroup);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedGroup.ShouldBeNull();
       
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationGroupNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new DeleteBulkImportOperationGroupCommand { Id = nonExistentId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportOperationGroup)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_CreateResponseWithCorrectProperties_When_BulkImportOperationGroupDeleted()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        existingGroup.InfraObjectName = "TestInfraObject";
        var command = new DeleteBulkImportOperationGroupCommand { Id = existingGroup.ReferenceId };
        _mockBulkImportOperationGroupRepository
               .Setup(repo => repo.GetByReferenceIdAsync(existingGroup.ReferenceId))
               .ReturnsAsync(existingGroup);

        _mockBulkImportOperationGroupRepository
           .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
           .ReturnsAsync(existingGroup);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var command = new DeleteBulkImportOperationGroupCommand { Id = testId };
        
        _mockBulkImportOperationGroupRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_bulkImportOperationGroupFixture.BulkImportOperationGroups.First());
        
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_DeleteSuccessful()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var command = new DeleteBulkImportOperationGroupCommand { Id = existingGroup.ReferenceId };
        _mockBulkImportOperationGroupRepository
              .Setup(repo => repo.GetByReferenceIdAsync(existingGroup.ReferenceId))
              .ReturnsAsync(existingGroup);

        _mockBulkImportOperationGroupRepository
           .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
           .ReturnsAsync(existingGroup);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<DeleteBulkImportOperationGroupResponse>();
        result.GetType().ShouldBe(typeof(DeleteBulkImportOperationGroupResponse));
    }

    [Fact]
    public async Task Handle_PerformSoftDelete_When_EntityExists()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        existingGroup.IsActive = true;
        var command = new DeleteBulkImportOperationGroupCommand { Id = existingGroup.ReferenceId };
        _mockBulkImportOperationGroupRepository
              .Setup(repo => repo.GetByReferenceIdAsync(existingGroup.ReferenceId))
              .ReturnsAsync(existingGroup);

        _mockBulkImportOperationGroupRepository
           .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
           .ReturnsAsync(existingGroup);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        existingGroup.IsActive.ShouldBeFalse();
        _mockBulkImportOperationGroupRepository.Verify(x => x.UpdateAsync(existingGroup), Times.Once);
    }

    [Fact]
    public async Task Handle_NotCallDeleteAsync_When_SoftDeletePerformed()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var command = new DeleteBulkImportOperationGroupCommand { Id = existingGroup.ReferenceId };
        _mockBulkImportOperationGroupRepository
              .Setup(repo => repo.GetByReferenceIdAsync(existingGroup.ReferenceId))
              .ReturnsAsync(existingGroup);

        _mockBulkImportOperationGroupRepository
           .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
           .ReturnsAsync(existingGroup);
        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.DeleteAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ReturnIsActiveFalse_When_DeleteCompleted()
    {
        // Arrange
        var existingGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        var command = new DeleteBulkImportOperationGroupCommand { Id = existingGroup.ReferenceId };
        _mockBulkImportOperationGroupRepository
              .Setup(repo => repo.GetByReferenceIdAsync(existingGroup.ReferenceId))
              .ReturnsAsync(existingGroup);

        _mockBulkImportOperationGroupRepository
           .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.BulkImportOperationGroup>()))
           .ReturnsAsync(existingGroup);
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_WithCorrectMessage_When_EntityNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new DeleteBulkImportOperationGroupCommand { Id = nonExistentId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportOperationGroup)null);
        
        // Act & Assert
        var exception = await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
        
        exception.Message.ShouldContain("BulkImportOperationGroup");
        exception.Message.ShouldContain(nonExistentId);
    }
}
