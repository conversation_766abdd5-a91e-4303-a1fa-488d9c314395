﻿using ContinuityPatrol.Application.Features.IncidentLogs.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentLogs.Commands.Delete;
using ContinuityPatrol.Application.Features.IncidentLogs.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.IncidentLogs.Queries.GetList;
using ContinuityPatrol.Application.Features.IncidentLogs.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.IncidentLogsModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class IncidentLogsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<IncidentLogsListVm>>> GetList()
    {
        Logger.LogDebug("Get All IncidentLogs");
        return await Mediator.Send(new GetIncidenLogsListQuery());
    }

    [Authorize(Policy = Permissions.Configuration.View)]
    [HttpGet("{id}", Name = "GetIncidentLogs")]
    public async Task<ActionResult<IncidentLogsDetailVm>> GetIncidentLogsById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "IncidentLogs Id");

        Logger.LogDebug($"Get IncidentLogs Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetIncidentLogsDetailQuery { Id = id }));
    }
    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateIncidentLogsResponse>> CreateIncidentLogs([FromBody] CreateIncidentLogsCommand createIncidentLogsCommand)
    {
        Logger.LogDebug($"Creating IncidentLogs '{createIncidentLogsCommand.InfraObjectName}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateIncidentLogs), await Mediator.Send(createIncidentLogsCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateIncidentLogsResponse>> UpdateIncidentLogs([FromBody] UpdateIncidentLogsCommand updateIncidentLogsCommand)
    {
        Logger.LogDebug($"Updating IncidentLogs '{updateIncidentLogsCommand.InfraObjectName}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateIncidentLogsCommand));
    }

    [HttpDelete("{id}")]

    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteIncidentLogsResponse>> DeleteIncidentLogs(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "IncidentLogs Id");

        Logger.LogDebug($"Deleting IncidentLogs Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteIncidentLogsCommand { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<IncidentLogsListVm>>> GetPaginatedIncidentLogs([FromQuery] GetIncidentLogsPaginatedQuery query)
    {
        Logger.LogDebug("Get Searching Details in IncidentLogs Paginated List");

        return Ok(await Mediator.Send(query));
    }
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllPageIncidentLogsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllPageIncidentLogsCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}
