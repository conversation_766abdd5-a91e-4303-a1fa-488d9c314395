﻿namespace ContinuityPatrol.Shared.Core.Helper;

public static class CryptographyHelper
{
    private const string SecretKey = "20Pe,rvtu!itLtPa/d10?Mah$petnThaohes%h2ktilso3*4ftMarSe(rTe)cs@ctimizhnviceP";

    public static string Encrypt(string text)
    {
        var originalBytes = Encoding.UTF8.GetBytes(text);
        var passwordBytes = Encoding.UTF8.GetBytes(SecretKey);

        // Hash the password with SHA256
        passwordBytes = SHA256.Create().ComputeHash(passwordBytes);

        // Generating salt bytes
        var saltBytes = GetRandomBytes();

        // Appending salt bytes to original bytes
        var bytesToBeEncrypted = new byte[saltBytes.Length + originalBytes.Length];
        for (var i = 0; i < saltBytes.Length; i++) bytesToBeEncrypted[i] = saltBytes[i];
        for (var i = 0; i < originalBytes.Length; i++) bytesToBeEncrypted[i + saltBytes.Length] = originalBytes[i];

        var encryptedBytes = AES_Encrypt(bytesToBeEncrypted, passwordBytes);

        return Convert.ToBase64String(encryptedBytes);
    }

    private static byte[] AES_Encrypt(byte[] bytesToBeEncrypted, byte[] passwordBytes)
    {
        // Set your salt here, change it to meet your flavor:
        // The salt bytes must be at least 8 bytes.
        byte[] saltBytes = { 1, 2, 3, 4, 5, 6, 7, 8 };
        //  byte[] saltBytes = GetRandomBytes();

        using var ms = new MemoryStream();

        using var aes = Aes.Create();
        aes.KeySize = 256;
        aes.BlockSize = 128;

        var key = new Rfc2898DeriveBytes(passwordBytes, saltBytes, 1000);
        aes.Key = key.GetBytes(aes.KeySize / 8);
        aes.IV = key.GetBytes(aes.BlockSize / 8);

        aes.Mode = CipherMode.CBC;

        using (var cs = new CryptoStream(ms, aes.CreateEncryptor(), CryptoStreamMode.Write))
        {
            cs.Write(bytesToBeEncrypted, 0, bytesToBeEncrypted.Length);
            cs.Close();
        }

        var encryptedBytes = ms.ToArray();

        return encryptedBytes;
    }

    private static byte[] AES_Decrypt(byte[] bytesToBeDecrypted, byte[] passwordBytes)
    {
        byte[] saltBytes = { 1, 2, 3, 4, 5, 6, 7, 8 };

        // byte[] saltBytes = GetRandomBytes();

        using var ms = new MemoryStream();

        using var aes = Aes.Create();

        aes.KeySize = 256;
        aes.BlockSize = 128;

        var key = new Rfc2898DeriveBytes(passwordBytes, saltBytes, 1000);
        aes.Key = key.GetBytes(aes.KeySize / 8);
        aes.IV = key.GetBytes(aes.BlockSize / 8);

        aes.Mode = CipherMode.CBC;

        using (var cs = new CryptoStream(ms, aes.CreateDecryptor(), CryptoStreamMode.Write))
        {
            cs.Write(bytesToBeDecrypted, 0, bytesToBeDecrypted.Length);
            cs.Close();
        }

        var decryptedBytes = ms.ToArray();

        return decryptedBytes;
    }

    public static string Decrypt(string cipherText)
    {
        var bytesToBeDecrypted = Convert.FromBase64String(cipherText);
        var passwordBytes = Encoding.UTF8.GetBytes(SecretKey);

        //Hash the password with SHA256
        passwordBytes = SHA256.Create().ComputeHash(passwordBytes);

        var decryptedBytes = AES_Decrypt(bytesToBeDecrypted, passwordBytes);

        // Getting the size of salt
        var saltSize = 8;

        // Removing salt bytes, retrieving original bytes
        var originalBytes = new byte[decryptedBytes.Length - saltSize];
        for (var i = saltSize; i < decryptedBytes.Length; i++) originalBytes[i - saltSize] = decryptedBytes[i];

        return Encoding.UTF8.GetString(originalBytes);
    }

    public static byte[] GetRandomBytes()
    {
        var saltSize = 8;
        var ba = new byte[saltSize];
        RandomNumberGenerator.Create().GetBytes(ba);
        return ba;
    }
}