﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@* @model ContinuityPatrol.Domain.ViewModels.AlertMasterModel.AlertMasterListVm; *@

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()
<style>
    .select2-results__option {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        /* max-width: 210px; /* Adjust this value as needed */ */
    }
</style>
<div class="page-content ">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-notification-manager"></i><span>Manage Alert</span></h6>
            <div class="d-flex align-items-center gap-2">              
                <div class="input-group" style="width:200px">
                    <span class="input-group-text form-label mb-0" for="basic-url" style="padding-top: 9px;"><i class="cp-priority me-1"></i>Priority</span>
                    <select id="manageAlertPriorityList" class="form-select managealert" data-placeholder="Select Alert Priority">
                        <option value="All">All</option>
                        <option value="High">High</option>
                        <option value="Low">Low</option>
                        <option value="Critical">Critical</option>
                        <option value="Information">Information</option>
                    </select>
                </div>

                <div class="input-group" style="width:200px">
                    <input type="search" id="manageAlertSearch" class="form-control" placeholder="Search" autocomplete="off" />
                    <span class="form-label mb-0 input-group-text"><i class="cp-search"></i></span>
                </div>
                <div class="d-flex gap-1">
                    <button type="button" class="btn-sm btn btn-primary d-none">Restore&nbsp;Settings</button>
                    <button id="btnManageAlertDownload" type="button" class="btn-sm btn btn-primary" title="Download"><i class="cp-download"></i></button>
                    <button type="button" class="btn-sm btn btn-primary createBtn" id="btnCreateManageAlert"><i class="cp-add me-1"></i>Create</button>
                </div>
            </div>
        </div>
        <div class="card-body pt-0">
            <div id="cardTable" class="table-responsive">
                <table @* class=" table table-hover align-middle" *@ class="datatable table table-hover dataTable no-footer" id="manageAlertMasterTable">
                    <thead>
                        <tr>
                            <th class="SrNo_th">Sr. No.</th>    
                            <th class="text-truncate">Alert ID</th>
                            <th class="text-truncate">Alert Name</th>
                            <th class="text-truncate">Alert Message</th>
                            <th class="text-truncate">Alert Priority</th>
                            <th class="text-truncate">Alert Count</th>
                            <th class="text-truncate">SMS </th>
                            <th class="text-truncate">Email</th>
                            @* <th class="text-truncate">Escalation</th>
                            <th class="text-truncate">Manage Escalation</th> *@
                            <th class="text-truncate">Status</th>
                            <th class="text-truncate">Action</th>
                        </tr>
                    </thead>
                    <tbody id="tablebody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!--Notification-->
<div class='Notification'>
    <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
        <div class='d-flex'>
            <div class='toast-body'>
                <span id="manageAlertClass" class='success-toast'>
                    <i id="icon" class='cp-check toast_icon'></i>
                </span>
                <span id="notificationAlertmessage">
                </span>
            </div>
            <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
        </div>
    </div>
</div>
<!--Modal Create-->
<div class="modal fade" id="manageAlertCreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered  modal-lg">
        <div class="modal-content" id="CreateForm">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-notification-manager"></i><span>Manage Alert Configuration</span></h6>
                <button type="button" class="btn-close btnManageAlertCancel" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xl-6">
                        <div class="form-group">

                            <div class="form-label">Alert ID</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class=" cp-alerts"></i></span>
                                <input type="number" class="form-control" name="AlertId" placeholder="Enter Alert ID" id="manageAlertId" autocomplete="off" max="5" />
                            </div>
                            <span id="alert_id_error"></span>
                        </div>
                    </div>
                    <div class="col-xl-6">
                        <div class="form-group">
                            <div class="form-label">Alert Name </div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-name"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Alert Name " id="manageAlertName" autocomplete="off" maxlength="100" />
                            </div>
                            <span id="alert_name_error"></span>
                        </div>
                    </div>
                    <div class="col-xl-6">
                        <div class="form-group">
                            <div class="form-label">Alert Message</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-message-alert"></i></span>
                                <input type="text" class="form-control" name="AlertMessage" placeholder="Enter Alert Message" id="manageAlertMessage" autocomplete="off" maxlength="500" />
                            </div>
                            <span id="alert_message_error"></span>
                        </div>
                    </div>
                    <div class="col-xl-6">
                        <div class="form-group">
                            <div class="form-label">Alert Priority</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-priority"></i></span>
                                <select class="form-select-modal" id="manageAlertPriority" data-placeholder="Select Alert Priority">                                   
                                    <option value="Information">Information</option>
                                    <option value="Low">Low</option>
                                    <option value="High">High</option>
                                    <option value="Critical">Critical</option>
                                </select>
                            </div>
                            <span id="alert_Priority_error"></span>
                        </div>
                    </div>
                    <div class="col-xl-6">
                        <div class="form-group">
                            <div class="form-label">Alert Count</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-error-handing"></i></span>
                                <input type="number" class="form-control" placeholder="Enter Alert Count" id="manageAlertCount" autocomplete="off" max="15" />
                            </div>
                            <span id="alert_count_error"></span>
                        </div>
                    </div>
                    <div class="col-xl-6">
                        <div class="form-group">
                            <div class="form-label">Alert Generated Time(HH:MM)</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-time"></i></span>
                                <input type="time" class="form-control" id="manageAlertTime" autocomplete="off" />
                            </div>
                            <span id="alert_time_error"></span>
                        </div>
                    </div>
                    <div class="col-xl-12 col-12">
                        <div class="d-flex gap-4">
                            <div class="form-check">
                                <input aria-label="option 1" id="manageAlertSMS" name="isSendSMS" type="checkbox"
                                       class="form-check-input" cursorshover="true"><label class="form-check-label" cursorshover="true">SMS</label>
                            </div>
                            <div class="form-check">
                                <input aria-label="option 1" id="manageAlertEmail" name="isSendEmail" type="checkbox"
                                       class="form-check-input" cursorshover="true"><label class="form-check-label" cursorshover="true">Email</label>
                            </div>
                            <div class="form-check">
                                <input aria-label="option 1" id="manageAlertActive" name="isAlertActive"
                                       type="checkbox" class="form-check-input"><label class="form-check-label" cursorshover="true">Is Alert Active</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-6">
                        <div class="form-group mt-2" id="manageAlertSMSMessageDiv">
                            <div class="form-label"> Message</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-message-alert"></i></span>
                                <input type="text" class="form-control" name="AlertMessage" placeholder="Enter Message" id="manageAlertSMSMessage" autocomplete="off" maxlength="500">
                            </div>
                          <span id="message_error"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary">
                    <i class="cp-note me-1"></i>Note: All fields are mandatory
                    except optional
                </small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm btnManageAlertCancel" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm" id="btnSaveManageAlert">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!--Modal View History-->
<div class="modal fade" id="manageAlertViewHistory" tabindex="-1" aria-labelledby="exampleModalLabel" data-bs-backdrop="static" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-notification-manager"></i><span>View History</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div>
                    <table class="table table-hover dataTable" style="width:100%;">
                        <thead>
                            <tr>
                                <th class="text-truncate" colspan="col-1">Alert ID</th>
                                <th class="text-truncate" scope="col">Alert Name</th>
                                <th class="text-truncate" scope="col">Alert Message</th>
                                <th class="text-truncate" scope="col">Alert Priority</th>
                                <th class="text-truncate" scope="col">Alert Count</th>
                                <th class="text-truncate" scope="col">Alert Generated Time</th>
                                <th class="text-truncate" scope="col">SMS</th>
                                <th class="text-truncate" scope="col">Email</th>

                            </tr>
                        </thead>
                        <tbody id="manageAlertHistoryTableBody">
                        </tbody>
                    </table>
                </div>
            </div>            
        </div>
    </div>
</div>
<!--Escalation Modal-->
<div class="modal fade" data-bs-backdrop="static" id="manageAlertEscalationModal" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-xl ">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title">
                    <i class="cp-escalation_matrix_header-icon-3"></i>
                    <span> Escalation Matrix Mapping</span>
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="workflow-action row row-cols-1" style="height:calc(100vh - 300px);overflow-y:auto">
                    <div class="col">
                        <div class="mx-auto" style="width: 30rem">
                            <button type="button" class="btn btn-primary btn-sm">End</button>
                            <div class="Escalation_Timeline">
                                <ul class="ul" id="Escalationlev_Time_ul">
                                </ul>
                                <button type="button" class="btn btn-primary btn-sm">Start Escalation</button>
                            </div>
                        </div>
                    </div>
                </div>
                <nav class="mt-3">
                    <div class="bia nav nav-tabs" id="nav-tab" role="tablist">
                        <button class="nav-link active fs-6" id="nav-home-tab" data-bs-toggle="tab" data-bs-target="#nav-home" type="button" role="tab" aria-controls="nav-home" aria-selected="true">Add Escalation Details</button>
                        <button class="nav-link fs-6" id="nav-profile-tab" data-bs-toggle="tab" data-bs-target="#nav-profile" type="button" role="tab" aria-controls="nav-profile" aria-selected="false">Add Resources for Escalation FYI Mail</button>
                    </div>
                </nav>
                <div class="tab-content" id="nav-tabContent">                   
                    <div class="tab-pane fade show active my-3" id="nav-home" role="tabpanel" aria-labelledby="nav-home-tab" tabindex="0">
                        <div class="form-label">Escalation Matrix Name</div>
                        <div class="d-flex">
                            <div class="form-group w-50">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-escalation_matrix_header-icon-3"></i></span>
                                    <select class="form-select-modal" id="manageAlertESCMatrixList">
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="cp-time"></i>

                                    </span>
                                    <input class="form-control" type="number" id="numId" />
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-time"></i></span>
                                    <select class="form-select-modal" id="tmdy">
                                        <option value="Min(s)">Min(s)</option>
                                        <option value="Hour(s)">Hour(s)</option>
                                        <option value="Day(s)">Day(s)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    @*</form> *@
                    <div class="tab-pane fade" id="nav-profile" role="tabpanel" aria-labelledby="nav-profile-tab" tabindex="0">
                        <div class="card">                        
                            <div class="card-body">
                                <div class="bia nav nav-tabs" id="nav-tab" role="tablist">
                                    <button class="nav-link active fs-6" id="nav-members-tab" data-bs-toggle="tab" data-bs-target="#nav-members" type="button" role="tab" aria-controls="nav-members" aria-selected="true">Members</button>
                                </div>                              
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary">
                    <i class="cp-note me-1"></i>Note: All fields are mandatory
                    except optional
                </small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm" id="btnSaveManageAlertEscalation">Attach Matrix Profile</button>
                </div>
            </div>
        </div>
    </div>
</div>
@* user Modal *@
<div class="modal fade" id="UserModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-LDAPs"></i><span>Select Teams/Users for Communication Notification</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="height:20rem">
                <div class="form-group">
                    <div class="form-label">FYA</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-LDAPs"></i></span>
                        <select class="form-select-modal" id="mySelect" data-placeholder="Select Users/Team" multiple>
                            <option value="all" id="data" class="select-all-option">Select All</option>
                            <option value="Test_wev">Test_wev</option>
                            <option value="new_one">new_one</option>
                            <option value="SVC_APP">SVC_APP</option>
                            <option value="Action_Test">Action_Test</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-label">FYI</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-LDAP"></i></span>
                        <select class="form-select-modal" id="mySelects" data-placeholder="Select Users/Team" multiple>
                            <option value="all" id="data" class="select-all-option">Select All</option>
                            <option value="Test_wev">Test_wev</option>
                            <option value="new_one">new_one</option>
                            <option value="SVC_APP">SVC_APP</option>
                            <option value="Action_Test">Action_Test</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-end">
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!--Modal Delete-->
<div class="modal fade" id="manageAlertDeleteModal" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/delete.png" />
            </div>
            <div class="modal-body text-center pt-0">
                <h4>Are you sure?</h4>
                <p class="d-flex align-items-center justify-content-center gap-1">
                    You want to delete the<span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="manageAlertDeteteName"></span>
                    data?
                </p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="button" id="btnManageAlertConfirmDelete"  class="btn btn-primary btn-sm">Yes</button>
            </div>
        </div>
    </div>
</div>
<div id="manageAlertCreatePermission" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Alerts.CreateAndEdit" aria-hidden="true"></div>
<div id="manageAlertDeletePermission" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Alerts.Delete" aria-hidden="true"></div>
<script>
    $(document).ready(function () {
        $('#mySelect,#mySelects').select2();
        $('#mySelect,#mySelects').on('select2:select', async function (e) {
            if (e.params.data && e.params.data.id === 'all') {
                $('#' + e.target.id).find('option').prop("selected", true);
                let values = $('#mySelect').val();
                let update = await values.filter((item) => item !== "all");
                $('#' + e.target.id).val(update).trigger('change');
                $("#data").text("unselect all")
            }
        });
        $('#mySelect,#mySelects').on('select2:unselect', function (e) {
            debugger
            if (e.params.data && e.params.data.id === 'all') {

                $('#' + e.target.id).val(null).trigger('change');
            }
        });
    });
    let arr = [1];
</script>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Alert/Manage Alert/manage_alert.js"></script>