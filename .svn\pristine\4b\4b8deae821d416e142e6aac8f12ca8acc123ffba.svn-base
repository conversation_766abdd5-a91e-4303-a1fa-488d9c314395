﻿using ContinuityPatrol.Application.Features.UserInfraObject.Commands.Create;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Application.UnitTests.Features.UserInfraObject.Commands
{
    public class CreateUserInfraObjectTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IUserInfraObjectRepository> _mockUserInfraObjectRepository;
        private readonly CreateUserInfraObjectCommandHandler _handler;

        public CreateUserInfraObjectTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockUserInfraObjectRepository = new Mock<IUserInfraObjectRepository>();
            _handler = new CreateUserInfraObjectCommandHandler(_mockMapper.Object, _mockUserInfraObjectRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnSuccessResponse_WhenInfraObjectIsCreated()
        {
            var command = new CreateUserInfraObjectCommand
            {
                UserId = Guid.NewGuid().ToString(),
                Properties = "SampleObject",
                IsApplication = 20
            };

            var infraObject = new Domain.Entities.UserInfraObject
            {
                Id = 1,
                UserId = command.UserId,
                Properties = "SampleObject",
                IsApplication = 20
            };

            var expectedResponse = new CreateUserInfraObjectResponse
            {
                Message = Message.Create(nameof(Domain.Entities.UserInfraObject), infraObject.UserId)
            };

            _mockMapper.Setup(mapper => mapper.Map<Domain.Entities.UserInfraObject>(command)).Returns(infraObject);

            _mockUserInfraObjectRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserInfraObject>()))
                .ReturnsAsync(infraObject);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedResponse.Message, result.Message);

            _mockMapper.Verify(mapper => mapper.Map<Domain.Entities.UserInfraObject>(command), Times.Once);
            _mockUserInfraObjectRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserInfraObject>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowException_WhenMappingFails()
        {
            var command = new CreateUserInfraObjectCommand
            {
                UserId = Guid.NewGuid().ToString(),
                Properties = "SampleObject",
                IsApplication = 20
            };

            _mockMapper.Setup(mapper => mapper.Map<Domain.Entities.UserInfraObject>(command)).Throws(new AutoMapperMappingException());

            await Assert.ThrowsAsync<AutoMapperMappingException>(() => _handler.Handle(command, CancellationToken.None));

            _mockMapper.Verify(mapper => mapper.Map<Domain.Entities.UserInfraObject>(command), Times.Once);
            _mockUserInfraObjectRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserInfraObject>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowException_WhenRepositoryFails()
        {
            var command = new CreateUserInfraObjectCommand
            {
                UserId = Guid.NewGuid().ToString(),
                Properties = "SampleObject",
                IsApplication = 20
            };

            var infraObject = new Domain.Entities.UserInfraObject
            {
                Id = 1,
                UserId = command.UserId,
                Properties = "SampleObject",
                IsApplication = 20
            };

            _mockMapper.Setup(mapper => mapper.Map<Domain.Entities.UserInfraObject>(command)).Returns(infraObject);
            _mockUserInfraObjectRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserInfraObject>()))
                .ThrowsAsync(new Exception("Database error"));

            await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));

            _mockMapper.Verify(mapper => mapper.Map<Domain.Entities.UserInfraObject>(command), Times.Once);
            _mockUserInfraObjectRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserInfraObject>()), Times.Once);
        }
    }
}
