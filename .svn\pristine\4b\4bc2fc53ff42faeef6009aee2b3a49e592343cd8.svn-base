﻿using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;
using ContinuityPatrol.Domain.ViewModels.ImpactActivityModel;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Dashboard.Controllers;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Dashboard.Controllers
{
    public class ResiliencyMappingControllerTests
    {
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<ILogger<ResiliencyMappingController>> _mockLogger = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private  ResiliencyMappingController _controller;

        public ResiliencyMappingControllerTests()
        {
            Initialize();
        }
        public void Initialize()
        { 
            _controller = new ResiliencyMappingController
                (_mockDataProvider.Object,
                _mockLogger.Object,
                _mockPublisher.Object);
        }

        [Fact]
        public void List_ReturnsViewResult()
        {           
            var result = _controller.List();

            var viewResult = Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public async Task GetBusinessServiceList_ReturnsJsonResultWithSuccess()
        {
            var mockData = new  List<GetDcMappingListVm>();
            _mockDataProvider.Setup(x => x.DashboardView.GetDcMappingDetails(It.IsAny<string>()))
                             .ReturnsAsync(mockData);

            var result = await _controller.GetBusinessServiceList("siteId");

            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic jsonData = jsonResult.Value;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetBusinessServiceList_HandlesException_ReturnsJsonException()
        {
            _mockDataProvider.Setup(x => x.DashboardView.GetDcMappingDetails(It.IsAny<string>()))
                             .ThrowsAsync(new Exception("Test Exception"));

            var result = await _controller.GetBusinessServiceList("siteId");

            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetImpactList_ReturnsJsonResultWithSuccess()
        {
            var mockImpactData = new List<ImpactActivityListVm>() ;
            _mockDataProvider.Setup(x => x.ImpactActivity.GetImpactActivityList())
                             .ReturnsAsync(mockImpactData);

            var result = await _controller.GetImpactList();

            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }   

        [Fact]
        public async Task GetImpactList_HandlesException_ReturnsJsonException()
        {
            _mockDataProvider.Setup(x => x.ImpactActivity.GetImpactActivityList())
                             .ThrowsAsync(new Exception("Test Exception"));

            var result = await _controller.GetImpactList();

            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }

        
    }
}

