using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberComponent.Events.Delete;

public class CyberComponentDeletedEventHandler : INotificationHandler<CyberComponentDeletedEvent>
{
    private readonly ILogger<CyberComponentDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberComponentDeletedEventHandler(ILoggedInUserService userService,
        ILogger<CyberComponentDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(CyberComponentDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.CyberComponent}",
            Entity = Modules.CyberComponent.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Component '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Component '{deletedEvent.Name}' deleted successfully.");
    }
}