﻿using ContinuityPatrol.Application.Features.DataSyncJob.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncJob.Commands.Update;
using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Replication.Commands.Update;
using ContinuityPatrol.Application.Features.Replication.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Replication.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.Create;
using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.update;
using ContinuityPatrol.Application.Features.RsyncJob.Commands.Create;
using ContinuityPatrol.Application.Features.RsyncJob.Commands.Update;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Domain.ViewModels.RoboCopyModel;
using ContinuityPatrol.Domain.ViewModels.RsyncOptionModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAll;


namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class ReplicationController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly ILogger<ReplicationController> _logger;
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;

    public ReplicationController(IPublisher publisher, ILogger<ReplicationController> logger, IDataProvider dataProvider, IMapper mapper)
    {
        _publisher = publisher;
        _logger = logger;
        _dataProvider = dataProvider;
        _mapper = mapper;
    }

    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in Replication");
        await _publisher.Publish(new ReplicationPaginatedEvent());
        return View();
    }

    //[Authorize(Policy = Permissions.Configuration.CreateAndEdit)]
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate([FromBody] ReplicationViewModel replicationViewmodel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in Replication");
        try
        {
            replicationViewmodel.Properties = replicationViewmodel.Properties.IsNullOrWhiteSpace() ? replicationViewmodel.Properties : SecurityHelper.Decrypt(replicationViewmodel.Properties);
            var replicationId = replicationViewmodel.Id;
            if (replicationId.IsNullOrWhiteSpace())
            {
                var formModel = _mapper.Map<CreateReplicationCommand>(replicationViewmodel);
                _logger.LogDebug($"Creating Replication '{formModel.Name}'");
                var result = await _dataProvider.Replication.CreateAsync(formModel);
                await CreateReplicationSubType(replicationViewmodel, result);
                _logger.LogDebug("Create operation completed successfully in Replication, returning view.");
                return Json(new { success = true, data = result });
            }
            else
            {
                var formModel = _mapper.Map<UpdateReplicationCommand>(replicationViewmodel);
                _logger.LogDebug($"Updating Replication '{formModel.Name}'");
                var result = await _dataProvider.Replication.UpdateAsync(formModel);
                await UpdateReplicationSubType(replicationViewmodel, result);
                _logger.LogDebug("Update operation completed successfully in Replication, returning view.");
                return Json(new { success = true, data = result });
            }
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on replication page: {ex.ValidationErrors.FirstOrDefault()}");
            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());
            return ex.GetJsonException();
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication page while processing the request for create or update.", ex);
            TempData.NotifyWarning(ex.GetMessage());
            return ex.GetJsonException();
        }
    }

    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in Server");
        try
        {
            var response = await _dataProvider.Replication.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted record in Replication");
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on Replication.", ex);
            return ex.GetJsonException();
        }
    }
   
    private async Task UpdateReplicationSubType(ReplicationViewModel replicationViewmodel, UpdateReplicationResponse result)
    {
        if (replicationViewmodel.Type.Trim().ToLower().Contains("rsync"))
        {
            foreach (var rsyncMap in replicationViewmodel.RsyncJobViewModels)
            {
                rsyncMap.Properties = rsyncMap.Properties.IsNullOrWhiteSpace()
                    ? rsyncMap.Properties
                    : SecurityHelper.Decrypt(rsyncMap.Properties);
                if (rsyncMap.Id.IsNullOrWhiteSpace())
                {
                    var rsync = _mapper.Map<CreateRsyncJobCommand>(rsyncMap);

                    rsync.ReplicationId = result.ReplicationId;

                    await _dataProvider.RsyncJob.CreateRsyncJob(rsync);

                    _logger.LogDebug($"Creating RSync '{rsyncMap.ReplicationName}'");
                }
                else
                {
                    var rsync = _mapper.Map<UpdateRsyncJobCommand>(rsyncMap);

                    rsync.ReplicationId = result.ReplicationId;

                    await _dataProvider.RsyncJob.UpdateRsyncJob(rsync);
                    _logger.LogDebug($"Updating Rsync '{rsyncMap.ReplicationName}'");
                }
            }
        }

        if (replicationViewmodel.Type.Trim().ToLower().Contains("robocopy"))
        {
            foreach (var roboCopyMap in replicationViewmodel.RoboCopyJobViewModels)
            {
                roboCopyMap.Properties = roboCopyMap.Properties.IsNullOrWhiteSpace()
                    ? roboCopyMap.Properties
                    : SecurityHelper.Decrypt(roboCopyMap.Properties);
                if (roboCopyMap.Id.IsNullOrWhiteSpace())
                {
                    var rsync = _mapper.Map<CreateRoboCopyJobCommand>(roboCopyMap);

                    rsync.ReplicationId = result.ReplicationId;

                    await _dataProvider.RoboCopyJob.CreateRoboCopyJob(rsync);

                    _logger.LogDebug($"Creating RoboCopy '{roboCopyMap.ReplicationName}'");
                }
                else
                {
                    var rsync = _mapper.Map<UpdateRoboCopyJobCommand>(roboCopyMap);

                    rsync.ReplicationId = result.ReplicationId;

                    await _dataProvider.RoboCopyJob.UpdateRoboCopyJob(rsync);

                    _logger.LogDebug($"Updating RoboCopy '{roboCopyMap.ReplicationName}'");
                }
            }
        }

        if (replicationViewmodel.Type.Trim().ToLower().Contains("datasync"))
        {
            foreach (var roboCopyMap in replicationViewmodel.DataSyncJobViewModels)
            {
                roboCopyMap.Properties = roboCopyMap.Properties.IsNullOrWhiteSpace()
                    ? roboCopyMap.Properties
                    : SecurityHelper.Decrypt(roboCopyMap.Properties);
                if (roboCopyMap.Id.IsNullOrWhiteSpace())
                {
                    var rsync = _mapper.Map<CreateDataSyncJobCommand>(roboCopyMap);

                    rsync.ReplicationId = result.ReplicationId;

                    await _dataProvider.DataSyncJob.CreateDataSyncJob(rsync);

                    _logger.LogDebug($"Creating DataSyncJob '{roboCopyMap.ReplicationName}'");
                }
                else
                {
                    var rsync = _mapper.Map<UpdateDataSyncJobCommand>(roboCopyMap);

                    rsync.ReplicationId = result.ReplicationId;

                    await _dataProvider.DataSyncJob.UpdateDataSyncJob(rsync);

                    _logger.LogDebug($"Updating DataSyncJob '{roboCopyMap.ReplicationName}'");
                }
            }
        }
    }

    private async Task CreateReplicationSubType(ReplicationViewModel replicationViewmodel, CreateReplicationResponse result)
    {

        if (replicationViewmodel.Type.Trim().ToLower().Contains("rsync"))
        {
            foreach (var rsyncMap in replicationViewmodel.RsyncJobViewModels.Select(rsync => _mapper.Map<CreateRsyncJobCommand>(rsync)))
            {
                rsyncMap.Properties = rsyncMap.Properties.IsNullOrWhiteSpace() ? rsyncMap.Properties : SecurityHelper.Decrypt(rsyncMap.Properties);
                rsyncMap.ReplicationId = result.ReplicationId;

                await _dataProvider.RsyncJob.CreateRsyncJob(rsyncMap);

                _logger.LogDebug($"Creating RSync '{rsyncMap.ReplicationName}'");
            }
        }

        if (replicationViewmodel.Type.Trim().ToLower().Contains("datasync"))
        {
            foreach (var dataSyncMap in replicationViewmodel.DataSyncJobViewModels.Select(rsync =>
                         _mapper.Map<CreateDataSyncJobCommand>(rsync)))
            {
                dataSyncMap.Properties = dataSyncMap.Properties.IsNullOrWhiteSpace()
                    ? dataSyncMap.Properties
                    : SecurityHelper.Decrypt(dataSyncMap.Properties);
                dataSyncMap.ReplicationId = result.ReplicationId;

                await _dataProvider.DataSyncJob.CreateDataSyncJob(dataSyncMap);

                _logger.LogDebug($"Creating DataSync '{dataSyncMap.ReplicationName}'");
            }
        }

        if (replicationViewmodel.Type.Trim().ToLower().Contains("robocopy"))
        {
            foreach (var roboCopyMap in replicationViewmodel.RoboCopyJobViewModels.Select(roboCopy =>
                         _mapper.Map<CreateRoboCopyJobCommand>(roboCopy)))
            {
                roboCopyMap.Properties = roboCopyMap.Properties.IsNullOrWhiteSpace()
                    ? roboCopyMap.Properties
                    : SecurityHelper.Decrypt(roboCopyMap.Properties);
                roboCopyMap.ReplicationId = result.ReplicationId;
                await _dataProvider.RoboCopyJob.CreateRoboCopyJob(roboCopyMap);
                _logger.LogDebug($"Creating RoboCopy '{roboCopyMap.ReplicationName}'");
            }
        }
    }

    public async Task<JsonResult> DeleteSubTypes(string Type, string optionId, string sourcePath, string targetPath)
    {
        try
        {
            if (Type.Trim().ToLower().Contains("datasync"))
            {
                var eventToDelete = (await _dataProvider.DataSyncJob.GetDataSyncJobs())
                    .FirstOrDefault(x => x.DataSyncOptionId.Equals(optionId) && x.SourceDirectory.Equals(sourcePath) && x.DestinationDirectory.Equals(targetPath));

                Guard.Against.NullOrDeactive(eventToDelete, nameof(Domain.Entities.DataSyncJob),
                     new NotFoundException(nameof(Domain.Entities.DataSyncJob), Type));

                var response = await _dataProvider.DataSyncJob.DeleteDataSyncJob(eventToDelete.Id);

                return Json(new { success = true, data = response });

            }
            else if (Type.Trim().ToLower().Contains("rsync"))
            {
                var eventToDelete = (await _dataProvider.RsyncJob.GetRsyncJobs())
                    .FirstOrDefault(x => x.RsyncOptionId.Equals(optionId) && x.SourceDirectory.Equals(sourcePath) && x.DestinationDirectory.Equals(targetPath));

                Guard.Against.NullOrDeactive(eventToDelete, nameof(Domain.Entities.DataSyncJob),
                     new NotFoundException(nameof(Domain.Entities.DataSyncJob), Type));

                var response = await _dataProvider.RsyncJob.DeleteRsyncJob(eventToDelete.Id);

                return Json(new { success = true, data = response });
            }
            else
            {
                var eventToDelete = (await _dataProvider.RoboCopyJob.GetRoboCopyJobs())
                    .FirstOrDefault(x => x.RoboCopyOptionsId.Equals(optionId) && x.SourceDirectory.Equals(sourcePath) && x.DestinationDirectory.Equals(targetPath));

                Guard.Against.NullOrDeactive(eventToDelete, nameof(Domain.Entities.DataSyncJob),
                     new NotFoundException(nameof(Domain.Entities.DataSyncJob), Type));

                var response = await _dataProvider.RoboCopyJob.DeleteRoboCopyJob(eventToDelete.Id);

                return Json(new { success = true, data = response });
            }
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication page while processing the request.", ex);
            return ex.GetJsonException();
        }
    }


    public async Task<JsonResult> GetReplicationList()
    {
        _logger.LogDebug("Entering GetReplicationList method in Replication");
        try
        {
            var replicationList = await _dataProvider.Replication.GetReplicationList();

            var replication = new ReplicationViewModel
            {
                Replication = replicationList
            };

            _logger.LogDebug("Successfully retrieved replication list in Replication");
            return Json(new { success = true, data = replication });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication page while processing the list request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetByReferenceId(string id)
    {
        _logger.LogDebug("Entering GetByReferenceId method in Replication");
        try
        {
            var serveDto = await _dataProvider.Server.GetByReferenceId(id);
            _logger.LogDebug("Successfully retrieved server by id in Replication");
            return Json(new { success = true, data = serveDto });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication page while retrieving server detail by id.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetByReplicationReferenceId(string id)
    {
        _logger.LogDebug("Entering GetByReplicationReferenceId method in Replication");
        try
        {
            var replicationData = await _dataProvider.Replication.GetReplicationById(id);
            _logger.LogDebug("Successfully retrieved replication by id in Replication");
            return Json(new { success = true, data = replicationData });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication page while retrieving replication detail by id.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetReplicationById(string id)
    {
        _logger.LogDebug("Entering GetReplicationById method in Replication");
        try
        {
            var replicationDto = await _dataProvider.Replication.GetReplicationById(id);
            _logger.LogDebug("Successfully retrieved replication by id in Replication");

            if (replicationDto.Type.Trim().Contains("rsync", StringComparison.OrdinalIgnoreCase))
            {
                var rsyncJobs = await _dataProvider.RsyncJob.GetRsyncJobs();
                rsyncJobs = rsyncJobs.Where(x => x.ReplicationId.Equals(id)).ToList();

                _logger.LogDebug($"Successfully retrieved rsync job by replicationId '{id}' in Replication");
                return Json(new { success = true, data = new { response1 = rsyncJobs, response2 = replicationDto } });
            }
            if (replicationDto.Type.Trim().Contains("robocopy", StringComparison.OrdinalIgnoreCase))
            {
                var roboCopyJobs = await _dataProvider.RoboCopyJob.GetRoboCopyJobs();
                roboCopyJobs = roboCopyJobs.Where(x => x.ReplicationId.Equals(id)).ToList();

                _logger.LogDebug($"Successfully retrieved robocopy job by replicationId '{id}' in Replication");
                return Json(new { success = true, data = new { response1 = roboCopyJobs, response2 = replicationDto } });
            }
            if (replicationDto.Type.Trim().Contains("datasync", StringComparison.OrdinalIgnoreCase))
            {
                var dataSyncJobs = await _dataProvider.DataSyncJob.GetDataSyncJobs();
                dataSyncJobs = dataSyncJobs.Where(x => x.ReplicationId.Equals(id)).ToList();

                _logger.LogDebug($"Successfully retrieved datasync job by replicationId '{id}' in Replication");
                return Json(new { success = true, data = new { response1 = dataSyncJobs, response2 = replicationDto } });
            }
            return Json(new { success = true, data = new { response1 = "", response2 = replicationDto } });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication page while retrieving replication detail by id", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetReplicationPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in Replication");
        try
        {
            _logger.LogDebug("Successfully retrieved replication paginated list on Replication page");
            return Json(await _dataProvider.Replication.GetPaginatedReplications(query));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }    

    //[HttpGet]
    //public async Task<JsonResult> GetSiteNames()
    //{
    //    var siteNames = await _dataProvider.Site.GetSiteNames();
    //    return Json(siteNames);
    //}

    [HttpGet]
    public async Task<bool> IsReplicationNameExist(string replicationName, string id)
    {
        _logger.LogDebug("Entering IsReplicationNameExist method in Replication");
        try
        {
            _logger.LogDebug("Returning result for IsReplicationNameExist on Replication");
            return await _dataProvider.Replication.IsReplicationNameExist(replicationName, id);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on replication while checking if replication  name exists for : {replicationName}.", ex);

            return false;
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetReplicationNames()
    {
        _logger.LogDebug("Entering GetReplicationNames method in Replication");
        try
        {
            var replications = await _dataProvider.Replication.GetReplicationNames();
            _logger.LogDebug("Successfully retrieved replication names in Replication");
            return Json(new { success = true, data = replications });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication page while retrieving replication names.", ex);
            return ex.GetJsonException();
        }
    }

    //Clone
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> SaveAsReplication(SaveAsReplicationCommand saveAsReplicationCommand)
    {
        _logger.LogDebug("Entering SaveAsReplication method in Server");

        try
        {
            var result = await _dataProvider.Replication.SaveAsReplication(saveAsReplicationCommand);
            _logger.LogDebug($"Successfully inserted replication");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication page while inserting replication", ex);
            return ex.GetJsonException();
        }
    }

    //SaveAll
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> SaveAllReplication(SaveAllReplicationCommand savelAllReplicationCommand)
    {
        _logger.LogDebug("Entering SaveAllReplication method in Replication");
        try
        {
            var result = await _dataProvider.Replication.SaveAllReplication(savelAllReplicationCommand);
            _logger.LogDebug($"Successfully inserted replications");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication page while inserting replications", ex);
            return ex.GetJsonException();
        }
    }

    //Replication.js Important Don't Change.
    public async Task<JsonResult> ReplicationDataSyncProperties()
    {
        _logger.LogDebug("Entering ReplicationDataSyncProperties method in Replication");
        try
        {
            var dataSyncOptions = await _dataProvider.DataSync.GetDataSyncList();

            var dataSync = new DataSyncOptionsViewModel
            {
                DataSync = dataSyncOptions
            };
            _logger.LogDebug("Successfully retrieved datasync list in Replication ");
            return Json(new { success = true, data = dataSync });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication page while retrieving datasync list.", ex);
            return ex.GetJsonException();
        }
    }

    //Replication.js Important Don't Change.
    public async Task<JsonResult> ReplicationRSyncOptions()
    {
        _logger.LogDebug("Entering ReplicationRSyncOptions method in Replication");
        try
        {
            var rSyncOptions = await _dataProvider.RsyncOption.GetRsyncOptionList();

            var rSync = new RsyncOptionViewModel
            {
                RSync = rSyncOptions
            };
            _logger.LogDebug("Successfully retrieved rsync options in Replication");
            return Json(new { success = true, data = rSync });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication page while retrieving rsync options", ex);
            return ex.GetJsonException();
        }
    }

    //For Replication.js Important Don't Change.
    public async Task<JsonResult> ReplicationRoboCopyOptions()
    {
        _logger.LogDebug("Entering ReplicationRoboCopyOptions method in Replication");
        try
        {
            var roboCopyOptions = await _dataProvider.RoboCopy.GetRoboCopyList();

            var roboCopy = new RoboCopyViewModel
            {
                Robocopy = roboCopyOptions
            };
            _logger.LogDebug("Successfully retrieved robocopy options in Replication");
            return Json(new { success = true, data = roboCopy });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on replication page while retrieving robocopy options.", ex);
            return ex.GetJsonException();
        }
    }
}