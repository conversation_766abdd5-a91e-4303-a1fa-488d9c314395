﻿function monitorTypeRPforVM(value, infraObjectName, moniterType, parsedData) {
    
    if (moniterType?.toLowerCase() === "recoverpointforvm") {
        $.ajax({
            url: "/Monitor/Monitoring/GetRPPagination",
            method: 'GET',
            dataType: 'json',
            async: true,
            success: function (response) {
                let data = response?.pagination?.data[0]
                let rpData = data?.cgProperties ? JSON?.parse(data?.cgProperties) : {}
                console.log(rpData,'rppp')
                let parseData = rpData?.VMRecoverPointMonitoringPR?.ReplicationMonitoringPR
                let drData = rpData?.VMRecoverPointMonitoring[0]?.ReplicationMonitoring
                let infraobjectdata =
                    '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
                    ' <table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
                    '<thead style="position: sticky;top: 0px;">' +
                    '<tr>' +
                    '<th>Component Monitor</th>' +
                    ' <th>Source Server</th>' +
                    ' <th>Remote Replica</th>' +
                    '</tr>' +
                    '</thead>' +
                    '<tbody style="" id="monitorwork">' +
                    '<tr>' +
                    '<td>' + 'Cluster Name' + '</td>' +
                    '<td>' + '<i class="cp-manage-server text-primary me-1 fs-6"></i>' +
                    (parseData?.VRPAClusterName !== undefined && parseData?.VRPAClusterName !== null && parseData?.VRPAClusterName !== "" ? parseData?.VRPAClusterName : "NA") + '</td>' +
                    '<td>' + '<i class="cp-manage-server text-primary me-1 fs-6"></i>' +
                    (drData?.VRPAClusterName !== undefined && drData?.VRPAClusterName !== null && drData?.VRPAClusterName !== "" ? drData?.VRPAClusterName : "NA") + '</td>' +
                '</tr>' +
                    '<tr>' +
                    '<td>' + 'IP Address' + '</td>' +
                    '<td>' + '<i class="text-success cp-fal-server me-1 fs-6"></i>' +
                    (parseData?.PluginServerIPAddress !== undefined && parseData?.PluginServerIPAddress !== null && parseData?.PluginServerIPAddress !== "" ? parseData?.PluginServerIPAddress : "NA") + '</td>' +
                    '<td>' + '<i class="text-success cp-fal-server me-1 fs-6"></i>' +
                    (drData?.PluginServerIPAddress !== undefined && drData?.PluginServerIPAddress !== null && drData?.PluginServerIPAddress !== "" ? drData?.PluginServerIPAddress : "NA") + '</td>' +
                '</tr>' +
                    '<tr>' +
                    '<td>' + 'Version' + '</td>' +
                    '<td>' + '<i class="text-primary cp-version me-1 fs-6"></i>' +
                    (parseData?.Version !== undefined && parseData?.Version !== null && parseData?.Version !== "" ? parseData?.Version : "NA") + '</td>' +
                    '<td>' + '<i class="text-primary cp-version me-1 fs-6"></i>' +
                    (drData?.Version !== undefined && drData?.Version !== null && drData?.Version !== "" ? drData?.Version : "NA") + '</td>' +
                '</tr>' +
                    '</tbody>' +
                    '</table>' +
                    '</div>';
                setTimeout(() => {
                    //$("#infraobjectalldata").empty();
                    $('#replicationMonitorContainer').hide();
                    $("#infraobjectalldata").html(infraobjectdata);
                }, 200)
            }
        })
    }
}
