using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DriftResourceSummaryFixture : IDisposable
{
    public List<DriftResourceSummary> DriftResourceSummaryPaginationList { get; set; }
    public List<DriftResourceSummary> DriftResourceSummaryList { get; set; }
    public DriftResourceSummary DriftResourceSummaryDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "INFRA_123";
    public const string EntityName = "TestEntity";
    public const string EntityType = "Database";
    public const string EntityStatus = "Active";
    public const string ResourceType = "CPU";
    public const string ResourceValue = "80%";

    public ApplicationDbContext DbContext { get; private set; }

    public DriftResourceSummaryFixture()
    {
        var fixture = new Fixture();

        DriftResourceSummaryList = fixture.Create<List<DriftResourceSummary>>();

        DriftResourceSummaryPaginationList = fixture.CreateMany<DriftResourceSummary>(20).ToList();

        DriftResourceSummaryPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftResourceSummaryPaginationList.ForEach(x => x.IsActive = true);

        DriftResourceSummaryList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftResourceSummaryList.ForEach(x => x.IsActive = true);
  


        DriftResourceSummaryDto = fixture.Create<DriftResourceSummary>();
        DriftResourceSummaryDto.ReferenceId = Guid.NewGuid().ToString();
        DriftResourceSummaryDto.IsActive = true;
      
     

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
