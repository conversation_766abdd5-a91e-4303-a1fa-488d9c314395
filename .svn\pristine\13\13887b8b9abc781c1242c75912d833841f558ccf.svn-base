using ContinuityPatrol.Application.Features.PageBuilder.Commands.Create;
using ContinuityPatrol.Application.Features.PageBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.PageBuilderModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class PageBuilderProfile : Profile
{
    public PageBuilderProfile()
    {
        CreateMap<PageBuilder, PageBuilderListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PageBuilder, PageBuilderDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<PageBuilder, CreatePageBuilderCommand>().ReverseMap();
        CreateMap<PageBuilder, PageBuilderViewModel>().ReverseMap();

        CreateMap<CreatePageBuilderCommand, PageBuilderViewModel>().ReverseMap();
        CreateMap<UpdatePageBuilderCommand, PageBuilderViewModel>().ReverseMap();

        CreateMap<UpdatePageBuilderCommand, PageBuilder>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<PaginatedResult<PageBuilder>, PaginatedResult<PageBuilderListVm>>()
             .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}