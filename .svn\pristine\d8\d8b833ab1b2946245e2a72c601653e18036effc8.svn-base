﻿using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Infrastructure.Impl;

public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;
    private readonly ISmtpClientFactory _smtpClientFactory;

    public EmailService(ILogger<EmailService> logger, ISmtpClientFactory smtpClientFactory)
    {
        _logger = logger;
        _smtpClientFactory = smtpClientFactory;
    }

    public async Task<bool> SendEmail(EmailDto message)
    {
        try
        {
            _logger.LogInformation("Starting email sending process...");

            if (message.IsPasswordLess)
            {
                _logger.LogInformation("Using password-less authentication for sending email.");

                var userName = Regex.Split(SecurityHelper.Decrypt(message.From), @"@");
                // mailMessages.To.Add(new MailAddress(isToEmailEnable ? toEmailAddress : fromEmailAddress));
                using var mailMessages = new MailMessage();
                mailMessages.From = new MailAddress(SecurityHelper.Decrypt(message.From), userName[0]);
                mailMessages.To.Add(new MailAddress(message.To));
                mailMessages.Subject = $"CP Alert :'{message.Subject}'";
                if (string.IsNullOrEmpty(message.Body))
                {
                    mailMessages.IsBodyHtml = true;
                    mailMessages.AlternateViews.Add(message.HtmlBody);
                }
                else
                {
                    mailMessages.Body = message.Body;
                }

                using var client = _smtpClientFactory.Create(message.SmtpHost, Convert.ToInt32(message.Port));

                // using var client = new SmtpClient(message.SmtpHost, Convert.ToInt32(message.Port));

                client.EnableSsl = false; // Depends on your server configuration
                client.UseDefaultCredentials = true;

                await client.SendMailAsync(mailMessages);

                return true;
            }

            _logger.LogInformation("Using credentials for SMTP authentication.");

            // var smtpClient = new SmtpClient(message.SmtpHost, Convert.ToInt32(message.Port));
            using var smtpClient = _smtpClientFactory.Create(message.SmtpHost, Convert.ToInt32(message.Port));

            smtpClient.UseDefaultCredentials = false;

            smtpClient.Credentials = new NetworkCredential(SecurityHelper.Decrypt(message.From),
                SecurityHelper.Decrypt(message.Password));
            
            smtpClient.EnableSsl = message.EnableSSL;

            var mailMessage = new MailMessage();
            mailMessage.From = new MailAddress(SecurityHelper.Decrypt(message.From));
            mailMessage.To.Add(message.To);
            mailMessage.Subject = $"CP Alert :'{message.Subject}'";
            mailMessage.Body = message.Body;
            if (string.IsNullOrEmpty(message.Body))
            {
                mailMessage.IsBodyHtml = true;
                mailMessage.AlternateViews.Add(message.HtmlBody);
            }
            else
            {
                mailMessage.Body = message.Body;
            }

            await smtpClient.SendMailAsync(mailMessage);

            _logger.LogInformation("Email successfully sent to {Recipient}.", message.To);

            return true;
        }
        catch (SmtpException ex)
        {
            _logger.LogError($"Error sending email:{ex.Message}");

            throw new SmtpException("SMTP Error:" + ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error sending email{ex.Message}");

            throw new InvalidException(ex.Message);
        }
    }

    public async Task<bool> SendTestEmail(SendTestEmailDto message)
    {
        try
        {
            if (message.IsPasswordLess)
            {
                var userName = Regex.Split(SecurityHelper.Decrypt(message.UserName), @"@");
               
                using var mailMessages = new MailMessage();
                mailMessages.From = new MailAddress(SecurityHelper.Decrypt(message.UserName), userName[0]);
                mailMessages.To.Add(new MailAddress(message.IsEmail ? message.ToEmail : SecurityHelper.Decrypt(message.UserName)));
                // mailMessages.To.Add(new MailAddress(SecurityHelper.Decrypt(message.UserName)));
                mailMessages.Subject = message.Subject;
                mailMessages.IsBodyHtml = true;
                mailMessages.AlternateViews.Add(message.Body);

                using var client = _smtpClientFactory.Create(message.SmtpHost, Convert.ToInt32(message.Port));

                client.EnableSsl = false; // Set to true if your server requires SSL/TLS
                client.UseDefaultCredentials = true; // Use default credentials

               // client.DeliveryMethod = SmtpDeliveryMethod.Network;

                await client.SendMailAsync(mailMessages);

                return true;
            }
            else
            {
                //var smtpClient = new SmtpClient(message.SmtpHost, Convert.ToInt32(message.Port));

                using var smtpClient = _smtpClientFactory.Create(message.SmtpHost, Convert.ToInt32(message.Port));

                var userName = SecurityHelper.Decrypt(message.UserName);
                smtpClient.UseDefaultCredentials = false;
                smtpClient.Credentials = new NetworkCredential(userName, SecurityHelper.Decrypt(message.Password));
                smtpClient.EnableSsl = message.EnableSSL;

                var mailMessage = new MailMessage();
                mailMessage.IsBodyHtml = true;
                mailMessage.From = new MailAddress(userName);
                mailMessage.To.Add(userName);
                mailMessage.Subject = message.Subject;
                mailMessage.AlternateViews.Add(message.Body);

                await smtpClient.SendMailAsync(mailMessage);

                return true;
            }
        }
        catch (SmtpException ex)
        {
            _logger.LogError($"Error sending email:{ex.Message}");

            throw new SmtpException("SMTP Error:" + ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error sending email:{ex.Message}");

            return false;
        }
    }

    //public async Task<bool> IsMailSendToClientSuccessfully(EmailDto emailDto)
    //{
    //    try
    //    {
    //        var hostIMap = emailDto.SmtpHost.Split(".");
    //        var hostIMapMail = string.Join(".", hostIMap.Skip(1));
    //        var iMapHost = hostIMapMail == "logix.in" ? $"zpop.{hostIMapMail}" : $"imap.{hostIMapMail}";

    //        var failureIndicators = new List<string>
    //        {
    //            "wasn't able to deliver", "wasn't delivered", "couldn't be delivered",
    //            "unable to deliver", "undeliver", "failed", "bounce", "failure"
    //        };

    //        using var client = new ImapClient();
    //        client.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;

    //        await client.ConnectAsync(iMapHost, 993, SecureSocketOptions.SslOnConnect);
    //        await client.AuthenticateAsync(SecurityHelper.Decrypt(emailDto.From), SecurityHelper.Decrypt(emailDto.Password));

    //        var inbox = client.Inbox;
    //        await inbox.OpenAsync(MailKit.FolderAccess.ReadOnly);

    //        var query = SearchQuery.DeliveredAfter(DateTimeOffset.UtcNow.DateTime.AddMinutes(-35));
    //        var results = await inbox.SearchAsync(query);

    //        if (results.Count > 0)
    //        {
    //            foreach (var uniqueId in results)
    //            {
    //                var message = await inbox.GetMessageAsync(uniqueId);
    //                var textBody = message.TextBody?.ToLower() ?? "";
    //                var subject = message.Subject ?? "";

    //                // Check if the email is intended for the recipient and contains certain keywords indicating failure
    //                if((textBody.Contains(emailDto.To.ToLower()) || subject.ToLower().Contains(emailDto.To.ToLower())) &&
    //                  failureIndicators.Any(indicator => textBody.Contains(indicator)))
    //                {
    //                    await client.DisconnectAsync(true);
    //                    _logger.LogError("E-Mail delivery to client was unsuccessful.");
    //                    return false;
    //                }
    //            }
    //        }

    //        await client.DisconnectAsync(true);

    //        _logger.LogInformation("E-Mail sent to client successfully.");

    //        return true;

           
    //    }
    //    catch (SmtpException ex)
    //    {
    //        _logger.LogError($"Error sending email:{ex.GetMessage()}");

    //        return false;
    //    }
    //}
}