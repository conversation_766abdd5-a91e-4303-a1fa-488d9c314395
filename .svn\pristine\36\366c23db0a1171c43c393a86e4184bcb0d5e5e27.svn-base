﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller
{
    public class GoldenGateReplicationControllerShould
    {
        private readonly GoldenGateReplicationController _controller;

        public GoldenGateReplicationControllerShould()
        {
            _controller = new GoldenGateReplicationController();
        }

        [Fact]
        public void List_ReturnsViewResult()
        {
            
            var result = _controller.List();

            
            var viewResult = Assert.IsType<ViewResult>(result);
            
        }
    }
}
