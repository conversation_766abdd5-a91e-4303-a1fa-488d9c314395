﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.MSSQLMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.MSSQLMonitorLogs.Queries.GetPaginatedList;

public class GetMSSQLMonitorLogsPaginatedListQueryHandler : IRequestHandler<GetMSSQLMonitorLogsPaginatedListQuery,
    PaginatedResult<MSSQLMonitorLogsListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMssqlMonitorLogsRepository _monitorLogsRepository;

    public GetMSSQLMonitorLogsPaginatedListQueryHandler(IMssqlMonitorLogsRepository monitorLogsRepository,
        IMapper mapper)
    {
        _monitorLogsRepository = monitorLogsRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<MSSQLMonitorLogsListVm>> Handle(GetMSSQLMonitorLogsPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _monitorLogsRepository.PaginatedListAllAsync();

        var productFilterSpec = new MsSqlMonitorLogsFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<MSSQLMonitorLogsListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}