using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class GlobalSettingRepositoryTests : IClassFixture<GlobalSettingFixture>, IDisposable
{
    private readonly GlobalSettingFixture _globalSettingFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly GlobalSettingRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public GlobalSettingRepositoryTests(GlobalSettingFixture globalSettingFixture)
    {
        _globalSettingFixture = globalSettingFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new GlobalSettingRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.GlobalSettings.RemoveRange(_dbContext.GlobalSettings);
        await _dbContext.SaveChangesAsync();
    }

    #region IsGlobalSettingKeyExist Tests

    [Fact]
    public async Task IsGlobalSettingKeyExist_ReturnsTrue_WhenKeyExists_WithoutId()
    {
        // Arrange
        await ClearDatabase();
        var globalSettingKey = "TestSettingKey";
        var globalSetting = new GlobalSetting
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GlobalSettingKey = globalSettingKey,
            GlobalSettingValue = "TestValue",
            IsActive = true
        };

        await _dbContext.GlobalSettings.AddAsync(globalSetting);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsGlobalSettingKeyExist(globalSettingKey, null);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsGlobalSettingKeyExist_ReturnsFalse_WhenKeyDoesNotExist_WithoutId()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentKey = "NonExistentKey";

        // Act
        var result = await _repository.IsGlobalSettingKeyExist(nonExistentKey, null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsGlobalSettingKeyExist_ReturnsTrue_WhenKeyExists_WithDifferentId()
    {
        // Arrange
        await ClearDatabase();
        var globalSettingKey = "TestSettingKey";
        var existingId = Guid.NewGuid().ToString();
        var differentId ="" ;

        var globalSetting = new GlobalSetting
        {
            ReferenceId = existingId,
            GlobalSettingKey = globalSettingKey,
            GlobalSettingValue = "TestValue",
            IsActive = true
        };

        await _dbContext.GlobalSettings.AddAsync(globalSetting);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsGlobalSettingKeyExist(globalSettingKey, differentId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsGlobalSettingKeyExist_ReturnsFalse_WhenKeyExists_WithSameId()
    {
        // Arrange
        await ClearDatabase();
        var globalSettingKey = "TestSettingKey";
        var sameId = Guid.NewGuid().ToString();

        var globalSetting = new GlobalSetting
        {
            ReferenceId = sameId,
            GlobalSettingKey = globalSettingKey,
            GlobalSettingValue = "TestValue",
            IsActive = true
        };

        await _dbContext.GlobalSettings.AddAsync(globalSetting);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsGlobalSettingKeyExist(globalSettingKey, sameId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsGlobalSettingKeyExist_ReturnsFalse_WhenInvalidGuid()
    {
        // Arrange
        await ClearDatabase();
        var globalSettingKey = "TestSettingKey";
        var invalidGuid = "invalid-guid";

        var globalSetting = new GlobalSetting
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GlobalSettingKey = globalSettingKey,
            GlobalSettingValue = "TestValue",
            IsActive = true
        };

        await _dbContext.GlobalSettings.AddAsync(globalSetting);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsGlobalSettingKeyExist(globalSettingKey, invalidGuid);

        // Assert
        Assert.True(result); // Should behave like no ID provided
    }

    #endregion

    #region IsGlobalSettingKeyUnique Tests

    [Fact]
    public async Task IsGlobalSettingKeyUnique_ReturnsTrue_WhenKeyExists()
    {
        // Arrange
        await ClearDatabase();
        var globalSettingKey = "UniqueSettingKey";
        var globalSetting = new GlobalSetting
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GlobalSettingKey = globalSettingKey,
            GlobalSettingValue = "TestValue",
            IsActive = true
        };

        await _dbContext.GlobalSettings.AddAsync(globalSetting);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsGlobalSettingKeyUnique(globalSettingKey);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsGlobalSettingKeyUnique_ReturnsFalse_WhenKeyDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentKey = "NonExistentKey";

        // Act
        var result = await _repository.IsGlobalSettingKeyUnique(nonExistentKey);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsGlobalSettingKeyUnique_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var globalSettingKey = "TestSettingKey";
        var globalSetting = new GlobalSetting
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GlobalSettingKey = globalSettingKey,
            GlobalSettingValue = "TestValue",
            IsActive = true
        };

        await _dbContext.GlobalSettings.AddAsync(globalSetting);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsGlobalSettingKeyUnique("TESTSETTINGKEY");

        // Assert
        Assert.False(result); // Should not match due to case sensitivity
    }

    #endregion

    #region GlobalSettingBySettingKey Tests

    [Fact]
    public async Task GlobalSettingBySettingKey_ReturnsGlobalSetting_WhenKeyExistsAndIsActive()
    {
        // Arrange
        await ClearDatabase();
        var settingKey = "TestSettingKey";
        var globalSetting = new GlobalSetting
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GlobalSettingKey = settingKey,
            GlobalSettingValue = "TestValue",
            LoginUserId = "TestUser",
            PasswordProtection = "TestPassword",
            IsActive = true
        };

        await _dbContext.GlobalSettings.AddAsync(globalSetting);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GlobalSettingBySettingKey(settingKey);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(settingKey, result.GlobalSettingKey);
        Assert.Equal("TestValue", result.GlobalSettingValue);
        Assert.Equal("TestUser", result.LoginUserId);
        Assert.Equal("TestPassword", result.PasswordProtection);
    }

    [Fact]
    public async Task GlobalSettingBySettingKey_ReturnsNull_WhenKeyDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentKey = "NonExistentKey";

        // Act
        var result = await _repository.GlobalSettingBySettingKey(nonExistentKey);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GlobalSettingBySettingKey_ReturnsNull_WhenKeyExistsButIsInactive()
    {
        // Arrange
        await ClearDatabase();
        var settingKey = "InactiveSettingKey";
        var globalSetting = new GlobalSetting
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GlobalSettingKey = settingKey,
            GlobalSettingValue = "TestValue",
            IsActive = false
        };

        await _dbContext.GlobalSettings.AddAsync(globalSetting);
        await _dbContext.SaveChangesAsync();
        globalSetting.IsActive=false;
 _dbContext.GlobalSettings.Update(globalSetting);

        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GlobalSettingBySettingKey(settingKey);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GlobalSettingBySettingKey_IsCaseInsensitive()
    {
        // Arrange
        await ClearDatabase();
        var settingKey = "TestSettingKey";
        var globalSetting = new GlobalSetting
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GlobalSettingKey = settingKey,
            GlobalSettingValue = "TestValue",
            IsActive = true
        };

        await _dbContext.GlobalSettings.AddAsync(globalSetting);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GlobalSettingBySettingKey("TESTSETTINGKEY");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(settingKey, result.GlobalSettingKey);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddGlobalSetting_WhenValidGlobalSetting()
    {
        // Arrange
        await ClearDatabase();
        var globalSetting = new GlobalSetting
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GlobalSettingKey = "TestKey",
            GlobalSettingValue = "TestValue",
            LoginUserId = "TestUser",
            PasswordProtection = "TestPassword",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(globalSetting);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(globalSetting.GlobalSettingKey, result.GlobalSettingKey);
        Assert.Equal(globalSetting.GlobalSettingValue, result.GlobalSettingValue);
        Assert.Equal(globalSetting.LoginUserId, result.LoginUserId);
        Assert.Equal(globalSetting.PasswordProtection, result.PasswordProtection);
        Assert.Single(_dbContext.GlobalSettings);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenGlobalSettingIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsGlobalSetting_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var globalSetting = new GlobalSetting
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GlobalSettingKey = "TestKey",
            GlobalSettingValue = "TestValue",
            IsActive = true
        };

        await _dbContext.GlobalSettings.AddAsync(globalSetting);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(globalSetting.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(globalSetting.Id, result.Id);
        Assert.Equal(globalSetting.GlobalSettingKey, result.GlobalSettingKey);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsGlobalSetting_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var referenceId = Guid.NewGuid().ToString();
        var globalSetting = new GlobalSetting
        {
            ReferenceId = referenceId,
            GlobalSettingKey = "TestKey",
            GlobalSettingValue = "TestValue",
            IsActive = true
        };

        await _dbContext.GlobalSettings.AddAsync(globalSetting);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal(globalSetting.GlobalSettingKey, result.GlobalSettingKey);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateGlobalSetting_WhenValidGlobalSetting()
    {
        // Arrange
        await ClearDatabase();
        var globalSetting = new GlobalSetting
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GlobalSettingKey = "TestKey",
            GlobalSettingValue = "TestValue",
            LoginUserId = "TestUser",
            IsActive = true
        };

        _dbContext.GlobalSettings.Add(globalSetting);
        await _dbContext.SaveChangesAsync();

        globalSetting.GlobalSettingKey = "UpdatedKey";
        globalSetting.GlobalSettingValue = "UpdatedValue";
        globalSetting.LoginUserId = "UpdatedUser";

        // Act
        var result = await _repository.UpdateAsync(globalSetting);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedKey", result.GlobalSettingKey);
        Assert.Equal("UpdatedValue", result.GlobalSettingValue);
        Assert.Equal("UpdatedUser", result.LoginUserId);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenGlobalSettingIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveGlobalSetting_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var globalSetting = new GlobalSetting
        {
            ReferenceId = Guid.NewGuid().ToString(),
            GlobalSettingKey = "TestKey",
            GlobalSettingValue = "TestValue",
            IsActive = true
        };

        await _dbContext.GlobalSettings.AddAsync(globalSetting);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(globalSetting);

        // Assert
        var deletedSetting = await _dbContext.GlobalSettings.FindAsync(globalSetting.Id);
        Assert.Null(deletedSetting);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsAllGlobalSettings_WhenDataExists()
    {
        // Arrange
        await ClearDatabase();
        var globalSettings = new List<GlobalSetting>
        {
            new GlobalSetting { ReferenceId = Guid.NewGuid().ToString(), GlobalSettingKey = "Setting1", GlobalSettingValue = "Value1", IsActive = true },
            new GlobalSetting { ReferenceId = Guid.NewGuid().ToString(), GlobalSettingKey = "Setting2", GlobalSettingValue = "Value2", IsActive = true },
            new GlobalSetting { ReferenceId = Guid.NewGuid().ToString(), GlobalSettingKey = "Setting3", GlobalSettingValue = "Value3", IsActive = false }
        };

        await _dbContext.GlobalSettings.AddRangeAsync(globalSettings);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(3, result.Count); // Should return all settings regardless of IsActive
        Assert.Contains(result, x => x.GlobalSettingKey == "Setting1");
        Assert.Contains(result, x => x.GlobalSettingKey == "Setting2");
        Assert.Contains(result, x => x.GlobalSettingKey == "Setting3");
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoData()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    #endregion
}
