﻿using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Create;

public class CreateWorkflowCategoryCommandValidator : AbstractValidator<CreateWorkflowCategoryCommand>
{
    private readonly IWorkflowCategoryRepository _workflowCategoryRepository;

    public CreateWorkflowCategoryCommandValidator(IWorkflowCategoryRepository workflowCategoryRepository)
    {
        _workflowCategoryRepository = workflowCategoryRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("Please Enter the {PropertyName}.")
            .NotNull()
            .Must(IsValidJson)
            .WithMessage("{PropertyName} must be a valid JSON string.");

        RuleFor(p => p)
            .MustAsync(WorkflowCategoryNameUnique)
            .WithMessage("A same name already exists.");

        RuleFor(p => p.Version)
            .NotEmpty().WithMessage("Version is required.")
            .NotNull();
    }

    private async Task<bool> WorkflowCategoryNameUnique(CreateWorkflowCategoryCommand createWorkflowCategoryCommand,
        CancellationToken cancellationToken)
    {
        return !await _workflowCategoryRepository.IsWorkflowCategoryNameUnique(createWorkflowCategoryCommand.Name);
    }

    private bool IsValidJson(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                var obj = JsonConvert.DeserializeObject(properties);
                return obj != null;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }
}