﻿namespace ContinuityPatrol.Application.Features.EscalationMatrixLevel.Queries.GetNameUnique;

public class
    GetEscalationMatrixLevelNameUniqueQueryHandler : IRequestHandler<GetEscalationMatrixLevelNameUniqueQuery, bool>
{
    private readonly IEscalationMatrixLevelRepository _escalationMatrixLevelRepository;

    public GetEscalationMatrixLevelNameUniqueQueryHandler(
        IEscalationMatrixLevelRepository escalationMatrixLevelRepository)
    {
        _escalationMatrixLevelRepository = escalationMatrixLevelRepository;
    }

    public async Task<bool> Handle(GetEscalationMatrixLevelNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _escalationMatrixLevelRepository.IsEscalationMatrixLevelNameExist(request.Name,request.Id);
    }
}