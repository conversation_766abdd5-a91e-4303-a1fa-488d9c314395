﻿@model ContinuityPatrol.Application.Features.Server.Commands.UpdateBulkPassword.UpdateBulkPasswordCommand
@using ContinuityPatrol.Shared.Services.Helper
@{
    ViewData["Title"] = "List";
}
@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None archiveisometric" style="height:calc(100vh - 105px);background-image:url(/img/isomatric/bulk_credential_one.svg)">
        <div class="">
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title">
                        <i class="cp-scheduled-report"></i>
                        <span>Bulk Server Credential</span>
                    </h6>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form id="bulkCredentials">
                <div class="row row-cols-6">
                    <div class="col-6">
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        Username
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-name"></i></span>
                                        <input type="hidden" id="loginName" data-loginnames="@WebHelper.UserSession.LoginName">
                                        <input class="form-control" id="userName" type="text" autocomplete="off" placeholder="Enter Username" />
                                    </div>
                                    <span id="userNameEror"></span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        Operating System
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-os-type"></i></span>
                                        <select class="form-select" id='operatingSystem' multiple aria-label="Default select example" data-live-search="true" data-placeholder="Select Operating System">
                                        </select>
                                    </div>
                                    <span id="operatingError"></span>
                                </div>
                            </div>
                            
                            <div class="col-12" id="ckBox">
                                <div class="form-group mt-2">
                                    <div class="form-check form-check-inline" id="checkbox1">
                                        <input class="form-check-input Checkbox" type="checkbox" id="inlineCheckbox1" >
                                        <label class="form-check-label" for="inlineCheckbox1">Substitute Authentication</label>
                                    </div>                                                                   
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        Server
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-server"></i></span>
                                        <select class="form-select" id="serverName" multiple data-live-search="true" data-placeholder="Select Server" required>
                                        </select>
                                    </div>
                                    <span id="serverError"></span>
                                    <input id="serverId" type="hidden" class="form-control" />
                                </div>
                            </div>
                            <div class="col-12" id="SubAuthenticat">
                                <div class="form-group">
                                    <label class="form-label">
                                        Substitute Authentication
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-server-authentication"></i></span>
                                        <select class="form-select" id='SubAuthentication' multiple aria-label="Default select example" data-live-search="true" data-placeholder="Select Substitute Authentication">
                                        </select>
                                    </div>
                                    <span id="SubAuthenticationError"></span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        Password
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-lock"></i></span>
                                        <input class="form-control" type="password" id="Password" placeholder="Enter Password" autocomplete="Password" maxlength="30" />
                                        <span class="input-group-text toggle-password" role="button"><i class="cp-password-visible fs-6" title="show password"></i></span>
                                    </div>
                                    <span id="passwordError"></span>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        Confirm Password
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-lock"></i></span>
                                        <input class="form-control" type="password" id="ConfirmPassword" placeholder="Enter Confirm Password" autocomplete="ConfirmPassword" maxlength="30" />
                                    </div>
                                    <span id="ConfirmPassword-error"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gap-2 d-flex align-items-center ">
                    <button type="button" class="btn btn-secondary btn-sm" id='cancel' style="min-width:80px"  cursorshover="true">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm btn-save" id="SaveFunction" style="min-width:80px">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div id="configurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true"></div>
@* Duplicate Actions *@
<div class="modal fade" id="DuplicateActionsModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <img class="w-100" src="~/img/isomatric/confirmation.svg" alt="Duplicate Actions" />
            </div>
            <div class="modal-body text-center pt-5">
                <h5 class="fw-bold">Confirmation</h5>
                <h6 class="fw-bold">Are you sure?</h6>Do you want to change your credentials? <p>Once the credential was updated, couldn't retrieve the password.</p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-target="#CreateModal" data-bs-toggle="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="duplicateConfirmation">Yes</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Configuration/Bulk Credential/BulkCredential.js"></script>