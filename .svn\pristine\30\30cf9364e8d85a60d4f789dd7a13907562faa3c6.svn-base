using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ComplianceHistoryFixture : IDisposable
{
    public List<ComplianceHistory> ComplianceHistoryPaginationList { get; set; }
    public List<ComplianceHistory> ComplianceHistoryList { get; set; }
    public ComplianceHistory ComplianceHistoryDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public ComplianceHistoryFixture()
    {
        var fixture = new Fixture();

        ComplianceHistoryList = fixture.Create<List<ComplianceHistory>>();

        ComplianceHistoryPaginationList = fixture.CreateMany<ComplianceHistory>(20).ToList();

        ComplianceHistoryDto = fixture.Create<ComplianceHistory>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
