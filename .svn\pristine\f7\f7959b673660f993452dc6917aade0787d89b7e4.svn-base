QUnit.module("Company Validation Tests", {
    beforeEach: function () {
        // Create a fresh DOM fixture for each test
        this.fixture = document.createElement('div');
        this.fixture.innerHTML = `
            <div id="companyNameError"></div>
            <div id="companyDisplayNameError"></div>
            <div id="companyWebAddressError"></div>
            <input id="companyName">
            <input id="companyDisplayName">
            <input id="companyWebAddress">
        `;
        document.body.appendChild(this.fixture);

        // Mock the AJAX calls used in validation
        this.originalAjax = $.ajax;
        $.ajax = function (options) {
            return {
                done: function (cb) {
                    // Simulate server response
                    if (options.url.includes("IsCompanyNameExist")) {
                        cb({ success: true, data: false }); // Name doesn't exist
                    } else if (options.url.includes("IsCompanyDisplayNameExist")) {
                        cb({ success: true, data: false }); // Display name doesn't exist
                    }
                    return { fail: function () { } };
                }
            };
        };
    },
    afterEach: function () {
        document.body.removeChild(this.fixture);
        $.ajax = this.originalAjax;
    }
});

QUnit.test("Show validation errors for empty inputs", async function (assert) {
    const done = assert.async();

    // 1. Test empty company name
    const nameResult = await validateName(
        "",
        null,
        "mock/url",
        $("#companyNameError"),
        "Enter company name",
        "Name already exists"
    );

    assert.equal(nameResult, false, "Should reject empty company name");
    assert.equal(
        $("#companyNameError").text().trim(),
        "Enter company name",
        "Should show company name error"
    );

    // 2. Test empty display name
    const displayResult = await validateDisplayName(
        "",
        null,
        "mock/url",
        $("#companyDisplayNameError"),
        "Enter display name",
        "Display name already exists"
    );

    assert.equal(displayResult, false, "Should reject empty display name");
    assert.equal(
        $("#companyDisplayNameError").text().trim(),
        "Enter display name",
        "Should show display name error"
    );

    // 3. Test empty web address
    const webResult = await validateWebAddress("");

    assert.equal(webResult, false, "Should reject empty web address");
    assert.equal(
        $("#companyWebAddressError").text().trim(),
        "Enter company web address",
        "Should show web address error"
    );

    done();
});
async function IsSameNameExist(url, inputValue, errordisplay) {
    return !inputValue?.name?.trim() ? true : (await getAysncWithHandler(url, inputValue, OnError)) ? errordisplay : true;
}

async function validateName(value, id = null, url, errorElement, errorText, isNameExist) {
    if (!value) {
        errorElement.text(errorText).addClass('field-validation-error');
        return false;
    } else if (value.includes('<')) {
        errorElement.text('Special characters not allowed').addClass('field-validation-error');
    } else {
        let data = { name: value, id: id };
        const validationResults = [
            SpecialCharValidateCustom(value),
            ShouldNotBeginWithUnderScore(value),
            ShouldNotBeginWithNumber(value),
            ShouldNotBeginWithDotAndHyphen(value),
            ShouldNotConsecutiveDotAndHyphen(value),
            OnlyNumericsValidate(value),
            ShouldNotBeginWithSpace(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            minMaxCompanylength(value),
            secondChar(value),
            await IsSameNameExist(url, data, isNameExist)
        ];
        return CommonValidation(errorElement, validationResults);
    }
}

async function validateDisplayName(value, id = null, url, errorElement, errorText, isNameExist) {

    if (!value) {
        errorElement.text(errorText).addClass('field-validation-error');
        return false;
    } else if (value.includes('<')) {
        errorElement.text('Special characters not allowed').addClass('field-validation-error');
    } else {
        let data = { name: value, id: id };

        const validationResults = [
            SpecialCharValidate(value),
            OnlyNumericsValidate(value),
            ShouldNotBeginWithUnderScore(value),
            ShouldNotBeginWithSpace(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            DisplayLength(value),
            secondChar(value),
            await IsSameNameExist(url, data, isNameExist)
        ];
        return CommonValidation(errorElement, validationResults);
    }
}

async function validateWebAddress(value) {
    const errorElement = $('#companyWebAddressError');
    let format = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;

    if (!value || format.test(value?.charAt(0))) {
        errorElement.text(!value ? 'Enter company web address' : 'Enter valid company web address').addClass('field-validation-error');
        return false;
    } else {
        return CommonValidation(errorElement, [WebAddressValidate(value)]);
    }
}
function companyFileFormatValidate(fileName) {
    const validExtension = 'png';
    const fileExtension = fileName?.split('.').pop().toLowerCase();
    return fileExtension === validExtension;
}

async function FileValidation(fileInput, create = null) {
    const errorElement = $('#companyLogoError');

    if (!fileInput?.files?.length || fileInput?.files[0]?.name === "No file chosen") {
        return true;
    }
    let fileSize = fileInput?.files[0]?.size;
    let fileName = fileInput?.files[0]?.name;

    if (!companyFileFormatValidate(fileName)) {
        errorElement.text('Only png images are allowed')
            .addClass('field-validation-error');
        return false;
    }
    if (create === "new") {
        dataCompanyLogo = await convertToBase64(fileInput);
    }
    if (!dataCompanyLogo) {
        dataCompanyLogo = await convertToBase64(fileInput);
    }
    $("#companyLogoFile").attr("logoName", fileName);
    const validationResults = [
        FileSizeValidate(fileSize)
    ];
    return CommonValidation(errorElement, validationResults);
}

QUnit.test("validateName - null/empty input", async function (assert) {
    const done = assert.async();

    const result = await validateName(null, null, "", $("#companyNameError"), "Error", "Exists");
    assert.equal(result, false, "Should return false for null input");
    assert.equal($("#companyNameError").text(), "Error", "Should show error message");

    const result2 = await validateName("", null, "", $("#companyNameError"), "Error", "Exists");
    assert.equal(result2, false, "Should return false for empty string");

    done();
});

QUnit.test("validateName - special characters", async function (assert) {
    const done = assert.async();

    const result = await validateName("<script>", null, "", $("#companyNameError"), "Error", "Exists");
    assert.equal(result, false, "Should reject special characters");
    assert.equal($("#companyNameError").text(), "Special characters not allowed", "Should show special chars error");

    done();
});

QUnit.test("validateName - valid input", async function (assert) {
    const done = assert.async();

    const result = await validateName("ValidName", null, "", $("#companyNameError"), "Error", "Exists");
    assert.equal(result, true, "Should accept valid name");
    assert.equal($("#companyNameError").text(), "", "Should clear error");

    done();
});
QUnit.test("validateDisplayName - edge cases", async function (assert) {
    const done = assert.async();

    // Test null
    let result = await validateDisplayName(null, null, "", $("#companyDisplayNameError"), "Error", "Exists");
    assert.equal(result, false, "Should reject null");

    // Test empty
    result = await validateDisplayName("", null, "", $("#companyDisplayNameError"), "Error", "Exists");
    assert.equal(result, false, "Should reject empty string");

    // Test special chars
    result = await validateDisplayName("<script>", null, "", $("#companyDisplayNameError"), "Error", "Exists");
    assert.equal(result, false, "Should reject special chars");

    // Test valid
    result = await validateDisplayName("Valid Display", null, "", $("#companyDisplayNameError"), "Error", "Exists");
    assert.equal(result, true, "Should accept valid display name");

    done();
});
QUnit.test("validateWebAddress - various cases", async function (assert) {
    const done = assert.async();

    // Test null
    let result = await validateWebAddress(null);
    assert.equal(result, false, "Should reject null");

    // Test invalid first char
    result = await validateWebAddress("_invalid");
    assert.equal(result, false, "Should reject invalid first char");

    // Test valid
    result = await validateWebAddress("valid.com");
    assert.equal(result, true, "Should accept valid web address");

    done();
});
QUnit.test("FileValidation - edge cases", async function (assert) {
    const done = assert.async();

    // Mock file input
    const fileInput = {
        files: [],
        getAttribute: () => ""
    };

    // Test no file
    let result = await FileValidation(fileInput);
    assert.equal(result, true, "Should accept no file");

    // Test invalid extension
    fileInput.files = [{ name: "test.jpg", size: 1000 }];
    result = await FileValidation(fileInput);
    assert.equal(result, false, "Should reject invalid extension");

    // Test valid file
    fileInput.files = [{ name: "test.png", size: 1000 }];
    result = await FileValidation(fileInput);
    assert.equal(result, true, "Should accept valid file");

    done();
});
QUnit.test("companyCreateBtn click", function (assert) {
    const done = assert.async();

    // Set up some existing values
    $("#companyName").val("Test").attr("companyDetails", "{'test':1}");
    $("#companyDisplayName").val("Test Display");
    $("#companyWebAddress").val("test.com");
    $("#companyLogoFile").val("test.png").attr("logoName", "test.png");
    dataCompanyLogo = "mock-logo";

    // Trigger click
    $("#companyCreateBtn").trigger("click");

    setTimeout(() => {
        assert.equal($("#companyName").val(), "", "Should clear company name");
        assert.equal($("#companyDisplayName").val(), "", "Should clear display name");
        assert.equal($("#companyWebAddress").val(), "", "Should clear web address");
        assert.equal($("#companyLogoFile").val(), "", "Should clear file input");
        assert.equal(dataCompanyLogo, "", "Should clear logo data");
        done();
    }, 100);
});
QUnit.test("companySaveBtn click - validation failure", function (assert) {
    const done = assert.async();

    // Set up invalid values
    $("#companyName").val("");
    $("#companyDisplayName").val("");
    $("#companyWebAddress").val("");

    // Mock validation functions
    const originalValidateName = validateName;
    validateName = async () => false;

    const originalValidateDisplayName = validateDisplayName;
    validateDisplayName = async () => false;

    const originalValidateWebAddress = validateWebAddress;
    validateWebAddress = async () => false;

    // Trigger click
    $("#companySaveBtn").trigger("click");

    setTimeout(() => {
        // Restore original functions
        validateName = originalValidateName;
        validateDisplayName = originalValidateDisplayName;
        validateWebAddress = originalValidateWebAddress;

        assert.equal($("#companyNameError").text(), "Enter company name", "Should show name error");
        assert.equal($("#companyDisplayNameError").text(), "Enter display name", "Should show display name error");
        assert.equal($("#companyWebAddressError").text(), "Enter company web address", "Should show web address error");
        done();
    }, 100);
});
QUnit.test("companyLogoFile change", function (assert) {
    const done = assert.async();

    // Mock file
    const file = new Blob([""], { type: "image/png" });
    file.name = "test.png";

    // Mock FileReader
    const originalFileReader = window.FileReader;
    window.FileReader = function () {
        this.readAsDataURL = function () {
            this.onload({ target: { result: "data:image/png;base64,mock" } });
        };
    };

    // Trigger change
    const event = {
        target: {
            files: [file]
        }
    };
    $("#companyLogoFile").trigger("change", [event]);

 
});
QUnit.test("IsSameNameExist - AJAX handling", function (assert) {
    const done = assert.async();

    // Test with name that exists
    $.ajax = function (options) {
        return {
            done: function (cb) {
                cb({ success: true, data: true });
                return { fail: function () { } };
            }
        };
    };

    IsSameNameExist("test-url", { name: "Existing" }, $("#companyNameError")).then(result => {
        assert.equal(result, false, "Should return false when name exists");

        // Test with name that doesn't exist
        $.ajax = function (options) {
            return {
                done: function (cb) {
                    cb({ success: true, data: false });
                    return { fail: function () { } };
                }
            };
        };

        return IsSameNameExist("test-url", { name: "New" }, $("#companyNameError"));
    }).then(result => {
        assert.equal(result, true, "Should return true when name doesn't exist");
        done();
    });
});
QUnit.test("Company delete flow", function (assert) {
    const done = assert.async();

    // Mock delete response
    $.ajax = function (options) {
        return {
            done: function (cb) {
                cb({ success: true, data: { message: "Deleted" } });
                return { fail: function () { } };
            }
        };
    };

    // Mock dataTable
    const mockDataTable = {
        ajax: {
            reload: function () {
                assert.ok(true, "Should reload data after delete");
                done();
            }
        }
    };
    window.dataTable = mockDataTable;

    // Trigger delete
    $("#companyDeleteBtn").trigger("click");
});
QUnit.test("populateCompanyFields with null data", function (assert) {
    populateCompanyFields(null);

    assert.equal($("#companyName").val(), "", "Should handle null companyData");
    assert.equal($("#companyDisplayName").val(), "", "Should handle null displayName");
    assert.equal($("#companyWebAddress").val(), "", "Should handle null webAddress");
});

QUnit.test("convertToBase64 with null input", function (assert) {
    const done = assert.async();

    convertToBase64({ files: [] }).catch(error => {
        assert.equal(error, "No file selected.", "Should reject when no file selected");
        done();
    });
});