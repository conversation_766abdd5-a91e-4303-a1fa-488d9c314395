﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />

@Html.AntiForgeryToken()

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title">
            <i class="cp-monitoring"></i><span>Oracle Rac Detail Monitoring: <span id="infraName"></span></span>
        </h6>
        <form class="d-flex align-items-center" style="width:33%;">
            <div class="d-flex align-items-center">
                <span title="Last Monitored Time"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
                @* <a class="btn btn-sm btn-primary ms-2 px-2 py-0 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow"></i>Back</a> *@
            </div>
            <div class="input-group" style="width:200px">
                <select id="clusterDetails" class="form-select" aria-label="Default select example">
                    <option disabled>Select Node Name</option>
                </select>
            </div>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </form>
    </div>
    <div id="noDataimg" class="monitor_pages">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-2">
            <div class="col-4 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" title="Database Details">Database Details</div>
                    <div class="card-body pt-0 p-2" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 270px);">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead class="position-sticky top-0">
                                <tr>
                                    <th title="Database Details">Database Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite_header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Name"> <span><i class="text-secondary cp-database me-1 fs-6"></i>Database Name</span> </td>
                                    <td class="text-truncate">@* <i class="cp-database me-1 text-primary"></i> *@<span id="PR_Database_name"></span></td>
                                    <td>@* <i class="cp-database me-1 text-primary"></i> *@<span id="Database_name"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Unique Name">
                                        <span> <i class="text-secondary cp-database-type me-1 fs-6"></i>Database Unique Name</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Unique_Name"></span></td>
                                    <td class="text-truncate"><span id="Unique_Name"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Role"><span><i class="text-secondary cp-database-role me-1 fs-6"></i>Database Role</span></td>
                                    <td class="text-truncate"><span id="PR_Database_role"></span></td>
                                    <td class="text-truncate"><span id="Database_role"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Open Mode">
                                        <span><i class="text-secondary cp-dr me-1 fs-6"></i>Open Mode</span>
                                    </td>
                                    <td class="text-truncate"><span id="PR_Openmode"></span></td>
                                    <td class="text-truncate"><span id="Openmode"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Created Time">
                                        <span>
                                            <i class="text-secondary cp-apply-finish-time
                                                me-1 fs-6"></i>Database Created Time
                                        </span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Database_createdtime"></span> </td>
                                    <td class="text-truncate"><span id="Database_createdtime"></span> </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Control File Type">
                                        <span>  <i class="text-secondary cp-control-file-type me-1 fs-6"></i>Control File Type</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Control_filetype"></span></td>
                                    <td class="text-truncate"><span id="Control_filetype"></span></td>
                                </tr>
                                @*  <tr>
                                <td class="fw-semibold text-truncate" title="Current SCN">
                                <span><i class="text-secondary cp-current-scn me-1 fs-6"></i>Current SCN</span>

                                </td>
                                <td class="text-truncate"><span id="PR_Currentscn"></span></td>
                                <td class="text-truncate"><span id="DR_Currentscn"></span></td>
                                </tr> *@
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Flashback_ON">
                                        <span><i class="text-secondary cp-warning me-1 fs-6"></i>Flashback_ON</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Flashback_on"></span></td>
                                    <td class="text-truncate"><span id="Flashback_on"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Version">
                                        <span>
                                            <i class="text-secondary cp-datas
                                                me-1 fs-6"></i>Database Version
                                        </span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Database_version"></span></td>
                                    <td class="text-truncate"><span id="Database_version"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Database Incarnation">
                                        <span> <i class="text-secondary cp-database me-1 fs-6"></i>Database Incarnation</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Database_incarnation"></span></td>
                                    <td class="text-truncate"><span id="Database_incarnation"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Resetlogs Change">
                                        <span>   <i class="text-secondary cp-refresh me-1 fs-6"></i>Resetlogs Change</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_DB_Reset_logschange"></span></td>
                                    <td class="text-truncate"><span id="DB_Reset_logschange"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Resetlogs Mode">
                                        <span> <i class="text-secondary cp-reset-log-change me-1 fs-6"></i>Resetlogs Mode</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Reset_logsmode"></span></td>
                                    <td class="text-truncate"><span id="Reset_logsmode"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Archive Mode">
                                        <span> <i class="text-secondary cp-refresh me-1 fs-6"></i>Archive Mode</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Archive_mode"></span></td>
                                    <td class="text-truncate"><span id="Archive_mode"></span></td>
                                </tr>
                                @*   <tr>
                                <td class="fw-semibold text-truncate" title="DB Size(in MB)">
                                <span>  <i class="text-secondary cp-storage-name me-1 fs-6"></i>DB Size(in MB)</span>

                                </td>
                                <td class="text-truncate"><span id="PR_Dbsize"></span></td>
                                <td class="text-truncate"><span id="DR_Dbsize"></span></td>
                                </tr> *@
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB Create File Dest">
                                        <span>     <i class="text-secondary cp-db-create-online me-1 fs-6"></i>DB Create File Dest</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Db_create_file_dest"></span></td>
                                    <td class="text-truncate"><span id="Db_create_file_dest"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB File Name Convert">
                                        <span><i class="text-secondary cp-database me-1 fs-6"></i>DB File Name Convert</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Db_file_name_convert"></span></td>
                                    <td class="text-truncate"><span id="Db_file_name_convert"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB Create Online">
                                        <span> <i class="text-secondary cp-db-create-online me-1 fs-6"></i>DB Create Online Log Dest   </span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Db_create_online_log_dest1"></span></td>
                                    <td class="text-truncate"><span id="Db_create_online_log_dest1"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Log File Name Convert">
                                        <span> <i class="text-secondary cp-log-file-name me-1 fs-6"></i>Log File Name Convert</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Log_file_name_convert"></span></td>
                                    <td class="text-truncate"><span id="Log_file_name_convert"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB Recovery File Dest">
                                        <span>   <i class="text-secondary cp-roate-settings me-1 fs-6"></i>DB Recovery File Dest</span>

                                    </td>
                                    <td>
                                        
                                        <span id="PR_Db_recovery_file_dest"></span>
                                    </td>
                                    <td class="text-truncate">
                                       
                                        <span id="Db_recovery_file_dest"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DB Recovery File Dest Size">
                                        <span> <i class="text-secondary cp-roate-settings me-1 fs-6"></i>DB Recovery File Dest Size</span>

                                    </td>
                                    <td>
                                        
                                        <span id="PR_Db_recovery_file_dest_size"></span>
                                    </td>
                                    <td>
                                        
                                        <span id="Db_recovery_file_dest_size"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Flashback Retention Target">
                                        <span> <i class="text-secondary cp-warning me-1 fs-6"></i>Flashback Retention Target</span>

                                    </td>
                                    <td>
                                        
                                        <span id="PR_Db_flashback_retention_target"></span>
                                    </td>
                                    <td>
                                        
                                        <span id="Db_flashback_retention_target"></span>
                                    </td>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>

            <div class="col-4 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" title="Replication Monitoring">Replication Monitoring</div>
                    <div class="card-body pt-0 p-2" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 270px);">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead class="position-sticky top-0">
                                <tr>
                                    <th title="Replication Details">Replication Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite_header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Active DG Enabled">
                                        <span> <i class="text-secondary cp-active-dg-enable me-1 fs-6"></i>Active DG Enabled</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Active_DG_Enabled"></span></td>
                                    <td class="text-truncate"><span id="Active_DG_Enabled"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="DG_Broker Status"><span><i class="text-secondary cp-files me-1 fs-6"></i>DG_Broker Status</span></td>
                                    <td class="text-truncate"><span id="PR_Dg_broker_status"></span></td>
                                    <td class="text-truncate"><span id="Dg_broker_status"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Dataguard_Status">
                                        <span> <i class="text-secondary cp-dataguard-status me-1 fs-6"></i>Dataguard_Status</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Dataguard_status"></span></td>
                                    <td class="text-truncate"><span id="Dataguard_status"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Recovery_Status"><span><i class="text-secondary cp-time me-1 fs-6"></i>Recovery_Status</span></td>
                                    <td class="text-truncate"><span id="PR_Recovery_Status"></span></td>
                                    <td class="text-truncate"><span id="Recovery_Status"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Switchover Status"><span><i class="text-secondary cp-files me-1 fs-6"></i>Switchover Status</span></td>
                                    <td class="text-truncate"><span id="PR_Switchover_status"></span></td>
                                    <td class="text-truncate"><span id="Switchover_status"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Log_Archive_Config">
                                        <span> <i class="text-secondary cp-configure-settings me-1 fs-6"></i>Log_Archive_Config</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Log_archive_config"></span></td>
                                    <td class="text-truncate"><span id="Log_archive_config"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Force Logging"><span><i class="text-secondary cp-left-right me-1 fs-6"></i>Force Logging</span></td>
                                    <td class="text-truncate"><span id="PR_Force_logging"></span></td>
                                    <td class="text-truncate"><span id="Force_logging"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="FAL Server"><i class="text-secondary cp-fal-server me-1 fs-6"></i>FAL Server</td>
                                    <td class="text-truncate"><span id="PR_Fal_server"></span></td>
                                    <td class="text-truncate"><span id="Fal_server"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="FAL Client"><i class="text-secondary cp-fal-client me-1 fs-6"></i>FAL Client</td>
                                    <td class="text-truncate"><span id="PR_Fal_client"></span></td>
                                    <td class="text-truncate"><span id="Fal_client"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Standby File Management"><i class="text-secondary cp-standby-file me-1 fs-6"></i>Standby File Management</td>
                                    <td class="text-truncate"><span id="PR_Standby_file_management"></span></td>
                                    <td class="text-truncate"><span id="Standby_file_management"></span></td>
                                </tr>

                                <tr>
                                    <td class="fw-semibold text-truncate" title="Standby REDO Logs"><i class="text-secondary cp-standby-redo-logs me-1 fs-6"></i>Standby REDO Logs</td>
                                    <td class="text-truncate"><span id="PR_Standby_redo_logs"></span></td>
                                    <td class="text-truncate"><span id="Standby_redo_logs"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Transport Lag"><i class="text-secondary cp-standby-redo-logs me-1 fs-6"></i>Transport Lag</td>
                                    <td class="text-truncate"><span id="PR_Transport_lag"></span></td>
                                    <td class="text-truncate"><span id="Transport_lag"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Apply Lag"><i class="text-secondary cp-apply-lag me-1 fs-6"></i>Apply Lag</td>
                                    <td class="text-truncate"><span id="PR_Apply_lag"></span></td>
                                    <td class="text-truncate"><span id="Apply_lag"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Apply Finish Time"><i class="text-secondary cp-time me-1 fs-6"></i>Apply Finish Time</td>
                                    <td class="text-truncate"><span id="PR_Apply_finish_time"></span></td>
                                    <td class="text-truncate"><span id="Apply_finish_time"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Estimated Startup Time"><i class="text-secondary cp-time me-1 fs-6"></i>Estimated Startup Time</td>
                                    <td class="text-truncate"><span id="PR_Estimated_startup_time"></span></td>
                                    <td class="text-truncate"><span id="Estimated_startup_time"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Archive Dest Location"><span><i class="text-secondary cp-location me-1 fs-6"></i>Archive Dest Location</span></td>
                                    <td class="text-truncate"><span id="PR_Archive_Dest_Location"></span></td>
                                    <td class="text-truncate"><span id="Archive_Dest_Location"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Protection Mode"><span><i class="text-secondary cp-protection-mode me-1 fs-6"></i>Protection Mode</span></td>
                                    <td class="text-truncate"><span id="PR_Protection_mode"></span></td>
                                    <td class="text-truncate"><span id="Protection_mode"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Transmit Mode"><span><i class="text-secondary cp-left-right me-1 fs-6"></i>Transmit Mode</span></td>
                                    <td class="text-truncate"><span id="PR_Transmit_mode"></span></td>
                                    <td class="text-truncate"><span id="Transmit_mode"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Recovery Mode"><span><i class="text-secondary cp-time me-1 fs-6"></i>Recovery Mode</span></td>
                                    <td class="text-truncate"><span id="PR_Recovery_mode"></span></td>
                                    <td class="text-truncate"><span id="Recovery_mode"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Affirm"><span><i class="text-secondary cp-files me-1 fs-6"></i>Affirm</span></td>
                                    <td class="text-truncate"><span id="PR_Affirm"></span></td>
                                    <td class="text-truncate"><span id="Affirm"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Archiver"><i class="text-secondary cp-files me-1 fs-6"></i>Archiver</td>
                                    <td class="text-truncate"><span id="PR_Archiver"></span></td>
                                    <td class="text-truncate"><span id="Archiver"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Archivelog Compression">
                                        <span> <i class="text-secondary cp-archive-mode me-1 fs-6"></i>Archivelog Compression</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Archivelog_compression"></span></td>
                                    <td class="text-truncate"><span id="Archivelog_compression"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Delay Mins"><span><i class="text-secondary cp-folder-server me-1 fs-6"></i>Delay Mins</span></td>
                                    <td class="text-truncate"><span id="PR_Delay_mins"></span></td>
                                    <td class="text-truncate"><span id="Delay_mins"></span></td>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid ">

                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram<span id="selctedNodeNameSolution"></span></div>
                    <div class="card-body text-center d-flex align-items-center justify-content-center w-100 h-100" id="Solution_Diagram">
                        @* <div id="SolutionDiagramRAC" style="width:100%; height:100%;"></div> *@
                    </div>
                </div>

                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title" style="font-size:15px" title="Database Size">Database Size</div>
                    <div class="card-body d-flex pt-0 align-items-center gap-4 justify-content-center">
                        <div>
                            <i class="cp-database-sizes text-light" style="font-size: 5.9rem;"></i>
                        </div>
                        <div class="d-grid  border-start border-3">
                            <div class="text-primary ms-2 fw-semibold" title="Primary">Primary</div>
                            <span class="text-secondary mb-1 ms-2" title="Database Size">Database Size</span>
                            <h6 class="mb-0 fw-bold ms-2" id="PR_Dbsize"></h6>
                        </div>
                        @*<div class="w-50" id="DatabaseSize"></div>*@
                        <div>
                            <div class="d-grid">
                                <div class="text-info ms-2 fw-semibold dynamicSite_header" title="DR">DR</div>
                                <span class="text-secondary mb-1 ms-2" title="Database Size">Database Size</span>
                                <h6 class="mb-0 fw-bold ms-2" id="Dbsize"></h6>
                            </div>
                        </div>
                    </div>
                </div>



                <div class="d-flex gap-2">
                    <div class="card Card_Design_None w-50 mb-0">
                        <div class="card-body">
                            <i class="cp-log-sequence text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="Log Sequence"></i><span title="Log Sequence" class="fw-semibold">Log Sequence</span>
                            <div class=" mt-3">
                                <div class="w-50 d-grid mb-0">
                                    <small class="text-primary fs-7 mb-1 fw-semibold" title="Primary">Primary</small>
                                    <h6 class="mb-0  fs-7" id="PR_Log_sequence"></h6>
                                </div>
                                <div class="w-50 d-grid">
                                    <small class="text-info fs-7 mb-1 fw-semibold dynamicSite_header" title="DR">DR</small>
                                    <h6 class="mb-0 fs-7 d-inline-block" id="Log_sequence"></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card Card_Design_None w-50 mb-2">
                        <div class="card-body">
                            <i class="cp-current-scn text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="Current SCN"></i><span title="Current SCN" class="fw-semibold">Current SCN</span>
                            <div class=" mt-3">
                                <div class="w-50 d-grid mb-2">

                                    <small class="text-primary fs-7 mb-1 fw-semibold" title="Primary">Primary</small>
                                    <h6 class="mb-0  fs-7" id="PR_Currentscn"></h6>

                                </div>
                                <div class="w-50 d-grid">
                                    <small class="text-info fs-7 mb-1 fw-semibold dynamicSite_header" title="DR">DR</small>
                                    <h6 class="mb-0 fs-7" id="Currentscn"></h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
            <!-- <div class="row mt-3"> -->
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-0" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 310px);">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title="Cluster Details">
                            Cluster Details <span id="selctedNodeNameCluster"></span>
                        </span>

                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" id="tableCluster" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Cluster Details">Cluster Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite_header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Cluster Name"><span><i class="text-secondary cp-cluster-database me-1 fs-6"></i>Cluster Name</span></td>
                                    <td class="text-truncate"><span id="PR_Cluster_Name"></span></td>
                                    <td class="text-truncate"><span id="Cluster_Name"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Clusterware Active Version">
                                        <span><i class="text-secondary cp-data me-1 fs-6"></i>Clusterware Active Version</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_ClusterWare_Active_Version"></span></td>
                                    <td class="text-truncate"><span id="ClusterWare_Active_Version"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="OHAS Status"><span><i class="text-secondary cp-ohas-status me-1 fs-6"></i>OHAS Status</span></td>
                                    <td class="text-truncate"><span id="PR_OHAS_Status"></span></td>
                                    <td class="text-truncate"><span id="OHAS_Status"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="CRS Status"><span><i class="text-secondary cp-cluster-database me-1 fs-6"></i>CRS Status</span></td>
                                    <td class="text-truncate"><span id="PR_CRS_Status"></span></td>
                                    <td class="text-truncate"><span id="CRS_Status"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="CSS Status"><span><i class="text-secondary cp-cluster-database me-1 fs-6"></i>CSS Status</span></td>
                                    <td class="text-truncate"><span id="PR_CSS_Status"></span></td>
                                    <td class="text-truncate"><span id="CSS_Status"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="EVM Status"><span><i class="text-secondary cp-ohas-status me-1 fs-6"></i>EVM Status</span></td>
                                    <td class="text-truncate"><span id="PR_EVM_Status"></span></td>
                                    <td class="text-truncate"><span id="EVM_Status"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Cluster Listener">
                                        <span> <i class="text-secondary cp-cluster-database me-1 fs-6"></i>Cluster Listener</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Cluster_Listener"></span></td>
                                    <td class="text-truncate"><span id="Cluster_Listener"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Scan Listener Status">
                                        <span> <i class="text-secondary cp-cluster-database me-1 fs-6"></i>Scan Listener Status</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Scan_Listener_Status"></span></td>
                                    <td class="text-truncate"><span id="Scan_Listener_Status"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Scan Status">
                                        <span> <i class="text-secondary cp-cluster-database me-1 fs-6"></i>Scan Status</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_Scan_Status"></span></td>
                                    <td class="text-truncate"><span id="Scan_Status"></span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" title="Pluggable Databases">Pluggable Databases</div>
                    <div class="card-body pt-0">
                        <div class="NoData text-center p-2 d-grid justify-content-center">
                            <img src="/img/isomatric/nodatalag.svg" class="mx-auto" />
                            <span class="text-danger">
                                Pluggable Databases
                                not configured.
                            </span>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-8 d-grid ">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title="Instance Details">
                            Instance Details <span id="selctedNodeNameInstance"></span>
                        </span>
                        @* <form>
                        <select id="instanceDetails" class="form-select" aria-label="Default select example">
                        <option disabled >Select Node Name</option>
                        </select>
                        </form>*@
                    </div>
                    <div class="card-body pt-0 p-2" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 275px);">
                        <table class="table mb-0" id="tableInstance" style="table-layout:fixed">
                            <thead class="position-sticky top-0">
                                <tr>
                                    <th title="Instance Details">Instance Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite_header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Instance Name"><span><i class="text-secondary cp-instance-name me-1 fs-6"></i>Instance Name</span></td>
                                    <td class="text-truncate"><span id="PR_InstanceName"></span></td>
                                    <td class="text-truncate"><span id="InstanceName"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Instance ID"><span><i class="text-secondary cp-instance-id me-1 fs-6"></i>Instance ID</span></td>
                                    <td class="text-truncate"><span id="PR_InstanceId"></span></td>
                                    <td class="text-truncate"><span id="InstanceId"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Instance Startup Time">
                                        <span>  <i class="text-secondary cp-table-clock me-1 fs-6"></i>Instance Startup Time</span>

                                    </td>
                                    <td class="text-truncate">
                                       <span id="PR_InstanceStartUpTime"></span>
                                    </td>
                                    <td class="text-truncate">
                                        <span id="InstanceStartUpTime"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Open Mode"><span><i class="text-secondary cp-platform-name me-1 fs-6"></i>Open Mode</span></td>
                                    <td class="text-truncate"><span id="PR_OpenMode"></span></td>
                                    <td class="text-truncate"><span id="OpenMode"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Cluster Database">
                                        <span>  <i class="text-secondary cp-cluster-database me-1 fs-6"></i>Cluster Database</span>

                                    </td>
                                    <td class="text-truncate"><span id="PR_IsClusterDatabase"></span></td>
                                    <td class="text-truncate"><span id="IsClusterDatabase"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Control File Name">
                                        <span><i class="text-secondary cp-control-file-type me-1 fs-6"></i>Control File Name</span>

                                    </td>
                                    <td class="text-truncate">
                                        <span id="PR_ControlfileName"></span>
                                    </td>
                                    <td class="text-truncate"><span id="ControlfileName"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Parameter File"><span><i class="text-secondary cp-parameter-file me-1 fs-6"></i>Parameter File</span></td>
                                    <td class="text-truncate">
                                        <span id="PR_ParameterFile"></span>
                                    </td>
                                    <td class="text-truncate"><span id="ParameterFile"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Platform Name"><span><i class="text-secondary cp-platform-name me-1 fs-6"></i>Platform Name</span></td>
                                    <td class="text-truncate"><span id="PR_Platform_name"></span></td>
                                    <td class="text-truncate"><span id="Platform_name"></span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="row">
                    <div class="col-12 d-grid ">
                        <div class="card Card_Design_None mb-2">
                            <div class="card-header card-title d-flex align-items-center justify-content-between">
                                <span title="Multi Tenancy">Multi Tenancy</span>
                            </div>
                            <div class="card-body pt-0 p-2">
                                <table class="table mb-0" style="table-layout:fixed">
                                    <thead>
                                        <tr>
                                            <th title="Multi Tenancy">Multi Tenancy</th>
                                            <th class="text-primary" title="Primary">Primary</th>
                                            <th class="text-info dynamicSite_header" title="DR">DR</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="fw-semibold text-truncate" title="CDB"><i class="text-secondary cp-control-file-type me-1 fs-6"></i>CDB</td>
                                            <td class="text-truncate"><span id="PR_CDB"></span></td>
                                            <td class="text-truncate"><span id="CDB"></span></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold text-truncate" title="Containers"><i class="text-secondary cp-dr me-1 fs-6"></i>Containers</td>
                                            <td class="text-truncate"><span id="PR_Containers"></span></td>
                                            <td class="text-truncate"><span id="Containers"></span></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-semibold text-truncate" title="PDBs"><i class="text-secondary cp-PDBs me-1 fs-6"></i>PDBs</td>
                                            <td class="text-truncate"><span id="PR_Pdbs"></span></td>
                                            <td class="text-truncate"><span id="Pdbs"></span></td>
                                        </tr>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 d-grid">
                        <div class="card Card_Design_None mb-0">
                            <div class="card-header card-title d-flex align-items-center justify-content-between">
                                <span title="TNS Service Details">
                                    TNS Service Details
                                </span>

                            </div>
                            <div class="card-body pt-0 p-2">
                                <table class="table mb-0" style="table-layout:fixed">
                                    <thead>
                                        <tr>
                                            <th title="TNS Service Details">TNS Service Details</th>
                                            <th class="text-primary" title="Primary">Primary</th>
                                            <th class="text-info dynamicSite_header" title="DR">DR</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="fw-semibold text-truncate" title="Service"><i class="text-secondary cp-service me-1 fs-6"></i>Service</td>
                                            @* <td class="fw-semibold text-truncate" id="PR_TNSServiceName"></td> *@
                                            <td>
                                                <ul class="list-group list-group-flush" id="PR_TNSServiceName">
                                                </ul>
                                            </td>
                                            <td>
                                                <ul class="list-group list-group-flush" id="TNSServiceName">
                                                </ul>
                                            </td>
                                            @* <td class="fw-semibold text-truncate" id="TNSServiceName"></td> *@
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- </div> -->

            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title" title="ASM Details">ASM Details </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th title="ASM">ASM</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite_header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Services">Services</td>
                                    <td class="text-truncate">
                                        <div style="height:215px;overflow:auto">
                                        <table class="table mb-0" id="asmPrimary" style="table-layout:fixed">
                                                <thead class="position-sticky top-0">
                                                <tr>
                                                    <th style="width:50px">#</th>
                                                    <th title="Name">Name</th>
                                                    <th title="State">State</th>
                                                    <th title="Type">Type</th>
                                                    <th title="Total MB">Total MB</th>
                                                    <th title="Fee MB">Free MB</th>
                                                    <th title="Used(%)">Used(%)</th>
                                                </tr>
                                            </thead>
                                            <tbody id="prASM">
                                            </tbody>
                                        </table>
                                        </div>
                                    </td>
                                    <td class="text-truncate">
                                        <div style="height:215px;overflow:auto">
                                        <table class="table mb-0" id="asmDR" style="table-layout:fixed">
                                                <thead class="position-sticky top-0">
                                                <tr>
                                                    <th style="width:50px">#</th>
                                                    <th title="Name">Name</th>
                                                    <th title="State">State</th>
                                                    <th title="Type">Type</th>
                                                    <th title="Total MB">Total MB</th>
                                                    <th title="Fee MB">Free MB</th>
                                                    <th title="Used(%)">Used(%)</th>
                                                </tr>
                                            </thead>
                                            <tbody id="drASM">
                                            </tbody>
                                        </table>
                                        </div>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-2" id="mssqlserver">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title=" Services ">
                            Services
                        </span>

                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" id="tableCluster" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Service / Process / Workflow Name">Service / Process / Workflow Name</th>
                                    <th class="text-primary" title="Server IP/HostName">Server IP/HostName</th>
                                    <th class="">Status</th>
                                </tr>
                            </thead>
                            <tbody id="mssqlserverbody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="row g-2">
            <div class="col-4 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title d-flex align-items-center justify-content-between mb-2">
                        <span class="text-truncate d-inline-block" title="Archive Log Generation Hourly(Count)">
                            Archive Log Generation
                            Hourly(Count)
                        </span>
                    </div>
                    <div class="card-body pt-0">
                        <div id="ArchiveLogHour" class="d-flex" style="width:100%; height:250px;"></div>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title d-flex align-items-center justify-content-between mb-2">
                        <span class="text-truncate d-inline-block" title="Archive Log Generation Hourly Last 24Hrs(Size)">
                            Archive Log Generation
                            Hourly Last 24Hrs(Size)
                        </span>

                    </div>
                    <div class="card-body pt-0">
                        <div id="ArchiveLogDay" class="d-flex" style="width:100%; height:250px;"></div>
                    </div>
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title d-flex align-items-center justify-content-between mb-2">
                        <span class="text-truncate d-inline-block" title="Archive Log Generation Past Week (Size)">
                            Archive Log Generation Past Week (Size)
                        </span>
                    </div>
                    <div class="card-body pt-0">
                        <div id="ArchiveLogWeek" class="d-flex" style="width:100%; height:250px;"></div>
                    </div>
                </div>
            </div>

        </div>

    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/js/Monitoring/ArchiveLog.js"></script>
@* <script src="~/js/monitoring/oraclerac.js"></script> *@
<script src="~/js/Monitoring/MonitoringOracleRAC.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>

