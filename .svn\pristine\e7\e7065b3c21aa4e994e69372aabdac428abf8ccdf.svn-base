﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.NodeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.Node.Queries.GetPaginatedList;

public class
    GetNodePaginatedListQueryHandler : IRequestHandler<GetNodePaginatedListQuery, PaginatedResult<NodeListVm>>
{
    private readonly IMapper _mapper;
    private readonly INodeRepository _nodeRepository;

    public GetNodePaginatedListQueryHandler(IMapper mapper, INodeRepository nodeRepository)
    {
        _mapper = mapper;
        _nodeRepository = nodeRepository;
    }

    public async Task<PaginatedResult<NodeListVm>> Handle(GetNodePaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new NodeFilterSpecification(request.SearchString);

        var nodes = request.TypeId != null
            ?await _nodeRepository.GetNodeByType(request.TypeId, request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder)
            :await _nodeRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var nodeList = _mapper.Map<PaginatedResult<NodeListVm>>(nodes);
        //var nodes = request.TypeId != null
        //    ? _nodeRepository.GetNodeByType(request.TypeId)
        //    : _nodeRepository.GetPaginatedQuery();      

        //var nodeList = await nodes
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<NodeListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return nodeList;
    }
}