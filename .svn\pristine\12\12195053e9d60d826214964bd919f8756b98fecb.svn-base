﻿using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoWorkflowActionDataAttribute : AutoDataAttribute
{
    public AutoWorkflowActionDataAttribute()
      : base(() =>
      {
          var fixture = new Fixture();

          fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateWorkflowActionCommand>(p => p.ActionName, 10));
          fixture.Customize<CreateWorkflowActionCommand>(c => c.With(b => b.NodeId, 10.ToString()));

          fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateWorkflowActionCommand>(p => p.ActionName, 10));
          fixture.Customize<UpdateWorkflowActionCommand>(c => c.With(b => b.Id, 0.ToString()));

          return fixture;
      })
    {

    }
}