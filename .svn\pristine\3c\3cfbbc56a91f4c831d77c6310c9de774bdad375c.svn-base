﻿using ContinuityPatrol.Application.Features.ReportSchedule.Event.Create;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ReportSchedule.Commands.Create;

public class
    CreateReportScheduleCommandHandler : IRequestHandler<CreateReportScheduleCommand, CreateReportScheduleResponse>
{
    private readonly IJobScheduler _client;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;
    private readonly IPublisher _publisher;
    private readonly IReportScheduleRepository _reportScheduleRepository;

    public CreateReportScheduleCommandHandler(IMapper mapper, IPublisher publisher,
        IReportScheduleRepository reportScheduleRepository, ILoggedInUserService loggedInUserService,
        ILoadBalancerRepository nodeConfigurationRepository, IJobScheduler client)
    {
        _mapper = mapper;
        _publisher = publisher;
        _reportScheduleRepository = reportScheduleRepository;
        _loggedInUserService = loggedInUserService;
        _nodeConfigurationRepository = nodeConfigurationRepository;
        _client = client;
    }

    public async Task<CreateReportScheduleResponse> Handle(CreateReportScheduleCommand request,
        CancellationToken cancellationToken)
    {
        request.CompanyId = _loggedInUserService.CompanyId;

        var reportSchedule = _mapper.Map<Domain.Entities.ReportSchedule>(request);

        reportSchedule = await _reportScheduleRepository.AddAsync(reportSchedule);

        await _publisher.Publish(new ReportScheduleCreatedEvent { ReportName = reportSchedule.ReportName },
            cancellationToken);

        var nodeConfig =
            await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString())
            ?? await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(),
                ServiceType.LoadBalancer.ToString());

        if (nodeConfig is not null)
        {
            var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

            var url = UrlHelper.GenerateScheduleReportUrl(baseUrl, reportSchedule.ReferenceId);

            await _client.ScheduleJob(reportSchedule.ReferenceId, new Dictionary<string, string> { ["url"] = url });
        }

        var response = new CreateReportScheduleResponse
        {
            Message = Message.Create("Report Scheduler", reportSchedule.ReportName),
            Id = reportSchedule.ReferenceId
        };
        
        return response;
    }
}