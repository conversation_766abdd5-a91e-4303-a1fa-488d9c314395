using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Commands.Delete;
using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceAvailabilityModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class BusinessServiceAvailabilityControllerTests : IClassFixture<BusinessServiceAvailabilityFixture>
{
    private readonly BusinessServiceAvailabilityFixture _businessServiceAvailabilityFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly BusinessServiceAvailabilityController _controller;

    public BusinessServiceAvailabilityControllerTests(BusinessServiceAvailabilityFixture businessServiceAvailabilityFixture)
    {
        _businessServiceAvailabilityFixture = businessServiceAvailabilityFixture;

        var testBuilder = new ControllerTestBuilder<BusinessServiceAvailabilityController>();
        _controller = testBuilder.CreateController(
            _ => new BusinessServiceAvailabilityController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetBusinessServiceAvailabilities_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessServiceAvailabilityListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_businessServiceAvailabilityFixture.BusinessServiceAvailabilityListVm);

        // Act
        var result = await _controller.GetBusinessServiceAvailability();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var availabilities = Assert.IsAssignableFrom<List<BusinessServiceAvailabilityListVm>>(okResult.Value);
        Assert.Equal(3, availabilities.Count);
    }

    [Fact]
    public async Task GetBusinessServiceAvailabilityById_ReturnsExpectedDetail()
    {
        // Arrange
        var availabilityId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessServiceAvailabilityDetailQuery>(q => q.Id == availabilityId), default))
            .ReturnsAsync(_businessServiceAvailabilityFixture.BusinessServiceAvailabilityDetailVm);

        // Act
        var result = await _controller.GetBusinessServiceAvailabilityById(availabilityId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var availability = Assert.IsType<BusinessServiceAvailabilityDetailVm>(okResult.Value);
        Assert.NotNull(availability);
    }

    [Fact]
    public async Task CreateBusinessServiceAvailability_Returns201Created()
    {
        // Arrange
        var command = _businessServiceAvailabilityFixture.CreateBusinessServiceAvailabilityCommand;
        var expectedMessage = "BusinessServiceAvailability has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBusinessServiceAvailabilityResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBusinessServiceAvailability(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBusinessServiceAvailabilityResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBusinessServiceAvailability_ReturnsOk()
    {
        // Arrange
        var command = _businessServiceAvailabilityFixture.UpdateBusinessServiceAvailabilityCommand;
        var expectedMessage = "BusinessServiceAvailability has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateBusinessServiceAvailabilityResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateBusinessServiceAvailability(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBusinessServiceAvailabilityResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBusinessServiceAvailability_ReturnsOk()
    {
        // Arrange
        var availabilityId = Guid.NewGuid().ToString();
        var expectedMessage = "BusinessServiceAvailability has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBusinessServiceAvailabilityCommand>(c => c.Id == availabilityId), default))
            .ReturnsAsync(new DeleteBusinessServiceAvailabilityResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteBusinessServiceAvailability(availabilityId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBusinessServiceAvailabilityResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task GetBusinessServiceAvailabilityById_HandlesInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetBusinessServiceAvailabilityById("invalid-guid"));
    }

    [Fact]
    public async Task CreateBusinessServiceAvailability_ValidatesTotalBusinessService()
    {
        // Arrange
        var command = new CreateBusinessServiceAvailabilityCommand
        {
            TotalBusinessService = -1, // Negative value should cause validation error
            AvailabilityUp = 10,
            AvailabilityDown = 5
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("TotalBusinessService must be non-negative"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBusinessServiceAvailability(command));
    }

    [Fact]
    public async Task UpdateBusinessServiceAvailability_ValidatesAvailabilityExists()
    {
        // Arrange
        var command = new UpdateBusinessServiceAvailabilityCommand
        {
            Id = Guid.NewGuid().ToString(),
            TotalBusinessService = 100,
            AvailabilityUp = 80,
            AvailabilityDown = 20
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("BusinessServiceAvailability not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateBusinessServiceAvailability(command));
    }

    [Fact]
    public async Task CreateBusinessServiceAvailability_HandlesComplexAvailabilityMetrics()
    {
        // Arrange
        var command = new CreateBusinessServiceAvailabilityCommand
        {
            TotalBusinessService = 500,
            AvailabilityUp = 450,
            AvailabilityDown = 50,
            TotalBusinessFunction = 250,
            BusinessFunctionUp = 230,
            BusinessFunctionDown = 20,
            HealthUp = 480,
            HealthDown = 20,
            DRReadynessUp = 400,
            DRReadynessDown = 100,
            TotalAlert = 75,
            AlertUp = 60,
            AlertDown = 15,
            TotalIncident = 25,
            IncidentUp = 20,
            IncidentDown = 5
        };

        var expectedMessage = "BusinessServiceAvailability has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBusinessServiceAvailabilityResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBusinessServiceAvailability(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBusinessServiceAvailabilityResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBusinessServiceAvailability_HandlesMetricUpdates()
    {
        // Arrange
        var command = new UpdateBusinessServiceAvailabilityCommand
        {
            Id = Guid.NewGuid().ToString(),
            TotalBusinessService = 600,
            AvailabilityUp = 550,
            AvailabilityDown = 50,
            TotalBusinessFunction = 300,
            BusinessFunctionUp = 280,
            BusinessFunctionDown = 20,
            HealthUp = 580,
            HealthDown = 20,
            DRReadynessUp = 500,
            DRReadynessDown = 100,
            TotalAlert = 80,
            AlertUp = 70,
            AlertDown = 10,
            TotalIncident = 30,
            IncidentUp = 25,
            IncidentDown = 5
        };

        var expectedMessage = "BusinessServiceAvailability has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateBusinessServiceAvailabilityResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateBusinessServiceAvailability(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBusinessServiceAvailabilityResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }
}
