﻿using ContinuityPatrol.Application.Features.SmtpConfiguration.Events.Update;

namespace ContinuityPatrol.Application.UnitTests.Features.SmtpConfiguration.Events
{
    public class UpdateSmtpConfigurationEventTests
    {
        private readonly Mock<ILogger<SmtpConfigurationUpdatedEventHandler>> _loggerMock;
        private readonly Mock<IUserActivityRepository> _userActivityRepositoryMock;
        private readonly Mock<ILoggedInUserService> _userServiceMock;
        private readonly SmtpConfigurationUpdatedEventHandler _handler;

        public UpdateSmtpConfigurationEventTests()
        {
            _loggerMock = new Mock<ILogger<SmtpConfigurationUpdatedEventHandler>>();
            _userActivityRepositoryMock = new Mock<IUserActivityRepository>();
            _userServiceMock = new Mock<ILoggedInUserService>();

            _handler = new SmtpConfigurationUpdatedEventHandler(
                _userServiceMock.Object,
                _loggerMock.Object,
                _userActivityRepositoryMock.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldLogAndSaveUserActivity_WhenEventIsHandled()
        {
            var updatedEvent = new SmtpConfigurationUpdatedEvent
            {
                UserName = "updatedUser"
            };

            _userServiceMock.Setup(us => us.UserId).Returns("123");
            _userServiceMock.Setup(us => us.LoginName).Returns("testLogin");
            _userServiceMock.Setup(us => us.RequestedUrl).Returns("/api/smtp/update");
            _userServiceMock.Setup(us => us.CompanyId).Returns("456");
            _userServiceMock.Setup(us => us.IpAddress).Returns("***********");

            var cancellationToken = CancellationToken.None;

            await _handler.Handle(updatedEvent, cancellationToken);

            _userActivityRepositoryMock.Verify(
                repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                    ua.UserId == "123" &&
                    ua.LoginName == "testLogin" &&
                    ua.RequestUrl == "/api/smtp/update" &&
                    ua.CompanyId == "456" &&
                    ua.HostAddress == "***********" &&
                    ua.Action == "Update SmtpConfiguration" &&
                    ua.Entity == "SmtpConfiguration" &&
                    ua.ActivityType == "Update" &&
                    ua.ActivityDetails == "SmtpConfiguration 'updatedUser' updated successfully."
                )),
                Times.Once
            );

            _loggerMock.Verify(
                logger => logger.LogInformation("SmtpConfiguration 'updatedUser' updated successfully."),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ShouldNotThrow_WhenRepositoryFails()
        {
            var updatedEvent = new SmtpConfigurationUpdatedEvent
            {
                UserName = "failingUser"
            };

            _userServiceMock.Setup(us => us.UserId).Returns("123");
            _userActivityRepositoryMock
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .ThrowsAsync(new System.Exception("Database error"));

            var cancellationToken = CancellationToken.None;

            var exception = await Record.ExceptionAsync(() => _handler.Handle(updatedEvent, cancellationToken));

            Assert.Null(exception);

            _loggerMock.Verify(
                logger => logger.LogInformation("SmtpConfiguration 'failingUser' updated successfully."),
                Times.Once
            );
        }
    }
}
