﻿using ContinuityPatrol.Application.Features.Report.Event.Delete;

namespace ContinuityPatrol.Application.Features.Report.Commands.Delete;

public class DeleteReportCommandHandler : IRequestHandler<DeleteReportCommand, DeleteReportResponse>
{
    private readonly IPublisher _publisher;
    private readonly IReportRepository _reportRepository;

    public DeleteReportCommandHandler(IReportRepository reportRepository, IPublisher publisher)
    {
        _reportRepository = reportRepository;
        _publisher = publisher;
    }

    public async Task<DeleteReportResponse> Handle(DeleteReportCommand request, CancellationToken cancellationToken)
    {
        var eventToDelete = await _reportRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.Report),
            new NotFoundException(nameof(Report), request.Id));

        eventToDelete.IsActive = false;

        await _reportRepository.UpdateAsync(eventToDelete);

        var response = new DeleteReportResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.Report), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new ReportDeletedEvent { ReportName = eventToDelete.Name }, cancellationToken);

        return response;
    }
}