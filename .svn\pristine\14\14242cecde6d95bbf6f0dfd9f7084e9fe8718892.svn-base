﻿using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetDetail;

namespace ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetImpactByBusinessServiceId;

public class
    GetImpactByBusinessServiceIdQueryHandler : IRequestHandler<GetImpactByBusinessServiceIdQuery,
        ImpactAvailabilityDetailVm>
{
    private readonly IImpactAvailabilityRepository _impactAvailabilityRepository;
    private readonly IMapper _mapper;

    public GetImpactByBusinessServiceIdQueryHandler(IMapper mapper,
        IImpactAvailabilityRepository impactAvailabilityRepository)
    {
        _mapper = mapper;
        _impactAvailabilityRepository = impactAvailabilityRepository;
    }

    public async Task<ImpactAvailabilityDetailVm> Handle(GetImpactByBusinessServiceIdQuery request,
        CancellationToken cancellationToken)
    {
        var impactAvailability =
            await _impactAvailabilityRepository.GetImpactAvailabilityByBusinessServiceId(request.BusinessServiceId);

        Guard.Against.NullOrDeactive(impactAvailability, nameof(Domain.Entities.ImpactAvailability),
            new NotFoundException(nameof(Domain.Entities.ImpactAvailability), request.BusinessServiceId));

        var impactAvailabilityDto = _mapper.Map<ImpactAvailabilityDetailVm>(impactAvailability);

        return impactAvailabilityDto;
    }
}