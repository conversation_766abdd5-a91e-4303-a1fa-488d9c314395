﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.Company.Commands.Create;

public class CreateCompanyCommand : IRequest<CreateCompanyResponse>
{
    public string Name { get; set; }

    public string DisplayName { get; set; }

    [JsonIgnore] public bool IsParent { get; set; }

    public string ParentId { get; set; }

    public string CompanyLogo { get; set; }

    public string LogoName { get; set; }

    public string WebAddress { get; set; }

    [JsonIgnore] public string CreatedBy { get; set; }

    [JsonIgnore] public string LastModifiedBy { get; set; }

    public override string ToString()
    {
        return $"Name: {Name}; DisplayName: {DisplayName};";
    }
}