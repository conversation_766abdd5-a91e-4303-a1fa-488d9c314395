﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.LoadBalancer.Events.Create;

public class LoadBalancerCreatedEventHandler : INotificationHandler<LoadBalancerCreatedEvent>
{
    private readonly ILogger<LoadBalancerCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public LoadBalancerCreatedEventHandler(ILoggedInUserService userService,
        ILogger<LoadBalancerCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(LoadBalancerCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} {Modules.LoadBalancer}",
            Entity = Modules.LoadBalancer.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"{createdEvent.TypeCategory} '{createdEvent.Name}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"{createdEvent.TypeCategory} '{createdEvent.Name}' created successfully.");
    }
}