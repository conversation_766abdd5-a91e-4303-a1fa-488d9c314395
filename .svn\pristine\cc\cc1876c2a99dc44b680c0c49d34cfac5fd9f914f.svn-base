using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class CyberComponentRepositoryTests : IClassFixture<CyberComponentFixture>
{
    private readonly CyberComponentFixture _cyberComponentFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberComponentRepository _repository;

    public CyberComponentRepositoryTests(CyberComponentFixture cyberComponentFixture)
    {
        _cyberComponentFixture = cyberComponentFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberComponentRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var cyberComponent = _cyberComponentFixture.CyberComponentDto;

        // Act
        await _dbContext.CyberComponents.AddAsync(cyberComponent);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(cyberComponent.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(cyberComponent.Name, result.Name);
        Assert.Equal(cyberComponent.Description, result.Description);
        Assert.Single(_dbContext.CyberComponents);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var cyberComponent = _cyberComponentFixture.CyberComponentDto;
        await _dbContext.CyberComponents.AddAsync(cyberComponent);
        await _dbContext.SaveChangesAsync();

        cyberComponent.Name = "UpdatedName";
        cyberComponent.Description = "UpdatedDescription";
        cyberComponent.Status = "UpdatedStatus";

        // Act
        _dbContext.CyberComponents.Update(cyberComponent);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(cyberComponent.ReferenceId);

        // Assert
        Assert.Equal("UpdatedName", result.Name);
        Assert.Equal("UpdatedDescription", result.Description);
        Assert.Equal("UpdatedStatus", result.Status);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var cyberComponent = _cyberComponentFixture.CyberComponentDto;
        await _dbContext.CyberComponents.AddAsync(cyberComponent);
        await _dbContext.SaveChangesAsync();

        // Act
        cyberComponent.IsActive = false;

        _dbContext.CyberComponents.Update(cyberComponent);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var cyberComponent = _cyberComponentFixture.CyberComponentDto;
        await _dbContext.CyberComponents.AddAsync(cyberComponent);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(cyberComponent.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(cyberComponent.Id, result.Id);
        Assert.Equal(cyberComponent.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var cyberComponent = _cyberComponentFixture.CyberComponentDto;
        await _dbContext.CyberComponents.AddAsync(cyberComponent);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(cyberComponent.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(cyberComponent.ReferenceId, result.ReferenceId);
        Assert.Equal(cyberComponent.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var cyberComponents = _cyberComponentFixture.CyberComponentList;
        await _repository.AddRangeAsync(cyberComponents);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(cyberComponents.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetCyberComponentBySiteId Tests

    [Fact]
    public async Task GetCyberComponentBySiteId_ShouldReturnComponentsForSite()
    {
        // Arrange
        var siteId = "SITE_001";
        var cyberComponents = new List<CyberComponent>
        {
            new CyberComponent 
            { 
                Name = "Component1",
                SiteId = siteId,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberComponent 
            { 
                Name = "Component2",
                SiteId = siteId,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberComponent 
            { 
                Name = "Component3",
                SiteId = "SITE_002",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(cyberComponents);

        // Act
        var result = await _repository.GetCyberComponentBySiteId(siteId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(siteId, x.SiteId));
    }

    [Fact]
    public async Task GetCyberComponentBySiteId_ShouldReturnEmpty_WhenNoComponentsForSite()
    {
        // Arrange
        var cyberComponents = _cyberComponentFixture.CyberComponentList;
        await _repository.AddRangeAsync(cyberComponents);

        // Act
        var result = await _repository.GetCyberComponentBySiteId("NON_EXISTENT_SITE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var cyberComponent = _cyberComponentFixture.CyberComponentDto;
        cyberComponent.Name = "ExistingName";
        await _dbContext.CyberComponents.AddAsync(cyberComponent);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.IsNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var cyberComponents = _cyberComponentFixture.CyberComponentList;
        await _repository.AddRangeAsync(cyberComponents);

        // Act
        var result = await _repository.IsNameExist("NonExistentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var cyberComponent = _cyberComponentFixture.CyberComponentDto;
        cyberComponent.Name = "SameName";
        await _dbContext.CyberComponents.AddAsync(cyberComponent);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.IsNameExist("SameName", cyberComponent.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var cyberComponent=_cyberComponentFixture.CyberComponentList;
        var cyberComponent1 = cyberComponent[0];
        var cyberComponent2 = cyberComponent[1];

        cyberComponent2.Name = "DifferentName";

        // Act
        await _dbContext.CyberComponents.AddAsync(cyberComponent1);
        await _dbContext.CyberComponents.AddAsync(cyberComponent2);
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, results.Count);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.CyberComponents.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var cyberComponents = _cyberComponentFixture.CyberComponentList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(cyberComponents);
        var initialCount = cyberComponents.Count;

        var toUpdate = cyberComponents.Take(2).ToList();
        toUpdate.ForEach(x => x.Status = "Updated");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = cyberComponents.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Status == "Updated").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleServerTypeFiltering()
    {
        // Arrange
        var cyberComponents = new List<CyberComponent>
        {
            new CyberComponent
            {
                Name = "Component1",
                ServerType = "Physical",
                Type = "Server",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberComponent
            {
                Name = "Component2",
                ServerType = "Virtual",
                Type = "Server",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberComponent
            {
                Name = "Component3",
                ServerType = "Physical",
                Type = "Database",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(cyberComponents);

        // Act
        var physicalComponents = await _repository.FindByFilterAsync(x => x.ServerType == "Physical");
        var virtualComponents = await _repository.FindByFilterAsync(x => x.ServerType == "Virtual");
        var serverComponents = await _repository.FindByFilterAsync(x => x.Type == "Server");

        // Assert
        Assert.Equal(2, physicalComponents.Count);
        Assert.Single(virtualComponents);
        Assert.Equal(2, serverComponents.Count);
        Assert.All(physicalComponents, x => Assert.Equal("Physical", x.ServerType));
        Assert.All(virtualComponents, x => Assert.Equal("Virtual", x.ServerType));
        Assert.All(serverComponents, x => Assert.Equal("Server", x.Type));
    }

    [Fact]
    public async Task Repository_ShouldHandleNullParametersGracefully()
    {
        // Act & Assert
        var result1 = await _repository.IsNameExist(null, "valid-guid");
        var result2 = await _repository.IsNameExist("TestName", null);
        var result3 = await _repository.GetCyberComponentBySiteId(null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.NotNull(result3);
        Assert.Empty(result3);
    }

    [Fact]
    public async Task Repository_ShouldHandleStatusFiltering()
    {
        // Arrange
        var cyberComponents = new List<CyberComponent>
        {
            new CyberComponent
            {
                Name = "Component1",
                Status = "Active",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberComponent
            {
                Name = "Component2",
                Status = "Inactive",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberComponent
            {
                Name = "Component3",
                Status = "Active",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(cyberComponents);

        // Act
        var activeComponents = await _repository.FindByFilterAsync(x => x.Status == "Active");
        var inactiveComponents = await _repository.FindByFilterAsync(x => x.Status == "Inactive");

        // Assert
        Assert.Equal(2, activeComponents.Count);
        Assert.Single(inactiveComponents);
        Assert.All(activeComponents, x => Assert.Equal("Active", x.Status));
        Assert.All(inactiveComponents, x => Assert.Equal("Inactive", x.Status));
    }

    #endregion
}
