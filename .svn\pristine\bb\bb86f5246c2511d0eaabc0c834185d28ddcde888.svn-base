using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class LicenseHistoryFixture : IDisposable
{
    public List<LicenseHistory> LicenseHistoryPaginationList { get; set; }
    public List<LicenseHistory> LicenseHistoryList { get; set; }
    public LicenseHistory LicenseHistoryDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string LicenseId = "LICENSE_123";

    public ApplicationDbContext DbContext { get; private set; }

    public LicenseHistoryFixture()
    {
        var fixture = new Fixture();

        LicenseHistoryList = fixture.Create<List<LicenseHistory>>();

        LicenseHistoryPaginationList = fixture.CreateMany<LicenseHistory>(20).ToList();

        LicenseHistoryPaginationList.ForEach(x =>
        {
            x.CompanyId = CompanyId;
            x.LicenseId = LicenseId;
            x.IsActive = true;
            x.PONumber = "PO" + fixture.Create<int>();
            x.CPHostName = "Host" + fixture.Create<int>();
            x.Properties = "Properties" + fixture.Create<int>();
            x.IPAddress = "192.168.1." + fixture.Create<int>() % 255;
            x.MACAddress = "00:11:22:33:44:" + (fixture.Create<int>() % 100).ToString("X2");
            x.Validity = "12 Months";
            x.ExpiryDate = DateTime.Now.AddMonths(12).ToString("dd MMMM yyyy");
            x.LicenseKey = "LK" + fixture.Create<string>().Substring(0, 10);
            x.ParentPONumber = "PARENT_PO" + fixture.Create<int>();
            x.UpdaterId = "USER_" + fixture.Create<int>();
            x.IsState = true;
            x.IsParent = fixture.Create<bool>();
        });

        LicenseHistoryList.ForEach(x =>
        {
            x.CompanyId = CompanyId;
            x.LicenseId = LicenseId;
            x.IsActive = true;
            x.PONumber = "PO" + fixture.Create<int>();
            x.CPHostName = "Host" + fixture.Create<int>();
            x.Properties = "Properties" + fixture.Create<int>();
            x.IPAddress = "192.168.1." + fixture.Create<int>() % 255;
            x.MACAddress = "00:11:22:33:44:" + (fixture.Create<int>() % 100).ToString("X2");
            x.Validity = "12 Months";
            x.ExpiryDate = DateTime.Now.AddMonths(12).ToString("dd MMMM yyyy");
            x.LicenseKey = "LK" + fixture.Create<string>().Substring(0, 10);
            x.ParentPONumber = "PARENT_PO" + fixture.Create<int>();
            x.UpdaterId = "USER_" + fixture.Create<int>();
            x.IsState = true;
            x.IsParent = fixture.Create<bool>();
        });

        LicenseHistoryDto = fixture.Create<LicenseHistory>();
        LicenseHistoryDto.CompanyId = CompanyId;
        LicenseHistoryDto.LicenseId = LicenseId;
        LicenseHistoryDto.IsActive = true;
        LicenseHistoryDto.PONumber = "PO123";
        LicenseHistoryDto.CPHostName = "TestHost";
        LicenseHistoryDto.Properties = "TestProperties";
        LicenseHistoryDto.IPAddress = "*************";
        LicenseHistoryDto.MACAddress = "00:11:22:33:44:55";
        LicenseHistoryDto.Validity = "12 Months";
        LicenseHistoryDto.ExpiryDate = DateTime.Now.AddMonths(12).ToString("dd MMMM yyyy");
        LicenseHistoryDto.LicenseKey = "TESTLICENSEKEY";
        LicenseHistoryDto.ParentPONumber = "PARENT_PO123";
        LicenseHistoryDto.UpdaterId = "USER_123";
        LicenseHistoryDto.IsState = true;
        LicenseHistoryDto.IsParent = false;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
