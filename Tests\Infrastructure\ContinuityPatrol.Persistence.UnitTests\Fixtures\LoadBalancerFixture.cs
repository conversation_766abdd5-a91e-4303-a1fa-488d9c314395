using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class LoadBalancerFixture : IDisposable
{
    public List<LoadBalancer> LoadBalancerPaginationList { get; set; }
    public List<LoadBalancer> LoadBalancerList { get; set; }
    public LoadBalancer LoadBalancerDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public LoadBalancerFixture()
    {
        var fixture = new Fixture();

        LoadBalancerList = fixture.Create<List<LoadBalancer>>();

        LoadBalancerPaginationList = fixture.CreateMany<LoadBalancer>(20).ToList();

        LoadBalancerPaginationList.ForEach(x => x.CompanyId = CompanyId);

        LoadBalancerList.ForEach(x => x.CompanyId = CompanyId);

        LoadBalancerDto = fixture.Create<LoadBalancer>();

        LoadBalancerDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
