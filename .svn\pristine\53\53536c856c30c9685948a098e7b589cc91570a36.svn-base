﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = 'ZertoVPG';
let infraObjectId = sessionStorage.getItem("infraobjectId");
setTimeout(() => { ZertoVPGmonitorstatus(mId, monitortype) }, 250)

setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})
async function ZertoVPGmonitorstatus(id, type) {
    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
}
function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}
let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'
function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}
function propertiesData(value) {
    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    else {
        let data = JSON?.parse(value?.properties);
        let customSite = data?.ZertoVPGMonitoring?.length > 1;
        if (customSite) {
            $("#Sitediv").show();
        } else {
            $("#Sitediv").hide();
        }
        $(".siteContainer").empty();
        data?.ZertoVPGMonitoring?.forEach((a, index) => {
            let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        });
        if (data?.ZertoVPGMonitoring?.length > 0) {

            $("#siteName0 .nav-link").addClass("active");
            displaySiteData(data?.ZertoVPGMonitoring[0]);
        }
        let defaultSite = data?.ZertoVPGMonitoring?.find(d => d?.Type === 'DR') || data?.ZertoVPGMonitoring[0];
        if (defaultSite) {

            displaySiteData(defaultSite);
        }
        $(document).on('click', '.siteListChange', function () {

            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0]?.id
            let getSiteName = $(`#${siteId} .siteName`).text()

            let MonitoringModel = data?.ZertoVPGMonitoring?.find(d => d?.Type === getSiteName);
            if (MonitoringModel) {
                displaySiteData(MonitoringModel);
            }
        });
        function displaySiteData(siteData) {

            let obj = {};

            for (let key in siteData?.ReplicationMonitoring) {
                obj[`DR_` + key] = siteData?.ReplicationMonitoring[key];
            }

            let MonitoringModelzertoVPG = [
                "DR_IPAddress", "DR_Type", "DR_SiteName", "DR_VPGName",
                "DR_SiteType", "DR_ProtectionStatus", "DR_VPGState"
            ];

            if (Object.keys(obj)?.length > 0) {
                bindProperties(obj, MonitoringModelzertoVPG, value);
            }
        }

        let dbDetail = data?.ZertoVPGMonitoringPR?.ReplicationMonitoringPR
        const dbDetailsProp = [
            "IPAddress", "Type", "SiteName", "VPGName",
            "SiteType", "ProtectionStatus", "VPGState"
        ];
        bindProperties(dbDetail, dbDetailsProp, value);

        //Datalag
        const datalag = checkAndReplace(data?.Datalag);
        let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0") ? `${(datalag)}` : 'NA';

        var result = "";
        let iconClass = "text-danger cp-disable";

        if (dataLagValue !== "NA") {
            if (dataLagValue.includes(".")) {
                const values = dataLagValue.split(".");
                const hours = values[0] * 24;
                const minutes = values[1]?.split(':')?.slice(0, 2)?.join(':');
                const min = minutes?.split(':');
                const firstValue = parseInt(min[0]) + parseInt(hours);
                result = firstValue + ":" + min[1];
            }
            else if (dataLagValue.includes("+")) {
                const value = dataLagValue.split(" ");
                result = value[1]?.split(':')?.slice(0, 2)?.join(':');
            }
            else {
                result = dataLagValue.split(':')?.slice(0, 2)?.join(':');
            }

            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);

            if (minute > value?.configuredRPO) {
                $('#PR_Datalag').text(result).attr('title', result).css('color', 'red');
            } else {
                $('#PR_Datalag').text(result).attr('title', result).css('color', '');
            }

            iconClass = "text-primary cp-time";
        } else {
            dataLagValue !== 'NA' ? $('#PR_Datalag').text(dataLagValue).attr('title', dataLagValue).css('color', 'red') : $('#PR_Datalag').text(dataLagValue).attr('title', dataLagValue).css('color', '')
        }

        $('#PR_Datalag').prepend(`<i class="${iconClass} me-1 fs-6"></i>`);

    }
}
function setPropData(data, propSets, value) {

    propSets?.forEach(properties => {
        bindProperties(data, properties, value);
    });
}
function bindProperties(data, properties, value) {

    properties?.forEach(property => {
        const values = data[property];
        const displayedValue = value !== undefined ? checkAndReplace(values) : 'NA';
        // Displayed value with icon
        const iconHtml = getIconClass(displayedValue, property, data, value);
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(mergeValue).attr('title', displayedValue);
    });

}
function getIconClass(displayedValue, property, data, value) {

    let prStatus = value?.prServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value?.prServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.prServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";
    let drStatus = value?.drServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value?.drServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";
    let prSite = data?.Type ? "cp-web text-primary" : "text-danger cp-disable"
    let drSite = data?.DR_Type ? "cp-web text-primary" : "text-danger cp-disable"
    let prSiteName = data?.SiteName ? "cp-site-names text-primary" : "text-danger cp-disable"
    let drSiteName = data?.DR_SiteName ? "cp-site-names text-primary" : "text-danger cp-disable"
    let prVpg = data?.VPGName ? "cp-vm_name text-primary" : "text-danger cp-disable"
    let drVpg = data?.DR_VPGName ? "cp-vm_name text-primary" : "text-danger cp-disable"
    let prsiteType = data?.SiteType ? "cp-web text-primary" : "text-danger cp-disable"
    let drsiteType = data?.DR_SiteType ? "cp-web text-primary" : "text-danger cp-disable"
    let prProtection = data?.ProtectionStatus ? "cp-protection-mode text-primary" : "text-danger cp-disable"
    let drProtection = data?.DR_ProtectionStatus ? "cp-protection-mode text-primary" : "text-danger cp-disable"

    const iconMapping = {
        'IPAddress': prStatus,
        'DR_IPAddress': drStatus,
        'Type': prSite,
        'DR_Type': drSite,
        'SiteName': prSiteName,
        'DR_SiteName': drSiteName,
        'VPGName': prVpg,
        'DR_VPGName': drVpg,
        'SiteType': prsiteType,
        'DR_SiteType': drsiteType,
        'ProtectionStatus': prProtection,
        'DR_ProtectionStatus': drProtection,

    }
    let iconClass = iconMapping[property] || '';
    switch (displayedValue.toLowerCase()) {
        case 'na':
            iconClass = 'text-danger cp-disable';
            break;
        case 'not allowed':
        case 'no':
            iconClass = 'text-danger cp-disagree';
            break;
        case 'disabled':
        case 'disable':
            iconClass = 'text-danger cp-disables';
            break;
        case 'enabled':
        case 'enable':
            iconClass = 'text-success cp-enables';
            break;
        case ' streaming ':
            iconClass = 'text-success cp-refresh';
            break;
        case 'running':
        case 'run':
            iconClass = 'text-success cp-reload cp-animate';
            break;
        case 'stopped':
        case 'stop':
            iconClass = 'text-danger cp-Stopped';
            break;
        case 'f':
        case 'false':
        case 'defer':
        case 'deferred':
            iconClass = 'text-danger cp-error';
            break;
        case 't':
        case 'true':
        case 'yes':
            iconClass = 'text-success  cp-agree';
            break;
        case 'valid':
            iconClass = 'text-success cp-success';
            break;
        case 'pending':
            iconClass = 'text-warning cp-pending';
            break;
        case 'pause':
        case 'paused':
            iconClass = 'text-warning cp-circle-pause';
            break;
        case 'manual':
            iconClass = 'text-warning cp-settings';
            break;
        case 'synchronous_commit':
        case 'synchronized':
        case 'synchronizing':
        case 'sync':
            iconClass = 'text-success cp-refresh';
            break;
        case 'asynchronous_commit':
        case 'asynchronizing':
        case 'asynchronized':
        case 'async':
            iconClass = 'text-danger cp-refresh';
            break;
        case 'online':
            iconClass = 'text-success cp-online';
            break;
        case 'offline':
            iconClass = 'text-danger cp-offline';
            break;
        case 'enabled':
        case 'connected':
        case 'connect':
            iconClass = 'text-success cp-connected';
            break;
        case 'disconnected':
        case 'disconnect':
            iconClass = 'text-danger cp-disconnecteds';
            break;
        case 'standby':
        case 'to standby':
        case 'mounted':
            iconClass = 'text-warning cp-control-file-type';
            break;
        case 'required':
        case 'require':
            iconClass = 'text-warning cp-warning';
            break;
        case 'healthy':
            iconClass = 'text-success cp-health-success';
            break;
        case 'nothealthy':
        case 'not_healthy':
        case 'unhealthy':
            iconClass = 'text-danger cp-health-error';
            break;
        case 'error':
            iconClass = 'text-danger cp-fail-back';
            break;
        case 'on':
            iconClass = 'text-success cp-end';
            break;
        case 'off':
            iconClass = 'text-danger cp-end';
            break;
        case 'current':
        case 'read write':
            iconClass = 'text-success cp-file-edits';
            break;
        case 'primary':
            iconClass = 'text-primary cp-list-prsite';
            break;
        case 'secondary':
            iconClass = 'text-info cp-dr';
            break;
        case 'physical standby':
            iconClass = 'text-info cp-physical-drsite';
            break;

        default:
            if (displayedValue?.includes('running')) {
                iconClass = 'text-success cp-reload cp-animate';
            } else if (displayedValue?.includes('production') || displayedValue?.includes('archive recovery')) {
                iconClass = 'text-warning cp-log-archive-config';
            }
            break;
    }
    return iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
}
