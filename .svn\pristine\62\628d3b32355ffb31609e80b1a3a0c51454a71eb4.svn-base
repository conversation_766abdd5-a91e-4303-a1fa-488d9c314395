﻿using ContinuityPatrol.Domain.ViewModels;
using ContinuityPatrol.Domain.ViewResults;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Shared.Tests.Mocks;

public class LoginServiceMocks
{
    public static Mock<ILoginService> PrepareLoginView(ApplicationStatus status)
    {
        var mock = new Mock<ILoginService>();

        var result = new PreLoginViewResult()
        {
            ApplicationStatus = status
        };

        mock.Setup(x => x.PrepareLoginViewAsync())
            .Returns(Task.FromResult(result));

        return mock;
    }
    public static Mock<ILoginService> Authenticate(LogInStatus status, bool isAdAuthentication)
    {
        var mock = new Mock<ILoginService>();

        var postLoginViewResult = new PostLoginViewResult()
        {
            LogInStatus = status
        };

        if (isAdAuthentication)
        {
            mock.Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
                .Returns(Task.FromResult(postLoginViewResult));
        }
        else
        {
            mock.Setup(x => x.Authenticate(It.IsAny<LoginViewModel>()))
                .Returns(Task.FromResult(postLoginViewResult));
        }

        return mock;
    }
}