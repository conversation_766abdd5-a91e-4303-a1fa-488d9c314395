using ContinuityPatrol.Application.Features.DriftEvent.Commands.Create;
using ContinuityPatrol.Application.Features.DriftEvent.Commands.Update;
using ContinuityPatrol.Application.Features.DriftEvent.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftEvent.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftEventModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IDriftEventService
{
    Task<List<DriftEventListVm>> GetDriftEventList();
    Task<BaseResponse> CreateAsync(CreateDriftEventCommand createDriftEventCommand);
    Task<BaseResponse> UpdateAsync(UpdateDriftEventCommand updateDriftEventCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<DriftEventDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsDriftEventNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<DriftEventListVm>> GetPaginatedDriftEvents(GetDriftEventPaginatedListQuery query);
    #endregion
}
