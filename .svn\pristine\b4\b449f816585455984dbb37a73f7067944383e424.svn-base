using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Delete;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class BulkImportActionResultFixture : IDisposable
{
    public List<BulkImportActionResult> BulkImportActionResults { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateBulkImportActionResultCommand CreateBulkImportActionResultCommand { get; set; }
    public UpdateBulkImportActionResultCommand UpdateBulkImportActionResultCommand { get; set; }
    public DeleteBulkImportActionResultCommand DeleteBulkImportActionResultCommand { get; set; }
    public IMapper Mapper { get; set; }

    public BulkImportActionResultFixture()
    {
        BulkImportActionResults = new List<BulkImportActionResult>
        {
            new BulkImportActionResult
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                NodeId = "Node001",
                NodeName = "TestNode",
                BulkImportOperationId = Guid.NewGuid().ToString(),
                BulkImportOperationGroupId = Guid.NewGuid().ToString(),
                EntityId = Guid.NewGuid().ToString(),
                EntityName = "TestEntity",
                EntityType = "Server",
                Status = "Pending",
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddHours(1),
                ErrorMessage = "",
                ConditionalOperation = 1,
                IsActive = true
            }
        };

        BulkImportActionResults = AutoBulkImportActionResultFixture.Create<List<BulkImportActionResult>>();
        UserActivities = AutoBulkImportActionResultFixture.Create<List<UserActivity>>();
        CreateBulkImportActionResultCommand = AutoBulkImportActionResultFixture.Create<CreateBulkImportActionResultCommand>();
        UpdateBulkImportActionResultCommand = AutoBulkImportActionResultFixture.Create<UpdateBulkImportActionResultCommand>();
        DeleteBulkImportActionResultCommand = AutoBulkImportActionResultFixture.Create<DeleteBulkImportActionResultCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<BulkImportActionResultProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoBulkImportActionResultFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateBulkImportActionResultCommand>(p => p.EntityName, 20));
            fixture.Customize<CreateBulkImportActionResultCommand>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString()));
            fixture.Customize<CreateBulkImportActionResultCommand>(c => c.With(b => b.NodeId, "Node001"));
            fixture.Customize<CreateBulkImportActionResultCommand>(c => c.With(b => b.NodeName, "TestNode"));
            fixture.Customize<CreateBulkImportActionResultCommand>(c => c.With(b => b.BulkImportOperationId, Guid.NewGuid().ToString()));
            fixture.Customize<CreateBulkImportActionResultCommand>(c => c.With(b => b.BulkImportOperationGroupId, Guid.NewGuid().ToString()));
            fixture.Customize<CreateBulkImportActionResultCommand>(c => c.With(b => b.EntityId, Guid.NewGuid().ToString()));
            fixture.Customize<CreateBulkImportActionResultCommand>(c => c.With(b => b.EntityType, "Server"));
            fixture.Customize<CreateBulkImportActionResultCommand>(c => c.With(b => b.Status, "Pending"));
            fixture.Customize<CreateBulkImportActionResultCommand>(c => c.With(b => b.StartTime, DateTime.Now));
            fixture.Customize<CreateBulkImportActionResultCommand>(c => c.With(b => b.EndTime, DateTime.Now.AddHours(1)));
            fixture.Customize<CreateBulkImportActionResultCommand>(c => c.With(b => b.ErrorMessage, ""));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateBulkImportActionResultCommand>(p => p.EntityName, 20));
            fixture.Customize<UpdateBulkImportActionResultCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateBulkImportActionResultCommand>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateBulkImportActionResultCommand>(c => c.With(b => b.NodeId, "Node001"));
            fixture.Customize<UpdateBulkImportActionResultCommand>(c => c.With(b => b.NodeName, "TestNode"));
            fixture.Customize<UpdateBulkImportActionResultCommand>(c => c.With(b => b.BulkImportOperationId, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateBulkImportActionResultCommand>(c => c.With(b => b.BulkImportOperationGroupId, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateBulkImportActionResultCommand>(c => c.With(b => b.EntityId, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateBulkImportActionResultCommand>(c => c.With(b => b.EntityType, "Server"));
            fixture.Customize<UpdateBulkImportActionResultCommand>(c => c.With(b => b.Status, "Pending"));
            fixture.Customize<UpdateBulkImportActionResultCommand>(c => c.With(b => b.StartTime, DateTime.Now));
            fixture.Customize<UpdateBulkImportActionResultCommand>(c => c.With(b => b.EndTime, DateTime.Now.AddHours(1)));
            fixture.Customize<UpdateBulkImportActionResultCommand>(c => c.With(b => b.ErrorMessage, ""));
            fixture.Customize<UpdateBulkImportActionResultCommand>(c => c.With(b => b.ConditionalOperation, 1));

            fixture.Customize<DeleteBulkImportActionResultCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.IsActive, true));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.NodeId, "Node001"));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.NodeName, "TestNode"));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.BulkImportOperationId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.BulkImportOperationGroupId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.EntityId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.EntityType, "Server"));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.Status, "Pending"));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.StartTime, DateTime.Now));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.EndTime, DateTime.Now.AddHours(1)));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.ErrorMessage, ""));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.ConditionalOperation, 1));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
