﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FormType.Events.Delete;

public class FormTypeDeletedEventHandler : INotificationHandler<FormTypeDeletedEvent>
{
    private readonly ILogger<FormTypeDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FormTypeDeletedEventHandler(ILoggedInUserService userService, ILogger<FormTypeDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(FormTypeDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.FormType}",
            Entity = Modules.FormType.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"FormType '{deletedEvent.FormTypeName}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation($"FormType '{deletedEvent.FormTypeName}' deleted successfully.");
    }
}