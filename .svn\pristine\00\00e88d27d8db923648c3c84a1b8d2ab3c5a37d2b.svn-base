using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DataSyncJobRepositoryTests : IClassFixture<DataSyncJobFixture>
{
    private readonly DataSyncJobFixture _dataSyncJobFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DataSyncJobRepository _repository;

    public DataSyncJobRepositoryTests(DataSyncJobFixture dataSyncJobFixture)
    {
        _dataSyncJobFixture = dataSyncJobFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DataSyncJobRepository(DbContextFactory.GetMockUserService(), _dbContext);
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var dataSyncJob = _dataSyncJobFixture.DataSyncJobDto;

        // Act
        await _dbContext.DataSyncJobs.AddAsync(dataSyncJob);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(dataSyncJob.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSyncJob.ReplicationName, result.ReplicationName);
        Assert.Equal(dataSyncJob.ReplicationId, result.ReplicationId);
        Assert.Single(_dbContext.DataSyncJobs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var dataSyncJob = _dataSyncJobFixture.DataSyncJobDto;
        await _dbContext.DataSyncJobs.AddAsync(dataSyncJob);
        await _dbContext.SaveChangesAsync();

        dataSyncJob.ReplicationName = "UpdatedReplicationName";

        // Act
        _dbContext.DataSyncJobs.Update(dataSyncJob);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(dataSyncJob.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedReplicationName", result.ReplicationName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var dataSyncJob = _dataSyncJobFixture.DataSyncJobDto;
        await _dbContext.DataSyncJobs.AddAsync(dataSyncJob);
        await _dbContext.SaveChangesAsync();

        // Act
        dataSyncJob.IsActive = false;

        _dbContext.DataSyncJobs.Update(dataSyncJob);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dataSyncJob = _dataSyncJobFixture.DataSyncJobDto;
        var addedEntity = await _repository.AddAsync(dataSyncJob);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.ReplicationName, result.ReplicationName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dataSyncJob = _dataSyncJobFixture.DataSyncJobDto;
        await _repository.AddAsync(dataSyncJob);

        // Act
        var result = await _repository.GetByReferenceIdAsync(dataSyncJob.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSyncJob.ReferenceId, result.ReferenceId);
        Assert.Equal(dataSyncJob.ReplicationName, result.ReplicationName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var dataSyncJobs = _dataSyncJobFixture.DataSyncJobList;
        await _repository.AddRangeAsync(dataSyncJobs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSyncJobs.Count, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldNotReturnInactiveEntities()
    {
        // Arrange
        var dataSyncJobs = _dataSyncJobFixture.DataSyncJobList;
        dataSyncJobs.First().IsActive = false; // Make one inactive
        await _repository.AddRangeAsync(dataSyncJobs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSyncJobs.Count - 1, result.Count); // Should exclude the inactive one
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var dataSyncJobs = _dataSyncJobFixture.DataSyncJobList;

        // Act
        var result = await _repository.AddRangeAsync(dataSyncJobs);

        // Assert
        Assert.Equal(dataSyncJobs.Count, result.Count());
        Assert.Equal(dataSyncJobs.Count, _dbContext.DataSyncJobs.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var dataSyncJobs = _dataSyncJobFixture.DataSyncJobList;
        await _repository.AddRangeAsync(dataSyncJobs);

        // Act
        var result = await _repository.RemoveRangeAsync(dataSyncJobs);

        // Assert
        Assert.Equal(dataSyncJobs.Count, result.Count());
        Assert.Empty(_dbContext.DataSyncJobs);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var dataSyncJobs = _dataSyncJobFixture.DataSyncJobList.Take(5).ToList();
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(dataSyncJobs);
        var initialCount = dataSyncJobs.Count;
        
        var toUpdate = dataSyncJobs.Take(2).ToList();
        toUpdate.ForEach(x => x.ReplicationName = "UpdatedReplicationName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = dataSyncJobs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.ReplicationName == "UpdatedReplicationName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion

    #region Entity Specific Tests

    [Fact]
    public async Task DataSyncJob_ShouldMaintainReplicationProperties()
    {
        // Arrange
        var dataSyncJob = _dataSyncJobFixture.DataSyncJobDto;
        dataSyncJob.ReplicationId = "REPL_001";
        dataSyncJob.ReplicationName = "Test Replication";
        dataSyncJob.DataSyncOptionId = "OPTION_001";
        dataSyncJob.ReplicationType = "Sync";
        dataSyncJob.SiteId = "SITE_001";
        dataSyncJob.SiteName = "Test Site";
        dataSyncJob.SourceDirectory = "/source/path";
        dataSyncJob.DestinationDirectory = "/dest/path";
        dataSyncJob.ModeType = "Full";

        // Act
        var result = await _repository.AddAsync(dataSyncJob);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("REPL_001", result.ReplicationId);
        Assert.Equal("Test Replication", result.ReplicationName);
        Assert.Equal("OPTION_001", result.DataSyncOptionId);
        Assert.Equal("Sync", result.ReplicationType);
        Assert.Equal("SITE_001", result.SiteId);
        Assert.Equal("Test Site", result.SiteName);
        Assert.Equal("/source/path", result.SourceDirectory);
        Assert.Equal("/dest/path", result.DestinationDirectory);
        Assert.Equal("Full", result.ModeType);
    }

    #endregion
}
