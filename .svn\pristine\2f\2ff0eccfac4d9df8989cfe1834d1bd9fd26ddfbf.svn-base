﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class LicenseHistoryRepository : BaseRepository<LicenseHistory>, ILicenseHistoryRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public LicenseHistoryRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<LicenseHistory>> GetAllLicenseHistoryAsync()
    {
        var licenseHistoryList = _loggedInUserService.IsParent
            ? await _dbContext.LicenseHistories.Where(x => x.IsActive).ToListAsync()
            : await _dbContext.LicenseHistories
                .Where(x => x.IsActive && x.CompanyId.Equals(_loggedInUserService.CompanyId)).ToListAsync();
        return licenseHistoryList.Select(x => new LicenseHistory
        {
            ReferenceId = x.ReferenceId,
            LicenseId = x.LicenseId,
            CompanyId = x.CompanyId,
            PONumber = SecurityHelper.Decrypt(x.PONumber),
            CPHostName = SecurityHelper.Decrypt(x.CPHostName),
            Properties = SecurityHelper.Decrypt(x.Properties),
            IPAddress = SecurityHelper.Decrypt(x.IPAddress),
            MACAddress = SecurityHelper.Decrypt(x.MACAddress),
            Validity = SecurityHelper.Decrypt(x.Validity),
            ExpiryDate = SecurityHelper.Decrypt(x.ExpiryDate),
            LicenseKey = x.LicenseKey,
            UpdaterId = x.UpdaterId,
            ParentPONumber = SecurityHelper.Decrypt(x.ParentPONumber),
            IsState = x.IsState
        }).OrderByDescending(x => x.ReferenceId).ToList();
    }

    public async Task<LicenseHistory> GetLicenseHistoryByLicenseId(string licenseId)
    {
        var detail = _loggedInUserService.IsParent
            ? await Entities.Where(x => x.LicenseId.Equals(licenseId)).FirstOrDefaultAsync()
            : await Entities
                .Where(x => x.LicenseId.Equals(licenseId) && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .FirstOrDefaultAsync();

        return new LicenseHistory
        {
            ReferenceId = detail.ReferenceId,
            LicenseId = detail.LicenseId,
            PONumber = SecurityHelper.Decrypt(detail.PONumber),
            CompanyId = detail.CompanyId,
            CPHostName = SecurityHelper.Decrypt(detail.CPHostName),
            Properties = SecurityHelper.Decrypt(detail.Properties),
            IPAddress = SecurityHelper.Decrypt(detail.IPAddress),
            MACAddress = SecurityHelper.Decrypt(detail.MACAddress),
            LicenseKey = detail.LicenseKey,
            Validity = SecurityHelper.Decrypt(detail.Validity),
            ExpiryDate = SecurityHelper.Decrypt(detail.ExpiryDate),
            UpdaterId = detail.UpdaterId,
            ParentPONumber = SecurityHelper.Decrypt(detail.ParentPONumber),
            IsState = detail.IsState
        };
    }
}