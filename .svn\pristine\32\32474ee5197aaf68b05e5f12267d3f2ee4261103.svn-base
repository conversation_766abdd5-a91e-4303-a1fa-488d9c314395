﻿using ContinuityPatrol.Application.Features.PluginManagerHistory.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.PluginManagerHistory.Commands;

public class CreatePluginManagerHistoryTests : IClassFixture<PluginManagerHistoryFixture>
{
    private readonly PluginManagerHistoryFixture _pluginManagerHistoryFixture;

    private readonly Mock<IPluginManagerHistoryRepository> _mockPluginManagerHistoryRepository;

    private readonly CreatePluginManagerHistoryCommandHandler _handler;

    public CreatePluginManagerHistoryTests(PluginManagerHistoryFixture pluginManagerHistoryFixture)
    {
        _pluginManagerHistoryFixture = pluginManagerHistoryFixture;

        var mockPublisher = new Mock<IPublisher>();
        
        _mockPluginManagerHistoryRepository = PluginManagerHistoryRepositoryMocks.CreatePluginManagerHistoryRepository(_pluginManagerHistoryFixture.PluginManagerHistories);

        _handler = new CreatePluginManagerHistoryCommandHandler(_pluginManagerHistoryFixture.Mapper, _mockPluginManagerHistoryRepository.Object, mockPublisher.Object);

    }

    [Fact]
    public async Task Handle_Should_Increase_PluginManagerHistoryCount_When_AddValidPluginManagerHistory()
    {
        await _handler.Handle(_pluginManagerHistoryFixture.CreatePluginManagerHistoryCommand, CancellationToken.None);

        var allCategories = await _mockPluginManagerHistoryRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_pluginManagerHistoryFixture.PluginManagerHistories.Count);
    }

    [Fact]
    public async Task Handle_Return_Successful_PluginManagerHistoryResponse_When_AddValidPluginManagerHistory()
    {
        var result = await _handler.Handle(_pluginManagerHistoryFixture.CreatePluginManagerHistoryCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreatePluginManagerHistoryResponse));

        result.PluginManagerHistoryId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_pluginManagerHistoryFixture.CreatePluginManagerHistoryCommand, CancellationToken.None);

        _mockPluginManagerHistoryRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.PluginManagerHistory>()), Times.Once);
    }
}