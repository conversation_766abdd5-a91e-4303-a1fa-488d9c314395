﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.Features.BusinessService.Events.DashboardViewEvent.Update;

public class
    BusinessServiceDashboardViewUpdatedEventHandler : INotificationHandler<BusinessServiceDashboardViewUpdatedEvent>
{
    private readonly IDashboardViewRepository _dashboardViewRepository;

    private readonly ILogger<BusinessServiceDashboardViewUpdatedEventHandler> _logger;

    public BusinessServiceDashboardViewUpdatedEventHandler(IDashboardViewRepository dashboardViewRepository,
        ILogger<BusinessServiceDashboardViewUpdatedEventHandler> logger)
    {
        _dashboardViewRepository = dashboardViewRepository;
        _logger = logger;
    }

    public async Task Handle(BusinessServiceDashboardViewUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var dashboardViewList =
            await _dashboardViewRepository.GetBusinessViewListByBusinessServiceId(updatedEvent.BusinessServiceId);

        dashboardViewList.ForEach(dashboardView =>
        {
            dashboardView.BusinessServiceId = updatedEvent.BusinessServiceId;
            dashboardView.BusinessServiceName = updatedEvent.BusinessServiceName;
            dashboardView.SiteProperties = updatedEvent.SiteProperties;
            dashboardView.Priority = updatedEvent.Priority;            
        });
        
        await _dashboardViewRepository.UpdateRangeAsync(dashboardViewList);

        _logger.LogInformation(
            $"OperationalService :: DashboardViewUpdateEvent '{updatedEvent.BusinessServiceName}' updated successfully.");
    }
}