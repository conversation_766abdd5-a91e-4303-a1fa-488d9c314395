<svg width="84" height="84" viewBox="0 0 84 84" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_100_515)">
<circle cx="42.0156" cy="41.8799" r="30" fill="white"/>
</g>
<g clip-path="url(#clip0_100_515)">
<path d="M42.0183 49.1326V51.5501M42.0183 38.0346V41.503M45.7405 45.2361H54.4898C55.3799 45.2361 56.1015 45.9577 56.1015 46.8478V49.4587M38.34 45.1033H30.2815C29.3913 45.1033 28.6698 45.8249 28.6698 46.715V49.1326M47.3601 32.511C47.2387 32.8145 47.178 33.0573 47.0566 33.3001C46.9959 33.4822 46.9959 33.6643 47.0566 33.7857L47.6636 34.9997C47.785 35.2425 47.7243 35.4853 47.5422 35.6067L46.9352 36.2137C46.7531 36.3958 46.5103 36.3958 46.3282 36.3351L45.1142 35.7281C44.9321 35.6674 44.75 35.6674 44.6286 35.7281C44.3858 35.8495 44.0823 35.9709 43.8395 36.0316C43.6574 36.0923 43.536 36.2137 43.4753 36.3958L42.9897 37.6705C42.929 37.9133 42.6862 38.0347 42.4434 38.0347H41.5936C41.3508 38.0347 41.1687 37.9133 41.0473 37.6705L40.5617 36.3958C40.501 36.2137 40.3796 36.0923 40.1975 36.0316C39.894 35.9709 39.6512 35.8495 39.4084 35.7281C39.2263 35.6674 39.0442 35.6674 38.9228 35.7281L37.7088 36.3351C37.4659 36.4565 37.2231 36.3958 37.1017 36.2137L36.4947 35.6067C36.3126 35.4246 36.3126 35.1818 36.3733 34.9997L36.9803 33.7857C37.041 33.6036 37.041 33.4215 36.9803 33.3001C36.8589 33.0573 36.7375 32.7538 36.6768 32.511C36.6161 32.3289 36.4947 32.2075 36.3126 32.1468L35.0379 31.6612C34.7951 31.6005 34.6737 31.3577 34.6737 31.1149V30.2651C34.6737 30.0223 34.7951 29.8402 35.0379 29.7188L36.3126 29.2332C36.4947 29.1725 36.6161 29.0511 36.6768 28.869C36.7982 28.5655 36.8589 28.3227 36.9803 28.0798C37.041 27.8977 37.041 27.7156 36.9803 27.5942L36.3733 26.3802C36.2519 26.1374 36.3126 25.8946 36.4947 25.7732L37.1017 25.1662C37.2838 24.9841 37.5266 24.9841 37.7088 25.0448L38.9228 25.6518C39.1049 25.7125 39.287 25.7125 39.4084 25.6518C39.6512 25.5304 39.9547 25.409 40.1975 25.3483C40.3796 25.2876 40.501 25.1662 40.5617 24.9841L41.0473 23.7094C41.108 23.4666 41.3508 23.3452 41.5936 23.3452H42.4434C42.6862 23.3452 42.8683 23.4666 42.9897 23.7094L43.4753 24.9841C43.536 25.1662 43.6574 25.2876 43.8395 25.3483C44.143 25.409 44.3858 25.5304 44.6286 25.6518C44.8107 25.7125 44.9928 25.7125 45.1142 25.6518L46.3282 25.0448C46.571 24.9234 46.8138 24.9841 46.9352 25.1662L47.5422 25.7732C47.7243 25.9553 47.7243 26.1981 47.6636 26.3802L47.0566 27.5942C46.9959 27.7763 46.9959 27.9584 47.0566 28.0798C47.178 28.3227 47.2994 28.6262 47.3601 28.869C47.4208 29.0511 47.5422 29.1725 47.7243 29.2332L48.999 29.7188C49.2418 29.7795 49.3632 30.0223 49.3632 30.2651V31.1149C49.3632 31.3577 49.2418 31.5398 48.999 31.6612L47.7243 32.1468C47.5422 32.2075 47.4208 32.3289 47.3601 32.511ZM42.1397 33.7229C40.5564 33.7229 39.2285 32.395 39.2285 30.8117C39.2285 29.2285 40.5564 27.9006 42.1397 27.9006C43.7229 27.9006 45.0508 29.2285 45.0508 30.8117C45.0508 32.395 43.7229 33.7229 42.1397 33.7229ZM46.2411 56.1918C46.2411 58.5239 44.3506 60.4145 42.0185 60.4145C39.6864 60.4145 37.7958 58.5239 37.7958 56.1918C37.7958 53.8597 39.6864 51.9692 42.0185 51.9692C44.3506 51.9692 46.2411 53.8597 46.2411 56.1918ZM42.6907 49.0706L45.8762 45.8852C46.1909 45.5705 46.1909 45.0603 45.8762 44.7455L42.6907 41.5601C42.376 41.2454 41.8658 41.2454 41.5511 41.5601L38.3657 44.7455C38.0509 45.0603 38.0509 45.5705 38.3657 45.8852L41.5511 49.0706C41.8658 49.3853 42.376 49.3853 42.6907 49.0706ZM24.1418 56.747H33.5226C33.9677 56.747 34.3285 56.3862 34.3285 55.9412V49.9384C34.3285 49.4934 33.9677 49.1326 33.5226 49.1326H24.1418C23.6967 49.1326 23.3359 49.4934 23.3359 49.9384V55.9412C23.3359 56.3862 23.6967 56.747 24.1418 56.747ZM50.5086 56.747H59.8895C60.3345 56.747 60.6953 56.3862 60.6953 55.9412V49.9384C60.6953 49.4934 60.3345 49.1326 59.8895 49.1326H50.5086C50.0636 49.1326 49.7028 49.4934 49.7028 49.9384V55.9412C49.7028 56.3862 50.0636 56.747 50.5086 56.747Z" stroke="#1A1A1A" stroke-width="1.5"/>
</g>
<defs>
<filter id="filter0_d_100_515" x="0.808049" y="0.672307" width="82.4152" height="82.4152" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5.60379"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_100_515"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_100_515" result="shape"/>
</filter>
<clipPath id="clip0_100_515">
<rect width="40" height="40" fill="white" transform="translate(22.0156 21.8799)"/>
</clipPath>
</defs>
</svg>
