﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = 'MongoDB';
let infraObjectId = sessionStorage.getItem("infraobjectId");
setTimeout(() => { mongodbmonitorstatus(mId, monitortype) }, 250)
setTimeout(() => { mongoDBServer(infraObjectId) }, 250)

let DR_ServerStatus, PR_ServerStatus;

setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
$('#mssqlserver').hide();
async function mongoDBServer(id) {

    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
    let data = {}
    data.infraObjectId = id;
    let mssqlServerData = await getAysncWithHandler(url, data);

    if (mssqlServerData != null && mssqlServerData?.length > 0) {
        mssqlServerData?.forEach(data => {
            let value = data?.isServiceUpdate
            let parsed = []
            if (value && value !== 'NA') parsed = JSON?.parse(value)
            if (Array,isArray(parsed)) {
                parsed?.forEach(s => {
                    if (s?.Services?.length) {
                        $('#mssqlserver').show();
                        bindmongoDBServer(mssqlServerData)
                    }
                })
            }
        })

    } else {
        $('#mssqlserver').hide();
    }

}
function bindmongoDBServer(mssqlServerData) {

    let prType = { IpAddress: '--', Services: [] };
    let drType = { IpAddress: '--', Services: [] };

    // Loop through each item to find PR and DR entries
    mssqlServerData?.forEach(item => {
        let parsedServices = [];
        try {
            const value = item?.isServiceUpdate
            if (value && value !== 'NA') {
                parsedServices = JSON.parse(item?.isServiceUpdate)
            }
        } catch (e) {
            console.error("Invalid JSON in isServiceUpdate:", item?.isServiceUpdate);
        }

        parsedServices?.forEach(serviceGroup => {
            if (serviceGroup?.Type === 'PR') {
                prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
                prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];
            } else if (serviceGroup?.Type === 'DR') {
                drType.IpAddress = serviceGroup?.IpAddress || drType?.IpAddress;
                drType.Services = [...drType.Services, ...(serviceGroup?.Services || [])];
            }
        });
    });

    // Set header IPs
    $('#prIp').text('Primary (' + prType?.IpAddress + ')');
    $('#drIp').text('DR (' + drType?.IpAddress + ')');

    const prStatusSummary = getStatusSummary(prType.Services.map(s => s.Status).filter(Boolean));
    const drStatusSummary = getStatusSummary(drType.Services.map(s => s.Status).filter(Boolean));
    $('#prIp').html(`Primary (${prType.IpAddress}) <br ><span class="status-summary text-muted small">${prStatusSummary}</span>`);
    $('#drIp').html(`DR (${drType.IpAddress}) <br ><span class="status-summary text-muted small">${drStatusSummary}</span>`);

    // Unique list of all service names from both PR and DR
    let allServiceNames = [...new Set([
        ...prType?.Services?.map(s => s?.ServiceName),
        ...drType?.Services?.map(s => s?.ServiceName)
    ])];

    // Build table rows
    let tbody = $('#mssqlserverbody');
    tbody.empty();

    allServiceNames?.forEach(serviceName => {
        let prService = prType?.Services?.find(s => s?.ServiceName === serviceName);
        let drService = drType?.Services?.find(s => s?.ServiceName === serviceName);

        let prStatus = prService ? prService?.Status : '--';
        let drStatus = drService ? drService?.Status : '--';
        let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
        let drIcon = drStatus !== '--' ? `<i class="${getStatusIconClass(drStatus)} me-2 fs-6"></i>` : '';
        const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";

        let row = `
            <tr>
                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
                <td>${prIcon}${prStatus}</td>
                <td>${drIcon}${drStatus}</td>
            </tr>
        `;
        tbody.append(row);
    });
}
function getStatusSummary(arr) {
    let countMap = {};
    arr?.forEach(status => {
        countMap[status] = (countMap[status] || 0) + 1;
    });
    let total = arr?.length;
    let statusSummary = Object.entries(countMap)
        .map(([status, count]) => `${count} ${status}`)
        .join(', ');
    return statusSummary ? `${statusSummary} out of ${total} services` : '--';
}
function getStatusIconClass(status) {
    if (!status) return "text-danger cp-disable";

    const lowerStatus = status.toLowerCase();
    if (lowerStatus === "running") {
        return "text-success cp-reload cp-animate";
    } else if (lowerStatus === "error" || lowerStatus === "stopped") {
        return "text-danger cp-fail-back";
    } else {
        return "text-danger cp-disable";
    }
}
function mongodbmonitorstatus(id, type) {
    $.ajax({
        url: "/Monitor/MongoDB/GetMonitorServiceStatusByIdAndType",
        method: 'GET',
        data: {
            monitorId: id,
            type: type,
        },
        dataType: 'json',
        async: true,
        success: function (data) {
            //console.log(data);
            infraDataa(data);
            propertiesDataa(data);
            let dataValue = JSON?.parse(data?.properties);
            // db2hadrSolutionDiagram(dataValue, data?.type);
        },
        error: function (error) {
            console.error('Error:', error);
        }
    });
}
$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'
let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">'
let noData = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'

function infraDataa(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}

function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}


function propertiesDataa(value) {
    
    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    else {
        let data = JSON?.parse(value?.properties);
        let customSite = data?.MongoDBMonitoringModel?.length > 1;
        if (customSite) {
            $("#Sitediv").show();
        } else {
            $("#Sitediv").hide();
        }


        $(".siteContainer").empty();


        data?.MongoDBMonitoringModel?.forEach((a, index) => {
            let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        });


        if (data?.MongoDBMonitoringModel?.length > 0) {
            $("#siteName0 .nav-link").addClass("active");
            displaySiteData(data?.MongoDBMonitoringModel[0]);
        }



        let defaultSite = data?.MongoDBMonitoringModel?.find(d => d?.Type === 'DR') || data?.MongoDBMonitoringModel[0];
        if (defaultSite) {
            displaySiteData(defaultSite);
        }

        $(document).on('click', '.siteListChange', function () {
            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0].id
            let getSiteName = $(`#${siteId} .siteName`).text()

            let MonitoringModel = data?.MongoDBMonitoringModel?.find(d => d?.Type === getSiteName);
            if (MonitoringModel) {
                displaySiteData(MonitoringModel);
            }
        });
        function displaySiteData(siteData) {
            let obj = {};
            $('.dynamicSite-header').text(siteData?.Type).attr('title', siteData?.Type);

            for (let key in siteData?.MongoDBMonitoring) {
                obj[key] = siteData?.MongoDBMonitoring[key];
            }

            let MonitoringModelPropMongodb = [
                "DBName", "MongodbStatus",
                "DBVersion", "SizesOnDisk", "Collections", "Documents", "Indexes",
                "HostName", "CurrentPriority", "ReplicaSetName", "StateDescription",
                "Health", "LastHeartbeatMessage", "MemberID","Datalag",
            ];

            if (Object.keys(obj)?.length > 0) {
                bindProperties(obj, MonitoringModelPropMongodb, value);
            }
            let obj1 = {};
        
            for (let key in siteData) {
                obj1[key] = siteData[key];
            }

            let MonitoringModelPropMongodbs = [
                "Server_IpAddress", "Server_HostName",
                "Database", "Server_Edition", "IsDRWindows", "LSN_Logs",
            ];

            if (Object.keys(obj1).length > 0) {
                bindProperties(obj1, MonitoringModelPropMongodbs, value);
            }
        }
        //console.log(data)
        //Database (DB2) Monitor

        // mongodbSolutionDiagram(data);
        let mondodbmonitor = data?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring
         
        const mongodbmonitorProp = ["PRDBName",  "PRMongodbStatus","PRDBVersion",
            "SizesOnDiskPR",  "CollectionsPR", "DocumentsPR", "IndexesPR", 
            "HostnamePR", "PRCurrentPriority", "PRreplicaSetName",  "PRStateDescription",
            "PRhealth", "PRlastHeartbeatMessage","PRmemberID", 
        ];
     
        if (mondodbmonitor !== '' && mondodbmonitor !== null && mondodbmonitor !== undefined) {
            bindProperties(mondodbmonitor, mongodbmonitorProp,value);
        } else {
            $("#MongoDB+").css('text-align', 'center').html(noDataImage + "<br><span class='text-danger'>MongoDB Monitoring Details is not available</span>");
        }

        //Replication Monitoring
        let mongorepli = data?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel

        const mongoreplicaMonitor = [
            "PR_Database", "PR_Server_Edition", "PR_Server_IpAddress", "PR_Server_HostName",  "IsPRWindows", "PR_LSN_Logs"];

        if (mongorepli !== '' && mongorepli !== null && mongorepli !== undefined) {
            bindProperties(mongorepli, mongoreplicaMonitor,value);
        } else {
            $("#mongorepli").css('text-align', 'center').html(noDataImage + "<br><span class='text-danger'>DB2 Replication Monitoring Details is not available</span>");
        }
        const datalag = checkAndReplace(data?.PR_Datalag);
        
        let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0") ? `${(datalag)}` : 'NA';

        var result = "";

        if (dataLagValue?.includes(".")) {
            var value = dataLagValue?.split(".");
            var hours = value[0] * 24;
            var minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
            var min = minutes?.split(':');
            var firstValue = parseInt(min[0]) + parseInt(hours);
            result = firstValue + ":" + min[1];
            const minute = (parseInt(result[0]) * 60) + parseInt(result[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        }
        else if (dataLagValue?.includes("+")) {
            const value = dataLagValue.split(" ");
            result = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')

        }
        else {
            result = dataLagValue?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        }
        // setPropData(data, [db2monitorProp, db2replicaMonitor]);
    }
}


function setPropData( data, propSets, value) {
    propSets?.forEach(properties => {
        bindProperties( data, properties, value);
    });
}

function bindProperties(data, properties, value) {
    let prStatus
    let drStatus
    value?.serverStatus.forEach(server => {
        
        Object.keys(server).forEach(key => {            
            let data = server[key]
            console.log(data, 'serverdata')
            prStatus = data?.status?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : data?.status?.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : data?.status?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";
            drStatus = data?.status?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : data?.status?.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : data?.status?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";

        })
    })
    //let prStatus = value?.prServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value?.prServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.prServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";
    //let drStatus = value?.drServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value?.drServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";
   
    const iconMapping = {
        'PR_Server_IpAddress': prStatus,
        'Server_IpAddress': drStatus,
        'PR_Server_HostName': prStatus,
        'Server_HostName':drStatus,
        "PRDBName": 'text-success cp-database-unique-name',
        "DBName": 'text-success cp-database-unique-name',
        "PRMongodbStatus": 'text-success cp-dataguard-status',
        "MongodbStatus": 'text-success cp-dataguard-status',
        "PRDBVersion": 'text-success cp-database',
        "DBVersion": 'text-success cp-database',
        "SizesOnDiskPR": 'text-success cp-database-size',
        "SizesOnDiskDR": 'text-success cp-database-size',
        "CollectionsPR": 'text-success cp-dataguard-status',
        "CollectionsDR": 'text-success cp-dataguard-status',
        "DocumentsPR": 'text-success cp-dataguard-status',
        "DocumentsDR": 'text-success cp-dataguard-status',
        "IndexesPR": 'text-success cp-dataguard-status',
        "IndexesDR": 'text-success cp-dataguard-status',
        "HostnamePR": 'cp-host-name text-primary',
        "HostNameDR": 'cp-host-name text-primary',
        "PRCurrentPriority": 'text-success cp-priority',
        "CurrentPriority": 'text-success cp-priority',
        "PRreplicaSetName": 'text-success cp-replication-source',
        "ReplicaSetName": 'text-success cp-replication-source',
        "PRStateDescription": 'text-success cp-relationship-state',
        "StateDescription": 'text-success cp-relationship-state',
        
        "PRhealth": 'text-success cp-health',
        "Health": 'text-success cp-health',
        "PRlastHeartbeatMessage":'text-success cp-online',
        "LastHeartbeatMessage": 'text-success cp-online',
        "PRmemberID": 'text-primary cp-id',
        "MemberID": 'text-primary cp-id',
        "Datalag": 'text-success cp-data-lag',
        "Monitor_Type": 'text-success cp-monitoring',
        "PR_Database": 'text-success cp-database-unique-name',
        "Database": 'text-success cp-database-unique-name',
        "PR_Server_Edition": 'text-success cp-fal-server',
        "Server_Edition": 'text-success cp-fal-server',
        "IsDRWindows": 'text-success cp-windows',
        "IsPRWindows": 'text-success cp-windows',
        "PR_LSN_Logs": 'text-success cp-logs',
        "LSN_Logs": 'text-success cp-logs'

    };
    properties?.forEach(property => {
        const value = data[property];
        let displayedValue = value !== undefined ? checkAndReplace(value) : 'NA';
        let iconClass = iconMapping[property] || '';

        // Add icons based on conditions
        if (displayedValue === 'NA') {
            iconClass = 'cp-disable text-danger';
        }
        else if (displayedValue === 'up') {
            iconClass = 'text-success cp-health';
        }
        else if (displayedValue === 'down') {
            iconClass = 'cp-error text-danger';
        }
        else if (displayedValue === 'Streaming') {
            iconClass = 'text-success cp-refresh';
        }
        else if (displayedValue?.includes('Running')) {
            iconClass = 'text-success cp-reload cp-animate me-1 fs-6';
        }
        else if (displayedValue?.includes('stopped') || displayedValue?.includes('stop')) {
            iconClass = 'text-danger cp-Stopped';
        }
        else if (displayedValue?.includes('production')) {
            iconClass = 'text-warning cp-log-archive-config';
        }
        else if (displayedValue?.includes('archive recovery')) {
            iconClass = 'text-warning cp-log-archive-config';
        }
        else if (displayedValue === 'f' || displayedValue === 'false') {
            iconClass = 'text-danger cp-error';
        }
        else if (displayedValue === 't' || displayedValue === 'true') {
            iconClass = 'text-success cp-success';
        }
        // Displayed value with icon
        const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(mergeValue).attr('title', displayedValue);
    });
}