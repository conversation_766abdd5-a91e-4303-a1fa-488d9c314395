using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SettingRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly SettingRepository _repository;
    private readonly SettingFixture _fixture;

    public SettingRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new SettingRepository(_dbContext);
        _fixture = new SettingFixture();
    }

    #region IsSettingKeyExist Tests

    [Fact]
    public async Task IsSettingKeyExist_ShouldReturnTrue_WhenKeyExistsAndIdIsNotValidGuid()
    {
        // Arrange
        await ClearDatabase();

        var setting = _fixture.CreateSetting(sKey: "TestKey", sValue: "TestValue");
        await _repository.AddAsync(setting);

        // Act
        var result = await _repository.IsSettingKeyExist("TestKey", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsSettingKeyExist_ShouldReturnFalse_WhenKeyDoesNotExistAndIdIsNotValidGuid()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsSettingKeyExist("NonExistentKey", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSettingKeyExist_ShouldReturnFalse_WhenKeyExistsButIdMatches()
    {
        // Arrange
        await ClearDatabase();

        var setting = _fixture.CreateSetting(sKey: "TestKey", sValue: "TestValue");
        await _repository.AddAsync(setting);

        // Act
        var result = await _repository.IsSettingKeyExist("TestValue", setting.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSettingKeyExist_ShouldReturnTrue_WhenValueExistsAndIdIsDifferent()
    {
        // Arrange
        await ClearDatabase();

        var setting = _fixture.CreateSetting(sKey: "TestKey", sValue: "TestValue");
        await _repository.AddAsync(setting);

        // Act
        var result = await _repository.IsSettingKeyExist("TestValue", Guid.NewGuid().ToString());

        // Assert
        Assert.True(result);
    }

    #endregion

    #region IsSettingKeyUnique Tests

    [Fact]
    public async Task IsSettingKeyUnique_ShouldReturnTrue_WhenKeyExists()
    {
        // Arrange
        await ClearDatabase();

        var setting = _fixture.CreateSetting(sKey: "ExistingKey");
        await _repository.AddAsync(setting);

        // Act
        var result = await _repository.IsSettingKeyUnique("ExistingKey");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsSettingKeyUnique_ShouldReturnFalse_WhenKeyDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsSettingKeyUnique("NonExistentKey");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSettingKeyUnique_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();

        var setting = _fixture.CreateSetting(sKey: "TestKey");
        await _repository.AddAsync(setting);

        // Act
        var result = await _repository.IsSettingKeyUnique("testkey");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetSettingByKey Tests

    [Fact]
    public async Task GetSettingByKey_ShouldReturnSetting_WhenKeyExists()
    {
        // Arrange
        await ClearDatabase();

        var setting = _fixture.CreateSetting(sKey: "TestKey", sValue: "TestValue");
        await _repository.AddAsync(setting);

        // Act
        var result = await _repository.GetSettingByKey("TestKey");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("TestKey", result.SKey);
        Assert.Equal("TestValue", result.SValue);
    }

    [Fact]
    public async Task GetSettingByKey_ShouldReturnNull_WhenKeyDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetSettingByKey("NonExistentKey");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetSettingByKey_ShouldBeCaseInsensitive()
    {
        // Arrange
        await ClearDatabase();

        var setting = _fixture.CreateSetting(sKey: "TestKey", sValue: "TestValue");
        await _repository.AddAsync(setting);

        // Act
        var result = await _repository.GetSettingByKey("testkey");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("TestKey", result.SKey);
    }

    [Fact]
    public async Task GetSettingByKey_ShouldTrimWhitespace()
    {
        // Arrange
        await ClearDatabase();

        var setting = _fixture.CreateSetting(sKey: "  TestKey  ", sValue: "TestValue");
        await _repository.AddAsync(setting);

        // Act
        var result = await _repository.GetSettingByKey("  testkey  ");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("  TestKey  ", result.SKey);
    }

    #endregion

    #region GetSettingLogByKey Tests

    [Fact]
    public async Task GetSettingLogByKey_ShouldReturnUIValue_WhenKeyExistsAndHasUIProperty()
    {
        // Arrange
        await ClearDatabase();

        var jsonValue = new JObject
        {
            ["UI"] = "UI Log Value",
            ["Other"] = "Other Value"
        }.ToString();

        var setting = _fixture.CreateSetting(sKey: "LogKey", sValue: jsonValue, isActive: true);
        await _repository.AddAsync(setting);

        // Act
        var result = await _repository.GetSettingLogByKey("LogKey");

        // Assert
        Assert.Equal("UI Log Value", result);
    }

    [Fact]
    public async Task GetSettingLogByKey_ShouldReturnEmptyString_WhenKeyDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetSettingLogByKey("NonExistentKey");

        // Assert
        Assert.Equal(string.Empty, result);
    }

    [Fact]
    public async Task GetSettingLogByKey_ShouldReturnEmptyString_WhenSettingIsInactive()
    {
        // Arrange
        await ClearDatabase();

        var jsonValue = new JObject
        {
            ["UI"] = "UI Log Value"
        }.ToString();

        var setting = _fixture.CreateSetting(sKey: "LogKey", sValue: jsonValue, isActive: false);
        await _dbContext.Settings.AddAsync(setting);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetSettingLogByKey("LogKey");

        // Assert
        Assert.Equal(string.Empty, result);
    }

    [Fact]
    public async Task GetSettingLogByKey_ShouldReturnEmptyString_WhenUIPropertyDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var jsonValue = new JObject
        {
            ["Other"] = "Other Value"
        }.ToString();

        var setting = _fixture.CreateSetting(sKey: "LogKey", sValue: jsonValue, isActive: true);
        await _dbContext.Settings.AddAsync(setting);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetSettingLogByKey("LogKey");

        // Assert
        Assert.Equal(null, result);
    }

    [Fact]
    public async Task GetSettingLogByKey_ShouldBeCaseInsensitive()
    {
        // Arrange
        await ClearDatabase();

        var jsonValue = new JObject
        {
            ["UI"] = "UI Log Value"
        }.ToString();

        var setting = _fixture.CreateSetting(sKey: "LogKey", sValue: jsonValue, isActive: true);
        await _repository.AddAsync(setting);

        // Act
        var result = await _repository.GetSettingLogByKey("logkey");

        // Assert
        Assert.Equal("UI Log Value", result);
    }

    [Fact]
    public async Task GetSettingLogByKey_ShouldTrimWhitespace()
    {
        // Arrange
        await ClearDatabase();

        var jsonValue = new JObject
        {
            ["UI"] = "UI Log Value"
        }.ToString();

        var setting = _fixture.CreateSetting(sKey: "  LogKey  ", sValue: jsonValue, isActive: true);
        await _repository.AddAsync(setting);

        // Act
        var result = await _repository.GetSettingLogByKey("  logkey  ");

        // Assert
        Assert.Equal("UI Log Value", result);
    }

    [Fact]
    public async Task GetSettingLogByKey_ShouldHandleInvalidJson()
    {
        // Arrange
        await ClearDatabase();

        var setting = _fixture.CreateSetting(sKey: "LogKey", sValue: "invalid json", isActive: true);
        await _repository.AddAsync(setting);

        // Act & Assert
        await Assert.ThrowsAsync<Newtonsoft.Json.JsonReaderException>(
            () => _repository.GetSettingLogByKey("LogKey"));
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.Settings.RemoveRange(_dbContext.Settings);
        await _dbContext.SaveChangesAsync();
    }
}
