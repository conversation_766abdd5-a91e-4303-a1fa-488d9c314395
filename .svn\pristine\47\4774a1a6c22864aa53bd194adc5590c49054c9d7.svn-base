﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Node.Events.Delete;

public class NodeDeletedEventHandler : INotificationHandler<NodeDeletedEvent>
{
    private readonly ILogger<NodeDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public NodeDeletedEventHandler(ILoggedInUserService userService, ILogger<NodeDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(NodeDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.Node.ToString(),
            Action = $"{ActivityType.Delete} {Modules.Node}",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $" Node '{deletedEvent.NodeName}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Node '{deletedEvent.NodeName}' deleted successfully.");
    }
}