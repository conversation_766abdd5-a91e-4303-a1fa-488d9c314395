﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfileInfo.Queries
{
    public class GetWorkflowProfileInfoPaginatedListQueryHandlerTests : IClassFixture<WorkflowProfileInfoFixture>
    {
        private readonly WorkflowProfileInfoFixture _workflowProfileInfoFixture;

        private readonly Mock<IWorkflowViewRepository> _mockWorkflowViewRepository;

        private readonly GetWorkflowProfileInfoPaginatedListQueryHandler _handler;

        public GetWorkflowProfileInfoPaginatedListQueryHandlerTests(WorkflowProfileInfoFixture workflowProfileInfoFixture)
        {
            _workflowProfileInfoFixture = workflowProfileInfoFixture;

            _mockWorkflowViewRepository = new Mock<IWorkflowViewRepository>();

            _handler = new GetWorkflowProfileInfoPaginatedListQueryHandler(_workflowProfileInfoFixture.Mapper, _mockWorkflowViewRepository.Object);

            _workflowProfileInfoFixture.WorkflowProfileInfos[0].ProfileName = "Demo_WF1";
            _workflowProfileInfoFixture.WorkflowProfileInfos[0].BusinessFunctionName = "BF_Test1";
            _workflowProfileInfoFixture.WorkflowProfileInfos[0].BusinessServiceName = "BS_Test1";

            _workflowProfileInfoFixture.WorkflowProfileInfos[1].ProfileName = "Demo_WF2";
            _workflowProfileInfoFixture.WorkflowProfileInfos[1].BusinessFunctionName = "BF_Test2";
            _workflowProfileInfoFixture.WorkflowProfileInfos[1].BusinessServiceName = "BS_Test2";
            _workflowProfileInfoFixture = workflowProfileInfoFixture;
        }

        [Fact]
        public async Task Handle_Return_TotalPage_ShouldRequested()
        {
            var result = await _handler.Handle(new GetWorkflowProfileInfoPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

            result.ShouldBeOfType<PaginatedResult<WorkflowProfileInfoListVm>>();

            result.TotalCount.ShouldBe(3);

            result.TotalPages.ShouldBe(1);
        }

        [Fact]
        public async Task Handle_Return_PaginatedWorkflowProfileInfo_When_QueryStringMatch()
        {
            var result = await _handler.Handle(new GetWorkflowProfileInfoPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Demo" }, CancellationToken.None);

            result.ShouldBeOfType<PaginatedResult<WorkflowProfileInfoListVm>>();

            result.TotalCount.ShouldBe(2);

            result.Data[0].Id.ShouldBeGreaterThan(0.ToString());
            result.Data[0].ProfileId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ProfileId);
            result.Data[0].ProfileName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ProfileName);
            result.Data[0].InfraObjectId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].InfraObjectId);
            result.Data[0].InfraObjectName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].InfraObjectName);
            result.Data[0].BusinessFunctionId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].BusinessFunctionId);
            result.Data[0].BusinessFunctionName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].BusinessFunctionName);
            result.Data[0].BusinessServiceId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].BusinessServiceId);
            result.Data[0].BusinessServiceName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].BusinessServiceName);
            result.Data[0].WorkflowId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].WorkflowId);
            result.Data[0].WorkflowName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].WorkflowName);
            result.Data[0].CurrentActionId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].CurrentActionId);
            result.Data[0].CurrentActionName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].CurrentActionName);
            result.Data[0].Message.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].Message);
            result.Data[0].ConditionalOperation.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ConditionalOperation);
            result.Data[0].WorkflowType.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].WorkflowType);
            result.Data[0].ActionMode.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ActionMode);
            result.Data[0].Status.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].Status);
            result.Data[0].ProgressStatus.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ProgressStatus);
        }

        [Fact]
        public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
        {
            var result = await _handler.Handle(new GetWorkflowProfileInfoPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "AaBbCc" }, CancellationToken.None);

            result.ShouldBeOfType<PaginatedResult<WorkflowProfileInfoListVm>>();

            result.TotalCount.ShouldBe(0);
        }

        [Fact]
        public async Task Handle_Return_WorkflowProfileInfo_With_MultipleQueryStringParameter()
        {
            var result = await _handler.Handle(new GetWorkflowProfileInfoPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "profilename=Demo;businessfunctionname=BF;businessservicename=BS" }, CancellationToken.None);

            result.ShouldBeOfType<PaginatedResult<WorkflowProfileInfoListVm>>();

            result.TotalCount.ShouldBe(2);

            result.Data[0].ProfileId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ProfileId);
            result.Data[0].ProfileName.ShouldBe("Demo_WF1");
            result.Data[0].InfraObjectId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].InfraObjectId);
            result.Data[0].InfraObjectName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].InfraObjectName);
            result.Data[0].BusinessFunctionId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].BusinessFunctionId);
            result.Data[0].BusinessFunctionName.ShouldBe("BF_Test1");
            result.Data[0].BusinessServiceId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].BusinessServiceId);
            result.Data[0].BusinessServiceName.ShouldBe("BS_Test1");
            result.Data[0].WorkflowId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].WorkflowId);
            result.Data[0].WorkflowName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].WorkflowName);
            result.Data[0].CurrentActionId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].CurrentActionId);
            result.Data[0].CurrentActionName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].CurrentActionName);
            result.Data[0].Message.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].Message);
            result.Data[0].ConditionalOperation.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ConditionalOperation);
            result.Data[0].WorkflowType.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].WorkflowType);
            result.Data[0].ActionMode.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ActionMode);
            result.Data[0].Status.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].Status);
            result.Data[0].ProgressStatus.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ProgressStatus);
        }

        [Fact]
        public async Task Handle_Call_PaginatedListAsyncMethod_OnlyOnce()
        {
            await _handler.Handle(new GetWorkflowProfileInfoPaginatedListQuery(), CancellationToken.None);

            _mockWorkflowViewRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
        }
    }
}
