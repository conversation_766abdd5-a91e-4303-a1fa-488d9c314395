﻿
let mId = sessionStorage.getItem("monitorId");
let monitortype = 'AzureMysqlPaas';
let infraObjectId = sessionStorage.getItem("infraobjectId");

setTimeout(() => { azuremysqlmonitorstatus(mId, monitortype) }, 250)

setTimeout(() => { monitoringSolution(infraObjectId) }, 250)


$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})



async function azuremysqlmonitorstatus(id, type) {

    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
}

function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}

let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'

function propertiesData(value) {
    
    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    else {
        let data = JSON?.parse(value?.properties);
        //  mysqlSolutionDiagram(data);
        let customSite = data?.AzureMysqlPaasMonitoring?.length > 1;
        if (customSite) {
            $("#Sitediv").show();
        } else {
            $("#Sitediv").hide();
        }


        $(".siteContainer").empty();


        data?.AzureMysqlPaasMonitoring?.forEach((a, index) => {
            let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        });


        if (data?.AzureMysqlPaasMonitoring?.length > 0) {
            $("#siteName0 .nav-link").addClass("active");
            displaySiteData(data?.AzureMysqlPaasMonitoring[0]);
        }



        let defaultSite = data?.AzureMysqlPaasMonitoring?.find(d => d?.Type === 'DR') || data?.AzureMysqlPaasMonitoring[0];
        if (defaultSite) {
            displaySiteData(defaultSite);
        }

        $(document).on('click', '.siteListChange', function () {
           
            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0]?.id
            let getSiteName = $(`#${siteId} .siteName`).text()

            let MonitoringModel = data?.AzureMysqlPaasMonitoring?.find(d => d?.Type === getSiteName);
            if (MonitoringModel) {
                displaySiteData(MonitoringModel);
            }
        });

        function displaySiteData(siteData) {
            let obj = {};
           // $('.dynamicSite-header').text(siteData.Type).attr('title', siteData.Type);

            for (let key in siteData?.Replica) {
                obj[`DR_` + key] = siteData?.Replica[key];
            }

            let MonitoringModelMysqlpass = [
                "DR_ServerName", "DR_ServerVersion", "DR_ServerFQDN",
                "DR_ServerResourceGroupName", "DR_ServerLocation", "DR_ServerStatus", "DR_ReplicationRole",
                "DR_Datalag"
            ];

            if (Object.keys(obj)?.length > 0) {
                bindProperties(obj, MonitoringModelMysqlpass, value);
            }
        }
     
        let dbDetail = data?.AzureMysqlPaasPRMonitoring?.Primary
        const dbDetailsProp = [
            "ServerName", "ServerVersion", "ServerFQDN",
            "ServerResourceGroupName", "ServerLocation", "ServerStatus", "ReplicationRole",
            "Datalag"
        ];

        bindProperties(dbDetail, dbDetailsProp, value);
        //Database Details

        //Datalag
        const datalag = checkAndReplace(data?.PR_Datalag);
        let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0") ? `${(datalag)}` : 'NA';

        var result = "";

        if (dataLagValue?.includes(".")) {
            var value = dataLagValue?.split(".");
            var hours = value[0] * 24;
            var minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
            var min = minutes?.split(':');
            var firstValue = parseInt(min[0]) + parseInt(hours);
            result = firstValue + ":" + min[1];
            const minute = (parseInt(result[0]) * 60) + parseInt(result[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        }
        else if (dataLagValue?.includes("+")) {
            const value = dataLagValue.split(" ");
            result = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')

        }
        else {
            result = dataLagValue?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        }
    }
}

function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}

function setPropData(data, propSets, value) {
    propSets?.forEach(properties => {
        bindProperties(data, properties, value);
    });
}

function bindProperties(data, properties, value) {

    properties?.forEach(property => {
        const values = data[property];
        const displayedValue = value !== undefined ? checkAndReplace(values) : 'NA';
        // Displayed value with icon
        const iconHtml = getIconClass(displayedValue, property, data, value);
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(mergeValue).attr('title', displayedValue);
    });
}

function getIconClass(displayedValue, property, data, value) {

    //let prStatus = value?.prServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value.prServerStatus.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.prServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";
    //let drStatus = value?.drServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value.drServerStatus.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";
    let prservericon = data?.ServerName ? "text-primary cp-server me-1" : "text-danger cp-disable"
    let drservericon = data?.DR_ServerName ? "text-primary cp-server me-1" : "text-danger cp-disable"
    let prServerVersion = data?.ServerVersion ? "cp-version text-primary" : "text-danger cp-disable"
    let drServerVersion = data?.DR_ServerVersion ? "cp-version text-primary" : "text-danger cp-disable"
    let prserverfdqn = data?.ServerFQDN ? "cp-control-file-type text-primary" : "text-danger cp-disable"
    let drserverfdqn = data?.DR_ServerFQDN ? "cp-control-file-type text-primary" : "text-danger cp-disable"
    let prresource = data?.ServerResourceGroupName ? "cp-manage-server text-primary" : "text-danger cp-disable"
    let drresource = data?.DR_ServerResourceGroupName ? "cp-manage-server text-primary" : "text-danger cp-disable"
    let prlocation = data?.ServerLocation ? "cp-location text-primary" : "text-danger cp-disable"
    let drlocation = data?.DR_ServerLocation ? "cp-location text-primary" : "text-danger cp-disable"
    let prrole = data?.ReplicationRole ? "cp-replication-type text-primary" : "text-danger cp-disable"
    let drrole = data?.DR_ReplicationRole ? "cp-replication-type text-primary" : "text-danger cp-disable"
    let prdatalag = data?.Datalag ? "cp-time text-primary mt-2" : "text-danger cp-disable"
    let drdatalag = data?.DR_Datalag ? "cp-time text-primary mt-2" : "text-danger cp-disable"

    const iconMapping = {
        'ServerName': prservericon,
        'DR_ServerName': drservericon,
        'ServerVersion': prServerVersion,
        'DR_ServerVersion': drServerVersion,
        'ServerFQDN': prserverfdqn,
        'DR_ServerFQDN': drserverfdqn,
        'ServerResourceGroupName': prresource,
        'DR_ServerResourceGroupName': drresource,
        'ServerLocation': prlocation,
        'DR_ServerLocation': drlocation,
        'ReplicationRole': prrole,
        'DR_ReplicationRole': drrole,
        'Datalag': prdatalag,
        'DR_Datalag': drdatalag,
    };

    let iconClass = iconMapping[property] || '';

    switch (displayedValue?.toLowerCase()) {

        case 'not allowed':
        case 'na':
            iconClass = 'text-danger cp-disable';
            break;
        case 'no':
            iconClass = 'text-danger cp-disagree';
            break;
        case 'streaming':
            iconClass = 'text-success cp-refresh';
            break;
        case 'running':
        case 'run':
        case 'ready':
            iconClass = 'text-success cp-reload cp-animate';
            break;
        case 'stopped':
        case 'stop':
            iconClass = 'text-danger cp-Stopped';
            break;
        case 'f':
        case 'false':
        case 'defer':
        case 'deferred':
            iconClass = 'text-danger cp-error';
            break;
        case 't':
        case 'yes':
        case 'deferred':
            iconClass = 'text-success cp-agree';
            break;
        case 'valid':
            iconClass = 'text-success cp-success';
            break;
        case 'pending':
            iconClass = 'text-warning cp-pending';
            break;
        case 'pause':
        case 'paused':
            iconClass = 'text-warning cp-circle-pause';
            break;
        case 'manual':
            iconClass = 'text-warning cp-settings';
            break;
        case 'synchronous_commit':
        case 'synchronized':
        case 'synchronizing':
        case 'sync':
            iconClass = 'text-success cp-refresh';
            break;
        case 'asynchronous_commit':
        case 'asynchronizing':
        case 'asynchronized':
        case 'async':
            iconClass = 'text-danger cp-refresh';
            break;
        case 'online':
            iconClass = 'text-success cp-online';
            break;
        case 'offline':
            iconClass = 'text-danger cp-offline';
            break;
        case 'disabled':
        case 'disable':
            iconClass = 'text-danger cp-disables';
            break;
        case 'enabled':
        case 'enable':
            iconClass = 'text-success cp-enables';
            break;
        case 'connected':
        case 'connect':
            iconClass = 'text-success cp-connected';
            break;
        case 'disconnected':
        case 'disconnect':
            iconClass = 'text-danger cp-disconnecteds';
            break;
        case 'standby':
        case 'to standby':
            iconClass = 'text-warning cp-control-file-type';
            break;
        case 'required':
        case 'require':
            iconClass = 'text-warning cp-warning';
            break
        case 'healthy':
            iconClass = 'text-success cp-health-success';
            break;
        case 'not_healthy':
        case 'nothealthy':
        case 'unhealthy':
            iconClass = 'text-danger cp-health-error';
            break;
        case 'error':
            iconClass = 'text-danger cp-fail-back';
            break;
        case 'on':
            iconClass = 'text-success cp-end';
            break;
        case 'off':
            iconClass = 'text-danger cp-end';
            break;
        case 'current':
        case 'read write':
            iconClass = 'text-success cp-file-edits';
            break
        case 'primary':
            iconClass = 'text-primary cp-list-prsite';
            break
        case 'secondary':
            iconClass = 'text-info cp-dr';
            break
        case 'physical standby':
            iconClass = 'text-info cp-physical-drsite';
            break
        default:

            break;

    }
    return iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
}