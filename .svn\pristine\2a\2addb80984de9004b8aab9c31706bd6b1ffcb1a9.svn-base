﻿namespace ContinuityPatrol.Application.Features.Node.Queries.GetDetail;

public class GetNodeDetailQueryHandler : IRequestHandler<GetNodeDetailQuery, NodeDetailVm>
{
    private readonly IMapper _mapper;
    private readonly INodeRepository _nodeRepository;

    public GetNodeDetailQueryHandler(IMapper mapper, INodeRepository nodeRepository)
    {
        _mapper = mapper;
        _nodeRepository = nodeRepository;
    }

    public async Task<NodeDetailVm> Handle(GetNodeDetailQuery request, CancellationToken cancellationToken)
    {
        //Guard.Against.NegativeOrZero(request.Id, nameof(request.Id), ErrorMessage.Node.NodeIdCannotBeZero);

        var node = await _nodeRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(node, nameof(Domain.Entities.Node),
            new NotFoundException(nameof(Domain.Entities.Node), request.Id));

        var nodeDetailDto = _mapper.Map<NodeDetailVm>(node);

        return nodeDetailDto == null
            ? throw new NotFoundException(nameof(Domain.Entities.Node), request.Id)
            : nodeDetailDto;
    }
}