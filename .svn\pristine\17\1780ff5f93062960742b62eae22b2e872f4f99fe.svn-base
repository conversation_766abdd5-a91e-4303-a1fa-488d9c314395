﻿using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;

namespace ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetWorkflowProfileInfoByProfileId;

public class GetWorkflowProfileInfoByProfileIdQueryHandler : IRequestHandler<GetWorkflowProfileInfoByProfileIdQuery,
    List<GetWorkflowProfileInfoByProfileIdVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowProfileInfoViewRepository _workflowProfileInfoViewRepository;
    private readonly IWorkflowProfileRepository _workflowProfileRepository;
    private readonly IWorkflowExecutionTempRepository _workflowExecutionTempRepository;

    public GetWorkflowProfileInfoByProfileIdQueryHandler(IWorkflowProfileRepository workflowProfileRepository, 
        I<PERSON><PERSON>per mapper, IWorkflowExecutionTempRepository workflowExecutionTempRepository,
        IWorkflowProfileInfoViewRepository workflowProfileInfoViewRepository)
    {
        _workflowProfileRepository = workflowProfileRepository;
        _mapper = mapper;
        _workflowExecutionTempRepository = workflowExecutionTempRepository;
        _workflowProfileInfoViewRepository = workflowProfileInfoViewRepository;
    }

    public async Task<List<GetWorkflowProfileInfoByProfileIdVm>> Handle(GetWorkflowProfileInfoByProfileIdQuery request,
        CancellationToken cancellationToken)
    {
        var splitProfileIds = request.ProfileId.Split(',')
            .Where(id=>id.IsNotNullOrWhiteSpace())
            .ToList();

        var workflowProfile = await _workflowProfileRepository.GetByProfileIdAsync(splitProfileIds);

        var profileDto = _mapper.Map<List<GetWorkflowProfileInfoByProfileIdVm>>(workflowProfile);

        var profileIds = workflowProfile
            .Where(id=>id.ReferenceId.IsNotNullOrWhiteSpace())
            .Select(id => id.ReferenceId)
            .ToList();

        var workflowProfileInfos = await _workflowProfileInfoViewRepository.GetWorkflowProfileInfoByProfileIds(profileIds);

        var workflowProfileInfoDtos = _mapper.Map<List<WorkflowProfileInfoDto>>(workflowProfileInfos);

        var workflowIds = workflowProfileInfoDtos
            .Where(info => info.WorkflowId.IsNotNullOrWhiteSpace())
            .Select(info => info.WorkflowId)
            .ToList();

        var workflowExecutionTemp = await _workflowExecutionTempRepository.GetFirstMatchedIdAndExistenceAsync(workflowIds);

        var workflowExecutionTempDict = workflowExecutionTemp.ToDictionary(x => x.WorkflowId, x => x);

        workflowProfileInfoDtos = workflowProfileInfoDtos.Select(info =>
        {
            if (info.WorkflowId.IsNotNullOrWhiteSpace())
            {
                var executionTemp = workflowExecutionTempDict.GetValueOrDefault(info.WorkflowId);

                info.CustomId = executionTemp.Id;
                info.IsCustom = executionTemp.Exists;
                return info;
            }
            return info;
        }).ToList();

      
        profileDto.ForEach(profile =>
            profile.WorkflowProfileInfos.AddRange(
                workflowProfileInfoDtos.Where(info => info.ProfileId.Equals(profile.ProfileId))));

        return profileDto;


        #region OldCode

        //GetWorkflowProfileInfoByProfileIds

        //Task<List<WorkflowProfile>> GetByProfileIdAsync(List<string> id)

        //var splitProfileId = Regex.Split(request.ProfileId, ",");

        //foreach (var profileId in splitProfileId)
        //{
        //    var workflowProfile = await _workflowProfileRepository.GetByReferenceIdAsync(profileId);

        //    var profileDto = _mapper.Map<GetWorkflowProfileInfoByProfileIdVm>(workflowProfile);

        //    var workflowProfileInfos = await _workflowProfileInfoRepository
        //        .GetWorkflowProfileInfoByProfileIdAsync(workflowProfile.ReferenceId);

        //    if (workflowProfileInfos.Count > 0)
        //    {
        //        profileDto.WorkflowProfileInfos = _mapper.Map<List<WorkflowProfileInfoDto>>(workflowProfileInfos);

        //        foreach (var workflowProfileInfo in profileDto.WorkflowProfileInfos.Where(workflowProfileInfo =>
        //                     workflowProfileInfo.InfraObjectId.IsNotNullOrWhiteSpace()))
        //        {
        //            var infraObject =
        //                await _infraObjectRepository.GetByReferenceIdAsync(workflowProfileInfo.InfraObjectId);

        //            workflowProfileInfo.State = infraObject?.State ?? "";

        //            var workflowExecutionTemp = await _workflowExecutionTempRepository
        //                .FilterByWorkflowId(workflowProfileInfo.WorkflowId);

        //            workflowProfileInfo.CustomId = workflowExecutionTemp.FirstOrDefault()?.ReferenceId ?? "";
        //            workflowProfileInfo.IsCustom = workflowExecutionTemp.Any();

        //        }

        //        profileInfos.AddRangeAsync(profileDto);
        //    }
        //}




        //splitProfileId.ForEach(profileId =>
        //{
        //    var workflowProfile = _workflowProfileRepository.GetByReferenceIdAsync(profileId).Result;

        //    var profileDto = _mapper.Map<GetWorkflowProfileInfoByProfileIdVm>(workflowProfile);

        //    var workflowProfileInfos = _workflowProfileInfoRepository
        //        .GetWorkflowProfileInfoByProfileIdAsync(workflowProfile.ReferenceId).Result;

        //    if (workflowProfileInfos.Count > 0)
        //    {
        //        profileDto.WorkflowProfileInfos = _mapper.Map<List<WorkflowProfileInfoDto>>(workflowProfileInfos);

        //        profileDto.WorkflowProfileInfos.ForEach(info =>
        //        {
        //            if (info.InfraObjectId.IsNotNullOrWhiteSpace())
        //            {
        //                var infraObject = _infraObjectRepository.GetByReferenceIdAsync(info.InfraObjectId).Result;

        //                info.State = infraObject?.State ?? "";
        //            }
        //        });

        //        profileInfos.AddRangeAsync(profileDto);
        //    }
        //});

        //return Task.FromResult(profileInfos);

        #endregion
    }
}