using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.DriftCategoryMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetPaginatedList;

public class GetDriftCategoryMasterPaginatedListQueryHandler : IRequestHandler<GetDriftCategoryMasterPaginatedListQuery,
    PaginatedResult<DriftCategoryMasterListVm>>
{
    private readonly IDriftCategoryMasterRepository _driftCategoryMasterRepository;
    private readonly IMapper _mapper;

    public GetDriftCategoryMasterPaginatedListQueryHandler(IMapper mapper,
        IDriftCategoryMasterRepository driftCategoryMasterRepository)
    {
        _mapper = mapper;
        _driftCategoryMasterRepository = driftCategoryMasterRepository;
    }

    public async Task<PaginatedResult<DriftCategoryMasterListVm>> Handle(
        GetDriftCategoryMasterPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DriftCategoryMasterFilterSpecification(request.SearchString);

        var queryable =await _driftCategoryMasterRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var driftCategoryMasterList = _mapper.Map<PaginatedResult<DriftCategoryMasterListVm>>(queryable);

        return driftCategoryMasterList;
        //var queryable = _driftCategoryMasterRepository.GetPaginatedQuery();

        //var productFilterSpec = new DriftCategoryMasterFilterSpecification(request.SearchString);

        //var driftCategoryMasterList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<DriftCategoryMasterListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return driftCategoryMasterList;
    }
}