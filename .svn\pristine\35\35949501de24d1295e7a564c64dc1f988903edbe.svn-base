﻿namespace ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReport;

public class LicenseReportVm
{
    public int TotalCount { get; set; }
    public AvailableCountViewVm AvailableCountVm { get; set; }
    public UsedCountViewVm UsedCountVm { get; set; }
    public List<LicenseReportDetail> LicenseReportDetail { get; set; } = new();
}

public class LicenseReport
{
    public string ReportGeneratedBy { get; set; }
    public string Date { get; set; }
    public string ChildLicensePOIds { get; set; }
    public string LicenseType { get; set; }

    public List<LicenseReportVm> LicenseReportVms { get; set; } = new();
}

public class LicenseReportDetail
{
    public string LicenseId { get; set; }
    public string PONumber { get; set; }
    public List<DatabaseReportVm> DatabaseReportVms { get; set; } = new();
    public List<ReplicationReportVm> ReplicationReportVms { get; set; } = new();
    public List<ApplicationReportVm> ApplicationReportVms { get; set; } = new();
    public List<NetworkReportVm> NetworkReportVms { get; set; } = new();
    public List<StorageReportVm> StorageReportVms { get; set; } = new();
    public List<VirtualizationReportVm> VirtualizationReportVms { get; set; } = new();
    public List<DNSReportVm> DNSReportVms { get; set; } = new();
    public List<ThirdPartyReportVm> ThirdPartyReportVms { get; set; } = new();
    public List<ChildLicenseReportVm> ChildLicenseReportVms { get; set; } = new();
}

public class DatabaseReportVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string Category { get; set; }
    public string EntityId { get; set; }
    public string Entity { get; set; }
    public string Type { get; set; }
    public string EntityName { get; set; }
    public string EntityType { get; set; }
    public string EntityField { get; set; }
    public string CreatedDate { get; set; }
}

public class ReplicationReportVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string Category { get; set; }
    public string EntityId { get; set; }
    public string Entity { get; set; }
    public string Type { get; set; }
    public string EntityName { get; set; }
    public string EntityType { get; set; }
    public string EntityField { get; set; }
    public string CreatedDate { get; set; }
}

public class ApplicationReportVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string Category { get; set; }
    public string EntityId { get; set; }
    public string Entity { get; set; }
    public string Type { get; set; }
    public string EntityName { get; set; }
    public string EntityType { get; set; }
    public string EntityField { get; set; }
    public string CreatedDate { get; set; }
}

public class NetworkReportVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string Category { get; set; }
    public string EntityId { get; set; }
    public string Entity { get; set; }
    public string Type { get; set; }
    public string EntityName { get; set; }
    public string EntityType { get; set; }
    public string EntityField { get; set; }
    public string CreatedDate { get; set; }
}

public class StorageReportVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string Category { get; set; }
    public string EntityId { get; set; }
    public string Entity { get; set; }
    public string Type { get; set; }
    public string EntityName { get; set; }
    public string EntityType { get; set; }
    public string EntityField { get; set; }
    public string CreatedDate { get; set; }
}

public class VirtualizationReportVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string Category { get; set; }
    public string EntityId { get; set; }
    public string Entity { get; set; }
    public string Type { get; set; }
    public string EntityName { get; set; }
    public string EntityType { get; set; }
    public string EntityField { get; set; }
    public string CreatedDate { get; set; }
}

public class DNSReportVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string Category { get; set; }
    public string EntityId { get; set; }
    public string Entity { get; set; }
    public string Type { get; set; }
    public string EntityName { get; set; }
    public string EntityType { get; set; }
    public string EntityField { get; set; }
    public string CreatedDate { get; set; }
}

public class ThirdPartyReportVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string Category { get; set; }
    public string EntityId { get; set; }
    public string Entity { get; set; }
    public string Type { get; set; }
    public string EntityName { get; set; }
    public string EntityType { get; set; }
    public string EntityField { get; set; }
    public string CreatedDate { get; set; }
}

public class AvailableCountViewVm
{
    public int DatabaseAvailableCount { get; set; }
    public int ReplicationAvailableCount { get; set; }
    public int StorageAvailableCount { get; set; }
    public int VirtualizationAvailableCount { get; set; }
    public int ApplicationAvailableCount { get; set; }
    public int DnsAvailableCount { get; set; }
    public int NetworkAvailableCount { get; set; }
    public int ThirdPartyAvailableCount { get; set; }
}

public class UsedCountViewVm
{
    public int DatabaseUsedCount { get; set; }
    public int ReplicationUsedCount { get; set; }
    public int StorageUsedCount { get; set; }
    public int VirtualizationUsedCount { get; set; }
    public int ApplicationUsedCount { get; set; }
    public int DnsUsedCount { get; set; }
    public int NetworkUsedCount { get; set; }
    public int ThirdPartyUsedCount { get; set; }
}

public class ChildLicenseReportVm
{
    public string LicenseId { get; set; }
    public string PONumber { get; set; }
    public List<DatabaseReportVm> DatabaseReportVms { get; set; } = new();
    public List<ReplicationReportVm> ReplicationReportVms { get; set; } = new();
    public List<ApplicationReportVm> ApplicationReportVms { get; set; } = new();
    public List<NetworkReportVm> NetworkReportVms { get; set; } = new();
    public List<StorageReportVm> StorageReportVms { get; set; } = new();
    public List<VirtualizationReportVm> VirtualizationReportVms { get; set; } = new();
    public List<DNSReportVm> DNSReportVms { get; set; } = new();
    public List<ThirdPartyReportVm> ThirdPartyReportVms { get; set; } = new();
}