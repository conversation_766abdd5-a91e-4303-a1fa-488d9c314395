﻿using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Events.Approval;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Approval;

public class
    ApprovalMatrixApprovalCommandHandler : IRequestHandler<ApprovalMatrixApprovalCommand,
        ApprovalMatrixApprovalResponse>
{
    private readonly IApprovalMatrixApprovalRepository _approvalMatrixApprovalRepository;
    private readonly IApprovalMatrixRequestRepository _approvalMatrixRequestRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public ApprovalMatrixApprovalCommandHandler(IApprovalMatrixRequestRepository approvalMatrixRequestRepository,
        IApprovalMatrixApprovalRepository approvalMatrixApprovalRepository, IMapper mapper, IPublisher publisher)
    {
        _approvalMatrixRequestRepository = approvalMatrixRequestRepository;
        _approvalMatrixApprovalRepository = approvalMatrixApprovalRepository;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<ApprovalMatrixApprovalResponse> Handle(ApprovalMatrixApprovalCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _approvalMatrixApprovalRepository.GetByReferenceIdAsync(request.Id)
                            ?? throw new NotFoundException(nameof(Domain.Entities.ApprovalMatrixApproval), request.Id);

        eventToUpdate.Status = request.Status;

        _mapper.Map(request, eventToUpdate, typeof(ApprovalMatrixApprovalCommand),
            typeof(Domain.Entities.ApprovalMatrixApproval));

        await _approvalMatrixApprovalRepository.UpdateAsync(eventToUpdate);

        var approvalMatrixToUpdate = await _approvalMatrixRequestRepository.GetByReferenceIdAsync(request.Id);

        approvalMatrixToUpdate.Status = request.Status;
        approvalMatrixToUpdate.IsRequest = false;

        await _approvalMatrixRequestRepository.UpdateAsync(approvalMatrixToUpdate);

        var response = new ApprovalMatrixApprovalResponse
        {
            Message = $"Approval Matrix request approved successfully {eventToUpdate.ProcessName}",

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new ApprovalMatrixApprovalEvent { Name = eventToUpdate.ProcessName },
            cancellationToken);

        return response;
    }
}