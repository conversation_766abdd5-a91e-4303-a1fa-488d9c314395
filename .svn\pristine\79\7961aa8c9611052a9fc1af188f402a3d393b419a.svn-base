﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.OracleMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.OracleMonitorLogs.Queries.GetPaginatedList;

public class GetOracleMonitorLogsPaginatedListQueryHandler : IRequestHandler<GetOracleMonitorLogsPaginatedListQuery,
    PaginatedResult<OracleMonitorLogsListVm>>
{
    private readonly IMapper _mapper;
    private readonly IOracleMonitorLogsRepository _oracleMonitorLogsRepository;

    public GetOracleMonitorLogsPaginatedListQueryHandler(IOracleMonitorLogsRepository oracleMonitorLogsRepository,
        IMapper mapper)
    {
        _oracleMonitorLogsRepository = oracleMonitorLogsRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<OracleMonitorLogsListVm>> Handle(GetOracleMonitorLogsPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _oracleMonitorLogsRepository.GetPaginatedQuery();

        var productFilterSpec = new OracleMonitorLogsFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<OracleMonitorLogsListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}