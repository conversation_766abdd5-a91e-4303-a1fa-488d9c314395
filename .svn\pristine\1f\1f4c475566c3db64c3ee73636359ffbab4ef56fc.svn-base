﻿@using ContinuityPatrol.Shared.Services.Helper
@using Microsoft.AspNetCore.Mvc.TagHelpers;
@using ContinuityPatrol.Domain.Entities;
@model ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel.WorkflowProfileInfoListVm
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<style>
    .truncate {
        max-width: 12em !important;
    }
</style>
@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-workflow"></i><span> Workflow List </span></h6>
            <form class="d-flex" autocomplete="off">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="wfListSearch" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="cp-filter" title="Filter"></i>
                            </span>
                             <ul class="dropdown-menu filter-dropdown">
                                <li>
                                    <h6 class="dropdown-header">Filter Search</h6>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="workflow=" id="name">
                                        <label class="form-check-label" >
                                           Workflow Name
                                        </label>
                                    </div>
                                     <div>
                                        <input class="form-check-input" type="checkbox" value="businessservice=" id="bsName">
                                        <label class="form-check-label" >
                                             Operational Service
                                        </label>
                                    </div>
                                    @*  <div>
                                        <input class="form-check-input" type="checkbox" value="businessfunction=" id="bfName">
                                        <label class="form-check-label" >
                                             Operational Function
                                        </label>
                                    </div> *@
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="profile=" id="profilename">
                                        <label class="form-check-label" >
                                            Profile Name
                                        </label>
                                    </div>
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="infraobject=" id="infraname">
                                        <label class="form-check-label">
                                            InfraObject Name
                                        </label>
                                    </div>
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="workflowtype=" id="wfType">
                                        <label class="form-check-label">
                                           Operation Type
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
               
            </form>
        </div>
        <div class="card-body pt-0">
            <table class="datatable table table-hover dataTable no-footer" id="WorkFlowList" style="width:100%">
                <thead>
                    <tr>
                        <th >Sr.No.</th>
                        <th style="min-width: 240px; max-width: 380px;">Workflow Name</th>
                        <th >Operational Service</th>
                       @*  <th >Operational Function</th> *@
                        <th >Profile Name</th>
                        <th >InfraObject Name</th>
                      @*  <th>Action Type</th>*@
                        <th >Operation Type</th>
                        <th>LastExecution Date</th>
                        <th >Created By</th>
                        <th>Action</th>
                        @* <th>Report</th>
                        <th >View</th> *@
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>
  <!--Lock Create-->



<div class="modal fade" id="lockWorkFlowList">
<div class="modal-dialog modal-sm modal-dialog-centered">
    <div class="modal-content">
        <div class="modal-header p-0">
            <img class="delete-img" src="~/img/isomatric/Lock-Unlock.svg" alt="Lock & Unlock Img" />
        </div>

        <div class="modal-body text-center pt-0">
            <h5 class="fw-bold">Are you sure?</h5>
            <p>You want to <span class="updateLockText">lock</span> <span class="font-weight-bolder text-primary" id="workflowLockData" ></span> workflow?</p>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
            <button type="button" class="btn btn-primary" id="btnSaveLockWorkflow">Yes</button>
        </div>
    </div>
</div>
</div>

<div class="modal fade" id="publishWorkFlowList">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img mb-3" src="~/img/isomatric/publish-unpublish.svg" alt="Publish & Unpublish Img" />
            </div>

            <div class="modal-body text-center pt-0">
                <h5 class="fw-bold">Are you sure?</h5>
                <p>You want to <span class="updatePublishText">publish</span> <span class="font-weight-bolder text-primary" id="workflowPublishData"></span> workflow?</p>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
                <button type="button" class="btn btn-primary" id="btnSavePublishWorkflow">Yes</button>
            </div>
        </div>
    </div>
</div>


@* Authentication *@
<div class="modal fade" id="WFListAuthModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">

    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel">
        <div class="modal-content">
            <form>
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-lock"></i><span>Authentication</span></h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <div class="form-label">Username</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-lock"></i></span>
                            <input type="text" autocomplete="off" class="form-control" placeholder="Enter Username" id="WFListCurrentUserName" />
                            @* <span role="button" class="input-group-text toggle-password3"><i class="cp-password-visible"></i></span> *@
                            <p class="d-none" id="WFList_LoginId">@WebHelper.UserSession.LoggedUserId</p>
                          
                        </div>
                        <span id="workflowListLoginName-error"></span>
                    </div>
                    <div class="form-group">
                        <div class="form-label">Password</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-lock"></i></span>
                            <input type="password" class="form-control txtPassword3" maxlength="30" autocomplete="off" placeholder="Enter Password" id="ProfileNewPassword" />
                            @* <span role="button" class="input-group-text toggle-password3"><i class="cp-password-visible"></i></span> *@
                            <i class="cp-password-visible toggle-password3 me-2" title="Show Password"></i>
                        </div>
                        <span id="workflowListPassword-error"></span>
                    </div>
                   

                </div>
                <div class="modal-footer d-flex justify-content-between">
                    @* <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small> *@
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" id="WFListAuthCancel">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="WFListAuthConfirmation">Ok</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<div id="orchestrationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Orchestration.CreateAndEdit" aria-hidden="true"></div>
<div id="orchestrationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Orchestration.Delete" aria-hidden="true"></div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<script src="~/js/itautomation/workflowlist/workflowlist.js"></script>
<script src="~/js/common/show_hide_password.js"></script>
