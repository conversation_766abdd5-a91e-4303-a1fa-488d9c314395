﻿using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Approval;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixApprovalModel;
using ContinuityPatrol.Shared.Services.Provider;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.API;

[Route("api/[controller]")]
[ApiController]
public class ApprovalMatrixController : ControllerBase
{
    private readonly IDataProvider _dataProvider;

    public ApprovalMatrixController(IDataProvider dataProvider)
    {
        _dataProvider = dataProvider;
    }

    [HttpGet("accept")]
    public async Task<IActionResult> AcceptApproval(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
            return BadRequest("Missing token.");

        var decodedBytes = Convert.FromBase64String(token);
        var originalToken = Encoding.UTF8.GetString(decodedBytes);

        var deserializedToken = JsonConvert.DeserializeObject<ApprovalToken>(originalToken);

        if (DateTime.Now >= deserializedToken.Expiry)
        {
            var command = new ApprovalMatrixApprovalCommand
            {
                Id = deserializedToken.ReferenceId,
                ProcessName = deserializedToken.ProcessName,
                Status = "Approved"
            };

            var approval = await _dataProvider.ApprovalMatrixApproval.UpdateApprovalMatrixStatus(command);

            return approval.Success ? Ok("Approved") : BadRequest("Invalid or already used token.");
        }

        return BadRequest("Token has expired.");
    }

    [HttpGet("reject")]
    public async Task<IActionResult> RejectApproval(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
            return BadRequest("Missing token.");

        var decodedBytes = Convert.FromBase64String(token);
        var originalToken = Encoding.UTF8.GetString(decodedBytes);

        var deserializedToken = JsonConvert.DeserializeObject<ApprovalToken>(originalToken);

        if (DateTime.Now >= deserializedToken.Expiry)
        {
            var command = new ApprovalMatrixApprovalCommand
            {
                Id = deserializedToken.ReferenceId,
                ProcessName = deserializedToken.ProcessName,
                Status = "Rejected"
            };

            var approval = await _dataProvider.ApprovalMatrixApproval.UpdateApprovalMatrixStatus(command);

            return approval.Success ? Ok("Rejected") : BadRequest("Invalid or already used token.");
        }

        return BadRequest("Token has expired.");

    }
}