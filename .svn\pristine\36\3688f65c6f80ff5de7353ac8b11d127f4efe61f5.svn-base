﻿using Microsoft.Extensions.Caching.Memory;

namespace ContinuityPatrol.Shared.Services.Base
{
    public interface ICacheService
    {
        T Get<T>(string cacheKey) where T : class;
        void Set(string cacheKey, object item, int minutes = 30);
        void RemoveData(string key);
    }

    public class InMemoryCache : ICacheService
    {
        private readonly IMemoryCache _cache;

        public InMemoryCache(IMemoryCache cache)
        {
            _cache = cache;
        }

        public T Get<T>(string cacheKey) where T : class
        {
            return _cache.Get(cacheKey) as T;
        }

        public void RemoveData(string key)
        {
            _cache.Remove(key);
        }

        public void Set(string cacheKey, object item, int minutes = 30)
        {
            if (item != null)
            {
                _cache.Set(cacheKey, item, DateTime.Now.AddMinutes(minutes));
            }
        }
    }
}
