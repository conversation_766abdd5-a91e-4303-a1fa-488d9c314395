﻿namespace ContinuityPatrol.Application.Models;

public class UserSession
{
    public string LoginName { get; set; }
    public string Token { get; set; }
    public string UserName { get; set; }
    public string LoggedUserId { get; set; }
    public string CompanyId { get; set; }
    public string CompanyName { get; set; }
    public string SessionId { get; set; }
    public string Permissions { get; set; }
    public string ParentCompanyId { get; set; }
    public string AuthenticationType { get; set; }
    public string RoleId { get; set; }
    public string RoleName { get; set; }
    public bool IsParent { get; set; }
    public bool IsAllInfra { get; set; }
    public int Expires { get; set; }
    public bool LicenseExpiry { get; set; }
    public bool EmptyLicense { get; set; }
    public string AssignedInfras { get; set; }
    public bool IsUserAuthenticated { get; set; }
    public string AdUserName { get; set; }
    public string SignalRUrl { get; set; }
    public string ChatBotRUrl { get; set; }
    public string CpVersion { get; set; }
}