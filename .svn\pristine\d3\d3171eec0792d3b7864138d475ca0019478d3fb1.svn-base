using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Xunit;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowDrCalenderRepositoryTests : IClassFixture<WorkflowDrCalenderFixture>
    {
        private readonly WorkflowDrCalenderFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowDrCalenderRepository _repository;

        public WorkflowDrCalenderRepositoryTests(WorkflowDrCalenderFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _repository = new WorkflowDrCalenderRepository(_dbContext, DbContextFactory.GetMockUserService());
        }

        [Fact]
        public async Task IsNameExist_ReturnsTrue_WhenNameExists_AndIdIsInvalidGuid()
        {
            var entity = _fixture.WorkflowDrCalenderDto;
            entity.UserName = "TestUser";
            _dbContext.WorkflowDrCalenders.Add(entity);
            _dbContext.SaveChanges();

            var result = await _repository.IsNameExist("TestUser", "invalid-guid");

            Assert.True(result);
        }

        [Fact]
        public async Task IsNameExist_ReturnsFalse_WhenNameDoesNotExist_AndIdIsInvalidGuid()
        {
            var result = await _repository.IsNameExist("NonExistent", "invalid-guid");

            Assert.False(result);
        }

        [Fact]
        public async Task IsNameExist_ReturnsExpected_WhenIdIsValidGuid()
        {
            var entity = _fixture.WorkflowDrCalenderDto;
            entity.ReferenceId = Guid.NewGuid().ToString();
            entity.UserName = "UniqueUser";
            await _dbContext.WorkflowDrCalenders.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.IsNameExist("UniqueUser", entity.ReferenceId);

            Assert.False(result);
        }
    }
}