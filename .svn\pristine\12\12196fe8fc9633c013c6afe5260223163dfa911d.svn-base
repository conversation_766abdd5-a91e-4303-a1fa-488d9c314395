using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Withdraw;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetByRequestId;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixRequestModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage;

public class ApprovalMatrixRequestService : BaseClient, IApprovalMatrixRequestService
{
    public ApprovalMatrixRequestService(IConfiguration config, IAppCache cache, ILogger<ApprovalMatrixRequestService> logger) : base(config, cache, logger)
    {
    }

    public async Task<WithdrawApprovalMatrixRequestResponse> WithdrawApprovalMatrixRequest(WithdrawApprovalMatrixRequestCommand command)
    {
        var request = new RestRequest("api/v6/approvalmatrixrequests/withdraw", Method.Put);

        request.AddJsonBody(command);

        return await Put<WithdrawApprovalMatrixRequestResponse>(request);
    }

    public async Task<List<ApprovalMatrixRequestListVm>> GetApprovalMatrixRequestList()
    {
        var request = new RestRequest("api/v6/approvalmatrixrequests");

        return await GetFromCache<List<ApprovalMatrixRequestListVm>>(request, "GetApprovalMatrixRequestList");
    }

    public async Task<BaseResponse> CreateAsync(CreateApprovalMatrixRequestCommand createApprovalMatrixRequestCommand)
    {
        var request = new RestRequest("api/v6/approvalmatrixrequests", Method.Post);

        request.AddJsonBody(createApprovalMatrixRequestCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateApprovalMatrixRequestCommand updateApprovalMatrixRequestCommand)
    {
        var request = new RestRequest("api/v6/approvalmatrixrequests", Method.Put);

        request.AddJsonBody(updateApprovalMatrixRequestCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/approvalmatrixrequests/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<ApprovalMatrixRequestDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/approvalmatrixrequests/{id}");

        return await Get<ApprovalMatrixRequestDetailVm>(request);
    }

    public async Task<List<ApprovalMatrixByRequestIdVm>> GetApprovalMatrixByRequestId(string requestId)
    {
        var request = new RestRequest($"api/v6/approvalmatrixrequests/request-id?requestId={requestId}");

        return await Get<List<ApprovalMatrixByRequestIdVm>>(request);
    }

    #region NameExist
    public async Task<bool> IsApprovalMatrixRequestNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/approvalmatrixrequests/name-exist?approvalmatrixrequestName={name}&id={id}");

        return await Get<bool>(request);
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<ApprovalMatrixRequestListVm>> GetPaginatedApprovalMatrixRequests(GetApprovalMatrixRequestPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/approvalmatrixrequests/paginated-list");

        return await Get<PaginatedResult<ApprovalMatrixRequestListVm>>(request);
    }

  


    #endregion
}
