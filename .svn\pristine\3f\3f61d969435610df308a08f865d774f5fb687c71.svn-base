using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DriftManagementMonitorLogsFixture : IDisposable
{
    public List<DriftManagementMonitorLogs> DriftManagementMonitorLogsPaginationList { get; set; }
    public List<DriftManagementMonitorLogs> DriftManagementMonitorLogsList { get; set; }
    public DriftManagementMonitorLogs DriftManagementMonitorLogsDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "INFRA_123";
    public const string LogMessage = "Test drift management log message";
    public const string LogLevel = "INFO";
    public const string Status = "Success";

    public ApplicationDbContext DbContext { get; private set; }

    public DriftManagementMonitorLogsFixture()
    {
        var fixture = new Fixture();

        DriftManagementMonitorLogsList = fixture.Create<List<DriftManagementMonitorLogs>>();

        DriftManagementMonitorLogsPaginationList = fixture.CreateMany<DriftManagementMonitorLogs>(20).ToList();

        DriftManagementMonitorLogsPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftManagementMonitorLogsPaginationList.ForEach(x => x.IsActive = true);
      
        DriftManagementMonitorLogsList.ForEach(x => x.IsActive = true);
      

        DriftManagementMonitorLogsDto = fixture.Create<DriftManagementMonitorLogs>();
        DriftManagementMonitorLogsDto.ReferenceId = Guid.NewGuid().ToString();
        DriftManagementMonitorLogsDto.IsActive = true;
   

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
