using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftCategoryList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftOperationSummary;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftResourceSummary;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetConflictList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetDriftTreeView;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetResourceStatus;
using ContinuityPatrol.Domain.ViewModels.DriftManagementMonitorStatusModel;
using ContinuityPatrol.Domain.ViewModels.DriftResourceSummaryModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class DriftManagementMonitorStatussController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<List<DriftManagementMonitorStatusListVm>>> GetDriftManagementMonitorStatuss()
    {
        Logger.LogDebug("Get All DriftManagementMonitorStatuss");

        return Ok(await Mediator.Send(new GetDriftManagementMonitorStatusListQuery()));
    }

    [HttpGet("{id}", Name = "GetDriftManagementMonitorStatus")]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<DriftManagementMonitorStatusDetailVm>> GetDriftManagementMonitorStatusById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftManagementMonitorStatus Id");

        Logger.LogDebug($"Get DriftManagementMonitorStatus Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDriftManagementMonitorStatusDetailQuery { Id = id }));
    }
    #region Paginated
    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<PaginatedResult<DriftManagementMonitorStatusListVm>>> GetPaginatedDriftManagementMonitorStatuss([FromQuery] GetDriftManagementMonitorStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in DriftManagementMonitorStatus Paginated List");

        return Ok(await Mediator.Send(query));
    }
    #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Drift.Create)]
    public async Task<ActionResult<CreateDriftManagementMonitorStatusResponse>> CreateDriftManagementMonitorStatus([FromBody] CreateDriftManagementMonitorStatusCommand createDriftManagementMonitorStatusCommand)
    {
        Logger.LogDebug($"Create DriftManagementMonitorStatus '{createDriftManagementMonitorStatusCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDriftManagementMonitorStatus), await Mediator.Send(createDriftManagementMonitorStatusCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Drift.Edit)]
    public async Task<ActionResult<UpdateDriftManagementMonitorStatusResponse>> UpdateDriftManagementMonitorStatus([FromBody] UpdateDriftManagementMonitorStatusCommand updateDriftManagementMonitorStatusCommand)
    {
        Logger.LogDebug($"Update DriftManagementMonitorStatus '{updateDriftManagementMonitorStatusCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateDriftManagementMonitorStatusCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Drift.Delete)]
    public async Task<ActionResult<DeleteDriftManagementMonitorStatusResponse>> DeleteDriftManagementMonitorStatus(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftManagementMonitorStatus Id");

        Logger.LogDebug($"Delete DriftManagementMonitorStatus Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteDriftManagementMonitorStatusCommand { Id = id }));
    }


    [Route("infraObjectId"), HttpGet]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<List<DriftManagementMonitorStatusListVm>>> GetDriftManagementStatusByInfraObjectId(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObject Id");

        Logger.LogDebug($"Get DriftManagementMonitorStatus Detail by infraObjectId '{infraObjectId}'");

        return Ok(await Mediator.Send(new GetByInfraObjectIdQuery { InfraObjectId = infraObjectId }));
    }

    [Route("drift-tree"), HttpGet]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<List<GetDriftTreeListVm>>> GetDriftTreeList()
    {
        Logger.LogDebug($"Get DriftManagementMonitorStatus tree List");

        return Ok(await Mediator.Send(new GetDriftTreeListQuery()));
    }

    [Route("drift-operation-summary"), HttpGet]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<DriftOperationSummaryVm>> GetDriftOperationSummary()
    {
        Logger.LogDebug("GetDriftOperationSummary List");

        return Ok(await Mediator.Send(new DriftOperationSummaryQuery()));
    }

    [Route("drift-resource-summary"), HttpGet]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<List<DriftResourceSummaryListVm>>> GetDriftResourceSummary()
    {
        Logger.LogDebug("GetDriftResourceSummary List");

        return Ok(await Mediator.Send(new GetDriftResourceSummaryQuery()));
    }
    [Route("drift-category"), HttpGet]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<DriftCategoryListVm>> GetDriftCategory()
    {
        Logger.LogDebug("GetDriftCategory List");

        return Ok(await Mediator.Send(new DriftCategoryListQuery()));
    }

    [Route("conflict-overview"), HttpGet]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<List<ConflictListVm>>> GetConflictOverView()
    {
        Logger.LogDebug("Get Conflict OverView ");

        return Ok(await Mediator.Send(new GetConflictListQuery()));
    }

    [Route("resource-status"), HttpGet]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<DriftDashboardResourceStatusVm>> GetDriftDashboardResourceStatus()
    {
        Logger.LogDebug("Get DriftDashboard Resource Status ");

        return Ok(await Mediator.Send(new DriftDashboardResourceStatusQuery()));
    }

    #region NameExist
    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsDriftManagementMonitorStatusNameExist(string driftManagementMonitorStatusName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(driftManagementMonitorStatusName, "DriftManagementMonitorStatus Name");

        Logger.LogDebug($"Check Name Exists Detail by DriftManagementMonitorStatus Name '{driftManagementMonitorStatusName}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetDriftManagementMonitorStatusNameUniqueQuery { Name = driftManagementMonitorStatusName, Id = id }));
    }
    #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


