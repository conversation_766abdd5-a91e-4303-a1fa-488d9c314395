﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CompanyRepository : BaseRepository<Company>, ICompanyRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public CompanyRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<Company>> ListAllAsync()
    {
        return IsParent
            ? await base.ListAllAsync()
            : await FindByFilter(company => company.ReferenceId.Equals(_loggedInUserService.CompanyId));
    }

    public override async Task<Company> GetByReferenceIdAsync(string id)
    {
        if (IsParent)
        {
            return await base.GetByReferenceIdAsync(id);
        }

        var companies = await FindByFilter(company => company.ReferenceId.Equals(id) && company.ReferenceId.Equals(_loggedInUserService.CompanyId));
        return companies.SingleOrDefault();
    }
    public override async Task<PaginatedResult<Company>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<Company> productFilterSpec, string sortColumn, string sortOrder)
    {
        var query = IsParent
            ? Entities.Specify(productFilterSpec).DescOrderById()
            : Entities.Specify(productFilterSpec).Where(x => x.ReferenceId.Equals(_loggedInUserService.CompanyId)).DescOrderById();

        return await SelectCompany(query).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public override IQueryable<Company> GetPaginatedQuery()
    {
        return IsParent
            ? base.GetPaginatedQuery()
            : Entities.Where(x => x.IsActive && x.ReferenceId.Equals(_loggedInUserService.CompanyId)).DescOrderById();
    }

    public async Task<List<Company>> GetAllCompanyNames()
    {
        return IsParent || !_loggedInUserService.UserId.IsValidGuid()
            ? await GetAllNames()
            : await GetChildCompanyName();
    }

    public async Task<Company> GetCompanyByLoginCompanyId(string id)
    {
        return await base.GetByReferenceIdAsync(id);
    }

    public async Task<Company> GetParentCompanyByLoginCompanyId(string id)
    {
        return await Entities.AsNoTracking()
            .Select(x=> new Company
            {
                Id = x.Id,
                ReferenceId = x.ReferenceId,
                DisplayName = x.DisplayName,
                IsParent = x.IsParent
            })
            .FirstOrDefaultAsync(x => x.ReferenceId==id);
    }

    public async Task<bool> IsCompanyAndDisplayNameUnique(string name, string displayName)
    {
        return await _dbContext.Companies.AnyAsync(e => e.Name.Equals(name) || e.DisplayName.Equals(displayName));
    }

    public async Task<bool> IsDisplayNameExist(string displayName, string id)
    {
        if (!id.IsValidGuid())
        {
            return await Entities.AnyAsync(e => e.DisplayName.Equals(displayName));
        }

        var entities = await Entities.Where(e => e.DisplayName.Equals(displayName)).ToListAsync();
        return entities.Unique(id);
    }

    public async Task<bool> IsNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
        {
            return await Entities.AnyAsync(e => e.Name == name);
        }
        var entities = await Entities.Where(e => e.Name == name).ToListAsync();
        return entities.Unique(id);
    }

    public override async Task<Company> GetByIdAsync(int id)
    {
        if (IsParent)
        {
            return await base.GetByIdAsync(id);
        }
        var companies = await FindByFilter(company => company.Id.Equals(id) && company.Id.Equals(_loggedInUserService.CompanyId));
        return companies.SingleOrDefault();
    }

    private async Task<List<Company>> GetAllNames()
    {
        return await Entities
            .Active()
            .Select(x => new Company
                { ReferenceId = x.ReferenceId, DisplayName = x.DisplayName,IsParent = x.IsParent})
            .OrderBy(x => x.DisplayName)
            .ToListAsync();
    }

    private async Task<List<Company>> GetChildCompanyName()
    {
        return await Entities
            .Active()
            .Where(x => x.ReferenceId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new Company { ReferenceId = x.ReferenceId, DisplayName = x.DisplayName, IsParent = x.IsParent })
            .OrderBy(x => x.DisplayName)
            .ToListAsync();
    }
    private IQueryable<Company> SelectCompany(IQueryable<Company> query)
    {
        return query.Select(x => new Company
        {
            Id = x.Id,
            CompanyLogo = x.CompanyLogo,
            DisplayName = x.DisplayName,
            IsParent = x.IsParent,
            LogoName = x.LogoName,
            Name = x.Name,
            ParentId = x.ParentId,
            ReferenceId = x.ReferenceId,
            WebAddress = x.WebAddress
        });
    }
}