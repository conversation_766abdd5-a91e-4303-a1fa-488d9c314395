﻿using ContinuityPatrol.Application.Features.Node.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Node.Validators;

public class CreateNodeValidatorTests
{
    private readonly Mock<INodeRepository> _mockNodeRepository;
    public List<Domain.Entities.Node> Nodes { get; set; }

    public CreateNodeValidatorTests()
    {
        Nodes = new Fixture().Create<List<Domain.Entities.Node>>();

        _mockNodeRepository = NodeRepositoryMocks.CreateNodeRepository(Nodes);
    }


    //NAME

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Name_InNode_WithEmpty(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_CreateNodeCommandValidator_Name_IsNull(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = null;

        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createNodeCommand);

        Assert.Contains("Please Enter Valid Server Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);

    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Name_InNode_Minimum(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "AR";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Name_InNode_Maximum(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "PTASDKNGDSGJGNFJDJDFGNVNNVSEFSKWOJRWIYRWERWKFNKJWQRKNVSBGG";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Valid_Name_InNode(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "   PTS  ";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Valid_Name_InNode_With_DoubleSpace_InFront(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "  PTS";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Valid_Name_InNode_With_DoubleSpace_InBack(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "PTS  ";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Valid_Name_InNode_With_TripleSpace_InBetween(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "PTS   India";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Valid_Name_InNode_With_SpecialCharacter_InFront(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "@$PTSIndia";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Valid_Name_InNode_With_SpecialCharacters_Only(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "!#@$%^#*$(><";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Valid_Name_InNode_With_Numbers_Only(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "1423487425";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Valid_Name_InNode_With_UnderScore_InFront(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "_PTS";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Valid_Name_InNode_With_UnderScore_InFront_AndBack(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "_PTS_";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Valid_Name_InNode_With_Numbers_InFront(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "456PTS";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Valid_Name_InNode_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "_456PTS_";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }


    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_Valid_Name_InNode_With_UnderScore_InFront_AndNumbers_InBack(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.Name = "_PTS677";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[0].ErrorMessage);
    }


    //SERVERNAME

    [Theory]
    [AutoNodeData]
    public async Task Verify_CreateNodeCommandValidator_ServerName_IsNull(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.ServerName = null;

        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createNodeCommand);

        Assert.Contains("'Server Name' must not be empty.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Node.SelectNodeServerNameNotNullRequired, validateResult.Errors[1].ErrorMessage);

    }

    [Theory]
    [AutoNodeData]
    public async Task Verify_Create_ServerName_Node_With_Empty(CreateNodeCommand createNodeCommand)
    {
        var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

        createNodeCommand.ServerName = "";
        createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

        var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Node.ServerNameValid, validateResult.Errors[1].ErrorMessage);
    }

    //PASSWORD

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_Password_InNode_Password_With_Empty(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.Password = "";
    //    createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.NodePasswordRequired, validateResult.Errors[2].ErrorMessage);
    //}

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_Password_InNode_Password_IsNull(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.Password = null;
    //    createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.NodePasswordNotNullRequired, validateResult.Errors[3].ErrorMessage);
    //}

    ////USERNAME

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_UserName_InNode_WithEmpty(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.UserName = "";
    //    createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.UserNameRequired, validateResult.Errors[2].ErrorMessage);
    //}

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_UserName_InNode_IsNull(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.UserName = null;
    //    createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.UserNameNotNullRequired, validateResult.Errors[3].ErrorMessage);
    //}

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_UserName_InNode_Range_Minimum_Validator(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.UserName = "AR";
    //    createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.UserNameRange, validateResult.Errors[2].ErrorMessage);
    //}

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_UserName_InNode_Range_Maximum_Validator(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.UserName = "PTASDKNGDSGJGNFJDJDFGNVNNVSEFSKWOJRWIYRWERWKFNKJWQRKNVSBGG";
    //    createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.UserNameRange, validateResult.Errors[2].ErrorMessage);
    //}

    //// ORACLE SID

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_OracleSID_InNode_WithEmpty(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.OracleSID = "";
    //    createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.OracleSidRequired, validateResult.Errors[2].ErrorMessage);
    //}

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_OracleSID_InNode_IsNull(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.OracleSID = null;
    //    createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.OracleSidNotNullRequired, validateResult.Errors[3].ErrorMessage);
    //}

    //// INSTANCE NAME

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_InstanceName_InNode_With_Empty(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.InstanceName = "";
    //    createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.InstanceNameRequired, validateResult.Errors[2].ErrorMessage);
    //}

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_InstanceName_InNode_IsNull(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.InstanceName = null;
    //    createNodeCommand.ServerId = "6b95fd33-2730-4da2-be00-743fe0662c7f";
    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.InstanceNameNotNullRequired, validateResult.Errors[3].ErrorMessage);
    //}

    //PORT

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_Port_InNode_With_Empty(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.Port = "";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.PortWithEmpty, validateResult.Errors[1].ErrorMessage);
    //}

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_Port_InNode_IsNull(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.Port = null;

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.PortNotNullRequired, validateResult.Errors[2].ErrorMessage);
    //}

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_Port_InNode_InvalidNumber(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.Port = "0000";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.PortNumberValidate, validateResult.Errors[1].ErrorMessage);
    //}

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_Port_InNode_MaximumRange(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.Port = "124158787894864";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.PortRange, validateResult.Errors[1].ErrorMessage);
    //}

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_Port_InNode_MinimumRange(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.Port = "15";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.PortRange, validateResult.Errors[1].ErrorMessage);
    //}
    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_Port_InNode_With_Character(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.Port = "abcd";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.PortNumberValidate, validateResult.Errors[1].ErrorMessage);
    //}

    //[Theory]
    //[AutoNodeData]
    //public async Task Verify_Create_Port_InNode_With_InvalidSign(CreateNodeCommand createNodeCommand)
    //{
    //    var validator = new CreateNodeCommandValidator(_mockNodeRepository.Object);

    //    createNodeCommand.Port = "-125";

    //    var validateResult = await validator.ValidateAsync(createNodeCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.Node.PortNumberValidate, validateResult.Errors[1].ErrorMessage);
    //}
}