using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DatalagImpactAvailabilityViewFixture : IDisposable
{
    public List<DatalagImpactAvailabilityView> DatalagImpactAvailabilityViewPaginationList { get; set; }
    public List<DatalagImpactAvailabilityView> DatalagImpactAvailabilityViewList { get; set; }
    public DatalagImpactAvailabilityView DatalagImpactAvailabilityViewDto { get; set; }

    public const string CompanyId = "f3fa2a13-24eb-4007-970f-7fd8b71cea23";
    public const string BusinessServiceId = "51f85c68-3488-4ceb-a8e0-220e2cb7e211";

    public ApplicationDbContext DbContext { get; private set; }

    public DatalagImpactAvailabilityViewFixture()
    {
        var fixture = new Fixture();

        DatalagImpactAvailabilityViewList = fixture.Create<List<DatalagImpactAvailabilityView>>();

        DatalagImpactAvailabilityViewPaginationList = fixture.CreateMany<DatalagImpactAvailabilityView>(20).ToList();

        DatalagImpactAvailabilityViewPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DatalagImpactAvailabilityViewPaginationList.ForEach(x => x.IsActive = true);
        DatalagImpactAvailabilityViewPaginationList.ForEach(x => x.ReferenceId = BusinessServiceId);

        DatalagImpactAvailabilityViewList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DatalagImpactAvailabilityViewList.ForEach(x => x.IsActive = true);
        DatalagImpactAvailabilityViewList.ForEach(x => x.ReferenceId = BusinessServiceId);

        DatalagImpactAvailabilityViewDto = fixture.Create<DatalagImpactAvailabilityView>();
        DatalagImpactAvailabilityViewDto.ReferenceId = Guid.NewGuid().ToString();
        DatalagImpactAvailabilityViewDto.IsActive = true;
        DatalagImpactAvailabilityViewDto.ReferenceId = BusinessServiceId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
