﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.GroupPolicy.Event.PaginatedView;

public class GroupPolicyPaginatedViewEventHandler : INotificationHandler<GroupPolicyPaginatedViewEvent>
{
    private readonly ILogger<GroupPolicyPaginatedViewEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public GroupPolicyPaginatedViewEventHandler(ILogger<GroupPolicyPaginatedViewEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(GroupPolicyPaginatedViewEvent paginatedViewEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.GroupPolicy.ToString(),
            Action = $"{ActivityType.View} {Modules.GroupPolicy}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Group Node Policy viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Group Node Policy viewed");
    }
}