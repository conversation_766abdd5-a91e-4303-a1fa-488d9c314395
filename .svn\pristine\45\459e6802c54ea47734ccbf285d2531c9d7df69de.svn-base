﻿using ContinuityPatrol.Application.Features.PluginManager.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.PluginManager.Queries;

public class GetPluginManagerNameUniqueQueryHandlerTests : IClassFixture<PluginManagerFixture>
{
    private readonly PluginManagerFixture _pluginManagerFixture;

    private Mock<IPluginManagerRepository> _mockPluginManagerRepository;

    private readonly GetPluginManagerNameUniqueQueryHandler _handler;

    public GetPluginManagerNameUniqueQueryHandlerTests(PluginManagerFixture pluginManagerFixture)
    {
        _pluginManagerFixture = pluginManagerFixture;

        _mockPluginManagerRepository = PluginManagerRepositoryMocks.GetPluginManagerNameUniqueRepository(_pluginManagerFixture.PluginManagers);

        _handler = new GetPluginManagerNameUniqueQueryHandler(_mockPluginManagerRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_PluginManagerName_Exist()
    {
        _pluginManagerFixture.PluginManagers[0].Name = "PluginManagers_Name";

        var result = await _handler.Handle(new GetPluginManagerNameUniqueQuery() { Name = _pluginManagerFixture.PluginManagers[0].Name, PluginId = _pluginManagerFixture.PluginManagers[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_PluginManagerNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetPluginManagerNameUniqueQuery { Name = "Demo_ERA", PluginId = _pluginManagerFixture.PluginManagers[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsPluginManagerNameExist_OneTime()
    {
        await _handler.Handle(new GetPluginManagerNameUniqueQuery(), CancellationToken.None);

        _mockPluginManagerRepository.Verify(x => x.IsPluginNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_False_PluginManagerName_NotMatch()
    {
        var result = await _handler.Handle(new GetPluginManagerNameUniqueQuery { Name = "Action_Test", PluginId = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockPluginManagerRepository = PluginManagerRepositoryMocks.GetPluginManagerEmptyRepository();

        var result = await _handler.Handle(new GetPluginManagerNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}