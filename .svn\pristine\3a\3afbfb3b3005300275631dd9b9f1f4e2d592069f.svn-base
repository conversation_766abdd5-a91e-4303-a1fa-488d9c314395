﻿using ContinuityPatrol.Application.Features.ManageWorkflow.Queries.GetManagedWorkflow;
using ContinuityPatrol.Application.Features.Workflow.Commands.Create;
using ContinuityPatrol.Application.Features.Workflow.Commands.Lock;
using ContinuityPatrol.Application.Features.Workflow.Commands.Publish;
using ContinuityPatrol.Application.Features.Workflow.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Workflow.Commands.Update;
using ContinuityPatrol.Application.Features.Workflow.Commands.Verify;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetCpslScript;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetDetailByActionName;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ManageWorkflow;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Orchestration;

public class WorkflowService : BaseClient,IWorkflowService
{
    public WorkflowService(IConfiguration config, IAppCache cache, ILogger<WorkflowService> logger)
        : base(config, cache, logger)
    {
       
    }

    public async Task<List<WorkflowNameVm>> GetWorkflowNames()
    {
        var request = new RestRequest("api/v6/workflow/names");

        return await GetFromCache<List<WorkflowNameVm>>(request, "GetWorkflowNames");
    }

    public async Task<List<WorkflowListVm>> GetWorkflowList()
    {
        var request = new RestRequest("api/v6/workflow");

        return await Get<List<WorkflowListVm>>(request);
    }

    public async Task<CreateWorkflowResponse> CreateAsync(CreateWorkflowCommand createWorkflow)
    {
        var request = new RestRequest("api/v6/workflow", Method.Post);

        request.AddJsonBody(createWorkflow);

        return await Post<CreateWorkflowResponse>(request);
    }

    public async Task<BaseResponse> UpdateWorkflowLock(UpdateWorkflowLockCommand workflowLock)
    {
        var request = new RestRequest("api/v6/workflow/lock", Method.Put);

        request.AddJsonBody(workflowLock);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateIsVerify(UpdateWorkflowVerifyCommand command)
    {
        var request = new RestRequest("api/v6/workflow/verify", Method.Put);

        request.AddJsonBody(command);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateWorkflowPublish(UpdateWorkflowPublishCommand workflowPublish)
    {
        var request = new RestRequest("api/v6/workflow/publish", Method.Put);

        request.AddJsonBody(workflowPublish);

        return await Put<BaseResponse>(request);
    }
    public async Task<UpdateWorkflowResponse> UpdateAsync(UpdateWorkflowCommand updateWorkflow)
    {
        var request = new RestRequest("api/v6/workflow", Method.Put);

        request.AddJsonBody(updateWorkflow);

        return await Put<UpdateWorkflowResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string workflowId)
    {
        var request = new RestRequest($"api/v6/workflow/{workflowId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<WorkflowDetailVm> GetByReferenceId(string workflowId)
    {
        var request = new RestRequest($"api/v6/workflow/{workflowId}");

        return await Get<WorkflowDetailVm>(request);
    }

    public async Task<bool> IsWorkflowNameExist(string workflowName, string? id)
    {
        var request = new RestRequest($"api/v6/workflow/name-exist?name={workflowName}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<List<GetWorkflowActionByIdVm>> GetWorkflowActionByWorkflowIdAndGroupId(string workflowId, string? workflowOperationGroupId)
    {
        var request = new RestRequest($"api/v6/workflow/workflowactiondetails?workflowId={workflowId}&workflowOperationGroupId={workflowOperationGroupId}");

        return await Get<List<GetWorkflowActionByIdVm>>(request);
    }

    public async Task<SaveAsWorkflowResponse> SaveAsWorkflow(SaveAsWorkflowCommand saveAsWorkflowCommand)
    {
        var request = new RestRequest("api/v6/workflow/save-as", Method.Post);

        request.AddJsonBody(saveAsWorkflowCommand);

        return await Post<SaveAsWorkflowResponse>(request);
    }

    public async Task<PaginatedResult<WorkflowListVm>> GetPaginatedWorkflow(GetWorkflowPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/workflow/paginated-list");

        return await Get<PaginatedResult<WorkflowListVm>>(request);
    }

    public async Task<GetDetailByActionNameVm> GetDetailByActionName(GetDetailByActionNameQuery query)
    {
        var request = new RestRequest($"api/v6/workflow/actionname?WorkflowId={query.WorkflowId}&ActionName={query.ActionName}");

        return await Get<GetDetailByActionNameVm>(request);
    }

    public Task<PaginatedResult<Manageworkflowlist>> GetManagedWorkflow(GetManagedWorkflowListQuery query)
    {
        throw new NotImplementedException();
    }

    public Task<List<ManageWorkflowModel>> GetManageWorkflows()
    {
        throw new NotImplementedException();
    }

    public async Task<GetCpslScriptDetailVm> GetCpslScript(GetCpslScriptDetailQuery query)
    {
        var request = new RestRequest($"api/v6/workflow/cpsl-converter{query}");

        return await Get<GetCpslScriptDetailVm>(request);
    }
}