﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowActionResultRepositoryMocks
{
    public static Mock<IWorkflowActionResultRepository> CreateWorkflowActionResultRepository(List<WorkflowActionResult> workflowActionResults)
    {
        var mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();

        mockWorkflowActionResultRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActionResults);

        mockWorkflowActionResultRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowActionResult>())).ReturnsAsync(
            (WorkflowActionResult workflowActionResult) =>
            {
                workflowActionResult.Id = new Fixture().Create<int>();

                workflowActionResult.ReferenceId = new Fixture().Create<Guid>().ToString();

                workflowActionResults.Add(workflowActionResult);

                return workflowActionResult;
            });

        return mockWorkflowActionResultRepository;
    }

    public static Mock<IWorkflowActionResultRepository> UpdateWorkflowActionResultRepository(List<WorkflowActionResult> workflowActionResults)
    {
        var mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();

        mockWorkflowActionResultRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActionResults);

        mockWorkflowActionResultRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowActionResults.SingleOrDefault(x => x.ReferenceId == i));

        mockWorkflowActionResultRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowActionResult>())).ReturnsAsync((WorkflowActionResult workflowActionResult) =>
        {
            var index = workflowActionResults.FindIndex(item => item.Id == workflowActionResult.Id);

            workflowActionResults[index] = workflowActionResult;

            return workflowActionResult;
        });

        return mockWorkflowActionResultRepository;
    }

    public static Mock<IWorkflowActionResultRepository> DeleteWorkflowActionResultRepository(List<WorkflowActionResult> workflowActionResults)
    {
        var mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();

        mockWorkflowActionResultRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActionResults);

        mockWorkflowActionResultRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowActionResults.SingleOrDefault(x => x.ReferenceId == i));

        mockWorkflowActionResultRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowActionResult>())).ReturnsAsync((WorkflowActionResult workflowActionResult) =>
        {
            var index = workflowActionResults.FindIndex(item => item.Id == workflowActionResult.Id);

            workflowActionResults[index] = workflowActionResult;

            return workflowActionResult;
        });

        return mockWorkflowActionResultRepository;
    }

    public static Mock<IWorkflowActionResultRepository> GetWorkflowActionResultRepository(List<WorkflowActionResult> workflowActionResults)
    {
        var mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();

        mockWorkflowActionResultRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActionResults);

        mockWorkflowActionResultRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowActionResults.SingleOrDefault(x => x.ReferenceId == i));

        return mockWorkflowActionResultRepository;
    }

    public static Mock<IWorkflowActionResultRepository> GetWorkflowActionResultNameRepository(List<WorkflowActionResult> workflowActionResults)
    {
        var mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();

        mockWorkflowActionResultRepository.Setup(repo => repo.GetWorkflowActionResultNames()).ReturnsAsync(workflowActionResults);

        return mockWorkflowActionResultRepository;
    }

    public static Mock<IWorkflowActionResultRepository> GetWorkflowActionResultEmptyRepository()
    {
        var mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();

        mockWorkflowActionResultRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowActionResult>());

        return mockWorkflowActionResultRepository;
    }

    public static Mock<IWorkflowActionResultRepository> GetPaginatedWorkflowActionResultRepository(List<WorkflowActionResult> workflowActionResults)
    {
        var mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();

        var queryableNode = workflowActionResults.BuildMock();

        mockWorkflowActionResultRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableNode);

        return mockWorkflowActionResultRepository;
    }

    public static Mock<IWorkflowActionResultRepository> GetWorkflowActionResultFromOperationGroupIdRepository(List<WorkflowActionResult> workflowActionResults)
    {
        var mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();

        mockWorkflowActionResultRepository.Setup(repo => repo.GetWorkflowActionResultByWorkflowOperationGroupId(It.IsAny<string>())).ReturnsAsync((string i) => workflowActionResults.Where(x => x.WorkflowOperationGroupId.Equals(i)).ToList());

        return mockWorkflowActionResultRepository;
    }

    public static Mock<IWorkflowActionResultRepository> GetWorkflowActionResultByWorkflowOperationId(List<WorkflowActionResult> workflowActionResults)
    {
        var mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();

        mockWorkflowActionResultRepository.Setup(repo => repo.GetWorkflowActionResultByWorkflowOperationId(It.IsAny<string>())).ReturnsAsync((string i) => workflowActionResults.Where(x => x.WorkflowOperationId.Equals(i)).ToList());

        return mockWorkflowActionResultRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateWorkflowActionResultEventRepository(List<UserActivity> userActivities)
    {
        var workflowActionResultEventRepository = new Mock<IUserActivityRepository>();
      
        workflowActionResultEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.LoginName = new Fixture().Create<string>();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return workflowActionResultEventRepository;
    }
}
