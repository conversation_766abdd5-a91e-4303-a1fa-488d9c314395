﻿using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Delete;
using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Update;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetByFormTypeId;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetFormTypeCategoryByName;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetList;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetNames;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class FormMappingService : BaseService, IFormMappingService
{
    public FormMappingService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateFormTypeCategoryCommand createCommand)
    {
        Logger.LogDebug($"Create FormTypeCategory '{createCommand.Name}'");

        return await Mediator.Send(createCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Form Type Category Id");

        Logger.LogDebug($"Delete Form Type Category Details by Id '{id}'");

        return await Mediator.Send(new DeleteFormTypeCategoryCommand { Id = id });
    }

    public async Task<FormTypeCategoryDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Form Type Category Id");

        Logger.LogDebug($"Get Form Type Category Detail by Id '{id}'");

        return await Mediator.Send(new GetFormTypeCategoryDetailQuery { Id = id });
    }

    public async Task<FormTypeCategoryByFormTypeIdVm> GetFormMappingByFormTypeId(string formTypeId, string? version)
    {
        Guard.Against.InvalidGuidOrEmpty(formTypeId, "Form Type Category Id");

        Logger.LogDebug($"Get Form Type Category List by Form TypeId '{formTypeId}' and Version '{version}'");

        return await Mediator.Send(new GetFormTypeCategoryByFormTypeIdQuery
            { FormTypeId = formTypeId, Version = version });
    }

    public async Task<List<FormTypeCategoryListVm>> GetFormMappingList()
    {
        Logger.LogDebug("Get All Form Type Category");

        return await Mediator.Send(new GetFormTypeCategoryListQuery());
    }

    public async Task<List<FormTypeCategoryNameVm>> GetFormMappingNames()
    {
        Logger.LogDebug("Get All Form Type Category Names");

        return await Mediator.Send(new GetFormTypeCategoryNameQuery());
    }

    public async Task<bool> IsFormMappingExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "FormType Name");

        Logger.LogDebug($"Check Name Exists Detail by FormType Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetFormTypeCategoryNameUniqueQuery { Name = name, Id = id });
    }

    public async Task<BaseResponse> UpdateAsync(UpdateFormTypeCategoryCommand updateCommand)
    {
        Logger.LogDebug($"Update FormTypeCategory '{updateCommand.Name}'");

        return await Mediator.Send(updateCommand);
    }

    public async Task<PaginatedResult<FormTypeCategoryListVm>> GetPaginatedFormTypeCategory(
        GetFormTypeCategoryPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in FormTypeCategory Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<List<FormTypeCategoryByNameVm>> GetFormMappingListByName(string name)
    {
        Guard.Against.NullOrWhiteSpace(name, "FormType Name");
        Logger.LogDebug($"");

        return await Mediator.Send(new GetFormTypeCategoryByNameQuery {Name = name});
    }
}