﻿using Moq;
using AutoMapper;
using Microsoft.Extensions.Logging;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using ContinuityPatrol.Application.Features.Company.Commands.Create;
using ContinuityPatrol.Application.Features.Company.Commands.Update;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Application.Features.Company.Queries.GetPaginatedList;
using Microsoft.AspNetCore.Mvc;
using MediatR;
using ContinuityPatrol.Application.Features.Company.Queries.GetDetail;
using AutoFixture;
using ContinuityPatrol.Shared.Core.Wrapper;
using Microsoft.AspNetCore.Http;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Services.Helper;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class CompanyControllerShould
    {
        private readonly Mock<IMapper> _mockMapper =new();
        private readonly Mock<ILogger<CompanyController>> _mockLogger = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private CompanyController _controller;

        private readonly Mock<IHttpContextAccessor> _mockHttpContextAccessor;
        private readonly Mock<HttpContext> _mockHttpContext;
        private readonly Mock<ISession> _mockSession;

        public CompanyControllerShould()
        {

            _mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            _mockHttpContext = new Mock<HttpContext>();
            _mockSession = new Mock<ISession>();
            _mockHttpContext.Setup(c => c.Session).Returns(_mockSession.Object);
            _mockHttpContextAccessor.Setup(a => a.HttpContext).Returns(_mockHttpContext.Object);
           

            Initialize();
        }
        internal void Initialize()
        {
            _controller = new CompanyController(
                _mockMapper.Object,
                _mockLogger.Object,
                _mockPublisher.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
            
        }

        [Fact]
        public async Task List_ShouldPublishCompanyPaginatedEvent()
        {
            var result = await _controller.List() as ViewResult;

            Assert.NotNull(result);
        }

        //[Fact]
        //public async Task CreateOrUpdate_ShouldCreateCompany_WhenIdIsNull()
        //{
            
            
        //    var viewModel = new Fixture().Create<CompanyViewModel>();
        //    var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        //    dic.Add("id", "22");
        //    var collection = new FormCollection(dic);
        //    _controller.Request.Form = collection;
            
        //    var command = new CreateCompanyCommand();
        //    _mockMapper.Setup(m => m.Map<CreateCompanyCommand>(It.IsAny<CompanyViewModel>())).Returns(command);
        //    _mockDataProvider.Setup(dp => dp.Company.CreateAsync(command)).ReturnsAsync(new BaseResponse { Message = "Company created successfully." });
        //    _mockDataProvider.Setup(dp => dp.Company.UpdateAsync(It.IsAny<UpdateCompanyCommand>())).ReturnsAsync(new BaseResponse { Message = "Company Updated successfully." });

            


        //    var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            
        //    Assert.NotNull(result);
        //    Assert.Equal("List", result.ActionName);
            
        //}

        //[Fact]
        //public async Task CreateOrUpdate_ShouldUpdateCompany_WhenIdIsNotNull()
        //{
        //    WebHelper.CurrentSession.Set("CompanyProfiles", CompanyFakes.GetCompanyListItems());
        //    var model = new CompanyViewModel { Id = "123", Name = "Updated Company", CompanyLogo = "" };
        //    var command = new UpdateCompanyCommand();
        //    _mockMapper.Setup(m => m.Map<UpdateCompanyCommand>(It.IsAny<CompanyViewModel>())).Returns(command);
        //    _mockDataProvider.Setup(dp => dp.Company.UpdateAsync(command)).ReturnsAsync(new BaseResponse { Message = "Company updated successfully." });

            
        //    var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            
        //    Assert.NotNull(result);
        //    Assert.Equal("List", result.ActionName);
            
        //}

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleValidationException()
        {
            var model = new CompanyViewModel();

            // Adjusting the expected type to JsonResult since CreateOrUpdate returns JsonResult
            var result = await _controller.CreateOrUpdate(model) as JsonResult;

            Assert.NotNull(result);
            Assert.Equal("List", result.Value); // Assuming the JsonResult contains a value indicating the action name
        }

        [Fact]
        public async Task Delete_ShouldDeleteCompany()
        {
            
            var id = "123";
            _mockDataProvider.Setup(dp => dp.Company.DeleteAsync(id)).ReturnsAsync(new BaseResponse { Message = "Company deleted successfully." });

            
            var result = await _controller.Delete(id) as JsonResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.Value);
           
        }

        //[Fact]
        //public async Task IsCompanyNameExist_ShouldReturnTrue_WhenCompanyNameExists()
        //{
            
        //    var companyName = "Existing Company";
        //    var id = "123";
        //    _mockDataProvider.Setup(dp => dp.Company.IsCompanyNameExist(companyName, id)).ReturnsAsync(true);
            
        //    var result = await _controller.IsCompanyNameExist(companyName, id) as JsonResult; ;

        //    Assert.True(result);
        //}

        //[Fact]
        //public async Task IsCompanyDisplayNameExist_ShouldReturnTrue_WhenDisplayNameExists()
        //{

        //    var displayName = "Existing Display Name";
        //    var id = "123";
        //    _mockDataProvider.Setup(dp => dp.Company.IsCompanyDisplayNameExist(displayName, id)).ReturnsAsync(true);

        //    var result = await _controller.IsCompanyDisplayNameExist(displayName, id);

        //    Assert.True(result);
        //}

        [Fact]
        public async Task GetCompanies_ShouldReturnJson()
        {
            
            var companies = new List<CompanyNameVm> ();
            _mockDataProvider.Setup(dp => dp.Company.GetCompanyNamesOnLogin()).ReturnsAsync(companies);

            var result = await _controller.GetCompanies() as JsonResult;
            Assert.NotNull(result);
            Assert.Equal(companies, result.Value);
        }

        [Fact]
        public async Task GetPagination_ShouldReturnPaginatedList()
        {
            
            var query = new GetCompanyPaginatedListQuery { 
                 PageNumber = 1,
                 PageSize = 10,
                 SearchString = string.Empty
            };
            var paginatedList = new PaginatedResult<CompanyListVm>();
            _mockDataProvider.Setup(dp => dp.Company.GetPaginatedCompanies(query)).ReturnsAsync(paginatedList);

            
            var result = await _controller.GetPagination(query) as JsonResult;

            
            Assert.NotNull(result);
            Assert.Equal(paginatedList, result.Value);
        }

        [Fact]
        public async Task GetCompanyById_ShouldReturnCompany()
        {
            
            var id = "123";
            var company = new CompanyDetailVm ();
            _mockDataProvider.Setup(dp => dp.Company.GetCompanyById(id)).ReturnsAsync(company);

            
            var result = await _controller.GetCompanyById(id) as JsonResult;

            
            Assert.NotNull(result);
            Assert.Equal(company, result.Value);
        }
    }
}
