using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DynamicSubDashboardRepositoryTests : IClassFixture<DynamicSubDashboardFixture>
{
    private readonly DynamicSubDashboardFixture _dynamicSubDashboardFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DynamicSubDashboardRepository _repository;
    private readonly DynamicSubDashboardRepository _repositoryNotParent;

    public DynamicSubDashboardRepositoryTests(DynamicSubDashboardFixture dynamicSubDashboardFixture)
    {
        _dynamicSubDashboardFixture = dynamicSubDashboardFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DynamicSubDashboardRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DynamicSubDashboardRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var dynamicSubDashboard = _dynamicSubDashboardFixture.DynamicSubDashboardDto;

        // Act
        var result = await _repository.AddAsync(dynamicSubDashboard);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dynamicSubDashboard.DynamicDashBoardName, result.DynamicDashBoardName);
        Assert.Equal(dynamicSubDashboard.DynamicDashBoardId, result.DynamicDashBoardId);
        Assert.Single(_dbContext.DynamicSubDashboards);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var dynamicSubDashboard = _dynamicSubDashboardFixture.DynamicSubDashboardDto;
        await _repository.AddAsync(dynamicSubDashboard);

        dynamicSubDashboard.DynamicDashBoardName = "UpdatedSubDashboardName";

        // Act
        var result = await _repository.UpdateAsync(dynamicSubDashboard);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedSubDashboardName", result.DynamicDashBoardName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var dynamicSubDashboard = _dynamicSubDashboardFixture.DynamicSubDashboardDto;
        await _repository.AddAsync(dynamicSubDashboard);

        // Act
        var result = await _repository.DeleteAsync(dynamicSubDashboard);

        // Assert
        Assert.Equal(dynamicSubDashboard.DynamicDashBoardName, result.DynamicDashBoardName);
        Assert.Empty(_dbContext.DynamicSubDashboards);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var dynamicSubDashboard = _dynamicSubDashboardFixture.DynamicSubDashboardDto;
        var addedEntity = await _repository.AddAsync(dynamicSubDashboard);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.DynamicDashBoardName, result.DynamicDashBoardName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var dynamicSubDashboard = _dynamicSubDashboardFixture.DynamicSubDashboardDto;
        var addedEntity = await _repositoryNotParent.AddAsync(dynamicSubDashboard);

        // Act
        var result = await _repositoryNotParent.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dynamicSubDashboard = _dynamicSubDashboardFixture.DynamicSubDashboardDto;
        await _repository.AddAsync(dynamicSubDashboard);

        // Act
        var result = await _repository.GetByReferenceIdAsync(dynamicSubDashboard.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dynamicSubDashboard.ReferenceId, result.ReferenceId);
        Assert.Equal(dynamicSubDashboard.DynamicDashBoardName, result.DynamicDashBoardName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var dynamicSubDashboards = _dynamicSubDashboardFixture.DynamicSubDashboardList;
        await _repository.AddRangeAsync(dynamicSubDashboards);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dynamicSubDashboards.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var dynamicSubDashboards = _dynamicSubDashboardFixture.DynamicSubDashboardList;
        await _repositoryNotParent.AddRangeAsync(dynamicSubDashboards);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetByDashboardId Tests

    [Fact]
    public async Task GetByDashboardId_ShouldReturnEntitiesWithMatchingDashboardId()
    {
        // Arrange
        var dynamicSubDashboards = _dynamicSubDashboardFixture.DynamicSubDashboardList;

        dynamicSubDashboards[0].DynamicDashBoardId = "DASHBOARD_123";
        dynamicSubDashboards[1].DynamicDashBoardId = "DASHBOARD_123";
        await _repository.AddRangeAsync(dynamicSubDashboards);

        // Act
        var result = await _repository.GetByDashboardIdAsync(DynamicSubDashboardFixture.DashboardId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(DynamicSubDashboardFixture.DashboardId, x.DynamicDashBoardId));
    }

    [Fact]
    public async Task GetByDashboardId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var dynamicSubDashboards = _dynamicSubDashboardFixture.DynamicSubDashboardList;
       
        await _repository.AddRangeAsync(dynamicSubDashboards);

        // Act
        var result = await _repository.GetByDashboardIdAsync("non-existent-dashboard-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByDashboardId_ShouldNotReturnInactiveEntities()
    {
        // Arrange
        var dynamicSubDashboards = _dynamicSubDashboardFixture.DynamicSubDashboardList;
        await _repository.AddRangeAsync(dynamicSubDashboards);

        // Act
        var result = await _repository.GetByDashboardIdAsync("DASHBOARD_123");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should exclude the inactive one
      
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var dynamicSubDashboard = _dynamicSubDashboardFixture.DynamicSubDashboardDto;
        dynamicSubDashboard.Name= "ExistingSubDashboardName";
        await _repository.AddAsync(dynamicSubDashboard);

        // Act
        var result = await _repository.IsNameExist("ExistingSubDashboardName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Act
        var result = await _repository.IsNameExist("NonExistentSubDashboardName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var dynamicSubDashboard = _dynamicSubDashboardFixture.DynamicSubDashboardDto;
        await _repository.AddAsync(dynamicSubDashboard);

        // Act
        var result = await _repository.IsNameExist("SameSubDashboardName", dynamicSubDashboard.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        var dynamicSubDashboard1 = _dynamicSubDashboardFixture.DynamicSubDashboardList;

        dynamicSubDashboard1[0].Name = "ExistingSubDashboardName";         
        await _repository.AddRangeAsync(dynamicSubDashboard1);


        // Act
        var result = await _repository.IsNameExist("ExistingSubDashboardName", dynamicSubDashboard1[0].ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var dynamicSubDashboards = _dynamicSubDashboardFixture.DynamicSubDashboardList;

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(dynamicSubDashboards);
        var initialCount = dynamicSubDashboards.Count;
        
        var toUpdate = dynamicSubDashboards.Take(2).ToList();
        toUpdate.ForEach(x => x.DynamicDashBoardName = "UpdatedSubDashboardName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = dynamicSubDashboards.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.DynamicDashBoardName.Equals("UpdatedSubDashboardName")).ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
