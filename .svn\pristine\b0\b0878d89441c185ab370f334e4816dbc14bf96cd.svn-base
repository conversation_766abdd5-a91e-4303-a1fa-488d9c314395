using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetByOperationIdAndOperationGroupId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportActionResult.Queries;

public class GetByOperationIdAndOperationGroupIdQueryTests : IClassFixture<BulkImportActionResultFixture>
{
    private readonly BulkImportActionResultFixture _bulkImportActionResultFixture;
    private readonly Mock<IBulkImportActionResultRepository> _mockBulkImportActionResultRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetByOperationIdAndOperationGroupIdQueryHandler _handler;

    public GetByOperationIdAndOperationGroupIdQueryTests(BulkImportActionResultFixture bulkImportActionResultFixture)
    {
        _bulkImportActionResultFixture = bulkImportActionResultFixture;

        _mockBulkImportActionResultRepository = BulkImportActionResultRepositoryMocks.CreateQueryBulkImportActionResultRepository(_bulkImportActionResultFixture.BulkImportActionResults);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<List<BulkImportActionResultListVm>>(It.IsAny<List<Domain.Entities.BulkImportActionResult>>()))
            .Returns((List<Domain.Entities.BulkImportActionResult> entities) => entities.Select(entity => new BulkImportActionResultListVm
            {
                Id = entity.ReferenceId,
                CompanyId = entity.CompanyId,
                NodeId = entity.NodeId,
                NodeName = entity.NodeName,
                BulkImportOperationId = entity.BulkImportOperationId,
                BulkImportOperationGroupId = entity.BulkImportOperationGroupId,
                EntityId = entity.EntityId,
                EntityName = entity.EntityName,
                EntityType = entity.EntityType,
                Status = entity.Status,
                StartTime = entity.StartTime,
                EndTime = entity.EndTime,
                ErrorMessage = entity.ErrorMessage
            }).ToList());

        _handler = new GetByOperationIdAndOperationGroupIdQueryHandler(
            _mockMapper.Object,
            _mockBulkImportActionResultRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_BulkImportActionResultListVm_When_ResultsExist()
    {
        // Arrange
        var testOperationId = "TestOperationId";
        var testGroupId = "TestGroupId";
        var query = new GetByOperationIdAndOperationGroupIdQuery 
        { 
            BulkImportOperationId = testOperationId,
            BulkImportOperationGroupId = testGroupId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportActionResultListVm>));
        result.Count.ShouldBeGreaterThanOrEqualTo(0);
    }

    [Fact]
    public async Task Handle_Call_GetByOperationIdAndOperationGroupId_OnlyOnce()
    {
        // Arrange
        var testOperationId = "TestOperationId";
        var testGroupId = "TestGroupId";
        var query = new GetByOperationIdAndOperationGroupIdQuery 
        { 
            BulkImportOperationId = testOperationId,
            BulkImportOperationGroupId = testGroupId
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.GetByOperationIdAndOperationGroupId(testOperationId, testGroupId), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_When_ResultsExist()
    {
        // Arrange
        var testOperationId = "TestOperationId";
        var testGroupId = "TestGroupId";
        var query = new GetByOperationIdAndOperationGroupIdQuery 
        { 
            BulkImportOperationId = testOperationId,
            BulkImportOperationGroupId = testGroupId
        };

        var testResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult 
            { 
                ReferenceId = "1", 
                BulkImportOperationId = testOperationId,
                BulkImportOperationGroupId = testGroupId
            }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(testOperationId, testGroupId))
            .ReturnsAsync(testResults);

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<List<BulkImportActionResultListVm>>(It.IsAny<List<Domain.Entities.BulkImportActionResult>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoResultsExist()
    {
        // Arrange
        var testOperationId = "NonExistentOperationId";
        var testGroupId = "NonExistentGroupId";
        var query = new GetByOperationIdAndOperationGroupIdQuery 
        { 
            BulkImportOperationId = testOperationId,
            BulkImportOperationGroupId = testGroupId
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(testOperationId, testGroupId))
            .ReturnsAsync(new List<Domain.Entities.BulkImportActionResult>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportActionResultListVm>));
        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_FilterByOperationIdAndGroupId_When_QueryExecuted()
    {
        // Arrange
        var testOperationId = "SpecificOperationId";
        var testGroupId = "SpecificGroupId";
        var query = new GetByOperationIdAndOperationGroupIdQuery 
        { 
            BulkImportOperationId = testOperationId,
            BulkImportOperationGroupId = testGroupId
        };

        var filteredResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult 
            { 
                ReferenceId = "1", 
                BulkImportOperationId = testOperationId,
                BulkImportOperationGroupId = testGroupId,
                EntityName = "FilteredEntity"
            }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(testOperationId, testGroupId))
            .ReturnsAsync(filteredResults);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1);
        result.First().Id.ShouldBe("1");
        result.First().BulkImportOperationId.ShouldBe(testOperationId);
        result.First().BulkImportOperationGroupId.ShouldBe(testGroupId);
        result.First().EntityName.ShouldBe("FilteredEntity");
    }

    [Fact]
    public async Task Handle_MapEntitiesToViewModels_WithCorrectProperties()
    {
        // Arrange
        var testOperationId = "TestOperationId";
        var testGroupId = "TestGroupId";
        var testResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult 
            { 
                ReferenceId = "1",
                CompanyId = "TestCompanyId",
                NodeId = "Node001",
                NodeName = "TestNode",
                BulkImportOperationId = testOperationId,
                BulkImportOperationGroupId = testGroupId,
                EntityId = "TestEntityId",
                EntityName = "TestEntity",
                EntityType = "Server",
                Status = "Pending",
                StartTime = DateTime.Now.AddHours(-1),
                EndTime = DateTime.Now,
                ErrorMessage = ""
            }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(testOperationId, testGroupId))
            .ReturnsAsync(testResults);

        var query = new GetByOperationIdAndOperationGroupIdQuery 
        { 
            BulkImportOperationId = testOperationId,
            BulkImportOperationGroupId = testGroupId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1);
        
        var firstItem = result.First();
        firstItem.Id.ShouldBe("1");
        firstItem.CompanyId.ShouldBe("TestCompanyId");
        firstItem.NodeId.ShouldBe("Node001");
        firstItem.NodeName.ShouldBe("TestNode");
        firstItem.BulkImportOperationId.ShouldBe(testOperationId);
        firstItem.BulkImportOperationGroupId.ShouldBe(testGroupId);
        firstItem.EntityId.ShouldBe("TestEntityId");
        firstItem.EntityName.ShouldBe("TestEntity");
        firstItem.EntityType.ShouldBe("Server");
        firstItem.Status.ShouldBe("Pending");
        firstItem.ErrorMessage.ShouldBe("");
    }

    [Fact]
    public async Task Handle_ReturnCorrectListType_When_MappingSuccessful()
    {
        // Arrange
        var testOperationId = "TestOperationId";
        var testGroupId = "TestGroupId";
        var query = new GetByOperationIdAndOperationGroupIdQuery 
        { 
            BulkImportOperationId = testOperationId,
            BulkImportOperationGroupId = testGroupId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<List<BulkImportActionResultListVm>>();
        result.GetType().ShouldBe(typeof(List<BulkImportActionResultListVm>));
    }

    [Fact]
    public async Task Handle_NotCallMapper_When_NoDataExists()
    {
        // Arrange
        var testOperationId = "EmptyOperationId";
        var testGroupId = "EmptyGroupId";
        var query = new GetByOperationIdAndOperationGroupIdQuery 
        { 
            BulkImportOperationId = testOperationId,
            BulkImportOperationGroupId = testGroupId
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(testOperationId, testGroupId))
            .ReturnsAsync(new List<Domain.Entities.BulkImportActionResult>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(0);
        _mockMapper.Verify(x => x.Map<List<BulkImportActionResultListVm>>(It.IsAny<List<Domain.Entities.BulkImportActionResult>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_PassCorrectParameters_When_CallingRepository()
    {
        // Arrange
        var operationId = "SpecificOperationId";
        var groupId = "SpecificGroupId";
        var query = new GetByOperationIdAndOperationGroupIdQuery 
        { 
            BulkImportOperationId = operationId,
            BulkImportOperationGroupId = groupId
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.GetByOperationIdAndOperationGroupId(operationId, groupId), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleMultipleResults_When_RepositoryReturnsMultipleItems()
    {
        // Arrange
        var testOperationId = "TestOperationId";
        var testGroupId = "TestGroupId";
        var multipleResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult { ReferenceId = "1", EntityName = "Entity1", Status = "Pending" },
            new Domain.Entities.BulkImportActionResult { ReferenceId = "2", EntityName = "Entity2", Status = "Running" },
            new Domain.Entities.BulkImportActionResult { ReferenceId = "3", EntityName = "Entity3", Status = "Completed" }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(testOperationId, testGroupId))
            .ReturnsAsync(multipleResults);

        var query = new GetByOperationIdAndOperationGroupIdQuery 
        { 
            BulkImportOperationId = testOperationId,
            BulkImportOperationGroupId = testGroupId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(3);
        result.Select(x => x.Status).ShouldContain("Pending");
        result.Select(x => x.Status).ShouldContain("Running");
        result.Select(x => x.Status).ShouldContain("Completed");
    }

    [Fact]
    public async Task Handle_ReturnEmptyListDirectly_When_CountIsZero()
    {
        // Arrange
        var testOperationId = "EmptyOperationId";
        var testGroupId = "EmptyGroupId";
        var query = new GetByOperationIdAndOperationGroupIdQuery 
        { 
            BulkImportOperationId = testOperationId,
            BulkImportOperationGroupId = testGroupId
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(testOperationId, testGroupId))
            .ReturnsAsync(new List<Domain.Entities.BulkImportActionResult>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportActionResultListVm>));
        result.Count.ShouldBe(0);
        result.ShouldBeEmpty();
    }
}
