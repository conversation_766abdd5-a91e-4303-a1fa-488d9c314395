﻿using ContinuityPatrol.Domain.ViewModels.UserActivityModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;

namespace ContinuityPatrol.Application.Features.User.Queries.GetUserProfile;

public class GetUserProfileDetailQueryHandler : IRequestHandler<GetUserProfileDetailQuery, UserProfileDetailVm>
{
    private readonly IAccessManagerRepository _accessManagerRepository;
    private readonly IMapper _mapper;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly IUserInfoRepository _userInfoRepository;
    private readonly IUserRepository _userRepository;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IWorkflowRepository _workflowRepository;

    public GetUserProfileDetailQueryHandler(IMapper mapper, IUserRepository userRepository,
        IWorkflowRepository workflowRepository, IWorkflowOperationGroupRepository workflowOperationGroupRepository,
        IAccessManagerRepository accessManagerRepository, IUserActivityRepository userActivityRepository,
        IUserInfoRepository userInfoRepository)
    {
        _mapper = mapper;
        _userRepository = userRepository;
        _workflowRepository = workflowRepository;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
        _accessManagerRepository = accessManagerRepository;
        _userActivityRepository = userActivityRepository;
        _userInfoRepository = userInfoRepository;
    }

    public async Task<UserProfileDetailVm> Handle(GetUserProfileDetailQuery request,
        CancellationToken cancellationToken)
    {
        var userDetail = await _userRepository.GetByReferenceIdAsync(request.UserId);

        Guard.Against.NullOrDeactive(userDetail, nameof(Domain.Entities.User),
            new NotFoundException(nameof(Domain.Entities.User), request.UserId));

        var userMap = _mapper.Map<UserProfileDetailVm>(userDetail);

        var userInfo = await _userInfoRepository.GetUserInfoByUserIdAsync(request.UserId);

        userMap.Email = userInfo?.Email ?? string.Empty;

        userMap.Mobile = userInfo?.Mobile ?? string.Empty;

        userMap.LogoName = userInfo?.LogoName ?? string.Empty;

        var accessManager = await _accessManagerRepository.GetAccessManagerByRoleId(userDetail.Role);

        userMap.AccessProperties = accessManager?.Properties ?? string.Empty;

        var userActivityLoginNames = await _userActivityRepository.GetloginUserActivityByLoginName(userDetail.LoginName);

        var workflowList = (await _workflowRepository.GetWorkflowNames()).Where(x =>
            x.CreatedBy.IsNotNullOrWhiteSpace() && x.CreatedBy.Equals(userDetail.ReferenceId)).ToList();

        var workflowOperationGroupList = (await _workflowOperationGroupRepository.ListAllAsync())
            .Where(x => x.CreatedBy.Equals(userDetail.ReferenceId)).ToList();

        userMap.UserActivityList = _mapper.Map<List<UserActivityListVm>>(userActivityLoginNames);
        userMap.WorkflowList = _mapper.Map<List<WorkflowListVm>>(workflowList);
        userMap.WorkflowOperationGroupList =
            _mapper.Map<List<WorkflowOperationGroupListVm>>(workflowOperationGroupList);

        return userMap;
    }
}