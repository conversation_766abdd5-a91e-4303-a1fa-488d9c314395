﻿const nameExistUrl = "Admin/GroupNodePolicy/IsGroupPolicyNameExist";
let createPermission = $("#adminCreate").data("create-permission").toLowerCase();
let deletePermission = $("#adminDelete").data("delete-permission").toLowerCase();
if (createPermission == 'false') {
    $("#createGrpPolicy").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
}

$(function () {
    btnCrudEnable('save'); 
    btnCrudEnable('confirmDeleteButton'); 
    $(document).on("mouseover", ".select2-selection__choice__remove", function () {       
        $('.select2-selection__choice__remove').attr('title', 'Remove Node');
    });   
    $('#DeleteModal').attr('data-bs-backdrop', 'static');   

    //pagination
    let selectedValues = [];
    let dataTable = $('#groupPolicy').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow"></i>'
                },
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": false,
            "filter": true,
            "ajax": {
                "type": "GET",
                "url": "/Admin/GroupNodePolicy/GetPagination",
                "dataType": "json",
                "data": function (d) {

                    d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                    d.pageSize = d?.length;
                    d.searchString = selectedValues?.length === 0 ? $('#search-inp').val() : selectedValues?.join(';');
                    selectedValues.length = 0;
                },
                "error": function (xhr, err) {
                    if (xhr.status === 401) {
                        window.location.assign('/Account/Logout');
                    }
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1, 3],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {

                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false,
                },
                {
                    "data": "groupName", "name": "Policy Name", "autoWidth": true,
                    "render": function (data, type, row) {

                        return '<span title="' + data + '">' + data + '</span>';;
                    }
                },
                {
                    "data": "type", "name": "type", "autoWidth": true,
                    "render": function (data, type, row) {

                        return data;
                    }
                },
                {
                    "data": "properties",
                    "name": "Nodes",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            try {
                                let properties = JSON.parse(data);
                                let propertyHtml = '';
                                for (let j = 0; j < properties.length; j++) {
                                    let node = properties[j];
                                    propertyHtml += '<span>' + node.label + '</span>';
                                    if (j < properties.length - 1) {
                                        propertyHtml += ', ';
                                    }
                                }
                                return propertyHtml;
                            } catch (error) {

                                return null;
                            }
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (createPermission == "true" && deletePermission == "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">                               
                           
                                            <span  role="button" title="Edit"  class="edit-button" data-grouppolicy='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>                                   
                                            <span role="button" title="Delete" class="delete-button" data-grouppolicy-id="${row.id}" data-grouppolicy-name="${row.groupName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                 
                        </div>`;
                        }
                        else if (createPermission == "true" && deletePermission == "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">                              
                           
                                            <span  role="button" title="Edit"  class="edit-button" data-grouppolicy='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                   
                                            <span role="button" title="Delete" class="icon-disabled">
                                                <i class="cp-Delete"></i>
                                            </span>
                                 
                        </div>`;
                        }
                        else if (createPermission == "false" && deletePermission == "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">                               
                           
                                            <span  role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                   
                                            <span role="button" title="Delete" class="delete-button" data-grouppolicy-id="${row.id}" data-grouppolicy-name="${row.groupName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                 
                        </div>`;
                        }
                        else if (createPermission == "false" && deletePermission == "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">                               
                           
                                            <span  role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                   
                                            <span role="button" title="Delete" class="icon-disabled">
                                                <i class="cp-Delete"></i>
                                            </span>
                                 
                        </div>`;
                        }
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
                $(".SrNo_th").removeClass("sorting_asc");
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
        $(".SrNo_th").removeClass("sorting_asc");
    });
   
    //search

    $('.form-select-sm').select2({
        "language": {
            "noResults": function () {
                return "No Results Found";
            }
        },
    });

    $('#search-inp').on('keypress input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const NameValue = $('#groupNodeName')        
        const PropertiesValue = $('#Properties')
        const ServiceTypeValue = $('#ServiceType')
        const inputValue = $('#search-inp').val();
        if (NameValue.is(':checked')) {
            selectedValues.push(NameValue.val() + inputValue);
        } 
        if (ServiceTypeValue.is(':checked')) {
            selectedValues.push(ServiceTypeValue.val() + inputValue);
        } 
        if (PropertiesValue.is(':checked')) {
            selectedValues.push(PropertiesValue.val() + inputValue);
        }       
        let currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {
                
                if (json  && json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }
    },500)); 
   
    //delete
    $("#groupPolicy").on('click', '.delete-button', function () {
        let groupId = $(this).data('grouppolicy-id');
        let groupName = $(this).data('grouppolicy-name');
        $('#deleteData').text(groupName);
        $('#textDeleteId').val(groupId);
    });

    $('#confirmDeleteButton').on('click', function () {
        btnCrudDiasable('confirmDeleteButton');
    });
    //Create
    $("#grpNodeBtnsave").on('click', async function (e) {
        e.preventDefault();
        let $btn = $(this);
        let name = $("#groupNodeName").val();
        let groupPolicyId = $("#groupPolicyId").val();
        let nodeValue = $("#cpNodeName").val();
        let typeValue = $('#nodeTypeId').val();
        let errorElementNode = $("#Node-error");
        let errorElementType = $("#Type-error");
        let isName = await validateName(name, groupPolicyId, nameExistUrl);
        let isType = await validateDropDown(typeValue, 'Select service type', errorElementType);
        let isNode = await validateDropDown(nodeValue, 'Select CP nodes', errorElementNode);
        if (isName && isType && isNode) {
            $btn.prop('disabled', true);
            btnCrudDiasable('save');
            let node = JSON.stringify(nodeProperties);
            $("#nodeId").val(node);

            let formData = {
                GroupName: name,
                Id: groupPolicyId,
                Properties: node,
                Type: typeValue,
                __RequestVerificationToken: gettoken()
            };
            $.ajax({
                type: "POST",
                url: RootUrl + 'Admin/GroupNodePolicy/CreateOrUpdate',
                data: formData,
                success: function (response) {
                    if (response?.success && response?.data) {
                        $("#CreateModal").modal("hide");
                        notificationAlert("success", response.data);
                        setTimeout(() => {
                            window.location.reload();
                        }, 500);
                    } else {
                        errorNotification(response);
                    }
                },                
                complete: function () {
                    $btn.prop('disabled', false);
                    btnCrudEnable('save');
                }
            });
        }
    });

    //Update
    $('#groupPolicy').on('click', '.edit-button', function () {
        let groupPolicyData = $(this).data('grouppolicy');
        populateModalFields(groupPolicyData);
        $('#grpNodeBtnsave').text('Update');      
        $('#CreateModal').modal('show');
    });       
   
    //edit
    function populateModalFields(groupPolicyData) {
        $('#cpNodeType').val(groupPolicyData.type)
        $('#groupNodeName').val(groupPolicyData.groupName);        
        $('#groupPolicyId').val(groupPolicyData.id);        
        try {
            const errorElement = ['#groupNodeName-error'];
            errorElement.forEach(element => {
                $(element).text('').removeClass('field-validation-error')
            });            
            $(`#cpNodeType`).trigger('change');            
            let jsonString = groupPolicyData.properties;
            let data = JSON.parse(jsonString);          
            if (!Array.isArray(data)) {
                throw new Error('Invalid JSON format: Not an array');
            }
            let dropdown = $("#cpNodeName");           
            dropdown.select2();
            let selectedValues = data.map(function (item) {
                return item.id;
            });           
            dropdown.val(selectedValues).trigger('change');             
        }
        catch (error) {
            console.log(error);           
        }        
    }
    //Validation
    $('#groupNodeName').on('keyup', commonDebounce(async function () {
        let groupPolicyId = $('#groupPolicyId').val();
        const value = $(this).val();
        if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }
        await validateName(value, groupPolicyId, nameExistUrl);
    },400));

    //Name
    async function validateName(value, id = null, url) {
        const errorElement = $('#groupNodeName-error');
        if (!value) {
            errorElement.text('Enter group node policy name')
                .addClass('field-validation-error');
            return false;
        }
        let url1 = RootUrl + url;
        let data = {};
        data.groupPolicyName = value;
        data.id = id;
        const validationResults = [
            await SpecialCharValidate(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await OnlyNumericsValidate(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await ShouldNotBeginWithNumber(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsNameExist(url1, data, OnError)
        ];
        return await CommonValidation(errorElement, validationResults);
    };

    //GetLoadBalancerList
    let nodeArray = [];
    const getLoadData = async () => {
        await $.ajax({
            type: "GET",
            url: RootUrl + 'Admin/GroupNodePolicy/GetLoadBalancerList',
            //data: data,
            dataType: "json",
            traditional: true,
            success: function (result) {                
                nodeArray = result.filter((d) => d?.typeCategory === 'CP Node' || d?.typeCategory === 'CPNode')
            }
        })
    }
    getLoadData()

    $(`#cpNodeType`).on('change', function () {
        $('#cpNodeName').empty()
        let value = $(`#cpNodeType`).find(':selected').val()  
        let text = $(`#cpNodeType`).find(':selected').text()
        $('#nodeTypeId').val(value)
       
        let filterd = nodeArray.filter((d) => d?.type === value || d?.type === text)       
        let html = '';        
        if (filterd.length > 0) {
            filterd.forEach((obj, idx) => {
                html += `<option value="${obj?.id}" data-NId="${obj?.name}">${obj?.name}</option>`;

            })
            
        } else {
            $("#cpNodeName").append($('<option>', {
                value: "No Data Found",
                text: "No Data Found",                
                disabled:true
            }));
            return false;
        }     
        $('#cpNodeName').append(html)
        let errorElementNode = $("#Type-error");
        validateDropDown(value, 'Select service type', errorElementNode);
    })

    //multi dropdown value
    let nodeProperties = []
    $('#cpNodeName').on('change', function () {
        let selectedOptions = $(this).find('option:selected');      
        const value = $('#cpNodeName').val();        
        let tempArray = [];
        selectedOptions.each(function () {
            let option = $(this);
            let value = option.val();
            let getName = option.data('nid');
            let obj = { label: getName, id: value };
            tempArray.push(obj);            
        });
        
        nodeProperties = tempArray;         
        let errorElementNode = $("#Node-error");
        validateDropDown(value, 'Select CP nodes', errorElementNode);
        
    });
    function validateDropDown(value, errorMessage, errorElement) {
        if (!value || value.length === 0) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
    
    //Clear data
    $('#createGrpPolicy').on('click', function () {
        const errorElements = ['#groupNodeName-error', '#Node-error','#Type-error'];
        clearInputFields('Create', errorElements);         
        $('#groupPolicyId, #groupNodeName, #cpNodeType').val(''); 
        $('#cpNodeName').empty();   
        $("#grpNodeBtnsave").prop('disabled', false);
        $('#grpNodeBtnsave').text('Save');
        $('#grpNodeBtnsave');
    });

    //name exist
    async function IsNameExist(url, data, errorFunc) {
        return !data.groupPolicyName.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
    }
});
