﻿namespace ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Commands.Delete;

public class DeleteBusinessServiceEvaluationCommandHandler : IRequestHandler<DeleteBusinessServiceEvaluationCommand,
    DeleteBusinessServiceEvaluationResponse>
{
    private readonly IBusinessServiceEvaluationRepository _businessServiceEvaluationRepository;

    public DeleteBusinessServiceEvaluationCommandHandler(
        IBusinessServiceEvaluationRepository businessServiceEvaluationRepository)
    {
        _businessServiceEvaluationRepository = businessServiceEvaluationRepository;
    }

    public async Task<DeleteBusinessServiceEvaluationResponse> Handle(DeleteBusinessServiceEvaluationCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _businessServiceEvaluationRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(eventToDelete, nameof(Domain.Entities.BusinessServiceEvaluation),
            new NotFoundException(nameof(Domain.Entities.BusinessServiceEvaluation), request.Id));

        eventToDelete.IsActive = false;

        await _businessServiceEvaluationRepository.UpdateAsync(eventToDelete);

        var response = new DeleteBusinessServiceEvaluationResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.BusinessServiceEvaluation), eventToDelete.ReferenceId),

            IsActive = eventToDelete.IsActive
        };

        return response;
    }
}