﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class FormFilterSpecification : Specification<Form>
{
    public FormFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "", StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("type=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Type.Contains(stringItem.Replace("type=", "", StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("properties=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Properties.Contains(stringItem.Replace("properties=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("version=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Version.Contains(stringItem.Replace("version=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.Name.Contains(searchString) || p.Type.Contains(searchString) ||
                    p.Properties.Contains(searchString) || p.Version.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.Name != null;
        }
    }
}