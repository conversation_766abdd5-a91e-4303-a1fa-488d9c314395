﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class UserLoginRepositoryMocks
{
    public static Mock<IUserLoginRepository> UpdatePasswordRepository(List<UserLogin> userLogins)
    {
        var updatePasswordRepository = new Mock<IUserLoginRepository>();

        updatePasswordRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userLogins);

        updatePasswordRepository.Setup(repo => repo.GetUserLoginByUserId(It.IsAny<string>())).ReturnsAsync((string i) => userLogins.SingleOrDefault(x => x.UserId == i));

        updatePasswordRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>())).ReturnsAsync((UserLogin userLogin) =>
        {
            var index = userLogins.FindIndex(item => item.UserId == userLogin.UserId);

            userLogins[index] = userLogin;

            return userLogin;
        });

        return updatePasswordRepository;
    }

    public static Mock<IUserLoginRepository> CreateUserLoginRepository(List<UserLogin> userLogins)
    {
        var userLoginRepository = new Mock<IUserLoginRepository>();

        userLoginRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userLogins);

        userLoginRepository.Setup(repo => repo.AddAsync(It.IsAny<UserLogin>())).ReturnsAsync((UserLogin userLogin) =>
        {
            userLogin.Id = new Fixture().Create<int>();

            userLogin.ReferenceId = new Fixture().Create<Guid>().ToString();

            userLogins.Add(userLogin);

            return userLogin;
        });

        return userLoginRepository;
    }

    public static Mock<IUserLoginRepository> UpdateUserLoginRepository(List<UserLogin> userLogins)
    {
        var userLoginRepository = new Mock<IUserLoginRepository>();

        userLoginRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userLogins);

        userLoginRepository.Setup(repo => repo.GetUserLoginByUserId(It.IsAny<string>())).ReturnsAsync((string i) => userLogins.SingleOrDefault(x => x.ReferenceId == i));

        userLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>())).ReturnsAsync((UserLogin userLogin) =>
        {

            var index = userLogins.FindIndex(item => item.Id == userLogin.Id);

            userLogins[index] = userLogin;

            return userLogin;
        });

        return userLoginRepository;
    }

    public static Mock<IUserLoginRepository> DeleteUserLoginRepository(List<UserLogin> userLogins)
    {
        var userLoginRepository = new Mock<IUserLoginRepository>();

        userLoginRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userLogins);

        userLoginRepository.Setup(repo => repo.GetUserLoginByUserId(It.IsAny<string>())).ReturnsAsync((string i) => userLogins.SingleOrDefault(x => x.ReferenceId == i));

        userLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>())).ReturnsAsync((UserLogin userLogin) =>
        {
            var index = userLogins.FindIndex(item => item.Id == userLogin.Id);

            userLogin.IsActive = false;

            userLogins[index] = userLogin;

            return userLogin;
        });

        return userLoginRepository;
    }

    public static Mock<IUserLoginRepository> GetUserLoginRepository(List<UserLogin> userLogins)
    {
        var userLoginRepository = new Mock<IUserLoginRepository>();

        userLoginRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userLogins);

        userLoginRepository.Setup(repo => repo.GetUserLoginByUserId(It.IsAny<string>())).ReturnsAsync((string i) => userLogins.SingleOrDefault(x => x.ReferenceId == i));

        return userLoginRepository;
    }

    public static Mock<IUserLoginRepository> GetUserLoginEmptyRepository()
    {
        var mockUserLoginRepository = new Mock<IUserLoginRepository>();

        mockUserLoginRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<UserLogin>());

        return mockUserLoginRepository;
    }

    public static Mock<IUserLoginRepository> GetUserLoginNamesRepository(List<UserLogin> userLogins)
    {
        var mockUserLoginRepository = new Mock<IUserLoginRepository>();

        mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(It.IsAny<string>())).ReturnsAsync((string i) => userLogins.SingleOrDefault(x => x.ReferenceId == i));

        return mockUserLoginRepository;
    }

    public static Mock<IUserLoginRepository> GetPaginatedUserLoginRepository(List<UserLogin> userLogins)
    {
        var mockUserLoginRepository = new Mock<IUserLoginRepository>();

        var queryableUserLogin = userLogins.BuildMock();

        mockUserLoginRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableUserLogin);

        return mockUserLoginRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateUserLoginEventRepository(List<UserActivity> userActivities)
    {
        var userLoginEventRepository = new Mock<IUserActivityRepository>();

        userLoginEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return userLoginEventRepository;
    }

    public static Mock<IUserLoginRepository> GetUserLoginByUserIdRepository(List<UserLogin> userLogins)
    {
        var userLoginByUserIdRepository = new Mock<IUserLoginRepository>();

        userLoginByUserIdRepository.Setup(repo => repo.GetUserLoginByUserId(It.IsAny<string>())).ReturnsAsync((string i) => userLogins.FirstOrDefault(x => x.UserId == i));

        return userLoginByUserIdRepository;
    }
}