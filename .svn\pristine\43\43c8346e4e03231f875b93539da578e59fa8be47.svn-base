﻿using ContinuityPatrol.Application.Features.WorkflowExecutionTemp.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowExecutionTemp.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowExecutionTemp.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.WorkflowExecutionTempModel;

namespace ContinuityPatrol.Application.Mappings;

public class WorkflowExecutionTempProfile : Profile
{
    public WorkflowExecutionTempProfile()
    {
        CreateMap<WorkflowExecutionTemp, CreateWorkflowExecutionTempCommand>().ReverseMap();
        CreateMap<UpdateWorkflowExecutionTempCommand, WorkflowExecutionTemp>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<WorkflowExecutionTemp, WorkflowExecutionTempListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowExecutionTemp, WorkflowExecutionTempDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}