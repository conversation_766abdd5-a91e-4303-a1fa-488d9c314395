﻿@using Microsoft.AspNetCore.Mvc.TagHelpers;
@using ContinuityPatrol.Domain.Entities;
@model ContinuityPatrol.Domain.ViewModels.TableAccessModel.TableAccessModel;
@Html.AntiForgeryToken()

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title">
                <i class="cp-database-type"></i><span>Table Access</span>
            </h6>
            <form class="d-flex gap-2">
                <div class="input-group w-auto">
                    <i class="cp-search"> </i>
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                </div>
                <div class="tablecheck form-switch m-2">
                    <label class="form-check-label me-1 mt-1" for="flexSwitchCheckChecked">Select All</label>
                    <input class="form-check-input ms-1" type="checkbox" role="switch" id="flexSwitchCheckChecked">
                </div>
                <button type="button" id="tableAccessSaveButton" class="btn btn-primary btn-sm disabled">
                    Save
                </button>
                <button type="button" id="tableAccessCancelButton" class="btn btn-secondary btn-sm disabled">
                    Cancel
                </button>
            </form>
        </div>
        <div class=" text-center" id="data_notfound">
            <img src="../../img/isomatric/no_data_found.svg" style="padding:10px">
            <p>No data found</p>
        </div>
        <div class="card-body card_ScrollBody pt-0" id="tableaccess_body">
            
            <form action="" class="custom-checkbox" id="CreateForm">
                @{
                    // Sort the PaginatedTableAccess array alphabetically by TableName
                    // var sortedTableAccessList = Model.PaginatedTableAccess.OrderBy(t => t.TableName).ToList();
                }
              @*   <div class="g-2 row row-cols-xl-5 row-cols-4" id="">

                    @foreach (var tableAccess in sortedTableAccessList)

                    {
                        var IsChecked = @tableAccess.IsChecked ? "checked" : "";
                        <div class="col tablecheck">
                            <div class="d-flex p-2 rounded-3 justify-content-between align-items-center" style="background: var(--bs-primary-bg-subtle)">
                                <span title="@tableAccess.TableName" class="custom-control-label w-75 mb-0 d-flex gap-2 custom-cursor-default-hover align-items-center">
                                    <span>
                                        <i class="cp-table fs-5"></i>
                                    </span>
                                    <span class="tablecheck_name text-truncate d-inline-block mw-75">@tableAccess.TableName</span>
                                </span>
                                <div class="form-switch">
                                    <input id="@tableAccess.Id" title="@tableAccess.TableName" type="checkbox" class="form-check-input isTableChecked" data-tableName="@tableAccess.TableName" data-schemaName="@tableAccess.SchemaName" @IsChecked>
                                </div>
                            </div>
                        </div>
                    }
                </div> *@

                
            </form>
        </div>
        <div id="AdminCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.CreateAndEdit" aria-hidden="true"></div>
        <script type="text/javascript">
            var RootUrl = '@Url.Content("~/")';
        </script>
        <script src="~/js/Admin/Dataset/TableAccess/TableAccess.js"></script>
