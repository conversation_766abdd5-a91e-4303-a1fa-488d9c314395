using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BiaRules.Events.Update;

public class BiaRulesUpdatedEventHandler : INotificationHandler<BiaRulesUpdatedEvent>
{
    private readonly ILogger<BiaRulesUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BiaRulesUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<BiaRulesUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(BiaRulesUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.BiaRules}",
            Entity = Modules.BiaRules.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"BiaRules '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"BiaRules '{updatedEvent.Name}' updated successfully.");
    }
}