﻿using ContinuityPatrol.Application.Features.SiteLocation.Commands.Update;
using ContinuityPatrol.Application.Features.SiteLocation.Events.Update;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SiteLocation.Commands
{
    public class UpdateSiteLocationTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<ISiteLocationRepository> _mockSiteLocationRepository;
        private readonly UpdateSiteLocationCommandHandler _handler;

        public UpdateSiteLocationTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockPublisher = new Mock<IPublisher>();
            _mockSiteLocationRepository = new Mock<ISiteLocationRepository>();

            _handler = new UpdateSiteLocationCommandHandler(
                _mockMapper.Object,
                _mockSiteLocationRepository.Object,
                _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_ShouldUpdateSiteLocation_WhenValidRequest()
        {
            var command = new UpdateSiteLocationCommand { Id = Guid.NewGuid().ToString(), City = "UpdatedCity" };

            var siteLocation = new Domain.Entities.SiteLocation
            {
                ReferenceId = command.Id,
                City = "OldCity",
                IsDelete = false
            };

            _mockSiteLocationRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(siteLocation);

            _mockMapper
                .Setup(m => m.Map(It.IsAny<UpdateSiteLocationCommand>(), It.IsAny<Domain.Entities.SiteLocation>()))
                .Verifiable();

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("UpdatedCity", result.Message);
            Assert.Equal(command.Id, result.Id);

            _mockSiteLocationRepository.Verify(repo => repo.UpdateAsync(It.Is<Domain.Entities.SiteLocation>(sl => sl.City == "UpdatedCity")), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<SiteLocationUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenSiteLocationDoesNotExist()
        {
            var command = new UpdateSiteLocationCommand { Id = Guid.NewGuid().ToString(), City = "UpdatedCity" };

            _mockSiteLocationRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync((Domain.Entities.SiteLocation)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));

            _mockSiteLocationRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.SiteLocation>()), Times.Never);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<SiteLocationUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldUpdateSiteLocation_WhenItIsDeletedFlagIsTrue()
        {
            var command = new UpdateSiteLocationCommand { Id = Guid.NewGuid().ToString(), City = "UpdatedCity" };

            var siteLocation = new Domain.Entities.SiteLocation
            {
                ReferenceId = command.Id,
                City = "OldCity",
                IsDelete = true
            };

            _mockSiteLocationRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(siteLocation);

            _mockMapper
                .Setup(m => m.Map(It.IsAny<UpdateSiteLocationCommand>(), It.IsAny<Domain.Entities.SiteLocation>()))
                .Verifiable();

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("UpdatedCity", result.Message);
            Assert.Equal(command.Id, result.Id);

            _mockSiteLocationRepository.Verify(repo => repo.UpdateAsync(It.Is<Domain.Entities.SiteLocation>(sl => sl.City == "UpdatedCity")), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<SiteLocationUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldNotUpdate_WhenSiteLocationIsAlreadyUpdated()
        {
            var command = new UpdateSiteLocationCommand { Id = Guid.NewGuid().ToString(), City = "UpdatedCity" };

            var siteLocation = new Domain.Entities.SiteLocation
            {
                ReferenceId = command.Id,
                City = "UpdatedCity",
                IsDelete = false
            };

            _mockSiteLocationRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(siteLocation);

            _mockMapper
                .Setup(m => m.Map(It.IsAny<UpdateSiteLocationCommand>(), It.IsAny<Domain.Entities.SiteLocation>()))
                .Verifiable();

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(" SiteUpdatedCity", result.Message);
            Assert.Equal(command.Id, result.Id);

            _mockSiteLocationRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.SiteLocation>()), Times.Never);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<SiteLocationUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}
