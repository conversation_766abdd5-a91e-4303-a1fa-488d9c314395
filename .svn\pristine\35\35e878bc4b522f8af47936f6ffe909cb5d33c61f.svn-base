﻿using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Infrastructure.Contract;
using System.Reflection;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace ContinuityPatrol.Application.UnitTests.Features.Replication.Validators;

public class CreateReplicationValidatorTests
{
    private readonly Mock<IReplicationRepository> _mockReplicationRepository;
    private readonly Mock<ISiteRepository> _mockSiteRepository;
    private readonly Mock<ISiteTypeRepository> _mockSiteTypeRepository;
    private readonly Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;
    private readonly Mock<ILicenseValidationService> _mockLicenseValidationService;
    private readonly Mock<IComponentTypeRepository> _mockComponentTypeRepository;
    private readonly CreateReplicationCommand _createReplicationCommand;
    private readonly CreateReplicationCommandValidator _validator;

    public CreateReplicationValidatorTests()
    {
        var sites = new Fixture().Create<List<Domain.Entities.Site>>();
        var replications = new Fixture().Create<List<Domain.Entities.Replication>>();
        var licenseManagers = new Fixture().Create<List<Domain.Entities.LicenseManager>>();

        _mockLicenseValidationService = new Mock<ILicenseValidationService>();
        _mockSiteRepository = SiteRepositoryMocks.CreateSiteRepository(sites);
        _mockReplicationRepository = ReplicationRepositoryMocks.CreateReplicationRepository(replications);
        _mockLicenseManagerRepository = LicenseManagerRepositoryMocks.UpdateBaseLicenseRepository(licenseManagers);
        _mockSiteTypeRepository = new Mock<ISiteTypeRepository>();
        _mockComponentTypeRepository = new Mock<IComponentTypeRepository>();

        _validator = new CreateReplicationCommandValidator(_mockReplicationRepository.Object, _mockSiteRepository.Object, _mockLicenseValidationService.Object, _mockLicenseManagerRepository.Object, _mockSiteTypeRepository.Object, _mockComponentTypeRepository.Object);
        var db = new AutoFixture.Fixture().Create<CreateReplicationCommand>();
        var prefixes = new List<string> { "Name", "Type", "TypeIdId", "CompanyId", "SiteId", "SiteName", "Logo", "Properties", "LicenseId", "LicenseKey", "BusinessServiceId", "BusinessServiceName", "FormVersion","Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        _createReplicationCommand = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        _createReplicationCommand = RemovePrefixesFromObject(_createReplicationCommand, pattern, jsonPrefix);
    }

    //Name

    static T RemovePrefixesFromObject<T>(T obj, string prefixPattern, string jsonPrefix)
    {

        var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                                      .Where(p => p.PropertyType == typeof(string));

        foreach (var property in properties)
        {
            var currentValue = (string)property.GetValue(obj);
            if (currentValue != null)
            {
                if (property.Name == jsonPrefix)
                {
                    // Convert to JSON if property name matches the specific prefix
                    var jsonValue = JsonSerializer.Serialize(new { Properties = currentValue });
                    property.SetValue(obj, jsonValue);
                }
                else
                {
                    // Remove prefixes for other properties
                    var newValue = Regex.Replace(currentValue, prefixPattern, "");
                    property.SetValue(obj, newValue);
                }
            }
        }
        return obj;
    }


    [Fact]
    public async Task Verify_Create_Name_InReplication_WithEmpty()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationNameValidator, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Name_InReplication_IsNull()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = null;
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationNameNotNullRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Name_InReplication_MinimumRange()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "IB";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Name_InReplication_MaximumRange()
    {
        _createReplicationCommand.Name = "ZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZZYXWVUTSRQPONMLKJIHGFEDCBA_ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";
        var sites = new Domain.Entities.Site { 
           Name= _createReplicationCommand.SiteName
        };
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Valid_Name_InReplication()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "   PTSIndia   ";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Valid_Name_InReplication_With_DoubleSpace_InFront()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "  PTS  India";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Valid_Name_InReplication_With_DoubleSpace_InBack()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "PTS  India  ";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Valid_Name_InReplication_With_TripleSpace_InBetween()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "PTS Technosoft   India";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Valid_Name_InReplication_With_SpecialCharacters_InFront()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "%#PTSTechnosoft India";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Valid_Name_InReplication_With_SpecialCharacters_InBetween()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "PTSTechnosoft @#$%India";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Valid_Name_InReplication_With_SpecialCharacters_Only()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "!@#$$^*%(><";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Valid_Name_InReplication_With_UnderScore_InFront()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "_PTS";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Valid_Name_InReplication_With_UnderScore_InFront_AndBack()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "_PTS_";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Valid_Name_InReplication_With_Numbers_InFront()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "234PTS";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Valid_Name_InReplication_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "_234PTS_";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Valid_Name_InReplication_With_UnderScore_InFront_AndNumbers_InBack()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "_PTS876";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Valid_Name_InReplication_With_Numbers_Only()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Name = "**********";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }



    [Fact]
    public async Task Verify_Create_Type_InReplication_WithEmpty()
    {
        _createReplicationCommand.Type = "";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";
        var sites = new Domain.Entities.Site
        {
            Name = _createReplicationCommand.SiteName,
            Type = _createReplicationCommand.Type,
            TypeId = _createReplicationCommand.TypeId,
            ReferenceId = _createReplicationCommand.SiteId
        };
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationOperationalServiceRequired, (string)validateResult.Errors[6].ErrorMessage);
    }

    //[Fact]
    //public async Task Verify_Create_Type_InReplication_IsNull()
    //{
    //    _createReplicationCommand.Type = null;
    //    _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";
    //    _createReplicationCommand.LicenseId = "a5cdb95b-2235-4ea2-92e7-cab758055717";
    //    _createReplicationCommand.TypeId = "ff251f20-40ad-41f7-b4e7-b752d170d3ac";
    //    var sites = new Domain.Entities.Site
    //    {
    //        Name = _createReplicationCommand.SiteName,
    //        Type = _createReplicationCommand.Type,
    //        TypeId = _createReplicationCommand.TypeId,
    //        ReferenceId = _createReplicationCommand.SiteId
    //    };
    //    _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
    //    var siteType = new Domain.Entities.SiteType
    //    {
    //        ReferenceId = "954d2220-5b06-43c8-bc34-63f73ee1c661",
    //    };
    //    _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
    //    int Index = 0;
    //    _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
    //    var license = new Domain.Entities.LicenseManager();
    //    _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
    //    int replicationCount = 0;
    //    _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(replicationCount);
    //    bool dbLicenseCount = true;
    //    _mockLicenseValidationService.Setup(dp => dp.IsReplicationLicenseCountExitMaxLimit(license, siteType, replicationCount, Index)).ReturnsAsync(dbLicenseCount);
    //    var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
    //    var error = validateResult.Errors.FirstOrDefault(e => e.PropertyName == nameof(CreateReplicationCommand.Type));
    //    Assert.NotNull(error);
    //    Assert.Equal("Select Type.", error.ErrorMessage);
    //}

    //SiteName

    [Fact]
    public async Task Verify_Create_SiteName_InReplication_WithEmpty()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.SiteName = "";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_SiteName_InReplication_IsNull()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.SiteName = null;
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameNotNullRequired, (string)validateResult.Errors[3].ErrorMessage);
    }

    //Properties

    [Fact]
    public async Task Verify_Create_Properties_InReplication_WithEmpty()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Properties = "";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationPropertiesRequired, (string)validateResult.Errors[7].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_Properties_InReplication_IsNull()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.Properties = null;
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationPropertiesNotNullRequired, (string)validateResult.Errors[8].ErrorMessage);
    }

    //LicenseKey

    [Fact]
    public async Task Verify_Create_LicenseKey_InReplication_WithEmpty()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.LicenseKey = "";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationLicenseCount, (string)validateResult.Errors[6].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Create_LicenseKey_InReplication_IsNull()
    {
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _createReplicationCommand.LicenseKey = null;
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationSiteNameValidator, (string)validateResult.Errors[2].ErrorMessage);
    }


    //ReplicationCountValidation

    [Fact]
    public async Task Verify_Create_LicenseKey_InReplication_IsNull_Exit_Maximum_Limit()
    {
        _createReplicationCommand.LicenseKey = "PONumberb5f266b0-66f5-49e8-a1d8-c44765588d93";
        _createReplicationCommand.SiteId = "3a8d0315-6f3b-4d2f-b1f3-b12a4a324481";
        var sites = new Domain.Entities.Site
        {
            Name = _createReplicationCommand.SiteName,
            Type = _createReplicationCommand.Type,
            TypeId = _createReplicationCommand.TypeId,
            ReferenceId = _createReplicationCommand.SiteId
        };
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(_createReplicationCommand.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_createReplicationCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockReplicationRepository.Setup(dp => dp.GetReplicationCountByLicenseKey(_createReplicationCommand.LicenseId, _createReplicationCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _mockLicenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
       

        var validateResult = await _validator.ValidateAsync(_createReplicationCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Replication.ReplicationLicenseCount, (string)validateResult.Errors[4].ErrorMessage);
    }
}