﻿using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PostgresMonitorLogsModel;

namespace ContinuityPatrol.Application.UnitTests.Features.PostgresMonitorLogs.Queries
{
    public class GetPostgresMonitorLogsPaginatedListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPostgresMonitorLogsRepository> _mockPostgresMonitorLogsRepository;
        private readonly GetPostgresMonitorLogsPaginatedListQueryHandler _handler;

        public GetPostgresMonitorLogsPaginatedListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockPostgresMonitorLogsRepository = new Mock<IPostgresMonitorLogsRepository>();
            _handler = new GetPostgresMonitorLogsPaginatedListQueryHandler(_mockPostgresMonitorLogsRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedResult_WhenLogsExist()
        {
            var query = new GetPostgresMonitorLogsPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = string.Empty
            };

            var logsFromRepo = new List<Domain.Entities.PostgresMonitorLogs>
            {
                new Domain.Entities.PostgresMonitorLogs { ReferenceId = Guid.NewGuid().ToString(), Type = "Active" },
                new Domain.Entities.PostgresMonitorLogs { ReferenceId = Guid.NewGuid().ToString(), Type = "InActive" },
                new Domain.Entities.PostgresMonitorLogs { ReferenceId = Guid.NewGuid().ToString(), ConfiguredRPO = "75" }
            }.AsQueryable();

            var mappedLogs = new List<PostgresMonitorLogsListVm>
            {
                new PostgresMonitorLogsListVm { Id = Guid.NewGuid().ToString(), ConfiguredRPO = "85" },
                new PostgresMonitorLogsListVm { Id = Guid.NewGuid().ToString(), ConfiguredRPO = "65" }
            };

            _mockPostgresMonitorLogsRepository.Setup(r => r.GetPaginatedQuery())
                .Returns(logsFromRepo);

            _mockMapper.Setup(m => m.Map<PostgresMonitorLogsListVm>(It.IsAny<Domain.Entities.PostgresMonitorLogs>()))
                .Returns((Domain.Entities.PostgresMonitorLogs source) =>
                    new PostgresMonitorLogsListVm { Id = source.ReferenceId, ConfiguredRPO = source.Type });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal("Log1", result.Data.First().Id);

            _mockPostgresMonitorLogsRepository.Verify(r => r.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(m => m.Map<PostgresMonitorLogsListVm>(It.IsAny<Domain.Entities.PostgresMonitorLogs>()), Times.Exactly(2));
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyPaginatedResult_WhenNoLogsExist()
        {
            var query = new GetPostgresMonitorLogsPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = string.Empty
            };

            _mockPostgresMonitorLogsRepository.Setup(r => r.GetPaginatedQuery())
                .Returns(Enumerable.Empty<Domain.Entities.PostgresMonitorLogs>().AsQueryable());

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);

            _mockPostgresMonitorLogsRepository.Verify(r => r.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(m => m.Map<PostgresMonitorLogsListVm>(It.IsAny<Domain.Entities.PostgresMonitorLogs>()), Times.Never);
        }
    }
}
