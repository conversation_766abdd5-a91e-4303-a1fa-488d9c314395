using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberComponentGroupModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CyberComponentGroupControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CyberComponentGroupsController _controller;
    private readonly CyberComponentGroupFixture _cyberComponentGroupFixture;

    public CyberComponentGroupControllerTests()
    {
        _cyberComponentGroupFixture = new CyberComponentGroupFixture();

        var testBuilder = new ControllerTestBuilder<CyberComponentGroupsController>();
        _controller = testBuilder.CreateController(
            _ => new CyberComponentGroupsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetCyberComponentGroups_ReturnsExpectedList()
    {
        // Arrange
        var expectedCyberComponentGroups = new List<CyberComponentGroupListVm>
        {
            _cyberComponentGroupFixture.CyberComponentGroupListVm,
            _cyberComponentGroupFixture.CyberComponentGroupListVm,
            _cyberComponentGroupFixture.CyberComponentGroupListVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberComponentGroupListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCyberComponentGroups);

        // Act
        var result = await _controller.GetCyberComponentGroups();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberComponentGroups = Assert.IsAssignableFrom<List<CyberComponentGroupListVm>>(okResult.Value);
        Assert.Equal(3, cyberComponentGroups.Count);
    }

    [Fact]
    public async Task GetCyberComponentGroupById_ReturnsExpectedDetail()
    {
        // Arrange
        var cyberComponentGroupId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentGroupDetailQuery>(q => q.Id == cyberComponentGroupId), default))
            .ReturnsAsync(_cyberComponentGroupFixture.CyberComponentGroupDetailVm);

        // Act
        var result = await _controller.GetCyberComponentGroupById(cyberComponentGroupId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberComponentGroup = Assert.IsType<CyberComponentGroupDetailVm>(okResult.Value);
        Assert.Equal(_cyberComponentGroupFixture.CyberComponentGroupDetailVm.GroupName, cyberComponentGroup.GroupName);
    }

    [Fact]
    public async Task GetPaginatedCyberComponentGroups_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetCyberComponentGroupPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = new List<CyberComponentGroupListVm>
        {
            _cyberComponentGroupFixture.CyberComponentGroupListVm,
            _cyberComponentGroupFixture.CyberComponentGroupListVm
        };
        var expectedResults = PaginatedResult<CyberComponentGroupListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentGroupPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberComponentGroups(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberComponentGroupListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task CreateCyberComponentGroup_ReturnsCreatedAtAction()
    {
        // Arrange
        var command = _cyberComponentGroupFixture.CreateCyberComponentGroupCommand;
        var expectedMessage = "CyberComponentGroup has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberComponentGroupResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberComponentGroup(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberComponentGroupResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberComponentGroup_ReturnsOk()
    {
        // Arrange
        var command = _cyberComponentGroupFixture.UpdateCyberComponentGroupCommand;
        var expectedMessage = "CyberComponentGroup has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberComponentGroupResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberComponentGroup(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberComponentGroupResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteCyberComponentGroup_ReturnsOk()
    {
        // Arrange
        var cyberComponentGroupId = Guid.NewGuid().ToString();
        var expectedMessage = "CyberComponentGroup has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCyberComponentGroupCommand>(c => c.Id == cyberComponentGroupId), default))
            .ReturnsAsync(new DeleteCyberComponentGroupResponse
            {
                Message = expectedMessage,
                IsActive = false
            });

        // Act
        var result = await _controller.DeleteCyberComponentGroup(cyberComponentGroupId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteCyberComponentGroupResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public async Task IsCyberComponentGroupNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var groupName = "Existing Component Group";
        var groupId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentGroupNameUniqueQuery>(q => 
                q.Name == groupName && q.Id == groupId), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsCyberComponentGroupNameExist(groupName, groupId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.True(exists);
    }

    [Fact]
    public async Task IsCyberComponentGroupNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var groupName = "Unique Component Group Name";
        var groupId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentGroupNameUniqueQuery>(q => 
                q.Name == groupName && q.Id == groupId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsCyberComponentGroupNameExist(groupName, groupId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }

    [Fact]
    public async Task GetCyberComponentGroups_HandlesEmptyList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberComponentGroupListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<CyberComponentGroupListVm>());

        // Act
        var result = await _controller.GetCyberComponentGroups();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberComponentGroups = Assert.IsAssignableFrom<List<CyberComponentGroupListVm>>(okResult.Value);
        Assert.Empty(cyberComponentGroups);
    }

    [Fact]
    public async Task GetCyberComponentGroupById_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetCyberComponentGroupById(invalidId));
    }

    [Fact]
    public async Task GetCyberComponentGroupById_HandlesNotFound()
    {
        // Arrange
        var cyberComponentGroupId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentGroupDetailQuery>(q => q.Id == cyberComponentGroupId), default))
            .ThrowsAsync(new NotFoundException("CyberComponentGroup", cyberComponentGroupId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetCyberComponentGroupById(cyberComponentGroupId));
    }

    [Fact]
    public async Task DeleteCyberComponentGroup_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteCyberComponentGroup(invalidId));
    }

    [Fact]
    public async Task CreateCyberComponentGroup_HandlesEnterpriseGroup()
    {
        // Arrange
        var command = new CreateCyberComponentGroupCommand
        {
            GroupName = "Enterprise Mission-Critical Infrastructure Group",
            ComponentProperties = "{\"enterpriseGroup\":{\"classification\":\"Mission-Critical\",\"businessImpact\":\"Severe\",\"rto\":\"2 hours\",\"rpo\":\"5 minutes\",\"components\":[{\"id\":\"ent-web-tier\",\"name\":\"Enterprise Web Tier\",\"type\":\"Web Server Farm\",\"instances\":8,\"loadBalancer\":\"F5 BIG-IP\",\"specifications\":{\"cpu\":\"16 cores per instance\",\"memory\":\"64GB per instance\",\"storage\":\"1TB NVMe per instance\"},\"highAvailability\":{\"clustering\":\"active-active\",\"healthChecks\":\"enabled\",\"autoScaling\":\"enabled\"}},{\"id\":\"ent-app-tier\",\"name\":\"Enterprise Application Tier\",\"type\":\"Application Server Cluster\",\"instances\":12,\"middleware\":\"WebLogic\",\"specifications\":{\"cpu\":\"32 cores per instance\",\"memory\":\"128GB per instance\",\"storage\":\"2TB NVMe per instance\"},\"performance\":{\"connectionPooling\":\"optimized\",\"caching\":\"distributed\",\"monitoring\":\"APM enabled\"}},{\"id\":\"ent-db-tier\",\"name\":\"Enterprise Database Tier\",\"type\":\"Database Cluster\",\"instances\":4,\"database\":\"Oracle RAC\",\"specifications\":{\"cpu\":\"64 cores per instance\",\"memory\":\"512GB per instance\",\"storage\":\"20TB Enterprise SAN per instance\"},\"dataProtection\":{\"backup\":\"RMAN\",\"replication\":\"Data Guard\",\"encryption\":\"TDE\"}}],\"infrastructure\":{\"networking\":{\"core\":\"100Gbps\",\"redundancy\":\"N+2\",\"segmentation\":\"micro-segmented\",\"security\":\"zero-trust\"},\"storage\":{\"primary\":\"All-Flash Array\",\"backup\":\"Tape Library + Cloud\",\"archive\":\"Glacier Deep Archive\",\"performance\":\"1M IOPS\"},\"compute\":{\"virtualization\":\"VMware vSphere Enterprise Plus\",\"containers\":\"Red Hat OpenShift\",\"orchestration\":\"Kubernetes\",\"serverless\":\"enabled\"}},\"operations\":{\"monitoring\":{\"infrastructure\":\"Nagios + Prometheus\",\"application\":\"New Relic + AppDynamics\",\"business\":\"Splunk + Tableau\",\"security\":\"Splunk SIEM\"},\"automation\":{\"deployment\":\"Jenkins + Ansible\",\"scaling\":\"Kubernetes HPA\",\"healing\":\"self-healing enabled\",\"patching\":\"automated\"},\"compliance\":{\"standards\":[\"SOX\",\"PCI-DSS\",\"HIPAA\",\"GDPR\",\"ISO 27001\"],\"auditing\":\"continuous\",\"reporting\":\"real-time\",\"certification\":\"annual\"}}}}",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise Global Headquarters"
        };

        var expectedMessage = "CyberComponentGroup has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberComponentGroupResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberComponentGroup(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberComponentGroupResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberComponentGroup_HandlesGroupExpansion()
    {
        // Arrange
        var command = new UpdateCyberComponentGroupCommand
        {
            Id = Guid.NewGuid().ToString(),
            GroupName = "Expanded Enterprise Infrastructure Group",
            ComponentProperties = "{\"expandedGroup\":{\"newComponents\":[{\"name\":\"AI/ML Processing Tier\",\"type\":\"GPU Cluster\",\"instances\":6,\"specifications\":{\"gpu\":\"NVIDIA A100\",\"cpu\":\"AMD EPYC\",\"memory\":\"1TB per instance\"}},{\"name\":\"Edge Computing Nodes\",\"type\":\"Edge Infrastructure\",\"instances\":50,\"locations\":\"global\"}],\"enhancements\":{\"security\":\"enhanced with SASE\",\"monitoring\":\"AI-powered analytics\",\"automation\":\"full DevSecOps pipeline\"}}}",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise Expanded Campus"
        };

        var expectedMessage = "CyberComponentGroup expanded successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberComponentGroupResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberComponentGroup(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberComponentGroupResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("AI/ML", command.ComponentProperties);
    }

    [Fact]
    public async Task GetPaginatedCyberComponentGroups_HandlesMultiSiteFiltering()
    {
        // Arrange
        var query = new GetCyberComponentGroupPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 20,
            SearchString = "Enterprise",
           
        };

        var expectedData = new List<CyberComponentGroupListVm>
        {
            new CyberComponentGroupListVm
            {
                Id = Guid.NewGuid().ToString(),
                GroupName = "Enterprise Core Infrastructure Group",
                SiteId = Guid.NewGuid().ToString(),
                SiteName = "Global Headquarters",
                ComponentProperties = "{\"groupConfiguration\":{\"type\":\"Infrastructure\",\"priority\":\"High\",\"components\":[{\"id\":\"comp-001\",\"name\":\"Updated Primary Database Server\",\"type\":\"Database\",\"role\":\"Primary\",\"specifications\":{\"cpu\":\"64 cores\",\"memory\":\"256GB\",\"storage\":\"4TB NVMe\"},\"dependencies\":[\"comp-002\",\"comp-003\",\"comp-004\"]},{\"id\":\"comp-002\",\"name\":\"Enhanced Application Server Cluster\",\"type\":\"Application\",\"role\":\"Active-Active\",\"specifications\":{\"cpu\":\"32 cores\",\"memory\":\"128GB\",\"storage\":\"1TB NVMe\"},\"loadBalancer\":\"enhanced\"},{\"id\":\"comp-003\",\"name\":\"Scaled Web Server Farm\",\"type\":\"Web\",\"role\":\"Auto-Scaling\",\"specifications\":{\"cpu\":\"16 cores\",\"memory\":\"64GB\",\"storage\":\"500GB NVMe\"},\"instances\":8},{\"id\":\"comp-004\",\"name\":\"Cache Layer\",\"type\":\"Cache\",\"role\":\"Distributed\",\"specifications\":{\"cpu\":\"8 cores\",\"memory\":\"32GB\",\"storage\":\"100GB NVMe\"},\"instances\":3}],\"networking\":{\"vlan\":\"VLAN-100,VLAN-101\",\"subnet\":\"192.168.100.0/24,192.168.101.0/24\",\"firewall\":\"enhanced\",\"monitoring\":\"24x7\",\"redundancy\":\"dual-path\"},\"backup\":{\"frequency\":\"hourly\",\"retention\":\"90 days\",\"location\":\"multi-site\",\"encryption\":\"AES-256\"},\"monitoring\":{\"healthChecks\":\"advanced\",\"alerting\":\"predictive\",\"dashboard\":\"AI-powered\",\"metrics\":\"comprehensive\"}}}",
              
               
            }
        };
        var expectedResults = PaginatedResult<CyberComponentGroupListVm>.Success(expectedData, 1, 1, 20);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentGroupPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber &&
                q.PageSize == query.PageSize &&
                q.SearchString == query.SearchString), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberComponentGroups(query);

        // Assert
        var componentProperties =
            "{\"groupConfiguration\":{\"type\":\"Infrastructure\",\"priority\":\"High\",\"components\":[{\"id\":\"comp-001\",\"name\":\"Updated Primary Database Server\",\"type\":\"Database\",\"role\":\"Primary\",\"specifications\":{\"cpu\":\"64 cores\",\"memory\":\"256GB\",\"storage\":\"4TB NVMe\"},\"dependencies\":[\"comp-002\",\"comp-003\",\"comp-004\"]},{\"id\":\"comp-002\",\"name\":\"Enhanced Application Server Cluster\",\"type\":\"Application\",\"role\":\"Active-Active\",\"specifications\":{\"cpu\":\"32 cores\",\"memory\":\"128GB\",\"storage\":\"1TB NVMe\"},\"loadBalancer\":\"enhanced\"},{\"id\":\"comp-003\",\"name\":\"Scaled Web Server Farm\",\"type\":\"Web\",\"role\":\"Auto-Scaling\",\"specifications\":{\"cpu\":\"16 cores\",\"memory\":\"64GB\",\"storage\":\"500GB NVMe\"},\"instances\":8},{\"id\":\"comp-004\",\"name\":\"Cache Layer\",\"type\":\"Cache\",\"role\":\"Distributed\",\"specifications\":{\"cpu\":\"8 cores\",\"memory\":\"32GB\",\"storage\":\"100GB NVMe\"},\"instances\":3}],\"networking\":{\"vlan\":\"VLAN-100,VLAN-101\",\"subnet\":\"192.168.100.0/24,192.168.101.0/24\",\"firewall\":\"enhanced\",\"monitoring\":\"24x7\",\"redundancy\":\"dual-path\"},\"backup\":{\"frequency\":\"hourly\",\"retention\":\"90 days\",\"location\":\"multi-site\",\"encryption\":\"AES-256\"},\"monitoring\":{\"healthChecks\":\"advanced\",\"alerting\":\"predictive\",\"dashboard\":\"AI-powered\",\"metrics\":\"comprehensive\"}}}";
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberComponentGroupListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        Assert.Equal("Global Headquarters", paginatedResult.Data.First().SiteName);
        Assert.Equal(componentProperties, paginatedResult.Data.First().ComponentProperties);
    }

    [Fact]
    public async Task CreateCyberComponentGroup_HandlesDisasterRecoveryGroup()
    {
        // Arrange
        var command = new CreateCyberComponentGroupCommand
        {
            GroupName = "Enterprise Disaster Recovery Infrastructure Group",
            ComponentProperties = "{\"disasterRecoveryGroup\":{\"purpose\":\"Business Continuity\",\"tier\":\"Tier 1\",\"rto\":\"1 hour\",\"rpo\":\"5 minutes\",\"components\":[{\"name\":\"DR Web Tier\",\"type\":\"Web Server Cluster\",\"instances\":4,\"replication\":\"real-time\"},{\"name\":\"DR Application Tier\",\"type\":\"App Server Cluster\",\"instances\":6,\"replication\":\"near real-time\"},{\"name\":\"DR Database Tier\",\"type\":\"Database Cluster\",\"instances\":2,\"replication\":\"synchronous\"}],\"infrastructure\":{\"location\":\"Secondary Data Center\",\"connectivity\":\"dedicated fiber + MPLS backup\",\"storage\":\"replicated all-flash array\",\"network\":\"isolated VLAN with firewall\"},\"testing\":{\"frequency\":\"monthly\",\"type\":\"full failover test\",\"duration\":\"4 hours\",\"success_criteria\":\"<1 hour RTO achieved\"},\"automation\":{\"failover\":\"automated with manual approval\",\"failback\":\"semi-automated\",\"monitoring\":\"24x7 NOC\",\"alerting\":\"immediate escalation\"}}}",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise DR Site"
        };

        var expectedMessage = "Disaster Recovery CyberComponentGroup created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberComponentGroupResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberComponentGroup(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberComponentGroupResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("Disaster Recovery", response.Message);
    }

    [Fact]
    public async Task GetCyberComponentGroupById_HandlesComplexGroupStructure()
    {
        // Arrange
        var groupId = Guid.NewGuid().ToString();
        var complexGroup = new CyberComponentGroupDetailVm
        {
            Id = groupId,
            GroupName = "Enterprise Multi-Tier Complex Infrastructure Group",
            ComponentProperties = "{\"complexStructure\":{\"tiers\":{\"presentation\":{\"components\":8,\"technology\":\"React + CDN\",\"scaling\":\"auto\"},\"business\":{\"components\":12,\"technology\":\"Spring Boot + Kafka\",\"scaling\":\"horizontal\"},\"data\":{\"components\":4,\"technology\":\"PostgreSQL + Redis\",\"scaling\":\"vertical\"}},\"crossCutting\":{\"security\":{\"authentication\":\"OAuth 2.0 + SAML\",\"authorization\":\"RBAC + ABAC\",\"encryption\":\"end-to-end\"},\"monitoring\":{\"metrics\":\"Prometheus + Grafana\",\"logs\":\"ELK Stack\",\"traces\":\"Jaeger\"},\"deployment\":{\"strategy\":\"blue-green\",\"automation\":\"GitOps\",\"rollback\":\"automated\"}}}}",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise Complex Infrastructure Site"
           
           
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentGroupDetailQuery>(q => q.Id == groupId), default))
            .ReturnsAsync(complexGroup);

        // Act
        var result = await _controller.GetCyberComponentGroupById(groupId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var group = Assert.IsType<CyberComponentGroupDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Multi-Tier Complex Infrastructure Group", group.GroupName);
        Assert.Contains("complexStructure", group.ComponentProperties);
        Assert.Contains("tiers", group.ComponentProperties);
    }

    [Fact]
    public async Task IsCyberComponentGroupNameExist_HandlesLongGroupNames()
    {
        // Arrange
        var longGroupName = "Enterprise Mission-Critical Multi-Site Globally Distributed High-Availability Infrastructure Component Group with Advanced Security and Compliance Features";
        var groupId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberComponentGroupNameUniqueQuery>(q =>
                q.Name == longGroupName && q.Id == groupId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsCyberComponentGroupNameExist(longGroupName, groupId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }

    [Fact]
    public async Task GetCyberComponentGroups_HandlesMultiSiteDeployment()
    {
        // Arrange
        var multiSiteGroups = new List<CyberComponentGroupListVm>();
        var sites = new[] { "New York", "London", "Tokyo", "Sydney", "Frankfurt" };

        for (int i = 0; i < 50; i++)
        {
            multiSiteGroups.Add(new CyberComponentGroupListVm
            {
                Id = Guid.NewGuid().ToString(),
                GroupName = $"Enterprise Group {i + 1}",
                SiteName = sites[i % sites.Length],
                ComponentProperties ="",
                SiteId = Guid.NewGuid().ToString()
            });
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberComponentGroupListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(multiSiteGroups);

        // Act
        var result = await _controller.GetCyberComponentGroups();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var groups = Assert.IsAssignableFrom<List<CyberComponentGroupListVm>>(okResult.Value);
        Assert.Equal(50, groups.Count);
        Assert.Contains(groups, g => g.SiteName == "New York");
        Assert.Contains(groups, g => g.SiteName == "Tokyo");
        
    }
}
