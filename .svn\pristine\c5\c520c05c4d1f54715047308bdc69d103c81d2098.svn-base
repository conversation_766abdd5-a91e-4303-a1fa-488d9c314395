using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetPaginatedList;

public class GetVeritasClusterPaginatedListQueryHandler : IRequestHandler<GetVeritasClusterPaginatedListQuery,
    PaginatedResult<VeritasClusterListVm>>
{
    private readonly IMapper _mapper;
    private readonly IVeritasClusterRepository _veritasClusterRepository;

    public GetVeritasClusterPaginatedListQueryHandler(IMapper mapper,
        IVeritasClusterRepository veritasClusterRepository)
    {
        _mapper = mapper;
        _veritasClusterRepository = veritasClusterRepository;
    }

    public async Task<PaginatedResult<VeritasClusterListVm>> Handle(GetVeritasClusterPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new VeritasClusterFilterSpecification(request.SearchString);

        var queryable = await _veritasClusterRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);
       
        var veritasClusterList = _mapper.Map<PaginatedResult<VeritasClusterListVm>>(queryable);
        //var queryable = _veritasClusterRepository.GetPaginatedQuery();       

        //var veritasClusterList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<VeritasClusterListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //await _publisher.Publish(new VeritasClusterPaginatedEvent(), cancellationToken);

        return veritasClusterList;
    }
}