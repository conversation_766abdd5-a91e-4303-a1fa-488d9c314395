using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class PluginManagerFixture : IDisposable
{
    public List<PluginManager> PluginManagerPaginationList { get; set; }
    public List<PluginManager> PluginManagerList { get; set; }
    public PluginManager PluginManagerDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public PluginManagerFixture()
    {
        var fixture = new Fixture();

        PluginManagerList = fixture.Create<List<PluginManager>>();

        PluginManagerPaginationList = fixture.CreateMany<PluginManager>(20).ToList();

        PluginManagerPaginationList.ForEach(x => x.CompanyId = CompanyId);

        PluginManagerList.ForEach(x => x.CompanyId = CompanyId);

        PluginManagerDto = fixture.Create<PluginManager>();

        PluginManagerDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
