﻿using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.DRReadyStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.DRReadyStatus.Queries;

public class GetDrReadyStatusPaginatedListQueryHandlerTests : IClassFixture<DrReadyStatusFixture>
{
    private readonly DrReadyStatusFixture _drReadyStatusFixture;

    private readonly Mock<IDrReadyStatusRepository> _mockDrReadyStatusRepository;

    private readonly GetDRReadyStatusPaginatedListQueryHandler _handler;

    public GetDrReadyStatusPaginatedListQueryHandlerTests(DrReadyStatusFixture drReadyStatusFixture)
    {
        _drReadyStatusFixture = drReadyStatusFixture;

        _mockDrReadyStatusRepository = DrReadyStatusRepositoryMocks.GetPaginatedDrReadyStatusRepository(_drReadyStatusFixture.DrReadyStatuses);

        _handler = new GetDRReadyStatusPaginatedListQueryHandler(_drReadyStatusFixture.Mapper, _mockDrReadyStatusRepository.Object);

        _drReadyStatusFixture.DrReadyStatuses[0].BusinessServiceName = "BS_Test_01";

        _drReadyStatusFixture.DrReadyStatuses[1].BusinessServiceName = "BS_Test_02";
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetDRReadyStatusPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABC" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DRReadyStatusListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedDRReadyStatuses_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetDRReadyStatusPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "BS_Test_01" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DRReadyStatusListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<DRReadyStatusListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());
        result.Data[0].UserId.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].UserId);
        result.Data[0].BusinessServiceId.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].BusinessServiceId);
        result.Data[0].BusinessServiceName.ShouldBe("BS_Test_01");
        result.Data[0].BusinessFunctionId.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].BusinessFunctionId);
        result.Data[0].BusinessFunctionName.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].BusinessFunctionName);
        result.Data[0].IsProtected.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].IsProtected);
        result.Data[0].AffectedInfra.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].AffectedInfra);
        result.Data[0].ActiveInfra.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].ActiveInfra);
        result.Data[0].WorkflowId.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].WorkflowId);
        result.Data[0].WorkflowName.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].WorkflowName);
        result.Data[0].WorkflowStatus.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].WorkflowStatus);
        result.Data[0].WorkflowAttach.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].WorkflowAttach);
        result.Data[0].FailedActionName.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].FailedActionName);
        result.Data[0].FailedActionId.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].FailedActionId);
        result.Data[0].ActiveBusinessFunction.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].ActiveBusinessFunction);
        result.Data[0].AffectedBusinessFunction.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].AffectedBusinessFunction);
        result.Data[0].DRReady.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].DRReady);
        result.Data[0].NotReady.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].NotReady);
        result.Data[0].InfraObjectId.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].InfraObjectId);
        result.Data[0].InfraObjectName.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].InfraObjectName);
        result.Data[0].ComponentName.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].ComponentName);
        result.Data[0].Type.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].Type);
        result.Data[0].ErrorMessage.ShouldBe(_drReadyStatusFixture.DrReadyStatuses[0].ErrorMessage);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetDRReadyStatusPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DRReadyStatusListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetDRReadyStatusPaginatedListQuery(), CancellationToken.None);

        _mockDrReadyStatusRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}
