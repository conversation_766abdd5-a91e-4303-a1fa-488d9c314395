using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class DynamicDashboardService : BaseClient, IDynamicDashboardService
{
    public DynamicDashboardService(IConfiguration config, IAppCache cache, ILogger<DynamicDashboardService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<DynamicDashboardListVm>> GetDynamicDashboardList()
    {
        var request = new RestRequest("api/v6/dynamicdashboards");

        return await GetFromCache<List<DynamicDashboardListVm>>(request, "GetDynamicDashboardList");
    }

    public async Task<BaseResponse> CreateAsync(CreateDynamicDashboardCommand createDynamicDashboardCommand)
    {
        var request = new RestRequest("api/v6/dynamicdashboards", Method.Post);

        request.AddJsonBody(createDynamicDashboardCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDynamicDashboardCommand updateDynamicDashboardCommand)
    {
        var request = new RestRequest("api/v6/dynamicdashboards", Method.Put);

        request.AddJsonBody(updateDynamicDashboardCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/dynamicdashboards/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<DynamicDashboardDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/dynamicdashboards/{id}");

        return await Get<DynamicDashboardDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsDynamicDashboardNameExist(string name, string id)
  {
     var request = new RestRequest($"api/v6/dynamicdashboards/name-exist?dynamicdashboardName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<DynamicDashboardListVm>> GetPaginatedDynamicDashboards(GetDynamicDashboardPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/dynamicdashboards/paginated-list");

      return await Get<PaginatedResult<DynamicDashboardListVm>>(request);
  }
   #endregion
}
