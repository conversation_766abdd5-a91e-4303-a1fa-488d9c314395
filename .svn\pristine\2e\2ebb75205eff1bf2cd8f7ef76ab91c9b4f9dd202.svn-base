namespace ContinuityPatrol.Application.Features.BackUpLog.Queries.GetNameUnique;

public class GetBackUpLogNameUniqueQueryHandler : IRequestHandler<GetBackUpLogNameUniqueQuery, bool>
{
    private readonly IBackUpLogRepository _backUpLogRepository;

    public GetBackUpLogNameUniqueQueryHandler(IBackUpLogRepository backUpLogRepository)
    {
        _backUpLogRepository = backUpLogRepository;
    }

    public async Task<bool> Handle(GetBackUpLogNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _backUpLogRepository.IsNameExist(request.Name, request.Id);
    }
}