﻿using ContinuityPatrol.Domain.ViewModels.UserGroupModel;

namespace ContinuityPatrol.Application.Features.UserGroup.Queries.GetList;

public class GetUserGroupListQueryHandler : IRequestHandler<GetUserGroupListQuery,
    List<UserGroupListVm>>
{
    private readonly IMapper _mapper;

    private readonly IUserGroupRepository _userGroupRepository;

    public GetUserGroupListQueryHandler(IUserGroupRepository userGroupRepository, IMapper mapper)
    {
        _userGroupRepository = userGroupRepository;
        _mapper = mapper;
    }

    public async Task<List<UserGroupListVm>> Handle(GetUserGroupListQuery request,
        CancellationToken cancellationToken)
    {
        var userGroups = await _userGroupRepository.ListAllAsync();

        return userGroups.Count == 0
            ? new List<UserGroupListVm>()
            : _mapper.Map<List<UserGroupListVm>>(userGroups);
    }
}