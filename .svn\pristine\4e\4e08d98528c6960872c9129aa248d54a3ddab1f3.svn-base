﻿using ContinuityPatrol.Application.Features.ComponentType.Commands.Create;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Update;
using ContinuityPatrol.Application.Features.ComponentType.Events.PaginatedView;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class ComponentTypeController : BaseController
{
    private readonly ILogger<ComponentTypeController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly IPublisher _publisher;
    public ComponentTypeController(ILogger<ComponentTypeController> logger, IPublisher publisher, IMapper mapper, IDataProvider provider)
    {
        _logger = logger;
        _mapper = mapper;
        _publisher = publisher;
        _dataProvider = provider;
    }
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in ComponentType");
        await _publisher.Publish(new ComponentTypePaginatedEvent());
        return View();
    }

    public async Task<JsonResult> GetComponentTypeListByName(string name)
    {
        _logger.LogDebug("Entering GetComponentTypeListByName method in ComponentType");
        try
        {
            var componentType = await _dataProvider.ComponentType.GetComponentTypeListByName(name);
            _logger.LogDebug($"Successfully retrieving component type for '{name}'");
            return Json(new { success = true, data = componentType });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on component type page while retrieving component type of '{name}'.", ex);
            return ex.GetJsonException();
        }
    }
    public async Task<JsonResult> ServerList(string name)
    {
        _logger.LogDebug("Entering ServerList method in ComponentType");
        try
        {
            var serverList = await _dataProvider.ComponentType.GetComponentTypeList();
            if (name != null)
            {
                serverList = serverList.Where(x => x.FormTypeName == name).ToList();
            }
            _logger.LogDebug("Successfully retrieved server list in componentType");
            return Json(new { success = true, data = serverList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on component type page while retrieving server data.", ex);
            return ex.GetJsonException();
        }
    }

    //For form Database.js
    public async Task<JsonResult> DatabaseList()
    {
        _logger.LogDebug("Entering DatabaseList method in ComponentType");
        try
        {
            var databaseList = await _dataProvider.ComponentType.GetComponentTypeList();
            var databaseNames = databaseList.Where(x => x.FormTypeName.ToLower() == "database").ToList();
            _logger.LogDebug("Successfully retrieved database list in componentType");
            return Json(new { success = true, data = databaseNames });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on component type page while retrieving database data.", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<JsonResult> ReplicationList()
    {
        _logger.LogDebug("Entering ReplicationList method in ComponentType");
        try
        {
            var replicationList = await _dataProvider.ComponentType.GetComponentTypeList();
            var replicationNameLists = replicationList.Where(x => x.FormTypeName.ToLower() == "replication").ToList();
            _logger.LogDebug("Successfully retrieved replication list in componentType");
            return Json(new { success = true, data = replicationNameLists });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on component type page while retrieving replication data.", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<JsonResult> SingleSignOnList()
    {
        _logger.LogDebug("Entering SingleSignOnList method in ComponentType");
        try
        {
            var singleSignOnList = await _dataProvider.ComponentType.GetComponentTypeList();
            var singleSignOnType = singleSignOnList.Where(x => x.FormTypeName.ToLower() == "single signon").ToList();
            _logger.LogDebug("Successfully retrieved single sign-on list in componentType");
            return Json(new { success = true, data = singleSignOnType });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on component type page while retrieving single sign-on data.", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<JsonResult> ServerList()
    {
        _logger.LogDebug("Entering ServerList method in ComponentType");
        try
        {
            var componentTypeList = await _dataProvider.ComponentType.GetComponentTypeList();
            var osType = componentTypeList.Where(x => x.FormTypeName.ToLower() == "server").ToList();
            _logger.LogDebug("Successfully retrieved server osType in componentType");
            return Json(new { success = true, data = osType });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on component type page while retrieving server osType.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(ComponentTypeViewModel componentTypeViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in ComponentType");
        var formId = Request.Form["id"].ToString();
        try
        {
            componentTypeViewModel.Properties = componentTypeViewModel.Properties.IsNullOrWhiteSpace() ? componentTypeViewModel.Properties : SecurityHelper.Decrypt(componentTypeViewModel.Properties);
            if (formId.IsNullOrWhiteSpace())
            {
                var componentTypeCreateCommand = _mapper.Map<CreateComponentTypeCommand>(componentTypeViewModel);
                _logger.LogDebug($"Creating ComponentType '{componentTypeCreateCommand.FormTypeName}'.");
                var response = await _dataProvider.ComponentType.CreateAsync(componentTypeCreateCommand);              
                _logger.LogDebug("Create operation completed successfully in ComponentType, returning view.");
                return Json(new { success = true, data = response });
            }
            else
            {
                var componentTypeUpdateCommand = _mapper.Map<UpdateComponentTypeCommand>(componentTypeViewModel);
                _logger.LogDebug($"Updating ComponentType '{componentTypeViewModel.FormTypeName}'");
                var response = await _dataProvider.ComponentType.UpdateAsync(componentTypeUpdateCommand);              
                _logger.LogDebug("Update operation completed successfully in ComponentType, returning view.");
                return Json(new { success = true, data = response });
            }
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation exception on component type page: {ex.ValidationErrors.FirstOrDefault()}");
            return ex.GetJsonException();
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on component type page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in ComponentType");
        try
        {
            _logger.LogDebug("Successfully deleted record in ComponentType");
            var componentType = await _dataProvider.ComponentType.DeleteAsync(id);
            return Json(new { success = true, data = componentType });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on component type.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetComponentTypePaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in ComponentType");
        try
        {
            var serverTypeList = await _dataProvider.ComponentType.GetPaginatedComponentTypes(query);
            _logger.LogDebug("Successfully retrieved componentType paginated list on component type page");
            return Json(new { success = true, data = serverTypeList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on component type page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<bool> ComponentTypeNameExist(string serverTypeName, string serverTypeId)
    {
        _logger.LogDebug("Entering ComponentTypeNameExist method in ComponentType");
        try
        {
            _logger.LogDebug("Returning result for ComponentTypeNameExist on ComponentType");
            return await _dataProvider.ComponentType.IsComponentTypeExist(serverTypeName, serverTypeId);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on component type page while checking if componentType name exists for : {serverTypeName}.", ex);
            return false;
        }
    }
}