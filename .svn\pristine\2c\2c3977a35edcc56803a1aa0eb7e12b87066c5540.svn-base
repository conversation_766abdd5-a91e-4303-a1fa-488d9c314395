﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()

<style>

    .table-container {
     height:350px;
     overflow:auto;
        width: 100%;
    }

    /* Keep first column fixed, scroll the rest */
    .table-sticky tbody td[rowspan] {
        position: sticky !important;
        left: 0;
        z-index: 1;
        border-bottom: 1px solid #e3dede;
        background: #fff;
    }


    /* Optional: Styling for table header */
    .table-sticky thead th:first-child {
        position: sticky;
        left: 0;
        z-index: 2; /* Ensures headers stay above table rows */
        top:0;
    }

        /* Optional: Set a fixed width for the first column */
        .table-sticky tbody td[rowspan]:first-child, .table-sticky thead th:first-child {
            width: 150px; /* Adjust width as needed */
        }

    .table-sticky tbody td {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width:100px;
        }

</style>


<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title" title=" MSSQL AlwaysOn Monitoring">
            <i class="cp-monitoring"></i><span>
                MSSQL AlwaysOn Availability Group Detail Monitoring:
                <span id="infraName"></span>
            </span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>

    </div>
    <div  class="monitor_pages mb-2">
        <div class="row mb-2 g-2 mt-0 ">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-2">
            <div class="col-6 d-grid">
                <div class="card Card_Design_None  h-100">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">Availability Group Summary</div>
                    <div class="card-body pt-0 p-2 "> 
                        <div class="table-container">
                            <table class="table mb-0 table-sticky w-100" id="availabilityGroupTable">
                                <thead style="position: sticky;top: 0;z-index: 999999;">
                                    <tr>
                                        <th rowspan="1">Availability Group Summary</th>
                                        <th colspan="1">Primary</th> <!-- Add column span for SOL-ALWAYSON -->
                                        <th colspan="2">Secondary</th> <!-- Add column span for SOL-ALWAYSON -->
                                    </tr>
                                   @*  <tr>
                                        <th>#</th>
                                        <th>SOL-ALWAYSON-1</th>
                                        <th >SOL-ALWAYSON-2</th>
                                        <th >SOL-ALWAYSON-3</th>
                                    </tr> *@
                                </thead>
                                <tbody></tbody>
                                @* <tbody>
                            
                                    <tr>
                                        <td rowspan="3"><i class="text-secondary cp-instance-name me-1 fs-6"></i>Instance Name (Database Name)</td>
                                        <td>test_db1</td>
                                        <td>test_db2</td>
                                        <td>test_db3</td>
                                    </tr>
                                    <tr>
                                        <td>test_db1</td>
                                        <td>test_db2</td>
                                        <td>test_db3</td>
                                    </tr>
                                    <tr>
                                        <td>test_db1</td>
                                        <td>test_db2</td>
                                        <td>test_db3</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="1"><i class="text-secondary cp-transport-lag me-1 fs-6"></i>Availability Group Role (Primary / Secondary / Resolving)</td>
                                        <td>Primary</td>
                                        <td>Secondary</td>
                                        <td>Secondary</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="1"><i class="text-secondary cp-transport-lag me-1 fs-6"></i>Replica Mode (Synchronous commit with automatic failover / Synchronous commit / Asynchronous commit )</td>
                                        <td>Synchronous Commit</td>
                                        <td>Synchronous Commit</td>
                                        <td>Synchronous Commit</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="1"><i class="text-secondary cp-data-replication me-1 fs-6"></i>Failover Mode Setting (Manual / Automatic)</td>
                                        <td>Manual</td>
                                        <td>Manual</td>
                                        <td>Manual</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="1"><i class="text-secondary cp-network me-1 fs-6"></i>Primary/Secondary Role Allow connections (Read_Write / No / Read_only / All)</td>
                                        <td>ALL</td>
                                        <td>ALL</td>
                                        <td>ALL</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="1"><i class="text-secondary cp-transport-lag me-1 fs-6"></i>Availability Group Operational State(PENDING_FAILOVER / PENDING / ONLINE / OFFLINE / FAILED /FAILED_NO_QUORUM /NULL)</td>
                                        <td>ONLINE</td>
                                        <td>ONLINE</td>
                                        <td>ONLINE</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="1"><i class="text-secondary cp-transport-lag me-1 fs-6"></i>Availability Group Connected State (Connected / Disconnected)</td>
                                        <td>Connected</td>
                                        <td>Connected</td>
                                        <td>Connected</td>
                                    </tr>
                                </tbody> *@

                            </table>
                        </div>

                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="row">
                    <div class="d-grid col-lg-12 ">
                        <div class="card Card_Design_None mb-0">
                            <div class="card-header card-title" title="Solution Diagram">Solution Diagram</div>
                            <div class="d-grid card-body">
                                <div id="Solution_Diagram" class="w-100 h-100"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None  h-100">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">Database Level Monitoring</div>
                    <div class="card-body pt-0 p-2 " >
                        <div class="table-container">
                            <table class="table mb-0 table-sticky w-100" id="databaseTable">
                                <thead style="position: sticky;top: 0;z-index: 999999;">
                                    <tr>
                                        <th rowspan="1">Database Level Monitoring</th>
                                        <th colspan="1">Primary</th> <!-- Add column span for SOL-ALWAYSON -->
                                        <th colspan="2">Secondary</th> <!-- Add column span for SOL-ALWAYSON -->
                                    </tr>
                                    @* <tr>
                                        <th>#</th>
                                        <th>SOL-ALWAYSON-1</th>
                                        <th>SOL-ALWAYSON-2</th>
                                        <th>SOL-ALWAYSON-3</th>
                                    </tr> *@
                                </thead>
                                <tbody></tbody>
                                @* <tbody>
                                    <tr>
                                        <td rowspan="3"><i class="text-secondary cp-dataguard-status me-1 fs-6"></i>Database Synchronization State (NOT SYNCHRONIZING / SYNCHRONIZING / SYNCHRONIZED / REVERTING / INITIALIZING)</td>
                                        <td>test_db4 (Synchronized)</td>
                                        <td>test_db4 (Synchronized)</td>
                                        <td>test_db4 (Synchronized)</td>
                                    </tr>
                                    <tr>
                                        <td>test_db5 (Synchronized)</td>
                                        <td>test_db5 (Synchronized)</td>
                                        <td>test_db5 (Synchronized)</td>
                                    </tr>
                                    <tr>
                                        <td>test_db4 (Healthy)</td>
                                        <td>test_db4 (Healthy)</td>
                                        <td>test_db4 (Healthy)</td>
                                    </tr>


                                    <tr>
                                        <td rowspan="1"><i class="text-secondary cp-dataguard-status me-1 fs-6"></i>Database Synchronization Health Status (NOT_HEALTHY / PARTIALLY_HEALTHY / HEALTHY)</td>
                                        <td>test_db5 (Healthy)</td>
                                        <td>test_db5 (Healthy)</td>
                                        <td>test_db5 (Healthy)</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="2"><i class="text-secondary cp-dataguard-status me-1 fs-6"></i>Database State (ONLINE /RESTORING / RECOVERING /RECOVERY_PENDING / SUSPECT /EMERGENCY /OFFLINE)</td>
                                        <td>test_db4 (Online)</td>
                                        <td>test_db4 (Online)</td>
                                        <td>test_db4 (Online)</td>
                                    </tr>
                                    <tr>
                                        <td>test_db5 (Online)</td>
                                        <td>test_db5 (Online)</td>
                                        <td>test_db5 (Online)</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="2"><i class="text-secondary cp-dataguard-status me-1 fs-6"></i>Data synchronization State on availability database, Reason if Suspended ( Resumed/Suspended, SUSPEND_FROM_USER /SUSPEND_FROM_PARTNER / SUSPEND_FROM_REDO / SUSPEND_FROM_APPLY / SUSPEND_FROM_CAPTURE / SUSPEND_FROM_RESTART / SUSPEND_FROM_UNDO / SUSPEND_FROM_REVALIDATION / SUSPEND_FROM_XRF_UPDATE / NULL)</td>
                                        <td>test_db4 (Resumed, NULL)</td>
                                        <td>test_db4 (Resumed, NULL)</td>
                                        <td>test_db4 (Resumed, NULL)</td>
                                    </tr>
                                    <tr>
                                        <td>test_db5 (Resumed, NULL)</td>
                                        <td>test_db5 (Resumed, NULL)</td>
                                        <td>test_db5 (Resumed, NULL)</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="1"><i class="text-secondary cp-endpoint-port-number me-1 fs-6"></i>Endpoint Port Number</td>
                                        <td>5022</td>
                                        <td>5022</td>
                                        <td>5022</td>

                                    </tr>
                                </tbody> *@

                            </table>
                        </div>

                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None  h-100">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">Database Replication LSN Monitoring</div>
                    <div class="card-body pt-0 p-2 ">
                        <div class="table-container">
                            <table class="table mb-0 table-sticky w-100" id="replicationTable">
                                <thead style="position: sticky;top: 0;z-index: 999999;">
                                    <tr>
                                        <th rowspan="1">Database Replication LSN Summery</th>
                                        <th colspan="1">Primary</th> <!-- Add column span for SOL-ALWAYSON -->
                                        <th colspan="2">Secondary</th> <!-- Add column span for SOL-ALWAYSON -->
                                    </tr>
                                    @* <tr>
                                        <th>#</th>
                                        <th>SOL-ALWAYSON-1</th>
                                        <th>SOL-ALWAYSON-2</th>
                                        <th>SOL-ALWAYSON-3</th>
                                    </tr> *@
                                </thead>
                                <tbody></tbody>
                               @*  <tbody>
                                    <tr>
                                        <td rowspan="2"><i class="text-secondary cp-last-copied-transaction me-1 fs-6"></i>Last Sent LSN (At Secondary)</td>
                                        <td>test_db4 (NULL)</td>
                                        <td>test_db4 (36000000054200001)</td>
                                        <td>test_db4 (36000000054200001)</td>
                                    </tr>
                                    <tr>
                                        <td>test_db5 (NULL)</td>
                                        <td>test_db5 (36000000054200001)</td>
                                        <td>test_db5 (36000000054200001)</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="2"><i class="text-secondary cp-last-copied-transaction me-1 fs-6"></i>Last Received LSN (At Secondary)</td>
                                        <td>test_db4 (NULL)</td>
                                        <td>test_db4 (36000000054200001)</td>
                                        <td>test_db4 (36000000054200001)</td>
                                    </tr>
                                    <tr>
                                        <td>test_db5 (NULL)</td>
                                        <td>test_db5 (36000000054200001)</td>
                                        <td>test_db5 (36000000054200001)</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="2"><i class="text-secondary cp-last-copied-transaction me-1 fs-6"></i>Last Redone LSN (At Secondary)</td>
                                        <td>test_db4 (NULL)</td>
                                        <td>test_db4 (36000000054200001)</td>
                                        <td>test_db4 (36000000054200001)</td>
                                    </tr>
                                    <tr>
                                        <td>test_db5 (NULL)</td>
                                        <td>test_db5 (36000000054200001)</td>
                                        <td>test_db5 (36000000054200001)</td>
                                    </tr>
                                    <tr>
                                        <td rowspan="2"><i class="text-secondary cp-last-copied-transaction me-1 fs-6"></i>Last Commit LSN</td>
                                        <td>test_db4 (NULL)</td>
                                        <td>test_db4 (36000000054200001)</td>
                                        <td>test_db4 (36000000054200001)</td>
                                    </tr>
                                    <tr>
                                        <td>test_db5 (NULL)</td>
                                        <td>test_db5 (36000000054200001)</td>
                                        <td>test_db5 (36000000054200001)</td>
                                    </tr>
                                </tbody> *@

                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 d-grid">
            <div class="card Card_Design_None mb-2 h-100" id="mssqlserver">
                <div class="card-header card-title" title="Service/Process/Workflow">Service/Process/Workflow</div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0 noDataimg" style="table-layout:fixed" id="tableCluster">
                        <thead class="align-middle">
                            <tr>
                                <th rowspan="2">Service / Process / Workflow Name</th>
                                <th colspan="2" class="text-center">Server IP/HostName</th>
                            </tr>
                            <tr>
                                <th id="prIp"></th>
                                <th id="drIp"></th>
                            </tr>
                        </thead>
                        <tbody id="mssqlserverbody">
                        </tbody>

                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/js/Monitoring/AlwaysOnAvailabilityGroup.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>
