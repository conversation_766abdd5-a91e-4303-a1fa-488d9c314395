﻿using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.XtraCharts;
using Newtonsoft.Json;
using System.Data;
using Newtonsoft.Json.Linq;
using System.Drawing;
using ContinuityPatrol.Shared.Core.Extensions;
using System.Globalization;
using ContinuityPatrol.Application.Features.Report.Queries.GetRPOSLAReport.Models;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class RPOSLAOracleRac : DevExpress.XtraReports.UI.XtraReport
    {
        public static string username;
        private readonly ILogger<PreBuildReportController> _logger;
        public static TimeSpan ConfigRPO;
        private static TimeSpan Threshold;
        private static Int64 ConfiguredRPO;
        private static Int64 ConfiguredThreshold;
        private string RportDateOption;
        public static DateTime reportFromdate;
        public static DateTime reportTodate;
        private List<GetOracleRacRPOSLAReportVm> ReportList = new List<GetOracleRacRPOSLAReportVm>();
        public  GetOracleRacRPOSLABusinessServiceDetails ReportData = new GetOracleRacRPOSLABusinessServiceDetails();
        private static List<Int64> dataLaggeneration = new List<Int64>();
        private static List<Int64> dataLagapplied = new List<Int64>();

        List<DataLagInfo> DataLagValues = new List<DataLagInfo>();
        public string[] InputDateFormats = { "dd/MM/yyyy", "MM/dd/yyyy", "yyyy-MM-dd", "M/d/yyyy", "d/M/yyyy", "MM-dd-yyyy", "dd-MM-yyyy", "dd-MM-yyyy HH:mm:ss", "dd-MM-yyyy hh:mm:ss tt", "dd-MM-yyyy HH.mm.ss", "dd-MM-yyyy HH.mm.ss tt" };
        
        
        private class SpDatalag
        {
            public TimeSpan Datalag { get; set; }
            public int TimeStamp { get; set; }
            public string Date { get; set; }
        }
        public class DataLagInfo
        {
            public string Name { get; set; }
            public List<string> Data { get; set; }
            public string Days { get; set; }
            public string Size { get; set; }
            public string Hour { get; set; }
        }
        public RPOSLAOracleRac(string data)
        {
            try
            {
                _logger = PreBuildReportController._logger;

                if (Report != null)
                {
                    InitializeComponent();
                    ClientCompanyLogo();
                    
                    ReportData = JsonConvert.DeserializeObject<GetOracleRacRPOSLABusinessServiceDetails>(data);

                    ReportList = ReportData.GetOracleRacRPOSLAReportVms;
                    
                    RportDateOption = ReportData.DateOption;

                    this.DataSource = ReportList;

                    //var propeties = ReportList[0].Properties.ToString();
                    var infra = ReportData.InfraObjectName;
                    this.DisplayName = "RPOSLAReport_" + infra + "_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");
                    lblInfra.Text = infra;

                    lblOperationalServiceName.Text = !string.IsNullOrEmpty(ReportData.BusinessServiceName)
                    ? ReportData.BusinessServiceName
                    : "-";

                    username = ReportData.ReportGeneratedBy;
                    //get database sid name from list
                    string prDatabaseSID = string.Join(", ",ReportData.PRDatabaseName);
                    string drDatabaseSID = string.Join(", ", ReportData.DRDatabaseName); ;

                    lblPRDB.Text = prDatabaseSID.Trim();
                    lblDRDB.Text = drDatabaseSID.Trim();

                    var lastReportListInfo = ReportList.LastOrDefault();

                    //get logsequnce from list
                    string prLogSequnce = string.Join(",", lastReportListInfo.PrArchiveLog);

                    string drLogSequnce = string.Join(",", lastReportListInfo.ArchiveLogApplied);

                    const string pattern = @"\d+";

                    if (!string.IsNullOrEmpty(prLogSequnce.ToString()) && prLogSequnce.ToString() != "NA")
                    {
                        Regex r = new Regex(pattern, RegexOptions.Compiled);

                        Match prmatchvalue = Regex.Match(prLogSequnce.ToString(), pattern);

                        prLogSequnce = !string.IsNullOrEmpty(prmatchvalue.Value.ToString()) 
                            ? prmatchvalue.Value.ToString()
                            :"-";
                        lblPRLogs.Text = prLogSequnce;

                    }
                    else
                    {
                        lblPRLogs.Text = "-";
                    }
                    if (!string.IsNullOrEmpty(drLogSequnce.ToString()) && drLogSequnce.ToString() != "NA")
                    {
                        Regex r = new Regex(pattern, RegexOptions.Compiled);

                        Match drmatchvalue = Regex.Match(drLogSequnce.ToString(), pattern);

                        drLogSequnce = !string.IsNullOrEmpty(drmatchvalue.Value.ToString())
                            ? drmatchvalue.Value.ToString()
                            : "-";
                        lblDRLogs.Text = drLogSequnce;
                    }
                    else
                    {
                        lblDRLogs.Text = "-";
                    }

                    ConfiguredRPO = lastReportListInfo.ConfigureRPO.ToInt64();
                    ConfiguredThreshold = lastReportListInfo.Threshold.ToInt64();

                    ConfigRPO = TimeSpan.FromMinutes(ConfiguredRPO);
                    Threshold = TimeSpan.FromMinutes(ConfiguredThreshold);

                    lblConfiguredRPO.Text = "Configured RPO(" + ConfiguredRPO.ToString() + " Mins)";
                    lblThreshold.Text = "Threshold Exceeded(" + ConfiguredThreshold.ToString() + " Mins)";

                    lblUnderThreshold.Text = "DataLag ≤ " + Threshold + " (Threshold)";
                    lblDatalagExceed.Text = "DataLag > " + ConfigRPO + " (Configured RPO)";
                    lblThresholdExceed.Text = "(Threshold)" + Threshold + " < Threshold Exceeded ≤ " + ConfigRPO + "(Configured RPO)";

                    var fromdate = ReportData.FromDate;
                    var todate = ReportData.ToDate;

                    lblTimedate.Text = fromdate == todate ? "Time (Hours)" : "Date (Days)";
                    
                    reportFromdate = DateTime.Parse(fromdate);
                    reportTodate = DateTime.Parse(todate);

                    lblfromdate.Text = reportFromdate.ToString("dd/MM/yyyy");
                    lbltodate.Text = reportTodate.ToString("dd/MM/yyyy");

                }
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLAOracleRac Report. The error message : " + ex.Message); throw; }
        }

        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + username;
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLAOracleRac Report's User Name. The error message : " + ex.Message); throw; }
        }

        private void xrChart1_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                string txtdayOrHours = string.Empty;
                var lastReportListInfo = ReportList.LastOrDefault();
                var propeties = lastReportListInfo?.Properties?.ToString();
                if (lastReportListInfo != null)
                {
                    if (RportDateOption == "Daily")
                    {
                        txtDataLagInfo.Text = "Archive Log Generation Hourly";
                        txtdayOrHours = "Time(Hours)";
                        var Archieve_Log = GetJsonOracleRacPrArrayValues(propeties, "MonitoringOracleRacModel", "PrModel.PrMonitoringModel.PrReplicationMonitoring.Archieve_Log_Genearion_Day");
                        
                        if (!string.IsNullOrEmpty(Archieve_Log) && Archieve_Log != "NA")
                        {
                            JObject jsonArchieveLog = JObject.Parse(Archieve_Log);
                            var Datavalues = jsonArchieveLog.SelectToken("DayData").ToString();
                            DataLagValues = JsonConvert.DeserializeObject<List<DataLagInfo>>(Datavalues);
                        }
                    }
                    else if (RportDateOption == "Weekly")
                    {
                        txtDataLagInfo.Text = "Archive Log Generation Weekly";
                        txtdayOrHours = "Date(Days)";
                        var Archieve_Log = GetJsonOracleRacPrArrayValues(propeties, "MonitoringOracleRacModel", "PrModel.PrMonitoringModel.PrReplicationMonitoring.Archieve_Log_Genearion_Weekly");
                        //var Archieve_Log = jsonArchiveLogWeekly.SelectToken("Archieve_Log_Genearion_Weekly").ToString();
                        if (!string.IsNullOrEmpty(Archieve_Log) && Archieve_Log != "NA")
                        {
                            JObject jsonArchieveLog = JObject.Parse(Archieve_Log);
                            var Datavalues = jsonArchieveLog.SelectToken("WeekData").ToString();
                            DataLagValues = JsonConvert.DeserializeObject<List<DataLagInfo>>(Datavalues);
                        }
                    }
                    else if (RportDateOption == "Monthly")
                    {
                        txtDataLagInfo.Text = "Archive Log Generation Monthly";
                        txtdayOrHours = "Date(Days)";
                        var Archieve_Log = GetJsonOracleRacPrArrayValues(propeties, "MonitoringOracleRacModel", "PrModel.PrMonitoringModel.PrReplicationMonitoring.Archieve_Log_Genearion_Monthly");
                        //var Archieve_Log = jsonArchiveLogMonthly.SelectToken("Archieve_Log_Genearion_Monthly").ToString();
                        if (!string.IsNullOrEmpty(Archieve_Log) && Archieve_Log != "NA")
                        {
                            JObject jsonArchieveLog = JObject.Parse(Archieve_Log);
                            var Datavalues = jsonArchieveLog.SelectToken("MonthData").ToString();
                            DataLagValues = JsonConvert.DeserializeObject<List<DataLagInfo>>(Datavalues);
                        }
                    }
                }


                if (DataLagValues.Count > 0)
                {
                    xrPictureBox8.Visible = false;
                    xrChart1.Visible = true;
                    xrLabel24.Visible = true;
                    xrLabel26.Visible = true;
                    xrLabel30.Visible = true;
                    xrShape6.Visible = true;
                    xrLabel26.Text = txtdayOrHours;
                    Series series = new Series("Generated", ViewType.Bar);
                    xrChart1.Series.Add(series);
                    series.LabelsVisibility = DevExpress.Utils.DefaultBoolean.True;
                    series.DataSource = CreateChartData(DataLagValues, RportDateOption);
                    series.ArgumentScaleType = ScaleType.Auto;
                    series.ArgumentDataMember = "Argument";
                    series.ValueScaleType = ScaleType.Numerical;
                    series.ValueDataMembers.AddRange(new string[] { "ValueUp" });
                    series.Label.TextPattern = "{V}";
                    series.Label.TextAlignment = StringAlignment.Near;

                    SideBySideBarSeriesView view = series.View as SideBySideBarSeriesView;
                    view.BarWidth = 0.3;
                    view.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                    view.Pane.BorderVisible = false;

                    xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
                    ((XYDiagram)xrChart1.Diagram).AxisY.GridLines.Visible = true;
                    int red = 138;
                    int green = 144;
                    int blue = 154;
                    Color customColor = Color.FromArgb(red, green, blue);
                    int red1 = 243;
                    int green1 = 243;
                    int blue1 = 243;
                    Color customColor1 = Color.FromArgb(red1, green1, blue1);

                    ((XYDiagram)xrChart1.Diagram).AxisY.GridLines.Color = customColor1;
                    ((XYDiagram)xrChart1.Diagram).AxisX.Label.TextColor = customColor;
                    ((XYDiagram)xrChart1.Diagram).AxisY.Label.TextColor = customColor;
                    NumericScaleOptions numericScaleOptions = ((XYDiagram)xrChart1.Diagram).AxisX.NumericScaleOptions;
                    numericScaleOptions.GridOffset = 0;
                    numericScaleOptions.AutoGrid = false;
                    numericScaleOptions.GridAlignment = NumericGridAlignment.Ones;
                    //numericScaleOptions.GridSpacing = 0;

                    xrChart1.Series.AddRange(new Series[] { series });
                }
                else
                {
                    xrLabel26.Visible = true;
                    xrLabel26.Text = "No Data Found";
                    xrPictureBox8.Visible = true;
                    xrLabel30.Visible = false;
                    xrShape6.Visible = false;
                    xrChart1.Visible = false;
                    xrLabel24.Visible = false;
                }
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLAOracleRac Report's Bar chart. The error message : " + ex.Message); throw; }
        }
        private DataTable CreateChartData(List<DataLagInfo> generation, string dateformat)
        {
            DataTable table = new DataTable("Table1");
            try
            {
                string format = @"[^0-9\\./]";
                table.Columns.Add("Argument", typeof(string));
                table.Columns.Add("ValueUp", typeof(float));
                Random rnd = new Random();

                if (dateformat == "Daily")
                {
                    var dayGeneration = generation.OrderBy(x => x.Hour);
                    foreach (var inline in dayGeneration)
                    {
                        var size = Convert.ToDecimal(inline.Size.Trim());
                        double roundedValue = (double)Math.Round(size);
                        table.Rows.Add(inline.Hour.Trim(), roundedValue);
                    }

                }
                else if (dateformat == "Weekly")
                {
                    foreach (var inline in generation)
                    {
                        inline.Days = inline.Days.ToString().Trim();
                        inline.Size = inline.Size.ToString().Trim();
                        bool dayFormat = Regex.IsMatch(inline.Days, format);
                        bool sizeFormat = Regex.IsMatch(inline.Size, format);
                        if (!dayFormat && !sizeFormat)
                        {
                            DateTime dateTime = DateTime.ParseExact(inline.Days, InputDateFormats, CultureInfo.InvariantCulture);
                            string formattedDate = dateTime.ToString("MMM-dd");
                            if (dateTime >= reportFromdate && dateTime <= reportTodate)
                            {
                                var size = Convert.ToDecimal(inline.Size);
                                double roundedValue = (double)Math.Round(size);
                                table.Rows.Add(formattedDate, roundedValue);
                            }
                        }
                    }
                }
                else if (dateformat == "Monthly")
                {
                    foreach (var inline in generation)
                    {
                        inline.Days = inline.Days.ToString().Trim();
                        inline.Size = inline.Size.ToString().Trim();
                        bool dayFormat = Regex.IsMatch(inline.Days, format);
                        bool sizeFormat = Regex.IsMatch(inline.Size, format);
                        if (!dayFormat && !sizeFormat)
                        {
                            DateTime dateTime = DateTime.ParseExact(inline.Days, InputDateFormats, CultureInfo.InvariantCulture);
                            string formattedDate = dateTime.ToString("MMM-dd");
                            if (dateTime >= reportFromdate && dateTime <= reportTodate)
                            {
                                var size = Convert.ToDecimal(inline.Size);
                                double roundedValue = (double)Math.Round(size);
                                table.Rows.Add(formattedDate, roundedValue);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message.ToString());
            }
            return table;
        }
        private void xrChart2_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                List<SpDatalag> chart = GetChart();
                Series series1 = new Series("Series1", ViewType.Spline);
                xrChart2.Series.Add(series1);

                series1.DataSource = CreateChartDatasp(chart);
                series1.ArgumentScaleType = ScaleType.Auto;
                series1.ArgumentDataMember = "Argument";
                series1.ValueScaleType = ScaleType.Numerical;
                series1.ValueDataMembers.AddRange(new string[] { "Value" });
                series1.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;

                xrChart2.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
                SplineSeriesView view1 = series1.View as SplineSeriesView;
                view1.MarkerVisibility = DevExpress.Utils.DefaultBoolean.False;
                view1.Pane.BorderVisible = false;

                Series series2 = new Series("Series2", ViewType.Spline);
                xrChart2.Series.Add(series2);

                series2.DataSource = CreateChartDataline(chart);
                series2.ArgumentScaleType = ScaleType.Auto;
                series2.ArgumentDataMember = "Argument";
                series2.ValueScaleType = ScaleType.Numerical;
                series2.ValueDataMembers.AddRange(new string[] { "Value" });
                series2.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
                SplineSeriesView view2 = series2.View as SplineSeriesView;
                view2.MarkerVisibility = DevExpress.Utils.DefaultBoolean.False;
                view2.Pane.BorderVisible = false;

                Series series3 = new Series("Series3", ViewType.Spline);
                xrChart2.Series.Add(series3);

                series3.DataSource = CreateChartThresholdline(chart);
                series3.ArgumentScaleType = ScaleType.Auto;
                series3.ArgumentDataMember = "Argument";
                series3.ValueScaleType = ScaleType.Numerical;
                series3.ValueDataMembers.AddRange(new string[] { "Value" });
                series3.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
                SplineSeriesView view3 = series3.View as SplineSeriesView;
                view3.MarkerVisibility = DevExpress.Utils.DefaultBoolean.False;
                view3.Pane.BorderVisible = false;

                ((XYDiagram)xrChart2.Diagram).AxisY.GridLines.Visible = true;
                int red = 138;
                int green = 144;
                int blue = 154;
                Color customColor = Color.FromArgb(red, green, blue);
                int red1 = 243;
                int green1 = 243;
                int blue1 = 243;
                Color customColor1 = Color.FromArgb(red1, green1, blue1);

                ((XYDiagram)xrChart2.Diagram).AxisY.GridLines.Color = customColor1;
                ((XYDiagram)xrChart2.Diagram).AxisX.Label.TextColor = customColor;
                ((XYDiagram)xrChart2.Diagram).AxisY.Label.TextColor = customColor;
                ((XYDiagram)xrChart2.Diagram).AxisX.WholeRange.SideMarginsValue = 0;
                NumericScaleOptions numericScaleOptions = ((XYDiagram)xrChart2.Diagram).AxisX.NumericScaleOptions;
                numericScaleOptions.GridOffset = 0;
                numericScaleOptions.AutoGrid = false;
                numericScaleOptions.GridAlignment = NumericGridAlignment.Ones;
                //numericScaleOptions.GridSpacing = 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message.ToString());
            }
        }

        private List<SpDatalag> GetChart()
        {
            TimeSpan timeSpan = TimeSpan.Zero;
            TimeSpan datalag = TimeSpan.Zero;
            List<SpDatalag> chart = new List<SpDatalag>();
            List<SpDatalag> finalchart = new List<SpDatalag>();
            try
            {
                if (RportDateOption == "Daily")
                {
                    foreach (var check in ReportList)
                    {
                        if (!string.IsNullOrEmpty(check.DataLag))
                        {
                            if (check.DataLag.Contains("+"))
                            {
                                string datalagvalue = check.DataLag;
                                datalagvalue = datalagvalue.TrimStart('+');
                                datalagvalue = datalagvalue.Replace(" ", ".");
                                datalag = TimeSpan.Parse(datalagvalue);
                            }
                            else
                            {
                                bool containsLetters = false;
                                if (check.DataLag != "NA") { containsLetters = Regex.IsMatch(check.DataLag, "[a-zA-Z]"); }
                                string datalagvalues = string.IsNullOrEmpty(check.DataLag) || check.DataLag == "NA" || containsLetters == true ? "00:00:00" : check.DataLag;
                                datalag = TimeSpan.Parse(datalagvalues);
                            }
                            DateTime validDateTime = DateTime.ParseExact(check.TimeStamp, InputDateFormats, CultureInfo.InvariantCulture);
                            int hours = validDateTime.Hour;
                            chart.Add(new SpDatalag { Datalag = datalag, TimeStamp = hours });
                        }

                    }
                    var groupedData = chart.GroupBy(entry => entry.TimeStamp);
                    foreach (var group in groupedData)
                    {

                        TimeSpan maxDatalag = group.Max(entry => entry.Datalag);
                        finalchart.Add(new SpDatalag { Datalag = maxDatalag, TimeStamp = group.Key });

                    }
                }
                else
                {
                    foreach (var check in ReportList)
                    {
                        if (!string.IsNullOrEmpty(check.DataLag))
                        {
                            if (check.DataLag.Contains("+"))
                            {
                                string datalagvalue = check.DataLag;
                                datalagvalue = datalagvalue.TrimStart('+');
                                datalagvalue = datalagvalue.Replace(" ", ".");
                                datalag = TimeSpan.Parse(datalagvalue);
                            }
                            else
                            {
                                bool containsLetters = false;
                                if (check.DataLag != "NA") { containsLetters = Regex.IsMatch(check.DataLag, "[a-zA-Z]"); }
                                string datalagvalues = string.IsNullOrEmpty(check.DataLag) || check.DataLag == "NA" || containsLetters == true ? "00:00:00" : check.DataLag;
                                datalag = TimeSpan.Parse(datalagvalues);
                            }
                            DateTime validDateTime = DateTime.ParseExact(check.TimeStamp, InputDateFormats, CultureInfo.InvariantCulture);
                            int day = validDateTime.Day;
                            string formattedDate = validDateTime.ToString("MMM-dd");
                            chart.Add(new SpDatalag { Datalag = datalag, TimeStamp = day, Date = formattedDate });
                        }

                    }
                    var groupedData = chart.GroupBy(entry => entry.TimeStamp);

                    foreach (var group in groupedData)
                    {
                        string date = string.Empty;
                        foreach (var entry in group)
                        {
                            if (!string.IsNullOrEmpty(entry.Date))
                            {
                                date = entry.Date;
                                break;
                            }
                        }
                        TimeSpan maxDatalag = group.Max(entry => entry.Datalag);
                        finalchart.Add(new SpDatalag { Datalag = maxDatalag, Date = date });

                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message.ToString());
            }
            return finalchart;

        }

        private DataTable CreateChartDatasp(List<SpDatalag> spDatalags)
        {
            DataTable table = new DataTable("Table1");
            try
            {
                table.Columns.Add("Argument", typeof(string));
                table.Columns.Add("Value", typeof(Int64));
                Random rnd = new Random();
                int i = 0;
                foreach (var inline in spDatalags)
                {
                    if (i == 0) { table.Rows.Add(0, 0); }
                    if (RportDateOption == "Daily")
                    {
                        table.Rows.Add(inline.TimeStamp.ToString(), inline.Datalag.TotalMinutes);
                    }
                    else
                    {
                        table.Rows.Add(inline.Date.ToString(), inline.Datalag.TotalMinutes);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message.ToString());
            }
            return table;
        }
        private DataTable CreateChartDataline(List<SpDatalag> spDatalags)
        {
            DataTable table = new DataTable("Table1");
            try
            {
                table.Columns.Add("Argument", typeof(string));
                table.Columns.Add("Value", typeof(Int64));
                Random rnd = new Random();
                int i = 0;
                foreach (var inline in spDatalags)
                {
                    if (i == 0) { table.Rows.Add(0, ConfiguredRPO); }
                    if (RportDateOption == "Daily")
                    {
                        table.Rows.Add(inline.TimeStamp.ToString(), ConfiguredRPO);
                    }
                    else
                    {
                        table.Rows.Add(inline.Date.ToString(), ConfiguredRPO);
                    }
                    i++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message.ToString());
            }
            return table;
        }
        private DataTable CreateChartThresholdline(List<SpDatalag> spDatalags)
        {
            DataTable table = new DataTable("Table1");
            try
            {
                table.Columns.Add("Argument", typeof(string));
                table.Columns.Add("Value", typeof(Int64));
                Random rnd = new Random();
                int i = 0;
                foreach (var inline in spDatalags)
                {
                    if (i == 0) { table.Rows.Add(0, ConfiguredThreshold); }
                    if (RportDateOption == "Daily")
                    {
                        table.Rows.Add(inline.TimeStamp.ToString(), ConfiguredThreshold);
                    }
                    else
                    {
                        table.Rows.Add(inline.Date.ToString(), ConfiguredThreshold);
                    }
                    i++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message.ToString());
            }
            return table;
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {

                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLAOracleRac Report's CP Version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the RPOSLAOracleRac Report's customer logo" + ex.Message.ToString());
            }
        }
        public static string GetJsonOracleRacPrArrayValues(string json, string arrayPath, string propertyPath)
        {
            var data = string.Empty;

            var jsonObject = JObject.Parse(json);
            var arrayToken = jsonObject.SelectToken(arrayPath) as JArray;
            if (arrayToken != null && arrayToken.Count > 0)
            {
                foreach (var item in arrayToken!)
                {
                    var jsonValue = string.IsNullOrEmpty(item.SelectToken(propertyPath)?.ToString())
                            ? "NA"
                            : item.SelectToken(propertyPath)!.ToString();

                    data=jsonValue;
                    break;
                }
            }
            else
            {
                data="NA";
            }
            return data;
        }
        
    }
}
