﻿$(async function () {
    //AMC
    $('#amcRenew').addClass('d-none')
    $('#option3').on('click', function () {
        $('#amcRenew').removeClass('d-none')
    })
    $('#option1, #option2').on('click', function () {
        $("#amcRenew").addClass('d-none');
    });
    $('.amctype').on('change', function () {
        if ($(this).is(':checked')) {

            $('.amctype').not(this).prop('disabled', true);
        } else {

            if ($('.amctype:checked').length === 0) {
                $('.amctype').prop('disabled', false);
            }
        }
    });
    //Renewal
    $("#renewdate").addClass('d-none');
    $('#option1').on('click', function () {
        $('.renewtype').not(this).prop('disabled', false);
        $("#renewdate").removeClass('d-none');
        $("#amcRenew").addClass('d-none');
    });
    $('#option2, #option3').on('click', function () {
        $("#renewdate").addClass('d-none');
    });
    $('#option2').on('click', function () {
        $("#amcRenew").addClass('d-none');
    });
    $('#option3').on('click', function () {
        $("#amcRenew").removeClass('d-none');
        $('.amctype').not(this).prop('disabled', false);
    });
    $('.renewtype').on('change', function () {
        if ($(this).is(':checked')) {
            $('.renewtype').not(this).prop('disabled', true);
        } else {
            if ($('.renewtype:checked').length === 0) {
                $('.renewtype').prop('disabled', false);
            }
        }
    });

setTimeout(() => {
    $('#ddlLicenseKey').select2()
},1000)

    let selectedLicenseId = '';
    $('#ddlLicenseKey').on('change', function () {
        selectedLicenseId = $(this).val();   
            $("#requestlicense").prop("disabled", "")
    });
    if (selectedLicenseId == "") {
        $("#requestlicense").prop("disabled", "disabled")
    } 
    await $.ajax({
        url: RootUrl + "Admin/LicenseManager/GetExpiredLicensesList",
        dataType: "json",
        type: "GET",
        success: function (result) {
            $('#ddlLicenseKey').empty();
            if (result?.success) {               
                $('#ddlLicenseKey').append('<option value="">Select License</option>');
                for (let index = 0; index < result?.data.length; index++) {                                       
                    $('#ddlLicenseKey').append('<option isAmc="' + result?.data[index]?.isAMC + '" validity="' + result?.data[index]?.validity +'" value="' + result?.data[index]?.id + '">' + result?.data[index]?.poNumber + '</option>')
                }
            }
        }
    })
    $(document).on('click', '#requestlicense', function () {
        $('input[name="type"]:checked').prop('checked', false);
        $('input[name="renewtype"]:checked').prop('checked', false);
        $('input[name="amctype"]:checked').prop('checked', false);
        $("#renewdate,#copy_request,#amcRenew").addClass('d-none');
        $(".myDiv").show();
        $("#generate").show();
        $('.myDiv2').addClass('d-none');        
        $('#generate').val()  

        if ($('#ddlLicenseKey option:selected').attr("isAmc") == true) {
            $(".amcnew").hide()
            $(".amcrenewal").show()
        } else {
            $(".amcnew").show()
            $(".amcrenewal").hide()
        }
        if ($('#ddlLicenseKey option:selected').attr("validity") == "Enterprise-Unlimited") {
            $(".renewchild").hide()
            $(".Upgrade").hide()
            $(".AMC").show()
        } else if ($('#ddlLicenseKey option:selected').attr("validity").includes("POC") || $('#ddlLicenseKey option:selected').attr("validity").includes("UAT")) {
            $(".renewchild").show()
            $(".AMC").hide()
            $(".Upgrade").show()
        } else {
            $(".AMC").show()
            $(".renewchild").show()
            $(".Upgrade").show()
          
        }
    });
    function licenseDebounce(func, delay = 300) {
        let timer;
        return function () {
            const context = this;
            const args = arguments;
            clearTimeout(timer);
            timer = setTimeout(() => {
                func.apply(context, args);
            }, delay);
        };
    } 

    $(document).on('click', '.check_license', licenseDebounce(async function () {
        if (!$(this).val()) {
            $("#encryptlicensecheck").text("Please select a type").addClass('field-validation-error');
            return false;
        } else {
            $("#encryptlicensecheck").text("").removeClass('field-validation-error');
            return true;
        }
    }))
    $(document).on('click', '#generate', licenseDebounce(async function () {
        let id = selectedLicenseId        
        let selectedType = $('input[name="type"]:checked').val();        
        let renewalType = $('input[name="renewtype"]:checked').val();        
        let amcType = $('input[name="amctype"]:checked').val();        
        if (!selectedType) {
            $("#encryptlicensecheck").text("Please select a type").addClass('field-validation-error');
            return false;
        } else {
            $("#encryptlicensecheck").text("").removeClass('field-validation-error');
            await $.ajax({
                url: RootUrl + "Admin/LicenseManager/GetLicenseGeneratorKeyById",
                dataType: "json",
                data: { id: id, type: selectedType, renewalType: renewalType ? renewalType : amcType },
                type: "GET",
                success: function (response) {                
                    if (response && response.success && response.data) {    
                        $("#copy_request").removeClass('d-none');
                        $('#encryptlicense').text(response.data);
                        $(".myDiv").hide();
                        $("#generate").hide();
                        $('.myDiv2').removeClass('d-none');
                    }
                },
            });
        } 
    },500));
    $(document).on('click', '.cp-copy', function () {
        let licenseKey = $('#encryptlicense').text();
        let $tooltip = $(this).find('.tooltip-text');
        if (licenseKey) {
            navigator.clipboard.writeText(licenseKey).then(() => {
                $tooltip.removeClass('d-none fade-out').addClass('fade-in');
                // Hide the tooltip after a short delay
                setTimeout(() => {
                    $tooltip.removeClass('fade-in').addClass('fade-out');
                }, 2000);
            });
        }
    })    
    $('[data-bs-target="#CreateModal"]').on('click', function () {
        clearInputFields();
    });

    const errorElements = ['#License-error'];

    const clearInputFields = () => {
        $('#licensekey').val('');
        $('#RenewFunction').text('Save');
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    };

    var isParentValue = document.getElementById('backToLoginLink').getAttribute('data-isParent');

    var isLicensetValue = document.getElementById('backToLoginLink').getAttribute('data-isLicense');

    var licenseKeyButton = document.getElementById('licenseKeyButton');

    var backToLoginLink = document.getElementById("backToLoginLink");

    var header = document.getElementById('txtheader');
    var p = document.getElementById('txtparagraph');

    if (isParentValue === "True") {
        backToLoginLink.style.display = "none";
    }
    else {
        header.textContent = "Derived license not configured.";
        p.textContent = "Kindly contact CP Admin";
        licenseKeyButton.style.display = "none";
    }

});
   async function validateLandingLicenseKey(value) {
        const errorElement = $('#License-error');
   
        if (!value) {
            errorElement.text('Enter license key').addClass('field-validation-error');
            return false;
        }
        else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }   
}

const dynamicButton = (isDisable) => {
    $("#RenewFunction").css({
        "cursor": isDisable ? "none" : "",
        "pointer-events": isDisable ? "none" : "",
        "opacity": isDisable ? "0.5" : ""
    });
}
function handlePaste() {
    if ($('#licensekey').val().length > 30) {
        dynamicButton(false)
    } else {
        dynamicButton(true)
    }
}

$("#RenewFunction").on('click', async function () {
    let form = $("#LicenseLanding")
    var licenseKey = $("#licensekey").val();
    var isLicenseKey = await validateLandingLicenseKey(licenseKey);
    if (isLicenseKey) {
        dynamicButton(true)
      
        let data = {
            id: "",
            LicenseKey: $("#licensekey").val(),
            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
        }
        await $.ajax({
            type: "POST",
            url: RootUrl + "Admin/LicenseManager/CreateOrUpdate",
            data: data,
            dataType: 'json',
            success: function (result) {
                let data = result           
                if (result.success == true) {
                    $("#CreateModal").modal("hide")
                    LicenseLandingnotificationAlert("success", data.message)
                    if (window.location.pathname == "Admin/LicenseManager/LicenseLanding") {
                        setTimeout(() => {
                            window.location.href = "/Dashboard/ServiceAvailability/List";
                        }, 2000)
                    } else {
                        setTimeout(() => {
                            window.location.href = "/Account/Logout";
                        }, 2000)
                    }
                 
                } else {              
                    licenseerrorNotification(data)
                }
            },
        })
        $("#licensekey").val("")
    }
});
$('#licensekey').on('keyup', async function () {
    var licenseId = $('#licensekey').val();
    const value = $(this).val();
    await validateLandingLicenseKey(value, licenseId);
    if (licenseId.length > 30) {
        dynamicButton(false)
    } else {
        dynamicButton(true)
    }
}); 
const licenseerrorNotification = (data) => {
    if (data.hasOwnProperty('ErrorCode')) {
        if (data.ErrorCode === 1001) {
            LicenseLandingnotificationAlert("unauthorised", 'Session expired')
            setTimeout(() => {
                window.location.assign(RootUrl + 'Account/Logout');
            }, 2000)
        }
    } else {
        LicenseLandingnotificationAlert("warning", data.message)
    }
}
const LicenseLandingnotificationAlert = (toastClass, data, mode = '') => {
    //$('#mytoastrdata').toast('hide');
    $('#alertClass, #icon_Detail').removeClass();
    let alertClass = toastClass === "success" ? "success-toast" : toastClass === "warning" ? "warning-toast" : toastClass === "info" ? "info-toast"
        : toastClass === "error" || "unauthorised" ? "unauthorised-toast" : '';
    let icon = toastClass === "success" ? "cp-check" : toastClass === "warning" ? "cp-warning" : toastClass === "info" ? "cp-note"
        : toastClass === "error" ? "cp-close" : toastClass === "unauthorised" ? "cp-user" : '';
    if (mode?.toLowerCase() == 'execution') {
        $('#notificationAlertmessage').append(data)
    } else {
        $('#alertClass').addClass(alertClass)
        $("#icon_Detail").addClass(`${icon} toast_icon`)
        $('#notificationAlertmessage').text(data)
    }
    if (data) $('#mytoastrdata').toast('show');
    let hideTimeout = setTimeout(function () {
        $('#mytoastrdata').toast('hide');
    }, 2000);
    $('#mytoastrdata')
        .on('mouseenter', function () {
            clearTimeout(hideTimeout);
        })
        .on('mouseleave', function () {
            hideTimeout = setTimeout(function () {
                $('#mytoastrdata').toast('hide');
            }, 2000);
        });
}