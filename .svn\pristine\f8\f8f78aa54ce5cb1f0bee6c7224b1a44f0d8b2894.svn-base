﻿using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.UpdateConditionActionId;
using ContinuityPatrol.Application.Features.CGExecutionReport.Queries.GetCgExecutionReportPaginatedList;
using ContinuityPatrol.Application.Features.EscalationMatrix.Command.Create;
using ContinuityPatrol.Application.Features.EscalationMatrix.Command.Update;
using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Domain.ViewModels.CGExecutionReportModel;
using ContinuityPatrol.Domain.ViewModels.EscalationMatrix;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Report.ReportTemplate;
using ContinuityPatrol.Web.Attributes;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.Areas.CyberResiliency.Controllers;
[Area("CyberResiliency")]

public class CGExecutionReportController : Controller
{
    public readonly IPublisher _publisher;
    public static ILogger<CGExecutionReportController> _logger;
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;
    public static string CompanyLogo { get; set; }


    public CGExecutionReportController(IPublisher publisher, ILogger<CGExecutionReportController> logger, IDataProvider dataProvider, IMapper mapper)
    {
        _publisher = publisher;
        _logger = logger;
        _dataProvider = dataProvider;
        _mapper = mapper;
    }
    public IActionResult List()
    {
        return View();
    }
    [HttpGet]
    public async Task<JsonResult> GetPagination(GetCgExecutionPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in CGExecutionReport");
        try
        {
            var paginatedList = await _dataProvider.CGExecutionReport.GetCgExecutionPaginatedList(query);
            _logger.LogDebug("Successfully retrieved alert paginatedList in CGExecutionReport");

            return Json(new { Success = true, data = paginatedList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on CGExecutionReport page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<IActionResult> DownloadReport(string workflowOperationId, string workflowName)
    {
        _logger.LogInformation("Enter into Scheduler Job Workflow DownloadReport method");
        var reportsDirectory = "";
        try
        {
            var cgExecutionList = await _dataProvider.Report.GetCGExecutionReport(workflowOperationId);

            if (cgExecutionList is not null && cgExecutionList.CGExecutionReportVm.Count > 0)
            {
                _logger.LogInformation("CG Execution Report List not null");
                CompanyLogo = string.Empty;
                var companyDetails = await _dataProvider.Company.GetCompanyById(WebHelper.UserSession.CompanyId);
                if (!string.IsNullOrEmpty(companyDetails.CompanyLogo) && companyDetails.CompanyLogo != "NA") { CompanyLogo = companyDetails.CompanyLogo; }

                var reportData = JsonConvert.SerializeObject(cgExecutionList);
                XtraReport report = new CGExecutionReport(reportData);
                var filenameSuffix = DateTime.Now.ToString("MMddyyyyhhmmsstt");
                var fileName = "CGExecutionReport" + "_" + filenameSuffix + ".pdf";
                reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);
                report.ExportToPdf(reportsDirectory);
                var fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);
                _logger.LogDebug("download initaiated for CG Execution Report");

                await _publisher.Publish(new ReportViewedEvent { ActivityType = ActivityType.Generate.ToString(), ReportName = "CGExecutionReport" });
                return File(fileBytes, "application/pdf", fileName);
            }
            else
            {
                _logger.LogInformation("CG Execution Report null");
                return Json(new { success = false, message = "No data found" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"CG Execution Download Report method throw exception {ex.Message}");
            return Json(new { success = false, message = "Downloading report error" });
        }
        finally
        {
            if (System.IO.File.Exists(reportsDirectory))
            {
                System.IO.File.Delete(reportsDirectory);
            }
        }
    }

    [HttpPost]
    [AntiXss]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateCondition(ResiliencyReadinessWorkflowScheduleLogCommand command)
    {
        try
        {
            var response = await _dataProvider.CGExecutionReport.Update(command);
            _logger.LogDebug("Successfully retrieved alert paginatedList in WorkflowScheduleExecutionHistory");
            return Json(new { Success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.LogError("CG Execution Download Report method throw exception");
            return ex.GetJsonException();
        }
    }
}
