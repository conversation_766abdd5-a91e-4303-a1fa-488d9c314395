using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AlertFixture : IDisposable
{
    public List<Alert> AlertPaginationList { get; set; }
    public List<Alert> AlertList { get; set; }
    public Alert AlertDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public AlertFixture()
    {
        var fixture = new Fixture();

        AlertList = fixture.Create<List<Alert>>();

        AlertPaginationList = fixture.CreateMany<Alert>(20).ToList();

        AlertPaginationList.ForEach(x => x.CompanyId = CompanyId);

        AlertList.ForEach(x => x.CompanyId = CompanyId);

        AlertDto = fixture.Create<Alert>();

        AlertDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
