using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Create;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Update;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.HacmpClusterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class HacmpClusterService : BaseClient, IHacmpClusterService
{
    public HacmpClusterService(IConfiguration config, IAppCache cache, ILogger<HacmpClusterService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<HacmpClusterListVm>> GetHacmpClusterList()
    {
        var request = new RestRequest("api/v6/hacmpclusters");

        return await GetFromCache<List<HacmpClusterListVm>>(request, "GetHacmpClusterList");
    }

    public async Task<BaseResponse> CreateAsync(CreateHacmpClusterCommand createHacmpClusterCommand)
    {
        var request = new RestRequest("api/v6/hacmpclusters", Method.Post);

        request.AddJsonBody(createHacmpClusterCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateHacmpClusterCommand updateHacmpClusterCommand)
    {
        var request = new RestRequest("api/v6/hacmpclusters", Method.Put);

        request.AddJsonBody(updateHacmpClusterCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/hacmpclusters/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<HacmpClusterDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/hacmpclusters/{id}");

        return await Get<HacmpClusterDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsHacmpClusterNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/hacmpclusters/name-exist?hacmpclusterName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<HacmpClusterListVm>> GetPaginatedHacmpClusters(GetHacmpClusterPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/hacmpclusters/paginated-list");

      return await Get<PaginatedResult<HacmpClusterListVm>>(request);
  }
   #endregion
}
