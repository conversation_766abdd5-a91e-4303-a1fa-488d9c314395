namespace ContinuityPatrol.Domain.Entities;

public class CyberAirGap : AuditableEntity
{
    public string Name { get; set; }
	public string Description { get; set; }
    public string SourceSiteId { get; set; }
    public string SourceSiteName { get; set; }
    public string TargetSiteName { get; set; }
    public string TargetSiteId { get; set; }
    public int Port{ get; set;}
    [Column(TypeName = "NCLOB")] public string Source { get; set; }
    [Column(TypeName = "NCLOB")] public string Target { get; set; }
    public string SourceComponentId { get; set; }
    public string SourceComponentName { get; set; }
    public string TargetComponentId { get; set; }
    public string TargetComponentName { get; set; }
    public string WorkflowStatus { get; set; }
    public string EnableWorkflowId { get; set; }
    public string DisableWorkflowId { get; set; }
    [Column(TypeName = "NCLOB")] public string ErrorMessage { get; set; }
    public DateTime StartTime { get; set; }
	public DateTime EndTime { get; set; }
	public string RPO { get; set; }
	public bool IsAttached { get; set; }

    public string Status { get; set; }

}
