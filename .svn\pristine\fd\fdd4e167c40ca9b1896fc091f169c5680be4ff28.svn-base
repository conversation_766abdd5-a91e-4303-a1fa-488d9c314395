﻿let businessServiceData = {}, siteProperties = {}, firstSiteId = [], validateProperty = false, globalOperationalService = '', clearId = '';

// URL
const businessServiceURL = {
    Pagination: "/Configuration/OperationalService/GetPagination",
    companyNameUrl : 'Configuration/Company/GetCompanies',
    siteListByCompanyIdUrl :'Configuration/OperationalService/GetSiteByTypeAndCompanyId',
    sameNameExistUrl : 'Configuration/OperationalService/IsBusinessServiceNameExist',
    siteTypeListUrl : 'Configuration/SiteType/GetSiteTypeList',
    siteListfiltered : 'Configuration/Site/GetSiteList',
    siteListss: 'Configuration/Site/GetSiteById',
    saveOrUpdate:'Configuration/OperationalService/CreateOrUpdate'
}

const exceptThisSymbol = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "+", "=", "{", "}", "[", "]", "|", ":", ";", "'", "\"", "<", ">", "?", "/", "\\"];

$(function () {

    let createPermission = $("#configurationCreate").data("create-permission").toLowerCase();
    let deletePermission = $("#configurationDelete").data("delete-permission").toLowerCase();

    if (createPermission == 'false') {
        $("#business-service-createbutton").removeClass('#business-service-createbutton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }

    let selectedValues = [];
    let dataTable = $('#tblBusinessService').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }
                , infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": businessServiceURL.Pagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "companyName" : sortIndex === 3 ? "priority" :
                        sortIndex === 4 ? "status" : "";
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                 
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json.data.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data;
                },
            },
            "columnDefs": [
                {
                    "targets": [1],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "companyName", "name": "Company Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "priority",
                    "name": "Priority",
                    "autoWidth": true,
                    "render": function (data, type, row) {

                        if (type === 'display' && (data == 1 || data == 2 || data == 3)) {
                            let iconClass = data == 1 ? "cp-up-doublearrow text-danger  me-1" :
                                data == 2 ? "cp-equal text-warning  me-1" :
                                    "cp-down-doublearrow text-info me-1";
                            let tooltip = data == 1 ? "High" : data == 2 ? "Medium" : "Low";
                            return ` <span > <i class="${iconClass}"></i> ${tooltip}
                            </span>`;
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                       <div class="d-flex align-items-center  gap-2">
                           <span role="button" title="Edit" class="edit_businessService-button" data-businessService='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete" class="delete-bservice-button" data-businessservice-id="${row.id}" data-businessservice-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>                                        
                                </div>`;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                       <div class="d-flex align-items-center  gap-2">
                       
                                            <span role="button" title="Edit" class="edit_businessService-button" aria-disabled="true" data-businessService='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span> 
                                                <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>  
                                            
                                </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                       <div class="d-flex align-items-center  gap-2">
                       
                                           <span role="button" title="Edit" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>  
                                                <span role="button" title="Delete" class="delete-bservice-button" data-businessservice-id="${row.id}" data-businessservice-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>  
                                            
                                </div>`;
                        }
                        else {
                            return `
                       <div class="d-flex align-items-center  gap-2">
                       
                                           <span role="button" class="icon-disabled"><i class="cp-edit"></i>
                                            </span>  
                                                <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>  
                                            
                                </div>`;
                        }
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart; 
                let counter = startIndex + index + 1; 
                $('td:eq(0)', row).html(counter); 
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $(document).on('keydown input', '#search-inp', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const NameCheckbox = $("#Name");
        const CompanyNameCheckbox = $("#CompanyName");
        
        const inputValue = $('#search-inp').val();
        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }
        if (CompanyNameCheckbox.is(':checked')) {
            selectedValues.push(CompanyNameCheckbox.val() + inputValue);
        }
        var currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {

                if (e.target.value && json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }
    }, 500));

    async function GetCompanyNames() {

        await $.ajax({
            type: "GET",
            url: RootUrl + businessServiceURL.companyNameUrl,
            data: {},
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result) {
                    $('#CompanyNames').empty()
                    $('#CompanyNames').append('<option value=""></option>');

                    if (result && result?.data?.length) {
                        result?.data?.forEach((s) => {
                            $('#CompanyNames').append('<option id="' + s.id + '"  value="' + s.displayName + '">' + s.displayName + '</option>');
                        })
                    }
                }

                if (Object.keys(businessServiceData).length) {
                    $('#CompanyNames').val(businessServiceData.companyName)
                    $('#BusinessServiceCompanyIdtxt').val(businessServiceData.companyId);
                }
            },
        });
    }

    async function GetSiteListByCompanyId(selectedCompanyId, siteProperties = {}) {

        
        let data = {
            'companyId': selectedCompanyId,
            'siteType': ""
        };
        let siteList = await GetAsync(RootUrl + businessServiceURL.siteListByCompanyIdUrl, data, OnError);

        let groupedData = [];

        siteList.forEach(site => {
            if (site.type && site.location) {

                if (groupedData.length) {
                    let checkSite = groupedData.find((d) => d.type == site.type)
                    if (checkSite) {
                        let filterd = groupedData.filter((x) => x.type === site.type)
                        let findIndex = groupedData.indexOf(filterd[0])
                        groupedData[findIndex].property.push(site)

                    } else {
                        groupedData.push({ 'type': site.type, 'id': site.typeId, property: [site] })
                    }

                } else {
                    groupedData.push({ 'type': site.type, 'id': site.typeId, property: [site] })



                }
            }
        });
        let name = $('.siteList');

        for (let i = 0; i < name?.length; i++) {
            let id = name[i]?.getAttribute('id');
            let type = name[i]?.getAttribute('data-type');

            if (id) {

                let filteredData = groupedData?.length && groupedData.filter((group) => group?.id == id);

                if (!filteredData?.length) {
                    $("#" + id).empty();
                } else {
                    $("#" + id).empty();

                    let uniqueLocations = new Set();

                    filteredData[0]?.property?.forEach(site => {
                        if (!uniqueLocations.has(site.location)) {
                            uniqueLocations.add(site.location);
                            
                            $("#" + id).append('<option>', {
                                value: '',
                                text: ''
                            });

                            $("#" + id).append($('<option>', {
                                value: site.location,
                                text: site.location,
                                'data-id': JSON.stringify(site),
                            }));
                        }
                    });

                }
            }

            if (siteProperties && Object.keys(siteProperties)?.length) {
                // Assuming 'type' and 'id' are properly defined somewhere in your scope
                if (siteProperties[type]) {
                    $("#" + id).val(siteProperties[type]?.Location);
                    
                    // Only push 'id' to firstSiteId if it's not already in the array
                    if (!firstSiteId.includes(id)) {
                        firstSiteId.push(id);
                    } else {
                        console.log("Duplicate ID, not adding:", id); // Optional: log for debugging
                    }
                }
            }
        }

        return groupedData;

    }


    //CreateFunction
    $("#business-service-createbutton").on('click', async function () {
        await GetCompanyNames();

    });
    
    // Update
    $('#tblBusinessService').on('click', '.edit_businessService-button', function () {
        businessServiceData = $(this).data('businessservice');
        populateModalFields(businessServiceData);
        $("#SaveBusinessService").text("Update");
        $('#CreateModal').modal('show');
    });

    //deleteFunction
    $('#tblBusinessService').on("click", ".delete-bservice-button", function () {
        let businessServiceId = $(this).data('businessservice-id');
        let businessServiceName = $(this).data('businessservice-name');
        $("#deletebusinessservicename").attr("title", businessServiceName).text(businessServiceName);
        $("#deletebusinessberviceid").val(businessServiceId);
    });

    //PriorityCheck
    $(document).on('change', '.priorityBtn', function () {
        $('.priorityBtn').prop('checked', false)
        let value = $(this).val()
        $(this).prop('checked', true)
        $("#btnradio").val(value)
        validateCritical(value, "select critical level");
    })

   
    
    //ValidationForName
    $('#BusinessServiceNametxt').on('keyup', commonDebounce(async function () {
        let businessServiceId = $('#updateBSIdtxt').val().trim();
        let value = $(this).val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await validateName(sanitizedValue, businessServiceId, businessServiceURL.sameNameExistUrl);
    }, 400));

    // Description
    $('#BusinessServiceDescriptiontxt').on('input', async function (event) {
        const value = $(this).val();
        const sanitizedValue = value.replace(/^\s+/, '').replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        if (exceptThisSymbol.includes(event.key)) {
            event.preventDefault();
        }
        let errorElement = $('#Description-error');
        await validateDescription(sanitizedValue, "Should not allow more than 250 characters", errorElement);
    });
   

    $(".clearBtn").on('click', async function () {         
        let value = $(this).data('btn');
        clearId = $(this).data('id');       
      $(this).parent().find('select').val('').trigger('change')
        if (siteProperties[value]) {
            delete siteProperties[value];
        }
       
        
    });

  
    $(".siteList").on('change', async function () {
        let selectedOption = $(this).find(':selected');
        let siteData = selectedOption.data('id');

        if (siteData && siteData.typeId && !firstSiteId.includes(siteData.typeId)) {
            firstSiteId.push(siteData.typeId);
            clearId = '';
        }
        if (clearId !== '') {
            firstSiteId = firstSiteId.filter(id => id !== clearId);
        }
        
        let errorElementProperties = $('#SiteProperty-error');
        await validateSiteProperty(firstSiteId, "Select sites", errorElementProperties);

        if (siteData && siteData.id && siteData.type) {
            let siteProperty = {
                Id: siteData.id,
                Name: siteData.name,
                Location: selectedOption.val(),
                Lng: siteData.lng,
                Lat: siteData.lat,
                category: siteData.category,
                TypeId: siteData.typeId,
                type: siteData.type
            };

            // Replace or add the entry for this type
            siteProperties[siteData.type] = siteProperty;
        }
    });



    $('#CompanyNames').on('change', async function () {
        firstSiteId = [];
        let selectedCompanyId = $(this).find(':selected').attr('id');
        let company = $("#CompanyNames").val();
        $('#sitePropertiesInput').val('').trigger("change");
        let errorElementCompany = $('#SelectCompanyName-error');
        await validateDropDown(company, "Select company name", errorElementCompany);
        $("#BusinessServiceCompanyIdtxt").val(selectedCompanyId);
        GetSiteListByCompanyId(selectedCompanyId);
       
    })
    
    // Save Function
    $("#SaveBusinessService").on('click', commonDebounce(async function () {
        let form = $("#CreateForm-BusinessService");
        let name = $("#BusinessServiceNametxt").val();
        let id = $("#updateBSIdtxt").val();
        let company = $("#CompanyNames").val();
        elementDescription = $("#BusinessServiceDescriptiontxt").val();
        errorElement = $('#Description-error');
        let isDescription = await validateDescription(elementDescription, "Between 3 to 250 characters", errorElement);
        elementCritical = $(".priorityBtn:checked").val();
        let isCriticalValue = await validateCritical(elementCritical, "Select critical level");
        let errorElementCompany = $('#SelectCompanyName-error');
        errorElementProperties = $('#SiteProperty-error');
        let validateBserviceName = await validateName(name, id, businessServiceURL.sameNameExistUrl);
        let validateCompany = await validateDropDown(company, "Select company name", errorElementCompany);
        validateProperty = await validateSiteProperty(firstSiteId, "Select sites", errorElementProperties);
        
        let businessServiceSanitizeArray = ['BusinessServiceNametxt', 'updateBSIdtxt', 'BusinessServiceDescriptiontxt', 'BusinessServiceCompanyIdtxt', 'CompanyNames', 'btnradio', 'sitePropertiesInput']
        sanitizeContainer(businessServiceSanitizeArray)
        setTimeout(() => {
            if (validateBserviceName && isDescription && validateCompany && isCriticalValue && validateProperty) {
                console.log(siteProperties)
                let jsonOutput = JSON.stringify(siteProperties);
                $("#sitePropertiesInput").text(jsonOutput);
                $("#sitePropertiesInput").val(jsonOutput);
                /*  form.trigger('submit');*/
                savePermission()
            }
        }, 200)        
    }, 800));
    const savePermission = async () => {

        let data = {
            "CompanyId": $("#BusinessServiceCompanyIdtxt").val(),
            "CompanyName": $("#CompanyNames").val(),
            "Description": $("#BusinessServiceDescriptiontxt").val(),
            "Id": $("#updateBSIdtxt").val(),
            "Name": $("#BusinessServiceNametxt").val(),
            "Priority": $(".priorityBtn:checked").val(),
            "SiteProperties": $("#sitePropertiesInput").val(),
            "BusinessServices": null, 
            "SiteTypes": null,
            __RequestVerificationToken: gettoken()
        }
        $('#SaveBusinessService').text() === "Update" ? data["id"] = globalOperationalService : null
        
        await $.ajax({
            type: "POST",
            url: RootUrl + businessServiceURL.saveOrUpdate,
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result.data
                if (result.success) {
                    notificationAlert("success", data.message)
                    $('#CreateModal').modal('hide');
                    setTimeout(() => {                      
                        dataTable.ajax.reload()
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            },
            error: function (response) {
                errorNotification(response)
            }
        })
    }
   
    //Clear data
    $('#business-service-createbutton').on('click', async function () {
        $(".siteList").empty()
        let errorElements = ['#Name-error', '#Description-error', '#SelectCompanyName-error', '#Priority-error', '#SiteProperty-error'];
        clearInputFields('CreateForm-BusinessService', errorElements);
        $(".priorityBtn").prop('checked', false);
        firstSiteId = [], siteProperties = {};      
        $('#SaveBusinessService').prop('disabled', false).text('Save');         
    });

    $('.edit_businessService-button').on('click', async function () {
        let errorElements = ['#Name-error', '#Description-error', '#SelectCompanyName-error', '#SelectPriority-error', '#SiteProperty-error'];

        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    });

    async function validateName(value, id = null, url) {
        let errorElement = $('#Name-error');

        if (!value) {
            errorElement.text('Enter operational service name')
                .addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        var url = RootUrl + url;
        let data = {};
        data.name = value;
        data.id = id;

        let validationResults = [
            await SpecialCharValidateCustom(value),
            await ShouldNotBeginWithSpace(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithDotAndHyphen(value),
            await ShouldNotConsecutiveDotAndHyphen(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
             await ShouldNotEndWithSpace(value),
            await ShouldNotAllowMultipleSpace(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsSameNameExist(url, data)
        ];

        return await CommonValidation(errorElement, validationResults);
    }
     async function IsSameNameExist(url, inputValue) {
       return !inputValue.name.trim() ? true : (await GetAsync(url, inputValue, OnError)) ? "Name already exists" : true;
     }


    async function validateDescription(value, errorMessage) {
        const errorElement = $('#Description-error');
        if (!value) {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        } else if (value.length < 0) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        } else if (value.length > 250) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        }
        else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }

    function validateCritical(value, errorMessage) {
        const errorElement = $('#Priority-error');
        if (!value) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        }
        else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }

    }

function validateDropDown(value, errorMessage, errorElement) {
    if (!value) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}

    async function validateSiteProperty(value, errorMessage, errorElement) {
        
        if (value.includes('1738f813-6090-40cc-8869-25741a156f73') &&
            value.includes('2914c9b2-91d3-4e03-baed-77824f16327c')) {
            if (!value.length) {
                errorElement.text(errorMessage).addClass('field-validation-error');
                return false;
            } else {
                errorElement.text('').removeClass('field-validation-error');
                return true;
            }
        } else if (value.includes('1738f813-6090-40cc-8869-25741a156f73')) {
            errorElement.text('Select DR site').addClass('field-validation-error');
            return false;
        } else if (value.includes('2914c9b2-91d3-4e03-baed-77824f16327c')) {
            errorElement.text('Select PR site').addClass('field-validation-error');
            return false;
        } else {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        }
    }

    // Edit 
    async function populateModalFields(businessServiceData) {
        selectedCompanyId = businessServiceData.companyId
        siteProperties = businessServiceData?.siteProperties ? JSON.parse(businessServiceData?.siteProperties) : {}
        GetCompanyNames();
        $('#BusinessServiceNametxt').val(businessServiceData.name);
        $('#BusinessServiceDescriptiontxt').val(businessServiceData.description);
        await GetSiteListByCompanyId(businessServiceData.companyId, siteProperties);
        $('.priorityBtn').prop('checked', false)
        let criticalLevel = businessServiceData.priority;
        $(this).prop('checked', true)
        $('.priorityBtn[value="' + criticalLevel + '"]').prop("checked", true);
        $("#btnradio").val(criticalLevel);

        $('#updateBSIdtxt').val(businessServiceData.id);
        let errorElement = ['#Name-error', '#Description-error', '#SelectCompanyName-error', '#Priority-error', '#SiteProperty-error'];
        errorElement.forEach(element => {
            $(element).text('').removeClass('field-validation-error')
        });
    }
});
