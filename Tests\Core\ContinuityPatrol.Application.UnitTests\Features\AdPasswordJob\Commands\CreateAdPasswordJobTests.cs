using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordJob.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Infrastructure.Contract;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordJob.Commands;

public class CreateAdPasswordJobTests : IClassFixture<AdPasswordJobFixture>
{
    private readonly AdPasswordJobFixture _adPasswordJobFixture;
    private readonly Mock<IAdPasswordJobRepository> _mockAdPasswordJobRepository;
    private readonly Mock<ILoadBalancerRepository> _mockLoadBalancerRepository;
    private readonly Mock<IJobScheduler> _mockJobScheduler;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly CreateAdPasswordJobCommandHandler _handler;

    public CreateAdPasswordJobTests(AdPasswordJobFixture adPasswordJobFixture)
    {
        _adPasswordJobFixture = adPasswordJobFixture;

        _mockAdPasswordJobRepository = AdPasswordJobRepositoryMocks.CreateAdPasswordJobRepository(_adPasswordJobFixture.AdPasswordJobs);
        _mockLoadBalancerRepository = AdPasswordJobRepositoryMocks.CreateLoadBalancerRepository(_adPasswordJobFixture.LoadBalancers);
        _mockJobScheduler = new Mock<IJobScheduler>();
        _mockMapper = new Mock<IMapper>();
        _mockPublisher = new Mock<IPublisher>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<Domain.Entities.AdPasswordJob>(It.IsAny<CreateAdPasswordJobCommand>()))
            .Returns((CreateAdPasswordJobCommand cmd) => new Domain.Entities.AdPasswordJob
            {
                DomainServerId = cmd.DomainServerId,
                DomainServer = cmd.DomainServer,
                State = cmd.State,
                IsSchedule = cmd.IsSchedule,
                ScheduleType = cmd.ScheduleType,
                CronExpression = cmd.CronExpression,
                ScheduleTime = cmd.ScheduleTime,
                NodeId = cmd.NodeId,
                NodeName = cmd.NodeName,
                ExceptionMessage = cmd.ExceptionMessage
            });

        _handler = new CreateAdPasswordJobCommandHandler(
            _mockJobScheduler.Object,
            _mockMapper.Object,
            _mockAdPasswordJobRepository.Object,
            _mockPublisher.Object,
            _mockLoadBalancerRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_CreateAdPasswordJobResponse_When_AdPasswordJobCreated()
    {
        // Arrange
        var command = _adPasswordJobFixture.CreateAdPasswordJobCommand;

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(CreateAdPasswordJobResponse));
        result.Id.ShouldNotBeNullOrEmpty();
        result.Message.ShouldContain("AdPasswordJob");
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var command = _adPasswordJobFixture.CreateAdPasswordJobCommand;

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockAdPasswordJobRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.AdPasswordJob>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_PublishEvent_OnlyOnce()
    {
        // Arrange
        var command = _adPasswordJobFixture.CreateAdPasswordJobCommand;

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(It.IsAny<AdPasswordJobCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_ScheduleJob_When_NodeConfigurationExists()
    {
        // Arrange
        var command = _adPasswordJobFixture.CreateAdPasswordJobCommand;

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockJobScheduler.Verify(x => x.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Not_Call_ScheduleJob_When_NodeConfigurationNotExists()
    {
        // Arrange
        var command = _adPasswordJobFixture.CreateAdPasswordJobCommand;
        _mockLoadBalancerRepository.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((Domain.Entities.LoadBalancer)null);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockJobScheduler.Verify(x => x.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var command = _adPasswordJobFixture.CreateAdPasswordJobCommand;

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<Domain.Entities.AdPasswordJob>(It.IsAny<CreateAdPasswordJobCommand>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_GetNodeConfiguration_Twice_When_FirstReturnsNull()
    {
        // Arrange
        var command = _adPasswordJobFixture.CreateAdPasswordJobCommand;
        _mockLoadBalancerRepository.SetupSequence(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((Domain.Entities.LoadBalancer)null)
            .ReturnsAsync(_adPasswordJobFixture.LoadBalancers.First());

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockLoadBalancerRepository.Verify(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()), Times.Exactly(2));
    }
}
