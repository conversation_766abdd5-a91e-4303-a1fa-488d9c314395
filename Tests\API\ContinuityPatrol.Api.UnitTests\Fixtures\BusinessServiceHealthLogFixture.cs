using AutoFixture;
using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceHealthLogModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class BusinessServiceHealthLogFixture : IDisposable
{
    public CreateBusinessServiceHealthLogCommand CreateBusinessServiceHealthLogCommand { get; set; }
    public UpdateBusinessServiceHealthLogCommand UpdateBusinessServiceHealthLogCommand { get; set; }
    public List<BusinessServiceHealthLogListVm> BusinessServiceHealthLogListVm { get; set; }
    public BusinessServiceHealthLogDetailVm BusinessServiceHealthLogDetailVm { get; set; }

    public BusinessServiceHealthLogFixture()
    {
        CreateBusinessServiceHealthLogCommand = AutoBusinessServiceHealthLogFixture.Create<CreateBusinessServiceHealthLogCommand>();
        UpdateBusinessServiceHealthLogCommand = AutoBusinessServiceHealthLogFixture.Create<UpdateBusinessServiceHealthLogCommand>();
        BusinessServiceHealthLogListVm = AutoBusinessServiceHealthLogFixture.CreateMany<BusinessServiceHealthLogListVm>(3).ToList();
        BusinessServiceHealthLogDetailVm = AutoBusinessServiceHealthLogFixture.Create<BusinessServiceHealthLogDetailVm>();
    }

    public Fixture AutoBusinessServiceHealthLogFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customize<CreateBusinessServiceHealthLogCommand>(c => c
                .With(b => b.ConfiguredCount, 50)
                .With(b => b.DRReadyCount, 45)
                .With(b => b.DRNotReadyCount, 5)
                .With(b => b.ProblemState, "All systems operational"));

            fixture.Customize<UpdateBusinessServiceHealthLogCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.ConfiguredCount, 60)
                .With(b => b.DRReadyCount, 55)
                .With(b => b.DRNotReadyCount, 5)
                .With(b => b.ProblemState, "Minor issues detected"));

            fixture.Customize<BusinessServiceHealthLogListVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.ConfiguredCount, () => fixture.Create<int>() % 100 + 20)
                .With(b => b.DRReadyCount, () => fixture.Create<int>() % 80 + 15)
                .With(b => b.DRNotReadyCount, () => fixture.Create<int>() % 20 + 1)
                .With(b => b.ProblemState, () => fixture.Create<bool>() ? "Operational" : "Issues Detected"));

            fixture.Customize<BusinessServiceHealthLogDetailVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.ConfiguredCount, 75)
                .With(b => b.DRReadyCount, 68)
                .With(b => b.DRNotReadyCount, 7)
                .With(b => b.ProblemState, "Critical Issues - Immediate Attention Required"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
