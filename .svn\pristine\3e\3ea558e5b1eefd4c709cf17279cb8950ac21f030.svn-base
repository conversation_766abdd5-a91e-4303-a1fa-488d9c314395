﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;


namespace ContinuityPatrol.Persistence.Repositories;

public class InfraObjectSchedulerLogsRepository : BaseRepository<InfraObjectSchedulerLogs>, IInfraObjectSchedulerLogsRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;
    
    public InfraObjectSchedulerLogsRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<InfraObjectSchedulerLogs>> GetInfraObjectSchedulerListByStartDateAndEndDate(string startDate, string endDate)
    {
       
        var result = await _dbContext.InfraObjectSchedulerLogs.Active()
            .Where(x => x.CreatedDate.Date >= startDate.ToDateTime().Date && x.CreatedDate.Date <= endDate.ToDateTime().Date)
            .DescOrderById()
            .ToListAsync();
        if (!_loggedInUserService.IsAllInfra)
        {
            var assignedBusinessInfraObjects = AssignedEntity?.AssignedBusinessServices
                     .SelectMany(assignedBusinessService => assignedBusinessService?.AssignedBusinessFunctions)
                     .SelectMany(assignedBusinessFunction => assignedBusinessFunction?.AssignedInfraObjects)
                     .Select(infraId => infraId.Id)
                     .ToList();
            if (assignedBusinessInfraObjects is null || !assignedBusinessInfraObjects.Any()) return new List<InfraObjectSchedulerLogs>();
            return result.Where(infraObject => assignedBusinessInfraObjects.Contains(infraObject.InfraObjectId)).ToList();

        }
        return result;
    }
    public override async Task<PaginatedResult<InfraObjectSchedulerLogs>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<InfraObjectSchedulerLogs> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await (_loggedInUserService.IsAllInfra
                ? MapInfraObjectScheduler(Entities.Specify(productFilterSpec).DescOrderById())
                : MapInfraObjectScheduler(GetPaginatedInfraObjectScheduler(Entities.Specify(productFilterSpec).DescOrderById()))).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        }
        return await (_loggedInUserService.IsAllInfra
            ? MapInfraObjectScheduler(Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())
            : MapInfraObjectScheduler(GetPaginatedInfraObjectScheduler(Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
            .DescOrderById()))).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public IQueryable<InfraObjectSchedulerLogs> GetPaginatedInfraObjectScheduler(
       IQueryable<InfraObjectSchedulerLogs> infraObjects)
    {
        var assignedInfraObjectIds = AssignedEntity.AssignedBusinessServices
            .SelectMany(businessService => businessService.AssignedBusinessFunctions)
            .SelectMany(businessFunction => businessFunction.AssignedInfraObjects)
            .Select(infraObject => infraObject.Id);

        return infraObjects.Where(infraObject => assignedInfraObjectIds.Contains(infraObject.InfraObjectId));
    }
    private IQueryable<InfraObjectSchedulerLogs> MapInfraObjectScheduler(IQueryable<InfraObjectSchedulerLogs> infraObjects)
    {
        var mappedInfra = infraObjects.Select(data => new
        {
            InfraObjectScheduler = data,
            WorkflowAction = _dbContext.WorkflowActionTypes.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.WorkflowTypeId)),
            BeforeWorkflow = _dbContext.Workflows.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.BeforeSwitchOverWorkflowId)),
            AfterWorkflow = _dbContext.Workflows.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.AfterSwitchOverWorkflowId)),
            GroupPolicy = _dbContext.GroupPolicies.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.GroupPolicyId)),
            InfraObject = _dbContext.InfraObjects.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.InfraObjectId))
        });

        var mappedInfraQuery = mappedInfra.Select(result => new InfraObjectSchedulerLogs
        {
            Id = result.InfraObjectScheduler.Id,
            ReferenceId = result.InfraObjectScheduler.ReferenceId,
            CompanyId = result.InfraObjectScheduler.CompanyId,
            InfraObjectId = result.InfraObject.ReferenceId,
            InfraObjectName = result.InfraObject.Name,
            WorkflowTypeId = result.WorkflowAction.ReferenceId,
            WorkflowType = result.WorkflowAction.ActionType,
            BeforeSwitchOverWorkflowId = result.BeforeWorkflow.ReferenceId,
            BeforeSwitchOverWorkflowName = result.BeforeWorkflow.Name,
            AfterSwitchOverWorkflowId = result.AfterWorkflow.ReferenceId,
            AfterSwitchOverWorkflowName = result.AfterWorkflow.Name,
            ScheduleType = result.InfraObjectScheduler.ScheduleType,
            ScheduleTime = result.InfraObjectScheduler.ScheduleTime,
            NodeId = result.InfraObjectScheduler.NodeId,
            NodeName = result.InfraObjectScheduler.NodeName,
            CronExpression = result.InfraObjectScheduler.CronExpression,
            IsEnable = result.InfraObjectScheduler.IsEnable,
            IsSchedule = result.InfraObjectScheduler.IsSchedule,
            WorkflowVersion = result.InfraObjectScheduler.WorkflowVersion,
            Status = result.InfraObjectScheduler.Status,
            GroupPolicyId = result.GroupPolicy.ReferenceId,
            GroupPolicyName = result.GroupPolicy.GroupName,
            ExecutionPolicy = result.InfraObjectScheduler.ExecutionPolicy,
            State = result.InfraObjectScheduler.State,
            ExceptionMessage = result.InfraObjectScheduler.ExceptionMessage,
            IsActive = result.InfraObjectScheduler.IsActive,
            CreatedBy = result.InfraObjectScheduler.CreatedBy,
            CreatedDate = result.InfraObjectScheduler.CreatedDate,
            LastModifiedBy = result.InfraObjectScheduler.LastModifiedBy,
            LastModifiedDate = result.InfraObjectScheduler.LastModifiedDate,
            LastExecutionTime = result.InfraObjectScheduler.LastExecutionTime,
        });

        return mappedInfraQuery;
    }
}
