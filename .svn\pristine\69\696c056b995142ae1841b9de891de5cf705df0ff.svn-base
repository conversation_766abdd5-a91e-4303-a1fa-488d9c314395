using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator.Helper;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Delete;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetList;
using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetRunningList;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class BulkImportOperationControllerTests : IClassFixture<BulkImportOperationFixture>
{
    private readonly BulkImportOperationFixture _bulkImportOperationFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly BulkImportOperationsController _controller;

    public BulkImportOperationControllerTests(BulkImportOperationFixture bulkImportOperationFixture)
    {
        _bulkImportOperationFixture = bulkImportOperationFixture;

        var testBuilder = new ControllerTestBuilder<BulkImportOperationsController>();
        _controller = testBuilder.CreateController(
            _ => new BulkImportOperationsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetBulkImportOperations_ReturnsExpectedList()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        //_controller.Cache.Remove(ApplicationConstants.Cache.AllBulkImportOperationCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBulkImportOperationListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_bulkImportOperationFixture.BulkImportOperationListVm);

        // Act
        var result = await _controller.GetBulkImportOperations();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var operations = Assert.IsAssignableFrom<List<BulkImportOperationListVm>>(okResult.Value);
        Assert.Equal(3, operations.Count);
    }

    [Fact]
    public async Task GetBulkImportOperations_ReturnsEmptyList_WhenNoOperationsExist()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
       // _controller.Cache.Remove(ApplicationConstants.Cache.AllBulkImportOperationCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBulkImportOperationListQuery>(), default))
            .ReturnsAsync(new List<BulkImportOperationListVm>());

        // Act
        var result = await _controller.GetBulkImportOperations();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var operations = Assert.IsAssignableFrom<List<BulkImportOperationListVm>>(okResult.Value);
        Assert.Empty(operations);
    }

    [Fact]
    public async Task GetBulkImportOperationById_ReturnsOperation_WhenIdIsValid()
    {
        // Arrange
        var operationId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBulkImportOperationDetailQuery>(q => q.Id == operationId), default))
            .ReturnsAsync(_bulkImportOperationFixture.BulkImportOperationDetailVm);

        // Act
        var result = await _controller.GetBulkImportOperationById(operationId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var operation = Assert.IsType<BulkImportOperationDetailVm>(okResult.Value);
        Assert.NotNull(operation);
    }

    [Fact]
    public async Task GetBulkImportOperationById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetBulkImportOperationById("invalid-guid"));
    }

    [Fact]
    public async Task CreateBulkImportOperation_Returns201Created()
    {
        // Arrange
        var command = _bulkImportOperationFixture.CreateBulkImportOperationCommand;
        var expectedMessage = $"BulkImportOperation '{command.Description}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBulkImportOperationResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBulkImportOperation(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBulkImportOperationResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task CreateBulkImportOperationValidator_ReturnsValidatorResponse()
    {
        // Arrange
        var command = _bulkImportOperationFixture.CreateBulkImportValidatorCommand;
        var expectedResponse = new CreateBulkImportValidatorCommandResponse
        {
            ValidatorResponse = new List<CreateBulkImportValidatorCommandResponseDetail>
            {
                new()
                {
                    InfraObjectName = "Test-InfraObject-01",
                    IsSwitchOver = true,
                    SwitchOverTemplate = "Test SwitchOver Template",
                    IsFailOver = false,
                    ServerCommand = new List<ValidationDetail>
                    {
                        new() { PropertyName = "Name", Name = "Server-01", Exception = null }
                    }
                }
            }
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateBulkImportOperationValidator(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<CreateBulkImportValidatorCommandResponse>>(result);
        var response = Assert.IsType<CreateBulkImportValidatorCommandResponse>(actionResult.Value);
        Assert.NotNull(response.ValidatorResponse);
        Assert.Single(response.ValidatorResponse);
        Assert.Equal("Test-InfraObject-01", response.ValidatorResponse.First().InfraObjectName);
    }

    [Fact]
    public async Task UpdateBulkImportOperation_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"BulkImportOperation '{_bulkImportOperationFixture.UpdateBulkImportOperationCommand.Description}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateBulkImportOperationCommand>(), default))
            .ReturnsAsync(new UpdateBulkImportOperationResponse
            {
                Message = expectedMessage,
                Id = _bulkImportOperationFixture.UpdateBulkImportOperationCommand.Id
            });

        // Act
        var result = await _controller.UpdateBulkImportOperation(_bulkImportOperationFixture.UpdateBulkImportOperationCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBulkImportOperationResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBulkImportOperation_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "BulkImportOperation 'Test Operation' has been deleted successfully!.";
        var operationId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBulkImportOperationCommand>(c => c.Id == operationId), default))
            .ReturnsAsync(new DeleteBulkImportOperationResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteBulkImportOperation(operationId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBulkImportOperationResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task CreateBulkImportOperationValidator_ValidatesBulkImportOperationList()
    {
        // Arrange
        var command = new CreateBulkImportValidatorCommand
        {
            BulkImportOperationList = null // Null list should cause validation error
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("BulkImportOperationList is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBulkImportOperationValidator(command));
    }

    [Fact]
    public async Task GetBulkImportOperationsRunningStatus_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBulkImportOperationRunningListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_bulkImportOperationFixture.BulkImportOperationRunningListVm);

        // Act
        var result = await _controller.GetBulkImportOperationsRunningStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var runningOperations = Assert.IsAssignableFrom<List<BulkImportOperationRunningListVm>>(okResult.Value);
        Assert.Single(runningOperations);
    }

    [Fact]
    public async Task CreateBulkImportOperation_ValidatesDescription()
    {
        // Arrange
        var command = new CreateBulkImportOperationCommand
        {
            Description = "", // Empty description should cause validation error
            UserName = "test.user",
            Status = "Initiated"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("Description is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBulkImportOperation(command));
    }

    [Fact]
    public async Task UpdateBulkImportOperation_ValidatesOperationExists()
    {
        // Arrange
        var command = new UpdateBulkImportOperationCommand
        {
            Id = Guid.NewGuid().ToString(),
            Description = "Updated Operation",
            Status = "Updated"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("BulkImportOperation not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateBulkImportOperation(command));
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateBulkImportOperation_HandlesComplexOperationProperties()
    {
        // Arrange
        var command = new CreateBulkImportOperationCommand
        {
            CompanyId = Guid.NewGuid().ToString(),
            UserName = "enterprise.operations.manager",
            Description = "Complex enterprise bulk import operation for critical infrastructure including database clusters, application servers, network security components, and backup systems with cross-dependency validation",
            Status = "Initiated",
            StartTime = DateTime.Now,
            EndTime = DateTime.Now.AddHours(8),
           // BulkImportOperationGroupId = Guid.NewGuid().ToString()
        };

        var expectedMessage = $"BulkImportOperation '{command.Description}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBulkImportOperationResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBulkImportOperation(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBulkImportOperationResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBulkImportOperation_HandlesStatusTransitions()
    {
        // Arrange
        var command = new UpdateBulkImportOperationCommand
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            UserName = "status.manager",
            Description = "Updated operation with status transition",
            Status = "In Progress",
            StartTime = DateTime.Now.AddHours(-2),
            EndTime =DateTime.MinValue
        };

        var expectedMessage = $"BulkImportOperation '{command.Description}' has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateBulkImportOperationResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateBulkImportOperation(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBulkImportOperationResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetBulkImportOperationsRunningStatus_HandlesMultipleRunningOperations()
    {
        // Arrange
        var runningOperations = new List<BulkImportOperationRunningListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Description = "Server Infrastructure Import - Phase 1",
                Status = "Running",
                StartTime = DateTime.Now.AddHours(-3),
                UserName = "server.admin"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Description = "Database Cluster Import - Phase 2",
                Status = "Running",
                StartTime = DateTime.Now.AddHours(-2),
                UserName = "database.admin"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Description = "Network Security Import - Phase 3",
                Status = "Running",
                StartTime = DateTime.Now.AddHours(-1),
                UserName = "security.admin"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBulkImportOperationRunningListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(runningOperations);

        // Act
        var result = await _controller.GetBulkImportOperationsRunningStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var operations = Assert.IsAssignableFrom<List<BulkImportOperationRunningListVm>>(okResult.Value);
        Assert.Equal(3, operations.Count);
        Assert.All(operations, op => Assert.Equal("Running", op.Status));
    }

    [Fact]
    public async Task CreateBulkImportOperationValidator_HandlesComplexValidationScenario()
    {
        // Arrange
        var command = new CreateBulkImportValidatorCommand
        {
            BulkImportOperationList = new List<CreateBulkImportValidatorCommandList>
            {
                new()
                {
                    ServerList = new List<CreateBulkImportComponentServerCommand>
                    {
                        new() { /* Server validation properties */ }
                    },
                    DatabaseList = new List<CreateBulkImportComponentDataBaseCommand>
                    {
                        new() { /* Database validation properties */ }
                    },
                    ReplicationList = new List<CreateBulkImportComponentReplicationCommand>
                    {
                        new() { /* Replication validation properties */ }
                    },
                    InfraObject = new CreateBulkImportComponentInfraObjectCommand(),
                    IsSwitchOver = true,
                    SwitchOverTemplate = "Enterprise SwitchOver Template",
                    IsFailOver = true,
                    FailOverTemplate = "Enterprise FailOver Template",
                    IsSwitchBack = false,
                    SwitchBackTemplate = null,
                    IsFailBack = false,
                    FailBackTemplate = null
                }
            }
        };

        var expectedResponse = new CreateBulkImportValidatorCommandResponse
        {
            ValidatorResponse = new List<CreateBulkImportValidatorCommandResponseDetail>
            {
                new()
                {
                    InfraObjectName = "Enterprise-Infrastructure-Component",
                    IsSwitchOver = true,
                    SwitchOverTemplate = "Enterprise SwitchOver Template",
                    IsFailOver = true,
                    FailOverTemplate = "Enterprise FailOver Template",
                    ServerCommand = new List<ValidationDetail>
                    {
                        new() { PropertyName = "ServerName", Name = "Enterprise-Server-01", Exception = null }
                    },
                    DatabaseCommand = new List<ValidationDetail>
                    {
                        new() { PropertyName = "DatabaseName", Name = "Enterprise-DB-01", Exception = null }
                    }
                }
            }
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateBulkImportOperationValidator(command);

        // Assert
        var actionResult = Assert.IsType<ActionResult<CreateBulkImportValidatorCommandResponse>>(result);
        var response = Assert.IsType<CreateBulkImportValidatorCommandResponse>(actionResult.Value);
        Assert.NotNull(response.ValidatorResponse);
        Assert.Single(response.ValidatorResponse);
        Assert.Equal("Enterprise-Infrastructure-Component", response.ValidatorResponse.First().InfraObjectName);
    }

    [Fact]
    public async Task DeleteBulkImportOperation_VerifiesOperationIsDeactivated()
    {
        // Arrange
        var operationId = Guid.NewGuid().ToString();
        var expectedMessage = "BulkImportOperation 'Test Operation' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBulkImportOperationCommand>(c => c.Id == operationId), default))
            .ReturnsAsync(new DeleteBulkImportOperationResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteBulkImportOperation(operationId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBulkImportOperationResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }
}
