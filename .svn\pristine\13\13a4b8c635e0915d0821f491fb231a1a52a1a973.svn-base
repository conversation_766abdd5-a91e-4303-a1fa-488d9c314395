using ContinuityPatrol.Application.Features.BusinessService.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessService.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetBusinessServiceDiagramDetail;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class BusinessServiceFixture : IDisposable
{
    public List<BusinessServiceListVm> BusinessServiceListVm { get; set; }
    public List<BusinessService> BusinessServices { get; set; }
    public BusinessServiceDetailVm BusinessServiceDetailVm { get; set; }
    public CreateBusinessServiceCommand CreateBusinessServiceCommand { get; set; }
    public UpdateBusinessServiceCommand UpdateBusinessServiceCommand { get; set; }
    public List<BusinessServiceNameVm> BusinessServiceNameVms { get; set; }
    public GetBusinessServicePaginatedListQuery GetBusinessServicePaginatedListQuery { get; set; }
    public GetBusinessServiceDiagramDetailVm BusinessServiceDiagramDetailVm { get; set; }

    public BusinessServiceFixture()
    {
        BusinessServiceListVm = AutoBusinessServiceFixture.Create<List<BusinessServiceListVm>>();
        BusinessServiceDetailVm = AutoBusinessServiceFixture.Create<BusinessServiceDetailVm>();
        CreateBusinessServiceCommand = AutoBusinessServiceFixture.Create<CreateBusinessServiceCommand>();
        UpdateBusinessServiceCommand = AutoBusinessServiceFixture.Create<UpdateBusinessServiceCommand>();
        BusinessServices = AutoBusinessServiceFixture.Create<List<BusinessService>>();
        BusinessServiceNameVms = AutoBusinessServiceFixture.Create<List<BusinessServiceNameVm>>();
        GetBusinessServicePaginatedListQuery = AutoBusinessServiceFixture.Create<GetBusinessServicePaginatedListQuery>();
        BusinessServiceDiagramDetailVm = AutoBusinessServiceFixture.Create<GetBusinessServiceDiagramDetailVm>();
    }

    public Fixture AutoBusinessServiceFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateBusinessServiceCommand>(p => p.Name, 10));
            fixture.Customize<CreateBusinessServiceCommand>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateBusinessServiceCommand>(p => p.Name, 10));
            fixture.Customize<UpdateBusinessServiceCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<UpdateBusinessServiceCommand>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString));

            fixture.Customize<BusinessService>(c => c.With(b => b.IsActive, true));
            fixture.Customize<BusinessService>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString));
            fixture.Customize<BusinessService>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<BusinessServiceListVm>(p => p.Name, 10));
            fixture.Customize<BusinessServiceListVm>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<BusinessServiceListVm>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<BusinessServiceDetailVm>(p => p.Name, 10));
            fixture.Customize<BusinessServiceDetailVm>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<BusinessServiceDetailVm>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<BusinessServiceNameVm>(p => p.Name, 10));
            fixture.Customize<BusinessServiceNameVm>(c => c.With(b => b.Id, Guid.NewGuid().ToString));

            fixture.Customize<GetBusinessServicePaginatedListQuery>(c => c.With(b => b.PageNumber, 1));
            fixture.Customize<GetBusinessServicePaginatedListQuery>(c => c.With(b => b.PageSize, 10));

            fixture.Customize<GetBusinessServiceDiagramDetailVm>(c => c.With(b => b.BusinessServiceId, Guid.NewGuid().ToString));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<GetBusinessServiceDiagramDetailVm>(p => p.BusinessServiceName, 10));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
