using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Domain.ViewModels.ArchiveModel;

public class ArchiveViewModel
{
    public string Id { get; set; }
    public string CompanyId { get; set; }
    public string TableNameProperties { get; set; }
    public string ArchiveProfileName { get; set; }
    public int Count { get; set; }
    public string CronExpression { get; set; }
    public string ScheduleTime { get; set; }
    public int ScheduleType { get; set; }
    public string BackUpType { get; set; }
    public string Type { get; set; }
    public string ClearBackup { get; set; }
    public string NodeId { get; set; }
    public string NodeName { get; set; }

    public PaginatedResult<TableAccessListVm> PaginatedTableAccess { get; set; }
}