﻿using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Form.Commands.Create;

public class CreateFormCommandValidator : AbstractValidator<CreateFormCommand>
{
    private readonly IFormRepository _formRepository;

    public CreateFormCommandValidator(IFormRepository formRepository)
    {
        _formRepository = formRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        //RuleFor(p => p.Type)
        //    .NotEmpty().WithMessage("{PropertyName} is required")
        //    .Matches(@"^[a-zA-Z]*$").WithMessage("Please Enter valid {PropertyName}")
        //    .NotNull();
        RuleFor(p => p.Version)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull();

        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("Please Enter the {PropertyName}")
            .NotNull()
            .Must(p => IsValidJsonObjcet(p)).WithMessage("{PropertyName} must be a valid Json string.");

        RuleFor(p => p).MustAsync(FormNameUnique)
            .WithMessage("A same Name Already Exists.");
    }

    private async Task<bool> FormNameUnique(CreateFormCommand createFormCommand, CancellationToken token)
    {
        return !await _formRepository.IsFormNameUnique(createFormCommand.Name);
    }

    private bool IsValidJsonObjcet(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                var obj = JsonConvert.DeserializeObject(properties);
                return true;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }
}