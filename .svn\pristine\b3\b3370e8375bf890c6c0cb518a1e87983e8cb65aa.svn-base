﻿using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;

namespace ContinuityPatrol.Application.Features.LicenseManager.Queries.GetDetailView;

public class
    GetLicenseManagerDetailViewQueryHandler : IRequestHandler<GetLicenseManagerDetailViewQuery,
        List<LicenseManagerDetailViewVm>>
{
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly IMapper _mapper;

    public GetLicenseManagerDetailViewQueryHandler(IMapper mapper, ILicenseManagerRepository licenseManagerRepository,
        ILicenseInfoRepository licenseInfoRepository)
    {
        _mapper = mapper;
        _licenseManagerRepository = licenseManagerRepository;
        _licenseInfoRepository = licenseInfoRepository;
    }


    public LicensePropertiesCount GetLicenseCount(string json)
    {
        var jsonObject = JObject.Parse(json);

        var isDatabase = jsonObject.SelectToken("isDatabase")?.Value<bool>() ?? false;

        return new LicensePropertiesCount
        {
            IsDatabase = isDatabase,
            DatabaseCount = jsonObject.SelectToken("primarydatabaseCount")?.Value<int>() ?? 0,
            ReplicationCount = jsonObject.SelectToken("primaryreplicationCount")?.Value<int>() ?? 0,
            StorageCount = jsonObject.SelectToken("primarystorageCount")?.Value<int>() ?? 0,
            VirtualizationCount = jsonObject.SelectToken("primaryvirtualizationCount")?.Value<int>() ?? 0,
            ApplicationCount = jsonObject.SelectToken("primaryapplicationCount")?.Value<int>() ?? 0,
            DnsCount = jsonObject.SelectToken("primarydnsCount")?.Value<int>() ?? 0,
            NetworkCount = jsonObject.SelectToken("primarynetworkCount")?.Value<int>() ?? 0,
            ThirdPartyCount = jsonObject.SelectToken("primarythirdPartyCount")?.Value<int>() ?? 0,
            DatabaseDto = isDatabase
                ? new DatabaseTypeCountDto
                {
                    Databases = jsonObject.SelectToken("DatabaseDto")?.ToObject<Dictionary<string, List<DatabaseItem>>>()
                }
                : null
        };
    }

    public async Task<List<LicenseManagerDetailViewVm>> Handle(GetLicenseManagerDetailViewQuery request,
        CancellationToken cancellationToken)
    {
        var baseLicenses = await _licenseManagerRepository.ListAllBaseLicense();

        var baseLicenseVm = _mapper.Map<List<LicenseManagerDetailViewVm>>(baseLicenses);

        var licenseIds = baseLicenseVm.Where(x=>x.Id.IsNotNullOrWhiteSpace()).Select(x => x.Id).ToList();
        var companyIds = baseLicenseVm.Where(x => x.CompanyId.IsNotNullOrWhiteSpace()).Select(x => x.CompanyId).Distinct().ToList();
        var poNumbers = baseLicenseVm.Where(x => x.PoNumber.IsNotNullOrWhiteSpace()).Select(x => x.PoNumber).Distinct().ToList();

        var childLicense = await _licenseManagerRepository.GetDerivedLicenseByCompanyIdAsync(companyIds, poNumbers);

        var childLicenseIds = childLicense.Where(x=>x.ReferenceId.IsNotNullOrWhiteSpace()).Select(x => x.ReferenceId).ToList();

        licenseIds.AddRange(childLicenseIds);

        var licenseInfo = await _licenseInfoRepository.GroupByUsageCountByLicenseId(licenseIds);

        var licenseCountWithId = licenseInfo
            .GroupBy(x => x.LicenseId)
            .ToDictionary(g => g.Key, g => g.ToList());

        baseLicenseVm = baseLicenseVm.Select(x =>
        {
            #region Childlicense


            //AvaliableCount
            var derivedDatabaseCount = 0;
            var derivedReplicationCount = 0;
            var derivedStorageCount = 0;
            var derivedVirtualizationCount = 0;
            var derivedApplicationCount = 0;
            var derivedDnsCount = 0;
            var derivedNetworkCount = 0;
            var derivedThirdPartyCount = 0;

            //UserCount
            var derivedUsedDatabaseCount = 0;
            var derivedUsedReplicationCount = 0;
            var derivedUsedStorageCount = 0;
            var derivedUsedVirtualizationCount = 0;
            var derivedUsedApplicationCount = 0;
            var derivedUsedDnsCount = 0;
            var derivedUsedNetworkCount = 0;
            var derivedUsedThirdPartyCount = 0;


            var childCompanyLicense = childLicense
              .Where(cl => cl.ParentId == x.CompanyId && cl.ParentPoNumber == x.PoNumber).ToList();

            foreach (var childManager in childCompanyLicense)
            {
                if (licenseCountWithId.TryGetValue(childManager.ReferenceId, out var licenseInfos1))
                {
                    //Start UsedCount

                    derivedUsedDatabaseCount += licenseInfos1
                        .Where(li => li.Entity.IsNotNullOrWhiteSpace() && li.Entity.Equals("Database", StringComparison.OrdinalIgnoreCase))
                        .Sum(li => li.Count);

                    derivedUsedReplicationCount += licenseInfos1
                        .Where(li => li.Entity.IsNotNullOrWhiteSpace() && li.Entity.Equals("Replication", StringComparison.OrdinalIgnoreCase))
                        .Sum(li => li.Count);

                    derivedUsedApplicationCount += licenseInfos1
                        .Where(li => li.EntityType.IsNotNullOrWhiteSpace() && li.EntityType.Equals("Application", StringComparison.OrdinalIgnoreCase))
                        .Sum(li => li.Count);

                    derivedUsedStorageCount += licenseInfos1
                        .Where(li => li.EntityType.IsNotNullOrWhiteSpace() && li.EntityType.Equals("Storage", StringComparison.OrdinalIgnoreCase))
                        .Sum(li => li.Count);

                    derivedUsedVirtualizationCount += licenseInfos1
                        .Where(li => li.EntityType.IsNotNullOrWhiteSpace() && li.EntityType.Equals("Virtualization", StringComparison.OrdinalIgnoreCase))
                        .Sum(li => li.Count);

                    derivedUsedDnsCount += licenseInfos1
                        .Where(li => li.EntityType.IsNotNullOrWhiteSpace() && li.EntityType.Equals("DNS", StringComparison.OrdinalIgnoreCase))
                        .Sum(li => li.Count);

                    derivedUsedNetworkCount += licenseInfos1
                        .Where(li => li.EntityType.IsNotNullOrWhiteSpace() && li.EntityType.Equals("Network", StringComparison.OrdinalIgnoreCase))
                        .Sum(li => li.Count);

                    derivedUsedThirdPartyCount += licenseInfos1
                        .Where(li => li.EntityType.IsNotNullOrWhiteSpace() && li.EntityType.Equals("ThirdParty", StringComparison.OrdinalIgnoreCase))
                        .Sum(li => li.Count);

                    //EndUsedCount
                }

                //Start DerivedLicense AvailableCount

                var derivedLicenseCounts = GetLicenseCount(childManager.Properties);

                derivedDatabaseCount += derivedLicenseCounts.DatabaseCount;
                derivedReplicationCount += derivedLicenseCounts.ReplicationCount;
                derivedStorageCount += derivedLicenseCounts.StorageCount;
                derivedVirtualizationCount += derivedLicenseCounts.VirtualizationCount;
                derivedApplicationCount += derivedLicenseCounts.ApplicationCount;
                derivedDnsCount += derivedLicenseCounts.DnsCount;
                derivedNetworkCount += derivedLicenseCounts.NetworkCount;
                derivedThirdPartyCount += derivedLicenseCounts.ThirdPartyCount;
              


                //End DerivedLicense AvailableCount
            }

            #endregion

            #region BaseLicense
            if (licenseCountWithId.TryGetValue(x.Id, out var licenseInfos))
            {
                //Start UsedCount

                x.BaseLicenseCountVm.DatabaseUsedCount += licenseInfos
                    .Where(li => li.Entity.IsNotNullOrWhiteSpace() && li.Entity.Equals("Database", StringComparison.OrdinalIgnoreCase))
                    .Sum(li => li.Count);

                x.BaseLicenseCountVm.ReplicationUsedCount += licenseInfos
                    .Where(li => li.Entity.IsNotNullOrWhiteSpace() && li.Entity.Equals("Replication", StringComparison.OrdinalIgnoreCase))
                    .Sum(li => li.Count);

                x.BaseLicenseCountVm.ApplicationUsedCount += licenseInfos
                    .Where(li => li.EntityType.IsNotNullOrWhiteSpace() && li.EntityType.Equals("Application", StringComparison.OrdinalIgnoreCase))
                    .Sum(li => li.Count);

                x.BaseLicenseCountVm.StorageUsedCount += licenseInfos
                    .Where(li => li.EntityType.IsNotNullOrWhiteSpace() && li.EntityType.Equals("Storage", StringComparison.OrdinalIgnoreCase))
                    .Sum(li => li.Count);

                x.BaseLicenseCountVm.VirtualizationUsedCount += licenseInfos
                    .Where(li => li.EntityType.IsNotNullOrWhiteSpace() && li.EntityType.Equals("Virtualization", StringComparison.OrdinalIgnoreCase))
                    .Sum(li => li.Count);

                x.BaseLicenseCountVm.DnsUsedCount += licenseInfos
                    .Where(li => li.EntityType.IsNotNullOrWhiteSpace() && li.EntityType.Equals("DNS", StringComparison.OrdinalIgnoreCase))
                    .Sum(li => li.Count);

                x.BaseLicenseCountVm.NetworkUsedCount += licenseInfos
                    .Where(li => li.EntityType.IsNotNullOrWhiteSpace() && li.EntityType.Equals("Network", StringComparison.OrdinalIgnoreCase))
                    .Sum(li => li.Count);

                x.BaseLicenseCountVm.NetworkUsedCount += licenseInfos
                    .Where(li => li.EntityType.IsNotNullOrWhiteSpace() && li.EntityType.Equals("ThirdParty", StringComparison.OrdinalIgnoreCase))
                    .Sum(li => li.Count);


                //EndUsedCount

            }

            x.IsLicenseExpiry =
                DateTime.TryParseExact(x.ExpiryDate, "dd MMMM yyyy", CultureInfo.GetCultureInfo("en-IN"),
                    DateTimeStyles.None, out var expiryDate) && expiryDate >= DateTime.UtcNow.Date;

            x.ExpiryDate = x.Validity.Contains("Enterprise-Unlimited")
                ? "Unlimited"
                : x.ExpiryDate;

            if (IsWarranty(x.AmcPlan))
            {
                x.IsWarranty = true;
                x.WarrantyPlan = Warranty(x.AmcPlan, "Warranty");
                x.WarrantyEndDate = Warranty(x.AmcPlan, "WarrantyEndDate");
            }

            if (x.IsAmc)
            {
                x.AmcStartDate = SecurityHelper.Decrypt(x.AmcStartDate);
                x.AmcEndDate = SecurityHelper.Decrypt(x.AmcEndDate);
            }


            var updateLicenseKey = SecurityHelper.Decrypt(x.LicenseKey);
            var updateLicenseKeyDetail = updateLicenseKey.Split('*');

            if (updateLicenseKeyDetail[8].ToLower().Contains("renewal"))
                x.RenewalDate = x.LastModifiedDate;

            x.SiteCount = GetJsonProperties.GetLicenseJsonValue(x.Properties, "totalSites");


            //Start BaseLicense AvailableCount
            var baseLicenseCounts = GetLicenseCount(x.Properties);

            x.BaseLicenseCountVm.DatabaseAvailableCount = baseLicenseCounts.DatabaseCount;
            x.BaseLicenseCountVm.ReplicationAvailableCount = baseLicenseCounts.ReplicationCount;
            x.BaseLicenseCountVm.ApplicationAvailableCount = baseLicenseCounts.ApplicationCount;
            x.BaseLicenseCountVm.StorageAvailableCount = baseLicenseCounts.StorageCount;
            x.BaseLicenseCountVm.VirtualizationAvailableCount = baseLicenseCounts.VirtualizationCount;
            x.BaseLicenseCountVm.DnsAvailableCount = baseLicenseCounts.DnsCount;
            x.BaseLicenseCountVm.NetworkAvailableCount = baseLicenseCounts.NetworkCount;
            x.BaseLicenseCountVm.ThirdPartyAvailableCount = baseLicenseCounts.ThirdPartyCount;
            x.BaseLicenseCountVm.DatabaseDto = baseLicenseCounts.DatabaseDto;

            //End BaseLicense AvailableCount


            var totalUsedCount = x.BaseLicenseCountVm.DatabaseUsedCount + x.BaseLicenseCountVm.ReplicationUsedCount +
                                 x.BaseLicenseCountVm.StorageUsedCount + x.BaseLicenseCountVm.VirtualizationUsedCount +
                                 x.BaseLicenseCountVm.ApplicationUsedCount +
                                 x.BaseLicenseCountVm.DnsUsedCount
                                 + x.BaseLicenseCountVm.NetworkUsedCount + x.BaseLicenseCountVm.ThirdPartyUsedCount
                                 + derivedUsedDatabaseCount + derivedUsedReplicationCount
                                 + derivedUsedStorageCount + derivedUsedVirtualizationCount +
                                 derivedUsedApplicationCount +
                                 derivedUsedDnsCount + derivedUsedNetworkCount + derivedUsedThirdPartyCount;


            var derivedTotalCount = derivedDatabaseCount + derivedReplicationCount
                                                         + derivedStorageCount + derivedVirtualizationCount + derivedApplicationCount +
                                                         derivedDnsCount
                                                         + derivedNetworkCount + derivedThirdPartyCount;



            var totalCount = derivedDatabaseCount + derivedReplicationCount
                                                                          + derivedStorageCount +
                                                                          derivedVirtualizationCount +
                                                                          derivedApplicationCount +
                                                                          derivedDnsCount
                                                                          + derivedNetworkCount +
                                                                          derivedThirdPartyCount
                                                                          + baseLicenseCounts.DatabaseCount
                                                                          + baseLicenseCounts.ReplicationCount
                                                                          + baseLicenseCounts.ApplicationCount
                                                                          + baseLicenseCounts.StorageCount
                                                                          + baseLicenseCounts.VirtualizationCount
                                                                          + baseLicenseCounts.DnsCount
                                                                          + baseLicenseCounts.NetworkCount
                                                                          + baseLicenseCounts.ThirdPartyCount;

            var totalNotUsedCount = totalCount - totalUsedCount - derivedTotalCount;


            x.BaseLicenseCountVm.TotalCount = totalCount;
            x.BaseLicenseCountVm.DerivedTotalCount = derivedTotalCount;
            x.BaseLicenseCountVm.TotalUsedCount = totalUsedCount;
            x.BaseLicenseCountVm.TotalNotUsedCount = totalNotUsedCount;


            x.BaseLicenseCountVm.DerivedDatabaseUsedCount = derivedUsedDatabaseCount;
            x.BaseLicenseCountVm.DerivedReplicationUsedCount = derivedUsedReplicationCount;
            x.BaseLicenseCountVm.DerivedStorageUsedCount = derivedUsedStorageCount;
            x.BaseLicenseCountVm.DerivedVirtualizationUsedCount = derivedUsedVirtualizationCount;
            x.BaseLicenseCountVm.DerivedApplicationUsedCount = derivedUsedApplicationCount;
            x.BaseLicenseCountVm.DerivedDnsUsedCount = derivedUsedDnsCount;
            x.BaseLicenseCountVm.DerivedNetworkUsedCount = derivedUsedNetworkCount;
            x.BaseLicenseCountVm.DerivedThirdPartyUsedCount = derivedUsedThirdPartyCount;


            x.BaseLicenseCountVm.DerivedDatabaseAvailableCount = derivedDatabaseCount;
            x.BaseLicenseCountVm.DerivedReplicationAvailableCount = derivedReplicationCount;
            x.BaseLicenseCountVm.DerivedStorageAvailableCount = derivedStorageCount;
            x.BaseLicenseCountVm.DerivedVirtualizationAvailableCount = derivedVirtualizationCount;
            x.BaseLicenseCountVm.DerivedApplicationAvailableCount = derivedApplicationCount;
            x.BaseLicenseCountVm.DerivedDnsAvailableCount = derivedDnsCount;
            x.BaseLicenseCountVm.DerivedNetworkAvailableCount = derivedNetworkCount;
            x.BaseLicenseCountVm.DerivedThirdPartyAvailableCount = derivedThirdPartyCount;

            return x;
            #endregion

        }).ToList();

        return baseLicenseVm;
    }

    public static bool IsWarranty(string src)
    {
        if (src.IsNullOrWhiteSpace()) return false;

        var jObject = JObject.Parse(src);

        return jObject.ContainsKey("Warranty") && jObject["Warranty"]?.ToObject<long>() != 0;
    }

    public static string Warranty(string src,string date)
    {
        if (src.IsNullOrWhiteSpace()) return string.Empty;

        var jObject = JObject.Parse(src);
        return jObject[date]?.ToString();
    }
}