﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.PostgresMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetPaginatedList;

public class GetPostgresMonitorStatusPaginatedListQueryHanlder : IRequestHandler<
    GetPostgresMonitorStatusPaginatedListQuery, PaginatedResult<PostgresMonitorStatusListVm>>
{
    private readonly IMapper _mapper;
    private readonly IPostgresMonitorStatusRepository _postgresMonitorStatusRepository;

    public GetPostgresMonitorStatusPaginatedListQueryHanlder(
        IPostgresMonitorStatusRepository postgresMonitorStatusRepository, IMapper mapper)
    {
        _postgresMonitorStatusRepository = postgresMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<PostgresMonitorStatusListVm>> Handle(
        GetPostgresMonitorStatusPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _postgresMonitorStatusRepository.GetPaginatedQuery();

        var productFilterSpec = new PostgresMonitorStatusFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<PostgresMonitorStatusListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}