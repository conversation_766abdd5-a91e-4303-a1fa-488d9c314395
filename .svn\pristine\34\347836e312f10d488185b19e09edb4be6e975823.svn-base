﻿using ContinuityPatrol.Application.Features.TeamResource.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamResource.Events
{
    public class PaginatedTeamResourceEventTests
    {
        private readonly Mock<ILogger<TeamResourcePaginatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly TeamResourcePaginatedEventHandler _handler;

        public PaginatedTeamResourceEventTests()
        {
            _mockLogger = new Mock<ILogger<TeamResourcePaginatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new TeamResourcePaginatedEventHandler(
                _mockUserService.Object,
                _mockUserActivityRepository.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_AddsUserActivityAndLogsInformation_WhenCalled()
        {
            var paginatedEvent = new TeamResourcePaginatedEvent();
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns("user-id");
            _mockUserService.Setup(s => s.LoginName).Returns("user-login");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("/api/team-resource");
            _mockUserService.Setup(s => s.CompanyId).Returns("company-id");
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            await _handler.Handle(paginatedEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "user-id" &&
                activity.LoginName == "user-login" &&
                activity.RequestUrl == "/api/team-resource" &&
                activity.CompanyId == "company-id" &&
                activity.HostAddress == "***********" &&
                activity.Entity == Modules.TeamResource.ToString() &&
                activity.Action == $"{ActivityType.View} {Modules.TeamResource}" &&
                activity.ActivityType == ActivityType.View.ToString() &&
                activity.ActivityDetails == " Team Resource viewed"
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation("Team Resource viewed"), Times.Once);
        }

        [Fact]
        public async Task Handle_LogsError_WhenAddAsyncThrowsException()
        {
            var paginatedEvent = new TeamResourcePaginatedEvent();
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns("user-id");
            _mockUserService.Setup(s => s.LoginName).Returns("user-login");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("/api/team-resource");
            _mockUserService.Setup(s => s.CompanyId).Returns("company-id");
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .ThrowsAsync(new System.Exception("Database error"));

            await Assert.ThrowsAsync<System.Exception>(() => _handler.Handle(paginatedEvent, cancellationToken));

            _mockLogger.Verify(logger => logger.LogInformation(It.IsAny<string>()), Times.Never);
        }
    }
}
