﻿using ContinuityPatrol.Application.Features.LogViewer.Commands.Create;
using ContinuityPatrol.Application.Features.LogViewer.Commands.Delete;
using ContinuityPatrol.Application.Features.LogViewer.Commands.Update;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetList;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetNameUnique;
using ContinuityPatrol.Domain.ViewModels.LogViewerModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class LogViewerController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<LogViewerListVm>>> GetLogViewerList()
    {
        Logger.LogDebug("Get Log Viewer List");

        return Ok(await Mediator.Send(new GetLogViewerListQuery()));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateLogViewerResponse>> CreateLogViewer([FromBody] CreateLogViewerCommand createLogViewerCommand)
    {
        Logger.LogDebug($"Create Log Viewer'{createLogViewerCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateLogViewer), await Mediator.Send(createLogViewerCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteLogViewerResponse>> DeleteLogViewer(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "LogViewer Id");

        Logger.LogDebug($"Delete LogViewer Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteLogViewerCommand { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<LogViewerListVm>>> GetPaginatedSites([FromQuery] GetLogViewerPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in LogViewer Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateLogViewerResponse>> UpdateLogViewer([FromBody] UpdateLogViewerCommand updateSiteCommand)
    {
        Logger.LogDebug($"Update LogViewer '{updateSiteCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateSiteCommand));
    }

    [Route("name-unique"), HttpGet]
    public async Task<ActionResult> IsLogViewerNameUnique(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "LogViewer Name");

        Logger.LogDebug($"Check Name Exists Detail by LogViewer Name '{name}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetLogViewerNameUniqueQuery { Name = name, Id = id }));
    }

    [HttpGet("{id}", Name = "GetServerLog")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<GetLogViewerDetailVm>> GetLogViewerDetail(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "LogViewer Id");

        Logger.LogDebug($"Get LogViewer Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetLogViewerDetailQuery { Id = id }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllLogViewersCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllLogViewersNameCacheKey };

        ClearCache(cacheKeys);
    }
}
