﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;
using System;

namespace ContinuityPatrol.Persistence.Repositories;

public class NodeRepository : BaseRepository<Node>, INodeRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public NodeRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext,loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<Node>> ListAllAsync()
    {
        var nodes = base.QueryAll(node =>
            node.CompanyId.Equals(_loggedInUserService.CompanyId));

        var node = MapNodes(nodes);
       
        return await node.ToListAsync();
    }

    public override Task<Node> GetByReferenceIdAsync(string id)
    {
        var node = base.GetByReferenceId(id,
            x => x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                   x.ReferenceId.Equals(id));

        var nodeDto = MapNodes(node);
        
       return nodeDto.FirstOrDefaultAsync();
    }

    public Task<List<Node>> GetNodeNames()
    {
        if (!_loggedInUserService.IsParent)
            return _dbContext.Nodes.Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new Node { ReferenceId = x.ReferenceId, Name = x.Name })
                .OrderBy(x => x.Name)
                .ToListAsync();

        return _dbContext.Nodes
            .Active()
            .Select(x => new Node { ReferenceId = x.ReferenceId, Name = x.Name })
            .OrderBy(x => x.Name)
            .ToListAsync();
    }
    public override async Task<PaginatedResult<Node>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<Node> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await SelectNode(_loggedInUserService.IsParent
            ?MapNodes(Entities.Specify(productFilterSpec).DescOrderById())
            :MapNodes(Entities.Specify(productFilterSpec).Where(x=>x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()))
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<Node> GetPaginatedQuery()
    {
        var nodes = base.QueryAll(node =>
            node.CompanyId.Equals(_loggedInUserService.CompanyId));

        var node = MapNodes(nodes);

        return node.AsNoTracking().OrderByDescending(x=>x.Id);
    }

    public Task<bool> IsNodeNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.Nodes.Any(e => e.Name.Equals(name)))
            : Task.FromResult(_dbContext.Nodes.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsNodeNameUnique(string name)
    {
        var matches = _dbContext.Nodes.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }

    public async Task<List<Node>> GetNodeListType(string typeId)
    {
        var nodes = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.TypeId.Equals(typeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.TypeId.Equals(typeId));

        var node = MapNodes(nodes);

        return await node.ToListAsync();
    }
    public async Task<PaginatedResult<Node>> GetNodeByType(string typeId, int pageNumber, int pageSize, Specification<Node> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await SelectNode(_loggedInUserService.IsParent
            ? MapNodes(Entities.Specify(productFilterSpec).Where(x=>x.TypeId.Equals(typeId)).DescOrderById())
            : MapNodes(Entities.Specify(productFilterSpec).Where(x => x.TypeId.Equals(typeId) && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .DescOrderById()))
                .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public IQueryable<Node> GetNodeByType(string typeId)
    {
        var nodes = SelectNode(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.TypeId.Equals(typeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.TypeId.Equals(typeId)));

        var node = MapNodes(nodes);

        return node.AsNoTracking().OrderByDescending(x=>x.Id);
    }

    public override Task<Node> GetByIdAsync(int id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByIdAsync(id)
            : Task.FromResult(
                FindByFilter(node => node.Id.Equals(id) && node.CompanyId.Equals(_loggedInUserService.CompanyId)).Result
                    .SingleOrDefault());
    }

    public async Task<List<Node>> GetNodeByServerId(string serverId)
    {
        var nodes = SelectNode(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ServerId.Equals(serverId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ServerId.Equals(serverId)));

        var node = MapNodes(nodes);

        return await node.ToListAsync();
    }
    public async Task<List<Node>> GetNodeByTypeIds(List<string> typeIds)
    {
        var nodes =await (_loggedInUserService.IsParent
             ? base.FilterBy(x => typeIds.Contains(x.TypeId))
             : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && typeIds.Contains(x.TypeId)))
             .Select(x => new Node
             {
                 Id = x.Id,
                 ReferenceId = x.ReferenceId,
                 Name = x.Name,
                 Type = x.Type,
                 TypeId = x.TypeId
             }).ToListAsync();

        return nodes;
    }


    private IQueryable<Node> MapNodes(IQueryable<Node> nodes)
    {
        return nodes
            .Select(node => new
            {
                Node = node,
                Server = _dbContext.Servers.Active().AsNoTracking().FirstOrDefault(bs => bs.ReferenceId.Equals(node.ServerId)),
                ComponentType = _dbContext.ComponentTypes.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId == node.TypeId)

            })
            .Select(result => new Node
            {
                Id = result!.Node!.Id,
                ReferenceId = result!.Node!.ReferenceId,
                CompanyId = result!.Node!.CompanyId,
                Name = result.Node!.Name,
                ServerId = result!.Server.ReferenceId,
                ServerName = result!.Server!.Name,
                TypeId = result!.Node!.TypeId,
                Type = result!.Node!.Type,
                FormVersion = result!.Node!.FormVersion,
                Properties = result!.Node!.Properties,
                IsActive = result!.Node!.IsActive,
                CreatedBy = result!.Node!.CreatedBy,
                CreatedDate = result!.Node!.CreatedDate,
                LastModifiedBy = result.Node!.LastModifiedBy,
                LastModifiedDate = result!.Node!.LastModifiedDate
            });
    }
    private IQueryable<Node> SelectNode(IQueryable<Node> query)
    {
        return query.Select(x => new Node
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Name = x.Name,
            CompanyId = x.CompanyId,
            ServerId = x.ServerId,
            ServerName = x.ServerName,
            Type = x.Type,
            TypeId = x.TypeId,
            Properties = x.Properties,
            FormVersion = x.FormVersion
        });
    }

    
}