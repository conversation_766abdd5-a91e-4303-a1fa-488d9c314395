﻿//using ContinuityPatrol.Application.Features.Database.Events.InfraSummaryEvents.Update;

//namespace ContinuityPatrol.Core.UnitTests.Domains.Database.Events.InfraSummaryEvents;

//public class UpdateDatabaseInfraSummaryEventTests : IClassFixture<DatabaseFixture>, IClassFixture<InfraSummaryFixture>
//{
//    private readonly DatabaseFixture _databaseFixture;
    
//    private readonly InfraSummaryFixture _infraSummaryFixture;

//    private readonly Mock<IInfraSummaryRepository> _mockInfraSummaryRepository;

//    private readonly DatabaseInfraSummaryUpdatedEventHandler _handler;

//    public UpdateDatabaseInfraSummaryEventTests(DatabaseFixture databaseFixture, InfraSummaryFixture infraSummaryFixture)
//    {
//        _databaseFixture = databaseFixture;

//        _infraSummaryFixture = infraSummaryFixture;

//        var mockDatabaseInfraSummaryUpdatedEventLogger = new Mock<ILogger<DatabaseInfraSummaryUpdatedEventHandler>>();

//        _mockInfraSummaryRepository = InfraSummaryRepositoryMocks.UpdateDatabaseInfraSummaryEventRepository(_infraSummaryFixture.InfraSummaries);

//        _handler = new DatabaseInfraSummaryUpdatedEventHandler(mockDatabaseInfraSummaryUpdatedEventLogger.Object, _mockInfraSummaryRepository.Object);
//    }

//    [Fact]
//    public async Task Handle_IncreaseInfraSummaryCount_When_UpdateDatabaseInfraSummaryEvent()
//    {
//        _databaseFixture.DatabaseInfraSummaryUpdatedEvent.CurrentDatabaseType = _infraSummaryFixture.InfraSummaries[0].Type;

//        var result = _handler.Handle(_databaseFixture.DatabaseInfraSummaryUpdatedEvent, CancellationToken.None);

//        Assert.True(result.IsCompleted);

//        await Task.CompletedTask;
//    }

//    [Fact]
//    public async Task Handle_IncreaseInfraSummaryCount_When_UpdateDatabaseInfraSummaryEventCreated()
//    {
//        var result = _handler.Handle(_databaseFixture.DatabaseInfraSummaryUpdatedEvent, CancellationToken.None);

//        Assert.True(result.IsCompleted);

//        await Task.CompletedTask;
//    }

//    [Fact]
//    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
//    {
//        await _handler.Handle(_databaseFixture.DatabaseInfraSummaryUpdatedEvent, CancellationToken.None);

//        _mockInfraSummaryRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.InfraSummary>()), Times.Once);
//    }

//    [Fact]
//    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
//    {
//        _databaseFixture.DatabaseInfraSummaryUpdatedEvent.CurrentDatabaseType = _infraSummaryFixture.InfraSummaries[0].Type;

//        await _handler.Handle(_databaseFixture.DatabaseInfraSummaryUpdatedEvent, CancellationToken.None);

//        _mockInfraSummaryRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.InfraSummary>()), Times.Once);
//    }
//}