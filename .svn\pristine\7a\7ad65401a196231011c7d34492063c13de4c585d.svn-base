﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPowerMax;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class PowerMaxMonitorStatusRepository : BaseRepository<PowerMaxMonitorStatus>, IPowerMaxMonitorStatusRepository
{  
    private readonly ApplicationDbContext _dbContext;

    public PowerMaxMonitorStatusRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
        _dbContext = dbContext;       
    }

    public async Task<List<PowerMaxDetailVm>> GetPowerMaxMonitorStatusByName(string? name, bool isSnap)
    {
        var powerMaxList = await _dbContext.PowerMaxMonitorStatus
            .AsNoTracking()
            .Active()
            .Select(x => x.Properties)
            .ToListAsync();

        if (powerMaxList.Count == 0) return new List<PowerMaxDetailVm>();

        var powerMaxDetails = powerMaxList
            .Select(SecurityHelper.Decrypt)  
            .Select(JsonConvert.DeserializeObject<PowerMaxDetailVm>) 
            .ToList();

        if (name.IsNullOrWhiteSpace())
        {
            return powerMaxDetails;
        }
       
        if (isSnap && name.IsNotNullOrWhiteSpace())
        {
            
            powerMaxDetails = powerMaxDetails
               .Select(x =>
               {
                   x.StorageGroupMonitoring = x.StorageGroupMonitoring
                       .Select(sg =>
                       {
                           sg.SnapshotDetails = sg.SnapshotDetails
                               .Where(sd => name!.Contains(sd.SnapshotName)|| sd.SnapshotName.Contains(name))
                               .ToList();
                           return sg;
                       })
                       .Where(sg => sg.SnapshotDetails.Any())
                       .ToList();
                   return x;
               })
               .Where(x => x.StorageGroupMonitoring.Any())
               .ToList();
            
            return powerMaxDetails;
        }

        if (!string.IsNullOrWhiteSpace(name))
        {
            powerMaxDetails = powerMaxDetails
                .Where(x => x.StorageGroupMonitoring
                    .Any(sg => sg.StorageGroupName.Equals(name)))
                .Select(detail =>
                {
                    detail.StorageGroupMonitoring = detail.StorageGroupMonitoring
                        .Where(sg => sg.StorageGroupName.Equals(name))
                        .ToList();
                    return detail;
                })
                .ToList();
        }

        return powerMaxDetails;
    }     
    
}
