﻿//using ContinuityPatrol.Orchestration.Core.Contracts;
//using ContinuityPatrol.Orchestration.Core.Features.WorkflowHistory.Commands.Create;
//using ContinuityPatrol.Orchestration.Core.UnitTests.Attributes;
//using ContinuityPatrol.Orchestration.Core.UnitTests.Mocks;

//namespace ContinuityPatrol.Orchestration.Core.UnitTests.Domains.WorkflowHistory.Validators;

//public class CreateWorkflowHistoryValidatorTests
//{
//    private readonly Mock<IWorkflowHistoryRepository> _mockWorkflowHistoryRepository;

//    public CreateWorkflowHistoryValidatorTests()
//    {
//        var workflowHistory = new Fixture().Create<List<Entities.WorkflowHistory>>();

//        _mockWorkflowHistoryRepository = WorkflowHistoryRepositoryMocks.CreateWorkflowHistoryRepository(workflowHistory);
//    //}

//    // Workflow Name

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_WorkflowName_InWorkflowHistory_WithEmpty(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);

//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_WorkflowName_InWorkflowHistory_IsNull(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = null;

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameNotEmptyRequired, validateResult.Errors[1].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_WorkflowName_InWorkflowHistory_MinimumRange(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "BA";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameRangeRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_WorkflowName_InWorkflowHistory_MaxiMumRange(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameRangeRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_Valid_WorkflowName_InWorkflowHistory(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "   Pts   ";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_Valid_WorkflowName_InWorkflowHistory_With_Double_Space_InFront(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "  PTS";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_Valid_WorkflowName_InWorkflowHistory_With_TripleSpace_InBetween(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "PTS   India";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_Valid_WorkflowName_InWorkflowHistory_With_SpecialCharacters(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "PTS$#%^India";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_Valid_WorkflowName_InWorkflowHistory_With_SpecialCharacters_Only(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "@@#$^&*(*&)><";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_Valid_WorkflowName_InWorkflowHistory_With_SpecialCharacters_InFront(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "@@PTS";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_Valid_WorkflowName_InWorkflowHistory_With_Underscore_InFront(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "_PTS";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_Valid_WorkflowName_InWorkflowHistory_With_Underscore_InFrontAndBack(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "_PTS_";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_Valid_WorkflowName_InWorkflowHistory_With_Numbers_InFront(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "123PTS";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_Valid_WorkflowName_InWorkflowHistory_With_UnderscoreAndNumbers_InFront_AndUnderscore_InBack(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "_123PTS_";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_Valid_WorkflowName_InWorkflowHistory_With_Underscore_InFront_AndNumbers_InBack(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "_PTS123";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Create_Valid_WorkflowName_InWorkflowHistory_With_Numbers_Only(CreateWorkflowHistoryCommand createWorkflowHistoryCommand)
//    {
//        var validator = new CreateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        createWorkflowHistoryCommand.WorkflowName = "0123456789";

//        var validateResult = await validator.ValidateAsync(createWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }
//}