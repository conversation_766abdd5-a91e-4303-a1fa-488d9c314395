﻿using ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoWorkflowOperationDataAttribute : AutoDataAttribute
{
    public AutoWorkflowOperationDataAttribute() : base(() =>
    {
        var fixture = new Fixture();

        fixture.Customizations.Add(
            new StringPropertyTruncateSpecimenBuilder<CreateWorkflowOperationCommand>(p => p.Description, 10));

        fixture.Customizations.Add(
            new StringPropertyTruncateSpecimenBuilder<UpdateWorkflowOperationCommand>(p => p.Description, 10));

        return fixture;
    })
    {

    }
}