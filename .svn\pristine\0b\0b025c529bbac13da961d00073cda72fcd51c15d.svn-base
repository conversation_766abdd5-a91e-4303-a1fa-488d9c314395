﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class ServerLogRepository : BaseRepository<ServerLog>, IServerLogRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public ServerLogRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<bool> IsServerLogNameUnique(string name, string? id)
    {
        return !id.IsValidGuid()
             ? Task.FromResult(_dbContext.ServerLog.Any(e => e.Name.Equals(name)))
             : Task.FromResult(_dbContext.ServerLog.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }
}