﻿using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Update;
using ContinuityPatrol.Application.Features.ReplicationJob.Events.Update;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.ReplicationJob.Commands
{
    public class UpdateReplicationJobTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<IReplicationJobRepository> _mockReplicationJobRepository;
        private readonly UpdateReplicationJobCommandHandler _handler;

        public UpdateReplicationJobTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockPublisher = new Mock<IPublisher>();
            _mockReplicationJobRepository = new Mock<IReplicationJobRepository>();

            _handler = new UpdateReplicationJobCommandHandler(
                _mockMapper.Object,
                _mockPublisher.Object,
                _mockReplicationJobRepository.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldUpdateReplicationJob_WhenValidRequest()
        {
            var command = new UpdateReplicationJobCommand { Id = Guid.NewGuid().ToString(), Name = "Updated Job" };
            var replicationJob = new Domain.Entities.ReplicationJob
            {
                ReferenceId = command.Id,
                Name = "Old Job",
                IsActive = true
            };

            _mockReplicationJobRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(replicationJob);

            _mockMapper
                .Setup(mapper => mapper.Map(command, replicationJob, typeof(UpdateReplicationJobCommand), typeof(Domain.Entities.ReplicationJob)))
                .Callback<object, object, Type, Type>((src, dest, srcType, destType) =>
                {
                    var job = (Domain.Entities.ReplicationJob)dest;
                    job.Name = command.Name;
                });

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.Equal("Updated Job", replicationJob.Name);
            _mockReplicationJobRepository.Verify(repo => repo.UpdateAsync(replicationJob), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(
                It.Is<ReplicationJobUpdatedEvent>(e => e.ReplicationJobName == replicationJob.Name),
                CancellationToken.None), Times.Once);
            Assert.Equal("Replication Job updated successfully.", result.Message);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenReplicationJobDoesNotExist()
        {
            var command = new UpdateReplicationJobCommand { Id = Guid.NewGuid().ToString() };

            _mockReplicationJobRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync((Domain.Entities.ReplicationJob)null);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Equal(nameof(Domain.Entities.ReplicationJob), exception.Source);
            _mockReplicationJobRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.ReplicationJob>()), Times.Never);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<ReplicationJobUpdatedEvent>(), CancellationToken.None), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowArgumentNullException_WhenCommandIsNull()
        {
            await Assert.ThrowsAsync<ArgumentNullException>(() => _handler.Handle(null, CancellationToken.None));

            _mockReplicationJobRepository.Verify(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()), Times.Never);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<ReplicationJobUpdatedEvent>(), CancellationToken.None), Times.Never);
        }
    }
}
