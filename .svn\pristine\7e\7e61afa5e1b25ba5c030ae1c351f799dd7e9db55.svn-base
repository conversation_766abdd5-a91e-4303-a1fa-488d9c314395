﻿$(document).ready(function () {
      $(document).on('change', '#userLoginType', async function () {
        const value = $(this).val()?.trim();
        const lowerValue = value?.toLowerCase().replace(/\s+/g, "");

        const $loginName = $("#textLoginName");
        const $encryptedPassword = $('#encriptedPassword');
        const $loginTypeError = $('#LoginType-error');
        const $domain = $('#userDomain');

        $loginName.val('');
        $("#loginType").val(value);

        if (lowerValue === "ad") {
            $loginName.prop('readonly', true);
            $('#LoginPassword-error, #ConfirmPassword-error').text('').removeClass('field-validation-error');

            try {
                const tempPassword = await EncryptPassword('Use your AD password', '#encriptedPassword');
                $encryptedPassword.val(tempPassword);
            } catch (err) {
                console.error("Encryption failed:", err);
            }

            $domain.val('').empty();
            $('#ADGroupName').hide();
            $("#ADIndividual").prop('checked', true).trigger("click");
        } else {
            $loginName.prop({ readonly: false, disabled: false });
            $('#AdDomain-error, #AdUser-error').text('').removeClass('field-validation-error');
            $encryptedPassword.val('');
        }

        ValidateLoginType(value);
        await validateDropDown(value, 'Select authentication type', $loginTypeError);
    });

    $(document).on('change', '#userADGroupName', async function () {
        const value = $(this).val()
        await validateDropDown(value, 'Select group', $('#Adgroup-error'));
       
    });

    $(document).on('change', '#userCompanyName', async function () {
        const value = $(this).val();
        const companyId = $("#userCompanyName option:selected").attr('id');
        const data = { id: companyId };
        try {
            const resultdb = await GetAsync(RootUrl + 'Admin/User/GetCompanyById', data, OnError);

            const response = await $.ajax({
                url: userManageURL.userRole,
                type: "GET",
                dataType: "json"
            });

            if (response?.success) {
                await roledata(response.data, resultdb?.isParent);
               
            } else {
                errorNotification(response);
            }
            $('#userCompanyId').val(companyId);
            const errorElement = $('#Company-error');
            validateDropDown(value, 'Select company name', errorElement);
        } catch (error) {
            errorNotification(error);
        }

     });

    $(document).on('change', '#userRoleName', function () {
        const value = $("#userRoleName :selected").text();        
        var errorElement = $('#Role-error');
        var roleId = $("#userRoleName option:selected").data('roleid');
        $('#userRoleId').val(roleId);
        validateDropDown(value, 'Select role', errorElement);

    });

    $(document).on('change', '#userDomain', function () {
        const value = $(this).val();
        var errorElement = $('#AdDomain-error');
        validateDropDown(value, 'Select domain', errorElement);
        if (value !== 'Select domain' && value !== undefined && value !== '') {    
            if (document.getElementById("ADIndividual").checked) {
                $('#searchStringDomain,#usernameDomain').show();
                
            } else if (document.getElementById("ADGroup").checked) {                             
                $('#searchStringDomain,#usernameDomain').hide();
                $('#ADGroupName').show();                
            }                  
        } else {
            $('#userDomainUser').empty();
        }

    });

    $(document).on('click', '#btnSearchUserDomain', function () {
        let value2 = $('#SearchUserDomain').val()
        let value = $('#userDomain').val()
        if (value) SetDomainUser(value, value2);
    });

    $(document).on('click', '#btnSearchGroupDomain', function () {
        let value2 = $('#SearchDomainGroup').val()
        let value = $('#userDomain').val()
        if (value) getGroupByDomain(value, value2);
    });
 
    $(document).on('click', '#ADIndividual', commonDebounce(async function () {
        $('#userDomain,#userRoleName,#SearchUserDomain,#userDomainUser,#textLoginName').val('').empty();
        $('#ADGroupName,#usernameDomain,#searchStringDomain').hide();
        $('#ad_individual').show();
        $('#userCompanyName').val('');
       
    }));

    $(document).on('click', '#ADGroup', function () {
        $('#userDomain,#userRoleName,#userADGroupName,#textLoginName').val('').empty();
        $('#ad_individual').hide();      
        $('#userCompanyName').val(''); 

    })

    $(document).on('change', '#SearchUserDomain', function () {
        const value = $(this).val();
        var errorElement = $('#AdUser-error');
        validateDropDown(value, 'Enter search', errorElement);
    })

    $(document).on('change', '#SearchDomainGroup', function () {
        const value = $(this).val();
        var errorElement = $('#AdGroup-error');
        validateDropDown(value, 'Enter search', errorElement);
    })


    $(document).on('change', '#userDomainUser', function () {
        var value = $('#userDomain').val() + "\\" + $('#userDomainUser').val();
        var errorElement = $('#AdUser-error');
        $('#textLoginName').val(value);
        $('#LoginName-error').text('').removeClass('field-validation-error');
        validateDropDown($(this).val(), 'Select username', errorElement);

    });
    $(document).on('change', '#userDomainGroup', function () {
        var value = $('#userDomain').val() + "\\" + $('#userDomainGroup').val();
        var errorElement = $('#AdGroup-error');

        $('#textLoginName').val(value);
        $('#LoginName-error').text('').removeClass('field-validation-error');
        validateDropDown($(this).val(), 'Select username', errorElement);

    });

    $(document).on('keypress input', '#userMobileNum', async function (event) {
        const value = $(this).val();
        if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }
        if (!/^[0-9]+$/.test(event.key)) {
            event?.preventDefault();
        }
        await validateMobile(value);
    });

    $.getJSON("/json/CountryDailCode.json", function (data) {
        setTimeout(() => {
            data.countrycode.forEach(function (value) {
                $('#userCountryCode').append('<option value="' + value.dial_code + '">' + value.dial_code + '</option>');
            });
        }, 500);
    }).fail(function () {
        errorNotification("Failed to load JSON data.");
    });


    $(document).on('change', '#userCountryCode', async function (event) {
        const value = $(this).val();
        await validateMobilePre(value);
    });

    $(document).on('click', '#userTable .edit-button', async function () {
        loginNameEdit = 0;
        JSONDataForClickEditButton = "";
        $("#example-form-t-0").trigger("click");

        $('#chk-LDAP').prop("checked", false);
        btnCrudEnable('SaveFunction');
        $('#password-group').hide();

        const encodedData = $(this).data('user');
        userData = JSON.parse(decodeURIComponent(atob(encodedData)));
        isCreate = false;
        await populateModalFields(userData);

        const loginType = $("#userLoginType").val();
        await validateDropDown(loginType, 'Select authentication type', $('#LoginType-error'));

        $('#SaveFunction').text('Update');
        $('#userCreateModal').modal('show');

        editedLoginID = userData.id;
        const selectedRole = $("#userRoleName :selected").text();
        const isSelfEdit = editedLoginID.trim() === loggedInUserId;

        $("#chk-workflowtype").prop("disabled", isSelfEdit);

        const disableFields = (...selectors) => $(selectors.join(',')).prop('disabled', true);
        const enableFields = (...selectors) => $(selectors.join(',')).prop('disabled', false);

        const lowerRole = userRoleValue?.toLowerCase();
        const lowerSelectedRole = selectedRole?.toLowerCase();

        const permissionsMatrix = {
            superadmin: {
                superadmin: { enable: ["#userRoleName"], disable: [".selecttree", "#userWorkflowAll", "#textLoginName", "#userCompanyName"] },
                administrator: { enable: [".selecttree", "#userWorkflowAll", "#userRoleName"], disable: ["#textLoginName", "#userCompanyName"] },
                default: { enable: [".selecttree", "#userWorkflowAll", "#userRoleName"], disable: ["#textLoginName", "#userCompanyName"] }
            },
            siteadmin: {
                superadmin: {
                    action: () => {
                        SelectAllTreeView(true);
                        disableFields(".selecttree", "#userWorkflowAll", "#userCompanyName", "#textLoginName");
                        enableFields("#userRoleName");
                    }
                }
            },
            administrator: {
                superadmin: { disable: [".selecttree", "#userWorkflowAll", "#userRoleName", "#textLoginName", "#userCompanyName"] },
                administrator: { enable: [".selecttree", "#userWorkflowAll", "#userRoleName"], disable: ["#textLoginName", "#userCompanyName"] },
                default: { enable: [".selecttree", "#userWorkflowAll", "#userRoleName"], disable: ["#textLoginName", "#userCompanyName"] }
            },
            default: {
                superadmin: { enable: ["#userRoleName"], disable: [".selecttree", "#userWorkflowAll", "#userCompanyName", "#textLoginName"] },
                default: { enable: [".selecttree", "#userWorkflowAll", "#userRoleName"], disable: ["#textLoginName", "#userCompanyName"] }
            }
        };

        const currentRoleMatrix = permissionsMatrix[lowerRole] || permissionsMatrix.default;
        const access = currentRoleMatrix[lowerSelectedRole] || currentRoleMatrix.default;

        if (access?.action) {
            access.action();
        } else {
            enableFields(...(access.enable || []));
            disableFields(...(access.disable || []));
        }
    });

    $(document).on('click', '#userTable .delete-button', function () {    
            var userId = $(this).data('user-id');
            var userName = $(this).data('user-name');
            $('#deleteData').text(userName);
            $('#textDeleteId').val(userId);
    });

    $(document).on('click', '#userTable .reset-button', async function () {
            var resetUserId = $(this).data('user-id');
            var resetUserName = $(this).data('user-name');
            var resetUserEmail = $(this).data('user-email');
            const randomString = generateRandomString(5);
            $('#newPassword').val(randomString);
            await EncryptPassword(resetUserName, randomString, '#password');
            $('#resetId').val(resetUserId);
            $('#resetName').val(resetUserName);
            $('#resetData').text(resetUserName);
            $('#resetEmail').val(resetUserEmail);
        })

    $(document).on('input', '#textLoginName', commonDebounce(async function (event) {
        if (!isCreate) {
            $(this).prop('disabled', true);
            event.preventDefault();
            return;
        }
        const $this = $(this);
        const loginId = $('#textLoginId').val();
        const sanitizedValue = $this.val().replace(/\s{2,}/g, ' ').trim();

        $this.val(sanitizedValue);

        if (!$this.prop('readonly')) {
            await validateLoginName(sanitizedValue, loginId, userManageURL.nameExist);
        }

        isLoginName = true;
        $("#userPassword, #userConfirmPassword").val("");
        if ($('#LoginPassword-error').hasClass('field-validation-error') && !sanitizedValue.length) {
            $('#LoginPassword-error').text('Enter password');
        }
        if ($('#ConfirmPassword-error').hasClass('field-validation-error') && !sanitizedValue.length) {
            $('#ConfirmPassword-error').text('Enter confirm password');
        }
    }, 500));


    $(document).on('click', '.eye-change', async function () {
        const $input = $(this).prev();
        const $icon = $(this).find("i");
        const isPassword = $input.attr("type") === "password";

        if (isPassword) {
            showPassword($input, $icon);

            const encryptedPassword = $('#encriptedPassword').val();
            if (encryptedPassword?.length > 30) {
                const decrypted = await onfocusPassword(encryptedPassword);
                $input.val(decrypted);
            }
        } else {
            hidePassword($input, $icon);
            const cleanValue = $input.val().replace(/\s+/g, '');
            blurpassword($input.attr("id"), cleanValue);
        }
    });
    $(document).on('input', '#userPassword, #userConfirmPassword', function () {
        const value = this.value.replace(/\s+/g, '');
        $(this).val(value);
        const id = this.id;

        if (id === 'userPassword') {
            inputpassword(id, value);
            $('#userConfirmPassword').val('');
        } else if (id === 'userConfirmPassword') {
            inputConfirmpassword(id, value);
        }
    });

    $(document).on('blur', '#userConfirmPassword, #userPassword', function () {
        let value = this.value.replace(/\s+/g, '');
        blurpassword(this.id, value);
    });

    $(document).on('focus', '#userConfirmPassword, #userPassword', function () {
        const value = this.value.replace(/\s+/g, '');
        $(this).val(value);
        const id = this.id;

        if (id === 'userConfirmPassword') {
            focusconfirmpassword(id);;
        } else if (id === 'userPassword') {
            focuspassword(id);
        }
    });

    $(document).on('input', '#textUserName, #textEmail', async function () {
        const userId = $('#textLoginId').val();
        const $input = $(this);
        const value = $input.val().replace(/\s{2,}/g, ' ').trim();
        $input.val(value);

        if (this.id === 'textUserName') {
            await validateUserName(value, userId);
        } else if (this.id === 'textEmail') {
            await validateEmail(value, userId);
        }
    });

    $(document).on('change', '#dashboardMode', function () {
        const isChecked = this.checked;
        $('#dashboardModeValue').val(isChecked);
        $('#dashboardInputContainer').toggleClass('d-none', !isChecked);

        if (!isChecked) {
            $('#dashboardList').val('').trigger('change');
        }
    });

        $(document).on('change', '#dashboardList', function (e) {
            if (e.target.value) $('#dashboard-error').text('').removeClass('field-validation-error')
        })

    $(document).on('input', '#usersSessionTimeout', async function () {
        const $input = $(this);
        const session = $('#textLoginId').val();
        let value = $input.val().replace(/[^0-9]/g, '');

        if (value.startsWith('0') || value === '0') value = '';
        if (value.length > 5) value = value.slice(0, 5);

        $input.val(value);
        await validateSessionTimeout(value, session);
    });


        $(document).on('click', '#NextFunction', commonDebounce(async function (e) {
            ValidateAll(true);
        }, 700));


    $(document).on('click', '#previousFunction', function () {
        const isEditable = loginNameEdit === 1;
        $('#textLoginName, #userCompanyName').prop('disabled', !isEditable);

        const errorSelectors = [
            '#LoginName-error', '#LoginPassword-error', '#ConfirmPassword-error', '#Company-error',
            '#UserName-error', '#Role-error', '#Email-error', '#AlertMode-error', '#SessionTimeout-error',
            '#MobilePre-error', '#Mobile-error', '#treeList-error', '#dashboard-error'
        ];

        errorSelectors.forEach(selector => {
            $(selector).text('').removeClass('field-validation-error');
        });
    });

        $(document).on('click', '#SaveFunction', commonDebounce(async function (e) {
            var res = ($('#userLoginType').val().toLowerCase() == "ad") ? await EncryptPassword(generateRandomString(5))
                : $('#userPassword').val();
            $('#userPassword').val(res);
            await ValidateAll(false);
        }, 800));

        $(document).on('click', '#confirmDeleteButton', function () {
            btnCrudDiasable('confirmDeleteButton');
        })

        $(document).on('click', '#btnCancle', function () {
            $("#treeview").empty();
        })

        $(document).on('change', '#chk-LDAP', function () {
            if ($(this).is(':checked')) {
                $('#mobileInputContainer').show();
            } else {
                $('#mobileInputContainer').hide();
                $('#userCountryCode,#userMobileNum').val('');      
                $('#userCountryCode').val('').trigger('change')
                $('#Mobile-error,#MobilePre-error').text('').removeClass('field-validation-error');              
            }
        });

    $(document).on('click', '#btnCreate', function () {
        loginNameEdit = 1;
        userData = {};
        isCreate = true;
        const errorSelectors = [
            '#LoginName-error', '#LoginPassword-error', '#ConfirmPassword-error', '#Company-error',
            '#UserName-error', '#Role-error', '#Email-error', '#AlertMode-error', '#SessionTimeout-error',
            '#AdDomain-error', '#AdUser-error', '#LoginType-error', '#MobilePre-error', '#Mobile-error',
            '#treeList-error', '#dashboard-error'
        ];
        $("#example-form-t-0").trigger("click");
        clearInputFields('example-form', errorSelectors);
        btnCrudEnable('SaveFunction');
        $('#userRoleName').empty().val('');
        $('#password-group, #login_hide').show();
        $('#ad_hide, #mobileInputContainer').hide();
        $('#dashboardInputContainer').addClass('d-none');
        $('#userCompanyName, #userMobileNum, #userCountryCode, #dashboardList').val('');
        $('#isMobile').prop("checked", false);
        $("#textLoginName").prop('readonly', false);
        $('#userRoleName, #userLoginType, #textLoginName, #userCompanyName').prop('disabled', false);
        $('#SaveFunction').text('Save');
        $('#userCreateModal').modal('show');
    });

        $(document).on('change', '#isMobile', function () {
            if (this.checked) {
                $('#mobileInputContainer, #mobpre, #userMobileNum, #userCountryCode').show();
            }
            else {
                $("#mobileInputContainer, #mobpre, #userMobileNum, #userCountryCode").hide();             
                $('#userCountryCode,#userMobileNum').val('');        
                $('#MobilePre-error,#Mobile-error').text('').removeClass('field-validation-error');
            }
        });

    $(document).on('click', '#userReset', function () {
        btnCrudDiasable('userReset');
        });

        $(document).on('change', '.selecttree', function () {
            let check = this.checked;
            let id = $(this).attr('businessid');
            let funcId = $(this).attr('functionId');
            let infraId = $(this).attr('infraId');
            JsonTreeView(check, id, funcId, infraId);
        });

        $(document).on('click', '.Workflow-Tree details > summary', function (e) {
            e.stopPropagation();

            let $details = $(this).parent();
            let isOpen = $details.attr('open') !== undefined;
            let selectInput = $details.find('input');

            if (selectInput && selectInput.length) {
                let getInputId = selectInput[0].getAttribute('id');

                if (getInputId === 'userWorkflowAll') {
                    isOpen ? SelectAllTreeViewExpended(false) : SelectAllTreeViewExpended(true);
                }
            }
        });

    $(document).on('change', '#userWorkflowAll', function () {
        let check = $('#userWorkflowAll').prop('checked');
            check ? SelectAllTreeView(true) : SelectAllTreeView(false);
            if (check === true) {
                $("#txtinfraObjectAllFlag").val("true");
                $("#userWorkflowAll").css("background-color", "");
                $('#treeList-error').text('').removeClass('field-validation-error');
            } else {
                $('#treeList-error').text('Select at least one infraobject').addClass('field-validation-error');
            }

        });

        $(document).on('change', '#treeview', function () {
            const anyChecked = $('.selecttree:checked').length > 0;
            if (anyChecked) {
                $('#treeList-error').text('').removeClass('field-validation-error');
            } else {
                $('#treeList-error').text('Select at least one infraobject').addClass('field-validation-error');
            }
        });

    $(document).on('change', '#dashboardMode', function () {
        if (this.checked) {
            $('#dashboard-error').text('').removeClass('field-validation-error');
        }
    });

    $(document).on('change', '#userProperties', function () {
        const isChecked = this.checked;
        const businessId = $(this).attr('businessid');
        const functionId = $(this).attr('functionId');
        const infraId = $(this).attr('infraId');
        userData.userInfraObject ??= {};
        userData.userInfraObject[businessId] ??= {};
        userData.userInfraObject[businessId][functionId] ??= {};
        userData.userInfraObject[businessId][functionId][infraId] = isChecked;
        $('#userProperties').val(JSON.stringify(userData.userInfraObject));
    });

          $(document).on('click', '#btnGetDomainExe',async function () {
          await SetDomain();    
          });

        $(document).on('click', '.plus', function () {
            $(this).toggleClass("minus").siblings("ul").toggle();
        })

        $(document).on('click', 'input[type=checkbox]', function () {
            $(this).siblings("ul").find("input[type=checkbox]").prop('checked', $(this).prop('checked'));
        })

    $(document).on('change', 'input[type=checkbox]', function () {
        const checkboxId = $(this).attr("id")?.trim();
        if (!checkboxId) return;

        if (checkboxId.startsWith("c_io")) {
            updateParentCheckState($(this), "bf_l");
        }

        if (checkboxId.startsWith("c_bf")) {
            updateParentCheckState($(this), "bs_l");
        }
    });

    var checkboxElement = checkboxElement?.prop("checked", s);
    checkboxElement?.on("change", function () {
        selectTree(this);
    });   
});

