﻿using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;

namespace ContinuityPatrol.Infrastructure.Impl;

public class SmsService : ISmsService
{
    public async Task<bool> SendSmsAsync(SmsMessage sms)
    {
        var text = "<Root>\n   <ChnlId>" + sms.UserName + "</ChnlId>\n   <Key>" + sms.Password + "</Key>\n       <Row>\n           <RefId>" + sms.RecipientNo + "</RefId>\n           <MobNo>" + sms.ToMobileNo + "</MobNo>\n           <Msg>" + sms.Message + "</Msg>\n       </Row>\n   </Root>";

        try
        {

            ServicePointManager.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;

            var options = new RestClientOptions(sms.Url)
            {
                RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true
            };

            var client = new RestClient(options);

            var request = new RestRequest()
                .AddStringBody(text, DataFormat.Xml);

            var response = await client.PostAsync(request);

            if (response is { IsSuccessful: true })
            {
                return true;
            }



            //ServicePointManager.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;

            //var contentToSend = text;
            //using HttpClientHandler handler = new HttpClientHandler();
            //handler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => cert.Subject.Contains("");


            //using HttpClient client = new HttpClient(handler);
            //try
            //{

            //    var content = new StringContent(contentToSend, Encoding.UTF8, "application/xml");

            //    var response = await client.PostAsync(sms.Url, content);

            //    response.EnsureSuccessStatusCode();

            //    var responseBody = await response.Content.ReadAsStringAsync();
            //    if (response.IsSuccessStatusCode)
            //    {
            //        return true;
            //    }
            //}
            //catch (HttpRequestException e)
            //{

            //    return false;
            //}
        }
        catch (Exception ex)
        {
            return false;
        }

        return true;
    }


}