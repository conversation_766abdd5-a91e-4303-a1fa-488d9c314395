﻿using ContinuityPatrol.Application.Features.User.Commands.UpdatePassword;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.User.Commands;

public class UpdatePasswordTests : IClassFixture<UserFixture>
{
    private readonly UserFixture _userFixture;

    private readonly Mock<IUserRepository> _mockUserRepository;

    private readonly Mock<IUserCredentialRepository> _mockUserCredentialRepository;

    private readonly UpdatePasswordCommandHandler _handler;

    public UpdatePasswordTests(UserFixture userFixture)
    {
        _userFixture = userFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockUserRepository = UserRepositoryMocks.UpdatePasswordRepository(_userFixture.Users);

        var mockUserLoginRepository = UserLoginRepositoryMocks.UpdatePasswordRepository(_userFixture.UserLogins);

        _mockUserCredentialRepository = UserCredentialRepositoryMocks.UpdatePasswordRepository(_userFixture.UserCredentials);

        _handler = new UpdatePasswordCommandHandler(_mockUserRepository.Object, mockUserLoginRepository.Object, _mockUserCredentialRepository.Object, mockPublisher.Object);

    }

    [Fact]
    public async Task Handle_ValidPassword_UpdateToPasswordsRepo()
    {
        _userFixture.Users[0].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UserCredentials[0].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.OldPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.NewPassword = "K3EMIrTgB7pBjCd1IXYbMf21B7rbV90E0kSxQ5Cy4+w=$1/4qlTRv8oUdNskTiUqy8xZsb8SYpJ5iGI8Ma3AA8519Ru2icw==";
        _userFixture.UpdatePasswordCommand.ConfirmPassword = "K3EMIrTgB7pBjCd1IXYbMf21B7rbV90E0kSxQ5Cy4+w=$1/4qlTRv8oUdNskTiUqy8xZsb8SYpJ5iGI8Ma3AA8519Ru2icw==";

        _userFixture.UpdatePasswordCommand.LoginName = _userFixture.Users[0].LoginName;

        _userFixture.UpdatePasswordCommand.ConfirmPassword = _userFixture.UpdatePasswordCommand.NewPassword;

        _userFixture.UserLogins[0].UserId = _userFixture.Users[0].ReferenceId;

        _userFixture.UserCredentials[0].UserId = _userFixture.Users[0].ReferenceId;

        var result = await _handler.Handle(_userFixture.UpdatePasswordCommand, CancellationToken.None);

        var user = await _mockUserRepository.Object.GetByReferenceIdAsync(result.UserId);

        Assert.Equal(_userFixture.UpdatePasswordCommand.LoginName, user.LoginName);

        _userFixture.UpdatePasswordCommand.ConfirmPassword = _userFixture.UpdatePasswordCommand.ConfirmPassword;
    }

    [Fact]
    public async Task Handle_ValidPassword_UpdateToPasswordsRepo_FiveTimeAttempt()
    {
        _userFixture.UpdatePasswordCommand.LoginName = _userFixture.Users[0].LoginName;

        _userFixture.UpdatePasswordCommand.OldPassword = _userFixture.Users[0].LoginPassword;

        _userFixture.UpdatePasswordCommand.ConfirmPassword = _userFixture.UpdatePasswordCommand.NewPassword;
        _userFixture.Users[0].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UserCredentials[0].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UserCredentials[1].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UserCredentials[2].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UserCredentials[3].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UserCredentials[4].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.OldPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.NewPassword = "K3EMIrTgB7pBjCd1IXYbMf21B7rbV90E0kSxQ5Cy4+w=$1/4qlTRv8oUdNskTiUqy8xZsb8SYpJ5iGI8Ma3AA8519Ru2icw==";
        _userFixture.UpdatePasswordCommand.ConfirmPassword = "K3EMIrTgB7pBjCd1IXYbMf21B7rbV90E0kSxQ5Cy4+w=$1/4qlTRv8oUdNskTiUqy8xZsb8SYpJ5iGI8Ma3AA8519Ru2icw==";

        _userFixture.UserLogins[0].UserId = _userFixture.Users[0].ReferenceId;

        _userFixture.UserCredentials[0].UserId = _userFixture.Users[0].ReferenceId;
        _userFixture.UserCredentials[1].UserId = _userFixture.Users[0].ReferenceId;
        _userFixture.UserCredentials[2].UserId = _userFixture.Users[0].ReferenceId;
        _userFixture.UserCredentials[3].UserId = _userFixture.Users[0].ReferenceId;
        _userFixture.UserCredentials[4].UserId = _userFixture.Users[0].ReferenceId;

        _userFixture.UserCredentials[0].IsActive = false;

        var result = await _handler.Handle(_userFixture.UpdatePasswordCommand, CancellationToken.None);

        await _mockUserCredentialRepository.Object.GetUserCredentialByUserId(result.UserId);
    }

    [Fact]
    public async Task Handle_Return_ValidPasswordResponse_When_PasswordUpdated()
    {
        _userFixture.UpdatePasswordCommand.LoginName = _userFixture.Users[0].LoginName;

        _userFixture.UpdatePasswordCommand.OldPassword = _userFixture.Users[0].LoginPassword;

        _userFixture.UpdatePasswordCommand.ConfirmPassword = _userFixture.UpdatePasswordCommand.NewPassword;

        _userFixture.Users[0].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UserCredentials[0].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.OldPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.NewPassword = "K3EMIrTgB7pBjCd1IXYbMf21B7rbV90E0kSxQ5Cy4+w=$1/4qlTRv8oUdNskTiUqy8xZsb8SYpJ5iGI8Ma3AA8519Ru2icw==";
        _userFixture.UpdatePasswordCommand.ConfirmPassword = "K3EMIrTgB7pBjCd1IXYbMf21B7rbV90E0kSxQ5Cy4+w=$1/4qlTRv8oUdNskTiUqy8xZsb8SYpJ5iGI8Ma3AA8519Ru2icw==";

        _userFixture.UserLogins[0].UserId = _userFixture.Users[0].ReferenceId;

        _userFixture.UserCredentials[0].UserId = _userFixture.Users[0].ReferenceId;

        var result = await _handler.Handle(_userFixture.UpdatePasswordCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdatePasswordResponse));

        result.UserId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.PasswordUpdatedSuccessfully);
    }

    [Fact]
    public async Task ThrowNotFoundException_When_InvalidOldPassword()
    {
        _userFixture.UpdatePasswordCommand.LoginName = _userFixture.Users[0].LoginName;
        _userFixture.Users[0].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.OldPassword = "phMFeP00rBjWj/rzdlyB0m1Ekbk/E/lv56OJv2YET+8=$Mc7GR+Xlq9zqp9PtC9Qm4d9qaJMJiB7tKw6uW7hDGRe6SbQXnA==";
        _userFixture.UpdatePasswordCommand.NewPassword = "K3EMIrTgB7pBjCd1IXYbMf21B7rbV90E0kSxQ5Cy4+w=$1/4qlTRv8oUdNskTiUqy8xZsb8SYpJ5iGI8Ma3AA8519Ru2icw==";
        _userFixture.UpdatePasswordCommand.ConfirmPassword = "K3EMIrTgB7pBjCd1IXYbMf21B7rbV90E0kSxQ5Cy4+w=$1/4qlTRv8oUdNskTiUqy8xZsb8SYpJ5iGI8Ma3AA8519Ru2icw==";

        _userFixture.UserLogins[0].UserId = _userFixture.Users[0].ReferenceId;

        _userFixture.UserCredentials[0].UserId = _userFixture.Users[0].ReferenceId;

        var result = await Assert.ThrowsAsync<InvalidPasswordException>(() => _handler.Handle(_userFixture.UpdatePasswordCommand, CancellationToken.None));

        result.Message.ShouldContain(CommonConstants.InvalidOldPassword);
    }

    [Fact]
    public async Task ThrowNotFoundException_When_PasswordUnique()
    {
        _userFixture.Users[0].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.OldPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.NewPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";

        _userFixture.UpdatePasswordCommand.LoginName = _userFixture.Users[0].LoginName;

        _userFixture.UserLogins[0].UserId = _userFixture.Users[0].ReferenceId;

        _userFixture.UserCredentials[0].UserId = _userFixture.Users[0].ReferenceId;

        var result = await Assert.ThrowsAsync<InvalidPasswordException>(() => _handler.Handle(_userFixture.UpdatePasswordCommand, CancellationToken.None));

        result.Message.ShouldContain(CommonConstants.PasswordUnique);
    }

    [Fact]
    public async Task ThrowNotFoundException_When_InvalidConfirmPassword()
    {
        _userFixture.UpdatePasswordCommand.LoginName = _userFixture.Users[0].LoginName;

        _userFixture.UpdatePasswordCommand.OldPassword = _userFixture.Users[0].LoginPassword;
        _userFixture.Users[0].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.OldPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.NewPassword = "K3EMIrTgB7pBjCd1IXYbMf21B7rbV90E0kSxQ5Cy4+w=$1/4qlTRv8oUdNskTiUqy8xZsb8SYpJ5iGI8Ma3AA8519Ru2icw==";
        _userFixture.UpdatePasswordCommand.ConfirmPassword = "yIS0vz1Xm8WR8zeeW3AqZSl7LSLbT5Ts8q8jn9I9mSI=$AABKQuLLqyCmv2RcqxkAOfmn4mBeRP++mZGXGaELTYOXeHHdOw==";

        _userFixture.UserLogins[0].UserId = _userFixture.Users[0].ReferenceId;

        _userFixture.UserCredentials[0].UserId = _userFixture.Users[0].ReferenceId;

        var result = await Assert.ThrowsAsync<InvalidPasswordException>(() => _handler.Handle(_userFixture.UpdatePasswordCommand, CancellationToken.None));

        result.Message.ShouldContain(CommonConstants.InvalidConfirmPassword);
    }

    [Fact]
    public async Task ThrowNotFoundException_When_LastFivePassword_CanNot_Be_Accepted()
    {
        _userFixture.UpdatePasswordCommand.LoginName = _userFixture.Users[0].LoginName;

        _userFixture.UpdatePasswordCommand.OldPassword = _userFixture.Users[0].LoginPassword;

        _userFixture.UpdatePasswordCommand.ConfirmPassword = _userFixture.UpdatePasswordCommand.NewPassword;
        _userFixture.Users[0].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UserCredentials[0].LoginPassword = "L9vv5CcTxWUdqJpoX6cjpL9hUQLYtyqHibRNsd5/H88=$hj6/0tM0PptBfXKZiy0O/QD7NqMOlv34ao+FmhMqUCXDKWCoGw==";
        _userFixture.UserCredentials[1].LoginPassword = "qofyQuFiCXIZhhT2b0sJYXQrxUqI2nCpAjIcfbItdvU=$R6scjOG02kZ7IIDibnBWfThiKpHXG7aHXmzCwpFuG4x403+ZYQ==";
        _userFixture.UserCredentials[2].LoginPassword = "R4e/bV5tubS6ocJMHx1YcoguEJB1P1OnWLwcFMChOe0=$+a/OpbXQoP5o0yOZPau6bKYPFfzTcB2dqUXI3XPPjuYKOaf8gg==";
        _userFixture.UserCredentials[3].LoginPassword = "VP1GhsZSh8HAn49oxgksA7oV5T2aI7y9MQlvWQlpRHA=$EP3GtvtkCZW1QcwFr81Jqumk7oIuo5cBPnkZh40ekFtTE/D4BA==";
        _userFixture.UserCredentials[4].LoginPassword = "V0OhyiNsf3NMu5SNAgwymnnzv3R98jWd0YqGvUguSeQ=$RL+6MiYRknk74m9WuuOlg2mT1H86vUU7OFDYL9tPrgWECE3wrQ==";
        _userFixture.UpdatePasswordCommand.OldPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.NewPassword = "pTJ2/4kQGdYYbGoxdVwd9vVYcQKBjyFwpdn2vCOd7e8=$okmQwqEz0FwSZT1Z0OoKiYXLOxHRjEW8xY7q9dpJMDLCz96zEg==";
        _userFixture.UpdatePasswordCommand.ConfirmPassword = "pTJ2/4kQGdYYbGoxdVwd9vVYcQKBjyFwpdn2vCOd7e8=$okmQwqEz0FwSZT1Z0OoKiYXLOxHRjEW8xY7q9dpJMDLCz96zEg==";

        _userFixture.UserLogins[0].UserId = _userFixture.Users[0].ReferenceId;

        _userFixture.UserCredentials[0].UserId = _userFixture.Users[0].ReferenceId;
        _userFixture.UserCredentials[1].UserId = _userFixture.Users[0].ReferenceId;
        _userFixture.UserCredentials[2].UserId = _userFixture.Users[0].ReferenceId;
        _userFixture.UserCredentials[3].UserId = _userFixture.Users[0].ReferenceId;
        _userFixture.UserCredentials[4].UserId = _userFixture.Users[0].ReferenceId;

        var result = await Assert.ThrowsAsync<InvalidPasswordException>(() => _handler.Handle(_userFixture.UpdatePasswordCommand, CancellationToken.None));

        result.Message.ShouldContain("Please try with a different password, Last five password can not be accepted.");
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _userFixture.UpdatePasswordCommand.LoginName = _userFixture.Users[0].LoginName;

        _userFixture.UpdatePasswordCommand.OldPassword = _userFixture.Users[0].LoginPassword;

        _userFixture.UpdatePasswordCommand.ConfirmPassword = _userFixture.UpdatePasswordCommand.NewPassword;

        _userFixture.Users[0].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UserCredentials[0].LoginPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.OldPassword = "fUp0SYr5JAbOz1WRMUhaJwKY/uZQDwLSCALN1l88pCo=$FtUAoQ7A+vYGx0OkOIpAmujuxUqtns04XstuZRaMfSW7BZhTfg==";
        _userFixture.UpdatePasswordCommand.NewPassword = "K3EMIrTgB7pBjCd1IXYbMf21B7rbV90E0kSxQ5Cy4+w=$1/4qlTRv8oUdNskTiUqy8xZsb8SYpJ5iGI8Ma3AA8519Ru2icw==";
        _userFixture.UpdatePasswordCommand.ConfirmPassword = "K3EMIrTgB7pBjCd1IXYbMf21B7rbV90E0kSxQ5Cy4+w=$1/4qlTRv8oUdNskTiUqy8xZsb8SYpJ5iGI8Ma3AA8519Ru2icw==";

        _userFixture.UserLogins[0].UserId = _userFixture.Users[0].ReferenceId;

        _userFixture.UserCredentials[0].UserId = _userFixture.Users[0].ReferenceId;

        await _handler.Handle(_userFixture.UpdatePasswordCommand, CancellationToken.None);

        _mockUserRepository.Verify(x => x.FindByLoginNameAsync(It.IsAny<string>()), Times.Once);

        _mockUserRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.User>()), Times.Once);
    }
}