﻿@model ContinuityPatrol.Domain.ViewModels.AdPasswordJobModel.AdPasswordJobViewModel

@Html.AntiForgeryToken()
<div class="modal-dialog modal-sm modal-dialog-centered">
    <div class="modal-content">
        <form asp-controller="ADPasswordExpire" asp-action="DeleteAd" asp-route-id="textAdDeleteId" enctype="multipart/form-data">
            <div class="modal-header p-0">
                <img class="delete-img" src="~/img/isomatric/delete.png" alt="Delete Img" />
            </div>
            <div class="modal-body text-center pt-0">
                <h5 class="fw-bold">Are you sure?</h5>
                <p class="d-flex align-items-center justify-content-center gap-1">You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="adDeleteData"></span> data?</p>
                <input asp-for="Id" type="hidden" id="textAdDeleteId" name="id" class="form-control" />
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="adConfirmDeleteButton">Yes</button>
            </div>
        </form>
    </div>
</div> 