using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class VeritasClusterFixture : IDisposable
{
    public List<VeritasCluster> VeritasClusterPaginationList { get; set; }
    public List<VeritasCluster> VeritasClusterList { get; set; }
    public VeritasCluster VeritasClusterDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public VeritasClusterFixture()
    {
        var fixture = new Fixture();

        VeritasClusterList = fixture.Create<List<VeritasCluster>>();

        VeritasClusterPaginationList = fixture.CreateMany<VeritasCluster>(20).ToList();

        VeritasClusterDto = fixture.Create<VeritasCluster>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
