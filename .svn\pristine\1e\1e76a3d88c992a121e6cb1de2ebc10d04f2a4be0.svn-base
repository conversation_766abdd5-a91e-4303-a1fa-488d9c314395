﻿using ContinuityPatrol.Application.Features.PluginManagerHistory.Commands.Create;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Commands.Update;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Events.Create;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Events.Delete;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class PluginManagerHistoryFixture : IDisposable
{
    public IMapper Mapper { get; }
    
    public List<PluginManagerHistory> PluginManagerHistories { get; set; }

    public List<UserActivity> UserActivities { get; set; }

    public CreatePluginManagerHistoryCommand CreatePluginManagerHistoryCommand { get; set; }

    public UpdatePluginManagerHistoryCommand UpdatePluginManagerHistoryCommand { get; set; }

    public PluginManagerHistoryCreatedEvent PluginManagerHistoryCreatedEvent { get; set; }

    public PluginManagerHistoryDeletedEvent PluginManagerHistoryDeletedEvent { get; set; }

    public PluginManagerHistoryUpdatedEvent PluginManagerHistoryUpdatedEvent { get; set; }


    public Fixture AutoPluginManagerHistoryFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreatePluginManagerHistoryCommand>(p => p.PluginManagerName, 10));
            fixture.Customize<CreatePluginManagerHistoryCommand>(c => c.With(b => b.PluginManagerName, 10.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdatePluginManagerHistoryCommand>(p => p.PluginManagerName, 10));
            fixture.Customize<UpdatePluginManagerHistoryCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<FormHistory>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserActivity>(p => p.LoginName, 10));
            fixture.Customize<UpdatePluginManagerHistoryCommand>(c => c.With(b => b.LoginName, 0.ToString()));
            fixture.Customize<PluginManagerHistory>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<PluginManagerHistoryCreatedEvent>(p => p.PluginManagerName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<PluginManagerHistoryDeletedEvent>(p => p.PluginManagerName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<PluginManagerHistoryUpdatedEvent>(p => p.PluginManagerName, 10));

            return fixture;
        }
    }

    public PluginManagerHistoryFixture()
    {
        PluginManagerHistories = AutoPluginManagerHistoryFixture.Create<List<PluginManagerHistory>>();

        UserActivities = AutoPluginManagerHistoryFixture.Create<List<UserActivity>>();

        CreatePluginManagerHistoryCommand = AutoPluginManagerHistoryFixture.Create<CreatePluginManagerHistoryCommand>();

        UpdatePluginManagerHistoryCommand = AutoPluginManagerHistoryFixture.Create<UpdatePluginManagerHistoryCommand>();

        PluginManagerHistoryCreatedEvent = AutoPluginManagerHistoryFixture.Create<PluginManagerHistoryCreatedEvent>();

        PluginManagerHistoryDeletedEvent = AutoPluginManagerHistoryFixture.Create<PluginManagerHistoryDeletedEvent>();

        PluginManagerHistoryUpdatedEvent = AutoPluginManagerHistoryFixture.Create<PluginManagerHistoryUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<PluginManagerHistoryProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public void Dispose()
    {

    }
}