﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class MssqlAlwaysOnMonitorStatusRepositoryMocks
{
    public static Mock<IMssqlAlwaysOnMonitorStatusRepository> CreateMssqlAlwaysOnMonitorStatusRepository(List<MSSQLAlwaysOnMonitorStatus> mssqlAlwaysOnMonitorStatues)
    {
        var mssqlAlwaysOnMonitorStatusRepository = new Mock<IMssqlAlwaysOnMonitorStatusRepository>();

        mssqlAlwaysOnMonitorStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(mssqlAlwaysOnMonitorStatues);

        mssqlAlwaysOnMonitorStatusRepository.Setup(repo => repo.AddAsync(It.IsAny<MSSQLAlwaysOnMonitorStatus>())).ReturnsAsync(
            (MSSQLAlwaysOnMonitorStatus mssqlAlwaysOnMonitorStatus) =>
            {
                mssqlAlwaysOnMonitorStatus.Id = new Fixture().Create<int>();

                mssqlAlwaysOnMonitorStatus.ReferenceId = new Fixture().Create<Guid>().ToString();

                mssqlAlwaysOnMonitorStatues.Add(mssqlAlwaysOnMonitorStatus);

                return mssqlAlwaysOnMonitorStatus;
            });

        return mssqlAlwaysOnMonitorStatusRepository;
    }

    public static Mock<IMssqlAlwaysOnMonitorStatusRepository> UpdateMssqlAlwaysOnMonitorStatusRepository(List<MSSQLAlwaysOnMonitorStatus> mssqlAlwaysOnMonitorStatues)
    {
        var mssqlAlwaysOnMonitorStatusRepository = new Mock<IMssqlAlwaysOnMonitorStatusRepository>();

        mssqlAlwaysOnMonitorStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(mssqlAlwaysOnMonitorStatues);

        mssqlAlwaysOnMonitorStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => mssqlAlwaysOnMonitorStatues.SingleOrDefault(x => x.ReferenceId == i));

        mssqlAlwaysOnMonitorStatusRepository.Setup(repo => repo.UpdateAsync(It.IsAny<MSSQLAlwaysOnMonitorStatus>())).ReturnsAsync((MSSQLAlwaysOnMonitorStatus mssqlAlwaysOnMonitorStatus) =>
        {
            var index = mssqlAlwaysOnMonitorStatues.FindIndex(item => item.ReferenceId == mssqlAlwaysOnMonitorStatus.ReferenceId);

            mssqlAlwaysOnMonitorStatues[index] = mssqlAlwaysOnMonitorStatus;

            return mssqlAlwaysOnMonitorStatus;

        });
        return mssqlAlwaysOnMonitorStatusRepository;
    }

    public static Mock<IMssqlAlwaysOnMonitorStatusRepository> GetMssqlAlwaysOnMonitorStatusRepository(List<MSSQLAlwaysOnMonitorStatus> mssqlAlwaysOnMonitorStatues)
    {
        var mssqlAlwaysOnMonitorStatusRepository = new Mock<IMssqlAlwaysOnMonitorStatusRepository>();

        mssqlAlwaysOnMonitorStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(mssqlAlwaysOnMonitorStatues);

        mssqlAlwaysOnMonitorStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => mssqlAlwaysOnMonitorStatues.SingleOrDefault(x => x.ReferenceId == i));

        return mssqlAlwaysOnMonitorStatusRepository;
    }

    public static Mock<IMssqlAlwaysOnMonitorStatusRepository> GetMssqlAlwaysOnMonitorStatusEmptyRepository()
    {
        var mssqlAlwaysOnMonitorStatusRepository = new Mock<IMssqlAlwaysOnMonitorStatusRepository>();

        mssqlAlwaysOnMonitorStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<MSSQLAlwaysOnMonitorStatus>());

        mssqlAlwaysOnMonitorStatusRepository.Setup(repo => repo.GetDetailByType(It.IsAny<string>())).ReturnsAsync(new List<MSSQLAlwaysOnMonitorStatus>());

        return mssqlAlwaysOnMonitorStatusRepository;
    }

    public static Mock<IMssqlAlwaysOnMonitorStatusRepository> GetMssqlAlwaysOnMonitorStatusTypeRepository(List<MSSQLAlwaysOnMonitorStatus> mssqlAlwaysOnMonitorStatues)
    {
        var mssqlAlwaysOnMonitorStatusRepository = new Mock<IMssqlAlwaysOnMonitorStatusRepository>();

        mssqlAlwaysOnMonitorStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(mssqlAlwaysOnMonitorStatues);

        mssqlAlwaysOnMonitorStatusRepository.Setup(repo => repo.GetDetailByType(It.IsAny<string>())).ReturnsAsync(mssqlAlwaysOnMonitorStatues);

        mssqlAlwaysOnMonitorStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => mssqlAlwaysOnMonitorStatues.SingleOrDefault(x => x.ReferenceId == i));

        return mssqlAlwaysOnMonitorStatusRepository;
    }

    public static Mock<IMssqlAlwaysOnMonitorStatusRepository> GetPaginatedMssqlAlwaysOnMonitorStatusRepository(List<MSSQLAlwaysOnMonitorStatus> mssqlAlwaysOnMonitorStatues)
    {
        var mssqlAlwaysOnMonitorStatusRepository = new Mock<IMssqlAlwaysOnMonitorStatusRepository>();

        var queryableMssqlAlwaysOnMonitorStatus = mssqlAlwaysOnMonitorStatues.BuildMock();

        mssqlAlwaysOnMonitorStatusRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableMssqlAlwaysOnMonitorStatus);

        return mssqlAlwaysOnMonitorStatusRepository;
    }
}