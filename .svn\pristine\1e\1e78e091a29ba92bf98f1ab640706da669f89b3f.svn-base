using ContinuityPatrol.Services.Db.Impl.Admin;
using ContinuityPatrol.Services.Db.Impl.Alert;
using ContinuityPatrol.Services.Db.Impl.Configuration;
using ContinuityPatrol.Services.Db.Impl.Cyber;
using ContinuityPatrol.Services.Db.Impl.Dashboard;
using ContinuityPatrol.Services.Db.Impl.Drift;
using ContinuityPatrol.Services.Db.Impl.FiaBia;
using ContinuityPatrol.Services.Db.Impl.Manage;
using ContinuityPatrol.Services.Db.Impl.Orchestration;
using ContinuityPatrol.Services.Db.Impl.Report;
using ContinuityPatrol.Services.Db.Provider;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Identity;
using ContinuityPatrol.Shared.Services.Provider;

namespace ContinuityPatrol.Services.Db.Extension;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddSharedDbServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));

        #region DbServices
        services.AddTransient<IWorkflowApprovalMappingService, WorkflowApprovalMappingService>();

        services.AddTransient<IGlobalVariableService, GlobalVariableService>();
        services.AddTransient<ICyberJobWorkflowSchedulerService, CyberJobWorkflowSchedulerService>();
        services.AddTransient<IFiaCostService, FiaCostService>();
        services.AddTransient<ITwoStepAuthenticationService, TwoStepAuthenticationService>();
        services.AddTransient<ICGExecutionReportService, CGExecutionReportService>();

        services.AddTransient<IApprovalMatrixUsersService, ApprovalMatrixUsersService>();

        services.AddTransient<IDataSyncJobService, DataSyncJobService>();
        services.AddTransient<IComponentSaveAllService, ComponentSaveAllService>();
        services.AddTransient<IWorkflowDrCalenderService, WorkflowDrCalenderService>();

        services.AddTransient<IAdPasswordJobService, AdPasswordJobService>();

        services.AddTransient<IAdPasswordExpireService, AdPasswordExpireService>();

        services.AddTransient<IFiaIntervalService, FiaIntervalService>();

        services.AddTransient<IFiaImpactTypeService, FiaImpactTypeService>();

        services.AddTransient<IFiaImpactCategoryService, FiaImpactCategoryService>();

        services.AddTransient<ICyberAirGapService, CyberAirGapService>();

        services.AddTransient<ICyberSnapsService, CyberSnapsService>();

        services.AddTransient<ICyberAlertService, CyberAlertService>();
        services.AddTransient<ICyberComponentMappingService, CyberComponentMappingService>();
        services.AddTransient<ICyberAirGapLogService, CyberAirGapLogService>();

        services.AddTransient<ICyberAirGapStatusService, CyberAirGapStatusService>();


        services.AddTransient<IDriftEventService, DriftEventService>();

        services.AddTransient<ICyberJobManagementService, CyberJobManagementService>();

        services.AddTransient<ICyberComponentGroupService, CyberComponentGroupService>();

        services.AddTransient<ICyberComponentService, CyberComponentService>();



        services.AddTransient<IDriftResourceSummaryService, DriftResourceSummaryService>();

        services.AddTransient<IDriftManagementMonitorStatusService, DriftManagementMonitorStatusService>();

        services.AddTransient<IDriftJobService, DriftJobService>();

        services.AddTransient<IApprovalMatrixApprovalService, ApprovalMatrixApprovalService>();

        services.AddTransient<IApprovalMatrixRequestService, ApprovalMatrixRequestService>();

        services.AddTransient<IDriftProfileService, DriftProfileService>();

        services.AddTransient<IDriftImpactTypeMasterService, DriftImpactTypeMasterService>();

        services.AddTransient<IDriftCategoryMasterService, DriftCategoryMasterService>();

        services.AddTransient<IDriftParameterService, DriftParameterService>();

        services.AddTransient<IEmployeeService, EmployeeService>();


        services.AddTransient<IBulkImportServices, BulkImportService>();
        services.AddTransient<IBulkImportActionResultService, BulkImportActionResultService>();

        services.AddTransient<IBulkImportOperationGroupService, BulkImportOperationGroupService>();

        services.AddTransient<IBulkImportOperationService, BulkImportOperationService>();

        services.AddTransient<IDynamicDashboardWidgetService, DynamicDashboardWidgetService>();

        services.AddTransient<IDynamicDashboardMapService, DynamicDashboardMapService>();

        services.AddTransient<IDynamicSubDashboardService, DynamicSubDashboardService>();

        services.AddTransient<IDynamicDashboardService, DynamicDashboardService>();


        services.AddTransient<IRoboCopyJobService, RoboCopyJobService>();
        services.AddTransient<IRsyncJobService, RsyncJobService>();
        services.AddTransient<IVeritasClusterService, VeritasClusterService>();

        services.AddTransient<IHacmpClusterService, HacmpClusterService>();

        services.AddTransient<IFiaTemplateService, FiaTemplateService>();

        services.AddTransient<IBiaRulesService, BiaRulesService>();


        services.AddTransient<IDb2HaDrMonitorLogService, Db2HaDrMonitorLogService>();
        services.AddTransient<IBackUpLogService, BackUpLogService>();

        services.AddTransient<IInfraMasterService, InfraMasterService>();


        services.AddTransient<IBackUpService, BackUpService>();

        services.AddTransient<IArchiveService, ArchiveService>();


        services.AddTransient<IPageWidgetService, PageWidgetService>();

        services.AddTransient<IRsyncOptionService, RsyncOptionService>();

        services.AddTransient<IRoboCopyService, RoboCopyService>();

        services.AddTransient<IDataSyncOptionsService, DataSyncOptionsService>();

        services.AddTransient<IImpactActivityService, ImpactActivityService>();

        services.AddTransient<IPageBuilderService, PageBuilderService>();

        services.AddTransient<ISiteLocationService, SiteLocationService>();

        services.AddTransient<IUserLoginService, UserLoginService>();


        services.AddTransient<IIncidentManagementService, IncidentManagementService>();
        services.AddTransient<IIncidentManagementSummaryService, IncidentManagementSummaryService>();

        services.AddTransient<IWorkflowActionFieldMasterService, WorkflowActionFieldMasterService>();


        services.AddTransient<IAccountService, AccountService>();
        services.AddTransient<ISolutionHistoryService, SolutionHistoryService>();
        services.AddTransient<ICompanyService, CompanyService>();
        services.AddTransient<IUserService, UserService>();
        services.AddTransient<IComponentTypeService, ComponentTypeService>();
        services.AddTransient<ILoginService, LoginService>();
        services.AddTransient<IDataSetColumnsService, DataSetColumnsService>();
        services.AddTransient<ISiteService, SiteService>();
        services.AddTransient<ISiteTypeService, SiteTypeService>();
        services.AddTransient<IBusinessFunctionService, BusinessFunctionService>();
        services.AddTransient<IBusinessServiceService, BusinessServiceService>();
        services.AddTransient<IAccessManagerService, AccessManagerService>();
        services.AddTransient<IUserRoleService, UserRoleService>();
        services.AddTransient<IInfraObjectService, InfraObjectService>();
        services.AddTransient<ILoadBalancerService, LoadBalancerService>();
        services.AddTransient<IDataSetService, DataSetService>();
        services.AddTransient<ISettingService, SettingService>();
        services.AddTransient<ITableAccessService, TableAccessService>();
        services.AddTransient<IServerService, ServerService>();
        services.AddTransient<IDatabaseService, DatabaseService>();
        services.AddTransient<IReplicationService, ReplicationService>();
        services.AddTransient<INodeService, NodeService>();
        services.AddTransient<IMonitorService, MonitorService>();
        services.AddTransient<ICredentialProfileService, CredentialProfileService>();
        services.AddTransient<IWorkflowInfraObjectService, WorkflowInfraObjectService>();
        services.AddTransient<IWorkflowProfileService, WorkflowProfileService>();
        services.AddTransient<IWorkflowProfileInfoService, WorkflowProfileInfoService>();
        services.AddTransient<ITemplateService, TemplateService>();
        services.AddTransient<IFormService, FormService>();
        services.AddTransient<IFormTypeService, FormTypeService>();
        services.AddTransient<IWorkflowCategoryService, WorkflowCategoryService>();
        services.AddTransient<IWorkflowActionService, WorkflowActionService>();
        services.AddTransient<IWorkflowHistoryService, WorkflowHistoryService>();
        services.AddTransient<ILicenseManagerService, LicenseManagerService>();
        services.AddTransient<ISingleSignOnService, SingleSignOnService>();
        services.AddTransient<ISmtpConfigurationService, SmtpConfigurationService>();
        services.AddTransient<IUserActivityService, UserActivityService>();
        services.AddTransient<IAccessManagerService, AccessManagerService>();
        services.AddTransient<ICredentialProfileService, CredentialProfileService>();
        services.AddTransient<IAlertService, AlertService>();
        services.AddTransient<IAlertInformationService, AlertInformationService>();
        services.AddTransient<IAlertMasterService, AlertMasterService>();
        services.AddTransient<IWorkflowPermissionService, WorkflowPermissionService>();
        services.AddTransient<IUserGroupService, UserGroupService>();
        services.AddTransient<IMssqlAlwaysOnMonitorLogsService, MssqlAlwaysOnMonitorLogsService>();
        services.AddTransient<IOracleMonitorLogsService, OracleMonitorLogsService>();
        services.AddTransient<IWorkflowOperationGroupService, WorkflowOperationGroupService>();
        services.AddTransient<IMssqlAlwaysOnMonitorStatusService, MssqlAlwaysOnMonitorStatusService>();
        services.AddTransient<IAlertNotificationService, AlertNotificationService>();
        services.AddTransient<IMssqlMonitorLogsService, MssqlMonitorLogsService>();
        services.AddTransient<IOracleRACMonitorLogsService, OracleRACMonitorLogsService>();
        services.AddTransient<IMssqlMonitorStatusService, MssqlMonitorStatusService>();
        services.AddTransient<IMysqlMonitorLogsService, MysqlMonitorLogsService>();
        services.AddTransient<IOracleMonitorStatusService, OracleMonitorStatusService>();
        services.AddTransient<IOracleRACMonitorStatusService, OracleRACMonitorStatusService>();
        services.AddTransient<IPostgresMonitorLogsService, PostgresMonitorLogsService>();
        services.AddTransient<IPostgresMonitorStatusService, PostgresMonitorStatusService>();
        services.AddTransient<IMysqlMonitorStatusService, MysqlMonitorStatusService>();
        services.AddTransient<IWorkflowActionTypeService, WorkflowActionTypeService>();
        services.AddTransient<IPluginManagerService, PluginManagerService>();
        services.AddTransient<IWorkflowOperationService, WorkflowOperationService>();
        services.AddTransient<IJobService, JobService>();
        services.AddTransient<IDrReadyService, DrReadyService>();
        services.AddTransient<IAlertReceiverService, AlertReceiverService>();
        services.AddTransient<IManageWorkflowListService, ManageWorkflowService>();
        services.AddTransient<IFourEyeApproverService, FourEyeApproverService>();
        services.AddTransient<IWorkflowService, WorkflowService>();
        services.AddTransient<IReplicationMasterService, ReplicationMasterService>();
        services.AddTransient<IInfraReplicationMappingService, InfraReplicationMappingService>();
        services.AddTransient<IAboutCpService, AboutCpService>();
        services.AddTransient<IFormMappingService, FormMappingService>();
        services.AddTransient<IMonitorServicesService, MonitorServicesService>();
        services.AddTransient<IWorkflowActionResultService, WorkflowActionResultService>();
        services.AddTransient<IRiskMitigationService, RiskMitigationService>();
        services.AddTransient<IUserInfraObjectService, UserInfraObjectService>();
        services.AddTransient<ISmsConfigurationService, SmsConfigurationService>();
        services.AddTransient<IBusinessServiceAvailabilityService, BusinessServiceAvailabilityService>();
        services.AddTransient<IBusinessServiceEvaluationService, BusinessServiceEvaluationService>();
        services.AddTransient<ILicenseInfoService, LicenseInfoService>();
        services.AddTransient<IBusinessServiceHealthStatusService, BusinessServiceHealthStatusService>();
        services.AddTransient<IHeatMapStatusService, HeatMapStatusService>();
        services.AddTransient<IDrReadyStatusService, DrReadyStatusService>();
        services.AddTransient<IInfraObjectInfoService, InfraObjectInfoService>();
        services.AddTransient<IInfraSummaryService, InfraSummaryService>();
        services.AddTransient<IImpactAvailabilityService, ImpactAvailabilityService>();
        services.AddTransient<IDashboardViewService, DashboardViewService>();
        services.AddTransient<IWorkflowExecutionTempService, WorkflowExecutionTempService>();
        services.AddTransient<IDashboardViewLogService, DashboardViewLogService>();
        services.AddTransient<IGroupPolicyService, GroupPolicyService>();
        // services.AddTransient<INodeConfigurationService, Impl.Admin.LoadBalancerService>();
        services.AddTransient<IDrCalenderService, DrCalenderService>();
        services.AddTransient<IRpoSlaDeviationReportService, RpoSlaDeviationReportService>();
        services.AddTransient<IReportService, ReportService>();
        services.AddTransient<IReportScheduleService, ReportScheduleService>();
        services.AddTransient<IDb2HaDrMonitorStatusService, Db2HaDrMonitorStatusService>();
        services.AddTransient<IMsSqlNativeLogShippingMonitorStatusService, MsSqlNativeLogShippingMonitorStatusService>();
        services.AddTransient<IMongoDbMonitorStatusService, MongoDbMonitorStatusService>();
        services.AddTransient<ISvcMsSqlMonitorStatusService, SvcMsSqlMonitorStatusService>();
        services.AddTransient<IWorkflowPredictionService, WorkflowPredictionService>();
        services.AddTransient<IGlobalSettingService, GlobalSettingService>();
        services.AddTransient<IMSSQLDbMirroringMonitorStatusService, MSSQLDbMirroringMonitorStatusService>();
        services.AddTransient<IServerTypeService, ServerTypeService>();
        services.AddTransient<IServerSubTypeService, ServerSubTypeService>();
        services.AddTransient<ITeamMasterService, TeamMasterService>();
        services.AddTransient<ITeamResourceService, TeamResourceService>();
        services.AddTransient<IApprovalMatrixService, ApprovalMatrixService>();
        services.AddTransient<IEscalationMatrixService, EscalationMatrixService>();
        services.AddTransient<IEscalationMatrixLevelService, EscalationMatrixLevelService>();
        services.AddScoped<IDataProvider, DbProvider>();
        services.AddTransient<IMSSQLDbMirroringMonitorLogsService, MSSQLDbMirroringMonitorLogsService>();
        services.AddTransient<IPageSolutionMappingService, PageSolutionMappingService>();
        services.AddTransient<IReplicationJobService, ReplicationJobService>();
        services.AddTransient<IFormHistoryService, FormHistoryService>();
        services.AddTransient<IRpForVmMonitorStatusService, RpForVmMonitorStatusService>();
        services.AddTransient<IUserInfoService, UserInfoService>();
        services.AddTransient<IServerLogService, ServerLogService>();
        services.AddTransient<IRpForVmCGMonitorLogsService, RpForVmCGMonitorLogsServices>();
        services.AddTransient<IRpForVmCGMonitorStatusService, RpForVmCGMonitorStatusService>();
        services.AddTransient<IFastCopyMonitorService, FastCopyMonitorService>();

        #endregion

        return services;
    }
}
