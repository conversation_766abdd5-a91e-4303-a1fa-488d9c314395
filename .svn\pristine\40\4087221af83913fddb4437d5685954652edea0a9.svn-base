﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;

namespace ContinuityPatrol.Persistence.Repositories;

public class InfraOperationalStatusRepository : BaseRepository<InfraOperationalStatus>, IInfraOperationalStatusRepository
{
    private readonly ApplicationDbContext _dbContext;

    public InfraOperationalStatusRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
        _dbContext = dbContext;
    }


    public async Task<InfraOperationalStatus> GetInfraOperationalStatusByInfraId(string infraId)
    {
        return await _dbContext.InfraOperationalStatus
            .AsNoTracking()
            .Where(x => x.InfraObjectId.Equals(infraId))
            .FirstOrDefaultAsync();
    }
}