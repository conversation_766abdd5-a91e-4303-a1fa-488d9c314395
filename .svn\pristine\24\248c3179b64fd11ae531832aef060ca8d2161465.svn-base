﻿namespace ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetDetail;

public class GetLicenseInfoDetailQueryHandler : IRequestHandler<GetLicenseInfoDetailQuery, List<LicenseInfoDetailVm>>
{
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly IMapper _mapper;

    public GetLicenseInfoDetailQueryHandler(ILicenseInfoRepository licenseInfoRepository, IMapper mapper)
    {
        _licenseInfoRepository = licenseInfoRepository;
        _mapper = mapper;
    }

    public async Task<List<LicenseInfoDetailVm>> Handle(GetLicenseInfoDetailQuery request,
        CancellationToken cancellationToken)
    {
        var licenseInfo = await _licenseInfoRepository.GetLicenseInfoDetailByLicenseId(request.LicenseId);

        var licenseInfoDtl = _mapper.Map<List<LicenseInfoDetailVm>>(licenseInfo);

        return licenseInfoDtl.Count == 0
            ? throw new NotFoundException(nameof(Domain.Entities.LicenseInfo), request.LicenseId)
            : licenseInfoDtl;
    }
}