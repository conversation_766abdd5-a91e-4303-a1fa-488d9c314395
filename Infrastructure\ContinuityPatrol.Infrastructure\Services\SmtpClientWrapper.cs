﻿using ContinuityPatrol.Infrastructure.Contract;

namespace ContinuityPatrol.Infrastructure.Services;

public class SmtpClientWrapper : ISmtpClientWrapper
{
    private readonly SmtpClient _smtpClient;

    public SmtpClientWrapper(SmtpClient client)
    {
        _smtpClient = client;
    }

    //public SmtpClientWrapper(string host, int port)
    //{
    //    _smtpClient = new SmtpClient(host, port);
    //}

    public NetworkCredential Credentials
    {
        set => _smtpClient.Credentials = value;
    }

    public bool EnableSsl
    {
        set => _smtpClient.EnableSsl = value;
    }

    public bool UseDefaultCredentials
    {
        set => _smtpClient.UseDefaultCredentials = value;
    }


    public Task SendMailAsync(MailMessage message)
    {
        return _smtpClient.SendMailAsync(message);
    }

    public void Dispose()
    {
        _smtpClient.Dispose();
    }
}