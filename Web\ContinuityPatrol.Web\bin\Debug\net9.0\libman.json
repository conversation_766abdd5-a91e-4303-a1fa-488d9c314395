{"version": "1.0", "defaultProvider": "cdnjs", "libraries": [{"library": "jquery-validate@1.21.0", "destination": "wwwroot/lib/jquery-validation", "files": ["jquery.validate.min.js", "additional-methods.min.js"]}, {"library": "jquery@3.7.1", "destination": "wwwroot/lib/jquery/", "files": ["jquery.min.js", "jquery.min.map"]}, {"provider": "unpkg", "library": "bootstrap@5.3.3", "destination": "wwwroot/lib/bootstrap/"}, {"library": "jquery-validation-unobtrusive@4.0.0", "destination": "wwwroot/lib/jquery-validation-unobtrusive/"}, {"library": "j<PERSON><PERSON>i@1.14.0", "destination": "wwwroot/lib/jqueryui/", "files": ["jquery-ui.min.js"]}, {"provider": "unpkg", "library": "aspnet-client-validation@0.11.0", "destination": "wwwroot/lib/aspnet-client-validation/", "files": ["dist/aspnet-validation.css", "dist/aspnet-validation.js", "dist/aspnet-validation.min.js", "dist/aspnet-validation.min.js.map"]}, {"library": "signalr.js@2.4.3", "destination": "wwwroot/lib/signalr.js/"}, {"library": "bootstrap-select@1.13.18", "destination": "wwwroot/lib/bootstrap-select/", "files": ["js/bootstrap-select.min.js", "css/bootstrap-select.min.css"]}, {"library": "selectize.js@0.15.2", "destination": "wwwroot/lib/selectize.js/", "files": ["js/selectize.min.js", "css/selectize.bootstrap5.min.css", "css/selectize.default.min.css", "css/selectize.min.css", "css/selectize.default.css", "css/selectize.bootstrap5.css", "css/selectize.css", "js/selectize.js"]}, {"provider": "unpkg", "library": "diff2html@3.4.48", "destination": "wwwroot/lib/diff2html/", "files": ["bundles/css/diff2html.min.css", "bundles/js/diff2html-ui-base.min.js", "bundles/js/diff2html-ui-slim.min.js", "bundles/js/diff2html-ui.min.js", "bundles/js/diff2html.min.js"]}, {"provider": "unpkg", "library": "typed.js@2.1.0", "destination": "wwwroot/lib/types.js/", "files": ["dist/typed.cjs", "dist/typed.cjs.map", "dist/typed.module.js", "dist/typed.module.js.map", "dist/typed.umd.js", "dist/typed.umd.js.map"]}, {"provider": "unpkg", "library": "dompurify@3.1.6", "destination": "wwwroot/lib/dompurify/", "files": ["dist/purify.cjs.js", "dist/purify.cjs.js.map", "dist/purify.es.mjs", "dist/purify.es.mjs.map", "dist/purify.js", "dist/purify.js.map", "dist/purify.min.js", "dist/purify.min.js.map"]}]}