﻿using ContinuityPatrol.Domain.ViewModels.DRReadyStatusModel;

namespace ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetList;

public class GetDRReadyStatusListQueryHandler : IRequestHandler<GetDRReadyStatusListQuery, List<DRReadyStatusListVm>>
{
    private readonly IDrReadyStatusRepository _dRReadyStatusRepository;
    private readonly IMapper _mapper;

    public GetDRReadyStatusListQueryHandler(IMapper mapper, IDrReadyStatusRepository dRReadyStatusRepository)
    {
        _mapper = mapper;
        _dRReadyStatusRepository = dRReadyStatusRepository;
    }

    public async Task<List<DRReadyStatusListVm>> Handle(GetDRReadyStatusListQuery request,
        CancellationToken cancellationToken)
    {
        var dRReadyStatus = (await _dRReadyStatusRepository.ListAllAsync()).ToList();

        return dRReadyStatus.Count <= 0
            ? new List<DRReadyStatusListVm>()
            : _mapper.Map<List<DRReadyStatusListVm>>(dRReadyStatus);
    }
}