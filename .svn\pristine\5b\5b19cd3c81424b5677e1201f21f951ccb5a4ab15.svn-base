﻿using ContinuityPatrol.Application.Features.SVCGMMonitoringStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.SVCGMMonitorStatusModel;

namespace ContinuityPatrol.Application.UnitTests.Features.SVCGMMonitoringStatus.Queries
{
    public class SVCGMMonitorStatusPaginatedListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISVCGMMonitorStatusRepository> _mockSVCGMMonitorStatusRepository;
        private readonly SVCGMMonitorStatusPaginatedListQueryHandler _handler;

        public SVCGMMonitorStatusPaginatedListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSVCGMMonitorStatusRepository = new Mock<ISVCGMMonitorStatusRepository>();
            _handler = new SVCGMMonitorStatusPaginatedListQueryHandler(_mockSVCGMMonitorStatusRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedResultWithMappedData_WhenDataExists()
        {
            var mockEntities = new List<SVCGMMonitorStatus>
            {
                new SVCGMMonitorStatus { ReferenceId = "123", Type = "Active" },
                new SVCGMMonitorStatus { ReferenceId = "456", Type = "Inactive" }
            };

            var mockViewModels = mockEntities
                .Select(e => new SVCGMMonitorStatusListVm { Id = e.ReferenceId, Type = e.Type })
                .ToList();

            _mockMapper.Setup(mapper => mapper.Map<SVCGMMonitorStatusListVm>(It.IsAny<SVCGMMonitorStatus>()))
                .Returns((SVCGMMonitorStatus source) =>
                    new SVCGMMonitorStatusListVm { Id = source.ReferenceId, Type = source.Type });

            var query = new SVCGMMonitorStatusPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = "Active"
            };

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal("123", result.Data[0].Id);
            Assert.Equal("Active", result.Data[0].Type);

            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyPaginatedResult_WhenNoDataExists()
        {
            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.GetPaginatedQuery())
                .ToString();

            var query = new SVCGMMonitorStatusPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = string.Empty
            };

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);

            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<SVCGMMonitorStatusListVm>(It.IsAny<SVCGMMonitorStatus>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryPaginatedListAllAsyncOnce()
        {
            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.GetPaginatedQuery())
                .Returns(Enumerable.Empty<SVCGMMonitorStatus>().AsQueryable());

            var query = new SVCGMMonitorStatusPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 5,
                SearchString = "Test"
            };

            await _handler.Handle(query, CancellationToken.None);

            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }
    }
}
