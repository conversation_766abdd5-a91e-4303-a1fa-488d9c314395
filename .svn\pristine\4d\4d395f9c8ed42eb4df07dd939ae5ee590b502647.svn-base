﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class LicenseInfoRepositoryMocks
{
    public static Mock<ILicenseInfoRepository> CreateLicenseInfoRepository(List<LicenseInfo> licenseInfos)
    {
        var mockLicenseInfoRepository = new Mock<ILicenseInfoRepository>();

        mockLicenseInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(licenseInfos);

        mockLicenseInfoRepository.Setup(repo => repo.AddAsync(It.IsAny<LicenseInfo>())).ReturnsAsync(
            (LicenseInfo licenseInfo) =>
            {
                licenseInfo.Id = new Fixture().Create<int>();

                licenseInfo.ReferenceId = new Fixture().Create<Guid>().ToString();

                licenseInfos.Add(licenseInfo);

                return licenseInfo;
            });

        return mockLicenseInfoRepository;
    }

    public static Mock<ILicenseInfoRepository> UpdateLicenseInfoRepository(List<LicenseInfo> licenseInfos)
    {
        var mockLicenseInfoRepository = new Mock<ILicenseInfoRepository>();

        mockLicenseInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(licenseInfos);

        mockLicenseInfoRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => licenseInfos.SingleOrDefault(x => x.ReferenceId == i));

        mockLicenseInfoRepository.Setup(repo => repo.GetByEntityId(It.IsAny<string>())).ReturnsAsync((string i) => licenseInfos.SingleOrDefault(x => x.IsActive && x.EntityId == i));

        mockLicenseInfoRepository.Setup(repo => repo.UpdateAsync(It.IsAny<LicenseInfo>())).ReturnsAsync((LicenseInfo licenseInfo) =>
        {
            var index = licenseInfos.FindIndex(item => item.Id == licenseInfo.Id);

            licenseInfos[index] = licenseInfo;

            return licenseInfo;
        });

        return mockLicenseInfoRepository;
    }

    public static Mock<ILicenseInfoRepository> DeleteLicenseInfoRepository(List<LicenseInfo> licenseInfos)
    {
        var mockLicenseInfoRepository = new Mock<ILicenseInfoRepository>();

        mockLicenseInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(licenseInfos);

        mockLicenseInfoRepository.Setup(repo => repo.GetByEntityId(It.IsAny<string>())).ReturnsAsync((string i) => licenseInfos.SingleOrDefault(x => x.ReferenceId == i));

        mockLicenseInfoRepository.Setup(repo => repo.UpdateAsync(It.IsAny<LicenseInfo>())).ReturnsAsync((LicenseInfo licenseInfo) =>
        {
            var index = licenseInfos.FindIndex(item => item.Id == licenseInfo.Id);

            licenseInfo.IsActive = false;

            licenseInfos[index] = licenseInfo;

            return licenseInfo;
        });

        return mockLicenseInfoRepository;
    }

    public static Mock<ILicenseInfoRepository> GetLicenseInfoRepository(List<LicenseInfo> licenseInfos)
    {
        var licenseInfoRepository = new Mock<ILicenseInfoRepository>();

        licenseInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(licenseInfos);

        licenseInfoRepository.Setup(repo => repo.GetLicenseInfoDetailByLicenseId(It.IsAny<string>())).ReturnsAsync((string i) => licenseInfos.Where(x => x.LicenseId == i).ToList());

        return licenseInfoRepository;
    }

    public static Mock<ILicenseInfoRepository> GetLicenseInfoEmptyRepository()
    {
        var licenseInfoRepository = new Mock<ILicenseInfoRepository>();

        licenseInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<LicenseInfo>());

        licenseInfoRepository.Setup(repo => repo.GetLicenseByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(new List<LicenseInfo>().ToList());

        //licenseInfoRepository.Setup(repo => repo.GetLicenseByBusinessServiceId(It.IsAny<string>())).ReturnsAsync((string i) => licenseInfos.Where(x => x.BusinessServiceId == i).ToList());

        return licenseInfoRepository;
    }

    public static Mock<ILicenseInfoRepository> GetPaginatedLicenseInfoRepository(List<LicenseInfo> licenseInfos)
    {
        var licenseInfoRepository = new Mock<ILicenseInfoRepository>();

        var queryableLicenseInfo = licenseInfos.AsEnumerable().BuildMock();

        licenseInfoRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableLicenseInfo);
        
        return licenseInfoRepository;
    }

    //public static Mock<ILicenseInfoRepository> GetLicenseInfoTypeRepository(List<LicenseInfo> licenseInfos)
    //{
    //    var licenseInfoRepository = new Mock<ILicenseInfoRepository>();

    //    licenseInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(licenseInfos);

    //    licenseInfoRepository.Setup(repo => repo.GetLicenseInfoByType(It.IsAny<string>())).ReturnsAsync(licenseInfos);

    //    licenseInfoRepository.Setup(repo => repo.GetLicenseInfoByType(It.IsAny<string>())).ReturnsAsync((string i) => licenseInfos.Where(x => x.Type == i).ToList());

    //    return licenseInfoRepository;
    //}

    public static Mock<ILicenseInfoRepository> GetLicenseInfoByBusinessServiceIdRepository(List<LicenseInfo> licenseInfos)
    {
        var licenseInfoRepository = new Mock<ILicenseInfoRepository>();

        licenseInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(licenseInfos);

        licenseInfoRepository.Setup(repo => repo.GetLicenseByBusinessServiceId(It.IsAny<string>())).ReturnsAsync((string i) => licenseInfos.Where(x => x.BusinessServiceId == i).ToList());

        return licenseInfoRepository;
    }

    public static Mock<ILicenseInfoRepository> GetAvailableCountRepository(List<LicenseInfo> licenseInfos)
    {
        var licenseInfoRepository = new Mock<ILicenseInfoRepository>();

        licenseInfoRepository.Setup(repo => repo.GetAvailableCountByLicenseId(It.IsAny<string>(), It.IsAny<string>())).Returns((string i, string j) => licenseInfos.Count(x => x.LicenseId == i && x.Type == j));

        return licenseInfoRepository;
    }

    public static Mock<ILicenseManagerRepository> GetLicenseManagerRepository(List<LicenseManager> licenseManagers)
    {
        var licenseManagersRepository = new Mock<ILicenseManagerRepository>();

        licenseManagersRepository.Setup(repo => repo.GetLicenseDetailByIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => licenseManagers.SingleOrDefault(x => x.ReferenceId == i));

        return licenseManagersRepository;
    }



    //Events

    public static Mock<IUserActivityRepository> CreateLicenseInfoEventRepository(List<UserActivity> userActivities)
    {
        var licenseInfoEventRepository = new Mock<IUserActivityRepository>();

        licenseInfoEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.LoginName = new Fixture().Create<string>();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return licenseInfoEventRepository;
    }

    public static Mock<ILicenseInfoRepository> CreateDatabaseLicenseInfoEventRepository(List<LicenseInfo> licenseInfos)
    {
        var mockLicenseInfoRepository = new Mock<ILicenseInfoRepository>();

        mockLicenseInfoRepository.Setup(repo => repo.AddAsync(It.IsAny<LicenseInfo>())).ReturnsAsync(
            (LicenseInfo licenseInfo) =>
            {
                licenseInfo.Id = new Fixture().Create<int>();

                licenseInfo.ReferenceId = new Fixture().Create<Guid>().ToString();

                licenseInfos.Add(licenseInfo);

                return licenseInfo;
            });

        return mockLicenseInfoRepository;
    }

    public static Mock<ILicenseInfoRepository> DeleteDatabaseLicenseInfoEventRepository(List<LicenseInfo> licenseInfos)
    {
        var mockLicenseInfoRepository = new Mock<ILicenseInfoRepository>();

        mockLicenseInfoRepository.Setup(repo => repo.GetByEntityId(It.IsAny<string>())).ReturnsAsync((string i) => licenseInfos.SingleOrDefault(x => x.EntityId == i));

        mockLicenseInfoRepository.Setup(repo => repo.UpdateAsync(It.IsAny<LicenseInfo>())).ReturnsAsync((LicenseInfo licenseInfo) =>
        {
            var index = licenseInfos.FindIndex(item => item.Id == licenseInfo.Id);

            licenseInfo.IsActive = false;

            return licenseInfo;
        });

        return mockLicenseInfoRepository;
    }

    public static Mock<ILicenseInfoRepository> UpdateDatabaseLicenseInfoEventRepository(List<LicenseInfo> licenseInfos)
    {
        var mockLicenseInfoRepository = new Mock<ILicenseInfoRepository>();

        mockLicenseInfoRepository.Setup(repo => repo.GetByEntityId(It.IsAny<string>())).ReturnsAsync((string i) => licenseInfos.FirstOrDefault(x => x.EntityId == i));

        mockLicenseInfoRepository.Setup(repo => repo.UpdateAsync(It.IsAny<LicenseInfo>())).ReturnsAsync((LicenseInfo licenseInfo) =>
        {
            var index = licenseInfos.FindIndex(item => item.Id == licenseInfo.Id);

            return licenseInfo;
        });

        return mockLicenseInfoRepository;
    }
}