﻿using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.CommonBaseLicenseUpdate;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetByPoNumber;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetChildLicenseByParentId;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetDecommissionByPoNumber;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseByCompanyId;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseByPONumber;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;

namespace ContinuityPatrol.Application.Mappings;

public class LicenseManagerProfile : Profile
{
    public LicenseManagerProfile()
    {
        CreateMap<CreateBaseLicenseCommand, BaseLicenseViewModel>().ReverseMap();
        CreateMap<UpdateBaseLicenseCommand, BaseLicenseViewModel>().ReverseMap();
        CreateMap<CreateDerivedLicenseCommand, DerivedLicenseViewModel>().ReverseMap();

        CreateMap<CreateBaseLicenseCommand, DerivedLicenseViewModel>().ReverseMap();
        CreateMap<UpdateBaseLicenseCommand, DerivedLicenseViewModel>().ReverseMap();
        CreateMap<UpdateDerivedLicenseCommand, DerivedLicenseViewModel>().ReverseMap();
        CreateMap<CommonBaseLicenseUpdateCommand, DerivedLicenseViewModel>().ReverseMap();
        CreateMap<CommonBaseLicenseUpdateCommand, CommonBaseLicenseUpdateCommandViewModel>().ReverseMap();

        CreateMap<LicenseManager, GetLicenseByCompanyIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        //CreateBaseLicense 

        CreateMap<LicenseDto, LicenseManager>()
            .ForMember(dest => dest.PoNumber, opt => opt.MapFrom(src => SecurityHelper.Encrypt(src.PoNumber)))
            .ForMember(dest => dest.HostName, opt => opt.MapFrom(src => SecurityHelper.Encrypt(src.CpHostName)))
            .ForMember(dest => dest.IpAddress, opt => opt.MapFrom(src => SecurityHelper.Encrypt(src.IpAddress)))
            .ForMember(dest => dest.MacAddress, opt => opt.MapFrom(src => SecurityHelper.Encrypt(src.MacAddress)))
            .ForMember(dest => dest.Properties, opt => opt.MapFrom(src => SecurityHelper.Encrypt(src.LicenseCount)))
            .ForMember(dest => dest.ParentId, opt => opt.MapFrom(src => string.Empty))
            .ForMember(dest => dest.Validity, opt => opt.MapFrom(src => SecurityHelper.Encrypt(src.LicenseType)))
            .ForMember(dest => dest.IsState, opt => opt.MapFrom(src => true))
            .ForMember(dest => dest.ParentPoNumber, opt => opt.MapFrom(src => SecurityHelper.Encrypt("NA")))
            .ForMember(dest => dest.IsAmc, opt => opt.MapFrom(src => IsAmc(src.SupportPlan)))
            .ForMember(dest => dest.AmcPlan, opt => opt.MapFrom(src => SecurityHelper.Encrypt(AmcPlan(src.SupportPlan))))
            .ForMember(dest => dest.AmcStartDate,
                opt => opt.MapFrom(src => SecurityHelper.Encrypt(AmcStartDate(src.SupportPlan))))
            .ForMember(dest => dest.AmcEndDate,
                opt => opt.MapFrom(src => SecurityHelper.Encrypt(AmcEndDate(src.SupportPlan))));


        CreateMap<LicenseManager, LicenseHistory>()
            .ForMember(dest => dest.LicenseId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.PONumber, opt => opt.MapFrom(src => src.PoNumber))
            .ForMember(dest => dest.CPHostName, opt => opt.MapFrom(src => src.HostName))
            .ForMember(dest => dest.IPAddress, opt => opt.MapFrom(src => src.IpAddress))
            .ForMember(dest => dest.MACAddress, opt => opt.MapFrom(src => src.MacAddress))
            .ForMember(dest => dest.Properties, opt => opt.MapFrom(src => src.Properties))
            .ForMember(dest => dest.ParentId, opt => opt.MapFrom(src => string.Empty))
            .ForMember(dest => dest.Validity, opt => opt.MapFrom(src => src.Validity))
            .ForMember(dest => dest.IsState, opt => opt.MapFrom(src => src.IsState))
            .ForMember(dest => dest.IsParent, opt => opt.MapFrom(src => src.IsParent))
            .ForMember(dest => dest.LicenseKey, opt => opt.MapFrom(src => src.LicenseKey))
            .ForMember(dest => dest.ParentPONumber, opt => opt.MapFrom(src => src.ParentPoNumber));


        ////Update BaseLicense

        //CreateMap<LicenseDto, UpdateBaseLicenseCommand>()
        //    .ForMember(dest => dest.PoNumber, opt => opt.MapFrom(src => SecurityHelper.Encrypt(src.PoNumber)))
        //    .ForMember(dest => dest.HostName, opt => opt.MapFrom(src => SecurityHelper.Encrypt(src.CpHostName)))
        //    .ForMember(dest => dest.IpAddress, opt => opt.MapFrom(src => SecurityHelper.Encrypt(src.IpAddress)))
        //    .ForMember(dest => dest.MacAddress, opt => opt.MapFrom(src => SecurityHelper.Encrypt(src.MacAddress)))
        //    .ForMember(dest => dest.Validity, opt => opt.MapFrom(src => SecurityHelper.Encrypt(src.LicenseType)))
        //    .ForMember(dest => dest.ParentId, opt => opt.MapFrom(src => string.Empty))
        //    .ForMember(dest => dest.ParentPoNumber, opt => opt.MapFrom(src => SecurityHelper.Encrypt("NA")));


        CreateMap<LicenseManager, CreateBaseLicenseCommand>().ReverseMap();
        CreateMap<UpdateBaseLicenseCommand, LicenseManager>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<CommonBaseLicenseUpdateCommand, LicenseManager>().ForMember(x => x.Id, y => y.Ignore());
        //  CreateMap<LicenseManager, GenerateLicenseManagerCommand>().ReverseMap();
        CreateMap<LicenseManager, CreateDerivedLicenseCommand>().ReverseMap();
        CreateMap<UpdateDerivedLicenseCommand, LicenseManager>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<LicenseManager, LicenseManagerDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<LicenseManager, LicenseManagerNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<LicenseManager, LicenseManagerByPoNumberVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<LicenseManager, GetLicenseByPONumberVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<LicenseManager, ChildLicenseDetailByParentIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.CreatedDate, opt => opt.MapFrom(src => FormatToCustomString(src.CreatedDate)));
        CreateMap<LicenseManager, LicenseManagerDetailViewVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.CreatedDate, opt => opt.MapFrom(src => FormatToCustomString(src.CreatedDate)))
            .ForMember(dest => dest.LastModifiedDate,
                opt => opt.MapFrom(src => FormatToCustomString(src.LastModifiedDate)));
        CreateMap<LicenseManager, LicenseManagerListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.CreatedDate, opt => opt.MapFrom(src => FormatToCustomString(src.CreatedDate)));
        CreateMap<LicenseManager, LicenseValidatorVm>();


        CreateMap<string[], LicenseDto>()
            .ForMember(dest => dest.PoNumber, opt => opt.MapFrom(src => src.Length > 0 ? src[0] : string.Empty))
            .ForMember(dest => dest.CpHostName, opt => opt.MapFrom(src => src.Length > 1 ? src[1] : string.Empty))
            .ForMember(dest => dest.IpAddress, opt => opt.MapFrom(src => src.Length > 2 ? src[2] : string.Empty))
            .ForMember(dest => dest.MacAddress, opt => opt.MapFrom(src => src.Length > 3 ? src[3] : string.Empty))
            .ForMember(dest => dest.LicenseCount, opt => opt.MapFrom(src => src.Length > 4 ? src[4] : string.Empty))
            .ForMember(dest => dest.LicenseType, opt => opt.MapFrom(src => src.Length > 5 ? src[5] : string.Empty))
            .ForMember(dest => dest.RandomString, opt => opt.MapFrom(src => src.Length > 6 ? src[6] : string.Empty))
            .ForMember(dest => dest.LicenseGeneratorDate,
                opt => opt.MapFrom(src => src.Length > 7 ? src[7] : string.Empty))
            .ForMember(dest => dest.LicenseActionType,
                opt => opt.MapFrom(src => src.Length > 8 ? src[8] : string.Empty))
            .ForMember(dest => dest.LicenseAction, opt => opt.MapFrom(src => src.Length > 9 ? src[9] : string.Empty))
            .ForMember(dest => dest.SupportPlan, opt => opt.MapFrom(src => src.Length > 10 ? src[10] : string.Empty))
            .ForMember(dest => dest.Request, opt => opt.MapFrom(src => src.Length > 11 ? src[11] : string.Empty))
            .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.Length > 12 ? src[12] : string.Empty))
            .ForMember(dest => dest.AuthenticationId,
                opt => opt.MapFrom(src => src.Length > 13 ? src[13] : string.Empty));

        //Decommission

        #region Decommission

        CreateMap<Server, DecommissionDetailVm>()
            .ForMember(dest => dest.ServerId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.ServerName, opt => opt.MapFrom(src => src.Name));


        CreateMap<Database, DecommissionDatabaseDetailVm>()
            .ForMember(dest => dest.DatabaseId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DatabaseName, opt => opt.MapFrom(src => src.Name));

        CreateMap<InfraObject, DecommissionInfraObjectDetailVm>()
            .ForMember(dest => dest.InfraObjectId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.InfraObjectName, opt => opt.MapFrom(src => src.Name));


        CreateMap<WorkflowInfraObject, Workflow>()
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.WorkflowId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.WorkflowName));

        CreateMap<WorkflowInfraObject, DecommissionWorkflowInfraObjectDetailVm>()
            .ForMember(dest => dest.WorkflowInfraObjectId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.WorkflowId, opt => opt.MapFrom(src => src.WorkflowId))
            .ForMember(dest => dest.WorkflowName, opt => opt.MapFrom(src => src.WorkflowName))
            .ForMember(dest => dest.InfraObjectId, opt => opt.MapFrom(src => src.InfraObjectId))
            .ForMember(dest => dest.InfraObjectName, opt => opt.MapFrom(src => src.InfraObjectName))
            .ForMember(dest => dest.IsAttach, opt => opt.MapFrom(src => src.IsAttach));

        CreateMap<Workflow, DecommissionWorkflowDetailVm>()
            .ForMember(dest => dest.WorkflowId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.WorkflowName, opt => opt.MapFrom(src => src.Name));


        CreateMap<Database, DecommissionDatabase>()
            .ForMember(dest => dest.DatabaseId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.DatabaseName, opt => opt.MapFrom(src => src.Name));

        CreateMap<WorkflowProfileInfo, DecommissionWorkflowProfile>()
            .ForMember(dest => dest.ProfileInfoId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.ProfileName, opt => opt.MapFrom(src => src.ProfileName));

        CreateMap<Replication, DecommissionReplication>()
            .ForMember(dest => dest.ReplicationId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.ReplicationName, opt => opt.MapFrom(src => src.Name));

        CreateMap<Replication, DecommissionReplicationDetailVm>()
            .ForMember(dest => dest.ReplicationId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.ReplicationName, opt => opt.MapFrom(src => src.Name));

        CreateMap<DecommissionDetailVm, Server>()
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.ServerId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.ServerName));

        CreateMap<DecommissionWorkflowProfile, WorkflowProfileInfo>()
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.ProfileInfoId))
            .ForMember(dest => dest.ProfileName, opt => opt.MapFrom(src => src.ProfileName));

        CreateMap<DecommissionWorkflowInfraObjectDetailVm, WorkflowInfraObject>()
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.WorkflowInfraObjectId))
            .ForMember(dest => dest.WorkflowId, opt => opt.MapFrom(src => src.WorkflowId))
            .ForMember(dest => dest.WorkflowName, opt => opt.MapFrom(src => src.WorkflowName))
            .ForMember(dest => dest.InfraObjectId, opt => opt.MapFrom(src => src.InfraObjectId))
            .ForMember(dest => dest.InfraObjectName, opt => opt.MapFrom(src => src.InfraObjectName))
            .ForMember(dest => dest.IsAttach, opt => opt.MapFrom(src => src.IsAttach));

        CreateMap<DecommissionWorkflowDetailVm, Workflow>()
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.WorkflowId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.WorkflowName));

        CreateMap<DecommissionReplicationDetailVm, Replication>()
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.ReplicationId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.ReplicationName));

        CreateMap<DecommissionReplication, Replication>()
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.ReplicationId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.ReplicationName));

        CreateMap<DecommissionDatabase, Database>()
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.DatabaseId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.DatabaseName));

        CreateMap<DecommissionDatabaseDetailVm, Database>()
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.DatabaseId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.DatabaseName));

        CreateMap<DecommissionWorkflowDetailVm, Workflow>()
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.WorkflowId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.WorkflowName));

        CreateMap<DecommissionInfraObjectDetailVm, InfraObject>()
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.InfraObjectId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.InfraObjectName));

        #endregion
    }

    private static string FormatToCustomString(DateTime dateTime)
    {
        var cultureInfo = new CultureInfo("en-IN");
        return dateTime.ToString("dd MMMM yyyy", cultureInfo);
    }

    public static bool IsAmc(string src)
    {
        if (src.IsNullOrWhiteSpace()) return false;

        var jObject = JObject.Parse(src);
        return jObject["AMC"]?.ToObject<long>() != 0;
    }

    public static bool IsWarranty(string src)
    {
        if (src.IsNullOrWhiteSpace()) return false;

        var jObject = JObject.Parse(src);
        return jObject.ContainsKey("Warranty") && jObject["Warranty"]?.ToObject<long>() != 0;
    }

    private string AmcPlan(string src)
    {
        if (IsWarranty(src))
        {
            var jObject = JObject.Parse(src);

            var warrantyStartDate = jObject.SelectToken("WarrantyStartDate")?.ToString();

            var formats = new[]
            {
                "yyyy-MM-dd",
                "dd MMMM yyyy"
            };


            var formattedDate = warrantyStartDate.IsNotNullOrWhiteSpace()
                ? DateTime.ParseExact(warrantyStartDate!, formats, null)
                : DateTime.Now;

            var warranty = jObject.SelectToken("Warranty")?.ToString();

            var warrantyEndDate = formattedDate.AddYears(int.Parse(warranty!)).ToString("dd MMMM yyyy");

            jObject["WarrantyEndDate"] = warrantyEndDate;

            return jObject.ToString();
        }

        return src;
    }


    private string AmcStartDate(string src)
    {
        if (IsAmc(src))
        {
            var jObject = JObject.Parse(src);

            var startDate = jObject.SelectToken("AMCStartDate")?.ToString();

          var formattedDate = startDate.IsNotNullOrWhiteSpace()
              ? DateTime.ParseExact(startDate!, "yyyy-MM-dd", null).ToString("dd MMMM yyyy")
                  : "NA";

            return formattedDate;
        }

        return "NA";
    }

    private string AmcEndDate(string expireTime)
    {
        if (IsAmc(expireTime))
        {
            var jObject = JObject.Parse(expireTime);

            var startDate = jObject.SelectToken("AMCStartDate")?.ToString();

            var parseStartDate = DateTime.ParseExact(startDate!, "yyyy-MM-dd", null);

            var endDate = jObject["AMC"]?.ToString();

            var expiryDate = parseStartDate.AddYears(int.Parse(endDate!)).ToString("dd MMMM yyyy");

            return expiryDate;
        }

        return "NA";
    }
}