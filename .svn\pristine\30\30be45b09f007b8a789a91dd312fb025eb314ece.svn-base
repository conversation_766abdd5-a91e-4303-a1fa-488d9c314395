﻿namespace ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetNameUnique;

public class GetLoadBalancerNameUniqueQueryHandler : IRequestHandler<GetLoadBalancerNameUniqueQuery, bool>
{
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;

    public GetLoadBalancerNameUniqueQueryHandler(ILoadBalancerRepository nodeConfigurationRepository)
    {
        _nodeConfigurationRepository = nodeConfigurationRepository;
    }

    public async Task<bool> Handle(GetLoadBalancerNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _nodeConfigurationRepository.IsNodeConfigurationNameExist(request.Name, request.Id);
    }
}