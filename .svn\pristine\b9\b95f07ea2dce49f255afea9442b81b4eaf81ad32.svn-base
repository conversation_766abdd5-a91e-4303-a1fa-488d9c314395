﻿using ContinuityPatrol.Domain.ViewModels.DriftManagementMonitorStatusModel;

namespace ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetResourceStatus;

public class
    DriftDashboardResourceStatusQueryHandler : IRequestHandler<DriftDashboardResourceStatusQuery,
        DriftDashboardResourceStatusVm>
{
    private readonly IDatabaseViewRepository _databaseViewRepository;
    private readonly IDriftResourceSummaryRepository _driftResourceSummaryRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IMapper _mapper;
    private readonly IServerViewRepository _serverViewRepository;

    public DriftDashboardResourceStatusQueryHandler(IServerViewRepository serverViewRepository,
        IDatabaseViewRepository databaseViewRepository,
        IInfraObjectRepository infraObjectRepository, IDriftResourceSummaryRepository driftResourceSummaryRepository,
        IMapper mapper)
    {
        _serverViewRepository = serverViewRepository;
        _databaseViewRepository = databaseViewRepository;
        _infraObjectRepository = infraObjectRepository;
        _driftResourceSummaryRepository = driftResourceSummaryRepository;
        _mapper = mapper;
    }

    public async Task<DriftDashboardResourceStatusVm> Handle(DriftDashboardResourceStatusQuery request,
        CancellationToken cancellationToken)
    {
        #region Concept
        //total resource = Total server + Total data base;
       // DriftENABLED Count = in infra table  is drift enabled infraObject serverproperty  and db-property - have server and Database component that component Count addd
 //Conflict and non ConflicCount from driftResourceSummaryTable
                               #endregion

        var serverList = await _serverViewRepository.ListAllAsync();

        var databaseList = await _databaseViewRepository.ListAllAsync();

        var infraObjectList = await _infraObjectRepository.GetDriftEnabledList();

        var driftDashboardResourceStatusVm = new DriftDashboardResourceStatusVm();

        driftDashboardResourceStatusVm.TotalResourceCount = serverList.Count + databaseList.Count;

        var isDriftServer = infraObjectList.Where(x => x.ServerProperties != null && serverList.Any(server => x.ServerProperties.Contains(server.ReferenceId)))
            .SelectMany(x => serverList
            .Where(server => x.ServerProperties.Contains(server.ReferenceId))
            .Select(ser => new
            {
                InfraObjectId = x.ReferenceId,
                Type = "server",
                EntityId = ser.ReferenceId 
            })).ToList();

        var isDriftDatabase = infraObjectList.Where(x => x.DatabaseProperties != null && databaseList.Any(db => x.DatabaseProperties.Contains(db.ReferenceId)))
          .SelectMany(x => databaseList
          .Where(db => x.DatabaseProperties.Contains(db.ReferenceId))
          .Select(database => new
          {
              InfraObjectId = x.ReferenceId,
              Type = "database",
              EntityId = database.ReferenceId
          })).ToList();
        
        var combinedList = isDriftServer.Concat(isDriftDatabase).DistinctBy(x => (x.Type, x.EntityId)).ToList();

        driftDashboardResourceStatusVm.DriftEnabledCount = combinedList.Count;

        var distincOnInfraBased = combinedList.DistinctBy(x => (x.InfraObjectId,x.Type)).ToList();

        foreach (var infra in distincOnInfraBased)
        {
            var driftResourceSummaryDtl =
                    await _driftResourceSummaryRepository.GetByInfraObjectIdAndEntityName(infra.InfraObjectId,infra.Type);

            driftDashboardResourceStatusVm.ConflictedCount += driftResourceSummaryDtl.Sum(x => x.ConflictCount);

            driftDashboardResourceStatusVm.NonConflictedCount += driftResourceSummaryDtl.Sum(x => x.NonConflictCount);
        }
        var driftResourceList=await _driftResourceSummaryRepository.ListAllAsync();

        driftDashboardResourceStatusVm.DriftResourceSummaryVm = _mapper.Map<List<DriftResourceSummaryVm>>(driftResourceList);

        driftDashboardResourceStatusVm.DriftResourceSummaryVm.ForEach(x=>x.InfraObjectName=infraObjectList.FirstOrDefault(y=>y.ReferenceId==x.InfraObjectId)?.Name);

        return driftDashboardResourceStatusVm;

        //var driftEnabledCount = 0;

        //var nonConflictCount = 0;

        //var conflictCount = 0;

        //var driftDashboardResourceStatusVm = new DriftDashboardResourceStatusVm();

        //foreach (var server in serverList)
        //{

        //    if (infraObject is not null && infraObject.IsDrift)
        //    {
        //        driftEnabledCount++;

        //        var driftResourceSummaryDtl =
        //            await _driftResourceSummaryRepository.GetByInfraObjectIdAndEntityName(infraObject.ReferenceId,
        //                "server");


        //        nonConflictCount += driftResourceSummaryDtl.Sum(x => x.NonConflictCount);

        //        conflictCount += driftResourceSummaryDtl.Sum(x => x.ConflictCount);

        //        var driftResourceSummary = _mapper.Map<List<DriftResourceSummaryVm>>(driftResourceSummaryDtl);

        //        if (driftResourceSummary.Count > 0)
        //        {
        //            driftResourceSummary.ForEach(x => x.InfraObjectName = infraObject.Name);

        //            driftDashboardResourceStatusVm.DriftResourceSummaryVm.AddRangeAsync(driftResourceSummary);
        //        }
        //    }
        //}

        //foreach (var database in databaseList)
        //{
        //    var infraObjectDtl = infraObjectList.FirstOrDefault(x =>
        //        x.DatabaseProperties != null && x.DatabaseProperties.Contains(database.ReferenceId));

        //    if (infraObjectDtl is not null && infraObjectDtl.IsDrift)
        //    {
        //        driftEnabledCount++;

        //        var driftResourceSummaryDtl =
        //            await _driftResourceSummaryRepository.GetByInfraObjectIdAndEntityName(infraObjectDtl.ReferenceId,
        //                "database");

        //        nonConflictCount += driftResourceSummaryDtl.Sum(x => x.NonConflictCount);
        //        conflictCount += driftResourceSummaryDtl.Sum(x => x.ConflictCount);

        //        var driftResourceSummary = _mapper.Map<List<DriftResourceSummaryVm>>(driftResourceSummaryDtl);


        //        if (driftResourceSummary.Count > 0)
        //        {
        //            driftResourceSummary.ForEach(x => x.InfraObjectName = infraObjectDtl.Name);

        //            driftDashboardResourceStatusVm.DriftResourceSummaryVm.AddRangeAsync(driftResourceSummary);
        //        }
        //    }
        //}

        //driftDashboardResourceStatusVm.TotalResourceCount = totalResourceCount;
        //driftDashboardResourceStatusVm.DriftEnabledCount = driftEnabledCount;
        //driftDashboardResourceStatusVm.NonConflictedCount = nonConflictCount;
        //driftDashboardResourceStatusVm.ConflictedCount = conflictCount;


        // return driftDashboardResourceStatusVm;
    }
}