﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class AlertReceiverRepository : BaseRepository<AlertReceiver>, IAlertReceiverRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public AlertReceiverRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<PaginatedResult<AlertReceiver>>PaginatedListAllAsync(int pageNumber, int pageSize, Specification<AlertReceiver> productFilterSpec, string sortColumn, string sortOrder)
    {
        var baseQuery = Entities.Specify(productFilterSpec).DescOrderById();
        if (!IsParent)
        {
            baseQuery = baseQuery.Where(x => x.ReferenceId == _loggedInUserService.CompanyId);
        }
        return await FilterRequiredField(baseQuery).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public override IQueryable<AlertReceiver> GetPaginatedQuery()
    {
        return IsParent
            ? base.GetPaginatedQuery()
            : Entities.Where(x => x.IsActive && x.ReferenceId == _loggedInUserService.CompanyId).DescOrderById();
    }

    public async Task<bool> IsAlertReceiverNameUnique(string name)
    {
       return await _dbContext.AlertReceivers.AnyAsync(x => x.Name == name);
    }

    public async Task<bool> IsAlertReceiverNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
        {
            return await _dbContext.AlertReceivers.AnyAsync(e => e.Name == name);
        }

        return await _dbContext.AlertReceivers.AnyAsync(e => e.Name == name && e.ReferenceId != id);
    }

    private static IQueryable<AlertReceiver> FilterRequiredField(IQueryable<AlertReceiver> query)
    {
        return query.Select(u => new AlertReceiver
        {
            Id = u.Id,
            ReferenceId = u.ReferenceId,
            CompanyId = u.CompanyId,
            EmailAddress = u.EmailAddress,
            MobileNumber = u.MobileNumber,
            Name = u.Name,
            Properties = u.Properties,
            IsMail = u.IsMail,
            IsActiveUser = u.IsActiveUser,
            IsSendReport = u.IsSendReport
        });
    }
}