﻿using ContinuityPatrol.Application.Features.Company.Commands.CreateDefaultCompany;
using ContinuityPatrol.Application.Features.User.Commands.CreateDefaultUser;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;

namespace ContinuityPatrol.Web.Controllers;

public class BasicController : Controller
{
    private readonly IDataProvider _dataProvider;
    private readonly ILogger<BasicController> _logger;

    public BasicController(IDataProvider dataProvider, ILogger<BasicController> logger)
    {
        _dataProvider = dataProvider;
        _logger = logger;
    }

    [AllowAnonymous]
    //[ValidateAntiForgeryToken]
    public async Task<IActionResult> Configuration(CreateDefaultCompanyCommand defaultCompany, CreateDefaultUserCommand defaultUser)
    {
        _logger.LogDebug("Configuration method called");

        var companyList=await _dataProvider.Company.GetCompanyNamesOnLogin();

        if (companyList.Count > 0)
        {
            _logger.LogWarning("Basic company already configured.");
            
            return RedirectToAction("Logout", "Account");
        }
        if (string.IsNullOrWhiteSpace(defaultCompany.Name) || string.IsNullOrWhiteSpace(defaultUser.LoginName))
        {
            _logger.LogWarning("Default company name or user login name is not provided");
            return View();
        }

        try
        {
            var company = await _dataProvider.Company.CreateDefaultCompany(defaultCompany);

            if (company.Success)
            {
                _logger.LogInformation("Default company created successfully");
                return await UserInfo(defaultUser);
            }

            _logger.LogWarning($"Failed to create default company: {company.Message}");

            TempData.Set(new NotificationMessage(NotificationType.Error, company.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while creating the default company");
            TempData.Set(new NotificationMessage(NotificationType.Error, ex.GetMessage()));
        }

        return View();
    }

    [AllowAnonymous]
    //[ValidateAntiForgeryToken]
    public async Task<IActionResult> UserInfo(CreateDefaultUserCommand defaultUser)
    {
        var users=await _dataProvider.User.HasUserAsync();

        if (users)
        {
            _logger.LogWarning("Basic user already configured.");

           
            return RedirectToAction("Logout", "Account");
        }
        if (string.IsNullOrWhiteSpace(defaultUser.LoginName))
        {
            _logger.LogWarning("Default user login name is not provided");
            return View();
        }

        try
        {
            var parentCompany = await GetCompanies();

            if (parentCompany == null || parentCompany.Count == 0)
            {
                _logger.LogWarning("No companies found");
                TempData.Set(new NotificationMessage(NotificationType.Error, "No companies found"));
                return View();
            }

            defaultUser.CompanyId = parentCompany[0].Id;
            defaultUser.CompanyName = parentCompany[0].DisplayName;

            var user = await _dataProvider.User.CreateDefaultUser(defaultUser);

            if (user.Success)
            {
                _logger.LogInformation("Default user created successfully");
                TempData.Set(new NotificationMessage(NotificationType.Success, user.Message));
                return RedirectToAction("Login", "Account");
            }

            _logger.LogWarning($"Failed to create default user: {user.Message}");

            TempData.Set(new NotificationMessage(NotificationType.Error, user.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while creating the default user");
            TempData.Set(new NotificationMessage(NotificationType.Error, ex.GetMessage()));
        }

        return View();
    }

    [HttpGet]
    [AllowAnonymous]
    public async Task<List<CompanyNameVm>> GetCompanies()
    {
        _logger.LogDebug("GetCompanies method called");

        try
        {
            var companyNames = await _dataProvider.Company.GetCompanyNamesOnLogin();
            _logger.LogInformation($"Retrieved {companyNames.Count} companies");
            return companyNames;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while retrieving company names");
            return null;
        }
    }
}
