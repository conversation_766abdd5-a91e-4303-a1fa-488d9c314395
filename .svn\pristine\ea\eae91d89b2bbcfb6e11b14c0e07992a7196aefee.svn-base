using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DashboardViewLogFixture : IDisposable
{
    public List<DashboardViewLog> DashboardViewLogPaginationList { get; set; }
    public List<DashboardViewLog> DashboardViewLogList { get; set; }
    public DashboardViewLog DashboardViewLogDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
    public const string BusinessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
    public const string InfraObjectId = "INFRA_123";
    public const string EntityId = "ENTITY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public DashboardViewLogFixture()
    {
        var fixture = new Fixture();

        DashboardViewLogList = fixture.Create<List<DashboardViewLog>>();

        DashboardViewLogPaginationList = fixture.CreateMany<DashboardViewLog>(20).ToList();

        DashboardViewLogPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DashboardViewLogPaginationList.ForEach(x => x.IsActive = true);
        DashboardViewLogPaginationList.ForEach(x => x.CompanyId = CompanyId);
        DashboardViewLogPaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        DashboardViewLogPaginationList.ForEach(x => x.BusinessFunctionId = BusinessFunctionId);
        DashboardViewLogPaginationList.ForEach(x => x.InfraObjectId = InfraObjectId);
        DashboardViewLogPaginationList.ForEach(x => x.EntityId = EntityId);

        DashboardViewLogList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DashboardViewLogList.ForEach(x => x.IsActive = true);
        DashboardViewLogList.ForEach(x => x.CompanyId = CompanyId);
        DashboardViewLogList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        DashboardViewLogList.ForEach(x => x.BusinessFunctionId = BusinessFunctionId);
        DashboardViewLogList.ForEach(x => x.InfraObjectId = InfraObjectId);
        DashboardViewLogList.ForEach(x => x.EntityId = EntityId);

        DashboardViewLogDto = fixture.Create<DashboardViewLog>();
        DashboardViewLogDto.ReferenceId = Guid.NewGuid().ToString();
        DashboardViewLogDto.IsActive = true;
        DashboardViewLogDto.CompanyId = CompanyId;
        DashboardViewLogDto.BusinessServiceId = BusinessServiceId;
        DashboardViewLogDto.BusinessFunctionId = BusinessFunctionId;
        DashboardViewLogDto.InfraObjectId = InfraObjectId;
        DashboardViewLogDto.EntityId = EntityId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
