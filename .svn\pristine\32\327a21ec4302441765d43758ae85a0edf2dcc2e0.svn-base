﻿using ContinuityPatrol.Application.Features.WorkflowPermission.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowPermission.Commands;

public class DeleteWorkflowPermissionTests : IClassFixture<WorkflowPermissionFixture>
{
    private readonly WorkflowPermissionFixture _workflowPermissionFixture;
    private readonly Mock<IWorkflowPermissionRepository> _mockWorkflowPermissionRepository;
    private readonly DeleteWorkflowPermissionCommandHandler _handler;

    public DeleteWorkflowPermissionTests(WorkflowPermissionFixture workflowPermissionFixture)
    {
        _workflowPermissionFixture = workflowPermissionFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockWorkflowPermissionRepository = WorkflowPermissionRepositoryMocks.DeleteWorkflowPermissionRepository(_workflowPermissionFixture.WorkflowPermissions);

        _handler = new DeleteWorkflowPermissionCommandHandler(_mockWorkflowPermissionRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_WorkflowPermissionDeleted()
    {
        var result = await _handler.Handle(new DeleteWorkflowPermissionCommand { Id = _workflowPermissionFixture.WorkflowPermissions[0].ReferenceId }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteAlertReceiverResponse_When_AlertReceiverDeleted()
    {
        var result = await _handler.Handle(new DeleteWorkflowPermissionCommand { Id = _workflowPermissionFixture.WorkflowPermissions[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteWorkflowPermissionResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_AlertReceiverDeleted()
    {
        await _handler.Handle(new DeleteWorkflowPermissionCommand { Id = _workflowPermissionFixture.WorkflowPermissions[0].ReferenceId }, CancellationToken.None);

        var accessManager = await _mockWorkflowPermissionRepository.Object.GetByReferenceIdAsync(_workflowPermissionFixture.WorkflowPermissions[0].ReferenceId);

        accessManager.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidAlertReceiverId()
    {
        var invalidGuid = Guid.NewGuid().ToString();
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteWorkflowPermissionCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteWorkflowPermissionCommand { Id = _workflowPermissionFixture.WorkflowPermissions[0].ReferenceId }, CancellationToken.None);

        _mockWorkflowPermissionRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockWorkflowPermissionRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.WorkflowPermission>()), Times.Once);
    }
}