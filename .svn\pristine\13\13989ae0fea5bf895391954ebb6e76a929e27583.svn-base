﻿using ContinuityPatrol.Domain.ViewModels.RpoSlaDeviationReportModel;

namespace ContinuityPatrol.Application.Features.Report.Queries.GetRpoSlaDeviationReport;

public class GetRpoSlaDeviationReportByStartTimeAndEndTimeQuery : IRequest<GetRpoSlaDeviationReportVm>
{
    public string BusinessServiceId { get; set; }
    public string InfraObjectId { get; set; }
    public string CreatedDate { get; set; }
    public string LastModifiedDate { get; set; }
}