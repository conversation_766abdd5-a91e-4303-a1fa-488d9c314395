using ContinuityPatrol.Application.Features.CyberComponent.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponent.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetBySiteId;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetInfrastructureSummary;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberComponentModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ICyberComponentService
{
    Task<List<CyberComponentListVm>> GetCyberComponentList();
    Task<BaseResponse> CreateAsync(CreateCyberComponentCommand createCyberComponentCommand);
    Task<BaseResponse> UpdateAsync(UpdateCyberComponentCommand updateCyberComponentCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<CyberComponentDetailVm> GetByReferenceId(string id);
    Task<List<CyberComponentBySiteIdVm>> GetCyberComponentBySiteId(string siteId);
    Task<List<GetInfrastructureSummaryVm>> GetInfrastructureSummary();
    #region NameExist
     Task<bool> IsCyberComponentNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<CyberComponentListVm>> GetPaginatedCyberComponents(GetCyberComponentPaginatedListQuery query);
    #endregion
}
