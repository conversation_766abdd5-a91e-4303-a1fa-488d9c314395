﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface ISingleSignOnRepository : IRepository<SingleSignOn>
{
    Task<bool> IsProfileNameExist(string profileName, string id);

    Task<bool> IsProfileNameUnique(string profileName);

    Task<List<SingleSignOn>> GetSingleSignOnNames();

    Task<List<SingleSignOn>> GetType(string typeId);

    IQueryable<SingleSignOn> GetSingleSignOnByType(string typeId);
    Task<PaginatedResult<SingleSignOn>> GetSingleSignOnByType(string typeId, int pageNumber, int pageSize, Specification<SingleSignOn> productFilterSpec, string sortColumn, string sortOrder);
    Task<List<SingleSignOn>> GetSingleSignOnByTypeIds(List<string> typeIds);
}