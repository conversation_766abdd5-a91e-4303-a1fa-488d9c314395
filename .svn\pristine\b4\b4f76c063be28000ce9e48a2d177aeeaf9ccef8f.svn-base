﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.SmtpConfigurationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.SmtpConfiguration.Queries.GetPaginatedList;

public class GetSmtpConfigurationPaginatedListQueryHandler : IRequestHandler<GetSmtpConfigurationPaginatedListQuery,
    PaginatedResult<SmtpConfigurationListVm>>
{
    private readonly IMapper _mapper;
    private readonly ISmtpConfigurationRepository _smtpConfigurationRepository;

    public GetSmtpConfigurationPaginatedListQueryHandler(IMapper mapper,
        ISmtpConfigurationRepository smtpConfigurationRepository)
    {
        _smtpConfigurationRepository = smtpConfigurationRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<SmtpConfigurationListVm>> Handle(
        GetSmtpConfigurationPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _smtpConfigurationRepository.GetPaginatedQuery();

        var productFilterSpec = new SmtpConfigurationFilterSpecification(request.SearchString);

        var smtpConfigurationList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<SmtpConfigurationListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        smtpConfigurationList.Data.ForEach(x => x.UserName = SecurityHelper.Decrypt(x.UserName));

        return smtpConfigurationList;
    }
}