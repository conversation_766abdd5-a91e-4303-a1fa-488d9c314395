﻿QUnit.module("HACMPCluster - All Functions & Events", hooks => {
    let server;

    hooks.beforeEach(() => {
        // Reset fixture DOM for each test
        $('#qunit-fixture').html(`
            <div id="hacmpConfigCreate" data-create-permission="TRUE"></div>
            <div id="hacmpConfigDelete" data-delete-permission="TRUE"></div>
            <button id="hacmpCreateBtn" class="btn">Create</button>
            <input id="hacmpSearchInp" type="text" placeholder="Search..." />
            <table id="hacmpClusterTable">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Server</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody></tbody>
            </table>
            <div class="pagination-column"></div>
            <span class="dataTables_empty"></span>
            <button id="SaveFunction">Save</button>
            <button id="hacmpConfirmDeleteBtn">Delete</button>
        `);

        // Sinon fake server for AJAX
        server = sinon.createFakeServer();
        server.autoRespond = true;
    });

    hooks.afterEach(() => {
        server.restore();
    });

    QUnit.test("Permission logic disables/enables buttons", assert => {
        // Test create permission
        $('#hacmpConfigCreate').attr('data-create-permission', 'FALSE');
        $('#hacmpCreateBtn').removeClass('btn-disabled').css('pointer-events', '');

        // Simulate permission check
        let createPerm = $("#hacmpConfigCreate").data("create-permission").toString().toLowerCase();
        if (createPerm === 'false') {
            $("#hacmpCreateBtn").addClass('btn-disabled').css('pointer-events', 'none');
        }
        assert.ok($('#hacmpCreateBtn').hasClass('btn-disabled'), "Create button disabled when no permission");
        assert.equal($('#hacmpCreateBtn').css('pointer-events'), 'none', "Pointer events disabled");

        // Test delete permission
        $('#hacmpConfigDelete').attr('data-delete-permission', 'FALSE');
        let deletePerm = $("#hacmpConfigDelete").data("delete-permission").toString().toLowerCase();
        let canDelete = deletePerm !== 'false';
        assert.notOk(canDelete, "Delete permission logic correct");
    });  


    QUnit.test("DataTable initializes and handles AJAX, search, and pagination", assert => {
        let done = assert.async();

        // Mock DataTable
        let ajaxDataCalled = false, ajaxDataSrcCalled = false;
        $.fn.DataTable = function (options) {
            // Simulate DataTable AJAX.data
            let d = { start: 0, length: 10 };
            $('#hacmpSearchInp').val('searchterm');
            window.selectedValues = [];
            options.ajax.data(d);
            assert.equal($('#hacmpSearchInp').val(), 'searchterm', "Search input used for searchString");

            // Simulate selectedValues usage
            window.selectedValues = ['name=foo', 'server=bar'];
            d = {};
            if (window.selectedValues && window.selectedValues.length > 0) {
                d.searchString = window.selectedValues.join(';');
                window.selectedValues = [];
            } else {
                d.searchString = $('#hacmpSearchInp').val();
            }

            // Assertions
            assert.equal(d.searchString, 'name=foo;server=bar', "selectedValues joined for searchString");
            assert.deepEqual(window.selectedValues, [], "selectedValues cleared after use");

            // Simulate error in dataSrc
            // Setup DOM elements needed for UI updates
            $('.pagination-column').removeClass('disabled'); // Reset state
            $('.dataTables_empty').text(''); // Clear message
            const dataSrc = function (json) {
                if (json.success) {
                    $('.pagination-column').removeClass('disabled');
                    return json.data.data;
                } else {
                    $('.pagination-column').addClass('disabled');
                    $('.dataTables_empty').text('No Data Found');
                    return [];
                }
            };
            const json = { success: false, message: "Error!", data: { data: [] } };
            const result = dataSrc(json);
            assert.deepEqual(result, [], "dataSrc returns empty array on error");
            assert.ok($('.pagination-column').hasClass('disabled'), "Pagination disabled on error");
            assert.equal($('.dataTables_empty').text(), 'No Data Found', "Error message displayed");

            ajaxDataCalled = true;
            ajaxDataSrcCalled = true;
            return this;
        };

        // Simulate DataTable init
        $('#hacmpClusterTable').DataTable({
            ajax: {
                data: function (d) { },
                dataSrc: function (json) { }
            }
        });

        setTimeout(() => {
            assert.ok(ajaxDataCalled && ajaxDataSrcCalled, "DataTable AJAX logic executed");
            done();
        }, 10);
    });

    QUnit.test("Search input triggers DataTable reload", assert => {
        let done = assert.async();
        let reloadCalled = false;
        // Mock DataTable instance with reload method
        let dtInstance = {
            ajax: { reload: function () { reloadCalled = true; } }
        };
        $.fn.DataTable = function () { return dtInstance; };

        // Simulate search input event
        $('#hacmpSearchInp').on('keyup', function () {
            dtInstance.ajax.reload();
        });
        $('#hacmpSearchInp').trigger('keyup');

        setTimeout(() => {
            assert.ok(reloadCalled, "DataTable reload triggered by search input");
            done();
        }, 10);
    });

    QUnit.test("Create button event opens modal or triggers handler", assert => {
        let handlerCalled = false;
        $('#hacmpCreateBtn').on('click', function () {
            handlerCalled = true;
        });
        $('#hacmpCreateBtn').trigger('click');
        assert.ok(handlerCalled, "Create button click event handled");
    });

    QUnit.test("Save button triggers AJAX and handles response", assert => {
        let done = assert.async();

        // Mock server response for save
        server.respondWith("POST", /CreateOrUpdate/, [
            200, { "Content-Type": "application/json" },
            JSON.stringify({ success: true })
        ]);

        // Simulate save event
        $('#SaveFunction').on('click', function () {
            $.ajax({
                type: "POST",
                url: "Configuration/HACMPCluster/CreateOrUpdate",
                data: {},
                success: function (resp) {
                    assert.ok(resp.success, "Save AJAX called and success handled");
                    done();
                }
            });
        });
        $('#SaveFunction').trigger('click');
    });

    QUnit.test("Delete button triggers AJAX and handles response", assert => {
        let done = assert.async();

        // Mock server response for delete
        server.respondWith("POST", /Delete/, [
            200, { "Content-Type": "application/json" },
            JSON.stringify({ success: true })
        ]);

        // Simulate delete event
        $('#hacmpConfirmDeleteBtn').on('click', function () {
            $.ajax({
                type: "POST",
                url: "Configuration/HACMPCluster/Delete",
                data: {},
                success: function (resp) {
                    assert.ok(resp.success, "Delete AJAX called and success handled");
                    done();
                }
            });
        });
        $('#hacmpConfirmDeleteBtn').trigger('click');
    });

    QUnit.test("notificationAlert called on AJAX error", assert => {
        window.notificationAlert = sinon.spy();
        let ajax = "";
        let options = {
            ajax: {
                dataSrc: function (json) {
                    if (!json.success && json.message) {
                        notificationAlert('warning', json.message);
                        ajax = true;
                    }
                    return [];
                }
            }
        };
        options.ajax.dataSrc({ success: false, message: "Test error" });
        assert.ok(ajax, true, "notificationAlert called on error");
    });

    QUnit.test("All major events are attached and can be triggered", assert => {
        // Create
        let createCalled = false;
        $('#hacmpCreateBtn').on('click', function () { createCalled = true; });
        $('#hacmpCreateBtn').trigger('click');
        assert.ok(createCalled, "Create event attached and triggered");

        // Save
        let saveCalled = false;
        $('#SaveFunction').on('click', function () { saveCalled = true; });
        $('#SaveFunction').trigger('click');
        assert.ok(saveCalled, "Save event attached and triggered");

        // Delete
        let deleteCalled = false;
        $('#hacmpConfirmDeleteBtn').on('click', function () { deleteCalled = true; });
        $('#hacmpConfirmDeleteBtn').trigger('click');
        assert.ok(deleteCalled, "Delete event attached and triggered");

        // Search
        let searchCalled = false;
        $('#hacmpSearchInp').on('keyup', function () { searchCalled = true; });
        $('#hacmpSearchInp').trigger('keyup');
        assert.ok(searchCalled, "Search event attached and triggered");
    });
});

QUnit.module("validateFields Randomized Loop", hooks => {
    // Helper to generate random strings
    function randomString(minLen, maxLen) {
        const specials = "!@#$%^&*()_+-=[]{}|;:',.<>/?";
        const letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const digits = "0123456789";
        const len = Math.floor(Math.random() * (maxLen - minLen + 1)) + minLen;
        let str = "";

        for (let i = 0; i < len; i++) {
            const rand = Math.random();
            if (rand < 0.1) {
                str += specials[Math.floor(Math.random() * specials.length)];
            } else if (rand < 0.3) {
                str += digits[Math.floor(Math.random() * digits.length)];
            } else {
                str += letters[Math.floor(Math.random() * letters.length)];
            }
        }
        return str;
    }


    hooks.beforeEach(() => {
        // Mock IsNameExist to alternate true/false
        let callCount = 0;
        window.IsNameExist = async function () {
            return ++callCount % 2 === 0 ? "Name already exists" : true;
        };
    });

    QUnit.test("Loop: display error/message from validateFields", async assert => {
        for (let i = 0; i < 250; i++) {
            const input = (i === 0) ? "" : (i === 1) ? randomString(121, 130) : randomString(1, 120);
            const $error = $('<div></div>');

            const result = await validateFields(input, "", $error, "Field is required", true);
            const errorMsg = $error.text(); // 👈 get actual validation error message

            let msg;
            if (result === true || result === undefined) {
                msg = `✅ Test #${i + 1}: PASS for "${input}"`;
            } else {
                msg = `❌ Test #${i + 1}: ERROR for "${input}" — Message: "${errorMsg}"`;
            }

            assert.ok(true, msg);
        }
    });
});
