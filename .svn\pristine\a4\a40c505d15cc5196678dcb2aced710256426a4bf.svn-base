﻿using ContinuityPatrol.Application.Features.AlertInformation.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertInformation.Commands;

public class UpdateAlertInformationTests : IClassFixture<AlertInformationFixture>
{
    private readonly AlertInformationFixture _alertInformationFixture;
    private readonly Mock<IAlertInformationRepository> _mockAlertInformationRepository;
    private readonly UpdateAlertInformationCommandHandler _handler;

    public UpdateAlertInformationTests(AlertInformationFixture alertInformationFixture)
    {
        _alertInformationFixture = alertInformationFixture;
        _mockAlertInformationRepository = AlertInformationRepositoryMocks.UpdateAlertInformationRepository(_alertInformationFixture.AlertInformations);
        _handler = new UpdateAlertInformationCommandHandler(_mockAlertInformationRepository.Object, _alertInformationFixture.Mapper);
    }

    [Fact]
    public async Task Handle_ValidAlertInformation_UpdateToAlertInformationsRepo()
    {
        _alertInformationFixture.UpdateAlertInformationCommand.Id = _alertInformationFixture.AlertInformations[0].ReferenceId;

        var result = await _handler.Handle(_alertInformationFixture.UpdateAlertInformationCommand, CancellationToken.None);

        var alertInformation = await _mockAlertInformationRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_alertInformationFixture.UpdateAlertInformationCommand.Type, alertInformation.Type);
    }

    [Fact]
    public async Task Handle_Return_UpdateAlertInformationResponse_When_AlertInformation_Updated()
    {
        _alertInformationFixture.UpdateAlertInformationCommand.Id = _alertInformationFixture.AlertInformations[0].ReferenceId;

        var result = await _handler.Handle(_alertInformationFixture.UpdateAlertInformationCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateAlertInformationResponse));
        
        result.Id.ShouldBeGreaterThan(0.ToString());
        
        result.Id.ShouldBe(_alertInformationFixture.UpdateAlertInformationCommand.Id);
        
        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotException_When_InvalidAlertInformationId()
    {
        _alertInformationFixture.UpdateAlertInformationCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_alertInformationFixture.UpdateAlertInformationCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsync_OnlyOnce()
    {
        _alertInformationFixture.UpdateAlertInformationCommand.Id = _alertInformationFixture.AlertInformations[0].ReferenceId;

        await _handler.Handle(_alertInformationFixture.UpdateAlertInformationCommand, CancellationToken.None);

        _mockAlertInformationRepository.Verify(x=>x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockAlertInformationRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.AlertInformation>()), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateFails_ThrowsException()
    {
        // Arrange
        var id = _alertInformationFixture.AlertInformations[0].ReferenceId;
        _alertInformationFixture.UpdateAlertInformationCommand.Id = id;

        _mockAlertInformationRepository
            .Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.AlertInformation>()))
            .ThrowsAsync(new Exception("Update failure"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(_alertInformationFixture.UpdateAlertInformationCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_RepositoryReturnsNull_ThrowsNotFoundException()
    {
        //Even with valid ID, if repository returns null

        var nonExistingId = "123";
        _alertInformationFixture.UpdateAlertInformationCommand.Id = nonExistingId;

        _mockAlertInformationRepository
            .Setup(x => x.GetByReferenceIdAsync(nonExistingId))
            .ReturnsAsync((Domain.Entities.AlertInformation)null!);

        await Assert.ThrowsAsync<NotFoundException>(() =>
            _handler.Handle(_alertInformationFixture.UpdateAlertInformationCommand, CancellationToken.None));
    }


    [Fact]
    public async Task Handle_UpdateCommandWithEmptyValues_ShouldUpdateProperly()
    {
        //handler works even if some fields are blank or optional.

        _alertInformationFixture.UpdateAlertInformationCommand.Id = _alertInformationFixture.AlertInformations[0].ReferenceId;
        _alertInformationFixture.UpdateAlertInformationCommand.Type = string.Empty;

        var result = await _handler.Handle(_alertInformationFixture.UpdateAlertInformationCommand, CancellationToken.None);

        result.ShouldBeOfType<UpdateAlertInformationResponse>();
        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

}