using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class BusinessFunctionFixture : IDisposable
{
    public List<BusinessFunction> BusinessFunctionPaginationList { get; set; }
    public List<BusinessFunction> BusinessFunctionList { get; set; }
    public BusinessFunction BusinessFunctionDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
    public const string busnessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";

    public ApplicationDbContext DbContext { get; private set; }

    public BusinessFunctionFixture()
    {
        var fixture = new Fixture();

        BusinessFunctionList = fixture.Create<List<BusinessFunction>>();

        BusinessFunctionPaginationList = fixture.CreateMany<BusinessFunction>(20).ToList();

        BusinessFunctionPaginationList.ForEach(x => x.CompanyId = CompanyId);
        BusinessFunctionPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BusinessFunctionPaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        BusinessFunctionPaginationList.ForEach(x => x.IsActive = true);

        BusinessFunctionList.ForEach(x => x.CompanyId = CompanyId);
        BusinessFunctionList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BusinessFunctionPaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        BusinessFunctionList.ForEach(x => x.IsActive = true);

        BusinessFunctionDto = fixture.Create<BusinessFunction>();
        BusinessFunctionDto.CompanyId = CompanyId;
        BusinessFunctionDto.ReferenceId = busnessFunctionId;
        BusinessFunctionPaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        BusinessFunctionDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
