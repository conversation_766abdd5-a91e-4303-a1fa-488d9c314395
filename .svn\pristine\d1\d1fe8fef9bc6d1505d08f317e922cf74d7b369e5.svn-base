<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Company.js QUnit Tests</title>
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.19.4.css">
</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture">
        <input type="text" id="companyName">
        <input type="text" id="companyDisplayName">
        <input type="text" id="companyWebAddress">
        <input type="file" id="companyLogoFile">
        <span id="companyNameError"></span>
        <span id="companyDisplayNameError"></span>
        <span id="companyWebAddressError"></span>
        <span id="companyLogoError"></span>
        <button id="companyCreateBtn" data-create-permission="true">Create</button>
        <button id="companySaveBtn">Save</button>
        <table id="companyTable"><tbody></tbody></table>
        <div id="companyCreateModal" class="modal"></div>
        <div id="companyDeleteModal" class="modal"></div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/qunit/qunit-2.19.4.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sinon.js/15.0.1/sinon.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="/js/Common/common.js"></script>
    <script src="/js/Configuration/Company/company.js"></script>   
    <script src="/js/Configuration/Company/companyTest.js"></script>
</body>
</html>
