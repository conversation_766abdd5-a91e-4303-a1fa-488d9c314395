﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.MYSQLMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Queries.GetPaginatedList;

public class GetMYSQLMonitorLogsPaginatedListQueryHandler : IRequestHandler<GetMYSQLMonitorLogsPaginatedListQuery,
    PaginatedResult<MYSQLMonitorLogsListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMysqlMonitorLogsRepository _mysqlMonitorLogsRepository;

    public GetMYSQLMonitorLogsPaginatedListQueryHandler(IMysqlMonitorLogsRepository mysqlMonitorLogsRepository,
        IMapper mapper)
    {
        _mysqlMonitorLogsRepository = mysqlMonitorLogsRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<MYSQLMonitorLogsListVm>> Handle(GetMYSQLMonitorLogsPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _mysqlMonitorLogsRepository.GetPaginatedQuery();

        var productFilterSpec = new MySqlMonitorLogsFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<MYSQLMonitorLogsListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}