﻿using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;

namespace ContinuityPatrol.Application.Features.User.Events.UpdateSendEmail;

public class UpdateSendEmailEventHandler : INotificationHandler<UpdateSendEmailEvent>
{
    private readonly IConfiguration _config;

    private readonly IEmailService _emailService;
    private readonly IGlobalSettingRepository _globalSettingRepository;
    private readonly ILogger<UpdateSendEmailEventHandler> _logger;
    private readonly ISmtpConfigurationRepository _smtpConfigurationRepository;

    public UpdateSendEmailEventHandler(ILogger<UpdateSendEmailEventHandler> logger,
        ISmtpConfigurationRepository smtpConfigurationRepository, IEmailService emailService, IConfiguration config,
        IGlobalSettingRepository globalSettingRepository)
    {
        _logger = logger;
        _smtpConfigurationRepository = smtpConfigurationRepository;
        _emailService = emailService;
        _config = config;
        _globalSettingRepository = globalSettingRepository;
    }

    public async Task Handle(UpdateSendEmailEvent notification, CancellationToken cancellationToken)
    {
        var globalSetting = await _globalSettingRepository.GlobalSettingBySettingKey("Email Notification");

        if (globalSetting is null || globalSetting.GlobalSettingValue.Equals("false"))
        {
            _logger.LogWarning("The email notification feature is not enabled in the global settings.");
            return;
        }

        var smtpConfigurations = (await _smtpConfigurationRepository.ListAllAsync()).LastOrDefault();

        if (smtpConfigurations is not null && notification.IsPreferredMode && smtpConfigurations.UserName is not null)
        {
            var emailDto = new EmailDto
            {
                From = smtpConfigurations.UserName,
                SmtpHost = smtpConfigurations.SmtpHost,
                EnableSSL = smtpConfigurations.EnableSSL,
                Port = smtpConfigurations.Port,
                Password = smtpConfigurations.Password
            };

            await SendEmailWithDetails(notification, emailDto);
        }
    }

    private async Task SendEmailWithDetails(UpdateSendEmailEvent request, EmailDto email)
    {
        var loginUrl = _config.GetValue<string>("SignalR:Url");

        var version = _config.GetValue<string>("CP:Version");
        var body = EmailTemplateHelper.GetAccountUpdateRoleEmailBody(request, version, loginUrl);

        var imageNames = new List<string>
        {
            "abstract.png",
            "cp_logo.png",
            "User_created.png",
            "username.png",
            "password.png",
            "company_name.png"
        };

        var htmlView = HtmlEmailBuilder.BuildHtmlView(body, imageNames, "UserCreate");

        await _emailService.SendEmail(new EmailDto
        {
            From = email.From,
            To = request.Email,
            Subject = "Your Account Role Has Been Updated!.",
            HtmlBody = htmlView,
            SmtpHost = email.SmtpHost,
            Port = email.Port,
            EnableSSL = email.EnableSSL,
            Password = email.Password
        });

        // await IsMailReceived(email, request.Email, request.UserId);


        // var uniqueJobId = Guid.NewGuid().ToString();

        //// _client.Enqueue(uniqueJobId, () => IsMailReceived(email, request.Email,request.UserId));


        // BackgroundJob.Enqueue( ()=> IsMailReceived(email, request.Email, request.UserId));


        // // await IsMailReceived(email, request.Email,request.UserId);


        // _logger.LogInformation($"User '{request.LoginName}' created email sent successFully.");
    }

    //public async Task IsMailReceived(EmailDto email,string toEmail,string userId)
    //{
    //    var response = await _emailService.IsMailSendToClientSuccessfully(new EmailDto
    //    {
    //        From = email.From,
    //        To = toEmail,
    //        Password = email.Password,
    //        SmtpHost = email.SmtpHost
    //    });

    //    var userInfo = await _userInfoRepository.GetUserInfoByUserIdAsync(userId);
    //    userInfo.IsEmailSuccess = response;
    //    await _userInfoRepository.UpdateAsync(userInfo);
    //}
}