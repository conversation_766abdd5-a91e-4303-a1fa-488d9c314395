﻿using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;

namespace ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetNames;

public class
    GetWorkflowProfileInfoNameQueryHandler : IRequestHandler<GetWorkflowProfileInfoNameQuery,
        List<WorkflowProfileInfoNameVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowProfileInfoViewRepository _workflowProfileInfoViewRepository;

    public GetWorkflowProfileInfoNameQueryHandler(IMapper mapper,
        IWorkflowProfileInfoViewRepository workflowProfileInfoViewRepository)
    {
        _mapper = mapper;
        _workflowProfileInfoViewRepository = workflowProfileInfoViewRepository;
    }

    public async Task<List<WorkflowProfileInfoNameVm>> Handle(GetWorkflowProfileInfoNameQuery request,
        CancellationToken cancellationToken)
    {
        var workflowProfileInfoNames = await _workflowProfileInfoViewRepository.GetWorkflowProfileInfoNames();

        var workflowProfileInfoDto = _mapper.Map<List<WorkflowProfileInfoNameVm>>(workflowProfileInfoNames);

        return workflowProfileInfoDto;
    }
}