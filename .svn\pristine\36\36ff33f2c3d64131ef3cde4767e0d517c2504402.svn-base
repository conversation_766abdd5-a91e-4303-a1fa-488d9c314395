﻿using ContinuityPatrol.Application.Contracts.Persistence;

namespace ContinuityPatrol.Application.Features.Database.Queries.GetType;

public class GetDatabaseTypeQueryHandler : IRequestHandler<GetDatabaseTypeQuery, List<DatabaseTypeVm>>
{
    private readonly IDatabaseViewRepository _databaseViewRepository;
    private readonly IMapper _mapper;

    public GetDatabaseTypeQueryHandler(IMapper mapper, IDatabaseViewRepository databaseViewRepository)
    {
        _mapper = mapper;
        _databaseViewRepository = databaseViewRepository;
    }

    public async Task<List<DatabaseTypeVm>> Handle(GetDatabaseTypeQuery request, CancellationToken cancellationToken)
    {
        var databases = request.TypeId != null
            ? await _databaseViewRepository.GetDatabaseByDatabaseTypeId(request.TypeId)
            : await _databaseViewRepository.ListAllAsync();

        return databases.Count <= 0 ? new List<DatabaseTypeVm>() : _mapper.Map<List<DatabaseTypeVm>>(databases);
    }
}