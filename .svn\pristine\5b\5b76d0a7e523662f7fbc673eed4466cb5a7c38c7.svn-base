﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class AccessManagerRepositoryTests : IClassFixture<AccessManagerFixture>
{
    private readonly AccessManagerFixture _accessManagerFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly AccessManagerRepository _repository;

    public AccessManagerRepositoryTests(AccessManagerFixture accessManagerFixture)
    {
        _accessManagerFixture = accessManagerFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new AccessManagerRepository(_dbContext, DbContextFactory.GetMockUserService());
    }


    [Fact]
    public async Task GetAccessManagerRoles_ReturnsEmptyList_WhenNoData()
    {
        var repository = new AccessManagerRepository(_dbContext, DbContextFactory.GetMockUserService());

        var result = await repository.GetAccessManagerRoles();
        Assert.Empty(result);
    }


    [Fact]
    public async Task GetAccessManagerRoles_ShouldReturnExpectedCount_WhenAccessManagersExist()
    {
        _dbContext.AccessManagers.AddRange(_accessManagerFixture.AccessManagerList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetAccessManagerRoles();

        Assert.Equal(3,result.Count);
    }


    [Fact]
    public async Task GetAccessManagerRoles_ReturnsOnlyChildCompanies_WhenIsParentFalse()
    {
        var mockUserService = new Mock<ILoggedInUserService>();

        mockUserService.Setup(x => x.CompanyId).Returns("CHILD_1");
        mockUserService.Setup(x => x.IsParent).Returns(false);

        var repository = new AccessManagerRepository(_dbContext, mockUserService.Object);

        _accessManagerFixture.AccessManagerList[0].CompanyId = "CHILD_1";
        _accessManagerFixture.AccessManagerList[2].CompanyId = "CHILD_1";


        await _dbContext.AccessManagers.AddRangeAsync(_accessManagerFixture.AccessManagerList);
        await _dbContext.SaveChangesAsync();

        var result = await repository.GetAccessManagerRoles();

        Assert.Equal(2, result.Count);
    }


    [Fact]
    public async Task ListAllAsync_ShouldReturnExpectedCount_WhenAccessManagersExist()
    {
        _dbContext.AccessManagers.AddRange(_accessManagerFixture.AccessManagerList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoAccessManagersMatchCriteria()
    {
        var mock = new Mock<ILoggedInUserService>();
        mock.Setup(x => x.CompanyId).Returns("COMPANY_123");
        mock.Setup(x => x.UserId).Returns("USER_456");
        mock.Setup(x => x.IsParent).Returns(false);
        
        _accessManagerFixture.AccessManagerList.ForEach(x=>x.CompanyId = Guid.NewGuid().ToString());

        var repository = new AccessManagerRepository(_dbContext, mock.Object);

        _dbContext.AccessManagers.AddRange(_accessManagerFixture.AccessManagerList);
        await _dbContext.SaveChangesAsync();

        var result = await repository.ListAllAsync();

        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsOnlyChildCompanies_WhenIsParentFalse()
    {
        var mockUserService = new Mock<ILoggedInUserService>();
        
        mockUserService.Setup(x => x.CompanyId).Returns("CHILD_1");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        
        var repository = new AccessManagerRepository(_dbContext, mockUserService.Object);

        _accessManagerFixture.AccessManagerList[0].CompanyId = "CHILD_1";
        _accessManagerFixture.AccessManagerList[2].CompanyId = "CHILD_1";
      

        await _dbContext.AccessManagers.AddRangeAsync(_accessManagerFixture.AccessManagerList);
        await _dbContext.SaveChangesAsync();

        var result = await repository.ListAllAsync();

        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.CompanyId == "CHILD_1");
        Assert.DoesNotContain(result, x => x.CompanyId == "PARENT_COMPANY");
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsAccessManager_WhenExistsForCurrentCompany()
    {
        var companyId = "COMPANY_123";

        _dbContext.AccessManagers.Add(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(_accessManagerFixture.AccessManagerDto.ReferenceId);

        Assert.NotNull(result);
        Assert.Equal(_accessManagerFixture.AccessManagerDto.ReferenceId, result.ReferenceId);
        Assert.Equal(companyId, result.CompanyId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsAccessManager_WhenIsParentFalse_AndCompanyMatches()
    {
        var referenceId = "REF_123";
        var companyId = "ChildCompany_123";

        _accessManagerFixture.AccessManagerDto.CompanyId = companyId;
        _accessManagerFixture.AccessManagerDto.ReferenceId = referenceId;

        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(companyId);

        var repository = new AccessManagerRepository(_dbContext, mockUserService.Object);

        _dbContext.AccessManagers.Add(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await repository.GetByReferenceIdAsync(referenceId);

        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal(companyId, result.CompanyId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenIsParentFalse_AndCompanyDoesNotMatch()
    {
        var referenceId = "REF_123";
        var companyId = "COMPANY_123";
        var differentCompanyId = "OTHER_COMPANY";

        _accessManagerFixture.AccessManagerDto.CompanyId = differentCompanyId;
        _accessManagerFixture.AccessManagerDto.ReferenceId = referenceId;

        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(companyId);

        var repository = new AccessManagerRepository(_dbContext, mockUserService.Object);

        _dbContext.AccessManagers.Add(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await repository.GetByReferenceIdAsync(referenceId);

        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenReferenceIdDoesNotExist()
    {
        var nonExistentReferenceId = "NON_EXISTENT_REF";
        var companyId = "COMPANY_123";

        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(companyId);

        var repository = new AccessManagerRepository(_dbContext, mockUserService.Object);

        var result = await repository.GetByReferenceIdAsync(nonExistentReferenceId);

        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsFirstMatching_WhenMultipleExist_AndIsParentTrue()
    {
        var referenceId = "REF_123";

        _accessManagerFixture.AccessManagerList.ForEach(x=>x.ReferenceId = referenceId);

        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(true);

        var repository = new AccessManagerRepository(_dbContext, mockUserService.Object);

        _dbContext.AccessManagers.AddRange(_accessManagerFixture.AccessManagerList);
        await _dbContext.SaveChangesAsync();

        var result = await repository.GetByReferenceIdAsync(referenceId);

        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenIsParentTrue_ButReferenceIdMismatch()
    {
        var nonExistentReferenceId = Guid.NewGuid().ToString();
        
        _dbContext.AccessManagers.Add(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(nonExistentReferenceId);

        Assert.Null(result); 
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsAll_WhenIsParentTrue()
    {
        await _dbContext.AccessManagers.AddRangeAsync(_accessManagerFixture.AccessManagerPaginationList);
        await _dbContext.SaveChangesAsync();

        var productFilterSpec = new AccessManagerFilterSpecification("");

        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 10,
            productFilterSpec: productFilterSpec,
            sortColumn: "RoleName",
            sortOrder: "asc");

        Assert.Equal(20, result.TotalCount);
        Assert.Equal(10, result.Data.Count);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsCompanySpecific_WhenIsParentFalse()
    {
        var companyId = "CHILDCOMPANY_123";

        _accessManagerFixture.AccessManagerPaginationList
            .Take(3)
            .ToList()
            .ForEach(x => x.CompanyId = companyId);

        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(companyId);

        var repository = new AccessManagerRepository(_dbContext, mockUserService.Object);

        await _dbContext.AccessManagers.AddRangeAsync(_accessManagerFixture.AccessManagerPaginationList);
        await _dbContext.SaveChangesAsync();

        var productFilterSpec = new AccessManagerFilterSpecification("");

        var result = await repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 10,
            productFilterSpec: productFilterSpec,
            sortColumn: "RoleName",
            sortOrder: "asc");

        Assert.Equal(3, result.TotalCount);
        Assert.All(result.Data, x => Assert.Equal(companyId, x.CompanyId));
    }
    [Fact]
    public async Task PaginatedListAllAsync_AppliesPaginationCorrectly()
    {
        await _dbContext.AccessManagers.AddRangeAsync(_accessManagerFixture.AccessManagerPaginationList);
        await _dbContext.SaveChangesAsync();

        var productFilterSpec = new AccessManagerFilterSpecification("");

        // Act
        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 2,
            pageSize: 5,
            productFilterSpec: productFilterSpec,
            sortColumn: "RoleName",
            sortOrder: "asc");

        // Assert
        Assert.Equal(20, result.TotalCount);
        Assert.Equal(5, result.Data.Count);
    }

    [Fact]
    public async Task PaginatedListAllAsync_AppliesSearchFilter()
    {
        // Arrange
        _accessManagerFixture.AccessManagerPaginationList[0].RoleName = "SuperAdmin";

        await _dbContext.AccessManagers.AddRangeAsync(_accessManagerFixture.AccessManagerPaginationList);
        await _dbContext.SaveChangesAsync();

        var productFilterSpec = new AccessManagerFilterSpecification("accessmanagerrole=Super");

        // Act
        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 10,
            productFilterSpec: productFilterSpec,
            sortColumn: "RoleName",
            sortOrder: "asc");

        // Assert
        Assert.Equal(1, result.TotalCount);
       // Assert.Equal("SuperAdmin", result.Data.FirstOrDefault()!.RoleName);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsEmpty_WhenNoMatches()
    {
        var companyId = "COMPANY_123";

        _accessManagerFixture.AccessManagerPaginationList.ForEach(x=>x.CompanyId = Guid.NewGuid().ToString());

        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(companyId);

        var repository = new AccessManagerRepository(_dbContext, mockUserService.Object);

        await _dbContext.AccessManagers.AddRangeAsync(_accessManagerFixture.AccessManagerPaginationList);
        await _dbContext.SaveChangesAsync();

        var productFilterSpec = new AccessManagerFilterSpecification("");

        // Act
        var result = await repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 10,
            productFilterSpec: productFilterSpec,
            sortColumn: "RoleName",
            sortOrder: "asc");

        // Assert
        Assert.Equal(0, result.TotalCount);
        Assert.Empty(result.Data);
    }

    [Fact]
    public async Task IsAccessManagerRoleUnique_ReturnsTrue_WhenRoleIsUnique()
    {
        _accessManagerFixture.AccessManagerDto.RoleName = "SuperAdmin";
        await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsAccessManagerRoleUnique(_accessManagerFixture.AccessManagerDto.RoleName);

        Assert.True(result);
    }

    [Fact]
    public async Task IsAccessManagerRoleUnique_ReturnsFalse_WhenRoleNotExists()
    {
        var role = "operator";
        await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsAccessManagerRoleUnique(role);

        Assert.False(result); 
    }


    //[Fact]
    //public async Task IsAccessManagerRoleUnique_IsCaseSensitive()
    //{
    //    _accessManagerFixture.AccessManagerDto.RoleName = "Admin";
    //    await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
    //    await _dbContext.SaveChangesAsync();

    //    var result = await _repository.IsAccessManagerRoleUnique("ADMIN");

    //    Assert.True(result);
    //}

    [Fact]
    public async Task IsAccessManagerRoleExist_ReturnsTrue_WhenRoleExists_WithoutId()
    {
        var roleName = "operator";

        _accessManagerFixture.AccessManagerDto.RoleName = roleName;

        await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsAccessManagerRoleExist(roleName, null);

        Assert.True(result);
    }
    [Fact]
    public async Task IsAccessManagerRoleExist_ReturnsFalse_WhenRoleUnique_WithoutId()
    {
        var roleName = "Administrator";

        var result = await _repository.IsAccessManagerRoleExist(roleName, null);

        Assert.False(result);
    }

    [Fact]
    public async Task IsAccessManagerRoleExist_ReturnsFalse_WhenRoleExists_WithSameId()
    {
        var existingId = Guid.NewGuid().ToString();
        var roleName = "Manager";

        _accessManagerFixture.AccessManagerDto.ReferenceId = existingId;
        _accessManagerFixture.AccessManagerDto.RoleName = roleName;

        await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsAccessManagerRoleExist(roleName, existingId);

        Assert.False(result);
    }

    [Fact]
    public async Task IsAccessManagerRoleExist_ReturnsTrue_WhenRoleExists_WithDifferentId()
    {
        var testId = Guid.NewGuid().ToString();
        var roleName = "SiteAdmin";

        _accessManagerFixture.AccessManagerDto.RoleName = roleName;

        await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsAccessManagerRoleExist(roleName, testId);

        Assert.True(result);
    }

    [Fact]
    public async Task IsAccessManagerRoleExist_ReturnsFalse_WhenInvalidGuidProvided()
    {
        var invalidId = "not-a-valid-guid";
        var roleName = "SiteAdmin";
        _accessManagerFixture.AccessManagerDto.RoleName = roleName;

        await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsAccessManagerRoleExist(roleName, invalidId);

        Assert.True(result);
    }

    [Fact]
    public async Task IsAccessManagerRoleExist_IsCaseSensitive()
    {
        _accessManagerFixture.AccessManagerDto.RoleName = "SiteAdmin";

        await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsAccessManagerRoleExist("SITEADMIN", null);

        Assert.False(result);
    }


    [Fact]
    public async Task GetAccessManagerByRoleId_ReturnsAccessManager_WhenExistsAndActive()
    {
        var roleId = Guid.NewGuid().ToString();

        _accessManagerFixture.AccessManagerDto.RoleId = roleId;
        _accessManagerFixture.UserRoleDto.ReferenceId = roleId;

        await _dbContext.UserRoles.AddAsync(_accessManagerFixture.UserRoleDto);
        await _dbContext.SaveChangesAsync();


        await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetAccessManagerByRoleId(roleId);

        Assert.NotNull(result);
        Assert.Equal(roleId, result.RoleId);
        Assert.Equal(_accessManagerFixture.AccessManagerDto.Id, result.Id);
    }

    [Fact]
    public async Task GetAccessManagerByRoleId_ReturnsNull_WhenRoleIdDoesNotExist()
    {
        var nonExistentRoleId = Guid.NewGuid().ToString();

        var roleId = Guid.NewGuid().ToString();

        _accessManagerFixture.AccessManagerDto.RoleId = roleId;
        _accessManagerFixture.UserRoleDto.ReferenceId = roleId;

        await _dbContext.UserRoles.AddAsync(_accessManagerFixture.UserRoleDto);
        await _dbContext.SaveChangesAsync();

        await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetAccessManagerByRoleId(nonExistentRoleId);

        Assert.Null(result);
    }

    [Fact]
    public async Task GetAccessManagerByRoleId_ReturnsNull_WhenInactive()
    {
        var roleId = Guid.NewGuid().ToString();

        _accessManagerFixture.AccessManagerDto.RoleId = roleId;
      
        _accessManagerFixture.UserRoleDto.ReferenceId = roleId;

        await _dbContext.UserRoles.AddAsync(_accessManagerFixture.UserRoleDto);
        await _dbContext.SaveChangesAsync();

        await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        _accessManagerFixture.AccessManagerDto.IsActive = false;

        _dbContext.AccessManagers.Update(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();


        var result = await _repository.GetAccessManagerByRoleId(roleId);

        Assert.Null(result);
    }

    [Fact]
    public async Task GetAccessManagerByRoleId_ReturnsNull_ForEmptyRoleId()
    {
        var result = await _repository.GetAccessManagerByRoleId("");

        Assert.Null(result);
    }

    [Fact]
    public async Task GetAccessManagerByRoleId_ReturnsNull_ForNullRoleId()
    {
        var result = await _repository.GetAccessManagerByRoleId(null);

        Assert.Null(result);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsAccessManager_WhenIsParentTrue()
    {
        _accessManagerFixture.AccessManagerDto.Id = 1;

        await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByIdAsync(1);

        Assert.NotNull(result);
        Assert.Equal(_accessManagerFixture.AccessManagerDto.Id, result.Id);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsAccessManager_WhenIsParentFalseAndCompanyMatches()
    {
        var companyId = "COMPANY_123";

        _accessManagerFixture.AccessManagerDto.CompanyId = companyId;
        _accessManagerFixture.AccessManagerDto.Id = 2;

        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(companyId);

        var repository = new AccessManagerRepository(_dbContext, mockUserService.Object);

        var testId = 2;
        await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await repository.GetByIdAsync(testId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(testId, result.Id);
        Assert.Equal(companyId, result.CompanyId);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenIsParentFalseAndCompanyDoesNotMatch()
    {
        var companyId = "COMPANY_123";

        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.CompanyId).Returns(companyId);

        _accessManagerFixture.AccessManagerDto.CompanyId = Guid.NewGuid().ToString();
        _accessManagerFixture.AccessManagerDto.Id = 3;

        var repository = new AccessManagerRepository(_dbContext, mockUserService.Object);

        var testId = 3;
        await _dbContext.AccessManagers.AddAsync(_accessManagerFixture.AccessManagerDto);
        await _dbContext.SaveChangesAsync();

        var result = await repository.GetByIdAsync(testId);

        Assert.Null(result);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenRecordDoesNotExist()
    {
        var nonExistentId = 999;
        var result = await _repository.GetByIdAsync(nonExistentId);

        Assert.Null(result);
    }

    [Fact]
    public async Task GetAccessManagersByRoleIds_ReturnsMatchingAccessManagers()
    {
        var firstRoleId = Guid.NewGuid().ToString();
        var secondRoleId = Guid.NewGuid().ToString();

        var roleIds = new List<string> { firstRoleId, secondRoleId };

        _accessManagerFixture.AccessManagerList[0].RoleId = firstRoleId;
        _accessManagerFixture.AccessManagerList[1].RoleId = secondRoleId;

        await _dbContext.AccessManagers.AddRangeAsync(_accessManagerFixture.AccessManagerList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetAccessManagersByRoleIds(roleIds);

        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.RoleId == firstRoleId);
        Assert.Contains(result, x => x.RoleId == secondRoleId);
        Assert.DoesNotContain(result, x => x.RoleId == Guid.NewGuid().ToString());
    }

    [Fact]
    public async Task GetAccessManagersByRoleIds_ReturnsEmptyList_WhenNoMatches()
    {
        await _dbContext.AccessManagers.AddRangeAsync(_accessManagerFixture.AccessManagerList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetAccessManagersByRoleIds(new List<string> { Guid.NewGuid().ToString() });

        Assert.Empty(result);
    }

    [Fact]
    public async Task GetAccessManagersByRoleIds_ReturnsEmptyList_WhenInputEmpty()
    {
        await _dbContext.AccessManagers.AddRangeAsync(_accessManagerFixture.AccessManagerList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetAccessManagersByRoleIds(new List<string>());

        Assert.Empty(result);
    }

    [Fact]
    public async Task GetAccessManagersByRoleIds_HandlesNullInput()
    {
        await _dbContext.AccessManagers.AddRangeAsync(_accessManagerFixture.AccessManagerList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetAccessManagersByRoleIds(null);

        Assert.Empty(result);
    }


}