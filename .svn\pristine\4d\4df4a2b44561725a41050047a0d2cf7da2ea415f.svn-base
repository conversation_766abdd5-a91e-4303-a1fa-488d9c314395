﻿using ContinuityPatrol.Application.Features.DataSetColumns.Event.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.DataSetColumns.Events;

public class DataSetColumnsCreatedEventHandlerTests : IClassFixture<DataSetColumnsFixture>, IClassFixture<UserActivityFixture>
{
    private readonly DataSetColumnsFixture _dataSetColumnsFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly DataSetColumnsCreatedEventHandler _handler;

    public DataSetColumnsCreatedEventHandlerTests(DataSetColumnsFixture dataSetColumnsFixture, UserActivityFixture userActivityFixture)
    {
        _dataSetColumnsFixture = dataSetColumnsFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockDataSetColumnsEventLogger = new Mock<ILogger<DataSetColumnsCreatedEventHandler>>();

        _mockUserActivityRepository = DataSetColumnsRepositoryMocks.CreateDataSetColumnsEventRepository(_userActivityFixture.UserActivities);

        _handler = new DataSetColumnsCreatedEventHandler(mockLoggedInUserService.Object, mockDataSetColumnsEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateDataSetColumnsEventCreated()
    {
        _dataSetColumnsFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_dataSetColumnsFixture.DataSetColumnsCreatedEvent, CancellationToken.None);

        result.Equals(_dataSetColumnsFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_dataSetColumnsFixture.DataSetColumnsCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_CreateDataSetColumnsEventCreated()
    {
        _dataSetColumnsFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_dataSetColumnsFixture.DataSetColumnsCreatedEvent, CancellationToken.None);

        result.Equals(_dataSetColumnsFixture.UserActivities[0].Id);

        result.Equals(_dataSetColumnsFixture.DataSetColumnsCreatedEvent.TableName);

        await Task.CompletedTask;
    }
}