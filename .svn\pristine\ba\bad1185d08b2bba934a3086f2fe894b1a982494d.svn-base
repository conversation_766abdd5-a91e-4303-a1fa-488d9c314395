using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class AboutCpFixture : IDisposable
{
    public List<AboutCp> AboutCps { get; set; }
    public AboutCp AboutCp { get; set; }

    public AboutCpFixture()
    {
        AboutCps = AutoAboutCpFixture.Create<List<AboutCp>>();
        AboutCp = AutoAboutCpFixture.Create<AboutCp>();
    }

    public Fixture AutoAboutCpFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customize<AboutCp>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                .With(b => b.CreatedDate, DateTime.UtcNow.AddDays(-30))
                .With(b => b.LastModifiedDate, DateTime.UtcNow)
                .With(b => b.<PERSON><PERSON>y, "TestUser")
                .With(b => b.LastModifiedBy, "TestUser"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
