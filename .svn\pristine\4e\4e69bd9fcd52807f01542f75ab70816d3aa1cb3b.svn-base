﻿using ContinuityPatrol.Application.Features.DataSetColumns.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.DataSetColumns.Commands;

public class UpdateDataSetColumnsTests : IClassFixture<DataSetColumnsFixture>
{
    private readonly DataSetColumnsFixture _dataSetColumnsFixture;

    private readonly Mock<IDataSetColumnsRepository> _mockDataSetColumnsRepository;

    private readonly UpdateDataSetColumnsCommandHandler _handler;

    public UpdateDataSetColumnsTests(DataSetColumnsFixture dataSetColumnsFixture)
    {
        _dataSetColumnsFixture = dataSetColumnsFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockDataSetColumnsRepository = DataSetColumnsRepositoryMocks.UpdateDataSetColumnsRepository(_dataSetColumnsFixture.DataSetColumns);

        _handler = new UpdateDataSetColumnsCommandHandler(_dataSetColumnsFixture.Mapper, _mockDataSetColumnsRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidDataSetColumns_UpdateReferenceIdAsync_ToDataSetColumnsRepo()
    {
        _dataSetColumnsFixture.UpdateDataSetColumnsCommand.Id = _dataSetColumnsFixture.DataSetColumns[0].ReferenceId;

        var result = await _handler.Handle(_dataSetColumnsFixture.UpdateDataSetColumnsCommand, CancellationToken.None);

        var dataSetColumn = await _mockDataSetColumnsRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_dataSetColumnsFixture.UpdateDataSetColumnsCommand.TableName, dataSetColumn.TableName);
    }

    [Fact]
    public async Task Handle_Return_ValidDataSetColumnsResponse_WhenUpdate_DataSetColumns()
    {
        _dataSetColumnsFixture.UpdateDataSetColumnsCommand.Id = _dataSetColumnsFixture.DataSetColumns[0].ReferenceId;

        var result = await _handler.Handle(_dataSetColumnsFixture.UpdateDataSetColumnsCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateDataSetColumnsResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_dataSetColumnsFixture.UpdateDataSetColumnsCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidDataSetColumnsId()
    {
        _dataSetColumnsFixture.UpdateDataSetColumnsCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_dataSetColumnsFixture.UpdateDataSetColumnsCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        _dataSetColumnsFixture.UpdateDataSetColumnsCommand.Id = _dataSetColumnsFixture.DataSetColumns[0].ReferenceId;

        await _handler.Handle(_dataSetColumnsFixture.UpdateDataSetColumnsCommand, CancellationToken.None);

        _mockDataSetColumnsRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockDataSetColumnsRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.DataSetColumns>()), Times.Once);
    }
}