﻿using ContinuityPatrol.Application.Features.ServerSubType.Queries.GetNameUnique;

namespace ContinuityPatrol.Application.UnitTests.Features.ServerSubType.Queries
{
    public class GetServerSubTypeNameUniqueQueryHandlerTests
    {
        private readonly Mock<IServerSubTypeRepository> _mockServerSubTypeRepository;
        private readonly GetServerSubTypeNameUniqueQueryHandler _handler;

        public GetServerSubTypeNameUniqueQueryHandlerTests()
        {
            _mockServerSubTypeRepository = new Mock<IServerSubTypeRepository>();
            _handler = new GetServerSubTypeNameUniqueQueryHandler(_mockServerSubTypeRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnTrue_WhenServerSubTypeExists()
        {
            var request = new GetServerSubTypeNameUniqueQuery
            {
                SubType = "TestSubType",
                SubTypeId = Guid.NewGuid().ToString(),
            };

            _mockServerSubTypeRepository
                .Setup(x => x.IsServerSubTypeExist(request.SubType, request.SubTypeId))
                .ReturnsAsync(true);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.True(result);
            _mockServerSubTypeRepository.Verify(x => x.IsServerSubTypeExist(request.SubType, request.SubTypeId), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnFalse_WhenServerSubTypeDoesNotExist()
        {
            var request = new GetServerSubTypeNameUniqueQuery
            {
                SubType = "NonExistentSubType",
                SubTypeId = Guid.NewGuid().ToString(),
            };

            _mockServerSubTypeRepository
                .Setup(x => x.IsServerSubTypeExist(request.SubType, request.SubTypeId))
                .ReturnsAsync(false);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.False(result);
            _mockServerSubTypeRepository.Verify(x => x.IsServerSubTypeExist(request.SubType, request.SubTypeId), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowException_WhenRepositoryThrows()
        {
            var request = new GetServerSubTypeNameUniqueQuery
            {
                SubType = "TestSubType",
                SubTypeId = Guid.NewGuid().ToString(),
            };

            _mockServerSubTypeRepository
                .Setup(x => x.IsServerSubTypeExist(request.SubType, request.SubTypeId))
                .ThrowsAsync(new Exception("Database error"));

            var exception = await Assert.ThrowsAsync<Exception>(() =>
                _handler.Handle(request, CancellationToken.None));

            Assert.Equal("Database error", exception.Message);
            _mockServerSubTypeRepository.Verify(x => x.IsServerSubTypeExist(request.SubType, request.SubTypeId), Times.Once);
        }
    }
}
