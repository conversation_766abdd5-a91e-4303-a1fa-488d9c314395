using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class TeamMasterRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly TeamMasterRepository _repository;
    private readonly TeamMasterFixture _fixture;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public TeamMasterRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        
        _repository = new TeamMasterRepository(_dbContext, _mockLoggedInUserService.Object);
        _fixture = new TeamMasterFixture();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
    }

    #region GetAllTeamNames Tests

    [Fact]
    public async Task GetAllTeamNames_ShouldReturnAllActiveTeams_OrderedByGroupName()
    {
        // Arrange
        await ClearDatabase();

        var team1 = _fixture.CreateTeamMaster(groupName: "Team C", description: "COMPANY_123", isActive: true);
        var team2 = _fixture.CreateTeamMaster(groupName: "Team A", description: "COMPANY_456", isActive: true);
        var team3 = _fixture.CreateTeamMaster(groupName: "Team B", description: "COMPANY_789", isActive: true);
        var inactiveTeam = _fixture.CreateTeamMaster(groupName: "Team D", description: "COMPANY_123", isActive: false);
        await _dbContext.TeamMasters.AddRangeAsync(team1, team2, team3, inactiveTeam);
        _dbContext.SaveChanges();
 

        // Act
        var result = await _repository.GetAllTeamNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, team => Assert.True(team.IsActive));
        
        // Should be ordered by GroupName
        Assert.Equal("Team A", result[0].GroupName);
        Assert.Equal("Team B", result[1].GroupName);
        Assert.Equal("Team C", result[2].GroupName);
        
        // Should only return ReferenceId and GroupName
        Assert.All(result, team => 
        {
            Assert.NotNull(team.ReferenceId);
            Assert.NotNull(team.GroupName);
            Assert.Null(team.Description);
        });
    }

    [Fact]
    public async Task GetAllTeamNames_ShouldReturnEmpty_WhenNoActiveTeams()
    {
        // Arrange
        await ClearDatabase();

        var inactiveTeam = _fixture.CreateTeamMaster(groupName: "Inactive Team", isActive: false);
        await _dbContext.TeamMasters.AddRangeAsync( inactiveTeam);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetAllTeamNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetTeamNameById Tests

    [Fact]
    public async Task GetTeamNameById_ShouldReturnTeam_WhenIdExists()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(
            groupName: "Test Team",
            description: "Test Description"
        );
        await _repository.AddAsync(team);

        // Act
        var result = await ((ITeamMasterRepository)_repository).GetTeamNameById(team.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(team.ReferenceId, result.ReferenceId);
        Assert.Equal("Test Team", result.GroupName);
        Assert.Equal("Test Description", result.Description);
    }

    [Fact]
    public async Task GetTeamNameById_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await ((ITeamMasterRepository)_repository).GetTeamNameById(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetTeamNameByName Tests

    [Fact]
    public async Task GetTeamNameByName_ShouldReturnTeam_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(
            groupName: "Development Team",
            description: "Dev Team Description",
            isActive: true
        );
        await _repository.AddAsync(team);

        // Act
        var result = await _repository.GetTeamNameByName("Development Team");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Development Team", result.GroupName);
        Assert.Equal("Dev Team Description", result.Description);
        Assert.True(result.IsActive);
    }

    [Fact]
    public async Task GetTeamNameByName_ShouldReturnNull_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(groupName: "Existing Team", isActive: true);
        await _repository.AddAsync(team);

        // Act
        var result = await _repository.GetTeamNameByName("Non-Existent Team");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetTeamNameByName_ShouldReturnNull_WhenTeamIsInactive()
    {
        // Arrange
        await ClearDatabase();

        var inactiveTeam = _fixture.CreateTeamMaster(
            groupName: "Inactive Team",
            isActive: false
        );
        await _dbContext.TeamMasters.AddRangeAsync(inactiveTeam);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetTeamNameByName("Inactive Team");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetTeamNameByName_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(groupName: "TestTeam", isActive: true);
        await _repository.AddAsync(team);

        // Act
        var result1 = await _repository.GetTeamNameByName("TestTeam");
        var result2 = await _repository.GetTeamNameByName("testteam");
        var result3 = await _repository.GetTeamNameByName("TESTTEAM");

        // Assert
        Assert.NotNull(result1);
        Assert.Null(result2);
        Assert.Null(result3);
    }

    #endregion

    #region GetTeamMasterNames Tests

    [Fact]
    public async Task GetTeamMasterNames_ShouldReturnAllTeams_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var team1 = _fixture.CreateTeamMaster(groupName: "Team Z", description: "COMPANY_123", isActive: true);
        var team2 = _fixture.CreateTeamMaster(groupName: "Team A", description: "COMPANY_456", isActive: true);
        var team3 = _fixture.CreateTeamMaster(groupName: "Team M", description: "COMPANY_789", isActive: true);

        await _repository.AddAsync(team1);
        await _repository.AddAsync(team2);
        await _repository.AddAsync(team3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetTeamMasterNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        
        // Should be ordered by GroupName
        Assert.Equal("Team A", result[0].GroupName);
        Assert.Equal("Team M", result[1].GroupName);
        Assert.Equal("Team Z", result[2].GroupName);
        
        // Should only return ReferenceId and GroupName
        Assert.All(result, team => 
        {
            Assert.NotNull(team.ReferenceId);
            Assert.NotNull(team.GroupName);
            Assert.Null(team.Description);
        });
    }

    [Fact]
    public async Task GetTeamMasterNames_ShouldReturnCompanyTeams_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var team1 = _fixture.CreateTeamMaster(groupName: "Team C", description: "COMPANY_123", isActive: true);
        var team2 = _fixture.CreateTeamMaster(groupName: "Team A", description: "COMPANY_456", isActive: true);
        var team3 = _fixture.CreateTeamMaster(groupName: "Team B", description: "COMPANY_123", isActive: true);

        await _repository.AddAsync(team1);
        await _repository.AddAsync(team2);
        await _repository.AddAsync(team3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetTeamMasterNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        
        // Should be ordered by GroupName
        Assert.Equal("Team B", result[0].GroupName);
        Assert.Equal("Team C", result[1].GroupName);
        
        // Should only return teams for the user's company
        Assert.All(result, team => 
        {
            Assert.NotNull(team.ReferenceId);
            Assert.NotNull(team.GroupName);
            Assert.Null(team.Description);
        });
    }

    [Fact]
    public async Task GetTeamMasterNames_ShouldReturnEmpty_WhenNoTeamsForCompany()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(groupName: "Team A", description: "COMPANY_456", isActive: true);
        await _repository.AddAsync(team);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetTeamMasterNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetTeamMasterNames_ShouldOnlyReturnActiveTeams()
    {
        // Arrange
        await ClearDatabase();

        var activeTeam = _fixture.CreateTeamMaster(groupName: "Active Team", description: "COMPANY_123", isActive: true);
        var inactiveTeam = _fixture.CreateTeamMaster(groupName: "Inactive Team", description: "COMPANY_123", isActive: false);

        await _dbContext.TeamMasters.AddRangeAsync(activeTeam, inactiveTeam);
        _dbContext.SaveChanges();

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetTeamMasterNames();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Active Team", result[0].GroupName);
    }

    #endregion

    #region IsTeamMasterNameExist Tests

    [Fact]
    public async Task IsTeamMasterNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(groupName: "Existing Team");
        await _repository.AddAsync(team);

        // Act
        var result = await _repository.IsTeamMasterNameExist("Existing Team", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTeamMasterNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(groupName: "Existing Team");
        await _repository.AddAsync(team);

        // Act
        var result = await _repository.IsTeamMasterNameExist("Non-Existent Team", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTeamMasterNameExist_ShouldReturnFalse_WhenNameExistsButSameId()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(groupName: "Test Team");
        await _repository.AddAsync(team);

        // Act
        var result = await _repository.IsTeamMasterNameExist("Test Team", team.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTeamMasterNameExist_ShouldReturnTrue_WhenNameExistsWithDifferentId()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(groupName: "Test Team");
        await _repository.AddAsync(team);

        // Act
        var result = await _repository.IsTeamMasterNameExist("Test Team", Guid.NewGuid().ToString());

        // Assert
        Assert.True(result);
    }

    #endregion

    #region IsTeamMasterNameUnique Tests

    [Fact]
    public async Task IsTeamMasterNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(groupName: "Existing Team");
        await _repository.AddAsync(team);

        // Act
        var result = await _repository.IsTeamMasterNameUnique("Existing Team");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTeamMasterNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(groupName: "Existing Team");
        await _repository.AddAsync(team);

        // Act
        var result = await _repository.IsTeamMasterNameUnique("Non-Existent Team");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsTeamMasterNameExist (Single Parameter) Tests

    [Fact]
    public async Task IsTeamMasterNameExist_SingleParam_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(groupName: "Test Team");
        await _repository.AddAsync(team);

        // Act
        var result = await _repository.IsTeamMasterNameExist("Test Team");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTeamMasterNameExist_SingleParam_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(groupName: "Existing Team");
        await _repository.AddAsync(team);

        // Act
        var result = await _repository.IsTeamMasterNameExist("Non-Existent Team");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTeamMasterNameExist_SingleParam_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();

        var team = _fixture.CreateTeamMaster(groupName: "TestTeam");
        await _repository.AddAsync(team);

        // Act
        var result1 = await _repository.IsTeamMasterNameExist("TestTeam");
        var result2 = await _repository.IsTeamMasterNameExist("testteam");

        // Assert
        Assert.True(result1);
        Assert.False(result2);
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task TeamMasterRepository_ShouldHandleComplexScenarios()
    {
        // Arrange
        await ClearDatabase();

        var team1 = _fixture.CreateTeamMaster(
            groupName: "Development Team",
            description: "COMPANY_123",
            isActive: true
        );
        var team2 = _fixture.CreateTeamMaster(
            groupName: "QA Team",
            description: "COMPANY_123",
            isActive: true
        );
        var team3 = _fixture.CreateTeamMaster(
            groupName: "DevOps Team",
            description: "COMPANY_456",
            isActive: true
        );

        await _repository.AddAsync(team1);
        await _repository.AddAsync(team2);
        await _repository.AddAsync(team3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act & Assert
        var allTeams = await _repository.GetAllTeamNames();
        Assert.Equal(3, allTeams.Count);

        var companyTeams = await _repository.GetTeamMasterNames();
        Assert.Equal(2, companyTeams.Count);

        var devTeam = await _repository.GetTeamNameByName("Development Team");
        Assert.NotNull(devTeam);
        Assert.Equal("Development Team", devTeam.GroupName);

        var teamById = await ((ITeamMasterRepository)_repository).GetTeamNameById(team1.ReferenceId);
        Assert.NotNull(teamById);
        Assert.Equal(team1.ReferenceId, teamById.ReferenceId);

        var nameExists = await _repository.IsTeamMasterNameExist("Development Team");
        Assert.True(nameExists);

        var nameUnique = await _repository.IsTeamMasterNameUnique("Development Team");
        Assert.True(nameUnique);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.TeamMasters.RemoveRange(_dbContext.TeamMasters);
        await _dbContext.SaveChangesAsync();
    }
}
