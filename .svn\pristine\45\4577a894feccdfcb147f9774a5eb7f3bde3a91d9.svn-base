﻿namespace ContinuityPatrol.Application.Features.IncidentDaily.Commands.Delete;

public class
    DeleteIncidentDailyCommandHandler : IRequestHandler<DeleteIncidentDailyCommand, DeleteIncidentDailyResponse>
{
    private readonly IIncidentDailyRepository _incidentDailyRepository;
    private readonly IMapper _mapper;

    public DeleteIncidentDailyCommandHandler(IIncidentDailyRepository incidentDailyRepository, IMapper mapper)
    {
        _incidentDailyRepository = incidentDailyRepository;
        _mapper = mapper;
    }

    public async Task<DeleteIncidentDailyResponse> Handle(DeleteIncidentDailyCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _incidentDailyRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.IncidentDaily),
            new NotFoundException(nameof(Domain.Entities.IncidentDaily), request.Id));

        eventToDelete.IsActive = false;

        await _incidentDailyRepository.UpdateAsync(eventToDelete);

        var response = new DeleteIncidentDailyResponse
        {
            Message = Message.Delete(nameof(Incident), eventToDelete.ParentBusinessServiceName),

            IsActive = eventToDelete.IsActive
        };

        return response;
    }
}