using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RsyncJobRepositoryTests : IClassFixture<RsyncJobFixture>
{
    private readonly RsyncJobFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly RsyncJobRepository _repository;

    public RsyncJobRepositoryTests(RsyncJobFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new RsyncJobRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddRsyncJob_WhenValidEntity()
    {
        // Arrange
        var rsyncJob = _fixture.RsyncJobDto;
        rsyncJob.ReplicationId = "REPL_TEST_001";
        rsyncJob.ReplicationName = "Test Replication Job";
        rsyncJob.ReplicationTypeId = "REPL_TYPE_001";
        rsyncJob.ReplicationType = "Rsync";
        rsyncJob.SiteId = "SITE_TEST_001";
        rsyncJob.SiteName = "Test Site";
        rsyncJob.SourceDirectory = "/home/<USER>";
        rsyncJob.DestinationDirectory = "/home/<USER>";
        rsyncJob.ModeType = "Incremental";
        rsyncJob.RsyncOptionId = Guid.NewGuid().ToString();
        rsyncJob.Properties = "{\"option1\":\"value1\",\"option2\":\"value2\"}";
        rsyncJob.JobProperties = "{\"jobProp1\":\"jobValue1\"}";
        rsyncJob.ScheduleProperties = "{\"schedule\":\"daily\"}";

        // Act
        var result = await _repository.AddAsync(rsyncJob);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(rsyncJob.ReplicationId, result.ReplicationId);
        Assert.Equal(rsyncJob.ReplicationName, result.ReplicationName);
        Assert.Equal(rsyncJob.ReplicationTypeId, result.ReplicationTypeId);
        Assert.Equal(rsyncJob.ReplicationType, result.ReplicationType);
        Assert.Equal(rsyncJob.SiteId, result.SiteId);
        Assert.Equal(rsyncJob.SiteName, result.SiteName);
        Assert.Equal(rsyncJob.SourceDirectory, result.SourceDirectory);
        Assert.Equal(rsyncJob.DestinationDirectory, result.DestinationDirectory);
        Assert.Equal(rsyncJob.ModeType, result.ModeType);
        Assert.Equal(rsyncJob.RsyncOptionId, result.RsyncOptionId);
        Assert.Equal(rsyncJob.Properties, result.Properties);
        Assert.Equal(rsyncJob.JobProperties, result.JobProperties);
        Assert.Equal(rsyncJob.ScheduleProperties, result.ScheduleProperties);
        Assert.Single(_dbContext.RsyncJobs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var rsyncJob = _fixture.RsyncJobDto;
        _dbContext.RsyncJobs.Add(rsyncJob);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(rsyncJob.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(rsyncJob.Id, result.Id);
        Assert.Equal(rsyncJob.ReplicationId, result.ReplicationId);
        Assert.Equal(rsyncJob.ReplicationName, result.ReplicationName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var rsyncJob = _fixture.RsyncJobDto;
        _dbContext.RsyncJobs.Add(rsyncJob);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(rsyncJob.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(rsyncJob.ReferenceId, result.ReferenceId);
        Assert.Equal(rsyncJob.ReplicationId, result.ReplicationId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var rsyncJobs = _fixture.RsyncJobList;
        _dbContext.RsyncJobs.AddRange(rsyncJobs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(rsyncJobs.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntitiesExist()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_WhenExists()
    {
        // Arrange
        var rsyncJob = _fixture.RsyncJobDto;
        _dbContext.RsyncJobs.Add(rsyncJob);
        await _dbContext.SaveChangesAsync();

        var updatedReplicationName = "Updated Replication Name";
        var updatedModeType = "Full";
        rsyncJob.ReplicationName = updatedReplicationName;
        rsyncJob.ModeType = updatedModeType;

        // Act
        var result = await _repository.UpdateAsync(rsyncJob);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(updatedReplicationName, result.ReplicationName);
        Assert.Equal(updatedModeType, result.ModeType);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity_WhenExists()
    {
        // Arrange
        var rsyncJob = _fixture.RsyncJobDto;
        _dbContext.RsyncJobs.Add(rsyncJob);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(rsyncJob);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(rsyncJob.Id);
        Assert.Null(deletedEntity);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleEntities()
    {
        // Arrange
        var rsyncJobs = new List<RsyncJob>
        {
            _fixture.CreateRsyncJobWithReplicationId("REPL_001"),
            _fixture.CreateRsyncJobWithReplicationId("REPL_002"),
            _fixture.CreateRsyncJobWithReplicationId("REPL_003")
        };

        // Act
        var result = await _repository.AddRangeAsync(rsyncJobs);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count());

        var savedEntities = await _repository.ListAllAsync();
        Assert.Equal(3, savedEntities.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    [Fact]
    public async Task Repository_ShouldHandleComplexProperties()
    {
        // Arrange
        var rsyncJob = _fixture.CreateRsyncJobWithProperties(
            replicationId: "COMPLEX_REPL_001",
            replicationName: "Complex Replication Job",
            sourceDirectory: "/complex/source/path/with spaces",
            destinationDirectory: "/complex/destination/path/with@special#characters",
            properties: "{\"complexProperty1\":\"complexValue1\",\"complexProperty2\":\"complexValue2\"}",
            jobProperties: "{\"jobComplexProp1\":\"jobComplexValue1\"}",
            scheduleProperties: "{\"schedule\":\"0 0 * * *\",\"timezone\":\"UTC\"}"
        );

        // Act
        var result = await _repository.AddAsync(rsyncJob);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(rsyncJob.Properties, result.Properties);
        Assert.Equal(rsyncJob.JobProperties, result.JobProperties);
        Assert.Equal(rsyncJob.ScheduleProperties, result.ScheduleProperties);
        Assert.Equal(rsyncJob.SourceDirectory, result.SourceDirectory);
        Assert.Equal(rsyncJob.DestinationDirectory, result.DestinationDirectory);
    }

    [Fact]
    public async Task Repository_ShouldHandleEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        var allJobs = await _repository.ListAllAsync();
        Assert.Empty(allJobs);

        var nonExistentJob = await _repository.GetByIdAsync(999);
        Assert.Null(nonExistentJob);

        var nonExistentByReference = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());
        Assert.Null(nonExistentByReference);
    }

    [Theory]
    [InlineData("Incremental")]
    [InlineData("Full")]
    [InlineData("Differential")]
    [InlineData("Mirror")]
    public async Task Repository_ShouldHandleDifferentModeTypes(string modeType)
    {
        // Arrange
        var rsyncJob = _fixture.CreateRsyncJobWithProperties(modeType: modeType);

        // Act
        var result = await _repository.AddAsync(rsyncJob);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(modeType, result.ModeType);
    }

    [Theory]
    [InlineData("Rsync")]
    [InlineData("RoboCopy")]
    [InlineData("DataSync")]
    [InlineData("FastCopy")]
    public async Task Repository_ShouldHandleDifferentReplicationTypes(string replicationType)
    {
        // Arrange
        var rsyncJob = _fixture.CreateRsyncJobWithProperties(replicationType: replicationType);

        // Act
        var result = await _repository.AddAsync(rsyncJob);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(replicationType, result.ReplicationType);
    }

    [Theory]
    [InlineData("/path with spaces/")]
    [InlineData("/path@#$%/")]
    [InlineData("/path_with_underscores/")]
    [InlineData("/path-with-dashes/")]
    public async Task Repository_ShouldHandleSpecialCharactersInPaths(string path)
    {
        // Arrange
        var rsyncJob = _fixture.CreateRsyncJobWithDirectories(path, path + "destination");

        // Act
        var result = await _repository.AddAsync(rsyncJob);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(path, result.SourceDirectory);
        Assert.Equal(path + "destination", result.DestinationDirectory);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.RsyncJobs.RemoveRange(_dbContext.RsyncJobs);
        await _dbContext.SaveChangesAsync();
    }
}
