﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class TableAccessRepositoryMocks
{
    public static Mock<ITableAccessRepository> CreateTableAccessRepository(List<TableAccess> tableAccesses)
    {
        var mockTableAccessRepository = new Mock<ITableAccessRepository>();

        mockTableAccessRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(tableAccesses);

        mockTableAccessRepository.Setup(repo => repo.AddAsync(It.IsAny<TableAccess>())).ReturnsAsync(
            (TableAccess tableAccess) =>
            {
                tableAccess.Id = new Fixture().Create<int>();

                tableAccess.ReferenceId = new Fixture().Create<Guid>().ToString();

                tableAccesses.Add(tableAccess);

                return tableAccess;
            });

        return mockTableAccessRepository;
    }

    public static Mock<ITableAccessRepository> UpdateTableAccessRepository(List<TableAccess> tableAccesses)
    {
        var mockTableAccessRepository = new Mock<ITableAccessRepository>();

        mockTableAccessRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(tableAccesses);

        mockTableAccessRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => tableAccesses.SingleOrDefault(x => x.ReferenceId == i));

        mockTableAccessRepository.Setup(repo => repo.UpdateAsync(It.IsAny<TableAccess>())).ReturnsAsync((TableAccess tableAccess) =>
        {
            var index = tableAccesses.FindIndex(item => item.Id == tableAccess.Id);

            tableAccesses[index] = tableAccess;

            return tableAccess;
        });

        return mockTableAccessRepository;
    }

    public static Mock<ITableAccessRepository> DeleteTableAccessRepository(List<TableAccess> tableAccesses)
    {
        var mockTableAccessRepository = new Mock<ITableAccessRepository>();

        mockTableAccessRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(tableAccesses);

        mockTableAccessRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => tableAccesses.SingleOrDefault(x => x.ReferenceId == i));

        mockTableAccessRepository.Setup(repo => repo.UpdateAsync(It.IsAny<TableAccess>())).ReturnsAsync((TableAccess tableAccess) =>
        {
            var index = tableAccesses.FindIndex(item => item.Id == tableAccess.Id);

            tableAccess.IsActive = false;

            tableAccesses[index] = tableAccess;

            return tableAccess;
        });

        return mockTableAccessRepository;
    }

    public static Mock<ITableAccessRepository> GetTableAccessRepository(List<TableAccess> tableAccesses)
    {
        var mockTableAccessRepository = new Mock<ITableAccessRepository>();

        mockTableAccessRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(tableAccesses);

        mockTableAccessRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => tableAccesses.SingleOrDefault(x => x.ReferenceId == i));

        return mockTableAccessRepository;
    }

    public static Mock<ITableAccessRepository> GetTableAccessNamesRepository(List<TableAccess> tableAccesses)
    {
        var mockTableAccessRepository = new Mock<ITableAccessRepository>();

        mockTableAccessRepository.Setup(repo => repo.GetTableAccessNames()).ReturnsAsync(tableAccesses);

        return mockTableAccessRepository;
    }

    public static Mock<ITableAccessRepository> GetTableAccessNameUniqueRepository(List<TableAccess> tableAccesses)
    {
        var mockTableAccessRepository = new Mock<ITableAccessRepository>();

        mockTableAccessRepository.Setup(repo => repo.IsTableAccessNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => tableAccesses.Exists(x => x.TableName == i && x.ReferenceId == j));

        return mockTableAccessRepository;
    }

    public static Mock<ITableAccessRepository> GetTableAccessEmptyRepository()
    {
        var mockTableAccessRepository = new Mock<ITableAccessRepository>();

        mockTableAccessRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<TableAccess>());

        return mockTableAccessRepository;
    }

    public static Mock<ITableAccessRepository> GetPaginatedTableAccessRepository(List<TableAccess> tableAccesses)
    {
        var mockTableAccessRepository = new Mock<ITableAccessRepository>();

        var queryableTableAccess = tableAccesses.BuildMock();

        mockTableAccessRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableTableAccess);

        return mockTableAccessRepository;
    }

    public static Mock<ITableAccessRepository> GetSchemaNameRepository(List<TableAccess> tableAccesses)
    {
        var mockTableAccessRepository = new Mock<ITableAccessRepository>();

        mockTableAccessRepository.Setup(repo => repo.GetSchemaNameList()).ReturnsAsync(tableAccesses);

        return mockTableAccessRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateTableAccessEventRepository(List<UserActivity> userActivities)
    {
        var tableAccessEventRepository = new Mock<IUserActivityRepository>();

        tableAccessEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return tableAccessEventRepository;
    }
}