﻿using ContinuityPatrol.Application.Features.RsyncJob.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.RsyncJobModel;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.RsyncJob.Queries
{
    public class GetRsyncJobDetailQueryHandlerTests
    {
        private readonly Mock<IRsyncJobRepository> _mockRsyncJobRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetRsyncJobDetailQueryHandler _handler;

        public GetRsyncJobDetailQueryHandlerTests()
        {
            _mockRsyncJobRepository = new Mock<IRsyncJobRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetRsyncJobDetailQueryHandler(_mockRsyncJobRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ReturnsRsyncJobDetailVm_WhenRsyncJobExists()
        {
            var rsyncJobId = Guid.NewGuid().ToString();
            var rsyncJob = new Domain.Entities.RsyncJob
            {
                Id = 1,
                ReplicationName = "Test Job"
            };
            var rsyncJobDetailVm = new RsyncJobDetailVm
            {
                Id = rsyncJobId,
                ReplicationName = "Test Job"
            };

            _mockRsyncJobRepository
                .Setup(repo => repo.GetByReferenceIdAsync(rsyncJobId))
                .ReturnsAsync(rsyncJob);

            _mockMapper
                .Setup(mapper => mapper.Map<RsyncJobDetailVm>(rsyncJob))
                .Returns(rsyncJobDetailVm);

            var query = new GetRsyncJobDetailQuery { Id = rsyncJobId };

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(rsyncJobDetailVm.Id, result.Id);
            Assert.Equal(rsyncJobDetailVm.ReplicationName, result.ReplicationName);

            _mockRsyncJobRepository.Verify(repo => repo.GetByReferenceIdAsync(rsyncJobId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<RsyncJobDetailVm>(rsyncJob), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenRsyncJobDoesNotExist()
        {
            var rsyncJobId = Guid.NewGuid().ToString();

            _mockRsyncJobRepository
                .Setup(repo => repo.GetByReferenceIdAsync(rsyncJobId))
                .ReturnsAsync((Domain.Entities.RsyncJob)null);

            var query = new GetRsyncJobDetailQuery { Id = rsyncJobId };

            var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(query, CancellationToken.None)
            );

            Assert.Equal($"Entity \"RsyncJob\" ({rsyncJobId}) was not found.", exception.Message);

            _mockRsyncJobRepository.Verify(repo => repo.GetByReferenceIdAsync(rsyncJobId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<RsyncJobDetailVm>(It.IsAny<Domain.Entities.RsyncJob>()), Times.Never);
        }
    }
}
