﻿@model ContinuityPatrol.Application.Features.WorkflowProfile.Commands.UpdateWorkflowProfilePassword.UpdateWorkflowProfilePasswordCommand
@using ContinuityPatrol.Shared.Services.Helper

<div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel">
    <div class="modal-content">
        <form id="CreateForm" asp-controller="WorkflowProfileManagement" asp-action="ProfileChangePassword" method="post" enctype="multipart/form-data">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-lock"></i><span>Change Workflow Profile Password</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <div class="form-label">Current Password</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-lock"></i></span>
                        <input type="hidden" id="loginName" data-loginnames="@WebHelper.UserSession.LoginName">

                        <input type="hidden" asp-for="Id" id="loginId">
                        <input type="password" asp-for="OldPassword" maxlength="30" autocomplete="off" class="form-control" placeholder="Enter Current Password" id="CurrentPassword" />
                        <span role="button" class="input-group-text toggle-password"><i class="cp-password-visible fs-6"></i></span>
                    </div>
                    <span asp-validation-for="OldPassword" id="OldPassword-error"></span>
                </div>
                <div class="form-group">
                    <div class="form-label">New Password</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-lock"></i></span>
                        <input type="password" id="Password" asp-for="NewPassword" class="form-control" maxlength="30" autocomplete="off" placeholder="Enter New Password" />
                        <span role="button" class="input-group-text toggle-password"><i class="cp-password-visible fs-6"></i></span>
                    </div>
                    <span asp-validation-for="NewPassword" id="NewPassword-error"></span>
                </div>

                <div class="form-group">
                    <div class="form-label">Confirm Password</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-lock"></i></span>
                        <input type="password" asp-for="ConfirmPassword" class="form-control" maxlength="30" autocomplete="off" placeholder="Enter Confirm Password" id="ConfirmPassword" />
                    </div>
                    <span asp-validation-for="ConfirmPassword" id="ConfirmPassword-error"></span>
                </div>

            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm" id="SaveFunction">Change</button>
                </div>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>


