using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class FastCopyMonitorRepositoryTests : IClassFixture<FastCopyMonitorFixture>, IDisposable
{
    private readonly FastCopyMonitorFixture _fastCopyMonitorFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly FastCopyMonitorRepository _repository;

    public FastCopyMonitorRepositoryTests(FastCopyMonitorFixture fastCopyMonitorFixture)
    {
        _fastCopyMonitorFixture = fastCopyMonitorFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new FastCopyMonitorRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region GetByDataSyncJobIds Tests

    [Fact]
    public async Task GetByDataSyncJobIds_ReturnsMatchingMonitors_WhenValidJobIds()
    {
        // Arrange
        var jobId1 = "JOB_001";
        var jobId2 = "JOB_002";
        var jobId3 = "JOB_003";
        var searchJobIds = new List<string> { jobId1, jobId2 };

        _fastCopyMonitorFixture.FastCopyMonitorList[0].DataSyncJobId = jobId1;
        _fastCopyMonitorFixture.FastCopyMonitorList[1].DataSyncJobId = jobId2;
        _fastCopyMonitorFixture.FastCopyMonitorList[2].DataSyncJobId = jobId3;

        await _dbContext.FastCopyMonitors.AddRangeAsync(_fastCopyMonitorFixture.FastCopyMonitorList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByDataSyncJobIds(searchJobIds);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.DataSyncJobId == jobId1);
        Assert.Contains(result, x => x.DataSyncJobId == jobId2);
        Assert.DoesNotContain(result, x => x.DataSyncJobId == jobId3);
    }

    [Fact]
    public async Task GetByDataSyncJobIds_ReturnsEmpty_WhenNoMatchingJobIds()
    {
        // Arrange
        var nonExistentJobIds = new List<string> { "NON_EXISTENT_1", "NON_EXISTENT_2" };

        _fastCopyMonitorFixture.FastCopyMonitorList[0].DataSyncJobId = "JOB_001";
        _fastCopyMonitorFixture.FastCopyMonitorList[1].DataSyncJobId = "JOB_002";

        await _dbContext.FastCopyMonitors.AddRangeAsync(_fastCopyMonitorFixture.FastCopyMonitorList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByDataSyncJobIds(nonExistentJobIds);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByDataSyncJobIds_ReturnsEmpty_WhenEmptyJobIdsList()
    {
        // Arrange
        var emptyJobIds = new List<string>();

        await _dbContext.FastCopyMonitors.AddRangeAsync(_fastCopyMonitorFixture.FastCopyMonitorList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByDataSyncJobIds(emptyJobIds);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByDataSyncJobIds_IncludesInactiveRecords_WhenInactiveRecordsExist()
    {
        // Arrange
        var jobId = "JOB_001";
        var searchJobIds = new List<string> { jobId };

        // Create fresh monitors to avoid fixture interference
        var activeMonitor = new Domain.Entities.FastCopyMonitor
        {
            DataSyncJobId = jobId,
            ReferenceId = Guid.NewGuid().ToString(),
            SourceIP = "***********",
            DestinationIP = "***********"
        };

        var inactiveMonitor = new Domain.Entities.FastCopyMonitor
        {
            DataSyncJobId = jobId,
            ReferenceId = Guid.NewGuid().ToString(),
            SourceIP = "***********",
            DestinationIP = "***********"
        };

        await _dbContext.FastCopyMonitors.AddRangeAsync(new[] { activeMonitor, inactiveMonitor });
        await _dbContext.SaveChangesAsync();

        // Manually set one record as inactive after saving (since SaveChangesAsync sets IsActive = true)
        inactiveMonitor.IsActive = false;
        _dbContext.FastCopyMonitors.Update(inactiveMonitor);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByDataSyncJobIds(searchJobIds);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.IsActive == true);
        Assert.Contains(result, x => x.IsActive == false);
    }

    [Fact]
    public async Task GetByDataSyncJobIds_HandlesSingleJobId_WhenOnlyOneJobIdProvided()
    {
        // Arrange
        var jobId = "SINGLE_JOB";
        var singleJobIdList = new List<string> { jobId };

        _fastCopyMonitorFixture.FastCopyMonitorList[0].DataSyncJobId = jobId;
        _fastCopyMonitorFixture.FastCopyMonitorList[1].DataSyncJobId = "OTHER_JOB";

        await _dbContext.FastCopyMonitors.AddRangeAsync(_fastCopyMonitorFixture.FastCopyMonitorList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByDataSyncJobIds(singleJobIdList);

        // Assert
        Assert.Single(result);
        Assert.Equal(jobId, result[0].DataSyncJobId);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddFastCopyMonitor_WhenValidMonitor()
    {
        // Arrange
        var monitor = _fastCopyMonitorFixture.FastCopyMonitorDto;
        monitor.DataSyncJobId = "TEST_JOB";

        // Act
        var result = await _repository.AddAsync(monitor);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitor.DataSyncJobId, result.DataSyncJobId);
        Assert.Single(_dbContext.FastCopyMonitors);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenMonitorIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsMonitor_WhenExists()
    {
        // Arrange
        _fastCopyMonitorFixture.FastCopyMonitorDto.Id = 1;
        await _dbContext.FastCopyMonitors.AddAsync(_fastCopyMonitorFixture.FastCopyMonitorDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_fastCopyMonitorFixture.FastCopyMonitorDto.Id, result.Id);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsAllActiveMonitors_WhenMonitorsExist()
    {
        // Arrange
        await _dbContext.FastCopyMonitors.AddRangeAsync(_fastCopyMonitorFixture.FastCopyMonitorList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateMonitor_WhenValidMonitor()
    {
        // Arrange
        _dbContext.FastCopyMonitors.Add(_fastCopyMonitorFixture.FastCopyMonitorDto);
        await _dbContext.SaveChangesAsync();

        _fastCopyMonitorFixture.FastCopyMonitorDto.DataSyncJobId = "UPDATED_JOB";

        // Act
        var result = await _repository.UpdateAsync(_fastCopyMonitorFixture.FastCopyMonitorDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UPDATED_JOB", result.DataSyncJobId);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenMonitorIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion
}
