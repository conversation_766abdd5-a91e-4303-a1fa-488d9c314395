﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ApprovalMatrix.Events.Update;

public class ApprovalMatrixUpdatedEventHandler : INotificationHandler<ApprovalMatrixUpdatedEvent>
{
    private readonly ILogger<ApprovalMatrixUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ApprovalMatrixUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<ApprovalMatrixUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ApprovalMatrixUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.ApprovalMatrix}",
            Entity = Modules.ApprovalMatrix.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Approval matrix '{updatedEvent.UserName}' updated successfully."
        };
        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation($"Approval matrix '{updatedEvent.UserName}' updated successfully.");
    }
}