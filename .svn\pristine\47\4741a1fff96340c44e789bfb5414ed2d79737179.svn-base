﻿@model ContinuityPatrol.Domain.ViewModels.NodeModel.NodeViewModel
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<link href="~/lib/formeo/formeo.min.css" rel="stylesheet" />

<style>
    .f-checkbox {
        display: flex;
        align-items: center;
    }

    .wizard > .content > .body label {
        margin-bottom: -1px;
    }
</style>

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-network"></i><span> Node</span></h6>
            <form class="d-flex">
                @* <div class="input-group ">
                <div class="input-group me-2" style="width: 350px !important;">
                <span class="input-group-text"><i class="cp-activity-type"></i></span>
                <select class="form-select w-100" data-placeholder="Select Type">
                <option></option>
                <option value="all">All</option>
                <option value="Node1">Node1</option>
                <option value="Node2">Node2</option>
                <option value="Node3">Node3</option>
                <option value="Node4">Node4</option>
                <option value="Node5">Node5</option>
                </select>
                </div>
                </div> *@
                <div class="input-group mx-2 w-100">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown"  title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="name=" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="serverName=" id="CompanyName">
                                        <label class="form-check-label" for="CompanyName">
                                            Server
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" id="createBtn" class="btn btn-primary btn-sm " data-bs-toggle="modal" data-bs-target="#CreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="datatablelist" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Name</th>
                        <th>Server</th>
                        <th>Oracle SID</th>
                        <th class="Action-th">Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div id="configurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>

<div id="configurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<!--Modal Create-->
<div class="modal fade " id="CreateModal" data-bs-backdrop="static" aria-labelledby="configureModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>
<!-- Delete -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="Delete" />
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<script src="~/lib/formeo/formeo.min.js"></script>

<script src="~/js/configuration/infra components/node/node.js"></script>
<script src="~/js/configuration/infra components/form builder/formbuildervalidation.js"></script>
<script src="~/js/configuration/infra components/form builder/formbuildercommonfunctions.js"></script>
