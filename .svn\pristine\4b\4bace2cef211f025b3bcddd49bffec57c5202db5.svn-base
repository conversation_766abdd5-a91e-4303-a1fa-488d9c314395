﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;


namespace ContinuityPatrol.Persistence.Repositories;

public class InfraObjectRepository : BaseRepository<InfraObject>, IInfraObjectRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public InfraObjectRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<InfraObject>> ListAllAsync()
    {
        var infraObjects =
            base.ListAllAsync(infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId));

        var infraObject = MapInfraObjects(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject);
    }

    public override async Task<InfraObject> GetByReferenceIdAsync(string id)
    {
        var infraObject = base.GetByReferenceIdAsync(id,
            infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                           infraObject.ReferenceId.Equals(id));

        var infraObjects = MapInfraObjects(infraObject);

        return _loggedInUserService.IsAllInfra
            ? await infraObjects.FirstOrDefaultAsync()
            : GetInfraObjectByReferenceId(infraObjects.FirstOrDefault());
    }

    public  async Task<IReadOnlyList<InfraObject>> GetByReferenceIdsAsync(List<string> ids)
    {
        var infraObject = _loggedInUserService.IsParent
            ? Entities.AsNoTracking().DescOrderById().Where(x => ids.Contains(x.ReferenceId))
            :Entities.AsNoTracking().DescOrderById().Where(x => ids.Contains(x.ReferenceId) && x.CompanyId.Equals(_loggedInUserService.CompanyId));

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject);
    }

    public async Task<InfraObject> GetInfraObjectByName(string name)
    {
        return await base.FilterBy(x => x.Name.ToLower().Equals(name.ToLower())).FirstOrDefaultAsync();
    }

    public async Task<List<InfraObject>> GetInfraObjectNames()
    {
        var infraObjects = base
            .ListAllAsync(infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new InfraObject { ReferenceId = x.ReferenceId, Name = x.Name });

        return _loggedInUserService.IsAllInfra
            ? await infraObjects.ToListAsync()
            : GetAssignedInfraObjects(infraObjects).ToList();
    }

    public async Task<List<InfraObject>> GetByReplicationTypeId(List<string> replicationTypeIds)
    {
       return await base.FilterBy(x => replicationTypeIds.Contains(x.ReplicationTypeId))
            .ToListAsync();
    }

    public async Task<List<InfraObject>> GetByReplicationCategoryTypeId(string replicationCategoryTypeId)
    {
        return await base.FilterBy(x => x.ReplicationCategoryTypeId.Equals(replicationCategoryTypeId))
            .ToListAsync();
    }


    public async Task<List<InfraObject>> GetByBusinessFunctionIds(List<string> businessFunctionIds)
    {
        var filteredQuery = _loggedInUserService.IsParent
            ? base.FilterBy(x => businessFunctionIds.Contains(x.BusinessFunctionId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && businessFunctionIds.Contains(x.BusinessFunctionId));

        var infraObjects = filteredQuery.Select(x => new InfraObject
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Name = x.Name,
            BusinessFunctionId = x.BusinessFunctionId,
            ServerProperties = x.ServerProperties,
            DatabaseProperties = x.DatabaseProperties,
            ReplicationProperties = x.ReplicationProperties,
            ReplicationTypeId = x.ReplicationTypeId,
            ReplicationTypeName = x.ReplicationTypeName,
            //PRReplicationId = x.PRReplicationId,
            //PRReplicationName = x.PRReplicationName,
            ReplicationCategoryType = x.ReplicationCategoryType,
            SubType = x.SubType,
            State=x.State
        });

        var orderedInfraObjects = infraObjects.OrderBy(x => x.Id);

        if (_loggedInUserService.IsAllInfra)
        {
            return await orderedInfraObjects.ToListAsync();
        }

        var assignedInfraObjects = GetAssignedInfraObjects(orderedInfraObjects);
        return assignedInfraObjects.ToList();
    }


    public async Task<IReadOnlyList<InfraObject>> GetInfraObjectByBusinessFunctionId(string businessFunctionId)
    {
        var infraObjects = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessFunctionId.Equals(businessFunctionId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.BusinessFunctionId.Equals(businessFunctionId)));

        var infraObject = MapInfraObjects(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject).ToList();
    }

    public async Task<IReadOnlyList<InfraObject>> GetInfraObjectByBusinessServiceId(string businessServiceId)
    {
        var infraObjects = FilterRequiredField( _loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.BusinessServiceId.Equals(businessServiceId)));

        var infraObject = MapInfraObjects(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject).ToList();
    }

    public override IQueryable<InfraObject> PaginatedListAllAsync()
    {
        var infraObjects =
            base.ListAllAsync(infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId));

        var infraObject = MapInfraObjects(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? infraObject.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedInfraObjects(infraObject).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public async Task<List<InfraObject>> GetInfraObjectByServerId(string serverId)
    {
        var infraObjects = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ServerProperties.Contains(serverId))
            : base.FilterBy(x => x.ServerProperties.Contains(serverId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

        var infraObject = MapInfraObjects(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject).ToList();
    }

    public async Task<List<InfraObject>> GetInfraObjectByReplicationId(string replicationId)
    {
        var infraObjects = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ReplicationProperties.Contains(replicationId))
            : base.FilterBy(x => x.ReplicationProperties.Contains(replicationId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

        var infraObject = MapInfraObjects(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject).ToList();
    }

    public async Task<List<InfraObject>> GetInfraObjectByDatabaseId(string databaseId)
    {
        var infraObjects = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.DatabaseProperties.Contains(databaseId))
            : base.FilterBy(x => x.DatabaseProperties.Contains(databaseId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

        var infraObject = MapInfraObjects(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject).ToList();
    }

    public async Task<List<InfraObject>> GetInfraObjectByNodeId(string nodeId)
    {
        var infraObjects = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.NodeProperties.Contains(nodeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.NodeProperties.Contains(nodeId)));

        var infraObject = MapInfraObjects(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject).ToList();
    }

    public async Task<List<InfraObject>> GetInfraObjectListByReplicationCategoryType(string replicationCategoryTypeId)
    {
        var infraObjects = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ReplicationCategoryTypeId.Equals(replicationCategoryTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ReplicationCategoryTypeId.Equals(replicationCategoryTypeId)));

        var infraObject = MapInfraObjects(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject).ToList();
    }

    public async Task<List<InfraObject>> GetInfraStateByReferenceIds(List<string> id)
    {
        return await base.FilterBy(x=> id.Contains(x.ReferenceId))
            .Select(x=> new InfraObject
            {
                ReferenceId = x.ReferenceId,
                Name = x.Name,
                State = x.State
            }).ToListAsync();
    }

    public async Task<List<InfraObject>> GetInfraObjectListByReplicationTypeId(string replicationTypeId)
    {
        var infraObjects = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ReplicationTypeId.Equals(replicationTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ReplicationTypeId.Equals(replicationTypeId)));

        var infraObject = MapInfraObjects(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject).ToList();
    }

    public async Task<int> GetInfraObjectStateCount(string state)
    {
        var infraObjects = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.State.Trim().ToLower().Equals(state.Trim().ToLower()))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.State.Trim().ToLower().Equals(state.Trim().ToLower()));

        return _loggedInUserService.IsAllInfra
            ? await infraObjects.CountAsync()
            : GetAssignedInfraObjects(infraObjects).Count;
    }

    public async Task<IReadOnlyList<InfraObject>> GetInfraObjectByStateType(string stateType)
    {
        var infraObjects = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.State.Trim().ToLower().Equals(stateType.Trim().ToLower()))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.State.Trim().ToLower().Equals(stateType.Trim().ToLower())));

        var infraObject = MapInfraObjects(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject).ToList();
    }

    public async Task<int> GetInfraObjectByTypeNameCount(string type)
    {
        var infraObjects = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.TypeName.Trim().ToLower().Equals(type.Trim().ToLower()))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.TypeName.Trim().ToLower().Equals(type.Trim().ToLower())));

        return _loggedInUserService.IsAllInfra
            ? await infraObjects.CountAsync()
            : GetAssignedInfraObjects(infraObjects).Count;
    }

    public async Task<IReadOnlyList<InfraObject>> GetInfraObjectByTypeNameAndState(string state, string type)
    {
        var infraObjects = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.State.Trim().ToLower().Equals(state.Trim().ToLower()) && x.TypeName.Trim().ToLower().Equals(type.Trim().ToLower()))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.TypeName.Trim().ToLower().Equals(type.Trim().ToLower()) && x.State.Trim().ToLower().Equals(state.Trim().ToLower())));

        var infraObject = MapInfraObjects(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject).ToList();
    }

    public async Task<IReadOnlyList<InfraObject>> GetInfraObjectByBusinessServiceName(string businessServiceName)
    {
        var infraObjects = await FilterRequiredField(_dbContext.InfraObjects.Active()
            .Where(infra => infra.BusinessServiceName.Equals(businessServiceName))).ToListAsync();

        return _loggedInUserService.IsParent
            ? _loggedInUserService.IsAllInfra
                ? infraObjects.ToList()
                : GetAssignedInfraObjects(infraObjects.AsQueryable())
                    .Where(infra => infra.BusinessServiceName.Equals(businessServiceName)).ToList()
            : _loggedInUserService.IsAllInfra
                ? infraObjects.Where(infra => infra.CompanyId == _loggedInUserService.CompanyId).ToList()
                : GetAssignedInfraObjects(infraObjects.AsQueryable()).Where(infra =>
                    infra.BusinessServiceName.Equals(businessServiceName) &&
                    infra.CompanyId == _loggedInUserService.CompanyId).ToList();
    }
    public async Task<PaginatedResult<InfraObject>> GetPaginatedByBusinessService(string businessServiceId, int pageNumber, int pageSize, Specification<InfraObject> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await FilterRequiredField(_loggedInUserService.IsAllInfra
                ? MapInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId)).DescOrderById())
                : MapInfraObjects(GetPaginatedInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId))
                    .DescOrderById()))).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
        }
        return await FilterRequiredField(_loggedInUserService.IsAllInfra
            ? MapInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())
            : MapInfraObjects(GetPaginatedInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId) && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                    .DescOrderById()))).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
    }   
    public async Task<PaginatedResult<InfraObject>> GetPaginatedByBusinessFunction(string businessFunctionId, int pageNumber, int pageSize, Specification<InfraObject> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await FilterRequiredField(_loggedInUserService.IsAllInfra
                ? MapInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessFunctionId.Equals(businessFunctionId)).DescOrderById())
                : MapInfraObjects(GetPaginatedInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessFunctionId.Equals(businessFunctionId))
                .DescOrderById()))).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        }
        return await FilterRequiredField(_loggedInUserService.IsAllInfra
            ? MapInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessFunctionId.Equals(businessFunctionId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())
            : MapInfraObjects(GetPaginatedInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessFunctionId.Equals(businessFunctionId) && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .DescOrderById()))).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }   
    public async Task<PaginatedResult<InfraObject>> GetPaginatedByBusinessServiceAndFunction(string businessServiceId, string businessFunctionId, int pageNumber, int pageSize, Specification<InfraObject> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await FilterRequiredField(_loggedInUserService.IsAllInfra
                 ? MapInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId)
             && x.BusinessFunctionId.Equals(businessFunctionId)).DescOrderById())
             : MapInfraObjects(GetPaginatedInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId)
             && x.BusinessFunctionId.Equals(businessFunctionId)).DescOrderById()))).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
        }
        return await FilterRequiredField(_loggedInUserService.IsAllInfra
            ? MapInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId)
             && x.BusinessFunctionId.Equals(businessFunctionId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())
            : MapInfraObjects(GetPaginatedInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId)
             && x.BusinessFunctionId.Equals(businessFunctionId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()))).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);

    }    

    public override async Task<PaginatedResult<InfraObject>> PaginatedListAllAsync(int pageNumber,int pageSize,Specification<InfraObject> specification, string sortColumn, string sortOrder)
    {
         var infraObjects = FilterRequiredField(
            base.ListAllAsync(infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId)));

        var infraObject = MapInfraObjects(infraObjects);

        return await (_loggedInUserService.IsAllInfra
            ? infraObject.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedInfraObjects(infraObject).AsNoTracking().OrderByDescending(x => x.Id)).ToSortedPaginatedListAsync(pageNumber,pageSize,sortColumn,sortOrder);
        //if (_loggedInUserService.IsParent)
        //{
        //    return await FilterRequiredField(_loggedInUserService.IsAllInfra
        //         ? MapInfraObjects(Entities.Specify(specification).DescOrderById())
        //     : MapInfraObjects(GetPaginatedInfraObjects(Entities.Specify(specification)).DescOrderById())).ToPaginatedListAsync(pageNumber, pageSize);
        //}
        //return await FilterRequiredField(_loggedInUserService.IsAllInfra
        //    ? MapInfraObjects(Entities.Specify(specification).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))).DescOrderById()
        //    : MapInfraObjects(GetPaginatedInfraObjects(Entities.Specify(specification).Where(x =>x.CompanyId.Equals(_loggedInUserService.CompanyId))).DescOrderById())).ToPaginatedListAsync(pageNumber, pageSize);

        
    }
    public Task<bool> IsInfraObjectNameUnique(string name)
    {
        var matches = _dbContext.InfraObjects.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }

    public Task<bool> IsInfraObjectNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.InfraObjects.Any(e => e.Name.Equals(name)))
            : Task.FromResult(_dbContext.InfraObjects.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public async Task<Dictionary<string ,List<InfraObject>>> GetInfraObjectGroupByBusinessFunctionIds(List<string> businessFunctionIds)
    {
        var infraObjects = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => businessFunctionIds.Contains(x.BusinessFunctionId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && businessFunctionIds.Contains(x.BusinessFunctionId)));

        var infraObject = MapInfraObjects(infraObjects);

          var infraList = _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject).ToList();

        return infraList
        .GroupBy(x => x.BusinessFunctionId)
        .ToDictionary(g => g.Key, g => g.ToList());
    }

    public async Task<List<InfraObject>> GetDriftEnabledList()
    {
        var infraObjects = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x =>x.IsDrift)
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) &&x.IsDrift));

        var infraObject = MapInfraObjects(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : GetAssignedInfraObjects(infraObject).ToList();

    }
    //Filter

    private IQueryable<InfraObject> MapInfraObjects(IQueryable<InfraObject> infraObjects)
    {
        return infraObjects
        .Select(infra => new
        {
            InfraObject = infra,
            BusinessService = _dbContext.BusinessServices.Active().AsNoTracking().FirstOrDefault(bs => bs.ReferenceId.Equals(infra.BusinessServiceId)),
            BusinessFunction = _dbContext.BusinessFunctions.Active().AsNoTracking().FirstOrDefault(bf => bf.ReferenceId.Equals(infra.BusinessFunctionId)),
            //PRServer = _dbContext.Servers.Active().AsNoTracking().FirstOrDefault(server => server.ReferenceId.Equals(infra.PRServerId)),
            //DRServer = _dbContext.Servers.Active().AsNoTracking().FirstOrDefault(server => server.ReferenceId.Equals(infra.DRServerId)),
            //NearDRServer = _dbContext.Servers.Active().AsNoTracking().FirstOrDefault(server => server.ReferenceId.Equals(infra.NearDRServerId)),
            //PRDatabase = _dbContext.Databases.Active().AsNoTracking().FirstOrDefault(database => database.ReferenceId.Equals(infra.PRDatabaseId)),
            //DRDatabase = _dbContext.Databases.Active().AsNoTracking().FirstOrDefault(database => database.ReferenceId.Equals(infra.DRDatabaseId)),
            //NearDRDatabase = _dbContext.Databases.Active().AsNoTracking().FirstOrDefault(database => database.ReferenceId.Equals(infra.NearDRDatabaseId)),
            //PRReplication = _dbContext.Replications.Active().AsNoTracking().FirstOrDefault(database => database.ReferenceId.Equals(infra.PRReplicationId)),
            //DRReplication = _dbContext.Replications.Active().AsNoTracking().FirstOrDefault(database => database.ReferenceId.Equals(infra.DRReplicationId)),
            //NearDRReplication = _dbContext.Replications.Active().AsNoTracking().FirstOrDefault(database => database.ReferenceId.Equals(infra.NearDRReplicationId)),

            SubType = _dbContext.ComponentTypes.Active().AsNoTracking().FirstOrDefault(com => com.ReferenceId.Equals(infra.SubTypeId)),
            ReplicationCategoryType = _dbContext.ReplicationMasters.Active().AsNoTracking().FirstOrDefault(repCa => repCa.ReferenceId.Equals(infra.ReplicationCategoryTypeId)),
            ReplicationType = _dbContext.ComponentTypes.Active().AsNoTracking().FirstOrDefault(co => co.ReferenceId.Equals(infra.ReplicationTypeId))
        })
        .Select(result => new InfraObject
        {
            Id = result!.InfraObject!.Id,
            ReferenceId = result!.InfraObject!.ReferenceId,
            CompanyId = result!.InfraObject!.CompanyId,
            Name = result!.InfraObject!.Name,
            Description = result!.InfraObject!.Description,
            Type = result!.InfraObject!.Type,
            TypeName = result!.InfraObject!.TypeName,
            DRReady = result!.InfraObject!.DRReady,
            NearDR = result!.InfraObject!.NearDR,
            IsDrift = result!.InfraObject!.IsDrift,
            RecoveryType = result!.InfraObject!.RecoveryType,
            Priority = result!.InfraObject!.Priority,
            State = result!.InfraObject!.State,
            ReplicationStatus = result!.InfraObject!.ReplicationStatus,
            DROperationStatus = result!.InfraObject!.DROperationStatus,
            BusinessServiceId = result!.BusinessService!.ReferenceId,
            BusinessServiceName = result!.BusinessService!.Name,
            BusinessFunctionId = result!.BusinessFunction!.ReferenceId,
            BusinessFunctionName = result!.BusinessFunction!.Name,
            //PRServerId = result!.InfraObject!.PRServerId,
            //PRServerName = result!.InfraObject!.PRServerName,
            //DRServerId = result!.InfraObject!.DRServerId,
            //DRServerName = result!.InfraObject!.DRServerName,
            //NearDRServerId = result.NearDRServer!.ReferenceId,
            //NearDRServerName = result.NearDRServer!.Name,
            //PRDatabaseId = result!.InfraObject!.PRDatabaseId,
            //PRDatabaseName = result!.InfraObject!.PRDatabaseName,
            //DRDatabaseId = result!.InfraObject!.DRDatabaseId,
            //DRDatabaseName = result!.InfraObject!.DRDatabaseName,
            //NearDRDatabaseId = result!.NearDRDatabase!.ReferenceId,
            //NearDRDatabaseName = result!.NearDRDatabase!.Name,
            //PRReplicationId = result!.PRReplication!.ReferenceId,
            //PRReplicationName = result!.PRReplication!.Name,
            //DRReplicationId = result!.DRReplication!.ReferenceId,
            //DRReplicationName = result!.DRReplication!.Name,
            //NearDRReplicationId = result!.NearDRReplication!.ReferenceId,
            //NearDRReplicationName = result!.NearDRReplication!.Name,
            ServerProperties = result.InfraObject.ServerProperties,
            DatabaseProperties = result.InfraObject.DatabaseProperties,
            ReplicationProperties = result.InfraObject.ReplicationProperties,

            //Changed to ComponentType from Component
            ReplicationTypeId = result!.InfraObject!.ReplicationTypeId,
            ReplicationTypeName = result!.InfraObject!.ReplicationTypeName,
            ReplicationCategoryTypeId = result!.ReplicationCategoryType!.ReferenceId,
            ReplicationCategoryType = result!.ReplicationCategoryType!.Name,
            IsPair = result!.InfraObject!.IsPair,
            PairInfraObjectId = result!.InfraObject!.PairInfraObjectId,
            PairInfraObjectName = result!.InfraObject!.PairInfraObjectName,
            IsAssociate = result!.InfraObject!.IsAssociate,
            IsAssociateInfraObjectId = result!.InfraObject!.IsAssociateInfraObjectId,
            IsAssociateInfraObjectName = result!.InfraObject!.IsAssociateInfraObjectName,

            //Changed to ComponentType from Component
            SubTypeId = result!.InfraObject!.SubTypeId,
            SubType = result!.InfraObject!.SubType,
            NodeProperties = result!.InfraObject!.NodeProperties,
            IsActive = result!.InfraObject!.IsActive,
            CreatedBy = result!.InfraObject!.CreatedBy,
            CreatedDate = result!.InfraObject!.CreatedDate,
            LastModifiedBy = result.InfraObject!.LastModifiedBy,
            LastModifiedDate = result!.InfraObject!.LastModifiedDate,
            Reason = result!.InfraObject!.Reason,
            SiteProperties = result!.InfraObject!.SiteProperties,
            DatalagStatus=result!.InfraObject!.DatalagStatus

        });
    }



    private IQueryable<InfraObject>FilterRequiredField(IQueryable<InfraObject> infraObjects)
    {
        return infraObjects.Select(x => new InfraObject
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Name = x.Name,
            Description = x.Description,
            CompanyId = x.CompanyId,
            BusinessServiceId = x.BusinessServiceId,
            BusinessServiceName = x.BusinessServiceName,
            BusinessFunctionId = x.BusinessFunctionId,
            BusinessFunctionName = x.BusinessFunctionName,
            Type = x.Type,
            SubType = x.SubType,
            DRReady = x.DRReady,
            NearDR = x.NearDR,
            RecoveryType = x.RecoveryType,
            ServerProperties = x.ServerProperties,
            DatabaseProperties = x.DatabaseProperties,
            ReplicationProperties = x.ReplicationProperties,
            //PRServerId = x.PRServerId,
            //PRServerName = x.PRServerName,
            //DRServerId = x.DRServerId,
            //DRServerName = x.DRServerName,
            //NearDRServerId = x.NearDRServerId,
            //NearDRServerName = x.NearDRServerName,
            //PRDatabaseId = x.PRDatabaseId,
            //PRDatabaseName = x.PRDatabaseName,
            //DRDatabaseId = x.DRDatabaseId,
            //DRDatabaseName = x.DRDatabaseName,
            //NearDRDatabaseId = x.NearDRDatabaseId,
            //NearDRDatabaseName = x.NearDRDatabaseName,
            //PRReplicationId = x.PRReplicationId,
            //PRReplicationName = x.PRReplicationName,
            //DRReplicationId = x.DRReplicationId,
            //DRReplicationName = x.DRReplicationName,
            //NearDRReplicationId = x.NearDRReplicationId,
            //NearDRReplicationName = x.NearDRReplicationName,
            Priority = x.Priority,
            State = x.State,
            ReplicationStatus = x.ReplicationStatus,
            DROperationStatus = x.DROperationStatus,
            IsPair = x.IsPair,
            IsDrift = x.IsDrift,
            PairInfraObjectId = x.PairInfraObjectId,
            PairInfraObjectName = x.PairInfraObjectName,
            IsAssociate = x.IsAssociate,
            IsAssociateInfraObjectId = x.IsAssociateInfraObjectId,
            IsAssociateInfraObjectName = x.IsAssociateInfraObjectName,
            ReplicationTypeId = x.ReplicationTypeId,
            ReplicationTypeName = x.ReplicationTypeName,
            ReplicationCategoryTypeId = x.ReplicationCategoryTypeId,
            ReplicationCategoryType = x.ReplicationCategoryType,
            TypeName = x.TypeName,
            SubTypeId = x.SubTypeId,
            //PRNodeId = x.PRNodeId,
            //PRNodeName = x.PRNodeName,
            //DRNodeId = x.DRNodeId,
            //DRNodeName = x.DRNodeName,
            NodeProperties = x.NodeProperties,
            Reason = x.Reason,
            SiteProperties = x.SiteProperties
        });
    }
    public async Task<List<InfraObject>> GetLockedInfraObjectListByIds(List<string> ids)
    {
        var infraObjects = await (FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.State.ToLower().Equals("locked"))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) &&  x.State.ToLower().Equals("locked")))).ToListAsync();

        return infraObjects;

    }
}