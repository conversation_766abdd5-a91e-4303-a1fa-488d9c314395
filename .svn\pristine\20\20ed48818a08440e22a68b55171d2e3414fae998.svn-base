using ContinuityPatrol.Application.Features.Employee.Commands.Create;
using ContinuityPatrol.Application.Features.Employee.Commands.Delete;
using ContinuityPatrol.Application.Features.Employee.Commands.Update;
using ContinuityPatrol.Application.Features.Employee.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Employee.Queries.GetList;
using ContinuityPatrol.Application.Features.Employee.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Employee.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.EmployeeModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class EmployeesController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<EmployeeListVm>>> GetEmployees()
    {
        Logger.LogInformation("Get All Employees");

        return Ok(await Mediator.Send(new GetEmployeeListQuery()));
    }

    [HttpGet("{id}", Name = "GetEmployee")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<EmployeeDetailVm>> GetEmployeeById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Employee Id");

        Logger.LogInformation($"Get Employee Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetEmployeeDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Configuration.View)]
 public async Task<ActionResult<PaginatedResult<EmployeeListVm>>> GetPaginatedEmployees([FromQuery] GetEmployeePaginatedListQuery query)
 {
     Logger.LogInformation("Get Searching Details in Employee Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateEmployeeResponse>> CreateEmployee([FromBody] CreateEmployeeCommand createEmployeeCommand)
    {
        Logger.LogInformation($"Create Employee '{createEmployeeCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateEmployee), await Mediator.Send(createEmployeeCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateEmployeeResponse>> UpdateEmployee([FromBody] UpdateEmployeeCommand updateEmployeeCommand)
    {
        Logger.LogInformation($"Update Employee '{updateEmployeeCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateEmployeeCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteEmployeeResponse>> DeleteEmployee(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Employee Id");

        Logger.LogInformation($"Delete Employee Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteEmployeeCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsEmployeeNameExist(string employeeName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(employeeName, "Employee Name");

     Logger.LogInformation($"Check Name Exists Detail by Employee Name '{employeeName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetEmployeeNameUniqueQuery { Name = employeeName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


