﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ReplicationRepository : BaseRepository<Replication>, IReplicationRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IInfraObjectRepository _infraObjectRepository;

    public ReplicationRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService,
        ILicenseManagerRepository licenseManagerRepository, IInfraObjectRepository infraObjectRepository) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
        _licenseManagerRepository = licenseManagerRepository;
        _infraObjectRepository = infraObjectRepository;
    }

    public override async Task<IReadOnlyList<Replication>> ListAllAsync()
    {
        var replication = base.QueryAll(replica =>
            replica.CompanyId.Equals(_loggedInUserService.CompanyId) && replica.IsActive);

        var replicaDto = MapReplication(replication);

        return _loggedInUserService.IsAllInfra
            ? await replicaDto.ToListAsync()
            : GetAssignedBusinessServicesByReplications(replicaDto);
    }
   

    public async Task<List<Replication>> GetType(string typeId)
    {
        var replication = SelectReplication(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.TypeId.Equals(typeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.TypeId.Equals(typeId)));

        var replicaDto = MapReplication(replication);

        return _loggedInUserService.IsAllInfra
            ? await replicaDto.ToListAsync()
            : GetAssignedBusinessServicesByReplications(replicaDto).ToList();
    }

    public override async Task<Replication> GetByReferenceIdAsync(string id)
    {
        var replication = base.GetByReferenceId(id,
            replica => replica.ReferenceId.Equals(id) && replica.CompanyId.Equals(_loggedInUserService.CompanyId));

        var replicaDto = MapReplication(replication);

        return await replicaDto.FirstOrDefaultAsync();
    }

    public async Task<List<Replication>> GetReplicationNames()
    {
        var replication = base
            .QueryAll(replica => replica.CompanyId.Equals(_loggedInUserService.CompanyId) && replica.IsActive)
            .Select(x => new Replication { ReferenceId=x.ReferenceId,Name=x.Name,BusinessServiceId=x.BusinessServiceId});
               
        return _loggedInUserService.IsAllInfra
            ? await replication.ToListAsync()
            : GetAssignedBusinessServicesByReplications(replication).ToList();
    }
    public override async Task<PaginatedResult<Replication>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<Replication> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await SelectReplication(_loggedInUserService.IsAllInfra
                ? MapReplication(Entities.Specify(productFilterSpec).DescOrderById())
                : MapReplication(GetPaginatedAssignedBusinessServicesByReplications(Entities.Specify(productFilterSpec)
                    .DescOrderById())))
                   .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        }
        return await SelectReplication(_loggedInUserService.IsAllInfra
               ? MapReplication(Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())
               : MapReplication(GetPaginatedAssignedBusinessServicesByReplications(Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                   .DescOrderById())))
                   .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<Replication> GetPaginatedQuery()
    {
        var replication = base.QueryAll(replica =>
           replica.CompanyId.Equals(_loggedInUserService.CompanyId) && replica.IsActive);

        var replicaDto = MapReplication(replication);

        return _loggedInUserService.IsAllInfra
            ? replicaDto.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedAssignedBusinessServicesByReplications(replicaDto).AsNoTracking()
                .OrderByDescending(x => x.Id);
    }

    public async Task<bool> IsReplicationNameUnique(string name)
    {
        return await _dbContext.Replications.AnyAsync(e => e.Name.Equals(name));
    }

    public async Task<bool> IsReplicationNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? await _dbContext.Replications.AnyAsync(e => e.Name.Equals(name))
            : (await _dbContext.Replications.Where(e => e.Name.Equals(name)).ToListAsync()).Unique(id);
    }

    //public async Task<bool> IsReplicationLicenseCountExitMaxLimit(string licenseId,string siteType,string siteId)
    //{
    //    var licenseDtl = await _licenseManagerRepository.GetLicenseDetailByIdAsync(licenseId) ??
    //                     throw new InvalidException("License GetList is null.");

    //    if (DateTime.TryParseExact(licenseDtl.ExpiryDate, "dd MMMM yyyy", CultureInfo.GetCultureInfo("en-IN"),
    //            DateTimeStyles.None, out var expiryDate))
    //        if (expiryDate < DateTime.UtcNow.Date)
    //            throw new InvalidOperationException("The license key has expired.");

    //    var siteTypeFirstString = SplitAndReplaceFirstOccurrence(siteType);

    //    var replicationCount = GetJsonProperties.GetLicenseJsonValue(licenseDtl.Properties, $"{siteTypeFirstString.ToLower()}replicationCount");

    //    return replicationCount > await GetReplicationCountByLicenseKey(licenseId,siteId);
    //}

    public async Task<bool> IsReplicationLicenseState(string licenseId)
    {
        var licenseDtl = await _licenseManagerRepository.GetLicenseDetailByIdAsync(licenseId) ??
                        throw new InvalidException("License GetList is null.");

        return licenseDtl.IsState;
    }
    public async Task<Dictionary<string, int>> GetReplicationCountByLicenseIds(List<string> licenseId, string siteTypeId)
    {
        var site = await _dbContext.Sites
            .Active()
            .AsNoTracking()
            .Where(x => x.TypeId.Equals(siteTypeId))
            .Select(x => x.ReferenceId)
            .ToListAsync();

        var replicationCounts = await _dbContext.Replications
          .Active()
          .AsNoTracking()
          .Where(x => licenseId.Contains(x.LicenseId) && site.Contains(x.SiteId))
          .GroupBy(x => x.LicenseId)
          .Select(group => new
          {
              LicenseId = group.Key,
              Count = group.Count()
          })
          .ToDictionaryAsync(x => x.LicenseId, x => x.Count);

        return replicationCounts;
    }
    public async Task<int> GetReplicationCountByLicenseKey(string licenseId, string siteTypeId)
    {
        var site = await _dbContext.Sites
            .Active()
            .AsNoTracking()
            .Where(x => x.TypeId.Equals(siteTypeId))
            .Select(x => x.ReferenceId)
            .ToListAsync();

        return await _dbContext.Replications.Active()
           .CountAsync(x => x.LicenseId.Equals(licenseId) && site.Contains(x.SiteId));
    }


    public async Task<List<Replication>> GetReplicationListByLicenseKey(string licenseId)
    {
        var replication = SelectReplication(_loggedInUserService.IsParent
              ? base.FilterBy(x => x.LicenseId.Equals(licenseId))
              : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.LicenseId.Equals(licenseId)));

        var replicaDto = MapReplication(replication);

        return _loggedInUserService.IsAllInfra
            ? await replicaDto.ToListAsync()
            : GetAssignedBusinessServicesByReplications(replicaDto).ToList();
    }
    public async Task<PaginatedResult<Replication>> GetReplicationByType(string typeId, int pageNumber, int pageSize, Specification<Replication> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await SelectReplication(_loggedInUserService.IsAllInfra
                ? MapReplication(Entities.Specify(productFilterSpec).Where(x => x.TypeId.Equals(typeId)).DescOrderById())
                : MapReplication(GetPaginatedAssignedBusinessServicesByReplications(Entities.Specify(productFilterSpec).Where(x => x.TypeId.Equals(typeId))
                    .DescOrderById())))
                    .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        }
        return await SelectReplication(_loggedInUserService.IsAllInfra
                ? MapReplication(Entities.Specify(productFilterSpec).Where(x => x.TypeId.Equals(typeId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())
                : MapReplication(GetPaginatedAssignedBusinessServicesByReplications(Entities.Specify(productFilterSpec).Where(x => x.TypeId.Equals(typeId)
                    && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())))
                    .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public IQueryable<Replication> GetReplicationByType(string typeId)
    {
        var replication = SelectReplication(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.TypeId.Equals(typeId))
            : base.FilterBy(x => x.TypeId.Equals(typeId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

        var replicationDto = MapReplication(replication);

        return _loggedInUserService.IsAllInfra
            ? replicationDto.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedAssignedBusinessServicesByReplications(replicationDto).AsNoTracking()
                .OrderByDescending(x => x.Id);
    }

    public async Task<List<Replication>> GetReplicationByTypeIds(List<string> typeIds)
    {
        if (_loggedInUserService.IsParent)
        {
            return await (_loggedInUserService.IsAllInfra
                ? Entities.Where(x => typeIds.Contains(x.TypeId)).DescOrderById()
                : GetPaginatedAssignedBusinessServicesByReplications(Entities.Where(x => typeIds.Contains(x.TypeId))
                    .DescOrderById()).Select(x => new Replication
                    {
                        Id = x.Id, ReferenceId = x.ReferenceId,
                        Name = x.Name,
                        Type = x.Type, 
                        TypeId = x.TypeId 
                    })).ToListAsync();
    
        }
        return await (_loggedInUserService.IsAllInfra
                ? Entities.Where(x => typeIds.Contains(x.TypeId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()
                : GetPaginatedAssignedBusinessServicesByReplications(Entities.Where(x => typeIds.Contains(x.TypeId)
                    && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())
                .Select(x => new Replication
                    {
                        Id = x.Id,
                        ReferenceId = x.ReferenceId,
                        Name = x.Name,
                        Type = x.Type,
                        TypeId = x.TypeId
                    })).ToListAsync();
    }
    //public async Task<List<Replication>> GetReplicationBySiteId(string siteId)
    //{
    //    var replication = SelectReplication(_loggedInUserService.IsParent
    //        ? base.FilterBy(x => x.SiteId.Equals(siteId))
    //        : base.FilterBy(x => x.SiteId.Equals(siteId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

    //    var replicationDto = MapReplication(replication);

    //    return _loggedInUserService.IsAllInfra
    //        ? await replicationDto.ToListAsync()
    //        : GetAssignedBusinessServicesByReplications(replicationDto).ToList();
    //}


    public async Task<List<Replication>> GetReplicationByBusinessServiceId(string businessServiceId)
    {
        var replication = SelectReplication(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId))
            : base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

        var replicationDto = MapReplication(replication);

        return _loggedInUserService.IsAllInfra
            ? await replicationDto.ToListAsync()
            : GetAssignedBusinessServicesByReplications(replicationDto).ToList();
    }

   
    public override async Task<Replication> GetByIdAsync(int id)
    {
        return _loggedInUserService.IsParent
            ? await base.GetByIdAsync(id)
            : (await FindByFilterAsync(replication =>
                    replication.Id.Equals(id) && replication.CompanyId.Equals(_loggedInUserService.CompanyId)))
                .SingleOrDefault();
    }

    private IReadOnlyList<Replication> GetAssignedBusinessServicesByReplications(
        IQueryable<Replication> businessServices)
    {
        var replications = new List<Replication>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                replications.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                                      where businessService.BusinessServiceId == assignedBusinessService.Id
                                      select businessService);

        var infraObjects = _infraObjectRepository.GetPaginatedQuery();


        return infraObjects.Count() > 0 ? replications.Where(replication => infraObjects
           .Any(x => x.ReplicationProperties.Contains(replication.ReferenceId))).ToList()
           : replications;

       
        //replications = replications.Where(server => infraObjects.Any(x =>
        //    server.ReferenceId.Equals(x.PRReplicationId) ||
        //    server.ReferenceId.Equals(x.DRReplicationId) ||
        //    server.ReferenceId.Equals(x.NearDRReplicationId))).ToList();

   
    }

    private IQueryable<Replication> GetPaginatedAssignedBusinessServicesByReplications(
        IQueryable<Replication> businessServices)
    {
        var assignedServiceIds = AssignedEntity.AssignedBusinessServices.Select(s => s.Id);

        businessServices = businessServices.Where(s => assignedServiceIds.Contains(s.BusinessServiceId));

        var infraObjects = _infraObjectRepository.GetPaginatedQuery();

        businessServices = businessServices.Where(replication => infraObjects.Any(x =>x.ReplicationProperties.Contains(replication.ReferenceId)));
        //businessServices = businessServices.Where(server => infraObjects.Any(x =>
        //    server.ReferenceId.Equals(x.PRReplicationId) ||
        //    server.ReferenceId.Equals(x.DRReplicationId) ||
        //    server.ReferenceId.Equals(x.NearDRReplicationId)));

        return businessServices;
    }
    private IQueryable<Replication> MapReplication(IQueryable<Replication> replications)
    {
        try
        {
            var mappedReplications = replications.Select(data => new
            {
                Replication = data,

                Company = _dbContext.Companies.Active().AsNoTracking().FirstOrDefault(c => c.ReferenceId.Equals(data.CompanyId)),

                Type = _dbContext.ComponentTypes.Active().AsNoTracking().FirstOrDefault(y => y.ReferenceId.Equals(data.TypeId)),

                BusinessService = _dbContext.BusinessServices.Active().AsNoTracking().FirstOrDefault(bs => bs.ReferenceId.Equals(data.BusinessServiceId)),

                Site = _dbContext.Sites.Active().AsNoTracking().FirstOrDefault(site => site.ReferenceId.Equals(data.SiteId)),

                License = _dbContext.LicenseManagers.Active().AsNoTracking()
                .FirstOrDefault(y => y.ReferenceId.Equals(data.LicenseId))
            });

            var mappedReplicaQuery = mappedReplications.Select(result => new Replication
            {
                Id = result.Replication.Id,
                ReferenceId = result.Replication.ReferenceId,
                CompanyId = result.Company.ReferenceId,
                Name = result.Replication.Name,
                TypeId = result.Type.ReferenceId,
                Type = result.Type.ComponentName ?? result.Replication.Type,
                BusinessServiceId = result.BusinessService.ReferenceId,
                BusinessServiceName = result.BusinessService.Name,
                SiteId = result.Site.ReferenceId,
                SiteName = result.Site.Name,
                Properties = result.Replication.Properties,
                LicenseId = result.License.ReferenceId,
                LicenseKey = SecurityHelper.Decrypt(result.License.PoNumber),
                FormVersion = result.Replication.FormVersion,
                IsActive = result.Replication.IsActive,
                CreatedBy = result.Replication.CreatedBy,
                CreatedDate = result.Replication.CreatedDate,
                LastModifiedBy = result.Replication.LastModifiedBy,
                LastModifiedDate = result.Replication.LastModifiedDate,
            });

            return mappedReplicaQuery;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }
    private static IQueryable<Replication> SelectReplication(IQueryable<Replication> query)
    {
        return query.Select(x => new Replication
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Name = x.Name,
            Type = x.Type,
            TypeId = x.TypeId,
            CompanyId = x.CompanyId,
            SiteId = x.SiteId,
            SiteName = x.SiteName,
            Properties = x.Properties,
            LicenseId = x.LicenseId,
            LicenseKey = x.LicenseKey,
            BusinessServiceId = x.BusinessServiceId,
            BusinessServiceName = x.BusinessServiceName,
            FormVersion = x.FormVersion
        });
    }
}