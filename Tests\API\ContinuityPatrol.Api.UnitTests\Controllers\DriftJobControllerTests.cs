using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DriftJob.Commands.Create;
using ContinuityPatrol.Application.Features.DriftJob.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftJob.Commands.RescheduleJob;
using ContinuityPatrol.Application.Features.DriftJob.Commands.Update;
using ContinuityPatrol.Application.Features.DriftJob.Commands.UpdateState;
using ContinuityPatrol.Application.Features.DriftJob.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.DriftJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftJob.Queries.GetList;
using ContinuityPatrol.Application.Features.DriftJob.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftJobModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DriftJobControllerTests : IClassFixture<DriftJobFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DriftJobsController _controller;
    private readonly DriftJobFixture _driftJobFixture;

    public DriftJobControllerTests(DriftJobFixture driftJobFixture)
    {
        _driftJobFixture = driftJobFixture;

        var testBuilder = new ControllerTestBuilder<DriftJobsController>();
        _controller = testBuilder.CreateController(
            _ => new DriftJobsController(),
            out _mediatorMock);
    }

    #region GetDriftJobs Tests

    [Fact]
    public async Task GetDriftJobs_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _driftJobFixture.DriftJobListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftJobListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftJobs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftJobListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.Contains("Enterprise", item.Name));
    }

    [Fact]
    public async Task GetDriftJobs_WithEmptyResult_ReturnsOkWithEmptyList()
    {
        // Arrange
        var emptyList = new List<DriftJobListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftJobListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDriftJobs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftJobListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDriftJobs_WithException_ThrowsException()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftJobListQuery>(), default))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.GetDriftJobs());
    }

    #endregion

    #region CreateDriftJob Tests

    [Fact]
    public async Task CreateDriftJob_WithValidScheduledCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftJobFixture.CreateDriftJobCommand;
        var expectedResponse = _driftJobFixture.CreateDriftJobResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftJob(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftJobResponse>(createdResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        Assert.Equal(1, command.IsSchedule);
        Assert.NotEmpty(command.CronExpression);
    }

    [Fact]
    public async Task CreateDriftJob_WithNullCommand_HandlesGracefully()
    {
        // Arrange
        CreateDriftJobCommand nullCommand = null;

        var successResponse = new CreateDriftJobResponse
        {
            Success = true,
            Message = "DriftJob created successfully",
            Id = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(nullCommand, default))
            .ReturnsAsync(successResponse);

        // Act
        var result = await _controller.CreateDriftJob(nullCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftJobResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreateDriftJob_WithInvalidCronExpression_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftJobFixture.CreateDriftJobCommand;
        command.CronExpression = "invalid-cron"; // Invalid cron expression

        var failureResponse = new CreateDriftJobResponse
        {
            Success = false,
            Message = "Invalid cron expression format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.CreateDriftJob(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftJobResponse>(createdResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region UpdateDriftJob Tests

    [Fact]
    public async Task UpdateDriftJob_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.UpdateDriftJobCommand;
        var expectedResponse = _driftJobFixture.UpdateDriftJobResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftJob(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftJobResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        Assert.True(Guid.TryParse(returnedResponse.Id, out _));
    }

    [Fact]
    public async Task UpdateDriftJob_WithNullCommand_HandlesGracefully()
    {
        // Arrange
        UpdateDriftJobCommand nullCommand = null;

        var successResponse = new UpdateDriftJobResponse
        {
            Success = true,
            Message = "DriftJob updated successfully",
            Id = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(nullCommand, default))
            .ReturnsAsync(successResponse);

        // Act
        var result = await _controller.UpdateDriftJob(nullCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftJobResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDriftJob_WithInvalidScheduleType_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftJobFixture.UpdateDriftJobCommand;
        command.ScheduleType = -1; // Invalid schedule type

        var failureResponse = new UpdateDriftJobResponse
        {
            Success = false,
            Message = "Invalid schedule type"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.UpdateDriftJob(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftJobResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region GetDriftJobById Tests

    [Fact]
    public async Task GetDriftJobById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftJobFixture.DriftJobDetailVm;
        expectedDetail.Id = id;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftJobDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftJobById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftJobDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.Contains("Enterprise", returnedDetail.Name);
        Assert.Equal("Active", returnedDetail.Status);
        Assert.Equal("Running", returnedDetail.State);
    }

    [Fact]
    public async Task GetDriftJobById_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDriftJobById(invalidId));
    }

    [Fact]
    public async Task GetDriftJobById_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetDriftJobById(nullId));
    }

    #endregion

    #region DeleteDriftJob Tests

    [Fact]
    public async Task DeleteDriftJob_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftJobFixture.DeleteDriftJobResponse;
        expectedResponse.IsActive = false;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDriftJobCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftJob(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftJobResponse>(okResult.Value);
        Assert.Equal(false, returnedResponse.IsActive);
        Assert.True(returnedResponse.Success);
        Assert.Contains("deleted successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task DeleteDriftJob_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDriftJob(invalidId));
    }

    [Fact]
    public async Task DeleteDriftJob_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteDriftJob(nullId));
    }

    #endregion

    #region GetPaginatedDriftJobs Tests

    [Fact]
    public async Task GetPaginatedDriftJobs_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _driftJobFixture.GetDriftJobPaginatedListQuery;
        var expectedResult = _driftJobFixture.DriftJobPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftJobs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftJobListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.True(returnedResult.Succeeded);
        Assert.NotEmpty(returnedResult.Data);
    }

    [Fact]
    public async Task GetPaginatedDriftJobs_WithNullQuery_HandlesGracefully()
    {
        // Arrange
        GetDriftJobPaginatedListQuery nullQuery = null;

        var emptyResult = new PaginatedResult<DriftJobListVm>
        {
            Data = new List<DriftJobListVm>(),
            TotalCount = 0,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(nullQuery, default))
            .ReturnsAsync(emptyResult);

        // Act
        var result = await _controller.GetPaginatedDriftJobs(nullQuery);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftJobListVm>>(okResult.Value);
        Assert.True(returnedResult.Succeeded);
        Assert.Empty(returnedResult.Data);
    }

    [Fact]
    public async Task GetPaginatedDriftJobs_WithInvalidPageSize_ReturnsEmptyResult()
    {
        // Arrange
        var query = _driftJobFixture.GetDriftJobPaginatedListQuery;
        query.PageSize = 0; // Invalid page size

        var emptyResult = new PaginatedResult<DriftJobListVm>
        {
            Data = new List<DriftJobListVm>(),
            TotalCount = 0,
            Succeeded = false
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(emptyResult);

        // Act
        var result = await _controller.GetPaginatedDriftJobs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftJobListVm>>(okResult.Value);
        Assert.False(returnedResult.Succeeded);
        Assert.Empty(returnedResult.Data);
    }

    #endregion

    #region IsDriftJobNameUnique Tests

    [Fact]
    public async Task IsDriftJobNameUnique_WithUniqueName_ReturnsTrue()
    {
        // Arrange
        var jobName = "Unique Enterprise Drift Job";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftJobNameUniqueQuery>(q => q.Name == jobName), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDriftJobNameExist(jobName,id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var isUnique = Assert.IsType<bool>(okResult.Value);
        Assert.True(isUnique);
    }

    [Fact]
    public async Task IsDriftJobNameUnique_WithDuplicateName_ReturnsFalse()
    {
        // Arrange
        var jobName = "Duplicate Enterprise Drift Job";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftJobNameUniqueQuery>(q => q.Name == jobName), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftJobNameExist(jobName,id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var isUnique = Assert.IsType<bool>(okResult.Value);
        Assert.False(isUnique);
    }

    [Fact]
    public async Task IsDriftJobNameUnique_WithNullName_ThrowsArgumentNullException()
    {
        // Arrange
        string nullName = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsDriftJobNameExist(nullName,""));
    }

    #endregion

    #region Additional Comprehensive Test Cases

    [Fact]
    public async Task CreateDriftJob_WithScheduledExecution_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftJobFixture.CreateDriftJobCommand;
        command.ScheduleTime = "14:30:00";
        command.IsSchedule = 1;
        var expectedResponse = _driftJobFixture.CreateDriftJobResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftJobCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftJob(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftJobResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal(1, command.IsSchedule);
        Assert.Equal("14:30:00", command.ScheduleTime);
    }

    [Fact]
    public async Task CreateDriftJob_WithRecurringSchedule_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftJobFixture.CreateDriftJobCommand;
        command.ScheduleType = 2; // Recurring schedule type
        command.CronExpression = "0 0 12 * * ?"; // Daily at noon
        var expectedResponse = _driftJobFixture.CreateDriftJobResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftJobCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftJob(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftJobResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal(2, command.ScheduleType);
        Assert.Equal("0 0 12 * * ?", command.CronExpression);
    }

    [Fact]
    public async Task UpdateDriftJob_WithJobStatusChange_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.UpdateDriftJobCommand;
        command.Status = "Completed";
        command.State = "Finished";
        var expectedResponse = _driftJobFixture.UpdateDriftJobResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftJobCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftJob(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftJobResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Completed", command.Status);
        Assert.Equal("Finished", command.State);
    }

    [Fact]
    public async Task CreateDriftJob_WithHighPriorityProperties_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftJobFixture.CreateDriftJobCommand;
        command.Properties = @"{""priority"": ""high"", ""alertLevel"": ""critical""}";
        command.Name = "High Priority Enterprise Job";
        var expectedResponse = _driftJobFixture.CreateDriftJobResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftJobCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftJob(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftJobResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("priority", command.Properties);
        Assert.Contains("High Priority", command.Name);
    }

    [Fact]
    public async Task GetPaginatedDriftJobs_WithStatusFilter_ReturnsOkResult()
    {
        // Arrange
        var query = _driftJobFixture.GetDriftJobPaginatedListQuery;
        query.SearchString = "Running";
        var expectedResult = _driftJobFixture.DriftJobPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftJobPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftJobs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftJobListVm>>(okResult.Value);
        Assert.True(returnedResult.Succeeded);
        Assert.Equal("Running", query.SearchString);
    }

    [Fact]
    public async Task UpdateDriftJob_WithExecutionLogs_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.UpdateDriftJobCommand;
        command.ExceptionMessage = "Job executed successfully with detailed logs";
        command.LastExecutionTime = DateTime.UtcNow.ToString();
        var expectedResponse = _driftJobFixture.UpdateDriftJobResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftJobCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftJob(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftJobResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("detailed logs", command.ExceptionMessage);
        Assert.NotNull(command.LastExecutionTime);
    }

    [Fact]
    public async Task GetDriftJobById_WithExecutionHistory_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftJobFixture.DriftJobDetailVm;
        expectedDetail.LastExecutionTime = DateTime.UtcNow.AddHours(-1).ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftJobDetailQuery>(), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftJobById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftJobDetailVm>(okResult.Value);
        Assert.NotNull(returnedDetail.LastExecutionTime);
        Assert.NotEmpty(returnedDetail.LastExecutionTime);
    }

    [Fact]
    public async Task DeleteDriftJob_WithRunningJob_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftJobFixture.DeleteDriftJobResponse;
        expectedResponse.Message = "Running job stopped and deleted successfully";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteDriftJobCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftJob(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftJobResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
    }

    [Fact]
    public async Task IsDriftJobNameUnique_WithJobNameVariations_ReturnsCorrectResult()
    {
        // Arrange
        var jobName = "Enterprise Job Name Variation";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftJobNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftJobNameExist(jobName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var nameExists = Assert.IsType<bool>(okResult.Value);
        Assert.False(nameExists);
    }

    [Fact]
    public async Task CreateDriftJob_WithComplexConfiguration_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftJobFixture.CreateDriftJobCommand;
        command.Properties = "{\"timeout\": 3600, \"retries\": 3, \"environment\": \"production\"}";
        command.SolutionTypeName = "Complex Enterprise Analysis";
        var expectedResponse = _driftJobFixture.CreateDriftJobResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftJobCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftJob(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftJobResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("timeout", command.Properties);
        Assert.Contains("Complex", command.SolutionTypeName);
    }

    #endregion

    #region RescheduleDriftJob Tests

    [Fact]
    public async Task RescheduleDriftJob_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.RescheduleDriftJobCommand;
        command.Id = Guid.NewGuid().ToString();
        command.Name = "Enterprise Configuration Drift Detection Job";
        command.Status = "Pending";
        var expectedResponse = _driftJobFixture.RescheduleDriftJobResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<RescheduleDriftJobCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.RescheduleDriftJob(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<RescheduleDriftJobResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("Rescheduled successfully", returnedResponse.Message);
        Assert.Equal("Pending", command.Status);
    }

    [Fact]
    public async Task RescheduleDriftJob_WithInvalidId_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.RescheduleDriftJobCommand;
        command.Id = "invalid-guid";
        command.Name = "Enterprise Configuration Drift Detection Job";
        var expectedResponse = _driftJobFixture.RescheduleDriftJobResponse;
        expectedResponse.Success = false;
        expectedResponse.Message = "Invalid job ID provided";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<RescheduleDriftJobCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.RescheduleDriftJob(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<RescheduleDriftJobResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid job ID", returnedResponse.Message);
    }

    [Fact]
    public async Task RescheduleDriftJob_WithComplexJobConfiguration_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.RescheduleDriftJobCommand;
        command.Id = Guid.NewGuid().ToString();
        command.Name = "Enterprise Multi-Node Configuration Drift Detection Job";
        command.Status = "Scheduled";
        var expectedResponse = _driftJobFixture.RescheduleDriftJobResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<RescheduleDriftJobCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.RescheduleDriftJob(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<RescheduleDriftJobResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("Multi-Node", command.Name);
        Assert.Equal("Scheduled", command.Status);
    }

    #endregion

    #region UpdateDriftJobState Tests

    [Fact]
    public async Task UpdateDriftJobState_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.UpdateDriftJobStateCommand;
        command.State = "Paused";
        command.Reason = "Maintenance window scheduled";
        var expectedResponse = _driftJobFixture.UpdateDriftJobStateResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftJobStateCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftJobState(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<BaseResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("state updated successfully", returnedResponse.Message);
        Assert.Equal("Paused", command.State);
    }

    [Fact]
    public async Task UpdateDriftJobState_WithMultipleJobs_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.UpdateDriftJobStateCommand;
        command.State = "Running";
        command.Reason = "Resuming after maintenance";
        command.UpdateDriftJobState = new List<UpdateDriftJobState>
        {
            new() { Id = Guid.NewGuid().ToString(), Name = "Enterprise Primary Drift Detection Job" },
            new() { Id = Guid.NewGuid().ToString(), Name = "Enterprise Secondary Drift Detection Job" },
            new() { Id = Guid.NewGuid().ToString(), Name = "Enterprise Backup Drift Detection Job" }
        };
        var expectedResponse = _driftJobFixture.UpdateDriftJobStateResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftJobStateCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftJobState(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<BaseResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Running", command.State);
        Assert.Equal(3, command.UpdateDriftJobState.Count);
        Assert.Contains("Resuming after maintenance", command.Reason);
    }

    [Fact]
    public async Task UpdateDriftJobState_WithStoppedState_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.UpdateDriftJobStateCommand;
        command.State = "Stopped";
        command.Reason = "Critical system error detected";
        var expectedResponse = _driftJobFixture.UpdateDriftJobStateResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftJobStateCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftJobState(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<BaseResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Stopped", command.State);
        Assert.Contains("Critical system error", command.Reason);
    }

    #endregion

    #region UpdateDriftJobStatus Tests

    [Fact]
    public async Task UpdateDriftJobStatus_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.UpdateDriftJobStatusCommand;
        command.Id = Guid.NewGuid().ToString();
        command.Name = "Enterprise Configuration Drift Detection Job";
        command.Status = "Completed";
        var expectedResponse = _driftJobFixture.UpdateDriftJobStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftJobStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftJobStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftJobStatusResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("status updated", returnedResponse.Message);
        Assert.Equal("Completed", command.Status);
    }

    [Fact]
    public async Task UpdateDriftJobStatus_WithFailedStatus_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.UpdateDriftJobStatusCommand;
        command.Id = Guid.NewGuid().ToString();
        command.Name = "Enterprise Configuration Drift Detection Job";
        command.Status = "Failed";
        var expectedResponse = _driftJobFixture.UpdateDriftJobStatusResponse;
        expectedResponse.Message = "Drift Job status updated to Failed successfully!";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftJobStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftJobStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftJobStatusResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("Failed", returnedResponse.Message);
        Assert.Equal("Failed", command.Status);
    }

    [Fact]
    public async Task UpdateDriftJobStatus_WithInProgressStatus_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.UpdateDriftJobStatusCommand;
        command.Id = Guid.NewGuid().ToString();
        command.Name = "Enterprise Multi-Server Configuration Drift Detection Job";
        command.Status = "In Progress";
        var expectedResponse = _driftJobFixture.UpdateDriftJobStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftJobStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftJobStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftJobStatusResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("Multi-Server", command.Name);
        Assert.Equal("In Progress", command.Status);
    }

    [Fact]
    public async Task UpdateDriftJobStatus_WithPendingStatus_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.UpdateDriftJobStatusCommand;
        command.Id = Guid.NewGuid().ToString();
        command.Name = "Enterprise Database Configuration Drift Detection Job";
        command.Status = "Pending";
        var expectedResponse = _driftJobFixture.UpdateDriftJobStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftJobStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftJobStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftJobStatusResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("Database", command.Name);
        Assert.Equal("Pending", command.Status);
    }

    [Fact]
    public async Task UpdateDriftJobStatus_WithCancelledStatus_ReturnsOkResult()
    {
        // Arrange
        var command = _driftJobFixture.UpdateDriftJobStatusCommand;
        command.Id = Guid.NewGuid().ToString();
        command.Name = "Enterprise Network Configuration Drift Detection Job";
        command.Status = "Cancelled";
        var expectedResponse = _driftJobFixture.UpdateDriftJobStatusResponse;
        expectedResponse.Message = "Drift Job status updated to Cancelled successfully!";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftJobStatusCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftJobStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftJobStatusResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("Cancelled", returnedResponse.Message);
        Assert.Equal("Cancelled", command.Status);
        Assert.Contains("Network", command.Name);
    }

    #endregion
}
