﻿using ContinuityPatrol.Application.Features.AlertReceiver.Event.Update;

namespace ContinuityPatrol.Application.Features.AlertReceiver.Commands.Update;

public class
    UpdateAlertReceiverCommandHandler : IRequestHandler<UpdateAlertReceiverCommand, UpdateAlertReceiverResponse>
{
    private readonly IAlertReceiverRepository _alertReceiverRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateAlertReceiverCommandHandler(IMapper mapper, IAlertReceiverRepository alertReceiverRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _alertReceiverRepository = alertReceiverRepository;
        _publisher = publisher;
    }

    public async Task<UpdateAlertReceiverResponse> Handle(UpdateAlertReceiverCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _alertReceiverRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.AlertReceiver), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateAlertReceiverCommand), typeof(Domain.Entities.AlertReceiver));

        await _alertReceiverRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateAlertReceiverResponse
        {
            Message = Message.Update("Notification Manager", eventToUpdate.Name),
            AlertReceiverId = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new AlertReceiverUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}