﻿namespace ContinuityPatrol.Application.Features.Report.Queries.GetRPOSLAReport.Models;

public class GetRPOSLASRMReportVM
{
    public int SrNo { get; set; }
    public string Id { get; set; }
    public string PRProtectionGroupName { get; set; }
    public string DRProtectionGroupName { get; set; }
    public string PRRecoveryPlanAndState { get; set; }
    public string DRRecoveryPlanAndState { get; set; }
    public string DataLag { get; set; }
    public string TimeStamp { get; set; }
    public string StartTime { get; set; }
    public string EndTime { get; set; }
    public string ConfigureRPO { get; set; }
    public string Threshold { get; set; }
    public bool IsDataLagExceeded { get; set; }
    public bool IsThresholdExceeded { get; set; }
    public string Properties { get; set; }
}

public class GetRPOSLASRMBusinessServiceDetails
{
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string FromDate { get; set; }
    public string ToDate { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string PRIPAddress { get; set; }
    public string PRDatabaseName { get; set; }
    public string DRIPAddress { get; set; }
    public string DRDatabaseName { get; set; }
    public string ReportGeneratedBy { get; set; }
    public string Date { get; set; }
    public int TotalCount { get; set; }
    public int DataLagExceededCount { get; set; }
    public int ThresholdExceededCount { get; set; }
    public int UnderThresholdCount { get; set; }
    public string DateOption { get; set; }
    public string InfraObjectType { get; set; }
    public List<GetRPOSLASRMReportVM> GetRPOSLASRMReportVM { get; set; }
}