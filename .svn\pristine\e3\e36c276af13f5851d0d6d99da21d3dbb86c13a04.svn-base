using ContinuityPatrol.Application.Features.DriftProfile.Commands.Create;
using ContinuityPatrol.Application.Features.DriftProfile.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftProfile.Commands.Update;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetList;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftProfileModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class DriftProfilesController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<List<DriftProfileListVm>>> GetDriftProfiles()
    {
        Logger.LogDebug("Get All DriftProfiles");

        return Ok(await Mediator.Send(new GetDriftProfileListQuery()));
    }

    [HttpGet("{id}", Name = "GetDriftProfile")]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<DriftProfileDetailVm>> GetDriftProfileById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftProfile Id");

        Logger.LogDebug($"Get DriftProfile Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDriftProfileDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Drift.View)]
 public async Task<ActionResult<PaginatedResult<DriftProfileListVm>>> GetPaginatedDriftProfiles([FromQuery] GetDriftProfilePaginatedListQuery query)
 {
     if (query == null)
         throw new ArgumentNullException(nameof(query));

     Logger.LogDebug("Get Searching Details in DriftProfile Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Drift.Create)]
    public async Task<ActionResult<CreateDriftProfileResponse>> CreateDriftProfile([FromBody] CreateDriftProfileCommand createDriftProfileCommand)
    {
        Logger.LogDebug($"Create DriftProfile '{createDriftProfileCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDriftProfile), await Mediator.Send(createDriftProfileCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Drift.Edit)]
    public async Task<ActionResult<UpdateDriftProfileResponse>> UpdateDriftProfile([FromBody] UpdateDriftProfileCommand updateDriftProfileCommand)
    {
        if (updateDriftProfileCommand == null)
            throw new ArgumentNullException(nameof(updateDriftProfileCommand));

        Logger.LogDebug($"Update DriftProfile '{updateDriftProfileCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateDriftProfileCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Drift.Delete)]
    public async Task<ActionResult<DeleteDriftProfileResponse>> DeleteDriftProfile(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftProfile Id");

        Logger.LogDebug($"Delete DriftProfile Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteDriftProfileCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsDriftProfileNameExist(string driftProfileName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(driftProfileName, "DriftProfile Name");

     Logger.LogDebug($"Check Name Exists Detail by DriftProfile Name '{driftProfileName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetDriftProfileNameUniqueQuery { Name = driftProfileName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


