using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class IncidentDailyRepositoryTests : IClassFixture<IncidentDailyFixture>, IDisposable
{
    private readonly IncidentDailyFixture _incidentDailyFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly IncidentDailyRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public IncidentDailyRepositoryTests(IncidentDailyFixture incidentDailyFixture)
    {
        _incidentDailyFixture = incidentDailyFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new IncidentDailyRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.IncidentDailies.RemoveRange(_dbContext.IncidentDailies);
        await _dbContext.SaveChangesAsync();
    }

    #region GetIncidentDailyByBusinessServiceIdAsync Tests

    [Fact]
    public async Task GetIncidentDailyByBusinessServiceIdAsync_ReturnsMatchingRecord_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var businessServiceId = "BS_123";

        var incidentDailies = new List<IncidentDaily>
        {
            new IncidentDaily
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ParentBusinessServiceId = businessServiceId,
                ParentBusinessServiceName = "Matching Business Service",
                InfraObjectId = "INFRA_123",
                InfraObjectName = "Test Infrastructure",
                Open = 5,
                Close = 3,
                Total = 8,
                IncidentDate = DateTime.Today,
                IsActive = true
            },
            new IncidentDaily
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ParentBusinessServiceId = "BS_456", // Different business service
                ParentBusinessServiceName = "Different Business Service",
                InfraObjectId = "INFRA_456",
                InfraObjectName = "Different Infrastructure",
                Open = 2,
                Close = 1,
                Total = 3,
                IncidentDate = DateTime.Today,
                IsActive = true
            }
        };

        await _dbContext.IncidentDailies.AddRangeAsync(incidentDailies);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetIncidentDailyByBusinessServiceIdAsync(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceId, result.ParentBusinessServiceId);
        Assert.Equal("Matching Business Service", result.ParentBusinessServiceName);
        Assert.Equal("INFRA_123", result.InfraObjectId);
        Assert.Equal("Test Infrastructure", result.InfraObjectName);
        Assert.Equal(5, result.Open);
        Assert.Equal(3, result.Close);
        Assert.Equal(8, result.Total);
        Assert.Equal(DateTime.Today, result.IncidentDate);
    }

    [Fact]
    public async Task GetIncidentDailyByBusinessServiceIdAsync_ReturnsNull_WhenNoMatch()
    {
        // Arrange
        await ClearDatabase();
        var businessServiceId = "BS_123";

        var incidentDaily = new IncidentDaily
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ParentBusinessServiceId = "BS_456", // Different business service
            ParentBusinessServiceName = "Different Business Service",
            InfraObjectId = "INFRA_456",
            InfraObjectName = "Different Infrastructure",
            Open = 2,
            Close = 1,
            Total = 3,
            IncidentDate = DateTime.Today,
            IsActive = true
        };

        await _dbContext.IncidentDailies.AddAsync(incidentDaily);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetIncidentDailyByBusinessServiceIdAsync(businessServiceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetIncidentDailyByBusinessServiceIdAsync_ReturnsFirstMatch_WhenMultipleMatches()
    {
        // Arrange
        await ClearDatabase();
        var businessServiceId = "BS_123";

        var incidentDailies = new List<IncidentDaily>
        {
            new IncidentDaily
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ParentBusinessServiceId = businessServiceId,
                ParentBusinessServiceName = "First Match",
                InfraObjectId = "INFRA_123",
                InfraObjectName = "First Infrastructure",
                Open = 5,
                Close = 3,
                Total = 8,
                IncidentDate = DateTime.Today,
                IsActive = true
            },
            new IncidentDaily
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ParentBusinessServiceId = businessServiceId,
                ParentBusinessServiceName = "Second Match",
                InfraObjectId = "INFRA_456",
                InfraObjectName = "Second Infrastructure",
                Open = 2,
                Close = 1,
                Total = 3,
                IncidentDate = DateTime.Today.AddDays(-1),
                IsActive = true
            }
        };

        await _dbContext.IncidentDailies.AddRangeAsync(incidentDailies);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetIncidentDailyByBusinessServiceIdAsync(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceId, result.ParentBusinessServiceId);
        // Should return the first match found
        Assert.True(result.ParentBusinessServiceName == "First Match" || result.ParentBusinessServiceName == "Second Match");
    }

    [Fact]
    public async Task GetIncidentDailyByBusinessServiceIdAsync_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var businessServiceId = "BS_123";

        // Act
        var result = await _repository.GetIncidentDailyByBusinessServiceIdAsync(businessServiceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetIncidentDailyByBusinessServiceIdAsync_HandlesNullBusinessServiceId()
    {
        // Arrange
        await ClearDatabase();

        var incidentDaily = new IncidentDaily
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ParentBusinessServiceId = "BS_123",
            ParentBusinessServiceName = "Test Service",
            IsActive = true
        };

        await _dbContext.IncidentDailies.AddAsync(incidentDaily);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetIncidentDailyByBusinessServiceIdAsync(null);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetIncidentDailyByDateAsync Tests

    [Fact]
    public async Task GetIncidentDailyByDateAsync_ThrowsNotImplementedException()
    {
        // Arrange
        var testDate = DateTime.Today;

        // Act & Assert
        await Assert.ThrowsAsync<NotImplementedException>(() => 
            _repository.GetIncidentDailyByDateAsync(testDate));
    }

    #endregion

    #region GetIncidentDailyByInfraObjectIdAsync Tests

    [Fact]
    public async Task GetIncidentDailyByInfraObjectIdAsync_ThrowsNotImplementedException()
    {
        // Arrange
        var infraObjectId = "INFRA_123";

        // Act & Assert
        await Assert.ThrowsAsync<NotImplementedException>(() =>
            _repository.GetIncidentDailyByInfraObjectIdAsync(infraObjectId));
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddIncidentDaily_WhenValidIncidentDaily()
    {
        // Arrange
        await ClearDatabase();
        var incidentDaily = new IncidentDaily
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ParentBusinessServiceId = "BS_123",
            ParentBusinessServiceName = "Test Business Service",
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Test Infrastructure Object",
            Open = 5,
            Close = 3,
            Total = 8,
            IncidentDate = DateTime.Today,
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(incidentDaily);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(incidentDaily.ParentBusinessServiceId, result.ParentBusinessServiceId);
        Assert.Equal(incidentDaily.ParentBusinessServiceName, result.ParentBusinessServiceName);
        Assert.Equal(incidentDaily.InfraObjectId, result.InfraObjectId);
        Assert.Equal(incidentDaily.InfraObjectName, result.InfraObjectName);
        Assert.Equal(incidentDaily.Open, result.Open);
        Assert.Equal(incidentDaily.Close, result.Close);
        Assert.Equal(incidentDaily.Total, result.Total);
        Assert.Equal(incidentDaily.IncidentDate, result.IncidentDate);
        Assert.Single(_dbContext.IncidentDailies);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenIncidentDailyIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsIncidentDaily_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var incidentDaily = new IncidentDaily
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ParentBusinessServiceName = "Test Service",
            InfraObjectName = "Test Infrastructure",
            Open = 5,
            Close = 3,
            Total = 8,
            IncidentDate = DateTime.Today,
            IsActive = true
        };

        await _dbContext.IncidentDailies.AddAsync(incidentDaily);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(incidentDaily.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(incidentDaily.Id, result.Id);
        Assert.Equal(incidentDaily.ParentBusinessServiceName, result.ParentBusinessServiceName);
        Assert.Equal(incidentDaily.InfraObjectName, result.InfraObjectName);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion
}
