namespace ContinuityPatrol.Domain.ViewModels.CyberJobManagementModel;

public record CyberJobManagementListVm
{
    public string Id { get; set; }
	public string Name { get; set; }
    public string AirgapId { get; set; }
    public string AirgapName { get; set; }
	public string WorkflowId { get; set; }
	public string WorkflowName { get; set; }
	public string SolutionId { get; set; }
	public string SolutionName { get; set; }
	public int IsSchedule { get; set; }
	public int ScheduleType { get; set; }
	public string ScheduleTime { get; set; }
	public string CronExpression { get; set; }
	public string Status { get; set; }
	public string State { get; set; }
	public string NodeId { get; set; }
	public string NodeName { get; set; }
	public string ExceptionMessage { get; set; }
    [Column(TypeName = "NCLOB")] public string LastExecutedTime { get; set; }

}
