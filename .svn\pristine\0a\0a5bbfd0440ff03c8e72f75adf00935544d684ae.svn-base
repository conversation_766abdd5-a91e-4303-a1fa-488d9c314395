﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningUserDetails;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Queries;

public class GetWorkflowOperationGroupRunningUserDetailQueryHandlerTests : IClassFixture<WorkflowOperationGroupFixture>, IClassFixture<WorkflowOperationFixture>, IClassFixture<WorkflowActionResultFixture>
{
    private readonly WorkflowOperationGroupFixture _workflowOperationGroupFixture;

    private readonly WorkflowOperationFixture _workflowOperationFixture;

    private Mock<IWorkflowOperationGroupRepository> _mockWorkflowOperationGroupRepository;

    private readonly Mock<IWorkflowOperationRepository> _mockWorkflowOperationRepository;

    private readonly Mock<IWorkflowActionResultRepository> _mockWorkflowActionResultRepository;

    private readonly Mock<IInfraObjectRepository> _mockInfraObjectRepository;

    private readonly Mock<ILoadBalancerRepository> _mockLoadBalancerRepository;

    private readonly GetWorkflowOperationGroupRunningUserDetailsQueryHandler _handler;

    public GetWorkflowOperationGroupRunningUserDetailQueryHandlerTests(WorkflowOperationGroupFixture workflowOperationGroupFixture, WorkflowOperationFixture workflowOperationFixture, WorkflowActionResultFixture workflowActionResultFixture)
    {
        _workflowOperationGroupFixture = workflowOperationGroupFixture;

        _workflowOperationFixture = workflowOperationFixture;

        var workflowActionResultFixture1 = workflowActionResultFixture;

        _mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();

        _mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();

        _mockWorkflowOperationRepository = new Mock<IWorkflowOperationRepository>();

        _mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();

        _mockInfraObjectRepository = new Mock<IInfraObjectRepository>();

        _mockWorkflowOperationGroupRepository = WorkflowOperationGroupRepositoryMocks.GetWorkflowOperationGroupByWorkflowOperationId(_workflowOperationGroupFixture.WorkflowOperationGroups);

        _mockWorkflowOperationRepository = WorkflowOperationRepositoryMocks.GetWorkflowOperationGroupRunningStatus(_workflowOperationFixture.WorkflowOperations);

        _mockWorkflowActionResultRepository = WorkflowActionResultRepositoryMocks.GetWorkflowActionResultFromOperationGroupIdRepository(workflowActionResultFixture1.WorkflowActionResults);

        _mockWorkflowActionResultRepository = WorkflowActionResultRepositoryMocks.GetWorkflowActionResultByWorkflowOperationId(workflowActionResultFixture1.WorkflowActionResults);

        _handler = new GetWorkflowOperationGroupRunningUserDetailsQueryHandler(_workflowOperationGroupFixture.Mapper, _mockWorkflowOperationGroupRepository.Object, _mockWorkflowOperationRepository.Object, _mockWorkflowActionResultRepository.Object, _mockInfraObjectRepository.Object, _mockLoadBalancerRepository.Object);

        _workflowOperationFixture.WorkflowOperations[0].Status = "running";
        _workflowOperationFixture.WorkflowOperations[1].Status = "running";
        _workflowOperationFixture.WorkflowOperations[2].Status = "running";

        _workflowOperationFixture.WorkflowOperations[0].CreatedBy = "cf620755-cn6e-4185-8306-3fk490b9dd94";
        _workflowOperationFixture.WorkflowOperations[1].CreatedBy = "cf620755-cn6e-4185-8306-3fk490b9dd94";
        _workflowOperationFixture.WorkflowOperations[2].CreatedBy = "cf620755-cn6e-4185-8306-3fk490b9dd94";

        _workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowOperationId = "ReferenceId95bf064c-b48f-4a6c-ae7d-5504cd11055a";
    }

    [Fact]
    public async Task Handle_ReturnWorkflowOperationGroupRunningUserDetails_When_ValidWorkflowOperationGroup()
    {
        _workflowOperationFixture.WorkflowOperations[0].ReferenceId = _workflowOperationGroupFixture.WorkflowOperationGroups[0].ReferenceId;

        var result = await _handler.Handle(new GetWorkflowOperationGroupRunningUserDetailQuery { UserId = "cf620755-cn6e-4185-8306-3fk490b9dd94" }, CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowOperationGroupRunningUserVm>>();
        result[0].Id.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].ReferenceId);
        result[0].ProfileId.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].ProfileId);
        result[0].ProfileName.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].ProfileName);
    }
    
    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowOperationGroupRunningUserDetailQuery { UserId = _workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowOperationId }, CancellationToken.None);

        _mockWorkflowOperationGroupRepository.Verify(x => x.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>()), Times.Exactly(0));
    }

    //[Fact]
    //public async Task Handle_ThrowNotFoundException_When_Invalid_WorkflowOperationGroupRunningUserDetails()
    //{
    //    _mockWorkflowOperationGroupRepository = WorkflowOperationGroupRepositoryMocks.GetWorkflowOperationGroupEmptyRepository();

    //    var handler = new GetWorkflowOperationGroupRunningUserDetailsQueryHandler(_workflowOperationGroupFixture.Mapper, _mockWorkflowOperationGroupRepository.Object, _mockWorkflowOperationRepository.Object, _mockWorkflowActionResultRepository.Object);

    //    await Assert.ThrowsAsync<ArgumentNullException>(() => handler.Handle(new GetWorkflowOperationGroupRunningUserDetailQuery(), CancellationToken.None));
    //}
}