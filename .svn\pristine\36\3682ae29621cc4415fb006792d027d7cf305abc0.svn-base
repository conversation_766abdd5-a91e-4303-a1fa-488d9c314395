using ContinuityPatrol.Application.Features.CyberAlert.Events.Update;

namespace ContinuityPatrol.Application.Features.CyberAlert.Commands.Update;

public class UpdateCyberAlertCommandHandler : IRequestHandler<UpdateCyberAlertCommand, UpdateCyberAlertResponse>
{
    private readonly ICyberAlertRepository _cyberAlertRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateCyberAlertCommandHandler(IMapper mapper, ICyberAlertRepository cyberAlertRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _cyberAlertRepository = cyberAlertRepository;
        _publisher = publisher;
    }

    public async Task<UpdateCyberAlertResponse> Handle(UpdateCyberAlertCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _cyberAlertRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.CyberAlert), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateCyberAlertCommand), typeof(Domain.Entities.CyberAlert));

        await _cyberAlertRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateCyberAlertResponse
        {
            Message = Message.Update(nameof(Domain.Entities.CyberAlert), eventToUpdate.JobName),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new CyberAlertUpdatedEvent { Name = eventToUpdate.JobName }, cancellationToken);

        return response;
    }
}