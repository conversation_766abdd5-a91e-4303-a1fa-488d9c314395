﻿namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetByWorkflowOperationId;

public class
    GetByWorkflowOperationIdQueryHandler : IRequestHandler<GetByWorkflowOperationIdQuery,
        List<GetByWorkflowOperationIdVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;

    public GetByWorkflowOperationIdQueryHandler(IMapper mapper,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository)
    {
        _mapper = mapper;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
    }

    public async Task<List<GetByWorkflowOperationIdVm>> Handle(GetByWorkflowOperationIdQuery request,
        CancellationToken cancellationToken)
    {
        var workflowOperationGroup =
            await _workflowOperationGroupRepository.GetWorkflowOperationGroupByWorkflowOperationId(
                request.WorkflowOperationId);

        Guard.Against.NullOrDeactive(workflowOperationGroup, nameof(Domain.Entities.WorkflowOperationGroup),
            new NotFoundException(nameof(Domain.Entities.WorkflowOperationGroup), request.WorkflowOperationId));

        var workflowOperationGroupDetail = _mapper.Map<List<GetByWorkflowOperationIdVm>>(workflowOperationGroup);

        return workflowOperationGroupDetail;
    }
}