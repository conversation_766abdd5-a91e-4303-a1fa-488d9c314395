using ContinuityPatrol.Application.Contexts;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Persistence.Persistence;

public partial class ApplicationDbContext : IManageDbContext
{
    #region Context
	public DbSet<ApprovalMatrixUsers> ApprovalMatrixUsers { get; set; }

    public DbSet<ApprovalMatrixApproval> ApprovalMatrixApprovals { get; set; }
    public DbSet<ApprovalMatrixRequest> ApprovalMatrixRequests { get; set; }
    public DbSet<MSSQLMonitorStatus> MssqlMonitorStatuses { get; set; }
    public DbSet<MYSQLMonitorStatus> MysqlMonitorStatuses { get; set; }
    public DbSet<OracleMonitorStatus> OracleMonitorStatuses { get; set; }
    public DbSet<OracleRACMonitorStatus> OracleRacStatuses { get; set; }
    public DbSet<PostgresMonitorStatus> PostgresMonitorStatuses { get; set; }
    public DbSet<MYSQLMonitorLogs> MysqlMonitorLogs { get; set; }
    public DbSet<MSSQLMonitorLogs> MssqlMonitorLogs { get; set; }
    public DbSet<OracleMonitorLogs> OracleMonitorLogs { get; set; }
    public DbSet<PostgresMonitorLogs> PostgresMonitorLogs { get; set; }
    public DbSet<OracleRACMonitorLogs> OracleRacMonitorLogs { get; set; }
    public DbSet<MSSQLAlwaysOnMonitorLogs> MssqlAlwaysOnMonitorLogs { get; set; }
    public DbSet<DB2HADRMonitorLog> Db2HadrMonitorLogs { get; set; }
    public DbSet<MssqlNativeLogShippingMonitorLog> MssqlNativeLogShippingMonitorLogs { get; set; }
    public DbSet<MongoDBMonitorLog> MongoDbMonitorLogs { get; set; }
    public DbSet<SVCMssqlMonitorLog> SvcMssqlMonitorLogs { get; set; }
    public DbSet<MSSQLAlwaysOnMonitorStatus> MssqlAlwaysOnMonitorStatus { get; set; }
    public DbSet<StateMonitorLog> StateMonitorLogs { get; set; }
    public DbSet<StateMonitorStatus> StateMonitorStatus { get; set; }
    public DbSet<MonitorService> MonitorServices { get; set; }
    public DbSet<Db2HaDrMonitorStatus> Db2HaDrMonitorStatus { get; set; }
    public DbSet<MsSqlNativeLogShippingMonitorStatus> MsSqlNativeLogShippingMonitorStatus { get; set; }
    public DbSet<MongoDbMonitorStatus> MongoDbMonitorStatus { get; set; }
    public DbSet<SvcMsSqlMonitorStatus> SvcMsSqlMonitorStatus { get; set; }
    public DbSet<MSSQLDBMirroringLogs> MSsqldbmirroingloGs { get; set; }
    public DbSet<MSSQLDBMirroringStatus> MSsqldbmirroingStatuses { get; set; }
    public DbSet<ApprovalMatrix> ApprovalMatrix { get; set; }
    public DbSet<SVCGMMonitorStatus> SVcgmMonitorStatuses { get; set; }
    public DbSet<SVCGMMonitorLog> SVcgmMonitorLogs { get; set; }
    public DbSet<RoboCopyMonitorLogs> RoboCopyMonitorLogs { get; set; }
    public DbSet<RoboCopyMonitorStatus> RoboCopyMonitorStatus { get; set; }
    public DbSet<RsyncMonitorLog> RsyncMonitorLog { get; set; }
    public DbSet<RsyncMonitorStatus> RsyncMonitorStatus { get; set;}
    public DbSet<SRMMonitorLog> SrmMonitorLog { get; set; }
    public DbSet<AzureStorageAccountMonitorlogs> AzureStorageAccountMonitorlogs { get; set; }
    public DbSet<RpForVmMonitorStatus>  rpForVmMonitorStatuses { get; set; }
    public DbSet<RpForVmMonitorLogs> rpForVmMonitorLogs { get; set; }
    public DbSet<PowerMaxMonitorStatus> PowerMaxMonitorStatus { get; set; }
    public DbSet<RpForVmCGMonitorStatus> RpForVmCGMonitorStatuses { get ; set ; }
    public DbSet<RpForVmCGMonitorLogs> RpForVmCGMonitorLogs { get; set; }
    public DbSet<FastCopyMonitor> FastCopyMonitors { get; set; }
    public DbSet<ActiveDirectoryMonitorLog> ActiveDirectoryMonitorLogs { get; set; }
    public DbSet<DataSyncMonitorLog> DataSyncMonitorLogs { get; set;}
    public DbSet<FastCopyMonitorLog> FastCopyMonitorLogs { get; set; }
    public DbSet<ZertoVpgMonitorLog> ZertoVpgMonitorLogs { get; set; }
    public DbSet<SybaseRSHADRMonitorLog> SybaseRSHADRMonitorLogs { get; set; }
    public DbSet<MssqlAlwaysOnAvailabilityGroupMonitorLog> MssqlAlwaysOnAvailabilityGroupMonitorLogs { get; set; }
    #endregion
}
