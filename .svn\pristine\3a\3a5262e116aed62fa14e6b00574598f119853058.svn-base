﻿using ContinuityPatrol.Domain.Entities;
namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowInfraObjectRepositoryMocks
{
    public static Mock<IWorkflowInfraObjectRepository> CreateWorkflowInfraObjectRepository(List<WorkflowInfraObject> workflowInfraObjects)
    {
        var mockWorkflowInfraObjectRepository = new Mock<IWorkflowInfraObjectRepository>();

        mockWorkflowInfraObjectRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowInfraObjects);

        mockWorkflowInfraObjectRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowInfraObject>())).ReturnsAsync(
            (WorkflowInfraObject workflowInfraObject) =>
            {
                workflowInfraObject.Id = new Fixture().Create<int>();

                workflowInfraObject.ReferenceId = new Fixture().Create<Guid>().ToString();

                workflowInfraObjects.Add(workflowInfraObject);

                return workflowInfraObject;
            });

        return mockWorkflowInfraObjectRepository;
    }

    public static Mock<IWorkflowInfraObjectRepository> DeleteWorkflowInfraObjectRepository(List<WorkflowInfraObject> workflowInfraObjects)
    {
        var mockWorkflowInfraObjectRepository = new Mock<IWorkflowInfraObjectRepository>();

        mockWorkflowInfraObjectRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowInfraObjects);

        mockWorkflowInfraObjectRepository.Setup(repo => repo.GetInfraObjectIdAttachByWorkflowId(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j, string k) => workflowInfraObjects.FirstOrDefault(x => x.WorkflowId==i && x.InfraObjectId == j && x.ActionType==k && x.IsAttach));

        mockWorkflowInfraObjectRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowInfraObject>())).ReturnsAsync((WorkflowInfraObject workflowInfraObject) =>
        {
            var index = workflowInfraObjects.FindIndex(item => item.Id == workflowInfraObject.Id);

            workflowInfraObject.IsActive = false;

            workflowInfraObjects[index] = workflowInfraObject;

            return workflowInfraObject;
        });

        return mockWorkflowInfraObjectRepository;
    }

    public static Mock<IWorkflowInfraObjectRepository> GetWorkflowInfraObjectRepository(List<WorkflowInfraObject> workflowInfraObjects)
    {
        var mockWorkflowInfraObjectRepository = new Mock<IWorkflowInfraObjectRepository>();

        mockWorkflowInfraObjectRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowInfraObjects);

        mockWorkflowInfraObjectRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowInfraObjects.FirstOrDefault(x => x.ReferenceId == i));

        return mockWorkflowInfraObjectRepository;
    }

    public static Mock<IWorkflowInfraObjectRepository> GetWorkflowInfraObjectEmptyRepository()
    {
        var mockWorkflowInfraObjectRepository = new Mock<IWorkflowInfraObjectRepository>();

        mockWorkflowInfraObjectRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowInfraObject>());

        mockWorkflowInfraObjectRepository.Setup(repo => repo.GetWorkflowInfraObjectFromInfraObjectId(It.IsAny<string>())).ReturnsAsync(new List<WorkflowInfraObject>());

        return mockWorkflowInfraObjectRepository;
    } 

    public static Mock<IWorkflowInfraObjectRepository> GetWorkflowInfraObjectFromInfraObjectIdRepository(List<WorkflowInfraObject> workflowInfraObjects)
    {
        var mockWorkflowInfraObjectRepository = new Mock<IWorkflowInfraObjectRepository>();

        mockWorkflowInfraObjectRepository.Setup(repo => repo.GetWorkflowInfraObjectFromInfraObjectId(It.IsAny<string>())).ReturnsAsync(workflowInfraObjects);
       
        return mockWorkflowInfraObjectRepository;
    }

    public static Mock<IWorkflowInfraObjectRepository> GetWorkflowInfraObjectByWorkflowIdRepository(List<WorkflowInfraObject> workflowInfraObjects)
    {
        var mockWorkflowInfraObjectRepository = new Mock<IWorkflowInfraObjectRepository>();

        mockWorkflowInfraObjectRepository.Setup(repo => repo.GetWorkflowInfraObjectFromWorkflowId(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => workflowInfraObjects.Where(x => x.WorkflowId == i && x.InfraObjectId == j).ToList());

        return mockWorkflowInfraObjectRepository;
    }

    public static Mock<IWorkflowInfraObjectRepository> GetPaginatedWorkflowInfraObjectRepository(List<WorkflowInfraObject> workflowInfraObjects)
    {
        var mockWorkflowInfraObjectRepository = new Mock<IWorkflowInfraObjectRepository>();

        var queryableWorkflowInfraObject = workflowInfraObjects.BuildMock();

        mockWorkflowInfraObjectRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableWorkflowInfraObject);

        return mockWorkflowInfraObjectRepository;
    }

    //Events
    public static Mock<IUserActivityRepository> CreateWorkflowInfraObjectEventRepository(List<UserActivity> userActivities)
    {
        var workflowInfraObjectEventRepository = new Mock<IUserActivityRepository>();        

        workflowInfraObjectEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return workflowInfraObjectEventRepository;
    }
}
