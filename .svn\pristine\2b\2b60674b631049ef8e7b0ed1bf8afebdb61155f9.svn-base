﻿@model ContinuityPatrol.Domain.ViewModels.NodeModel.NodeViewModel
@*
    For more information on enabling MVC for empty projects, visit http://go.microsoft.com/fwlink/?LinkID=397860
*@

<!--Modal Create-->
<div class="modal-dialog modal-lg modal-dialog-scrollable modal-dialog-centered">
    <div class="modal-content">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-network"></i><span>Node Configuration</span></h6>
            <button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body py-0">
            <form id="node-form" asp-controller="Node" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data"
                  class="tab-wizard wizard-circle wizard clearfix mb-3 example-form">

                <input id="nodeId" type="hidden" asp-for="Id" />
                <input id="ServerId" type="hidden" asp-for="ServerId" />
                <input asp-for="Properties" type="hidden" id="Props" class="form-control" />
             @*   <input id="PropertiesId" type="hidden" asp-for="Properties" /> *@
                <h6>
                    <span class="step">
                    </span>
                    <span class="step_title" title="Replication Details">
                    </span>
                </h6>
                <section>
                    <div class="row">
                        <div class="mb-3">
                            <div class="form-group">
                                <div class="form-label">Name</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-name"></i></span>
                                    <input id="nodeName" maxlength="100" asp-for="Name" type="text" class="form-control"
                                           placeholder="Enter Node Name" autocomplete="off"/>
                                </div>
                                <span asp-validation-for="Name" id="Name-error"></span>
                            </div>
                            <div class="form-group">
                                <div class="form-label">Server</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-server"></i></span>
                                    <select id="nodeServer" asp-for="ServerName" class="form-select-modal infraComponentsServerName"
                                            onchange="validationServerName()" data-placeholder="Select Server">
                                        <option value="">Select server</option>
                                    </select>
                                </div>
                                <span asp-validation-for="ServerName" id="ServerName-error"></span>
                            </div>

                            <div class="form-group">
                                <div class="form-label">Node Type</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-network"></i></span>
                                    <select id="nodeType" asp-for="TypeId" class="form-select-modal infraComponentsNodeType"
                                            onchange="validationType()" data-placeholder="Select Node Type">
                                        <option value="">Select Type</option>
                                    </select>
                                    <input id="nodeTypeName" type="hidden" asp-for="Type" />
                                </div>
                                <span asp-validation-for="Type" id="NodeType-error"></span>
                            </div>
                        </div>
                    </div>
                    <h6>
                        <span class="step">

                        </span>
                        <span class="step_title" title="Replication Details">

                        </span>
                    </h6>
                </section>
                <section>
                    <div id="nodeFormRenderingArea" class="row">
                    </div>
                </section>
            </form>
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary">
                <i class="cp-note me-1"></i>Note: All fields are mandatory
                except optional
            </small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-cancel btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary save_btn btn-sm" id="saveButton" >Save</button>
            </div>
        </div>
    </div>
</div>


@section Scripts
    {
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}
