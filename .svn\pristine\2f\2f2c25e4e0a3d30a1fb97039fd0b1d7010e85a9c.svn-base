﻿using ContinuityPatrol.Application.Features.SVCGMMonitoringStatus.Commands.Create;
using ContinuityPatrol.Application.Features.SVCGMMonitoringStatus.Commands.Update;
using ContinuityPatrol.Application.Features.SVCGMMonitoringStatus.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.SVCGMMonitoringStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.SVCGMMonitoringStatus.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.SVCGMMonitorStatusModel;

namespace ContinuityPatrol.Application.Mappings;

public class SvcgmMonitorStatusProfile : Profile
{
    public SvcgmMonitorStatusProfile()
    {
        CreateMap<SVCGMMonitorStatus, CreateSVCGMMonitorStatusCommand>().ReverseMap();
        CreateMap<UpdateSVCGMMonitorStatusCommand, SVCGMMonitorStatus>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<SVCGMMonitorStatus, SVCGMMonitorStatusDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<SVCGMMonitorStatus, SVCGMMonitorStatusListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<SVCGMMonitorStatus, SVCGMMonitorStatusDetailByTypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<SVCGMMonitorStatus, SVCGMMonitorStatusByInfraObjectIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}