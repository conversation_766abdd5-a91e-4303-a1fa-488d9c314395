using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Xunit;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowActionRepositoryTests : IClassFixture<WorkflowActionFixture>
    {
        private readonly WorkflowActionFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowActionRepository _repository;

        public WorkflowActionRepositoryTests(WorkflowActionFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _repository = new WorkflowActionRepository(_dbContext, DbContextFactory.GetMockUserService());
        }

        [Fact]
        public async Task GetWorkflowActionNames_ReturnsAllActionNames()
        {
            await _dbContext.WorkflowActions.AddRangeAsync(_fixture.WorkflowActionList);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.GetWorkflowActionNames();

            Assert.Equal(_fixture.WorkflowActionList.Count, result.Count);
            Assert.All(result, x => Assert.Contains(_fixture.WorkflowActionList, y => y.ActionName == x.ActionName));
        }

        [Fact]
        public async Task IsWorkflowActionNameExist_ReturnsTrue_WhenNameExists_AndIdIsInvalidGuid()
        {
            var entity = _fixture.WorkflowActionDto;
            entity.ActionName = "TestAction";
            await  _dbContext.WorkflowActions.AddAsync(entity);
            await  _dbContext.SaveChangesAsync();

            var result = await _repository.IsWorkflowActionNameExist("TestAction", "invalid-guid");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowActionNameExist_ReturnsFalse_WhenNameDoesNotExist_AndIdIsInvalidGuid()
        {
            var result = await _repository.IsWorkflowActionNameExist("NonExistent", "invalid-guid");

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowActionNameExist_ReturnsExpected_WhenIdIsValidGuid()
        {
            var id = Guid.NewGuid().ToString();
            var entity = _fixture.WorkflowActionDto;
            entity.ReferenceId = id;
            entity.ActionName = "UniqueAction";
            await _dbContext.WorkflowActions.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.IsWorkflowActionNameExist("UniqueAction", id);

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowActionNameUnique_ReturnsTrue_WhenNameExists()
        {
            var entity = _fixture.WorkflowActionDto;
            entity.ActionName = "UniqueAction";
            await  _dbContext.WorkflowActions.AddAsync(entity);
            await  _dbContext.SaveChangesAsync();

            var result = await _repository.IsWorkflowActionNameUnique("UniqueAction");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowActionNameUnique_ReturnsFalse_WhenNameDoesNotExist()
        {
            var result = await _repository.IsWorkflowActionNameUnique("NonExistent");

            Assert.False(result);
        }

        [Fact]
        public async Task GetWorkflowActionDetailsByNodeId_ReturnsActionsForNode()
        {
            var entity = _fixture.WorkflowActionDto;
            entity.NodeId = "Node1";
            await  _dbContext.WorkflowActions.AddAsync(entity);
            await  _dbContext.SaveChangesAsync();

            var result = await _repository.GetWorkflowActionDetailsByNodeId("Node1");

            Assert.Single(result);
            Assert.Equal("Node1", result[0].NodeId);
        }

        [Fact]
        public async Task GetWorkflowActionsByIds_ReturnsMatchingActions()
        {
            await _dbContext.WorkflowActions.AddRangeAsync(_fixture.WorkflowActionList);
            await _dbContext.SaveChangesAsync();

            var ids = _fixture.WorkflowActionList.Select(x => x.ReferenceId).ToList();
            var result = await _repository.GetWorkflowActionsByIds(ids);

            Assert.Equal(ids.Count, result.Count);
        }

        [Fact]
        public async Task GetPaginatedQuery_ReturnsActiveActionsOrdered()
        {
            await _dbContext.WorkflowActions.AddRangeAsync(_fixture.WorkflowActionPaginationList);

            var result = _repository.GetPaginatedQuery().ToList();

            Assert.All(result, x => Assert.True(x.IsActive));
            Assert.True(result.SequenceEqual(result.OrderByDescending(x => x.Id)));
        }

        [Fact]
        public async Task GetWorkflowActionDetailsByName_ReturnsAction_WhenExists()
        {
            var entity = _fixture.WorkflowActionDto;
            entity.ActionName = "TestAction";
            await  _dbContext.WorkflowActions.AddAsync(entity);
            await  _dbContext.SaveChangesAsync();

            var result = await _repository.GetWorkflowActionDetailsByName("TestAction");

            Assert.NotNull(result);
            Assert.Equal("TestAction", result.ActionName);
        }

        [Fact]
        public async Task GetWorkflowActionDetailsByName_ReturnsNull_WhenNotExists()
        {
            var result = await _repository.GetWorkflowActionDetailsByName("NonExistent");

            Assert.Null(result);
        }
    }
}