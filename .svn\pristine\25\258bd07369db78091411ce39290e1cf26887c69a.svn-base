﻿using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.update;
using ContinuityPatrol.Application.Features.RoboCopyJob.Events.Update;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopyJob.Commands
{
    public class UpdateRoboCopyJobTests
    {
        private readonly Mock<IRoboCopyJobRepository> _mockRoboCopyJobRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly UpdateRoboCopyJobCommandHandler _handler;

        public UpdateRoboCopyJobTests()
        {
            _mockRoboCopyJobRepository = new Mock<IRoboCopyJobRepository>();
            _mockMapper = new Mock<IMapper>();
            _mockPublisher = new Mock<IPublisher>();

            _handler = new UpdateRoboCopyJobCommandHandler(
                _mockRoboCopyJobRepository.Object,
                _mockMapper.Object,
                _mockPublisher.Object
            );
        }

        [Fact]
        public async Task Handle_ValidRequest_UpdatesRoboCopyJobAndReturnsResponse()
        {
            var command = new UpdateRoboCopyJobCommand
            {
                Id = Guid.NewGuid().ToString(),
                ReplicationName = "UpdatedReplication"
            };

            var existingRoboCopyJob = new Domain.Entities.RoboCopyJob
            {
                ReferenceId = command.Id,
                ReplicationName = "OldReplication"
            };

            _mockRoboCopyJobRepository
                .Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
                .ReturnsAsync(existingRoboCopyJob);

            _mockRoboCopyJobRepository
                .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RoboCopyJob>()))
                .Returns(ToString);

            _mockMapper
                .Setup(mapper => mapper.Map(command, existingRoboCopyJob, typeof(UpdateRoboCopyJobCommand), typeof(Domain.Entities.RoboCopyJob)));

            _mockPublisher
                .Setup(pub => pub.Publish(It.IsAny<RoboCopyJobUpdatedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(command.Id, result.Id);
            Assert.Equal($"RoboCopyJob with ID {command.Id} has been updated.", result.Message);

            _mockRoboCopyJobRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RoboCopyJob>()), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RoboCopyJobUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_RoboCopyJobNotFound_ThrowsNotFoundException()
        {
            var command = new UpdateRoboCopyJobCommand { Id = Guid.NewGuid().ToString() };

            _mockRoboCopyJobRepository
                .Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
                .ReturnsAsync((Domain.Entities.RoboCopyJob)null);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Equal($"Entity \"RoboCopyJob\" ({command.Id}) was not found.", exception.Message);

            _mockRoboCopyJobRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RoboCopyJob>()), Times.Never);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RoboCopyJobUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ValidRequest_CallsMapperCorrectly()
        {
            var command = new UpdateRoboCopyJobCommand
            {
                Id = Guid.NewGuid().ToString(),
                ReplicationName = "UpdatedReplication"
            };

            var existingRoboCopyJob = new Domain.Entities.RoboCopyJob
            {
                ReferenceId = command.Id,
                ReplicationName = "OldReplication"
            };

            _mockRoboCopyJobRepository
                .Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
                .ReturnsAsync(existingRoboCopyJob);

            _mockMapper
                .Setup(mapper => mapper.Map(command, existingRoboCopyJob, typeof(UpdateRoboCopyJobCommand), typeof(Domain.Entities.RoboCopyJob)));

            _mockRoboCopyJobRepository
                .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RoboCopyJob>()))
                .Returns(ToString);

            await _handler.Handle(command, CancellationToken.None);

            _mockMapper.Verify(mapper => mapper.Map(command, existingRoboCopyJob, typeof(UpdateRoboCopyJobCommand), typeof(Domain.Entities.RoboCopyJob)), Times.Once);
        }
    }
}
