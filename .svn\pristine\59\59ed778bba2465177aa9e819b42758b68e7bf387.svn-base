﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByInfraObjectId;

public class ItViewByInfraObjectIdVm
{
    public string Id { get; set; }
    public string Properties { get; set; }
    public string PRServerId { get; set; }
    public string DRServerId { get; set; }
    public string PRServerName { get; set; }
    public string DRServerName { get; set; }
    public string PRServerStatus { get; set; }
    public string DRServerStatus { get; set; }
    public string PRDatabaseId { get; set; }
    public string DRDatabaseId { get; set; }
    public string PRDatabaseName { get; set; }
    public string DRDatabaseName { get; set; }
    public string PRDatabaseStatus { get; set; }
    public string DRDatabaseStatus { get; set; }
    public string ReplicationType { get; set; }
    public string ReplicationCategoryType { get; set; }
    public string DROperationStatus { get; set; }
    public int ReplicationStatus { get; set; }
    public string DataLagValue { get; set; }
    public string ConfiguredRTO { get; set; }
    public string ConfiguredRPO { get; set; }
    public string CurrentRTO { get; set; }
    public string CurrentRPO { get; set; }
    public string RPOThreshold { get; set; }
    public string RPOGeneratedDate { get; set; }
    public string RTOGeneratedDate { get; set; }
    public string LastModifiedDate { get; set; }
    public string LastComputedRPO { get; set; }
    public string LastComputedRTO { get; set; }
    public string EstimatedRTO { get; set; }
    public List<MonitorServiceDetails> MonitorServiceDetails { get; set; } = new();
}

public class MonitorServiceDetails
{
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string ServerId { get; set; }
    public string ServerName { get; set; }
    public string ServerType { get; set; }
    public string Status { get; set; }
    public string IsServiceUpdate { get; set; }
    public string FailedActionId { get; set; }
    public string FailedActionName { get; set; }
    public string Type { get; set; }
}