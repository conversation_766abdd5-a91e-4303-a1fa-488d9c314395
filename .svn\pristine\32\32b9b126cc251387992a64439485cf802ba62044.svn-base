﻿using ContinuityPatrol.Application.Features.OracleRACMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.OracleRACMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.OracleRACMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.OracleRACMonitorStatusModel;

namespace ContinuityPatrol.Application.Mappings;

public class OracleRacMonitorStatusProfile : Profile
{
    public OracleRacMonitorStatusProfile()
    {
        CreateMap<OracleRACMonitorStatus, CreateOracleRACStatusCommand>().ReverseMap();
        CreateMap<UpdateOracleRACStatusCommand, OracleRACMonitorStatus>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<OracleRACMonitorStatus, OracleRACStatusDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<OracleRACMonitorStatus, OracleRACMonitorStatusListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}