﻿@* @model ContinuityPatrol.Domain.ViewModels.PRDRCompareModel.PRDRCompareViewModel *@

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@*<div class="page-content">*@
@*<div class="modal fade"  data-bs-backdrop="static" aria-labelledby="DetailModalLabel" aria-hidden="true">*@
<div class="modal fade" id="myModal" role="dialog">
<div class="modal-dialog modal-lg modal-dialog-scrollable modal-dialog-centered Organization_modal">
    <from class="modal-content" asp-controller="PRDRCompare" asp-antiforgery="true" id="ref" asp-action="Details" method="get" class="tab-wizard wizard-circle wizard clearfix">
        <div class="modal-content">
            <div class="modal-header">

                <h6 class="page_title" title="Server List"><i class="cp-server"></i><span>  Compare Prod DR Server Details </span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="row modal-body py-0" style="width:800px;">
                @*<from asp-action="Details" >*@

                <div class="col-md-6" style="margin-top: 20px; margin-bottom:20px;">
                    <h3 class="text-primary" style="font-weight: bold;">Prod Site</h3>
                    <div class="col-md-6" style="margin-top: 20px; margin-bottom:20px;">
                        <h3 class="text-primary" style="font-weight: bold;">DR Site</h3>
                    </div>
                    <table>
                        <thead class="TableThead">
                            <tr>
                                <th class="SrNo_th" title="Sr. No">Sr. No.</th>
                                <th title="PR Configuration Name">Configuration Details</th>
                                <th title="DR ConfigurationName">Configuration Details</th>
                            </tr>
                        </thead>
                        <tbody id="table-body">
                            @if (Model.PRDROSLevelCompareStatusList.Count() > 0)
                            {
                                int i = 1;

                                @foreach (var m in Model.PRDROSLevelCompareStatusList)
                                {



                                    <tr>

                                        <td>@i</td>

                                        <td>Prod IP Address : @m.PRIPAddress</td>

                                        <td>DR IP Address : @m.DRIPAddress</td>
                                    </tr>
                                    i++;
                                    <tr>
                                        <td>@i</td>

                                        <td>Prod System Name : @m.PRSystemName</td>

                                        <td>DR System Name : @m.DRSystemName</td>



                                    </tr>
                                    i++;
                                    <tr>
                                        <td>@i</td>
                                        <td>Prod OS Name : @m.PROSName</td>
                                        <td>DR OS Name :  @m.DROSName</td>
                                    </tr>
                                    i++;
                                    <tr>
                                        <td>@i</td>
                                        <td>Prod Build Version : @m.PRBuildVersion</td>
                                        <td>DR Build Version : @m.DRBuildVersion</td>
                                    </tr>
                                    i++;
                                    <tr>
                                        <td>@i</td>
                                        <td>Prod Processor : @m.PRProcessor</td>
                                        <td>DR Processor : @m.DRProcessor</td>
                                    </tr>
                                    i++;
                                    <tr>
                                        <td>@i</td>
                                        <td>Prod Memory : @m.PRMemory</td>
                                        <td>DR Memory : @m.DRMemory</td>
                                    </tr>
                                    i++;
                                    <tr>
                                        <td>@i</td>
                                        <td>Prod Local Drive Name : @m.PRLocalDrive</td>
                                        <td>DR Local Drive Name : @m.DRLocalDrive</td>
                                    </tr>
                                    i++;
                                    <tr>
                                        <td>@i</td>
                                        <td>Prod Shared Drive : @m.PRSharedDrive</td>
                                        <td>DR Shared Drive : @m.DRSharedDrive</td>
                                    </tr>



                                }
                            }
                        </tbody>
                    </table>

                </div>



    </from>



    @*     <div class="col-md-6" style="margin-top: 20px; margin-bottom:20px;">
    <h3 class="text-primary" style="font-weight: bold;">DR Site</h3>
    <table>

    <thead class="TableThead">
    <tr>
    <th class="SrNo_th" title="Sr. No">Sr. No.</th>
    <th title="Business Service Name">Configuration Details</th>

    </tr>
    </thead>
    <tbody id="table-body">
    @if (Model.InfraObjectlist.Count() > 0)
    {
    int j = 1;

    @foreach (var m in Model.PRDROSLevelCompareStatusList)
    {
    <tr>
    <th>
    @j
    </th>
    <th>
    @m.DRIPAddress
    </th>
    </tr>
    j++;
    }
    }
    </tbody>



    </table>


    </div>*@

</div>


    @*</div>*@
</div>

