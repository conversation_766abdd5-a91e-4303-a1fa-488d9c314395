﻿namespace ContinuityPatrol.Application.Features.EscalationMatrix.Command.Create;

public class CreateEscalationMatrixCommand : IRequest<CreateEscalationMatrixResponse>
{
    public string EscMatCode { get; set; }
    public string EscMatName { get; set; }
    public string EscMatDesc { get; set; }
    public string EscMatType { get; set; }
    public string EscMatStatus { get; set; }
    public string OwnerId { get; set; }
    public string ApproverId { get; set; }
    public string CreatedDate { get; set; }
    public string LastModifiedDate { get; set; }
    public string CompanyId { get; set; }
}