﻿using ContinuityPatrol.Application.Features.InfraObject.Events.UpdateState;

namespace ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateState;

public class
    UpdateInfraObjectStateCommandHandler : IRequestHandler<UpdateInfraObjectStateCommand,
        UpdateInfraObjectStateResponse>
{
   // private readonly IDashboardViewRepository _dashboardViewRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly ILogger<UpdateInfraObjectStateCommandHandler> _logger;
    private readonly IMapper _mapper;
    private readonly StringBuilder _message = new();
    private readonly IPublisher _publisher;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IWorkflowOperationRepository _workflowOperationRepository;

    private bool _isAllUpdated = true;
    public UpdateInfraObjectStateCommandHandler(IMapper mapper, IInfraObjectRepository infraObjectRepository,
        //IDashboardViewRepository dashboardViewRepository,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository,
        IWorkflowOperationRepository workflowOperationRepository, ILogger<UpdateInfraObjectStateCommandHandler> logger,
        IPublisher publisher)
    {
        _mapper = mapper;
        _infraObjectRepository = infraObjectRepository;
      //  _dashboardViewRepository = dashboardViewRepository;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
        _workflowOperationRepository = workflowOperationRepository;
        _logger = logger;
        _publisher = publisher;
    }

    public async Task<UpdateInfraObjectStateResponse> Handle(UpdateInfraObjectStateCommand request,
        CancellationToken cancellationToken)
    {

        foreach (var infra in request.UpdateInfraObjectStates)
            try
            {
                var eventToUpdate = await _infraObjectRepository.GetByReferenceIdAsync(infra.Id);

                if (eventToUpdate is not null && string.Equals(eventToUpdate.State?.Trim(), "locked", StringComparison.OrdinalIgnoreCase))
                {
                    _isAllUpdated = false;

                    _message.Append(
                        $"The InfraObject '{eventToUpdate.Name}' is in a locked state due to mapped licence was expired.")
                        .Append("$");
                    _logger.LogError(
                        $"The InfraObject '{eventToUpdate.Name}' is in a locked state due to mapped licence was expired.");
                    continue;
                }
                if (eventToUpdate is not null && string.Equals(eventToUpdate.State?.Trim(), "deactivate", StringComparison.OrdinalIgnoreCase))
                {
                    _isAllUpdated = false;

                    _message.Append(
                            $"The InfraObject '{eventToUpdate.Name}' is in a deactivated state because the license was deactivated by the parent company.")
                        .Append("$");
                    _logger.LogError(
                        $"The InfraObject '{eventToUpdate.Name}' is in a deactivated state because the license was deactivated by the parent company.");
                    continue;
                }

                if (infra.State.Trim().ToLower().Equals("active"))
                {
                    var infraId = await ValidateInfraObjectWorkflowStatus(infra);

                    if (infraId.Count > 0) continue;
                }


                // var dataLagDtl = await _dashboardViewRepository.GetBusinessViewByInfraObjectId(infra.Id);

                if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.InfraObject), infra.Id);

                eventToUpdate.State = infra.State;

                eventToUpdate.Reason = infra.Reason;

               // dataLagDtl.State = infra.State;

                _mapper.Map(infra, eventToUpdate, typeof(UpdateInfraObjectStateCommand),
                    typeof(Domain.Entities.InfraObject));

                await _infraObjectRepository.UpdateAsync(eventToUpdate);

               // await _dashboardViewRepository.UpdateAsync(dataLagDtl);

                await _publisher.Publish(
                    new InfraObjectStateUpdatedEvent { InfraObjectName = eventToUpdate.Name, State = infra.State },
                    cancellationToken);
            }
            catch (Exception exc)
            {
                _isAllUpdated = false;

                _message.Append(exc.Message).Append("$");

                _logger.LogError(exc, $"Exception occurred while update infraObject state {infra.State}");
            }

        string message;

        if (_isAllUpdated)
            message = request.UpdateInfraObjectStates.Any(x => x.State.Trim().ToLower().Equals("active"))
                ? "InfraObject state updated to 'Active' successfully."
                : "InfraObject state updated to 'Maintenance' successfully.";
        else
            message = _message.ToString().TrimEnd('$');

        _message.Clear();

        return new UpdateInfraObjectStateResponse
        {
            Success = _isAllUpdated,

            Message = message
        };
    }

    private async Task<List<string>> ValidateInfraObjectWorkflowStatus(UpdateInfraObjectListCommand infra)
    {
        var infraObjectId = new List<string>();

        var workflowOperation = await _workflowOperationRepository.GetWorkflowOperationByRunningStatus();

        foreach(var x in workflowOperation)
        {
            var workflowOperationGroup =await _workflowOperationGroupRepository
                .GetWorkflowOperationGroupByWorkflowOperationId(x.ReferenceId);

            workflowOperationGroup.ForEach(wfg =>
            {
                if (wfg.Status.Trim().ToLower().Equals("aborted") ||
                    wfg.Status.Trim().ToLower().Equals("completed")) return;
                if (wfg.InfraObjectId.Equals(infra.Id))
                {
                    infraObjectId.Add(infra.Id);
                    _isAllUpdated = false;
                    _message.Append(
                            $"The workflow for the InfraObject '{wfg.InfraObjectName}' is in a running state, and monitoring cannot start at the time.")
                        .Append("$");
                    _logger.LogError(
                        $"The workflow for the InfraObject '{wfg.InfraObjectName}' is in a running state, and monitoring cannot start at the time.");
                }
            });
        }
        return infraObjectId;
    }
}