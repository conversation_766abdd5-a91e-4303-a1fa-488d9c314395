﻿using ContinuityPatrol.Application.Features.AccessManager.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AccessManagerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.AccessManager.Queries;

public class GetAccessManagerPaginatedListQueryHandlerTests : IClassFixture<AccessManagerFixture>
{
    private readonly AccessManagerFixture _accessManagerFixture;
    private readonly GetAccessManagerPaginatedListQueryHandler _handler;
    private readonly Mock<IAccessManagerRepository> _mockAccessManagerRepository;

    public GetAccessManagerPaginatedListQueryHandlerTests(AccessManagerFixture accessManagerFixture)
    {
        _accessManagerFixture = accessManagerFixture;

        _accessManagerFixture.AccessManagers[0].RoleName = "Access";
        _accessManagerFixture.AccessManagers[0].Properties = "{\"CommandName\": \"Pending\", \"password\": \"CpAdmin@1234\"}";

        _accessManagerFixture.AccessManagers[1].RoleName = "Fixture";
        _accessManagerFixture.AccessManagers[1].Properties = "{\"Name\": \"Status\", \"password\": \"Admin@321\"}";

        _mockAccessManagerRepository = AccessManagerRepositoryMocks.GetPaginatedAccessManagerRepository(_accessManagerFixture.AccessManagers);

        _handler = new GetAccessManagerPaginatedListQueryHandler(_accessManagerFixture.Mapper, _mockAccessManagerRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetAccessManagerPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AccessManagerListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_AccessManagers_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetAccessManagerPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "username=Manager;accessmanagerrole=Access;properties={\"CommandName\": \"Pending\", \"password\": \"CpAdmin@1234\"}" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AccessManagerListVm>>();

        result.TotalCount.ShouldBe(1);


        result.Data[0].RoleName.ShouldBe("Access");

        result.Data[0].Properties.ShouldBe("{\"CommandName\": \"Pending\", \"password\": \"CpAdmin@1234\"}");
    }

    [Fact]
    public async Task Handle_Return_PaginatedAccessManagers_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetAccessManagerPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "RoleName" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AccessManagerListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<AccessManagerListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());


        result.Data[0].RoleName.ShouldNotBeEmpty();


        result.Data[0].Properties.ShouldNotBeEmpty(_accessManagerFixture.AccessManagers[1].Properties);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetAccessManagerPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "LKJH" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AccessManagerListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetAccessManagerPaginatedListQuery(), CancellationToken.None);

        _mockAccessManagerRepository.Verify(x => x.PaginatedListAllAsync(It.IsAny<int>(),
           It.IsAny<int>(), It.IsAny<AccessManagerFilterSpecification>(),
           It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }
}