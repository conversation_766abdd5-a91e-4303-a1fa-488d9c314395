using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ApprovalMatrixRepositoryTests : IClassFixture<ApprovalMatrixFixture>
{
    private readonly ApprovalMatrixFixture _approvalMatrixFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ApprovalMatrixRepository _repository;

    public ApprovalMatrixRepositoryTests(ApprovalMatrixFixture approvalMatrixFixture)
    {
        _approvalMatrixFixture = approvalMatrixFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new ApprovalMatrixRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var approvalMatrix = _approvalMatrixFixture.ApprovalMatrixDto;

        // Act
        var result = await _repository.AddAsync(approvalMatrix);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(approvalMatrix.Name, result.Name);
        Assert.Equal(approvalMatrix.BusinessFunctionId, result.BusinessFunctionId);
        Assert.Single(_dbContext.ApprovalMatrix);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var approvalMatrix = _approvalMatrixFixture.ApprovalMatrixDto;
        await _repository.AddAsync(approvalMatrix);

        approvalMatrix.Name = "UpdatedName";
        approvalMatrix.Description = "UpdatedDescription";

        // Act
        var result = await _repository.UpdateAsync(approvalMatrix);

        // Assert
        Assert.Equal("UpdatedName", result.Name);
        Assert.Equal("UpdatedDescription", result.Description);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.NullReferenceException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var approvalMatrix = _approvalMatrixFixture.ApprovalMatrixDto;
        await _repository.AddAsync(approvalMatrix);

        // Act
        var result = await _repository.DeleteAsync(approvalMatrix);

        // Assert
        Assert.Equal(approvalMatrix.Name, result.Name);
        Assert.Empty(_dbContext.ApprovalMatrix);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var approvalMatrix = _approvalMatrixFixture.ApprovalMatrixDto;
        var addedEntity = await _repository.AddAsync(approvalMatrix);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var approvalMatrix = _approvalMatrixFixture.ApprovalMatrixDto;
        await _repository.AddAsync(approvalMatrix);

        // Act
        var result = await _repository.GetByReferenceIdAsync(approvalMatrix.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(approvalMatrix.ReferenceId, result.ReferenceId);
        Assert.Equal(approvalMatrix.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var approvalMatrices = _approvalMatrixFixture.ApprovalMatrixList;
        await _repository.AddRange(approvalMatrices);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(approvalMatrices.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsApprovalMatrixNameUnique Tests

    [Fact]
    public async Task IsApprovalMatrixNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var approvalMatrix = _approvalMatrixFixture.ApprovalMatrixDto;
        approvalMatrix.Name = "UniqueName";
        await _repository.AddAsync(approvalMatrix);

        // Act
        var result = await _repository.IsApprovalMatrixNameUnique("UniqueName");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsApprovalMatrixNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var approvalMatrices = _approvalMatrixFixture.ApprovalMatrixList;
        await _repository.AddRange(approvalMatrices);

        // Act
        var result = await _repository.IsApprovalMatrixNameUnique("NonExistentName");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsApprovalMatrixNameExist Tests

    [Fact]
    public async Task IsApprovalMatrixNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var approvalMatrix = _approvalMatrixFixture.ApprovalMatrixDto;
        approvalMatrix.Name = "ExistingName";
        await _repository.AddAsync(approvalMatrix);

        // Act
        var result = await _repository.IsApprovalMatrixNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsApprovalMatrixNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var approvalMatrix = _approvalMatrixFixture.ApprovalMatrixDto;
        approvalMatrix.Name = "SameName";
        await _repository.AddAsync(approvalMatrix);

        // Act
        var result = await _repository.IsApprovalMatrixNameExist("SameName", approvalMatrix.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetApprovalMatrixByBusinessFunctionId Tests

    [Fact]
    public async Task GetApprovalMatrixByBusinessFunctionId_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var businessFunctionId = "BF_001";
        var approvalMatrix = _approvalMatrixFixture.ApprovalMatrixDto;
        approvalMatrix.BusinessFunctionId = businessFunctionId;
        await _repository.AddAsync(approvalMatrix);

        // Act
        var result = await _repository.GetApprovalMatrixByBusinessFunctionId(businessFunctionId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessFunctionId, result.BusinessFunctionId);
        Assert.True(result.IsActive);
    }

    [Fact]
    public async Task GetApprovalMatrixByBusinessFunctionId_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var approvalMatrices = _approvalMatrixFixture.ApprovalMatrixList;
        await _repository.AddRange(approvalMatrices);

        // Act
        var result = await _repository.GetApprovalMatrixByBusinessFunctionId("NON_EXISTENT_BF");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetApprovalMatrixByBusinessFunctionId_ShouldNotReturnInactiveEntity()
    {
        // Arrange
        var businessFunctionId = "BF_001";
        var approvalMatrix = _approvalMatrixFixture.ApprovalMatrixDto;
        approvalMatrix.BusinessFunctionId = businessFunctionId;
        approvalMatrix.IsActive = false;
        _dbContext.ApprovalMatrix.Add(approvalMatrix);

        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetApprovalMatrixByBusinessFunctionId(businessFunctionId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var approvalMatrixs = _approvalMatrixFixture.ApprovalMatrixList;
        var approvalMatrix1 = approvalMatrixs[0];
        var approvalMatrix2 = approvalMatrixs[1];

        // Act
        var task1 = _repository.AddAsync(approvalMatrix1);
        var task2 = _repository.AddAsync(approvalMatrix2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.ApprovalMatrix.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var approvalMatrices = _approvalMatrixFixture.ApprovalMatrixList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRange(approvalMatrices);
        var initialCount = approvalMatrices.Count;

        var toUpdate = approvalMatrices.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedName");
        await _repository.UpdateRange(toUpdate);

        var toDelete = approvalMatrices.Skip(2).Take(1).ToList();
        await _repository.RemoveRange(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Name == "UpdatedName").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task IsApprovalMatrixNameExist_ShouldHandleNullParameters()
    {
        // Act & Assert
        var result1 = await _repository.IsApprovalMatrixNameExist(null, "valid-guid");
        var result2 = await _repository.IsApprovalMatrixNameExist("TestName", null);
        var result3 = await _repository.IsApprovalMatrixNameExist(null, null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    [Fact]
    public async Task IsApprovalMatrixNameUnique_ShouldHandleNullParameter()
    {
        // Act
        var result = await _repository.IsApprovalMatrixNameUnique(null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task GetApprovalMatrixByBusinessFunctionId_ShouldHandleNullParameter()
    {
        // Act
        var result = await _repository.GetApprovalMatrixByBusinessFunctionId(null);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task Repository_ShouldHandleSpecialCharactersInNames()
    {
        // Arrange
        var approvalMatrix = _approvalMatrixFixture.ApprovalMatrixDto;
        approvalMatrix.Name = "Test@Name#123$%";

        // Act
        var addedMatrix = await _repository.AddAsync(approvalMatrix);
        var nameUnique = await _repository.IsApprovalMatrixNameUnique("Test@Name#123$%");
        var nameExists = await _repository.IsApprovalMatrixNameExist("Test@Name#123$%", "invalid-guid");

        // Assert
        Assert.NotNull(addedMatrix);
        Assert.Equal("Test@Name#123$%", addedMatrix.Name);
        Assert.True(nameUnique);
        Assert.True(nameExists);
    }

    #endregion
}
