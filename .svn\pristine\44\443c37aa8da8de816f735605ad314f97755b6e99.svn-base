﻿namespace ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetByLast7Days;

public record DRReadyLogByLast7DaysVm
{
    public string Id { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string LogsSchedulerId { get; set; }
    public bool IsDRReady { get; set; }
    public string Reason { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
}