using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public static class BiaRulesRepositoryMocks
{
    public static Mock<IBiaRulesRepository> CreateBiaRulesRepository(List<BiaRules> biaRules)
    {
        var mockBiaRulesRepository = new Mock<IBiaRulesRepository>();

        mockBiaRulesRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(biaRules);

        mockBiaRulesRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => biaRules.FirstOrDefault(x => x.ReferenceId == id));

        mockBiaRulesRepository.Setup(repo => repo.GetBiaRulesByEntityIdAndType(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string entityId, string type) => 
                biaRules.FirstOrDefault(x => x.EntityId == entityId && x.Type == type && x.IsActive));

        mockBiaRulesRepository.Setup(repo => repo.AddAsync(It.IsAny<BiaRules>()))
            .ReturnsAsync((BiaRules biaRule) =>
            {
                biaRule.ReferenceId = Guid.NewGuid().ToString();
                biaRule.Id = biaRules.Count + 1;
                biaRules.Add(biaRule);
                return biaRule;
            });

        mockBiaRulesRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BiaRules>()))
            .Returns((BiaRules biaRule) =>
            {
                var existingBiaRule = biaRules.FirstOrDefault(x => x.ReferenceId == biaRule.ReferenceId);
                if (existingBiaRule != null)
                {
                    existingBiaRule.Description = biaRule.Description;
                    existingBiaRule.Type = biaRule.Type;
                    existingBiaRule.EntityId = biaRule.EntityId;
                    existingBiaRule.Properties = biaRule.Properties;
                    existingBiaRule.EffectiveDateFrom = biaRule.EffectiveDateFrom;
                    existingBiaRule.EffectiveDateTo = biaRule.EffectiveDateTo;
                    existingBiaRule.IsEffective = biaRule.IsEffective;
                    existingBiaRule.RuleCode = biaRule.RuleCode;
                    existingBiaRule.IsActive = biaRule.IsActive;
                }
                return Task.CompletedTask;
            });

        mockBiaRulesRepository.Setup(repo => repo.DeleteAsync(It.IsAny<BiaRules>()))
            .Returns((BiaRules biaRule) =>
            {
                biaRules.Remove(biaRule);
                return Task.CompletedTask;
            });

        //mockBiaRulesRepository.Setup(repo => repo.PaginatedListAllAsync(
        //    It.IsAny<int>(), It.IsAny<int>(), It.IsAny<ISpecification<BiaRules>>(), 
        //    It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((int pageNumber, int pageSize, ISpecification<BiaRules> spec, string sortColumn, string sortOrder) =>
        //    {
        //        var filteredBiaRules = biaRules.Where(x => x.IsActive).ToList();
                
        //        if (spec != null)
        //        {
        //            // Apply specification filter if needed
        //            filteredBiaRules = filteredBiaRules.Where(spec.Criteria.Compile()).ToList();
        //        }

        //        var totalCount = filteredBiaRules.Count;
        //        var pagedBiaRules = filteredBiaRules
        //            .Skip((pageNumber - 1) * pageSize)
        //            .Take(pageSize)
        //            .ToList();

        //        return new PaginatedResult<BiaRules>
        //        {
        //            Data = pagedBiaRules,
        //            TotalCount = totalCount,
        //            PageNumber = pageNumber,
        //            PageSize = pageSize,
        //            TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
        //        };
        //    });

        return mockBiaRulesRepository;
    }

    public static Mock<IUserActivityRepository> CreateUserActivityRepository(List<UserActivity> userActivities)
    {
        var mockUserActivityRepository = new Mock<IUserActivityRepository>();

        mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
            .ReturnsAsync((UserActivity userActivity) =>
            {
                userActivity.Id = userActivities.Count + 1;
                userActivity.ReferenceId = Guid.NewGuid().ToString();
                userActivities.Add(userActivity);
                return userActivity;
            });

        mockUserActivityRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(userActivities);

        return mockUserActivityRepository;
    }
}
