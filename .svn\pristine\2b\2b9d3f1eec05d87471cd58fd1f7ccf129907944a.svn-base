using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DataSyncOptions.Events.Create;

public class DataSyncOptionsCreatedEventHandler : INotificationHandler<DataSyncOptionsCreatedEvent>
{
    private readonly ILogger<DataSyncOptionsCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DataSyncOptionsCreatedEventHandler(ILoggedInUserService userService, ILogger<DataSyncOptionsCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(DataSyncOptionsCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} DataSyncOptionsProperties",
            Entity = "DataSyncProperties",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"DataSync Options '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"DataSync Options '{createdEvent.Name}' created successfully.");
    }
}