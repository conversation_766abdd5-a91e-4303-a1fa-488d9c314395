﻿using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Create;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.TestConnection;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Update;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateDefault;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateNodeStatus;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetNamesByType;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.LoadBalancerModel;
using ContinuityPatrol.Domain.ViewModels.StateMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;


namespace ContinuityPatrol.Shared.Services.Contract;

public interface ILoadBalancerService
{
    Task<BaseResponse> CreateAsync(CreateLoadBalancerCommand createNodeConfigurationCommand);
    Task<BaseResponse> TestConnection(LoadBalancerTestConnectionCommand nodeConfigurationTestConnectionCommand);
    Task<BaseResponse> UpdateAsync(UpdateLoadBalancerCommand updateNodeConfigurationCommand);
    Task<BaseResponse> DeleteAsync(string loadBalancerId);
    Task<List<LoadBalancerListVm>> GetLoadBalancerList();
    Task<bool> IsLoadBalancerNameExist(string loadBalancerName, string id);
    Task<LoadBalancerDetailVm> GetLoadBalancerById(string id);
    Task<PaginatedResult<LoadBalancerListVm>> GetPaginatedNodeConfigurations(GetLoadBalancerPaginatedListQuery query);
    Task<List<StateMonitorStatusListVm>> GetStateMonitorStatusList();
    Task<bool> IsIpAddressAndPortExist(string ipAddress, int port, string id);
    Task<UpdateNodeStatusResponse> UpdateNodeStatus(UpdateNodeStatusCommand command);
    Task<UpdateLoadBalancerDefaultResponse> UpdateDefault(UpdateLoadBalancerDefaultCommand command);
    Task<List<LoadBalancerNameVm>> GetLoadBalancerNamesByType(string type=null);
}