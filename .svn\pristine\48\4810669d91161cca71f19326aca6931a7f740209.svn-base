﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Server.Events.InfraSummaryEvents.Create;

public class ServerInfraSummaryCreatedEventHandler : INotificationHandler<ServerInfraSummaryCreatedEvent>
{
    private readonly IInfraSummaryRepository _infraSummaryRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ILogger<ServerInfraSummaryCreatedEventHandler> _logger;

    public ServerInfraSummaryCreatedEventHandler(ILogger<ServerInfraSummaryCreatedEventHandler> logger,
        IInfraSummaryRepository infraSummaryRepository, ILoggedInUserService loggedInUserService)
    {
        _logger = logger;
        _infraSummaryRepository = infraSummaryRepository;
        _loggedInUserService = loggedInUserService;
    }

    public async Task Handle(ServerInfraSummaryCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var infraSummary =
            await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(createdEvent.Type,
                createdEvent.BusinessServiceId, createdEvent.CompanyId);

        if (infraSummary is null)
        {
            await _infraSummaryRepository.AddAsync(new Domain.Entities.InfraSummary
            {
                EntityName = Modules.Server.ToString(),
                TypeId = createdEvent.TypeId,
                Type = createdEvent.Type,
                Logo = createdEvent.Logo,
                Count = 1,
                BusinessServiceId = createdEvent.BusinessServiceId,
                CompanyId = createdEvent.CompanyId
            });
        }
        else
        {
            infraSummary.EntityName = Modules.Server.ToString();
            infraSummary.Logo = createdEvent.Logo;
            infraSummary.Count += 1;
            infraSummary.BusinessServiceId = createdEvent.BusinessServiceId;
            infraSummary.CompanyId = createdEvent.CompanyId;
            await _infraSummaryRepository.UpdateAsync(infraSummary);
        }

        _logger.LogInformation($"InfraSummary '{createdEvent.Type}' created successfully.");
    }
}