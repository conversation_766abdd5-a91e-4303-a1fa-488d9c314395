﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetServiceTopologyList;

public class
    GetServiceTopologyListQueryHandler : IRequestHandler<GetServiceTopologyListQuery, List<GetServiceTopologyListVm>>
{
    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IDatabaseViewRepository _databaseViewRepository;
    private readonly IInfraObjectViewRepository _infraObjectViewRepository;
    private readonly IMapper _mapper;
    private readonly IReplicationViewRepository _replicationViewRepository;
    private readonly IServerViewRepository _serverViewRepository;

    public GetServiceTopologyListQueryHandler(IMapper mapper, IBusinessServiceRepository businessServiceRepository,
        IInfraObjectViewRepository infraObjectViewRepository
        , IBusinessFunctionRepository businessFunctionRepository,
        IServerViewRepository serverViewRepository, IDatabaseViewRepository databaseViewRepository
        , IReplicationViewRepository replicationViewRepository)
    {
        _mapper = mapper;
        _businessServiceRepository = businessServiceRepository;
        _infraObjectViewRepository = infraObjectViewRepository;
        _businessFunctionRepository = businessFunctionRepository;
        _serverViewRepository = serverViewRepository;
        _databaseViewRepository = databaseViewRepository;
        _replicationViewRepository = replicationViewRepository;
    }

    public async Task<List<GetServiceTopologyListVm>> Handle(GetServiceTopologyListQuery request,
        CancellationToken cancellationToken)
    {
        var businessService = await _businessServiceRepository.ListAllAsync();

        if (request.BusinessServiceId.IsNotNullOrWhiteSpace())
            businessService = businessService.Where(x => x.ReferenceId.Equals(request.BusinessServiceId)).ToList();

        var serviceTopologyList = new List<GetServiceTopologyListVm>();

        var service = new GetServiceTopologyListVm();
        var databases = new List<DatabaseTopologyList>();
        var infraObjectList = new List<InfraObjectTopologyList>();
        var serverIds = new List<string>();

        foreach (var bs in businessService)
        {
            var json = JObject.Parse(bs.SiteProperties);
            var propertyNames = GetPropertyNames(json);
            foreach (var propertyName in propertyNames)
            {

                service.SiteId = json.SelectToken($"{propertyName}.Id")?.ToString() ?? "NA";
                service.SiteName = json.SelectToken($"{propertyName}.Name")?.ToString() ?? "NA";
                service.SiteType = propertyName;

                serverIds.Add(service.SiteId);
            }
        }
        var servers = await _serverViewRepository.GetServerBySiteIds(serverIds);

        service.ServerTopologyLists = _mapper.Map<List<ServerTopologyList>>(servers);

        var database = await _databaseViewRepository.GetDatabaseByServerIds(serverIds);

        databases.AddRange(_mapper.Map<List<DatabaseTopologyList>>(database));

        service.DatabaseTopologyLists.AddRange(databases);

        var replications = await _replicationViewRepository.GetReplicationBySiteId(service.SiteId);

        service.ReplicationTopologyLists = _mapper.Map<List<ReplicationTopologyList>>(replications);

        var businessServiceIds = businessService.Select(x => x.ReferenceId).ToList();

        var businessFunctionIds = (await _businessFunctionRepository.GetByBusinessServiceIds(businessServiceIds)).Select(x=>x.ReferenceId).ToList();

        var infraObject = await _infraObjectViewRepository.GetInfraObjectListByBusinessFunctionIds(businessFunctionIds);

        infraObjectList.AddRange(_mapper.Map<List<InfraObjectTopologyList>>(infraObject));

        var infraDto = infraObjectList.DistinctBy(x => x.InfraObjectId).ToList();

        var replicationStatusValues = infraDto
                    .Where(item => item?.ReplicationStatus != null)
                    .Select(item => (int)item?.ReplicationStatus)
                    .Distinct().ToList();

        var drOperationStatusValues = infraDto
            .Where(item => item?.DROperationStatus != null)
            .Select(item => (int)item?.DROperationStatus)
            .Distinct().ToList();

        service.ReplicationStatus.AddRange(replicationStatusValues);
        service.DROperationStatus.AddRange(drOperationStatusValues);


        service.InfraObjectTopologyLists.AddRange(infraObjectList.DistinctBy(x => x.InfraObjectId));

        serviceTopologyList.AddRange(service);


        return serviceTopologyList;
    }

    private static List<string> GetPropertyNames(JObject json)
    {
        var propertyNames = new List<string>();
        foreach (var property in json.Properties()) propertyNames.Add(property.Name);
        return propertyNames;
    }
}

#region Old Code

//        foreach (var bs in businessService)
//        {
//            var json = JObject.Parse(bs.SiteProperties);

//var propertyNames = GetPropertyNames(json);
//            foreach (var propertyName in propertyNames)
//            {
//                var service = new GetServiceTopologyListVm();

//service.SiteId = json.SelectToken($"{propertyName}.Id")?.ToString() ?? "NA";
//                service.SiteName = json.SelectToken($"{propertyName}.Name")?.ToString() ?? "NA";
//                service.SiteType = propertyName;

//                var server = await _serverViewRepository.GetServerBySiteId(service.SiteId);

//service.ServerTopologyLists = _mapper.Map<List<ServerTopologyList>>(server);

//                var databases = new List<DatabaseTopologyList>();

//                foreach (var ser in server)
//                {
//                    var database = await _databaseViewRepository.GetDatabaseByServerId(ser.ReferenceId);

//databases.AddRangeAsync(_mapper.Map<List<DatabaseTopologyList>>(database));
//                };

//service.DatabaseTopologyLists.AddRangeAsync(databases);

//var replications = await _replicationViewRepository.GetReplicationBySiteId(service.SiteId);

//service.ReplicationTopologyLists = _mapper.Map<List<ReplicationTopologyList>>(replications);

//var businessFunction = await _businessFunctionRepository
//    .GetBusinessFunctionListByBusinessServiceId(bs.ReferenceId);
//var infraObjectList = new List<InfraObjectTopologyList>();

//foreach (var bf in businessFunction)
//{
//    var infraObject = await _infraObjectViewRepository.GetInfraObjectByBusinessFunctionId(bf.ReferenceId);

//    infraObjectList.AddRangeAsync(_mapper.Map<List<InfraObjectTopologyList>>(infraObject));
//};

//var infraDto = infraObjectList.DistinctBy(x => x.InfraObjectId).ToList();

//var replicationStatusValues = infraDto
//    .Where(item => item?.ReplicationStatus != null)
//    .Select(item => (int)item?.ReplicationStatus)
//    .Distinct().ToList();

//var drOperationStatusValues = infraDto
//    .Where(item => item?.DROperationStatus != null)
//    .Select(item => (int)item?.DROperationStatus)
//    .Distinct().ToList();

//service.ReplicationStatus.AddRangeAsync(replicationStatusValues);
//service.DROperationStatus.AddRangeAsync(drOperationStatusValues);


//service.InfraObjectTopologyLists.AddRangeAsync(infraObjectList.DistinctBy(x => x.InfraObjectId));

//serviceTopologyList.AddRangeAsync(service);
//            }
//        };

#endregion