using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.ArchiveModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.Archive.Queries.GetPaginatedList;

public class
    GetArchivePaginatedListQueryHandler : IRequestHandler<GetArchivePaginatedListQuery, PaginatedResult<ArchiveListVm>>
{
    private readonly IArchiveRepository _archiveRepository;
    private readonly IMapper _mapper;

    public GetArchivePaginatedListQueryHandler(IMapper mapper, IArchiveRepository archiveRepository)
    {
        _mapper = mapper;
        _archiveRepository = archiveRepository;
    }

    public async Task<PaginatedResult<ArchiveListVm>> Handle(GetArchivePaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new ArchiveFilterSpecification(request.SearchString);

        var queryable = await _archiveRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var archiveList = _mapper.Map<PaginatedResult<ArchiveListVm>>(queryable);

        return archiveList;

        //var queryable = _archiveRepository.GetPaginatedQuery();

        //var productFilterSpec = new ArchiveFilterSpecification(request.SearchString);

        //var archiveList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<ArchiveListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return archiveList;
    }
}