using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using System.Threading.Tasks;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class AlertRepositoryTests : IClassFixture<AlertFixture>,IClassFixture<InfraObjectFixture>
{
    private readonly AlertFixture _alertFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly AlertRepository _repository;
    private readonly InfraObjectFixture _infraObjectFixture;    

    public AlertRepositoryTests(AlertFixture alertFixture, InfraObjectFixture infraObjectFixture)
    {
        _alertFixture = alertFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new AlertRepository(_dbContext, DbContextFactory.GetMockUserService());
        _infraObjectFixture = infraObjectFixture;
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var alert = _alertFixture.AlertDto;

        // Act
      await   _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(alert.ReferenceId); 
        // Assert
        Assert.NotNull(result);
        Assert.Equal(alert.Type, result.Type);
        Assert.Equal(alert.Severity, result.Severity);
        Assert.Equal(alert.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.Alerts);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var alert = _alertFixture.AlertDto;
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        // Act
        alert.Type = "Updated Type";
        alert.Severity = "Critical";
        alert.IsResolve = 1;

         _dbContext.Alerts.Update(alert);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.GetByReferenceIdAsync(alert.ReferenceId);

        // Assert
        Assert.Equal("Updated Type", result.Type);
        Assert.Equal("Critical", result.Severity);
        Assert.Equal(1, result.IsResolve);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var alert = _alertFixture.AlertDto;
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        // Act

        alert.IsActive = false;
         _dbContext.Alerts.Update(alert);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var alert = _alertFixture.AlertDto;
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(alert.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(alert.Id, result.Id);
        Assert.Equal(alert.Type, result.Type);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnFirstEntity_WhenIdIsZero()
    {
        // Arrange
        var alerts = _alertFixture.AlertList.Take(3).ToList();
        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repository.GetByIdAsync(0);

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetAlertByMaxId Tests

    [Fact]
    public async Task GetAlertByMaxId_ShouldReturnMaxId_WhenAlertsExist()
    {
        // Arrange
        var alerts = _alertFixture.AlertList.Take(5).ToList();
        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repository.GetAlertByMaxId();

        // Assert
        Assert.True(result > 0);
    }

    [Fact]
    public async Task GetAlertByMaxId_ShouldReturnZero_WhenNoAlertsExist()
    {
        // Act
        var result = await _repository.GetAlertByMaxId();

        // Assert
        Assert.Equal(0, result);
    }

    #endregion

    #region GetAlertByInfraObjectId Tests

    [Fact]
    public async Task GetAlertByInfraObjectId_ShouldReturnMatchingAlerts()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var entityId = "ENTITY_001";
        
        var alerts = new List<Alert>
        {
            new Alert 
            { 
                InfraObjectId = infraObjectId, 
                EntityId = entityId, 
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type1"
            },
            new Alert 
            { 
                InfraObjectId = infraObjectId, 
                EntityId = entityId, 
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type2"
            },
            new Alert 
            { 
                InfraObjectId = "INFRA_002", 
                EntityId = entityId, 
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type3"
            }
        };

        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repository.GetAlertByInfraObjectId(infraObjectId, entityId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(infraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.Equal(entityId, x.EntityId));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetAlertByInfraObjectId_ShouldReturnEmpty_WhenNoMatch()
    {
        // Arrange
        var alerts = _alertFixture.AlertList;
        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repository.GetAlertByInfraObjectId("NON_EXISTENT_INFRA", "NON_EXISTENT_ENTITY");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetAlertByClientAlertId Tests

    [Fact]
    public async Task GetAlertByClientAlertId_ShouldReturnMatchingAlerts()
    {
        // Arrange
        var clientAlertId = "CLIENT_ALERT_001";
        
        var alerts = new List<Alert>
        {
            new Alert 
            { 
                ClientAlertId = clientAlertId, 
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type1"
            },
            new Alert 
            { 
                ClientAlertId = clientAlertId, 
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type2"
            },
            new Alert 
            { 
                ClientAlertId = "CLIENT_ALERT_002", 
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type3"
            }
        };

        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repository.GetAlertByClientAlertId(clientAlertId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(clientAlertId, x.ClientAlertId));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetAlertByClientAlertId_ShouldReturnEmpty_WhenNoMatch()
    {
        // Arrange
        var alerts = _alertFixture.AlertList;
        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repository.GetAlertByClientAlertId("NON_EXISTENT_CLIENT_ALERT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetLastAlertByInfraObject Tests

    [Fact]
    public async Task GetLastAlertByInfraObject_ShouldReturnAlertsInDateRange()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var baseDate = DateTime.Now.Date;
        var userLastAlertDate = baseDate.AddDays(-5);
        var alertDate = baseDate.AddDays(5);

        var alerts = new List<Alert>
        {
            new Alert
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(-3),
                LastModifiedDate = baseDate.AddDays(-2),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "InRange1"
            },
            new Alert
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(-1),
                LastModifiedDate = baseDate.AddDays(1),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "InRange2"
            },
            new Alert
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(-10),
                LastModifiedDate = baseDate.AddDays(-8),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "OutOfRange"
            }
        };

        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetLastAlertByInfraObject(infraObjectId, userLastAlertDate, alertDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(infraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.True(x.CreatedDate >= userLastAlertDate));
        Assert.All(result, x => Assert.True(x.LastModifiedDate <= alertDate));
    }

    #endregion

    #region GetAlertByUserLastAlertId Tests

    [Fact]
    public async Task GetAlertByUserLastAlertId_ShouldReturnAlertsAfterGivenId_WhenPreviousAlertsExist()
    {
        // Arrange
        var alerts = _alertFixture.AlertPaginationList.Take(5).ToList();
        await _repository.AddRangeAsync(alerts);

        var addedAlerts = await _repository.ListAllAsync();
        var sortedAlerts = addedAlerts.OrderBy(x => x.Id).ToList();
        var userLastAlertId = sortedAlerts[2].Id; 

        // Act
        var result = await _repository.GetAlertByUserLastAlertId(userLastAlertId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count >= 1);
        Assert.All(result, x => Assert.True(x.Id > userLastAlertId));
    }

    [Fact]
    public async Task GetAlertByUserLastAlertId_ShouldReturnAlertsFromGivenId_WhenNoPreviousAlerts()
    {
        // Arrange
        var alerts = _alertFixture.AlertList.Take(3).ToList();
        await _repository.AddRangeAsync(alerts);

        var addedAlerts = await _repository.ListAllAsync();
        var firstAlertId = addedAlerts.Min(x => x.Id);

        // Act
        var result = await _repository.GetAlertByUserLastAlertId(firstAlertId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.True(x.Id >= firstAlertId));
    }

    #endregion

    #region GetAlertByUserLastInfraObjectId Tests

    [Fact]
    public async Task GetAlertByUserLastInfraObjectId_ShouldReturnAlertsAfterDate()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var baseDate = DateTime.Now.Date;
        var createdDate = baseDate.AddDays(-2);

        var alerts = new List<Alert>
        {
            new Alert
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(-1),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "AfterDate"
            },
            new Alert
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(-5),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "BeforeDate"
            }
        };

         _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetAlertByUserLastInfraObjectId(infraObjectId, createdDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("AfterDate", result.First().Type);
        Assert.True(result.First().CreatedDate >= createdDate);
    }

    #endregion

    #region GetAlertListFilterByDate Tests

    [Fact]
    public async Task GetAlertListFilterByDate_ShouldReturnAlertsInDateRange()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = baseDate.AddDays(5).ToString("yyyy-MM-dd");

        var alerts = new List<Alert>
        {
            new Alert
            {
                CreatedDate = baseDate.AddDays(-3),
                LastModifiedDate = baseDate.AddDays(-2),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "InRange"
            },
            new Alert
            {
                CreatedDate = baseDate.AddDays(-10),
                LastModifiedDate = baseDate.AddDays(-8),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "OutOfRange"
            }
        };

        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetAlertListFilterByDate(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("InRange", result.First().Type);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public void PaginatedListAllAsync_ShouldReturnQueryable()
    {
        // Arrange
        var alerts = _alertFixture.AlertPaginationList;
        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedQuery();;

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count() > 0);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldFilterByCompanyId_WhenNotParent()
    {
        var infra = _infraObjectFixture.InfraObjectList;
        infra.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        await _dbContext.InfraObjects.AddRangeAsync(infra);

        var repository = new AlertRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());

        var alerts = _alertFixture.AlertPaginationList;
        // Set some with different company IDs
        alerts.Take(5).ToList().ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        alerts.Take(5).ToList().ForEach(x => x.InfraObjectId = infra[0].ReferenceId);

        alerts[3].InfraObjectId = infra[1].ReferenceId;
        alerts[4].InfraObjectId = infra[2].ReferenceId;
        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();

        // Act
        var result = repository.GetPaginatedQuery();;

        // Assert
        Assert.NotNull(result);
        var filteredResults = result.ToList();
        Assert.All(filteredResults, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    #endregion

    #region GetPaginatedBySeverity Tests

    [Fact]
    public void GetPaginatedBySeverity_ShouldReturnFilteredAlerts()
    {
        // Arrange
        var alerts = _alertFixture.AlertList;

        alerts[0].Severity = "Critical";

        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedBySeverity("Critical");

        // Assert
        Assert.NotNull(result);
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Equal("Critical", filteredResults.First().Severity);
    }

    #endregion

    #region GetPaginatedByType Tests

    [Fact]
    public void GetPaginatedByType_ShouldReturnFilteredAlerts()
    {
        // Arrange
        var type = "Database";
        var alerts = _alertFixture.AlertList;
        alerts[0].Type = type;

        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedByType(type);

        // Assert
        Assert.NotNull(result);
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Equal(type, filteredResults.First().Type);
    }

    #endregion

    #region GetPaginatedByInfraObjectId Tests

    [Fact]
    public void GetPaginatedByInfraObjectId_ShouldReturnFilteredAlerts()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var alerts = _alertFixture.AlertList;
        alerts[0].InfraObjectId = infraObjectId;

        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Equal(infraObjectId, filteredResults.First().InfraObjectId);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var alerts = _alertFixture.AlertList;
        var alert1 = alerts[0];
        var alert2 = alerts[1];


        // Act
        
        await _dbContext.Alerts.AddAsync(alert1);
        await _dbContext.Alerts.AddAsync(alert2);
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, results.Count);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.Alerts.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var alerts = _alertFixture.AlertList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(alerts);
        var initialCount = alerts.Count;

        var toUpdate = alerts.Take(2).ToList();
        toUpdate.ForEach(x => x.Type = "Updated Type");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = alerts.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Type == "Updated Type").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
