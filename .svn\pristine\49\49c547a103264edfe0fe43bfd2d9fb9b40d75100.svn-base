﻿namespace ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterByAlertName;

public class
    GetAlertMasterByAlertNameQueryHandler : IRequestHandler<GetAlertMasterByAlertNameQuery,
        List<AlertMasterByAlertNameVm>>
{
    private readonly IAlertMasterRepository _alertMasterRepository;
    private readonly IMapper _mapper;

    public GetAlertMasterByAlertNameQueryHandler(IMapper mapper, IAlertMasterRepository alertMasterRepository)
    {
        _mapper = mapper;
        _alertMasterRepository = alertMasterRepository;
    }

    public async Task<List<AlertMasterByAlertNameVm>> Handle(GetAlertMasterByAlertNameQuery request,
        CancellationToken cancellationToken)
    {
        var alertMaster = await _alertMasterRepository.GetAlertMasterByAlertName(request.AlertName);

        Guard.Against.NullOrDeactive(alertMaster, nameof(Domain.Entities.AlertMaster),
            new NotFoundException(nameof(Domain.Entities.AlertMaster), request.AlertName));

        var alertMasterDetailDto = _mapper.Map<List<AlertMasterByAlertNameVm>>(alertMaster);

        return alertMasterDetailDto;
    }
}