﻿
function monitorTypeMssqlNLS(value, infraObjectName, moniterType, parsedData) {
    let prWfName = [], drWfName = [];
    let prStatusArr = [], drStatusArr = [];
    let prWfDisplay = '--', drWfDisplay = '--';
    let prStatusDisplay = '--', drStatusDisplay = '--';
    let iconWF = '', iconStatus = '';
    let monitor = value?.monitorServiceDetails;

    if (value?.monitorServiceDetails?.length > 0) {
        value?.monitorServiceDetails?.forEach(list => {
            let parsed = [];
            const isValidJson = list?.isServiceUpdate && Array.isArray(list?.isServiceUpdate)

            if (isValidJson) {
                try {
                    parsed = JSON?.parse(list?.isServiceUpdate);
                } catch (err) {
                    console.warn('Invalid JSON in isServiceUpdate:', list?.isServiceUpdate);
                    parsed = [];
                }
            }
            parsed?.forEach(entry => {
                entry?.Services?.forEach(service => {
                    if (entry?.Type?.toLowerCase() === 'pr') {
                        if (service?.ServiceName && service?.ServiceName !== 'NA') {
                            prWfName.push(service?.ServiceName);
                        }
                        if (service?.Status && service?.Status !== 'NA') {
                            prStatusArr.push(service?.Status);
                        }
                    } else if (entry?.Type?.toLowerCase() === 'dr') {
                        if (service?.ServiceName && service?.ServiceName !== 'NA') {
                            drWfName.push(service?.ServiceName);
                        }
                        if (service?.Status && service?.Status !== 'NA') {
                            drStatusArr.push(service?.Status);
                        }
                    }
                });
            });
        });

        // Unique workflow names
        prWfDisplay = prWfName?.length > 0 ? [...new Set(prWfName)].join(', ') : '--';
        drWfDisplay = drWfName?.length > 0 ? [...new Set(drWfName)].join(', ') : '--';

        // Status summary
        function getStatusSummary(arr) {
            let countMap = {};
            arr?.forEach(status => {
                countMap[status] = (countMap[status] || 0) + 1;
            });
            let total = arr?.length;
            let statusSummary = Object.entries(countMap)
                .map(([status, count]) => `${count} ${status}`)
                .join(', ');
            return statusSummary ? `${statusSummary} / ${total}` : '--';
        }

        prStatusDisplay = getStatusSummary(prStatusArr);
        drStatusDisplay = getStatusSummary(drStatusArr);

        iconWF = (prWfDisplay !== '--' || drWfDisplay !== '--') ? '<i class="text-primary cp-monitoring-services me-1 fs-6"></i>' : '';

        iconStatus = (prStatusDisplay !== '--' || drStatusDisplay !== '--') ? '<i class="text-primary cp-Job-status me-1 fs-6"></i>' : '';

    }
    const getDRDetails = (data, value, obj = null) => {
        
        let tdHtml = '';
        data.forEach((item, i) => {
             let iconClass = getIconClass(value, item);
            let tableData = obj ? item?.MonitoringModel[obj][value] : item?.MonitoringModel[value];

            tdHtml += `<td class="text-truncate"><i class="${iconClass} me-1"></i>${tableData || 'NA'}</td>`
        })
        return tdHtml
    }

    const getIconClass = (value, monitoringData) => {
        let iconClass = '';
        
        if (value == 'Server_Name') {
            iconClass = 'cp-stand-server text-primary'

        } else if (value === 'Server_IpAddress' || value === 'Server_HostName') {
            let text = monitoringData?.MonitoringModel?.Server_Status?.toLowerCase()
            iconClass = text === 'down' ? "cp-down-linearrow me-1 text-danger" : text === 'pending' ? 'cp-pending me-1 text-warning' : text === 'up' ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";

        } else if (value === 'Database_Name') {
          
            iconClass = monitoringData?.MonitoringModel?.Database_Name ? 'cp-database me-1 text-primary' : "cp-disable me-1 text-danger";

        } else if (value === 'Last_Backup_Transaction_Log') {
            iconClass = monitoringData?.MonitoringModel?.Last_Backup_Transaction_Log?.toLowerCase()?.includes('na') ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.Last_Backup_Transaction_Log ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'Last_Restored_Transaction_Log') {
            iconClass = monitoringData?.MonitoringModel?.Last_Restored_Transaction_Log?.toLowerCase()?.includes('na') ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.Last_Restored_Transaction_Log ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'LastLSN_backup') {
            iconClass = monitoringData?.MonitoringModel?.LastLSN_backup?.toLowerCase()?.includes('na') ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.LastLSN_backup ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'LastLSN_copied') {
            iconClass = monitoringData?.MonitoringModel?.LastLSN_copied?.toLowerCase()?.includes('na') ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.LastLSN_copied ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'last_copied_file') {
            iconClass = monitoringData?.MonitoringModel?.last_copied_file?.toLowerCase()?.includes('na') ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.last_copied_file ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'LastLSN_restored') {
            iconClass = monitoringData?.MonitoringModel?.LastLSN_restored?.toLowerCase()?.includes('na') ? "cp-disable me-1 text-danger" : monitoringData?.MonitoringModel?.LastLSN_restored ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";

        }

        return iconClass;
    }

    const getDynamicHeader = (NlsOnMonitoringModels) => {

        let dynamicHeader = '';

        NlsOnMonitoringModels?.length && NlsOnMonitoringModels?.map((data) => {
            dynamicHeader += `<th>${data?.Type}</th>`
        })

        return dynamicHeader;
    }

    if (moniterType?.toLowerCase() === "mssqlnls") {        
        let prStatus = parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Server_Status
        let ipOrHostName;
        let repType = value?.replicationType ? 'cp-replication-type me-1 text-primary' : 'cp-disable me-1 text-danger'
        let rep = value?.replicationType !== null && value?.replicationType !== "" ? value?.replicationType : 'NA'
        let application = value?.monitorServiceDetails[0]?.isServiceUpdate?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.isServiceUpdate?.toLowerCase()?.includes("running") ? "text-success cp-reload cp-animate" : "text-danger cp-fail-back";
        let workflow = value?.monitorServiceDetails[0]?.workflowName?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.workflowName ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
        let pripaddress = prStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : prStatus?.toLowerCase() === "pending" ? "cp-pending me-1 text-warning" : prStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
        let dripaddress = value?.drServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending me-1 text-warning" : value?.drServerStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
        let prdatabase = parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Database_Name ? "cp-database me-1 text-primary" : "cp-disable me-1 text-danger";
        let drdatabase = parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_backup?.toLowerCase()?.includes('na') ? "cp-disable me-1 text-danger" : parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_backup ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";
        let prprimarystate = parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Last_Backup_Transaction_Log?.toLowerCase()?.includes('na') ? "cp-disable me-1 text-danger" : parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Last_Backup_Transaction_Log ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";
        let drprimarytate = parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_copied?.toLowerCase()?.includes('na') ? "cp-disable me-1 text-danger" : parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_copied ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";
        let prconnectstate = parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_restored?.toLowerCase()?.includes('na') ? "cp-disable me-1 text-danger" : parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_restored ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";
        let drconnectstate = parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRlast_copied_file?.toLowerCase()?.includes('na') ? "cp-disable me-1 text-danger" : parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRlast_copied_file ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";
        let ipprdata = parsedData?.PrNlsMonitoringModel?.MonitoringModel?.Pr_ConnectViaHostName.toLowerCase() === "true" ? parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Server_HostName : parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Server_IpAddress
        let drdata = parsedData.NlsOnMonitoringModels.map((ip) => ip.MonitoringModel.connectViaHostName);
        parsedData?.NlsOnMonitoringModels.forEach((ip, index) => {

            let isHostName = drdata[index]?.toLowerCase() === "true";
            value = isHostName ? 'Server_HostName' : 'Server_IpAddress';
            ipOrHostName = isHostName
                ? getDRDetails(parsedData?.NlsOnMonitoringModels, 'Server_HostName')
                : getDRDetails(parsedData?.NlsOnMonitoringModels, 'Server_IpAddress');
        });
        
        let infraobjectdata =
            '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
            '<thead ="dynamic_site_header" style="position: sticky;top: 0px;">' +
            '<tr>' +
            '<th>Component Monitor</th>' +
            '<th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.NlsOnMonitoringModels)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td class="text-truncate">' + "Server Name" + '</td>' +
            '<td class="text-truncate">' + '<i class="cp-stand-server me-1 text-primary"></i>' + (parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Server_Name !== null && parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Server_Name !== "" ? parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Server_Name : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.NlsOnMonitoringModels, 'Server_Name')}` +

            '</tr>' +
            '<tr>' +
            '<td>' + 'IP Address/HostName' + '</td>' +
            '<td>' + '<i class="' + pripaddress + '"></i>' + (ipprdata|| 'NA') + '</td>' +
            `${ipOrHostName}` +    
            '</tr>' +
            '<tr>' +
            '<td>' + 'Database Name' + '</td>' +
            '<td>' + '<i class="' + prdatabase + '"></i>' + (parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Database_Name !== null && parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Database_Name !== "" ? parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Database_Name : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.NlsOnMonitoringModels, 'Database_Name')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Last Generated transcation Log" + '</td>' +
            '<td>' + '<i class="' + prprimarystate + '"></i>' + (parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Last_Backup_Transaction_Log !== null && parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Last_Backup_Transaction_Log !== "" ? parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PR_Last_Backup_Transaction_Log : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.NlsOnMonitoringModels, 'Last_Backup_Transaction_Log')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Last Applied transcation Log" + '</td>' +
            '<td>' + '' + ('--') + '</td>' +
            `${getDRDetails(parsedData?.NlsOnMonitoringModels, 'Last_Restored_Transaction_Log')}` +

            '</tr>';
        if (Array.isArray(monitor) && monitor?.length > 0) {
            infraobjectdata +=
                '<tr id="prWorkflow">' +
                '<td>Monitoring workflow</td>' +
                '<td>' + iconWF + prWfDisplay + '</td>' +
                '<td>' + iconWF + drWfDisplay + '</td>' +
                '</tr>' +
                '<tr id="prStatus">' +
                '<td>Application Status</td>' +
                '<td>' + iconStatus + prStatusDisplay + '</td>' +
                '<td>' + iconStatus + drStatusDisplay + '</td>' +
                '</tr>';
        }

        infraobjectdata += generateMonitorServiceDetailsRow(workflow, application, value.monitorServiceDetails);

        infraobjectdata += '</tbody>' +
            '</table>' +
            '</div>' +
            '<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
            '<thead style="position: sticky;top: 0px;z-index: 1;">' +
            '<tr>' +
            '<th>Replication Monitor</th>' +
            '<th>Production Server</th>' +
          
            `${getDynamicHeader(parsedData?.NlsOnMonitoringModels)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + "Replication Type" + '</td>' + 
            '<td>' + '<i class="' + repType + '"></i>' + rep + '</td>' +
            
            '</tr>' +
            '<tr>' +
            '<td>' + "LSN of Last Backup Log" + '</td>' +
            '<td>' + '<i class="' + drdatabase +'"></i>' + (parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_backup !== null && parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_backup !== "" ? parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_backup : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.NlsOnMonitoringModels, 'LastLSN_backup')}` +

            '</tr>' +
            '<tr>' +
            '<td>' + "LSN of Last Copied Log" + '</td>' +
            '<td>' + '<i class="' + drprimarytate + '"></i>' + (parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_copied !== null && parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_copied !== "" ? parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_copied : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.NlsOnMonitoringModels, 'LastLSN_copied')}` +

            '</tr>' +
            '<tr>' +
            '<td>' + "Last Copied Log" + '</td>' +
            '<td>' + '<i class="' + drconnectstate + '"></i>' + (parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRlast_copied_file !== null && parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRlast_copied_file !== "" ? parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRlast_copied_file : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.NlsOnMonitoringModels, 'last_copied_file')}` +

            '</tr>' +
            '<tr>' +
            '<td>' + "LSN of Last Restored Log" + '</td>' +
            '<td>' + '<i class="' + prconnectstate + '"></i>' + (parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_restored !== null && parsedData?.PrNlsMonitoringModel?.MonitoringModel?.DRLastLSN_restored !== "" ? parsedData?.PrNlsMonitoringModel?.MonitoringModel?.PRLastLSN_restored : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.NlsOnMonitoringModels, 'LastLSN_restored')}` +

            '</tr>' +
            '</tbody style="">' +
            '</table>' +
            '</div>'



        setTimeout(() => {
            $("#infraobjectalldata").append(infraobjectdata);
        }, 200)


    }

}
//function monitorTypeMssqlNLS(value, infraObjectName, moniterType, parsedData, commondata) {
    
//    if (moniterType?.toLowerCase() === "mssqlnls") {
//        let application = value?.monitorServiceDetails[0]?.isServiceUpdate?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.isServiceUpdate.toLowerCase().includes("running") ? "text-success cp-reload cp-animate" : "text-danger cp-fail-back";
//        let workflow = value?.monitorServiceDetails[0]?.workflowName?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.workflowName ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
//        let pripaddress = value?.prServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : value?.prServerStatus?.toLowerCase() === "pending" ? "cp-pending me-1 text-warning" : value?.prServerStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
//        let dripaddress = value?.drServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending me-1 text-warning" : value?.drServerStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
//        let prhealthstate = parsedData?.DatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_Health_Status?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.DatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_Health_Status?.toLowerCase() === "healthy" ? "cp-health-success me-1 text-success" : parsedData?.DatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_Health_Status?.toLowerCase() === "unhealthy" ? "cp-health-error me-1 text-danger" : "cp-disable me-1 text-danger";
//        let drhealthstate = parsedData?.DatabaseLevelMonitoring?.DR_DataBase_Synchroniztion_Health_Status?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.DatabaseLevelMonitoring?.DR_DataBase_Synchroniztion_Health_Status?.toLowerCase() === "healthy" ? "cp-health-success me-1 text-success" : parsedData?.DatabaseLevelMonitoring?.DR_DataBase_Synchroniztion_Health_Status?.toLowerCase() === "unhealthy" ? "cp-health-error me-1 text-danger" : "cp-disable me-1 text-danger";
//        let prdatabase = parsedData?.PR_Database_Name?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.PR_Database_Name ? "cp-database me-1 text-primary" : "cp-disable me-1 text-danger";
//        let drdatabase = parsedData?.DR_Database_Name?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.DR_Database_Name ? "cp-database me-1 text-primary" : "cp-disable me-1 text-danger";
//        let prprimarystate = parsedData?.PR_Last_Backup_Transaction_Log?  "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";
//        let drprimarytate = parsedData?.PRLastLSN_backup ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";
//        let prstate = parsedData?.DRLastLSN_copied ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";
//        let drstate = parsedData?.DR_Last_Restored_Transaction_Log ? "cp-primary me-1 text-primary" : "cp-disables me-1 text-danger";
//        let prconnectstate = parsedData?.DRLastLSN_restored ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";
//        let drconnectstate = parsedData?.DRlast_copied_file ? "cp-primary me-1 text-primary" : "cp-disable me-1 text-danger";
//        let prdatabasestate = parsedData?.DatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_State?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.DatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_State?.toLowerCase()?.substring(0, 6) === 'asynch' ? "cp-refresh me-1 text-danger" : parsedData?.DatabaseLevelMonitoring?.PR_DataBase_Synchroniztion_State?.toLowerCase()?.substring(0, 6) === 'synchr' ? "cp-refresh me-1 text-success" : "cp-disable me-1 text-danger";
//        let drdatabasestate = parsedData?.DatabaseLevelMonitoring?.DR_DataBase_Synchroniztion_State?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.DatabaseLevelMonitoring?.DR_DataBase_Synchroniztion_State?.toLowerCase()?.substring(0, 6) === 'asynch' ? "cp-refresh me-1 text-danger" : parsedData?.DatabaseLevelMonitoring?.DR_DataBase_Synchroniztion_State?.toLowerCase()?.substring(0, 6) === 'synchr' ? "cp-refresh me-1 text-success" : "cp-disable me-1 text-danger";
//        let prreplicationstate = parsedData?.AvailabilityGroupMonitoring?.PR_Availability_Mode?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.AvailabilityGroupMonitoring?.PR_Availability_Mode?.toLowerCase()?.substring(0, 6) === 'asynch' ? "cp-refresh me-1 text-danger" : parsedData?.AvailabilityGroupMonitoring?.PR_Availability_Mode?.toLowerCase()?.substring(0, 6) === 'synchr' ? "cp-refresh me-1 text-success" : "cp-disable me-1 text-danger";
//        let drreplicationstate = parsedData?.AvailabilityGroupMonitoring?.DR_Availability_Mode?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.AvailabilityGroupMonitoring?.DR_Availability_Mode?.toLowerCase()?.substring(0, 6) === 'asynch' ? "cp-refresh me-1 text-danger" : parsedData?.AvailabilityGroupMonitoring?.DR_Availability_Mode?.toLowerCase()?.substring(0, 6) === 'synchr' ? "cp-refresh me-1 text-success" : "cp-disable me-1 text-danger";
//        let infraobjectdata =
//            '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
//            '<table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
//            '<thead style="position: sticky;top: 0px;">' +
//            '<tr>' +
//            '<th>Component Monitor</th>' +
//            '<th>Production Server</th>' +
//            '<th>DR Server</th>' +
//            '</tr>' +
//            '</thead>' +
//            '<tbody style="">' +
//            '<tr>' +
//            '<td>' + "Server Name" + '</td>' +
//            '<td>' + '<i class="text-primary cp-server me-1 fs-6"></i>' +(parsedData.PR_Server_Name !== null && parsedData.PR_Server_Name !== "" ? parsedData.PR_Server_Name : 'NA') + '</td>' +
//            '<td>' + '<i class="text-primary cp-server me-1 fs-6"></i>' +(parsedData.DR_Server_Name !== null && parsedData.DR_Server_Name !== "" ? parsedData.DR_Server_Name : 'NA') + '</td>' +
//            '</tr>' +
//            '<tr>' +
//            '<td>' + 'IP Address/HostName' + '</td>' +
//            '<td>' + '<i class="' + pripaddress + '"></i>' + (parsedData.PR_Server_IpAddress !== null && parsedData.PR_Server_IpAddress !== "" ? parsedData.PR_Server_IpAddress : 'NA') + '</td>' +
//            '<td>' + '<i class="' + dripaddress + '"></i>' + (parsedData.DR_Server_IpAddress !== null && parsedData.DR_Server_IpAddress !== "" ? parsedData.DR_Server_IpAddress : 'NA') + '</td>' +
//            '</tr>' +
//            '<tr>' +
//            '<td>' + 'Database Name' + '</td>' +
//            '<td>' + '<i class="' + prdatabase + '"></i>' + (parsedData.PR_Database_Name !== null && parsedData.PR_Database_Name !== "" ? parsedData.PR_Database_Name : 'NA') + '</td>' +
//            '<td>' + '<i class="' + drdatabase + '"></i>' + (parsedData.DR_Database_Name !== null && parsedData.DR_Database_Name !== "" ? parsedData.DR_Database_Name : 'NA') + '</td>' +
//            '</tr>' +
//            '<tr>' +
//            '<td>' + "Last Generated transcation Log" + '</td>' +
//            '<td>' + '<i class="' + prprimarystate + '"></i>' + (parsedData.PR_Last_Backup_Transaction_Log !== null && parsedData.PR_Last_Backup_Transaction_Log !== "" ? parsedData.PR_Last_Backup_Transaction_Log : 'NA') + '</td>' +
//            '<td>' +  ('--') + '</td>' +
//            '</tr>' +
//            '<tr>' +
//            '<td>' + "Last Applied transcation Log" + '</td>' +
//            '<td>' +   ('--') + '</td>' +
//            '<td>' + '<i class="' + drstate + '"></i>' + (parsedData.DR_Last_Restored_Transaction_Log !== null && parsedData.DR_Last_Restored_Transaction_Log !== "" ? parsedData.DR_Last_Restored_Transaction_Log : 'NA') + '</td>' +
//            '</tr>';
//            infraobjectdata += generateMonitorServiceDetailsRow(workflow, application, value.monitorServiceDetails);

//        infraobjectdata += '</tbody>' +
//            '</table>' +
//            '</div>' +
//            '<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">' +
//            '<table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
//            '<thead style="position: sticky;top: 0px;z-index: 1;">' +
//            '<tr>' +
//            '<th>Replication Monitor</th>' +
//            '<th></th>' +
//            '<th></th>' +
//            '</tr>' +
//            '</thead>' +
//            '<tbody style="">' +
//            '<tr>' +
//            '<td>' + "Replication Type" + '</td>' +
//            '<td>' + (commondata.replicationType !== null && commondata.replicationType !== "" ? commondata.replicationType : 'NA') + '</td>' +
            
//            '</tr>' +
//            '<tr>' +
//            '<td>' + "LSN of Last Backup Log" + '</td>' +
//            '<td>' + '<i class="' + drprimarytate + '"></i>' + (parsedData.PRLastLSN_backup !== null && parsedData.PR_Last_Backup_Transaction_Log !== "" ? parsedData.PRLastLSN_backup : 'NA') + '</td>' +
            
//            '</tr>' +
//            '<tr>' +
//            '<td>' + "LSN of Last Copied Log" + '</td>' +
//            '<td>' + '<i class="' + prstate + '"></i>' + (parsedData.DRLastLSN_copied !== null && parsedData.DRLastLSN_copied !== "" ? parsedData.DRLastLSN_copied : 'NA') + '</td>' +
            
//            '</tr>' +
//            '<tr>' +
//            '<td>' + "Last Copied Log" + '</td>' +
//            '<td>' + '<i class="' + drconnectstate + '"></i>'+(parsedData.DRlast_copied_file !== null && parsedData.DRlast_copied_file !== "" ? parsedData.DRlast_copied_file : 'NA')  + '</td>' +
            
//            '</tr>' +
//            '<tr>' +
//            '<td>' + "LSN of Last Restored Log" + '</td>' +
//            '<td>' + '<i class="' + prconnectstate + '"></i>' + (parsedData.DRLastLSN_restored !== null && parsedData.DRLastLSN_restored !== "" ? parsedData.DRLastLSN_restored : 'NA') + '</td>' +
            
//            '</tr>' +
//            '</tbody style="">' +
//            '</table>' +
//            '</div>'



//        setTimeout(() => {
//            $("#infraobjectalldata").append(infraobjectdata);
//        }, 200)


//    }
//}