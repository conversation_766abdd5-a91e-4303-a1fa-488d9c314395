﻿using ContinuityPatrol.Application.Features.AccessManager.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.AccessManager.Events;

public class CreateAccessManagerEventTests : IClassFixture<AccessManagerFixture>, IClassFixture<UserActivityFixture>
{
    private readonly AccessManagerFixture _accessManagerFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly AccessManagerCreatedEventHandler _handler;

    public CreateAccessManagerEventTests(AccessManagerFixture accessManagerFixture, UserActivityFixture userActivityFixture)
    {
        _accessManagerFixture = accessManagerFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockAccessManagerEventLogger = new Mock<ILogger<AccessManagerCreatedEventHandler>>();

        _mockUserActivityRepository = AccessManagerRepositoryMocks.CreateAccessManagerEventRepository(_userActivityFixture.UserActivities);

        _handler = new AccessManagerCreatedEventHandler(mockLoggedInUserService.Object, mockAccessManagerEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateAccessManagerEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_accessManagerFixture.AccessManagerCreatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_accessManagerFixture.AccessManagerCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}