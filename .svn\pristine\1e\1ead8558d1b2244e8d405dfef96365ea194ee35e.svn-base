﻿namespace ContinuityPatrol.Application.Features.FormType.Commands.Update;

public class UpdateFormTypeCommand : IRequest<UpdateFormTypeResponse>
{
    public string Id { get; set; }
    public string FormTypeName { get; set; }
    public string FormTypeLogo { get; set; }
    public bool IsDelete { get; set; }

    public override string ToString()
    {
        return $"Type: {FormTypeName}; Id:{Id};";
    }
}