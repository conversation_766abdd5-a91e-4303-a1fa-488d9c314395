using AutoFixture;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetByUserId;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetDefaultDashboardByRoleId;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardMapModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DynamicDashboardMapFixture
{
    public CreateDynamicDashboardMapCommand CreateDynamicDashboardMapCommand { get; set; }
    public CreateDynamicDashboardMapResponse CreateDynamicDashboardMapResponse { get; set; }
    public UpdateDynamicDashboardMapCommand UpdateDynamicDashboardMapCommand { get; set; }
    public UpdateDynamicDashboardMapResponse UpdateDynamicDashboardMapResponse { get; set; }
    public DeleteDynamicDashboardMapCommand DeleteDynamicDashboardMapCommand { get; set; }
    public DeleteDynamicDashboardMapResponse DeleteDynamicDashboardMapResponse { get; set; }
    public GetDynamicDashboardMapDetailQuery GetDynamicDashboardMapDetailQuery { get; set; }
    public DynamicDashboardMapDetailVm DynamicDashboardMapDetailVm { get; set; }
    public GetDynamicDashboardMapListQuery GetDynamicDashboardMapListQuery { get; set; }
    public List<DynamicDashboardMapListVm> DynamicDashboardMapListVm { get; set; }
    public GetDynamicDashboardMapNameUniqueQuery GetDynamicDashboardMapNameUniqueQuery { get; set; }
    public GetDynamicDashboardMapPaginatedListQuery GetDynamicDashboardMapPaginatedListQuery { get; set; }
    public PaginatedResult<DynamicDashboardMapListVm> DynamicDashboardMapPaginatedResult { get; set; }
    public GetDefaultDashboardByRoleIdQuery GetDefaultDashboardByRoleIdQuery { get; set; }
    public GetDynamicDashboardMapByUserIdQuery GetDynamicDashboardMapByUserIdQuery { get; set; }

    public DynamicDashboardMapFixture()
    {
        var fixture = new Fixture();

        // Configure fixture to handle circular references
        fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
            .ForEach(b => fixture.Behaviors.Remove(b));
        fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        // Create Commands
        fixture.Customize<CreateDynamicDashboardMapCommand>(c => c
            .With(b => b.DashBoardSubId, Guid.NewGuid().ToString)
            .With(b => b.DashBoardSubName, "Enterprise Sub Dashboard")
            .With(b => b.UserId, Guid.NewGuid().ToString)
            .With(b => b.UserName, "Enterprise User")
            .With(b => b.RoleId, Guid.NewGuid().ToString)
            .With(b => b.RoleName, "Administrator")
            .With(b => b.Type, "Dashboard")
            .With(b => b.IsDefault, true)
            .With(b => b.IsView, true)
            .With(b => b.Url, "/dashboard/enterprise"));
        CreateDynamicDashboardMapCommand = fixture.Create<CreateDynamicDashboardMapCommand>();

        fixture.Customize<UpdateDynamicDashboardMapCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.DashBoardSubId, Guid.NewGuid().ToString)
            .With(b => b.DashBoardSubName, "Updated Enterprise Sub Dashboard")
            .With(b => b.UserId, Guid.NewGuid().ToString)
            .With(b => b.UserName, "Updated Enterprise User")
            .With(b => b.RoleId, Guid.NewGuid().ToString)
            .With(b => b.RoleName, "Senior Administrator")
            .With(b => b.Type, "Dashboard")
            .With(b => b.IsDefault, false)
            .With(b => b.IsView, true)
            .With(b => b.Url, "/dashboard/enterprise-updated"));
        UpdateDynamicDashboardMapCommand = fixture.Create<UpdateDynamicDashboardMapCommand>();

        DeleteDynamicDashboardMapCommand = fixture.Create<DeleteDynamicDashboardMapCommand>();

        // Create Responses
        fixture.Customize<CreateDynamicDashboardMapResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Success, true)
            .With(b => b.Message, "DynamicDashboardMap created successfully"));
        CreateDynamicDashboardMapResponse = fixture.Create<CreateDynamicDashboardMapResponse>();

        fixture.Customize<UpdateDynamicDashboardMapResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Success, true)
            .With(b => b.Message, "DynamicDashboardMap updated successfully"));
        UpdateDynamicDashboardMapResponse = fixture.Create<UpdateDynamicDashboardMapResponse>();

        fixture.Customize<DeleteDynamicDashboardMapResponse>(c => c
            .With(b => b.Success, true)
            .With(b => b.IsActive, false)
            .With(b => b.Message, "DynamicDashboardMap deleted successfully"));
        DeleteDynamicDashboardMapResponse = fixture.Create<DeleteDynamicDashboardMapResponse>();

        // Create Queries
        GetDynamicDashboardMapDetailQuery = fixture.Create<GetDynamicDashboardMapDetailQuery>();
        GetDynamicDashboardMapListQuery = fixture.Create<GetDynamicDashboardMapListQuery>();
        GetDynamicDashboardMapNameUniqueQuery = fixture.Create<GetDynamicDashboardMapNameUniqueQuery>();
        GetDynamicDashboardMapPaginatedListQuery = fixture.Create<GetDynamicDashboardMapPaginatedListQuery>();
        GetDefaultDashboardByRoleIdQuery = fixture.Create<GetDefaultDashboardByRoleIdQuery>();
        GetDynamicDashboardMapByUserIdQuery = fixture.Create<GetDynamicDashboardMapByUserIdQuery>();

        // Create ViewModels
        fixture.Customize<DynamicDashboardMapDetailVm>(c => c
            .With(b => b.ReferenceId, Guid.NewGuid().ToString)
            .With(b => b.DashBoardSubId, Guid.NewGuid().ToString)
            .With(b => b.DashBoardSubName, "Enterprise Dashboard Map Detail")
            .With(b => b.UserId, Guid.NewGuid().ToString)
            .With(b => b.UserName, "Enterprise Admin User")
            .With(b => b.RoleId, Guid.NewGuid().ToString)
            .With(b => b.RoleName, "System Administrator")
            .With(b => b.Type, "Dashboard")
            .With(b => b.IsDefault, true)
            .With(b => b.IsView, true)
            .With(b => b.Url, "/dashboard/enterprise-detail"));
        DynamicDashboardMapDetailVm = fixture.Create<DynamicDashboardMapDetailVm>();

        fixture.Customize<DynamicDashboardMapListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.DashBoardSubId, Guid.NewGuid().ToString)
            .With(b => b.DashBoardSubName, "Enterprise Dashboard Map")
            .With(b => b.UserId, Guid.NewGuid().ToString)
            .With(b => b.UserName, "Enterprise User")
            .With(b => b.RoleId, Guid.NewGuid().ToString)
            .With(b => b.RoleName, "Administrator")
            .With(b => b.Type, "Dashboard")
            .With(b => b.IsDefault, false)
            .With(b => b.IsView, true)
            .With(b => b.Url, "/dashboard/enterprise"));
        DynamicDashboardMapListVm = fixture.CreateMany<DynamicDashboardMapListVm>(5).ToList();

        // Create PaginatedResult using the Success factory method
        DynamicDashboardMapPaginatedResult = PaginatedResult<DynamicDashboardMapListVm>.Success(
            data: DynamicDashboardMapListVm,
            count: DynamicDashboardMapListVm.Count,
            page: 1,
            pageSize: 10
        );
    }
}
