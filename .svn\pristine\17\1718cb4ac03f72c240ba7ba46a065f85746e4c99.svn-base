using ContinuityPatrol.Application.Features.IncidentManagementSummary.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Commands.Delete;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Queries.GetDetail;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Queries.GetList;
//using ContinuityPatrol.Application.Features.IncidentManagementSummary.Queries.GetNameUnique;
//using ContinuityPatrol.Application.Features.IncidentManagementSummary.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.IncidentManagementSummaryModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class IncidentManagementSummarysController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<IncidentManagementSummaryListVm>>> GetIncidentManagementSummarys()
    {
        Logger.LogDebug("Get All IncidentManagementSummaries");

        return Ok(await Mediator.Send(new GetIncidentManagementSummaryListQuery()));
    }

    [HttpGet("{id}", Name = "GetIncidentManagementSummary")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<IncidentManagementSummaryDetailVm>> GetIncidentManagementSummaryById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "IncidentManagementSummary Id");

        Logger.LogDebug($"Get IncidentManagementSummary Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetIncidentManagementSummaryDetailQuery { Id = id }));
    }
    #region Paginated
    // [Route("paginated-list"), HttpGet]
    // [Authorize(Policy = Permissions.Configuration.View)]
    // public async Task<ActionResult<PaginatedResult<IncidentManagementSummaryListVm>>> GetPaginatedIncidentManagementSummarys([FromQuery] GetIncidentManagementSummaryPaginatedListQuery query)
    // {
    //     Logger.LogDebug("Get Searching Details in IncidentManagementSummary Paginated List");
    //
    //     return Ok(await Mediator.Send(query));
    // }
    #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateIncidentManagementSummaryResponse>> CreateIncidentManagementSummary([FromBody] CreateIncidentManagementSummaryCommand createIncidentManagementSummaryCommand)
    {
        Logger.LogDebug($"Create IncidentManagementSummary '{createIncidentManagementSummaryCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateIncidentManagementSummary), await Mediator.Send(createIncidentManagementSummaryCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateIncidentManagementSummaryResponse>> UpdateIncidentManagementSummary([FromBody] UpdateIncidentManagementSummaryCommand updateIncidentManagementSummaryCommand)
    {
        Logger.LogDebug($"Update IncidentManagementSummary '{updateIncidentManagementSummaryCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateIncidentManagementSummaryCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteIncidentManagementSummaryResponse>> DeleteIncidentManagementSummary(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "IncidentManagementSummary Id");

        Logger.LogDebug($"Delete IncidentManagementSummary Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteIncidentManagementSummaryCommand { Id = id }));
    }

    #region NameExist

    // [Route("name-exist"), HttpGet]
    // public async Task<ActionResult> IsIncidentManagementSummaryNameExist(string incidentManagementSummaryName, string? id)
    // {
    //     Guard.Against.NullOrWhiteSpace(IncidentManagementSummaryName, "IncidentManagementSummary Name");
    //
    //     Logger.LogDebug($"Check Name Exists Detail by IncidentManagementSummary Name '{incidentManagementSummaryName}' and Id '{id}'");
    //
    //     return Ok(await Mediator.Send(new GetIncidentManagementSummaryNameUniqueQuery { Name = IncidentManagementSummaryName, Id = id }));
    // }
    #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


