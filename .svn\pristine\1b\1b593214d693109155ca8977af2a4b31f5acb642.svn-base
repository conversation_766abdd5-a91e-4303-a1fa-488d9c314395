using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowActionResultFixture : IDisposable
{
    public List<WorkflowActionResult> WorkflowActionResultPaginationList { get; set; }
    public List<WorkflowActionResult> WorkflowActionResultList { get; set; }
    public WorkflowActionResult WorkflowActionResultDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string workflowOperationId = "baac749f-fb84-4498-9677-3ef8d995e8d4";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowActionResultFixture()
    {
        var fixture = new Fixture();

        WorkflowActionResultList = fixture.Create<List<WorkflowActionResult>>();

        WorkflowActionResultPaginationList = fixture.CreateMany<WorkflowActionResult>(20).ToList();

        WorkflowActionResultPaginationList.ForEach(x => x.CompanyId = CompanyId);
        WorkflowActionResultPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        WorkflowActionResultPaginationList.ForEach(x => x.WorkflowOperationGroupId = Guid.NewGuid().ToString());
        WorkflowActionResultPaginationList.ForEach(x => x.WorkflowOperationId = workflowOperationId);

        WorkflowActionResultList.ForEach(x => x.CompanyId = CompanyId);
        WorkflowActionResultList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        WorkflowActionResultList.ForEach(x => x.WorkflowOperationGroupId = Guid.NewGuid().ToString());
        WorkflowActionResultList.ForEach(x => x.WorkflowOperationId = workflowOperationId);

        WorkflowActionResultDto = fixture.Create<WorkflowActionResult>();

        WorkflowActionResultDto.CompanyId = CompanyId;
        WorkflowActionResultDto.ReferenceId = Guid.NewGuid().ToString();
        WorkflowActionResultDto.WorkflowOperationGroupId = Guid.NewGuid().ToString();
        WorkflowActionResultDto.WorkflowOperationId = workflowOperationId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
