﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetPaginatedList;

public class GetMSSQLAlwaysOnMonitorStatusPaginatedListQueryHandler : IRequestHandler<
    GetMSSQLAlwaysOnMonitorStatusPaginatedListQuery, PaginatedResult<MSSQLAlwaysOnMonitorStatusListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMssqlAlwaysOnMonitorStatusRepository _mssqlAlwaysOnMonitorStatusRepository;

    public GetMSSQLAlwaysOnMonitorStatusPaginatedListQueryHandler(
        IMssqlAlwaysOnMonitorStatusRepository mssqlAlwaysOnMonitorStatusRepository, IMapper mapper)
    {
        _mssqlAlwaysOnMonitorStatusRepository = mssqlAlwaysOnMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<MSSQLAlwaysOnMonitorStatusListVm>> Handle(
        GetMSSQLAlwaysOnMonitorStatusPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _mssqlAlwaysOnMonitorStatusRepository.GetPaginatedQuery();

        var productFilterSpec = new MsSqlAlwaysOnMonitorStatusFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<MSSQLAlwaysOnMonitorStatusListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}