﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.RoboCopyModel.RoboCopyViewModel
@using ContinuityPatrol.Domain.ViewModels.RoboCopyModel
<div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
    <form asp-controller="RoboCopyOptions" class="modal-content" asp-action="CreateOrUpdate"  id="CreateForm" method="post" enctype="multipart/form-data">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-robocopy"></i><span>RoboCopy Options Configuration</span></h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                       <div class="col-6">
                            <div class="form-group">
                                <div class="form-label">Name</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-name"></i></span>
                                    <input asp-for="Name" class="form-control" id="txtName" placeholder="Enter RoboCopy Options Name" maxlength="100" autocomplete="off" />
                                </div>
                                <span asp-validation-for="Name" id="rcpNameError"></span>
                                <input asp-for="Id" type="hidden" id="txtNameId" />
                                <input asp-for="Properties" type="hidden" id="roboCopyProperties" />
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <div class="form-label">Replication Type</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-replication-type"></i></span>
                                    <select asp-for="ReplicationType" class="form-select-modal" data-live-search="true" id="selectReplicationtype" data-placeholder="Select Replication Type">
                                        <option></option>
                                        <option value=""></option>
                                        <option value="Application">Application</option>
                                        <option value="Database">Database</option>
                                    </select>
                                </div>
                                <span asp-validation-for="ReplicationType" id="rcpReplicationTypeError"></span>
                            </div>
                        </div>                        
                        <div class="col-12">
                            <div class="form-group p-3 border border-1 border-secondary-subtle">
                                <div class="form-label mb-2">Copy Options</div>
                                <table class="table table-sm table-borderless mb-0" style="table-layout:fixed">
                                    <tbody>
                                        <tr>
                                            <td>
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="S-Checkbox1" value="/S">
                                        <label class="form-check-label" for="inlineCheckbox1">/S</label>
                                    </div>
                                            </td>
                                            <td>
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="MOV-Checkbox2" value="/E">
                                        <label class="form-check-label" for="inlineCheckbox1">/E</label>
                                    </div>
                                            </td>
                                            <td>
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="MIR-Checkbox3" value="/M">
                                        <label class="form-check-label" for="inlineCheckbox1">/M</label>
                                    </div>
                                            </td>
                                            <td>
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="FAT-Checkbox4" value="/Z">
                                        <label class="form-check-label" for="inlineCheckbox1">/Z</label>
                                    </div>
                                            </td>
                                            <td>
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="LEV-Checkbox5" value="/SEC">
                                        <label class="form-check-label" for="inlineCheckbox1">/SEC</label>
                                    </div>
                                            </td>
                                            <td>
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="E-Checkbox6" value="/MOV">
                                        <label class="form-check-label" for="inlineCheckbox1">/MOV</label>
                                    </div>
                                            </td>
                                            <td>
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="A-Checkbox7" value="/A">
                                        <label class="form-check-label" for="inlineCheckbox1">/A</label>
                                    </div>
                                            </td>
                                            <td>
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="ZB-Checkbox8" value="/B">
                                        <label class="form-check-label" for="inlineCheckbox1">/B</label>
                                    </div>
                                            </td>
                                            <td>
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="FFT-Checkbox9" value="/COPYALL">
                                        <label class="form-check-label" for="inlineCheckbox1">/COPYALL</label>
                                    </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="M-Checkbox10" value="/NOCOPY">
                                        <label class="form-check-label" for="inlineCheckbox1">/NOCOPY</label>
                                    </div>
                                            </td>
                                            <td>
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="B-Checkbox11" value="/MIR">
                                        <label class="form-check-label" for="inlineCheckbox1">/MIR</label>
                                    </div>
                                            </td>
                                            <td>
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="CreateCheckbox12" value="/ZB">
                                        <label class="form-check-label" for="inlineCheckbox1">/ZB	</label>
                                    </div>
                                            </td>
                                            <td>
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="Z-Checkbox13" value="/CREATE">
                                        <label class="form-check-label" for="inlineCheckbox1">/CREATE</label>
                                    </div>
                                            </td>
                                            <td>
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="CopyAllCheckbox14" value="/PURGE">
                                        <label class="form-check-label" for="inlineCheckbox1">/PURGE	</label>
                                    </div>
                                            </td>
                                            <td>
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="PurgeCheckbox15" value="/MOVE">
                                        <label class="form-check-label" for="inlineCheckbox1">/MOVE	</label>
                                    </div>
                                            </td>
                                            <td>
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="SEC-Checkbox16" value="/FAT">
                                        <label class="form-check-label" for="inlineCheckbox1">/FAT</label>
                                    </div>
                                            </td>
                                            <td>
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="NoCopyCheckbox17" value="/FFT">
                                        <label class="form-check-label" for="inlineCheckbox1">/FFT</label>
                                    </div>
                                            </td>
                                            <td>
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="enableTextBox('#MoveCheckbox18','#txtLEV')" id="MoveCheckbox18" value="/LEV">
                                        <label class="form-check-label" for="inlineCheckbox1">/LEV:</label>
                                    </div>
                                            </td> 
                                             <td>
                                                <input class="form-control border form-control-sm" disabled id="txtLEV" placeholder="/LEV" >
                                            </td> 
                                         </tr>
                                         <tr class="border-top">
                                            <td colspan="2">
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="CopyOptionCheckbox19"
                                        data-childId="child1" value="/A-">
                                        <label class="form-check-label" for="inlineCheckbox1">/A-:</label>
                                    </div>
                                     <table class="table table-sm table-borderless mb-0" id="child1">
                                     <tbody>
                                         <tr>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                     <input class="form-check-input" type="checkbox" id="copychild1" value="R">
                                                        <label class="form-check-label">R</label>
                                                    </div>
                                             </td>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                         <input class="form-check-input" type="checkbox" id="copychild2" value="A">
                                                            <label class="form-check-label">A</label>
                                                    </div>
                                             </td>
                                         </tr>
                                          <tr>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                     <input class="form-check-input" type="checkbox" id="copychild3" value="H">
                                                        <label class="form-check-label">H</label>
                                                    </div>
                                             </td>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                         <input class="form-check-input" type="checkbox" id="copychild4" value="N">
                                                            <label class="form-check-label">N</label>
                                                    </div>
                                             </td>
                                         </tr>
                                         <tr>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                     <input class="form-check-input" type="checkbox"  id="copychild5" value="T">
                                                        <label class="form-check-label">T</label>
                                                    </div>
                                             </td>
                                           
                                         </tr>
                                     </tbody>   
                                     </table>
                                            </td>
                                              <td colspan="2">
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="CopyOptionCheckbox20" 
                                        data-childId="child2" value="/A+">
                                        <label class="form-check-label" for="inlineCheckbox2">/A+:</label>
                                    </div>
                                     <table class="table table-sm table-borderless mb-0" id="child2">
                                     <tbody>
                                         <tr>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                     <input class="form-check-input" type="checkbox" id="copychild6"  value="R">
                                                        <label class="form-check-label">R</label>
                                                    </div>
                                             </td>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                         <input class="form-check-input" type="checkbox" id="copychild7"  value="A">
                                                            <label class="form-check-label">A</label>
                                                    </div>
                                             </td>
                                         </tr>
                                          <tr>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                     <input class="form-check-input" type="checkbox" id="copychild8"  value="H">
                                                        <label class="form-check-label">H</label>
                                                    </div>
                                             </td>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                         <input class="form-check-input" type="checkbox" id="copychild9"  value="N">
                                                            <label class="form-check-label">N</label>
                                                    </div>
                                             </td>
                                         </tr>
                                         <tr>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                     <input class="form-check-input" type="checkbox" id="copychild10"  value="T">
                                                        <label class="form-check-label">T</label>
                                                    </div>
                                             </td>                                           
                                         </tr>
                                     </tbody>   
                                     </table>
                                            </td>
                                             <td colspan="2">
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input Checkbox" type="checkbox" onchange="areAnyCheckboxesChecked(event)" id="CopyOptionCheckbox21" 
                                        data-childId="child3" value="/Copy">
                                        <label class="form-check-label" for="inlineCheckbox3">/Copy:</label>
                                    </div>
                                     <table class="table table-sm table-borderless mb-0" id="child3">
                                     <tbody>
                                         <tr>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                     <input class="form-check-input" type="checkbox" id="copychild11"  value="D">
                                                        <label class="form-check-label">D</label>
                                                    </div>
                                             </td>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                         <input class="form-check-input" type="checkbox" id="copychild12"  value="A">
                                                            <label class="form-check-label">A</label>
                                                    </div>
                                             </td>
                                         </tr>
                                          <tr>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                     <input class="form-check-input" type="checkbox"  id="copychild13"  value="T">
                                                        <label class="form-check-label">T</label>
                                                    </div>
                                             </td>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                         <input class="form-check-input" type="checkbox" id="copychild14"  value="S">
                                                            <label class="form-check-label">S</label>
                                                    </div>
                                             </td>
                                         </tr>
                                         <tr>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                     <input class="form-check-input" type="checkbox" id="copychild15"  value="O">
                                                        <label class="form-check-label">O</label>
                                                    </div>
                                             </td>
                                             <td>
                                                    <div class="form-check form-check-inline">
                                                     <input class="form-check-input" type="checkbox" id="copychild16"  value="U">
                                                        <label class="form-check-label">U</label>
                                                    </div>
                                             </td>
                                         </tr>
                                     </tbody>   
                                     </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <span asp-validation-for="Properties" id="CopyOptions-error"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="col-12">
                                <div class="form-group p-3 border border-1 border-secondary-subtle">
                                    <diV class="form-label">Retry Options</div>
                                        <table class="table table-sm table-borderless mb-0" >
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <div class="mt-2 form-check-inline">
                                        <input class="form-check-input" type="checkbox" onchange="enableTextBox('#RetryOptionCheckbox1','#txtRetry1')" id="RetryOptionCheckbox1" value="/R">
                                        <label class="form-check-label" for="inlineCheckbox3">/R</label>
                                    </div>
                                                    </td>
                                                    <td>
                                                        <input type="number" class="form-control border form-control-sm" @* oninput="textBoxChange('#RetryOptionText1-Error')" *@ disabled id="txtRetry1" placeholder="/R" />
                                                        <span asp-validation-for="Properties" id="RetryOptionText1-error"></span>
                                                    </td>
                                                    <td>
                                                        <div class="mt-2 form-check-inline">
                                        <input class="form-check-input" type="checkbox" onchange="enableTextBox('#RetryOptionCheckbox2','#txtRetry2')" id="RetryOptionCheckbox2" value="/W">
                                        <label class="form-check-label" for="inlineCheckbox3">/W</label>
                                    </div>
                                                    </td>
                                                    <td>
                                                        <input type="number" class="form-control border form-control-sm" @* oninput="textBoxChange('#RetryOptionText2-Error')" *@ disabled id="txtRetry2" placeholder="/W" />
                                                        <span asp-validation-for="Properties" id="RetryOptionText2-error"></span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>                                           
                                </div>
                            </div> 
                            <div class="form-group p-3 border border-1 border-secondary-subtle">
                                <div class="form-label mb-2">Filters</div>
                                <table class="table table-sm table-borderless mb-0">
                                    <tbody>
                                        <tr>
                                            <td>
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="XJ-Checkbox1" value="/XJ">
                                        <label class="form-check-label" for="inlineCheckbox1">/XJ</label>
                                    </div>
                                            </td>
                                            <td>
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="XX-Checkbox2" value="/IT">
                                        <label class="form-check-label" for="inlineCheckbox1">/IT</label>
                                    </div>
                                            </td>
                                            <td>
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="IA-Checkbox3" value="/IS">
                                        <label class="form-check-label" for="inlineCheckbox1">/IS</label>
                                    </div>
                                            </td>
                                            <td>
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="IT-Checkbox4" value="/XC">
                                        <label class="form-check-label" for="inlineCheckbox1">/XC</label>
                                    </div>
                                            </td>
                                             <td>
                                                 <div class="mt-2">
                                        <input class="form-check-input" type="checkbox" onchange="enableTextBox('#MAX-Checkbox11','#txtMax1')" id="MAX-Checkbox11" value="/MAX">
                                        <label class="form-check-label" for="inlineCheckbox1">/MAX:</label>
                                    </div>
                                            </td>
                                            <td class="d-flex align-items-center">
                                                <input class="form-control border form-control-sm" type="number" disabled placeholder="/MAX" id="txtMax1"  style="width:80px;"><span class="ms-1">bytes</span>
                                            </td>                                          
                                        </tr>
                                        <tr>
                                              <td>
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="XO-Checkbox5" value="/XX">
                                        <label class="form-check-label" for="inlineCheckbox1">/XX</label>
                                    </div>
                                            </td>
                                            <td>
                                                 <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="IS-Checkbox6" value="/XO">
                                        <label class="form-check-label" for="inlineCheckbox1">/XO</label>
                                    </div>
                                            </td>
                                            <td>
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="XN-Checkbox7" value="/XN">
                                        <label class="form-check-label" for="inlineCheckbox1">/XN</label>
                                    </div>
                                            </td>
                                            <td>
                                                <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="XC-Checkbox8" value="/XL">
                                        <label class="form-check-label" for="inlineCheckbox1">/XL</label>
                                    </div>
                                            </td>
                                             <td>
                                                 <div class="mt-2">
                                        <input class="form-check-input" type="checkbox" onchange="enableTextBox('#MIN-Checkbox12','#txtMin2')" id="MIN-Checkbox12" value="/MIN">
                                        <label class="form-check-label" for="inlineCheckbox1">/MIN:</label>
                                    </div>
                                            </td>
                                            <td class="d-flex align-items-center">
                                                <input class="form-control border form-control-sm" type="number" disabled id="txtMin2" placeholder="/MIN" style="width:80px;"><span class="ms-1">bytes</span>
                                            </td>
                                        </tr>
                                        <tr>                                           
           <td colspan="2">
            <div class="form-check form-check-inline">
    <input class="form-check-input Checkbox" type="checkbox" onchange="checkboxShowHide(event)" id="IA-Checkbox"
    data-childId="child11" value="/IA">
    <label class="form-check-label" for="inlineCheckbox1">/IA:</label>
</div>
 <table class="table table-sm table-borderless mb-0" id="child11">
 <tbody>
     <tr>
         <td>
                <div class="form-check form-check-inline">
                 <input class="form-check-input" type="checkbox" id="IA-CheckboX1" value="R">
                    <label class="form-check-label">R</label>
                </div>
         </td>
         <td>
                <div class="form-check form-check-inline">
                     <input class="form-check-input" type="checkbox" id="IA-CheckboX2" value="S">
                        <label class="form-check-label">S</label>
                </div>
         </td>
     </tr>
      <tr>
         <td>
                <div class="form-check form-check-inline">
                 <input class="form-check-input" type="checkbox" id=IA-CheckboX3" value="C">
                    <label class="form-check-label">C</label>
                </div>
         </td>
         <td>
                <div class="form-check form-check-inline">
                     <input class="form-check-input" type="checkbox" id="IA-CheckboX4" value="E">
                        <label class="form-check-label">E</label>
                </div>
         </td>
     </tr>
     <tr>
         <td>
                <div class="form-check form-check-inline">
                 <input class="form-check-input" type="checkbox"  id="IA-CheckboX5" value="D">
                    <label class="form-check-label">D</label>
                </div>
         </td>
        <td>
                <div class="form-check form-check-inline">
                 <input class="form-check-input" type="checkbox"  id="IA-CheckboX6" value="O">
                    <label class="form-check-label">O</label>
                </div>
         </td>
     </tr>
     <tr>
         <td>
                <div class="form-check form-check-inline">
                 <input class="form-check-input" type="checkbox" id="IA-CheckboX7" value="T">
                    <label class="form-check-label">T</label>
                </div>
         </td>
         <td>
                <div class="form-check form-check-inline">
                     <input class="form-check-input" type="checkbox" id="IA-CheckboX8" value="N">
                        <label class="form-check-label">N</label>
                </div>
         </td>
     </tr>
     <tr>
         <td>
                <div class="form-check form-check-inline">
                 <input class="form-check-input" type="checkbox" id="IA-CheckboX9" value="H">
                    <label class="form-check-label">H</label>
                </div>
         </td>
         <td>
                <div class="form-check form-check-inline">
                     <input class="form-check-input" type="checkbox" id="IA-CheckboX10" value="A">
                        <label class="form-check-label">A</label>
                </div>
         </td>
     </tr>
 </tbody>   
 </table>
        </td>
        <td colspan="2">
            <div class="form-check form-check-inline">
    <input class="form-check-input Checkbox" type="checkbox" onchange="checkboxShowHide(event)" id="XA-Checkbox"
    data-childId="child12" value="/XA">
    <label class="form-check-label" for="inlineCheckbox1">/XA:</label>
</div>
 <table class="table table-sm table-borderless mb-0" id="child12">
 <tbody>
     <tr>
         <td>
                <div class="form-check form-check-inline">
                 <input class="form-check-input" type="checkbox" id="XA-CheckboX1" value="R">
                    <label class="form-check-label">R</label>
                </div>
         </td>
         <td>
                <div class="form-check form-check-inline">
                     <input class="form-check-input" type="checkbox" id="XA-CheckboX2" value="S">
                        <label class="form-check-label">S</label>
                </div>
         </td>
     </tr>
      <tr>
         <td>
                <div class="form-check form-check-inline">
                 <input class="form-check-input" type="checkbox" id="XA-CheckboX3" value="C">
                    <label class="form-check-label">C</label>
                </div>
         </td>
         <td>
                <div class="form-check form-check-inline">
                     <input class="form-check-input" type="checkbox" id="XA-CheckboX4" value="E">
                        <label class="form-check-label">E</label>
                </div>
         </td>
     </tr>
     <tr>
         <td>
                <div class="form-check form-check-inline">
                 <input class="form-check-input" type="checkbox"  id="XA-CheckboX5" value="D">
                    <label class="form-check-label">D</label>
                </div>
         </td>
        <td>
                <div class="form-check form-check-inline">
                 <input class="form-check-input" type="checkbox"  id="XA-CheckboX6" value="O">
                    <label class="form-check-label">O</label>
                </div>
         </td>
     </tr>
     <tr>
         <td>
                <div class="form-check form-check-inline">
                 <input class="form-check-input" type="checkbox" id="XA-CheckboX7" value="T">
                    <label class="form-check-label">T</label>
                </div>
         </td>
         <td>
                <div class="form-check form-check-inline">
                     <input class="form-check-input" type="checkbox" id="XA-CheckboX8" value="N">
                        <label class="form-check-label">N</label>
                </div>
         </td>
     </tr>
     <tr>
         <td>
                <div class="form-check form-check-inline">
                 <input class="form-check-input" type="checkbox" id="XA-CheckboX9" value="H">
                    <label class="form-check-label">H</label>
                </div>
         </td>
         <td>
                <div class="form-check form-check-inline">
                     <input class="form-check-input" type="checkbox" id="XA-CheckboX10" value="A">
                        <label class="form-check-label">A</label>
                </div>
         </td>
     </tr>
 </tbody>   
 </table>
        </td>
                                            <td>
                                                 <div class="mt-2">
                                        <input class="form-check-input"  onchange="enableTextBox('#XD-Checkbox13','#txtXD3')" type="checkbox"  id="XD-Checkbox13" value="/XD">
                                        <label class="form-check-label" for="inlineCheckbox1">/XD</label>
                                    </div>
                                            </td>
                                            <td>
                                                <input class="form-control border form-control-sm" id="txtXD3" placeholder="/XD" style="width:80px;" disabled>
                                            </td>
                                          
                                        </tr>
                                        <tr>
                                            <td colspan="2"> </td>
                                            <td colspan="2"> </td>
                                              <td>
                                                <div class="mt-2">
                                        <input class="form-check-input" type="checkbox" onchange="enableTextBox('#XF-Checkbox14','#txtXF4')" id="XF-Checkbox14" value="/XF">
                                        <label class="form-check-label" for="inlineCheckbox1">/XF</label>
                                    </div>
                                            </td>
                                            <td>
                                                <input class="form-control border form-control-sm" disabled id="txtXF4" placeholder="/XF" style="width:80px;">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <span asp-validation-for="Properties" id="Filters-error"></span>
                            </div>
</div>
                         <div class="col-12">
                            <div class="col-12">
                                <div class="form-group p-3 border border-1 border-secondary-subtle">
                                    <diV class="form-label">Advanced Filter Option</div>
                                        <table class="table table-sm table-borderless mb-0">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                         <div class="form-check form-check-inline">
                                                             <input class="form-check-input" type="checkbox" id="RecyclebinCheckbox1" value="/XD ($RECYCLE.BIN)">
                                                                <label class="form-check-label" for="inlineCheckbox1">/XD ($RECYCLE.BIN)	</label>
                                              
                                                         </div>
                                                    </td>
                                                      <td>
                                                          <div class="form-check form-check-inline">
                                                             <input class="form-check-input" type="checkbox" id="SystemVoulumeCheckbox2" value="XD (System Volume Information)">
                                                              <label class="form-check-label" for="inlineCheckbox2">/XD (System Volume Information)</label>
                                          
                                                          </div>
                                                    </td> 
                                                    <td>
                                                          <div class="form-check form-check-inline">
                                                                <input class="form-check-input" type="checkbox" id="RecyclerCheckbox3" value="/XD (RECYCLER)">
                                                                <label class="form-check-label" for="inlineCheckbox2">/XD (RECYCLER)</label>
                                          
                                                          </div>
                                                    </td>
                                                </tr>
                                                 <tr>
                                                    <td>
                                                        <div class="mt-2 form-check-inline">
                                                           <input class="form-check-input" type="checkbox" onchange="enableTextBox('#MinAgeCheckbox4','#txtMinAge1')" id="MinAgeCheckbox4" value="/MINAGE">
                                                             <label class="form-check-label" for="inlineCheckbox3">/MINAGE: </label>
                                                            </div>
                                                    </td>
                                                    <td>
                                                        <input class="form-control border form-control-sm" type="number" disabled id="txtMinAge1" placeholder="/MINAGE:" />
                                                    </td>
                                                    <td>
                                                        <div class="mt-2 form-check-inline">
                                                             <input class="form-check-input" type="checkbox" onchange="enableTextBox('#MinLadCheckbox5','#txtMinLad2')" id="MinLadCheckbox5" value="/MINLAD">
                                                             <label class="form-check-label" for="inlineCheckbox3">/MINLAD:</label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                              <input type="number" class="form-control border form-control-sm" disabled id="txtMinLad2" placeholder="/MINLAD:" />
                                                    </td>
                                                </tr>
                                                 <tr>
                                                    <td>
                                                        <div class="mt-2 form-check-inline">
                                                           <input class="form-check-input" type="checkbox" onchange="enableTextBox('#MaxAgeCheckbox6','#txtMaxAge3')" id="MaxAgeCheckbox6" value="/MAXAGE">
                                                             <label class="form-check-label" for="inlineCheckbox6">/MAXAGE: </label>
                                                            </div>
                                                    </td>
                                                    <td>
                                                        <input type="number" class="form-control border form-control-sm" disabled id="txtMaxAge3" placeholder="/MAXAGE:" />
                                                    </td>
                                                    <td>
                                                        <div class="mt-2 form-check-inline">
                                                             <input class="form-check-input" type="checkbox" onchange="enableTextBox('#MaxLadCheckbox7','#txtMaxlad4')" id="MaxLadCheckbox7" value="/MAXLAD">
                                                             <label class="form-check-label" for="inlineCheckbox5">/MAXLAD: </label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                              <input type="number" class="form-control border form-control-sm" disabled id="txtMaxlad4" placeholder="/MAXLAD: " />
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <span asp-validation-for="Properties" id="AdvancedFilterOptions-error"></span>                            
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="SaveFunction">Save</button>
                    </div>
                </div>
            </form>       
    </div>