using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ReplicationJobFixture : IDisposable
{
    public List<ReplicationJob> ReplicationJobPaginationList { get; set; }
    public List<ReplicationJob> ReplicationJobList { get; set; }
    public ReplicationJob ReplicationJobDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ReplicationJobFixture()
    {
        var fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        fixture.Customize<ReplicationJob>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.CompanyId, CompanyId)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .With(x => x.LastModifiedDate, DateTime.UtcNow)
            .With(x => x.CreatedBy, "TestUser")
            .With(x => x.LastModifiedBy, "TestUser")
            .With(x => x.Name, () =>
            {
                var name = fixture.Create<string>();
                return $"ReplicationJob_{(name.Length > 20 ? name.Substring(0, 20) : name)}";
            })
            .With(x => x.TemplateId, () => Guid.NewGuid().ToString())
            .With(x => x.TemplateName, () =>
            {
                var name = fixture.Create<string>();
                return $"Template_{(name.Length > 15 ? name.Substring(0, 15) : name)}";
            })
            .With(x => x.SolutionTypeId, () => Guid.NewGuid().ToString())
            .With(x => x.SolutionType, () =>
            {
                var type = fixture.Create<string>();
                return $"Solution_{(type.Length > 15 ? type.Substring(0, 15) : type)}";
            })
            .With(x => x.NodeId, () => Guid.NewGuid().ToString())
            .With(x => x.NodeName, () =>
            {
                var name = fixture.Create<string>();
                return $"Node_{(name.Length > 15 ? name.Substring(0, 15) : name)}";
            })
            .With(x => x.Status, () => "Active")
            .With(x => x.CronExpression, () => "0 0 12 * * ?")
            .With(x => x.GroupPolicyId, () => Guid.NewGuid().ToString())
            .With(x => x.GroupPolicyName, () =>
            {
                var name = fixture.Create<string>();
                return $"Policy_{(name.Length > 15 ? name.Substring(0, 15) : name)}";
            })
            .With(x => x.IsSchedule, () => 1)
            .With(x => x.ScheduleTime, () => "12:00:00")
            .With(x => x.ScheduleType, () => 1)
            .With(x => x.Type, () => "Replication")
            .With(x => x.State, () => "Running")
            .With(x => x.ExecutionPolicy, () => "Standard"));

        ReplicationJobList = fixture.CreateMany<ReplicationJob>(5).ToList();
        ReplicationJobPaginationList = fixture.CreateMany<ReplicationJob>(20).ToList();
        ReplicationJobDto = fixture.Create<ReplicationJob>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public ReplicationJob CreateReplicationJobWithProperties(
        string name = null,
        string referenceId = null,
        string companyId = null,
        bool isActive = true)
    {
        var fixture = new Fixture();
        var nameStr = fixture.Create<string>();

        return new ReplicationJob
        {
            ReferenceId = referenceId ?? Guid.NewGuid().ToString(),
            CompanyId = companyId ?? CompanyId,
            Name = name ?? $"ReplicationJob_{(nameStr.Length > 20 ? nameStr.Substring(0, 20) : nameStr)}",
            TemplateId = Guid.NewGuid().ToString(),
            TemplateName = "TestTemplate",
            SolutionTypeId = Guid.NewGuid().ToString(),
            SolutionType = "TestSolution",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "TestNode",
            Status = "Active",
            CronExpression = "0 0 12 * * ?",
            GroupPolicyId = Guid.NewGuid().ToString(),
            GroupPolicyName = "TestPolicy",
            IsSchedule = 1,
            ScheduleTime = "12:00:00",
            ScheduleType = 1,
            Type = "Replication",
            State = "Running",
            ExecutionPolicy = "Standard",
            IsActive = isActive,
            CreatedDate = DateTime.UtcNow,
            LastModifiedDate = DateTime.UtcNow,
            CreatedBy = "TestUser",
            LastModifiedBy = "TestUser"
        };
    }

    public ReplicationJob CreateReplicationJobWithSpecificName(string name)
    {
        return CreateReplicationJobWithProperties(name: name);
    }

    public List<ReplicationJob> CreateReplicationJobsWithSameName(string name, int count)
    {
        var jobs = new List<ReplicationJob>();
        for (int i = 0; i < count; i++)
        {
            jobs.Add(CreateReplicationJobWithProperties(name: name));
        }
        return jobs;
    }

    public List<ReplicationJob> CreateReplicationJobsWithDifferentNames(List<string> names)
    {
        var jobs = new List<ReplicationJob>();
        foreach (var name in names)
        {
            jobs.Add(CreateReplicationJobWithProperties(name: name));
        }
        return jobs;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
