const rsyncURL = {
    createOrUpdate: "Configuration/RSyncOptions/CreateOrUpdate",
    delete: "Configuration/RSyncOptions/Delete",
    getPagination: "/Configuration/RSyncOptions/GetPagination",
    getRsyncOptionsDataById: "Configuration/RSyncOptions/GetRsyncOptionsDataById",
    isRsyncNameExist: "Configuration/RSyncOptions/IsRsyncNameExist"
};

let createPermission = $("#configurationrsyncCreate").data("create-permission")?.toLowerCase();
let deletePermission = $("#configurationrsyncDelete").data("delete-permission")?.toLowerCase();
let btnDisableRSync = false;
let selectedValues = [];
let dataTable = "";

let generalOptionsCheckboxes = [$("#verbosityCheckbox"), $("#quietCheckbox"), $("#checksumCheckbox"),
$("#archiveCheckbox"), $("#compressionCheckbox"), $("#recursiveCheckbox")];

let advancedOptionsCheckboxes = [$("#progressCheckbox"), $("#copyLinksCheckbox"), $("#keepDirlinksCheckbox"),
$("#deleteCheckbox"), $("#linksCheckbox"), $("#copyDirlinksCheckbox"), $("#hardLinksCheckbox"),
$("#statsCheckbox")];

let advancedOptionsTextBoxCheckboxes = [$("#logFileCheckbox"), $("#excludeCheckbox"), $("#excludeFromCheckbox"),
$("#portCheckbox"), $("#includeCheckbox"), $("#includeFromCheckbox"), $("#additionalCheckbox")];

let advancedOptionsTextBoxes = [$("#logFileTextBox"), $("#excludeTextBox"), $("#excludeFromTextBox"),
$("#portTextBox"), $("#includeTextBox"), $("#includeFromTextBox"), $("#additionalTextBox")];

let advancedOptionsTextBoxesValidation = [$("#logFileTextBoxError"), $("#excludeTextBoxError"), $("#excludeFromTextBoxError"),
$("#portTextBoxError"), $("#includeTextBoxError"), $("#includeFromTextBoxError"), $("#additionalTextBoxError")];

let advancedOptionsTextBoxesErrorMessage = ['Enter log file', 'Enter exclude', 'Enter exclude from',
    'Enter port', 'Enter include', 'Enter include from', 'Enter additional'];

$(function () {
    infraPreventSpecialKeys('#search-inp');  
    $('#showHideGeneralAdvancedOptions').hide();
    $('#showHideCustom').hide();

    if (createPermission == 'false') {
        $("#rsync-createbutton").removeClass('#rsync-createbutton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    dataTable = $('#rSyncDatatablelist').DataTable(
        {
            language: {
                decimal: ",",
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous" ></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "Sortable": true,
            "order": [],
            fixedColumns: {
                left: 1,
                right: 1
            },
            "ajax": {
                "type": "GET",
                "url": rsyncURL.getPagination,
                "dataType": "json",
                "data": function (d) {
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {

                    if (json.success) {
                        const { data } = json;
                        json.recordsTotal = data?.totalPages;
                        json.recordsFiltered = data?.totalCount;
                        let hasData = data?.data?.length === 0;
                        $(".pagination-column").toggleClass("disabled", hasData);
                        return data?.data;
                    }
                    else {
                        errorNotification(json)
                    }
                },
            },
            "columnDefs": [
                {
                    "targets": [1, 3, 4],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        return type === 'display' ? meta.row + 1 : data;
                    }
                },
                {
                    "data": "name", "name": "name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data;
                    }
                },
                {
                    "data": "properties", "name": "generalOptions", "autoWidth": true,
                    "render": function (data, type, row) {
                        let replacedProps = JSON.parse(row?.properties)?.generalOptions ?? "NA";
                        return type === 'display' ? `<span title="${replacedProps}">${replacedProps}</span>` : replacedProps;
                    }
                },
                {
                    "data": "properties", "name": "advancedOptions", "autoWidth": true,
                    "render": function (data, type, row) {
                        let advancedOptions = JSON.parse(row?.properties)?.advancedOptions ?? "NA";
                        return type === 'display' ? `<span title="${advancedOptions}">${advancedOptions}</span>` : advancedOptions;
                    }
                },
                {
                    "data": "properties", "name": "customCommand", "autowidth": true,
                    "render": function (data, type, row) {
                        let customCommand = JSON.parse(row?.properties)?.customCommand ?? "NA";
                        return type === 'display' ? `<span title="${customCommand}">${customCommand}</span>` : customCommand;
                    }
                },
                {
                    "orderable": false,
                    "render": function (data, type, row) {
                        const rowId = row?.id;
                        const rowName = row?.name;
                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                                <div class="d-flex align-items-center gap-2">                                    
                                    <span role="button" title="Edit" class="rsync-edit-button" data-rsync='${rowId}'>
                                        <i class="cp-edit "></i>
                                    </span>
                                    <span role="button" title="Delete" class="rsync-delete-button" data-bs-toggle="modal" 
                                        data-dbname="${rowName}" data-dbid="${rowId}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete "></i>
                                    </span>  
                                </div>`;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                                <div class="d-flex align-items-center  gap-2">
                                    <span role="button" title="Edit" class="rsync-edit-button" aria-disabled="true" data-rsync='${rowId}'>
                                    <i class="cp-edit"></i>
                                    </span> 
                                    <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                    </span>                                     
                                </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                                <div class="d-flex align-items-center  gap-2">
                                    <span role="button" title="Edit" class="icon-disabled">
                                        <i class="cp-edit"></i>
                                    </span>  
                                    <span role="button" title="Delete" class="rsync-delete-button" data-dbid="${rowId}"
                                                data-dbname="${rowName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete"></i>
                                    </span>                                      
                                 </div>`;
                        }
                        else {
                            return `
                                <div class="d-flex align-items-center  gap-2">
                                    <span role="button" title="Edit" class="icon-disabled">
                                       <i class="cp-edit"></i>
                                    </span>  
                                    <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                    </span>  
                                </div>`;
                        }
                    }
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart; // Get the start index of displayed data
                let counter = startIndex + index + 1; // Calculate the serial number based on start index and index
                $('td:eq(0)', row).html(counter); // Update the cell in the first column with the serial number
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },
            "drawCallback": function (settings) {
            }
        });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-inp').on('input', commonDebounce(async function (e) {
        const nameCheckbox = $("#Name");
        const generalOptions = $("#Generaloptions");
        const advancedOptions = $("#Advancedoptions");
        const customCommand = $("#Customcommand");
        let sanitizedValue = $(this).val().replace(/^\s/, '').replace(/\s+/g, ' ').replace(/\s$/, ' ');

        if (sanitizedValue.trim() === "") {
            $(this).val("");
            sanitizedValue = "";
        } else {
            $(this).val(sanitizedValue);
        }
        if (nameCheckbox.is(':checked')) {
            selectedValues.push(nameCheckbox.val() + sanitizedValue);
        }
        //if (generalOptions.is(':checked')) {
        //    selectedValues.push(generalOptions.val() + sanitizedValue);
        //}
        //if (advancedOptions.is(':checked')) {
        //    selectedValues.push(advancedOptions.val() + sanitizedValue);
        //}
        //if (customCommand.is(':checked')) {
        //    selectedValues.push(customCommand.val() + sanitizedValue);
        //} 
        if (nameCheckbox.is(':checked') === false && generalOptions.is(':checked') === false &&
            advancedOptions.is(':checked') === false && customCommand.is(':checked') === false) {
            selectedValues.push(nameCheckbox.val() + sanitizedValue);
            //selectedValues.push(generalOptions.val() + sanitizedValue);
            //selectedValues.push(advancedOptions.val() + sanitizedValue);
            //selectedValues.push(customCommand.val() + sanitizedValue);
        }
        dataTable.ajax.reload(function (json) {
            let $dataTables_empty = $('.dataTables_empty');

            if (sanitizedValue.length === 0 && json?.data?.data?.length === 0) {
                $dataTables_empty.text('No Data Found');
            } else if (json?.recordsFiltered === 0) {
                $dataTables_empty.text('No matching records found');
            }
        })
    }));

    $('.saveRSyncData').on('click', async function () {
        let name = $('#rsyncOptionName').val();
        let id = $('#rsyncID').val();
        let type = $('#replicationType :selected').val();

        //CommonFunctions.js InfraNameValidation
        let validateName = await InfraNameValidation(name, id, rsyncURL.isRsyncNameExist, $("#nameError"),
            "Enter r-sync options name", 'Special characters not allowed', 'Name');
        let validateReplication = replicationTypeValidation(type, 'Select replication type', 'replicationTypeError');
        let userSelection = $("#inlineRadio1").is(':checked');
        let custom = $("#inlineRadio2").is(':checked');
        let optionSelectionChecked;
        let generalOptionsChecked;
        let advancedOptionsTextbox = [];
        let advancedOptionsTBox = true;

        if (userSelection || custom) {
            optionSelectionChecked = true;
            $("#optionSelectionError").text("").removeClass('field-validation-error');
        } else {
            optionSelectionChecked = false;
            $("#optionSelectionError").text("Select atleast one radio button").addClass('field-validation-error');
        }
        if (optionSelectionChecked) {
            let props = {};

            if ($("#inlineRadio1").is(':checked')) {
                props.optionsSelection = $("#inlineRadio1").val();
            }
            if ($("#inlineRadio2").is(':checked')) {
                props.optionsSelection = $("#inlineRadio2").val();
            }
            if (userSelection) {
                let isAnyChecked = generalOptionsCheckboxes.some(checkbox => checkbox.is(':checked'));

                if (isAnyChecked) {
                    generalOptionsChecked = true;
                    $("#generalOptionsError").text("").removeClass('field-validation-error');
                } else {
                    generalOptionsChecked = false;
                    $("#generalOptionsError").text("Select atleast one checkbox").addClass('field-validation-error');
                }
                advancedOptionsTextBoxCheckboxes.forEach(function (checkbox, index) {

                    if (checkbox.is(':checked')) {
                        let value = advancedOptionsTextBoxes[index].val();

                        if (value) {
                            advancedOptionsTextbox.push(true);
                            advancedOptionsTextBoxesValidation[index].text("").removeClass('field-validation-error');
                        } else {
                            advancedOptionsTextbox.push(false);
                            advancedOptionsTextBoxesValidation[index].text(advancedOptionsTextBoxesErrorMessage[index]).addClass('field-validation-error');
                        }
                    } else {
                        advancedOptionsTextbox.push(true);
                    }
                });
                advancedOptionsTBox = advancedOptionsTextbox.every(item => item === true);

                if (validateName && validateReplication && optionSelectionChecked && generalOptionsChecked && advancedOptionsTBox) {

                    if (props.optionsSelection === "userSelection") {
                        let selectedValuesGeneralOptions = [];
                        generalOptionsCheckboxes.forEach(function (checkbox) {

                            if (checkbox.is(':checked')) {
                                selectedValuesGeneralOptions.push(checkbox.val());
                            }
                        });
                        let concatenatedValues = selectedValuesGeneralOptions.join(',');
                        let removedComma = "-" + concatenatedValues.split(',').join('');
                        props.generalOptions = removedComma;
                        let selectedValuesAdvancedOptions = [];
                        advancedOptionsCheckboxes.forEach(function (checkbox) {

                            if (checkbox.is(':checked')) {
                                selectedValuesAdvancedOptions.push(checkbox.val());
                            }
                        });
                        let concatenatedValuesAdvanced = selectedValuesAdvancedOptions.join(',');
                        props.advancedOptions = concatenatedValuesAdvanced;
                        let selectedValuesAdvancedOptionsTextbox = [];
                        advancedOptionsTextBoxCheckboxes.forEach(function (checkbox, index) {

                            if (checkbox.is(':checked')) {
                                let keyValue = {
                                    key: checkbox.val(),
                                    value: advancedOptionsTextBoxes[index].val()
                                };
                                selectedValuesAdvancedOptionsTextbox.push(keyValue);
                            }
                        });
                        props.advancedOptionsTextFields = selectedValuesAdvancedOptionsTextbox;
                    }
                    if (!btnDisableRSync) {
                        btnDisableRSync = true;
                        let encryption = await propertyEncryption(props);
                        $('#rsyncProperties').val(encryption);
                        const form = $('#createFormRSync')[0];
                        const formData = new FormData(form);
                        let response = await infraCreateOrUpdate(RootUrl + rsyncURL.createOrUpdate, formData);
                        $('#RSyncCreateModal').modal('hide');
                        btnDisableRSync = false;

                        if (response?.success) {
                            notificationAlert("success", response?.data?.message);
                            setTimeout(() => {
                                dataTableCreateAndUpdate($(".saveRSyncData"), dataTable);
                            }, 2000)
                        } else {
                            errorNotification(response);
                        }
                    }
                }
            }
            if (custom) {
                let customValue = $('#customFieldTextarea').val();
                let customCommandValue;

                if (customValue) {
                    customCommandValue = true;
                    $("#customCommandError").text("").removeClass('field-validation-error');
                } else {
                    customCommandValue = false;
                    $("#customCommandError").text("Enter custom command").addClass('field-validation-error');
                }
                if (validateName && validateReplication && customCommandValue) {

                    if (props.optionsSelection === "custom") {
                        props.customCommand = $('#customFieldTextarea').val();
                    }
                    if (!btnDisableRSync) {
                        btnDisableRSync = true;
                        let encryption = await propertyEncryption(props);
                        $('#rsyncProperties').val(encryption);
                        const form = $('#createFormRSync')[0];
                        const formData = new FormData(form);
                        let response = await infraCreateOrUpdate(RootUrl + rsyncURL.createOrUpdate, formData);
                        $('#RSyncCreateModal').modal('hide');
                        btnDisableRSync = false;

                        if (response?.success) {
                            notificationAlert("success", response?.data?.message);
                            setTimeout(() => {
                                dataTableCreateAndUpdate($(".saveRSyncData"), dataTable);
                            }, 2000)
                        } else {
                            errorNotification(response);
                        }
                    }
                }
            }
        }
    });

    $('#rSyncDatatablelist').on('click', '.rsync-delete-button', function () {
        $("#deleteData").attr("title", $(this).data('dbname'));
        $("#deleteData").text($(this).data("dbname"));
        $("#textDeleteId").val($(this).data("dbid"));
    });

    $("#confirmDeleteButton").on("click", async function () {
        const form = $('#deleteRsync')[0];
        const formData = new FormData(form);

        if (!btnDisableRSync) {
            btnDisableRSync = true;
            //$('#loginLoader').removeClass('d-none').show();
            //$(this).prop('disabled', true); 
            let response = await infraDeleteData(RootUrl + rsyncURL.delete, formData);
            $("#DeleteModal").modal("hide");
            btnDisableRSync = false;

            if (response?.success) {
                notificationAlert("success", response?.data?.message);
                setTimeout(() => {
                    dataTableDelete(dataTable);
                }, 2000)
            } else {
                errorNotification(response);
            }
        }
    });

    $('#rSyncDatatablelist').on('click', '.rsync-edit-button', async function () {
        let data = $(this).data("rsync");
        $("input[type='checkbox']").prop("checked", false);
        $("input[type='text']").val("");
        $("input[type='text']").prop("disabled", true);
        $('#rsyncOptionName').prop("disabled", false);
        let getRsyncOptionsDataById = await infraGetRequestWithData(RootUrl + rsyncURL.getRsyncOptionsDataById, { id: data });

        if (typeof getRsyncOptionsDataById === "object" && Object.keys(getRsyncOptionsDataById).length) {
            populateModalFields(getRsyncOptionsDataById);
        }
        $('.saveRSyncData').text('Update');
        $('#RSyncCreateModal').modal('show');
    });

    $('.createButton').on('click', function () {
        $('#rsyncID').val('');
        $('#showHideGeneralAdvancedOptions').hide();
        $('#showHideCustom').hide();
        $('.saveRSyncData').text('Save');
        advancedOptionsTextBoxes.forEach(textBox => textBox.prop('disabled', true));
        clearServerTypeErrorMessage();
    });

    $('#portTextBox').on('keydown', function (event) {
        if (event.key === 'e' || event.key === "E" || event.key === "-") {
            event.preventDefault();
        }
    });

    $('#rsyncOptionName').on('keyup', commonDebounce(function () {
        let rSyncName = $('#rsyncOptionName').val();
        let sanitizedValue = rSyncName.replace(/\s{2,}/g, ' '); //Restrict Multiple spaces
        $('#rsyncOptionName').val(sanitizedValue);
        let name = $('#rsyncOptionName').val();
        let id = $('#rsyncID').val();

        //CommonFunctions.js InfraNameValidation
        InfraNameValidation(name, id, rsyncURL.isRsyncNameExist, $("#nameError"),
            "Enter name", 'Special characters not allowed', 'Name');
    }));
});
