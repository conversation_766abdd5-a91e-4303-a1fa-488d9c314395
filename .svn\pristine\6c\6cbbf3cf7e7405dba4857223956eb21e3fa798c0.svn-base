using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ArchiveRepositoryTests : IClassFixture<ArchiveFixture>
{
    private readonly ArchiveFixture _archiveFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ArchiveRepository _repository;

    public ArchiveRepositoryTests(ArchiveFixture archiveFixture)
    {
        _archiveFixture = archiveFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new ArchiveRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var archive = _archiveFixture.ArchiveDto;

        // Act
        var result = await _repository.AddAsync(archive);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(archive.ArchiveProfileName, result.ArchiveProfileName);
        Assert.Equal(archive.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.Archives);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var archive = _archiveFixture.ArchiveDto;
        await _repository.AddAsync(archive);

        archive.ArchiveProfileName = "Updated Profile";
        archive.BackUpType = "Full";
        archive.Count = 100;

        // Act
        var result = await _repository.UpdateAsync(archive);

        // Assert
        Assert.Equal("Updated Profile", result.ArchiveProfileName);
        Assert.Equal("Full", result.BackUpType);
        Assert.Equal(100, result.Count);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.NullReferenceException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var archive = _archiveFixture.ArchiveDto;
        await _repository.AddAsync(archive);

        // Act
        var result = await _repository.DeleteAsync(archive);

        // Assert
        Assert.Equal(archive.ArchiveProfileName, result.ArchiveProfileName);
        Assert.Empty(_dbContext.Archives);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var archive = _archiveFixture.ArchiveDto;
        var addedEntity = await _repository.AddAsync(archive);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.ArchiveProfileName, result.ArchiveProfileName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var archive = _archiveFixture.ArchiveDto;
        await _repository.AddAsync(archive);

        // Act
        var result = await _repository.GetByReferenceIdAsync(archive.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(archive.ReferenceId, result.ReferenceId);
        Assert.Equal(archive.ArchiveProfileName, result.ArchiveProfileName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

  
    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var archives = _archiveFixture.ArchiveList;
        await _repository.AddRangeAsync(archives);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(archives.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public void PaginatedListAllAsync_ShouldReturnQueryable()
    {
        // Arrange
        var archives = _archiveFixture.ArchivePaginationList;
        _dbContext.Archives.AddRange(archives);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedQuery();;

        // Assert
        Assert.NotNull(result);
        Assert.Equal(archives.Count, result.Count());
    }

    [Fact]
    public void PaginatedListAllAsync_ShouldReturnEmptyQueryable_WhenNoEntities()
    {
        // Act
        var result = _repository.GetPaginatedQuery();;

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.ToList());
    }

    [Fact]
    public void PaginatedListAllAsync_ShouldFilterByCompanyId_WhenNotParent()
    {
        var repository = new ArchiveRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
        
        var archives = _archiveFixture.ArchivePaginationList;
        // Set some with different company IDs
        archives.Take(5).ToList().ForEach(x => x.CompanyId = "COMPANY_456");
        
        _dbContext.Archives.AddRange(archives);
        _dbContext.SaveChanges();

        // Act
        var result = repository.GetPaginatedQuery();;

        // Assert
        Assert.NotNull(result);
        var filteredResults = result.ToList();
        Assert.All(filteredResults, x => Assert.Equal("COMPANY_123", x.CompanyId));
    }
    [Fact]
    public async Task PaginatedListAllAsync_ReturnsPaginatedResult_WhenIsParentIsTrue()
    {
        var repository = new ArchiveRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());

        var archives = new List<Archive>
        {
            new Archive { Id = 1, CompanyId = "C1" },
            new Archive { Id = 2, CompanyId = "C2" }
        }.AsQueryable();

        var entitiesMock = new Mock<DbSet<Archive>>();
        entitiesMock.As<IQueryable<Archive>>().Setup(m => m.Provider).Returns(archives.Provider);
        entitiesMock.As<IQueryable<Archive>>().Setup(m => m.Expression).Returns(archives.Expression);
        entitiesMock.As<IQueryable<Archive>>().Setup(m => m.ElementType).Returns(archives.ElementType);
        entitiesMock.As<IQueryable<Archive>>().Setup(m => m.GetEnumerator()).Returns(archives.GetEnumerator());

        var specification = new Mock<Specification<Archive>>().Object;

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Id", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.IsType<PaginatedResult<Archive>>(result);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ReturnsPaginatedResult_FilteredByCompanyId_WhenIsParentIsFalse()
    {
        var repository = new ArchiveRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());

        var archives = new List<Archive>
        {
            new Archive { Id = 1, CompanyId = "C1" },
            new Archive { Id = 2, CompanyId = "C2" }
        }.AsQueryable();

        var entitiesMock = new Mock<DbSet<Archive>>();
        entitiesMock.As<IQueryable<Archive>>().Setup(m => m.Provider).Returns(archives.Provider);
        entitiesMock.As<IQueryable<Archive>>().Setup(m => m.Expression).Returns(archives.Expression);
        entitiesMock.As<IQueryable<Archive>>().Setup(m => m.ElementType).Returns(archives.ElementType);
        entitiesMock.As<IQueryable<Archive>>().Setup(m => m.GetEnumerator()).Returns(archives.GetEnumerator());


        var specification = new Mock<Specification<Archive>>().Object;

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Id", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.IsType<PaginatedResult<Archive>>(result);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ThrowsArgumentException_WhenSortColumnIsNull()
    {
        var repository = new ArchiveRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());

        var specification = new Mock<Specification<Archive>>().Object;

        // Act & Assert
       var res=await _repository.PaginatedListAllAsync(1, 10, specification, null, "desc");
        Assert.NotNull(res);
        Assert.IsType<PaginatedResult<Archive>>(res);
        Assert.Empty(res.Data);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var archive = _archiveFixture.ArchiveDto;
        archive.ArchiveProfileName = "ExistingProfile";
        await _repository.AddAsync(archive);

        // Act
        var result = await _repository.IsNameExist("ExistingProfile", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var archives = _archiveFixture.ArchiveList;
        await _repository.AddRangeAsync(archives);

        // Act
        var result = await _repository.IsNameExist("NonExistentProfile", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var archive = _archiveFixture.ArchiveDto;
        archive.ArchiveProfileName = "SameProfile";
        await _repository.AddAsync(archive);

        // Act
        var result = await _repository.IsNameExist("SameProfile", archive.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsSolutionTypeExist Tests

    [Fact]
    public async Task IsSolutionTypeExist_ShouldReturnTrue_WhenProfileNameExists()
    {
        // Arrange
        var archive = _archiveFixture.ArchiveDto;
        archive.ArchiveProfileName = "TestSolutionType";
        await _repository.AddAsync(archive);

        // Act
        var result = await _repository.IsSolutionTypeExist("testsolutiontype");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsSolutionTypeExist_ShouldReturnFalse_WhenProfileNameDoesNotExist()
    {
        // Arrange
        var archives = _archiveFixture.ArchiveList;
        await _repository.AddRangeAsync(archives);

        // Act
        var result = await _repository.IsSolutionTypeExist("nonexistentsolutiontype");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsTableNameExist Tests

    [Fact]
    public async Task IsTableNameExist_ShouldReturnTrue_WhenTableNameExists()
    {
        // Arrange
        var archive = _archiveFixture.ArchiveDto;
        archive.TableNameProperties = "[{\"tableName\":\"TestTable\",\"property\":\"value\"}]";
        await _repository.AddAsync(archive);

        // Act
        var result = await _repository.IsTableNameExist("TestTable", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTableNameExist_ShouldReturnFalse_WhenTableNameDoesNotExist()
    {
        // Arrange
        var archive = _archiveFixture.ArchiveDto;
        archive.TableNameProperties = "[{\"tableName\":\"DifferentTable\",\"property\":\"value\"}]";
        await _repository.AddAsync(archive);

        // Act
        var result = await _repository.IsTableNameExist("NonExistentTable", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTableNameExist_ShouldBeCaseInsensitive()
    {
        // Arrange
        var archive = _archiveFixture.ArchiveDto;
        archive.TableNameProperties = "[{\"tableName\":\"TestTable\",\"property\":\"value\"}]";
        await _repository.AddAsync(archive);

        // Act
        var result = await _repository.IsTableNameExist("testtable", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    #endregion

    #region GetByTableAccessId Tests

    [Fact]
    public async Task GetByTableAccessId_ShouldReturnMatchingArchives()
    {
        // Arrange
        var tableAccessId = "TABLE_ACCESS_001";

        var archives = new List<Archive>
        {
            new Archive
            {
                TableNameProperties = $"[{{\"tableAccessId\":\"{tableAccessId}\",\"tableName\":\"Table1\"}}]",
                ArchiveProfileName = "Profile1",
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new Archive
            {
                TableNameProperties = $"[{{\"tableAccessId\":\"{tableAccessId}\",\"tableName\":\"Table2\"}}]",
                ArchiveProfileName = "Profile2",
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new Archive
            {
                TableNameProperties = "[{\"tableAccessId\":\"DIFFERENT_ID\",\"tableName\":\"Table3\"}]",
                ArchiveProfileName = "Profile3",
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(archives);

        // Act
        var result = await _repository.GetByTableAccessId(tableAccessId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(tableAccessId, x.TableNameProperties));
    }

    [Fact]
    public async Task GetByTableAccessId_ShouldReturnEmpty_WhenNoMatch()
    {
        // Arrange
        var archives = _archiveFixture.ArchiveList;
        await _repository.AddRangeAsync(archives);

        // Act
        var result = await _repository.GetByTableAccessId("NON_EXISTENT_ID");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var archives = _archiveFixture.ArchiveList;
        var archive1 = archives[0];
        var archive2 = archives[1];
  
        // Act
        var task1 = _repository.AddAsync(archive1);
        var task2 = _repository.AddAsync(archive2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.Archives.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var archives = _archiveFixture.ArchiveList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(archives);
        var initialCount = archives.Count;

        var toUpdate = archives.Take(2).ToList();
        toUpdate.ForEach(x => x.ArchiveProfileName = "Updated Profile");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = archives.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.ArchiveProfileName == "Updated Profile").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleNullParameters()
    {
        // Act & Assert
        var result1 = await _repository.IsNameExist(null, "valid-guid");
        var result2 = await _repository.IsNameExist("TestName", null);
        var result3 = await _repository.IsNameExist(null, null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    [Fact]
    public async Task Repository_ShouldHandleSpecialCharactersInProfileNames()
    {
        // Arrange
        var archive = _archiveFixture.ArchiveDto;
        archive.ArchiveProfileName = "Test@Profile#123$%";

        // Act
        var addedArchive = await _repository.AddAsync(archive);
        var nameExists = await _repository.IsNameExist("Test@Profile#123$%", "invalid-guid");
        var solutionTypeExists = await _repository.IsSolutionTypeExist("test@profile#123$%");

        // Assert
        Assert.NotNull(addedArchive);
        Assert.Equal("Test@Profile#123$%", addedArchive.ArchiveProfileName);
        Assert.True(nameExists);
        Assert.True(solutionTypeExists);
    }

    #endregion
}
