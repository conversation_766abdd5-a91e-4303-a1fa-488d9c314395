$(function () {
    let passwordCopy = '';

    $("#domainName, .differentiator").hide();
    $("#txtLoginName").text('');
    localStorage.setItem('selecteddropdown', 'Service Availability');
    localStorage.setItem('selectedmenu', 'Dashboard');

    function loginEventDebounce(func, timeout = 300) {
        let timer;
        return function (...args) {
            if (!timer) func.apply(this, args);
            clearTimeout(timer);
            timer = setTimeout(function () {
                timer = undefined;
            }, timeout);
        };
    }

    $('#txtLoginName').on('keyup propertychange paste', loginEventDebounce(async function () {
        const element = $(this);
        setTimeout(() => {
            const value = element.val();
            const sanitizedValue = value.replace(/\s{2,}/g, ' ');
            element.val(sanitizedValue);
            clearPassword();
            validateDropDown(sanitizedValue.trim(), 'Enter login name', $('#loginName_error'));
        }, 0);
        //const value = $(this).val();
        //var sanitizedValue = value.replace(/\s{2,}/g, ' ');
        //$(this).val(sanitizedValue);
        //clearPassword();
        //validateDropDown($("#txtLoginName").val().trim(), 'Enter login name', $('#loginName_error'))
    }));

    $("#txtPassword").on('blur', function () {
       
        hashPassword("txtLoginName", "txtPassword");
    });

    $("#txtPassword").on('input', async function () {
        passwordCopy = $('#txtPassword').val();

        if ($("#txtPassword").val().length > 64) {
            setTimeout(() => {
                $("#txtPassword").val('');
                $('#password_error').text('').removeClass('field-validation-error');
            }, 300)
        } else {
            validateDropDown($("#txtPassword").val(), 'Enter password', $('#password_error'))
        }
    })

    $('#confirmClearButton').on('click', async function (e) {
        e.preventDefault();
        $('#loginLoader').removeClass('d-none').show();
        $(this).prop('disabled', true);         
        $('#loginContainer').addClass('background_diabled')
        const name = $("#userName").val();
        try {
            const response = await $.post(RootUrl + "Account/ClearSession", { 'loginName': name }, "json");

            if (response.success) {

                var loginResponse = await $.post(RootUrl + "Account/Login", { 'model': response.loginViewModel, 'returnUrl': '', 'isMultipleLogin': true }, "json");

                if (loginResponse.success) {

                    if (loginResponse.url !== "" && loginResponse.url != undefined) {
                        window.location.href = RootUrl + loginResponse.url;
                    } else {
                        if (loginResponse.role === "SiteAdmin") {
                            window.location.href = RootUrl + 'Admin/User/SiteAdminLanding';
                        } else {
                            window.location.href = RootUrl + 'Dashboard/ServiceAvailability/List';
                        }
                    }

                } else {
                    window.location.href = RootUrl + 'Account/Logout';
                }

            } else {
                window.location.replace(RootUrl + 'Account/Logout');
            }
           
        } catch (error) {
            console.log("Error: " + error);
        }

        setTimeout(() => {
            $('#loginContainer').removeClass('background_diabled');
            $('#DeleteAccountModal').modal('hide');
        },4000)
        
    });


    /*Select2 focus open*/
    jQuery(document).on('focus', '.select2', function () {
        jQuery(this).siblings('select').select2('open')
    });

    setTimeout(() => {
        $('#ddlCompanyId, #ddlADLogin, #ddlADGroup').select2({
            "language": {
                "noResults": function () {
                    return "No results found";
                }
            },
        })
    }, 300);

    //document.addEventListener('keyup', (e) => {
    //    if (e.getModifierState('CapsLock')) {
    //        console.log("Caps Lock is on");
    //    } else {
    //        console.log("Caps Lock is off");
    //    }
    //});

    function clearPassword() {
        var password = $("#txtPassword").val();
        if (password != "" && password.length > 0) {
            $("#txtPassword").val('');
        }
    }

    const getADGroupList = async (domain) => {

        await $.ajax({
            url: 'Account/GetADGroup',
            data: `domainName=${domain}`,
            method: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    console.log(response)
                    if (response.data && Array.isArray(response.data) && response.data.length) {
                        let html = ''
                        response.data.forEach((d) => {
                            html += `<option>${d}</option>`
                        })
                        $('#ddlADGroup').append(html);
                    }
                }
            }
        })
    }

    $('#ADCheckBox').on('change', loginEventDebounce(async function (e) {
        if (e.target.checked) {
            $('#forgotPasswordModel').hide();
            $('#authentication').val('AD');
            $('#ddlADLogin').empty();
            await $.ajax({
                type: "GET",
                url: "Account/GetDomains",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (response) {
                    let domainList = response.addomainlist;
                    if (domainList?.length) {
                        $('#ddlADLogin').append('<option value="">Select Domain</option>');
                        for (let x = 0; x < domainList.length; x++) {
                            $('#ddlADLogin').append(`<option value="${domainList[x]?.value}">${domainList[x]?.text}</option>`);
                        }
                    }
                },
            })
            setTimeout(() => {
                $('.ADLoginCheck').prop('checked', false);
                $('#WFInfraNameList').prop('checked', true).trigger('change');
                $("#domainName").show();
            }, 500)
        }
        else {
            $("#domainName").hide();
            $('#forgotPasswordModel').show();
            $('#authentication').val('In House');
            $('#ddlADLogin').empty();
            $('#Domain-error').removeClass('field-validation-error').text('').val('')
        }
    }))

    $('#ddlADLogin').on("change", loginEventDebounce(function () {
        let value = $('#ddlADLogin option:selected').val()
        getADGroupList(value)
        validateDropDown($('#ddlADLogin').val(), "Select domain", $('#Domain-error'))
    }));

    $(document).on('change', '.ADLoginCheck', function (e) {
        if (e.target.checked) {
            let value = e.target.value
            $('.ADLoginCheck').prop('checked', false);
            if (value === 'group') {
                $('#ADDomainContainer').removeClass('col-12').addClass('col-6')
                $('#ADGroup-error').text('').removeClass('field-validation-error');
                $('#ADGroupContainer').show()
            } else {
                $('#ADDomainContainer').removeClass('col-6').addClass('col-12')
                $('#ADGroupContainer').hide()
                $('#ddlADGroup').val('').trigger('change')
            }
            $(this).prop('checked', true);
        }
       
    })

    $('#btnLogin').on("click", loginEventDebounce(async function (e) {
        e.preventDefault();
        await CPLogin()
    }, 800))

    $(document).on('keydown', async function (e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            await CPLogin()
        }
    })

    const CPLogin = async () => {       
        let password = $("#txtPassword").val()
        let loginVal = $("#txtLoginName").val();
        let isAdChecked = $('#ADCheckBox').prop('checked')

        if (password.length < 64 || isAdChecked) {
            await hashPassword("txtLoginName", "txtPassword", isAdChecked, passwordCopy);
        }
        sessionStorage.setItem('IsFirst', true)
        setTimeout(async () => {
            let isAD_Domain = true;
            let isADGroup = true;
            if (isAdChecked) {
                isAD_Domain = validateDropDown($('#ddlADLogin').val(), "Select domain", $('#Domain-error'))
                if ($('.ADLoginCheck:checked').val() === 'group') {
                    isADGroup = validateDropDown($('#ddlADGroup').val(), "Select AD group", $('#ADGroup-error'))
                    isADGroup && $('#isGroupLogin').val('true');
                }              
            }
            let isLoginName = validateDropDown($("#txtLoginName").val().trim(), 'Enter login name', $('#loginName_error'))
            let isPassword = validateDropDown(password, 'Enter password', $('#password_error'))

            let sanitizeArray = ['txtLoginName', 'txtPassword', 'ddlADLogin']
            sanitizeArray.forEach((item) => {
                if ($(`#${item}`).val()) {
                    $(`#${item}`).val(DOMPurify.sanitize($(`#${item}`).val()))
                }
            })

            if (isAdChecked && loginVal) $("#hiddenTxtLogin").val($('#ddlADLogin').val() + "\\" + loginVal)
            else $("#hiddenTxtLogin").val(loginVal)
           
            if (isAD_Domain && isLoginName && isPassword && isADGroup) { 
               
                $("#loginForm").trigger('submit')
            }
           
        }, 1000)
    }

    function validateDropDown(value, errorMsg, errorElement) {
        if (!value) {
            errorElement.text(errorMsg);
            errorElement.addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('');
            errorElement.removeClass('field-validation-error');
            return true;
        }
    }

    function hideSelect2Keyboard() {
        $('#ddlADLogin input, :focus,input').prop('focus', false).trigger('blur');
        $('#ddlCompanyId input, :focus,input').prop('focus', false).trigger('blur');
        $('#btnLogin').trigger('focus')
    }

    $("#ddlADLogin, #ddlCompanyId").on("change", function () {
        setTimeout(hideSelect2Keyboard, 50);
    });
});