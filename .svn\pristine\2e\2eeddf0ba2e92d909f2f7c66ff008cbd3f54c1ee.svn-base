﻿namespace ContinuityPatrol.Application.Features.MonitorService.Command.Create;

public class CreateMonitorServiceCommandValidator : AbstractValidator<CreateMonitorServiceCommand>
{
    private readonly IMonitorServiceRepository _monitorServiceRepository;

    public CreateMonitorServiceCommandValidator(IMonitorServiceRepository monitorServiceRepository)
    {
        _monitorServiceRepository = monitorServiceRepository;

        RuleFor(p => p.InfraObjectName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");


        RuleFor(p => p.ServerName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");


        RuleFor(p => p.BusinessServiceName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.Type)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^[a-zA-Z0-9_\s-]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.WorkflowName)
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 200).WithMessage("{PropertyName} should contain between 3 to 200 characters.")
            .When(p => p.WorkflowName.IsNotNullOrWhiteSpace());

        RuleFor(p => p.NodeName)
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.")
            .When(p => p.NodeName.IsNotNullOrWhiteSpace());

        RuleFor(p => p.FailedActionName)
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.")
            .When(p => p.FailedActionName.IsNotNullOrWhiteSpace());

        RuleFor(p => p.Status)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull();

        RuleFor(p => p)
            .MustAsync(IsServicePathUnique)
            .WithMessage("A same Name already exist.");
        RuleFor(p => p)
            .MustAsync(IsValidGuidId).WithMessage("Id is invalid");
    }

    public async Task<bool> IsServicePathUnique(CreateMonitorServiceCommand p, CancellationToken cancellationToken)
    {
        return !await _monitorServiceRepository.IsMonitorServicePathUnique(p.ServicePath, p.InfraObjectId, p.Type,
            p.ThreadType, p.WorkflowType, p.WorkflowId, p.WorkflowName, p.ServerId);
    }

    private Task<bool> IsValidGuidId(CreateMonitorServiceCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.BusinessServiceId, "BusinessService Id");
        Guard.Against.InvalidGuidOrEmpty(p.InfraObjectId, "InfraObject Id");
        Guard.Against.InvalidGuidOrEmpty(p.ServerId, "Server Id");
        if (!string.IsNullOrEmpty(p.WorkflowId))
            Guard.Against.InvalidGuidOrEmpty(p.WorkflowId, "Workflow Id");
        if (!string.IsNullOrEmpty(p.NodeId))
            Guard.Against.InvalidGuidOrEmpty(p.NodeId, "Node Id");
        if (!string.IsNullOrEmpty(p.FailedActionId))
            Guard.Against.InvalidGuidOrEmpty(p.FailedActionId, "Node Id");

        return Task.FromResult(true);
    }
}