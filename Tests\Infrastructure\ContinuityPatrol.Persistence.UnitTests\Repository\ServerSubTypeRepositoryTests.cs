using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple specification for testing
public class ServerSubTypeFilterSpecification : Specification<ServerSubType>
{
    public ServerSubTypeFilterSpecification(string? searchString = null)
    {
        Criteria = p => p.Name != null;

        if (!string.IsNullOrEmpty(searchString))
        {
            And(p => p.Name.Contains(searchString) || p.ServerTypeName.Contains(searchString));
        }
    }
}

public class ServerSubTypeRepositoryTests : IClassFixture<ServerSubTypeFixture>, IDisposable
{
    private readonly ServerSubTypeFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly ServerSubTypeRepository _repository;

    public ServerSubTypeRepositoryTests(ServerSubTypeFixture fixture)
    {
        _fixture = fixture;
        _dbContext = _fixture.DbContext;
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(ServerSubTypeFixture.CompanyId);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _repository = new ServerSubTypeRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        ClearDatabase().Wait();
    }

    #region GetServerSubTypeByServerType Tests

    [Fact]
    public async Task GetServerSubTypeByServerType_ShouldReturnMatchingServerSubTypes_WhenServerTypeIdMatches()
    {
        // Arrange
        await ClearDatabase();

        var serverTypeId = "SERVER_TYPE_001";
        var serverSubType1 = _fixture.CreateServerSubType(name: "SubType1", serverTypeId: serverTypeId, serverTypeName: "Type1");
        var serverSubType2 = _fixture.CreateServerSubType(name: "SubType2", serverTypeId: serverTypeId, serverTypeName: "Type1");
        var serverSubType3 = _fixture.CreateServerSubType(name: "SubType3", serverTypeId: "DIFFERENT_TYPE", serverTypeName: "Type2");

        await _repository.AddAsync(serverSubType1);
        await _repository.AddAsync(serverSubType2);
        await _repository.AddAsync(serverSubType3);

        // Act
        var result = await _repository.GetServerSubTypeByServerType(serverTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, st => Assert.Equal(serverTypeId, st.ServerTypeId));
        Assert.Contains(result, st => st.Name == "SubType1");
        Assert.Contains(result, st => st.Name == "SubType2");
        Assert.DoesNotContain(result, st => st.Name == "SubType3");
    }

    [Fact]
    public async Task GetServerSubTypeByServerType_ShouldReturnEmpty_WhenNoMatchingServerSubTypes()
    {
        // Arrange
        await ClearDatabase();

        var serverTypeId = "SERVER_TYPE_001";
        var serverSubType = _fixture.CreateServerSubType(name: "SubType1", serverTypeId: "DIFFERENT_TYPE");

        await _repository.AddAsync(serverSubType);

        // Act
        var result = await _repository.GetServerSubTypeByServerType(serverTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetServerSubTypeByServerType_ShouldReturnOnlyActiveServerSubTypes()
    {
        // Arrange
        await ClearDatabase();

        var serverTypeId = "SERVER_TYPE_001";
        var activeServerSubType = _fixture.CreateServerSubType(name: "ActiveSubType", serverTypeId: serverTypeId, isActive: true);
        var inactiveServerSubType = _fixture.CreateServerSubType(name: "InactiveSubType", serverTypeId: serverTypeId, isActive: false);

        await _dbContext.ServerSubTypes.AddAsync(activeServerSubType);
        _dbContext.SaveChanges();
        await _dbContext.ServerSubTypes.AddAsync(inactiveServerSubType);
        _dbContext.SaveChanges();
    
        // Act
        var result = await _repository.GetServerSubTypeByServerType(serverTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("ActiveSubType", result[0].Name);
        Assert.True(result[0].IsActive);
    }

    #endregion

    #region IsServerSubTypeExist Tests

    [Fact]
    public async Task IsServerSubTypeExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();

        var serverSubType = _fixture.CreateServerSubType(name: "TestSubType");
        await _repository.AddAsync(serverSubType);

        // Act
        var result = await _repository.IsServerSubTypeExist("TestSubType", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsServerSubTypeExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsServerSubTypeExist("NonExistentSubType", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsServerSubTypeExist_ShouldReturnFalse_WhenNameExistsButIdMatches()
    {
        // Arrange
        await ClearDatabase();

        var serverSubType = _fixture.CreateServerSubType(name: "TestSubType");
        await _repository.AddAsync(serverSubType);

        // Act
        var result = await _repository.IsServerSubTypeExist("TestSubType", serverSubType.ReferenceId);

        // Assert
        Assert.False(result);
    }

    //[Fact]
    //public async Task IsServerSubTypeExist_ShouldBeCaseInsensitive()
    //{
    //    // Arrange
    //    await ClearDatabase();

    //    var serverSubType = _fixture.CreateServerSubType(name: "TestSubType");
    //    await _repository.AddAsync(serverSubType);

    //    // Act
    //    var result = await _repository.IsServerSubTypeExist("testsubtype", "invalid-guid");

    //    // Assert
    //    Assert.True(result);
    //}

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WithBasicPagination()
    {
        // Arrange
        await ClearDatabase();

        var serverSubTypes = new List<ServerSubType>();
        for (int i = 1; i <= 15; i++)
        {
            serverSubTypes.Add(_fixture.CreateServerSubType(
                name: $"SubType{i:D2}",
                serverTypeId: "SERVER_TYPE_001",
                serverTypeName: "Test Type",
                logo: $"logo{i}.png"
            ));
        }

        foreach (var serverSubType in serverSubTypes)
        {
            await _repository.AddAsync(serverSubType);
        }

        var specification = new ServerSubTypeFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(15, result.TotalCount);
        Assert.Equal(10, result.Data.Count);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(2, result.TotalPages);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnCorrectFields()
    {
        // Arrange
        await ClearDatabase();

        var serverSubType = _fixture.CreateServerSubType(
            name: "TestSubType",
            serverTypeId: "SERVER_TYPE_001",
            serverTypeName: "Test Type",
            logo: "test-logo.png"
        );

        await _repository.AddAsync(serverSubType);

        var specification = new ServerSubTypeFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Single(result.Data);

        var returnedServerSubType = result.Data[0];
        Assert.NotNull(returnedServerSubType.ReferenceId);
        Assert.Equal("TestSubType", returnedServerSubType.Name);
        Assert.Equal("SERVER_TYPE_001", returnedServerSubType.ServerTypeId);
        Assert.Equal("Test Type", returnedServerSubType.ServerTypeName);
        Assert.Equal("test-logo.png", returnedServerSubType.Logo);
        Assert.True(returnedServerSubType.Id > 0);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.ServerSubTypes.RemoveRange(_dbContext.ServerSubTypes);
        await _dbContext.SaveChangesAsync();
    }
}
