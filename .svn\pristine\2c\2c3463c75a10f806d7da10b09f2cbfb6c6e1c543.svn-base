using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class DriftJobFilterSpecification : Specification<DriftJob>
{
    public DriftJobFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.Name != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("nodeid=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.NodeId.Contains(stringItem.Replace("nodeid=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("nodename=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.NodeName.Contains(stringItem.Replace("nodename=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("status=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Status.Contains(stringItem.Replace("status=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("cronexpression=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.CronExpression.Contains(stringItem.Replace("cronexpression=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("lastexecutiontime=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.LastExecutionTime.Contains(stringItem.Replace("lastexecutiontime=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("scheduletime=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ScheduleTime.Contains(stringItem.Replace("scheduletime=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("exceptionmessage=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ExceptionMessage.Contains(stringItem.Replace("exceptionmessage=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("driftprofile=", StringComparison.InvariantCultureIgnoreCase))
                    {
                        var driftProfile =
                            stringItem.Replace("driftprofile=", "", StringComparison.InvariantCultureIgnoreCase);
                        if (string.IsNullOrEmpty(driftProfile))
                            Criteria = p => p.Name != null;
                        else
                            AddJsonCriteria(s => s.Properties, "value", driftProfile);
                    }

            }
            else
            {
                Criteria = p => p.Name.Contains(searchString) || p.Properties.Contains(searchString) ||
                                p.NodeId.Contains(searchString) || p.NodeName.Contains(searchString) ||
                                p.Status.Contains(searchString) || p.CronExpression.Contains(searchString) ||
                                p.ScheduleTime.Contains(searchString) || p.ExceptionMessage.Contains(searchString);
            }
        }
    }
}