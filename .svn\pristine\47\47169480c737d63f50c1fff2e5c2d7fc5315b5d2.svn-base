﻿using ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Commands.Delete;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.RpoSlaDeviationReport.Commands
{
    public class DeleteRpoSlaDeviationReportTests
    {
        private readonly Mock<IRpoSlaDeviationReportRepository> _mockRpoSlaDeviationReportRepository;
        private readonly DeleteRpoSlaDeviationReportCommandHandler _handler;

        public DeleteRpoSlaDeviationReportTests()
        {
            _mockRpoSlaDeviationReportRepository = new Mock<IRpoSlaDeviationReportRepository>();
            _handler = new DeleteRpoSlaDeviationReportCommandHandler(_mockRpoSlaDeviationReportRepository.Object);
        }

        [Fact]
        public async Task Handle_ReturnsResponse_WhenDeletionIsSuccessful()
        {
            var reportId = Guid.NewGuid().ToString();
            var report = new Domain.Entities.RpoSlaDeviationReport
            {
                ReferenceId = reportId,
                InfraObjectName = "TestInfraObject",
                IsActive = true
            };

            _mockRpoSlaDeviationReportRepository
                .Setup(repo => repo.GetByReferenceIdAsync(reportId))
                .ReturnsAsync(report);

            _mockRpoSlaDeviationReportRepository
                .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RpoSlaDeviationReport>()))
                .Returns(ToString);

            var command = new DeleteRpoSlaDeviationReportCommand { Id = reportId };

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.False(result.IsActive);
            Assert.Contains("deleted", result.Message.ToLower());

            _mockRpoSlaDeviationReportRepository.Verify(repo => repo.GetByReferenceIdAsync(reportId), Times.Once);
            _mockRpoSlaDeviationReportRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RpoSlaDeviationReport>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenReportDoesNotExist()
        {
            var reportId = Guid.NewGuid().ToString();

            _mockRpoSlaDeviationReportRepository
                .Setup(repo => repo.GetByReferenceIdAsync(reportId))
                .ReturnsAsync((Domain.Entities.RpoSlaDeviationReport)null);

            var command = new DeleteRpoSlaDeviationReportCommand { Id = reportId };

            var exception = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Equal($"Entity \"RpoSlaDeviationReport\" ({reportId}) was not found.", exception.Message);

            _mockRpoSlaDeviationReportRepository.Verify(repo => repo.GetByReferenceIdAsync(reportId), Times.Once);
            _mockRpoSlaDeviationReportRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RpoSlaDeviationReport>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenUpdateFails()
        {
            var reportId = Guid.NewGuid().ToString();
            var report = new Domain.Entities.RpoSlaDeviationReport
            {
                ReferenceId = reportId,
                InfraObjectName = "TestInfraObject",
                IsActive = true
            };

            _mockRpoSlaDeviationReportRepository
                .Setup(repo => repo.GetByReferenceIdAsync(reportId))
                .ReturnsAsync(report);

            _mockRpoSlaDeviationReportRepository
                .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RpoSlaDeviationReport>()))
                .ThrowsAsync(new Exception("Database update error"));

            var command = new DeleteRpoSlaDeviationReportCommand { Id = reportId };

            await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));

            _mockRpoSlaDeviationReportRepository.Verify(repo => repo.GetByReferenceIdAsync(reportId), Times.Once);
            _mockRpoSlaDeviationReportRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RpoSlaDeviationReport>()), Times.Once);
        }
    }
}
