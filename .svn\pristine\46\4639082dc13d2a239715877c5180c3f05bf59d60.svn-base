﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObjectScheduler.Events;

public class DeleteInfraObjectSchedulerEventTests : IClassFixture<InfraObjectSchedulerFixture>, IClassFixture<UserActivityFixture>
{
    private readonly InfraObjectSchedulerFixture _infraObjectSchedulerFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly InfraObjectSchedulerDeletedEventHandler _handler;

    public DeleteInfraObjectSchedulerEventTests(InfraObjectSchedulerFixture infraObjectSchedulerFixture, UserActivityFixture userActivityFixture)
    {
        _infraObjectSchedulerFixture = infraObjectSchedulerFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockInfraObjectEventLogger = new Mock<ILogger<InfraObjectSchedulerDeletedEventHandler>>();

        _mockUserActivityRepository = InfraObjectSchedulerRepositoryMocks.CreateInfraObjectSchedulerEventRepository(_userActivityFixture.UserActivities);

        _handler = new InfraObjectSchedulerDeletedEventHandler(mockLoggedInUserService.Object, mockInfraObjectEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteInfraObjectSchedulerEventDeleted()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_infraObjectSchedulerFixture.InfraObjectSchedulerDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_infraObjectSchedulerFixture.InfraObjectSchedulerDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}