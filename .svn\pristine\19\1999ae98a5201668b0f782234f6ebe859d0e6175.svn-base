﻿let AIInfraId = '';
let AISolutionId = '';
let AIActionTypeId = '';
let AIRestoreWorkflow = [];
let isFromGenie = false;

let globalUserName = $('#userLoginName').text().replace(/ /g, '')
let genieImage = `<img class="AI-Profile" src="/../img/genie_bot/LOGO-SMALL.svg" />`;
let userGenieImage = `<img class="User-Profile" src="/../img/genie_bot/USER.svg" />`;
let userData = `<li id=${getRandomId('Genie')} class="list-group-item User-select-Option userChatCont"><div class="User-Message-Bg"></div>${userGenieImage}</li>`;
let generateTemplate = `<li class="list-group-item AI-Suggestions-Option genieChatCont">${genieImage}              
                <div class="d-grid AI-Suggestions-Bg"> <div class="">
                    <img src="/../img/genie_bot/workflow.svg" alt="" height="230px" width="250px"/><div class="mt-4 d-flex justify-content-end">
                            <button class="btn btn-primary btn-sm me-5" id="btnGenerateAITemplate">Generate</button>
                      </div></div> </div></li>`;
let tempLoader = `<li class="list-group-item AI-Suggestions-Option genieChatCont" id=${getRandomId('Genie')}>${genieImage}<div class="d-grid w-100">
                    <div class="bg-white">
                        <img class="AIGenerateProcessImg mt-3 ms-3 mb-2" src="/../img/restore_GIF/restore.gif" alt="" height="200px" width="200px" />
                        <div> <img class="AICompletedImage w-100" src="/../img/genie_bot/wf_gen.svg" alt="">
                        <p class="text-center fs-7 mt-3 AICompletedText ">Completed!</p></div>                      
                    </div></div></li>`;

let genieinitialCont = ` <li class="list-group-item AI-Suggestions-Option AI_InitailContainer1" id=${getRandomId('Genie')}>
                <img class="AI-Profile" src="/../img/genie_bot/LOGO-SMALL.svg" />
                <div class="d-grid AI-Suggestions-Bg">
                    <div class="">
                        <p class="mb-2">Hi ${globalUserName}, this is<span class="text-primary fw-bold"> Dave.</span> I am here to help you configure workflows <span class="text-primary">autonomously</span> within a minimal timeframe.</p>
                        <p> Let's start with <span class="text-primary">autonomous mode.</span></p>
                    </div>
                </div>
            </li>`;

let genieInitialCont2 = `<li class="list-group-item AI-Suggestions-Option AI_InitailContainer2" id=${getRandomId('Genie')}>
                <img class="AI-Profile" src="/../img/genie_bot/LOGO-SMALL.svg" />
                <div class="d-grid AI-Suggestions-Bg">
                    <div class="">
                        <p>Select the option below to construct the workflow.</p>
                        <div class="AI-Option list-group">
                            <button type="button" class="list-group-item text-start d-flex align-items-center btnAIClass rounded list-group-item-action" id="btnAIInfraObject">
                                <i class="cp-infra-object fs-4 text-primary me-2"></i>
                                <div>
                                    <div class="fw-bold optionName">InfraObject</div>
                                    <small class="text-secondary">Create the workflow through or using infraObjects</small>
                                </div>
                            </button>
                            <button type="button" class="list-group-item text-start d-flex align-items-center btnAIClass rounded list-group-item-action" id="btnAITemplate">
                                <i class="cp-token fs-4 text-primary me-2"></i>
                                <div>
                                    <div class="fw-bold optionName">Solution</div>
                                    <small class="text-secondary">
                                        Create the workflow through or using workflow templates and map the inputs
                                    </small>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </li>`;

$('.AICompletedImage, .AICompletedText').hide();

const getUniqueContainer = () => {
    return `<li class="list-group-item AI-Suggestions-Option AI_InitailContainer2" id=${getRandomId('Genie')}>
                <img class="AI-Profile" src="/../img/genie_bot/LOGO-SMALL.svg" />
                <div class="d-grid AI-Suggestions-Bg">
                    <div class="">
                        <p>Select the option below to construct the workflow.</p>
                        <div class="AI-Option list-group">
                            <button type="button" class="list-group-item text-start d-flex align-items-center btnAIClass rounded list-group-item-action" id=${getRandomId('Genie')}>
                                <i class="cp-infra-object fs-4 text-primary me-2"></i>
                                <div>
                                    <div class="fw-bold optionName">InfraObject</div>
                                    <small class="text-secondary">Create the workflow through or using infraObjects.</small>
                                </div>
                            </button>
                            <button type="button" class="list-group-item text-start d-flex align-items-center btnAIClass rounded list-group-item-action" id=${getRandomId('Genie')}>
                                <i class="cp-token fs-4 text-primary me-2"></i>
                                <div>
                                    <div class="fw-bold optionName">Solution</div>
                                    <small class="text-secondary">
                                        Create the workflow through or using workflow templates and map the inputs.
                                    </small>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </li>`
}


const workflowActionUniqueCheck = (data) => {
    workflowActionArray = []
    let action = $('.workflowActions')
    for (let i = 0; i < action.length; i++) {

        if (action[i]?.id && (data?.actionInfo?.uniqueId !== action[i]?.id)) {
            let actionId = action[i]?.id
            let getDetails = actionId && atob($('#' + actionId).attr('details'))
            getDetails = getDetails ? JSON.parse(getDetails) : {}

            if (getDetails?.actionInfo?.actionName === data?.actionInfo?.actionName) {
                let decodedDetails = atob($('#' + actionId).attr('details'))
                let actionDetails = JSON.parse(decodedDetails)
                workflowActionArray.push(actionDetails)
            }
        }
    }
    return workflowActionArray;
}

$('#btnWorkFlowReport').on('click', async function () {

    var WorkflowId = $("#workflowList").val();
    var alertClass, iconClass, message;
    try {
        $("#btnWorkFlowReport").addClass('disabled');
        const url = `${RootUrl + Urls.getRunBookReport}?WorkflowId=${GlobalWorkflowId}`;
        const method = 'POST';
        const fetchResponse = await fetch(url, {
            method: method,
            headers: {
                'RequestVerificationToken': gettoken(),
            },

        });
        if (fetchResponse.ok) {
            const response = await fetchResponse.blob();
            console.log("Response from server:", response);
            if (response.type == "application/pdf") {
                const DateTime = new Date().toLocaleString('en-US', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    fractionalSecondDigits: 3,
                    hour12: false,
                }).replace(/[^0-9]/g, '');

                const formattedDateTime = DateTime.replace(/(\d{2})(\d{2})(\d{4})(\d{2})(\d{2})(\d{2})(\d{3})/, '$1$2$3_$4$5$6');
                downloadFileXls(response, GlobalWorkflowName + "_RunBook_" + formattedDateTime + ".xls", "application/xls");
                message = GlobalWorkflowName + " RunBook downloaded successfully";
                notificationAlert("success", message);
                $("#btnWorkFlowReport").removeClass('disabled');
            }
            else {
                message = GlobalWorkflowName + " RunBook downloaded failed!";
                notificationAlert("error", message);
                $("#btnWorkFlowReport").removeClass('disabled');
            }
        } else {
            message = GlobalWorkflowName + " RunBook downloaded failed!";
            notificationAlert("error", message);
            $("#btnWorkFlowReport").removeClass('disabled');
        }
        $("#btnWorkFlowReport").removeClass('disabled');
    } catch (error) {
        console.error(error);
        message = GlobalWorkflowName + " RunBook downloaded failed!";
        notificationAlert("error", message);
        $("#btnWorkFlowReport").removeClass('disabled');
    }

    reportData = [];
    data = [];
});


function downloadFileXls(blob, fileName, contentType) {
    try {
        const link = document.createElement("a");
        link.download = fileName;
        link.href = window.URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error("Error downloading file: " + error.message);
    }
}

/*  < ----- Genie Bot ----> */
$('#AI_SuggestionReset').hide()
$('#suggestionChatContainerParent').hide()
let checkTypeArray = []

const setPointerEventNone = () => {
    let cont = $('#genieChatContainer')
    cont.children().not(':last-child').css('pointer-events', 'none')
    setTimeout(() => {
        cont.find('.active').removeClass('active')
    }, 1500)
}

const scrollToBottom = () => {
    $(".AI-Suggestion-List").animate({ scrollTop: $('.AI-Suggestion-List').prop("scrollHeight") }, 700);
}

$(document).on('change', '.AIModeCheck', function () {
    hideChatComponents()
    let optionId = $(this)[0].id
    if (optionId === 'option2') {
        $('.spanTextAIConfig').text('Deals with interpreting different commands and is capable of executing privileged instructions.')
    } else {
        $('.spanTextAIConfig').text(' Autonomous mode refers to the period in the match where the Intelligence operates without minimum user input.')
    }
})

$('#AISavemodal').on('click', function () {
    $('.btnChatBotIcon').css('pointer-events', 'none').css('cursor', 'not-allowed').css('opacity', '0.3')
    $('#AI-ConfigurationModal').modal('hide');
    $('#genieChatContainer').empty()
    if (!$('#offcanvasScrolling').hasClass('show')) {
        $('#offcanvasScrolling').offcanvas('show')
    }
    if ($('#option1').is(':checked')) {
        $('#AI_SuggestionReset').show()
        $('#suggestionChatContainerParent').hide()
        $('#ActionSuggestionContainerAI').hide()
        setTimeout(() => {
            $('#genieChatContainer').append(genieinitialCont)
        }, 300)
        setTimeout(() => {
            $('#genieChatContainer').append(getUniqueContainer())
            scrollToBottom()
        }, 1000)
    } else {
        $('.AI_InitailContainer1').hide();
        $('.AI_InitailContainer2').hide();
        $('#AI_SuggestionReset').hide()
        $('#ActionSuggestionContainerAI').show()
    }
    scrollToBottom()
    setPointerEventNone()
});

$(document).on('click', '.btnAIClass', async function (e) {
    let btnId = $(this)[0].id
    $('.btnAIClass').removeClass('active')
    $('#' + btnId).addClass('active')
    let type = $('#' + btnId + ' .optionName').text()
    let userHtml = `<li class="list-group-item User-select-Option userChatCont" id=${getRandomId('genie')}><div class="User-Message-Bg">${type}</div>${userGenieImage}</li>`
    $('#genieChatContainer').append(userHtml)
    $('#workflowActions').empty()
    $('#EndWorkflowButton').hide()
    $("#versionText").hide();
    $('#workflowTitle').text('')
    $(".checkSaveWorkflow, .actionCheckBox").hide();

    newActionObj = {
        actionInfo: {
            properties: {},
            propertyData: { propertiesInfo: [] },
            formInput: []
        }
    };
    infraRoleReverseDetails = [];
    scrollToBottom()
    setPointerEventNone()
    if ($('#' + btnId + ' .optionName').text() === 'InfraObject') {
        genieSolution = false;

        setTimeout(() => {
            getAIInfraObject(Urls.getInfraObjectList);
        }, 1000)
    } else {
        setTimeout(() => {
            getAIInfraObject(Urls.getTemplateList);
        }, 1000)
    }
})

$(document).on('click', '.AISolutionBtnList', btnDebounce(function (e) {
    let solutionData = $(this).children().data('template')
    AISolutionId = $(this).children().first()[0].id

    $('.AISolutionBtnList').removeClass('active')
    $('#' + AISolutionId).parent().addClass('active')

    let AISolutionName = $(this).children().first()[0].textContent
    let userHtml = `<li id=${getRandomId('genie')} class="list-group-item User-select-Option userChatCont"><div class="User-Message-Bg">${AISolutionName}</div>${userGenieImage}</li>`
    $('#genieChatContainer').append(userHtml)

    $('#workflowActions').empty()

    scrollToBottom()
    setPointerEventNone()
    setTimeout(() => {
        getAISolutionActionType(solutionData)

    }, 1000)
}, 500))

const getAISolutionActionType = (solutionData) => {
    let count = 0;
    let appendData = '';
    let actionTypeUnique = [];
    solutionData.map((tempData) => {
        if (tempData.actionType === 'SwitchOver' || tempData.actionType === 'SwitchBack' || tempData.actionType === 'FailOver' || tempData.actionType === 'FailBack') {
            count++;
            if (!actionTypeUnique.includes(tempData.actionType)) {
                appendData += `<li type="button" id='${getRandomId('Solution')}' class="list-group-item list-group-item-action d-flex justify-content-between align-items-start solutionActionType"><div class="ms-2 me-auto AIsolutionSelect" id="${tempData.id}" data-properties='${JSON.stringify(tempData)}'><i class="cp-single-dot text-muted me-3 fs-9"></i>${tempData.actionType}</div></li>`;
                actionTypeUnique.push(tempData.actionType)
            }
        }
    })

    let InfraHtml = `<li id=${getRandomId('genie')} class="list-group-item AI-Suggestions-Option genieChatCont">${genieImage}<div class="d-grid gap-2 AI-Suggestions-Bg" style="width:83%;">
        <p class="mb-0"><i class="cp-workflow-type fs-5 text-primary me-2"></i>Choose Operation Type</p>
        <ol style="max-height:180px;" class="list-group list-group-flush AI_SolutionAppendList overflow-auto ms-3">${appendData}</ol></div></li>`

    let checkTypehtml = ` <li class="list-group-item AI-Suggestions-Option">
                <img class="AI-Profile" src="/../img/genie_bot/LOGO-SMALL.svg" />
                <div class="d-grid AI-Suggestions-Bg">
                    <div class="">
                      <p class="mb-1">Sorry ${globalUserName}, Unfortunately i couldn't figure out any operation type attached with this infraobject.</p>
                    </div>
                </div>         
            </li>`

    count > 0 ? $('#genieChatContainer').append(InfraHtml) : $('#genieChatContainer').append(checkTypehtml)
    scrollToBottom()
    setPointerEventNone()
}

$(document).on('click', '.solutionActionType', btnDebounce(function (e) {
    let propertiesData = $(this).children().data('properties')
    let actionId = $(this).children().first()[0].id
    $('.solutionActionType').removeClass('active')
    $('#' + actionId).parent().addClass('active')
    let AIActionName = $(this).children().first()[0].textContent
    let userHtml = `<li id=${getRandomId('genie')} class="list-group-item User-select-Option userChatCont"><div class="User-Message-Bg">${AIActionName}</div>${userGenieImage}</li>`
    $('#genieChatContainer').append(userHtml)
    restoreFieldObj = []
    restoreFormInput = []
    restoreUniqueObj = []
    $('.genieRestoreTemplate').empty()
    $('#btnGenerateSolutionTemplate').remove()
    scrollToBottom()
    setTimeout(() => {
        if (propertiesData) {
            if (propertiesData?.properties) getUniqueFormInput(propertiesData, 'genieSolution', 'restore')
        }
        setPointerEventNone()
    }, 1000)

}, 500))

$(document).on('click', '.AIInfraObjectBtnList', btnDebounce(function () {
    AIInfraId = $(this).children().attr('id')
    getWorkflowDetails(AIInfraId)
    $('.AIInfraObjectBtnList').removeClass('active')
    $('#' + $(this).attr('id')).addClass('active')
    let AIInfraName = $(this).children().first()[0].textContent
    let userHtml = `<li id=${getRandomId('genie')} class="list-group-item User-select-Option userChatCont"><div class="User-Message-Bg">${AIInfraName}</div>${userGenieImage}</li>`
    $('#genieChatContainer').append(userHtml)
    //$('.AITextInfraObject').text(AIInfraName)
    /* $('#User_InfraObjectData').show('slow')*/
    scrollToBottom()

}, 500))


const getAIWorkflowActionType = async () => {
    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.getWorkflowActionType,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                let data = result.data
                let dataLength = data.length;
                let appendData = '';
                let checkArr = []
                for (let i = 0; i < dataLength; i++) {
                    let imageSrc = ''
                    if (data[i].actionType === 'SwitchOver' || data[i].actionType === 'SwitchBack' || data[i].actionType == 'FailOver' || data[i].actionType == 'FailBack') {
                        checkTypeArray.includes(data[i].actionType) ? checkArr.push(true) : checkArr.push(false);
                        if (checkTypeArray.includes(data[i].actionType)) {
                            appendData += '<li type="button" class="list-group-item list-group-item-action d-flex justify-content-between align-items-start AIWorkflowActionTypeData"><div class="me-auto" id=' + data[i].id + '><i class="cp-single-dot text-muted fs-10 me-3"></i>' + data[i].actionType + `</div><i class="ms-3 ${imageSrc}" ></i></li>`
                        }
                    }
                }

                let actionTypehtml = `<li id=${getRandomId('genie')} class="list-group-item AI-Suggestions-Option genieChatCont">${genieImage}<div class="d-grid gap-2 AI-Suggestions-Bg" style="width:83%;">
                                            <p class="mb-0"><i class="cp-workflow-type fs-4 text-primary me-2"></i>Choose Operation Type</p> 
                                              <ol style="max-height:180px;"class="list-group list-group-flush overflow-auto ms-3">${appendData}</ol></div></li>`

                //let actionTypehtml = `<li id=${getRandomId('genie')} class="list-group-item AI-Suggestions-Option genieChatCont">${genieImage}
                //<div class="d-grid gap-2 AI-Suggestions-Bg">
                //    <p class="mb-2">Choose operation type</p>
                //    <ul class="list-group list-group-flush"> ${appendData}
                //    </ul></div></li>`

                let checkTypehtml = ` <li class="list-group-item AI-Suggestions-Option">
                <img class="AI-Profile" src="/../img/genie_bot/LOGO-SMALL.svg" />
                <div class="d-grid AI-Suggestions-Bg">
                    <div class="">
                      <p class="mb-1">Couldn't find any solutions attached with this infraObject.</p>
                    </div>
                </div>         
            </li>`

                let checkTypehtml2 = ` <li class="list-group-item AI-Suggestions-Option" id=${getRandomId('genie')}>
                <img class="AI-Profile" src="/../img/genie_bot/LOGO-SMALL.svg" />
                <div class="d-grid AI-Suggestions-Bg">
                    <div class="row">
                      <p class="mb-1">Do you want to continue with different infraObject ? </p>
                      <div class='mt-1'>
                         <button class='btn btn-secondary btn-sm ms-4 me-2 btnOtherSolutionNo'>cancel</button>
                         <button class='btn btn-primary btn-sm btnOtherSolutionYes'>Yes</button>
                      </div>
                    </div>
                </div>         
            </li>`

                if (checkArr.includes(true)) {
                    $('#genieChatContainer').append(actionTypehtml)
                } else {
                    $('#genieChatContainer').append(checkTypehtml)
                    setTimeout(() => {
                        $('#genieChatContainer').append(checkTypehtml2);
                        scrollToBottom();
                        setPointerEventNone()
                    }, 1000)
                }
            } else {
                $('.AIWorkflowActionTypeCont').html('<div>Error Occured</div>')
            }
        }
    })
    scrollToBottom();
    setPointerEventNone()
}

$(document).on('click', '.btnOtherSolutionYes', btnDebounce(function () {
    let userHtml = `<li id=${getRandomId('genie')} class="list-group-item User-select-Option userChatCont"><div class="User-Message-Bg">${'Yes'}</div>${userGenieImage}</li>`
    $('#genieChatContainer').append(userHtml)
    scrollToBottom();
    setTimeout(() => {
        let randomId = getRandomId('genie')
        $('#genieChatContainer').append($('.AI_InfraAppendList').last().parents('.genieChatCont').clone().attr('id', randomId))
        $(`#${randomId}`).css('pointer-events', '')
        let infraList = $(`#${randomId} .AIInfraObjectBtnList`)
        $('.AIInfraObjectBtnList').removeClass('active')
        infraList.each((idx, obj) => {
            $(obj).attr('id', getRandomId('infraList'))
        })
        scrollToBottom();
    }, 1000)

    setPointerEventNone()
}, 500))

$(document).on('click', '.btnOtherSolutionNo', btnDebounce(function () {
    $('#AI_SuggestionClose').trigger('click')
}, 500))

const getAIInfraObject = async (url) => {
    await $.ajax({
        type: "GET",
        url: RootUrl + url,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                let data = result.data
                let dataLength = data.length;
                let appendData = '';
                let checkReplicationType = filterReplicationType(data)
                isTemplateWorkFlow = true;
                for (let i = 0; i < dataLength; i++) {
                    if (url === "ITAutomation/WorkflowConfiguration/GetTemplateList") {
                        if (data[i].replicationTypeName !== null && data[i].replicationTypeName !== undefined && data[i].replicationTypeId !== null && checkReplicationType.includes(data[i].replicationTypeId)) {
                            appendData += `<li type="button" class="list-group-item list-group-item-action AISolutionBtnList" id='${getRandomId('Solution')}'><span class="ms-1 AIsolutionSelect" id="${data[i].replicationTypeId}" data-template='${JSON.stringify(data[i]?.templateListVm)}'>${data[i].replicationTypeName}</span></li>`;
                        }
                    } else {
                        appendData += `<li type="button" class="list-group-item list-group-item-action  AIInfraObjectBtnList" id=${getRandomId('infraList')}><span class="ms-1 AIinfraNameSelect" id="${data[i].id}">${data[i].name}</span></li>`;
                    }
                }
                let InfraHtml = `<li id=${getRandomId('genie')} class="list-group-item AI-Suggestions-Option genieChatCont">${genieImage}<div class="d-grid gap-2 AI-Suggestions-Bg">
                       <p class="mb-0"><i class="${url === "ITAutomation/WorkflowConfiguration/GetTemplateList" ? 'cp-token' : 'cp-infra-object'} p-1 fs-5 text-primary me-2"></i>Choose ${url === "ITAutomation/WorkflowConfiguration/GetTemplateList" ? 'Solution' : 'InfraObject'}</p> <div class="input-group mb-2">
                       <div class="input-group-addon p-2"><i class="cp-search"></i></div> <input class="form-control searchInfraObject" placeholder="Search" /></div>
                       <ol style="max-height:180px;" class="list-group list-group-flush AI_InfraAppendList list-group-numbered overflow-auto">${appendData}</ol></div></li>`
                $('#genieChatContainer').append(InfraHtml)
            } else {
                $('.AI_InfraAppendList').html('<div>Error Occured</div>')
            }
        }
    })
    scrollToBottom()
    setPointerEventNone()
}

const filterReplicationType = (data) => {
    let arr = []
    if (data.length > 0) {
        data.forEach((d) => {
            if (d.replicationTypeId && d.replicationTypeName) {
                d.templateListVm.forEach((t) => {
                    if (t.actionType === 'SwitchOver' || t.actionType === 'SwitchBack' || t.actionType === 'FailOver' || t.actionType === 'FailBack') {
                        arr.push(d.replicationTypeId)
                    }
                })
            }
        })
    }
    return arr
}

$(document).on('keyup', '.searchInfraObject', btnDebounce(function () {
    let search = $(this).val().toLowerCase();
    $('.AI_InfraAppendList .noDataListContainer').remove();
    $('.AI_InfraAppendList').children().each(function (idx, obj) {
        if ($(obj).text().toLowerCase().includes(search)) {
            if ($('#' + obj.id).hasClass('d-none')) {
                $('#' + obj.id).removeClass('d-none')
                $('#' + obj.id).addClass('d-flex')
            }
        } else {
            $('#' + obj.id).removeClass('d-flex')
            $('#' + obj.id).addClass('d-none')
        }
    })
    // setTimeout(() => {
    if ($('.AIInfraObjectBtnList :visible').length === 0) {
        $('.AI_InfraAppendList').append(`<li class='noDataListContainer'>No results found</li>`)
    }
    //  },300)

}, 300))

//$(document).on('keyup', '.searchSolutionAction', function () {
//    let search = $(this).val().toLowerCase();
//    $('.AI_SolutionAppendList .noDataListContainer').remove();
//    $('.AI_SolutionAppendList').children().each(function (idx, obj) {
//        if ($(obj).text().toLowerCase().startsWith(search)) {
//            if ($('#' + obj.id).hasClass('d-none')) {
//                $('#' + obj.id).removeClass('d-none')
//                $('#' + obj.id).addClass('d-flex')
//            }
//        } else {
//            $('#' + obj.id).removeClass('d-flex')
//            $('#' + obj.id).addClass('d-none')
//        }
//    })
//    if ($('.AI_SolutionAppendList :visible').length === 0) {
//        $('.AI_SolutionAppendList').append(`<li class='noDataListContainer'>No results found</li>`)
//    }
//})

const getWorkflowDetails = async (id) => {
    let data = {}
    data.infraobjectId = id

    await $.ajax({

        type: "GET",
        url: RootUrl + Urls.GetWorkflowByInfra,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (Array.isArray(result.data) && result.data.length > 0) {
                    checkTypeArray = [];
                    result.data.forEach((d) => {
                        checkTypeArray.push(d.actionType)
                    })
                }
                setTimeout(() => {
                    getAIWorkflowActionType()
                    setPointerEventNone()
                }, 1000)

            }
        }
    })
}

const hideChatComponents = () => {
    $('#lastConfiguredAISuggestion, #AISuggestionsList').empty()
    $('#suggestionChatContainerParent').hide()
}

$('#AI_SuggestionClose').on('click', btnDebounce(function () {
    hideChatComponents();
    $('#option1, #option2').prop('checked', false)
    $('.btnChatBotIcon').removeAttr('style')
    removeErrorSugg()
}, 500))

$('#AI_SuggestionReset').on('click', btnDebounce(function () {
    $('#AISavemodal').trigger('click')
}, 500))

$(document).on('click', '.AIWorkflowActionTypeData', btnDebounce(async function (e) {
    let operationId = $(this).children().first()[0].id
    $('.AIWorkflowActionTypeData').removeClass('active')
    $('#' + operationId).parent().addClass('active')
    AIActionType = $(this).children().first()[0].textContent
    let userHtml = `<li id=${getRandomId('genie')} class="list-group-item User-select-Option userChatCont"><div class="User-Message-Bg">${AIActionType}</div>${userGenieImage}</li>`
    $('#genieChatContainer').append(userHtml)
    scrollToBottom()
    setPointerEventNone()
    if (checkTypeArray.includes(AIActionType)) {
        setTimeout(() => {
            $('#genieChatContainer').append(generateTemplate)
            scrollToBottom()
            setPointerEventNone()
        }, 1500)
    }
}, 500))

let checkComponets = { 'prServer': [], 'drServer': [], 'prDB': [], 'drDB': [], };

const appendAIRestoreWorkflow = (item, data) => {
    item.actionInfo.formInput.forEach((key, index) => {

        let deleteId = key?.id

        if (key?.hasOwnProperty('optionType') && key?.optionType == 'PRDBServer') {
            let prdbData = JSON.parse(data?.serverProperties);
            let getPropertiesData = item?.actionInfo?.propertyData?.propertiesInfo?.findIndex(keys => keys?.id == key?.id)

            if (prdbData?.PR?.id?.includes(',')) {
                let splitServer = prdbData?.PR?.id?.split(',');
                let splitServerName = prdbData?.PR?.name?.split(',');

                let prIdFound = false
                checkComponets?.prServer?.forEach((p, i) => {

                    if (p?.id == key?.id) {
                        prIdFound = true;
                        item.actionInfo.properties[key.name] = p?.value
                        key.id = p?.value

                        replacePropertiesData(getPropertiesData, item, p?.value, p?.name)
                    }
                })

                if (!prIdFound) {

                    let index = checkComponets?.prServer?.length

                    checkComponets.prServer.push({ id: key.id, value: splitServer[index], name: splitServerName[index] })

                    item.actionInfo.properties[key.name] = splitServer[index];
                    key.id = splitServer[index]

                    replacePropertiesData(getPropertiesData, item, splitServer[index], splitServerName[index])
                }

            } else {

                item.actionInfo.properties[key.name] = prdbData?.PR?.id;
                key.id = prdbData?.PR?.id

                replacePropertiesData(getPropertiesData, item, prdbData?.PR?.id, prdbData?.PR?.name)

            }
            delete item.actionInfo.properties[deleteId]

        } else if (key?.hasOwnProperty('optionRoleType') && key?.optionRoleType?.toLowerCase() == 'application') {
            let getPropertiesData = item?.actionInfo?.propertyData?.propertiesInfo?.findIndex(keys => keys?.id == key?.id)

            let prserverData = JSON.parse(data?.serverProperties);

            item.actionInfo.properties[key.name] = prserverData?.PR?.id;
            key.id = prserverData?.PR?.id

            replacePropertiesData(getPropertiesData, item, prserverData?.PR?.id, prserverData?.PR?.name)

            delete item.actionInfo.properties[deleteId]

        } else if (key?.hasOwnProperty('optionRoleType') && key?.optionRoleType?.toLowerCase() == 'virtualization') {

            if (key?.hasOwnProperty('optionType') && key?.optionType == 'DRESXIServer') {
                let getPropertiesData = item?.actionInfo?.propertyData?.propertiesInfo?.findIndex(keys => keys?.id == key?.id)

                let drserverData = JSON.parse(data?.serverProperties);

                item.actionInfo.properties[key.name] = drserverData?.DR?.id;
                key.id = drserverData?.DR?.id;

                replacePropertiesData(getPropertiesData, item, drserverData?.DR?.id, drserverData?.DR?.name)

                delete item.actionInfo.properties[deleteId]
            } else if (key?.hasOwnProperty('optionRoleType') && key?.optionType == 'PRESXIServer') {
                let getPropertiesData = item?.actionInfo?.propertyData?.propertiesInfo?.findIndex(keys => keys?.id == key?.id)

                let prserverData = JSON.parse(data?.serverProperties);

                item.actionInfo.properties[key.name] = prserverData?.PR?.id;
                key.id = prserverData?.PR?.id;

                replacePropertiesData(getPropertiesData, item, prserverData?.PR?.id, prserverData?.PR?.name)

                delete item.actionInfo.properties[deleteId]
            }

        } else if (key?.hasOwnProperty('optionType') && key?.optionType == 'DRDBServer') {
            let drdbData = JSON.parse(data?.serverProperties);
            let getPropertiesData = item?.actionInfo?.propertyData?.propertiesInfo?.findIndex(keys => keys?.id == key?.id)

            if (drdbData?.DR?.id?.includes(',')) {
                let splitServer = drdbData?.DR?.id?.split(',');
                let splitServerName = drdbData?.DR?.id?.split(',');

                let drIdFound = false;

                checkComponets?.drServer?.forEach((p, i) => {

                    if (p?.id == key?.id) {
                        drIdFound = true;
                        item.actionInfo.properties[key.name] = p?.value
                        key.id = p?.value

                        replacePropertiesData(getPropertiesData, item, p?.value, p?.name)
                    }
                })

                if (!drIdFound) {
                    let index = checkComponets?.drServer?.length

                    checkComponets.drServer.push({ id: key.id, value: splitServer[index], name: splitServerName[index] })
                    item.actionInfo.properties[key.name] = splitServer[index];
                    key.id = splitServer[index]

                    replacePropertiesData(getPropertiesData, item, splitServer[index], splitServerName[index])
                }

            } else {

                item.actionInfo.properties[key.name] = drdbData?.DR?.id
                key.id = drdbData?.DR?.id

                replacePropertiesData(getPropertiesData, item, drdbData?.DR?.id, drdbData?.DR?.name)
            }
            delete item.actionInfo.properties[deleteId]

        } else if (key?.hasOwnProperty('optionType') && key?.optionType == 'DRDB') {
            let drdbData = JSON.parse(data?.databaseProperties);
            let getPropertiesData = item?.actionInfo?.propertyData?.propertiesInfo?.findIndex(keys => keys?.id == key?.id)

            if (drdbData?.DR?.id?.includes(',')) {
                let splitServer = drdbData?.DR?.id?.split(',');
                let splitServerName = drdbData?.DR?.name?.split(',');

                let drDBIdFound = false;

                checkComponets?.drDB?.forEach((p, i) => {

                    if (p?.id == key?.id) {
                        drDBIdFound = true;
                        item.actionInfo.properties[key.name] = p?.value
                        key.id = p?.value

                        replacePropertiesData(getPropertiesData, item, p?.value, p?.name)
                    }
                })

                if (!drDBIdFound) {
                    let index = checkComponets?.drDB?.length

                    checkComponets.drDB.push({ id: key.id, value: splitServer[index], name: splitServerName[index] })
                    item.actionInfo.properties[key.name] = splitServer[index];
                    key.id = splitServer[index]

                    replacePropertiesData(getPropertiesData, item, splitServer[index], splitServerName[index])
                }

            } else {

                item.actionInfo.properties[key.name] = drdbData?.DR?.id
                key.id = drdbData?.DR?.id

                replacePropertiesData(getPropertiesData, item, drdbData?.DR?.id, drdbData?.DR?.name)

            }
            delete item.actionInfo.properties[deleteId]

        } else if (key?.hasOwnProperty('optionType') && key?.optionType == 'PRDB') {
            let prdbData = JSON.parse(data?.databaseProperties);
            let getPropertiesData = item?.actionInfo?.propertyData?.propertiesInfo?.findIndex(keys => keys?.id == key?.id)

            if (prdbData?.PR?.id?.includes(',')) {
                let splitServer = prdbData?.PR?.id?.split(',');
                let splitServerName = prdbData?.PR?.name?.split(',');

                let prDBIdFound = false;

                checkComponets?.prDB?.forEach((p, i) => {

                    if (p?.id == key?.id) {
                        prDBIdFound = true;
                        item.actionInfo.properties[key.name] = p?.value
                        key.id = p?.value

                        replacePropertiesData(getPropertiesData, item, p?.value, p?.name)
                    }
                })

                if (!prDBIdFound) {
                    let index = checkComponets?.prDB?.length

                    checkComponets.prDB.push({ id: key.id, value: splitServer[index] })
                    item.actionInfo.properties[key.name] = splitServer[index];
                    key.id = splitServer[index]

                    replacePropertiesData(getPropertiesData, item, splitServer[index], splitServerName[index])
                }

            } else {

                item.actionInfo.properties[key.name] = prdbData?.PR?.id;
                key.id = prdbData?.PR?.id;

                replacePropertiesData(getPropertiesData, item, prdbData?.PR?.id, prdbData?.PR?.name)
            }
            delete item.actionInfo.properties[deleteId]

        } else if (key?.hasOwnProperty('type') && key?.type == 'replication') {

            if (key?.name?.toLowerCase()?.includes('prreplication')) {
                let getPropertiesData = item?.actionInfo?.propertyData?.propertiesInfo?.findIndex(keys => keys?.id == key?.id)

                let prreplicationData = JSON.parse(data?.replicationProperties);

                item.actionInfo.properties[key.name] = prreplicationData?.PR?.id;
                key.id = prreplicationData?.PR?.id;

                replacePropertiesData(getPropertiesData, item, prreplicationData?.PR?.id, prreplicationData?.PR?.name)

                delete item.actionInfo.properties[deleteId]
            } else if (key?.name?.toLowerCase()?.includes('drreplication')) {
                let getPropertiesData = item?.actionInfo?.propertyData?.propertiesInfo?.findIndex(keys => keys?.id == key?.id)

                let drreplicationData = JSON.parse(data?.replicationProperties);

                item.actionInfo.properties[key.name] = drreplicationData?.DR?.id;
                key.id = drreplicationData?.DR?.id;

                replacePropertiesData(getPropertiesData, item, drreplicationData?.DR?.id, drreplicationData?.DR?.name)

                delete item.actionInfo.properties[deleteId]
            } else if (key?.name?.toLowerCase()?.includes('@replication')) {
                item.actionInfo.formInput.forEach((x) => {
                    if (x.optionType === 'PRDBServer') {
                        let getPropertiesData = item?.actionInfo?.propertyData?.propertiesInfo?.findIndex(keys => keys?.id == key?.id)

                        let prreplicationData = JSON.parse(data?.replicationProperties);

                        item.actionInfo.properties[key.name] = prreplicationData?.PR?.id;
                        key.id = prreplicationData?.PR?.id;

                        replacePropertiesData(getPropertiesData, item, prreplicationData?.PR?.id, prreplicationData?.PR?.name)

                        delete item.actionInfo.properties[deleteId]
                    } else if (x.optionType === 'DRDBServer') {
                        let getPropertiesData = item?.actionInfo?.propertyData?.propertiesInfo?.findIndex(keys => keys?.id == key?.id)

                        let drreplicationData = JSON.parse(data?.replicationProperties);

                        item.actionInfo.properties[key.name] = drreplicationData?.DR?.id;
                        key.id = drreplicationData?.DR?.id;

                        replacePropertiesData(getPropertiesData, item, drreplicationData?.DR?.id, drreplicationData?.DR?.name)

                        delete item.actionInfo.properties[deleteId]
                    }
                })
            }

        } else {

            let getPropertiesData = item?.actionInfo?.propertyData?.propertiesInfo?.findIndex(keys => keys?.id == key?.id)

            replacePropertiesData(getPropertiesData, item)

        }

    })
}

const replacePropertiesData = (getPropertiesData, item, id = '', value = '') => {

    if (getPropertiesData !== -1 && item?.actionInfo?.propertyData?.propertiesInfo[getPropertiesData]) {
        item.actionInfo.propertyData.propertiesInfo[getPropertiesData].id = id;
        item.actionInfo.propertyData.propertiesInfo[getPropertiesData].value = value;
    }

}
function replaceAIWorkflow(Properties, data, mode = '') {
    let templateRestoreNodes = Properties?.length && Properties;
    let ConvertText = $('#findGenerateValue').val();

    Properties?.length && Properties.forEach((item) => {
        if (item.hasOwnProperty('children') || item.hasOwnProperty('parallelActions')) {
            let childelem = item.hasOwnProperty('children') ? item.children : item.parallelActions
            childelem.forEach((childItem) => {

                if (ConvertText) childItem.actionInfo.actionName = childItem?.actionInfo?.actionName?.replace(ConvertText, $('#replaceGenerateValue').val())

                appendAIRestoreWorkflow(childItem, data)
            })
        } else if (item.hasOwnProperty('groupName')) {
            item.groupActions.forEach((groupItem) => {
                if (groupItem.hasOwnProperty('children')) {
                    groupItem.children.forEach((childItem) => {
                        if (ConvertText) childItem.actionInfo.actionName = childItem?.actionInfo?.actionName?.replace(ConvertText, $('#replaceGenerateValue').val())
                        appendAIRestoreWorkflow(childItem, data)
                    })
                } else {
                    if (ConvertText) groupItem.actionInfo.actionName = groupItem?.actionInfo?.actionName?.replace(ConvertText, $('#replaceGenerateValue').val())
                    appendAIRestoreWorkflow(groupItem, data)
                }
            })
        } else if (!item.actionInfo.hasOwnProperty('IsGroup')) {
            if (ConvertText) item.actionInfo.actionName = item?.actionInfo?.actionName?.replace(ConvertText, $('#replaceGenerateValue').val())
            appendAIRestoreWorkflow(item, data)
        }
    })

    if (mode == 'infrarestore') {
        $("#workflowActions").empty();

        workFlowData.push(...templateRestoreNodes)
        loadWorkFlow(templateRestoreNodes, 'template');
        workflowSaveMode = "Save";
        $('#workflowTitle').text('Untitled Workflow');
        $('#workflowRunningStatus').addClass('d-none');
        $('#workflowLockStatus, #workflowVerifyStatus').hide();
        $(".checkSaveWorkflow").show();
        $('#versionText').text('')
        $('#workflowVersion').text('')
        $('#attachedInfraObjectName, #attachedInfraObjectType, #attachedProfileName').text('').parent().hide();
        $('#RestoreModal').modal('hide');
        $('#templateComponent').addClass('d-none')
    }
}

$(document).on('click', '#btnGenerateAITemplate', btnDebounce(async function () {
    $('#genieChatContainer').append(tempLoader)
    $('.AICompletedImage, .AICompletedText').hide()
    scrollToBottom()
    setPointerEventNone()
    if (genieSolution) {
        setTimeout(() => {
            replaceObjValues(restoreUniqueObj, 'template', templateNodes)
            $('.AIGenerateProcessImg').hide()
            $('#btnAIOption').hide()
            $('.AICompletedImage, .AICompletedText').show()
            saveGenieWorkflow()
            scrollToBottom()
            setPointerEventNone()
        }, 8000)

    } else {
        setTimeout(async () => {
            let data = {}
            data.infraObjectId = AIInfraId
            data.actionType = AIActionType

            await $.ajax({
                type: "GET",
                url: RootUrl + Urls.GetTemplateByReplicationTypeId,
                data: data,
                dataType: "json",
                traditional: true,
                success: function (result) {
                    if (result.success) {
                        let data = result.data
                        let Properties = JSON.parse(data.properties)

                        replaceAIWorkflow(Properties?.nodes, data);
                        AIRestoreWorkflow = Properties
                        $('.AIGenerateProcessImg').hide()
                        $('#btnAIOption').hide()
                        $('.AICompletedImage, .AICompletedText').show()
                        saveGenieWorkflow();
                        scrollToBottom()
                        setPointerEventNone()
                        loadWorkFlow(Properties?.nodes, 'template')
                        $('#btnSaveModalOpen').prop('disabled', false);
                        $('#workflowTitle').text('Untitled Workflow');
                        $('#workflowRunningStatus').addClass('d-none');
                        $('#workflowLockStatus, #workflowVerifyStatus').hide();
                        workflowSaveMode = "Save";
                        $(".checkSaveWorkflow").show();
                    } else {
                        $('.AIGenerateProcessImg').hide()
                        $('.AICompletedText').text('Error Occured')
                        $('.AICompletedText').show('slow')
                    }
                },
            })
        }, 8000)
    }
}, 500))

const saveGenieWorkflow = () => {
    let saveText = `The workflow is not yet saved. Would you like to save this workflow?`;

    let InfraHtml = `<li id=${getRandomId('genie')} class="list-group-item AI-Suggestions-Option genieChatCont deletebleList">${genieImage}<div class="d-grid gap-2 AI-Suggestions-Bg"><span>${saveText}</span>
                    </div></li> <div class='d-flex gap-2 p-1 pb-0 justify-content-end deletebleList mb-3 me-4'><button class='btn btn-sm btn-outline-primary rounded-pill' id='genieSaveNoBtn'>No</button><button class='btn btn-sm btn-outline-primary rounded-pill' id='genieSaveYesBtn'>Yes</button></div>`
    setTimeout(() => {
        $('#genieChatContainer').append(InfraHtml)
        scrollToBottom()
        setPointerEventNone()
    }, 1000)
}

$(document).on('click', '#genieSaveYesBtn', btnDebounce(function () {
    workflowEditable = false;
    isFromGenie = true;
    $('#workflowName-error').text('').removeClass('field-validation-error');
    openSaveModal()
}, 500))

$(document).on('click', '#genieSaveNoBtn', btnDebounce(function () {
    let text = 'It appears that you require a new workflow. Is it your preference to keep developing new processes?'
    let InfraHtml = `<li id=${getRandomId('genie')} class="list-group-item AI-Suggestions-Option genieChatCont deletebleList">${genieImage}<div class="d-grid gap-2 AI-Suggestions-Bg"><span>${text}</span>
        </div></li> <div class='d-flex gap-2 p-1 pb-0 justify-content-end deletebleList mb-3 me-4'><button class='btn btn-sm btn-outline-primary rounded-pill' id='genieInitializeNoBtn'>No</button><button class='btn btn-sm btn-outline-primary rounded-pill' id='genieInitializeBtn'>Yes</button></div>`
    $('#genieChatContainer .deletebleList').remove()

    setTimeout(() => {
        $('#genieChatContainer').append(InfraHtml)
        scrollToBottom()
        setPointerEventNone()
    }, 1000)

}, 500))


$(document).on('click', '#genieInitializeNoBtn', btnDebounce(function () {
    $('#AI_SuggestionClose').trigger('click')
}, 500))


$(document).on('click', '#genieInitializeBtn', btnDebounce(function () {
    $('#genieChatContainer .deletebleList').remove()
    setTimeout(() => {
        $('#AI_SuggestionReset').trigger('click')
        scrollToBottom()
        setPointerEventNone()
    }, 1000)

}, 500))


/*  < ----- workflow prediction ---->*/

$('#mismatchAISuggestion').hide();
$('#ActionSuggestionContainerAI').hide()
$('#AIIngnoreButton').hide();
$('#lastConfiguredAISuggestion, #AISuggestionsList').empty();
let AISuggestionList = []

const getPrediction = async (currentId, previousId) => {
    $('#genieChatContainer').empty()
    $('#mismatchAISuggestionList').empty()
    $('#mismatchActionCheck').empty()
    $('#lastConfiguredAISuggestion').empty()
    $('#AISuggestionsList').empty()

    AISuggestionList = []
    let suggestData = {
        actionId: currentId,
    }
    if (currentId !== previousId) {
        previousId !== '' ? suggestData.previousId = previousId : null
    }

    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.getWorkflowPrediction,
        data: suggestData,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                let data = result.data
                if (data.status) {
                    lastConfiguredAction(newActionObj, '#lastConfiguredAISuggestion')
                    $('#ActionSuggestionContainerAI').show()
                    $('#suggestionChatContainerParent').show()
                    if (data.workflowPredictionListByActionIdVms.length > 0) {
                        data.workflowPredictionListByActionIdVms = data.workflowPredictionListByActionIdVms.filter((item) => item.nextPossibleId !== '')
                        if (data.workflowPredictionListByActionIdVms.length > 0) {
                            showPrimarySuggestion(data)
                            suggestionActions(data.workflowPredictionListByActionIdVms, '#AISuggestionsList')
                        } else {
                            $('#offcanvasScrolling').offcanvas('show')
                            $('.textBeforeAppendSuggestion').html(`<div class='mt-4 text-danger'>No suggestion found</div>`)
                        }
                    }
                } else {
                    $('#ActionSuggestionContainerAI').hide()
                    $('#suggestionChatContainerParent').hide()
                    $('#mismatchAISuggestion').show();
                    $('#offcanvasScrolling').offcanvas('show')
                    lastConfiguredAction(newActionObj, '#mismatchActionCheck')
                    suggestionActions(data.matchedActions, '#mismatchAISuggestionList')
                    $('#AIIngnoreButton').show();
                    $('#workflowActions').children().last().find('.workflowActions').addClass('selected-error')
                    $('#workflowActions').children().last().find('.workflowActions').append(`<img class="shadow AIErrorImage" type="button" src="/../img/genie_bot/Error-Gif.gif" style="margin-right: -12px; height:16px;" />`)

                }
            } else {
                errorNotification(result)
            }
        }
    })
    scrollToBottom()
}

$('#AIIngnoreButton').on('click', function () {
    $('#mismatchAISuggestion').hide();
    $('#AIIngnoreButton').hide();
    $('#workflowActions').children().last().find('.workflowActions').removeClass('selected-error')
    $('.workflowActions .AIErrorImage').remove()
    $('#mismatchAISuggestionList').empty()
    $('#mismatchActionCheck').empty()
    let gerPreviousNodeId = dataDisplay.children().last().prev().children()[1].id
    let gerLastNodeId = dataDisplay.children().last().prev().children()[1].id
    getPrediction(JSON.parse(atob($('#' + gerLastNodeId).attr('details'))).actionInfo.actionType, JSON.parse(atob($('#' + gerPreviousNodeId).attr('details'))).actionInfo.actionType)
})

const showPrimarySuggestion = (data) => {
    //if (data?.workflowPredictionListByActionIdVms?.length > 0) {
    newActionObj = {
        actionInfo: {
            properties: {},
            formInput: []
        }
    };
    let nodeId = $('.actiontype[nodeid=' + data?.workflowPredictionListByActionIdVms[0]?.nodeId + ']')
    actionNodeId = nodeId.attr("nodeId")
    parentActionIcon = nodeId.attr("parenticon")
    parentActionId = nodeId.attr("id")
    parentActionColor = nodeId.attr("parentcolor")

    newActionObj.actionInfo.actionName = data.workflowPredictionListByActionIdVms[0]?.nextPossibleActionName
    newActionObj.actionInfo.actionType = data.workflowPredictionListByActionIdVms[0].nextPossibleId
    newActionObj.actionInfo.nodeId = data.workflowPredictionListByActionIdVms[0].nodeId
    if (!newActionObj.actionInfo.hasOwnProperty('uniqueId')) newActionObj.actionInfo.uniqueId = getRandomId('node')
    if (!newActionObj.actionInfo.hasOwnProperty('IsParallel')) newActionObj.actionInfo.IsParallel = false
    newActionObj.actionInfo.parentActionId = parentActionId
    newActionObj.actionInfo.icon = parentActionIcon
    newActionObj.actionInfo.color = parentActionColor
    if (!newActionObj.hasOwnProperty("stepId")) newActionObj.stepId = generateStepId();
    newActionObj.actionInfo.description = '';
    newActionObj.actionInfo.rto = 3;
    newActionObj.actionInfo.email = false;
    newActionObj.actionInfo.sms = false;

    $('#offcanvasScrolling').offcanvas('show')
    appendSuggestion(newActionObj);
    $(".sectionData").empty();
    // }

}

const suggestionActions = (data, id) => {
    $(id).empty();
    $(id).empty()
    if (data.length > 0) {
        data?.forEach((item, index) => {
            if (index !== 0) {
                if (item.nextPossibleActionName !== "" && item.nextPossibleId !== "" && item.nodeId !== null)
                    getSuggestionDetails(item, id)
            }
        })
    }
    if ($(id).children().length === 0) {
        $('.textBeforeAppendSuggestion').text('')
        $(id).append(`<div>No secondary suggestion found</div>`)
    }
}

const getSuggestionDetails = (data, id) => {
    let SuggestionObject = {
        actionInfo: {
            properties: {},
            formInput: []
        }
    };

    let nodeId = $('.actiontype[nodeid=' + data.nodeId + ']')

    actionNodeId = nodeId.attr("nodeId")
    parentActionIcon = nodeId.attr("parenticon")
    parentActionId = nodeId.attr("id")
    parentActionColor = nodeId.attr("parentcolor")

    SuggestionObject.actionInfo.actionName = data.nextPossibleActionName
    SuggestionObject.actionInfo.actionType = data.nextPossibleId
    SuggestionObject.actionInfo.nodeId = data.nodeId
    if (!SuggestionObject.actionInfo.hasOwnProperty('uniqueId')) SuggestionObject.actionInfo.uniqueId = getRandomId('node')
    if (!SuggestionObject.actionInfo.hasOwnProperty('IsParallel')) SuggestionObject.actionInfo.IsParallel = false
    SuggestionObject.actionInfo.parentActionId = parentActionId
    SuggestionObject.actionInfo.icon = parentActionIcon
    SuggestionObject.actionInfo.color = parentActionColor
    if (!SuggestionObject.hasOwnProperty("stepId")) SuggestionObject.stepId = generateStepId();
    SuggestionObject.actionInfo.description = '';
    SuggestionObject.actionInfo.rto = 3;
    SuggestionObject.actionInfo.email = false;
    SuggestionObject.actionInfo.sms = false;
    sugestionListAppend(SuggestionObject, id)
}

const sugestionListAppend = (SuggestionObject, id, mode = '') => {
    let dataIcon = $(`[nodeid="${SuggestionObject.actionInfo.nodeId}"]`).attr('parenticon') || 'cp-flow'
    let dataColor = $(`[nodeid="${SuggestionObject.actionInfo.nodeId}"]`).attr('parentcolor')
    dataColor = dataColor === 'rgb(255,255,255)' ? '#8f8f8f' : dataColor;
    let icon = id === '#mismatchAISuggestionList' ? 'cp-reload' : 'cp-circle-plus'
    const iconClass = `${SuggestionObject?.actionInfo?.icon ?? dataIcon} workflowTextClass circle fs-7`;
    let loopedSuggestions = `<div class="AISuggestionActions mx-3 border" parentId='${SuggestionObject?.actionInfo?.parentActionId}' id='${SuggestionObject?.actionInfo?.uniqueId}' details='${JSON.stringify(SuggestionObject)}'">
                <i class="${iconClass} workflowTextClass circle fs-7" style='background: ${SuggestionObject?.actionInfo?.color ?? dataColor}'></i>
                <div class="flex-fill text-truncate mx-2">
                    
                    <span class="actionSpan" title=${SuggestionObject?.actionInfo?.actionName}>${SuggestionObject?.actionInfo?.actionName}</span>
                </div>
                <i type="button" class="addSuggestionAIAction ${icon} text-success" style="margin-right: -12px;" ></i>
            </div>`

    if (mode === 'prepend') {
        $(id).prepend(loopedSuggestions)
    } else {
        $(id).append(loopedSuggestions)
    }
}


const removeErrorSugg = () => {
    $('.shadow.AIErrorImage').remove()
    $('.workflowActions').removeClass('selected-error')
}

const appendSuggestion = (data, mode = '') => {

    let dataIcon = $(`[nodeid="${data.actionInfo.nodeId}"]`).attr('parenticon') || 'cp-flow'
    let dataColor = $(`[nodeid="${data.actionInfo.nodeId}"]`).attr('parentcolor')
    dataColor = dataColor === 'rgb(255,255,255)' ? '#8f8f8f' : dataColor;
    const iconClass = `${data?.actionInfo?.icon ?? dataIcon} workflowTextClass circle fs-7`;
    let appendData = `<div class="card mt-3 primary-suggestion shadow" id="AISuggestion">
    
        <div class="card-body d-flex gap-2 align-items-center p-0">
            <div class="bg-primary p-2 rounded-start position-relative">
                <span class="primary-suggestion-bg"></span>
                <span class="primary-suggestion-circle">
                    <img src="/../img/genie_bot/logo-small.svg" width="14" />
                </span>
            </div>
            <div class="AISuggestionActions border  border-dark-subtle" parentId='${data?.actionInfo?.parentActionId}' id='${data?.actionInfo?.uniqueId}' details='${JSON.stringify(data)}'">
                <i class="${iconClass} workflowTextClass circle fs-7" style='background: ${data?.actionInfo?.color ?? dataColor}'></i>
                <div class="flex-fill text-truncate mx-2">
                    
                    <span class="actionSpan" title=${data?.actionInfo?.actionName}>${data?.actionInfo?.actionName}</span>
                </div>
                <i type="button" class="addNewAIAction cp-circle-plus text-success" style="margin-right: -12px;" ></i>
            </div>
        <i type="button" class="cp-close fs-8 fw-bold p-2 align-self-start AISuggestionClose"></i>
        </div>
    </div>`

    if (mode === 'change') {
        newActionObj = data
    }

    if (globalSelectedNodeId && globalInsertIndex !== -1) {
        const parentElement = $("#workflowActions").children();
        $('#AISuggestion').fadeOut();
        parentElement.eq(globalInsertIndex + 1).after(appendData);
        $('#AISuggestion').fadeIn('slow');
        parentElement.children().removeClass("selectedWfContainer")
    } else {
        $('#AISuggestion').fadeOut();
        dataDisplay.append(appendData);
        $('#AISuggestion').fadeIn('slow');
    }
}

const lastConfiguredAction = (data, id) => {
    $('#lastConfiguredAISuggestion').empty()
    const iconClass = `${data?.actionInfo?.icon || 'cp-flow'} ${!data?.actionInfo?.color && 'bg-warning'} workflowTextClass circle fs-7`;
    let showSuggestion = `<div class="AISuggestionActions border border-dark-subtle" parentId='${data?.actionInfo?.parentActionId}' id='${data?.actionInfo?.uniqueId}' details='${JSON.stringify(data)}'">
                <i class="${iconClass} workflowTextClass circle fs-7" style='background: ${data?.actionInfo?.color}'></i>
                <div class="flex-fill text-truncate mx-2">
                    
                    <span class="actionSpan" title=${data?.actionInfo?.actionName}>${data?.actionInfo?.actionName}</span>
                </div>
            </div>`
    $('#parentActionCatagoryName').text($('#' + data?.actionInfo?.parentActionId)[0].innerText.trim())
    $(id).append(showSuggestion)
}

$(document).on('click', '.addNewAIAction', function (e) {
    e.preventDefault();
    isEdit = false
    GetActionListByNodeId(newActionObj.actionInfo.nodeId)
    populateFormWithData(newActionObj.actionInfo);
    form.steps('previous');
    $("#next").removeClass('next-disabled')
    $('#CreateModal').modal('show');
    $(".sectionData").empty();
})

$(document).on('click', '.AISuggestionClose', function () {
    $('#AISuggestion').remove()
    $('#offcanvasScrolling').offcanvas('hide')
})

$(document).on('click', '.addSuggestionAIAction', function () {
    if ($('#ActionSuggestionContainerAI').is(':visible')) {
        let detailsId = $(this).parents('.AISuggestionActions')[0].id
        let decodedDetails = atob($('#' + detailsId).attr('details'))
        let details = JSON.parse(decodedDetails)
        let decodedOldData = atob($('#AISuggestion .AISuggestionActions ').attr('details'))
        let oldData = JSON.parse(decodedOldData)
        $('#AISuggestion').remove()
        appendSuggestion(details, 'change')
        $('#AISuggestionsList ' + '#' + detailsId).remove()
        sugestionListAppend(oldData, '#AISuggestionsList', 'prepend')
    } else {
        let detailsId = $(this).parents('.AISuggestionActions')[0].id
        let decodedDetails = atob($('#' + detailsId).attr('details'))
        let details = JSON.parse(decodedDetails)
        $('#workflowActions').children().last().remove()
        newActionObj = details
        GetActionListByNodeId(newActionObj.actionInfo.nodeId)
        populateFormWithData(newActionObj.actionInfo);
        form.steps('previous');
        let selectedNodeId = $("#workflowActions .selectedWfContainer").last().attr('id')
        const insertIndex = $(".workflowActions").index($("#" + selectedNodeId));
        appendData(details, selectedNodeId, insertIndex);
        setTimeout(() => {
            $createModal.modal('show');
        }, 500)

    }
})



