using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberAlert.Events.Update;

public class CyberAlertUpdatedEventHandler : INotificationHandler<CyberAlertUpdatedEvent>
{
    private readonly ILogger<CyberAlertUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberAlertUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<CyberAlertUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(CyberAlertUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.CyberAlert}",
            Entity = Modules.CyberAlert.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"CyberAlert '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"CyberAlert '{updatedEvent.Name}' updated successfully.");
    }
}