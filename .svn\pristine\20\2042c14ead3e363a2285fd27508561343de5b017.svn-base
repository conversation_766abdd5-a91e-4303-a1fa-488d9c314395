﻿using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Update;
using ContinuityPatrol.Application.Features.FiaImpactType.Commands.Create;
using ContinuityPatrol.Application.Features.FiaImpactType.Commands.Update;
using ContinuityPatrol.Application.Features.FiaInterval.Commands.Create;
using ContinuityPatrol.Application.Features.FiaInterval.Commands.Update;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Domain.ViewModels.FiaTemplateModel;
using ContinuityPatrol.Application.Features.FiaTemplate.Commands.Create;
using ContinuityPatrol.Application.Features.FiaTemplate.Commands.Update;
using ContinuityPatrol.Application.Features.FiaTemplate.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.FiaTemplate.Events.PaginatedView;
using ContinuityPatrol.Domain.ViewModels.FiaImpactCategoryModel;
using ContinuityPatrol.Domain.ViewModels.FiaImpactTypeModel;
using ContinuityPatrol.Domain.ViewModels.FiaIntervalModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Exceptions;


namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class FiaTemplatesController : Controller
{
    private readonly IPublisher _publisher;
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;
    private readonly ILogger<FiaTemplatesController> _logger;

    public FiaTemplatesController(IPublisher publisher, ILogger<FiaTemplatesController> logger, IDataProvider dataProvider, IMapper mapper)
    {
        _publisher = publisher;
        _dataProvider = dataProvider;
        _mapper = mapper;
        _logger = logger;
    }


    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in FIATemplates");
        await _publisher.Publish(new FiaTemplatePaginatedevent());
        return View();

    }

    public async Task<IActionResult> GetPaginatedFiaTemplatesList(GetFiaTemplatePaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPaginatedFiaTemplatesList method in FIATemplates");
        try
        {
            var timeList = await _dataProvider.FiaTemplate.GetPaginatedFiaTemplates(query);

            _logger.LogDebug("Successfully retrieved FiaTemplate paginated list on FiaTemplates page");

            return Json(new { Success = true, data = timeList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on fia template page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }

    }

    public async Task<IActionResult> GetTimeIntervalMasterList()
    {
        _logger.LogDebug("Entering GetTimeIntervalMasterList method in FIATemplates");
        try
        {
            var timeIntervalMasterList = await _dataProvider.FiaInterval.GetFiaIntervalList();
            _logger.LogDebug("Successfully retrieved TimeIntervalMaster list on FiaTemplates page");
            return Json(new { Success = true, data = timeIntervalMasterList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on fia template page while retrieving the timeIntervalMaster list.", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<IActionResult> GetImpactMasterList()
    {
        _logger.LogDebug("Entering GetImpactMasterList method in FIATemplates");
        try
        {
            var impactMasterList = await _dataProvider.FiaImpactCategory.GetFiaImpactCategoryList();
            _logger.LogDebug("Successfully retrieved ImpactMaster list on FiaTemplates page");
            return Json(new { Success = true, data = impactMasterList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on fia template page while retrieving the ImpactMaster list.", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<IActionResult> GetImpactTypeMasterList()
    {
        _logger.LogDebug("Entering GetImpactTypeMasterList method in FIATemplates");
        try
        {
            var impactMasterList = await _dataProvider.FiaImpactType.GetFiaImpactTypeList();
            _logger.LogDebug("Successfully retrieved ImpactTypeMaster list on FiaTemplates page");
            return Json(new { Success = true, data = impactMasterList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on fia template page while retrieving the ImpactTypeMaster list.", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<IActionResult> GetList()
    {
        _logger.LogDebug("Entering GetList method in FIATemplates");
        try
        {
            var timeIntervalMasterList = await _dataProvider.FiaInterval.GetFiaIntervalList();

            var impactMasterList = await _dataProvider.FiaImpactCategory.GetFiaImpactCategoryList();

            var impactTypeMasterList = await _dataProvider.FiaImpactType.GetFiaImpactTypeList();

            var allData = new { timeInterval = timeIntervalMasterList, impactType = impactMasterList, impact = impactTypeMasterList };
            _logger.LogDebug("Successfully retrieved TimeIntervalMaster , ImpactMaster , ImpactMasterType lists on FiaTemplates page");
            return Json(new { Success = true, data = allData });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on fia template page while retrieving the list of timeIntervalMaster, impactMaster and impactMasterType.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> FiaTemplateCreateOrUpdate(FiaTemplateViewModel model)
    {
        _logger.LogDebug("Entering FiaTemplateCreateOrUpdate method in FIATemplates");
        var templateId = Request.Form["id"].ToString();
        try
        {
            BaseResponse result;
            if (templateId.IsNullOrWhiteSpace())
            {
                var createFiaTemplateCommand = _mapper.Map<CreateFiaTemplateCommand>(model);
                result = await _dataProvider.FiaTemplate.CreateAsync(createFiaTemplateCommand);
                _logger.LogDebug($"Creating FIATemplates '{createFiaTemplateCommand.Name}'");
            }
            else
            {
                var updateTemplateCommand = _mapper.Map<UpdateFiaTemplateCommand>(model);
                result = await _dataProvider.FiaTemplate.UpdateAsync(updateTemplateCommand);
                _logger.LogDebug($"Updating FIATemplates '{updateTemplateCommand.Name}'");
            }
            return Json(new { Success = true, data = result });
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on fia templates page in fia templates : {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on fia template page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> TimeIntervalMasterCreateOrUpdate(FiaIntervalViewModel model)
    {
        _logger.LogDebug("Entering TimeIntervalMasterCreateOrUpdate method in FIATemplates");
        var templateId = Request.Form["id"].ToString();
        try
        {
            BaseResponse result;
            if (templateId.IsNullOrWhiteSpace())
            {
                var createTimeCommand = _mapper.Map<CreateFiaIntervalCommand>(model);
                result = await _dataProvider.FiaInterval.CreateAsync(createTimeCommand);
                _logger.LogDebug($"Creating TimeIntervalMaster of max '{createTimeCommand.MaxTime}' , min '{createTimeCommand.MinTime}'");
            }
            else
            {
                var updateTimeCommand = _mapper.Map<UpdateFiaIntervalCommand>(model);
                result = await _dataProvider.FiaInterval.UpdateAsync(updateTimeCommand);
                _logger.LogDebug($"Updating TimeIntervalMaster of max '{updateTimeCommand.MaxTime}' , min '{updateTimeCommand.MinTime}'");
            }
            return Json(new { Success = true, data = result });
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on fia templates page in time interval master : {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on fia template page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> ImpactMasterCreateOrUpdate(FiaImpactCategoryViewModel fiaImpactCategory)
    {
        _logger.LogDebug("Entering ImpactMasterCreateOrUpdate method in FIATemplates");
        var templateId = Request.Form["id"].ToString();
        try
        {
            BaseResponse result;
            if (templateId.IsNullOrWhiteSpace())
            {
                var createImpactMaster = _mapper.Map<CreateFiaImpactCategoryCommand>(fiaImpactCategory);
                result = await _dataProvider.FiaImpactCategory.CreateAsync(createImpactMaster);
                _logger.LogDebug($"Creating ImpactMaster '{createImpactMaster.Name}'");
            }
            else
            {
                var createImpactMaster = _mapper.Map<UpdateFiaImpactCategoryCommand>(fiaImpactCategory);
                result = await _dataProvider.FiaImpactCategory.UpdateAsync(createImpactMaster);
                _logger.LogDebug($"Updating ImpactMaster '{createImpactMaster.Name}'");
            }
            return Json(new { Success = true, data = result });
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on fia templates page in impact master : {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on fia template page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> ImpactTypeMasterCreateOrUpdate(FiaImpactTypeViewModel fiaImpactType)
    {
        _logger.LogDebug("Entering ImpactTypeMasterCreateOrUpdate method in FIATemplates");
        var templateId = Request.Form["id"].ToString();
        try
        {
            BaseResponse result;
            if (templateId.IsNullOrWhiteSpace())
            {
                var createImpactMaster = _mapper.Map<CreateFiaImpactTypeCommand>(fiaImpactType);
                result = await _dataProvider.FiaImpactType.CreateAsync(createImpactMaster);
                _logger.LogDebug($"Creating ImpactMasterType '{createImpactMaster.Name}'");
            }
            else
            {
                var createImpactMaster = _mapper.Map<UpdateFiaImpactTypeCommand>(fiaImpactType);
                result = await _dataProvider.FiaImpactType.UpdateAsync(createImpactMaster);
                _logger.LogDebug($"Updating ImpactMasterType '{createImpactMaster.Name}'");
            }
            return Json(new { Success = true, data = result });
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on fia templates page in impact master type : {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on fia template page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }
    }

    public async Task<bool> IsTemplateNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsTemplateNameExist method in FIATemplates");
        try
        {
            var isNameExits = await _dataProvider.FiaTemplate.IsFiaTemplateNameExist(name, id);
            _logger.LogDebug("Returning result for IsTemplateNameExist on FIATemplates");
            return isNameExits;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on Fia Template page while checking if FiaTemplate name exists for : {name}.", ex);
            return false;
        }
    }

    public async Task<bool> ImpactMasterNameExist(string name, string id)
    {
        _logger.LogDebug("Entering ImpactMasterNameExist method in FIATemplates");
        try
        {
            var isNameExits = await _dataProvider.FiaImpactCategory.IsFiaImpactCategoryNameExist(name, id);
            _logger.LogDebug("Returning result for ImpactMasterNameExist on FIATemplates");
            return isNameExits;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on Fia Template page while checking if ImpactMaster name exists for : {name}.", ex);
            return false;
        }
    }
    public async Task<bool> ImpactTypeMasterNameExist(string name, string id)
    {
        _logger.LogDebug("Entering ImpactTypeMasterNameExist method in FIATemplates");
        try
        {
            var nameExits = await _dataProvider.FiaImpactType.IsFiaImpactTypeNameExist(name, id);
            _logger.LogDebug("Returning result for ImpactTypeMasterNameExist on FIATemplates");
            return nameExits;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on Fia Template page while checking if ImpactMasterType name exists for : {name}.", ex);
            return false;
        }
    }
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> TimeIntervalMasterDelete(string id)
    {
        _logger.LogDebug("Entering TimeIntervalMasterDelete method in FIATemplates");
        try
        {
            var delete = await _dataProvider.FiaInterval.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted TimeIntervalMaster record in FIATemplates");
            return Json(new { Success = true, data = delete });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on fia template page while deleting record of time interval master on FIATemplates.", ex);
            return ex.GetJsonException();
        }

    }
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> ImpactTypeMasterDelete(string id)
    {
        _logger.LogDebug("Entering ImpactTypeMasterDelete method in FIATemplates");
        try
        {
            var delete = await _dataProvider.FiaImpactType.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted ImpactTypeMaster record in FIATemplates");
            return Json(new { Success = true, data = delete });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on fia template page while deleting record of impact type master on FIATemplates.", ex);
            return ex.GetJsonException();
        }

    }
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> ImpactMasterDelete(string id)
    {
        _logger.LogDebug("Entering ImpactMasterDelete method in FIATemplates");
        try
        {
            var delete = await _dataProvider.FiaImpactCategory.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted Impact Master record in FIATemplates");
            return Json(new { Success = true, data = delete });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on fia template page while deleting record of impact master on FIATemplates.", ex);
            return ex.GetJsonException();
        }

    }
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> FiaTemplateDelete(string id)
    {
        _logger.LogDebug("Entering FiaTemplateDelete method in FIATemplates");
        try
        {
            var delete = await _dataProvider.FiaTemplate.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted record in FIATemplates");
            return Json(new { Success = true, data = delete });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on fia template page while deleting record on FIATemplates.", ex);
            return ex.GetJsonException();
        }

    }
}

