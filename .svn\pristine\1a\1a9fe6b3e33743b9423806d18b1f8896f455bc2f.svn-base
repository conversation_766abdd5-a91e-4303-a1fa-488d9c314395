using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IBulkImportOperationGroupRepository : IRepository<BulkImportOperationGroup>
{
    //Task<bool> IsNameExist(string name, string id);
    Task<List<BulkImportOperationGroup>> GetBulkImportOperationGroupByBulkImportOperationIds(List<string> ids);
    Task<List<BulkImportOperationGroup>> GetBulkImportOperationGroupByBulkImportOperationId(string id);
}