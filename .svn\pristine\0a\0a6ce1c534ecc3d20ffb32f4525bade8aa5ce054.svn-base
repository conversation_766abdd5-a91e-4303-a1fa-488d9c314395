﻿namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

using ContinuityPatrol.Application.Features.PageWidget.Commands.Create;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Update;
using ContinuityPatrol.Application.Features.PageWidget.Events.PaginatedView;
using ContinuityPatrol.Domain.ViewModels.PageWidgetModel;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

[Area("Admin")]
public class ConfigureWidgetController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IPageWidgetService _iPageWidgetService;
    private readonly IDataSetService _iDataSetService;
    private readonly ILogger<ConfigureWidgetController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    public ConfigureWidgetController(IPublisher publisher,IDataProvider dataProvider, IPageWidgetService pageWidgetService, IDataSetService dataSetService, ILogger<ConfigureWidgetController> logger, IMapper mapper)
    {
        _publisher = publisher;
        _iPageWidgetService = pageWidgetService;
        _iDataSetService = dataSetService;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }
    [AntiXss]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in pageWidget");

        await _publisher.Publish(new PageWidgetPaginatedEvent());
        return View();
    }
    private IActionResult RouteToPostView(BaseResponse result)
    {
        TempData.Set(result.Success
            ? new NotificationMessage(NotificationType.Success, result.Message)
            : new NotificationMessage(NotificationType.Error, result.Message));

        return RedirectToAction("List", "ConfigureWidget", new { area = "Admin" });
    }
    [AntiXss]
    public async Task<IActionResult> GetPageWidgetList()
    {
        _logger.LogDebug("Entering GetPageWidgetList method in pagewidget");
        try
        {
            var pageWidgetList = await _iPageWidgetService.GetPageWidgetList();

            _logger.LogDebug("Successfully retrieved  pageWidget list  on pagewidget page");

            return Json(new { Success = true, Message = pageWidgetList });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occured on get pagewidget list while processing the request.{ex.GetMessage()}");
            return Json(new { Success = false, Message = "Data not found." });
        }
    }

    public async Task<IActionResult> GetAllTableAccesses()
    {
        _logger.LogDebug("Entering GetAllTableAccesses method in pagewidget");
        try
        {
            var tableAccesses = await _iDataSetService.GetDataSetList();

            if(tableAccesses.Count()>0)
                _logger.LogDebug("Successfully retrieved  pageWidget list  on pagewidget page");

            return Json(new { Success = true, Message = tableAccesses });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occured on get all table accesses while processing the request.{ex.GetMessage()}");
            return Json(new { Success = false, Message = "Data not found." });
        }
    }

    public async Task<JsonResult> DatasetDetails(string data)
    {
        _logger.LogDebug("Entering DatasetDetails method in pagewidget");
        try
        {
            var dataSetServiceDetails = await _iDataSetService.RunQuery(data);

            return Json(new { Success = true, Message = dataSetServiceDetails });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occured on get dataset details while processing the request.{ex.GetMessage()}");
            return Json(new { Success = false, Message = "Data not found." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(PageWidgetViewModel pageWidgetView)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in pagewidget");
        try
        {
            var pagewidgetId = Request.Form["id"];
            BaseResponse result;


            if (string.IsNullOrEmpty(pagewidgetId))
            {
                var pageWidgetModel = _mapper.Map<CreatePageWidgetCommand>(pageWidgetView);

                _logger.LogDebug($"Creating pagewidget '{pageWidgetModel.Name}'");

                result = await _iPageWidgetService.CreateAsync(pageWidgetModel);
            }
            else
            {
                var pageWidgetModel = _mapper.Map<UpdatePageWidgetCommand>(pageWidgetView);

                _logger.LogDebug($"Updating pagewidget '{pageWidgetModel.Name}'");

                result = await _iPageWidgetService.UpdateAsync(pageWidgetModel);
            }

            return Json(result);
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on pagewidget page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on pagewidget page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in pagewidget");
        
        try
        {
            var deleteWidgetPage = await _iPageWidgetService.DeleteAsync(id);

            _logger.LogDebug("Successfully deleted record in pagewidget");

            return RouteToPostView(deleteWidgetPage);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on pagewidget.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    public JsonResult DecryptDetails(string monitorData)
    {
        try
        {
            if (monitorData.IsNullOrWhiteSpace())
        {
            return Json(new { decrypt = "" });
        }

        var decrypt = SecurityHelper.Decrypt($"{monitorData}");

        return Json(new { decrypt = decrypt });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();

        }
    }
    public async Task<JsonResult> GetMonitorServiceStatusByIdAndType(string monitorId, string type)
    {
        _logger.LogDebug("Entering GetMonitorServiceStatusByIdAndType method in pagewidget");
        try
        {
            var monitoringData = await _dataProvider.DashboardView.GetMonitorServiceStatusByIdAndType(monitorId, type);

            _logger.LogDebug("Successfully retrieved  MonitorServiceStatus By Id And Type on pagewidget page");

            return Json(new { Success = true, data = monitoringData });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on pagewidget page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();

        }
    }
}

