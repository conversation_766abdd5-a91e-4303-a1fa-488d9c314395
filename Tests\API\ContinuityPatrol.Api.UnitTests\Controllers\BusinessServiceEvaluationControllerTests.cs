using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Commands.Delete;
using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceEvaluationModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class BusinessServiceEvaluationControllerTests : IClassFixture<BusinessServiceEvaluationFixture>
{
    private readonly BusinessServiceEvaluationFixture _businessServiceEvaluationFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly BusinessServiceEvaluationController _controller;

    public BusinessServiceEvaluationControllerTests(BusinessServiceEvaluationFixture businessServiceEvaluationFixture)
    {
        _businessServiceEvaluationFixture = businessServiceEvaluationFixture;

        var testBuilder = new ControllerTestBuilder<BusinessServiceEvaluationController>();
        _controller = testBuilder.CreateController(
            _ => new BusinessServiceEvaluationController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetBusinessServiceEvaluations_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessServiceEvaluationListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_businessServiceEvaluationFixture.BusinessServiceEvaluationListVm);

        // Act
        var result = await _controller.GetBusinessServiceEvaluations();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var evaluations = Assert.IsAssignableFrom<List<BusinessServiceEvaluationListVm>>(okResult.Value);
        Assert.Equal(3, evaluations.Count);
    }

    [Fact]
    public async Task GetBusinessServiceEvaluationById_ReturnsExpectedDetail()
    {
        // Arrange
        var evaluationId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessServiceEvaluationDetailQuery>(q => q.Id == evaluationId), default))
            .ReturnsAsync(_businessServiceEvaluationFixture.BusinessServiceEvaluationDetailVm);

        // Act
        var result = await _controller.GetBusinessServiceEvaluationById(evaluationId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var evaluation = Assert.IsType<BusinessServiceEvaluationDetailVm>(okResult.Value);
        Assert.NotNull(evaluation);
    }

    [Fact]
    public async Task CreateBusinessServiceEvaluation_Returns201Created()
    {
        // Arrange
        var command = _businessServiceEvaluationFixture.CreateBusinessServiceEvaluationCommand;
        var expectedMessage = $"BusinessServiceEvaluation '{command.BusinessServiceName}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBusinessServiceEvaluationResponse
            {
                Message = expectedMessage,
                BusinessServiceEvaluationId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBusinessServiceEvaluation(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBusinessServiceEvaluationResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBusinessServiceEvaluation_ReturnsOk()
    {
        // Arrange
        var command = _businessServiceEvaluationFixture.UpdateBusinessServiceEvaluationCommand;
        var expectedMessage = $"BusinessServiceEvaluation '{command.BusinessServiceName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateBusinessServiceEvaluationResponse
            {
                Message = expectedMessage,
                BusinessServiceEvaluationId = command.Id
            });

        // Act
        var result = await _controller.UpdateBusinessServiceEvaluation(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBusinessServiceEvaluationResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBusinessServiceEvaluation_ReturnsOk()
    {
        // Arrange
        var evaluationId = Guid.NewGuid().ToString();
        var expectedMessage = "BusinessServiceEvaluation has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBusinessServiceEvaluationCommand>(c => c.Id == evaluationId), default))
            .ReturnsAsync(new DeleteBusinessServiceEvaluationResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteBusinessServiceEvaluation(evaluationId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBusinessServiceEvaluationResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task GetBusinessServiceEvaluationById_HandlesInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetBusinessServiceEvaluationById("invalid-guid"));
    }

    [Fact]
    public async Task CreateBusinessServiceEvaluation_ValidatesBusinessServiceName()
    {
        // Arrange
        var command = new CreateBusinessServiceEvaluationCommand
        {
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "", // Empty business service name should cause validation error
            Description = "Empty business service name should cause validation error",
            Grade = "A",
            GradeValue = "200"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("BusinessServiceName is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBusinessServiceEvaluation(command));
    }

    [Fact]
    public async Task UpdateBusinessServiceEvaluation_ValidatesEvaluationExists()
    {
        // Arrange
        var command = new UpdateBusinessServiceEvaluationCommand
        {
            Id = Guid.NewGuid().ToString(),
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Updated Service",
            Description = "Empty business service name should cause validation error",
            Grade = "A",
            GradeValue = "200"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("BusinessServiceEvaluation not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateBusinessServiceEvaluation(command));
    }

    [Fact]
    public async Task CreateBusinessServiceEvaluation_HandlesComplexEvaluationData()
    {
        // Arrange
        var command = new CreateBusinessServiceEvaluationCommand
        {
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Enterprise Multi-Tier Application Platform",
           
            Description = "Performance (25%), Availability (20%), Security (20%), Scalability (15%), Maintainability (10%), User Experience (10%)",
          Grade = "B",
          GradeValue = "100"
        };

        var expectedMessage = $"BusinessServiceEvaluation '{command.BusinessServiceName}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBusinessServiceEvaluationResponse
            {
                Message = expectedMessage,
                BusinessServiceEvaluationId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBusinessServiceEvaluation(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBusinessServiceEvaluationResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBusinessServiceEvaluation_HandlesEvaluationScoreUpdate()
    {
        // Arrange
        var command = new UpdateBusinessServiceEvaluationCommand
        {
            Id = Guid.NewGuid().ToString(),
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Updated Enterprise Platform",
            Description = "Significant improvements implemented based on previous evaluation feedback",
            Grade = "C",
            GradeValue = "Final Approval"
        };

        var expectedMessage = $"BusinessServiceEvaluation '{command.BusinessServiceName}' has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateBusinessServiceEvaluationResponse
            {
                Message = expectedMessage,
                BusinessServiceEvaluationId = command.Id
            });

        // Act
        var result = await _controller.UpdateBusinessServiceEvaluation(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBusinessServiceEvaluationResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.BusinessServiceEvaluationId);
    }

    [Fact]
    public async Task CreateBusinessServiceEvaluation_ValidatesEvaluationScore()
    {
        // Arrange
        var command = new CreateBusinessServiceEvaluationCommand
        {
            BusinessServiceName = "Test Service",
           
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("EvaluationScore must be between 0 and 100"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBusinessServiceEvaluation(command));
    }
}
