﻿using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IApprovalMatrixService
{
    Task<BaseResponse> CreateAsync(CreateApprovalMatrixCommand approvalMatrixCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<BaseResponse> UpdateAsync(UpdateApprovalMatrixCommand approvalMatrixCommand);
    Task<List<ApprovalMatrixListVm>> GetApprovalMatrixList();
    Task<PaginatedResult<ApprovalMatrixListVm>> GetPaginatedApprovalMatrices(GetApprovalMatrixPaginatedListQuery query);
    Task<bool> IsApprovalMatrixNameExist(string name, string id);
}