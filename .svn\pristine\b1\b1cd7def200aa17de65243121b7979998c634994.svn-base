using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.ComponentSaveAll.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class ComponentSaveAllControllerTests : IClassFixture<ComponentSaveAllFixture>
{
    private readonly ComponentSaveAllFixture _componentSaveAllFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly ComponentSaveAllController _controller;

    public ComponentSaveAllControllerTests(ComponentSaveAllFixture componentSaveAllFixture)
    {
        _componentSaveAllFixture = componentSaveAllFixture;

        var testBuilder = new ControllerTestBuilder<ComponentSaveAllController>();
        _controller = testBuilder.CreateController(
            _ => new ComponentSaveAllController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetComponentById_ReturnsExpectedDetail()
    {
        // Arrange
        var componentId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<ComponentSaveAllDetailQuery>(q => q.Id == componentId), default))
            .ReturnsAsync(_componentSaveAllFixture.ComponentSaveAllDetailVm);

        // Act
        var result = await _controller.GetComponentById(componentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var component = Assert.IsType<ComponentSaveAllDetailVm>(okResult.Value);
        Assert.NotNull(component);
        Assert.Equal(_componentSaveAllFixture.ComponentSaveAllDetailVm.EntityName, component.EntityName);
    }

    [Fact]
    public async Task GetComponentById_HandlesInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetComponentById("invalid-guid"));
    }

    [Fact]
    public async Task GetComponentById_HandlesNullOrEmptyId()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetComponentById(""));

        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _controller.GetComponentById(null));
    }

    [Fact]
    public async Task GetComponentById_HandlesComponentNotFound()
    {
        // Arrange
        var componentId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<ComponentSaveAllDetailQuery>(q => q.Id == componentId), default))
            .ReturnsAsync((ComponentSaveAllDetailVm)null);

        // Act
        var result = await _controller.GetComponentById(componentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Null(okResult.Value);
    }

    [Fact]
    public async Task GetComponentById_HandlesComplexComponentData()
    {
        // Arrange
        var componentId = Guid.NewGuid().ToString();
        var complexComponent = new ComponentSaveAllDetailVm
        {
            Id = componentId,
            EntityId = Guid.NewGuid().ToString(),
            EntityName = "Enterprise High-Availability Multi-Tier Application Platform",
            Type = "Application-Database-Cluster",
            Properties = "{\"primaryServer\":{\"name\":\"APP-CLUSTER-01\",\"ip\":\"**********\",\"cpu\":\"32 cores\",\"memory\":\"128GB\"},\"secondaryServers\":[{\"name\":\"APP-CLUSTER-02\",\"ip\":\"**********\"},{\"name\":\"APP-CLUSTER-03\",\"ip\":\"**********\"}],\"primaryDB\":{\"name\":\"EnterpriseAppDB\",\"version\":\"SQL Server 2022 Enterprise\",\"size\":\"5TB\"},\"replicaDBs\":[{\"name\":\"EnterpriseAppDB_Replica1\",\"location\":\"DR Site\"},{\"name\":\"EnterpriseAppDB_Replica2\",\"location\":\"Backup Site\"}],\"replication\":{\"type\":\"Multi-Master\",\"topology\":\"Active-Active\",\"synchronization\":\"Real-time\"},\"infrastructure\":{\"name\":\"Enterprise-Critical-Infrastructure\",\"tier\":\"Tier-1\",\"sla\":\"99.99%\"},\"loadBalancer\":\"F5 BigIP\",\"monitoring\":\"SCOM + Nagios\",\"backup\":\"Veeam + SQL Native\",\"security\":\"TLS 1.3 + Certificate-based Auth\",\"compliance\":\"SOX + HIPAA + PCI-DSS\"}"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<ComponentSaveAllDetailQuery>(q => q.Id == componentId), default))
            .ReturnsAsync(complexComponent);

        // Act
        var result = await _controller.GetComponentById(componentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var component = Assert.IsType<ComponentSaveAllDetailVm>(okResult.Value);
        Assert.NotNull(component);
        Assert.Equal("Enterprise High-Availability Multi-Tier Application Platform", component.EntityName);
        Assert.Equal("Application-Database-Cluster", component.Type);
        Assert.Contains("Multi-Master", component.Properties);
        Assert.Contains("Enterprise-Critical-Infrastructure", component.Properties);
    }

    [Fact]
    public async Task GetComponentById_HandlesServerComponentType()
    {
        // Arrange
        var componentId = Guid.NewGuid().ToString();
        var serverComponent = new ComponentSaveAllDetailVm
        {
            Id = componentId,
            EntityId = Guid.NewGuid().ToString(),
            EntityName = "Enterprise Web Server Farm",
            Type = "Server",
            Properties = "{\"serverFarm\":[{\"name\":\"WEB-01\",\"role\":\"Primary\",\"ip\":\"************\"},{\"name\":\"WEB-02\",\"role\":\"Secondary\",\"ip\":\"************\"},{\"name\":\"WEB-03\",\"role\":\"Load Balancer\",\"ip\":\"************\"}],\"replication\":{\"type\":\"File Replication\",\"schedule\":\"Real-time\",\"scope\":\"Web Content + Configuration\"},\"infrastructure\":{\"name\":\"Web-Server-Infrastructure\",\"location\":\"DMZ\",\"environment\":\"Production\"},\"webServer\":\"IIS 10\",\"framework\":\".NET 8\",\"ssl\":\"Wildcard Certificate\",\"monitoring\":\"Application Insights\"}"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<ComponentSaveAllDetailQuery>(q => q.Id == componentId), default))
            .ReturnsAsync(serverComponent);

        // Act
        var result = await _controller.GetComponentById(componentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var component = Assert.IsType<ComponentSaveAllDetailVm>(okResult.Value);
        Assert.NotNull(component);
        Assert.Equal("Enterprise Web Server Farm", component.EntityName);
        Assert.Equal("Server", component.Type);
        Assert.Contains("File Replication", component.Properties);
    }

    [Fact]
    public async Task GetComponentById_HandlesDatabaseComponentType()
    {
        // Arrange
        var componentId = Guid.NewGuid().ToString();
        var databaseComponent = new ComponentSaveAllDetailVm
        {
            Id = componentId,
            EntityId = Guid.NewGuid().ToString(),
            EntityName = "Enterprise Data Warehouse Cluster",
            Type = "Database",
            Properties = "{\"dbServers\":[{\"name\":\"DW-DB-01\",\"role\":\"Primary\",\"specs\":\"64GB RAM, 16 cores\"},{\"name\":\"DW-DB-02\",\"role\":\"Secondary\",\"specs\":\"64GB RAM, 16 cores\"}],\"databases\":[{\"name\":\"DataWarehouse\",\"size\":\"10TB\",\"type\":\"OLAP\"},{\"name\":\"DataMart_Sales\",\"size\":\"2TB\",\"type\":\"OLTP\"},{\"name\":\"DataMart_Finance\",\"size\":\"1.5TB\",\"type\":\"OLTP\"}],\"replication\":{\"type\":\"Always On Availability Groups\",\"mode\":\"Synchronous\",\"readableSecondary\":true,\"backupPreference\":\"Secondary\"},\"infrastructure\":{\"name\":\"Data-Warehouse-Infrastructure\",\"tier\":\"Tier-1\",\"criticality\":\"High\"},\"dbEngine\":\"SQL Server 2022 Enterprise\",\"indexing\":\"Columnstore + B-Tree\",\"compression\":\"Page + Row\",\"encryption\":\"TDE + Always Encrypted\"}"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<ComponentSaveAllDetailQuery>(q => q.Id == componentId), default))
            .ReturnsAsync(databaseComponent);

        // Act
        var result = await _controller.GetComponentById(componentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var component = Assert.IsType<ComponentSaveAllDetailVm>(okResult.Value);
        Assert.NotNull(component);
        Assert.Equal("Enterprise Data Warehouse Cluster", component.EntityName);
        Assert.Equal("Database", component.Type);
        Assert.Contains("DataWarehouse", component.Properties);
        Assert.Contains("Always On Availability Groups", component.Properties);
    }

    [Fact]
    public void ClearDataCache_ThrowsNotImplementedException()
    {
        // Act & Assert
        Assert.Throws<NotImplementedException>(() => _controller.ClearDataCache());
    }

    [Fact]
    public async Task GetComponentById_ValidatesGuidFormat()
    {
        // Arrange
        var invalidGuids = new[] { "not-a-guid", "12345", "abc-def-ghi", "00000000-0000-0000-0000-000000000000" };

        // Act & Assert
        foreach (var invalidGuid in invalidGuids.Take(3)) // Test first 3 to avoid too many test cases
        {
            await Assert.ThrowsAsync<InvalidArgumentException>(() =>
                _controller.GetComponentById(invalidGuid));
        }
    }

    [Fact]
    public async Task GetComponentById_HandlesReplicationComponentType()
    {
        // Arrange
        var componentId = Guid.NewGuid().ToString();
        var replicationComponent = new ComponentSaveAllDetailVm
        {
            Id = componentId,
            EntityId = Guid.NewGuid().ToString(),
            EntityName = "Enterprise Cross-Site Replication Service",
            Type = "Replication",
            Properties = "{\"replicationServers\":[{\"name\":\"REP-SRV-01\",\"site\":\"Primary\",\"role\":\"Publisher\"},{\"name\":\"REP-SRV-02\",\"site\":\"DR\",\"role\":\"Subscriber\"}],\"replicatedDatabases\":[{\"name\":\"CriticalAppDB\",\"replicationMode\":\"Transactional\"},{\"name\":\"UserDataDB\",\"replicationMode\":\"Merge\"}],\"topology\":\"Publisher-Subscriber\",\"frequency\":\"Continuous\",\"compression\":true,\"encryption\":true,\"conflictResolution\":\"Publisher Wins\",\"infrastructure\":{\"name\":\"Cross-Site-Replication-Infrastructure\",\"sites\":[\"Primary DC\",\"DR DC\",\"Backup DC\"]},\"bandwidth\":\"1Gbps\",\"latency\":\"<50ms\",\"monitoring\":\"SQL Server Replication Monitor\",\"alerting\":\"SCOM Integration\"}"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<ComponentSaveAllDetailQuery>(q => q.Id == componentId), default))
            .ReturnsAsync(replicationComponent);

        // Act
        var result = await _controller.GetComponentById(componentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var component = Assert.IsType<ComponentSaveAllDetailVm>(okResult.Value);
        Assert.NotNull(component);
        Assert.Equal("Enterprise Cross-Site Replication Service", component.EntityName);
        Assert.Equal("Replication", component.Type);
        Assert.Contains("Publisher-Subscriber", component.Properties);
        Assert.Contains("Cross-Site-Replication-Infrastructure", component.Properties);
    }
}
