﻿using ContinuityPatrol.Application.Features.ServerType.Events.Update;

namespace ContinuityPatrol.Application.Features.ServerType.Commands.Update;

public class UpdateServerTypeCommandHandler : IRequestHandler<UpdateServerTypeCommand, UpdateServerTypeResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IServerTypeRepository _serverTypeRepository;

    public UpdateServerTypeCommandHandler(IMapper mapper, IServerTypeRepository serverTypeRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _serverTypeRepository = serverTypeRepository;
        _publisher = publisher;
    }

    public async Task<UpdateServerTypeResponse> Handle(UpdateServerTypeCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _serverTypeRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.ServerType), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateServerTypeCommand), typeof(Domain.Entities.ServerType));

        await _serverTypeRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateServerTypeResponse
        {
            Message = Message.Update("Server Role", eventToUpdate.Name),

            ServerTypeId = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new ServerTypeUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}