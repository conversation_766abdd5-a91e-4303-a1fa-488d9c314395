﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.SvcMsSqlMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.SvcMsSqlMonitorStatus.Queries.GetPaginatedList;

public class SvcMsSqlMonitorStatusPaginatedListQueryHandler : IRequestHandler<SvcMsSqlMonitorStatusPaginatedListQuery,
    PaginatedResult<SvcMsSqlMonitorStatusListVm>>
{
    private readonly IMapper _mapper;
    private readonly ISvcMsSqlMonitorStatusRepository _svcMsSqlMonitorStatusRepository;

    public SvcMsSqlMonitorStatusPaginatedListQueryHandler(IMapper mapper,
        ISvcMsSqlMonitorStatusRepository svcMsSqlMonitorStatusRepository)
    {
        _mapper = mapper;
        _svcMsSqlMonitorStatusRepository = svcMsSqlMonitorStatusRepository;
    }

    public async Task<PaginatedResult<SvcMsSqlMonitorStatusListVm>> Handle(
        SvcMsSqlMonitorStatusPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _svcMsSqlMonitorStatusRepository.PaginatedListAllAsync();

        var productFilterSpec = new SvcMsSqlMonitorStatusFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<SvcMsSqlMonitorStatusListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}