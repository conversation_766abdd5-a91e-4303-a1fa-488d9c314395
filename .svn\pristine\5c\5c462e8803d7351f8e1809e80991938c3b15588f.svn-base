﻿//using ContinuityPatrol.Orchestration.Core.Contracts;
//using ContinuityPatrol.Orchestration.Core.Features.WorkflowHistory.Commands.Update;
//using ContinuityPatrol.Orchestration.Core.UnitTests.Attributes;
//using ContinuityPatrol.Orchestration.Core.UnitTests.Mocks;

//namespace ContinuityPatrol.Orchestration.Core.UnitTests.Domains.WorkflowHistory.Validators;

//public class UpdateWorkflowHistoryValidatorTests
//{
//    private readonly Mock<IWorkflowHistoryRepository> _mockWorkflowHistoryRepository;

//    public UpdateWorkflowHistoryValidatorTests()
//    {
//        var workflowHistory = new Fixture().Create<List<Entities.WorkflowHistory>>();

//        _mockWorkflowHistoryRepository = WorkflowHistoryRepositoryMocks.CreateWorkflowHistoryRepository(workflowHistory);
//    }

//    //WorkflowName

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_WorkflowName_InWorkflowHistory_WithEmpty(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);

//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_WorkflowName_InWorkflowHistory_IsNull(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = null;

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameNotEmptyRequired, validateResult.Errors[1].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_WorkflowName_InWorkflowHistory_MinimumRange(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "CA";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameRangeRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_WorkflowName_InWorkflowHistory_MaxiMumRange(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIHGFEDCBA";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameRangeRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_Valid_WorkflowName_InWorkflowHistory(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "   PTS  ";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_Valid_WorkflowName_InWorkflowHistory_With_Double_Space_InFront(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "  PTS";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_Valid_WorkflowName_InWorkflowHistory_With_TripleSpace_InBetween(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "PTS   India";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_Valid_WorkflowName_InWorkflowHistory_With_SpecialCharacters(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "PTS$#%^India";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_Valid_WorkflowName_InWorkflowHistory_With_SpecialCharacters_Only(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "@@#$^&*(*&)><";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_Valid_WorkflowName_InWorkflowHistory_With_SpecialCharacters_InFront(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "@@PTS";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_Valid_WorkflowName_InWorkflowHistory_With_Underscore_InFront(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "_PTS";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_Valid_WorkflowName_InWorkflowHistory_With_Underscore_InFront_andBack(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "_PTS_";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_Valid_WorkflowName_InWorkflowHistory_With_Numbers_InFront(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "123PTS";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_Valid_WorkflowName_InWorkflowHistory_With_UnderscoreAndNumbers_InFront_AndUnderscore_InBack(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "_123PTS_";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }

//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_Valid_WorkflowName_InWorkflowHistory_With_Underscore_InFront_AndNumbers_InBack(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "_PTS123";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }
    
//    [Theory]
//    [AutoWorkflowHistoryData]
//    public async Task Verify_Update_Valid_WorkflowName_InWorkflowHistory_With_Numbers_Only(UpdateWorkflowHistoryCommand updateWorkflowHistoryCommand)
//    {
//        var validator = new UpdateWorkflowHistoryCommandValidator(_mockWorkflowHistoryRepository.Object);

//        updateWorkflowHistoryCommand.WorkflowName = "0123456789";

//        var validateResult = await validator.ValidateAsync(updateWorkflowHistoryCommand, CancellationToken.None);
//        Assert.Equal(ValidatorConstants.WorkflowHistory.WorkflowHistoryNameValidRequired, validateResult.Errors[0].ErrorMessage);
//    }
//}