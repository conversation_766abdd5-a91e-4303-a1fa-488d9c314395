using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class FiaCostRepository : BaseRepository<FiaCost>, IFiaCostRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public FiaCostRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<FiaCost> GetByBusinessFunctionId(string businessFunctionId)
    {
        return await _dbContext.FiaCosts.Active()
            .FirstOrDefaultAsync(x => x.BusinessFunctionId.Equals(businessFunctionId));
    }

    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.BusinessFunctionName.Equals(name))
            : Entities.Where(e => e.BusinessFunctionName.Equals(name)).ToList().Unique(id));
    }
}
