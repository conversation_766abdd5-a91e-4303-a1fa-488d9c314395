﻿
@{
    ViewData["Title"] = "CMDBImportSummary";
}
@using DevExpress.AspNetCore
@using ContinuityPatrol.Web.Areas.Report.ReportTemplate

<style>
    .form-group {
        margin-left: 550px;
        margin-top: 60px;
    }
</style>
@section Styles
    {
    <link href="~/css/viewer.part.bundle.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/thirdparty.bundle.css" rel="stylesheet" asp-append-version="true" />
    }
@section HeaderScripts
    {
    <script src="~/js/common/thirdparty.bundle.js" asp-append-version="true"></script>
    <script src="~/js/common/viewer.part.bundle.js" asp-append-version="true"></script>
   }

<div class="card card-custom gutter-b">
    <div class="card-body p-0">
        <div id="reportContainer">
            @Html.DevExpress().WebDocumentViewer("CMDBImportSummaryDocumentViewer").Height("1150px").Bind(new CMDBImportReport())

        </div>
    </div>
</div>

@*<div class="row mt-3">
    <div class="col">
        <img src="/img/logo/cplogo.svg" height="35" />
    </div>
    <div class="col text-end">
        <img src="/img/logo/pts_logo.png" height="35" />
    </div>
    <div class="col-12">
        <div class="bg-secondary rounded-0 text-light mt-1">
            <h6 class="Report-Header text-center">
                CMDB Import Report

            </h6>
        </div>
    </div>
</div>
<div class="card">
    <div>

        <div class="mt-3">
            <div class="rounded card bg-light">
                <div class="card-header text-primary fw-semibold border-bottom">
                        CMDB Import Summary
                </div>
                <div class="p-0 card-body">
                    <div class="rounded-0 py-3">
                        <div class="row">
                            <div class="col border-end">
                                <div class="d-flex justify-content-between px-3 py-2 border-bottom list-group-item">
                                    <div class="me-auto d-flex align-items-center">
                                        <span class="ms-1 align-middle">
                                            Report Generation
                                            Time
                                        </span>
                                    </div>
                                    01-02-2022
                                </div>
                                <div class="d-flex justify-content-between px-3 py-2  border-bottom list-group-item">
                                    <div class="me-auto d-flex align-items-center">
                                        <span class="ms-1 align-middle">
                                            CMDB File Name
                                        </span>
                                    </div>
                                    Application_Windows2012_WMI.xls
                                </div>
                                <div class="d-flex justify-content-between px-3 py-2  border-bottom  list-group-item">
                                    <div class="me-auto d-flex align-items-center">
                                        <span class="ms-1 align-middle">
                                            Generation Timestamp
                                        </span>
                                    </div>
                                    03-09-2022 12:25:20 PM
                                </div>
                            </div>
                            <div class="col">
                                <div class="d-flex justify-content-between px-3 py-2 border-bottom list-group-item">
                                    <div class="me-auto d-flex align-items-center">

                                        <span class="ms-1 align-middle">
                                            Total CMDB Records
                                        </span>
                                    </div>
                                    1
                                </div>
                                <div class="d-flex justify-content-between px-3 py-2 border-bottom list-group-item">
                                    <div class="me-auto d-flex align-items-center">

                                        <span class="ms-1 align-middle">
                                            Successfully Import
                                            Records

                                        </span>
                                    </div>
                                    1
                                </div>
                                <div class="d-flex justify-content-between px-3 py-2 border-bottom list-group-item">
                                    <div class="me-auto d-flex align-items-center">

                                        <span class="ms-1 align-middle">
                                            Failed Records

                                        </span>
                                    </div>
                                    0
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div class="mt-3 prebuildreportcustom">
            <div>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="bg-light">
                            <tr class="text-center">
                                <th>Sr.No</th>
                                <th>Serve</th>
                                <th>IP Address</th>
                                <th>Failed Reason</th>
                            </tr>
                        </thead>
                        <tbody id="tableDataone">
                            <tr class="text-center">
                                <td>1</td>
                                <td>Always_ON_Infra</td>
                                <td>NA</td>
                                <td>NA</td>
                            </tr>
                            <tr class="text-center">
                                <td>2</td>
                                <td>Always_ON_Infra</td>
                                <td>NA</td>
                                <td>NA</td>
                            </tr>
                            <tr class="text-center">
                                <td>3</td>
                                <td>Always_ON_Infra</td>
                                <td>NA</td>
                                <td>NA</td>
                            </tr>
                             <tr class="text-center">
                                <td>4</td>
                                <td>Always_ON_Infra</td>
                                <td>NA</td>
                                <td>NA</td>
                            </tr>
                            <tr class="text-center">
                                <td>5</td>
                                <td>Always_ON_Infra</td>
                                <td>NA</td>
                                <td>NA</td>
                            </tr>
                             <tr class="text-center">
                                <td>6</td>
                                <td>Always_ON_Infra</td>
                                <td>NA</td>
                                <td>NA</td>
                            </tr>
                            
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


<script src="~/js/report-charts/cmdb_import_report.js"></script>*@