﻿using AutoFixture;
using ContinuityPatrol.Application.Features.DataSet.Commands.Create;
using ContinuityPatrol.Application.Features.DataSet.Commands.Update;
using ContinuityPatrol.Application.Features.DataSet.Event.PaginatedView;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetRunQuery;
using ContinuityPatrol.Application.Features.DataSetColumns.Queries.GetColumnNames;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetSchemaNameList;
using ContinuityPatrol.Domain.ViewModels.DataSetModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using Microsoft.Extensions.Configuration;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class DataSetControllerShould
    {
        private readonly Mock<IPublisher> _publisherMock = new ();
        private readonly Mock<IMapper> _mapperMock = new();
        private readonly Mock<IDataProvider> _dataProviderMock = new();
        private readonly Mock<ILogger<DataSetController>> _loggerMock = new();
        private readonly Mock<IConfiguration> _configMock = new();
        private readonly Fixture _fixture = new();
        private  DataSetController _controller;

        public DataSetControllerShould()
        {


            Initialize();

        }
        internal void Initialize()
        {
            _controller = new DataSetController(
                _publisherMock.Object,
                _loggerMock.Object,
                _dataProviderMock.Object,
                _mapperMock.Object,
                _configMock.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_Returns_View_With_Data()
        {
            // Arrange
            var schemaNames = new List<SchemaNameListVm>
            {
                new SchemaNameListVm { Id = "1", SchemaName = "TestSchema" }
            };

            _dataProviderMock.Setup(dp => dp.TableAccess.GetSchemaNames())
                .ReturnsAsync(schemaNames);

            // Act
            var result = await _controller.List() as ViewResult;
            var model = result?.Model as DataSetModel;

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(model);
            Assert.NotNull(model.SchemaList);
            Assert.Single(model.SchemaList);
        }

        [Fact]
        public async Task List_ThrowsException_PropagatesException()
        {
            // Arrange
            _publisherMock.Setup(p => p.Publish(It.IsAny<DataSetPaginatedEvent>(), It.IsAny<CancellationToken>()))
                         .ThrowsAsync(new Exception("Test exception"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _controller.List());
        }
        
        [Fact]
        public async Task CreateOrUpdate_Creates_DataSet()
        {
            // Arrange
            var dataSetModel = _fixture.Create<DataSetModel>();
            dataSetModel.Id = ""; // Ensure empty ID for create path
            var command = new CreateDataSetCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mapperMock.Setup(m => m.Map<CreateDataSetCommand>(dataSetModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.DataSet.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(dataSetModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task CreateOrUpdate_Updates_DataSet()
        {
            // Arrange
            var dataSetModel = _fixture.Create<DataSetModel>();
            dataSetModel.Id = "22"; // Ensure non-empty ID for update path
            var command = new UpdateDataSetCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mapperMock.Setup(m => m.Map<UpdateDataSetCommand>(dataSetModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.DataSet.UpdateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(dataSetModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
        }
        
        [Fact]
        public async Task CreateOrUpdate_Handles_Exception()
        {
            // Arrange
            var dataSetModel = _fixture.Create<DataSetModel>();
            dataSetModel.Id = ""; // Ensure empty ID for create path
            var command = new CreateDataSetCommand();

            _mapperMock.Setup(m => m.Map<CreateDataSetCommand>(dataSetModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.DataSet.CreateAsync(command))
                .ThrowsAsync(new Exception("Error"));

            // Act
            var result = await _controller.CreateOrUpdate(dataSetModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task Delete_Calls_Delete_Method()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

            _dataProviderMock.Setup(dp => dp.DataSet.DeleteAsync(id))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task Delete_Handles_Exception()
        {
            // Arrange
            var id = "1";
            _dataProviderMock.Setup(dp => dp.DataSet.DeleteAsync(id))
                .ThrowsAsync(new Exception("Delete error"));

            // Act
            var result = await _controller.Delete(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task RunQuery_Returns_Json_With_Data()
        {
            // Arrange
            var runQuery = "SELECT * FROM Table";
            var queryResult = new DataSetRunQueryVm();

            _dataProviderMock.Setup(dp => dp.DataSet.RunQuery(runQuery))
                .ReturnsAsync(queryResult);

            // Act
            var result = await _controller.RunQuery(runQuery) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task RunQuery_WithEmptyQuery_ReturnsEmptyJson()
        {
            // Act
            var result = await _controller.RunQuery("") as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("", result.Value);
        }

        [Fact]
        public async Task RunQuery_WithNullQuery_ReturnsEmptyJson()
        {
            // Act
            var result = await _controller.RunQuery(null) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("", result.Value);
        }

        [Fact]
        public async Task RunQuery_Handles_Exception()
        {
            // Arrange
            var runQuery = "SELECT * FROM Table";
            _dataProviderMock.Setup(dp => dp.DataSet.RunQuery(runQuery))
                .ThrowsAsync(new Exception("Error"));

            // Act
            var result = await _controller.RunQuery(runQuery) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetPagination_Returns_Json_With_Data()
        {
            // Arrange
            var paginatedQuery = new GetDataSetPaginatedListQuery();
            var paginatedList = new PaginatedResult<DataSetListVm>();

            _dataProviderMock.Setup(dp => dp.DataSet.GetDataSetPaginatedList(paginatedQuery))
                .ReturnsAsync(paginatedList);

            // Act
            var result = await _controller.GetPagination(paginatedQuery) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(paginatedList, result.Value);
        }

        [Fact]
        public async Task GetPagination_Handles_Exception()
        {
            // Arrange
            var paginatedQuery = new GetDataSetPaginatedListQuery();
            _dataProviderMock.Setup(dp => dp.DataSet.GetDataSetPaginatedList(paginatedQuery))
                .ThrowsAsync(new Exception("Pagination error"));

            // Act
            var result = await _controller.GetPagination(paginatedQuery) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task DataSetNameExist_Returns_Json_With_Bool()
        {
            // Arrange
            var dataSetName = "DataSetName";
            var id = "1";
            var exists = true;

            _dataProviderMock.Setup(dp => dp.DataSet.IsDataSetNameExist(dataSetName, id))
                .ReturnsAsync(exists);

            // Act
            var result = await _controller.DataSetNameExist(dataSetName, id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task DataSetNameExist_Handles_Exception()
        {
            // Arrange
            var dataSetName = "DataSetName";
            var id = "1";
            _dataProviderMock.Setup(dp => dp.DataSet.IsDataSetNameExist(dataSetName, id))
                .ThrowsAsync(new Exception("Name check error"));

            // Act
            var result = await _controller.DataSetNameExist(dataSetName, id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public void GetDbDetail_Returns_Json_With_Success()
        {
            // Arrange
            var dbProvider = "TestDbProvider";
            var encryptedDbProvider = CryptographyHelper.Encrypt(dbProvider);

            // Mock the GetSection method to return a section with the encrypted value
            var mockSection = new Mock<IConfigurationSection>();
            mockSection.Setup(x => x.Value).Returns(encryptedDbProvider);

            _configMock.Setup(c => c.GetSection("ConnectionStrings:DBProvider"))
                      .Returns(mockSection.Object);

            // Act
            var result = _controller.GetDbDetail() as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Value);

            // Convert to JSON and check structure - should be success response
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":", json);
            Assert.Contains($"\"{dbProvider}\"", json);
        }

        [Fact]
        public void GetDbDetail_Returns_Json_With_Error()
        {
            // Arrange
            // Don't set up any mock - this will cause the method to fail and return error JSON
            // The configuration will return null by default, causing CryptographyHelper.Decrypt to throw

            // Act
            var result = _controller.GetDbDetail() as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Value);

            // Convert to JSON and check structure - should be error response
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("\"Message\":", json);
            Assert.Contains("\"ErrorCode\":", json);
        }

        [Fact]
        public async Task GetColumnNamesBySchemaNameAndTableName_Returns_Json_With_Columns()
        {
            // Arrange
            var schemaName = "TestSchema";
            var tableName = "TestTable";
            var columns = new List<DataSetColumnsColumnNameVm>
            {
                new DataSetColumnsColumnNameVm { ColumnName = "Column1" }
            };

            _dataProviderMock.Setup(dp => dp.DataSetColumns.GetColumnNamesBySchemaNameAndTableName(schemaName, tableName))
                .ReturnsAsync(columns);

            // Act
            var result = await _controller.GetColumnNamesBySchemaNameAndTableName(schemaName, tableName) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetColumnNamesBySchemaNameAndTableName_Handles_Exception()
        {
            // Arrange
            var schemaName = "TestSchema";
            var tableName = "TestTable";
            _dataProviderMock.Setup(dp => dp.DataSetColumns.GetColumnNamesBySchemaNameAndTableName(schemaName, tableName))
                .ThrowsAsync(new Exception("Column retrieval error"));

            // Act
            var result = await _controller.GetColumnNamesBySchemaNameAndTableName(schemaName, tableName) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        // ===== CONTROLLER ATTRIBUTE TESTS =====

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange & Act
            var areaAttribute = _controller.GetType().GetCustomAttributes(typeof(AreaAttribute), false).FirstOrDefault() as AreaAttribute;

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void CreateOrUpdate_ShouldHaveRequiredAttributes()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("CreateOrUpdate");
            var httpPostAttribute = method.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(antiXssAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
        }

        [Fact]
        public void RunQuery_ShouldHaveHttpGetAttribute()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("RunQuery");
            var httpGetAttribute = method.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void GetPagination_ShouldHaveHttpGetAttribute()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("GetPagination");
            var httpGetAttribute = method.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void DataSetNameExist_ShouldHaveHttpGetAttribute()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("DataSetNameExist");
            var httpGetAttribute = method.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void GetDbDetail_ShouldHaveHttpGetAttribute()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("GetDbDetail");
            var httpGetAttribute = method.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void GetColumnNamesBySchemaNameAndTableName_ShouldHaveAntiXssAttribute()
        {
            // Arrange & Act
            var method = _controller.GetType().GetMethod("GetColumnNamesBySchemaNameAndTableName");
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        // ===== CONSTRUCTOR TESTS =====

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new DataSetController(
                _publisherMock.Object,
                _loggerMock.Object,
                _dataProviderMock.Object,
                _mapperMock.Object,
                _configMock.Object
            );

            // Assert
            Assert.NotNull(controller);
        }
    }
}
