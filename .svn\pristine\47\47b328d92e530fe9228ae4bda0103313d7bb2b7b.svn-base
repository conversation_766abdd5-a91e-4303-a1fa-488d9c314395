﻿using ContinuityPatrol.Application.Features.WorkflowExecutionTemp.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowExecutionTemp.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class WorkflowExecutionTempFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<WorkflowExecutionTemp> WorkflowExecutionTemps { get; set; }

    public CreateWorkflowExecutionTempCommand CreateWorkflowExecutionTempCommand { get; set; }
    public UpdateWorkflowExecutionTempCommand UpdateWorkflowExecutionTempCommand { get; set; }

    public WorkflowExecutionTempFixture()
    {
        WorkflowExecutionTemps = AutoWorkflowExecutionTempFixture.Create<List<WorkflowExecutionTemp>>();

        CreateWorkflowExecutionTempCommand = AutoWorkflowExecutionTempFixture.Create<CreateWorkflowExecutionTempCommand>();

        UpdateWorkflowExecutionTempCommand = AutoWorkflowExecutionTempFixture.Create<UpdateWorkflowExecutionTempCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<WorkflowExecutionTempProfile>();
        });

        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoWorkflowExecutionTempFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateWorkflowExecutionTempCommand>(p => p.WorkflowName, 10));
            fixture.Customize<CreateWorkflowExecutionTempCommand>(c => c.With(b => b.WorkflowName, 0.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateWorkflowExecutionTempCommand>(p => p.WorkflowName, 10));
            fixture.Customize<UpdateWorkflowExecutionTempCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<NodeWorkflowExecution>(c => c.With(b => b.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}