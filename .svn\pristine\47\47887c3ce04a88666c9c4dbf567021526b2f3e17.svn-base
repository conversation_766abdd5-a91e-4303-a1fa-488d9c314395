﻿using ContinuityPatrol.Application.Features.SmtpConfiguration.Events.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.SmtpConfiguration.Events
{
    public class CreateSmtpConfigurationEventTests
    {
        private readonly Mock<ILogger<SmtpConfigurationCreatedEventHandler>> _loggerMock;
        private readonly Mock<IUserActivityRepository> _userActivityRepositoryMock;
        private readonly Mock<ILoggedInUserService> _userServiceMock;
        private readonly SmtpConfigurationCreatedEventHandler _handler;

        public CreateSmtpConfigurationEventTests()
        {
            _loggerMock = new Mock<ILogger<SmtpConfigurationCreatedEventHandler>>();
            _userActivityRepositoryMock = new Mock<IUserActivityRepository>();
            _userServiceMock = new Mock<ILoggedInUserService>();

            _handler = new SmtpConfigurationCreatedEventHandler(
                _userServiceMock.Object,
                _loggerMock.Object,
                _userActivityRepositoryMock.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldLogAndSaveUserActivity_WhenEventIsHandled()
        {
            var createdEvent = new SmtpConfigurationCreatedEvent
            {
                UserName = "testUser"
            };

            _userServiceMock.Setup(us => us.UserId).Returns("123");
            _userServiceMock.Setup(us => us.LoginName).Returns("testLogin");
            _userServiceMock.Setup(us => us.RequestedUrl).Returns("/api/smtp");
            _userServiceMock.Setup(us => us.CompanyId).Returns("456");
            _userServiceMock.Setup(us => us.IpAddress).Returns("127.0.0.1");

            var cancellationToken = CancellationToken.None;

            await _handler.Handle(createdEvent, cancellationToken);

            _userActivityRepositoryMock.Verify(
                repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                    ua.UserId == "123" &&
                    ua.LoginName == "testLogin" &&
                    ua.RequestUrl == "/api/smtp" &&
                    ua.CompanyId == "456" &&
                    ua.HostAddress == "127.0.0.1" &&
                    ua.Action == "Create SmtpConfiguration" &&
                    ua.Entity == "SmtpConfiguration" &&
                    ua.ActivityType == "Create" &&
                    ua.ActivityDetails == "SmtpConfiguration 'testUser' Created Successfully."
                )),
                Times.Once
            );

            _loggerMock.Verify(
                logger => logger.LogInformation("SmtpConfiguration 'testUser' Created Successfully."),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ShouldNotThrow_WhenRepositoryFails()
        {
            var createdEvent = new SmtpConfigurationCreatedEvent
            {
                UserName = "testUser"
            };

            _userServiceMock.Setup(us => us.UserId).Returns("123");
            _userActivityRepositoryMock
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .ThrowsAsync(new System.Exception("Database error"));

            var cancellationToken = CancellationToken.None;

            var exception = await Record.ExceptionAsync(() => _handler.Handle(createdEvent, cancellationToken));

            Assert.Null(exception);

            _loggerMock.Verify(
                logger => logger.LogInformation("SmtpConfiguration 'testUser' Created Successfully."),
                Times.Once
            );
        }
    }
}
