﻿using ContinuityPatrol.Application.Features.FormHistory.Commands.Create;
using ContinuityPatrol.Application.Features.FormHistory.Commands.Update;
using ContinuityPatrol.Application.Features.FormHistory.Events.Create;
using ContinuityPatrol.Application.Features.FormHistory.Events.Delete;
using ContinuityPatrol.Application.Features.FormHistory.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class FormHistoryFixture : IDisposable
{
    public IMapper Mapper { get; }
    
    public List<FormHistory> FormHistories { get; set; }

    public List<UserActivity> UserActivities { get; set; }

    public CreateFormHistoryCommand CreateFormHistoryCommand { get; set; }

    public UpdateFormHistoryCommand UpdateFormHistoryCommand { get; set; }

    public FormHistoryCreatedEvent FormHistoryCreatedEvent { get; set; }

    public FormHistoryDeletedEvent FormHistoryDeletedEvent { get; set; }

    public FormHistoryUpdatedEvent FormHistoryUpdatedEvent { get; set; }

    public Fixture AutoFormHistoryFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateFormHistoryCommand>(p => p.LoginName, 10));
            fixture.Customize<CreateFormHistoryCommand>(c => c.With(b => b.LoginName, 10.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateFormHistoryCommand>(p => p.LoginName, 10));
            fixture.Customize<UpdateFormHistoryCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<FormHistory>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserActivity>(p => p.LoginName, 10));
            fixture.Customize<UpdateFormHistoryCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<FormHistory>(c => c.With(b => b.IsActive, true));


            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<FormHistoryCreatedEvent>(p => p.FormName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<FormHistoryDeletedEvent>(p => p.FormName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<FormHistoryUpdatedEvent>(p => p.FormName, 10));

            return fixture;
        }

    }

    public FormHistoryFixture()
    {
        FormHistories = AutoFormHistoryFixture.Create<List<FormHistory>>();

        UserActivities = AutoFormHistoryFixture.Create<List<UserActivity>>();

        CreateFormHistoryCommand = AutoFormHistoryFixture.Create<CreateFormHistoryCommand>();

        UpdateFormHistoryCommand = AutoFormHistoryFixture.Create<UpdateFormHistoryCommand>();

        FormHistoryCreatedEvent = AutoFormHistoryFixture.Create<FormHistoryCreatedEvent>();

        FormHistoryDeletedEvent = AutoFormHistoryFixture.Create<FormHistoryDeletedEvent>();

        FormHistoryUpdatedEvent = AutoFormHistoryFixture.Create<FormHistoryUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<FormHistoryProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public void Dispose()
    {

    }


}
