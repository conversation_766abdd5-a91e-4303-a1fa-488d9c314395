﻿using ContinuityPatrol.Application.Features.TemplateHistory.Queries.GetTemplateHistoryByTemplateId;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.TemplateHistory.Queries
{
    public class GetTemplateHistoryByTemplateIdQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ITemplateHistoryRepository> _mockTemplateHistoryRepository;
        private readonly GetTemplateHistoryByTemplateIdQueryHandler _handler;

        public GetTemplateHistoryByTemplateIdQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockTemplateHistoryRepository = new Mock<ITemplateHistoryRepository>();

            _handler = new GetTemplateHistoryByTemplateIdQueryHandler(
                _mockMapper.Object,
                _mockTemplateHistoryRepository.Object);
        }

        [Fact]
        public async Task Handle_ReturnsTemplateHistory_WhenTemplateHistoryExists()
        {
            var templateId = Guid.NewGuid().ToString();
            var templateHistoryEntities = new List<Domain.Entities.TemplateHistory>
            {
                new Domain.Entities.TemplateHistory { Id = 1, TemplateId = templateId },
                new Domain.Entities.TemplateHistory { Id = 2, TemplateId = templateId }
            };

            var templateHistoryVmList = new List<TemplateHistoryByIdVm>
            {
                new TemplateHistoryByIdVm { Id = Guid.NewGuid().ToString() },
                new TemplateHistoryByIdVm { Id = Guid.NewGuid().ToString() }
            };

            _mockTemplateHistoryRepository
                .Setup(repo => repo.GetTemplateHistoryById(templateId))
                .ReturnsAsync(templateHistoryEntities);

            _mockMapper
                .Setup(mapper => mapper.Map<List<TemplateHistoryByIdVm>>(templateHistoryEntities))
                .Returns(templateHistoryVmList);

            var query = new GetTemplateHistoryByTemplateIdQuery { TemplateId = templateId };
            var cancellationToken = CancellationToken.None;

            var result = await _handler.Handle(query, cancellationToken);

            Assert.NotNull(result);
            Assert.Equal(templateHistoryVmList.Count, result.Count);
            Assert.Equal(templateHistoryVmList[0].Id, result[0].Id);
            Assert.Equal(templateHistoryVmList[1].Id, result[1].Id);

            _mockTemplateHistoryRepository.Verify(repo => repo.GetTemplateHistoryById(templateId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<TemplateHistoryByIdVm>>(templateHistoryEntities), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenTemplateHistoryIsNull()
        {
            var templateId = Guid.NewGuid().ToString();
            _mockTemplateHistoryRepository
                .Setup(repo => repo.GetTemplateHistoryById(templateId))
                .ReturnsAsync((List<Domain.Entities.TemplateHistory>)null);

            var query = new GetTemplateHistoryByTemplateIdQuery { TemplateId = templateId };
            var cancellationToken = CancellationToken.None;

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, cancellationToken));

            _mockTemplateHistoryRepository.Verify(repo => repo.GetTemplateHistoryById(templateId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<TemplateHistoryByIdVm>>(It.IsAny<List<Domain.Entities.TemplateHistory>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenTemplateHistoryDtoIsNull()
        {
            var templateId = Guid.NewGuid().ToString();
            var templateHistoryEntities = new List<Domain.Entities.TemplateHistory>
            {
                new Domain.Entities.TemplateHistory { Id = 1, TemplateId = templateId }
            };

            _mockTemplateHistoryRepository
                .Setup(repo => repo.GetTemplateHistoryById(templateId))
                .ReturnsAsync(templateHistoryEntities);

            _mockMapper
                .Setup(mapper => mapper.Map<List<TemplateHistoryByIdVm>>(templateHistoryEntities))
                .Returns((List<TemplateHistoryByIdVm>)null);

            var query = new GetTemplateHistoryByTemplateIdQuery { TemplateId = templateId };
            var cancellationToken = CancellationToken.None;

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, cancellationToken));

            _mockTemplateHistoryRepository.Verify(repo => repo.GetTemplateHistoryById(templateId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<TemplateHistoryByIdVm>>(templateHistoryEntities), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenRepositoryThrows()
        {
            var templateId = Guid.NewGuid().ToString();
            _mockTemplateHistoryRepository
                .Setup(repo => repo.GetTemplateHistoryById(templateId))
                .ThrowsAsync(new Exception("Database error"));

            var query = new GetTemplateHistoryByTemplateIdQuery { TemplateId = templateId };
            var cancellationToken = CancellationToken.None;

            await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, cancellationToken));

            _mockTemplateHistoryRepository.Verify(repo => repo.GetTemplateHistoryById(templateId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<TemplateHistoryByIdVm>>(It.IsAny<List<Domain.Entities.TemplateHistory>>()), Times.Never);
        }
    }
}
