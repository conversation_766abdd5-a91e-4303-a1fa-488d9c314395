﻿using ContinuityPatrol.Application.Features.WorkflowPermission.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowPermission.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowPermission.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowPermission.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowPermissionModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Orchestration;

public class WorkflowPermissionService : BaseClient , IWorkflowPermissionService
{
    public WorkflowPermissionService(IConfiguration config, IAppCache cache, ILogger<WorkflowPermissionService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<WorkflowPermissionListVm>> GetWorkflowPermissions()
    {
        var request = new RestRequest("api/v6/workflowpermissions");

        return await GetFromCache<List<WorkflowPermissionListVm>>(request, "GetWorkflowPermissions");
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/workflowpermissions/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<WorkflowPermissionDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/workflowpermissions/{id}");

        return await Get<WorkflowPermissionDetailVm>(request);
    }
    
    public async Task<BaseResponse> CreateAsync(CreateWorkflowPermissionCommand createCommand)
    {
        var request = new RestRequest("api/v6/workflowpermissions", Method.Post);

        request.AddJsonBody(createCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowPermissionCommand updateCommand)
    {
        var request = new RestRequest("api/v6/workflowpermissions", Method.Put);

        request.AddJsonBody(updateCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<PaginatedResult<WorkflowPermissionListVm>> GetPaginatedWorkflowPermissions(GetWorkflowPermissionPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/workflowpermissions/paginated-list");

        return await Get<PaginatedResult<WorkflowPermissionListVm>>(request);
    }
}