﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class AlertNotificationRepositoryMocks
{
    public static Mock<IAlertNotificationRepository> CreateAlertNotificationRepository(List<AlertNotification> alertNotifications)
    {
        var alertNotificationRepository = new Mock<IAlertNotificationRepository>();

        alertNotificationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alertNotifications);

        alertNotificationRepository.Setup(repo => repo.AddAsync(It.IsAny<AlertNotification>())).ReturnsAsync(
            (AlertNotification alertNotification) =>
            {
                alertNotification.Id = new Fixture().Create<int>();

                alertNotification.ReferenceId = new Fixture().Create<Guid>().ToString();

                alertNotifications.Add(alertNotification);

                return alertNotification;
            });

        return alertNotificationRepository;
    }

    public static Mock<IAlertNotificationRepository> UpdateAlertNotificationRepository(List<AlertNotification> alertNotifications)
    {
        var alertNotificationRepository = new Mock<IAlertNotificationRepository>();

        alertNotificationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alertNotifications);

        alertNotificationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alertNotifications.SingleOrDefault(x => x.ReferenceId == i));

        alertNotificationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AlertNotification>())).ReturnsAsync((AlertNotification alertNotification) =>
        {
            var index = alertNotifications.FindIndex(item => item.ReferenceId == alertNotification.ReferenceId);

            alertNotifications[index] = alertNotification;

            return alertNotification;

        });
        return alertNotificationRepository;
    }

    public static Mock<IAlertNotificationRepository> DeleteAlertNotificationRepository(List<AlertNotification> alertNotifications)
    {
        var alertNotificationRepository = new Mock<IAlertNotificationRepository>();

        alertNotificationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alertNotifications);

        alertNotificationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alertNotifications.SingleOrDefault(x => x.ReferenceId == i));

        alertNotificationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AlertNotification>())).ReturnsAsync((AlertNotification alertNotification) =>
        {
            var index = alertNotifications.FindIndex(item => item.ReferenceId == alertNotification.ReferenceId);

            alertNotification.IsActive = false;

            alertNotifications[index] = alertNotification;

            return alertNotification;
        });

        return alertNotificationRepository;
    }

    public static Mock<IAlertNotificationRepository> GetAlertNotificationRepository(List<AlertNotification> alertNotifications)
    {
        var alertNotificationRepository = new Mock<IAlertNotificationRepository>();

        alertNotificationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alertNotifications);

        alertNotificationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alertNotifications.SingleOrDefault(x => x.ReferenceId == i));

        return alertNotificationRepository;
    }
    public static Mock<IAlertNotificationRepository> GetAlertNotificationEmptyRepository()
    {
        var alertNotificationRepository = new Mock<IAlertNotificationRepository>();

        alertNotificationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<AlertNotification>());

        alertNotificationRepository.Setup(repo => repo.GetAlertNotificationByInfraObjectIdAndAlertCode(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(new List<AlertNotification>());

        return alertNotificationRepository;
    }

    public static Mock<IAlertNotificationRepository> GetPaginatedAlertNotificationRepository(List<AlertNotification> alertNotifications)
    {
        var alertNotificationRepository = new Mock<IAlertNotificationRepository>();

        var queryableAlertNotification = alertNotifications.BuildMock();

        alertNotificationRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableAlertNotification);

        return alertNotificationRepository;
    }

    public static Mock<IAlertNotificationRepository> GetAlertNotificationDetailByInfraObjectId(List<AlertNotification> alertNotifications)
    {
        var alertNotificationRepository = new Mock<IAlertNotificationRepository>();

        alertNotificationRepository.Setup(repo => repo.GetAlertNotificationByInfraObjectIdAndAlertCode(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(alertNotifications);

        return alertNotificationRepository;
    }
}