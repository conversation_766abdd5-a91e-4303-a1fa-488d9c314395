﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class BusinessServiceEvaluationRepository : BaseRepository<BusinessServiceEvaluation>,
    IBusinessServiceEvaluationRepository
{
    private readonly ILoggedInUserService _loggedInUserService;

    public BusinessServiceEvaluationRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<BusinessServiceEvaluation>> ListAllAsync()
    {
        var businessServices = base.QueryAll(businessService => businessService.IsActive)
            .AsNoTracking();

        return _loggedInUserService.IsAllInfra
            ? await businessServices.ToListAsync()
            : AssignedBusinessServices(businessServices);
    }

    public async Task<BusinessServiceEvaluation> GetBusinessServiceEvaluationByReferenceIdAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ReferenceId", "ReferenceId cannot be invalid");

        var query = Entities.AsNoTracking().Where(x => x.ReferenceId == id && x.IsActive);

        if (!_loggedInUserService.IsAllInfra)
        {
            var assignedInfra = JsonConvert.DeserializeObject<AssignedEntity>(_loggedInUserService.AssignedInfras);
            var assignedIds = new HashSet<string>(assignedInfra.AssignedBusinessServices.Select(a => a.Id));
            query = query.Where(x => assignedIds.Contains(x.BusinessServiceId));
        }

        var bServiceEvaluation = await query.FirstOrDefaultAsync();

        if (bServiceEvaluation == null)
            throw new NotFoundException(nameof(BusinessServiceEvaluation), id);

        return bServiceEvaluation;
    }


    public IReadOnlyList<BusinessServiceEvaluation> AssignedBusinessServices(IQueryable<BusinessServiceEvaluation> businessServices)
    {
        if (businessServices == null)
            return Array.Empty<BusinessServiceEvaluation>();

        if (AssignedEntity.AssignedBusinessServices.Count == 0)
            return Array.Empty<BusinessServiceEvaluation>();

        // Use HashSet for more efficient lookups
        var assignedIds = new HashSet<string>(
            AssignedEntity.AssignedBusinessServices.Select(x => x.Id));

        return businessServices
            .Where(x => assignedIds.Contains(x.BusinessServiceId))
            .ToList();
    }
}