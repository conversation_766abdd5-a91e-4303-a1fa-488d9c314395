﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IBusinessFunctionRepository : IRepository<BusinessFunction>
{
    Task<List<BusinessFunction>> GetBusinessFunctionNames();
    Task<bool> IsBusinessFunctionNameExist(string name, string id);
    Task<bool> IsBusinessFunctionNameUnique(string name);
    Task<List<BusinessFunction>> GetBusinessFunctionListByBusinessServiceId(string businessServiceId);
    Task<List<BusinessFunction>> GetByBusinessServiceIds(List<string> businessServiceIds);
    Task<IReadOnlyList<BusinessFunction>> GetByReferenceIdsAsync(List<string> ids);
    Task<List<BusinessFunction>> GetNamesByBusinessServiceId(string businessServiceId);
    Task<List<BusinessFunction>> GetFilterByBusinessServiceId(string businessServiceId);
}