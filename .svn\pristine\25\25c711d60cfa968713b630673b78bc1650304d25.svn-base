﻿using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Application.Features.Report.Queries.GetRpoSlaDeviationReport;
using ContinuityPatrol.Domain.ViewModels.RpoSlaDeviationReportModel;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Queries
{
    public class GetRpoSlaDeviationReportByStartTimeAndEndTimeQueryHandlerTests
    {
        private readonly Mock<IRpoSlaDeviationReportRepository> _mockRpoSlaDeviationReportRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly GetRpoSlaDeviationReportByStartTimeAndEndTimeQueryHandler _handler;

        public GetRpoSlaDeviationReportByStartTimeAndEndTimeQueryHandlerTests()
        {
            _mockRpoSlaDeviationReportRepository = new Mock<IRpoSlaDeviationReportRepository>();
            _mockMapper = new Mock<IMapper>();
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();
            _mockPublisher = new Mock<IPublisher>();

            _handler = new GetRpoSlaDeviationReportByStartTimeAndEndTimeQueryHandler(
                _mockRpoSlaDeviationReportRepository.Object,
                _mockMapper.Object,
                _mockLoggedInUserService.Object,
                _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnReport_WhenBusinessServiceIdProvided()
        {
            var query = new GetRpoSlaDeviationReportByStartTimeAndEndTimeQuery
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                CreatedDate = "DateTime.UtcNow.AddDays(-7)",
                LastModifiedDate = "DateTime.UtcNow"
            };

            var reportEntities = new List<Domain.Entities.RpoSlaDeviationReport>
            {
                new Domain.Entities.RpoSlaDeviationReport { Id = 1 }
            };

            _mockRpoSlaDeviationReportRepository
                .Setup(x => x.GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndBusinessServiceId(
                    query.BusinessServiceId, query.CreatedDate, query.LastModifiedDate))
                .ReturnsAsync(reportEntities);

            _mockMapper
                .Setup(x => x.Map<List<GetRpoSlaDeviationReportByStartTimeAndEndTimeVm>>(reportEntities))
                .Returns(new List<GetRpoSlaDeviationReportByStartTimeAndEndTimeVm>());

            _mockLoggedInUserService.Setup(x => x.LoginName).Returns("TestUser");

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("TestUser", result.ReportGeneratedBy);
            Assert.NotNull(result.RpoSlaDeviationReportByStartTimeAndEndTimeVms);

            _mockPublisher.Verify(x => x.Publish(
                It.IsAny<ReportViewedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyReport_WhenNoRecordsFound()
        {
            var query = new GetRpoSlaDeviationReportByStartTimeAndEndTimeQuery
            {
                BusinessServiceId = Guid.NewGuid().ToString(),
                CreatedDate = "DateTime.UtcNow.AddDays(-7)",
                LastModifiedDate = "DateTime.UtcNow"
            };

            _mockRpoSlaDeviationReportRepository
                .Setup(x => x.GetRpoSlaDeviationReportListByStartTimeAndEndTimeAndBusinessServiceId(
                    query.BusinessServiceId, query.CreatedDate, query.LastModifiedDate))
                .ReturnsAsync(new List<Domain.Entities.RpoSlaDeviationReport>());

            _mockLoggedInUserService.Setup(x => x.LoginName).Returns("TestUser");

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.RpoSlaDeviationReportByStartTimeAndEndTimeVms);

            _mockPublisher.Verify(x => x.Publish(
                It.IsAny<ReportViewedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowException_WhenQueryIsNull()
        {
            GetRpoSlaDeviationReportByStartTimeAndEndTimeQuery query = null;

            await Assert.ThrowsAsync<ArgumentNullException>(() => _handler.Handle(query, CancellationToken.None));
        }
    }
}
