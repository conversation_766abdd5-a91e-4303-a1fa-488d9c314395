﻿using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CredentialProfile.Validators;

public class CreateCredentialProfileValidatorTests
{
    private readonly Mock<ICredentialProfileRepository> _mockCredentialProfileRepository;

    public CreateCredentialProfileValidatorTests()
    {
        var credentialProfiles = new Fixture().Create<List<Domain.Entities.CredentialProfile>>();

        _mockCredentialProfileRepository = CredentialProfileRepositoryMocks.CreateCredentialProfileRepository(credentialProfiles);
    }

    //Name

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Name_InCredentialProfile_WithEmpty(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = "";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Name_InCredentialProfile_IsNull(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = null;

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameNotEmpty, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Name_InCredentialProfile_MinimumRange(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = "AR";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameRange, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Name_InCredentialProfile_MaximumRange(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameRange, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Valid_Name_InCredentialProfile(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = "   PTS  ";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Valid_Name_InCredentialProfile_With_DoubleSpace_InFront(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = "  PTS India";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Valid_Name_InCredentialProfile_With_TripleSpace_InBetween(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = "PTS  Technosoft   India";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }
    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Valid_Name_InCredentialProfile_With_SpecialCharacters_InFront(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = "#@PTS India";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Valid_Name_InCredentialProfile_With_SpecialCharacters_Only(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = "@!!#$$%%^&*<>:";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Valid_Name_InCredentialProfile_With_UnderScore_InFront(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = "_PTS";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Valid_Name_InCredentialProfile_With_UnderScore_InFront_AndBack(CreateCredentialProfileCommand credentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        credentialProfileCommand.Name = "_PTSIndia_";

        var validateResult = await validator.ValidateAsync(credentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Valid_Name_InCredentialProfile_With_Numbers_InFront(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = "1243PTSIndia";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Valid_Name_InCredentialProfile_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = "_1243PTSIndia_";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Valid_Name_InCredentialProfile_With_UnderScore_InFront_With_Numbers_InBack(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = "_PTSIndia1234";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_Valid_Name_InCredentialProfile_With_Numbers_Only(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.Name = "1234567890";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileNameValid, validateResult.Errors[0].ErrorMessage);
    }

    //CredentialProfileType

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_CredentialProfileType_WithEmpty(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.CredentialType = "";

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileTypeRequired, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoCredentialProfileData]
    public async Task Verify_Create_CredentialProfileType_InCredentialProfile_IsNull(CreateCredentialProfileCommand createCredentialProfileCommand)
    {
        var validator = new CreateCredentialProfileCommandValidator(_mockCredentialProfileRepository.Object);

        createCredentialProfileCommand.CredentialType = null;

        var validateResult = await validator.ValidateAsync(createCredentialProfileCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.CredentialProfile.CredentialProfileTypeNotNullRequired, validateResult.Errors[2].ErrorMessage);
    }
}