﻿using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowProfileInfoViewRepositoryMocks
{
    public static Mock<IWorkflowProfileInfoViewRepository> GetWorkflowProfileInfoViewRepository(List<WorkflowProfileInfoView> workflowProfileInfoViews)
    {
        var workflowProfileInfoViewRepository = new Mock<IWorkflowProfileInfoViewRepository>();

        workflowProfileInfoViewRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowProfileInfoViews);

        workflowProfileInfoViewRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowProfileInfoViews.SingleOrDefault(x => x.ReferenceId == i));

        return workflowProfileInfoViewRepository;
    }

    public static Mock<IWorkflowProfileInfoViewRepository> GetWorkflowProfileInfoViewEmptyRepository()
    {
        var workflowProfileInfoViewEmptyRepository = new Mock<IWorkflowProfileInfoViewRepository>();

        workflowProfileInfoViewEmptyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowProfileInfoView>());

        return workflowProfileInfoViewEmptyRepository;
    }

    public static Mock<IWorkflowProfileInfoViewRepository> GetWorkflowProfileInfoByProfileIds(List<WorkflowProfileInfoView> workflowProfileInfoViews)
    {
        var workflowProfileInfoViewEmptyRepository = new Mock<IWorkflowProfileInfoViewRepository>();

        workflowProfileInfoViewEmptyRepository.Setup(repo => repo.GetWorkflowProfileInfoByProfileIds(It.IsAny<List<string>>())).ReturnsAsync(workflowProfileInfoViews);

        return workflowProfileInfoViewEmptyRepository;
    }

}