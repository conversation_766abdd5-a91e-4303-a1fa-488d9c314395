using ContinuityPatrol.Application.Features.FiaInterval.Events.Update;

namespace ContinuityPatrol.Application.Features.FiaInterval.Commands.Update;

public class UpdateFiaIntervalCommandHandler : IRequestHandler<UpdateFiaIntervalCommand, UpdateFiaIntervalResponse>
{
    private readonly IFiaIntervalRepository _fiaIntervalRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateFiaIntervalCommandHandler(IMapper mapper, IFiaIntervalRepository fiaIntervalRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _fiaIntervalRepository = fiaIntervalRepository;
        _publisher = publisher;
    }

    public async Task<UpdateFiaIntervalResponse> Handle(UpdateFiaIntervalCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _fiaIntervalRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.FiaInterval), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateFiaIntervalCommand), typeof(Domain.Entities.FiaInterval));

        await _fiaIntervalRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateFiaIntervalResponse
        {
            Message = "Time interval updated successfully",

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new FiaIntervalUpdatedEvent { Name = "Time interval" }, cancellationToken);

        return response;
    }
}