﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.UpdateLog;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetByWorkflowOperationId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetLogDataByGroupId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetNames;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatus;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatusList;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningUserDetails;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupByInfraObjectId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupByNodeId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupListByWorkflowId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowServiceStatus;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Orchestration;

public class WorkflowOperationGroupService : BaseClient,IWorkflowOperationGroupService
{
    public WorkflowOperationGroupService(IConfiguration config, IAppCache cache, ILogger<WorkflowOperationGroupService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<WorkflowOperationGroupNameVm>> GetWorkflowOperationGroupNames()
    {
        var request = new RestRequest("api/v6/workflowoperationgroup/names");

        return await GetFromCache<List<WorkflowOperationGroupNameVm>>(request, "GetTemplateNames");
    }

    public async Task<List<WorkflowOperationGroupListVm>> GetWorkflowOperationGroupList()
    {
        var request = new RestRequest("api/v6/workflowoperationgroup");

        return await GetFromCache<List<WorkflowOperationGroupListVm>>(request, "GetWorkflowOperationGroupList");
    }

    public async Task<List<WorkflowOperationGroupRunningStatusVm>> GetWorkflowOperationGroupRunningList()
    {
        var request = new RestRequest("api/v6/workflowoperationgroup/runningstatus");

        return await Get<List<WorkflowOperationGroupRunningStatusVm>>(request);
    }

    public async Task<List<WorkflowOperationGroupRunningUserVm>> GetWorkflowOperationGroupByRunningUserId(string userId)
    {
        var request = new RestRequest($"api/v6/workflowoperationgroup/userid?userId={userId}");

        return await Get<List<WorkflowOperationGroupRunningUserVm>>(request);
    }

    public async Task<List<WorkflowOperationGroupByNodeIdVm>> GetWorkflowOperationGroupByNodeId(string nodeId)
    {
        var request = new RestRequest($"api/v6/workflowoperationgroup/nodeid?nodeId={nodeId}");

        return await Get<List<WorkflowOperationGroupByNodeIdVm>>(request);
    }

    public async Task<List<GetByWorkflowOperationIdVm>> GetWorkflowOperationGroupByWorkflowOperationId(string workflowOperationId)
    {
        var request = new RestRequest($"api/v6/workflowoperationgroup/by/{workflowOperationId}");

        return await Get<List<GetByWorkflowOperationIdVm>>(request);
    }

    public async Task<List<WorkflowOperationGroupByInfraObjectIdVm>> GetWorkflowOperationGroupByInfraObjectId(string infraObjectId)
    {
        var request = new RestRequest($"api/v6/workflowoperationgroup/infraobjectid?infraObjectId={infraObjectId}");

        return await Get<List<WorkflowOperationGroupByInfraObjectIdVm>>(request);
    }

    public async Task<bool> IsWorkflowOperationGroupNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/workflowoperationgroup/name-exist?name={name}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<WorkflowOperationGroupDetailVm> GetWorkflowOperationGroupById(string id)
    {
        var request = new RestRequest($"api/v6/workflowoperationgroup/{id}");

        return await Get<WorkflowOperationGroupDetailVm>(request);
    }

    public async Task<List<WorkflowOperationGroupListByWorkflowIdVm>> GetOperationGroupByWorkflowIdAndOperationId(string workflowId, string workflowOperationId)
    {
        var request = new RestRequest($"api/v6/workflowoperationgroup/workflowid?workflowId={workflowId}&workflowOperationId={workflowOperationId}");

        return await Get<List<WorkflowOperationGroupListByWorkflowIdVm>>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowOperationGroupCommand createWorkflowOperationGroupCommand)
    {
        var request = new RestRequest("api/v6/workflowoperationgroup", Method.Post);

        request.AddJsonBody(createWorkflowOperationGroupCommand);

        ClearCache("GetWorkflowOperationGroupList");

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowOperationGroupCommand updateWorkflowOperationGroupCommand)
    {
        var request = new RestRequest("api/v6/workflowoperationgroup", Method.Put);

        request.AddJsonBody(updateWorkflowOperationGroupCommand);

        ClearCache("GetWorkflowOperationGroupList");

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/workflowoperationgroup/{id}", Method.Delete);

        ClearCache("GetWorkflowOperationGroupList");

        return await Delete<BaseResponse>(request);
    }

    public async Task<List<GetLogByGroupIdVm>> GetLogDataByGroupId(string groupId)
    {
        var request = new RestRequest($"api/v6/workflowoperationgroup/log-push?workflowOperationGroupId={groupId}");

        return await Get<List<GetLogByGroupIdVm>>(request);
    }

    public async Task<GetWorkflowServiceResponse> CheckWindowsServiceConnection(string type)
    {
        var request = new RestRequest($"api/v6/workflowoperationgroup/check-windows-service?type={type}");

        return await Get<GetWorkflowServiceResponse>(request);
    }

    public async Task<PaginatedResult<WorkflowOperationGroupListVm>> GetPaginatedWorkflowOperationGroup(GetWorkflowOperationGroupPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/workflowoperationgroup/paginated-list");

        return await Get<PaginatedResult<WorkflowOperationGroupListVm>>(request);
    }

    public async Task<List<ProfileRunningCountListVm>> GetWorkflowOperationGroupRunningStatusList()
    {
        var request = new RestRequest("api/v6/workflowoperationgroup/getrunningstatuslist");

        return await Get<List<ProfileRunningCountListVm>>(request);
    }

    public async Task<UpdateOperationGroupLogResponse> UpdateOperationGroupLog(UpdateOperationGroupLogCommand updateWorkflowOperationGroupCommand)
    {
        var request = new RestRequest("api/v6/workflowoperationgroup/update-log-state", Method.Put);

        request.AddJsonBody(updateWorkflowOperationGroupCommand);

        ClearCache("GetWorkflowOperationGroupList");

        return await Put<UpdateOperationGroupLogResponse>(request);
    }
}