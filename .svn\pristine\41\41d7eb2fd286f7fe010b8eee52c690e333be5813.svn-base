﻿using ContinuityPatrol.Infrastructure.Contract;

namespace ContinuityPatrol.Infrastructure.Impl;

public class SymmetrixService : ISymmetrixService
{
    private async Task<string> ExecuteApiAsync(string url, string ipAddress,string port,string userName,string password)
    {
        var baseUrl = $"https://{ipAddress}:{port}/";

       var client = new RestClient(new RestClientOptions(baseUrl)
        {
            RemoteCertificateValidationCallback = (sender, cert, chain, sslPolicyErrors) => true,
            ThrowOnAnyError = true
        });

      var authHeader = "Basic " + Convert.ToBase64String(Encoding.UTF8.GetBytes($"{userName}:{password}"));


        var request = new RestRequest(url, Method.Get);
        request.AddHeader("Content-Type", "application/json");
        request.AddHeader("Authorization", authHeader);

        var response = await client.ExecuteAsync(request);
        if (!response.IsSuccessful)
            throw new Exception($"API Error: {response.StatusCode} - {response.ErrorMessage}");

        return response.Content ?? string.Empty;
    }

    public async Task<List<string>> GetSymmetrixIds(string version, string ip, string port, string username, string password)
    {
        var url = $"univmax/restapi/{version}/sloprovisioning/symmetrix";
        var responseJson = await ExecuteApiAsync(url,ip,port,username,password);
        var ids = JObject.Parse(responseJson)["symmetrixId"]?.ToObject<List<string>>();
        return ids ?? new List<string>();
    }

    public async Task<List<string>> GetStorageGroupIds(string version, string storageGroupId, string ip, string port, string username, string password)
    {
        var url = $"univmax/restapi/{version}/sloprovisioning/symmetrix/{storageGroupId}/storagegroup";
        var responseJson = await ExecuteApiAsync(url, ip, port,username,password);
        var ids = JObject.Parse(responseJson)["storageGroupId"]?.ToObject<List<string>>();
        return ids ?? new List<string>();
    }

}