﻿using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Replication.Commands.Update;
using ContinuityPatrol.Application.Features.Replication.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Replication.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Replication.Queries.GetReplicationByLicensekey;
using ContinuityPatrol.Application.Features.Replication.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IReplicationService
{
    Task<CreateReplicationResponse> CreateAsync(CreateReplicationCommand createReplicationCommand);
    Task<BaseResponse> SaveAsReplication(SaveAsReplicationCommand saveAsReplicationCommand);
    Task<UpdateReplicationResponse> UpdateAsync(UpdateReplicationCommand updateReplicationCommand);
    Task<BaseResponse> DeleteAsync(string replicationId);
    Task<List<ReplicationListVm>> GetReplicationList();
    Task<bool> IsReplicationNameExist(string replicationName, string id);
    Task<List<ReplicationNameVm>> GetReplicationNames();
    Task<List<GetReplicationByLicenseKeyListVm>> GetReplicationByLicenseKey(string licenseId);
    Task<List<ReplicationTypeVm>> GetReplicationByType(string typeId);
    Task<ReplicationDetailVm> GetReplicationById(string id);
    Task<PaginatedResult<ReplicationListVm>> GetPaginatedReplications(GetReplicationPaginatedListQuery query);
}