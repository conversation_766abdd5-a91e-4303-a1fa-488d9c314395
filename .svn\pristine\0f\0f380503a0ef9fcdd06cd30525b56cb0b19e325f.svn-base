$(function () {
   $('#saveFunction').prop('disabled', true);
    let createPermission = $("#AdminCreate")?.data("create-permission")?.toLowerCase();
     let currentuserRoleId = sessionStorage.getItem('userRoleId');
    if (createPermission == 'false') {
        $("#saveFunction, #btnCancel").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }   

    //Functions
    function updateCheckboxByRole(role) {
        let checkboxes = $('input[type="checkbox"]'),
            disableAll = ["SuperAdmin", "Administrator", "Operator", "Manager"].includes(role);
        checkboxes.toggleClass('opacity-80', disableAll);
        checkboxes.prop({ 'checked': !disableAll, 'disabled': disableAll });
        $("#saveFunction").prop("disabled", true);
    };
    function validateDropDown(value, errorMessage, errorElement) {
        if (!value) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    };
    function anyCheckboxChecked() {
        return $('.form-check-input').is(':checked');
    };
    function getCheckedCategories(data) {
        let count = 0;
        for (const category in data.Permissions) {
            const permissions = data.Permissions[category];
            const categoryChecked = Object.values(permissions).some(checkbox => checkbox);
            if (categoryChecked) {
                count++;
            }
        }
        const errorElement = document.getElementById('treeList-error');
        if (count === 0) {
            errorElement.textContent = 'Please check at least one checkbox';
            errorElement.classList.add('field-validation-error');
            return false;
        } else {
            errorElement.textContent = '';
            errorElement.classList.remove('field-validation-error');
            return true;
        }
    }
    function getUserRoleById(forUpdateUserRole) {
        if (forUpdateUserRole != null && typeof forUpdateUserRole !== 'undefined') {
            $.ajax({
                type: "GET",
                url: RootUrl + "Admin/AccessManager/GetRoleDetails",
                data: { role: forUpdateUserRole },
                dataType: "json",
                success: function (data) {
                    if (data != null) {
                        $('#userRoleRefId').val(data.id);
                        $('#saveFunction').text(data.id != null ? 'Update' : 'Save');
                        if (data.properties != null) {
                            let roleName = data.roleName;
                            let jsonDataStore = JSON.parse(data.properties);
                            const sectionActionCheckboxMap = {
                                Dashboard: { Monitor: '#chkMonitor', Management: '#chkManagment' },
                                Configuration: { CreateAndEdit: '#chkConfigAdd', Delete: '#chkConfigDelete', View: '#chkConfigView' },
                                Manage: { CreateAndEdit: '#chkManageAdd', Delete: '#chkManageDelete', View: '#chkManageView' },
                                Alerts: { CreateAndEdit: '#chkalertsAdd', Delete: '#chkalertsDelete', View: '#chkalertsView' },
                                Reports: { CreateAndEdit: '#chkReportsAdd', Delete: '#chkReportsDelete', View: '#chkReportsView' },
                                Orchestration: { CreateAndEdit: '#chkOrchestrationAdd', Delete: '#chkOrchestrationDelete', View: '#chkOrchestrationView', Execute: '#chkOrchestrationExecute' },
                                Admin: { CreateAndEdit: '#chkAdminAdd', Delete: '#chkAdminDelete', View: '#chkAdminView' },
                                Drift: { CreateAndEdit: '#chkDriftAdd', Delete: '#chkDriftDelete', View: '#chkDriftView' },
                                ResiliencyReadiness: { CreateAndEdit: '#chkResilencyAdd', Delete: '#chkResilencyDelete', View: '#chkResilencyView' },
                                Cyber: { CreateAndEdit: '#chkCyberAdd', Delete: '#chkCyberDelete', View: '#chkCyberView' },
                                CloudConnect: { View: '#chkcloudView' }
                            };
                            const isEditableRole = !(roleName === "SuperAdmin" || roleName === "Operator" || roleName === "Manager" || roleName === "Administrator");
                            for (let section in jsonDataStore.Permissions) {
                                let actions = jsonDataStore.Permissions[section];
                                if (sectionActionCheckboxMap.hasOwnProperty(section)) {
                                    let actionCheckboxMap = sectionActionCheckboxMap[section];
                                    for (let action in actions) {
                                        let isChecked = actions[action];
                                        if (actionCheckboxMap.hasOwnProperty(action)) {
                                            let checkboxSelector = actionCheckboxMap[action];
                                            $(checkboxSelector).prop('checked', isChecked === true);
                                            if (action === 'View' && isEditableRole) {
                                                $(checkboxSelector).prop('disabled', false);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        $('#userRoleRefId').val('');
                        $('#saveFunction').text('Save');
                        $('input[type="checkbox"]').prop('checked', false);
                    }
                },
                error: function () {
                    notificationAlert(data.errorMessage);
                }
            });
        }
    }

    //Events

    $(document).on('change', '#userRoleName', function () {
        $("#btnCancel").prop("disabled", true);
        let userRole = $("#userRoleName option:selected"),
            errorElement = $('#selectUserRole-error'),
            treeListErrorElement = $('#treeList-error'),
            userRoleName = userRole.text().trim(),
            userRoleId = userRole.attr("id");

        $("#saveFunction, #btnCancel").toggle(!['Administrator', 'Manager', 'Operator', 'SuperAdmin'].includes(userRoleName));
        updateCheckboxByRole(userRoleName);
        getUserRoleById(userRoleId);
        validateDropDown(userRoleName, "select role", errorElement);
        treeListErrorElement.text('').removeClass('field-validation-error');
        if (createPermission === 'false') {
            $("#saveFunction, #btnCancel").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle data-bs-target id');
        }
        $('#chkInfraFull').prop('checked', userRoleName === 'SuperAdmin');
        $('#chkInfraCustom').prop('checked', userRoleName !== 'SuperAdmin');
    });

      if (currentuserRoleId != null) {
        var userRole = $('#userRoleName option[id="' + currentuserRoleId + '"]').val();
        $('#userRoleName').val(userRole).trigger('change');
        sessionStorage.removeItem('userRoleId');
      }
      else {
        let currentUserRole = $("#currentuserRole")?.data("role");
        $('#userRoleName').val(currentUserRole).trigger('change');
      };

    $('#btnCancel').on('click', function () {
        userRoleName = $("#userRoleName option:selected").text().trim();
        userRoleId = $("#userRoleName option:selected").attr("id");
        $('#TreeView').empty();
        $('#saveFunction').text('Save').prop("disabled", true);
        $('#btnCancel').prop("disabled", true);
        $(':checkbox, .AccessManager_Table input[type="checkbox"]').prop("checked", false);
        getUserRoleById(userRoleId);
        updateCheckboxByRole(userRoleName);
        $('#selectUserRole-error, #selectUserName-error').hide();
        $('#chkInfraFull').prop('checked', userRoleName === 'SuperAdmin');
        $('#chkInfraCustom').prop('checked', userRoleName !== 'SuperAdmin');
    });

    $('#saveFunction').on('click', async function (event) {
        let userRole = $("#userRoleName option:selected");
        let userRoleRefId = $('#userRoleRefId').val();
        let userRoleName = userRole.text().trim();
        let userRoleId = userRole.attr("id");

        let jsonData = {
            "Permissions": {
                "Dashboard": {
                    "View": true,
                    "Monitor": $('#chkMonitor').is(':checked'),
                    "Management": $('#chkManagment').is(':checked')
                },
                "Configuration": {
                    "CreateAndEdit": $('#chkConfigAdd').is(':checked'),
                    "Delete": $('#chkConfigDelete').is(':checked'),
                    "View": $('#chkConfigView').is(':checked')
                },
                "Manage": {
                    "CreateAndEdit": $('#chkManageAdd').is(':checked'),
                    "Delete": $('#chkManageDelete').is(':checked'),
                    "View": $('.chkManageView').is(':checked')
                },
                "Alerts": {
                    "CreateAndEdit": $('.chkalertsAdd').is(':checked'),
                    "Delete": $('#chkalertsDelete').is(':checked'),
                    "View": $('.chkalertsView').is(':checked')
                },
                "Reports": {

                    "CreateAndEdit": $('#chkReportsAdd').is(':checked'),
                    "Delete": $('#chkReportsDelete').is(':checked'),
                    "View": $('#chkReportsView').is(':checked'),
                },
                "Orchestration": {

                    "CreateAndEdit": $('#chkOrchestrationAdd').is(':checked'),
                    "Delete": $('#chkOrchestrationDelete').is(':checked'),
                    "View": $('#chkOrchestrationView').is(':checked'),
                    "Execute": $('#chkOrchestrationExecute').is(':checked')
                },
                "Admin": {
                    "CreateAndEdit": $('#chkAdminAdd').is(':checked'),
                    "Delete": $('#chkAdminDelete').is(':checked'),
                    "View": $('#chkAdminView').is(':checked')
                },
                "Drift": {
                    "CreateAndEdit": $('#chkDriftAdd').is(':checked'),
                    "Delete": $('#chkDriftDelete').is(':checked'),
                    "View": $('#chkDriftView').is(':checked')
                },
                "ResiliencyReadiness": {
                    "CreateAndEdit": $('#chkResilencyAdd').is(':checked'),
                    "Delete": $('#chkResilencyDelete').is(':checked'),
                    "View": $('#chkResilencyView').is(':checked')
                },
                "Cyber": {
                    "CreateAndEdit": $('#chkCyberAdd').is(':checked'),
                    "Delete": $('#chkCyberDelete').is(':checked'),
                    "View": $('#chkCyberView').is(':checked')
                },
                "CloudConnect": {
                    "View": $('#chkcloudView').is(':checked')
                }
            }
        }
        let jsonString = JSON.stringify(jsonData);
        let result = getCheckedCategories(jsonData);

        let sanitizeaccessArray = ['userRoleName', 'chkMonitor', 'chkManagment', 'chkConfigAdd', 'chkConfigDelete', 'chkConfigView', 'chkManageAdd',
            'chkManageDelete', 'chkManageView', 'chkalertsAdd', 'chkalertsDelete', 'chkalertsView', 'chkReportsAdd', 'chkReportsDelete', 'chkReportsView',
            'chkOrchestrationAdd', 'chkOrchestrationDelete', 'chkOrchestrationView', 'chkOrchestrationExecute', 'chkAdminAdd', 'chkAdminDelete', 'chkAdminView', 'chkCyberAdd', 'chkCyberDelete', 'chkCyberView',
            'chkResilencyAdd', 'chkResilencyDelete', 'chkResilencyView', 'chkDriftAdd', 'chkDriftDelete', 'chkDriftView', 'chkcloudView']
        sanitizeContainer(sanitizeaccessArray);

        if (userRoleName != null && result != false) {
            let token = $('input[name="__RequestVerificationToken"]').val();

            await $.ajax({
                url: RootUrl + "Admin/AccessManager/CreateOrUpdate",
                type: "POST",
                dataType: "json",
                headers: { "RequestVerificationToken": token },
                data: {
                    Id: userRoleRefId,
                    RoleId: userRoleId,
                    RoleName: userRoleName,
                    Properties: jsonString,
                },
                success: function (response) {
                    if (response.success) {
                        notificationAlert("success", response.data)
                        $('#userRoleName').trigger('change');
                        $('#saveFunction').prop("disabled", true);
                        $('#saveFunction').text("Update");
                    }
                },
                error: function (response) {
                    errorNotification(response)
                }
            });
        }
        else if (result == false) {
            event.preventDefault();
        }
    });

    $('.form-check-input').on('change', function () {
        let isChecked = anyCheckboxChecked();
        $("#btnCancel, #saveFunction").prop("disabled", !isChecked);
    });

    $(document).on('change', '.btnAccessAdd', function () {
        if ($(this).is(':checked')) {
            $(this).closest('tr').find('.btnAccessView').prop('checked', true);
        } else {
            $(this).closest('tr').find('.btnAccessDelete').prop('checked', false);
            $(this).closest('tr').find('.btnAccessExcute').prop('checked', false);
        }
    });

    $(document).on('change', '.btnAccessDelete', function () {
        if ($(this).is(':checked')) {
            $(this).closest('tr').find('.btnAccessView').prop('checked', true);
            $(this).closest('tr').find('.btnAccessAdd').prop('checked', true);
        } else {
            $(this).closest('tr').find('.btnAccessExcute').prop('checked', false);
        }
    });

    $(document).on('change', '.btnAccessView', function () {
        let row = $(this).closest('tr');
        if (!$(this).is(':checked')) {
            row.find('input[type="checkbox"]').prop('checked', false);
        }
    });

    $(document).on('change', '.btnAccessExcute', function () {
        let row = $(this).closest('tr');
        if ($(this).is(':checked')) {
            row.find('input[type="checkbox"]').prop('checked', true);
        }
    });
});


//var createPermission = $("#AdminCreate")?.data("create-permission")?.toLowerCase();
//var deletePermission = $("#AdminDelete")?.data("delete-permission")?.toLowerCase();
//let userRole = $('#txtRole').data('role');
//if (createPermission == 'false') {
//    $("#saveFunction").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
//    $("#btnCancel").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
//    //$("#saveFunction").removeClass('btn-primary').addClass('btn-disabled').removeAttr('data-bs-toggle').removeAttr('data-bs-target').css("cursor", "not-allowed")
//    //    && $("#btnCancel").removeClass('#btnCancel').addClass('btn-disabled', createPermission == 'false').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target');
//}

//$(function () {
//    $("#btnCancel").prop("disabled", true);
//    var retrievedData = localStorage.getItem("myDataKey");
//    var newValue = ""
//    $(function () {
//        $('#select2-ddlId-container').removeAttr('title')
//        if (retrievedData != null) {
//            var userDetails = JSON.parse(retrievedData);
//            ddlValue = userDetails?.userRole;
//            newValue = userDetails?.userId;
//            $('#ddlId').val(ddlValue).trigger('change');
//            $('#userRole').val(newValue)
//            $('#textUpdateId').val('')
//            localStorage.removeItem('myDataKey');
//        } else {
//            $('#userRoleName').val(userRole).trigger('change');
//        }

//    });

//    async function GetUserListByUserRole(id, elementId) {

//        var data = {};
//        data.data = id;
//        var userRoleList = await GetAsync(RootUrl + "Admin/AccessManager/GetUserByRole", data, OnError);

//        if (userRoleList.length) {

//            $("#" + elementId).empty();
//            $("#" + elementId).append($('<option>', {
//                //value: "No Users",
//                text: "Select User",
//            }));

//            for (var i = 0; i < userRoleList.length; i++) {

//                $("#" + elementId).append($('<option>', {
//                    value: userRoleList[i].id,
//                    text: userRoleList[i].loginName,

//                }));

//            }
//        }
//    }

//    $('#ddlAccessUserName').on('change', function () {

//        $('#TreeView').empty();
//        var values = $('#ddlAccessUserName :selected').val();
//        GetInfraDetailsByUser(values)
//        var role = $("#userRole").val();
//        updateCheckboxState(role);

//        const errorElement = $('#selectUserName-error');
//        validateDropDown(values, "select the user name", errorElement);

//    });
//    $("#userRoleName").on('change', function () {
//        $("#btnCancel").prop("disabled", true);
//        var ddlName = $("#userRoleName option:selected").text();
//        $("#saveFunction, #btnCancel").toggle(![' Administrator', ' Manager', ' Operator', ' SuperAdmin'].includes(ddlName));
//        $('#userRole').val(ddlName);
//        //var role = ddlName;
//        var username = $('#userNameHide').val();
//        var ddlId = $("#userRoleName option:selected").attr("id")
//        var role = $("#userRole").val().trim();
//        //$('#userRole').val(ddlId);
//        $("#userRole").val(ddlId);
//        updateCheckboxState(role);
//        //handleCheckboxChange('#chkConfigAdd', '#chkConfigDelete', '#chkConfigExecute', '#chkConfigView');
//        //handleCheckboxChange('#chkManageAdd', '#chkManageDelete', '#chkManageExecute', '#chkManageView');
//        //handleCheckboxChange('#chkOrchestrationAdd', '#chkOrchestrationDelete', '#chkOrchestrationExecute', '#chkOrchestrationView');
//        //handleCheckboxChange('#chkReportsAdd', '#chkReportsDelete', '#chkReportsExecute', '#chkReportsView');
//        //handleCheckboxChange('#chkalertsAdd', '#chkalertsDelete', '#chkalertsExecute', '#chkalertsView');
//        //handleCheckboxChange('#chkAdminAdd', '#chkAdminDelete', '#chkAdminExecute', '#chkAdminView');

//        //handleCheckboxChange('#chkResilencyAdd', '#chkResilencyDelete','#chkresilencyExecute', '#chkResilencyView');
//        //handleCheckboxChange('#chkCyberAdd', '#chkCyberDelete', '#chkcyberExecute', '#chkCyberView');
//        //handleCheckboxChange('#chkDriftAdd', '#chkDriftDelete', '#chkdriftExecute', '#chkDriftView');

//        getById(newValue ? newValue.trim() : ddlId);
//        const errorElement = $('#selectUserRole-error');
//        validateDropDown(ddlName, "select role", errorElement);
//        newValue = "";
//        const treeListErrorElement = $('#treeList-error');
//        treeListErrorElement.text('')
//            .removeClass('field-validation-error');
//        if (createPermission == 'false') {
//            $("#saveFunction").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
//            $("#btnCancel").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
//        }
//        // accessPermission();
//        // setTimeout(() => {
//        if (ddlName.trim() === 'SuperAdmin') {
//            $('#chkInfraFull').prop('checked', true)
//            $('#chkInfraCustom').prop('checked', false)
//        } else {
//            $('#chkInfraFull').prop('checked', false)
//            $('#chkInfraCustom').prop('checked', true)
//        }
//        // },700)

//    });


//    $("#ddlAccessUserName").on('change', function () {
//        $('input[type="checkbox"]').prop('checked', true);
//        $('#saveFunction').text('Save');
//        var role = $("#userRole").val();
//        updateCheckboxState(role);
//        var forUpdateUserId = $('#ddlAccessUserName').val();
//        var companyId = $("#ddlAccessUserName option:selected").text();
//        $('#userNameHide').val(companyId);
//        var getvalues = $("#propertieSet").val();
//    }
//    );
//    function updateCheckboxState(role) {

//        if (role == "SuperAdmin" || role == "Administrator") {

//            $('input[type="checkbox"]').prop('disabled', true);
//            $("#saveFunction").prop("disabled", true);
//        }
//        else if (role === "Operator") {

//            // Allow only specific checkboxes for the operator role
//            $('input[type="checkbox"]').prop('checked', false);
//            $('input[type="checkbox"]').prop('disabled', true); // Disable all checkboxes
//            $("#saveFunction").prop("disabled", true);
//        }
//        else if (role === "Manager") {

//            $('input[type="checkbox"]').prop('checked', false);
//            $('input[type="checkbox"]').prop('disabled', true); // Disable all checkboxes
//            $("#saveFunction").prop("disabled", true);
//        }
//        else if (role === "") {
//            $("#saveFunction").prop("disabled", true);
//        }
//        else {
//            $('input[type="checkbox"]').prop('checked', true);
//            $('input[type="checkbox"]').prop('disabled', false);
//            $("#saveFunction").prop("disabled", true);
//        }

//    }
//    function validateDropDown(value, errorMessage, errorElement) {
//        if (!value) {
//            errorElement.text(errorMessage).addClass('field-validation-error');
//            return false;
//        } else {
//            errorElement.text('').removeClass('field-validation-error');
//            return true;
//        }
//    }
//    //Cancle Function
//    $('#btnCancel').on('click', function () {
//        // $("#ddlId").val('');
//        //// Trigger a change event to make the dropdown display the default text
//        $("#userRoleName").on('change');
//        $('#TreeView').empty();
//        $('#saveFunction').text('Save');
//        var role = $("#userRole").val();
//        getById(role)
//        updateCheckboxState(role);
//        clearDropdownError();
//        $("#saveFunction").prop("disabled", true);
//        $(':checkbox').prop("checked", false);
//        $(".AccessManager_Table input[type='checkbox']").prop("checked", false);
//        $("#btnCancel").prop("disabled", true);
//        // debugger
//        var ddlNames = $("#userRoleName option:selected").text();
//        if (ddlNames.trim() === 'SuperAdmin') {
//            $('#chkInfraFull').prop('checked', true)
//            $('#chkInfraCustom').prop('checked', false)
//        } else {
//            $('#chkInfraFull').prop('checked', false)
//            $('#chkInfraCustom').prop('checked', true)
//        }
//        //$('.form-check-input').prop('checked', false);
//        // $('.form-check-input').prop('disabled', true);
//    })

//    function accessPermission() {
//        if (createPermission == 'false') {
//            $("#saveFunction").removeClass('btn-primary').addClass('btn-disabled', createPermission == 'false').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target')
//                && $("#btnCancel").removeClass('#btnCancel').addClass('btn-disabled', createPermission == 'false').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target');
//        }
//    }
//    function clearDropdownError() {
//        $('#selectUserRole-error').hide();
//        $('#selectUserName-error').hide();
//    }

//    //Save Function
//    $('#saveFunction').on('click', function (event) {


//        var form = $("#createAccessManager")

//        var role = $("#userRole").val();
//        $(".roleId").val(role);
//        if (!role || role.toLowerCase() === 'select role') {
//            if (!role || role.toLowerCase() === 'select role') {
//                errorElement = $('#selectUserRole-error');
//                role = '';
//                var isSelectValue = validateDropDown(role, "Select role", errorElement);
//                $('#selectUserRole-error').show();
//            }
//            event.preventDefault();
//            return;
//        }

//        var jsonData = {
//            "Permissions": {
//                "Dashboard": {
//                    "View": true,
//                    "Monitor": $('#chkMonitor').is(':checked'),
//                    "Management": $('#chkManagment').is(':checked')
//                },
//                "Configuration": {
//                    "CreateAndEdit": $('#chkConfigAdd').is(':checked'),
//                    //"Edit": $('#chkConfigEdit').is(':checked'),
//                    "Delete": $('#chkConfigDelete').is(':checked'),
//                    "View": $('#chkConfigView').is(':checked')
//                },
//                "Manage": {
//                    "CreateAndEdit": $('#chkManageAdd').is(':checked'),
//                    //"Edit": $('#chkManageEdit').is(':checked'),
//                    "Delete": $('#chkManageDelete').is(':checked'),
//                    "View": $('.chkManageView').is(':checked')
//                },
//                "Alerts": {
//                    "CreateAndEdit": $('.chkalertsAdd').is(':checked'),
//                    "Delete": $('#chkalertsDelete').is(':checked'),
//                    "View": $('.chkalertsView').is(':checked')
//                },
//                "Reports": {

//                    "CreateAndEdit": $('#chkReportsAdd').is(':checked'),
//                    //"Edit": $('#chkReportsEdit').is(':checked'),
//                    "Delete": $('#chkReportsDelete').is(':checked'),
//                    "View": $('#chkReportsView').is(':checked'),
//                },
//                "Orchestration": {

//                    "CreateAndEdit": $('#chkOrchestrationAdd').is(':checked'),
//                    //"Edit": $('#chkOrchestrationEdit').is(':checked'),
//                    "Delete": $('#chkOrchestrationDelete').is(':checked'),
//                    "View": $('#chkOrchestrationView').is(':checked'),
//                    "Execute": $('#chkOrchestrationExecute').is(':checked')
//                },
//                "Admin": {
//                    "CreateAndEdit": $('#chkAdminAdd').is(':checked'),
//                    //"Edit": $('#chkAdminEdit').is(':checked'),
//                    "Delete": $('#chkAdminDelete').is(':checked'),
//                    "View": $('#chkAdminView').is(':checked')
//                },
//                "Drift": {
//                    "CreateAndEdit": $('#chkDriftAdd').is(':checked'),
//                    //"Edit": $('#chkAdminEdit').is(':checked'),
//                    "Delete": $('#chkDriftDelete').is(':checked'),
//                    "View": $('#chkDriftView').is(':checked')
//                },
//                "ResiliencyReadiness": {
//                    "CreateAndEdit": $('#chkResilencyAdd').is(':checked'),
//                    //"Edit": $('#chkAdminEdit').is(':checked'),
//                    "Delete": $('#chkResilencyDelete').is(':checked'),
//                    "View": $('#chkResilencyView').is(':checked')
//                },
//                "Cyber": {
//                    "CreateAndEdit": $('#chkCyberAdd').is(':checked'),
//                    //"Edit": $('#chkAdminEdit').is(':checked'),
//                    "Delete": $('#chkCyberDelete').is(':checked'),
//                    "View": $('#chkCyberView').is(':checked')
//                },
//                "CloudConnect": {
//                    "View": $('#chkcloudView').is(':checked')
//                }
//            }
//        }
//        var jsonString = JSON.stringify(jsonData);
//        var result = getCheckedCategories(jsonData);
//        let sanitizeaccessArray = ['createAccessManager', 'userRole', 'chkMonitor', 'chkManagment', 'chkConfigAdd', 'chkConfigDelete', 'chkConfigView', 'chkManageAdd',
//            'chkManageDelete', 'chkManageView', 'chkalertsAdd', 'chkalertsDelete', 'chkalertsView', 'chkReportsAdd', 'chkReportsDelete', 'chkReportsView',
//            'chkOrchestrationAdd', 'chkOrchestrationDelete', 'chkOrchestrationView', 'chkOrchestrationExecute', 'chkAdminAdd', 'chkAdminDelete', 'chkAdminView', 'chkCyberAdd', 'chkCyberDelete', 'chkCyberView',
//            'chkResilencyAdd', 'chkResilencyDelete', 'chkResilencyView', 'chkDriftAdd', 'chkDriftDelete', 'chkDriftView','chkcloudView']
//        sanitizeContainer(sanitizeaccessArray)
//        $('#propertieSet').val(jsonString);
//        if (role != null && result != false) {

//            form.trigger('submit');
//        }
//        else if (result == false) {
//            event.preventDefault();
//        }

//    });
//    //function anyCheckboxChecked() {
//    //       return $('.form-check-input').is(':checked');
//    //   }
//    // $('#saveFunction').prop('disabled', true);

//    //   $('.form-check-input').on('change',function () {

//    //       if (anyCheckboxChecked()) {
//    //           $('#saveFunction').prop('disabled', false);
//    //       } else {
//    //           $('#saveFunction').prop('disabled', true);
//    //       }
//    //   });

//    function anyCheckboxChecked() {
//        return $('.form-check-input').is(':checked');
//    }
//    $('#saveFunction').prop('disabled', true);

//    $('.form-check-input').on('change', function () {
//        if (anyCheckboxChecked()) {
//            $("#btnCancel").prop("disabled", false);
//            $('#saveFunction').prop('disabled', false);
//        } else {
//            $('#saveFunction').prop('disabled', true);
//            $("#btnCancel").prop("disabled", true);
//        }
//    });


//    function getCheckedCategories(data) {
//        var checkedCategories = [];
//        var count = 0;
//        // Check if any checkbox within a category is checked
//        $.each(data.Permissions, function (category, permissions) {
//            var categoryChecked = Object.values(permissions).some(function (checkbox) {
//                return checkbox;
//            });

//            if (categoryChecked) {
//                count++;
//                //checkedCategories.push(category);
//            }
//        });
//        const errorElement = $('#treeList-error');
//        if (count == 0) {
//            errorElement.text('Please check atleast one checkbox')
//                .addClass('field-validation-error');
//            return false;
//        }
//        else {
//            errorElement.text('')
//                .removeClass('field-validation-error');
//        }
//        return true;
//    }
//    $(document).on('change', '.btnAccessAdd', function () {
//        var $viewCheckbox = $(this).closest('tr').find('.btnAccessView');
//        var $deleteCheckbox = $(this).closest('tr').find('.btnAccessDelete');
//        var $excuteCheckbox = $(this).closest('tr').find('.btnAccessExcute');
//        if ($(this).is(':checked')) {
//            $viewCheckbox.prop('checked', true);
//        } else {
//            $deleteCheckbox.prop('checked', false);
//            $excuteCheckbox.prop('checked', false);
//        }
//    });
//    $(document).on('change', '.btnAccessDelete', function () {
//        var $viewCheckbox = $(this).closest('tr').find('.btnAccessView');
//        var $addCheckbox = $(this).closest('tr').find('.btnAccessAdd');
//        var $excuteCheckbox = $(this).closest('tr').find('.btnAccessExcute');
//        if ($(this).is(':checked')) {
//            $viewCheckbox.prop('checked', true);
//            $addCheckbox.prop('checked', true);
//        } else {
//            $excuteCheckbox.prop('checked', false);
//        }
//    });
//    $(document).on('change', '.btnAccessView', function () {
//        var $row = $(this).closest('tr');
//        if (!$(this).is(':checked')) {
//            $row.find('input[type="checkbox"]').prop('checked', false);
//        }
//    });
//    $(document).on('change', '.btnAccessExcute', function () {
//        var $row = $(this).closest('tr');
//        if ($(this).is(':checked')) {
//            $row.find('input[type="checkbox"]').prop('checked', true);
//        }
//    });

//    //function handleCheckboxChange(addSelector, deleteSelector, executeSelector, viewSelector) {

//    //    function handleChange() {
//    //        var isAddChecked = $(addSelector).is(':checked');
//    //        var isDeleteChecked = $(deleteSelector).is(':checked');
//    //        var isExecuteChecked = $(executeSelector).is(':checked');

//    //        var anyChecked = isAddChecked || isDeleteChecked || isExecuteChecked;

//    //        if (anyChecked) {
//    //            $(viewSelector).prop('checked', true).prop('disabled', false);
//    //        } else {
//    //            $(viewSelector).prop('checked', false).prop('disabled', false);
//    //        }
//    //    }

//    //    if (addSelector) $(addSelector).on('change', handleChange);
//    //    if (deleteSelector) $(deleteSelector).on('change', handleChange);
//    //    if (executeSelector) $(executeSelector).on('change', handleChange);
//    //}

//    //handleCheckboxChange('#chkConfigAdd', '#chkConfigDelete', '#chkConfigExecute', '#chkConfigView');
//    //handleCheckboxChange('#chkManageAdd', '#chkManageDelete', '#chkManageExecute', '#chkManageView');
//    //handleCheckboxChange('#chkOrchestrationAdd', '#chkOrchestrationDelete', '#chkOrchestrationExecute', '#chkOrchestrationView');
//    //handleCheckboxChange('#chkReportsAdd', '#chkReportsDelete', '#chkReportsExecute', '#chkReportsView');
//    //handleCheckboxChange('#chkalertsAdd', '#chkalertsDelete', '#chkalertsExecute', '#chkalertsView');
//    //handleCheckboxChange('#chkAdminAdd', '#chkAdminDelete', '#chkAdminExecute', '#chkAdminView');
//    //handleCheckboxChange('#chkResilencyAdd', '#chkResilencyDelete','#chkresilencyExecute', '#chkResilencyView');
//    //handleCheckboxChange('#chkCyberAdd', '#chkCyberDelete', '#chkcyberExecute', '#chkCyberView');
//    //handleCheckboxChange('#chkDriftAdd', '#chkDriftDelete', '#chkdriftExecute', '#chkDriftView');

//    function getById(forUpdateUserRole) {
//        if (forUpdateUserRole !== null && typeof forUpdateUserRole !== 'undefined') {
//            $.ajax({
//                type: "GET",
//                url: RootUrl + "Admin/AccessManager/GetRoleDetails",
//                //url: RootUrl + "Admin/AccessManager/GetDetails",// Replace with your API endpoint URL
//                data: {
//                    role: forUpdateUserRole
//                },
//                dataType: "json",

//                success: function (data) {
//                    if (data != null) {

//                        var textUpdateId = data.id;
//                        $('#textUpdateId').val(textUpdateId);
//                        if (textUpdateId != null)
//                            $('#saveFunction').text('Update');

//                        // Handle the retrieved data in the success callback


//                        if (data.properties != null) {
//                            let roleName = data.roleName
//                            var jsonDataStore = JSON.parse(data.properties);

//                            $.each(jsonDataStore.Permissions, function (section, actions) {
//                                $.each(actions, function (action, isChecked) {
//                                    var checkboxId = section + action;
//                                    if (section == "Dashboard") {
//                                        if (action == "Monitor") {
//                                            $('.chkMonitor').prop('checked', isChecked ? true : false);
//                                        }
//                                        else if (action == "Management") {
//                                            $('.chkmanagment').prop('checked', isChecked ? true : false);
//                                        }
//                                    }
//                                    else if (section == "Configuration") {
//                                        if (action == "CreateAndEdit") {
//                                            $('.chkConfigAdd').prop('checked', isChecked ? true : false);
//                                        }

//                                        //else if (action == "Edit") {
//                                        //    $('.chkConfigEdit').prop('checked', isChecked ? true : false);
//                                        //}
//                                        else if (action == "Delete") {
//                                            $('.chkConfigDelete').prop('checked', isChecked ? true : false);
//                                        }
//                                        else if (action == "View") {
//                                            $('.chkConfigView').prop('checked', isChecked ? true : false);
//                                        }
//                                        //if ($('.chkConfigAdd').is(':checked') || $('.chkConfigDelete').is(':checked')) {
//                                        //    $('.chkConfigView').prop('disabled', true);
//                                        //} else
//                                        if (!roleName === "SuperAdmin" || !roleName === "Operator" || !roleName === "Manager" || !roleName === "Administrator") {
//                                            $('.chkConfigView').prop('disabled', false);
//                                        }

//                                    }
//                                    else if (section == "Manage") {
//                                        if (action == "CreateAndEdit") {
//                                            $('.chkManageAdd').prop('checked', isChecked ? true : false);
//                                        }
//                                        //else if (action == "Edit") {
//                                        //    $('.chkManageEdit').prop('checked', isChecked ? true : false);
//                                        //}
//                                        else if (action == "Delete") {
//                                            $('.chkManageDelete').prop('checked', isChecked ? true : false);
//                                        }
//                                        else if (action == "View") {
//                                            $('.chkManageView').prop('checked', isChecked ? true : false);
//                                        }
//                                        //if ($('.chkManageAdd').is(':checked') || $('.chkManageDelete').is(':checked')) {
//                                        //    $('.chkManageView').prop('disabled', true);
//                                        //} else
//                                            if (!roleName === "SuperAdmin" || !roleName === "Operator" || !roleName === "Manager" || !roleName === "Administrator") {
//                                            $('.chkManageView').prop('disabled', false);
//                                           }

//                                    }
//                                    else if (section == "Alerts") {
//                                        if (action == "CreateAndEdit") {
//                                            $('.chkalertsAdd').prop('checked', isChecked ? true : false);
//                                        }
//                                        else if (action == "Delete") {
//                                            $('.chkalertsDelete').prop('checked', isChecked ? true : false);
//                                        }
//                                        else if (action == "View") {
//                                            $('.chkalertsView').prop('checked', isChecked ? true : false);
//                                        }
//                                        //if ($('.chkalertsAdd').is(':checked') || $('.chkalertsDelete').is(':checked')) {
//                                        //    $('.chkalertsView').prop('disabled', true);
//                                        //} else
//                                            if (!roleName === "SuperAdmin" || !roleName === "Operator" || !roleName === "Manager" || !roleName === "Administrator") {
//                                            $('.chkalertsView').prop('disabled', false);
//                                        }

//                                    }
//                                    else if (section == "Reports") {
//                                        if (action == "CreateAndEdit") {
//                                            $('.chkReportsAdd').prop('checked', isChecked ? true : false);
//                                        }
//                                        //else if (action == "Edit") {
//                                        //    $('.chkReportsEdit').prop('checked', isChecked ? true : false);
//                                        //}
//                                        else if (action == "Delete") {
//                                            $('.chkReportsDelete').prop('checked', isChecked ? true : false);
//                                        }
//                                        else if (action == "View") {
//                                            $('.chkReportsView').prop('checked', isChecked ? true : false);
//                                        }
//                                        //if ($('.chkReportsAdd').is(':checked') || $('.chkReportsDelete').is(':checked')) {
//                                        //    $('.chkReportsView').prop('disabled', true);
//                                        //} else
//                                        if (!roleName === "SuperAdmin" || !roleName === "Operator" || !roleName === "Manager" || !roleName === "Administrator") {
//                                            $('.chkReportsView').prop('disabled', false);
//                                        }
//                                    }
//                                    else if (section == "Orchestration") {
//                                        if (action == "CreateAndEdit") {
//                                            $('.chkOrchestrationAdd').prop('checked', isChecked ? true : false);
//                                        }
//                                        //else if (action == "Edit") {
//                                        //    $('.chkOrchestrationEdit').prop('checked', isChecked ? true : false);
//                                        //}
//                                        else if (action == "Delete") {
//                                            $('.chkOrchestrationDelete').prop('checked', isChecked ? true : false);
//                                        }
//                                        else if (action == "View") {
//                                            $('.chkOrchestrationView').prop('checked', isChecked ? true : false);
//                                        }
//                                        else if (action == "Execute") {
//                                            $('.chkOrchestrationExecute').prop('checked', isChecked ? true : false);
//                                        }
//                                        //if ($('.chkOrchestrationAdd').is(':checked') || $('.chkOrchestrationDelete').is(':checked') || $('.chkOrchestrationExecute').is(':checked')) {
//                                        //    $('.chkOrchestrationView').prop('disabled', true);
//                                        //} else
//                                            if (!roleName === "SuperAdmin" || !roleName === "Operator" || !roleName === "Manager" || !roleName === "Administrator") {
//                                            $('.chkOrchestrationView').prop('disabled', false);
//                                        }

//                                    }
//                                    else if (section == "Admin") {
//                                        if (action == "CreateAndEdit") {
//                                            $('.chkAdminAdd').prop('checked', isChecked ? true : false);
//                                        }
//                                        //else if (action == "Edit") {
//                                        //    $('.chkAdminEdit').prop('checked', isChecked ? true : false);
//                                        //}
//                                        else if (action == "Delete") {
//                                            $('.chkAdminDelete').prop('checked', isChecked ? true : false);
//                                        }
//                                        else if (action == "View") {
//                                            $('.chkAdminView').prop('checked', isChecked ? true : false);
//                                        }
//                                        //if ($('.chkAdminAdd').is(':checked') || $('.chkAdminDelete').is(':checked')) {
//                                        //    $('.chkAdminView').prop('disabled', true);
//                                        //} else
//                                            if (!roleName === "SuperAdmin" || !roleName === "Operator" || !roleName === "Manager" || !roleName === "Administrator") {
//                                            $('.chkAdminView').prop('disabled', false);
//                                        }

//                                    }

//                                    else if (section == "Drift") {
//                                        if (action == "CreateAndEdit") {
//                                            $('.chkDriftAdd').prop('checked', isChecked ? true : false);
//                                        }
//                                        //else if (action == "Edit") {
//                                        //    $('.chkAdminEdit').prop('checked', isChecked ? true : false);
//                                        //}
//                                        else if (action == "Delete") {
//                                            $('.chkDriftDelete').prop('checked', isChecked ? true : false);
//                                        }
//                                        else if (action == "View") {
//                                            $('.chkDriftView').prop('checked', isChecked ? true : false);
//                                        }
//                                        //if ($('.chkAdminAdd').is(':checked') || $('.chkAdminDelete').is(':checked')) {
//                                        //    $('.chkAdminView').prop('disabled', true);
//                                        //} else
//                                        if (!roleName === "SuperAdmin" || !roleName === "Operator" || !roleName === "Manager" || !roleName === "Administrator") {
//                                            $('.chkDriftView').prop('disabled', false);
//                                        }

//                                    }
//                                    else if (section == "ResiliencyReadiness") {
//                                        if (action == "CreateAndEdit") {
//                                            $('.chkResilencyAdd').prop('checked', isChecked ? true : false);
//                                        }
//                                        //else if (action == "Edit") {
//                                        //    $('.chkAdminEdit').prop('checked', isChecked ? true : false);
//                                        //}
//                                        else if (action == "Delete") {
//                                            $('.chkResilencyDelete').prop('checked', isChecked ? true : false);
//                                        }
//                                        else if (action == "View") {
//                                            $('.chkResilencyView').prop('checked', isChecked ? true : false);
//                                        }
//                                        //if ($('.chkAdminAdd').is(':checked') || $('.chkAdminDelete').is(':checked')) {
//                                        //    $('.chkAdminView').prop('disabled', true);
//                                        //} else
//                                        if (!roleName === "SuperAdmin" || !roleName === "Operator" || !roleName === "Manager" || !roleName === "Administrator") {
//                                            $('.chkResilencyView').prop('disabled', false);
//                                        }

//                                    }

//                                    else if (section == "Cyber") {
//                                        if (action == "CreateAndEdit") {
//                                            $('.chkCyberAdd').prop('checked', isChecked ? true : false);
//                                        }
//                                        //else if (action == "Edit") {
//                                        //    $('.chkAdminEdit').prop('checked', isChecked ? true : false);
//                                        //}
//                                        else if (action == "Delete") {
//                                            $('.chkCyberDelete').prop('checked', isChecked ? true : false);
//                                        }
//                                        else if (action == "View") {
//                                            $('.chkCyberView').prop('checked', isChecked ? true : false);
//                                        }
//                                        //if ($('.chkAdminAdd').is(':checked') || $('.chkAdminDelete').is(':checked')) {
//                                        //    $('.chkAdminView').prop('disabled', true);
//                                        //} else
//                                        if (!roleName === "SuperAdmin" || !roleName === "Operator" || !roleName === "Manager" || !roleName === "Administrator") {
//                                            $('.chkCyberView').prop('disabled', false);
//                                        }

//                                    }

//                                    else if (section == "CloudConnect") {
//                                        if (action == "View") {
//                                            $('.chkcloudView').prop('checked', isChecked ? true : false);
//                                        }
//                                        //if ($('.chkAdminAdd').is(':checked') || $('.chkAdminDelete').is(':checked')) {
//                                        //    $('.chkAdminView').prop('disabled', true);
//                                        //} else
//                                        if (!roleName === "SuperAdmin" || !roleName === "Operator" || !roleName === "Manager" || !roleName === "Administrator") {
//                                            $('.chkcloudView').prop('disabled', false);
//                                        }

//                                    }
//                                });
//                            });
//                        }
//                    }
//                    else {
//                        $('#saveFunction').text('Save');
//                    }
//                },
//                error: function () {

//                }
//            });
//        }

//    }

//    async function GetInfraDetailsByUser(id) {
//        var data = {};
//        data.data = id;
//        var userRoleList = await GetAsync(RootUrl + "Admin/AccessManager/GetUserInfraByUser", data, OnError);
//        var jsonresult = userRoleList.properties;

//        var result = JSON.parse(userRoleList.properties)
//        if (result.length !== 0) {
//            result.assignedBusinessServices.forEach(function (result) {
//                var workflowView = "<details>"
//                workflowView += "<summary id=" + result.id + " parentId=''>" + result.name + "</summary>"
//                if (result.assignedBusinessFunctions.length != 0) {
//                    result.assignedBusinessFunctions.forEach(function (result) {
//                        workflowView += "<details>"
//                        workflowView += "<summary id=" + result.id + " parentId=" + result.name + ">" + result.name + "</summary>"
//                        if (result.assignedInfraObjects.length != 0) {
//                            result.assignedInfraObjects.forEach(function (result) {
//                                //workflowView += "<details>"
//                                //workflowView += "<summary id=" + result.id + " parentId=" + result.name + ">" + result.name + "</summary>"
//                                workflowView += "<div class='d-grid'>"
//                                workflowView += "<span role='button' id=" + result.id + " parentId=" + result.name + ">" + result.name + "</span>"
//                                workflowView += "</div>"

//                                //if (jsonresult1.length != 0) {
//                                //    jsonresult2.children.forEach(function (jsonresult2) {
//                                //        workflowView += "<div class='d-grid'>"
//                                //        workflowView += "<span role='button' id=" + jsonresult2.id + " parentId=" + jsonresult1.parentId + ">" + jsonresult2.title + "</span>"
//                                //        workflowView += "</div>"
//                                //    })
//                                //}
//                                //workflowView += "</details>"
//                            })
//                        }
//                        workflowView += "</details>"
//                    })
//                }
//                workflowView += "</details>"

//                $("#TreeView").append(workflowView)
//            })
//        }
//    }
//})
