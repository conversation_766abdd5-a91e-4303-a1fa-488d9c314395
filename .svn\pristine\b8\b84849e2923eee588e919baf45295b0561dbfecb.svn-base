using ContinuityPatrol.Application.Features.DrReady.Commands.Create;
using ContinuityPatrol.Application.Features.DrReady.Commands.Delete;
using ContinuityPatrol.Application.Features.DrReady.Commands.Update;
using ContinuityPatrol.Application.Features.DrReady.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DrReady.Queries.GetDrReadyByBusinessServiceId;
using ContinuityPatrol.Domain.ViewModels.DrReadyModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DrReadyFixture : IDisposable
{
    public CreateDrReadyCommand CreateDrReadyCommand { get; }
    public CreateDrReadyResponse CreateDrReadyResponse { get; }
    public UpdateDrReadyCommand UpdateDrReadyCommand { get; }
    public UpdateDrReadyResponse UpdateDrReadyResponse { get; }
    public DeleteDrReadyCommand DeleteDrReadyCommand { get; }
    public DeleteDrReadyResponse DeleteDrReadyResponse { get; }
    public GetDrReadyDetailQuery GetDrReadyDetailQuery { get; }
    public DrReadyDetailVm DrReadyDetailVm { get; }
    public GetDrReadyByBusinessServiceIdQuery GetDrReadyByBusinessServiceIdQuery { get; }
    public DrReadyByBusinessServiceIdVm DrReadyByBusinessServiceIdVm { get; }

    public DrReadyFixture()
    {
        var fixture = new Fixture();

        // Configure fixture for enterprise DR Ready scenarios
        fixture.Customize<CreateDrReadyCommand>(c => c
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise Critical Business Service")
            .With(x => x.TotalBusinessFunction, 25)
            .With(x => x.BFAvailable, 23)
            .With(x => x.BFImpact, 2)
            .With(x => x.BFDRReady, 20)
            .With(x => x.BFDRNotReady, 5)
            .With(x => x.TotalInfraObject, 150)
            .With(x => x.InfraAvailable, 145)
            .With(x => x.InfraImpact, 5)
            .With(x => x.InfraDRReady, 130)
            .With(x => x.InfraDRNotReady, 20));

        fixture.Customize<CreateDrReadyResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise DR Ready status created successfully!")
            .With(x => x.Success, true));

        fixture.Customize<UpdateDrReadyCommand>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise Updated Business Service")
            .With(x => x.TotalBusinessFunction, 30)
            .With(x => x.BFAvailable, 28)
            .With(x => x.BFImpact, 2)
            .With(x => x.BFDRReady, 25)
            .With(x => x.BFDRNotReady, 5)
            .With(x => x.TotalInfraObject, 180)
            .With(x => x.InfraAvailable, 175)
            .With(x => x.InfraImpact, 5)
            .With(x => x.InfraDRReady, 160)
            .With(x => x.InfraDRNotReady, 20));

        fixture.Customize<UpdateDrReadyResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise DR Ready status updated successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DeleteDrReadyResponse>(c => c
            .With(x => x.Message, "Enterprise DR Ready status deleted successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DrReadyDetailVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceName, "Enterprise Detail Business Service")
            .With(x => x.TotalBusinessFunction, 35)
            .With(x => x.BFAvailable, 33)
            .With(x => x.BFImpact, 2)
            .With(x => x.BFDRReady, 30)
            .With(x => x.BFDRNotReady, 5)
            .With(x => x.TotalInfraObject, 200)
            .With(x => x.InfraAvailable, 195)
            .With(x => x.InfraImpact, 5)
            .With(x => x.InfraDRReady, 180)
            .With(x => x.InfraDRNotReady, 20));

        fixture.Customize<DrReadyByBusinessServiceIdVm>(c => c
            .With(x => x.TotalBusinessFunction, 40)
            .With(x => x.BFAvailable, 38)
            .With(x => x.BFImpact, 2)
            .With(x => x.BFDRReady, 35)
            .With(x => x.BFDRNotReady, 5)
            .With(x => x.TotalInfraObject, 220)
            .With(x => x.InfraAvailable, 215)
            .With(x => x.InfraImpact, 5)
            .With(x => x.InfraDRReady, 200)
            .With(x => x.InfraDRNotReady, 20));

        // Create instances
        CreateDrReadyCommand = fixture.Create<CreateDrReadyCommand>();
        CreateDrReadyResponse = fixture.Create<CreateDrReadyResponse>();
        
        UpdateDrReadyCommand = fixture.Create<UpdateDrReadyCommand>();
        UpdateDrReadyResponse = fixture.Create<UpdateDrReadyResponse>();
        
        DeleteDrReadyCommand = new DeleteDrReadyCommand { Id = Guid.NewGuid().ToString() };
        DeleteDrReadyResponse = fixture.Create<DeleteDrReadyResponse>();
        
        GetDrReadyDetailQuery = new GetDrReadyDetailQuery { Id = Guid.NewGuid().ToString() };
        DrReadyDetailVm = fixture.Create<DrReadyDetailVm>();
        
        GetDrReadyByBusinessServiceIdQuery = new GetDrReadyByBusinessServiceIdQuery { BusinessServiceId = Guid.NewGuid().ToString() };
        DrReadyByBusinessServiceIdVm = fixture.Create<DrReadyByBusinessServiceIdVm>();
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
