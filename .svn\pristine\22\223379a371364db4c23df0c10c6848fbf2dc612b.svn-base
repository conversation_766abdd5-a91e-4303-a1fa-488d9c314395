﻿namespace ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Commands.Create;

public class CreateBusinessServiceHealthStatusCommandHandler : IRequestHandler<CreateBusinessServiceHealthStatusCommand,
    CreateBusinessServiceHealthStatusResponse>
{
    private readonly IBusinessServiceHealthStatusRepository _businessServiceHealthRepository;
    private readonly IMapper _mapper;

    public CreateBusinessServiceHealthStatusCommandHandler(
        IBusinessServiceHealthStatusRepository businessServiceHealthRepository, IMapper mapper)
    {
        _businessServiceHealthRepository = businessServiceHealthRepository;
        _mapper = mapper;
    }

    public async Task<CreateBusinessServiceHealthStatusResponse> Handle(
        CreateBusinessServiceHealthStatusCommand request, CancellationToken cancellationToken)
    {
        var businessServiceHealth = _mapper.Map<Domain.Entities.BusinessServiceHealthStatus>(request);

        businessServiceHealth = await _businessServiceHealthRepository.AddAsync(businessServiceHealth);

        var response = new CreateBusinessServiceHealthStatusResponse
        {
            Message = Message.Create(nameof(Domain.Entities.BusinessServiceHealthStatus),
                businessServiceHealth.ReferenceId),
            Id = businessServiceHealth.ReferenceId
        };

        return response;
    }
}