﻿using ContinuityPatrol.Application.Features.ImpactActivity.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.ImpactActivity.Commands;

public class DeleteImpactActivityTests : IClassFixture<ImpactActivityFixture>
{
    private readonly ImpactActivityFixture _impactActivityFixture;

    private readonly Mock<IImpactActivityRepository> _mockImpactActivityRepository;

    private readonly DeleteImpactActivityCommandHandler _handler;

    public DeleteImpactActivityTests(ImpactActivityFixture impactActivityFixture)
    {
        _impactActivityFixture = impactActivityFixture;

        Mock<IPublisher> mockPublisher = new();

        _mockImpactActivityRepository =
            ImpactActivityRepositoryMocks.DeleteImpactActivityRepository(_impactActivityFixture.ImpactActivities);
        _handler = new DeleteImpactActivityCommandHandler(_mockImpactActivityRepository.Object,
            mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_Success_When_Delete_ImpactActivity()
    {
        var result = await _handler.Handle(new DeleteImpactActivityCommand { Id = _impactActivityFixture.ImpactActivities[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteImpactActivityResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Return_IsActive_False_When_Delete_ImpactActivity()
    {
        var result = await _handler.Handle(new DeleteImpactActivityCommand { Id = _impactActivityFixture.ImpactActivities[0].ReferenceId }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_IsActive_False_When_GetByReferenceIdAsyncMethod_DeleteImpactActivity()
    {
        await _handler.Handle(new DeleteImpactActivityCommand { Id = _impactActivityFixture.ImpactActivities[0].ReferenceId }, CancellationToken.None);

        var impactMaster = await _mockImpactActivityRepository.Object.GetByReferenceIdAsync(_impactActivityFixture.ImpactActivities[0].ReferenceId);

        impactMaster.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidImpactActivityId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteImpactActivityCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteImpactActivityCommand { Id = _impactActivityFixture.ImpactActivities[0].ReferenceId }, CancellationToken.None);

        _mockImpactActivityRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockImpactActivityRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.ImpactActivity>()), Times.Once);
    }
}