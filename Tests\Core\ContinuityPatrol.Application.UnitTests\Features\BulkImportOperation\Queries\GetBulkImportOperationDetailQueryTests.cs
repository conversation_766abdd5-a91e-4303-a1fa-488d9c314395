using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperation.Queries;

public class GetBulkImportOperationDetailQueryTests : IClassFixture<BulkImportOperationFixture>
{
    private readonly BulkImportOperationFixture _bulkImportOperationFixture;
    private readonly Mock<IBulkImportOperationRepository> _mockBulkImportOperationRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetBulkImportOperationDetailsQueryHandler _handler;

    public GetBulkImportOperationDetailQueryTests(BulkImportOperationFixture bulkImportOperationFixture)
    {
        _bulkImportOperationFixture = bulkImportOperationFixture;

        _mockBulkImportOperationRepository = BulkImportOperationRepositoryMocks.CreateQueryBulkImportOperationRepository(_bulkImportOperationFixture.BulkImportOperations);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<BulkImportOperationDetailVm>(It.IsAny<Domain.Entities.BulkImportOperation>()))
            .Returns((Domain.Entities.BulkImportOperation entity) => new BulkImportOperationDetailVm
            {
                Id = entity.ReferenceId,
                CompanyId = entity.CompanyId,
                UserName = entity.UserName,
                Description = entity.Description,
                Status = entity.Status,
                StartTime = entity.StartTime,
                EndTime = entity.EndTime
            });

        _handler = new GetBulkImportOperationDetailsQueryHandler(
            _mockMapper.Object,
            _mockBulkImportOperationRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_BulkImportOperationDetailVm_When_BulkImportOperationExists()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var query = new GetBulkImportOperationDetailQuery { Id = existingOperation.ReferenceId };

        _mockBulkImportOperationRepository
        .Setup(repo => repo.GetByReferenceIdAsync(existingOperation.ReferenceId))
        .ReturnsAsync(existingOperation);

        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(BulkImportOperationDetailVm));
        result.Id.ShouldBe(existingOperation.ReferenceId);
        result.CompanyId.ShouldBe(existingOperation.CompanyId);
        result.UserName.ShouldBe(existingOperation.UserName);
    }
  
    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var query = new GetBulkImportOperationDetailQuery { Id = existingOperation.ReferenceId };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var query = new GetBulkImportOperationDetailQuery { Id = existingOperation.ReferenceId };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<BulkImportOperationDetailVm>(It.IsAny<Domain.Entities.BulkImportOperation>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var query = new GetBulkImportOperationDetailQuery { Id = nonExistentId };

        _mockBulkImportOperationRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportOperation)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationIsInactive()
    {
        // Arrange
        var inactiveOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        inactiveOperation.IsActive = false;
        var query = new GetBulkImportOperationDetailQuery { Id = inactiveOperation.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_MapEntityToViewModel_WithCorrectProperties()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        existingOperation.CompanyId = "TestCompanyId";
        existingOperation.UserName = "TestUser";
        existingOperation.Description = "Test bulk import operation";
        existingOperation.Status = "Pending";
        existingOperation.StartTime = DateTime.Now.AddHours(-1);
        existingOperation.EndTime = DateTime.Now;

        var query = new GetBulkImportOperationDetailQuery { Id = existingOperation.ReferenceId };


        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingOperation.ReferenceId);
        result.CompanyId.ShouldBe("TestCompanyId");
        result.UserName.ShouldBe("TestUser");
        result.Description.ShouldBe("Test bulk import operation");
        result.Status.ShouldBe("Pending");
        result.StartTime.ShouldBe(existingOperation.StartTime);
        result.EndTime.ShouldBe(existingOperation.EndTime);
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var query = new GetBulkImportOperationDetailQuery { Id = testId };

        _mockBulkImportOperationRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_bulkImportOperationFixture.BulkImportOperations.First());

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectViewModelType_When_MappingSuccessful()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        var query = new GetBulkImportOperationDetailQuery { Id = existingOperation.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<BulkImportOperationDetailVm>();
        result.GetType().ShouldBe(typeof(BulkImportOperationDetailVm));
    }

    [Fact]
    public async Task Handle_MapStatusAndTimes_WithCorrectValues()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        existingOperation.Status = "Running";
        existingOperation.StartTime = DateTime.Now.AddHours(-2);
        existingOperation.EndTime = DateTime.Now;

        var query = new GetBulkImportOperationDetailQuery { Id = existingOperation.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Status.ShouldBe("Running");
        result.StartTime.ShouldBe(existingOperation.StartTime);
        result.EndTime.ShouldBe(existingOperation.EndTime);
    }

    [Fact]
    public async Task Handle_MapUserAndCompanyInfo_WithCorrectValues()
    {
        // Arrange
        var existingOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        existingOperation.CompanyId = "Company123";
        existingOperation.UserName = "ProductionUser";
        existingOperation.Description = "Production bulk import";

        var query = new GetBulkImportOperationDetailQuery { Id = existingOperation.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.CompanyId.ShouldBe("Company123");
        result.UserName.ShouldBe("ProductionUser");
        result.Description.ShouldBe("Production bulk import");
    }

    [Fact]
    public async Task Handle_UseGuardAgainstNullOrDeactive_When_EntityValidation()
    {
        // Arrange
        var inactiveOperation = _bulkImportOperationFixture.BulkImportOperations.First();
        inactiveOperation.IsActive = false;
        var query = new GetBulkImportOperationDetailQuery { Id = inactiveOperation.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }
}
