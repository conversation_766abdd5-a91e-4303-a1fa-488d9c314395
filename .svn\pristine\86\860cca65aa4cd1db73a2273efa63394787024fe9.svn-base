using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SvcMsSqlMonitorStatusRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly SvcMsSqlMonitorStatusRepository _repository;
    private readonly SvcMsSqlMonitorStatusFixture _fixture;

    public SvcMsSqlMonitorStatusRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _repository = new SvcMsSqlMonitorStatusRepository(_dbContext);
        _fixture = new SvcMsSqlMonitorStatusFixture();
    }

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnMatchingStatuses_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();

        var status1 = _fixture.CreateSvcMsSqlMonitorStatus(
            type: "MSSQL_REPLICATION",
            infraObjectId: "INFRA_001",
            infraObjectName: "Test MSSQL 1",
            workflowId: "WF_001",
            workflowName: "MSSQL Workflow 1",
            isActive: true
        );
        var status2 = _fixture.CreateSvcMsSqlMonitorStatus(
            type: "MSSQL_REPLICATION",
            infraObjectId: "INFRA_002",
            infraObjectName: "Test MSSQL 2",
            workflowId: "WF_002",
            workflowName: "MSSQL Workflow 2",
            isActive: true
        );
        var status3 = _fixture.CreateSvcMsSqlMonitorStatus(
            type: "MSSQL_BACKUP",
            infraObjectId: "INFRA_003",
            infraObjectName: "Test MSSQL 3",
            workflowId: "WF_003",
            workflowName: "MSSQL Workflow 3",
            isActive: true
        );

        await _repository.AddAsync(status1);
        await _repository.AddAsync(status2);
        await _repository.AddAsync(status3);

        // Act
        var result = await _repository.GetDetailByType("MSSQL_REPLICATION");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, status => Assert.Equal("MSSQL_REPLICATION", status.Type));
        Assert.Contains(result, status => status.InfraObjectName == "Test MSSQL 1");
        Assert.Contains(result, status => status.InfraObjectName == "Test MSSQL 2");
        Assert.DoesNotContain(result, status => status.Type == "MSSQL_BACKUP");
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var status = _fixture.CreateSvcMsSqlMonitorStatus(type: "MSSQL_REPLICATION", isActive: true);
        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetDetailByType("NONEXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldOnlyReturnActiveStatuses()
    {
        // Arrange
        await ClearDatabase();

        var activeStatus = _fixture.CreateSvcMsSqlMonitorStatus(
            type: "MSSQL_REPLICATION",
            infraObjectName: "Active MSSQL",
            isActive: true
        );
        var inactiveStatus = _fixture.CreateSvcMsSqlMonitorStatus(
            type: "MSSQL_REPLICATION",
            infraObjectName: "Inactive MSSQL",
            isActive: false
        );


        await _dbContext.SvcMsSqlMonitorStatus.AddRangeAsync(activeStatus, inactiveStatus);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType("MSSQL_REPLICATION");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Active MSSQL", result[0].InfraObjectName);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnAllProperties()
    {
        // Arrange
        await ClearDatabase();

        var status = _fixture.CreateSvcMsSqlMonitorStatus(
            type: "MSSQL_REPLICATION",
            infraObjectId: "8f6aaef6-2b08-45d1-8915-c194439f4c27",
            infraObjectName: "Test MSSQL Object",
            workflowId: "WF_001",
            workflowName: "Test Workflow",
            properties: "{\"rpo\": \"15\", \"status\": \"running\"}",
            configuredRPO: "15",
            dataLagValue: "5",
            isActive: true
        );

        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetDetailByType("MSSQL_REPLICATION");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var resultStatus = result[0];
        
        Assert.Equal("MSSQL_REPLICATION", resultStatus.Type);
        Assert.Equal("8f6aaef6-2b08-45d1-8915-c194439f4c27", resultStatus.InfraObjectId);
        Assert.Equal("Test MSSQL Object", resultStatus.InfraObjectName);
        Assert.Equal("WF_001", resultStatus.WorkflowId);
        Assert.Equal("Test Workflow", resultStatus.WorkflowName);
        Assert.Equal("{\"rpo\": \"15\", \"status\": \"running\"}", resultStatus.Properties);
        Assert.Equal("15", resultStatus.ConfiguredRPO);
        Assert.Equal("5", resultStatus.DataLagValue);
        Assert.True(resultStatus.IsActive);
        Assert.NotNull(resultStatus.ReferenceId);
        Assert.True(resultStatus.Id > 0);
    }

    [Fact]
    public async Task GetDetailByType_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();

        var status = _fixture.CreateSvcMsSqlMonitorStatus(type: "MSSQL_REPLICATION", isActive: true);
        await _repository.AddAsync(status);

        // Act
        var result1 = await _repository.GetDetailByType("MSSQL_REPLICATION");
        var result2 = await _repository.GetDetailByType("mssql_replication");
        var result3 = await _repository.GetDetailByType("MSSQL_Replication");

        // Assert
        Assert.NotNull(result1);
        Assert.Single(result1);
        
        Assert.NotNull(result2);
        Assert.Empty(result2);
        
        Assert.NotNull(result3);
        Assert.Empty(result3);
    }

    #endregion

    #region GetSvcMsSqlMonitorStatusByInfraObjectId Tests

    [Fact]
    public async Task GetSvcMsSqlMonitorStatusByInfraObjectId_ShouldReturnStatus_WhenInfraObjectIdExists()
    {
        // Arrange
        await ClearDatabase();

        var infraObjectId = "8f6aaef6-2b08-45d1-8915-c194439f4c27";
        var status1 = _fixture.CreateSvcMsSqlMonitorStatus(
            infraObjectId: infraObjectId,
            infraObjectName: "Test MSSQL 1",
            type: "MSSQL_REPLICATION",
            workflowId: "WF_001",
            workflowName: "Test Workflow 1",
            isActive: true
        );
        var status2 = _fixture.CreateSvcMsSqlMonitorStatus(
            infraObjectId: "abea4151-6ee0-4f6b-9fc0-2fa5c961d956",
            infraObjectName: "Test MSSQL 2",
            type: "MSSQL_REPLICATION",
            workflowId: "WF_002",
            workflowName: "Test Workflow 2",
            isActive: true
        );

        await _repository.AddAsync(status1);
        await _repository.AddAsync(status2);

        // Act
        var result = await _repository.GetSvcMsSqlMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal("Test MSSQL 1", result.InfraObjectName);
        Assert.Equal("MSSQL_REPLICATION", result.Type);
        Assert.Equal("WF_001", result.WorkflowId);
        Assert.Equal("Test Workflow 1", result.WorkflowName);
    }

    [Fact]
    public async Task GetSvcMsSqlMonitorStatusByInfraObjectId_ShouldReturnNull_WhenInfraObjectIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var status = _fixture.CreateSvcMsSqlMonitorStatus(infraObjectId: "8f6aaef6-2b08-45d1-8915-c194439f4c27", isActive: true);
        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetSvcMsSqlMonitorStatusByInfraObjectId("abea4151-6ee0-4f6b-9fc0-2fa5c961d956");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetSvcMsSqlMonitorStatusByInfraObjectId_ShouldReturnFirstMatch_WhenMultipleStatusesExist()
    {
        // Arrange
        await ClearDatabase();

        var infraObjectId = "8f6aaef6-2b08-45d1-8915-c194439f4c27";
        var status1 = _fixture.CreateSvcMsSqlMonitorStatus(
            infraObjectId: infraObjectId,
            infraObjectName: "First MSSQL",
            type: "MSSQL_REPLICATION",
            isActive: true
        );
        var status2 = _fixture.CreateSvcMsSqlMonitorStatus(
            infraObjectId: infraObjectId,
            infraObjectName: "Second MSSQL",
            type: "MSSQL_BACKUP",
            isActive: true
        );

        await _repository.AddAsync(status1);
        await _repository.AddAsync(status2);

        // Act
        var result = await _repository.GetSvcMsSqlMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should return the first one found (database order dependent)
        Assert.True(result.InfraObjectName == "First MSSQL" || result.InfraObjectName == "Second MSSQL");
    }

    [Fact]
    public async Task GetSvcMsSqlMonitorStatusByInfraObjectId_ShouldReturnInactiveStatus()
    {
        // Arrange
        await ClearDatabase();

        var infraObjectId = "8f6aaef6-2b08-45d1-8915-c194439f4c27";
        var inactiveStatus = _fixture.CreateSvcMsSqlMonitorStatus(
            infraObjectId: infraObjectId,
            infraObjectName: "Inactive MSSQL",
            type: "MSSQL_REPLICATION",
            isActive: false
        );


        await _dbContext.SvcMsSqlMonitorStatus.AddAsync( inactiveStatus);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetSvcMsSqlMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal("Inactive MSSQL", result.InfraObjectName);
        Assert.False(result.IsActive);
    }



    [Fact]
    public async Task GetSvcMsSqlMonitorStatusByInfraObjectId_ShouldReturnAllProperties()
    {
        // Arrange
        await ClearDatabase();

        var infraObjectId = "8f6aaef6-2b08-45d1-8915-c194439f4c27";
        var status = _fixture.CreateSvcMsSqlMonitorStatus(
            infraObjectId: infraObjectId,
            infraObjectName: "Complete MSSQL Object",
            type: "MSSQL_REPLICATION",
            workflowId: "WF_001",
            workflowName: "Complete Workflow",
            properties: "{\"rpo\": \"30\", \"status\": \"healthy\", \"lastCheck\": \"2024-01-01T12:00:00Z\"}",
            configuredRPO: "30",
            dataLagValue: "8",
            isActive: true
        );

        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetSvcMsSqlMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal("Complete MSSQL Object", result.InfraObjectName);
        Assert.Equal("MSSQL_REPLICATION", result.Type);
        Assert.Equal("WF_001", result.WorkflowId);
        Assert.Equal("Complete Workflow", result.WorkflowName);
        Assert.Equal("{\"rpo\": \"30\", \"status\": \"healthy\", \"lastCheck\": \"2024-01-01T12:00:00Z\"}", result.Properties);
        Assert.Equal("30", result.ConfiguredRPO);
        Assert.Equal("8", result.DataLagValue);
        Assert.True(result.IsActive);
        Assert.NotNull(result.ReferenceId);
        Assert.True(result.Id > 0);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.SvcMsSqlMonitorStatus.RemoveRange(_dbContext.SvcMsSqlMonitorStatus);
        await _dbContext.SaveChangesAsync();
    }
}
