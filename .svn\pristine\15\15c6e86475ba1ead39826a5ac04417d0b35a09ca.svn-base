﻿using ContinuityPatrol.Application.Features.Workflow.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Workflow.Queries;

public class GetWorkflowNameUniqueQueryHandlerTests : IClassFixture<WorkflowFixture>
{
    private readonly WorkflowFixture _workflowFixture;
    private Mock<IWorkflowRepository> _mockWorkflowRepository;
    private readonly GetWorkflowNameUniqueQueryHandler _handler;

    public GetWorkflowNameUniqueQueryHandlerTests(WorkflowFixture workflowFixture)
    {
        _workflowFixture = workflowFixture;
        _mockWorkflowRepository = WorkflowRepositoryMocks.GetWorkflowNameUniqueRepository(_workflowFixture.Workflows);
        _handler = new GetWorkflowNameUniqueQueryHandler(_mockWorkflowRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_WorkflowName_Exist()
    {
        _workflowFixture.Workflows[0].Name = "PR_Site";
        _workflowFixture.Workflows[0].IsActive = true;

        var result = await _handler.Handle(new GetWorkflowNameUniqueQuery { WorkflowId = _workflowFixture.Workflows[0].ReferenceId, WorkflowName = _workflowFixture.Workflows[0].Name }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_WorkflowNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowNameUniqueQuery { WorkflowName = "Testing", WorkflowId = 1.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_WorkflowName_NotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowNameUniqueQuery() { WorkflowName = "Demo", WorkflowId = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsWorkflowNameExist_OneTime()
    {
        await _handler.Handle(new GetWorkflowNameUniqueQuery(), CancellationToken.None);

        _mockWorkflowRepository.Verify(x => x.IsWorkflowNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockWorkflowRepository = WorkflowRepositoryMocks.GetWorkflowEmptyRepository();

        var result = await _handler.Handle(new GetWorkflowNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}