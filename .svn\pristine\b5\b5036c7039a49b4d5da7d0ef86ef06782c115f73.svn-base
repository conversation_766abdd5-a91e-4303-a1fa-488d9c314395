using ContinuityPatrol.Application.Features.DriftResourceSummary.Commands.Create;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Commands.Update;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Queries.GetDetail;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.DriftResourceSummaryModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DriftResourceSummaryFixture : IDisposable
{
    public List<DriftResourceSummaryListVm> DriftResourceSummaryListVm { get; set; }
    public List<DriftResourceSummary> DriftResourceSummaries { get; set; }
    public DriftResourceSummaryDetailVm DriftResourceSummaryDetailVm { get; set; }
    public CreateDriftResourceSummaryCommand CreateDriftResourceSummaryCommand { get; set; }
    public CreateDriftResourceSummaryResponse CreateDriftResourceSummaryResponse { get; set; }
    public UpdateDriftResourceSummaryCommand UpdateDriftResourceSummaryCommand { get; set; }
    public UpdateDriftResourceSummaryResponse UpdateDriftResourceSummaryResponse { get; set; }
    public DeleteDriftResourceSummaryCommand DeleteDriftResourceSummaryCommand { get; set; }
    public DeleteDriftResourceSummaryResponse DeleteDriftResourceSummaryResponse { get; set; }
    public PaginatedResult<DriftResourceSummaryListVm> DriftResourceSummaryPaginatedResult { get; set; }
    public DriftResourceSummaryDetailVm DriftResourceSummaryByInfraObjectIdVm { get; set; }

    public DriftResourceSummaryFixture()
    {
        DriftResourceSummaryListVm = AutoDriftResourceSummaryFixture.Create<List<DriftResourceSummaryListVm>>();
        DriftResourceSummaryDetailVm = AutoDriftResourceSummaryFixture.Create<DriftResourceSummaryDetailVm>();
        CreateDriftResourceSummaryCommand = AutoDriftResourceSummaryFixture.Create<CreateDriftResourceSummaryCommand>();
        CreateDriftResourceSummaryResponse = AutoDriftResourceSummaryFixture.Create<CreateDriftResourceSummaryResponse>();
        UpdateDriftResourceSummaryCommand = AutoDriftResourceSummaryFixture.Create<UpdateDriftResourceSummaryCommand>();
        UpdateDriftResourceSummaryResponse = AutoDriftResourceSummaryFixture.Create<UpdateDriftResourceSummaryResponse>();
        DeleteDriftResourceSummaryCommand = AutoDriftResourceSummaryFixture.Create<DeleteDriftResourceSummaryCommand>();
        DeleteDriftResourceSummaryResponse = AutoDriftResourceSummaryFixture.Create<DeleteDriftResourceSummaryResponse>();
        DriftResourceSummaries = AutoDriftResourceSummaryFixture.Create<List<DriftResourceSummary>>();
        DriftResourceSummaryPaginatedResult = AutoDriftResourceSummaryFixture.Create<PaginatedResult<DriftResourceSummaryListVm>>();
        DriftResourceSummaryByInfraObjectIdVm = AutoDriftResourceSummaryFixture.Create<DriftResourceSummaryDetailVm>();
    }

    public Fixture AutoDriftResourceSummaryFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateDriftResourceSummaryCommand>(p => p.ParameterName, 10));
            fixture.Customize<CreateDriftResourceSummaryCommand>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString));
            fixture.Customize<CreateDriftResourceSummaryCommand>(c => c.With(b => b.InfraObjectId, Guid.NewGuid().ToString));
            fixture.Customize<CreateDriftResourceSummaryCommand>(c => c.With(b => b.TypeId, Guid.NewGuid().ToString));
            fixture.Customize<CreateDriftResourceSummaryCommand>(c => c.With(b => b.TotalCount, 100));
            fixture.Customize<CreateDriftResourceSummaryCommand>(c => c.With(b => b.ConflictCount, 5));
            fixture.Customize<CreateDriftResourceSummaryCommand>(c => c.With(b => b.NonConflictCount, 95));
            fixture.Customize<CreateDriftResourceSummaryCommand>(c => c.With(b => b.IsConflict, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateDriftResourceSummaryCommand>(p => p.ParameterName, 10));
            fixture.Customize<UpdateDriftResourceSummaryCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<UpdateDriftResourceSummaryCommand>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString));
            fixture.Customize<UpdateDriftResourceSummaryCommand>(c => c.With(b => b.InfraObjectId, Guid.NewGuid().ToString));
            fixture.Customize<UpdateDriftResourceSummaryCommand>(c => c.With(b => b.TypeId, Guid.NewGuid().ToString));
            fixture.Customize<UpdateDriftResourceSummaryCommand>(c => c.With(b => b.TotalCount, 150));
            fixture.Customize<UpdateDriftResourceSummaryCommand>(c => c.With(b => b.ConflictCount, 3));
            fixture.Customize<UpdateDriftResourceSummaryCommand>(c => c.With(b => b.NonConflictCount, 147));
            fixture.Customize<UpdateDriftResourceSummaryCommand>(c => c.With(b => b.IsConflict, true));

            fixture.Customize<DeleteDriftResourceSummaryCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString));

            fixture.Customize<CreateDriftResourceSummaryResponse>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<CreateDriftResourceSummaryResponse>(c => c.With(b => b.Success, true));

            fixture.Customize<UpdateDriftResourceSummaryResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.Success, true)
                .With(b => b.Message, "DriftResourceSummary updated successfully"));

            fixture.Customize<DeleteDriftResourceSummaryResponse>(c => c
                .With(b => b.Success, true)
                .With(b => b.IsActive, false)
                .With(b => b.Message, "DriftResourceSummary deleted successfully"));

            fixture.Customize<DriftResourceSummary>(c => c.With(b => b.IsActive, true));
            fixture.Customize<DriftResourceSummary>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DriftResourceSummaryListVm>(p => p.ParameterName, 10));
            fixture.Customize<DriftResourceSummaryListVm>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<DriftResourceSummaryListVm>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString));
            fixture.Customize<DriftResourceSummaryListVm>(c => c.With(b => b.InfraObjectId, Guid.NewGuid().ToString));
            fixture.Customize<DriftResourceSummaryListVm>(c => c.With(b => b.TypeId, Guid.NewGuid().ToString));
            fixture.Customize<DriftResourceSummaryListVm>(c => c.With(b => b.TotalCount, 100));
            fixture.Customize<DriftResourceSummaryListVm>(c => c.With(b => b.ConflictCount, 5));
            fixture.Customize<DriftResourceSummaryListVm>(c => c.With(b => b.NonConflictCount, 95));
            fixture.Customize<DriftResourceSummaryListVm>(c => c.With(b => b.IsConflict, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DriftResourceSummaryDetailVm>(p => p.EntityName, 10));
            fixture.Customize<DriftResourceSummaryDetailVm>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<DriftResourceSummaryDetailVm>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString));
            fixture.Customize<DriftResourceSummaryDetailVm>(c => c.With(b => b.InfraObjectId, Guid.NewGuid().ToString));
            fixture.Customize<DriftResourceSummaryDetailVm>(c => c.With(b => b.TypeId, Guid.NewGuid().ToString));
            fixture.Customize<DriftResourceSummaryDetailVm>(c => c.With(b => b.TotalCount, 100));
            fixture.Customize<DriftResourceSummaryDetailVm>(c => c.With(b => b.ConflictCount, 5));
            fixture.Customize<DriftResourceSummaryDetailVm>(c => c.With(b => b.NonConflictCount, 95));
            fixture.Customize<DriftResourceSummaryDetailVm>(c => c.With(b => b.IsConflict, true));

           

            fixture.Customize<PaginatedResult<DriftResourceSummaryListVm>>(c => c.With(b => b.Succeeded, true));
            fixture.Customize<PaginatedResult<DriftResourceSummaryListVm>>(c => c.With(b => b.PageSize, 10));
            fixture.Customize<PaginatedResult<DriftResourceSummaryListVm>>(c => c.With(b => b.CurrentPage, 1));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}
