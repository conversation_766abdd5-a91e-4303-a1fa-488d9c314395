using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class UserRoleFixture : IDisposable
{
    public List<UserRole> UserRolePaginationList { get; set; }
    public List<UserRole> UserRoleList { get; set; }
    public UserRole UserRoleDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public UserRoleFixture()
    {
        var fixture = new Fixture();

        UserRoleList = fixture.Create<List<UserRole>>();

        UserRolePaginationList = fixture.CreateMany<UserRole>(20).ToList();

        UserRoleDto = fixture.Create<UserRole>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
