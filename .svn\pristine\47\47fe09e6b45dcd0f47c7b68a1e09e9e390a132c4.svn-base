using ContinuityPatrol.Application.Features.InfraMaster.Commands.Create;
using ContinuityPatrol.Application.Features.InfraMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.InfraMaster.Commands.Update;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetList;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.InfraMasterModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class InfraMasterService : BaseService,IInfraMasterService
{
    public InfraMasterService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<InfraMasterListVm>> GetInfraMasterList()
    {
        Logger.LogDebug("Get All InfraMasters");

        return await Mediator.Send(new GetInfraMasterListQuery());
    }

    public async Task<InfraMasterDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "InfraMaster Id");

        Logger.LogDebug($"Get InfraMaster Detail by Id '{id}'");

        return await Mediator.Send(new GetInfraMasterDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateInfraMasterCommand createInfraMasterCommand)
    {
        Logger.LogDebug($"Create InfraMaster '{createInfraMasterCommand}'");

        return await Mediator.Send(createInfraMasterCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateInfraMasterCommand updateInfraMasterCommand)
    {
        Logger.LogDebug($"Update InfraMaster '{updateInfraMasterCommand}'");

        return await Mediator.Send(updateInfraMasterCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "InfraMaster Id");

        Logger.LogDebug($"Delete InfraMaster Details by Id '{id}'");

        return await Mediator.Send(new DeleteInfraMasterCommand { Id = id });
    }
     #region NameExist
 public async Task<bool> IsInfraMasterNameExist(string name, string? id)
 {
     Guard.Against.NullOrWhiteSpace(name, "InfraMaster Name");

     Logger.LogDebug($"Check Name Exists Detail by InfraMaster Name '{name}' and Id '{id}'");

     return await Mediator.Send(new GetInfraMasterNameUniqueQuery { Name = name, Id = id });
 }
    #endregion

     #region Paginated
public async Task<PaginatedResult<InfraMasterListVm>> GetPaginatedInfraMasters(GetInfraMasterPaginatedListQuery query)
{
    Logger.LogDebug("Get Searching Details in InfraMaster Paginated List");

    return await Mediator.Send(query);
}
     #endregion
}
