﻿using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetWorkflowInfraObjectByInfraObjectId;
using ContinuityPatrol.Domain.ViewModels.GroupPolicyModel;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.ResiliencyReadiness.Controllers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.ResiliencyReadiness.Controller;

public class ManageResilienceReadinessControllerTests
{
    private readonly Mock<IPublisher> _mockPublisher = new();
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<ILogger<ManageResilienceReadinessController>> _mockLogger = new();
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private ManageResilienceReadinessController _controller;

    public ManageResilienceReadinessControllerTests()
    {
        Initialize();
    }
    public void Initialize()
    {
        _controller = new ManageResilienceReadinessController(
            _mockPublisher.Object,
            _mockMapper.Object,
            _mockLogger.Object,
            _mockDataProvider.Object
        );
        _controller.ControllerContext.HttpContext = new DefaultHttpContext();
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
    }

    [Fact]
    public async Task List_ReturnsViewResult_WithInfraObjectSchedulerViewModel()
    {
        var infraObjects = new List<InfraObjectListVm> {
            new InfraObjectListVm { Id = "1", Name = "Test", DRReady = true }
        };

        var paginatedList = new PaginatedResult<InfraObjectSchedulerListVm>();
        _mockDataProvider.Setup(p => p.DrReady.GetDrReadyPaginatedList(It.IsAny<GetInfraObjectSchedulerPaginatedListQuery>()))
            .ReturnsAsync(paginatedList);

        _mockDataProvider.Setup(p => p.InfraObject.GetInfraObjectList())
            .ReturnsAsync(infraObjects);

        _mockDataProvider.Setup(p => p.Workflow.GetWorkflowNames())
            .ReturnsAsync(new List<WorkflowNameVm> { });

        _mockDataProvider.Setup(p => p.GroupPolicy.GetGroupPolicies())
            .ReturnsAsync(new List<GroupPolicyListVm> { });

        var result = await _controller.List() as ViewResult;

        Assert.NotNull(result);
        var model = result.Model as InfraObjectSchedulerViewModel;
        Assert.NotNull(model);
        Assert.Equal(paginatedList, model.PaginationDrReady);
        Assert.Single(model.InfraObjectNameVms);
           
    }

    [Fact]
    public async Task GetWorkflowNameByInfraID_ReturnsJsonWithSuccess()
    {
        var infraId = "1";
        var workflowNames = new List<WorkflowInfraObjectByInfraObjectIdVm> { };
        _mockDataProvider.Setup(p => p.WorkflowInfraObject.GetWorkflowByInfraObjectId(infraId))
            .ReturnsAsync(workflowNames);

        var result = await _controller.GetWorkflowNameByInfraId(infraId) as JsonResult;

        var json = JsonConvert.SerializeObject(result);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetWorkflowNameByInfraID_ThrowsException_ReturnsJsonWithError()
    {
        var infraId = "1";
        _mockDataProvider.Setup(p => p.WorkflowInfraObject.GetWorkflowByInfraObjectId(infraId))
            .Throws(new Exception("Test Exception"));

        var result = await _controller.GetWorkflowNameByInfraId(infraId) as JsonResult;

        var json = JsonConvert.SerializeObject(result);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task CreateOrUpdate_CreateSuccess_ReturnsRedirectToList()
    {
        var viewModel = new Fixture().Create<InfraObjectSchedulerViewModel>();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("id", "22");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;
        var command = new CreateInfraObjectSchedulerCommand();

        var model = new InfraObjectSchedulerViewModel { Id = null };
        _mockMapper.Setup(m => m.Map<CreateInfraObjectSchedulerCommand>(model))
            .Returns(command);

        var response = new BaseResponse { Success = true, Message = "Success" };
        _mockDataProvider.Setup(p => p.DrReady.CreateAsync(command))
            .ReturnsAsync(response);

        var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

        Assert.NotNull(result);
        Assert.Equal("List", result.ActionName);
    }

    [Fact]
    public async Task CreateOrUpdate_ValidationError_ReturnsRedirectToList()
    {
        var viewModel = new Fixture().Create<InfraObjectSchedulerViewModel>();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("id", "22");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;
        var command = new CreateInfraObjectSchedulerCommand();

        var model = new InfraObjectSchedulerViewModel { Id = null };
        _mockMapper.Setup(m => m.Map<CreateInfraObjectSchedulerCommand>(model))
            .Returns(command);

        var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

        Assert.NotNull(result);
        Assert.Equal("List", result.ActionName);
    }

    [Fact]
    public async Task Reset_DrReady_Success_ReturnsJsonWithSuccess()
    {
        var command = new UpdateInfraObjectSchedulerStatusCommand();
        var response = new BaseResponse { Success = true };
        _mockDataProvider.Setup(p => p.DrReady.UpdateInfraObjectSchedulerStatus(It.IsAny<UpdateInfraObjectSchedulerStatusCommand>()))
            .ReturnsAsync(response);

        var result = await _controller.ResetManageResilienceReadinessStatus(command) as JsonResult;

        var json = JsonConvert.SerializeObject(result);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task Delete_Success_ReturnsRedirectToList()
    {
        var response = new BaseResponse { Success = true, Message = "Deleted Successfully" };
        _mockDataProvider.Setup(p => p.DrReady.DeleteAsync(It.IsAny<string>()))
            .ReturnsAsync(response);

        var result = await _controller.Delete("1") as RedirectToActionResult;

        Assert.NotNull(result);
        Assert.Equal("List", result.ActionName);
    }
}