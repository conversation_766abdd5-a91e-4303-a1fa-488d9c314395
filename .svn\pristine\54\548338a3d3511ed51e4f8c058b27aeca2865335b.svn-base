﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Form.Events.Lock;

public class FormLockEventHandler : INotificationHandler<FormLockEvent>
{
    private readonly ILogger<FormLockEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FormLockEventHandler(ILogger<FormLockEventHandler> logger, IUserActivityRepository userActivityRepository,
        ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(FormLockEvent formLockEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{(formLockEvent.IsLock ? ActivityType.Lock : ActivityType.Unlock)} {Modules.Form}",
            Entity = Modules.Form.ToString(),
            ActivityType = formLockEvent.IsLock ? ActivityType.Lock.ToString() : ActivityType.Unlock.ToString(),
            ActivityDetails =
                $"Form Builder '{formLockEvent.FormName}' {(formLockEvent.IsLock ? "Locked" : "UnLocked")} successfully."
        };

        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation(
            $"Form Builder '{formLockEvent.FormName}' {(formLockEvent.IsLock ? "Locked" : "UnLocked")} successfully.");
    }
}