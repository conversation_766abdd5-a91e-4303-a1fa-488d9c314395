﻿using ContinuityPatrol.Application.Features.Template.Commands.Create;
using ContinuityPatrol.Application.Features.Template.Commands.Update;
using ContinuityPatrol.Application.Features.Template.Queries.GetByInfraObjectIdandActiontype;
using ContinuityPatrol.Application.Features.Template.Queries.GetByReplicationTypeId;
using ContinuityPatrol.Application.Features.Template.Queries.GetByReplicationTypeIdAndType;
using ContinuityPatrol.Application.Features.Template.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Template.Queries.GetTemplateByInfraObjectId;
using ContinuityPatrol.Domain.ViewModels.TemplateModel;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class TemplateProfile : Profile
{
    public TemplateProfile()
    {
        CreateMap<Template, CreateTemplateCommand>().ReverseMap();
        CreateMap<UpdateTemplateCommand, Template>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<Template, TemplateListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<Template, TemplateDetailVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<Template, TemplateNameVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));


        CreateMap<Template, GetTemplateByReplicationTypeIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<Template, TemplateByInfraObjectIdandActiontypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<Template, GetTemplateByInfraObjectIdVm>().ReverseMap();

        CreateMap<CreateTemplateCommand, TemplateViewModel>().ReverseMap();
        CreateMap<UpdateTemplateCommand, TemplateViewModel>().ReverseMap();

        CreateMap<Server, GetByReplicationTypeIdAndTypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.ServerType));


        CreateMap<Database, GetByReplicationTypeIdAndTypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.DatabaseType));
        CreateMap<PaginatedResult<Template>,PaginatedResult<TemplateListVm>>()
             .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
        CreateMap<ServerView, GetByReplicationTypeIdAndTypeVm>()
           .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
           .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
           .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.ServerType));


        CreateMap<DatabaseView, GetByReplicationTypeIdAndTypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.DatabaseType));
    }
}