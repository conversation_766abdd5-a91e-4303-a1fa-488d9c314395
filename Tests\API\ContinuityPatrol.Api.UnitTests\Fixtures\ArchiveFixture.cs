using ContinuityPatrol.Application.Features.Archive.Commands.Create;
using ContinuityPatrol.Application.Features.Archive.Commands.Update;
using ContinuityPatrol.Application.Features.Archive.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ArchiveModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class ArchiveFixture
{
    public List<ArchiveListVm> ArchiveListVm { get; }
    public ArchiveDetailVm ArchiveDetailVm { get; }
    public CreateArchiveCommand CreateArchiveCommand { get; }
    public UpdateArchiveCommand UpdateArchiveCommand { get; }

    public ArchiveFixture()
    {
        var fixture = new Fixture();

        // Create sample Archive list data
        ArchiveListVm = new List<ArchiveListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                TableNameProperties = "{\"tables\":[\"Users\",\"Logs\",\"Transactions\"]}",
                ArchiveProfileName = "Monthly Data Archive",
                Count = 50000,
                CronExpression = "0 0 1 * *",
                ScheduleTime = "02:00:00",
                ScheduleType = 1,
                BackUpType = "Full",
                Type = "Database",
                ClearBackup = "Yes",
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Archive-Node-01"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                TableNameProperties = "{\"tables\":[\"AuditLogs\",\"SystemEvents\"]}",
                ArchiveProfileName = "Weekly Audit Archive",
                Count = 25000,
                CronExpression = "0 0 * * 0",
                ScheduleTime = "03:30:00",
                ScheduleType = 2,
                BackUpType = "Incremental",
                Type = "Log",
                ClearBackup = "No",
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Archive-Node-02"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                TableNameProperties = "{\"tables\":[\"Reports\",\"Analytics\"]}",
                ArchiveProfileName = "Daily Report Archive",
                Count = 10000,
                CronExpression = "0 0 * * *",
                ScheduleTime = "01:00:00",
                ScheduleType = 3,
                BackUpType = "Differential",
                Type = "Report",
                ClearBackup = "Yes",
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Archive-Node-03"
            }
        };

        // Create detailed Archive data
        ArchiveDetailVm = new ArchiveDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            TableNameProperties = "{\"tables\":[\"CriticalData\",\"BusinessRecords\"],\"retention\":\"7years\"}",
            ArchiveProfileName = "Enterprise Critical Data Archive",
            Count = 100000,
            CronExpression = "0 0 15 * *",
            ScheduleTime = "04:00:00",
            ScheduleType = 1,
            BackUpType = "Full",
            Type = "Enterprise",
            ClearBackup = "No",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "Enterprise-Archive-Node"
        };

        // Create command for creating Archive
        CreateArchiveCommand = new CreateArchiveCommand
        {
            CompanyId = Guid.NewGuid().ToString(),
            TableNameProperties = "{\"tables\":[\"NewTable1\",\"NewTable2\"]}",
            ArchiveProfileName = "New Archive Profile",
            Count = 5000,
            CronExpression = "0 0 * * *",
            ScheduleTime = "02:30:00",
            ScheduleType = 1,
            BackUpType = "Full",
            Type = "Standard",
            ClearBackup = "Yes",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "New-Archive-Node"
        };

        // Create command for updating Archive
        UpdateArchiveCommand = new UpdateArchiveCommand
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            TableNameProperties = "{\"tables\":[\"UpdatedTable1\",\"UpdatedTable2\"]}",
            ArchiveProfileName = "Updated Archive Profile",
            Count = 7500,
            CronExpression = "0 0 2 * *",
            ScheduleTime = "03:00:00",
            ScheduleType = 2,
            BackUpType = "Incremental",
            Type = "Updated",
            ClearBackup = "No",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "Updated-Archive-Node"
        };
    }
}
