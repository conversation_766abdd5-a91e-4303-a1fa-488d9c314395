﻿using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Workflow.Queries.GetWorkflowActionById;

public class
    GetWorkflowActionByIdQueryHandler : IRequestHandler<GetWorkflowActionByIdQuery, List<GetWorkflowActionByIdVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowActionResultRepository _workflowActionResultRepository;
    private readonly IWorkflowExecutionTempRepository _workflowExecutionTempRepository;
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IWorkflowRunningActionRepository _workflowRunningActionRepository;
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IServerRepository _serverRepository;
    private readonly IReplicationRepository _replicationRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;

    public GetWorkflowActionByIdQueryHandler(IMapper mapper, IWorkflowRepository workflowRepository,
        IWorkflowActionResultRepository workflowActionResultRepository,
        IWorkflowExecutionTempRepository workflowExecutionTempRepository,
        IWorkflowRunningActionRepository workflowRunningActionRepository, IDatabaseRepository databaseRepository,
        IServerRepository serverRepository, IReplicationRepository replicationRepository,
        IInfraObjectRepository infraObjectRepository)
    {
        _mapper = mapper;
        _workflowRepository = workflowRepository;
        _workflowActionResultRepository = workflowActionResultRepository;
        _workflowExecutionTempRepository = workflowExecutionTempRepository;
        _workflowRunningActionRepository = workflowRunningActionRepository;
        _databaseRepository = databaseRepository;
        _serverRepository = serverRepository;
        _replicationRepository = replicationRepository;
        _infraObjectRepository = infraObjectRepository;
    }

    public async Task<List<GetWorkflowActionByIdVm>> Handle(GetWorkflowActionByIdQuery request,
        CancellationToken cancellationToken)
    {
        var workflowRunning = await _workflowRunningActionRepository.GetWorkflowRunningActionsByOperationGroupId(request.WorkflowOperationGroupId);

        if (workflowRunning.Any())
        {
            var actionMap = _mapper.Map<List<GetWorkflowActionByIdVm>>(workflowRunning);

            var workflowRunningActions = await _workflowActionResultRepository.GetWorkflowActionResultByGroupId(request.WorkflowOperationGroupId);

            var actionResultDictionary = workflowRunningActions.DistinctBy(x=>x.StepId).ToDictionary(x => x.StepId);

            actionMap = actionMap.Select(jsonObject =>
            {
                if (actionResultDictionary.TryGetValue(jsonObject.StepId, out var actionResult))
                {
                    jsonObject.WorkflowOperationGroupId = actionResult.WorkflowOperationGroupId;
                    jsonObject.ActionId = actionResult.ReferenceId;
                    jsonObject.WorkflowActionName = actionResult.WorkflowActionName;
                    jsonObject.Status = actionResult.Status;
                    jsonObject.StartTime = actionResult.StartTime;
                    jsonObject.EndTime = actionResult.EndTime;
                    jsonObject.Message = actionResult.Message;
                }

                return jsonObject;
            }).ToList();

            return actionMap;
        }


        var workflow = await _workflowRepository.GetByReferenceIdAsync(request.WorkflowId);

        Guard.Against.NullOrDeactive(workflow, nameof(Domain.Entities.Workflow),
            new NotFoundException(nameof(Domain.Entities.Workflow), request.WorkflowId));

        var workflowActionResult =
            await _workflowActionResultRepository.GetWorkflowActionResultByWorkflowOperationGroupId(
                request.WorkflowOperationGroupId);

        var workflowActionList = new List<GetWorkflowActionByIdVm>();

        var customProperties = await _workflowExecutionTempRepository.GetByWorkflowIdAsync(request.WorkflowId);

        var properties = customProperties?.Properties ?? workflow.Properties;

        //var properties = workflowOperationGroup?.IsCustom == true
        //    ? (await _workflowExecutionTempRepository.GetByWorkflowIdAsync(request.WorkflowId))?.Properties ?? workflow.Properties
        //    : workflow.Properties;

        if (properties.IsNullOrWhiteSpace())
            throw new InvalidException("Workflow properties is empty");

        var deserializedNodes = JsonConvert.DeserializeObject<dynamic>(properties)?.SelectToken("nodes");

        if (deserializedNodes == null)
            return new List<GetWorkflowActionByIdVm>();

        foreach (var node in deserializedNodes)
        {
            await ProcessNode(node, workflowActionList, workflowActionResult);
        }


        if (request.WorkflowOperationGroupId.IsNotNullOrWhiteSpace())
        {
            var actionMap = _mapper.Map<List<WorkflowRunningAction>>(workflowActionList);

            actionMap.ForEach(x => x.WorkflowId = request.WorkflowId);
            actionMap.ForEach(x => x.WorkflowOperationGroupId = request.WorkflowOperationGroupId);

            _ = await _workflowRunningActionRepository.AddRange(actionMap) as List<WorkflowRunningAction>;
        }

        return workflowActionList;
    }

    public async Task ProcessNode(dynamic node, List<GetWorkflowActionByIdVm> actionList, List<Domain.Entities.WorkflowActionResult> results, string groupId = null, string groupName = null)
    {
        // Create action
        var action = await CreateActionVm(node, groupId, groupName);

        // Apply result mapping
        var mappedAction = await ApplyResultMapping(action, results);
        if (!string.IsNullOrWhiteSpace(mappedAction.WorkflowActionName))
            actionList.Add(mappedAction);

        // Process children
        var children = node?.SelectToken("children");
        if (children != null)
        {
            foreach (var child in children)
            {
                await ProcessNode(child, actionList, results, mappedAction.GroupId, mappedAction.GroupName);
            }
        }

        // Process group actions
        var groupActions = node?.SelectToken("groupActions");
        if (groupActions != null)
        {
            foreach (var groupAction in groupActions)
            {
                var groupIdStr = node.SelectToken("groupId")?.ToString() ?? string.Empty;
                var groupNameStr = node.SelectToken("groupName")?.ToString() ?? string.Empty;

                await ProcessNode(groupAction, actionList, results, groupIdStr, groupNameStr);
            }
        }
    }

    public async Task<GetWorkflowActionByIdVm> CreateActionVm(dynamic node, string groupId, string groupName)
    {
        var actionInfo = node?.SelectToken("actionInfo");

        var properties = actionInfo?.SelectToken("properties");

        var json = await ProcessJsonAsync(properties!.ToString());

        var result = new GetWorkflowActionByIdVm
        {
            StepId = node.SelectToken("stepId"),
            WorkflowActionName = actionInfo.SelectToken("actionName"),
            Icon = actionInfo.SelectToken("icon"),
            IsParallel = actionInfo.SelectToken("IsParallel") ?? false,
            IsGroup = actionInfo.SelectToken("IsGroup") ?? false,
            IsCustom = actionInfo.SelectToken("isCustom") ?? false,
            GroupId = groupId,
            GroupName = groupName,
            Type = actionInfo.SelectToken("type"),
            Properties = json
        };

        return result;
    }

    public Task<GetWorkflowActionByIdVm> ApplyResultMapping(GetWorkflowActionByIdVm action, List<Domain.Entities.WorkflowActionResult> results)
    {
        var result = results.FirstOrDefault(r => r.StepId == action.StepId);
        if (result == null) return Task.FromResult(action);
        var mappedAction = _mapper.Map<GetWorkflowActionByIdVm>(result);
        mappedAction.Icon ??= action.Icon;
        mappedAction.IsParallel = action.IsParallel || mappedAction.IsParallel;
        mappedAction.IsGroup = action.IsGroup || mappedAction.IsGroup;
        mappedAction.IsCustom = action.IsCustom || mappedAction.IsCustom;
        mappedAction.GroupId = action.GroupId;
        mappedAction.GroupName = action.GroupName;
        mappedAction.Type = action.Type;
        return Task.FromResult(mappedAction);
    }

    public async Task<string> ProcessJsonAsync(string json)
    { 
        var properties = JObject.Parse(json);

        var propertyList = properties.Properties().ToList();

        foreach (var property in propertyList)
        {
            var key = property.Name;
            var value = property.Value.ToString();

            if (key.Contains("Server", StringComparison.OrdinalIgnoreCase))
            {
               var server = await _serverRepository.GetByReferenceIdAsync(value);
               if (server != null)
                   properties[key] = server.Name;
            }
            else if (key.Contains("Database", StringComparison.OrdinalIgnoreCase))
            {
               var database = await _databaseRepository.GetByReferenceIdAsync(value);

               if (database != null)
                   properties[key] = database.Name;
            }
            else if(key.Contains("Replication", StringComparison.OrdinalIgnoreCase))
            {
               var replication = await _replicationRepository.GetByReferenceIdAsync(value);

               if (replication != null)
                   properties[key] = replication.Name;
            }
            else if (key.Contains("InfraObject", StringComparison.OrdinalIgnoreCase))
            {
               var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(value);

               if (infraObject != null)
                   properties[key] = infraObject.Name;
            }
        }

        return JsonConvert.SerializeObject(properties,Formatting.None);
    }

}