using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Create;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Update;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.HacmpClusterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class HacmpClusterProfile : Profile
{
    public HacmpClusterProfile()
    {
        CreateMap<HacmpCluster, HacmpClusterListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<HacmpCluster, HacmpClusterDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<HacmpCluster, CreateHacmpClusterCommand>().ReverseMap();
        CreateMap<HacmpCluster, HacmpClusterViewModel>().ReverseMap();

        CreateMap<CreateHacmpClusterCommand, HacmpClusterViewModel>().ReverseMap();
        CreateMap<UpdateHacmpClusterCommand, HacmpClusterViewModel>().ReverseMap();

        CreateMap<UpdateHacmpClusterCommand, HacmpCluster>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<PaginatedResult<HacmpCluster>, PaginatedResult<HacmpClusterListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}