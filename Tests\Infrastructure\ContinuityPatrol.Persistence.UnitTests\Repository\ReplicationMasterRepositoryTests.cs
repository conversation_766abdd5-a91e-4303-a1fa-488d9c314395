using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ReplicationMasterRepositoryTests : IClassFixture<ReplicationMasterFixture>, IDisposable
{
    private readonly ReplicationMasterFixture _replicationMasterFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ReplicationMasterRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public ReplicationMasterRepositoryTests(ReplicationMasterFixture replicationMasterFixture)
    {
        _replicationMasterFixture = replicationMasterFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _repository = new ReplicationMasterRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.ReplicationMasters.RemoveRange(_dbContext.ReplicationMasters);
        await _dbContext.SaveChangesAsync();
    }

    #region GetReplicationNames Tests

    [Fact]
    public async Task GetReplicationNames_ShouldReturnActiveReplicationMasters_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var activeReplicationMasters = new List<ReplicationMaster>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "Active1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "Active2", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "Inactive1", IsActive = false }
        };

        foreach (var master in activeReplicationMasters)
        {
            await _dbContext.ReplicationMasters.AddAsync(master);
            _dbContext.SaveChanges();
        }

        // Act
        var result = await _repository.GetReplicationNames();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.True(r.IsActive));
        Assert.Contains(result, r => r.Name == "Active1");
        Assert.Contains(result, r => r.Name == "Active2");
        Assert.DoesNotContain(result, r => r.Name == "Inactive1");
    }

    [Fact]
    public async Task GetReplicationNames_ShouldReturnActiveReplicationMasters_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var activeReplicationMasters = new List<ReplicationMaster>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "Active1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "Active2", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "Inactive1", IsActive = false }
        };

        foreach (var master in activeReplicationMasters)
        {
            await _dbContext.ReplicationMasters.AddAsync(master);
            _dbContext.SaveChanges(); 
        }

        // Act
        var result = await _repository.GetReplicationNames();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.True(r.IsActive));
        Assert.Contains(result, r => r.Name == "Active1");
        Assert.Contains(result, r => r.Name == "Active2");
    }

    [Fact]
    public async Task GetReplicationNames_ShouldReturnOrderedByName()
    {
        // Arrange
        await ClearDatabase();

        var replicationMasters = new List<ReplicationMaster>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "ZReplication", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "AReplication", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "MReplication", IsActive = true }
        };

        foreach (var master in replicationMasters)
        {
            await _repository.AddAsync(master);
        }

        // Act
        var result = await _repository.GetReplicationNames();

        // Assert
        Assert.Equal(3, result.Count);
        Assert.Equal("AReplication", result[0].Name);
        Assert.Equal("MReplication", result[1].Name);
        Assert.Equal("ZReplication", result[2].Name);
    }

    [Fact]
    public async Task GetReplicationNames_ShouldReturnEmptyList_WhenNoActiveReplicationMasters()
    {
        // Arrange
        await ClearDatabase();

        var inactiveReplicationMasters = new List<ReplicationMaster>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "Inactive1", IsActive = false },
            new() { ReferenceId = Guid.NewGuid().ToString(), Name = "Inactive2", IsActive = false }
        };

        foreach (var master in inactiveReplicationMasters)
        {
            await _dbContext.ReplicationMasters.AddAsync(master);
            _dbContext.SaveChanges();
        }

        // Act
        var result = await _repository.GetReplicationNames();

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetReplicationNames_ShouldReturnOnlyReferenceIdAndName()
    {
        // Arrange
        await ClearDatabase();

        var replicationMaster = new ReplicationMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            InfraMasterId = "InfraId",
            InfraMasterName = "InfraName",
            IsActive = true
        };

        await _repository.AddAsync(replicationMaster);

        // Act
        var result = await _repository.GetReplicationNames();

        // Assert
        Assert.Single(result);
        var returnedMaster = result[0];
        Assert.Equal(replicationMaster.ReferenceId, returnedMaster.ReferenceId);
        Assert.Equal(replicationMaster.Name, returnedMaster.Name);
        // Other properties should be null/default as they're not selected
        Assert.Null(returnedMaster.InfraMasterId);
        Assert.Null(returnedMaster.InfraMasterName);
    }

    #endregion

    #region IsReplicationMasterNameUnique Tests

    [Fact]
    public async Task IsReplicationMasterNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();
        var replicationMaster = new ReplicationMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingName",
            IsActive = true
        };
        await _repository.AddAsync(replicationMaster);

        // Act
        var result = await _repository.IsReplicationMasterNameUnique("ExistingName");

        // Assert
        Assert.True(result); // Method returns true when name exists (opposite of unique)
    }

    [Fact]
    public async Task IsReplicationMasterNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var replicationMaster = new ReplicationMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingName",
            IsActive = true
        };
        await _repository.AddAsync(replicationMaster);

        // Act
        var result = await _repository.IsReplicationMasterNameUnique("NonExistentName");

        // Assert
        Assert.False(result); // Method returns false when name doesn't exist (is unique)
    }

    [Fact]
    public async Task IsReplicationMasterNameUnique_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var replicationMaster = new ReplicationMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "CaseSensitiveName",
            IsActive = true
        };
        await _repository.AddAsync(replicationMaster);

        // Act
        var result1 = await _repository.IsReplicationMasterNameUnique("CaseSensitiveName");
        var result2 = await _repository.IsReplicationMasterNameUnique("casesensitivename");
        var result3 = await _repository.IsReplicationMasterNameUnique("CASESENSITIVENAME");

        // Assert
        Assert.True(result1);   // Exact match should return true
        Assert.False(result2);  // Different case should return false
        Assert.False(result3);  // Different case should return false
    }

    [Fact]
    public async Task IsReplicationMasterNameUnique_ShouldHandleNullName()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReplicationMasterNameUnique(null);

        // Assert
        Assert.False(result); // Should return false when searching for null name
    }

    [Fact]
    public async Task IsReplicationMasterNameUnique_ShouldHandleEmptyName()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReplicationMasterNameUnique("");

        // Assert
        Assert.False(result); // Should return false when searching for empty name
    }

    #endregion

    #region IsReplicationMasterNameExist Tests

    [Fact]
    public async Task IsReplicationMasterNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var replicationMaster = new ReplicationMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingName",
            IsActive = true
        };
        await _repository.AddAsync(replicationMaster);

        // Act
        var result = await _repository.IsReplicationMasterNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsReplicationMasterNameExist_ShouldReturnFalse_WhenNameExistsAndIdMatchesExistingEntity()
    {
        // Arrange
        await ClearDatabase();
        var replicationMaster = new ReplicationMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingName",
            IsActive = true
        };
        await _repository.AddAsync(replicationMaster);

        // Act
        var result = await _repository.IsReplicationMasterNameExist("ExistingName", replicationMaster.ReferenceId);

        // Assert
        Assert.False(result); // Should return false because it's the same entity
    }

    [Fact]
    public async Task IsReplicationMasterNameExist_ShouldReturnTrue_WhenNameExistsAndIdDoesNotMatchExistingEntity()
    {
        // Arrange
        await ClearDatabase();
        var replicationMaster = new ReplicationMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingName",
            IsActive = true
        };
        await _repository.AddAsync(replicationMaster);

        // Act
        var differentId = Guid.NewGuid().ToString();
        var result = await _repository.IsReplicationMasterNameExist("ExistingName", differentId);

        // Assert
        Assert.True(result); // Should return true because it's a different entity with same name
    }

    [Fact]
    public async Task IsReplicationMasterNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReplicationMasterNameExist("NonExistentName", "any-id");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetReplicationMasterByInfraMasterName Tests

    [Fact]
    public async Task GetReplicationMasterByInfraMasterName_ShouldReturnMatchingActiveReplicationMasters()
    {
        // Arrange
        await ClearDatabase();
        var infraMasterName = "TestInfraMaster";

        var replicationMasters = new List<ReplicationMaster>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication1",
                InfraMasterId = "InfraId1",
                InfraMasterName = infraMasterName,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication2",
                InfraMasterId = "InfraId2",
                InfraMasterName = infraMasterName,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication3",
                InfraMasterId = "InfraId3",
                InfraMasterName = "DifferentInfraMaster",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication4",
                InfraMasterId = "InfraId4",
                InfraMasterName = infraMasterName,
                IsActive = false
            }
        };

        foreach (var master in replicationMasters)
        {
            await _dbContext.ReplicationMasters.AddAsync(master);
            _dbContext.SaveChanges();
        }

        // Act
        var result = await _repository.GetReplicationMasterByInfraMasterName(infraMasterName);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.Equal(infraMasterName, r.InfraMasterName));
        Assert.All(result, r => Assert.True(r.IsActive));
        Assert.Contains(result, r => r.Name == "Replication1");
        Assert.Contains(result, r => r.Name == "Replication2");
        Assert.DoesNotContain(result, r => r.Name == "Replication3");
        Assert.DoesNotContain(result, r => r.Name == "Replication4");
    }

    //[Fact]
    //public async Task GetReplicationMasterByInfraMasterName_ShouldHandleWhitespaceAndCaseInsensitive()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var infraMasterName = "  TestInfraMaster  ";

    //    var replicationMaster = new ReplicationMaster
    //    {
    //        ReferenceId = Guid.NewGuid().ToString(),
    //        Name = "TestReplication",
    //        InfraMasterId = "InfraId1",
    //        InfraMasterName = "testinframaster", // Different case
    //        IsActive = true
    //    };

    //    await _dbContext.ReplicationMasters.AddAsync(replicationMaster);
    //    _dbContext.SaveChanges();

    //    // Act
    //    var result = await _repository.GetReplicationMasterByInfraMasterName(infraMasterName);

    //    // Assert
    //    Assert.Single(result);
    //    Assert.Equal("TestReplication", result[0].Name);
    //}

    [Fact]
    public async Task GetReplicationMasterByInfraMasterName_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();

        var replicationMaster = new ReplicationMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            InfraMasterId = "InfraId1",
            InfraMasterName = "DifferentInfraMaster",
            IsActive = true
        };

        await _repository.AddAsync(replicationMaster);

        // Act
        var result = await _repository.GetReplicationMasterByInfraMasterName("NonExistentInfraMaster");

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetReplicationMasterByInfraMasterName_ShouldReturnOnlySpecificProperties()
    {
        // Arrange
        await ClearDatabase();
        var infraMasterName = "TestInfraMaster";

        var replicationMaster = new ReplicationMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            InfraMasterId = "InfraId1",
            InfraMasterName = infraMasterName,
            IsActive = true,
            CreatedBy = "TestUser",
            CreatedDate = DateTime.UtcNow
        };

        await _repository.AddAsync(replicationMaster);

        // Act
        var result = await _repository.GetReplicationMasterByInfraMasterName(infraMasterName);

        // Assert
        Assert.Single(result);
        var returnedMaster = result[0];
        Assert.NotEqual(0, returnedMaster.Id); // Id should be populated
        Assert.Equal(replicationMaster.ReferenceId, returnedMaster.ReferenceId);
        Assert.Equal(replicationMaster.Name, returnedMaster.Name);
        Assert.Equal(replicationMaster.InfraMasterId, returnedMaster.InfraMasterId);
        Assert.Equal(replicationMaster.InfraMasterName, returnedMaster.InfraMasterName);
        // Other properties should be null/default as they're not selected
        Assert.Null(returnedMaster.CreatedBy);
        Assert.Equal(default(DateTime), returnedMaster.CreatedDate);
    }

    [Fact]
    public async Task GetReplicationMasterByInfraMasterName_ShouldHandleNullInfraMasterName()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetReplicationMasterByInfraMasterName(null);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetReplicationMasterByInfraMasterName_ShouldHandleEmptyInfraMasterName()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetReplicationMasterByInfraMasterName("");

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetReplicationMasterByInfraMasterName_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();
        var infraMasterName = "InfraMaster123";

        var replicationMaster = new ReplicationMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            InfraMasterId = "InfraId1",
            InfraMasterName = infraMasterName,
            IsActive = true
        };

        await _dbContext.ReplicationMasters.AddAsync(replicationMaster);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetReplicationMasterByInfraMasterName(infraMasterName);

        // Assert
        Assert.Single(result);
        Assert.Equal("TestReplication", result[0].Name);
    }

    #endregion
}
