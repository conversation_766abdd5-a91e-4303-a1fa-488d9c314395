﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.LoadBalancerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetPaginatedList;

public class GetLoadBalancerPaginatedListQueryHandler : IRequestHandler<GetLoadBalancerPaginatedListQuery,
    PaginatedResult<LoadBalancerListVm>>
{
    private readonly IMapper _mapper;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;

    public GetLoadBalancerPaginatedListQueryHandler(IMapper mapper,
        ILoadBalancerRepository nodeConfigurationRepository)
    {
        _mapper = mapper;
        _nodeConfigurationRepository = nodeConfigurationRepository;
    }

    public async Task<PaginatedResult<LoadBalancerListVm>> Handle(GetLoadBalancerPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new LoadBalancerFilterSpecification(request.SearchString?.Replace(" ",""));
     
        var queryable =await _nodeConfigurationRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var nodeConfiguration = _mapper.Map<PaginatedResult<LoadBalancerListVm>>(queryable);

        return nodeConfiguration;
    }
}