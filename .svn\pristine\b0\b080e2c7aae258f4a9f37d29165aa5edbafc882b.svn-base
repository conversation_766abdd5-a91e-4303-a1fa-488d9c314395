using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ApprovalMatrixUsersRepository : BaseRepository<ApprovalMatrixUsers>, IApprovalMatrixUsersRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public ApprovalMatrixUsersRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<ApprovalMatrixUsers> GetByUserIdAsync(string userId)
    {
        return await Entities.AsNoTracking()
            .FirstOrDefaultAsync(e => e.UserId == userId);
    }


    public async Task<List<ApprovalMatrixUsers>> GetListByApprovalIdsAsync(List<string> approvalIds)
    {
        return await FilterBy(e => approvalIds.Contains(e.UserId))
            .ToListAsync();
    }


    public async Task<PaginatedResult<ApprovalMatrixUsers>> GetApprovalMatrixUserByType(string type, int pageNumber, int pageSize, Specification<ApprovalMatrixUsers> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await Entities
            .AsNoTracking()
            .Specify(productFilterSpec).Where(x=>x.Type.Equals(type))
            .DescOrderById()
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }


    public async Task<bool> IsNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
        {
            return await Entities.AnyAsync(e => e.UserName == name);
        }

        var matchingUsers = await Entities
            .Where(e => e.UserName == name)
            .ToListAsync();

        return matchingUsers.Unique(id);
    }
}
