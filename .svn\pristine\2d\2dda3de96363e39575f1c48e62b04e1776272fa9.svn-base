using ContinuityPatrol.Application.Features.InfraMaster.Commands.Create;
using ContinuityPatrol.Application.Features.InfraMaster.Commands.Update;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.InfraMasterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class InfraMasterService : BaseClient, IInfraMasterService
{
    public InfraMasterService(IConfiguration config, IAppCache cache, ILogger<InfraMasterService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<InfraMasterListVm>> GetInfraMasterList()
    {
        var request = new RestRequest("api/v6/inframasters");

        return await GetFromCache<List<InfraMasterListVm>>(request, "GetInfraMasterList");
    }

    public async Task<BaseResponse> CreateAsync(CreateInfraMasterCommand createInfraMasterCommand)
    {
        var request = new RestRequest("api/v6/inframasters", Method.Post);

        request.AddJsonBody(createInfraMasterCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateInfraMasterCommand updateInfraMasterCommand)
    {
        var request = new RestRequest("api/v6/inframasters", Method.Put);

        request.AddJsonBody(updateInfraMasterCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/inframasters/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<InfraMasterDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/inframasters/{id}");

        return await Get<InfraMasterDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsInfraMasterNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/inframasters/name-exist?inframasterName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<InfraMasterListVm>> GetPaginatedInfraMasters(GetInfraMasterPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/inframasters/paginated-list");

      return await Get<PaginatedResult<InfraMasterListVm>>(request);
  }
   #endregion
}
