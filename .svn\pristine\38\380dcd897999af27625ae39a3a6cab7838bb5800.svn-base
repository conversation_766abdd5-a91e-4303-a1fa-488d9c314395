﻿namespace ContinuityPatrol.Domain.Entities;

public class FormTypeCategory : AuditableEntity
{
    public string Name { get; set; }
    public string FormId { get; set; }
    public string FormName { get; set; }
    public string FormTypeId { get; set; }
    public string FormTypeName { get; set; }
    [Column(TypeName = "NCLOB")] public string Logo { get; set; }
    [Column(TypeName = "NCLOB")] public string Version { get; set; }
    [Column(TypeName = "NCLOB")] public string Properties { get; set; }
    public string FormVersion { get; set; }
}