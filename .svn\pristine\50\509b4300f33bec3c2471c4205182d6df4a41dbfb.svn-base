using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DrReady.Events.Update;

public class DrReadyUpdatedEventHandler : INotificationHandler<DrReadyUpdatedEvent>
{
    private readonly ILogger<DrReadyUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DrReadyUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<DrReadyUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(DrReadyUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} DrReady",
            Entity = "DrReady",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"DrReady '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"DrReady '{updatedEvent.Name}' updated successfully.");
    }
}