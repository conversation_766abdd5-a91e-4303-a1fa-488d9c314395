﻿namespace ContinuityPatrol.Domain.Views;

public class WorkflowView : AuditableEntity
{
    public string CompanyId { get; set; }
    public string WorkflowName { get; set; }
    public string ProfileId { get; set; }
    public string ProfileName { get; set; }
    public string Properties { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string WorkflowType { get; set; }
    public bool IsLock { get; set; }
    public bool IsPublish { get; set; }
    public bool IsVerify { get; set; }
    public bool IsDraft { get; set; }
    public bool IsRunning { get; set; }
    public string Version { get; set; }
    public string ProgressBar { get; set; }
    public string CreatedByUserName { get; set; }
    public string LastExecutionDate { get; set; }
}