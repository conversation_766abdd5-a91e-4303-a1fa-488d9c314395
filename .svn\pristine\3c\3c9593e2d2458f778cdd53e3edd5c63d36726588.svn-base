﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Create;

public class CreateWorkflowProfileInfoCommand : IRequest<CreateWorkflowProfileInfoResponse>
{
    [JsonIgnore] public string CompanyId { get; set; }

    public string ProfileId { get; set; }
    public string ProfileName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public int Index { get; set; }

    [JsonIgnore] public string Status { get; set; }

    [JsonIgnore] public string CurrentActionId { get; set; }

    [JsonIgnore] public string CurrentActionName { get; set; }

    [JsonIgnore] public string Message { get; set; }

    [JsonIgnore] public string ProgressStatus { get; set; }

    [JsonIgnore] public int ConditionalOperation { get; set; }

    public string WorkflowType { get; set; }

    [JsonIgnore] public string ActionMode { get; set; }

    [JsonIgnore] public bool IsLock { get; set; }

    [JsonIgnore] public bool IsPublish { get; set; }

    public override string ToString()
    {
        return $"Name: {ProfileName};";
    }
}