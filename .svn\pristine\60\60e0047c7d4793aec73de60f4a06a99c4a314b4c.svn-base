using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AzureStorageAccountMonitorlogsFixture : IDisposable
{
    public List<AzureStorageAccountMonitorlogs> AzureStorageAccountMonitorlogsPaginationList { get; set; }
    public List<AzureStorageAccountMonitorlogs> AzureStorageAccountMonitorlogsList { get; set; }
    public AzureStorageAccountMonitorlogs AzureStorageAccountMonitorlogsDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public AzureStorageAccountMonitorlogsFixture()
    {
        var fixture = new Fixture();

        AzureStorageAccountMonitorlogsList = fixture.Create<List<AzureStorageAccountMonitorlogs>>();

        AzureStorageAccountMonitorlogsPaginationList = fixture.CreateMany<AzureStorageAccountMonitorlogs>(20).ToList();

        AzureStorageAccountMonitorlogsDto = fixture.Create<AzureStorageAccountMonitorlogs>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
