using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Delete;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Update;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetList;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class DataSyncOptionsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<DataSyncOptionsListVm>>> GetDataSyncs()
    {
        Logger.LogDebug("Get All DataSyncs");

        return Ok(await Mediator.Send(new GetDataSyncOptionsListQuery()));
    }

    [HttpGet("{id}", Name = "GetDataSync")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<DataSyncOptionsDetailVm>> GetDataSyncById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DataSync Id");

        Logger.LogDebug($"Get DataSync Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDataSyncOptionsDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Configuration.View)]
 public async Task<ActionResult<PaginatedResult<DataSyncOptionsListVm>>> GetPaginatedDataSyncs([FromQuery] GetDataSyncOptionsPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in DataSync Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateDataSyncOptionsResponse>> CreateDataSync([FromBody] CreateDataSyncOptionsCommand createDataSyncCommand)
    {
        Logger.LogDebug($"Create DataSync  '{createDataSyncCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDataSync), await Mediator.Send(createDataSyncCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateDataSyncOptionsResponse>> UpdateDataSync([FromBody] UpdateDataSyncOptionsCommand updateDataSyncCommand)
    {
        Logger.LogDebug($"Update DataSync '{updateDataSyncCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateDataSyncCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteDataSyncOptionsResponse>> DeleteDataSync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DataSync Id");

        Logger.LogDebug($"Delete DataSync Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteDataSyncOptionsCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsDataSyncNameExist(string dataSyncName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(dataSyncName, "DataSync Name");

     Logger.LogDebug($"Check Name Exists Detail by DataSync Name '{dataSyncName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetDataSyncOptionsNameUniqueQuery { Name = dataSyncName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


