using ContinuityPatrol.Application.Contracts.Job;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperation.Events.Create;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperation.Commands;

public class CreateBulkImportOperationTests : IClassFixture<BulkImportOperationFixture>
{
    private readonly BulkImportOperationFixture _bulkImportOperationFixture;
    private readonly Mock<IBulkImportOperationRepository> _mockBulkImportOperationRepository;
    private readonly Mock<IBulkImportOperationGroupRepository> _mockBulkImportOperationGroupRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly Mock<IQuartzJobScheduler> _mockScheduleClient;
    private readonly CreateBulkImportOperationCommandHandler _handler;

    public CreateBulkImportOperationTests(BulkImportOperationFixture bulkImportOperationFixture)
    {
        _bulkImportOperationFixture = bulkImportOperationFixture;

        _mockBulkImportOperationRepository = BulkImportOperationRepositoryMocks.CreateBulkImportOperationRepository(_bulkImportOperationFixture.BulkImportOperations);
        _mockBulkImportOperationGroupRepository = BulkImportOperationRepositoryMocks.CreateBulkImportOperationGroupRepository(new List<Domain.Entities.BulkImportOperationGroup>());
        _mockMapper = new Mock<IMapper>();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockPublisher = new Mock<IPublisher>();
        _mockScheduleClient = new Mock<IQuartzJobScheduler>();

        // Setup logged in user service
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(Guid.NewGuid().ToString());

        // Setup mapper
        _mockMapper.Setup(m => m.Map<Domain.Entities.BulkImportOperation>(It.IsAny<CreateBulkImportOperationCommand>()))
            .Returns((CreateBulkImportOperationCommand cmd) => new Domain.Entities.BulkImportOperation
            {
                CompanyId = cmd.CompanyId,
                UserName = cmd.UserName,
                Description = cmd.Description,
                Status = cmd.Status,
                StartTime = cmd.StartTime,
                EndTime = cmd.EndTime
            });

        _handler = new CreateBulkImportOperationCommandHandler(
            _mockMapper.Object,
            _mockBulkImportOperationRepository.Object, _mockPublisher.Object,
            _mockBulkImportOperationGroupRepository.Object,
            _mockLoggedInUserService.Object,
            _mockScheduleClient.Object);
    }

    [Fact]
    public async Task Handle_Return_CreateBulkImportOperationResponse_When_BulkImportOperationCreated()
    {
        // Arrange
        var command = _bulkImportOperationFixture.CreateBulkImportOperationCommand;
        command.BulkImportOperationList = new List<CreateBulkImportOperationListCommand>
        {
           // new CreateBulkImportOperationListCommand { InfraObject = new InfraObject { Name = "TestInfra" } }
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(CreateBulkImportOperationResponse));
        result.Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var command = _bulkImportOperationFixture.CreateBulkImportOperationCommand;
        command.BulkImportOperationList = new List<CreateBulkImportOperationListCommand>();

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.BulkImportOperation>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var command = _bulkImportOperationFixture.CreateBulkImportOperationCommand;
        command.BulkImportOperationList = new List<CreateBulkImportOperationListCommand>();

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<Domain.Entities.BulkImportOperation>(It.IsAny<CreateBulkImportOperationCommand>()), Times.Once);
    }

    [Fact]
    public async Task Handle_SetUserContextProperties_When_BulkImportOperationCreated()
    {
        // Arrange
        var command = _bulkImportOperationFixture.CreateBulkImportOperationCommand;
        command.BulkImportOperationList = new List<CreateBulkImportOperationListCommand>();

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        command.UserName.ShouldBe("TestUser");
        command.CompanyId.ShouldNotBeNullOrEmpty();
        command.Status.ShouldBe("Pending");
        command.StartTime.ShouldBeGreaterThan(DateTime.MinValue);
    }

    [Fact]
    public async Task Handle_CreateBulkImportOperationGroups_When_BulkImportOperationListProvided()
    {
        // Arrange
        var command = _bulkImportOperationFixture.CreateBulkImportOperationCommand;
        command.BulkImportOperationList = new List<CreateBulkImportOperationListCommand>
        {
            //new CreateBulkImportOperationListCommand 
            //{ 
            //    InfraObject = new InfraObject { Name = "TestInfra1" },
            //    ServerList = new List<Server> { new Server() },
            //    DatabaseList = new List<Database>()
            //},
            //new CreateBulkImportOperationListCommand 
            //{ 
            //    InfraObject = new InfraObject { Name = "TestInfra2" },
            //    ServerList = new List<Server>(),
            //    DatabaseList = new List<Database> { new Database() }
            //}
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.AddRangeAsync(It.IsAny<List<Domain.Entities.BulkImportOperationGroup>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PublishEvent_When_BulkImportOperationCreated()
    {
        // Arrange
        var command = _bulkImportOperationFixture.CreateBulkImportOperationCommand;
        command.BulkImportOperationList = new List<CreateBulkImportOperationListCommand>();

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(It.IsAny<BulkImportOperationCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ScheduleJob_When_BulkImportOperationCreated()
    {
        // Arrange
        var command = _bulkImportOperationFixture.CreateBulkImportOperationCommand;
        command.BulkImportOperationList = new List<CreateBulkImportOperationListCommand>();

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockScheduleClient.Verify(x => x.ScheduleJob<BulkImportJob>(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_MapCommandToEntity_WithCorrectProperties()
    {
        // Arrange
        var command = new CreateBulkImportOperationCommand
        {
            Description = "Test bulk import operation",
            BulkImportOperationList = new List<CreateBulkImportOperationListCommand>()
        };

        Domain.Entities.BulkImportOperation capturedEntity = null;
        _mockBulkImportOperationRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.BulkImportOperation>()))
            .Callback<Domain.Entities.BulkImportOperation>(entity => capturedEntity = entity)
            .ReturnsAsync((Domain.Entities.BulkImportOperation entity) => 
            {
                entity.ReferenceId = Guid.NewGuid().ToString();
                return entity;
            });

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEntity.ShouldNotBeNull();
        capturedEntity.UserName.ShouldBe("TestUser");
        capturedEntity.CompanyId.ShouldNotBeNullOrEmpty();
        capturedEntity.Description.ShouldBe("Test bulk import operation");
        capturedEntity.Status.ShouldBe("Pending");
        capturedEntity.StartTime.ShouldBeGreaterThan(DateTime.MinValue);
    }

    [Fact]
    public async Task Handle_CreateResponseWithCorrectMessage_When_BulkImportOperationCreated()
    {
        // Arrange
        var command = _bulkImportOperationFixture.CreateBulkImportOperationCommand;
        command.BulkImportOperationList = new List<CreateBulkImportOperationListCommand>();

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldContain("BulkImportOperation");
        result.Message.ShouldContain("TestUser");
        result.Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_OperationSuccessful()
    {
        // Arrange
        var command = _bulkImportOperationFixture.CreateBulkImportOperationCommand;
        command.BulkImportOperationList = new List<CreateBulkImportOperationListCommand>();

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<CreateBulkImportOperationResponse>();
        result.GetType().ShouldBe(typeof(CreateBulkImportOperationResponse));
    }

    [Fact]
    public async Task Handle_SetReferenceIdInResponse_When_EntityCreated()
    {
        // Arrange
        var command = _bulkImportOperationFixture.CreateBulkImportOperationCommand;
        command.BulkImportOperationList = new List<CreateBulkImportOperationListCommand>();
        var expectedReferenceId = Guid.NewGuid().ToString();

        _mockBulkImportOperationRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.BulkImportOperation>()))
            .ReturnsAsync((Domain.Entities.BulkImportOperation entity) => 
            {
                entity.ReferenceId = expectedReferenceId;
                return entity;
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Id.ShouldBe(expectedReferenceId);
    }
}
