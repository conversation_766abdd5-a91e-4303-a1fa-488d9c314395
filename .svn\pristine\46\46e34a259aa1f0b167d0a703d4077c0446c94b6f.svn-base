﻿using ContinuityPatrol.Application.Features.TeamResource.Events.Create;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamResource.Events
{
    public class CreateTeamResourceEventTests
    {
        private readonly Mock<ILogger<TeamResourceCreatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly TeamResourceCreatedEventHandler _handler;

        public CreateTeamResourceEventTests()
        {
            _mockLogger = new Mock<ILogger<TeamResourceCreatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new TeamResourceCreatedEventHandler(
                _mockUserService.Object,
                _mockUserActivityRepository.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_AddsUserActivityAndLogsInformation_WhenCalled()
        {
            var createdEvent = new TeamResourceCreatedEvent();
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns("user-id");
            _mockUserService.Setup(s => s.LoginName).Returns("user-login");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("/api/team-resource");
            _mockUserService.Setup(s => s.CompanyId).Returns("company-id");
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            await _handler.Handle(createdEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "user-id" &&
                activity.LoginName == "user-login" &&
                activity.RequestUrl == "/api/team-resource" &&
                activity.CompanyId == "company-id" &&
                activity.HostAddress == "***********" &&
                activity.Entity == Modules.TeamResource.ToString() &&
                activity.Action == $"{ActivityType.Create} {Modules.TeamResource}" &&
                activity.ActivityType == ActivityType.Create.ToString() &&
                activity.ActivityDetails == " Team  Resource  created successfully."
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation("Team Resource  created successfully."), Times.Once);
        }

        [Fact]
        public async Task Handle_LogsError_WhenAddAsyncThrowsException()
        {
            var createdEvent = new TeamResourceCreatedEvent();
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns("user-id");
            _mockUserService.Setup(s => s.LoginName).Returns("user-login");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("/api/team-resource");
            _mockUserService.Setup(s => s.CompanyId).Returns("company-id");
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .ThrowsAsync(new System.Exception("Database error"));

            await Assert.ThrowsAsync<System.Exception>(() => _handler.Handle(createdEvent, cancellationToken));

            _mockLogger.Verify(logger => logger.LogInformation("Team Resource  created successfully."), Times.Never);
        }
    }
}
