using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ServerSubTypeRepository : BaseRepository<ServerSubType>, IServerSubTypeRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public ServerSubTypeRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<ServerSubType>> GetServerSubTypeByServerType(string serverSubTypeId)
    {
        return await _dbContext.ServerSubTypes
            .Active()
            .Where(x => x.ServerTypeId.Equals(serverSubTypeId))
            .ToListAsync();
    }
   
    public Task<bool> IsServerSubTypeExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.ServerSubTypes.Any(e => e.Name.Equals(name)))
            : Task.FromResult(_dbContext.ServerSubTypes.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public override async Task<PaginatedResult<ServerSubType>>PaginatedListAllAsync(int pageNumber, int pageSize, Specification<ServerSubType> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await SelectServerSubType(Entities.Specify(productFilterSpec).DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    private IQueryable<ServerSubType> SelectServerSubType(IQueryable<ServerSubType> serverSubType)
    {
        return serverSubType.Select(x=>new ServerSubType{
            Id=x.Id,
            ReferenceId=x.ReferenceId,
            Name=x.Name,
            ServerTypeId=x.ServerTypeId,
            ServerTypeName=x.ServerTypeName,
            Logo=x.Logo
        });

    }
}