using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.CyberComponentMappingModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class CyberComponentMappingFixture
{
    public CreateCyberComponentMappingCommand CreateCyberComponentMappingCommand { get; }
    public UpdateCyberComponentMappingCommand UpdateCyberComponentMappingCommand { get; }
    public DeleteCyberComponentMappingCommand DeleteCyberComponentMappingCommand { get; }
    public CyberComponentMappingListVm CyberComponentMappingListVm { get; }
    public CyberComponentMappingDetailVm CyberComponentMappingDetailVm { get; }

    public CyberComponentMappingFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateCyberComponentMappingCommand>(c => c
            .With(b => b.Name, "Enterprise Database Mapping")
            .With(b => b.Properties, "{\"sourceDatabase\":{\"name\":\"PROD_DB_01\",\"server\":\"db-prod-01.company.com\",\"port\":1521,\"schema\":\"PRODUCTION\",\"connectionString\":\"encrypted\"},\"targetDatabase\":{\"name\":\"DR_DB_01\",\"server\":\"db-dr-01.company.com\",\"port\":1521,\"schema\":\"DISASTER_RECOVERY\",\"connectionString\":\"encrypted\"},\"mappingRules\":{\"tableMapping\":[{\"source\":\"CUSTOMERS\",\"target\":\"CUSTOMERS_DR\"},{\"source\":\"ORDERS\",\"target\":\"ORDERS_DR\"}],\"dataTransformation\":{\"encryption\":\"AES-256\",\"compression\":\"GZIP\"},\"syncFrequency\":\"15min\",\"retentionPolicy\":\"30days\"}}")
            .With(b => b.Version, "1.0.0"));

        fixture.Customize<UpdateCyberComponentMappingCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Updated Enterprise Database Mapping")
            .With(b => b.Properties, "{\"sourceDatabase\":{\"name\":\"PROD_DB_02\",\"server\":\"db-prod-02.company.com\",\"port\":1521,\"schema\":\"PRODUCTION_V2\",\"connectionString\":\"encrypted\"},\"targetDatabase\":{\"name\":\"DR_DB_02\",\"server\":\"db-dr-02.company.com\",\"port\":1521,\"schema\":\"DISASTER_RECOVERY_V2\",\"connectionString\":\"encrypted\"},\"mappingRules\":{\"tableMapping\":[{\"source\":\"CUSTOMERS_V2\",\"target\":\"CUSTOMERS_DR_V2\"},{\"source\":\"ORDERS_V2\",\"target\":\"ORDERS_DR_V2\"},{\"source\":\"INVENTORY\",\"target\":\"INVENTORY_DR\"}],\"dataTransformation\":{\"encryption\":\"AES-256\",\"compression\":\"LZMA\"},\"syncFrequency\":\"10min\",\"retentionPolicy\":\"60days\",\"backupStrategy\":\"incremental\"}}"));

        fixture.Customize<DeleteCyberComponentMappingCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<CyberComponentMappingListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, () => $"ComponentMapping-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.Properties, () => $"{{\"config\":{{\"type\":\"mapping\",\"id\":\"{fixture.Create<string>().Substring(0, 10)}\",\"active\":true}}}}"));

        fixture.Customize<CyberComponentMappingDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Critical Infrastructure Component Mapping")
            .With(b => b.Properties, "{\"infrastructureMapping\":{\"sourceInfrastructure\":{\"type\":\"VMware vSphere\",\"datacenter\":\"Primary-DC\",\"cluster\":\"PROD-Cluster-01\",\"hosts\":[{\"name\":\"esxi-01.company.com\",\"ip\":\"**************\",\"specs\":\"64GB RAM, 32 cores\"},{\"name\":\"esxi-02.company.com\",\"ip\":\"**************\",\"specs\":\"64GB RAM, 32 cores\"}],\"storage\":{\"type\":\"SAN\",\"capacity\":\"100TB\",\"raid\":\"RAID 10\"}},\"targetInfrastructure\":{\"type\":\"VMware vSphere\",\"datacenter\":\"DR-DC\",\"cluster\":\"DR-Cluster-01\",\"hosts\":[{\"name\":\"esxi-dr-01.company.com\",\"ip\":\"***********\",\"specs\":\"64GB RAM, 32 cores\"},{\"name\":\"esxi-dr-02.company.com\",\"ip\":\"***********\",\"specs\":\"64GB RAM, 32 cores\"}],\"storage\":{\"type\":\"SAN\",\"capacity\":\"100TB\",\"raid\":\"RAID 10\"}},\"replicationSettings\":{\"method\":\"vSphere Replication\",\"rpo\":\"15 minutes\",\"networkCompression\":true,\"encryption\":\"AES-256\",\"bandwidth\":\"1Gbps\"},\"failoverPlan\":{\"automated\":true,\"testSchedule\":\"monthly\",\"recoveryTimeObjective\":\"4 hours\",\"priorityOrder\":[\"Database Servers\",\"Application Servers\",\"Web Servers\"]}}}"));

        CreateCyberComponentMappingCommand = fixture.Create<CreateCyberComponentMappingCommand>();
        UpdateCyberComponentMappingCommand = fixture.Create<UpdateCyberComponentMappingCommand>();
        DeleteCyberComponentMappingCommand = fixture.Create<DeleteCyberComponentMappingCommand>();
        CyberComponentMappingListVm = fixture.Create<CyberComponentMappingListVm>();
        CyberComponentMappingDetailVm = fixture.Create<CyberComponentMappingDetailVm>();
    }
}
