<svg width="136" height="105" viewBox="0 0 136 105" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_305_3067)">
<rect width="136" height="105" rx="6" fill="#FCFCFC"/>
<g filter="url(#filter0_f_305_3067)">
<ellipse cx="16.8926" cy="15.1525" rx="49.4746" ry="45.8498" fill="#FFE1DA"/>
</g>
<g filter="url(#filter1_f_305_3067)">
<ellipse cx="98.1406" cy="12.4475" rx="65.0859" ry="43.1449" fill="#FDE0FF"/>
</g>
<g filter="url(#filter2_f_305_3067)">
<ellipse cx="-12.709" cy="71.6074" rx="39.252" ry="45.8498" fill="#FFE1DA"/>
</g>
</g>
<defs>
<filter id="filter0_f_305_3067" x="-74.482" y="-72.5973" width="182.749" height="175.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="20.95" result="effect1_foregroundBlur_305_3067"/>
</filter>
<filter id="filter1_f_305_3067" x="-8.84531" y="-72.5973" width="213.972" height="170.09" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="20.95" result="effect1_foregroundBlur_305_3067"/>
</filter>
<filter id="filter2_f_305_3067" x="-93.8609" y="-16.1424" width="162.304" height="175.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="20.95" result="effect1_foregroundBlur_305_3067"/>
</filter>
<clipPath id="clip0_305_3067">
<rect width="136" height="105" rx="6" fill="white"/>
</clipPath>
</defs>
</svg>
