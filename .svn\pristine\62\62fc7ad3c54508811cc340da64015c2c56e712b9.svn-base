using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AdPasswordExpireModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IAdPasswordExpireService
{
    Task<List<AdPasswordExpireListVm>> GetAdPasswordExpireList();
    Task<BaseResponse> CreateAsync(CreateAdPasswordExpireCommand createAdPasswordExpireCommand);
    Task<BaseResponse> UpdateAsync(UpdateAdPasswordExpireCommand updateAdPasswordExpireCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<AdPasswordExpireDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsAdPasswordExpireNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<AdPasswordExpireListVm>> GetPaginatedAdPasswordExpires(GetAdPasswordExpirePaginatedListQuery query);
    #endregion
}
