//Functions.

async function serverOSType() {
    let getServerLists = await infraGetRequest(RootUrl + serverURL.getServerListData); //commonfunctions.js

    if (Array.isArray(getServerLists) && getServerLists?.length) {
        let options = [];
        serverList = getServerLists;
        let distinctObjects = Object.values(serverList?.reduce((acc, obj) => {
            acc[obj.osTypeId] = obj;
            return acc;
        }, {}));
        let length = distinctObjects?.length;
        for (let i = 0; i < length; i++) {
            options.push("<option value=" + distinctObjects[i]?.osTypeId + ">" + distinctObjects[i]?.osType + "</option>");
        }
        $(".search-in-type").append(options)
    }
}

function resetVars() {
    substitueAuthType = true;
    enableValidation = false;
    deploymentsTable = true;
    enableDeploymentsValidation = false;
    //previousVersion = "";
    checkedTestConnection = [];
    clonedServerLists = {
        "ServerId": "",
        "ServerList": []
    };
}

async function getServerType(id, serverTypeID, saveas = null) {
    await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + serverURL.getServerType,
        dataType: "json",
        data: { id: id },
        success: function (result) {
            let serverType = serverTypeID;
            let options = [];
            serverType.empty().append($('<option>').val("").text(""));

            if (result.success && (Array.isArray(result?.data) && result?.data.length > 0)) {
                const sortedData = result?.data?.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
                sortedData.forEach(function (item) {
                    let toLowerSiteCategory = siteCategory?.toLowerCase();
                    let toLowerName = item.name.toLowerCase();
                    const roleTypeId = item.id;
                    let option = "";

                    if (saveas === "saveas") {
                        option = $('<option>').val(roleTypeId).text(item.name).attr('roletypeid', roleTypeId).attr('serverTypeId', roleTypeId);
                    } else {
                        option = $('<option>').val(item.name).text(item.name).attr('roletypeid', roleTypeId).attr('serverTypeId', roleTypeId);
                    }

                    if (toLowerSiteCategory === "primary" && toLowerName.includes("pr")) {
                        options.push(option);
                    } else if (toLowerSiteCategory === "dr" && toLowerName.includes("dr")) {
                        options.push(option);
                    } else if (toLowerSiteCategory?.replace(" ", "") === "customdr" &&
                        (toLowerName.includes("near") || (!toLowerName.includes("dr") &&
                            !toLowerName.includes("pr")))) {
                        options.push(option);
                    } else {
                        options.push(option);
                    }
                });
                serverType.append(options);
            } else {
                errorNotification(result);
            }
        },
    });
}

function downloadServer(blob, fileName, contentType) {
    try {
        const link = document.createElement("a");
        link.download = fileName;
        link.href = window.URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        notificationAlert("Error downloading file: " + error.message);
    }
}

async function fetchDataServer(url, value = null) {
    let resultData = null;
    await $.ajax({
        type: "GET",
        url: RootUrl + url,
        dataType: "json",
        data: { name: value },
        success: function (result) {
            if (result.success) {
                resultData = result?.data;
            } else {
                errorNotification(result)
            }
        },
    });
    return resultData;
}

const clearSessionData = () => {
    setTimeout(() => {
        if (isSession) $('#search-in-type').val(getserver).trigger('change')
        sessionStorage.removeItem('serverDataFromITView');
        getserver = '';
    }, 300)
}

function clearErrorMessage() {
    const errorElements = ['#ServerType-error', '#RoleType-error', '#SiteName-error', '#OS-error',
        '#Name-error', '#Licensekey-error', '#Version-error', '#BusinessServiceIdError'];
    clearInputFields('server-form', errorElements);
}

const populateModalFields = (data) => {
    OSTypeAfterEditClicked = data.osType;
    //previousVersion = data.formVersion;
    $("#serverAttach").val(data.isAttached);
    $("#serverStatus").val(data.status);
    $("#serverId").val(data.id);
    $("#serverFormVersion").val(data.formVersion);
    $('#ServerName').val(data.name);
    $('#names').val(data.siteName);
    $('#osType').val(data.osTypeId).trigger("change");
    $('#ostypeid').val(data.osType);
    $("#businessServiceID").val(data.businessServiceId).trigger("change");
    $("#businessServiceName").val(data.businessServiceName);
    $('#siteNames').val(data.siteId).trigger("change");
    siteCategory = $('#siteNames :selected').attr("siteCategory");
    $('#serverRole').val(data.roleType).trigger("change");
    $('#serverType').val(data.serverType).trigger('change');
    $('#server_RoleTypeId').val(data.roleTypeId);
    $('#server_ServerTypeId').val(data.serverTypeId);
    $('#server_LicenseId').val(data.licenseId);
    licenseIdForCountError = data.licenseId;
    setTimeout(() => {
        $('#license').val(data.licenseKey).trigger('change');
        $('#version').val(data.version).trigger("change");
    }, 150)
    serverProps = data.properties
};

//async function updateServerFormVersion(serverdata) {
//    await $.ajax({
//        url: RootUrl + "Configuration/Server/UpdateServerFormVersion",
//        type: "POST",
//        dataType: "json",
//        data: serverdata,
//        success: function (result) {
//            if (result?.success) {
//                notificationAlert("success", result?.data?.message);
//            } else {
//                errorNotification(result);
//            }
//        }
//    });
//}

async function populateFormModal(data) {
    let $formRenderingArea = $('#formRenderingArea');
    $formRenderingArea.empty();

    // try {
    let parsedJsonData = JSON.parse(data?.properties);
    var renderedForm = new FormeoRenderer({
        renderContainer: document.querySelector("#formRenderingArea")
    });
    await renderedForm.render(parsedJsonData);

    ////initial state
    if (!isEdit) {
        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
            var field = parsedJsonData.fields[fieldId];
            if (field.conditions && field.conditions.length > 0) {
                field.conditions.forEach(function (condition) {
                    condition.if.forEach(function (ifClause) {
                        condition.then.forEach(function (thenClause) {
                            if ((thenClause.targetProperty === 'isVisible') &&
                                thenClause.assignment === 'equals' &&
                                ifClause.comparison !== "notEquals") {
                                let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);

                                //For checkbox
                                if (targetElement === null) {
                                    targetElement = document.getElementById(`f-${thenClause.target.substring(7)}-0`);
                                }
                                if (targetElement && !targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none") &&
                                    !targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) {
                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                }
                            }
                        });
                    });
                });
            }
        });
    }

    setTimeout(() => {
        var selectElements = document.querySelectorAll('.form-select-modal-dynamic');
        selectElements?.forEach(async function (selectElement) {
            let $this = $(selectElement);
            $this.attr('title', '');
            $this.select2({
                dropdownParent: this1.find('.modal-content'), placeholder: $this.attr('placeholder')
            });
        });
        $('.form-select-modal-dynamic').next('.select2-container').css('width', '100%');

        let disableSelectTagTitle = document.querySelectorAll('.select2-selection__rendered');
        disableSelectTagTitle?.forEach(async function (selectElement) {
            let $this = $(selectElement);
            $this.attr('title', '');
        });

        onChangeFormBuilderValidation('server');
    }, 500);

    await populateFormbuilderDynamicFields(parsedJsonData?.fields);

    for (const key in parsedJsonData?.fields) {
        if (parsedJsonData.fields.hasOwnProperty(key)) {
            const field = parsedJsonData.fields[key];
            setFieldAttrValues(field);
        }
    }

    ///onsetconditionals
    $formRenderingArea.on('input', '.formeo-render .f-field-group input', function (event) {
        formBuilderTextConditions(event, parsedJsonData);
    });

    $formRenderingArea.on('focus', '.formeo-render .f-field-group input', function (event) {
        let selectedId = event.target.id;
        let type = event.target.type;
        Object.keys(parsedJsonData.fields).forEach(async function (fieldId) {
            var field = parsedJsonData.fields[fieldId];
            if (selectedId == `f-${fieldId}`) {
                if (event.target.value !== '' && type == 'text' && field?.attrs?.encryption) {
                    await $.ajax({
                        type: "POST",
                        url: RootUrl + 'Configuration/Server/ServerDataDecrypt',
                        data: { data: event.target.value, __RequestVerificationToken: gettoken() },
                        dataType: 'text',
                        success: function (decryptedValue) {
                            event.target.value = decryptedValue;
                        }
                    });
                }
            }
        })
    });

    $formRenderingArea.on('blur', '.formeo-render .f-field-group input', function (event) {
        let selectedId = event.target.id;
        let type = event.target.type;
        Object.keys(parsedJsonData.fields).forEach(async function (fieldId) {
            var field = parsedJsonData.fields[fieldId];
            if (selectedId == `f-${fieldId}`) {
                if (event.target.value !== '' && type == 'text' && field?.attrs?.encryption) {
                    await $.ajax({
                        type: "POST",
                        url: RootUrl + 'Configuration/Server/ServerDataEncrypt',
                        data: { data: event.target.value, __RequestVerificationToken: gettoken() },
                        dataType: 'text',
                        success: function (encryptedValue) {
                            event.target.value = encryptedValue;
                        }
                    });
                }
            }
        })
    });

    $formRenderingArea.on('change input', '.formeo-render .f-field-group input , .formeo-render .f-field-group select', function (event) {
        formBuilderConditions(event, parsedJsonData);
    });

    if (isEdit && serverProps) {
        setTimeout(() => {
            let formData = JSON.parse(serverProps);
            $('#formRenderingArea .formeo-render .f-field-group').each(async function (index, element) {
                let fieldName = $(element).find('input, select, textarea, table').attr('name');
                let fieldClass = $(element).find('input, select, textarea, table').attr('class');
                let fieldVal = $(element).find('input, select, textarea').attr('value');
                let fieldType = $(element).find('input, select, textarea').attr('type');

                //if (fieldType === "date") {
                //    console.log(fieldType);
                //    let datePicker = document.getElementById(fieldName);
                //    datePicker.value = dateToBind;
                //}
                //if (fieldType === "date") {
                //    $(element).find('input[type="date"]').val(chkValue).trigger("change");
                //}

                if (fieldName && formData.hasOwnProperty(fieldName) || fieldVal) {

                    let value = formData[fieldName];
                    let SSOValue = formData[fieldName + "ID"];
                    let checkbox = $(element).find('input[type="checkbox"]').attr("value")
                    let chkValue = formData[checkbox]

                    //if (value || chkValue) {
                    if (fieldType == 'radio') {
                        $(element).find('input[type="radio"]').map((index, radio) => {
                            let radioValue = $(radio).val();
                            if (radioValue === formData[radioValue]) {
                                $(radio).prop('checked', true);
                            }
                        });
                    }

                    if (typeof value === "boolean") {
                        $(element).find('input[type="checkbox"]').prop('checked', chkValue);
                        $(element).find('input[type="checkbox"]').trigger("change")
                    }
                    else if (fieldVal) {
                        if (typeof chkValue == 'string') chkValue = (chkValue == '0' || chkValue?.toLowerCase() == 'false') ? false : true
                        $(element).find('input[type="checkbox"]').prop('checked', chkValue).trigger("change");
                    }
                    else if (typeof value === "object") {
                        if (value) {
                            /*const selectElement = document.getElementById('f-0d95bdab-911d-4521-91aa-56a0b40fe093');*/
                            const valuesArray = Object.values(value);
                            const containsObject = Array.isArray(valuesArray) && valuesArray.some(item => typeof item === 'object');
                            let labelsArray;
                            if (containsObject) {
                                labelsArray = valuesArray.map(item => item.value);
                            } else {
                                labelsArray = valuesArray.map(item => item);
                            }
                            //$(element).find('input, select, textarea').val(labelsArray).change();
                            const selectElement = $(element).find('select');
                            // Loop through options in the select element
                            setTimeout(function () {
                                selectElement?.find('option').each(function () {
                                    const optionValue = $(this).val();
                                    if (labelsArray.includes(optionValue)) {
                                        $(this).prop('selected', true);
                                    }
                                });
                                selectElement.trigger('change');
                            }, 350)
                        }
                        //if (value) {
                        /*const selectElement = document.getElementById('f-0d95bdab-911d-4521-91aa-56a0b40fe093');*/
                        //const valuesArray = Object.values(value);
                        //$(element).find('input, select, textarea').val(valuesArray).change();
                        //$(element).find('input, select, textarea').trigger("change");
                        //}
                    }
                    else {
                        if (fieldName.includes("singlesignon")) {
                            $(element).find('input, select, textarea').val(SSOValue).trigger("change")
                        }
                        if (!fieldName.includes("singlesignon")) {
                            $(element).find('input, select, textarea').val(value).trigger("change");

                            if (fieldType == 'select') {

                                if (fieldName?.toLowerCase()?.replace(/\s+/g, '') == 'authenticationtype' && (value?.toLowerCase()?.replace(/\s+/g, '') == 'apitoken' || value?.toLowerCase()?.replace(/\s+/g, '') == 'basicapiauthentication')) {
                                    let getAuthElement = $("select[name='APISubAuthentication']")

                                    $(element).find('input, select, textarea').val('API').trigger("change");

                                    if (value?.toLowerCase()?.replace(/\s+/g, '') == 'apitoken') getAuthElement.val(value).trigger("change");
                                    else if (value?.toLowerCase()?.replace(/\s+/g, '') == 'basicapiauthentication') getAuthElement.val('BasicAuthentication').trigger("change");
                                }

                            }

                            //for migration 4.5 to 6. Get Confirmation whether it will affect exist saved data???..
                            //let selectElement = $(element).find('select')?.[0];
                            //if (selectElement) {
                            //    let options = selectElement.getElementsByTagName("option");
                            //    for (let option of options) {
                            //        option.value = option.value.toLowerCase();
                            //    }
                            //    selectElement.innerHTML = selectElement.innerHTML;
                            //    value = value.replace(/\s+/g, '').toLowerCase();
                            //    $(element).find('select').val(value).trigger("change");
                            //}
                        }
                        //if (fieldName.toLocaleLowerCase()?.trim() === 'ipaddress' || fieldName.toLocaleLowerCase()?.trim() === 'hostname' || fieldName.toLocaleLowerCase()?.trim() === 'virtualipaddress') {
                        //    let ipAddressInput = document.querySelector('input[name="IpAddress"]');
                        //    if (ipAddressInput) {
                        //        ipAddressInput.setAttribute("readonly", "readonly");
                        //    }
                        //    let VirtualIPAddress = document.querySelector('input[name="VirtualIPAddress"]');
                        //    if (VirtualIPAddress) {
                        //        VirtualIPAddress.setAttribute("readonly", "readonly");
                        //    }
                        //    let hostName = document.querySelector('input[name="HostName"]');
                        //    if (hostName) {
                        //        hostName.setAttribute("readonly", "readonly");
                        //    }
                        //}
                        if (fieldName.toLowerCase().includes("singlesignon")) {
                            if (formData["signonprofileID"]) {
                                setTimeout(() => {
                                    $("#f-new-select-id8rhdgry0").val(formData["signonprofileID"]).trigger("change");
                                }, 250);
                            }
                        }
                        if (fieldName === "@@workflow_name") {
                            if (formData["@@workflow_actions"]) {
                                setTimeout(() => {
                                    $("#f-new-select-id8actions0").val(formData["@@workflow_actions"]);
                                    $("#f-new-select-id8actions0").trigger("change");
                                }, 250);
                            }
                        }
                    }
                    //}
                }

                if (fieldClass == 'custom-table' && formData.hasOwnProperty('ConfigureSubstituteAuthentication')) {

                    if (formData.ConfigureSubstituteAuthentication.length > 0) {
                        sudosuTableWhileEdit(formData);
                    }
                }

                if (fieldClass == 'deployments-table' && formData.hasOwnProperty('ConfigureDeployments')) {

                    if (formData.ConfigureDeployments.length > 0) {
                        deploymentTableWhileEdit(formData);
                    }
                }

                //    if (fieldClass == 'dynamic-custom-table' && formData.hasOwnProperty('DynamicCustomTable')) {
                //        if (formData.DynamicCustomTable.length > 0) {
                //            let tableIDCollection = document.getElementsByClassName('dynamic-custom-table');
                //            let tableID = tableIDCollection[0];
                //            let $tableId
                //            if (tableID) {
                //                $tableId = $(tableID).attr('id');
                //                let $table = document.querySelector(`#${$tableId}`)
                //                //$table.innerHTML = '';
                //                let $tHead = $(' <thead> </thead>')
                //                let $headerRow = $('<tr class="header_row5"></tr>');
                //                for (let i = 0; i < formData?.DynamicCustomTable[0]?.length; i++) {
                //                    $headerRow.append(`<th>${formData?.DynamicCustomTable[0][i]}</th>`);
                //                }
                //                //// Append the header row to the table
                //                $tHead.append($headerRow[0])
                //                $table.append($tHead[0]);

                //                let $tBody = $('<tbody></tbody>');
                //                for (let i = 0; i < formData?.DynamicCustomTable[1]?.length; i++) {
                //                    let $dataRow = $('<tr></tr>');
                //                    let values = Object.values(formData?.DynamicCustomTable[1][i]);
                //                    let keys = Object.keys(formData?.DynamicCustomTable[1][i]);
                //                    //console.log(keys);
                //                    for (let j = 0; j < values.length; j++) {
                //                        //let dynamicID = Math.floor((Math.random() * 10000) + 1);
                //                        $dataRow.append(`<td><input type="text" value=${values[j]} name="${keys[j]}" /></td>`);
                //                    }

                //                    //// Add action buttons to the data row
                //                    let $actionCell = $('<td style="height:62px" class="d-flex align-items-center justify-content-center"></td>');

                //                    $actionCell.append(`<span role="button" title="Add" onclick="event.preventDefault(); handleAddDynamicRow('${$tableId}', this.parentElement.parentElement, event)">
                //                                        <i class="cp-add"></i>
                //                                    </span>`);

                //                    i !== 0 && $actionCell.append(`<span role="button" title="Delete" class="delete-button">
                //                                   <i onclick="handleDelete(event)" class="cp-Delete"></i>
                //                               </span>`);

                //                    $dataRow.append($actionCell);
                //                    $tBody.append($dataRow[0])
                //                    $table.append($tBody[0]);
                //                }
                //            }
                //        }
                //    }
            });

            let encryption = $("input[type='password']"); //when it's optional also
            encryption?.each(function () {
                let $this = $(this);
                let id = $this.attr("id");
                document.getElementById(id)?.addEventListener("focus", async function () {

                    if ($this.is(':visible')) {
                        let $thisName = $(this).attr('name');
                        let $thisval = $(this).val();
                        if ($thisval) {
                            let pwd = await DecryptPassword($thisval);
                            $(`input[name=${$thisName}]`).val(pwd);
                        }
                    }
                });

                document.getElementById(id)?.addEventListener('blur', async function () {
                    if ($this.is(':visible')) {
                        let $thisName = $(this).attr('name');
                        let $thisval = $(this).val();
                        if ($thisval) {
                            let pwd = await EncryptPassword($thisval);
                            $(`input[name=${$thisName}]`).val(pwd);
                        }
                    }
                });
            });
            btnCrudEnable('saveButton');
        }, 250);
    }
    //} catch (error) {
    //notificationAlert("warning", error); //"Form property is not valid format."
    //    nextButtonStyle('0.5', 'none');
    //}
}

async function serverSaveFormFields() {
    let SSHPassword = "";
    let WMIPassword = "";
    let SSHWMIPassword = "";
    let PowerShellPassword = "";
    var formData = {};
    var promises = [];
    $('#formRenderingArea .formeo-render .f-field-group').each(async function (index, element) {
        if ($(this).is(':visible')) {
            let fieldName = $(element).find('input, select, textarea').attr('name');
            let fieldNameLowerCase = fieldName?.toLowerCase();
            let fieldValue = $(element).find('input').attr('value');
            let fieldType = $(element).find('input, select, textarea').attr('type');
            const tableClassName = $(element).find('table')?.attr('class');
            let value;

            if (fieldNameLowerCase?.includes("singlesignon") || fieldNameLowerCase?.includes("signonprofile")) {
                let selectElement = document.querySelector(`select[name="${fieldName}"]`);
                formData[fieldName] = selectElement?.options[selectElement.selectedIndex]?.text || "";
            }
            if (fieldType === "date") {
                formData[fieldName] = $(element).find('input[type="date"]').val();
            }
            if (fieldName) {
                if (fieldType === 'checkbox') {
                    value = $(element).find('input[type="checkbox"]').prop('checked');
                    formData[fieldValue] = value;
                } else if (fieldType === 'radio') {
                    value = $(element).find('input[type="radio"]:checked').val();
                    formData[value] = value;
                } else {
                    value = $(element).find('input, select, textarea').val();
                }
                if (fieldType === "password" && (value && value !== "") && value?.length < 64) {
                    promises.push(EncryptPasswordServer(value).then(encryptedPassword => {
                        formData[fieldName] = encryptedPassword;
                    }));
                } else {
                    if (fieldNameLowerCase?.includes("singlesignon") || fieldNameLowerCase?.includes("signonprofile")) {
                        formData[fieldName + "ID"] = value;
                    } else if (fieldNameLowerCase !== "substituteauthenticationtype" && fieldNameLowerCase !== "deploymentsreplicasetname") {
                        formData[fieldName] = value;
                    }
                }

                if (fieldName?.toLowerCase() === "authenticationtype" && $(element).find('input, select, textarea').val()?.toLowerCase().includes("ssh")) {
                    SSHPassword = true;
                }

                if (fieldName?.toLowerCase() === "authenticationtype" && $(element).find('input, select, textarea').val()?.toLowerCase().includes("wmi")) {
                    WMIPassword = true;
                }

                if (fieldName?.toLowerCase() === "authenticationtype" && $(element).find('input, select, textarea').val()?.toLowerCase().includes("powershell")) {
                    PowerShellPassword = true;
                }

                if (fieldValue?.toLowerCase() === "ssoenabled" && $(element).find('input[type="checkbox"]').prop('checked')) {
                    SSHWMIPassword = true;
                }

                if (SSHPassword && SSHWMIPassword) {
                    formData["SSHPassword"] = "";
                }

                if (WMIPassword && SSHWMIPassword) {
                    formData["WMIpassword"] = "";
                }

                if (PowerShellPassword && SSHWMIPassword) {
                    formData["PowerShellPassword"] = "";
                }

            }
            if (fieldNameLowerCase?.includes('nodes')) {
                const selectedOptions = $(element).find('select').val();
                const idToLabelMapping = {};
                $(element).find('select option').each(function () {
                    idToLabelMapping[this.value] = $(this).val();
                });

                // Create key-value pairs of ID and label
                objects_list = [];
                const selectedKeyValuePairs = selectedOptions?.reduce((acc, curr) => {
                    acc[curr] = idToLabelMapping[curr];
                    return acc;
                }, {});

                $.each(selectedKeyValuePairs, function (key, value) {
                    var obj = { 'label': value, 'value': key };
                    objects_list.push(obj);
                });
                formData[fieldName] = objects_list; // Save the key-value pairs in formData           
            }

            if (tableClassName === 'custom-table') {
                let formDataArray = [];
                const rows = $(element).find('tr:not(.header_row)'); // Extracting data from each row (excluding header row)
                rows.each(function () {
                    const rowDataObj = {};
                    const columns = $(this).find('td');
                    columns.each(function () {
                        const header = $(this).find('input, select').attr('name') || $(this).text();
                        const value = ($(this).find('select') ? $(this).find('select').find('option:selected').val() ? $(this).find('select').find('option:selected').val() :
                            $(this).find('input').val() : '') || '';
                        if (header && header?.trim() !== '') {
                            if (header == "SubstituteAuthenticationPassword" && value !== '') {
                                promises.push(EncryptPasswordServer(value).then(encryptedPassword => {
                                    rowDataObj[header] = encryptedPassword;
                                }));
                            } else {
                                rowDataObj[header] = value;
                            }
                        }
                    });
                    if (Object.keys(rowDataObj).length !== 0) {
                        formDataArray.push(rowDataObj);
                    }
                });
                formDataArray = formDataArray.filter(s => s.SubstituteAuthenticationType != '')
                formData["ConfigureSubstituteAuthentication"] = formDataArray;//extractFormData($(element), promises)
            }
            if (tableClassName === 'deployments-table') {
                let formDataArray = [];
                const rows = $(element).find('tr:not(.deployment_row)'); // Extracting data from each row (excluding header row)
                rows.each(function () {
                    const rowDataObj = {};
                    const columns = $(this).find('td');
                    columns.each(function () {
                        const header = $(this).find('input').attr('name') || $(this).text();
                        const value = $(this).find('input').val() || '';
                        if (header && header?.trim() !== '') {
                            rowDataObj[header] = value;
                        }
                    });
                    if (Object.keys(rowDataObj).length !== 0) {
                        formDataArray.push(rowDataObj);
                    }
                });
                formData["ConfigureDeployments"] = formDataArray;//extractFormData($(element), promises)
            }
        }
    });
    await Promise.all(promises);
    return formData;
}

const getSignalRConnectionServer = async () => {
    configurationURL = $('#Chat_Bot').attr('signalRurl');
    let url = configurationURL + "notificationhub"
    let connection = new signalR.HubConnectionBuilder()
        .withUrl(url, {
            skipNegotiation: true,
            rejectUnauthorized: false,
            transport: signalR.HttpTransportType.WebSockets
        })
        .configureLogging(signalR.LogLevel.Information)
        .build();
    connection.onclose(async () => {
        await new Promise(resolve => setTimeout(resolve, 10000));
        await startSignalRReceiveMessageConnection();
    });
    startSignalRReceiveMessageConnection(connection);   //Start the connection.   
}

const startSignalRReceiveMessageConnection = async (connection) => {
    try {
        await connection.start();
        connection.on("ServerSaveAllMessage", (message) => {
            showSaveAllUpdates(message);
        });
    } catch (err) {
        setTimeout(startSignalRReceiveMessageConnection, 10000); //Retry after 10 seconds.
    }
};

$(() => {
    getSignalRConnectionServer();
})

const showSaveAllUpdates = (data) => {
    console.log(data)
}

const EncryptPasswordServer = async (text) => {
    const generateKey = async () => {
        const key = await window.crypto.subtle.generateKey({ name: 'AES-GCM', length: 256 }, true, ['encrypt', 'decrypt']);
        const exportedKey = await window.crypto.subtle.exportKey('raw', key,);
        return bufferToBase64(exportedKey);
    }
    const bufferToBase64 = (arrayBuffer) => {
        return window.btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
    }
    const loadKey = async (base64Key) => {
        return await window.crypto.subtle.importKey('raw', base64ToBuffer(base64Key), "AES-GCM", true, ["encrypt", "decrypt"]);
    }
    const base64ToBuffer = (base64) => {
        const binary_string = window.atob(base64);
        const len = binary_string.length;
        let bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binary_string.charCodeAt(i);
        }
        return bytes.buffer;
    }
    const cryptGcm = async (base64Key, bytes) => {
        const key = await loadKey(base64Key);
        const iv = window.crypto.getRandomValues(new Uint8Array(12));
        const algorithm = { iv, name: 'AES-GCM' };
        const cipherData = await window.crypto.subtle.encrypt(algorithm, key, bytes);
        const cipherText = concatArrayBuffers(iv.buffer, cipherData);
        return bufferToBase64(cipherText);
    }
    const concatArrayBuffers = (buffer1, buffer2) => {
        let tmp = new Uint8Array(buffer1.byteLength + buffer2.byteLength);
        tmp.set(new Uint8Array(buffer1), 0);
        tmp.set(new Uint8Array(buffer2), buffer1.byteLength);
        return tmp.buffer;
    }
    const plaintext = text;
    const plaintextBytes = (new TextEncoder()).encode(plaintext, 'utf-8');
    const encryptionKey = await generateKey();
    const ciphertext = await cryptGcm(encryptionKey, plaintextBytes);
    if (encryptionKey && ciphertext) {
        return encryptionKey + "$" + ciphertext;
    }
}

//Validations
async function clearCloneErrorMessage() {
    const errorElements = ['#cloneNameError', '#cloneServerNameError', '#cloneSiteNameError',
        '#cloneServerTypeError', '#cloneLicenseKeyError', '#cloneIPAddressError', '#cloneHostNameError'];
    await clearInputFields('createFormDB', errorElements);
}

function cloneServerNameValidation(validation, serverName) {
    if (!validation || clonedServerLists.ServerList.length === 0) {
        return true;
    };
    const existsName = clonedServerLists.ServerList.some(
        (data) => data.Name.toLowerCase().trim() === serverName.toLowerCase().trim()
    );
    $('#cloneServerNameError')
        .text(existsName ? "Name already exists" : "")
        .toggleClass('field-validation-error', existsName);
    return !existsName;
}

function cloneIPAddressValidation(value, errorMessage, errorElement) {
    let sanitizedValue = value.replace(/\s{2,}/g, ' '); //Restrict Multiple spaces
    let cleanedIp = sanitizedValue.replace(/\.+/g, '.');
    $("#cloneIPAddress").val(cleanedIp);
    let $errorEl = $('#' + errorElement);

    if (!value) {
        $errorEl.text(errorMessage).addClass('field-validation-error');
        return false;
    }
    if (cleanedIp.includes(" ")) {
        $errorEl.text("Enter IP address without space").addClass('field-validation-error');
        return false;
    }
    const ipRegex = /^(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)$/;

    if (ipRegex.test(cleanedIp)) {
        $errorEl.text("").removeClass('field-validation-error');
        return true;
    }
    $errorEl.text("Enter valid IP address").addClass('field-validation-error');
    return false;

}

function commonInputValidation(value, errorMessage, errorElement) {
    let $errorEl = $('#' + errorElement);
    if (!value) {
        $errorEl.text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $errorEl.text('').removeClass('field-validation-error');
        return true;
    }
};

async function getLicenseWithCount() {
    if ($("#serverRole").val() && $('#siteNames').val()) {
        $("#information").html("");
        let licensesNameLists = "";
        $("#Licensekey-error").text("").removeClass("field-validation-error");
        await $.ajax({
            type: "GET",
            async: false, //dont't delete.
            url: RootUrl + serverURL.getLicensesNamesWithCount,
            dataType: "json",
            data: { type: "server", roleType: $('#serverRole').val(), siteId: $('#siteNames').val(), serverId: "", replicationType: "" },
            success: function (result) {
                if (result.success && (Array.isArray(result?.data) && result?.data?.length > 0)) {
                    licensesNameLists = result?.data;
                } else {
                    errorNotification(result)
                }
            },
        });

        if (licensesNameLists && (Array.isArray(licensesNameLists) && licensesNameLists?.length > 0)) {
            let options = [];
            let license = $('#license');
            license.empty().append($('<option>').val("").text("Select PO"));
            licensesNameLists.forEach(function (item) {
                options.push($('<option>').val(item?.poNumber)
                    .text(`${item?.poNumber || ''}`)
                    .attr('licenseId', item?.id)
                    .attr('remainingcount', item?.remainingCount || '0')
                    .attr('licenseIsApplicable', item?.licenseIsApplicable))
            });
            license.append(options);
        }
    }
}

function licenseCountValidation() {
    let $license = $("#license :selected");
    let count = $license.attr('remainingcount');
    let licenseIsApplicable = $license.attr('licenseIsApplicable');
    let licenseId = $license.attr('licenseId');
    let licenseError = $('#Licensekey-error');

    if (licenseIsApplicable === 'false') {
        licenseError.text('License expired').addClass('field-validation-error');
        $("#information").text("");
        return false;
    } else if (count) {
        const countInt = parseInt(count, 10);
        const isValidCount = countInt > 0 || (licenseIdForCountError === licenseId);

        if (isValidCount) {
            licenseError.text('').removeClass('field-validation-error');
            return true;
        }
        licenseError.text('License count exceeded').addClass('field-validation-error');
        return false;
    }
}
