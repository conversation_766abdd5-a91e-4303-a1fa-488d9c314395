﻿@model ContinuityPatrol.Domain.ViewModels.BasicCompanyViewModal;

@using Microsoft.Extensions.Configuration;
@inject IConfiguration Configuration;

@{
    ViewData["Title"] = "Index";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
<link href="~/lib/jquery.steps/jquery.steps.css" rel="stylesheet" />
<link href="~/css/wizard.css" rel="stylesheet" />
<link href="~/css/password_stregnth_meter.css" rel="stylesheet"/>

<div class="container-fluid">
    <div class="align-items-center vh-100 row">
     
        <div class="d-grid bg-light justify-content-center align-items-center col-md-7 col-lg-7 vh-100 ">
            
            <div class="px-4 py-1">
                <img class="mb-4" src="~/img/logo/pts_logo.png" alt="Customer Logo" height="40" />
                <div class="">
                    <h4>Compliance Management & Operational Resilience</h4>
                    <h6 class="fw-normal my-3">Simple workflows to perform actions & failovers in few clicks.</h6>
                </div>
                @* <img src="~/img/isomatric/login-configuration.svg" style="width:100%;" /> *@

                <svg width="577" height="413" fill="none"><path fill="url(#a)" d="M277.507 25.797c-140.59 0-254.57 113.98-254.57 254.57 0 41.35 9.86 80.41 27.36 114.93h454.421c17.5-34.52 27.36-73.58 27.36-114.93 0-140.59-113.981-254.57-254.571-254.57" /><path fill="url(#b)" d="M106.298 65.36c10.554 0 19.11-8.555 19.11-19.11 0-10.553-8.556-19.11-19.11-19.11s-19.11 8.557-19.11 19.11c0 10.555 8.555 19.11 19.11 19.11" /><path fill="url(#c)" d="M562.302 340.353c6.185 0 11.2-5.014 11.2-11.2s-5.015-11.2-11.2-11.2c-6.186 0-11.2 5.015-11.2 11.2 0 6.186 5.014 11.2 11.2 11.2" /><path fill="#fff" d="M338.803 74.754a5.42 5.42 0 1 0 0-10.84 5.42 5.42 0 0 0 0 10.84" /><path fill="url(#d)" d="M411.342 27.863a5.42 5.42 0 1 0 0-10.84 5.42 5.42 0 0 0 0 10.84" /><path fill="#fff" d="M531.433 213.43c7.53-6.766 8.149-18.356 1.382-25.886s-18.355-8.148-25.885-1.382c-7.53 6.767-8.149 18.356-1.382 25.886s18.356 8.148 25.885 1.382M54.513 273.253c0 2.18-1.77 3.94-3.94 3.94a3.941 3.941 0 0 1 0-7.881c2.17 0 3.94 1.771 3.94 3.941M160.552 325.893c0 2.18-1.77 3.94-3.94 3.94-2.18 0-3.94-1.77-3.94-3.94 0-2.18 1.77-3.94 3.94-3.94 2.18-.01 3.94 1.76 3.94 3.94" /><path fill="url(#e)" d="M41.98 381.6c19.772 0 35.8-16.028 35.8-35.8S61.75 310 41.98 310c-19.772 0-35.8 16.028-35.8 35.8s16.028 35.8 35.8 35.8" /><path fill="#fff" d="M300.834 342.38c.14-4.54-.43-8.81-1.37-12.66-.76-3.11-.97-6.31-.65-9.49.16-1.57.2-3.47-.01-5.66-.69-7.16-8.43-23.28-21.82-25.57 0 0 2.32 8.36 4.42 14.76 2.7 8.25 5.61 13.39 10.18 17.89 4.62 4.55 7.61 10.51 7.97 16.99.07 1.22.08 2.46.05 3.72z" /><path fill="url(#f)" d="M303.991 329.719c-.16-3.1.13-6.21 1-9.19 1.13-3.87 2.1-9.64.76-16.05-2.73-13.05-17.97-25.94-17.97-25.94s2.52 19.1 5.35 27.42c1.51 4.42 4.02 9.36 6.15 13.18a28.25 28.25 0 0 1 3.57 12.66c.45 12.15-.74 20.81-.77 20.96l1.23.18c.01-.17 1.36-9.88.68-23.22" /><path fill="#fff" d="M325.997 268.133s-13.99 9.22-17.68 23.91c-1.13 4.52-.83 8.47-.13 11.47 1.01 4.34.96 8.86.17 13.25-3.58 19.71-1.72 36.53-1.7 36.75l1.23-.14c-.02-.2-1.7-15.42 1.24-33.79.86-5.36 3.12-10.43 6.6-14.6 2.26-2.7 4.9-6.85 7.16-13.04 4.13-11.28 3.11-23.81 3.11-23.81" /><path fill="url(#g)" d="M310.086 312.993c-1.63 5.99-1.131 10.85-.301 14.12.7 2.75.84 5.6.37 8.4-2.3 13.66-1.769 26.66-1.759 26.85l1.24-.06c-.01-.18-.5-12.14 1.5-25.09a19.4 19.4 0 0 1 4.35-9.58c2.16-2.57 4.63-6.25 6.5-10.47 5.09-11.48 6.919-25.96 6.919-25.96s-15.319 8.92-18.819 21.79" /><path fill="#fff" d="M323.267 336.352h-34.97s1.07 52.17 11.89 58.57h11.19c10.83-6.41 11.89-58.57 11.89-58.57" /><path stroke="#fff" stroke-miterlimit="10" d="M480.945 233.227h9.93c3.03 0 5.48 2.46 5.48 5.48v156.69" /><path stroke="#fff" stroke-miterlimit="10" d="M480.945 268.969h9.93c3.03 0 5.48 2.46 5.48 5.48v120.95" /><path stroke="#fff" stroke-miterlimit="10" d="M480.945 304.703h9.93c3.03 0 5.48 2.46 5.48 5.48v85.22" /><path stroke="#fff" stroke-miterlimit="10" d="M480.945 340.438h9.93c3.03 0 5.48 2.46 5.48 5.48v49.479" /><path stroke="#fff" stroke-miterlimit="10" d="M480.945 376.18h9.93c3.03 0 5.48 2.46 5.48 5.48v13.74M259.734 277.547v117.75" /><path fill="#fff" d="M275.709 77.447c2.153-9.19-3.551-18.385-12.74-20.537-9.19-2.153-18.385 3.55-20.538 12.74s3.552 18.384 12.741 20.537 18.384-3.551 20.537-12.74" /><path fill="url(#h)" d="M264.696 71.088c0 3.54-2.519 6.4-5.629 6.4s-5.629-2.87-5.629-6.4 2.519-6.4 5.629-6.4 5.629 2.87 5.629 6.4" /><path fill="url(#i)" d="M271.611 85.143a17.05 17.05 0 0 1-12.54 5.48c-4.95 0-9.42-2.11-12.54-5.48.61-.79 1.31-1.51 2.06-2.17 2.28-1.97 5.14-3.27 8.27-3.65 1.47-.18 2.96-.18 4.42 0 4.16.51 7.85 2.65 10.33 5.82" /><path fill="#fff" d="M455.917 180.241a8.55 8.55 0 1 0 0-17.1 8.55 8.55 0 0 0 0 17.1" /><path fill="url(#j)" d="M458.729 170.473c0 1.77-1.26 3.2-2.81 3.2s-2.81-1.43-2.81-3.2 1.26-3.2 2.81-3.2 2.81 1.43 2.81 3.2" /><path fill="url(#k)" d="M462.188 177.499a8.5 8.5 0 0 1-6.27 2.74c-2.48 0-4.71-1.06-6.27-2.74.31-.4.65-.76 1.03-1.08a7.7 7.7 0 0 1 4.14-1.82c.73-.09 1.48-.09 2.21 0 2.07.24 3.92 1.31 5.16 2.9" /><path fill="#fff" d="M44.126 242.698c6.473 0 11.72-5.247 11.72-11.72s-5.247-11.72-11.72-11.72c-6.472 0-11.72 5.247-11.72 11.72s5.248 11.72 11.72 11.72" /><path fill="url(#l)" d="M47.986 229.304c0 2.42-1.73 4.39-3.86 4.39s-3.86-1.96-3.86-4.39 1.73-4.39 3.86-4.39 3.86 1.96 3.86 4.39" /><path fill="url(#m)" d="M52.723 238.929a11.7 11.7 0 0 1-8.6 3.76c-3.39 0-6.46-1.45-8.6-3.76.42-.54.89-1.04 1.41-1.48 1.56-1.35 3.53-2.24 5.67-2.5 1.01-.12 2.03-.12 3.03 0 2.86.34 5.38 1.81 7.09 3.98" /><path fill="url(#n)" d="M468.226 391.131h-142.48c-4.88 0-8.84-3.96-8.84-8.84v-14.06c0-4.88 3.96-8.84 8.84-8.84h142.48c4.88 0 8.84 3.96 8.84 8.84v14.05c0 4.89-3.96 8.85-8.84 8.85" /><path fill="url(#o)" d="M336.134 384.071h-10.11c-.71 0-1.29-.58-1.29-1.29v-15.03c0-.71.58-1.29 1.29-1.29h10.11c.71 0 1.29.58 1.29 1.29v15.03c.01.71-.57 1.29-1.29 1.29" /><path fill="url(#p)" d="M352.283 384.071h-10.11c-.71 0-1.29-.58-1.29-1.29v-15.03c0-.71.58-1.29 1.29-1.29h10.11c.71 0 1.29.58 1.29 1.29v15.03c0 .71-.57 1.29-1.29 1.29" /><path fill="#fff" d="M428.013 384.071h-69.7c-.71 0-1.29-.58-1.29-1.29v-15.03c0-.71.58-1.29 1.29-1.29h69.7c.71 0 1.29.58 1.29 1.29v15.03c0 .71-.57 1.29-1.29 1.29" /><path fill="url(#q)" d="M442.896 375.288c0 2.5-2.03 4.53-4.53 4.53s-4.53-2.03-4.53-4.53 2.03-4.53 4.53-4.53c2.51 0 4.53 2.03 4.53 4.53" /><path fill="#fff" d="M455.255 375.288c0 2.5-2.03 4.53-4.53 4.53s-4.53-2.03-4.53-4.53 2.03-4.53 4.53-4.53 4.53 2.03 4.53 4.53" /><path fill="url(#r)" d="M468.382 377.235h-9.61a.85.85 0 0 1-.85-.85v-2.23c0-.47.38-.85.85-.85h9.61c.47 0 .85.38.85.85v2.23c0 .47-.38.85-.85.85" /><path fill="#fff" d="M342.416 391.133h-11.33v3.77h11.33zM463.58 391.133h-11.33v3.77h11.33z" /><path fill="url(#s)" d="M479.413 379.35h-2.351v-8.17h2.351c.84 0 1.53.68 1.53 1.53v5.11c0 .84-.69 1.53-1.53 1.53" /><path fill="url(#t)" d="M468.226 355.621h-142.48c-4.88 0-8.84-3.96-8.84-8.84v-14.05c0-4.88 3.96-8.84 8.84-8.84h142.48c4.88 0 8.84 3.96 8.84 8.84v14.05c0 4.88-3.96 8.84-8.84 8.84" /><path fill="url(#u)" d="M336.134 348.563h-10.11c-.71 0-1.29-.58-1.29-1.29v-15.03c0-.71.58-1.29 1.29-1.29h10.11c.71 0 1.29.58 1.29 1.29v15.03c.01.71-.57 1.29-1.29 1.29" /><path fill="#fff" d="M352.283 348.563h-10.11c-.71 0-1.29-.58-1.29-1.29v-15.03c0-.71.58-1.29 1.29-1.29h10.11c.71 0 1.29.58 1.29 1.29v15.03c0 .71-.57 1.29-1.29 1.29M428.013 348.563h-69.7c-.71 0-1.29-.58-1.29-1.29v-15.03c0-.71.58-1.29 1.29-1.29h69.7c.71 0 1.29.58 1.29 1.29v15.03c0 .71-.57 1.29-1.29 1.29" /><path fill="url(#v)" d="M442.896 339.78c0 2.5-2.03 4.53-4.53 4.53s-4.53-2.03-4.53-4.53 2.03-4.53 4.53-4.53c2.51 0 4.53 2.03 4.53 4.53" /><path fill="#fff" d="M455.255 339.78c0 2.5-2.03 4.53-4.53 4.53s-4.53-2.03-4.53-4.53 2.03-4.53 4.53-4.53 4.53 2.03 4.53 4.53M468.382 341.727h-9.61a.85.85 0 0 1-.85-.85v-2.23c0-.47.38-.85.85-.85h9.61c.47 0 .85.38.85.85v2.23c0 .47-.38.85-.85.85M342.416 355.617h-11.33v3.77h11.33zM463.58 355.617h-11.33v3.77h11.33z" /><path fill="url(#w)" d="M479.413 343.842h-2.351v-8.17h2.351c.84 0 1.53.68 1.53 1.53v5.11c0 .84-.69 1.53-1.53 1.53" /><path fill="url(#x)" d="M468.226 320.105h-142.48c-4.88 0-8.84-3.96-8.84-8.84v-14.05c0-4.88 3.96-8.84 8.84-8.84h142.48c4.88 0 8.84 3.96 8.84 8.84v14.05c0 4.88-3.96 8.84-8.84 8.84" /><path fill="url(#y)" d="M336.134 313.047h-10.11c-.71 0-1.29-.579-1.29-1.289v-15.03c0-.71.58-1.29 1.29-1.29h10.11c.71 0 1.29.58 1.29 1.29v15.03c.01.72-.57 1.289-1.29 1.289" /><path fill="url(#z)" d="M352.283 313.047h-10.11c-.71 0-1.29-.579-1.29-1.289v-15.03c0-.71.58-1.29 1.29-1.29h10.11c.71 0 1.29.58 1.29 1.29v15.03c0 .72-.57 1.289-1.29 1.289" /><path fill="#fff" d="M428.013 313.047h-69.7c-.71 0-1.29-.579-1.29-1.289v-15.03c0-.71.58-1.29 1.29-1.29h69.7c.71 0 1.29.58 1.29 1.29v15.03c0 .72-.57 1.289-1.29 1.289" /><path fill="url(#A)" d="M442.896 304.264c0 2.5-2.03 4.53-4.53 4.53s-4.53-2.03-4.53-4.53 2.03-4.53 4.53-4.53c2.51.01 4.53 2.03 4.53 4.53" /><path fill="url(#B)" d="M455.255 304.264c0 2.5-2.03 4.53-4.53 4.53s-4.53-2.03-4.53-4.53 2.03-4.53 4.53-4.53c2.5.01 4.53 2.03 4.53 4.53" /><path fill="#fff" d="M468.382 306.219h-9.61a.85.85 0 0 1-.85-.85v-2.23c0-.47.38-.85.85-.85h9.61c.47 0 .85.38.85.85v2.23c0 .46-.38.85-.85.85M342.416 320.102h-11.33v3.77h11.33zM463.58 320.102h-11.33v3.77h11.33z" /><path fill="url(#C)" d="M479.413 308.326h-2.351v-8.17h2.351c.84 0 1.53.68 1.53 1.53v5.11c0 .85-.69 1.53-1.53 1.53" /><path fill="url(#D)" d="M468.226 284.605h-142.48c-4.88 0-8.84-3.96-8.84-8.84v-14.05c0-4.88 3.96-8.84 8.84-8.84h142.48c4.88 0 8.84 3.96 8.84 8.84v14.05c0 4.88-3.96 8.84-8.84 8.84" /><path fill="url(#E)" d="M336.134 277.547h-10.11c-.71 0-1.29-.579-1.29-1.289v-15.031c0-.71.58-1.289 1.29-1.289h10.11c.71 0 1.29.579 1.29 1.289v15.031a1.28 1.28 0 0 1-1.29 1.289" /><path fill="#fff" d="M352.283 277.547h-10.11c-.71 0-1.29-.579-1.29-1.289v-15.031c0-.71.58-1.289 1.29-1.289h10.11c.71 0 1.29.579 1.29 1.289v15.031c0 .71-.57 1.289-1.29 1.289M428.013 277.547h-69.7c-.71 0-1.29-.579-1.29-1.289v-15.031c0-.71.58-1.289 1.29-1.289h69.7c.71 0 1.29.579 1.29 1.289v15.031c0 .71-.57 1.289-1.29 1.289" /><path fill="url(#F)" d="M442.896 268.764c0 2.5-2.03 4.53-4.53 4.53s-4.53-2.03-4.53-4.53 2.03-4.53 4.53-4.53c2.51 0 4.53 2.03 4.53 4.53" /><path fill="url(#G)" d="M455.255 268.764c0 2.5-2.03 4.53-4.53 4.53s-4.53-2.03-4.53-4.53 2.03-4.53 4.53-4.53 4.53 2.03 4.53 4.53" /><path fill="#fff" d="M468.382 270.711h-9.61a.85.85 0 0 1-.85-.85v-2.23c0-.47.38-.85.85-.85h9.61c.47 0 .85.38.85.85v2.23c0 .47-.38.85-.85.85M342.416 284.602h-11.33v3.77h11.33zM463.58 284.602h-11.33v3.77h11.33z" /><path fill="url(#H)" d="M479.413 272.826h-2.351v-8.17h2.351c.84 0 1.53.68 1.53 1.53v5.11c0 .84-.69 1.53-1.53 1.53" /><path fill="url(#I)" d="M468.226 249.097h-142.48c-4.88 0-8.84-3.96-8.84-8.84v-14.05c0-4.88 3.96-8.84 8.84-8.84h142.48c4.88 0 8.84 3.96 8.84 8.84v14.05c0 4.88-3.96 8.84-8.84 8.84" /><path fill="#fff" d="M336.134 242.04h-10.11c-.71 0-1.29-.58-1.29-1.29v-15.03c0-.71.58-1.29 1.29-1.29h10.11c.71 0 1.29.58 1.29 1.29v15.03c.01.71-.57 1.29-1.29 1.29M352.283 242.04h-10.11c-.71 0-1.29-.58-1.29-1.29v-15.03c0-.71.58-1.29 1.29-1.29h10.11c.71 0 1.29.58 1.29 1.29v15.03c0 .71-.57 1.29-1.29 1.29M428.013 242.04h-69.7c-.71 0-1.29-.58-1.29-1.29v-15.03c0-.71.58-1.29 1.29-1.29h69.7c.71 0 1.29.58 1.29 1.29v15.03c0 .71-.57 1.29-1.29 1.29M442.896 233.257c0 2.5-2.03 4.53-4.53 4.53s-4.53-2.03-4.53-4.53 2.03-4.53 4.53-4.53c2.51 0 4.53 2.03 4.53 4.53" /><path fill="url(#J)" d="M455.255 233.257c0 2.5-2.03 4.53-4.53 4.53s-4.53-2.03-4.53-4.53 2.03-4.53 4.53-4.53 4.53 2.03 4.53 4.53" /><path fill="#fff" d="M468.382 235.203h-9.61a.85.85 0 0 1-.85-.85v-2.23c0-.47.38-.85.85-.85h9.61c.47 0 .85.38.85.85v2.23c0 .47-.38.85-.85.85M342.416 249.094h-11.33v3.77h11.33zM463.58 249.094h-11.33v3.77h11.33z" /><path fill="url(#K)" d="M479.413 237.318h-2.351v-8.17h2.351c.84 0 1.53.68 1.53 1.53v5.11c0 .84-.69 1.53-1.53 1.53" /><path fill="url(#L)" d="M346.477 280.492s.94 1.97 1.53 3.53c.59 1.55-.17 4.35-.17 4.35h-1.22s-4.85 3.61-7.22 5.07c-2.38 1.46-3.36 1.98-4.61 1.99-.94.01-3.29-.71-3.06-2.17s7.83-11.66 7.83-11.66 5.23.1 6.92-1.11" /><path fill="url(#M)" d="M321.034 244.32s1.14 1.31 2.35 3.12c1.22 1.81 1.34 5.42 1.34 5.42l-18.67 8.87s-3.23-2.06-.33-5.5c3.79-4.51 7.51-9 7.51-9s4.6-.45 7.8-2.91" /><path fill="url(#N)" d="m311.5 241.363 1.75 5.88s4.75.29 7.79-2.92l-1.17-5.12" /><path fill="url(#O)" d="M329.484 196.15s7.05.94 11.06.48c4.64-.53.33-4.95.33-4.95l-10.9 2.62z" /><path fill="url(#P)" d="m352.188 202.525-1.26 14.84h-14.82s-5.79-1.5-12.82-4.28c-2.48-.98-5.11-2.12-7.69-3.4.93 1.99 1.32 3.92 1.85 8.04 1.74 13.52 3.2 23.39 3.2 23.39-4.5 3-9.01 2.7-9.01 2.7s-11.02-29.92-12.72-40.41c-.93-5.76 3.07-12.08 15.81-10.46 4.53.58 16.23 3.6 20.49 4.62 10.18 2.43 16.97 4.96 16.97 4.96" /><path fill="url(#Q)" d="M345.979 217.366h-9.87s-5.79-1.5-12.82-4.28c0 0 9.35 2.74 18.71 2.8 2.67.02 3.74.19 3.98 1.48" opacity=".5" /><path fill="url(#R)" d="m338.438 276.18 1.12 5.42s5.24.95 6.92-1.11l-.33-3.82" /><path fill="url(#S)" d="M371.247 200.391s-13.51 3.82-20.22.22c0 0-20.77 25.42-21.58 35-.21 2.47 3.15 15.86 5.75 27.25 2.12 9.32 3.56 17.25 3.56 17.25s4.94.08 7.94-1.08c0 0-.77-33.04-3.36-41.25 6.36-4.52 16.36-11.43 22.23-18.54.97-1.18 2.4-1.88 3.92-1.88 0 0 5.06-6.36 1.76-16.97" /><path fill="#0479FF" d="M339.946 176.272c-.72-.17-1.41-.36-2.06-.55-6.75-1.98-8.62-4.35-8.62-4.35 4.24-11.88 6.92-14.55 8.9-16.74 3.65-4.03.89 8.85.47 9.07z" /><path fill="url(#T)" d="m338.474 164.516-1.56 7.18 3.75-.94z" /><path fill="#0479FF" d="M371.519 199.242c.23 1.92 1.38 2.62 1.36 4.83 0 .53-1.82 2.79-6.44 3.61 0 0-9.84 4.41-19.36-3.49-4.58-3.81-9.71-6.13-9.71-6.13-.22-.14-.18-.53.08-.89.2-.29 1.37-2.01 1.4-2.15 1.85-9.96.29-10.95.18-18.57 0-.19-.01-.38-.01-.57-.2-11.75-1.39-17.63-.84-21.26 2.72-3.04 10.24-5.43 10.72-5.53.02-.01 7.91-.06 9.63 0 3.94 1.01 10.91 3.11 14.38 9.01-.19 4.72.11 10.45-.46 17.8-.11 1.12-1.92 15.15-.93 23.34" /><path fill="#192139" d="M340.985 131s-.59 4.09 1.31 4.84l.75-4.53" /><path fill="url(#U)" d="M350.926 154.923c-4.62-.55-2.54-5.26-2.05-5.88l.01.01s.77-5.01.83-5.25l7.83-3.42s.18 8.55.97 8.67c1.19.56-.41 6.72-7.59 5.87" /><path fill="url(#V)" d="M354.824 143.227s-1.7 3.73-5.73 4.47l.62-3.9z" /><path fill="url(#W)" d="M341.918 130.812s-1.77 13.49 4.18 15.71c0 0 5.93.56 9.38-4.09.99-1.34 2.25-1.64 2.66-3.26.67-2.63 1.61-7.47.41-10.39-1.7-4.15-16.46-8.09-16.63 2.03" /><path fill="#192139" d="M345.897 121.343s1.39-3.21 5.66-1.68c2.99 1.07 4.51 3.29 4.51 3.29s1.2-.97 2.29.51c.71.97.26 3.03.26 3.03 7.1 1.3-.07 16.38-.16 16.87l-.76.44c-.2-2.21-.14-4.23-.26-5.77-.04-.47-.78-.74-.95-1.06-1.4.23-2.77.21-3.56-.46-1.39-1.18-1.67-3.13-1.81-4.44-1.32.44-12.16 2.16-11.62-4.03 0 0-.49-.95-.65-1.92-1.29-7.68 7.05-4.78 7.05-4.78" /><path fill="url(#X)" d="M355.083 139.922c2.69 1.03 3.52-5.58.97-5.87-1.93-.22-2.01 2.48-2.01 2.48s-.49 2.8 1.04 3.39" /><path fill="url(#Y)" d="m361.648 167.133 3.09 10.19 6.25.87z" /><path fill="url(#Z)" d="M381.709 179.056c-2.56-7.72-7.13-16.9-7.13-16.9l-10.45 6s3.99 7.97 6.64 13.54c-2.55.62-9.15 2.28-14.2 3.9-6.39 2.07-12.35 2.64-12.35 2.64.28 1.97.28 3.77.28 3.77 6.24.34 18.01.44 26.33-.13 3.43-.23 6.36-.56 7.91-1.09 2.44-.84 4.1-1.5 4.22-4.05.16-3.74-1.2-7.59-1.25-7.68" /><path fill="#fff" d="M356.48 195.022H342v-1.42h14.48c.39 0 .71.32.71.71 0 .4-.32.71-.71.71" /><path fill="#fff" d="M348.647 195.022h-34.6c-.43 0-.81-.29-.93-.71l-7.42-26.71a.933.933 0 0 1 .89-1.18h33.4c.82 0 1.53.54 1.75 1.33l7.39 26.61c.1.34-.14.66-.48.66" /><path fill="url(#aa)" d="M347.335 195.022h-34.6c-.43 0-.81-.29-.93-.71l-7.42-26.71a.933.933 0 0 1 .89-1.18h33.4c.82 0 1.53.54 1.75 1.33l7.39 26.61c.1.34-.14.66-.48.66" /><path fill="url(#ab)" d="M316.797 195.023s.6 1.34 3.31 1.61l-.19-1.61z" /><path fill="url(#ac)" d="M317.43 190.485c.06-.25.67-.53 1.16-.37-.21-.58-.37-1.08-.37-1.15.05-.71.74-.62 1.13-.4.45.25.98 1.42 1.12 1.55-.18-.37-.36-.81-.35-.86.1-.42 1.03-.77 1.56-.28.27.25 1.08 1.6 1.28 1.96-.06-.23-.09-.39-.08-.42.1-.42.89-.57 1.42-.08.78.71 1.89 3.3 1.89 3.3.51 1.15.99 1.3 1.13 1.3h2.85s-.22 1.18-1.56 1.63c-1.32.44-3.41.72-5.98.65-1.28-.03-2.81-.48-3.59-1.66-.27-.41-.82-2.46-1.02-3.23-.11-.4-.6-1.89-.59-1.94" /><path fill="#0479FF" d="M372.885 158.117s2.31 2.88 6.43 12.9c0 0-6.21 3.65-13.8 5.15 0 0-1.59-3.21-5.32-12.61" /><path fill="url(#ad)" d="M226.57 395.532h22.63v-139.29h-22.63z" /><path fill="url(#ae)" d="M225.297 395.532h20.9v-139.29h-20.9z" /><path fill="url(#af)" d="M246.197 278.633h-20.9v4.94h20.9z" opacity=".5" /><path fill="url(#ag)" d="m193.914 276.247 4.05-.48 10.82-68.25c.1-.65.66-1.13 1.32-1.13h74.86l.74-2.34 2.97 1.88.09.06.01.01c.36.29.57.76.49 1.26l-11.14 70.23c-.1.65-.66 1.13-1.32 1.13h-78.83c-.35 0-.66-.14-.89-.35v.01z" /><path fill="url(#ah)" d="M194.806 276.59h78.84c.66 0 1.22-.48 1.32-1.13l11.14-70.23c.13-.81-.5-1.55-1.32-1.55h-78.84c-.66 0-1.22.48-1.32 1.13l-11.14 70.23c-.13.81.49 1.55 1.32 1.55" /><path fill="#fff" d="M198.483 273.248h72.37c.42 0 .77-.3.84-.72l10.24-64.54a.848.848 0 0 0-.84-.98h-72.37c-.42 0-.77.3-.84.72l-10.24 64.54c-.08.52.32.98.84.98" /><path fill="#DCDCDC" d="M232.569 225.606h37.565c.828 0 1.53-.38 1.655-.91l2.363-4.3c.156-.65-.625-1.24-1.655-1.24h-37.565c-.827 0-1.53.38-1.655.91l-2.363 4.3c-.171.64.609 1.24 1.655 1.24M230.21 235.755h37.565c.827 0 1.53-.38 1.655-.91l2.363-4.3c.156-.65-.625-1.24-1.655-1.24h-37.565c-.828 0-1.53.38-1.655.91l-2.363 4.3c-.172.64.609 1.24 1.655 1.24" /><path fill="#0479FF" d="M226.976 246.513h37.564c.828 0 1.53-.38 1.655-.91l2.363-4.3c.156-.65-.624-1.241-1.655-1.241h-37.564c-.828 0-1.531.381-1.655.911l-2.363 4.3c-.172.64.609 1.24 1.655 1.24" /><g opacity=".4"><path fill="url(#ai)" d="M208.896 257.128h13.95c.41 0 .79-.33.86-.74s-.21-.74-.62-.74h-13.95c-.41 0-.79.33-.86.74s.21.74.62.74" /><path fill="url(#aj)" d="M208.451 259.917h13.95c.41 0 .79-.329.86-.739.06-.41-.21-.74-.62-.74h-13.95c-.41 0-.79.33-.86.74-.07.4.21.739.62.739" /><path fill="url(#ak)" d="M208.014 262.699h13.95c.41 0 .79-.33.86-.74.06-.41-.21-.74-.62-.74h-13.95c-.41 0-.79.33-.86.74s.21.74.62.74" /><path fill="url(#al)" d="M215.285 265.48h6.23c.41 0 .79-.33.86-.74s-.21-.74-.62-.74h-6.23c-.41 0-.79.33-.86.74-.06.41.21.74.62.74" /></g><g opacity=".5"><path fill="url(#am)" d="M212.201 217.661h9.54c.28 0 .54-.23.59-.51.04-.28-.15-.51-.43-.51h-9.54c-.28 0-.54.23-.59.51-.04.29.15.51.43.51" opacity=".4" /><path fill="url(#an)" d="M211.904 219.567h9.54c.28 0 .54-.23.59-.51.04-.28-.15-.51-.43-.51h-9.54c-.28 0-.54.23-.59.51-.04.28.15.51.43.51" opacity=".4" /><path fill="url(#ao)" d="M216.881 221.473h4.26c.28 0 .54-.23.59-.51.04-.28-.15-.51-.43-.51h-4.26c-.28 0-.54.23-.59.51-.04.28.15.51.43.51" opacity=".4" /></g><path fill="url(#ap)" d="m202.953 378.099.6 9.15s5.45 1.9 8.96 0l.94-9.39" /><path fill="url(#aq)" d="M225.175 394.92h-22.58c-.04-.77-.17-2.8.07-4.44.28-1.89.89-3.23.89-3.23 4.25.91 8.96 0 8.96 0s7.3 2.2 10.2 3.86c2.21 1.26 2.55 3.03 2.46 3.81" /><path fill="url(#ar)" d="m154.704 374.43-2.47 8.19s3.46 3.52 7.78 2.83l4.18-10.03" /><path fill="url(#as)" d="M168.265 394.899c-.02.01-.19.02-.21.02h-8.9c-.15-.01-.3-.01-.43-.02-2.29-2.08-10.74-9.24-10.59-9.67.14-.44.88-1.89 2.07-3.62 1.32-1.94 3.17-2.82 3.17-2.82 3.54 2.99 8.1 3.13 8.1 3.13s2.2 5.88 3.45 6.96c1.47.22 4.96 2.15 3.34 6.02" /><path fill="url(#at)" d="M213.457 383.473c-7.29 1.97-10.9-.4-10.9-.4s-2.11-33.46-2.25-39.53c-.15-6.58.34-12.24.34-12.24-3.07-5.63-7.11-21.93-7.11-21.93s-1.42 5.78-2.95 11.44v.01c-.99 3.73-2.041 7.41-2.761 9.37-2.07 5.62-23.808 50.26-23.808 50.26-8.92-.84-10.33-3.67-10.33-3.67s5.839-19.74 10.399-32.7c4.57-12.96 7.511-18.93 7.511-18.93l3.478-34.45s-1.799-4.74-1.599-8.45c.23-4.29 3.151-11.26 3.151-11.26l30.53-1.54 3.368 6.03s6.451 46.82 6.912 53.93c.449 7.12-3.981 54.06-3.981 54.06" /><path fill="url(#au)" d="M216.804 191.116c-.14 1.85-1.05 4.19-1.52 6.97-.34 2.01-1.08 4.69-2.16 6.88s-2.29 3.84-3.59 4.13c-2.65.6-11.92-2.14-14.51-12.23-3.05-11.91 5.54-15.14 9.77-14.54 4.22.62 12.49 2.45 12.01 8.79" /><path fill="#E9CDB2" d="M205.803 203.288s-1.28 4.04-1.66 9.14c0 0-8.07 1.35-12.44-1.02 0 0 4.53-9.22 5-17.26" /><path fill="url(#av)" d="M176.193 232.836s-3.239 11.29-2.819 15.56c.15 1.55 9.64 5.68 9.64 5.68l1.79-16.13z" /><path fill="#0479FF" d="M184.386 240.696c-.21-.01-.42-.02-.62-.04-2.37-.15-4.35-.62-5.88-1.14-2.43-.83-3.73-1.78-3.73-1.78 2.69-11.11 4.94-16.47 4.94-16.47z" /><path fill="url(#aw)" d="M183.763 240.654c-2.37-.15-4.35-.62-5.88-1.14.52-3.44 1.03-8.17 1.03-8.17l1.07 1.46z" /><path fill="#0479FF" d="M210.942 273.306c.17 1.39-.43 2.171-.43 2.171-12.46-3.53-24.49 14.83-38.76 3.19-.58-.48 4.13-8.721 6.59-22.811.79-4.56.95-10.22 1.07-15.78.17-7.62-1.91-14.53-.34-18.69 2.05-5.43 8.87-9.12 11.88-9.66 0-.01 5.22-1.17 12.01.78 5.77 1.66 10.3 9.15 10.3 9.15.21 3.58-1.25 9.84-2.55 16.77-.84 4.47-1.62 9.23-1.83 13.72l-.03.74c-.18 7.05 1.55 16.04 2.09 20.42" /><path fill="url(#ax)" d="M210.713 238.429c-.84 4.47-1.62 9.23-1.83 13.72l-.03.74-.05.14-6.03-20.49 3.02.3z" /><path fill="url(#ay)" d="M242.551 242.203s1.267-1.273 1.922-2.051c.65-.768 1.252-1.662 1.523-1.445.276.208.386.938.197 1.683-.19.745-1.087 2.129-1.087 2.129z" /><path fill="url(#az)" d="M251.772 242.709c-.169-.131-.836-.101-1.52-.147a60 60 0 0 1-1.908-.154l-2.337.244a14 14 0 0 1 1.637-.478c.86-.19 1.639-.331 1.854-.401 1.404-.44 1.058-1.346.36-1.321-.689.03-2.281.004-2.854.115-.959.185-3.403.992-3.609 1.066-.354.123-4.122 3.039-4.777 3.355-.764.374-2.782.478-2.782.478-5.797-.432-17.447-1.472-17.447-1.472l.196-9.712-11.933-3.104s.631 17.309 2.12 20c3.248 5.89 24.102-1.206 29.613-1.312 0 0 2.714.138 4.005-.258.771-.235 1.379-.722 1.766-1.086l.887-.681s-.547 1.315-.736 1.926.25 1.16.591 1.065c.148-.048.565-.756 1.041-1.49.602-.916 1.277-1.908 1.242-2.083-.051-.318-.214-1.187-.214-1.187s.285-.359.171-.597c-.097-.207-.33-.719-.556-.935l2.065-.439s.748-.034 1.884-.15c1.128-.076 1.498-1.043 1.241-1.242" /><path fill="#0479FF" d="M201.478 225.235c-.61 6.12 5.91 17.07 5.91 17.07s6.7-1.18 12.42-6.44c0 0-4.34-11.06-6.53-14.2-2.37-3.38-11.19-2.52-11.8 3.57" /><path fill="#192139" d="M219.787 189.997c.8-2.14.33-4.49-3.55-4.58-4.52-7.59-13.27-5.97-15.96-5.29-7.25 1.75-11.05 9.94-10.24 15.97.94 7.01 2.82 8.21 3.54 9.18.71.96 6.58 2.42 9.54 1 .97-.47 3.88-9.11 4.21-10.04.08-.22.15-.46.32-.62.27-.24.69-.19 1.05-.17h.24c-.05.89.08 2.01 1.15 1.74.93-.23 1.59-1.09 2-1.84 5.11-.93 7.2-4 7.7-5.35" /><path fill="url(#aA)" d="M210.101 197.066s.02-3.5-2.53-3.03c-2.26.42-2.73 6.91-.64 7.38s3.34-1.69 3.17-4.35" /><path fill="#192139" d="M191.264 188.234s-9.97 13.69-6.6 23.14c1.44 4.03 19.46 8.68 24.15 1.43-4.02-1.23-2.92-12.54-2.92-12.54z" /><path fill="url(#aB)" d="M193.536 309.363s-1.42 5.78-2.95 11.44v-21.6z" opacity=".5" /><path fill="url(#aC)" d="m86.974 378.367-.38 9.46s4.1 1.73 7.31 0l2.39-9.01" /><path fill="url(#aD)" d="m58.204 375.266-2.36 11.26s4.55 2.24 8.11.7l2.93-9.35" /><path fill="url(#aE)" d="M85.296 395.048h19.98c.24 0 .44-.22.38-.46-.18-.82-1.36-2.13-3.63-3.3-2.35-1.22-8.13-3.46-8.13-3.46s-4.11.73-7.31 0c0 0-1.14 1.16-1.4 2.89s.11 3.94.11 4.33" /><path fill="url(#aF)" d="m121.383 223.477 14.52 21.23s1.51 6.15-5.52 5.38-17.68-18.06-17.68-18.06" /><path fill="#0479FF" d="M126.489 230.479s-3.94 6.32-10.4 10.04l-9.95-9.61-.28-21.94s5.66 2.31 11.41 9.09c2.27 2.69 4.66 5.7 9.22 12.42" /><path fill="url(#aG)" d="m115.713 223.992 3.06 6.86-6.07-1.17z" /><path fill="url(#aH)" d="M77.427 265.117s-7.1 21.89-9.48 41.25-1.48 14.86-2.27 19.83c-.71 4.48-1.81 10.93-3.87 20.24-2.07 9.31-5.19 35.08-5.19 35.08s4.31 1.87 9.06 1.2c0 0 11.49-29.54 13.1-37.85 1.62-8.38 2.12-17.52 2.12-17.52s8.72-17.51 12.13-30.13l1.83-.52-.35 34.49s-5.57 12.62-6.48 22.92c-.91 10.32-1.43 28.92-1.43 28.92s6.56 1.99 8.79.45c0 0 11.12-42.46 13.81-53.12 2.69-10.65 6.97-35.05 7.25-39.92 1-17.17-1.46-24.5-1.46-24.5z" /><path fill="url(#aI)" d="M64.066 386.844s-4.38.56-8.17-.61l-.51 1.58s-3.74 1.27-5.16 2.82c-2.17 2.35-1.21 4.4-1.21 4.4h16.18c.01.01.86-3.98-1.13-8.19" /><path fill="#0479FF" d="M115.37 264.787c-.23 2-1.3 2.451-1.3 2.451s-7.84 4.089-37.19-.401c0 0-1.05-.2-.65-2.14 1.05-5.04 1.69-11.1 1.4-15.5-.1-1.57-.33-2.92-.69-3.94-1.09-3.08-2.69-7.399-4.03-11.879-1.63-5.41-2.87-11.051-2.38-15.011 1.01-8.14 15.23-11.569 15.23-11.569l12.18-.11s10.73 1.089 15.74 8.649c3.89 5.87 2.03 26.02 1.11 39.59-.27 4.01.84 7.56.58 9.86" /><path fill="url(#aJ)" d="m81.296 231.938-2.19 17.259h-1.48c-.1-1.57-.33-2.92-.69-3.94-1.09-3.08-2.69-7.399-4.03-11.879z" /><path fill="#192139" d="M103.106 179.406s2 7.59-.93 9.02c-2.38 1.16-2.59-2.23-2.59-2.23l-.57-3.7" /><path fill="url(#aK)" d="M85.758 206.798s1.99 5.15 9.55 4.41c6.57-.64 2.64-4.52 2.64-4.52s-.34-1.04-.76-2.41c-.41-1.34-.89-3-1.19-4.3l-2.63-2.15-5.58-4.57s.72 9.92-2.03 13.54" /><path fill="url(#aL)" d="M89.953 197.876s2.28 4.86 7.23 6.41c-.41-1.34-.89-3-1.19-4.3l-2.63-2.15z" /><path fill="url(#aM)" d="M101.985 183.826s3.16 15.22-1.26 17.61c0 0-5.01 1.87-10.24-2.37-1.51-1.22-2.71-2.77-3.63-4.48-1.51-2.77-3.96-7.96-3.44-11.59.73-5.18 15.48-10.54 18.57.83" /><path fill="url(#aN)" d="M90.503 190.732s-.9-2.8-2.86-1.98c-2.58 1.08.26 7.68 2.8 5.84" /><path fill="#192139" d="M82.435 178.756c3.29-6.54 9.38-3.42 16.19-4.07 2.31-2.22 4.74-2.34 5.98-1.7 1.7.88 1.61 4.25 1.46 5.69-.92 8.96-10.8 8.28-13.68 7.88-.08.68-.26 1.32-.65 1.87-.38.55-1.03.91-1.82 1.16-.35-.5-.86-.97-1.54-.98-.22-.01-.47.04-.73.15-.54.22-.84.69-.95 1.27-.24 1.14.43 2.89 1.24 3.94.04 1.96-.13 3.68-.13 4.86 0 0-1.57-2.61-2.57-3.78-.55-.63-2.1-2.72-3.04-5.46-.01 0-.01 0-.01-.01-.59-1-4.4-7.71.25-10.82" /><path fill="url(#aO)" d="m115.405 259.663-21.58-.6c-1.04-.03-1.76-1.06-1.42-2.04l10.03-29.63a2.7 2.7 0 0 1 2.63-1.83l21.58.6c1.04.03 1.76 1.06 1.42 2.04l-10.03 29.63a2.705 2.705 0 0 1-2.63 1.83" /><path fill="url(#aP)" d="M106.58 243.338c-.18-.48-1.14-.29-1.43-.18-.28.1-.62.26-1.13.48-.12.05-.25.11-.39.17.79-.46 1.34-.79 1.15-1.28-.18-.48-1.14-.29-1.43-.18-.28.1-.62.26-1.13.48-.73.31-1.82.79-2.47 1.07-1.15.18-2.56.57-2.55.51.06-.42.33-.94.41-1.41.2-.44.89-2.12.45-2.67-.5-.62-.85-.37-1.24.43-.38.8-1.53 1.95-1.93 2.41-.41.46-.46.92-1 1.56-.66.79-1.63 1.13-2.2 1.27-1.05.22-10.39.58-16.88.77 1.87-6.36 3.12-15.89 3.12-15.89l-12.22-4.62s-4.56 27.41-3.09 29.91c1.06 1.8 3.84 2.66 6.68 2.35 3.77-.41 11.83-3.42 23.75-6.79 1.88-.25 3.11-.47 5.52-.78.75-.1 1.52-.29 2.14-.54 0 0 .02-.01.05-.02.05-.02.1-.04.15-.07.41-.18 1.38-.63 2.21-1.14 1.09-.68 2.39-1.53 2.17-2.13-.11-.29-.5-.33-.86-.3.99-.61 1.83-1.14 1.62-1.69-.09-.23-.36-.3-.64-.31.83-.51 1.37-.91 1.17-1.41" /><path fill="#0479FF" d="M68.95 216.739c1.48-2.78 7.32-5.95 7.32-5.95l5.87 13.83c.07 4.91-2.98 17.72-2.98 17.72-1.73.15-5.54-.02-8.49-.47-3.53-.56-7.31-1.75-7.31-1.75s2-16.65 5.59-23.38" /><path fill="#fff" d="m164.57 204.868-36.12 4.53c-2.6.33-4.97-1.52-5.3-4.11l-.69-5.49a4.75 4.75 0 0 1 4.11-5.3l36.12-4.53c2.6-.33 4.97 1.52 5.29 4.11l.69 5.49c.34 2.6-1.5 4.97-4.1 5.3" /><path fill="url(#aQ)" d="m131.946 205.41-2.97.37a2.5 2.5 0 0 1-2.79-2.17l-.37-2.97a2.5 2.5 0 0 1 2.17-2.79l2.97-.37a2.5 2.5 0 0 1 2.79 2.17l.37 2.97a2.5 2.5 0 0 1-2.17 2.79" /><path fill="url(#aR)" d="m163.033 195.96-26.8 3.37a.394.394 0 0 1-.44-.34v-.02c-.03-.22.13-.41.34-.44l26.8-3.37c.22-.03.41.13.44.34v.02a.4.4 0 0 1-.34.44" /><path fill="url(#aS)" d="m147.569 199.837-11.11 1.39a.394.394 0 0 1-.44-.34v-.02c-.03-.22.13-.41.34-.44l11.11-1.4c.22-.03.41.13.44.34v.02c.03.22-.12.42-.34.45" /><path fill="url(#aT)" d="m163.27 197.866-14.1 1.77a.394.394 0 0 1-.44-.34v-.02c-.03-.22.13-.41.34-.44l14.1-1.77c.22-.03.41.13.44.34v.02a.4.4 0 0 1-.34.44" /><path fill="url(#aU)" d="m151.191 201.319-14.49 1.82a.394.394 0 0 1-.44-.34v-.02c-.03-.22.13-.41.34-.44l14.49-1.82c.22-.03.41.13.44.34v.02c.03.22-.12.42-.34.44" /><path fill="url(#aV)" d="m138.244 208.168-.1-.05s.29-.81.54-1.73c.23-.45.85-1.43.85-1.99 0-1.73-1.32-.54-2.06.24-1.9 1.61-3.25 3.06-3.25 4.66 0 3.03-.1 5.4-.1 5.41-2.52 7.23-7.64 21.93-7.64 21.93l9.1 11.52s1.86-3.1 2.51-17.74c.25-5.64 1.36-11.76 1.83-14.21.01-.01 2.25-2.65 2.98-4.52.46-.66.87-1.26 1.08-1.54.62-.83 1.04-2.82 1.04-2.82z" /><path fill="#fff" d="M304.425 160.341h-50.49c-2.62 0-4.74-2.12-4.74-4.74v-10.15c0-2.62 2.12-4.74 4.74-4.74h50.49c2.62 0 4.74 2.12 4.74 4.74v10.15c.01 2.62-2.12 4.74-4.74 4.74" /><path fill="url(#aW)" d="M261.346 155.745h-5.44a2.5 2.5 0 0 1-2.5-2.5v-5.44a2.5 2.5 0 0 1 2.5-2.5h5.44a2.5 2.5 0 0 1 2.5 2.5v5.44a2.507 2.507 0 0 1-2.5 2.5" /><path fill="url(#aX)" d="M302.412 148.529h-35.28c-.29 0-.53-.24-.53-.53s.24-.53.53-.53h35.28c.29 0 .53.24.53.53-.01.3-.24.53-.53.53" /><path fill="url(#aY)" d="M281.734 151.052h-14.61c-.29 0-.53-.24-.53-.53s.24-.53.53-.53h14.61c.29 0 .53.24.53.53s-.24.53-.53.53" /><path fill="url(#aZ)" d="M302.416 151.052h-18.55c-.29 0-.53-.24-.53-.53s.24-.53.53-.53h18.55c.29 0 .53.24.53.53a.54.54 0 0 1-.53.53" /><path fill="url(#ba)" d="M286.184 153.568h-19.06c-.29 0-.53-.24-.53-.53s.24-.53.53-.53h19.06c.29 0 .53.24.53.53s-.24.53-.53.53" /><path fill="url(#bb)" d="M60.248 202.905H9.578a6.5 6.5 0 0 1-6.5-6.5v-49.28a6.5 6.5 0 0 1 6.5-6.5h50.67a6.5 6.5 0 0 1 6.5 6.5v49.28c.01 3.59-2.91 6.5-6.5 6.5" /><path stroke="#fff" stroke-miterlimit="10" stroke-width=".5" d="M44.49 164.359H16.75c-3.35 0-6.07-2.72-6.07-6.07s2.72-6.07 6.07-6.07h27.74c3.35 0 6.07 2.72 6.07 6.07.01 3.35-2.71 6.07-6.07 6.07ZM53.076 183.906h-27.74c-3.35 0-6.07-2.72-6.07-6.07s2.72-6.07 6.07-6.07h27.74c3.35 0 6.07 2.72 6.07 6.07s-2.71 6.07-6.07 6.07Z" /><path fill="#fff" d="M45.506 155.952h-28.37c-.41 0-.73-.33-.73-.73 0-.41.33-.73.73-.73h28.36c.41 0 .73.33.73.73.01.4-.32.73-.72.73M45.506 159.015h-28.37c-.41 0-.73-.33-.73-.73 0-.41.33-.73.73-.73h28.36c.41 0 .73.33.73.73.01.4-.32.73-.72.73M30.586 162.085h-13.45c-.41 0-.73-.33-.73-.73 0-.41.33-.73.73-.73h13.45c.41 0 .73.33.73.73.01.4-.32.73-.73.73M24.597 175.507h28.36c.41 0 .73-.33.73-.73 0-.41-.33-.73-.73-.73h-28.36c-.41 0-.73.33-.73.73s.33.73.73.73M35.3 178.569h17.67c.41 0 .73-.33.73-.73 0-.41-.33-.73-.73-.73H35.3c-.41 0-.73.33-.73.73-.01.41.32.73.73.73M24.597 178.569h7.57c.41 0 .73-.33.73-.73 0-.41-.33-.73-.73-.73h-7.57c-.41 0-.73.33-.73.73 0 .41.33.73.73.73M39.52 181.64h13.45c.41 0 .73-.33.73-.73 0-.41-.33-.73-.73-.73H39.52c-.41 0-.73.33-.73.73-.01.4.32.73.73.73M13.52 192.006a1.419 1.419 0 1 1-2.84 0c0-.78.63-1.42 1.42-1.42.78.01 1.42.64 1.42 1.42M19.246 192.006a1.42 1.42 0 1 1-1.42-1.42c.78.01 1.42.64 1.42 1.42M24.973 192.006a1.419 1.419 0 1 1-1.42-1.42c.79 0 1.42.64 1.42 1.42" /><path stroke="#232230" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M17.984 395.398h519.5" /><path stroke="#fff" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M99.102 162.078v-2.5" /><path stroke="#fff" stroke-dasharray="5.01 5.01" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M99.102 153.571v-23.72c0-16.06 13.02-29.07 29.07-29.07h239.73c16.06 0 29.08 13.02 29.08 29.07v71.66" /><path stroke="#fff" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M396.984 204.523v2.5" /><path stroke="#fff" stroke-dasharray="5 5" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" d="M204.336 100.781v70.91M325.508 100.781v61.38" /><defs><linearGradient id="a" x1="277.503" x2="277.503" y1="398.096" y2="-38.89" gradientUnits="userSpaceOnUse"><stop stop-color="#DFEDF2" /><stop offset="1" stop-color="#E8F8FF" /></linearGradient><linearGradient id="b" x1="106.302" x2="106.302" y1="399.445" y2="-37.543" gradientUnits="userSpaceOnUse"><stop stop-color="#DFEDF2" /><stop offset="1" stop-color="#E8F8FF" /></linearGradient><linearGradient id="c" x1="562.306" x2="562.306" y1="399.447" y2="-37.54" gradientUnits="userSpaceOnUse"><stop stop-color="#DFEDF2" /><stop offset="1" stop-color="#E8F8FF" /></linearGradient><linearGradient id="d" x1="411.338" x2="411.338" y1="396.529" y2="8.267" gradientUnits="userSpaceOnUse"><stop stop-color="#DFEDF2" /><stop offset="1" stop-color="#E8F8FF" /></linearGradient><linearGradient id="e" x1="41.976" x2="41.976" y1="407.852" y2="-235.123" gradientUnits="userSpaceOnUse"><stop stop-color="#DFEDF2" /><stop offset="1" stop-color="#E8F8FF" /></linearGradient><linearGradient id="f" x1="298.747" x2="294.747" y1="345.651" y2="222.318" gradientUnits="userSpaceOnUse"><stop stop-color="#B6DFF6" /><stop offset="1" stop-color="#DDEAF7" /></linearGradient><linearGradient id="g" x1="308.331" x2="328.891" y1="326.794" y2="326.794" gradientUnits="userSpaceOnUse"><stop stop-color="#B6DFF6" /><stop offset="1" stop-color="#DDEAF7" /></linearGradient><linearGradient id="h" x1="252.424" x2="270.812" y1="61.718" y2="87.664" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="i" x1="246.35" x2="263.2" y1="67.195" y2="90.973" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="j" x1="452.595" x2="461.789" y1="165.789" y2="178.762" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="k" x1="449.554" x2="457.98" y1="168.52" y2="180.41" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="l" x1="58.739" x2="37.489" y1="252.336" y2="218.836" gradientUnits="userSpaceOnUse"><stop stop-color="#DFEDF2" /><stop offset="1" stop-color="#E8F8FF" /></linearGradient><linearGradient id="m" x1="54.38" x2="33.13" y1="255.104" y2="221.604" gradientUnits="userSpaceOnUse"><stop stop-color="#DFEDF2" /><stop offset="1" stop-color="#E8F8FF" /></linearGradient><linearGradient id="n" x1="396.985" x2="396.985" y1="394.953" y2="205.262" gradientUnits="userSpaceOnUse"><stop stop-color="#256ABA" /><stop offset="1" stop-color="#5CA5F7" /></linearGradient><linearGradient id="o" x1="322.263" x2="350.596" y1="366.338" y2="395.004" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="p" x1="330.429" x2="358.762" y1="358.265" y2="386.932" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="q" x1="471.918" x2="353.993" y1="375.284" y2="375.284" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="r" x1="471.914" x2="353.991" y1="375.264" y2="375.264" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="s" x1="478.999" x2="478.999" y1="369.819" y2="380.45" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="t" x1="396.985" x2="396.985" y1="394.953" y2="205.262" gradientUnits="userSpaceOnUse"><stop stop-color="#256ABA" /><stop offset="1" stop-color="#5CA5F7" /></linearGradient><linearGradient id="u" x1="340.84" x2="320.84" y1="353.906" y2="324.906" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="v" x1="438.371" x2="438.371" y1="332.207" y2="345.013" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="w" x1="478.999" x2="478.999" y1="334.315" y2="344.945" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="x" x1="396.985" x2="396.985" y1="394.947" y2="205.256" gradientUnits="userSpaceOnUse"><stop stop-color="#256ABA" /><stop offset="1" stop-color="#5CA5F7" /></linearGradient><linearGradient id="y" x1="323.527" x2="363.492" y1="304.244" y2="304.244" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="z" x1="340.877" x2="353.577" y1="304.244" y2="304.244" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="A" x1="438.371" x2="438.371" y1="296.695" y2="309.501" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="B" x1="463.752" x2="434.086" y1="315.245" y2="290.245" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="C" x1="478.999" x2="478.999" y1="298.803" y2="309.433" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="D" x1="396.985" x2="396.985" y1="394.947" y2="205.256" gradientUnits="userSpaceOnUse"><stop stop-color="#256ABA" /><stop offset="1" stop-color="#5CA5F7" /></linearGradient><linearGradient id="E" x1="323.527" x2="363.492" y1="268.738" y2="268.738" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="F" x1="462.538" x2="427.204" y1="282.669" y2="262.336" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="G" x1="465.611" x2="430.278" y1="277.328" y2="256.994" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="H" x1="478.999" x2="478.999" y1="263.296" y2="273.927" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="I" x1="396.985" x2="396.985" y1="394.95" y2="205.259" gradientUnits="userSpaceOnUse"><stop stop-color="#256ABA" /><stop offset="1" stop-color="#5CA5F7" /></linearGradient><linearGradient id="J" x1="450.726" x2="450.726" y1="225.684" y2="238.49" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="K" x1="478.999" x2="478.999" y1="227.792" y2="238.423" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="L" x1="339.451" x2="340.307" y1="277.803" y2="296.852" gradientUnits="userSpaceOnUse"><stop stop-color="#1A2F79" /><stop offset="1" stop-color="#160545" /></linearGradient><linearGradient id="M" x1="314.629" x2="314.629" y1="245.453" y2="269.681" gradientUnits="userSpaceOnUse"><stop stop-color="#1A2F79" /><stop offset="1" stop-color="#160545" /></linearGradient><linearGradient id="N" x1="316.268" x2="316.268" y1="239.72" y2="250.917" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="O" x1="336.091" x2="336.091" y1="191.602" y2="196.613" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="P" x1="325.49" x2="325.49" y1="196.023" y2="267.159" gradientUnits="userSpaceOnUse"><stop stop-color="#1A2F79" /><stop offset="1" stop-color="#160545" /></linearGradient><linearGradient id="Q" x1="334.636" x2="334.636" y1="210.488" y2="222.069" gradientUnits="userSpaceOnUse"><stop stop-color="#283770" /><stop offset="1" stop-color="#141A5A" /></linearGradient><linearGradient id="R" x1="342.3" x2="342.646" y1="274.788" y2="282.486" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="S" x1="350.897" x2="350.897" y1="196.943" y2="269.97" gradientUnits="userSpaceOnUse"><stop stop-color="#1A2F79" /><stop offset="1" stop-color="#160545" /></linearGradient><linearGradient id="T" x1="338.793" x2="338.793" y1="165.423" y2="173.65" gradientUnits="userSpaceOnUse"><stop stop-color="#6EC4F7" /><stop offset="1" stop-color="#398EF6" /></linearGradient><linearGradient id="U" x1="356.82" x2="351.674" y1="135.679" y2="158.721" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="V" x1="354.814" x2="345.032" y1="142.292" y2="149.339" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="W" x1="350.135" x2="344.975" y1="134.151" y2="157.251" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="X" x1="356.101" x2="350.955" y1="135.518" y2="158.561" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="Y" x1="366.315" x2="366.315" y1="161.878" y2="194.155" gradientUnits="userSpaceOnUse"><stop stop-color="#6EC4F7" /><stop offset="1" stop-color="#398EF6" /></linearGradient><linearGradient id="Z" x1="293.829" x2="351.854" y1="152.238" y2="174.18" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aa" x1="326.097" x2="326.097" y1="161.694" y2="198.891" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="ab" x1="311.432" x2="317.187" y1="192.008" y2="194.719" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="ac" x1="322.306" x2="325.272" y1="187.653" y2="197.768" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="ad" x1="237.883" x2="237.883" y1="230.444" y2="302.601" gradientUnits="userSpaceOnUse"><stop stop-color="#8BBDF2" /><stop offset="1" stop-color="#92C2E8" /></linearGradient><linearGradient id="ae" x1="235.744" x2="235.744" y1="269.111" y2="377.243" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="af" x1="217.817" x2="259.567" y1="274.659" y2="289.659" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="ag" x1="241.604" x2="241.604" y1="276.689" y2="215.691" gradientUnits="userSpaceOnUse"><stop stop-color="#8BBDF2" /><stop offset="1" stop-color="#92C2E8" /></linearGradient><linearGradient id="ah" x1="239.793" x2="239.793" y1="276.588" y2="206.457" gradientUnits="userSpaceOnUse"><stop stop-color="#DFEDF2" /><stop offset="1" stop-color="#E8F8FF" /></linearGradient><linearGradient id="ai" x1="270.955" x2="250.262" y1="225.504" y2="233.094" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="aj" x1="271.721" x2="251.028" y1="227.605" y2="235.196" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="ak" x1="272.494" x2="251.801" y1="229.709" y2="237.298" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="al" x1="273.926" x2="253.233" y1="233.61" y2="241.2" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="am" x1="276.737" x2="255.439" y1="183.895" y2="191.562" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="an" x1="277.26" x2="255.963" y1="185.336" y2="193.003" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="ao" x1="278.218" x2="256.92" y1="188.012" y2="195.679" gradientUnits="userSpaceOnUse"><stop stop-color="#71D2F9" /><stop offset="1" stop-color="#51B9F8" /></linearGradient><linearGradient id="ap" x1="202.957" x2="213.458" y1="382.973" y2="382.973" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aq" x1="202.525" x2="225.189" y1="391.088" y2="391.088" gradientUnits="userSpaceOnUse"><stop stop-color="#1A2F79" /><stop offset="1" stop-color="#160545" /></linearGradient><linearGradient id="ar" x1="152.24" x2="164.199" y1="379.978" y2="379.978" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="as" x1="148.141" x2="168.686" y1="386.858" y2="386.858" gradientUnits="userSpaceOnUse"><stop stop-color="#1A2F79" /><stop offset="1" stop-color="#160545" /></linearGradient><linearGradient id="at" x1="185.574" x2="185.574" y1="384.253" y2="273.818" gradientUnits="userSpaceOnUse"><stop stop-color="#283770" /><stop offset="1" stop-color="#141A5A" /></linearGradient><linearGradient id="au" x1="205.559" x2="205.814" y1="187.785" y2="207.577" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="av" x1="173.338" x2="184.811" y1="243.45" y2="243.45" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aw" x1="180.825" x2="180.825" y1="223.92" y2="253.638" gradientUnits="userSpaceOnUse"><stop stop-color="#6EC4F7" /><stop offset="1" stop-color="#398EF6" /></linearGradient><linearGradient id="ax" x1="206.745" x2="206.745" y1="220.091" y2="275.537" gradientUnits="userSpaceOnUse"><stop stop-color="#6EC4F7" /><stop offset="1" stop-color="#398EF6" /></linearGradient><linearGradient id="ay" x1="246.292" x2="245.118" y1="238.408" y2="240.287" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2B091" /></linearGradient><linearGradient id="az" x1="205.294" x2="245.963" y1="233.84" y2="254.595" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aA" x1="207.723" x2="207.979" y1="187.769" y2="207.609" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aB" x1="190.588" x2="193.537" y1="310.003" y2="310.003" gradientUnits="userSpaceOnUse"><stop stop-color="#283770" /><stop offset="1" stop-color="#141A5A" /></linearGradient><linearGradient id="aC" x1="96.288" x2="86.59" y1="383.483" y2="383.483" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aD" x1="66.5" x2="56.313" y1="382.149" y2="381.236" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aE" x1="105.67" x2="85.096" y1="391.436" y2="391.436" gradientUnits="userSpaceOnUse"><stop stop-color="#1A2F79" /><stop offset="1" stop-color="#160545" /></linearGradient><linearGradient id="aF" x1="129.244" x2="123.681" y1="196.851" y2="245.234" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aG" x1="115.738" x2="115.738" y1="223.472" y2="235.32" gradientUnits="userSpaceOnUse"><stop stop-color="#6EC4F7" /><stop offset="1" stop-color="#398EF6" /></linearGradient><linearGradient id="aH" x1="86.645" x2="86.645" y1="379.949" y2="232.233" gradientUnits="userSpaceOnUse"><stop stop-color="#283770" /><stop offset="1" stop-color="#141A5A" /></linearGradient><linearGradient id="aI" x1="48.814" x2="65.386" y1="390.641" y2="390.641" gradientUnits="userSpaceOnUse"><stop stop-color="#1A2F79" /><stop offset="1" stop-color="#160545" /></linearGradient><linearGradient id="aJ" x1="77.103" x2="77.103" y1="230.617" y2="260.459" gradientUnits="userSpaceOnUse"><stop stop-color="#6EC4F7" /><stop offset="1" stop-color="#398EF6" /></linearGradient><linearGradient id="aK" x1="100.171" x2="91.061" y1="182.109" y2="208.074" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aL" x1="97.179" x2="89.953" y1="201.051" y2="201.051" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aM" x1="95.021" x2="89.507" y1="180.933" y2="207.892" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aN" x1="91.064" x2="85.55" y1="180.12" y2="207.079" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aO" x1="124.479" x2="94.69" y1="217.793" y2="269.675" gradientUnits="userSpaceOnUse"><stop stop-color="#6EC4F7" /><stop offset="1" stop-color="#489FF6" /></linearGradient><linearGradient id="aP" x1="14.506" x2="117.888" y1="226.403" y2="258.411" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aQ" x1="161.031" x2="126.199" y1="205.211" y2="201.208" gradientUnits="userSpaceOnUse"><stop stop-color="#6EC4F7" /><stop offset="1" stop-color="#489FF6" /></linearGradient><linearGradient id="aR" x1="161.793" x2="126.96" y1="198.641" y2="194.638" gradientUnits="userSpaceOnUse"><stop stop-color="#6EC4F7" /><stop offset="1" stop-color="#489FF6" /></linearGradient><linearGradient id="aS" x1="161.362" x2="126.529" y1="202.359" y2="198.356" gradientUnits="userSpaceOnUse"><stop stop-color="#6EC4F7" /><stop offset="1" stop-color="#489FF6" /></linearGradient><linearGradient id="aT" x1="161.75" x2="126.917" y1="198.989" y2="194.985" gradientUnits="userSpaceOnUse"><stop stop-color="#6EC4F7" /><stop offset="1" stop-color="#489FF6" /></linearGradient><linearGradient id="aU" x1="161.197" x2="126.364" y1="203.819" y2="199.816" gradientUnits="userSpaceOnUse"><stop stop-color="#6EC4F7" /><stop offset="1" stop-color="#489FF6" /></linearGradient><linearGradient id="aV" x1="136.717" x2="134.528" y1="200.454" y2="249.954" gradientUnits="userSpaceOnUse"><stop stop-color="#F4CFB3" /><stop offset="1" stop-color="#F2BB9B" /></linearGradient><linearGradient id="aW" x1="336.606" x2="221.997" y1="184.807" y2="134.426" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="aX" x1="340.114" x2="227.971" y1="172.334" y2="123.038" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="aY" x1="337.388" x2="225.452" y1="178.199" y2="128.993" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="aZ" x1="340.5" x2="228.493" y1="171.345" y2="122.107" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="ba" x1="336.873" x2="224.857" y1="179.507" y2="130.267" gradientUnits="userSpaceOnUse"><stop stop-color="#CBE1F2" /><stop offset="1" stop-color="#AED2ED" /></linearGradient><linearGradient id="bb" x1="100.645" x2="-24.237" y1="229.649" y2="119.669" gradientUnits="userSpaceOnUse"><stop stop-color="#B6DFF6" /><stop offset="1" stop-color="#DDEAF7" /></linearGradient></defs></svg>
            </div>
           
           
        </div>
        <div class="col-md-5 col-lg-5 position-relative h-100">
            <div class="d-grid align-items-center justify-content-center h-100">
                <div class="card login_card" style="width:30rem;">
                    <div class="d-flex align-items-end p-3 card-header">
                        <img src="~/img/logo/cplogo.svg" title="CP Logo" alt="Logo" width="320" />
                    </div>
                    <div class="card-body pb-0">
                        <div class="wizard-content" id="basicWizard">
                            <form id="example-form" asp-controller="Basic" asp-action="Configuration" class="tab-wizard wizard-circle wizard clearfix example-form icons-tab-steps checkout-tab-steps">
                                <h6 id="company">
                                    <span class="step">
                                        <i class="cp-company"></i>
                                    </span>
                                    <span class="step_title">
                                        Company Information
                                    </span>
                                </h6>
                                <section>
                                    <div class="row justify-content-center">
                                        <div class="col-10">
                                            <div class="form-group">
                                                <div class="form-label">Company Name</div>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="cp-company"></i></span>
                                                    <input asp-for="Name" type="text" class="form-control" id="textName" placeholder="Enter Company Name" maxlength="100" autocomplete="off" />
                                                </div>
                                                <span asp-validation-for="Name" id="Name-error"></span>
                                            </div>
                                        </div>
                                        <div class="col-10"></div>


                                        <div class="col-10">
                                            <div class="form-group">
                                                <div class="form-label">Display Name</div>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="cp-display-name"></i></span>
                                                    <input asp-for="DisplayName" type="text" class="form-control" id="textDisplayName" placeholder="Enter Display Name" maxlength="15" autocomplete="off" />
                                                </div>
                                                <span asp-validation-for="DisplayName" id="DisplayName-error"></span>
                                            </div>
                                        </div>
                                        <div class="col-10"></div>


                                        <div class="col-10">
                                            <div class="form-group">
                                                <div class="form-label">Web Address</div>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="cp-web"></i></span>
                                                    <input asp-for="WebAddress" type="text" class="form-control" id="textWebAddress" placeholder="Enter Web Address" maxlength="99" autocomplete="off" />
                                                </div>
                                                <span asp-validation-for="WebAddress" id="WebAddress-error"></span>
                                            </div>
                                        </div>
                                        <div class="col-10"></div>
                                    </div>



                                </section>
                                <input asp-for="Id" type="hidden" id="textCompanyId" class="form-control" />
                                <h6 id="login">
                                    <span class="step">
                                        <i class="cp-logs"></i>
                                    </span>
                                    <span class="step_title">
                                        Login Information
                                    </span>
                                </h6>
                                <section>
                                    <div class="row">
                                        <div class="col-11">
                                            <div class="form-group">
                                                <div class="form-label">Login Name</div>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="cp-user"></i></span>
                                                    <input asp-for="LoginName" id="textLoginName" type="text" class="form-control" placeholder="Enter Login Name" maxlength="100" autocomplete="off" />
                                                </div>
                                                <span asp-validation-for="LoginName" id="LoginName-error"></span>
                                            </div>
                                        </div>
                                        <div class="col-10"></div>


                                        <div class="col-11">
                                            <div class="form-group position-relative">
                                                <div class="form-label">Login Password</div>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="cp-lock"></i></span>
                                                    <input asp-for="LoginPassword" id="Password" type="password" class="form-control" placeholder="Enter Login Password" maxlength="40" autocomplete="off" />
                                                    <span class="input-group-text toggle-password"><i class="cp-password-visible fs-6"></i></span>
                                                </div>
                                                <span asp-validation-for="LoginPassword" id="LoginPassword-error"></span>

                                            </div>
                                        </div>
                                        <div class="col-11">
                                            <div class="form-group">
                                                <div class="form-label">Confirm Password</div>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="cp-lock"></i></span>
                                                    <input asp-for="ConfrimPassword" id="textConfirmPassword" type="password" autocomplete="off" maxlength="40" class="form-control" placeholder="Enter Confirm Password" />

                                                </div>
                                                <span asp-validation-for="ConfrimPassword" id="ConfirmPassword-error"></span>
                                            </div>
                                        </div>
                                        <div class="col-10"></div>
                                    </div>



                                </section>
                                <input asp-for="Id" type="hidden" id="textLoginId" class="form-control" />
                            </form>
                        </div>
                    </div>
                    <div class="card-footer wizard-btn text-end">
                        <div id="mycPass_strength_wrap" class="mt-4" style="top: 83px;z-index: 999;">
                            <div id="passwordDescription">Password not entered</div>
                            <div id="passwordStrength" class="strength0"></div>
                            <div id="pswd_info">
                                <strong>Strong Password Tips:</strong>
                                <ul>
                                    <li class="invalid" id="length">At least 8 characters</li>
                                    <li class="invalid" id="pnum">At least one number</li>
                                    <li class="invalid" id="capital">At least one lowercase &amp; one uppercase letter</li>
                                    <li class="invalid" id="spchar">At least one special character</li>
                                </ul>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-11">
                                <button type="button" class="btn btn-secondary btn-sm" id="cancel" data-bs-dismiss="modal">Reset</button>
                                <button class="btn btn-primary prev_btn btn-sm" href="javascript:void(0)" role="menuitem">Previous</button>
                                <button class="btn btn-primary next_btn btn-sm" href="javascript:void(0)" role="menuitem">Next</button>
                                <button id="save" class="btn btn-primary finish_btn btn-sm" href="javascript:void(0)" role="menuitem">Save</button>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <footer class="text-center p-1 position-absolute bottom-0 end-0 start-0">
                @{
                    var version = Configuration.GetValue<string>("CP:Version");
                    var isCOE = Configuration.GetValue<string>("Release:isCOE");
                }

                @if (@isCOE != null)
                {
                    <small>Continuity Patrol Version <span>@version</span> | <span class="fw-bold">CoE Release</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
                }
                else
                {
                    <small>Continuity Patrol Version <span>@version</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
                }

                @* <small>Continuity Patrol Version <span class="cpVersionData"></span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small> *@
            </footer>
        </div>
    </div>
</div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
 <script>

        //disable back button in browser
        function preventBack() {
            window.history.forward();
        }
        setTimeout("preventBack()", 0);
        window.onunload = function () { null }
    </script>
<script src="~/js/common/wizard.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>
<script src="~/js/configuration/basiccompany/basiccompany.js"></script>
<script src="~/js/common/password_stregnth_meter.js"></script>
