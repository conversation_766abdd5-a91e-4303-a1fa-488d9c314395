﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class MSSQLAlwaysOnMonitorStatusRepository : BaseRepository<MSSQLAlwaysOnMonitorStatus>,
    IMssqlAlwaysOnMonitorStatusRepository
{
    private readonly ApplicationDbContext _dbContext;

    public MSSQLAlwaysOnMonitorStatusRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<List<MSSQLAlwaysOnMonitorStatus>> GetDetailByType(string type)
    {
        return await _dbContext.MssqlAlwaysOnMonitorStatus.Active().Where(x => x.Type.Equals(type)).ToListAsync();
    }

    public async Task<MSSQLAlwaysOnMonitorStatus> GetMSSQLAlwaysOnMonitorStatusByInfraObjectId(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObjectId", "InfraObjectId cannot be invalid");

        return await _dbContext.MssqlAlwaysOnMonitorStatus
            .Where(x => x.InfraObjectId.Equals(infraObjectId))
            //.Select(x => new MYSQLMonitorStatus { ReferenceId = x.ReferenceId })
            .FirstOrDefaultAsync();
    }
}