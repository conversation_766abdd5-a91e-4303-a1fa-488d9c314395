﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class SiteRepositoryMocks
{
    public static Mock<ISiteRepository> CreateSiteRepository(List<Site> sites)
    {
        var mockSiteRepository = new Mock<ISiteRepository>();

        mockSiteRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(sites);

        mockSiteRepository.Setup(repo => repo.AddAsync(It.IsAny<Site>())).ReturnsAsync(
            (Site site) =>
            {
                site.Id = new Fixture().Create<int>();

                site.ReferenceId = new Fixture().Create<Guid>().ToString();

                sites.Add(site);

                return site;
            });

        return mockSiteRepository;
    }

    public static Mock<ISiteRepository> UpdateSiteRepository(List<Site> sites)
    {
        var mockSiteRepository = new Mock<ISiteRepository>();

        mockSiteRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(sites);

        mockSiteRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => sites.SingleOrDefault(x => x.ReferenceId == i));

        mockSiteRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Site>())).ReturnsAsync((Site site) =>
        {
            var index = sites.FindIndex(item => item.Id == site.Id);

            sites[index] = site;

            return site;
        });

        return mockSiteRepository;
    }

    public static Mock<ISiteRepository> DeleteSiteRepository(List<Site> sites)
    {
        var mockSiteRepository = new Mock<ISiteRepository>();

        mockSiteRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(sites);

        mockSiteRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => sites.SingleOrDefault(x => x.ReferenceId == i));

        mockSiteRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Site>())).ReturnsAsync((Site site) =>
        {
            var index = sites.FindIndex(item => item.Id == site.Id);

            site.IsActive = false;

            sites[index] = site;

            return site;
        });

        return mockSiteRepository;
    }

    public static Mock<ISiteRepository> GetSiteRepository(List<Site> sites)
    {
        var mockSiteRepository = new Mock<ISiteRepository>();

        mockSiteRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(sites);

        mockSiteRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => sites.SingleOrDefault(x => x.ReferenceId == i));

        return mockSiteRepository;
    }

    public static Mock<ISiteRepository> GetSiteNamesRepository(List<Site> sites)
    {
        var mockSiteRepository = new Mock<ISiteRepository>();

        mockSiteRepository.Setup(repo => repo.GetSiteNames()).ReturnsAsync(sites);

        return mockSiteRepository;
    }

    public static Mock<ISiteRepository> GetSiteNameUniqueRepository(List<Site> sites)
    {
        var mockSiteRepository = new Mock<ISiteRepository>();

        mockSiteRepository.Setup(repo => repo.IsSiteNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => sites.Exists(x => x.Name == i && x.ReferenceId == j));

        return mockSiteRepository;
    }

    public static Mock<ISiteRepository> GetSiteByCompanyIdRepository(List<Site> sites)
    {
        var mockSiteRepository = new Mock<ISiteRepository>();

        mockSiteRepository.Setup(repo => repo.GetSiteByCompanyId(It.IsAny<string>())).ReturnsAsync(sites);

        return mockSiteRepository;
    }

    public static Mock<ISiteRepository> GetSiteEmptyRepository()
    {
        var mockSiteRepository = new Mock<ISiteRepository>();

        mockSiteRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Site>());

        return mockSiteRepository;
    }

    public static Mock<ISiteRepository> GetPaginatedSiteRepository(List<Site> sites)
    {
        var siteRepository = new Mock<ISiteRepository>();

        var queryableSite = sites.BuildMock();

        siteRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableSite);

        return siteRepository;
    }

    public static Mock<ISiteRepository> GetSiteByTypeRepository(List<Site> sites)
    {
        var mockSiteRepository = new Mock<ISiteRepository>();

        mockSiteRepository.Setup(repo => repo.GetSiteBySiteType(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(sites);

        return mockSiteRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateSiteEventRepository(List<UserActivity> userActivities)
    {
        var mockSiteEventRepository = new Mock<IUserActivityRepository>();

        mockSiteEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        mockSiteEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return mockSiteEventRepository;
    }
}