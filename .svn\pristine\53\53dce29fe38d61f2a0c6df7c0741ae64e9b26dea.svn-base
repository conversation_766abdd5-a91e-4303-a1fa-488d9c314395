using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class EmployeeFixture : IDisposable
{
    public List<Employee> EmployeePaginationList { get; set; }
    public List<Employee> EmployeeList { get; set; }
    public Employee EmployeeDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public EmployeeFixture()
    {
        var fixture = new Fixture();

        EmployeeList = fixture.Create<List<Employee>>();

        EmployeePaginationList = fixture.CreateMany<Employee>(20).ToList();

        EmployeeDto = fixture.Create<Employee>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
