﻿
let mId = sessionStorage.getItem("monitorId");
let monitortype = 'MssqlAlwaysOn';
let infraObjectId = sessionStorage.getItem("infraobjectId");

setTimeout(() => { alwaysonmonitorstatus(mId, monitortype) }, 250)
setTimeout(() => { msSQLServer(infraObjectId) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
$('#mssqlserver').hide();
async function msSQLServer(id) {

    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
    let data = {}
    data.infraObjectId = id;
    let mssqlServerData = await getAysncWithHandler(url, data);

    if (mssqlServerData != null && mssqlServerData?.length > 0) {
        $('#mssqlserver').show();
        bindMSSQLServer(mssqlServerData)
    } else {
        $('#mssqlserver').hide();
    }

}
$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})

function bindMSSQLServer(mssqlServerData) {
    const rowsValue = mssqlServerData?.map((list, i) => {
        let serverName = "";
       
        if (list.workflowName && (list?.isServiceUpdate?.toLowerCase() === "error" || list?.isServiceUpdate?.toLowerCase() === "stopped")) {
            serverName = checkAndReplace(list?.failedActionName);
        } else {
            serverName = checkAndReplace(list?.servicePath === null ? list?.workflowName : list?.workflowName === null ? list?.servicePath : list?.workflowName);
        }

        const ipAddress = checkAndReplace(list?.ipAddress);
        const status = checkAndReplace(list?.isServiceUpdate);

        const iconServer = serverName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";
        const iconIp = ipAddress === "NA" ? "text-danger cp-disable" : "text-secondary cp-ip-address";

        let iconStatus;
        if (status?.toLowerCase() === "running") {
            iconStatus = "text-success cp-reload cp-animate";
        } else if (status?.toLowerCase() === "error" || status?.toLowerCase() === "stopped") {
            iconStatus = "text-danger cp-fail-back";
        } else {
            iconStatus = "text-danger cp-disable";
        }

        return `<tr><td><i class="${iconServer} me-1 fs-6"></i><span>${serverName}</span></td><td><i class="${iconIp} me-1 fs-6"></i>${ipAddress}</td><td><i class="${iconStatus} me-1 fs-6"></i>${status}</td></tr>`;
    }).join('');

    $('#mssqlserverbody').append(rowsValue);
}

async function alwaysonmonitorstatus(id, type) {

    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;

    let monitoringData = await getAysncWithHandler(url, data);
    if (monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
}

function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}

let noImage = '<img src="../../img/isomatric/nodatalag.svg" class="mx-auto">'
let setNoDataMessage = (element, message) => $(element).css('text-align', 'center').html(noImage + `<br><span class='text-danger'>${message}</span>`);
let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'

function propertiesData(value) {
    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    else {
        let data = JSON?.parse(value?.properties);
    
        let customSite = data?.AlwaysOnMonitoringModels?.length > 1;
        if (customSite) {
            $("#Sitediv").show();
        } else {
            $("#Sitediv").hide();
        }

       
        $(".siteContainer").empty();

        
        data?.AlwaysOnMonitoringModels?.forEach((a, index) => {
            let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        });

       
        if (data?.AlwaysOnMonitoringModels?.length > 0) {
            $("#siteName0 .nav-link").addClass("active");
            displaySiteData(data?.AlwaysOnMonitoringModels[0]);
        }


        
        let defaultSite = data?.AlwaysOnMonitoringModels?.find(d => d?.Type === 'DR') || data?.AlwaysOnMonitoringModels[0];
        
        //Datalag
        const datalag = checkAndReplace(defaultSite?.MonitoringModel?.PR_Datalag);

        $('#PR_Datalag').text(datalag)
        let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0" && datalag !== "NA")
            ? `${datalag}`
            : 'NA';

        var result = "";
        let iconClass = "text-danger cp-disable";

        if (dataLagValue !== "NA") {
            if (dataLagValue.includes(".")) {
                const values = dataLagValue.split(".");
                const hours = values[0] * 24;
                const minutes = values[1]?.split(':')?.slice(0, 2)?.join(':');
                const min = minutes?.split(':');
                const firstValue = parseInt(min[0]) + parseInt(hours);
                result = firstValue + ":" + min[1];
            }
            else if (dataLagValue.includes("+")) {
                const value = dataLagValue.split(" ");
                result = value[1]?.split(':')?.slice(0, 2)?.join(':');
            }
            else {
                result = dataLagValue.split(':')?.slice(0, 2)?.join(':');
            }

            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);

            if (minute > value?.configuredRPO) {
                $('#PR_Datalag').text(result).attr('title', result).css('color', 'red');
            } else {
                $('#PR_Datalag').text(result).attr('title', result).css('color', '');
            }

            iconClass = "text-primary cp-time";
        } else {
            dataLagValue !== 'NA' ? $('#PR_Datalag').text(dataLagValue).attr('title', dataLagValue).css('color', 'red') : $('#PR_Datalag').text(dataLagValue).attr('title', dataLagValue).css('color', '')
        }

        $('#PR_Datalag').prepend(`<i class="${iconClass} me-1 fs-6"></i>`);

        if (defaultSite) {
            displaySiteData(defaultSite);
        }

        $(document).on('click', '.siteListChange', function () {
            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0]?.id
            let getSiteName = $(`#${siteId} .siteName`).text()

            let MonitoringModel = data?.AlwaysOnMonitoringModels?.find(d => d?.Type === getSiteName);            
            if (MonitoringModel) {
                displaySiteData(MonitoringModel);
            }
        });

        function displaySiteData(siteData) {
            let obj = {};
            $('.dynamicSite-header').text(siteData?.Type).attr('title', siteData?.Type);

            for (let key in siteData?.MonitoringModel?.AvailabilityGroupMonitoring) {
                obj[`DR_` + key] = siteData?.MonitoringModel?.AvailabilityGroupMonitoring[key];
            }

            let MonitoringModelProp = [
                "DR_Instance_Name", "DR_Availability_Group_Name",
                "DR_Availability_Group_Role", "DR_Availability_Mode",
                "DR_Failover_Mode_Setting", "DR_Role_Allow_Connections",
                "DR_Availability_Group_Operational_State", "DR_Availability_Group_Connected_State"
            ];

            if (Object.keys(obj)?.length > 0) {
                bindProperties(obj, MonitoringModelProp,value);
            } else {
                setNoDataMessage('#availability', 'Availability Group Summary not available');
            }
            let objdl = {}
            for (let key in siteData?.MonitoringModel) {
                objdl['DR_' + key] = siteData?.MonitoringModel?.[key];
            }
            
            //let MonitoringDatalagProp = [
            //    "DR_PR_Datalag"
            //];
            
            //if (Object.keys(objdl)?.length > 0) {
            //    bindProperties(objdl, MonitoringDatalagProp, value);
            //}
            let objdb = {};
            for (let key in siteData?.MonitoringModel?.DatabaseLevelMonitoring) {
                objdb['DR_' + key] = siteData?.MonitoringModel?.DatabaseLevelMonitoring[key];
            }

            let DatabaseModelProp = [
                "DR_Database_Name", "DR_DataBase_Synchroniztion_State",
                "DR_DataBase_Synchroniztion_Health_Status", "DR_DataBase_State",
                "DR_DataSync_State_Availability_Database", "DR_Endpoint_Port_Number"
            ];
            if (Object.keys(objdb).length > 0) {
                bindProperties(objdb, DatabaseModelProp, value);
            }
            else {
                setNoDataMessage('#dbLevelSummary', 'Database Level Summary not available');
            }

            let objlsn = {};
            for (let key in siteData?.MonitoringModel?.LsnReplicationMonitoring) {
                objlsn['DR_' + key] = siteData?.MonitoringModel?.LsnReplicationMonitoring[key];
            }

            let LsnModalProp = [
                "DR_Last_Sent_LSN", "DR_Last_Received_LSN", "DR_Last_Redone_LSN",
                "DR_Last_Commit_LSN", "DR_Last_Sent_Time", "DR_Last_Received_Time", "DR_Last_Redone_Time",
                "DR_Last_Commit_Time", "DR_Last_Hardened_Lsn", "DR_Log_Send_Queue_Size", "DR_Redo_Queue_Size"
            ];

            if (Object.keys(objlsn)?.length > 0) {
                bindProperties(objlsn, LsnModalProp);
            } else {
                setNoDataMessage('#dbReplicaLSN', 'Database Replication LSN Summary not available');
            }
        }



        //  solutionDiagramMSSQL(data);
        let instanceDetail = data?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrAvailabilityGroupMonitoring
        const instanceProperties = [
            "PR_Instance_Name", "PR_Availability_Group_Name", "PR_Availability_Group_Role",
            "PR_Availability_Mode", "PR_Failover_Mode_Setting",
            "PR_Role_Allow_Connections", "PR_Availability_Group_Operational_State",
            "PR_Availability_Group_Connnected_State",
        ];
        if (instanceDetail !== '' && instanceDetail !== null && instanceDetail !== undefined) {
            bindProperties(instanceDetail, instanceProperties,value);
        } else {
            setNoDataMessage('#availability', 'Availability Group Summary not available');
        }

        //Database Level Summary
        let dbLevelSummary = data?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrDatabaseLevelMonitoring;

        const dbLevelSummaryProperties = [
            "PR_Database_Name", "PR_DataBase_Synchroniztion_State",
            "PR_DataBase_Synchroniztion_Health_Status", "PR_DataBase_State",
            "PR_DataSync_State_Availability_Database", "PR_Endpoint_Port_Number",
        ];

        if (dbLevelSummary !== '' && dbLevelSummary !== null && dbLevelSummary !== undefined) {
            bindProperties(dbLevelSummary, dbLevelSummaryProperties,value);
        }
        else {
            setNoDataMessage('#dbLevelSummary', 'Database Level Summary not available');
        }

        //Database Replication LSN Summary 
        let dbReplicaLSN = data?.PrAlwaysOnMonitoringModel?.PrMonitoringModel?.PrLsnReplicationMonitoring;

        const dbReplicaLSNProperties = [
            "PR_Last_Sent_LSN", "PR_Last_Received_LSN", "PR_Last_Redone_LSN", "PR_Last_Commit_LSN",
            "PR_Last_Sent_Time", "PR_Last_Received_Time", "PR_Last_Redone_Time",
            "PR_Last_Commit_Time", "PR_Last_Hardened_Lsn", "PR_Log_Send_Queue_Size", "PR_Redo_Queue_Size"
        ];

        if (dbReplicaLSN !== '' && dbReplicaLSN !== null && dbReplicaLSN !== undefined) {
            bindProperties(dbReplicaLSN, dbReplicaLSNProperties,value);
        }
        else {
            setNoDataMessage('#dbReplicaLSN', 'Database Replication LSN Summary not available');
        }

        
    }
}


function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}

function bindProperties(data, properties, value) {

    properties.forEach(property => {
        const values = data[property];
        const displayedValue = values !== undefined ? checkAndReplace(values) : 'NA';
        // Displayed value with icon
        const iconHtml = getIconClass(displayedValue, property, data, values);
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(mergeValue).attr('title', displayedValue);
    })
}
function getIconClass(displayedValue, property, data, value) {   
    let prinstanceicon = data?.PR_Instance_Name
 ? "text-primary cp-instance-name  me-1 fs-6" : "text-danger cp-disable"
    let drinstanceicon = data?.
        DR_Instance_Name ? "text-primary cp-instance-name  me-1 fs-6" : "text-danger cp-disable"
    let prgroupname = data?.PR_Availability_Group_Name ? "text-primary cp-name me-1 fs-6" : "text-danger cp-disable"
    let drgroupname = data?.DR_Availability_Group_Name ? "text-primary cp-name me-1 fs-6" : "text-danger cp-disable"
    let prconnection = data?.PR_Role_Allow_Connections ? "text-primary cp-cluster-database  me-1 fs-6" : "text-danger cp-disable"
    let drconnection = data?.DR_Role_Allow_Connections ? "text-primary cp-cluster-database  me-1 fs-6" : "text-danger cp-disable"
    let prport = data?.PR_Endpoint_Port_Number ? "text-primary cp-endpoint-port-number me-1 fs-6" : "text-danger cp-disable"
    let drport = data?.DR_Endpoint_Port_Number ? "text-primary cp-endpoint-port-number me-1 fs-6" : "text-danger cp-disable"
    let prcommittime = data?.PR_Last_Commit_Time ? "text-primary cp-time me-1 fs-6" : "text-danger cp-disable"
    let drcommittime = data?.DR_Last_Commit_Time ? "text-primary cp-time me-1 fs-6" : "text-danger cp-disable"
    let prdatabase = data?.PR_Database_Name ? "text-primary cp-database me-1 fs-6" : "text-danger cp-disable"
    let drdatabase = data?.DR_Database_Name ? "text-primary cp-database me-1 fs-6" : "text-danger cp-disable"
    let datalag = data?.DR_PR_Datalag ? "text-primary cp-time me-1 fs-6" : "text-danger cp-disable"

    let commitTime = data.DR_Last_Commit_Time ?  "text-primary cp-time me-1 fs-6" : "text-danger cp-disable"
    let receivedTime = data.DR_Last_Received_Time ?  "text-primary cp-time me-1 fs-6" : "text-danger cp-disable"
    let redoneTime = data.DR_Last_Redone_Time ?  "text-primary cp-time me-1 fs-6" : "text-danger cp-disable"
    let sentTime = data.DR_Last_Sent_Time ? "text-primary cp-time me-1 fs-6" : "text-danger cp-disable"

    let commitLSN = data.DR_Last_Commit_LSN ? "text-primary cp-control-file-type me-1 fs-6" : "text-danger cp-disable"
    let receivedLSN = data.DR_Last_Received_LSN ? "text-primary cp-control-file-type me-1 fs-6" : "text-danger cp-disable"
    let redoneLSN = data.DR_Last_Redone_LSN ? "text-primary cp-control-file-type me-1 fs-6" : "text-danger cp-disable"
    let sentLSN = data.DR_Last_Sent_LSN ? "text-primary cp-control-file-type me-1 fs-6" : "text-danger cp-disable"
    let hardenedLSN = data.DR_Last_Hardened_Lsn ? "text-primary cp-control-file-type me-1 fs-6" : "text-danger cp-disable"

    let pr_Last_Hardened_Lsn = data.PR_Last_Hardened_Lsn ? "text-primary cp-control-file-type" : "text-danger cp-disable"
    let pr_Last_Commit_LSN = data.PR_Last_Commit_LSN ? "text-primary cp-control-file-type" : "text-danger cp-disable"
  
    const iconMapping = {
        'PR_Instance_Name': prinstanceicon,
        'DR_Instance_Name': drinstanceicon,
        'PR_Availability_Group_Name': prgroupname,
        'DR_Availability_Group_Name': drgroupname,
        'PR_Role_Allow_Connections': prconnection,
        'DR_Role_Allow_Connections': drconnection,
        'PR_Endpoint_Port_Number': prport,
        'DR_Endpoint_Port_Number': drport,
        'PR_Last_Commit_Time': prcommittime,
        'DR_Last_Commit_Time': drcommittime,
        'PR_Database_Name': prdatabase,
        'DR_Database_Name': drdatabase,
        'DR_PR_Datalag': datalag,
        'DR_Last_Commit_Time': commitTime,
        'DR_Last_Received_Time': receivedTime,
        'DR_Last_Redone_Time': redoneTime,
        'DR_Last_Sent_Time': sentTime,
        'DR_Last_Commit_LSN': commitLSN,
        'DR_Last_Received_LSN': receivedLSN,
        'DR_Last_Redone_LSN': redoneLSN,
        'DR_Last_Sent_LSN': sentLSN,
        'DR_Last_Hardened_Lsn': hardenedLSN,
        "PR_Last_Hardened_Lsn": pr_Last_Hardened_Lsn,
        "PR_Last_Commit_LSN": pr_Last_Commit_LSN
    }
    
    let iconClass = iconMapping[property] || '';    
    switch (displayedValue?.toLowerCase()) {

        case 'not allowed':
        case 'no':
        case 'na':
            iconClass = 'text-danger cp-disable';
            break;
        case 'manual':
            iconClass = 'text-warning cp-settings';
            break;
        case 'healthy':
            iconClass = 'text-success cp-health-success';
            break;
        case 'nothealthy':
        case 'not_healthy':
        case 'unhealthy':
            iconClass = 'text-danger cp-health-error';
            break;
        case 'online':
            iconClass = 'text-success cp-online';
            break;
        case 'offline':
            iconClass = 'text-danger cp-offline';
            break;
        case 'primary':
            iconClass = 'text-primary cp-list-prsite';
            break;
        case 'secondary':
            iconClass = 'text-info cp-dr';
            break;
        case 'physical standby':
            iconClass = 'text-info cp-physical-drsite';
            break;
        case 'connected':
        case 'connect':
            iconClass = 'text-success cp-connected';
            break;
        case 'disconnected':
        case 'disconnect':
            iconClass = 'text-danger cp-disconnected';
            break;
        case 'synchronous_commit':
        case 'synchronized':
        case 'synchronizing':
        case 'sync':
            iconClass = 'text-success cp-refresh';
            break;
        case 'asynchronous_commit':
        case 'notsynchronizing':
        case 'not':
        case 'notsynchronized':
        case 'not synchronized':
        case 'not synchronizing':
        case 'asynchronizing':
        case 'asynchronized':
        case 'async':
            iconClass = 'text-danger cp-refresh';
            break;
        case 'pending':
        case 'recovery pending':
            iconClass = 'text-warning cp-pending';
            break;
        case 'resumed':
        case 'resume':                  
            iconClass = 'text-success cp-play-resume';
            break;
        case 'running':
        case 'ready':
        case 'run':
            iconClass = 'text-success cp-reload cp-animate';
            break;
        case 'error':
            iconClass = 'text-danger cp-fail-back';
            break;
        case 'stopped':
        case 'stop':
            iconClass = 'text-danger cp-Stopped';
            break;
        case 'standby':
        case 'to standby':
        case 'mounted':
            iconClass = 'text-warning cp-control-file-type';
            break;
        case 'enabled':
        case 'enable':
            iconClass = 'text-success cp-enables';
            break;
        case 'disabled':
        case 'disable':
            iconClass = 'text-danger cp-disables';
            break;
        case 'true':
        case 'yes':
        case 'valid':
            iconClass = 'text-success cp-success';
            break;
        case 'false':
        case 'defer':
        case 'deferred':
            iconClass = 'text-danger cp-error';
            break;
        case 'resolving':
        case 'resolve':
        case 'resolved':
            iconClass = 'text-primary cp-configure-settings';
            break;
        case 'pause':
        case 'paused':
            iconClass = 'text-warning cp-circle-pause';
            break;
        case 'required':
        case 'require':
            iconClass = 'text-warning cp-warning';
            break;
        case 'on':
            iconClass = 'text-success cp-end';
            break;
        case 'off':
            iconClass = 'text-danger cp-end';
            break;
        case 'current':
        case 'read write':
            iconClass = 'text-success cp-file-edits';
            break;
        default:

            break;

    }
    return iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
}
//function getIconClass(displayedValue) {
//    const lowercaseValue = displayedValue?.toLowerCase();

//    switch (lowercaseValue) {
//        case 'na':
//        case 'no':
//        case 'not allowed': return 'text-danger cp-disable';
//        case 'manual': return 'text-warning cp-settings';
//        case 'healthy': return 'text-success cp-health-success';
//        case 'nothealthy':
//        case 'not_healthy':
//        case 'unhealthy': return 'text-danger cp-health-error';
//        case 'online': return 'text-success cp-online';
//        case 'offline': return 'text-danger cp-offline';
//        case 'primary': return 'text-primary cp-list-prsite';
//        case 'secondary': return 'text-info cp-dr';
//        case 'physical standby': return 'text-info cp-physical-drsite';
//        case 'connected':
//        case 'connect': return 'text-success cp-connected';
//        case 'disconnected':
//        case 'disconnect': return 'text-danger cp-disconnected';
//        case 'synchronous_commit':
//        case 'synchronized':
//        case 'synchronizing':
//        case 'sync': return 'text-success cp-refresh';
//        case 'asynchronous_commit':
//        case 'notsynchronizing':
//        case 'not':
//        case 'notsynchronized':
//        case 'not synchronized':
//        case 'not synchronizing':
//        case 'asynchronizing':
//        case 'asynchronized':
//        case 'async': return 'text-danger cp-refresh';
//        case 'pending': return 'text-warning cp-pending';
//        case 'running':
//        case 'ready':
//        case 'run': return 'text-success cp-reload cp-animate';
//        case 'error': return 'text-danger cp-fail-back';
//        case 'stopped':
//        case 'stop': return 'text-danger cp-Stopped';
//        case 'standby':
//        case 'to standby':
//        case 'mounted': return 'text-warning cp-control-file-type';
//        case 'enabled':
//        case 'enable': return 'text-success cp-enables';
//        case 'disabled':
//        case 'disable': return 'text-danger cp-disables';
//        case 'true':
//        case 'yes':
//        case 'valid': return 'text-success cp-success';
//        case 'false':
//        case 'defer':
//        case 'deferred': return 'text-danger cp-error';
//        case 'pause':
//        case 'paused': return 'text-warning cp-circle-pause';
//        case 'required':
//        case 'require': return 'text-warning cp-warning';
//        case 'on': return 'text-success cp-end';
//        case 'off': return 'text-danger cp-end';
//        case 'current':
//        case 'read write':
//            return 'text-success cp-file-edits';
//        default: return '';
//    }
//}