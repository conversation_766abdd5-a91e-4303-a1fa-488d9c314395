﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.LicenseManager.Events.DerivedLicenseEvent.Create;

public class DerivedLicenseCreatedEventHandler : INotificationHandler<DerivedLicenseCreatedEvent>
{
    private readonly ILogger<DerivedLicenseCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DerivedLicenseCreatedEventHandler(ILoggedInUserService userService,
        ILogger<DerivedLicenseCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(DerivedLicenseCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} {Modules.DerivedLicense}",
            Entity = Modules.DerivedLicense.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Derived License '{createdEvent.PONumber}' added successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Derived License '{createdEvent.PONumber}' added successfully.");
    }
}