﻿@using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerModel;
@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerModel.InfraObjectSchedulerViewModel
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()
<link href="~/css/switch_slide_toggle.css" rel="stylesheet" />

<div class="page-content">
    <div class="card Card_Design_None">

        <div class="card-header header">
            <h6 class="page_title"><i class="cp-dr-readiness"></i><span>Manage Resiliency Readiness </span></h6>
            <form class="d-flex gap-1 align-items-center">
                <div class="input-group w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" aria-expanded="false" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="infraobjectname=" id="CompanyName">
                                        <label class="form-check-label" for="CompanyName">
                                            InfraObject Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="beforeswitchoverworkflowname=" id="DisplayName">
                                        <label class="form-check-label" for="DisplayName">
                                            Before SwitchOver
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="afterswitchoverworkflowname=" id="DisplayName1">
                                        <label class="form-check-label" for="DisplayName">
                                            After SwitchOver
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" id="Activebtn" style="background-color:#fafff8" class="btn btn-transparent border border-success-subtle me-2 d-flex align-items-center text-primary-emphasis">
                    <i class="cp-active-inactive text-success" title="Active"></i>&nbsp; Active
                    @* <i class="cp-active-inactive text-success me-1"></i>Active *@
                </button>
                <button type="button" id="Inactivebtn" class="btn btn-transparent border border-danger-subtle me-2 d-flex align-items-center"
                        role="button" style="background-color:#fff0f2">
                    <i class="cp-active-inactive text-danger" title="InActive"></i>&nbsp; InActive
                    @* <i class="cp-active-inactive text-danger me-1"></i>InActive *@
                </button>
                <button type="button" class="btn btn-primary btn-sm btn-create" id="drbtn_create" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <div id="collapetable">
                <table id="tblDrReadiness" class="datatable table table-hover dataTable no-footer" style="width:100%">
                    <thead>
                        <tr>
                            <th class="SrNo_th">Sr.No</th>
                            <th>
                                <div class="">
                                    <input name="checkboxAll" type="checkbox" id="flexCheckDefault"
                                           class="form-check-input custom-cursor-default-hover">
                                </div>
                            </th>
                            <th>InfraObject Name</th>
                            @* <th>Workflow Type</th> *@
                            <th>Scheduled Time</th>
                            <th>Before Switchover Workflow </th>
                            <th>After Switchover Workflow </th>
                            <th>Node Name</th>
                            <th>Last Executed Time</th>
                            <th class="">Status</th>
                            <th>State</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div id="manageCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.CreateAndEdit" aria-hidden="true"></div>
<div id="manageDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.Delete" aria-hidden="true"></div>
<!--Modal Delete-->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" data-bs-backdrop="static" aria-hidden="true">
    <partial name="Delete" model="new InfraObjectSchedulerViewModel()" />
</div>

<!--Modal Create-->
<div class="modal fade" id="CreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>
<!--Modal Error-->

<div class="modal fade" id="ErrorModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-error-message"></i><span>Error Message</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <div class="list-group list-group-flush Profile-Select">
                    <div class="d-grid text-center">
                        <p id="error_message"></p>
                    </div>
                </div>
            </div>

        </div>

    </div>
</div>
@* notification *@
@* <div class='Notification'>
    <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
        <div class='d-flex'>
            <div class='toast-body'>
                <span id="alertClass" class='success-toast'>
                    <i id="icon" class='cp-check toast_icon'></i>
                </span>
                <span id="message">

                </span>
            </div>
            <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
        </div>
    </div>
</div> *@
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/common/slide_toggle.js"></script>
<script src="~/js/Resiliency Readiness/Manage Resiliency Readiness/DrReadiness.js"></script>
<script src="~/js/common/commoncronexpressionjs.js"></script>


