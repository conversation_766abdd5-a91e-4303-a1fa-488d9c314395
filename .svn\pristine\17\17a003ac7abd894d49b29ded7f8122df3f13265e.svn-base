namespace ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Queries.GetDetail;

public class GetWorkflowActionFieldMasterDetailsQueryHandler : IRequestHandler<GetWorkflowActionFieldMasterDetailQuery,
    WorkflowActionFieldMasterDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowActionFieldMasterRepository _workflowActionFieldMasterRepository;

    public GetWorkflowActionFieldMasterDetailsQueryHandler(IMapper mapper,
        IWorkflowActionFieldMasterRepository workflowActionFieldMasterRepository)
    {
        _mapper = mapper;
        _workflowActionFieldMasterRepository = workflowActionFieldMasterRepository;
    }

    public async Task<WorkflowActionFieldMasterDetailVm> Handle(GetWorkflowActionFieldMasterDetailQuery request,
        CancellationToken cancellationToken)
    {
        var workflowActionFieldMaster = await _workflowActionFieldMasterRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(workflowActionFieldMaster, nameof(Domain.Entities.WorkflowActionFieldMaster),
            new NotFoundException(nameof(Domain.Entities.WorkflowActionFieldMaster), request.Id));

        var workflowActionFieldMasterDetailDto =
            _mapper.Map<WorkflowActionFieldMasterDetailVm>(workflowActionFieldMaster);

        return workflowActionFieldMasterDetailDto;
    }
}