﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:Boolean x:Key="/Default/Environment/Filtering/ExcludeCoverageFilters/=ContinuityPatrol_002EPersistence_002EUnitTests_003B_002A_003BContinuityPatrol_002EPersistence_002EUnitTests_002ERepository_002EAccessManagerRepositoryTests_003B_002A/@EntryIndexedValue">True</s:Boolean></wpf:ResourceDictionary>