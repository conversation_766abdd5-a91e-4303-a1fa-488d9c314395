using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FormTypeCategoryFixture : IDisposable
{
    public List<FormTypeCategory> FormTypeCategoryPaginationList { get; set; }
    public List<FormTypeCategory> FormTypeCategoryList { get; set; }
    public FormTypeCategory FormTypeCategoryDto { get; set; }

   

    public ApplicationDbContext DbContext { get; private set; }

    public FormTypeCategoryFixture()
    {
        var fixture = new Fixture();

        FormTypeCategoryList = fixture.Create<List<FormTypeCategory>>();

        FormTypeCategoryPaginationList = fixture.CreateMany<FormTypeCategory>(20).ToList();

        FormTypeCategoryDto = fixture.Create<FormTypeCategory>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
