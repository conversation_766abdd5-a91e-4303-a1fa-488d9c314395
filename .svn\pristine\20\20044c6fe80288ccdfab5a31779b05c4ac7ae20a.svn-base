let changePasswordURL= {
    changePassword:"Admin/User/ChangePassword"
}

async function CurrentPassword(value) {
    const errorElement = $('#OldPassword-error');

    if (!value?.trim()) {
        errorElement.text('Enter current password').addClass('field-validation-error');
        return false;
    }
    errorElement.text('').removeClass('field-validation-error');
    return true;
}

async function NewPassword(value) {
    const errorElement = $('#NewPassword-error');
    if (!value) {
        errorElement.text('Enter new password').addClass('field-validation-error');
        return false;
    }
    errorElement.text('').removeClass('field-validation-error');   
    return true;
}

async function ConfirmPassword(value) {
    const errorElement = $('#ConfirmPassword-error');
    if (!value) {
        errorElement.text('Enter confirm password').addClass('field-validation-error');
        return false;
    }
    errorElement.text('').removeClass('field-validation-error');  
    return true;
}

$(function () {

    $("#SaveFunction").prop('disabled', true);

    $(".toggle-password").on('click', async function () {
        const $toggle = $(this);
        const $input = $toggle.prev();      
        const $icon = $toggle.find("i");
        const loginName = $("#LoginName").data("loginnames");
        const inputType = $input.attr("type");
        let passwordValue = $input.val();

        if (inputType === "password") {
            showPassword($input, $icon);
            const isEncrypted = passwordValue && passwordValue.length > 30;
            if (isEncrypted) {
                let decrypted = await onfocusPassword(passwordValue);
                if (loginName) {
                    decrypted = decrypted?.substring(loginName.length);
                }
                $input.val(decrypted);
            }
        } else {
            hidePassword($input, $icon);
            const cleanedValue = passwordValue.replace(/\s+/g, '');
            blurpassword($input.attr("id"), cleanedValue);
        }
    });

    $("#reset_value").on('click', function () {
        $('#OldPassword-error, #NewPassword-error, #ConfirmPassword-error').text('').removeClass('field-validation-error');
        $("#CurrentPassword, #changeConfirmPassword, #changeNewPassword").val("");
        $('.toggle-password i').addClass('cp-password-visible fs-6');
        $('#CurrentPassword,#Password').attr('type', 'password');
        $("#SaveFunction").prop('disabled', true);
    });

    $(document).on('input', '#CurrentPassword', function () {
        let value = this.value.replace(/\s+/g, '');
        inputpassword(this.id, value);
    });

    $(document).on('input', '#changeNewPassword', function () {
        let value = this.value.replace(/\s+/g, '');
        inputpassword(this.id, value);
        $('#changeConfirmPassword').val('');
        $('#ConfirmPassword-error').text('').removeClass('field-validation-error');
    });

    $(document).on('input', '#changeConfirmPassword', function () {
        let value = this.value.replace(/\s+/g, '');
        inputConfirmpassword(this.id, value);
    });

    $(document).on('blur', '#changeConfirmPassword,#CurrentPassword', function () {
        let value = this.value.replace(/\s+/g, '');
        blurpassword(this.id, value);
    });

    $(document).on('focus', '#changeNewPassword, #CurrentPassword', function () {
        focuspassword(this.id);
    });

    $("#SaveFunction").on('click', async function () {
        const $btn = $(this).prop('disabled', true);
        const $currentPassword = $("#CurrentPassword");
        const $newPassword = $("#changeNewPassword");
        const $confirmPassword = $("#changeConfirmPassword");
        const $loginName = $("#LoginName");
        const $loginId = $("#loginId");
        const currentPassword = $currentPassword.val();
        const newPassword = $newPassword.val();
        const confirmPassword = $confirmPassword.val();
        const loginName = $loginName.data("loginnames");
        const loginId = $loginId.data("loginid");
        const isCurrentPasswordValid = await CurrentPassword(currentPassword);
        const isNewPasswordValid = await NewPassword(newPassword);
        const isConfirmPasswordValid = await ConfirmPassword(confirmPassword);
        if (loginName && isCurrentPasswordValid && isNewPasswordValid && isConfirmPasswordValid) {
            const encryptIfNeeded = async ($input, value) => {
                if (value.length < 30) {
                    const encrypted = await EncryptPassword(loginName.toLowerCase() + value);
                    $input.attr("type", "password").val(encrypted);
                }
            }; 
            await Promise.all([
                encryptIfNeeded($currentPassword, currentPassword),
                encryptIfNeeded($newPassword, newPassword),
                encryptIfNeeded($confirmPassword, confirmPassword)
            ]);
            $loginName.val(loginName);
            $loginId.val(loginId);
            sanitizeContainer([
                'CurrentPassword',
                'changeNewPassword',
                'changeConfirmPassword',
                'LoginName',
                'loginId'
            ]);
            try {
                const response = await $.ajax({
                    url: RootUrl + changePasswordURL.changePassword,
                    type: "POST",
                    dataType: "json",
                    data: {
                        UserId: $loginId.val(),
                        LoginName: $loginName.val(),
                        OldPassword: $currentPassword.val(),
                        NewPassword: $newPassword.val(),
                        ConfirmPassword: $confirmPassword.val(),
                        __RequestVerificationToken: gettoken()
                    }
                });

                const { success, data, message } = response;

                if (success) {
                    notificationAlert("success", data.message);
                    setTimeout(() => window.location.href = "/Account/Login", 2000);
                } else {
                    notificationAlert("warning", message);
                }
            } catch (error) {
                errorNotification(error);
            }

            $currentPassword.val("");
            $newPassword.val("");
            $confirmPassword.val("");
        }

        $btn.prop('disabled', false);
    });

});


