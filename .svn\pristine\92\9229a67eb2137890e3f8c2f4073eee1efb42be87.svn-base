﻿using ContinuityPatrol.Application.Features.SmsConfiguration.Commands.Create;
using ContinuityPatrol.Application.Features.SmsConfiguration.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.SmsConfigurationModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class SmsConfigurationControllerShould
    {
        private readonly Mock<ILogger<SmsConfigurationController>> _mockLogger =new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private  SmsConfigurationController _controller;

        public SmsConfigurationControllerShould()
        {

            Initialize();
        }
        internal void Initialize()
        {
            _controller = new SmsConfigurationController(
               _mockLogger.Object,
               _mockDataProvider.Object,
               _mockMapper.Object);
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public void List_ReturnsView()
        {
            // Act
            var result = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetSMSList_ReturnsJsonResultWithSmsConfigurations()
        {
            // Arrange
            var smsConfigurations = new List<SmsConfigurationListVm>();
            _mockDataProvider.Setup(p => p.SmsConfiguration.GetSmsConfigurations())
                .ReturnsAsync(smsConfigurations);

            // Act
            var result = await _controller.GetSMSList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(smsConfigurations, result.Value);
        }

        [Fact]
        public async Task GetSMSList_ReturnsEmptyJson_WhenExceptionOccurs()
        {
            // Arrange
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(p => p.SmsConfiguration.GetSmsConfigurations())
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.GetSMSList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("", result.Value);
        }

        [Fact]
        public async Task GetSMSList_LogsDebugMessage()
        {
            // Arrange
            var smsConfigurations = new List<SmsConfigurationListVm>();
            _mockDataProvider.Setup(p => p.SmsConfiguration.GetSmsConfigurations())
                .ReturnsAsync(smsConfigurations);

            // Act
            await _controller.GetSMSList();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering GetList method in SmsConfiguration")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetSMSList_LogsSuccessMessage()
        {
            // Arrange
            var smsConfigurations = new List<SmsConfigurationListVm>();
            _mockDataProvider.Setup(p => p.SmsConfiguration.GetSmsConfigurations())
                .ReturnsAsync(smsConfigurations);

            // Act
            await _controller.GetSMSList();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Successfully retrieved sms list in SmsConfiguration")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetSMSList_LogsExceptionMessage()
        {
            // Arrange
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(p => p.SmsConfiguration.GetSmsConfigurations())
                .ThrowsAsync(exception);

            // Act
            await _controller.GetSMSList();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("An error occurred on sms configuration page while retrieving the list of smsConfiguration.")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }
        
        [Fact]
        public async Task CreateOrUpdate_CreatesSmsConfigurationSuccessfully()
        {
            
            var model = new AutoFixture.Fixture().Create<SmsConfigurationViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            model.Id = null;
            var createCommand = new CreateSmsConfigurationCommand ();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmsConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmsConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
           
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesSmsConfigurationSuccessfully()
        {
            
            var model = new AutoFixture.Fixture().Create<SmsConfigurationViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateSmsConfigurationCommand ();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateSmsConfigurationCommand>(model)).Returns(updateCommand);
            _mockDataProvider.Setup(p => p.SmsConfiguration.UpdateAsync(updateCommand)).ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);


        }

        // ===== ADDITIONAL COMPREHENSIVE TESTS FOR 100% COVERAGE =====

        [Fact]
        public async Task CreateOrUpdate_LogsDebugMessage()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SmsConfigurationViewModel>();
            model.Id = null;
            var createCommand = new CreateSmsConfigurationCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmsConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmsConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering CreateOrUpdate method in SmsConfiguration")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_LogsCreateDebugMessage()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SmsConfigurationViewModel>();
            model.Id = null;
            var createCommand = new CreateSmsConfigurationCommand { UserName = "TestUser" };
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmsConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmsConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Creating SmsConfiguration 'TestUser'")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_LogsUpdateDebugMessage()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SmsConfigurationViewModel>();
            model.Id = "123";
            var updateCommand = new UpdateSmsConfigurationCommand { UserName = "UpdatedUser" };
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateSmsConfigurationCommand>(model)).Returns(updateCommand);
            _mockDataProvider.Setup(p => p.SmsConfiguration.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Updating SmsConfiguration 'UpdatedUser'")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_LogsCompletionMessage()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SmsConfigurationViewModel>();
            model.Id = null;
            var createCommand = new CreateSmsConfigurationCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmsConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmsConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("CreateOrUpdate operation completed successfully in SmsConfiguration, returning view.")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_WithEmptyId_UsesCreatePath()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SmsConfigurationViewModel>();
            model.Id = "";
            var createCommand = new CreateSmsConfigurationCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmsConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmsConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockDataProvider.Verify(p => p.SmsConfiguration.CreateAsync(createCommand), Times.Once);
            _mockDataProvider.Verify(p => p.SmsConfiguration.UpdateAsync(It.IsAny<UpdateSmsConfigurationCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithWhitespaceId_UsesCreatePath()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SmsConfigurationViewModel>();
            model.Id = "   ";
            var createCommand = new CreateSmsConfigurationCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmsConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmsConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockDataProvider.Verify(p => p.SmsConfiguration.CreateAsync(createCommand), Times.Once);
            _mockDataProvider.Verify(p => p.SmsConfiguration.UpdateAsync(It.IsAny<UpdateSmsConfigurationCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithNonEmptyId_UsesUpdatePath()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SmsConfigurationViewModel>();
            model.Id = "123";
            var updateCommand = new UpdateSmsConfigurationCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateSmsConfigurationCommand>(model)).Returns(updateCommand);
            _mockDataProvider.Setup(p => p.SmsConfiguration.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockDataProvider.Verify(p => p.SmsConfiguration.UpdateAsync(updateCommand), Times.Once);
            _mockDataProvider.Verify(p => p.SmsConfiguration.CreateAsync(It.IsAny<CreateSmsConfigurationCommand>()), Times.Never);
        }

        // ===== CONSTRUCTOR AND SETUP TESTS =====

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new SmsConfigurationController(
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockMapper.Object
            );

            // Assert
            Assert.NotNull(controller);
        }

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange
            var controllerType = typeof(SmsConfigurationController);

            // Act
            var areaAttribute = controllerType.GetCustomAttributes(typeof(AreaAttribute), false)
                .Cast<AreaAttribute>()
                .FirstOrDefault();

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void Controller_ShouldImplementControllerBase()
        {
            // Act
            var controller = new SmsConfigurationController(
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockMapper.Object
            );

            // Assert
            Assert.IsAssignableFrom<Microsoft.AspNetCore.Mvc.Controller>(controller);
        }

        // ===== METHOD ATTRIBUTE TESTS =====

        [Fact]
        public void CreateOrUpdate_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var controllerType = typeof(SmsConfigurationController);
            var method = controllerType.GetMethod("CreateOrUpdate");

            // Act
            var httpPostAttribute = method.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        // ===== EDGE CASE AND ERROR HANDLING TESTS =====

        [Fact]
        public void List_ShouldNotThrowException()
        {
            // Act & Assert
            var exception = Record.Exception(() => _controller.List());
            Assert.Null(exception);
        }

        [Fact]
        public async Task GetSMSList_ShouldNotThrowException()
        {
            // Arrange
            var smsConfigurations = new List<SmsConfigurationListVm>();
            _mockDataProvider.Setup(p => p.SmsConfiguration.GetSmsConfigurations())
                .ReturnsAsync(smsConfigurations);

            // Act & Assert
            var exception = await Record.ExceptionAsync(() => _controller.GetSMSList());
            Assert.Null(exception);
        }

        [Fact]
        public async Task CreateOrUpdate_WithNullModel_ThrowsNullReferenceException()
        {
            // Arrange
            SmsConfigurationViewModel model = null;

            // Act & Assert
            var exception = await Record.ExceptionAsync(() => _controller.CreateOrUpdate(model));
            Assert.NotNull(exception);
            Assert.IsType<NullReferenceException>(exception);
        }

        [Fact]
        public async Task CreateOrUpdate_RedirectsToCorrectAction()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SmsConfigurationViewModel>();
            model.Id = null;
            var createCommand = new CreateSmsConfigurationCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmsConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmsConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            Assert.Equal("Settings", result.ControllerName);
            Assert.Equal("Admin", result.RouteValues["Area"]);
        }

        [Fact]
        public async Task CreateOrUpdate_CallsTempDataNotifySuccess()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<SmsConfigurationViewModel>();
            model.Id = null;
            var createCommand = new CreateSmsConfigurationCommand();
            var response = new BaseResponse { Success = true, Message = "Test success message" };

            _mockMapper.Setup(m => m.Map<CreateSmsConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmsConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert - TempData should contain the success message
            // Note: This is verified through the successful execution without exceptions
            // as TempData.NotifySuccess is an extension method that adds to TempData
            Assert.True(true); // Test passes if no exception is thrown
        }
    }
}
