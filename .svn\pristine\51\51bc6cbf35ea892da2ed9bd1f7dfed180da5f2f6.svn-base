﻿using ContinuityPatrol.Domain.ViewModels.InfraReplicationMappingModel;

namespace ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetType;

public class
    InfraReplicationMappingTypeQueryHandler : IRequestHandler<InfraReplicationMappingTypeQuery,
        List<InfraReplicationMappingListVm>>
{
    private readonly IInfraReplicationMappingRepository _infraReplicationMappingRepository;

    private readonly IMapper _mapper;

    public InfraReplicationMappingTypeQueryHandler(IInfraReplicationMappingRepository infraReplicationMappingRepository,
        IMapper mapper)
    {
        _infraReplicationMappingRepository = infraReplicationMappingRepository;
        _mapper = mapper;
    }

    public async Task<List<InfraReplicationMappingListVm>> Handle(InfraReplicationMappingTypeQuery request,
        CancellationToken cancellationToken)
    {
        var infraReplicationMapping =
            (await _infraReplicationMappingRepository.GetInfraReplicationMappingByType(request.Type)).ToList();

        var infraReplicationMappingList = _mapper.Map<List<InfraReplicationMappingListVm>>(infraReplicationMapping);

        return infraReplicationMappingList;
    }
}