using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class LogViewerRepositoryTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly LogViewerRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Fixture _autoFixture;

    public LogViewerRepositoryTests()
    {
        _context = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString()); ;
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _autoFixture = new Fixture();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        _repository = new LogViewerRepository(_context, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _context?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _context.LogViewers.RemoveRange(_context.LogViewers);
        await _context.SaveChangesAsync();
    }

    #region IsLogViewerNameUnique Tests - Invalid GUID Scenarios

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldReturnTrue_WhenNameExistsAndIdIsInvalidGuid()
    {
        // Arrange
        await ClearDatabase();
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = "TestLogViewer";
        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLogViewerNameUnique("TestLogViewer", "INVALID_GUID");

        // Assert
        Assert.True(result); // Returns true when name exists and id is invalid GUID
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldReturnFalse_WhenNameNotExistsAndIdIsInvalidGuid()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsLogViewerNameUnique("NonExistentName", "INVALID_GUID");

        // Assert
        Assert.False(result); // Returns false when name doesn't exist and id is invalid GUID
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldReturnTrue_WhenNameExistsAndIdIsNull()
    {
        // Arrange
        await ClearDatabase();
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = "TestLogViewer";
        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLogViewerNameUnique("TestLogViewer", null);

        // Assert
        Assert.True(result); // Returns true when name exists and id is null
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldReturnFalse_WhenNameNotExistsAndIdIsNull()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsLogViewerNameUnique("NonExistentName", null);

        // Assert
        Assert.False(result); // Returns false when name doesn't exist and id is null
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldReturnTrue_WhenNameExistsAndIdIsEmptyString()
    {
        // Arrange
        await ClearDatabase();
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = "TestLogViewer";
        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLogViewerNameUnique("TestLogViewer", "");

        // Assert
        Assert.True(result); // Returns true when name exists and id is empty string
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldReturnFalse_WhenNameNotExistsAndIdIsEmptyString()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsLogViewerNameUnique("NonExistentName", "");

        // Assert
        Assert.False(result); // Returns false when name doesn't exist and id is empty string
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldReturnTrue_WhenNameExistsAndIdIsWhitespace()
    {
        // Arrange
        await ClearDatabase();
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = "TestLogViewer";
        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLogViewerNameUnique("TestLogViewer", "   ");

        // Assert
        Assert.True(result); // Returns true when name exists and id is whitespace
    }

    #endregion

    #region IsLogViewerNameUnique Tests - Valid GUID Scenarios

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldReturnFalse_WhenNoLogViewersWithNameAndIdIsValidGuid()
    {
        // Arrange
        await ClearDatabase();
        var validGuid = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsLogViewerNameUnique("NonExistentName", validGuid);

        // Assert
        Assert.False(result); // Returns false when no LogViewers with name exist (Unique extension returns false for empty list)
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldReturnTrue_WhenMultipleLogViewersWithSameNameAndIdIsValidGuid()
    {
        // Arrange
        await ClearDatabase();
        var logViewer1 = _autoFixture.Create<LogViewer>();
        logViewer1.Name = "DuplicateName";
        logViewer1.ReferenceId = Guid.NewGuid().ToString();

        var logViewer2 = _autoFixture.Create<LogViewer>();
        logViewer2.Name = "DuplicateName";
        logViewer2.ReferenceId = Guid.NewGuid().ToString();

        await _context.LogViewers.AddRangeAsync(logViewer1, logViewer2);
        await _context.SaveChangesAsync();

        var validGuid = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsLogViewerNameUnique("DuplicateName", validGuid);

        // Assert
        Assert.True(result); // Returns true when multiple LogViewers with same name exist (Unique extension returns true for count > 1)
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldReturnFalse_WhenSingleLogViewerWithSameNameAndSameIdAsInput()
    {
        // Arrange
        await ClearDatabase();
        var referenceId = Guid.NewGuid().ToString();
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = "TestLogViewer";
        logViewer.ReferenceId = referenceId;

        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLogViewerNameUnique("TestLogViewer", referenceId);

        // Assert
        Assert.False(result); // Returns false when single LogViewer with same name and same ReferenceId (Unique extension returns false)
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldReturnTrue_WhenSingleLogViewerWithSameNameButDifferentId()
    {
        // Arrange
        await ClearDatabase();
        var existingReferenceId = Guid.NewGuid().ToString();
        var differentReferenceId = Guid.NewGuid().ToString();
        
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = "TestLogViewer";
        logViewer.ReferenceId = existingReferenceId;

        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLogViewerNameUnique("TestLogViewer", differentReferenceId);

        // Assert
        Assert.True(result); // Returns true when single LogViewer with same name but different ReferenceId (Unique extension returns true)
    }

    #endregion

    #region IsLogViewerNameUnique Tests - Edge Cases

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = "TestLogViewer";
        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        var validGuid = Guid.NewGuid().ToString();

        // Act
        var result1 = await _repository.IsLogViewerNameUnique("testlogviewer", validGuid); // lowercase
        var result2 = await _repository.IsLogViewerNameUnique("TESTLOGVIEWER", validGuid); // uppercase
        var result3 = await _repository.IsLogViewerNameUnique("TestLogViewer", validGuid); // exact case

        // Assert
        Assert.False(result1); // Different case - no match found
        Assert.False(result2); // Different case - no match found
        Assert.True(result3);  // Exact case - match found but different ID
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandleSpecialCharactersInName()
    {
        // Arrange
        await ClearDatabase();
        var specialName = "Test@Log#Viewer$123!";
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = specialName;
        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLogViewerNameUnique(specialName, "INVALID_GUID");

        // Assert
        Assert.True(result); // Should handle special characters correctly
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandleEmptyNameString()
    {
        // Arrange
        await ClearDatabase();
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = "";
        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLogViewerNameUnique("", "INVALID_GUID");

        // Assert
        Assert.True(result); // Should handle empty name string
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandleWhitespaceInName()
    {
        // Arrange
        await ClearDatabase();
        var nameWithSpaces = "  Test Log Viewer  ";
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = nameWithSpaces;
        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLogViewerNameUnique(nameWithSpaces, "INVALID_GUID");

        // Assert
        Assert.True(result); // Should handle whitespace in name exactly
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandleVeryLongNames()
    {
        // Arrange
        await ClearDatabase();
        var longName = new string('A', 1000); // Very long name
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = longName;
        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLogViewerNameUnique(longName, "INVALID_GUID");

        // Assert
        Assert.True(result); // Should handle very long names
    }

    #endregion

    #region IsLogViewerNameUnique Tests - GUID Validation Edge Cases

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldTreatEmptyGuidAsInvalidGuid()
    {
        // Arrange
        await ClearDatabase();
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = "TestLogViewer";
        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLogViewerNameUnique("TestLogViewer", Guid.Empty.ToString());

        // Assert
        Assert.True(result); // Empty GUID should be treated as invalid GUID
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandleValidGuidWithDifferentFormats()
    {
        // Arrange
        await ClearDatabase();
        var guid = Guid.NewGuid();
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = "TestLogViewer";
        logViewer.ReferenceId = guid.ToString();
        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result1 = await _repository.IsLogViewerNameUnique("TestLogViewer", guid.ToString()); // Standard format
        var result2 = await _repository.IsLogViewerNameUnique("TestLogViewer", guid.ToString("D")); // With hyphens
        var result3 = await _repository.IsLogViewerNameUnique("TestLogViewer", guid.ToString("N")); // Without hyphens

        // Assert
        Assert.False(result1); // Same GUID format - should match
        Assert.False(result2); // Same GUID with hyphens - should match
        Assert.True(result3);  // Different GUID format - should not match (string comparison)
    }

    #endregion

    #region IsLogViewerNameUnique Tests - Complex Business Scenarios

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandleMultipleLogViewersWithDifferentNames()
    {
        // Arrange
        await ClearDatabase();
        var logViewer1 = _autoFixture.Create<LogViewer>();
        logViewer1.Name = "LogViewer1";
        logViewer1.ReferenceId = Guid.NewGuid().ToString();

        var logViewer2 = _autoFixture.Create<LogViewer>();
        logViewer2.Name = "LogViewer2";
        logViewer2.ReferenceId = Guid.NewGuid().ToString();

        var logViewer3 = _autoFixture.Create<LogViewer>();
        logViewer3.Name = "LogViewer3";
        logViewer3.ReferenceId = Guid.NewGuid().ToString();

        await _context.LogViewers.AddRangeAsync(logViewer1, logViewer2, logViewer3);
        await _context.SaveChangesAsync();

        var newGuid = Guid.NewGuid().ToString();

        // Act
        var result1 = await _repository.IsLogViewerNameUnique("LogViewer1", newGuid);
        var result2 = await _repository.IsLogViewerNameUnique("LogViewer4", newGuid);

        // Assert
        Assert.True(result1);  // Existing name with different ID - not unique
        Assert.False(result2); // New name - unique
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandleInactiveLogViewers()
    {
        // Arrange
        await ClearDatabase();
        var activeLogViewer = _autoFixture.Create<LogViewer>();
        activeLogViewer.Name = "TestLogViewer";
        activeLogViewer.IsActive = true;
        activeLogViewer.ReferenceId = Guid.NewGuid().ToString();

        var inactiveLogViewer = _autoFixture.Create<LogViewer>();
        inactiveLogViewer.Name = "TestLogViewer";
        inactiveLogViewer.IsActive = false;
        inactiveLogViewer.ReferenceId = Guid.NewGuid().ToString();

        await _context.LogViewers.AddRangeAsync(activeLogViewer, inactiveLogViewer);
        await _context.SaveChangesAsync();

        var newGuid = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsLogViewerNameUnique("TestLogViewer", newGuid);

        // Assert
        Assert.True(result); // Should find both active and inactive LogViewers (method doesn't filter by IsActive)
    }

    //[Fact]
    //public async Task IsLogViewerNameUnique_ShouldHandleNullNameInDatabase()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var logViewer = _autoFixture.Create<LogViewer>();
    //    logViewer.Name = null;
    //    await _context.LogViewers.AddAsync(logViewer);
    //    await _context.SaveChangesAsync();

    //    // Act
    //    var result = await _repository.IsLogViewerNameUnique(null, "INVALID_GUID");

    //    // Assert - This will depend on how EF Core handles null comparisons
    //    // Typically, null.Equals(null) in LINQ to Entities might not behave as expected
    //    // The test documents the actual behavior
    //    Assert.False(result); // Documenting expected behavior - may need adjustment based on EF Core behavior
    //}

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandleCreateScenario()
    {
        // Arrange - Simulating creating a new LogViewer
        await ClearDatabase();
        var existingLogViewer = _autoFixture.Create<LogViewer>();
        existingLogViewer.Name = "ExistingLogViewer";
        await _context.LogViewers.AddAsync(existingLogViewer);
        await _context.SaveChangesAsync();

        // Act - Check if new name is unique (no ID provided, simulating create)
        var result1 = await _repository.IsLogViewerNameUnique("ExistingLogViewer", null);
        var result2 = await _repository.IsLogViewerNameUnique("NewLogViewer", null);

        // Assert
        Assert.True(result1);  // Existing name - not unique for create
        Assert.False(result2); // New name - unique for create
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandleUpdateScenario()
    {
        // Arrange - Simulating updating an existing LogViewer
        await ClearDatabase();
        var existingLogViewer1 = _autoFixture.Create<LogViewer>();
        existingLogViewer1.Name = "LogViewer1";
        existingLogViewer1.ReferenceId = Guid.NewGuid().ToString();

        var existingLogViewer2 = _autoFixture.Create<LogViewer>();
        existingLogViewer2.Name = "LogViewer2";
        existingLogViewer2.ReferenceId = Guid.NewGuid().ToString();

        await _context.LogViewers.AddRangeAsync(existingLogViewer1, existingLogViewer2);
        await _context.SaveChangesAsync();

        // Act - Check if updated name is unique
        var result1 = await _repository.IsLogViewerNameUnique("LogViewer1", existingLogViewer1.ReferenceId); // Same name, same ID
        var result2 = await _repository.IsLogViewerNameUnique("LogViewer2", existingLogViewer1.ReferenceId); // Different name, same ID
        var result3 = await _repository.IsLogViewerNameUnique("NewName", existingLogViewer1.ReferenceId);    // New name, same ID

        // Assert
        Assert.False(result1); // Same name, same ID - unique (can keep same name)
        Assert.True(result2);  // Different existing name, same ID - not unique
        Assert.False(result3); // New name, same ID - unique
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandleThreeLogViewersWithSameName()
    {
        // Arrange
        await ClearDatabase();
        var logViewer1 = _autoFixture.Create<LogViewer>();
        logViewer1.Name = "SameName";
        logViewer1.ReferenceId = Guid.NewGuid().ToString();

        var logViewer2 = _autoFixture.Create<LogViewer>();
        logViewer2.Name = "SameName";
        logViewer2.ReferenceId = Guid.NewGuid().ToString();

        var logViewer3 = _autoFixture.Create<LogViewer>();
        logViewer3.Name = "SameName";
        logViewer3.ReferenceId = Guid.NewGuid().ToString();

        await _context.LogViewers.AddRangeAsync(logViewer1, logViewer2, logViewer3);
        await _context.SaveChangesAsync();

        var newGuid = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.IsLogViewerNameUnique("SameName", newGuid);

        // Assert
        Assert.True(result); // Multiple LogViewers with same name - not unique (Unique extension returns true for count > 1)
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandleGuidCaseInsensitivity()
    {
        // Arrange
        await ClearDatabase();
        var guid = Guid.NewGuid();
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = "TestLogViewer";
        logViewer.ReferenceId = guid.ToString().ToUpper(); // Store in uppercase
        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result1 = await _repository.IsLogViewerNameUnique("TestLogViewer", guid.ToString().ToLower()); // Query with lowercase
        var result2 = await _repository.IsLogViewerNameUnique("TestLogViewer", guid.ToString().ToUpper()); // Query with uppercase

        // Assert
        Assert.True(result1);  // Different case GUID - should not match (string comparison is case-sensitive)
        Assert.False(result2); // Same case GUID - should match
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandleUnicodeCharacters()
    {
        // Arrange
        await ClearDatabase();
        var unicodeName = "测试日志查看器"; // Chinese characters
        var logViewer = _autoFixture.Create<LogViewer>();
        logViewer.Name = unicodeName;
        await _context.LogViewers.AddAsync(logViewer);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.IsLogViewerNameUnique(unicodeName, "INVALID_GUID");

        // Assert
        Assert.True(result); // Should handle Unicode characters correctly
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandlePerformanceWithManyLogViewers()
    {
        // Arrange
        await ClearDatabase();
        var logViewers = new List<LogViewer>();
        for (int i = 0; i < 100; i++)
        {
            var logViewer = _autoFixture.Create<LogViewer>();
            logViewer.Name = $"LogViewer{i}";
            logViewer.ReferenceId = Guid.NewGuid().ToString();
            logViewers.Add(logViewer);
        }

        // Add one with duplicate name
        var duplicateLogViewer = _autoFixture.Create<LogViewer>();
        duplicateLogViewer.Name = "LogViewer50";
        duplicateLogViewer.ReferenceId = Guid.NewGuid().ToString();
        logViewers.Add(duplicateLogViewer);

        await _context.LogViewers.AddRangeAsync(logViewers);
        await _context.SaveChangesAsync();

        var newGuid = Guid.NewGuid().ToString();

        // Act
        var result1 = await _repository.IsLogViewerNameUnique("LogViewer50", newGuid); // Duplicate name
        var result2 = await _repository.IsLogViewerNameUnique("LogViewer999", newGuid); // Unique name

        // Assert
        Assert.True(result1);  // Duplicate name found - not unique
        Assert.False(result2); // Unique name - unique
    }

    #endregion

    #region IsLogViewerNameUnique Tests - Error Handling

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldHandleNullNameParameter()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert - Should not throw exception
        var result1 = await _repository.IsLogViewerNameUnique(null, "INVALID_GUID");
        var result2 = await _repository.IsLogViewerNameUnique(null, Guid.NewGuid().ToString());

        // Assert - Document the behavior (may need adjustment based on actual EF Core behavior)
        Assert.False(result1); // null name with invalid GUID
        Assert.False(result2); // null name with valid GUID
    }

    [Fact]
    public async Task IsLogViewerNameUnique_ShouldReturnTaskSuccessfully()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var task = _repository.IsLogViewerNameUnique("TestName", "INVALID_GUID");

        // Assert
        Assert.NotNull(task);
        Assert.True(task.IsCompleted);
        var result = await task;
        Assert.False(result); // No LogViewers exist
    }

    #endregion
}
