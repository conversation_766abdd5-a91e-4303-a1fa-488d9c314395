﻿using ContinuityPatrol.Application.Features.Company.Commands.Create;
using ContinuityPatrol.Application.Features.Company.Commands.Update;
using ContinuityPatrol.Application.Features.Company.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Company.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Company.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Helper;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class CompanyControllerShould
    {
        private readonly Mock<IMapper> _mockMapper =new();
        private readonly Mock<ILogger<CompanyController>> _mockLogger = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private CompanyController _controller;

        private readonly Mock<IHttpContextAccessor> _mockHttpContextAccessor;
        private readonly Mock<HttpContext> _mockHttpContext;
        private readonly Mock<ISession> _mockSession;

        public CompanyControllerShould()
        {
            _mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            _mockHttpContext = new Mock<HttpContext>();
            _mockSession = new Mock<ISession>();
            _mockHttpContext.Setup(c => c.Session).Returns(_mockSession.Object);
            _mockHttpContextAccessor.Setup(a => a.HttpContext).Returns(_mockHttpContext.Object);

            Initialize();
        }

        internal void Initialize()
        {
            _controller = new CompanyController(
                _mockMapper.Object,
                _mockLogger.Object,
                _mockPublisher.Object,
                _mockDataProvider.Object
            );

            // Setup HttpContext with session
            var httpContext = new DefaultHttpContext();
            httpContext.Session = new TestSession();
            _controller.ControllerContext.HttpContext = httpContext;
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");

            // Configure WebHelper
            _mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext);
            WebHelper.Configure(_mockHttpContextAccessor.Object);
        }

        // ===== LIST TESTS =====

        [Fact]
        public async Task List_ShouldPublishCompanyPaginatedEventAndReturnView()
        {
            // Arrange
            _mockPublisher.Setup(x => x.Publish(It.IsAny<CompanyPaginatedEvent>(), default))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            _mockPublisher.Verify(x => x.Publish(It.IsAny<CompanyPaginatedEvent>(), default), Times.Once);
        }

        // ===== CREATE OR UPDATE TESTS =====

        [Fact]
        public async Task CreateOrUpdate_ShouldCreateCompany_WhenIdIsNullOrEmpty()
        {
            // Arrange
            var company = new CompanyViewModel
            {
                Name = "Test Company",
                CompanyLogo = "logo.png"
            };

            var createCommand = new CreateCompanyCommand { Name = "Test Company" };
            var response = new BaseResponse { Message = "Company created successfully" };

            // Mock form data with empty Id
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "Id", "" }
            });
            _controller.Request.Form = formCollection;

            // Mock WebHelper.UserSession.IsParent to return true
            SetupUserSessionAsParent();

            _mockMapper.Setup(x => x.Map<CreateCompanyCommand>(It.IsAny<CompanyViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(x => x.Company.CreateAsync(It.IsAny<CreateCompanyCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(company) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            var successProperty = resultValue.GetType().GetProperty("success");
            Assert.NotNull(successProperty);
            Assert.Equal(true, successProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldUpdateCompany_WhenIdIsProvided()
        {
            // Arrange
            var company = new CompanyViewModel
            {
                Id = "123",
                Name = "Updated Company",
                CompanyLogo = "logo.png"
            };

            var updateCommand = new UpdateCompanyCommand { Id = "123", Name = "Updated Company" };
            var response = new BaseResponse { Message = "Company updated successfully" };

            // Mock form data with Id
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "Id", "123" }
            });
            _controller.Request.Form = formCollection;

            // Mock WebHelper.UserSession.IsParent to return true
            SetupUserSessionAsParent();

            _mockMapper.Setup(x => x.Map<UpdateCompanyCommand>(It.IsAny<CompanyViewModel>()))
                .Returns(updateCommand);
            _mockDataProvider.Setup(x => x.Company.UpdateAsync(It.IsAny<UpdateCompanyCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(company) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            var successProperty = resultValue.GetType().GetProperty("success");
            Assert.NotNull(successProperty);
            Assert.Equal(true, successProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldReturnAccessRestricted_WhenUserIsNotParent()
        {
            // Arrange
            var company = new CompanyViewModel { Name = "Test Company" };

            // Mock WebHelper.UserSession.IsParent to return false
            SetupUserSessionAsNonParent();

            // Act
            var result = await _controller.CreateOrUpdate(company) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            var successProperty = resultValue.GetType().GetProperty("success");
            var messageProperty = resultValue.GetType().GetProperty("message");
            Assert.NotNull(successProperty);
            Assert.NotNull(messageProperty);
            Assert.Equal(false, successProperty.GetValue(resultValue));
            Assert.Equal("Access restricted to child company user", messageProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleNullCompanyLogo()
        {
            // Arrange
            var company = new CompanyViewModel
            {
                Name = "Test Company",
                CompanyLogo = null // Test null logo
            };

            var createCommand = new CreateCompanyCommand { Name = "Test Company" };
            var response = new BaseResponse { Message = "Company created successfully" };

            // Mock form data with empty Id
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "Id", "" }
            });
            _controller.Request.Form = formCollection;

            SetupUserSessionAsParent();

            _mockMapper.Setup(x => x.Map<CreateCompanyCommand>(It.IsAny<CompanyViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(x => x.Company.CreateAsync(It.IsAny<CreateCompanyCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(company) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(string.Empty, company.CompanyLogo); // Should be set to empty string
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleException()
        {
            // Arrange
            var company = new CompanyViewModel { Name = "Test Company" };

            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "Id", "" }
            });
            _controller.Request.Form = formCollection;

            SetupUserSessionAsParent();

            _mockMapper.Setup(x => x.Map<CreateCompanyCommand>(It.IsAny<CompanyViewModel>()))
                .Throws(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(company);

            // Assert
            Assert.IsType<JsonResult>(result);
        }

        // ===== DELETE TESTS =====

        [Fact]
        public async Task Delete_ShouldDeleteCompany_WhenUserIsParent()
        {
            // Arrange
            var id = "123";
            var response = new BaseResponse { Message = "Company deleted successfully" };

            SetupUserSessionAsParent();
            _mockDataProvider.Setup(x => x.Company.DeleteAsync(id))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            var successProperty = resultValue.GetType().GetProperty("Success");
            Assert.NotNull(successProperty);
            Assert.Equal(true, successProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task Delete_ShouldReturnAccessRestricted_WhenUserIsNotParent()
        {
            // Arrange
            var id = "123";

            SetupUserSessionAsNonParent();

            // Act
            var result = await _controller.Delete(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            var successProperty = resultValue.GetType().GetProperty("success");
            var messageProperty = resultValue.GetType().GetProperty("message");
            Assert.NotNull(successProperty);
            Assert.NotNull(messageProperty);
            Assert.Equal(false, successProperty.GetValue(resultValue));
            Assert.Equal("Access restricted to child company user", messageProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task Delete_ShouldHandleException()
        {
            // Arrange
            var id = "123";

            SetupUserSessionAsParent();
            _mockDataProvider.Setup(x => x.Company.DeleteAsync(id))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Delete(id);

            // Assert
            Assert.IsType<JsonResult>(result);
        }

        // ===== VALIDATION TESTS =====

        [Fact]
        public async Task IsCompanyNameExist_ShouldReturnTrue_WhenCompanyNameExists()
        {
            // Arrange
            var companyName = "Existing Company";
            var id = "123";
            _mockDataProvider.Setup(x => x.Company.IsCompanyNameExist(companyName, id))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.IsCompanyNameExist(companyName, id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");
            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.Equal(true, successProperty.GetValue(resultValue));
            Assert.Equal(true, dataProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task IsCompanyNameExist_ShouldReturnFalse_WhenCompanyNameDoesNotExist()
        {
            // Arrange
            var companyName = "Non-existing Company";
            var id = "123";
            _mockDataProvider.Setup(x => x.Company.IsCompanyNameExist(companyName, id))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.IsCompanyNameExist(companyName, id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");
            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.Equal(true, successProperty.GetValue(resultValue));
            Assert.Equal(false, dataProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task IsCompanyNameExist_ShouldHandleException()
        {
            // Arrange
            var companyName = "Test Company";
            var id = "123";
            _mockDataProvider.Setup(x => x.Company.IsCompanyNameExist(companyName, id))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.IsCompanyNameExist(companyName, id);

            // Assert
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task IsCompanyDisplayNameExist_ShouldReturnTrue_WhenDisplayNameExists()
        {
            // Arrange
            var displayName = "Existing Display Name";
            var id = "123";
            _mockDataProvider.Setup(x => x.Company.IsCompanyDisplayNameExist(displayName, id))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.IsCompanyDisplayNameExist(displayName, id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");
            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.Equal(true, successProperty.GetValue(resultValue));
            Assert.Equal(true, dataProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task IsCompanyDisplayNameExist_ShouldReturnFalse_WhenDisplayNameDoesNotExist()
        {
            // Arrange
            var displayName = "Non-existing Display Name";
            var id = "123";
            _mockDataProvider.Setup(x => x.Company.IsCompanyDisplayNameExist(displayName, id))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.IsCompanyDisplayNameExist(displayName, id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");
            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.Equal(true, successProperty.GetValue(resultValue));
            Assert.Equal(false, dataProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task IsCompanyDisplayNameExist_ShouldHandleException()
        {
            // Arrange
            var displayName = "Test Display Name";
            var id = "123";
            _mockDataProvider.Setup(x => x.Company.IsCompanyDisplayNameExist(displayName, id))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.IsCompanyDisplayNameExist(displayName, id);

            // Assert
            Assert.IsType<JsonResult>(result);
        }

        // ===== GET COMPANIES TESTS =====

        [Fact]
        public async Task GetCompanies_ShouldReturnCompanyList()
        {
            // Arrange
            var companies = new List<CompanyNameVm>
            {
                new CompanyNameVm { Id = "1", DisplayName = "Company 1" },
                new CompanyNameVm { Id = "2", DisplayName = "Company 2" }
            };
            _mockDataProvider.Setup(x => x.Company.GetCompanyNamesOnLogin())
                .ReturnsAsync(companies);

            // Act
            var result = await _controller.GetCompanies() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");
            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.Equal(true, successProperty.GetValue(resultValue));
            Assert.Equal(companies, dataProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task GetCompanies_ShouldHandleException()
        {
            // Arrange
            _mockDataProvider.Setup(x => x.Company.GetCompanyNamesOnLogin())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetCompanies();

            // Assert
            Assert.IsType<JsonResult>(result);
        }

        // ===== GET PAGINATION TESTS =====

        [Fact]
        public async Task GetPagination_ShouldReturnPaginatedList()
        {
            // Arrange
            var query = new GetCompanyPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "test"
            };
            var paginatedList = new PaginatedResult<CompanyListVm>
            {
                Data = new List<CompanyListVm>
                {
                    new CompanyListVm { Id = "1", Name = "Company 1" }
                },
                TotalCount = 1,
                CurrentPage = 1,
                PageSize = 10
            };
            _mockDataProvider.Setup(x => x.Company.GetPaginatedCompanies(query))
                .ReturnsAsync(paginatedList);

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");
            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.Equal(true, successProperty.GetValue(resultValue));
            Assert.Equal(paginatedList, dataProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task GetPagination_ShouldHandleException()
        {
            // Arrange
            var query = new GetCompanyPaginatedListQuery { PageNumber = 1, PageSize = 10 };
            _mockDataProvider.Setup(x => x.Company.GetPaginatedCompanies(query))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            Assert.IsType<JsonResult>(result);
        }

        // ===== GET COMPANY BY ID TESTS =====

        [Fact]
        public async Task GetCompanyById_ShouldReturnCompany()
        {
            // Arrange
            var id = "123";
            var company = new CompanyDetailVm
            {
                Id = id,
                Name = "Test Company",
                DisplayName = "Test Display Name"
            };
            _mockDataProvider.Setup(x => x.Company.GetCompanyById(id))
                .ReturnsAsync(company);

            // Act
            var result = await _controller.GetCompanyById(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            var successProperty = resultValue.GetType().GetProperty("success");
            var dataProperty = resultValue.GetType().GetProperty("data");
            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.Equal(true, successProperty.GetValue(resultValue));
            Assert.Equal(company, dataProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task GetCompanyById_ShouldHandleException()
        {
            // Arrange
            var id = "123";
            _mockDataProvider.Setup(x => x.Company.GetCompanyById(id))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetCompanyById(id);

            // Assert
            Assert.IsType<JsonResult>(result);
        }
        // ===== ADDITIONAL EDGE CASE TESTS =====

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleEmptyCompanyName()
        {
            // Arrange
            var company = new CompanyViewModel
            {
                Name = "", // Empty name
                CompanyLogo = "logo.png"
            };

            var createCommand = new CreateCompanyCommand { Name = "" };
            var response = new BaseResponse { Message = "Company created successfully" };

            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "Id", "" }
            });
            _controller.Request.Form = formCollection;

            SetupUserSessionAsParent();

            _mockMapper.Setup(x => x.Map<CreateCompanyCommand>(It.IsAny<CompanyViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(x => x.Company.CreateAsync(It.IsAny<CreateCompanyCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(company) as JsonResult;

            // Assert
            Assert.NotNull(result);
            _mockDataProvider.Verify(x => x.Company.CreateAsync(It.IsAny<CreateCompanyCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleWhitespaceOnlyCompanyName()
        {
            // Arrange
            var company = new CompanyViewModel
            {
                Name = "   ", // Whitespace only
                CompanyLogo = "logo.png"
            };

            var createCommand = new CreateCompanyCommand { Name = "   " };
            var response = new BaseResponse { Message = "Company created successfully" };

            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "Id", "" }
            });
            _controller.Request.Form = formCollection;

            SetupUserSessionAsParent();

            _mockMapper.Setup(x => x.Map<CreateCompanyCommand>(It.IsAny<CompanyViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(x => x.Company.CreateAsync(It.IsAny<CreateCompanyCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(company) as JsonResult;

            // Assert
            Assert.NotNull(result);
            _mockDataProvider.Verify(x => x.Company.CreateAsync(It.IsAny<CreateCompanyCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleVeryLongCompanyName()
        {
            // Arrange
            var longName = new string('A', 1000); // Very long name
            var company = new CompanyViewModel
            {
                Name = longName,
                CompanyLogo = "logo.png"
            };

            var createCommand = new CreateCompanyCommand { Name = longName };
            var response = new BaseResponse { Message = "Company created successfully" };

            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "Id", "" }
            });
            _controller.Request.Form = formCollection;

            SetupUserSessionAsParent();

            _mockMapper.Setup(x => x.Map<CreateCompanyCommand>(It.IsAny<CompanyViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(x => x.Company.CreateAsync(It.IsAny<CreateCompanyCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(company) as JsonResult;

            // Assert
            Assert.NotNull(result);
            _mockDataProvider.Verify(x => x.Company.CreateAsync(It.IsAny<CreateCompanyCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleSpecialCharactersInCompanyName()
        {
            // Arrange
            var specialName = "Company@#$%^&*()_+-=[]{}|;':\",./<>?";
            var company = new CompanyViewModel
            {
                Name = specialName,
                CompanyLogo = "logo.png"
            };

            var createCommand = new CreateCompanyCommand { Name = specialName };
            var response = new BaseResponse { Message = "Company created successfully" };

            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "Id", "" }
            });
            _controller.Request.Form = formCollection;

            SetupUserSessionAsParent();

            _mockMapper.Setup(x => x.Map<CreateCompanyCommand>(It.IsAny<CompanyViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(x => x.Company.CreateAsync(It.IsAny<CreateCompanyCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(company) as JsonResult;

            // Assert
            Assert.NotNull(result);
            _mockDataProvider.Verify(x => x.Company.CreateAsync(It.IsAny<CreateCompanyCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleUnicodeCharactersInCompanyName()
        {
            // Arrange
            var unicodeName = "公司名称 Société Компания 会社名";
            var company = new CompanyViewModel
            {
                Name = unicodeName,
                CompanyLogo = "logo.png"
            };

            var createCommand = new CreateCompanyCommand { Name = unicodeName };
            var response = new BaseResponse { Message = "Company created successfully" };

            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "Id", "" }
            });
            _controller.Request.Form = formCollection;

            SetupUserSessionAsParent();

            _mockMapper.Setup(x => x.Map<CreateCompanyCommand>(It.IsAny<CompanyViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(x => x.Company.CreateAsync(It.IsAny<CreateCompanyCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(company) as JsonResult;

            // Assert
            Assert.NotNull(result);
            _mockDataProvider.Verify(x => x.Company.CreateAsync(It.IsAny<CreateCompanyCommand>()), Times.Once);
        }

        // ===== HELPER METHODS =====

        private void SetupUserSessionAsParent()
        {
            var userSession = new UserSession
            {
                IsParent = true,
                CompanyId = "parent-company-123",
                LoggedUserId = "user-123"
            };

            WebHelper.UserSession = userSession;
        }

        private void SetupUserSessionAsNonParent()
        {
            var userSession = new UserSession
            {
                IsParent = false,
                CompanyId = "child-company-456",
                LoggedUserId = "user-456"
            };

            WebHelper.UserSession = userSession;
        }
    }
}
