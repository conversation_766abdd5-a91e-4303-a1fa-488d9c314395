﻿using ContinuityPatrol.Domain.ViewModels.ReportScheduleModel;

namespace ContinuityPatrol.Application.Features.ReportSchedule.Queries.GetList;

public class GetReportScheduleListQueryHandler : IRequestHandler<GetReportScheduleListQuery, List<ReportScheduleListVm>>
{
    private readonly IMapper _mapper;
    private readonly IReportScheduleRepository _reportScheduleRepository;

    public GetReportScheduleListQueryHandler(IMapper mapper, IReportScheduleRepository reportScheduleRepository)
    {
        _mapper = mapper;
        _reportScheduleRepository = reportScheduleRepository;
    }

    public async Task<List<ReportScheduleListVm>> Handle(GetReportScheduleListQuery request,
        CancellationToken cancellationToken)
    {
        var reportSchedule = (await _reportScheduleRepository.ListAllAsync()).ToList();

        return reportSchedule.Count == 0
            ? new List<ReportScheduleListVm>()
            : _mapper.Map<List<ReportScheduleListVm>>(reportSchedule);
    }
}