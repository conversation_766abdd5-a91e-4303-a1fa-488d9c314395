let fileDeleteData = '', fileExcludeData = '', fileIncludeData = '', prSSHData = '', drSSHData = '', globalDataSync = '', prShellPromptData = '', drShellPromptData = '', threadsData = '', dataSync = {}, selectedValue, filterOption = 'None';
let isSSHPrivatekeyPR = false, isSSHPrivatekeyDR = false, isDeletionFilter = false, isFolderPermission = false, parallelReplication = false, isIncrementalReplication = false, btnfalse = false;

const datSyncURL = {
    datSyncExistUrl: 'Configuration/DataSyncProperties/IsDataSyncNameExist',
    dataSyncPaginatedUrl: "/Configuration/DataSyncProperties/GetPaginated",
    saveOrUpdate: 'Configuration/DataSyncProperties/CreateOrUpdate'
}

let errorElements = ['#dataSyncNameError', '#replicationTypeError', '#drShellPromptError', 'fileIncludeError', '#prShellPromptError', '#drSSHError', "#prSSHError", "#fileDeleteError", "#fileExcludeError"];


$(function () {
    const datSyncPermission = {
        createPermission: $("#configurationdataCreate").data("create-permission")?.toLowerCase(),
        deletePermission: $("#configurationdataDelete").data("delete-permission")?.toLowerCase()
    }
    if (datSyncPermission.createPermission == 'false') {
        $("#btnDataSyncCreate").removeClass('#btnDataSyncCreate').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', datSyncPermission.createPermission == 'false');
    }
    let selectedValues = [];
    let dataTable = $('#dataSyncTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": datSyncURL.dataSyncPaginatedUrl,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "replicationType" :
                        sortIndex === 3 ? "status" : "";
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, orderable: false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    orderable: false
                },

                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<td><span class="text-truncate" title="${row.name}" style="max-width: 6em;display:inline-block"> ${row.name}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "replicationType", "name": "Replication Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<td><span title="${row.replicationType}" > ${row.replicationType}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "properties", "name": "SSH&nbsp;Private&nbsp;Key&nbsp;Path", "autoWidth": true,
                    "render": function (data, type, row) {
                        let name = JSON.parse(data).map(userpre => userpre.SSHPr);
                        let val = name[0]
                        if (val) {
                            if (type === 'display') {
                                return '<span title="' + val + '">' + val + '</span>';
                            }
                        }
                        return val ? val : "NA";
                    }
                },
                {
                    "data": "properties", "name": "SSH&nbsp;Private&nbsp;Key&nbsp;Path (DR)", "autoWidth": true,
                    "render": function (data, type, row) {
                        let name = JSON.parse(data).map(userpre => userpre.SSHDr);
                        let val = name[0]
                        if (val) {
                            let name = JSON.parse(data).map(userpre => userpre.SSHDr);
                            if (type === 'display') {
                                return '<span title="' + val + '">' + val + '</span>';
                            }
                        }
                        return val ? val : "NA";
                    }
                },
                {
                    "data": "properties", "name": "Retain&nbsp;Folder&nbsp;Permission", "autoWidth": true,
                    "render": function (data, type, row) {
                        let name = JSON.parse(data).map(userpre => userpre.isFolderPermission);
                        let val = name[0]
                        if (val) {
                            if (type === 'display') {
                                return '<span title="' + val + '">' + val + '</span>';
                            }
                        }
                        return val ? val : "False";
                    }
                },

                {
                    "data": "properties", "name": "Incremental Replication", "autoWidth": true,
                    "render": function (data, type, row) {
                        let name = JSON.parse(data).map(userpre => userpre.isIncrementalReplication);
                        let val = name[0]
                        if (val) {
                            if (type === 'display') {
                                return '<span title="' + val + '">' + val + '</span>';
                            }
                        }
                        return val ? val : "False";
                    }
                },
                {
                    "data": "properties", "name": "Shell&nbsp;Prompt&nbsp;(PR)", "autoWidth": true,
                    "render": function (data, type, row) {
                        let name = JSON.parse(data).map(userpre => userpre.PRShellPrompt);
                        let val = name[0]
                        if (val) {
                            if (type === 'display') {
                                return '<span title="' + val + '">' + val + '</span>';
                            }
                        }
                        return val ? val : "NA";
                    }
                },
                {
                    "data": "properties", "name": "Shell & nbsp; Prompt& nbsp; (DR)", "autoWidth": true,
                    "render": function (data, type, row) {
                        let name = JSON.parse(data).map(userpre => userpre.DRShellPrompt);
                        let val = name[0]
                        if (val) {
                            if (type === 'display') {
                                return '<span title="' + val + '">' + val + '</span>';
                            }
                        }
                        return val ? val : "NA";
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (datSyncPermission.createPermission === 'true' && datSyncPermission.deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="edit-button" data-datasync='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>          
                                 <span role="button" title="Delete" class="delete-button" data-datasync-id="${row.id}" data-datasync-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                 <i class="cp-Delete"></i>
                                </span>
                            </div>`;

                        }
                        else if (datSyncPermission.createPermission === 'true' && datSyncPermission.deletePermission === "false") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="edit-button" data-datasync='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>          
                                 <span role="button" title="Delete" class="icon-disabled">
                                 <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                        }
                        else if (datSyncPermission.createPermission === 'false' && datSyncPermission.deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>          
                                 <span role="button" title="Delete" class="delete-button" data-datasync-id="${row.id}" data-datasync-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                 <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                        }
                        else if (datSyncPermission.createPermission === 'false' && datSyncPermission.deletePermission === "false") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>          
                                 <span role="button" title="Delete" class="icon-disabled">
                                 <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                        }

                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    }); 
    // Search 
    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const checkboxes = [$("#name"), $("#ReplicationType")];
        const inputValue = $('#search-inp').val();

        checkboxes.forEach(checkbox => {
            if (checkbox.is(':checked')) {
                selectedValues.push(checkbox.val() + inputValue);
            }
        });

        let currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {
                if ($('#search-inp').val().length === 0) {
                    if (json?.data?.data?.length === 0) {
                        $('.dataTables_empty').text('No Data Found');
                    }
                } else if (json?.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }
    }, 500));



    //Update
    $('#dataSyncTable').on('click', '.edit-button', function () {
        dataSync = $(this).data("datasync");
        $('#CreateModal').modal('show');
        dataSyncEdit(dataSync);
        $('#dataSyncSave').text("Update");
    });

    //Delete
    $('#dataSyncTable').on('click', '.delete-button', function () {
        const datasyncId = $(this).data("datasync-id");
        const datasyncName = $(this).data("datasync-name")
        $('#deleteData').attr('title', datasyncName).text(datasyncName);
        $('#textDeleteId').val(datasyncId);
    });

    //Clear data
    $('#btnDataSyncCreate').on('click', function () {       
        $("#fileDeleteClm,#checkbox1,#checkbox4,#radioBtn,#threadsClm,#prSSHClm,#drSSHClm,#excludeClm,#IncludeClm1").hide();        
        clearInputFields('CreateForm', errorElements);
        $('#dataSyncSave').text('Save');
    });

    // DataSync Name
    $('#dataSyncName').on('input', commonDebounce(async function () {
        const value = $(this).val().replace(/\s{2,}/g, ' ');
        $(this).val(value); 
        const datasyncId = $('#dataSyncId').val();
        await validateName(value, datasyncId, IsNameExist);
    }, 400));

    // Replication Type
    $('#dataSyncReplicationType').on('change', function () {
        let value = $(this).val();
        validateDropDown(value, 'Select replication type ', 'replicationTypeError');

        if (value == 'Application') {
            $("#checkbox1,#checkbox4,#radioBtn").show();       
        } else {               
            $('#inlineRadio1').prop('checked', true);
            $("#excludeClm,#IncludeClm1,#radioBtn,#excludeClm,#checkbox1").hide();
            $("#checkbox4").show();
        }
    });

    // File Delete
    $('#txtFileDelete').on('input', function () {
        let value = $(this).val();
        fileDeleteData = value;
        validateDropDown(value, 'Enter file not to delete', 'fileDeleteError');
    });

    // Threads
    $('#txtThreads').on('input', function () {
        let sanitizedValue = $(this).val().replace(/[^0-9]/g, '');
        $(this).val(sanitizedValue);
        threadsData = sanitizedValue;
        validateDropDown(sanitizedValue, 'Enter number of threads', 'Threads-error');
    });
    // File Exclude
    $('#txtFileExclude').on('input', function () {
        let value = $(this).val();
        fileExcludeData = value
        validateDropDown(value, 'Enter file to exclude ', 'fileExcludeError')
    })

    // File Include
    $('#txtFileInclude').on('input', function () {
        let value = $(this).val();
        fileIncludeData = value
        validateDropDown(value, 'Enter file to include', 'fileIncludeError')
    })
    // SSHPr
    $('#txtSSHPr').on('input', function () {
        let sanitizedValue = $(this).val().replace(/^\s+/, '').replace(/\s{2,}/g, ' ');;
        $(this).val(sanitizedValue);
        prSSHData = sanitizedValue
        let errorElement = $('#prSSHError');
        validatePath(sanitizedValue, 'Enter SSH private key path (production)', errorElement)
    })

    // SSHDr
    $('#txtSSHDr').on('input', function () {
        let sanitizedValue = $(this).val().replace(/^\s+/, '').replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        let errorElement = $('#drSSHError');
        drSSHData = sanitizedValue
        validatePath(sanitizedValue, 'Enter SSH private key path (DR)', errorElement)
    })

    // PR ShellPrompt
    $('#prShellPrompt').on('input', function () {
        let sanitizedValue = $(this).val().replace(/^\s+/, '').replace(/\s{2,}/g, ' '); 
        $(this).val(sanitizedValue);
        prShellPromptData = sanitizedValue;
        validateDropDown(sanitizedValue, 'Enter production shell prompt', 'prShellPromptError');
    });

    // DR ShellPrompt   
    $('#drShellPrompt').on('input', function () {
        const sanitizedValue = $(this).val().replace(/^\s+/, '').replace(/\s{2,}/g, ' '); 
        $(this).val(sanitizedValue); 
        drShellPromptData = sanitizedValue;    
        validateDropDown(sanitizedValue, 'Enter DR shell prompt', 'drShellPromptError');      
    });

    var initialValue = $("input[name='inlineRadioOptions']:checked").val();
    selectedValue = initialValue;
    $("input[name='inlineRadioOptions']").on('change', function () {

        selectedValue = $(this).val();
        filterOption = selectedValue

        if (selectedValue == 'Exclude') {
            $('#IncludeClm1').hide();
            $('#excludeClm').show();
            $("#txtFileInclude").val('')
            fileIncludeData = ''
            $('#fileExcludeError').text('').removeClass('field-validation-error');

        } else if (selectedValue == 'Include') {
            $('#excludeClm').hide();
            $('#IncludeClm1').show();
            $("#txtFileExclude").val('')
            fileExcludeData =''
            $('#fileIncludeError').text('').removeClass('field-validation-error');
        } else {
            $("#excludeClm,#IncludeClm1").hide()
            $("#txtFileInclude,#txtFileExclude").val('')
        }

    });
   

    $(document).on('change', '.Checkbox', function (e) {
        let value = $(this).val();
        switch (value) {
            case "option1":

                if (e.target.checked) {
                    $("#fileDeleteClm").show();
                    isDeletionFilter = true
                    $('#fileDeleteError').text('').removeClass('field-validation-error');

                } else {
                    $("#fileDeleteClm").hide();
                    isDeletionFilter = false
                    $("#txtFileDelete").val('');
                    fileDeleteData = ''
                }
                break;
            case "option2":

                if (e.target.checked) {
                    $("#prSSHClm").show();
                    isSSHPrivatekeyPR = true
                    $('#prSSHError').text('').removeClass('field-validation-error');
                } else {
                    $("#prSSHClm").hide();
                    isSSHPrivatekeyPR = false
                    $("#txtSSHPr").val('');
                    prSSHData = ''
                }
                break;
            case "option3":

                if (e.target.checked) {
                    $("#drSSHClm").show();
                    isSSHPrivatekeyDR = true
                    $('#drSSHError').text('').removeClass('field-validation-error');
                } else {
                    $("#drSSHClm").hide();
                    isSSHPrivatekeyDR = false
                    $("#txtSSHDr").val('');
                    drSSHData = ''
                }
                break;
            case "option4":

                if (e.target.checked) {

                    isFolderPermission = true
                } else {
                    isFolderPermission = false
                }
                break;
        }
    });

    $(document).on('change', '.Checkboxs', function (e) {
        const value = $(this).val();
        if (value === 'options1') {
            if (e.target.checked) {
                $("#threadsClm").show();
                parallelReplication = true;
                $('#Threads-error').text('').removeClass('field-validation-error');
            } else {
                $("#threadsClm").hide();
                parallelReplication = false;
                $("#txtThreads").val('');
                threadsData = ''
            }
        }

        if (value === 'options2') {
            if (e.target.checked) {
                isIncrementalReplication = true;
            } else {
                isIncrementalReplication = false;
            }
        }
    });


    // DataSync SaveFunction

    $("#dataSyncSave").on('click', async function () {
       // let form = $('#CreateForm');
        let datasyncName = $("#dataSyncName").val();
        let dataId = $('#dataSyncId').val();
        let ReplicationType = $("#dataSyncReplicationType").val();
        let FileExclude = $("#txtFileExclude").val();
        let Include = $("#txtFileInclude").val();
        let FileDelete = $("#txtFileDelete").val();
        let SSHPr = $("#txtSSHPr").val();
        let SSHDr = $("#txtSSHDr").val();
        let PRShellPrompt = $("#prShellPrompt").val();
        let DRShellPrompt = $("#drShellPrompt").val();
        let Threads = $("#txtThreads").val();
        let isName = await validateName(datasyncName, dataId, IsNameExist);
        let isReplicationType = validateDropDown(ReplicationType, 'Select replication type', 'replicationTypeError');
        let errorElementSSHPr = $('#prSSHError');
        let errorElementSShDr = $('#drSSHError');
        let isThreads =  validateDropDown(Threads, ' Enter number of threads', 'Threads-error')
        let isFileExclude = validateDropDown(FileExclude, ' Enter file to exclude', 'fileExcludeError')
        let isFileInclude = validateDropDown(Include, ' Enter file to include', 'fileIncludeError')
        let isFileDelete = validateDropDown(FileDelete, ' Enter file not to delete', 'fileDeleteError')
        let isSSHPr =  await validatePath(SSHPr, 'Enter SSH private key path (production)', errorElementSSHPr)
        let isSSHDr =  await validatePath(SSHDr, 'Enter SSH private key path (DR)', errorElementSShDr)
        let isDRShellPrompt = validateDropDown(DRShellPrompt, 'Enter DR shell prompt ', 'drShellPromptError')
        let isPRShellPrompt = validateDropDown(PRShellPrompt, 'Enter production shell prompt', 'prShellPromptError')
        
        if (
            isName && isReplicationType && isPRShellPrompt && isDRShellPrompt && !btnfalse &&
            ($("#threadsClm").is(":visible") ? isThreads : true) && 
            ($("#IncludeClm1").is(":visible") ? isFileInclude : true) &&
            ($("#excludeClm").is(":visible") ? isFileExclude : true) &&
            ($("#fileDeleteClm").is(":visible") ? isFileDelete : true) &&
            ($("#prSSHClm").is(":visible") ? isSSHPr : true) &&
            ($("#drSSHClm").is(":visible") ? isSSHDr : true)
        ) {
            updateProperties();
            btnfalse = true
            // form.trigger('submit');
            savePermission()

        }
    });

    function updateProperties() {

        properties = [{
            FileDelete: fileDeleteData,
            FileExclude: fileExcludeData,
            FileInclude: fileIncludeData,
            SSHPr: prSSHData,
            SSHDr: drSSHData,
            PRShellPrompt: prShellPromptData,
            DRShellPrompt: drShellPromptData,
            isSSHPrivatekeyPR: isSSHPrivatekeyPR,
            isSSHPrivatekeyDR: isSSHPrivatekeyDR,
            isDeletionFilter: isDeletionFilter,
            isFolderPermission: isFolderPermission,
            ParallelReplication: parallelReplication,
            isIncrementalReplication: isIncrementalReplication,
            FilterOption: filterOption,
            ThreadsData: threadsData
        }];

        let propertiesVal = (JSON.stringify(properties));
        $('#dataSyncProperties').val(propertiesVal)
    }


    const savePermission = async () => {

        let data = {
            "Id": $('#dataSyncId').val(),
            "Name": $("#dataSyncName").val(),
            "ReplicationType": $("#dataSyncReplicationType").val(),          
            "Properties": $('#dataSyncProperties').val(),         
            __RequestVerificationToken: gettoken()
        }
        $('#dataSyncSave').text() === "Update" ? data["id"] = globalDataSync : null

        await $.ajax({
            type: "POST",
            url: RootUrl + datSyncURL.saveOrUpdate,
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result.data
                if (result.success) {
                    notificationAlert("success", data.message)
                    $('#CreateModal').modal('hide');
                   
                    setTimeout(() => {
                        dataTable.ajax.reload()
                        btnfalse = false
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            },
            error: function (response) {
                errorNotification(response)
            }
        })
    }

    // DataSync Name Validate 
    async function validateName(value, id = null) {
        
        const errorElement = $('#dataSyncNameError');
        if (!value) {
            errorElement.text('Enter datasync properties name ')
                .addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        var url = RootUrl + datSyncURL.datSyncExistUrl;
        var data = {};
        data.name = value;
        data.id = id;
        
        const validationResults = [
            await SpecialCharValidate(value),
            await ShouldNotBeginWithSpace(value),
            await ShouldNotBeginWithUnderScore(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await ShouldNotEndWithSpace(value),
            await ShouldNotAllowMultipleSpace(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsNameExist(url, data, OnError)
        ];
        return await CommonValidation(errorElement, validationResults);
    }
    async function IsNameExist(url, data, errorFunc) {
        return !data.name.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
    }

    // DataSync Path Validate
    async function validatePath(value, errorMsg, errorElement) {
        
        if (!value) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        }
        const validationResults = [
            await InvalidPathRegex(value)
        ];
        return await CommonValidation(errorElement, validationResults);
    }

    // DataSync DropDown Validate
    function validateDropDown(value, errorMessage, errorElement) {

        if (!value) {
            $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            $('#' + errorElement).text('').removeClass('field-validation-error');
            return true;
        }
    }

    // DataSync Edit 
    function dataSyncEdit(DataSync) {       
        globalDataSync = DataSync.id
        $('#dataSyncId').val(DataSync.id);
        $('#dataSyncName').val(DataSync.name);
        $('#dataSyncReplicationType').val(DataSync.replicationType);
        let DataSyncPropertie = JSON.parse(DataSync.properties);
        let firstObject = DataSyncPropertie[0];
        filterOption = firstObject.FilterOption

        if (DataSync.replicationType === 'Application') {
            $("#radioBtn").show();

            if (firstObject.FilterOption === 'None') {
                $("#excludeClm, #IncludeClm1").hide();
                $("#inlineRadio1").prop("checked", true);
                $("#inlineRadio1").val(firstObject.FilterOption);
            } else if (firstObject.FilterOption === 'Exclude') {
                $("#excludeClm").show();
                $("#inlineRadio2").prop("checked", true);
                $("#inlineRadio2").val(firstObject.FilterOption);
                $("#txtFileExclude").val(firstObject.FileExclude);
                fileExcludeData = firstObject.FileExclude
                $("#IncludeClm1").hide();
            } else if (firstObject.FilterOption === 'Include') {
                $("#IncludeClm1").show();
                $("#inlineRadio3").prop("checked", true);
                $("#inlineRadio3").val(firstObject.FilterOption);
                $("#txtFileInclude").val(firstObject.FileInclude);
                fileIncludeData = firstObject.FileInclude
                $("#excludeClm").hide();
            }
        } else if (DataSync.replicationType === 'Database') {
            $("#radioBtn, #excludeClm, #IncludeClm1").hide();
        }

        if (DataSync.replicationType === 'Application') {
            $("#checkbox1, #checkbox4").show();
            if (firstObject.isDeletionFilter) {
                $("#fileDeleteClm").show()
                $("#inlineCheckbox1").prop("checked", firstObject.isDeletionFilter);
                $("#txtFileDelete").val(firstObject.FileDelete)
                isDeletionFilter = firstObject.isDeletionFilter
                fileDeleteData = firstObject.FileDelete

            } else {
                $("#fileDeleteClm").hide()
                $("#inlineCheckbox1").prop("checked", false);
            }
        } else if (DataSync.replicationType === 'Database') {
            $("#checkbox4").show();           
            $("#fileDeleteClm,#checkbox1").hide()
        }
        $("#prShellPrompt").val(firstObject.PRShellPrompt)
        $("#drShellPrompt").val(firstObject.DRShellPrompt)
        prShellPromptData = firstObject.PRShellPrompt
        drShellPromptData = firstObject.DRShellPrompt
        isSSHPrivatekeyPR = firstObject.isSSHPrivatekeyPR
        isSSHPrivatekeyDR = firstObject.isSSHPrivatekeyDR

        if (firstObject.isSSHPrivatekeyPR) {
            $("#inlineCheckbox2").prop("checked", firstObject.isSSHPrivatekeyPR);
            $("#prSSHClm").show();
            $("#txtSSHPr").val(firstObject.SSHPr)
            prSSHData = firstObject.SSHPr
        } else {
            $("#inlineCheckbox2").prop("checked", false);
            $("#prSSHClm").hide();
        }
        if (firstObject.isSSHPrivatekeyDR) {
            $("#inlineCheckbox3").prop("checked", firstObject.isSSHPrivatekeyDR);
            $("#drSSHClm").show();
            $("#txtSSHDr").val(firstObject.SSHDr)
            drSSHData = firstObject.SSHDr
        } else { 
            $("#inlineCheckbox3").prop("checked", false);
            $("#drSSHClm").hide();
        }
        if (firstObject.isFolderPermission) {

            $("#inlineCheckbox4").prop("checked", firstObject.isFolderPermission);

            isFolderPermission = firstObject.isFolderPermission
        } else {
            $("#inlineCheckbox4").prop("checked", false);

        }

        parallelReplication = firstObject.ParallelReplication
        if (firstObject.ParallelReplication) {
            $("#inlineCheckboxs1").prop("checked", firstObject.ParallelReplication);
            $("#threadsClm").show();
            $("#txtThreads").val(firstObject.ThreadsData);
            threadsData = firstObject.ThreadsData
        } else {
            $("#inlineCheckboxs1").prop("checked", false);
            $("#threadsClm").hide();
        }
        if (firstObject.isIncrementalReplication) {
            $("#inlineCheckboxs2").prop("checked", firstObject.isIncrementalReplication);

            isIncrementalReplication = firstObject.isIncrementalReplication
        } else {
            $("#inlineCheckboxs2").prop("checked", false);

        }
       
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error')
        });
    }

})
