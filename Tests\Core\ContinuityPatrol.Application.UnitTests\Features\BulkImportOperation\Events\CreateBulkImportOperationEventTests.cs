using ContinuityPatrol.Application.Features.BulkImportOperation.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperation.Events;

public class CreateBulkImportOperationEventTests : IClassFixture<BulkImportOperationFixture>, IClassFixture<UserActivityFixture>
{
    private readonly BulkImportOperationFixture _bulkImportOperationFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly BulkImportOperationCreatedEventHandler _handler;

    public CreateBulkImportOperationEventTests(BulkImportOperationFixture bulkImportOperationFixture, UserActivityFixture userActivityFixture)
    {
        _bulkImportOperationFixture = bulkImportOperationFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");
        mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/bulkimportoperation");
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        var mockBulkImportOperationEventLogger = new Mock<ILogger<BulkImportOperationCreatedEventHandler>>();

        _mockUserActivityRepository = BulkImportOperationRepositoryMocks.CreateBulkImportOperationEventRepository(_userActivityFixture.UserActivities);

        _handler = new BulkImportOperationCreatedEventHandler(
            mockLoggedInUserService.Object, 
            mockBulkImportOperationEventLogger.Object, 
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateBulkImportOperationEventCreated()
    {
        // Arrange
        _userActivityFixture.UserActivities[0].LoginName = "Tester";
        var bulkImportOperationCreatedEvent = new BulkImportOperationCreatedEvent { Name = "TestUser" };

        // Act
        var result = _handler.Handle(bulkImportOperationCreatedEvent, CancellationToken.None);

        // Assert
        Assert.True(result.IsCompleted);
        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var bulkImportOperationCreatedEvent = new BulkImportOperationCreatedEvent { Name = "TestUser" };

        // Act
        await _handler.Handle(bulkImportOperationCreatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_WithCorrectProperties()
    {
        // Arrange
        var bulkImportOperationCreatedEvent = new BulkImportOperationCreatedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.LoginName.ShouldBe("Tester");
        capturedUserActivity.Action.ShouldContain("Create BulkImportOperation");
        capturedUserActivity.Entity.ShouldBe("BulkImportOperation");
        capturedUserActivity.ActivityType.ShouldBe("Create");
        capturedUserActivity.ActivityDetails.ShouldContain("TestUser");
        capturedUserActivity.ActivityDetails.ShouldContain("created successfully");
    }

    [Fact]
    public async Task Handle_LogCorrectInformation_When_BulkImportOperationCreated()
    {
        // Arrange
        var bulkImportOperationCreatedEvent = new BulkImportOperationCreatedEvent { Name = "TestUser" };
        var mockLogger = new Mock<ILogger<BulkImportOperationCreatedEventHandler>>();

        var handler = new BulkImportOperationCreatedEventHandler(
            new Mock<ILoggedInUserService>().Object,
            mockLogger.Object,
            _mockUserActivityRepository.Object);

        // Act
        await handler.Handle(bulkImportOperationCreatedEvent, CancellationToken.None);

      
    }

    [Fact]
    public async Task Handle_SetCorrectUserActivityProperties_When_UserServiceHasData()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var testUrl = "/api/test";
        var testIpAddress = "***********";
        var testLoginName = "TestUser";

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);
        mockLoggedInUserService.Setup(x => x.LoginName).Returns(testLoginName);
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testUrl);
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testIpAddress);

        var handler = new BulkImportOperationCreatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationCreatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationCreatedEvent = new BulkImportOperationCreatedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.UserId.ShouldBe(testUserId);
        capturedUserActivity.LoginName.ShouldBe(testLoginName);
        capturedUserActivity.RequestUrl.ShouldBe(testUrl);
        capturedUserActivity.HostAddress.ShouldBe(testIpAddress);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_EventHandled()
    {
        // Arrange
        var bulkImportOperationCreatedEvent = new BulkImportOperationCreatedEvent { Name = "ProductionUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.Action.ShouldBe("Create BulkImportOperation");
        capturedUserActivity.Entity.ShouldBe("BulkImportOperation");
        capturedUserActivity.ActivityType.ShouldBe("Create");
        capturedUserActivity.ActivityDetails.ShouldBe("BulkImportOperation 'ProductionUser' created successfully.");
    }

    [Fact]
    public async Task Handle_SetCreatedByAndLastModifiedBy_When_UserIdExists()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);

        var handler = new BulkImportOperationCreatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationCreatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationCreatedEvent = new BulkImportOperationCreatedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.CreatedBy.ShouldBe(testUserId);
        capturedUserActivity.LastModifiedBy.ShouldBe(testUserId);
    }

    [Fact]
    public async Task Handle_SetDefaultGuidForCreatedBy_When_UserIdIsEmpty()
    {
        // Arrange
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns("");

        var handler = new BulkImportOperationCreatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationCreatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationCreatedEvent = new BulkImportOperationCreatedEvent { Name = "TestUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.CreatedBy.ShouldNotBeNullOrEmpty();
        capturedUserActivity.LastModifiedBy.ShouldNotBeNullOrEmpty();
        Guid.TryParse(capturedUserActivity.CreatedBy, out _).ShouldBeTrue();
        Guid.TryParse(capturedUserActivity.LastModifiedBy, out _).ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_EventProcessed()
    {
        // Arrange
        var bulkImportOperationCreatedEvent = new BulkImportOperationCreatedEvent { Name = null };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldContain("created successfully");
    }

    [Fact]
    public async Task Handle_UseUserNameAsEventName_When_BulkImportOperationCreated()
    {
        // Arrange
        var bulkImportOperationCreatedEvent = new BulkImportOperationCreatedEvent { Name = "AdminUser" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldContain("AdminUser");
        capturedUserActivity.ActivityDetails.ShouldBe("BulkImportOperation 'AdminUser' created successfully.");
    }
}
