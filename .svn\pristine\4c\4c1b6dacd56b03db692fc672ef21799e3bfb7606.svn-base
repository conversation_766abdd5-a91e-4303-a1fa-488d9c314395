namespace ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetDetail;

public class GetHacmpClusterDetailsQueryHandler : IRequestHandler<GetHacmpClusterDetailQuery, HacmpClusterDetailVm>
{
    private readonly IHacmpClusterRepository _hacmpClusterRepository;
    private readonly IMapper _mapper;

    public GetHacmpClusterDetailsQueryHandler(IMapper mapper, IHacmpClusterRepository hacmpClusterRepository)
    {
        _mapper = mapper;
        _hacmpClusterRepository = hacmpClusterRepository;
    }

    public async Task<HacmpClusterDetailVm> Handle(GetHacmpClusterDetailQuery request,
        CancellationToken cancellationToken)
    {
        var hacmpCluster = await _hacmpClusterRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(hacmpCluster, nameof(Domain.Entities.HacmpCluster),
            new NotFoundException(nameof(Domain.Entities.HacmpCluster), request.Id));

        var hacmpClusterDetailDto = _mapper.Map<HacmpClusterDetailVm>(hacmpCluster);

        return hacmpClusterDetailDto;
    }
}