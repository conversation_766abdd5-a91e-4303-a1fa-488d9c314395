using ContinuityPatrol.Application.Features.DataSyncJob.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncJob.Commands.Delete;
using ContinuityPatrol.Application.Features.DataSyncJob.Commands.Update;
using ContinuityPatrol.Application.Features.DataSyncJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSyncJob.Queries.GetList;
using ContinuityPatrol.Application.Features.DataSyncJob.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.DataSyncJobModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DataSyncJobFixture
{
    public CreateDataSyncJobCommand CreateDataSyncJobCommand { get; }
    public CreateDataSyncJobResponse CreateDataSyncJobResponse { get; }
    public UpdateDataSyncJobCommand UpdateDataSyncJobCommand { get; }
    public UpdateDataSyncJobResponse UpdateDataSyncJobResponse { get; }
    public DeleteDataSyncJobCommand DeleteDataSyncJobCommand { get; }
    public DeleteDataSyncJobResponse DeleteDataSyncJobResponse { get; }
    public DataSyncJobDetailVm DataSyncJobDetailVm { get; }
    public DataSyncJobListVm DataSyncJobListVm { get; }
    public GetDataSyncJobListQuery GetDataSyncJobListQuery { get; }
    public GetDataSyncJobDetailQuery GetDataSyncJobDetailQuery { get; }
    public GetDataSyncJobPaginatedQuery GetDataSyncJobPaginatedQuery { get; }

    public DataSyncJobFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateDataSyncJobCommand>(c => c
            .With(b => b.ReplicationId, Guid.NewGuid().ToString())
            .With(b => b.ReplicationName, "Enterprise Primary Replication")
            .With(b => b.DataSyncOptionId, Guid.NewGuid().ToString())
            .With(b => b.ReplicationTypeId, Guid.NewGuid().ToString())
            .With(b => b.ReplicationType, "Real-time Synchronization")
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, "Enterprise Primary Site")
            .With(b => b.Properties, "{\"syncInterval\":\"5min\",\"retryCount\":3,\"timeout\":\"30s\"}")
            .With(b => b.JobProperties, "{\"priority\":\"High\",\"maxConcurrency\":5,\"batchSize\":1000}")
            .With(b => b.ScheduleProperties, "{\"schedule\":\"0 */5 * * * *\",\"timezone\":\"UTC\",\"enabled\":true}")
            .With(b => b.SourceDirectory, "/enterprise/data/source")
            .With(b => b.DestinationDirectory, "/enterprise/data/destination")
            .With(b => b.ModeType, "Incremental")
            .With(b => b.LastSuccessfullReplTime, DateTime.UtcNow.AddMinutes(-5).ToString("yyyy-MM-dd HH:mm:ss")));

        fixture.Customize<CreateDataSyncJobResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Message, "Enterprise Primary Replication job created successfully!")
            .With(b => b.Success, true));

        fixture.Customize<UpdateDataSyncJobCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.ReplicationId, Guid.NewGuid().ToString())
            .With(b => b.ReplicationName, "Enterprise Updated Replication")
            .With(b => b.DataSyncOptionId, Guid.NewGuid().ToString())
            .With(b => b.ReplicationTypeId, Guid.NewGuid().ToString())
            .With(b => b.ReplicationType, "Batch Synchronization")
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, "Enterprise Secondary Site")
            .With(b => b.Properties, "{\"syncInterval\":\"10min\",\"retryCount\":5,\"timeout\":\"60s\"}")
            .With(b => b.JobProperties, "{\"priority\":\"Medium\",\"maxConcurrency\":3,\"batchSize\":2000}")
            .With(b => b.ScheduleProperties, "{\"schedule\":\"0 */10 * * * *\",\"timezone\":\"UTC\",\"enabled\":true}")
            .With(b => b.SourceDirectory, "/enterprise/data/updated_source")
            .With(b => b.DestinationDirectory, "/enterprise/data/updated_destination")
            .With(b => b.ModeType, "Full")
            .With(b => b.LastSuccessfullReplTime, DateTime.UtcNow.AddMinutes(-10).ToString("yyyy-MM-dd HH:mm:ss")));

        fixture.Customize<UpdateDataSyncJobResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Message, "Enterprise Updated Replication job updated successfully!")
            .With(b => b.Success, true));

        fixture.Customize<DeleteDataSyncJobCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<DeleteDataSyncJobResponse>(c => c
            .With(b => b.IsActive, false)
            .With(b => b.Message, "Enterprise Replication job deleted successfully!")
            .With(b => b.Success, true));

        fixture.Customize<DataSyncJobDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.ReplicationId, Guid.NewGuid().ToString())
            .With(b => b.ReplicationName, "Enterprise Detail Replication")
            .With(b => b.DataSyncOptionId, Guid.NewGuid().ToString())
            .With(b => b.ReplicationTypeId, Guid.NewGuid().ToString())
            .With(b => b.ReplicationType, "Continuous Synchronization")
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, "Enterprise Detail Site")
            .With(b => b.Properties, "{\"syncInterval\":\"1min\",\"retryCount\":10,\"timeout\":\"120s\"}")
            .With(b => b.JobProperties, "{\"priority\":\"Critical\",\"maxConcurrency\":10,\"batchSize\":500}")
            .With(b => b.ScheduleProperties, "{\"schedule\":\"0 * * * * *\",\"timezone\":\"UTC\",\"enabled\":true}")
            .With(b => b.SourceDirectory, "/enterprise/data/detail_source")
            .With(b => b.DestinationDirectory, "/enterprise/data/detail_destination")
            .With(b => b.ModeType, "Delta")
            .With(b => b.LastSuccessfullReplTime, DateTime.UtcNow.AddMinutes(-1).ToString("yyyy-MM-dd HH:mm:ss")));

        fixture.Customize<DataSyncJobListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.ReplicationId, Guid.NewGuid().ToString())
            .With(b => b.ReplicationName, "Enterprise List Replication")
            .With(b => b.DataSyncOptionId, Guid.NewGuid().ToString())
            .With(b => b.ReplicationTypeId, Guid.NewGuid().ToString())
            .With(b => b.ReplicationType, "Scheduled Synchronization")
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, "Enterprise List Site")
            .With(b => b.Properties, "{\"syncInterval\":\"15min\",\"retryCount\":3,\"timeout\":\"45s\"}")
            .With(b => b.JobProperties, "{\"priority\":\"Normal\",\"maxConcurrency\":2,\"batchSize\":1500}")
            .With(b => b.ScheduleProperties, "{\"schedule\":\"0 */15 * * * *\",\"timezone\":\"UTC\",\"enabled\":true}")
            .With(b => b.SourceDirectory, "/enterprise/data/list_source")
            .With(b => b.DestinationDirectory, "/enterprise/data/list_destination")
            .With(b => b.ModeType, "Incremental")
            .With(b => b.LastSuccessfullReplTime, DateTime.UtcNow.AddMinutes(-15).ToString("yyyy-MM-dd HH:mm:ss")));

        fixture.Customize<GetDataSyncJobDetailQuery>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<GetDataSyncJobPaginatedQuery>(c => c
            .With(b => b.PageNumber, 1)
            .With(b => b.PageSize, 10)
            .With(b => b.SearchString, "Enterprise")
           );

        CreateDataSyncJobCommand = fixture.Create<CreateDataSyncJobCommand>();
        CreateDataSyncJobResponse = fixture.Create<CreateDataSyncJobResponse>();
        UpdateDataSyncJobCommand = fixture.Create<UpdateDataSyncJobCommand>();
        UpdateDataSyncJobResponse = fixture.Create<UpdateDataSyncJobResponse>();
        DeleteDataSyncJobCommand = fixture.Create<DeleteDataSyncJobCommand>();
        DeleteDataSyncJobResponse = fixture.Create<DeleteDataSyncJobResponse>();
        DataSyncJobDetailVm = fixture.Create<DataSyncJobDetailVm>();
        DataSyncJobListVm = fixture.Create<DataSyncJobListVm>();
        GetDataSyncJobListQuery = fixture.Create<GetDataSyncJobListQuery>();
        GetDataSyncJobDetailQuery = fixture.Create<GetDataSyncJobDetailQuery>();
        GetDataSyncJobPaginatedQuery = fixture.Create<GetDataSyncJobPaginatedQuery>();
    }
}
