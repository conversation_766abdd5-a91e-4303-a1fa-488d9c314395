﻿using AutoFixture;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Create;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Update;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetReplicationMasterByInfraMasterName;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Domain.ViewModels.InfraReplicationMappingModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class InfraReplicationMappingControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IDataProvider> _mockProvider = new();
        private readonly Mock<ILogger<InfraReplicationMappingController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private  InfraReplicationMappingController _controller;

        public InfraReplicationMappingControllerShould()
        {
            Initialize();
        }
        internal void Initialize()
        {
            _controller = new InfraReplicationMappingController(
                _mockPublisher.Object,
                _mockProvider.Object,
                _mockMapper.Object,
                _mockLogger.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ReturnsViewResult()
        {
            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
        }
        
        [Fact]
        public async Task CreateOrUpdate_CreatesInfraReplicationMappingSuccessfully()
        {
            
            var model = new AutoFixture.Fixture().Create<InfraReplicationMappingViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateInfraReplicationMappingCommand ();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateInfraReplicationMappingCommand>(model)).Returns(createCommand);
            _mockProvider.Setup(p => p.InfraReplicationMapping.CreateAsync(createCommand)).ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesInfraReplicationMappingSuccessfully()
        {
            
            var model = new AutoFixture.Fixture().Create <InfraReplicationMappingViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateInfraReplicationMappingCommand ();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateInfraReplicationMappingCommand>(model)).Returns(updateCommand);
            _mockProvider.Setup(p => p.InfraReplicationMapping.UpdateAsync(updateCommand)).ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
           
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesValidationException_Create()
        {
            // Arrange
            var model = new InfraReplicationMappingViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", ""); // Empty id for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateInfraReplicationMappingCommand();
            _mockMapper.Setup(m => m.Map<CreateInfraReplicationMappingCommand>(model)).Returns(createCommand);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockProvider.Setup(p => p.InfraReplicationMapping.CreateAsync(createCommand)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesValidationException_Update()
        {
            // Arrange
            var model = new InfraReplicationMappingViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateInfraReplicationMappingCommand();
            _mockMapper.Setup(m => m.Map<UpdateInfraReplicationMappingCommand>(model)).Returns(updateCommand);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockProvider.Setup(p => p.InfraReplicationMapping.UpdateAsync(updateCommand)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        
        

        [Fact]
        public async Task Delete_ReturnsRedirectToPostView()
        {
            
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockProvider.Setup(p => p.InfraReplicationMapping.DeleteAsync("some-id")).ReturnsAsync(response);

            
            var result = await _controller.Delete("some-id") as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
           
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResultWithPaginatedList()
        {

            
            
            var query = new AutoFixture.Fixture().Create<GetInfraReplicationMappingPaginatedListQuery>();
            _mockProvider.Setup(p => p.InfraReplicationMapping.GetPaginatedInfraReplicationMapping(query)).ReturnsAsync(It.IsAny<PaginatedResult<InfraReplicationMappingListVm>>);
            
            var result = await _controller.GetPagination(query) as JsonResult;
            var json = JsonConvert.SerializeObject(result);
            Assert.IsType<JsonResult>(result);
           
            
        }

        [Fact]
        public async Task GetReplicationComponentType_ReturnsJsonResultWithServerTypes()
        {
            
            var serverList = new List<ComponentTypeModel> ();
            _mockProvider.Setup(p => p.ComponentType.GetComponentTypeListByName("Replication")).ReturnsAsync(serverList);

            
            var result = await _controller.GetReplicationComponentType() as JsonResult;
            var json = JsonConvert.SerializeObject(result);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task GetDatabaseComponentType_ReturnsJsonResultWithDatabases()
        {
            
            var databaseList = new List<ComponentTypeModel> ();
            _mockProvider.Setup(p => p.ComponentType.GetComponentTypeListByName("Database")).ReturnsAsync(databaseList);

            
            var result = await _controller.GetDatabaseComponentType() as JsonResult;
            var json = JsonConvert.SerializeObject(result);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":true", json);

            
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task GetReplicationMasterByInfraMasterName_ReturnsJsonResultWithReplicationMasters()
        {
            // Arrange
            var replicationMasterList = new List<GetByInfraMasterNameVm>();
            _mockProvider.Setup(p => p.ReplicationMaster.GetReplicationMasterByInfraMasterName("InfraName")).ReturnsAsync(replicationMasterList);

            // Act
            var result = await _controller.GetReplicationMasterByInfraMasterName("InfraName") as JsonResult;

            // Assert
            var json = JsonConvert.SerializeObject(result);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetReplicationMasterByInfraMasterName_ReturnsEmptyJson_WhenInfraMasterNameIsNull()
        {
            // Act
            var result = await _controller.GetReplicationMasterByInfraMasterName(null) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("", result.Value);
        }

        [Fact]
        public async Task GetReplicationMasterByInfraMasterName_ReturnsEmptyJson_WhenInfraMasterNameIsEmpty()
        {
            // Act
            var result = await _controller.GetReplicationMasterByInfraMasterName("") as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("", result.Value);
        }

        [Fact]
        public async Task GetReplicationMasterByInfraMasterName_ReturnsEmptyJson_WhenInfraMasterNameIsWhitespace()
        {
            // Act
            var result = await _controller.GetReplicationMasterByInfraMasterName("   ") as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("", result.Value);
        }

        // Exception handling tests
        [Fact]
        public async Task CreateOrUpdate_HandlesException_Create()
        {
            // Arrange
            var model = new InfraReplicationMappingViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", ""); // Empty id for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateInfraReplicationMappingCommand();
            _mockMapper.Setup(m => m.Map<CreateInfraReplicationMappingCommand>(model)).Returns(createCommand);
            _mockProvider.Setup(p => p.InfraReplicationMapping.CreateAsync(createCommand)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesException_Update()
        {
            // Arrange
            var model = new InfraReplicationMappingViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateInfraReplicationMappingCommand();
            _mockMapper.Setup(m => m.Map<UpdateInfraReplicationMappingCommand>(model)).Returns(updateCommand);
            _mockProvider.Setup(p => p.InfraReplicationMapping.UpdateAsync(updateCommand)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task Delete_HandlesException()
        {
            // Arrange
            var id = "testId";
            _mockProvider.Setup(p => p.InfraReplicationMapping.DeleteAsync(id)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task GetPagination_HandlesException()
        {
            // Arrange
            var query = new GetInfraReplicationMappingPaginatedListQuery();
            _mockProvider.Setup(p => p.InfraReplicationMapping.GetPaginatedInfraReplicationMapping(query)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            // Should return exception JSON format
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetReplicationComponentType_HandlesException()
        {
            // Arrange
            _mockProvider.Setup(p => p.ComponentType.GetComponentTypeListByName("Replication")).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetReplicationComponentType() as JsonResult;

            // Assert
            Assert.NotNull(result);
            // Should return exception JSON format
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetDatabaseComponentType_HandlesException()
        {
            // Arrange
            _mockProvider.Setup(p => p.ComponentType.GetComponentTypeListByName("Database")).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetDatabaseComponentType() as JsonResult;

            // Assert
            Assert.NotNull(result);
            // Should return exception JSON format
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetReplicationMasterByInfraMasterName_HandlesException()
        {
            // Arrange
            var infraMasterName = "testName";
            _mockProvider.Setup(p => p.ReplicationMaster.GetReplicationMasterByInfraMasterName(infraMasterName)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetReplicationMasterByInfraMasterName(infraMasterName) as JsonResult;

            // Assert
            Assert.NotNull(result);
            // Should return exception JSON format
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        // ===== ADDITIONAL TESTS TO COVER UNCOVERED LINES =====

        [Fact]
        public async Task CreateOrUpdate_UpdatesWithProperLoggingAndNotification()
        {
            // Arrange
            var model = new InfraReplicationMappingViewModel
            {
                Type = "TestReplicationType"
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "123"); // Non-empty id for update path
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateInfraReplicationMappingCommand
            {
                Type = "TestReplicationType"
            };
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateInfraReplicationMappingCommand>(model)).Returns(updateCommand);
            _mockProvider.Setup(p => p.InfraReplicationMapping.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);

            // Verify that UpdateAsync was called
            _mockProvider.Verify(p => p.InfraReplicationMapping.UpdateAsync(updateCommand), Times.Once);

            // Verify that the mapper was called
            _mockMapper.Verify(m => m.Map<UpdateInfraReplicationMappingCommand>(model), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesWithNullTypeHandling()
        {
            // Arrange
            var model = new InfraReplicationMappingViewModel
            {
                Type = null
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "456"); // Non-empty id for update path
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateInfraReplicationMappingCommand
            {
                Type = null
            };
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateInfraReplicationMappingCommand>(model)).Returns(updateCommand);
            _mockProvider.Setup(p => p.InfraReplicationMapping.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);

            // Verify that UpdateAsync was called
            _mockProvider.Verify(p => p.InfraReplicationMapping.UpdateAsync(updateCommand), Times.Once);

            // Verify that the mapper was called
            _mockMapper.Verify(m => m.Map<UpdateInfraReplicationMappingCommand>(model), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesWithEmptyTypeHandling()
        {
            // Arrange
            var model = new InfraReplicationMappingViewModel
            {
                Type = ""
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "789"); // Non-empty id for update path
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateInfraReplicationMappingCommand
            {
                Type = ""
            };
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateInfraReplicationMappingCommand>(model)).Returns(updateCommand);
            _mockProvider.Setup(p => p.InfraReplicationMapping.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);

            // Verify that UpdateAsync was called
            _mockProvider.Verify(p => p.InfraReplicationMapping.UpdateAsync(updateCommand), Times.Once);

            // Verify that the mapper was called
            _mockMapper.Verify(m => m.Map<UpdateInfraReplicationMappingCommand>(model), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesAndVerifiesTempDataNotification()
        {
            // Arrange
            var model = new InfraReplicationMappingViewModel
            {
                Type = "SpecialReplicationType"
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "999"); // Non-empty id for update path
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateInfraReplicationMappingCommand
            {
                Type = "SpecialReplicationType"
            };
            var response = new BaseResponse { Success = true, Message = "Special update message" };

            _mockMapper.Setup(m => m.Map<UpdateInfraReplicationMappingCommand>(model)).Returns(updateCommand);
            _mockProvider.Setup(p => p.InfraReplicationMapping.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);

            // Verify that UpdateAsync was called
            _mockProvider.Verify(p => p.InfraReplicationMapping.UpdateAsync(updateCommand), Times.Once);

            // Verify that the mapper was called
            _mockMapper.Verify(m => m.Map<UpdateInfraReplicationMappingCommand>(model), Times.Once);
        }
    }
}
