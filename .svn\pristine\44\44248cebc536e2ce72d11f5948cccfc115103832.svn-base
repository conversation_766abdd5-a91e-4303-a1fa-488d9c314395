﻿using ContinuityPatrol.Application.Features.LicenseInfo.Events.Delete;

namespace ContinuityPatrol.Application.Features.LicenseInfo.Commands.Delete;

public class DeleteLicenseInfoCommandHandler : IRequestHandler<DeleteLicenseInfoCommand, DeleteLicenseInfoResponse>
{
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly IPublisher _publisher;

    public DeleteLicenseInfoCommandHandler(ILicenseInfoRepository licenseInfoRepository, IPublisher publisher)
    {
        _licenseInfoRepository = licenseInfoRepository;
        _publisher = publisher;
    }

    public async Task<DeleteLicenseInfoResponse> Handle(DeleteLicenseInfoCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _licenseInfoRepository.GetByEntityId(request.EntityId);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.LicenseInfo),
            new NotFoundException(nameof(Domain.Entities.LicenseInfo), request.EntityId));

        eventToDelete.IsActive = false;

        await _licenseInfoRepository.UpdateAsync(eventToDelete);

        var response = new DeleteLicenseInfoResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.LicenseInfo), eventToDelete.EntityName),

            IsActive = eventToDelete.IsActive
        };
        await _publisher.Publish(new LicenseInfoDeletedEvent { EntityName = eventToDelete.EntityName },
            cancellationToken);

        return response;
    }
}