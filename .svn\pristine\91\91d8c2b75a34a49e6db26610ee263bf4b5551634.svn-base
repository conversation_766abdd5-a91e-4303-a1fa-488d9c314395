using ContinuityPatrol.Application.Features.BiaRules.Commands.Create;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Delete;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class BiaRulesFixture : IDisposable
{
    public List<BiaRules> BiaRules { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateBiaRulesCommand CreateBiaRulesCommand { get; set; }
    public UpdateBiaRulesCommand UpdateBiaRulesCommand { get; set; }
    public DeleteBiaRulesCommand DeleteBiaRulesCommand { get; set; }
    public IMapper Mapper { get; set; }

    public BiaRulesFixture()
    {
        // Initialize with manual data first
        BiaRules = new List<BiaRules>
        {
            new BiaRules
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Description = "Test BIA Rule",
                Type = "RTO",
                EntityId = Guid.NewGuid().ToString(),
                Properties = "{\"threshold\":\"4\",\"unit\":\"hours\"}",
                EffectiveDateFrom = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd"),
                EffectiveDateTo = DateTime.Now.AddDays(30).ToString("yyyy-MM-dd"),
                IsEffective = true,
                RuleCode = "BIA_RTO_001",
                IsActive = true
            }
        };

        // Create additional entities using AutoFixture
        try
        {
            var additionalRules = AutoBiaRulesFixture.CreateMany<BiaRules>(2).ToList();
            BiaRules.AddRange(additionalRules);

            UserActivities = AutoBiaRulesFixture.CreateMany<UserActivity>(3).ToList();
            CreateBiaRulesCommand = AutoBiaRulesFixture.Create<CreateBiaRulesCommand>();
            UpdateBiaRulesCommand = AutoBiaRulesFixture.Create<UpdateBiaRulesCommand>();
            DeleteBiaRulesCommand = AutoBiaRulesFixture.Create<DeleteBiaRulesCommand>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            UserActivities = new List<UserActivity>();
            CreateBiaRulesCommand = new CreateBiaRulesCommand();
            UpdateBiaRulesCommand = new UpdateBiaRulesCommand();
            DeleteBiaRulesCommand = new DeleteBiaRulesCommand();
        }

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<BiaRulesProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoBiaRulesFixture
    {
        get
        {
            var fixture = new Fixture();

            // Configure fixture to handle circular references
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            // String customizations
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateBiaRulesCommand>(p => p.Description, 100));
            fixture.Customize<CreateBiaRulesCommand>(c => c
                .With(b => b.Description, () => $"Test BIA Rule {fixture.Create<int>()}")
                .With(b => b.Type, "RTO")
                .With(b => b.EntityId, () => Guid.NewGuid().ToString())
                .With(b => b.Properties, "{\"threshold\":\"4\",\"unit\":\"hours\"}")
                .With(b => b.EffectiveDateFrom, DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd"))
                .With(b => b.EffectiveDateTo, DateTime.Now.AddDays(30).ToString("yyyy-MM-dd"))
                .With(b => b.IsEffective, true)
                .With(b => b.RuleCode, () => $"BIA_RTO_{fixture.Create<int>():000}"));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateBiaRulesCommand>(p => p.Description, 100));
            fixture.Customize<UpdateBiaRulesCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString())
                .With(b => b.Description, () => $"Updated BIA Rule {fixture.Create<int>()}")
                .With(b => b.Type, "RPO")
                .With(b => b.EntityId, () => Guid.NewGuid().ToString())
                .With(b => b.Properties, "{\"threshold\":\"2\",\"unit\":\"hours\"}")
                .With(b => b.EffectiveDateFrom, DateTime.Now.AddDays(-15).ToString("yyyy-MM-dd"))
                .With(b => b.EffectiveDateTo, DateTime.Now.AddDays(45).ToString("yyyy-MM-dd"))
                .With(b => b.IsEffective, true)
                .With(b => b.RuleCode, () => $"BIA_RPO_{fixture.Create<int>():000}"));

            fixture.Customize<DeleteBiaRulesCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString()));

            fixture.Customize<BiaRules>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                .With(b => b.Description, () => $"Test BIA Rule {fixture.Create<int>()}")
                .With(b => b.Type, "RTO")
                .With(b => b.EntityId, () => Guid.NewGuid().ToString())
                .With(b => b.Properties, "{\"threshold\":\"4\",\"unit\":\"hours\"}")
                .With(b => b.EffectiveDateFrom, DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd"))
                .With(b => b.EffectiveDateTo, DateTime.Now.AddDays(30).ToString("yyyy-MM-dd"))
                .With(b => b.IsEffective, true)
                .With(b => b.RuleCode, () => $"BIA_RTO_{fixture.Create<int>():000}"));

            // Add UserActivity customization
            fixture.Customize<UserActivity>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.UserId, () => Guid.NewGuid().ToString())
                .With(a => a.LoginName, () => $"TestUser{fixture.Create<int>()}")
                .With(a => a.Entity, "BiaRules")
                .With(a => a.Action, "Create")
                .With(a => a.ActivityType, "Create")
                .With(a => a.ActivityDetails, () => $"Test activity {fixture.Create<int>()}")
                .With(a => a.RequestUrl, "/api/test")
                .With(a => a.HostAddress, "127.0.0.1")
                .With(a => a.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
