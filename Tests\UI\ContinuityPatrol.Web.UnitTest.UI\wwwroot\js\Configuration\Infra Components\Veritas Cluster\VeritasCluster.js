console.log("Veritas Cluster Loaded");
const veritasClusterURL = {
    veritasClusterExistUrl: "Configuration/VeritasCluster/IsVeritasClusterNameExist",
    veritasClusterPaginatedUrl: "/Configuration/VeritasCluster/GetPaginated",
    veritasClusterSaveUrl: "Configuration/VeritasCluster/CreateOrUpdate",
    veritasClusterDeleteUrl: "Configuration/VeritasCluster/Delete"
}
let veritasClusters = '';
let selectedValues = [];

let permission = {
    create: $("#veritasConfigCreate").data("create-permission")?.toLowerCase(),
    delete: $("#veritasConfigDelete").data("delete-permission")?.toLowerCase()
};
    

    if(permission.create == 'false') $("#veritasCreateBtn").addClass('btn-disabled').css('pointer-events', 'none');


    let dataTable = $('#veritasClusterTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": veritasClusterURL.veritasClusterPaginatedUrl,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "clusterProfileName" : sortIndex === 2 ? "clusterServerName" : sortIndex === 3 ? "clusterName" :
                        sortIndex === 4 ? "clusterBinPath" : sortIndex === 5 ? "status" : "";
                    let orderValue = d?.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                    d.pageSize = d?.length;
                    d.searchString = selectedValues?.length === 0 ? $('#veritasSearchInp')?.val() : selectedValues?.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.data?.totalPages;
                    json.recordsFiltered = json?.data?.totalCount;
                    if (json?.success && Array.isArray(json?.data?.data) && json?.data?.data?.length) {
                        $(".pagination-column").removeClass("disabled");
                        return json?.data?.data;
                    }
                    else {
                        $(".pagination-column").addClass("disabled");
                        if (json?.message) {
                            $('.dataTables_empty').text('No Data Found');
                            notificationAlert('warning', json?.message);
                        }
                        return [];
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [1, 2, 3, 4],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, orderable: false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta?.row + 1;
                        }
                        return data;
                    },
                },
                {
                    "data": "clusterProfileName", "name": "Cluster Profile Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<td><span title="${row?.clusterProfileName || 'NA'}" > ${row?.clusterProfileName || "NA"}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "clusterServerName", "name": "Cluster Server", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {

                            return `<td><span title="${row?.clusterServerName || 'NA'}" > ${row?.clusterServerName || "NA"}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "clusterName", "name": "Cluster Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<td><span title="${row?.clusterName || 'NA'}" > ${row?.clusterName || "NA"}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "clusterBinPath", "name": "Cluster Bin Path", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<td><span title="${row?.clusterBinPath || 'NA'}" > ${row?.clusterBinPath || "NA"}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        return `<div class="d-flex align-items-center gap-2"> <span role="button" title="Edit" class="${permission.create == 'true' ? 'editbutton' : 'icon-disabled'}" ${permission.create == 'true' ? `data-cluster='${btoa(JSON.stringify(row || 'NA'))}'` : ''}><i class="cp-edit"></i></span>
                         <span role="button" title="Delete" class="${permission.delete == 'true' ? 'deletebutton' : 'icon-disabled'}" ${permission.delete == 'true' ? `data-cluster-id="${row?.id || 'NA'}" data-cluster-name="${row?.clusterProfileName || 'NA'}"` : ''}><i class="cp-Delete"></i></span></div>`;
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api?.context[0]?._iDisplayStart;
                let counter = startIndex + index + 1;
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        }
    );

    $('#veritasSearchInp').on('keydown input', function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        handleSearchDebounced();
    });

    const handleSearchDebounced = commonDebounce(function () {

        const checkboxes = [$("#profilename"), $("#clusterserver"), $("#clustername")];
        const inputValue = $('#veritasSearchInp').val();

        checkboxes.forEach(checkbox => {
            if (checkbox.is(':checked')) {
                selectedValues.push(checkbox?.val() + inputValue);
            }
        });
        let currentPage = dataTable?.page?.info()?.page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {
                if ($('#veritasSearchInp').val().length === 0) {
                    if (json?.data?.data?.length === 0) {
                        $('.dataTables_empty').text('No Data Found');
                    }
                }
                else if (json?.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }
    }, 500);

    // Get dropdwon server names
    async function GetserverNames(veritasClusters) {
        await $.ajax({
            type: "GET",
            url: RootUrl + 'Configuration/VeritasCluster/GetServerList',
            data: {},
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result && result?.data && result?.data?.length) {
                    $('#clusterServerName').append('<option value=""></option>');

                    let options = '';
                    result?.data?.forEach(s => {
                        options += `<option value="${s?.id}" clusterServerNames="${s?.name}">${s?.name}</option>`;
                    });

                    $('#clusterServerName').append(options);

                    if (veritasClusters) {
                        const existingOption = $(`#clusterServerName option[value="${veritasClusters?.clusterServerId}"]`);
                        if (existingOption?.length) {
                            existingOption.prop('selected', true);
                        } else {
                            $('#clusterServerName').append($(`<option value="${veritasClusters?.clusterServerId}" clusterServerNames="${veritasClusters.clusterServerName}" selected>${veritasClusters.clusterServerName}</option>`));
                        }
                    }
                } else {
                    errorNotification(result);
                }
            },
        });
    }

// Populate Feild Elements
function populateVeritasFields(veritasClusters) {
    if (veritasClusters) {
        GetserverNames(veritasClusters)
        $('#clusterProfileName').val(veritasClusters?.clusterProfileName).attr('clusterProfileNameId', veritasClusters?.id);
        $('#clusterName').val(veritasClusters?.clusterName).attr('clusterId', veritasClusters?.id);
        $('#clusterBinPath').val(veritasClusters?.clusterBinPath);
        $('#clusterServerName').val(veritasClusters?.clusterServerId).attr('clusterServerNames', veritasClusters?.clusterServerName);
        $('#clusterProfileNameError,#clusterServerNameError,#clusterNameError,#clusterBinPathError').text('').removeClass('field-validation-error')
    }
}

async function validatePath(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        await InvalidPathRegex(value)
    ];
    return await CommonValidation(errorElement, validationResults);
}

async function clusterNameValidations(value, errorElement, validationMsg, includeNameExistCheck = false, id = null) {
    if (!value) {
        errorElement.text(validationMsg).addClass('field-validation-error');
        return false;
    } else if (value.includes('<')) {
        errorElement.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }
    else {
        const validationResults = [SpecialCharValidate(value), ShouldNotBeginWithSpace(value), ShouldNotBeginWithUnderScore(value),
        OnlyNumericsValidate(value), ShouldNotBeginWithNumber(value), ShouldNotEndWithSpace(value),
        ShouldNotAllowMultipleSpace(value), SpaceWithUnderScore(value), ShouldNotEndWithUnderScore(value),
        MultiUnderScoreRegex(value), SpaceAndUnderScoreRegex(value), minMaxlength(value), secondChar(value)
        ];
        if (includeNameExistCheck) {
            const url = RootUrl + veritasClusterURL.veritasClusterExistUrl;
            const data = { name: value, id };
            validationResults.push(await IsNameExist(url, data, OnError));
        }
        return await CommonValidation(errorElement, validationResults);
    }
}

async function IsNameExist(url, data, errorFunc) {
    return !data.name.trim() ? true : (await getAysncWithHandler(url, data, errorFunc)) ? "Name already exists" : true;
}

function validateDropDown(value, errorMessage, errorElement) {
    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}

// Edit icon on click
$('#veritasClusterTable').on('click', '.editbutton', function () {
    veritasClusters = JSON.parse(atob($(this).data("cluster")));
    if (veritasClusters) {
        populateVeritasFields(veritasClusters);
        $('#veritasSaveBtn').text("Update");
        $('#veritasCreateModal').modal('show');
    }
});

// Clear data before save
$('#veritasCreateBtn').on('click', function () {
    veritasClusters = ''
    $('#clusterProfileName').val("").attr('clusterProfileNameId', "");
    $('#clusterName').val("").attr('clusterId', "");
    $('#clusterBinPath').val("");
    $('#clusterServerName').val("").attr('clusterServerNames', "");
    $('#clusterProfileNameError,#clusterServerNameError,#clusterNameError,#clusterBinPathError').text('').removeClass('field-validation-error')
    GetserverNames();
    $('#veritasCreateModal').modal('show');
});

// Create or Update 
$("#veritasSaveBtn").on('click', async function () {
    let clusterProfile = $("#clusterProfileName").val();
    let clusterServer = $('#clusterServerName option:selected').attr('clusterServerNames');
    let clusterServerId = $("#clusterServerName").val();
    let clusterProfileId = $("#clusterProfileName").attr('clusterProfileNameId');
    let clusterName = $("#clusterName").val();
    let clusterBinPath = $("#clusterBinPath").val();
    let isclusterServer = await validateDropDown(clusterServer, 'Select cluster server', 'clusterServerNameError');
    let isclusterProfile = await clusterNameValidations(clusterProfile, $('#clusterProfileNameError'), "Enter cluster profile name", true, clusterProfileId);
    let isclusterName = await clusterNameValidations(clusterName, $('#clusterNameError'), 'Enter cluster name ', false, null);
    let isclusterBinPath = await validatePath(clusterBinPath, ' Enter cluster bin path', $('#clusterBinPathError'))

    if (isclusterServer && isclusterProfile && isclusterBinPath && isclusterName) {

        sanitizeContainer(['clusterProfileName', 'clusterProfileNameId', 'clusterServerNames', 'clusterServerNameId', 'clusterName', 'clusterBinPath']);

        let savedata = { Id: $("#clusterName").attr('clusterId'), ClusterProfileName: clusterProfile, ClusterServerId: clusterServerId, ClusterServerName: clusterServer, ClusterName: clusterName, ClusterBinPath: clusterBinPath, __RequestVerificationToken: gettoken() };

        await $.ajax({
            url: RootUrl + veritasClusterURL.veritasClusterSaveUrl,
            type: "POST",
            dataType: "json",
            data: savedata,
            success: function (response) {
                if (response && response?.success && response?.data?.message) {
                    notificationAlert("success", response?.data?.message);
                    setTimeout(() => {
                        $('#veritasCreateModal').modal('hide');
                        dataTable.ajax.reload()
                    }, 1000);
                } else {
                    errorNotification(response);
                }
            }
        });
    }
});

// Delete Icon on click
$('#veritasClusterTable').on('click', '.deletebutton', function () {
    const ClusternId = $(this).data("cluster-id");
    const ClusterName = $(this).data("cluster-name")
    $("#deleteData").attr("title", ClusterName).val(ClusternId).text(ClusterName);
    $('#veritasDeleteModal').modal('show');
});

// Confirm delete on click
$("#confirmDeleteButton").on('click', async function () {
    const deleteid = $('#deleteData').val();
    if (deleteid) {
        await $.ajax({
            url: RootUrl + veritasClusterURL.veritasClusterDeleteUrl,
            type: "DELETE",
            dataType: "json",
            data: { id: deleteid, __RequestVerificationToken: gettoken() },
            success: function (response) {
                if (response && response?.success && response?.data?.message) {
                    notificationAlert("success", response?.data?.message);
                    setTimeout(() => {
                        $('#veritasDeleteModal').modal('hide');
                        dataTable.ajax.reload();
                    }, 1000);
                } else {
                    errorNotification(response);
                    $('#veritasDeleteModal').modal('hide');
                }
            },
        });
    }
});

// Common Validations Profile, Server and CLuster names
$('#clusterProfileName').on('keyup', commonDebounce(async function () {
    let clusterProfileId = $('#clusterProfileName').attr('clusterProfileNameId');
    const value = $(this).val();
    let name = await sanitizeInput($(this).val());
    $(this).val(name);
    await clusterNameValidations(name, $('#clusterProfileNameError'), "Enter cluster profile name", true, clusterProfileId);
}, 400));

$('#clusterServerName').on('change', function () {
    const value = $(this).val();
    if (value) {
        validateDropDown(value, 'Select cluster server name ', 'clusterServerNameError');
    }
});

$('#clusterName').on('input', function () {
    let sanitizedValue = $(this).val()?.replace(/^\s+/, '').replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    clusterNameValidations(sanitizedValue, $('#clusterNameError'), 'Enter cluster name ', false, null);
});

$('#clusterBinPath').on('input', async function () {
    let sanitizedValue = $(this).val()?.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    let errorElement = $('#clusterBinPathError');
    await validatePath(sanitizedValue, 'Enter cluster bin path ', errorElement)
})