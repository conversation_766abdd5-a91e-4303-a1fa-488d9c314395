
$(function () {   

    let arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20];

    let tableData = arr.map((curr, index) => {
        return (`
            <tr>
   <td class="d-flex justify-content-between">
      <span><label class="customcheckbox ms-3"><input type="checkbox" id="mainCheckbox${index + 1}"><span class="checkmark"></span></label></span>
      <span>
         <span class="me-4">
           <i class="cp-success"></i>
         </span>
      </span>
   </td>
   <td >MSSQL_Mirror_DR_DB_2</td>
   <td >PR_Database</td>
   <td >Mirror_sql</td>
   <td >2020</td>
   <td class="col text-truncate bg-danger-subtle text-danger">IBM DB2</td>
   <td >IBM DB2</td>
   <td >IBM DB2</td>
   <td >IBM DB2</td>
   <td >IBM DB2</td>
   <td >IBM DB2</td>
   </tr>`
        )
    }).join(' ');

    document.getElementById("tablebody").innerHTML = tableData;



    $('#search_in_type').on('change', function () {
        var selected_templateType = $('#search_in_type').val();
        if (selected_templateType == "Server") {
            $('#div_excelDataDatabase').hide();
            $('#div_excelData').show();
        } else if (selected_templateType == "Database") {
            $('#div_excelDataDatabase').show();
            $('#div_excelData').hide();
        }
    });

    $('#file-input').on('change', function (e) {
        var selected_templateType = $('#search_in_type').val();
        //  $('#progress').hide();
        $('#uploadedFileName').text('');

        if ($("#file-input").val() == null) {
            return;
        }

        var fdata = new FormData();

        var fileInput = $('#file-input')[0];

        var fileinfo = fileInput.value.split('.');

        var ext = fileinfo[fileinfo.length - 1];

        if (ext == "xlsx" || ext == "xls") {
            var file = fileInput.files[0];
            fdata.append("uploadedFile", file);

            if (selected_templateType == "Server") {

                $.ajax({
                    type: 'POST',
                    url: "/Configuration/BulkImport/UploadFile",
                    data: fdata,
                    async: false,
                    processData: false,
                    contentType: false,
                    success: function (data) {

                        BindExcelData_ToDatatable(data);

                        $('#uploadedFileName').text(fileInput.value.split('\\')[fileInput.value.split('\\').length - 1]);


                    },
                    xhr: function () {
                        var fileXhr = new window.XMLHttpRequest();
                        if (fileXhr.upload) {

                            fileXhr.upload.addEventListener("progress", function (e) {
                                if (e.lengthComputable) {
                                    var percentage = Math.ceil(((e.loaded / e.total) * 100));
                                    $('div.progress-bar').text(percentage + '%');
                                    $('div.progress-bar').width(percentage + '%');
                                    if (percentage == 100) {
                                        $('div.progress-bar').text('100%');
                                    }
                                }
                            }, false);
                        }
                        return fileXhr;
                    },

                    failure: function (result) {
                        alert('File Upload Failed' + result.responseText);
                    },
                    error: function (result) {
                        alert('File Upload Failed' + result.responseText);
                    }
                });

            } else if (selected_templateType == "Database") {

                $.ajax({
                    type: 'POST',
                    url: "/Configuration/BulkImport/UploadFileDatabase",
                    data: fdata,
                    async: false,
                    processData: false,
                    contentType: false,
                    success: function (data) {

                        BindExcelData_ToDatatable_ForDatabase(data);

                        $('#uploadedFileName').text(fileInput.value.split('\\')[fileInput.value.split('\\').length - 1]);


                    },
                    xhr: function () {
                        var fileXhr = new window.XMLHttpRequest();
                        if (fileXhr.upload) {

                            fileXhr.upload.addEventListener("progress", function (e) {
                                if (e.lengthComputable) {
                                    var percentage = Math.ceil(((e.loaded / e.total) * 100));
                                    $('div.progress-bar').text(percentage + '%');
                                    $('div.progress-bar').width(percentage + '%');
                                    if (percentage == 100) {
                                        $('div.progress-bar').text('100%');
                                    }
                                }
                            }, false);
                        }
                        return fileXhr;
                    },

                    failure: function (result) {
                        alert('File Upload Failed' + result.responseText);
                    },
                    error: function (result) {
                        alert('File Upload Failed' + result.responseText);
                    }
                });

            }

        } else {
            alert('Please Select file with \'xlsx\' or \'xls\' extention.');
        }
    });

    $('#btn_downloadTemplate').on('click', function () {
        var selected_templateType = $('#search_in_type').val();
        if (selected_templateType == "Server") {
            $.ajax({
                type: 'POST',
                url: '/Configuration/BulkImport/download_Template',
                data: { templateType: selected_templateType },
                success: function (data) {
                    //Construct download link
                    var downloadLink = document.createElement("a");
                    downloadLink.href = "/Configuration/BulkImport/DownloadFile?fileName=" + data.fileName + ".xlsx";
                    // Trigger download
                    downloadLink.click();

                },
                error: function (error) {
                    console.error('File download failed:', error);
                }
            });
        } else if (selected_templateType == "Database") {
            $.ajax({
                type: 'POST',
                url: '/Configuration/BulkImport/download_TemplateDatabase',
                data: { templateType: selected_templateType },
                success: function (data) {
                    //Construct download link
                    var downloadLink = document.createElement("a");
                    downloadLink.href = "/Configuration/BulkImport/DownloadFile?fileName=" + data.fileName + ".xlsx";
                    // Trigger download
                    downloadLink.click();

                },
                error: function (error) {
                    console.error('File download failed:', error);
                }
            });
        }

    });


    $('#btn_SaveAll').hide();
    $('#btn_OpenUploadResultModal').hide();
    $('#btn_OpenUploadResultModal_Database').hide();


    Set_Event_For_SaveAll_btn();
});



function BindExcelData_ToDatatable(data) {

    //var fdata = new FormData();

    //var fileInput = $('#file-input')[0];
    //var file = fileInput.files[0];
    //fdata.append("uploadedFile", file);
    //var selectedValues = [];

    if (data == undefined || data == null) {
        alert("Data Not Found.");
        $('#btn_SaveAll').hide();
        $('#btn_OpenUploadResultModal').hide();
        $('#btn_OpenUploadResultModal_Database').hide();
        return;
    }

    var dataTable = $('#tbl_excelData').DataTable(
        {
            "bDestroy": true,
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: false,
            deferRender: true,
            scroller: false,
            Sortable: true,
            processing: true,
            serverSide: false,
            filter: true,
            data: data,

            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",

                    "render": function (data, type, row, meta) {

                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        else if (type === 'sort' || type === 'type') {
                            return parseInt(data, 10);
                        }
                        return data;
                    }
                },
                {
                    //data: "name",
                    //name: 'Name',

                    //"render": function (data, type, row) {
                    //    if (type === 'display') {
                    //        return '<span title="' + data + '">' + data + '</span>';
                    //    }
                    //    return data;
                    //}

                    "render": function (data, type, row) {

                        return `<input type="text" class="form-control border border-info small input-text" value="${row.name}" />`;
                    },
                    "orderable": false


                },
                {
                    "data": "siteName", "name": "Site Name",

                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span id="' + row.siteId + '" title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "businessServiceName", name: "Business Service Name",
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span id="' + row.businessServiceId + '" title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },

                {
                    "data": "roleType", name: "Role Type",
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "serverType", name: "Server Type",
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "licenseKey", name: "License Key",
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span  class="short-string" id="' + row.companyId + '" title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "osType", name: "OS Type",
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span id="' + row.osTypeId + '" title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "version", name: "Version",
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {

                        return `<input type="text" readonly id="txt_${row.name}" class="form-control border border-secondary small" placeholder="{...}" />`;
                    },
                    "orderable": false
                },
                {
                    "render": function (data, type, row) {

                        return `<button id="${row.name}" type="button" onclick="Generate_Properti_JSON(this);" value="${row.osTypeId}|${row.version}|${row.osType}" class="btn btn-sm btn-info">Add Properties</button>`;
                    },
                    "orderable": false
                },
            ],


        });


    //$(document).on('keyup', '#search-inp', function () {
    //    console.log("Keyup event triggered!");
    //    $('input[type="checkbox"]').each(function () {
    //        if ($(this).is(':checked')) {
    //            var checkboxValue = $(this).val();
    //            var inputValue = $('#search-inp').val();
    //            selectedValues.push(checkboxValue + inputValue);
    //        }
    //    });
    //    dataTable.search($(this).val()).draw();
    //});

    $('#btn_SaveAll').show();
}


function BindExcelData_ToDatatable_ForDatabase(data) {

    if (data == undefined || data == null) {
        alert("Data Not Found.");
        $('#btn_SaveAll').hide();
        $('#btn_OpenUploadResultModal').hide();
        $('#btn_OpenUploadResultModal_Database').hide();
        return;
    }

    var dataTable2 = $('#tbl_excelDataDatabase').DataTable(
        {
            "bDestroy": true,
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: false,
            deferRender: true,
            scroller: false,
            Sortable: true,
            processing: true,
            serverSide: false,
            filter: true,
            data: data,

            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",

                    "render": function (data, type, row, meta) {

                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        else if (type === 'sort' || type === 'type') {
                            return parseInt(data, 10);
                        }
                        return data;
                    }
                },
                {

                    "render": function (data, type, row) {

                        return `<input type="text" class="form-control border border-info small input-text" value="${row.name}" />`;
                    },
                    "orderable": false


                },

                {
                    "data": "businessServiceName", name: "Business Service Name",
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span id="' + row.businessServiceId + '" title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "serverName", name: "Server Name",
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span id="' + row.serverId + '" title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },

                {
                    "data": "", name: "Database Type",
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span  id="' + row.databaseType.split("|")[1] + '" title="' + row.databaseType.split("|")[0] + '">' + row.databaseType.split("|")[0] + '</span>';
                        }
                        return row.databaseType.split("|")[1];
                    }
                },
                {
                    "data": "licenseKey", name: "License Key",
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span  class="short-string" id="' + row.companyId + '" title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {

                        return `<input type="text" readonly id="txtdb_${row.name}" class="form-control border border-secondary small" placeholder="{...}" />`;
                    },
                    "orderable": false
                },
                {
                    "render": function (data, type, row) {

                        return `<button id="${row.name}" type="button" onclick="GenerateDatabase_Properti_JSON(this);" value="${row.databaseType.split("|")[1]}" class="btn btn-sm btn-info">Add Properties</button>`;
                    },
                    "orderable": false
                },
            ],


        });

    $('#btn_SaveAll').show();
}

function Set_Event_For_SaveAll_btn() {

    $('#btn_SaveAll').on('click', function () {


        var selected_templateType = $('#search_in_type').val();
        if (selected_templateType == "Server") {

            $('#btn_OpenUploadResultModal').click();


            processArrayWithDelay();

        } else if (selected_templateType == "Database") {
            $('#btn_OpenUploadResultModal_Database').click();

            processArrayWithDelay_Database();
        }
    });


}

//================================================Start Server Save Records=========================================================
//=====                                                                                                                      ====//
function processArrayWithDelay() {

    var rowData_SuccessServerNames = [];
    var rowData_DuplicateServerNames = [];
    var rowData_blankPropertyValue = [];
    var rowData_licenseKeyIssues = [];

    var total_Rows = $('#tbl_excelData tbody tr');

    var total_Row_Count = 0;
    var success_Row_Count = 0;
    var failed_Row_Count = 0;

    if (total_Rows != null && total_Rows.length > 0) {

        $('#lbl_uploadingstatus').text('Saving Records....');
        $('#lbl_uploadingstatus').attr('style', 'color:orange;');

        total_Row_Count = total_Rows.length;

        // Iterate through each row in the table
        var counter = 1;


        var i = 0;
        function processNext() {
            if (i < total_Rows.length) {


                var rowData = {};

                // Read values from each column in the current row
                rowData.Name = $(total_Rows[i]).find('td:eq(1)').find('input').val();
                rowData.SiteId = $(total_Rows[i]).find('td:eq(2)').find('span').attr('Id');
                rowData.SiteName = $(total_Rows[i]).find('td:eq(2)').text();
                rowData.BusinessServiceId = $(total_Rows[i]).find('td:eq(3)').find('span').attr('Id');
                rowData.BusinessServiceName = $(total_Rows[i]).find('td:eq(3)').text();
                rowData.RoleType = $(total_Rows[i]).find('td:eq(4)').text();
                rowData.ServerType = $(total_Rows[i]).find('td:eq(5)').text();
                rowData.CompanyId = $(total_Rows[i]).find('td:eq(6)').find('span').attr('Id');
                rowData.LicenseKey = $(total_Rows[i]).find('td:eq(6)').text();
                rowData.OSType = $(total_Rows[i]).find('td:eq(7)').text();
                rowData.OSTypeId = $(total_Rows[i]).find('td:eq(7)').find('span').attr('Id');
                rowData.Version = $(total_Rows[i]).find('td:eq(8)').text();

                // Read values from input fields and span elements
                rowData.Properties = $(total_Rows[i]).find('td:eq(9)').find('input').val();

                var continue_for_Save = true;

                if (rowData.Name == undefined || rowData.Name == null || rowData.Name == "") {
                    continue_for_Save = false;
                }

                if (rowData.Properties == undefined || rowData.Properties == null || rowData.Properties == "") {
                    rowData_blankPropertyValue.push(rowData.Name);
                    continue_for_Save = false;
                }

                var CanSaveServerName = validateName(rowData.Name);
                if (CanSaveServerName == false) {
                    rowData_DuplicateServerNames.push(rowData.Name);
                    continue_for_Save = false;
                }

                if (continue_for_Save == true) {

                    $.ajax({
                        url: '/Configuration/BulkImport/Save_ServerRecord',
                        method: 'POST',
                        async: false,
                        contentType: 'application/json',
                        processData: false,
                        //   contentType: false,
                        data: JSON.stringify(rowData),
                        success: function (response) {
                            // Handle success response
                            if (response.success == true) {
                                success_Row_Count++;
                                rowData_SuccessServerNames.push(rowData.Name);
                                // Remove the successfully saved rows from the table
                                $(total_Rows[i]).remove();
                            } else if (response.success == false && response.message.indexOf('The license key has expired') !== -1) {
                                failed_Row_Count++;
                                rowData_licenseKeyIssues.push(rowData.Name + ' - The license key has expired.');
                            } else if (response.success == false && (response.message.toLowerCase().includes('licenseKey') || response.message.toLowerCase().includes('license Key') || response.message.toLowerCase().includes('license'))) {
                                failed_Row_Count++;
                                rowData_licenseKeyIssues.push(rowData.Name + ' - Check License Key, it\'s Not Found or Not Authorized.');
                            } else {
                                failed_Row_Count++;
                            }

                        },
                        error: function (error) {
                            // Handle error response
                            failed_Row_Count++;
                            console.error(error);
                        }
                    });
                } else {
                    failed_Row_Count++;
                }




                var _progress = parseInt(((parseInt(counter) / parseInt(total_Row_Count)) * 100)).toString();
                counter++;

                $('#btn_OpenUploadResultModal').show();

                $('div.progress-bar').width(_progress + '%');
                $('div.progress-bar').text(_progress);

                if (_progress == 100) {

                    $('#lbl_uploadingstatus').text('Completed');
                    $('#lbl_uploadingstatus').attr('style', 'color:green;');
                }


                // alert("list of failed servers : " + JSON.stringify(rowData_DuplicateServerNames) + " | " + JSON.stringify(rowData_blankPropertyValue) + " | " + JSON.stringify(rowData_licenseKeyIssues));

                $('#lbl_totalRecords').text(total_Row_Count);
                $('#lbl_successRecords').text(success_Row_Count);
                $('#lbl_failedRecords').text(failed_Row_Count);



                var duplicate_Servers = '';
                $.each(rowData_DuplicateServerNames, function (index, value) {
                    duplicate_Servers = duplicate_Servers + '<span>' + value + '</span><br>';
                });

                var propertyFieldBlank = '';
                $.each(rowData_blankPropertyValue, function (index, value) {
                    propertyFieldBlank = propertyFieldBlank + '<span>' + value + '</span><br>';
                });

                var LicenseKeyIssue = '';
                $.each(rowData_licenseKeyIssues, function (index, value) {
                    LicenseKeyIssue = LicenseKeyIssue + '<span>' + value + '</span><br>';
                });

                var SuccessServerNames = '';
                $.each(rowData_SuccessServerNames, function (index, value) {
                    SuccessServerNames = SuccessServerNames + '<span>' + value + '</span><br>';
                });

                $('#div_SuccessServers').empty();
                $('#div_duplicateServers').empty();
                $('#div_propertyFieldBlank').empty();
                $('#div_LicenseKeyIssue').empty();

                $('#div_SuccessServers').append(SuccessServerNames);
                $('#div_duplicateServers').append(duplicate_Servers);
                $('#div_propertyFieldBlank').append(propertyFieldBlank);
                $('#div_LicenseKeyIssue').append(LicenseKeyIssue);

                i++;
                setTimeout(processNext, 2000); // Call processNext after 2 seconds
            }


        }

    } else {
        alert('First Upload an Excel File With Valid Data.')
    }











    processNext(); // Start processing the array
}

const nameExistURL = "Configuration/BulkImport/IsServerNameExist";

function validateName(val) {
    let sanitizedValue = val.replace(/\s{2,}/g, ' ');
    //  $('#ServerName').val(sanitizedValue);
    return nameValidation(sanitizedValue, null, nameExistURL);
};

function Get_Async(url, data, errorFunc) {
    //var retval = $.get(url, data).done(function (responce) {
    //    return responce;
    //}).fail(errorFunc);
    //return retval;


    var retval = false;
    $.ajax({
        url: url,
        type: 'GET',
        data: data,
        async: false, // Make the request synchronous
        success: function (response) {
            // Handle success
            console.log('Success:', response);

            // Check the response and take appropriate actions
            if (response === true) {
                console.log('Record saved successfully');
                retval = true;
            } else {
                console.log('Failed to save record');
                retval = false;
            }
        },
        error: function (error) {
            // Handle error
            console.error('Error:', error);
            retval = false;
        }
    });

    return retval;
}

function nameValidation(value, id = null, nameExistURL) {
    const errorElement = $("#Name-error");

    if (!value) {
        //    errorElement.text("Enter server name").addClass('field-validation-error');
        return false;
    }

    const url = RootUrl + nameExistURL;
    let data = {
        id: id,
        serverName: value
    };

    const validationResults = [
        SpecialCharValidateCustom(value), //SpecialCharValidate(value),
        ShouldNotBeginWithUnderScore(value),
        ShouldNotBeginWithSpace(value),
        OnlyNumericsValidate(value),
        ShouldNotBeginWithNumber(value),
        SpaceWithUnderScore(value),
        ShouldNotEndWithUnderScore(value),
        ShouldNotEndWithSpace(value),
        MultiUnderScoreRegex(value),
        SpaceAndUnderScoreRegex(value),
        minMaxlength(value),
        secondChar(value),
        IsNameExist(url, data, OnError)
    ];
    return CommonValidation(errorElement, validationResults);
};

function IsNameExist(url, data, errorFunc) {
    return !data.serverName?.trim() ? true : (Get_Async(url, data, errorFunc)) ? "Name already exists" : true;
}
//=====                                                                                                                     ====//
//================================================End Server Save Records==========================================================





//************************************************Start Database Save Records**********************************************************
//*****                                                                                                                          *****//

const DatabasenameExistURL = "Configuration/Database/IsDatabaseNameExist";

function processArrayWithDelay_Database() {

    var rowData_SuccessDatabaseNames = [];
    var rowData_DuplicateDatabaseNames = [];
    var rowData_blankPropertyValue2 = [];
    var rowData_licenseKeyIssues2 = [];

    var total_Rows2 = $('#tbl_excelDataDatabase tbody tr');

    var total_Row_Count2 = 0;
    var success_Row_Count2 = 0;
    var failed_Row_Count2 = 0;

    if (total_Rows2 != null && total_Rows2.length > 0) {

        $('#lbl_uploadingstatusDatabase').text('Saving Records....');
        $('#lbl_uploadingstatusDatabase').attr('style', 'color:orange;');

        total_Row_Count2 = total_Rows2.length;

        // Iterate through each row in the table
        var counter2 = 1;


        var i = 0;
        function processNext() {
            if (i < total_Rows2.length) {


                var rowData = {};

                // Read values from each column in the current row
                rowData.Name = $(total_Rows2[i]).find('td:eq(1)').find('input').val();
                rowData.BusinessServiceId = $(total_Rows2[i]).find('td:eq(2)').find('span').attr('Id');
                rowData.BusinessServiceName = $(total_Rows2[i]).find('td:eq(2)').text();
                rowData.ServerId = $(total_Rows2[i]).find('td:eq(3)').find('span').attr('Id');
                rowData.ServerName = $(total_Rows2[i]).find('td:eq(3)').text();
                rowData.DatabaseType = $(total_Rows2[i]).find('td:eq(4)').text();
                rowData.LicenseKey = $(total_Rows2[i]).find('td:eq(5)').text();

                // Read values from input fields and span elements
                rowData.Properties = $(total_Rows2[i]).find('td:eq(6)').find('input').val();

                var continue_for_Save2 = true;

                if (rowData.Name == undefined || rowData.Name == null || rowData.Name == "") {
                    continue_for_Save2 = false;
                }

                if (rowData.Properties == undefined || rowData.Properties == null || rowData.Properties == "") {
                    rowData_blankPropertyValue2.push(rowData.Name);
                    continue_for_Save2 = false;
                }


                var CanSaveDatabaseName = validateDatabaseName(rowData.Name); //validateName();
                if (CanSaveDatabaseName == false) {
                    rowData_DuplicateDatabaseNames.push(rowData.Name);
                    continue_for_Save2 = false;
                }

                if (continue_for_Save2 == true) {

                    $.ajax({
                        url: '/Configuration/BulkImport/Save_DatabaseRecord',
                        method: 'POST',
                        async: false,
                        contentType: 'application/json',
                        processData: false,
                        //   contentType: false,
                        data: JSON.stringify(rowData),
                        success: function (response) {
                            // Handle success response
                            if (response.success == true) {
                                success_Row_Count2++;
                                rowData_SuccessDatabaseNames.push(rowData.Name);
                                // Remove the successfully saved rows from the table
                                $(total_Rows2[i]).remove();
                            } else if (response.success == false && response.message.indexOf('The license key has expired') !== -1) {
                                failed_Row_Count2++;
                                rowData_licenseKeyIssues2.push(rowData.Name + ' - The license key has expired.');
                            } else if (response.success == false && (response.message.toLowerCase().includes('licenseKey') || response.message.toLowerCase().includes('license Key') || response.message.toLowerCase().includes('license'))) {
                                failed_Row_Count2++;
                                rowData_licenseKeyIssues2.push(rowData.Name + ' - Check License Key, it\'s Not Found or Not Authorized.');
                            } else {
                                failed_Row_Count2++;
                            }

                        },
                        error: function (error) {
                            // Handle error response
                            failed_Row_Count2++;
                            console.error(error);
                        }
                    });
                } else {
                    failed_Row_Count2++;
                }




                var _progress2 = parseInt(((parseInt(counter2) / parseInt(total_Row_Count2)) * 100)).toString();
                counter2++;

                $('#btn_OpenUploadResultModal_Database').show();

                $('div.progress-bar').width(_progress2 + '%');
                $('div.progress-bar').text(_progress2);

                if (_progress2 == 100) {

                    $('#lbl_uploadingstatusDatabase').text('Completed');
                    $('#lbl_uploadingstatusDatabase').attr('style', 'color:green;');
                }


                // alert("list of failed servers : " + JSON.stringify(rowData_DuplicateServerNames) + " | " + JSON.stringify(rowData_blankPropertyValue) + " | " + JSON.stringify(rowData_licenseKeyIssues));

                $('#lbl_totalRecordsDatabase').text(total_Row_Count2);
                $('#lbl_successRecordsDatabase').text(success_Row_Count2);
                $('#lbl_failedRecordsDatabase').text(failed_Row_Count2);



                var duplicate_Databases = '';
                $.each(rowData_DuplicateDatabaseNames, function (index, value) {
                    duplicate_Databases = duplicate_Databases + '<span>' + value + '</span><br>';
                });

                var propertyFieldBlank2 = '';
                $.each(rowData_blankPropertyValue2, function (index, value) {
                    propertyFieldBlank2 = propertyFieldBlank2 + '<span>' + value + '</span><br>';
                });

                var LicenseKeyIssue2 = '';
                $.each(rowData_licenseKeyIssues2, function (index, value) {
                    LicenseKeyIssue2 = LicenseKeyIssue2 + '<span>' + value + '</span><br>';
                });

                var SuccessDatabaseNames = '';
                $.each(rowData_SuccessDatabaseNames, function (index, value) {
                    SuccessDatabaseNames = SuccessDatabaseNames + '<span>' + value + '</span><br>';
                });

                $('#div_SuccessDatabase').empty();
                $('#div_duplicateDatabase').empty();
                $('#div_propertyFieldBlankDatabase').empty();
                $('#div_LicenseKeyIssueDatabase').empty();

                $('#div_SuccessDatabase').append(SuccessDatabaseNames);
                $('#div_duplicateDatabase').append(duplicate_Databases);
                $('#div_propertyFieldBlankDatabase').append(propertyFieldBlank2);
                $('#div_LicenseKeyIssueDatabase').append(LicenseKeyIssue2);

                i++;
                setTimeout(processNext, 2000); // Call processNext after 2 seconds
            }


        }

    } else {
        alert('First Upload an Excel File With Valid Data.')
    }


    processNext(); // Start processing the array
}

function validateDatabaseName(val) {
    let sanitizedValue = val.replace(/\s{2,}/g, ' ');
    //  $('#ServerName').val(sanitizedValue);
    return databaseNameValidation(sanitizedValue, null, nameExistURL);
};

async function databaseNameValidation(value, id = null, nameExistURL) {
    const errorElement = $("#nameError");
    if (!value) {
        //  errorElement.text("Enter database name").addClass('field-validation-error');
        return false;
    }
    const url = RootUrl + nameExistURL;
    let data = {
        id: id,
        databaseName: value
    };
    const validationResults = [
        await SpecialCharValidateCustom(value), //SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsNameExist(url, data, OnError)
    ];
    return await CommonValidation(errorElement, validationResults);
}


//*****                                                                                                                         *****//
//************************************************Ends Database Save Records***********************************************************












//************************************** Add Properties Details For Server ***********************************************/
//*****                                                                                                             *****//
var getserverlists = "Configuration/Server/ListTwo";
var SavePath = "Configuration/Server/CreateOrUpdate";
var serverList = "";
var osLists;
var osTypes;
var isEdit = false
var props;
var logovalue = "";
var thisVar;
//var createPermission = $("#configurationCreate").data("create-permission").toLowerCase();
//var deletePermission = $("#configurationDelete").data("delete-permission").toLowerCase();
let hasInputEmptyFields = false;
let hasSelectEmptyFields = false;
let hasInputRestrict = false;
let buttonClicked = false;

function handleAdd(id, newRow, event) {
    event.preventDefault();
    //debugger
    const table = document.querySelector(`#f-${id}`) ? document.querySelector(`#f-${id}`) : document.querySelector(`#${id}`);
    const clonedRow = newRow.cloneNode(true);

    // Clear input and select values in the cloned row
    const inputElements = clonedRow.querySelectorAll('input');
    const selectElements = clonedRow.querySelectorAll('select');

    inputElements.forEach(input => { input.value = ''; });

    selectElements.forEach(select => { select.value = ''; });

    // Check if the cloned row already has a delete button
    const hasDeleteButton = clonedRow.querySelector('.delete-button');

    if (!hasDeleteButton) {
        const lastTd = clonedRow.querySelector('td:last-child');
        lastTd.innerHTML += '<span role="button" title="Delete" class="delete-button"><i onclick="handleDelete(event)" class="cp-Delete"></i></span>';
    }

    // Append the cloned row to the table
    table.appendChild(clonedRow);

    //'<span role="button" title="Delete" class="delete-button" ><i onclick="handleDelete(event)" class="cp-Delete"></i></span>'
    //table.appendChild(newRow.cloneNode(true));
}

function handleDelete(event) {
    event.preventDefault(); // Prevent default form submission behavior

    // Remove the closest row (tr) when the delete button is clicked
    $(event.target).closest('tr').remove();
}

function populateDynamicFields(data) {

    var formData = JSON.parse(data);
    $('#formRenderingArea .formeo-render .f-field-group').each(async function (index, element) {
        let fieldName = $(element).find('input, select, textarea, table').attr('name');
        let fieldVal = $(element).find('input, select, textarea').attr('value');
        let fieldType = $(element).find('input, select, textarea').attr('type');

        if (fieldName && formData.hasOwnProperty(fieldName) || fieldVal) {

            let value = formData[fieldName];
            let checkbox = $(element).find('input[type="checkbox"]').attr("value")
            let chkValue = formData[checkbox]

            //if (value || chkValue) {
            if (fieldType == 'radio') {
                $(element).find('input[type="radio"]').map((index, radio) => {
                    let radioValue = $(radio).val();
                    if (radioValue === formData[radioValue]) {
                        $(radio).prop('checked', true);
                    }
                });
            }

            if (typeof value === "boolean") {
                $(element).find('input[type="checkbox"]').prop('checked', chkValue);
                $(element).find('input[type="checkbox"]').trigger("change")
            }
            else if (fieldVal) {
                $(element).find('input[type="checkbox"]').prop('checked', chkValue).trigger("change");
            }
            else if (typeof value === "object") {
                if (value) {
                    /*const selectElement = document.getElementById('f-0d95bdab-911d-4521-91aa-56a0b40fe093');*/
                    const valuesArray = Object.values(value);
                    $(element).find('input, select, textarea').val(valuesArray).change();
                }
            }
            else {
                setTimeout(() => {
                    $(element).find('input, select, textarea').val(value).change();
                    $(element).find('input, select, textarea').trigger("change");

                    if (fieldName.toLocaleLowerCase()?.trim() === 'ipaddress' || fieldName.toLocaleLowerCase()?.trim() === 'hostname') {

                        var ipAddressInput = document.querySelector('input[name="IpAddress"]');
                        if (ipAddressInput) {
                            // Set the readonly attribute
                            ipAddressInput.setAttribute("readonly", "readonly");
                        }

                        var hostName = document.querySelector('input[name="HostName"]');
                        if (hostName) {
                            // Set the readonly attribute
                            hostName.setAttribute("readonly", "readonly");
                        }
                    }

                    if (fieldName === "@@singlesignon_name") {
                        setTimeout(() => {
                            $("#f-new-select-id8rhdgry0").val(formData["@@signonprofile"]);
                            $("#f-new-select-id8rhdgry0").trigger("change");
                        }, 200)
                    }
                }, 400);
            }
            //}
        }
        else if (fieldName == 'custom_table' && formData.hasOwnProperty('ConfigureSubstituteAuthentication')) {

            if (formData.ConfigureSubstituteAuthentication.length) {
                let $tableId = $(element).find('table').attr('id');
                let $table = document.querySelector(`#${$tableId}`)

                var $headerRow = $('<tr class=".header_row"></tr>');
                $headerRow.append('<th>Authenticate Type</th>');
                $headerRow.append('<th>Path</th>');
                $headerRow.append('<th>User</th>');
                $headerRow.append('<th>Password</th>');
                $headerRow.append('<th>Action</th>');

                $table.append($headerRow[0]);

                // Create a data row
                for (let i = 0; i < formData.ConfigureSubstituteAuthentication.length; i++) {
                    var $dataRow = $('<tr></tr>');
                    let res = '';

                    $dataRow.append(`<td><select class="form-select-modal" placeholder="Select Auth Type" name="SubstituteAuthenticationType">
                        <option value="" selected>Select Type</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'sudo su' ? 'selected' : ''}>sudo su</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'su' ? 'selected' : ''}>su</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'asu' ? 'selected' : ''}>asu</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'sudo' ? 'selected' : ''}>sudo</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'privrun' ? 'selected' : ''}>privrun</option>
                        <option ${formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationType === 'other' ? 'selected' : ''}>other</option>
                    </select></td>`);

                    if (formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationPassword !== '') {
                        await $.ajax({
                            type: "POST",
                            url: RootUrl + 'Configuration/Server/ServerDataDecrypt',
                            data: { data: formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationPassword },
                            dataType: 'text',
                            success: function (decryptedValue) {
                                //console.log(decryptedValue);
                                res = decryptedValue;
                            }
                        });
                    }

                    $dataRow.append('<td><input placeholder="Enter Path" name="SubstituteAuthenticationPath" value="' + formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationPath + '" type="text"/></td>');
                    $dataRow.append('<td><input placeholder="Enter User" name="SubstituteAuthenticationUser" value="' + formData.ConfigureSubstituteAuthentication[i].SubstituteAuthenticationUser + '" type="text" /> </td>');
                    $dataRow.append('<td><input placeholder="Enter Password" name="SubstituteAuthenticationPassword" value="' + res + '" type="password" /></td>');

                    // Add action buttons to the data row
                    var $actionCell = $('<td style="height:47px" class="d-flex align-items-center  gap-2"> </td>');

                    $actionCell.append(`<span role="button" title="Edit" onclick="event.preventDefault(); handleAdd('${$tableId}', this.parentElement.parentElement, event)">
                            <i class="cp-add"></i>
                       </span>`);

                    i !== 0 && $actionCell.append(`<span role="button" title="Delete" class="delete-button">
                                       <i onclick="handleDelete(event)" class="cp-Delete"></i>
                                   </span>`);

                    $dataRow.append($actionCell);
                    $table.append($dataRow[0]);
                }
            }
        }
    });
}

function getOptionsFromServer(type) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + "Configuration/Database/GetDatabaseNames",
            method: 'GET',
            dataType: 'json',
            data: { type: type }, // Pass the type parameter to the server
            success: function (data) {
                const options = [];
                options.push({ value: '', text: 'Select Database' });
                $.each(data, function (index, item) {
                    options.push({ value: item.id, text: item.name });
                });
                resolve(options);
            },
            error: function (error) {
                console.error('Error fetching data from the API:', error);
                reject(error);
            }
        });
    });
}

function getServOptionsFromServer(type, serverType) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + "Configuration/Server/GetServerNames",
            method: 'GET',
            dataType: 'json',
            data: { 'roleType': type, 'serverType': serverType },
            success: function (data) {
                const options = [];
                options.push({ value: '', text: 'Select Database' });
                $.each(data, function (index, item) {
                    options.push({ value: item.id, text: item.name });
                });
                resolve(options);
            },
            error: function (error) {
                console.error('Error fetching data from the API:', error);
                reject(error);
            }
        });
    });
}

function getSSOOptionsFromServer() {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + "Configuration/SingleSignOn/GetSingleSignOnList",
            method: 'GET',
            dataType: 'json',
            success: function (data) {
                const options = [];
                options.push({ value: '', text: 'Select Single Sign-On' });
                const uniqueValues = new Set();
                $.each(data?.data, function (index, item) {
                    if (!uniqueValues.has(item?.signOnType?.toLowerCase())) {
                        options.push({ value: item.signOnType, text: item.signOnType });
                        uniqueValues.add(item?.signOnType.toLowerCase());
                    }
                });
                resolve(options);
            },
            error: function (error) {
                console.error('Error fetching data from the API:', error);
                reject(error);
            }
        });
    });
}

function getSSOProfileNameOptionsFromServer(value) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + "Configuration/SingleSignOn/GetSingleSignOnByType",
            method: 'POST',
            data: { type: value },
            dataType: 'json',
            success: function (data) {
                const options = [];
                options.push({ value: "", text: "Select Profile Name" });
                $.each(data.data, function (index, item) {
                    options.push({ value: item.id, text: item.profileName });
                });
                resolve(options);
            },
            error: function (error) {
                console.error('Error fetching data from the API:', error);
                reject(error);
            }
        });
    });
}

function getRepOptionsFromServer() {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + "Configuration/Replication/GetReplicationNames",
            method: 'GET',
            dataType: 'json',
            success: function (data) {
                const options = [];
                options.push({ value: '', text: 'Select Replication' });
                $.each(data, function (index, item) {
                    options.push({ value: item.id, text: item.name });
                });
                resolve(options);
            },
            error: function (error) {
                console.error('Error fetching data from the API:', error);
                reject(error);
            }
        });
    });
};

function getNodesFromServer() {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + "Configuration/Node/GetNodeNames",
            method: 'GET',
            dataType: 'json',
            success: function (data) {
                const options = [];
                options.push({ value: '', text: 'Select Nodes ' });
                $.each(data, function (index, item) {
                    options.push({ value: item.id, text: item.name });
                });
                resolve(options);
            },
            error: function (error) {
                console.error('Error fetching data from the API:', error);
                reject(error);
            }
        });
    });
}

function getWorkflowsActionsFromServer(id) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + "Orchestration/WorkflowList/GetWorkflowActionNamesbyId",
            method: 'GET',
            dataType: 'json',
            data: { workflowId: id },
            success: function (data) {
                const options = [];
                let Properties = JSON.parse(data.properties)
                let propLength = Properties.nodes.length
                for (let i = 0; i < propLength; i++) {
                    if (Properties.nodes[i].hasOwnProperty('children')) {
                        Properties.nodes[i].children.forEach(function (obj) {
                            let Obj = { 'id': obj.actionInfo.nodeId, value: obj.actionInfo.actionName }
                            options.push(Obj)
                        })
                    }
                    else if (!Properties.nodes[i].hasOwnProperty('groupName')) {
                        let obj = { 'id': Properties.nodes[i].actionInfo.nodeId, value: Properties.nodes[i].actionInfo.actionName }
                        options.push(obj)
                    }
                }
                //console.log(options)
                //options.push({ value: '', text: 'Select Actions' });
                //$.each(data, function (index, item) {
                //    options.push({ id: item.id, text: item.actionName });
                //});
                resolve(options);
            },
            error: function (error) {
                console.error('Error fetching data from the API:', error);
                reject(error);
            }
        });
    });
}

function getWorkflowsFromServer(isAction, id) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + "Orchestration/WorkflowList/GetWorkflowNames",
            method: 'GET',
            dataType: 'json',
            success: function (data) {
                const options = [];
                options.push({ value: '', text: 'Select workflow' });
                $.each(data, function (index, item) {
                    options.push({ value: item.id, text: item.name });
                });
                resolve(options);
            },
            error: function (error) {
                console.error('Error fetching data from the API:', error);
                reject(error);
            }
        });
    });
}

function getServerLists() {
    $.ajax({
        type: "GET",
        url: RootUrl + getserverlists,
        dataType: "json",
        success: function (response) {
            serverList = response;
            var distinctNames = [...new Set(response.map(obj => obj.osType))];
            for (let i = 0; i < distinctNames.length; i++) {
                $(".search-in-type").append("<option value=" + distinctNames[i] + ">" + distinctNames[i] + "</option>");
            }
        },
        error: function (response) {
            console.log("error", response);
        }
    });
}

function populatethedynamicfields(fields) {
    for (const key in fields) {
        if (fields.hasOwnProperty(key)) {
            const field = fields[key];
            const { id, meta, config, attrs } = field;
            if (meta.id === "password-input") {
                //setTimeout(() => {
                //    const fieldElement = document.querySelector(`#f-${id}`);; // Assuming id is the ID of the password input field

                //    if (fieldElement) {
                //        const fieldGroup = fieldElement.closest('.f-field-group'); // Assuming the input field is within a container with class 'f-field-group'
                //        if (fieldGroup) {
                //            // Create an icon element (for Font Awesome, use <i> tag)
                //            const icon = document.createElement('span');
                //            icon.classList.add('cp-password-hide'); // Example classes for Font Awesome icon

                //            // Append the icon to the field container
                //            fieldGroup.appendChild(icon);
                //        }
                //    }
                //}, 1000)
            }
            if (meta.id === "database") {
                const type = attrs.DatabaseType === "All" ? "" : attrs.DatabaseType
                getOptionsFromServer(type).then(_options => {
                    const selectField = document.querySelector(`#f-${id}`);
                    if (selectField) {
                        //nextButtonStyle(' ', ' ');
                        _options.forEach(option => {
                            const optionElement = document.createElement("option");
                            optionElement.value = option.value;
                            optionElement.text = option.text;
                            selectField.appendChild(optionElement);
                        });
                    } else {
                        var error = { message: "Check the database condition field in the form builder module." }
                        errorNotification(error);
                        //nextButtonStyle('0.5', 'none');
                    }
                });
            }
            if (meta.id === "server") {
                const type = attrs.ServerRole
                const serverType = attrs.ServerType
                getServOptionsFromServer(type, serverType).then(_options => {
                    const selectField = document.querySelector(`#f-${id}`);
                    if (selectField) {
                        //nextButtonStyle(' ', ' ');
                        _options.forEach(option => {
                            const optionElement = document.createElement("option");
                            optionElement.value = option.value;
                            optionElement.text = option.text;
                            selectField.appendChild(optionElement);
                        });
                    } else {
                        var error = { message: "Check the server condition field in the form builder module." }
                        errorNotification(error);
                        //nextButtonStyle('0.5', 'none');
                    }
                });
            }
            if (meta.id === "replication") {
                getRepOptionsFromServer().then(_options => {
                    const selectField = document.querySelector(`#f-${id}`);
                    if (selectField) {
                        //nextButtonStyle(' ', ' ');
                        _options.forEach(option => {
                            const optionElement = document.createElement("option");
                            optionElement.value = option.value;
                            optionElement.text = option.text;
                            selectField.appendChild(optionElement);
                        });
                    } else {
                        var error = { message: "Check the replication condition field in the form builder module." }
                        errorNotification(error);
                        //nextButtonStyle('0.5', 'none');
                    }
                });
            }
            if (meta.id === "workflow") {
                var isAction = attrs.dependentAction
                getWorkflowsFromServer(isAction).then(_options => {
                    const selectField = document.querySelector(`#f-${id}`);

                    if (selectField) {
                        //nextButtonStyle(' ', ' ');
                        if (isAction) {
                            // Duplicate the select field
                            const duplicatedSelectField = selectField.parentNode.parentNode.parentNode.parentNode.cloneNode(true);
                            const labelElement = duplicatedSelectField.querySelector('label');
                            labelElement.textContent = 'Workflows Actions';
                            labelElement.setAttribute('for', 'f-new-select-id'); // Set a new ID for the label
                            // Update the select element with a new name and ID
                            const selectElement = duplicatedSelectField.querySelector('select');
                            selectElement.name = '@@workflow_actions';
                            selectElement.multiple = true
                            selectElement.id = 'f-new-select-id' + Math.random().toString(36).substring(7); // Set a new ID for the select element
                            selectField.parentNode.parentNode.parentNode.parentNode.appendChild(duplicatedSelectField);

                            _options.forEach(option => {
                                const optionElement = document.createElement("option");
                                optionElement.value = option.value;
                                optionElement.text = option.text;
                                selectField.appendChild(optionElement);
                            });

                            $(`#f-${id}`).on("change", function () {
                                selectElement.innerHTML = '';
                                getWorkflowsActionsFromServer(this.value).then(optins => {
                                    optins.forEach(option => {
                                        const optionElement = document.createElement("option");
                                        optionElement.value = option.id;
                                        optionElement.text = option.value;
                                        selectElement.appendChild(optionElement);
                                    });
                                })
                            })
                        }
                        else {
                            _options.forEach(option => {
                                const optionElement = document.createElement("option");
                                optionElement.value = option.value;
                                optionElement.text = option.text;
                                selectField.appendChild(optionElement);
                            });
                        }

                    } else {
                        var error = { message: "Check the workflow condition field in the form builder module." }
                        errorNotification(error);
                        //nextButtonStyle('0.5', 'none');
                    }
                });
            }
            if (meta.id === "singlesignon") {
                getSSOOptionsFromServer().then(_options => {
                    const selectField = document.querySelector(`#f-${id}`);
                    if (selectField) {
                        //
                        const profileField = selectField.parentNode.cloneNode(true);
                        const labelElement = profileField.querySelector('label');
                        labelElement.textContent = 'Profile';
                        const selectElement = profileField.querySelector('select');
                        selectElement.name = '@@signonprofile';
                        /*     selectElement.multiple = true*/
                        //selectElement.id = 'f-new-select-id' + Math.random().toString(36).substring(7);
                        selectElement.id = 'f-new-select-id8rhdgry0';
                        selectField.parentNode.parentNode.appendChild(profileField);

                        _options.forEach(option => {
                            const optionElement = document.createElement("option");
                            optionElement.value = option.value;
                            optionElement.text = option.text;
                            selectField.appendChild(optionElement);
                        });

                        $(`#f-${id}`).on("change", function () {
                            getSSOProfileNameOptionsFromServer(this.value).then(optins => {
                                selectElement.innerHTML = '';
                                optins.forEach(option => {
                                    const optionElement = document.createElement("option");
                                    optionElement.value = option.value;
                                    optionElement.text = option.text;
                                    selectElement.appendChild(optionElement);
                                });
                            })
                        });
                    } else {
                        var error = { message: "Check the single sign-on condition field in the form builder module." }
                        errorNotification(error);
                        //nextButtonStyle('0.5', 'none');
                    }
                });
            }
            if (meta.id === "nodes") {
                getNodesFromServer().then(_options => {
                    const selectNodeField = document.querySelector(`#f-${id}`);
                    if (selectNodeField) {
                        //
                        _options.forEach(option => {
                            const optionElement = document.createElement("option");
                            optionElement.value = option.value;
                            optionElement.text = option.text;
                            selectNodeField?.appendChild(optionElement);
                        });
                    } else {
                        var error = { message: "Check the nodes condition field in the form builder module." }
                        errorNotification(error);
                        //nextButtonStyle('0.5', 'none');
                    }
                });
            }
            if (meta.id === "table") {

                setTimeout(() => {
                    const $table = document.querySelector(`#f-${id}`);
                    //let tableField = $($table).find('.custom-table');

                    if ($table.children.length == 0) {

                        let $headerRow = $('<tr class=".header_row"></tr>');
                        $headerRow.append('<th>Authenticate Type</th>');
                        $headerRow.append('<th>Path</th>');
                        $headerRow.append('<th>User</th>');
                        $headerRow.append('<th>Password</th>');
                        $headerRow.append('<th>Action</th>');

                        // Append the header row to the table
                        $table.append($headerRow[0]);

                        // Create a data row
                        // Create a data row
                        // Create a data row
                        let $dataRow = $('<tr></tr>');
                        $dataRow.append('<td><select class="form-select-modal-dynamic" name="SubstituteAuthenticationType"><option value="" selected>Select Type</option><option value="sudo su">sudo su</option><option value="su">su</option><option value="asu">asu</option><option value="sudo">sudo</option><option value="privrun">privrun</option><option value="other">other</option></select></td>');
                        $dataRow.append('<td><input placeholder="Enter Path" type="text" name="SubstituteAuthenticationPath"/></td>');
                        $dataRow.append('<td><input placeholder="Enter User" type="text" name="SubstituteAuthenticationUser" /> </td>');
                        $dataRow.append('<td><input placeholder="Enter Password" type="password" name="SubstituteAuthenticationPassword" /></td>');


                        // Add action buttons to the data row
                        let $actionCell = $('<td style="height:47px" class="d-flex align-items-center  gap-2"> </td>');
                        $actionCell.append(`<span role="button" title="Edit"  onclick="event.preventDefault(); handleAdd('${id}', this.parentElement.parentElement, event)"> <i  class="cp-add "></i></span>`);
                        //$actionCell.append('<span role="button" title="Delete" class="delete-button" ><i onclick="handleDelete(event)" class="cp-Delete"></i></span>');

                        $dataRow.append($actionCell);
                        // Append the data row to the table
                        $table.append($dataRow[0]);
                    }
                }, 1000)
            }
        }
    }
}


var txt_field_Id;
function Generate_Properti_JSON(_this) {

    $('#formRenderingArea').empty();

    btn = _this;
    txt_field_Id = '#txt_' + btn.id;
    var combineVal = btn.value.split('|');

    let selectedValue = combineVal[1];
    let OS = combineVal[0];

    $('#lbl_serverName').text(btn.id + '  --  ' + combineVal[2] + ' | ' + combineVal[1]);

    //logovalue = $('#osType option:selected').attr('icon');
    //$("#serverLogo").val(logovalue);

    $.ajax({
        url: RootUrl + "Admin/FormMapping/GetFormMappingByFormId",
        method: 'GET',
        data: { "formTypeId": OS, "version": selectedValue },
        dataType: 'json',
        success: function (result) {
            if (result.success) {

                //Remove style for next button
                ////
                let data = result.data
                let propertyDiv = $('#formRenderingArea');
                propertyDiv.empty();
                let parsedJsonData = JSON.parse(data?.properties);
                populatethedynamicfields(parsedJsonData?.fields);
                var renderedForm = new FormeoRenderer({
                    renderContainer: document.querySelector("#formRenderingArea")
                });

                renderedForm.render(parsedJsonData);

                ///*  $('.form-select-modal-dynamic ').select2();*/
                //setTimeout(() => {
                //    this1.find('.form-select-modal-dynamic').select2({
                //        dropdownParent: this1.find('.modal-content'),
                //        placeholder: "Select"
                //    });
                //    $(".select2-container").css("width", "100%");

                //    //Restrict Special characters.
                //    let restrictRequiredInputs = $("input[restrict]");
                //    var pattern = /^[a-zA-Z0-9]+$/;
                //    restrictRequiredInputs.each(function () {
                //        let id = $(this).attr("id");
                //        document.getElementById(id).addEventListener("keyup", function () {
                //            var inputValue = this.value;
                //            if (inputValue) {
                //                if (!pattern.test(inputValue)) { //if entered special char it will become true
                //                    let $this = $(this);
                //                    $this.next(".dynamic-input-field").remove();
                //                    $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'>Restricted special characters.</span></div>");
                //                    hasInputEmptyFields = true;
                //                } else {
                //                    let $this = $(this);
                //                    $this.next(".dynamic-input-field").remove();
                //                    hasInputEmptyFields = false;
                //                }
                //            } else {
                //                let $this = $(this);
                //                $this.next(".dynamic-input-field").remove();
                //                hasInputEmptyFields = false;
                //            }
                //        });
                //    });
                //    //End Restrict Special characters.                         
                //}, 500);

                for (const key in parsedJsonData.fields) {
                    if (parsedJsonData.fields.hasOwnProperty(key)) {
                        const field = parsedJsonData.fields[key];
                        const { id, meta } = field;

                        if (meta.id === "number") {
                            const numberField = $(`#f-${id}`);
                            numberField.prop('min', '1');
                            numberField.attr('max', '99999');

                            numberField.on("input", function () {
                                const inputValue = $(this).val();

                                if (inputValue.length > 5) {
                                    $(this).val(inputValue.slice(0, 5));
                                }

                                const numericValue = parseInt($(this).val(), 10);

                                if (isNaN(numericValue) || numericValue < 1 || numericValue > 99999) {
                                    $(this).val('');
                                }
                            });
                        }
                    }
                }

                $('#formRenderingArea').on('focus', '.formeo-render .f-field-group input', function (event) {
                    let selectedId = event.target.id;
                    let type = event.target.type;
                    Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                        var field = parsedJsonData.fields[fieldId];

                        if (selectedId == `f-${fieldId}`) {

                            if (event.target.value !== '' && type == 'text' && field?.attrs?.encryption) {

                                $.ajax({
                                    type: "POST",
                                    url: RootUrl + 'Configuration/Server/ServerDataDecrypt',
                                    data: { data: event.target.value },
                                    dataType: 'text',
                                    success: function (decryptedValue) {
                                        event.target.value = decryptedValue;
                                    }
                                });
                            }
                        }
                    })
                });

                $('#formRenderingArea').on('blur', '.formeo-render .f-field-group input', function (event) {
                    let selectedId = event.target.id;
                    let type = event.target.type;

                    Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                        var field = parsedJsonData.fields[fieldId];

                        if (selectedId == `f-${fieldId}`) {
                            if (event.target.value !== '' && type == 'text' && field?.attrs?.encryption) {

                                $.ajax({
                                    type: "POST",
                                    url: RootUrl + 'Configuration/Server/ServerDataEncrypt',
                                    data: { data: event.target.value },
                                    dataType: 'text',
                                    success: function (encryptedValue) {
                                        event.target.value = encryptedValue;
                                    }
                                });
                            }
                        }
                    })
                });

                ///onsetconditionals
                $('#formRenderingArea').on('change', '.formeo-render .f-field-group input , .formeo-render .f-field-group select', function (event) {
                    var selectedValue = event.target.value;
                    var selectedid = event.target.id;
                    //let modifiedStr = selectedid.substring(2, selectedid.length - 2);
                    var typ = event.target.type;
                    var getId = selectedid.replace('f-', '');

                    if (typ === "radio" || typ === "checkbox") {
                        // Loop through all fields and their conditions id radio
                        var replacedId = getId.replace(/-0$/, '');

                        var field = parsedJsonData?.fields && parsedJsonData.fields[replacedId];

                        if (field?.conditions && field?.conditions.length > 0) {
                            var isVisible = false;
                            field.conditions.forEach(function (condition) {
                                var isMatchingCondition = condition.if.some(function (ifClause) {
                                    sourceField = parsedJsonData.fields[ifClause.source.substring(7)];
                                    return ifClause.target === selectedValue;
                                });

                                if (isMatchingCondition) {
                                    isVisible = true;
                                }
                            });
                            field.conditions.forEach(function (condition) {
                                condition.then.forEach(function (thenClause) {
                                    condition.if.forEach(function (ifClause) {

                                        var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                        var targetElementChk = document.getElementById(`f-${thenClause.target.substring(7)}-0`);
                                        var srcElement = document.getElementById(`f-${ifClause.source.substring(7)}`);

                                        let parentID = targetElement && targetElement.parentNode.parentNode.getAttribute('id')

                                        if (targetElement) {

                                            if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {

                                                if (isVisible) {
                                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                }
                                                if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                    if (targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) return false;
                                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add("d-none")
                                                }
                                            }
                                            else if (ifClause.comparison === "notEquals") {

                                                if (targetElement && event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                    if ($('#' + parentID).children().length > 1) {
                                                        targetElement.parentNode.classList.add('d-none')
                                                    } else {
                                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none')
                                                    }
                                                }
                                                else if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                    var isFound = Object.keys(parsedJsonData.fields).some(function (id) {
                                                        var field = parsedJsonData.fields[id];

                                                        if ((replacedId !== id) && field.conditions && field.conditions.length > 0) {
                                                            return field.conditions.some(function (condition) {
                                                                var isMatchingCondition = condition.then.some(function (ifClauses) {
                                                                    return ifClauses.target === thenClause.target;
                                                                });
                                                                return isMatchingCondition && condition.if.some(function (ifClauses) {
                                                                    return ifClauses.target === $('#' + `f-${id}`).val();
                                                                });
                                                            });
                                                        }
                                                        return false;
                                                    });

                                                    if (isFound) {
                                                        if ($('#' + parentID).children().length > 1) {
                                                            targetElement.parentNode.classList.remove('d-none')
                                                        } else {
                                                            targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        else if (targetElementChk) {

                                            if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {

                                                if (isVisible) {
                                                    targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                }
                                                if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                    if (targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) return false;
                                                    targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.add("d-none")
                                                }
                                            }
                                            else if (ifClause.comparison === "notEquals") {

                                                if (targetElementChk && event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                    targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.add('d-none')
                                                }
                                                else if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                                    targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                                                }
                                            }
                                        }
                                    });
                                });
                            });
                        }
                    };

                    if (typ === "select-one") {
                        //Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                        var field = parsedJsonData?.fields && parsedJsonData.fields[getId];
                        if (field?.conditions && field?.conditions.length > 0) {
                            var isMatchingCondition = field.conditions.some(function (condition) {
                                return condition.if.some(function (ifClause) {
                                    if (ifClause.source === `fields.${getId}` && ifClause.target === selectedValue) {
                                        return true;
                                    }
                                });
                            });

                            if (isMatchingCondition) {
                                field.conditions.forEach(function (condition) {
                                    condition.then.forEach(function (thenClause) {
                                        condition.if.forEach(function (ifClause) {

                                            var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                            let parentID = targetElement && targetElement.parentNode.parentNode.getAttribute('id')

                                            if (targetElement && thenClause.targetProperty === 'isVisible') {

                                                if (ifClause.target === selectedValue && thenClause.assignment === 'equals' && (targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none") || targetElement.parentNode.classList.contains("d-none"))) {
                                                    var isFound = Object.keys(parsedJsonData.fields).some(function (id) {
                                                        var field = parsedJsonData.fields[id];

                                                        if ((getId !== id) && field.conditions && field.conditions.length > 0) {
                                                            return field.conditions.some(function (condition) {
                                                                return condition.then.some(function (ifClauses) {
                                                                    return ifClauses.target === thenClause.target && $('#' + `f-${id}-0`).prop('checked');
                                                                });
                                                            });
                                                        }

                                                        return false;
                                                    });

                                                    if (!isFound) {
                                                        if ($('#' + parentID).children().length > 1) {
                                                            targetElement.parentNode.classList.remove('d-none')
                                                        }

                                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                                        correctElementId = targetElement.id
                                                    }

                                                } else if (ifClause.target !== selectedValue) {
                                                    targetElement.value = ""
                                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                }
                                            }
                                        });
                                    });
                                });
                            } else {
                                //Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                                var field = parsedJsonData?.fields && parsedJsonData.fields[getId];

                                if (field.conditions && field.conditions.length > 0) {
                                    field.conditions.forEach(function (condition) {
                                        condition.then.forEach(function (thenClause) {
                                            condition.if.forEach(function (ifClause) {
                                                var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                var sourceElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                                var sourceName = document.getElementsByName(`f-${ifClause.source.substring(7)}`);
                                                //if (targetElement === null) {
                                                //    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                //}

                                                if (targetElement && selectedValue !== ifClause.target && ifClause.comparison !== 'notEquals' && (selectedid === sourceElement || selectedid == sourceName)) {
                                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                }
                                            });
                                        });
                                    });
                                }
                                //});
                            }
                        }
                        //});
                    }
                });

                if (!isEdit) {
                    Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                        var field = parsedJsonData.fields[fieldId];

                        if (field.conditions && field.conditions.length > 0) {
                            field.conditions.forEach(function (condition) {
                                condition.if.forEach(function (ifClause) {
                                    condition.then.forEach(function (thenClause) {
                                        if ((thenClause.targetProperty === 'isVisible') && thenClause.assignment === 'equals' && ifClause.comparison !== "notEquals") {
                                            var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                            if (targetElement && !targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none") && !targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) {
                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                            }
                                        }
                                    });
                                });
                            });
                        }
                    });
                }
                if (isEdit && props) {
                    populateDynamicFields(props);
                }

                $('#btn_OpenAddPropertyModel').click();
            }
            else {
                errorNotification(result);
                ////nextButtonStyle('0.5', 'none');

            }
        },
    });




}


var txtdb_field_Id;
function GenerateDatabase_Properti_JSON(_this) {

    $('#databaseFormRenderingArea').empty();

    btn = _this;
    txtdb_field_Id = '#txtdb_' + btn.id;
    var combineVal = btn.value.split('|');

    let selectedValue = combineVal[1];
    let OS = combineVal[0];

    $('#lbl_serverName').text(btn.id + '  --  ' + combineVal[2] + ' | ' + combineVal[1]);

    $.ajax({
        url: RootUrl + "Admin/FormMapping/GetFormMappingByFormId",
        method: 'GET',
        data: { "formTypeId": OS, "version": selectedValue },
        dataType: 'json',
        success: function (result) {
            if (result.success) {

                let form = result.data
                $('#databaseFormRenderingArea').empty();
                let parsedJsonData = JSON.parse(form.properties)

                populatethedynamicfields(parsedJsonData.fields)
                var renderedForm = new FormeoRenderer({
                    renderContainer: document.querySelector("#databaseFormRenderingArea")
                });
                renderedForm.render(parsedJsonData);
                ////$('.form-select-modal-dynamic ').select2();
                //setTimeout(() => {
                //    this1.find('.form-select-modal-dynamic').select2({
                //        dropdownParent: this1.find('.modal-content'),
                //        //placeholder: "Select"
                //    });
                //    $(".select2-container").css("width", "100%");

                //    onChangeFormBuilderValidation();
                //}, 500);

                for (const key in parsedJsonData.fields) {
                    if (parsedJsonData.fields.hasOwnProperty(key)) {
                        const field = parsedJsonData.fields[key];
                        const { id, meta } = field;
                        if (meta.id === "number") {
                            const numberField = $(`#f-${id}`);
                            numberField.prop('min', '1');
                            numberField.attr('max', '999999');
                            numberField.on("input", function () {
                                const inputValue = $(this).val();
                                if (inputValue.length > 6) {
                                    $(this).val(inputValue.slice(0, 6));
                                }
                                const numericValue = parseInt($(this).val(), 10);
                                if (isNaN(numericValue) || numericValue < 1 || numericValue > 999999) {
                                    $(this).val('');
                                }
                            });
                        }
                    }
                }

                ///on`setconditionals
                $('#databaseFormRenderingArea').on('change', '.formeo-render .f-field-group input , .formeo-render .f-field-group select', function (event) {
                    formBuilderDBCondition(event, parsedJsonData);
                });

                //  initalstate;
                if (!isEdit) {
                    Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                        var field = parsedJsonData.fields[fieldId];
                        if (field.conditions && field.conditions.length > 0) {
                            field.conditions.forEach(function (condition) {
                                condition.if.forEach(function (ifClause) {
                                    condition.then.forEach(function (thenClause) {
                                        if (thenClause.targetProperty === 'isVisible' && thenClause.assignment === 'equals' && ifClause.comparison !== "notEquals") {
                                            var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                            if (targetElement && !targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none")) {
                                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                            }
                                        }
                                    });
                                });
                            });
                        }
                    });
                }
                if (isEdit && props) {
                    populateDatabaseDynamicFields(props);
                }

                $('#btn_OpenAdd_DATABASE_PropertyModel').click();
            }
            else {
                errorNotification(result);
                // nextButtonStyle('0.5', 'none');
            }
        },
    });


}


async function encryptPassword(PASS) {
    try {
        const response = await $.ajax({
            url: RootUrl + "Configuration/Server/HashPassword",
            method: 'GET',
            data: { Password: PASS },
            dataType: 'json'
        });
        return response.encrypt;
    } catch (error) {
        console.error('Error fetching data from the encryption API:', error);
        throw error;
    }
};

async function saveFormFields() {
    var formData = {};
    const formDataArray = [];
    var promises = [];
    $('#formRenderingArea .formeo-render .f-field-group').each(function (index, element) {
        var fieldName = $(element).find('input, select, textarea').attr('name');
        var fieldVal = $(element).find('input').attr('value');
        var fieldEncrypt = $(element).find('input').attr('encryption');
        var fieldType = $(element).find('input, select, textarea').attr('type');
        var value;
        const isTable = $(element).find('table');

        if (fieldName) {
            if (fieldType === 'checkbox') {
                value = $(element).find('input[type="checkbox"]').prop('checked');
            } else if (fieldType === 'radio') {
                value = $(element).find('input[type="radio"]:checked').val();
                formData[value] = value;
            } else {
                value = $(element).find('input, select, textarea').val();
            }

            if (fieldType === "checkbox") {
                formData[fieldVal] = value;
            }
            //if (fieldType == 'radio') {
            //    let id = $(element).find('input').attr('id')
            //    if ($('#' + id).prop('checked')) {
            //        formData[fieldVal] = fieldVal
            //    }
            //}
            if (fieldType === "password" && (value && value !== "") && value.length < 64) {
                promises.push(encryptPassword(value).then(encryptedPassword => {
                    formData[fieldName] = encryptedPassword;
                }));
            } else {
                formData[fieldName] = value;
            }
        }
        if (fieldName?.toLocaleLowerCase()?.includes('nodes')) {

            const selectedOptions = $(element).find('select').val();
            const idToLabelMapping = {};
            $(element)
                .find('select option')
                .each(function () {
                    idToLabelMapping[this.value] = $(this).text();
                });
            const selectedKeyValuePairs = selectedOptions.reduce((acc, curr) => {
                acc[curr] = idToLabelMapping[curr];
                return acc;
            }, {});
            formData[fieldName] = selectedKeyValuePairs;
        }
        if (isTable.length != 0) {
            formData["ConfigureSubstituteAuthentication"] = extractFormData($(element), promises)
        }
    });

    await Promise.all(promises)
    var formDataJson = JSON.stringify(formData);

    var hiddenInput = document.getElementById('Props');
    if (hiddenInput) {
        hiddenInput.value = formDataJson;
    }
    return formData;
}

async function saveFormFieldsDatabase() {
    var formData = {};
    const formDataArray = [];
    var promises = [];
    $('#databaseFormRenderingArea .formeo-render .f-field-group').each(function (index, element) {
        var fieldName = $(element).find('input, select, textarea').attr('name');
        var fieldVal = $(element).find('input').attr('value');
        var fieldEncrypt = $(element).find('input').attr('encryption');
        var fieldType = $(element).find('input, select, textarea').attr('type');
        var value;
        const isTable = $(element).find('table');

        if (fieldName) {
            if (fieldType === 'checkbox') {
                value = $(element).find('input[type="checkbox"]').prop('checked');
            } else if (fieldType === 'radio') {
                value = $(element).find('input[type="radio"]:checked').val();
                formData[value] = value;
            } else {
                value = $(element).find('input, select, textarea').val();
            }

            if (fieldType === "checkbox") {
                formData[fieldVal] = value;
            }
            //if (fieldType == 'radio') {
            //    let id = $(element).find('input').attr('id')
            //    if ($('#' + id).prop('checked')) {
            //        formData[fieldVal] = fieldVal
            //    }
            //}
            if (fieldType === "password" && (value && value !== "") && value.length < 64) {
                promises.push(encryptPassword(value).then(encryptedPassword => {
                    formData[fieldName] = encryptedPassword;
                }));
            } else {
                formData[fieldName] = value;
            }
        }
        if (fieldName?.toLocaleLowerCase()?.includes('nodes')) {

            const selectedOptions = $(element).find('select').val();
            const idToLabelMapping = {};
            $(element)
                .find('select option')
                .each(function () {
                    idToLabelMapping[this.value] = $(this).text();
                });
            const selectedKeyValuePairs = selectedOptions.reduce((acc, curr) => {
                acc[curr] = idToLabelMapping[curr];
                return acc;
            }, {});
            formData[fieldName] = selectedKeyValuePairs;
        }
        if (isTable.length != 0) {
            formData["ConfigureSubstituteAuthentication"] = extractFormData($(element), promises)
        }
    });

    await Promise.all(promises)
    var formDataJson = JSON.stringify(formData);

    var hiddenInput = document.getElementById('Props');
    if (hiddenInput) {
        hiddenInput.value = formDataJson;
    }
    return formData;
}

function extractFormData(element, promises) {
    let formDataArray = [];

    // Extracting data from each row (excluding header row)
    const rows = $(element).find('tr:not(.header_row)');
    const headerRow = $(element).find('th');

    rows.each(async function () {
        const rowDataObj = {};
        const columns = $(this).find('td');
        columns.each(function (colIndex) {
            const header = $(this).find('input, select').attr('name') || $(this).text();
            const value = ($(this).find('select') ? $(this).find('select').find('option:selected').val() ? $(this).find('select').find('option:selected').val() :
                $(this).find('input').val() : '') || '';

            if (header && header?.trim() !== '') {

                if (header == "SubstituteAuthenticationPassword") {

                    if (value !== '') {
                        promises.push(encryptPassword(value).then(encryptedPassword => {
                            rowDataObj[header] = encryptedPassword;
                        }));
                    } else {
                        rowDataObj[header] = value;
                    }
                } else {
                    rowDataObj[header] = value;
                }
            }

        });

        if (Object.keys(rowDataObj).length !== 0) {
            formDataArray.push(rowDataObj);
        }
    });

    formDataArray = formDataArray.filter(s => s.SubstituteAuthenticationType != '')
    return formDataArray;
}


$("#Create_Json").on("click", async function () {
    let hasInputEmptyFields = false;
    let hasSelectEmptyFields = false;

    var txt_Field = $(txt_field_Id);
    txt_Field.val('');

    // if (!buttonClicked) {

    let requiredInputs = $("input[required]");
    requiredInputs?.each(async function () {
        let $this = $(this);
        let associatedLabel = $('label[for="' + $this.attr('id') + '"]');
        let labelText = associatedLabel.text().replace(/\*/g, '');
        var toLowerCase = labelText.toLowerCase();
        let inputValue2 = $this?.val();
        $this.next(".dynamic-input-field").remove();

        if (inputValue2 === "" && $this.is(':visible')) {
            // Show error message for each empty required field
            $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> Enter " + toLowerCase + "</span></div>");
            hasInputEmptyFields = true
        }
    });

    let selectInput = $("select[required]");
    selectInput?.each(function () {
        let $this = $(this);
        let associatedLabel2 = $('label[for="' + $this.attr('id') + '"]');
        let labelText2 = associatedLabel2.text().replace(/\*/g, '');
        var toLowerCase2 = labelText2.toLowerCase();
        let inputValue2 = $this?.val();

        if (Array.isArray(inputValue2)) {
            if (inputValue2.length > 0) {
                inputValue2 = inputValue2[0];
            } else {
                inputValue2 = "";
            }
        } else if (typeof inputValue2 === 'string') {
            inputValue2 = $this?.val()?.trim();
            if (inputValue2 && inputValue2.toLowerCase().replace(/\*/g, '').includes('select')) inputValue2 = ""
        }
        $this.next(".dynamic-select-tag").remove();

        if ($this.is(':visible') && (inputValue2.length === 0 || inputValue2 === "")) {
            $this.after("<div class='mt-4 dynamic-select-tag field-validation-error'><span class='required-field-msg'> Select " + toLowerCase2 + "</span></div>");
            hasSelectEmptyFields = true;
        }
    });

    if (!hasInputEmptyFields && !hasSelectEmptyFields) {
        buttonClicked = true;
        let fd = await saveFormFields();
        fd["icon"] = logovalue;
        const keys = Object.keys(fd);
        keys.forEach(key => {
            if (key.startsWith('f-')) {
                delete fd[key];
            }
        });
        var formDataJson = JSON.stringify(fd);

        txt_Field.val(formDataJson);

        $("#formRenderingArea").empty();
        txt_field_Id = '';

        $('#btn_closeModelPopup').click();
    }

    // }
});


$("#Create_JsonDatabase").on("click", async function () {
    let hasInputEmptyFields = false;
    let hasSelectEmptyFields = false;

    var txtdb_Field2 = $(txtdb_field_Id);
    txtdb_Field2.val('');

    // if (!buttonClicked) {

    let requiredInputs = $("input[required]");
    requiredInputs?.each(async function () {
        let $this = $(this);
        let associatedLabel = $('label[for="' + $this.attr('id') + '"]');
        let labelText = associatedLabel.text().replace(/\*/g, '');
        var toLowerCase = labelText.toLowerCase();
        let inputValue2 = $this?.val();
        $this.next(".dynamic-input-field").remove();

        if (inputValue2 === "" && $this.is(':visible')) {
            // Show error message for each empty required field
            $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> Enter " + toLowerCase + "</span></div>");
            hasInputEmptyFields = true
        }
    });

    let selectInput = $("select[required]");
    selectInput?.each(function () {
        let $this = $(this);
        let associatedLabel2 = $('label[for="' + $this.attr('id') + '"]');
        let labelText2 = associatedLabel2.text().replace(/\*/g, '');
        var toLowerCase2 = labelText2.toLowerCase();
        let inputValue2 = $this?.val();

        if (Array.isArray(inputValue2)) {
            if (inputValue2.length > 0) {
                inputValue2 = inputValue2[0];
            } else {
                inputValue2 = "";
            }
        } else if (typeof inputValue2 === 'string') {
            inputValue2 = $this?.val()?.trim();
            if (inputValue2 && inputValue2.toLowerCase().replace(/\*/g, '').includes('select')) inputValue2 = ""
        }
        $this.next(".dynamic-select-tag").remove();

        if ($this.is(':visible') && (inputValue2.length === 0 || inputValue2 === "")) {
            $this.after("<div class='mt-4 dynamic-select-tag field-validation-error'><span class='required-field-msg'> Select " + toLowerCase2 + "</span></div>");
            hasSelectEmptyFields = true;
        }
    });

    if (!hasInputEmptyFields && !hasSelectEmptyFields) {
        buttonClicked = true;
        let fd = await saveFormFieldsDatabase();
        fd["icon"] = logovalue;
        const keys = Object.keys(fd);
        keys.forEach(key => {
            if (key.startsWith('f-')) {
                delete fd[key];
            }
        });
        var formDataJson = JSON.stringify(fd);

        txtdb_Field2.val(formDataJson);

        $("#databaseFormRenderingArea").empty();
        txtdb_field_Id = '';

        $('#btn_closeModelPopupDatabase').click();
    }

    // }
});

//*****                                                                                                                       *****//
//************************************** Add Properties Details For Server ENDs Here ***********************************************/

$("#SolutionType").on("change", function () {
    let templateType = $("#SolutionType option:selected").val();

    const errorElement = $('#SolutionType_Error');
    if (templateType == "" || templateType) {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
    }
})
$('#downloadBulk').on('click', function () {
    $("#SolutionType").val("").change();
});

$("#DownloadTemplate").on("click", async function () {

    let templateType = $("#SolutionType option:selected").val();
    $("#DownloadTemplate").addClass('disabled');
    const errorElement = $('#SolutionType_Error');
    if (!templateType) {
        errorElement.text("Select solution type");
        errorElement.addClass('field-validation-error');
        $("#DownloadTemplate").removeClass('disabled');
        return false;
    }
    else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
    }

    const url = `${RootUrl}Configuration/BulkImportInput/Download_Template?templateType=${encodeURIComponent(templateType)}`;

    const fetchResponse = await fetch(url, {
        method: 'POST',
        headers: {
            'RequestVerificationToken': gettoken(),
        },
    });
    if (fetchResponse.ok) {
        const response = await fetchResponse.blob();
        console.log("Response from server:", response);
        if (response.type == "application/vnd.ms-excel") {
            const DateTime = new Date().toLocaleString('en-US', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                fractionalSecondDigits: 3,
                hour12: false,
            }).replace(/[^0-9]/g, '');

            const formattedDateTime = DateTime.replace(/(\d{2})(\d{2})(\d{4})(\d{2})(\d{2})(\d{2})(\d{3})/, '$1$2$3_$4$5$6');
            downloadFileTemplate(response, templateType + "_BulkImportTemplate_" + formattedDateTime + ".xls", "application/xls");
            message = "Bulk Import Template " + templateType +" has been downloaded successfully";
            notificationAlert("success", message);
            $("#DownloadTemplate").removeClass('disabled');
        }
        else {
            var ErrorMessage = await response.text();
            message = "Bulk Import Template " + templateType + " has been downloaded failed!" + ErrorMessage;
            notificationAlert("error", message);
            $("#DownloadTemplate").removeClass('disabled');
        }
    } else {
        message = "Bulk Import Template " + templateType +" has been downloaded failed!";
        notificationAlert("error", message);
        $("#DownloadTemplate").removeClass('disabled');
    }
    $("#DownloadTemplate").removeClass('disabled');
    $("#SolutionType").val("").change();
})
function downloadFileTemplate(blob, fileName, contentType) {
    try {
        const link = document.createElement("a");
        link.download = fileName;
        link.href = window.URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error("Error downloading file: " + error.message);
    }
}

$('.modal').on('shown.bs.modal', function (e) {
    $(this).find('.form-select-modal').select2({
        dropdownParent: $(this).find('.modal-content')
    });
})

