﻿using AutoFixture;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Update;
using ContinuityPatrol.Application.Features.DataSyncOptions.Events.PaginatedView;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class DataSyncPropertiesControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<ILogger<DataSyncPropertiesController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private DataSyncPropertiesController _controller = null!;

        public DataSyncPropertiesControllerShould()
        {
            Initialize();
        }

        internal void Initialize()
        {
            _controller = new DataSyncPropertiesController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        // ===== LIST METHOD TESTS =====

        [Fact]
        public async Task List_ShouldReturnViewResult()
        {
            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task List_ShouldPublishDataSyncOptionsPaginatedEvent()
        {
            // Act
            await _controller.List();

            // Assert
            _mockPublisher.Verify(p => p.Publish(It.IsAny<DataSyncOptionsPaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        // ===== GETPAGINATED METHOD TESTS =====

        [Fact]
        public async Task GetPaginated_ShouldReturnJsonResult()
        {
            // Arrange
            var query = new Fixture().Create<GetDataSyncOptionsPaginatedListQuery>();
            var paginatedList = new PaginatedResult<DataSyncOptionsListVm>();
            _mockDataProvider.Setup(dp => dp.DataSync.GetPaginatedDataSyncs(query))
                             .ReturnsAsync(paginatedList);

            // Act
            var result = await _controller.GetPaginated(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task GetPaginated_ShouldCallDataProviderWithCorrectQuery()
        {
            // Arrange
            var query = new Fixture().Create<GetDataSyncOptionsPaginatedListQuery>();
            var paginatedList = new PaginatedResult<DataSyncOptionsListVm>();
            _mockDataProvider.Setup(dp => dp.DataSync.GetPaginatedDataSyncs(query))
                             .ReturnsAsync(paginatedList);

            // Act
            await _controller.GetPaginated(query);

            // Assert
            _mockDataProvider.Verify(dp => dp.DataSync.GetPaginatedDataSyncs(query), Times.Once);
        }

        [Fact]
        public async Task GetPaginated_ShouldReturnJsonExceptionOnError()
        {
            // Arrange
            var query = new Fixture().Create<GetDataSyncOptionsPaginatedListQuery>();
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.DataSync.GetPaginatedDataSyncs(query))
                             .ThrowsAsync(exception);

            // Act
            var result = await _controller.GetPaginated(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        // ===== CREATEORUPDATE METHOD TESTS =====

        [Fact]
        public async Task CreateOrUpdate_WithNewDataSync_ShouldReturnJsonWithSuccess()
        {
            // Arrange
            var viewModel = new Fixture().Create<DataSyncOptionsViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreateDataSyncOptionsCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateDataSyncOptionsCommand>(viewModel)).Returns(command);
            _mockDataProvider.Setup(dp => dp.DataSync.CreateAsync(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            Assert.NotNull(resultValue);
            var successProperty = resultValue.GetType().GetProperty("Success");
            Assert.NotNull(successProperty);
            Assert.Equal(true, successProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task CreateOrUpdate_WithExistingDataSync_ShouldReturnJsonWithSuccess()
        {
            // Arrange
            var viewModel = new Fixture().Create<DataSyncOptionsViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", "existing-id");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new UpdateDataSyncOptionsCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateDataSyncOptionsCommand>(viewModel)).Returns(command);
            _mockDataProvider.Setup(dp => dp.DataSync.UpdateAsync(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            Assert.NotNull(resultValue);
            var successProperty = resultValue.GetType().GetProperty("Success");
            Assert.NotNull(successProperty);
            Assert.Equal(true, successProperty.GetValue(resultValue));
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldReturnJsonExceptionOnError()
        {
            // Arrange
            var viewModel = new Fixture().Create<DataSyncOptionsViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var exception = new Exception("Test exception");
            _mockMapper.Setup(m => m.Map<CreateDataSyncOptionsCommand>(viewModel)).Throws(exception);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        // ===== DELETEASYNC METHOD TESTS =====

        [Fact]
        public async Task DeleteAsync_ShouldReturnRedirectToAction()
        {
            // Arrange
            var id = "test-id";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockDataProvider.Setup(dp => dp.DataSync.DeleteAsync(id)).ReturnsAsync(response);

            // Act
            var result = await _controller.DeleteAsync(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task DeleteAsync_ShouldCallDataProviderWithCorrectId()
        {
            // Arrange
            var id = "test-id";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockDataProvider.Setup(dp => dp.DataSync.DeleteAsync(id)).ReturnsAsync(response);

            // Act
            await _controller.DeleteAsync(id);

            // Assert
            _mockDataProvider.Verify(dp => dp.DataSync.DeleteAsync(id), Times.Once);
        }

        [Fact]
        public async Task DeleteAsync_ShouldSetTempDataSuccessMessage()
        {
            // Arrange
            var id = "test-id";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockDataProvider.Setup(dp => dp.DataSync.DeleteAsync(id)).ReturnsAsync(response);

            // Act
            await _controller.DeleteAsync(id);

            // Assert
            Assert.True(_controller.TempData.Values.Any(v => v.ToString().Contains("Deleted successfully")));
        }

        [Fact]
        public async Task DeleteAsync_ShouldReturnRedirectToActionOnException()
        {
            // Arrange
            var id = "test-id";
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.DataSync.DeleteAsync(id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.DeleteAsync(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task DeleteAsync_ShouldSetTempDataWarningMessageOnException()
        {
            // Arrange
            var id = "test-id";
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.DataSync.DeleteAsync(id)).ThrowsAsync(exception);

            // Act
            await _controller.DeleteAsync(id);

            // Assert
            Assert.True(_controller.TempData.Values.Any(v => v.ToString().Contains("Test exception")));
        }

        // ===== ISDATASYNCNAMEEXIST METHOD TESTS =====

        [Fact]
        public async Task IsDataSyncNameExist_ShouldReturnTrue_WhenNameExists()
        {
            // Arrange
            var name = "test-name";
            var id = "test-id";
            _mockDataProvider.Setup(dp => dp.DataSync.IsDataSyncNameExist(name, id)).ReturnsAsync(true);

            // Act
            var result = await _controller.IsDataSyncNameExist(name, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task IsDataSyncNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
        {
            // Arrange
            var name = "test-name";
            var id = "test-id";
            _mockDataProvider.Setup(dp => dp.DataSync.IsDataSyncNameExist(name, id)).ReturnsAsync(false);

            // Act
            var result = await _controller.IsDataSyncNameExist(name, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsDataSyncNameExist_ShouldCallDataProviderWithCorrectParameters()
        {
            // Arrange
            var name = "test-name";
            var id = "test-id";
            _mockDataProvider.Setup(dp => dp.DataSync.IsDataSyncNameExist(name, id)).ReturnsAsync(true);

            // Act
            await _controller.IsDataSyncNameExist(name, id);

            // Assert
            _mockDataProvider.Verify(dp => dp.DataSync.IsDataSyncNameExist(name, id), Times.Once);
        }

        [Fact]
        public async Task IsDataSyncNameExist_ShouldReturnFalseOnException()
        {
            // Arrange
            var name = "test-name";
            var id = "test-id";
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.DataSync.IsDataSyncNameExist(name, id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.IsDataSyncNameExist(name, id);

            // Assert
            Assert.False(result);
        }

        // ===== CONSTRUCTOR TESTS =====

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new DataSyncPropertiesController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );

            // Assert
            Assert.NotNull(controller);
        }

        [Fact]
        public void Constructor_ShouldAcceptNullPublisher_WithoutThrowingException()
        {
            // Act & Assert - Production code doesn't validate null, so it should not throw
            var exception = Record.Exception(() => new DataSyncPropertiesController(
                null!,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            ));
            Assert.Null(exception);
        }

        [Fact]
        public void Constructor_ShouldAcceptNullLogger_WithoutThrowingException()
        {
            // Act & Assert - Production code doesn't validate null, so it should not throw
            var exception = Record.Exception(() => new DataSyncPropertiesController(
                _mockPublisher.Object,
                null!,
                _mockMapper.Object,
                _mockDataProvider.Object
            ));
            Assert.Null(exception);
        }

        [Fact]
        public void Constructor_ShouldAcceptNullMapper_WithoutThrowingException()
        {
            // Act & Assert - Production code doesn't validate null, so it should not throw
            var exception = Record.Exception(() => new DataSyncPropertiesController(
                _mockPublisher.Object,
                _mockLogger.Object,
                null!,
                _mockDataProvider.Object
            ));
            Assert.Null(exception);
        }

        [Fact]
        public void Constructor_ShouldAcceptNullDataProvider_WithoutThrowingException()
        {
            // Act & Assert - Production code doesn't validate null, so it should not throw
            var exception = Record.Exception(() => new DataSyncPropertiesController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                null!
            ));
            Assert.Null(exception);
        }

        // ===== EDGE CASE TESTS =====

        [Fact]
        public async Task CreateOrUpdate_WithWhitespaceId_ShouldTreatAsCreate()
        {
            // Arrange
            var viewModel = new Fixture().Create<DataSyncOptionsViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", "   ");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreateDataSyncOptionsCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateDataSyncOptionsCommand>(viewModel)).Returns(command);
            _mockDataProvider.Setup(dp => dp.DataSync.CreateAsync(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            _mockDataProvider.Verify(dp => dp.DataSync.CreateAsync(It.IsAny<CreateDataSyncOptionsCommand>()), Times.Once);
            _mockDataProvider.Verify(dp => dp.DataSync.UpdateAsync(It.IsAny<UpdateDataSyncOptionsCommand>()), Times.Never);
        }

        [Fact]
        public async Task DeleteAsync_WithNullId_ShouldHandleGracefully()
        {
            // Arrange
            string? id = null;

            // Act
            var result = await _controller.DeleteAsync(id!) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task DeleteAsync_WithEmptyId_ShouldHandleGracefully()
        {
            // Arrange
            var id = "";

            // Act
            var result = await _controller.DeleteAsync(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task IsDataSyncNameExist_WithNullName_ShouldReturnFalse()
        {
            // Arrange
            string? name = null;
            var id = "test-id";

            // Act
            var result = await _controller.IsDataSyncNameExist(name!, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsDataSyncNameExist_WithEmptyName_ShouldReturnFalse()
        {
            // Arrange
            var name = "";
            var id = "test-id";

            // Act
            var result = await _controller.IsDataSyncNameExist(name, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsDataSyncNameExist_WithNullId_ShouldCallDataProvider()
        {
            // Arrange
            var name = "test-name";
            string? id = null;
            _mockDataProvider.Setup(dp => dp.DataSync.IsDataSyncNameExist(name, id!)).ReturnsAsync(true);

            // Act
            var result = await _controller.IsDataSyncNameExist(name, id!);

            // Assert
            Assert.True(result);
            _mockDataProvider.Verify(dp => dp.DataSync.IsDataSyncNameExist(name, id!), Times.Once);
        }

    }
}
