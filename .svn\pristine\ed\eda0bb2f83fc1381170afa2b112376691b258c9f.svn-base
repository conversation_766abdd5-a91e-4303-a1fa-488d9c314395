using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowPermissionFixture : IDisposable
{
    public List<WorkflowPermission> WorkflowPermissionPaginationList { get; set; }
    public List<WorkflowPermission> WorkflowPermissionList { get; set; }
    public WorkflowPermission WorkflowPermissionDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowPermissionFixture()
    {
        var fixture = new Fixture();

        WorkflowPermissionList = fixture.Create<List<WorkflowPermission>>();

        WorkflowPermissionPaginationList = fixture.CreateMany<WorkflowPermission>(20).ToList();

        WorkflowPermissionPaginationList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowPermissionList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowPermissionDto = fixture.Create<WorkflowPermission>();

        WorkflowPermissionDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
