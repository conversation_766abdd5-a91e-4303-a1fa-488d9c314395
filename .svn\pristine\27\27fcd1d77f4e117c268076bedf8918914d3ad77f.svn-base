﻿@using ContinuityPatrol.Shared.Core.Enums;
@using ContinuityPatrol.Shared.Services.Helper;
<link href="~/css/Menu.css" rel="stylesheet" />
<link href="~/fonts/cp-icons/cp-icon.css" rel="stylesheet" />
<nav class="navbar navbar-expand-lg">
    <div class="container-fluid justify-content-between Top_Menu">
        <a class="navbar-brand p-0" href="#">
            <img class="logo" src="~/img/logo/cplogo.svg"  alt="Cp Logo" />
        </a>
        <div class="collapse navbar-collapse justify-content-center" id="navbarSupportedContent">
            <ul class="navbar-nav gap-2">
                <li class="nav-item dropdown">
                    <a class="nav-link Menu_Icon" href="#" role="button" data-bs-toggle="dropdown" id="admin-link"
                       aria-expanded="false">
                        <span class="icon cp-form-builder me-1"></span><span>Form</span>
                    </a>
                    <ul class="dropdown-menu btnDropDown">
                        <li><a asp-area="Admin" asp-controller="ComponentType" asp-action="List" class="nav-link dropdown-item">Component Type</a></li>                       
                        <li><a asp-area="Admin" asp-controller="ServerMapping" asp-action="List" class="nav-link dropdown-item">Server Mapping</a></li>
                        <li><a asp-area="Admin" asp-controller="OperationType" asp-action="List" class="nav-link dropdown-item">Operation Type</a></li>
                        <li><a asp-area="Admin" asp-controller="FormMapping" asp-action="List" class="nav-link dropdown-item">Form Mapping</a></li>
                        <li><a asp-area="Admin" asp-controller="FormType" asp-action="List" class="nav-link dropdown-item" >Form Type</a></li>                      
                        <li><a asp-area="Admin" asp-controller="FormBuilder" asp-action="List" class="nav-link dropdown-item">Form Builder</a></li>
                        <li class="nav-item"><a asp-area="Admin" asp-controller="InfraReplicationMapping" asp-action="List" class="nav-link dropdown-item">Infra-Replication Mapping</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link  Menu_Icon" aria-current="page" asp-area="Admin" asp-controller="ActionBuilder" asp-action="List">
                        <span class="icon cp-action-builder me-1"></span><span>Action Builder</span>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link Menu_Icon" href="#" role="button" data-bs-toggle="dropdown" id="admin-link"
                       aria-expanded="false">
                        <span class="icon cp-form-builder me-1"></span><span>Page Builder</span>
                    </a>
                    <ul class="dropdown-menu btnDropDown">                       
                        <li><a asp-area="Admin" asp-controller="ConfigureWidget" asp-action="List" class="nav-link dropdown-item">Configure Widget</a></li>
                        <li><a asp-area="Admin" asp-controller="ConfigurePage" asp-action="List" class="nav-link dropdown-item">Configure Page</a></li>
                        <li><a asp-area="Admin" asp-controller="SolutionMapping" asp-action="List" class="nav-link dropdown-item">Solution Mapping</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link  Menu_Icon" aria-current="page" asp-area="Configuration" asp-controller="Company" asp-action="List">
                        <span class="icon cp-company me-1"></span><span>Company</span>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link  Menu_Icon" aria-current="page" asp-area="Admin" asp-controller="User" asp-action="List">
                        <span class="icon cp-user me-1"></span><span>Users</span>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link  Menu_Icon" aria-current="page" asp-area="Admin" asp-controller="MenuBuilder" asp-action="List">
                        <span class="icon cp-user me-1"></span><span>Menu Builder</span>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link  Menu_Icon" aria-current="page" asp-area="Admin" asp-controller="LicenseManager" asp-action="List">
                        <span class="icon cp-license-manager me-1"></span><span>License Manager</span>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link Menu_Icon" aria-current="page" asp-area="Admin" asp-controller="Settings" asp-action="List">
                        <span class="icon cp-settings me-1"></span><span>Settings</span>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link Menu_Icon" aria-current="page" asp-area="Admin" asp-controller="GlobalSettings" asp-action="List">
                        <span class="icon cp-globe-settings me-1"></span><span>Global Settings</span>
                    </a>
                </li>
            </ul>
        </div>
        <ul class="navbar-nav align-items-center">            
            <li class="nav-item dropdown">
                <a class="nav-link Menu_Icon d-flex align-items-center pt-0 pb-0" aria-current="page" href="#">
                    <i class="cp-user-profile me-2"></i>
                    <div class="d-grid" style="width:120px; color:var(--bs-nav-link-color)">
                        <span class="align-middle">  @WebHelper.UserSession.LoginName</span>
                        <small id="time" class="align-middle" style="font-size:11px !important;"></small>
                    </div>
                </a>
                <ul class="dropdown-menu btnDropDown end-0">
                   @*  <li class="nav-item"><a asp-controller="Account" asp-action="UserProfile" asp-area="" class="nav-link dropdown-item">Profile</a></li> *@
                    @if (WebHelper.UserSession.AuthenticationType != AuthenticationType.AD.ToString())
                    {
                        <li class="nav-item"><a asp-controller="User" asp-action="ChangePassword" asp-area="Admin" class="nav-link dropdown-item">Change Password</a></li>
                    }
                    else
                    {
                        <li class="nav-item"><a asp-controller="User" asp-action="ChangePassword" asp-area="Admin" class="nav-link dropdown-item disabled">Change Password</a></li>
                    }
                    <li class="nav-item"><a asp-controller="Account" asp-action="About" asp-area="" class="nav-link dropdown-item">About</a></li>
                    <li class="nav-item"><a asp-controller="User" asp-action="Lock" asp-area="Admin" class="nav-link dropdown-item">Lock</a></li>
                    <li class="nav-item"><a asp-controller="Account" asp-action="Logout" asp-area="" class="nav-link dropdown-item">Logout</a></li>                   
                </ul>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link Menu_Icon d-flex align-items-center pt-0 pb-1" aria-current="page" href="~/pdf/cp6_help.pdf" target="_blank" title="User manual">
                    <i class="cp-question-mark"></i>
                </a>
            </li>
        </ul>
        <span id="sessionExpirationTime" style="display:none;">@WebHelper.UserSession.Expires</span>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false"
                aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
    <span id="currentUserRole" class="d-none">@WebHelper.UserSession.RoleName</span>
</nav>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
@* <script src="~/js/common/lock.js"></script> *@
<script src="~/js/common/navbarPartial.js"></script>
<script>
    if(sessionStorage.getItem('IsFirst')){
        window.location.href = '/Admin/User/SiteAdminLanding'
        setTimeout(() => {
            sessionStorage.removeItem('IsFirst')
        })
    }
</script>
