﻿using ContinuityPatrol.Application.Features.Server.Events.LicenseInfoEvents.Update;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Events
{
    public class ServerLicenseInfoUpdatedEventTests
    {
        private readonly Mock<ILicenseInfoRepository> _mockLicenseInfoRepository;
        private readonly Mock<ILogger<ServerLicenseInfoUpdatedEventHandler>> _mockLogger;
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly ServerLicenseInfoUpdatedEventHandler _handler;

        public ServerLicenseInfoUpdatedEventTests()
        {
            _mockLicenseInfoRepository = new Mock<ILicenseInfoRepository>();
            _mockLogger = new Mock<ILogger<ServerLicenseInfoUpdatedEventHandler>>();
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();

            _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("company-id-123");

            _handler = new ServerLicenseInfoUpdatedEventHandler(
                _mockLicenseInfoRepository.Object,
                _mockLogger.Object,
                _mockLoggedInUserService.Object);
        }

        [Fact]
        public async Task Handle_LicenseInfoDoesNotExist_AddNewLicenseInfoWhenPrimarySiteType()
        {
            var updatedEvent = new ServerLicenseInfoUpdatedEvent
            {
                EntityId = "entity-id-123",
                EntityName = "TestEntity",
                SiteType = "Primary",
                LicenseId = "license-id-123",
                PONumber = "PO123",
                EntityType = "Type1",
                Type = "TypeA",
                IpAddress = "***********",
                BusinessServiceId = "service-id",
                BusinessServiceName = "ServiceName",
                Category = "CategoryA",
                Logo = "LogoData"
            };

            _mockLicenseInfoRepository
                .Setup(repo => repo.GetLicenseInfoByEntityId(updatedEvent.EntityId))
                .ReturnsAsync((Domain.Entities.LicenseInfo)null);

            await _handler.Handle(updatedEvent, CancellationToken.None);

            _mockLicenseInfoRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.LicenseInfo>(info =>
                info.LicenseId == updatedEvent.LicenseId &&
                info.EntityId == updatedEvent.EntityId &&
                info.CompanyId == "company-id-123"
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation($"'{updatedEvent.EntityName}' updated successfully in license Info."), Times.Once);
        }

        [Fact]
        public async Task Handle_LicenseInfoExists_UpdateLicenseInfoWhenPrimarySiteType()
        {
            var updatedEvent = new ServerLicenseInfoUpdatedEvent
            {
                EntityId = "entity-id-123",
                EntityName = "UpdatedEntity",
                SiteType = "Primary",
                LicenseId = "license-id-456",
                PONumber = "PO456",
                EntityType = "Type2",
                Type = "TypeB",
                IpAddress = "***********",
                BusinessServiceId = "new-service-id",
                BusinessServiceName = "NewServiceName",
                Category = "CategoryB",
                Logo = "NewLogoData"
            };

            var existingLicenseInfo = new Domain.Entities.LicenseInfo
            {
                LicenseId = "old-license-id",
                EntityId = "entity-id-123",
                EntityName = "OldEntity",
                IsActive = true
            };

            _mockLicenseInfoRepository
                .Setup(repo => repo.GetLicenseInfoByEntityId(updatedEvent.EntityId))
                .ReturnsAsync(existingLicenseInfo);

            await _handler.Handle(updatedEvent, CancellationToken.None);

            _mockLicenseInfoRepository.Verify(repo => repo.UpdateAsync(It.Is<Domain.Entities.LicenseInfo>(info =>
                info.LicenseId == updatedEvent.LicenseId &&
                info.EntityName == updatedEvent.EntityName &&
                info.BusinessServiceId == updatedEvent.BusinessServiceId
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation($"'{updatedEvent.EntityName}' updated successfully in license Info."), Times.Once);
        }

        [Fact]
        public async Task Handle_LicenseInfoExists_DeleteWhenSiteTypeNotPrimary()
        {
            var updatedEvent = new ServerLicenseInfoUpdatedEvent
            {
                EntityId = "entity-id-123",
                EntityName = "DeletedEntity",
                SiteType = "Secondary",
            };

            var existingLicenseInfo = new Domain.Entities.LicenseInfo
            {
                LicenseId = "license-id-789",
                EntityId = "entity-id-123",
                EntityName = "ToBeDeletedEntity",
                IsActive = true
            };

            _mockLicenseInfoRepository
                .Setup(repo => repo.GetLicenseInfoByEntityId(updatedEvent.EntityId))
                .ReturnsAsync(existingLicenseInfo);

            await _handler.Handle(updatedEvent, CancellationToken.None);

            Assert.False(existingLicenseInfo.IsActive);

            _mockLicenseInfoRepository.Verify(repo => repo.DeleteAsync(It.Is<Domain.Entities.LicenseInfo>(info =>
                info.EntityId == updatedEvent.EntityId &&
                !info.IsActive
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation($"'{updatedEvent.EntityName}' updated successfully in license Info."), Times.Once);
        }
    }
}
