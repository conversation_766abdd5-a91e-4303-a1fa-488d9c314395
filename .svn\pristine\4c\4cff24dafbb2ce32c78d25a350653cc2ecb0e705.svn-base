using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class TemplateFixture : IDisposable
{
    public List<Template> TemplatePaginationList { get; set; }
    public List<Template> TemplateList { get; set; }
    public Template TemplateDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public TemplateFixture()
    {
        var fixture = new Fixture();

        TemplateList = fixture.Create<List<Template>>();

        TemplatePaginationList = fixture.CreateMany<Template>(20).ToList();

        TemplatePaginationList.ForEach(x => x.CompanyId = CompanyId);

        TemplateList.ForEach(x => x.CompanyId = CompanyId);

        TemplateDto = fixture.Create<Template>();

        TemplateDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
