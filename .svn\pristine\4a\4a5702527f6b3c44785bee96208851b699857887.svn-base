﻿using ContinuityPatrol.Application.Features.Workflow.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Workflow.Validators;

public class UpdateWorkflowValidatorTests
{
    public List<Domain.Entities.Workflow> Workflows { get; set; }

    private readonly Mock<IWorkflowRepository> _mockWorkflowRepository;
    private UpdateWorkflowCommandValidator _validator;
	public UpdateWorkflowValidatorTests()
    {
        Workflows = new Fixture().Create<List<Domain.Entities.Workflow>>();
        _mockWorkflowRepository = WorkflowRepositoryMocks.UpdateWorkflowRepository(Workflows);
    }

    //NAME

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Name_WithEmpty(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Name is required", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Name_IsNull(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = null;
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("'Name' must not be empty.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameNotEmpty, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Name_MinimumRange(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "AB";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Name should contain between 3 to 200 characters", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameRange, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Name_MaxiMumRange(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowCommand.Properties = "{\"key\":\"value\"}";

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);
		Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Name should contain between 3 to 200 characters");
		

    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Valid_Name(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "   PTS   ";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Valid_Name_With_Numbers_only(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "123415447";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Name_With_SpecialCharacters_Only(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "!@#$^%^&*(><?";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Name_With_SpecialCharacters_InFront(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "!@PTS";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Valid_Name_With_SpecialCharacters_InBack(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "PTSINDIA%$";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Valid_Name_With_OneSpace_InFront(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = " PTsINDIA";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Valid_Name_With_OneSpace_InBack(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "PTsINDIA ";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Valid_Name_With_TripleSpace_InFront(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "    PTS INDIA";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Valid_Name_With_DoubleSpace_InFront(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "  PTS INDIA";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Valid_Name_With_DoubleSpace_InBack(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "PTS INDIA  ";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Valid_Name_With_UnderScore_InFront(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "_PTINDIA";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }
    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Valid_Name_With_UnderScore_InFront_andBack(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "_PTINDIA_";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Valid_Name_With_Numbers_InFront(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "123PTINDIA";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Valid_Name_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "_123PTINDIA_";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_UpdateWorkflowCommandValidator_Valid_Name_With_UnderScore_InFront_AndNumbers_InBack(UpdateWorkflowCommand updateWorkflowCommand)
    {
		_validator = new UpdateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        updateWorkflowCommand.Name = "_PTINDIA123";
        updateWorkflowCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await _validator.ValidateAsync(updateWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(updateWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }
}