using ContinuityPatrol.Application.Features.RoboCopy.Commands.Create;
using ContinuityPatrol.Application.Features.RoboCopy.Commands.Delete;
using ContinuityPatrol.Application.Features.RoboCopy.Commands.Update;
using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetDetail;
using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetList;
using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RoboCopyModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class RoboCopyService : BaseService, IRoboCopyService
{
    public RoboCopyService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<RoboCopyListVm>> GetRoboCopyList()
    {
        Logger.LogDebug("Get All RoboCopys");

        return await Mediator.Send(new GetRoboCopyListQuery());
    }

    public async Task<RoboCopyDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "RoboCopy Id");

        Logger.LogDebug($"Get RoboCopy Detail by Id '{id}'");

        return await Mediator.Send(new GetRoboCopyDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateRoboCopyCommand createRoboCopyCommand)
    {
        Logger.LogDebug($"Create RoboCopy '{createRoboCopyCommand}'");

        return await Mediator.Send(createRoboCopyCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateRoboCopyCommand updateRoboCopyCommand)
    {
        Logger.LogDebug($"Update RoboCopy '{updateRoboCopyCommand}'");

        return await Mediator.Send(updateRoboCopyCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "RoboCopy Id");

        Logger.LogDebug($"Delete RoboCopy Details by Id '{id}'");

        return await Mediator.Send(new DeleteRoboCopyCommand { Id = id });
    }

    #region NameExist

    public async Task<bool> IsRoboCopyNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "RoboCopy Name");

        Logger.LogDebug($"Check Name Exists Detail by RoboCopy Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetRoboCopyNameUniqueQuery { Name = name, Id = id });
    }

    #endregion

    #region Paginated

    public async Task<PaginatedResult<RoboCopyListVm>> GetPaginatedRoboCopys(GetRoboCopyPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in RoboCopy Paginated List");

        return await Mediator.Send(query);
    }

    #endregion
}