using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ImpactActivity.Events.Update;

public class ImpactActivityUpdatedEventHandler : INotificationHandler<ImpactActivityUpdatedEvent>
{
    private readonly ILogger<ImpactActivityUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ImpactActivityUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<ImpactActivityUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(ImpactActivityUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} ImpactActivity",
            Entity = "ImpactActivity",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"ImpactActivity '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"ImpactActivity '{updatedEvent.Name}' updated successfully.");
    }
}