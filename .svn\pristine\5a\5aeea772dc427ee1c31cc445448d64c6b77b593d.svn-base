﻿
function generateMonitorServiceDetailsRow(workflow, application, monitorServiceDetails) {
    function getIconClass(value, label, status) {
        let iconClass = '';

        if (status) {
            return `<i class="${status} me-1 fs-6"></i>`;
        }



        if (value) {
            const lowerValue = Array.isArray(value) ? value[0]?.toLowerCase() : value.toLowerCase();

            switch (lowerValue) {
                case 'na':
                case 'disabled':
                case 'no':
                case 'not allowed':
                    iconClass = 'text-danger cp-disable';
                    break;
                case 'stopped':
                case 'stopped -1':
                case 'stopped -2':
                case 'stopped -3':
                case 'error':
                    iconClass = 'text-danger cp-fail-back';
                    break;
                case 'running':
                case 'running -1':
                case 'running -2':
                case 'running -3':
                    iconClass = 'text-success cp-reload cp-animate';
                    break;
            }
        }

        return iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
    }

    function checkAndReplace(value) {
        return (value !== null && value !== '' && value !== undefined) ? value : "NA";
    }

    if (!monitorServiceDetails?.length) {
        return '';
    } else {
        let prserverNames = [];
        let drserverNames = [];
        let prisService = [];
        let drisService = [];

        monitorServiceDetails.forEach((item) => {
            if (item?.serverType?.toLowerCase()?.includes("pr")) {
                prserverNames.push(checkAndReplace(item.workflowName));
                prisService.push(checkAndReplace(item.isServiceUpdate));
            } else if (item?.serverType?.toLowerCase()?.includes("dr")) {
                drserverNames.push(checkAndReplace(item.workflowName));
                drisService.push(checkAndReplace(item.isServiceUpdate));
            }
        });

        function formatServiceLength(services, status) {
            const filteredServices = services.filter(service => service?.toLowerCase() === status);
            return filteredServices.length > 1 ? `${status} -${filteredServices.length}` : filteredServices.length === 1 ? status : services;
        }

        const lengthservicepr = formatServiceLength(prserverNames, "NA");
        const lengthdatapr = formatServiceLength(prisService, "running") || formatServiceLength(prisService, "stopped") || formatServiceLength(prisService, "error");
        const lengthservicedr = formatServiceLength(drserverNames, "NA");
        const lengthdatadr = formatServiceLength(drisService, "running") || formatServiceLength(drisService, "stopped") || formatServiceLength(drisService, "error");

        let row = `
        <tr>
            <td>Monitoring Workflow</td>
            <td>${getIconClass(lengthservicepr, 'monitoringworkflow')} ${lengthservicepr}</td>
            <td>${getIconClass(lengthservicedr, 'monitoringworkflow')} ${lengthservicedr}</td>
        </tr>
        <tr>
            <td>Application Status</td>
            <td>${getIconClass(lengthdatapr, 'application status')} ${lengthdatapr}</td>
            <td>${getIconClass(lengthdatadr, 'application status')} ${lengthdatadr}</td>
        </tr>`;

        return row;
    }
}
