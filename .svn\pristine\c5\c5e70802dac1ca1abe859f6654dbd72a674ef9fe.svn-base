﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Replication.Events.Delete;

public class ReplicationDeletedEventHandler : INotificationHandler<ReplicationDeletedEvent>
{
    private readonly ILogger<ReplicationDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ReplicationDeletedEventHandler(ILoggedInUserService userService,
        ILogger<ReplicationDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
       
    }

    public async Task Handle(ReplicationDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress ?? "::1",
            Entity = Modules.Replication.ToString(),
            Action = $"{ActivityType.Delete} {Modules.Replication}",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Replication '{deletedEvent.ReplicationName}' deleted successfully."
        };
      
        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Replication '{deletedEvent.ReplicationName}' deleted successfully.");
    }
}