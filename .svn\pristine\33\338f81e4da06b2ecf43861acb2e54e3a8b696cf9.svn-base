﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FormTypeCategory.Events.PaginatedView;

public class FormTypeCategoeyPaginatedViewEventHandler : INotificationHandler<FormTypeCategoeyPaginatedViewEvent>
{
    private readonly ILogger<FormTypeCategoeyPaginatedViewEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FormTypeCategoeyPaginatedViewEventHandler(ILogger<FormTypeCategoeyPaginatedViewEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(FormTypeCategoeyPaginatedViewEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.FormTypeCategory.ToString(),
            Action = $"{ActivityType.View} {Modules.FormTypeCategory}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Form Mapping viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Form Mapping viewed");
    }
}