using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class DynamicDashboardRepository : BaseRepository<DynamicDashboard>, IDynamicDashboardRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public DynamicDashboardRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }
    public override async Task<IReadOnlyList<DynamicDashboard>>ListAllAsync()
    {
        return await base.QueryAll(x=>x.IsActive).Select(d=>new DynamicDashboard{
            Id=d.Id,
            ReferenceId = d.ReferenceId,
            Name = d.Name,
            Url = d.Url,
            IsDelete = d.IsDelete
        }).ToListAsync();

    }

}
