﻿using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;

namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupByNodeId;

public class WorkflowOperationGroupByNodeIdVm
{
    public string Id { get; set; }
    public string ProfileId { get; set; }
    public string ProfileName { get; set; }

    public List<WorkflowOperationGroupListVm> WorkflowOperationGroupListVm { get; set; } = new();
}