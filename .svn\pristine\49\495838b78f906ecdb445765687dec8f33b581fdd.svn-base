
function monitorTypeOCP(value, infraObjectName, moniterType, parsedData) {
    
    if (moniterType === "OpenShift") {
        
        //let replicationstatus = value.drOperationStatus
        //let pripaddress = value?.prServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : value?.prServerStatus?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : value?.prServerStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
        //let dripaddress = value?.drServerStatus?.toLowerCase() === "down" ? "cp-down-linearrow me-1 text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending  me-1 text-warning" : value?.drServerStatus?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";
        //let prdatabase = parsedData?.PR_Database_Sid?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.PR_Database_Sid ? "cp-database me-1 text-primary" : "cp-disable me-1 text-danger";
        //let drdatabase = parsedData?.DR_Database_Sid?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.DR_Database_Sid ? "cp-database me-1 text-primary" : "cp-disable me-1 text-danger";
        //let prgenerated = parsedData?.PR_Log_sequence?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.PR_Log_sequence ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
        //let application = value?.monitorServiceDetails[0]?.isServiceUpdate?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.isServiceUpdate.toLowerCase().includes("running") ? "text-success cp-reload cp-animate" : "text-danger cp-fail-back";
        //let workflow = value?.monitorServiceDetails[0]?.workflowName?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.workflowName ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
        //let drapplied = parsedData?.DR_Log_sequence?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.DR_Log_sequence ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
       
        let infraobjectdata =
            '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
            ' <table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
            '<thead style="position: sticky;top: 0px;">' +
            '<tr>' +
            '<th>Component Monitor</th>' +
            ' <th>Status</th>' +
           
            '</tr>' +
            '</thead>' +
            '<tbody style="" id="monitorwork">' +
            '<tr>' +
            '<td>' + 'OpenShiftClusterFQDN' + '</td>' +
            '<td>' + '<i class="cp-stand-server me-1 text-primary"></i>' + (parsedData?.ClusterMonitoring?.OpenShiftClusterFQDN !== undefined && parsedData?.ClusterMonitoring?.OpenShiftClusterFQDN !== null && parsedData?.ClusterMonitoring?.OpenShiftClusterFQDN !== "" ? parsedData?.ClusterMonitoring?.OpenShiftClusterFQDN : "NA") + '</td>' +
            
            '</tr>' +
            '<tr>' +
            '<td>' + 'ClusterResourceName' + '</td>' +
            '<td>' + '<i class="cp-cluster-database me-1 text-success"></i>' + (parsedData?.ClusterMonitoring?.ClusterResourceName !== undefined && parsedData?.ClusterMonitoring?.ClusterResourceName !== null && parsedData?.ClusterMonitoring?.ClusterResourceName !== "" ? parsedData?.ClusterMonitoring?.ClusterResourceName : "NA") + '</td>' +
           
            '</tr>' +
            '<tr>' +
            '<td>' + 'ClusterInfrastructure' + '</td>' +
            '<td>' + '<i class="cp-refresh me-1 text-primary"></i>' + (parsedData?.ClusterMonitoring?.ClusterInfrastructure !== undefined && parsedData?.ClusterMonitoring?.ClusterInfrastructure !== null && parsedData?.ClusterMonitoring?.ClusterInfrastructure !== "" ? parsedData?.ClusterMonitoring?.ClusterInfrastructure : "NA") + '</td>' +
            
            '</tr>' +
            '<tr>' +
            '<td>' + 'DistributionVersion' + '</td>' +
           
            '<td>' + '<i class="cp-version me-1 text-success"></i>' + (parsedData?.ClusterMonitoring?.DistributionVersion !== undefined && parsedData?.ClusterMonitoring?.DistributionVersion !== null && parsedData?.ClusterMonitoring?.DistributionVersion !== "" ? parsedData?.ClusterMonitoring?.DistributionVersion : "NA") +
            '</tr>' +
            '<tr>' +
            '<td>' + 'ProjectsName' + '</td>' +
            '<td>' + '<i class="cp-form-name me-1 text-success"></i>' + (parsedData?.ClusterMonitoring?.ClusterResourceName !== undefined && parsedData?.ClusterMonitoring?.ClusterResourceName !== null && parsedData?.ClusterMonitoring?.ClusterResourceName !== "" ? parsedData?.ClusterMonitoring?.ClusterResourceName : "NA") +
            
            '</tr>';
       // infraobjectdata += generateMonitorServiceDetailsRow(workflow, application, value.monitorServiceDetails);

        //infraobjectdata += '</tbody>' +
        //    '</table>' +
        //    '</div>' +

        //    '<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">' +
        //    '<table class="table table-hover mb-0 table-sm" style="table-layout:fixed">' +
        //    '<thead style="position: sticky;top: 0px;z-index: 1;">' +
        //    '<tr>' +
        //    '<th>Replication Monitor</th>' +
        //    '<th>Production Server</th>' +
        //    '<th>DR Server</th>' +
        //    '</tr>' +
        //    '</thead>' +
        //    '<tbody style="">' +
        //    '<tr>' +
        //    '<td>' + 'Replication Type' + '</td>' +
        //    '<td>' + '<i class="' + prreplicatype + '"></i>' + commondata.replicationType + '</td>' +
        //    '</tr>' +
        //    '<tr>' +
        //    '<td>' + "Replication Mode" + '</td>' +
        //    '<td>' + '<i class="' + prreplicamode + '"></i>' + (parsedData.PR_Replication_Mode !== null && parsedData.PR_Replication_Mode !== "" ? parsedData.PR_Replication_Mode : "NA") + '</td>' +
        //    '<td>' + '<i class="' + drreplicamode + '"></i>' + (parsedData.DR_Replication_Mode !== null && parsedData.DR_Replication_Mode !== "" ? parsedData.DR_Replication_Mode : "NA") + '</td>' +
        //    '</tr>' +
        //    '<tr>' +

        //    '<td>' + 'Service Name' + '</td>' +
        //    '<td>' + '<i class="' + prservicename + '"></i>' + (parsedData.PR_Services !== null && parsedData.PR_Services !== "" ? parsedData.PR_Services : "NA") + '</td>' +
        //    '<td>' + '<i class="' + drservicename + '"></i>' + (parsedData.DR_Services !== null && parsedData.DR_Services !== "" ? parsedData.DR_Services : "NA") + '</td>' +
        //    '</tr>' +
        //    '<tr>' +
        //    '<td>' + 'Protection Mode' + '</td>' +
        //    '<td>' + '<i class="' + prprotection + '"></i>' + (parsedData.PR_Protection_mode !== null && parsedData.PR_Protection_mode !== "" ? parsedData.PR_Protection_mode : "NA") + '</td>' +
        //    '<td>' + '<i class="' + drprotection + '"></i>' + (parsedData.DR_Protection_mode !== null && parsedData.DR_Protection_mode !== "" ? parsedData.DR_Protection_mode : "NA") + '</td>' +
        //    '</tr>' +
        //    '<tr>' +
        //    '<td>' + 'DataGuard Status' + '</td>' +
        //    '<td>' + '<i class="' + prdataguard + '"></i>' + (parsedData.PR_Dataguard_status !== null && parsedData.PR_Dataguard_status !== "" ? parsedData.PR_Dataguard_status : "NA") + '</td>' +
        //    '<td>' + '<i class="' + drdataguard + '"></i>' + (parsedData.DR_Dataguard_status !== null && parsedData.DR_Dataguard_status !== "" ? parsedData.DR_Dataguard_status : "NA") + '</td>' +
        //    '</tr>' +
        //    '</tbody>' +
        //    '</table>' +
        //    '</div>';


        setTimeout(() => {
            //$("#infraobjectalldata").empty();
            $('#replicationMonitorContainer').hide();
            $("#infraobjectalldata").html(infraobjectdata);
        }, 200)


    }
}