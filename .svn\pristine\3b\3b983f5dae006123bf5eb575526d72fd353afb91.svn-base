using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FiaInterval.Events.Delete;

public class FiaIntervalDeletedEventHandler : INotificationHandler<FiaIntervalDeletedEvent>
{
    private readonly ILogger<FiaIntervalDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FiaIntervalDeletedEventHandler(ILoggedInUserService userService,
        ILogger<FiaIntervalDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(FiaIntervalDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} FiaInterval",
            Entity = "FiaInterval",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"FiaInterval '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"FiaInterval '{deletedEvent.Name}' deleted successfully.");
    }
}