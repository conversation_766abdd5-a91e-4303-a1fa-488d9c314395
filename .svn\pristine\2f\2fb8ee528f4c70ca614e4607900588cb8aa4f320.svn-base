﻿using ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Commands.Create;
using ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Commands.Delete;
using ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Commands.Update;
using ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class RpoSlaDeviationReportsController : CommonBaseController
{
    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateRpoSlaDeviationReportResponse>> CreateRpoSlaDeviationReport([FromBody] CreateRpoSlaDeviationReportCommand createRpoSlaDeviationReportCommand)
    {
        Logger.LogDebug($"Create RpoSlaDeviationReport '{createRpoSlaDeviationReportCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateRpoSlaDeviationReport), await Mediator.Send(createRpoSlaDeviationReportCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateRpoSlaDeviationReportResponse>> UpdateSite([FromBody] UpdateRpoSlaDeviationReportCommand updateRpoSlaDeviationReportCommand)
    {
        Logger.LogDebug($"Update RpoSlaDeviationReport '{updateRpoSlaDeviationReportCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateRpoSlaDeviationReportCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteRpoSlaDeviationReportResponse>> DeleteReplication(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "RpoSlaDeviationReport Id");

        Logger.LogDebug($"Delete RpoSlaDeviationReport Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteRpoSlaDeviationReportCommand { Id = id }));
    }

    [HttpGet("businessServiceId")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<OkObjectResult> GetRpoSlaDeviationReportListByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "BusinessService Id");

        Logger.LogDebug($"Get RpoSlaDeviationReport List by BusinessService Id '{businessServiceId}'");

        return Ok(await Mediator.Send(new GetRpoSlaDeviationReportByBusinessServiceIdQuery { BusinessServiceId = businessServiceId }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllRpoSlaDeviationReportsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllRpoSlaDeviationReportsNameCacheKey };

        ClearCache(cacheKeys);
    }
}