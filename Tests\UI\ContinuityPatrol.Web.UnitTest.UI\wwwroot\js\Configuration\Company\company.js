﻿let selectedValues = [], lastSelectedFile = "", dataCompanyLogo = "";
window.__companyJsLoaded = true;

const companyURL = {
    nameExistUrl: "Configuration/Company/IsCompanyNameExist",
    displayNameExistUrl: "Configuration/Company/IsCompanyDisplayNameExist",
    paginationUrl: "/Configuration/Company/GetPagination",
    createUpdateUrl: "Configuration/Company/CreateOrUpdate",
    deleteUrl: "Configuration/Company/Delete",
    getCompanyNameUrl: "Configuration/Company/GetCompanyById"
}



let permission = {
    "Create": $("#companyConfigCreate")?.data("create-permission")?.toLowerCase(),
    "Delete": $("#companyConfigDelete")?.data("delete-permission")?.toLowerCase()
}

if (permission.Create == 'false') {
    $("#companyCreateBtn").addClass('btn-disabled').css('pointer-events', 'none');
}

const randomColor = () => {
    return `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0').toUpperCase()}`
}

let dataTable = $('#companyTable').DataTable(
    {
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next" ></i>',
                previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
            },
            infoFiltered: ""
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        Sortable: true,
        "processing": true,
        "serverSide": true,
        "filter": true,
        "order": [],
        "ajax": {
            "type": "GET",
            "url": companyURL.paginationUrl,
            "dataType": "json",
            "data": function (d) {
                let sortIndex = d?.order[0]?.column || '';
                let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "displayName" : sortIndex === 3 ? "webAddress" : sortIndex === 4 ? "status" : "";
                let orderValue = d?.order[0]?.dir || 'asc';
                d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                d.pageSize = d?.length;
                d.searchString = selectedValues?.length === 0 ? $('#companySearchInp')?.val() : selectedValues?.join(';');
                d.sortColumn = sortValue;
                d.SortOrder = orderValue;
                selectedValues.length = 0;
            },
            "dataSrc": function (json) {
                json.recordsTotal = json?.data?.totalPages;
                json.recordsFiltered = json?.data?.totalCount;
                if (json?.success && Array.isArray(json?.data?.data) && json?.data?.data?.length) {
                    $(".pagination-column").removeClass("disabled");
                    //if ($('#currentUserRole').text()?.toLowerCase() === 'siteadmin') {
                    //    json.data.data = json?.data?.data ?.filter((d) => d?.isParent);
                    //}
                    return json?.data?.data;
                }
                else {
                    $(".pagination-column").addClass("disabled");
                    if (!json?.success && json?.message) {
                        notificationAlert('warning', json?.message);
                    }
                    return [];
                }
            },
        },
        "columnDefs": [
            {
                "targets": [1, 2],
                "className": "truncate"
            }
        ],
        "columns": [
            {
                "data": null,
                "name": "Sr. No.",
                "autoWidth": true,
                "orderable": false,
                "render": function (data, type, row, meta) {
                    if (type === 'display') {
                        return meta.row + 1;
                    }
                    return data;
                },
                "orderable": false
            },
            {
                "data": "name",
                "name": 'Company',
                "autoWidth": true,
                "render": function (data, type, row) {
                    let companyName = row?.name || 'NA';
                    let companyLogo = row?.companyLogo;
                    let nameSplit = companyName?.split(/[ _]+/);
                    let initials = nameSplit?.length > 1 ? nameSplit[0]?.trim().substring(0, 1) + nameSplit[1]?.trim().substring(0, 1).toUpperCase() : nameSplit[0]?.trim().substring(0, 1).toUpperCase();

                    if (type === 'display') {
                        return companyLogo
                            ? `<div class="d-flex align-items-center"><span><img class="Avatar_Logo" title="${companyName || 'NA'}" src="${companyLogo}" alt="${companyName || 'NA'}" /></span><span class="text-truncate d-inline-block" title="${companyName || 'NA'}" style="max-width:150px">${companyName || 'NA'}</span></div>`
                            : `<span class="Avatar_Logo"><span id="companyname" class="Icon" title="${companyName || 'NA'}">${initials || 'NA'}</span></span><span title="${companyName || 'NA'}">${companyName || 'NA'}</span>`;
                    } else {
                        return data;
                    }
                }
            },
            {
                "data": "displayName", "name": "Display Name", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return `<span title= "${data || 'NA'}">${data || 'NA'}</span>`;
                    }
                    return data;
                }
            },
            {
                "data": "webAddress", "name": "Web Address", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return `<span title="${data || 'NA'}">${data || 'NA'}</span>`;
                    }
                    return data;
                }
            },
            {
                "render": function (data, type, row) {
                    const isEditAllowed = permission?.Create === "true" ? true : false;
                    const isDeleteAllowed = permission?.Delete === "true" ? true : false;
                    const isParent = row.isParent;
                    return `<div class="d-flex align-items-center gap-2"><span role="button" title="Edit" class="${isEditAllowed ? 'btnCompanyEdit' : 'icon-disabled'}" data-company='${isEditAllowed ? btoa(encodeURIComponent(JSON.stringify(row || "NA"))) : ''}'> <i class="cp-edit"></i></span>
                        <span ${!isDeleteAllowed || isParent ? 'style="cursor:not-allowed; opacity:0.50;"' : 'role="button"'}title="Delete" class="${isDeleteAllowed && !isParent ? 'btnCompanyDelete' : 'icon-disabled'}"  ${isDeleteAllowed && !isParent ? `data-datacompanyid="${row.id || 'NA'}" data-datacompanyname="${row.name || 'NA'}"` : ''}"><i class="cp-Delete"></i></span></div>`;
                },
                "orderable": false
            }
        ],
        "columnDefs": [
            {
                "targets": [1, 2, 3],
                "className": "truncate"
            }
        ],
        "rowCallback": function (row, data, index) {
            let api = this.api();
            let startIndex = api?.context[0]?._iDisplayStart;
            let counter = startIndex + index + 1;
            $('td:eq(0)', row).html(counter);
        },
        "initComplete": function () {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
        },
        "drawCallback": function () {
            const namelist = document.querySelectorAll("#companyname");
            namelist.forEach((name) => {
                name.style.backgroundColor = randomColor();
            });
        }
    }
);

dataTable.on('draw.dt', function () {
    $('.paginate_button.page-item.previous').attr('title', 'Previous');
    $('.paginate_button.page-item.next').attr('title', 'Next');
});

$('#companySearchInp').on('keydown input', function (e) {
    if (e.key === '=' || e.key === 'Enter' || (e.shiftKey && e.key == '<')) {
        e.preventDefault();
        return false;
    }
    handleSearchDebounced();
});

const handleSearchDebounced = commonDebounce(function () {
    const inputValue = $('#companySearchInp').val();
    const checkboxIds = ['filterCompanyName', 'filterDisplayName', 'filterWebAddress'];

    checkboxIds.forEach(id => {
        const checkbox = $(`#${id}`);
        if (checkbox.is(':checked')) {
            selectedValues.push(checkbox?.val() + inputValue);
        }
    });

    dataTable?.ajax.reload(function (json) {
        if (json?.recordsFiltered === 0) {
            $('.dataTables_empty').text('No matching records found');
        }
    })
}, 500);

function populateCompanyFields(companyData) {
    let compDetail = { companyId: companyData?.id, isParent: companyData?.isParent, parentId: companyData?.parentId };
    $('#companyName').val(companyData?.name).attr('companyDetails', JSON.stringify(compDetail));
    $('#companyDisplayName').val(companyData?.displayName);
    $('#companyWebAddress').val(companyData?.webAddress);
    $('#parentCompanyName').val(companyData?.parentName)
    $('#companyLogoFile').attr('logoName', companyData?.logoName);

    const fileInput = document.getElementById('companyLogoFile');
    $("#btnRemoveCompanyLogo").toggle(!!companyData?.logoName);
    if (companyData?.companyLogo) {
        dataCompanyLogo = companyData?.companyLogo;
        const myFile = new File([companyData?.companyLogo], companyData?.logoName, { type: 'text/plain', });
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(myFile);
        fileInput.files = dataTransfer?.files;
    } else {
        fileInput.value = "";
        fileInput.setAttribute('placeholder', 'No file chosen');
    }
    $('#companyNameError, #companyDisplayNameError, #companyWebAddressError, #companyLogoError').text('').removeClass('field-validation-error');
}

//Validations
async function IsSameNameExist(url, inputValue, errordisplay) {
    return !inputValue?.name?.trim() ? true : (await getAysncWithHandler(url, inputValue, OnError)) ? errordisplay : true;
}

async function validateName(value, id = null, url, errorElement, errorText, isNameExist) {
    if (!value) {
        errorElement.text(errorText).addClass('field-validation-error');
        return false;
    } else if (value.includes('<')) {
        errorElement.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    } else {
        let data = { name: value, id: id };
        const validationResults = [
            SpecialCharValidateCustom(value),
            ShouldNotBeginWithUnderScore(value),
            ShouldNotBeginWithNumber(value),
            ShouldNotBeginWithDotAndHyphen(value),
            ShouldNotConsecutiveDotAndHyphen(value),
            OnlyNumericsValidate(value),
            ShouldNotBeginWithSpace(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            minMaxCompanylength(value),
            secondChar(value),
            await IsSameNameExist(url, data, isNameExist)
        ];
        return CommonValidation(errorElement, validationResults);
    }
}

async function validateDisplayName(value, id = null, url, errorElement, errorText, isNameExist) {

    if (!value) {
        errorElement.text(errorText).addClass('field-validation-error');
        return false;
    } else if (value.includes('<')) {
        errorElement.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    } else {
        let data = { name: value, id: id };

        const validationResults = [
            SpecialCharValidate(value),
            OnlyNumericsValidate(value),
            ShouldNotBeginWithUnderScore(value),
            ShouldNotBeginWithSpace(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            DisplayLength(value),
            secondChar(value),
            await IsSameNameExist(url, data, isNameExist)
        ];
        return CommonValidation(errorElement, validationResults);
    }
}

async function validateWebAddress(value) {
    const errorElement = $('#companyWebAddressError');
    let format = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;

    if (!value || format.test(value?.charAt(0))) {
        errorElement.text(!value ? 'Enter company web address' : 'Enter valid company web address').addClass('field-validation-error');
        return false;
    } else {
        return CommonValidation(errorElement, [WebAddressValidate(value)]);
    }
}
function convertToBase64(fileInput) {
    return new Promise((resolve, reject) => {
        const selectedFile = fileInput?.files[0];
        if (selectedFile) {
            const reader = new FileReader();
            reader.onload = function (event) {
                const base64String = event?.target?.result;
                resolve(base64String);
            };
            reader.readAsDataURL(selectedFile);
        } else {
            reject('No file selected.');
        }
    });
}
function companyFileFormatValidate(fileName) {
    const validExtension = 'png';
    const fileExtension = fileName?.split('.').pop().toLowerCase();
    return fileExtension === validExtension;
}

async function FileValidation(fileInput, create = null) {
    const errorElement = $('#companyLogoError');

    if (!fileInput?.files?.length || fileInput?.files[0]?.name === "No file chosen") {
        return true;
    }
    let fileSize = fileInput?.files[0]?.size;
    let fileName = fileInput?.files[0]?.name;

    if (!companyFileFormatValidate(fileName)) {
        errorElement.text('Only png images are allowed')
            .addClass('field-validation-error');
        return false;
    }
    if (create === "new") {
        dataCompanyLogo = await convertToBase64(fileInput);
    }
    if (!dataCompanyLogo) {
        dataCompanyLogo = await convertToBase64(fileInput);
    }
    $("#companyLogoFile").attr("logoName", fileName);
    const validationResults = [
        FileSizeValidate(fileSize)
    ];
    return CommonValidation(errorElement, validationResults);
}

//Clear data on create new Company
$('#companyCreateBtn').on('click', async function () {
    $("#btnRemoveCompanyLogo").hide();
    $("#companyName").val("").attr("companyDetails", '{}');
    $("#companyDisplayName, #companyWebAddress").val("");
    $("#companyLogoFile").val("").attr("logoName", "");
    dataCompanyLogo = "";
    $('#companyNameError,#companyDisplayNameError,#companyWebAddressError,#companyLogoError').text('').removeClass('field-validation-error');
    let parentCompanyId = $('#parentCompanyName').data('parentid');
    if (parentCompanyId) {
        await $.ajax({
            url: RootUrl + companyURL.getCompanyNameUrl,
            method: 'GET',
            dataType: 'json',
            data: { id: parentCompanyId },
            success: function (response) {
                if (response && response?.success && response?.data?.hasOwnProperty("displayName")) {
                    $('#parentCompanyName').val(response?.data?.displayName ?? '');
                } else {
                    $('#parentCompanyName').val("");
                }
                $('#companySaveBtn').text('Save');
            },
        });
    }
    $('#companyCreateModal').modal('show')
});

//edit icon on click
$('#companyTable').on('click', '.btnCompanyEdit', function () {
    let companyencoded = $(this).data('company');
    let companyData = JSON.parse(decodeURIComponent(atob(companyencoded)));
    if (companyData) {
        populateCompanyFields(companyData);
        $('#companySaveBtn').text('Update');
        $('#companyCreateModal').modal('show');
    }
});

// save btn on click
$("#companySaveBtn").on('click', commonDebounce(async function () {
    let companyname = $("#companyName").val();
    let companyDetail = JSON.parse($('#companyName').attr('companyDetails') || '{}');
    let compDisplayName = $("#companyDisplayName").val();
    let webAddress = $("#companyWebAddress").val();
    let compLogoName = $("#companyLogoFile").attr("logoName");
    let isName = await validateName(companyname, companyDetail.companyId, RootUrl + companyURL.nameExistUrl, $('#companyNameError'), 'Enter company name', 'Name already exists');
    let isDisplayName = await validateDisplayName(compDisplayName, companyDetail.companyId, RootUrl + companyURL.displayNameExistUrl, $('#companyDisplayNameError'), 'Enter display name', 'Display name already exists');
    let isWebAddress = await validateWebAddress(webAddress);
    let fileInput = document.getElementById('companyLogoFile');
    let isFileName = await FileValidation(fileInput);

    if (isName && isDisplayName && isWebAddress && isFileName) {

        sanitizeContainer(['companyName', 'companyDisplayName', 'companyWebAddress', 'parentCompanyName']);
        let savedata = {
            id: companyDetail.companyId, name: companyname, displayName: compDisplayName, IsParent: companyDetail.isParent, parentId: companyDetail.parentId, parentName: $("#parentCompanyName").val(), companyLogo: dataCompanyLogo, LogoName: compLogoName, webAddress: webAddress, __RequestVerificationToken: gettoken()
        }

        await $.ajax({
            url: RootUrl + companyURL.createUpdateUrl,
            type: "POST",
            dataType: "json",
            data: savedata,
            success: function (response) {
                if (response && response?.success && response?.data?.message) {
                    $('#companyCreateModal').modal('hide');
                    notificationAlert("success", response?.data?.message);
                    setTimeout(() => {
                        dataTable.ajax.reload()
                    }, 500);
                } else {
                    errorNotification(response);
                }
            }
        });
    }
}, 800));

//delete icon on click
$('#companyTable').on('click', '.btnCompanyDelete', function () {
    let companyId = $(this).data('datacompanyid');
    let companyName = $(this).data('datacompanyname');
    if (companyId) {
        $('#companyDeleteId').attr("title", companyName).val(companyId).text(companyName);
        $('#companyDeleteModal').modal('show');
    }
});

//confirm Delete on click
$("#companyDeleteBtn").on('click', commonDebounce(async function () {
    const companyId = $('#companyDeleteId').val();
    if (companyId) {
        await $.ajax({
            url: RootUrl + companyURL.deleteUrl,
            type: "DELETE",
            dataType: "json",
            data: { id: companyId },
            success: function (response) {
                if (response && response?.success && response?.data) {
                    $('#companyDeleteModal').modal('hide');
                    notificationAlert("success", response?.data?.message);
                    setTimeout(() => {
                        dataTable.ajax.reload()
                    }, 500);
                } else {
                    $('#companyDeleteModal').modal('hide');
                    errorNotification(response);
                }
            }
        });
    }
}, 800));

// Name and Display name validations
$('#companyName, #companyDisplayName').on('input', commonDebounce(async function () {
    let companyDetails = JSON.parse($('#companyName').attr('companyDetails') || '{}');
    const value = $(this)?.val();

    let sanitizedValue = value?.replace(/\s{2,}/g, ' ');
    $(this)?.val(sanitizedValue);

    if (this.id === 'companyName') {
        await validateName(sanitizedValue, companyDetails?.companyId, RootUrl + companyURL.nameExistUrl, $('#companyNameError'), 'Enter company name', 'Name already exists');
    } else {
        await validateDisplayName(sanitizedValue, companyDetails?.companyId, RootUrl + companyURL.displayNameExistUrl, $('#companyDisplayNameError'), 'Enter display name', 'Display name already exists');
    }
}, 400));

// Web Address on input
$('#companyWebAddress').on('input', async function () {
    const value = $(this)?.val();
    $(this).val(value?.replace(/  +/g, " "));
    await validateWebAddress(value);
});

$('#companyLogoFile').on('click', function () {
    let fileInput = document.getElementById('companyLogoFile');
    lastSelectedFile = fileInput?.files?.length ? fileInput?.files[0] : null;
});

$('#companyLogoFile').on('change', async function (event) {
    let create = "new";
    let fileInput = document.getElementById('companyLogoFile');
    if (!event?.target?.files?.length && lastSelectedFile) {
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(lastSelectedFile);
        fileInput.files = dataTransfer?.files;
        await FileValidation(fileInput, create);
    }
    else if (event?.target?.files?.length) {
        await FileValidation(fileInput, create);
        lastSelectedFile = fileInput?.files[0];
    }
    if (fileInput?.files?.length !== 0) {
        $("#btnRemoveCompanyLogo").show();
    } else {
        $("#btnRemoveCompanyLogo").hide();
    }
});

// Remove Company Logo button on click
$("#btnRemoveCompanyLogo").on("click", function (e) {
    e.stopPropagation();
    $("#companyLogoFile").val('').attr("logoName", "");
    dataCompanyLogo = "";
    let file = $('#companyLogoFile')?.val()
    if (file?.length == 0) {
        $('#companyLogoError').text('').removeClass('field-validation-error');
        $("#btnRemoveCompanyLogo").hide();
    }
});

