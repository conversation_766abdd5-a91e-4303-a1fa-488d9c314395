﻿namespace ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessServiceDrReadyDetails;

public record BusinessServiceDrReadyDetailVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string DRReady { get; set; }
    public virtual DrReadyListVm DrReadyListVm { get; set; } = new();
}

public record DrReadyListVm
{
    public int DrReadyInfraCount { get; set; }
    public string DrReadyInfraObject { get; set; }
    public int DrReadyWorkflowCount { get; set; }
    public string DrReadyWorkflow { get; set; }
    public bool IsDataLagExceed { get; set; }
    public virtual List<DrReadyWorkflowAttach> DrReadyWorkflowAttaches { get; set; } = new();
    public int DrReadyExecutionCount { get; set; }
    public string DrReadyWorkflowExecutionStatus { get; set; }
    public virtual List<DrReadyWorkflowExecution> DrReadyWorkflowExecutions { get; set; } = new();
    public string DataLagExceedCount { get; set; }
    public virtual List<DrDataLagExceed> DrDataLagExceeds { get; set; } = new();
}

public record DrReadyWorkflowAttach
{
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string WorkflowStatus { get; set; }
}

public record DrReadyWorkflowExecution
{
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string FailedActionId { get; set; }
    public string FailedActionName { get; set; }
    public string ErrorMessage { get; set; }
    public string WorkflowStatus { get; set; }
}

public record DrDataLagExceed
{
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string ErrorMessage { get; set; }
    public string HeatmapStatus { get; set; }
}