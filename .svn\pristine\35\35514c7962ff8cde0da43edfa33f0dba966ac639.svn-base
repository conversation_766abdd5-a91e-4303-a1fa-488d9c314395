﻿using ContinuityPatrol.Domain.ViewModels.IncidentDailyModel;

namespace ContinuityPatrol.Application.Features.IncidentDaily.Queries.GetDetail;

public class GetIncidentDailyDetailQueryHandler : IRequestHandler<GetIncidentDailyDetailQuery, IncidentDailyDetailVm>
{
    private readonly IIncidentDailyRepository _incidentDailyRepository;
    private readonly IMapper _mapper;

    public GetIncidentDailyDetailQueryHandler(IMapper mapper, IIncidentDailyRepository incidentDailyRepository)
    {
        _mapper = mapper;
        _incidentDailyRepository = incidentDailyRepository;
    }

    public async Task<IncidentDailyDetailVm> Handle(GetIncidentDailyDetailQuery request,
        CancellationToken cancellationToken)
    {
        var incidentDaily = await _incidentDailyRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(incidentDaily, nameof(Domain.Entities.IncidentDaily),
            new NotFoundException(nameof(Domain.Entities.IncidentDaily), request.Id));

        var incidentDailyDetail = _mapper.Map<IncidentDailyDetailVm>(incidentDaily);

        return incidentDailyDetail;
    }
}