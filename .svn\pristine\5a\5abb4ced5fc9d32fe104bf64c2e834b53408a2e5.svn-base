﻿using ContinuityPatrol.Application.Features.SVCGMMonitoringStatus.Queries.GetDetail;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SVCGMMonitoringStatus.Queries
{
    public class SVCGMMonitorStatusDetailQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISVCGMMonitorStatusRepository> _mockSVCGMMonitorStatusRepository;
        private readonly SVCGMMonitorStatusDetailQueryHandler _handler;

        public SVCGMMonitorStatusDetailQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSVCGMMonitorStatusRepository = new Mock<ISVCGMMonitorStatusRepository>();
            _handler = new SVCGMMonitorStatusDetailQueryHandler(_mockMapper.Object, _mockSVCGMMonitorStatusRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedDetail_WhenReferenceIdExists()
        {
            var referenceId = "123";
            var mockEntity = new SVCGMMonitorStatus
            {
                ReferenceId = referenceId,
                Type = "Active"
            };

            var mockViewModel = new SVCGMMonitorStatusDetailVm
            {
                Id = referenceId,
                Type = "Active"
            };

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(referenceId))
                .ReturnsAsync(mockEntity);
            _mockMapper.Setup(mapper => mapper.Map<SVCGMMonitorStatusDetailVm>(mockEntity))
                .Returns(mockViewModel);

            var query = new SVCGMMonitorStatusDetailQuery { Id = referenceId };

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(referenceId, result.Id);
            Assert.Equal("Active", result.Type);

            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.GetByReferenceIdAsync(referenceId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<SVCGMMonitorStatusDetailVm>(mockEntity), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenReferenceIdDoesNotExist()
        {
            var invalidReferenceId = "999";

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(invalidReferenceId))
                .ReturnsAsync((SVCGMMonitorStatus)null);

            var query = new SVCGMMonitorStatusDetailQuery { Id = invalidReferenceId };

            await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(query, CancellationToken.None));

            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.GetByReferenceIdAsync(invalidReferenceId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<SVCGMMonitorStatusDetailVm>(It.IsAny<SVCGMMonitorStatus>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryOnce()
        {
            var referenceId = "123";

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(referenceId))
                .ReturnsAsync(new SVCGMMonitorStatus());

            var query = new SVCGMMonitorStatusDetailQuery { Id = referenceId };

            await _handler.Handle(query, CancellationToken.None);

            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.GetByReferenceIdAsync(referenceId), Times.Once);
        }
    }
}
