using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Create;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Update;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.DriftImpactTypeMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class DriftImpactTypeMasterProfile : Profile
{
    public DriftImpactTypeMasterProfile()
    {
        CreateMap<DriftImpactTypeMaster, DriftImpactTypeMasterListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DriftImpactTypeMaster, DriftImpactTypeMasterDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<DriftImpactTypeMaster, CreateDriftImpactTypeMasterCommand>().ReverseMap();
        CreateMap<DriftImpactTypeMaster, DriftImpactTypeMasterViewModel>().ReverseMap();

        CreateMap<CreateDriftImpactTypeMasterCommand, DriftImpactTypeMasterViewModel>().ReverseMap();
        CreateMap<UpdateDriftImpactTypeMasterCommand, DriftImpactTypeMasterViewModel>().ReverseMap();

        CreateMap<UpdateDriftImpactTypeMasterCommand, DriftImpactTypeMaster>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<PaginatedResult<DriftImpactTypeMaster>, PaginatedResult<DriftImpactTypeMasterListVm>>()
          .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}