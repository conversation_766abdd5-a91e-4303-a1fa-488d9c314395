﻿
// Themes begin
am4core.useTheme(am4themes_animated);
// Themes end

// create chart
var chart = am4core.create("ResilienceHealthChart", am4charts.GaugeChart);

chart.innerRadius = -15;
if (chart.logo) {
    chart.logo.disabled = true;
}

// Change the padding values
chart.padding(-10, -20, -20, -20)

var axis = chart.xAxes.push(new am4charts.ValueAxis());
axis.min = 0;
axis.max = 100;
axis.strictMinMax = true;
axis.renderer.labels.template.disabled = true;

var colorSet = new am4core.ColorSet();

var gradient = new am4core.LinearGradient();
gradient.stops.push({ color: am4core.color("red") })
gradient.stops.push({ color: am4core.color("yellow") })
gradient.stops.push({ color: am4core.color("green") })

axis.renderer.line.stroke = gradient;
axis.renderer.line.strokeWidth = 5;
axis.renderer.line.strokeOpacity = 1;

axis.renderer.grid.template.disabled = true;

var hand = chart.hands.push(new am4charts.ClockHand());
hand.radius = am4core.percent(97);
hand.fill = am4core.color("#fbbd60");
hand.stroke = am4core.color("#fbbd60");
hand.radius = am4core.percent(50);
hand.innerRadius = am4core.percent(0);
hand.radius = am4core.percent(80);
hand.startWidth = 10;
function ResilienceHealthChartdata(infraObjectId, infraObjectName, datalagValue, configuredRPO, data, moniterType, currentRPO, rpoThreshold) {
    
    const dataLagValuehealth = (datalagValue !== undefined && datalagValue !== "" && datalagValue !== null && datalagValue !== "0")
        ? `${datalagValue}`
        : 'NA';
    const currentRPOValuehealth = (currentRPO !== undefined && currentRPO !== "" && currentRPO !== null && currentRPO !== "0")
        ? `${currentRPO}`
        : 'NA';

    let result = "";
    let styleValue = 0;

    let rpoResult = '';
    let rpoStyleValue = 0
    
    if (currentRPOValuehealth !== "NA" && moniterType?.toLowerCase() !== "openshift") {
        hand.disabled = false;
        if (currentRPOValuehealth?.includes(".")) {
            const value = currentRPOValuehealth?.split(".");
            const hours = value[0] * 24;
            const minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const min = minutes?.split(':');
            const firstValue = parseInt(min[0]) + parseInt(hours);
            rpoResult = firstValue + ":" + min[1];
            rpoStyleValue = (parseInt(firstValue) * 60) + parseInt(min[1]);
        } else if (currentRPOValuehealth?.includes("+")) {
            const value = currentRPOValuehealth?.split(" ");
            rpoResult = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = rpoResult?.split(':')?.slice(1, 2)?.join(':');
            rpoStyleValue = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
        } else {
            rpoResult = currentRPOValuehealth?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = rpoResult.split(':')?.slice(1, 2)?.join(':');
            rpoStyleValue = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
        }
    }
    else {
        hand.disabled = true;
    }

    if (dataLagValuehealth !== "NA" && moniterType?.toLowerCase() !== "openshift" ) {
        hand.disabled = false;
        if (dataLagValuehealth?.includes(".")) {
            const value = dataLagValuehealth?.split(".");
            const hours = value[0] * 24;
            const minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const min = minutes?.split(':');
            const firstValue = parseInt(min[0]) + parseInt(hours);
            result = firstValue + ":" + min[1];
            styleValue = (parseInt(firstValue) * 60) + parseInt(min[1]);
        } else if (dataLagValuehealth?.includes("+")) {
            const value = dataLagValuehealth?.split(" ");
            result = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            styleValue = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
        } else {
            result = dataLagValuehealth?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result.split(':')?.slice(1, 2)?.join(':');
            styleValue = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
        }
    } 
    else {
        hand.disabled = true;
    }
    function calculateAverage(configuredRPO, rpoStyleValue) {
        const configureRPO = Number(configuredRPO);
        const currentRPO = Number(rpoStyleValue);
        const percentage = (Math.round(currentRPO) / Math.round(configureRPO)) * 100
        //console.log(percentage,'average')
        return percentage
    }
    function updateStatusAndHealth() {
        let handValue = null;
        let healthText = "";
        let healthClass = "";
        let perecent = calculateAverage(configuredRPO, rpoStyleValue)
        let perecentage =  perecent < 100 ? (100 - perecent) : 1 
        //console.log(perecentage, 'percent')
        const isNA = dataLagValuehealth === "NA"
            if (isNA || moniterType?.toLowerCase() === "openshift") {
                handValue = null;
                healthText = "NA";
                healthClass = "text-info";
            } else if (perecentage >= 90) {
                healthText = "Excellent";
                healthClass = "text-success";
                handValue = perecentage
            } else if (perecentage >= 70 && perecentage < 90) {
                healthText = "Good";
                healthClass = "text-success";
                handValue = perecentage
            } else if (perecentage >= 40 && perecentage < 70) {
                healthText = "Average";
                healthClass = "text-warning";
                handValue = perecentage
            } else if (perecentage < 30) {
                healthText = "Critical";
                healthClass = "text-danger";
                handValue = perecentage
            }else {
                handValue = null;
                healthText = "NA";
                healthClass = "text-info";
            }

            if (handValue !== null && !isNaN(handValue)) {
                hand.showValue(handValue, 1000, am4core.ease.cubicOut);
            }

            $("#Resilience_Health")
                .removeClass("text-warning text-info text-success text-danger")
                .text(healthText)
                .addClass(healthClass);
    }
    //function calculateAverage(configuredRPO, rpoStyleValue, rpoThreshold) {
    //    const configureRPO = Number(configuredRPO);
    //    const currentRPO = Number(rpoStyleValue);
    //    const thresholdRPO = Number(rpoThreshold);
      

    //    if (currentRPO < thresholdRPO) {
    //        return 100;
    //    } else if (currentRPO > thresholdRPO && currentRPO < configureRPO) {
    //        return 60;
    //    } else if (currentRPO > configureRPO) {
    //        return 0;
    //    }
    //}
    //function updateStatusAndHealth(data) {
    //    const percentage = Number(data?.percentage);
    //    let handValue = null;
    //    let healthText = "";
    //    let healthClass = "";
    //    let dataLagScore = calculateAverage(configuredRPO, rpoStyleValue, rpoThreshold);
    //     let total = rpoStyleValue ? dataLagScore == 0 ? dataLagScore : (dataLagScore + percentage) / 2 : percentage;
        
    //    const isNA = dataLagValuehealth === "NA" || currentRPOValuehealth === "NA";

    //    if (isNA || percentage === "" || moniterType?.toLowerCase() === "openshift") {
    //        handValue = null;
    //        healthText = "NA";
    //        healthClass = "text-info";
    //    } else if (total >= 90) {
    //        healthText = "Excellent";
    //        healthClass = "text-success";
    //        handValue = total
    //    } else if (total >= 70 && total < 90) {
    //        healthText = "Good";
    //        healthClass = "text-success";
    //        handValue = total
    //    } else if (total >= 40 && total < 70) {
    //        healthText = "Average";
    //        healthClass = "text-warning";
    //        handValue = total
    //    } else if (total < 30) {
    //        healthText = "Critical";
    //        healthClass = "text-danger";
    //        handValue = total
    //    }else {
    //        handValue = null;
    //        healthText = "NA";
    //        healthClass = "text-info";
    //    }

    //    if (handValue !== null && !isNaN(handValue)) {
    //        hand.showValue(handValue, 1000, am4core.ease.cubicOut);
    //    } 

    //    $("#Resilience_Health")
    //        .removeClass("text-warning text-info text-success text-danger")
    //        .text(healthText)
    //        .addClass(healthClass);
    //}
    setTimeout(() => {
       
        updateStatusAndHealth();
    }, 100);

    $("#Resilience_infraobject").text(infraObjectName).attr('title', infraObjectName);

}


