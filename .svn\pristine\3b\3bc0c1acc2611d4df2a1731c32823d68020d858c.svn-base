﻿using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class FormHistoryController : BaseController
{
    private readonly ILogger<FormHistoryController> _logger;
    private readonly IDataProvider _dataProvider;

    public FormHistoryController(ILogger<FormHistoryController> logger, IDataProvider dataProvider)
    {
        _logger = logger;
        _dataProvider = dataProvider;
    }
    public IActionResult List()
    {
        _logger.LogDebug("Entering List method in FormHistory");

        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> GetFormHistoryByFormId(string formId, string version = null)
    {
        _logger.LogDebug("Entering GetFormHistoryByFormId method in FormHistory");

        try
        {
            var formVersions = await _dataProvider.FormHistory.GetFormHistoryById(formId, version);
            _logger.LogDebug($"Successfully retrieved form history by formId '{formId}' & version in FormHistory");
            return Json(new { success = true, data = formVersions });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on form history page while retrieving form history list by formId & version.", ex);
            return ex.GetJsonException();
        }
    }
}