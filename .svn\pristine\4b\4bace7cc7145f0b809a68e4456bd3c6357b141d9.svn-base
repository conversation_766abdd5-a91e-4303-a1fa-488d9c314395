﻿namespace ContinuityPatrol.Application.Features.WorkflowActionType.Commands.Create;

public class CreateWorkflowActionTypeCommandValidator : AbstractValidator<CreateWorkflowActionTypeCommand>
{
    private readonly IWorkflowActionTypeRepository _workflowActionTypeRepository;

    public CreateWorkflowActionTypeCommandValidator(IWorkflowActionTypeRepository workflowActionTypeRepository)
    {
        _workflowActionTypeRepository = workflowActionTypeRepository;


        RuleFor(p => p.ActionType)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(e => e)
            .MustAsync(WorkflowActionTypeUnique)
            .WithMessage("A same ActionType already exists.");
    }

    private async Task<bool> WorkflowActionTypeUnique(CreateWorkflowActionTypeCommand createWorkflowActionTypeCommand,
        CancellationToken token)
    {
        return !await _workflowActionTypeRepository.IsWorkflowActionTypeUnique(createWorkflowActionTypeCommand
            .ActionType);
    }
}