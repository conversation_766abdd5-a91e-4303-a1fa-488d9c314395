﻿// Mock data for Menu Builder

// Available pages that can be mapped to menu items
const AVAILABLE_PAGES = [
    {
        id: 'dashboard',
        title: 'Dashboard',
        description: 'Main dashboard with overview statistics',
        url: '/dashboard',
        icon: 'cp-dashboard'
    },
    {
        id: 'analytics',
        title: 'Analytics',
        description: 'Data analytics and insights',
        url: '/analytics',
        icon: 'fas fa-chart-line'
    },
    {
        id: 'reports',
        title: 'Reports',
        description: 'Generate and view reports',
        url: '/reports',
        icon: 'fas fa-file-alt'
    },
    {
        id: 'users',
        title: 'User Management',
        description: 'Manage system users',
        url: '/users',
        icon: 'fas fa-users'
    },
    {
        id: 'settings',
        title: 'Settings',
        description: 'System configuration and preferences',
        url: '/settings',
        icon: 'fas fa-cog'
    },
    {
        id: 'profile',
        title: 'User Profile',
        description: 'Edit user profile information',
        url: '/profile',
        icon: 'fas fa-user'
    },
    {
        id: 'notifications',
        title: 'Notifications',
        description: 'View and manage notifications',
        url: '/notifications',
        icon: 'fas fa-bell'
    },
    {
        id: 'calendar',
        title: 'Calendar',
        description: 'Schedule and event management',
        url: '/calendar',
        icon: 'fas fa-calendar'
    },
    {
        id: 'tasks',
        title: 'Task Management',
        description: 'Create and track tasks',
        url: '/tasks',
        icon: 'fas fa-tasks'
    },
    {
        id: 'projects',
        title: 'Projects',
        description: 'Project management and tracking',
        url: '/projects',
        icon: 'fas fa-project-diagram'
    },
    {
        id: 'files',
        title: 'File Manager',
        description: 'Upload and manage files',
        url: '/files',
        icon: 'fas fa-folder'
    },
    {
        id: 'messages',
        title: 'Messages',
        description: 'Internal messaging system',
        url: '/messages',
        icon: 'fas fa-envelope'
    },
    {
        id: 'help',
        title: 'Help & Support',
        description: 'Documentation and support',
        url: '/help',
        icon: 'fas fa-question-circle'
    },
    {
        id: 'billing',
        title: 'Billing',
        description: 'Billing and subscription management',
        url: '/billing',
        icon: 'fas fa-credit-card'
    },
    {
        id: 'integrations',
        title: 'Integrations',
        description: 'Third-party integrations',
        url: '/integrations',
        icon: 'fas fa-plug'
    },
    {
        id: 'audit-logs',
        title: 'Audit Logs',
        description: 'System audit and activity logs',
        url: '/audit-logs',
        icon: 'fas fa-history'
    },
    {
        id: 'backup',
        title: 'Backup & Restore',
        description: 'Data backup and restoration',
        url: '/backup',
        icon: 'fas fa-database'
    },
    {
        id: 'security',
        title: 'Security Settings',
        description: 'Security configuration and monitoring',
        url: '/security',
        icon: 'fas fa-shield-alt'
    },
    {
        id: 'api-docs',
        title: 'API Documentation',
        description: 'API reference and documentation',
        url: '/api-docs',
        icon: 'fas fa-code'
    },
    {
        id: 'system-status',
        title: 'System Status',
        description: 'System health and status monitoring',
        url: '/system-status',
        icon: 'fas fa-heartbeat'
    }
];

// Available user roles
const USER_ROLES = [
    {
        id: 'admin',
        name: 'Admin',
        description: 'Full system access',
        color: '#dc3545'
    },
    {
        id: 'manager',
        name: 'Manager',
        description: 'Management level access',
        color: '#fd7e14'
    },
    {
        id: 'user',
        name: 'User',
        description: 'Standard user access',
        color: '#198754'
    },
    {
        id: 'guest',
        name: 'Guest',
        description: 'Limited read-only access',
        color: '#6c757d'
    }
];

// Default menu structure (can be used as sample data)
const DEFAULT_MENU_STRUCTURE = [
    {
        id: 'main-dashboard',
        type: 'category',
        title: 'Dashboard',
        icon: 'cp-dashboard',
        roles: ['admin', 'manager', 'user'],
        children: [
            {
                id: 'overview',
                type: 'page',
                title: 'Overview',
                pageId: 'dashboard',
                icon: 'cp-foureye-approval',
                roles: ['admin', 'manager', 'user']
            },
            {
                id: 'analytics-sub',
                type: 'page',
                title: 'Analytics',
                pageId: 'analytics',
                icon: 'cp-analytics',
                roles: ['admin', 'manager']
            }
        ]
    },
    {
        id: 'content-management',
        type: 'category',
        title: 'Content Management',
        icon: 'cp-drift-manage',
        roles: ['admin', 'manager'],
        children: [
            {
                id: 'projects-sub',
                type: 'page',
                title: 'Projects',
                pageId: 'projects',
                icon: 'cp-solution-type',
                roles: ['admin', 'manager']
            },
            {
                id: 'tasks-sub',
                type: 'page',
                title: 'Tasks',
                pageId: 'tasks',
                icon: 'cp-form-list',
                roles: ['admin', 'manager', 'user']
            },
            {
                id: 'files-sub',
                type: 'page',
                title: 'Files',
                pageId: 'files',
                icon: 'cp-folder-file',
                roles: ['admin', 'manager', 'user']
            }
        ]
    },
    {
        id: 'administration',
        type: 'category',
        title: 'Administration',
        icon: 'cp-super-admin',
        roles: ['admin'],
        children: [
            {
                id: 'user-management',
                type: 'subcategory',
                title: 'User Management',
                icon: 'cp-user',
                roles: ['admin'],
                children: [
                    {
                        id: 'users-list',
                        type: 'page',
                        title: 'All Users',
                        pageId: 'users',
                        icon: 'cp-user',
                        roles: ['admin']
                    },
                    {
                        id: 'user-profile-mgmt',
                        type: 'page',
                        title: 'User Profiles',
                        pageId: 'profile',
                        icon: 'cp-user',
                        roles: ['admin']
                    },
                    {
                        id: 'user-notifications',
                        type: 'page',
                        title: 'User Notifications',
                        pageId: 'notifications',
                        icon: 'cp-notification-manager-1',
                        roles: ['admin']
                    }
                ]
            },
            {
                id: 'system-management',
                type: 'subcategory',
                title: 'System Management',
                icon: 'cp-configuration-setting',
                roles: ['admin'],
                children: [
                    {
                        id: 'system-settings',
                        type: 'page',
                        title: 'System Settings',
                        pageId: 'settings',
                        icon: 'cp-chain-settings',
                        roles: ['admin']
                    },
                    {
                        id: 'security-settings',
                        type: 'page',
                        title: 'Security Settings',
                        pageId: 'security',
                        icon: 'cp-security',
                        roles: ['admin']
                    },
                    {
                        id: 'backup-settings',
                        type: 'page',
                        title: 'Backup & Restore',
                        pageId: 'backup',
                        icon: 'cp-backup_data',
                        roles: ['admin']
                    }
                ]
            },
            {
                id: 'integrations-admin',
                type: 'page',
                title: 'Integrations',
                pageId: 'integrations',
                icon: 'cp-administrator',
                roles: ['admin']
            }
        ]
    }
];

// Helper functions for data manipulation
const DataHelpers = {
    // Find page by ID
    findPageById: function (pageId) {
        return AVAILABLE_PAGES.find(page => page.id === pageId);
    },

    // Find role by ID
    findRoleById: function (roleId) {
        return USER_ROLES.find(role => role.id === roleId);
    },

    // Search pages by title
    searchPages: function (query) {
        if (!query) return AVAILABLE_PAGES;

        const lowerQuery = query.toLowerCase();
        return AVAILABLE_PAGES.filter(page =>
            page.title.toLowerCase().includes(lowerQuery) ||
            page.description.toLowerCase().includes(lowerQuery)
        );
    },

    // Generate unique ID
    generateId: function () {
        return 'menu-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    },

    // Validate menu item data
    validateMenuItem: function (item) {
        const errors = [];

        if (!item.title || item.title.trim() === '') {
            errors.push('Title is required');
        }

        if (!item.type || !['category', 'subcategory', 'page'].includes(item.type)) {
            errors.push('Valid menu type is required');
        }

        if (item.type === 'page' && !item.pageId) {
            errors.push('Page selection is required for page links');
        }

        if (!item.roles || item.roles.length === 0) {
            errors.push('At least one role must be assigned');
        }

        return errors;
    }
};
