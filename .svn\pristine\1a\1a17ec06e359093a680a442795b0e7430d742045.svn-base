﻿using ContinuityPatrol.Application.Features.RiskMitigation.Commands.Create;
using ContinuityPatrol.Application.Features.RiskMitigation.Commands.Update;
using ContinuityPatrol.Application.Features.RiskMitigation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.RiskMitigation.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RiskMitigationModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Dashboard;

public class RiskMitigationService : BaseClient, IRiskMitigationService
{
    public RiskMitigationService(IConfiguration config, IAppCache cache, ILogger<RiskMitigationService> logger) : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateRiskMitigationCommand createRiskMitigationCommand)
    {
        var request = new RestRequest("api/v6/riskmitigation", Method.Post);

        request.AddJsonBody(createRiskMitigationCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateRiskMitigationCommand updateRiskMitigationCommand)
    {
        var request = new RestRequest("api/v6/riskmitigation", Method.Put);

        request.AddJsonBody(updateRiskMitigationCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string riskMitigationId)
    {
        var request = new RestRequest($"api/v6/riskmitigation/{riskMitigationId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<List<RiskMitigationListVm>> GetRiskMitigationLists()
    {
        var request = new RestRequest("api/v6/riskmitigation");

        return await GetFromCache<List<RiskMitigationListVm>>(request, "GetRiskMitigationLists");
    }

    public async Task<RiskMitigationDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/riskmitigation/{id}");

        return await Get<RiskMitigationDetailVm>(request);
    }

    public async Task<PaginatedResult<RiskMitigationListVm>> GetPaginatedRiskMitigation(GetRiskMitigationPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/riskmitigation/paginated-list");

        return await GetFromCache<PaginatedResult<RiskMitigationListVm>>(request, "GetPaginatedRiskMitigation");
    }
}