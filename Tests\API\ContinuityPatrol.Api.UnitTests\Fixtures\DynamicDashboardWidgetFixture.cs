using AutoFixture;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardWidgetModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DynamicDashboardWidgetFixture
{
    public CreateDynamicDashboardWidgetCommand CreateDynamicDashboardWidgetCommand { get; set; }
    public CreateDynamicDashboardWidgetResponse CreateDynamicDashboardWidgetResponse { get; set; }
    public UpdateDynamicDashboardWidgetCommand UpdateDynamicDashboardWidgetCommand { get; set; }
    public UpdateDynamicDashboardWidgetResponse UpdateDynamicDashboardWidgetResponse { get; set; }
    public DeleteDynamicDashboardWidgetCommand DeleteDynamicDashboardWidgetCommand { get; set; }
    public DeleteDynamicDashboardWidgetResponse DeleteDynamicDashboardWidgetResponse { get; set; }
    public GetDynamicDashboardWidgetDetailQuery GetDynamicDashboardWidgetDetailQuery { get; set; }
    public DynamicDashboardWidgetDetailVm DynamicDashboardWidgetDetailVm { get; set; }
    public GetDynamicDashboardWidgetListQuery GetDynamicDashboardWidgetListQuery { get; set; }
    public List<DynamicDashboardWidgetListVm> DynamicDashboardWidgetListVm { get; set; }
    public GetDynamicDashboardWidgetNameUniqueQuery GetDynamicDashboardWidgetNameUniqueQuery { get; set; }
    public GetDynamicDashboardWidgetPaginatedListQuery GetDynamicDashboardWidgetPaginatedListQuery { get; set; }
    public PaginatedResult<DynamicDashboardWidgetListVm> DynamicDashboardWidgetPaginatedResult { get; set; }

    public DynamicDashboardWidgetFixture()
    {
        var fixture = new Fixture();

        // Configure fixture to handle circular references
        fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
            .ForEach(b => fixture.Behaviors.Remove(b));
        fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        // Create Commands
        CreateDynamicDashboardWidgetCommand = fixture.Create<CreateDynamicDashboardWidgetCommand>();
        UpdateDynamicDashboardWidgetCommand = fixture.Create<UpdateDynamicDashboardWidgetCommand>();
        DeleteDynamicDashboardWidgetCommand = fixture.Create<DeleteDynamicDashboardWidgetCommand>();

        // Create Responses
        fixture.Customize<CreateDynamicDashboardWidgetResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Success, true)
            .With(b => b.Message, "DynamicDashboardWidget created successfully"));
        CreateDynamicDashboardWidgetResponse = fixture.Create<CreateDynamicDashboardWidgetResponse>();

        fixture.Customize<UpdateDynamicDashboardWidgetResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Success, true)
            .With(b => b.Message, "DynamicDashboardWidget updated successfully"));
        UpdateDynamicDashboardWidgetResponse = fixture.Create<UpdateDynamicDashboardWidgetResponse>();

        fixture.Customize<DeleteDynamicDashboardWidgetResponse>(c => c
            .With(b => b.Success, true)
            .With(b => b.IsActive, false)
            .With(b => b.Message, "DynamicDashboardWidget deleted successfully"));
        DeleteDynamicDashboardWidgetResponse = fixture.Create<DeleteDynamicDashboardWidgetResponse>();

        // Create Queries
        GetDynamicDashboardWidgetDetailQuery = fixture.Create<GetDynamicDashboardWidgetDetailQuery>();
        GetDynamicDashboardWidgetListQuery = fixture.Create<GetDynamicDashboardWidgetListQuery>();
        GetDynamicDashboardWidgetNameUniqueQuery = fixture.Create<GetDynamicDashboardWidgetNameUniqueQuery>();
        GetDynamicDashboardWidgetPaginatedListQuery = fixture.Create<GetDynamicDashboardWidgetPaginatedListQuery>();

        // Create ViewModels
        fixture.Customize<DynamicDashboardWidgetDetailVm>(c => c
            .With(b => b.ReferenceId, Guid.NewGuid().ToString)
            .With(b => b.Name, "Enterprise Dashboard Widget")
            .With(b => b.Properties, "{\"type\":\"chart\",\"config\":{\"title\":\"Performance Metrics\"}}")
            .With(b => b.IsActive, true));
        DynamicDashboardWidgetDetailVm = fixture.Create<DynamicDashboardWidgetDetailVm>();

        fixture.Customize<DynamicDashboardWidgetListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Name, "Enterprise Widget")
            .With(b => b.Properties, "{\"type\":\"chart\",\"config\":{\"title\":\"Widget\"}}")
        );
        DynamicDashboardWidgetListVm = fixture.CreateMany<DynamicDashboardWidgetListVm>(5).ToList();

        // Create PaginatedResult using the Success factory method
        DynamicDashboardWidgetPaginatedResult = PaginatedResult<DynamicDashboardWidgetListVm>.Success(
            data: DynamicDashboardWidgetListVm,
            count: DynamicDashboardWidgetListVm.Count,
            page: 1,
            pageSize: 10
        );
    }
}
