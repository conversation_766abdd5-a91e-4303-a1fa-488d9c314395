using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Delete;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetByOperationId;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetList;
//using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetNameUnique;
//using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationGroupModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class BulkImportOperationGroupService : BaseService,IBulkImportOperationGroupService
{
    public BulkImportOperationGroupService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<BulkImportOperationGroupListVm>> GetBulkImportOperationGroupList()
    {
        Logger.LogDebug("Get All BulkImportOperationGroups");

        return await Mediator.Send(new GetBulkImportOperationGroupListQuery());
    }

    public async Task<BulkImportOperationGroupDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BulkImportOperationGroup Id");

        Logger.LogDebug($"Get BulkImportOperationGroup Detail by Id '{id}'");

        return await Mediator.Send(new GetBulkImportOperationGroupDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateBulkImportOperationGroupCommand createBulkImportOperationGroupCommand)
    {
        Logger.LogDebug($"Create BulkImportOperationGroup '{createBulkImportOperationGroupCommand}'");

        return await Mediator.Send(createBulkImportOperationGroupCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateBulkImportOperationGroupCommand updateBulkImportOperationGroupCommand)
    {
        Logger.LogDebug($"Update BulkImportOperationGroup '{updateBulkImportOperationGroupCommand}'");

        return await Mediator.Send(updateBulkImportOperationGroupCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BulkImportOperationGroup Id");

        Logger.LogDebug($"Delete BulkImportOperationGroup Details by Id '{id}'");

        return await Mediator.Send(new DeleteBulkImportOperationGroupCommand { Id = id });
    }

    public async Task<List<BulkImportOperationGroupListVm>> GetBulkImportOperationGroupByOperationId(string operationId)
    {
        Guard.Against.InvalidGuidOrEmpty(operationId, "BulkImportOperation Id");

        Logger.LogDebug($"Get BulkImportOperationGroup Detail by OperationId '{operationId}'");

        return await Mediator.Send(new GetByOperationIdQuery { BulkImportOperationId = operationId });
    }

    #region NameExist
    // public async Task<bool> IsBulkImportOperationGroupNameExist(string name, string? id)
    // {
    //     Guard.Against.NullOrWhiteSpace(name, "BulkImportOperationGroup Name");
    //
    //     Logger.LogDebug($"Check Name Exists Detail by BulkImportOperationGroup Name '{name}' and Id '{id}'");
    //
    //     return await Mediator.Send(new GetBulkImportOperationGroupNameUniqueQuery { Name = name, Id = id });
    // }
    #endregion

    #region Paginated
    //public async Task<PaginatedResult<BulkImportOperationGroupListVm>> GetPaginatedBulkImportOperationGroups(GetBulkImportOperationGroupPaginatedListQuery query)
    //{
    //    Logger.LogDebug("Get Searching Details in BulkImportOperationGroup Paginated List");
    //
    //    return await Mediator.Send(query);
    //}
    #endregion
}
