﻿let LPassword = "";
let blurPassword = "";
let blurcurrentPassword = "";

async function onfocusPassword(encryptedPassword) {
    if (encryptedPassword?.length > 30) {
        try {
            return await DecryptPassword(encryptedPassword);
        } catch (error) {
            errorNotification(error);
        }
    }
    return null;
}

$(document).on('mouseover', '.cp-password-hide, .cp-password-visible', function () {
    const titleText = $(this).hasClass('cp-password-hide') ? 'Hide Password' : 'Show Password';
    $(this).attr('title', titleText);
});

function showPassword(input, icon) {
    input.attr("type", "text");
    icon.removeClass("cp-password-visible").addClass("cp-password-hide").attr("title", "Hide Password");
}

function hidePassword(input, icon) {
    input.attr("type", "password");
    icon.removeClass("cp-password-hide").addClass("cp-password-visible").attr("title", "Show Password");
}

async function inputpassword(id, value) {
    const actions = {
        userPassword: () => validatePassword(value, 'User'),
        workflowPassword: () => validatePassword(value, 'Workflow'),
        changeNewPassword: () => validatePassword(value, 'change'),
        Password: () => validatePassword(value, 'Workflowpassword'),
        CurrentPassword: () => validateCurrentPassword(value)
    };

    const action = actions[id];
    if (typeof action === "function") {
        await action();
    }
}

async function validateCurrentPassword(value) {
    const errorElement = $('#OldPassword-error');
    const newPasswordElement = $("#changeNewPassword");

    if (!value?.trim()) {
        errorElement.text('Enter current password').addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
    }

    const storedEncryptedCurrent = $("#CurrentPassword").val();
    const storedEncryptedNew = newPasswordElement.val();

    const decryptedCurrent = await onfocusPassword(storedEncryptedCurrent);
    const decryptedNew = await onfocusPassword(storedEncryptedNew);
    if (decryptedCurrent === value || decryptedNew === value) {
        $('#NewPassword-error').text('Same password already exists').addClass('field-validation-error');
        return false;
    } else {
        $('#NewPassword-error').text('').removeClass('field-validation-error');
    }

    return true;
}

async function inputConfirmpassword(id, value) {
    if (!LPassword?.trim()) {
        return false;
    }

    const errorElement = (
        id === 'userConfirmPassword' || id === 'changeConfirmPassword' || id === 'ConfirmPassword')
        ? $('#ConfirmPassword-error')
        : $('#Confirmpassword-error');
    return await validateConfirmPassword(value, errorElement);
}
async function blurpassword(id, password) {
    if (!password?.trim() || password.length >= 64) {
        $('#encriptedPassword').val("");
        return;
    }

    try {
        const $input = $(`#${id}`);
        const loginName = ($("#textLoginName").val() || $("#LoginName").data("loginnames") || "").toLowerCase();
        let blurPassword;

        if (['userPassword', 'userConfirmPassword', 'CurrentPassword', 'changeConfirmPassword'].includes(id)) {
            blurPassword = await EncryptPassword(loginName + password);
            const tempPassword = await EncryptPassword(password);
            $('#encriptedPassword').val(tempPassword);

        } else if (id === "changeNewPassword") {
            const userId = $("#loginId").data("loginid");
            const newPassword = $("#changeNewPassword").val();

            const isAllowed = await $.ajax({
                url: RootUrl + "Admin/User/IsNewPasswordInLastFive",
                type: "POST",
                dataType: "json",
                data: {
                    UserId: userId,
                    NewPassword: loginName + newPassword
                }
            });

            if (!isAllowed) {
                $('#NewPassword-error')
                    .text('Please try with a different password, Last five passwords cannot be reused.')
                    .addClass('field-validation-error');
                return;
            }

            $('#NewPassword-error').text('').removeClass('field-validation-error');
            blurPassword = await EncryptPassword(loginName + password);

        } else {
            blurPassword = await EncryptPassword(password);
        }

        $input.val(blurPassword).attr('type', 'password');
        $input.siblings('.toggle-password').find('i')
            .removeClass('fs-6')
            .addClass('cp-password-visible fs-6');

    } catch (error) {
        errorNotification(error)
    }
}
async function focuspassword(id) {
    const $input = $(`#${id}`);
    const encryptedPassword = $input.val();
    if (!encryptedPassword || encryptedPassword.length <= 30) return;
    let loginName = "";
    if (id === 'userPassword') {
        loginName = $('#textLoginName').val() || "";
    } else if (id === 'changeNewPassword' || id === 'CurrentPassword') {
        loginName = $("#LoginName").data("loginnames") || "";
    }
    const decrypted = await onfocusPassword(encryptedPassword);
    if (!decrypted) return;

    const finalValue = loginName ? decrypted.substring(loginName.length) : decrypted;
    $input.val(finalValue);
}


function getPasswordSourceId(confirmId) {
    const map = {
        userConfirmPassword: '#userPassword',
        workflowConfirmPassword: '#workflowPassword',
        ConfirmPassword: '#Password',
        changeConfirmPassword: '#changeNewPassword'
    };
    return map[confirmId] || null;
}

function getLoginNameForConfirmId(confirmId) {
    if (confirmId === 'userConfirmPassword') {
        return $('#textLoginName').val() || "";
    }
    if (confirmId === 'changeConfirmPassword') {
        return $("#LoginName").data("loginnames") || "";
    }
    return "";
}

async function decryptPasswordAndStripPrefix(encrypted, confirmId) {
    if (!encrypted || encrypted.length <= 30) return encrypted;

    const decrypted = await onfocusPassword(encrypted);
    const loginName = getLoginNameForConfirmId(confirmId);

    return loginName ? decrypted?.substring(loginName.length) : decrypted;
}

async function focusconfirmpassword(id) {
    const sourcePasswordSelector = getPasswordSourceId(id);
    if (!sourcePasswordSelector) return;

    const rawPassword = $(sourcePasswordSelector).val();
    if (!rawPassword) {
        LPassword = "";
        return false;
    }
    LPassword = rawPassword.length < 60
        ? rawPassword
        : await DecryptPassword(rawPassword);

    const $input = $(`#${id}`);
    const encryptedConfirm = $input.val();
    const finalValue = await decryptPasswordAndStripPrefix(encryptedConfirm, id);

    $input.val(finalValue);
}

async function validateConfirmPassword(value, errorElement) {
    if (!value?.trim()) {
        errorElement.text('Enter confirm password')
            .addClass('field-validation-error');
        return false;
    }

    const isMatch = await confirmationPassword(value, errorElement);
    if (!isMatch) {
        errorElement.text('Password does not match')
            .addClass('field-validation-error');
        return false;
    }

    return await CommonValidation(errorElement, [isMatch]);
}

function getLoginNamePrefix() {
    return ($("#textLoginName").val() || $("#LoginName").data("loginnames") || "").toLowerCase();
}


async function confirmationPassword(confirmPassword, errorElement) {
    const isMainPasswordField = errorElement.is($('#ConfirmPassword-error'));

    let passwordValue = isMainPasswordField
        ? ($('#userPassword').val() || $('#changeNewPassword').val() || $('#Password').val())
        : $('#workflowPassword').val();

    if (!passwordValue) return false;

    const isEncrypted = confirmPassword.length >= 60;

    const decryptedPassword = await DecryptPassword(passwordValue);
    const decryptedConfirmPassword = isEncrypted
        ? await DecryptPassword(
            isMainPasswordField
                ? ($('#userConfirmPassword').val() || $('#changeConfirmPassword').val())
                : $('#workflowConfirmPassword').val()
        )
        : isMainPasswordField
            ? (getLoginNamePrefix() + confirmPassword)
            : confirmPassword;

    const match = decryptedPassword === decryptedConfirmPassword;

    if (!match) {
        errorElement.text('Password does not match').addClass('field-validation-error');
    } else {
        errorElement.text('').removeClass('field-validation-error');
    }

    return match;
}

async function validateLoginPassword(value, errorElement) {
    if (!value?.trim()) {
        errorElement.text('Enter password')
            .addClass('field-validation-error');
        return false;
    }
    const plainPassword = value.length > 60 ? await DecryptPassword(value)
        : value;
    return await PasswordPolicy(plainPassword, errorElement);
}

async function validatePassword(value, type) {
    const errorSelectorMap = {
        User: '#LoginPassword-error',
        Workflow: '#Password-error',
        Workflowpassword: '#NewPassword-error',
        change: '#NewPassword-error'
    };

    const errorElement = $(errorSelectorMap[type] || '#NewPassword-error');
    const disableSave = type === 'Workflow' || type === 'change';

    if (!value?.trim()) {
        errorElement.text(type === 'User' ? 'Enter password' : 'Enter new password')
            .addClass('field-validation-error');
        if (disableSave) $("#SaveFunction").prop('disabled', true);
        return false;
    }
    if (value.length > 60) {
        value = await DecryptPassword(value);
    }
    if (type === 'Workflow' && value === $("#CurrentPassword").val()) {
        errorElement.text('Same password already exists')
            .addClass('field-validation-error');
        return false;
    }
    try {
        const isValid = await PasswordPolicy(value, errorElement);
        if (disableSave) $("#SaveFunction").prop('disabled', !isValid);
        return isValid;
    } catch (error) {
        if (disableSave) $("#SaveFunction").prop('disabled', true);
        console.error("Password policy validation failed:", error);
        return false;
    }
}


////Passwword Policy

async function PasswordPolicy(value, errorElement) {
    const endpoint = `${RootUrl}Admin/Settings/GetList`;
    const defaultKey = "Password Policy";

    try {
        const response = await $.ajax({ type: "GET", url: endpoint });

        if (!Array.isArray(response) || response.length === 0) {
            return settingPassword(value);
        }
        const policy = response.find(item => item.sKey === defaultKey);
        if (!policy?.sValue) {
            return settingPassword(value);
        }
        const rules = JSON.parse(policy.sValue);
        const { minSValue, maxSValue, minUpSValue, minNumSValue, minLowSValue, minSpclSValue } = rules;
        if (value === "Password_strength") {
            return { minSValue, maxSValue, minUpSValue, minNumSValue, minLowSValue, minSpclSValue };
        }
        return validatePolicyPassword(
            value,
            errorElement,
            minSValue,
            minUpSValue,
            minNumSValue,
            minLowSValue,
            minSpclSValue,
            maxSValue
        );
    } catch (error) {
        errorNotification(error)
        return "Error fetching password policy";
    }
}

function validatePolicyPassword(value, errorElement, minSValue, minUpSValue, minNumSValue, minLowSValue, minSpclSValue, maxSValue) {
    const counts = {
        upper: (value.match(/[A-Z]/g) || []).length,
        lower: (value.match(/[a-z]/g) || []).length,
        digit: (value.match(/[0-9]/g) || []).length,
        special: (value.match(/[^a-zA-Z0-9]/g) || []).length,
        total: value.length
    };
    const rules = [
        { valid: counts.total >= parseInt(minSValue), message: `Password should contain at least ${minSValue} characters` },
        { valid: counts.upper >= parseInt(minUpSValue), message: `Password should contain at least ${minUpSValue} uppercase character(s)` },
        { valid: counts.digit >= parseInt(minNumSValue), message: `Password should contain at least ${minNumSValue} numeric character(s)` },
        { valid: counts.lower >= parseInt(minLowSValue), message: `Password should contain at least ${minLowSValue} lowercase character(s)` },
        { valid: counts.special >= parseInt(minSpclSValue), message: `Password should contain at least ${minSpclSValue} special character(s)` },
        { valid: counts.total <= parseInt(maxSValue), message: `Password must be no more than ${maxSValue} characters` }
    ];
    for (const rule of rules) {
        if (!rule.valid) {
            errorElement.text(rule.message).addClass('field-validation-error');
            return false;
        }
    }

    errorElement.text('').removeClass('field-validation-error');
    return true;
}

function settingPassword(value) {
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{8,15}$/;
    const errorElement = $('#LoginPassword-error');

    const validators = [
        { test: val => val.length >= 8, message: "Password must be at least 8 characters" },
        { test: val => /\d/.test(val), message: "Password must contain at least one number" },
        { test: val => /[!@#$%^&*]/.test(val), message: "Password must contain at least one symbol" },
        { test: val => /[A-Z]/.test(val), message: "Password must contain at least one uppercase letter" },
        { test: val => /[a-z]/.test(val), message: "Password must contain at least one lowercase letter" },
        { test: val => passwordRegex.test(val), message: "Invalid password" }
    ];

    for (const { test, message } of validators) {
        if (!test(value)) {
            errorElement.text(message).addClass('field-validation-error');
            return false;
        }
    }

    errorElement.text('').removeClass('field-validation-error');
    return true;
}

function debouncePasswordStrength(func, delay = 300) {
    let timer;
    return function (...args) {
        clearTimeout(timer);
        timer = setTimeout(() => func.apply(this, args), delay);
    };
}

//-----------
const PASSWORD_DESCRIPTIONS = ["Too short", "Weak", "Good", "Strong", "Best"];

function updateStrengthUI({ lengthValid, numValid, caseValid, specialValid, score }) {
    toggleValidation("#length", lengthValid);
    toggleValidation("#pnum", numValid);
    toggleValidation("#capital", caseValid);
    toggleValidation("#spchar", specialValid);

    $("#passwordDescription").text("Password Strength: " + (PASSWORD_DESCRIPTIONS[score] || "Best"));
    $("#passwordStrength").removeClass().addClass("strength" + score);
    $("#mycPass_strength_wrap").fadeIn(400);
}

function toggleValidation(selector, isValid) {
    $(selector).removeClass(isValid ? "invalid" : "valid")
        .addClass(isValid ? "valid" : "invalid");
}

function bindPasswordStrengthEvents() {
    $("input#changeNewPassword").on("blur", function () {
        $("#mycPass_strength_wrap").fadeOut(400);
        blurpassword(this.id, this.value.replace(/\s+/g, ''));
    });

    $("input#changeConfirmPassword").on("focus keyup", function () {
        $("#mycPass_strength_wrap").fadeOut(400);
        focusconfirmpassword(this.id);
    });
}

function evaluatePasswordStrength(value, policy = null) {
    let score = 0;
    const upperCount = (value.match(/[A-Z]/g) || []).length;
    const lowerCount = (value.match(/[a-z]/g) || []).length;
    const digitCount = (value.match(/\d/g) || []).length;
    const specialCount = (value.match(/[^a-zA-Z0-9]/g) || []).length;

    const minLen = policy?.minSValue || 8;
    const maxLen = policy?.maxSValue || 15;
    const minUpper = policy?.minUpSValue || 1;
    const minLower = policy?.minLowSValue || 1;
    const minDigit = policy?.minNumSValue || 1;
    const minSpecial = policy?.minSpclSValue || 1;

    const isLengthValid = value.length >= minLen && value.length <= maxLen;
    const isNumValid = digitCount >= minDigit;
    const isCaseValid = upperCount >= minUpper && lowerCount >= minLower;
    const isSpecialValid = specialCount >= minSpecial;

    score += isLengthValid ? 1 : 0;
    score += isNumValid ? 1 : 0;
    score += isCaseValid ? 1 : 0;
    score += isSpecialValid ? 1 : 0;

    updateStrengthUI({
        lengthValid: isLengthValid,
        numValid: isNumValid,
        caseValid: isCaseValid,
        specialValid: isSpecialValid,
        score
    });
}


$(function () {
    $("input#changeNewPassword").on("keyup", debouncePasswordStrength(async function () {
        const value = $(this).val();
        try {
            const policy = await PasswordPolicy("Password_strength");

            // Update UI text based on policy
            if (!policy || Object.values(policy).some(v => v === 0)) {
                $("#length").text("At least 8 characters");
                $("#pnum").text("At least 1 number");
                $("#capital").text("At least 1 lowercase & 1 uppercase letter");
                $("#spchar").text("At least 1 special character");
            } else {
                $("#length").text(`Between ${policy.minSValue} to ${policy.maxSValue} characters`);
                $("#pnum").text(`At least ${policy.minNumSValue} number(s)`);
                $("#capital").text(`At least ${policy.minLowSValue} lowercase & ${policy.minUpSValue} uppercase letter(s)`);
                $("#spchar").text(`At least ${policy.minSpclSValue} special character(s)`);
            }

            evaluatePasswordStrength(value, policy);
            bindPasswordStrengthEvents();

        } catch (error) {
            console.error("Password strength evaluation failed:", error);
        }
    }, 500));
});