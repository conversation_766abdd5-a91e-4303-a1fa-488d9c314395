﻿@using ContinuityPatrol.Web.Areas.Report.Controllers;
@{
    ViewData["Title"] = "RPOSLAReport";
}

@using DevExpress.AspNetCore
@using ContinuityPatrol.Web.Areas.Report.ReportTemplate

<style>
    .form-group {
        margin-left: 550px;
        margin-top: 60px;
    }
</style>
@section Styles
    {
    <link href="~/css/viewer.part.bundle.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/thirdparty.bundle.css" rel="stylesheet" asp-append-version="true" />
    }
@section HeaderScripts
    {
    <script src="~/js/thirdparty.bundle.js" asp-append-version="true"></script>
    <script src="~/js/viewer.part.bundle.js" asp-append-version="true"></script>
   }

<div class="card card-custom gutter-b">
    <div class="card-body p-0">
        <div id="reportContainer">
            @{
                var reportData = ViewData["RPOSLAReportData"]?.ToString();
                var reportDataObj = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(reportData);
                string reportType = reportDataObj.InfraObjectType;
                if (reportType.ToLower().Equals("oracle"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLAODG(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.ToLower().Equals("mssqlalwayson"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLAMssqlAlwaysOn(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.ToLower().Equals("oraclerac"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLAOracleRac(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.ToLower().Equals("postgres"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLAPostgresSQL(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.ToLower().Equals("mysql"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLAMySQLReport(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.ToLower().Equals("mongodb"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLAMongoDBReport(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.ToLower().Equals("db2hadr"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLADB2HADRReport(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.ToLower().Equals("mssqldbmirroring"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLAMssqlDBMirroringReport(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.ToLower().Equals("mssqlnls"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLAMssql2k19(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.ToLower().Equals("robocopy"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLARoboCopyReport(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.Trim().ToLower().Equals("rsyncappreplication") || reportType.Trim().ToLower().Equals("rsyncdbreplication"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLARSyncReport(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.Trim().ToLower().Equals("srm"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLASRMReport(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.Trim().ToLower().Equals("azurestorageaccount"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLAAzureStorageAccountReport(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.Trim().ToLower().Equals("activedirectory") || reportType.Trim().ToLower().Equals("windowsactivedirectory"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLAActiveDirectory(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.Trim().ToLower().Equals("datasyncappreplication"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLADataSyncReport(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.ToLower().Trim().Equals("zertovpg"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLAZertoVPGReport(ViewData["RPOSLAReportData"].ToString()))
                }
                else if (reportType.ToLower().Trim().Equals("sybasershadr"))
                {
                    @Html.DevExpress().WebDocumentViewer("RPOSLADocumentViewer").Height("1150px").Bind(new RPOSLASybaseRSHADRReport(ViewData["RPOSLAReportData"].ToString()))
                }
            }
            

        </div>
    </div>
</div>

@*<div class="row mt-3">
    <div class="col">
        <img src="/img/logo/cplogo.svg" height="35" />
    </div>
    <div class="col text-end">
        <img src="/img/logo/pts_logo.png" height="35" />
    </div>
    <div class="col-12">
        <div class="bg-secondary rounded-0 text-light mt-1">
            <h6 class="Report-Header text-center">
                RPO SLA Report
            </h6>
        </div>
    </div>
</div>
<div class="card">
    <div>
        <div class="mt-3">
            <div class="rounded card bg-light">
                <div class="p-0 card-body">
                    <div class="rounded-0 list-group">

                        <li class="d-flex justify-content-between  py-2 border-0 list-group-item">
                            <div class="d-flex align-items-center">
                                <span class="ms-1 align-middle text-dark d-flex">
                                    <span class="badge bg-dark status_warning"></span>&nbsp;
                                    Not Available
                                </span>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="ms-1 align-middle d-flex">
                                    <span class="badge bg-success status_warning"></span>&nbsp;
                                    DataLag &lt;= 00:20:00Hours
                                </span>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="ms-1 align-middle d-flex">
                                    <span class="badge bg-danger status_warning"></span>&nbsp;
                                    DataLag > 00:20:00 Hours
                                </span>
                            </div>
                        </li>


                    </div>
                </div>
            </div>

        </div>



        <div class="mt-3 prebuildreportcustom">
            <div>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="bg-light">
                            <tr>
                                <th class="text-center">Sr.No</th>
                                <th>Production Archive Log Sequence</th>
                                <th>Archive Log Applied at DR</th>
                                <th>Data Lag</th>
                                <th>TimeStamp</th>
                            </tr>
                        </thead>
                        <tbody id="tableDataone">
                            <tr>
                                <td class="text-center">1</td>
                                <td>1026 Thread(#1)</td>
                                <td>1022 Thread(#1)</td>
                                <td class="text-danger">2.22:20:24</td>
                                <td>20-02-2023 11:30:36</td>
                            </tr>
                            <tr>
                                <td class="text-center">2</td>
                                <td>1026 Thread(#1)</td>
                                <td>1022 Thread(#1)</td>
                                <td class="text-success">00.00:00:00</td>
                                <td>20-02-2023 11:30:36</td>
                            </tr>
                            <tr>
                                <td class="text-center">3</td>
                                <td>1026 Thread(#1)</td>
                                <td>1022 Thread(#1)</td>
                                <td class="text-danger">2.22:20:24</td>
                                <td>20-02-2023 11:30:36</td>
                            </tr>
                            <tr>
                                <td class="text-center">4</td>
                                <td>1026 Thread(#1)</td>
                                <td>1022 Thread(#1)</td>
                                <td class="text-success">00.00:00:00</td>
                                <td>20-02-2023 11:30:36</td>
                            </tr>
                            <tr>
                                <td class="text-center">5</td>
                                <td>1026 Thread(#1)</td>
                                <td>1022 Thread(#1)</td>
                                <td class="text-danger">2.22:20:24</td>
                                <td>20-02-2023 11:30:36</td>
                            </tr>
                            <tr>
                                <td class="text-center">6</td>
                                <td>1026 Thread(#1)</td>
                                <td>1022 Thread(#1)</td>
                                <td class="text-success">00.00:00:00</td>
                                <td>20-02-2023 11:30:36</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
*@
