using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetDescriptionBulkImportStartAndEndTime;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperation.Queries;

public class GetDescriptionBulkImportStartAndEndTimeQueryTests : IClassFixture<BulkImportOperationFixture>
{
    private readonly BulkImportOperationFixture _bulkImportOperationFixture;
    private readonly Mock<IBulkImportOperationRepository> _mockBulkImportOperationRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetDescriptionBulkImportStartAndEndTimeQueryHandler _handler;

    public GetDescriptionBulkImportStartAndEndTimeQueryTests(BulkImportOperationFixture bulkImportOperationFixture)
    {
        _bulkImportOperationFixture = bulkImportOperationFixture;

        _mockBulkImportOperationRepository = BulkImportOperationRepositoryMocks.CreateQueryBulkImportOperationRepository(_bulkImportOperationFixture.BulkImportOperations);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<List<GetDescriptionBulkImportStartAndEndTimeVm>>(It.IsAny<List<Domain.Entities.BulkImportOperation>>()))
            .Returns((List<Domain.Entities.BulkImportOperation> entities) => entities.Select(entity => new GetDescriptionBulkImportStartAndEndTimeVm
            {
                Id = entity.ReferenceId,
                Description = entity.Description,
                StartTime = entity.StartTime.ToString(),
                EndTime = entity.EndTime.ToString()
            }).ToList());

        _handler = new GetDescriptionBulkImportStartAndEndTimeQueryHandler(
            _mockBulkImportOperationRepository.Object,
            _mockMapper.Object);
    }

    [Fact]
    public async Task Handle_Return_GetDescriptionBulkImportStartAndEndTimeVm_When_OperationsExist()
    {
        // Arrange
        var query = new GetDescriptionBulkImportStartAndEndTimeQuery
        {
            StartTime = DateTime.Now.AddDays(-1).ToString(),
            EndTime = DateTime.Now.ToString()
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<GetDescriptionBulkImportStartAndEndTimeVm>));
        result.Count.ShouldBeGreaterThanOrEqualTo(0);
    }

    [Fact]
    public async Task Handle_Call_GetDescriptionBulkImportStartAndEndTime_OnlyOnce()
    {
        // Arrange
        var startTime = DateTime.Now.AddDays(-1).ToString();
        var endTime = DateTime.Now.ToString();
        var query = new GetDescriptionBulkImportStartAndEndTimeQuery
        {
            StartTime = startTime,
            EndTime = endTime
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.GetDescriptionBulkImportStartAndEndTime(startTime, endTime), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce_When_DataExists()
    {
        // Arrange
        var query = new GetDescriptionBulkImportStartAndEndTimeQuery
        {
            StartTime = DateTime.Now.AddDays(-1).ToString(),
            EndTime = DateTime.Now.ToString()
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<List<GetDescriptionBulkImportStartAndEndTimeVm>>(It.IsAny<List<Domain.Entities.BulkImportOperation>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoOperationsInTimeRange()
    {
        // Arrange
        var query = new GetDescriptionBulkImportStartAndEndTimeQuery
        {
            StartTime = DateTime.Now.AddDays(-10).ToString(),
            EndTime = DateTime.Now.AddDays(-9).ToString()
        };

        _mockBulkImportOperationRepository.Setup(x => x.GetDescriptionBulkImportStartAndEndTime(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperation>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<GetDescriptionBulkImportStartAndEndTimeVm>));
        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_FilterByTimeRange_When_QueryExecuted()
    {
        // Arrange
        var startTime = "2023-01-01";
        var endTime = "2023-12-31";
        var query = new GetDescriptionBulkImportStartAndEndTimeQuery
        {
            StartTime = startTime,
            EndTime = endTime
        };

        var filteredOperations = new List<Domain.Entities.BulkImportOperation>
        {
            new Domain.Entities.BulkImportOperation 
            { 
                ReferenceId = "op1", 
                Description = "Test operation",
                StartTime = new DateTime(2023, 6, 1),
                EndTime = new DateTime(2023, 6, 2)
            }
        };

        _mockBulkImportOperationRepository.Setup(x => x.GetDescriptionBulkImportStartAndEndTime(startTime, endTime))
            .ReturnsAsync(filteredOperations);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1);
        result.First().Id.ShouldBe("op1");
        result.First().Description.ShouldBe("Test operation");
    }

    [Fact]
    public async Task Handle_MapEntitiesToViewModels_WithCorrectProperties()
    {
        // Arrange
        var testOperations = new List<Domain.Entities.BulkImportOperation>
        {
            new Domain.Entities.BulkImportOperation 
            { 
                ReferenceId = "op1", 
                Description = "Test bulk import operation",
                StartTime = DateTime.Now.AddHours(-2),
                EndTime = DateTime.Now.AddHours(-1)
            }
        };

        _mockBulkImportOperationRepository.Setup(x => x.GetDescriptionBulkImportStartAndEndTime(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(testOperations);

        var query = new GetDescriptionBulkImportStartAndEndTimeQuery
        {
            StartTime = DateTime.Now.AddDays(-1).ToString(),
            EndTime = DateTime.Now.ToString()
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1);
        
        var firstItem = result.First();
        firstItem.Id.ShouldBe("op1");
        firstItem.Description.ShouldBe("Test bulk import operation");
        firstItem.StartTime.ShouldNotBeNullOrEmpty();
        firstItem.EndTime.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_ReturnCorrectListType_When_MappingSuccessful()
    {
        // Arrange
        var query = new GetDescriptionBulkImportStartAndEndTimeQuery
        {
            StartTime = DateTime.Now.AddDays(-1).ToString(),
            EndTime = DateTime.Now.ToString()
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<List<GetDescriptionBulkImportStartAndEndTimeVm>>();
        result.GetType().ShouldBe(typeof(List<GetDescriptionBulkImportStartAndEndTimeVm>));
    }

    [Fact]
    public async Task Handle_NotCallMapper_When_NoDataExists()
    {
        // Arrange
        var query = new GetDescriptionBulkImportStartAndEndTimeQuery
        {
            StartTime = DateTime.Now.AddDays(-1).ToString(),
            EndTime = DateTime.Now.ToString()
        };

        _mockBulkImportOperationRepository.Setup(x => x.GetDescriptionBulkImportStartAndEndTime(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperation>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(0);
        _mockMapper.Verify(x => x.Map<List<GetDescriptionBulkImportStartAndEndTimeVm>>(It.IsAny<List<Domain.Entities.BulkImportOperation>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_PassCorrectParameters_When_CallingRepository()
    {
        // Arrange
        var startTime = "2023-01-01T00:00:00";
        var endTime = "2023-12-31T23:59:59";
        var query = new GetDescriptionBulkImportStartAndEndTimeQuery
        {
            StartTime = startTime,
            EndTime = endTime
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.GetDescriptionBulkImportStartAndEndTime(startTime, endTime), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleNullOrEmptyTimeStrings_When_QueryExecuted()
    {
        // Arrange
        var query = new GetDescriptionBulkImportStartAndEndTimeQuery
        {
            StartTime = "",
            EndTime = ""
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.GetDescriptionBulkImportStartAndEndTime("", ""), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnEmptyListDirectly_When_CountIsZero()
    {
        // Arrange
        var query = new GetDescriptionBulkImportStartAndEndTimeQuery
        {
            StartTime = DateTime.Now.AddDays(-1).ToString(),
            EndTime = DateTime.Now.ToString()
        };

        _mockBulkImportOperationRepository.Setup(x => x.GetDescriptionBulkImportStartAndEndTime(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperation>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<GetDescriptionBulkImportStartAndEndTimeVm>));
        result.Count.ShouldBe(0);
        result.ShouldBeEmpty();
    }

    [Fact]
    public async Task Handle_MapTimeStrings_WithCorrectFormat()
    {
        // Arrange
        var testTime = DateTime.Now;
        var testOperations = new List<Domain.Entities.BulkImportOperation>
        {
            new Domain.Entities.BulkImportOperation 
            { 
                ReferenceId = "op1", 
                Description = "Test operation",
                StartTime = testTime.AddHours(-1),
                EndTime = testTime
            }
        };

        _mockBulkImportOperationRepository.Setup(x => x.GetDescriptionBulkImportStartAndEndTime(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(testOperations);

        var query = new GetDescriptionBulkImportStartAndEndTimeQuery
        {
            StartTime = DateTime.Now.AddDays(-1).ToString(),
            EndTime = DateTime.Now.ToString()
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        var firstItem = result.First();
        firstItem.StartTime.ShouldBe(testTime.AddHours(-1).ToString());
        firstItem.EndTime.ShouldBe(testTime.ToString());
    }
}
