using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DatabaseRepositoryTests : IClassFixture<DatabaseFixture>,IClassFixture<InfraObjectFixture>
{
    private readonly DatabaseFixture _databaseFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DatabaseRepository _repository;
    private readonly DatabaseRepository _repositoryNotParent;
    private readonly InfraObjectRepository _infraObjectRepository;

    public DatabaseRepositoryTests(DatabaseFixture databaseFixture,InfraObjectFixture infraObjectFixture)
    {
        _databaseFixture = databaseFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _infraObjectRepository=new InfraObjectRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repository = new DatabaseRepository(_dbContext, DbContextFactory.GetMockUserService(),_infraObjectRepository);
        _repositoryNotParent = new DatabaseRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(),_infraObjectRepository);
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;

        // Act
        await _dbContext.Databases.AddAsync(database);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(database.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(database.Name, result.Name);
        Assert.Equal(database.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.Databases);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        await _dbContext.Databases.AddAsync(database);
        await _dbContext.SaveChangesAsync();

        database.Name = "UpdatedDatabaseName";

        // Act
        _dbContext.Databases.Update(database);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(database.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedDatabaseName", result.Name);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        await _dbContext.Databases.AddAsync(database);
        await _dbContext.SaveChangesAsync();

        // Act
        database.IsActive = false;

        _dbContext.Databases.Update(database);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsParent()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        var addedEntity = await _repository.AddAsync(database);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsNotParent()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        database.CompanyId = "ChHILD_COMPANY_123";
        var addedEntity = await _repositoryNotParent.AddAsync(database);
        
        // Act
        var result = await _repositoryNotParent.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        await _repository.AddAsync(database);

        // Act
        var result = await _repository.GetByReferenceIdAsync(database.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(database.ReferenceId, result.ReferenceId);
        Assert.Equal(database.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        database.CompanyId = "CHILD_COMPANY_123";
        database.LicenseId="c9b3cd51-f688-4667-be33-46f82b7086fa";

        await _repositoryNotParent.AddAsync(database);

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(database.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(database.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(databases.Count, result.Count);
        Assert.All(result, x => Assert.Equal(DatabaseFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repositoryNotParent.AddRangeAsync(databases);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DatabaseFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ShouldReturnQueryableOrderedByIdDescending_WhenIsAllInfraTrue()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        _repository.AddRangeAsync(databases).Wait();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Any());
        
        // Verify ordering by checking if the first item has a higher ID than the last
        var resultList = result.ToList();
        if (resultList.Count > 1)
        {
            Assert.True(resultList.First().Id >= resultList.Last().Id);
        }
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnFilteredQueryable_WhenIsAllInfraFalse()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        _repositoryNotParent.AddRangeAsync(databases).Wait();

        // Act
        var result = _repositoryNotParent.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetDatabaseNames Tests

    [Fact]
    public async Task GetDatabaseNames_ShouldReturnDatabaseNames()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(databases.Count, result.Count);
        Assert.All(result, x => Assert.NotNull(x.Name));
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
    }

    [Fact]
    public async Task GetDatabaseNames_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.GetDatabaseNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsDatabaseNameUnique Tests

    [Fact]
    public async Task IsDatabaseNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        database.Name = "ExistingDatabaseName";
        await _repository.AddAsync(database);

        // Act
        var result = await _repository.IsDatabaseNameUnique("ExistingDatabaseName");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsDatabaseNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.IsDatabaseNameUnique("NonExistentDatabaseName");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsDatabaseNameExist Tests

    [Fact]
    public async Task IsDatabaseNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        database.Name = "ExistingDatabaseName";
        await _repository.AddAsync(database);

        // Act
        var result = await _repository.IsDatabaseNameExist("ExistingDatabaseName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsDatabaseNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var database = _databaseFixture.DatabaseDto;
        database.Name = "SameDatabaseName";
        await _repository.AddAsync(database);

        // Act
        var result = await _repository.IsDatabaseNameExist("SameDatabaseName", database.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;

        // Act
        var result = await _repository.AddRangeAsync(databases);

        // Assert
        Assert.Equal(databases.Count, result.Count());
        Assert.Equal(databases.Count, _dbContext.Databases.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.RemoveRangeAsync(databases);

        // Assert
        Assert.Equal(databases.Count, result.Count());
        Assert.Empty(_dbContext.Databases);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region GetDatabaseByServerId Tests

    [Fact]
    public async Task GetDatabaseByServerId_ShouldReturnEntitiesWithMatchingServerId_WhenIsAllInfraTrue()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        databases[0].ServerId = "d2c9fd6c-345e-405f-ab59-6b4da8e51bca";
        databases[1].ServerId = "d2c9fd6c-345e-405f-ab59-6b4da8e51bm4";
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseByServerId("d2c9fd6c-345e-405f-ab59-6b4da8e51bca");

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal("d2c9fd6c-345e-405f-ab59-6b4da8e51bca", x.ServerId));
    }

    [Fact]
    public async Task GetDatabaseByServerId_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        databases[0].ServerId = "d2c9fd6c-345e-405f-ab59-6b4da8e51bca";
        databases[1].ServerId = "d2c9fd6c-345e-405f-ab59-6b4da8e51bm4";

        await _repositoryNotParent.AddRangeAsync(databases);

        // Act
        var result = await _repositoryNotParent.GetDatabaseByServerId("d2c9fd6c-345e-405f-ab59-6b4da8e51bca");

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task GetDatabaseByServerId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseByServerId("non-existent-server-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDatabaseByDatabaseTypeId Tests

    [Fact]
    public async Task GetDatabaseByDatabaseTypeId_ShouldReturnEntitiesWithMatchingDatabaseTypeId()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        databases[0].DatabaseTypeId = "cddd5b1c-076b-4bd5-aaba-75df3b86a476";
        databases[1].DatabaseTypeId = "cddd5b1c-076b-4bd5-aaba-75df3b86a476";

        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseByDatabaseTypeId("cddd5b1c-076b-4bd5-aaba-75df3b86a476");

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal("cddd5b1c-076b-4bd5-aaba-75df3b86a476", x.DatabaseTypeId));
        Assert.Equal(2, result.Count);
    }

    [Fact]
    public async Task GetDatabaseByDatabaseTypeId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetDatabaseByDatabaseTypeId("non-existent-database-type-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetByUserName Tests

    [Fact]
    public async Task GetByUserName_ShouldReturnEntitiesWithMatchingUserName_WhenIsParent()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        databases[0].Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"admin\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}";
        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetByUserName("admin");

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Contains("admin", x.Properties));
    }

    [Fact]
    public async Task GetByUserName_ShouldReturnFilteredEntities_WhenIsNotParent()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        databases[0].Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"admin\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}";

        await _repositoryNotParent.AddRangeAsync(databases);

        // Act
        var result = await _repositoryNotParent.GetByUserName("admin");

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Contains("admin", x.Properties));
        Assert.All(result, x => Assert.Equal(DatabaseFixture.CompanyId, x.CompanyId));
    }

    #endregion

    #region GetByUserNameAndDatabaseType Tests

    [Fact]
    public async Task GetByUserNameAndDatabaseType_ShouldReturnFilteredEntities()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        databases[0].DatabaseTypeId = "cddd5b1c-076b-4bd5-aaba-75df3b86a476";
        databases[1].DatabaseTypeId = "cddd5b1c-076b-4bd5-aaba-75df3b86a476";
        databases[0].Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"admin\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}";

        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetByUserNameAndDatabaseType("admin", "cddd5b1c-076b-4bd5-aaba-75df3b86a476");

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Contains("admin", x.Properties));
        Assert.All(result, x => Assert.Equal("cddd5b1c-076b-4bd5-aaba-75df3b86a476", x.DatabaseTypeId));
    }

    [Fact]
    public async Task GetByUserNameAndDatabaseType_ShouldHandleCommaSeparatedDatabaseTypeIds()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList;
        var database1 = databases.First();
        database1.DatabaseTypeId = "TYPE1";
        database1.Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"admin\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}";
       
        var database2 = databases.Skip(1).First();
        database2.DatabaseTypeId = "TYPE2";

        await _repository.AddRangeAsync(databases);

        // Act
        var result = await _repository.GetByUserNameAndDatabaseType("admin", "TYPE1,TYPE2");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.DatabaseTypeId == "TYPE1");
        Assert.Contains(result, x => x.DatabaseTypeId == "TYPE2");
    }

    #endregion

    //#region GetDatabaseByNodeId Tests

    //[Fact]
    //public async Task GetDatabaseByNodeId_ShouldReturnEntitiesWithMatchingNodeId_WhenIsAllInfraTrue()
    //{
    //    // Arrange
    //    var databases = _databaseFixture.DatabaseList;
    //    databases[0].Properties = "{\"DatabaseConnectivity\":\"viaSSH\",\"This is a Part of Cluster\":false,\"IsRac\":false,\"OracleSID\":\"dddd\",\"InstanceName\":\"dddd\",\"SSOEnabled\":false,\"UserName\":\"admin\",\"Password\":\"pZ7HCd24oxNFyvrgwNxkcjosL2TeFv0+PmO5DxXz0gw=$avOqJmck+1bapwM5UeKz/5wJznZKZ//9JkcaExTiSaR1/8vxPw==\",\"Port\":\"5666\",\"ArchivePath\":\"\",\"RedoPath\":\"\",\"OracleHomePath\":\"c://b\",\"isASMGrid\":false,\"ENVVariable\":false,\"DatabaseAuthentication\":false,\"icon\":\"cp-oracle\",\"databaseType\":\"Oracle\",\"version\":\"12c\"}"

    //    await _repository.AddRangeAsync(databases);

    //    // Act
    //    var result = await _repository.GetDatabaseByNodeId(DatabaseFixture.NodeId);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.All(result, x => Assert.Equal(DatabaseFixture.NodeId, x.Properties));
    //}

    //[Fact]
    //public async Task GetDatabaseByNodeId_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    //{
    //    // Arrange
    //    var databases = _databaseFixture.DatabaseList;
    //    await _repositoryNotParent.AddRangeAsync(databases);

    //    // Act
    //    var result = await _repositoryNotParent.GetDatabaseByNodeId(DatabaseFixture.NodeId);

    //    // Assert
    //    Assert.NotNull(result);
    //    // Result should be filtered based on assigned infrastructure
    //}

    //#endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var databases = _databaseFixture.DatabaseList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(databases);
        var initialCount = databases.Count;

        var toUpdate = databases.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedDatabaseName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = databases.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Name == "UpdatedDatabaseName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
