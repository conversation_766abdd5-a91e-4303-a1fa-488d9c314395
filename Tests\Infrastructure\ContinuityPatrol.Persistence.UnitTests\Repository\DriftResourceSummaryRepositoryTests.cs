using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DriftResourceSummaryRepositoryTests : IClassFixture<DriftResourceSummaryFixture>
{
    private readonly DriftResourceSummaryFixture _driftResourceSummaryFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DriftResourceSummaryRepository _repository;
    private readonly DriftResourceSummaryRepository _repositoryNotParent;

    public DriftResourceSummaryRepositoryTests(DriftResourceSummaryFixture driftResourceSummaryFixture)
    {
        _driftResourceSummaryFixture = driftResourceSummaryFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DriftResourceSummaryRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DriftResourceSummaryRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var driftResourceSummary = _driftResourceSummaryFixture.DriftResourceSummaryDto;

        // Act
        var result = await _repository.AddAsync(driftResourceSummary);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftResourceSummary.InfraObjectId, result.InfraObjectId);
        Assert.Equal(driftResourceSummary.EntityName, result.EntityName);
        Assert.Single(_dbContext.DriftResourceSummarys);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var driftResourceSummary = _driftResourceSummaryFixture.DriftResourceSummaryDto;
        await _repository.AddAsync(driftResourceSummary);

        driftResourceSummary.EntityName = "UpdatedEntityName";

        // Act
        var result = await _repository.UpdateAsync(driftResourceSummary);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedEntityName", result.EntityName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var driftResourceSummary = _driftResourceSummaryFixture.DriftResourceSummaryDto;
        await _repository.AddAsync(driftResourceSummary);

        // Act
        var result = await _repository.DeleteAsync(driftResourceSummary);

        // Assert
        Assert.Equal(driftResourceSummary.EntityName, result.EntityName);
        Assert.Empty(_dbContext.DriftResourceSummarys);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var driftResourceSummary = _driftResourceSummaryFixture.DriftResourceSummaryDto;
        var addedEntity = await _repository.AddAsync(driftResourceSummary);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.EntityName, result.EntityName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var driftResourceSummary = new DriftResourceSummary
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_123",
            EntityName = "TestEntity",
            IsActive = true
        };
        var addedEntity = await _repositoryNotParent.AddAsync(driftResourceSummary);

        // Act
        var result = await _repositoryNotParent.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var driftResourceSummary = new DriftResourceSummary
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_123",
            EntityName = "TestEntity",
            IsActive = true
        };
        await _repository.AddAsync(driftResourceSummary);

        // Act
        var result = await _repository.GetByReferenceIdAsync(driftResourceSummary.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftResourceSummary.ReferenceId, result.ReferenceId);
        Assert.Equal(driftResourceSummary.EntityName, result.EntityName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var driftResourceSummaries = _driftResourceSummaryFixture.DriftResourceSummaryList;
        await _repository.AddRangeAsync(driftResourceSummaries);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftResourceSummaries.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var driftResourceSummaries = new List<DriftResourceSummary>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", EntityName = "Entity1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_456", EntityName = "Entity2", IsActive = true }
        };
        await _repositoryNotParent.AddRangeAsync(driftResourceSummaries);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetByInfraObjectIdAndEntityName Tests

    [Fact]
    public async Task GetByInfraObjectIdAndEntityName_ShouldReturnMatchingEntities()
    {
        // Arrange
        var driftResourceSummaries = new List<DriftResourceSummary>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", EntityName = "TestEntity", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", EntityName = "testentity", IsActive = true }, // Case insensitive match
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", EntityName = "OtherEntity", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_456", EntityName = "TestEntity", IsActive = true }
        };
        await _repository.AddRangeAsync(driftResourceSummaries);

        // Act
        var result = await _repository.GetByInfraObjectIdAndEntityName("INFRA_123", "TestEntity");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Should match case-insensitive
        Assert.All(result, x => Assert.Equal("INFRA_123", x.InfraObjectId));
        Assert.All(result, x => Assert.Equal("TestEntity", x.EntityName, StringComparer.OrdinalIgnoreCase));
    }

    [Fact]
    public async Task GetByInfraObjectIdAndEntityName_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var driftResourceSummaries = new List<DriftResourceSummary>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", EntityName = "TestEntity", IsActive = true }
        };
        await _repository.AddRangeAsync(driftResourceSummaries);

        // Act
        var result = await _repository.GetByInfraObjectIdAndEntityName("INFRA_456", "TestEntity");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectIdAndEntityName_ShouldHandleNullInfraObjectId()
    {
        // Arrange
        var driftResourceSummaries = new List<DriftResourceSummary>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = null, EntityName = "TestEntity", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", EntityName = "TestEntity", IsActive = true }
        };
        await _repository.AddRangeAsync(driftResourceSummaries);

        // Act
        var result = await _repository.GetByInfraObjectIdAndEntityName("INFRA_123", "TestEntity");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Should only return the one with matching non-null InfraObjectId
        Assert.Equal("INFRA_123", result.First().InfraObjectId);
    }

    #endregion

    #region GetByInfraObjectId Tests

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEntitiesWithMatchingInfraObjectId()
    {
        // Arrange
        var driftResourceSummaries = new List<DriftResourceSummary>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", EntityName = "Entity1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", EntityName = "Entity2", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_456", EntityName = "Entity3", IsActive = true }
        };
        await _repository.AddRangeAsync(driftResourceSummaries);

        // Act
        var result = await _repository.GetByInfraObjectId("INFRA_123");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal("INFRA_123", x.InfraObjectId));
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var driftResourceSummaries = new List<DriftResourceSummary>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", EntityName = "Entity1", IsActive = true }
        };
        await _repository.AddRangeAsync(driftResourceSummaries);

        // Act
        var result = await _repository.GetByInfraObjectId("non-existent-infra-object-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleNullInfraObjectId()
    {
        // Arrange
        var driftResourceSummaries = new List<DriftResourceSummary>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = null, EntityName = "Entity1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", EntityName = "Entity2", IsActive = true }
        };
        await _repository.AddRangeAsync(driftResourceSummaries);

        // Act
        var result = await _repository.GetByInfraObjectId("INFRA_123");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Should only return the one with matching non-null InfraObjectId
        Assert.Equal("INFRA_123", result.First().InfraObjectId);
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var driftResourceSummaries = new List<DriftResourceSummary>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", EntityName = "Entity1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_456", EntityName = "Entity2", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_789", EntityName = "Entity3", IsActive = true }
        };
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(driftResourceSummaries);
        var initialCount = driftResourceSummaries.Count;
        
        var toUpdate = driftResourceSummaries.Take(2).ToList();
        toUpdate.ForEach(x => x.EntityName = "UpdatedEntityName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = driftResourceSummaries.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.EntityName == "UpdatedEntityName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion

    #region Entity Specific Tests

    [Fact]
    public async Task DriftResourceSummary_ShouldMaintainInfraObjectProperties()
    {
        // Arrange
        var driftResourceSummary = _driftResourceSummaryFixture.DriftResourceSummaryDto;


        driftResourceSummary.InfraObjectId = "2cc8a13c-bf82-42a0-a305-e4e5917396f3";
        driftResourceSummary.TypeId = "04fbf1d5-1e2f-4352-a1f3-3f903fda59ef";
        driftResourceSummary.Type = "Linux";
        driftResourceSummary.EntityName = "Database";
        driftResourceSummary.ConflictCount = 0;
        driftResourceSummary.ParameterName = "CPU";
        driftResourceSummary.NonConflictCount=1;

        // Act
        var result = await _repository.AddAsync(driftResourceSummary);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("2cc8a13c-bf82-42a0-a305-e4e5917396f3", result.InfraObjectId);
        Assert.Equal("04fbf1d5-1e2f-4352-a1f3-3f903fda59ef", result.TypeId);
        Assert.Equal("Linux", result.Type);
        Assert.Equal("Database", result.EntityName);
        Assert.Equal("CPU", result.ParameterName);

    }

    #endregion
}
