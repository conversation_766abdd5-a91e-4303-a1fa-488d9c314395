
let jsonData = "", selectedValues = [], checkdetail = false, dataTable, infra_check = []


const NotificationPermission = {
     createPermission : $("#ConfigurationCreate").data("create-permission").toLowerCase(),
     deletePermission : $("#ConfigurationDelete").data("delete-permission").toLowerCase()
}
treeListView();
const NotificationURL = {
    nameExistUrl: "Manage/NotificationManager/IsAlertReceiverNameExist",
    Pagination:"/Manage/NotificationManager/GetPagination"
}

if (NotificationPermission.createPermission == 'false') {
    $("#CreteButton").removeClass('#CreteButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled',NotificationPermission.createPermission == 'false');
}
function Notificationdebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
$(function () {
 dataTable = $('#notificationManager').DataTable(
    {
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow"></i>'
            }, infoFiltered: ""
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        "processing": true,
        "serverSide": true,
        "filter": true,
        "order": [],
        "ajax": {
            "type": "GET",
            "url": NotificationURL.Pagination,
            "dataType": "json",
            "data": function (d) {
             
                let sortIndex = d?.order[0]?.column || '';
                let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "emailAddress" : sortIndex === 3 ? "properties" :
                    sortIndex === 4 ? "mobileNumber" : sortIndex === 5 ? "isActiveUser" : ""
                let orderValue = d.order[0]?.dir || 'asc';
                d.sortColumn = sortValue;
                d.SortOrder = orderValue;
                d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                d.pageSize = d?.length;
                d.searchString = selectedValues?.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                selectedValues.length = 0;
            },
            "error": function (xhr, err) {
                if (xhr.status === 401) {
                    window.location.assign('/Account/Logout');
                }
            },
            "dataSrc": function (json) {
                if (json?.success) {
                    json.recordsTotal = json?.data?.totalPages;
                    json.recordsFiltered = json?.data?.totalCount;
                    if (json?.data?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data?.data;
                } else {
                    errorNotification(json)
                }
            }
        },
        "columnDefs": [
            {
                "targets": [0, 1, 2, 3, 4],
                "className": "truncate"
            }
        ],
        "columns": [
            {
                "data": null,
                "name": "Sr. No.",
                "autoWidth": true,
                "orderable": false,
                "render": function (data, type, row, meta) {
                    if (type === 'display') {
                        return meta.row + 1;
                    }
                    return data;
                },
                "orderable": false,
            },
            {
                "data": "name", "name": "Name", "autoWidth": true,
                "render": function (data, type, row) {
                    return `<td><span title="${data == null ? " NA" : data}"> ${data == null ? "NA" : data}</span></td >`;
                }
            },
            {
                "data": "emailAddress", "name": "Email Address", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return `<td><span title="${data == null ? " NA" : data}"> ${data == null ? "NA" : data}</span></td >`;
                    }
                    return data;
                }
            },
            {
                "data": "properties", "name": "InfraObject Name", "autoWidth": true,
                "render": function (data, type, row) {
                    let parsedatas = JSON.parse(data)
                    let infradats = []
                    parsedatas?.assignedBusinessServices?.forEach(service => {
                        service?.assignedBusinessFunctions?.forEach(func => {
                            func?.assignedInfraObjects?.forEach(infra => {
                                if (infra?.isSelected) {
                                    infradats.push(infra?.name)
                                }
                            });
                        });
                    });
                    if (type === 'display') {
                        return '<span title="' + infradats + '">' + infradats + '</span>';
                    }
                    return data;
                }
            },
            {
                "data": "mobileNumber", "name": "Mobile Number", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display' && data === null) {
                        return '<span >NA</span>';
                    } else {
                        return '<span >' + data + '</span>';
                    }
                    return data;
                }
            },
            {
                "data": "isActiveUser", "name": "Active", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return '<span title="' + (data == true ? "Active" : "InActive") + '" class="' + (data == true ? "cp-success text-success" : "cp-error text-danger") + '"></span>';
                    }
                    return data;
                }
            },
            {
                "render": function (data, type, row) {
                    if (NotificationPermission.createPermission === 'true' && NotificationPermission.deletePermission === "true") {
                        return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" class="edit-button" title="Edit" data-alertreceiver='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>                               
                                            <span role="button" class="delete-button"  title="Delete" data-alertreceiver-id="${row.id}" data-alertreceiver-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                   
                        </div>`;
                    }
                    else if (NotificationPermission.createPermission === 'true' && NotificationPermission.deletePermission === "false") {
                        return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" class="edit-button"  title="Edit" data-alertreceiver='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>                                
                                            <span role="button"  class="icon-disabled"  title="Delete">
                                                <i class="cp-Delete"></i>
                                            </span>
                                   
                        </div>`;
                    }
                    else if (NotificationPermission.createPermission === 'false' && NotificationPermission.deletePermission === "true") {
                        return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" class="icon-disabled"  title="Edit">
                                                <i class="cp-edit"></i>
                                            </span>
                                 
                                            <span role="button"  title="Delete"  class="delete-button" data-alertreceiver-id="${row.id}" data-alertreceiver-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                <i class="cp-Delete"></i>
                                            </span>
                                   
                        </div>`;
                    }
                    else {
                        return `
                        <div class="d-flex align-items-center gap-2">    
                                            <span role="button" class="icon-disabled"  title="Edit">
                                                <i class="cp-edit"></i>
                                          </span>                              
                                            <span role="button"   class="icon-disabled"  title="Delete">
                                                <i class="cp-Delete"></i>
                                            </span>                  
                        </div>`;
                    }
                },
                "orderable": false,
            }
        ],
        "rowCallback": function (row, data, index) {
            var api = this.api();
            var startIndex = api.context[0]._iDisplayStart;
            var counter = startIndex + index + 1;
            $('td:eq(0)', row).html(counter);
        },
        initComplete: function () {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
        }
    });
dataTable.on('draw.dt', function () {
    $('.paginate_button.page-item.previous').attr('title', 'Previous');
    $('.paginate_button.page-item.next').attr('title', 'Next');
});
    $('#search-inp').on('keydown input', Notificationdebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const nameCheckbox = $("#Name");
        const emailNameCheckbox = $("#Email");
        const mobileCheckBox = $("#Mobile");
        const inputValue = $('#search-inp').val();
        if (nameCheckbox.is(':checked')) {
            selectedValues.push(nameCheckbox.val() + inputValue);
        }
        if (emailNameCheckbox.is(':checked')) {
            selectedValues.push(emailNameCheckbox.val() + inputValue);
        }
        if (mobileCheckBox.is(':checked')) {
            selectedValues.push(mobileCheckBox.val() + inputValue);
        }
        let currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {
                if ($('#search-inp').val().length === 0) {
                    if (json?.data?.data?.length === 0) {
                        $('.dataTables_empty').text('No Data Found');
                    }
                } else if (json?.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }
    }));
})
//ClearData's
$("#CreteButton").on('click', function () {
    clearInputField();
});
$("#notify_btn_cancel").on("click", function () {
    clearInputField();
})
const clearInputField = () => {
    $("#notifyId,#name,#email,#mobilenum,#mobilepre").val('');;
    $("#isMobile,#activeuser,#sendreport").prop("checked", false);
    $("#mob,#mobpre").hide();
    jsonData = null; // Clear the data
    $("#treeview").empty();
    treeListView();
    $("#save").prop('disabled', false);
    checkdetail = false
    $('#save').text('Save');
    $("#Name-error,#Email-error,#Mobile-error,#MobilePre-error").text('').removeClass('field-validation-error')
    $("#tree-error").text('').removeClass('text-danger');
};
$("#mob,#mobpre").hide();
//Validation
$('#name').on('input', Notificationdebounce(async function () {
    let value = await sanitizeInput($("#name").val());
    $("#name").val(value);
    await validateName(value, $('#notifyId').val(), NotificationURL.nameExistUrl);
}));

$('#email').on('input keypress', async function () {
    if ($(this).val() !== undefined) {
        $(this).val($(this).val().replace(/  +/g, " "));
    }
    await validateEmail($(this).val());
});

$('#mobilenum').on('input keypress', async function (event) {
    if ($(this).val() !== undefined) {
        $(this).val($(this).val().replace(/  +/g, " "));
    }
    if (!/^[0-9]+$/.test(event.key)) {
        event.preventDefault();
    }
    await validateMobile($(this).val());
});

    $.getJSON("/json/CountryDailCode.json", function (data) {
        setTimeout(() => {
            data.countrycode.forEach(function (value) {
                $('#mobilepre').append('<option value="' + value.dial_code + '">' + value.dial_code + '</option>');
            });
        }, 500);
    }).fail(function () {
        console.error("Failed to load JSON data.");
    });


$('#mobilepre').on('change', async function () {    
    await validateMobilePre($(this).val());
});

//Checkbox
$('#isMobile').on("change", function () {
    if (this.checked) {
        $("#mob,#mobpre,#mobilenum,#mobilepre").show();
    }
    else {
        $("#mob,#mobpre,#mobilenum,#mobilepre").hide();
        $('#mobilenum').val('');
        $('#mobilepre').val('').trigger('change')
        $("#Mobile-error,#MobilePre-error").text('').removeClass('field-validation-error');
    }
});
//Update
$('#notificationManager').on('click', '.edit-button', function () {
    const alertData = $(this).data('alertreceiver');
    populateModalFields(alertData);
    $('#save').text('Update');
    $('#CreateModal').modal('show');
});
function populateModalFields(alertData, ClickedEditButton = true) {
    $('#notifyId').val(alertData?.id);
    $('#name').val(alertData?.name);
    $('#email').val(alertData?.emailAddress);
    $('#textProperties').val(alertData?.properties);
    jsonData = JSON.parse(alertData?.properties);
    $('#isMobile').prop("checked", alertData?.isMail);
    alertData.isMail ? $('input[name="isMail"]').prop("checked", true) : $('input[name="isMail"]').prop("checked", false);
    if (alertData.isMail == true) {
        $("#mob,#mobpre,#mobilenum,#mobilepre").show();
        const mobileNumber = alertData?.mobileNumber.split('-');
        $('#mobilepre').val(mobileNumber[0]);
        $('#mobilenum').val(mobileNumber[1]);
    }
    else {
        $("#mob,#mobpre,#mobilenum,#mobilepre").hide();
    }
    $("#activeuser").prop('checked', alertData?.isActiveUser);
    alertData?.isActiveUser ? $('input[name="isActiveUser"]').prop("checked", true) : $('input[name="isActiveUser"]').prop("checked", false);
    $("#sendreport").prop('checked', alertData?.isSendReport);
    alertData?.isSendReport ? $('input[name="isSendReport"]').prop("checked", true) : $('input[name="isSendReport"]').prop("checked", false);
    $("#Name-error,#Email-error,#Mobile-error,#MobilePre-error").text('').removeClass('field-validation-error')
    updateTreeView(alertData, ClickedEditButton);
}
//Delete
$("#notificationManager").on('click', '.delete-button', function () {
    const alertId = $(this).data('alertreceiver-id');
    const alertName = $(this).data('alertreceiver-name');
    $("#deleteData").attr("title", alertName).text(alertName);
    $('#textDeleteId').val(alertId);
});
//Save Function
$("#save").on("click", async function () {
    const name = $("#name").val();
    const emailAddress = $('#email').val();
    const mobilePre = $("#mobilepre").val();
    const mobileNo = $("#mobilenum").val();
    if (mobilePre && mobileNo) {
        const comMobile = mobilePre + '-' + mobileNo;
        $('#comMobile').val(comMobile)
    }
    const alertId = $('#notifyId').val();
    const infraTree = $("#treeview").html();
    const isMobileChk = $("#isMobile").prop('checked');
    const isName = await validateName(name, alertId, NotificationURL.nameExistUrl);
    const isEmail = await validateEmail(emailAddress);
    const isMobileNo = isMobileChk ? await validateMobile(mobileNo) : true;
    const isMobilePre = isMobileChk ? await validateMobilePre(mobilePre) : true;
    const isInfraTree = await validateInfraTree(infraTree, jsonData);
    if (isName && isEmail && isInfraTree && isMobilePre && isMobileNo && checkdetail) {
        $(this).prop('disabled', true);
        $("#CreateForm").trigger("submit");
    }
});

$("#selectAll").on("change", function () {
    if ($("#selectAll").prop("checked") == true) {
        $("#tree-error").text("")
        return true;
    } else {
        setTimeout(() => {
            $("#tree-error").text("Select at least one infraobject").css({ "color": "#FF0000", "font-size":"11px" })
        }, 500)
        return false
    }
})
function datacheck(infras) {
    if (infras == true) {
        checkdetail = true
    }
}
function validateInfraTree(value, jsondata) { 
    jsondata?.assignedBusinessServices?.forEach(service => {
        service?.assignedBusinessFunctions?.forEach(func => {
            func?.assignedInfraObjects?.forEach(infra => {
                datacheck(infra?.isSelected)
            });
        });
    });
    if (value === undefined || value === null || value?.length === 0 || checkdetail == false) {
        $("#tree-error").text("Select at least one infraobject").css({ "color": "#FF0000", "font-size":"11px" })
    } else {
        $("#tree-error").text("")
        return true;
    }
}
async function validateMobilePre(value) {
    if (!value) {
        $('#MobilePre-error').text('Select country code')
            .addClass('field-validation-error');
        return false;
    }
    let validationResults = false
    if (value) {
        validationResults = true
        $('#MobilePre-error').text('').removeClass('field-validation-error');
        return true;
    }
    return await CommonValidation($('#MobilePre-error'), validationResults);
}
async function validateMobile(value) {
    if (!value) {
        $('#Mobile-error').text('Enter mobile number')
            .addClass('field-validation-error');
        return false;
    } else if (value == 0) {
        $('#Mobile-error').text('Number not starts with zero')
            .addClass('field-validation-error');
        return false;
    }
    else if (value) {
        const minLength = 7;
        if (value.length < minLength) {
            $('#Mobile-error').text('Must be at least 7 characters')
                .addClass('field-validation-error');
            return false;
        } else {
            $('#Mobile-error').text('')
                .removeClass('field-validation-error');
            return true;
        }
    }
    else {
        $('#Mobile-error').text('')
            .removeClass('field-validation-error');
        return true;
    }
}
async function validateName(value, id = null, url) {
    if (!value) {
        $('#Name-error').text('Enter notification name')
            .addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')){
        $('#Name-error').text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    var url = RootUrl + url;
    var data = {};
    data.alertReceiverName = value;
    data.id = id;
    const validationResults = [
        await SpecialCharValidateCustom(value),
        value == "_" ? await ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? await ShouldNotBeginWithSpace(value) :
            await OnlyNumericsValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsNameExist(url, data, OnError)
    ];
    return await CommonValidation($('#Name-error'), validationResults);
}
 async function validateEmail(value) {
     var format = /^[a-zA-Z0-9._]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/   
     if (!value) {
         $('#Email-error').text('Enter email address')
             .addClass('field-validation-error');
         return false;
     } else if (value.length >= 321) {
         $('#Email-error').text('Enter the value less than 320 characters')
             .addClass('field-validation-error');
         return false;
     } else if (value.length) {
         if (format.test(value) == false) {
             $('#Email-error').text('Invalid email')
                 .addClass('field-validation-error');
             return false;
         } else if (value.charAt(0) == "." || value.charAt(0) == "_" ) {
             $('#Email-error').text('Invalid email')
                 .addClass('field-validation-error');
             return false;
         } else {
             $('#Email-error').text('')
                 .removeClass('field-validation-error');
             return true;
         }
     }
     else {
         $('#Email-error').text('')
             .removeClass('field-validation-error');
         return true;
     }
     const validationResults = [await emailRegex(value)];
     return await CommonValidation($('#Email-error'), validationResults);
 }
//Name alrea
async function IsNameExist(url, data, errorFunc) {
    return !data?.alertReceiverName.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}
//Tree
async function treeListView() {
    $("#treeview").empty();
   await $.ajax({
        url: RootUrl + "Manage/NotificationManager/GetAllInfraObjectList",
        method: 'GET',
        dataType: 'json',
        success: function (data) {
            jsonData = data?.data
            populateTreeView(jsonData);
        },
        error: function (error) {
            notificationAlert('Error:', error);
        }
    });
}
function SelectAllTreeViewExpended(open) {
    $('#expand details').attr('open', open);
}
function populateTreeView(jsonData) {
    const container = $("#treeview");
    container.empty();
    if (jsonData && jsonData?.assignedBusinessServices) {
        createTreeView(container, jsonData);
    }
}
async function updateTreeView(alertData, ClickedEditButton) {
    $("#treeview").empty();
    let userInfraObject = JSON.parse(alertData?.properties);
    if (ClickedEditButton) {
        let jsonDetails = "";
        await $.ajax({
            url: RootUrl + "Manage/NotificationManager/GetAllInfraObjectList",
            method: 'GET',
            dataType: 'json',
            success: function (data) {
                if (data?.success) {
                    jsonDetails = data?.data
                } else {
                    errorNotification(result)
                }
            },
            error: function (error) {
                notificationAlert('Error:', error);
            }
        });
        if (jsonDetails?.assignedBusinessServices?.length > 0) {
            jsonDetails.isAll = userInfraObject.isAll    
            jsonDetails?.assignedBusinessServices?.forEach(function (JSONData) {
                userInfraObject?.assignedBusinessServices?.forEach(function (infraObject) {    
                    if (JSONData?.id === infraObject?.id) {
                        JSONData.isAll = infraObject?.isAll;
                        JSONData.isPartial = infraObject?.isPartial;
                        JSONData?.assignedBusinessFunctions?.forEach(function (jsonDataFunction) {
                            infraObject?.assignedBusinessFunctions?.forEach(function (infraFunction) {
                                if (jsonDataFunction?.id === infraFunction?.id) {
                                    jsonDataFunction.isAll = infraFunction?.isAll;
                                    jsonDataFunction.isPartial = infraFunction?.isPartial;
                                    jsonDataFunction?.assignedInfraObjects?.forEach(function (jsonDataInfa) { 
                                        infraFunction?.assignedInfraObjects?.forEach(function (infra) {
                                           if (jsonDataInfa?.id === infra?.id) {
                                               jsonDataInfa.isSelected = infra?.isSelected;
                                            }
                                        });
                                   });
                                }
                            });
                        });
                    }
                });
            });
            userInfraObject = [];
            userInfraObject = jsonDetails;
            jsonData = jsonDetails
        }
    }
    if (userInfraObject === null) {
        treeListView();
    }
    else {
        createTreeView($("#treeview"), userInfraObject);
    }
}
$('#textProperties').on('change', function () {
    let chk = this.checked, id = $(this).attr('businessid'), funcId = $(this).attr('functionId'), infraId = $(this).attr('infraId')
    // Update alertData.userInfraObject
    if (!alertData.userInfraObject) {
        alertData.userInfraObject = {};
    }
    if (!alertData.userInfraObject[id]) {
        alertData.userInfraObject[id] = {};
    }
    if (!alertData.userInfraObject[id][funcId]) {
        alertData.userInfraObject[id][funcId] = {};
    }
    alertData.userInfraObject[id][funcId][infraId] = chk;
    // Update the hidden input field
    $('#textProperties').val(JSON.stringify(alertData.properties));
});
function selectTree(checkbox) {
    let chk = checkbox.checked, businessId = $(checkbox).attr("businessid"), functionid = $(checkbox).attr("functionid"), infraid = $(checkbox).attr("infraid")
    infratree(jsonData)
    JsonTreeView(chk, businessId, functionid, infraid);
}
function infratree(data) {
    infra_check=[]
    if (data) {
        setTimeout(() => {
            infras(jsonData)
        }, 300)
    }
    function infras(d) {
        d?.assignedBusinessServices?.forEach(service => {
            service?.assignedBusinessFunctions?.forEach(func => {
                func?.assignedInfraObjects?.forEach(infra => {
                    if (infra?.isSelected == true) {
                      infra_check.push(infra.isSelected) 
                    }
                });
            });
        });
    }
    setTimeout(() => {
        if (infra_check?.length == 0) {
            $("#tree-error").text("Select at least one infraobject").css("color", "#FF0000").css("font-size", "11px")
        } else {
            $("#tree-error").text("")
        } 
    },300)
}
function JsonTreeView(s, id, funcId, infraId) {
    jsonData?.assignedBusinessServices?.forEach((d) => {
        if (id && funcId && !infraId) {
            if (d?.id === id) {
                d?.assignedBusinessFunctions?.forEach((f) => {
                    if (f.id === funcId) {
                        f.isAll = s;
                        f.isPartial = s;
                        if (infraId || s === false)
                            if (infraId) m
                        {
                            f?.assignedInfraObjects?.forEach((infra) => {
                                if (infra?.id != null) {
                                    infra.isSelected = s;
                                }
                            });
                        }
                        var selectedBFCount = d?.assignedBusinessFunctions?.reduce(function (count, busimessFunction) {
                            return count + (busimessFunction?.isAll ? 1 : 0);
                        },
                            0); if (selectedBFCount === d?.assignedBusinessFunctions?.length) {
                                d.isAll = true;
                                d.isPartial = true;
                            } else if (selectedBFCount > 0) {
                                d.isAll = false;
                                d.isPartial = false;
                            } else {
                            d.isAll = false;
                            d.isPartial = false;
                        }
                    }
                });
            }
        } else if (d?.id === id && !funcId && !infraId) {
            d.isAll = s;
            d.isPartial = s;
            d.assignedBusinessFunctions.forEach((bf) => {
                if (bf != null) {
                    d.isAll = s;
                    d.isPartial = s;
                    bf.isAll = s;
                    bf.isPartial = s;
                    bf.assignedInfraObjects?.forEach((infra) => {
                        if (infra?.id != null) {
                            infra.isSelected = s;
                        }
                    });
                }
            });
        }
        else if (d?.id === id && funcId && infraId != null) {
            setTimeout(() => {
                $("#tree-error").text("")
            }, 200) 
            if (s === false) {
                d?.assignedBusinessFunctions?.forEach((bf) => {
                    if (bf != null) {
                        bf?.assignedInfraObjects?.forEach((infra) => {
                            if (infra?.id === infraId) {
                                infra.isSelected = s;
                                d?.assignedBusinessFunctions?.forEach((bf) => {
                                    if (bf?.id === funcId) {
                                        d.isAll = s;
                                        d.isPartial = s;
                                        bf.isAll = s;
                                        bf.isPartial = s;
                                    }
                                });
                            }
                        });
                    }
                });
            }
            else if (s === true) {
                d?.assignedBusinessFunctions?.forEach((bf) => {
                    if (bf != null) {
                        bf?.assignedInfraObjects?.forEach((infra) => {
                            if (s === true) {
                                if (infra?.id === infraId) {
                                    infra.isSelected = s;
                                }
                            } else {
                                d.isAll = false;
                                d.isPartial = false;
                                bf.isAll = false;
                                bf.isPartial = false;
                            }                          
                            var selectedCount = bf?.assignedInfraObjects?.reduce(function (count, infra) {
                                return count + (infra?.isSelected ? 1 : 0);
                            }, 0);

                            if (selectedCount === bf?.assignedInfraObjects?.length) {
                                //d.isAll = true;
                                //d.isPartial = false
                                bf.isAll = true;
                                bf.isPartial = false;
                            } else if (selectedCount > 0) {
                                d.isAll = false;
                                d.isPartial = true;
                                bf.isAll = false;
                                bf.isPartial = true;
                            } else {
                                bf.isAll = false;
                                bf.isPartial = false;
                            }
                            var selectedBFCount = d?.assignedBusinessFunctions?.reduce(function (count, busimessFunction) {
                                return count + (busimessFunction?.isAll ? 1 : 0);
                            }, 0);
                            if (selectedBFCount === d?.assignedBusinessFunctions?.length) {
                                d.isAll = true;
                                d.isPartial = true;
                            } else if (selectedBFCount > 0) {
                                d.isAll = false;
                                d.isPartial = false;
                            } else {
                                d.isAll = false;
                                d.isPartial = false;
                            }
                        });
                    }
                });
            }
            else {
                d.isAll = s;
                d.isPartial = s;
                d?.assignedBusinessFunctions?.forEach((bf) => {
                    if (bf != null) {
                        d.isAll = s;
                        d.isPartial = s;
                        bf.isAll = s;
                        bf.isPartial = s;
                        bf.assignedInfraObjects?.forEach((infra) => {
                            if (infra?.id == infraId) {
                                infra.isSelected = s;
                                d?.assignedBusinessFunctions?.forEach((bf) => {
                                    if (bf?.id === funcId) {
                                        bf.isSelected = s;
                                    }
                                });
                            }
                        });
                    }
                });
            }
        }
    });
    const allBusinessServicesChecked = jsonData?.assignedBusinessServices?.every(service => service?.isAll); 
    if (allBusinessServicesChecked) {
        $("#selectAll").prop("checked", allBusinessServicesChecked);
        jsonData.isAll = true;
    } else {
        jsonData.isAll = false;
    }  
    $("#treeview").empty();
    $('#textProperties').val(JSON.stringify(jsonData));
    createTreeView($("#treeview"), jsonData, s, id, funcId, infraId);
}
$('#selectAll').on('change', function () {
    let check = this.checked;
    check ? JsonAllTreeView(true) : JsonAllTreeView(false);
});
$('#expand').on('toggle', function () {
    let open = this.open;
    open ? SelectAllTreeViewExpended(true) : SelectAllTreeViewExpended(false)
});
function JsonAllTreeView(chk) {  
    $('.selecttree').prop('checked', chk);
    jsonData?.assignedBusinessServices?.forEach(service => {
        service.isAll = chk;
        service.isPartial = chk;
        service?.assignedBusinessFunctions?.forEach(func => {
            func.isAll = chk;
            func.isPartial = chk;
            func?.assignedInfraObjects?.forEach(infra => {
                infra.isSelected = chk;
            });
        });
    });
    jsonData.isAll = chk;
    $("#treeview").empty();
    $('#textProperties').val(JSON.stringify(jsonData));
    createTreeView($("#treeview"), jsonData);
    $("#treeview").enable = false;
}
async function createTreeView(container, userInfraObject, chk, id, funcId, infraId) {

    if (userInfraObject?.isAll === true) {
        $("#selectAll").prop('checked', true);
    } else {
        $("#selectAll").prop('checked', false);
    }
    if (userInfraObject?.assignedBusinessServices !== undefined && userInfraObject?.assignedBusinessServices?.length > 0) {
        userInfraObject?.assignedBusinessServices?.forEach((service) => {
            let serviceDetails = $('<details open></details>');
            const serviceSummary = $('<summary></summary>');
            const serviceCheckbox = $('<input type="checkbox" class="form-check-input selecttree" name="bs"  onchange="selectTree(this)">');
            serviceCheckbox.attr('businessId', service.id);
            serviceCheckbox.prop('checked', service.isAll);
            serviceSummary.append(serviceCheckbox);
            serviceSummary.append(' ' + service.name);

            if (service?.isAll) {
                serviceDetails = $('<details open></details>');
            } else if (service?.isPartial) {
                serviceDetails = $('<details open></details>');
            } else if (service?.id == id) {
                serviceDetails = $('<details open></details>');
            } else {
                serviceDetails = $('<details></details>');
            }
            service?.assignedBusinessFunctions?.forEach((f) => {
                if (f?.id == funcId || f?.isAll == true) {
                    serviceDetails = $('<details open></details>');
                }
                f?.assignedInfraObjects?.forEach((infra) => {
                    if (infra?.isSelected == true) {
                        serviceDetails = $('<details open></details>');
                    } else if (infra?.id == infraId) {
                        serviceDetails = $('<details open></details>');
                    }
                    if (infra?.id == infraId && infra?.isSelected == true) {
                        serviceDetails = $('<details open></details>');
                        $("#tree-error").text("Select at least one infraobject").css("color", "#FF0000").css("font-size", "11px")
                        return false;
                    } else {
                        $("#tree-error").text("")
                        return true;
                    }
                });
            })
            if (service?.assignedBusinessFunctions?.length > 0) {
                const functionList = $('<ul class="tree"></ul>');
                createFunctions(functionList, service?.assignedBusinessFunctions, service.id);
                serviceDetails.append(functionList);
            }
            serviceDetails.append(serviceSummary);
            container.append(serviceDetails[0]);
        });
    }
}
function createFunctions(container, functions, id) {
    functions.forEach((func) => {
        const functionDetails = $('<details open></details>');
        const functionSummary = $('<summary></summary>');
        const functionCheckbox = $('<input type="checkbox" class="form-check-input selecttree " name="bf" onchange="selectTree(this)">');
        functionCheckbox.prop('checked', func?.isAll);
        functionCheckbox.attr('businessId', id);
        functionCheckbox.attr('functionId', func?.id);
        functionSummary.append(functionCheckbox);
        functionSummary.append(' ' + func?.name);
        if (func?.assignedInfraObjects?.length > 0) {
            const objectList = $('<ul class="tree"></ul>');
            createObjects(objectList, func?.assignedInfraObjects, id, func?.id);
            functionDetails.append(objectList);
        }
        functionDetails.append(functionSummary);
        container.append(functionDetails[0]);
    });
}
function createObjects(container, objects, parentId, functionId) {
    objects.forEach((obj) => {
        const objectDetails = $('<details open></details>'); // Add the "open" attribute
        const objectSummary =
            $('<summary style="list-style-type: none;" class="rightarrow"></summary>'); // Add inline style to hide the arrow
        const objectCheckbox = $('<input type="checkbox" name="infra" class="form-check-input selecttree" onchange="selectTree(this)">');
        objectCheckbox.prop('checked', obj?.isSelected);
        objectCheckbox.attr('businessId', parentId);
        objectCheckbox.attr('functionId', functionId);
        objectCheckbox.attr('infraId', obj?.id);
        objectSummary.append(objectCheckbox);
        objectSummary.append(' ' + obj?.name);
        objectDetails.append(objectSummary);
        container.append(objectDetails[0]);
    });
}
