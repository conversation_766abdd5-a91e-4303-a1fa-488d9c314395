﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.SiteModel.SiteViewModel
@{
    ViewData["Title"] = "Site";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title" >
                <i class="cp-web"></i><span>
                    Site 
                </span>
            </h6>
            <form class="d-flex" method="get">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown" title="Filter">
                            <span data-bs-toggle="dropdown"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="type=" id="Type">
                                        <label class="form-check-label" for="Type">
                                            Type
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="companyname=" id="CompanyName">
                                        <label class="form-check-label" for="CompanyName">
                                            Company Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="location=" id="Location">
                                        <label class="form-check-label" for="Location">
                                            Location
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="platformtype=" id="PlatformType">
                                        <label class="form-check-label" for="PlatformType">
                                            Platform Type
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm"  data-bs-toggle="modal" data-bs-target="#CreateModal" id="create"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="tblSite" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                <tr>
                    <th class="SrNo_th">Sr. No.</th>
                    <th>Name</th>
                    <th>Type</th>
                    <th >Company Name</th>
                    <th >Location</th>
                        <th>Platform Type</th>
                    <th class="Action-th" >Action</th>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>
<div id="configurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<div id="configurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<!--Modal Create-->
<div class="modal fade " id="CreateModal" data-bs-backdrop="static" aria-labelledby="configureModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>
<!--Modal Delete-->
<div class="modal fade" id="DeleteModal" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="Delete" />
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="/js/Configuration/Sites/Site/site.js"></script>
