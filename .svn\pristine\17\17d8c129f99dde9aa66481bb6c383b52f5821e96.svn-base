using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class DataSyncOptionsRepository : BaseRepository<DataSyncOptions>, IDataSyncOptionsRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public DataSyncOptionsRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }
    public Task<bool> IsDataSyncNameUnique(string name)
    {
        var matches = _dbContext.DataSyncs.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }
    public override async Task<PaginatedResult<DataSyncOptions>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<DataSyncOptions> specification, string sortColumn, string sortOrder)
    {
        return await Entities.Specify(specification).DescOrderById()
            .Select(x=> new DataSyncOptions
            {
                Id=x.Id,
                ReferenceId=x.ReferenceId,
                Name=x.Name,
                ReplicationType=x.ReplicationType,
                Properties=x.Properties
            }).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
}