﻿ @using ContinuityPatrol.Shared.Services.Helper
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()
<link href="~/css/switch_slide_toggle.css" rel="stylesheet" />
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-drift-profile-list"></i><span>Drift Profile</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="profileSearchInp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" aria-expanded="false" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="profileNames">
                                        <label class="form-check-label" for="profileNames">
                                            Name
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <button type="button" class="btn btn-primary btn-sm  " id="profileCreateButton" data-bs-toggle="modal" data-bs-target="#profileCreateModal"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table class="datatable table table-hover dataTable no-footer" id="driftProfileTable" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Profile Name</th>
                        <th class="Action-th">Action</th>
                    </tr>
                </thead>
                <tbody>
                    
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="modal fade" id="profileCreateModal" tabindex="-1" aria-labelledby="profileCreateModal" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-drift-profile-list"></i><span>Drift Profile Configuration</span></h6>
                <button type="button" class="btn-close profileClose" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-4">
                        <div class="form-group">
                            <label class="form-label">
                                Profile
                                Name
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-profile-name"></i></span>
                                <input type="text" class="form-control" placeholder="Enter Profile Name" id="profileName" maxlength="100" autocomplete="off" />
                            </div>
                            <span id="profileNameError"></span>
                        </div>
                        <div class="card Card_Design_None mb-0" style="box-shadow:0 .125rem .25rem rgb(0 0 0 / 15%)!important">
                            <div class="card-header header">
                                <h6 class="sub-title">Parameters List</h6>
                            </div>
                            <div class="card-body p-0 pe-2" style="height:calc(100vh - 320px);overflow:auto">
                                <div class="form-control">
                                    <div class="input-group">
                                        <input class="form-control " placeholder="Search" type="text" id="parameterListSearch"  autocomplete="off"/>
                                        <div class="input-group-text" role="button">
                                            <div class="dropdown">
                                                <span data-bs-toggle="dropdown" aria-expanded="false" title="Filter"><i class="cp-filter"></i></span>
                                                <ul class="dropdown-menu filter-dropdown">
                                                    <li>
                                                        <h6 class="dropdown-header">Filter Search</h6>
                                                    </li>
                                                    <li class="dropdown-item">
                                                        <div>
                                                            <input class="form-check-input" type="checkbox" value="" id="ProfileModalName">
                                                            <label class="form-check-label" for="ProfileModalName">
                                                                Name
                                                            </label>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="profileNodataParameter">
                                   
                                </div> 
                                <span id="profileListCheckboxError"></span>
                            </div>
                            <div class="card-footer text-center">
                                <button class="btn btn-sm btn-success" id="profileListCheck">Move<i class="ms-1 cp-right-linearrow align-middle"></i></button>
                            </div>
                        </div>
                    </div>
                    <div class="col-8">
                        <div class="card Card_Design_None mb-0" style="box-shadow:0 .125rem .25rem rgb(0 0 0 / 15%)!important">
                            <div class="card-header header">
                                <h6 class="sub-title">Profile Parameters</h6>
                                @* <p class="mb-0">Auto Align</p> *@
                            </div>
                            <div class="card-body p-0 px-2" style="height:calc(100vh - 205px);overflow:auto">
                                <div id="addProfileData">
                                   
                                </div>
                                 <span id="profileMainCheckboxError"></span>
                            </div>
                           
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm profileClose" >Cancel</button>
                    @* <button type="button" class="btn btn-danger btn-sm" id="profile_validate">Validate</button> *@
                   @*  <button type="button" class="btn btn-secondary btn-sm " id="profile_reset">Reset</button> *@
                    <button type="button" class="btn btn-primary btn-sm" title="" id="addProfileSave">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- modal thershold-->
<div class="modal fade" id="profileThersholdModal" tabindex="-1" aria-labelledby="profileThersholdModal" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm modal-dialog-centered">
       <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-drift-configuration"></i><span>Thershold</span></h6>
                <button type="button" class="btn-close profileThersholdClose" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <form class="w-100">
                    <div class="form-group " >
                        <label class="form-label">Threshold (Optional)</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-threshold"></i></span>
                            <input type="text" class="form-control" placeholder="Enter Threshold" maxlength="200" id="profileParameterThreshold" autocomplete="off" />
                            
                        </div>
                        <span id="profileParameterThresholdError"></span>
                    </div>
                    <div class="form-group d-none">
                        <label class="form-label">Thershold Operator (Optional) </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-threshold"></i></span>
                            <select class="form-select-modal" data-placeholder="Select thershold operator" id="profileParameterThresholdOperator">
                                <option value=""></option>
                                <option value="<"> < </option>
                                <option value=">"> > </option>
                                <option value=">="> >= </option>
                                <option value="<="> <= </option>
                                <option value="=< & >="> =< & >= </option>
                                <option value="=< || >="> =< || >= </option>
                           @*      <option value="="> = </option> *@
                              
                           </select>

                        </div>
                        <span id="  "></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="profileThersholdAdd">Add</button>
            </div>
   </div>  
   </div>
</div>
<!--Modal Delete-->
<div class="modal fade" id="profileDeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">

            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
            </div>
            <div class="modal-body  pt-0">
                <div class="text-center">
                    <h4>Are you sure?</h4>
                    <p class="d-flex align-items-center justify-content-center gap-1">
                        You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="profileOverallDeletedId"></span>
                        data?
                    </p>

                </div>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="#profileDeleteModal" data-bs-toggle="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="profileOverallConfirmDeleteButton">Yes</button>
            </div>

        </div>
    </div>
</div>
<div id="profileDriftCreate" data-create-permission="@WebHelper.CurrentSession.Permissions.Drift.CreateAndEdit" aria-hidden="true"></div>
<div id="profileDriftDelete" data-delete-permission="@WebHelper.CurrentSession.Permissions.Drift.Delete" aria-hidden="true"></div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Drift/Drift Configuration/Drift Profile/DriftProfile.js"></script>

<script src="~/js/common/slide_toggle.js"></script>