﻿using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.Create;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.SendTestEmail;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.SmtpConfigurationModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ValidationException = ContinuityPatrol.Shared.Core.Exceptions.ValidationException;

namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class SmtpConfigurationController : BaseController
{
    private readonly ILogger<SmtpConfigurationController> _logger;
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;

    public SmtpConfigurationController(ILogger<SmtpConfigurationController> logger, IDataProvider dataProvider, IMapper mapper)
    {
        _logger = logger;
        _dataProvider = dataProvider;
        _mapper = mapper;
    }

    public IActionResult List()
    {
        return View();
    }

    [HttpGet]
    public async Task<IActionResult> GetSmtpList()
    {
        _logger.LogDebug("Entering GetSmtpList method in SmtpConfiguration");
        try
        {
            var configurationList = await _dataProvider.SmtpConfiguration.GetSmtpConfigurationList();
            _logger.LogDebug("Successfully retrieved smtp list in SmtpConfiguration");
            return Json(configurationList);

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on smtp configuration while retrieving the smtp list.", ex);
            //TempData.NotifyWarning(ex.Message);
            return Json(ex.Message);

        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> SendTestMail(SmtpConfigurationViewModel sendTestEmailCommand)
    {
        _logger.LogDebug("Entering SendTestMail method in SmtpConfiguration");
        try
        {
            sendTestEmailCommand.UserName = sendTestEmailCommand.UserName.Length < 60 ? SecurityHelper.Encrypt(sendTestEmailCommand.UserName) : sendTestEmailCommand.UserName;


            if (sendTestEmailCommand.IsPasswordLess)
            {

                sendTestEmailCommand.Password = string.Empty;
            }
            else
            {
                sendTestEmailCommand.Password = sendTestEmailCommand.Password.Length < 60 ? SecurityHelper.Encrypt(sendTestEmailCommand.Password) : sendTestEmailCommand.Password;
            }

            var createCommand = _mapper.Map<SendTestEmailCommand>(sendTestEmailCommand);
            var result = await _dataProvider.SmtpConfiguration.SendTestMail(createCommand);
            _logger.LogDebug($"Test email sent successfully using SMTP configuration with Username: {sendTestEmailCommand.UserName}");
            return Json(new { success = true, data = result });

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on smtp configuration page while processing test mail request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.CreateAndEdit)]
    [AntiXss]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateOrUpdate(SmtpConfigurationViewModel smtpConfigurationViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in SmtpConfiguration");
        try
        {
            var id = Request.Form["Id"].ToString();

            smtpConfigurationViewModel.CompanyId = LoggedInUserCompanyId;

            smtpConfigurationViewModel.UserName = smtpConfigurationViewModel.UserName.Length < 60 ? SecurityHelper.Encrypt(smtpConfigurationViewModel.UserName) : smtpConfigurationViewModel.UserName;

            if (smtpConfigurationViewModel.IsPasswordLess)
            {

                smtpConfigurationViewModel.Password = string.Empty;
            }
            else
            {
                smtpConfigurationViewModel.Password = smtpConfigurationViewModel.Password.Length < 60 ? SecurityHelper.Encrypt(smtpConfigurationViewModel.Password) : smtpConfigurationViewModel.Password;
            }

            if (id.IsNullOrWhiteSpace())
            {
                var createCommand = _mapper.Map<CreateSmtpConfigurationCommand>(smtpConfigurationViewModel);
                var result = await _dataProvider.SmtpConfiguration.CreateAsync(createCommand);
                _logger.LogDebug($"Creating SmtpConfiguration '{createCommand.UserName}'");
                TempData.NotifySuccess(result.Message);
            }
            else
            {
                var updateCommand = _mapper.Map<UpdateSmtpConfigurationCommand>(smtpConfigurationViewModel);
                var result = await _dataProvider.SmtpConfiguration.UpdateAsync(updateCommand);
                _logger.LogDebug($"Updating SmtpConfiguration '{updateCommand.UserName}'");
                TempData.NotifySuccess(result.Message);
            }
            _logger.LogDebug("CreateOrUpdate operation completed successfully in SmtpConfiguration, returning view.");
            return RedirectToAction("List", "Settings", new { Area = "Admin" });
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on smtp configuration page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on smtp configuration page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    [AllowAnonymous]
    [HttpGet]
    public JsonResult DecryptPassword(string password)
    {
        _logger.LogDebug("Entering DecryptPassword method in SmtpConfiguration");

        if (password.IsNullOrWhiteSpace())
        {
            _logger.LogDebug("Password is null or empty in DecryptPassword method on SmtpConfiguration");
            return Json(new { decrypt = "" });
        }

        try
        {
            var decryption = SecurityHelper.Decrypt($"{password}");
            _logger.LogDebug("Successfully decrypted password in SmtpConfiguration");
            return Json(new { decrypt = decryption });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on smtp configuration while decrypting the password.", ex);
            return Json("");
        }
    }
}

