﻿using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Queries;

public class GetWorkflowActionPaginatedListQueryHandlerTests : IClassFixture<WorkflowActionFixture>
{
    private readonly Mock<IWorkflowActionRepository> _mockWorkflowActionRepository;

    private readonly GetWorkflowActionPaginatedListQueryHandler _handler;

    public GetWorkflowActionPaginatedListQueryHandlerTests(WorkflowActionFixture workflowActionFixture)
    {
        var workflowActionNewFixture = workflowActionFixture;

        workflowActionNewFixture.WorkflowActions[0].ActionName = "Action_Test";
        workflowActionNewFixture.WorkflowActions[0].Properties = "{\"Name\": \"admin\", \"password\": \"Admin@123\"}";
        workflowActionNewFixture.WorkflowActions[0].Version = "6.0.2";


        workflowActionNewFixture.WorkflowActions[1].ActionName = "Workflow_Testing";
        workflowActionNewFixture.WorkflowActions[1].Properties = "{\"Name\": \"cpadmin\", \"password\": \"Admin@4321\"}";
        workflowActionNewFixture.WorkflowActions[1].Version = "6.0.6";

        _mockWorkflowActionRepository = WorkflowActionRepositoryMocks.GetPaginatedWorkflowActionRepository(workflowActionNewFixture.WorkflowActions);

        _handler = new GetWorkflowActionPaginatedListQueryHandler(workflowActionNewFixture.Mapper, _mockWorkflowActionRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowActionPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowActionListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedWorkflowActions_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetWorkflowActionPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Test" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowActionListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<WorkflowActionListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].ActionName.ShouldBe("Action_Test");

        result.Data[0].Version.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Properties.ShouldBe("{\"Name\": \"admin\", \"password\": \"Admin@123\"}");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetWorkflowActionPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowActionListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_WorkflowActions_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetWorkflowActionPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "actionname=Test;properties={\"Name\": \"cpadmin\", \"password\": \"Admin@4321\"};version=6.0.6" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowActionListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<WorkflowActionListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].ActionName.ShouldBe("Action_Test");

        result.Data[0].Version.ShouldBe("6.0.2");

        result.Data[0].Properties.ShouldBe("{\"Name\": \"admin\", \"password\": \"Admin@123\"}");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetWorkflowActionPaginatedListQuery(), CancellationToken.None);

        _mockWorkflowActionRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}