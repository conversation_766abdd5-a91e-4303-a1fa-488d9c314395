using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixApprovalModel;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetList;

public class
    GetApprovalMatrixApprovalListQueryHandler : IRequestHandler<GetApprovalMatrixApprovalListQuery,
        List<ApprovalMatrixApprovalListVm>>
{
    private readonly IApprovalMatrixApprovalRepository _approvalMatrixApprovalRepository;
    private readonly IMapper _mapper;

    public GetApprovalMatrixApprovalListQueryHandler(IMapper mapper,
        IApprovalMatrixApprovalRepository approvalMatrixApprovalRepository)
    {
        _mapper = mapper;
        _approvalMatrixApprovalRepository = approvalMatrixApprovalRepository;
    }

    public async Task<List<ApprovalMatrixApprovalListVm>> Handle(GetApprovalMatrixApprovalListQuery request,
        CancellationToken cancellationToken)
    {
        var approvalMatrixApprovals = await _approvalMatrixApprovalRepository.ListAllAsync();

        if (approvalMatrixApprovals.Count <= 0) return new List<ApprovalMatrixApprovalListVm>();

        return _mapper.Map<List<ApprovalMatrixApprovalListVm>>(approvalMatrixApprovals);
    }
}