﻿namespace ContinuityPatrol.Application.Features.IncidentDaily.Commands.Create;

public class
    CreateIncidentDailyCommandHandler : IRequestHandler<CreateIncidentDailyCommand, CreateIncidentDailyResponse>
{
    private readonly IIncidentDailyRepository _incidentDailyRepository;
    private readonly IMapper _mapper;

    public CreateIncidentDailyCommandHandler(IIncidentDailyRepository incidentDailyRepository, IMapper mapper)
    {
        _incidentDailyRepository = incidentDailyRepository;
        _mapper = mapper;
    }

    public async Task<CreateIncidentDailyResponse> Handle(CreateIncidentDailyCommand request,
        CancellationToken cancellationToken)
    {
        var incidentDaily = _mapper.Map<Domain.Entities.IncidentDaily>(request);

        incidentDaily = await _incidentDailyRepository.AddAsync(incidentDaily);

        var response = new CreateIncidentDailyResponse
        {
            Message = Message.Create(nameof(Domain.Entities.IncidentDaily), incidentDaily.ParentBusinessServiceId),

            Id = incidentDaily.ReferenceId
        };
        return response;
    }
}