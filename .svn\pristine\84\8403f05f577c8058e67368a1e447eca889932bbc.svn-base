using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class CyberAirGapRepositoryTests : IClassFixture<CyberAirGapFixture>
{
    private readonly CyberAirGapFixture _cyberAirGapFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberAirGapRepository _repository;

    public CyberAirGapRepositoryTests(CyberAirGapFixture cyberAirGapFixture)
    {
        _cyberAirGapFixture = cyberAirGapFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberAirGapRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var cyberAirGap = _cyberAirGapFixture.CyberAirGapDto;

        // Act
        var result = await _repository.AddAsync(cyberAirGap);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(cyberAirGap.Name, result.Name);
        Assert.Equal(cyberAirGap.SourceSiteId, result.SourceSiteId);
        Assert.Equal(cyberAirGap.TargetSiteId, result.TargetSiteId);
        Assert.Equal(cyberAirGap.SourceComponentId, result.SourceComponentId);
        Assert.Equal(cyberAirGap.TargetComponentId, result.TargetComponentId);
        Assert.Single(_dbContext.CyberAirGaps);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var cyberAirGap = _cyberAirGapFixture.CyberAirGapDto;
        await _repository.AddAsync(cyberAirGap);

        cyberAirGap.Name = "UpdatedName";
        cyberAirGap.Status = "UpdatedStatus";
        cyberAirGap.WorkflowStatus = "UpdatedWorkflowStatus";
        cyberAirGap.IsAttached = false;

        // Act
        var result = await _repository.UpdateAsync(cyberAirGap);

        // Assert
        Assert.Equal("UpdatedName", result.Name);
        Assert.Equal("UpdatedStatus", result.Status);
        Assert.Equal("UpdatedWorkflowStatus", result.WorkflowStatus);
        Assert.False(result.IsAttached);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var cyberAirGap = _cyberAirGapFixture.CyberAirGapDto;
        await _repository.AddAsync(cyberAirGap);

        // Act
        var result = await _repository.DeleteAsync(cyberAirGap);

        // Assert
        Assert.Equal(cyberAirGap.Name, result.Name);
        Assert.Empty(_dbContext.CyberAirGaps);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var cyberAirGap = _cyberAirGapFixture.CyberAirGapDto;
        var addedEntity = await _repository.AddAsync(cyberAirGap);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
        Assert.Equal(addedEntity.SourceSiteId, result.SourceSiteId);
        Assert.Equal(addedEntity.TargetSiteId, result.TargetSiteId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var cyberAirGap = _cyberAirGapFixture.CyberAirGapDto;
        await _repository.AddAsync(cyberAirGap);

        // Act
        var result = await _repository.GetByReferenceIdAsync(cyberAirGap.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(cyberAirGap.ReferenceId, result.ReferenceId);
        Assert.Equal(cyberAirGap.Name, result.Name);
        Assert.Equal(cyberAirGap.SourceSiteId, result.SourceSiteId);
        Assert.Equal(cyberAirGap.TargetSiteId, result.TargetSiteId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var cyberAirGaps = _cyberAirGapFixture.CyberAirGapList;
        await _repository.AddRangeAsync(cyberAirGaps);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(cyberAirGaps.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var cyberAirGap = _cyberAirGapFixture.CyberAirGapDto;
        cyberAirGap.Name = "ExistingName";
        await _repository.AddAsync(cyberAirGap);

        // Act
        var result = await _repository.IsNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var cyberAirGaps = _cyberAirGapFixture.CyberAirGapList;
        await _repository.AddRangeAsync(cyberAirGaps);

        // Act
        var result = await _repository.IsNameExist("NonExistentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var cyberAirGap = _cyberAirGapFixture.CyberAirGapDto;
        cyberAirGap.Name = "SameName";
        await _repository.AddAsync(cyberAirGap);

        // Act
        var result = await _repository.IsNameExist("SameName", cyberAirGap.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetAirGapBySiteId Tests

    [Fact]
    public async Task GetAirGapBySiteId_ShouldReturnAirGapsForSourceSite()
    {
        // Arrange
        var siteId = "SITE_001";
        var cyberAirGaps = new List<CyberAirGap>
        {
            new CyberAirGap 
            { 
                Name = "AirGap1",
                SourceSiteId = siteId,
                TargetSiteId = "SITE_002",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGap 
            { 
                Name = "AirGap2",
                SourceSiteId = "SITE_003",
                TargetSiteId = siteId,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGap 
            { 
                Name = "AirGap3",
                SourceSiteId = "SITE_004",
                TargetSiteId = "SITE_005",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(cyberAirGaps);

        // Act
        var result = await _repository.GetAirGapBySiteId(siteId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.SourceSiteId == siteId || x.TargetSiteId == siteId));
    }

    [Fact]
    public async Task GetAirGapBySiteId_ShouldReturnEmpty_WhenNoAirGapsForSite()
    {
        // Arrange
        var cyberAirGaps = _cyberAirGapFixture.CyberAirGapList;
        await _repository.AddRangeAsync(cyberAirGaps);

        // Act
        var result = await _repository.GetAirGapBySiteId("NON_EXISTENT_SITE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetAirGapByServerId Tests

    [Fact]
    public async Task GetAirGapByServerId_ShouldReturnAirGapsForServer()
    {
        // Arrange
        var serverId = "SERVER_001";
        var cyberAirGaps = new List<CyberAirGap>
        {
            new CyberAirGap
            {
                Name = "AirGap1",
                Source = serverId,
                Target = "SERVER_002",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGap
            {
                Name = "AirGap2",
                Source = "SERVER_003",
                Target = serverId,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGap
            {
                Name = "AirGap3",
                Source = "SERVER_004",
                Target = "SERVER_005",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(cyberAirGaps);

        // Act
        var result = await _repository.GetAirGapByServerId(serverId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.Source == serverId || x.Target == serverId));
    }

    [Fact]
    public async Task GetAirGapByServerId_ShouldReturnEmpty_WhenNoAirGapsForServer()
    {
        // Arrange
        var cyberAirGaps = _cyberAirGapFixture.CyberAirGapList;
        await _repository.AddRangeAsync(cyberAirGaps);

        // Act
        var result = await _repository.GetAirGapByServerId("NON_EXISTENT_SERVER");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetAirGapByComponentId Tests

    [Fact]
    public async Task GetAirGapByComponentId_ShouldReturnAirGapsForComponent()
    {
        // Arrange
        var componentId = "COMPONENT_001";
        var cyberAirGaps = new List<CyberAirGap>
        {
            new CyberAirGap
            {
                Name = "AirGap1",
                SourceComponentId = componentId,
                TargetComponentId = "COMPONENT_002",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGap
            {
                Name = "AirGap2",
                SourceComponentId = "COMPONENT_003",
                TargetComponentId = componentId,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGap
            {
                Name = "AirGap3",
                SourceComponentId = "COMPONENT_004",
                TargetComponentId = "COMPONENT_005",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(cyberAirGaps);

        // Act
        var result = await _repository.GetAirGapByComponentId(componentId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.SourceComponentId == componentId || x.TargetComponentId == componentId));
    }

    [Fact]
    public async Task GetAirGapByComponentId_ShouldReturnEmpty_WhenNoAirGapsForComponent()
    {
        // Arrange
        var cyberAirGaps = _cyberAirGapFixture.CyberAirGapList;
        await _repository.AddRangeAsync(cyberAirGaps);

        // Act
        var result = await _repository.GetAirGapByComponentId("NON_EXISTENT_COMPONENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Infrastructure Assignment Tests

    [Fact]
    public async Task Repository_ShouldHandleInfrastructureAssignments()
    {
        // Arrange
        var cyberAirGaps = new List<CyberAirGap>
        {
            new CyberAirGap
            {
                Name = "DatabaseAirGap",
                SourceSiteId = "DB_SITE_001",
                TargetSiteId = "DB_SITE_002",
                SourceComponentId = "DB_COMP_001",
                TargetComponentId = "DB_COMP_002",
                Source = "DB_SERVER_001",
                Target = "DB_SERVER_002",
                Port = 1433,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGap
            {
                Name = "WebAirGap",
                SourceSiteId = "WEB_SITE_001",
                TargetSiteId = "WEB_SITE_002",
                SourceComponentId = "WEB_COMP_001",
                TargetComponentId = "WEB_COMP_002",
                Source = "WEB_SERVER_001",
                Target = "WEB_SERVER_002",
                Port = 80,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGap
            {
                Name = "AppAirGap",
                SourceSiteId = "APP_SITE_001",
                TargetSiteId = "APP_SITE_002",
                SourceComponentId = "APP_COMP_001",
                TargetComponentId = "APP_COMP_002",
                Source = "APP_SERVER_001",
                Target = "APP_SERVER_002",
                Port = 8080,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(cyberAirGaps);

        // Act
        var dbInfra = await _repository.FindByFilterAsync(x => x.SourceSiteId.Contains("DB") || x.TargetSiteId.Contains("DB"));
        var webInfra = await _repository.FindByFilterAsync(x => x.SourceSiteId.Contains("WEB") || x.TargetSiteId.Contains("WEB"));
        var appInfra = await _repository.FindByFilterAsync(x => x.SourceSiteId.Contains("APP") || x.TargetSiteId.Contains("APP"));

        // Assert
        Assert.Single(dbInfra);
        Assert.Single(webInfra);
        Assert.Single(appInfra);
        Assert.Equal(1433, dbInfra.First().Port);
        Assert.Equal(80, webInfra.First().Port);
        Assert.Equal(8080, appInfra.First().Port);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var cyberAirGap = _cyberAirGapFixture.CyberAirGapList;
        var cyberAirGap1 = cyberAirGap[0] ;
        var cyberAirGap2 = cyberAirGap[1];

        cyberAirGap2.Name = "DifferentName";

        // Act
        var task1 = _repository.AddAsync(cyberAirGap1);
        var task2 = _repository.AddAsync(cyberAirGap2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.CyberAirGaps.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var cyberAirGaps = _cyberAirGapFixture.CyberAirGapList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(cyberAirGaps);
        var initialCount = cyberAirGaps.Count;

        var toUpdate = cyberAirGaps.Take(2).ToList();
        toUpdate.ForEach(x => x.Status = "Updated");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = cyberAirGaps.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Status == "Updated").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleWorkflowStatusFiltering()
    {
        // Arrange
        var cyberAirGaps = new List<CyberAirGap>
        {
            new CyberAirGap
            {
                Name = "AirGap1",
                WorkflowStatus = "Enabled",
                Status = "Active",
                IsAttached = true,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGap
            {
                Name = "AirGap2",
                WorkflowStatus = "Disabled",
                Status = "Inactive",
                IsAttached = false,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberAirGap
            {
                Name = "AirGap3",
                WorkflowStatus = "Pending",
                Status = "Pending",
                IsAttached = false,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(cyberAirGaps);

        // Act
        var enabledWorkflows = await _repository.FindByFilterAsync(x => x.WorkflowStatus == "Enabled");
        var disabledWorkflows = await _repository.FindByFilterAsync(x => x.WorkflowStatus == "Disabled");
        var pendingWorkflows = await _repository.FindByFilterAsync(x => x.WorkflowStatus == "Pending");

        // Assert
        Assert.Single(enabledWorkflows);
        Assert.Single(disabledWorkflows);
        Assert.Single(pendingWorkflows);
        Assert.True(enabledWorkflows.First().IsAttached);
        Assert.False(disabledWorkflows.First().IsAttached);
        Assert.False(pendingWorkflows.First().IsAttached);
    }

    #endregion
}
