using ContinuityPatrol.Application.Features.BulkImport.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Next;
using ContinuityPatrol.Application.Features.BulkImport.Commands.RollBack;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class BulkImportFixture : IDisposable
{
    public List<BulkImportOperationGroup> BulkImportOperationGroups { get; set; }
    public List<BulkImportActionResult> BulkImportActionResults { get; set; }
    public CreateBulkImportCommand CreateBulkImportCommand { get; set; }
    public NextBulkImportCommand NextBulkImportCommand { get; set; }
    public RollBackBulkImportCommand RollBackBulkImportCommand { get; set; }
    public IMapper Mapper { get; set; }

    public BulkImportFixture()
    {
        BulkImportOperationGroups = new List<BulkImportOperationGroup>
        {
            new BulkImportOperationGroup
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BulkImportOperationId = Guid.NewGuid().ToString(),
                InfraObjectName = "TestInfraObject",
                CompanyId = Guid.NewGuid().ToString(),
                Properties = "{\"ServerList\":[],\"DatabaseList\":[],\"ReplicationList\":[],\"InfraObject\":null}",
                ProgressStatus = "0/10",
                Status = "Pending",
                ErrorMessage = "",
                ConditionalOperation = 1,
                NodeId = "Node001",
                IsActive = true
            }
        };

        BulkImportActionResults = new List<BulkImportActionResult>
        {
            new BulkImportActionResult
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                NodeId = "Node001",
                NodeName = "TestNode",
                BulkImportOperationId = Guid.NewGuid().ToString(),
                BulkImportOperationGroupId = Guid.NewGuid().ToString(),
                EntityId = Guid.NewGuid().ToString(),
                EntityName = "TestEntity",
                EntityType = "Server",
                Status = "Pending",
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddHours(1),
                ErrorMessage = "",
                ConditionalOperation = 1,
                IsActive = true
            }
        };

        BulkImportOperationGroups = AutoBulkImportFixture.Create<List<BulkImportOperationGroup>>();
        BulkImportActionResults = AutoBulkImportFixture.Create<List<BulkImportActionResult>>();
        CreateBulkImportCommand = AutoBulkImportFixture.Create<CreateBulkImportCommand>();
        NextBulkImportCommand = AutoBulkImportFixture.Create<NextBulkImportCommand>();
        RollBackBulkImportCommand = AutoBulkImportFixture.Create<RollBackBulkImportCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<BulkImportOperationGroupProfile>();
            cfg.AddProfile<BulkImportActionResultProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoBulkImportFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customize<CreateBulkImportCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<NextBulkImportCommand>(c => c.With(b => b.GroupId, Guid.NewGuid().ToString()));

            fixture.Customize<RollBackBulkImportCommand>(c => c.With(b => b.GroupId, Guid.NewGuid().ToString()));

            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.IsActive, true));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.BulkImportOperationId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.InfraObjectName, "TestInfraObject"));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.Properties, "{\"ServerList\":[],\"DatabaseList\":[],\"ReplicationList\":[],\"InfraObject\":null}"));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.ProgressStatus, "0/10"));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.Status, "Pending"));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.ErrorMessage, ""));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.ConditionalOperation, 1));
            fixture.Customize<BulkImportOperationGroup>(c => c.With(b => b.NodeId, "Node001"));

            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.IsActive, true));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.NodeId, "Node001"));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.NodeName, "TestNode"));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.BulkImportOperationId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.BulkImportOperationGroupId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.EntityId, Guid.NewGuid().ToString()));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.EntityType, "Server"));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.Status, "Pending"));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.StartTime, DateTime.Now));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.EndTime, DateTime.Now.AddHours(1)));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.ErrorMessage, ""));
            fixture.Customize<BulkImportActionResult>(c => c.With(b => b.ConditionalOperation, 1));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
