namespace ContinuityPatrol.Application.Features.PageBuilder.Queries.GetNameUnique;

public class GetPageBuilderNameUniqueQueryHandler : IRequestHandler<GetPageBuilderNameUniqueQuery, bool>
{
    private readonly IPageBuilderRepository _pageBuilderRepository;

    public GetPageBuilderNameUniqueQueryHandler(IPageBuilderRepository pageBuilderRepository)
    {
        _pageBuilderRepository = pageBuilderRepository;
    }

    public async Task<bool> Handle(GetPageBuilderNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _pageBuilderRepository.IsNameExist(request.Name, request.Id);
    }
}