using ContinuityPatrol.Application.Features.DataSet.Commands.Create;
using ContinuityPatrol.Application.Features.DataSet.Commands.Delete;
using ContinuityPatrol.Application.Features.DataSet.Commands.Update;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetDataSetById;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetList;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetNames;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetRunQuery;
using ContinuityPatrol.Domain.ViewModels.DataSetModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DataSetFixture
{
    public CreateDataSetCommand CreateDataSetCommand { get; }
    public CreateDataSetResponse CreateDataSetResponse { get; }
    public UpdateDataSetCommand UpdateDataSetCommand { get; }
    public UpdateDataSetResponse UpdateDataSetResponse { get; }
    public DeleteDataSetCommand DeleteDataSetCommand { get; }
    public DeleteDataSetResponse DeleteDataSetResponse { get; }
    public DataSetDetailVm DataSetDetailVm { get; }
    public DataSetListVm DataSetListVm { get; }
    public DataSetNameVm DataSetNameVm { get; }
    public GetDataSetByIdVm GetDataSetByIdVm { get; }
    public DataSetRunQueryVm DataSetRunQueryVm { get; }
    public GetDataSetListQuery GetDataSetListQuery { get; }
    public GetDataSetDetailQuery GetDataSetDetailQuery { get; }
    public GetDataSetNameQuery GetDataSetNameQuery { get; }
    public GetDataSetNameUniqueQuery GetDataSetNameUniqueQuery { get; }
    public GetDataSetPaginatedListQuery GetDataSetPaginatedListQuery { get; }
    public GetDataSetRunQuery GetDataSetRunQuery { get; }
    public GetDataSetByIdQuery GetDataSetByIdQuery { get; }

    public DataSetFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateDataSetCommand>(c => c
            .With(b => b.DataSetName, "Enterprise Business Analytics Dataset")
            .With(b => b.Description, "Comprehensive dataset for enterprise business analytics and reporting")
            .With(b => b.StoredQuery, "SELECT * FROM Enterprise_Business_Metrics WHERE IsActive = 1")
            .With(b => b.TableAccessId, Guid.NewGuid().ToString())
            .With(b => b.PrimaryTableName, "Enterprise_Business_Metrics")
            .With(b => b.PrimaryTablePKColumn, "business_metric_id")
            .With(b => b.QueryType, "SELECT")
            .With(b => b.StoredProcedureName, ""));

        fixture.Customize<CreateDataSetResponse>(c => c
            .With(b => b.DataSetId, Guid.NewGuid().ToString())
            .With(b => b.Message, "Enterprise Business Analytics Dataset created successfully!")
           );

        fixture.Customize<UpdateDataSetCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.DataSetName, "Updated Enterprise Analytics Dataset")
            .With(b => b.Description, "Updated comprehensive dataset for enterprise analytics")
            .With(b => b.StoredQuery, "SELECT * FROM Enterprise_Business_Metrics WHERE IsActive = 1 AND LastModified >= DATEADD(day, -30, GETDATE())")
            .With(b => b.TableAccessId, Guid.NewGuid().ToString())
            .With(b => b.PrimaryTableName, "Enterprise_Business_Metrics")
            .With(b => b.PrimaryTablePKColumn, "business_metric_id")
            .With(b => b.QueryType, "SELECT")
            .With(b => b.StoredProcedureName, ""));

        fixture.Customize<UpdateDataSetResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Message, "Updated Enterprise Analytics Dataset updated successfully!")
           );

        fixture.Customize<DeleteDataSetCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<DeleteDataSetResponse>(c => c
            .With(b => b.IsActive, false)
            .With(b => b.Message, "Enterprise Analytics Dataset deleted successfully!")
            );

        fixture.Customize<DataSetDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.DataSetName, "Enterprise Detail Dataset")
            .With(b => b.Description, "Detailed enterprise dataset for comprehensive analysis")
            .With(b => b.StoredQuery, "SELECT TOP 1000 * FROM Enterprise_Detail_Metrics ORDER BY CreatedDate DESC")
            .With(b => b.TableAccessId, Guid.NewGuid().ToString())
            .With(b => b.PrimaryTableName, "Enterprise_Detail_Metrics")
            .With(b => b.PrimaryTablePKColumn, "detail_metric_id")
            .With(b => b.QueryType, "SELECT")
            .With(b => b.StoredProcedureName, ""));

        fixture.Customize<DataSetListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.DataSetName, "Enterprise List Dataset")
            .With(b => b.Description, "Enterprise dataset for list operations")
            .With(b => b.StoredQuery, "SELECT Id, Name, Status FROM Enterprise_List_View")
            .With(b => b.TableAccessId, Guid.NewGuid().ToString())
            .With(b => b.PrimaryTableName, "Enterprise_List_View")
            .With(b => b.PrimaryTablePKColumn, "Id")
            .With(b => b.QueryType, "SELECT")
            .With(b => b.StoredProcedureName, ""));

        fixture.Customize<DataSetNameVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.DataSetName, "Enterprise Name Dataset"));

        fixture.Customize<GetDataSetByIdVm>(c => c
           
            .With(b => b.QueryResult, "SELECT * FROM Enterprise_Query_Metrics WHERE Active = 1")
           );

        fixture.Customize<DataSetRunQueryVm>(c => c
            .With(b => b.TableValue, "Enterprise_Run_Query_Results_JSON_Data"));

        fixture.Customize<GetDataSetDetailQuery>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<GetDataSetNameUniqueQuery>(c => c
            .With(b => b.DataSetName, "Enterprise Unique Dataset")
            .With(b => b.DataSetId, Guid.NewGuid().ToString()));

        fixture.Customize<GetDataSetPaginatedListQuery>(c => c
            .With(b => b.PageNumber, 1)
            .With(b => b.PageSize, 10)
            .With(b => b.SearchString, "Enterprise")
            .With(b => b.SortColumn,  "DataSetName"));

        fixture.Customize<GetDataSetRunQuery>(c => c
            .With(b => b.Table, "Enterprise_Sample_Table"));

        fixture.Customize<GetDataSetByIdQuery>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        CreateDataSetCommand = fixture.Create<CreateDataSetCommand>();
        CreateDataSetResponse = fixture.Create<CreateDataSetResponse>();
        UpdateDataSetCommand = fixture.Create<UpdateDataSetCommand>();
        UpdateDataSetResponse = fixture.Create<UpdateDataSetResponse>();
        DeleteDataSetCommand = fixture.Create<DeleteDataSetCommand>();
        DeleteDataSetResponse = fixture.Create<DeleteDataSetResponse>();
        DataSetDetailVm = fixture.Create<DataSetDetailVm>();
        DataSetListVm = fixture.Create<DataSetListVm>();
        DataSetNameVm = fixture.Create<DataSetNameVm>();
        GetDataSetByIdVm = fixture.Create<GetDataSetByIdVm>();
        DataSetRunQueryVm = fixture.Create<DataSetRunQueryVm>();
        GetDataSetListQuery = fixture.Create<GetDataSetListQuery>();
        GetDataSetDetailQuery = fixture.Create<GetDataSetDetailQuery>();
        GetDataSetNameQuery = fixture.Create<GetDataSetNameQuery>();
        GetDataSetNameUniqueQuery = fixture.Create<GetDataSetNameUniqueQuery>();
        GetDataSetPaginatedListQuery = fixture.Create<GetDataSetPaginatedListQuery>();
        GetDataSetRunQuery = fixture.Create<GetDataSetRunQuery>();
        GetDataSetByIdQuery = fixture.Create<GetDataSetByIdQuery>();
    }
}
