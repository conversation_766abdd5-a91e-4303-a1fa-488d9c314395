﻿using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessFunction.Commands;

public class CreateBusinessFunctionTests : IClassFixture<BusinessFunctionFixture>
{
    private readonly BusinessFunctionFixture _businessFunctionFixture;
    private readonly Mock<IBusinessFunctionRepository> _mockBusinessFunctionRepository;
    private readonly CreateBusinessFunctionCommandHandler _handler;

    public CreateBusinessFunctionTests(BusinessFunctionFixture businessFunctionFixture)
    {
        _businessFunctionFixture = businessFunctionFixture;

        var mockPublisher = new Mock<IPublisher>();

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        _mockBusinessFunctionRepository = BusinessFunctionRepositoryMocks.CreateBusinessFunctionRepository(_businessFunctionFixture.BusinessFunctions);

        _handler = new CreateBusinessFunctionCommandHandler(_businessFunctionFixture.Mapper, mockPublisher.Object, _mockBusinessFunctionRepository.Object, mockLoggedInUserService.Object);
    }

    [Fact]
    public async Task Handle_IncreaseBusinessFunctionCount_When_BusinessFunctionCreated()
    {
        await _handler.Handle(_businessFunctionFixture.CreateBusinessFunctionCommand, CancellationToken.None);

        var allCategories = await _mockBusinessFunctionRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_businessFunctionFixture.BusinessFunctions.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateBusinessFunctionResponse_When_BusinessFunctionCreated()
    {
        var result = await _handler.Handle(_businessFunctionFixture.CreateBusinessFunctionCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateBusinessFunctionResponse));

        result.BusinessFunctionId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_businessFunctionFixture.CreateBusinessFunctionCommand, CancellationToken.None);

        _mockBusinessFunctionRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.BusinessFunction>()), Times.Once);
    }
}