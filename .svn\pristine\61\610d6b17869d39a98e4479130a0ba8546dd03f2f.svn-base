﻿using ContinuityPatrol.Application.Contracts.Job;
using Quartz;
using Quartz.Spi;

namespace ContinuityPatrol.Persistence.Job;

public class QuartzJobScheduler : IQuartzJobScheduler
{
    private readonly ISchedulerFactory _schedulerFactory;
    private readonly IJobFactory _jobFactory;
    private readonly ILogger<QuartzJobScheduler> _logger;
    // private IBusinessFunctionRepository _businessFunctionRepository;

    public QuartzJobScheduler(ISchedulerFactory schedulerFactory, ILogger<QuartzJobScheduler> logger, IJobFactory jobFactory)
    {
        _schedulerFactory = schedulerFactory;
        _logger = logger;
        _jobFactory = jobFactory;
        // _businessFunctionRepository = businessFunctionRepository;
    }//

    public async Task ScheduleJob<T>(string jobId, Dictionary<string, string> dynamicData) where T : IJob
    {
        _logger.LogInformation("Starting ScheduleJob.");

        var scheduler = await _schedulerFactory.GetScheduler();
        scheduler.JobFactory = _jobFactory;

        var jobKey = new JobKey(jobId, typeof(T).Name);


        if (!scheduler.IsStarted)
        {
            await scheduler.Start();
            _logger.LogInformation("Scheduler started.");
        }

        // Build JobDetail
        var job = JobBuilder.Create<T>()
            .WithIdentity($"{jobKey}-{Guid.NewGuid().ToString()}")
            .UsingJobData(new JobDataMap(dynamicData))
            .Build();

        // Trigger to run immediately (you can also use CronTrigger etc.)

        var trigger = TriggerBuilder.Create()
            .WithIdentity($"trigger-{jobId}-{Guid.NewGuid().ToString()}", typeof(T).Name)
            .StartAt(DateTimeOffset.UtcNow)
            .WithSimpleSchedule(x => x
                .WithRepeatCount(0)
                .WithMisfireHandlingInstructionFireNow())
            .Build();

        await scheduler.ScheduleJob(job, trigger);

        if (!scheduler.IsStarted)
        {
            await scheduler.Start();
            _logger.LogInformation("Scheduler started.");
        }

        _logger.LogInformation("Scheduled job {JobId} of type {JobType}", jobId, typeof(T).Name);
    }


    public async Task DeleteJob<T>(string jobId) where T : IJob
    {
        var scheduler = await _schedulerFactory.GetScheduler();
        var jobKey = new JobKey(jobId, typeof(T).Name);

        if (await scheduler.CheckExists(jobKey))
        {
            await scheduler.DeleteJob(jobKey);
            _logger.LogInformation("Deleted job {JobId} of type {JobType}", jobId, typeof(T).Name);
        }
        else
        {
            _logger.LogWarning("Job {JobId} of type {JobType} not found.", jobId, typeof(T).Name);
        }
    }

}