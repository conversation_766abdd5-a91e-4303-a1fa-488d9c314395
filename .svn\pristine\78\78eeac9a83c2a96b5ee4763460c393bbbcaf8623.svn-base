﻿using ContinuityPatrol.Application.Features.WorkflowActionType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionType.Queries
{
    public class GetWorkflowActionTypePaginatedListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IWorkflowActionTypeRepository> _mockWorkflowActionTypeRepository;
        private readonly GetWorkflowActionTypePaginatedListQueryHandler _handler;

        public GetWorkflowActionTypePaginatedListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockWorkflowActionTypeRepository = new Mock<IWorkflowActionTypeRepository>();
            _handler = new GetWorkflowActionTypePaginatedListQueryHandler(_mockMapper.Object, _mockWorkflowActionTypeRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedListOfWorkflowActionTypeListVm()
        {
            var query = new GetWorkflowActionTypePaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "Test"
            };

            var workflowActionTypes = new List<Domain.Entities.WorkflowActionType>
            {
                new Domain.Entities.WorkflowActionType {  },
                new Domain.Entities.WorkflowActionType {  }
            };

            _mockWorkflowActionTypeRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(workflowActionTypes.AsQueryable());

            _mockMapper
                .Setup(m => m.Map<WorkflowActionTypeListVm>(It.IsAny<Domain.Entities.WorkflowActionType>()))
                .Returns(new WorkflowActionTypeListVm {  });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(query.PageNumber, result.PageSize);
            Assert.Equal(query.PageSize, result.PageSize);
            Assert.True(result.Data.Count > 0);
            Assert.IsType<PaginatedResult<WorkflowActionTypeListVm>>(result);
        }
    }
}
