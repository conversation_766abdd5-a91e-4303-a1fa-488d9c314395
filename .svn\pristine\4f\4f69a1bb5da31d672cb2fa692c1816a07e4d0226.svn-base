﻿const workflowActionNameExists = "Admin/WorkflowAction/WorkflowActionNameExist";
const validateCpActionScript = "Admin/WorkflowAction/ValidateCpActionScript";
const deleteActionUrl = "Admin/WorkflowAction/ActionDelete"
const postactionurl = "Admin/WorkflowAction/CreateOrUpdate"
const lockactionurl = "Admin/WorkflowAction/LockCreateOrUpdate"
const lockStatusUpdateUrl = "Admin/WorkflowAction/LockStatusUpdate"
const saveasActionUrl = "Admin/WorkflowAction/SaveAsCreateOrUpdate"
let actionId = "";
let actionName = "";
let ExecuteTreeDataScriptJson;
let nodeId = "";
let validateScript = "";
let scriptData = "";

const controlOptions = {
    groups: [
        {
            id: 'custom',
            label: 'CP Custom Elements',
            elementOrder: ['database,server,replication'],
        },
        {
            id: 'common',
            label: 'Form Fields',
            elementOrder: [
                'text-input',
                'number',
                'select',
                'checkbox',
                'radio',
                'textarea',
                'date-input',
                'hidden',
                'upload',
                'button',
                'email'
            ],
        },
        {
            id: 'html',
            label: 'HTML Elements',
            elementOrder: ['header', 'paragraph', 'divider'],
        },
        {
            id: 'layout',
            label: 'Layout',
            elementOrder: ['header', 'paragraph', 'divider'],
        },
    ],
    groupOrder: ['common', 'custom', 'html', 'layout'],
    sortable: false,
    disable: {
        elements: ["upload"]
    },
    elements: [
        {
            tag: 'select',
            config: {
                label: 'Database',
                disabledAttrs: ['type', 'className', 'DatabaseTypeID'],//Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder', 'DatabaseType'],// disable delete button.
            },
            meta: {
                group: 'custom',
                id: 'database',
                icon: '', //〚〛
            },
            attrs: {
                name: "@@DBName",
                placeholder: "Select Option",
                className: 'form-select-modal-dynamic',
                type: 'select',
                required: true,
                DatabaseType: "all",
                DatabaseTypeID: " "
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Server',
                disabledAttrs: ['type', 'className', 'ServerRoleID', 'ServerTypeID'],
                lockedAttrs: ['required', 'name', 'ServerRole', 'ServerType', 'placeholder'],
            },
            meta: {
                group: 'custom',
                id: 'server',
                icon: '',//⌨
            },
            attrs: {
                name: "@@ServerName",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
                ServerRole: "",
                ServerType: "",
                ServerRoleID: "",
                ServerTypeID: ""
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Replication',
                disabledAttrs: ['type', 'className'], //Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder'], // disable delete button.
            },
            meta: {
                group: 'custom',
                id: 'replication',
                icon: '', //⌨
            },
            attrs: {
                name: "@@replication",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
            },
        },
        {
            tag: 'select',
            config: {
                label: 'InfraObject',
                disabledAttrs: ['type', 'className', 'InfraObjectID'],
                lockedAttrs: ['required', 'name', 'InfraObject', 'placeholder'],
            },
            meta: {
                group: 'custom',
                id: 'infraobject',
                icon: '',//⌨
            },
            attrs: {
                name: "@@infraobject",
                className: 'form-select-modal-dynamic',
                placeholder: "Select Option",
                type: 'select',
                required: true,
                InfraObject: "",
                InfraObjectID: "",
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Workflows',
                disabledAttrs: ['type', 'className'], //Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder', 'dependentAction'], // disable delete button.
            },
            meta: {
                group: 'custom',
                id: 'workflow',
                icon: '', //⌨
            },
            attrs: {
                name: "@@workflow_name",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
                dependentAction: false
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Node',
                disabledAttrs: ['type', 'className', 'id'], //Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder', 'multiple'], // disable delete button.
            },
            meta: {
                group: 'custom',
                id: 'nodes',
                icon: '', //⌨
            },
            attrs: {
                name: "nodes",
                id: "assigned_nodes",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
                multiple: true
            },
        },
        {
            tag: 'table',
            config: {
                label: 'Sudo/Su Table',
                disabledAttrs: ['className', 'rows', 'columns'],
                lockedAttrs: ['required', 'name'],
            },
            meta: {
                group: 'custom',
                id: 'table',
                icon: '⌨',
            },
            attrs: {
                name: 'Sudo/Su Table',
                className: 'custom-table',
                required: true,
                rows: 1, // Initial number of rows
                columns: 4, // Initial number of columns
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Select',
                disabledAttrs: ['type', 'className'],
                lockedAttrs: ['required', 'name', 'placeholder', 'multiple'],
            },
            meta: {
                group: 'common',
                id: 'select',
                icon: 'select',
            },
            options: [
                {
                    label: '',
                    value: '',
                },
                {
                    label: 'Option-1',
                    value: 'option-1',
                }
            ],
            attrs: {
                name: "select_field",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
                multiple: false
            },
        },
        {
            tag: 'input',
            attrs: {
                name: "radio_field",
                type: 'radio',

            },
            config: {
                label: 'Radio Group',
                disabledAttrs: ['type'],
                lockedAttrs: ['name'],
            },
            meta: {
                group: 'common',
                icon: 'radio-group',
                id: 'radio'
            },
            options: (() => {
                let options = [1, 2, 3].map(i => {
                    return {
                        label: 'Radio ' + i,
                        value: 'radio-' + i,
                        selected: false,
                    };
                });
                return options;
            })(),
        },
        {
            tag: 'input',
            config: {
                label: 'Checkbox',
                disabledAttrs: ['type'],
                lockedAttrs: ['name', 'required', 'readonly'],
                hideLabel: true
            },
            meta: {
                group: 'common',
                icon: 'checkbox',
                id: 'checkbox'
            },
            attrs: {
                name: "checkbox_field",
                type: 'checkbox',
                required: true,
                readonly: false,
            },
            options: [{
                label: 'Option 1',
                value: 'option-1',
                checked: false,
            }],
        },
        {
            tag: 'input',
            attrs: {
                name: "checkbox_group",
                type: 'checkbox',
                //required: true,
            },
            config: {
                label: 'Checkbox Group',
                disabledAttrs: ['type'],
                lockedAttrs: ['name', 'required'],
                hideLabel: false
            },
            meta: {
                group: 'common',
                icon: 'checkbox-group',
                id: 'checkbox-group'
            },
            options: (() => {
                let options = [1, 2, 3].map(i => {
                    return {
                        label: 'Option ' + i,
                        value: 'option-' + i,
                        checked: false,
                    };
                });
                return options;
            })(),
        },
        {
            tag: 'textarea',
            attrs: {
                name: "text_area",
                type: 'textarea',
                required: false,
                //minlength: "",
                //maxlength: "",
            },
            config: {
                label: 'Text Area',
                disabledAttrs: ['type'],
                lockedAttrs: ['name', 'required', 'maxlength', 'minlength'],
            },
            meta: {
                group: 'common',
                icon: 'textarea',
                id: 'textarea'
            },
        },
        {
            tag: "input",
            config: {
                label: 'Text Input',
                disabledAttrs: ['type', "class"],
                lockedAttrs: ['name', 'required', 'placeholder', 'minlength', 'maxlength', 'encryption', 'restrict', 'readonly', 'textInputValue'],
            },
            meta: {
                group: 'common',
                icon: 'text-input',
                id: 'text-input'
            },
            attrs: {
                name: "text_field",
                textInputValue: "",
                required: true,
                // placeholder: "Enter Text",
                type: "text",
                maxlength: "",
                minlength: "",
                encryption: false,
                //customvalidation: false,
                restrict: false,
                readonly: false,
            },
        },
        {
            tag: "input",
            config: {
                "label": "Number Input",
                "disabledAttrs": ["type"],
                lockedAttrs: ['name', 'placeholder', 'required', 'minlength', 'maxlength'],
            },
            meta: {
                "group": "common",
                "icon": "hash",
                "id": "number"
            },
            attrs: {
                name: "number_field",
                placeholder: "Enter Number",
                type: "number",
                required: true,
                minlength: "",
                maxlength: "",
            }
        },
        {
            tag: "input",
            config: {
                label: "Password Input",
                disabledAttrs: ["type", 'encryption', 'minlength', 'maxlength'],
                lockedAttrs: ['name', 'required', 'placeholder', 'minlength', 'maxlength'],
            },
            meta: {
                "group": "common",
                "icon": "",//menu
                "id": "password-input"
            },
            attrs: {
                name: "password_field",
                type: "password",
                placeholder: "Enter Password",
                required: true,
                minlength: "",
                maxlength: "",
                //encryption: true,               
            }
        },
        {
            tag: "input",
            config: {
                label: 'Command',
                disabledAttrs: ['type'],
                lockedAttrs: ['name', 'placeholder'],
            },
            meta: {
                group: 'common',
                icon: 'text-input',
                id: 'command'
            },
            attrs: {
                name: "Command",
                placeholder: "Enter Command",
                type: "textarea",
            },
        },
        {
            tag: "input",
            config: {
                label: "IP Address",
                disabledAttrs: ["type", "attrid"],
                lockedAttrs: ['name', 'placeholder', 'required'],
            },
            meta: {
                group: "common",
                icon: "", //text-input
                id: "ip-address"
            },
            attrs: {
                name: "IpAddress",
                type: "text",
                placeholder: "Enter IP Address",
                required: true,
                attrid: "ipAddressField"
            }
        },
    ],
}

$(function () {
    $('#parentAction').sortable({
        cancel: '#placeholder-span',
        update: function (event, ui) {

            // Iterate through the sorted elements
            $('#parentAction > div').each(function (newIndex) {
                var $this = $(this);
                var newIndexValue = newIndex + 1;
                // Update the index in the element
                $this.find('.workflowIndex').html(newIndexValue);
            });
        },
    }).disableSelection();

    document.addEventListener('keydown', function (e) {
        // Left arrow = 37, Right arrow = 39
        if (e.key === "ArrowLeft" || e.key === "ArrowRight") {
            e.preventDefault(); 
        }
    });
})

const formeoOptions = {
    editorContainer: '#actionForms',
    controls: controlOptions,
    events: {
        onUpdate: (formData) => handleUpdate(formData)
    },
    disableActionButtons: true,
}

/////////////////////////////////////////////////

$('#ActionbuilderconfigModal').on('shown.bs.modal', function (e) {

    if ($(".finish_btn ").text() == "Save") {
        $("#formBuilderType ").val("Common").trigger('change')
    }
    //$('#ActionbuilderconfigModal').attr("aria-hidden", true)
    var formeo = new FormeoEditor(formeoOptions);
    $(".formeo-stage").addClass("mt-5");
    dynamicIconChange();
});

$(".AddAction").on("click", function () {
    validateScript = true;
    scriptData = "";
    $("#validateObject").addClass('d-none');
    $("#nextButton").removeClass("disabled");
    $(".first").css({ "pointer-events": "none" });
    $(".current").css({ "pointer-events": "none" });
    $(".last").css({ "pointer-events": "none" });
    $(".done").css({ "pointer-events": "none" });
    $(".finish_btn").removeClass("disabled")
    $("#formbuildname-error").removeClass("field-validation-error")
    $("#description").val("")
    $("#timeWait").val("")
    $("#msBetweenTries").val("")
    $("#formBuilderInput").val("")
    $("#formbuildname-error").empty();
    $('#parentAction').empty();
    $("#formBuilderType option[value='all']").attr('selected', 'selected');
    $(".finish_btn").html("Save")
    nodeId = $(this).attr("nodeId")
    $(".copyContent").addClass("d-none")
    $("#steps-uid-0-t-0").trigger("click");
    ExecuteTreeDataView()
})

$("#restore").on("click", async function () {
    let actionName = $("#saveAsActionName").val()
    let isName = await validateName(actionName, $('#actionName-error'), 'Enter action name')

    if (!isName) {
        return;
    }
    let splitData = $(this).attr("name").split("$")
    var returnJson = jsonResultActionView(splitData[2])

    formData = {
        WorkflowActionId: splitData[2],
        __RequestVerificationToken: gettoken(),
        Name: actionName,
        isLock: true
    };
    $.ajax({
        type: "POST",
        url: RootUrl + saveasActionUrl,
        data: formData,
        dataType: "json",
        traditional: true,
        success: function (data) {
            if (data.message) {
                toaster("info-toast", "success-toast", data.message, "cp-exclamation", "cp-check");
            }
            else {
                toaster("success-toast", "info-toast", data, "cp-check", "cp-exclamation");
            }
            getActionListView(splitData[1])
        }
    })
    $("#SaveAs").modal("hide")
})

$(".next_btn").on("click", async function (e) {
    e.preventDefault()
    let arrayName = []
    $(".propertiWindow").hide();
    var formBuilderInput = $("#formBuilderInput").val();
    var formBuilderType = $("#formBuilderType").val();
    var serverName = $("#serverName").val();
    var description = $("#description").val()
    var timeWait = $("#timeWait").val()
    var msBetweenTries = $("#msBetweenTries").val()
    var buttonText = $(".finish_btn").text()
    var formBuilderText = $("#formBuilderType option:selected").text()
    var flagNameList = await alreadyExistActionList(formBuilderInput)

    ////Action builder data not there when clicked previous button
    //var formeo = new FormeoEditor(formeoOptions);
    //var propertydata = formeo.formData;

    if ($(".formeo-stage").children(".children").children().length == 0) {
        $('#alertClass').addClass("info-toast")
        $(".iconClass").addClass("cp-exclamation")
        $('#message').text("Please configure get input field!")
        $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
        return false;
    }

    if (formBuilderInput == "") {
        $("#formbuildname-error").text('Enter action name')
            .addClass('field-validation-error')
        return false;
    }
    //else if (formBuilderInput != formBuilderInput.match(/^[a-zA-Z]+$/)) {
    //    $('#formbuildname-error').text('Please enter only letters').addClass('field-validation-error')
    //    return false;
    //}
    else if (flagNameList) {
        if (buttonText == "Save") {
            $("#formbuildname-error").text('Name already exist!')
                .addClass('field-validation-error')
            return false;
        }
    }

    $(".formeo-stage .children .formeo-row .formeo-column .formeo-field").each((i, x) => {
        let name = $("#" + x.id + "-attrs-name-name").val()
        arrayName.push(name)
    })

    let dubilcateName;
    if (arrayName.length != 0) {
        dubilcateName = arrayName.filter((item, index) => arrayName.indexOf(item) !== index);
    }
    if (dubilcateName.length != 0) {
        $('#alertClass').addClass("info-toast")
        $(".iconClass").addClass("cp-exclamation")
        $('#message').text(dubilcateName.join(",") + " input Name already exist!")
        $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
        return false;
    }

    var isName = await validateName(formBuilderInput, $('#formbuildname-error'), 'Enter action name');
    if (isName) {
        formBuilderInput ? $("#actionNameSummary").text(formBuilderInput) : $("#actionNameSummary").text("NA")
        formBuilderType ? $("#actionTypeSummary").text(formBuilderText) : $("#actionNameSummary").text("NA")
        serverName ? $("#serverNameSummary").text(serverName) : $("#serverNameSummary").text("NA")
        description ? $("#descriptionSummary").text(description) : $("#descriptionSummary").text("NA")
        form.steps("next");
        !$('.prev_btn').is(':visible') && $('.next_btn').is(':visible') ? $(".copyContent").removeClass("d-none") : $(".copyContent").addClass("d-none")
    }

    //$(".next_btn").addClass("disabled");
    setTimeout(() => {
        if ($(".finish_btn").is(':visible')) {
            $("#validateObject").addClass('d-none');
        } else {
            $("#validateObject").removeClass('d-none');
            if (validateScript) {
                const parent = document.getElementById('parentAction');
                const hasChildDiv = parent.querySelector('div') !== null;
                if (hasChildDiv) {
                    $("#nextButton").addClass("disabled")
                }
                else {
                    $("#nextButton").removeClass("disabled")
                }               
            } else {
                $("#nextButton").removeClass("disabled")
            }           
        }
    }, 350)
})

function showFormattedError(errorMessage) {
    const lines = errorMessage.split('\n').map(line => line.trim()).filter(Boolean);
    const formattedMessage = lines.map((line, index) => `${index + 1}. ${line}`).join('<br>');
    return formattedMessage;
}

$("#validateObject").on("click", async function () {
    try {
        let scriptElement = getScriptActionList();
        scriptElement = scriptElement.replace(/tokenString\s*=\s*api\.getbearertoken\([^\n]*\);?/g, '');
        let response = await $.ajax({
            url: RootUrl + validateCpActionScript,
            data: { script: scriptElement },
            type: "GET",
            dataType: "text",
            traditional: true,
        });

        if (response.startsWith("OK:")) {
            if (!response.replace("OK:", "").trim()) {
                toaster("warning-toast info-toast", "success-toast", "The script has been validated and is good to go", "cp-exclamation", "cp-check");
                $("#nextButton").removeClass("disabled")
            } else {
                let message = showFormattedError(response.replace("OK:", "").trim());
                toaster("success-toast info-toast", "warning-toast", message, "cp-check", "cp-exclamation");
                $("#nextButton").addClass("disabled")
            }
        } else if (response.startsWith("ERROR:")) {
            let message = showFormattedError(response.replace("ERROR:", "").trim());
            toaster("success-toast info-toast", "warning-toast", message, "cp-check", "cp-exclamation");
            $("#nextButton").addClass("disabled")
        } else {
            toaster("success-toast info-toast", "warning-toast", response || "Script validation failed: Input script is null or empty", "cp-check", "cp-exclamation");
            $("#nextButton").addClass("disabled")
        }
    } catch (error) {
        toaster("success-toast info-toast", "warning-toast", "Network error or server is unavailable", "cp-check", "cp-exclamation");
        $("#nextButton").addClass("disabled")
    }
});

$(".prev_btn").on("click", async function (e) {
    $('.prev_btn').is(':visible') && !$('.next_btn').is(':visible') ? $(".copyContent").removeClass("d-none") : $(".copyContent").addClass("d-none")

    setTimeout(() => {
        if ($(".next_btn").is(':visible')) {
            $("#validateObject").removeClass('d-none');
        }
        if (!$('.prev_btn').is(':visible')) {
            $("#validateObject").addClass('d-none');
            $("#nextButton").removeClass("disabled")
        }
    }, 350)
})

let copiedText;
$(".copyContent").on("click", async function (e) {
    $(this).addClass("text-primary")
    copiedHTMLArray = [];
    copiedText = []
    let element = $("#parentAction")[0]; // raw DOM element
    let range = document.createRange();
    range.selectNodeContents(element);
    let selection = window.getSelection();
    selection.removeAllRanges(); // Clear any existing selection
    selection.addRange(range);   // Set selection
    let clonedNodes = range.cloneContents().childNodes;
    let cleanedLines = [];

    // Loop through nodes and extract clean text
    clonedNodes.forEach(node => {
        let line = node.textContent.trim();

        // Remove leading line numbers (e.g., "1 ", "12 ", etc.)
        line = line.replace(/^\d+\s*/, '');

        if (line) {
            cleanedLines.push(line);
        }
    });

    copiedText = cleanedLines

    // Set the array
    copiedHTMLArray.push(range.cloneContents().childNodes)

    // ✅ Copy to clipboard so you can paste in Notepad
    let finalText = copiedText.join('\n');
    navigator.clipboard.writeText(finalText)
        .then(() => {
            console.log("Copied to clipboard!");
        })
        .catch(err => {
            console.error("Clipboard copy failed:", err);
        });
    setTimeout(() => {
        $(this).removeClass("text-primary")
    }, 500)
})

$(".finish_btn").on("click", function () {
    let formeo = new FormeoEditor(formeoOptions);
    let propertydata = formeo.formData;
    let buttonValue = $(this).text().trim()
    let formBuilderInput = $("#formBuilderInput").val()
    let formBuilderType = $("#formBuilderType option:selected").val()
    let script = scriptActionList()
    let scriptJson = scriptActionListJson()
    let elements = document.querySelectorAll('li.formeo-field.first-field.last-field');

    if (elements) {
        elements?.forEach(function (id, index) {
            let fieldID = id?.id;
            Object.keys(propertydata.fields).forEach(function () {
                let field = propertydata?.fields[fieldID];
                field["index"] = index;
            });
        })
    }
    let formData;
    let formBuilderJson = {
        "formInput": propertydata,
        "executeActions": scriptJson,
        "test": {},
        "summary": {},
        "type": "",
        "description": $("#description").val(),
        "timeWait": $("#timeWait").val(),
        color: $("#formBuilderType option:selected").attr("color"),
        "msBetweenTries": $("#msBetweenTries").val()
    }

    if (buttonValue == "Save") {
        formData = {
            __RequestVerificationToken: gettoken(),
            nodeid: nodeId,
            actionname: formBuilderInput,
            properties: JSON.stringify(formBuilderJson),
            script: script,
            type: formBuilderType,
            isLock: false
        };
    }
    else {
        nodeId = $(".finish_btn").attr("nodeId")
        let id = $(".finish_btn").attr("id")
        formData = {
            __RequestVerificationToken: gettoken(),
            id: id,
            nodeid: nodeId,
            actionname: formBuilderInput,
            properties: JSON.stringify(formBuilderJson),
            script: script,
            type: formBuilderType,
            isLock: true
        };
    }

    $.ajax({
        type: "POST",
        url: RootUrl + postactionurl,
        data: formData,
        dataType: "json",
        traditional: true,
        contentType: 'application/x-www-form-urlencoded; charset=utf-8',
        success: function (data) {

            if (data.message) {
                toaster("info-toast", "success-toast", data.message, "cp-exclamation", "cp-check");
            }
            else {
                toaster("success-toast", "info-toast", data, "cp-check", "cp-exclamation");
            }
            getActionListView(nodeId)
            $("#formbuildname-error").text('')
                .removeClass('field-validation-error')
        },
        error: function (data) {
            toaster("success-toast", "info-toast", data.message, "cp-check", "cp-exclamation");
            getActionListView(nodeId)
            $("#formbuildname-error").text('').removeClass('field-validation-error')
        }
    })
    $("#ActionbuilderconfigModal").modal("hide")
})

let copyContent;
document.getElementById("droppable").addEventListener('copy', function (e) {

    if (typeof window.getSelection != "undefined") {
        let sel = window.getSelection();
        if (sel.rangeCount) {
            let container = document.createElement("div");
            for (let i = 0, len = sel.rangeCount; i < len; ++i) {
                copyContent = sel.getRangeAt(i).cloneContents()
            }

        }
    } else if (typeof document.selection != "undefined") {
        if (document.selection.type == "Text") {
            copyContent = document.selection.createRange().htmlText;
        }
    }
    e.preventDefault(); // default behaviour is to copy any selected text
});

$("#userName").on('keyup', function () {
    let userName = $("#userName").val()

    if (!userName) {
        $("#Namelog-error").text('Enter user name')
            .addClass('field-validation-error')
        return false;
    }
    else if (userName.length < 3) {
        $("#Namelog-error").text('Between 3 to 200 characters')
            .addClass('field-validation-error')
        return false;
    }
    else if (userName.length > 200) {
        $("#Namelog-error").text('Between 3 to 200 characters')
            .addClass('field-validation-error')
        return false;
    }
    else {
        $("#Namelog-error").removeClass('field-validation-error').text("")
    }
})

$("#password").on('keyup', function () {
    let password = $("#password").val()

    if (!password) {
        $("#passwordlog-error").text('Enter secret key')
            .addClass('field-validation-error')
        return false;
    }
    else if (password.length < 3) {
        $("#passwordlog-error").text('Between 3 to 200 characters')
            .addClass('field-validation-error')
        return false;
    }
    else if (password.length > 200) {
        $("#passwordlog-error").text('Between 3 to 200 characters')
            .addClass('field-validation-error')
        return false;
    }
    else {
        $("#passwordlog-error").removeClass('field-validation-error').text("")
    }
})

$("#fieldNameInput").on('keyup', async function () {
    let fieldNameInput = $("#fieldNameInput").val()

    if (fieldNameInput == "") {
        checkAction = validateAction(fieldNameInput, "Enter field name", $("#fieldNameInputCategory-error"))
    }
})

$("#commendInput").on('keyup', async function () {
    let commendInput = $("#commendInput").val()

    if (commendInput == "") {
        checkAction = validateAction(commendInput, "Enter comment", $("#Commend-error"))
    }
    else {
        $("#Commend-error").text('').removeClass('field-validation-error')
    }
})

$(".actionFooterCategory,.actionCommendCategory").on("click", async function (e) {
    e.preventDefault();
    $("#actionNameCategory-error").empty()
    let checkAction = true
    let actionNameInput = $("#actionNameInput").val()
    let fieldNameInput = $("#fieldNameInput").val()
    let commendInput = $("#commendInput").val()
    let image = $(this).attr("image")

    if (image) {
        let imageSelected = image
    }
    else {
        let imageSelected = "cp-images"
    }
    if ($(this).attr("status") == "category") {

        if (actionNameInput == "") {
            $("#actionNameCategory-error").text('Enter action name')
                .addClass('field-validation-error')
            return false;
        }
        let isName = await validateName(actionNameInput, $("#actionNameCategory-error"));
        if (!isName) {
            return false;
        }
        let propertyData = { 'id': 0, 'nodeId': Uuidv4(), 'parentId': '', 'title': actionNameInput, 'subtitle': '', 'expanded': true, 'noDragging': false, 'icon': imageSelected, 'children': [], 'properties': [] }
        var formData = {
            Name: actionNameInput,
            Properties: JSON.stringify(propertyData),
            Version: "1.0.0",
            __RequestVerificationToken: gettoken()
        };
    }
    else if ($(this).attr("status") == "EditCategory" || $(this).attr("status") == "EditSubCategory") {
        let id = $(this).attr("referenceId")
        let nodeId = $(this).attr("nodeId")
        let parentId = $(this).attr("parentId")
        let classNameData = $(this).attr("status")
        let parentName = $(this).attr("parentname")
        let NameValidate = $(this).attr("status") == "EditCategory" ? "Enter action name" : 'Enter action name'

        if (actionNameInput == "") {
            $("#actionNameCategory-error").text(NameValidate)
                .addClass('field-validation-error')
            return false;
        }

        let isName = await validateName(actionNameInput, $("#actionNameCategory-error"));
        if (!isName) {
            return false;
        }

        var propertyData = WorkflowPropertyUpdateList(id, nodeId, parentId, actionNameInput, classNameData, imageSelected)
        if ($(this).attr("status") == "EditSubCategory") {
            var formData = {
                Id: id,
                Name: parentName,
                Properties: JSON.stringify(propertyData),
                Version: "1.0.0",
                __RequestVerificationToken: gettoken()
            };
        } else {
            var formData = {
                Id: id,
                Name: actionNameInput,
                Properties: JSON.stringify(propertyData),
                Version: "1.0.0",
                __RequestVerificationToken: gettoken()
            };
        }
    }
    else if ($(this).attr("status") == "AddSubCategory") {

        if (actionNameInput == "") {
            $("#actionNameCategory-error").text('Enter action name')
                .addClass('field-validation-error')
            return false;
        }

        let isName = await validateName(actionNameInput, $("#actionNameCategory-error"));
        if (!isName) {
            return false;
        }
        let id = $(this).attr("referenceId")
        let nodeId = $(this).attr("nodeId")
        let parentId = $(this).attr("parentId")
        let classNameData = $(this).attr("status")
        let parentName = $(this).attr("parentname")
        let propertyData = WorkflowPropertyAddList(id, nodeId, parentId, actionNameInput, classNameData, imageSelected)
        var formData = {
            Id: id,
            Name: parentName,
            Properties: JSON.stringify(propertyData),
            Version: "1.0.0",
            __RequestVerificationToken: gettoken()
        };
    }
    else if ($(this).attr("status") == "addChildSubComment" || $(this).attr("status") == "EditchidSubComment") {

        if (fieldNameInput == "") {
            checkAction = validateAction(fieldNameInput, "Enter field name", $("#fieldNameInputCategory-error"))

        }
        if (commendInput == "") {
            checkAction = validateAction(commendInput, "Enter comment", $("#Commend-error"))

        }
        let id = $(this).attr("referenceId")
        let nodeId = $(this).attr("nodeId")
        let parentId = $(this).attr("parentId")
        let classNameData = $(this).attr("status")
        let parentName = $(this).attr("parentname")

        if ($(this).attr("status") == "EditchidSubComment") {
            var propertyData = WorkflowPropertyUpdateList(id, nodeId, parentId, fieldNameInput, classNameData, imageSelected)
        }
        else {
            var propertyData = WorkflowPropertyAddList(id, nodeId, parentId, fieldNameInput, classNameData, imageSelected)
        }
        var formData = {
            Id: id,
            Name: parentName,
            Properties: JSON.stringify(propertyData),
            Version: "1.0.0",
            __RequestVerificationToken: gettoken()
        };
    }

    if (checkAction) {
        $.ajax({
            type: "POST",
            url: RootUrl + createActionUrl,
            data: formData,
            dataType: "json",
            traditional: true,
            success: function (data) {
                $('#alertClass').removeClass().addClass("success-toast")
                $('#message').text(data.message)
                $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
                ExecuteTreeDataView()
                $("#Name-error").text('').removeClass('field-validation-error')
            },
            error: function () {

            }
        })
    }
})

$(".saveLock").on("click", async function () {
    let userName = $("#userName").val()
    let password = $("#password").val()
    let referenceId = $(this).attr("referenceId")
    let nodeId = $(this).attr("nodeId")
    let lockStatusdata = $(this).attr("lockStatus") == "false" ? true : false

    if (!userName && !password) {
        $("#passwordlog-error").text('Enter secret key')
            .addClass('field-validation-error')
        return false;
    }
    else if (userName && !password) {
        $("#passwordlog-error").text('Enter secret key')
            .addClass('field-validation-error')
        return false;
    }
    else if (password.length < 3) {
        $("#passwordlog-error").text('Between 3 to 200 characters')
            .addClass('field-validation-error')
        return false;
    }
    else if (password.length > 200) {
        $("#passwordlog-error").text('Between 3 to 200 characters')
            .addClass('field-validation-error')
        return false;
    }
    else {
        $("#passwordlog-error").removeClass('field-validation-error').text("")
    }

    var data = {}
    let stringPassword = password.split("$")

    if (stringPassword.length >= 2) {
        await $.ajax({
            type: "GET",
            url: RootUrl + lockDecryptUrl,
            data: { password: stringPassword[0] },
            dataType: "json",
            traditional: true,
            success: function (decryptdata) {
                data.password = decryptdata
            }
        })
    }
    else {
        data.password = password
    }

    await $.ajax({
        type: "GET",
        url: RootUrl + lockEncryptUrl,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (data) {
            let securityKey = "SecurityKey"
            let lockData = {
                userName: $("#userName").attr("value"),
                password: data + "$" + Math.random().toString(36).substring(2, 7),
                settingKey: securityKey,
                __RequestVerificationToken: gettoken()
            };

            $.ajax({
                type: "POST",
                url: RootUrl + lockactionurl,
                data: lockData,
                dataType: "json",
                traditional: true,
                success: function (data) {
                    if (data.message) {

                        if (data.success) {
                            let lockData = {
                                id: referenceId,
                                isLock: lockStatusdata,
                                __RequestVerificationToken: gettoken()
                            };

                            $.ajax({
                                type: "POST",
                                url: RootUrl + lockStatusUpdateUrl,
                                data: lockData,
                                dataType: "json",
                                traditional: true,
                                success: function (data) {
                                    toaster("info-toast", "success-toast", data.message, "cp-exclamation", "cp-check");
                                    getActionListView(nodeId)
                                    $('#LockModal').modal('hide');
                                }
                            })
                        }
                        else {
                            toaster("success-toast", "info-toast", data, "cp-check", "cp-exclamation");
                            $('#LockModal').modal('hide');
                        }
                    }
                    else {
                        $("#passwordlog-error").text('Invalid Credential')
                            .addClass('field-validation-error')
                    }
                }
            })
            $("#SaveAs").modal("hide")
        }
    })
})

$('#actionNameInput').on("input", function () {
    let actionName = $('#actionNameInput').val();
    let sanitizedValue = actionName.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    validateActionName($('#actionNameInput').val());
});

$(".action_catagory_toggle").css("display", "none")

$(".categoryactionlist ").on("click", function () {
    $(".action_catagory_toggle").css("display", "block")

})

$(document).ready(function () {
    $('.draggable-item').draggable({
        connectToSortable: '#parentAction', // Allows dropping into #parentAction
        helper: 'clone', // Clone the element while dragging
        revert: 'invalid' // Revert if not dropped in the list
    });

    $('#parentAction').droppable({
        accept: '.draggable-item'
    });
})

$("#actionDeleteButton").on("click", function () {
    var formData = {
        id: actionId,
        __RequestVerificationToken: gettoken()
    }
    $.ajax({
        type: "POST",
        url: RootUrl + deleteActionUrl,
        data: formData,
        dataType: "json",
        traditional: true,
        success: function (data) {
            $("#DeleteActionModal").modal("hide");
            $('#alertClass').removeClass().addClass("success-toast");
            $('#message').text("'" + actionName + "' has been deleted successfully")
            $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
            ExecuteTreeDataView()
            $("#Name-error").text('').removeClass('field-validation-error')
        },
        error: function () {

        }
    })
});
