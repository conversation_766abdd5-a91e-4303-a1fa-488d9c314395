﻿namespace ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateDrOperation;

public class
    UpdateInfraObjectDrOperationCommandHandler : IRequestHandler<UpdateInfraObjectDrOperationCommand,
        UpdateInfraObjectDrOperationResponse>
{
    private readonly IDashboardViewRepository _dashboardViewRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IPublisher _publisher;
    
    public UpdateInfraObjectDrOperationCommandHandler( IInfraObjectRepository infraObjectRepository,
        IDashboardViewRepository dashboardViewRepository,
        IPublisher publisher)
    {
       
        _infraObjectRepository = infraObjectRepository;
        _dashboardViewRepository = dashboardViewRepository;
        _publisher = publisher;
    }

    public async Task<UpdateInfraObjectDrOperationResponse> Handle(UpdateInfraObjectDrOperationCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _infraObjectRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.InfraObject), request.Id);

        var dataLagDtl = await _dashboardViewRepository.GetBusinessViewByInfraObjectId(request.Id);

        if (dataLagDtl == null) throw new NotFoundException(nameof(Domain.Entities.DashboardView), request.Id);


        var actionType = request.ActionType?.ToLowerInvariant() switch
        {
            "switchover" => 2,
            "switchback" => 5,
            "failover" => 8,
            "failback" => 11,
            "custom" => 14,
            "customdr" => 15,
            _ => 0
        };


        eventToUpdate.DROperationStatus = actionType;
        dataLagDtl.DROperationStatus = actionType;
        await _infraObjectRepository.UpdateAsync(eventToUpdate);
        await _dashboardViewRepository.UpdateAsync(dataLagDtl);



        return new UpdateInfraObjectDrOperationResponse
        {
            Id = eventToUpdate.ReferenceId,
            Message = $"InfraObject '{eventToUpdate.Name}' DROperationStatus updated to '{request.ActionType}' successfully."
        };
    }
}