﻿using ContinuityPatrol.Application.Features.EscalationMatrixLevel.Events.Create;

namespace ContinuityPatrol.Application.Features.EscalationMatrixLevel.Command.Create;

public class CreateEscalationMatrixLevelCommandHandler : IRequestHandler<CreateEscalationMatrixLevelCommand,
    CreateEscalationMatrixLevelResponse>
{
    private readonly IEscalationMatrixLevelRepository _escalationMatrixLevelRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateEscalationMatrixLevelCommandHandler(IMapper mapper,
        IPublisher publisher, IEscalationMatrixLevelRepository escalationMatrixLevelRepository)
    {
        _mapper = mapper;
        _publisher = publisher;
        _escalationMatrixLevelRepository = escalationMatrixLevelRepository;
    }

    public async Task<CreateEscalationMatrixLevelResponse> Handle(CreateEscalationMatrixLevelCommand request,
        CancellationToken cancellationToken)
    {
        var escalationMatrixLevel = _mapper.Map<Domain.Entities.EscalationMatrixLevel>(request);

        await _escalationMatrixLevelRepository.AddAsync(escalationMatrixLevel);

        var response = new CreateEscalationMatrixLevelResponse
        {
            Message = Message.Create(nameof(Domain.Entities.EscalationMatrixLevel), escalationMatrixLevel.EscLevName),
            Id = escalationMatrixLevel.ReferenceId
        };

        await _publisher.Publish(
            new EscalationMatrixLevelCreatedEvent { EscLevName = escalationMatrixLevel.EscLevName },
            cancellationToken);

        return response;
    }
}