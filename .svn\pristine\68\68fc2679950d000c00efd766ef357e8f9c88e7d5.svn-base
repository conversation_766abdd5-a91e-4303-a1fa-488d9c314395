const requestURL = {
    getRequestPaginatedlist: "/Manage/ApprovalMatrix/GetPaginatedRequestList",
    getApprovalPaginatedlist: "/Manage/ApprovalMatrix/GetPaginatedApprovalList",
    withdrawRequest: "Manage/ApprovalMatrix/WithdrawRequest",
    approveReject: "Manage/ApprovalMatrix/ApproveReject",
    getApprovalMatrixByRequestId: "Manage/ApprovalMatrix/GetApprovalMatrixByRequestId",
}

let requestDataTable = "";
let approvalDataTable = "";
let selectedValues = [];

const approvalPreventSpecialKeys = (selector) => {
    const blockedKeys = new Set(['=', 'Enter']);
    $(selector).on('keypress', e => blockedKeys.has(e.key) && e.preventDefault());
};

const baseDataTableConfig = {
    language: {
        decimal: ",",
        paginate: {
            next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
            previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
        },
        infoFiltered: ""
    },
    dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
    scrollY: true,
    deferRender: true,
    scroller: true,
    processing: true,
    serverSide: true,
    filter: true,
    Sortable: true,
    order: [],
    fixedColumns: { left: 1, right: 1 },
    rowCallback: function (row, data, index) {
        let startIndex = this.api().context[0]._iDisplayStart;
        $('td:eq(0)', row).html(startIndex + index + 1);
    },
    initComplete: function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    }
};

function commonAjax(url) {
    return {
        type: "GET",
        url,
        dataType: "json",
        data: function (d) {
            const sortIndex = d?.order[0]?.column || '';
            d.PageNumber = Math.ceil(d.start / d.length) + 1;
            d.pageSize = d.length;
            d.searchString = selectedValues?.length === 0
                ? $('#searchInputRequest').val()
                : selectedValues.join(';');
            d.sortColumn = sortIndex === 1 ? "name" : "";
            d.SortOrder = d?.order[0]?.dir || 'asc';
            selectedValues.length = 0;
            reportSearchStr = d.searchString;
        },
        dataSrc: function (json) {
            if (json?.success) {
                const { data } = json;
                json.recordsTotal = data?.totalPages;
                json.recordsFiltered = data?.totalCount;
                $(".pagination-column").toggleClass("disabled", data?.data?.length === 0);
                return url === requestURL.getApprovalPaginatedlist ? data?.data?.reverse() : data?.data;
            } else {
                errorNotification(json);
            }
        }
    };
}

function myRequestLists() {
    requestDataTable = $('#dataTableListsMyRequest').DataTable({
        ...baseDataTableConfig,
        ajax: commonAjax(requestURL.getRequestPaginatedlist),
        columnDefs: [{ targets: [1, 3], className: "truncate" }],
        columns: [
            {
                data: null, name: "Sr. No.", autoWidth: true, orderable: false,
                render: (data, type, row, meta) => type === 'display' ? meta.row + 1 : data
            },
            {
                data: "requestId", name: "requestId",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },
            {
                data: "status", name: "status",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },
            {
                data: "reason", name: "reason",
                render: (data, type) => {
                    return type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data;
                }
            },
            {
                data: "userName", name: "userName",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },
            {
                render: function (_, __, row) {
                    const { id, requestId, status } = row;
                    const reqStatus = status?.replace(/\s+/g, '').toLowerCase();
                    const isDisabled = ["withdraw", "rejected", "approved"].includes(reqStatus);
                    return `<div class="d-flex align-items-center gap-2">
                        <span role="button" title="View" id="viewButtonRequest" data-request="${requestId}" data-bs-toggle="modal" data-bs-target="#viewModalApprovalReject">
                            <i class="cp-password-visible text-primary fw-semibold me-2"></i> View
                        </span>
                        <span role="button" title="Withdraw" id="requestWithdrawButton" data-request="${requestId}" data-bs-toggle="modal" data-bs-target="#approvalModalWithdraw"
                              data-withdraw-id="${id}" data-withdraw-request="${requestId}" data-withdraw-status="Withdraw"
                              style="${isDisabled ? 'pointer-events: none; opacity: 0.5;' : ''}">
                            <i class="cp-undo text-warning fw-semibold me-2"></i> Withdraw
                        </span>
                    </div>`;
                }, orderable: false
            }
        ]
    });
}

function myApprovalLists() {
    approvalDataTable = $('#dataTableListsMyApproval').DataTable({
        ...baseDataTableConfig,
        ajax: commonAjax(requestURL.getApprovalPaginatedlist),
        columns: [
            {
                data: null, name: "Sr. No.", autoWidth: true, orderable: false,
                render: (data, type, row, meta) => type === 'display' ? meta.row + 1 : data
            },
            {
                data: "requestId", name: "requestId",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },
            {
                data: "description", name: "description",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },
            {
                data: "status", name: "status",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },           
            {
                data: "userName", name: "CreatedBy",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },
            {
                data: "createdDate", name: "CreatedOn",
                render: (data, type) => {
                    const d = new Date(data);
                    const pad = (n) => n.toString().padStart(2, '0');
                    const formatted = `${pad(d.getDate())}-${pad(d.getMonth() + 1)}-${d.getFullYear()} ${pad(d.getHours())}:${pad(d.getMinutes())}`;
                    return type === 'display' ? `<span title="${formatted}">${formatted}</span>` : formatted;
                }
            },
            {
                render: function (_, __, row) {
                    const { id, processName, status, approverName, requestId } = row;
                    const statusClean = status?.replace(/\s+/g, '').toLowerCase();
                    const isAllowed = statusClean === "waitingforapproval"; 

                    const style = isAllowed ? "" : 'pointer-events: none; opacity: 0.5;';
                    return `<div class="d-flex align-items-center gap-2">
                        <span role="button" data-bs-toggle="modal" data-bs-target="#approvalModalRequest"
                              title="Approve" id="buttonApprovalAM" style="${style}"
                              data-approval-id="${id}" data-approval-procname="${processName}" data-approval-status="Approved">
                            <i class="cp-success text-success fw-semibold me-2"></i> Approve
                        </span>
                        <span role="button" data-bs-toggle="modal" data-bs-target="#rejectModalRequest"
                              title="Reject" id="buttonRejectAM" style="${style}"
                              data-approval-id="${id}" data-approval-procname="${processName}" data-approval-status="Rejected">
                            <i class="cp-error text-warning fw-semibold me-2"></i> Reject
                        </span>
                        <span role="button" title="View" id="viewButtonApproval" data-request="${requestId}" data-bs-toggle="modal" data-bs-target="#viewModalApprovalReject">
                            <i class="cp-password-visible text-primary fw-semibold me-2"></i> View
                        </span>
                    </div>`;
                }, orderable: false
            }
        ]
    });
}

function textFieldValidation(value, message, errorelement) {
    if (!value) {
        errorelement.text(message).addClass("field-validation-error");
        return false
    } else {
        errorelement.text("").removeClass("field-validation-error");
        return true
    }
};

async function approvalRejectWithdraw(command, url, approveReject = null) {
    await $.ajax({
        url: RootUrl + url,
        data: command,
        type: "GET",
        dataType: "Json",
        success: function (response) {
            if (response?.success) {
                notificationAlert("success", response?.data?.message);
            } else {
                errorNotification(response);
            }
            requestDataTable.ajax.reload();
            if (approveReject) {
                approvalDataTable.ajax.reload();
            }
        }
    });
}

function withdrawTimeline(username, processname, status) {
    let htmlTemplateWithdraw = ` <li>
                                    <div class="direction-l">
                                        <div class="flag-wrapper">
                                            <span class="hexa" data-item-status="rejected"></span>
                                            <span class="flag">${processname}</span>
                                            <span class="time-wrapper"><span class="time small">10:28 AM</span><span class="alert alert-danger py-1 px-2 small rounded-pill">${status}</span></span>
                                        </div>
                                        <div class="desc">${username} has withdraw the matrix</div>
                                    </div>
                                </li>`;
    return htmlTemplateWithdraw;
}

function waitingForApproval(username, processname, status) {
    let htmlTemplateWithdraw = ` <li>
                                    <div class="direction-l">
                                        <div class="flag-wrapper">
                                            <span class="hexa" data-item-status="rejected"></span>
                                            <span class="flag">${processname}</span>
                                            <span class="time-wrapper"><span class="time small">10:28 AM</span><span class="alert alert-danger py-1 px-2 small rounded-pill">${status}</span></span>
                                        </div>
                                        <div class="desc">Waiting for ${username} approval</div>
                                    </div>
                                </li>`;
    return htmlTemplateWithdraw;
}

function approveTimeline(username, processname, status) {
    let htmlTemplateApprove = ` <li>
                                    <div class="direction-r">
                                        <div class="flag-wrapper">
                                            <span class="hexa" data-item-status="approval"></span>
                                            <span class="time-wrapper"><span class="alert alert-success py-1 px-2 small rounded-pill">${status}</span><span class="time small">10:28 AM</span></span>
                                            <span class="flag">${processname}</span>
                                        </div>
                                        <div class="desc">${username} has approved the matrix</div>
                                    </div>
                                </li>`;
    return htmlTemplateApprove;
}

function rejectTimeline(username, processname, status) {
    let htmlTemplateReject = ` <li>
                                    <div class="direction-l">
                                        <div class="flag-wrapper">
                                            <span class="hexa" data-item-status="rejected"></span>
                                            <span class="flag">${processname}</span>
                                            <span class="time-wrapper"><span class="time small">10:28 AM</span><span class="alert alert-danger py-1 px-2 small rounded-pill">${status}</span></span>
                                        </div>
                                        <div class="desc">${username} has rejected the matrix</div>
                                    </div>
                                </li>`;
    return htmlTemplateReject;
}

function sendRequestTimeline() {
    let htmlTemplateCreated = `<li>
                                  <div class="direction-r">
                                        <div class="flag-wrapper">
                                            <span class="hexa" data-item-status="approval"></span>
                                            <span class="time-wrapper"><span class="time small">10:28 AM</span><span class="alert alert-success py-1 px-2 small rounded-pill">Approval</span></span>
                                           
                                        </div>
                                        <div class="desc">Send approvel request to selected users</div>
                                   </div>
                              </li>`
    return htmlTemplateCreated;
}

function createdTimeline(createdUser) {
    let htmlTemplateCreated = `<li>
                                  <div class="direction-r">
                                        <div class="flag-wrapper">
                                            <span class="hexa" data-item-status="created"></span>
                                            <span class="time-wrapper"><span class="time small">10:28 AM</span><span class="alert alert-primary py-1 px-2 small rounded-pill">Created</span></span>
                                        </div>
                                        <div class="desc">${createdUser} created the matrix</div>
                                   </div>
                              </li>`
    return htmlTemplateCreated;
}

async function approvalTimeline(command, url) {
    await $.ajax({
        url: RootUrl + url,
        data: command,
        type: "GET",
        dataType: "Json",
        success: function (response) {
            if (response?.success) {
                let $timelineForAppMatrix = $("#approvalMatrixTimeline");
                $timelineForAppMatrix.empty();
                let createdDate = "";
                let createdUser = response?.data[0]?.userName;

                response?.data?.forEach(function (data) {
                    const statusData = data?.status?.toLowerCase().replace(/\s+/g, "");
                    const { userName, processName, status, message, approverName } = data;

                    switch (true) {
                        case statusData === "waitingforapproval":
                            $timelineForAppMatrix.append(waitingForApproval(approverName, processName, data.status));
                            break;
                        case statusData === "withdraw":
                            $timelineForAppMatrix.append(withdrawTimeline(userName, processName, data.status));
                            break;
                        case statusData === "accepted":
                            $timelineForAppMatrix.append(approveTimeline(userName, processName, data.status));
                            break;
                        case statusData === "rejected":
                            $timelineForAppMatrix.append(rejectTimeline(userName, processName, data.status));
                            break;
                        case message?.includes("Approval sent to") || message?.includes("Approval forwarded to"):
                            $timelineForAppMatrix.append(sendRequestTimeline());
                            break;
                        default:
                            $timelineForAppMatrix.append(createdTimeline(createdUser));
                            const dateMatch = message?.split("on ")[1]?.replace(".", "");
                            if (dateMatch) createdDate = dateMatch;
                            break;
                    }

                });

                if (createdDate) {
                    $timelineForAppMatrix.append(`<li class="text-center">
                                                    <span class="date-wrapper rounded">${createdDate}</span>
                                                  </li>`);
                    }
            } else {
                errorNotification(response);
            }
        }
    });
}

$(function () {
    approvalPreventSpecialKeys('#searchInputRequest');
    myRequestLists();
    $("#pills-myrequest-tab").on("click", myRequestLists);
    $("#pills-myapproval-tab").on("click", myApprovalLists);
});

$("#dataTableListsMyApproval").on("click", "#buttonApprovalAM", async function () {   
    $("#approveRequestId").val($(this).data("approval-id"));
    $("#approveProcessName").val($(this).data("approval-procname"));
    $("#approveApprovalStatus").val($(this).data("approval-status"));
    $("#approveRemarkRequestError").text("").removeClass("field-validation-error");
    $("#approveRemarkRequest").val("");
    $("#approvalName").text($(this).data("approval-procname"));
});

$("#dataTableListsMyApproval").on("click", "#buttonRejectAM", async function () {
    $("#rejectRequestId").val($(this).data("approval-id"));
    $("#rejectProcessName").val($(this).data("approval-procname"));
    $("#rejectApprovalStatus").val($(this).data("approval-status"));
    $("#rejectReasonRequestError").text("").removeClass("field-validation-error");
    $("#rejectReasonRequest").val("");
    $("#rejectName").text($(this).data("approval-procname"));
});

$("#approveRemarkRequest").on("input", function () {
    textFieldValidation($(this).val(), "Enter remark", $("#approveRemarkRequestError"))
});

$("#rejectReasonRequest").on("input", function () {
    textFieldValidation($(this).val(), "Enter reason", $("#rejectReasonRequestError"))
});

$("#confirmApproveRequest").on("click", async function () {
    const remarkStatus = textFieldValidation($("#approveRemarkRequest").val(), "Enter remark", $("#approveRemarkRequestError"))

    if (remarkStatus) {
        let command = {
            Id: $("#approveRequestId").val(),
            ProcessName: $("#approveProcessName").val(),
            Status: $("#approveApprovalStatus").val(),
        };
        await approvalRejectWithdraw(command, requestURL.approveReject, true);
        $("#cancelButtonApprove").trigger("click");
    }
})

$("#confirmRejectRequest").on("click", async function () {
    const reasonStatus = textFieldValidation($("#rejectReasonRequest").val(), "Enter reason", $("#rejectReasonRequestError"))

    if (reasonStatus) {
        let command = {
            Id: $("#rejectRequestId").val(),
            ProcessName: $("#rejectProcessName").val(),
            Status: $("#rejectApprovalStatus").val(),
        };
        await approvalRejectWithdraw(command, requestURL.approveReject, true);
        $("#cancelButtonReject").trigger("click");
    }
})

$("#dataTableListsMyRequest").on("click", "#requestWithdrawButton", async function () {
    $("#withdrawRequestId").val($(this).data("withdraw-request"));
    $("#withdrawId").val($(this).data("withdraw-id"));
    $("#withdrawStatus").val($(this).data("withdraw-status"));
    $("#withdrawName").text($(this).data("request"));
});

$("#confirmWithdraw").on("click", async function () {
    let command = {
        Id: $("#withdrawId").val(),
        RequestId: $("#withdrawRequestId").val(),
        Status: $("#withdrawStatus").val(),
    };
    await approvalRejectWithdraw(command, requestURL.withdrawRequest);
    $("#cancelButtonWithdraw").trigger("click");
})

$("#dataTableListsMyApproval").on("click", "#viewButtonApproval", async function () {
    const requestId = $(this).data("request");
    let command = {
        requestId: requestId,
    };
    approvalTimeline(command, requestURL.getApprovalMatrixByRequestId);
});

$("#dataTableListsMyRequest").on("click", "#viewButtonRequest", async function () {
    const requestId = $(this).data("request");
    let command = {
        requestId: requestId,
    };
    approvalTimeline(command, requestURL.getApprovalMatrixByRequestId);
});