using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class IsvcgmMonitorLogFixture : IDisposable
{
    public ApplicationDbContext DbContext { get; private set; }

    public IsvcgmMonitorLogFixture()
    {
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
