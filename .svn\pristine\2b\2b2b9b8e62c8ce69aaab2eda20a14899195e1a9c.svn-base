﻿using ContinuityPatrol.Application.Features.Report.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Queries;

public class GetReportDetailQueryHandlerTests : IClassFixture<ReportFixture>
{
    private readonly ReportFixture _reportFixture;

    private readonly Mock<IReportRepository> _mockReportRepository;

    private readonly GetReportDetailQueryHandler _handler;

    public GetReportDetailQueryHandlerTests(ReportFixture reportFixture)
    {
        _reportFixture = reportFixture;

        _mockReportRepository = ReportRepositoryMocks.GetReportRepository(_reportFixture.Reports);

        _handler = new GetReportDetailQueryHandler(_reportFixture.Mapper, _mockReportRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_ReportDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetReportDetailQuery { Id = _reportFixture.Reports[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<ReportDetailVm>();
        result.Id.ShouldBe(_reportFixture.Reports[0].ReferenceId);
        result.Name.ShouldBe(_reportFixture.Reports[0].Name);
        result.Description.ShouldBe(_reportFixture.Reports[0].Description);
        result.FilterColumn.ShouldBe(_reportFixture.Reports[0].FilterColumn);
        result.HeaderColumn.ShouldBe(_reportFixture.Reports[0].HeaderColumn);
        result.Design.ShouldBe(_reportFixture.Reports[0].Design);
        result.DataSet.ShouldBe(_reportFixture.Reports[0].DataSet);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidReportId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetReportDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetReportDetailQuery { Id = _reportFixture.Reports[0].ReferenceId }, CancellationToken.None);

        _mockReportRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}