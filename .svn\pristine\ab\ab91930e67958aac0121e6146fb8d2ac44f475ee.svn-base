﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowActionRepositoryMocks
{
    public static Mock<IWorkflowActionRepository> CreateWorkflowActionRepository(List<WorkflowAction> workflowActions)
    {
        var workflowActionRepository = new Mock<IWorkflowActionRepository>();
        workflowActionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActions);
        workflowActionRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowAction>())).ReturnsAsync(
            (WorkflowAction workflowAction) =>
            {
                workflowAction.Id = new Fixture().Create<int>();
                workflowAction.ReferenceId = new Fixture().Create<Guid>().ToString();
                workflowActions.Add(workflowAction);
                return workflowAction;
            });

        return workflowActionRepository;
    }

    public static Mock<IWorkflowActionRepository> UpdateWorkflowActionRepository(List<WorkflowAction> workflowActions)
    {
        var workflowActionRepository = new Mock<IWorkflowActionRepository>();

        workflowActionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActions);

        workflowActionRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowActions.SingleOrDefault(x => x.ReferenceId == i));

        workflowActionRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowAction>())).ReturnsAsync((WorkflowAction workflowAction) =>
        {
            var index = workflowActions.FindIndex(item => item.ReferenceId == workflowAction.ReferenceId);

            workflowActions[index] = workflowAction;

            return workflowAction;
        });

        return workflowActionRepository;
    }

    public static Mock<IWorkflowActionRepository> DeleteWorkflowActionRepository(List<WorkflowAction> workflowActions)
    {
        var workflowActionRepository = new Mock<IWorkflowActionRepository>();
        workflowActionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActions);

        workflowActionRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowActions.SingleOrDefault(x => x.ReferenceId == i));

        workflowActionRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowAction>())).ReturnsAsync((WorkflowAction workflowAction) =>
        {
            var index = workflowActions.FindIndex(item => item.ReferenceId == workflowAction.ReferenceId);

            workflowAction.IsActive = false;
            workflowActions[index] = workflowAction;

            return workflowAction;
        });

        return workflowActionRepository;
    }

    public static Mock<IWorkflowActionRepository> ImportWorkflowActionRepository(List<WorkflowAction> workflowActions)
    {
        var workflowActionRepository = new Mock<IWorkflowActionRepository>();
        workflowActionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActions);
        workflowActionRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowAction>())).ReturnsAsync(
            (WorkflowAction workflowAction) =>
            {
                workflowAction.Id = new Fixture().Create<int>();
                workflowAction.ReferenceId = new Fixture().Create<Guid>().ToString();
                workflowActions.Add(workflowAction);
                return workflowAction;
            });

        return workflowActionRepository;
    }

    public static Mock<IWorkflowActionRepository> GetWorkflowActionRepository(List<WorkflowAction> workflowActions)
    {
        var workflowActionRepository = new Mock<IWorkflowActionRepository>();

        workflowActionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActions);

        workflowActionRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowActions.SingleOrDefault(x => x.ReferenceId == i));

        workflowActionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowActions);

        return workflowActionRepository;
    }

    public static Mock<IWorkflowActionRepository> GetWorkflowActionEmptyRepository()
    {
        var workflowActionRepository = new Mock<IWorkflowActionRepository>();

        workflowActionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowAction>());

        return workflowActionRepository;
    }

    public static Mock<IWorkflowActionRepository> GetWorkflowActionNamesRepository(List<WorkflowAction> workflowActions)
    {

        var workflowActionRepository = new Mock<IWorkflowActionRepository>();
        workflowActionRepository.Setup(repo => repo.GetWorkflowActionNames()).ReturnsAsync(workflowActions);

        return workflowActionRepository;
    }

    public static Mock<IWorkflowActionRepository> GetWorkflowActionNameUniqueRepository(List<WorkflowAction> workflowActions)
    {
        var workflowActionRepository = new Mock<IWorkflowActionRepository>();

        workflowActionRepository.Setup(repo => repo.IsWorkflowActionNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) =>
        {
            return j == 0.ToString() ? workflowActions.Exists(x => x.ActionName == i) : workflowActions.Exists(x => x.ActionName == i && x.ReferenceId == j);
        });

        return workflowActionRepository;
    }

    public static Mock<IWorkflowActionRepository> GetPaginatedWorkflowActionRepository(List<WorkflowAction> workflowActions)
    {
        var workflowActionRepository = new Mock<IWorkflowActionRepository>();

        var queryableWorkflowAction = workflowActions.BuildMock();

        workflowActionRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableWorkflowAction);

        return workflowActionRepository;
    }

    public static Mock<IWorkflowActionRepository> GetWorkflowActionByNodeIdRepository(List<WorkflowAction> workflowActions)
    {
        var workflowActionRepository = new Mock<IWorkflowActionRepository>();

        workflowActionRepository.Setup(repo => repo.GetWorkflowActionDetailsByNodeId(It.IsAny<string>())).ReturnsAsync(workflowActions);

        return workflowActionRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateWorkflowActionEventRepository(List<UserActivity> userActivities)
    {
        var workflowActionEventRepository = new Mock<IUserActivityRepository>();

        workflowActionEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return workflowActionEventRepository;
    }
}