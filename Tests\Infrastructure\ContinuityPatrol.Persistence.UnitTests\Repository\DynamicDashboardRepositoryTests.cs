using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using System.Threading.Tasks;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DynamicDashboardRepositoryTests: IClassFixture<DynamicDashboardFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly DynamicDashboardRepository _repository;
    private readonly DynamicDashboardRepository _repositoryNotParent;
    private readonly DynamicDashboardFixture _fixture;

    public DynamicDashboardRepositoryTests(DynamicDashboardFixture fixture)
    {
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DynamicDashboardRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DynamicDashboardRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
        _fixture=fixture;
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var dynamicDashboard = _fixture.DynamicDashboardDto;
        dynamicDashboard.Name = "TestDashboard";
   
        // Act
        var result = await _repository.AddAsync(dynamicDashboard);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dynamicDashboard.Name, result.Name);
        Assert.Single(_dbContext.DynamicDashboards);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var dashboards = _fixture.DynamicDashboardList;
      
        await _repository.AddRangeAsync(dashboards);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboards.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange

        var dashboards = _fixture.DynamicDashboardList;
     
        await _repositoryNotParent.AddRangeAsync(dashboards);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);

    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public async Task GetPaginatedQuery_ShouldReturnQueryableOrderedByIdDescending_WhenIsAllInfraTrue()
    {
        // Arrange
      
        var dashboards = _fixture.DynamicDashboardList;
    
        await _repository.AddRangeAsync(dashboards);

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Any());
        
        var resultList = result.ToList();
        if (resultList.Count > 1)
        {
            Assert.True(resultList.First().Id >= resultList.Last().Id);
        }
    }

    [Fact]
    public async Task GetPaginatedQuery_ShouldReturnFilteredQueryable_WhenIsAllInfraFalse()
    {
        // Arrange
        var dashboards = _fixture.DynamicDashboardPaginationList;
       
       await _repositoryNotParent.AddRangeAsync(dashboards);

        // Act
        var result = _repositoryNotParent.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);

    }

    #endregion

    #region IsDashboardNameUnique Tests

    [Fact]
    public async Task IsDashboardNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var dynamicDashboard = _fixture.DynamicDashboardDto;
        dynamicDashboard.Name= "ExistingDashboard";
        await _repository.AddAsync(dynamicDashboard);

        // Act
        var result = await _repository.IsNameExist("ExistingDashboard","");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsDashboardNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var dashboards = _fixture.DynamicDashboardList;
        await _repository.AddRangeAsync(dashboards);

        // Act
        var result = await _repository.IsNameExist("NonExistentDashboard", "");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsDashboardNameExist Tests

    [Fact]
    public async Task IsDashboardNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var dynamicDashboard =_fixture.DynamicDashboardDto;

        dynamicDashboard.Name = "ExistingDashboard";
        dynamicDashboard.IsActive = true;
        
        await _repository.AddAsync(dynamicDashboard);

        // Act
        var result = await _repository.IsNameExist("ExistingDashboard", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsDashboardNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var dynamicDashboard = _fixture.DynamicDashboardDto;

        dynamicDashboard.Name = "ExistingDashboard";
        dynamicDashboard.IsActive = true;

        await _repository.AddAsync(dynamicDashboard);

        // Act
        var result = await _repository.IsNameExist("ExistingDashboard", dynamicDashboard.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion
   

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var dashboards = _fixture.DynamicDashboardList;

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(dashboards);
        var initialCount = dashboards.Count;
        
        var toUpdate = dashboards.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedDashboard");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = dashboards.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.Name == "UpdatedDashboard").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
