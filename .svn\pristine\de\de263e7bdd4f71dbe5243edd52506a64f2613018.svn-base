using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using Microsoft.Extensions.Configuration;
using System.Data;
using System.Data.Common;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class MSSQLMonitorLogsRepositoryTests : IClassFixture<MSSQLMonitorLogsFixture>
{
    private readonly MSSQLMonitorLogsFixture _mssqlMonitorLogsFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly MSSQLMonitorLogsRepository _repository;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public MSSQLMonitorLogsRepositoryTests(MSSQLMonitorLogsFixture mssqlMonitorLogsFixture)
    {
        _mssqlMonitorLogsFixture = mssqlMonitorLogsFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockConfiguration = ConfigurationRepositoryMocks.GetConnectionString();
        
        _repository = new MSSQLMonitorLogsRepository(_dbContext, _mockConfiguration.Object);
    }

    private async Task ClearDatabase()
    {
        _dbContext.MssqlMonitorLogs.RemoveRange(_dbContext.MssqlMonitorLogs);
        await _dbContext.SaveChangesAsync();
    }

    //public void Dispose()
    //{
    //    _dbContext?.Dispose();
    //}

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var mssqlMonitorLogs = _mssqlMonitorLogsFixture.MSSQLMonitorLogsDto;

        // Act
        var result = await _repository.AddAsync(mssqlMonitorLogs);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(mssqlMonitorLogs.Type, result.Type);
        Assert.Equal(mssqlMonitorLogs.InfraObjectId, result.InfraObjectId);
        Assert.Single(_dbContext.MssqlMonitorLogs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var mssqlMonitorLogs = _mssqlMonitorLogsFixture.MSSQLMonitorLogsDto;
        var addedEntity = await _repository.AddAsync(mssqlMonitorLogs);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Type, result.Type);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var mssqlMonitorLogs = _mssqlMonitorLogsFixture.MSSQLMonitorLogsDto;
        await _repository.AddAsync(mssqlMonitorLogs);

        // Act
        var result = await _repository.GetByReferenceIdAsync(mssqlMonitorLogs.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(mssqlMonitorLogs.ReferenceId, result.ReferenceId);
        Assert.Equal(mssqlMonitorLogs.Type, result.Type);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenReferenceIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var mssqlMonitorLogs = _mssqlMonitorLogsFixture.MSSQLMonitorLogsDto;
        var addedEntity = await _repository.AddAsync(mssqlMonitorLogs);
        
        addedEntity.Type = "UpdatedType";
        addedEntity.InfraObjectName = "UpdatedName";

        // Act
        await _repository.UpdateAsync(addedEntity);
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedType", result.Type);
        Assert.Equal("UpdatedName", result.InfraObjectName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldDeleteEntity()
    {
        // Arrange
        var mssqlMonitorLogs = _mssqlMonitorLogsFixture.MSSQLMonitorLogsDto;
        var addedEntity = await _repository.AddAsync(mssqlMonitorLogs);

        // Act
        await _repository.DeleteAsync(addedEntity);
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnActiveLogs_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var logs = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithMixedActiveStatus(testType);
        
        await _dbContext.MssqlMonitorLogs.AddRangeAsync(logs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, log => Assert.True(log.IsActive));
        Assert.All(result, log => Assert.Equal(testType, log.Type));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType("NonExistentType");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsNull()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsEmpty()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType("");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnOnlyActiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var activeLog = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(type: testType, isActive: true);
        var inactiveLog = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(type: testType, isActive: false);
        
         _dbContext.MssqlMonitorLogs.AddRange(new[] { activeLog, inactiveLog });
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result[0].IsActive);
        Assert.Equal(testType, result[0].Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleWhitespaceInType()
    {
        // Arrange
        await ClearDatabase();
        var whitespaceLog = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithWhitespace();
        whitespaceLog.IsActive = true;
        await _dbContext.MssqlMonitorLogs.AddAsync(whitespaceLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(whitespaceLog.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(whitespaceLog.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleVeryLongTypeNames()
    {
        // Arrange
        await ClearDatabase();
        var longTypeLog = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithLongType(1000);
        longTypeLog.IsActive = true;
        await _dbContext.MssqlMonitorLogs.AddAsync(longTypeLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(longTypeLog.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(longTypeLog.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();
        var specialTypes = MSSQLMonitorLogsFixture.TestData.SpecialCharacterTypes;
        var logs = new List<MSSQLMonitorLogs>();

        foreach (var type in specialTypes)
        {
            var log = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(type: type, isActive: true);
            logs.Add(log);
        }

        await _dbContext.MssqlMonitorLogs.AddRangeAsync(logs);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        foreach (var type in specialTypes)
        {
            var result = await _repository.GetDetailByType(type);
            Assert.Single(result);
            Assert.Equal(type, result.First().Type);
            Assert.True(result.First().IsActive);
        }
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnMultipleLogs_WhenMultipleActiveLogsExist()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var logs = _mssqlMonitorLogsFixture.CreateMultipleMSSQLMonitorLogsWithSameType(testType, 5);
        
        await _dbContext.MssqlMonitorLogs.AddRangeAsync(logs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(5, result.Count);
        Assert.All(result, log => Assert.Equal(testType, log.Type));
        Assert.All(result, log => Assert.True(log.IsActive));
    }

    [Fact]
    public async Task GetDetailByType_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var lowerCaseType = "testtype";
        var upperCaseType = "TESTTYPE";
        
        var lowerCaseLog = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(type: lowerCaseType, isActive: true);
        var upperCaseLog = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(type: upperCaseType, isActive: true);
        
        await _dbContext.MssqlMonitorLogs.AddRangeAsync(new[] { lowerCaseLog, upperCaseLog });
        await _dbContext.SaveChangesAsync();

        // Act
        var lowerResult = await _repository.GetDetailByType(lowerCaseType);
        var upperResult = await _repository.GetDetailByType(upperCaseType);

        // Assert
        Assert.Single(lowerResult);
        Assert.Single(upperResult);
        Assert.Equal(lowerCaseType, lowerResult.First().Type);
        Assert.Equal(upperCaseType, upperResult.First().Type);
    }

    #endregion

    #region GetByInfraObjectId Tests

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnLogs_WhenDataExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "test-infra-object-id";
        var startDate = DateTime.UtcNow.AddDays(-1);
        var endDate = DateTime.UtcNow.AddDays(1);

        var logs = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithDateRange(
            infraObjectId, startDate, endDate, 3);

        await _dbContext.MssqlMonitorLogs.AddRangeAsync(logs);
        await _dbContext.SaveChangesAsync();

        // Create testable repository that returns false for table existence
        var repo = new TestableMSSQLMonitorLogsRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId(infraObjectId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, log => Assert.Equal(infraObjectId, log.InfraObjectId));
        Assert.All(result, log => Assert.True(log.CreatedDate.Date >= startDate.Date && log.CreatedDate.Date <= endDate.Date));
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmpty_WhenNoDataExists()
    {
        // Arrange
        await ClearDatabase();
        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        var repo = new TestableMSSQLMonitorLogsRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId("nonexistent", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldFilterByDateRange()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "test-infra-object-id";
        var startDate = DateTime.UtcNow.AddDays(-5);
        var endDate = DateTime.UtcNow.AddDays(-1);

        var logsInRange = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithDateRange(
            infraObjectId, startDate, endDate, 2);
        var logsOutsideRange = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsOutsideDateRange(
            infraObjectId, startDate, endDate);

         _dbContext.MssqlMonitorLogs.AddRange(logsInRange.Concat(logsOutsideRange));
         _dbContext.SaveChanges();

        var repo = new TestableMSSQLMonitorLogsRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId(infraObjectId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only logs in range should be returned
        Assert.All(result, log => Assert.True(log.CreatedDate.Date >= startDate.Date && log.CreatedDate.Date <= endDate.Date));
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnOnlyActiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "test-infra-object-id";
        var startDate = DateTime.UtcNow.AddDays(-1);
        var endDate = DateTime.UtcNow.AddDays(1);

        var activeLog = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(
            infraObjectId: infraObjectId,
            isActive: true,
            createdDate: DateTime.UtcNow);

        var inactiveLog = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(
            infraObjectId: infraObjectId,
            isActive: false,
            createdDate: DateTime.UtcNow);

         _dbContext.MssqlMonitorLogs.AddRange(new[] { activeLog, inactiveLog });
         _dbContext.SaveChanges();

        var repo = new TestableMSSQLMonitorLogsRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId(infraObjectId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleNullParameters()
    {
        // Arrange
        var repo = new TestableMSSQLMonitorLogsRepository(_dbContext, _mockConfiguration.Object, false);

        // Act & Assert
        var result1 = await repo.GetByInfraObjectId(null, "2023-01-01", "2023-01-02");
        var result2 = await repo.GetByInfraObjectId("infra1", null, "2023-01-02");
        var result3 = await repo.GetByInfraObjectId("infra1", "2023-01-01", null);

        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.NotNull(result3);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnCombinedLogs_WhenBackupTableExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "test-infra-object-id";
        var startDate = DateTime.UtcNow.AddDays(-1);
        var endDate = DateTime.UtcNow.AddDays(1);

        var log = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(
            infraObjectId: infraObjectId,
            isActive: true,
            createdDate: DateTime.UtcNow);

        await _dbContext.MssqlMonitorLogs.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        // Create testable repository that returns true for table existence
        var repo = new TestableMSSQLMonitorLogsRepository(_dbContext, _mockConfiguration.Object, true);

        // Act & Assert - Should not throw exception even if backup table query fails
        try
        {
            var result = await repo.GetByInfraObjectId(infraObjectId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
            Assert.NotNull(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment without actual backup table
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldOrderByCreatedDate()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "test-infra-object-id";
        var startDate = DateTime.UtcNow.AddDays(-1);
        var endDate = DateTime.UtcNow.AddDays(1);

        var log1 = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(
            infraObjectId: infraObjectId,
            isActive: true,
            createdDate: DateTime.UtcNow.AddHours(-2));

        var log2 = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(
            infraObjectId: infraObjectId,
            isActive: true,
            createdDate: DateTime.UtcNow.AddHours(-1));

        await _dbContext.MssqlMonitorLogs.AddRangeAsync(new[] { log2, log1 }); // Add in reverse order
        await _dbContext.SaveChangesAsync();

        var repo = new TestableMSSQLMonitorLogsRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId(infraObjectId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.True(result[0].CreatedDate <= result[1].CreatedDate); // Should be ordered by CreatedDate
    }

    //[Fact]
    //public async Task GetByInfraObjectId_ShouldHandleOracleProviderDateFormatting()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var infraObjectId = "test-infra-object-id";
    //    var startDate = DateTime.UtcNow.AddDays(-1);
    //    var endDate = DateTime.UtcNow.AddDays(1);

    //    var log = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(
    //        infraObjectId: infraObjectId,
    //        isActive: true,
    //        createdDate: DateTime.UtcNow);

    //    await _dbContext.MssqlMonitorLogs.AddAsync(log);
    //    await _dbContext.SaveChangesAsync();

    //    // Create testable repository that returns true for table existence and simulates Oracle provider
    //    var repo = new TestableMSSQLMonitorLogsRepositoryWithOracleProvider(_dbContext, _mockConfiguration.Object, true);

    //    // Act & Assert - Should not throw exception even if Oracle-specific query fails
    //    try
    //    {
    //        var result = await repo.GetByInfraObjectId(infraObjectId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
    //        Assert.NotNull(result);
    //    }
    //    catch (Exception ex)
    //    {
    //        // Expected behavior for test environment without actual Oracle connection
    //        Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
    //    }
    //}

    #endregion

    #region GetTableName Tests

    [Fact]
    public void GetTableName_ShouldReturnCorrectTableName()
    {
        // Act
        var tableName = _repository.GetTableName<MSSQLMonitorLogs>();

        // Assert
        Assert.NotNull(tableName);
        Assert.NotEmpty(tableName);
    }

    [Fact]
    public void GetTableName_ShouldReturnNull_ForUnmappedEntity()
    {
        // Act
        var tableName = _repository.GetTableName<UnmappedEntity>();

        // Assert
        Assert.Null(tableName);
    }

    [Fact]
    public void GetTableName_ShouldReturnCorrectTableNameForMYSQLMonitorLogs()
    {
        // Note: The repository has a bug where it calls GetTableName<MYSQLMonitorLogs>() instead of MSSQLMonitorLogs
        // This test verifies the current behavior

        // Act
        var tableName = _repository.GetTableName<MYSQLMonitorLogs>();

        // Assert
        Assert.NotNull(tableName);
        Assert.NotEmpty(tableName);
    }

    #endregion

    #region IsTableExistAsync Tests

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleOracleProvider()
    {
        // Arrange
        var tableName = "MssqlMonitorLogs_bkp";
        var schemaName = "TESTSCHEMA";
        var providerName = "oracle";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment without Oracle connection
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleMsSqlProvider()
    {
        // Arrange
        var tableName = "MssqlMonitorLogs_bkp";
        var schemaName = "dbo";
        var providerName = "mssql";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleMySqlProvider()
    {
        // Arrange
        var tableName = "MssqlMonitorLogs_bkp";
        var schemaName = "testdb";
        var providerName = "mysql";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment without MySQL connection
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandlePostgreSqlProvider()
    {
        // Arrange
        var tableName = "MssqlMonitorLogs_bkp";
        var schemaName = "public";
        var providerName = "npgsql";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment without PostgreSQL connection
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleCaseInsensitiveProviderName()
    {
        // Arrange
        var tableName = "MssqlMonitorLogs_bkp";
        var schemaName = "dbo";
        var providerName = "ORACLE"; // Uppercase

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
     
        }
    }
    [Fact]
    public async Task IsTableExistAsync_ShouldReturnFalse_WhenTableDoesNotExist()
    {
        // Arrange
        var tableName = "NonExistentTable";
        var schemaName = "dbo";
        var providerName = "mssql";

        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);

            // Assert
            Assert.False(result); // Expect false if table doesn't exist
        }
        catch (Exception ex)
        {
            // Should not throw unless DB provider is not supported in test
            Assert.True(
                ex is InvalidOperationException ||
                ex is NotSupportedException ||
                ex is System.Data.Common.DbException
            );
        }
    }


    #endregion

    //#region GetDatabaseNameFromConnectionString Tests

    //[Fact]
    //public void GetDatabaseNameFromConnectionString_ShouldReturnDatabaseName_ForMySql()
    //{
    //    // Arrange
    //    var connectionString = "Server=localhost;Database=testdb;Uid=user;Pwd=password;";
    //    var provider = "mysql";
    //    var repo = new TestableMSSQLMonitorLogsRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

    //    // Act
    //    var result = repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider);

    //    // Assert
    //    Assert.Equal("testdb", result);
    //}

    //[Fact]
    //public void GetDatabaseNameFromConnectionString_ShouldReturnDatabaseName_ForMsSql()
    //{
    //    // Arrange
    //    var connectionString = "Server=localhost;Database=testdb;Trusted_Connection=true;";
    //    var provider = "mssql";
    //    var repo = new TestableMSSQLMonitorLogsRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

    //    // Act
    //    var result = repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider);

    //    // Assert
    //    Assert.Equal("testdb", result);
    //}

    //[Fact]
    //public void GetDatabaseNameFromConnectionString_ShouldReturnDatabaseName_ForPostgreSql()
    //{
    //    // Arrange
    //    var connectionString = "Host=localhost;Database=testdb;Username=user;Password=password;";
    //    var provider = "npgsql";
    //    var repo = new TestableMSSQLMonitorLogsRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

    //    // Act
    //    var result = repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider);

    //    // Assert
    //    Assert.Equal("testdb", result);
    //}

    //[Fact]
    //public void GetDatabaseNameFromConnectionString_ShouldThrow_ForUnsupportedProvider()
    //{
    //    // Arrange
    //    var connectionString = "some connection string";
    //    var provider = "unsupported";
    //    var repo = new TestableMSSQLMonitorLogsRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

    //    // Act & Assert
    //    Assert.Throws<ArgumentException>(() => repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider));
    //}

    //[Fact]
    //public void GetDatabaseNameFromConnectionString_ShouldReturnUserIdAsDatabase_WhenDatabaseNotFound()
    //{
    //    // Arrange
    //    var connectionString = "Server=localhost;User Id=testuser;Trusted_Connection=true;"; // No Database parameter
    //    var provider = "mssql";
    //    var repo = new TestableMSSQLMonitorLogsRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

    //    // Act
    //    var result = repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider);

    //    // Assert
    //    Assert.Equal("TESTUSER", result); // Should return User Id in uppercase
    //}

    // #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var activeLogs = new List<MSSQLMonitorLogs>
        {
            _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(isActive: true),
            _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(isActive: true)
        };
        var inactiveLog = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(isActive: false);

         _dbContext.MssqlMonitorLogs.AddRange(activeLogs.Concat(new[] { inactiveLog }));
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, log => Assert.True(log.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmpty_WhenNoActiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var inactiveLogs = new List<MSSQLMonitorLogs>
        {
            _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(isActive: false),
            _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(isActive: false)
        };

        _dbContext.MssqlMonitorLogs.AddRange(inactiveLogs);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRangeAsync Tests

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var logs = new List<MSSQLMonitorLogs>
        {
            _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(),
            _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(),
            _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties()
        };

        // Act
        await _repository.AddRangeAsync(logs);

        // Assert
        var allLogs = await _repository.ListAllAsync();
        Assert.Equal(3, allLogs.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenListIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region Edge Case Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var tasks = new List<Task>();

        // Act - Perform concurrent add operations
        for (int i = 0; i < 10; i++)
        {
            var log = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties();
            tasks.Add(_repository.AddAsync(log));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allLogs = await _repository.ListAllAsync();
        Assert.Equal(10, allLogs.Count);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleUnicodeCharacters()
    {
        // Arrange
        await ClearDatabase();
        var unicodeType = "测试类型_тест_テスト";
        var log = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(type: unicodeType, isActive: true);
        await _dbContext.MssqlMonitorLogs.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(unicodeType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(unicodeType, result[0].Type);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleSpecialCharactersInInfraObjectId()
    {
        // Arrange
        await ClearDatabase();
        var specialInfraId = "infra@#$%^&*()";
        var startDate = DateTime.UtcNow.AddDays(-1);
        var endDate = DateTime.UtcNow.AddDays(1);

        var log = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(
            infraObjectId: specialInfraId,
            isActive: true,
            createdDate: DateTime.UtcNow);

        await _dbContext.MssqlMonitorLogs.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        var repo = new TestableMSSQLMonitorLogsRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId(specialInfraId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(specialInfraId, result[0].InfraObjectId);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleTableNameBug()
    {
        // Arrange
        // The repository has a bug where it calls GetTableName<MYSQLMonitorLogs>() instead of MSSQLMonitorLogs
        // This test verifies that the method still works despite this bug
        await ClearDatabase();
        var infraObjectId = "test-infra-object-id";
        var startDate = DateTime.UtcNow.AddDays(-1);
        var endDate = DateTime.UtcNow.AddDays(1);

        var log = _mssqlMonitorLogsFixture.CreateMSSQLMonitorLogsWithProperties(
            infraObjectId: infraObjectId,
            isActive: true,
            createdDate: DateTime.UtcNow);

        await _dbContext.MssqlMonitorLogs.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        var repo = new TestableMSSQLMonitorLogsRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId(infraObjectId, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(infraObjectId, result[0].InfraObjectId);
    }

    #endregion

    #region Helper Classes and Methods

    private class UnmappedEntity { }

    // Helper class to override IsTableExistAsync for testing
    private class TestableMSSQLMonitorLogsRepository : MSSQLMonitorLogsRepository
    {
        private readonly bool _tableExists;

        public TestableMSSQLMonitorLogsRepository(ApplicationDbContext dbContext, IConfiguration config, bool tableExists)
            : base(dbContext, config)
        {
            _tableExists = tableExists;
        }

        public override async Task<bool> IsTableExistAsync(string tableName, string schemaName, string providerName)
        {
            // Simulate async operation
            await Task.Delay(1);
            return _tableExists;
        }
    }

    // Helper class to expose private methods for testing
    //private class TestableMSSQLMonitorLogsRepositoryWithPublicMethods : MSSQLMonitorLogsRepository
    //{
    //    public TestableMSSQLMonitorLogsRepositoryWithPublicMethods(ApplicationDbContext dbContext, IConfiguration config)
    //        : base(dbContext, config)
    //    {
    //    }

    //    public string GetDatabaseNameFromConnectionStringPublic(string connectionString, string provider)
    //    {
    //        // Use reflection to call the private method
    //        var method = typeof(MSSQLMonitorLogsRepository).GetMethod("GetDatabaseNameFromConnectionString",
    //            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
    //        return (string)method.Invoke(this, new object[] { connectionString, provider });
    //    }
    //}

    //// Helper class to test Oracle provider specific behavior
    //private class TestableMSSQLMonitorLogsRepositoryWithOracleProvider : MSSQLMonitorLogsRepository
    //{
    //    private readonly bool _tableExists;

    //    public TestableMSSQLMonitorLogsRepositoryWithOracleProvider(ApplicationDbContext dbContext, IConfiguration config, bool tableExists)
    //        : base(dbContext, config)
    //    {
    //        _tableExists = tableExists;
    //    }

    //    public override async Task<bool> IsTableExistAsync(string tableName, string schemaName, string providerName)
    //    {
    //        // Simulate async operation
    //        await Task.Delay(1);
    //        return _tableExists;
    //    }
    //}

    #endregion
}
