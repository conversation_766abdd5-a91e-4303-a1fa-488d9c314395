﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class UserInfoRepositoryMocks
{
    public static Mock<IUserInfoRepository> UpdateUserInfoRepository(List<UserInfo> userInfos)
    {
        var mockUserInfoRepository = new Mock<IUserInfoRepository>();

        mockUserInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userInfos);

        mockUserInfoRepository.Setup(repo => repo.GetUserInfoByUserIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => userInfos.SingleOrDefault(x => x.UserId == i));

        mockUserInfoRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserInfo>())).ReturnsAsync((UserInfo userInfo) =>
        {
            var index = userInfos.FindIndex(item => item.UserId == userInfo.UserId);

            userInfos[index] = userInfo;

            return userInfo;
        });

        return mockUserInfoRepository;
    }

    public static Mock<IUserInfoRepository> DeleteUserInfoRepository(List<UserInfo> userInfos)
    {
        var mockUserInfoRepository = new Mock<IUserInfoRepository>();

        mockUserInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userInfos);

        mockUserInfoRepository.Setup(repo => repo.GetUserInfoByUserIdAsync(It.IsAny<string>())).ReturnsAsync((string userId) =>
        {
            return userInfos?.FirstOrDefault(ui => ui?.UserId == userId);
        });
        mockUserInfoRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserInfo>())).ReturnsAsync((UserInfo userInfo) =>
        {
            if (userInfo == null)
            {
                return null;
            }
            var index = userInfos.FindIndex(ui => ui?.UserId == userInfo.UserId);
            if (index >= 0)
            {
                userInfo.IsActive = false;

                userInfos[index] = userInfo;
            }
            else
            {
                throw new Exception("UserInfo not found in the list.");
            }
            return userInfo;
        });
        return mockUserInfoRepository;
    }

    public static Mock<IUserInfoRepository> GetUserInfoRepository(List<UserInfo> userInfos)
    {
        var mockUserInfoRepository = new Mock<IUserInfoRepository>();

        mockUserInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userInfos);

        mockUserInfoRepository.Setup(repo => repo.GetUserInfoByUserIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => userInfos.SingleOrDefault(x => x.UserId == i));

        return mockUserInfoRepository;
    }

    public static Mock<IUserInfoRepository> GetPaginatedUserInfoRepository(List<UserInfo> userInfos)
    {
        var userInfoRepository = new Mock<IUserInfoRepository>();

        var queryableUserInfos = userInfos.BuildMock();

        userInfoRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableUserInfos);

        return userInfoRepository;
    }
}