﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Server.Events.PaginatedView;

public class ServerPaginatedEventHandler : INotificationHandler<ServerPaginatedEvent>
{
    private readonly ILogger<ServerPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ServerPaginatedEventHandler(ILoggedInUserService userService, ILogger<ServerPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ServerPaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Entity = Modules.Server.ToString(),
            Action = $"{ActivityType.View} {Modules.Server}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Server viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Server viewed");
    }
}