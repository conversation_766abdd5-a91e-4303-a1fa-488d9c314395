﻿using ContinuityPatrol.Application.Features.ComponentType.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.ComponentType.Queries;

public class GetComponentTypePaginatedListQueryHandlerTests : IClassFixture<ComponentTypeFixture>, IClassFixture<FormTypeCategoryFixture>
{
    private readonly ComponentTypeFixture _componentTypeFixture;
    private readonly FormTypeCategoryFixture _formTypeCategoryFixture;
    private readonly Mock<IComponentTypeRepository> _mockComponentTypeRepository;
    private readonly Mock<IFormTypeCategoryRepository> _mockFormTypeCategoryRepository;
    private readonly GetComponentTypePaginatedListQueryHandler _handler;

    public GetComponentTypePaginatedListQueryHandlerTests(ComponentTypeFixture componentTypeFixture, FormTypeCategoryFixture formTypeCategoryFixture)
    {
        _componentTypeFixture = componentTypeFixture;
        _formTypeCategoryFixture = formTypeCategoryFixture;

        _componentTypeFixture.ComponentTypes[0].ComponentName = "Server_Type";

        _componentTypeFixture.ComponentTypes[1].ComponentName = "Testing_Component";

        _mockComponentTypeRepository = ComponentTypeRepositoryMocks.GetPaginatedComponentTypeRepository(_componentTypeFixture.ComponentTypes);
        _mockFormTypeCategoryRepository =
            FormTypeCategoryRepositoryMocks.GetFormTypeCategoryByFormTypeIdRepository(_formTypeCategoryFixture
                .FormTypeCategory);
        _handler = new GetComponentTypePaginatedListQueryHandler(_componentTypeFixture.Mapper, _mockComponentTypeRepository.Object, _mockFormTypeCategoryRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();

        result.TotalCount.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }
    
    [Fact]
    public async Task Handle_Return_ComponentTypes_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();

        result.TotalCount.ShouldBe(3);

        result.Data[0].Id.ShouldBe(_componentTypeFixture.ComponentTypes[0].ReferenceId);

        result.Data[0].ComponentName.ShouldBe("Server_Type");
    }

    [Fact]
    public async Task Handle_Return_PaginatedComponentTypes_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Test" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<ComponentTypeListVm>();

        result.Data[0].ComponentName.ShouldBe("Testing_Component");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetComponentTypePaginatedListQuery(), CancellationToken.None);

        _mockComponentTypeRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}
