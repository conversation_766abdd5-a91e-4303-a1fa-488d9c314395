using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ApprovalMatrixApprovalRepository : BaseRepository<ApprovalMatrixApproval>, IApprovalMatrixApprovalRepository
{
    private readonly ILoggedInUserService _loggedInUserService;

    public ApprovalMatrixApprovalRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _loggedInUserService = loggedInUserService;
    }
    public async Task<bool> IsNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
        {
            return await Entities.AsNoTracking().AnyAsync(e => e.ProcessName == name);
        }
        return await Entities.AsNoTracking().AnyAsync(e => e.ProcessName == name && e.ReferenceId != id);
    }

    public async Task<List<ApprovalMatrixApproval>> GetApprovalMatrixByApprovalIds(List<string> approvalIds)
    {
        return await FilterBy(x => approvalIds.Contains(x.ApproverId)).ToListAsync();
    }

    public override async Task<PaginatedResult<ApprovalMatrixApproval>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<ApprovalMatrixApproval> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await Entities
            .AsNoTracking()
            .Specify(productFilterSpec)
            .Where(x => x.IsApproval && x.ApproverId == _loggedInUserService.UserId)
            .DescOrderById()
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public async Task<List<ApprovalMatrixApproval>> GetApprovalMatrixApprovalByRequestId(string requestId)
    {
        return await FilterBy(x => x.RequestId == requestId).ToListAsync();
    }

    public async Task<List<ApprovalMatrixApproval>> GetUnapprovedByRequestId(string requestId)
    {
        return await FilterBy(x => x.RequestId == requestId && x.Status != "Approved").ToListAsync();
    }
}
