﻿using ContinuityPatrol.Application.Features.SolutionHistory.Commands.Create;
using ContinuityPatrol.Application.Features.SolutionHistory.Commands.Update;
using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetSolutionHistoryByActionId;
using ContinuityPatrol.Domain.ViewModels.SolutionHistoryModel;

namespace ContinuityPatrol.Application.Mappings;

public class SolutionHistoryProfile : Profile
{
    public SolutionHistoryProfile()
    {
        CreateMap<SolutionHistory, CreateSolutionHistoryCommand>().ReverseMap();
        CreateMap<UpdateSolutionHistoryCommand, SolutionHistory>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<SolutionHistory, SolutionHistoryDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<SolutionHistory, SolutionHistoryListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<SolutionHistory, SolutionHistoryByActionIdQueryVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}