using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationGroupModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class BulkImportOperationGroupFixture
{
    public List<BulkImportOperationGroupListVm> BulkImportOperationGroupListVm { get; }
    public BulkImportOperationGroupDetailVm BulkImportOperationGroupDetailVm { get; }
    public CreateBulkImportOperationGroupCommand CreateBulkImportOperationGroupCommand { get; }
    public UpdateBulkImportOperationGroupCommand UpdateBulkImportOperationGroupCommand { get; }

    public BulkImportOperationGroupFixture()
    {
        var fixture = new Fixture();

        // Create sample BulkImportOperationGroup list data
        BulkImportOperationGroupListVm = new List<BulkImportOperationGroupListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                BulkImportOperationId = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                Properties = "{\"groupType\":\"server\",\"priority\":\"high\",\"batchSize\":50}",
                ProgressStatus = "Completed",
                Status = "Success",
                ErrorMessage = null,
                ConditionalOperation = 1,
                NodeId = Guid.NewGuid().ToString(),
                InfraObjectName = "Production Server Group A"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                BulkImportOperationId = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                Properties = "{\"groupType\":\"database\",\"priority\":\"medium\",\"batchSize\":25}",
                ProgressStatus = "Failed",
                Status = "Error",
                ErrorMessage = "Database connection timeout during import",
                ConditionalOperation = 2,
                NodeId = Guid.NewGuid().ToString(),
                InfraObjectName = "Database Cluster Group B"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                BulkImportOperationId = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                Properties = "{\"groupType\":\"network\",\"priority\":\"low\",\"batchSize\":100}",
                ProgressStatus = "In Progress",
                Status = "Processing",
                ErrorMessage = null,
                ConditionalOperation = 3,
                NodeId = Guid.NewGuid().ToString(),
                InfraObjectName = "Network Infrastructure Group C"
            }
        };

        // Create detailed BulkImportOperationGroup data
        BulkImportOperationGroupDetailVm = new BulkImportOperationGroupDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            BulkImportOperationId = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            Properties = "{\"groupType\":\"enterprise\",\"priority\":\"critical\",\"batchSize\":200,\"validation\":\"strict\",\"rollback\":\"enabled\"}",
            Status = "Completed",
            ProgressStatus = "100%",
            ErrorMessage = null,
            ConditionalOperation = 1,
            NodeId = Guid.NewGuid().ToString(),
            InfraObjectName = "Enterprise Critical Infrastructure Group"
        };

        // Create command for creating BulkImportOperationGroup
        CreateBulkImportOperationGroupCommand = new CreateBulkImportOperationGroupCommand
        {
            BulkImportOperationId = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            Properties = "{\"groupType\":\"application\",\"priority\":\"high\",\"batchSize\":75}",
            Status = "Initiated",
            ProgressStatus = "0%",
            ErrorMessage = null,
            ConditionalOperation = 1,
            NodeId = Guid.NewGuid().ToString(),
            InfraObjectName = "New Application Server Group"
        };

        // Create command for updating BulkImportOperationGroup
        UpdateBulkImportOperationGroupCommand = new UpdateBulkImportOperationGroupCommand
        {
            Id = Guid.NewGuid().ToString(),
            BulkImportOperationId = Guid.NewGuid().ToString(),
            CompanyId = Guid.NewGuid().ToString(),
            Properties = "{\"groupType\":\"security\",\"priority\":\"critical\",\"batchSize\":30,\"encryption\":\"enabled\"}",
            Status = "Updated",
            ProgressStatus = "50%",
            ErrorMessage = null,
            ConditionalOperation = 2,
            NodeId = Guid.NewGuid().ToString(),
            InfraObjectName = "Updated Security Infrastructure Group"
        };
    }
}
