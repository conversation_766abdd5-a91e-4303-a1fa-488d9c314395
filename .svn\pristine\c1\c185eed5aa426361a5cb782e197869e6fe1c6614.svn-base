using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Repositories;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class BulkImportOperationGroupRepositoryMocks
{
    public static Mock<IBulkImportOperationGroupRepository> CreateBulkImportOperationGroupRepository(List<BulkImportOperationGroup> bulkImportOperationGroups)
    {
        var mockBulkImportOperationGroupRepository = new Mock<IBulkImportOperationGroupRepository>();

        mockBulkImportOperationGroupRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportOperationGroups);

        mockBulkImportOperationGroupRepository.Setup(repo => repo.AddAsync(It.IsAny<BulkImportOperationGroup>())).ReturnsAsync(
            (BulkImportOperationGroup bulkImportOperationGroup) =>
            {
                bulkImportOperationGroup.Id = new Fixture().Create<int>();
                bulkImportOperationGroup.ReferenceId = new Fixture().Create<Guid>().ToString();
                bulkImportOperationGroups.Add(bulkImportOperationGroup);
                return bulkImportOperationGroup;
            });

        mockBulkImportOperationGroupRepository.Setup(repo => repo.AddRangeAsync(It.IsAny<IEnumerable<BulkImportOperationGroup>>()))
            .ReturnsAsync((IEnumerable<BulkImportOperationGroup> entities) =>
            {
                var fixture = new Fixture();
                var entityList = entities.ToList();
                foreach (var entity in entityList)
                {
                    entity.Id = fixture.Create<int>();
                    entity.ReferenceId = fixture.Create<Guid>().ToString();
                    bulkImportOperationGroups.Add(entity);
                }
                return entityList;
            });

        mockBulkImportOperationGroupRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BulkImportOperationGroup>()))
            .ReturnsAsync((BulkImportOperationGroup entity) => entity);

        mockBulkImportOperationGroupRepository.Setup(repo => repo.DeleteAsync(It.IsAny<BulkImportOperationGroup>()))
            .ReturnsAsync((BulkImportOperationGroup entity) => entity);

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportOperationGroups.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetBulkImportOperationGroupByBulkImportOperationId(It.IsAny<string>()))
            .ReturnsAsync((string operationId) => bulkImportOperationGroups.Where(x => x.BulkImportOperationId == operationId).ToList());

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetBulkImportOperationGroupByBulkImportOperationIds(It.IsAny<List<string>>()))
            .ReturnsAsync((List<string> operationIds) => bulkImportOperationGroups.Where(x => operationIds.Contains(x.BulkImportOperationId)).ToList());

        return mockBulkImportOperationGroupRepository;
    }

    public static Mock<IBulkImportOperationGroupRepository> CreateUpdateBulkImportOperationGroupRepository(List<BulkImportOperationGroup> bulkImportOperationGroups)
    {
        var mockBulkImportOperationGroupRepository = new Mock<IBulkImportOperationGroupRepository>();

        mockBulkImportOperationGroupRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportOperationGroups);

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportOperationGroups.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportOperationGroupRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BulkImportOperationGroup>())).ReturnsAsync((BulkImportOperationGroup business) =>
        {
            var index = bulkImportOperationGroups.FindIndex(item => item.ReferenceId == business.ReferenceId);

            bulkImportOperationGroups[index] = business;

            return business;

        });
        return mockBulkImportOperationGroupRepository;
    }

    public static Mock<IBulkImportOperationGroupRepository> CreateDeleteBulkImportOperationGroupRepository(List<BulkImportOperationGroup> bulkImportOperationGroups)
    {
        var mockBulkImportOperationGroupRepository = new Mock<IBulkImportOperationGroupRepository>();

        mockBulkImportOperationGroupRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportOperationGroups);

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportOperationGroups.FirstOrDefault(x => x.ReferenceId == id));
        
        mockBulkImportOperationGroupRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BulkImportOperationGroup>()))
       .ReturnsAsync((BulkImportOperationGroup adp) =>
       {
           var index = bulkImportOperationGroups.FindIndex(item => item.ReferenceId == adp.ReferenceId);
           adp.IsActive = false;
           bulkImportOperationGroups[index] = adp;

           return adp;
       });
        return mockBulkImportOperationGroupRepository;
    }

    public static Mock<IBulkImportOperationGroupRepository> CreateQueryBulkImportOperationGroupRepository(List<BulkImportOperationGroup> bulkImportOperationGroups)
    {
        var mockBulkImportOperationGroupRepository = new Mock<IBulkImportOperationGroupRepository>();

        mockBulkImportOperationGroupRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportOperationGroups);

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportOperationGroups.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetBulkImportOperationGroupByBulkImportOperationId(It.IsAny<string>()))
            .ReturnsAsync((string operationId) => bulkImportOperationGroups.Where(x => x.BulkImportOperationId == operationId).ToList());

        mockBulkImportOperationGroupRepository.Setup(repo => repo.GetBulkImportOperationGroupByBulkImportOperationIds(It.IsAny<List<string>>()))
            .ReturnsAsync((List<string> operationIds) => bulkImportOperationGroups.Where(x => operationIds.Contains(x.BulkImportOperationId)).ToList());

        return mockBulkImportOperationGroupRepository;
    }

    public static Mock<IUserActivityRepository> CreateBulkImportOperationGroupEventRepository(List<UserActivity> userActivities)
    {
        var mockUserActivityRepository = new Mock<IUserActivityRepository>();

        mockUserActivityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();
                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();
                userActivities.Add(userActivity);
                return userActivity;
            });

        return mockUserActivityRepository.As<IUserActivityRepository>();
    }
}
