﻿namespace ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Update;

public class
    UpdateDRReadyStatusCommandHandler : IRequestHandler<UpdateDRReadyStatusCommand, UpdateDRReadyStatusResponse>
{
    private readonly IDrReadyStatusRepository _dRReadyStatusRepository;
    private readonly IMapper _mapper;

    public UpdateDRReadyStatusCommandHandler(IMapper mapper, IDrReadyStatusRepository dRReadyStatusRepository)
    {
        _mapper = mapper;
        _dRReadyStatusRepository = dRReadyStatusRepository;
    }

    public async Task<UpdateDRReadyStatusResponse> Handle(UpdateDRReadyStatusCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _dRReadyStatusRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.DRReadyStatus), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateDRReadyStatusCommand), typeof(Domain.Entities.DRReadyStatus));

        await _dRReadyStatusRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateDRReadyStatusResponse
        {
            Message = Message.Update(nameof(Domain.Entities.DRReadyStatus), eventToUpdate.BusinessServiceName),

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}