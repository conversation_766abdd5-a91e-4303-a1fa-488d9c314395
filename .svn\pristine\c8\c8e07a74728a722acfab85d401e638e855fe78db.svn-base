﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.BiaRulesModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.BiaRules.Queries.GetPaginatedList;

public class
    GetBiaRulesPaginatedListQueryHandler : IRequestHandler<GetBiaRulesPaginatedListQuery,
        PaginatedResult<BiaRulesListVm>>
{
    private readonly IBiaRulesRepository _biaImpactRepository;
    private readonly IMapper _mapper;

    public GetBiaRulesPaginatedListQueryHandler(IBiaRulesRepository biaImpactRepository, IMapper mapper)
    {
        _biaImpactRepository = biaImpactRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<BiaRulesListVm>> Handle(GetBiaRulesPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new BiaRulesFilterSpecification(request.SearchString);

        var queryable =await _biaImpactRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var biaImpactList = _mapper.Map<PaginatedResult<BiaRulesListVm>>(queryable);
           
        return biaImpactList;
        //var queryable = _biaImpactRepository.GetPaginatedQuery();

        //var productFilterSpec = new BiaRulesFilterSpecification(request.SearchString);

        //var biaImpactList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<BiaRulesListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return biaImpactList;
    }
}