﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class NodeFilterSpecification : Specification<Node>
{
    public NodeFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("servername=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.ServerName.Contains(stringItem.Replace("servername=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    else if (stringItem.Contains("name=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    //else if (stringItem.Contains("properties=", StringComparison.InvariantCultureIgnoreCase))
                    //    Or(p => p.Properties.Contains(stringItem.Replace("properties=", "",
                    //        StringComparison.InvariantCultureIgnoreCase)));
                    else if (stringItem.Contains("type=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Type.Contains(stringItem.Replace("type=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.Name.Contains(searchString) || p.ServerName.Contains(searchString) 
                    //|| p.Properties.Contains(searchString) 
                    || p.Type.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.Name != null;
        }
    }
}