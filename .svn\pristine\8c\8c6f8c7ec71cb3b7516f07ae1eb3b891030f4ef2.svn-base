﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerModel.InfraObjectSchedulerViewModel

@*@Html.AntiForgeryToken()*@
<div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
    <form class="modal-content" id="drReadysaveform" asp-controller="ManageResilienceReadiness" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data" class="tab-wizard wizard-circle wizard clearfix example-form">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-dr-readiness"></i><span>Manage Resiliency Readiness Configuration</span></h6>
            <button type="button" class="btn-close drbtn_cancel" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
        </div>

        <div class="modal-body">
            <div class="container ">
                <div class="row row-cols-2 mt-2">
                    <div class="col">
                        <div class="mb-3 form-group">
                            <div class="form-label" >InfraObject Name</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-infra-object"></i></span>
                                <select asp-for="InfraObjectName" id="selectInfraObject" class="form-select-modal" aria-label="Default select example" data-placeholder="Select InfraObject Name">
                                    <option id="select" value=""></option>
                                        @foreach (var infraObject in Model.InfraObjectNameVms)
                                        {
                                            <option id="@infraObject.Id" value="@infraObject.Name">@infraObject.Name</option>
                                        }
                                </select>
                            </div>
                            <input asp-for="InfraObjectId" id="infraobjectname" type="hidden" class="form-control" />
                            <input asp-for="Id" id="textDrReadyId" type="hidden" class="form-control" />
                            <input asp-for="ScheduleTime" id="txtCronViewList" type="hidden" class="form-control" />
                            <input asp-for="Status" id="textStatus" type="hidden" class="form-control" />
                            <input asp-for="IsSchedule" id="textIsSchedule" type="hidden" class="form-control" />
                            <input asp-for="ScheduleType" id="textScheduleType" type="hidden" class="form-control" />
                            <input asp-for="CronExpression" id="textCronExpression" type="hidden" class="form-control" />
                            <input asp-for="LastExecutionTime" id="txtlastexecutetime" type="hidden" class="form-control" />
                            <input asp-for="State" id="txtCronViewList" type="hidden" class="form-control" value="Active" />
                            <input asp-for="CompanyId" type="hidden" class="form-control" id="text_companyid" />
                            <input asp-for="WorkflowType" type="hidden" class="form-control" id="workflowType"  />
                            <input asp-for="WorkflowTypeId" type="hidden" class="form-control" id="workflowTypeId" />
                           @*  <input asp-for="WorkflowType" id="workflowtypename" type="hidden" class="form-control"  /> *@
                            <span asp-validation-for="InfraObjectName" id="infraName-error"></span>
                        </div>
                    </div>
                  @*   <div class="col">
                        <div class="mb-3 form-group">
                            <div class="form-label" >Workflow Type</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-workflow-configuration"></i></span>
                                <select asp-for="WorkflowType" id="selectWorkflowtype" class="form-select-modal" aria-label="Default select example" data-live-search="true" data-placeholder="Select Workflow Type">
                                    <option value=""></option>
                                    <option id="60256798-ae32-4bf3-84c0-2e1a6a26e196" value="Resiliency Ready">Resiliency Ready</option>
                                </select>
                            </div>
                            <span asp-validation-for="WorkflowType" id="workflowtype-error"></span>
                        </div>
                    </div> *@
                    <div class="col">
                        <div class="mb-3 form-group">
                            <div class="form-label">Before Switchover Workflow</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-switch-back"></i></span>
                                <select asp-for="BeforeSwitchOverWorkflowName" id="drbeforeWorkflowDrop" class="form-select-modal" aria-label="Default select example" data-placeholder="Select Workflow Name">
                                    @* <option id="selectdef" value=""></option> *@
                                   @*  @foreach (var workflow in Model.WorkflowNamesvms)
                                    {
                                        <option class="@workflow.Id" value="@workflow.Name">@workflow.Name</option>
                                    } *@
                                </select>
                            </div>
                            <input asp-for="BeforeSwitchOverWorkflowId" id="workflowdrope" type="hidden" class="form-control" />
                            <span asp-validation-for="BeforeSwitchOverWorkflowId" id="beforeswitchover-error"></span>
                        </div>
                    </div>
                    <div class="col">
                        <div class="mb-3 form-group">
                            <div class="form-label">After Switchover Workflow</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-switch-over"></i></span>
                                <select asp-for="AfterSwitchOverWorkflowName" id="drAfterWorkflowDrop" class="form-select-modal" aria-label="Default select example" data-placeholder="Select Workflow Name">
                                   @*  <option id="selectdeflt" value=""></option>                *@                     
                                    @* @foreach (var workflow in Model.WorkflowNamesvms)
                                    {
                                        <option class="@workflow.Id" value="@workflow.Name">@workflow.Name</option>
                                    } *@
                                </select>
                            </div>
                            <input asp-for="AfterSwitchOverWorkflowId" id="Afterworkflowdrope" type="hidden" class="form-control" />
                            <span asp-validation-for="AfterSwitchOverWorkflowId" id="afterworkflowdrope-error"></span>
                        </div>
                    </div>
                    <div class="col ">
                        <div class="mb-3 form-group">
                            <div class="form-label" >Execution Policy</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-group-policy"></i></span>
                                <select asp-for="ExecutionPolicy" class="form-select-modal" data-live-search="true" data-placeholder="Select Execution Policy" id="GroupPolicy">
                                    <option value=""></option>
                                    <option value="2">Distribution Policy</option>
                                    <option value="1">Group Node Policy</option>
                                    
                                    @* <option value="3">Resource Policy</option> *@
                                </select>
                            </div>
                            @*<input asp-for="GroupPolicyName" id="selectGroupPolicy" type="hidden" class="form-control" />*@
                            <input asp-for="NodeId" id="txtNodeId" type="hidden" class="form-control" />
                            <input asp-for="NodeName" id="txtNodeName" type="hidden" class="form-control" />
                            <span asp-validation-for="ExecutionPolicy" id="GroupPolicy-error"></span>
                        </div>
                    </div>
                    <div class="col">
                        <div class="mb-3 form-group" id="ExecutionPolicy">
                            <div class="form-label">Group Node Policy</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-standby-file"></i></span>
                                <select asp-for="GroupPolicyName" id="selectGroupPolicy" class="form-select-modal" data-placeholder="Select Group Node Policy">
                                    <option></option>
                                    @foreach (var GroupPolicies in Model.GroupPolicies)
                                    {
                                        @if (GroupPolicies.Type == "Resiliency Ready Service" || GroupPolicies.Type == "ResiliencyReadyService")
                                        {
                                            <option id="@GroupPolicies.Id" value="@GroupPolicies.GroupName">@GroupPolicies.GroupName</option>
                                        }
                                    }
                                </select>
                            </div>
                            <input asp-for="GroupPolicyName" id="selectGroupPolicy" type="hidden" class="form-control" />
                            <input asp-for="GroupPolicyId" id="selectGroupPolicyId" type="hidden" class="form-control" />
                            @*       <input asp-for="NodeId" id="txtNodeId" type="hidden" class="form-control" />
                            <input asp-for="NodeName" id="txtNodeName" type="hidden" class="form-control" /> *@
                            <span asp-validation-for="GroupPolicyName" id="Policy-error"></span>
                        </div>
                    </div>
                </div>
            <div class="row">
                <div class="col">
                        <div class="switches-container mb-3">
                            <input type="radio" id="switchMonthly" name="switchPlan" value="Cycle" checked="checked" />
                            <input type="radio" id="switchYearly" name="switchPlan" value="Once" />
                            <label for="switchMonthly" class="nav-link">Cycle</label>
                            <label for="switchYearly" class="nav-link">Once</label>
                            <div class="switch-wrapper">
                                <div class="switch">
                                    <div>Cycle</div>
                                    <div>Once</div>
                                </div>
                            </div>
                        </div>
                        <div class="month" id="yeargroup">
                        <div class="form-group ">
                            <div id="clndatetime" class="form-label" >Schedule Time</div>
                            <div class="input-group">
                                <span class="input-group-text"></span>
                                <i class="cp-time"></i>
                                <input id="datetimeCron" class="form-control" name="datetime_currentdate" placeholder="Schedule Time" type="datetime-local" />
                            </div>
                            <span asp-validation-for="ScheduleTime" id="CronExpression-error"></span>
                        </div>
                    </div>
                </div>
                    <div class="row mt-2 w-100 ">
                        <div class="year" id="monthgroup">
                            <div class="mb-3">
                                <div class="form-label">Scheduler</div>
                                <div>
                                    <nav>
                                        <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                            <button class="nav-link active" id="nav-Minutes-tab" data-bs-toggle="tab" name="Scheduler"
                                                    data-bs-target="#nav-Minutes" type="button" role="tab"
                                                    aria-controls="nav-Minutes" aria-selected="true">
                                                Minutes
                                            </button>
                                            <button class="nav-link" id="nav-Hourly-tab" data-bs-toggle="tab" name="Scheduler"
                                                    data-bs-target="#nav-Hourly" type="button" role="tab"
                                                    aria-controls="nav-Hourly" aria-selected="false">
                                                Hourly
                                            </button>
                                            <button class="nav-link" id="nav-Daily-tab" data-bs-toggle="tab" name="Scheduler"
                                                    data-bs-target="#nav-Daily" type="button" role="tab"
                                                    aria-controls="nav-Daily" aria-selected="false">
                                                Daily
                                            </button>
                                            <button class="nav-link" id="nav-Weekly-tab" data-bs-toggle="tab" name="Scheduler"
                                                    data-bs-target="#nav-Weekly" type="button" role="tab"
                                                    aria-controls="nav-Weekly" aria-selected="false">
                                                Weekly
                                            </button>
                                            <button class="nav-link" id="nav-Monthly-tab" data-bs-toggle="tab" name="Scheduler"
                                                    data-bs-target="#nav-Monthly" type="button" role="tab"
                                                    aria-controls="nav-Monthly" aria-selected="false">
                                                Monthly
                                            </button>
                                        </div>
                                    </nav>
                                    <div class="tab-content" id="nav-tabContent">
                                        <div class="tab-pane fade show active" id="nav-Minutes" role="tabpanel" aria-labelledby="nav-home-tab" tabindex="0">
                                            <div class="row mt-2 align-items-end">
                                                <div class="col-3">
                                                    <div class="form-group">
                                                        <div class="form-label">Every</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text pe-0"><i class="cp-calendar"></i></span>
                                                            @Html.TextBox("txtMins", null, new { id = "txtMins", type = "number", maxlength = "2", min = "0", max = "59", pattern = "d{2}", @class = "form-control", @placeholder = "Enter Mins", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                            <span class="input-group-text small">Mins</span>
                                                        </div>
                                                        <span asp-validation-for="CronExpression" id="CronMin-error"></span>
                                                    </div>
                                                </div>
                                                <div class="col-3 d-none">
                                                    <div class="form-group">
                                                        <div class="form-label"></div>
                                                        <div class="input-group">
                                                            <span class="input-group-text pe-0"><i class="cp-calendar"></i></span>
                                                            <input type="number" class="form-control" placeholder="Select Minute" />
                                                            <span class="input-group-text small pe-0">Minute</span>
                                                        </div>
                                                        <span asp-validation-for="CronExpression" id="CronMin-error"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="nav-Hourly" role="tabpanel" aria-labelledby="nav-Hourly-tab" tabindex="0">
                                            <div class="row mt-2">
                                                <div class="col-4">
                                                    <div class="form-group">
                                                        <div class="form-label">Every</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text">
                                                                <i class="cp-calendar"></i>
                                                            </span>
                                                            @Html.TextBox("txtHours", null, new { id = "txtHours", type = "number", min = "0", max = "23", @class = "form-control", @placeholder = "Enter Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                            <span class="input-group-text fs-8 ms-1">
                                                                hrs
                                                            </span>
                                                        </div>
                                                        <span asp-validation-for="CronExpression" id="CronHourly-error"></span>
                                                    </div>
                                                </div>
                                                <div class="col-xl-6">
                                                    <div class="form-group">
                                                        <div class="form-label">Minutes</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text">
                                                                <i class="cp-calendar"></i>
                                                            </span>
                                                            @Html.TextBox("txtMinutes", null, new { id = "txtMinutes", type = "number", min = "0", max = "59", @class = "form-control", @placeholder = "Enter Min(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                            <span class="input-group-text form-label mb-0 text-secondary">Mins</span>
                                                        </div>
                                                        <span asp-validation-for="CronExpression" id="CronHourMin-error"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="nav-Daily" role="tabpanel" aria-labelledby="nav-Daily-tab" tabindex="0">
                                            <div class="row mt-2 align-items-center">
                                                <div class="col-4">
                                                    <div class="form-group flex-fill ">
                                                        <label class="animation-label form-label">
                                                            Select Day Type
                                                        </label>
                                                        <div class="">
                                                            <span class="input-group-text"></span>
                                                            <div class="form-check form-check-inline">
                                                                <input name="daysevery" aria-label="Every Day" type="radio" id="defaultCheck-everyday" class="form-check-input custom-cursor-default-hover" value="everyday" cursorshover="true">
                                                                <label for="defaultCheck-everyday" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Day</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="daysevery" aria-label="Every Week Day" type="radio" id="defaultCheck-MON-FRI" class="form-check-input custom-cursor-default-hover" value="MON-FRI">
                                                                <label for="defaultCheck-MON-FRI" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Week Day</label>
                                                            </div>
                                                        </div>
                                                        <span asp-validation-for="CronExpression" id="Crondaysevery-error"></span>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="form-group">
                                                        <div class="form-label">Starts at</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text"><i class="cp-calendar"></i></span>
                                                            @Html.TextBox("everyHours", null, new { id = "everyHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                        </div>
                                                        <span asp-validation-for="CronExpression" id="CroneveryHour-error"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="nav-Weekly" role="tabpanel" aria-labelledby="nav-Weekly-tab" tabindex="0">
                                            <div class="row row-cols-2 mt-2">
                                                <div class="col-12">
                                                    <div class="form-group">
                                                        <label class="form-label custom-cursor-default-hover">Select Day(s)</label>
                                                        <div class="bg-transparent input-group">
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Monday" type="checkbox" id="defaultCheck-1" class="form-check-input" value="MON"><label for="defaultCheck-1" class="form-check-label custom-cursor-default-hover">Monday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Tuesday" type="checkbox" id="defaultCheck-2" class="form-check-input" value="TUE"><label for="defaultCheck-2" class="form-check-label">Tuesday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Wednesday" type="checkbox" id="defaultCheck-3" class="form-check-input" value="WED"><label for="defaultCheck-3" class="form-check-label" cursorshover="true">Wednesday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Thursday" type="checkbox" id="defaultCheck-4" class="form-check-input" value="THU"><label for="defaultCheck-4" class="form-check-label custom-cursor-default-hover" cursorshover="true">Thursday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Friday" type="checkbox" id="defaultCheck-5" class="form-check-input" value="FRI" cursorshover="true"><label for="defaultCheck-5" class="form-check-label">Friday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Saturday" type="checkbox" id="defaultCheck-6" class="form-check-input" value="SAT"><labelfor ="defaultCheck-6" class="form-check-label">Saturday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Sunday" type="checkbox" id="defaultCheck-0" class="form-check-input" value="SUN"><label for="defaultCheck-0" class="form-check-label">Sunday</label>
                                                            </div>
                                                        </div>
                                                        <span asp-validation-for="CronExpression" id="CronDay-error"></span>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="form-group">
                                                        <div class="form-label">Starts at</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text"><i class="cp-calendar"></i></span>
                                                            @Html.TextBox("ddlHours", null, new { id = "ddlHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                        </div>
                                                        <span asp-validation-for="CronExpression" id="CronddlHour-error"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="nav-Monthly" role="tabpanel" aria-labelledby="nav-Monthly-tab" tabindex="0">
                                            <div class="row row-cols-2 mt-2">
                                                <div class="col-4">
                                                    <div class="mb-3 form-group">
                                                        <div class="form-label">Select Month And Year</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text">
                                                                <i class="cp-calendar"></i>
                                                            </span>
                                                            <input name="month" autocomplete="off" type="month"
                                                                   id="lblMonth"
                                                                   class="form-control custom-cursor-default-hover"
                                                                   cursorshover="true" min="2025-02" max="2101-02" />
                                                        </div>
                                                        <span asp-validation-for="CronExpression" id="CronMonthly-error"></span>
                                                    </div>
                                                </div>
                                                <div class="col-12 pe-0">
                                                    <div class="mb-3 form-group text-justify " style="display: inline-table;">
                                                        <div class="form-label mb-2">Select Date(s)</div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox1" value="1">
                                                            <label class="form-check-label checklabel">1</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox2" value="2">
                                                            <label class="form-check-label checklabel">2</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox3" value="3">
                                                            <label class="form-check-label checklabel">3</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox4" value="4">
                                                            <label class="form-check-label checklabel">4</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox5" value="5">
                                                            <label class="form-check-label checklabel">5</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox6" value="6">
                                                            <label class="form-check-label checklabel">6</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox7" value="7">
                                                            <label class="form-check-label checklabel">7</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox8" value="8">
                                                            <label class="form-check-label checklabel">8</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox9" value="9">
                                                            <label class="form-check-label checklabel">9</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox10" value="10">
                                                            <label class="form-check-label checklabel">10</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox11" value="11">
                                                            <label class="form-check-label checklabel">11</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox12" value="12">
                                                            <label class="form-check-label checklabel">12</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox13" value="13">
                                                            <label class="form-check-label checklabel">13</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox14" value="14">
                                                            <label class="form-check-label checklabel">14</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox15" value="15">
                                                            <label class="form-check-label checklabel">15</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox16" value="16">
                                                            <label class="form-check-label checklabel">16</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox17" value="17">
                                                            <label class="form-check-label checklabel">17</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox18" value="18">
                                                            <label class="form-check-label checklabel">18</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox19" value="19">
                                                            <label class="form-check-label checklabel">19</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox20" value="20">
                                                            <label class="form-check-label checklabel">20</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox21" value="21">
                                                            <label class="form-check-label checklabel">21</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox22" value="22">
                                                            <label class="form-check-label checklabel">22</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox23" value="23">
                                                            <label class="form-check-label checklabel">23</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox24" value="24">
                                                            <label class="form-check-label checklabel">24</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox25" value="25">
                                                            <label class="form-check-label checklabel">25</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox26" value="26">
                                                            <label class="form-check-label checklabel">26</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox27" value="27">
                                                            <label class="form-check-label checklabel">27</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox28" value="28">
                                                            <label class="form-check-label checklabel">28</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox29" value="29">
                                                            <label class="form-check-label checklabel">29</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox30" value="30">
                                                            <label class="form-check-label checklabel">30</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox31" value="31">
                                                            <label class="form-check-label checklabel">31</label>
                                                        </div>
                                                        <div class="form-group">
                                                            <span asp-validation-for="CronExpression" id="CronMon-error"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="form-group">
                                                        <div class="form-label">Starts at</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text">
                                                                <i class="cp-calendar"></i>
                                                            </span>
                                                            @Html.TextBox("MonthlyHours", null, new { id = "MonthlyHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                        </div>
                                                        <span asp-validation-for="CronExpression" id="MonthlyHours-error"></span>
                                                        <input asp-for="IsSchedule" id="textIsSchedule" type="hidden" class="form-control" />
                                                        <input asp-for="ScheduleType" id="textScheduleType" type="hidden" class="form-control" />
                                                        <input asp-for="ScheduleTime" id="textScheduleTime" type="hidden" class="form-control" />
                                                        <input asp-for="CronExpression" id="textCronExpression" type="hidden" class="form-control" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


            </div>
            </div>
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary">
                <i class="cp-note me-1"></i>Note: All fields are mandatory except optional
            </small>
            @*<div class="gap-2 d-flex">
            <button type="button" class="btn btn-secondary btn-sm"
            data-bs-dismiss="modal" title="Cancel">
            Cancel
            </button>
            <a id="btnDrReadysave" type="button" class="btn btn-primary finish_btn btn-sm" href="javascript:void(0)" role="menuitem" onclick="form.steps('finish')" title="Save">Save</a>
            </div>*@
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm drbtn_cancel" data-bs-dismiss="modal">Cancel</button>
                <button id="btnDrReadysave" type="button" class="btn btn-primary finish_btn btn-sm" >Save</button>
            </div>
        </div>

    </form>

</div>

