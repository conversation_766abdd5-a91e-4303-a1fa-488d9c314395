﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class InfraObjectSchedulerWorkflowDetailRepositoryMocks
{
    public static Mock<IInfraObjectSchedulerWorkflowDetailRepository> CreateInfraObjectSchedulerWorkflowDetailRepository(List<InfraObjectSchedulerWorkflowDetail> infraObjectSchedulerWorkflowDetails)
    {
        var infraObjectSchedulerWorkflowDetailRepository = new Mock<IInfraObjectSchedulerWorkflowDetailRepository>();

        infraObjectSchedulerWorkflowDetailRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjectSchedulerWorkflowDetails);

        infraObjectSchedulerWorkflowDetailRepository.Setup(repo => repo.AddAsync(It.IsAny<InfraObjectSchedulerWorkflowDetail>())).ReturnsAsync(
            (InfraObjectSchedulerWorkflowDetail infraObjectSchedulerWorkflowDetail) =>
            {
                infraObjectSchedulerWorkflowDetail.Id = new Fixture().Create<int>();

                infraObjectSchedulerWorkflowDetail.ReferenceId = new Fixture().Create<Guid>().ToString();

                infraObjectSchedulerWorkflowDetails.Add(infraObjectSchedulerWorkflowDetail);

                return infraObjectSchedulerWorkflowDetail;
            });

        return infraObjectSchedulerWorkflowDetailRepository;
    }

    public static Mock<IInfraObjectSchedulerWorkflowDetailRepository> UpdateInfraObjectSchedulerWorkflowDetailRepository(List<InfraObjectSchedulerWorkflowDetail> infraObjectSchedulerWorkflowDetails)
    {
        var infraObjectSchedulerWorkflowDetailRepository = new Mock<IInfraObjectSchedulerWorkflowDetailRepository>();

        infraObjectSchedulerWorkflowDetailRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjectSchedulerWorkflowDetails);

        infraObjectSchedulerWorkflowDetailRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraObjectSchedulerWorkflowDetails.SingleOrDefault(x => x.ReferenceId == i));

        infraObjectSchedulerWorkflowDetailRepository.Setup(repo => repo.UpdateAsync(It.IsAny<InfraObjectSchedulerWorkflowDetail>())).ReturnsAsync((InfraObjectSchedulerWorkflowDetail infraObjectSchedulerWorkflowDetail) =>
        {
            var index = infraObjectSchedulerWorkflowDetails.FindIndex(item => item.ReferenceId == infraObjectSchedulerWorkflowDetail.ReferenceId);

            infraObjectSchedulerWorkflowDetails[index] = infraObjectSchedulerWorkflowDetail;

            return infraObjectSchedulerWorkflowDetail;

        });
        return infraObjectSchedulerWorkflowDetailRepository;
    }

    public static Mock<IInfraObjectSchedulerWorkflowDetailRepository> DeleteInfraObjectSchedulerWorkflowDetailRepository(List<InfraObjectSchedulerWorkflowDetail> infraObjectSchedulerWorkflowDetails)
    {
        var infraObjectSchedulerWorkflowDetailRepository = new Mock<IInfraObjectSchedulerWorkflowDetailRepository>();

        infraObjectSchedulerWorkflowDetailRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjectSchedulerWorkflowDetails);

        infraObjectSchedulerWorkflowDetailRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraObjectSchedulerWorkflowDetails.SingleOrDefault(x => x.ReferenceId == i));

        infraObjectSchedulerWorkflowDetailRepository.Setup(repo => repo.UpdateAsync(It.IsAny<InfraObjectSchedulerWorkflowDetail>())).ReturnsAsync((InfraObjectSchedulerWorkflowDetail infraObjectSchedulerWorkflowDetail) =>
        {
            var index = infraObjectSchedulerWorkflowDetails.FindIndex(item => item.ReferenceId == infraObjectSchedulerWorkflowDetail.ReferenceId);

            infraObjectSchedulerWorkflowDetail.IsActive = false;

            infraObjectSchedulerWorkflowDetails[index] = infraObjectSchedulerWorkflowDetail;

            return infraObjectSchedulerWorkflowDetail;
        });

        return infraObjectSchedulerWorkflowDetailRepository;
    }   

    public static Mock<IInfraObjectSchedulerWorkflowDetailRepository> GetInfraObjectSchedulerWorkflowDetailRepository(List<InfraObjectSchedulerWorkflowDetail> infraObjectSchedulerWorkflowDetails)
    {
        var infraObjectSchedulerWorkflowDetailRepository = new Mock<IInfraObjectSchedulerWorkflowDetailRepository>();

        infraObjectSchedulerWorkflowDetailRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjectSchedulerWorkflowDetails);

        infraObjectSchedulerWorkflowDetailRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraObjectSchedulerWorkflowDetails.SingleOrDefault(x => x.ReferenceId == i));

        return infraObjectSchedulerWorkflowDetailRepository;
    }

    public static Mock<IInfraObjectSchedulerWorkflowDetailRepository> GetInfraObjectSchedulerWorkflowDetailEmptyRepository()
    {
        var infraObjectInfoRepository = new Mock<IInfraObjectSchedulerWorkflowDetailRepository>();

        infraObjectInfoRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<InfraObjectSchedulerWorkflowDetail>());

        return infraObjectInfoRepository;
    }

    public static Mock<IInfraObjectSchedulerWorkflowDetailRepository> GetPaginatedInfraObjectSchedulerWorkflowDetailRepository(List<InfraObjectSchedulerWorkflowDetail> infraObjectSchedulerWorkflowDetails)
    {
        var infraObjectSchedulerWorkflowDetailsRepository = new Mock<IInfraObjectSchedulerWorkflowDetailRepository>();

        var queryableInfraObjectSchedulerWorkflowDetails = infraObjectSchedulerWorkflowDetails.BuildMock();

        infraObjectSchedulerWorkflowDetailsRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableInfraObjectSchedulerWorkflowDetails);

        return infraObjectSchedulerWorkflowDetailsRepository;
    }
}