﻿namespace ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;

public class GetUserInfoDetailQueryHandler : IRequestHandler<GetUserInfoDetailQuery, UserInfoDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IUserInfoRepository _userInfoRepository;

    public GetUserInfoDetailQueryHandler(IMapper mapper, IUserInfoRepository userInfoRepository)
    {
        _mapper = mapper;
        _userInfoRepository = userInfoRepository;
    }

    public async Task<UserInfoDetailVm> Handle(GetUserInfoDetailQuery request, CancellationToken cancellationToken)
    {
        var userInfo = await _userInfoRepository.GetUserInfoByUserIdAsync(request.UserId);

        Guard.Against.NullOrDeactive(userInfo, nameof(Domain.Entities.UserInfo),
            new NotFoundException(nameof(Domain.Entities.UserInfo), request.UserId));

        var userInfoDetailDto = _mapper.Map<UserInfoDetailVm>(userInfo);

        return userInfoDetailDto;
    }
}