﻿using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetMYSQLMonitorStatusByInfraObjectId;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MYSQLMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class MysqlMonitorStatusController : CommonBaseController
{
    [HttpPost]
    [Authorize(Policy = Permissions.Manage.Create)]
    public async Task<ActionResult<CreateMYSQLMonitorStatusResponse>> CreateMysqlMonitorStatus([FromBody] CreateMYSQLMonitorStatusCommand createMysqlMonitorStatusCommand)
    {
        Logger.LogDebug($"Create MYSQL Monitor Status '{createMysqlMonitorStatusCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateMysqlMonitorStatus), await Mediator.Send(createMysqlMonitorStatusCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Manage.Edit)]
    public async Task<ActionResult<UpdateMYSQLMonitorStatusResponse>> UpdateMysqlMonitorStatus([FromBody] UpdateMYSQLMonitorStatusCommand updateMysqlMonitorStatusCommand)
    {
        Logger.LogDebug($"Update MYSQL Monitor Status '{updateMysqlMonitorStatusCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateMysqlMonitorStatusCommand));
    }

    [HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<MYSQLMonitorStatusListVm>>> GetAllMysqlMonitorStatus()
    {
        Logger.LogDebug("Get All  MYSQL Monitor Status");

        return Ok(await Mediator.Send(new GetMYSQLMonitorStatusListQuery()));
    }
    [HttpGet("{id}")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<MYSQLMonitorStatusDetailVm>> GetMysqlMonitorStatusById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "MYSQL MonitorStatus Detail By Id");

        Logger.LogDebug($"Get  MYSQL Monitor Status Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetMYSQLMonitorStatusDetailQuery { Id = id }));
    }
    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<MYSQLMonitorStatusDetailVm>>> GetPaginatedMysqlMonitorStatus([FromQuery] GetMYSQLMonitorStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in MYSQL MonitorStatus Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet("type")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<MYSQLMonitorStatusDetailByTypeVm>> GetMysqlMonitorStatusByType(string type)
    {
        Guard.Against.NullOrEmpty(type, "MYSQL Monitor Status Detail By Type");

        Logger.LogDebug($"Get  MYSQL Monitor Status Detail by Id '{type}'");

        return Ok(await Mediator.Send(new GetMYSQLMonitorStatusDetailByTypeQuery { Type = type }));
    }

    [HttpGet("by/{infraObjectId}")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<string>> GetMysqlMonitorStatusByInfraObjectId(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "MYSQL MonitorStatus InfraObjectId");

        Logger.LogDebug($"Get MYSQL MonitorStatus Detail by InfraObjectId '{infraObjectId}'");

        return Ok(await Mediator.Send(new GetMYSQLMonitorStatusByInfraObjectIdQuery { InfraObjectId = infraObjectId }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllMYSQLMonitorStatusCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllMYSQLMonitorStatusNameCacheKey };

        ClearCache(cacheKeys);
    }
}
