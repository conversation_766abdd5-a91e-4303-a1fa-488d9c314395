using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.FiaIntervalModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.FiaInterval.Queries.GetPaginatedList;

public class
    GetFiaIntervalPaginatedListQueryHandler : IRequestHandler<GetFiaIntervalPaginatedListQuery,
        PaginatedResult<FiaIntervalListVm>>
{
    private readonly IFiaIntervalRepository _fiaIntervalRepository;
    private readonly IMapper _mapper;

    public GetFiaIntervalPaginatedListQueryHandler(IMapper mapper, IFiaIntervalRepository fiaIntervalRepository)
    {
        _mapper = mapper;
        _fiaIntervalRepository = fiaIntervalRepository;
    }

    public async Task<PaginatedResult<FiaIntervalListVm>> Handle(GetFiaIntervalPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _fiaIntervalRepository.GetPaginatedQuery();

        var fiaIntervalList = await queryable
            .Select(m => _mapper.Map<FiaIntervalListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return fiaIntervalList;
    }
}