﻿using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AlertNotificationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertNotification.Queries;

public class GetAlertNotificationPaginatedListQueryHandlerTests : IClassFixture<AlertNotificationFixture>
{
    private readonly GetAlertNotificationPaginatedListQueryHandler _handler;

    private readonly Mock<IAlertNotificationRepository> _mockAlertNotificationRepository;

    public GetAlertNotificationPaginatedListQueryHandlerTests(AlertNotificationFixture alertNotificationFixture)
    {
        var alertNotificationNewFixture = alertNotificationFixture;

        alertNotificationNewFixture.AlertNotifications[0].AlertType = "Row";

        alertNotificationNewFixture.AlertNotifications[1].AlertType = "Row123";
            

        _mockAlertNotificationRepository = AlertNotificationRepositoryMocks.GetPaginatedAlertNotificationRepository(alertNotificationNewFixture.AlertNotifications);

        _handler = new GetAlertNotificationPaginatedListQueryHandler(alertNotificationNewFixture.Mapper, _mockAlertNotificationRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetAlertNotificationPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Row" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertNotificationListVm>>();

        result.TotalCount.ShouldBe(2);
    }

    [Fact]
    public async Task Handle_Return_PaginatedAlertNotification_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetAlertNotificationPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Row123" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertNotificationListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<AlertNotificationListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].AlertType.ShouldBe("Row123");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetAlertNotificationPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertNotificationListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_AlertNotification_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetAlertNotificationPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "alerttype=Row123;" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertNotificationListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].AlertType.ShouldBe("Row123");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetAlertNotificationPaginatedListQuery(), CancellationToken.None);

        _mockAlertNotificationRepository.Verify(x => x.PaginatedListAllAsync(), Times.Once);
    }
}