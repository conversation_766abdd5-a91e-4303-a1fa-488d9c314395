using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowFixture : IDisposable
{
    public List<Workflow> WorkflowPaginationList { get; set; }
    public List<Workflow> WorkflowList { get; set; }
    public Workflow WorkflowDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowFixture()
    {
        var fixture = new Fixture();

        WorkflowList = fixture.Create<List<Workflow>>();

        WorkflowPaginationList = fixture.CreateMany<Workflow>(20).ToList();

        WorkflowPaginationList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowDto = fixture.Create<Workflow>();

        WorkflowDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
