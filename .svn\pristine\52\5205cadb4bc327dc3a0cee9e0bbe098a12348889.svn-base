﻿using ContinuityPatrol.Application.Features.TeamMaster.Commands.Create;
using ContinuityPatrol.Application.Features.TeamMaster.Commands.Update;
using ContinuityPatrol.Application.Features.TeamMaster.Events.PaginatedView;
using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.TeamMasterModel;
using ContinuityPatrol.Domain.ViewModels.TeamResourceModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class TeamMasterControllerShould
    {
        private TeamMasterController _controller;
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<Microsoft.Extensions.Logging.ILogger<TeamMasterController>> _mocklogger = new();

        public TeamMasterControllerShould()
        {
           
            _controller = new TeamMasterController(_mockPublisher.Object, _mockMapper.Object, _mockDataProvider.Object, _mocklogger.Object);
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ReturnsViewResult_WithTeamMasterViewModel()
        {
            
            var paginatedList = new PaginatedResult<TeamMasterListVm>(); 
            var teamNames = new List<TeamMasterDetailVm>();

            var teamMembers = new List<TeamResourceListVm>();
            

            //_mockPublisher.Setup(p => p.Publish(It.IsAny<TeamMasterPaginatedEvent>())).Returns(Task.CompletedTask);
            _mockDataProvider.Setup(dp => dp.TeamMasterService.GetTeamConfigurationList(It.IsAny<GetTeamMasterPaginatedListQuery>()))
                .ReturnsAsync(paginatedList);
            _mockDataProvider.Setup(dp => dp.TeamMasterService.GetAllTeamNames())
                .ReturnsAsync(teamNames);
            _mockDataProvider.Setup(dp => dp.TeamResourceService.GetTeamMemberNames())
                .ReturnsAsync(teamMembers);

            
            var result = await _controller.List();

            
            var viewResult = Assert.IsType<ViewResult>(result);
            var model = Assert.IsType<TeamMasterViewModel>(viewResult.Model);
            Assert.Empty(model.TeamConfigurationsList);
        }

        [Fact]
        public void UpdateTeamList_ReturnsListOfTeamMasterListVm()
        {
            

            var teamSelectionList = new List<TeamMasterListVm>
            {
                new TeamMasterListVm { GroupName = "Team A", Description = "Description A" }
            };

            // Act
            var result = _controller.UpdateTeamList(teamSelectionList);

            // Assert
            Assert.NotEmpty(result);
            Assert.Equal(1, result.Count);
            Assert.Equal("Team A", result.First().GroupName);
        }

        [Fact]
        public async Task CreateOrUpdate_WithNewTeam_CallsCreateAsync()
        {
            // Arrange
            var teamMasterViewModel = new TeamMasterViewModel { GroupName = "New Team" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", ""); // Empty Id means create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateTeamMasterCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateTeamMasterCommand>(It.IsAny<TeamMasterViewModel>())).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.TeamMasterService.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(teamMasterViewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.TeamMasterService.CreateAsync(It.IsAny<CreateTeamMasterCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_WithExistingTeam_CallsUpdateAsync()
        {
            
            var teamMasterViewModel = new TeamMasterViewModel();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateTeamMasterCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateTeamMasterCommand>(It.IsAny<TeamMasterViewModel>())).Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.TeamMasterService.UpdateAsync(updateCommand)).ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(teamMasterViewModel);

            
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task Delete_ReturnsRedirectToActionResult()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

            _mockDataProvider.Setup(dp => dp.TeamMasterService.DeleteAsync(id)).ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task IsTeamNameAlreadyExist_ReturnsTrue()
        {
            // Arrange
            var teamName = "Team A";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.TeamMasterService.IsTeamNameAlreadyExist(teamName, id)).ReturnsAsync(true);

            // Act
            var result = await _controller.IsTeamNameAlreadyExist(teamName, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResult()
        {
            // Arrange
            var query = new GetTeamMasterPaginatedListQuery();
            var paginatedList = new PaginatedResult<TeamMasterListVm>();
            _mockDataProvider.Setup(dp => dp.TeamMasterService.GetTeamConfigurationList(query)).ReturnsAsync(paginatedList);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(paginatedList, jsonResult.Value);
        }

        // Additional test cases for 100% coverage

        [Fact]
        public async Task List_WithTeamMembersData_ReturnsCorrectMemberCount()
        {
            // Arrange
            var paginatedList = new PaginatedResult<TeamMasterListVm>();
            var teamNames = new List<TeamMasterDetailVm>
            {
                new TeamMasterDetailVm { Id = "1", GroupName = "Team A", Description = "Description A" }
            };
            var teamMembers = new List<TeamResourceListVm>
            {
                new TeamResourceListVm { TeamMasterName = "Team A" },
                new TeamResourceListVm { TeamMasterName = "Team A" }
            };

            _mockDataProvider.Setup(dp => dp.TeamMasterService.GetTeamConfigurationList(It.IsAny<GetTeamMasterPaginatedListQuery>()))
                .ReturnsAsync(paginatedList);
            _mockDataProvider.Setup(dp => dp.TeamMasterService.GetAllTeamNames())
                .ReturnsAsync(teamNames);
            _mockDataProvider.Setup(dp => dp.TeamResourceService.GetTeamMemberNames())
                .ReturnsAsync(teamMembers);

            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            var model = Assert.IsType<TeamMasterViewModel>(viewResult.Model);
            Assert.Single(model.TeamConfigurationsList);
            Assert.Equal("2", model.TeamConfigurationsList.First().MemberCount);
        }

        [Fact]
        public void UpdateTeamList_WithEmptyList_ReturnsEmptyList()
        {
            // Arrange
            var teamSelectionList = new List<TeamMasterListVm>();

            // Act
            var result = _controller.UpdateTeamList(teamSelectionList);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public void UpdateTeamList_WithMultipleItems_ReturnsCorrectCount()
        {
            // Arrange
            var teamSelectionList = new List<TeamMasterListVm>
            {
                new TeamMasterListVm { GroupName = "Team A", Description = "Description A" },
                new TeamMasterListVm { GroupName = "Team B", Description = "Description B" }
            };

            // Act
            var result = _controller.UpdateTeamList(teamSelectionList);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.Equal("Team A", result[0].GroupName);
            Assert.Equal("Team B", result[1].GroupName);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidationException_ReturnsRedirectWithWarning()
        {
            // Arrange
            var teamMasterViewModel = new TeamMasterViewModel { GroupName = "Test Team" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateTeamMasterCommand();
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Property", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<CreateTeamMasterCommand>(It.IsAny<TeamMasterViewModel>())).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.TeamMasterService.CreateAsync(createCommand)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(teamMasterViewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_WithGeneralException_ReturnsRedirectWithWarning()
        {
            // Arrange
            var teamMasterViewModel = new TeamMasterViewModel { GroupName = "Test Team" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateTeamMasterCommand();
            var exception = new Exception("General error occurred");

            _mockMapper.Setup(m => m.Map<CreateTeamMasterCommand>(It.IsAny<TeamMasterViewModel>())).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.TeamMasterService.CreateAsync(createCommand)).ThrowsAsync(exception);

            // Act
            var result = await _controller.CreateOrUpdate(teamMasterViewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatePath_WithValidationException_ReturnsRedirectWithWarning()
        {
            // Arrange
            var teamMasterViewModel = new TeamMasterViewModel { GroupName = "Test Team" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "123");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateTeamMasterCommand();
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Property", "Update validation error"));
            var validationException = new ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<UpdateTeamMasterCommand>(It.IsAny<TeamMasterViewModel>())).Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.TeamMasterService.UpdateAsync(updateCommand)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(teamMasterViewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task Delete_WithException_ReturnsRedirectWithWarning()
        {
            // Arrange
            var id = "1";
            var exception = new Exception("Delete error occurred");

            _mockDataProvider.Setup(dp => dp.TeamMasterService.DeleteAsync(id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task IsTeamNameAlreadyExist_WithException_ReturnsFalse()
        {
            // Arrange
            var teamName = "Team A";
            var id = "1";
            var exception = new Exception("Service error");

            _mockDataProvider.Setup(dp => dp.TeamMasterService.IsTeamNameAlreadyExist(teamName, id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.IsTeamNameAlreadyExist(teamName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetPagination_WithException_ReturnsJsonException()
        {
            // Arrange
            var query = new GetTeamMasterPaginatedListQuery();
            var exception = new Exception("Pagination error");

            _mockDataProvider.Setup(dp => dp.TeamMasterService.GetTeamConfigurationList(query)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            // The result should be the exception converted to JSON format
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task List_PublishesTeamMasterPaginatedEvent()
        {
            // Arrange
            var paginatedList = new PaginatedResult<TeamMasterListVm>();
            var teamNames = new List<TeamMasterDetailVm>();
            var teamMembers = new List<TeamResourceListVm>();

            _mockDataProvider.Setup(dp => dp.TeamMasterService.GetTeamConfigurationList(It.IsAny<GetTeamMasterPaginatedListQuery>()))
                .ReturnsAsync(paginatedList);
            _mockDataProvider.Setup(dp => dp.TeamMasterService.GetAllTeamNames())
                .ReturnsAsync(teamNames);
            _mockDataProvider.Setup(dp => dp.TeamResourceService.GetTeamMemberNames())
                .ReturnsAsync(teamMembers);

            // Act
            var result = await _controller.List();

            // Assert
            _mockPublisher.Verify(p => p.Publish(It.IsAny<TeamMasterPaginatedEvent>(), default), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_WithNullOrWhitespaceId_CallsCreatePath()
        {
            // Arrange
            var teamMasterViewModel = new TeamMasterViewModel { GroupName = "New Team" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "   "); // Whitespace Id should trigger create path
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateTeamMasterCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateTeamMasterCommand>(It.IsAny<TeamMasterViewModel>())).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.TeamMasterService.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(teamMasterViewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.TeamMasterService.CreateAsync(It.IsAny<CreateTeamMasterCommand>()), Times.Once);
            _mockDataProvider.Verify(dp => dp.TeamMasterService.UpdateAsync(It.IsAny<UpdateTeamMasterCommand>()), Times.Never);
        }
    }
}
