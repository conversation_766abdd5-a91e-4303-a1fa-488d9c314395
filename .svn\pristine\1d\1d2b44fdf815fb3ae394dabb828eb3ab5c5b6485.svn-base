﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatus;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;

namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningUserDetails;

public class GetWorkflowOperationGroupRunningUserDetailsQueryHandler : IRequestHandler<
    GetWorkflowOperationGroupRunningUserDetailQuery, List<WorkflowOperationGroupRunningUserVm>>
{
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IMapper _mapper;
    private readonly IWorkflowActionResultRepository _workflowActionResultRepository;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IWorkflowOperationRepository _workflowOperationRepository;
    private readonly ILoadBalancerRepository _loadBalancerRepository;

    public GetWorkflowOperationGroupRunningUserDetailsQueryHandler(IMapper mapper,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository,
        IWorkflowOperationRepository workflowOperationRepository,
        IWorkflowActionResultRepository workflowActionResultRepository, 
        IInfraObjectRepository infraObjectRepository, ILoadBalancerRepository loadBalancerRepository)
    {
        _mapper = mapper;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
        _workflowOperationRepository = workflowOperationRepository;
        _workflowActionResultRepository = workflowActionResultRepository;
        _infraObjectRepository = infraObjectRepository;
        _loadBalancerRepository = loadBalancerRepository;
    }

    public async Task<List<WorkflowOperationGroupRunningUserVm>> Handle(
        GetWorkflowOperationGroupRunningUserDetailQuery request, CancellationToken cancellationToken)
    {
        #region OldCode

        //var workflowOperationRunningStatus = new List<Domain.Entities.WorkflowOperation>();

        //var splitUserId = Regex.Split(request.UserId, ",");

        //foreach (var userId in splitUserId)
        //{
        //    var workflowOperation = await _workflowOperationRepository.GetWorkflowOperationByRunningUserId(userId);
        //    workflowOperationRunningStatus.AddRange(workflowOperation);
        //}

        //var workflowOperationGroup = new List<Domain.Entities.WorkflowOperationGroup>();

        //foreach (var workflowOperation in workflowOperationRunningStatus)
        //{
        //    var workflowOperationGroupRunningStatus = await _workflowOperationGroupRepository.GetWorkflowOperationGroupByWorkflowOperationId(workflowOperation.ReferenceId);
        //    workflowOperationGroup.AddRange(workflowOperationGroupRunningStatus);
        //}

        //var workflowOperationListVm =
        //    _mapper.Map<List<WorkflowOperationGroupRunningUserVm>>(workflowOperationRunningStatus);

        //var workflowOperationGroupListVm = _mapper.Map<List<WorkflowOperationGroupListVm>>(workflowOperationGroup);

        //foreach (var groupVm in workflowOperationGroupListVm.Where(groupVm => groupVm.InfraObjectId.IsNotNullOrWhiteSpace()))
        //{
        //    var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(groupVm.InfraObjectId);
        //    groupVm.State = infraObject?.State ?? string.Empty;

        //    if (groupVm.NodeId.IsNotNullOrWhiteSpace())
        //    {
        //        var loadBalancer = await _loadBalancerRepository.GetByReferenceIdAsync(groupVm.NodeId);
        //        groupVm.NodeName = loadBalancer?.Name ?? string.Empty;
        //    }
        //}

        //workflowOperationListVm.ForEach(x =>
        //    x.WorkflowOperationGroupListVm.AddRange(
        //        workflowOperationGroupListVm.Where(y => y.WorkflowOperationId == x.Id)));

        //foreach (var workflowOperation in workflowOperationListVm)
        //{
        //    var workflowActionResultStatusCount = await _workflowActionResultRepository
        //        .GetWorkflowActionResultByWorkflowOperationId(workflowOperation.Id);

        //    workflowOperation.WorkflowActionStatusCount =
        //        _mapper.Map<WorkflowActionStatusCount>(workflowActionResultStatusCount);
        //}

        #endregion

        var userIds = Regex.Split(request.UserId, ",")
            .Where(id => id.IsNotNullOrWhiteSpace())
            .ToList();

        var workflowOperationRunningStatus = await _workflowOperationRepository.GetWorkflowOperationByRunningUserId(userIds);

        var operationIds = workflowOperationRunningStatus
            .Where(x => x.ReferenceId.IsNotNullOrWhiteSpace())
            .Select(x => x.ReferenceId)
            .ToList();

        var workflowOperationGroup = await _workflowOperationGroupRepository
            .GetOperationGroupByWorkflowOperationIds(operationIds);

        var workflowOperationListVm =
            _mapper.Map<List<WorkflowOperationGroupRunningUserVm>>(workflowOperationRunningStatus);

        var workflowOperationGroupListVm = _mapper.Map<List<WorkflowOperationGroupListVm>>(workflowOperationGroup);


        var result = workflowOperationGroupListVm
            .Where(groupVm => groupVm.InfraObjectId.IsNotNullOrWhiteSpace() || groupVm.NodeId.IsNotNullOrWhiteSpace())
            .Aggregate(new { InfraIds = new HashSet<string>(), NodeIds = new HashSet<string>() }, (acc, groupVm) =>
            {
                if (groupVm.InfraObjectId.IsNotNullOrWhiteSpace())
                    acc.InfraIds.Add(groupVm.InfraObjectId);

                if (groupVm.NodeId.IsNotNullOrWhiteSpace())
                {
                    acc.NodeIds.UnionWith(groupVm.NodeId.Split(',').Select(x => x));

                    // acc.NodeIds.Add(groupVm.NodeId);
                }

                return acc;
            });

        var infraObjects = result.InfraIds.Count > 0
            ? await _infraObjectRepository.GetInfraStateByReferenceIds(result.InfraIds.ToList())
            : new List<Domain.Entities.InfraObject>();

        var loadBalancers = result.NodeIds.Count > 0
            ? await _loadBalancerRepository.GetNodeNameByIdAsync(result.NodeIds.ToList())
            : new List<Domain.Entities.LoadBalancer>();

        workflowOperationGroupListVm = workflowOperationGroupListVm.Select(groupVm =>
        {
            groupVm.State = infraObjects.FirstOrDefault(x => x.ReferenceId == groupVm.InfraObjectId)?.State ?? string.Empty;

            var nodeNames = groupVm.NodeId?
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(id => loadBalancers.FirstOrDefault(lb => lb.ReferenceId == id)?.Name)
                .Where(name => name.IsNotNullOrWhiteSpace())
                .ToList();

            groupVm.NodeName = nodeNames != null && nodeNames.Any()
                ? string.Join("/", nodeNames)
                : string.Empty;

            // groupVm.NodeName = loadBalancers.FirstOrDefault(x => x.ReferenceId == groupVm.NodeId)?.Name ?? string.Empty;
            return groupVm;
        }).ToList();

        workflowOperationListVm.ForEach(x =>
            x.WorkflowOperationGroupListVm.AddRange(
                workflowOperationGroupListVm.Where(y => y.WorkflowOperationId == x.Id)));

        var workflowActionResultStatusCount = await _workflowActionResultRepository.GetByWorkflowOperationIds(operationIds);

       // var workflowActionCounts = _mapper.Map<List<WorkflowActionStatusCount>>(workflowActionResultStatusCount);

        var workflowActionCounts = workflowActionResultStatusCount
            .GroupBy(x => x.WorkflowOperationId)
            .Select(group => new WorkflowActionStatusCount
            {
                WorkflowOperationId = group.Key ?? "NA",
                SkipCount = group.Count(x => x.Status?.Trim().ToLower() == "skip" || x.Status?.Trim().ToLower() == "skipped"),
                SuccessCount = group.Count(x => x.Status?.Trim().ToLower() == "success"),
                BypassedCount = group.Count(x => x.Status?.Trim().ToLower() == "bypassed"),
                ErrorCount = group.Count(x => x.Status?.Trim().ToLower() == "error"),
                RunningCount = group.Count(x => x.Status?.Trim().ToLower() == "running")
            }).ToList();

        workflowOperationListVm.ForEach(actionCount =>
        {
            actionCount.WorkflowActionStatusCount = workflowActionCounts.FirstOrDefault(x => x.WorkflowOperationId == actionCount.Id) ?? new WorkflowActionStatusCount();
        });

        return workflowOperationListVm;
    }
}