﻿using ContinuityPatrol.Web.Areas.Admin.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class PluginManagerHistoryControllerTests
    {
        [Fact]
        public void List_ReturnsViewResult()
        {
            var controller = new PluginManagerHistoryController();

            var result = controller.List();

            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Null(viewResult.ViewName); 
        }
    }
}

