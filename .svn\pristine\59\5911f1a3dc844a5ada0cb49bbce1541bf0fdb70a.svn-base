﻿using ContinuityPatrol.Application.Features.Site.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Domain.ViewModels.SiteModel;

namespace ContinuityPatrol.Application.UnitTests.Features.Site.Queries;

public class GetSiteNameQueryHandlerTests : IClassFixture<SiteFixture>
{
   
    private Mock<ISiteRepository> _mockSiteRepository=new();
    private Mock<ISiteTypeRepository> _mockSiteTypeRepository=new();
    private readonly Mock<IMapper> _mockmapper= new();

    private readonly GetSiteNameQueryHandler _handler;

   
    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        var sitenames= new List<Domain.Entities.Site> { new Domain.Entities.Site { 
        
        }};
        var siteNameVm = new List<SiteNameVm> { new SiteNameVm {

        }};
        _mockmapper.Setup(x => x.Map<List<SiteNameVm>>(sitenames)).Returns(siteNameVm);
        _mockSiteRepository.Setup(x => x.GetSiteNames()).ReturnsAsync(sitenames);
        _mockSiteTypeRepository.Setup(x => x.GetByReferenceIdAsync(siteNameVm[0].Id)).ReturnsAsync(It.IsAny<Domain.Entities.SiteType>);
        var handler = new GetSiteNameQueryHandler(_mockSiteRepository.Object, _mockmapper.Object, _mockSiteTypeRepository.Object);

        var result = await handler.Handle(new GetSiteNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        var sitenames = new List<Domain.Entities.Site> { new Domain.Entities.Site {

        }};
        var siteNameVm = new List<SiteNameVm> { new SiteNameVm {

        }};
        _mockmapper.Setup(x => x.Map<List<SiteNameVm>>(sitenames)).Returns(siteNameVm);
        _mockSiteRepository.Setup(x => x.GetSiteNames()).ReturnsAsync(sitenames);
        _mockSiteTypeRepository.Setup(x => x.GetByReferenceIdAsync(siteNameVm[0].Id)).ReturnsAsync(It.IsAny<Domain.Entities.SiteType>);
        var handler = new GetSiteNameQueryHandler(_mockSiteRepository.Object, _mockmapper.Object, _mockSiteTypeRepository.Object);
        await handler.Handle(new GetSiteNameQuery(), CancellationToken.None);

        _mockSiteRepository.Verify(x => x.GetSiteNames(), Times.Once);
    }
}