﻿using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AlertReceiverModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertReceiver.Queries;

public class GetAlertReceiverPaginatedListQueryHandlerTests : IClassFixture<AlertReceiverFixture>
{
    private readonly GetAlertReceiverPaginatedListQueryHandler _handler;

    private readonly Mock<IAlertReceiverRepository> _mockAlertReceiverRepository;
    private readonly Mock<IPublisher> _mockPublisher = new();
    public GetAlertReceiverPaginatedListQueryHandlerTests(AlertReceiverFixture alertReceiverFixture)
    {
        var alertReceiverNewFixture = alertReceiverFixture;

        alertReceiverNewFixture.AlertReceivers[0].Name = "Alert";
        alertReceiverNewFixture.AlertReceivers[0].MobileNumber = "9638527410";
        alertReceiverNewFixture.AlertReceivers[0].EmailAddress = "<EMAIL>";
        alertReceiverNewFixture.AlertReceivers[0].Properties = "{\"Name\": \"Pending\", \"password\": \"Admin@123\"}";

        alertReceiverNewFixture.AlertReceivers[1].Name = "Notification";
        alertReceiverNewFixture.AlertReceivers[1].MobileNumber = "8529637410";
        alertReceiverNewFixture.AlertReceivers[1].EmailAddress = "<EMAIL>";
        alertReceiverNewFixture.AlertReceivers[1].Properties = "{\"Name\": \"Status\", \"password\": \"Admin@4321\"}";


        _mockAlertReceiverRepository = AlertReceiverRepositoryMocks.GetPaginatedAlertReceiverRepository(alertReceiverNewFixture.AlertReceivers);

        _handler = new GetAlertReceiverPaginatedListQueryHandler(alertReceiverNewFixture.Mapper, _mockPublisher.Object, _mockAlertReceiverRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetAlertReceiverPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Row" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertReceiverListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedAlertReceivers_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetAlertReceiverPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Alert" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertReceiverListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<AlertReceiverListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("Alert");

        result.Data[0].MobileNumber.ShouldBe("9638527410");

        result.Data[0].EmailAddress.ShouldBe("<EMAIL>");

        result.Data[0].Properties.ShouldBe("{\"Name\": \"Pending\", \"password\": \"Admin@123\"}");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetAlertReceiverPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertReceiverListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_AlertReceivers_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetAlertReceiverPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=Alert;mobilenumber=9638527410;emailaddress=<EMAIL>;properties={\"Name\": \"Pending\", \"password\": \"Admin@123\"};" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<AlertReceiverListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("Alert");

        result.Data[0].MobileNumber.ShouldBe("9638527410");

        result.Data[0].EmailAddress.ShouldBe("<EMAIL>");

        result.Data[0].Properties.ShouldBe("{\"Name\": \"Pending\", \"password\": \"Admin@123\"}");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetAlertReceiverPaginatedListQuery(), CancellationToken.None);

        _mockAlertReceiverRepository.Verify(x => x.PaginatedListAllAsync(It.IsAny<int>(),
         It.IsAny<int>(), It.IsAny<AlertReceiverFilterSpecification>(),
         It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }
}