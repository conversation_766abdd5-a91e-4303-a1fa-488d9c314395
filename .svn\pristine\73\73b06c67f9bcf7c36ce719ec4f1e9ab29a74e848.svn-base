﻿using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Core.Contracts.Persistence;

public interface IRepository<T> where T : class
{
    public bool IsParent { get; }
    public bool IsAllInfra { get; }
    Task<T> GetByReferenceIdAsync(string id);
    Task<T> GetByIdAsync(int id);
    IQueryable<T> GetByReferenceId(string id, Expression<Func<T, bool>> expression = null);
    Task<IReadOnlyList<T>> ListAllAsync();
    IQueryable<T> QueryAll(Expression<Func<T, bool>> expression = null);
    IQueryable<T> GetPaginatedQuery();
    Task<T> AddAsync(T entity);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
    Task<T> UpdateAsync(T entity);
    Task<IReadOnlyList<T>> FindByFilterAsync(Expression<Func<T, bool>> expression);
    IQueryable<T> FilterBy(Expression<Func<T, bool>> expression);
    Task<IEnumerable<T>> RemoveRangeAsync(IEnumerable<T> entities);
    Task<T> DeleteAsync(T entity);
    Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities);
    T GetBusinessServiceByReferenceId(T businessService);
    T GetBusinessFunctionByReferenceId(T businessFunction);
    T GetInfraObjectByReferenceId(T infraObject);
    Task<PaginatedResult<T>> PaginatedListAllAsync(int pageNumber, int pageSize,
        Specification<T> productFilterSpec, string sortColumn, string sortOrder);
    //Task<IEnumerable<T>> BulkInsertAsync(IEnumerable<T> entities);
    //Task<IEnumerable<T>> BulkUpdateAsync(IEnumerable<T> entities);
    //Task<IEnumerable<T>> BulkDeleteAsync(IEnumerable<T> entities);
}