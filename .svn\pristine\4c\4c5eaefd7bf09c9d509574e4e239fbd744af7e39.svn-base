using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class DriftManagementMonitorStatusFilterSpecification : Specification<DriftManagementMonitorStatus>
{
    public DriftManagementMonitorStatusFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.InfraObjectName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("infraobjectname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.InfraObjectName.Contains(stringItem.Replace("infraobjectname=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("properties=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Properties.Contains(stringItem.Replace("properties=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("conflictproperties=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ConflictProperties.Contains(stringItem.Replace("conflictproperties=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.InfraObjectName.Contains(searchString) || p.Properties.Contains(searchString) ||
                    p.ConflictProperties.Contains(searchString);
            }
        }
    }
}