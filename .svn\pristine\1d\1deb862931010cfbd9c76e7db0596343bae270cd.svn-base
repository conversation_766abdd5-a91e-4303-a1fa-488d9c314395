﻿namespace ContinuityPatrol.Application.Features.UserRole.Commands.Create;

public class CreateUserRoleCommandValidator : AbstractValidator<CreateUserRoleCommand>
{
    private readonly IUserRoleRepository _userRoleRepository;

    public CreateUserRoleCommandValidator(IUserRoleRepository userRoleRepository)
    {
        _userRoleRepository = userRoleRepository;

        RuleFor(p => p.Role)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.Logo)
            .Matches(
                @"^rgb\(\s*(1?[0-9]{1,2}|2[0-4][0-9]|25[0-5])\s*,\s*(1?[0-9]{1,2}|2[0-4][0-9]|25[0-5])\s*,\s*(1?[0-9]{1,2}|2[0-4][0-9]|25[0-5])\s*\)$")
            .WithMessage("Please enter a valid RGB color value.");

        RuleFor(p => p)
            .MustAsync(UserRoleNameUnique)
            .WithMessage("A same name already exist.");
    }

    public async Task<bool> UserRoleNameUnique(CreateUserRoleCommand p, CancellationToken cancellationToken)
    {
        return !await _userRoleRepository.IsUserRoleNameUnique(p.Role);
    }
}