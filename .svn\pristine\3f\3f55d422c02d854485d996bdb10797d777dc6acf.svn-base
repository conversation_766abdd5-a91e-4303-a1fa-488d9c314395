using ContinuityPatrol.Application.Features.BackUp.Commands.Create;
using ContinuityPatrol.Application.Features.BackUp.Commands.Delete;
using ContinuityPatrol.Application.Features.BackUp.Commands.Execute;
using ContinuityPatrol.Application.Features.BackUp.Commands.Update;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetByConfig;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetList;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BackUpModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class BackUpService : BaseService, IBackUpService
{
    public BackUpService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<BackUpListVm>> GetBackUpList()
    {
        Logger.LogDebug("Get All BackUps");

        return await Mediator.Send(new GetBackUpListQuery());
    }

    public async Task<BackUpDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BackUp Id");

        Logger.LogDebug($"Get BackUp Detail by Id '{id}'");

        return await Mediator.Send(new GetBackUpDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateBackUpCommand createBackUpCommand)
    {
        Logger.LogDebug($"Create BackUp '{createBackUpCommand}'");

        return await Mediator.Send(createBackUpCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateBackUpCommand updateBackUpCommand)
    {
        Logger.LogDebug($"Update BackUp '{updateBackUpCommand}'");

        return await Mediator.Send(updateBackUpCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BackUp Id");

        Logger.LogDebug($"Delete BackUp Details by Id '{id}'");

        return await Mediator.Send(new DeleteBackUpCommand { Id = id });
    }
    public async Task<BaseResponse> ExecuteBackUp(BackUpExecuteCommand backUpExecuteCommand)
    {
        Logger.LogDebug($"Execute BackUp '{backUpExecuteCommand}'");

        return await Mediator.Send(backUpExecuteCommand);
    }
    #region NameExist

    public async Task<bool> IsBackUpNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "BackUp Name");

        Logger.LogDebug($"Check Name Exists Detail by BackUp Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetBackUpNameUniqueQuery { Name = name, Id = id });
    }

    #endregion

    #region Paginated

    public async Task<PaginatedResult<BackUpListVm>> GetPaginatedBackUps(GetBackUpPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in BackUp Paginated List");

        return await Mediator.Send(query);
    }
    #endregion

    public async Task<GetByConfigDetailVm> GetBackUpByConfig()
    {
        Logger.LogDebug("Get BackUp Detail by Config");

        return await Mediator.Send(new GetByConfigDetailQuery());
    }
}