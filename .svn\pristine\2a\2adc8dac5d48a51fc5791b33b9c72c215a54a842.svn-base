using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class BackUpFilterSpecification : Specification<BackUp>
{
    public BackUpFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.HostName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("hostname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.HostName.Contains(stringItem.Replace("hostname=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("databasename=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.DatabaseName.Contains(stringItem.Replace("databasename=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("username=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.UserName.Contains(stringItem.Replace("username=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("password=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Password.Contains(stringItem.Replace("password=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("backuppath=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.BackUpPath.Contains(stringItem.Replace("backuppath=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("cronexpression=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.CronExpression.Contains(stringItem.Replace("cronexpression=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("properties=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Properties.Contains(stringItem.Replace("properties=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p => p.HostName.Contains(searchString) || p.DatabaseName.Contains(searchString) ||
                                p.UserName.Contains(searchString) || p.Password.Contains(searchString) ||
                                p.BackUpPath.Contains(searchString) || p.CronExpression.Contains(searchString) ||
                                p.Properties.Contains(searchString);
            }
        }
    }
}