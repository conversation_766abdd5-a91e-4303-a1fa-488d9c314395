using ContinuityPatrol.Application.Features.BiaRules.Commands.Update;
using ContinuityPatrol.Application.Features.BiaRules.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BiaRules.Commands;

public class UpdateBiaRulesTests : IClassFixture<BiaRulesFixture>
{
    private readonly BiaRulesFixture _biaRulesFixture;
    private readonly Mock<IBiaRulesRepository> _mockBiaRulesRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly UpdateBiaRulesCommandHandler _handler;

    public UpdateBiaRulesTests(BiaRulesFixture biaRulesFixture)
    {
        _biaRulesFixture = biaRulesFixture;
        _mockBiaRulesRepository = BiaRulesRepositoryMocks.CreateBiaRulesRepository(_biaRulesFixture.BiaRules);
        _mockPublisher = new Mock<IPublisher>();

        _handler = new UpdateBiaRulesCommandHandler(
            _biaRulesFixture.Mapper,
            _mockBiaRulesRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateBiaRules_When_ValidCommand()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var command = new UpdateBiaRulesCommand
        {
            Id = existingBiaRule.ReferenceId,
            Description = "Updated BIA Rule",
            Type = "RPO",
            EntityId = existingBiaRule.EntityId,
            Properties = "{\"threshold\":\"2\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-15).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(45).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RPO_UPDATED"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules updated successfully");
        result.Id.ShouldBe(existingBiaRule.ReferenceId);

        _mockBiaRulesRepository.Verify(x => x.GetByReferenceIdAsync(existingBiaRule.ReferenceId), Times.Once);
        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BiaRules>()), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<BiaRulesUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateBiaRulesDescription_When_ValidCommand()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var command = new UpdateBiaRulesCommand
        {
            Id = existingBiaRule.ReferenceId,
            Description = "Updated Description for BIA Rule",
            Type = existingBiaRule.Type,
            EntityId = existingBiaRule.EntityId,
            Properties = existingBiaRule.Properties,
            EffectiveDateFrom = existingBiaRule.EffectiveDateFrom,
            EffectiveDateTo = existingBiaRule.EffectiveDateTo,
            IsEffective = existingBiaRule.IsEffective,
            RuleCode = existingBiaRule.RuleCode
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules updated successfully");
        result.Id.ShouldBe(existingBiaRule.ReferenceId);

        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BiaRules>(b => 
            b.Description == "Updated Description for BIA Rule")), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateBiaRulesType_When_ValidCommand()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var command = new UpdateBiaRulesCommand
        {
            Id = existingBiaRule.ReferenceId,
            Description = existingBiaRule.Description,
            Type = "RPO", // Changed from RTO to RPO
            EntityId = existingBiaRule.EntityId,
            Properties = "{\"threshold\":\"1\",\"unit\":\"hours\",\"backup_type\":\"incremental\"}",
            EffectiveDateFrom = existingBiaRule.EffectiveDateFrom,
            EffectiveDateTo = existingBiaRule.EffectiveDateTo,
            IsEffective = existingBiaRule.IsEffective,
            RuleCode = "BIA_RPO_CONVERTED"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules updated successfully");

        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BiaRules>(b => 
            b.Type == "RPO" && 
            b.RuleCode == "BIA_RPO_CONVERTED")), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateBiaRulesProperties_When_ValidCommand()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var newProperties = "{\"threshold\":\"30\",\"unit\":\"minutes\",\"priority\":\"critical\",\"escalation_enabled\":true}";
        var command = new UpdateBiaRulesCommand
        {
            Id = existingBiaRule.ReferenceId,
            Description = existingBiaRule.Description,
            Type = existingBiaRule.Type,
            EntityId = existingBiaRule.EntityId,
            Properties = newProperties,
            EffectiveDateFrom = existingBiaRule.EffectiveDateFrom,
            EffectiveDateTo = existingBiaRule.EffectiveDateTo,
            IsEffective = existingBiaRule.IsEffective,
            RuleCode = existingBiaRule.RuleCode
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules updated successfully");

        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BiaRules>(b => 
            b.Properties == newProperties)), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateBiaRulesEffectiveDates_When_ValidCommand()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var newFromDate = DateTime.Now.AddDays(10).ToString("yyyy-MM-dd");
        var newToDate = DateTime.Now.AddDays(100).ToString("yyyy-MM-dd");
        var command = new UpdateBiaRulesCommand
        {
            Id = existingBiaRule.ReferenceId,
            Description = existingBiaRule.Description,
            Type = existingBiaRule.Type,
            EntityId = existingBiaRule.EntityId,
            Properties = existingBiaRule.Properties,
            EffectiveDateFrom = newFromDate,
            EffectiveDateTo = newToDate,
            IsEffective = existingBiaRule.IsEffective,
            RuleCode = existingBiaRule.RuleCode
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules updated successfully");

        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BiaRules>(b => 
            b.EffectiveDateFrom == newFromDate && 
            b.EffectiveDateTo == newToDate)), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateBiaRulesEffectiveStatus_When_ValidCommand()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var command = new UpdateBiaRulesCommand
        {
            Id = existingBiaRule.ReferenceId,
            Description = existingBiaRule.Description,
            Type = existingBiaRule.Type,
            EntityId = existingBiaRule.EntityId,
            Properties = existingBiaRule.Properties,
            EffectiveDateFrom = existingBiaRule.EffectiveDateFrom,
            EffectiveDateTo = existingBiaRule.EffectiveDateTo,
            IsEffective = false, // Changed from true to false
            RuleCode = existingBiaRule.RuleCode
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Message.ShouldBe("BIA Rules updated successfully");

        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BiaRules>(b => 
            b.IsEffective == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BiaRuleNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new UpdateBiaRulesCommand
        {
            Id = nonExistentId,
            Description = "Non-existent BIA Rule",
            Type = "RTO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"4\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(30).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_NOT_FOUND"
        };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));

        _mockBiaRulesRepository.Verify(x => x.GetByReferenceIdAsync(nonExistentId), Times.Once);
        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BiaRules>()), Times.Never);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<BiaRulesUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_PublishEvent_When_BiaRulesUpdated()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var command = new UpdateBiaRulesCommand
        {
            Id = existingBiaRule.ReferenceId,
            Description = "Event Test Rule",
            Type = "RTO",
            EntityId = existingBiaRule.EntityId,
            Properties = existingBiaRule.Properties,
            EffectiveDateFrom = existingBiaRule.EffectiveDateFrom,
            EffectiveDateTo = existingBiaRule.EffectiveDateTo,
            IsEffective = existingBiaRule.IsEffective,
            RuleCode = existingBiaRule.RuleCode
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(
            It.Is<BiaRulesUpdatedEvent>(e => e.Name == command.Type), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_MapCorrectly_When_ValidCommand()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var command = new UpdateBiaRulesCommand
        {
            Id = existingBiaRule.ReferenceId,
            Description = "Mapping Test Update",
            Type = "RPO",
            EntityId = "updated-entity-456",
            Properties = "{\"updated\":\"properties\"}",
            EffectiveDateFrom = "2024-06-01",
            EffectiveDateTo = "2024-12-31",
            IsEffective = false,
            RuleCode = "BIA_UPDATE_MAP"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBiaRulesRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BiaRules>(b =>
            b.Description == command.Description &&
            b.Type == command.Type &&
            b.EntityId == command.EntityId &&
            b.Properties == command.Properties &&
            b.EffectiveDateFrom == command.EffectiveDateFrom &&
            b.EffectiveDateTo == command.EffectiveDateTo &&
            b.IsEffective == command.IsEffective &&
            b.RuleCode == command.RuleCode)), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponse_When_BiaRulesUpdated()
    {
        // Arrange
        var existingBiaRule = _biaRulesFixture.BiaRules.First();
        var command = new UpdateBiaRulesCommand
        {
            Id = existingBiaRule.ReferenceId,
            Description = "Response Test Update",
            Type = "RTO",
            EntityId = existingBiaRule.EntityId,
            Properties = existingBiaRule.Properties,
            EffectiveDateFrom = existingBiaRule.EffectiveDateFrom,
            EffectiveDateTo = existingBiaRule.EffectiveDateTo,
            IsEffective = existingBiaRule.IsEffective,
            RuleCode = existingBiaRule.RuleCode
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<UpdateBiaRulesResponse>();
        result.Message.ShouldBe("BIA Rules updated successfully");
        result.Id.ShouldBe(existingBiaRule.ReferenceId);
        Guid.TryParse(result.Id, out _).ShouldBeTrue();
    }
}
