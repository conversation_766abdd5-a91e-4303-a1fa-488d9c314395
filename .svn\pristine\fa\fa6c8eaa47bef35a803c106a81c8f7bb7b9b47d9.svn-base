using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public static class BackUpRepositoryMocks
{
    public static Mock<IBackUpRepository> CreateBackUpRepository(List<BackUp> backUps)
    {
        var mockBackUpRepository = new Mock<IBackUpRepository>();

        mockBackUpRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(backUps);

        mockBackUpRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => backUps.FirstOrDefault(x => x.ReferenceId == id));

        mockBackUpRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string name, string id) => 
                backUps.Any(x => x.HostName == name && x.ReferenceId != id && x.IsActive));

        mockBackUpRepository.Setup(repo => repo.AddAsync(It.IsAny<BackUp>()))
            .ReturnsAsync((BackUp backUp) =>
            {
                backUp.ReferenceId = Guid.NewGuid().ToString();
                backUp.Id = backUps.Count + 1;
                backUps.Add(backUp);
                return backUp;
            });

        mockBackUpRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BackUp>()))
            .Returns((BackUp backUp) =>
            {
                var existingBackUp = backUps.FirstOrDefault(x => x.ReferenceId == backUp.ReferenceId);
                if (existingBackUp != null)
                {
                    existingBackUp.HostName = backUp.HostName;
                    existingBackUp.DatabaseName = backUp.DatabaseName;
                    existingBackUp.UserName = backUp.UserName;
                    existingBackUp.Password = backUp.Password;
                    existingBackUp.IsLocalServer = backUp.IsLocalServer;
                    existingBackUp.IsBackUpServer = backUp.IsBackUpServer;
                    existingBackUp.BackUpPath = backUp.BackUpPath;
                    existingBackUp.BackUpType = backUp.BackUpType;
                    existingBackUp.CronExpression = backUp.CronExpression;
                    existingBackUp.ScheduleType = backUp.ScheduleType;
                    existingBackUp.ScheduleTime = backUp.ScheduleTime;
                    existingBackUp.Properties = backUp.Properties;
                    existingBackUp.KeepBackUpLast = backUp.KeepBackUpLast;
                    existingBackUp.NodeId = backUp.NodeId;
                    existingBackUp.NodeName = backUp.NodeName;
                    existingBackUp.IsActive = backUp.IsActive;
                }
                return Task.CompletedTask;
            });

        mockBackUpRepository.Setup(repo => repo.DeleteAsync(It.IsAny<BackUp>()))
            .Returns((BackUp backUp) =>
            {
                backUps.Remove(backUp);
                return Task.CompletedTask;
            });

        //mockBackUpRepository.Setup(repo => repo.PaginatedListAllAsync(
        //    It.IsAny<int>(), It.IsAny<int>(), It.IsAny<ISpecification<BackUp>>(), 
        //    It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync((int pageNumber, int pageSize, ISpecification<BackUp> spec, string sortColumn, string sortOrder) =>
        //    {
        //        var filteredBackUps = backUps.Where(x => x.IsActive).ToList();
                
        //        if (spec != null)
        //        {
        //            // Apply specification filter if needed
        //            filteredBackUps = filteredBackUps.Where(spec.Criteria.Compile()).ToList();
        //        }

        //        var totalCount = filteredBackUps.Count;
        //        var pagedBackUps = filteredBackUps
        //            .Skip((pageNumber - 1) * pageSize)
        //            .Take(pageSize)
        //            .ToList();

        //        return new PaginatedResult<BackUp>
        //        {
        //            Data = pagedBackUps,
        //            TotalCount = totalCount,
        //            PageNumber = pageNumber,
        //            PageSize = pageSize,
        //            TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
        //        };
        //    });

        return mockBackUpRepository;
    }

    public static Mock<IBackUpLogRepository> CreateBackUpLogRepository(List<BackUpLog> backUpLogs)
    {
        var mockBackUpLogRepository = new Mock<IBackUpLogRepository>();

        mockBackUpLogRepository.Setup(repo => repo.AddAsync(It.IsAny<BackUpLog>()))
            .ReturnsAsync((BackUpLog backUpLog) =>
            {
                backUpLog.ReferenceId = Guid.NewGuid().ToString();
                backUpLog.Id = backUpLogs.Count + 1;
                backUpLogs.Add(backUpLog);
                return backUpLog;
            });

        mockBackUpLogRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(backUpLogs);

        return mockBackUpLogRepository;
    }

    public static Mock<IUserActivityRepository> CreateUserActivityRepository(List<UserActivity> userActivities)
    {
        var mockUserActivityRepository = new Mock<IUserActivityRepository>();

        mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
            .ReturnsAsync((UserActivity userActivity) =>
            {
                userActivity.Id = userActivities.Count + 1;
                userActivity.ReferenceId = Guid.NewGuid().ToString();
                userActivities.Add(userActivity);
                return userActivity;
            });

        mockUserActivityRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(userActivities);

        return mockUserActivityRepository;
    }

    public static Mock<ILoadBalancerRepository> CreateLoadBalancerRepository()
    {
        var mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();

        mockLoadBalancerRepository.Setup(repo => repo.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new LoadBalancer
            {
                ReferenceId = Guid.NewGuid().ToString(),
                TypeCategory = "LoadBalancer",
                Type = "ALL",
                IsActive = true
            });

        return mockLoadBalancerRepository;
    }

    public static Mock<IJobScheduler> CreateJobScheduler()
    {
        var mockJobScheduler = new Mock<IJobScheduler>();

        mockJobScheduler.Setup(scheduler => scheduler.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
            .Returns(Task.CompletedTask);

        return mockJobScheduler;
    }

    public static Mock<IWindowsService> CreateWindowsService()
    {
        var mockWindowsService = new Mock<IWindowsService>();

        //mockWindowsService.Setup(service => service.GetBaseUrl())
        //    .Returns("http://localhost:8080");

        return mockWindowsService;
    }
}
