﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FormHistory.Events.Delete;

public class FormHistoryDeletedEventHandler : INotificationHandler<FormHistoryDeletedEvent>
{
    private readonly ILogger<FormHistoryDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FormHistoryDeletedEventHandler(ILoggedInUserService userService,
        ILogger<FormHistoryDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(FormHistoryDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.FormHistory}",
            Entity = Modules.FormHistory.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"FormHistory '{deletedEvent.FormName}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation($"FormHistory '{deletedEvent.FormName}' deleted successfully.");
    }
}