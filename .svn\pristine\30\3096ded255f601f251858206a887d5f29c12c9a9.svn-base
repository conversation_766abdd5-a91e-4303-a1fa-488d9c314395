using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Events.Create;

namespace ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Create;

public class CreateDynamicDashboardWidgetCommandHandler : IRequestHandler<CreateDynamicDashboardWidgetCommand,
    CreateDynamicDashboardWidgetResponse>
{
    private readonly IDynamicDashboardWidgetRepository _dynamicDashboardWidgetRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateDynamicDashboardWidgetCommandHandler(IMapper mapper,
        IDynamicDashboardWidgetRepository dynamicDashboardWidgetRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _dynamicDashboardWidgetRepository = dynamicDashboardWidgetRepository;
    }

    public async Task<CreateDynamicDashboardWidgetResponse> Handle(CreateDynamicDashboardWidgetCommand request,
        CancellationToken cancellationToken)
    {
        var dynamicDashboardWidget = _mapper.Map<Domain.Entities.DynamicDashboardWidget>(request);

        dynamicDashboardWidget = await _dynamicDashboardWidgetRepository.AddAsync(dynamicDashboardWidget);

        var response = new CreateDynamicDashboardWidgetResponse
        {
            Message = Message.Create(nameof(Domain.Entities.DynamicDashboardWidget), dynamicDashboardWidget.Name),

            Id = dynamicDashboardWidget.ReferenceId
        };

        await _publisher.Publish(new DynamicDashboardWidgetCreatedEvent { Name = dynamicDashboardWidget.Name },
            cancellationToken);

        return response;
    }
}