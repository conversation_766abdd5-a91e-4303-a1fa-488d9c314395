﻿namespace ContinuityPatrol.Application.Features.SVCGMMonitoringStatus.Queries.GetByType;

public class SVCGMMonitorStatusDetailByTypeVm
{
    public string Id { get; set; }
    public string Type { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string ConfiguredRPO { get; set; }
    public string DataLagValue { get; set; }
    public string Properties { get; set; }
}