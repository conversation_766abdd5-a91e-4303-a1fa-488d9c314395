using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportActionResult.Queries;

public class GetBulkImportActionResultListQueryTests : IClassFixture<BulkImportActionResultFixture>
{
    private readonly BulkImportActionResultFixture _bulkImportActionResultFixture;
    private readonly Mock<IBulkImportActionResultRepository> _mockBulkImportActionResultRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetBulkImportActionResultListQueryHandler _handler;

    public GetBulkImportActionResultListQueryTests(BulkImportActionResultFixture bulkImportActionResultFixture)
    {
        _bulkImportActionResultFixture = bulkImportActionResultFixture;

        _mockBulkImportActionResultRepository = BulkImportActionResultRepositoryMocks.CreateQueryBulkImportActionResultRepository(_bulkImportActionResultFixture.BulkImportActionResults);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<List<BulkImportActionResultListVm>>(It.IsAny<List<Domain.Entities.BulkImportActionResult>>()))
            .Returns((List<Domain.Entities.BulkImportActionResult> entities) => entities.Select(entity => new BulkImportActionResultListVm
            {
                Id = entity.ReferenceId,
                CompanyId = entity.CompanyId,
                NodeId = entity.NodeId,
                NodeName = entity.NodeName,
                BulkImportOperationId = entity.BulkImportOperationId,
                BulkImportOperationGroupId = entity.BulkImportOperationGroupId,
                EntityId = entity.EntityId,
                EntityName = entity.EntityName,
                EntityType = entity.EntityType,
                Status = entity.Status,
                StartTime = entity.StartTime,
                EndTime = entity.EndTime,
                ErrorMessage = entity.ErrorMessage
            }).ToList());

        _handler = new GetBulkImportActionResultListQueryHandler(
            _mockMapper.Object,
            _mockBulkImportActionResultRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_BulkImportActionResultListVm_When_BulkImportActionResultsExist()
    {
        // Arrange
        var query = new GetBulkImportActionResultListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportActionResultListVm>));
        result.Count.ShouldBeGreaterThan(0);
        result.First().Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_Call_ListAllAsync_OnlyOnce()
    {
        // Arrange
        var query = new GetBulkImportActionResultListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var query = new GetBulkImportActionResultListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<List<BulkImportActionResultListVm>>(It.IsAny<List<Domain.Entities.BulkImportActionResult>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoBulkImportActionResultsExist()
    {
        // Arrange
        var query = new GetBulkImportActionResultListQuery();
        _mockBulkImportActionResultRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.BulkImportActionResult>());

        _mockMapper.Setup(m => m.Map<List<BulkImportActionResultListVm>>(It.IsAny<List<Domain.Entities.BulkImportActionResult>>()))
            .Returns(new List<BulkImportActionResultListVm>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportActionResultListVm>));
        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_MapEntitiesToViewModels_WithCorrectProperties()
    {
        // Arrange
        var testResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        testResult.CompanyId = "TestCompanyId";
        testResult.NodeId = "Node001";
        testResult.NodeName = "TestNode";
        testResult.BulkImportOperationId = "TestOperationId";
        testResult.BulkImportOperationGroupId = "TestGroupId";
        testResult.EntityId = "TestEntityId";
        testResult.EntityName = "TestEntity";
        testResult.EntityType = "Server";
        testResult.Status = "Pending";
        testResult.ErrorMessage = "";
        testResult.ConditionalOperation = 1;

        var query = new GetBulkImportActionResultListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBeGreaterThan(0);
        
        var firstItem = result.First();
        firstItem.Id.ShouldBe(testResult.ReferenceId);
        firstItem.CompanyId.ShouldBe("TestCompanyId");
        firstItem.NodeId.ShouldBe("Node001");
        firstItem.NodeName.ShouldBe("TestNode");
        firstItem.BulkImportOperationId.ShouldBe("TestOperationId");
        firstItem.BulkImportOperationGroupId.ShouldBe("TestGroupId");
        firstItem.EntityId.ShouldBe("TestEntityId");
        firstItem.EntityName.ShouldBe("TestEntity");
        firstItem.EntityType.ShouldBe("Server");
        firstItem.Status.ShouldBe("Pending");
        firstItem.ErrorMessage.ShouldBe("");
    }

    [Fact]
    public async Task Handle_ReturnCorrectListType_When_MappingSuccessful()
    {
        // Arrange
        var query = new GetBulkImportActionResultListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<List<BulkImportActionResultListVm>>();
        result.GetType().ShouldBe(typeof(List<BulkImportActionResultListVm>));
    }

    [Fact]
    public async Task Handle_ReturnAllActiveItems_When_RepositoryHasData()
    {
        // Arrange
        var query = new GetBulkImportActionResultListQuery();
        var expectedCount = _bulkImportActionResultFixture.BulkImportActionResults.Count;

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(expectedCount);
    }

    [Fact]
    public async Task Handle_NotCallMapper_When_NoDataExists()
    {
        // Arrange
        var query = new GetBulkImportActionResultListQuery();
        _mockBulkImportActionResultRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.BulkImportActionResult>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(0);
        _mockMapper.Verify(x => x.Map<List<BulkImportActionResultListVm>>(It.IsAny<List<Domain.Entities.BulkImportActionResult>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_MapStatusAndTimes_WithCorrectValues()
    {
        // Arrange
        var testResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        testResult.Status = "Running";
        testResult.StartTime = DateTime.Now.AddHours(-2);
        testResult.EndTime = DateTime.Now;

        var query = new GetBulkImportActionResultListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        var firstItem = result.First();
        firstItem.Status.ShouldBe("Running");
        firstItem.StartTime.ShouldBe(testResult.StartTime);
        firstItem.EndTime.ShouldBe(testResult.EndTime);
    }

    [Fact]
    public async Task Handle_MapEntityAndNodeInfo_WithCorrectValues()
    {
        // Arrange
        var testResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        testResult.EntityType = "Database";
        testResult.EntityName = "ProductionDB";
        testResult.NodeId = "Node123";
        testResult.NodeName = "ProductionNode";

        var query = new GetBulkImportActionResultListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        var firstItem = result.First();
        firstItem.EntityType.ShouldBe("Database");
        firstItem.EntityName.ShouldBe("ProductionDB");
        firstItem.NodeId.ShouldBe("Node123");
        firstItem.NodeName.ShouldBe("ProductionNode");
    }

    [Fact]
    public async Task Handle_ReturnEmptyListDirectly_When_CountIsZero()
    {
        // Arrange
        var query = new GetBulkImportActionResultListQuery();
        _mockBulkImportActionResultRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.BulkImportActionResult>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportActionResultListVm>));
        result.Count.ShouldBe(0);
        result.ShouldBeEmpty();
    }

    [Fact]
    public async Task Handle_HandleMultipleResults_When_RepositoryHasMultipleItems()
    {
        // Arrange
        var query = new GetBulkImportActionResultListQuery();
        var multipleResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult { ReferenceId = "1", EntityName = "Entity1", Status = "Pending" },
            new Domain.Entities.BulkImportActionResult { ReferenceId = "2", EntityName = "Entity2", Status = "Running" },
            new Domain.Entities.BulkImportActionResult { ReferenceId = "3", EntityName = "Entity3", Status = "Completed" }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(multipleResults);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(3);
        result.Select(x => x.Status).ShouldContain("Pending");
        result.Select(x => x.Status).ShouldContain("Running");
        result.Select(x => x.Status).ShouldContain("Completed");
    }
}
