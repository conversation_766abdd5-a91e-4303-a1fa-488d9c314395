﻿namespace ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetBusinessFunctionAvailability;

public record BusinessFunctionAvailabilityVm
{
    public int BFConfiguredCount { get; set; }
    public int BFAvailableCount { get; set; }
    public int BFNotAvailableCount { get; set; }
    public List<FunctionAvailability> FunctionAvailability { get; set; } = new();
}

public class FunctionAvailability
{
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public bool IsAffected { get; set; }
    public FunctionRemark FunctionRemark { get; set; } = new();
}

public class FunctionRemark
{
    public int ConfiguredInfraObjectCount { get; set; }
    public int AffectedInfraObjectCount { get; set; }
    public List<BFHeatmapStatusDto> BFHeatmapStatusesDto { get; set; } = new();
}

public class BFHeatmapStatusDto
{
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string EntityId { get; set; }
    public string HeatmapType { get; set; }
    public string HeatmapStatus { get; set; }
    public string ErrorMessage { get; set; }
}