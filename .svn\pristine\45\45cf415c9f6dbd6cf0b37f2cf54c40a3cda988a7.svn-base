﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowRunningActionRepository : BaseRepository<WorkflowRunningAction>, IWorkflowRunningActionRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public WorkflowRunningActionRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<WorkflowRunningAction>> GetWorkflowRunningActionsByOperationGroupId(string groupId)
    {
        return await base.FilterBy(x=>x.WorkflowOperationGroupId == groupId)
            .Select(w=> new WorkflowRunningAction
            {
                Id = w.Id,
                WorkflowId = w.WorkflowId,
                WorkflowOperationGroupId = w.WorkflowOperationGroupId,
                StepId = w.StepId,
                WorkflowActionName = w.WorkflowActionName,
                ActionId = w.ActionId,
                StartTime = w.StartTime,
                EndTime = w.EndTime,
                Status = w.Status,
                Message = w.Message,
                Icon = w.Icon,
                IsParallel = w.IsParallel,
                IsGroup = w.IsGroup,
                IsCustom = w.IsCustom,
                GroupName = w.GroupName,
                GroupId = w.GroupId,
				 Type = w.Type,
                 Properties = w.Properties
            }).OrderBy(x=>x.Id).ToListAsync();
    }

    public async Task<List<WorkflowRunningAction>> GetWorkflowRunningActionsByWorkflowId(string workflowId)
    {
        return await base.FilterBy(x => x.WorkflowId == workflowId).Select(w => new WorkflowRunningAction
        {
            Id = w.Id,
            WorkflowId = w.WorkflowId,
            WorkflowOperationGroupId = w.WorkflowOperationGroupId,
            StepId = w.StepId,
            WorkflowActionName = w.WorkflowActionName,
            ActionId = w.ActionId,
            StartTime = w.StartTime,
            EndTime = w.EndTime,
            Status = w.Status,
            Message = w.Message,
            Icon = w.Icon,
            IsParallel = w.IsParallel,
            IsGroup = w.IsGroup,
            IsCustom = w.IsCustom,
            GroupName = w.GroupName,
            GroupId = w.GroupId,
			 Type = w.Type,
            Properties = w.Properties
        }).ToListAsync();
    }

    public async Task DeleteWorkflowRunningActionsByGroupIds(List<string> groupIds)
    {
       
        var workflowActions = await base
            .FilterBy(x => groupIds.Contains(x.WorkflowOperationGroupId)) 
            .OrderBy(x => x.Id) 
            .ToListAsync();

        // Delete all retrieved actions
        if (workflowActions.Any())
        {
            await base.RemoveRangeAsync(workflowActions);
        }
    }
}