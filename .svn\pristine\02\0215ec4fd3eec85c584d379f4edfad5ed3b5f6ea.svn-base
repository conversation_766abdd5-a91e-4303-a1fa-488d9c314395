﻿@using ContinuityPatrol.Domain.Entities
@model ContinuityPatrol.Domain.ViewModels.RoboCopyModel.RoboCopyViewModel
@Html.AntiForgeryToken()
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-robocopy"></i><span>RoboCopy Options</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="name">
                                        <label class="form-check-label" for="name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="replicationtype=" id="ReplicationType">
                                        <label class="form-check-label" for="ReplicationType">
                                            Replication&nbsp;Type
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" id="robo_create" data-bs-target="#CreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="pt-1 card-body" style="height: calc(100vh - 141px);">
            <div>
                <table class="datatable table table-hover dataTable no-footer" style="width:100%" id="tblRoboCopy">
                    <thead>
                        <tr>
                            <th>Sr No</th>
                            <th>Name</th>
                            <th>Replication Type</th>
                            <th>Copy Options</th>
                            @* <th>Filters</th> *@
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div id="configurationroboCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
        <!-- Your Configuration content here -->
    </div>
    <div id="configurationroboDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
        <!-- Your Configuration content here -->
    </div>
    <!--Modal Create-->
    <div class="modal fade" id="CreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="configureModalLabel" aria-hidden="true">
        <partial name="Configuration" />
    </div>
    <!--Modal Delete-->
    <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <partial name="Delete" />
    </div>
</div>
@section Scripts
{
    <partial name="_ValidationScriptsPartial" />
}
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Configuration/Infra Components/RoboCopyOptions/Robocopyoptions.js"></script>
