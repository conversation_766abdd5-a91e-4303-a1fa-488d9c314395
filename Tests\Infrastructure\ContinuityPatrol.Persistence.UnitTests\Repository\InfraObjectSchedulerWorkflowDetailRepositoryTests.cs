﻿using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class InfraObjectSchedulerWorkflowDetailRepositoryTests
    {
        private readonly ApplicationDbContext _dbContext;

        public InfraObjectSchedulerWorkflowDetailRepositoryTests()
        {
            _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        }
        public void Dispose()
        {
            _dbContext?.Dispose();
        }
        [Fact]
        public void Constructor_ShouldInitializeRepository()
        {
            // Arrange

            // Act
            var repository = new InfraObjectSchedulerWorkflowDetailRepository(_dbContext, DbContextFactory.GetMockUserService());

            // Assert
            Assert.NotNull(repository);
        }
    }
}
