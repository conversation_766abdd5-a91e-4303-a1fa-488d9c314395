﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowCategory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowCategory.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowCategory.Queries.GetWorkflowCategoryViewList;
using ContinuityPatrol.Domain.ViewModels.WorkflowCategoryModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class WorkflowCategoryService :BaseClient, IWorkflowCategoryService
{
    public WorkflowCategoryService(IConfiguration config, IAppCache cache, ILogger<WorkflowActionService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<WorkflowCategoryNameVm>> GetWorkflowCategoryNames()
    {
        var request = new RestRequest("api/v6/workflowcategories/names");

        return await GetFromCache<List<WorkflowCategoryNameVm>>(request, "GetWorkflowCategoryNames");
    }

    public async Task<List<WorkflowCategoryListVm>> GetWorkflowCategoryList()
    {
        var request = new RestRequest("api/v6/workflowcategories");

        return await Get<List<WorkflowCategoryListVm>>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var request = new RestRequest("api/v6/workflowcategories", Method.Post);

        request.AddJsonBody(createWorkflowCategoryCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowCategoryCommand updateWorkflowCategoryCommand)
    {
        var request = new RestRequest("api/v6/workflowcategories", Method.Put);

        request.AddJsonBody(updateWorkflowCategoryCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string workflowCategoryId)
    {
        var request = new RestRequest($"api/v6/workflowcategories/{workflowCategoryId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<WorkflowCategoryDetailVm> GetByReferenceId(string workflowCategoryId)
    {
        var request = new RestRequest($"api/v6/workflowcategories/{workflowCategoryId}");

        return await Get<WorkflowCategoryDetailVm>(request);
    }

    public async Task<bool> IsWorkflowCategoryNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/workflowcategories/name-exist?name={name}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<PaginatedResult<WorkflowCategoryListVm>> GetPaginatedWorkflowCategories(GetWorkflowCategoryPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/workflowcategories/paginated-list");

        return await Get<PaginatedResult<WorkflowCategoryListVm>>(request);
    }

    public async Task<List<WorkflowCategoryViewListVm>> GetWorkflowCategoryViewList()
    {
        var request = new RestRequest("api/v6/workflowcategories/workflowcategoryviewlist");

        return await Get<List<WorkflowCategoryViewListVm>>(request);
    }
}