﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface ILoadBalancerRepository : IRepository<LoadBalancer>
{
    Task<bool> IsNodeConfigurationNameExist(string name, string id);
    Task<bool> IsNodeConfigurationNameUnique(string name);
    Task<List<LoadBalancer>> GetNodeConfigurationListById(List<string> ids);

    Task<LoadBalancer> GetNodeConfigurationByTypeAndTypeCategory(string type, string typeCategory);

    Task<List<LoadBalancer>> GetLoadBalancerType(string type);

    Task<(List<string> ActiveNodes, List<string> InActiveNodes)> GetActiveNodeAndInActiveNodeByType(string type);
    Task<bool> IsNodeConfigurationIpAddressAndPortExist(string ipAddress, int port, string id);
    Task<List<LoadBalancer>> GetNodeNameByIdAsync(List<string> id);
    Task<bool> IsNodeInUse(string id);
    Task<List<LoadBalancer>> GetLoadBalancerByType(string id, string type);
    Task<List<LoadBalancer>> GetNamesByType(string type);

}