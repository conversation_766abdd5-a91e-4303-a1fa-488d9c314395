﻿using ContinuityPatrol.Application.Features.RoboCopyJob.Events.Create;

namespace ContinuityPatrol.Application.Features.RoboCopyJob.Commands.Create;

public class CreateRoboCopyJobCommandHandler : IRequestHandler<CreateRoboCopyJobCommand, CreateRoboCopyJobResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IRoboCopyJobRepository _roboCopyJobRepository;

    public CreateRoboCopyJobCommandHandler(IRoboCopyJobRepository roboCopyJobRepository, IMapper mapper,
        IPublisher publisher)
    {
        _roboCopyJobRepository = roboCopyJobRepository;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<CreateRoboCopyJobResponse> Handle(CreateRoboCopyJobCommand request,
        CancellationToken cancellationToken)
    {
        var roboCopyJob = _mapper.Map<Domain.Entities.RoboCopyJob>(request);

        await _roboCopyJobRepository.AddAsync(roboCopyJob);


        var response = new CreateRoboCopyJobResponse
        {
            Message = Message.Create(nameof(Domain.Entities.RoboCopyJob), roboCopyJob.ReplicationName),

            Id = roboCopyJob.ReferenceId
        };

        await _publisher.Publish(new RoboCopyJobCreatedEvent { ReplicationName = roboCopyJob.ReplicationName },
            cancellationToken);

        return response;
    }
}