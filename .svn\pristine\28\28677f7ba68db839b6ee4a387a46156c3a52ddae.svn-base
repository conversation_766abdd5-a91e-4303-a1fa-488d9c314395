﻿using ContinuityPatrol.Application.Features.SolutionHistory.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SolutionHistory.Commands;

public class UpdateSolutionHistoryTests : IClassFixture<SolutionHistoryFixture>
{
    private readonly SolutionHistoryFixture _solutionHistoryFixture;

    private readonly Mock<ISolutionHistoryRepository> _mockSolutionHistoryRepository;

    private readonly UpdateSolutionHistoryCommandHandler _handler;

    public UpdateSolutionHistoryTests(SolutionHistoryFixture solutionHistoryFixture)
    {
        _solutionHistoryFixture = solutionHistoryFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockSolutionHistoryRepository = SolutionHistoryRepositoryMocks.UpdateSolutionHistoryRepository(_solutionHistoryFixture.SolutionHistories);

        _handler = new UpdateSolutionHistoryCommandHandler(_solutionHistoryFixture.Mapper, _mockSolutionHistoryRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidSolutionHistory_UpdateReferenceIdAsync_ToSolutionHistoriesRepo()
    {
        _solutionHistoryFixture.UpdateSolutionHistoryCommand.Id = _solutionHistoryFixture.SolutionHistories[0].ReferenceId;

        var result = await _handler.Handle(_solutionHistoryFixture.UpdateSolutionHistoryCommand, CancellationToken.None);

        var solutionHistory = await _mockSolutionHistoryRepository.Object.GetByReferenceIdAsync(result.SolutionHistoryId);

        Assert.Equal(_solutionHistoryFixture.UpdateSolutionHistoryCommand.LoginName, solutionHistory.LoginName);
    }

    [Fact]
    public async Task Handle_Return_ValidSolutionHistoryResponse_WhenUpdate_SolutionHistory()
    {
        _solutionHistoryFixture.UpdateSolutionHistoryCommand.Id = _solutionHistoryFixture.SolutionHistories[0].ReferenceId;

        var result = await _handler.Handle(_solutionHistoryFixture.UpdateSolutionHistoryCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateSolutionHistoryResponse));

        result.SolutionHistoryId.ShouldBeGreaterThan(0.ToString());

        result.SolutionHistoryId.ShouldBe(_solutionHistoryFixture.UpdateSolutionHistoryCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidSolutionHistoryId()
    {
        _solutionHistoryFixture.UpdateSolutionHistoryCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_solutionHistoryFixture.UpdateSolutionHistoryCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        _solutionHistoryFixture.UpdateSolutionHistoryCommand.Id = _solutionHistoryFixture.SolutionHistories[0].ReferenceId;

        await _handler.Handle(_solutionHistoryFixture.UpdateSolutionHistoryCommand, CancellationToken.None);

        _mockSolutionHistoryRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockSolutionHistoryRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.SolutionHistory>()), Times.Once);
    }
}