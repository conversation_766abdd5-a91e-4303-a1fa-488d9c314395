using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class BackUpRepository : BaseRepository<BackUp>, IBackUpRepository
{
    private readonly ApplicationDbContext _dbContext;

    public BackUpRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
    }

    public async Task<bool> IsNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
        {
            return await Entities.AsNoTracking().AnyAsync(e => e.HostName == name);
        }

        var matchedEntities = await Entities.AsNoTracking()
            .Where(e => e.HostName == name)
            .ToListAsync();

        return matchedEntities.Unique(id);
    }
}