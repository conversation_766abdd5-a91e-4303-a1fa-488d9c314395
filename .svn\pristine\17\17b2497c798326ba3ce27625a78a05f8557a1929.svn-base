﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class MssqlAlwaysOnMonitorLogsRepositoryMocks
{
    public static Mock<IMssqlAlwaysOnMonitorLogsRepository> CreateMssqlAlwaysOnMonitorLogsRepository(List<MSSQLAlwaysOnMonitorLogs> mssqlAlwaysOnMonitorLogs)
    {
        var mssqlAlwaysOnMonitorLogsRepository = new Mock<IMssqlAlwaysOnMonitorLogsRepository>();

        mssqlAlwaysOnMonitorLogsRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(mssqlAlwaysOnMonitorLogs);

        mssqlAlwaysOnMonitorLogsRepository.Setup(repo => repo.AddAsync(It.IsAny<MSSQLAlwaysOnMonitorLogs>())).ReturnsAsync(
            (MSSQLAlwaysOnMonitorLogs mssqlAlwaysOnMonitorLog) =>
            {
                mssqlAlwaysOnMonitorLog.Id = new Fixture().Create<int>();

                mssqlAlwaysOnMonitorLog.ReferenceId = new Fixture().Create<Guid>().ToString();

                mssqlAlwaysOnMonitorLogs.Add(mssqlAlwaysOnMonitorLog);

                return mssqlAlwaysOnMonitorLog;
            });

        return mssqlAlwaysOnMonitorLogsRepository;
    }

    public static Mock<IMssqlAlwaysOnMonitorLogsRepository> GetMssqlAlwaysOnMonitorLogsRepository(List<MSSQLAlwaysOnMonitorLogs> mssqlAlwaysOnMonitorLogs)
    {
        var mssqlAlwaysOnMonitorLogsRepository = new Mock<IMssqlAlwaysOnMonitorLogsRepository>();

        mssqlAlwaysOnMonitorLogsRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(mssqlAlwaysOnMonitorLogs);

        mssqlAlwaysOnMonitorLogsRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => mssqlAlwaysOnMonitorLogs.SingleOrDefault(x => x.ReferenceId == i));

        return mssqlAlwaysOnMonitorLogsRepository;
    }

    public static Mock<IMssqlAlwaysOnMonitorLogsRepository> GetMssqlAlwaysOnMonitorLogsEmptyRepository()
    {
        var mssqlAlwaysOnMonitorLogsRepository = new Mock<IMssqlAlwaysOnMonitorLogsRepository>();

        mssqlAlwaysOnMonitorLogsRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<MSSQLAlwaysOnMonitorLogs>());

        mssqlAlwaysOnMonitorLogsRepository.Setup(repo => repo.GetDetailByType(It.IsAny<string>())).ReturnsAsync(new List<MSSQLAlwaysOnMonitorLogs>());

        return mssqlAlwaysOnMonitorLogsRepository;
    }

    public static Mock<IMssqlAlwaysOnMonitorLogsRepository> GetMssqlAlwaysOnMonitorLogsTypeRepository(List<MSSQLAlwaysOnMonitorLogs> mssqlAlwaysOnMonitorLogs)
    {
        var mssqlAlwaysOnMonitorLogsRepository = new Mock<IMssqlAlwaysOnMonitorLogsRepository>();

        mssqlAlwaysOnMonitorLogsRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(mssqlAlwaysOnMonitorLogs);

        mssqlAlwaysOnMonitorLogsRepository.Setup(repo => repo.GetDetailByType(It.IsAny<string>())).ReturnsAsync(mssqlAlwaysOnMonitorLogs);

        mssqlAlwaysOnMonitorLogsRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => mssqlAlwaysOnMonitorLogs.SingleOrDefault(x => x.ReferenceId == i));

        return mssqlAlwaysOnMonitorLogsRepository;
    }

    public static Mock<IMssqlAlwaysOnMonitorLogsRepository> GetPaginatedMssqlAlwaysOnMonitorLogsRepository(List<MSSQLAlwaysOnMonitorLogs> mssqlAlwaysOnMonitorLogs)
    {
        var mssqlAlwaysOnMonitorLogsRepository = new Mock<IMssqlAlwaysOnMonitorLogsRepository>();

        var queryableMssqlAlwaysOnMonitorLogs = mssqlAlwaysOnMonitorLogs.BuildMock();

        mssqlAlwaysOnMonitorLogsRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableMssqlAlwaysOnMonitorLogs);

        return mssqlAlwaysOnMonitorLogsRepository;
    }
}