﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Server.Events.Update;

public class ServerUpdatedEventHandler : INotificationHandler<ServerUpdatedEvent>
{
    private readonly ILogger<ServerUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ServerUpdatedEventHandler(ILoggedInUserService userService, ILogger<ServerUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ServerUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            CompanyId = _userService.CompanyId,
            Entity = Modules.Server.ToString(),
            Action = $"{ActivityType.Update} {Modules.Server}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Server '{updatedEvent.ServerName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Server '{updatedEvent.ServerName}' updated successfully.");
    }
}