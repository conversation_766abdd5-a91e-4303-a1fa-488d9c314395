﻿using ContinuityPatrol.Web.Areas.Admin.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class LicenseInfoControllerTests
    {
        private readonly LicenseInfoController _controller;

        public LicenseInfoControllerTests()
        {
            _controller = new LicenseInfoController();
        }

        [Fact]
        public void List_ShouldReturnViewResult()
        {
            var result = _controller.List();
            Assert.IsType<ViewResult>(result);
        }
    }
}

