﻿using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Domain.ViewModels.AlertReceiverModel;

public class AlertReceiverViewModal
{
    public string Id { get; set; }
    public string EmailAddress { get; set; }
    public string MobileNumber { get; set; }
    public string Name { get; set; }
    public string Properties { get; set; }
    public bool IsMail { get; set; }
    public bool IsActiveUser { get; set; }
    public bool IsSendReport { get; set; }
    public PaginatedResult<AlertReceiverListVm> PaginatedAlertReceiver { get; set; }
}