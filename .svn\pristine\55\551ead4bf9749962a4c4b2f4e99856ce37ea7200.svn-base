﻿using ContinuityPatrol.Application.Features.Server.Commands.Create;
using ContinuityPatrol.Application.Features.Server.Commands.Delete;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Server.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Server.Commands.Update;
using ContinuityPatrol.Application.Features.Server.Commands.UpdateBulkPassword;
using ContinuityPatrol.Application.Features.Server.Commands.UpdateVersion;
using ContinuityPatrol.Application.Features.Server.Queries.GetBySiteId;
using ContinuityPatrol.Application.Features.Server.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Server.Queries.GetList;
using ContinuityPatrol.Application.Features.Server.Queries.GetNames;
using ContinuityPatrol.Application.Features.Server.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Server.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Server.Queries.GetRoleType;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByIpAddress;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByLicenseKey;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByOsType;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByServerName;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByUserName;
using ContinuityPatrol.Application.Features.Server.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class ServerService : BaseService, IServerService
{
    public ServerService(IHttpContextAccessor accessor) : base(accessor)
    {
   
    }

    public async Task<BaseResponse> SaveAllServer(SaveAllServerCommand command)
    {
        Logger.LogDebug($"SaveAll Server '{command.ServerId}'");

        if(command.ServerId.IsNullOrWhiteSpace()) throw new Exception("Server Id is required");

        return await Mediator.Send(command);
    }


    public async Task<List<ServerNameVm>> GetServerNames()
    {
        Logger.LogDebug("Get All Server Names");

        return await Mediator.Send(new GetServerNameQuery());
    }

    public async Task<List<ServerListVm>> GetServerList()
    {
        Logger.LogDebug("Get All Servers ");

        return await Mediator.Send(new GetServerListQuery());
    }

    public async Task<BaseResponse> CreateAsync(CreateServerCommand createServerCommand)
    {
        Logger.LogDebug($"Create Server '{createServerCommand}'");

        return await Mediator.Send(createServerCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateServerCommand updateServerCommand)
    {
        Logger.LogDebug($"Update Server '{updateServerCommand}'");

        return await Mediator.Send(updateServerCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Server Id");

        Logger.LogDebug($"Delete Server Details by Id '{id}'");

        return await Mediator.Send(new DeleteServerCommand { Id = id });
    }

    public async Task<ServerDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Server Id");

        Logger.LogDebug($"Get Server Detail by Id '{id}'");

        return await Mediator.Send(new GetServerDetailQuery { Id = id });
    }

    public async Task<bool> IsServerNameExist(string serverName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(serverName, "Server Name");

        Logger.LogDebug($"Check Name Exists Detail by Server Name '{serverName}' and Id '{id}'");

        return await Mediator.Send(new GetServerNameUniqueQuery { ServerName = serverName, ServerId = id });
    }

    public async Task<List<ServerTypeVm>> GetByType(string serverTypeId)
    {
        Logger.LogDebug("Get Server by Type");

        return await Mediator.Send(new GetServerTypeQuery { ServerTypeId = serverTypeId });
    }

    public async Task<List<ServerByLicenseKeyVm>> GetByLicenseKey(string? licenseId)
    {
        Logger.LogDebug("Get Server by licenseKey");

        return await Mediator.Send(new GetServerByLicenseKeyQuery { LicenseId = licenseId });
    }

    public async Task<List<GetServerByOsTypeVm>> GetByServerOsType(string? osTypeId)
    {
        Logger.LogDebug("Get Servers Type ");

        return await Mediator.Send(new GetServerByOsTypeQuery { OSTypeId = osTypeId });
    }

    public async Task<ServerByServerNameVm> GetByServerName(string serverName)
    {
        Logger.LogDebug($"Get Server Detail by Id '{serverName}'");

        return await Mediator.Send(new GetServerByServerNameQuery { ServerName = serverName });
    }

    public async Task<List<ServerRoleTypeVm>> GetByRoleTypeAndServerType(string? roleTypeId, string? serverTypeId)
    {
        Logger.LogDebug($"Get Server Detail by RoleType '{roleTypeId}'");

        return await Mediator.Send(new GetServerRoleTypeQuery { RoleTypeId = roleTypeId, ServerTypeId = serverTypeId });
    }

    public async Task<BaseResponse> ServerTestConnection(ServerTestConnectionCommand command)
    {
        Logger.LogDebug($"Entered into Server Test Connection Method.");

        return await Mediator.Send(command);
    }

    public async Task<PaginatedResult<ServerViewListVm>> GetPaginatedServers(GetServerPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Server Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<BaseResponse> SaveAsServer(SaveAsServerCommand saveAsServerCommand)
    {
        Logger.LogDebug($"SaveAs Server '{saveAsServerCommand.Name}'");

        return await Mediator.Send(saveAsServerCommand);
    }

    public async Task<BaseResponse> UpdateServerPassword(UpdateBulkPasswordCommand command)
    {
        Logger.LogDebug("Update Server Access key");

        return await Mediator.Send(command);
    }

    public async Task<List<ServerByUserNameVm>> GetServerByUserName(string userName, string? osTypeId,bool substituteAuthentication)
    {
        Logger.LogDebug($"Get Servers by userName '{userName}' and osType '{osTypeId}'");

        return await Mediator.Send(new GetServerByUserNameQuery { UserName = userName, OSTypeId = osTypeId,SubstituteAuthentication=substituteAuthentication });
    }
    public async Task<BaseResponse> UpdateServerFormVersion(UpdateServerVersionCommand updateServerVersionCommand)
    {
        Logger.LogDebug($"Update Server Form Version {updateServerVersionCommand.OldFormVersion}");

        return await Mediator.Send(updateServerVersionCommand);
    }
    public async Task<List<ServerListVm>> GetServerByIpAddress(string ipAddress)
    {
        Logger.LogDebug($"Get Server Detail by IpAddress '{ipAddress}'");

        return await Mediator.Send(new GetServerByIpAddressQuery { IpAddress = ipAddress });
    }
    public async Task<List<ServerListVm>> GetServerBySiteId(string siteId)
    {
        Logger.LogDebug($"Get Server List by siteId '{siteId}'");

        return await Mediator.Send(new GetServerBySiteIdQuery { SiteId = siteId });
    }
}