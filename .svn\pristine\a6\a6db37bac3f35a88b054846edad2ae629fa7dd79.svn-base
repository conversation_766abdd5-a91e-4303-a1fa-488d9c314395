using AutoFixture;
using ContinuityPatrol.Application.Features.DataLag.Commands.Create;
using ContinuityPatrol.Application.Features.DataLag.Commands.Delete;
using ContinuityPatrol.Application.Features.DataLag.Commands.Update;
using ContinuityPatrol.Application.Features.DataLag.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.DataLag.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataLag.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.DataLagModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DataLagFixture
{
    public CreateDataLagCommand CreateDataLagCommand { get; }
    public CreateDataLagResponse CreateDataLagResponse { get; }
    public UpdateDataLagCommand UpdateDataLagCommand { get; }
    public UpdateDataLagResponse UpdateDataLagResponse { get; }
    public DeleteDataLagCommand DeleteDataLagCommand { get; }
    public DeleteDataLagResponse DeleteDataLagResponse { get; }
    public DataLagDetailVm DataLagDetailVm { get; }
    public DataLagListVm DataLagListVm { get; }
    public GetDataLagListQuery GetDataLagListQuery { get; }
    public GetDataLagDetailQuery GetDataLagDetailQuery { get; }
    public GetDataLagDetailByBusinessServiceIdQuery GetDataLagDetailByBusinessServiceIdQuery { get; }

    public DataLagFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateDataLagCommand>(c => c
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Data Lag Service")
            .With(b => b.TotalBusinessFunction, 50)
            .With(b => b.BFAvailable, 45)
            .With(b => b.BFImpact, 3)
            .With(b => b.BFExceed, 2)
            .With(b => b.BFUnConfigured, 0)
            .With(b => b.BFNotAvailable, 0)
            .With(b => b.BFThreshold, 1)
            .With(b => b.TotalInfraObject, 100)
            .With(b => b.InfraAvailable, 92)
            .With(b => b.InfraImpact, 5)
            .With(b => b.InfraExceed, 3)
            .With(b => b.InfraNotAvailable, 0)
            .With(b => b.InfraThreshold, 2)
            .With(b => b.InfraUnderConfigured, 0));

        fixture.Customize<CreateDataLagResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Message, "Enterprise Data Lag Service created successfully!")
            .With(b => b.Success, true));

        fixture.Customize<UpdateDataLagCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Updated Data Lag Service")
            .With(b => b.TotalBusinessFunction, 60)
            .With(b => b.BFAvailable, 55)
            .With(b => b.BFImpact, 3)
            .With(b => b.BFExceed, 2)
            .With(b => b.BFUnConfigured, 0)
            .With(b => b.BFNotAvailable, 0)
            .With(b => b.BFThreshold, 1)
            .With(b => b.TotalInfraObject, 120)
            .With(b => b.InfraAvailable, 110)
            .With(b => b.InfraImpact, 6)
            .With(b => b.InfraExceed, 4)
            .With(b => b.InfraNotAvailable, 0)
            .With(b => b.InfraThreshold, 2)
            .With(b => b.InfraUnderConfigured, 0));

        fixture.Customize<UpdateDataLagResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Message, "Enterprise Data Lag Service updated successfully!")
            .With(b => b.Success, true));

        fixture.Customize<DeleteDataLagCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<DeleteDataLagResponse>(c => c
            .With(b => b.IsActive, false)
            .With(b => b.Message, "Enterprise Data Lag Service deleted successfully!")
            .With(b => b.Success, true));

        fixture.Customize<DataLagDetailVm>(c => c
            .With(b => b.Id, 1)
            .With(b => b.ReferenceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Data Lag Detail Service")
            .With(b => b.TotalBusinessFunction, 40)
            .With(b => b.BFAvailable, 36)
            .With(b => b.BFImpact, 2)
            .With(b => b.BFExceed, 2)
            .With(b => b.BFUnConfigured, 0)
            .With(b => b.BFNotAvailable, 0)
            .With(b => b.BFThreshold, 1)
            .With(b => b.TotalInfraObject, 80)
            .With(b => b.InfraAvailable, 74)
            .With(b => b.InfraImpact, 4)
            .With(b => b.InfraExceed, 2)
            .With(b => b.InfraNotAvailable, 0)
            .With(b => b.InfraThreshold, 1)
            .With(b => b.InfraUnderConfigured, 0));

        fixture.Customize<DataLagListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Data Lag List Service")
            .With(b => b.TotalBusinessFunction, 35)
            .With(b => b.BFAvailable, 32)
            .With(b => b.BFImpact, 2)
            .With(b => b.BFExceed, 1)
            .With(b => b.BFUnConfigured, 0)
            .With(b => b.BFNotAvailable, 0)
            .With(b => b.BFThreshold, 1)
            .With(b => b.TotalInfraObject, 70)
            .With(b => b.InfraAvailable, 65)
            .With(b => b.InfraImpact, 3)
            .With(b => b.InfraExceed, 2)
            .With(b => b.InfraNotAvailable, 0)
            .With(b => b.InfraThreshold, 1)
            .With(b => b.InfraUnderConfigured, 0));

        fixture.Customize<GetDataLagDetailQuery>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<GetDataLagDetailByBusinessServiceIdQuery>(c => c
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString()));

        CreateDataLagCommand = fixture.Create<CreateDataLagCommand>();
        CreateDataLagResponse = fixture.Create<CreateDataLagResponse>();
        UpdateDataLagCommand = fixture.Create<UpdateDataLagCommand>();
        UpdateDataLagResponse = fixture.Create<UpdateDataLagResponse>();
        DeleteDataLagCommand = fixture.Create<DeleteDataLagCommand>();
        DeleteDataLagResponse = fixture.Create<DeleteDataLagResponse>();
        DataLagDetailVm = fixture.Create<DataLagDetailVm>();
        DataLagListVm = fixture.Create<DataLagListVm>();
        GetDataLagListQuery = fixture.Create<GetDataLagListQuery>();
        GetDataLagDetailQuery = fixture.Create<GetDataLagDetailQuery>();
        GetDataLagDetailByBusinessServiceIdQuery = fixture.Create<GetDataLagDetailByBusinessServiceIdQuery>();
    }
}
