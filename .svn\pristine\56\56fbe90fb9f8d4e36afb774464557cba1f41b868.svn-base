using ContinuityPatrol.Application.Features.Rto.Commands.Create;
using ContinuityPatrol.Application.Features.Rto.Commands.Delete;
using ContinuityPatrol.Application.Features.Rto.Commands.Update;
using ContinuityPatrol.Application.Features.Rto.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Rto.Queries.GetList;
using ContinuityPatrol.Application.Features.Rto.Queries.GetRTOByBusinessServiceId;
//using ContinuityPatrol.Application.Features.Rto.Queries.GetNameUnique;
//using ContinuityPatrol.Application.Features.Rto.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RtoModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class RtosController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<RtoListVm>>> GetRtos()
    {
        Logger.LogDebug("Get All Rtos");

        return Ok(await Mediator.Send(new GetRtoListQuery()));
    }

    [HttpGet("{id}", Name = "GetRto")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<RtoDetailVm>> GetRtoById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Rto Id");

        Logger.LogDebug($"Get Rto Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetRtoDetailQuery { Id = id }));
    }
    #region Paginated
    // [Route("paginated-list"), HttpGet]
    // [Authorize(Policy = Permissions.Dashboard.View)]
    // public async Task<ActionResult<PaginatedResult<RtoListVm>>> GetPaginatedRtos([FromQuery] GetRtoPaginatedListQuery query)
    // {
    //     Logger.LogDebug("Get Searching Details in Rto Paginated List");
    //
    //     return Ok(await Mediator.Send(query));
    // }
    #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Dashboard.Create)]
    public async Task<ActionResult<CreateRtoResponse>> CreateRto([FromBody] CreateRtoCommand createRtoCommand)
    {
        Logger.LogDebug($"Create Rto '{createRtoCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateRto), await Mediator.Send(createRtoCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Dashboard.Edit)]
    public async Task<ActionResult<UpdateRtoResponse>> UpdateRto([FromBody] UpdateRtoCommand updateRtoCommand)
    {
        Logger.LogDebug($"Update Rto '{updateRtoCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateRtoCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Dashboard.Delete)]
    public async Task<ActionResult<DeleteRtoResponse>> DeleteRto(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Rto Id");

        Logger.LogDebug($"Delete Rto Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteRtoCommand { Id = id }));
    }
    [HttpGet("businessserviceid")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<RTOByBusinessServiceIdVm>> GetRTOByBusinessServiceId(string businessServiceId)
    {
        Logger.LogDebug($"Get MonitorServiceStatus RTO Detail by BusinessServiceId '{businessServiceId}'");

        return Ok(await Mediator.Send(new GetRTOByBusinessServiceIdQuery { BusinessServiceId = businessServiceId }));
    }
    #region NameExist

    // [Route("name-exist"), HttpGet]
    // public async Task<ActionResult> IsRtoNameExist(string rtoName, string? id)
    // {
    //     Guard.Against.NullOrWhiteSpace(RtoName, "Rto Name");
    //
    //     Logger.LogDebug($"Check Name Exists Detail by Rto Name '{rtoName}' and Id '{id}'");
    //
    //     return Ok(await Mediator.Send(new GetRtoNameUniqueQuery { Name = RtoName, Id = id }));
    // }
    #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


