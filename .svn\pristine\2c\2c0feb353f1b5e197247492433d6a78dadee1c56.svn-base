﻿namespace ContinuityPatrol.Application.Features.MSSQLDBMirroingLogs.Commands.Create;

public class
    CreateSQLDBMirroringLogsCommandHandler : IRequestHandler<CreateSQLDBMirroringLogsCommand,
        CreateSQLDBMirroringLogsResponse>
{
    private readonly IMapper _mapper;
    private readonly IMsSqlDbMirroringLogRepository _mssqldbmirroringLogsRepository;

    public CreateSQLDBMirroringLogsCommandHandler(IMapper mapper,
        IMsSqlDbMirroringLogRepository mssqldbmirrorMonitorLogsRepository)
    {
        _mapper = mapper;
        _mssqldbmirroringLogsRepository = mssqldbmirrorMonitorLogsRepository;
    }

    public async Task<CreateSQLDBMirroringLogsResponse> Handle(CreateSQLDBMirroringLogsCommand request,
        CancellationToken cancellationToken)
    {
        var mssqlDbmirrorMonitorLogs = _mapper.Map<MSSQLDBMirroringLogs>(request);

        mssqlDbmirrorMonitorLogs = await _mssqldbmirroringLogsRepository.AddAsync(mssqlDbmirrorMonitorLogs);

        var response = new CreateSQLDBMirroringLogsResponse
        {
            Message = Message.Create(nameof(Domain.Entities.MSSQLMonitorLogs), mssqlDbmirrorMonitorLogs.ReferenceId),

            Id = mssqlDbmirrorMonitorLogs.ReferenceId
        };

        return response;
    }
}