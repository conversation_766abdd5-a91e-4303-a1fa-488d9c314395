﻿namespace ContinuityPatrol.Domain.Entities;

public class FourEyeApprovers : AuditableEntity
{
    public string WorkflowProfileId { get; set; }
    public string WorkflowProfileName { get; set; }
    [Column(TypeName = "NCLOB")] public string WorkflowProfile { get; set; }
    [Column(TypeName = "NCLOB")] public string ActionType { get; set; }
    [Column(TypeName = "NCLOB")] public string UserIds { get; set; }
    [Column(TypeName = "NCLOB")] public string Approvers { get; set; }
    [Column(TypeName = "NCLOB")] public string Properties { get; set; }
}