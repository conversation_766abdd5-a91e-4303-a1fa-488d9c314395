using ContinuityPatrol.Application.Features.FiaImpactType.Commands.Create;
using ContinuityPatrol.Application.Features.FiaImpactType.Commands.Update;
using ContinuityPatrol.Application.Features.FiaImpactType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FiaImpactType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FiaImpactTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IFiaImpactTypeService
{
    Task<List<FiaImpactTypeListVm>> GetFiaImpactTypeList();
    Task<BaseResponse> CreateAsync(CreateFiaImpactTypeCommand createFiaImpactTypeCommand);
    Task<BaseResponse> UpdateAsync(UpdateFiaImpactTypeCommand updateFiaImpactTypeCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<FiaImpactTypeDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsFiaImpactTypeNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<FiaImpactTypeListVm>> GetPaginatedFiaImpactTypes(GetFiaImpactTypePaginatedListQuery query);
    #endregion
}
