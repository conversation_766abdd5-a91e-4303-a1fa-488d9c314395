﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}



<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title"><i class="cp-monitoring"></i><span>Rsync Linux</span></h6>
        <span><i class="cp-"></i>Last Monitored Time : 12/9/2022 1:39:31 PM</span>
    </div>
    <div class="monitor_pages">
        <div class="row g-2 mt-0">
            <div class="col-7 d-grid">
                <div class="card Card_Design_None">
                    <div class="card-header card-title">Rsync Replication Details</div>
                    <div class="card-body pt-0 px-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>Replication Monitor</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-stand-storage me-1"></i>Primaryimary Server</td>
                                    <td class="text-truncate">172.16.128.145</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-secondary-server me-1"></i>Secondary Server</td>
                                    <td class="text-truncate">************</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-replication-source me-1"></i>Source Replication Path</td>
                                    <td class="text-truncate">/root/PUNE/EAST. /rootPUNE/West</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-file-location me-1"></i>Destination Path</td>
                                    <td class="text-truncate">/root/PUNE/EAST. /rootPUNE/West</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-storage-name me-1"></i>Number of files</td>
                                    <td class="text-truncate">147,63</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-file-sizee me-1"></i>Total file size </td>
                                    <td class="text-truncate">18668392 byetes, 13757638 bytes</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-last-backup me-1"></i>Number of regular files transferred</td>
                                    <td class="text-truncate">0,0</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-last-backup me-1"></i>Total transferred file size</td>
                                    <td class="text-truncate">0 byetes, 0 byetes</td>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>
            <div class="col-5 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center">
                        <div id="Solution_Diagram"></div>
                    </div>
                </div>
                <div class="card Card_Design_None">
                    <div class="card-header card-title">Rsync Application</div>
                    <div class="card-body pt-0 px-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>Component</th>
                                    <th class="text-primary">Primary</th>
                                    <th>DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-list-Primarysite me-1"></i>Server Name</td>
                                    <td class="text-truncate">Rsync Ser Primaryod</td>
                                    <td class="text-truncate">Rsync Ser DR</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-ip-address me-1"></i>IP Address/HostName</td>
                                    <td class="text-truncate">************</td>
                                    <td class="text-truncate">************</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/js/monitoring/SolutionDiagramRsyncLinux.js"></script>

