﻿using ContinuityPatrol.Domain.ViewModels.AlertReceiverModel;

namespace ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetList;

public class GetAlertReceiverListQueryHandler : IRequestHandler<GetAlertReceiverListQuery, List<AlertReceiverListVm>>
{
    private readonly IAlertReceiverRepository _alertReceiverRepository;
    private readonly IMapper _mapper;

    public GetAlertReceiverListQueryHandler(IMapper mapper, IAlertReceiverRepository alertReceiverRepository)
    {
        _mapper = mapper;
        _alertReceiverRepository = alertReceiverRepository;
    }

    public async Task<List<AlertReceiverListVm>> Handle(GetAlertReceiverListQuery request,
        CancellationToken cancellationToken)
    {
        var alertReceiver = (await _alertReceiverRepository.ListAllAsync()).ToList();

        return alertReceiver.Count <= 0
            ? new List<AlertReceiverListVm>()
            : _mapper.Map<List<AlertReceiverListVm>>(alertReceiver);
    }
}