﻿namespace ContinuityPatrol.Domain.Entities;

public class RiskMitigation : AuditableEntity
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public bool UnderControlRTO { get; set; }
    public bool ExceedRTO { get; set; }
    public bool MaintenanceRTO { get; set; }
    [Column(TypeName = "NCLOB")] public string RTODescription { get; set; }
    public bool UnderControlRPO { get; set; }
    public bool ExceedRPO { get; set; }
    public bool MaintenanceRPO { get; set; }
    [Column(TypeName = "NCLOB")] public string RPODescription { get; set; }
    public bool IsAffected { get; set; }
    [Column(TypeName = "NCLOB")] public string ErrorMessage { get; set; }
}