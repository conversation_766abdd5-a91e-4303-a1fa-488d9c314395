﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfileInfo.Events;

public class WorkflowProfileInfoUpdatedEventHandlerTests : IClassFixture<WorkflowProfileInfoFixture>
{
    private readonly WorkflowProfileInfoFixture _workflowProfileInfoFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly WorkflowProfileInfoUpdatedEventHandler _handler;

    public WorkflowProfileInfoUpdatedEventHandlerTests(WorkflowProfileInfoFixture workflowProfileInfoFixture)
    {
        _workflowProfileInfoFixture = workflowProfileInfoFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockWorkflowProfileInfoEventLogger = new Mock<ILogger<WorkflowProfileInfoUpdatedEventHandler>>();

        _mockUserActivityRepository = WorkflowProfileInfoRepositoryMocks.CreateWorkflowProfileInfoEventRepository(_workflowProfileInfoFixture.UserActivities);

        _handler = new WorkflowProfileInfoUpdatedEventHandler(mockLoggedInUserService.Object, mockWorkflowProfileInfoEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateWorkflowProfileInfoEventUpdated()
    {
        _workflowProfileInfoFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowProfileInfoFixture.WorkflowProfileInfoUpdatedEvent, CancellationToken.None);

        result.Equals(_workflowProfileInfoFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowProfileInfoFixture.WorkflowProfileInfoUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_UpdateWorkflowProfileInfoEventUpdated()
    {
        _workflowProfileInfoFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowProfileInfoFixture.WorkflowProfileInfoUpdatedEvent, CancellationToken.None);

        result.Equals(_workflowProfileInfoFixture.UserActivities[0].Id);

        result.Equals(_workflowProfileInfoFixture.WorkflowProfileInfoUpdatedEvent.WorkflowProfileName);

        await Task.CompletedTask;
    }
}