﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileModel;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Events.PaginatedView;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Archive.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetPaginatedList;

namespace ContinuityPatrol.Web.Areas.Manage.Controllers;

[Area("Manage")]
public class ApprovalMatrixController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IDataProvider _dataProvider;
    private readonly ILogger<ApprovalMatrixController> _logger;
    private readonly IMapper _mapper;
    private readonly IEmailService _emailService;

    public ApprovalMatrixController(IPublisher publisher,IMapper mapper, ILogger<ApprovalMatrixController> logger, IDataProvider dataProvider, IEmailService emailService)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
        _emailService = emailService;
    }
    
    [AntiXss]
    public IActionResult List()
    {
        _publisher.Publish(new ApprovalMatrixPaginatedEvent());
        return View();
    }

    [HttpGet]
    public async Task<JsonResult> ApprovalMatrixList()
    {
        _logger.LogDebug("Entering ApprovalMatrixList method in ApprovalMatrix");

        try
        {
            var result = await _dataProvider.approvalMatrixService.GetApprovalMatrixList();
            _logger.LogDebug("Successfully retrieved ApprovalMatrixList in ApprovalMatrix");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on ApprovalMatrix page while retrieving ApprovalMatrixList.", ex);
            return ex.GetJsonException();
        }
    }


    public IActionResult TemplateList()
    {      
        return View();
    }

    public IActionResult ApproversList()
    {
        return View();
    }

    //// [ValidateAntiForgeryToken]
    //[AntiXss]
    //public async Task<IActionResult> Template()
    //{
    //   // await _publisher.Publish(new ApprovalMatrixTemplatePaginatedEvent());
    //    var userGroupList = await _dataProvider.UserGroup.GetUserGroupList();
    //    var userNames = await _dataProvider.User.GetUserNames();
    //    var workflowTemplates = await _dataProvider.Workflow.GetWorkflowNames();
    //    var workflowProfiles = await _dataProvider.WorkflowProfile.GetPaginatedWorkflowProfile(new GetWorkflowProfilePaginatedListQuery());
    //    var businessServiceList = await _dataProvider.BusinessService.GetBusinessServiceList();
    //    //var templates = await _dataProvider.ApprovalMatrixTemplateService.GetPaginatedApprovalMatrixTemplates(new GetPaginatedTemplatesQuery());

    //    string username = WebHelper.UserSession.LoginName;

    //    userNames = userNames.Where(x => x.LoginName.ToLower().TrimEnd() != username.ToLower().TrimEnd()).ToList();

    //    //ApprovalMatrixTemplate approvalMatrixTemplate = new ApprovalMatrixTemplate
    //    //{
    //    //    workflows = workflowTemplates,
    //    //    workflowProfiles = workflowProfiles,
    //    //    businessServiceList = businessServiceList,
    //    //    templates = templates
    //    //};

    //    FourEyeModel _fourEyeModel = new FourEyeModel
    //    {
    //        UserGroup = userGroupList,
    //        UsersList = userNames
    //    };

    //    ApprovalMatrixModel approvalMatrixModel = new ApprovalMatrixModel
    //    {
    //        //ApprovalMatrixTemplate = approvalMatrixTemplate,
    //        //FourEyeModel = _fourEyeModel
    //    };

    //    return View(approvalMatrixModel);
    //}

    //[HttpPost]
    //[ValidateAntiForgeryToken]
    //[AntiXss]
    //public async Task<IActionResult> CreateOrUpdate(ApprovalMatrixModel approvalMatrixModel)
    //{
    //    if (!string.IsNullOrEmpty(approvalMatrixModel.FourEyeModel.UserNames))
    //    {
    //        foreach (var item in approvalMatrixModel.FourEyeModel.UserNames.Split(','))
    //        {
    //            if (!item.Equals(" "))
    //            {
    //                var userData = await _dataProvider.User.GetByReferenceId(item.TrimEnd().Replace(" ", ""));
    //                approvalMatrixModel.FourEyeModel.UserId = approvalMatrixModel.FourEyeModel.UserId + userData.Id +",";
    //                approvalMatrixModel.FourEyeModel.UserName = approvalMatrixModel.FourEyeModel.UserName + userData.LoginName+",";
    //                approvalMatrixModel.FourEyeModel.Emails = approvalMatrixModel.FourEyeModel.Emails + userData.UserInfo.Email+",";
    //            }
    //        }
    //    }
    //    if (!string.IsNullOrEmpty(approvalMatrixModel.FourEyeModel.GroupNames))
    //    {
    //        foreach (var _item in approvalMatrixModel.FourEyeModel.GroupNames.TrimEnd(',').Split(','))
    //        {
    //            if (!_item.Equals(" "))
    //            {
    //                string username = WebHelper.UserSession.LoginName;
    //                string userId = WebHelper.UserSession.LoggedUserId;
    //                var userGroupData = await _dataProvider.UserGroup.GetByReferenceId(_item.TrimStart().ToString());
    //                foreach (var _dt in userGroupData.UserNames.Split(','))
    //                {
    //                    if (!username.Equals(_dt.TrimStart()))
    //                    {
    //                        approvalMatrixModel.FourEyeModel.UserName = approvalMatrixModel.FourEyeModel.UserName.TrimEnd(',');
    //                        approvalMatrixModel.FourEyeModel.UserName = approvalMatrixModel.FourEyeModel.UserName + "," + _dt;
    //                    }
    //                }

    //                foreach (var item in userGroupData.UserId.Split(','))
    //                {
    //                    if (!userId.Equals(item.TrimStart()))
    //                    {
    //                        var userDetails = await _dataProvider.User.GetByReferenceId(item);
    //                        approvalMatrixModel.FourEyeModel.UserId = approvalMatrixModel.FourEyeModel.UserId.TrimEnd(',');
    //                        approvalMatrixModel.FourEyeModel.UserId = approvalMatrixModel.FourEyeModel.UserId+ "," + item;
    //                        approvalMatrixModel.FourEyeModel.Emails = approvalMatrixModel.FourEyeModel.Emails + "," + userDetails.UserInfo.Email;

    //                    }
    //                }
    //            }
    //        }

    //    }

    //    //var approvalmatrix_Command = _mapper.Map<CreateApprovalMatrixTemplateCommand>(_approvalmodelmatrix.FourEyeModel);
    //    //var result = await _dataProvider.ApprovalMatrixTemplateService.CreateAsync(approvalmatrix_Command);

    //    #region Mail_Sending
    //    //   string UserdId = WebHelper.UserSession.LoggedUserId;
    //    //   var userdetails = await _dataProvider.User.GetByReferenceId(UserdId);
    //    //   string message = string.Empty;
    //    //   string subject = string.Empty;
    //    //   string _mailHeader = @"<html><head><title>CP</title></head><body>" + "<table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"background-color:#f4f7f9;font-family:Verdana;\">" +
    //    //"<tbody><tr>" + "<td align=\"center\" valign=\"middle\">" + "<table width=\"800\" border=\"0\" align=\"center\" cellpadding=\"0\" cellspacing=\"0\">" +
    //    //"<tbody><tr>" + "<td style=\"background: rgb(255, 255, 255); padding: 0px 30px 15px 30px;\" valign=\"top\">" + "<table><tbody>" +
    //    //"<tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px\"> Dear Team,</td>" + "</tr>" +
    //    //"<tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px\">Request  for " + _approvalmodelmatrix.FourEyeModel.Name + "  is raised by " + userdetails.LoginName + " on " + DateTime.Now + "<br> <br> " + "</td>" + "</tr>" +
    //    //       "<tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px\"> User is waiting for your approval " +"</td>" + "</tr>";
    //    //   string _incidentSource = "<tr></tr></tbody></table>";

    //    //   string ApproveString = "";
    //    //   string _mailFooter = "</td></tr><tr><td style=\"background: rgb(255, 255, 255); padding: 0px 30px 15px 30px;\" valign=\"top\">" + "<table><tbody><tr>" +
    //    //   "<td style=\"color: rgb(51, 51, 51);padding: 0px 0px 5px;font-size:13px;\">Thank You,</td>" + "</tr><tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px; font-weight: 600;\">Continuty Patrol</td>" +
    //    //   "</tr></tbody></table>" + "</td></tr>" + "</tbody></table>" + "</td>" + "</tr>" + "</body></html>";


    //    //   string _additionalnotes = string.Empty;
    //    //   message = _mailHeader + _incidentSource + ApproveString + _additionalnotes + _mailFooter;

    //    //   var smtpdetails = await _dataProvider.SmtpConfiguration.GetSmtpConfigurationList();

    //    //   EmailDto _emaildto = new EmailDto();
    //    //   _emaildto.To = _approvalmodelmatrix.FourEyeModel.Emails;
    //    //   _emaildto.From= userdetails.UserInfo.Email;
    //    //   _emaildto.Subject = "Approval Required for : " + _approvalmodelmatrix.FourEyeModel.Name;
    //    //   _emaildto.Body = message;
    //    //   _emaildto.SmtpHost = smtpdetails.SmtpHost;
    //    //   _emaildto.Port = smtpdetails.Port;
    //    //   _emaildto.EnableSSL = smtpdetails.EnableSSL;

    //    //   var sendingMails = await _emailService.SendEmail(_emaildto);


    //    #endregion Mail_Sending

    //    return RouteToPostView_2(new BaseResponse());

    //}

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate_Process(ApprovalMatrixModel approvalModelMatrix)
    {
        //if (!string.IsNullOrEmpty(approvalModelMatrix.ApprovalMatrixTemplate.BusinessServiceName))
        //{
        //    foreach(var item in approvalModelMatrix.ApprovalMatrixTemplate.BusinessServiceName.Split(','))
        //    {
        //        if (!(item.Equals(" ")))
        //        {
        //            var bsDetails = await _dataProvider.BusinessService.GetByBusinessServiceName(item);
        //            approvalModelMatrix.ApprovalMatrixTemplate.BusinessServiceId = approvalModelMatrix.ApprovalMatrixTemplate.BusinessServiceId + "," + bsDetails.Id;
        //        }
        //    }
        //}
        //string userId = WebHelper.UserSession.LoggedUserId;
        //var setProperties = await _dataProvider.User.GetByReferenceId(userId);
        // approvalModelMatrix.ApprovalMatrixTemplate.Properties = (DateTime.Now.ToString("MMMM dd yyyy")+"$"+DateTime.Now.ToString("hh:mm tt").ToString()+"$" +setProperties.LoginName+"$").ToString();

        //var approvalMatrix_Command = _mapper.Map<CreateApprovalMatrixCommand>(approvalModelMatrix.ApprovalMatrixTemplate);
        //approvalMatrix_Command.ApprovalFlag="0";
        //approvalMatrix_Command.RejectedFlag="0";
        //approvalMatrix_Command.Status = "Pending";

        #region Mail_Sending
        //   string userId = WebHelper.UserSession.LoggedUserId;
        //   var userDetails = await _dataProvider.User.GetByReferenceId(userId);
        //   string message = string.Empty;
        //   string subject = string.Empty;
        //   string _mailHeader = @"<html><head><title>CP</title></head><body>" + "<table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"background-color:#f4f7f9;font-family:Verdana;\">" +
        //"<tbody><tr>" + "<td align=\"center\" valign=\"middle\">" + "<table width=\"800\" border=\"0\" align=\"center\" cellpadding=\"0\" cellspacing=\"0\">" +
        //"<tbody><tr>" + "<td style=\"background: rgb(255, 255, 255); padding: 0px 30px 15px 30px;\" valign=\"top\">" + "<table><tbody>" +
        //"<tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px\"> Dear Team,</td>" + "</tr>" +
        //"<tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px\">Request  for " + approvalMatrix_Command.Name + "  is raised by " + userDetails.LoginName + " on " + DateTime.Now + "<br> <br> For "+approvalMatrix_Command.WorkflowNames + "</td>" + "</tr>" +
        //       "<tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px\"> and the User is waiting for your approval " +"</td>" + "</tr>";
        //   string _incidentSource = "<tr></tr></tbody></table>";

        //   string ApproveString = "";
        //   string _mailFooter = "</td></tr><tr><td style=\"background: rgb(255, 255, 255); padding: 0px 30px 15px 30px;\" valign=\"top\">" + "<table><tbody><tr>" +
        //   "<td style=\"color: rgb(51, 51, 51);padding: 0px 0px 5px;font-size:13px;\">Thank You,</td>" + "</tr><tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px; font-weight: 600;\">Continuity Patrol</td>" +
        //   "</tr></tbody></table>" + "</td></tr>" + "</tbody></table>" + "</td>" + "</tr>" + "</body></html>";


        //   string additionalNotes = string.Empty;
        //   message = _mailHeader + _incidentSource + ApproveString + additionalNotes + _mailFooter;

        //   var smtpDetails = await _dataProvider.SmtpConfiguration.GetSmtpConfigurationList();

        //   EmailDto emailDto = new EmailDto();
        //   emailDto.To = approvalModelMatrix.FourEyeModel.Emails;
        //   emailDto.From= userDetails.UserInfo.Email;
        //   emailDto.Subject = "Approval Required for : " + approvalMatrix_Command.Name;
        //   emailDto.Body = message;
        //   emailDto.SmtpHost = smtpDetails.SmtpHost;
        //   emailDto.Port = smtpDetails.Port;
        //   emailDto.EnableSSL = smtpDetails.EnableSSL;

        //   var sendingMails = await _emailService.SendEmail(emailDto);

        #endregion Mail_Sending
        // var result = await _dataProvider.approvalMatrixService.CreateAsync(approvalMatrix_Command);



        return RouteToPostView_1(new BaseResponse());
    }

    //[HttpGet]
    //public async Task<JsonResult> GetPaginationByUser()
    //{
    //    string userId = WebHelper.UserSession.LoggedUserId;

    //    return Json(await _dataProvider.approvalMatrixService.GetPaginatedApprovalMatricesByUserId(userId));
    //}

    //[HttpGet]
    //public async Task<JsonResult> GetPaginatedTemplateList(GetPaginatedTemplatesQuery query)
    //{
    //    return Json(await _dataProvider.ApprovalMatrixTemplateService.GetPaginatedApprovalMatrixTemplates(query));
    //}

    [HttpGet]
    public async Task<JsonResult> GetPaginationss(GetApprovalMatrixPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in ApprovalMatrix");
        try
        {
            _logger.LogDebug("Successfully retrieved ApprovalMatrix paginated list on OperationalService page");
            return Json(await _dataProvider.approvalMatrixService.GetPaginatedApprovalMatrices(query));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on ApprovalMatrix page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }
    }


    [HttpGet]
    public async Task<JsonResult> GetPagination()
    {
        string userId = WebHelper.UserSession.LoggedUserId;

        return Json(await _dataProvider.approvalMatrixService.GetApprovalMatrixListByUser(userId));

    }

    [HttpGet]
    public async Task<JsonResult> GetPaginatedlist(GetApprovalMatrixRequestPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPaginated method in Archive");
        try
        {
            _logger.LogDebug("Successfully retrieved paginated list for archive");

            return Json(await _dataProvider.ApprovalMatrixRequest.GetPaginatedApprovalMatrixRequests(query));

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on archive page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> ApproveMatrix(string id, string name)
    {
        _logger.LogInformation("==== Approving the approval matrix : " + id);

        string status = "Approved";
        string userId = WebHelper.UserSession.LoggedUserId;
        await _dataProvider.approvalMatrixService.ApproveApprovalRequest(id, status, userId);
        return RouteToPostView("Approval Request is approved for : " + name);

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> RejectMatrix(string id, string name)
    {
        _logger.LogInformation("==== Rejecting the approval matrix : " + id);

        string status = "Rejected";
        string userId = WebHelper.UserSession.LoggedUserId;
        await _dataProvider.approvalMatrixService.ApproveApprovalRequest(id, status, userId);
        return RouteToPostView("Approval Request is rejected for : " + name);

    }

    [HttpGet]
    public async Task<JsonResult> GetBusinessServiceList()
    {
        _logger.LogDebug("Entering GetSiteTypeList method in SiteType");

        try
        {
            var result = await _dataProvider.BusinessService.GetBusinessServiceNames();

            _logger.LogDebug("Successfully retrieved SiteType names in SiteType");

            return Json(result);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on SiteType page while retrieving SiteType List.", ex);

            return ex.GetJsonException();
        }
    }
    [HttpGet]
    public async Task<JsonResult> GetWorkflowByBusinessService(string list)
    {
        _logger.LogInformation("==== Getting workflow details by businessService name ====");

        List<WorkflowNameVm> workflowNameVms = new List<WorkflowNameVm>();
        if (!string.IsNullOrEmpty(list))
        {
            var bsIds = list.TrimEnd().Split(',');

            foreach (var item in bsIds)
            {
                var infraList = await _dataProvider.InfraObject.GetInfraObjectByBusinessServiceName(item.TrimEnd());
                if (infraList.Count()>0)
                {
                    foreach (var _item in infraList)
                    {
                        var bs_list = await _dataProvider.WorkflowInfraObject.GetWorkflowByInfraObjectId(_item.Id);
                        if (bs_list!=null)
                        {
                            foreach (var data in bs_list)
                            {
                                WorkflowNameVm workflowNameVm = new WorkflowNameVm();
                                workflowNameVm.Id = data.WorkflowId;
                                workflowNameVm.Name = data.WorkflowName;
                                workflowNameVms.Add(workflowNameVm);
                            }
                        }
                    }
                }
            }

        }
        return Json(workflowNameVms);
    }

    [HttpGet]
    public async Task<JsonResult> GetWorkflowProfilesByBusinessService(string list)
    {
        _logger.LogInformation("==== Getting workflow details by businessService name ====");

        List<WorkflowProfileNameVm> workflowNameVms = new List<WorkflowProfileNameVm>();
        if (!string.IsNullOrEmpty(list))
        {
            var bsIds = list.TrimEnd().Split(',');

            foreach (var item in bsIds)
            {
                var infraList = await _dataProvider.InfraObject.GetInfraObjectByBusinessServiceName(item.TrimEnd());
                if (infraList.Count()>0)
                {
                    foreach (var _item in infraList)
                    {
                        var bs_list = await _dataProvider.WorkflowProfileInfo.GetWorkflowProfileInfoByInfraObjectId(_item.Id);
                        if (bs_list!=null)
                        {
                            foreach (var data in bs_list)
                            {
                                WorkflowProfileNameVm workflowNameVm = new WorkflowProfileNameVm();
                                workflowNameVm.Id = data.ProfileId;
                                workflowNameVm.Name = data.ProfileName;
                                workflowNameVms.Add(workflowNameVm);
                            }
                        }
                    }
                }
            }

        }
        return Json(workflowNameVms);
    }

    [HttpGet]
    public bool IsTemplateNameExist(string templateName)
    {
        //var nameExist = await _dataProvider.ApprovalMatrixTemplateService.IsTemplateNameExist(TemplateName);

        return true;
    }
    [HttpGet]
    public async Task<bool> IsMatrixNameExist(string matrixName, string id)
    {
        var nameExist = await _dataProvider.approvalMatrixService.IsMatrixNameExist(matrixName,id);

        return nameExist;
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> WithdrawApprovalMatrix(string approvalId)
    {
        string[] data = approvalId.Split('$');
        await _dataProvider.approvalMatrixService.WithdrawApprovalMatrix(data[0]);
        //return (JsonResult)RouteToPostView("Approval Request successfully withdrawal for : " + data[1]);
        return Json("Success");
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> WithdrawApprovalMatrixTemplate(string approvalId)
    {
        var IsAttached = await _dataProvider.approvalMatrixService.IsTemplateAttached(approvalId.Split('$')[1]);
        if (IsAttached)
            return Json("Attached");
        else
            return Json("");

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public JsonResult IsApproversForSelected(string approvalId)
    {
        //string[] data = approvalId.Split('$');
       // var result = await _dataProvider.ApprovalMatrixTemplateService.IsApproverForTemplate(approvalId.TrimEnd());
        return Json(new BaseResponse());
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public Task<JsonResult> GetLoginName()
    {
        string UserName = WebHelper.UserSession.LoginName;
        return Task.FromResult(Json(UserName));
    }


    private IActionResult RouteToPostView(string result)
    {
        TempData.NotifySuccess(result);

        return RedirectToAction("List");
    }
    private IActionResult RouteToPostView_1(BaseResponse result)
    {
        TempData.NotifySuccess(result.Message);

        return RedirectToAction("List");
    }
    //private IActionResult RouteToPostView_2(BaseResponse result)
    //{
    //    TempData.NotifySuccess(result.Message);

    //    return RedirectToAction("Template");
    //}
}