using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class EscalationMatrixRepositoryTests : IClassFixture<EscalationMatrixFixture>, IDisposable
{
    private readonly EscalationMatrixFixture _escalationMatrixFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly EscalationMatrixRepository _repository;

    public EscalationMatrixRepositoryTests(EscalationMatrixFixture escalationMatrixFixture)
    {
        _escalationMatrixFixture = escalationMatrixFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new EscalationMatrixRepository(_dbContext);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnExpectedCount_WhenEscalationMatricesExist()
    {
        await _dbContext.EscalationMatrix.AddRangeAsync(_escalationMatrixFixture.EscalationMatrixList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoEscalationMatricesExist()
    {
        var result = await _repository.ListAllAsync();

        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsOnlyActiveEscalationMatrices_WhenInactiveEscalationMatricesExist()
    {
        _escalationMatrixFixture.EscalationMatrixList[0].IsActive = true;
 
        _escalationMatrixFixture.EscalationMatrixList[2].IsActive = true;

        await _dbContext.EscalationMatrix.AddRangeAsync(_escalationMatrixFixture.EscalationMatrixList);
        await _dbContext.SaveChangesAsync();
        _escalationMatrixFixture.EscalationMatrixList[1].IsActive = false;
         _dbContext.EscalationMatrix.UpdateRange(_escalationMatrixFixture.EscalationMatrixList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsEscalationMatrix_WhenExists()
    {
        _dbContext.EscalationMatrix.Add(_escalationMatrixFixture.EscalationMatrixDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(_escalationMatrixFixture.EscalationMatrixDto.ReferenceId);

        Assert.NotNull(result);
        Assert.Equal(_escalationMatrixFixture.EscalationMatrixDto.ReferenceId, result.ReferenceId);
        Assert.Equal(_escalationMatrixFixture.EscalationMatrixDto.EscMatCode, result.EscMatCode);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenReferenceIdDoesNotExist()
    {
        var nonExistentReferenceId = Guid.NewGuid().ToString();

        var result = await _repository.GetByReferenceIdAsync(nonExistentReferenceId);

        Assert.Null(result);
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ReturnsEscalationMatrix_WhenExists()
    {
        _escalationMatrixFixture.EscalationMatrixDto.Id = 1;
        await _dbContext.EscalationMatrix.AddAsync(_escalationMatrixFixture.EscalationMatrixDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByIdAsync(1);

        Assert.NotNull(result);
        Assert.Equal(_escalationMatrixFixture.EscalationMatrixDto.Id, result.Id);
        Assert.Equal(_escalationMatrixFixture.EscalationMatrixDto.EscMatCode, result.EscMatCode);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenRecordDoesNotExist()
    {
        var nonExistentId = 999;
        var result = await _repository.GetByIdAsync(nonExistentId);

        Assert.Null(result);
    }

    #endregion

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEscalationMatrix_WhenValidEscalationMatrix()
    {
        var escalationMatrix = _escalationMatrixFixture.EscalationMatrixDto;
        escalationMatrix.EscMatCode = "ESC001";

        var result = await _repository.AddAsync(escalationMatrix);

        Assert.NotNull(result);
        Assert.Equal(escalationMatrix.EscMatCode, result.EscMatCode);
        Assert.Single(_dbContext.EscalationMatrix);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEscalationMatrixIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEscalationMatrix_WhenValidEscalationMatrix()
    {
        _dbContext.EscalationMatrix.Add(_escalationMatrixFixture.EscalationMatrixDto);
        await _dbContext.SaveChangesAsync();

        _escalationMatrixFixture.EscalationMatrixDto.EscMatCode = "Updated Code";
        var result = await _repository.UpdateAsync(_escalationMatrixFixture.EscalationMatrixDto);

        Assert.NotNull(result);
        Assert.Equal("Updated Code", result.EscMatCode);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEscalationMatrixIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region IsEscalationNameExist Tests

    [Fact]
    public async Task IsEscalationNameExist_ReturnsTrue_WhenNameExists_WithoutId()
    {
        var escalationName = "ESC001";
        _escalationMatrixFixture.EscalationMatrixDto.EscMatCode = escalationName;
        _escalationMatrixFixture.EscalationMatrixDto.IsActive = true;

        await _dbContext.EscalationMatrix.AddAsync(_escalationMatrixFixture.EscalationMatrixDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationNameExist(escalationName, null);

        Assert.True(result);
    }

    [Fact]
    public async Task IsEscalationNameExist_ReturnsFalse_WhenNameDoesNotExist_WithoutId()
    {
        var nonExistentName = "Non Existent Code";

        var result = await _repository.IsEscalationNameExist(nonExistentName, null);

        Assert.False(result);
    }

    [Fact]
    public async Task IsEscalationNameExist_ReturnsFalse_WhenNameExists_WithSameId()
    {
        var existingId = Guid.NewGuid().ToString();
        var escalationName = "ESC002";

        _escalationMatrixFixture.EscalationMatrixDto.ReferenceId = existingId;
        _escalationMatrixFixture.EscalationMatrixDto.EscMatCode = escalationName;
        _escalationMatrixFixture.EscalationMatrixDto.IsActive = true;

        await _dbContext.EscalationMatrix.AddAsync(_escalationMatrixFixture.EscalationMatrixDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationNameExist(escalationName, existingId);

        Assert.False(result);
    }

    [Fact]
    public async Task IsEscalationNameExist_ReturnsTrue_WhenNameExists_WithDifferentId()
    {
        var testId = Guid.NewGuid().ToString();
        var escalationName = "ESC003";

        _escalationMatrixFixture.EscalationMatrixDto.EscMatCode = escalationName;
        _escalationMatrixFixture.EscalationMatrixDto.IsActive = true;

        await _dbContext.EscalationMatrix.AddAsync(_escalationMatrixFixture.EscalationMatrixDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationNameExist(escalationName, testId);

        Assert.True(result);
    }

    [Fact]
    public async Task IsEscalationNameExist_ReturnsTrue_WhenNameExists_WithInvalidGuid()
    {
        var invalidId = "not-a-valid-guid";
        var escalationName = "ESC004";

        _escalationMatrixFixture.EscalationMatrixDto.EscMatCode = escalationName;
        _escalationMatrixFixture.EscalationMatrixDto.IsActive = true;

        await _dbContext.EscalationMatrix.AddAsync(_escalationMatrixFixture.EscalationMatrixDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationNameExist(escalationName, invalidId);

        Assert.True(result);
    }

    [Fact]
    public async Task IsEscalationNameExist_IsCaseSensitive()
    {
        var escalationName = "ESC005";
        _escalationMatrixFixture.EscalationMatrixDto.EscMatCode = escalationName;
        _escalationMatrixFixture.EscalationMatrixDto.IsActive = true;

        await _dbContext.EscalationMatrix.AddAsync(_escalationMatrixFixture.EscalationMatrixDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationNameExist("esc005", null);

        Assert.False(result);
    }

    [Fact]
    public async Task IsEscalationNameExist_HandlesEmptyString()
    {
        var result = await _repository.IsEscalationNameExist("", null);

        Assert.False(result);
    }

    [Fact]
    public async Task IsEscalationNameExist_HandlesEmptyId()
    {
        var escalationName = "Test Code";
        _escalationMatrixFixture.EscalationMatrixDto.EscMatCode = escalationName;
        _escalationMatrixFixture.EscalationMatrixDto.IsActive = true;

        await _dbContext.EscalationMatrix.AddAsync(_escalationMatrixFixture.EscalationMatrixDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationNameExist(escalationName, "");

        Assert.True(result);
    }

    #endregion

    #region IsEscalationNameUnique Tests

    [Fact]
    public async Task IsEscalationNameUnique_ReturnsTrue_WhenNameExists()
    {
        var escalationName = "Unique Code";
        _escalationMatrixFixture.EscalationMatrixDto.EscMatCode = escalationName;
        _escalationMatrixFixture.EscalationMatrixDto.IsActive = true;

        await _dbContext.EscalationMatrix.AddAsync(_escalationMatrixFixture.EscalationMatrixDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationNameUnique(escalationName);

        Assert.True(result);
    }

    [Fact]
    public async Task IsEscalationNameUnique_ReturnsFalse_WhenNameDoesNotExist()
    {
        var nonExistentName = "Non Existent Code";

        var result = await _repository.IsEscalationNameUnique(nonExistentName);

        Assert.False(result);
    }


    [Fact]
    public async Task IsEscalationNameUnique_IsCaseSensitive()
    {
        var escalationName = "Case Sensitive Code";
        _escalationMatrixFixture.EscalationMatrixDto.EscMatCode = escalationName;
        _escalationMatrixFixture.EscalationMatrixDto.IsActive = true;

        await _dbContext.EscalationMatrix.AddAsync(_escalationMatrixFixture.EscalationMatrixDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationNameUnique("CASE SENSITIVE CODE");

        Assert.False(result);
    }

    [Fact]
    public async Task IsEscalationNameUnique_HandlesEmptyString()
    {
        var result = await _repository.IsEscalationNameUnique("");

        Assert.False(result);
    }

    #endregion

    #region GetNames Tests

    [Fact]
    public async Task GetNames_ReturnsActiveEscalationMatrixNames_WhenActiveRecordsExist()
    {
        _escalationMatrixFixture.EscalationMatrixList[0].EscMatCode = "ESC001";
        _escalationMatrixFixture.EscalationMatrixList[0].IsActive = true;

        _escalationMatrixFixture.EscalationMatrixList[2].EscMatCode = "ESC003";
        _escalationMatrixFixture.EscalationMatrixList[2].IsActive = true;

        await _dbContext.EscalationMatrix.AddRangeAsync(_escalationMatrixFixture.EscalationMatrixList);
        await _dbContext.SaveChangesAsync();
        _escalationMatrixFixture.EscalationMatrixList[1].EscMatCode = "ESC002";
        _escalationMatrixFixture.EscalationMatrixList[1].IsActive = false;
         _dbContext.EscalationMatrix.UpdateRange(_escalationMatrixFixture.EscalationMatrixList);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.GetNames();

        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.Name == "ESC001");
        Assert.Contains(result, x => x.Name == "ESC003");
        Assert.DoesNotContain(result, x => x.Name == "ESC002");
    }

    [Fact]
    public async Task GetNames_ReturnsEmpty_WhenNoActiveRecordsExist()
    {

        await _dbContext.EscalationMatrix.AddRangeAsync(_escalationMatrixFixture.EscalationMatrixList);
        await _dbContext.SaveChangesAsync();
        _escalationMatrixFixture.EscalationMatrixList.ForEach(x => x.IsActive = false);
        _dbContext.EscalationMatrix.UpdateRange(_escalationMatrixFixture.EscalationMatrixList);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.GetNames();

        Assert.Empty(result);
    }

    [Fact]
    public async Task GetNames_ReturnsEmpty_WhenNoRecordsExist()
    {
        var result = await _repository.GetNames();

        Assert.Empty(result);
    }

    [Fact]
    public async Task GetNames_ReturnsCorrectIdAndNameMapping()
    {
        var escalationMatrix = _escalationMatrixFixture.EscalationMatrixDto;
        escalationMatrix.EscMatCode = "TEST001";
        escalationMatrix.IsActive = true;

        await _dbContext.EscalationMatrix.AddAsync(escalationMatrix);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetNames();

        Assert.Single(result);
        Assert.Equal(escalationMatrix.ReferenceId, result[0].Id);
        Assert.Equal(escalationMatrix.EscMatCode, result[0].Name);
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldMarkAsInactive_WhenEscalationMatrixExists()
    {
        _dbContext.EscalationMatrix.Add(_escalationMatrixFixture.EscalationMatrixDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.DeleteAsync(_escalationMatrixFixture.EscalationMatrixDto);
        var retrieved = await _repository.ListAllAsync();
        Assert.True(retrieved.Count==0);
        //Assert.False(result.IsActive);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEscalationMatrixIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region FindByFilterAsync Tests

    [Fact]
    public async Task FindByFilterAsync_ReturnsMatchingEscalationMatrices_WhenFilterMatches()
    {
        _escalationMatrixFixture.EscalationMatrixList[0].EscMatCode = "Critical Matrix";
        _escalationMatrixFixture.EscalationMatrixList[1].EscMatCode = "Warning Matrix";
        _escalationMatrixFixture.EscalationMatrixList[2].EscMatCode = "Critical Alert";

        await _dbContext.EscalationMatrix.AddRangeAsync(_escalationMatrixFixture.EscalationMatrixList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.FindByFilterAsync(e => e.EscMatCode.Contains("Critical"));

        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains("Critical", x.EscMatCode));
    }

    [Fact]
    public async Task FindByFilterAsync_ReturnsEmpty_WhenNoMatches()
    {
        await _dbContext.EscalationMatrix.AddRangeAsync(_escalationMatrixFixture.EscalationMatrixList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.FindByFilterAsync(e => e.EscMatCode == "Non Existent Matrix");

        Assert.Empty(result);
    }

    [Fact]
    public async Task FindByFilterAsync_ReturnsOnlyActiveRecords_WhenFilteringByActiveStatus()
    {
        _escalationMatrixFixture.EscalationMatrixList[0].IsActive = true;
        _escalationMatrixFixture.EscalationMatrixList[1].IsActive = false;
        _escalationMatrixFixture.EscalationMatrixList[2].IsActive = true;

        await _dbContext.EscalationMatrix.AddRangeAsync(_escalationMatrixFixture.EscalationMatrixList);
        await _dbContext.SaveChangesAsync();
        _escalationMatrixFixture.EscalationMatrixList[1].IsActive = false;

         _dbContext.EscalationMatrix.UpdateRange(_escalationMatrixFixture.EscalationMatrixList);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.FindByFilterAsync(e => e.IsActive);

        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region QueryAll Tests

 
    //[Fact]
    //public async Task QueryAll_WithFilter_ReturnsFilteredResults()
    //{
    //    _escalationMatrixFixture.EscalationMatrixList[0].EscMatCode = "Test Matrix 1";
    //    _escalationMatrixFixture.EscalationMatrixList[1].EscMatCode = "Test Matrix 2";
    //    _escalationMatrixFixture.EscalationMatrixList[2].EscMatCode = "Other Matrix";

    //    await _dbContext.EscalationMatrix.AddRangeAsync(_escalationMatrixFixture.EscalationMatrixList);
    //    await _dbContext.SaveChangesAsync();

    //    var result = _repository.QueryAll(e => e.EscMatCode.StartsWith("Test")).ToList();

    //    Assert.Equal(2, result.Count);
    //    Assert.All(result, x => Assert.StartsWith("Test", x.EscMatCode));
    //}

    #endregion

    #region FilterBy Tests

    [Fact]
    public async Task FilterBy_ReturnsFilteredQueryable_WhenCalled()
    {
        _escalationMatrixFixture.EscalationMatrixList[0].EscMatDesc = "Description A";
        _escalationMatrixFixture.EscalationMatrixList[1].EscMatDesc = "Description B";
        _escalationMatrixFixture.EscalationMatrixList[2].EscMatDesc = "Description A";

        await _dbContext.EscalationMatrix.AddRangeAsync(_escalationMatrixFixture.EscalationMatrixList);
        await _dbContext.SaveChangesAsync();

        var result = _repository.FilterBy(e => e.EscMatDesc == "Description A").ToList();

        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal("Description A", x.EscMatDesc));
    }

    #endregion
}
