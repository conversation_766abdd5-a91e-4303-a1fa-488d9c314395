﻿namespace ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.
    GetWorkflowActionResultByOperationGroupId;

public class GetWorkflowActionResultByOperationGroupIdQueryHandler : IRequestHandler<
    GetWorkflowActionResultByOperationGroupIdQuery, List<WorkflowActionResultByOperationGroupIdVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowActionResultRepository _workflowActionResultRepository;

    public GetWorkflowActionResultByOperationGroupIdQueryHandler(IMapper mapper,
        IWorkflowActionResultRepository workflowActionResultRepository)
    {
        _mapper = mapper;
        _workflowActionResultRepository = workflowActionResultRepository;
    }

    public async Task<List<WorkflowActionResultByOperationGroupIdVm>> Handle(
        GetWorkflowActionResultByOperationGroupIdQuery request, CancellationToken cancellationToken)
    {
        var workflowActionResult =
            await _workflowActionResultRepository.GetWorkflowActionResultByWorkflowOperationGroupId(
                request.WorkflowOperationGroupId);

        var workflowActionResultDto = _mapper.Map<List<WorkflowActionResultByOperationGroupIdVm>>(workflowActionResult);

        return workflowActionResultDto;
    }
}