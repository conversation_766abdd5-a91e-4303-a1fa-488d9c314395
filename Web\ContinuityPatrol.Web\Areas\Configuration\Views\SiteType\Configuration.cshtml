﻿@using ContinuityPatrol.Shared.Services.Helper;
@model ContinuityPatrol.Domain.ViewModels.SiteTypeModel.SiteTypeListModel

<div id="createModal" class="modal-dialog modal-dialog-centered modal-lg">
	<form class="modal-content" id="siteTypeCreateForm">
		<div class="modal-header">
			<h6 class="page_title"><i class="cp-web"></i><span>Site Type Configuration</span></h6>
			<button type="button" title="Close" class="btn-close" data-bs-dismiss="modal"></button>
		</div>
		<div class="modal-body">
			<div class="form-group">
				<label class="animation-label form-label custom-cursor-default-hover"
					   cursorshover="true">Name</label>
				<div class="input-group">
					<span class="input-group-text">
						<i class="cp-name"></i>
					</span>
					<input type="text" maxlength="100" id="siteTypeName" class="form-control" autocomplete="off" placeholder="Enter Site Type Name" />
				</div>
				<span id="siteTypeNameError"></span>
			</div>
			<div class="form-group">
				<label class="form-label">Type</label>
				<div class="input-group">
					<span class="input-group-text"><i class="cp-activity-type"></i></span>
					<select class="form-select-modal" data-placeholder="Select Type" id="siteTypeDropdown">
						<option value=""></option>
						<option value="Primary">Primary</option>
						<option value="DR">DR</option>
						<option value="Custom DR">Custom DR </option>
					</select>
				</div>

				<span id="siteTypeError"></span>
			</div>
			<div class="form-group">
				<div class="d-flex mt-4 gap-2">
				</div>
				<span id="Icon-error"></span>
			</div>
		</div>
		<div class="modal-footer d-flex justify-content-between">
			<small class="text-secondary ModalFooter-Note-Text"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
			<div class="gap-2 d-flex">
				<button type="button" id="cancelFunction" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
				<button type="button" class="btn btn-primary btn-sm" id="siteTypeSaveBtn">Save</button>
			</div>
		</div>
	</form>
</div>

@section Scripts
{
	@{
		<partial name="_ValidationScriptsPartial" />
	}
}