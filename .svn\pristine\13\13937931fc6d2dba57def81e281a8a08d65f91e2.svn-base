﻿using ContinuityPatrol.Domain.ViewModels.UserGroupModel;

namespace ContinuityPatrol.Application.Features.UserGroup.Queries.GetDetail;

public class GetUserGroupDetailQueryHandler : IRequestHandler<GetUserGroupDetailQuery, UserGroupListVm>
{
    private readonly IMapper _mapper;
    private readonly IUserGroupRepository _usergroupeRepository;

    public GetUserGroupDetailQueryHandler(IUserGroupRepository usergroupeRepository, IMapper mapper)
    {
        _usergroupeRepository = usergroupeRepository;
        _mapper = mapper;
    }

    public async Task<UserGroupListVm> Handle(GetUserGroupDetailQuery request, CancellationToken cancellationToken)
    {
        var usergroup = await _usergroupeRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(usergroup, nameof(Domain.Entities.UserGroup),
            new NotFoundException(nameof(Domain.Entities.UserGroup), request.Id));

        var UserGroupDetailDto = _mapper.Map<UserGroupListVm>(usergroup);

        return UserGroupDetailDto ?? throw new NotFoundException(nameof(UserGroup), request.Id);
    }
}