$(async function () {

    //SaveAs
    $("#saveAsReplicationsData").on("click", function () {
        clearSaveAsErrorMessage();
        flagEdit = true;
        $("#totalCloneReplication").text(0);
        $("#saveAllReplication").css({ "opacity": "0.5", "pointer-events": "none" });
        $("#cloneDataTable").empty();
        $("#cloneTable").hide();

        getReplicationDataForSaveAsAndClone($("#saveAsReplicationCloneName"));
        getReplicationTypeSaveAs();
        getSiteNamesSaveAs();
    });

    $("#saveAsReplicationCloneName").on("change", async function () {
        commonValidationReplication($(this).val(), "Select replication name", "saveAsReplicationCloneNameError");
        clonedReplicationRowData = await getReplicationData($(this).val());
    });

    $("#saveAsReplicationType").on("change", function () {
        const selectedText = $(this).find(":selected").text().toLowerCase();
        const selectedValue = $(this).val();
        const isPerpetuuiti = selectedText.includes("perpetuuiti");
        $("#showHideLicenseField").toggleClass("d-none", !isPerpetuuiti);
        if (isPerpetuuiti) {
            getLicenseWithCountSaveAs();
        }
        commonValidationReplication(selectedValue, "Select replication type", "saveAsReplicationTypeError");

        //commonValidationReplication($("#saveAsReplicationType").val(), "Select replication type", "saveAsReplicationTypeError");
        //if ($("#saveAsReplicationType :selected").text().toLowerCase().includes("perpetuuiti")) {
        //    $("#showHideLicenseField").removeClass("d-none");
        //    getLicenseWithCountSaveAs();
        //} else {
        //    $("#showHideLicenseField").addClass("d-none");
        //}
    });

    $("#saveAsReplicationSiteName").on("change", function () {
        commonValidationReplication($(this).val(), "Select site name", "saveAsReplicationSiteNameError");
        getLicenseWithCountSaveAs();
    });

    $("#saveAsLicenseKey").on("change", function () {
        commonValidationReplication($(this).val(), "Select license key", "saveAsLicenseKeyError");
    });

    $("#saveAsReplicationName").on("keyup", function () {
        InfraNameValidation($(this).val(), "", replicationURL.isReplicationNameExist,
            $("#saveAsReplicationNameError"), "Enter mirror replication name", 'Special characters not allowed', 'replicationname')
    });

    $("#addSaveAsData").on("click", async function () {
        let rowCount = $("#cloneDataTable tr").length;
        let replicationProps = clonedReplicationRowData?.properties;
        let saveAsReplicationCloneName = $("#saveAsReplicationCloneName").val();
        let saveAsReplicationType = $("#saveAsReplicationType").val();
        let saveAsReplicationTypeName = $("#saveAsReplicationType :selected").text();
        let saveAsReplicationName = $("#saveAsReplicationName").val();
        let saveAsReplicationSite = $("#saveAsReplicationSiteName").val();
        let saveAsReplicationSiteName = $("#saveAsReplicationSiteName :selected").text();
        let saveAsReplicationLicense = $("#saveAsLicenseKey").val();
        let saveAsReplicationLicenseName = $("#saveAsLicenseKey :selected").text();

        let validateReplicationType = commonValidationReplication(saveAsReplicationType, "Select replication type", "saveAsReplicationTypeError");
        let validateReplicationCloneName = commonValidationReplication(saveAsReplicationCloneName, "Select replication name", "saveAsReplicationCloneNameError");
        let validateReplicationName = await InfraNameValidation(saveAsReplicationName, "", replicationURL.isReplicationNameExist,
            $("#saveAsReplicationNameError"), "Enter mirror replication name", 'Special characters not allowed', 'replicationname');
        let validateReplicationSiteName = commonValidationReplication(saveAsReplicationSite, "Select site name", "saveAsReplicationSiteNameError");
        let validateReplicationLicense = true;

        if ($("#addSaveAsData").hasClass("cp-update")) {
            flagEdit = true;
            $("#addSaveAsData").addClass("cp-circle-plus").removeClass("cp-update").prop("title", "Add");
        }

        if ($("#saveAsReplicationType :selected").text().toLowerCase().includes("perpetuuiti")) {
            validateReplicationLicense = commonValidationReplication(saveAsReplicationLicense, "Select license key", "saveAsLicenseKeyError");
        }

        if (validateReplicationType && validateReplicationCloneName && validateReplicationName
            && validateReplicationSiteName && validateReplicationLicense) {
            $("#cloneTable").show();

            if (cloneReplicationSlNo > 0) {
                $("#cloneTable tbody tr").each(function () {
                    const row = $(this);
                    const cellWithSerialNumber = row.find("td:first");

                    if (cellWithSerialNumber.text().trim() === String(cloneReplicationSlNo)) {
                        row.find("td").eq(1).text($("#saveAsReplicationName").val());
                        row.find("td").eq(2).text($("#saveAsReplicationType :selected").text());
                        row.find("td").eq(3).text($("#saveAsReplicationSiteName :selected").text());
                        row.find("td").eq(4).text($("#saveAsLicenseKey :selected").text() || "-");

                        // Update the content or attributes in the 6th column
                        const cell = row.find("td").eq(6);

                        // Update the attributes of the "cloneEditButton"
                        const editButton = cell.find(".cloneEditButton");
                        editButton.attr("data-replname", $("#saveAsReplicationName").val());
                        editButton.attr("data-typeid", $("#saveAsReplicationType").val());
                        editButton.attr("data-siteid", $("#saveAsReplicationSiteName").val());
                        editButton.attr("data-licensekeyid", $("#saveAsLicenseKey").val());

                        // update the delete button if needed
                        const deleteButton = cell.find(".saveAsDeleteButton");
                        deleteButton.attr("data-replname", $("#saveAsReplicationName").val());

                        let selectedReplication = clonedReplicationLists.replictionListCommands[cloneReplicationSlNo - 1];
                        Object.assign(selectedReplication, {
                            Name: saveAsReplicationName,
                            SiteId: saveAsReplicationSite,
                            SiteName: saveAsReplicationSiteName,
                            TypeId: saveAsReplicationType,
                            Type: saveAsReplicationTypeName,
                            Properties: replicationProps,
                            LicenseId: saveAsReplicationLicense,
                            LicenseKey: saveAsReplicationLicenseName || "NA"
                        });
                        return false;
                    }
                });
            } else {
                clonedReplicationLists.ReplicationId = saveAsReplicationCloneName;
                clonedReplicationLists.replictionListCommands.push({
                    "Name": saveAsReplicationName,
                    "SiteId": saveAsReplicationSite,
                    "SiteName": saveAsReplicationSiteName,
                    "TypeId": saveAsReplicationType,
                    "Type": saveAsReplicationTypeName,
                    "Properties": replicationProps,
                    "LicenseId": saveAsReplicationLicense,
                    "LicenseKey": saveAsReplicationLicenseName || "NA"
                });

                $("#totalCloneReplication").text(rowCount + 1);
                $("#cloneDataTable").append(`<tr>
                            <td>${rowCount + 1}</td>
                            <td>${saveAsReplicationName}</td>
                            <td class="text-truncate">${saveAsReplicationSiteName}</td>
                            <td>${saveAsReplicationTypeName}</td>
                            <td>${saveAsReplicationLicenseName || "-"}</td>
                            <td><span class="text-warning iconClass"><i class="cp-pending"></i></span></td>
                            <td>
                              <div class="d-flex align-items-center gap-2">
                                  <span role="button" title="Edit" class="cloneEditButton" data-replname="${saveAsReplicationName}"
                                    data-siteid="${saveAsReplicationSite}" data-typeid="${saveAsReplicationType}"
                                    data-licensekeyid="${saveAsReplicationLicense}" data-slno="${rowCount + 1}" >
                                    <i class="cp-edit"></i>
                                  </span>
                                  <span role="button" title="Delete" data-slno="${rowCount + 1}" data-replname="${saveAsReplicationName}" 
                                       class="button saveAsDeleteButton" data-bs-toggle="modal" data-bs-target="#deleteModalSaveAs">
                                    <i class="cp-Delete"></i>
                                  </span>
                             </div>
                           </td>
                         </tr>`);
            }
            cloneReplicationSlNo = 0;
            $("#saveAllReplication").css({ "opacity": "1.0", "pointer-events": "" });
            $("#saveAsReplicationName").val("");
            $("#saveAsReplicationType, #saveAsReplicationSiteName, #saveAsLicenseKey").val("").trigger("change");
            $('#saveAsLicenseKeyError, #saveAsReplicationTypeError, #saveAsReplicationSiteNameError').text('').removeClass('field-validation-error');
        }
    });

    $("#saveAllReplication").on("click", async function () {
        await $.ajax({
            type: "POST",
            url: RootUrl + replicationURL.SaveAllReplication,
            dataType: "json",
            headers: {
                'RequestVerificationToken': await gettoken()
            },
            data: { savelAllReplicationCommand: clonedReplicationLists },
            success: function (result) {
                clonedReplicationLists = {
                    "ReplicationId": "",
                    "replictionListCommands": []
                };
                if (result?.success) {
                    if (result?.data?.success) {
                        let resultData = result?.data;
                        notificationAlert("success", resultData?.message);

                        //Comment this after added style in modal.                       
                        setTimeout(() => {
                            window.location.reload(); //Incase if change this var value won't change.
                            dataTableCreateAndUpdate($("#saveButton"), dataTable, $("#searchInTypeReplication"), $("#ddlReplicationTypeID :selected").val());
                        }, 2000);
                    } else {
                        errorNotification(result?.data);
                    }                    
                    $("#saveasModal").modal("hide");
                } else {
                    errorNotification(result);                   
                    $("#saveasModal").modal("hide");
                }
            },
        });
    });

    $("#cloneTable").on("click", ".cloneEditButton", function () {
        $("#saveAsReplicationName").val($(this).attr("data-replname"));
        $("#saveAsReplicationType").val($(this).attr("data-typeid")).trigger("change");
        $("#saveAsReplicationSiteName").val($(this).attr("data-siteid")).trigger("change");

        if ($(this).attr("data-licensekeyid")) {
            getLicenseWithCountSaveAs($(this).attr("data-licensekeyid"));
            //$("#saveAsLicenseKey").val($(this).attr("data-licensekeyid")).trigger("change");
        }
        cloneReplicationSlNo = $(this).attr("data-slno");
        flagEdit = false;
        $("#addSaveAsData").removeClass("cp-circle-plus").addClass("cp-update").prop("title", "Update");
    });

    $("#cloneTable").on("click", ".saveAsDeleteButton", function () {
        deleteSaveAsReplRow = $(this).data("slno");
        $("#deleteCloneReplicationData").text($(this).data("saveAsReplicationName"));
    });

    $("#deleteSaveAsRow").on("click", async function () {
        $("#cloneTable tbody tr").each(function () {
            const row = $(this);
            const cellWithSerialNumber = row.find("td:first");

            if (cellWithSerialNumber.text().trim() === String(deleteSaveAsReplRow)) {
                row.remove();
                let index = Number(cellWithSerialNumber.text().trim())
                let indexToRemove = index - 1;

                if (indexToRemove >= 0 && indexToRemove < clonedReplicationLists.replictionListCommands.length) {
                    clonedReplicationLists.replictionListCommands.splice(indexToRemove, 1);
                }
                return false;
            }
        });
        $("#totalCloneReplication").text($("#totalCloneReplication").text() - 1);
        $("#saveasModal").modal("show");

        if ($("#cloneDataTable tr").length === 0) {
            $("#cloneTable").hide();
            $("#saveAllReplication").css({ "opacity": "0.5", "pointer-events": "none" });
        }
    });

    $("#cancelDelete").on("click", function () {
        $("#saveasModal").modal("show");
    });

    //Clone
    $("#cloneReplicationData").on("click", function () {
        $("#cloneReplicationModal").modal("show");
        $('#cloneReplication, #inputCloneReplication').val("");
        $('#cloneReplicationError, #inputCloneReplicationError').text("").removeClass("field-validation-error");
        getReplicationDataForSaveAsAndClone($("#cloneReplication"))
    })

    $('#cloneReplicationButton').on("click", async function () {
        const $selectReplicationName = $('#cloneReplication');
        const $cloneReplicationName = $('#inputCloneReplication');

        let replicationNameValidate = commonValidationReplication($selectReplicationName.val(), "Select replication name", "cloneReplicationError");
        let cloneReplicationNameValidate = await InfraNameValidation($cloneReplicationName.val(), "", replicationURL.isReplicationNameExist,
            $("#inputCloneReplicationError"), "Enter clone replication name", 'Special characters not allowed', 'replicationname');

        if (replicationNameValidate && cloneReplicationNameValidate) {
            async function saveAs() {
                await $.ajax({
                    type: "POST",
                    url: RootUrl + replicationURL.saveAsReplication,
                    dataType: "json",
                    headers: {
                        'RequestVerificationToken': await gettoken()
                    },
                    data: { saveAsReplicationCommand: { "ReplicationId": $('#replicationID').val(), "Name": $cloneReplicationName.val() } },
                    success: function (result) {
                        if (result.success) {
                            if (result?.data?.success) {
                                let resultData = result?.data;
                                notificationAlert("success", resultData?.message);
                                $('#replicationID').val("");

                                //Comment this after added style in modal.                              
                                setTimeout(() => {
                                    let selectedReplType = $("#ddlReplicationTypeID :selected").val();
                                    //window.location.reload(); //Incase if change this var value won't change.
                                    dataTableCreateAndUpdate($("#saveButton"), dataTable, $("#searchInTypeReplication"), selectedReplType);
                                }, 2000);

                            } else {
                                errorNotification(result?.data);
                            }
                            $("#cloneReplicationModal").modal("hide");

                        } else {
                            errorNotification(result)
                            $("#cloneReplicationModal").modal("hide");
                        }
                    },
                });
            }
            saveAs();
        }
    });

    $('#cloneReplication').on('change', function () {
        const $replicationName = $(this);
        $('#replicationID').val($replicationName.val());
        commonValidationReplication($replicationName.val(), "Select replication name", "cloneReplicationError");
    });

    $('#inputCloneReplication').on('input', commonDebounce(function () {

        //CommonFunctions.js InfraNameValidation
        InfraNameValidation($(this).val(), "", replicationURL.isReplicationNameExist,
            $("#inputCloneReplicationError"), "Enter clone replication name", 'Special characters not allowed', 'replicationname');
    }));
})

async function getReplicationDataForSaveAsAndClone(selectreplication) {
    await $.ajax({
        type: "GET",
        url: RootUrl + replicationURL.getReplicationNames,
        dataType: "json",
        success: function (result) {
            if (result.success) {
                let response = result?.data;
                if (response && (Array.isArray(response) && response.length > 0)) {
                    selectreplication.empty().append($('<option>').val("").text(""));
                    let options = [];
                    response.forEach(function (item) {
                        options.push($('<option>').val(item.id).text(item.name));
                    });
                    selectreplication.append(options);
                }
            } else {
                errorNotification(result);
            }
        }
    })
}

async function getReplicationTypeSaveAs() {
    await $.ajax({
        url: RootUrl + replicationURL.replicationTypes,
        method: 'GET',
        dataType: 'json',
        data: { name: 'replication' },
        success: function (response) {
            if (response.success) {
                var distinctNames = response?.data?.map(function (item) {
                    var properties = JSON.parse(item?.properties); // Parse the properties string to an object
                    return {
                        name: item?.formTypeName,
                        logo: item?.logo,
                        id: item?.formTypeId
                    };
                });
                let replicationType = $('#saveAsReplicationType');
                let options = [];
                replicationType.empty().append($('<option>').val('').text(''));

                distinctNames?.forEach((d) => {
                    options.push($('<option>').val(d?.id).text(d?.name).attr('replicationid', d?.id).attr('replicationLogo', d?.logo));
                });

                replicationType.append(options);
            } else {
                errorNotification(response);
            }
        }
    });
}

async function getSiteNamesSaveAs() {
    await $.ajax({
        url: RootUrl + replicationURL.siteList,
        method: 'GET',
        dataType: 'json',
        success: function (result) {
            if (result.success && (Array.isArray(result?.data) && result?.data.length > 0)) {
                const sortedData = result?.data?.sort((a, b) => a?.name.toLowerCase().localeCompare(b?.name.toLowerCase()));
                let siteNames = $('#saveAsReplicationSiteName');
                let options = [];
                siteNames.empty().append($('<option>').val('').text('Select Site'));

                sortedData?.forEach(function (item) {
                    options.push($('<option>').val(item?.id).text(item?.name))
                });

                siteNames.append(options);
            } else {
                errorNotification(result);
            }
        }
    });
}

async function getLicenseWithCountSaveAs(value = null) {
    if ($("#saveAsReplicationType :selected").text().toLowerCase().includes("perpetuuiti") && $('#saveAsReplicationSiteName').val()) {
        //$("#information").html("");
        $("#saveAsLicenseKeyError").text("").removeClass("field-validation-error");

        await $.ajax({
            type: "GET",
            url: RootUrl + 'Admin/LicenseManager/GetLicensesNamesWithCount',
            dataType: "json",
            data: { type: "replication", roleType: "", siteId: $('#saveAsReplicationSiteName').val(), serverId: "", replicationType: $("#saveAsReplicationType :selected").text() },
            success: function (result) {
                if (result?.success) {
                    let licensesNameLists = result?.data;

                    if (licensesNameLists && (Array.isArray(licensesNameLists) && licensesNameLists.length > 0)) {
                        let license = $('#saveAsLicenseKey');
                        let options = [];
                        license.empty().append($('<option>').val("").text("Select License Key"));
                        licensesNameLists.forEach(function (item) {
                            options.push($('<option>').val(item?.id)
                                .text(`${item?.poNumber || ''}`)
                                .attr('remainingcount', item?.remainingCount || '0')
                                .attr('licenseIsApplicable', item?.licenseIsApplicable))
                        });
                        license.append(options);

                        if (value) {
                            license.val(value).trigger('change');
                        }
                    }

                } else {
                    errorNotification(result)
                }
            },
        });
    }
}

function getReplicationData(replid) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + replicationURL.getByReplicationReferenceId,
            method: 'GET',
            dataType: 'json',
            data: { id: replid },
            success: function (result) {
                if (result.success) {
                    resolve(result.data);
                } else {
                    errorNotification(result);
                }
            }
        })
    });
}