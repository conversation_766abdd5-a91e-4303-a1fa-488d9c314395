﻿using ContinuityPatrol.Application.Features.AlertMaster.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertMaster.Commands;

public class CreateAlertMasterTests : IClassFixture<AlertMasterFixture>
{
    private readonly AlertMasterFixture _alertMasterFixture;
    private readonly Mock<IAlertMasterRepository> _mockAlertMasterRepository;
    private readonly CreateAlertMasterCommandHandler _handler;

    public CreateAlertMasterTests(AlertMasterFixture alertMasterFixture)
    {
        _alertMasterFixture = alertMasterFixture;

        var mockPublisher = new Mock<IPublisher>();

        //var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        _mockAlertMasterRepository = AlertMasterRepositoryMocks.CreateAlertMasterRepository(_alertMasterFixture.AlertMasters);

        _handler = new CreateAlertMasterCommandHandler(_mockAlertMasterRepository.Object, _alertMasterFixture.Mapper, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_IncreaseAlertMasterCount_When_AlertMasterCreated()
    {
        await _handler.Handle(_alertMasterFixture.CreateAlertMasterCommand, CancellationToken.None);

        var allCategories = await _mockAlertMasterRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_alertMasterFixture.AlertMasters.Count);
    }

    //[Fact]
    //public async Task Handle_Return_CreateAlertMasterResponse_When_AlertMasterCreated()
    //{
    //    var result = await _handler.Handle(_alertMasterFixture.CreateAlertMasterCommand, CancellationToken.None);

    //    result.ShouldBeOfType(typeof(CreateAlertMasterResponse));

    //    result.AlertMasterId.ShouldBeGreaterThan(0.ToString());

    //    result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    //}

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_alertMasterFixture.CreateAlertMasterCommand, CancellationToken.None);

        _mockAlertMasterRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.AlertMaster>()), Times.Once);
    }
}