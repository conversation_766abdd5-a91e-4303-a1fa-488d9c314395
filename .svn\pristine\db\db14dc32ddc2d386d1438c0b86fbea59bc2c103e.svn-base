using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DriftEventRepositoryTests
{
    private readonly ApplicationDbContext _dbContext;
    private readonly DriftEventRepository _repository;

    public DriftEventRepositoryTests()
    {
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DriftEventRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var driftEvent = new DriftEvent
        {
            ReferenceId = Guid.NewGuid().ToString(),
            EntityName = "TestEvent",
            InfraObjectId = "INFRA_123",
            EntityStatus = "Active",
            IsActive = true,
            CreatedDate = DateTime.Now
        };

        // Act
        var result = await _repository.AddAsync(driftEvent);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftEvent.EntityName, result.EntityName);
        Assert.Single(_dbContext.DriftEvents);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var driftEvent = new DriftEvent
        {
            ReferenceId = Guid.NewGuid().ToString(),
            EntityName = "ExistingEvent",
            IsActive = true
        };
        await _repository.AddAsync(driftEvent);

        // Act
        var result = await _repository.IsNameExist("ExistingEvent", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Act
        var result = await _repository.IsNameExist("NonExistentEvent", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetStartDateAndEndDate Tests

    [Fact]
    public async Task GetStartDateAndEndDate_ShouldReturnEventsInDateRange()
    {
        // Arrange
        var startDate = DateTime.Now.AddDays(-5);
        var endDate = DateTime.Now.AddDays(-1);
        
        var events = new List<DriftEvent>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event1", IsActive = true, CreatedDate = startDate.AddDays(1) },
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event2", IsActive = true, CreatedDate = startDate.AddDays(2) },
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event3", IsActive = true, CreatedDate = DateTime.Now.AddDays(-10) }
        };
        _dbContext.DriftEvents.AddRange(events);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetStartDateAndEndDate(startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= startDate.Date && x.CreatedDate.Date <= endDate.Date));
    }

    #endregion

    #region GetInfraObjectIdByStatus Tests

    [Fact]
    public async Task GetInfraObjectIdByStatus_ShouldReturnFilteredEvents()
    {
        // Arrange
        var startDate = DateTime.Now.AddDays(-5);
        var endDate = DateTime.Now.AddDays(-1);
        var infraObjectId = "INFRA_123";
        
        var events = new List<DriftEvent>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event1", InfraObjectId = infraObjectId, IsActive = true, CreatedDate = startDate.AddDays(1) },
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event2", InfraObjectId = "OTHER_INFRA", IsActive = true, CreatedDate = startDate.AddDays(1) },
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event3", InfraObjectId = infraObjectId, IsActive = true, CreatedDate = DateTime.Now.AddDays(-10) }
        };
        _dbContext.DriftEvents.AddRange(events);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetInfraObjectIdByStatus(startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"), infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.All(result, x => Assert.Equal(infraObjectId, x.InfraObjectId));
    }

    [Fact]
    public async Task GetInfraObjectIdByStatus_ShouldReturnAllEvents_WhenInfraObjectIdIsAll()
    {
        // Arrange
        var startDate = DateTime.Now.AddDays(-5);
        var endDate = DateTime.Now.AddDays(-1);
        
        var events = new List<DriftEvent>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event1", InfraObjectId = "INFRA_123", IsActive = true, CreatedDate = startDate.AddDays(1) },
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event2", InfraObjectId = "INFRA_456", IsActive = true, CreatedDate = startDate.AddDays(1) }
        };
        _dbContext.DriftEvents.AddRange(events);
        _dbContext.SaveChanges();   

        // Act
        var result = await _repository.GetInfraObjectIdByStatus(startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"), "all");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
    }

    #endregion

    #region GetStartDateEndDateAndInfraObjectIdAndStatus Tests

    [Fact]
    public async Task GetStartDateEndDateAndInfraObjectIdAndStatus_ShouldReturnFilteredEvents()
    {
        // Arrange
        var startDate = DateTime.Now.AddDays(-5);
        var endDate = DateTime.Now.AddDays(-1);
        var infraObjectId = "INFRA_123";
        var status = "Active";
        
        var events = new List<DriftEvent>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event1", InfraObjectId = infraObjectId, EntityStatus = status, IsActive = true, CreatedDate = startDate.AddDays(1) },
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event2", InfraObjectId = infraObjectId, EntityStatus = "Inactive", IsActive = true, CreatedDate = startDate.AddDays(1) },
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event3", InfraObjectId = "OTHER_INFRA", EntityStatus = status, IsActive = true, CreatedDate = startDate.AddDays(1) }
        };
        _dbContext.DriftEvents.AddRange(events);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetStartDateEndDateAndInfraObjectIdAndStatus(
            startDate.ToString("yyyy-MM-dd"), 
            endDate.ToString("yyyy-MM-dd"), 
            infraObjectId, 
            status);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.All(result, x => Assert.Equal(infraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.Equal(status, x.EntityStatus));
    }

    [Fact]
    public async Task GetStartDateEndDateAndInfraObjectIdAndStatus_ShouldReturnAllEvents_WhenFiltersAreAll()
    {
        // Arrange
        var startDate = DateTime.Now.AddDays(-5);
        var endDate = DateTime.Now.AddDays(-1);
        
        var events = new List<DriftEvent>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event1", InfraObjectId = "INFRA_123", EntityStatus = "Active", IsActive = true, CreatedDate = startDate.AddDays(1) },
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event2", InfraObjectId = "INFRA_456", EntityStatus = "Inactive", IsActive = true, CreatedDate = startDate.AddDays(1) }
        };
        _dbContext.DriftEvents.AddRange(events);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetStartDateEndDateAndInfraObjectIdAndStatus(
            startDate.ToString("yyyy-MM-dd"), 
            endDate.ToString("yyyy-MM-dd"), 
            "all", 
            "all");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var events = new List<DriftEvent>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event1", IsActive = true, CreatedDate = DateTime.Now },
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event2", IsActive = true, CreatedDate = DateTime.Now },
            new() { ReferenceId = Guid.NewGuid().ToString(), EntityName = "Event3", IsActive = true, CreatedDate = DateTime.Now }
        };
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(events);
        var initialCount = events.Count;
        
        var toUpdate = events.Take(2).ToList();
        toUpdate.ForEach(x => x.EntityName = "UpdatedEvent");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = events.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.EntityName == "UpdatedEvent").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
