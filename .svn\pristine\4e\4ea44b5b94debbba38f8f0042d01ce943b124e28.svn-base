﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ComponentType.Events.Delete;

public class ComponentTypeDeletedEventHandler : INotificationHandler<ComponentTypeDeletedEvent>
{
    private readonly ILogger<ComponentTypeDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ComponentTypeDeletedEventHandler(ILoggedInUserService userService,
        ILogger<ComponentTypeDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ComponentTypeDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.ComponentType.ToString(),
            Action = $"{ActivityType.Delete} {Modules.ComponentType}",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"ComponentType '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"ComponentType '{deletedEvent.Name}' deleted successfully.");
    }
}