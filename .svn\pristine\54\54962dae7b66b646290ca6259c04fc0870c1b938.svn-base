﻿using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Application.Features.Company.Commands.Create;
using ContinuityPatrol.Application.Features.Company.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Domain.ViewModels.FiaCostModel;
using NPOI.SS.Formula.Functions;
using ContinuityPatrol.Application.Features.FiaCost.Commands.Create;
using ContinuityPatrol.Application.Features.FiaCost.Commands.Update;
using ContinuityPatrol.Application.Features.FiaCost.Queries.GetPaginatedList;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

  [Area("Configuration")]

    public class FIACostController : Controller
    {

    private readonly ILogger<CompanyController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly IPublisher _publisher;

    public FIACostController(IMapper mapper, ILogger<CompanyController> logger, IPublisher publisher, IDataProvider dataProvider)
    {
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
        _publisher = publisher;
    }

    [AntiXss]
    public IActionResult List()
        {
            return View();
        }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetFiaCostPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in FIA Cost");

        try
        {
            var paginationList = await _dataProvider.FiaCost.GetPaginatedFiaCosts(query);
            _logger.LogDebug("Successfully retrieved pagination list for FIA Cost");
            return Json(new { Success = true, data = paginationList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on report schedule page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetFiaTemplateList()
    {
        _logger.LogDebug("Entering IsCompanyNameExist method in Company");
        try
        {
            _logger.LogDebug("Returning result for IsCompanyNameExist on company");

            var getTemplateList = await _dataProvider.FiaTemplate.GetFiaTemplateList();

            return Json(new { Success = true, data = getTemplateList });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on company while checking if company name exists for : .", ex);

            return ex.GetJsonException();
        }
    }



    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> CreateOrUpdate(FiaCostViewModel FIACostModal)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in Company");
        try
        {
           
            var FIACostId = Request.Form["Id"].ToString();

          //  _logger.LogDebug($"Company Logo '{company.CompanyLogo}' is set in Company");

            if (FIACostId.IsNullOrWhiteSpace())
            {


                 var FIACommand = _mapper.Map<CreateFiaCostCommand>(FIACostModal);

                //_logger.LogDebug($"Creating Company '{company.Name}'");

                var result = await _dataProvider.FiaCost.CreateAsync(FIACommand);

                return Json(new { Success = true, data = result });

            }
            else
            {
               

                var FIACommand = _mapper.Map<UpdateFiaCostCommand>(FIACostModal);

              //  _logger.LogDebug($"Updating Company '{company.Name}'");

                var result = await _dataProvider.FiaCost.UpdateAsync(FIACommand);

                return Json(new { Success = true, data = result });

            }
           // _logger.LogDebug("CreateOrUpdate operation completed successfully in FIA Cost, returning view.");

            
        }      
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on company while checking if company name exists for : .", ex);

            return ex.GetJsonException();
        }
    }

}

