﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class IncidentLogsRepository:BaseRepository<IncidentLogs>, IIncidentLogsRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public IncidentLogsRepository(ApplicationDbContext dbContext,ILoggedInUserService loggedInUserService):base(dbContext,loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<List<IncidentLogs>> GetIncidentLogsByIncidentNumber(string incidentNumber)
    {
        throw new NotImplementedException();
    }

    public Task<List<IncidentLogs>> GetIncidentLogsByInfraObjectId(string infraObjectId)
    {
        throw new NotImplementedException();
    }
}
