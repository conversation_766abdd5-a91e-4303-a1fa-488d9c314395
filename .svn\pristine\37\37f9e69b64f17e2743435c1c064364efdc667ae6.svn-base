namespace ContinuityPatrol.Application.Features.DataLag.Commands.Update;

public class UpdateDataLagCommand : IRequest<UpdateDataLagResponse>
{
    public string Id { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public int TotalBusinessFunction { get; set; }
    public int BFAvailable { get; set; }
    public int BFImpact { get; set; }
    public int BFExceed { get; set; }
    public int BFUnConfigured { get; set; }
    public int BFNotAvailable { get; set; }
    public int BFThreshold { get; set; }
    public int TotalInfraObject { get; set; }
    public int InfraAvailable { get; set; }
    public int InfraImpact { get; set; }
    public int InfraExceed { get; set; }
    public int InfraNotAvailable { get; set; }
    public int InfraThreshold { get; set; }
    public int InfraUnderConfigured { get; set; }
}