using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class BusinessFunctionRepositoryTests : IClassFixture<BusinessFunctionFixture>
{
    private readonly BusinessFunctionFixture _businessFunctionFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BusinessFunctionRepository _repository;

    public BusinessFunctionRepositoryTests(BusinessFunctionFixture businessFunctionFixture)
    {
        _businessFunctionFixture = businessFunctionFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BusinessFunctionRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;

        // Act
        var result = await _repository.AddAsync(businessFunction);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessFunction.Name, result.Name);
        Assert.Equal(businessFunction.BusinessServiceId, result.BusinessServiceId);
        Assert.Single(_dbContext.BusinessFunctions);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _repository.AddAsync(businessFunction);

        businessFunction.Name = "UpdatedName";
        businessFunction.Description = "UpdatedDescription";
        businessFunction.CriticalityLevel = "High";

        // Act
        var result = await _repository.UpdateAsync(businessFunction);

        // Assert
        Assert.Equal("UpdatedName", result.Name);
        Assert.Equal("UpdatedDescription", result.Description);
        Assert.Equal("High", result.CriticalityLevel);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _repository.AddAsync(businessFunction);

        // Act
        var result = await _repository.DeleteAsync(businessFunction);

        // Assert
        Assert.Equal(businessFunction.Name, result.Name);
        Assert.Empty(_dbContext.BusinessFunctions);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        var addedEntity = await _repository.AddAsync(businessFunction);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _repository.AddAsync(businessFunction);

        // Act
        var result = await _repository.GetByReferenceIdAsync(businessFunction.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessFunction.ReferenceId, result.ReferenceId);
        Assert.Equal(businessFunction.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        await _repository.AddRangeAsync(businessFunctions);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessFunctions.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsBusinessFunctionNameExist Tests

    [Fact]
    public async Task IsBusinessFunctionNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.Name = "ExistingName";
        await _repository.AddAsync(businessFunction);

        // Act
        var result = await _repository.IsBusinessFunctionNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        await _repository.AddRangeAsync(businessFunctions);

        // Act
        var result = await _repository.IsBusinessFunctionNameExist("NonExistentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.Name = "SameName";
        await _repository.AddAsync(businessFunction);

        // Act
        var result = await _repository.IsBusinessFunctionNameExist("SameName", businessFunction.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsBusinessFunctionNameUnique Tests

    [Fact]
    public async Task IsBusinessFunctionNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.Name = "UniqueName";
        await _repository.AddAsync(businessFunction);

        // Act
        var result = await _repository.IsBusinessFunctionNameUnique("UniqueName");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsBusinessFunctionNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        await _repository.AddRangeAsync(businessFunctions);

        // Act
        var result = await _repository.IsBusinessFunctionNameUnique("NonExistentName");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionList;
        var businessFunction1 = businessFunction[0];
        var businessFunction2 = businessFunction[1];
        // Act
        var task1 = _repository.AddAsync(businessFunction1);
        var task2 = _repository.AddAsync(businessFunction2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.BusinessFunctions.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(businessFunctions);
        var initialCount = businessFunctions.Count;

        var toUpdate = businessFunctions.Take(2).ToList();
        toUpdate.ForEach(x => x.CriticalityLevel = "Critical");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = businessFunctions.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.CriticalityLevel == "Critical").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleNullParametersGracefully()
    {
        // Act & Assert
        var result1 = await _repository.IsBusinessFunctionNameExist(null, "valid-guid");
        var result2 = await _repository.IsBusinessFunctionNameExist("TestName", null);
        var result3 = await _repository.IsBusinessFunctionNameUnique(null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    [Fact]
    public async Task Repository_ShouldHandleCriticalityLevelFiltering()
    {
        // Arrange
        var businessFunctions = new List<BusinessFunction>
        {
            new BusinessFunction
            {
                Name = "Function1",
                CriticalityLevel = "Critical",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BusinessFunctionFixture.CompanyId,
                IsActive = true
            },
            new BusinessFunction
            {
                Name = "Function2",
                CriticalityLevel = "High",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BusinessFunctionFixture.CompanyId,
                IsActive = true
            },
            new BusinessFunction
            {
                Name = "Function3",
                CriticalityLevel = "Critical",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BusinessFunctionFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(businessFunctions);

        // Act
        var criticalFunctions = await _repository.FindByFilterAsync(x => x.CriticalityLevel == "Critical");
        var highFunctions = await _repository.FindByFilterAsync(x => x.CriticalityLevel == "High");

        // Assert
        Assert.Equal(2, criticalFunctions.Count);
        Assert.Single(highFunctions);
        Assert.All(criticalFunctions, x => Assert.Equal("Critical", x.CriticalityLevel));
        Assert.All(highFunctions, x => Assert.Equal("High", x.CriticalityLevel));
    }

    #endregion
}
