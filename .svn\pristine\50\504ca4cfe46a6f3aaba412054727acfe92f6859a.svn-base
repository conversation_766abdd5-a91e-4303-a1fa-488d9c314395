using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class BulkImportActionResultRepository : BaseRepository<BulkImportActionResult>, IBulkImportActionResultRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public BulkImportActionResultRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<BulkImportActionResult> GetByEntityIdAndBulkImportOperationId(string entityId, string bulkImportOperationId)
    {
        var result =  _dbContext.BulkImportActionResults.AsNoTracking().Active().Where(x => x.EntityId.Equals(entityId) && x.BulkImportOperationId.Equals(bulkImportOperationId));
        return await MapToBulkImportActionResult(result).FirstOrDefaultAsync();
    }

    public async Task<List<BulkImportActionResult>> GetByOperationIdsAndOperationGroupIds(List<string> operationIds, List<string> operationGroupIds)
    {
        var bulkImportActionResult = _dbContext.BulkImportActionResults.AsNoTracking().Active()
            .Where(x => operationIds.Contains(x.BulkImportOperationId) && operationGroupIds.Contains(x.BulkImportOperationGroupId)).OrderBy(x => x.Id);

        return await MapToBulkImportActionResult(bulkImportActionResult).ToListAsync();
    }
    public async Task<List<BulkImportActionResult>>GetByOperationIdAndOperationGroupId(string operationId, string operationGroupId)
    {
        var bulkImportActionResult =  _dbContext.BulkImportActionResults
            .AsNoTracking()
            .Active()
            .Where(x => x.BulkImportOperationId.Equals(operationId) && x.BulkImportOperationGroupId.Equals(operationGroupId))
            .OrderBy(x => x.Id);
            
            return await MapToBulkImportActionResult(bulkImportActionResult).ToListAsync();
    }

    public async Task<BulkImportActionResult> GetActionByOperationGroupIdAndEntityType(string operationGroupId,
        string entityType)
    {
       var result = base.FilterBy(x =>
            x.BulkImportOperationGroupId.Equals(operationGroupId) &&
            x.EntityType.ToLower().Equals(entityType.ToLower()));

        return await MapToBulkImportActionResult(result).FirstOrDefaultAsync();
    }

    private IQueryable<BulkImportActionResult> MapToBulkImportActionResult(IQueryable<BulkImportActionResult> query)
    {
        return query.Select(x => new BulkImportActionResult
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            CompanyId = x.CompanyId,
            NodeId = x.NodeId,
            NodeName = x.NodeName,
            BulkImportOperationId = x.BulkImportOperationId,
            BulkImportOperationGroupId = x.BulkImportOperationGroupId,
            EntityId = x.EntityId,
            EntityType = x.EntityType,
            EntityName = x.EntityName,
            ConditionalOperation = x.ConditionalOperation,
            Status = x.Status,
            ErrorMessage = x.ErrorMessage,
            StartTime = x.StartTime,
            EndTime = x.EndTime
        });
    }
    //public Task<bool> IsNameExist(string name, string id)
    //{
    //    return Task.FromResult(!id.IsValidGuid()
    //        ? Entities.Any(e => e.Name.Equals(name))
    //        : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    //}
}
