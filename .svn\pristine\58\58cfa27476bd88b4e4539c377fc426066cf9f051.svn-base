namespace ContinuityPatrol.Domain.Entities;

public class BulkImportActionResult : AuditableEntity
{
    public string CompanyId;
    public string NodeId { get; set; }
	public string NodeName { get; set; }
	public string BulkImportOperationId { get; set; }
	public string BulkImportOperationGroupId { get; set; }
	public string EntityId { get; set; }
	public string EntityName { get; set; }
	public string EntityType { get; set; }
	public int ConditionalOperation { get; set; }
    public string Status { get; set; }
	public DateTime StartTime { get; set; }
	public DateTime EndTime { get; set; }
    [Column(TypeName = "NCLOB")] public string ErrorMessage { get; set; }
		
}
