using AutoFixture;
using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceEvaluationModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class BusinessServiceEvaluationFixture : IDisposable
{
    public CreateBusinessServiceEvaluationCommand CreateBusinessServiceEvaluationCommand { get; set; }
    public UpdateBusinessServiceEvaluationCommand UpdateBusinessServiceEvaluationCommand { get; set; }
    public List<BusinessServiceEvaluationListVm> BusinessServiceEvaluationListVm { get; set; }
    public BusinessServiceEvaluationDetailVm BusinessServiceEvaluationDetailVm { get; set; }

    public BusinessServiceEvaluationFixture()
    {
        CreateBusinessServiceEvaluationCommand = AutoBusinessServiceEvaluationFixture.Create<CreateBusinessServiceEvaluationCommand>();
        UpdateBusinessServiceEvaluationCommand = AutoBusinessServiceEvaluationFixture.Create<UpdateBusinessServiceEvaluationCommand>();
        BusinessServiceEvaluationListVm = AutoBusinessServiceEvaluationFixture.CreateMany<BusinessServiceEvaluationListVm>(3).ToList();
        BusinessServiceEvaluationDetailVm = AutoBusinessServiceEvaluationFixture.Create<BusinessServiceEvaluationDetailVm>();
    }

    public Fixture AutoBusinessServiceEvaluationFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customize<CreateBusinessServiceEvaluationCommand>(c => c
                .With(b => b.BusinessServiceId, Guid.NewGuid().ToString)
                .With(b => b.BusinessServiceName, "Enterprise Customer Portal")
                .With(b => b.Description, "Comprehensive evaluation of customer portal performance and reliability")
                .With(b => b.Grade, "A")
                .With(b => b.GradeValue, "85"));

            fixture.Customize<UpdateBusinessServiceEvaluationCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.BusinessServiceId, Guid.NewGuid().ToString)
                .With(b => b.BusinessServiceName, "Updated Enterprise Customer Portal")
                .With(b => b.Description, "Updated comprehensive evaluation with improved metrics")
                .With(b => b.Grade, "A+")
                .With(b => b.GradeValue, "92"));

            fixture.Customize<BusinessServiceEvaluationListVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.BusinessServiceId, Guid.NewGuid().ToString)
                .With(b => b.BusinessServiceName, () => $"BusinessService-{fixture.Create<string>().Substring(0, 8)}")
                .With(b => b.Description, () => $"Description-{fixture.Create<string>().Substring(0, 15)}")
                .With(b => b.Grade, () => fixture.Create<bool>() ? "A" : "B")
                .With(b => b.GradeValue, () => (fixture.Create<int>() % 40 + 60).ToString()));

            fixture.Customize<BusinessServiceEvaluationDetailVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.BusinessServiceId, Guid.NewGuid().ToString)
                .With(b => b.BusinessServiceName, "Enterprise Financial Services Platform")
                .With(b => b.Description, "Comprehensive evaluation of financial services platform covering all critical aspects")
                .With(b => b.Grade, "A+")
                .With(b => b.GradeValue, "95"));
               

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
