﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class IncidentManagementRepositoryTests
    {
        private readonly ApplicationDbContext _dbContext;

        public IncidentManagementRepositoryTests()
        {
            _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        }
        public void Dispose()
        {
            _dbContext?.Dispose();
        }
        [Fact]
        public void Constructor_ShouldInitializeRepository()
        {
            // Arrange

            // Act
            var repository = new IncidentManagementRepository(_dbContext, DbContextFactory.GetMockUserService());

            // Assert
            Assert.NotNull(repository);
        }
    }
}
