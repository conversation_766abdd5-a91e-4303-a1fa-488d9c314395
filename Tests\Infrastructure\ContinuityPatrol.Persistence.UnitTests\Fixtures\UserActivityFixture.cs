using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class UserActivityFixture : IDisposable
{
    public List<UserActivity> UserActivityPaginationList { get; set; }
    public List<UserActivity> UserActivityList { get; set; }
    public UserActivity UserActivityDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public UserActivityFixture()
    {
        var fixture = new Fixture();

        UserActivityList = fixture.Create<List<UserActivity>>();

        UserActivityPaginationList = fixture.CreateMany<UserActivity>(20).ToList();

        UserActivityPaginationList.ForEach(x => x.CompanyId = CompanyId);

        UserActivityList.ForEach(x => x.CompanyId = CompanyId);

        UserActivityDto = fixture.Create<UserActivity>();

        UserActivityDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
