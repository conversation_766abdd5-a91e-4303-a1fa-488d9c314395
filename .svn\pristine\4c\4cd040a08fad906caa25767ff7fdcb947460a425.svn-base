﻿namespace ContinuityPatrol.Application.Features.MSSQLMonitorLogs.Queries.GetDetail;

public class
    GetMSSQLMonitorLogsDetailQueryHandler : IRequestHandler<GetMSSQLMonitorLogsDetailQuery, MSSQLMonitorLogsDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IMssqlMonitorLogsRepository _mssqlMonitorLogsRepository;

    public GetMSSQLMonitorLogsDetailQueryHandler(IMssqlMonitorLogsRepository mssqlMonitorLogsRepository, IMapper mapper)
    {
        _mssqlMonitorLogsRepository = mssqlMonitorLogsRepository;
        _mapper = mapper;
    }

    public async Task<MSSQLMonitorLogsDetailVm> Handle(GetMSSQLMonitorLogsDetailQuery request,
        CancellationToken cancellationToken)
    {
        var mssqlMonitorLogs = await _mssqlMonitorLogsRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(mssqlMonitorLogs, nameof(MSSQLMonitorLogs),
            new NotFoundException(nameof(MSSQLMonitorLogs), request.Id));

        var mssqlMonitorLogsDetailDto = _mapper.Map<MSSQLMonitorLogsDetailVm>(mssqlMonitorLogs);

        return mssqlMonitorLogsDetailDto ?? throw new NotFoundException(nameof(MSSQLMonitorLogs), request.Id);
    }
}