﻿using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Events.Withdraw;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Withdraw;

public class
    WithdrawApprovalMatrixRequestCommandHandler : IRequestHandler<WithdrawApprovalMatrixRequestCommand, WithdrawApprovalMatrixRequestResponse>
{
    private readonly IApprovalMatrixRequestRepository _approvalMatrixRequestRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IApprovalMatrixApprovalRepository _approvalMatrixApprovalRepository;
    private readonly IWorkflowTempRepository _workflowTempRepository;
    private readonly IWorkflowRepository _workflowRepository;

    public WithdrawApprovalMatrixRequestCommandHandler(IApprovalMatrixRequestRepository approvalMatrixRequestRepository, IMapper mapper, IPublisher publisher, IApprovalMatrixApprovalRepository approvalMatrixApprovalRepository, IWorkflowTempRepository workflowTempRepository, IWorkflowRepository workflowRepository)
    {
        _approvalMatrixRequestRepository = approvalMatrixRequestRepository;
        _mapper = mapper;
        _publisher = publisher;
        _approvalMatrixApprovalRepository = approvalMatrixApprovalRepository;
        _workflowTempRepository = workflowTempRepository;
        _workflowRepository = workflowRepository;
    }

    public async Task<WithdrawApprovalMatrixRequestResponse> Handle(WithdrawApprovalMatrixRequestCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _approvalMatrixRequestRepository.GetByReferenceIdAsync(request.Id)
                            ?? throw new NotFoundException(nameof(Domain.Entities.ApprovalMatrixRequest), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(WithdrawApprovalMatrixRequestCommand),
            typeof(Domain.Entities.ApprovalMatrixRequest));

        eventToUpdate.Status = "Withdraw";

        await _approvalMatrixRequestRepository.UpdateAsync(eventToUpdate);

        var eventToUpdateApproval =
            await _approvalMatrixApprovalRepository.GetApprovalMatrixApprovalByRequestId(eventToUpdate.RequestId);

        eventToUpdateApproval.ForEach(x => x.IsApproval = false);
        eventToUpdateApproval.ForEach(x => x.Status = "Withdraw");

        await _approvalMatrixApprovalRepository.UpdateRangeAsync(eventToUpdateApproval);

        var workflowTemp = await _workflowTempRepository.GetWorkflowTempByRequestId(eventToUpdate.RequestId);

        if (workflowTemp is not null)
        {
            var workflow = await _workflowRepository.GetByReferenceIdAsync(workflowTemp.WorkflowId);

            workflow.IsDraft = false;

            await _workflowRepository.UpdateAsync(workflow);
        }


        var response = new WithdrawApprovalMatrixRequestResponse
        {
            Message = $"Approval Matrix Request '{eventToUpdate.ProcessName}' has been withdraw successfully.",

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new WithdrawApprovalMatrixRequestedEvent { Name = eventToUpdate.ProcessName },
            cancellationToken);

        return response;
    }
}