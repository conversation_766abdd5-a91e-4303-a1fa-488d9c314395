﻿@using ContinuityPatrol.Web.Areas.Alert.Controllers
<link href="~/css/thirdparty.bundle.css" rel="stylesheet" />
<link href="~/css/viewer.part.bundle.css" rel="stylesheet">
<link href="~/css/report.css" rel="stylesheet" />


<div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollabel">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-business-service"></i><span>Alert Dashboard</span></h6>
                <button type="button" class="btn-close ms-2" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">

                <div class="card-body pt-0">
                    <div class="d-flex gap-2">
                        <div class="input-group">
                            <span class="input-group-text form-label mb-0" for="basic-url">Infraobject</span>
                            <div class="input-group">
                                <select aria-label="Default select example" class="form-select" id="infraValue">
                                    <option>Select Infraobject</option>
                                    
                                </select>
                            </div>
                        </div>
                        <div class="input-group">
                            <span class="input-group-text form-label mb-0" for="basic-url">Severity</span>
                            <div class="input-group">
                                <select aria-label="Default select example" class="form-select " id="serverityValue">
                                    <option>Select Severity</option>
                                    
                                </select>
                            </div>
                        </div>
                        <div class="input-group">
                            <span class="input-group-text form-label mb-0" for="basic-url">Type</span>
                            <div class="input-group">
                                <select aria-label="Default select example" class="form-select " id="typeValue">
                                    <option>Select Type</option>
                                   
                                </select>
                            </div>
                        </div>
                        <div class="input-group">
                            <span class="input-group-text form-label mb-0" for="startDate" cursorshover="true">Start&nbsp;Date</span>
                            <div class="input-group" id="startDate">
                                <input placeholder="Select start date" type="date"
                                       class="form-control custom-cursor-default-hover" value="" />
                            </div>
                        </div>
                        <div class="input-group">
                            <span class="input-group-text form-label mb-0" for="endDate">End&nbsp;Date</span>
                            <div class="input-group" id="endDate">
                                <input placeholder="Select end date" type="date"
                                       class="form-control custom-cursor-default-hover" value="" />
                            </div>
                        </div>
                    </div>
                    <!-- chart section start -->
                    <div class="collapse row mt-2" id="collapseExample">
                        <div class="col-xl-5">
                            <div id="chartdiv"></div>
                        </div>
                        <div class="col-xl-7">
                            <div id="severitylinechart"></div>
                        </div>
                    </div>
                    <!-- chart section end -->
                    <div id="btnAlert"></div>
                </div>

            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/common/viewer.part.bundle.js"></script>
