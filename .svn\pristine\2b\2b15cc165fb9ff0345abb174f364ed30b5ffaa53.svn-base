using ContinuityPatrol.Application.Features.DriftParameter.Commands.Create;
using ContinuityPatrol.Application.Features.DriftParameter.Commands.Update;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftParameterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IDriftParameterService
{
    Task<List<DriftParameterListVm>> GetDriftParameterList();
    Task<BaseResponse> CreateAsync(CreateDriftParameterCommand createDriftParameterCommand);
    Task<BaseResponse> UpdateAsync(UpdateDriftParameterCommand updateDriftParameterCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<DriftParameterDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsDriftParameterNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<DriftParameterListVm>> GetPaginatedDriftParameters(GetDriftParameterPaginatedListQuery query);
    #endregion
}
