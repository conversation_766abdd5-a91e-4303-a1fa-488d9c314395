﻿using ContinuityPatrol.Domain.ViewModels.ReplicationModel;

namespace ContinuityPatrol.Application.Features.Replication.Queries.GetNames;

public class GetReplicationNameQueryHandler : IRequestHandler<GetReplicationNameQuery, List<ReplicationNameVm>>
{
    private readonly IMapper _mapper;
    private readonly IReplicationRepository _replicationRepository;

    public GetReplicationNameQueryHandler(IReplicationRepository replicationRepository, IMapper mapper)
    {
        _replicationRepository = replicationRepository;
        _mapper = mapper;
    }

    public async Task<List<ReplicationNameVm>> Handle(GetReplicationNameQuery request,
        CancellationToken cancellationToken)
    {
        var replications = await _replicationRepository.GetReplicationNames();

        var replicationDto = _mapper.Map<List<ReplicationNameVm>>(replications);

        return replicationDto;
    }
}