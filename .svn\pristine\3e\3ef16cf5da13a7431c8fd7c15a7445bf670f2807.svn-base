﻿using ContinuityPatrol.Application.Features.Report.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Queries;

public class GetReportNameQueryHandlerTests : IClassFixture<ReportFixture>
{
    private readonly ReportFixture _reportFixture;

    private Mock<IReportRepository> _mockReportRepository;

    private readonly GetReportNameQueryHandler _handler;

    public GetReportNameQueryHandlerTests(ReportFixture reportFixture)
    {
        _reportFixture = reportFixture;

        _mockReportRepository = ReportRepositoryMocks.GetReportNamesRepository(_reportFixture.Reports);

        _handler = new GetReportNameQueryHandler(_reportFixture.Mapper, _mockReportRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_ReportsName()
    {
        var result = await _handler.Handle(new GetReportNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<GetReportNameVm>>();

        result[0].Id.ShouldBe(_reportFixture.Reports[0].ReferenceId);
        result[0].Name.ShouldBe(_reportFixture.Reports[0].Name);
    }

    [Fact]
    public async Task Handle_Return_Active_ReportNamesCount()
    {
        var result = await _handler.Handle(new GetReportNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<GetReportNameVm>>();
        result.Count.ShouldBe(_reportFixture.Reports.Count);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockReportRepository = ReportRepositoryMocks.GetReportEmptyRepository();

        var handler = new GetReportNameQueryHandler(_reportFixture.Mapper, _mockReportRepository.Object);

        var result = await handler.Handle(new GetReportNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetReportNameQuery(), CancellationToken.None);

        _mockReportRepository.Verify(x => x.GetReportNames(), Times.Once);
    }
}