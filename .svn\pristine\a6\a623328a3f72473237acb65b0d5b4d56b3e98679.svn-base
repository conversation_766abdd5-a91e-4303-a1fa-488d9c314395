using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Create;
using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Delete;
using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetList;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MenuBuilderModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class MenuBuilderService : BaseService, IMenuBuilderService
{
    public MenuBuilderService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<MenuBuilderListVm>> GetMenuBuilderList()
    {
        Logger.LogInformation("Get All MenuBuilders");

        return await Mediator.Send(new GetMenuBuilderListQuery());
    }

    public async Task<MenuBuilderDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "MenuBuilder Id");

        Logger.LogInformation($"Get MenuBuilder Detail by Id '{id}'");

        return await Mediator.Send(new GetMenuBuilderDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateMenuBuilderCommand createMenuBuilderCommand)
    {
        Logger.LogInformation($"Create MenuBuilder '{createMenuBuilderCommand}'");

        return await Mediator.Send(createMenuBuilderCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateMenuBuilderCommand updateMenuBuilderCommand)
    {
        Logger.LogInformation($"Update MenuBuilder '{updateMenuBuilderCommand}'");

        return await Mediator.Send(updateMenuBuilderCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "MenuBuilder Id");

        Logger.LogInformation($"Delete MenuBuilder Details by Id '{id}'");

        return await Mediator.Send(new DeleteMenuBuilderCommand { Id = id });
    }
    #region NameExist
    public async Task<bool> IsMenuBuilderNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "MenuBuilder Name");

        Logger.LogInformation($"Check Name Exists Detail by MenuBuilder Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetMenuBuilderNameUniqueQuery { Name = name, Id = id });
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<MenuBuilderListVm>> GetPaginatedMenuBuilders(GetMenuBuilderPaginatedListQuery query)
    {
        Logger.LogInformation("Get Searching Details in MenuBuilder Paginated List");

        return await Mediator.Send(query);
    }
    #endregion
}
