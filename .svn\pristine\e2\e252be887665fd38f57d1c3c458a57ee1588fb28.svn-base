﻿using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Create;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.TestConnection;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Update;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateDefault;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateNodeStatus;
using ContinuityPatrol.Application.Features.LoadBalancer.Events.PaginatedView;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.MonitorService.Command.UpdateStatus;
using ContinuityPatrol.Application.Features.UserActivity.Commands.Create;
using ContinuityPatrol.Domain.ViewModels.LoadBalancerModel;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class LoadBalancerController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly ILogger<LoadBalancerController> _logger;
    private readonly ILoggedInUserService _loggedInUserService;

    public LoadBalancerController(IPublisher publisher, IMapper mapper, IDataProvider dataProvider, 
        ILogger<LoadBalancerController> logger, ILoggedInUserService loggedInUserService)
    {
        _publisher = publisher;
        _mapper = mapper;
        _dataProvider = dataProvider;
        _logger = logger;
        _loggedInUserService = loggedInUserService;
    }

    [AntiXss]
    public async Task<IActionResult> List()
    {
        try
        {
            _logger.LogDebug("Entering List method in LoadBalancer");

            await _publisher.Publish(new LoadBalancerPaginatedEvent());

            return View();
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred in LoadBalancer list.", ex);

            return View(new LoadBalancerViewModel
            {
                NodesVm = new List<LoadBalancerListVm>()
            });
        }

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Admin.CreateAndEdit)]
    public async Task<IActionResult> CreateOrUpdate(LoadBalancerViewModel nodeConfigurationViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in LoadBalancer");

        var id = Request.Form["Id"].ToString();
        try
        {

            if (id.IsNullOrWhiteSpace())
            {
                var createNode = _mapper.Map<CreateLoadBalancerCommand>(nodeConfigurationViewModel);
                var result = await _dataProvider.LoadBalancer.CreateAsync(createNode);
                _logger.LogDebug($"Creating LoadBalancer node '{createNode.Name}'.");
                TempData.NotifySuccess(result.Message);
            }
            else
            {
                var updateNode = _mapper.Map<UpdateLoadBalancerCommand>(nodeConfigurationViewModel);
                var result = await _dataProvider.LoadBalancer.UpdateAsync(updateNode);
                _logger.LogDebug($"Updating LoadBalancer node '{updateNode.Name}'.");
                TempData.NotifySuccess(result.Message);
            }
            _logger.LogDebug("CreateOrUpdate operation completed successfully in LoadBalancer, returning view.");
            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on load balancer page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on group node policy page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in LoadBalancer");
        try
        {
            var loadBalancer = await _dataProvider.LoadBalancer.DeleteAsync(id);
            TempData.NotifySuccess(loadBalancer.Message);
            _logger.LogDebug("Successfully deleted record in LoadBalancer");
            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on load balancer.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetLoadBalancerPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in LoadBalancer");
        try
        {
            _logger.LogDebug("Successfully retrieved load balancer paginated list on LoadBalancer");
            return Json(await _dataProvider.LoadBalancer.GetPaginatedNodeConfigurations(query));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on load balancer page while processing the pagination request.", ex);
            return Json("");
        }

    }
    [HttpGet]
    public async Task<bool> IsLoadBalancerNameExist(string loadBalancerName, string id)
    {
        _logger.LogDebug("Entering IsLoadBalancerNameExist method in LoadBalancer");
        try
        {
            _logger.LogDebug("Returning result for IsLoadBalancerNameExist on LoadBalancer");
            return await _dataProvider.LoadBalancer.IsLoadBalancerNameExist(loadBalancerName, id);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on load balancer page while checking if loadBalancer name exists for : {loadBalancerName}.", ex);
            return false;
        }
    }
    [HttpGet]
    public async Task<bool> IsLoadBalancerIpandPortExist(string ipAddress, int port, string id)
    {
        _logger.LogDebug("Entering IsLoadBalancerNameExist method in LoadBalancer");
        try
        {
            _logger.LogDebug("Returning result for IsLoadBalancerNameExist on LoadBalancer");
            return await _dataProvider.LoadBalancer.IsIpAddressAndPortExist(ipAddress,port, id);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on load balancer page while checking if loadBalancer Port exists for : {port}.", ex);
            return false;
        }
    }
    public IActionResult StateMonitoring()
    {
        
        return View();
    }    

    [HttpGet]
    public async Task<JsonResult> LoadStateMonitoring()
    {
        _logger.LogDebug("Entering LoadStateMonitoring method in LoadBalancer");
        await CreateUserActivity();
        try
        {
            var stateDetails = await _dataProvider.LoadBalancer.GetStateMonitorStatusList();
            _logger.LogDebug("Successfully load State Monitoring in LoadBalancer");
            return Json(new { Success = true, data = stateDetails });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on load Balancer page while loading state monitoring request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<IActionResult> TestConfiguration(string nodeId)
    {
        _logger.LogDebug("Entering TestConfiguration method in LoadBalancer");
        try
        {
            LoadBalancerTestConnectionCommand nodeConfigurationTestConnectionCommand = new()
            {
                Id = nodeId
            };
            var result = await _dataProvider.LoadBalancer.TestConnection(nodeConfigurationTestConnectionCommand);
            _logger.LogDebug($"Successfully test loadBalancer connection for nodeId '{nodeId}'");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on load balancer page while testing the loadBalancer connection.", ex);
            return ex.GetJsonException();
        }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> UpdateNodeStatus(UpdateNodeStatusCommand command)
    {
        _logger.LogDebug("Entering UpdateNodeStatus method in load balancer.");
        try
        {
            var getStatus = await _dataProvider.LoadBalancer.UpdateNodeStatus(command);
            return Json(new { Success = true, data = getStatus });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on load balancer page while updating the node status.", ex);

            return ex.GetJsonException();
        }
    }
    private async Task CreateUserActivity()
    {
        try
        {
            var userActivity = new CreateUserActivityCommand
            {
                CompanyId = _loggedInUserService.CompanyId,
                UserId = _loggedInUserService.UserId,
                LoginName = _loggedInUserService.LoginName,
                RequestUrl = _loggedInUserService.RequestedUrl,
                HostAddress = _loggedInUserService.IpAddress,
                Action = $"{ActivityType.View} StateMonitoring",
                Entity = "StateMonitoring",
                ActivityType = ActivityType.View.ToString(),
                ActivityDetails = $"State Monitoring viewed"
            };

            await _dataProvider.UserActivity.CreateAsync(userActivity);

            _logger.LogInformation($"State Monitoring viewed");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while creating user activity for State Monitoring.", ex);
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> IsDefault(UpdateLoadBalancerDefaultCommand command)
    {
        _logger.LogDebug("Entering IsDefault method in load balancer.");
        try
        {
            var defaultStatus = await _dataProvider.LoadBalancer.UpdateDefault(command);
            return Json(new { Success = true, data = defaultStatus });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on load balancer page while updating the node status.", ex);

            return ex.GetJsonException();
        }
    }
}