﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = 'DataSyncAppReplication';
let infraObjectId = sessionStorage.getItem("infraobjectId");
setTimeout(() => { DataSyncmonitorStatus(mId, monitortype) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
//setTimeout(() => { datasyncServer(infraObjectId) }, 250)

//$('#mssqlserver').hide();
//async function datasyncServer(id) {

//    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
//    let data = {}
//    data.infraObjectId = id;
//    let mssqlServerData = await getAysncWithHandler(url, data);

//    if (mssqlServerData != null && mssqlServerData?.length > 0) {
//        mssqlServerData?.forEach(data => {
//            let value = data?.isServiceUpdate            
//            let parsed = []            
//            //if (value && value !== 'NA') parsed = JSON?.parse(value)
//            if (typeof value === 'string' && value.trim().startsWith('[')) {
//                try {
//                    parsed = JSON?.parse(value)
//                } catch (err) {
//                    console.warn('Invalid JSON in isServiceUpdate:', value);
//                }                
//                if (Array.isArray(parsed)) {
//                    parsed?.forEach(s => {
//                        if (s?.Services?.length) {
//                            $('#mssqlserver').show();
//                            bindDatasyncServer(mssqlServerData)
//                        }
//                    })
//                }
//            }
//        })

//    } else {
//        $('#mssqlserver').hide();
//    }

//}
//function bindDatasyncServer(mssqlServerData) {

//    let prType = { IpAddress: '--', Services: [] };
//    let drType = { IpAddress: '--', Services: [] };

//    // Loop through each item to find PR and DR entries
//    mssqlServerData?.forEach(item => {
//        let parsedServices = [];
//        try {
//            const value = item?.isServiceUpdate
//            if (value && value !== 'NA') {
//                parsedServices = JSON.parse(item?.isServiceUpdate)
//            }
//        } catch (e) {
//            console.error("Invalid JSON in isServiceUpdate:", item?.isServiceUpdate);
//        }

//        parsedServices?.forEach(serviceGroup => {
//            if (serviceGroup?.Type === 'PR') {
//                prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
//                prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];
//            } else if (serviceGroup?.Type === 'DR') {
//                drType.IpAddress = serviceGroup?.IpAddress || drType?.IpAddress;
//                drType.Services = [...drType.Services, ...(serviceGroup?.Services || [])];
//            }
//        });
//    });

//    // Set header IPs
//    $('#prIp').text('Primary (' + prType?.IpAddress + ')');
//    $('#drIp').text('DR (' + drType?.IpAddress + ')');

//    const prStatusSummary = getStatusSummary(prType.Services.map(s => s.Status).filter(Boolean));
//    const drStatusSummary = getStatusSummary(drType.Services.map(s => s.Status).filter(Boolean));
//    $('#prIp').html(`Primary (${prType.IpAddress}) <br ><span class="status-summary text-muted small">${prStatusSummary}</span>`);
//    $('#drIp').html(`DR (${drType.IpAddress}) <br ><span class="status-summary text-muted small">${drStatusSummary}</span>`);

//    // Unique list of all service names from both PR and DR
//    let allServiceNames = [...new Set([
//        ...prType?.Services?.map(s => s?.ServiceName),
//        ...drType?.Services?.map(s => s?.ServiceName)
//    ])];

//    // Build table rows
//    let tbody = $('#mssqlserverbody');
//    tbody.empty();

//    allServiceNames?.forEach(serviceName => {
//        let prService = prType?.Services?.find(s => s?.ServiceName === serviceName);
//        let drService = drType?.Services?.find(s => s?.ServiceName === serviceName);

//        let prStatus = prService ? prService?.Status : '--';
//        let drStatus = drService ? drService?.Status : '--';
//        let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
//        let drIcon = drStatus !== '--' ? `<i class="${getStatusIconClass(drStatus)} me-2 fs-6"></i>` : '';
//        const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";

//        let row = `
//            <tr>
//                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
//                <td>${prIcon}${prStatus}</td>
//                <td>${drIcon}${drStatus}</td>
//            </tr>
//        `;
//        tbody.append(row);
//    });
//}
//function getStatusSummary(arr) {
//    let countMap = {};
//    arr?.forEach(status => {
//        countMap[status] = (countMap[status] || 0) + 1;
//    });
//    let total = arr?.length;
//    let statusSummary = Object.entries(countMap)
//        .map(([status, count]) => `${count} ${status}`)
//        .join(', ');
//    return statusSummary ? `${statusSummary} out of ${total} services` : '--';
//}
//function getStatusIconClass(status) {
//    if (!status) return "text-danger cp-disable";

//    const lowerStatus = status.toLowerCase();
//    if (lowerStatus === "running") {
//        return "text-success cp-reload cp-animate";
//    } else if (lowerStatus === "error" || lowerStatus === "stopped") {
//        return "text-danger cp-fail-back";
//    } else {
//        return "text-danger cp-disable";
//    }
//}
let prStatus = ''
let drStatus = ''

$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})
function DataSyncmonitorStatus(id, type) {
    $.ajax({
        url: "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType",
        method: 'GET',
        data: {
            monitorId: id,
            type: type
        },
        dataType: 'json',
        async: true,
        success: function (res) {
            const value = res?.data;            
            let data = JSON?.parse(value?.properties)   
           
            ReplicationType.push(checkAndReplace(data?.Monitor_Type));
            let dataVal = data?.DataSyncReplicationModels[0]?.MonitoringModel.DataSyncJobIds
            
            DataSyncTableList(dataVal)
            function checkAndReplace(value) {
                return (value === null || value === '' || value === undefined) ? 'NA' : value;
            }

            $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
            $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
            if (value === undefined || value === null || value === '') {
                $("#noDataimg").css('text-align', 'center').html(noDataImage);
            }
            else {

                let data = JSON?.parse(value?.properties);                
                value?.serverStatus?.forEach(server => {
                    Object.keys(server).forEach(key => {
                        let data = server[key]
                        if (data?.serverType?.toLowerCase()?.includes('pr')) {
                            prStatus = data?.status?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success"></i>' : data?.status?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger"></i>' : '<i class="cp-pending text-warning"></i>';
                        } else if(data?.serverType?.toLowerCase()?.includes('dr')) {
                            drStatus = data?.status?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success"></i>' : data?.status?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger"></i>' : '<i class="cp-pending text-warning"></i>';
                        } 
                    })
                    
                })                

                $('#PR_Server_Name').text(checkAndReplace(data?.PrDataSyncReplicationModel?.PrMonitoringModel?.PR_Server_Name))
                $('#PR_Server_IpAddress').text(checkAndReplace(data?.PrDataSyncReplicationModel?.PrMonitoringModel?.PR_Server_IpAddress)).prepend(prStatus)

                let customSite = data?.DataSyncReplicationModels?.length > 1;
                if (customSite) {
                    $("#Sitediv").show();
                } else {
                    $("#Sitediv").hide();
                }

                $(".siteContainer").empty();

                data?.DataSyncReplicationModels?.forEach((a, index) => {
                    let selectTab = `
                         <li class="nav-item siteListChange" id='siteName${index}'>
                              <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
                            </li>
                         <li class="nav-item vr"></li>`;
                    $(".siteContainer").append(selectTab);
                });
                if (data?.DataSyncReplicationModels?.length > 0) {
                    $("#siteName0 .nav-link").addClass("active");
                    displaySiteData(data?.DataSyncReplicationModels[0]);
                }
                $(document).on('click', '.siteListChange', function () {
                    $(".siteListChange .nav-link").removeClass("active");
                    $(this).find(".nav-link").addClass("active");
                    let siteId = $(this)[0].id
                    let getSiteName = $(`#${siteId} .siteName`).text()

                    let MonitoringModel = data?.DataSyncReplicationModels?.find(d => d?.Type === getSiteName);
                    if (MonitoringModel) {
                        displaySiteData(MonitoringModel, getSiteName);
                    }
                    bindMonitoringServices(globalMSSQLServerData, getSiteName);
                });
                function displaySiteData(siteData) {                    
                    $("#DR_Server_IpAddress").text(checkAndReplace(siteData?.MonitoringModel?.Server_IpAddress)).prepend(drStatus)
                    $('#DR_Server_Name').text(checkAndReplace(siteData?.MonitoringModel?.Server_Name))
                }
            }
        }
    })
}

let ReplicationType = [];

async function DataSyncTableList(data) {
    
    let dataSyncJobIds =data
    $.ajax({
        url: "/Monitor/Monitoring/GetFastMonitorList",
        method: 'GET',
        data: {
            dataSyncJobIds: JSON?.stringify(dataSyncJobIds)
        },
        dataType: 'json',
        async: true,
        success: function (res) {
            const value = res.data;    
          
            function checkAndReplace(value) {
                return (value === null || value === '' || value === undefined) ? 'NA' : value;
            }

            if (value === undefined || value === null || value === '') {
                $("#noDataimg").css('text-align', 'center').html(noDataImage);
            }
            else {

                    let data = value;                          
                    let SourcePath = '';
                   
                    let TotalNumberoffiles = [];
                    let TotalFilesSize = [];
                    let NumberOfRegFilesTransfer = [];
                    let SDirectory = [];
                    let TDirectory = [];
                    let TotalFileSize = [];
                    let LastFileSize = [];
                    let LastFileName = [];
                    let skipFile = [];
                    $('#dataSynctable').empty();
                    
                data?.length && data?.forEach((list, i) => {
                   
                        SourcePath = checkAndReplace(list?.sourceIP?.split(',')[0])              
                        LastFileSize.push(checkAndReplace(list?.lastFileSize))
                        LastFileName.push(checkAndReplace(list?.lastFileName));
                        SDirectory.push(checkAndReplace(list?.sourcePath));
                        TDirectory.push(checkAndReplace(list?.destinationPath))                     
                        TotalFileSize.push(checkAndReplace(list?.totalFilesCount))
                        skipFile.push(checkAndReplace(list?.skippedFilesCount))
                       });
                       
                    let jobData = ''
                let iconClass = TotalNumberoffiles !== 'NA' ? 'cp-files me-1 fs-6 text-primary' : TotalFilesSize !== 'NA' ? "cp-file-size me-1 fs-6 text-primary" : NumberOfRegFilesTransfer != "NA" ? "cp-files me-1 fs-6 text-primary" : TotalTransferfileSize !== 'NA' ? "cp-file-size me-1 fs-6 text-primary" : skipFile !== 'NA' ? "cp-file-size me-1 fs-6 text-primary" : 'text-danger me-1 fs-6 cp-disable';
                    
                    jobData += `
                        <thead>
                                <tr>
                                    <th>Replication Monitoring</th>
                                    <th></th>

                                </tr>
                            </thead>
                            <tbody>
                        <tr>
                           <td class="fw-semibold text-truncate "><i class="text-secondary cp-server-ip me-1"></i> Source IP</td>
                            <td class="text-truncate">${prStatus}${SourcePath}</td>
                        </tr>
                         <tr>
                             <td class="fw-semibold text-truncate "><i class="text-secondary cp-replication-type me-1"></i>Replication Type</td>
                             <td class="text-truncate"><i class="cp-replication-type me-1 fs-6 text-primary"></i>${ReplicationType}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-control-file-type me-1"></i>Source Directory</td>
                            <td class="text-truncate"><i class="cp-report-path me-1 fs-6 text-primary"></i>${SDirectory}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-control-file-type me-1"></i>Target Directory</td>
                            <td class="text-truncate"><i class="cp-report-path me-1 fs-6 text-primary"></i>${TDirectory}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-replication-connect me-1"></i>Total Replication Files</td>
                            <td class="text-truncate"><i class="${iconClass}"></i>${TotalFileSize}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-control-file-name me-1"></i>Last Replicated File Name</td>
                            <td class="text-truncate"><i class="${iconClass}"></i>${LastFileName}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-file-size me-1"></i>Last Replicated File Size (in KB)</td>
                            <td class="text-truncate"><i class="${iconClass}"></i>${LastFileSize}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-file-size me-1"></i>Skipped Files</td>
                            <td class="text-truncate"><i class="${iconClass}"></i>${skipFile}</td>
                        </tr>
                        </tbody>
                            `

                    $('#dataSynctable').append(jobData);

                }
            }
    });

}