﻿using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.LicenseManager.Queries.GetChildLicenseByParentId;

public class GetChildLicenseDetailByParentIdQueryHandler : IRequestHandler<GetChildLicenseDetailByParentIdQuery,
    List<ChildLicenseDetailByParentIdVm>>
{
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly IMapper _mapper;

    public GetChildLicenseDetailByParentIdQueryHandler(ILicenseManagerRepository licenseManagerRepository,
        ILicenseInfoRepository licenseInfoRepository, IMapper mapper)
    {
        _licenseManagerRepository = licenseManagerRepository;
        _mapper = mapper;
        _licenseInfoRepository = licenseInfoRepository;
    }

    public async Task<List<ChildLicenseDetailByParentIdVm>> Handle(GetChildLicenseDetailByParentIdQuery request,
        CancellationToken cancellationToken)
    {
        var childList =
            await _licenseManagerRepository.GetDerivedLicenseDetailByBaseLicenseDetailAsync(request.ParentId,
                request.ParentPO);

        var childListDtl = _mapper.Map<List<ChildLicenseDetailByParentIdVm>>(childList);

        foreach (var derivedLicense in childListDtl)
        {
            

        //childListDtl.ForEach(derivedLicense =>
        //{
            derivedLicense.IsLicenseExpiry =
                DateTime.TryParseExact(derivedLicense.ExpiryDate, "dd MMMM yyyy", CultureInfo.GetCultureInfo("en-IN"),
                    DateTimeStyles.None, out var expiryDate) && expiryDate >= DateTime.UtcNow.Date
                    ? true
                    : false;

            derivedLicense.ExpiryDate = derivedLicense.Validity.Contains("Enterprise-Unlimited")
                ? "Unlimited"
                : derivedLicense.ExpiryDate;

            var derivedUsedDatabaseCount = await _licenseInfoRepository
                .GetAvailableCountByLicenseId(derivedLicense.Id, Modules.Database.ToString());
            var derivedUsedReplicationCount = await _licenseInfoRepository
                .GetAvailableCountByLicenseId(derivedLicense.Id, Modules.Replication.ToString());

            var derivedUsedStorageCount = await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(derivedLicense.Id, Modules.Server.ToString(), "storage");

            var derivedUsedVirtualizationCount = await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(derivedLicense.Id, Modules.Server.ToString(),
                    "virtualization");

            var derivedUsedApplicationCount = await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(derivedLicense.Id, Modules.Server.ToString(),
                    "application");
            var derivedUsedDnsCount = await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(derivedLicense.Id, Modules.Server.ToString(), "dns");

            var derivedUsedNetworkCount = await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(derivedLicense.Id, Modules.Server.ToString(), "network");

            var derivedUsedThirdPartyCount = await _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(derivedLicense.Id, Modules.Server.ToString(), "thirdparty");


            var totalUsedCount = derivedUsedDatabaseCount + derivedUsedReplicationCount
                                                          + derivedUsedStorageCount + derivedUsedVirtualizationCount +
                                                          derivedUsedApplicationCount + derivedUsedDnsCount
                                                          + derivedUsedNetworkCount + derivedUsedThirdPartyCount;

            var totalCount = GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties, "primarydatabaseCount")
                             + GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties,
                                 "primaryreplicationCount")
                             + GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties, "primarystorageCount")
                             + GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties,
                                 "primaryvirtualizationCount")
                             + GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties,
                                 "primaryapplicationCount")
                             + GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties, "primarydnsCount")
                             + GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties, "primarynetworkCount")
                             + GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties,
                                 "primarythirdPartyCount");

            var totalNotUsedCount = totalCount - totalUsedCount;

            derivedLicense.AvailableCount = new AvailableCount
            {
                TotalCount = totalCount,
                TotalUsedCount = totalUsedCount,
                TotalNotUsedCount = totalNotUsedCount,
                DatabaseUsedCount = derivedUsedDatabaseCount,
                ReplicationUsedCount = derivedUsedReplicationCount,
                ApplicationUsedCount = derivedUsedApplicationCount,
                StorageUsedCount = derivedUsedStorageCount,
                VirtualizationUsedCount = derivedUsedVirtualizationCount,
                DnsUsedCount = derivedUsedDnsCount,
                NetworkUsedCount = derivedUsedNetworkCount,
                ThirdPartyUsedCount = derivedUsedThirdPartyCount,
                DatabaseAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties, "primarydatabaseCount"),
                ReplicationAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties, "primaryreplicationCount"),
                StorageAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties, "primarystorageCount"),
                VirtualizationAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties, "primaryvirtualizationCount"),
                ApplicationAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties, "primaryapplicationCount"),
                DnsAvailableCount = GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties, "primarydnsCount"),
                NetworkAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties, "primarynetworkCount"),
                ThirdPartyAvailableCount =
                    GetJsonProperties.GetLicenseJsonValue(derivedLicense.Properties, "primarythirdPartyCount")
            };
        }

        return childListDtl;
    }
}