﻿using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;

namespace ContinuityPatrol.Application.Features.BusinessService.Queries.GetList;

public class
    GetBusinessServiceListQueryHandler : IRequestHandler<GetBusinessServiceListQuery, List<BusinessServiceListVm>>
{
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IMapper _mapper;

    public GetBusinessServiceListQueryHandler(IMapper mapper, IBusinessServiceRepository businessServiceRepository)
    {
        _mapper = mapper;
        _businessServiceRepository = businessServiceRepository;
    }

    public async Task<List<BusinessServiceListVm>> Handle(GetBusinessServiceListQuery request,
        CancellationToken cancellationToken)
    {
        var businessServices = (await _businessServiceRepository.ListAllAsync()).ToList();

        return businessServices.Count <= 0
            ? new List<BusinessServiceListVm>()
            : _mapper.Map<List<BusinessServiceListVm>>(businessServices);
    }
}