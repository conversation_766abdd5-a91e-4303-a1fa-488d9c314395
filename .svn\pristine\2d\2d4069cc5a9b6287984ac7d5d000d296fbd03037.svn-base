﻿using ContinuityPatrol.Application.Features.ImpactActivity.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ImpactActivity.Events;

public class DeleteImpactActivityEventTests : IClassFixture<ImpactActivityFixture>, IClassFixture<UserActivityFixture>
{
    private readonly ImpactActivityFixture _impactActivityFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly ImpactActivityDeletedEventHandler _handler;

    public DeleteImpactActivityEventTests(ImpactActivityFixture impactActivityFixture,
        UserActivityFixture userActivityFixture)
    {
        _impactActivityFixture = impactActivityFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockImpactActivityEventLogger = new Mock<ILogger<ImpactActivityDeletedEventHandler>>();

        _mockUserActivityRepository = ImpactActivityRepositoryMocks.CreateImpactActivityEventRepository(_userActivityFixture.UserActivities);

        _handler = new ImpactActivityDeletedEventHandler(mockLoggedInUserService.Object,
            mockImpactActivityEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteCompanyEventDeleted()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_impactActivityFixture.ImpactActivityDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_impactActivityFixture.ImpactActivityDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}