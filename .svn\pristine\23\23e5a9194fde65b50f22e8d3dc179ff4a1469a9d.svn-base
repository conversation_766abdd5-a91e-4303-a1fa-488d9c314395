﻿using ContinuityPatrol.Domain.ViewModels.SiteLocationModel;
using ContinuityPatrol.Domain.ViewModels.SiteTypeModel;

namespace ContinuityPatrol.Domain.ViewModels.SiteModel;

public class SiteViewModel
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string LocationId { get; set; }
    public string Location { get; set; }
    public string TypeId { get; set; }
    public string Type { get; set; }
    public string PlatformType { get; set; }
    public string CompanyId { get; set; }
    public string CompanyName { get; set; }
    public string Lat { get; set; }
    public string Lng { get; set; }
    public string DataTemperature { get; set; }

    public List<SiteTypeListVm> SiteTypes { get; set; }
    public SiteTypeListModel SiteType { get; set; }
    public List<SiteLocationListVm> SiteLocations { get; set; }
    [ValidateNever] public IEnumerable<SelectListItem> Companies { get; set; }
}