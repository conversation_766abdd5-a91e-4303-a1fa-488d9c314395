using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class MenuBuilderRepository : BaseRepository<MenuBuilder>, IMenuBuilderRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public MenuBuilderRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
public Task<bool> IsNameExist(string name, string id)
{
    return Task.FromResult(!id.IsValidGuid()
        ? Entities.Any(e => e.Name.Equals(name))
        : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
}
}
