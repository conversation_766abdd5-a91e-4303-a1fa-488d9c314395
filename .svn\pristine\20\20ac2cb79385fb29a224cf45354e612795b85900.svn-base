namespace ContinuityPatrol.Application.Features.CyberComponent.Queries.GetNameUnique;

public class GetCyberComponentNameUniqueQueryHandler : IRequestHandler<GetCyberComponentNameUniqueQuery, bool>
{
    private readonly ICyberComponentRepository _cyberComponentRepository;

    public GetCyberComponentNameUniqueQueryHandler(ICyberComponentRepository cyberComponentRepository)
    {
        _cyberComponentRepository = cyberComponentRepository;
    }

    public async Task<bool> Handle(GetCyberComponentNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _cyberComponentRepository.IsNameExist(request.Name, request.Id);
    }
}