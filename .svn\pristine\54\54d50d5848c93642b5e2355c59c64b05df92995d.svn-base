﻿using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetDetail;

namespace ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetDetailByName;

public class GetWorkflowActionDetailByNameQueryHandler : IRequestHandler<GetWorkflowActionDetailByNameQuery, WorkflowActionDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowActionRepository _workflowActionRepository;

    public GetWorkflowActionDetailByNameQueryHandler(IMapper mapper, IWorkflowActionRepository workflowActionRepository)
    {
        _mapper = mapper;
        _workflowActionRepository = workflowActionRepository;
    }

    public async Task<WorkflowActionDetailVm> Handle(GetWorkflowActionDetailByNameQuery request,
        CancellationToken cancellationToken)
    {
        var workflowActionList = await _workflowActionRepository.GetWorkflowActionDetailsByName(request.Name);

        Guard.Against.NullOrDeactive(workflowActionList, nameof(Domain.Entities.WorkflowAction),
            new NotFoundException(nameof(Domain.Entities.WorkflowAction), request.Name));

        var workflowActionListDetailDto = _mapper.Map<WorkflowActionDetailVm>(workflowActionList);

        return workflowActionListDetailDto == null
            ? throw new NotFoundException(nameof(Domain.Entities.WorkflowAction), request.Name)
            : workflowActionListDetailDto;
    }
}