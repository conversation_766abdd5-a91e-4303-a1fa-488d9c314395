using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordExpire.Queries;

public class GetAdPasswordExpireDetailsQueryTests : IClassFixture<AdPasswordExpireFixture>
{
    private readonly AdPasswordExpireFixture _adPasswordExpireFixture;
    private readonly Mock<IAdPasswordExpireRepository> _mockAdPasswordExpireRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetAdPasswordExpireDetailsQueryHandler _handler;

    public GetAdPasswordExpireDetailsQueryTests(AdPasswordExpireFixture adPasswordExpireFixture)
    {
        _adPasswordExpireFixture = adPasswordExpireFixture;

        _mockAdPasswordExpireRepository = AdPasswordExpireRepositoryMocks.CreateQueryAdPasswordExpireRepository(_adPasswordExpireFixture.AdPasswordExpires);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<AdPasswordExpireDetailVm>(It.IsAny<Domain.Entities.AdPasswordExpire>()))
            .Returns((Domain.Entities.AdPasswordExpire entity) => new AdPasswordExpireDetailVm
            {
                Id = entity.ReferenceId,
                DomainServerId = entity.DomainServerId,
                DomainServer = entity.DomainServer,
                UserName = entity.UserName,
                Email = entity.Email,
                ServerList = entity.ServerList,
                NotificationDays = entity.NotificationDays,
                IsPassword = entity.IsPassword
            });

        _handler = new GetAdPasswordExpireDetailsQueryHandler(
            _mockMapper.Object,
            _mockAdPasswordExpireRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_AdPasswordExpireDetailVm_When_AdPasswordExpireExists()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var query = new GetAdPasswordExpireDetailQuery { Id = existingExpire.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(AdPasswordExpireDetailVm));
        result.Id.ShouldBe(existingExpire.ReferenceId);
        result.UserName.ShouldBe(existingExpire.UserName);
        result.Email.ShouldBe(existingExpire.Email);
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires[0];
        var query = new GetAdPasswordExpireDetailQuery { Id = existingExpire.ReferenceId };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordExpireRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var query = new GetAdPasswordExpireDetailQuery { Id = existingExpire.ReferenceId };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<AdPasswordExpireDetailVm>(It.IsAny<Domain.Entities.AdPasswordExpire>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AdPasswordExpireNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var query = new GetAdPasswordExpireDetailQuery { Id = nonExistentId };

        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.AdPasswordExpire)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AdPasswordExpireIsInactive()
    {
        // Arrange
        var inactiveExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        inactiveExpire.IsActive = false;
        var query = new GetAdPasswordExpireDetailQuery { Id = inactiveExpire.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_MapEntityToViewModel_WithCorrectProperties()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        existingExpire.DomainServerId = "DS001";
        existingExpire.DomainServer = "TestDomain.com";
        existingExpire.UserName = "TestUser";
        existingExpire.Email = "<EMAIL>";
        existingExpire.ServerList = "Server1,Server2";
        existingExpire.NotificationDays = "7,14,30";
        existingExpire.IsPassword = true;

        var query = new GetAdPasswordExpireDetailQuery { Id = existingExpire.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingExpire.ReferenceId);
        result.DomainServerId.ShouldBe("DS001");
        result.DomainServer.ShouldBe("TestDomain.com");
        
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var query = new GetAdPasswordExpireDetailQuery { Id = testId };

        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_adPasswordExpireFixture.AdPasswordExpires[0]);

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordExpireRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectViewModelType_When_MappingSuccessful()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var query = new GetAdPasswordExpireDetailQuery { Id = existingExpire.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<AdPasswordExpireDetailVm>();
        result.GetType().ShouldBe(typeof(AdPasswordExpireDetailVm));
    }
}
