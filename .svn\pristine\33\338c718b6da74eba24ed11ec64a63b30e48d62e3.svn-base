﻿using ContinuityPatrol.Application.Features.Database.Events.LicenseInfoEvents.Create;
using ContinuityPatrol.Application.Features.Database.Events.SaveAll;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.Database.Commands.SaveAll;

public class SaveAllDatabaseCommandHandler : IRequestHandler<SaveAllDatabaseCommand, SaveAllDatabaseResponse>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ISiteTypeRepository _siteTypeRepository;
    private readonly ISiteRepository _siteRepository;
    private readonly IPublisher _publisher;
    private readonly IServerRepository _serverRepository;
    private readonly ILogger<SaveAllDatabaseCommandHandler> _logger;
    public SaveAllDatabaseCommandHandler(IDatabaseRepository databaseRepository, ILoggedInUserService loggedInUserService, ISiteTypeRepository siteTypeRepository,
     ISiteRepository siteRepository,IPublisher publisher,IServerRepository serverRepository,ILogger<SaveAllDatabaseCommandHandler> logger)
    {
        _databaseRepository = databaseRepository; 
        _loggedInUserService = loggedInUserService;
        _siteTypeRepository = siteTypeRepository;
        _siteRepository = siteRepository;
        _publisher = publisher; 
        _serverRepository=serverRepository;
    }

    public async Task<SaveAllDatabaseResponse> Handle(SaveAllDatabaseCommand request, CancellationToken cancellationToken)
    {

        var eventToUpdate = await _databaseRepository.GetByReferenceIdAsync(request.DatabaseId);

        Guard.Against.NullOrDeactive(eventToUpdate, nameof(Domain.Entities.Database),
            new NotFoundException(nameof(Domain.Entities.Database), request.DatabaseName));

            var databaseList = request.DatabaseList.Select(database => new Domain.Entities.Database
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = database.Name,
            Type = eventToUpdate.Type,
            ServerId = database.ServerId,
            ServerName = database.ServerName,
            ModeType = "Pending",
            DatabaseTypeId = database.DatabaseTypeId,
            DatabaseType = database.DatabaseType,
            Properties = database.Properties,
            LicenseId = database.LicenseId,
            LicenseKey =database?.LicenseKey,
            CompanyId = eventToUpdate.CompanyId,
            BusinessServiceId = eventToUpdate.BusinessServiceId,
            BusinessServiceName = eventToUpdate.BusinessServiceName,
            Version = eventToUpdate.Version,
            FormVersion = eventToUpdate.FormVersion,
            IsActive = true,
            CreatedBy = _loggedInUserService.UserId,
            CreatedDate = DateTime.Now,
            LastModifiedBy = _loggedInUserService.UserId,
            LastModifiedDate = DateTime.Now

        }).ToList();

        var serverIds = databaseList.Select(x => x.ServerId).ToList();

        var serverList = await _serverRepository.GetByServerIdsAsync(serverIds);

        var addedDbNames = new List<string>();

        foreach (var database in databaseList)
        {
            try
            {
                string poNumber = database.LicenseKey;

                database.LicenseKey = SecurityHelper.Encrypt(database.LicenseKey);

                var addedDb = await _databaseRepository.AddAsync(database);

                var server = serverList.FirstOrDefault(d => d.ReferenceId.Equals(addedDb?.ServerId));

                server.LicenseKey = SecurityHelper.Encrypt(server.LicenseKey);

                server.IsAttached = true;

                await _serverRepository.UpdateAsync(server);

                var site = await _siteRepository.GetByReferenceIdAsync(server?.SiteId);

                var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site?.TypeId);

                var ipAddress = GetJsonProperties.GetIpAddressFromProperties(server.Properties);
                var logo = GetJsonProperties.GetJsonValue(addedDb.Properties, "icon");
                var sid = GetJsonProperties.GetJsonDatabaseSidValue(addedDb.Properties);
                var hostName = GetJsonProperties.GetHostNameFromProperties(server.Properties);

                if (siteType.Category.ToLower().Equals("primary"))
                    await _publisher.Publish(new DatabaseLicenseInfoCreatedEvent
                    {
                        EntityName = addedDb.Name,
                        LicenseId = addedDb.LicenseId,
                        PONumber = poNumber,
                        EntityId = addedDb.ReferenceId,
                        EntityField = $"{ipAddress},{hostName},{sid}",
                        Type = addedDb.DatabaseType,
                        BusinessServiceId = addedDb.BusinessServiceId,
                        BusinessServiceName = addedDb.BusinessServiceName,
                        Category = addedDb.Type,
                        Logo = logo
                    }, cancellationToken);

                addedDbNames.Add(addedDb.Name);
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"Error while process save-as for {database.Name} in Database {ex.Message}");
                continue; 
            }

        }

        await _publisher.Publish(new SaveAllDatabaseEvent {DatabaseOriginal=eventToUpdate.Name, saveAllDatabaseNames = addedDbNames}, cancellationToken);

        var response = new SaveAllDatabaseResponse
        {
            Message = "Database save-as Completed!."
        };

        return response;
    }
}