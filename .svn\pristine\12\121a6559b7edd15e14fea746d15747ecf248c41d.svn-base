﻿using ContinuityPatrol.Application.Features.StateMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.StateMonitorStatus.Commands;

public class UpdateStateMonitorStatusTests : IClassFixture<StateMonitorStatusFixture>
{
    private readonly StateMonitorStatusFixture _stateMonitorStatusFixture;

    private readonly Mock<IStateMonitorStatusRepository> _mockStateMonitorStatusRepository;

    private readonly UpdateStateMonitorStatusCommandHandler _handler;

    public UpdateStateMonitorStatusTests(StateMonitorStatusFixture stateMonitorStatusFixture)
    {
        _stateMonitorStatusFixture = stateMonitorStatusFixture;

        _mockStateMonitorStatusRepository =
            StateMonitorStatusRepositoryMocks.UpdateStateMonitorStatusRepository(_stateMonitorStatusFixture
                .StateMonitorStatuses);

        _handler = new UpdateStateMonitorStatusCommandHandler(_mockStateMonitorStatusRepository.Object,
            _stateMonitorStatusFixture.Mapper);
    }

    [Fact]
    public async Task Handle_ValidStateMonitorStatus_UpdateToStateMonitorStatussRepo()
    {
        _stateMonitorStatusFixture.UpdateStateMonitorStatusCommand.Id =
            _stateMonitorStatusFixture.StateMonitorStatuses[0].ReferenceId;

        var result = await _handler.Handle(_stateMonitorStatusFixture.UpdateStateMonitorStatusCommand,
            CancellationToken.None);

        var stateMonitorStatus = await _mockStateMonitorStatusRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_stateMonitorStatusFixture.UpdateStateMonitorStatusCommand.Properties,
            stateMonitorStatus.Properties);
    }


    [Fact]
    public async Task Handle_Return_UpdateStateMonitorStatusResponse_When_StateMonitorStatusUpdated()
    {
        _stateMonitorStatusFixture.UpdateStateMonitorStatusCommand.Id =
            _stateMonitorStatusFixture.StateMonitorStatuses[0].ReferenceId;

        var result = await _handler.Handle(_stateMonitorStatusFixture.UpdateStateMonitorStatusCommand,
            CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateStateMonitorStatusResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_stateMonitorStatusFixture.UpdateStateMonitorStatusCommand.Id);

        result.Message.ShouldContain(CommonConstants.StateMonitorStatuses);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidStateMonitorStatusId()
    {
        _stateMonitorStatusFixture.UpdateStateMonitorStatusCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() =>
            _handler.Handle(_stateMonitorStatusFixture.UpdateStateMonitorStatusCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _stateMonitorStatusFixture.UpdateStateMonitorStatusCommand.Id = _stateMonitorStatusFixture.StateMonitorStatuses[0].ReferenceId;

        await _handler.Handle(_stateMonitorStatusFixture.UpdateStateMonitorStatusCommand, CancellationToken.None);

        _mockStateMonitorStatusRepository.Verify(x=>x.GetByReferenceIdAsync(It.IsAny<string>()),Times.Once);

        _mockStateMonitorStatusRepository.Verify(x=>x.UpdateAsync(It.IsAny<Domain.Entities.StateMonitorStatus>()), Times.Once);
    }
}