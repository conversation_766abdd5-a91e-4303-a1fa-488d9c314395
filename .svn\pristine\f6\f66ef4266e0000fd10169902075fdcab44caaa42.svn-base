﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;
using FluentValidation.TestHelper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfileInfo.Validators;

public class UpdateWorkflowProfileInfoValidatorTests
{
    public List<Domain.Entities.WorkflowProfileInfo> WorkflowProfileInfos { get; set; }

    private readonly UpdateWorkflowProfileInfoCommandValidator _validator;

    public UpdateWorkflowProfileInfoValidatorTests()
    {
        WorkflowProfileInfos = new Fixture().Create<List<Domain.Entities.WorkflowProfileInfo>>();

        Mock<IWorkflowProfileInfoRepository> mockWorkflowProfileInfoRepository = new();

        mockWorkflowProfileInfoRepository
            .Setup(repo => repo.IsWorkflowProfileInfoNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);

        _validator = new UpdateWorkflowProfileInfoCommandValidator(mockWorkflowProfileInfoRepository.Object);

        WorkflowProfileInfoRepositoryMocks.UpdateWorkflowProfileInfoRepository(WorkflowProfileInfos);
    }

    // PROFILE NAME 

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_ProfileName_WithEmpty(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
     {
        updateWorkflowProfileInfoCommand.ProfileName = "";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Profile Name is required.");
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_ProfileName_IsNull(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = null;
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("'Profile Name' must not be empty.");
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_ProfileName_MiniMumRange(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "AB";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Profile Name should contain between 3 to 200 characters.");
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_ProfileName_MaximumRange(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIHGFEDCBA";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldNotHaveValidationErrorFor(x => x.ProfileName);
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_ProfileName(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "   Pts   ";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Please Enter Valid Profile Name");
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_Valid_ProfileName_With_SpecialCharacters_InBetween(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "A!B@C#D$E%F^G&H*";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Please Enter Valid Profile Name");
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_Valid_ProfileName_With_SpecialCharacters_Only(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "@!#$%^&**()(**";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Please Enter Valid Profile Name");
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_Valid_ProfileName_With_Numbers_Only(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "123456789";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Please Enter Valid Profile Name");
    }
    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_Valid_ProfileName_With_DoubleSpace_InFront(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "  PTS";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Please Enter Valid Profile Name");
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_Valid_ProfileName_With_DoubleSpace_InBack(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "  PTS";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Please Enter Valid Profile Name");
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_Valid_ProfileName_With_TripleSpace_InBetween(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "PTS   India";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Please Enter Valid Profile Name");
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_Valid_ProfileName_With_UnderScore_InFront(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "_PTsIndia";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Please Enter Valid Profile Name");
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_Valid_ProfileName_With_UnderScore_InFront_AndBack(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "_PTsIndia_";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Please Enter Valid Profile Name");
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_Valid_ProfileName_With_Numbers_InFront(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "123PTS";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Please Enter Valid Profile Name");
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_Valid_ProfileName_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "_123PTS_";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Please Enter Valid Profile Name");
    }

    [Theory]
    [AutoWorkflowProfileInfoData]
    public async Task Verify_UpdateWorkflowProfileInfoCommandValidator_Valid_ProfileName_With_UnderScore_InFront_AndNumbers_InBack(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        updateWorkflowProfileInfoCommand.ProfileName = "_PTS123";
        updateWorkflowProfileInfoCommand.Id = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.ProfileId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateWorkflowProfileInfoCommand.WorkflowId = Guid.NewGuid().ToString();

        var result = await _validator.TestValidateAsync(updateWorkflowProfileInfoCommand);
        result.ShouldHaveValidationErrorFor(x => x.ProfileName)
            .WithErrorMessage("Please Enter Valid Profile Name");
    }
}