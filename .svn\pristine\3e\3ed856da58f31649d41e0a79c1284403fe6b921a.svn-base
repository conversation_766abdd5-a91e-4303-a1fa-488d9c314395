﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessServiceId;

public class GetDashboardViewByBusinessServiceIdQueryHandler : IRequestHandler<
    GetDashboardViewByBusinessServiceIdQuery, List<DashboardViewByBusinessServiceIdVm>>
{
    private readonly IDashboardViewRepository _dashboardViewRepository;
    private readonly IMapper _mapper;

    public GetDashboardViewByBusinessServiceIdQueryHandler(IMapper mapper,
        IDashboardViewRepository dashboardViewRepository)
    {
        _mapper = mapper;
        _dashboardViewRepository = dashboardViewRepository;
    }

    public async Task<List<DashboardViewByBusinessServiceIdVm>> Handle(
        GetDashboardViewByBusinessServiceIdQuery request, CancellationToken cancellationToken)
    {
        var dataLagStatus =
            (await _dashboardViewRepository.GetBusinessViewListByBusinessServiceId(request.BusinessServiceId))
            .ToList();

        return dataLagStatus.Count <= 0
            ? new List<DashboardViewByBusinessServiceIdVm>()
            : _mapper.Map<List<DashboardViewByBusinessServiceIdVm>>(dataLagStatus);
    }
}