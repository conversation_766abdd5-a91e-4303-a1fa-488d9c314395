using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DriftProfileFixture : IDisposable
{
    public List<DriftProfile> DriftProfilePaginationList { get; set; }
    public List<DriftProfile> DriftProfileList { get; set; }
    public DriftProfile DriftProfileDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string Name = "TestDriftProfile";
    public const string Description = "Test Drift Profile Description";
    public const string ProfileType = "Standard";

    public ApplicationDbContext DbContext { get; private set; }

    public DriftProfileFixture()
    {
        var fixture = new Fixture();

        DriftProfileList = fixture.Create<List<DriftProfile>>();

        DriftProfilePaginationList = fixture.CreateMany<DriftProfile>(20).ToList();

        DriftProfilePaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftProfilePaginationList.ForEach(x => x.IsActive = true);


        DriftProfileList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftProfileList.ForEach(x => x.IsActive = true);
      

        DriftProfileDto = fixture.Create<DriftProfile>();
        DriftProfileDto.ReferenceId = Guid.NewGuid().ToString();
        DriftProfileDto.IsActive = true;
        DriftProfileDto.Name = Name;
       

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
