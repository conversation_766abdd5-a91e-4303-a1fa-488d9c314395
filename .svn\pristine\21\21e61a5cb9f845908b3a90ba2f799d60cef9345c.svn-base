﻿@model ContinuityPatrol.Domain.ViewModels.CyberAlertModel.CyberAlertViewModel;
@using Microsoft.AspNetCore.Mvc.TagHelpers
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-alert-dashboard"></i><span>Cyber Resiliency Alert</span></h6>
            <form class="d-flex gap-2">

                <div class="input-group" style="width:200px">
                    <span class="form-label mb-0 input-group-text">Alert&nbsp;Priority</span>
                    <select id="Alert_Priority" class="form-select managealert" data-placeholder="Select Alert Priority">
                        <option value="All">All</option>
                        <option value="High">High</option>
                        <option value="Low">Low</option>
                        <option value="Critical">Critical</option>
                        <option value="Information">Information</option>
                    </select>
                </div>
                <div class="input-group" style="width:200px">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <span class="form-label mb-0 input-group-text"><i class="cp-search"></i></span>
                </div>
            </form>
        </div>
        <div class="card-body pt-0">
            <table class="datatable table table-hover dataTable no-footer" id="tblAlert" style="width:100%">
                <thead>
                    <tr>
                        <th>
                            Sr.No
                        </th>
                        <th>Alert Name</th>
                        <th>Alert Message</th>
                        <th>Alert Priority</th>
                    </tr>
                </thead>
                <tbody>
                
                </tbody>
            </table>
        </div>
    </div>
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<script src="~/js/CyberResiliency/Alert/Alert.js"></script>


<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <form asp-controller="UserMapping" asp-action="Delete" asp-route-id="textDeleteId" method="post" enctype="multipart/form-data" class="w-100">
            <div class="modal-content">

                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body   text-center pt-0">
                    <h4>Are you sure?</h4>
                    <p>You want to delete <span class="font-weight-bolder text-primary" id="deleteData"></span> data?</p>
                    <input id="textDeleteId" name="id" class="form-control d-none" />
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="confirmDeleteButton">Yes</button>
                </div>
            </div>
        </form>
    </div>
</div>
