﻿using ContinuityPatrol.Application.Features.Report.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Commands;

public class UpdateReportTests : IClassFixture<ReportFixture>
{
    private readonly ReportFixture _reportFixture;

    private readonly Mock<IReportRepository> _mockReportRepository;

    private readonly UpdateReportCommandHandler _handler;

    public UpdateReportTests(ReportFixture reportFixture)
    {
        _reportFixture = reportFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockReportRepository = ReportRepositoryMocks.UpdateReportRepository(_reportFixture.Reports);

        _handler = new UpdateReportCommandHandler(_reportFixture.Mapper, _mockReportRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidReport_UpdateReferenceIdAsync_ToReportsRepo()
    {
        _reportFixture.UpdateReportCommand.Id = _reportFixture.Reports[0].ReferenceId;

        var result = await _handler.Handle(_reportFixture.UpdateReportCommand, CancellationToken.None);

        var report = await _mockReportRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_reportFixture.UpdateReportCommand.Name, report.Name);
    }

    [Fact]
    public async Task Handle_Return_ValidReportResponse_WhenUpdate_Report()
    {
        _reportFixture.UpdateReportCommand.Id = _reportFixture.Reports[0].ReferenceId;

        var result = await _handler.Handle(_reportFixture.UpdateReportCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateReportResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_reportFixture.UpdateReportCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        _reportFixture.UpdateReportCommand.Id = _reportFixture.Reports[0].ReferenceId;

        await _handler.Handle(_reportFixture.UpdateReportCommand, CancellationToken.None);

        _mockReportRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockReportRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.Report>()), Times.Once);
    }
}