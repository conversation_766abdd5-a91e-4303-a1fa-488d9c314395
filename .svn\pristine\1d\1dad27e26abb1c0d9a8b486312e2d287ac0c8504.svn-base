﻿namespace ContinuityPatrol.Application.Features.Setting.Commands.Update;

public class UpdateSettingCommandValidator : AbstractValidator<UpdateSettingCommand>
{
    private readonly ISettingRepository _settingRepository;

    public UpdateSettingCommandValidator(ISettingRepository settingRepository)
    {
        _settingRepository = settingRepository;

        RuleFor(s => s.<PERSON>ey)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}");

        RuleFor(s => s.SValue)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull();

        RuleFor(s => s.LoginUserId)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull();

        RuleFor(y => y)
            .NotNull()
            .MustAsync(VerifyGuid)
            .WithMessage("Id is invalid");
    }

    private Task<bool> VerifyGuid(UpdateSettingCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.Id, "Setting Id");

        return Task.FromResult(true);
    }
}