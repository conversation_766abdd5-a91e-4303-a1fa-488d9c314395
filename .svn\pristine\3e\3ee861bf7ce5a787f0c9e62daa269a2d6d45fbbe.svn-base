﻿using ContinuityPatrol.Application.Features.UserGroup.Events.Update;

namespace ContinuityPatrol.Application.Features.UserGroup.Commands.Update;

public class UpdateUserGroupCommandHandler : IRequestHandler<UpdateUserGroupCommand, UpdateUserGroupResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IUserGroupRepository _userGroupRepository;

    public UpdateUserGroupCommandHandler(IUserGroupRepository userGroupRepository, IMapper mapper, IPublisher publisher)
    {
        _userGroupRepository = userGroupRepository;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<UpdateUserGroupResponse> Handle(UpdateUserGroupCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _userGroupRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.UserGroup), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateUserGroupCommand), typeof(Domain.Entities.UserGroup));

        await _userGroupRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateUserGroupResponse
        {
            Message = Message.Update("User Group", eventToUpdate.GroupName),

            UserGroupId = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new UserGroupUpdatedEvent { Name = eventToUpdate.GroupName }, cancellationToken);

        return response;
    }
}