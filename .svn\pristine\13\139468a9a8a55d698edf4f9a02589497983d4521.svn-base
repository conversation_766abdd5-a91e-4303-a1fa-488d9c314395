﻿using ContinuityPatrol.Domain.ViewModels.ReplicationModel;

namespace ContinuityPatrol.Application.Features.Replication.Queries.GetList;

public class GetReplicationListQueryHandler : IRequestHandler<GetReplicationListQuery, List<ReplicationListVm>>
{
    private readonly IMapper _mapper;
    private readonly IReplicationViewRepository _replicationViewRepository;

    public GetReplicationListQueryHandler(IMapper mapper, IReplicationViewRepository replicationViewRepository)
    {
        _mapper = mapper;
        _replicationViewRepository = replicationViewRepository;
    }

    public async Task<List<ReplicationListVm>> Handle(GetReplicationListQuery request,
        CancellationToken cancellationToken)
    {
        var replications = (await _replicationViewRepository.ListAllAsync()).ToList();

        return replications.Count <= 0
            ? new List<ReplicationListVm>()
            : _mapper.Map<List<ReplicationListVm>>(replications);
    }
}