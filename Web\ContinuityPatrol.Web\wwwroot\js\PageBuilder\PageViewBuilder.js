
$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})



PageBuilderData()

let setProperties;


function PageBuilderData() {

  
    let dashboardViewId = sessionStorage.getItem("dashboardViewId")

    $.ajax({
        type: "GET",
        url: RootUrl + 'Admin/ConfigurePage/GetByReferenceId',
        dataType: "json",
        data: { id: dashboardViewId },
        traditional: true,
        success: function (result) {
            
            $("#ITViewBuilderList").empty()
            $("#monitoringTitle").text(result.message.name + " : ")

            let propertices = JSON.parse(result.message.properties)
            $("#ITViewBuilderList").append(propertices.pageDetails)
            
            $(".pageBuilderSetDesign").removeClass("border-dashed")
            $(".pageBuilderSetRowDesign").removeAttr("style")
            
            //$(".pageBuilderSetRowDesign").removeAttr("style")
            $("div").removeAttr("onclick")
            $("div").removeAttr("ondrop")
            $("span").removeAttr("onclick")
            $("span").removeAttr("ondrop")
            $(".iconcollection").hide()
            $(".tablerowcolumn").removeAttr("contenteditable")
            $("#ITViewBuilderList td i").removeClass("cp-images")
            let infraId = sessionStorage.getItem("infraobjectId")
            let datainfra = {}
            datainfra.infraId = infraId
            $.ajax({
                type: "GET",
                url: RootUrl + 'Admin/ITViewBuilder/GetDashboardViewListByInfraObjectId',
                dataType: "json",
                data: datainfra,
                traditional: true,
                success: function (result) {


                    if (result.success) {
                        let data = {}
                        data.monitorId = result.data.entityId
                        data.type = result.data.monitorType;
                        $.ajax({
                            type: "GET",
                            url: RootUrl + 'Monitor/Monitoring/GetMonitorServiceStatusByIdAndType',
                            dataType: "json",
                            data: data,
                            traditional: true,
                            success: function (result) {
                                if (result.success) {
                                    $("#infraName").text(result.data.infraObjectName)
                                    $("#modifiedTime").text(result.data.rpoGeneratedDate)
                                    let properties = JSON.parse(result.data.properties)
                                    setProperties = JSON.parse(result.data.properties)
                                    let infraobjectId = result.data.infraObjectId
                                  if (infraobjectId) {
                                        monitoringSolution(infraobjectId, data.type)
                                        propertiesData(properties);
                                        msSQLNLSServer(infraobjectId)
                                    }
                                    updateDOM(properties);
                                    
                                }
                            }


                        })
                    }
                }


            })



        }
    })
}

let DOMtype;
function updateDOM(properties, parentClass = "") {
    
    if (properties?.Type) {
        DOMtype = properties?.Type
    }
    for (let key in properties) {
        if (properties.hasOwnProperty(key)) {
            let className;
            if (DOMtype) {
                if (DOMtype.toLowerCase() == "pr") {
                    className = parentClass ? `.${key}` : `.${key}`;
                }
                else if (DOMtype.toLowerCase() == "neardr") {
                    className = parentClass ? `.DR_${key}` : `.DR_${key}`;
                }
                else {
                    className = parentClass ? `.${DOMtype}_${key}` : `.${DOMtype}_${key}`;
                }
            }
            else {
                 className = parentClass ? `.${key}` : `.${key}`;
            }
            let value = properties[key];
            $(className).empty();

            if (value == null || value == undefined || value === "") {
                $(className).text("NA");
                $(className).prev().addClass("text-danger");
            } else {
                //$(className).prev().removeClass("text-danger");
                if (typeof value === "object") {
                    // Recursively process nested objects
                    updateDOM(value, className);
                } else {
                    $(className).text(value);
                    let iconClass = getIconClass(value);
                        $(className).prev().addClass(iconClass);
                    if ($(className).attr("listdata")) {
                        updateDOMClass(setProperties, className)
                    }
                    }
            }
        }
    }
}


let DOMtypeClass;
function updateDOMClass(classProperties, parentName, parent = "") {
    

    if (classProperties?.Type) {
        DOMtypeClass = classProperties.Type
    }
    for (let key in classProperties) {
        if (classProperties.hasOwnProperty(key)) {

            let idName;
            if (DOMtypeClass) {
                if (DOMtypeClass.toLowerCase() == "pr") {
                    idName = parent ? `${key}` : `${key}`;
                }
                else if(DOMtype.toLowerCase() == "neardr") {
                    idName = parent ? `DR_${key}` : `DR_${key}`;
                }
                else {
                    idName = parent ? `${DOMtypeClass}_${key}` : `${DOMtypeClass}_${key}`;
                }
            }
            else {
                idName = parent ? `${key}` : `${key}`;
            }

            let value = classProperties[key];
            if (typeof value === "object") {
                // Recursively process nested objects
                updateDOMClass(value, parentName, idName);
            } else {
                let listdata = JSON.parse($(parentName).attr("listdata"))
                if (listdata && listdata.length != 0) {
                    listdata.forEach((data) => {
                        if (data.dataSet == idName) {
                            if (data.Time?.toLowerCase() === value.toLowerCase()) {
                                $(parentName).prev().addClass(data.Style);
                                $(parentName).prev().addClass(data.Icon);
                            }
                        }
                    })
                }
            }

        }
    }
}


$(document).on('click', '.siteListChange', function () {

    $(".siteListChange .nav-link").removeClass("active");
    $(this).find(".nav-link").addClass("active");
    let siteId = $(this)[0].id
    let getSiteName = $(`#${siteId} .siteName`).text()
    const foundArrays = findArrays(setProperties);
    let MonitoringModelProperties = foundArrays[0].find(d => d.Type === getSiteName);
    if (MonitoringModelProperties) {
        updateDOM(MonitoringModelProperties);
    }

    $('table thead th').each((index, data) => {
        let childrenText = data.children[0].children[0].textContent
        if (childrenText.toLowerCase() == "dr" || childrenText.toLowerCase() == "neardr") {
            data.children[0].children[0].textContent = getSiteName
        }
    })

});


function findArrays(obj) {
    const arrays = [];

    function traverse(current) {
        if (Array.isArray(current)) {
            arrays.push(current);
        } else if (typeof current === 'object' && current !== null) {
            for (let key in current) {
                traverse(current[key]);
            }
        }
    }

    traverse(obj);
    return arrays;
}


function setPropData(data, propSets) {
    propSets.forEach(properties => {
        bindProperties(data, properties);
    });
}

function propertiesData(data) {
     

    if (checkForNearDR(data)) {
        $("#Sitediv").show();
        $("#Sitediv").removeClass("d-none");
    }

}


function checkForNearDR(obj) {
    
    if (typeof obj !== 'object' || obj === null) return false;

    if (obj.hasOwnProperty('Type') && obj.Type.toLowerCase() === 'neardr') {
        return true;
    }

    for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
            const value = obj[key];
            if (typeof value === 'object') {
                if (checkForNearDR(value)) {
                    return true;
                }
            }
        }
    }

    return false;
}


function bindProperties(data, properties) {
    properties.forEach(property => {
        const value = data[property];
        const displayedValue = (value !== undefined || value !== '') ? checkAndReplace(value) : 'NA';

        let iconClass = getIconClass(displayedValue);

        // Displayed value with icon
        if (property !== 'PR_InstanceStartUpTime' && property !== 'DR_InstanceStartUpTime') {
            const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
            const mergeValue = `${iconHtml}${displayedValue}`;
            $(`#${property}`).html(mergeValue).attr('title', displayedValue);
        }
        if (property === 'PR_InstanceStartUpTime' || property === 'DR_InstanceStartUpTime') {
            if (displayedValue !== "NA") {
                const [date, time] = displayedValue.split(' ');
                const formattedValue = `${date}<br>${time}`;
                const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
                const mergeValue = `${iconHtml}${formattedValue}`;
                $(`#${property}`).html(mergeValue).attr('title', `${date} ${time}`);
            }
            else {
                const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
                const mergeValue = `${iconHtml}${displayedValue}`;
                $(`#${property}`).html(mergeValue).attr('title', displayedValue);
            }

        }
        //if (property === 'PR_Recovery_Status' || property === 'DR_Recovery_Status') {
        //    if (displayedValue !== "NA") {
        //        const part = displayedValue?.split('\t');
        //        const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
        //        const mergeValue = `${iconHtml}${part[1]}`;
        //        $(`#${property}`).html(mergeValue).attr('title', part[1]);
        //    }
        //}
    });
}





async function msSQLNLSServer(id) {

    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
    let data = {}
    data.infraObjectId = id;
    let mssqlServerData = await getAysncWithHandler(url, data);

    if (mssqlServerData != null && mssqlServerData?.length > 0) {
        $('#mssqlserver').show();
        $('#mssqlserverbody').empty()
        bindMSSQLServer(mssqlServerData)
    } else {
        $('#mssqlserver').hide();
    }

}

function bindMSSQLServer(mssqlServerData) {
    
    const rowsValue = mssqlServerData?.map((list, i) => {
        const serverName = checkAndReplace(list?.servicePath === null ? list.workflowName : list.workflowName === null ? list.servicePath : "NA");
        const ipAddress = checkAndReplace(list?.ipAddress);
        const status = checkAndReplace(list?.isServiceUpdate);
        const iconServer = serverName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";
        const iconIp = ipAddress === "NA" ? "text-danger cp-disable" : "text-secondary cp-ip-address";
        const iconStatus = status?.toLowerCase() === "running" ? "text-success cp-reload cp-animate" : "error" ? "text-danger cp-fail-back" : "text-danger cp-disable";

        return `<tr><td><i class="${iconServer} me-1 fs-6"></i><span>${serverName}</span></td><td><i class="${iconIp} me-1 fs-6"></i>${ipAddress}</td><td><i class="${iconStatus} me-1 fs-6"></i>${status}</td></tr>`;
    }).join('');

    $('#mssqlserverbody').append(rowsValue);

}

function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}


function getIconClass(displayedValue) {

    switch (displayedValue.toLowerCase()) {
        case 'na':
        case 'no':
        case 'not allowed':
        case 'disabled':
        case 'disable': return 'text-danger cp-disable';
        case 'manual': return 'text-warning cp-settings';
        case 'healthy': return 'text-success cp-health-success';
        case 'nothealthy':
        case 'not_healthy':
        case 'unhealthy': return 'text-danger cp-health-error';
        case 'online': return 'text-success cp-online';
        case 'offline': return 'text-danger cp-offline';
        case 'primary': return 'text-primary cp-list-prsite';
        case 'secondary': return 'text-info cp-dr';
        case 'physical standby': return 'text-info cp-physical-drsite';
        case 'connected':
        case 'connect': return 'text-success cp-connected';
        case 'disconnected':
        case 'disconnect': return 'text-danger cp-disconnected';
        case 'synchronous_commit':
        case 'synchronized':
        case 'synchronizing':
        case 'sync': return 'text-success cp-refresh';
        case 'asynchronous_commit':
        case 'notsynchronizing ':
        case 'notsynchronized':
        case 'not':
        case 'not synchronized':
        case 'not synchronizing':
        case 'asynchronizing':
        case 'asynchronized':
        case 'async': return 'text-danger cp-refresh';
        case 'pending': return 'text-warning cp-pending';
        case 'running':
        case 'run': return 'text-success cp-reload cp-animate';
        case 'error': return 'text-danger cp-fail-back';
        case 'stopped':
        case 'stop': return 'text-danger cp-Stopped';
        case 'standby':
        case 'to standby':
        case 'mounted': return 'text-warning cp-control-file-type';
        case 'enabled':
        case 'enable': return 'text-success cp-end';
        case 'true':
        case 'yes':
        case 'valid': return 'text-success cp-success';
        case 'false':
        case 'defer':
        case 'deferred': return 'text-danger cp-error';
        case 'pause':
        case 'paused': return 'text-warning cp-circle-pause';
        case 'required':
        case 'require': return 'text-warning cp-warning';
        case 'on': return 'text-success cp-end';
        case 'off': return 'text-danger cp-end';
        case 'current':
        case 'read write':
            return 'text-success cp-file-edits';
        case 'up':
            return 'cp-up-linearrow text-success';
        case 'down':
            return 'cp-down-linearrow text-danger';
        case displayedValue.includes('running'):

            if (displayedValue.includes('running')) {
                return iconClass = 'text-success cp-reload cp-animate';
            } else if (displayedValue.includes('production') || displayedValue.includes('archive recovery')) {
                return iconClass = 'text-warning cp-log-archive-config';
            }
        default:
            return '';
    }
}


function displayASM(val, target, element) {
    try {
        if (!val) {
            $(element)
                .css('text-align', 'center')
                .html(asmNoDataimg);
        } else {
            let data = val.replace(/,(?=\s*[\]}])/, '');
            let asmVal = JSON?.parse(data);
            if (asmVal?.length > 0) {
                const asmRows = asmVal.map((list, i) => `<tr><td>${i + 1}</td><td>${list.NAME}</td><td>${list.STATE}</td><td>${list.TYPE}</td><td>${list.TOTAL_MB}</td><td>${list.FREE_MB}</td><td>${list['USED(%)']}</td></tr>`).join('');
                $(target).append(asmRows);
            }
            else {
                $(element)
                    .css('text-align', 'center')
                    .html(asmNoDataimg);
            }
        }
    } catch (error) {
        $(element)
            .css('text-align', 'center')
            .html(asmNoDataimg);
        //notificationAlert("warning", "Invalid JSON Format");
        //setTimeout(() => {
        //    window.location.assign('/Dashboard/ITResiliencyView/List');
        //}, 3000)
    }
}