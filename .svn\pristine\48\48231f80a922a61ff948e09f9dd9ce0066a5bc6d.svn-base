{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "None",
      "Microsoft.AspNetCore.Hosting.Diagnostics": "None",
      "Microsoft.EntityFrameworkCore": "None"
    }
  },
  "ApiSettings": {
    "ApiUrl": "https://***********:1000/"
  },
  "SeqConfig": {
    "ServerUrl": "http://***********:5341",
    "ApiKey": "YNgDU0FtfvHkChDdO2xP",
    "Username": "eP9LNIYSBVwVV4WQ6MducnR//dnewZrRbWBEmjjVHEA=$H3r86NBjEQ3rGpB0mqTxQg8jzo5ghek6mHeq3N+a8vrZ",
    "Password": "drXltNHflna07X6TalHC08EBRH3HKFuTNroCm4LVD7k=$LZKU8c5Upf+0Pt1XGTkmgARhE3Ccf81YC+ClrkH8daXv55AT+Q==",
    "path": "C:\\CP\\Logs\\SeqLogs\\"
  },
  "SignalR": {
    "Url": "https://localhost:7079/"
  },
  "ChatBotURL": {
    "Url": "https://***********:8083/"
  },
  "CP": {
    "Version": "6.0"
  },
  "UpdateApiVersion": {
    "Version": "6.0.0"
  },
  "ConnectionStrings": {
    "DBProvider": "RwGXpcWb0H2uQm3TsIZ48Q==",
    "Default": "lrZ2POAx2F8ZAhR92IT8uowL0iEYTIrGmc6ll7h+12SdwXRoj/DU65uCn1PcObu3jCvR+8MI9G455GKXle3D5IZ2FOeGzljwXfivE8RkJrim48+1PjOXAgwQiY/67vFhvcsycV/oaGC2LS1OVrtXNoZYoizJKtvw8j8rOWZMEMVPUqXPSxR2Z85r6AaFM6fKwvls1t7S6UL2kncu1RPJFuqxw03KFC6O6Q9e6ZxSIJ4="
  },
  "DatabaseSettings": {
    "CommandTimeoutSeconds": 180
  },
  //"IpRateLimiting": {
  //  "EnableEndpointRateLimiting": true,
  //  "StackBlockedRequests": false,
  //  "RealIpHeader": "X-Real-IP",
  //  "ClientIdHeader": "X-ClientId",
  //  "GeneralRules": [
  //    {
  //      "Endpoint": "*",
  //      "Period": "5m",
  //      "Limit": 100000
  //    }
  //  ]
  //},
  //"IpRateLimitPolicies": {
  //  "ClientIdRules": [
  //    {
  //      "Ip": "127.0.0.1",
  //      "Rules": [
  //        {
  //          "Endpoint": "*",
  //          "Period": "5m",
  //          "Limit": 100000
  //        }
  //      ]
  //    }
  //  ]
  //},
  "x-api-key": "pgH7QzFHJx4w46fI~5Uzi4RvtTwlEXp",
  "DataProvider": {
    "Default": "db"
  },
  "Policies": {
    "Default": "localhost"
  },
  "App": {
    "CorsOrigins": "https://localhost:7079/,https://localhost:7079,http://localhost:3001,http://subdomain1.localhost:3000,http://subdomain2.localhost:3000"
  },
  "CacheSettings": {
    "SlidingExpiration": 60
  },
  "JwtSettings": {
    "Key": "84322CFB66934ECC86D547C5CF4F2EFC",
    "Issuer": "http://localhost:8047",
    "Audience": "http://localhost:8047",
    "DurationInMinutes": 60
  },
  "Serilog": {
    "Using": [
      "Serilog.Sinks.File",
      "Serilog.Enrichers.ClientInfo"
    ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Error",
        "System": "Error",
        "Quartz": "Error"
      }
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "C:\\CP\\CP_Web_log-.txt",
          "fileSizeLimitBytes": "524288000",
          "rollOnFileSizeLimit": true,
          "retainedFileCountLimit": null,
          "rollingInterval": "Day",
          "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{UserName}] [{UserRole}] [{Level:u3}] : [{ClientIp}/{MachineName}/{ThreadId}] - {Message}{NewLine}{Exception}"
        }
      },
      {
        "Name": "Logger",
        "Args": {
          "configureLogger": {
            "Filter": [
              {
                "Name": "ByIncludingOnly",
                "Args": {
                  "expression": "UserRole = 'SiteAdmin'"
                }
              }
            ],
            "WriteTo": [
              {
                "Name": "File",
                "Args": {
                  "path": "C:\\CP\\Logs\\SiteAdminLogs-.txt",
                  "fileSizeLimitBytes": "524288000",
                  "rollOnFileSizeLimit": true,
                  "retainedFileCountLimit": null,
                  "rollingInterval": "Day",
                  "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{UserName}] [{UserRole}] [{Level:u3}] : [{ClientIp}/{MachineName}/{ThreadId}] - {Message}{NewLine}{Exception}"
                }
              }
            ]
          }
        }
      }
    ],
    "Enrich": [
      "WithClientIp",
      "WithMachineName",
      "WithThreadId"
    ]
  },

  "RedisCacheUrl": "127.0.0.1:6379,abortConnect=false,connectTimeout=30000,responseTimeout=30000",
  "AllowedHosts": "*"
}