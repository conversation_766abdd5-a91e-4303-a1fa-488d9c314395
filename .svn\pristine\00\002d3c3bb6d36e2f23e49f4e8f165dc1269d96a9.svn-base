﻿using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;

namespace ContinuityPatrol.Web.Areas.ITAutomation.Controllers;

[Area("ITAutomation")]
public class WorkflowExecutionTempController : BaseController
{
    private readonly ILogger<WorkflowExecutionTempController> _logger;
    private readonly IDataProvider _dataProvider;

    public WorkflowExecutionTempController(IDataProvider dataProvider, ILogger<WorkflowExecutionTempController> logger)
    {
        _logger = logger;
        _dataProvider = dataProvider;
    }

    [HttpGet]
    public async Task<JsonResult> GetWorkflowExecutionTempByGroupId(string workflowGroupId)
    {
        _logger.LogDebug("Entering  Get Workflow Execution Temp By GroupId method in Workflow Execution Temp");

        try
        {
            var workflow = await _dataProvider.WorkflowExecutionTemp.GetWorkflowExecutionTempById(workflowGroupId);

            _logger.LogDebug($"Successfully retrieved Workflow Execution Temp details for GroupId :{workflowGroupId} in Workflow Execution Temp page");

            // var workflow = workflowGroupId == null ? await _dataProvider.Workflow.GetWorkflowActionByWorkflowIdAndGroupId(id, "") : await _dataProvider.Workflow.GetWorkflowActionByWorkflowIdAndGroupId(workflowGroupId, id);
            return Json(workflow);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Workflow Execution Temp page while retrieving the Workflow Execution Temp details by groupId.", ex);

            return ex.GetJsonException();
        }
    }
}