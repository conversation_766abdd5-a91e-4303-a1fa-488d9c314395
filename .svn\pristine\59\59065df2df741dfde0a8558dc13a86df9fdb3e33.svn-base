﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}



<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title">
            <i class="cp-monitoring"></i>
            <span>
                Azure Storage Account Detail Monitoring:
                <span id="infraName"></span>
            </span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>

    </div>
    <div class="monitor_pages mb-2">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-3 mt-0">
            <div class="col-6">
                <div class="card Card_Design_None" id="mssqlserver">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title="Azure Storage Account Replication Monitoring">
                            Azure Storage Account Replication Monitoring
                        </span>

                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" id="tableCluster" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>Storage Account Location (Primary/Secondary)</th>
                                    <th class="text-primary">Primary</th>
                                    <th class="text-secondary" id="customSite">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><i class="text-secondary cp-folder-file me-1"></i>Location (Region)</td>
                                    <td class="text-truncate"><i class="cp-location me-1 text-primary"></i><span id="Location"></span></td>
                                    <td class="text-truncate"><i class="cp-location me-1 text-primary"></i><span id="DR_Location"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-disk-controller me-1"></i>Disk state</td>
                                    <td><i class="text-success cp-success me-1"></i><span id="Diskstate"></span></td>
                                    <td><i class="text-success cp-success me-1"></i><span id="DR_Diskstate"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-replication-on me-1"></i>Geo Replication Type</td>
                                    <td colspan="2"><i class="text-primary cp-replication-type me-1"></i><span id="GeoReplicationType"></span></td>

                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-disk-controller me-1"></i>Failover Progress State (NA/In Progress)</td>
                                    <td colspan="2"><i class="text-danger cp-not-applicable me-1"></i><span id="FailoverProgressState"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-disconnected me-1"></i>Last Geo Failover Time (UTC)</td>
                                    <td colspan="2"><i class="text-primary cp-calendar me-1"></i><span id="LastGeoFailoverTime"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-asynchronous me-1"></i>Last Sync Time (UTC)</td>
                                    <td colspan="2"><i class="text-primary cp-calendar me-1"></i><span id="LastSyncTime"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-transport-lag me-1"></i>Data Lag in Minutes</td>
                                    <td colspan="2"><i class="text-success cp-estimated-time me-1"></i><span id="DataLag"></span></td>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None">
                    <div class="card-header card-title" title="Solution Diagram">Solution Diagram</div>
                    <div class="d-grid card-body">
                        <div id="Solution_Diagram"></div>
                    </div>
                </div>
                <div class="card Card_Design_None">
                    <div class="card-header card-title" title="Solution Diagram">Azure Storage Account Monitoring</div>
                    <div class="d-grid card-body">
                        <table class="table mb-0" id="tableCluster" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>Azure Storage</th>
                                    <th class="text-primary">Primary</th>

                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><i class="text-secondary cp-server-cloud me-1"></i>Azure Storage Account Monitoring</td>
                                    <td class="text-truncate"><i class="cp-stand-storage me-1 text-primary"></i><span id="StorageAccountName"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-Storage-three me-1"></i>Storage Account Resource Group Name</td>
                                    <td class="text-truncate"><i class="cp-storage-name me-1 text-primary"></i><span id="StorageAccountResourceGroupName"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-location me-1"></i>Storage Account Location</td>
                                    <td class="text-truncate"><i class="cp-location me-1 text-primary"></i><span id="StorageAccountLocation"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-Storage-chain me-1"></i>Provisioning state</td>
                                    <td class="text-truncate"><i class="text-success cp-success me-1"></i><span id="Provisioningstate"></span></td>
                                </tr>

                            </tbody>

                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 d-grid">
            <div class="card Card_Design_None mb-2 h-100" id="mssqlserver">
                <div class="card-header card-title" title="Service/Process/Workflow">Service/Process/Workflow</div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0 noDataimg" style="table-layout:fixed" id="tableCluster">
                        <thead class="align-middle">
                            <tr>
                                <th rowspan="2">Service / Process / Workflow Name</th>
                                <th colspan="2" class="text-center">Server IP/HostName</th>
                            </tr>
                            <tr>
                                <th id="prIp"></th>
                                <th id="drIp"></th>
                            </tr>
                        </thead>
                        <tbody id="mssqlserverbody">
                        </tbody>

                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>
<script src="~/js/Monitoring/MonitoringStorageAzure.js"></script>
<script src="~/js/Monitoring/MonitoringServiceDetails.js"></script>
