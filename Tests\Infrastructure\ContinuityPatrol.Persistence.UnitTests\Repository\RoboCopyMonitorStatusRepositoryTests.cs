using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RoboCopyMonitorStatusRepositoryTests : IClassFixture<RoboCopyMonitorStatusFixture>
{
    private readonly RoboCopyMonitorStatusFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly RoboCopyMonitorStatusRepository _repository;

    public RoboCopyMonitorStatusRepositoryTests(RoboCopyMonitorStatusFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new RoboCopyMonitorStatusRepository(_dbContext);
    }

    #region GetRoboCopyStatusByInfraObjectIdAsync Tests

    [Fact]
    public async Task GetRoboCopyStatusByInfraObjectIdAsync_ShouldReturnRoboCopyMonitorStatus_WhenValidInfraObjectIdExists()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var roboCopyStatus = _fixture.CreateRoboCopyMonitorStatusWithInfraObjectId(infraObjectId);
        
        _dbContext.RoboCopyMonitorStatus.Add(roboCopyStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRoboCopyStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal(roboCopyStatus.Type, result.Type);
        Assert.Equal(roboCopyStatus.WorkflowId, result.WorkflowId);
    }

    [Fact]
    public async Task GetRoboCopyStatusByInfraObjectIdAsync_ShouldReturnNull_WhenInfraObjectIdDoesNotExist()
    {
        // Arrange
        var nonExistentInfraObjectId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetRoboCopyStatusByInfraObjectIdAsync(nonExistentInfraObjectId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetRoboCopyStatusByInfraObjectIdAsync_ShouldReturnFirstMatch_WhenMultipleRecordsExist()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var roboCopyStatus1 = _fixture.CreateRoboCopyMonitorStatusWithInfraObjectId(infraObjectId);
        var roboCopyStatus2 = _fixture.CreateRoboCopyMonitorStatusWithInfraObjectId(infraObjectId);
        
        roboCopyStatus1.CreatedDate = DateTime.UtcNow.AddDays(-1);
        roboCopyStatus2.CreatedDate = DateTime.UtcNow;

        _dbContext.RoboCopyMonitorStatus.AddRange(roboCopyStatus1, roboCopyStatus2);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetRoboCopyStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should return one of the matching records
        Assert.True(result.ReferenceId == roboCopyStatus1.ReferenceId || result.ReferenceId == roboCopyStatus2.ReferenceId);
    }


    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddRoboCopyMonitorStatus_WhenValidEntity()
    {
        // Arrange
        var roboCopyStatus = _fixture.RoboCopyMonitorStatusDto;
        roboCopyStatus.Type = "RoboCopy";
        roboCopyStatus.InfraObjectId = Guid.NewGuid().ToString();
        roboCopyStatus.InfraObjectName = "Test Infra Object";
        roboCopyStatus.WorkflowId = Guid.NewGuid().ToString();
        roboCopyStatus.WorkflowName = "Test Workflow";

        // Act
        var result = await _repository.AddAsync(roboCopyStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(roboCopyStatus.Type, result.Type);
        Assert.Equal(roboCopyStatus.InfraObjectId, result.InfraObjectId);
        Assert.Equal(roboCopyStatus.InfraObjectName, result.InfraObjectName);
        Assert.Equal(roboCopyStatus.WorkflowId, result.WorkflowId);
        Assert.Equal(roboCopyStatus.WorkflowName, result.WorkflowName);
        Assert.Single(_dbContext.RoboCopyMonitorStatus);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var roboCopyStatus = _fixture.RoboCopyMonitorStatusDto;
        _dbContext.RoboCopyMonitorStatus.Add(roboCopyStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(roboCopyStatus.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(roboCopyStatus.Id, result.Id);
        Assert.Equal(roboCopyStatus.InfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var roboCopyStatuses = _fixture.RoboCopyMonitorStatusList;
        _dbContext.RoboCopyMonitorStatus.AddRange(roboCopyStatuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(roboCopyStatuses.Count, result.Count);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_WhenExists()
    {
        // Arrange
        var roboCopyStatus = _fixture.RoboCopyMonitorStatusDto;
        _dbContext.RoboCopyMonitorStatus.Add(roboCopyStatus);
        await _dbContext.SaveChangesAsync();

        var updatedThreshold = "Updated Threshold";
        roboCopyStatus.Threshold = updatedThreshold;

        // Act
        var result = await _repository.UpdateAsync(roboCopyStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(updatedThreshold, result.Threshold);
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity_WhenExists()
    {
        // Arrange
        var roboCopyStatus = _fixture.RoboCopyMonitorStatusDto;
        _dbContext.RoboCopyMonitorStatus.Add(roboCopyStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(roboCopyStatus);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(roboCopyStatus.Id);
        Assert.Null(deletedEntity);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.RoboCopyMonitorStatus.RemoveRange(_dbContext.RoboCopyMonitorStatus);
        await _dbContext.SaveChangesAsync();
    }
}
