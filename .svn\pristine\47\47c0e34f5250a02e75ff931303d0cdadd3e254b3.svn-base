﻿using ContinuityPatrol.Application.Features.WorkflowAction.Commands.SaveAs;
using ContinuityPatrol.Application.Features.WorkflowAction.Events.SaveAs;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Commands
{
    public class SaveAsWorkflowActionTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<IVersionManager> _mockVersionManager;
        private readonly Mock<IWorkflowActionRepository> _mockWorkflowActionRepository;
        private readonly SaveAsWorkflowActionCommandHandler _handler;

        public SaveAsWorkflowActionTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockPublisher = new Mock<IPublisher>();
            _mockVersionManager = new Mock<IVersionManager>();
            _mockWorkflowActionRepository = new Mock<IWorkflowActionRepository>();

            _handler = new SaveAsWorkflowActionCommandHandler(
                _mockMapper.Object,
                _mockWorkflowActionRepository.Object,
                _mockVersionManager.Object,
                _mockPublisher.Object
            );
        }

        [Fact]
        public async Task Handle_Should_SaveWorkflowAction_When_DataIsValid()
        {
            var command = new SaveAsWorkflowActionCommand
            {
                WorkflowActionId = Guid.NewGuid().ToString(),
                Name = "New Workflow Action",
                Version = "v1.0"
            };

            var existingAction = new Domain.Entities.WorkflowAction
            {
                ReferenceId = command.WorkflowActionId,
                ActionName = "Old Workflow Action"
            };

            var version = "v1.1";

            _mockWorkflowActionRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.WorkflowActionId))
                .ReturnsAsync(existingAction);

            _mockVersionManager
                .Setup(vm => vm.GetVersion(command.Version))
                .ReturnsAsync(version);

            _mockMapper
                .Setup(m => m.Map(command, existingAction, typeof(SaveAsWorkflowActionCommand), typeof(Domain.Entities.WorkflowAction)));

            _mockWorkflowActionRepository
                .Setup(repo => repo.AddAsync(existingAction))
                .ReturnsAsync(existingAction);

            var expectedMessage = $" WorkflowAction '{"New Workflow Action"}' has been created successfully";
            _mockPublisher
                .Setup(p => p.Publish(It.IsAny<WorkflowActionSaveAsEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(command.WorkflowActionId, result.Id);
            Assert.Equal(expectedMessage, result.Message);
            _mockWorkflowActionRepository.Verify(repo => repo.AddAsync(existingAction), Times.Once);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<WorkflowActionSaveAsEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_ThrowNotFoundException_When_WorkflowActionNotFound()
        {
            var command = new SaveAsWorkflowActionCommand
            {
                WorkflowActionId = Guid.NewGuid().ToString(),
                Name = "New Workflow Action",
                Version = "v1.0"
            };

            _mockWorkflowActionRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.WorkflowActionId))
                .ReturnsAsync((Domain.Entities.WorkflowAction)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
            _mockWorkflowActionRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.WorkflowAction>()), Times.Never);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<WorkflowActionSaveAsEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_Should_ThrowArgumentNullException_When_CommandIsNull()
        {
            await Assert.ThrowsAsync<ArgumentNullException>(() => _handler.Handle(null, CancellationToken.None));
        }
    }
}
