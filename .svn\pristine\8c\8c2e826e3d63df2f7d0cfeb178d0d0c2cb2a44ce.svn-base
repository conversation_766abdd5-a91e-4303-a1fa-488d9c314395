using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ArchiveRepository : BaseRepository<Archive>, IArchiveRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public ArchiveRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<Archive>> ListAllAsync()
    {
        var archives =await FilterRequiredField(base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))).ToListAsync();

        return archives;
    }
    public override async Task<PaginatedResult<Archive>> PaginatedListAllAsync(int pageNumber,int Pagesize,Specification<Archive> specifiaction, string sortColumn,string sortOrder)
    {
        return await FilterRequiredField(_loggedInUserService.IsParent
            ?   Entities.Specify(specifiaction).DescOrderById()
            :  Entities.Specify(specifiaction).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()).ToSortedPaginatedListAsync(pageNumber, Pagesize, sortColumn, sortOrder);
    }
    public override IQueryable<Archive> GetPaginatedQuery()
    {
        return FilterRequiredField(base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).AsNoTracking().OrderByDescending(x => x.Id));
    }

    public override async Task<Archive> GetByReferenceIdAsync(string id)
    {
        var archives = base.GetByReferenceId(id,
            archive => archive.CompanyId.Equals(_loggedInUserService.CompanyId) && archive.ReferenceId.Equals(id));

        return await archives.FirstOrDefaultAsync();
    }

    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.ArchiveProfileName.Equals(name))
            : Entities.Where(e => e.ArchiveProfileName.Equals(name)).ToList().Unique(id));
    }

    public async Task<bool> IsSolutionTypeExist(string profileName)
    {
        return await _dbContext.Archives.AsNoTracking()
            .AnyAsync(x => x.ArchiveProfileName.ToLower().Equals(profileName));
    }



    public Task<bool> IsTableNameExist(string tableName, string id)
    {
        var deserializedObjects = _dbContext.Archives
            .Select(e => JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(e.TableNameProperties))
            .ToList();       

        var result = deserializedObjects
            .SelectMany(list => list) 
            .Any(dict =>
                dict.ContainsKey("tableName") && dict.TryGetValue("tableName", out var name) &&
                name.ToLower() == tableName.ToLower());

        return Task.FromResult(result);       
    }

    public async Task<List<Archive>>GetByTableAccessId(string id)
    {
        return await FilterRequiredField(Entities.Where(x =>x.TableNameProperties.Contains(id))).ToListAsync();
    }
    private IQueryable<Archive>FilterRequiredField(IQueryable<Archive> archives)
    {
        return archives.Select(x=> new Archive
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            ArchiveProfileName = x.ArchiveProfileName,
            TableNameProperties = x.TableNameProperties,
            CompanyId = x.CompanyId,
            Count = x.Count,
            CronExpression = x.CronExpression,
            ScheduleTime = x.ScheduleTime,
            ScheduleType = x.ScheduleType,
            BackUpType = x.BackUpType,
            Type = x.Type,
            ClearBackup = x.ClearBackup,
            NodeId = x.NodeId,
            NodeName = x.NodeName,

        });
    }
}