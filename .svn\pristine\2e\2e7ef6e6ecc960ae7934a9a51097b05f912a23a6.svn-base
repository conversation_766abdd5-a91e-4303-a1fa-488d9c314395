﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-server-type"></i><span>Server Type</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" aria-expanded="false" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="FormTypeName=" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" id="createModalButton" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table class="datatable table table-hover no-footer" style="width:100%" id="formTypeTableData">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Name</th>
                        <th>Server Role</th>
                        <th class="Action-th">Action</th>
                    </tr>
                </thead>
                <tbody>
              
                </tbody>
            </table>
        </div>
    </div>
</div>

<!--Modal Create-->
<div class="modal fade" id="CreateModal" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered ">
        <div class="modal-content" >
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-server-type"></i><span>Server Type Configuration</span></h6>
                <button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label" for="formTypeName">Type</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-name"></i></span>
                        <input type="text" id="serverTypeName" class="form-control" maxlength="100"
                               placeholder="Enter Type" autocomplete="off" />
                    </div>
                    <span id="serverTypeName_error"></span>
                </div>
                <div class="form-group">
                    <label class="form-label" for="formTypeName">Server Role</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-server-role"></i></span>
                        <select data-placeholder="select server role" class="form-select-modal" id="serverTypeRole">
                            <option value=""></option>
                           
                        </select>
                        <span class="input-group-text">
                            <span type="button" id="ServerRolenameModal" data-bs-target="#ServerRoleModal" title="Server Role" data-bs-toggle="modal"><i class="cp-circle-plus text-primary"></i></span>
                        </span>
                    </div>
                    <span id="serverTyperole_error"></span>
                </div>

            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm" id="SaveFunction">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>



<!--Server Role Modal-->
<div class="modal fade" id="ServerRoleModal" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel2" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-server-role"></i><span>Server Role Configuration</span></h6>
                <button type="button" class="btn-close rolename_close" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label" for="RoleName">Role Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-name"></i></span>
                        <input type="text" class="form-control" maxlength="100"
                               placeholder="Enter Role Name" autocomplete="off" id="rolename" />
                    </div>
                    <span id="rolename_error"></span>
                </div>
                <div>
                    <label class="form-label fs-7 mb-2">Server Role List</label>
                    <div style="height:calc(100vh - 550px);overflow:auto">
                        <table class="table" id="rolenametable">
                            <thead class="position-sticky top-0 z-3">
                                <tr>
                                    <th>S.No</th>
                                    <th>Server Role</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                              

                            </tbody>
                        </table>
                    </div>
             
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm rolename_close" >Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm" id="rolenameSave">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Modal Delete-->
<div class="modal fade" id="rolenameDeleteModal" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/delete.png" />
            </div>
            <div class="modal-body text-center pt-0">
                <h4>Are you sure?</h4>
                <p class="d-flex align-items-center justify-content-center gap-1">
                    You want to delete the<span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="rolename_deleted_id"></span>
                    data?
                </p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="button" id="confirmrolenamedelete" class="btn btn-primary btn-sm">Yes</button>
            </div>
        </div>
    </div>

</div>
<div class="modal fade" id="DeleteModal" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div>
                <div class="modal-header p-0">
                    <img class="delete-img" src="~/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body text-center pt-0">
                    <h5 class="fw-bold">Are you sure?</h5>
                    <p class="d-flex align-items-center justify-content-center gap-1">You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="deleteData"></span> data?</p>

                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="confirmDeleteButton">Yes</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="~/js/siteadmin/form/servermapping/servermappping.js"></script>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>