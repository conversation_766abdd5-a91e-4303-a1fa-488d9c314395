﻿namespace ContinuityPatrol.Application.Features.OracleMonitorLogs.Queries.GetByType;

public class GetOracleMonitorLogsDetailByTypeQueryHandler : IRequestHandler<GetOracleMonitorLogsDetailByTypeQuery,
    List<OracleMonitorLogsDetailByTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly IOracleMonitorLogsRepository _oracleMonitorLogsRepository;

    public GetOracleMonitorLogsDetailByTypeQueryHandler(IOracleMonitorLogsRepository oracleMonitorLogsRepository,
        IMapper mapper)
    {
        _oracleMonitorLogsRepository = oracleMonitorLogsRepository;
        _mapper = mapper;
    }

    public async Task<List<OracleMonitorLogsDetailByTypeVm>> Handle(GetOracleMonitorLogsDetailByTypeQuery request,
        CancellationToken cancellationToken)
    {
        var oracleMonitorLogs = await _oracleMonitorLogsRepository.GetDetailByType(request.Type);

        return oracleMonitorLogs.Count <= 0
            ? new List<OracleMonitorLogsDetailByTypeVm>()
            : _mapper.Map<List<OracleMonitorLogsDetailByTypeVm>>(oracleMonitorLogs);
    }
}