﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class CompanyRepositoryMocks
{
    public static Mock<ICompanyRepository> CreateCompanyRepository(List<Company> companies)
    {
        var companyRepository = new Mock<ICompanyRepository>();

        companyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(companies);

        companyRepository.Setup(repo => repo.AddAsync(It.IsAny<Company>())).ReturnsAsync(
            (Company company) =>
            {
                company.Id = new Fixture().Create<int>();

                company.ReferenceId = new Fixture().Create<Guid>().ToString();

                companies.Add(company);

                return company;
            });

        return companyRepository;
    }

    public static Mock<ICompanyRepository> UpdateCompanyRepository(List<Company> companies)
    {
        var mockCompanyRepository = new Mock<ICompanyRepository>();

        mockCompanyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(companies);

        mockCompanyRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => companies.SingleOrDefault(x => x.ReferenceId == i));

        mockCompanyRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Company>())).ReturnsAsync((Company company) =>
        {
            var index = companies.FindIndex(item => item.ReferenceId == company.ReferenceId);

            companies[index] = company;

            return company;
        });

        return mockCompanyRepository;
    }

    public static Mock<ICompanyRepository> DeleteCompanyRepository(List<Company> companies)
    {
        var mockCompanyRepository = new Mock<ICompanyRepository>();

        mockCompanyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(companies);

        mockCompanyRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => companies.SingleOrDefault(x => x.ReferenceId == i));

        mockCompanyRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Company>())).ReturnsAsync((Company company) =>
        {
            var index = companies.FindIndex(item => item.ReferenceId == company.ReferenceId);

            company.IsActive = false;

            companies[index] = company;

            return company;
        });

        return mockCompanyRepository;
    }

    public static Mock<ICompanyRepository> GetCompanyRepository(List<Company> companies)
    {
        var companyRepository = new Mock<ICompanyRepository>();

        companyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(companies);

        companyRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => companies.SingleOrDefault(x => x.ReferenceId == i));

        return companyRepository;
    }

    public static Mock<ICompanyRepository> GetPaginatedCompanyRepository(List<Company> companies)
    {
        var companyRepository = new Mock<ICompanyRepository>();

        var queryableCompany = companies.BuildMock();

        companyRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableCompany);

        return companyRepository;
    }

    public static Mock<ICompanyRepository> GetCompanyNamesRepository(List<Company> companies)
    {
        var companyNamesRepository = new Mock<ICompanyRepository>();

        companyNamesRepository.Setup(repo => repo.GetAllCompanyNames()).ReturnsAsync(companies);

        return companyNamesRepository;
    }

    public static Mock<ICompanyRepository> GetDisplayNameUniqueRepository(List<Company> companies)
    {
        var displayNameUniqueRepository = new Mock<ICompanyRepository>();

        displayNameUniqueRepository.Setup(repo => repo.IsDisplayNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(
            (string i, string j) =>
            {
                return j == 0.ToString() ? companies.Exists(x => x.DisplayName == i) : companies.Exists(x => x.DisplayName == i && x.ReferenceId == j);
            }
        );

        return displayNameUniqueRepository;
    }

    public static Mock<ICompanyRepository> GetCompanyNameUniqueRepository(List<Company> companies)
    {
        var companyNameUniqueRepository = new Mock<ICompanyRepository>();

        companyNameUniqueRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) =>
        {
            return j == 0.ToString() ? companies.Exists(x => x.Name == i) : companies.Exists(x => x.Name == i && x.ReferenceId == j);
        });

        return companyNameUniqueRepository;
    }

    public static Mock<ICompanyRepository> GetCompanyEmptyRepository()
    {
        var mockCompanyRepository = new Mock<ICompanyRepository>();

        mockCompanyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Company>());

        return mockCompanyRepository;
    }


    //Events

    public static Mock<IUserActivityRepository> CreateCompanyEventRepository(List<UserActivity> userActivities)
    {
        var companyEventRepository = new Mock<IUserActivityRepository>();

        companyEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        companyEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return companyEventRepository;
    }
}