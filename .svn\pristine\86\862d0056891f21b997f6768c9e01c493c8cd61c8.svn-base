
// common update values
function updateTextContent(selectorValuePairs) {
    for (const [selector, value] of Object.entries(selectorValuePairs)) {
        $(selector)?.text(value || 0);
    }
}

// Operational/ workflow Analytics
const getOperationalAnalytics = async () => {

    try {
        const result = await $.ajax({
            type: "GET",
            url: RootUrl + "Dashboard/Analytics/GetWorkflowAnalytics",
            dataType: "json",
        });

        if (result?.success && result?.data) {

            updateTextContent({
                '#totalConfigured': result?.data?.totalConfigured,
                '#executedWorkflow': result?.data?.executedWorkFlowCount,
                '#successWorkflow': result?.data?.workFlowSuccessCount,
                '#errorWorkflow': result?.data?.workFlowErrorCount
            });

            loadWorkflowAnalyticsChart(result?.data)
                    
        } else {
             errorNotification(result);
        }
            
    } catch (error) {}

}

// Drill Analytics
const getOperationalReadiness = async () => {

    try {

        const result = await $.ajax({
            type: "GET",
            url: RootUrl + "Dashboard/Analytics/GetDrillAnalytics",
            dataType: "json",
        });
   
        if (result?.success && result?.data) {
             
            let html = '';
            let drillAnalytics = Array.isArray(result?.data?.drillAnalyticsDetailLists) ? result?.data?.drillAnalyticsDetailLists : []

            updateTextContent({
                '#totalDrConfigured': result?.data?.configuredProfileCount,
                '#totalDrProfiles': result?.data?.executedProfileCount,
                '#totalDrWorkflow': result?.data?.executedWorkflowCount
            });

            for (let i = 0; i < drillAnalytics?.length; i++) {

                  html += `<tr><td>
                    <div class="d-flex gap-2">
                           <small><i class="cp-wf-profile me-1 fs-7"></i>${drillAnalytics[i]?.profileTotalCount || 0}</small>
                            <small><i class="cp-check text-success me-1 fs-7"></i>${drillAnalytics[i]?.profileSuccessCount || 0}</small>
                            <small><i class="cp-close text-danger me-1" style="font-size:10px"></i>${drillAnalytics[i]?.profiledFailedCount || 0}</small>
                    </div></td>
                    <td>
                    <div class="d-flex gap-2">
                       <small><i class="cp-workflow-executed me-1 fs-7"></i>${drillAnalytics[i]?.workflowTotalCount || 0}</small>
                       <small><i class="cp-check text-success me-1 fs-7"></i>${drillAnalytics[i]?.workflowSuccessCount || 0}</small>
                       <small><i class="cp-close text-danger me-1" style="font-size:10px"></i>${drillAnalytics[i]?.workflowFailedCount || 0}</small>
                    </div></td>
                     <td><i class="cp-RTO me-1 fs-7"></i>${drillAnalytics[i]?.outOfRtoCount || 0}</td>
                     <td>${drillAnalytics[i]?.drillDate || 'NA'}</td>`;
            }

            $('#drActivityBody').append(html)                   
        } else {
           errorNotification(result);
        }

    } catch (error) {}
}

// component failure Analytics
const getOperationalFailure = async () => {

    try {

        const result = await $.ajax({
            type: "GET",
            url: RootUrl + "Dashboard/Analytics/GetComponentFailureAnalytics",
            dataType: "json",
        });

        if (result?.success && result?.data) {

            updateTextContent({
                '#totalComponent': result?.data?.totalComponent,
                '#componentsAffectedToday': result?.data?.componentsAffectedToday,
                '#availableCount': result?.data?.availableCount,
                '#failedCount': result?.data?.failedCount,
                '#totalServer': result?.data?.totalServerDatabaseCount,
                '#totalDatabase': result?.data?.totalDatabaseCount,
                '#totalStorage': result?.data?.totalStorageCount,
                '#totalReplication': result?.data?.totalReplicationCount,
                '#totalNetwork': result?.data?.totalNetworkCount,
                '#totalApplication': result?.data?.totalApplicationCount
            });
 
          loadComponentFailureChart(result?.data)
                    
        } else {
              errorNotification(result);
        }
    } catch (error) {}
}

// Operational service Availability 
const getAvailableOperations = async () => {

    try {
        const result = await $.ajax({
            type: "GET",
            url: RootUrl + "Dashboard/Analytics/GetOperationalAvailabilityAnalytics",
            dataType: "json",
        });

        if (result?.success && result?.data) {

          let siteList = Array.isArray(result?.data?.siteRunningListVm) ? result?.data?.siteRunningListVm : [];

            updateTextContent({
                '.totalBusiness': result?.data?.totalBusinessServiceCount,
                '#successBusiness': result?.data?.businessServiceSuccessCount,
                '#errorBusiness': result?.data?.businessServiceErrorCount,
                '#totalInfra': result?.data?.totalInfraObjectCount,
                '#successInfra': result?.data?.infraObjectSuccessCount,
                '#errorInfra': result?.data?.infraObjectErrorCount
            });

            loadOperationalServiceChart(siteList)
                    
        } else {
            errorNotification(result);
        }
    } catch (error) {}
}

// Operational Service Health Summary
const getHealthSummary = async () => {
    let html = '';

    try {
        const result = await $.ajax({
            type: "GET",
            url: RootUrl + "Dashboard/Analytics/GetOperationalHealthSummary",
            dataType: "json",
        });

        if (result?.success) {
            if (result?.data && Array.isArray(result?.data)) {
                let obj = {
                    totalOperation: result?.data?.length,
                    totalHealthy: 0,
                    totalUnHealthy: 0,
                    totalMaintenance: 0
                };

                for (let i = 0; i < result?.data?.length; i++) {

                    obj['totalHealthy'] += result?.data[i]?.healthyCount || 0;
                    obj['totalUnHealthy'] += result?.data[i]?.unHealthyCount || 0;
                    obj['totalMaintenance'] += result?.data[i]?.maintenanceCount || 0;

                    html += `<tr><td class="text-truncate">${result?.data[i]?.businessServiceName || 'NA'}</td>
                        <td class="text-truncate">
                            <span>
                                <i class="cp-health-success text-success"></i><span class="ms-1">${result?.data[i]?.healthyCount || 0}</span>
                            </span>
                            <span class="mx-2">
                                <i class="cp-health-error text-danger"></i><span class="ms-1">${result?.data[i]?.unHealthyCount || 0}</span>
                            </span>
                            <span>
                                <i class="cp-maintenance text-warning"></i><span class="ms-1">${result?.data[i]?.maintenanceCount || 0}</span>
                            </span>
                        </td>
                    </tr>`
                }

                $('#healthSummaryTable').append(html)
                loadHealthSummaryChart(obj)
            }
        } else {
            errorNotification(result);
        }
    } catch (error) {}
}

const getSlaBreach = async () => {

    try {
        const result = await $.ajax({
            type: "GET",
            url: RootUrl + "Dashboard/Analytics/GetSlaBreach",
            dataType: "json",
        });
    
        if (result?.success && result?.data) {

            updateTextContent({
                '#activeAlertCount': result?.data?.activeAlertCount,
                '#slaMeetingRtoCount': result?.data?.slaMeetingRtoCount
            });

            loadSlaChart(result?.data)
          
        } else {
            errorNotification(result);
        }
                   
    } catch (error) {}
}

const loadAnalyticsData = async () => {
    await getOperationalAnalytics();  
    await getOperationalReadiness();
    await getOperationalFailure();  
    await getAvailableOperations(); 
    await getHealthSummary();
    await getSlaBreach();  
};

loadAnalyticsData();
function loadHealthSummaryChart(data) {

    let chart = am4core.create("BusinessServiceHealthSummaryChart", am4charts.PieChart);
    if (chart?.logo) {
        chart.logo.disabled = true;
    }

    if (data?.totalHealthy === 0 && data?.totalUnHealthy === 0) {
        chart.data = [
            {
                "country": "No Data",
                "litres": 1,
                "color": am4core.color("#d3d3d3")
            }
        ]
    } else {
        chart.data = [
            {
                "country": "Healthy",
                "litres": data?.totalHealthy,
                "color": am4core.color("#40c200")
            },
            {
                "country": "Unhealthy",
                "litres": data?.totalUnHealthy,
                "color": am4core.color("#ff4191")
            }
        ];
    }

    let pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "litres";
    pieSeries.dataFields.category = "country";
    pieSeries.slices.template.propertyFields.fill = "color";
    pieSeries.slices.template.stroke = am4core.color("#fff");
    pieSeries.slices.template.strokeWidth = 5;
    pieSeries.slices.template.strokeOpacity = 5;
    pieSeries.slices.template.cornerRadius = 20;
    pieSeries.slices.template.innerCornerRadius = 20;

    // Let's cut a hole in our Pie chart the size of 40% the radius
    chart.innerRadius = am4core.percent(65);
    chart.padding(-10, 0, 0, -12);
    // Disable ticks and labels
    pieSeries.labels.template.disabled = true;
    pieSeries.ticks.template.disabled = true;

    if (data?.totalHealthy !== 0 || data?.totalUnHealthy !== 0) {
        pieSeries.slices.template.tooltipText = "{category}: {value}";
    } else {
        pieSeries.slices.template.tooltipText = "";
    }

    // Add a legend
    chart.legend = new am4charts.Legend();
    chart.legend.position = "bottom";
    chart.legend.valueLabels.template.disabled = true;
    chart.legend.labels.template.text = "[font-size:12px ]{name}";
    chart.legend.labels.template.fill = am4core.color("#6c757d");
    chart.legend.itemContainers.template.padding(8, 0, 0, 0);
    chart.legend.markers.template.children.getIndex(0).cornerRadius(30, 30, 30, 30);
    let markerTemplate = chart.legend.markers.template;
    markerTemplate.width = 10;
    markerTemplate.height = 10;

    let label3 = chart.seriesContainer.createChild(am4core.Label);

    if (data?.totalOperation === 0) {
        label3.text = `[bold]NA[/]`;
    } else {
        label3.text = `[bold]${data?.totalOperation}[/] \n [font-size:10px] \n Operational Services [/]`;
    }
  
    label3.horizontalCenter = "middle";
    label3.verticalCenter = "middle";
    label3.textAlign = "middle";
    label3.fontSize = 16;
}

function loadOperationalServiceChart(data) {

    let chart = am4core.create('ServiceAnalytics_Chart', am4charts.XYChart)
    //chart.colors.step = 2;
    if (chart?.logo) {
        chart.logo.disabled = true;
    }
    chart.padding(10, 0, -30, 0);
    chart.legend = new am4charts.Legend()
    chart.legend.position = 'right'
    chart.legend.paddingBottom = 20
    //chart.legend.labels.template.maxWidth = 95;

    let markerTemplate = chart.legend.markers.template;
    markerTemplate.width = 10;
    markerTemplate.height = 10;

    let valueAxisTitle = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxisTitle.title.text = "Servers Availability %";

    let xAxis = chart.xAxes.push(new am4charts.CategoryAxis())
    xAxis.paddingRight = 0;
    xAxis.dataFields.category = 'category'

    if (data.length === 1) {
        xAxis.renderer.cellStartLocation = 0; 
        xAxis.renderer.cellEndLocation = 0.3;   
    } else {
        xAxis.renderer.cellStartLocation = 0.1;
        xAxis.renderer.cellEndLocation = 0.9;
    }

    xAxis.renderer.grid.template.location = 0;

    let yAxis = chart.yAxes.push(new am4charts.ValueAxis());
    yAxis.paddingLeft = 0;
    yAxis.paddingRight = -30;
    yAxis.min = 0;

    function createSeries(value, name) {
        let series = chart.series.push(new am4charts.ColumnSeries())
        series.dataFields.valueY = value
        series.dataFields.categoryX = 'category'
        series.name = name
        series.events.on("hidden", arrangeColumns);
        series.events.on("shown", arrangeColumns);
        series.columns.template.column.cornerRadiusTopRight = 50;
        series.columns.template.column.cornerRadiusTopLeft = 50;
        series.columns.template.column.cornerRadiusBottomRight = 50;
        series.columns.template.column.cornerRadiusBottomLeft = 50;
        series.columns.template.width = am4core.percent(30);
        series.columns.template.tooltipText = "{name}: [bold]{valueY}[/]";
        let bullet = series.bullets.push(new am4charts.LabelBullet())
        bullet.interactionsEnabled = false
        bullet.dy = 30;
        bullet.label.text = '{valueY}'
        bullet.label.fill = am4core.color('#ffffff')
        return series;
    }

    chart.data = [
        {
            category: 'Site Availability',
        }
    ]

    for (let i = 0; i < data.length; i++) {
        chart.data[0][data[i]?.siteType] = data[i]?.totalRunningCount
    }

    chart.colors.list = [
        am4core.color("#ffc060"),
        am4core.color("#f76161"),
        am4core.color("#5cd17f"),
    ];

    for (let i = 0; i < data.length; i++) {
        createSeries(data[i]?.siteType, data[i]?.siteType);
    }

    function arrangeColumns() {
        let series = chart.series.getIndex(0);
        let w = 1 - xAxis.renderer.cellStartLocation - (1 - xAxis.renderer.cellEndLocation);
        if (series.dataItems.length > 1) {
            let x0 = xAxis.getX(series.dataItems.getIndex(0), "categoryX");
            let x1 = xAxis.getX(series.dataItems.getIndex(1), "categoryX");
            let delta = ((x1 - x0) / chart.series.length) * w;
            if (am4core.isNumber(delta)) {
                let middle = chart.series.length / 2;
                let newIndex = 0;
                chart.series.each(function (series) {
                    if (!series.isHidden && !series.isHiding) {
                        series.dummyData = newIndex;
                        newIndex++;
                    }
                    else {
                        series.dummyData = chart.series.indexOf(series);
                    }
                })
                let visibleCount = newIndex;
                let newMiddle = visibleCount / 2;
                chart.series.each(function (series) {
                    let trueIndex = chart.series.indexOf(series);
                    let newIndex = series.dummyData;
                    let dx = (newIndex - trueIndex + middle - newMiddle) * delta
                    series.animate({ property: "dx", to: dx }, series.interpolationDuration, series.interpolationEasing);
                    series.bulletsContainer.animate({ property: "dx", to: dx }, series.interpolationDuration, series.interpolationEasing);
                })
            }
        }
    }
}

