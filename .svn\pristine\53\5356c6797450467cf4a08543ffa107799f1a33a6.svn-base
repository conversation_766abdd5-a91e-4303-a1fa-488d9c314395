using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Delete;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetByOperationGroupId;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetByOperationIdAndOperationGroupId;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetList;
//using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetNameUnique;
//using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class BulkImportActionResultService : BaseService,IBulkImportActionResultService
{
    public BulkImportActionResultService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<BulkImportActionResultListVm>> GetBulkImportActionResultList()
    {
        Logger.LogDebug("Get All BulkImportActionResults");

        return await Mediator.Send(new GetBulkImportActionResultListQuery());
    }

    public async Task<BulkImportActionResultDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BulkImportActionResult Id");

        Logger.LogDebug($"Get BulkImportActionResult Detail by Id '{id}'");

        return await Mediator.Send(new GetBulkImportActionResultDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateBulkImportActionResultCommand createBulkImportActionResultCommand)
    {
        Logger.LogDebug($"Create BulkImportActionResult '{createBulkImportActionResultCommand}'");

        return await Mediator.Send(createBulkImportActionResultCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateBulkImportActionResultCommand updateBulkImportActionResultCommand)
    {
        Logger.LogDebug($"Update BulkImportActionResult '{updateBulkImportActionResultCommand}'");

        return await Mediator.Send(updateBulkImportActionResultCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BulkImportActionResult Id");

        Logger.LogDebug($"Delete BulkImportActionResult Details by Id '{id}'");

        return await Mediator.Send(new DeleteBulkImportActionResultCommand { Id = id });
    }
    public async Task<List<BulkImportActionResultListVm>> GetByOperationIdAndOperationGroupId(string operationId, string operationGroupId)
    {
        Logger.LogDebug($"Get BulkImport Action Result by operationId '{operationId}' and operationGroupId '{operationGroupId}'");

        return await Mediator.Send(new GetByOperationIdAndOperationGroupIdQuery { BulkImportOperationId = operationId, BulkImportOperationGroupId = operationGroupId });
    }

    public async Task<List<BulkImportActionResultListVm>> GetBulkImportActionResultOperationGroupId(string operationGroupId)
    {
        Logger.LogDebug($"Get BulkImport Action Result by operationGroupId '{operationGroupId}'");

        return await Mediator.Send(new GetBulkImportActionResultByGroupIdQuery { BulkImportOperationGroupId = operationGroupId });
    }


    #region NameExist
    // public async Task<bool> IsBulkImportActionResultNameExist(string name, string? id)
    // {
    //     Guard.Against.NullOrWhiteSpace(name, "BulkImportActionResult Name");
    //
    //     Logger.LogDebug($"Check Name Exists Detail by BulkImportActionResult Name '{name}' and Id '{id}'");
    //
    //     return await Mediator.Send(new GetBulkImportActionResultNameUniqueQuery { Name = name, Id = id });
    // }
    #endregion

    #region Paginated
    //public async Task<PaginatedResult<BulkImportActionResultListVm>> GetPaginatedBulkImportActionResults(GetBulkImportActionResultPaginatedListQuery query)
    //{
    //    Logger.LogDebug("Get Searching Details in BulkImportActionResult Paginated List");
    //
    //    return await Mediator.Send(query);
    //}
    #endregion
}
