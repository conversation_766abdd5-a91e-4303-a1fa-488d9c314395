using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetList;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixUsersModel;

using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class ApprovalMatrixUsersService : BaseService,IApprovalMatrixUsersService
{
    public ApprovalMatrixUsersService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<ApprovalMatrixUsersListVm>> GetApprovalMatrixUsersList()
    {
        Logger.LogInformation("Get All ApprovalMatrixUsers");

        return await Mediator.Send(new GetApprovalMatrixUsersListQuery());
    }

    public async Task<ApprovalMatrixUsersDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ApprovalMatrixUsers Id");

        Logger.LogInformation($"Get ApprovalMatrixUsers Detail by Id '{id}'");

        return await Mediator.Send(new GetApprovalMatrixUsersDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateApprovalMatrixUsersCommand createApprovalMatrixUsersCommand)
    {
        Logger.LogInformation($"Create ApprovalMatrixUsers '{createApprovalMatrixUsersCommand}'");

        return await Mediator.Send(createApprovalMatrixUsersCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateApprovalMatrixUsersCommand updateApprovalMatrixUsersCommand)
    {
        Logger.LogInformation($"Update ApprovalMatrixUsers '{updateApprovalMatrixUsersCommand}'");

        return await Mediator.Send(updateApprovalMatrixUsersCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ApprovalMatrixUsers Id");

        Logger.LogInformation($"Delete ApprovalMatrixUsers Details by Id '{id}'");

        return await Mediator.Send(new DeleteApprovalMatrixUsersCommand { Id = id });
    }
     #region NameExist
 public async Task<bool> IsApprovalMatrixUsersNameExist(string name, string? id)
 {
     Guard.Against.NullOrWhiteSpace(name, "ApprovalMatrixUsers Name");

     Logger.LogInformation($"Check Name Exists Detail by ApprovalMatrixUsers Name '{name}' and Id '{id}'");

     return await Mediator.Send(new GetApprovalMatrixUsersNameUniqueQuery { Name = name, Id = id });
 }
    #endregion

     #region Paginated
public async Task<PaginatedResult<ApprovalMatrixUsersListVm>> GetPaginatedApprovalMatrixUsers(GetApprovalMatrixUsersPaginatedListQuery query)
{
    Logger.LogInformation("Get Searching Details in ApprovalMatrixUsers Paginated List");

    return await Mediator.Send(query);
}
     #endregion
}
