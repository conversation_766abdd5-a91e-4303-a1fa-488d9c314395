using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RpForVmCGMonitorLogsFixture : IDisposable
{
    public List<RpForVmCGMonitorLogs> RpForVmCGMonitorLogsPaginationList { get; set; }
    public List<RpForVmCGMonitorLogs> RpForVmCGMonitorLogsList { get; set; }
    public RpForVmCGMonitorLogs RpForVmCGMonitorLogsDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "INFRA_OBJ_123";
    public const string ConsistencyGroupId = "CG_123";
    public const string ConsistencyGroupName = "Test CG";
    public const string UserId = "USER_123";

    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public RpForVmCGMonitorLogsFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<RpForVmCGMonitorLogs>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.ConsistencyGroupId, () => ConsistencyGroupId)
            .With(x => x.ConsistencyGroupName, () => ConsistencyGroupName)
            .With(x => x.CGProperties, () => _fixture.Create<string>())
            .With(x => x.State, () => "Active")
            .With(x => x.TransferStatus, () => "Completed")
            .With(x => x.ActivityType, () => "Replication")
            .With(x => x.ActivityStatus, () => "Success")
            .With(x => x.LastSnapShotTime, () => DateTime.UtcNow.AddHours(-1))
            .With(x => x.DataLag, () => "5 minutes")
            .With(x => x.SnapProperties, () => _fixture.Create<string>())
            .With(x => x.AvailabilityStatus, () => "Available")
            .With(x => x.ProtectedSize, () => "100GB")
            .With(x => x.IsAlertSent, () => false)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
        );

        RpForVmCGMonitorLogsList = _fixture.CreateMany<RpForVmCGMonitorLogs>(5).ToList();
        RpForVmCGMonitorLogsPaginationList = _fixture.CreateMany<RpForVmCGMonitorLogs>(20).ToList();
        RpForVmCGMonitorLogsDto = _fixture.Create<RpForVmCGMonitorLogs>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public RpForVmCGMonitorLogs CreateRpForVmCGMonitorLogsWithInfraObjectId(string infraObjectId)
    {
        return _fixture.Build<RpForVmCGMonitorLogs>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectId, infraObjectId)
            .With(x => x.ConsistencyGroupId, ConsistencyGroupId)
            .With(x => x.ConsistencyGroupName, ConsistencyGroupName)
            .With(x => x.CGProperties, _fixture.Create<string>())
            .With(x => x.State, "Active")
            .With(x => x.TransferStatus, "Completed")
            .With(x => x.ActivityType, "Replication")
            .With(x => x.ActivityStatus, "Success")
            .With(x => x.LastSnapShotTime, DateTime.UtcNow.AddHours(-1))
            .With(x => x.DataLag, "5 minutes")
            .With(x => x.SnapProperties, _fixture.Create<string>())
            .With(x => x.AvailabilityStatus, "Available")
            .With(x => x.ProtectedSize, "100GB")
            .With(x => x.IsAlertSent, false)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RpForVmCGMonitorLogs CreateRpForVmCGMonitorLogsWithConsistencyGroupId(string consistencyGroupId)
    {
        return _fixture.Build<RpForVmCGMonitorLogs>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectId, InfraObjectId)
            .With(x => x.ConsistencyGroupId, consistencyGroupId)
            .With(x => x.ConsistencyGroupName, ConsistencyGroupName)
            .With(x => x.CGProperties, _fixture.Create<string>())
            .With(x => x.State, "Active")
            .With(x => x.TransferStatus, "Completed")
            .With(x => x.ActivityType, "Replication")
            .With(x => x.ActivityStatus, "Success")
            .With(x => x.LastSnapShotTime, DateTime.UtcNow.AddHours(-1))
            .With(x => x.DataLag, "5 minutes")
            .With(x => x.SnapProperties, _fixture.Create<string>())
            .With(x => x.AvailabilityStatus, "Available")
            .With(x => x.ProtectedSize, "100GB")
            .With(x => x.IsAlertSent, false)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RpForVmCGMonitorLogs CreateRpForVmCGMonitorLogsWithProperties(
        string infraObjectId = null,
        string consistencyGroupId = null,
        string state = null,
        string transferStatus = null,
        string activityStatus = null,
        string availabilityStatus = null,
        bool? isAlertSent = null,
        DateTime? lastSnapShotTime = null)
    {
        return _fixture.Build<RpForVmCGMonitorLogs>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.ConsistencyGroupId, consistencyGroupId ?? ConsistencyGroupId)
            .With(x => x.ConsistencyGroupName, ConsistencyGroupName)
            .With(x => x.CGProperties, _fixture.Create<string>())
            .With(x => x.State, state ?? "Active")
            .With(x => x.TransferStatus, transferStatus ?? "Completed")
            .With(x => x.ActivityType, "Replication")
            .With(x => x.ActivityStatus, activityStatus ?? "Success")
            .With(x => x.LastSnapShotTime, lastSnapShotTime ?? DateTime.UtcNow.AddHours(-1))
            .With(x => x.DataLag, "5 minutes")
            .With(x => x.SnapProperties, _fixture.Create<string>())
            .With(x => x.AvailabilityStatus, availabilityStatus ?? "Available")
            .With(x => x.ProtectedSize, "100GB")
            .With(x => x.IsAlertSent, isAlertSent ?? false)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonStates = { "Active", "Inactive", "Suspended", "Error" };
        public static readonly string[] CommonTransferStatuses = { "Completed", "In Progress", "Failed", "Pending" };
        public static readonly string[] CommonActivityStatuses = { "Success", "Failed", "Warning", "In Progress" };
        public static readonly string[] CommonAvailabilityStatuses = { "Available", "Unavailable", "Degraded", "Unknown" };
        public static readonly string[] CommonActivityTypes = { "Replication", "Backup", "Restore", "Sync" };
    }
}
