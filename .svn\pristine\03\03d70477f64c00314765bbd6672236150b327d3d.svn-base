using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ApprovalMatrixRequestRepository : BaseRepository<ApprovalMatrixRequest>, IApprovalMatrixRequestRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public ApprovalMatrixRequestRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.ProcessName.Equals(name))
            : Entities.Where(e => e.ProcessName.Equals(name)).ToList().Unique(id));
    }


    public async Task<bool> IsValidWithdrawUser(string id)
    {
        return await _dbContext.ApprovalMatrixRequests
            .AsNoTracking()
            .AnyAsync(x => x.ReferenceId == id && x.CreatedBy == _loggedInUserService.UserId);
    }

    public async Task<ApprovalMatrixRequest> GetByRequestId(string requestId)
    {
        return await _dbContext.ApprovalMatrixRequests
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.RequestId.Equals(requestId) && x.IsActive);
    }

    public override async Task<PaginatedResult<ApprovalMatrixRequest>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<ApprovalMatrixRequest> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await Entities.Specify(productFilterSpec)
            .Where(x => x.CreatedBy.Equals(_loggedInUserService.UserId))
            .DescOrderById()
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
}
