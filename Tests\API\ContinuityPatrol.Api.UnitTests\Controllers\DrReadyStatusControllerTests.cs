using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetListByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Create;
using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Update;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessFunctionListByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessServiceDrReadyDetails;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessServiceIdByCount;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDrReadinessByBusinessServices;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.DRReadyStatusModel;
using ContinuityPatrol.Domain.ViewModels.GetBusinessServiceIdByCount;
using static ContinuityPatrol.Domain.ViewModels.GetBusinessServiceIdByCount.BusinessServiceIdByCountVm;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using Moq;
using GetBusinessFunctionListByBusinessServiceIdQuery = ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessFunctionListByBusinessServiceId.GetBusinessFunctionListByBusinessServiceIdQuery;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DrReadyStatusControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DrReadyStatusController _controller;
    private readonly DrReadyStatusFixture _drReadyStatusFixture;

    public DrReadyStatusControllerTests()
    {
        _drReadyStatusFixture = new DrReadyStatusFixture();

        var testBuilder = new ControllerTestBuilder<DrReadyStatusController>();
        _controller = testBuilder.CreateController(
            _ => new DrReadyStatusController(),
            out _mediatorMock);
    }

    #region Core CRUD Operations

    [Fact]
    public async Task CreateDrReadyStatus_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _drReadyStatusFixture.CreateDRReadyStatusCommand;
        var expectedResponse = _drReadyStatusFixture.CreateDRReadyStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrReadyStatus(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDRReadyStatusResponse>(createdResult.Value);
        Assert.Equal("Enterprise DR Ready Status created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDrReadyStatus_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _drReadyStatusFixture.UpdateDRReadyStatusCommand;
        var expectedResponse = _drReadyStatusFixture.UpdateDRReadyStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDrReadyStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDRReadyStatusResponse>(okResult.Value);
        Assert.Equal("Enterprise DR Ready Status updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task DeleteDrReadyStatus_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var statusId = Guid.NewGuid().ToString();
        var expectedResponse = _drReadyStatusFixture.DeleteDRReadyStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDRReadyStatusCommand>(c => c.Id == statusId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDrReadyStatus(statusId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDRReadyStatusResponse>(okResult.Value);
        Assert.Equal("Enterprise DR Ready Status deleted successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
    }

    [Fact]
    public async Task GetDrReadyStatusById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var statusId = Guid.NewGuid().ToString();
        var expectedDetail = _drReadyStatusFixture.DRReadyStatusDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDRReadyStatusDetailQuery>(q => q.Id == statusId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDrReadyStatusById(statusId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DRReadyStatusDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Detail Status Service", returnedDetail.BusinessServiceName);
        Assert.Equal("Enterprise Detail Status Function", returnedDetail.BusinessFunctionName);
        Assert.Equal("Active", returnedDetail.WorkflowStatus);
        Assert.Equal("Yes", returnedDetail.DRReady);
    }

    [Fact]
    public async Task GetDrReadyStatus_ReturnsOkResult()
    {
        // Arrange
        var statusList = _drReadyStatusFixture.DRReadyStatusListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDRReadyStatusListQuery>(), default))
            .ReturnsAsync(statusList);

        // Act
        var result = await _controller.GetDrReadyStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DRReadyStatusListVm>>(okResult.Value);
        Assert.Equal(6, returnedList.Count);
        Assert.All(returnedList, status => Assert.Contains("Enterprise", status.BusinessServiceName));
    }

    [Fact]
    public async Task GetPaginatedDrReadyStatus_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _drReadyStatusFixture.GetDRReadyStatusPaginatedListQuery;
        var paginatedResult = _drReadyStatusFixture.DRReadyStatusPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedDrReadyStatus(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DRReadyStatusListVm>>(okResult.Value);
        Assert.Equal(10, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, status => Assert.Contains("Enterprise", status.BusinessServiceName));
    }

    [Fact]
    public async Task GetDrReadyStatusByBusinessServiceId_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var expectedDetailList = new List<DRReadyStatusByBusinessServiceIdVm>
        {
            _drReadyStatusFixture.DRReadyStatusByBusinessServiceIdVm,
            new DRReadyStatusByBusinessServiceIdVm
            {
                Id = Guid.NewGuid().ToString(),
                UserId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessServiceName = "Enterprise Status by Business Service 2",
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = "Enterprise Status Business Function 2",
                IsProtected = "Yes",
                AffectedInfra = "3",
                ActiveInfra = "120",
                WorkflowId = Guid.NewGuid().ToString(),
                WorkflowName = "Enterprise Business Service Status Workflow 2",
                WorkflowStatus = "Active",
                FailedActionName = "",
                FailedActionId = "",
                ActiveBusinessFunction = "25",
                AffectedBusinessFunction = "1",
                DRReady = "Yes",
                NotReady = "No",
                WorkflowAttach = "Enterprise_Status_Workflow_2.pdf",
                InfraObjectId = Guid.NewGuid().ToString(),
                InfraObjectName = "Enterprise Status Infrastructure 2",
                ComponentName = "Status Component 2",
                Type = "DR_STATUS",
                ErrorMessage = ""
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDRReadyStatusByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(expectedDetailList);

        // Act
        var result = await _controller.GetDrReadyStatusByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetailList = Assert.IsType<List<DRReadyStatusByBusinessServiceIdVm>>(okResult.Value);
        Assert.Equal(2, returnedDetailList.Count);
        Assert.All(returnedDetailList, detail => Assert.Contains("Enterprise Status by Business Service", detail.BusinessServiceName));
        Assert.All(returnedDetailList, detail => Assert.Equal("Active", detail.WorkflowStatus));
        Assert.All(returnedDetailList, detail => Assert.Equal("Yes", detail.DRReady));
    }

    [Fact]
    public async Task GetBusinessServiceDrReady_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var expectedDetails = _drReadyStatusFixture.BusinessServiceDrReadyDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessServiceDrReadyDetailQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(expectedDetails);

        // Act
        var result = await _controller.GetBusinessServiceDrReady(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<BusinessServiceDrReadyDetailVm>>(okResult.Value);
        Assert.Equal(4, returnedList.Count);
        Assert.All(returnedList, detail => Assert.Contains("Enterprise", detail.BusinessServiceName));
    }

    [Fact]
    public async Task GetReadinessDetails_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var expectedReadiness = _drReadyStatusFixture.GetDrReadinessByBusinessServiceVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrReadinessByBusinessServiceQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(expectedReadiness);

        // Act
        var result = await _controller.GetReadinessDetails(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedReadiness = Assert.IsType<GetDrReadinessByBusinessServiceVm>(okResult.Value);
        Assert.Equal(50, returnedReadiness.TotalBusinessServiceCount);
        Assert.Equal(380, returnedReadiness.TotalDrReadyInfraObjectCount);
        Assert.Equal(25, returnedReadiness.RelatedServiceCount);
        Assert.Equal(15, returnedReadiness.TotalOrchestrationCount);
    }

    [Fact]
    public async Task GetBusinessFunctionListByBusinessServiceId_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var expectedFunctions = _drReadyStatusFixture.BusinessFunctionListByBusinessServiceIdVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessFunctionListByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(expectedFunctions);

        // Act
        var result = await _controller.GetBusinessFunctionListByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<GetBusinessFunctionListByBusinessServiceIdVm>>(okResult.Value);
        Assert.Equal(8, returnedList.Count);
        Assert.All(returnedList, func => Assert.Contains("Enterprise", func.BusinessFunctionName));
    }

    [Fact]
    public async Task GetBusinessServiceIdByCount_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var expectedCountList = new AllCount
        {
                TotalInfraCount = 150,
                ActiveInfraCount = 75,
                AffectedInfraCount = 130,
                TotalBusinessFunctionCount = 20,
                ActiveBusinessFunctionCount = 130,
                AffectedBusinessFunctionCount = 20,
                InfraAttachCount = 70,
                DRReady = 120,
                NotReady = 30
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessServiceIdByCountQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(expectedCountList);

        // Act
        var result = await _controller.GetBusinessServiceIdByCount(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedCount = Assert.IsType<AllCount>(okResult.Value);
        Assert.Equal(150, returnedCount.TotalInfraCount);
        Assert.Equal(75, returnedCount.ActiveInfraCount);
        Assert.Equal(130, returnedCount.AffectedInfraCount);
        Assert.Equal(20, returnedCount.TotalBusinessFunctionCount);
        Assert.Equal(130, returnedCount.ActiveBusinessFunctionCount);
        Assert.Equal(20, returnedCount.AffectedBusinessFunctionCount);
        Assert.Equal(120, returnedCount.DRReady);
        Assert.Equal(30, returnedCount.NotReady);
        Assert.Equal(70, returnedCount.InfraAttachCount);
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public async Task CreateDrReadyStatus_CallsClearDataCache()
    {
        // Arrange
        var command = _drReadyStatusFixture.CreateDRReadyStatusCommand;
        var expectedResponse = _drReadyStatusFixture.CreateDRReadyStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.CreateDrReadyStatus(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task UpdateDrReadyStatus_CallsClearDataCache()
    {
        // Arrange
        var command = _drReadyStatusFixture.UpdateDRReadyStatusCommand;
        var expectedResponse = _drReadyStatusFixture.UpdateDRReadyStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.UpdateDrReadyStatus(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task DeleteDrReadyStatus_CallsClearDataCache()
    {
        // Arrange
        var statusId = Guid.NewGuid().ToString();
        var expectedResponse = _drReadyStatusFixture.DeleteDRReadyStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDRReadyStatusCommand>(c => c.Id == statusId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.DeleteDrReadyStatus(statusId);

        // Assert
        _mediatorMock.Verify(m => m.Send(It.Is<DeleteDRReadyStatusCommand>(c => c.Id == statusId), default), Times.Once);
    }

    #endregion

   
    [Fact]
    public async Task CreateDrReadyStatus_HandlesHighVolumeEnvironment()
    {
        // Arrange
        var highVolumeCommand = _drReadyStatusFixture.CreateDRReadyStatusCommand;
        highVolumeCommand.BusinessServiceName = "Enterprise High Volume Status Service";
        highVolumeCommand.ActiveInfra = "1000";
        highVolumeCommand.AffectedInfra = "50";
        highVolumeCommand.ActiveBusinessFunction = "200";
        highVolumeCommand.AffectedBusinessFunction = "10";
        highVolumeCommand.Type = "DR_HIGH_VOLUME";

        var expectedResponse = _drReadyStatusFixture.CreateDRReadyStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(highVolumeCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrReadyStatus(highVolumeCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDRReadyStatusResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise High Volume Status Service", highVolumeCommand.BusinessServiceName);
        Assert.Equal("1000", highVolumeCommand.ActiveInfra);
        Assert.Equal("200", highVolumeCommand.ActiveBusinessFunction);
        Assert.Equal("DR_HIGH_VOLUME", highVolumeCommand.Type);
    }

    [Fact]
    public async Task GetBusinessServiceDrReady_HandlesNullBusinessServiceId()
    {
        // Arrange
        string? businessServiceId = null;
        var expectedDetails = _drReadyStatusFixture.BusinessServiceDrReadyDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessServiceDrReadyDetailQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(expectedDetails);

        // Act
        var result = await _controller.GetBusinessServiceDrReady(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<BusinessServiceDrReadyDetailVm>>(okResult.Value);
        Assert.NotNull(returnedList);
        Assert.Equal(4, returnedList.Count);
    }

    [Fact]
    public async Task GetReadinessDetails_HandlesNullBusinessServiceId()
    {
        // Arrange
        string? businessServiceId = null;
        var expectedReadiness = _drReadyStatusFixture.GetDrReadinessByBusinessServiceVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrReadinessByBusinessServiceQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(expectedReadiness);

        // Act
        var result = await _controller.GetReadinessDetails(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedReadiness = Assert.IsType<GetDrReadinessByBusinessServiceVm>(okResult.Value);
        Assert.NotNull(returnedReadiness);
        Assert.Equal(50, returnedReadiness.TotalBusinessServiceCount);
        Assert.Equal(380, returnedReadiness.TotalDrReadyInfraObjectCount);
    }

    [Fact]
    public async Task UpdateDrReadyStatus_HandlesStatusImprovement()
    {
        // Arrange
        var improvementCommand = _drReadyStatusFixture.UpdateDRReadyStatusCommand;
        improvementCommand.BusinessServiceName = "Enterprise Improved Status Service";
        improvementCommand.WorkflowStatus = "Optimized";
        improvementCommand.AffectedInfra = "2";
        improvementCommand.AffectedBusinessFunction = "1";
        improvementCommand.DRReady = "Yes";
        improvementCommand.NotReady = "No";
        improvementCommand.ErrorMessage = "";
        improvementCommand.Type = "DR_IMPROVEMENT";

        var expectedResponse = _drReadyStatusFixture.UpdateDRReadyStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(improvementCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDrReadyStatus(improvementCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDRReadyStatusResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Improved Status Service", improvementCommand.BusinessServiceName);
        Assert.Equal("Optimized", improvementCommand.WorkflowStatus);
        Assert.Equal("2", improvementCommand.AffectedInfra);
        Assert.Equal("Yes", improvementCommand.DRReady);
        Assert.Equal("", improvementCommand.ErrorMessage);
    }

    [Fact]
    public async Task GetPaginatedDrReadyStatus_HandlesLargeEnterpriseDataset()
    {
        // Arrange
        var query = _drReadyStatusFixture.GetDRReadyStatusPaginatedListQuery;
        query.PageSize = 25;
        query.SearchString = "Enterprise Production Status";

        var largePaginatedResult = new PaginatedResult<DRReadyStatusListVm>
        {
            Data = Enumerable.Range(1, 25).Select(i => new DRReadyStatusListVm
            {
                Id = Guid.NewGuid().ToString(),
                BusinessServiceName = $"Enterprise Production Status Service {i:D2}",
                BusinessFunctionName = $"Enterprise Production Status Function {i:D2}",
                WorkflowName = $"Enterprise Production Status Workflow {i:D2}",
                WorkflowStatus = i % 4 == 0 ? "Failed" : "Active",
                DRReady = i % 5 == 0 ? "No" : "Yes",
                IsProtected = "Yes"
            }).ToList(),
            TotalCount = 25,
            PageSize = 25,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(largePaginatedResult);

        // Act
        var result = await _controller.GetPaginatedDrReadyStatus(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DRReadyStatusListVm>>(okResult.Value);

        Assert.Equal(25, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, status => Assert.Contains("Enterprise Production Status", status.BusinessServiceName));
        Assert.Contains(returnedResult.Data, status => status.WorkflowStatus == "Failed");
        Assert.Contains(returnedResult.Data, status => status.WorkflowStatus == "Active");
        Assert.Contains(returnedResult.Data, status => status.DRReady == "No");
        Assert.Contains(returnedResult.Data, status => status.DRReady == "Yes");
        Assert.All(returnedResult.Data, status => Assert.Equal("Yes", status.IsProtected));
    }

    [Fact]
    public async Task GetBusinessServiceIdByCount_HandlesComprehensiveMetrics()
    {
        // Arrange
        var businessServiceId = Guid.NewGuid().ToString();
        var comprehensiveCount = new AllCount
        {
                TotalInfraCount = 500,
                TotalBusinessFunctionCount = 200,
                ActiveInfraCount = 425,
                InfraAttachCount = 425,
                AffectedInfraCount = 75,
                ActiveBusinessFunctionCount = 180,
                AffectedBusinessFunctionCount = 20,
                DRReady = 400,
                NotReady = 100
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessServiceIdByCountQuery>(q => q.BusinessServiceId == businessServiceId), default))
            .ReturnsAsync(comprehensiveCount);

        // Act
        var result = await _controller.GetBusinessServiceIdByCount(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedCount = Assert.IsType<AllCount>(okResult.Value);

        Assert.Equal(500, returnedCount.TotalInfraCount);
        Assert.Equal(200, returnedCount.TotalBusinessFunctionCount);
        Assert.Equal(425, returnedCount.InfraAttachCount);
        Assert.Equal(400, returnedCount.DRReady);
        Assert.Equal(100, returnedCount.NotReady);

        // Calculate readiness percentage
        var readinessPercentage = (double)returnedCount.DRReady / (returnedCount.DRReady + returnedCount.NotReady) * 100;
        Assert.Equal(80.0, readinessPercentage);

        // Verify comprehensive metrics are valid
        Assert.True(returnedCount.TotalInfraCount > 0);
        Assert.True(returnedCount.TotalBusinessFunctionCount > 0);
        Assert.True(returnedCount.DRReady + returnedCount.NotReady > 0);
    }

   

   
    [Fact]
    public async Task CreateDrReadyStatus_WithEdgeComputingScenario_ReturnsCreatedResult()
    {
        // Arrange
        var edgeComputingCommand = _drReadyStatusFixture.CreateDRReadyStatusCommand;
        edgeComputingCommand.BusinessServiceName = "Enterprise Edge Computing Service";
        edgeComputingCommand.BusinessFunctionName = "Enterprise Edge Computing Function";
        edgeComputingCommand.WorkflowName = "Enterprise Edge Computing DR Workflow";
        edgeComputingCommand.WorkflowStatus = "Edge_Nodes_Active";
        edgeComputingCommand.Type = "EDGE_COMPUTING_DR";
        edgeComputingCommand.InfraObjectName = "Enterprise Edge Computing Node";
        edgeComputingCommand.ComponentName = "Edge Gateway";
        edgeComputingCommand.AffectedInfra = "15";
        edgeComputingCommand.ActiveInfra = "250";
        edgeComputingCommand.ActiveBusinessFunction = "50";
        edgeComputingCommand.AffectedBusinessFunction = "3";
        edgeComputingCommand.DRReady = "Yes";
        edgeComputingCommand.NotReady = "No";
        edgeComputingCommand.IsProtected = "Yes";

        var expectedResponse = _drReadyStatusFixture.CreateDRReadyStatusResponse;
        expectedResponse.Message = "Enterprise Edge Computing DR Ready Status created successfully!";

        _mediatorMock
            .Setup(m => m.Send(edgeComputingCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrReadyStatus(edgeComputingCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDRReadyStatusResponse>(createdResult.Value);
        Assert.Equal("Enterprise Edge Computing DR Ready Status created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDrReadyStatus_WithIoTIntegration_ReturnsOkResult()
    {
        // Arrange
        var iotCommand = _drReadyStatusFixture.UpdateDRReadyStatusCommand;
        iotCommand.BusinessServiceName = "Enterprise IoT Integration Service";
        iotCommand.BusinessFunctionName = "Enterprise IoT Integration Function";
        iotCommand.WorkflowName = "Enterprise IoT Integration DR Workflow";
        iotCommand.WorkflowStatus = "IoT_Devices_Connected";
        iotCommand.Type = "IOT_INTEGRATION_DR";
        iotCommand.InfraObjectName = "Enterprise IoT Hub";
        iotCommand.ComponentName = "IoT Device Manager";
        iotCommand.AffectedInfra = "5";
        iotCommand.ActiveInfra = "1000";
        iotCommand.ActiveBusinessFunction = "200";
        iotCommand.AffectedBusinessFunction = "1";
        iotCommand.DRReady = "Yes";
        iotCommand.NotReady = "No";
        iotCommand.IsProtected = "Yes";

        var expectedResponse = _drReadyStatusFixture.UpdateDRReadyStatusResponse;
        expectedResponse.Message = "Enterprise IoT Integration DR Ready Status updated successfully!";

        _mediatorMock
            .Setup(m => m.Send(iotCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDrReadyStatus(iotCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDRReadyStatusResponse>(okResult.Value);
        Assert.Equal("Enterprise IoT Integration DR Ready Status updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task GetDrReadyStatusById_WithAIMLWorkloads_ReturnsOkResult()
    {
        // Arrange
        var statusId = Guid.NewGuid().ToString();
        var aimlStatus = _drReadyStatusFixture.DRReadyStatusDetailVm;
        aimlStatus.Id = statusId;
        aimlStatus.BusinessServiceName = "Enterprise AI/ML Workloads Service";
        aimlStatus.BusinessFunctionName = "Enterprise AI/ML Function";
        aimlStatus.WorkflowName = "Enterprise AI/ML DR Workflow";
        aimlStatus.WorkflowStatus = "ML_Models_Active";
        aimlStatus.Type = "AIML_WORKLOADS_DR";
        aimlStatus.InfraObjectName = "Enterprise AI/ML Cluster";
        aimlStatus.ComponentName = "ML Model Server";
        aimlStatus.AffectedInfra = "8";
        aimlStatus.ActiveInfra = "400";
        aimlStatus.ActiveBusinessFunction = "80";
        aimlStatus.AffectedBusinessFunction = "2";
        aimlStatus.DRReady = "Yes";
        aimlStatus.NotReady = "No";
        aimlStatus.IsProtected = "Yes";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDRReadyStatusDetailQuery>(q => q.Id == statusId), default))
            .ReturnsAsync(aimlStatus);

        // Act
        var result = await _controller.GetDrReadyStatusById(statusId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedStatus = Assert.IsType<DRReadyStatusDetailVm>(okResult.Value);
        Assert.Equal(statusId, returnedStatus.Id);
        Assert.Equal("Enterprise AI/ML Workloads Service", returnedStatus.BusinessServiceName);
        Assert.Equal("AIML_WORKLOADS_DR", returnedStatus.Type);
        Assert.Equal("Enterprise AI/ML Cluster", returnedStatus.InfraObjectName);
        Assert.Equal("Yes", returnedStatus.DRReady);
        Assert.Equal("ML_Models_Active", returnedStatus.WorkflowStatus);
    }

  
}
