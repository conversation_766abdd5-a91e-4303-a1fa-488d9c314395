using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class PageWidgetFixture : IDisposable
{
    public List<PageWidget> PageWidgetPaginationList { get; set; }
    public List<PageWidget> PageWidgetList { get; set; }
    public PageWidget PageWidgetDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public PageWidgetFixture()
    {
        var fixture = new Fixture();

        PageWidgetList = fixture.Create<List<PageWidget>>();

        PageWidgetPaginationList = fixture.CreateMany<PageWidget>(20).ToList();


        PageWidgetDto = fixture.Create<PageWidget>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
