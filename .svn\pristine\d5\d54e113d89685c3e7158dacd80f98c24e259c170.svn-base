﻿@model ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel.WorkflowOperationGroupViewModel
@Html.AntiForgeryToken()

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/WorkflowConfiguration.css" rel="stylesheet" />
<link href="~/css/switch_slide_toggle.css" rel="stylesheet" />
<style>
    .tooltip-inner {
        max-width: 500px !important;
        padding: 8px;
        color: #000000;
        text-align: left;
        text-decoration: none;
        background-color: #fff;
        border-radius: 10px;
        -webkit-box-shadow: 3px 7px 16px 0px rgba(224,201,224,1);
        -moz-box-shadow: 3px 7px 16px 0px rgba(224,201,224,1);
        box-shadow: 3px 7px 16px 0px rgba(224,201,224,1);
    }

    .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before, .bs-tooltip-start .tooltip-arrow::before {
        border-left-color: #fff;
    }

    .wait-action.btn-outline-warning {
        color: #0d6efd !important;
    }

        .wait-action.btn-outline-warning:hover {
            color: #fff !important;
            background-color: #0d6efd !important
        }

    .slide-horizontal {
        width: 0;
        overflow: hidden;
        white-space: nowrap;
        transition: width 0.8s ease;
        background-color: transparent;
    }

        .slide-horizontal.open {
            width: 190px; /* or whatever width you want */
        }
</style>
<div id="orchestrationExecute" data-execute-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Orchestration.Execute" aria-hidden="true"></div>

<div class="page-content">
    <div class="row g-3">
        <div class="col-8">
            <div class="card Card_Design_None" id="profileexecution">
                <div class="card-header header">
                    <h6 class="page_title w-50"><i class="cp-workflow-execution"></i><span>Workflow Execution</span></h6>
                    <div class="d-flex gap-2 align-items-center w-50">

                        <div class="gap-2 flex-fill d-flex align-items-center">
                            <div class="form-label mb-0 d-none" style="font-size: 12px"></div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-workflow-profile me-1 mb-1"></i></span>
                                <select id="ProfileList" data-placeholder="Select Workflow Profile" data-live-search="true" class="form-select text-truncate" style="overflow-y: auto;" >
                                   
                                </select>
                            </div>
                            <div id="runningProfileContainer" class="dropstart d-none" >
                                <div class="btn btn-sm btn-primary" id="btnRunningProfiles" type="button" title="Running Profiles">
                                    <i id="btnRunningProfileIcon" class="cp-workflow-execution cp-animate fs-6"></i>
                                </div>
                                <div class="dropdown-menu " style="max-width:280px;max-height:220px;overflow:auto">

                                <ul class="list-group list-group-flush" id='executionDropDownMenu' ></ul>
                               </div>
                             </div>
                        </div>
                        <div class="gap-2 w-25 d-flex align-items-center ms-2">
                            <label class="form-label mb-0 d-none" style="font-size: 12px">Users</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-user"></i></span>
                                <select id="UserList" data-placeholder="Select User" class="form-select form-select-sm" onselec="">
                                </select>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-primary rounded-1 py-1" title="Refresh" id="btnExecutionRefresh"><i class="cp-refresh"></i></button>
                        <button type="button" class="btn btn-sm btn-primary rounded-1 py-1 d-none" title="Last Download"><i class="cp-download"></i></button>
                    </div>
                </div>
                <div class="card-body pt-0 Workflow-Execution ">
                    <div id="messageWarningText" class="text-danger blinkingEffect m-2 fs-8 text-end d-none"><i class="cp-warning me-2 fs-7 pb-1"></i><span class="">Workflow service not started!</span></div>
                    <div id="LoadRunningProfile"></div>

                    <div class="card-body p-0 d-none" align="center" id="parentWEProfileLoader">
                        <div id="WEProfileLoader" class="spinner-border text-primary position-absolute" style="width: 3rem; height: 3rem; top:45%;left:47%;" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-4 d-grid">
            <div class="card Card_Design_None mb-3 autoscrolltimeline position-relative">
                <div class="card-header pe-2" id="TimelineWorkflowContainer">
                    <div style="display: flex;justify-content:space-between;align-items:center" id="timeline_WorkflowName">
                        <div class="fw-semibold text-center">
                            <i class="cp-timeline-view me-2"></i><span class="">Timeline View</span><span class="me-2 mt-2"><i class="cp-note ms-1 fs-8 mt-2 text-primary d-none"></i></span>
                        </div>
                       
                        <div class="d-flex flex-row">
                            <div class="btn-group btn-group-sm me-1" id="timelineWFCountContainer">
                                <div class="slide-horizontal open" id="individualCountCont">
                                    <button type="button" class="btn actionIcon px-2 timelineConditionalCounts d-none"><i class="text-success cp-success me-1" title="Success"></i><span class="align-middle" id="successTimelineContainer">0</span></button>
                                    <button type="button" class="btn actionIcon px-2 timelineConditionalCounts d-none"><i class="text-primary cp-reload cp-animate me-1" title="Running"></i><span class="align-middle" id="runningTimelineContainer">0</span></button>
                                    <button type="button" class="btn actionIcon px-2 timelineConditionalCounts d-none"><i class="text-info cp-skipped me-1" title="Skip"></i><span class="align-middle" id="skipTimelineContainer">0</span></button>
                                    <button type="button" class="btn actionIcon px-2 timelineConditionalCounts d-none"><i class="text-danger cp-error me-1" title="Error"></i><span class="align-middle" id="errorTimelineContainer">0</span></button>
                                    <button type="button" class="btn actionIcon px-2 d-none"><i class="text-secondary cp-by-pass me-1 fs-7" title="Bypass"></i><span class="align-middle" id="bypassTimelineContainer">0</span></button>
                                </div>
                               
                                <button type="button" class="btn text-secondary px-0 ms-1 d-none" id="btnToggleTimeline" title="Toggle"><i class="cp-rignt-arrow fs-7"></i></button>
                                <button type="button" class="btn actionIcon px-1 ms-1 timelineConditionalCounts d-none"><i class="cp-network text-warning timelineNodeName"></i></button>
                                <button type="button" class="btn actionIcon px-1 ms-1" id="btnExpandTimeline" title="Expand"><i class="cp-open"></i></button>
                            </div>
                            @*  <div class="card-header d-none" id="TimelineWorkflowContainer">
                            <span title="" class="mt-2 fs-8" style="max-width:95px;"><i class="cp-workflow me-2 fs-8"></i><span class="text-truncate timelineWorkflowName"></span></span>
                            <span class="text-truncate text-primary d-block" style="max-width: 92px;font-size:9px;"><i class="cp-network align-middle me-1 fs-9" title="Current Node"></i><span>-</span><span title="" class="timelineNodeName ms-1"></span></span>
                            </div> *@                           
                           
                        </div>
                    </div>
                    <div class="d-none" style="max-width:75%;display:block;color:#0479ff;font-size: 11px;padding:0 5px" id="timelineWorkflowNameContainer"><i class="cp-circle-workflow me-2 fs-8"></i><span title="" class="text-truncate timelineWorkflowName"></span></div>
                </div>
              
                @* border: 1px solid #0479ff;display:block;color: #0479ff;font-size: 10px;padding: 0px 5px;border-radius: 5px; *@
               
                <div class="card-body py-0 px-2">

                    <div class="list-group list-group-flush Profile-Select " id="timeline_view" style="height:calc(100vh - 385px);overflow-y: auto;">
                    </div>
                    <div class="card-body p-0 d-none" align="center" id="parentWETimeLoader">
                        <div id="WETimeLoader" class="spinner-border text-primary position-absolute" style="width: 1.5rem; height: 1.5rem; top:45%;left:47%;" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>

                    </div>
                </div>
                <div class="card-footer text-end d-none" id="workflowCustomContainer">
                   @*  <button type="button" class="btn btn-sm btn-secondary my-2 px-2 py-0 rounded-1 me-1 d-none" id="discardTimeline">Delete</button>
                       <button type="button" class="btn btn-sm btn-secondary my-2 px-2 py-0 rounded-1 me-1" id="cancelCustomExecution">Cancel</button>
                       <button type="button" class="btn btn-sm btn-primary my-2 px-2 py-0 rounded-1" id="createCustomExecution">Save</button> *@
                     <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group dropup-start dropup">
                            <button type="button" class="btn btn-primary dropdown-toggle  my-2 px-2 py-0 rounded-1 me-1" id="btnCustomTimelineIndex" data-bs-auto-close="outside">
                                Select More
                            </button>
                            <div class="dropdown-menu py-0 border" id="customIndexDropdownCont">
                                <form class="p-3">
                                    <div class="mb-2 d-grid">
                                        <label class="form-label">Select Custom Action</label>
                                       <div class="d-flex align-items-center">                                           
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input customIndex" type="radio" name="" id="customDownIndex" value="downIndex" checked>
                                                <label class="form-check-label" for="inlineRadio2">Downward</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input customIndex" type="radio" name="" id="customUpIndex" value="upIndex" >
                                                <label class="form-check-label" for="inlineRadio1">Upward</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input customIndex" type="radio" name="" id="customBetweenIndex" value="betweenIndex">
                                                <label class="form-check-label" for="inlineRadio3">Between</label>
                                            </div>
                                       </div>
                                    </div>
                                    <div class="mb-2">
                                        <label class="form-label" id='textIndexContainer'>Enter Sr. No</label>
                                        <div class="input-group border-0 gap-2">
                                            <div class="w-50">
                                                 <input id='fromIndexCount' type="number" min='0' aria-label="First name" class="form-control form-control-sm border rounded-2">
                                            </div>
                                           
                                            <div class="d-flex flex-row w-50 d-none" id="customIndexEndContainer">
                                                 <small class="input-group-text me-2">to</small>
                                                 <input id='endIndexCount' type="number" min='0' aria-label="Last name" class="form-control form-control-sm border rounded-2">
                                            </div>
                                           
                                        </div>
                                    </div>
                                    <div class="text-end gap-1">
                                        <button class="btn btn-secondary btn-sm px-2 py-0" id="btnCancelCustomCount">Cancel</button>
                                        <button type="reset" class="btn btn-secondary btn-sm px-2 py-0" id="btnClearCustomCount">Clear</button>
                                        <button class="btn btn-primary btn-sm px-2 py-0" id="btnSubmitCustomCount">OK</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div id="createCustomCont">
                             <button type="button" class="btn btn-sm btn-secondary my-2 px-2 py-0 rounded-1 me-1 d-none" id="discardTimeline">Delete</button>
                             <button type="button" class="btn btn-sm btn-secondary my-2 px-2 py-0 rounded-1 me-1" id="cancelCustomExecution">Cancel</button>
                             <button type="button" class="btn btn-sm btn-primary my-2 px-2 py-0 rounded-1" id="createCustomExecution">Save</button> 
                          @*   <button type="button" class="btn btn-sm btn-secondary my-2 px-2 py-0 rounded-1 me-1 d-none" id="discardTimeline">Discard</button>
                            <button type="button" class="btn btn-sm btn-secondary my-2 px-2 py-0 rounded-1 me-1" id="cancelTimeline">Cancel</button>
                            <button type="button" class="btn btn-sm btn-primary my-2 px-2 py-0 rounded-1" id="saveTimeline">Save</button> *@
                        </div>
                    </div>
                </div>
            </div>
            <div class="card Card_Design_None" id="logViewerContainer">
                <div class="card-header d-flex align-items-center justify-content-between pb-1">
                    <span class="card-title">
                        <i class="cp-log-viewer me-2"></i>
                        <i class="cp-single-dot text-success fs-10 d-none" style="position: absolute;left: 27px;top: 19px;"></i>
                        <span class="">Log View</span>
                    </span>
                    <div class="form-switch">
                        <i class="cp-circle-switch text-danger fs-5 me-2 d-none" title="Disable" id="logViewerToggle" role="button"></i>
                        @* <input id="enableLogView" title="On/Off Log Viewer" type="checkbox" class="form-check-input isTableChecked"> *@
                        <i class="cp-full-screen" id="workflowFullScreenLogViewer" title="Full Screen" role="button"></i>
                    </div>
                </div>
                <div class="card-body autoscroll" style="height:calc(100vh - 450px);overflow-y: auto;">
                    <div class="d-grid LogViewData">
                         <div id="workflowLogViewer"></div>
                     </div>
                </div>
                @* <img src="../../img/isomatric/log_viewer.svg" width="200" /> *@
            </div>
        </div>        
    </div>
</div>


<!-- Log Viewer -->
<div class="modal fade" id="workflowExecutionLogViewer" data-bs-backdrop="static">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollabel">
        <div class="modal-content">
            <div class="modal-header header">
                <h6 class="page_title"><span><i class="cp-log-file-name me-1"></i>Log View</span></h6>
                <div class="d-flex align-items-center gap-3 me-2" >
                    <div id="btnsLogContainer" class="d-flex align-items-center gap-3">
                        <div class="input-group w-auto">
                            <input type="search" id="searchLogViewer" class="form-control" placeholder="Search" autocomplete="off">
                            <div class="input-group-text">
                                    <span><i class="cp-search"></i></span>
                            </div>
                        </div>
                        <span class="cp-drill-error fs-5 text-danger" id="btnFindLogViewError" role="button" title="Exception"></span>
                        <span><a id='logDownload' role='button'><i class="cp-export text-primary" title="Export"></i></a></span>
                        <span class="cp-circle-up-linearrow scrollUp" role="button" title="Scroll Up"></span>
                        <span class="cp-circle-down-linearrow scrollDown" role="button" title="Scroll Down"></span>
                    </div>
                   
                    <button type="button" class="btn-close" id="btnLogViewModalClose" title="Close"></button>
                </div>
            </div>
            <div class="modal-body scrollStatic" style="min-height: 490px">
                <div class="LogViewZoomData">
                    <div class="LogViewData" id="workflowLogViewerModal"></div>

                    <div class="card-body p-0 d-none" align="center" id="parentWELogViewLoader">
                        <div id="WELogViewLoader" class="spinner-border text-primary position-absolute" style="width: 1.5rem; height: 1.5rem; top:45%;left:47%;" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<!--Modal Authendication-->
<div class="modal fade" id="AuthendicateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-authentication"></i><span>Authenticate</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="form-label">Description</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-description"></i></span>
                        <input type="text" id="txtDescription" class="form-control" placeholder="Description" maxlength="200" />
                    </div>
                    <span id="txtDescription-error" style="margin-left:-20px"></span>
                </div>
                <div class="mb-3 form-group">
                    <div class="form-label">Password</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-lock"></i></span>
                        <input type="password" id="txtPassword" class="form-control validate" placeholder="Enter Password" required />
                        <span role="button" class="input-group-text toggle-password"></span>
                    </div>
                    <span id="txtPassword-error"></span>
                </div>

                <div class="form-group d-flex align-items-center justify-content-between tooltip_inform">
                    <div>

                        <div class="form-check form-check-inline">
                            <input class="form-check-input execution_type" type="radio" name="runMode" id="executionMode" value="execution" checked>
                            <label class="form-check-label" for="inlineRadio1">Execution</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input execution_type" type="radio" name="runMode" id="dryRunMode" value="Dry Run">
                            <label class="form-check-label" for="inlineRadio2">Dry Run</label>
                        </div>
                    </div>
                    <div data-bs-custom-class="tooltip" data-bs-toggle="tooltip" data-bs-html="true" data-bs-placement="left" data-bs-title=" <p class='w-100 mb-2'>
                            <b>Execution :</b><br/>For actual drill activities, you can choose the execution option. The
                            drill report will be filtered based on the Dry Run or Execution selection.
                        </p><p class='w-100 mb-2'>
                            <b>Dry Run</b>:<br/>A dry run can be utilized to perform a test execution without making
                            actual changes.
                        </p>
                       ">
                        <i class="cp-note text-primary"></i>

                  </div>
                </div>

                <table class="datatable table table-hover no-footer" style="width:100%;table-layout:fixed">
                    <thead>
                        <tr id="authenticate_table">
                            <th class="SrNo_th">Sr.No.</th>
                            <th>Workflow Name</th>
                            <th class="d-flex flex-row justify-content-between align-items-center ">
                                <span class="">
                                    Execution Mode
                                </span>
                                <div class="d-flex gap-2 align-items-center">
                                    <div id="modeSelectionContainer">
                                        <select class="form-select form-select-modal mt-1" id="allModeOperation">
                                            <option value="1">Auto</option>
                                            <option value="2">Step</option>
                                        </select>
                                    </div>

                                    <div class="fade show" data-bs-custom-class="custom-tooltip" data-bs-toggle="tooltip" data-bs-html="true" data-bs-placement="left" data-bs-title="<div style='width:100%!important;'><p class='w-100 mb-2'> <b>Auto Mode :</b> <br/>Workflow actions will run automatically. If any action fails, the workflow will wait for user input before proceeding. </p>
                                                       <p class='w-100 mb-2'> <b>Step Mode:</b><br/>You can execute the workflow step by step, with the user required to provide input to proceed to the next step, regardless of success or failure.</p>
                                                       <p class='w-100 mb-2 simulation Mode'><b>Simulation Mode :</b><br/>Only validation actions will be executed. Disaster Recovery (DR) operation actions will not run. </p><div class='d-flex align-items-center justify-content-between'><p class='d-bolck'><i class='cp-single-dot me-1 text-primary'></i>Represents validation actions</p><p class='d-bolck'><i class='cp-single-dot me-1 text-danger'></i>Represents DR operation actions</p></div></div>">
                        <i class="cp-note text-primary"></i>
                                    </div>
                                </div>
                            </th>
                            <th id="custumExecutionAuthContainer" class="d-none">Customized</th>
                        </tr>
                    </thead>
                    <tbody id="workflowModeContainer"></tbody>
                </table>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" id="CancelBtn" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="ProceedBtn" class="btn btn-primary btn-sm">
                        Proceed
                        <div id="proceedBtnLoader" class="spinner-border text-white ms-2 mt-1 p-1 d-none" style="width: 0.8rem; height: 0.8rem;">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </button>
                </div>
            </div>

        </div>
    </div>
</div>

<!--Modal Failed Actions-->
<div class="modal fade" id="failedModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-warning"></i><span>Failed Actions</span></h6>
                <div>
                    <button type='button' class='btn btn-sm btn-primary border-0 me-2' id="btnReloadParellelErrorUpdateOperations"><i class='cp-reload fs-6 me-1'></i><span class='align-middle'>Reload</span></button>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
               
            </div>
            <div class="modal-body">
                <table class="datatable table table-hover no-footer" style="width:100%">
                    <thead>
                        <tr>
                            <th>Sr.No</th>
                            <th>Action Name</th>
                            <th>Workflow Name</th>
                            <th>Error Message</th>
                            <th>Workflow Actions</th>
                        </tr>
                    </thead>
                    <tbody id="failedActionResult">
                    </tbody>
                </table>

                <div class="card-body p-0 d-none" align="center" id="failedParallelLoader">
                    <div id="WETimeLoader" class="spinner-border text-primary position-absolute" style="width: 1.5rem; height: 1.5rem; top:45%;left:47%;" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>

                </div>
            </div>

        </div>
    </div>
</div>


<!--Modal Wait Actions-->
<div class="modal fade" id="waitModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg  modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-alerts"></i><span>Alert</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <table class="datatable table  no-footer align-middle" style="width:100%;table-layout:fixed">
                    <thead class="position-sticky top-0 z-3">
                        <tr>                        
                            <th>Action Name</th>
                            <th>Reason</th>
                            <th>Workflow Name</th>
                            <th id="waitPasswordHead">Password</th>
                            <th id="waitAcceptHead">Action Note</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody id="waitActionBody">
                    </tbody>
                </table>

            </div>

        </div>
    </div>
</div>


@section Scripts
{
    <partial name="_ValidationScriptsPartial" />
}

<!--Modal Confimation-->
<div class="modal fade" id="ConfimationModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content" style="border-radius:70px 70px 10px 10px">
            <form>
                <div class="modal-header p-0">
                    <img class="delete-img" src="~/img/isomatric/maintanence.svg" alt="Delete Img" />
                </div>

                <div class="modal-body text-center pt-0">
                    <h5 class="fw-bold">Confirmation</h5>
                    <div class="mb-3 form-group" id="boxContainer">
                        <div class="form-label"><i class="cp-question-mark me-1"></i> Reason</div>
                        <div>
                            <textarea class="form-control border" id="textArea" placeholder="Enter The Reason" maxlength="250" rows="2" cols="50" style="resize: none;"></textarea>
                        </div>
                    </div>
                    <p>
                        <span class="font-weight-bolder text-primary infraNameDetails" id="infraNameDetails"></span> InfraObject(s) are in <span class="font-weight-bolder text-primary">Active</span> state.
                        Do you want to switch the state to <span class="font-weight-bolder text-primary">Maintenance</span> ?
                    </p>
                    @* <p>Do you want to switch the state to <span class="font-weight-bolder text-primary">Maintenance</span> ?</p> *@
                </div>

                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary btn-sm" id="confirmStateButton">Yes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!--Modal active Confimation-->
<div class="modal fade" id="activeConfimationModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content" style="border-radius:70px 70px 10px 10px">
            <form>
                <div class="modal-header p-0">
                    <img class="delete-img" src="~/img/isomatric/maintanence.svg" alt="Delete Img" />
                </div>
                <div class="modal-body text-center pt-0">
                    <h5 class="fw-bold">Confirmation</h5>
                    <p>Do you want to change <span class="font-weight-bolder text-primary text-truncate d-inline-block align-bottom" style="max-width: 90px;" id="completeInfraDetails"></span> infraObject(s) from maintenance to active state.</p>
                </div>
                <div class="modal-footer gap-1 justify-content-end">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" id="cancelStateButton">Cancel</button>
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" id="btnCloseConfirmationModal">Close</button>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-dismiss="modal" id="activeStateButton">OK</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!--Modal Abort Confimation-->
<div class="modal fade" id="AbortModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content" style="border-radius:70px 70px 10px 10px">
            <form>
                <div class="modal-header p-0">
                    <img class="delete-img" src="~/img/isomatric/maintanence.svg" alt="Delete Img" />
                </div>
                <div class="modal-body text-center pt-0">
                    <h5 class="fw-bold">Confirmation</h5>
                    <p>Are you sure you want to abort <span class="font-weight-bolder  text-primary" id="abortingWorkflowName"></span> workflow?</p>

                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary btn-sm" id="confirmAbortButton">Yes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!--Modal Log State Confimation-->
<div class="modal fade" id="updateLogStateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content" style="border-radius:70px 70px 10px 10px">
            <form>
                <div class="modal-header p-0">
                    <img class="delete-img" src="~/img/isomatric/maintanence.svg" alt="Delete Img" />
                </div>
                <div class="modal-body text-center pt-0">
                    <h5 class="fw-bold">Confirmation</h5>
                    <p>Are you sure you want to <span class="me-1" id="textLogState">disable</span><span class="font-weight-bolder text-primary text-truncate d-inline-block align-bottom" style="max-width:90px" id="updateLogWorkflowName"></span> workflow Log View?</p>

                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary btn-sm" id="btnLogStateUpdate">Yes</button>
                </div>
            </form>
        </div>
    </div>
</div>

@* Delete custom execution *@
<div class="modal fade" id="DeleteCustomExeModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content" style="border-radius:70px 70px 10px 10px">
            <form>
                <div class="modal-header p-0">
                    <img class="delete-img" src="~/img/isomatric/delete.png" alt="Delete Img" loading="lazy" />
                </div>
                <div class="modal-body text-center pt-0">
                    <h5 class="fw-bold">Confirmation</h5>
                    <p>Are you sure you want to reset <span class="font-weight-bolder text-primary" id="deleteCustomWorkflowName"></span> workflow from custom execution?</p>

                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" id="btnCancelResetCustomExecution">No</button>
                    <button type="button" class="btn btn-primary btn-sm" id="btnDeleteCustomExecution">Yes</button>
                </div>
            </form>
        </div>
    </div>
</div>


<!------Snap Action Modal ------------->

<div class="modal fade" id="snapModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-xl ">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-snap-1"></i><span>Snap</span></h6>

                <div class="d-flex align-items-end gap-3">
                    <div class="input-group w-auto">
                        <input type="search" id="snap_search" class="form-control" placeholder="Search" autocomplete="off">                       
                    </div>
                    <div class="d-flex align-items-end gap-2 d-none">
                        <span class="form-label">Select Storage </span>
                        <div class="input-group align-items-center " style="width:100px">
                            <select class="form-select-modal" data-live-search="true" id="storageGroup">
                                @* <option value="All">All</option> *@
                            </select>
                        </div>
                    </div>
                    <div class="d-flex align-items-end gap-2">
                        <span class="form-label">Select Date </span>
                        <div class="input-group w-auto align-items-center">
                            <input type="date" id="filter_TimeStamp" class="form-control" autocomplete="off" style="height: 30px;" />
                            <span id="clearButton" class="me-2" style="cursor:pointer">X</span>
                            <button type="button" class="btn-close ms-2" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div class="wrapper">
                    <div class="container1">
                        <table class="datatable table no-footer align-middle" style="width:100%;table-layout:fixed">
                            <thead class="position-sticky top-0 z-3">
                                <tr>
                                    <th>Snap Name</th>
                                    <th>Gen</th>
                                    <th>Storage Group</th>                             
                                    <th>Time Stamp</th>
                                    <th>Linked Status</th>
                                </tr>
                            </thead>
                            <tbody id="snapActionBody">
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>

            <div class="modal-footer">
                <div class="d-flex align-items-baseline gap-3">
                    <label>Password</label>
                    <div class="form-group">
                        <div class="input-group">
                            <input type="password" id="snapPassword" class="form-control border border-secondary-subtle" placeholder="Enter password">
                        </div>
                        <span id="snapPasswordError"></span>
                    </div>
                    <button class="btn btn-sm btn-primary" id="btnSnapSubmit">Submit</button>
                </div>
            </div>

        </div>

    </div>
</div>

@* Custom Confirmation Modal *@
<div class="modal" id="customConfirmationModal" tabindex="-1" area-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <img class="w-100" src="~/img/isomatric/confirmation.svg" alt="Duplicate Actions" loading="lazy" />
            </div>
            <div class="modal-body text-center pt-5">
                <h5 class="fw-bold">Confirmation</h5>
                <p>Some workflows are in <span class="text-primary">custom</span>  mode. Do you want to proceed with execution?</p>
             <p></p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" id="CancelCustomConfirmation">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="customConfirmation">Yes</button>
            </div>

        </div>
    </div>
</div>

<!------- Global Variable --------->
<div class="modal fade" id="globalVariableModal" tabindex="-1" aria-labelledby="globalVariableModal" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-xl ">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-variables me-2"></i><span>Global Variable -</span><span id="currentGlobalVariableName" class="ms-2 text-primary"></span></h6>
                <div class="d-flex align-items-end gap-3">
                    <div class="input-group w-auto">
                        <input type="search" id="variableSearch" class="form-control" placeholder="Search" autocomplete="off">
                    </div>
                    @*  <div class="d-flex align-items-end gap-2 d-none">
                        <span class="form-label">Select Storage </span>
                        <div class="input-group align-items-center " style="width:100px">
                            <select class="form-select-modal" data-live-search="true" id="storageGroup">

                            </select>
                        </div>
                    </div> *@
                    @* <div class="d-flex align-items-end gap-2">
                        <span class="form-label">Select Date </span>
                        <div class="input-group w-auto align-items-center">
                            <input type="date" id="filter_TimeStamp" class="form-control" autocomplete="off" style="height: 30px;" />
                            <span id="clearButton" class="me-2" style="cursor:pointer">X</span>

                        </div>
                    </div> *@
                    <button type="button" class="btn-close ms-2 mb-3" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

            </div>
            <div class="modal-body mt-2">
                <div class="wrapper">
                    <div class="container1">
                        <table class="datatable table no-footer align-middle " style="width:100%;table-layout:fixed" id="tableGlobalVariable">
                            <thead class="position-sticky top-0 z-3 align-middle">
                                <tr>
                                    <th style="width:50px !important;"><input type='checkbox' class="globalVariableGroupCheckbox form-check" /></th>
                                    <th style="width:90px !important;">Sr. No</th>
                                    <th>Name</th>
                                    <th>VMXPath</th>
                                </tr>
                            </thead>
                            <tbody id="globalVariableTableBody">
                            </tbody>
                        </table>
                        <div id="noDataImageContainer" class="d-none text-center" style="margin-top:70px !important;"><img src='../../img/isomatric/Workflow_Execution_No_Data_Found.svg' class='Card_NoData_ImgExe'></div>
                    </div>

                </div>
            </div>

            <div class="modal-footer">
                <div class="d-flex align-items-baseline gap-3">
                    <button class="btn btn-sm btn-secondary" id="btnVariableModalCancel" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                    <button class="btn btn-sm btn-primary" id="btnVariableSubmit">Submit</button>
                </div>
            </div>

        </div>

    </div>
</div>

<!-------message box--------->
<div class="modal fade" id="messageModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-md  modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-message-alert"></i><span id="messageHeader"></span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="messageBody">
            </div>
            <div class="text-end">
                <button class="btn btn-sm btn-primary m-3" id="btnMessageSubmit">Submit</button>
            </div>
        </div>
    </div>
</div>




<!--Overall Risk Modal -->
<div class="modal fade" id="OverallRiskModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-risk"></i><span>Overall Risk View</span></h6>
                <div>
                    <button type="button" class="btn btn-primary btn-sm"><i class="cp-report me-1 align-middle"></i>Report</button>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body">
                <div class="Profile-Select justify-content-between d-flex align-items-center">
                    <div class="d-flex w-50">
                        <div class="mt-1"><i class="cp-idea Success_Running me-2"></i></div><div class="text-truncate me-1">
                            <span class="fw-bold infraObjectContainer">Infra_test_mssql<i class="cp-pin-point ms-1 fs-8 text-success d-none"></i></span><br>
                            <div class="text-light text-truncate mt-1 workflowTextContainer"><i class="cp-flow me-2"></i><span>Mark</span></div>
                        </div>
                    </div>
                    <div class="w-auto text-center">
                        <span>Risk Score</span><br />
                        <div class="donut-chart" style="--progress: 50;">
                            <svg width="40" height="40" viewBox="0 0 120 120">
                                <circle class="circle-bg" cx="60" cy="60" r="50" />
                                <circle class="circle-progress" cx="60" cy="60" r="50" style="stroke:var(--bs-danger)" />
                            </svg>
                            <div class="percentage">75%</div>
                        </div>
                    </div>
                </div>
                <table class="table table-borderless mt-3 mb-0 fs-7">
                    <thead>
                        <tr>
                            <th class="bg-white" colspan="3"><i class="cp-breakdown me-2"></i>Breakdown</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><i class="cp-historical-failure-rate text-info me-2"></i>Historical Failure Rate</td>
                            <td class="text-center">:</td>
                            <td>45% <i class="cp-right-linearrow mx-2"></i> 15Pts</td>
                        </tr>
                        <tr>
                            <td><i class="cp-last_failure me-2 text-danger"></i>Last Failure</td>
                            <td class="text-center">:</td>
                            <td>12 hours ago <i class="cp-right-linearrow mx-2"></i> 20Pts</td>
                        </tr>
                        <tr>
                            <td><i class="cp-execution-time-deviation me-2 text-primary"></i>Execution Time Deviation</td>
                            <td class="text-center">:</td>
                            <td>40% <i class="cp-right-linearrow mx-2"></i> 10Pts</td>
                        </tr>
                        <tr>
                            <td><i class="cp-infra_health me-2 text-success"></i>Infra Health</td>
                            <td class="text-center">:</td>
                            <td>Minor Issues <i class="cp-right-linearrow mx-2"></i> 10Pts</td>
                        </tr>
                        <tr>
                            <td><i class="cp-SLA-breach-risk me-2 text-warning"></i>SLA Breach Risk</td>
                            <td class="text-center">:</td>
                            <td>70% <i class="cp-right-linearrow mx-2"></i> 20Pts</td>
                        </tr>
                        <tr>
                            <td><i class="cp-config-changes me-2 text-primary-emphasis"></i>Config Changes</td>
                            <td class="text-center">:</td>
                            <td>2 days ago <i class="cp-right-linearrow mx-2"></i> 20Pts</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="modal-footer gap-3">
                <div class="alert p-1 w-100" role="alert" style="background: #57C785; background: linear-gradient(90deg, #57c7851f 0%, #eddd5321 100%);">
                    <table class="table table-borderless my-0 fs-7">
                        <tbody>
                            <tr>
                                <td><i class="cp-prediction me-2"></i>Prediction</td>
                                <td class="text-center">:</td>
                                <td>Likely to fail due to frequent past issues + recent failures.</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="alert p-1 w-100" role="alert" style="background: linear-gradient(90deg, #c757571f 0, #eddd5321 100%);">
                    <table class="table table-borderless my-0 fs-7">
                        <tbody>
                            <tr>
                                <td><i class="cp-recommendation me-2"></i>Recommendation</td>
                                <td class="text-center">:</td>
                                <td>
                                    Run pre-checks or validate config before starting.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script>
    $('select').on('select2:closing', () => {
        $('.select2-selection').width('auto');
        var $choice = $('.select2-selection__choice');
        $choice.first().show();
        $choice.slice(2).hide();
        $choice.eq(2).after(`<li class='select2-selection__choice select2-selection__choice_more'>...</li>`);
    });

    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl))
</script>

@* <script src="~/js/workflowexecution/logviewer.js"></script>
<script src="~/js/WorkflowExecution/WorkflowProfileContainer.js"></script>
<script src="~/js/workflowexecution/timelineview.js"></script>
<script src="~/js/WorkflowExecution/UpdateConditions.js"></script>
<script src="~/js/WorkflowExecution/CustomExecution.js"></script>
<script src="~/js/workflowexecution/authenticate.js"></script>
<script src="~/js/workflowexecution/signalrconnectivity.js"></script> *@

<script src="~/js/itautomation/workflowexecution/authenticate.js"></script>
<script src="~/js/itautomation/workflowexecution/timelineview.js"></script>
<script src="~/js/itautomation/workflowexecution/logviewer.js"></script>
@* <script src="~/js/itautomation/workflowexecution/WebWorker.js"></script> *@
<script src="~/js/itautomation/workflowexecution/signalrconnectivity.js"></script>
<script src="~/js/itautomation/workflowexecution/customexecution.js"></script>
<script src="~/js/itautomation/workflowexecution/updateconditions.js"></script>
<script src="~/js/itautomation/workflowexecution/workflowprofilecontainer.js"></script>



@* <script src="~/js/WorkflowExecution/WorkflowOperationGroup.js"></script> *@

<script src="~/js/common/show_hide_password.js"></script>


