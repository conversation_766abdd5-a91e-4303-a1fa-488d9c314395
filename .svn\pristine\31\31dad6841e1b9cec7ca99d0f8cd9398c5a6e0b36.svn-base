﻿using ContinuityPatrol.Application.Features.HeatMapLog.Commands.Create;
using ContinuityPatrol.Application.Features.HeatMapLog.Commands.Update;
using ContinuityPatrol.Application.Features.HeatMapLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.HeatMapLog.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.HeatMapLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class HeatMapLogProfile : Profile
{
    public HeatMapLogProfile()
    {
        CreateMap<HeatMapLog, CreateHeatMapLogCommand>().ReverseMap();
        CreateMap<UpdateHeatMapLogCommand, HeatMapLog>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<HeatMapLog, HeatMapLogDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<HeatMapLog, HeatMapLogListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<HeatMapLog, HeatMapLogTypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<PaginatedResult<HeatMapLog>,PaginatedResult<HeatMapLogListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}