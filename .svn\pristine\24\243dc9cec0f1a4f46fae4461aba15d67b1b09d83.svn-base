﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.UserLoginModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.UserLogin.Queries.GetPaginatedList;

public class
    GetUserLoginPaginatedListQueryHandler : IRequestHandler<GetUserLoginPaginatedListQuery,
        PaginatedResult<UserLoginListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMediator _mediator;
    private readonly IUserRepository _userRepository;

    public GetUserLoginPaginatedListQueryHandler(IMapper mapper, IMediator mediator, IUserRepository userRepository)
    {
        _mapper = mapper;
        _mediator = mediator;
        _userRepository = userRepository;
    }

    public async Task<PaginatedResult<UserLoginListVm>> Handle(GetUserLoginPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _userRepository.GetPaginatedQuery();

        var productFilterSpec = new UserFilterSpecification(request.SearchString);

        var usersList = await queryable
            //.Specify(productFilterSpec)
            .Select(m => _mapper.Map<UserLoginListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        if (usersList == null) throw new NotFoundException("Users Not Found!", "User");

        //var companies = await _mediator.Send(new GetCompanyListQuery(), cancellationToken);


        //if (companies.Count > 0)
        //{
        //    usersList.Data.ForEach(x => x.CompanyName = companies.First(c => c.Id == x.CompanyId).DisplayName);
        //}
        return usersList;
    }
}