﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class AlertMasterRepository : BaseRepository<AlertMaster>, IAlertMasterRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public AlertMasterRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<AlertMaster>> GetAlertMasterByAlertId(string alertId)
    {
        return await _dbContext.AlertMasters.AsNoTracking()
            .Where(e => e.AlertId == alertId && e.IsActive)
            .ToListAsync();
    }

    public async Task<List<AlertMaster>> GetAlertMasterByAlertName(string alertName)
    {
        return await _dbContext.AlertMasters.AsNoTracking().Active()
            .Where(e => e.AlertName == alertName)
            .ToListAsync();
    }

    public async Task<List<AlertMaster>> GetAlertMasterNames()
    {
        return await _dbContext.AlertMasters
            .Active()
            .Select(x => new AlertMaster { ReferenceId = x.ReferenceId, AlertMessage = x.AlertMessage })
            .OrderBy(x => x.AlertMessage)
            .ToListAsync();
    }

    public async Task<PaginatedResult<AlertMaster>> PaginatedListAllAsync(int pageNumber,int pageSize, Specification<AlertMaster> specification,Expression<Func<AlertMaster,bool>> expression,string sortColumn,string sortOrder)
    {
        return await Entities.AsNoTracking()
            .Specify(specification)
            .Where(expression)
            .DescOrderById()
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<AlertMaster> GetPaginatedQuery()
    {
        return Entities.Where(x => x.IsActive).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public IQueryable<AlertMaster> GetPaginatedByAlertName(string alertName)
    {
        return _dbContext.AlertMasters.Active().AsNoTracking()
            .Where(x => x.AlertName == alertName)
            .OrderByDescending(x => x.Id);
    }

    public IQueryable<AlertMaster> GetPaginatedByAlertPriority(string alertPriority)
    {
        return _dbContext.AlertMasters.Active().AsNoTracking()
            .Where(x => x.AlertPriority == alertPriority)
            .OrderByDescending(x => x.Id);
    }

    public IQueryable<AlertMaster> GetPaginatedByAlertNameAndPriority(string alertName, string alertPriority)
    {
        return _dbContext.AlertMasters.Active().AsNoTracking()
            .Where(x => x.AlertName == alertName && x.AlertPriority == alertPriority)
            .OrderByDescending(x => x.Id);
    }

    public async Task<bool> IsNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
            return await Entities.AnyAsync(e => e.AlertName == name);

        var result = await Entities
            .Where(e => e.AlertName == name)
            .ToListAsync();

        return result.Unique(id);
    }

    public async Task<bool> IsAlertIdExist(string id)
    {
        if (!id.IsValidGuid())
            return await Entities.AnyAsync(e => e.AlertId == id);

        return false;
    }
}