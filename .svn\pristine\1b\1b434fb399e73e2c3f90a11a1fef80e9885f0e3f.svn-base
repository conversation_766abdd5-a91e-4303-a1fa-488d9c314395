﻿using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Create;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Delete;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Update;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetList;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageSolutionMappingModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class PageSolutionMappingService : BaseService, IPageSolutionMappingService
{
    public PageSolutionMappingService(IHttpContextAccessor accessor) : base(accessor) { }

    public async Task<List<PageSolutionMappingListVm>> GetPageSolutionMappingList()
    {
        Logger.LogDebug("Get All PageBuilders");

        return await Mediator.Send(new GetPageSolutionMappingListQuery());
    }

    public async Task<PageSolutionMappingDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "PageSolutionMapping Id");

        Logger.LogDebug($"Get PageSolutionMapping Detail by Id '{id}'");

        return await Mediator.Send(new GetPageSolutionMappingDetailQuery { Id = id });
    }

    public async Task<BaseResponse> CreateAsync(CreatePageSolutionMappingCommand createPageSolutionMappingCommand)
    {
        Logger.LogDebug($"Creating PageSolutionMapping '{createPageSolutionMappingCommand.Name}'");

        return await Mediator.Send(createPageSolutionMappingCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdatePageSolutionMappingCommand updatePageSolutionMappingCommand)
    {
        Logger.LogDebug($"Updating PageSolutionMapping '{updatePageSolutionMappingCommand.Name}'");

        return await Mediator.Send(updatePageSolutionMappingCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "PageSolutionMapping Id");

        Logger.LogDebug($"Delete PageSolutionMapping Details by Id '{id}'");

        return await Mediator.Send(new DeletePageSolutionMappingCommand { Id = id });
    }

    public async Task<bool> IsPageSolutionMappingNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "PageSolutionMapping Name");

        Logger.LogDebug($"Check Name Exists Detail by PageSolutionMapping Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetPageSolutionMappingNameUniqueQuery { Name = name, Id = id });
    }

    public async Task<PaginatedResult<PageSolutionMappingListVm>> GetPaginatedPageSolutionMapping(GetPageSolutionMappingPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in PageSolutionMapping Paginated List");

        return await Mediator.Send(query);
    }
}
