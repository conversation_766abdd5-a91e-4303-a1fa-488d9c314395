﻿const workflowActionNameExists = "Admin/WorkflowAction/WorkflowActionNameExist";
let actionId = "";
let actionName = "";

const controlOptions = {
    groups: [
        {
            id: 'custom',
            label: 'CP Custom Elements',
            elementOrder: ['database,server,replication'],
        },
        {
            id: 'common',
            label: 'Form Fields',
            elementOrder: [
                'text-input',
                'number',
                'select',
                'checkbox',
                'radio',
                'textarea',
                'date-input',
                'hidden',
                'upload',
                'button',
                'email'
            ],
        },
        {
            id: 'html',
            label: 'HTML Elements',
            elementOrder: ['header', 'paragraph', 'divider'],
        },
        {
            id: 'layout',
            label: 'Layout',
            elementOrder: ['header', 'paragraph', 'divider'],
        },
    ],
    groupOrder: ['common', 'custom', 'html', 'layout'],
    sortable: false,
    disable: {
        elements: ["upload"]
    },
    elements: [
        {
            tag: 'select',
            config: {
                label: 'Database',
                disabledAttrs: ['type', 'className', 'DatabaseTypeID'],//Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder', 'DatabaseType'],// disable delete button.
            },

            meta: {
                group: 'custom',
                id: 'database',
                icon: '', //〚〛
            },
            attrs: {
                name: "@@DBName",
                placeholder: "Select Option",
                className: 'form-select-modal-dynamic',
                type: 'select',
                required: true,
                DatabaseType: "all",
                DatabaseTypeID: " "
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Server',
                disabledAttrs: ['type', 'className', 'ServerRoleID', 'ServerTypeID'],
                lockedAttrs: ['required', 'name', 'ServerRole', 'ServerType', 'placeholder'],
            },
            meta: {
                group: 'custom',
                id: 'server',
                icon: '',//⌨
            },
            attrs: {
                name: "@@ServerName",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
                ServerRole: "",
                ServerType: "",
                ServerRoleID: "",
                ServerTypeID: ""
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Replication',
                disabledAttrs: ['type', 'className'], //Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder'], // disable delete button.
            },
            meta: {
                group: 'custom',
                id: 'replication',
                icon: '', //⌨
            },
            attrs: {
                name: "@@replication",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
            },
        },
        {
            tag: 'select',
            config: {
                label: 'InfraObject',
                disabledAttrs: ['type', 'className', 'InfraObjectID'],
                lockedAttrs: ['required', 'name', 'InfraObject', 'placeholder'],
            },
            meta: {
                group: 'custom',
                id: 'infraobject',
                icon: '',//⌨
            },
            attrs: {
                name: "@@infraobject",
                className: 'form-select-modal-dynamic',
                placeholder: "Select Option",
                type: 'select',
                required: true,
                InfraObject: "",
                InfraObjectID: "",
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Workflows',
                disabledAttrs: ['type', 'className'], //Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder', 'dependentAction'], // disable delete button.
            },
            meta: {
                group: 'custom',
                id: 'workflow',
                icon: '', //⌨
            },
            attrs: {
                name: "@@workflow_name",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
                dependentAction: false
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Node',
                disabledAttrs: ['type', 'className', 'id'], //Hide from user.
                lockedAttrs: ['required', 'name', 'placeholder', 'multiple'], // disable delete button.
            },
            meta: {
                group: 'custom',
                id: 'nodes',
                icon: '', //⌨
            },
            attrs: {
                name: "nodes",
                id: "assigned_nodes",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
                multiple: true
            },
        },
        {
            tag: 'table',
            config: {
                label: 'Sudo/Su Table',
                disabledAttrs: ['className', 'rows', 'columns'],
                lockedAttrs: ['required', 'name'],
            },
            meta: {
                group: 'custom',
                id: 'table',
                icon: '⌨',
            },
            attrs: {
                name: 'Sudo/Su Table',
                className: 'custom-table',
                required: true,
                rows: 1, // Initial number of rows
                columns: 4, // Initial number of columns
            },
        },
        {
            tag: 'select',
            config: {
                label: 'Select',
                disabledAttrs: ['type', 'className'],
                lockedAttrs: ['required', 'name', 'placeholder', 'multiple'],
            },
            meta: {
                group: 'common',
                id: 'select',
                icon: 'select',
            },
            options: [
                {
                    label: '',
                    value: '',
                },
                {
                    label: 'Option-1',
                    value: 'option-1',
                }
            ],
            attrs: {
                name: "select_field",
                className: 'form-select-modal-dynamic',
                type: 'select',
                placeholder: "Select Option",
                required: true,
                multiple: false
            },
        },
        {
            tag: 'input',
            attrs: {
                name: "radio_field",
                type: 'radio',

            },
            config: {
                label: 'Radio Group',
                disabledAttrs: ['type'],
                lockedAttrs: ['name'],
            },
            meta: {
                group: 'common',
                icon: 'radio-group',
                id: 'radio'
            },
            options: (() => {
                let options = [1, 2, 3].map(i => {
                    return {
                        label: 'Radio ' + i,
                        value: 'radio-' + i,
                        selected: false,
                    };
                });
                return options;
            })(),
        },
        {
            tag: 'input',
            config: {
                label: 'Checkbox',
                disabledAttrs: ['type'],
                lockedAttrs: ['name', 'required', 'readonly'],
                hideLabel: true
            },
            meta: {
                group: 'common',
                icon: 'checkbox',
                id: 'checkbox'
            },
            attrs: {
                name: "checkbox_field",
                type: 'checkbox',
                required: true,
                readonly: false,
            },
            options: [{
                label: 'Option 1',
                value: 'option-1',
                checked: false,
            }],
        },
        {
            tag: 'input',
            attrs: {
                name: "checkbox_group",
                type: 'checkbox',
                //required: true,
            },
            config: {
                label: 'Checkbox Group',
                disabledAttrs: ['type'],
                lockedAttrs: ['name', 'required'],
                hideLabel: false
            },
            meta: {
                group: 'common',
                icon: 'checkbox-group',
                id: 'checkbox-group'
            },
            options: (() => {
                let options = [1, 2, 3].map(i => {
                    return {
                        label: 'Option ' + i,
                        value: 'option-' + i,
                        checked: false,
                    };
                });
                return options;
            })(),
        },
        {
            tag: 'textarea',
            attrs: {
                name: "text_area",
                type: 'textarea',
                required: false,
                //minlength: "",
                //maxlength: "",
            },
            config: {
                label: 'Text Area',
                disabledAttrs: ['type'],
                lockedAttrs: ['name', 'required', 'maxlength', 'minlength'],
            },
            meta: {
                group: 'common',
                icon: 'textarea',
                id: 'textarea'
            },
        },
        {
            tag: "input",
            config: {
                label: 'Text Input',
                disabledAttrs: ['type', "class"],
                lockedAttrs: ['name', 'required', 'placeholder', 'minlength', 'maxlength', 'encryption', 'restrict', 'readonly', 'textInputValue'],
            },
            meta: {
                group: 'common',
                icon: 'text-input',
                id: 'text-input'
            },
            attrs: {
                name: "text_field",
                textInputValue: "",
                required: true,
                // placeholder: "Enter Text",
                type: "text",
                maxlength: "",
                minlength: "",
                encryption: false,
                //customvalidation: false,
                restrict: false,
                readonly: false,
            },
        },
        {
            tag: "input",
            config: {
                "label": "Number Input",
                "disabledAttrs": ["type"],
                lockedAttrs: ['name', 'placeholder', 'required', 'minlength', 'maxlength'],
            },
            meta: {
                "group": "common",
                "icon": "hash",
                "id": "number"
            },
            attrs: {
                name: "number_field",
                placeholder: "Enter Number",
                type: "number",
                required: true,
                minlength: "",
                maxlength: "",
            }
        },
        {
            tag: "input",
            config: {
                label: "Password Input",
                disabledAttrs: ["type", 'encryption', 'minlength', 'maxlength'],
                lockedAttrs: ['name', 'required', 'placeholder', 'minlength', 'maxlength'],
            },
            meta: {
                "group": "common",
                "icon": "",//menu
                "id": "password-input"
            },
            attrs: {
                name: "password_field",
                type: "password",
                placeholder: "Enter Password",
                required: true,
                minlength: "",
                maxlength: "",
                //encryption: true,               
            }
        },
        {
            tag: "input",
            config: {
                label: 'Command',
                disabledAttrs: ['type'],
                lockedAttrs: ['name', 'placeholder'],
            },
            meta: {
                group: 'common',
                icon: 'text-input',
                id: 'command'
            },
            attrs: {
                name: "Command",
                placeholder: "Enter Command",
                type: "textarea",
            },
        },
        {
            tag: "input",
            config: {
                label: "IP Address",
                disabledAttrs: ["type", "attrid"],
                lockedAttrs: ['name', 'placeholder', 'required'],
            },
            meta: {
                group: "common",
                icon: "", //text-input
                id: "ip-address"
            },
            attrs: {
                name: "IpAddress",
                type: "text",
                placeholder: "Enter IP Address",
                required: true,
                attrid: "ipAddressField"
            }
        },
    ],
}

$(document).ready(function () {

    $('#parentAction').sortable({
        cancel: '#placeholder-span',
        update: function (event, ui) {

            // Iterate through the sorted elements
            $('#parentAction > div').each(function (newIndex) {
                var $this = $(this);
                var newIndexValue = newIndex + 1;
                // Update the index in the element
                $this.find('.workflowIndex').html(newIndexValue);
            });
        },

    }).disableSelection();
})


function populateSecondDropdown(selectedRole, changedDropdown) {
    let modifiedString = changedDropdown.name.replace("ServerRole", "ServerType");
    let siblingElement = $(`select[name=${modifiedString}]`);
    if (siblingElement) {
        serverType(siblingElement, selectedRole);
    }
}

function serverType(element, type) {
    let serverTypeID = element.attr('id');
    $(`#${serverTypeID}`).empty();
    if (type) {
        $.ajax({
            type: "GET",
            url: RootUrl + 'Configuration/Server/GetServerType',
            dataType: "json",
            data: { id: type },
            success: function (result) {
                if (result.success) {
                    $(`#${serverTypeID}`).empty();
                    $(`<option>`).val('').text('Select Server Type').appendTo(`#${serverTypeID}`);
                    (result?.data || []).forEach(option => {
                        $('<option>').val(option.name).text(option.name).attr('idServerType', option.id).appendTo(`#${serverTypeID}`);
                    });
                } else {
                    errorNotification(result);
                }
            },
        });
    } else {
        $(`#${serverTypeID}`).empty().append(
            $('<option>', { value: '', text: 'Select Server Type' }),
        );
    }
}


function handleAdd(newRow) {
    //event.preventDefault();

    var $table = $('.custom-table');
    const clonedRow = newRow.cloneNode(true);

    // Clear input and select values in the cloned row
    const inputElements = clonedRow.querySelectorAll('input');
    const selectElements = clonedRow.querySelectorAll('select');

    inputElements.forEach(input => {
        input.value = '';
    });

    selectElements.forEach(select => {
        select.value = '';
    });

    // Check if the cloned row already has a delete button
    const hasDeleteButton = clonedRow.querySelector('.delete-button');

    if (!hasDeleteButton) {
        const lastTd = clonedRow.querySelector('td:last-child');
        lastTd.innerHTML += '<button class="delete-button" onclick="handleDelete(event)">Delete</button>';
    }

    // Append the cloned row to the table
    $table[0].appendChild(clonedRow);

}
function handleDelete(event) {
    event.preventDefault(); // Prevent default form submission behavior
    // Remove the closest row (tr) when the delete button is clicked
    $(event.target).closest('tr').remove();
}

function handleUpdate(formData) {
    //var id = formData.detail.id
    var rows = formData.detail.rows
    var columns = formData.detail.columns
    var listofrows = Object.keys(rows)
    let columnWidths = [25, 50, 75, 100]

    setTimeout(() => {
        let buttonGrp = $('.field-preview .f-btn-group button');
        if (buttonGrp?.length > 0) {
            $('.field-preview button').on('click', function (event) {
                event.preventDefault();
            });
        }
    }, 150);

    ////frdb;
    //var inputElements = document.querySelectorAll('.DatabaseType');
    let inputElements = document.querySelectorAll('.DatabaseType');
    let columnElement = $('.formeo-stage .formeo-row');

    columnElement.each((index, element) => {
        let columnPreset = $(element).find('.column-preset');

        Object.keys(columns).forEach((s) => {
            let specificColumnPreset = $(element).find('#' + s);
            let percentValue = columns[s]?.config?.width.split('%')

            if (specificColumnPreset.length > 0) {
                if (columnPreset.children().length === 0) {
                    let tr = `<option selected>Select Column Width</option>`;

                    columnWidths.forEach((column) => {
                        tr += `<option value=${column}>${column}</option>`;
                    });

                    columnPreset.append(tr);
                    percentValue.length && columnPreset.val(percentValue[0]);
                }
            }
        });
    });
    inputElements?.forEach(async function (inputElement) {
        if (inputElement.tagName === 'INPUT') {
            let labelElement = document.createElement('label');
            labelElement.textContent = 'Database Type';
            let selectElement = document.createElement('select');
            selectElement.setAttribute('name', inputElement.getAttribute('name'));
            selectElement.setAttribute('id', inputElement.getAttribute('id'));
            selectElement.setAttribute('class', 'DatabaseType');
            let response = await fetch(`${RootUrl}Configuration/Database/GetPagination`);
            let result = await response.json();

            if (result?.success) {
                const optionElement = document.createElement('option');
                optionElement.value = '';
                optionElement.textContent = 'Select Database Type';
                selectElement.appendChild(optionElement);
                const uniqueValues = new Set();
                result?.data?.data?.forEach(function (dbtype) {
                    if (!uniqueValues.has(dbtype?.databaseType?.toLowerCase())) {
                        let optionElement = document.createElement('option');
                        optionElement.setAttribute('value', dbtype?.databaseTypeId);
                        optionElement.textContent = dbtype?.databaseType;
                        selectElement.appendChild(optionElement);
                        uniqueValues.add(dbtype?.databaseType?.toLowerCase());
                    }
                });
            } else {
                errorNotification(result)
            }
            
            selectElement.addEventListener('change', function (event) {
                let selectedValue = event.target.value;
                let databaseType = $('.DatabaseType option:selected').text();
                let fieldId = selectElement.id;
                let subString = fieldId.substring(0, 36)
                formData.detail.fields[subString].attrs.DatabaseType = databaseType;
                formData.detail.fields[subString].attrs.DatabaseTypeID = selectedValue;
                //console.log(formData)
            });
            let parentElement = inputElement.parentNode;
            parentElement.insertBefore(labelElement, inputElement);
            parentElement.insertBefore(selectElement, inputElement);
            parentElement.removeChild(inputElement);
        }
    });



    //////////////////////////////////////frdb;
    var inputElements2 = document.querySelectorAll('.InfraObject');
    let columnElement2 = $('.formeo-stage .formeo-row');

    columnElement2.each((index, element) => {
        let columnPreset = $(element).find('.column-preset');

        Object.keys(columns).forEach((s) => {
            let specificColumnPreset = $(element).find('#' + s);
            let percentValue = columns[s]?.config?.width.split('%')

            if (specificColumnPreset.length > 0) {
                if (columnPreset.children().length === 0) {
                    let tr = `<option selected>Select Column Width</option>`;

                    columnWidths.forEach((column) => {
                        tr += `<option value=${column}>${column}</option>`;
                    });

                    columnPreset.append(tr);
                    percentValue.length && columnPreset.val(percentValue[0]);
                }
            }
        });
    });
    inputElements2.forEach(async function (inputElement) {
        if (inputElement.tagName === 'INPUT') {
            var labelElement2 = document.createElement('label');
            labelElement2.textContent = 'InfraObject';

            var selectElement2 = document.createElement('select');
            selectElement2.setAttribute('name', inputElement.getAttribute('name'));
            selectElement2.setAttribute('id', inputElement.getAttribute('id'));
            selectElement2.setAttribute('class', 'InfraObject');
            var options2;

            await $.ajax({
                url: RootUrl + "Configuration/Node/GetNodeNames",
                method: 'GET',
                dataType: 'json',
                success: function (result) {
                    if (result.success) {
                        options2 = result?.data;
                    } else {
                        errorNotification(result)
                    }
                }
            });

            var optionElement = document.createElement('option');
            optionElement.setAttribute('value', "");
            optionElement.textContent = "Select InfraObject";
            selectElement2.appendChild(optionElement);

            options2?.forEach(function (optionValue) {
                var optionElement = document.createElement('option');
                optionElement.setAttribute('value', optionValue?.id);
                optionElement.textContent = optionValue?.name;
                selectElement2.appendChild(optionElement);
            });
            selectElement2.addEventListener('change', function (event) {
                let selectedValue = event.target.value;
                let fieldId = selectElement2.id;
                let Infra = $('.InfraObject option:selected').text();
                let subString = fieldId.substring(0, 36)
                formData.detail.fields[subString].attrs.InfraObjectID = selectedValue;
                formData.detail.fields[subString].attrs.InfraObject = Infra;
            });
            var parentElement2 = inputElement.parentNode;
            parentElement2.insertBefore(labelElement2, inputElement);
            parentElement2.insertBefore(selectElement2, inputElement);
            parentElement2.removeChild(inputElement);
        }
    });

    /////ENCRYPTION

    var inputEnc = document.querySelectorAll('.password-input');
    inputEnc.forEach(function (inputElement) {
        if (inputElement.tagName === 'INPUT') {
            var labelElement = document.createElement('label');
            labelElement.textContent = 'Encrypt';
            var parentElement = inputElement.parentNode;
            parentElement.insertBefore(labelElement, inputElement);
        }
    });

    //var inputPWD = document.querySelectorAll('.customvalidation');
    var inputPWD = document.querySelectorAll('.restrict');
    inputPWD.forEach(function (inputElement) {
        if (inputElement.tagName === 'INPUT') {
            var label = inputElement.parentNode.querySelector('label');
            if (!label) {
                var labelElement = document.createElement('label'); 
                labelElement.textContent = 'Restrict special characters';
                var parentElement = inputElement.parentNode;
                parentElement.insertBefore(labelElement, inputElement);

            }
        }
    });
    ////Readonly
    var readonly = document.querySelectorAll('.readonly');

    readonly.forEach(function (inputElement) {
        var label = inputElement.parentNode.querySelector('label');

        if (!label) {
            if (inputElement.tagName === 'INPUT') {
                var labelElement = document.createElement('label');
                labelElement.textContent = 'Readonly';
                var parentElement = inputElement.parentNode;
                parentElement.insertBefore(labelElement, inputElement);
            }
        }
    });

    ////Readonly
    var textInputValue = document.querySelectorAll('.textInputValue');

    textInputValue.forEach(function (inputElement) {
        var label = inputElement.parentNode.querySelector('label');

        if (!label) {
            if (inputElement.tagName === 'INPUT') {
                var labelElement = document.createElement('label');
                labelElement.textContent = 'Value';
                inputElement.placeholder = 'Enter Value';
                var parentElement = inputElement.parentNode;
                parentElement.insertBefore(labelElement, inputElement);
            }
        }
    });

    ////MINLENGTH
    var inputMin = document.querySelectorAll('.minlength');

    inputMin.forEach(function (inputElement) {
        inputElement.type = 'number';
        var label = inputElement.parentNode.querySelector('label');

        if (!label) {
            if (inputElement.tagName === 'INPUT') {
                inputElement.placeholder = "Min Length"
                var labelElement = document.createElement('label');
                labelElement.textContent = 'Min Length';
                var parentElement = inputElement.parentNode;
                parentElement.insertBefore(labelElement, inputElement);
            }
        }
    });

    ////MAXLENGTH

    var inputMax = document.querySelectorAll('.maxlength');
    inputMax.forEach(function (inputElement) {
        inputElement.type = 'number';
    });

    /////for server role and type
    let roleElements = document.querySelectorAll('.ServerRole');
    async function fetchData(url) {
        let resultData = null;
        await $.ajax({
            type: "GET",
            url: RootUrl + url,
            dataType: "json",
            success: function (result) {
                if (result.success) {
                    resultData = result.data;
                } else {
                    errorNotification(result)
                }
            },
        });
        return resultData;
    }
    roleElements?.forEach(async function (roleElement) {
        if (roleElement.tagName === 'INPUT') {
            let roleLabelElement = document.createElement('label');
            roleLabelElement.textContent = 'Server Role';
            let roleSelectElement = document.createElement('select');
            roleSelectElement.setAttribute('name', roleElement.getAttribute('name'));
            roleSelectElement.setAttribute('id', roleElement.getAttribute('id'));
            roleSelectElement.setAttribute('class', 'ServerRole');

            // Populate Server Role options
            let serverRoleData = await fetchData('Configuration/Server/GetServerRole');
            if (serverRoleData) {
                let roleOptionElement = document.createElement('option');
                roleOptionElement.setAttribute('value', '');
                roleOptionElement.textContent = 'Select Server Role';
                roleSelectElement.appendChild(roleOptionElement);
                serverRoleData.forEach(function (optionValue) {
                    let roleOptionElement = document.createElement('option');
                    roleOptionElement.setAttribute('value', optionValue.name);
                    roleOptionElement.setAttribute('data-ServerRoleID', optionValue.id);
                    roleOptionElement.textContent = optionValue.name;
                    roleSelectElement.appendChild(roleOptionElement);
                });
            }

            // Append elements to the parent container for Server Role dropdown
            let roleParentElement = roleElement.parentNode;
            if (roleParentElement) {
                roleParentElement.innerHTML = '';
                roleParentElement.appendChild(roleLabelElement);
                roleParentElement.appendChild(roleSelectElement);
            }
        }

        let ServerRoleElements = document.querySelectorAll('select.ServerRole');
        ServerRoleElements.forEach(function (elementID) {
            document.getElementById(elementID.getAttribute('id'))?.addEventListener("change", function (event) {
                let selectedRole = event.target.value;
                let selectedRoleId = $(`#${elementID.getAttribute('id')} option:selected`).attr('data-ServerRoleID');
                let fieldId = elementID.getAttribute('id');//roleSelectElement.id;
                let subString = fieldId.substring(0, 36)
                if (selectedRole)
                    role = "server"
                formData.detail.fields[subString].attrs.ServerRole = selectedRole;
                formData.detail.fields[subString].attrs.ServerRoleID = selectedRoleId;
                populateSecondDropdown(selectedRoleId, event.target);
            });
        });
    });

    let serverType = document.querySelectorAll('.ServerType');
    serverType?.forEach(function (inputElement) {
        if (inputElement.tagName === 'INPUT') {
            let labelElement = document.createElement('label');
            labelElement.textContent = 'Server Type';
            let selectElement = document.createElement('select');
            selectElement.setAttribute('name', inputElement.getAttribute('name'));
            selectElement.setAttribute('id', inputElement.getAttribute('id'));
            selectElement.setAttribute('class', 'ServerType');
            let options = ['Select Server Type'];
            options.forEach(function (optionValue) {
                let optionElement = document.createElement('option');
                optionElement.setAttribute('value', optionValue);
                optionElement.textContent = optionValue;
                selectElement.appendChild(optionElement);
            });
            selectElement.addEventListener('change', function (event) {
                let selectedValue = event.target.value;
                let fieldId = selectElement.id;
                let SelectedTypeID = $(`#${inputElement.getAttribute('id')} option:selected`).attr('idServerType');
                let subString = fieldId.substring(0, 36)
                formData.detail.fields[subString].attrs.ServerType = selectedValue;
                formData.detail.fields[subString].attrs.ServerTypeID = SelectedTypeID;
            });
            let parentElement = inputElement.parentNode;
            parentElement.insertBefore(labelElement, inputElement);
            parentElement.insertBefore(selectElement, inputElement);
            parentElement.removeChild(inputElement);
        }
    });

    var workflowEl = document.querySelectorAll(".dependentAction");
    workflowEl.forEach(function (input) {

        if (input.tagName === 'INPUT') {

            var label = input.parentNode.querySelector('label');
            if (label) {
                // <label> tag is found inside the parent node
            } else {
                // <label> tag is not found inside the parent node
                var labelElement = document.createElement('label');
                labelElement.textContent = 'Include dependent workflow actions';
                var parentElement = input.parentNode;
                parentElement.insertBefore(labelElement, input);
                input.addEventListener('change', function (event) {

                    event.preventDefault()
                    if (event.target.checked) input.setAttribute("data-actions", true)
                    else input.setAttribute("data-actions", false)
                });
            }
        }
    });

    /////tbale 
    // For each table with the class .custom-table

    setTimeout(() => {
        // Find the target table
        var $table = $('.custom-table');
        if ($table.children().length == 0) {

            var $headerRow = $('<tr class=".header_row"></tr>');
            $headerRow.append('<th>Authenticate Type</th>');
            $headerRow.append('<th>Path</th>');
            $headerRow.append('<th>User</th>');
            $headerRow.append('<th>Password</th>');
            $headerRow.append('<th>Action</th>');

            // Append the header row to the table
            $table.append($headerRow);

            // Create a data row
            var $dataRow = $('<tr></tr>');
            $dataRow.append('<td><select class=form-select-modal-dynamic placeholder="Select Auth Type" name="SubstituteAuthenticationType"><option value="">Select Type</option><option value="sudo su">sudo su</option><option value="su">su</option><option value="asu">asu</option><option value="sudo">sudo</option><option value="privrun">privrun</option><option value="other">other</option></select></td>');
            $dataRow.append('<td><input placeholder="Enter Path" type="text" name="SubstituteAuthenticationPath"/></td>');
            $dataRow.append('<td><input placeholder="Enter User" type="text" name="SubstituteAuthenticationUser" /> </td>');
            $dataRow.append('<td><input placeholder="Enter Password" type="text" name="SubstituteAuthenticationPassword" /></td>');


            // Add action buttons to the data row
            var $actionCell = $('<td></td>');
            $actionCell.append(`<button class="add-button" onclick="event.preventDefault(); handleAdd(this.parentElement.parentElement)">Add</button>`);
            //$actionCell.append('<button class="delete-button">Delete</button>');
            $dataRow.append($actionCell);
            // Append the data row to the table
            $table.append($dataRow);
        }
        // Create a header row

    }, 500)

    ////////select 
    var selectFields = document.querySelectorAll(".multiple")
    selectFields.forEach(function (input) {

        if (input.tagName === 'INPUT') {

            var label = input.parentNode.querySelector('label');
            if (label) {
                // <label> tag is found inside the parent node
            } else {
                // <label> tag is not found inside the parent node
                var labelElement = document.createElement('label');
                labelElement.textContent = 'isMultiple';
                var parentElement = input.parentNode;
                parentElement.insertBefore(labelElement, input);
                input.addEventListener('change', function (event) {

                    event.preventDefault()
                    if (event.target.checked) input.setAttribute("multiple", true)
                    else input.setAttribute("multiple", false)

                });
            }

        }
    })

    ////for encryption

    var encryption = document.querySelectorAll(".encryption");
    encryption.forEach(function (input) {

        if (input.tagName === 'INPUT') {

            var label = input.parentNode.querySelector('label');
            if (label) {
                // <label> tag is found inside the parent node
            } else {
                // <label> tag is not found inside the parent node
                var labelElement = document.createElement('label');
                labelElement.textContent = 'Encrypted';
                var parentElement = input.parentNode;
                parentElement.insertBefore(labelElement, input);
                input.addEventListener('change', function (event) {

                    event.preventDefault()
                    if (event.target.checked) input.setAttribute("data-encryption", true)
                    else input.setAttribute("data-encryption", false)

                });
            }
        }
    });

    const hideAttributeButton = document.querySelectorAll('.add-attrs')

    hideAttributeButton.forEach(function (target) {
        target.style.display = 'none'
    });

    ////forConditionas
    var conditionTargets = document.querySelectorAll('.condition-target');

    // Loop through each condition-target element
    conditionTargets.forEach(function (target) {
        // Find input elements within each condition-target element
        var inputs = target.querySelectorAll('input.f-autocomplete-display-field');

        // Loop through input elements and disable autocomplete for the specific placeholder
        inputs.forEach(function (input) {
            if (input.placeholder === 'target / value') {

                const nextElement = input.nextElementSibling;

                if (nextElement) {
                    nextElement?.nextElementSibling?.remove();
                }
            }
        });
    });

    var rowColButtons = document.querySelectorAll('.action-btn-wrap button');
    var EditColRemoveBtn = document.querySelectorAll('.prop-remove')
    rowColButtons.forEach(function (button, index) {

        if (button.classList.contains('item-handle')) {
            button.setAttribute('title', 'Move');
        } else if (button.classList.contains('item-edit-toggle')) {
            button.setAttribute('title', 'Edit');
        } else if (button.classList.contains('item-clone')) {
            button.setAttribute('title', 'Clone');
        } else if (button.classList.contains('item-remove')) {
            button.setAttribute('title', 'Remove');
        }
    });

    EditColRemoveBtn.forEach(function (button, index) {
        button.setAttribute('title', 'Remove');

    });

    ///// for removing conditions
    const inputFields = document.querySelectorAll('.children .field-edit');

    inputFields.forEach(field => {

        const parentId = field.closest('.formeo-field').getAttribute('id');

        const formFields = formData.detail?.fields

        if (formFields[parentId] && (formFields[parentId]?.meta?.group.toLowerCase() == 'custom'
            || formFields[parentId]?.meta?.group.toLowerCase() == 'html'
            || formFields[parentId]?.meta?.id.toLowerCase() == 'ip-address'
            || formFields[parentId]?.meta?.id.toLowerCase() == 'dynamicbutton')) {

            if (formFields[parentId]?.tag && formFields[parentId].tag == 'hr') {
                $('#' + parentId).find('.panel-labels h5:contains("Conditions")').remove();
                $('#' + parentId).find('.conditions-panel').remove();
            } else {
                $('#' + parentId).find('.panel-labels h5:contains("Conditions")').remove();
            }

        }
    });

    var labelElements = document.querySelectorAll('.formeo-stage .formeo-field .prev-label label');
    var otherElements = document.querySelectorAll('.formeo-stage .formeo-field .f-checkbox label');

    labelElements.forEach(function (labelElement) {
        validateLabelInput(labelElement);
    });

    otherElements.forEach(function (labelElement) {
        const parentElement = labelElement.closest('.formeo-field');

        if (parentElement) {

            const hasPrevLabelClass = Array.from(parentElement.children).some(child => child.classList.contains('prev-label'));
            !hasPrevLabelClass && validateLabelInput(labelElement);
        } else {
            validateLabelInput(labelElement);
        }

    });
}


var formIsValid = true
function validateLabelInput(labelElement) {

    const labelValue = labelElement.textContent.trim();
    const parentContainer = labelElement.closest('.formeo-field');

    if (labelValue.length > 50 || labelValue == '') {

        formIsValid = false
        const errorDiv = document.createElement('div');
        errorDiv.classList.add('error-message');
        errorDiv.textContent = 'Label must be Between 1 to 50 characters';
        errorDiv.style.color = 'red';

        const existingError = parentContainer.querySelector('.error-message');
        if (existingError) {
            // If an error message exists, replace it
            parentContainer.replaceChild(errorDiv, existingError);
        } else {
            // If no error message exists, append the error message after the prev-label div
            const prevLabelDiv = parentContainer.querySelector('.prev-label') ? parentContainer.querySelector('.prev-label') : parentContainer.querySelector('.f-checkbox');
            prevLabelDiv.insertAdjacentElement('afterend', errorDiv);
        }
    } else {
        // Remove any existing error message
        const existingError = parentContainer.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
            formIsValid = true
        }
    }
}

const formeoOptions = {
    editorContainer: '#actionForms',
    controls: controlOptions,
    events: {
        onUpdate: (formData) => handleUpdate(formData)
    },
    disableActionButtons: true,
}
/////////////////////////////////////////////////

    var ExecuteTreeDataScriptJson;
    var deleteActionUrl = "Admin/WorkflowAction/ActionDelete"
    var postactionurl = "Admin/WorkflowAction/CreateOrUpdate"
    var lockactionurl = "Admin/WorkflowAction/LockCreateOrUpdate"
    var lockStatusUpdateUrl = "Admin/WorkflowAction/LockStatusUpdate"
    var saveasActionUrl = "Admin/WorkflowAction/SaveAsCreateOrUpdate"
    var nodeId = ""

    function iconChange(className, iconName, fontSize) {
        var liElement = document.getElementsByClassName(className);
        if (liElement?.[0]) {
            var spanElement = liElement?.[0].querySelector("span");

            if (spanElement) {
                spanElement.classList.remove();
                spanElement.classList.add(iconName, fontSize);
            } else {
                spanElement.classList.add('⌨', fontSize);
                //console.error("No span element found with class 'control-icon' inside the <li> element.");
            }
        } else {
            dynamicIconChange();
            //console.error("No <li> element found with class 'database-control'.");
        }
    }

    function dynamicIconChange() {
        setTimeout(() => {
            iconChange("database-control", "cp-database", "fs-5");
            iconChange("server-control", "cp-server", "fs-5");
            iconChange("replication-control", "cp-replication-rotate", "fs-5");
            iconChange("workflow-control", "cp-workflow", "fs-5");
            iconChange("singlesignon-control", "cp-single-sign_on", "fs-5");
            iconChange("nodes-control", "cp-network", "fs-5");
            iconChange("infraobject-control", "cp-infra-object", "fs-5");
            iconChange("password-input-control", "cp-lock", "fs-5");
            iconChange("ip-address-control", "cp-ip-address", "fs-4");
        }, 500);
    }

    $('#ActionbuilderconfigModal').on('shown.bs.modal', function (e) {

        if ($(".finish_btn ").text() == "Save") {

            $("#formBuilderType ").val("Common").trigger('change')
        }
        //$('#ActionbuilderconfigModal').attr("aria-hidden", true)
        var formeo = new FormeoEditor(formeoOptions);
        $(".formeo-stage").addClass("mt-5");
        dynamicIconChange();
    })


    $(".AddAction").on("click", function () {


        $(".first").css({ "pointer-events": "none" });
        $(".current").css({ "pointer-events": "none" });
        $(".last").css({ "pointer-events": "none" });
        $(".done").css({ "pointer-events": "none" });

        $(".finish_btn").removeClass("disabled")
        $("#formbuildname-error").removeClass("field-validation-error")
        $("#description").val("")
        $("#timeWait").val("")
        $("#msBetweenTries").val("")
        $("#formBuilderInput").val("")
        $("#formbuildname-error").empty();
        $('#parentAction').empty();
        $("#formBuilderType option[value='all']").attr('selected', 'selected');
        $(".finish_btn").html("Save")
        nodeId = $(this).attr("nodeId")
        $(".copyContent").addClass("d-none")
        $("#steps-uid-0-t-0").trigger("click");
        ExecuteTreeDataView()
    })


    $("#restore").on("click", async function () {

        let actionName = $("#saveAsActionName").val()

        //let isName = await ajaxDebounce(validateName(actionName, $('#actionName-error'), 'Enter action name'), 500);

        let isName = await validateName(actionName, $('#actionName-error'), 'Enter action name')
        if (!isName) {
            return;
        }

        let splitData = $(this).attr("name").split("$")
        var returnJson = jsonResultActionView(splitData[2])
        //if (isName) {
            formData = {
                WorkflowActionId: splitData[2],
                __RequestVerificationToken: gettoken(),
                Name: actionName,
                isLock:true
            };
            $.ajax({
                type: "POST",
                url: RootUrl + saveasActionUrl,
                data: formData,
                dataType: "json",
                traditional: true,
                success: function (data) {

                    if (data.message) {
                        $('#alertClass').removeClass("info-toast")
                        $('#alertClass').addClass("success-toast")
                        $('#message').text(data.message)
                        $('#mytoastrdata').toast({ delay: 3000 });
                        $('#mytoastrdata').toast('show');
                        $(".iconClass").removeClass("cp-exclamation")
                        $(".iconClass").addClass("cp-check")
                    }
                    else {
                        $('#alertClass').removeClass("success-toast")
                        $('#alertClass').addClass("info-toast")
                        $('#message').text(data)
                        $(".iconClass").removeClass("cp-check")
                        $(".iconClass").addClass("cp-exclamation")
                        $('#mytoastrdata').toast({ delay: 3000 });
                        $('#mytoastrdata').toast('show');

                    }
                    getActionListView(splitData[1])
                }

            })
            $("#SaveAs").modal("hide")
       // }
    })

    $(".next_btn").on("click", async function (e) {

        e.preventDefault()

        let arrayName=[]
        $(".propertiWindow").hide();
        var formBuilderInput = $("#formBuilderInput").val();
        var formBuilderType = $("#formBuilderType").val();
        var serverName = $("#serverName").val();
        var description = $("#description").val()
        var timeWait = $("#timeWait").val()
        var msBetweenTries = $("#msBetweenTries").val()
        var buttonText = $(".finish_btn").text()
        var formBuilderText = $("#formBuilderType option:selected").text()
        var flagNameList = await alreadyExistActionList(formBuilderInput)

        ////Action builder data not there when clicked previous button
        //var formeo = new FormeoEditor(formeoOptions);
        //var propertydata = formeo.formData;

        if ($(".formeo-stage").children(".children").children().length == 0) {
            $('#alertClass').addClass("info-toast")
            $(".iconClass").addClass("cp-exclamation")
            $('#message').text("Please configure get input field!")
            $('#mytoastrdata').toast({ delay: 3000 });
            $('#mytoastrdata').toast('show');
            return false;
        }

        if (formBuilderInput == "") {
            $("#formbuildname-error").text('Enter action name')
                .addClass('field-validation-error')
            return false;
        }

        //else if (formBuilderInput != formBuilderInput.match(/^[a-zA-Z]+$/)) {
        //    $('#formbuildname-error').text('Please enter only letters').addClass('field-validation-error')
        //    return false;
        //}

        else if (flagNameList) {
            if (buttonText == "Save") {
                $("#formbuildname-error").text('Name already exist!')
                    .addClass('field-validation-error')
                return false;
            }
        }

        $(".formeo-stage .children .formeo-row .formeo-column .formeo-field").each((i,x) => {
            let name = $("#" + x.id + "-attrs-name-name").val()
            arrayName.push(name)
        })
        let dubilcateName;
        if (arrayName.length != 0) {
            dubilcateName = arrayName.filter((item, index) => arrayName.indexOf(item) !== index);
        }
        if (dubilcateName.length!=0) {
            $('#alertClass').addClass("info-toast")
            $(".iconClass").addClass("cp-exclamation")
            $('#message').text(dubilcateName.join(",") +" input Name already exist!")
            $('#mytoastrdata').toast({ delay: 3000 });
            $('#mytoastrdata').toast('show');
            return false;
        }
        var isName = await validateName(formBuilderInput, $('#formbuildname-error'), 'Enter action name');
        if (isName) {
            formBuilderInput ? $("#actionNameSummary").text(formBuilderInput) : $("#actionNameSummary").text("NA")
            formBuilderType ? $("#actionTypeSummary").text(formBuilderText) : $("#actionNameSummary").text("NA")
            serverName ? $("#serverNameSummary").text(serverName) : $("#serverNameSummary").text("NA")
            description ? $("#descriptionSummary").text(description) : $("#descriptionSummary").text("NA")
            form.steps("next");
            !$('.prev_btn').is(':visible') && $('.next_btn').is(':visible') ? $(".copyContent").removeClass("d-none") : $(".copyContent").addClass("d-none")
        }
    })

$(".prev_btn").on("click", async function (e) {

    $('.prev_btn').is(':visible') && !$('.next_btn').is(':visible') ? $(".copyContent").removeClass("d-none") : $(".copyContent").addClass("d-none")
})
let copiedText;

$(".copyContent").on("click", async function (e) {
    debugger
    copiedHTMLArray = [];
    copiedText=[]
    let element = $("#parentAction")[0]; // raw DOM element

    let range = document.createRange();
    range.selectNodeContents(element);

    let selection = window.getSelection();
    selection.removeAllRanges(); // Clear any existing selection
    selection.addRange(range);   // Set selection

    let clonedNodes = range.cloneContents().childNodes;
    let cleanedLines = [];

    // Loop through nodes and extract clean text
    clonedNodes.forEach(node => {
        let line = node.textContent.trim();
        // Remove leading line numbers (e.g., "1 ", "12 ", etc.)
        line = line.replace(/^\d+\s*/, '');
        if (line) {
            cleanedLines.push(line);
        }
    });
    copiedText = cleanedLines
    // Set the array
    copiedHTMLArray.push(range.cloneContents().childNodes)

    // ✅ Copy to clipboard so you can paste in Notepad
    let finalText = copiedText.join('\n');

    navigator.clipboard.writeText(finalText)
        .then(() => {
            console.log("Copied to clipboard!");
        })
        .catch(err => {
            console.error("Clipboard copy failed:", err);
        });


})


    $(".finish_btn").on("click", function () {

        var formeo = new FormeoEditor(formeoOptions);


        var propertydata = formeo.formData;


        var buttonValue = $(this).text().trim()


        var formBuilderInput = $("#formBuilderInput").val()
        var formBuilderType = $("#formBuilderType option:selected").val()


        var script = scriptActionList()
        var scriptJson = scriptActionListJson()

        let elements = document.querySelectorAll('li.formeo-field.first-field.last-field');
        if (elements) {
            elements?.forEach(function (id, index) {
                let fieldID = id?.id;
                Object.keys(propertydata.fields).forEach(function () {
                    let field = propertydata?.fields[fieldID];
                    field["index"] = index;
                });
            })
        }

        var formData;
        var formBuilderJson = {
            "formInput": propertydata,
            "executeActions": scriptJson,
            "test": {},
            "summary": {},
            "type": "",
            "description": $("#description").val(),
            "timeWait": $("#timeWait").val(),
            color: $("#formBuilderType option:selected").attr("color"),
            "msBetweenTries": $("#msBetweenTries").val()

        }

        if (buttonValue == "Save") {

            formData = {
                __RequestVerificationToken: gettoken(),
                nodeid: nodeId,
                actionname: formBuilderInput,
                properties: JSON.stringify(formBuilderJson),
                script: script,
                type: formBuilderType,
                isLock: false
            };
        }
        else {

            nodeId = $(".finish_btn").attr("nodeId")
            var id = $(".finish_btn").attr("id")

            formData = {
               __RequestVerificationToken: gettoken(),
                id: id,
                nodeid: nodeId,
                actionname: formBuilderInput,
                properties: JSON.stringify(formBuilderJson),
                script: script,
                type: formBuilderType,
                isLock: true
            };
        }
      
        $.ajax({
            type: "POST",
            url: RootUrl + postactionurl,
            data: formData,
            dataType: "json",
            traditional: true,
            contentType: 'application/x-www-form-urlencoded; charset=utf-8',
            success: function (data) {

                if (data.message) {
                    $('#alertClass').removeClass("info-toast")
                    $('#alertClass').addClass("success-toast")
                    $('#message').text(data.message)
                    $('#mytoastrdata').toast({ delay: 3000 });
                    $('#mytoastrdata').toast('show');
                    $(".iconClass").removeClass("cp-exclamation")
                    $(".iconClass").addClass("cp-check")
                }
                else {
                    $('#alertClass').removeClass("success-toast")
                    $('#alertClass').addClass("info-toast")
                    $('#message').text(data)
                    $(".iconClass").removeClass("cp-check")
                    $(".iconClass").addClass("cp-exclamation")
                    $('#mytoastrdata').toast({ delay: 3000 });
                    $('#mytoastrdata').toast('show');

                }

                getActionListView(nodeId)
                $("#formbuildname-error").text('')
                    .removeClass('field-validation-error')

            },
            error: function (data) {

                $('#alertClass').removeClass("success-toast")
                $('#alertClass').addClass("info-toast")
                $('#message').text(data.message)
                $(".iconClass").removeClass("cp-check")
                $(".iconClass").addClass("cp-exclamation")
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
                getActionListView(nodeId)
                $("#formbuildname-error").text('')
                    .removeClass('field-validation-error')
            }

        })
        $("#ActionbuilderconfigModal").modal("hide")


        //window.location.reload();
    })


    var copyContent;
    document.getElementById("droppable").addEventListener('copy', function (e) {

        if (typeof window.getSelection != "undefined") {
            var sel = window.getSelection();
            if (sel.rangeCount) {
                var container = document.createElement("div");
                for (var i = 0, len = sel.rangeCount; i < len; ++i) {
                    copyContent = sel.getRangeAt(i).cloneContents()
                }

            }
        } else if (typeof document.selection != "undefined") {
            if (document.selection.type == "Text") {
                copyContent = document.selection.createRange().htmlText;
            }
        }


        e.preventDefault(); // default behaviour is to copy any selected text
    });





    $("#userName").on('keyup', function () {
        let userName = $("#userName").val()
        if (!userName) {
            $("#Namelog-error").text('Enter user name')
                .addClass('field-validation-error')
            return false;
        }
        else if (userName.length < 3) {
            $("#Namelog-error").text('Between 3 to 200 characters')
                .addClass('field-validation-error')
            return false;
        }
        else if (userName.length > 200) {
            $("#Namelog-error").text('Between 3 to 200 characters')
                .addClass('field-validation-error')
            return false;
        }
        else {
            $("#Namelog-error").removeClass('field-validation-error').text("")

        }
    })
    $("#password").on('keyup', function () {

        let password = $("#password").val()
        if (!password) {
            $("#passwordlog-error").text('Enter secret key')
                .addClass('field-validation-error')
            return false;
        }
        else if (password.length < 3) {
            $("#passwordlog-error").text('Between 3 to 200 characters')
                .addClass('field-validation-error')
            return false;
        }
        else if (password.length > 200) {
            $("#passwordlog-error").text('Between 3 to 200 characters')
                .addClass('field-validation-error')
            return false;
        }
        else {
            $("#passwordlog-error").removeClass('field-validation-error').text("")

        }
    })

    $("#fieldNameInput").on('keyup', async function () {

        let fieldNameInput = $("#fieldNameInput").val()

        if (fieldNameInput == "") {
            checkAction = validateAction(fieldNameInput, "Enter field name", $("#fieldNameInputCategory-error"))

        }

    })

    $("#commendInput").on('keyup', async function () {

        let commendInput = $("#commendInput").val()

        if (commendInput == "") {
            checkAction = validateAction(commendInput, "Enter comment", $("#Commend-error"))

        }
        else {
            $("#Commend-error").text('').removeClass('field-validation-error')
        }
      
    })

    $(".actionFooterCategory,.actionCommendCategory").on("click", async function (e) {

        e.preventDefault();
        $("#actionNameCategory-error").empty()
        let checkAction = true
        let actionNameInput = $("#actionNameInput").val()
        let fieldNameInput = $("#fieldNameInput").val()
        let commendInput = $("#commendInput").val()
        let image = $(this).attr("image")
        if (image) {
            let imageSelected = image
        }
        else {
            let imageSelected = "cp-images"
        }

        if ($(this).attr("status") == "category") {
            if (actionNameInput == "") {
                $("#actionNameCategory-error").text('Enter action name')
                    .addClass('field-validation-error')
                return false;
            }
            let isName = await validateName(actionNameInput, $("#actionNameCategory-error"));
            if (!isName) {
                return false;
            }

            let propertyData = { 'id': 0, 'nodeId': Uuidv4(), 'parentId': '', 'title': actionNameInput, 'subtitle': '', 'expanded': true, 'noDragging': false, 'icon': imageSelected, 'children': [], 'properties': [] }
            var formData = {
                Name: actionNameInput,
                Properties: JSON.stringify(propertyData),
                Version: "1.0.0",
                __RequestVerificationToken: gettoken()
            };
        }
        else if ($(this).attr("status") == "EditCategory" || $(this).attr("status") == "EditSubCategory") {

            let id = $(this).attr("referenceId")
            let nodeId = $(this).attr("nodeId")
            let parentId = $(this).attr("parentId")
            let classNameData = $(this).attr("status")
            let parentName = $(this).attr("parentname")
            let NameValidate = $(this).attr("status") == "EditCategory" ? "Enter action name" : 'Enter action name'

            if (actionNameInput == "") {
                $("#actionNameCategory-error").text(NameValidate)
                    .addClass('field-validation-error')
                return false;
            }
            let isName = await validateName(actionNameInput, $("#actionNameCategory-error"));
            if (!isName) {
                return false;
            }
            var propertyData = WorkflowPropertyUpdateList(id, nodeId, parentId, actionNameInput, classNameData, imageSelected)
            if ($(this).attr("status") == "EditSubCategory") {
                var formData = {
                    Id: id,
                    Name: parentName,
                    Properties: JSON.stringify(propertyData),
                    Version: "1.0.0",
                    __RequestVerificationToken: gettoken()
                };
            }
            else {
                var formData = {
                    Id: id,
                    Name: actionNameInput,
                    Properties: JSON.stringify(propertyData),
                    Version: "1.0.0",
                    __RequestVerificationToken: gettoken()
                };
            }

        }
        else if ($(this).attr("status") == "AddSubCategory") {
            if (actionNameInput == "") {
                $("#actionNameCategory-error").text('Enter action name')
                    .addClass('field-validation-error')
                return false;
            }
            let isName = await validateName(actionNameInput, $("#actionNameCategory-error"));
            if (!isName) {
                return false;
            }

            let id = $(this).attr("referenceId")
            let nodeId = $(this).attr("nodeId")
            let parentId = $(this).attr("parentId")
            let classNameData = $(this).attr("status")
            let parentName = $(this).attr("parentname")
            let propertyData = WorkflowPropertyAddList(id, nodeId, parentId, actionNameInput, classNameData, imageSelected)

            var formData = {
                Id: id,
                Name: parentName,
                Properties: JSON.stringify(propertyData),
                Version: "1.0.0",
                __RequestVerificationToken: gettoken()
            };
        }
        else if ($(this).attr("status") == "addChildSubComment" || $(this).attr("status") == "EditchidSubComment") {


            if (fieldNameInput == "") {
                checkAction = validateAction(fieldNameInput, "Enter field name", $("#fieldNameInputCategory-error"))

            }
            if (commendInput == "") {
                checkAction = validateAction(commendInput, "Enter comment", $("#Commend-error"))

            }


            let id = $(this).attr("referenceId")
            let nodeId = $(this).attr("nodeId")
            let parentId = $(this).attr("parentId")
            let classNameData = $(this).attr("status")
            let parentName = $(this).attr("parentname")
            if ($(this).attr("status") == "EditchidSubComment") {
                var propertyData = WorkflowPropertyUpdateList(id, nodeId, parentId, fieldNameInput, classNameData, imageSelected)
            }
            else {
                var propertyData = WorkflowPropertyAddList(id, nodeId, parentId, fieldNameInput, classNameData, imageSelected)
            }

            var formData = {
                Id: id,
                Name: parentName,
                Properties: JSON.stringify(propertyData),
                Version: "1.0.0",
                __RequestVerificationToken: gettoken()
            };
        }
        
        if (checkAction) {
            $.ajax({
                type: "POST",
                url: RootUrl + createActionUrl,
                data: formData,
                dataType: "json",
                traditional: true,
                success: function (data) {

                    $('#alertClass').removeClass();
                    $('#alertClass').addClass("success-toast")
                    $('#message').text(data.message)
                    $('#mytoastrdata').toast({ delay: 3000 });
                    $('#mytoastrdata').toast('show');
                    ExecuteTreeDataView()
                    $("#Name-error").text('').removeClass('field-validation-error')
                },
                error: function () {


                }

            })
        }


    })
    function validateAction(val, text, err) {
        if (val == "") {
            err.text(text)
                .addClass('field-validation-error')

        }

    }

    $(".saveLock").on("click",async function () {


        let userName = $("#userName").val()
        let password = $("#password").val()
        let referenceId = $(this).attr("referenceId")
        let nodeId = $(this).attr("nodeId")
        let lockStatusdata = $(this).attr("lockStatus") == "false" ? true : false
        if (!userName && !password) {
            $("#passwordlog-error").text('Enter secret key')
                .addClass('field-validation-error')
            return false;
        }

        else if (userName && !password) {

            $("#passwordlog-error").text('Enter secret key')
                .addClass('field-validation-error')
            return false;

        }
        else if (password.length < 3) {
            $("#passwordlog-error").text('Between 3 to 200 characters')
                .addClass('field-validation-error')

            return false;
        }
        else if (password.length > 200) {
            $("#passwordlog-error").text('Between 3 to 200 characters')
                .addClass('field-validation-error')

            return false;
        }
        else {
            $("#passwordlog-error").removeClass('field-validation-error').text("")
        }

        var data = {}
        let stringPassword= password.split("$")
        if (stringPassword.length >= 2) {
          await  $.ajax({
                type: "GET",
                url: RootUrl + lockDecryptUrl,
              data: { password: stringPassword[0] },
                dataType: "json",
                traditional: true,
                success: function (decryptdata) {
                    
                    data.password = decryptdata
                }
            })
           
        }
        else {
            data.password = password
        }
       

        await  $.ajax({
            type: "GET",
            url: RootUrl + lockEncryptUrl,
            data: data,
            dataType: "json",
            traditional: true,
            success: function (data) {

                let securityKey = "SecurityKey"

                let lockData = {
                    userName: $("#userName").attr("value"),
                    password: data + "$" + Math.random().toString(36).substring(2, 7),
                    settingKey: securityKey,
                    __RequestVerificationToken: gettoken()
                };
                $.ajax({
                    type: "POST",
                    url: RootUrl + lockactionurl,
                    data: lockData,
                    dataType: "json",
                    traditional: true,
                    success: function (data) {

                        if (data.message) {


                            if (data.success) {
                                let lockData = {
                                    id: referenceId,
                                    isLock: lockStatusdata,
                                    __RequestVerificationToken: gettoken()

                                };
                                $.ajax({
                                    type: "POST",
                                    url: RootUrl + lockStatusUpdateUrl,
                                    data: lockData,
                                    dataType: "json",
                                    traditional: true,
                                    success: function (data) {

                                        $('#alertClass').removeClass("info-toast")
                                        $('#alertClass').addClass("success-toast")
                                        $('#message').text(data.message)
                                        $('#mytoastrdata').toast({ delay: 3000 });
                                        $('#mytoastrdata').toast('show');
                                        $(".iconClass").removeClass("cp-exclamation")
                                        $(".iconClass").addClass("cp-check")
                                        getActionListView(nodeId)
                                        $('#LockModal').modal('hide');
                                    }
                                })
                               

                            }
                            else {
                                $('#alertClass').removeClass("success-toast")
                                $('#alertClass').addClass("info-toast")
                                $('#message').text(data)
                                $('#LockModal').modal('hide');
                                $(".iconClass").removeClass("cp-check")
                                $(".iconClass").addClass("cp-exclamation")
                                $('#mytoastrdata').toast({ delay: 3000 });
                                $('#mytoastrdata').toast('show');

                            }

                        }
                        else {
                           
                            $("#passwordlog-error").text('Invalid Credential')
                                .addClass('field-validation-error')
                        }
                    }
                })
                $("#SaveAs").modal("hide")
            }
        })
    })


async function validateName(value, name, text) {
    const errorElement = name;
    let url = RootUrl + "Admin/WorkflowAction/WorkflowActionNameExist";
    var getID = $(".finish_btn").attr("id") ? $(".finish_btn").attr("id") : null;
    let data2 = { actionName: value, actionID: getID };

    if (!value) {
        errorElement.text(text)
            .addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxCompanylength(value),
        await secondChar(value),
        await IsFormNameExist(url, data2, OnError)
    ];

    return await CommonValidation(errorElement, validationResults);


}

async function IsFormNameExist(url, data, errorFunc) {
    return !data.actionName.trim() ? true : (await GetFormAsync(url, data, errorFunc))? "Name already exist!" : true;
}

async function GetFormAsync(url, data, errorFunc) {
    
    return await $.get(url, data).fail(errorFunc);
}

$('#actionNameInput').on("input", function () {
    let actionName = $('#actionNameInput').val();
    let sanitizedValue = actionName.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    validateActionName($('#actionNameInput').val());
});

async function validateActionName(value, id = null) {
    const errorElement = $('#actionNameCategory-error');
    if (!value) {
        errorElement.text('Enter action name')
            .addClass('field-validation-error');
        return false;
    }
    let url = RootUrl + workflowActionNameExists;
    data = {};
    data.actionName = value;
    data.actionID = id;
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        //await IsActionNameAlreadyExist(url, data, OnError)
    ];
    return await CommonValidation(errorElement, validationResults);
}



function addCategoryScript(data) {
    $(".actionFooterCategory").text("Save")
    $("#actionHeaderCategory").empty()
    $("#actionHeaderCategory").text("Add Action")
    $("#actionNameInput").val("").removeClass('field-validation-error')
    $("#CreateActionModal").modal("show")
    $(".actionFooterCategory").attr("status", data)
    $("#actionNameCategory-error").text("").removeClass('field-validation-error')
}



function saveAsActionListView(data) {

    $("#saveAsActionName").val("")
    $("#actionName-error").text("").removeClass('field-validation-error')
    $("#restore").attr("name", data.type)

}

function lockModalActionListView(data) {

    $("#userName").val("")
    $("#password").val("")
    $("#passwordlog-error").text("").removeClass("field-validation-error")
    $(".saveLock").attr("referenceId", data.id)
    $(".saveLock").attr("nodeId", data.getAttribute("nodeId"))
    $(".saveLock").attr("lockStatus", data.getAttribute("lockStatus"))
    data.getAttribute("lockStatus") == "true" ? $("#tittleName").text("Lock Action") : $("#tittleName").text("Unlock Action")

}

async function actionInputvalidation(data) {

    var buttonText = $(".finish_btn").text()
    var flagNameList = alreadyExistActionList(data.value)
    var formBuilderInput = $("#formBuilderInput")
   
    if (data.value == "") {
        $("#formbuildname-error").text('Enter the name')
            .addClass('field-validation-error')

    }
   

    else if (flagNameList) {
        if (buttonText == "Save") {
            $("#formbuildname-error").text('Name already exist!')
                .addClass('field-validation-error')
        }
    }
    formBuilderInput.val(formBuilderInput.val().replace(/(\s{2,})|[^a-zA-Z0-9_']/g, ' ').replace(/^\s*/, ''));
    var isName = await validateName(data.value, $('#formbuildname-error'), 'Enter action name');


}

function deletePropertice(data) {


    var boardId = data.dataset.boardid

    $("#" + boardId).remove()
    $('.delete-action').on('click', function () {
        // Remove the parent div when the delete button is clicked
        $(this).parent('div').remove();
    });
    $(".propertiWindow").hide();

    updateWorkflowIndices();

}
function updateWorkflowIndices() {
    // Iterate through the sorted elements
    $('#parentAction > div').each(function (newIndex) {
        var $this = $(this);
        var newIndexValue = newIndex + 1;

        // Update the index in the element
        $this.find('.workflowIndex').text(newIndexValue);
        // Update the 'index' attribute
        $this.find('#childspan').attr('index', newIndexValue);
    });
}


function alreadyExistActionList(name) {

    var flagName = false
    $(".actionType").each(function () {

        var listName = $(this).find(".d-grid").text()
        if (listName.toLowerCase() == name.toLowerCase()) {
            flagName = true
        }

    })
    return flagName
}

function scriptActionList() {

    const transformedScript = [];
    $("[id^=actionDescriptions] span#childspan").each(function () {

        let transformedLine = $(this).text().replace(/"/g, '"');
        transformedLine = transformedLine.replace(/&nbsp;/g, ' ');
        transformedScript.push(transformedLine);


    })
    var scriptName = transformedScript.join('\n')
    return scriptName
}
function scriptActionListJson() {

    var transformedScriptJson = []
    $("[id^=actionDescriptions] span#childspan").each(function () {

        if (ExecuteTreeDataScriptJson?.length != 0) {
            ExecuteTreeDataScriptJson.forEach((json) => {
                let properties = JSON.parse(json.properties)
                if (properties.children.length != 0) {
                    properties.children.forEach((jsonChild) => {
                        if (jsonChild.children.length != 0) {
                            jsonChild.children.forEach((jsonDataSubChild) => {

                                if (jsonDataSubChild.field_name == $(this).attr("actionid")) {

                                    var x = {}
                                    let transformedLine = $(this).text().replace(/"/g, '"');
                                    transformedLine = transformedLine.replace(/&nbsp;/g, ' ');
                                    x.ActionId = ""
                                    x.field_type = jsonDataSubChild.field_type
                                    x.ActionType = jsonDataSubChild.ActionType
                                    x.EventType = jsonDataSubChild.EventType
                                    x.field_name = jsonDataSubChild.field_name
                                    x.Description = jsonDataSubChild.Description
                                    x.Value = jsonDataSubChild.Value
                                    x.Active = jsonDataSubChild.Active
                                    x.OnError = {}

                                    x.ApplyRule = jsonDataSubChild.ApplyRule
                                    x.IsComment = jsonDataSubChild.IsComment
                                    x.Log = jsonDataSubChild.Log
                                    x.FinalDescription = transformedLine
                                    x.linenumber = $(this).attr("index")
                                    x.globalId = $(this).attr("globalid")
                                    x.title = $(this).html()
                                    var buttonText = $(".finish_btn").text()
                                    if ($(this).attr("data-propertics") != undefined) {
                                        x.Properties = JSON.parse($(this).attr("data-propertics"))
                                        //x.Properties =$(this).attr("data-propertics")
                                    }
                                    else {
                                        x.Properties = []
                                    }
                                    x.comment = $("#description").val()
                                    x.comment = $("#description").val()
                                    transformedScriptJson.push(x)
                                }

                            })
                        }

                    })
                }
            })

        }

    })
    return transformedScriptJson
}

async function editActionListView(data, e) {   

    $(".copyContent").addClass("d-none")
    $("#ActionbuilderconfigModal").modal("show");
    $('#parentAction').empty();
    $("#formbuildname-error").removeClass("field-validation-error")
    var splitData = data.type.split("$")
    $("#formBuilderInput").empty();
    $("#formbuildname-error").empty();
    $(".finish_btn").html("Update")
    $("#formBuilderInput").val(splitData[1])
    $("#formBuilderType option").removeAttr('selected')
    if (splitData[0] == 'all') {
        $("#formBuilderType option[value='Common']").attr('selected', 'selected');
    }
    else {
        $("#formBuilderType option[value=" + splitData[0] + "]").attr('selected', 'selected');
    }
    $(".finish_btn").attr("id", data.id)
    $(".finish_btn").attr("nodeId", splitData[2])
    data.getAttribute("lockStatus") == "false" ? $(".finish_btn").addClass("disabled") : $(".finish_btn").removeClass("disabled")

    var viewJsonData = jsonResultActionView(data.id)
    var jsonData = JSON.parse(viewJsonData[0].properties)

    Object.keys(jsonData?.formInput?.fields).forEach(function (fieldId) {
        let field = jsonData?.formInput?.fields[fieldId];
        setTimeout(() => {
            let selectTags = document.querySelectorAll('select.form-select-modal-dynamic');
            selectTags.forEach(selectTag => {
                let replacedID = selectTag?.getAttribute('id')?.replace('prev-', '');
                if (field?.id === replacedID) {
                    setTimeout(() => {
                        $(`#${field?.id}-attrs-ServerRole-ServerRole`).val(field.attrs.ServerRole);
                        let serverRoleId = $(`#${field?.id}-attrs-ServerRole-ServerRole option:selected`).attr('data-ServerRoleID');
                        if (serverRoleId) {
                            serverType($(`#${field?.id}-attrs-ServerType-ServerType`), serverRoleId);
                        }
                        setTimeout(() => {
                            $(`#${field?.id}-attrs-ServerType-ServerType`).val(field.attrs.ServerType);
                        }, 500);
                    }, 1000);
                }
            });

            let databaseSelectTags = document.querySelectorAll('select.form-select-modal-dynamic');
            databaseSelectTags.forEach(selectTag => {
                let replacedID = selectTag?.getAttribute('id')?.replace('prev-', '');
                if (field?.id === replacedID) {
                    setTimeout(() => {
                        $(`#${field?.id}-attrs-DatabaseType-DatabaseType`).val(field.attrs.DatabaseTypeID);
                    }, 1000);
                }
            });

            let InfraSelectTags = document.querySelectorAll('select.form-select-modal-dynamic');
            InfraSelectTags.forEach(selectTag => {
                let replacedID = selectTag?.getAttribute('id')?.replace('prev-', '');
                if (field?.id === replacedID) {
                    setTimeout(() => {
                        $(`#${field?.id}-attrs-InfraObject-InfraObject`).val(field.attrs.InfraObjectID);
                    }, 1000);
                }
            });
        }, 1500);
    });

    var typeOf = typeof (jsonData.formInput)
    var data = []
    if (jsonData.formInput.length != 0) {
        if (typeOf == "object") {
            data = jsonData.formInput
        }
        else {
            data = JSON.parse(jsonData.formInput)
        }
    }

    var executeboardData = jsonData.executeActions;
    $("#description").val(jsonData?.description)
    $("#timeWait").val(jsonData?.timeWait)
    $("#msBetweenTries").val(jsonData?.msBetweenTries)

    if (executeboardData.length != 0) {
        executeboardData.forEach((data) => {
            var matchDescription = data.Description
            var placeholders = matchDescription.match(/@@\w+/g);
            if (placeholders) {
                placeholders.forEach(function (placeholder) {
                    var span = document.createElement('span');
                    span.textContent = placeholder;
                    span.setAttribute("contenteditable", "true");
                    span.id = "placeholder-span";
                    span.style.color = "yellow";
                    // Replace the placeholder in the descriptioned string with the <span> tag
                    matchDescription = matchDescription.replace(placeholder, span.outerHTML);
                });
            }
            if (typeof (data.Properties) == "object") {
                var propertyData = JSON.stringify(data.Properties)
            }
            else {
                var propertyData = data.Properties
            }

            let index = $('#parentAction > div').length + 1;
            //let propertyData = JSON.stringify(data.Properties)
            let appendedElementEdit = $("<div class='w-100 text-info text-break d-flex' data-propertics='" + propertyData + "' data-status='edit'  data-field_name='" + data.field_name + "' data-description='" + data.Description + "'  data-title='" + matchDescription + "' onclick='actionDescriptions(this,\"" + data.field_name + "\", \"" + data.globalId + "\")' id='" + data.globalId + "' style='color:white' >" +
                "<span  class='text-secondary mx-1 workflowIndex'>" + index + " </span >" + "<span class='w-100 text-info text-break' contentEditable='true' data-status='edit'  data-propertics='" + propertyData + "' data-title='" + matchDescription + "' data-field_name='" + data.field_name + "' index='" + data.linenumber + "' actionId='" + data.field_name + "' globalId='" + data.globalId + "' id='childspan' style='cursor: pointer;'>" + data.title + "</span>" + "</div>");
            $('#parentAction').append(appendedElementEdit);
            $("#" + data.globalId).attr("data-content", data.title);
            $("#" + data.globalId).children("#childspan").attr("data-content", data.title);
        })
    }
    $('#parentAction').sortable({
        cancel: '#placeholder-span',
    })


    $("#sortable").disableSelection();
    $('#parentAction').sortable({
        update: function (event, ui) {
            // Iterate through the sorted elements
            $('#parentAction > div').each(function (newIndex) {
                var $this = $(this);
                var newIndexValue = newIndex + 1;
                // Update the index in the element
                $this.find('.workflowIndex').html(newIndexValue);
            });
        },
    });
    // $('#parentAction').sortable();

    $("#steps-uid-0-t-0").trigger("click");
    $("[id ^= actionDescriptions]").removeClass("ui-sortable-handle");
    ExecuteTreeDataView()

    let keyValuePair = Object.values(data.fields);
    if (keyValuePair.length > 0) {
        keyValuePair.forEach(function (data, index) {
            if (data.meta.id === "text-input") {
                if (!data.attrs.hasOwnProperty("readonly")) {
                    data.attrs["textInputValue"] = "";
                    data.attrs["readonly"] = false;
                    data.attrs["restrict"] = false;
                    delete data.attrs["customvalidation"];
                }
            }
            if (data.meta.id === "checkbox") {
                if (!data.attrs.hasOwnProperty("readonly")) {
                    data.attrs["readonly"] = false;
                }
            }
            if (data.meta.id === "database") {
                data.attrs["name"] = data.attrs.name ? data.attrs.name : "";
                data.attrs["placeholder"] = data.attrs.placeholder ? data.attrs.placeholder : "Select Option";
                data.attrs["DatabaseType"] = data.attrs.DatabaseType ? data.attrs.DatabaseType : 'All';
                data.attrs["DatabaseTypeID"] = data.attrs.DatabaseTypeID ? data.attrs.DatabaseTypeID : '';
            }
            if (data.meta.id === "ip-address") {
                delete data.config["disabledAttrs"];
                delete data.config["lockedAttrs"];
                data.config["disabledAttrs"] = ["type", "attrid"];
                data.config["lockedAttrs"] = ['name', 'placeholder', 'required'];
                data.attrs["attrid"] = "ipAddressField";
            }
            if (data.meta.id === "server") {
                data.attrs["name"] = data.attrs.name ? data.attrs.name : "";
                data.attrs["placeholder"] = data.attrs.placeholder ? data.attrs.placeholder : "Select Server";
                data.attrs["ServerRole"] = data.attrs.ServerRole ? data.attrs.ServerRole : "";
                data.attrs["ServerType"] = data.attrs.ServerType ? data.attrs.ServerType : "";
                data.attrs["ServerRoleID"] = data.attrs.ServerRoleID ? data.attrs.ServerRoleID : "";
                data.attrs["ServerTypeID"] = data.attrs.ServerTypeID ? data.attrs.ServerTypeID : "";
            }
            if (data.meta.id === "infraobject") {
                data.attrs["name"] = data.attrs.name ? data.attrs.name : "";
                data.attrs["placeholder"] = data.attrs.placeholder ? data.attrs.placeholder : "Select InfraObject";
                data.attrs["InfraObject"] = data.attrs.InfraObject ? data.attrs.InfraObject : "";
                data.attrs["InfraObjectID"] = data.attrs.InfraObjectID ? data.attrs.InfraObjectID : "";
            }
        });
    }

    setTimeout(() => {
        new FormeoEditor({
            editorContainer: '#actionForms',
            controls: controlOptions,
            events: {
                onUpdate: (formData) => handleUpdate(formData),
            },
            formData: data,
        })
    }, 1250)
}


async function actionDescriptions(data, actionId, globalId) {
    
    
    var buttonText = $(".finish_btn").text()
    $("#btnDescriptionChange").attr("boardactionId", actionId)

    $(".propertiWindow").show();
    if (buttonText == "Save") {
        $("#btnDescriptionChange").attr("boardId", "actionDescriptions" + globalId)
        $(".Skipped_Paused").attr("data-boardId", "actionDescriptions" + globalId)
        handleDropAndSwitch(actionId, "actionDescriptions" + globalId, data.dataset.title);
    }
    else {

        var globadata = globalId.substr(0, 18);
        if (globadata == "actionDescriptions") {
            var globalIdsplit = globalId
        }
        else {
            var globalIdsplit = "actionDescriptions" + globalId
        }
        handleDropAndSwitch(actionId, globalIdsplit, data.dataset.title);
        $("#btnDescriptionChange").attr("boardId", globalIdsplit)
        $(".Skipped_Paused").attr("data-boardId", globalIdsplit)
    }
    if (data.dataset.propertics) {
        var dataPropertics = data.dataset.propertics.replaceAll("..", "'")
        var parsedata = JSON.parse(dataPropertics)
       
        for (var key in parsedata) {
            let encryptSplit;
            if (parsedata[key]) {

                encryptSplit=parsedata[key].split("=$")
            }
            let valueIndex;
            let flagStatus=false
            if (parsedata[key] && encryptSplit.length>1) {
                valueIndex = await DecryptPassword(parsedata[key])
                flagStatus=true
            }
            else {
                valueIndex = parsedata[key]
                flagStatus = false
            }

            $("input#" + key).empty();
            $("textarea#" + key).empty();
            $("input#" + key).val(valueIndex)
            $("textarea#" + key).val(valueIndex)

            if (flagStatus) {
                $("input#" + key).parent().prev().prop("checked", flagStatus)
                $("textarea#" + key).parent().prev().prop("checked", flagStatus)
            }

            if (key == "tableCollectionData" || key == "sendTableCollectionData") {
                if (parsedata[key] == "@@tableCollectionData") {
                    renderTable()

                }
                else if (parsedata[key] == "@@sendTableCollectionData") {
                    renderSendTable()
                }
                else {
                    let jsonTable = JSON.parse(parsedata[key])
                    tableData = []
                    jsonTable.forEach((data) => {
                        let dataTable = renderTable()
                        $("#" + dataTable.type).val(data.type)
                        $("#" + dataTable.value).val(data.value)
                        let dataSendTable = renderSendTable()
                        $("#" + dataSendTable.type).val(data.ParameterType)
                        $("#" + dataSendTable.name).val(data.ParameterName)
                        $("#" + dataSendTable.value).val(data.ParameterValue)

                    })
                }
            }

            if (key == "isLog") {

                $("input#" + key).prop("checked", parsedata[key])
            }

        }
    }
    else {

        var placeholders = data.dataset.title.match(/@@\w+/g);
        if (placeholders) {
            placeholders.forEach(function (data) {
                $(".propertiesInput").each(async function () {

                    if ($(this).attr("scriptvalue") == data) {

                        let encryptSplit = data.split("=$")
                        let valuedata;
                        if (encryptSplit.length > 1) {
                            valuedata = await DecryptPassword(data)
                        }
                        else {
                            valuedata = data
                        }

                        var splitData = valuedata.split("@@")

                        if ($(this).attr("type") != "number") {
                            $("#" + splitData[1]).empty();
                            $("#" + splitData[1]).val(valuedata)
                        }
                        else {
                            $("input#" + splitData[1]).empty();
                            $("textarea#" + splitData[1]).empty();
                            $("input#" + splitData[1]).val(0)
                            $("textarea#" + splitData[1]).val(0)

                        }
                    }
                })
            })
        }
    }
}


function ExecuteTreeDataView() {
    var listActionUrl = "Admin/WorkflowAction/workflowActionDataList"
    $("#Workflow-ActionTree").empty();
    //  $.getJSON('/json/ExecuteTreeData.json', function (ExecuteTreeData) {
    $.ajax({
        type: "GET",
        url: RootUrl + listActionUrl,
        dataType: "json",
        traditional: true,
        success: function (ExecuteTreeData) {

            //if (ExecuteTreeData.success) {
            propertyList = ExecuteTreeData
            var treeDataHtml = ""
            ExecuteTreeDataScriptJson = ExecuteTreeData

            if (ExecuteTreeData.length != 0) {
                ExecuteTreeData.forEach((treeData) => {
                    
                    let properties = JSON.parse(treeData.properties)

                    treeDataHtml += "<details class='ms-0'>"
                    treeDataHtml += "<summary class='categorysummary'><label class='mb-0'><i class='" + treeData.icon + "'></i> " + treeData.name + "</label>"
                    treeDataHtml += "<i class='cp-horizontal-dots float-end' data-bs-toggle='dropdown'' aria-expanded='true' role = 'button' >"
                    treeDataHtml += "</i > "
                    treeDataHtml += "<div class='dropdown'>"
                    treeDataHtml += "<ul class='dropdown-menu'>"
                    treeDataHtml += "<li id='" + treeData.id + "' parentName='" + treeData.name + "' nodeId='" + properties.nodeId + "' parentId='" + properties.nodeId + "' class='AddActionChidSubCategory' onclick='AddActionChidSubCategory(this)' status='AddSubCategory' name='@workflow.Name' title='Add SubCategory' type='Add'><a class='dropdown-item' href='#'><i class='cp-add me-2'></i>Add SubCategory</a></li>"
                    treeDataHtml += "<li id='" + treeData.id + "' parentName='" + treeData.name + "' nodeId='" + properties.nodeId + "' parentId='" + properties.nodeId + "' icon='@propertiesData.icon' onclick='AddActionChidSubCategory(this)' status='EditCategory' parentName='@workflow.Name' name='@propertiesData.title' class='EditCategory' title='Edit Action' type='Update'><a class='dropdown-item' href='#'><i class='cp-edit me-2'></i>Edit Action</a></li>"
                    treeDataHtml += "<li class='DeleteCategory' id='" + treeData.id + "' parentName=" + treeData.name + " nodeId='" + properties.nodeId + "' name='" + treeData.name + "' parentId='" + properties.nodeId + "' onclick='DeleteActionChidCategory(this)' status='DeleteCategory' data-WorkflowCategory-id='@propertiesData.id' parentName='@workflow.Name' data-WorkflowCategory-name='@propertiesData.title'><a class='dropdown-item' href='#'><i class='cp-Delete me-2'></i>Delete Action</a></li>"
                    treeDataHtml += "</ul>"
                    treeDataHtml += "</div>"
                    treeDataHtml += "</summary > "
                    if (properties.children.length != 0) {
                        properties.children.forEach((treeDataChild) => {

                            treeDataHtml += "<details><summary class='categorysummary'><label class='mb-0'><i class='" + treeDataChild.icon + "'></i> " + treeDataChild.title + "</label>"
                            treeDataHtml += "<i class='cp-horizontal-dots float-end' data-bs-toggle='dropdown'' aria-expanded='true' role = 'button' >"
                            treeDataHtml += "</i > "
                            treeDataHtml += "<div class='dropdown'>"
                            treeDataHtml += "<ul class='dropdown-menu'>"
                            treeDataHtml += "<li id='" + treeData.id + "'  parentName='" + treeData.name + "' nodeId='" + treeDataChild.nodeId + "' parentId='" + treeDataChild.parentId + "' class='AddActionChidSubCategory' onclick='AddActionChidSubCategory(this)' status='addChildSubComment' name='@workflow.Name' title='Add Comment' type='Add'><a class='dropdown-item' href='#'><i class='cp-add me-2'></i>Add Comment</a></li>"
                            treeDataHtml += "<li id='" + treeData.id + "' name ='" + treeDataChild.title + "' parentName='" + treeData.name + "' nodeId='" + treeDataChild.nodeId + "' parentId='" + treeDataChild.parentId + "' icon='@propertiesData.icon' parentName='@workflow.Name' onclick='AddActionChidSubCategory(this)' name='@propertiesData.title' status='EditSubCategory' class='EditSubCategory' title='Edit SubCategory' type='Update'><a class='dropdown-item' href='#'><i class='cp-edit me-2'></i>Edit SubCategory</a></li>"
                            treeDataHtml += "<li class='DeleteSubCategory' id='" + treeData.id + "' name ='" + treeDataChild.title + "' parentName=" + treeData.name + " nodeId='" + treeDataChild.nodeId + "' parentId='" + treeDataChild.parentId + "' status='DeleteSubCategory' onclick='DeleteActionChidCategory(this)' data-WorkflowCategory-id='@propertiesData.id' parentName='@workflow.Name' data-WorkflowCategory-name='@propertiesData.title'><a class='dropdown-item' href='#'><i class='cp-Delete me-2'></i>Delete Sub Category</a></li>"
                            treeDataHtml += "</ul>"
                            treeDataHtml += "</div>"
                            treeDataHtml += "</summary > "
                            if (treeDataChild.children.length != 0) {
                                treeDataChild.children.forEach((treeDataSubChild) => {
                                    //treeDataHtml += "<div id='draggable' class='d-flex align-items-center justify-content-between  categorysummary'><span class='p-0 text-truncate' role='button' draggable='true' ondragstart='drag(event)' data-id='" + treeDataSubChild.id + "' data-nodeId='" + treeDataSubChild.nodeId + "' data-parentId='" + treeDataSubChild.parentId + "' data-name ='" + treeDataSubChild.title + "' data-parentName='" + treeData.name + "'  data-description='" + treeDataSubChild.Description + "' data-description1='" + treeDataSubChild.title + "'> " + treeDataSubChild.title + " </span>"

                                    treeDataHtml += "<div class='d-flex align-items-center justify-content-between  categorysummary'><span class='p-0 text-truncate draggable-item' role='button' draggable='true' ondragstart='drag(event)' data-id='" + treeDataSubChild.id + "' data-nodeId='" + treeDataSubChild.nodeId + "' data-parentId='" + treeDataSubChild.parentId + "' data-name ='" + treeDataSubChild.title + "' data-parentName='" + treeData.name + "'  data-description='" + treeDataSubChild.Description + "' data-description1='" + treeDataSubChild.title + "'> " + treeDataSubChild.title + " </span>"
                                    treeDataHtml += "<div class='dropdown'>"
                                    treeDataHtml += "<i class='cp-horizontal-dots float-end' data-bs-toggle='dropdown' aria-expanded='true' role = 'button' ></i >"
                                    treeDataHtml += "<ul class='dropdown-menu'>"
                                    treeDataHtml += "<li id='" + treeData.id + "' name ='" + treeDataSubChild.title + "' parentName='" + treeData.name + "' nodeId='" + treeDataSubChild.nodeId + "' parentId='" + treeDataSubChild.parentId + "' description='" + treeDataSubChild.Description + "' icon='@childPropertiesData.icon' name='@childPropertiesData.title' onclick='AddActionChidSubCategory(this)' class='EditchidSubComment' status='EditchidSubComment'  title='Edit Comment' type='Update'><a class='dropdown-item' href='#'><i class='cp-edit me-2'></i>Edit Comment</a></li>"
                                    treeDataHtml += "<li id='" + treeData.id + "' name ='" + treeDataSubChild.title + "' parentName='" + treeData.name + "' nodeId='" + treeDataSubChild.nodeId + "' parentId='" + treeDataSubChild.parentId + "'  class='DeleteSubChildCategory' status='DeleteSubChildCategory' onclick='DeleteActionChidCategory(this)' data-WorkflowCategory-name='@childPropertiesData.title'><a class='dropdown-item' href='#'>"
                                    treeDataHtml += "<i class='cp-Delete me-2' ></i > Delete Comment</a ></li > "
                                    treeDataHtml += "</ul>"
                                    treeDataHtml += "</div>"
                                    treeDataHtml += "</div>"

                                })
                            }
                            treeDataHtml += "</details>"
                        })
                    }
                    treeDataHtml += "</details>"
                })

            }

            $("#Workflow-ActionTree").append(treeDataHtml)
            $("#CreateActionModal").modal("hide")
            $("#CommendActionModal").modal("hide")

            //}
        }
    })
}
$(".action_catagory_toggle").css("display", "none")

$(".categoryactionlist ").on("click", function () {
    $(".action_catagory_toggle").css("display", "block")

})
$(document).ready(function () {

    $('.draggable-item').draggable({
        connectToSortable: '#parentAction', // Allows dropping into #parentAction
        helper: 'clone', // Clone the element while dragging
        revert: 'invalid' // Revert if not dropped in the list
       
    });

    $('#parentAction').droppable({
        accept: '.draggable-item'
       
    });

})
function allowDrop(e) {
    e.preventDefault();
}
function drag(e) {



    var description = e.target.getAttribute("data-description");
    var description1 = e.target.getAttribute("data-description1");


    e.dataTransfer.setData("data-description", description);
    e.dataTransfer.setData("actionId", description1);
    e.dataTransfer.setData("description", description);
    e.dataTransfer.setData("id", e.target.getAttribute("data-id"));
    e.dataTransfer.setData("nodeId", e.target.getAttribute("data-nodeId"));
    e.dataTransfer.setData("parentId", e.target.getAttribute("data-nodeId"));
    e.dataTransfer.setData("titleName", e.target.getAttribute("data-name"));
    e.dataTransfer.setData("parentName", e.target.getAttribute("data-parentName"));
    
}



function AddActionChidSubCategory(data) {
    let className = data.getAttribute("class");
    if (className === 'EditCategory') {
        $(".actionFooterCategory").text("Update");
    } else {
        $(".actionFooterCategory").text("Save");
    }

    $("#actionNameCategory-error").text("").removeClass('field-validation-error')
    let status = data.getAttribute("status")
    if (status == "AddSubCategory" || status == "EditCategory" || status == "EditSubCategory") {
        let title
        if (status == "EditCategory") {
            title = 'Edit Action'
            $("#actionNameInput").val(data.getAttribute("parentname"))
        }
        else if (status == "EditSubCategory") {
            title = 'Edit SubCategory'
            $("#actionNameInput").val(data.getAttribute("name"))
        }
        else if (status == "AddSubCategory") {
            title = 'Add SubCategory'
            $("#actionNameInput").val("")
        }

        else {
            title = 'Add Action'
            $("#actionNameInput").val("")

        }
        $("#actionHeaderCategory").empty()
        $("#actionHeaderCategory").text(title)
        $("#CreateActionModal").modal("show")
        $(".actionFooterCategory").attr("status", status)
        $(".actionFooterCategory").attr("referenceId", data.getAttribute("id"))
        $(".actionFooterCategory").attr("nodeId", data.getAttribute("nodeId"))
        $(".actionFooterCategory").attr("parentId", data.getAttribute("parentId"))
        $(".actionFooterCategory").attr("parentname", data.getAttribute("parentName"))

    }
    else {
        $("#fieldNameInputCategory-error,#Commend-error").text("").removeClass('field-validation-error')
        if (status == "EditchidSubComment") {
            title = 'Edit Comment'
            $("#fieldNameInput").val(data.getAttribute("name"))
            $("#commendInput").val(data.getAttribute("description"))
        }

        else {
            title = 'Add Comment'
            $("#fieldNameInput").val("")
            $("#commendInput").val("")
        }
        $("#CommendActionModal").modal("show")
        $("#actionCommendCategory").attr("status", status)
        $("#actionCommendCategory").attr("referenceId", data.getAttribute("id"))
        $("#actionCommendCategory").attr("nodeId", data.getAttribute("nodeId"))
        $("#actionCommendCategory").attr("parentId", data.getAttribute("parentId"))
        $("#actionCommendCategory").attr("parentname", data.getAttribute("parentName"))
    }


}

$("#actionDeleteButton").on("click", function () {
    var formData = {
        id: actionId,
        __RequestVerificationToken: gettoken()
    }
    $.ajax({
        type: "POST",
        url: RootUrl + deleteActionUrl,
        data: formData,
        dataType: "json",
        traditional: true,
        success: function (data) {
            $("#DeleteActionModal").modal("hide");
            $('#alertClass').removeClass();
            $('#alertClass').addClass("success-toast")
            $('#message').text("'" + actionName + "' has been deleted successfully")
            $('#mytoastrdata').toast({ delay: 3000 });
            $('#mytoastrdata').toast('show');
            ExecuteTreeDataView()
            $("#Name-error").text('').removeClass('field-validation-error')
        },
        error: function () {


        }

    })
});


function DeleteActionChidCategory(data) {

    actionId = data.getAttribute("id")
    var nodeId = data.getAttribute("nodeId")
    var parentId = data.getAttribute("parentId")
    var parentName = data.getAttribute("parentName")
    actionName = data.getAttribute("name")
    var className = data.getAttribute("status")
    if (className == "DeleteCategory") {
        $("#deleteData").text(actionName);
        $("#DeleteActionModal").modal("show");
    }
    else {
        var propertyData = WorkflowPropertyDeleteList(actionId, nodeId, parentId, parentName, className)
        var formData = {
            Id: actionId,
            __RequestVerificationToken: gettoken(),
            Name: parentName,
            Properties: JSON.stringify(propertyData),
            Version: "1.0.0"
        };

        $.ajax({
            type: "POST",
            url: RootUrl + createActionUrl,
            data: formData,
            dataType: "json",
            traditional: true,
            success: function (data) {
                $('#alertClass').removeClass();
                $('#alertClass').addClass("success-toast")
                $('#message').text("'" + actionName + "' has been deleted successfully")
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
                ExecuteTreeDataView()
                $("#Name-error").text('').removeClass('field-validation-error')
            },
            error: function () {

            }
        })
    }
}


function drop(e) {

    e.preventDefault()
    let s = e.dataTransfer.getData("description")
    if (s == "") {
        return false;
    }
    $(".propertiWindow").show();
    let actionId = e.dataTransfer.getData("actionId");
    let id = e.dataTransfer.getData("id");
    let nodeId = e.dataTransfer.getData("nodeId");
    let parentId = e.dataTransfer.getData("parentId");
    let titleName = e.dataTransfer.getData("titleName");
    let parentName = e.dataTransfer.getData("parentName");
    let parentElement=e.target.parentElement.getAttribute("id")
    $("#btnDescriptionChange").attr("data-id", id)
    $("#btnDescriptionChange").attr("data-nodeId", nodeId)
    $("#btnDescriptionChange").attr("data-parentId", parentId)
    $("#btnDescriptionChange").attr("data-titleName", titleName)
    $("#btnDescriptionChange").attr("data-parentName", parentName)


    boardScriptDrop(s, actionId, parentElement)
    renderTable()
    renderSendTable()
};
let copiedHTMLArray = [];
function OnBoardCopy() {
    debugger
    copiedHTMLArray=[]

    let selection = window.getSelection();
    if (selection.rangeCount > 0) {
        let range = selection.getRangeAt(0);
        let div = document.createElement("div");
        div.appendChild(range.cloneContents());

        copiedHTMLArray.push(range.cloneContents().childNodes)

    }
}
let elementPasteData = ""
let flagPaste =true
function paste(elem, e) {

    e.preventDefault();
    let parentElementPaste = e.target.parentElement.getAttribute("id")
    if (copiedHTMLArray.length != 0) { 
        flagPaste = true
        elementPasteData = ""
    copiedHTMLArray[0].forEach((html,index) => {
        if (html.dataset.title && html.dataset.field_name) { 
            let parentElementPasteData;
            if (flagPaste) {
               parentElementPasteData= parentElementPaste
            }
            else {
                parentElementPasteData = elementPasteData
            }
            flagPaste=false
            boardCopyPaste(html.dataset.title, html.dataset.field_name, html.dataset.content, html.dataset.status, html.dataset.propertics, parentElementPasteData)
        }
    })
    }
}



function boardScriptDrop(s, actionId, parentElement) {
    
    s = s.replace(/\\n/g, '');
    s = s.replace(/\\/g, '');
    var placeholders = s.match(/@@\w+/g);
    if (placeholders) {
        placeholders.forEach(function (placeholder) {
            var span = document.createElement('span');
            span.textContent = placeholder;
            var splitData = placeholder.split("@@")
            span.setAttribute('data-Name', splitData[1]);
            span.setAttribute('contenteditable', 'true');
            span.id = "placeholder-span";
            // span.setAttribute("onkeydown", inputValueChange)

            span.style.color = "yellow";
            // Replace the placeholder in the descriptioned string with the <span> tag
            s = s.replace(placeholder, span.outerHTML);


        });
    }

    let index = $('#parentAction > div').length + 1;
    let globalId = randomNumber(1, 100000)
    let appendedElement = $("<div class='w-100 text-info text-break d-flex'  data-field_name='" + actionId + "' data-content='" + s + "' data-title='" + s + "' data-status='drop' onclick='actionDescriptions(this,\"" + actionId + "\", \"" + globalId + "\")' id='actionDescriptions" + globalId + "' style='color:white;' >" +
        "<span  class='text-secondary mx-1 workflowIndex'>" + index + " </span >" + "<span class='w-100 text-info text-break' data-content='" + s + "' data-status='drop' data-field_name='" + actionId + "'  data-title='" + s + "' contentEditable='true' onclick='$(this).focus();' index='" + index + "' actionId='" + actionId + "' globalId='actionDescriptions" + globalId + "' id='childspan' onclick='$(this).focus();' style='cursor: pointer;'>" + s + "</span>" + "</div>");
    if (parentElement == null) {
        $('#parentAction').append(appendedElement)
    }
    else if (parentElement != "childspan") {
        $("#" + parentElement).after(appendedElement);
    }
    else {
       let id= $("#" + parentElement).parent().attr("id")
        $("#" + id).after(appendedElement);
    }
    //$('#parentAction').sortable("refresh");
    updateIndexes();
   
    $("[id ^= actionDescriptions]").removeClass("ui-sortable-handle");

    $("#description").empty();
    $("#description").attr("data-title", s)
    $("#description").html(s)
    $("#btnDescriptionChange").attr("boardactionId", actionId)
    $("#btnDescriptionChange").attr("boardId", "actionDescriptions" + globalId)
    $(".Skipped_Paused").attr("data-boardId", "actionDescriptions" + globalId)

    handleDropAndSwitch(actionId, "actionDescriptions" + globalId, s);
    if (placeholders) {
        var propertics = {}
        placeholders.forEach(function (data) {
            $(".propertiesInput").each(function () {


                if ($(this).attr("scriptvalue") == data) {
                    var splitData = data.split("@@")
                    var key = splitData[1]
                    propertics[key] = data

                    if ($(this).attr("type") != "number") {
                        $("input#" + splitData[1]).empty();
                        $("textarea#" + splitData[1]).empty();
                        $("input#" + splitData[1]).val(data)
                        $("textarea#" + splitData[1]).val(data)
                        var key = splitData[1]
                        propertics[key] = data

                    }
                    else {
                        $("input#" + splitData[1]).empty();
                        $("textarea#" + splitData[1]).empty();
                        $("input#" + splitData[1]).val(0)
                        $("textarea#" + splitData[1]).val(0)
                        var key = splitData[1]
                        propertics[key] = 0
                    }
                    
                    if (typeof (propertics) == "object") {
                        var properticsType = JSON.stringify(propertics)
                    }
                    else {
                        var properticsType = propertics
                    }
                    $("#actionDescriptions" + globalId + " span#childspan").parent().attr("data-propertics", properticsType)
                    $("#actionDescriptions" + globalId + " span#childspan").attr("data-propertics", properticsType)

                }
            })
        })
    }
}



function boardCopyPaste(s, actionId, content, status, propertices, parentElementPaste) {
    
    s = s.replace(/\\n/g, '');
    s = s.replace(/\\/g, '');
    var placeholders = s.match(/@@\w+/g);
    let jsonpropertics = JSON.parse(propertices)
    if (placeholders) {
        placeholders.forEach(function (placeholder) {
            var span = document.createElement('span');
            span.textContent = placeholder;
            var splitData = placeholder.split("@@")
            span.setAttribute('data-Name', splitData[1]);
            span.setAttribute('contenteditable', 'true');
            span.id = "placeholder-span";
            // span.setAttribute("onkeydown", inputValueChange)

            span.style.color = "yellow";
            // Replace the placeholder in the descriptioned string with the <span> tag
            s = s.replace(placeholder, span.outerHTML);


        });
    }
    let globalId = randomNumber(1, 100000)
    let setId;
    let index = $('#parentAction > div').length + 1;
    if (status == "drop") {
        setId = globalId
    }
    else {
        setId = 'actionDescriptions' + globalId
    }
    let appendedElement = $("<div class='w-100 text-info text-break d-flex'  data-field_name='" + actionId + "'   data-title='" + s + "' data-propertics='" + propertices + "' data-content='" + content + "'  onclick='actionDescriptions(this,\"" + actionId + "\", \"" + setId + "\")' id='actionDescriptions" + globalId + "' style='color:white;' >" +
        "<span  class='text-secondary mx-1 workflowIndex'>" + index + " </span >" + "<span class='w-100 text-info text-break' data-content='" + content + "' data-propertics='" + propertices + "' contentEditable='true' onclick='$(this).focus();' index='" + index + "' data-field_name='" + actionId + "'  data-title='" + s + "' actionId='" + actionId + "' globalId='actionDescriptions" + globalId + "' id='childspan' onclick='$(this).focus();' style='cursor: pointer;'>" + content + "</span>" + "</div>");

    if (parentElementPaste == null) {
        $('#parentAction').append(appendedElement)
    }
    else if (parentElementPaste != "childspan") {
        $("#" + parentElementPaste).after(appendedElement);
    }
    else {
        let id = $("#" + parentElementPaste).parent().attr("id")
        $("#" + id).after(appendedElement);
    }
    
    //$('#parentAction').sortable("refresh");
    updateIndexes();
    elementPasteData = 'actionDescriptions' + globalId
 
    $("[id ^= actionDescriptions]").removeClass("ui-sortable-handle");

    $("#description").empty();
    $("#description").attr("data-title", content)
    $("#description").html(content)
    $("#btnDescriptionChange").attr("boardactionId", actionId)
    $("#btnDescriptionChange").attr("boardId", "actionDescriptions" + globalId)
    $(".Skipped_Paused").attr("data-boardId", "actionDescriptions" + globalId)

    handleDropAndSwitch(actionId, "actionDescriptions" + globalId, content);
    if (jsonpropertics) {
        var propertics = {}
        for (let jsondata in jsonpropertics) { 
            $(".propertiesInput").each(function () {
                debugger

                if ($(this).attr("scriptvalue") == '@@'+jsondata) {
                   
                    
                    propertics[jsondata] = jsonpropertics[jsondata]

                    if ($(this).attr("type") != "number") {
                        $("input#" + jsondata).empty();
                        $("textarea#" + jsondata).empty();
                        $("input#" + jsondata).val(jsonpropertics[jsondata])
                        $("textarea#" + jsondata).val(jsonpropertics[jsondata])
                      
                        propertics[jsondata] = jsonpropertics[jsondata]

                    }
                    else {
                        $("input#" + jsondata).empty();
                        $("textarea#" + jsondata).empty();
                        $("input#" + jsondata).val(0)
                        $("textarea#" + jsondata).val(0)
                        
                        propertics[jsondata] = 0
                    }
                    if (typeof (propertics) == "object") {
                        var properticsType = JSON.stringify(propertics)
                    }
                    else {
                        var properticsType = propertics
                    }
                    $("#actionDescriptions" + globalId + " span#childspan").parent().attr("data-propertics", properticsType)
                    $("#actionDescriptions" + globalId + " span#childspan").attr("data-propertics", properticsType)

                }
            })
        }
    }
}


function updateIndexes() {
    $('#parentAction > div').each(function (newIndex) {
        var newIndexValue = newIndex + 1;
        $(this).find('.workflowIndex').html(newIndexValue);
    });
}

function randomNumber(min, max) {
    return Math.floor(Math.random() * (max - min) + min);
}
function ActionSearchWorkflowTree(e) {

    var filter = e.value
    $("#Workflow-ActionTree details").each(function () {

        var $i = 0;

        $(this).find(".categorysummary").each(function () {

            var splitText = $(this).text().trim()
            if (splitText.search(new RegExp(filter, "i")) >= 0) {
                $i++;
            }
        });
        if ($i > 0) {
            $(this).closest("#Workflow-ActionTree details").show();
        } else {
            $(this).closest("#Workflow-ActionTree details").hide();
        }
    });
}
