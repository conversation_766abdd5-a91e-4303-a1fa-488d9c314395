﻿using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetType;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetNamesByType;

public class LoadBalancerNameQueryHandler :IRequestHandler<LoadBalancerNameQuery, List<LoadBalancerNameVm>>
{
    private readonly ILoadBalancerRepository _loadBalancerRepository;
    private readonly IMapper _mapper;

    public LoadBalancerNameQueryHandler(ILoadBalancerRepository loadBalancerRepository, IMapper mapper)
    {
        _loadBalancerRepository = loadBalancerRepository;
        _mapper = mapper;
    }

    public async Task<List<LoadBalancerNameVm>> Handle(LoadBalancerNameQuery request, CancellationToken cancellationToken)
    {
        var loadBalancer =  request.Type != null
            ? await _loadBalancerRepository.GetNamesByType(request.Type)
            : await _loadBalancerRepository.GetLoadBalancerType();

        return loadBalancer.Count <= 0
           ? new List<LoadBalancerNameVm>()
           : _mapper.Map<List<LoadBalancerNameVm>>(loadBalancer);
    }
}
