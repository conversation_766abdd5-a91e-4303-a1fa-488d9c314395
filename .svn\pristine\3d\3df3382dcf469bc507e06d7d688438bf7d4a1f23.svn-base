﻿using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;

namespace ContinuityPatrol.Application.UnitTests.Features.LicenseInfo.Queries;

public class GetLicenseInfoListQueryHandlerTests : IClassFixture<LicenseInfoFixture>
{
    private readonly LicenseInfoFixture _licenseInfoFixture;

    private Mock<ILicenseInfoRepository> _mockLicenseInfoRepository;

    private readonly GetLicenseInfoListQueryHandler _handler;

    public GetLicenseInfoListQueryHandlerTests(LicenseInfoFixture licenseInfoFixture)
    {
        _licenseInfoFixture = licenseInfoFixture;

        _mockLicenseInfoRepository = new Mock<ILicenseInfoRepository>();

        _mockLicenseInfoRepository = LicenseInfoRepositoryMocks.GetLicenseInfoRepository(_licenseInfoFixture.LicenseInfos);

        _handler = new GetLicenseInfoListQueryHandler(_mockLicenseInfoRepository.Object, _licenseInfoFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_Valid_LicenseInfosList()
    {
        var result = await _handler.Handle(new GetLicenseInfoListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<LicenseInfoListVm>>();

        result[0].Id.ShouldBe(_licenseInfoFixture.LicenseInfos[0].ReferenceId);
        result[0].LicenseId.ShouldBe(_licenseInfoFixture.LicenseInfos[0].LicenseId);
        result[0].BusinessServiceId.ShouldBe(_licenseInfoFixture.LicenseInfos[0].BusinessServiceId);
        result[0].EntityId.ShouldBe(_licenseInfoFixture.LicenseInfos[0].EntityId);
        result[0].Type.ShouldBe(_licenseInfoFixture.LicenseInfos[0].Type);
        result[0].EntityName.ShouldBe(_licenseInfoFixture.LicenseInfos[0].EntityName);
        result[0].EntityField.ShouldBe(_licenseInfoFixture.LicenseInfos[0].EntityField);
        result[0].Entity.ShouldBe(_licenseInfoFixture.LicenseInfos[0].Entity);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockLicenseInfoRepository = LicenseInfoRepositoryMocks.GetLicenseInfoEmptyRepository();

        var handler = new GetLicenseInfoListQueryHandler(_mockLicenseInfoRepository.Object, _licenseInfoFixture.Mapper);

        var result = await handler.Handle(new GetLicenseInfoListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetLicenseInfoListQuery(), CancellationToken.None);

        _mockLicenseInfoRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

}