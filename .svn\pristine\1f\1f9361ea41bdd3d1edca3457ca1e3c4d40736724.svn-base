﻿using ContinuityPatrol.Domain.ViewModels.SVCGMMonitorLogsModel;

namespace ContinuityPatrol.Application.Features.SVCGMMonitoringLogs.Queries.GetList;

public class
    GetSVCGMMonitorLogListQueryHandler : IRequestHandler<GetSVCGMMonitorLogListQuery, List<SVCGMMonitorLogsListVm>>
{
    private readonly IMapper _mapper;
    private readonly ISVCGMMonitorLogRepository _svcGMMonitorLogRepository;

    public GetSVCGMMonitorLogListQueryHandler(ISVCGMMonitorLogRepository svcGMMonitorLogRepository, IMapper mapper)
    {
        _svcGMMonitorLogRepository = svcGMMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<List<SVCGMMonitorLogsListVm>> Handle(GetSVCGMMonitorLogListQuery request,
        CancellationToken cancellationToken)
    {
        var svcGMMonitorLogList = await _svcGMMonitorLogRepository.ListAllAsync();

        return svcGMMonitorLogList.Count <= 0
            ? new List<SVCGMMonitorLogsListVm>()
            : _mapper.Map<List<SVCGMMonitorLogsListVm>>(svcGMMonitorLogList);
    }
}