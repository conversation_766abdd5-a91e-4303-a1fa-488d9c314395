﻿using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessFunction.Validators;

public class CreateBusinessFunctionValidatorTests
{
    private readonly Mock<IBusinessFunctionRepository> _mockBusinessFunctionRepository;

    public CreateBusinessFunctionValidatorTests()
    {
        var businessFunction = new Fixture().Create<List<Domain.Entities.BusinessFunction>>();

        _mockBusinessFunctionRepository = BusinessFunctionRepositoryMocks.CreateBusinessFunctionRepository(businessFunction);
    }

    //Name

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Name_InBusinessFunction_WithEmpty(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "";

        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";

        createBusinessFunctionCommand.ConfiguredRPO = "50";

        createBusinessFunctionCommand.RPOThreshold = "10";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Name_InBusinessFunction_IsNull(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = null;
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "60";
        createBusinessFunctionCommand.RPOThreshold = "20";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNotNullRequired, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Name_InBusinessFunction_MiniMumRange(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "AR";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "70";
        createBusinessFunctionCommand.RPOThreshold = "30";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameRangeRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Name_InBusinessFunction_MaxiMumRange(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "80";
        createBusinessFunctionCommand.RPOThreshold = "40";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionDescriptionContains, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Valid_Name_InBusinessFunction_With_FrontSpace(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = " PTS";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "90";
        createBusinessFunctionCommand.RPOThreshold = "50";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Name_InBusinessFunction_Valid(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "  PTS  ";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "30";
        createBusinessFunctionCommand.RPOThreshold = "10";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Valid_Name_InBusinessFunction_With_DoubleSpace_InBetween(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "PTS  Technology";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "50";
        createBusinessFunctionCommand.RPOThreshold = "15";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Valid_Name_InBusinessFunction_With_TripleSpace_InBetween(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "PTS Technology   India  ";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "75";
        createBusinessFunctionCommand.RPOThreshold = "16";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Valid_Name_InBusinessFunction_With_SpecialCharacter(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "PTS$Technology";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "56";
        createBusinessFunctionCommand.RPOThreshold = "16";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Valid_Name_InBusinessFunction_With_UnderScore_InFront(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "_PTSTechnology";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "83";
        createBusinessFunctionCommand.RPOThreshold = "23";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Valid_Name_InBusinessFunction_With_UnderScore_InFront_AndBack(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "_PTSTechnology_";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "98";
        createBusinessFunctionCommand.RPOThreshold = "53";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Valid_Name_InBusinessFunction_With_Numbers_InFront(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "123PTSTechnology";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "63";
        createBusinessFunctionCommand.RPOThreshold = "56";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Valid_Name_InBusinessFunction_With_UnderScoreAndNumbers_InFront_AndUnderscore_InBack(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "_123PTSTechnology_";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "60";
        createBusinessFunctionCommand.RPOThreshold = "20";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Valid_Name_InBusinessFunction_With_UnderScore_InFront_AndNumbers_InBack(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "_PTSTechnology123";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "88";
        createBusinessFunctionCommand.RPOThreshold = "66";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Valid_Name_InBusinessFunction_With_NumbersOnly(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "0123456789";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "73";
        createBusinessFunctionCommand.RPOThreshold = "25";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Valid_Name_InBusinessFunction_With_SpecialCharacters_Only(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "!@#$$%^*&><:";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "66";
        createBusinessFunctionCommand.RPOThreshold = "12";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Valid_Name_InBusinessFunction_With_SpecialCharacters_InFront(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Name = "!@PTR";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "60";
        createBusinessFunctionCommand.RPOThreshold = "18";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionNameValidRequired, validateResult.Errors[0].ErrorMessage);
    }


    //Description

    //[Theory]
    // [AutoBusinessFunctionData]
    // public async Task Verify_Create_Description_InBusinessFunction_WithEmpty(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    // {
    //     var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

    //     createBusinessFunctionCommand.Description = "";

    //     var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
    //     Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionDescriptionRequired, validateResult.Errors[1].ErrorMessage);
    // }

    //[Theory]
    //[AutoBusinessFunctionData]
    //public async Task Verify_Create_Description_InBusinessFunction_IsNull(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    //{
    //    var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

    //    createBusinessFunctionCommand.Description = null;
    //    createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
    //    createBusinessFunctionCommand.ConfiguredRPO = "46";
    //    createBusinessFunctionCommand.RPOThreshold = "32";

    //    var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionOperationalServiceRequired, validateResult.Errors[1].ErrorMessage);
    //}

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_Description_InBusinessFunction_MaxiMumRange(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.Description = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "56";
        createBusinessFunctionCommand.RPOThreshold = "16";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionDescriptionMaximum, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_BusinessServiceName_InBusinessFunction_WithEmpty(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.BusinessServiceName = "";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "52";
        createBusinessFunctionCommand.RPOThreshold = "17";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionDescriptionContains, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_BusinessServiceName_InBusinessFunction_IsNull(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.BusinessServiceName = null;
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "36";
        createBusinessFunctionCommand.RPOThreshold = "23";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionBusinessServiceNameRequired, validateResult.Errors[3].ErrorMessage);
    }

    //ConfiguredRTO

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_ConfiguredRTO_InBusinessFunction_WithEmpty(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.ConfiguredRTO = "";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "77";
        createBusinessFunctionCommand.RPOThreshold = "56";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredRtoRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_ConfiguredRTO_InBusinessFunction_WithNumberOnly(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.ConfiguredRTO = "ABCD";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "81";
        createBusinessFunctionCommand.RPOThreshold = "11";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredRtoNumbersOnlyRequired, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_ConfiguredRTO_InBusinessFunction_IsNull(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.ConfiguredRTO = null;
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "65";
        createBusinessFunctionCommand.RPOThreshold = "36";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredRtoNotNullRequired, validateResult.Errors[4].ErrorMessage);
    }

    //ConfiguredRPO

    //[Theory]
    //[AutoBusinessFunctionData]
    //public async Task Verify_Create_ConfiguredRPO_InBusinessFunction_WithEmpty(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    //{
    //    var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

    //    createBusinessFunctionCommand.ConfiguredRPO = "";
    //    createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
    //    createBusinessFunctionCommand.ConfiguredRPO = "39";
    //    createBusinessFunctionCommand.RPOThreshold = "12";

    //    var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredRpoRequired, validateResult.Errors[2].ErrorMessage);
    //}

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_ConfiguredRPO_InBusinessFunction_WithNumberOnly(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.ConfiguredRPO = "ABCD";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "61";
        createBusinessFunctionCommand.RPOThreshold = "21";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredRpoNumbersOnlyRequired, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_ConfiguredRPO_InBusinessFunction_IsNull(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.ConfiguredRPO = null;
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        //createBusinessFunctionCommand.ConfiguredRPO = "62";
        createBusinessFunctionCommand.RPOThreshold = "42";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredRpoNotNullRequired, validateResult.Errors[5].ErrorMessage);
    }

    //ConfiguredMAO

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_ConfiguredMAO_InBusinessFunction_WithEmpty(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.ConfiguredMAO = "";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "60";
        createBusinessFunctionCommand.RPOThreshold = "20";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredMaoRequired, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_ConfiguredMAO_InBusinessFunction_WithNumberOnly(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.ConfiguredMAO = "ABCD";
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "40";
        createBusinessFunctionCommand.RPOThreshold = "30";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredMaoNumbersOnlyRequired, validateResult.Errors[4].ErrorMessage);
    }

    [Theory]
    [AutoBusinessFunctionData]
    public async Task Verify_Create_ConfiguredMAO_InBusinessFunction_IsNull(CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        var validator = new CreateBusinessFunctionCommandValidator(_mockBusinessFunctionRepository.Object);

        createBusinessFunctionCommand.ConfiguredMAO = null;
        createBusinessFunctionCommand.BusinessServiceId = "369e5a4c-8727-4481-a855-0f4b3fd147bd";
        createBusinessFunctionCommand.ConfiguredRPO = "61";
        createBusinessFunctionCommand.RPOThreshold = "21";

        var validateResult = await validator.ValidateAsync(createBusinessFunctionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.BusinessFunction.BusinessFunctionConfiguredMaoNotNullRequired, validateResult.Errors[5].ErrorMessage);
    }
}