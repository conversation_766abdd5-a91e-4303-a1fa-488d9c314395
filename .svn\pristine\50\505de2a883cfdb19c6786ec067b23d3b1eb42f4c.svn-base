const getADList = "Admin/Settings/GetList";
var createPermission = $("#adminCreate").data("create-permission").toLowerCase();
if (createPermission == 'false') {
    $('.d-flex.gap-4.flex-wrap').css({
        'pointer-events': 'none',
        'opacity': '0.5'
    });
    $(".btn-check").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-toggle').removeAttr('data-bs-target').removeAttr('id');

}
$(document).ready(function () {
    let defaultSkey = "AD Configuration"
   
    $(".nav-link").on('click', function () {             
        localStorage.setItem("id", $(".nav .nav-link.active").attr("id"));
        if ($("#v-pills-tab .nav-link.active").attr("id") == "v-pills-ad-tab") {
            $(".tab-pane.fade.show.active").removeClass("show active");
            $($(this).attr("href")).addClass("show active");
        }
        let ADSkey = $("#v-pills-tab .nav-link.active").text().trim();       
        let trimmedText = ADSkey.replace(/\s+/g, ' ').trim();
        defaultSkey = trimmedText;
        $("#txtADUserSKey").val(defaultSkey);
    });


    $('input[type=radio]').on('change', function () {                
        localStorage.setItem("id", $(".nav .nav-link.active").attr("id"))        
        $("#txtADUserSKey").val();
        let loginId = $('#txtADUserLoginUser').data("loginid");
        $('#txtADUserLoginUser').val(loginId);
        $('#txtADUserId').val();        
        $('#CreateADUserForm').submit();
    })
   
        //$.ajax({
        //    type: "GET",
        //    url: RootUrl + getADList,
        //    async: true,
        //    success: function (response) {
        //        if (response && response?.length > 0 || response != null) {
        //            var AD = response?.filter(Ad => Ad?.sKey === defaultSkey)
        //            if (AD?.length) {

        //                const ADValue = AD[0]
        //                var Adtype = ADValue.sValue;
        //                $('input[name="SValue"][value="' + Adtype + '"]').prop("checked", true);
        //                $('#txtADUserSKey').val(ADValue.sKey)
        //                $('#txtADUserLoginUser').val(ADValue.loginUserId)
        //                $('#txtADUserId').val(ADValue.id)

        //            }
        //            $(".nav .nav-link").removeClass("active")
        //            var id = localStorage.getItem("id")                     
        //            if (id !== null) {                       
        //                $("#" + id).removeClass("active")
        //                $("#" + id).addClass("nav-link active")
        //                var splitId = id.split("-")
        //                var slicevalue = splitId.slice(0, splitId.length - 1).join("-");
        //                $(".tab-content .tab-pane").removeClass("active")
        //                $("#" + slicevalue).removeClass("tab-pane fade")
        //                $("#" + slicevalue).addClass("tab-pane fade active show")
        //            }
        //        }

        //    }
        //}) 
})