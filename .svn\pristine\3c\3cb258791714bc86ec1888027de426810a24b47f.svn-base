﻿using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Create;
using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Update;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessFunctionListByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessServiceDrReadyDetails;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDrReadinessByBusinessServices;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyReport;
using ContinuityPatrol.Domain.ViewModels.DRReadyStatusModel;
using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.Mappings;

public class DrReadyStatusProfile : Profile
{
    public DrReadyStatusProfile()
    {
        CreateMap<DRReadyStatus, CreateDRReadyStatusCommand>().ReverseMap();
        CreateMap<UpdateDRReadyStatusCommand, DRReadyStatus>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<DRReadyStatus, DRReadyStatusListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DRReadyStatus, DRReadyStatusDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DRReadyStatus, DRReadyStatusByBusinessServiceIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        //CreateMap<Domain.Entities.DRReadyStatus, AllCount>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        // CreateMap<DRReadyStatus, DrReadyStatusForDrReadyReportVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DRReadyStatus, InfraObjectCountList>().ReverseMap();
        // CreateMap<DrReadyStatusForDrReadyReportVm, InfraObjectCountList>().ReverseMap();
        CreateMap<DRReadyStatus, GetBusinessFunctionListByBusinessServiceIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DRReadyStatus, GetInfraObjectList>().ReverseMap();

        //BusinessServiceDrReady

        CreateMap<HeatMapStatusView, GetInfraObjectList>()
            .ForMember(des => des.ReMark, opt => opt.MapFrom(src => src.ErrorMessage))
            .ForMember(des => des.Status, opt => opt.MapFrom(src => src.HeatmapStatus));


        CreateMap<List<HeatMapStatusView>, Components>()
            .ForMember(des => des.ServerDownCount,
                opt => opt.MapFrom(src =>
                    src.Count(x => x.HeatmapType.Trim().ToLower().Equals("server") && x.IsAffected.Equals(true))))
            .ForMember(des => des.DatabaseDownCount,
                opt => opt.MapFrom(src =>
                    src.Count(x => x.HeatmapType.Trim().ToLower().Equals("database") && x.IsAffected.Equals(true))))
            .ForMember(des => des.DataLagDownCount,
                opt => opt.MapFrom(src => src.Count(x =>
                    x.HeatmapType.Trim().ToLower().Equals("replication") && x.IsAffected.Equals(true))));


        CreateMap<MonitorService, RelatedServiceDto>().ReverseMap();

        CreateMap<MonitorService, RelatedServiceDto>().ReverseMap();


        CreateMap<BusinessService, BusinessServiceDrReadyDetailVm>()
            .ForMember(des => des.BusinessServiceId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(des => des.BusinessServiceName, opt => opt.MapFrom(src => src.Name));

        CreateMap<DRReadyStatus, DrReadyWorkflowAttach>().ReverseMap();
        CreateMap<DRReadyStatus, DrReadyWorkflowExecution>().ReverseMap();
        CreateMap<HeatMapStatusView, DrDataLagExceed>().ReverseMap();


        CreateMap<InfraObject, GetTotalDrReadyVm>()
            .ForMember(des => des.InfraObjectId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(des => des.InfraObjectName, opt => opt.MapFrom(src => src.Name));
        CreateMap<DRReadyStatus, GetDrReadyErrorExecutionVm>().ReverseMap();
        CreateMap<InfraObject, GetWorkflowNotConfiguredVm>()
            .ForMember(des => des.InfraObjectId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(des => des.InfraObjectName, opt => opt.MapFrom(src => src.Name))
            .ForMember(des => des.ErrorMessage, opt => opt.MapFrom(src => "Resiliency workflow not configured."));
    }
}