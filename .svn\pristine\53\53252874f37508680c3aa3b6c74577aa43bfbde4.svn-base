﻿using ContinuityPatrol.Application.Features.Database.Events.LicenseInfoEvents.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Tests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Database.Events.LicenseInfoEvents;

public class UpdateDatabaseLicenseInfoEventTests : IClassFixture<DatabaseFixture>, IClassFixture<LicenseInfoFixture>
{
    private readonly DatabaseFixture _databaseFixture;
    
    private readonly LicenseInfoFixture _licenseInfoFixture;

    private readonly Mock<ILicenseInfoRepository> _mockLicenseInfoRepository;

    private readonly DatabaseLicenseInfoUpdatedEventHandler _handler;

    public UpdateDatabaseLicenseInfoEventTests(DatabaseFixture databaseFixture, LicenseInfoFixture licenseInfoFixture)
    {
        _databaseFixture = databaseFixture;
        
        _licenseInfoFixture = licenseInfoFixture;

        var mockDatabaseLicenseInfoCreatedEventLogger = new Mock<ILogger<DatabaseLicenseInfoUpdatedEventHandler>>();

        var mockLoggedInUserService = LoggedInUserServiceRepositoryMocks.GetLoggedInUserServiceRepository();

        _mockLicenseInfoRepository = LicenseInfoRepositoryMocks.UpdateDatabaseLicenseInfoEventRepository(_licenseInfoFixture.LicenseInfos);
        
        _handler = new DatabaseLicenseInfoUpdatedEventHandler(_mockLicenseInfoRepository.Object, mockDatabaseLicenseInfoCreatedEventLogger.Object, mockLoggedInUserService.Object);
    }

    [Fact]
    public async Task Handle_IncreaseLicenseInfoCount_When_UpdateDatabaseLicenseInfoEvent()
    {
        var result = _handler.Handle(_databaseFixture.DatabaseLicenseInfoUpdatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _licenseInfoFixture.LicenseInfos[0].EntityId = _databaseFixture.DatabaseLicenseInfoUpdatedEvent.EntityId;

        await _handler.Handle(_databaseFixture.DatabaseLicenseInfoUpdatedEvent, CancellationToken.None);

        _mockLicenseInfoRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.LicenseInfo>()), Times.Once);
    }
}