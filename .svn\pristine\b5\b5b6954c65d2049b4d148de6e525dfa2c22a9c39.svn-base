﻿using ContinuityPatrol.Application.Features.Company.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.Features.Database.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Database.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Database.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Database.Commands.Update;
using ContinuityPatrol.Application.Features.Database.Commands.UpdateVersion;
using ContinuityPatrol.Application.Features.Database.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByType;
using ContinuityPatrol.Application.Features.Database.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Database.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Database.Queries.GetType;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using FluentValidation.Results;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class DatabaseControllerShould
    {
        private readonly Mock<ILogger<DatabaseController>> _mockLogger = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly DatabaseController _controller;

        public DatabaseControllerShould()
        {
            _controller = new DatabaseController(
                _mockLogger.Object,
                _mockPublisher.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );

            var httpContext = new DefaultHttpContext();
            _controller.ControllerContext.HttpContext = httpContext;
            _controller.TempData = TempDataFakes.GeTempDataDictionary(httpContext, "Test", "Test");

            // Setup WebHelper.UserSession for tests that need it
            WebHelper.UserSession = new UserSession
            {
                CompanyId = "test-company-123",
                LoggedUserId = "test-user-123"
            };
        }

        // ===== LIST METHOD TESTS =====
        [Fact]
        public async Task List_ShouldReturnViewResult()
        {
            // Act
            var result = await _controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
            _mockPublisher.Verify(x => x.Publish(It.IsAny<DatabasePaginatedEvent>(), default), Times.Once);
        }

        [Fact]
        public async Task List_ShouldPublishDatabasePaginatedEvent()
        {
            // Act
            await _controller.List();

            // Assert
            _mockPublisher.Verify(x => x.Publish(It.IsAny<DatabasePaginatedEvent>(), default), Times.Once);
        }

        // ===== CREATE OR UPDATE METHOD TESTS =====

        [Fact]
        public async Task CreateOrUpdate_ShouldCreateDatabase_WhenIdIsEmpty()
        {
            // Arrange
            var viewModel = new DatabaseViewModel { Name = "TestDB", Properties = "test-props" };
            var createCommand = new CreateDatabaseCommand { Name = "TestDB" };
            var response = new BaseResponse { Message = "Created successfully" };

            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "" }
            });
            _controller.Request.Form = formCollection;

            _mockMapper.Setup(x => x.Map<CreateDatabaseCommand>(It.IsAny<DatabaseViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(x => x.Database.CreateAsync(It.IsAny<CreateDatabaseCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = GetJsonResultValue(result);
            Assert.True((bool)resultValue.GetType().GetProperty("success")?.GetValue(resultValue));
            _mockDataProvider.Verify(x => x.Database.CreateAsync(It.IsAny<CreateDatabaseCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldUpdateDatabase_WhenIdIsProvided()
        {
            // Arrange
            var viewModel = new DatabaseViewModel { Name = "TestDB", Properties = "test-props" };
            var updateCommand = new UpdateDatabaseCommand { Name = "TestDB" };
            var response = new BaseResponse { Message = "Updated successfully" };

            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "test-id-123" }
            });
            _controller.Request.Form = formCollection;

            _mockMapper.Setup(x => x.Map<UpdateDatabaseCommand>(It.IsAny<DatabaseViewModel>()))
                .Returns(updateCommand);
            _mockDataProvider.Setup(x => x.Database.UpdateAsync(It.IsAny<UpdateDatabaseCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = GetJsonResultValue(result);
            Assert.True((bool)resultValue.GetType().GetProperty("success")?.GetValue(resultValue));
            _mockDataProvider.Verify(x => x.Database.UpdateAsync(It.IsAny<UpdateDatabaseCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleValidationException()
        {
            // Arrange
            var viewModel = new DatabaseViewModel { Name = "TestDB" };
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "" }
            });
            _controller.Request.Form = formCollection;

            _mockMapper.Setup(x => x.Map<CreateDatabaseCommand>(It.IsAny<DatabaseViewModel>()))
                .Returns(new CreateDatabaseCommand());
            _mockDataProvider.Setup(x => x.Database.CreateAsync(It.IsAny<CreateDatabaseCommand>()))
                .ThrowsAsync(new ValidationException(new ValidationResult()));

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleGeneralException()
        {
            // Arrange
            var viewModel = new DatabaseViewModel { Name = "TestDB" };
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", "" }
            });
            _controller.Request.Form = formCollection;

            _mockMapper.Setup(x => x.Map<CreateDatabaseCommand>(It.IsAny<DatabaseViewModel>()))
                .Returns(new CreateDatabaseCommand());
            _mockDataProvider.Setup(x => x.Database.CreateAsync(It.IsAny<CreateDatabaseCommand>()))
                .ThrowsAsync(new Exception("General error"));

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== DELETE METHOD TESTS =====

        [Fact]
        public async Task Delete_ShouldReturnJsonResult_OnSuccess()
        {
            // Arrange
            var id = "test-id";
            var response = new BaseResponse { Message = "Deleted successfully" };
            _mockDataProvider.Setup(x => x.Database.DeleteAsync(id)).ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = GetJsonResultValue(result);
            Assert.True((bool)resultValue.GetType().GetProperty("success")?.GetValue(resultValue));
            _mockDataProvider.Verify(x => x.Database.DeleteAsync(id), Times.Once);
        }

        [Fact]
        public async Task Delete_ShouldHandleException()
        {
            // Arrange
            var id = "test-id";
            _mockDataProvider.Setup(x => x.Database.DeleteAsync(id))
                .ThrowsAsync(new Exception("Delete failed"));

            // Act
            var result = await _controller.Delete(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== GET DATABASE LIST METHOD TESTS =====

        [Fact]
        public async Task GetDataBaseList_ShouldReturnDatabaseList_OnSuccess()
        {
            // Arrange
            var type = "test-type";
            var expectedList = new List<GetDatabaseByTypeVm>
            {
                new GetDatabaseByTypeVm { Id = "1", Name = "Database1" },
                new GetDatabaseByTypeVm { Id = "2", Name = "Database2" }
            };
            _mockDataProvider.Setup(x => x.Database.GetByType(type)).ReturnsAsync(expectedList);

            // Act
            var result = await _controller.GetDataBaseList(type);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedList.Count, result.Count);
            _mockDataProvider.Verify(x => x.Database.GetByType(type), Times.Once);
        }

        [Fact]
        public async Task GetDataBaseList_ShouldReturnEmptyList_OnException()
        {
            // Arrange
            var type = "test-type";
            _mockDataProvider.Setup(x => x.Database.GetByType(type))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.GetDataBaseList(type);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        // ===== GET DATABASE NAMES FOR SAVE AS METHOD TESTS =====

        [Fact]
        public async Task GetDatabaseNamesForSaveAs_ShouldReturnJsonResult_OnSuccess()
        {
            // Arrange
            var databaseNames = new List<DatabaseNameVm>
            {
                new DatabaseNameVm { Name = "DB1" },
                new DatabaseNameVm { Name = "DB2" },
                new DatabaseNameVm { Name = "DB3" }
            };
            _mockDataProvider.Setup(x => x.Database.GetDatabaseNames()).ReturnsAsync(databaseNames);

            // Act
            var result = await _controller.GetDatabaseNamesForSaveAs() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = GetJsonResultValue(result);
            Assert.True((bool)resultValue.GetType().GetProperty("success")?.GetValue(resultValue));
            _mockDataProvider.Verify(x => x.Database.GetDatabaseNames(), Times.Once);
        }

        [Fact]
        public async Task GetDatabaseNamesForSaveAs_ShouldHandleException()
        {
            // Arrange
            _mockDataProvider.Setup(x => x.Database.GetDatabaseNames())
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.GetDatabaseNamesForSaveAs() as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== SAVE ALL DATABASE METHOD TESTS =====

        [Fact]
        public async Task SaveAllDatabase_ShouldReturnJsonResult_OnSuccess()
        {
            // Arrange
            var command = new SaveAllDatabaseCommand();
            var response = new BaseResponse { Message = "Saved successfully" };
            _mockDataProvider.Setup(x => x.Database.SaveAllDatabase(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.SaveAllDatabase(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = GetJsonResultValue(result);
            Assert.True((bool)resultValue.GetType().GetProperty("success")?.GetValue(resultValue));
            _mockDataProvider.Verify(x => x.Database.SaveAllDatabase(command), Times.Once);
        }

        [Fact]
        public async Task SaveAllDatabase_ShouldHandleException()
        {
            // Arrange
            var command = new SaveAllDatabaseCommand();
            _mockDataProvider.Setup(x => x.Database.SaveAllDatabase(command))
                .ThrowsAsync(new Exception("Save failed"));

            // Act
            var result = await _controller.SaveAllDatabase(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== SAVE AS DATABASE METHOD TESTS =====

        [Fact]
        public async Task SaveAsDatabase_ShouldReturnJsonResult_OnSuccess()
        {
            // Arrange
            var command = new SaveAsDatabaseCommand();
            var response = new BaseResponse { Message = "Saved successfully" };
            _mockDataProvider.Setup(x => x.Database.SaveAsDatabase(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.SaveAsDatabase(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = GetJsonResultValue(result);
            Assert.True((bool)resultValue.GetType().GetProperty("success")?.GetValue(resultValue));
            _mockDataProvider.Verify(x => x.Database.SaveAsDatabase(command), Times.Once);
        }

        [Fact]
        public async Task SaveAsDatabase_ShouldHandleException()
        {
            // Arrange
            var command = new SaveAsDatabaseCommand();
            _mockDataProvider.Setup(x => x.Database.SaveAsDatabase(command))
                .ThrowsAsync(new Exception("Save failed"));

            // Act
            var result = await _controller.SaveAsDatabase(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== GET DATABASE NAMES METHOD TESTS =====

        [Fact]
        public async Task GetDatabaseNames_ShouldReturnJsonResult_OnSuccess()
        {
            // Arrange
            var type = "test-type";
            var databaseNames = new List<DatabaseTypeVm>
            {
                new DatabaseTypeVm { Id = "1", Name = "Database1" },
                new DatabaseTypeVm { Id = "2", Name = "Database2" }
            };
            _mockDataProvider.Setup(x => x.Database.GetByDatabaseType(type)).ReturnsAsync(databaseNames);

            // Act
            var result = await _controller.GetDatabaseNames(type) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = GetJsonResultValue(result);
            Assert.True((bool)resultValue.GetType().GetProperty("success")?.GetValue(resultValue));
            _mockDataProvider.Verify(x => x.Database.GetByDatabaseType(type), Times.Once);
        }

        [Fact]
        public async Task GetDatabaseNames_ShouldHandleException()
        {
            // Arrange
            var type = "test-type";
            _mockDataProvider.Setup(x => x.Database.GetByDatabaseType(type))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.GetDatabaseNames(type) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== IS DATABASE NAME EXIST METHOD TESTS =====

        [Fact]
        public async Task IsDatabaseNameExist_ShouldReturnTrue_WhenNameExists()
        {
            // Arrange
            var name = "test-name";
            var id = "test-id";
            _mockDataProvider.Setup(x => x.Database.IsDatabaseNameExist(name, id)).ReturnsAsync(true);

            // Act
            var result = await _controller.IsDatabaseNameExist(name, id);

            // Assert
            Assert.True(result);
            _mockDataProvider.Verify(x => x.Database.IsDatabaseNameExist(name, id), Times.Once);
        }

        [Fact]
        public async Task IsDatabaseNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
        {
            // Arrange
            var name = "test-name";
            var id = "test-id";
            _mockDataProvider.Setup(x => x.Database.IsDatabaseNameExist(name, id)).ReturnsAsync(false);

            // Act
            var result = await _controller.IsDatabaseNameExist(name, id);

            // Assert
            Assert.False(result);
            _mockDataProvider.Verify(x => x.Database.IsDatabaseNameExist(name, id), Times.Once);
        }

        [Fact]
        public async Task IsDatabaseNameExist_ShouldReturnFalse_OnException()
        {
            // Arrange
            var name = "test-name";
            var id = "test-id";
            _mockDataProvider.Setup(x => x.Database.IsDatabaseNameExist(name, id))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.IsDatabaseNameExist(name, id);

            // Assert
            Assert.False(result);
        }

        // ===== UPDATE DATABASE FORM VERSION METHOD TESTS =====

        [Fact]
        public async Task UpdateDatabaseFormVersion_ShouldReturnJsonResult_OnSuccess()
        {
            // Arrange
            var command = new UpdateDatabaseVersionCommand();
            var response = new BaseResponse { Message = "Version updated successfully" };
            _mockDataProvider.Setup(x => x.Database.UpdateDatabaseFormVersion(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.UpdateDatabaseFormVersion(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = GetJsonResultValue(result);
            Assert.True((bool)resultValue.GetType().GetProperty("success")?.GetValue(resultValue));
            _mockDataProvider.Verify(x => x.Database.UpdateDatabaseFormVersion(command), Times.Once);
        }

        [Fact]
        public async Task UpdateDatabaseFormVersion_ShouldHandleException()
        {
            // Arrange
            var command = new UpdateDatabaseVersionCommand();
            _mockDataProvider.Setup(x => x.Database.UpdateDatabaseFormVersion(command))
                .ThrowsAsync(new Exception("Update failed"));

            // Act
            var result = await _controller.UpdateDatabaseFormVersion(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== GET PAGINATION METHOD TESTS =====

        [Fact]
        public async Task GetPagination_ShouldReturnJsonResult_OnSuccess()
        {
            // Arrange
            var query = new GetDatabasePaginatedListQuery
            {
                DatabaseTypeId = "test-type",
                PageNumber = 1,
                PageSize = 10,
                SearchString = "test"
            };
            var paginatedResult = new PaginatedResult<DatabaseListVm>
            {
                Data = new List<DatabaseListVm>(),
                TotalCount = 0
            };
            _mockDataProvider.Setup(x => x.Database.GetDatabasePaginatedList(query))
                .ReturnsAsync(paginatedResult);

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = GetJsonResultValue(result);
            Assert.True((bool)resultValue.GetType().GetProperty("success")?.GetValue(resultValue));
            _mockDataProvider.Verify(x => x.Database.GetDatabasePaginatedList(query), Times.Once);
        }

        [Fact]
        public async Task GetPagination_ShouldHandleException()
        {
            // Arrange
            var query = new GetDatabasePaginatedListQuery();
            _mockDataProvider.Setup(x => x.Database.GetDatabasePaginatedList(query))
                .ThrowsAsync(new Exception("Pagination failed"));

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== DATABASE TEST CONNECTION METHOD TESTS =====

        [Fact]
        public async Task DatabaseTestConnection_ShouldReturnJsonResult_OnSuccessfulConnection()
        {
            // Arrange
            var command = new DatabaseTestConnectionCommand { Id = new List<string> { "test-id-1", "test-id-2" } };
            var response = new BaseResponse { Success = true, Message = "Connection successful" };
            _mockDataProvider.Setup(x => x.Database.DatabaseTestConnection(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.DatabaseTestConnection(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = GetJsonResultValue(result);
            Assert.True((bool)resultValue.GetType().GetProperty("success")?.GetValue(resultValue));
            _mockDataProvider.Verify(x => x.Database.DatabaseTestConnection(command), Times.Once);
        }

        [Fact]
        public async Task DatabaseTestConnection_ShouldReturnJsonResult_OnFailedConnection()
        {
            // Arrange
            var command = new DatabaseTestConnectionCommand { Id = new List<string> { "test-id-1" } };
            var response = new BaseResponse { Success = false, Message = "Connection failed" };
            _mockDataProvider.Setup(x => x.Database.DatabaseTestConnection(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.DatabaseTestConnection(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = GetJsonResultValue(result);
            Assert.False((bool)resultValue.GetType().GetProperty("success")?.GetValue(resultValue));
        }

        [Fact]
        public async Task DatabaseTestConnection_ShouldHandleException()
        {
            // Arrange
            var command = new DatabaseTestConnectionCommand { Id = new List<string> { "test-id-1", "test-id-2", "test-id-3" } };
            _mockDataProvider.Setup(x => x.Database.DatabaseTestConnection(command))
                .ThrowsAsync(new Exception("Connection test failed"));

            // Act
            var result = await _controller.DatabaseTestConnection(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== GET BY REFERENCE ID METHOD TESTS =====

        [Fact]
        public async Task GetByReferenceId_ShouldReturnJsonResult_OnSuccess()
        {
            // Arrange
            var id = "test-id";
            var response = new DatabaseDetailVm { Id = id, Name = "TestDatabase" };
            _mockDataProvider.Setup(x => x.Database.GetByReferenceId(id)).ReturnsAsync(response);

            // Act
            var result = await _controller.GetByReferenceId(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = GetJsonResultValue(result);
            Assert.True((bool)resultValue.GetType().GetProperty("success")?.GetValue(resultValue));
            _mockDataProvider.Verify(x => x.Database.GetByReferenceId(id), Times.Once);
        }

        [Fact]
        public async Task GetByReferenceId_ShouldHandleException()
        {
            // Arrange
            var id = "test-id";
            _mockDataProvider.Setup(x => x.Database.GetByReferenceId(id))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.GetByReferenceId(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== LOAD REPORT METHOD TESTS =====

        [Fact]
        public async Task LoadReport_ShouldReturnFileResult_ForPdfType()
        {
            // Arrange
            var type = "pdf";
            var selectedTypeId = "test-type-id";
            var searchString = "test-search";

            var companyDetails = new CompanyDetailVm { CompanyLogo = "test-logo.png" };
            var paginatedResult = new PaginatedResult<DatabaseListVm>
            {
                Data = new List<DatabaseListVm>()
            };

            _mockDataProvider.Setup(x => x.Company.GetCompanyById(It.IsAny<string>()))
                .ReturnsAsync(companyDetails);
            _mockDataProvider.Setup(x => x.Database.GetDatabasePaginatedList(It.IsAny<GetDatabasePaginatedListQuery>()))
                .ReturnsAsync(paginatedResult);

            // Act
            var result = await _controller.LoadReport(type, selectedTypeId, searchString);

            // Assert
            Assert.NotNull(result);
            // Note: This test may need adjustment based on the actual report generation implementation
        }

        [Fact]
        public async Task LoadReport_ShouldReturnFileResult_ForExcelType()
        {
            // Arrange
            var type = "excel";
            var selectedTypeId = "test-type-id";
            var searchString = "test-search";

            var companyDetails = new CompanyDetailVm { CompanyLogo = "test-logo.png" };
            var paginatedResult = new PaginatedResult<DatabaseListVm>
            {
                Data = new List<DatabaseListVm>()
            };

            _mockDataProvider.Setup(x => x.Company.GetCompanyById(It.IsAny<string>()))
                .ReturnsAsync(companyDetails);
            _mockDataProvider.Setup(x => x.Database.GetDatabasePaginatedList(It.IsAny<GetDatabasePaginatedListQuery>()))
                .ReturnsAsync(paginatedResult);

            // Act
            var result = await _controller.LoadReport(type, selectedTypeId, searchString);

            // Assert
            Assert.NotNull(result);
            // Note: This test may need adjustment based on the actual report generation implementation
        }

        [Fact]
        public async Task LoadReport_ShouldReturnContentResult_OnException()
        {
            // Arrange
            var type = "pdf";
            var selectedTypeId = "test-type-id";
            var searchString = "test-search";

            _mockDataProvider.Setup(x => x.Company.GetCompanyById(It.IsAny<string>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.LoadReport(type, selectedTypeId, searchString);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ContentResult>(result);
        }

        // ===== HELPER METHODS =====

        private static object GetJsonResultValue(JsonResult jsonResult)
        {
            return jsonResult.Value ?? new object();
        }
    }
}
