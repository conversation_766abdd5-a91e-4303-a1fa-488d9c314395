using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapLogModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ICyberAirGapLogService
{
    Task<List<CyberAirGapLogListVm>> GetCyberAirGapLogList();
    Task<BaseResponse> CreateAsync(CreateCyberAirGapLogCommand createCyberAirGapLogCommand);
    Task<BaseResponse> UpdateAsync(UpdateCyberAirGapLogCommand updateCyberAirGapLogCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<CyberAirGapLogDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsCyberAirGapLogNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<CyberAirGapLogListVm>> GetPaginatedCyberAirGapLogs(GetCyberAirGapLogPaginatedListQuery query);
    #endregion
}
