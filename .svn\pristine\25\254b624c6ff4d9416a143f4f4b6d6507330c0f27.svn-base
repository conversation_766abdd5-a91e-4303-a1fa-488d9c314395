﻿namespace ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Commands.Create;

public class
    CreateMYSQLMonitorLogCommandHandler : IRequestHandler<CreateMYSQLMonitorLogCommand, CreateMYSQLMonitorLogResponse>
{
    private readonly IMapper _mapper;
    private readonly IMysqlMonitorLogsRepository _mYSQLMonitorLogsRepository;

    public CreateMYSQLMonitorLogCommandHandler(IMapper mapper, IMysqlMonitorLogsRepository mYSQLMonitorLogsRepository)
    {
        _mapper = mapper;
        _mYSQLMonitorLogsRepository = mYSQLMonitorLogsRepository;
    }

    public async Task<CreateMYSQLMonitorLogResponse> Handle(CreateMYSQLMonitorLogCommand request,
        CancellationToken cancellationToken)
    {
        var mysqlMonitorLog = _mapper.Map<Domain.Entities.MYSQLMonitorLogs>(request);

        mysqlMonitorLog = await _mYSQLMonitorLogsRepository.AddAsync(mysqlMonitorLog);

        var response = new CreateMYSQLMonitorLogResponse
        {
            Message = Message.Create(nameof(Domain.Entities.MYSQLMonitorLogs), mysqlMonitorLog.ReferenceId),
            Id = mysqlMonitorLog.ReferenceId
        };

        return response;
    }
}