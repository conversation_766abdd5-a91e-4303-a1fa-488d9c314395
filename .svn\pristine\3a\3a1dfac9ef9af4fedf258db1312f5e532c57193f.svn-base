﻿namespace ContinuityPatrol.Application.Features.SmsConfiguration.Queries.GetDetail;

public class
    GetSmsConfigurationDetailQueryHandler : IRequestHandler<GetSmsConfigurationDetailQuery, SmsConfigurationDetailVm>
{
    private readonly IMapper _mapper;
    private readonly ISmsConfigurationRepository _smsConfigurationRepository;

    public GetSmsConfigurationDetailQueryHandler(IMapper mapper, ISmsConfigurationRepository smsConfigurationRepository)
    {
        _mapper = mapper;
        _smsConfigurationRepository = smsConfigurationRepository;
    }

    public async Task<SmsConfigurationDetailVm> Handle(GetSmsConfigurationDetailQuery request,
        CancellationToken cancellationToken)
    {
        var smsConfiguration = await _smsConfigurationRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(smsConfiguration, nameof(Domain.Entities.SmsConfiguration),
            new NotFoundException(nameof(Domain.Entities.SmsConfiguration), request.Id));

        var smsConfigurationDetailDto = _mapper.Map<SmsConfigurationDetailVm>(smsConfiguration);

        return smsConfigurationDetailDto;
    }
}