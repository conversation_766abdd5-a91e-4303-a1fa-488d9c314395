﻿const alertnameExistUrl = "Alert/ManageAlert/IsAlertNameExist";
const alertidExistUrl = "Alert/ManageAlert/IsAlertIdExist";
const levListParent = document.getElementById('Escalationlev_Time_ul');
var createPermission = $("#AlertCreate").data("create-permission").toLowerCase();
var deletePermission = $("#AlertDelete").data("delete-permission").toLowerCase();
var selectedValues = [];
let DeleteId = "", globalAlertId = '', reportQuery = [], dataTable
if (createPermission == 'false') {
    $(".createBtn").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
}
function managealertdebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
$(function () {
     dataTable = $('#alertmaster_tabledata').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }, infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/Alert/ManageAlert/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "alertId" : sortIndex === 2 ? "alertName" : sortIndex === 3 ? "alertMessage" :
                        sortIndex === 4 ? "alertPriority" : sortIndex === 5 ? "alertFrequency" : "";
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.alertPriority = $("#Alert_Priority").find("option:selected").val() === "All" ? '' : $("#Alert_Priority").find("option:selected").val()
                    d.alertName = $("#Infraobject").find("option:selected").val() === "All" ? '' : $("#Infraobject").find("option:selected").val()
                    selectedValues.length = 0;
                    reportQuery = d;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        let alert_data = json?.data?.data
                        alert_data.forEach(function (value, index) {
                            $('#Infraobject').append('<option  title="' + value.alertName + '" value="' + value.alertName + '">' + value.alertName + '</option>')
                        });
                        $("#Infraobject option,#Alert_Priority option").each(function () {
                            $(this).siblings('[value="' + this.value + '"]').remove()
                        })
                        json.recordsTotal = json.data.totalPages;
                        json.recordsFiltered = json.data.totalCount;
                        if (json?.data?.data?.length === 0) {
                            $(".pagination-column").addClass("disabled")
                            $("#BtnManageAlertDownload").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                            $("#BtnManageAlertDownload").removeClass("disabled")
                        }
                        return json?.data?.data;
                    } else {
                        errorNotification(json)
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [ 1, 2, 3, 4, 5, 6, 7, 8, 9, 10,11],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        return type === 'display' ? meta.row + 1 : data;
                    },
                    "orderable": false
                },
                {
                    "data": "alertId",
                    "name": "Alert ID",
                    "autoWidth": true,
                    "render": function (data, type, row, meta) {
                        return `<td class="text-truncate"><span title="${data}" > ${data}</td>`;
                    }
                },
                { 
                    "data": "alertName", "name": "Alert Name", "autoWidth": true, 
                    "render": function (data, type, row) {
                        return `<td class="text-truncate" ><span title="${data == "" ? "NA" : data}" > ${data == "" ? "NA" : data}</td>`;
                    }
                },
                {
                    "data": "alertMessage",
                    "name": "Alert Message",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data}" > ${data}</span></td>`;
                    }
                },
                {
                    "data": "alertPriority",
                    "name": "Alert Priority",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        var iconClass = '';
                        if (data == "High") {
                            iconClass = "fw-bold cp-up-doublearrow text-warning";
                        } else if (data == "Critical") {
                            iconClass = "fw-bold cp-critical-level text-danger";
                        } else if (data == "Information") {
                            iconClass = "cp-warning text-primary";
                        } else if (data == "Low") {
                            iconClass = "fw-bold cp-down-doublearrow text-success";
                        }
                        return `<td><i class="me-1 ${iconClass}"></i>  ${data}</td>`;
                    }
                },
                {
                    "data": "alertFrequency", "name": "Alert Count", "autoWidth": true,
                    "render": function (data, type, full, meta) {
                        return '<td>' + data + '</td>';
                    }
                },
                {
                    "data": "isSendSMS", "name": "SMS ", "autoWidth": true, 
                    "render": function (data, type, full, meta) {
                        if (createPermission === 'false' && data == true) {
                            return '<input type="checkbox"  name="smscheckbox" checked disabled>'
                        }
                        if (createPermission === 'false') {
                            return '<input type="checkbox"  name="smscheckbox" disabled>'
                        }
                        if (data == true) {
                            return '<input type="checkbox"  name="smscheckbox" checked disabled>'
                        } else {
                            return '<input type="checkbox"  name="smscheckbox" disabled >'
                        }
                    }, "orderable": false,
                },
                {
                    "data": "isSendEmail", "name": "Email", "autoWidth": true,
                    "render": function (data, type, full, meta) {
                        if (createPermission === 'false' && data == true) {
                            return '<input type="checkbox"  name="emailcheckbox" checked disabled>'
                        }
                        if (createPermission === 'false') {
                            return '<input type="checkbox"  name="emailcheckbox" disabled>'
                        }
                        if (data == true) {
                            return '<input type="checkbox"  name="emailcheckbox" checked disabled>';
                        } else {
                            return '<input type="checkbox"  name="emailcheckbox" disabled>';
                        }
                    }, "orderable": false,
                },
                {
                    "data": null, "name": "Escalation", "autoWidth": true,
                    "render": function (data, type, full, meta) {
                        if (createPermission === 'false' && data.isAcknowledgement) {
                            return `<input  type="checkbox" class="attachCheckbox" name="Escalationcheckbox" checked id=${data.id} disabled>`
                        }
                        if (createPermission === 'false') {
                            return `<input  type="checkbox" class="attachCheckbox" name="Escalationcheckbox" disabled>`
                        }
                        if (data.isAcknowledgement)
                            return `<input  type="checkbox" class="attachCheckbox" name="Escalationcheckbox" checked id=${data.id}>`;
                        else
                            return `<input  type="checkbox" class="attachCheckbox" name="Escalationcheckbox" id=${data.id}>`;

                    }, "orderable": false,
                },
                {
                    "data": null, "name": "Manage Escalation", "autoWidth": true,
                    "render": function (data, type, full, meta) {

                        if (data.isAcknowledgement) {
                            return `<td class="text-truncate "> <button class="btn btn-primary btn-sm escmatlist fs-8" data-bs-target="#EscalationModal" data-bs-toggle="modal" data-esc-id="${data.id}" style="padding:2px 5px;"  data-esc-ack="${data.isAcknowledgement}"  data-esc-matidd="${data.escMatId}"  id=attbtn${data.id}>Attach Escalation</button></td>`;
                        } else {
                            return `<td class="text-truncate "> <button class="btn btn-primary btn-sm escmatlist fs-8" data-bs-target="#EscalationModal" data-bs-toggle="modal" disabled style="padding:2px 5px;"  data-esc-id="${data.id}" data-esc-ack="${data.isAcknowledgement}" data-esc-matidd="${data.escMatId}" id=attbtn${data.id}>Attach Escalation</button></td>`;
                        }
                    }, "orderable": false,
                },
                {
                    "data": "isAlertActive", "name": "Active / Deactive", "autoWidth": true,
                    "render": function (data, type, full, meta) {
                        if (createPermission === 'false' && data == true) {
                            return '<input type="checkbox"  name="Active/inactive_checkbox" checked="' + data + '" disabled>';
                        }
                        if (createPermission === 'false') {
                            return '<input type="checkbox"  name="Active/inactive_checkbox" disabled>';
                        }
                        if (data == true) {
                            return '<input type="checkbox"  name="Active/inactive_checkbox" checked="' + data + '" disabled>';
                        } else {
                            return '<input type="checkbox"  name="Active/inactive_checkbox" disabled>';
                        }
                    }, "orderable": false,
                },
                {
                    "data": null, "name": "Action", "autoWidth": true,
                    "render": function (data, type, full, meta) {
                        if (createPermission == 'false' && deletePermission == 'true') {
                            return '<td class="Action-th text-truncate"><div class="d-flex align-items-center gap-2"> <span class="icon-disabled role="button"><i class="cp-edit"></i></span><span   role="button" data-bs-toggle="modal" onclick="delete_id(this)" title="Delete" deletename="' + data.alertName + '" deleteId="' + data.id + '" data-bs-target="#DeleteModal"><i class="cp-Delete"></i></span> <span  role="button" data-bs-toggle="modal" data-bs-target="#ViewHistory" isSendEmail="' + data.isSendEmail + '"  isSendSMS="' + data.isSendSMS + '"   alertfrequency="' + data.alertFrequency + '" alerttime="' + data.alertSendTime + '" alertPriorty="' + data.alertPriority + '"  alertMessage="' + data.alertMessage + '" alertName="' + data.alertName + '" alertId="' + data.alertId + '" onclick="viewhistory(this)" title="Schedule"><i class="cp-time "></i></span></div></td>';
                        }
                        else if (createPermission == 'true' && deletePermission == 'false') {
                            return '<td class="Action-th text-truncate"><div class="d-flex align-items-center gap-2"> <span  role="button" data-bs-toggle="modal" isSendEmail=' + data.isSendEmail + ' isSendSMS=' + data.isSendSMS + '  isAlertActive=' + data.isAlertActive + ' alertfrequency="' + data.alertFrequency + '" alerttime="' + data.alertSendTime + '" alertPriorty="' + data.alertPriority + '"  alertMessage="' + data.alertMessage + '" alertName="' + data.alertName + '" alertId="' + data.alertId + '" smsmessage="' + data.smSMessage + '" onclick="editalert(this)"  title="Edit" editId="' + data.id + '"><i class="cp-edit"></i></span><span  class="icon-disabled" role="button" deletename="' + data.alertName + '" deleteId="' + data.id + '" data-bs-toggle="modal"onclick="delete_id(this)" title="Delete" ><i class="cp-Delete"></i></span> <span  role="button" data-bs-toggle="modal" data-bs-target="#ViewHistory" isSendEmail1=' + data.isSendEmail + '  isSendSMS1=' + data.isSendSMS + '   alertfrequency1=' + data.alertFrequency + ' alerttime1="' + data.alertSendTime + '" alertPriorty1="' + data.alertPriority + '"  alertMessage1="' + data.alertMessage + '" alertName1="' + data.alertName + '" alertId1="' + data.alertId + '" onclick="viewhistory(this)" title="Schedule"><i class="cp-time "></i></span></div></td>';

                        }
                        else if (createPermission == 'false' && deletePermission == 'false') {
                            return '<td class="Action-th text-truncate"><div class="d-flex align-items-center gap-2"><span class="icon-disabled"><i class="cp-edit" ></i></span><span  class="icon-disabled" role="button" data-bs-toggle="modal" onclick="delete_id(this)" title="Delete"><i class="cp-Delete"></i></span><span  role="button" data-bs-toggle="modal" data-bs-target="#ViewHistory" isSendEmail1=' + data.isSendEmail + '  isSendSMS1=' + data.isSendSMS + '   alertfrequency1=' + data.alertFrequency + ' alerttime1="' + data.alertSendTime + '" alertPriorty1="' + data.alertPriority + '"  alertMessage1="' + data.alertMessage + '" alertName1="' + data.alertName + '" alertId1="' + data.alertId + '" onclick="viewhistory(this)" title="Schedule"><i class="cp-time "></i></span></div></td>';
                        }
                        else if (createPermission == 'true' && deletePermission == 'true') {
                            return '<td class="Action-th text-truncate"><div class="d-flex align-items-center gap-2"> <span  role="button" data-bs-toggle="modal" isSendEmail=' + data.isSendEmail + '  isSendSMS=' + data.isSendSMS + ' isAlertActive=' + data.isAlertActive + ' alertfrequency="' + data.alertFrequency + '" alerttime="' + data.alertSendTime + '" alertPriorty="' + data.alertPriority + '"  alertMessage="' + data.alertMessage + '" alertName="' + data.alertName + '" alertId="' + data.alertId + '" smsmessage="' + data.smSMessage + '" onclick="editalert(this)"  title="Edit" editId="' + data.id + '"><i class="cp-edit"></i></span><span   role="button" data-bs-toggle="modal"onclick="delete_id(this)" deletename="' + data.alertName + '" deleteId="' + data.id + '" title="Delete" deleteId="' + data.id + '"data-bs-target="#DeleteModal"><i class="cp-Delete"></i></span> <span  role="button" data-bs-toggle="modal" data-bs-target="#ViewHistory" isSendEmail1=' + data.isSendEmail + '  isSendSMS1=' + data.isSendSMS + '  alertfrequency1="' + data.alertFrequency + '" alerttime1="' + data.alertSendTime + '" alertPriorty1="' + data.alertPriority + '"  alertMessage1="' + data.alertMessage + '" alertName1="' + data.alertName + '" alertId1="' + data.alertId + '" onclick="viewhistory(this)" title="Schedule"><i class="cp-time "></i></span></div></td>';
                        }
                    }, "orderable": false,
                },
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    $('#search-inp').on('keydown input', managealertdebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
            const inputValue = $('#search-inp').val();
            selectedValues.push(inputValue);
            var currentPage = dataTable.page.info().page + 1;
            if (!isNaN(currentPage)) {
                dataTable.ajax.reload(function (json) {

                    if (e.target.value && json.recordsFiltered === 0) {
                        $('.dataTables_empty').text('No matching records found');
                    }
                }, false)
            }
    }, 500));
    $("#Alert_Priority,#Infraobject").on("change", function () {
        dataTable.ajax.reload()
    })
  })
$('#alertmaster_tabledata').on('click', '.escmatlist',async function () {
    var alertId = $(this).data('esc-id');
    var ackchk = $(this).data('esc-ack');
    sessionStorage.setItem('_alertId', alertId);
    var _esc_maatId = $(this).data('esc-matidd');
    var postactionurl = "Alert/AlertDashboard/GetEsclationMatrixList";
    levListParent.innerHTML = '';
    if (ackchk) {
       await $.ajax({
            type: "POST",
            url: RootUrl + postactionurl,
            dataType: "json",
            async: false,
            success: function (response) {
                if (_esc_maatId !== "0" || _esc_maatId !== "undefined") {
                    let _esctma = _esc_maatId.split('$');
                    var ddlCustomers = $("#escmatrixlst");
                    ddlCustomers.empty();
                    ddlCustomers.append(`<option id="0" value="0" >Select Escalation Matrix</option>`);
                    for (var i = 0; i < response.data.length; i++) {

                        var escmatname = response.data[i].escMatCode + " " + response.data[i].escMatName
                        if (_esctma[0] == response.data[i].id)
                            ddlCustomers.append(`<option id="${response.data[i].id}" value="${response.data[i].id}" selected >${escmatname}</option>`);
                        else
                            ddlCustomers.append(`<option id="${response.data[i].id}" value="${response.data[i].id}" >${escmatname}</option>`);
                    }
                    $("#numId").val(_esctma[1]);
                    $("#tmdy").val(_esctma[2]);
                    var _postacturl = "Alert/AlertDashboard/GetEsclationMatrixLevelBYMatrixID";
                  $.ajax({
                        type: "POST",
                        url: RootUrl + _postacturl,
                        data: {
                            "EscMatID": _esctma[0]
                        },
                        dataType: "json",
                        async: false,
                        success: function (response) {
                            GenerateEscalationMatrixleveTree(response.data, _esctma[0]);
                        },
                        error: function (response) {
                            errorNotification(response)
                        }
                    });
                }
            },
            error: function (response) {
                errorNotification(response)
            }
        });
    } else {
       await $.ajax({
            type: "POST",
            url: RootUrl + postactionurl,
            dataType: "json",
            async: false,
            success: function (response) {
                var ddlCustomers = $("#escmatrixlst");
                ddlCustomers.empty();
                ddlCustomers.append(`<option id="0" value="0" >Select Escalation Matrix</option>`);
                for (var i = 0; i < response.data.length; i++) {
                    var escmatname = response.data[i].escMatCode + " " + response.data[i].escMatName
                    ddlCustomers.append(`<option id="${response.data[i].id}" value="${response.data[i].id}" >${escmatname}</option>`);
                }
            },
            error: function (response) {
                errorNotification(response)
            }
        });
    }
});
$('#alertmaster_tabledata').on('click', '.attachCheckbox', function () {
    if ($("#attbtn" + this.id).attr("disabled")) {
        $("#attbtn" + this.id).removeAttr("disabled");
    } else {
        $("#attbtn" + this.id).attr("disabled", "true");
    }
});
$('#escmatrixlst').on('change',async function () {
    var escmatID = $("#escmatrixlst").val();
    var postactionurl = "Alert/AlertDashboard/GetEsclationMatrixLevelBYMatrixID";
   await $.ajax({
        type: "POST",
        url: RootUrl + postactionurl,
        data: {
            "EscMatID": escmatID
        },
        dataType: "json",
        async: false,
        success: function (response) {
            GenerateEscalationMatrixleveTree(response.data, escmatID);
        },
        error: function (response) {
            errorNotification(response)
        }
    });
});

$('#btnClick').on('click',async function () {
    var escmatID = $("#escmatrixlst").val();
    var nmID = $("#numId").val();
    var tmtID = $("#tmdy").val();
    let alertId = "";
    if (sessionStorage.getItem('_alertId') !== undefined) {
        alertId = sessionStorage.getItem('_alertId')
    }
    var escmatId = escmatID + "$" + nmID + "$" + tmtID + "#" + alertId;
    $('#textmatidlId').val(escmatId);
   await $.ajax({
        type: "POST",
        url: "/Alert/AlertDashboard/UpdateAlertMaster",
        data: {
            "EscMatID": escmatId
        },
        dataType: "json",
        async: false,
        success: function (response) {
            window.location.href = "/Alert/ManageAlert/List";
        },
        error: function (response) {
            errorNotification(response)
        }
    });
});
async function GenerateEscalationMatrixleveTree(treeData, matrixID) {
    let Matrixowner = " ";
    var postactionurl = "Alert/AlertDashboard/GetEsclationMatrixOwnerBYMatrixID";
   await $.ajax({
        type: "POST",
        url: RootUrl + postactionurl,
        data: {
            "EscOwnerMatID": matrixID
        },
        dataType: "json",
        async: false,
        success: function (response) {
            Matrixowner = response;
        },
        error: function (response) {
            errorNotification(response)
        }
    });
    let j = 0;
    var BorderColors = ['border-primary', 'border-success', 'border-warning', 'border-danger'];
    var circle = ['bg-primary', 'bg-success', 'bg-warning', 'bg-danger'];
    let output = " ";

    for (let i = 0; i < treeData.length; i++) {
        if (treeData[i] != "undefined") {
            var _time = `"Will Escalate After:- ${treeData[i].escalationTime} ${treeData[i].escalationTimeUnit}`;
            var _matrixowner = `Matrix Owner:- ${Matrixowner}"`;
            var _tit = _time + "\n" + _matrixowner;
            output += `<li class="li"><div class="Escalation_Timeline_Card card ${BorderColors[j]}"><div class="d-flex align-items-center"><span class="Timeline_Card_Level ${circle[j]} badge bg-primary"> Level ${i + 1} </span><div class="d-grid ms-3"><p class="mb-1 text-truncate" title=${_tit}> ${treeData[i].escLevName} </p></div></div></div></li>`;
            j++;
            if (j > 3) { j = 0; }
        }
    }
    levListParent.innerHTML = output;
}
//delete
function delete_id(data) {
    DeleteId = $(data).attr('deleteId')
    $("#Data_id").text($(data).attr('deletename')).attr("title", $(data).attr('deletename'))
}
$("#confirmdelete").on("click",async function () {
    await $.ajax({
        type: "GET",
        url: RootUrl + "Alert/ManageAlert/Delete_id",
        dataType: "json",
        data: { id: DeleteId },
        success: function (result) {
            let data = result.data
            if (result.success) {
                notificationAlert("success", data.message)
                $("#DeleteModal").modal("hide")
                dataTable.ajax.reload()
            } else {
                errorNotification(result)
            }
        },
        error: function (response) {
            errorNotification(response)
        }
    })
})
$(".manage_btn-cancel").on("click", function () {
    ClearFiaTemplateErrorElements()
})
function ClearFiaTemplateErrorElements() {
    $("#alert_id_error,#message_error,#alert_name_error,#alert_message_error,#alert_Priority_error,#alert_count_error,#alert_time_error").text('').removeClass('field-validation-error');
}
$('#alert_name').on('keypress', async function (event) {
    const value = $('#alert_name').val();
    if (value.length > 99) {
        event.preventDefault()
        event.stopPropagation()
    }
})
if ($("#savebtn").text() != "Update") {
    $('#alert_id').on('keypress', function (event) {
        const alert_id = $('#alert_id').val();
        if (alert_id.length > 4) {
            event.preventDefault()
            event.stopPropagation()
        }
        ["e", "E", "+", "-", "."].includes(event.key) && event.preventDefault()
    })
    $('#alert_message').on('keypress', async function (event) {
        const value = $('#alert_message').val();
        if (value.length > 499) {
            event.preventDefault()
            event.stopPropagation()
        }
    })
    $('#alert_id').on('input', managealertdebounce(async function () {
        let value = await sanitizeInput($('#alert_id').val());
        $("#alert_id").val(value);
        const errorElement = $('#alert_id_error');
        if (value == 0 || value == "-1" || value.length > 5) {
            $('#alert_id').val("")
        }
        if (!value || value.length === 0) {
            validateidDropDown($("#alert_id").val(), "Enter alert ID", errorElement, alertidExistUrl);
        } else {
            validateidDropDown($("#alert_id").val(), "Enter 5 digits only", errorElement, alertidExistUrl);
        }
    }, 400))
    $('#alert_message').on('input', async function () {
        const value = $(this).val();
        const errorElement = $('#alert_message_error');
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        if (!value || value.length === 0) {
            validatemessageDropDown(value, "Enter alert message", errorElement);
        } else {
            const validationResults = [
                //SpecialCharValidate(value),
                value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) :
                    //OnlyNumericsValidate(value),
                    minMaxlength(value, 500),
                ShouldNotBeginWithSpace(value),
                ShouldNotBeginWithNumber(value),
                SpaceWithUnderScore(value),
                ShouldNotEndWithUnderScore(value),
                ShouldNotEndWithSpace(value),
                MultiUnderScoreRegex(value),
                SpaceAndUnderScoreRegex(value),
                secondChar(value),
            ];
            return CommonValidation(errorElement, validationResults);
        }
    })
}
$('#alert_name').on('input', managealertdebounce(async function () {
    let value = await sanitizeInput($('#alert_name').val());
    $("#alert_name").val(value);
    $("#alert_name").val(value);
    if (!value || value.length === 0) {
        validatenameDropDown($("#alert_name").val(), "Enter alert name", $('#alert_name_error'));
    } else {
        validatenameDropDown($("#alert_name").val(), "", $('#alert_name_error'), alertnameExistUrl);
    }
}, 400))

$('#alert_Priority').on('change', function () {
    validateJobDropDown($(this).val(), "Select the alert priority", $('#alert_Priority_error'));
})
$('#alert_count').on('keypress', function (e) {
    if ($(this).val().length >= 2) {
        e.preventDefault()
    }
    ["e", "E", "+", "-","."].includes(e.key) && e.preventDefault()
})
$('#alert_count').on('input', function () {
    const value = $(this).val()
    if (value == "-1" || value > 15 || value == "00") {
        $('#alert_count').val("")
    }
    if (!value || value.length === 0) {
        validatecountDropDown(value, "Enter alert count", $('#alert_count_error'));
    } else {
        validatecountDropDown(value, "Enter the value 1 to 15 ", $('#alert_count_error'));
    }
})
$('#alert_time').on('change Keyup keydown', function () {
    validateJobDropDown($(this).val(), "Select time", $('#alert_time_error'));
})
//validation
function validateJobDropDown(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
async function validatenameDropDown(value, errorMsg, errorElement, url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    var url = RootUrl + url;
    var data = {};
    data.alertId = null;
    data.alertName = value

    const validationResults = [
        SpecialCharValidate(value),
        value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) :
            OnlyNumericsValidate(value),
        minMaxlength(value),
        ShouldNotBeginWithSpace(value),
        ShouldNotBeginWithNumber(value),
        SpaceWithUnderScore(value),
        ShouldNotEndWithUnderScore(value),
        ShouldNotEndWithSpace(value),
        MultiUnderScoreRegex(value),
        SpaceAndUnderScoreRegex(value),
        secondChar(value),
        await IsSameNameExist(url, data)
    ];
    const failedValidations = validationResults.filter(result => result !== true);

    if (failedValidations.length > 0) {
        errorElement.text(failedValidations[0]).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
async function IsSameNameExist(url, inputValue) {
    return !inputValue.alertName.trim() || $("#savebtn").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Alert name already exists" : true;
}
function validatemessageDropDown(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg);
        errorElement.addClass('field-validation-error');
        return false;
    } else if (value.length) {
        const validationResults = [
            //SpecialCharValidate(value),
            value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) :
                //OnlyNumericsValidate(value),
                minMaxlength(value, 500),
            ShouldNotBeginWithSpace(value),
            ShouldNotBeginWithNumber(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            secondChar(value),
        ];
        return CommonValidation(errorElement, validationResults);
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
//Manage Alert Report Download
$('#BtnManageAlertDownload').on('click', async function () {
    try {
        $("#BtnManageAlertDownload").addClass("disabled");

       await $.ajax({
            url: "/Alert/ManageAlert/LoadReport",
            type: "GET",
            xhrFields: {
                responseType: 'blob'
            },
            data: reportQuery,
            success: function (blob) {
                var alertClass, iconClass, message;
                if (blob.size > 0) {
                    const DateTime = new Date().toLocaleString('en-US', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        fractionalSecondDigits: 3,
                        hour12: false
                    }).replace(/[^0-9]/g, '');

                    // Trigger file download
                    downloadManageAlert(blob, "ManageAlertReport_" + DateTime + ".pdf", "application/pdf");

                    alertClass = "success-toast";
                    iconClass = "cp-check toast_icon";
                    message = "Manage Alert report downloaded successfully";
                } else {
                    alertClass = "warning-toast";
                    iconClass = "cp-exclamation toast_icon";
                    message = "Manage AlertReport Download Error";
                }
                $("#BtnManageAlertDownload").removeClass("disabled");

                // Show the toast notification
                $('#manageAlertClass').removeClass().addClass(alertClass);
                $('#icon').removeClass().addClass(iconClass);
                $('#notificationAlertmessage').text(message);
                $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
            },
            error: function (xhr, status, error) {
                $("#BtnManageAlertDownload").removeClass("disabled");
                // Show error toast
                $('#alertClass').removeClass().addClass("warning-toast");
                $('#icon').removeClass().addClass("cp-exclamation toast_icon");
                $('#notificationAlertmessage').text("Manage Alert Report Download Error");
                $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
            }
        });
    } catch (error) {
        $("#BtnManageAlertDownload").removeClass("disabled");
    }
});
function downloadManageAlert(blob, fileName, contentType) {
    try {
        const link = document.createElement("a");
        link.download = fileName;
        link.href = window.URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error("Error downloading file: " + error.message);
    }
}
async function validateidDropDown(value, errorMsg, errorElement, url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else if (value.length < 5) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    }
    var url = RootUrl + url;
    var data = {};
    data.alertId = value;

    const validationResults = [
        await IsSameIdExist(url, data)
    ];

    const failedValidations = validationResults.filter(result => result !== true);

    if (failedValidations.length > 0) {
        errorElement.text(failedValidations[0]).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
async function IsSameIdExist(url, inputValue) {
    return !inputValue.alertId.trim() ? true : (await GetAsync(url, inputValue, OnError)) ? "Alert ID already exists" : true;
}
function validatecountDropDown(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else if (value == 0) {
        errorElement.text("Enter value more than 0").addClass('field-validation-error');
        return false;
    } else if (value < 1 || value > 15) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
$("#manage_create_Btn").on("click", function () {
    $("[name=AlertId]").removeAttr("readonly");
    $("[name=AlertMessage]").removeAttr("readonly");
    $('#savebtn').text('Save');
    $("#alert_id,#alert_name,#alert_message,#alert_Priority,#alert_count,#alert_time,#sms_message").val("")
    $("#alert_sms,#alert_email,#alert_active").prop("checked", false);
    $("#alert_sms").prop("disabled", false);
    $("#smsMessage").hide()
    $('#message_error').removeClass('field-validation-error'); 
})
$('#sms_message').on('keypress', async function (event) {
    const value = $('#sms_message').val();
    if (value.length > 499) {
        event.preventDefault()
        event.stopPropagation()
    }
})
$("#alert_sms").on("click", function () {
    if ($(this).prop("checked") == true) {
        $("#smsMessage").show()
    } else {
        $("#smsMessage").hide()
        $('#message_error').text('').removeClass('field-validation-error');
    }
})
$('#sms_message').on('input', async function () {
    const value = $(this).val();
    const errorElement = $('#message_error');
    let sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    if (!value || value.length === 0) {
        validatemessageDropDown(value, "Enter message", errorElement);
    } else {
        const validationResults = [
            //SpecialCharValidate(value),
            value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) :
                //OnlyNumericsValidate(value),
                minMaxlength(value, 500),
            ShouldNotBeginWithSpace(value),
            ShouldNotBeginWithNumber(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            secondChar(value),
        ];
        return CommonValidation(errorElement, validationResults);
    }
})

function editalert(data) {
    $("[name=AlertId]").attr("readonly", "readonly").css({
        "opacity": 1,
    });
    $("[name=AlertMessage]").attr("readonly", "readonly").css({
        "opacity": 1,
    });
    globalAlertId = $(data).attr('editId');
    $('#savebtn').text('Update');
    $('#CreateModal').modal('show');
    $("#alert_id").val($(data).attr('alertId'))
    $("#alert_name").val($(data).attr('alertName'))
    $("#alert_message").val($(data).attr('alertMessage'))
    $("#alert_Priority").val($(data).attr('alertPriorty'))
    $("#alert_count").val($(data).attr('alertfrequency'))
    let alerttime = $(data).attr('alerttime').split(":")
    $("#alert_time").val(alerttime.length == 2 ? (alerttime[0] + ":" + alerttime[1]) : ("00" + ":" + "00"))
    let sms = $(data).attr('isSendSMS') == "true" ? true : false
    let email = $(data).attr('isSendEmail') == "true" ? true : false
    let isactive = $(data).attr('isAlertActive') == "true" ? true : false
    let smscheck = $(data).attr('smsmessage')
    if (sms == true && (smscheck != null)) {
        $("#smsMessage").show()
        $("#sms_message").val(smscheck)
        $("#alert_sms").prop("checked", true);
        $("#alert_sms").prop("disabled", true);
    } else {
        $("#smsMessage").hide()
        $("#sms_message").val("")
        $("#alert_sms").prop("checked", false);
        $("#alert_sms").prop("disabled", true);
    }
    $("#alert_email").prop("checked", email);
    $("#alert_active").prop("checked", isactive);
}
function viewhistory(data) {
    $("#modaltablebody").empty()
    var iconClass = '';
    if ($(data).attr('alertPriorty1') == "High") {
        iconClass = "fw-bold cp-up-doublearrow text-warning";
    } else if ($(data).attr('alertPriorty1') == "Critical") {
        iconClass = "fw-bold cp-critical-level text-danger";
    } else if ($(data).attr('alertPriorty1') == "Normal") {
        iconClass = "fw-bold cp-equal text-info";
    } else if ($(data).attr('alertPriorty1') == "Information") {
        iconClass = "cp-warning text-primary";
    } else if ($(data).attr('alertPriorty1') == "Low") {
        iconClass = "fw-bold cp-down-doublearrow text-success";
    }
    let alerttime = $(data).attr('alerttime1') == 'null' ? null : $(data).attr('alerttime1')
    let sms = $(data).attr('isSendSMS1') == "true" ? true : false
    let email = $(data).attr('isSendEmail1') == "true" ? true : false
    var datas = ""
    datas += '<tr>'
    datas += '<td>' + $(data).attr('alertId1') + '</td>'
    datas += '<td title="' + $(data).attr('alertName1') + '"><span class="d-inline-block text-truncate" style="max-width:60%">' + $(data).attr('alertName1') + ' </span ></td >'
    datas += '<td title="' + $(data).attr('alertMessage1') + '"><span class="d-inline-block text-truncate" style="max-width:70%">' + $(data).attr('alertMessage1') + '</span></td>'
    datas += '<td><span class="text-danger me-1"><i class="' + iconClass + '"></i></span><span>' + $(data).attr('alertPriorty1') + '</span></td>'
    datas += '<td>' + $(data).attr('alertfrequency1') + '</td>'
    datas += alerttime == null ? '<td>' + "NA" + '</td>' : '<td>' + $(data).attr('alerttime1') + '</td>'
    datas += sms == true ? '<td class="text-truncate" ><div><input aria-label="option 1" type="checkbox" disabled class="form-check-input" checked cursorshover="true"></div></td>' : '<td class="text-truncate" ><div ><input aria-label="option 1" type="checkbox" disabled class="form-check-input"  cursorshover="true"></div></td>'
    datas += email == true ? '<td class="text-truncate" ><div><input aria-label="option 1" type="checkbox"  disabled class="form-check-input" checked cursorshover="true"></div></td>' : '<td class="text-truncate" ><div ><input aria-label="option 1" type="checkbox" class="form-check-input" disabled  cursorshover="true"></div></td>'
    datas += '</tr>'

    $("#modaltablebody").append(datas)
}
let btndisabled = false
$("#savebtn").on("click", async function () {
    let form = $("#CreateForm")
    let isalertid
    let isalertmessage;
    let issmsmessage;
    if ($("#savebtn").text() != "Update") {
        elementValue = $("#alert_id").val();
        errorElement = $('#alert_id_error');
        if (!elementValue || elementValue.length === 0) {
            isalertid = await validateidDropDown(elementValue, "Enter alert ID", errorElement, alertidExistUrl);
        } else {
            isalertid = await validateidDropDown(elementValue, "Enter 5 digits only", errorElement, alertidExistUrl);
        }

        elementValue = $("#alert_message").val();
        errorElement = $('#alert_message_error');
        if (!elementValue || elementValue.length === 0) {
            isalertmessage = validatemessageDropDown(elementValue, "Enter alert message", errorElement);
        } else {
            isalertmessage = validatemessageDropDown(elementValue, "Between 3 to 500 characters", errorElement);
        }
        if ($("#alert_sms").prop("checked") == true) {
            elementValue = $("#sms_message").val();
            errorElement = $('#message_error');
            if (!elementValue || elementValue.length === 0) {
                issmsmessage = validatemessageDropDown(elementValue, "Enter message", errorElement);
            } else {
                issmsmessage = validatemessageDropDown(elementValue, "Between 3 to 500 characters", errorElement);
            }
        } else {
            issmsmessage = true
        }
    }
    let isalertname
    elementValue = $("#alert_name").val();
    errorElement = $('#alert_name_error');
    if (!elementValue || elementValue.length === 0) {
        isalertname = await validatenameDropDown(elementValue, "Enter alert name", errorElement);
    } else {
        isalertname = await validatenameDropDown(elementValue, "Between 3 to 100 characters", errorElement, alertnameExistUrl);
    }

    elementValue = $("#alert_Priority").val();
    errorElement = $('#alert_Priority_error');
    let isalertPriority = validateJobDropDown(elementValue, "Select alert priority", errorElement);

    let isalertcount
    elementValue = $("#alert_count").val();
    errorElement = $('#alert_count_error');
    if (!elementValue || elementValue.length === 0) {
        isalertcount = validatecountDropDown(elementValue, "Enter alert count", errorElement);
    } else {
        isalertcount = validatecountDropDown(elementValue, "Enter the value 1 to 15 ", errorElement);
    }

    let isalerttime
    elementValue = $("#alert_time").val();
    errorElement = $('#alert_time_error');
    isalerttime = validateJobDropDown(elementValue, "Select time", errorElement);

    if ((isalertid === undefined ? true : isalertid) && (issmsmessage == undefined ? true : issmsmessage) && isalertname && (isalertmessage == undefined ? true : isalertmessage) && isalertPriority && isalertcount && isalerttime) {
        if (!btndisabled) {
            btndisabled = true
            form.trigger("submit")
            var data = {
                "AlertId": $("#alert_id").val(),
                "AlertName": $("#alert_name").val(),
                "AlertMessage": $("#alert_message").val(),
                "AlertPriority": $("#alert_Priority").val(),
                "AlertFrequency": $("#alert_count").val(),
                "AlertSendTime": $("#alert_time").val(),
                "IsSendEmail": $("input[name=isSendEmail]").prop("checked"),
                "SmSMessage": $("#sms_message").val(),
                "IsSendSMS": $("input[name=isSendSMS]").prop("checked"),
                "IsAlertActive": $("input[name=isAlertActive]").prop("checked"), __RequestVerificationToken: gettoken()
            }
            $('#savebtn').text() === "Update" ? data["id"] = globalAlertId : null
            await $.ajax({
                type: "POST",
                url: RootUrl + "Alert/ManageAlert/CreateOrUpdate",
                dataType: "json",
                data: data,
                success: function (result) {
                    let data = result.data
                    if (result.success) {
                        notificationAlert("success", data.message)
                        $('#CreateModal').modal('hide');
                        setTimeout(() => {
                            btndisabled = false
                            dataTable.ajax.reload()
                        }, 2000)
                    } else {
                        errorNotification(result)
                    }
                },
                error: function (response) {
                    errorNotification(response)
                }
            })
        }
    }
})

