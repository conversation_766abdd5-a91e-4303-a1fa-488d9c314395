﻿using Moq;
using Microsoft.Extensions.Logging;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Domain.ViewModels.FormModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Application.Features.Form.Commands.Create;
using ContinuityPatrol.Application.Features.Form.Commands.Update;
using ContinuityPatrol.Application.Features.Form.Commands.Publish;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Application.Features.Form.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Tests.Fakes;
using MediatR;
using ContinuityPatrol.Application.Features.Form.Queries.GetDetail;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using AutoFixture;
using ContinuityPatrol.Application.Features.Form.Queries.GetType;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class FormBuilderControllerShould
    {
        private readonly Mock<IFormService> _mockFormService =new();
        private readonly Mock<ILogger<FormBuilderController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private  FormBuilderController _controller ;

        public FormBuilderControllerShould()
        {
            Initialize();

        }
        internal void Initialize()
        {
            _controller = new FormBuilderController(
                _mockPublisher.Object,
                //_mockFormService.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public async Task List_ReturnsViewWithFormViewModel()
        {
            var formlist = new List<FormListVm>();
            _mockDataProvider.Setup(x => x.Form.GetFormList()).ReturnsAsync(formlist);
            var result = await _controller.List() as ViewResult;
            var model = result?.Model as FormViewModel;

            
            Assert.NotNull(result);
            Assert.NotNull(model);
        }

        [Fact]
        public async Task GetNames_ReturnsJsonResultWithSuccess()
        {
            _mockFormService.Setup(p => p.GetFormNames()).ReturnsAsync(It.IsAny<List<FormNameVm>>);
            var result = await _controller.GetNames() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }
        
        [Fact]
        public async Task CreateOrUpdate_CreatesFormSuccessfully()
        {
            
            var form = new AutoFixture.Fixture().Create<FormViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateFormCommand ();

            _mockMapper.Setup(mapper => mapper.Map<CreateFormCommand>(It.IsAny<FormViewModel>())).Returns(command);
            _mockFormService.Setup(service => service.CreateAsync(command)).ReturnsAsync(new BaseResponse { Success = true, Message = "Created" });

            
            var result = await _controller.CreateOrUpdate(form) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesFormSuccessfully()
        {
            
            var form = new AutoFixture.Fixture().Create<FormViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateFormCommand ();
            _mockMapper.Setup(mapper => mapper.Map<UpdateFormCommand>(It.IsAny<FormViewModel>())).Returns(command);
            _mockFormService.Setup(service => service.UpdateAsync(command)).ReturnsAsync(new BaseResponse { Success = true, Message = "Updated" });

            
            var result = await _controller.CreateOrUpdate(form) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task IsPublish_PublishesFormSuccessfully()
        {
            
            var command = new UpdateFormPublishCommand { Id = ""};
            _mockMapper.Setup(mapper => mapper.Map<UpdateFormPublishCommand>(It.IsAny<UpdateFormPublishCommand>())).Returns(command);
            _mockFormService.Setup(service => service.Publish(command)).ReturnsAsync(new BaseResponse { Success = true, Message = "Published" });

            
            var result = await _controller.IsPublish(command) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task Delete_DeletesFormSuccessfully()
        {
            
            var id = "someId";
            _mockDataProvider.Setup(provider => provider.Form.DeleteAsync(id)).ReturnsAsync(new BaseResponse { Success = true, Message = "Deleted" });

            
            var result = await _controller.Delete(id) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task GetFormById_ReturnsJsonResultWithForm()
        {
            
            var id = "someId";
            _mockFormService.Setup(m => m.GetByReferenceId(id)).ReturnsAsync(It.IsAny<FormDetailVm>);
            var result = await _controller.GetFormById(id) as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task GetForms_ReturnsJsonResultWithFormList()
        {
            var type = "someType";

            _mockDataProvider.Setup(p => p.Form.GetFormByType(type)).ReturnsAsync(It.IsAny<List<FormTypeVm>>);
            
            var result = await _controller.GetForms(type) as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
            
            
        }

        [Fact]
        public async Task FormNameExist_ReturnsTrueIfNameExists()
        {
            
            var formName = "formName";
            var id = "someId";
            _mockDataProvider.Setup(p => p.Form.IsFormNameExist(formName, id)).ReturnsAsync(It.IsAny<bool>);


            var result = await _controller.FormNameExist(formName, id);

            
            Assert.NotNull(result.ToString());
        }

        [Fact]
        public async Task GetFormPagination_ReturnsJsonResultWithPaginatedFormList()
        {
            
            var query = new GetFormPaginatedListQuery { Type=""};
            var paginatedList = new PaginatedResult<FormListVm> ();
            _mockDataProvider.Setup(provider => provider.Form.GetPaginatedForms(query)).ReturnsAsync(paginatedList);

            
            var result = await _controller.GetFormPagination(query) as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
            
        }
    }
}
