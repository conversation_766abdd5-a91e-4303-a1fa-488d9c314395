using AutoFixture;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftCategoryList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftOperationSummary;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftResourceSummary;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetConflictList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetDriftTreeView;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetResourceStatus;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.DriftManagementMonitorStatusModel;
using ContinuityPatrol.Domain.ViewModels.DriftResourceSummaryModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DriftManagementMonitorStatusFixture : IDisposable
{
    public List<DriftManagementMonitorStatusListVm> DriftManagementMonitorStatusListVm { get; set; }
    public List<DriftManagementMonitorStatus> DriftManagementMonitorStatuses { get; set; }
    public DriftManagementMonitorStatusDetailVm DriftManagementMonitorStatusDetailVm { get; set; }
    public CreateDriftManagementMonitorStatusCommand CreateDriftManagementMonitorStatusCommand { get; set; }
    public CreateDriftManagementMonitorStatusResponse CreateDriftManagementMonitorStatusResponse { get; set; }
    public UpdateDriftManagementMonitorStatusCommand UpdateDriftManagementMonitorStatusCommand { get; set; }
    public UpdateDriftManagementMonitorStatusResponse UpdateDriftManagementMonitorStatusResponse { get; set; }
    public DeleteDriftManagementMonitorStatusCommand DeleteDriftManagementMonitorStatusCommand { get; set; }
    public DeleteDriftManagementMonitorStatusResponse DeleteDriftManagementMonitorStatusResponse { get; set; }
    public GetDriftManagementMonitorStatusPaginatedListQuery GetDriftManagementMonitorStatusPaginatedListQuery { get; set; }
    public PaginatedResult<DriftManagementMonitorStatusListVm> DriftManagementMonitorStatusPaginatedResult { get; set; }

    // Missing query objects and view models
    public GetByInfraObjectIdQuery GetByInfraObjectIdQuery { get; set; }
    public GetDriftTreeListQuery GetDriftTreeListQuery { get; set; }
    public List<GetDriftTreeListVm> GetDriftTreeListVm { get; set; }
    public DriftOperationSummaryQuery DriftOperationSummaryQuery { get; set; }
    public DriftOperationSummaryVm DriftOperationSummaryVm { get; set; }
    public GetDriftResourceSummaryQuery GetDriftResourceSummaryQuery { get; set; }
    public List<DriftResourceSummaryListVm> DriftResourceSummaryListVm { get; set; }
    public DriftCategoryListQuery DriftCategoryListQuery { get; set; }
    public DriftCategoryListVm DriftCategoryListVm { get; set; }
    public GetConflictListQuery GetConflictListQuery { get; set; }
    public List<ConflictListVm> ConflictListVm { get; set; }
    public DriftDashboardResourceStatusQuery DriftDashboardResourceStatusQuery { get; set; }
    public DriftDashboardResourceStatusVm DriftDashboardResourceStatusVm { get; set; }
    public GetDriftManagementMonitorStatusNameUniqueQuery GetDriftManagementMonitorStatusNameUniqueQuery { get; set; }

    public DriftManagementMonitorStatusFixture()
    {
        DriftManagementMonitorStatusListVm = AutoDriftManagementMonitorStatusFixture.Create<List<DriftManagementMonitorStatusListVm>>();
        DriftManagementMonitorStatusDetailVm = AutoDriftManagementMonitorStatusFixture.Create<DriftManagementMonitorStatusDetailVm>();
        CreateDriftManagementMonitorStatusCommand = AutoDriftManagementMonitorStatusFixture.Create<CreateDriftManagementMonitorStatusCommand>();
        CreateDriftManagementMonitorStatusResponse = AutoDriftManagementMonitorStatusFixture.Create<CreateDriftManagementMonitorStatusResponse>();
        UpdateDriftManagementMonitorStatusCommand = AutoDriftManagementMonitorStatusFixture.Create<UpdateDriftManagementMonitorStatusCommand>();
        UpdateDriftManagementMonitorStatusResponse = AutoDriftManagementMonitorStatusFixture.Create<UpdateDriftManagementMonitorStatusResponse>();
        DeleteDriftManagementMonitorStatusCommand = AutoDriftManagementMonitorStatusFixture.Create<DeleteDriftManagementMonitorStatusCommand>();
        DeleteDriftManagementMonitorStatusResponse = AutoDriftManagementMonitorStatusFixture.Create<DeleteDriftManagementMonitorStatusResponse>();
        DriftManagementMonitorStatuses = AutoDriftManagementMonitorStatusFixture.Create<List<DriftManagementMonitorStatus>>();
        GetDriftManagementMonitorStatusPaginatedListQuery = AutoDriftManagementMonitorStatusFixture.Create<GetDriftManagementMonitorStatusPaginatedListQuery>();
        DriftManagementMonitorStatusPaginatedResult = AutoDriftManagementMonitorStatusFixture.Create<PaginatedResult<DriftManagementMonitorStatusListVm>>();

        // Initialize missing query objects and view models
        GetByInfraObjectIdQuery = AutoDriftManagementMonitorStatusFixture.Create<GetByInfraObjectIdQuery>();
        GetDriftTreeListQuery = AutoDriftManagementMonitorStatusFixture.Create<GetDriftTreeListQuery>();
        GetDriftTreeListVm = AutoDriftManagementMonitorStatusFixture.Create<List<GetDriftTreeListVm>>();
        DriftOperationSummaryQuery = AutoDriftManagementMonitorStatusFixture.Create<DriftOperationSummaryQuery>();
        DriftOperationSummaryVm = AutoDriftManagementMonitorStatusFixture.Create<DriftOperationSummaryVm>();
        GetDriftResourceSummaryQuery = AutoDriftManagementMonitorStatusFixture.Create<GetDriftResourceSummaryQuery>();
        DriftResourceSummaryListVm = AutoDriftManagementMonitorStatusFixture.Create<List<DriftResourceSummaryListVm>>();
        DriftCategoryListQuery = AutoDriftManagementMonitorStatusFixture.Create<DriftCategoryListQuery>();
        DriftCategoryListVm = AutoDriftManagementMonitorStatusFixture.Create<DriftCategoryListVm>();
        GetConflictListQuery = AutoDriftManagementMonitorStatusFixture.Create<GetConflictListQuery>();
        ConflictListVm = AutoDriftManagementMonitorStatusFixture.Create<List<ConflictListVm>>();
        DriftDashboardResourceStatusQuery = AutoDriftManagementMonitorStatusFixture.Create<DriftDashboardResourceStatusQuery>();
        DriftDashboardResourceStatusVm = AutoDriftManagementMonitorStatusFixture.Create<DriftDashboardResourceStatusVm>();
        GetDriftManagementMonitorStatusNameUniqueQuery = AutoDriftManagementMonitorStatusFixture.Create<GetDriftManagementMonitorStatusNameUniqueQuery>();
    }

    public Fixture AutoDriftManagementMonitorStatusFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customize<CreateDriftManagementMonitorStatusCommand>(c => c.With(b => b.InfraObjectId, Guid.NewGuid().ToString));

            fixture.Customize<UpdateDriftManagementMonitorStatusCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<UpdateDriftManagementMonitorStatusCommand>(c => c.With(b => b.InfraObjectId, Guid.NewGuid().ToString));

            fixture.Customize<DeleteDriftManagementMonitorStatusCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString));

            fixture.Customize<CreateDriftManagementMonitorStatusResponse>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<CreateDriftManagementMonitorStatusResponse>(c => c.With(b => b.Success, true));

            fixture.Customize<UpdateDriftManagementMonitorStatusResponse>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<UpdateDriftManagementMonitorStatusResponse>(c => c.With(b => b.Success, true));

            fixture.Customize<DeleteDriftManagementMonitorStatusResponse>(c => c.With(b => b.Success, true));

            fixture.Customize<DriftManagementMonitorStatus>(c => c.With(b => b.IsActive, true));
            fixture.Customize<DriftManagementMonitorStatus>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString));

            fixture.Customize<DriftManagementMonitorStatusListVm>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<DriftManagementMonitorStatusListVm>(c => c.With(b => b.InfraObjectId, Guid.NewGuid().ToString));

            fixture.Customize<DriftManagementMonitorStatusDetailVm>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<DriftManagementMonitorStatusDetailVm>(c => c.With(b => b.InfraObjectId, Guid.NewGuid().ToString));

            fixture.Customize<GetDriftManagementMonitorStatusPaginatedListQuery>(c => c.With(b => b.PageNumber, 1));
            fixture.Customize<GetDriftManagementMonitorStatusPaginatedListQuery>(c => c.With(b => b.PageSize, 10));

            fixture.Customize<PaginatedResult<DriftManagementMonitorStatusListVm>>(c => c.With(b => b.Succeeded, true));
            fixture.Customize<PaginatedResult<DriftManagementMonitorStatusListVm>>(c => c.With(b => b.PageSize, 10));
            fixture.Customize<PaginatedResult<DriftManagementMonitorStatusListVm>>(c => c.With(b => b.CurrentPage, 1));

            // Customize missing query objects and view models
            fixture.Customize<GetByInfraObjectIdQuery>(c => c.With(b => b.InfraObjectId, Guid.NewGuid().ToString()));

            fixture.Customize<GetDriftTreeListVm>(c => c
                .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
                .With(b => b.BusinessServiceName, "Enterprise Business Service"));

            fixture.Customize<DriftOperationSummaryVm>(c => c
                .With(b => b.TotalBusinessServiceCount, 150)
                .With(b => b.TotalConflictCount, 25)
                .With(b => b.TotalNonConflictCount, 125));

            fixture.Customize<DriftResourceSummaryListVm>(c => c
                .With(b => b.InfraObjectId, Guid.NewGuid().ToString())
                .With(b => b.EntityName, "Database")
                .With(b => b.ConflictCount, 5)
                .With(b => b.NonConflictCount, 45));

            fixture.Customize<DriftCategoryListVm>(c => c
                .With(b => b.CountMismatch, 25)
                .With(b => b.VersionMismatch, 15)
                .With(b => b.ConfigMismatch, 10)
                .With(b => b.PolicyMismatch, 8));

            fixture.Customize<ConflictListVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.InfraObjectId, Guid.NewGuid().ToString())
                .With(b => b.InfraObjectName, "Enterprise Database Server")
                .With(b => b.Status, "Conflict"));

            fixture.Customize<DriftDashboardResourceStatusVm>(c => c
                .With(b => b.TotalResourceCount, 200)
                .With(b => b.DriftEnabledCount, 150)
                .With(b => b.ConflictedCount, 25)
                .With(b => b.NonConflictedCount, 125));

            fixture.Customize<GetDriftManagementMonitorStatusNameUniqueQuery>(c => c
                .With(b => b.Name, "Enterprise Drift Management Monitor Status")
                .With(b => b.Id, Guid.NewGuid().ToString()));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}
