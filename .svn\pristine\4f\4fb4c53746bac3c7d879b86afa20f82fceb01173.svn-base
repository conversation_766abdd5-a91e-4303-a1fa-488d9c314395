using ContinuityPatrol.Application.Features.BackUpLog.Commands.Create;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Delete;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Update;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetList;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BackUpLogModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class BackUpLogsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<BackUpLogListVm>>> GetBackUpLogs()
    {
        Logger.LogDebug("Get All BackUpLogs");

        return Ok(await Mediator.Send(new GetBackUpLogListQuery()));
    }

    [HttpGet("{id}", Name = "GetBackUpLog")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<BackUpLogDetailVm>> GetBackUpLogById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BackUpLog Id");

        Logger.LogDebug($"Get BackUpLog Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetBackUpLogDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Admin.View)]
 public async Task<ActionResult<PaginatedResult<BackUpLogListVm>>> GetPaginatedBackUpLogs([FromQuery] GetBackUpLogPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in BackUpLog Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreateBackUpLogResponse>> CreateBackUpLog([FromBody] CreateBackUpLogCommand createBackUpLogCommand)
    {
        Logger.LogDebug($"Create BackUpLog '{createBackUpLogCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateBackUpLog), await Mediator.Send(createBackUpLogCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdateBackUpLogResponse>> UpdateBackUpLog([FromBody] UpdateBackUpLogCommand updateBackUpLogCommand)
    {
        Logger.LogDebug($"Update BackUpLog '{updateBackUpLogCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateBackUpLogCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<ActionResult<DeleteBackUpLogResponse>> DeleteBackUpLog(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BackUpLog Id");

        Logger.LogDebug($"Delete BackUpLog Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteBackUpLogCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsBackUpLogNameExist(string backUpLogName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(backUpLogName, "BackUpLog Name");

     Logger.LogDebug($"Check Name Exists Detail by BackUpLog Name '{backUpLogName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetBackUpLogNameUniqueQuery { Name = backUpLogName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


