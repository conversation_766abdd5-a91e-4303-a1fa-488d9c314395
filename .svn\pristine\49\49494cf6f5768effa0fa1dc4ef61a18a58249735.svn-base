﻿using ContinuityPatrol.Application.Features.WorkflowActionResult.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionResult.Commands;

public class DeleteWorkflowActionResultTests : IClassFixture<WorkflowActionResultFixture>
{
    private readonly WorkflowActionResultFixture _workflowActionResultFixture;

    private readonly Mock<IWorkflowActionResultRepository> _mockWorkflowActionResultRepository;

    private readonly DeleteWorkflowActionResultCommandHandler _handler;

    public DeleteWorkflowActionResultTests(WorkflowActionResultFixture workflowActionResultFixture)
    {
        _workflowActionResultFixture = workflowActionResultFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockWorkflowActionResultRepository = WorkflowActionResultRepositoryMocks.DeleteWorkflowActionResultRepository(_workflowActionResultFixture.WorkflowActionResults);

        _handler = new DeleteWorkflowActionResultCommandHandler(_mockWorkflowActionResultRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_Success_When_Delete_WorkflowActionResult()
    {
        var result = await _handler.Handle(new DeleteWorkflowActionResultCommand { Id = _workflowActionResultFixture.WorkflowActionResults[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteWorkflowActionResultResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Return_IsActive_False_When_DeleteReferenceIdAsync_WorkflowActionResult()
    {
        await _handler.Handle(new DeleteWorkflowActionResultCommand { Id = _workflowActionResultFixture.WorkflowActionResults[0].ReferenceId }, CancellationToken.None);

        var workflowActionResult = await _mockWorkflowActionResultRepository.Object.GetByReferenceIdAsync(_workflowActionResultFixture.WorkflowActionResults[0].ReferenceId);

        workflowActionResult.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_NotFoundException_When_Invalid_WorkflowOperationId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteWorkflowActionResultCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteWorkflowActionResultCommand { Id = _workflowActionResultFixture.WorkflowActionResults[0].ReferenceId }, CancellationToken.None);

        _mockWorkflowActionResultRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockWorkflowActionResultRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.WorkflowActionResult>()), Times.Once);
    }
}