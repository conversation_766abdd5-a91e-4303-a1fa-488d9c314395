﻿namespace ContinuityPatrol.Application.Features.TableAccess.Commands.Create;

public class CreateTableAccessCommandValidator : AbstractValidator<CreateTableAccessCommand>
{
    private readonly ITableAccessRepository _tableAccessRepository;

    public CreateTableAccessCommandValidator(ITableAccessRepository tableAccessRepository)
    {
        _tableAccessRepository = tableAccessRepository;

        RuleFor(p => p.TableName)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        //RuleFor(p => p.SchemaName)
        //    .NotEmpty().WithMessage("{PropertyName} is Required.")
        //    .NotNull()
        //    .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
        //    .WithMessage("Please Enter Valid {PropertyName}.")
        //    .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");


        RuleFor(e => e)
            .MustAsync(TableAccessNameUnique)
            .WithMessage("A same name already exists.");
    }

    private async Task<bool> TableAccessNameUnique(CreateTableAccessCommand e, CancellationToken cancellationToken)
    {
        return !await _tableAccessRepository.IsTableAccessNameUnique(e.TableName);
    }
}