﻿namespace ContinuityPatrol.Application.Features.RiskMitigation.Commands.Create;

public class CreateRiskMitigationCommand : IRequest<CreateRiskMitigationResponse>
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public bool UnderControlRTO { get; set; }
    public bool ExeedRTO { get; set; }
    public bool MaintenanceRTO { get; set; }
    public string RTODescription { get; set; }
    public bool UnderControlRPO { get; set; }
    public bool ExeedRPO { get; set; }
    public bool MaintenanceRPO { get; set; }
    public string RPODescription { get; set; }
    public bool IsAffected { get; set; }
    public string ErrorMessage { get; set; }

    public override string ToString()
    {
        return $"InfraObjectName: {InfraObjectName};";
    }
}