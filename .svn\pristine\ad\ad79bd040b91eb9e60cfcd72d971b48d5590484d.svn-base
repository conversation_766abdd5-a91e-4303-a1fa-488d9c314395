using ContinuityPatrol.Application.Features.BackUpLog.Commands.Create;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Update;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BackUpLogModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class BackUpLogFixture
{
    public List<BackUpLogListVm> BackUpLogListVm { get; }
    public BackUpLogDetailVm BackUpLogDetailVm { get; }
    public CreateBackUpLogCommand CreateBackUpLogCommand { get; }
    public UpdateBackUpLogCommand UpdateBackUpLogCommand { get; }

    public BackUpLogFixture()
    {
        var fixture = new Fixture();

        // Create sample BackUpLog list data
        BackUpLogListVm = new List<BackUpLogListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                HostName = "db-server-01.company.com",
                DatabaseName = "ContinuityPatrolDB",
                UserName = "backup_admin",
                IsLocalServer = true,
                IsBackUpServer = false,
                BackUpPath = "C:\\Backups\\Daily\\20241127_020000.bak",
                Type = "Full",
                Status = "Completed",
                Properties = "{\"size\":\"2.5GB\",\"duration\":\"45minutes\",\"compression\":\"enabled\"}",
                CreatedBy = "system.scheduler",
                CreatedDate = DateTime.Now.AddHours(-2),
                LastModifiedBy = "system.scheduler",
                LastModifiedDate = DateTime.Now.AddHours(-2)
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                HostName = "db-server-02.company.com",
                DatabaseName = "LogsDB",
                UserName = "log_backup",
                IsLocalServer = false,
                IsBackUpServer = true,
                BackUpPath = "\\\\backup-server\\logs\\20241127_060000.bak",
                Type = "Incremental",
                Status = "Failed",
                Properties = "{\"size\":\"0MB\",\"duration\":\"5minutes\",\"error\":\"Network timeout\"}",
                CreatedBy = "system.scheduler",
                CreatedDate = DateTime.Now.AddHours(-6),
                LastModifiedBy = "system.scheduler",
                LastModifiedDate = DateTime.Now.AddHours(-6)
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                HostName = "db-server-03.company.com",
                DatabaseName = "AnalyticsDB",
                UserName = "analytics_backup",
                IsLocalServer = true,
                IsBackUpServer = true,
                BackUpPath = "D:\\Backups\\Analytics\\20241127_000000.bak",
                Type = "Differential",
                Status = "In Progress",
                Properties = "{\"size\":\"1.2GB\",\"duration\":\"ongoing\",\"progress\":\"75%\"}",
                CreatedBy = "system.scheduler",
                CreatedDate = DateTime.Now.AddMinutes(-30),
                LastModifiedBy = "system.scheduler",
                LastModifiedDate = DateTime.Now.AddMinutes(-5)
            }
        };

        // Create detailed BackUpLog data
        BackUpLogDetailVm = new BackUpLogDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            HostName = "enterprise-db.company.com",
            DatabaseName = "EnterpriseDB",
            UserName = "enterprise_backup",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = "\\\\enterprise-backup\\critical\\20241127_010000.bak",
            Type = "Full",
            Status = "Completed",
            Properties = "{\"size\":\"15.7GB\",\"duration\":\"2hours15minutes\",\"compression\":\"enabled\",\"encryption\":\"AES256\",\"verification\":\"passed\"}",
            CreatedBy = "system.scheduler",
            CreatedDate = DateTime.Now.AddHours(-4),
            LastModifiedBy = "system.scheduler",
            LastModifiedDate = DateTime.Now.AddHours(-2)
        };

        // Create command for creating BackUpLog
        CreateBackUpLogCommand = new CreateBackUpLogCommand
        {
            HostName = "new-db-server.company.com",
            DatabaseName = "NewApplicationDB",
            UserName = "new_backup_user",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = "C:\\Backups\\NewApp\\manual_backup.bak",
            Type = "Manual",
            Status = "Initiated"
        };

        // Create command for updating BackUpLog
        UpdateBackUpLogCommand = new UpdateBackUpLogCommand
        {
            Id = Guid.NewGuid().ToString(),
            HostName = "updated-db-server.company.com",
            DatabaseName = "UpdatedApplicationDB",
            UserName = "updated_backup_user",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = "\\\\updated-backup\\app\\updated_backup.bak",
            Type = "Incremental",
            Status = "Completed"
        };
    }
}
