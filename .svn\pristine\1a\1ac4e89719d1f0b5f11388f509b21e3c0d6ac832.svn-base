﻿namespace ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterByAlertId;

public class
    GetAlertMasterByAlertIdQueryHandler : IRequestHandler<GetAlertMasterByAlertIdQuery, List<AlertMasterByAlertIdVm>>
{
    private readonly IAlertMasterRepository _alertMasterRepository;
    private readonly IMapper _mapper;

    public GetAlertMasterByAlertIdQueryHandler(IMapper mapper, IAlertMasterRepository alertMasterRepository)
    {
        _mapper = mapper;
        _alertMasterRepository = alertMasterRepository;
    }

    public async Task<List<AlertMasterByAlertIdVm>> Handle(GetAlertMasterByAlertIdQuery request,
        CancellationToken cancellationToken)
    {
        var alertMaster = await _alertMasterRepository.GetAlertMasterByAlertId(request.AlertId);

        Guard.Against.NullOrDeactive(alertMaster, nameof(Domain.Entities.AlertMaster),
            new NotFoundException(nameof(Domain.Entities.AlertMaster), request.AlertId));

        var alertMasterId = _mapper.Map<List<AlertMasterByAlertIdVm>>(alertMaster);

        return alertMasterId;
    }
}