﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IReportScheduleRepository : IRepository<ReportSchedule>
{
    Task<bool> IsReportScheduleNameExist(string reportName, string id);
    Task<bool> IsReportScheduleNameUnique(string reportName);
    Task<List<ReportSchedule>> GetReportScheduleNames();
    Task<List<ReportSchedule>> GetReportSchedulerByUserGroupId(string userGroupId);

}