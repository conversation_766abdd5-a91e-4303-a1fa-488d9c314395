using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Events.Delete;

public class ApprovalMatrixUsersDeletedEventHandler : INotificationHandler<ApprovalMatrixUsersDeletedEvent>
{
    private readonly ILogger<ApprovalMatrixUsersDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ApprovalMatrixUsersDeletedEventHandler(ILoggedInUserService userService, ILogger<ApprovalMatrixUsersDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ApprovalMatrixUsersDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} ApprovalMatrixUsers",
            Entity = "ApprovalMatrixUsers",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"ApprovalMatrixUsers '{deletedEvent.UserName}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"ApprovalMatrixUsers '{deletedEvent.UserName}' deleted successfully.");
    }
}
