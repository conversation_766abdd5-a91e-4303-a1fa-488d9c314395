using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using System.Linq;

namespace ContinuityPatrol.Persistence.Repositories;

public class DriftProfileRepository : BaseRepository<DriftProfile>, IDriftProfileRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public DriftProfileRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }
    public async Task<List<DriftProfile>> GetByReferenceIdsAsync(List<string> ids)
    { 
        return await Entities.Where(dp=> ids.Contains(dp.ReferenceId)).ToListAsync();
    }
}
