using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportActionResult.Commands;

public class DeleteBulkImportActionResultTests : IClassFixture<BulkImportActionResultFixture>
{
    private readonly BulkImportActionResultFixture _bulkImportActionResultFixture;
    private readonly Mock<IBulkImportActionResultRepository> _mockBulkImportActionResultRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly DeleteBulkImportActionResultCommandHandler _handler;

    public DeleteBulkImportActionResultTests(BulkImportActionResultFixture bulkImportActionResultFixture)
    {
        _bulkImportActionResultFixture = bulkImportActionResultFixture;

        _mockBulkImportActionResultRepository = BulkImportActionResultRepositoryMocks.CreateDeleteBulkImportActionResultRepository(_bulkImportActionResultFixture.BulkImportActionResults);
        _mockPublisher = new Mock<IPublisher>();

        _handler = new DeleteBulkImportActionResultCommandHandler(
            _mockBulkImportActionResultRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_DeleteBulkImportActionResultResponse_When_BulkImportActionResultDeleted()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var command = new DeleteBulkImportActionResultCommand { Id = existingResult.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(DeleteBulkImportActionResultResponse));
        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var command = new DeleteBulkImportActionResultCommand { Id = existingResult.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsync_OnlyOnce()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var command = new DeleteBulkImportActionResultCommand { Id = existingResult.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportActionResult>()), Times.Once);
    }

    [Fact]
    public async Task Handle_SetIsActiveToFalse_When_BulkImportActionResultDeleted()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        existingResult.IsActive = true; // Ensure it starts as active
        var command = new DeleteBulkImportActionResultCommand { Id = existingResult.ReferenceId };

        Domain.Entities.BulkImportActionResult capturedResult = null;


        _mockBulkImportActionResultRepository
            .Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportActionResult>()))
            .Callback<Domain.Entities.BulkImportActionResult>(result => capturedResult = result)
            .ReturnsAsync((Domain.Entities.BulkImportActionResult result) => result);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedResult.ShouldNotBeNull();
        capturedResult.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportActionResultNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new DeleteBulkImportActionResultCommand { Id = nonExistentId };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportActionResult)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_CreateResponseWithCorrectProperties_When_BulkImportActionResultDeleted()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        existingResult.EntityName = "TestEntity";
        var command = new DeleteBulkImportActionResultCommand { Id = existingResult.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var command = new DeleteBulkImportActionResultCommand { Id = testId };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_bulkImportActionResultFixture.BulkImportActionResults.First());

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_DeleteSuccessful()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var command = new DeleteBulkImportActionResultCommand { Id = existingResult.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<DeleteBulkImportActionResultResponse>();
        result.GetType().ShouldBe(typeof(DeleteBulkImportActionResultResponse));
    }

    [Fact]
    public async Task Handle_PerformSoftDelete_When_EntityExists()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        existingResult.IsActive = true;
        var command = new DeleteBulkImportActionResultCommand { Id = existingResult.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        existingResult.IsActive.ShouldBeFalse();
        _mockBulkImportActionResultRepository.Verify(x => x.UpdateAsync(existingResult), Times.Once);
    }

    [Fact]
    public async Task Handle_NotCallDeleteAsync_When_SoftDeletePerformed()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var command = new DeleteBulkImportActionResultCommand { Id = existingResult.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.DeleteAsync(It.IsAny<Domain.Entities.BulkImportActionResult>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ReturnIsActiveFalse_When_DeleteCompleted()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var command = new DeleteBulkImportActionResultCommand { Id = existingResult.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_WithCorrectMessage_When_EntityNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new DeleteBulkImportActionResultCommand { Id = nonExistentId };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportActionResult)null);

        // Act & Assert
        var exception = await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
        
        exception.Message.ShouldContain("BulkImportActionResult");
        exception.Message.ShouldContain(nonExistentId);
    }

    [Fact]
    public async Task Handle_UseGuardAgainstNull_When_EntityNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new DeleteBulkImportActionResultCommand { Id = nonExistentId };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportActionResult)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }
}
